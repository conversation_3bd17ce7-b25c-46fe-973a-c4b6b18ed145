﻿#include "etstdafx.h"
#include "kbinreadertravel.h"

namespace binary_wo 
{

ResultCode KBinReaderEnum::read()
{
    if (!m_travel) return ResultCode::kFail;
    if (!readNameList()) return ResultCode::kFail;
    
    BinVarData rootData(binary_wo::typeStruct);
    
    BuffName name;
    int beginCursor = m_buffer.readCursor();
    m_travel->onBeginStruct(name);
    ResultCode res = readStruct(rootData);
    m_travel->onEndStruct(m_buffer.readCursor() - beginCursor);
    return res;
}

bool KBinReaderEnum::readNameList()
{
    uint16 nameCnt = m_buffer.readUint16();
    for (int32 i = 0; i < (int32)nameCnt; ++i) {
        uint16 nameID = m_buffer.readUint16();
        uint8 nameLen = m_buffer.readUint8();
        WebName str = m_buffer.readName(nameLen);
        if (str == nullptr)
            return false;
        uint16 type = m_buffer.readUint16();
        m_nameMaps[nameID] = BuffName{str, type, nameLen};
    }
    m_buffer.readCursor();
    return true;
}

const BuffName & KBinReaderEnum::readName()
{
    uint16 id = m_buffer.readUint16();
    return m_nameMaps[id];
}

ResultCode KBinReaderEnum::readStruct(BinVarData & parent)
{
    while (true)
    {
        int32 oldCur = m_buffer.readCursor();
        binary_wo::Types tp = binary_wo::typeInvalid;
        ResultCode res = readElement(parent, tp);
        if (res != ResultCode::kSucc)
            return res;
        
        if (tp == binary_wo::typeStructEnd)
            break;
        else if (m_buffer.readCursor() >= m_buffer.readLength())
            break;
        else if(oldCur >= m_buffer.readCursor()) 
            break; 
    }
    return ResultCode::kSucc;
}

ResultCode KBinReaderEnum::readElement(BinVarData & parent, binary_wo::Types & types)
{
    const BuffName &name = readName();
    types = (binary_wo::Types)name.type();
    ResultCode res = ResultCode::kFail;
    
    switch(types) 
    {
        case binary_wo::typeStruct:
            {
                BinVarData elem(binary_wo::typeStruct);
                int oldCur = m_buffer.readCursor();
                m_travel->onBeginStruct(name);
                res = readStruct(elem);
                m_travel->onEndStruct(m_buffer.readCursor() - oldCur);
                if (m_travel->needBreak())
                    return ResultCode::kInterrupt;
            }
            break;
        case binary_wo::typeNamedStruct:
            {
                const BuffName & structName = readName();
                BinVarData elem(binary_wo::typeNamedStruct);
                int oldCur = m_buffer.readCursor();
                m_travel->onBeginNameStruct(name, structName);
                res = readStruct(elem);
                m_travel->onEndNameStruct(m_buffer.readCursor() - oldCur);
                if (m_travel->needBreak())
                    return ResultCode::kInterrupt;
            }
            break;
        case binary_wo::typeArray:
            {
                BinVarData elem(binary_wo::typeArray);
                int oldCur = m_buffer.readCursor();
                int32 arrLen = m_buffer.readUint32();
                m_buffer.seek_read(oldCur);
                m_travel->onBeginArray(name, arrLen);
                res = readArray(elem);
                m_travel->onEndArray(m_buffer.readCursor() - oldCur);
                if (m_travel->needBreak())
                    return ResultCode::kInterrupt;
            }
            break;
        case binary_wo::typeStructEnd:
            res = ResultCode::kSucc;
            break;
        default:
            res = readValue(parent, name);
    }
    return res;
}

ResultCode KBinReaderEnum::readValue(BinVarData & parent, const BuffName & name)
{
    switch(name.type())
    {
        case binary_wo::typeInt8: 
            {
                int8 v = m_buffer.readInt8();
                m_travel->onValueInt8(name, v);
            }
            break;
        case binary_wo::typeUint8: 
            {
                uint8 v = m_buffer.readUint8();
                m_travel->onValueUint8(name, v);
            }
            break;
        case binary_wo::typeInt16: 
            {
                int16 v = m_buffer.readInt16();
                m_travel->onValueInt16(name, v);
            }
            break;
        case binary_wo::typeUint16: 
            {
                uint16 v = m_buffer.readUint16();
                m_travel->onValueUint16(name, v);
            }
            break;
        case binary_wo::typeInt32: 
            {
                int32 v = m_buffer.readInt32();
                m_travel->onValueInt32(name, v);
            }
            break;
        case binary_wo::typeUint32: 
            {
                uint32 v = m_buffer.readUint32();
                m_travel->onValueUint32(name, v);
            }
            break;
        case binary_wo::typeFloat32: 
            {
                float32 v = m_buffer.readFloat();
                m_travel->onValueFloat32(name, v);
            }
            break;
        case binary_wo::typeFloat64: 
            {
                float64 v = m_buffer.readDouble();
                m_travel->onValueFloat64(name, v);
            }
            break;
        case binary_wo::typeAbsObject32:
            {
                uint32 v = m_buffer.readUint32();
                m_travel->onValueAbsObj32(name, v);
            }
            break;
        case binary_wo::typeAbsObject64:
            {
                int64 v = (int64)m_buffer.readDouble();
                m_travel->onValueAbsObj64(name, v);
            }
            break;
        case binary_wo::typeBool:
            {
                bool v = (int64)m_buffer.readBool();
                m_travel->onValueBool(name, v);
            }
            break;
        case binary_wo::typeString:
            {
                uint32 len = m_buffer.readUint32();
                const WCHAR* str = m_buffer.readString(len);
                if (str == nullptr) return ResultCode::kFail;
                m_travel->onValueString(name, str, len);
            }
            break;
        case binary_wo::typeNameString:
            {
                const BuffName& var = readName();
                m_travel->onValueNameString(name, var);
            }
            break;
        case binary_wo::typeArrayBuffer:
            {
                uint32 len = m_buffer.readUint32();
                uint8 * pArray = m_buffer.readUint8Array(len);
                m_travel->onValueUint8Array(name, pArray, len);
            }
            break;
        default:
            break;
    }
    return ResultCode::kSucc;
}

ResultCode KBinReaderEnum::readArray(BinVarData & parent)
{
	int32 arrLen = m_buffer.readUint32();
	if (arrLen == 0)
		return ResultCode::kSucc;
    Types arrType = (Types)m_buffer.readUint16();
    if (arrType == typeStruct || arrType == typeNamedStruct || arrType == typeArray)
    {
        for (int i = 0; i < arrLen; i++) {
            binary_wo::Types type = typeInvalid;
            ResultCode res = readElement(parent, type);
            if (res != ResultCode::kSucc)
                return res;
        }
    }
    else if (arrType == typeString)
    {
        for (int i = 0; i < arrLen; i++) {
            uint32 len = m_buffer.readUint32();
            const WCHAR* str = m_buffer.readString(len);
            if (str == nullptr) return ResultCode::kFail;
            m_travel->onArrayItemString(str, len);
        }
    }
    else if (arrType == typeNameString)
    {
        for (int i = 0; i < arrLen; i++) {
            const BuffName & v = readName();
            m_travel->onArrayItemNameString(v);
        }
    }
    else
    {
        int32 len = arrLen * binary_wo::s_typeLen[arrType];
        uint8 * pArray = m_buffer.readUint8Array(len);
        m_travel->onArrayItemValue(pArray, len, arrType);
    }
    return ResultCode::kSucc;
}
    
}