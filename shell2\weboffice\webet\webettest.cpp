﻿#include "etcmd/etstdafx.h"
#include "webettest.h"
#include "etcmd/src/op_transform/et_op_tf.h"
#include "etcmd/src/etapplication.h"
#include "etcmd/src/ksignalmetrics.h"

extern std::unique_ptr<wo::KEtApplication> app;

class KTestGetter: public wo::ITestGetter
{
public:
	etoldapi::_Workbook* GetWorkbook() override { return app->GetWorkbooks()->GetWorkbook()->GetCoreWorkbook(); }
	IBook* GetBook() override { return GetWorkbook()->GetBook(); }
	IWoSignalMetrics * CreateSignalMetrics() override 
	{
		return new wo::KSignalMetrics();
	}
};

KTestGetter gTestGetter;

WEB_EXPORT wo::OpTransformation* CreateOpTransformation()
{
	return new wo::KEtOpTrsfmt();
}

WEB_EXPORT wo::ITestGetter* TestGetter()
{
	return &gTestGetter;
}
