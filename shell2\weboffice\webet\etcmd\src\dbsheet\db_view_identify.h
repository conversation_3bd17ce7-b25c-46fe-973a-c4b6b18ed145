﻿#ifndef __DB_VIEW_IDENTIFY_H__
#define __DB_VIEW_IDENTIFY_H__

namespace wo
{

class KIdentifyDBView
{
public:
    KIdentifyDBView(IKWorksheet* pWorksheet, IDBSheetOp* pDbSheetOp);
    ~KIdentifyDBView();
    void identify(IDBSheetView* pView, QJsonObject& jsonObj);
    bool transformTable(IDBSheetView* pView, QJsonObject& jsonTable);
    void currencyToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col);
    void dateToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col);
    void numberToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col);
    void boolToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col);
    void urlToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col);
    void formulaToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col);
    bool hasSheetOverflow() const;

private:
    ks_stdptr<_Workbook> m_spWorkbook;
    ks_stdptr<_Worksheet> m_spWorksheet;
    ks_stdptr<IDBSheetOp> m_spDbSheetOp;
    int m_nCount = 0;
    bool m_isEmptyRow = true;
};

} // namespace wo

#endif  // __DB_VIEW_IDENTIFY_H__