#include "etstdafx.h"

#include "pivot_core/pivot_core_x.h"
#include "applogic/et_api_convert.h"
#include "applogic/et_apihost.h"
#include "etcore/et_ranges_helper.h"
#include "etcore/little_alg.h"
#include "wo/core_stake.h"
#include "pivot_core/pivot_core_cpl.h"
#include "pivot_core/pivot_core_def.h"
#include "pivot_core/pivot_core_filter_operator.h"
#include "opl/et_opl_helper.h"
#include <public_header/opl/mvc/chart/et_chart_layer.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include <public_header/opl/mvc/et_mvc_shape.h>

#include "workbook.h"
#include "et_revision_context_impl.h"
#include "pivot_tables_helper.h"
#include "shape_helper.h"

#include "protection_helper.h"
#include "util.h"

namespace wo
{

static HRESULT makeRefHR(HRESULT hr1, HRESULT hr2)
{
    if (hr1 == S_OK)
        return hr2;

    if (hr2 == S_OK)
        return hr1;

    if (hr1 == E_FORMULA_HAS_FORBIDDEN_FUNC || hr2 == E_FORMULA_HAS_FORBIDDEN_FUNC)
    {
        return E_FORMULA_HAS_FORBIDDEN_FUNC;
    }

    if (hr1 == E_FORMULA_NOT_SUPPORTED_ON_HIDDEN_BOOK || hr2 == E_FORMULA_NOT_SUPPORTED_ON_HIDDEN_BOOK)
    {
        return E_FORMULA_NOT_SUPPORTED_ON_HIDDEN_BOOK;
    }

    if (hr1 == E_HAS_FORMULA_REF_HIDDEN_PROP_RANGE || hr2 == E_HAS_FORMULA_REF_HIDDEN_PROP_RANGE)
    {
        return E_HAS_FORMULA_REF_HIDDEN_PROP_RANGE;
    }

    if (hr1 == E_FORMULA_NOT_SUPPORTED_ON_SHARED_SHEET || hr2 == E_FORMULA_NOT_SUPPORTED_ON_SHARED_SHEET)
    {
        return E_FORMULA_NOT_SUPPORTED_ON_SHARED_SHEET;
    }

    return hr1;
}

class KCellValueAcptProtection : public ICellValueAcpt
{
  public:
    KCellValueAcptProtection(KEtRevisionContext *ctx, IBookOp *pBookOp, ISheet *pSheet)
        : hr(S_OK),
          spCtx(ctx),
          spBookOp(pBookOp),
          spSheet(pSheet)
    {
    }

    STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
    {
        IDX idxSheet = 0;
        spSheet->GetIndex(&idxSheet);
        ks_stdptr<IFormula> ptrFormula;
        hr = spBookOp->GetCellFormula(idxSheet, row, col, &ptrFormula, NULL);
        if (SUCCEEDED(hr))
        {
            BOOL pbFmla = FALSE;
            ks_stdptr<ITokenVectorInstant> ptrStTokens;
            ptrFormula->GetContent(&pbFmla, &ptrStTokens, NULL);
            hr = spCtx->getProtectionCtx()->checkFormula(idxSheet, row, col, ptrStTokens);
            if (pbFmla && S_OK != hr)
            {
                return 1; // 停止枚举
            }
        }

        return 0; // 继续枚举
    };

    HRESULT hr;
    KEtRevisionContext *spCtx;
    IBookOp *spBookOp;
    ISheet *spSheet;
};

bool isPivotTableRefHiddenRange(ISheet *isheet, pivot_core::IPivotTable *spPvtTable, KEtRevisionContext *ctx)
{
    if (!isheet)
    {
        return false;
    }

    auto sectionRanges = PivotHelpers::getPivotTableSectionRanges(isheet, spPvtTable);
    for (auto it = sectionRanges.begin(); it != sectionRanges.end(); ++it)
    {
        if (ctx->getProtectionCtx()->isRangeHasHidden(*it))
        {
            return true;
        }
    }

    return false;
}

HRESULT checkProtectionContentKind(KEtRevisionContext *ctx,
                                   etoldapi::_Workbook* pApiWb,
                                   const RANGE &rg,
                                   bool checkAll,
                                   uint32_t *pckKind)
{
    uint32_t kind = IEtProtectionCtx::pckNone;
    *pckKind = kind;

    ks_stdptr<etoldapi::Range> range_api = util::CreateRangeObj(pApiWb, rg);
    if (!range_api)
        return S_OK;

    ks_stdptr<ISheet> spSheet;
    IBook *pBook = pApiWb->GetBook();
    pBook->GetSheet(rg.SheetFrom(), &spSheet);
    if (!spSheet)
    {
        return S_OK;
    }

    HRESULT hr = S_OK;
    HRESULT hr2 = S_OK;

    ks_stdptr<IBookOp> spBookOp;
    pBook->GetOperator(&spBookOp);

    et_sdptr<ISheetEnum> spSheetEnum;
    spSheet->CreateEnum(&spSheetEnum);
    KCellValueAcptProtection cellValue(ctx, spBookOp, spSheet);
    spSheetEnum->EnumCellValue(rg, &cellValue);
    hr = cellValue.hr;
    if (FAILED(hr))
    {
        kind |= IEtProtectionCtx::pckFormula;
        if (!checkAll)
        {
            *pckKind = kind;
            return hr;
        }

        hr2 = makeRefHR(hr2, hr);
    }

    hr = ctx->getProtectionCtx()->checkDVFormulaInRange(rg);
    if (FAILED(hr))
    {
        kind |= IEtProtectionCtx::pckDV;
        if (!checkAll)
        {
            *pckKind = kind;
            return hr;
        }

        hr2 = makeRefHR(hr2, hr);
    }

    hr = ctx->getProtectionCtx()->checkCFFormulaInRange(rg);
    if (FAILED(hr))
    {
        kind |= IEtProtectionCtx::pckCF;
        if (!checkAll)
        {
            *pckKind = kind;
            return hr;
        }

        hr2 = makeRefHR(hr2, hr);
    }

    KCOMPTR(IKRanges) ptrIKRanges;
    _etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void **)&ptrIKRanges);
    ptrIKRanges->Append(alg::STREF_THIS_BOOK, rg);

    IKWorksheet *pWs = pApiWb->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    ks_stdptr<IKShapeRange> ptrShapeRange;
    if (S_OK == GetRangeCoverShapes(spSheet, pWs->GetDrawingCanvas(), ptrIKRanges, &ptrShapeRange))
    {
        LONG count = 0;
        ptrShapeRange->GetShapeCount(&count);
        for (LONG i = 0; i < count; ++i)
        {
            ks_stdptr<drawing::AbstractShape> ptrShape;
            ptrShapeRange->GetShapeByIndex(i, &ptrShape);
            if (ptrShape && ptrShape->hasChart())
            {
                kind |= IEtProtectionCtx::pckChart;
                hr = E_APPLY_CONTENT_NOT_SUPPORTED_ON_HIDDEN_BOOK;
                *pckKind = kind;
                break;
            }
        }
    }

    *pckKind = kind;
    return hr2;
}
uint32_t getProtectionContentKind(etoldapi::_Workbook* pApiWb,
                                  const RANGE &rg,
                                  bool checkAll,
                                  KEtRevisionContext *ctx)
{
    uint32_t kind = IEtProtectionCtx::pckNone;
    checkProtectionContentKind(ctx, pApiWb, rg, checkAll, &kind);
    return kind;
}

bool isPivotTablesRefRange(ISheet *isheet, const RANGE &rg)
{
    if (!isheet)
    {
        return false;
    }

    ks_stdptr<pivot_core::IPivotTables> spCorePvtTbls;
    HRESULT hr = pivot_core::GetPivotTablesFromSheet(isheet, &spCorePvtTbls);
    if (FAILED(hr))
    {
        return false;
    }

    long lCount = spCorePvtTbls->Count();
    for (int idx = 0; idx < lCount; ++idx)
    {
        ks_stdptr<pivot_core::IPivotTable> spCorePvtTbl = spCorePvtTbls->Item(idx);
        if (!spCorePvtTbl)
            break;

        ks_stdptr<pivot_core::IPivotCache> cache = spCorePvtTbl->GetPivotCache();
        if (!cache)
            break;

        ks_stdptr<pivot_core::ISrcDescription> srcDes = cache->GetSrcDes();
        if (!srcDes)
            break;

        RANGE srcRg(isheet->GetBMP());
        srcDes->GetSourceData(&srcRg);
        if (srcRg.IsValid())
        {
            RANGE rgIntersect = rg.Intersect(srcRg);
            if (rgIntersect.IsValid())
            {
                return true;
            }
        }
    }

    return false;
}

bool hasPivotTablesRefRange(IBook *ibook, const RANGE &rg)
{
    if (!ibook)
    {
        return false;
    }

    INT shtCnt = 0;
    ibook->GetSheetCount(&shtCnt);
    for (INT i = 0; i < shtCnt; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        ibook->GetSheet(i, &spSheet);
        if (isPivotTablesRefRange(spSheet, rg))
        {
            return true;
        }
    }

    return false;
}

IEtProtectionCtx::ProtectionContentKind getFormulaRefRangeKind(IBook *pBook, range_helper::ranges &rgs)
{
    uint32_t kind = pBook->GetWoStake()->getProtectionFormula()->getFormulaRefRangeKind(pBook, rgs);
    for (int i = 0; i < rgs.size(); ++i)
    {
        const RANGE &rgSrc = *rgs[i].second;
        if (hasPivotTablesRefRange(pBook, rgSrc))
        {
            kind |= IEtProtectionCtx::pckPivotTable;
            break;
        }
    }

    return IEtProtectionCtx::ProtectionContentKind(kind);
}

bool IsProtectionError(HRESULT hr)
{
    switch (hr)
    {
    case E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET:
    case E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE:
    case E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK:
    case E_FORMULA_NOT_SUPPORTED_ON_HIDDEN_BOOK:
    case E_HAS_FORMULA_REF_HIDDEN_PROP_RANGE:
    case E_FORMULA_HAS_FORBIDDEN_FUNC:
    case E_APPLY_CONTENT_NOT_SUPPORTED_ON_HIDDEN_BOOK:
    case E_NOT_SUPPORTED_APPLY_RANGE_ON_HIDDEN_BOOK:
        return true;

    default:
        return false;
    }
}

HRESULT switchOnProtection(KEtRevisionContext* ctx, _Worksheet* pWs, LPCWSTR lpszPassword, const SHEETPROTECTION& options)
{
    if (pWs->GetSheet()->IsProtected())
		return E_FAIL;

	WOLOG_INFO << "[protection][switch on protection] due to add the first protection region!";
    HRESULT hr = S_OK;
    if (options.bSelLockedCells)
        hr = pWs->put_EnableSelection(KComVariant(etNoRestrictions));
    else if (options.bSelUnlockedCells)
        hr = pWs->put_EnableSelection(KComVariant(etUnlockedCells));
    else
        hr = pWs->put_EnableSelection(KComVariant(etNoSelection));

	if (FAILED(hr))
		return hr;

	KComVariant varFormatCells = KComVariant(options.bFormatCells);
	KComVariant varFotmatCols = KComVariant(options.bFormatCols);
	KComVariant varFormatRows = KComVariant(options.bFormatRows);
	KComVariant varInsertCols = KComVariant(options.bInsertCols);
	KComVariant varInsertRows = KComVariant(options.bInsertRows);
	KComVariant varInsertHyperlinks = KComVariant(options.bInsertHyperlinks);
	KComVariant varDeleteCols = KComVariant(options.bDeleteCols);
	KComVariant varDeleteRows = KComVariant(options.bDeleteRows);
	KComVariant varSort = KComVariant(options.bSort);
	KComVariant varUseAutoFilter = KComVariant(options.bUseAutoFilter);
	KComVariant varUsePivotTable = KComVariant(options.bUsePivotTableReports);
	KComVariant varDrawingObjects = KComVariant(!options.bEditObjects);	//kworksheet.protect里面设置的时候会取反
	KComVariant varEditScenarios = KComVariant(options.bEditScenarios);

	KComVariant varPassword = KComVariant(lpszPassword);

	hr = pWs->Protect(varPassword, varDrawingObjects, KComVariant(VARIANT_TRUE), varEditScenarios, KComVariant(VARIANT_FALSE), varFormatCells, varFotmatCols,
		varFormatRows, varInsertCols, varInsertRows, varInsertHyperlinks, varDeleteCols, varDeleteRows, varSort, varUseAutoFilter, varUsePivotTable);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[protection][switch on protection] operation pWs->Protect failed！";
		return hr;
	}
	WOLOG_INFO << "[protection][switch on protection] operation pWs->Protect successed!";
	
	ISheetProtection* pSheetProtection = pWs->GetProtection();
	pSheetProtection->SetMaster(ctx->getUser()->userID());
	//清除自动密码
	pSheetProtection->SetPasswordUUID(__X(""));

	return S_OK;
}

//注意，对于webet，该方法会跳过密码校验直接清除保护信息，目前密码校验和权限检查由流程保证
HRESULT switchOffProtection(KEtRevisionContext* ctx,_Worksheet* pWs)
{
    if (!pWs->GetSheet()->IsProtected())
		return S_OK;

    IBook* pBook = pWs->GetSheet()->LeakBook();
    if (!pBook->GetBMP()->bKsheet)
    {
        bool bSucc = clearProtection(pWs);
        return bSucc ? S_OK : E_FAIL;
    }

	ISheetProtection* pSheetProtection = pWs->GetProtection();

	KComVariant varPassword;
	varPassword = KComVariant(__X(""));
	
	HRESULT hr = pWs->Unprotect(varPassword);
	if (FAILED(hr))
	{
		ctx->collectInfo("behaviour.sheetprotectclose_false");
		return hr;
	}

   	hr = switchOffProtectedCols(pWs);
	if (FAILED(hr))
		return hr;

	pSheetProtection->SetMaster(__X(""));
	pSheetProtection->markWoObjectModified();
	return S_OK;
}

HRESULT switchOffProtectedCols(_Worksheet* pWs)
{
    ks_stdptr<ISheet> ptrSheet = pWs->GetSheet();
	et_sdptr<ISheetEnum> spSheetEnum;
	ptrSheet->CreateEnum(&spSheetEnum);

    BMP_PTR bmpPtr = ptrSheet->GetBMP();
	
    BOOL bProtected = FALSE;
	INT colCnt = spSheetEnum->GetColProtect(0, &bProtected);
	if (!bProtected && colCnt == bmpPtr->cntCols)
		return S_OK;
	
	ks_stdptr<IRowColOp> spRowColOp = NULL;
	ptrSheet->QueryInterface(IID_IRowColOp, (void**)&spRowColOp);
	if (!spRowColOp)
		return E_FAIL;
    
    RANGE colArea(bmpPtr);
	colArea.SetRowFromTo(0, bmpPtr->cntRows - 1);
    ICellProctection* ptrCellProtection = ptrSheet->GetCellProtection();
    
	INT curCol = 0;
    while (curCol < ptrSheet->GetBMP()->cntCols)
    {
        INT colCnt = spSheetEnum->GetColProtect(curCol, &bProtected);
        if (colCnt < 0)
            break;
        if (bProtected)
        {
			spRowColOp->SetColProtect(curCol, curCol + colCnt - 1, false);
			spRowColOp->SetColProtectCnt(curCol, curCol + colCnt - 1, 0);

            colArea.SetColFromTo(curCol, curCol + colCnt - 1);
            ptrCellProtection->Clear(colArea);
        }
        curCol += colCnt;
    }
	return S_OK;
}

void getSheetProtection(ISheet *spSheet, ISheetProtection **ptrSheetProtection)
{
    ASSERT(spSheet);
    ks_stdptr<IUnknown> ptrUnknown;
    spSheet->GetExtDataItem(edSheetProtection, &ptrUnknown);
    ptrUnknown->QueryInterface(IID_ISheetProtection, (void **)ptrSheetProtection);
}

bool isSheetHasProtectedCols(ISheet* pSheet)
{
    ks_stdptr<ISheetProtection> sheetProtection;
    getSheetProtection(pSheet, &sheetProtection);
    if (!sheetProtection || !sheetProtection->IsProtected())
        return false;

	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
	BOOL bProtected = FALSE;
	INT colCnt = spSheetEnum->GetColProtect(0, &bProtected);
	if (colCnt ==  pSheet->GetBMP()->cntCols && !bProtected)
		return false;
	return true;
}

bool needSwitchOffProtection(KEtRevisionContext* ctx, IDX sheetIdx, _Worksheet* pWs)
{
    if (!isEnableRegionProtect(pWs))
        return false;

    ISheetProtection* pSheetProtection = pWs->GetProtection();
	if (!pSheetProtection)
		return false;

	bool bProtectedOn = pSheetProtection->IsProtected();
	if(bProtectedOn && !pSheetProtection->HasValidUserRange())
    {
        return !isSheetHasProtectedCols(pWs->GetSheet());
    }

    return false;
}

bool clearProtection(_Worksheet* pWs)
{
    ISheet *pSheet = pWs->GetSheet();
    if (!pSheet)
        return false;

    if (pSheet->IsDbSheet())
        return false;

    // 列隐私保护和区域权限在内部共用同一套逻辑
    switchOffProtectedCols(pWs);
    pSheet->SetProtected(FALSE);
    ks_stdptr<IUnknown> spUnknown;
    ISheetProtection* pSheetProtection = pWs->GetProtection();
    if (!pSheetProtection)
        return false;

    for (int i = pSheetProtection->GetUserRangeCount() - 1; i >= 0; --i)
        pSheetProtection->DeleteUserRange(i);
    SHEETPROTECTION resetSP = {0};
    pSheetProtection->GetProperty(&resetSP);
    resetSP.bProtect = 0;
    resetSP.wPassword = 0;
    resetSP.bEditObjects = 1;
    resetSP.bEditScenarios = 1;
    pSheetProtection->SetProperty(&resetSP);
    pSheetProtection->SetMaster(__X(""));
    pSheetProtection->SetPasswordUUID(__X(""));

    return true;
}

bool isRegionProtect(IKWorksheet* pWorksheet)
{
    IKWorkbook* pWorkbook = pWorksheet->GetWorkbook();
    if (pWorkbook->GetBook()->GetBMP()->bKsheet)
        return true;
    if (pWorkbook->GetBook()->GetBMP()->bDbSheet)
        return false;
    if (!_kso_GetWoEtSettings()->IsEnableRegionProtect())
        return false;

    ISheetProtection* pProtection = pWorksheet->GetProtection();
	if (!pProtection)
		return false;

    return pProtection->GetOtherUserPermission() == PTAAP_Edit;
}

bool isEnableRegionProtect(IKWorksheet* pWorksheet)
{
    IKWorkbook* pWorkbook = pWorksheet->GetWorkbook();
    if (pWorkbook->GetBook()->GetBMP()->bKsheet)
        return true;
    if (pWorkbook->GetBook()->GetBMP()->bDbSheet)
        return false;

    return _kso_GetWoEtSettings()->IsEnableRegionProtect();
}

ProtectionAccessPerms getAccessPermission(const binary_wo::VarObj &obj, const char* name, ProtectionAccessPerms defaultRight)
{
    ProtectionAccessPerms right = defaultRight;
	if (obj.has(name))
	{
		const WebStr accessPermission = obj.field_str(name);
		if (xstrcmp(accessPermission, __X("invisible")) == 0)
		{
			right = PTAAP_None;
		}
		else if (xstrcmp(accessPermission, __X("visible")) == 0)
		{
			right = PTAAP_Visible;
		}
		else if (xstrcmp(accessPermission, __X("edit")) == 0)
		{
			right = PTAAP_Edit;
		}
	}

	return right;
}

}; // namespace wo
