﻿#include "etstdafx.h"
#include "database.h"
#include "database_field.h"
#include "database_field_context.h"
#include "util.h"

namespace wo
{
namespace Database
{

#define DB_ADD_FIELD(name)                                 \
{                                                          \
    m_fieldsVec.emplace_back(new name##Field);             \
    m_fieldsMap[dft##name] = m_fieldsVec.size() - 1;       \
    ASSERT(GetField(dft##name)->GetType() == dft##name);   \
}

FieldsManager::FieldsManager()
{
    DB_ADD_FIELD(Date)
    DB_ADD_FIELD(Time)
    DB_ADD_FIELD(Number)
    DB_ADD_FIELD(Currency)
    DB_ADD_FIELD(Percentage)
    DB_ADD_FIELD(Text)
    DB_ADD_FIELD(ID)
    DB_ADD_FIELD(Phone)
    DB_ADD_FIELD(Email)
    DB_ADD_FIELD(Hyperlink)
    DB_ADD_FIELD(Checkbox)
    DB_ADD_FIELD(List)
    DB_ADD_FIELD(Rating)
    DB_ADD_FIELD(Schedule)
    DB_ADD_FIELD(CellPicture)
    DB_ADD_FIELD(AI)
    DB_ADD_FIELD(GenQRLabel)
}

IDbField *FieldsManager::GetField(FieldType type)
{
    if (m_fieldsMap.count(type) != 0)
        return m_fieldsVec[m_fieldsMap[type]].get();
    
    ASSERT(FALSE);
    return nullptr;
}

IDbField *FieldsManager::IdentifyAll(FieldContext *pContext, const RANGE &rg, VALIDATION dv)
{
    for (auto it = m_fieldsVec.begin(); it != m_fieldsVec.end(); ++it)
        if (it->get()->Identify(pContext, rg, dv))
            return it->get();
    return nullptr;
}

HRESULT FieldsManager::EnumFieldsInRANGE(FieldContext *pContext,
                                        const RANGE &rg,
                                        FieldType type,
                                        FieldEnumOption enumOption,
                                        IFieldEnum *pEnum)
{
    ks_stdptr<IRangeInfo> spRangeInfo = pContext->CreateRangeObj(rg);
    ks_stdptr<IKRanges> spKRanges;
    spRangeInfo->GetIRanges(&spKRanges);

    ks_stdptr<IBook> spBook = pContext->GetBook();
    ks_stdptr<IBookOp> spBookOp;
    if (spBook)
        spBook->GetOperator(&spBookOp);
    if (!spBookOp)
        return E_FAIL;

    ks_stdptr<IKRanges> spDVRanges;
    spBookOp->GetDataValidationRanges(spKRanges, &spDVRanges);

    UINT nCnt = 0;
    spDVRanges->GetCount(&nCnt);
    INT64 totalCellCount = 0;
    for (int i = 0; i < nCnt; i++)
    {
        const RANGE *pRange = NULL;
        spDVRanges->GetItem(i, NULL, &pRange);
        if (pRange == NULL || !pRange->IsValid())
            continue;

        wo::util::VALIDATION_Wrap dv;
        RANGE *pRgn = NULL;
        spBookOp->GetDataValidation(*pRange, pRange->SheetFrom(), pRange->RowFrom(), pRange->ColFrom(), &dv, NULL, pRgn, NULL);

        IDbField *pField = nullptr;
        switch (enumOption)
        {
            case dbEnumAll:
                pField = IdentifyAll(pContext, *pRange, dv);
                break;
            case dbEnumExcludeType:
                pField = IdentifyAll(pContext, *pRange, dv);
                if (pField && pField->GetType() == type)
                    continue;
                break;
            case dbEnumType:
                pField = GetField(type);
                if (pField && !pField->Identify(pContext, *pRange, dv))
                    continue;
                break;
        }

        if (pField != nullptr)
        {
            if (FAILED(pEnum->Do(pContext, pField, *pRange, dv)))
                return E_FAIL;
            else
                pContext->SetEnumAny();
        }

        if (pEnum->ShouldStopEnum())
            break;
    }

    return S_OK;
}

} // Database
} // wo