﻿#ifndef _ET_KCON_MODULE_H__
#define _ET_KCON_MODULE_H__

#include "etstdafx.h"
#include <kso/dircfginfo.h>
#include <kso/reg.h>
#include <kso/appfeature/kappfeature.h>
#include <kso/userinfo/ksouserinfo.h>

namespace wo
{
class KxKsoModule : public QLibrary
{
	typedef HRESULT (WINAPI*_func_Initialize)(void);
	typedef HRESULT (WINAPI*_func_InitlizeFeatureStates)(void);
	typedef int		(WINAPI*_func_QueryFeatureState)(int);
	typedef HRESULT (WINAPI*_func_GetDirInfo)(ksoDirEnum, int, bool, WCHAR*, int, bool);
	typedef HRESULT (WINAPI*_func_GetUserDirInfo)(ksoUserDirEnum, bool, WCHAR*, int);
	typedef HRESULT (WINAPI*_func_RegSetValueI)(Kso_Reg_Type, LPCWSTR, LPCWSTR,	int);
	typedef HRESULT (WINAPI*_func_RegQueryValueI)(Kso_Reg_Type, LPCWSTR, LPCWSTR, int, HRESULT*);
	typedef HRESULT (WINAPI*_func_GetUserInformation)(IUserInformation** ppInfo);
	typedef LPCWSTR (WINAPI* _func_GetProductInfo)(KSO_PRODUCTINFO nInfoType);
	typedef HRESULT (WINAPI* _func_RegQueryValue)(Kso_Reg_Type, LPCWSTR, LPCWSTR,	BSTR*, LPCWSTR);
	typedef HRESULT (WINAPI* _func_RegQueryValueEx)(Kso_Reg_Type, LPCWSTR, LPCWSTR,	BSTR*, LPCWSTR);
	typedef HRESULT (WINAPI*_func_RegQueryValueExI)(Kso_Reg_Type, LPCWSTR, LPCWSTR, int, HRESULT*);
	typedef int (WINAPI* _func_GetVersionType)();
	typedef HRESULT (WINAPI*_func_GetOEMText2)(BSTR, BSTR, bool, BSTR*);
	typedef HRESULT (WINAPI* _func_GetNormalOEMValue)(PCWSTR, PCWSTR, BSTR*);
	typedef HRESULT (WINAPI* _func_GetCurrentLanguage)(long* llanguage);
#ifdef X_OS_WINDOWS
	typedef HRESULT (WINAPI*_func_KFT_CreateLibrary)(struct KFT_Library**);
	typedef HRESULT (WINAPI*_func_KFT_DestroyLibrary)(void);
	typedef HRESULT (*_func_SendInfoCellect)(int nType);
#endif
	typedef HRESULT (WINAPI*_func_funcRegSetValue)(Kso_Reg_Type, LPCWSTR, LPCWSTR,	LPCWSTR);
	typedef HRESULT (WINAPI *_func_OpenHelp)(int emHelpFile, BSTR bszSubAddress);

	_func_Initialize m_funcInitialize;
	_func_InitlizeFeatureStates m_funcInitlizeFeatureStates;
	_func_QueryFeatureState m_funcQueryFeatureState;
	_func_GetDirInfo m_funcGetDirInfo;
	_func_GetUserDirInfo m_funcGetUserDirInfo;
	_func_RegSetValueI m_funcRegSetValueI;
	_func_RegQueryValueI m_funcRegQueryValueI;
	_func_GetUserInformation m_funcGetUserInformation;
	_func_GetProductInfo m_fun_GetProductInfo;
	_func_RegQueryValue m_fun_RegQueryValue;
	_func_RegQueryValueEx m_fun_RegQueryValueEx;
	_func_RegQueryValueExI m_fun_RegQueryValueExI;
	_func_GetVersionType m_fun_GetVersionType;
	_func_GetOEMText2 m_fun_GetOEMText2;
	_func_GetNormalOEMValue m_GetNormalOEMValue;
	_func_GetCurrentLanguage m_funcGetCurrentLanguage;
#ifdef X_OS_WINDOWS
	_func_KFT_CreateLibrary m_funcKFT_CreateLibrary;
	_func_KFT_DestroyLibrary m_funcKFT_DestroyLibrary;
	_func_SendInfoCellect m_funcSendInfoCellect;
#endif
	_func_funcRegSetValue m_funcRegSetValue;
	_func_OpenHelp m_funcOpenHelp;

public:
	KxKsoModule();
	HRESULT Initialize();
	HRESULT InitlizeFeatureStates();
	int		QueryFeatureState(int nFeatureID);
	QString GetDirInfo(ksoDirEnum nDir, int nLanguage = KSO_LANGUAGE_AUTO, bool bFull = true);
	QString GetUserDirInfo(ksoUserDirEnum nDir, int balluser = false);
	HRESULT RegSetValueI(Kso_Reg_Type type,	LPCWSTR lpcwSubKey,	LPCWSTR lpcwValName, int nVal);
	HRESULT RegSetValue(Kso_Reg_Type type, LPCWSTR lpcwSubKey, LPCWSTR lpcwValName, LPCWSTR lpcwValue);
	int RegQueryValueI(Kso_Reg_Type type, LPCWSTR lpcwSubKey, LPCWSTR lpValName, int nDefVal = 0, HRESULT* phr = NULL);
	HRESULT GetUserInformation(IUserInformation** ppInfo);
	LPCWSTR GetProductInfo(KSO_PRODUCTINFO nInfoType);
	HRESULT RegQueryValue(Kso_Reg_Type type,
		LPCWSTR lpcwSubKey,
		LPCWSTR lpValName,
		BSTR* pVal,
		LPCWSTR lpDefVal);
	HRESULT RegQueryValueEx(Kso_Reg_Type type,
		LPCWSTR lpcwSubKey,
		LPCWSTR lpValName,
		BSTR* pVal,
		LPCWSTR lpDefVal);
	HRESULT RegQueryValueExI(Kso_Reg_Type type, LPCWSTR lpcwSubKey,
		LPCWSTR lpValName, int nDefVal = 0, HRESULT* phr = NULL);
	int GetVersionType();
	HRESULT GetOEMText2(BSTR keyName, BSTR attribName, bool bIsPath, BSTR* pVal);
	HRESULT GetNormalOEMValue(IN BSTR bszSectionName,
		IN BSTR bszKeyName,
		OUT BSTR* bszKeyValue);
	long GetCurrentLanguage();

#ifdef X_OS_WINDOWS
	struct KFT_Library * KFT_CreateLibrary();
	HRESULT KFT_DestoryLibrary();

	HRESULT SendInfoCellect(int nType);
#endif

	HRESULT OpenHelp(int emHelpFile, BSTR bszSubAddress = NULL);
};
}

#endif // _ET_KCON_MODULE_H__