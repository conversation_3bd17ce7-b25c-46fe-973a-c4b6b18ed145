﻿#ifndef __WEBET_UPDATE_APP_VERSION_HELPER__
#define __WEBET_UPDATE_APP_VERSION_HELPER__

#include "etstdafx.h"
#include "workbook.h"

class KEtWorkbook;

namespace UpdateAppVersionHelper
{
	//view级别
	struct UpdateViewToAppItem
	{
		UpdateViewToAppItem(EtDbId viewId, ET_DBSheet_ViewType viewTp, bool bPersonalView, PCWSTR appUserId,
			bool bNeedCreateSharedId, PCWSTR sharedId);
		bool SetSharedId(PCWSTR sharedId);
		bool SetAirAppInfo(UINT airAppSheetStId, EtDbId airAppId, const ks_wstring& airAppName);
		void SerialResult(binary_wo::BinWriter& writer);
		void AddToParam(binary_wo::VarObj varViewSharedId);
		
		EtDbId m_viewId;
		ET_DBSheet_ViewType m_viewTp;
		bool m_bPersonalView;
		ks_wstring m_appUserId;
		bool m_bNeedCreateSharedId;
		ks_wstring m_sharedId;
		UINT m_airAppSheetStId;
		EtDbId m_airAppId;
		ks_wstring m_airAppName;
	};

	//sheet级别
	struct UpdateDbSheetToAppItem
	{
		UpdateDbSheetToAppItem(UINT sheetStId, bool bVeryHidden);
		void PushViewToAppItem(const UpdateViewToAppItem& item);
		bool ModifySharedId(EtDbId viewId, PCWSTR sharedId);
		bool CheckFetchAllSharedIds();
		void SerialResult(binary_wo::BinWriter& writer);
		void AddToParam(binary_wo::VarObj& varSheetSharedIds);
		bool IsEmpty();
		//保存清洗的执行先后顺序，保持和视图的本身顺序一致。
		std::vector<EtDbId> m_updateViewIdOrders;
		std::unordered_map<EtDbId, UpdateViewToAppItem> m_updateViews;
		UINT m_sheetStId;
		bool m_bVeryHidden;
	};

	//管理所有需要升级应用的dbsheet
	struct UpdateDbSheetToAppMgr
	{
		void PushDbSheetToAppItem(const UpdateDbSheetToAppItem& item);
		bool ModifySharedId(UINT sheetStId, EtDbId viewId, PCWSTR sharedId);
		bool CheckFetchAllSharedIds();
		void SerialResult(binary_wo::BinWriter& writer);
		void AddToParam(binary_wo::VarObj& param);
		bool IsEmpty();
		void LogInfo();
		//保存清洗的执行先后顺序，因为如果出现sheet重名的清空下，让后清洗的sheet去fix同名冲突。
		std::vector<UINT> m_updateSheetStIdOrders;
		std::unordered_map<UINT, UpdateDbSheetToAppItem> m_updateSheets;
	};

	struct updateAppVersionResGuard
	{
		updateAppVersionResGuard(wo::KEtRevisionContext* ctx);
		~updateAppVersionResGuard();
		void UpdateResult(bool bStatus, UpdateDbSheetToAppMgr* pUdateDbSheetToAppMgr);
	private:
		bool m_bStatus;
		UpdateDbSheetToAppMgr* m_pUpdateDbSheetToAppMgr;
		wo::KEtRevisionContext* m_ctx;
	};

	//应用升级入口函数
	HRESULT UpdateAppVersion(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, UpdateDbSheetToAppMgr& updateDbSheetToAppMgr, VarObj& param);
	bool IsNeedUpdateAppVersion(wo::KEtWorkbook* pWorkbook);
	HRESULT ExecShareView(wo::IEtRevisionContext* ctx, UINT sheetStId, EtDbId viewId, PCWSTR sharedId, UINT airAppSheetStId, EtDbId airAppId, KDbDisplayFormType displayFormTp);
}//end namespace UpdateAppVersionHelper

#endif // __WEBET_UPDATE_APP_VERSION_HELPER__
