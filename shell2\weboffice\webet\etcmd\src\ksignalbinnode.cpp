﻿#include "etstdafx.h"
#include "ksignalbinnode.h"

namespace wo {
BinNode::BinNode()
{
}

BinNode::BinNode(BinNode* parent)
: m_parent(parent)
{
    if (m_parent)
    {
        m_parent->m_children.push_back(this);
    }
}

void BinNode::dispose()
{
    for (auto pNode : m_children)
    {
        pNode->dispose();
    }
    m_children.clear();
    delete this;
}

bool BinNode::isNewObjArray()
{
    switch (m_nameType)
    {
        case BinNodeNameType::kNewObjArray:
        case BinNodeNameType::kVersionNewObjArray:
        case BinNodeNameType::kNewCustomObjArray:
            return true;
    }
    return false;
}

BinNode * BinNode::findFistNewObjNode()
{
    if (isNewObjItem()) return this;
    BinNode * p = m_parent;
    while (p)
    {
        if (p->isNewObjItem())
            return p;
        p = p->parent();
    }
    return p;
}

std::string BinNode::genPath()
{
    std::string str(m_name.nameArray(), m_name.nameLen());
    BinNode *pNode = this->m_parent;
    while (pNode != nullptr)
    {
        str = pNode->m_name.makeNameStr() + "/" + str;
        pNode = pNode->m_parent;
    }
    str += '/';
    return str;
}

void BinNode::setSize(int byteSize)
{
    m_size = byteSize;
    m_selfSize = byteSize;
    for (const auto & pNode : m_children)
    {
        m_selfSize -= pNode->m_size;
    }
    
}

}