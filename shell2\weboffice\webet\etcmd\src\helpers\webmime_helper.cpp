﻿#include "etstdafx.h"
#include "webetlink.h"
#include "webmime_helper.h"
#include <public_header/revision/src/kwcommand.h>
#include "et_revision_context_impl.h"

extern Callback* gs_callback;
namespace wo
{

WebMimeHelper::WebMimeHelper()
{
	strField = nullptr, ctx = nullptr, cmd = nullptr;
}

WebMimeHelper::~WebMimeHelper()
{
	if (gs_callback->deleteObject)
	{
		for (const std::string& path: m_tempFileList)
		{
			WebDeleteArg deleteArg;
			deleteArg.type = DeleteType::DeleteFile;
			deleteArg.pObjectId = path.c_str();
			gs_callback->deleteObject(&deleteArg);
		}
	}
}


QString WebMimeHelper::resolvePath(WebInt* p)
{
	QString wId = getId();
	if (wId.isEmpty())
	{
		return QString();
	}

	QByteArray id = wId.toUtf8();

	WebInt count = 0;
	WebMimeData* pMineDatas = nullptr;
	gs_callback->getMimeDatas(id.data(), &count, &pMineDatas);
	if (p != nullptr)
	{
		(*p) = count;
	}

	if (pMineDatas == nullptr || count < 1 || pMineDatas[0].path == nullptr)
	{
		return QString();
	}

	m_tempFileList.emplace_back(pMineDatas[0].path);
	return QString::fromUtf8(pMineDatas[0].path);
}

WebMimeData* WebMimeHelper::resolveMimeData()
{
	QString wId = getId();
	if (wId.isEmpty())
		return nullptr;

	QByteArray id = wId.toUtf8();

	WebInt count = 0;
	WebMimeData* pMineDatas = nullptr;
	gs_callback->getMimeDatas(id.data(), &count, &pMineDatas);

	if (pMineDatas == nullptr || count < 1 || pMineDatas[0].path == nullptr)
	{
		WOLOG_INFO << "pMineDatas is nullptr !";
		return nullptr;
	}

	m_tempFileList.emplace_back(pMineDatas[0].path);
	return pMineDatas;
}

QString WebMimeHelper::getId()
{
	if (objParam.has(strField))
	{
		return QString::fromUtf16(objParam.field_str(strField));
	}
	else
	{
		return QString();
	}
}

bool WebMimeHelper::addMimeStream(binary_wo::VarObj target, WebName nn)
{
	WebMimeData* pMineDatas = resolveMimeData();
	if (!pMineDatas)
		return false;

	QString path = QString::fromUtf8(pMineDatas[0].path);
	if (path.isEmpty())
		return false;

	ks_stdptr<IStream> strm;
	HRESULT res = _XCreateStreamOnFile(krt::utf16(path), STGM_G_READ, &strm);
	if (res == S_OK && strm != nullptr)
	{
		LARGE_INTEGER zero; zero.QuadPart = 0;
		ULARGE_INTEGER ss; ss.QuadPart = 0;
		VS(strm->Seek(zero, STREAM_SEEK_END, &ss));
		if (ss.QuadPart > 8 * 1024)
		{
			QString objid = QString::fromUtf8(pMineDatas[0].id);
			target.add_field_str("objectID", krt::utf16(objid));
			return true;
		}

		VS(strm->Seek(zero, STREAM_SEEK_SET, nullptr));

		std::vector<uint8> vec(ss.QuadPart+1, 0);
		VS(strm->Read(&vec[0], vec.size(), nullptr));

		binary_wo::VarObj arr = target.add_field_array(nn, binary_wo::typeUint8);
		arr.add_item_uint8((vec.empty() ? nullptr : &vec[0]), vec.size());
		return true;
	}
	else
	{
		return false;
	}
}

binary_wo::VarObj WebMimeHelper::commandConvertMimeStream(
	KwCommand* cmd, ISerialAcceptor* acpt, WebName strmName, binary_wo::VarObjRoot& newRoot)
{
	if (!objParam.has(strField) || !resolveMimeData())
	{
		return (newRoot.get() == nullptr) ? cmd->cast() : newRoot.cast();
	}

	if (!newRoot.get())
	{
		newRoot = binary_wo::VarObjRoot(new binary_wo::BinVarRoot());
		binary_wo::VarObj::_copyStruct(newRoot.get(), cmd->getVar(), newRoot.get());
	}
	binary_wo::VarObj param = newRoot.cast().get("param");

	if (!addMimeStream(param, strmName))
	{
		return binary_wo::VarObj();
	}
	else
	{
		return newRoot.cast();
	}
}

void WebMimeHelper::setHasExtra()
{
	cmd->setHasExtra();
	ctx->setIsRealTransform(true);
}

}
