﻿#include "etstdafx.h"
#include "remove_app_related_sheet.h"
#include "workbook.h"
#include "find_app_helper.h"
#include "delete_app_helper.h"

namespace wo
{
HRESULT RemoveVeryHiddenSheetOfQueryApp(KEtWorkbook* pWorkBook)
{
    IBook* pBook = pWorkBook->GetCoreWorkbook()->GetBook();
    ks_stdptr<IDbtBookCtx> spDbBookCtx;
    VS(pBook->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbBookCtx));
    if (spDbBookCtx->GetOpenIODataVersion() >= DbIoDataVersion_Remove_Query_App_Very_Hidden_Sheet)
        return S_OK;
    int nShtCnt = 0;
    pBook->GetSheetCount(&nShtCnt);
    std::vector<UINT> needRemoveSheetStIds;
    for (int idx = 0; idx < nShtCnt; ++idx)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(idx, &spSheet);
        if (!spSheet) continue;
        if (!spSheet->IsDbSheet()) continue;
        SHEETSTATE ss = ssVisible;
        spSheet->GetVisible(&ss);
        if (ss != ssVeryhidden) continue;
        UINT dbsheetId = spSheet->GetStId();
        UINT relatedEtSheetStId = 0;
        bool bHasRelatedEtSheet = FindAppHelper::FindAppRelatedEtSheetStIdByDbSheetStId(pWorkBook, dbsheetId, relatedEtSheetStId);
        if (bHasRelatedEtSheet == false)
            needRemoveSheetStIds.push_back(dbsheetId);
    }
    for (const UINT& sheetId : needRemoveSheetStIds)
        DeleteAppHelper::DelOneAppRelatedDbSheet(pWorkBook, sheetId, INV_EtDbId);
    return S_OK;
}
} // wo