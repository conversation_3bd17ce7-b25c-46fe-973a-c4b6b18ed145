#include "etstdafx.h"
#include "et_http_query_class.h"
#include "et_ai/common/identifytable.h"
#include "et_ai/smart_analysis/identifytable.h"
#include "et_ai/proof/recognizedata.h"
#include "http_error.h"
#include "src/et_revision_context_impl.h"
#include "src/workbook.h"
#include "webbase/serialize_impl.h"
#include "webbase/serialize.h"
#include "wo/sa_helper.h"
#include "helpers/varobject_helper.h"
#include "utils/attachment_utils.h"
#include "util.h"
#include "database/database.h"
#include "database/database_utils.h"
#include "database/database_field_context.h"
#include "helpers/table_struct_rec_helper.h"
#include "et_http_underStandable_type_helper.h"
#include "kso/io/clipboard/ksomimetype.h"       // for macro kso_cb_html_format and kso_cb_text_format
#include "kso/io/clipboard/ksoclipboard.h"      // for KsoDataStream
#include "helpers/shape_helper.h"       // for GetRangeCoverShapes
#include "helpers/qrlabel_helper.h"
#include "dataanalyze/et_dataanalyze.h"
#include "dataanalyze/et_dataanalyze_helper.h"
#include "wo_data_analyze/data_analyze_helper.h"
#include "helpers/db_field_copy_helper.h"
#include <cstring>
#include "helpers/jsapi_helper.h"
#include "helpers/protection_helper.h"

namespace wo
{

static PCWSTR getSheetTypeStr(ISheet* pSheet)
{
	ASSERT(pSheet);
	SHEETTYPE st = stUnknown;
	pSheet->GetFullType(&st);
	switch (st)
	{
		case stGrid:
			return __X("et");
		case stGrid_DB:
			if (pSheet->IsOldDbSheet())
				return __X("oldDb");
			return __X("db");
		case stFlexPaper:
			return __X("fp");
		case stApp:
			return __X("airApp");
		case stWorkbench:
			return __X("workbench");
		case stDashBoard:
			return __X("dbDashBoard");
		case stOldDashBoard:
			return __X("etDashBoard");
		default:
			return __X("unknown");
	}
}

class JudgeIsPicApct: public ICellValueAcpt
{

private:
    ICellImages *m_pCellImages;
    wo::KEtRevisionContext* m_ctx;
    ISheet* m_pSheet;
    bool *m_isAllCellImage;

private:
    bool isCellImg(const_token_ptr pToken)
    {
        //判断是否为单元格图片
        if(!alg::const_vstr_token_assist::is_type(pToken) || !m_pCellImages)
        {
            return false;
        }
                
        CellImg_Param param;
        PCWSTR cellValue = alg::const_vstr_token_assist(pToken).get_value();
        if (!cellValue)
        {
            return false;
        }
        if (m_pCellImages->GetCellImgParamFromCellValue(cellValue, &param) == FALSE)
        {
            return false;
        }
        return true;
    }

public:   
    JudgeIsPicApct(etoldapi::_Workbook* wb, wo::KEtRevisionContext* ctx, ISheet* pSheet, bool *isAllCellImage)
        : m_ctx(ctx), m_pSheet(pSheet), m_isAllCellImage(isAllCellImage)
    {
        m_pCellImages = wb->GetCellImages();
    }
    STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken) 
    {
        // 空单元格继续遍历
        if (pToken == NULL)
        {
            return 0;
        }

        //判断读取的区域是否在保护区域内，若在，禁止读取
        bool bCellInvisibleForCurUser = m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col);
        if(bCellInvisibleForCurUser)
        {
            return 0;
        }

        ks_bstr text;
        m_ctx->getStringTools()->GetCellText(m_pSheet, row, col, &text, NULL, -1, NULL);
         // 空单元格继续遍历
        if (xstrcmp(text.c_str(), __X("")) == 0)
        {
            return 0;
        }

        if (isCellImg(pToken))
        {
            *m_isAllCellImage = true;
            return 0;
        }
        
        *m_isAllCellImage = false;
        return 1;
    }
};

class Cmp
	{
	public:
		bool operator() (const RANGE& lhs, const RANGE& rhs) const
		{
			if (lhs.RowFrom() < rhs.RowFrom())
				return true;
			else if(rhs.RowFrom() < lhs.RowFrom())
				return false;
			if (lhs.ColFrom() < rhs.ColFrom())
				return true;
			else if (rhs.ColFrom() < lhs.ColFrom())
				return false;
			if (lhs.RowTo() < rhs.RowTo())
				return true;
			else if (rhs.RowTo() < lhs.RowTo())
				return false;
			return lhs.ColTo() < rhs.ColTo();
		}
	};

class SearchApct: public ICellValueAcpt
{

private:
    std::map<int, std::vector<ks_wstring>>* m_spMapSearch;
    wo::KEtRevisionContext* m_ctx;
    ISheet* m_pSheet;
    bool* m_IsInSearch;
    ICellImages *m_pCellImages;

private:
    bool isContain(const ks_wstring& string1, const ks_wstring& string2)
    {
        ks_wstring::const_iterator it = std::search(string1.begin(), string1.end(),
			string2.begin(), string2.end(), [](WCHAR ch1, WCHAR ch2) ->bool 
			{
                // 忽略大小写
				return toxupper(ch1) == toxupper(ch2);
			});

		return it != string1.end();
    }

    bool isCellImg(const_token_ptr pToken)
    {
        //判断是否为单元格图片
        if(!alg::const_vstr_token_assist::is_type(pToken) || !m_pCellImages)
        {
            return false;
        }
                
        CellImg_Param param;
        ks_wstring strCellValue = alg::const_vstr_token_assist(pToken).get_value();
        if (m_pCellImages->GetCellImgParamFromCellValue(strCellValue.c_str(), &param) == FALSE)
        {
            return false;
        }

        IKShape* pShape = m_pCellImages->GetImgByName(param.getNameStr());
        ks_castptr<drawing::AbstractShape> spShape = pShape;
        if (!spShape)
        {
            return false;
        }
            
        IKBlipAtom* spBlipAtom = spShape->picID();
        if (!spBlipAtom)
        {
            return false;
        }
        return true;
    }


public:    
    SearchApct(etoldapi::_Workbook* wb,std::map<int, std::vector<ks_wstring>> *spMapSearch, wo::KEtRevisionContext* ctx, ISheet* pSheet, bool* isInSearch)
        : m_spMapSearch(spMapSearch), m_ctx(ctx), m_pSheet(pSheet), m_IsInSearch(isInSearch)
    {
        m_pCellImages = wb->GetCellImages();
    }

    STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken) 
    {

        if (m_spMapSearch == nullptr)
        {
            return 1;
        }

        if (pToken == NULL || isCellImg(pToken))
        {
            return 0;
        }

        ks_bstr text;
        m_ctx->getStringTools()->GetCellText(m_pSheet, row, col, &text, NULL, -1, NULL);

        if (m_spMapSearch->find(col) != m_spMapSearch->end())
        {
            std::vector<ks_wstring>*spVecSearch = &m_spMapSearch->find(col)->second;
            for (int j = 0; j < spVecSearch->size(); j++)
            {
                // 忽略大小写 搜索到了 
                if (isContain(text.c_str() ,spVecSearch->at(j)))
                {
                    *m_IsInSearch = true;
                    return 1;
                }
            }
        }

        return 0;
    }
};

class CellValueAcpt : public ICellValueAcpt
{
    enum class CellValueType
    {
        CVT_BASIC,
        CVT_CELLPIC,
        CVT_QRLABEL
    };
    public:
        CellValueAcpt(etoldapi::_Workbook* wb,int sheetIdx,ISheet *pSheet,ISerialAcceptor* acpt, wo::KEtRevisionContext* ctx,
            OptionColContext* optionColContext = nullptr, bool isWriteContent = true, bool* spIsHasData = nullptr, bool bGetHyperLinkInfo = false, bool bGetHyperLinkRunAddress = false)
            :m_sheetIdx(sheetIdx)
            ,m_pSheet(pSheet)
            ,m_acpt(acpt)
            ,m_ctx(ctx)
            ,m_pCellImages(nullptr)
            ,m_optionColContext(optionColContext)
            ,m_isWriteContent(isWriteContent)
            ,m_spIsHasData(spIsHasData)
            ,m_bGetHyperLinkInfo(bGetHyperLinkInfo)
            ,m_bGetHyperLinkRunAddress(bGetHyperLinkRunAddress)
            ,m_spWorkSheet(nullptr)
        {
            wb->GetBook()->GetOperator(&m_op);
            m_pCellImages = wb->GetCellImages();
            m_spWorkSheet = wb->GetWorksheets()->GetSheetItem(sheetIdx);
        }

        void SetRowColOp(IRowColOp* pRowColOp)
        {
            m_pRowColOp = pRowColOp;
        }

        STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
        {
            return Do(row, col, pToken, false);
        }


        INT Do(ROW row, COL col, const_token_ptr pToken, bool bFromOutputMerge)
        {   
            if(!pToken)
            {
                return 0;//继续枚举
            }
               
            //判断读取的区域是否在保护区域内，若在，禁止读取
            bool bCellInvisibleForCurUser = m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col);
            if(bCellInvisibleForCurUser)
                return 0;//继续枚举

            if (m_pRowColOp && (m_pRowColOp->GetRowHidden(row) || m_pRowColOp->GetColHidden(col)))
                return 0;

            int rowFrom = row;
            int rowTo = row;
            int colFrom = col;
            int colTo = col;
            // 合并单元格
            BOOL isMerge = handMerge(row, col, rowFrom, rowTo, colFrom, colTo);

            //获取数据
            ks_bstr text;
            if (bFromOutputMerge)
            {
                m_ctx->getStringTools()->GetCellText(m_pSheet, rowFrom, colFrom, &text, NULL, -1, NULL);
            }
            else 
            {
                m_ctx->getStringTools()->GetCellText(m_pSheet, row, col, &text, NULL, -1, NULL);
            }
            ks_bstr originalCellValue;

            m_ctx->getStringTools()->GetCellText(pToken, NULL, &originalCellValue);
            if (m_optionColContext != nullptr && !isMerge)
            {
                m_optionColContext->PutColData(col, text.c_str(), originalCellValue.c_str());
            }

            if (m_spIsHasData != nullptr && !(*m_spIsHasData))
            {
                *m_spIsHasData = true;
            }

            // 如果不需要输出内容 直接继续枚举
            if (!m_isWriteContent)
            {
                return 0;//继续枚举
            }

            ks_bstr bstrAddress;
            if (m_bGetHyperLinkInfo)
            {
                CELL cell{row, col};
                ks_stdptr<etoldapi::Hyperlink> spHL;
                m_spWorkSheet->HasHyperlink(cell, &spHL, TRUE);
                if (spHL)
                {
                    ks_stdptr<IHyperlinkInfo> spHLInfo = spHL;
                    ks_stdptr<IKHyperlink> spKHL;
                    spHLInfo->GetETHyperlink(&spKHL);
                    if (spKHL)
                        spKHL->GetAddress(&bstrAddress);
                }
            }

            ks_bstr fmlaText;
            m_ctx->getStringTools()->GetCellFmlaText(m_pSheet, row, col, &fmlaText, NULL);

            wo::sa::Leave itemLeave = wo::sa::enterStruct(m_acpt, nullptr);

            m_acpt->addInt32("rowFrom", rowFrom);
            m_acpt->addInt32("rowTo", rowTo);
            m_acpt->addInt32("colFrom", colFrom);
            m_acpt->addInt32("colTo", colTo);
            m_acpt->addInt32("originRow", row);
            m_acpt->addInt32("originCol", col);
            
            m_acpt->addString("cellText", text.c_str()  == NULL ? __X("") : text.c_str());

            m_acpt->addString("originalCellValue", originalCellValue.c_str() == NULL ? __X("") : originalCellValue.c_str());
            if (!bstrAddress.empty())
                m_acpt->addString("cellTextLink", bstrAddress.c_str());

            if (m_bGetHyperLinkRunAddress)
                getAddress(row, col);
            
            if (fmlaText.c_str() != NULL) 
            {
                m_acpt->addString("fmlaText", fmlaText.c_str());
            }

            //数值格式类型
            const XF* pXF = NULL;
            m_op->GetCellFormat(m_sheetIdx, row, col, &pXF, NULL);
            const NUMFMT* pNumFmt = pXF->pNumFmt;
            PCWSTR pcwNumfmt = pNumFmt->fmt;
            m_acpt->addString("numFormat", pcwNumfmt == NULL ? __X("") : pcwNumfmt);
            CellValueType cellValueType = CellValueType::CVT_BASIC;
            auto writeCellValueType = qScopeGuard([&] {
                switch (cellValueType)
                {
                    case CellValueType::CVT_BASIC:
                        m_acpt->addString("cellValueType", __X("basic"));
                        break;
                    case CellValueType::CVT_CELLPIC:
                        m_acpt->addString("cellValueType", __X("cellPic"));
                        break;
                    case CellValueType::CVT_QRLABEL:
                        m_acpt->addString("cellValueType", __X("qrLabel"));
                        break;
                    default:
                        ASSERT(FALSE);
                        break;
                }
            });

            if (alg::const_handle_token_assist::is_type(pToken))
            {
                alg::const_handle_token_assist chta(pToken);
                if (chta.get_handleType() == alg::ET_HANDLE_QRLABEL)
                {
                    ks_stdptr<IQRLabelHandle> spTokenQRLabel = chta.get_handle()->CastUnknown();
                    if (spTokenQRLabel)
                    {
                        cellValueType = CellValueType::CVT_QRLABEL;
                        m_acpt->addKey("qrLabel");
                        spTokenQRLabel->SerialContent(m_acpt, false);
                    }
                }
            }

            wo::serialHttpUnderstandableType(m_ctx, m_pSheet, m_op, m_sheetIdx, row, col, pToken, text.c_str(), m_acpt);

            //判断是否为单元格图片
            if(!alg::const_vstr_token_assist::is_type(pToken) || !m_pCellImages)
            {
                m_acpt->addBool("isCellPic", FALSE);
                return 0;//继续枚举
            }
                
            CellImg_Param param;
            ks_wstring strCellValue = alg::const_vstr_token_assist(pToken).get_value();
            if (m_pCellImages->GetCellImgParamFromCellValue(strCellValue.c_str(), &param) == FALSE)
            {
                m_acpt->addBool("isCellPic", FALSE);
                return 0;//继续枚举
            }
            //单元格图片	
            m_acpt->addBool("isCellPic", TRUE);
            cellValueType = CellValueType::CVT_CELLPIC;
            IKShape* pShape = m_pCellImages->GetImgByName(param.getNameStr());
            ks_castptr<drawing::AbstractShape> spShape = pShape;
            if (!spShape)
            {
                WOLOG_INFO << "[ExportRangeData] get shape failed!";	
                m_acpt->addBool("isCellPic", FALSE);
                return 0;//继续枚举
            }
                
            if (ExportShape(spShape, m_acpt,  m_ctx->getUser()->getDpi(), true))
                return 0;
                    
            return 0;//继续枚举
        };

        int m_sheetIdx;
        ks_stdptr<IBookOp> m_op;
        ICellImages *m_pCellImages;
        ISheet *m_pSheet;
        wo::KEtRevisionContext *m_ctx;
        ISerialAcceptor* m_acpt;
        OptionColContext* m_optionColContext;
        bool m_isWriteContent;
        bool* m_spIsHasData;
        std::map<RANGE, int, Cmp>* m_spMapMergeRg;
        IRowColOp* m_pRowColOp = nullptr;
        bool m_bGetHyperLinkInfo;
        bool m_bGetHyperLinkRunAddress;
        ks_stdptr<IKWorksheet> m_spWorkSheet;

    private:
        BOOL handMerge(int row, int col, int& rowFrom, int& rowTo, int& colFrom, int& colTo)
        {
            BOOL bMerge = FALSE;
            m_pSheet->IsMerged(row, col, &bMerge);
            if (bMerge)
            {
                //合并单元格
                RANGE rgCell(m_pSheet->GetBMP());
                rgCell.SetCell(m_sheetIdx, row, col);

                ks_stdptr<IKRanges> ptrRgs;
                m_pSheet->FindEffectMergeCell(rgCell, FALSE, &ptrRgs);
                const RANGE* pMergeRg = NULL;
                ptrRgs->GetItem(0, NULL, &pMergeRg);
                ASSERT(pMergeRg);
                rowFrom = pMergeRg->RowFrom();
                rowTo = pMergeRg->RowTo();
                colFrom = pMergeRg->ColFrom();
                colTo = pMergeRg->ColTo();
            }
            return bMerge;
        }

        // 获取单元格附件的address
        BOOL getAddress(int row, int col)
        {
            ks_stdptr<IUnknown> spUnk;
            m_pSheet->GetExtDataItem(edSheetHyperlinks, &spUnk);
            if (spUnk == nullptr)
                return FALSE;

            ks_stdptr<IKHyperlinks> spKHLs;
            VS(spUnk->QueryInterface(IID_IKHyperlinks, (void**)&spKHLs));

            ks_stdptr<IKHyperlink> spKHL;
            if(spKHLs->HasHyperLinkWithRuns(row, col, &spKHL) != S_OK || spKHL == nullptr)
                return FALSE;

            ks_stdptr<IHyperlinkRuns> spRuns;
            spKHL->GetRuns(&spRuns);
            UINT count = spRuns->GetRunsCount();
            if (count == 0)
                return FALSE;

            wo::sa::Leave runsLeave = wo::sa::enterArray(m_acpt, "cellTextLinkRuns");
            for (UINT i = 0; i < count; i++)
            {
                IHyperlinkRun* run = spRuns->GetRun(i);
                if (run == nullptr)
                    continue;

                wo::sa::Leave runsLeave = wo::sa::enterStruct(m_acpt, nullptr);
                run->serialRun(m_acpt);
            }
            return TRUE;
        }
};

HRESULT EtHttpQueryClassBase::PostExecute(HRESULT hr, const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
    pCtx->postExecute(hr);

    if (FAILED(hr))
    {
        binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
        KSerialWrapBinWriter acpt(*pResponse, pCtx);
        AddHttpError(hr, &acpt, m_errMsg.empty() ? nullptr : m_errMsg.c_str());
        m_errMsg.clear();
    }
    return EtQueryExecBase::PostExecute(hr, param, pCtx);
}

HRESULT EtHttpQueryClassBase::PreExecute(const VarObj& param, KEtRevisionContext* ctx)
{
    //检测是否是操作的db表， db数据表不允许调用
    IDX sheetIdx = GetSheetIdx(param, ctx);
    if (sheetIdx >= 0)
    {
        HRESULT hr = CheckDBSheet(sheetIdx);
        if (FAILED(hr))
            return hr;
    }

    return EtQueryExecBase::PreExecute(param, ctx);
}

HRESULT EtHttpQueryClassBase::CheckDBSheet(IDX sheetIdx)
{
    IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    if (pWorksheet == NULL) 
    {
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ks_stdptr<ISheet> spSheet = pWorksheet->GetSheet();
    if (spSheet == NULL) 
    {
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    if (spSheet->IsDbSheet())
    {
        return E_KSHEET_NOT_GRIDSHEET;
    }
    return S_OK;
}

//////////////////////////////////////////////////////////////////////// EtHttpJudgeRangeIsPicQueryClass
EtHttpJudgeRangeIsPicQueryClass::EtHttpJudgeRangeIsPicQueryClass(KEtWorkbook* wb)
        : EtHttpQueryClassBase(wb, __X("http.et.judgeRangePic"))
{
}

HRESULT EtHttpJudgeRangeIsPicQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{

    VAR_OBJ_EXPECT_STRUCT(param, "range")
    binary_wo::VarObj varRange = param.get("range");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")

    IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();

    RANGE rgJudge(pBook->GetBMP());
    IDX sheetIdx = INVALIDIDX;

    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    m_wwb->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    ISheet* pSheet;
    pBook->GetSheet(sheetIdx, &pSheet);
    if (pSheet->IsGridSheet() == false)
        return E_KSHEET_NOT_GRIDSHEET;
    RECT effectiveRect;
    pSheet->GetUsedScale(&effectiveRect);

    int varRangeRowFrom = varRange.field_int32("rowFrom");
    int varRangeRowTo = varRange.field_int32("rowTo");
    int varRangeColFrom = varRange.field_int32("colFrom");
    int varRangeColTo = varRange.field_int32("colTo");
    rgJudge.SetSheetFromTo(sheetIdx);
    rgJudge.SetRowFromTo(varRangeRowFrom, varRangeRowTo > effectiveRect.bottom ? effectiveRect.bottom : varRangeRowTo);
    rgJudge.SetColFromTo(varRangeColFrom, varRangeColTo > effectiveRect.right ? effectiveRect.right : varRangeColTo);

    if (!rgJudge.IsValid())
    {
        WOLOG_INFO << "rgJudge no visible";
        return E_FAIL;
    }

    VAR_OBJ_EXPECT_ARRAY(param, "optionCols")
    binary_wo::VarObj optionColsVar = param.get("optionCols");
    int optionColsLength = optionColsVar.arrayLength();

    RANGE serializeRg(pSheet->GetBMP());
    serializeRg.SetSheetFromTo(sheetIdx, sheetIdx);

    et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

    acpt.addKey("results");
    acpt.beginArray();
    for (int i = 0; i < optionColsLength; i++)
    {
        bool isAllCellImage = false;
        int col = optionColsVar.at(i).value_int32();
        acpt.beginStruct();
        int rowFrom = rgJudge.RowFrom();
        int rowTo = rgJudge.RowTo();

        col = col + rgJudge.ColFrom();

        serializeRg.SetRowFromTo(rowFrom, rowTo);
        serializeRg.SetColFromTo(col, col);
        JudgeIsPicApct judgeIsPicApct(m_wwb->GetCoreWorkbook(), ctx, pSheet, &isAllCellImage);
        spSheetEnum->EnumCellValue(serializeRg, &judgeIsPicApct);

        acpt.addInt32("isAllCellImage", isAllCellImage ? 1 : 0);

        acpt.endStruct();
    }
    acpt.endArray();

    return S_OK;
}

/////////////////////////////////////////////////////////////////////// EtHttpGetColTypeQueryClass
EtHttpGetColTypeQueryClass::EtHttpGetColTypeQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getColType"))
{
}

HRESULT EtHttpGetColTypeQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{

    IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();

    VAR_OBJ_EXPECT_ARRAY(param, "optionCols");
    binary_wo::VarObj optionColsVar = param.get("optionCols");

    int size = optionColsVar.arrayLength();
    if (size <= 0) 
    {
        return E_FAIL;
    }

    IDX sheetIdx = INVALIDIDX;

    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    m_wwb->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    ISheet* pSheet;
    pBook->GetSheet(sheetIdx, &pSheet);
    if (pSheet->IsGridSheet() == false)
        return E_KSHEET_NOT_GRIDSHEET;

    RANGE range(pBook->GetBMP());
    range.SetSheetFromTo(sheetIdx);
    range.SetColFromTo(0, 0);
    range.SetRowFromTo(1, 1);

    Database::FieldsManager *pDbMgr = Database::FieldsManager::Instance();
    Database::FieldContext fieldContext(m_wwb, ctx);
    
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("types");
    acpt.beginArray();
    for (int i = 0; i < size; i++)
    {   
        acpt.beginStruct();
        int col = optionColsVar.at(i).value_int32();
        range.SetColFromTo(col, col);
        wo::util::VALIDATION_Wrap dv;
        ks_stdptr<IBookOp> spBookOp;
        pBook->GetOperator(&spBookOp);
        RANGE *pRgn = NULL;
        spBookOp->GetDataValidation(range, sheetIdx, range.RowFrom(), range.ColFrom(), &dv, NULL, pRgn, NULL);

        Database::IDbField* pIDbField = pDbMgr->IdentifyAll(&fieldContext, range, dv);
        ks_wstring sType = __X("");
        if (pIDbField != nullptr)
        {
            sType = Database::Utils::FieldTypeToStr(pIDbField->GetType());
        }

        acpt.addString("type", sType.c_str());

        if (pIDbField != nullptr && pIDbField->GetType() == Database::dftList && dv.bsFormula1)
        {   
            acpt.addString("formula1", dv.bsFormula1);
        }
        acpt.endStruct();
    }
    acpt.endArray();

    return S_OK;
}

/////////////////////////////////////////////////////////////////////////
EtHttpRetrieveRecordQueryClass::EtHttpRetrieveRecordQueryClass(KEtWorkbook* wb)
	: EtHttpQueryClassBase(wb, __X("http.et.retrieveRecord"))
{
}

std::set<int> EtHttpRetrieveRecordQueryClass::removeDuplicates(std::set<int>& rows, ISheet* pSheet, std::vector<long>& cols, const RANGE& rgFilter)
{

    class HandDuplicatesResult : public IHandDuplicatesResult
    {

    public:
        std::set<int>* m_pResult;
        const RANGE* m_pRgFilter;
        HandDuplicatesResult(std::set<int>* pResult, const RANGE* pRgFilter) 
            : m_pResult(pResult), m_pRgFilter(pRgFilter)
            {}

        void result(int i)
        {
            m_pResult->insert(i - 1 - m_pRgFilter->RowFrom());
        };
    };

    ks_stdptr<IRemoveDuplicateItems> ptrUniqueTool;
    HRESULT hr = _appcore_CreateObject(CLSID_KRemoveDuplicateItems, IID_IRemoveDuplicateItems, (void**)&ptrUniqueTool);
    if (FAILED(hr) || !ptrUniqueTool)
        return rows;
    ptrUniqueTool->Initialize(pSheet);

    std::vector<int> rowVec;

    for (auto it = rows.begin(); it != rows.end(); it++)
    {
        int row = *it + 1 + rgFilter.RowFrom();
        rowVec.push_back(row);
    }

    std::set<int> readSet;
    HandDuplicatesResult handDuplicatesResult(&readSet, &rgFilter);

    ptrUniqueTool->HandDuplicates(&rowVec[0], rowVec.size(), &cols[0], cols.size(), &handDuplicatesResult);

    return readSet;
}

HRESULT EtHttpRetrieveRecordQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{

    VAR_OBJ_EXPECT_STRUCT(param, "range")
    binary_wo::VarObj varRange = param.get("range");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")

    IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();

    RANGE rgFilter(pBook->GetBMP());
    IDX sheetIdx = INVALIDIDX;

    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    m_wwb->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    ISheet* pSheet;
    pBook->GetSheet(sheetIdx, &pSheet);
    if (pSheet->IsGridSheet() == false)
        return E_KSHEET_NOT_GRIDSHEET;

    RECT effectiveRect;
    pSheet->GetUsedScale(&effectiveRect);

    int varRangeRowFrom = varRange.field_int32("rowFrom");
    int varRangeRowTo = varRange.field_int32("rowTo");
    int varRangeColFrom = varRange.field_int32("colFrom");
    int varRangeColTo = varRange.field_int32("colTo");
    rgFilter.SetSheetFromTo(sheetIdx);
    rgFilter.SetRowFromTo(varRangeRowFrom, varRangeRowTo > effectiveRect.bottom ? effectiveRect.bottom : varRangeRowTo);
    rgFilter.SetColFromTo(varRangeColFrom, varRangeColTo > effectiveRect.right ? effectiveRect.right : varRangeColTo);

    if (!rgFilter.IsValid())
    {
        WOLOG_INFO << "rgFilter no visible";
        return E_FAIL;
    }

    // 分页相关 非必要参数
    // 每页的大小
    int pageSize = 100;
    // 在第几页
    int page = 1;
    if (param.has("page"))
    {
        VAR_OBJ_EXPECT_STRUCT(param, "page")
        binary_wo::VarObj pageVar = param.get("page");
        VAR_OBJ_EXPECT_NUMERIC(pageVar, "pageSize")
        VAR_OBJ_EXPECT_NUMERIC(pageVar, "page")
        pageSize = pageVar.field_int32("pageSize");
        page = pageVar.field_int32("page");
        if (page < 1 || pageSize < 1)
        {
            WOLOG_INFO << "page:" << page << " pageSize:" << pageSize;
            return E_FAIL;
        }
    }

    // 分页的起始点
	int start = pageSize * (page - 1);
	// 分页的结束点
	int end = start + pageSize;

    // 需要获取第几列选项数据
    std::vector<int> optionCols;
    if (param.has("optionCols"))
    {
        VAR_OBJ_EXPECT_ARRAY(param, "optionCols")
        binary_wo::VarObj optionColsVar = param.get("optionCols");

        int optionColsLength = optionColsVar.arrayLength();
        for (int i = 0; i < optionColsLength; i++)
        {
            // 转化成文档坐标
            int col = optionColsVar.at(i).value_int32();
            // 判断是否超过了区域
            if (col >= rgFilter.Width())
            {
                WOLOG_INFO << "optionCols col:" << col << " rgFilter.Width():" << rgFilter.Width();
                return E_FAIL;
            }
            optionCols.push_back(col + rgFilter.ColFrom());
        }
    }

    bool isShowTotal = false;
    if (param.has("showTotal"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "showTotal")
        isShowTotal = param.field_bool("showTotal");
    }

    bool ignoreHiddenCell = false;
    if (param.has("ignoreHiddenCell"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "ignoreHiddenCell")
        ignoreHiddenCell = param.field_bool("ignoreHiddenCell");
    }

    bool isShowHyperlinkRunAddress = false;
    if (param.has("showHyperlinkRunAddress"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "showHyperlinkRunAddress")
        isShowHyperlinkRunAddress = param.field_bool("showHyperlinkRunAddress");
    }

    bool isShowHyperlinkInfo = false;
    if (param.has("showHyperlinkInfo"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "showHyperlinkInfo")
        isShowHyperlinkInfo = param.field_bool("showHyperlinkInfo");
    }

    OptionColContext optionColContext(&optionCols, &rgFilter, start, end);

    ks_stdptr<IKEtExtraFilter> ptrEtExtraFilter;
    _appcore_CreateObject(CLSID_KEtExtraFilter, IID_IKEtExtraFilter, (void**)&ptrEtExtraFilter);

    std::set<int> result;
    KEtExtraResult extraResult(&result);

    VAR_OBJ_EXPECT_STRUCT(param, "filter")
    binary_wo::VarObj filterVar = param.get("filter");

    std::map<int, std::vector<ks_wstring>> mapSearch;

    if (filterVar.has("search"))
    {
        VAR_OBJ_EXPECT_ARRAY(filterVar, "search");
        binary_wo::VarObj searchVar = filterVar.get("search");
        int searchLength = searchVar.arrayLength();
        for (int i = 0; i < searchLength; i++)
        {
            binary_wo::VarObj itemVar = searchVar.at(i);
            VAR_OBJ_EXPECT_NUMERIC(itemVar, "col")
            int col = itemVar.field_int32("col");

            VAR_OBJ_EXPECT_ARRAY(itemVar, "value");
            binary_wo::VarObj valueVar = itemVar.get("value");

            std::vector<ks_wstring> vecSearch = mapSearch[col];
            int colSearchLength = valueVar.arrayLength();
            for (int i = 0; i < colSearchLength; i++)
            {   
                ks_wstring temp = valueVar.at(i).value_str();
                if (temp.empty())
                {
                    continue;
                }
                vecSearch.push_back(temp);  
            }

            mapSearch[col] = vecSearch;
        }
    }

    bool isOptionCols = optionCols.size() > 0;
    // 如果输出col选项，或者需要返回全部数据的数量，在外面进行分页
    bool isOutSidePagin = isOptionCols || isShowTotal || mapSearch.size() > 0 || filterVar.has("duplicates");
    if (isOutSidePagin)
    {
        ptrEtExtraFilter->Init(pBook, pSheet, &rgFilter, &extraResult, -1, -1);
    }
    else 
    {   
        ptrEtExtraFilter->Init(pBook, pSheet, &rgFilter, &extraResult, start, end);
    }


    VAR_OBJ_EXPECT_ARRAY(filterVar, "condition")
    binary_wo::VarObj condition = filterVar.get("condition");
    int length = condition.arrayLength();
    for (int i = 0; i < length; i++)
    {
        std::vector<PCWSTR> vecParam;

        binary_wo::VarObj itemVar = condition.at(i);

        VAR_OBJ_EXPECT_NUMERIC(itemVar, "col")
        int col = itemVar.field_int32("col");
        if (col < 0 || col >= rgFilter.Width())
        {
            WOLOG_INFO << "col:" << col << " rgFilter.Width():" << rgFilter.Width();
            return E_FAIL;
        }


        VAR_OBJ_EXPECT_ARRAY(itemVar, "info")
        binary_wo::VarObj infoVar = itemVar.get("info");

        int infoLength = infoVar.arrayLength();
        for (int j = 0; j < infoLength; j++)
        {
            binary_wo::VarObj infoItemVar = infoVar.at(j);

            VAR_OBJ_EXPECT_STRING(infoItemVar, "value")
            PCWSTR value = infoItemVar.field_str("value");
            vecParam.push_back(value);
        }
  
        // 筛选模式为非必要参数
        ks_wstring mode = __X("");
        if (itemVar.has("mode"))
        {
            mode = itemVar.field_str("mode");
        }
        if (vecParam.size() > 0) {
            // 添加筛选条件
            ptrEtExtraFilter->addNormalFilterCondition(col, &vecParam[0], vecParam.size(), mode != __X("OR") ? FOp_And : FOp_Or);
        }
       
    }

    if (length > 0) 
    {
        ptrEtExtraFilter->filter();
    }
    else
    {
        // 没有条件 则直接输出全部行，筛选会忽略表头 这里需要减1 不然会多输出
        int size = rgFilter.Height() - 1;
        for (int i = 0; i < size; i++)
        {
            result.insert(i);
        }
        // 存在一种情况，是不需要输出选项，不输出输出总数，不需要搜索，会导致分页失效，这里强制设置在外部分页
        isOutSidePagin = true;
    }
   
    et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);
    ks_stdptr<IRowColOp> spRowColOp;
    pSheet->GetCurUserOperator(&spRowColOp);


    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

    RANGE serializeRg(pSheet->GetBMP());
    serializeRg.SetSheetFromTo(sheetIdx, sheetIdx);

    int resultType = 0;
    if (result.size() <= 0) 
    {   
        // 上面没有条件，默认输出全部，所以跑到这里 说明没有结果，直接返回即可。
        acpt.addInt32("resultType", resultType);
        return S_OK; 
    }

    // 去除重复项相关
    if (filterVar.has("duplicates"))
    {
        std::vector<long> duplicateVec;

        VAR_OBJ_EXPECT_STRUCT(filterVar, "duplicates")
        binary_wo::VarObj duplicatesVar = filterVar.get("duplicates");
        VAR_OBJ_EXPECT_ARRAY(duplicatesVar, "col");
        binary_wo::VarObj duplicatesColVar = duplicatesVar.get("col");
        int duplicatesLength = duplicatesColVar.arrayLength();
        for (int i = 0; i < duplicatesLength; i++)
        {
            
            int col = duplicatesColVar.at(i).value_int32();
            if (col < 0 || col >= rgFilter.Width())
            {
                WOLOG_INFO << "col:" << col << " rgFilter.Width():" << rgFilter.Width();
                return E_FAIL;
            }

            col = col + rgFilter.ColFrom();
            duplicateVec.push_back(col);
        }

        if (duplicateVec.size() > 0)
        {
            result = removeDuplicates(result, pSheet, duplicateVec, rgFilter);
        }
    }

    // 合并单元格的信息
    std::map<RANGE, int, Cmp> mergeRgMap;

    acpt.addKey("rangeData");
    acpt.beginArray();
    int count = 0;
    for (auto it = result.begin(); it != result.end(); it++)
    {
        int row = *it + 1 + rgFilter.RowFrom();

        int colFrom = rgFilter.ColFrom();
        int colTo = rgFilter.ColTo();

        serializeRg.SetRowFromTo(row, row);
        serializeRg.SetColFromTo(colFrom, colTo);

        // 先判断当前行，是否符合搜索条件
        if (mapSearch.size() > 0)
        {
            bool isInSearch = false;
            SearchApct searchApct(m_wwb->GetCoreWorkbook(), &mapSearch, ctx, pSheet, &isInSearch);
            spSheetEnum->EnumCellValue(serializeRg, &searchApct);
            if (!isInSearch)
            {
                continue;
            }
        }

        // 如果不在外面分页，或者 条件判断在页面内，则要输出内容
        bool isInPage = !isOutSidePagin || (count >= start && count < end);
        bool isHasData = false;
        CellValueAcpt cellValue(m_wwb->GetCoreWorkbook(), sheetIdx, pSheet, &acpt, ctx
            , isOptionCols ? &optionColContext : nullptr, isInPage, &isHasData, isShowHyperlinkInfo, isShowHyperlinkRunAddress);
        if (ignoreHiddenCell)
            cellValue.SetRowColOp(spRowColOp);
        int ret = spSheetEnum->EnumCellValue(serializeRg, &cellValue);
        if (isHasData)
        {
            count++;
        }
        
        if (resultType == 0 && count > 0 && isInPage)
        {
            resultType = 1;
        }

        // 寻找与这个区域相交的合并区域
        std::vector_s<RANGE> vecRgs;
        pSheet->FindEffectMergeCell(serializeRg, FALSE, vecRgs);
        if (vecRgs.size() <= 0)
        {
            continue;
        }
        
        for (int j = 0, n = vecRgs.size(); j < n; j++)
        {
            RANGE* tempRg = &vecRgs.at(j);
            auto it = mergeRgMap.find(*tempRg);
            if (it == mergeRgMap.end())
            {
                mergeRgMap.insert(std::make_pair(*tempRg, row));
                if (isOptionCols)
                {
                    // 获取合并单元格的值
                    ks_bstr text;
                    int mergeRowFrom = tempRg->RowFrom();
                    int mergeColFrom = tempRg->ColFrom();
                    int mergeColTo = tempRg->ColTo();
                    ctx->getStringTools()->GetCellText(pSheet, mergeRowFrom, mergeColFrom, &text, NULL, -1, NULL);

                    // 起始的坐标取最大
                    int start = mergeColFrom > colFrom ? mergeColFrom : colFrom;
                    // 结束的坐标取最小
                    int end = mergeColTo < colTo ? mergeColTo : colTo;

                    // 这一行的横向，都需要输出
                    for (int z = start; z <= end; z++)
                    {   
                        optionColContext.PutColData(z, text.c_str(), text.c_str());
                    }
                }
            }
        }

    }

    acpt.endArray();
    
    if (mergeRgMap.size() > 0)
    {

        IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
        ks_stdptr<IBookOp> spBookOp;
        pBook->GetOperator(&spBookOp);

        // 输出合并单元格的信息
        acpt.addKey("mergeRangeData");
        acpt.beginArray();

        for (auto it = mergeRgMap.begin(); it != mergeRgMap.end(); it++)
        {
            int row = it->second;

            if (row < 0)
            {
                continue;
            }

            RANGE mergeRange = it->first;
            CellValueAcpt cellValue(m_wwb->GetCoreWorkbook(), sheetIdx, pSheet, &acpt, ctx);

            const_token_ptr pToken = nullptr;
            spBookOp->GetCellValue(sheetIdx, mergeRange.RowFrom(), mergeRange.ColFrom(), &pToken);
            if (pToken == nullptr)
            {
                acpt.beginStruct();
                acpt.addInt32("rowFrom", mergeRange.RowFrom());
                acpt.addInt32("rowTo", mergeRange.RowTo());
                acpt.addInt32("colFrom", mergeRange.ColFrom());
                acpt.addInt32("colTo", mergeRange.ColTo());
                acpt.addInt32("originRow", row);
                acpt.addInt32("originCol", mergeRange.ColFrom());
                
                acpt.addString("cellText", __X(""));
                acpt.addString("originalCellValue", __X(""));

                acpt.endStruct();

                continue;
            }
            cellValue.Do(row, mergeRange.ColFrom(), pToken, true);
        }

        acpt.endArray();
    }

    // 在外面分页，输出全部个数
    if (isShowTotal)
    {
        acpt.addInt32("total", count);
    }

    WOLOG_INFO << "count:" << count;

    // 存在 在外面分页的情况，result需要在分页之后才能确定结果
    acpt.addInt32("resultType", resultType);

    if (isOptionCols)
    {
        optionColContext.Serialize(&acpt, count);
    }

    return S_OK;
}

//////////////////////////////////////////////////////////////////////////
EtHttpSheetsInfoQueryClass::EtHttpSheetsInfoQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getSheetsInfo"))
{
}

HRESULT EtHttpSheetsInfoQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    IKWorkbook *pWorkbook = m_wwb->GetCoreWorkbook();
    IKWorksheets *pWorksheets = pWorkbook->GetWorksheets();
    INT iSheetCnt = pWorksheets->GetSheetCount();
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("sheetsInfo");
    acpt.beginArray();
    for (int i = 0; i < iSheetCnt; i++)
    {
        IKWorksheet *pWorksheet = pWorksheets->GetSheetItem(i);
        ISheet *pSheet = pWorksheet->GetSheet();
        bool isVisible = false;
        SHEETSTATE state = ssVisible;
        pSheet->GetVisible(&state);
        if (state == ssVeryhidden)
            continue;
        else if (state == ssVisible)
            isVisible = true;

        PCWSTR sheetName = nullptr;
        pSheet->GetName(&sheetName);
        UINT iStSheet = pSheet->GetStId();

        HRESULT hr = CheckSheetPermission(iStSheet, ctx);
        if (FAILED(hr))
            continue;

        RANGE rg(pSheet->GetBMP());
        ks_stdptr<IKWorksheet> spWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(i);
        spWorksheet->GetUsedRange(&rg);
        ISheetProtection* pSheetProtection = spWorksheet->GetProtection();
		bool isProtected = pSheetProtection->IsProtected();

        acpt.beginStruct();
            acpt.addString("sheetName", sheetName);
            acpt.addUint32("sheetIdx", i);
            acpt.addUint32("sheetId", iStSheet);
            acpt.addInt32("rowFrom", rg.RowFrom());
            acpt.addInt32("rowTo", rg.RowTo());
            acpt.addInt32("colFrom", rg.ColFrom());
            acpt.addInt32("colTo", rg.ColTo());
            acpt.addInt32("maxRow", rg.GetBMP()->cntRows - 1);
            acpt.addInt32("maxCol", rg.GetBMP()->cntCols - 1);
            acpt.addBool("isVisible", isVisible);
            acpt.addBool("isProtected", isProtected);
            acpt.addBool("isEmpty", util::IsRangeEmpty(ctx, pSheet, rg));
            acpt.addString("sheetType", getSheetTypeStr(pSheet));
        acpt.endStruct();
    }
    acpt.endArray();

    return S_OK;
}

class CellTextAcpt : public ICellValueAcpt
{
    public:
        CellTextAcpt(etoldapi::_Workbook* wb, int sheetIdx, ISheet *pSheet,ISerialAcceptor* acpt, wo::KEtRevisionContext* ctx, std::vector<std::vector<ks_wstring>>* matrix)
            :m_sheetIdx(sheetIdx)
            ,m_pSheet(pSheet)
            ,m_acpt(acpt)
            ,m_ctx(ctx)
            ,m_rangeMatrix(matrix)
        {
            pSheet->GetCurUserOperator(&m_spRowColOp);
        }

        void setRangeFrom(int rowFrom, int colFrom)
        {
            m_rowFrom = rowFrom;
            m_colFrom = colFrom;
        }

        STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
        {
            return Do(row, col, pToken, false);
        }

        INT Do(ROW row, COL col, const_token_ptr pToken, bool bFromOutputMerge)
        {   
            if(!pToken)
            {
                return 0;//继续枚举
            }
               
            //判断读取的区域是否在保护区域内，若在，禁止读取
            bool bCellInvisibleForCurUser = m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col);
            if(bCellInvisibleForCurUser)
                return 0;//继续枚举

            //获取数据
            ks_bstr text;
            m_ctx->getStringTools()->GetCellText(m_pSheet, row, col, &text, NULL, -1, NULL);
            
            if (m_rangeMatrix)
            {
                (*m_rangeMatrix)[row - m_rowFrom][col - m_colFrom] = ks_wstring(text.c_str());
            }
            return 0;//继续枚举
        };
    private:
         int m_sheetIdx = 0;
         int m_rowFrom = 0;
         int m_colFrom = 0;

        ISheet *m_pSheet = nullptr;
        wo::KEtRevisionContext *m_ctx = nullptr;
        ISerialAcceptor* m_acpt = nullptr;
        ks_stdptr<IRowColOp> m_spRowColOp;
        std::vector<std::vector<ks_wstring>>* m_rangeMatrix = nullptr;
};

EtHttpRangeDataQueryClass::EtHttpRangeDataQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getRangeData"))
{
}

HRESULT EtHttpRangeDataQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    VAR_OBJ_EXPECT_STRUCT(param, "range")
    binary_wo::VarObj varRange = param.get("range");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    ks_stdptr<etoldapi::_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();

    RANGE rg(spWorkbook->GetBook()->GetBMP());
    IDX sheetIdx = INVALIDIDX;
    m_wwb->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;
    rg.SetSheetFromTo(sheetIdx);
    rg.SetRowFromTo(
        varRange.field_int32("rowFrom"), varRange.field_int32("rowTo"));
    rg.SetColFromTo(
        varRange.field_int32("colFrom"), varRange.field_int32("colTo"));

    if(!rg.IsValid())
    {
        WOLOG_INFO << "[ExportRangeData] param struct error";
        return E_RANGE_INVALID;
    }

    ASSERT(rg.SheetFrom() == rg.SheetTo());
    IKWorksheet* pWorksheet = spWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
    if (pWorksheet == NULL) 
    {
        WOLOG_INFO << "[ExportRangeData] get worksheet failed, sheet idx=" << sheetIdx;	
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ks_stdptr<ISheet> spSheet = pWorksheet->GetSheet();
    if (spSheet == NULL) 
    {
        WOLOG_INFO << "[ExportRangeData] get sheet failed, sheet idx=" << sheetIdx;	
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    if (spSheet->IsGridSheet() == false)
    {
        return E_KSHEET_NOT_GRIDSHEET;
    }

    et_sdptr<ISheetEnum> spSheetEnum;
    spSheet->CreateEnum(&spSheetEnum);
    
    acpt.addKey("rangeData");
    acpt.beginArray();

    bool bGetHyperLinkInfo = false;
    if (param.has("getHyperLinkInfo"))
        bGetHyperLinkInfo = param.field_bool("getHyperLinkInfo");
    CellValueAcpt cellValue(spWorkbook, sheetIdx, spSheet, &acpt, ctx, nullptr, true, nullptr, bGetHyperLinkInfo);
    int ret = spSheetEnum->EnumCellValue(rg, &cellValue);
    acpt.endArray();

    if (wo::VarObjFieldValidation::expectBool(param, "isExportShape"))
    {
        bool isExportShape = alg::bool2BOOL(param.field_bool("isExportShape"));
        if (isExportShape)
            ExportShapeData(pWorksheet, &acpt, rg, ctx);
    }

    return S_OK;
}

void EtHttpRangeDataQueryClass::ExportShapeData(IKWorksheet* pWorksheet, ISerialAcceptor* pAcpt, const RANGE& rg, KEtRevisionContext* pCtx)
{
    if (!pAcpt || !pWorksheet || !pCtx || !pCtx->getUser())
        return;

    ks_stdptr<IKRanges> spRanges;
    if (FAILED(_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRanges)))
        return;

    spRanges->Append(alg::STREF_THIS_BOOK, rg);

    ISheet* spSheet = pWorksheet->GetSheet();

    ks_stdptr<IKShapeRange> pShapeRange;
    if (S_OK != GetRangeCoverShapes(spSheet,
            pWorksheet->GetDrawingCanvas(),
            spRanges.get(), &pShapeRange, true))
    {
        return;
    }

    pAcpt->addKey("shapeData");
    pAcpt->beginArray();

    LONG count = 0;
    pShapeRange->GetShapeCount(&count);
    for (LONG index = 0; index < count; index++)
    {
        ks_stdptr<drawing::AbstractShape> spShape;
        pShapeRange->GetShapeByIndex(index, &spShape);
        if (!spShape)
            continue;

        pAcpt->beginStruct();
            ExportShape(spShape, pAcpt, pCtx->getUser()->getDpi(), false);
        pAcpt->endStruct();
    }
    pAcpt->endArray();
}

//////////////////////////////////////////////////////////////////////////
EtHttpDefinedNameQueryClass::EtHttpDefinedNameQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getDefinedName"))
{
}

HRESULT EtHttpDefinedNameQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);
	INT nNameCount = 0;
	spBookOp->GetNameUdfCount(&nNameCount);
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("definedName");
    acpt.beginArray();
	for (INT iName = 0; iName < nNameCount; ++iName)
	{
		IDX iSheet = 0;
		PCWSTR strName = nullptr;
		HRESULT  hr = spBookOp->GetDefinedNameInfo(iName, &iSheet, &strName, nullptr);
        if (FAILED(hr))
            continue;

        if (iSheet >= 0)
        {
            UINT sheetId = 0;
            pBook->RTSheetToSTSheet(iSheet, &sheetId);
            hr = CheckSheetPermission(sheetId, ctx);
            if (FAILED(hr))
                continue;
        }

        ks_stdptr<IFormula> spFmla;
        BSTR bstrFml;
	    spBookOp->GetDefinedNameContent(iName, &spFmla);
        if(spFmla)
        {
            spFmla->GetFormula(&bstrFml, CS_COMPILE_PARAM());
        }
        acpt.beginStruct();
            acpt.addString("name", strName);
            acpt.addString("nameContent", bstrFml);
        acpt.endStruct();
    }
    acpt.endArray();

    return S_OK;
}

////////////// OptionColContext

void OptionColContext::PutColData(int col, const ks_wstring& text, const ks_wstring& originText)
{
    bool isContinue = false;
    int n = m_optionCols->size();
    for (int i = 0; i < n; i++)
    {
        if (m_optionCols->at(i) == col)
        {
            isContinue = true;
            break;
        }
    }

    if (!isContinue)
    {
        return;
    }

    OptionColContextItem* values;
    auto it = m_colMap.find(col);
    if (it == m_colMap.end())
    {
        values = new OptionColContextItem;
        m_colMap.insert(std::make_pair(col, std::unique_ptr<OptionColContextItem>(values)));
    }
    else 
    {
        values = it->second.get();
    }

    auto valueIt = values->map.find(text);
    if (valueIt == values->map.end())
    {
        values->map.insert(std::make_pair(text, 1));
        values->vec.push_back(text);
        values->originTextVec.push_back(originText);
        return;
    }

    int count = valueIt->second;
    values->map.erase(text);
    values->map.insert(std::make_pair(text, count + 1));
}

void OptionColContext::Serialize(KSerialWrapBinWriter *acpt, int count)
{

    if (!m_optionCols || m_optionCols->size() <= 0)
    {
        return;
    }
    acpt->addKey("optionCol");
    acpt->beginArray();

    int colSize = m_optionCols->size();
    for (int i = 0; i < colSize; i++)
    {
        int col = m_optionCols->at(i);
        acpt->beginStruct();
            // 第几行
            acpt->addInt32("col", col - m_rgFilter->ColFrom());
            acpt->addKey("texts");
            acpt->beginArray();

                int tempCount = 0;
                int emptyTextIndex = -1;
                if (m_colMap.find(col) != m_colMap.end())
                {
                     OptionColContextItem* context = m_colMap[col].get();
                    int vecSize = context->vec.size();
                    for (int j = 0; j < vecSize; j++)
                    {   
                        ks_wstring text = context->vec.at(j);
                        if (text == __X(""))
                        {
                            emptyTextIndex = j;
                            continue;
                        }

                        acpt->beginStruct();
                        acpt->addString("text", text.c_str());
                        acpt->addString("origin",context->originTextVec.at(j).c_str());
                        acpt->addInt32("count", context->map[text]);
                        acpt->endStruct();
                        tempCount += context->map[text];
                    }
                }

                if (emptyTextIndex != -1)
                {
                    OptionColContextItem* context = m_colMap[col].get();
                    ks_wstring text = context->vec.at(emptyTextIndex);
                    acpt->beginStruct();
                    acpt->addString("text", text.c_str());
                    acpt->addString("origin",context->originTextVec.at(emptyTextIndex).c_str());
                    acpt->addInt32("count", count - tempCount);
                    acpt->endStruct();
                } 
                else if (tempCount < count)
                {
                    acpt->beginStruct();
                    acpt->addString("text", __X(""));
                    acpt->addString("origin",__X(""));
                    acpt->addInt32("count", count - tempCount);
                    acpt->endStruct();
                }

            acpt->endArray();
        acpt->endStruct();

    }

    acpt->endArray();
}


STDIMP_(void) KEtExtraResult::result(int i) 
{
    m_result->insert(i);
}

//////////////////////////////////////////////////////////////////////////
EtHttpTitleContentQueryClass::EtHttpTitleContentQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getTitleContent"))
{
}

HRESULT EtHttpTitleContentQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    IKWorksheets* pSheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	int sheetLimitItems = param.field_int32("sheetLimitItems");
	bool bHasSheetIdVec = param.has("sheetIdVec");

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

	acpt.addKey("res");
	acpt.beginStruct();

	acpt.addKey("sheets");
	acpt.beginArray();
	if (bHasSheetIdVec)
	{
		VarObj sheetIdVec = param.get("sheetIdVec");
		for (int i = 0, len = sheetIdVec.arrayLength(); i < len; ++i)
        {
            IDX sheetIdx = INVALIDIDX;
            pBook->STSheetToRTSheet(sheetIdVec.at(i).value_int32(), &sheetIdx);
            if (sheetIdx == INVALIDIDX)
                continue;
			WriteTitleContent(ctx, &acpt, sheetIdx, sheetLimitItems);
        }
	}
	else
	{
		for (int i = 0, len = pSheets->GetSheetCount(); i < len; ++i)
			WriteTitleContent(ctx, &acpt, i, sheetLimitItems);
	}
	acpt.endArray();
	acpt.endStruct();
	
	return S_OK;
}

void EtHttpTitleContentQueryClass::WriteTitleContent(IN KEtRevisionContext* ctx, IN ISerialAcceptor* acpt, IN IDX sheetIdx, IN int leftCount)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet || !spSheet->IsGridSheet())
		return;
	
    PCWSTR sheetName = nullptr;
    spSheet->GetName(&sheetName);
    UINT stId = spSheet->GetStId();

    RANGE rg(spSheet->GetBMP());
    IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    pWorksheet->GetUsedRange(&rg);

	acpt->beginStruct();
	acpt->addInt32("sheetId", stId);
    acpt->addString("sheetName", sheetName);

    acpt->addKey("usedRange");
    acpt->beginStruct();
        acpt->addInt32("rowFrom", rg.RowFrom());
        acpt->addInt32("rowTo", rg.RowTo());
        acpt->addInt32("colFrom", rg.ColFrom());
        acpt->addInt32("colTo", rg.ColTo());
    acpt->endStruct();

    TableStructRecHelper::TableTitleInfo info;
	HRESULT hr = TableStructRecHelper::GetTableTitleInfo(m_wwb, ctx, sheetIdx, info);
	if (S_OK != hr || info.errorCode != TableStructRecHelper::TableStructRecSuccessed || info.colFrom > info.colTo)
    {
        acpt->endStruct();
		return;
    }

	acpt->addKey("titles");
	acpt->beginArray();
	for (int j = info.colFrom; j <= info.colTo; ++j)
	{
		if (leftCount <= 0)
			break;

        acpt->beginStruct();
        acpt->addInt32("row", info.row);
        acpt->addInt32("col", j);
		ks_bstr cellText;
		ctx->getStringTools()->GetCellText(spSheet, info.row, j, &cellText, nullptr, -1, nullptr);
        if (cellText)
		    acpt->addString("title", cellText.c_str());

        acpt->addKey("samples");
        acpt->beginArray();
        for (int row = info.row + 1; row < info.row + 4 && row < spSheet->GetBMP()->cntRows; ++row)
        {
            if (ctx->getProtectionCtx()->isCellHidden(spSheet, row, j))
                continue;

            ks_bstr cellText;
            ctx->getStringTools()->GetCellText(spSheet, row, j, &cellText, nullptr, -1, nullptr);
            if (cellText)
                acpt->addString(nullptr, cellText.c_str());
        }
        acpt->endArray();

		--leftCount;
        acpt->endStruct();
	}
	acpt->endArray();
	acpt->endStruct();
}

EtHttpAiRecognizedInfoQueryClass::EtHttpAiRecognizedInfoQueryClass(KEtWorkbook* wb)
	: EtHttpQueryClassBase(wb, __X("http.et.aiRecognizedInfo"))
{
}

static void getSheetResult(ISerialAcceptor* acpt, IKWorksheet* pWorksheet, IEtProtectionCtx* pProtectionCtx, UINT rowLimit, JsonDataType jsonDataType, PCWSTR jmTask, const RANGE* customRg)
{
	sa::Leave itemLeave = sa::enterStruct(acpt, nullptr);
	if (!pWorksheet)
		return;
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!pSheet || pSheet->IsGridSheet() == false)
		return;
	std::vector<RANGE> rgVec;
	// 选择区域目前无用，而ai接口要求必传，暂时直接传第一个单元格
	RANGE rg(pSheet->GetBMP());
	rg.SetRowFromTo(0);
	rg.SetColFromTo(0);
	rgVec.emplace_back(rg);
	QJsonObject obj;
	KIdentifyTable identify(pWorksheet, pProtectionCtx, jsonDataType);
	HRESULT hr = identify.Init();
	if (FAILED(hr))
		return;
	identify.SetRangeLimit(rowLimit);
	identify.SetSelectionRange(rgVec);
    if (nullptr != customRg)
        identify.SetCustomRange(*customRg);
	identify.Identify(obj);
	acpt->addString("sheetType", getSheetTypeStr(pSheet));
	acpt->addUint32("sheetId", pSheet->GetStId());
	acpt->addString("jm_task", jmTask);
	acpt->addInt32("jm_cells", obj.value("jmCells").toInt());
	acpt->addInt32("jm_rangeCells", obj.value("jmRangeCells").toInt());
	if (xstricmp(jmTask, __X("teval")) != 0)
		acpt->addString("table_data", krt::utf16(QString(QJsonDocument(obj).toJson(QJsonDocument::Compact))));
}

HRESULT EtHttpAiRecognizedInfoQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_ARRAY(param, "sheetIdVec")
	VAR_OBJ_EXPECT_STRING(param, "jmTask")
	VarObj sheetIdVec = param.get("sheetIdVec");
	int sheetLen = sheetIdVec.arrayLength_s();
	UINT rowLimit = 0;
	if (param.has("rowLimit"))
		rowLimit = param.field_uint32("rowLimit");
	PCWSTR jmTask = param.field_str("jmTask");
	JsonDataType jsonDataType = JsonDataArrangement;
	HRESULT hr = GetJsonDataType(jmTask, &jsonDataType);
	if (FAILED(hr))
		return hr;
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
	IBook* pBook = pWorkbook->GetBook();
	std::vector<IKWorksheet*> worksheetVec;
	worksheetVec.reserve(sheetLen);
    std::map<UINT, RANGE> cusRgs;
    if (param.has("customRangeVec"))
    {
        VAR_OBJ_EXPECT_ARRAY(param, "customRangeVec")
        binary_wo::VarObj customRangeVec = param.get("customRangeVec");
        for (int i = 0; i < customRangeVec.arrayLength_s(); ++i)
        {
            binary_wo::VarObj obj = customRangeVec.at_s(i);
            VAR_OBJ_EXPECT_NUMERIC(obj, "sheetId");
            VAR_OBJ_EXPECT_STRUCT(obj, "customRange")

            UINT sheetId = obj.field_uint32("sheetId");
            binary_wo::VarObj varRange = obj.get("customRange");
            VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom");
            VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo");
            VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom");
            VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo");

            IDX sheetIdx = INVALIDIDX;
            pBook->STSheetToRTSheet(sheetId, &sheetIdx);
            if (sheetIdx == INVALIDIDX)
                return E_INVALID_REQUEST;
            RANGE customRg(pBook->GetBMP());
            customRg.SetSheetFromTo(sheetIdx);
            customRg.SetRowFromTo(varRange.field_int32("rowFrom"), varRange.field_int32("rowTo"));
            customRg.SetColFromTo(varRange.field_int32("colFrom"), varRange.field_int32("colTo"));
            if (!customRg.IsValid())
                return E_INVALID_REQUEST;
            cusRgs.emplace(sheetId, customRg);
        }
    }
	for (int i = 0; i < sheetLen; ++i)
	{
		IDX sheetIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(sheetIdVec.item_uint32(i), &sheetIdx);
		if (sheetIdx == INVALIDIDX)
			return E_INVALIDARG;
        HRESULT hr = CheckSheetPermission(sheetIdVec.item_uint32(i), ctx);
        if (FAILED(hr))
            return hr;
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
		worksheetVec.emplace_back(pWorksheet);
	}
	IEtProtectionCtx* pProtectionCtx = ctx->getProtectionCtx();
	binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	acpt.addKey("sheets");
	acpt.beginArray();
	for (int i = 0; i < worksheetVec.size(); ++i)
    {
        uint32 id = sheetIdVec.item_uint32(i);
        auto it = cusRgs.find(id);
        RANGE* cusRg= nullptr;
        if (it != cusRgs.end())
            cusRg = &it->second;
        getSheetResult(&acpt, worksheetVec.at(i), pProtectionCtx, rowLimit, jsonDataType, jmTask, cusRg);
    }
	acpt.endArray();
	return S_OK;
}

EtHttpGetTitleContentByAIQueryClass::EtHttpGetTitleContentByAIQueryClass(KEtWorkbook* wb)
	: EtHttpQueryClassBase(wb, __X("http.et.getTitleContentByAI"))
{
}

static PCWSTR getTokenType(const_token_ptr pToken)
{
	switch (GetExecTokenMajorType(pToken))
	{
		case alg::ETP_ERROR:
			return __X("ExecTokenERROR");
		case alg::ETP_VBOOL:
			return __X("ExecTokenBOOL");
		case alg::ETP_VDBL:
			return __X("ExecTokenDBL");
		default:
			break;
	}
	return __X("ExecTokenSTR");
}

static void WriteCellValue(KEtRevisionContext* ctx, ISheet* pSheet, ROW row, COL col, ISerialAcceptor* acpt)
{
	IETStringTools* pStringTools = ctx->getStringTools();
	IEtProtectionCtx* pProtectionCtx = ctx->getProtectionCtx();
	BOOL isMerged = FALSE;
	pSheet->IsMerged(row, col, &isMerged);
	int hMerge = 0, vMerge = 0;
	if (isMerged)
	{
		RANGE rg(pSheet->GetBMP());
		pSheet->RetieveMerge(row, col, &rg);
		ROW rowFrom = rg.RowFrom();
		ROW rowTo = rg.RowTo();
		COL colFrom = rg.ColFrom();
		COL colTo = rg.ColTo();
		if (colFrom != colTo)
			hMerge = col == colFrom ? colTo - colFrom : colFrom - col;
		if (rowFrom != rowTo)
			vMerge = row == rowFrom ? rowTo - rowFrom : rowFrom - row;
		row = rowFrom;
		col = colFrom;
	}
	const_token_ptr pToken = nullptr;
	IDX sheetIdx = INVALIDIDX;
	pSheet->GetIndex(&sheetIdx);
	pSheet->LeakBook()->LeakOperator()->GetCellValue(sheetIdx, row, col, &pToken);
	ks_bstr valueStr;
	if (!pProtectionCtx->isCellHidden(pSheet, row, col))
		pStringTools->GetCellText(pSheet, row, col, &valueStr, nullptr, -1, nullptr);

	acpt->beginStruct();
	acpt->addInt32("row", row + 1);
	acpt->addInt32("col", col + 1);
	acpt->addInt32("h_merge", hMerge);
	acpt->addInt32("v_merge", vMerge);
	acpt->addString("cell_type", getTokenType(pToken));
	acpt->addString("value", valueStr.empty() ? __X("") : valueStr.c_str());
	acpt->endStruct();
}

static void WriteDBTitleContent(KEtRevisionContext* ctx, IKWorksheet* pWorksheet, UINT activeStId, int maxleftCount, ISerialAcceptor* acpt)
{
	// 数据表按照格式要求直接返回表头内容信息
	PCWSTR sheetName = nullptr;
	ISheet* pSheet = pWorksheet->GetSheet();
	pSheet->GetName(&sheetName);
	BMP_PTR pBMP = pSheet->GetBMP();
	RANGE rg(pBMP);
	pWorksheet->GetUsedRange(&rg);
	if (!sheetName || !rg.IsValid())
		return;
	ks_stdptr<IDBSheetOp> spDBSheetOp;
	DbSheet::GetDBSheetOp(pSheet, &spDBSheetOp);
	if (!spDBSheetOp)
		return;
	ROW rowFrom = rg.RowFrom();
	ROW rowTo = rg.RowTo();
	COL colFrom = rg.ColFrom();
	ROW colTo = rg.ColTo();
	acpt->addString("name", sheetName);
	acpt->addBool("selected", activeStId == pSheet->GetStId());
	acpt->addInt32("max_row", rowTo - rowFrom + 1);
	acpt->addInt32("max_col", colTo - colFrom + 1);

	acpt->addKey("tables");
	acpt->beginArray();
	acpt->beginStruct();
	acpt->addKey("data_range");
	acpt->beginStruct();
	acpt->addInt32("row_from", rowFrom + 1);
	acpt->addInt32("row_to", rowTo + 1);
	acpt->addInt32("column_from", colFrom + 1);
	acpt->addInt32("column_to", colTo + 1);
	acpt->endStruct();

	acpt->addKey("title");
	acpt->beginArray();
	int outputColTo = std::min(colTo, colFrom + maxleftCount - 1);
	acpt->beginStruct();
	acpt->addKey("cells");
	acpt->beginArray();
	IDbFieldsManager* pFieldsMgr = spDBSheetOp->GetFieldsManager();
	const IDBIds* pFields = spDBSheetOp->GetAllFields();
	for (int col = colFrom; col <= outputColTo; ++col)
	{
		sa::Leave cellLeave = sa::enterStruct(acpt, nullptr);
		ks_stdptr<IDbField> spField;
		pFieldsMgr->GetField(pFields->IdAt(col), &spField);
		PCWSTR valueStr = __X("");
		if (spField)
			valueStr = spField->GetName();
		acpt->addInt32("row", 0);
		acpt->addInt32("col", col + 1);
		acpt->addInt32("h_merge", 0);
		acpt->addInt32("v_merge", 0);
		acpt->addString("cell_type", __X("ExecTokenSTR"));
		acpt->addString("value", valueStr);
	}
	acpt->endArray();
	acpt->endStruct();
	acpt->endArray();

	acpt->addKey("content_sample");
	acpt->beginArray();
	int roEnd = std::min(std::min(rowFrom + 2, rowTo), pBMP->cntRows - 1);
	for (int row = rowFrom; row <= roEnd; ++row)
	{
		sa::Leave cellLeave = sa::enterStruct(acpt, nullptr);
		acpt->addKey("cells");
		acpt->beginArray();
		for (int col = colFrom; col <= outputColTo; ++col)
			WriteCellValue(ctx, pSheet, row, col, acpt);
		acpt->endArray();
	}
	acpt->endArray();
	acpt->endStruct();
	acpt->endArray();
}

static void WriteTitleContent(KEtRevisionContext* ctx, IKWorksheet* pWorksheet, PCWSTR recognizeResult, UINT activeStId, int maxleftCount, ISerialAcceptor* acpt)
{
	sa::Leave itemLeave = sa::enterStruct(acpt, nullptr);
	if (!pWorksheet)
		return;
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!pSheet)
		return;
	acpt->addString("sheet_type", getSheetTypeStr(pSheet));
	if (pSheet->IsDbSheet())
	{
		WriteDBTitleContent(ctx, pWorksheet, activeStId, maxleftCount, acpt);
		return;
	}
	if (!pSheet->IsGridSheet())
		return;
	PCWSTR sheetName = nullptr;
	pSheet->GetName(&sheetName);
	BMP_PTR pBMP = pSheet->GetBMP();
	RANGE rg(pBMP);
	pWorksheet->GetUsedRange(&rg);
	if (!sheetName || !rg.IsValid())
		return;
	QString result = QString::fromUtf16(recognizeResult);
	QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
	if (!doc.isObject())
		return;
	QJsonObject jsonObject = doc.object();
	if (!jsonObject.contains("sheetAreaList"))
		return;

	acpt->addString("name", sheetName);
	acpt->addBool("selected", activeStId == pSheet->GetStId());
	acpt->addInt32("max_row", rg.RowTo() - rg.RowFrom() + 1);
	acpt->addInt32("max_col", rg.ColTo() - rg.ColFrom() + 1);

	QJsonArray sheetAreaList = jsonObject["sheetAreaList"].toArray();
	acpt->addKey("tables");
	acpt->beginArray();
	auto extendTableContent = [](ES_CUBE& content, const ES_CUBE& cube, ES_CUBE* pTitle = nullptr) -> bool {
		if (pTitle)
		{
			if (content.colFrom != pTitle->colFrom || content.colTo != pTitle->colTo)
				return false;
			if (cube.rowFrom <= pTitle->rowTo)
				return false;
		}
		if (content.colFrom != cube.colFrom || content.colTo != cube.colTo)
			return false;
		content.rowFrom = std::min(content.rowFrom, cube.rowFrom);
		content.rowTo = std::min(content.rowTo, cube.rowTo);
		return true;
	};
	std::vector<std::pair<ES_CUBE, ES_CUBE>> tableRangeVec;
    //构造HiddenRowColTool，当有隐藏行列时，计算出真实的行列
    std::unique_ptr<etai::HiddenRowColTool> spTool = std::make_unique<etai::HiddenRowColTool>(pSheet, rg);

	// ai中台返回的数据结构，根据表格情况，每一个表格下的行标题和内容范围可能是多个，也可能没有，共有4种情况(1. 有表头有内容 2. 仅有内容 3. 仅有表头 4. 无表头无内容),需要特殊处理
	// rowTitle 和 content 不一定是一对一的，所以是按照顺序罗列的，因此可能出现，4中情况混杂在一起导致难以区分
	// 策略: 允许输出仅有表头和仅有内容的情况；数据混杂时：1. 读取content区域时与之前的content（没有使用title，也没有直接重置）做比对，列范围一致做合并操作，否则将之前的信息认为是一个table，重置title及content值 2. 读取title区域时，将已有信息因为是一个独立table并进行重置
	for (const auto& table : sheetAreaList)
	{
		QJsonArray zonesArray = table.toArray();
		bool hasTitle = false, hasContent = false;
		ES_CUBE rowTitle, content;
		ES_CUBE nullCube;
		nullCube.colFrom = -1;
		for (const auto& zone : zonesArray)
		{
			etai::Zone eachZone(zone.toArray());
            //修正下eachZone.m_range的范围。
            etai::IdentifyRange rg(eachZone.m_range.rowFrom, eachZone.m_range.rowTo, eachZone.m_range.colFrom, eachZone.m_range.colTo);
            etai::IdentifyRange realrg = spTool->GetRealRange(rg);
            eachZone.m_range.rowFrom = realrg.iTop;
            eachZone.m_range.rowTo = realrg.iBottom;
            eachZone.m_range.colFrom = realrg.iLeft;
            eachZone.m_range.colTo = realrg.iRight;

			switch (eachZone.m_type)
			{
				case etai::Content:
					if (!hasContent)
					{
						content = eachZone.m_range;
						if (hasTitle && !extendTableContent(content, eachZone.m_range, &rowTitle))
						{
							tableRangeVec.emplace_back(rowTitle, nullCube);
							hasTitle = false;
						}
					}
					else if (!extendTableContent(content, eachZone.m_range, hasTitle ? &rowTitle : nullptr))
					{
						tableRangeVec.emplace_back(hasTitle ? rowTitle : nullCube, content);
						content = eachZone.m_range;
						hasTitle = false;
					}
					hasContent = true;
					break;
				case etai::RowTitle:
					if (hasTitle || hasContent)
						tableRangeVec.emplace_back(hasTitle ? rowTitle : nullCube, hasContent ? content : nullCube);
					rowTitle = eachZone.m_range;
					hasTitle = true;
					hasContent = false;
					break;
				default:
					break;
			}
		}
		if (hasTitle || hasContent)
			tableRangeVec.emplace_back(hasTitle ? rowTitle : nullCube, hasContent ? content : nullCube);
	}
	for (const auto& table : tableRangeVec)
	{
		sa::Leave tableLeave = sa::enterStruct(acpt, nullptr);
		const ES_CUBE& rowTitle = table.first;
		const ES_CUBE& content = table.second;
		bool rowTitleValid = rowTitle.colFrom != -1;
		bool contentValid = content.colFrom != -1;
		acpt->addKey("data_range");
		acpt->beginStruct();
		acpt->addInt32("row_from", rowTitleValid ? rowTitle.rowFrom + 1 : content.rowFrom + 1);
		acpt->addInt32("row_to", contentValid ? content.rowTo + 1 : rowTitle.rowTo + 1);
		acpt->addInt32("column_from", rowTitleValid ? rowTitle.colFrom + 1 : content.colFrom + 1);
		acpt->addInt32("column_to", rowTitleValid ? rowTitle.colTo + 1 : content.colTo + 1);
		acpt->endStruct();

		acpt->addKey("title");
		acpt->beginArray();
		if (rowTitleValid)
		{
			int outputColTo = std::min(rowTitle.colTo, rowTitle.colFrom + maxleftCount - 1);
			for (int row = rowTitle.rowFrom; row <= rowTitle.rowTo; ++row)
			{
				sa::Leave cellLeave = sa::enterStruct(acpt, nullptr);
				acpt->addKey("cells");
				acpt->beginArray();
				for (int col = rowTitle.colFrom; col <= outputColTo; ++col)
					WriteCellValue(ctx, pSheet, row, col, acpt);
				acpt->endArray();
			}
		}
		acpt->endArray();

		acpt->addKey("content_sample");
		acpt->beginArray();
		constexpr int maxSampleNum = 3;
		if (contentValid)
		{
			int outputColTo = std::min(content.colTo, content.colFrom + maxleftCount - 1);
			int outputRowTo = std::min(content.rowTo, content.rowFrom + maxSampleNum - 1);
			for (int row = content.rowFrom; row <= outputRowTo; ++row)
			{
				sa::Leave cellLeave = sa::enterStruct(acpt, nullptr);
				acpt->addKey("cells");
				acpt->beginArray();
				for (int col = content.colFrom; col <= outputColTo; ++col)
					WriteCellValue(ctx, pSheet, row, col, acpt);
				acpt->endArray();
			}
		}
		acpt->endArray();
	}
	acpt->endArray();
}

HRESULT EtHttpGetTitleContentByAIQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_ARRAY(param, "sheetIdVec")
	VAR_OBJ_EXPECT_ARRAY(param, "resultVec")
	VAR_OBJ_EXPECT_NUMERIC(param, "active_sheet_id")
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetLimitItems")
	VarObj sheetIdVec = param.get("sheetIdVec");
	VarObj resultVec = param.get("resultVec");
	int sheetLen = sheetIdVec.arrayLength();
	if (sheetLen != resultVec.arrayLength())
		return E_INVALIDARG;
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
	IBook* pBook = pWorkbook->GetBook();
	std::vector<IDX> sheetIdxVec;
	sheetIdxVec.reserve(sheetLen);
	for (int i = 0; i < sheetLen; ++i)
	{
		IDX sheetIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(sheetIdVec.item_uint32(i), &sheetIdx);
		if (sheetIdx == INVALIDIDX)
			return E_INVALIDARG;
        HRESULT hr = CheckSheetPermission(sheetIdVec.item_uint32(i), ctx);
        if (FAILED(hr))
            return hr;
		sheetIdxVec.emplace_back(sheetIdx);
	}
	UINT activeStId = param.field_uint32("active_sheet_id");
	int maxleftCount = param.field_int32("sheetLimitItems");

	binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	acpt.addKey("sheets");
	acpt.beginArray();
	for (int i = 0; i < sheetLen; ++i)
		WriteTitleContent(ctx, pWorksheets->GetSheetItem(sheetIdxVec[i]), resultVec.item_str(i), activeStId, maxleftCount, &acpt);
	acpt.endArray();
	return S_OK;
}

EtHttpIdentifyFieldTypeQueryClass::EtHttpIdentifyFieldTypeQueryClass(KEtWorkbook* wb)
	: EtHttpQueryClassBase(wb, __X("http.et.identifyFieldType"))
{
}

HRESULT EtHttpIdentifyFieldTypeQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	VAR_OBJ_EXPECT_NUMERIC(param, "rowFrom")
	VAR_OBJ_EXPECT_NUMERIC(param, "rowTo")
	VAR_OBJ_EXPECT_NUMERIC(param, "colFrom")
	VAR_OBJ_EXPECT_NUMERIC(param, "colTo")
	UINT stId = param.field_uint32("sheetId");
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IBook* pBook = pWorkbook->GetBook();
	IDX sheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(stId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_KSHEET_SHEET_NOT_FOUND;
	ROW rowFrom = param.field_int32("rowFrom");
	ROW rowTo = param.field_int32("rowTo");
	COL colFrom = param.field_int32("colFrom");
	COL colTo = param.field_int32("colTo");
	IKWorksheet* pWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
    if (pWorksheet->GetSheet()->IsGridSheet() == false)
        return E_KSHEET_NOT_GRIDSHEET;
	RANGE rg(pBook->GetBMP());
	rg.SetSheetFromTo(sheetIdx);
	rg.SetRowFromTo(rowFrom, rowTo);
	rg.SetColFromTo(colFrom, colTo);
	if (!rg.IsValid())
		return E_INVALIDARG;
	if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
		return E_NOT_HAVE_PERMISSION;
    HRESULT hr = S_OK;
    int primaryCol = colFrom;
    Et2DbFieldCopyHelper stFieldCopyHelper(m_wwb->GetCoreWorkbook(), static_cast<etoldapi::_Worksheet*>(pWorksheet), nullptr, ctx, rg);
    stFieldCopyHelper.SetIterRowsByRange(rg.RowFrom(), rg.RowTo());
	binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	{
		sa::Leave typeVecLeave = sa::enterArray(&acpt, "typeResultVec");
		for (int col = colFrom; col <= colTo; ++col)
		{
			ET_DbSheet_FieldType type = ET_DbSheet_FieldType_Invalid;
            stFieldCopyHelper.BeginDeduceFieldType();
            for (;stFieldCopyHelper.Valid();stFieldCopyHelper.Next())
            {
                hr = stFieldCopyHelper.DeduceFieldType(col);
                if (hr == S_OK)
                    break;
            }
            stFieldCopyHelper.EndDeduceFieldType();
            PCWSTR numFmt = nullptr;
            stFieldCopyHelper.GetTypeInfo(type, numFmt);
            if (col == primaryCol)
                stFieldCopyHelper.CorrectionForPrimaryField(type, numFmt);
            PCWSTR typeStr = nullptr;
			VS(_appcore_GainEncodeDecoder()->EncodeFieldType(type, &typeStr));
			acpt.addString(nullptr, typeStr);
		}
	}
	return S_OK;
}

void EtHttpDocumentInfoQueryClass::WriteCommentChains(IWoComment* pWoComment, bool bIsResolved, ISerialAcceptor* acpt, int& totalCnt)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	size_t chainsCount = pWoComment->GetChainCount(bIsResolved);
	for (size_t i = 0; i < chainsCount; ++i)
	{
		IWoCommentChain* pCommentChain = pWoComment->GetChainByIndex(bIsResolved, i);
		if (!pCommentChain) continue;
		size_t itemCount = pCommentChain->Count();
		for (size_t j = 0; j < itemCount; ++j)
		{
			IWoCommentItem* pItem = pCommentChain->GetItem(j);
			if (!pItem) continue;
			acpt->beginStruct();
			{
				PCWSTR text = pItem->GetText();
				acpt->addInt32("wordCount", xstrlen(text));

				bool bIsNormal = pItem->IsNormal();
				if (bIsNormal)
					acpt->addBool("isNormal", bIsNormal);
				else
				{
					double dateTime = pItem->GetDateTime();
					BOOL b1904 = pBook->Is1904DateSystem();
					DATE date = _XDateFromDouble(dateTime);
					TIMEINFO timeInfo;
					_XTmFromDate2(date, b1904, FALSE, 0, timeInfo, nullptr, nullptr);
					QDate qDate(timeInfo.tm_year, timeInfo.tm_mon, timeInfo.tm_mday);
					QString strDate = qDate.toString(Qt::ISODate);

					acpt->addString("author", pItem->GetUserName());
					acpt->addString("createTime", krt::utf16(strDate));
				}
			}
			acpt->endStruct();
			++totalCnt;
		}
	}
}

//////////////////////////////////////////////////////////////////////////
EtHttpDocumentInfoQueryClass::EtHttpDocumentInfoQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getDocumentInfo"))
{
}

HRESULT EtHttpDocumentInfoQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

    _Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	ks_stdptr<IBook> pBook = pWorkbook->GetBook();

	int totalCommentCnt = 0;
	int validShtCnt = 0;

	// write sheets info
	IDX shtCnt = pWorkbook->GetWorksheets()->GetSheetCount();
    acpt.addKey("sheets");
	acpt.beginArray();
	for (IDX shtIdx = 0; shtIdx < shtCnt; ++shtIdx)
	{
		if (wo::util::isCellImgListSheet(pBook, shtIdx))
			continue;
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(shtIdx, &spSheet);
		if (!spSheet) continue;

        HRESULT hr = CheckSheetPermission(spSheet->GetStId(), ctx);
        if (FAILED(hr))
            continue;
        
		PCWSTR sheetName = nullptr;
		spSheet->GetName(&sheetName);

		RANGE rg(spSheet->GetBMP());
		ks_stdptr<IKWorksheet> spWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(shtIdx);
		spWorksheet->GetUsedRange(&rg);

		acpt.beginStruct();
			acpt.addString("sheetName", sheetName);
			acpt.addInt32("sheetIdx", shtIdx);
			acpt.addInt32("maxRow", rg.RowTo());
			acpt.addInt32("maxCol", rg.ColTo());

			WriteCommentInfo(spSheet, &acpt, totalCommentCnt);
		acpt.endStruct();
		++validShtCnt;
	}	
	acpt.endArray();

	PCWSTR fileName = nullptr;
	pBook->GetFileName(&fileName);

	// write document info
	acpt.addString("fileName", fileName);
	acpt.addString("creatorId", pBook->GetWoStake()->GetCreatorUserID());
	acpt.addInt32("sheetsCount", validShtCnt);
	acpt.addInt32("commentsCount", totalCommentCnt);

    return S_OK;
}

void EtHttpDocumentInfoQueryClass::WriteCommentInfo(ISheet* pSheet, ISerialAcceptor* acpt, int& totalCnt)
{
    acpt->addKey("comments");
	acpt->beginArray();

	ks_stdptr<IKDrawingCanvas> spCommentCanvas;
	ks_stdptr<IUnknown> spUnk;
	if (SUCCEEDED(pSheet->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk)))
		spUnk->QueryInterface(IID_IKDrawingCanvas, (void**)&spCommentCanvas);

	if (!spCommentCanvas)
		return;

	ks_stdptr<ICellComments> spCMTs = spCommentCanvas;

	int count = 0;
	spCMTs->GetCount(&count);
	for (int i = 0; i < count; ++i)
	{
		ks_stdptr<ICellComment> spCellComment;
		spCMTs->GetItem(i, &spCellComment);
		if (!spCellComment) continue;

		ks_stdptr<IWoComment> spWoComment;
		spCellComment->GetWoComment(&spWoComment);
		if (!spWoComment || spWoComment->Empty()) continue;

		CELL cellPos = {0};
		spCellComment->GetBindCell(&cellPos);

		acpt->beginStruct();
		acpt->addKey("cellPos");
		acpt->beginStruct();
			acpt->addInt32("row", cellPos.row);
			acpt->addInt32("col", cellPos.col);
		acpt->endStruct();

		acpt->addKey("unresolved");
		acpt->beginArray();
			WriteCommentChains(spWoComment, false, acpt, totalCnt);
		acpt->endArray();

		acpt->addKey("resolved");
		acpt->beginArray();
			WriteCommentChains(spWoComment, true, acpt, totalCnt);
        acpt->endArray();
		acpt->endStruct();
	}
	acpt->endArray();
}


EtHttpGetQRColQueryClass::EtHttpGetQRColQueryClass(wo::KEtWorkbook* wb)
        : EtHttpQueryClassBase(wb, __X("http.et.getQRCol"))
{

}

HRESULT EtHttpGetQRColQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    Database::GenQRLabelCol qrCol;
    Database::FieldContext fieldContext(m_wwb, ctx);
    if (param.has("pageId"))
    {
        VAR_OBJ_EXPECT_STRING(param, "pageId");
        PCWSTR pageId = param.field_str("pageId");
        HRESULT hr = Database::Utils::GetQRColByPageId(&fieldContext, sheetIdx, pageId, qrCol);
        if (FAILED(hr) || qrCol.col == INVALID_COL)
            return E_QRCOL_NOT_FOUND;
    }
    else
    {
        COL col = 0;
        if (param.has("col"))
        {
            VAR_OBJ_EXPECT_NUMERIC(param, "col")
            col = param.field_int32("col");
        }
        if (col < 0 || col > pBook->GetBMP()->cntCols - 1)
            return E_INVALID_REQUEST;

        HRESULT hr = Database::Utils::GetQRCol(&fieldContext, sheetIdx, col, qrCol);
        if (FAILED(hr) || qrCol.col == INVALID_COL)
            return E_QRCOL_NOT_FOUND;
    }

    bool getFirstEmptyRow = false;
    ROW firstEmptyRow = INVALID_ROW;
    if (param.has("getFirstEmptyRow"))
        getFirstEmptyRow = param.field_bool("getFirstEmptyRow");

    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addInt32("col", qrCol.col);
    ks_bstr keyColName;
    ks_bstr colName;
    if (qrCol.keyCol != INVALID_COL && qrCol.headerRow != INVALID_ROW)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(sheetIdx, &spSheet);
        ctx->getStringTools()->GetCellText(spSheet, qrCol.headerRow, qrCol.keyCol, &keyColName, nullptr, 0, nullptr);
        ctx->getStringTools()->GetCellText(spSheet, qrCol.headerRow, qrCol.col, &colName, nullptr, 0, nullptr);

        if (getFirstEmptyRow)
            firstEmptyRow = GetFirstEmptyRow(ctx, spSheet, qrCol.keyCol, qrCol.headerRow);
    }

    if (getFirstEmptyRow)
        acpt.addInt32("firstEmptyRow", firstEmptyRow);
    acpt.addString("keyColName", keyColName.empty() ? __X("") : keyColName.c_str());
    acpt.addKey("colConfig");
    acpt.beginStruct();
    acpt.addString("colName", colName.empty() ? __X("") : colName.c_str());
    acpt.addString("pageId", qrCol.pageId.c_str());
    acpt.addInt32("headerRow", qrCol.headerRow);
    acpt.addInt32("keyCol", qrCol.keyCol);

    acpt.addKey("refCols");
    acpt.beginArray();
    for (COL col: qrCol.refCols)
        acpt.addInt32(nullptr, col);
    acpt.endArray();

    acpt.endStruct();
    return S_OK;
}

ROW EtHttpGetQRColQueryClass::GetFirstEmptyRow(KEtRevisionContext* ctx, ISheet* pSheet, COL keyCol, ROW headerRow)
{
    IDX sheetIdx = INVALIDIDX;
    pSheet->GetIndex(&sheetIdx);

    RANGE dataRange(m_wwb->GetBMP());
    for (ROW row = headerRow + 1; row <= pSheet->GetBMP()->cntRows - 1; ++row)
    {
        dataRange.SetRows(sheetIdx, sheetIdx, row, row);
        if (!util::IsRangeEmpty(ctx, pSheet, dataRange))
            continue;

        RANGE cellRange(m_wwb->GetBMP());
        cellRange.SetCell(sheetIdx, row, keyCol);
        if (!util::HasMergedCell(pSheet, cellRange))
            return row;
    }
    return INVALID_ROW;
}

EtHttpFindFirstEmptyColQueryClass::EtHttpFindFirstEmptyColQueryClass(wo::KEtWorkbook* wb)
        : EtHttpQueryClassBase(wb, __X("http.et.findFirstEmptyCol"))
{

}

HRESULT EtHttpFindFirstEmptyColQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    int maxCols = 100;
    if (param.has("maxCols"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "maxCols")
        maxCols = std::min(param.field_int32("maxCols"), pBook->GetBMP()->cntCols);
        if (maxCols <= 0)
            return E_INVALID_REQUEST;
    }

    bool afterData = false;
    if (param.has("afterData"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "afterData")
        afterData = param.field_bool("afterData");
    }

    bool skipMergedCell = false;
    if (param.has("skipMergedCell"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "skipMergedCell")
        skipMergedCell = param.field_bool("skipMergedCell");
    }

    bool skipNonEditableCol = false;
    if (param.has("skipNonEditableCol"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "skipNonEditableCol")
        skipNonEditableCol = param.field_bool("skipNonEditableCol");
    }

    COL firstNonEmptyCol = INVALID_COL;
    COL emptyCol = INVALID_COL;
    IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    ISheet* pSheet = pWorkSheet->GetSheet();
    RANGE range(pBook->GetBMP());
    range.SetCols(sheetIdx, sheetIdx, 0 , 0);
    if (afterData)
    {
        // 优先从第一个非空列后开始查找
        RANGE usedRange(pBook->GetBMP());
        pWorkSheet->GetUsedRange(&usedRange);
        for (COL col = usedRange.ColFrom(), colTo = usedRange.ColTo(); col <= colTo; ++col)
        {
            range.SetColFromTo(col, col);
            if (!util::IsRangeEmpty(ctx, pSheet, range))
            {
                firstNonEmptyCol = col;
                break;
            }
        }
    }
    auto isSuitableEmptyCol = [&](COL col) {
        range.SetColFromTo(col, col);
        if (skipMergedCell && util::HasMergedCell(pSheet, range))
            return false;

        auto accessPerm = PTAAP_None;
        if (skipNonEditableCol && !ctx->getProtectionCtx()->isAllowEdit(range, &accessPerm))
            return false;

        return util::IsRangeEmpty(ctx, pSheet, range);
    };
    COL beginCol = firstNonEmptyCol == INVALID_COL ? 0 : firstNonEmptyCol + 1;
    for (COL col = beginCol; col < maxCols; ++col)
    {
        if (isSuitableEmptyCol(col))
        {
            emptyCol = col;
            break;
        }
    }
    if (emptyCol == INVALID_COL && beginCol > 0)
    {
        for (COL col = 0; col < beginCol; ++col)
        {
            if (isSuitableEmptyCol(col))
            {
                emptyCol = col;
                break;
            }
        }
    }
    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addInt32("col", emptyCol);
    return S_OK;
}

EtHttpFindQRKeyColQueryClass::EtHttpFindQRKeyColQueryClass(wo::KEtWorkbook* wb)
        : EtHttpQueryClassBase(wb, __X("http.et.findQRKeyCol"))
{

}

bool EtHttpFindQRKeyColQueryClass::ShouldExcludeCol(KEtRevisionContext* pCtx, ISheet* pSheet, const RANGE& colRange)
{
    if (pCtx->getProtectionCtx()->isRangeHasHiddenProperty(colRange))
        return true;

    if (util::HasMergedCell(pSheet, colRange))
        return true;

    Database::FieldContext fieldContext(m_wwb, pCtx);
    Database::FieldType fieldType = Database::Utils::GetColType(&fieldContext, colRange.SheetFrom(), colRange.ColFrom());
    return fieldType == Database::dftCellPicture || fieldType == Database::dftGenQRLabel || fieldType == Database::dftAI;
}

COL EtHttpFindQRKeyColQueryClass::FindKeyCol(ISheet* pSheet, const RANGE& range, KEtRevisionContext* pCtx,
                                             UINT uniquePercent, const std::vector<ks_wstring>* pMatchingHeaderNames)
{
    class CheckUniqueAcpt : public ICellValueAcpt
    {
    public:
        CheckUniqueAcpt(IETStringTools* pStringTool, ISheet* pSheet, ICellImages* pCellImages, UINT targetUniquePercent) :
            m_pStringTool(pStringTool), m_pSheet(pSheet), m_pCellImages(pCellImages), m_targetUniquePercent(targetUniquePercent)
        {
        }

        bool IsMatch() const
        {
            if (m_totalCount == 0 || m_cellImgCnt > 2)
                return false;

            UINT uniquePercent = m_values.size() * 100 / m_totalCount;
            return uniquePercent >= m_targetUniquePercent;
        };

        STDPROC_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
        {
            ks_bstr celltext;
            m_pStringTool->GetCellText(m_pSheet, row, col, &celltext, nullptr, 0, nullptr);
            if (celltext.empty())
                return 0;

            // 大于2个单元格图片不推荐为关键列
            if (IsCellImg(pToken))
                m_cellImgCnt++;
            if (m_cellImgCnt > 2)
                return 1;

            QString value = krt::fromUtf16(celltext);
            if (m_targetUniquePercent >= 100 && m_values.contains(value))
            {
                m_totalCount = 0;
                return 1;
            }
            m_values.insert(value);
            m_totalCount++;
            return 0;
        };
    private:
        bool IsCellImg(const_token_ptr pToken)
        {
            if(!alg::const_vstr_token_assist::is_type(pToken) || !m_pCellImages)
                return false;
                    
            CellImg_Param param;
            PCWSTR cellValue = alg::const_vstr_token_assist(pToken).get_value();
            if (!cellValue)
                return false;
            if (m_pCellImages->GetCellImgParamFromCellValue(cellValue, &param) == FALSE)
                return false;
            return true;
        };
    private:
        UINT m_totalCount = 0;
        IETStringTools* m_pStringTool;
        ISheet* m_pSheet;
        QSet<QString> m_values;
        UINT m_targetUniquePercent;
        ICellImages* m_pCellImages;
        UINT m_cellImgCnt = 0;
    };
    et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);
    COL colFrom = range.ColFrom();
    COL colTo = range.ColTo();
    ROW headerRow = range.RowFrom();
    RANGE dataRange(range);
    dataRange.SetRowFrom(headerRow + 1);
    ICellImages* pCellImages = m_wwb->GetCoreWorkbook()->GetCellImages();
    for (COL col = colFrom; col <= colTo; ++col)
    {
        dataRange.SetColFromTo(col, col);
        if (ShouldExcludeCol(pCtx, pSheet, dataRange))
            continue;

        IETStringTools* pStringTools = pCtx->getStringTools();
        if (pMatchingHeaderNames)
        {
            ks_bstr celltext;
            pStringTools->GetCellText(pSheet, headerRow, col, &celltext, nullptr, 0, nullptr);
            if (celltext.empty())
                continue;

            ks_wstring headerName = celltext.c_str();
            auto isHeaderNameMatch = [&headerName](const ks_wstring& str) {
                return headerName.find(str) != ks_wstring::npos;
            };
            if (!std::any_of(pMatchingHeaderNames->cbegin(), pMatchingHeaderNames->cend(), isHeaderNameMatch))
                continue;
        }
        CheckUniqueAcpt checkUniqueAcpt(pStringTools, pSheet, pCellImages, uniquePercent);
        spSheetEnum->EnumCellValue(dataRange, &checkUniqueAcpt);
        if (checkUniqueAcpt.IsMatch())
            return col;
    }
    return INVALID_COL;
}

HRESULT EtHttpFindQRKeyColQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    VAR_OBJ_EXPECT_NUMERIC(param, "headerRow")
    ROW headerRow = param.field_int32("headerRow");
    if (headerRow < 0 || headerRow >= pBook->GetBMP()->cntRows - 1)
        return E_INVALID_REQUEST;

    VAR_OBJ_EXPECT_ARRAY(param, "matchingHeaderNames")
    binary_wo::VarObj matchingHeaderNamesObj = param.get("matchingHeaderNames");
    int nameCount = matchingHeaderNamesObj.arrayLength();
    std::vector<ks_wstring> matchingHeaderNames;
    matchingHeaderNames.reserve(nameCount);
    for (int i = 0; i < nameCount; ++i)
    {
        binary_wo::VarObj nameObj = matchingHeaderNamesObj.at(i);
        if (nameObj.type() != typeString)
            return E_INVALID_REQUEST;
        matchingHeaderNames.emplace_back(nameObj.value_str());
    }

    int maxRows = 10;
    if (param.has("maxRows"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "maxRows")
        maxRows = param.field_int32("maxRows");
        if (maxRows <= 0)
            return E_INVALID_REQUEST;
    }

    int maxCols = 100;
    if (param.has("maxCols"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "maxCols")
        maxCols = std::min(param.field_int32("maxCols"), pBook->GetBMP()->cntCols);
        if (maxCols <= 0)
            return E_INVALID_REQUEST;
    }

    UINT uniquePercent = 100;
    if (param.has("uniquePercent"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "uniquePercent")
        uniquePercent = param.field_uint32("uniquePercent");
    }

    RANGE usedRange(pBook->GetBMP());
    IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    pWorkSheet->GetUsedRange(&usedRange);
    COL keyCol = INVALID_COL;
    if (headerRow < usedRange.RowTo())
    {
        ROW rowTo = std::min(headerRow + maxRows, usedRange.RowTo());
        COL colTo = std::min(maxCols - 1, usedRange.ColTo());
        RANGE range(pBook->GetBMP());
        range.SetSheetFromTo(sheetIdx);
        range.SetRowFromTo(headerRow, rowTo);
        range.SetColFromTo(0, colTo);
        ISheet* pSheet = pWorkSheet->GetSheet();
        keyCol = FindKeyCol(pSheet, range, ctx, uniquePercent, &matchingHeaderNames);
        if (keyCol == INVALID_COL)
            keyCol = FindKeyCol(pSheet, range, ctx, uniquePercent, nullptr);
    }
    ks_bstr keyColName;
    IETStringTools* pStringTools = ctx->getStringTools();
    if (pStringTools && keyCol != INVALID_COL)
    {
        ISheet* pSheet = pWorkSheet->GetSheet();
        pStringTools->GetCellText(pSheet, headerRow, keyCol, &keyColName, nullptr, 0, nullptr);
    }

    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addInt32("col", keyCol);
    acpt.addString("colName", keyColName.empty() ? __X("") : keyColName.c_str());
    return S_OK;
}


EtHttpColsInfoQueryClass::EtHttpColsInfoQueryClass(wo::KEtWorkbook* wb)
        : EtHttpQueryClassBase(wb, __X("http.et.getColsInfo"))
{

}

PCWSTR EtHttpColsInfoQueryClass::GetValueTypeStr(ICellImages* pCellImages, const_token_ptr pToken)
{
    DWORD majorType = GetExecTokenMajorType(pToken);
    switch (majorType)
    {
        case alg::ETP_HANDLE:
        {
            if (alg::const_handle_token_assist(pToken).get_handleType() == alg::ET_HANDLE_QRLABEL)
                return __X("qrLabel");
        }
        case alg::ETP_VSTR:
        {
            PCWSTR str = alg::const_vstr_token_assist(pToken).get_value();
            if (pCellImages && pCellImages->IsValidCellImgStr(str))
                return __X("cellPic");
        }
        default:
            break;
    }
    return __X("other");
}

HRESULT EtHttpColsInfoQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    ROW headerRow = 0;
    if (param.has("headerRow"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "headerRow")
        headerRow = param.field_int32("headerRow");
        if (headerRow < 0 || headerRow >= pBook->GetBMP()->cntRows - 1)
            return E_INVALID_REQUEST;
    }

    int maxCols = 100;
    if (param.has("maxCols"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "maxCols")
        maxCols = std::min(param.field_int32("maxCols"), pBook->GetBMP()->cntCols);
        if (maxCols <= 0)
            return E_INVALID_REQUEST;
    }

    RANGE usedRange(pBook->GetBMP());
    IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    ISheet* pSheet = pWorkSheet->GetSheet();
    pWorkSheet->GetUsedRange(&usedRange);
    COL colTo = std::min(maxCols - 1, usedRange.ColTo());
    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    sa::Leave leaveArray(sa::enterArray(&acpt, "colsInfo"));
    RANGE colRange(pBook->GetBMP());
    colRange.SetCols(sheetIdx, sheetIdx, 0, 0);
    ICellImages* pCellImages = m_wwb->GetCoreWorkbook()->GetCellImages();
    Database::FieldContext fieldContext(m_wwb, ctx);
    // 当表头位于合并单元格内，且不在合并单元格左上角时，由于没有内容，有可能枚举不到，所以这里使用for循环来遍历
    for (COL col = 0; col <= colTo; ++col)
    {
        colRange.SetColFromTo(col, col);
        bool isEmptyCol = util::IsRangeEmpty(ctx, pSheet, colRange);
        bool hasHiddenPropertyCell = ctx->getProtectionCtx()->isRangeHasHiddenProperty(colRange);
        CELL cell = {headerRow, col};
        RANGE mergedRange(pBook->GetBMP());
        app_helper::GetMergeCell(pSheet, cell, mergedRange);
        const_token_ptr pToken = nullptr;
        pBook->LeakOperator()->GetCellValue(sheetIdx, mergedRange.RowFrom(), mergedRange.ColFrom(), &pToken);
        PCWSTR valueType = GetValueTypeStr(pCellImages, pToken);
        ks_bstr celltext;
        ctx->getStringTools()->GetCellText(pSheet, mergedRange.RowFrom(), mergedRange.ColFrom(), &celltext, nullptr, 0, nullptr);
        Database::FieldType fieldType = Database::Utils::GetColType(&fieldContext, colRange.SheetFrom(), colRange.ColFrom());
        ks_wstring fieldTypeStr;
        if (fieldType != Database::dftInvalid)
            fieldTypeStr = Database::Utils::FieldTypeToStr(fieldType);
        sa::Leave leaveStruct(sa::enterStruct(&acpt, nullptr));
        acpt.addInt32("col", col);
        acpt.addBool("isEmptyCol", isEmptyCol);
        acpt.addBool("hasHiddenPropertyCell", hasHiddenPropertyCell);
        acpt.addString("colType", fieldTypeStr.c_str());
        sa::Leave leaveHeaderValueStruct(sa::enterStruct(&acpt, "headerValue"));
        acpt.addString("valueType", valueType);
        acpt.addString("text", celltext.empty() ? __X("") : celltext.c_str());
    }
    return S_OK;
}

// -----------------------------------------------------------------------------------------------------------

EtHttpCopyRangeDataClass::EtHttpCopyRangeDataClass(wo::KEtWorkbook* wb)
        : EtHttpQueryClassBase(wb, __X("http.et.copyRangeData"))
{

}

HRESULT EtHttpCopyRangeDataClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    IETPersist* const pPersist = m_wwb->GetCoreApp()->GetAppPersist()->GetPersist();
    if (pPersist == nullptr)
    {
        WOLOG_INFO << "[EtHttpCopyRangeDataClass::Exec] Get document persist failed!";
        return E_FAIL;
    }

    WebStr prefix {}, postfix {};
    if (param.has("cloudPathPrefix"))
    {
        VAR_OBJ_EXPECT_STRING(param, "cloudPathPrefix")
        WebStr prefix = param.field_str("cloudPathPrefix");
        WebStr postfix = __X("");
        if (param.has("cloudPathPostfix"))
        {
            VAR_OBJ_EXPECT_STRING(param, "cloudPathPostfix")
            postfix = param.field_str("cloudPathPostfix");
        }
        ctx->InitCopyShapeCtx(prefix, postfix);
    }

    // 纳入新格式之后, 更改此处数字, 并在 Format 中增加新格式的名称
    constexpr std::size_t maxFormatNumber = 2;      // 暂时最多支持两个格式
    std::bitset<maxFormatNumber> formats;
    VAR_OBJ_EXPECT_ARRAY_FOR_TYPE(param, "format", typeString)
    const binary_wo::VarObj format = param.get("format");
    const int32 formatCount = static_cast<int>(format.arrayLength_s());
    if (formatCount == 0)
        return E_KSHEET_COPY_FORMAT_ERROR;
    for (int i = 0; i < formatCount; ++i)
    {
        if (xstrcmp(format.at(i).value_str(), __X("text")) == 0)
            formats[text] = true;
        else
            formats[html] = true;
    }

    const auto generateInformation {[](BOOL html, std::vector<CopyTypeInfo> &information,
            std::vector<STGMEDIUM> &mediums) -> void {
        information.emplace_back();
        CopyTypeInfo &copyTypeInformation = information.back();
        copyTypeInformation.format = html == TRUE ? kso_cb_html_format : kso_cb_text_format;
        copyTypeInformation.applyFormat = html;
        mediums.emplace_back();
        copyTypeInformation.medium = &mediums.back();
    }};

    qreal DPI = ctx->getUser()->getDpi();
    if (param.has("DPI"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "DPI");
        DPI = static_cast<qreal>(param.field_double("DPI"));
    }

    _Workbook* const pWorkbook = m_wwb->GetCoreWorkbook();
    IBook* const pBook = pWorkbook->GetBook();
    ICellImages* const pCellImages = pWorkbook->GetCellImages();

    VAR_OBJ_EXPECT_ARRAY_FOR_TYPE(param, "sheetInfo", binary_wo::typeStruct);
    const binary_wo::VarObj sheetsInfo = param.get_s("sheetInfo");
    const int32 sheetsSize = sheetsInfo.arrayLength_s();
    std::map<IDX, range_helper::ranges> sheets;
    std::set<QString> file_SHA1_list;
    binary_wo::BinWriter* const pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("data");
    acpt.beginArray();      // BEGIN copy core data

    for (int32 i = 0; i < sheetsSize; ++i)
    {
        // 预先检查 sheetId 合法性
        const binary_wo::VarObj sheetInfo = sheetsInfo.at_s(i);
        VAR_OBJ_EXPECT_NUMERIC(sheetInfo, "sheetId");
        const int sheetStId = sheetInfo.field_int32("sheetId");
        IDX sheetIndex = INVALIDIDX;
        pBook->STSheetToRTSheet(sheetStId, &sheetIndex);
        if (sheetIndex == INVALIDIDX)
            return E_KSHEET_SHEET_NOT_FOUND;
        if (FAILED(CheckSheetPermission(sheetIndex, ctx)))
            return E_NOT_HAVE_PERMISSION;

        // 获取 range
        ks_stdptr<IRangeInfo> rangeHost;
        RANGE range {pBook->GetBMP()};
        if (sheetInfo.has("range"))
        {
            binary_wo::VarObj readRangeParam = sheetInfo.get("range");
            VAR_OBJ_EXPECT_NUMERIC(readRangeParam, "colFrom")
            VAR_OBJ_EXPECT_NUMERIC(readRangeParam, "colTo")
            VAR_OBJ_EXPECT_NUMERIC(readRangeParam, "rowFrom")
            VAR_OBJ_EXPECT_NUMERIC(readRangeParam, "rowTo")
            readRangeParam.add_field_int32("sheetIdx", sheetIndex);
            range = ReadRange(readRangeParam);
        }
        else
        {
            ks_stdptr<IKWorksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIndex);
            spWorksheet->GetUsedRange(&range);
        }
        if (not range.IsValid())
            return E_KSHEET_RANGE_ERROR;
        if (ctx->getProtectionCtx()->isRangeHasHidden(range))
            return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
        rangeHost = m_wwb->CreateRangeObj(range);
        ks_stdptr<IAppCoreRange> spCoreRange;
        rangeHost->GetAppCoreRange(&spCoreRange);
        if (spCoreRange == nullptr)
            return E_KSHEET_RANGE_ERROR;

        // 过滤筛选区域
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(sheetIndex, &spSheet);
        if (not spSheet->IsGridSheet())
            return E_KSHEET_NOT_GRIDSHEET;
        IKAutoFilters* pFilters = spSheet->GetAutoFilters();
        bool hasUserFilterOn {};
        if (pFilters)
        {
            const PCWSTR filterId = ctx->getFilterContext()->GetID();
            IKAutoFilter* const pFilter = pFilters->GetFilter(filterId);
            if (pFilter and pFilter->HasFilterOn())
                hasUserFilterOn = true;
        }
        range_helper::ranges ranges;       // 真正需要序列化的区域数据
        if (hasUserFilterOn or spCoreRange->IsRangeInFilterMode())
            spCoreRange->GetFilteredIRanges(FALSE, &ranges);
        else
            rangeHost->GetIRanges(&ranges, TRUE);
        sheets.emplace(sheetIndex, std::move(ranges));

        // 根据 prefix 和 postfix 对 image/src 中的链接进行处理
        if (prefix)
        {
            ks_stdptr<IKRanges> spRanges;
            rangeHost->GetIRanges(&spRanges);
            if (!spRanges)
                return E_FAIL;

            ks_stdptr<IKShapeRange> pShapeRange;
            if (S_OK == GetRangeCoverShapes(spSheet,
                    pWorkbook->GetWorksheets()->GetSheetItem(sheetIndex)->GetDrawingCanvas(),
                    spRanges.get(), &pShapeRange))
                util::prepareCoverShapes(sheetIndex, pShapeRange, ctx, &acpt);
        }

        std::vector<CopyTypeInfo> information;
        std::vector<STGMEDIUM> mediums;
        information.reserve(maxFormatNumber);
        mediums.reserve(maxFormatNumber);

        COPYINFO copyInformation {};
        copyInformation.grbits.fCopy = 1;
        copyInformation.grbits.fCache  = 1;
        copyInformation.grbits.fWithObjs = 1;
        copyInformation.grbits.fNoEmptyShape = 1;
        copyInformation.grbits.fBatchCopy = 1;
        copyInformation.grbits.fWithTableStyle = 1;

        // 新增格式需要增加这里的 case, 不太优雅, 但是也只能这样
        if (formats[html])
        {
            generateInformation(TRUE, information, mediums);
            if (copyInformation.grbits.fApplyCondFmt != 1 and _kso_GetWoEtSettings()->IsEnableCFApplyWhenCopy())
                copyInformation.grbits.fApplyCondFmt = 1;
        }
        if (formats[text])
            generateInformation(FALSE, information, mediums);

        // copy
        std::vector<CopyTypeInfo*> informationPointers;
        informationPointers.reserve(maxFormatNumber);
        std::for_each(information.begin(), information.end(), [&informationPointers](CopyTypeInfo &information) {
                informationPointers.emplace_back(&information);
        });
        if (pPersist->WoBatchCopy(pBook, sheetIndex, ranges, &copyInformation,
                informationPointers.data(), formatCount) not_eq S_OK)
            return E_KSHEET_COPY_FAILED;

        // 回传数据
        acpt.beginArray();
            for (const CopyTypeInfo* const copyInformation : informationPointers)
            {
                // 新增格式需要增加这里的 case, 不太优雅, 但是也只能这样
                Format format {};
                if (std::strcmp(copyInformation->format, kso_cb_text_format) == 0)
                {
                    format = Format::text;
                }
                else       // HTML case
                {
                    constexpr int limitedSize = 50 * 1024 * 1024;       // 50 MB, 此处类型与函数参数适应
                    spSheet->GetWoStake()->setCopyHtmlBreakSize(limitedSize);
                    format = Format::html;
                }

                acpt.beginStruct();
                acpt.addString("sheetId", krt::utf16(QString::number(sheetStId)));
                    // copyInformation->format 的设计并没有考虑到 format 要序列化到前端的情况
                    switch (format)
                    {
                    case Format::html:
                        acpt.addString("format", __X("html"));
                        break;
                    case Format::text:
                        acpt.addString("format", __X("text"));
                        break;
                    default:
                        ASSERT(false);
                        break;
                    }
                    
                    if (copyInformation->successed)
                    {
                        ks_stdptr<IStream> spStrm;
                        spStrm.attach(copyInformation->medium->pstm);
                        KsoDataStream* const dataStream = ks_castptr<KsoDataStream>(copyInformation->medium->pstm);
                        ASSERT(copyInformation->medium->tymed == TYMED_ISTREAM and dataStream);
                        const QByteArray binaryData = dataStream->getByteArray();
                        switch (format)
                        {
                        case Format::html:
                            acpt.addString("html", krt::utf16(QString::fromUtf8(binaryData)));
                            break;
                        case Format::text:
                            acpt.addString("text", reinterpret_cast<PCWSTR>(binaryData.data()));
                            break;
                        default:
                            ASSERT(false);
                            break;
                        }
                    }
                    else
                    {
                        acpt.addString("content", __X(""));
                    }
                acpt.endStruct();
            }
        acpt.endArray();

        // file_SHA1_list
        for (int i = 0; i < ranges.size(); ++i)
        {
            const RANGE* const range = ranges[i].second;
            for (INT32 j = range->RowFrom(); j <= range->RowTo(); ++j)
            {
                for (INT32 k = range->ColFrom(); k <= range->ColTo(); ++k)
                {
                    const drawing::AbstractShape* const pShape = pCellImages->GetImgCellAtom(sheetIndex, j, k);
                    if (pShape)
                    {
                        QByteArray SHA1_byte, _;
                        pShape->getPicture(SHA1_byte, _, DPI / 96.0);
                        file_SHA1_list.emplace(SHA1_byte);
                    }
                }
            }
        }
    }
    acpt.endArray();        // END copy core data

    // 回传 file_SHA1_list 总数据
    acpt.addKey("file_SHA1_list");
    acpt.beginArray();
    for (const QString& result : file_SHA1_list)
    {
        acpt.addString(nullptr, krt::utf16(result));
    }
    acpt.endArray();

    return S_OK;
}

// -----------------------------------------------------------------------------------------------------------
EtHttpGetRangeQRLabelCountQueryClass::EtHttpGetRangeQRLabelCountQueryClass(wo::KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getRangeQRLabelCount"))
{

}

HRESULT EtHttpGetRangeQRLabelCountQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    VAR_OBJ_EXPECT_STRUCT(param, "range")
    binary_wo::VarObj varRange = param.get("range");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    RANGE range(pBook->GetBMP());
    range.SetSheetFromTo(sheetIdx);
    range.SetRowFromTo(varRange.field_int32("rowFrom"), varRange.field_int32("rowTo"));
    range.SetColFromTo(varRange.field_int32("colFrom"), varRange.field_int32("colTo"));
    if (!range.IsValid())
        return E_RANGE_INVALID;
    
    ASSERT(range.SheetFrom() == range.SheetTo());

    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(range.SheetFrom(), &spSheet);
    if (!spSheet)
        return E_FAIL;

    if (!spSheet->IsGridSheet())
        return E_KSHEET_NOT_GRIDSHEET;

    std::tuple<UINT, UINT, bool> queryResult = qrlabel_helper::QueryQRLabelCount(ctx, range, spSheet);
    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addUint32("totalQRLabelCount", std::get<0>(queryResult));
	acpt.addUint32("visibleQRLabelCount", std::get<1>(queryResult));
    acpt.addBool("hasUncalculatedCell", std::get<2>(queryResult));
    return S_OK;
}

// -----------------------------------------------------------------------------------------------------------
EtHttpExecuteQueryApiClass::EtHttpExecuteQueryApiClass(wo::KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.ExecQueryApi"))
{
}

HRESULT EtHttpExecuteQueryApiClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    KJsApiHelper helper(ctx, m_wwb, true);
    return helper.Execute(param);
}

// -----------------------------------------------------------------------------------------------------------
EtHttpGetLocalImagesSha1Class::EtHttpGetLocalImagesSha1Class(wo::KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getLocalImagesSha1"))
{
}

HRESULT EtHttpGetLocalImagesSha1Class::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_ARRAY(param, "imageNames");
    qreal dpi = 96.0;
    if (param.has("dpi"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "dpi");
        dpi = static_cast<qreal>(param.field_double("dpi"));
    }
    ICellImages* pCellImages = m_wwb->GetCoreWorkbook()->GetCellImages();
    if (!pCellImages)
        return E_FAIL;

    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    sa::Leave leaveStruct(sa::enterStruct(&acpt, "sha1s"));
    binary_wo::VarObj imageNamesObj = param.get("imageNames");
    for (int i = 0; i < imageNamesObj.arrayLength(); ++i)
    {
        binary_wo::VarObj imageNameObj = imageNamesObj.at(i);
        if (imageNameObj.type() != typeString)
            return E_INVALID_REQUEST;

        PCWSTR imageName = imageNameObj.value_str();
        std::string imageNameStr = QString::fromUtf16(imageName).toStdString();
        QString sha1 = GetImageSha1(pCellImages, imageName, dpi, ctx);
        acpt.addKey(imageNameStr.c_str(), true);
        acpt.addString(nullptr, krt::utf16(sha1));
    }
    return S_OK;
}

QString EtHttpGetLocalImagesSha1Class::GetImageSha1(ICellImages* pCellImages, PCWSTR imageName, qreal dpi, KEtRevisionContext* pCtx)
{
    ks_castptr<drawing::AbstractShape> cpShape = pCellImages->GetImgByName(imageName);
    QByteArray sha1;
    if (cpShape)
    {
        IKBlipAtom* spBlipAtom = cpShape->picID();
        if (spBlipAtom)
        {
            ks_bstr bstrUrl;
            spBlipAtom->GetLinkPath(&bstrUrl);
            if (bstrUrl.empty() && cpShape->isPicture())
                util::UploadImg(dpi, cpShape, sha1);
        }
    }
    return QString(sha1);
}

EtHttpRangeMatrixQueryClass::EtHttpRangeMatrixQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getRangeMatrix"))
{
}

HRESULT EtHttpRangeMatrixQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId")
    VAR_OBJ_EXPECT_STRUCT(param, "range")
    binary_wo::VarObj varRange = param.get("range");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    ks_stdptr<etoldapi::_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();

    RANGE rg(spWorkbook->GetBook()->GetBMP());
    IDX sheetIdx = INVALIDIDX;
    m_wwb->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(param.field_int32("sheetStId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;
    rg.SetSheetFromTo(sheetIdx);
    int rowFrom = varRange.field_int32("rowFrom");
    int rowTo = varRange.field_int32("rowTo");
    int colFrom = varRange.field_int32("colFrom");
    int colTo = varRange.field_int32("colTo");
    rg.SetRowFromTo(rowFrom, rowTo);
    rg.SetColFromTo(colFrom, colTo);
    
    if(!rg.IsValid())
    {
        WOLOG_INFO << "[ExportRangeData] param struct error";
        return E_RANGE_INVALID;
    }

    ASSERT(rg.SheetFrom() == rg.SheetTo());
    IKWorksheet* pWorksheet = spWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
    if (pWorksheet == NULL) 
    {
        WOLOG_INFO << "[ExportRangeData] get worksheet failed, sheet idx=" << sheetIdx;	
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ks_stdptr<ISheet> spSheet = pWorksheet->GetSheet();
    if (spSheet == NULL) 
    {
        WOLOG_INFO << "[ExportRangeData] get sheet failed, sheet idx=" << sheetIdx;	
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    if (spSheet->IsGridSheet() == false)
    {
        return E_KSHEET_NOT_GRIDSHEET;
    }

    QSet<int> hideColsList;
    if (param.has("hideCols"))
    {
        VAR_OBJ_EXPECT_ARRAY(param, "hideCols")
        binary_wo::VarObj hideCols = param.get_s("hideCols");
        for (int i = 0; i < hideCols.arrayLength_s(); i++)
        {
            auto hideColItem = hideCols.item_int32(i);
            hideColsList.insert(hideColItem);
        }
    }

    PCWSTR bookName = nullptr;
    if (param.has("bookName"))
    {
        VAR_OBJ_EXPECT_STRING(param, "bookName")
        bookName = param.field_str("bookName");
    }
    int sourceIndex = 0;
    if (param.has("sourceIndex"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "sourceIndex")
        sourceIndex = param.field_int32("sourceIndex");
    }
    PCWSTR tableText = nullptr;
    if (param.has("tableText"))
    {
        VAR_OBJ_EXPECT_STRING(param, "tableText")
        tableText = param.field_str("tableText");
    }

    et_sdptr<ISheetEnum> spSheetEnum;
    spSheet->CreateEnum(&spSheetEnum);
    RANGE usedRg(spSheet->GetBMP());
    pWorksheet->GetUsedRange(&usedRg);
    EmptyColAcceptor emptyColAcceptor;
    spSheetEnum->EnumCellValue(usedRg, &emptyColAcceptor);
    
    // 获取非空列集合，用于跳过空列
    KNotEmptyColsSet& notEmptyColsSet = emptyColAcceptor.getNotEmptyCols();
    notEmptyColsSet.SetColFrom(colFrom);
    std::vector<std::vector<ks_wstring>> matrix(rowTo - rowFrom + 1, std::vector<ks_wstring>(colTo - colFrom + 1, ks_wstring()));
    CellTextAcpt cellValue(spWorkbook, sheetIdx, spSheet, &acpt, ctx, &matrix);
    cellValue.setRangeFrom(rowFrom, colFrom);
    int ret = spSheetEnum->EnumCellValue(rg, &cellValue);

    std::vector<ks_wstring> headerNameWithAutoCompleteEmptyVec;
    processMatrixColName(headerNameWithAutoCompleteEmptyVec, sourceIndex, matrix, notEmptyColsSet, tableText);
    exportRangeData(notEmptyColsSet, matrix, headerNameWithAutoCompleteEmptyVec, hideColsList, acpt);

    return S_OK;
}

EtHttpDataAnalyzeMergeInfoQueryClass::EtHttpDataAnalyzeMergeInfoQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getDataAnalyzeMergeTaskInfo"))
{
}


HRESULT EtHttpDataAnalyzeMergeInfoQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId")
    int sheetStId = param.field_int32("sheetStId");
    int sheetIdx = -1;
    IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    if (!pWorkSheet)
    {
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ISheet* pSheet = pWorkSheet->GetSheet();
    if (!pSheet)
    {
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ks_stdptr<IEtDataAnalyzeData> spData;
    pSheet->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
    if (!spData)
    {
        return E_FAIL;
    }
    auto list = spData->GetDataAnalyseInfoList();
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("fileList");
    {
        wo::sa::Leave mergeColConditionLeave = wo::sa::enterArray(&acpt, nullptr);
        for(auto it : list)
        {
            etda::MergeInfo* info = it.second;
            if (!info)
                continue;
            for (auto it : info->dataSourceConfig)
            {
                wo::sa::Leave sourceConfigLeave = wo::sa::enterStruct(&acpt, nullptr);
                acpt.addInt32("sourceId", it.sourceId);
                acpt.addString("fileId", krt::utf16(it.fileId));
                acpt.addInt32("sheetStId", it.sheetStId);
            }
        }
    }

    acpt.addKey("taskList");
    wo::sa::Leave taskListLeave = wo::sa::enterArray(&acpt, nullptr);
    for(auto it : list)
    {
        wo::sa::Leave stLeave = wo::sa::enterStruct(&acpt, nullptr);
        {
            acpt.addInt32("taskId", it.first);
            etda::MergeInfo* info = it.second;
            if (!info)
                continue;
            acpt.addKey("mergeInfo");
            {
                wo::sa::Leave mergInfoLeave = wo::sa::enterStruct(&acpt, nullptr);
                acpt.addKey("mergeConfig");
                {
                    wo::sa::Leave mergeConfigLeave = wo::sa::enterStruct(&acpt, nullptr);
                    auto mergeDirection = info->mergeConfig.mergeDirection;
                    ks_wstring mergeType = __X("union");
                    if (mergeDirection == etda::MergeType::Join)
                    {
                        mergeType = __X("join");
                        auto joinMode = info->mergeConfig.joinMode;
                        ks_wstring mode = __X("left");
                        if (joinMode == etda::JoinMode::RightJoin)
                            mode = __X("right");
                        if (joinMode == etda::JoinMode::OuterJoin)
                            mode = __X("outer");
                        if (joinMode == etda::JoinMode::InnerJoin)
                            mode = __X("inner");
                        acpt.addString("joinMode", mode.c_str());
                    }
                    acpt.addString("mergeDirection", mergeType.c_str());
                }
                acpt.addKey("execResultConfig");
                {
                    wo::sa::Leave execResultLeave = wo::sa::enterStruct(&acpt, nullptr);
                    acpt.addKey("filterCols");
                    {
                        wo::sa::Leave filterColsLeave = wo::sa::enterArray(&acpt, nullptr);
                        for(auto i : info->execResultConfig.filterCols)
                        {
                            acpt.addInt16(nullptr, i);
                        }
                    }
                    acpt.addKey("finalColNames");
                    {
                        wo::sa::Leave finalColNamesLeave = wo::sa::enterArray(&acpt, nullptr);
                        for(auto i : info->execResultConfig.finalColNames)
                        {
                            acpt.addString(nullptr,krt::utf16(i));
                        }
                    }
                    acpt.addString("targetFileId", krt::utf16(info->execResultConfig.targetFileId));
                    acpt.addInt16("targetSheetStId", info->execResultConfig.targetSheetStId);
                    acpt.addBool("isDeduplication", info->execResultConfig.isDeduplication);
                    acpt.addBool("isTagSource", info->execResultConfig.isTagSource);
                }
                acpt.addKey("dataSourceConfig");
                {
                    wo::sa::Leave dataSourceLeave = wo::sa::enterArray(&acpt, nullptr);
                    for (auto it : info->dataSourceConfig)
                    {
                        wo::sa::Leave sourceConfigLeave = wo::sa::enterStruct(&acpt, nullptr);
                        acpt.addInt32("sourceId", it.sourceId);
                        acpt.addString("fileId", krt::utf16(it.fileId));
                        acpt.addInt32("sheetStId", it.sheetStId);
                        acpt.addInt32("headRow", it.headRow);
                        acpt.addString("bookName", krt::utf16(it.bookName));
                        acpt.addString("sheetName", krt::utf16(it.sheetName));
                        acpt.addInt32("sourceIndex", it.sourceIndex);
                        acpt.addKey("filterCols");
                        {
                            wo::sa::Leave filterColsLeave = wo::sa::enterArray(&acpt, nullptr);
                            for (auto i : it.filterCols)
                            {
                                acpt.addInt32(nullptr, i);
                            }
                        }
                        acpt.addKey("mergeColCondition");
                        {
                            wo::sa::Leave mergeColConditionLeave = wo::sa::enterArray(&acpt, nullptr);
                            for (auto i : it.mergeCondition)
                            {
                                acpt.addString(nullptr, krt::utf16(i));
                            }
                        }
                    }
                }
            }
        }
    }
    return S_OK;
}

EtHttpDataAnalyzeMergeLogQueryClass::EtHttpDataAnalyzeMergeLogQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getDataAnalyzeMergeLog"))
{
}

HRESULT EtHttpDataAnalyzeMergeLogQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId")
    int sheetStId = param.field_int32("sheetStId");

    VAR_OBJ_EXPECT_NUMERIC(param, "fromIdx")
    int fromIdx = param.field_int32("fromIdx");

    VAR_OBJ_EXPECT_NUMERIC(param, "endIdx")
    int endIdx = param.field_int32("endIdx");
    int sheetIdx = -1;
    IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    if (!pWorkSheet)
    {
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ISheet* pSheet = pWorkSheet->GetSheet();
    if (!pSheet)
    {
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ks_stdptr<IEtDataAnalyzeData> spData;
    pSheet->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
    if (!spData)
    {
        return E_FAIL;
    }
    auto logList = spData->GetDataAnalyseLogList();
    std::reverse(logList.begin(), logList.end());
    int totalCount = logList.size();
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addInt16("totalCount", totalCount);
    acpt.addKey("tasks");
    {
        wo::sa::Leave tasksArrayLeave = wo::sa::enterArray(&acpt, nullptr);
        fromIdx = std::min(fromIdx, totalCount);
        endIdx = std::min(endIdx, totalCount);
        for (int i = fromIdx; i < endIdx; i++)
        {
            wo::sa::Leave taskConfigLeave = wo::sa::enterStruct(&acpt, nullptr);
            auto item = logList[i];
            acpt.addInt32("taskId", item->taskId);
            acpt.addString("date", krt::utf16(item->date));
            acpt.addString("time", krt::utf16(item->time));
            acpt.addInt32("updateType", item->updateType);
            acpt.addInt32("logId", item->logId);
            acpt.addString("status", krt::utf16(item->status));
            if (item->status != "success")
            {
                acpt.addString("errorCode", krt::utf16(QString::number(etda::EXPORT_FILE_FAILED)));
            }
            acpt.addKey("sources");
            {
                wo::sa::Leave sourcesLeave = wo::sa::enterArray(&acpt, nullptr);
                for (auto it : item->sources)
                {
                    wo::sa::Leave sourceLeave = wo::sa::enterStruct(&acpt, nullptr);
                    acpt.addString("fileName", krt::utf16(it.fileName));
                    acpt.addString("sheetName", krt::utf16(it.sheetName));
                    acpt.addInt32("cols", it.cols);
                    acpt.addString("status", krt::utf16(it.status));
                    acpt.addString("failReason", krt::utf16(it.failReason));
                    acpt.addString("errorCode", krt::utf16(it.errorCode));
                }
            }
        }
    }
    return S_OK;
}

EtHttpCheckHasHiddenQueryClass::EtHttpCheckHasHiddenQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.checkHasHidden"))
{
}

HRESULT EtHttpCheckHasHiddenQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    if (pBook == nullptr)
        return E_FAIL;

    int sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    bool hasHidden = false;
    for (int i = 0; i < sheetCount; i++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        if (!spSheet)
            continue;
        hasHidden = ctx->getProtectionCtx()->isSheetHasHidden(spSheet);
        if (hasHidden)
            break;
    }

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addBool("hasHidden", hasHidden);
    return S_OK;
}
bool ExportShape(drawing::AbstractShape* spShape, ISerialAcceptor* pAcpt, int16 dpi, bool isForCellPic)
{
    IKBlipAtom* spBlipAtom = spShape->picID();
    if (isForCellPic && !spBlipAtom)
    {
        WOLOG_INFO << "[ExportRangeData] get IKBlipAtom failed!";
        pAcpt->addBool("isCellPic", FALSE);
        return true;//继续枚举
    }
    
    ks_bstr strUrl;
    if (spBlipAtom)
        spBlipAtom->GetLinkPath(&strUrl);
    if (strUrl.empty())
    {
        // 本地图片(非在线图片)
        QByteArray sha1;
        bool uploaded = util::UploadImg(
            dpi, spShape, sha1);
        if (not uploaded)
        {
            WOLOG_INFO << "[ExportRangeData] shape get Picture failed!";
            return true; //继续枚举
        }
        else
        {
            pAcpt->addString("tag", __X("local"));
            pAcpt->addString("sha1",krt::utf16(QString(sha1)));
            return true;//继续枚举
        }
    }
    else
    {
        //在线图片(1附件图片;2.云空间图片)
        //判断是否为附件
        bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(strUrl.c_str());
        if(isAttachment)
        {
            pAcpt->addString("tag", __X("attachment"));
            PCWSTR attachmentId = util::getAttachmentId(strUrl.c_str());
            pAcpt->addString("picData", attachmentId);
            return true;//继续枚举
        }
        else
        {
            pAcpt->addString("tag", __X("url"));
            pAcpt->addString("picData", strUrl.c_str());
            return true;//继续枚举
        }
    }

    return false;
}

//////////////////////////////////////////////////////////////////////////
EtHttpGetFieldInfoQueryClass::EtHttpGetFieldInfoQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getFieldInfo"))
{
}

HRESULT EtHttpGetFieldInfoQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    IKWorksheets* pSheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	int sheetLimitItems = param.field_int32("sheetLimitItems");
	bool bHasSheetIdVec = param.has("sheetIdVec");
    VAR_OBJ_EXPECT_STRING(param, "defaultName");
    PCWSTR pcwDefaultName = param.field_str("defaultName");

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    HRESULT hr = S_OK;

    wo::sa::Leave leaveStruct = wo::sa::enterStruct(&acpt, "res");
    wo::sa::Leave leaveArray = wo::sa::enterArray(&acpt, "sheets");
	if (bHasSheetIdVec)
	{
		VarObj sheetIdVec = param.get("sheetIdVec");
		for (int i = 0, len = sheetIdVec.arrayLength(); i < len; ++i)
        {
            IDX sheetIdx = INVALIDIDX;
            pBook->STSheetToRTSheet(sheetIdVec.at(i).value_int32(), &sheetIdx);
            if (sheetIdx == INVALIDIDX)
                continue;
			hr = WriteFieldInfo(ctx, &acpt, sheetIdx, sheetLimitItems, pcwDefaultName);
            if (FAILED(hr))
                return hr;
        }
	}
	else
	{
		for (int i = 0, len = pSheets->GetSheetCount(); i < len; ++i)
        {
            hr = WriteFieldInfo(ctx, &acpt, i, sheetLimitItems, pcwDefaultName);
            if (FAILED(hr))
                return hr;
        }
	}
	return S_OK;
}
HRESULT EtHttpGetFieldInfoQueryClass::genFieldInfo(ISerialAcceptor* acpt, PCWSTR pcwDefaultName)
{
    wo::sa::Leave leaveArray = wo::sa::enterArray(acpt, "fields");
    constexpr int iDefaultFieldNums = 6;
    for (int i = 0; i < iDefaultFieldNums; i++)
    {
        wo::sa::Leave leaveStruct = wo::sa::enterStruct(acpt, nullptr);
        acpt->addString("name", pcwDefaultName);
        PCWSTR typeStr = nullptr;
        VS(_appcore_GainEncodeDecoder()->EncodeFieldType(Et_DbSheetField_MultiLineText, &typeStr));
        acpt->addString("type", typeStr);
    }
    return S_OK;
}

HRESULT EtHttpGetFieldInfoQueryClass::WriteFieldInfo(IN KEtRevisionContext* ctx, IN ISerialAcceptor* acpt, IN IDX sheetIdx, IN int& leftCount, PCWSTR pcwDefaultName)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet || !spSheet->IsGridSheet())
		return E_KSHEET_NOT_GRIDSHEET;
	
    PCWSTR sheetName = nullptr;
    spSheet->GetName(&sheetName);
    UINT stId = spSheet->GetStId();

    RANGE rg(spSheet->GetBMP());
    IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    pWorksheet->GetUsedRange(&rg);
    if (!rg.IsValid())
		return E_INVALIDARG;
    if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
		return E_NOT_HAVE_PERMISSION;

    wo::sa::Leave leave = wo::sa::enterStruct(acpt, nullptr);
	acpt->addInt32("id", stId);
    acpt->addString("name", sheetName);
    TableStructRecHelper::TableTitleInfo info;
	HRESULT hr = TableStructRecHelper::GetTableTitleInfo(m_wwb, ctx, sheetIdx, info);
    if ((info.errorCode != TableStructRecHelper::TableStructRecSuccessed &&
         info.errorCode != TableStructRecHelper::TableStructRecFailed && info.errorCode != TableStructRecHelper::TableStructRecOverLimit)
        || S_OK != hr
        || info.colFrom > info.colTo)
    {
        if (S_OK == hr)
            hr = TableStructRecHelper::GetResult(info.errorCode);
        // 产品要求如果是空表则返回默认的表字段
        if (info.errorCode == TableStructRecHelper::TableStructRecNull)
        {
            genFieldInfo(acpt, pcwDefaultName);
            hr = S_OK;
        }
		return hr;
    }
    rg.SetRowFrom(info.row);
    Et2DbFieldCopyHelper stFieldCopyHelper(m_wwb->GetCoreWorkbook(), static_cast<etoldapi::_Worksheet*>(pWorksheet), nullptr, ctx, rg);
    // 去掉标题行之后再推导类型
    stFieldCopyHelper.SetIterRowsByRange(rg.RowFrom() + 1, rg.RowTo());
    int primaryCol = info.colFrom;

    wo::sa::Leave leaveArray = wo::sa::enterArray(acpt, "fields");
	for (int j = info.colFrom; j <= info.colTo; ++j)
	{
		if (leftCount <= 0)
			break;

        wo::sa::Leave leaveStruct = wo::sa::enterStruct(acpt, nullptr);
		ks_bstr cellText;
		ctx->getStringTools()->GetCellText(spSheet, info.row, j, &cellText, nullptr, -1, nullptr);
        if (cellText)
		    acpt->addString("name", cellText.c_str());
        else
            acpt->addString("name", pcwDefaultName);

        ET_DbSheet_FieldType type = ET_DbSheet_FieldType_Invalid;
        stFieldCopyHelper.BeginDeduceFieldType();
        for (;stFieldCopyHelper.Valid();stFieldCopyHelper.Next())
        {
            hr = stFieldCopyHelper.DeduceFieldType(j);
            if (hr == S_OK)
                break;
        }
        stFieldCopyHelper.EndDeduceFieldType();
		PCWSTR numFmt = nullptr;
		stFieldCopyHelper.GetTypeInfo(type, numFmt);
		if (j == primaryCol)
			stFieldCopyHelper.CorrectionForPrimaryField(type, numFmt);
        PCWSTR typeStr = nullptr;
        VS(_appcore_GainEncodeDecoder()->EncodeFieldType(type, &typeStr));
        acpt->addString("type", typeStr);

		--leftCount;
	}
    return S_OK;
}

//////////////////////////////////////////////////////////////////////////
EtHttpGetHeaderInfoQueryClass::EtHttpGetHeaderInfoQueryClass(KEtWorkbook* wb)
    : EtHttpQueryClassBase(wb, __X("http.et.getHeaderInfo"))
{
}

HRESULT EtHttpGetHeaderInfoQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    IKWorksheets* pSheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	bool bHasSheetIdVec = param.has("sheetIdVec");
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    HRESULT hr = S_OK;
    int len = 0;
    VarObj sheetIdVec;
    if (bHasSheetIdVec)
    {
        sheetIdVec = param.get("sheetIdVec");
        len = sheetIdVec.arrayLength();
    }
    {
        wo::sa::Leave leaveArray = wo::sa::enterArray(&acpt, "sheets");
        if (len > 0)
        {
            VAR_OBJ_EXPECT_ARRAY(param, "sheetIdVec");
            for (int i = 0; i < len; ++i)
            {
                IDX sheetIdx = INVALIDIDX;
                pBook->STSheetToRTSheet(sheetIdVec.at(i).value_int32(), &sheetIdx);
                if (sheetIdx == INVALIDIDX)
                    continue;
                hr = WriteHeaderInfo(ctx, &acpt, sheetIdx);
                if (FAILED(hr))
                    return hr;
            }
        }
        else
        {
            for (int i = 0, len = pSheets->GetSheetCount(); i < len; ++i)
            {
                hr = WriteHeaderInfo(ctx, &acpt, i);
                if (FAILED(hr))
                    return hr;
            }
        }
    }
    if (m_wwb->GetBMP()->bKsheet)
		acpt.addString("bookType", __X("as"));
    else if (m_wwb->GetBMP()->bDbSheet)
        acpt.addString("bookType", __X("db"));
	return S_OK;
}

static void fillCustomListValues(KEtWorkbook* pEtWorkbook, ISerialAcceptor* acpt, ISheet* pSheet, ROW row, COL col)
{
    RANGE rgCell(pSheet->GetBMP());
    IDX iSheetIdx = INVALIDIDX;
    pSheet->GetIndex(&iSheetIdx);
    rgCell.SetCell(iSheetIdx, row, col);
    ks_stdptr<etoldapi::Range> spRangeApi = pEtWorkbook->CreateRangeObj(rgCell);
    if (!spRangeApi)
        return;

    KCOMPTR(Validation) ptrValidation;
    spRangeApi->get_Validation(&ptrValidation);
    KCOMPTR(IValidationInfo) ptrValidationInfo = ptrValidation;
    if (!ptrValidationInfo)
        return;
    std::etvector<BSTR> vecValue;
    util::EtVectorBSTRGuard vecValueAutoRelease(vecValue);
    HRESULT hr = ptrValidationInfo->GetCustomListValues(ETStringToolsOpt_None, &vecValue, FALSE);
    if (FAILED(hr) || vecValue.empty())
        return;
    wo::sa::Leave leaveArray = wo::sa::enterArray(acpt, "items");
    for (int i = 0, size = vecValue.size(); i < size; ++i)
    {
        BSTR bstrValue = vecValue.at(i);
        if (bstrValue && __Xc('\0') != *bstrValue)
        {
            wo::sa::Leave leaveStruct = wo::sa::enterStruct(acpt, nullptr);
            acpt->addString("value", bstrValue);
        }
    }
}

HRESULT EtHttpGetHeaderInfoQueryClass::WriteHeaderInfo(IN KEtRevisionContext* ctx, IN ISerialAcceptor* acpt, IN IDX sheetIdx)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet || !spSheet->IsGridSheet())
		return E_KSHEET_NOT_GRIDSHEET;
	
    PCWSTR sheetName = nullptr;
    spSheet->GetName(&sheetName);
    UINT stId = spSheet->GetStId();

    RANGE rg(spSheet->GetBMP());
    IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    pWorksheet->GetUsedRange(&rg);
    if (!rg.IsValid())
		return E_INVALIDARG;
    if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
		return E_NOT_HAVE_PERMISSION;

    wo::sa::Leave leave = wo::sa::enterStruct(acpt, nullptr);
	acpt->addInt32("id", stId);
    acpt->addString("name", sheetName);

    ks_stdptr<IEt2DbSyncData> spEt2DbSyncData;
    spSheet->GetExtDataItem(edSheetEt2DbSyncData, (IUnknown**)&spEt2DbSyncData);
    if (!spEt2DbSyncData)
        return E_DBSHEET_SYNC_GRIDSHEET_DATA_INVALID;
    IEtIdContainer* pEtIdContainer = spSheet->GetWoStake()->GetEtIdContainer();
    if (!pEtIdContainer)
        return E_DBSHEET_SYNC_GRIDSHEET_ID_CONTAINER_INVALID;
    EtDbId headerId = pEtIdContainer->GetHeaderId();
    EtDbIdStr buf;
    VS(_appcore_GainDbSheetContext()->EncodeEtDbId(headerId, &buf));	
    acpt->addString("headerId", buf);
    wo::sa::Leave leaveArray = wo::sa::enterArray(acpt, "fields");
	// 枚举器
    class FillFieldsEtSyncDataEnum : public IEtSyncDataEnum
    {
    public:
        FillFieldsEtSyncDataEnum(ISerialAcceptor* acpt, ISheet* pSheet, IEtIdContainer* pEtIdContainer,
                                 KEtRevisionContext* pCtx, KEtWorkbook* pEtWorkbook)
        : m_acpt(acpt)
        , m_pSheet(pSheet)
        , m_pEtIdContainer(pEtIdContainer)
        , m_pCtx(pCtx)
        , m_headerIndex(pEtIdContainer->GetHeaderIndex())
        , m_pEtWorkbook(pEtWorkbook)
        {}
        STDPROC_(BOOL) Do(EtDbId id, IEtFieldItem* pItem) override
        {
            COL col = m_pEtIdContainer->ColumnIdToIndex(id);
            if (col < 0)
                return TRUE;
            wo::sa::Leave itemLeave = wo::sa::enterStruct(m_acpt, nullptr);
            EtDbIdStr buf;
            VS(_appcore_GainDbSheetContext()->EncodeEtDbId(id, &buf));	
            m_acpt->addString("fieldId", buf);
            PCWSTR typeStr = nullptr;
			VS(_appcore_GainEncodeDecoder()->EncodeFieldType(pItem->GetFieldType(), &typeStr));
            m_acpt->addString("type", typeStr);
            ks_bstr cellText;
            m_pCtx->getStringTools()->GetCellText(m_pSheet, m_headerIndex, col, &cellText, nullptr, -1, nullptr);
            if (!cellText)
                m_acpt->addString("name", __X(""));
            else
		        m_acpt->addString("name", cellText.c_str());
            if (Et_DbSheetField_SingleSelect == pItem->GetFieldType() || Et_DbSheetField_MultipleSelect == pItem->GetFieldType())
                fillCustomListValues(m_pEtWorkbook, m_acpt, m_pSheet, m_headerIndex + 1, col);
            return TRUE;
        }
    private:
        ISerialAcceptor* m_acpt;
        ISheet* m_pSheet;
        IEtIdContainer* m_pEtIdContainer;
        EtDbIdx m_headerIndex;
        KEtRevisionContext* m_pCtx;
        KEtWorkbook* m_pEtWorkbook = nullptr;
    };
    FillFieldsEtSyncDataEnum stFillFields(acpt, spSheet, pEtIdContainer, ctx, m_wwb);
    spEt2DbSyncData->EnumEtSyncData(&stFillFields, ET_SyncDataType::Et_SyncDataItemsMap);
    return S_OK;
}

// -----------------------------------------------------------------------------------------------------------
EtHttpQueryProtectionInfoClass::EtHttpQueryProtectionInfoClass(wo::KEtWorkbook* wb)
	: EtHttpQueryClassBase(wb, __X("http.et.getProtectionInfo"))
{
}

HRESULT EtHttpQueryProtectionInfoClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_STRING(param, "userId");
	PCWSTR userId = param.field_str("userId");

	IKWorkbook *pWorkbook = m_wwb->GetCoreWorkbook();
	if (!pWorkbook)
		return E_FAIL;
	IKWorksheets *pWorksheets = pWorkbook->GetWorksheets();
	if (!pWorksheets)
		return E_FAIL;

	ProtectionAccessPerms perm = ProtectionAccessPerms::PTAAP_Edit;

	int iSheetCnt = pWorksheets->GetSheetCount();
	for (int i = 0; i < iSheetCnt; ++i)
	{
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(i);
		if (!pWorksheet)
			return E_FAIL;
		ISheetProtection* pSheetProtection = pWorksheet->GetProtection();
		if (!pSheetProtection)
			return E_FAIL;
		if (!pSheetProtection->IsProtected())
			continue;

		int cnt = pSheetProtection->GetUserRangeCount();
		for (int j = 0; j < cnt; ++j)
		{
			bool bInUserData = false;
			auto pRangeData = pSheetProtection->GetUserRangeUser(j);
			if (!pRangeData)
				return E_FAIL;
			for (const AllowEditRangeUserData& data : *pRangeData)
			{
				if (xstrcmp(data.userId.c_str(), userId) == 0)
				{
					bInUserData = true;
					perm = static_cast<ProtectionAccessPerms>(perm & data.accessPermission);
					break;
				}
			}
			if (!bInUserData)
				perm = static_cast<ProtectionAccessPerms>(perm & pSheetProtection->GetOthersAccessPermission(j));
			if (perm == ProtectionAccessPerms::PTAAP_Invisible)
				break;
		}

		if (wo::isEnableRegionProtect(pWorksheet) && xstrcmp(pSheetProtection->GetMaster(), userId) != 0)
			perm = static_cast<ProtectionAccessPerms>(perm & pSheetProtection->GetOtherUserPermission());
		if (perm == ProtectionAccessPerms::PTAAP_Invisible)
			break;
	}

	binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	WebStr strPerm = __X("invisible");
	if (perm == ProtectionAccessPerms::PTAAP_Edit)
		strPerm = __X("edit");
	else if (perm == ProtectionAccessPerms::PTAAP_Visible)
		strPerm = __X("visible");

	acpt.addString("permission", strPerm);

	return S_OK;
}

} // namespace wo
