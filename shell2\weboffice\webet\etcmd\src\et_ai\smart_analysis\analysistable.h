﻿#ifndef __SMARTANALYIS_DATAANALYSIS_H__
#define __SMARTANALYIS_DATAANALYSIS_H__

#include "smartanalysis.h"
namespace etai
{
enum KDataFieldTypeEnum
{
    DataFieldTypeString = 0,
    DataFieldTypeNumber,
    DataFieldTypeDate
};

class KDataFieldItem
{
  public:
    KDataFieldItem(QString name, long cnt);
    QString GetName() const;
    long GetRecordCount() const;
    void AddCount(long cnt);

  protected:
    QString strName;
    long nRecordCount;
};

class KDataFieldItemList : public std::vector<KDataFieldItem *>
{
  public:
    KDataFieldItemList();
    virtual ~KDataFieldItemList();
    virtual void ClearAll();

    KDataFieldItem *FindByName(QString strName);
    const KDataFieldItem *FindByName(QString strName) const;
};

class KDataField
{
  public:
    KDataField(int i, QJsonObject &jsonColumn, QString name);
    KDataField(int i, QString name);
    virtual ~KDataField();

    int GetIndexInt() const;
    QString GetIndex() const;
    QString GetName() const;
    QString GetClassType() const;
    QString GetIdentifyType() const;
    KDataFieldTypeEnum GetType() const;
    QString GetTypeStr() const;
    double GetPredValue() const;
    virtual BOOL IsNumberField() const;
    virtual void ExportJson(QJsonObject &jsonObj, int nMaxItemCnt) const;
    virtual void ResetState()
    {
    }

  protected:
    void Init(QJsonObject &jsonColumn);

  protected:
    int m_nIdx;
    QString m_strIdx;
    QString m_strName;
    QString m_strIdentifyType;
    QString m_strClassType;
    KDataFieldTypeEnum m_nType;
    double m_dPredValue;
};

class KNumberField : public KDataField
{
  public:
    KNumberField(int i, QJsonObject &jsonColumn, QString name);
    KNumberField(int i, QString name, bool is, oldapi::ETConsolidationFunction type);
    virtual ~KNumberField();
    void SetSum(double sum);
    double GetSum() const;
    void SetAverage(double avg);
    double GetAverage() const;
    void SetMax(double max);
    double GetMax() const;
    void SetMin(double min);
    double GetMin() const;
    bool IsPercent() const;
    oldapi::ETConsolidationFunction GetNumberFieldType() const;
    oldapi::ETConsolidationFunction GetUsedNumberFieldType() const;
    void SetUsedNumberFieldType(oldapi::ETConsolidationFunction numType);
    virtual BOOL IsNumberField() const override;
    virtual void ExportJson(QJsonObject &jsonObj, int nMaxItemCnt) const override;
    virtual void ResetState() override;

  protected:
    double m_dSum;
    double m_dAverage;
    double m_dMax;
    double m_dMin;
    bool m_bPercent;
    oldapi::ETConsolidationFunction m_nNumType;     // 默认的
    oldapi::ETConsolidationFunction m_nUsedNumType; // 用户选择的
};

class KStringField : public KDataField
{
  public:
    KStringField(int i, QJsonObject &jsonColumn, QString name);
    KStringField(int i, QString name);
    virtual ~KStringField();
    long GetSumCount() const;
    virtual void AddDataFieldItem(QString name, long cnt);
    int GetItemCount() const;
    virtual const KDataFieldItemList &GetDataFieldItemList() const;
    void AddIncludeField(QString strField, double dPredValue);
    BOOL IsIncludeField(QString strField) const;
    virtual void ExportJson(QJsonObject &jsonObj, int nMaxItemCnt) const;
    virtual void SortItemList(bool bDes = true);
    virtual void ResetState();

  protected:
    std::vector<std::pair<QString, double>> m_includeFieldList;
    KDataFieldItemList m_itemList;
    bool m_bDescending;
};

enum KDateFieldTypeEnum
{
    DateFieldTypeYMD = 0,
    DateFieldTypeYM,
    DateFieldTypeMD,
    DateFieldTypeY,
    DateFieldTypeM,
    DateFieldTypeD,
    DateFieldTypeYMDH,
    DateFieldTypeYMDHM
};

class KDateField : public KStringField
{
  public:
    KDateField(int i, QJsonObject &jsonColumn, QString name, etoldapi::Range *pRange);
    KDateField(int i, QString name, KDateFieldTypeEnum dateType);
    virtual ~KDateField();
    virtual void AddDataFieldItem(QString name, long cnt);
    virtual void SortItemList(bool bDes = false);
    virtual void ExportJson(QJsonObject &jsonObj, int nMaxItemCnt) const;
    KDateFieldTypeEnum GetDateTypeEnum() const;
    virtual void ResetState();

  protected:
    QString CutString(QString strName);

  protected:
    KDateFieldTypeEnum m_nDateType; // 日期数据属于的类型
    KDataFieldItemList m_dateTypeItemList; // 不同日期数据，区分m_itemList（完整的年月日，对应筛选数据）
};

class KDataFieldList : public std::vector<KDataField *>
{
  public:
    KDataFieldList();
    virtual ~KDataFieldList();
    virtual void ClearAll();
    KDataField *FindByName(QString str);
    KDataField *FindByIndex(QString str);
    const KDataField *FindByName(QString str) const;
    const KDataField *FindByIndex(QString str) const;
    void SortByPredValue();
};

#define VecNameField std::vector<std::pair<QString, oldapi::ETConsolidationFunction>>
#define VecKDataField std::vector<const KDataField *>
class KAnalysisTable;
class KUnderstand;
class KAnalysisPivotTable
{
  public:
    KAnalysisPivotTable(ks_stdptr<etoldapi::_Worksheet> spSheet, ks_stdptr<etoldapi::PivotTable> spPivotTable,
                        KAnalysisTable *pTable);
    ~KAnalysisPivotTable();
    void Init();
    IKWorksheet *GetWorksheet();
    oldapi::ETPivotFieldOrientation GetPivotFieldOrientation(const KDataField *pField);
    KDataFieldList &GetAllDataField();
    etoldapi::PivotTable *GetApiPivotTable();
    void SetApiPivotTable(etoldapi::PivotTable *pPivotTable);
    HRESULT GetAnalysisData(QJsonObject &jsonObj);

    HRESULT InsertPivotTableField(const KDataField *pField, bool isPage, bool bSelect);
    HRESULT DeletePivotTableField(const KDataField *pField, bool bSelect);
    HRESULT ReplacePivotTableFields(const VecKDataField &vecFields, bool bSelect);
    HRESULT ReplacePivotTableFields(const QStringList &qslRow, const QStringList &qslCol, const VecNameField &vecData);

    HRESULT DeleteApiPivotTable();

    BOOL IsUsedField(QString strField);
    BOOL IsUsedPageField(QString strField);

    HRESULT Refresh();
    BOOL IsEmptyAnalysis();
    BOOL HasDimension();
    HRESULT SetDataFieldFunc(const KDataField *pField, oldapi::ETConsolidationFunction nFunc);

    void GetPivotTableFields(QStringList &qslRowFields, QStringList &qslColFields, QStringList &qslDataFields);
    ISmartUnderstand *GetUnderstand();
    void CleanUnderstand();
    BOOL HasUnderstand();

    void Reset(BOOL *pReload);

    // 是否推荐排序
    BOOL GetIsRecommend();
    void SetIsRecommend(BOOL isRecommend);
    // 是否仅显示正在分析的字段
    BOOL GetIsOnlyUsed();
    void SetIsOnlyUsed(BOOL isOnlyUsed);
    // 设置查找的字段名称
    QString GetFindField();
    void SetFindField(QString strName);

    int GetUsedNumberFieldTypeList(std::vector<std::pair<QString, QString>> &vecNumFieldType);
    QStringList GetInsertFieldList();

  private:
    void Reset();
    void ResetInsertList();
    HRESULT InitFields(oldapi::ETPivotFieldOrientation efo, std::vector<const KDataField *> &fieldList);
    HRESULT InitFields(oldapi::ETPivotFieldOrientation efo, std::vector<const KStringField *> &fieldList);
    HRESULT AddPageField(const KDataField *pField);
    HRESULT DeletePageField(const KDataField *pField);

    HRESULT GetAnalysisDataPivotTable(QJsonObject &jsonTable);
    HRESULT GetAnalysisDataPivotTableInfo(QJsonObject &jsonInfo);
    HRESULT GetPivotFieldsInfo(oldapi::ETPivotFieldOrientation efo, QJsonArray &jsonList);
    HRESULT GetPivotFieldInfo(etoldapi::PivotField *pField, QJsonObject &jsonInfo);

    HRESULT ClearApiPivotTable();
    HRESULT ClearFields(oldapi::ETPivotFieldOrientation efo);
    void GetAllFields(VecKDataField &vecFields);
    HRESULT SetPivotTableFields(const VecKDataField &vecFields);
    void TransPivotTableSruct(const VecKDataField &vecFields, VecKDataField &vecRowFields, VecKDataField &vecColFields,
                              VecKDataField &vecDataFields);
    HRESULT ReplacePivotTableFields(const VecKDataField &vecRowFields, const VecKDataField &vecColFields,
                                    const VecKDataField &vecDataFields);

  private:
    ks_stdptr<etoldapi::_Workbook> m_spBook;
    ks_stdptr<etoldapi::_Worksheet> m_spSheet;
    KAnalysisTable *m_pAnalysisTable;
    ks_stdptr<etoldapi::PivotTable> m_spPivotTable;
    std::vector<const KStringField *> rowFieldList;
    std::vector<const KStringField *> colFieldList;
    std::vector<const KDataField *> dataFieldList;
    std::vector<const KDataField *> pageFieldList;
    BOOL m_bInit;
    KUnderstand *m_pUnderstand;
    BOOL m_isRecommend;
    BOOL m_isOnlyUsed;
    QString m_strFindField;
    VecKDataField m_vecInsertFieldList; // 记录添加顺序，目前仅于图谱使用
};

class KFieldMap;
class KSmartAnalysis;
class KRecommendUnderstand;
class KAnalysisTable
{
  public:
    KAnalysisTable(etoldapi::_Workbook *pBook);
    ~KAnalysisTable();

    HRESULT InsertTable(int iSheet, QString strSourceSheetName, QJsonObject &jsonObj, ISmartAnalysisListener *);
    HRESULT InsertPivotTable();
    HRESULT InitAllDataField(QJsonObject &jsonObj);

    void SetSource(IKWorksheet *pSheet, etoldapi::Range *pRange);
    IKWorksheet *GetSourceSheet();
    etoldapi::Range *GetSourceRange();

    int GetAnalysisPivotTableCount();
    KAnalysisPivotTable *GetAnalysisPivotTable(int idx);

    KDataFieldList &GetAllDataField();
    const KDataField *GetDataField(QString strName) const;
    KDataField *GetDataField(QString strName);

    HRESULT AddUserPivotTable(etoldapi::PivotTable *pPivotTable);

    HRESULT GetFilterInfo(const KDataField *pField, QString strItem, QJsonArray &jsonObj);
    HRESULT GetFilterInfoNumber(QString strField, oldapi::ETConsolidationFunction type);

    KFieldMap *GetFieldMap();

    HRESULT UpdateOnDeleteSheet(IKWorksheet *pDelSheet);
    long GetFieldRowCount() const;
    long GetNotEmptyCellCount() const;
    // 是否推荐排序
    BOOL GetIsRecommend();
    void SetIsRecommend(BOOL isRecommend);
    ISmartUnderstand *GetRecommendUnderstand();
    QString GetLabel() const;

  private:
    HRESULT InitFieldItem();
    HRESULT InitFieldInfo();
    HRESULT GetFilterInfo(const KDataField *pField, QString strItem, IKAutoFilter *pAutoFilter, QJsonArray &jsonObj);

  private:
    ks_stdptr<IKWorksheet> m_spSrcSheet;
    ks_stdptr<etoldapi::Range> m_spSrcRange;
    ks_stdptr<etoldapi::_Workbook> m_spBook;
    KDataFieldList m_allFieldList;
    std::vector<KAnalysisPivotTable *> m_vecPivotTable;
    KFieldMap *m_pFieldMap;
    long m_nFieldRowCount;
    long m_nNotEmptyCellCount;
    BOOL m_isRecommend;
    KRecommendUnderstand *m_pRecommendUnderstand;
    QString m_strLabel;
};

// ----------------------------------------------------------------------
// 图谱模块

class KFieldMapField
{
  public:
    KFieldMapField(const KDataField *pField, double predValue, QString strType);
    virtual ~KFieldMapField()
    {
    }
    const KDataField *GetField() const;
    double GetPredValue() const;
    QString GetTyle() const;
    virtual HRESULT GetJsonObj(QJsonObject &jsonObj) const;

  protected:
    const KDataField *m_pField;
    double m_dPredValue; // 图谱的predValue不等同于字段列表里的predValue
    QString m_strType;
};

class KFieldMapNumField : public KFieldMapField
{
  public:
    KFieldMapNumField(const KNumberField *pField, double predValue, QString strType);
    virtual ~KFieldMapNumField()
    {
    }
    virtual HRESULT GetJsonObj(QJsonObject &jsonObj) const;
};

struct KFieldMapListItem
{
  public:
    KFieldMapListItem(const KStringField *p, double d, int des) : pField(p), dPredValue(d), nDirection(des)
    {
    }
    KFieldMapListItem(const KStringField *p, double d) : pField(p), dPredValue(d), nDirection(0)
    {
    }
    const KStringField *pField;
    double dPredValue;
    int nDirection;
};

class KFieldMapStrField : public KFieldMapField
{
  public:
    KFieldMapStrField(const KStringField *pField, double predValue, QString strType);
    virtual ~KFieldMapStrField()
    {
    }
    void AddRelationField(const KStringField *pField, double dPredValue);
    void AddIncludeField(const KStringField *pField, double dPredValue, int nDirection);
    virtual HRESULT GetJsonObj(QJsonObject &jsonObj) const;

  protected:
    std::vector<KFieldMapListItem> m_relationList;
    std::vector<KFieldMapListItem> m_includeList;
};

class KFieldMapDataFieldList : public std::vector<KFieldMapField *>
{
  public:
    KFieldMapDataFieldList()
    {
    }
    virtual ~KFieldMapDataFieldList();
};

class KFieldMap
{
  public:
    KFieldMap(QJsonObject &jsonObj, const KDataFieldList &allFieldList);
    virtual ~KFieldMap()
    {
    }
    HRESULT GetFieldMapJsonObj(QJsonObject &jsonObj) const;
    HRESULT GetFieldMapInfoJsonObj(QString strField, QJsonObject &jsonObj) const;
    QString GetFieldType(QString strField) const;

  private:
    void Init(QJsonObject &jsonObj, const KDataFieldList &allFieldList);
    const KFieldMapField *FindField(QString strField, QString &strType) const;

  private:
    QString m_strView;
    std::map<QString, KFieldMapDataFieldList> m_mapField;
};

// ----------------------------------------------------------------------
// 解读模块
class KChartXValues
{
  public:
    KChartXValues(QString strName);
    QString GetName() const;
    const QStringList &GetValues() const;
    void AddValues(QString strValue);
    HRESULT ExportJson(QJsonObject &jsonObj) const;

  private:
    QString m_strName;
    QStringList m_qslValues;
};

class KChartYValues
{
  public:
    KChartYValues(QString strName);
    QString GetName() const;
    const std::vector<double> &GetValues() const;
    void AddValues(double dValue);
    HRESULT ExportJson(QJsonObject &jsonObj) const;

  private:
    QString m_strName;
    std::vector<double> m_vecValues;
};

enum KUnderstandChartTypeEnum
{
    UnderstandChartWebExtension = 0,     //扩展类型
    UnderstandChartTypeBar = 51,         // xlColumnClustered 柱形图
    UnderstandChartTypeRowBar = 57,      // xlBarClustered 条形图
    UnderstandChartTypeLine = 65,        // xlLineMarkers 折线图
    UnderstandChartTypePie = 5,          // xlPie 饼图
    UnderstandChartTypeRadar = -4151,    // xlRadar 雷达图
    UnderstandChartTypeDoughnut = -4120, // xlDoughnut 环形图
    UnderstandChartTypePoint = -4169     // xlXYScatter 散点图
};

class KUnderstandChart
{
  public:
    KUnderstandChart(IKWorksheet *pSheet, QJsonObject &jsonObj);
    HRESULT InsertChart(IKWorksheet *pSheet, QString strType, RANGE &rg, const QString &strTitle, bool bSelect);
    HRESULT ExportJson(QJsonObject &jsonObj) const;
    QString GetChartType() const;
    void ChangeChartType(const QString &strChartType);
    bool IsMoreXValues();

  private:
    void Init(QJsonObject &jsonObj);
    HRESULT InsertChartShape(IKWorksheet *pSheet, QString strType, RANGE &rg, const QString &strTitle, bool bSelect);

  private:
    ks_stdptr<IKWorksheet> m_spSheet;
    KUnderstandChartTypeEnum m_chartType;
    QString m_chartTypeName;
    QString m_curChartType;
    QString m_strFormat;
    std::vector<KChartXValues> m_XValuesList;
    std::vector<KChartYValues> m_YValuesList;
};

struct KReportConclusion
{
  public:
    KReportConclusion()
    {
    }
    void Init(const QJsonObject &jsonObj);
    HRESULT ExportJson(QJsonObject &jsonObj) const;

  public:
    std::vector<QString> vecChartType;
    QString strConclusion;
    QString strAnalysisType;
};

class KReportPart
{
public:
  KReportPart(int i, IKWorksheet *pSheet, QJsonObject &jsonObj);
  KReportPart(int i, IKWorksheet *pSheet);
  virtual ~KReportPart();
  QString GetChartName() const;
  QString GetChartType() const;
  HRESULT ChangeChartType(const QString &strChartType);
  QString GetAnalysisType(const QString &strChartType) const;
  HRESULT GetTableData(QJsonArray &jsonObj) const;
  HRESULT InsertReport(QString strType, QString strInsertType, bool isNewSheet, QString &strSheetName, bool bSelect);
  virtual HRESULT ExportJson(QJsonObject &jsonObj, const QString &strType = QString()) const;
  virtual void SetLike(bool b)
  {
  }
  QStringList GetNameList();
  RANGE GetRealInsertTableRange(const RANGE &range);

public:
  virtual void Init(QJsonObject &jsonObj);
  HRESULT GetShapeOutRange(IKWorksheet *pSheet, RANGE &rg);
  HRESULT GetInsertRange(IKWorksheet *pSheet, RANGE &rg);
  HRESULT InsertTitle(IKWorksheet *pSheet, const QString &strType, RANGE &rg, bool bSelect);
  HRESULT InsertConclusion(IKWorksheet *pSheet, const QString &strType, RANGE &rg, bool isColAutoFit, bool bSelect);
  HRESULT InsertTable(IKWorksheet *pSheet, RANGE &rg, bool isColAutoFit, bool isRowAutoFit, bool isWrapText,
                      bool bSelect);
  HRESULT InsertChart(IKWorksheet *pSheet, QString strType, RANGE &rg, bool bTitle, bool bSelect);
  HRESULT InsertWebShapeChart(IKWorksheet *pSheet, QString strType, RANGE &rg, bool bTitle, bool bSelect);
  QString GetConclusion(const QString &strChartType);

protected:
  ks_stdptr<IKWorksheet> m_spSheet;
  QString m_strChartName;
  QString m_strConclusion;
  QStringList m_qslViews;
  std::vector<QStringList> m_tableData;
  KUnderstandChart *m_pChart;
  QString m_strAnalysisType;
  QStringList m_qslNameList;
  std::vector<KReportConclusion> m_vecConclusion;
};

class KUnderstand : public std::vector<KReportPart *>, public ISmartUnderstand
{
  public:
    KUnderstand(IKWorksheet *pSheet, QJsonArray &jsonObj);
    KUnderstand(IKWorksheet *pSheet);
    virtual ~KUnderstand();
    virtual void ClearAll();

    // 接受服务器返回的解读结果Json数据
    virtual HRESULT ParseUnderstandJsonObj(IKWorksheet *pSheet, QJsonArray &jsonObj, QJsonArray &jsonReportList);
    // 获取解读结果Json数据
    virtual HRESULT ExportJsonObj(QJsonArray &jsonReportList);
    // 获取某图表的表格数据
    virtual HRESULT GetChartTableJsonObj(QString strChart, QJsonArray &jsonObj);
    // 将某解读片段插入文档
    virtual HRESULT InsertReport(QString strChart, QString strType, QString strInsertType, bool isNewSheet,
                                 QString &strSheetName, bool bSelect);
    // 当前是否有解读
    virtual BOOL IsEmpty();
    // 获取指定片段的解读报告
    virtual HRESULT GetReport(const QString &strType, const QString &chartName, QJsonObject &object) override;
    // 是否插入过解读片段
    virtual BOOL IsFirstInsertReport() const override;
    // 获取解读个数
    virtual INT GetCount() const override;
    // 获取解读名称
    virtual QString GetReportName(int idx) const override;
    // 获取解读信息
    virtual int GetReportInfo(const QString &strChartName, QString &strChartType,
                              QString &strAnalysisType) const override;
    // 设置某解读片段点赞状态
    virtual HRESULT SetReportLike(const QString &strChart, bool b) override;
    // 判断是否已经返回解读结果
    virtual BOOL IsReady() const override;
    // 设置空白分析页的解读
    virtual HRESULT SetIsReady() override;
    // 获取某个解读结果Json数据
    virtual HRESULT ExportReportJsonObj(const QString &strChartName, QJsonObject &jsonReport) const override;
    // 切换某个解读的图表类型
    virtual HRESULT ChangeChartType(const QString &strChartName, const QString &strChartType) override;
    // 获取当前分析的行字段、列字段、值字段名字列表
    virtual void GetRowColDataFields(const QString &strChartName, QStringList &qslRowFields, QStringList &qslColFields,
                                     QStringList &qslDataFields) const override;
    // 获取某解读片段的使用字段名称
    virtual QStringList GetNameList(QString strChart) const override;

  protected:
    void Init(QJsonArray &jsonObj);

  protected:
    ks_stdptr<IKWorksheet> m_spSheet;
    BOOL m_bIsFirstInsert;
    BOOL m_bReady;
};

// ----------------------------------------------------------------------
// 推荐解读模块
class KRecommendChart : public KReportPart
{
  public:
    KRecommendChart(int i, IKWorksheet *pSheet, QJsonObject &jsonObj);
    virtual ~KRecommendChart();
    HRESULT GetRowColDataFields(QStringList &qslRow, QStringList &qslCol, VecNameField &vecData) const;
    virtual HRESULT ExportJson(QJsonObject &jsonObj, const QString &strType = QString()) const;
    virtual void SetLike(bool b)
    {
        m_bLike = b;
    }

  protected:
    virtual void Init(QJsonObject &jsonObj);

  private:
    QStringList m_qslRowFields;
    QStringList m_qslColFields;
    VecNameField m_vecDataFields;
    bool m_bLike;
};

class KRecommendUnderstand : public KUnderstand
{
  public:
    KRecommendUnderstand(IKWorksheet *pSheet, QJsonArray &jsonObj);
    virtual ~KRecommendUnderstand();
    const KRecommendChart *GetRecommend(QString strName) const;
    // 获取当前分析的行字段、列字段、值字段名字列表
    virtual void GetRowColDataFields(const QString &strChartName, QStringList &qslRowFields, QStringList &qslColFields,
                                     QStringList &qslDataFields) const override;

  protected:
    void Init(QJsonArray &jsonObj);
};

// ----------------------------------------------------------------------
// 功能接口
class KSmartAnalysis : public ISmartAnalysis
{
  public:
    KSmartAnalysis(IKWorksheet *pSheet);
    virtual ~KSmartAnalysis();

    // 获取发给服务器分析的Json数据
    virtual BOOL GetIdentifyData(QJsonObject &jsonObj);

    // 接收服务端返回的Json数据
    virtual HRESULT ParseJsonObj(QJsonArray &jsonObj);
    // 是否有分析数据透视表
    virtual BOOL HaveAnalysisPivotTable();
    // 切换当前选择的分析
    virtual HRESULT SelectAnalysis(IKWorksheet *pWorksheet);
    // 添加空白的分析
    virtual HRESULT NewAnalysis();
    // 刷新数据透视表
    virtual HRESULT Refresh();
    // 是否插入清洗数据
    virtual BOOL IsInsertCleanTable(int *pEnum);
    // 是否选择数据透视表
    virtual BOOL IsSelectionPivotTable();

    // 获取字段列表
    virtual HRESULT GetFieldList(QJsonArray &jsonObj);
    // 获取某个字段完整信息
    virtual HRESULT GetField(QString strField, QJsonObject &jsonObj);
    // 获取使用到的字段列表
    virtual QStringList GetUsedFieldList();
    // 获取使用到的筛选器字段列表
    virtual QStringList GetUsedPageFieldList();
    // 获取当前分析的行字段、列字段、值字段名字列表
    virtual void GetPivotTableFields(QStringList &qslRowFields, QStringList &qslColFields, QStringList &qslDataFields);
    // 获取所有字段列表名字
    virtual QStringList GetAllFieldList();
    // 获取按添加顺序的使用到的字段列表，目前仅图谱使用
    virtual QStringList GetInsertFieldList() override;
    // 添加字段
    virtual HRESULT InsertField(QString strField, bool isPage, bool bSelect = true, bool bCleanUR = false);
    // 删除字段
    virtual HRESULT DeleteField(QString strField, bool bSelect = true, bool bCleanUR = false);
    // 将数据透视表已使用字段清空后，加入需要的字段列表
    virtual HRESULT ReplaceFields(QStringList &qslFields, bool bSelect = true, bool bCleanUR = false) override;
    // 获取某字段项的筛选信息（联动）
    virtual HRESULT GetFilterInfo(QString strField, QString strItem, QJsonArray &jsonObj);
    //字符串类型字段按计数个数排序
    virtual HRESULT SortStringFieldCount(QString strField, bool bDes, QJsonObject &jsonObj);
    // 重置字段列表选择的状态，包括字符串字段改为降序，日期字段改为按日显示且为降序
    virtual HRESULT resetState();
    // 重新读取数据透视表使用到的行、列、值字段，用于更新使用到的字段，pReload为TRUE表示有变化，需要刷新
    virtual HRESULT ResetPivotTable(BOOL *pReload);

    // 获取字段图谱数据
    virtual HRESULT GetFieldMapJsonObj(QJsonObject &jsonObj);
    // 获取字段图谱某个字段详细信息
    virtual HRESULT GetFieldMapInfoJsonObj(QString strField, QJsonObject &jsonObj);
    // 进入图谱新建图谱专用的分析工作表
    virtual HRESULT InsertFieldMapPivotTable() override;
    // 退出图谱删除图谱专用的分析工作表
    virtual HRESULT DeleteFieldMapPivotTable() override;
    // 获取图谱工作表
    virtual IKWorksheet *GetFieldMapWorksheet(bool bDel = false) override;

    // 获取当前分析的数据，用于给服务端进行解读
    virtual HRESULT GetActiveAnalysisData(QJsonObject &jsonObj);
    // 当前分析是否为空白分析
    virtual BOOL IsEmptyAnalysis();
    // 当前分析是否有维度，即行字段或者列字段
    virtual BOOL HasDimension();
    // 数据是否已准备好
    virtual BOOL IsReady();
    // 获取字段个数
    virtual INT GetFieldCount();
    // 获取源数据行数
    virtual INT GetFieldRowCount();
    // 获取源数据非空单元格数
    virtual INT GetNotEmptyCellCount() const;
    // 获取源数据sheet服务端返回的表格个数
    virtual INT GetCleanTableCount() const;
    // 获取当前分析的Worksheet
    virtual IKWorksheet *GetActiveAnalysisWorksheet();
    // 获取源数据的Worksheet
    virtual IKWorksheet *GetSourceWorksheet();
    // 获取创建智能分析的Worksheet
    virtual IKWorksheet *GetNewAnalysisWorksheet();
    // 当前分析是否标脏
    virtual BOOL IsActiveAnalysisDirty();
    // 设置当前分析标脏
    virtual HRESULT SetActiveAnalysisDirty();
    // 当删除工作表时，检查更新我的分析列表和当前分析
    virtual HRESULT UpdateOnDeleteSheet(IKWorksheet *pDelSheet);

    // 解读插入图表时调用记录状态
    virtual void SetInsertChart(BOOL b);
    virtual BOOL GetInsertChart() const;

    virtual BOOL GetIsNewSheet() const;
    virtual void SetIsNewSheet(BOOL b);

    // 获取解读接口
    virtual ISmartUnderstand *GetUnderstand();
    // 清除当前分析的解读数据
    virtual void CleanUnderstand();
    // 是否已生成解读数据
    virtual BOOL HasUnderstand();
    // 获取某个字段的类型信息
    virtual HRESULT GetFieldTypeInfo(QString strField, QString &strType, QString &strFieldMapType);

    // 是否推荐排序
    virtual BOOL GetIsRecommend(bool isSource);
    virtual void SetIsRecommend(BOOL isRecommend, bool isSource);
    // 是否仅显示正在分析的字段
    virtual BOOL GetIsOnlyUsed();
    virtual void SetIsOnlyUsed(BOOL isOnlyUsed);
    // 设置查找的字段名称
    virtual QString GetFindField();
    virtual void SetFindField(QString strName);
    // 获取数据解析错误信息
    virtual KParseJsonErrorEnum GetParseJsonError();
    virtual void SetParseJsonError(KParseJsonErrorEnum err);
    // 获取使用的数值字段的数值计算类型
    virtual int GetUsedNumberFieldTypeList(std::vector<std::pair<QString, QString>> &vecNumFieldType);
    virtual void addListener(ISmartAnalysisListener *listener) override;
    virtual void removeListener(ISmartAnalysisListener *) override;
    // 获取某字段项数
    virtual INT GetFieldItemCount(QString strName) const;
    virtual BOOL GetFieldMapIsDisplay() override;
    virtual void SetFieldMapIsDisplay(BOOL b) override;
    // 是否首次打开数据源
    virtual void SetIsFirstSource(BOOL b) override;
    virtual BOOL GetIsFirstSource() const override;
    // 是否首次打开图谱
    virtual void SetIsFirstDispalyFieldMap(BOOL b) override;
    virtual BOOL GetIsFirstDispalyFieldMap() const override;
    // 插入数据是否中断
    virtual void SetIsStop(BOOL b) override;
    virtual BOOL GetIsStop() const override;
    // 获取某个字段在所有字段的推荐排序
    virtual INT GetFieldPredValueIndex(QString strName) const override;
    // 获取和设置字段列表是否打开
    virtual BOOL GetFieldListIsOpen() override;
    virtual void SetFieldListIsOpen(BOOL b) override;
    // 检查当前数据透视表对象是否有效，如无效则更新数据透视表对象
    virtual BOOL ResetApiPivotTable() override;
    // 是否当前分析不发送解读
    virtual BOOL GetIsNotRequestUnderstand() override;
    // 获取推荐解读接口
    virtual ISmartUnderstand *GetRecommendUnderstand() override;
    // 将推荐解读片段插入分析方案
    virtual HRESULT InsertRecommendAnalysis(QString strName) override;
    // 是否已发送识别请求
    virtual BOOL GetDoRecognize() override;
    // 设置已发送识别请求
    virtual void SetDoRecognize(BOOL) override;
    // 获取数据源场景分类结果
    virtual QString GetLabel() const override;

    int GetPivotTableCount();
    IKWorksheet *GetPivotTableSheet(int i);

  private:
    void Init();
    void InitPivotTable(etoldapi::PivotTable *pPivotTable);
    int GetSelectTableJsonIdx(QJsonArray &jsonArray);
    HRESULT GetPivotTableSourceRange(etoldapi::PivotTable *pPivotTable, RANGE &rg);
    int PivotTableListByRange(const RANGE *pTableRange);
    BOOL NeedCleanTable(QJsonObject &jsonTable, RANGE &rg, etoldapi::Range **ppRange);
    BOOL NeedCleanRealTableRange(QJsonObject jsonCleanTable, RANGE &rg);
    void CheckSheetIsActive();
    void clearListenner();
    bool FindPivotTable(etoldapi::PivotTable **ppPivotTable);

  private:
    ks_stdptr<IET_PivotTableTool> m_spPivotTableTool;
    ks_stdptr<IKWorksheet> m_spActiveSheet;               // 当前Sheet
    ks_stdptr<etoldapi::PivotTable> m_spSelectPivotTable; // 当前选择的数据透视表
    ks_stdptr<IKWorksheet> m_spSrcSheet;
    ks_stdptr<etoldapi::Range> m_spSrcRange;
    ks_stdptr<ISmartAnalysisListener> m_spListener;
    BOOL m_bCleanTable;
    BOOL m_bSelectPivotTable;
    BOOL m_bNewSheet;
    BOOL m_bInsertChart;
    KAnalysisTable *m_pAnalysis;
    KAnalysisPivotTable *m_pActivePivotTable;
    BOOL m_bNewActive; // 是否刚刚更新当前分析，用于判断是否刷新使用字段
    int m_nCleanTableCount;
    KParseJsonErrorEnum m_error;
    BOOL m_bDisplayFieldMap;
    BOOL m_bIsFirstSource;
    BOOL m_bIsFirstDispalyFieldMap;
    BOOL m_bIsStop;
    BOOL m_bFieldListOpen;
    BOOL m_bIsNotRequestUnderstand;
    KAnalysisPivotTable *m_pFieldMapPivotTable;
    ks_stdptr<IKWorksheet> m_spOldActiveSheet; // 进入图谱状态前的激活工作表，可能是分析页、数据源
    int m_nCleanEnum = 0;
    BOOL m_bDoRecognize = FALSE;
};

class KSmartAnalysisManager : public ISmartAnalysisManager
{
  public:
    explicit KSmartAnalysisManager(IKWorksheet *pSheet);
    virtual ~KSmartAnalysisManager();
    // 获取当前数据源
    virtual ISmartAnalysis *GetActiveSmartAnalysis();
    virtual void SetActiveSmartAnalysis(ISmartAnalysis *pAnalysis) override;
    // 设置当前数据源
    virtual HRESULT SelectAnalysis(IKWorksheet *pSheet);
    // 新增数据源
    virtual HRESULT AddAnalysis(IKWorksheet *pSheet);
    // 删除数据源
    virtual HRESULT DeleteAnalysis(ISmartAnalysis *pAnalysis);
    virtual HRESULT DeleteAnalysis(IKWorksheet *pSheet);
    virtual HRESULT DeleteAnalysis(IKWorkbook *pBook);
    // 查找数据源
    virtual ISmartAnalysis *GetSmartAnalysis(IKWorksheet *pSheet) override;
    // 判断当前工作表是否是分析页
    virtual BOOL IsAnalysisPivotTable(IKWorksheet *pSheet);
    // 判断当前工作表是否是数据源页
    virtual BOOL IsAnalysisPivotTableSource(IKWorksheet *pSheet);
    // 获取当前工作簿的分析数据源个数
    virtual uint32 GetAnalysisCount(IKWorkbook *pBook) override;
    // 判断该分析是否有效
    virtual BOOL IsValidAnalysis(ISmartAnalysis *pAnalysis) override;
    // 清除所有数据
    virtual HRESULT ClearAll() override;

  private:
    ISmartAnalysis *FindAnalysisBySheet(IKWorksheet *pSheet);

  private:
    std::vector<ISmartAnalysis *> m_vecAnalysis;
    ISmartAnalysis *m_pActiveAnalysis = nullptr;
};
} // namespace etai

#endif /* __DATAANALYSIS_H__ */