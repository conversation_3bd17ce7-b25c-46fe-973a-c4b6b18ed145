﻿#include "etstdafx.h"
#include "etmodules.h"
#include "etshareapplication.h"

namespace wo
{
namespace {

	void _checkError(bool b, QString s)
	{
		if (!b)
		{
#if QT_VERSION < QT_VERSION_CHECK(5, 0, 0)
			QMessageBox::critical(NULL, "wps", s);
#else
			qFatal("%s", (char *)s.data());
#endif
			exit(1);
		}
	}

} //  namespace

// -----------------------------------------------------------------------

KxKsoModule::KxKsoModule()
	: QLibrary("kso")
{
	load();
	_checkError(isLoaded(), errorString());

	m_funcInitialize =
		(_func_Initialize)resolve("_kso_Initialize");
	_checkError(m_funcInitialize, errorString());

	m_funcQueryFeatureState =
		(_func_QueryFeatureState)resolve("_kso_QueryFeatureState");
	_checkError(m_funcQueryFeatureState, errorString());

	m_funcGetDirInfo =
		(_func_GetDirInfo)resolve("_kso_GetDirInfo");
	_checkError(m_funcGetDirInfo, errorString());

	m_funcGetUserDirInfo =
		(_func_GetUserDirInfo)resolve("_kso_GetUserDirInfo");
	_checkError(m_funcGetUserDirInfo, errorString());

	m_funcRegSetValueI =
		(_func_RegSetValueI)resolve("_kso_RegSetValueI");
	_checkError(m_funcRegSetValueI, errorString());

	m_funcRegQueryValueI =
		(_func_RegQueryValueI)resolve("_kso_RegQueryValueI");
	_checkError(m_funcRegQueryValueI, errorString());

	m_funcGetUserInformation =
		(_func_GetUserInformation)resolve("_kso_GetUserInformation");
	_checkError(m_funcGetUserInformation, errorString());

	m_fun_GetProductInfo =
		(_func_GetProductInfo)resolve("_kso_GetProductInfo");
	_checkError(m_fun_GetProductInfo, errorString());

	m_fun_RegQueryValue =
		(_func_RegQueryValue)resolve("_kso_RegQueryValue");
	_checkError(m_fun_RegQueryValue, errorString());

	m_fun_GetOEMText2 =
		(_func_GetOEMText2)resolve("_kso_GetOEMText2");
	_checkError(m_fun_GetOEMText2, errorString());

	m_GetNormalOEMValue=
		(_func_GetNormalOEMValue)resolve("_kso_Get_NormalOEMValue");
	_checkError(m_GetNormalOEMValue, errorString());

#ifdef X_OS_WINDOWS
	m_funcKFT_CreateLibrary =
		(_func_KFT_CreateLibrary)resolve("_kso_KFT_CreateLibrary");
	_checkError(m_funcKFT_CreateLibrary, errorString());

	m_funcKFT_DestroyLibrary =
		(_func_KFT_DestroyLibrary)resolve("_kso_KFT_DestroyLibrary");
	_checkError(m_funcKFT_DestroyLibrary, errorString());

	m_funcSendInfoCellect =
		(_func_SendInfoCellect)resolve("_kso_SendInfoCellect");
	_checkError(m_funcSendInfoCellect, errorString());
#endif

	m_funcRegSetValue =
		(_func_funcRegSetValue)resolve("_kso_RegSetValue");
	_checkError(m_funcRegSetValue, errorString());

	m_funcOpenHelp =
		(_func_OpenHelp)resolve("_kso_OpenHelp");
	_checkError(m_funcOpenHelp, errorString());
}

HRESULT KxKsoModule::Initialize()
{
	return m_funcInitialize();
}

HRESULT KxKsoModule::InitlizeFeatureStates()
{
	return m_funcInitlizeFeatureStates();
}

int KxKsoModule::QueryFeatureState( int nFeatureID )
{
	return m_funcQueryFeatureState(nFeatureID);
}

QString KxKsoModule::GetDirInfo( ksoDirEnum nDir, int nLanguage /*= KSO_LANGUAGE_AUTO*/, bool bFull /*= true*/ )
{
	WCHAR* buff = new WCHAR[MAX_PATH];
	HRESULT hr = m_funcGetDirInfo(nDir, nLanguage, bFull, buff, MAX_PATH, true);
	QString ret;
	if (SUCCEEDED(hr))
		ret = QString::fromUtf16(buff);
	delete[] buff;
	return ret;
}

QString KxKsoModule::GetUserDirInfo(ksoUserDirEnum nDir, int balluser /*= false*/)
{
	WCHAR* buff = new WCHAR[MAX_PATH];
	HRESULT hr = m_funcGetUserDirInfo(nDir, balluser, buff, MAX_PATH);
	QString ret;
	if (SUCCEEDED(hr))
		ret = QString::fromUtf16(buff);
	delete[] buff;
	return ret;
}

HRESULT KxKsoModule::RegSetValueI(Kso_Reg_Type type, LPCWSTR lpcwSubKey, LPCWSTR lpcwValName, int nVal)
{
	return m_funcRegSetValueI(type, lpcwSubKey, lpcwValName, nVal);
}

HRESULT KxKsoModule::RegSetValue(Kso_Reg_Type type, LPCWSTR lpcwSubKey, LPCWSTR lpcwValName, LPCWSTR lpcwValue)
{
	return m_funcRegSetValue(type, lpcwSubKey, lpcwValName, lpcwValue);
}

int KxKsoModule::RegQueryValueI( Kso_Reg_Type type, LPCWSTR lpcwSubKey, LPCWSTR lpValName, int nDefVal /*= 0*/, HRESULT* phr /*= NULL*/ )
{
	return m_funcRegQueryValueI(type, lpcwSubKey, lpValName, nDefVal, phr);
}

HRESULT KxKsoModule::RegQueryValue(Kso_Reg_Type type,
	LPCWSTR lpcwSubKey,
	LPCWSTR lpValName,
	BSTR* pVal,
	LPCWSTR lpDefVal)
{
	return m_fun_RegQueryValue(type, lpcwSubKey, lpValName, pVal, lpDefVal);
}

HRESULT KxKsoModule::RegQueryValueEx(Kso_Reg_Type type,
	LPCWSTR lpcwSubKey,
	LPCWSTR lpValName,
	BSTR* pVal,
	LPCWSTR lpDefVal)
{
	return m_fun_RegQueryValueEx(type, lpcwSubKey, lpValName, pVal, lpDefVal);
}

HRESULT KxKsoModule::RegQueryValueExI( Kso_Reg_Type type, LPCWSTR lpcwSubKey,
	LPCWSTR lpValName, int nDefVal /*= 0*/,
	HRESULT* phr /*= NULL*/ )
{
	return m_fun_RegQueryValueExI(type, lpcwSubKey, lpValName, nDefVal, phr);
}

HRESULT KxKsoModule::GetUserInformation(IUserInformation** ppInfo)
{
	return m_funcGetUserInformation(ppInfo);
}

LPCWSTR KxKsoModule::GetProductInfo(KSO_PRODUCTINFO nInfoType)
{
	return m_fun_GetProductInfo(nInfoType);
}

int KxKsoModule::GetVersionType()
{
	return m_fun_GetVersionType();
}

HRESULT KxKsoModule::GetOEMText2( BSTR keyName, BSTR attribName, bool bIsPath, BSTR* pVal )
{
	return m_fun_GetOEMText2(keyName, attribName, bIsPath, pVal);
}

HRESULT KxKsoModule::GetNormalOEMValue(IN BSTR bszSectionName,
	IN BSTR bszKeyName,
	OUT BSTR* bszKeyValue)
{
	return m_GetNormalOEMValue(bszSectionName, bszKeyName, bszKeyValue);
}

long KxKsoModule::GetCurrentLanguage()
{
	long lng;
	m_funcGetCurrentLanguage(&lng);
	return lng;
}

#ifdef X_OS_WINDOWS
struct KFT_Library * KxKsoModule::KFT_CreateLibrary()
{
	struct KFT_Library * pl;
	m_funcKFT_CreateLibrary(&pl);
	return pl;
}

HRESULT KxKsoModule::KFT_DestoryLibrary()
{
	return m_funcKFT_DestroyLibrary();
}

HRESULT KxKsoModule::SendInfoCellect( int nType )
{
	return m_funcSendInfoCellect(nType);
}
#endif

HRESULT KxKsoModule::OpenHelp(int emHelpFile, BSTR bszSubAddress /* = NULL */)
{
	return m_funcOpenHelp(emHelpFile, bszSubAddress);
}
}
