﻿#include "etstdafx.h"
#include "kcoremetrics.h"
#include "webbase/binvariant/binwriter.h"
#include "collect.h"
#include "woetsetting.h"
#include "workbook.h"

namespace wo
{

template<typename MetricType>
struct KCollectMetrics
{
    const char * m_key;
    MetricType m_type;
    int m_unitCvt;
    bool m_needReset;
    bool m_bSend;
};

using KCollectU32Metrics = KCollectMetrics<KU32MetricItem>;
using KCollect64Metrics = KCollectMetrics<K64MetricItem>;
using KCollectWStrMetrics = KCollectMetrics<KWStrMetricItem>;

template<typename MetricType>
struct KCollectMetricsX
{
    const WCHAR * m_key;
    MetricType m_type;
};
using KCollectMetricsType = KCollectMetricsX<KMetricsType>;

#define ITEM_U32(key, type, unit) KCollectU32Metrics{key, type, unit, true, true}
#define ITEM_64(key, type, unit, needReset) KCollect64Metrics{key, type, unit, needReset, true}
#define ITEM_64_NotSend(key, type, unit, needReset) KCollect64Metrics{key, type, unit, needReset, false}
#define ITEM_WSTR(key, type) KCollectWStrMetrics{key, type, 1, true, true}
#define ITEM_TYPE(key, type) KCollectMetricsType{key, type}

constexpr KCollectU32Metrics g_u32MetricKey[] = {
    ITEM_U32("line_his_time", KU32MetricItem::kLineHistoriesTime, 1),
    ITEM_U32("tasks_time", KU32MetricItem::kTasksTime, 1),
    ITEM_U32("undo_time", KU32MetricItem::kUndoTime, 1),
    ITEM_U32("redo_time", KU32MetricItem::kRedoTime, 1),
    ITEM_U32("init_time", KU32MetricItem::kInitTime, 1),
    ITEM_U32("cmds_time", KU32MetricItem::kCmdsTime, 1),
    ITEM_U32("fork_time", KU32MetricItem::kForkTime, 1000),
    ITEM_U32("fork_lock_time", KU32MetricItem::kForkLockTime, 1000),
    ITEM_U32("ser_versions_time", KU32MetricItem::kSerVersionsTime, 1),
    ITEM_U32("ser_init_time", KU32MetricItem::kSerInitTime, 1),
    ITEM_U32("transform_time", KU32MetricItem::kTransformTime, 1),
    ITEM_U32("end_trans_time", KU32MetricItem::kAppEndTransTime, 1),
    ITEM_U32("signal_time", KU32MetricItem::kSignalTime, 1),
    ITEM_U32("commit_time", KU32MetricItem::kCommitTime, 1),
    ITEM_U32("before_exec_time", KU32MetricItem::kBeforeExecTime, 1),
    ITEM_U32("exec_time", KU32MetricItem::kExecTime, 1),
    ITEM_U32("after_exec_time", KU32MetricItem::kAfterExecTime, 1),
    ITEM_U32("bpproc_time", KU32MetricItem::kBPProcTime, 1),
    ITEM_U32("respond_time", KU32MetricItem::kRespondTime, 1),
    ITEM_U32("respond_step1_time", KU32MetricItem::kRespondStep1Time, 1),
    ITEM_U32("respond_step1_kb", KU32MetricItem::kRespondStep1Bytes, 1024), // KB
    ITEM_U32("respond_step2_time", KU32MetricItem::kRespondStep2Time, 1),
    ITEM_U32("respond_step2_kb", KU32MetricItem::kRespondStep2Bytes, 1024), // KB
    ITEM_U32("respond_step3_time", KU32MetricItem::kRespondStep3Time, 1),
    ITEM_U32("respond_step3_kb", KU32MetricItem::kRespondStep3Bytes, 1024), // KB
    ITEM_U32("cmds_cnt", KU32MetricItem::kCmdsCnt, 1),
    ITEM_U32("fork_count", KU32MetricItem::kForkCount, 1),
    ITEM_U32("ser_versions", KU32MetricItem::kSerVersionCnt, 1),
    ITEM_U32("ser_versions_kb", KU32MetricItem::kSerVersBytes, 1024), // kb
    ITEM_U32("hit_versions", KU32MetricItem::kSerHitVersionCnt, 1),
    ITEM_U32("init_diff_versions", KU32MetricItem::kInitDiffVersCnt, 1),
    ITEM_U32("new_block_cnt", KU32MetricItem::kNewBlocksCnt, 1),
    ITEM_U32("bp_cnt", KU32MetricItem::kBPCnt, 1),
    ITEM_U32("fill_block_cnt", KU32MetricItem::kBPFillBlocksCnt, 1),
    ITEM_U32("fill_block_byte", KU32MetricItem::kBPFillBytes, 1024), // kb
    ITEM_U32("bpinit_time", KU32MetricItem::kBPInitTime, 1),
    ITEM_U32("bpinit_block_cnt", KU32MetricItem::kBPInitBlocksCnt, 1),
    ITEM_U32("bpinit_kb", KU32MetricItem::kBPInitBytes, 1024), // kb
    ITEM_U32("bpproc_kb", KU32MetricItem::kBPProcBytes, 1024), // kb
    ITEM_U32("bpcalc_time", KU32MetricItem::kBPCalcCellsTime, 1),
    ITEM_U32("bpcalc_kb", KU32MetricItem::kBPCalcCellsBytes, 1024), // kb
    ITEM_U32("bpcalc_isall", KU32MetricItem::kBPCalcCellsIsAll, 1),
    ITEM_U32("bpcalc_bp_cnt", KU32MetricItem::kBPCalcCellsBpCnt, 1),
    ITEM_U32("bpcalc_visiblecell_cnt", KU32MetricItem::kBPCalcCellsVisibleCellCnt, 1),
    ITEM_U32("bpcalc_allcell_cnt", KU32MetricItem::kBPCalcCellsAllCellCnt, 1),
    ITEM_U32("newcustomobj_time", KU32MetricItem::kNewCustomObjsTime, 1), //
    ITEM_U32("newcustomobj_kb", KU32MetricItem::kNewCustomObjsBytes, 1024), // kb
    ITEM_U32("newcustomobj_cnt", KU32MetricItem::kNewCustomObjsCnt, 1), //
    ITEM_U32("extstate_res_kb", KU32MetricItem::kExtStateResBytes, 1024), // kb
    ITEM_U32("after_exec_kb", KU32MetricItem::kAfterExecBytes, 1024), // kb
    ITEM_U32("serial_sheet_kb", KU32MetricItem::kSerialSheetBytes, 1024), // kb
    ITEM_U32("serial_sheet_cnt", KU32MetricItem::kSerialSheetCnt, 1), // 
    ITEM_U32("serial_delay_sheet_kb", KU32MetricItem::kSerialDelaySheetBytes, 1024), // kb
    ITEM_U32("serial_delay_sheet_cnt", KU32MetricItem::kSerialDelaySheetCnt, 1),
    ITEM_U32("serial_hidden_sheet_cnt", KU32MetricItem::kSerialHiddenSheetCnt, 1),
    ITEM_U32("serial_xfs_cnt", KU32MetricItem::kSerialXfsCnt, 1), // 
    ITEM_U32("serial_cmts_newobj_cnt", KU32MetricItem::kSerialCmtsNewsObjCnt, 1), // 
    ITEM_U32("serial_init_kb", KU32MetricItem::kSerialInitBytes, 1024), // kb
    ITEM_U32("serial_newobj_time", KU32MetricItem::kSerialNewObjTime, 1),
    ITEM_U32("serial_newobj_kb", KU32MetricItem::kSerialNewObjBytes, 1024), // kb
    ITEM_U32("serial_newobj_cnt", KU32MetricItem::kSerialNewObjCnt, 1),
    ITEM_U32("serial_versions_newobj_time", KU32MetricItem::kSerialVerNewObjTime, 1),
    ITEM_U32("serial_versions_newobj_kb", KU32MetricItem::kSerialVerNewObjBytes, 1024), // kb
    ITEM_U32("serial_versions_newobj_cnt", KU32MetricItem::kSerialVerNewObjCnt, 1),

    ITEM_U32("serial_orgobj_time", KU32MetricItem::kSerialOrgObjTime, 1),
    ITEM_U32("serial_orgobj_kb", KU32MetricItem::kSerialOrgObjBytes, 1024), // kb
    ITEM_U32("serial_orgobj_cnt", KU32MetricItem::kSerialOrgObjCnt, 1),
    ITEM_U32("serial_orgobj_orig_cnt", KU32MetricItem::kSerialOrgObjOrigCnt, 1),
    ITEM_U32("serial_onlyversion_time", KU32MetricItem::kSerialOnlyVersionTime, 1),
    ITEM_U32("serial_onlyversion_kb", KU32MetricItem::kSerialOnlyVersionBytes, 1024), // kb
    ITEM_U32("serial_onlyversion_cnt", KU32MetricItem::kSerialOnlyVersionCnt, 1),
	
    ITEM_U32("serial_querycmds_kb", KU32MetricItem::kSerialExQueryCmdsByte, 1024), // kb
    ITEM_U32("serial_shapepics_time", KU32MetricItem::kSerialShapePicsTime, 1),
    ITEM_U32("serial_shapepics_kb", KU32MetricItem::kSerialShapePicsBytes, 1024), // kb

    ITEM_U32("serial_pivottable_time", KU32MetricItem::kSerialPivotTableTime, 1),
    ITEM_U32("serial_pivottable_kb", KU32MetricItem::kSerialPivotTableBytes, 1024), // kb
    ITEM_U32("serial_pivottable_cnt", KU32MetricItem::kSerialPivotTableCnt, 1),

    ITEM_U32("serial_fmla_kb", KU32MetricItem::kSerialFmlaKb, 1024), // kb
    ITEM_U32("serial_fmla_allcell_cnt", KU32MetricItem::kSerialFmlAllCellCnt, 1),
    ITEM_U32("serial_fmla_visiblecell_cnt", KU32MetricItem::kSerialFmlVisibleCellCnt, 1),
	
    ITEM_U32("cmds_used_mem_kb", KU32MetricItem::kCmdMemUsedKb, 1), // kb
    ITEM_U32("cmds_begin_mem_kb", KU32MetricItem::kCmdBeginMemKb, 1), // kb
    ITEM_U32("used_mem_kb", KU32MetricItem::kMemUsedKb, 1), // kb
    ITEM_U32("begin_mem_kb", KU32MetricItem::kBeginMemKb, 1), // kb

    ITEM_U32("transform_cnt", KU32MetricItem::kTransformationCnt, 1), //
    ITEM_U32("task_transform_cnt", KU32MetricItem::kTaskTransformCnt, 1), //
    ITEM_U32("task_transform_time", KU32MetricItem::kTaskTransformTime, 1), //
    ITEM_U32("unredo_transform_cnt", KU32MetricItem::kUndoRedoTransformCnt, 1), //
    ITEM_U32("unredo_transform_time", KU32MetricItem::kUndoRedoTransformTime, 1), //

    ITEM_U32("ser_grid_kb", KU32MetricItem::kSerialGridKb, 1024), //
    ITEM_U32("ser_grid_block_cnt", KU32MetricItem::kSerialGridBlockCnt, 1), //
    ITEM_U32("ser_grid_emptycell_cnt", KU32MetricItem::kSerialGridEmptyCellCnt, 1), //
    ITEM_U32("ser_grid_solidcell_cnt", KU32MetricItem::kSerialGridSolidCellCnt, 1), //

    ITEM_U32("begin_init", KU32MetricItem::kBeginExecInitTime, 1),
    ITEM_U32("begin_tasks", KU32MetricItem::kBeginExecTasksTime, 1),
    ITEM_U32("begin_serial_vers", KU32MetricItem::kBeginSerialVersTime, 1),
};

constexpr KCollect64Metrics g_64MetricKey[] = {
    ITEM_64("all_count", K64MetricItem::kAllCout, 1, false),
    ITEM_64("all_time", K64MetricItem::kAllTime, 1000, false), // ms -> s
    ITEM_64("all_init_count", K64MetricItem::kAllInitCount, 1, false),
    ITEM_64("all_reinit_count", K64MetricItem::kAllReInitCnt, 1, false),
    ITEM_64("all_fork_count", K64MetricItem::kAllForkCount, 1, false),
    ITEM_64("all_fork_time", K64MetricItem::kAllForkTime, 1000 * 1000, false), // us -> s
    ITEM_64("all_fork_locktime", K64MetricItem::kAllForkLockTime, 1000 * 1000, false), // us -> s
    ITEM_64("all_fork_query_count", K64MetricItem::kAllForkQueryCnt, 1, false),
    ITEM_64("all_fork_query_time", K64MetricItem::kAllForkQueryTime, 1000, false), // ms -> s
    ITEM_64("signal_size", K64MetricItem::kSignalSize, 1024, true), // KB
    ITEM_64_NotSend("outoflimit_version_cnt", K64MetricItem::kAllOutOfLimitSerVerCnt, 1, false),
    ITEM_64_NotSend("all_transform_cnt", K64MetricItem::kAllTransformCnt, 1, false),
    ITEM_64_NotSend("all_task_transform_cnt", K64MetricItem::kAllTaskTransformCnt, 1, false),
    ITEM_64_NotSend("all_unredo_transform_cnt", K64MetricItem::kAllUndoRedoTransformCnt, 1, false),
};

constexpr KCollectWStrMetrics g_WStrMetricKey[] = {
    ITEM_WSTR("first_cmd", KWStrMetricItem::kFirstCmdName),
    ITEM_WSTR("first_cmd_id", KWStrMetricItem::kFirstCmdTraceId),
    ITEM_WSTR("signal_type", KWStrMetricItem::kSignalMsgType),
    ITEM_WSTR("ser_version_fail", KWStrMetricItem::kSerialVersionFailed),
};

KCollectMetricsType g_metricType[] = {
    ITEM_TYPE(__X("Query"), KMetricsType::kQuery),
    ITEM_TYPE(__X("Exec"), KMetricsType::kExec),
    ITEM_TYPE(__X("QueryInit"), KMetricsType::kQueryInit),
};

template <typename T, std::size_t N>
constexpr bool checkConstArray(const T (&arr)[N], int exptCnt)
{
    if (N != exptCnt)
        return false;

    int i = 0;
    for (const auto & item : arr)
    {
        if (i != (int)item.m_type)
            return false;
        ++i;
    }
    return true;
}

template <typename T, std::size_t N>
constexpr bool isSend(const KCollectMetrics<T> (&arr)[N], T itemEnum)
{
    return arr[(int)itemEnum].m_bSend;
}

template <typename T, std::size_t N>
constexpr const char * getKey(const KCollectMetrics<T> (&arr)[N], T itemEnum)
{
    return arr[(int)itemEnum].m_key;
}

template <typename T, std::size_t N>
constexpr int getUnitCvt(const KCollectMetrics<T> (&arr)[N], T itemEnum)
{
    return arr[(int)itemEnum].m_unitCvt;
}

template <typename T, std::size_t N>
constexpr bool needReset(const KCollectMetrics<T> (&arr)[N], T itemEnum)
{
    return arr[(int)itemEnum].m_needReset;
}

static_assert(checkConstArray(g_u32MetricKey, (int)KU32MetricItem::_kCount), "g_u32MetricKey和KU32MetricItem 数量或定义顺序不同");
static_assert(checkConstArray(g_64MetricKey, (int)K64MetricItem::_kCount), "g_64MetricKey和K64MetricItem 数量或定义顺序不同");
static_assert(checkConstArray(g_WStrMetricKey, (int)KWStrMetricItem::_kCount), "g_WStrMetricKey和KWStrMetricItem 数量或定义顺序不同");

KCoreMetric::KCoreMetric()
: m_forkNonLockTime(0)
, m_syncChildCount(0)
, m_syncChildTime(0)
, m_preInitTaskVers(0)
, m_execIdx(0)
, m_level(0)
, m_allForkNonLockTime(0)
, m_sampleRate(0)
, m_type(KMetricsType::kQueryInit)
{
	static_assert(sizeof(KCoreMetric::Flags) == sizeof(uint32_t), "Flags exceed uint32_t size!");
	m_flags.value = 0;
	if (_kso_GetWoEtSettings() && _kso_GetWoEtSettings()->IsSendCoreTime())
		m_flags.enable = 1;
	memset(m_64Metrics, 0, sizeof(m_64Metrics));
	resetData();
	
#ifdef _DEBUG
	int len = sizeof(g_metricType) / sizeof(g_metricType[0]);
	for (int i = 0; i < len; ++i)
	{
		if (g_metricType[i].m_type != (KMetricsType)i)
			WOLOG_ERROR << "g_metricsType 顺序不对。。。。";
	}
#endif	
}

void KCoreMetric::onBeginQuery()
{
	if (!m_flags.enable)
		return;
	m_flags.isQuery = 1;
	m_begin = std::chrono::steady_clock::now();
}

void KCoreMetric::onBeginExec()
{
	if (!m_flags.enable)
		return;
	++m_execIdx;
	m_flags.isExec = 1;
	m_begin = std::chrono::steady_clock::now();
}

void KCoreMetric::setU32Metrics(KU32MetricItem type, unsigned int val)
{
	if (m_level == 0) // beginCollect才收集
		return;

	assert((int)type >= 0 && (int)type < (int)KU32MetricItem::_kCount);
	_setU32Metrics(type, val);
}

void KCoreMetric::addU32Metrics(KU32MetricItem type, unsigned int val)
{
	if (m_level == 0) // beginCollect才收集
		return;

	assert((int)type >= 0 && (int)type < (int)KU32MetricItem::_kCount);
	_addU32Metrics(type, val);
}

void KCoreMetric::addU32MetricsGlb(KU32MetricItem type, unsigned int val)
{
	if (!m_flags.enable) // 非beginCollect也会收集
		return;

	assert((int)type >= 0 && (int)type < (int)KU32MetricItem::_kCount);
	_addU32Metrics(type, val);
}

void KCoreMetric::set64Metrics(K64MetricItem type, int64_t val)
{
	if (m_level == 0) // beginCollect才收集
		return;

	assert((int)type >= 0 && (int)type < (int)K64MetricItem::_kCount);
	_set64Metrics(type, val);
}

void KCoreMetric::add64Metrics(K64MetricItem type, int64_t val)
{
	if (m_level == 0) // beginCollect才收集
		return;

	assert((int)type >= 0 && (int)type < (int)K64MetricItem::_kCount);
	_add64Metrics(type, val);
}

void KCoreMetric::add64MetricsGlb(K64MetricItem type, int64_t val)
{
	if (!m_flags.enable) // 非beginCollect也会收集
		return;

	assert((int)type >= 0 && (int)type < (int)K64MetricItem::_kCount);
	_add64Metrics(type, val);
}

void KCoreMetric::setWstrMetrics(KWStrMetricItem type, const WCHAR * wsz)
{
	if (m_level == 0) // beginCollect才收集
		return;

	assert((int)type >= 0 && (int)type < (int)KWStrMetricItem::_kCount);
	_setWStrMetrics(type, wsz);
}

void KCoreMetric::onBeforeExecTasks()
{
	if (m_level == 0) // beginCollect才收集
		return;
	_setU32Metrics(KU32MetricItem::kBeginExecTasksTime, _calcBeginTime(0));
	m_flags.isInExecTask = 1;
}

void KCoreMetric::onBeginUndoRedo(bool isUndo)
{
	if (m_level == 0) // beginCollect才收集
		return;
	m_flags.isInUndoRedo = 1;
}

void KCoreMetric::onTransformChanged()
{
	if (!m_flags.enable) // 非beginCollect也会收集
		return;
	
	_add64Metrics(K64MetricItem::kAllTransformCnt, 1);
	if (m_flags.isInExecTask)
		_add64Metrics(K64MetricItem::kAllTaskTransformCnt, 1);
	if (m_flags.isInUndoRedo)
		_add64Metrics(K64MetricItem::kAllUndoRedoTransformCnt, 1);
	
	if (m_level != 0)
	{
		_addU32Metrics(KU32MetricItem::kTransformationCnt, 1);
		if (m_flags.isInExecTask)
			_addU32Metrics(KU32MetricItem::kTaskTransformCnt, 1);
		if (m_flags.isInUndoRedo)
			_addU32Metrics(KU32MetricItem::kUndoRedoTransformCnt, 1);
	}
}

void KCoreMetric::onSerialOnlyVersionStart()
{
	if (m_level == 0) // beginCollect才收集
		return;
	m_beginOnlySerialVersion = std::chrono::steady_clock::now();
	m_flags.isInSerVersions = 1;
}

void KCoreMetric::onSerialOnlyVersionEnd(int cnt, int binSize)
{
	if (m_level == 0) // beginCollect才收集
		return;
	unsigned int ms = (std::chrono::steady_clock::now() - m_beginOnlySerialVersion) / std::chrono::milliseconds(1);
	_addU32Metrics(KU32MetricItem::kSerialOnlyVersionTime, ms);
	_addU32Metrics(KU32MetricItem::kSerialOnlyVersionBytes, binSize);
	_addU32Metrics(KU32MetricItem::kSerialOnlyVersionCnt, cnt);
	m_flags.isInSerVersions = 0;
}

void KCoreMetric::getSerNewObjMetricItem(KU32MetricItem & bytesItem, KU32MetricItem & timeItem, KU32MetricItem & cntItem)
{
	if (m_flags.isInSerVersions)
	{
		bytesItem = KU32MetricItem::kSerialVerNewObjBytes;
		timeItem = KU32MetricItem::kSerialVerNewObjTime;
		cntItem = KU32MetricItem::kSerialVerNewObjCnt;
	}
	else if (m_flags.isInCustomNewObjs)
	{
		bytesItem = KU32MetricItem::kNewCustomObjsBytes;
		timeItem = KU32MetricItem::kNewCustomObjsTime;
		cntItem = KU32MetricItem::kNewCustomObjsCnt;
	}
	else
	{
		bytesItem = KU32MetricItem::kSerialNewObjBytes;
		timeItem = KU32MetricItem::kSerialNewObjTime;
		cntItem = KU32MetricItem::kSerialNewObjCnt;
	}
}

void KCoreMetric::setTasksTime(unsigned int ms)
{
	if (m_level == 0)
		return;
	
	_setU32Metrics(KU32MetricItem::kTasksTime, ms);
	m_flags.isInExecTask = 0;
}

void KCoreMetric::setSerInitTime(unsigned int ms)
{
	setU32Metrics(KU32MetricItem::kSerInitTime, ms);
}

void KCoreMetric::setSerVersionTime(unsigned int ms)
{
	setU32Metrics(KU32MetricItem::kSerVersionsTime, ms);
}

void KCoreMetric::onLockBefore(bool isLock)
{
	if (!m_flags.enable)
		return;

	m_flags.isLock = isLock ? 1 : 0;
	m_flags.hasLock = 1;
	if (m_flags.isLock)
	{// befork fork, befor lock
		m_begin = std::chrono::steady_clock::now();
		m_lock.lock();
	}
	else
	{// after fork, befork unlock. non lock time
		m_lock.unlock();
		unsigned int us = (std::chrono::steady_clock::now() - m_beforeForkAfterLock) / std::chrono::microseconds(1);
		m_forkNonLockTime += us;
		m_allForkNonLockTime += us;
	}
}

void KCoreMetric::onLockAfter()
{
	if (!m_flags.enable || !m_flags.hasLock)
		return;
	
	m_flags.hasLock = 0;
	if (m_flags.isLock)
	{// before fork, after lock
		m_beforeForkAfterLock = std::chrono::steady_clock::now();
	}
	else 
	{ // after fork, after lock
		unsigned int us = (std::chrono::steady_clock::now() - m_begin) / std::chrono::microseconds(1);
		_addU32Metrics(KU32MetricItem::kForkTime, us);
		_add64Metrics(K64MetricItem::kAllForkTime, us);
		_addU32Metrics(KU32MetricItem::kForkCount, 1);
		_add64Metrics(K64MetricItem::kAllForkCount, 1);
	}
}

void KCoreMetric::onBeginSyncChild()
{
	/*if (!m_flags.enable)
		return;
	m_beginSyncChild = std::chrono::steady_clock::now();*/
}

void KCoreMetric::onEndSyncChild()
{
	/*if (!m_flags.enable)
		return;
	
	unsigned int ms = (std::chrono::steady_clock::now() - m_beginSyncChild) / std::chrono::microseconds(1);
	m_syncChildTime += ms;
	++m_syncChildCount;*/
}

void KCoreMetric::onSerialVersions(WebInt serVers, WebInt hitVers)
{
	if (m_level == 0)
		return;
	_setU32Metrics(KU32MetricItem::kSerVersionCnt, serVers);
	_setU32Metrics(KU32MetricItem::kSerHitVersionCnt, hitVers);
}

void KCoreMetric::addMetric(KEtWorkbook * wb, binary_wo::BinWriter & bw, WebProcType procType)
{
	if (!m_flags.enable)
		return;
	if (!m_flags.isQuery && !m_flags.isExec)
		return;
	if (m_flags.isHttpCalling)
		return;
	
	unsigned int ms = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
	bool isMaster = procType == WebProcTypeMaster;
	unsigned int forkCnt = _getU32Metrics(KU32MetricItem::kForkCount);
	bw.beginStruct("coreMetric");
		bw.addUint32Field(ms, "time");
		bw.addBoolField(isMaster, "isMaster");
		bw.addUint32Field(
			_getU32Metrics(KU32MetricItem::kTasksTime), 
			getKey(g_u32MetricKey, KU32MetricItem::kTasksTime));
		if (m_flags.isExec)
			bw.addUint32Field(m_execIdx, "idx");
		if (m_flags.isExec && forkCnt)
		{
			unsigned int forkTime = _getU32Metrics(KU32MetricItem::kForkTime);
			bw.addUint32Field(forkCnt, "forkCount");
			bw.addUint32Field(forkTime / 1000, "forkTime");
			bw.addUint32Field((forkTime - m_forkNonLockTime) / 1000, "forkLockTime");
		}
		if (isMaster && m_syncChildCount)
		{
			bw.addUint32Field(m_syncChildTime, "syncChildTime");
			bw.addUint32Field(m_syncChildCount, "syncChildCnt");
		}
		int serVerCnt = _getU32Metrics(KU32MetricItem::kSerVersionCnt);
		if (serVerCnt)
		{
			bw.addUint32Field(serVerCnt, "serVers");
			bw.addInt32Field(_getU32Metrics(KU32MetricItem::kSerHitVersionCnt), "hitVers");
			bw.addUint32Field(_getU32Metrics(KU32MetricItem::kSerVersionsTime), "serVerTime");
		}
		
		if (m_flags.hasExecInit)
		{
			WebInt taskVers = wb->getVersionMgr()->getTaskVersionCount();
			WebInt diffVers = taskVers - m_preInitTaskVers;
			_setU32Metrics(KU32MetricItem::kInitDiffVersCnt, diffVers);
			bw.addInt32Field(diffVers, "initDifVers");
			bw.addUint32Field(_getU32Metrics(KU32MetricItem::kSerInitTime), "serInitTime");
			m_preInitTaskVers = taskVers;
		}

		int sampleRate = 0;
		if (calcCollect(ms, sampleRate))
		{
			bw.addBoolField(true, "isCollect");
			bw.addInt32Field(sampleRate, "sampleRate");
		}
	bw.endStruct();
	
	if (0 == m_level)
	{
		ASSERT(false);
		resetData();
	}
}

void KCoreMetric::onSyncChildMetric(binary_wo::VarObj rootObj, binary_wo::BinWriter & bw)
{
	if (!m_flags.enable)
		return;
	
	VarObj varOp = rootObj.get_s("syncOp");
	if (varOp.empty())
		return;
	
	KCoreMetricSyncOp op = (KCoreMetricSyncOp)varOp.value_int32();
	switch (op) 
	{
		case KCoreMetricSyncOp::kQuery:
			{
				uint32_t ms = rootObj.field_uint32("time");
				int64_t cnt, time;
				{ // add
					ThreadLiteLib::SpinLockHelper locker(&m_lock);
					_add64Metrics(K64MetricItem::kAllForkQueryCnt, 1);
					_add64Metrics(K64MetricItem::kAllForkQueryTime, ms);
					
					cnt = _get64Metrics(K64MetricItem::kAllForkQueryCnt);
					time = _get64Metrics(K64MetricItem::kAllForkQueryTime);
				}
				{// ser
					bw.addInt32Field((int)op, "syncOp");
					bw.addAbsObjIDField(time, "time");
					bw.addAbsObjIDField(cnt, "count");
				}
			}
			break;
		default:
			ASSERT(false);
	}
}

void KCoreMetric::onSyncMasterMetric(binary_wo::VarObj rootObj)
{
	if (!m_flags.enable)
		return;
	
	VarObj varOp = rootObj.get_s("syncOp");
	if (varOp.empty())
		return;
	
	KCoreMetricSyncOp op = (KCoreMetricSyncOp)varOp.value_int32();
	switch (op) 
	{
		case KCoreMetricSyncOp::kQuery:
			{
				int64_t ms = rootObj.field_web_id("time");
				int64_t cnt = rootObj.field_web_id("count");
				_set64Metrics(K64MetricItem::kAllForkQueryCnt, cnt);
				_set64Metrics(K64MetricItem::kAllForkQueryTime, ms);
			}
			break;
		default:
			ASSERT(false);
	}
}

void KCoreMetric::addUndoRedoTime(unsigned int ms, bool isUndo)
{
	if (isUndo)
		addU32Metrics(KU32MetricItem::kUndoTime, ms);
	else
		addU32Metrics(KU32MetricItem::kRedoTime, ms);
	
	m_flags.isInUndoRedo = 0;
}

void KCoreMetric::setExecInitImplTime(unsigned int ms, bool isReinit)
{
	if (m_level == 0)
		return;

	m_flags.hasExecInit = 1;
	_setU32Metrics(KU32MetricItem::kInitTime, ms);
	_add64Metrics(K64MetricItem::kAllInitCount, 1);
	if (isReinit)
	{
		_add64Metrics(K64MetricItem::kAllReInitCnt, 1);
		m_flags.isReInit = 1;
	}
	_setU32Metrics(KU32MetricItem::kBeginExecInitTime, _calcBeginTime(ms));
}

void KCoreMetric::addAppEndTransTime(unsigned int ms)
{
	addU32Metrics(KU32MetricItem::kAppEndTransTime, ms); // undo/redo/execTasks都有。
}

void KCoreMetric::addTransformationTime(unsigned int ms)
{
	if (m_level == 0) // beginCollect才收集
		return;
	_addU32Metrics(KU32MetricItem::kTransformTime, ms); // undo/redo/execTasks都有。
	if (m_flags.isInExecTask)
		_addU32Metrics(KU32MetricItem::kTaskTransformTime, ms);
	if (m_flags.isInUndoRedo)
		_addU32Metrics(KU32MetricItem::kUndoRedoTransformTime, ms);
}

void KCoreMetric::addCmdTime(unsigned int ms, unsigned int memUsedKb, unsigned int beginMemKb)
{
	addU32Metrics(KU32MetricItem::kCmdsTime, ms);
	addU32Metrics(KU32MetricItem::kCmdMemUsedKb, memUsedKb);
	if (_getU32Metrics(KU32MetricItem::kCmdBeginMemKb) == 0)
		setU32Metrics(KU32MetricItem::kCmdBeginMemKb, beginMemKb);
}

void KCoreMetric::onSerializeVersionsBegin()
{
	setU32Metrics(KU32MetricItem::kBeginSerialVersTime, _calcBeginTime(0));
}

void KCoreMetric::onBeforeCmdExec(WebStr name, const ks_wstring & cmdId)
{
	if (m_level == 0)
		return;
	if (m_flags.isInExecTask && !m_wstrFlags[(int)KWStrMetricItem::kFirstCmdName])
	{
		_setWStrMetrics(KWStrMetricItem::kFirstCmdName, name);
		_setWStrMetrics(KWStrMetricItem::kFirstCmdTraceId, cmdId.c_str());
	}
	_addU32Metrics(KU32MetricItem::kCmdsCnt, 1);
}

void KCoreMetric::setTraceId(const ks_wstring & traceId) 
{
	if (!m_flags.enable)
		return;
	if (m_level > 0 && m_traceId.empty())
		m_traceId = traceId; 
}

void KCoreMetric::setIsHttpCaling(bool v)
{
	if (!m_flags.enable)
		return;
	if (v)
		m_flags.isHttpCalling = 1;
}

void KCoreMetric::beginCollect(KMetricsType type)
{
	if (!m_flags.enable)
		return;
    
	++m_level;
	if (1 == m_level)
	{
		m_beginCollect = std::chrono::steady_clock::now();
		m_type = type;
	}
	assert(m_level == 1);
}

void KCoreMetric::setAllCountAndTime(int64_t cnt, int64_t ms)
{
	if (!m_flags.enable)
		return;
	_set64Metrics(K64MetricItem::kAllCout, cnt);
	_set64Metrics(K64MetricItem::kAllTime, ms);
}

void KCoreMetric::endCollect(wo::KEtWorkbook* wb, const char * name, unsigned int ms, WebProcType procType)
{
	if (!m_flags.enable)
		return;
	
	--m_level;
	if (m_level > 0)
		return;
	
	m_level = 0;
	int sampleRate = 0;
	if (calcCollect(ms, sampleRate))
	{
		unsigned int durOpenTime = (std::chrono::steady_clock::now() - m_tpOpenEnd) / std::chrono::seconds(1);
		
		QString cltName = makeCollectName(wb, name, procType);
		WoCoreMetricsCollector collector(wb, krt::utf16(cltName), ms);
		
		_setU32Metrics(KU32MetricItem::kForkLockTime, _getU32Metrics(KU32MetricItem::kForkTime) - m_forkNonLockTime);
		_set64Metrics(K64MetricItem::kAllForkLockTime, _get64Metrics(K64MetricItem::kAllForkTime) - m_allForkNonLockTime);
		
		std::stringstream logTime;
		for (int i = 0; i < (int)KU32MetricItem::_kCount; ++i)
		{
			KU32MetricItem tp = (KU32MetricItem)i;
			unsigned int cmValue = m_u32Metrics[i];
			const char * cmKey = getKey(g_u32MetricKey, tp);
			int cvt = getUnitCvt(g_u32MetricKey, tp);

			cmValue = cvt == 1 ? cmValue : cmValue / cvt;
			if (isSend(g_u32MetricKey, tp))
				collector.addUInt32(cmKey, cmValue);
			if (cmValue != 0)
				logTime << ", " << cmKey << ": " << cmValue;
		}

		for (int i = 0; i < (int)K64MetricItem::_kCount; ++i)
		{
			K64MetricItem tp = (K64MetricItem)i;
			int64_t cmValue = m_64Metrics[i];
			const char * cmKey = getKey(g_64MetricKey, tp);
			int cvt = getUnitCvt(g_64MetricKey, tp);

			cmValue = cvt == 1 ? cmValue : cmValue / cvt;
			if (isSend(g_64MetricKey, tp))
				collector.addInt64(cmKey, cmValue);
			if (cmValue != 0)
				logTime << ", " << cmKey << ": " << cmValue;
		}
		
		for (int i = 0; i < (int)KWStrMetricItem::_kCount; ++i)
		{
			KWStrMetricItem tp = (KWStrMetricItem)i;
			int64_t cmValue = m_64Metrics[i];
			const char * cmKey = getKey(g_WStrMetricKey, tp);
			
			if (m_wstrFlags[i])
			{
				collector.addString(cmKey, m_wstrMetrics[i]);
#ifdef _DEBUG
				QString str = krt::fromUtf16(m_wstrMetrics[i].c_str(), m_wstrMetrics[i].size());
				QByteArray strData = str.toUtf8();
				logTime << ", " << cmKey << ": " << strData.data();
#endif
			}
			else
			{
				ks_wstring wstr;
				collector.addString(cmKey, wstr);
			}
		}

		const WCHAR * wszOpType = g_metricType[(int)m_type].m_key;
		collector.addString("traceid", m_traceId)
				.addInt32("sample_rate", sampleRate)
				.addInt32("is_master", procType == WebProcTypeMaster)
				.addString("op_type", wszOpType)
				.addUInt32("open_interval_s", durOpenTime)
				.addInt32("is_reinit", m_flags.isReInit);
				;

		WOLOG_INFO << "coreMetric: " << cltName
				<< ", traceId: " << m_traceId
				<< ", sample_rate: " << sampleRate
				<< ", is_master: " << (procType == WebProcTypeMaster)
				<< ", core_time: " << ms
				<< ", op_type: " << wszOpType
				<< ", open_interval_s: " << durOpenTime
				<< logTime.str()
				;
	}
	
	resetData(); // 有些是两个期间数据，故begin不resetData()
}

bool KCoreMetric::calcCollect(unsigned int ms, int & rate)
{
	if (!m_flags.hasCalcCollect)
	{
		wo::WoEtSetting * settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
		
		unsigned int signalSzMB = _get64Metrics(K64MetricItem::kSignalSize) >> 20;
		if (signalSzMB >= settings->GetCMSignalThreshold())
		{// signal size >= 10 M
			m_flags.isCollect = 1;
			m_sampleRate = 1;
		}
		else
		{
			unsigned int cltMs = ms;
	#ifdef _DEBUG
			cltMs += 1000;
	#endif
			int collectTimeThreshold = settings->GetCoreMetricsCollectThreshold();
			m_flags.isCollect = settings->isCollect(cltMs, &m_sampleRate, settings->GetCoreMetricsRand(), &collectTimeThreshold);
		}
		m_flags.hasCalcCollect = 1;
	}
	rate = m_sampleRate;
	return m_flags.isCollect;
}

void KCoreMetric::resetData()
{
	int enable = m_flags.enable;
	m_flags.value = 0;
	m_flags.enable = enable;
	memset(m_u32Metrics, 0, sizeof(m_u32Metrics));
	for (int i = 0; i < (int)K64MetricItem::_kCount; ++i)
	{
		K64MetricItem tp = (K64MetricItem)i;
		if (needReset(g_64MetricKey, tp))
			m_64Metrics[i] = 0;
	}
	
	for (int i = 0; i < (int)KWStrMetricItem::_kCount; ++i)
	{
		if (!m_wstrFlags[i])
			continue;
		m_wstrMetrics[i] = ks_wstring();
	}
	m_wstrFlags.reset();

	m_allForkNonLockTime = m_forkNonLockTime = 0;
	m_traceId = ks_wstring();
}

QString KCoreMetric::makeCollectName(wo::KEtWorkbook* wb, const char *szName, WebProcType procType)
{
    QString name(procType == WebProcTypeMaster ? "behaviour_master_": "behaviour_slave_");
    if (wb->isSecDoc())
        name.append("sec_");
    IWoCallBack* pCb = _kso_GetWoCallBack();
    if (pCb && pCb->isBreak())
        name += "break_";
	if (m_flags.isHttpCalling)
		name += "http_";
	name.append(szName);
	return name;
}

void KCoreMetric::onOpenEnd()
{
	m_tpOpenEnd = std::chrono::steady_clock::now();
}

}
