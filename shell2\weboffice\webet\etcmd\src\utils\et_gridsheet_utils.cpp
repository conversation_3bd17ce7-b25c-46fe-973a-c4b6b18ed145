﻿#include "etstdafx.h"
#include "et_gridsheet_utils.h"

namespace wo
{

HRESULT GetValidSheetName(IKWorksheets* pWorksheets, IKWorksheet* pWorksheet, PCWSTR name, BSTR* newName)
{
	if (!pWorksheets || !pWorksheet || !newName)
		return E_INVALIDARG;
	ks_bstr sheetName(name);
	HRESULT hr = S_OK;
	if (!name || pWorksheet->IsValidSheetName(name) != S_OK)
	{
		constexpr int len = MAX_SHEET_NAME_CCH + 1;
		std::array<WCHAR, len> nameBuf = {0};
		SHEETTYPE sheetType = stUnknown;
		pWorksheet->GetSheet()->GetFullType(&sheetType);
		hr = pWorksheets->ValidateSheetName(sheetType, name, nameBuf.data(), len);
		if (FAILED(hr))
			return hr;
		sheetName.assign(nameBuf.data());
	}
	*newName = sheetName.detach();
	return S_OK;
}

namespace GridSheet
{

HRESULT GetCellToken(IBookOp* pBookOp, IDX sheet, ROW row, COL col, MergedCellMode mode, const_token_ptr* pToken)
{
	if (!pBookOp)
		return E_INVALIDARG;
	ks_stdptr<ISheet> spSheet;
	pBookOp->GetSheet(sheet, &spSheet);
	if (!spSheet)
		return E_FAIL;
	BOOL isMerged = FALSE;
	spSheet->IsMerged(row, col, &isMerged);
	if (isMerged)
	{
		RANGE rg(spSheet->GetBMP());
		spSheet->RetieveMerge(row, col, &rg);
		switch (mode)
		{
			case Fill:
			{
				row = rg.RowFrom();
				col = rg.ColFrom();
				break;
			}
			default:
				break;
		}
	}
	return pBookOp->GetCellValue(sheet, row, col, pToken);
}

HRESULT GetCellText(IETStringTools* pStringTools, IKWorksheet* pWorksheet, ROW row, COL col, MergedCellMode mode, BSTR* pBstrVal)
{
	if (!pWorksheet)
		return E_FAIL;
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!pSheet)
		return E_FAIL;
	IDX sheetIdx = INVALIDIDX;
	VS(pSheet->GetIndex(&sheetIdx));
	BOOL isMerged = FALSE;
	pSheet->IsMerged(row, col, &isMerged);
	RANGE rg(pSheet->GetBMP());
	if (isMerged)
	{
		pSheet->RetieveMerge(row, col, &rg);
		switch (mode)
		{
			case Fill:
			{
				row = rg.RowFrom();
				col = rg.ColFrom();
				break;
			}
			default:
				break;
		}
	}
	rg.SetCell(sheetIdx, row, col);
	ks_stdptr<etoldapi::Range> spRange;
	VS(pWorksheet->GetRangeByData(&rg, &spRange));
	KComVariant var;
	spRange->get_Value2(&var);
	VARTYPE vt = V_VT(&var);
	switch (vt)
	{
		case VT_R8:
		{
			double val = V_R8(&var);
			constexpr int prec = 16;
			QString qstrVal = QString::number(val, 'g', prec);
			PCWSTR strVal = krt::utf16(qstrVal);
			*pBstrVal = SysAllocString(strVal);
			break;
		}
		case VT_LPWSTR:
		case VT_BSTR:
		{
			PCWSTR strVal = V_BSTR(&var);
			*pBstrVal = SysAllocString(strVal);
			break;
		}
		default:
			return pStringTools->GetCellText(pSheet, row, col, nullptr, pBstrVal, -1, nullptr); // 走 const XF* 的重载
	}
	return S_OK;
}

} // namespace GridSheet

} // namespace wo
