﻿#include "etstdafx.h"
#include "appsheet_copysheet_helper.h"
#include "dbsheet/et_dbsheet_utils.h"

namespace wo
{
HRESULT CopyAppSheetHelper::Init(etoldapi::_Worksheet* pWorkSheet, etoldapi::_Worksheet* pNewWorkSheet, std::vector<AppSharedInfo>* pVec, binary_wo::VarObj* pParam)
{
	if (!pWorkSheet || !pNewWorkSheet)
		return E_FAIL;
	m_pWorkSheet = pWorkSheet;
	m_pNewWorkSheet = pNewWorkSheet;
    m_pSharedInfo = pVec;
    m_pParam = pParam;
	return S_OK;
}

HRESULT CopyAppSheetHelper::InitForTemplate(std::unordered_map<UINT, UINT>* pMap)
{
	m_pSheetIdMap = pMap;
	return S_OK;
}

HRESULT CopyAppSheetHelper::ExecCopy()
{
    HRESULT hr = E_FAIL;
	ISheet* pSheet = m_pWorkSheet->GetSheet();
	ISheet* pNewSheet = m_pNewWorkSheet->GetSheet();
    ks_stdptr<IAppSheetData> spSrcAppSheetData;
    pSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spSrcAppSheetData);
    if (!spSrcAppSheetData)
        return E_FAIL;

    ks_stdptr<IAppSheetData> spTarAppSheetData;
    pNewSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spTarAppSheetData);
    if (!spTarAppSheetData)
        return E_FAIL;
    
    pNewSheet->LeakBook()->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&m_spSharedLinkMgr);
    for (int i = 0; i < spSrcAppSheetData->GetAppCount(); ++i)
    {
        IAirApp* pSrcApp = spSrcAppSheetData->GetApp(i);
        ks_stdptr<IAirApp> spTarApp;
        spTarAppSheetData->CreateAppForIO(pSrcApp->GetType(), pSrcApp->GetId(), &spTarApp);
        
        switch (pSrcApp->GetCategory())
        {
            case KSheet_AirAppCat_DbSheetView:
            {
                //copyDbView()里面会去调用SetSharedId，因此外面不用再次调用SetSharedId
                hr = copyDbView(pSrcApp, spTarApp);
                break;
            }
            default:
            {
                //其他情况：保证一定会调用SetSharedId
                hr = spTarApp->SetSharedId(pSrcApp->GetSharedId());
                break;
            }
        }
        if(FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT CopyAppSheetHelper::copyDbView(IAirApp* pSrcApp, IAirApp* pTarApp)
{
    HRESULT hr = E_FAIL;
    ks_stdptr<IAirApp_DBView> spSrcApp_DB = pSrcApp;
    ks_stdptr<IAirApp_DBView> spTarApp_DB = pTarApp;
    ks_wstring newSharedId = __X("");
    ET_DBSheet_ViewType viewType = static_cast<ET_DBSheet_ViewType>(_appcore_GainAirAppTypeConverter()->ConvertToNormalType(pSrcApp->GetType()));
    fetchSharedId(viewType, pTarApp, &newSharedId);
    IDBSheetView* pSrcView = spSrcApp_DB->GetDBSheetView();
    if (pSrcView)
    {
        UINT appSrcSheetStId = pSrcView->GetSheetOp()->GetSheetId();
        UINT appTarSheetStId = appSrcSheetStId;
        EtDbId tarViewId = INV_EtDbId;
        if (m_pSheetIdMap)
        {
            auto it = m_pSheetIdMap->find(appSrcSheetStId);
            ASSERT(it != m_pSheetIdMap->cend());
            appTarSheetStId = it->second;
            tarViewId = pSrcView->GetId();
        }
        else
        {
            //需要拷贝view
            ks_stdptr<IDBSheetViews> spDbSheetViews;
            VS(pSrcView->GetSheetOp()->GetDbSheetViews(&spDbSheetViews));
            if (!spDbSheetViews)
                return E_FAIL;
            hr = spDbSheetViews->CopyView(pSrcView->GetId(), TRUE, tarViewId);
            if(FAILED(hr))
            {
                WOLOG_ERROR << "[CopyAppSheetHelper] CopyView Fail!";
				return hr;
            }

            //重命名viewname为appsheetname
            PCWSTR tarAppSheetName = nullptr;
            pTarApp->GetSheet()->GetName(&tarAppSheetName);
            ks_wstring viewName(tarAppSheetName);
            HRESULT hr = DbSheet::GetValidViewName(spDbSheetViews, tarAppSheetName, viewName);
            if(FAILED(hr))
            {
                WOLOG_ERROR << "[CopyAppSheetHelper] GetValidViewName Fail!";
				return hr;
            }

            ks_stdptr<IDBSheetView> pTarView;
            spDbSheetViews->GetItemById(tarViewId, &pTarView);
            hr = pTarView->SetName(viewName.c_str());

            if(FAILED(hr))
            {
                WOLOG_ERROR << "[CopyAppSheetHelper] GetValidViewName Fail!";
				return hr;
            }
        }

        ASSERT(tarViewId != INV_EtDbId);
        spTarApp_DB->SetInfoForIO(appTarSheetStId, tarViewId);
        //给拷贝的appsheet绑定sharedlink
        IDBSharedLinkView* pSrcSharedView = pSrcView->GetSharedLink();
        ks_stdptr<ISharedLink> spSharedLink;
        m_spSharedLinkMgr->CreateSharedLink(newSharedId.c_str(), pSrcSharedView->SharedUserId(), pSrcSharedView->IsUsedForApp(), pSrcSharedView->Type(),
            &spSharedLink, appTarSheetStId, tarViewId);
        ks_stdptr<IDBSharedLinkView> spSharedView = spSharedLink;
        pSrcSharedView->CopyTo(spSharedView);
        spSharedView->SetAppSheetStId(pTarApp->GetSheet()->GetStId());
        spSharedView->SetAppId(pTarApp->GetId());
        hr = m_spSharedLinkMgr->Add(spSharedView);
        if(FAILED(hr))
            return hr;
    }
    pTarApp->SetSharedId(newSharedId.c_str());
    AirAppInfo appInfo = {pTarApp->GetSheet()->GetStId(), pTarApp->GetId()};
    AppSharedInfo sharedInfo = {appInfo, pSrcApp->GetSharedId(), newSharedId.c_str()};
    m_pSharedInfo->push_back(sharedInfo);
    return S_OK;
}

HRESULT CopyAppSheetHelper::fetchSharedId(ET_DBSheet_ViewType viewType, IAirApp* pTarApp, ks_wstring* sharedId)
{
    wo::IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
    if (pCtx->isExecDirect())
    {
        return fetchSharedIdForDirect(pTarApp, sharedId);
    }
    else
    {
        DbSheet::FetchSharedIdForNotDirect(viewType, sharedId);
        return S_OK;
    }
}

HRESULT CopyAppSheetHelper::fetchSharedIdForDirect(IAirApp* pTarApp, ks_wstring* sharedId)
{
    HRESULT hr= E_FAIL;
    binary_wo::VarObj sharedIds = m_pParam->get_s("sharedIds");
    for (int i = 0; i < sharedIds.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = sharedIds.at_s(i);
        UINT sheetStId = item.field_uint32("sheetStId");
        if (sheetStId != m_pNewWorkSheet->GetSheet()->GetStId())
            continue;

        binary_wo::VarObj sharedIdsInfo = item.get_s("sharedIdsInfo");

        for (int j = 0; j < sharedIdsInfo.arrayLength_s(); j++)
        {
            binary_wo::VarObj sharedIdInfoItem = sharedIdsInfo.at_s(j);
            PCWSTR appIdStr = sharedIdInfoItem.field_str("appId");
            EtDbId appId = INV_EtDbId;
            hr = _appcore_GainDbSheetContext()->DecodeEtDbId(appIdStr, &appId);
            if (FAILED(hr))
            {
                WOLOG_ERROR << "[fetchSharedIdForDirect] param parse appId failed";
                return hr;
            }
            if (pTarApp->GetId() != appId)
                continue;
            *sharedId = sharedIdInfoItem.field_str("sharedId");
            return S_OK;
        }
    }
    return hr;
}

void AppSharedInfo::Serialize(binary_wo::VarObj* pObj)
{
    IDBSheetCtx* pDbCtx =_appcore_GainDbSheetContext();
    pObj->add_field_str("oldSharedId", m_oldSharedId.c_str());
    pObj->add_field_str("newSharedId", m_newSharedId.c_str());
    pObj->add_field_uint32("airAppSheetStId", m_appInfo.sheetStId);
    EtDbIdStr buf;
    pDbCtx->EncodeEtDbId(m_appInfo.appId, &buf);
    pObj->add_field_str("airAppId", buf);
}

HRESULT CopyAppSheetForLocal::Init(etoldapi::_Worksheet* pWorkSheet, etoldapi::_Worksheet* pNewWorkSheet, binary_wo::VarObj* pParam)
{
    return CopyAppSheetHelper::Init(pWorkSheet, pNewWorkSheet, &m_sharedInfo, pParam);
}

void CopyAppSheetForLocal::SerialAppSharedInfo(binary_wo::VarObj* pObj)
{
    if (m_sharedInfo.size() != 0)
    {
        binary_wo::VarObj arrObj = pObj->add_field_array("appSharedInfo", binary_wo::typeStruct);
        for (int i = 0; i < m_sharedInfo.size(); ++i)
        {
            AppSharedInfo sharedInfo = m_sharedInfo[i];
            binary_wo::VarObj item = arrObj.add_item_struct();
            sharedInfo.Serialize(&item);
        }
    }
}

} // wo