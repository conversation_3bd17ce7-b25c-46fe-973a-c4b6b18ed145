#include "etstdafx.h"
#include "webbase/logger.h"
#include "workbook.h"
#include "db_protection_context_impl.h"
#include "util.h"
#include "appcore/et_appcore_shared_link_sheet.h"

namespace wo
{

KDbProtectionContext::KDbProtectionContext(KEtWorkbook *workbook)
{
    IBook* pBook = workbook->GetCoreWorkbook()->GetBook();
    ks_stdptr<IUnknown> spUnknown;
    VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
    spDBUserGroups->GetJudgement(&m_spProtectionJudgement);
    m_pBook = pBook;
}

KDbProtectionContext::~KDbProtectionContext()
{
}

void KDbProtectionContext::setupProtectionCache(ISheet *spSheet)
{
}

void KDbProtectionContext::setHasProtectionCache(bool bl)
{
}

void KDbProtectionContext::setupEtContext()
{
}

void KDbProtectionContext::clearEtContext()
{
}

bool KDbProtectionContext::isAllHidden()
{
    return m_spProtectionJudgement->IsNoPermissions();
}

bool KDbProtectionContext::isCellHidden(ISheet *pSheet, INT32 row, INT32 col)
{
    if (!pSheet || !isBookHasHidden())
    {
        return false;
    }

    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(pSheet->GetStId());
    if(FAILED(hr))
       return true;

    return FAILED(m_spProtectionJudgement->CheckCellCanVisit(pSheet->GetStId(), row, col));
}

bool KDbProtectionContext::isRangeHasHidden(const RANGE &rg, bool ignoreShared)
{
    if (!rg.IsValid() || !isBookHasHidden())
    {
        return false;
    }

    for (IDX s = rg.SheetFrom(); s <= rg.SheetTo(); ++s)
    {
        UINT stId = 0;
        if (FAILED(m_pBook->RTSheetToSTSheet(s, &stId)))
            return true;

        HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(stId);
        if(FAILED(hr))
            return true;

        for (ROW r = rg.RowFrom(); r <= rg.RowTo(); ++r)
        {
            for (COL c = rg.ColFrom(); c <= rg.ColTo(); ++c)
            {
                if(FAILED(m_spProtectionJudgement->CheckCellCanVisit(stId, r, c)))
                    return true;
            }
        }
    }

    return false;
}

bool KDbProtectionContext::isRangeHasHidden(IKRanges *rgs, bool ignoreShared)
{
    if (!rgs || !isBookHasHidden())
    {
        return false;
    }

    if (m_spProtectionJudgement->IsNoPermissions())
        return true;

    UINT count = 0;
    rgs->GetCount(&count);
    for (UINT i = 0; i < count; ++i)
    {
        const RANGE *rg = nullptr;
        rgs->GetItem(i, NULL, &rg);
        if (isRangeHasHidden(*rg))
        {
            return true;
        }
    }

    return false;
}

bool KDbProtectionContext::isRangeHasHidden(ISheet *pSheet, const RANGE &rg, bool ignoreShared)
{
    if (!pSheet || !rg.IsValid() || !isBookHasHidden())
    {
        return false;
    }

    UINT stId = pSheet->GetStId();
    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(stId);
    if(FAILED(hr))
        return true;

    for (ROW r = rg.RowFrom(); r <= rg.RowTo(); ++r)
    {
        for (COL c = rg.ColFrom(); c <= rg.ColTo(); ++c)
        {
            if(FAILED(m_spProtectionJudgement->CheckCellCanVisit(stId, r, c)))
                return true;
        }
    }
    return false;
}

void KDbProtectionContext::splitVisibleRange(ISheet *pSheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg)
{
    if (!pSheet || !rg.IsValid() || !isBookHasHidden())
    {
        vctVisibleRg.push_back(rg);
        return;
    }

}

void KDbProtectionContext::splitHiddenRange(ISheet *pSheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges)
{
    if (!pSheet || !rg.IsValid() || !isBookHasHidden())
    {
        return;
    }

    hiddenRanges.push_back(rg);
}

bool KDbProtectionContext::isSheetHidden(UINT stId)
{
    IDX sheetIdx = INVALIDIDX;
    m_pBook->STSheetToRTSheet(stId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
		return true;
    ks_stdptr<ISheet> spSheet;
    m_pBook->GetSheet(sheetIdx, &spSheet);
    if (util::isCellImgListSheet(m_pBook, sheetIdx))
        return false;
    //如果当前conn为sheet级别的shareLink，则根据当前sheet是否为share决定hidden。
    ISharedLink* pLink = GetSharedLink();
    if (pLink && pLink->Type() == SharedLinkType_Sheet)
    {
        ISharedLinkSheet* pSharedLinkSheet = static_cast<ISharedLinkSheet*>(pLink);
        if (pSharedLinkSheet && pSharedLinkSheet->GetSheet()->IsDbDashBoardSheet())
        {
            const IDBSheetProtection* pDbSheetProtection = pSharedLinkSheet->GetDbSheetProtection(stId);
            return !pDbSheetProtection->GetVisible();
        }
        return !isSheetShared(stId);
    }
    //如果当前conn不为sheet级别的shareLink，走原本的逻辑。
    if (spSheet->IsFpSheet() || spSheet->IsDbDashBoardSheet())
        return FAILED(m_spProtectionJudgement->CheckSheetCanVisit(stId));
	if (!spSheet->IsDbSheet())
        return true;

    return FAILED(m_spProtectionJudgement->CheckSheetCanVisit(stId));
}

bool KDbProtectionContext::isSheetLinkShare(UINT stId)
{
    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	m_pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    wo::IEtRevisionContext *pRvsCtx = _etcore_GetEtRevisionContext();
    if (!spSharedLinkMgr || !pRvsCtx || !pRvsCtx->getUser())
        return false;

    PCWSTR sharedId = spSharedLinkMgr->GetConnSharedLink(pRvsCtx->getUser());
    if (!sharedId)
        return false;

    ISharedLink* pLink = spSharedLinkMgr->GetItem(sharedId);
    if (pLink)
    {
         switch(pLink->Type())
        {
            case SharedLinkType_DbView:
                return false;
            case SharedLinkType_Sheet:
            {
                ks_stdptr<ISharedLinkSheet> spSharedLinkSheet = pLink;
                if (spSharedLinkSheet && spSharedLinkSheet->GetSheet()->GetStId() == stId)
                    return true;
                break;
            }
            default:
                ASSERT(FALSE);
        }
    }

    return false;
}

bool KDbProtectionContext::isSheetShared(UINT stId)
{
    IDX sheetIdx = INVALIDIDX;
    m_pBook->STSheetToRTSheet(stId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
		return false;
    ks_stdptr<ISheet> spSheet;
    m_pBook->GetSheet(sheetIdx, &spSheet);
    if(spSheet->IsDbDashBoardSheet())
    {
        return isSheetLinkShare(stId);
    }

    return m_spProtectionJudgement->IsSheetShared(stId);
}

bool KDbProtectionContext::isSheetHasHidden(ISheet *pSheet)
{
    if (!pSheet)
    {
        return false;
    }

    return FAILED(m_spProtectionJudgement->CheckSheetCanVisit(pSheet->GetStId()));
}

bool KDbProtectionContext::isBookHasHidden(IBook *pBook)
{
    return true;
}

bool KDbProtectionContext::isBookHasHidden()
{
    return alg::BOOL2bool(m_spProtectionJudgement->HasHiddenData());
}

bool KDbProtectionContext::isBookHasHidden(IKWorkbook *workbook)
{
    return true;
}

bool KDbProtectionContext::isSheetHasHiddenProperty(ISheet *pSheet)
{
    if (!pSheet)
    {
        return false;
    }

    return true;
}

bool KDbProtectionContext::isBookHasHiddenProperty(IBook *pBook)
{
    return true;
}

bool KDbProtectionContext::isRangeHasHiddenProperty(const RANGE &rg, bool checkUnprotected /* = false */)
{
    if (!rg.IsValid())
    {
        return false;
    }

    return false;
}

bool KDbProtectionContext::isBookHasHiddenProperty()
{
    return alg::BOOL2bool(m_spProtectionJudgement->IsNeedProtection()); 
}

bool KDbProtectionContext::isSheetHasReadonly(ISheet *pSheet)
{
    return true;
}

bool KDbProtectionContext::isBookHasReadonly(IBook *pBook)
{
    return true;
}

bool KDbProtectionContext::isBookHasReadonly()
{
    return true;
}

bool KDbProtectionContext::isSheetHasReadonlyProperty(ISheet *pSheet)
{
    return true;
}

bool KDbProtectionContext::isBookHasReadonlyProperty(IBook *pBook)
{
    return true;
}

bool KDbProtectionContext::isSheetHasEditable(ISheet *pSheet)
{
	return true;
}

bool KDbProtectionContext::isBookHasReadonlyProperty()
{
    return true;
}

bool KDbProtectionContext::isBookHasSheetProtected()
{
    return m_spProtectionJudgement->IsNeedProtection();
}

bool KDbProtectionContext::isThisBookHasEditable()
{
	return true;
}


void KDbProtectionContext::clearProtectionCache()
{
}

bool KDbProtectionContext::isCellImageFormula(const_token_vector vecToken)
{
    if (!vecToken)
    {
        return false;
    }

    int count = 0;
    vecToken->GetSize(&count);
    if (count > 0)
    {
        const_token_ptr funcToken = NULL;
        vecToken->GetItem(count - 1, &funcToken);
        if (funcToken && GetExecTokenMajorType(funcToken) == alg::ETP_FUNCTION)
        {
            alg::const_function_token_assist func(funcToken);
            return func.get_id() == FNID_DISPIMG;
        }
    }

    return false;
}

bool KDbProtectionContext::isCellImageFormula(PCWSTR formula)
{
    if (!formula)
    {
        return false;
    }

    return xstrstr(formula, __X("=DISPIMG")) == formula;
}

HRESULT KDbProtectionContext::checkAllowEdit(const RANGE &rg)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkAllowEditWidthLockedCell(const RANGE &rg)
{
    return S_OK;
}

bool KDbProtectionContext::isAllowEdit(const RANGE &rg, ProtectionAccessPerms *out)
{
    return true;
}

bool KDbProtectionContext::isAllowEdit(IKRanges *rgs, ProtectionAccessPerms *right)
{
     return true;
}

bool KDbProtectionContext::isAllowEdit(const std::vector<RANGE> &ranges, ProtectionAccessPerms *right)
{
    return true;
}

HRESULT KDbProtectionContext::isOperationAllowedProtected(const RANGE &rg, et_appcore::ActionType acType)
{
    return WO_FAIL;
}


bool KDbProtectionContext::isRangeAllHidden(const RANGE &rg)
{
    if (!rg.IsValid())
    {
        return false;
    }
    return isBookHasHidden();
}

bool KDbProtectionContext::isProtectionAreaUnlock(LPCWSTR id)
{
    return false;
}

bool KDbProtectionContext::isAllowedFormula(PCWSTR formula)
{
	return true;
}

HRESULT KDbProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, const_token_ptr token)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, const_token_vector vecToken)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkFormula(const RANGE &rg, const_token_vector vecToken)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, PCWSTR fmla)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkFormula(const RANGE &rg, PCWSTR fmla)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkDVFormula(const RANGE &rg, const VALIDATION *dv)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkDVFormula(const RANGE &rg, DVValueType dvType, PCWSTR pFormula1, PCWSTR pFormula2)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkDVFormula(const RANGE &rg,
                                           DVValueType dvType,
                                           ITokenVectorInstant *pFormula1,
                                           ITokenVectorInstant *pFormula2)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkDVFormulaInRange(const RANGE &rg)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkCFFormula(const RANGE &appliedRg, ICondFmt *condFmt)
{
    return S_OK;
}

HRESULT KDbProtectionContext::checkCFFormulaInRange(const RANGE &rg)
{
    return S_OK;
}


HRESULT KDbProtectionContext::checkProtectionCopyContent(const RANGE &rg)
{
    return S_OK;
}

uint32_t KDbProtectionContext::getProtectionContentKind(const RANGE &rg, bool isAll)
{
    return pckNone;
}

HRESULT KDbProtectionContext::checkProtectionContent(const RANGE &rg, uint32_t *pckKind)
{
    return S_OK;
}

void KDbProtectionContext::setValidRefKind(uint32_t k)
{

}

bool KDbProtectionContext::isCompileFormula() const
{
    return true;
}

void KDbProtectionContext::setCompileFormula(bool set)
{

}

uint32_t KDbProtectionContext::getPasteContentKind() const
{
    return 0;
}

void KDbProtectionContext::setPasteContentKind(uint32_t kind)
{

}

bool KDbProtectionContext::allowDeleteLockedRowCol() const
{
    return true;
}

void KDbProtectionContext::setAllowDeleteLockedRowCol(bool isAllowed)
{

}

int KDbProtectionContext::currentCommand() const
{
    return IEtProtectionCtx::wocmdInvalid;
}

void KDbProtectionContext::setCurrentCommand(int cmd)
{
}

bool KDbProtectionContext::getAutoPassword(PCWSTR srcUUID, ks_wstring *outPwd)
{
	return false;
}

bool KDbProtectionContext::needCheckInsertDeleteHiddenRange(const RANGE& rg)
{
    return true;
}

bool KDbProtectionContext::canRefColProtect(const RANGE& rg)
{
    return true;
}

bool KDbProtectionContext::hasHiddenColProtect(ISheet* spSheet, const RANGE& rg)
{
    return false;
}

bool KDbProtectionContext::isBookHasColProtect()
{
    return false;
}

void KDbProtectionContext::splitVisibleRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg)
{
    splitVisibleRange(isheet, rg, vctVisibleRg);
}

void KDbProtectionContext::splitHiddenRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges)
{
    splitHiddenRange(isheet, rg, hiddenRanges);
}

void KDbProtectionContext::onUserColPermsChanged(LPCWSTR userId)
{
}

void KDbProtectionContext::updateUserColPermsStatus(LPCWSTR excludeId)
{
}

void KDbProtectionContext::markAllUsersColPermsChanged(LPCWSTR excludeId)
{
}

bool KDbProtectionContext::isAllUsersColPermsChanged()
{
    return false;
}

bool KDbProtectionContext::isConnSharedLink()
{
    return false;
}

IEtProtectionCtx::ProtectionType KDbProtectionContext::getPasteProtectionType() const
{
    return ptNone;
}

void KDbProtectionContext::setPasteProtectionType(IEtProtectionCtx::ProtectionType type)
{
}

ISharedLink* KDbProtectionContext::GetSharedLink()
{
    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	m_pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    wo::IEtRevisionContext *pRvsCtx = _etcore_GetEtRevisionContext();
    if (!spSharedLinkMgr || !pRvsCtx || !pRvsCtx->getUser())
        return nullptr;

    PCWSTR sharedId = spSharedLinkMgr->GetConnSharedLink(pRvsCtx->getUser());
    if (!sharedId)
        return nullptr;

    ISharedLink* pLink = spSharedLinkMgr->GetItem(sharedId);
    return pLink;
}

} // namespace wo
