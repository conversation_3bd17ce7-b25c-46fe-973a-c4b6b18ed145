﻿#ifndef __JSAPI_CONTEXT_H__
#define __JSAPI_CONTEXT_H__


namespace wo
{
struct JsApiSheetContext
{
	ks_stdptr<_Worksheet> spSheet;
	ks_stdptr<etoldapi::Range> spSelectionRange;
	ks_stdptr<IKCoreObject> spSelectionObject;
	CELL activeCell = {INVALIDIDX, INVALIDIDX};
	CELL leftTop = {INVALIDIDX, INVALIDIDX};
};

struct JsApiContext
{
	UINT32 curSheetId = INVALIDIDX;
	std::unordered_map<UINT32, std::unique_ptr<const JsApiSheetContext>> sheetsMap;
	int nWidth = 0;
	int nHeight = 0;
	ks_stdptr<etoldapi::Window> spWindow;
    bool m_bIsRunning = false;
};

struct QStrHash
{
	std::size_t operator()(const QString& str) const
	{
		return qHash(str);
	}
};

class KJsApiContextScope
{
public:
	KJsApiContextScope(KEtWorkbook* pWb, KEtRevisionContext* pRC, const QString& taskId, const binary_wo::VarObj& param, bool bEnter);
	~KJsApiContextScope();
private:
	KEtWorkbook* m_pWb = nullptr;
	QString m_curTaskId;
    bool m_bEnter = false;
};

class KJsApiContextManager
{
public:
	static KJsApiContextManager* Instance();
	~KJsApiContextManager();

	void Enter(KEtWorkbook* pWb, KEtRevisionContext* pRC, const QString& taskId, const binary_wo::VarObj& param);
	void Exit(KEtWorkbook* pWb, const QString& taskId);
	void Close(const QString& taskId);
    void CloseAll();
    void SetRunning(const QString& taskId);
    bool IsRunning(const QString& taskId) const;

private:
	KJsApiContextManager();
	bool ParseContext(KEtWorkbook* pWb, const binary_wo::VarObj& param, JsApiContext *ctx);
	void SyncTaskContext(KEtWorkbook* pWb, KEtRevisionContext* pRC, JsApiContext* ctx);
	bool SyncSheetContext(KEtWorkbook* pWb, const JsApiSheetContext* sc);
	bool SyncSelectObject(IKCoreObject* pCoreObject);
	bool GetCell(const binary_wo::VarObj& param, const char* name, _Worksheet* pSheet, CELL& cell);
	bool GetRange(const binary_wo::VarObj& param, const char* name, _Worksheet* pSheet, etoldapi::Range** ppRange);
	bool GetCell(WebStr name, _Worksheet* pSheet, CELL& cell);
	bool GetRange(WebStr name, _Worksheet* pSheet, etoldapi::Range** ppRange);
	bool SupportSheet(_Worksheet* pSheet);
    void CleanContext(JsApiContext* ctx);

private:
	std::unordered_map<QString, JsApiContext*, QStrHash> m_contexts;
};

struct JsRunScope
{
    JsRunScope(KEtRevisionContext* ctx)
        : m_ctx(ctx)
    {
        if (m_ctx)
            m_bOld = m_ctx->SetJSRun(true);
    }
    ~JsRunScope()
    {
        if (m_ctx)
            m_ctx->SetJSRun(m_bOld);
    }
private:
    KEtRevisionContext* m_ctx = nullptr;
    bool m_bOld = false;
};

} // namespace wo

#endif // __JSAPI_CONTEXT_H__