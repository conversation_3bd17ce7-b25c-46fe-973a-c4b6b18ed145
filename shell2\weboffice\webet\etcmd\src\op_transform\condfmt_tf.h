﻿#ifndef __CONDFMT_TF_H__
#define __CONDFMT_TF_H__

namespace wo
{
	struct CfSwapPara
	{
		int m_iBelow;
		int m_iMove;
		CfSwapPara()
		{
			m_iBelow = -1;
			m_iMove = -1;
		}
		CfSwapPara(int below, int move)
		{
			m_iBelow = below;
			m_iMove = move;
		}
		void operator=(const CfSwapPara& rhs)
		{
			m_iBelow = rhs.m_iBelow;
			m_iMove = rhs.m_iMove;
		}
		bool isValid() const
		{
			return (m_iBelow >= 0) && (m_iMove >= 0);
		}
	};

	class CondFmtTrans
	{
	public:
		static bool TransByAdd(int iTrs, int& iRes);
		static bool TransByDel(int iDel, int iTrs, int& iRes);
		static bool TransBySwap(const CfSwapPara& para, int iTrs, int& iRes);
		static bool SwapByAdd(const CfSwapPara& paraTrs, CfSwapPara& paraRes);
		static bool SwapByDel(int iDel, const CfSwapPara& paraTrs, CfSwapPara& paraRes);
		static bool SwapBySwap(const CfSwapPara& para, const CfSwapPara& paraTrs, CfSwapPara& paraRes);
	};
}

#endif // __CONDFMT_TF_H__
