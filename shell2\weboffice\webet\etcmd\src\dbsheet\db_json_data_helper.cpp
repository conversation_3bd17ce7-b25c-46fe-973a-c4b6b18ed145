﻿#include "db_json_data_helper.h"
#include "etcore/et_core_dbsheet_enum.h"


// json type name
#define _json_type_name_str         "str"
#define _json_type_name_num         "num"
#define _json_type_name_time        "time"
#define _json_type_name_sel         "sel"
#define _json_type_name_usr         "usr"
#define _json_type_name_url         "url"
#define _json_type_name_addr        "addr"
#define _json_type_name_dept        "dept"
#define _json_type_name_atta        "atta"
#define _json_type_name_note        "note"
// json key
#define _json_key_name_from         "from"
#define _json_key_name_datas        "datas"
#define _json_key_name_t            "t"
#define _json_key_name_v            "v"
#define _json_key_name_d            "d"
#define _json_key_name_h            "h"
#define _json_key_name_list         "list"
#define _json_key_name_detail       "detail"
#define _json_key_name_districts    "districts"
#define _json_key_name_id           "id"
#define _json_key_name_name         "name"
#define _json_key_name_type         "type"
#define _json_key_name_link         "link"
#define _json_key_name_size         "size"
#define _json_key_name_source       "source"
#define _json_key_name_imageSize    "imageSize"
#define _json_key_name_date         "date"
#define _json_key_name_summary      "summary"
#define _json_key_name_avatar       "avatar"
#define _json_key_name_companyId    "companyId"
#define _json_key_name_sheetId      "sheetStId"
#define _json_key_name_viewId       "viewId"
#define _json_key_name_recIdLow     "recIdLow" // recId是64位的，存储的时候分别存上下32位
#define _json_key_name_recIdHigh    "recIdHigh"
#define _json_key_name_parent       "pidx"
#define _json_key_name_cells        "cells"

// 是否空单元格
#define _json_key_name_blank        "isblank"


namespace wo
{
    DBJsonDataType DBJsonDataHelper::TranslateFieldTypeToCopyDataType(ET_DbSheet_FieldType fldType)
    {
        switch (fldType)
        {
            case Et_DbSheetField_MultiLineText:                   // 多行文本
            case Et_DbSheetField_SingleLineText:                  // 单行文本
            case Et_DbSheetField_ID:                              // 身份证
            case Et_DbSheetField_Phone:                           // 电话
            case Et_DbSheetField_Email:                           // 电子邮箱
            case Et_DbSheetField_Formula:                         // 公式
            case Et_DbSheetField_Lookup:                          // 引用
            case Et_DbSheetField_ParentRecord:                    // 父记录
            case Et_DbSheetField_AutoNumber:                      // 编号
            case Et_DbSheetField_Automations:                     // 触发器
            case Et_DbSheetField_CellPicture:                     // 单元格图片
            case Et_DbSheetField_Cascade:                         // 级联选项
            case Et_DbSheetField_Button:
            case Et_DbSheetField_BarCode:
                return DBJsonDataType_Str;
            case Et_DbSheetField_Date:                            // 日期
            case Et_DbSheetField_Time:                            // 时间
            case Et_DbSheetField_CreatedTime:                     // 创建时间
            case Et_DbSheetField_LastModifiedTime:                // 最后修改时间
                return DBJsonDataType_Time;
            case Et_DbSheetField_Number:                          // 数值
            case Et_DbSheetField_Checkbox:                        // 复选框
            case Et_DbSheetField_Rating:                          // 等级
            case Et_DbSheetField_Complete:                        // 进度条
            case Et_DbSheetField_Currency:                        // 货币
            case Et_DbSheetField_Percentage:                      // 百分比
                return DBJsonDataType_Num;
            case Et_DbSheetField_SingleSelect:                    // 单选项
            case Et_DbSheetField_MultipleSelect:                  // 多选项
                return DBJsonDataType_Sel;
            case Et_DbSheetField_Url:                             // 超链接
                return DBJsonDataType_Url;
            case Et_DbSheetField_Contact:                         // 联系人
            case Et_DbSheetField_CreatedBy:                       // 创建者
            case Et_DbSheetField_LastModifiedBy:                  // 最后修改者
                return DBJsonDataType_Usr;
            case Et_DbSheetField_Address:                         // 地址
                return DBJsonDataType_Addr;
            case Et_DbSheetField_Department: 
                return DBJsonDataType_Dept;
            case Et_DbSheetField_Attachment:                      // Attachment
                return DBJsonDataType_Atta;
            case Et_DbSheetField_Note:                            // Embedded FlexPaper, RichEdit
                return DBJsonDataType_Note;
            case Et_DbSheetField_Link:                            // 关联
            case Et_DbSheetField_OneWayLink:                      // 单向关联
                return DBJsonDataType_Link;
        }
        return DBJsonDataType_Str;
    }

DBJsonDataType DBJsonDataHelper::getCellType(QString ws)
{
    DBJsonDataType cellType = DBJsonDataType_Str;
    if (ws == _json_type_name_str)
        cellType = DBJsonDataType_Str;
    else if (ws == _json_type_name_time)
        cellType = DBJsonDataType_Time;
    else if (ws == _json_type_name_num)
        cellType = DBJsonDataType_Num;
    else if (ws == _json_type_name_sel)
        cellType = DBJsonDataType_Sel;
    else if (ws == _json_type_name_url)
        cellType = DBJsonDataType_Url;
    else if (ws == _json_type_name_usr)
        cellType = DBJsonDataType_Usr;
    else if (ws == _json_type_name_addr)
        cellType = DBJsonDataType_Addr;
    else if (ws == _json_type_name_dept)
        cellType = DBJsonDataType_Dept;
    else if (ws == _json_type_name_atta)
        cellType = DBJsonDataType_Atta;
    else if (ws == _json_type_name_note)
        cellType = DBJsonDataType_Note;
    else if (ws == _json_key_name_link)
        cellType = DBJsonDataType_Link;
    return cellType;
}

HRESULT DBJsonDataHelper::parse(QJsonDocument jsonDoc)
{
    if (!jsonDoc.isObject())
        return E_INVALIDARG;
    
    QJsonObject jo = jsonDoc.object();
    if (!jo.contains(_json_key_name_datas) || !jo.contains(_json_key_name_from))
        return E_INVALIDARG;

    m_srcFileId = krt::utf16(jo.value(_json_key_name_from).toString());

    QJsonValue datas = jo.value(_json_key_name_datas);
    if (!datas.isArray())
        return E_INVALIDARG;

    QJsonArray ja_row = datas.toArray();
    if (ja_row.count() <= 0)
        return E_INVALIDARG;

    QJsonValue jv_temp;
    int rowCnt = ja_row.count();
    int colCnt = 0;
    for (int i = 0; i < rowCnt; ++i)
    {
        jv_temp = ja_row.at(i);
        QJsonArray ja_col;
        std::shared_ptr<DBJsonRecord> record(new DBJsonRecord());
        if (jv_temp.isObject())
        {
            ja_col = jv_temp[_json_key_name_cells].toArray();
            if (m_handleSubRecord)
            {
                EtDbIdx parent = jv_temp[_json_key_name_parent].toInt(INV_EtDbIdx);
                record->setParentRecordIdx(parent);
            }
        }
        else
            return E_INVALIDARG;

        if (colCnt == 0)
            colCnt = ja_col.count();
        else if (colCnt != ja_col.count())
            return E_INVALIDARG;
        
        for (int j = 0; j < colCnt; ++j)
        {
            jv_temp = ja_col.at(j);
            if (!jv_temp.isObject())
                return E_INVALIDARG;
            QJsonObject jo_rec = jv_temp.toObject();

            DBJsonDataType dataType = DBJsonDataType_Str;
            if (jo_rec.contains(_json_key_name_t))
                dataType = getCellType(jo_rec.value(_json_key_name_t).toString(""));

            switch(dataType)
            {
                case DBJsonDataType_Str: record->addCellData(new DBJsonDataString(jo_rec)); break;
                case DBJsonDataType_Time: record->addCellData(new DBJsonDataTime(jo_rec)); break;
                case DBJsonDataType_Num: record->addCellData(new DBJsonDataNumber(jo_rec)); break;
                case DBJsonDataType_Sel: record->addCellData(new DBJsonDataSelect(jo_rec)); break;
                case DBJsonDataType_Url: record->addCellData(new DBJsonDataUrl(jo_rec)); break;
                case DBJsonDataType_Usr: record->addCellData(new DBJsonDataUser(jo_rec)); break;
                case DBJsonDataType_Addr: record->addCellData(new DBJsonDataAddress(jo_rec)); break;
                case DBJsonDataType_Dept: record->addCellData(new DBJsonDataDepartment(jo_rec)); break;
                case DBJsonDataType_Atta: record->addCellData(new DBJsonDataAttachment(jo_rec)); break;
                case DBJsonDataType_Note: record->addCellData(new DBJsonDataNote(jo_rec)); break;
                case DBJsonDataType_Link: record->addCellData(new DBJsonDataLink(jo_rec)); break;
                default: record->addCellData(nullptr); break;
            }
        }

        m_records.push_back(record);
    }

    m_rowCnt = rowCnt;
    m_colCnt = colCnt;

    return S_OK;
}

void DBJsonDataHelper::toJson(ks_wstring& jsonString)
{
    QJsonObject jo_doc;
    QJsonArray json_rows;
    for (UINT i = 0; i < m_records.size(); ++i)
    {
        DBJsonRecord* record = m_records.at(i).get();
        QJsonObject json_record;
        QJsonArray json_cells;
        for (UINT j = 0, count = record->getCellCount(); j < count; ++j)
        {
            DBJsonDataBaseObject* obj = record->getCellData(j);
            if (obj)
            {
                QJsonObject json_obj;
                obj->SerialContent(json_obj);
                json_cells.append(json_obj);
            }
        }
        json_record.insert(_json_key_name_cells, json_cells);
        if (m_handleSubRecord && record->getParentRecordIdx() != INV_EtDbIdx)
            json_record.insert(_json_key_name_parent, QJsonValue((qint64)record->getParentRecordIdx()));
        json_rows.append(json_record);
    }

    jo_doc.insert(_json_key_name_datas, json_rows);
    jo_doc.insert(_json_key_name_from, QJsonValue(QString::fromUtf16(m_srcFileId.c_str())));

    QString str = QJsonDocument(jo_doc).toJson(QJsonDocument::Compact);
    jsonString.assign(krt::utf16(str), str.size()); 
}

void DBJsonDataHelper::SetSrcFileId(PCWSTR fileId)
{
    m_srcFileId = fileId;
}

PCWSTR DBJsonDataHelper::GetSrcFileId()
{
    return m_srcFileId.c_str();
}

UINT DBJsonDataHelper::GetColCount() 
{ 
    return m_colCnt;
}

UINT DBJsonDataHelper::GetRowCount() 
{ 
    return m_rowCnt; 
}

void DBJsonDataHelper::GetString(UINT r, UINT c, ks_wstring& res)
{
    CellOBJ* obj = GetCellData(r, c);
    if (obj != NULL) 
        obj->GetStringValue(res);
}

DBJsonDataHelper::CellOBJ* DBJsonDataHelper::GetCellData(UINT r, UINT c)
{
    if (r < m_records.size())
    {
        DBJsonRecord* rec = m_records[r].get();
        if (c < rec->getCellCount())
        {
            return rec->getCellData(c);
        }
    }
    return nullptr;
}

void DBJsonDataHelper::AddRecord(DBJsonRecord* record)
{
    m_records.emplace_back(record);
}

const DBJsonRecord* DBJsonDataHelper::GetRecord(UINT r)
{
    return r < m_records.size() ? m_records[r].get(): nullptr; 
}

//////////////////////////////////////////////////////////////////////
// serial json

/// str
DBJsonDataString::DBJsonDataString(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    m_value = jsonObj.contains(_json_key_name_v) ? krt::utf16(jsonObj.value(_json_key_name_v).toString("")) : __X("");
}

void DBJsonDataString::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_str);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    jsonObj.insert(_json_key_name_v, QJsonValue(QString::fromUtf16(m_value.c_str())));
}

/// time
DBJsonDataTime::DBJsonDataTime(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    m_value = jsonObj.contains(_json_key_name_v) ? krt::utf16(jsonObj.value(_json_key_name_v).toString("")) : __X("");
    m_d = jsonObj.contains(_json_key_name_d) ? jsonObj.value(_json_key_name_d).toDouble(0) : 0;
}

void DBJsonDataTime::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_time);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    jsonObj.insert(_json_key_name_v, QJsonValue(QString::fromUtf16(m_value.c_str())));
    jsonObj.insert(_json_key_name_d, m_d);  
} 

/// num
DBJsonDataNumber::DBJsonDataNumber(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    m_value = jsonObj.contains(_json_key_name_v) ? krt::utf16(jsonObj.value(_json_key_name_v).toString("")) : __X("");
    m_d = jsonObj.contains(_json_key_name_d) ? jsonObj.value(_json_key_name_d).toDouble(0) : 0;
}

void DBJsonDataNumber::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_num);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    jsonObj.insert(_json_key_name_v, QJsonValue(QString::fromUtf16(m_value.c_str())));
    jsonObj.insert(_json_key_name_d, m_d);
}

/// sel
DBJsonDataSelect::DBJsonDataSelect(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    if (jsonObj.contains(_json_key_name_list))
    {
        QJsonValue jv_temp = jsonObj.value(_json_key_name_list);
        if (jv_temp.isArray())
        {
            QJsonArray ja_list = jv_temp.toArray();
            for (int li = 0; li < ja_list.count(); ++li)
            {
                jv_temp = ja_list.at(li);
                if (jv_temp.isString())
                    AddSelectItem(krt::utf16(jv_temp.toString()));
            }
        }
    }
}

void DBJsonDataSelect::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_sel);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    QJsonArray ja_list;
    for (int32 i = 0; i < m_selects.size(); ++i)
    {
        ja_list.append(QJsonValue(QString::fromUtf16(m_selects.at(i).c_str())));
    }
    jsonObj.insert(_json_key_name_list, ja_list);
}

/// url
DBJsonDataUrl::DBJsonDataUrl(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    m_address = jsonObj.contains(_json_key_name_h) ? krt::utf16(jsonObj.value(_json_key_name_h).toString("")) : __X("");
    m_displayText = jsonObj.contains(_json_key_name_v) ? krt::utf16(jsonObj.value(_json_key_name_v).toString("")) : __X("");
}

void DBJsonDataUrl::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_url);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    jsonObj.insert(_json_key_name_v, QJsonValue(QString::fromUtf16(m_displayText.c_str())));
    jsonObj.insert(_json_key_name_h, QJsonValue(QString::fromUtf16(m_address.c_str())));
}

/// usr
DBJsonDataUser::DBJsonDataUser(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    if (jsonObj.contains(_json_key_name_list))
    {
        QJsonValue jv_temp = jsonObj.value(_json_key_name_list);
        if (jv_temp.isArray())
        {
            QJsonArray ja_list = jv_temp.toArray();
            for (int li = 0; li < ja_list.count(); ++li)
            {
                jv_temp = ja_list.at(li);
                if (jv_temp.isObject())
                {
                    QJsonObject jo_item = jv_temp.toObject();
                    DBJsonDataUserItem user;
                    user.id = jo_item.contains(_json_key_name_id) ? krt::utf16(jo_item.value(_json_key_name_id).toString("")) : __X("");
                    user.name = jo_item.contains(_json_key_name_name) ? krt::utf16(jo_item.value(_json_key_name_name).toString("")) : __X("");
                    user.avatar = jo_item.contains(_json_key_name_avatar) ? krt::utf16(jo_item.value(_json_key_name_avatar).toString("")) : __X("");
                    user.companyId = jo_item.contains(_json_key_name_companyId) ? krt::utf16(jo_item.value(_json_key_name_companyId).toString("")) : __X("");
                    AddUserItem(user);
                }
            }
        }
    }
}

void DBJsonDataUser::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_usr);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    QJsonArray ja_list;
    for (int32 i = 0; i < m_items.size(); ++i)
    {
        DBJsonDataUserItem &item = m_items.at(i);
        QJsonObject jo_item;
        jo_item.insert(_json_key_name_name, QString::fromUtf16(item.name.c_str()));
        jo_item.insert(_json_key_name_id, QString::fromUtf16(item.id.c_str()));
        jo_item.insert(_json_key_name_avatar, QString::fromUtf16(item.avatar.c_str()));
        jo_item.insert(_json_key_name_companyId, QString::fromUtf16(item.companyId.c_str()));
        ja_list.append(jo_item);
    }
    jsonObj.insert(_json_key_name_list, ja_list);
}

/// addr
DBJsonDataAddress::DBJsonDataAddress(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    SetDetail(jsonObj.contains(_json_key_name_detail) ? krt::utf16(jsonObj.value(_json_key_name_detail).toString("")) : __X(""));
    if (jsonObj.contains(_json_key_name_districts))
    {
        QJsonValue jv_temp = jsonObj.value(_json_key_name_districts);
        if (jv_temp.isArray())
        {
            QJsonArray ja_list = jv_temp.toArray();
            for (int li = 0; li < ja_list.count(); ++li)
            {
                jv_temp = ja_list.at(li);
                if (jv_temp.isString())
                    AddDistrict(krt::utf16(jv_temp.toString()));
            }
        }
    }
}

void DBJsonDataAddress::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_addr);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    jsonObj.insert(_json_key_name_detail, QJsonValue(QString::fromUtf16(GetDetail())));
    QJsonArray ja_list;
    for (int32 i = 0; i < m_districts.size(); ++i)
    {
        ja_list.append(QString::fromUtf16(m_districts.at(i).c_str()));
    }
    jsonObj.insert(_json_key_name_districts, ja_list);
}

DBJsonDataDepartment::DBJsonDataDepartment(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    if (jsonObj.contains(_json_key_name_list))
    {
        QJsonValue jv_temp = jsonObj.value(_json_key_name_list);
        if (jv_temp.isArray())
        {
            QJsonArray ja_list = jv_temp.toArray();
            for (int i = 0; i < ja_list.count(); ++i)
            {
                jv_temp = ja_list.at(i);
                if (jv_temp.isObject())
                {
                    QJsonObject jo_item = jv_temp.toObject();
                    DBJsonDataAddress  departmentItem;
                    departmentItem.SetDetail(jo_item.contains(_json_key_name_detail) ? krt::utf16(jo_item.value(_json_key_name_detail).toString("")) : __X(""));
                    if (jo_item.contains(_json_key_name_districts))
                    {
                        QJsonValue jv_districts = jo_item.value(_json_key_name_districts);
                        if (jv_districts.isArray())
                        {
                            QJsonArray list = jv_districts.toArray();
                            for (int j = 0; j < list.count(); ++j)
                            {
                                departmentItem.AddDistrict(krt::utf16(list.at(j).toString()));
                            }
                        }
                    }
                    AddDepartmentItem(departmentItem);
                }
            }
        }
    }
}

void DBJsonDataDepartment::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_dept);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    QJsonArray ja_list;
    for (int32 i = 0; i < m_items.size(); ++i)
    {
        DBJsonDataAddress &item = m_items.at(i);
        QJsonObject jo_item;
        jo_item.insert(_json_key_name_detail, krt::fromUtf16(item.GetDetail()));
        QJsonArray list;
        for (int32 j = 0; j < item.GetDistrictsCount(); ++j)
        {
            list.append(krt::fromUtf16(item.GetDistrict(j)));
        }
        jo_item.insert(_json_key_name_districts, list);
        ja_list.append(jo_item);
    }
    jsonObj.insert(_json_key_name_list, ja_list);
}

/// atta
DBJsonDataAttachment::DBJsonDataAttachment(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    if (jsonObj.contains(_json_key_name_list))
    {
        QJsonValue jv_temp = jsonObj.value(_json_key_name_list);
        if (jv_temp.isArray())
        {
            QJsonArray ja_list = jv_temp.toArray();
            for (int li = 0; li < ja_list.count(); ++li)
            {
                jv_temp = ja_list.at(li);
                if (jv_temp.isObject())
                {
                    QJsonObject jo_item = jv_temp.toObject();
                    DBJsonDataAttachmentItem attachment;
                    attachment.id = jo_item.contains(_json_key_name_id) ? krt::utf16(jo_item.value(_json_key_name_id).toString("")) : __X("");
                    attachment.type = jo_item.contains(_json_key_name_type) ? krt::utf16(jo_item.value(_json_key_name_type).toString("")) : __X("");
                    attachment.size = jo_item.contains(_json_key_name_size) ? jo_item.value(_json_key_name_size).toInt(0) : 0;
                    attachment.imageSize = jo_item.contains(_json_key_name_imageSize) ? krt::utf16(jo_item.value(_json_key_name_imageSize).toString("")) : __X("");
                    attachment.link = jo_item.contains(_json_key_name_link) ? krt::utf16(jo_item.value(_json_key_name_link).toString("")) : __X("");
                    attachment.source = jo_item.contains(_json_key_name_source) ? jo_item.value(_json_key_name_source).toInt(0) : 0;
                    attachment.name = jo_item.contains(_json_key_name_name) ? krt::utf16(jo_item.value(_json_key_name_name).toString("")) : __X("");
                    AddAttachmentItem(attachment);
                }
            }
        }
    }
}

void DBJsonDataAttachment::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_atta);
    if (TRUE == m_bBlank) 
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    QJsonArray ja_list;
    for (int32 i = 0; i < m_items.size(); ++i)
    {
        DBJsonDataAttachmentItem &item = m_items.at(i);
        QJsonObject jo_item;
        jo_item.insert(_json_key_name_id, QString::fromUtf16(item.id.c_str()));
        jo_item.insert(_json_key_name_type, QString::fromUtf16(item.type.c_str()));
        jo_item.insert(_json_key_name_name, QString::fromUtf16(item.name.c_str()));
        jo_item.insert(_json_key_name_size, item.size);
        jo_item.insert(_json_key_name_imageSize, QString::fromUtf16(item.imageSize.c_str()));
        jo_item.insert(_json_key_name_link, QString::fromUtf16(item.link.c_str()));
        jo_item.insert(_json_key_name_source, item.source);
        ja_list.append(jo_item);
    }
    jsonObj.insert(_json_key_name_list, ja_list);
}

/// note
DBJsonDataNote::DBJsonDataNote(QJsonObject jsonObj)
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    if (jsonObj.contains(_json_key_name_list))
    {
        QJsonValue jv_temp = jsonObj.value(_json_key_name_list);
        if (jv_temp.isArray())
        {
            QJsonArray ja_list = jv_temp.toArray();
            for (int li = 0; li < ja_list.count(); ++li)
            {
                jv_temp = ja_list.at(li);
                if (jv_temp.isObject())
                {
                    QJsonObject jo_item = jv_temp.toObject();
                    DBJsonDataNoteItem note;
                    note.attachmentId = jo_item.contains(_json_key_name_id) ? krt::utf16(jo_item.value(_json_key_name_id).toString("")) : __X("");
                    note.shortSummary = jo_item.contains(_json_key_name_summary) ? krt::utf16(jo_item.value(_json_key_name_summary).toString("")) : __X("");
                    note.modifyDate = jo_item.contains(_json_key_name_date) ? jo_item.value(_json_key_name_date).toDouble(0) : 0;
                    AddNoteItem(note);
                }
            }
        }
    }
    else
    {
        DBJsonDataNoteItem note;
        note.attachmentId = jsonObj.contains(_json_key_name_id) ? krt::utf16(jsonObj.value(_json_key_name_id).toString("")) : __X("");
        note.shortSummary = jsonObj.contains(_json_key_name_summary) ? krt::utf16(jsonObj.value(_json_key_name_summary).toString("")) : __X("");
        note.modifyDate = jsonObj.contains(_json_key_name_date) ? jsonObj.value(_json_key_name_date).toDouble(0) : 0;
        AddNoteItem(note);
    }
}

void DBJsonDataNote::SerialContent(QJsonObject& jsonObj)
{
    jsonObj.insert(_json_key_name_t, _json_type_name_note);
    if (TRUE == m_bBlank || m_items.size() == 0)
    {
        jsonObj.insert(_json_key_name_blank, true);
        return;
    }
    QJsonArray ja_list;
    int count = m_items.size();
    for (int32 i = 0; i < count; ++i)
    {
        DBJsonDataNoteItem &item = m_items.at(i);
        QJsonObject jo_item;
        jo_item.insert(_json_key_name_id, QString::fromUtf16(item.attachmentId.c_str()));
        jo_item.insert(_json_key_name_summary, QString::fromUtf16(item.shortSummary.c_str()));
        jo_item.insert(_json_key_name_date, item.modifyDate);
        ja_list.append(jo_item);
    }
    jsonObj.insert(_json_key_name_list, ja_list);
}


DBJsonDataLink::DBJsonDataLink(QJsonObject jsonObj) 
{
    m_bBlank = jsonObj.contains(_json_key_name_blank) && jsonObj.value(_json_key_name_blank).toBool() ? TRUE : FALSE; 
    if (TRUE == m_bBlank) return;
    if (jsonObj.contains(_json_key_name_sheetId))
    {
        m_sheetStId = static_cast<UINT>(jsonObj.value(_json_key_name_sheetId).toDouble());
        if (jsonObj.contains(_json_key_name_viewId))
        {
            bool success = false;
            kso_qt::quint64 viewId = jsonObj.value(_json_key_name_viewId).toString().toULongLong(&success);
            ASSERT(success);
            m_viewId = viewId;
        }
        if (jsonObj.contains(_json_key_name_recIdLow))
        {
            kso_qt::QJsonValue jv_low = jsonObj.value(_json_key_name_recIdLow);
            if (jv_low.isArray())
            {
                kso_qt::QJsonArray ja_low = jv_low.toArray();
                // 如果不需要拼接，则不会有_json_key_name_recIdHigh
                bool bNeedJoin = jsonObj.contains(_json_key_name_recIdHigh) && jsonObj.value(_json_key_name_recIdHigh).isArray();
                kso_qt::QJsonArray ja_high =  bNeedJoin ? jsonObj.value(_json_key_name_recIdHigh).toArray() : QJsonArray();
                auto count = ja_low.count();
                AssignContainerSize(count);
                for (int i = 0 ; i < count; ++i)
                {
                    UINT low = static_cast<UINT>(ja_low.at(i).toDouble());
                    UINT high = bNeedJoin ? static_cast<UINT>(ja_high.at(i).toDouble()) : 0;
                    EtDbId recId = low | (static_cast<EtDbId>(high) << 32);
                    AddRecordItem(recId); 
                }
            }
        }
    }
    m_recordPrimaryFieldString = jsonObj.contains(_json_key_name_d) ? krt::utf16(jsonObj.value(_json_key_name_d).toString("")) : __X("");
}

void DBJsonDataLink::GetStringValue(ks_wstring &value) 
{
    value = m_recordPrimaryFieldString;
}

void DBJsonDataLink::SerialContent(QJsonObject& jsonObj) 
{
    jsonObj.insert(_json_key_name_t, _json_key_name_link);
    jsonObj.insert(_json_key_name_sheetId, static_cast<double>(m_sheetStId));
    jsonObj.insert(_json_key_name_viewId, kso_qt::QString::number(m_viewId));
    // EtDbId是UINT64，Qt中Json库使用double无法全部表示，这里使用拆分成上下32位的方式来实现
    kso_qt::QJsonArray lowArray, highArray;
    bool bNeedHigh32 = false;
    for (const auto& recId : m_linkRecords)
    {
        if (recId > UINT32_MAX)
        {
            bNeedHigh32 = true;
            break;
        }
    }
    for (const auto& recId : m_linkRecords)
    {
        UINT low = static_cast<UINT>(recId & 0xFFFFFFFF);
        lowArray.append(static_cast<double>(low));
        if (bNeedHigh32)
        {
            UINT high = static_cast<UINT>(recId >> 32);
            highArray.append(static_cast<double>(high));
        }
    }
    jsonObj.insert(_json_key_name_recIdLow, lowArray);
    if (bNeedHigh32)
        jsonObj.insert(_json_key_name_recIdHigh, highArray);
    jsonObj.insert(_json_key_name_d, kso_qt::QString::fromUtf16(m_recordPrimaryFieldString.c_str()));
}
}