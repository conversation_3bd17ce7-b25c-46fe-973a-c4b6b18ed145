﻿#include "db_sidebar_folder_tree_helper.h"
#include "etcmd/src/workbooks.h"
#include "et_dbsheet_utils.h"
#include "util.h"

namespace wo
{
DbSidebarFolderTreeHelper::DbSidebarFolderTreeHelper(KEtWorkbook* pWorkbook)
    : m_pBook(pWorkbook->GetCoreWorkbook()->GetBook())
{}

////////////////////////////////////////////////////////////////////////
DbSidebarFolderTreeAddHelper::DbSidebarFolderTreeAddHelper(KEtWorkbook* wwb) 
    : DbSidebarFolderTreeHelper(wwb) 
{}

HRESULT DbSidebarFolderTreeAddHelper::MoveSheet(binary_wo::VarObj param, UINT stId)
{
    if (!param.has("sidebarFolderTree"))
        return E_FAIL;
    binary_wo::VarObj sidebarFolderTreeObj = param.get_s("sidebarFolderTree");
    if (!sidebarFolderTreeObj.has("target"))
        return E_FAIL;
    binary_wo::VarObj targetObj = sidebarFolderTreeObj.get_s("target");
    if (!targetObj.has("id"))
        return E_FAIL;
    EtDbId tarId = DbSheet::GetEtDbId(targetObj, "id");
    if (targetObj.has("before"))
    {
        binary_wo::VarObj beforeObj = targetObj.get_s("before");
        if (!beforeObj.has("id"))
            return E_FAIL;
        EtDbId relId = DbSheet::GetEtDbId(beforeObj, "id");
        return MoveSheetPart(tarId, stId, DbSidebarFolderTreeRelPos_Before, relId);
    }
    else if (targetObj.has("after"))
    {
        binary_wo::VarObj afterObj = targetObj.get_s("after");
        if (!afterObj.has("id"))
            return E_FAIL;
        EtDbId relId = DbSheet::GetEtDbId(afterObj, "id");
        return MoveSheetPart(tarId, stId, DbSidebarFolderTreeRelPos_After, relId);
    } 
    else
    {
        return MoveSheetPart(tarId, stId, DbSidebarFolderTreeRelPos_PushBack, 0);
    }

    return E_FAIL;
}

HRESULT DbSidebarFolderTreeAddHelper::MoveSheetPart(EtDbId targetId, UINT stId, DbSidebarFolderTreeRelPos relPos, EtDbId relId)
{
    ks_stdptr<IDbSidebarFolderTreeManager> spMgr;
    m_pBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spMgr);
    if (!spMgr)
        return E_FAIL;

    EtDbId nodeId = INV_EtDbId;
	if (SUCCEEDED(spMgr->GetNodeIdByStId(stId, &nodeId)))
		return spMgr->Move(targetId, nodeId, relPos, relId);

    return E_FAIL;
}

////////////////////////////////////////////////////////////////////////
DbSidebarFolderTreeCopyHelper::DbSidebarFolderTreeCopyHelper(KEtWorkbook* wwb) 
    : DbSidebarFolderTreeHelper(wwb) 
{}

HRESULT DbSidebarFolderTreeCopyHelper::MoveSheet(UINT stId, UINT relStId)
{
    ks_stdptr<IDbSidebarFolderTreeManager> spMgr;
    m_pBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spMgr);
    if (!spMgr)
        return E_FAIL;

    EtDbId nodeId = INV_EtDbId;
    EtDbId relNodeId = INV_EtDbId;
	if (SUCCEEDED(spMgr->GetNodeIdByStId(stId, &nodeId)) && SUCCEEDED(spMgr->GetNodeIdByStId(relStId, &relNodeId)))
	 	return spMgr->MoveByCopy(nodeId, DbSidebarFolderTreeRelPos_After, relNodeId);

    return E_FAIL;
}
} // namespace wo