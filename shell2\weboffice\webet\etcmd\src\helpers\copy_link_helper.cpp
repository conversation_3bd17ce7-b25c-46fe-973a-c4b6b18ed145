﻿#include "copy_link_helper.h"
#include "etcore/et_core_basic.h"
#include "persist/et_persist_basic_itf.h"


namespace wo 
{

namespace CopyLink 
{
    bool isSuppportCopyLink(IBook* pBook)
    {
        BOOKERRORS* pErr = NULL; 
        pBook->GetBookErrors(&pErr);
        switch(pErr->fFileFormat)
        {
            case ffXLSX:
            case ffXLSM:
            case ffKSHEET:
            case ffXLTM:
            case ffXLT:
            case ffXLTX:
            {
                return true;
            }

        }
        return false;
    }
}

}
