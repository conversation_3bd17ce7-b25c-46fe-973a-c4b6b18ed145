﻿#ifndef __WEBET_ET_TASK_PERIPHERAL_H__
#define __WEBET_ET_TASK_PERIPHERAL_H__

#include <public_header/revision/src/kwrevisionctrl.h>
#include "etcore/et_core_sheet.h"

namespace wo
{
class KEtWorkbook;
class EtTaskPeripheral;
interface IEtRevisionContext;

class ScopeRcHeader
{
public:
	ScopeRcHeader(EtTaskPeripheral*, KwCommand*, IEtRevisionContext*, const HRESULT& hr);
	~ScopeRcHeader();

protected:
	EtTaskPeripheral* m_taskPer;
	KwCommand* m_cmd;
	IEtRevisionContext* m_ctx;
	INT32 m_idxSheet;
	const HRESULT& m_hr;
};

class EtTaskPeripheral
{
public:
	typedef RANGE (*RangeReader)(IBook*, KwCommand*);

public:
	EtTaskPeripheral(bool bPre, bool bPost, KEtWorkbook* wwb);
	virtual ~EtTaskPeripheral();

	//影响PreProgress, PostProgress里面读取range的行为
	void installRangeReader(RangeReader);
	void setDefBanUndo(KwVersion::BanUndoType tp);
	void setBanUndo(KwVersion::BanUndoType tp);
	void setCheckRange(bool bCheckRange);
	void setRcHeaderRecord();
	void setCareFilterHidden();
	void setEffectedRg(const std::vector<RANGE>& rgVec);

public:
	INT32 rcHeaderBeginRecord(KwCommand*);
	void rcHeaderEndRecord(INT32 idxSheet, KwCommand*, IEtRevisionContext*, bool bCancel);
	void cacheCurFilterHiddenIfNeeded(KwCommand*, IEtRevisionContext*, bool bPreCache = false);

public:
	virtual void PreProgress(KwCommand*, bool* bDenyOp, binary_wo::VarObj resOthers, binary_wo::VarObj resSelf);
	virtual KwVersion::BanUndoType getBanUndo();

	virtual void PostProgress(KwCommand*);
	binary_wo::VarObj resSelf() { return m_resSelf; }
	binary_wo::VarObj resOthers() { return m_resOthers; }

protected:
	RANGE ReadRange(KwCommand*);

protected:
	bool m_bPre;
	bool m_bPost;
	KwVersion::BanUndoType m_banUndo; // 每次命令执行前复位成 m_banUndoDef
	KwVersion::BanUndoType m_banUndoDef;
	bool m_bCheckRange;
	bool m_bRcHeaderRecord;
	bool m_bCareFilterHidden;
	KEtWorkbook* m_wwb;
	RangeReader m_rangeReader;
	binary_wo::VarObj m_resOthers;
	binary_wo::VarObj m_resSelf;
	std::vector<RANGE> m_rgEffected;
};

class KEtTaskPeripheralFullRC: public EtTaskPeripheral
{
public:
	KEtTaskPeripheralFullRC(KEtWorkbook* wwb);

public:
	void PreProgress(KwCommand*, bool* bDenyOp, binary_wo::VarObj resOthers, binary_wo::VarObj resSelf) override;
};

class KEtMultiRangeTaskPeripheral: public EtTaskPeripheral
{
public:
	KEtMultiRangeTaskPeripheral(bool bPre, bool bPost, KEtWorkbook* wwb);

public:
	virtual void PreProgress(KwCommand*, bool* bDenyOp, binary_wo::VarObj resOthers, binary_wo::VarObj resSelf) override;
	virtual void PostProgress(KwCommand*) override;
protected:
	void ReadRanges(KwCommand*, std::vector<RANGE> &);
	virtual INT64 GetTotalCellCount(const std::vector<RANGE> &);
};

class KEtRangesTaskPeripheral: public EtTaskPeripheral
{
public:
	KEtRangesTaskPeripheral(bool bPre, bool bPost, KEtWorkbook* wwb);

public:
	void PreProgress(KwCommand*, bool* bDenyOp, binary_wo::VarObj resOthers, binary_wo::VarObj resSelf) override;
protected:
	void ReadRanges(KwCommand*, std::vector<RANGE> &);
	void CheckDenyOp(bool* bDenyOp, const std::vector<RANGE> &);
};

class KEtMultiRangeTaskPeripheralFullRC: public KEtMultiRangeTaskPeripheral
{
public:
	KEtMultiRangeTaskPeripheralFullRC(KEtWorkbook* wwb);

protected:
	virtual INT64 GetTotalCellCount(const std::vector<RANGE> &) override;
};

}

#endif //__WEBET_ET_TASK_PERIPHERAL_H__
