﻿#ifndef __WO_SPLIT_BOOK_H__
#define __WO_SPLIT_BOOK_H__

#include "etcore/et_core_basic.h"
#include <public_header/revision/src/kwrevisionctrl.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include "applogic/et_applogic_i.h"
#include "webbase/serialize_impl.h"
#include "webbase/wo_sa_helper.h"

namespace wo
{
class KEtWorkbook;
class KEtRevisionContext;

struct SplitSheetToNewBookItem 
{
	IDX m_sheetIdx;
	ks_wstring m_sheetName;
	SplitSheetToNewBookItem(IDX sheetIdx, ks_wstring sheetName): m_sheetIdx(sheetIdx), m_sheetName(sheetName) {}
};

class SplitBookItem
{

public:
	SplitBookItem(ks_wstring fullPath, std::vector<ks_wstring> attachmentImageIds, std::vector<ks_wstring> cloudSpaceImageUrls, std::vector<ks_wstring> attachmentVideoIds)
		: m_fullPath(fullPath), m_attachmentImageIds(attachmentImageIds), m_cloudSpaceImageUrls(cloudSpaceImageUrls), m_attachmentVideoIds(attachmentVideoIds)
		{

		}

	void write(KSerialWrapBinWriter& write)
	{
		WOLOG_INFO << "SplitBookItem write";

		write.beginStruct();
		write.addString("path", m_fullPath.c_str());
		{
			sa::Leave cloudspaceLeave = sa::enterArray(&write, "cloudspace");
			for (const auto& url : m_cloudSpaceImageUrls)
			{
				sa::Leave anonymousLeave = sa::enterStruct(&write, nullptr);
				write.addString("url", url.c_str());
				write.addString("source", __X("drive"));
			}
		}

		{
			sa::Leave attachmentsLeave = sa::enterArray(&write, "videoAttachmentIds");

			for (const auto& attachmentId : m_attachmentVideoIds)
			{
				sa::Leave anonymousLeave = sa::enterStruct(&write, nullptr);
				write.addString("fileId", attachmentId.c_str());
				write.addString("source", __X("base_storage"));
			}
		}

		{
			sa::Leave attachmentsLeave = sa::enterArray(&write, "attachments");

			for (const auto& attachmentId : m_attachmentImageIds)
			{
				sa::Leave anonymousLeave = sa::enterStruct(&write, nullptr);
				write.addString("fileId", attachmentId.c_str());
				write.addString("source", __X("base_storage"));
			}
		}

		write.endStruct();
	}

private:
	ks_wstring m_fullPath;
	std::vector<ks_wstring> m_attachmentImageIds;
	std::vector<ks_wstring> m_attachmentVideoIds;
    std::vector<ks_wstring> m_cloudSpaceImageUrls;
};

class SplitResultContext
{

private:
	ks_wstring err;
	int m_firstSheetIndex;

public:
	SplitResultContext() : m_firstSheetIndex(-1) {}

public:
	ks_wstring& GetErrStr() { return err;}
	void SetFirstSheetIndex(int sheetIndex) { m_firstSheetIndex = sheetIndex; }
	int GetFirstSheetIndex() { return m_firstSheetIndex;}

};

class SplitBookCallBack : public IKAfSheetSpliterBookCallBack, public SplitResultContext
{

public:
	SplitBookCallBack(KEtRevisionContext* ctx) : pCtx(ctx) {}

private:
	std::vector<SplitBookItem> m_SplitBookItems;
	KEtRevisionContext* pCtx;

	QString m_emptySheetDefaultName;
	RANGE* m_pRange = nullptr;
	bool m_distribute = false;

public:
	STDPROC callBack(LPCWSTR fullPath, etoldapi::_Workbook* workbook);
	STDPROC postProcessCopySheet(etoldapi::_Worksheet* pSrc, etoldapi::_Worksheet* pDst);
	STDPROC_(const QString&) GetEmptySheetDefaultName () const;
	STDPROC init(bool distribute, RANGE* range);
	STDPROC_ (const bool) isDistribute () const;
	const bool isSelectionDeliver () const;
	RANGE* getRange() const;
	
public:

	void write(KSerialWrapBinWriter& write)
	{
		WOLOG_INFO << "SplitBookCallBack write";
		write.addKey("paths");
		write.beginArray();
		for (int i = 0; i < m_SplitBookItems.size(); i++)
		{
			m_SplitBookItems.at(i).write(write);
		}
		write.endArray();
	}

	void SetEmptySheetDefaultName(const ks_wstring& name)
	{
		m_emptySheetDefaultName = krt::fromUtf16(name.c_str());
	}

};

class SplitBookOpt
{

public:
	SplitBookOpt(wo::KEtWorkbook* wwb, KEtRevisionContext* ctx, binary_wo::VarObj* pParam) : m_wwb(wwb), m_ctx(ctx), m_pParam(pParam) {
		memset(&m_Rect, 0, sizeof(m_Rect));
		m_bHasHeader = false;
		m_bDisplayPreView = false;
		m_bDateType = false;
		m_selectIndex = 0;
	}

private:
	wo::KEtWorkbook* m_wwb;
	KEtRevisionContext* m_ctx;
	binary_wo::VarObj* m_pParam;

public:
	HRESULT InitData(int sheetIdX);

	void initDefaultRange(Range** spEXpRange);
	HRESULT GetRangeAddress(BSTR* pbsAddr, Range*spEXpRange, IKRanges** rgsTbl);
	ks_wstring get_column_name(ISheet* sht, COL col, bool isA1Ref);
	HRESULT GetAppSettings(ISheet* pSheet, IAppSettings** spAppSettings);

	bool initRect(IKRanges* spRg, bool bManualCheckIncludeTitle, bool bHasSelectRange, int selectIndex,
		bool* sp_bStartEnabled, bool* sp_bIsSameBook, bool* sp_cbIncludeTitleEnabled, bool* sp_cbIncludeTitleCheck, ks_wstring& errMsg);
		
	bool  checkIsListObjectHeader(const  RANGE*  rg, bool& headIsHide);

	bool checkRangeIsOneRow(const RANGE* rg);
	bool isDateTypeField(int nfield, bool bHasSelectRange);
	bool isDateType(const_token_ptr pToken, const XF* pXf, BOOL b1904);
	bool isRectValid(const RECT & rect);

	SplitSheetDateType GetSplitDateType(int idx);
	int GetPreViewCount(int dataTypeIndex);

	bool isA1Ref();
	bool isHasHeader() { return m_bHasHeader;}

	IKAutoFilter* GetIKAutoFilter() { return m_spAutoFilter;}
	ISheet* GetISheet() { return m_pSheet;}
	IETStringTools* GetIETStringTools() { return m_spStringTools;}
	RECT GetRECT() {return m_Rect;}

	void SetDateType(bool dateType) { m_bDateType = dateType;}

	bool isCanSplit(IBook* pBook, IDX sheetIdX);

	void handleFormat(binary_wo::VarObj& param);

	HRESULT split(int dataTypeIndex, bool bToSheet, const ks_wstring path, SplitResultContext* resultContext, SplitBookCallBack* splitBookCallBack = nullptr);

	HRESULT SplitNewBook(const std::vector<SplitSheetToNewBookItem>& sheetVec, SplitBookCallBack* splitBookCallBack, const ks_wstring& path);

	static HRESULT splitOpt(binary_wo::VarObj& param, bool bToSheet, const ks_wstring& path, SplitBookCallBack* splitBookCallBack, wo::KEtWorkbook* wwb, KEtRevisionContext* pCtx, SplitResultContext* resultContext);

	static HRESULT splitOptSheet(binary_wo::VarObj& param, wo::KEtWorkbook* wwb, KEtRevisionContext* pCtx, SplitResultContext* resultContext);
 	static HRESULT splitOptBook(binary_wo::VarObj& param, const ks_wstring& path, SplitBookCallBack* splitBookCallBack, wo::KEtWorkbook* wwb, KEtRevisionContext* pCtx);

	static bool IsProtected(wo::KEtWorkbook* wwb, IDX sheetIdx);

	BOOL isBookProtected();

protected:
	et_sptr<RANGE> m_spRg;
	RECT           m_Rect;
	ks_stdptr<IKAfSheetSpliter> m_spSheetSpliter;
	bool           m_bHasHeader;
	bool           m_bDisplayPreView;
	ks_stdptr<IETStringTools> m_spStringTools;
	ks_stdptr<IKAutoFilter> m_spAutoFilter;
	ks_stdptr<etoldapi::_Worksheet> m_spWorksheet;
	ks_stdptr<etoldapi::_Workbook> m_spWorkbook;
	ISheet* m_pSheet;

	ks_stdptr<IAppSettings> m_spAppSettings;

	bool m_bDateType;
	int m_selectIndex;

};
}
#endif // __WO_SPLIT_BOOK_H__