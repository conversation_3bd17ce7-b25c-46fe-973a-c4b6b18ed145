﻿#include "etcmd/etstdafx.h"
#include "kbase/string/ksstring.h"
#include "webetlink.h"
#include <public_header/opl/mvc/cellimage/et_cellimage_shape_tree.h>
#include "etcmd/src/etapplication.h"
#include "etcmd/src/exec_detail.h"
#include "etcmd/src/helpers/picture_upload_helper.h"
#include "krt/global.h"
#include "etcmd/src/et_revision_context_impl.h"
#include "webbase/binvariant/binreader.h"
#include <public_header/webcommon/src/security_doc.h>
#include <public_header/webcommon/src/execdetailcommon.h>
#include <public_header/webcommon/src/wodocmemostat.h>
#include <public_header/webcommon/src/wobuildcoopcmds.h>
#include "webbase/logger.h"
#include "etcore/wo_et_cell_history.h"
#include "webhook/webhook_data_create_helper_old.h"
#include "wo/core_stake.h"
#include "kso/kso_weboffice.h"
#include "webbase/calback.h"
#include "etcmd/src/fileInfoCollect.h"
#include "etcmd/src/censor/exportcensordata.h"
#ifdef Q_OS_UNIX
#include "sys/resource.h"
#endif
#if defined(__linux__) && defined(__x86_64__)
#include <public_header/webcommon/src/crash_report.h>
#endif

#include "etcmd/src/database/database.h"
#include "etcmd/src/database/database_field_context.h"
#include "etcmd/src/dbsheet/et_dbsheet_utils.h"
#include "etcmd/src/remote_chart/remote_chart_helper.h"
#include "etcmd/src/helpers/variable_restore_scope.h"
#include "etcmd/src/helpers/coreconquer_helper.h"
#include "etcmd/src/util.h"
#include "etcmd/src/woetsetting.h"
#include "etcmd/src/utils/pixel_img_util.h"
#include "ettools/ettools_encode_decoder.h"
#include "appcore/et_appcore_webhook.h"
#include "etcmd/src/webhook/webhook_data_create_helper.h"
#include "etcmd/src/hresult_to_string.h"
#include <public_header/weblog/weblog.h>
#include <public_header/webcommon/src/weblogutils.h>
#include "wo/wo_bookInfo_itf.h"
#include "collect.h"
#include "applogic/et_applogic_doc_slim.h"
#include "helpers/litefile_helper.h"
#include "etcore/et_core_supbook.h"
#include "etcmd/src/helpers/sheet_operator_helper.h"
#include "etcmd/src/et_binvar_spec.h"
#include "helpers/slim_helper.h"
#include "kso/framework/docslim_helper.h"
#include "etcmd/src/wo_data_analyze/data_analyze.h"
#include "etcore/et_core_event_tracking.h"
#include "etcore/et_core_timestat.h"
#include "etcmd/src/woeventdispatcher.h"

#include "etcmd/src/http/db_value_serialiser.h"
#include "webbase/serialize_impl.h"
#include "wo/sa_helper.h"
#include "etcmd/src/helpers/db_value_serialize_helper.h"
using namespace binary_wo;

#define BASE_PROCESS_CHECK_RET_FAIL	\
if (WebProcTypeBase == gs_procType)	\
{									\
	QString info = QString("[%1] Error Call").arg(__func__);	\
	callbackLog(WO_LOG_ERROR, info.toUtf8().data());	\
	return WO_FAIL;					\
}

#define BASE_PROCESS_CHECK_RET_VOID	\
if (WebProcTypeBase == gs_procType)	\
{									\
	QString info = QString("[%1] Error Call").arg(__func__);	\
	callbackLog(WO_LOG_ERROR, info.toUtf8().data());	\
	return;							\
}

// 程序可以是因为卡死等原因服务端直接 exit(-1) 了, 这种情况是无法调用 End/OnExit 的
// 但动态库的释放(unload)还是会调用 app 的虚构函数导致崩溃, 这种情况不主动释放 app, 也没必要释放了
wo::KEtApplication* app = nullptr;

wo::WoEtSetting gs_WoEtSettings;
Callback 	gs_defCallBack = {0};
Callback*  	gs_callback = &gs_defCallBack;
bool g_isCanBreak = true;
static ThreadLiteLib::VolatileInt64 gs_isBreak;
static wo::util::BreakTimeStat  gs_breakTimeStat;
// 于当前提交时刻, 服务端保证 CalculateReference 只会对后台触发的跨book引用/importrange公式计算的样张在另外的进程调用
// 对这样的样张, importrange的公式是不应该中断的. 但 OnAsyncGetNetFileDone 没有附带信息, 因此暂时依赖这个变量进行判断
static ThreadLiteLib::VolatileInt64 gs_isForCalcRef(0);
enum SavaAsLiteVersionScene
{
	SavaAsLiteVersionScene_Cbs_Update = 0,
	SavaAsLiteVersionScene_Cbs_Merge,
};

static bool gs_hasCollectLogOpenTime = false;
WebProcType gs_procType = WebProcTypeMaster;
static std::atomic<bool> gs_needUpdateCrossBook(false);

static QString g_childProcCrossbookUerdId;
static QString g_childProcCrossbookConnId;

static int gs_collectUserConnsCount = 0;
constexpr int userConnsCollectFreq = 100;
static bool gs_hasInitBaseContext = false;

CoopCmd g_WebEtCoopCmd;
QDate g_lastDateForMonitorDateChange;

static void callbackLog(int log_level, const char *msg)
 {
	if (gs_callback && gs_callback->log)
	{
		gs_callback->log(log_level, msg);
	}
 }

namespace ExecTimeCollect
{
static int64_t gs_querySum ;
static int64_t gs_queryInitSum;
static int64_t gs_queryInitCount = 0;
static int64_t gs_execSum;
static int64_t gs_exportSum;
static int64_t gs_queryCount = 0;
static int64_t gs_execCount = 0;
} // namespace ExecTimeCollect

static std::chrono::steady_clock::time_point gs_lastMemStatTime;
static std::chrono::steady_clock::time_point gs_lastFullMemStatTime;
static int gs_memFullStatCount = 0;
std::atomic<uint> gs_signalSize(0); 

static QString fullNameFromCmdName(wo::KEtWorkbook* wb, const char *cmdName, bool bCareSec) 
{
	QString name(gs_procType == WebProcTypeMaster ? "behaviour_master_": "behaviour_slave_");
	if (bCareSec && wb->isSecDoc())
		name.append("sec_");
	name.append(cmdName);
	return name;
}

static void CollectCallTime2(const char* cmdName, unsigned int ms, unsigned int ms2, bool bCareSec = false, const char * szTraceId = nullptr)
{
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (!wb)
		return;

	QString name = fullNameFromCmdName(wb, cmdName, bCareSec);
	QString traceId = szTraceId == nullptr ? QString() : QString::fromUtf8(szTraceId);
	wo::WoFileIncludeCollector collector(wb, krt::utf16(name), ms);
	if (!traceId.isEmpty())
		collector.addCmdTraceId(krt::utf16(traceId));
	collector.addCount2(ms2);
	collector.collect();
}

static void CollectCallTime(const char* cmdName, unsigned int ms, bool bCareSec = false, const char * szTraceId = nullptr)
{
	CollectCallTime2(cmdName, ms, 0, bCareSec, szTraceId);
}

static void CollectCallTimeRand2(const char* cmdName, unsigned int ms, unsigned int ms2, bool bCareSec, const char * szTraceId, wo::util::RandDist * rand)
{
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (!wb)
		return;

	wo::WoEtSetting * settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
	int sampleRate = 0;
	if (settings->isCollect(ms, &sampleRate, rand))
	{
		QString name = fullNameFromCmdName(wb, cmdName, bCareSec);
		QString traceId = szTraceId == nullptr ? QString() : QString::fromUtf8(szTraceId);
		wo::WoFileIncludeCollector collector(wb, krt::utf16(name), ms);
		if (!traceId.isEmpty())
			collector.addCmdTraceId(krt::utf16(traceId));
		collector.addSampleRate(sampleRate);
		collector.addCount2(ms2);
		collector.collect();
	}
}

static void CollectCallTimeRand(const char* cmdName, unsigned int ms, bool bCareSec, const char * szTraceId, wo::util::RandDist * rand)
{
	CollectCallTimeRand2(cmdName, ms, 0, bCareSec, szTraceId, rand);
}

static void CollectCallTimeApiRand(const char* cmdName, unsigned int ms, unsigned int ms2 = 0, bool bCareSec = true, const char * szTraceId = nullptr)
{
	wo::WoEtSetting * settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
	if (!settings)
		return;
	CollectCallTimeRand2(cmdName, ms, ms2, bCareSec, szTraceId, settings->GetApiTimeRand());
}

#define WO_STR_V(v) #v
#define API_COLLECT_STR_NAME(name) WO_STR_V(wo_##name)
#define API_TIME_STATE(name, title) \
	wo::util::CallTimeStat callTime(WO_STR_V(name), (title), [](unsigned int ms) { \
		CollectCallTimeApiRand(API_COLLECT_STR_NAME(name), ms); \
	});

static void BeginCoreMetricCollect(wo::KMetricsType mcType)
{
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* wb = wbs->GetWorkbook();
	if (wb && wb->coreMetric().isEnable())
	{
		IEtEventTracking *pEventTracking = wb->GetBook()->LeakWorkspace()->GetEventTracking();
		pEventTracking->SetWoCoreMetrics(&wb->coreMetric());
		wb->coreMetric().beginCollect(mcType);
	}
}

static void EndCoreMetricCollect(const char *name, unsigned int ms, std::function<void(wo::KCoreMetric&)> callback = nullptr)
{
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* wb = wbs->GetWorkbook();
	if (wb && wb->coreMetric().isEnable())
	{
		if (callback)
			callback(wb->coreMetric());
		wb->coreMetric().endCollect(wb, name, ms, gs_procType);
		IEtEventTracking *pEventTracking = wb->GetBook()->LeakWorkspace()->GetEventTracking();
		pEventTracking->SetWoCoreMetrics(nullptr);
	}
}

static void SyncCoreMetricTime(wo::KCoreMetric & cm, wo::KCoreMetricSyncOp syncOp, unsigned int ms)
{
	if (WebProcTypeChild != gs_procType)
		return;
	
	binary_wo::BinWriter bw;
	bw.addStringField(__X("syncCoreMetric"), "operate");
	bw.addInt32Field((int)syncOp, "syncOp");
	bw.addUint32Field(ms, "time");
	
	binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice input = {shbt.get(), bw.writeLength()};
	WebSlice output = {nullptr, 0};
	gs_callback->onSyncParentProc(&input, &output);

	binary_wo::BinReader br(output.data, output.size);
	binary_wo::VarObjRoot root = br.buildRoot();
	binary_wo::VarObj obj = root.cast();
	cm.onSyncMasterMetric(obj);
	
	gs_callback->onFreeWebSlice(&output);
}

class WoCallBackImpl : public IWoCallBack
{
public:
	BOOL isBreak() override
	{
		if (!g_isCanBreak)
			return FALSE;

		return gs_isBreak.get();
	}

	BOOL isCanBreak() override
	{
		return g_isCanBreak;
	}

	BOOL isTypeBreak(WoBreakType breakType) override
	{
		int pos = static_cast<int>(breakType);
		return m_breakFlags[pos];
	}

	void setTypeBreak(WoBreakType breakType, BOOL bv) override
	{
		m_breakFlags.set(static_cast<int>(breakType), bv);

		//粘贴中断埋点
		if (breakType == WoBreakType::BreakPaste)
		{
			IWorkspace* pWorkSpace = app->getCoreApp()->GetWorkSpace();
			IEtCollectInfo* pEtCollectInfo = pWorkSpace->GetEventTracking()->GetCollectInfo();
			pEtCollectInfo->CollectBookTimeInterval(et_event_tracking::BOOK_FIELD_NAME::PASTE_CANCEL_RESPONSE_TIME, true);
		}
	}

	void setBreakInUnitTest(bool b) override { ASSERT(FALSE);} // 仅用于单测中设置break. 不要用于任何单测外的场合!

	WebLogFunc getLogFunc() override
	{
		return gs_callback->log;
	}

	BOOL isMasterProc() override
	{
		return WebProcTypeMaster == gs_procType;
	}

	CollectInfo getCollectInfoFunc() override
	{
		return gs_callback->collectInfo;
	}

    CollectInfo getCollectProgressFunc() override
	{
		return gs_callback->collectInfo;
	}

	// 提交 "外部异步调用函数" (external asynchronous function) 的请求
	void CommitEafCalcRequest(const WebSlice *slice) override
	{
		app->GetWorkbooks()->GetWorkbook()->CalculateEAF(slice);
	}
	// eaf 版本变化时进行广播
	void OnEafVersionChanged() override
    {
		// 广播需要更新的单元格
		// 可能错位(version不一致), 后续处理
		if (gs_callback->broadcast) {
			wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
			binary_wo::BinWriter ww;
			ww.addBoolField(true, "eafResNeedUpdate");
			wb->BroadcastChanged(ww, NULL);
		}
    }
    // importrange 版本变化时进行广播
    void OnImportrangeVersionChanged() override
    {
		// 广播需要更新的单元格
		// 可能错位(version不一致), 后续处理
		if (gs_callback->broadcast) {
			wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
			binary_wo::BinWriter ww;
			ww.addBoolField(true, "importrangeResNeedUpdate");
			wb->BroadcastChanged(ww, NULL);
		}
    }

	void NotifyJson(const char *connID, const char *name, const WebSlice *msg) override
	{
		return gs_callback->notifyJson(connID, name, msg);
	}

	void Broadcast(const char *name, const WebSlice *msg, const char *exclude) override
	{
		return gs_callback->broadcast(name, msg, exclude);
	}

	void CollectCallTime(const char* cmdName, unsigned int ms, bool bCareSec = false, const char * szTraceId = nullptr) override
	{
		::CollectCallTime(cmdName, ms, bCareSec, szTraceId);
	}

	void CollectCallTimeRand(const char* cmdName, unsigned int ms, bool bCareSec = false, const char * szTraceId = nullptr) override
	{
		wo::WoEtSetting * settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
		if (settings)
			::CollectCallTimeRand(cmdName, ms, bCareSec, szTraceId, settings->GetQueryInitTimeRand());
	}

	int getNetDiskFile(PCWSTR netDiskName, PCWSTR fileId, 
		OUT BSTR* path, OUT BSTR* version, OUT BSTR* fileName)
	{
		QString strUserId;
		IRevisionContext* pCtx = _kso_GetRevisionContext();
		if (pCtx)
		{
			IKUserConn* user = pCtx->getUser();
			if (user)
				strUserId = QString::fromUtf16(user->userID());
		}
		ks_wstring strPath, strVersion, strFileName;
		SUPBOOK_STAT res = wo::util::getNetDiskFile(netDiskName, fileId, strPath, strVersion, strFileName, strUserId);
		if (sbsValidFile == res)
		{
			if (path)
				*path = strPath.allocBSTR();
			if (version)
				*version = strVersion.allocBSTR();
			if (fileName)
				*fileName = strFileName.allocBSTR();
		}
		return res;
	}

	PCWSTR getNetDiskName()
	{
		return wo::util::getNetDiskName();
	}

	BOOL asyncGetNetDiskFile(const char* userId, const char* url, INT32 task) override
	{
		if (!userId)
		{
			IKEtApplication* etApp = app->getCoreApp();
			if (etApp && etApp->getUserConns() && etApp->getUserConns()->getCurrentUserConn())
				userId = krt::fromUtf16(etApp->getUserConns()->getCurrentUserConn()->userID()).toUtf8();
		}
		wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
		return wb->asyncGetNetDiskFile(krt::utf16(QString::fromUtf8(userId)), krt::utf16(QString::fromUtf8(url)), task);
	}

	void onNotifyHook(WebSlice &slice)
	{
		gs_callback->onNotifyHook(&slice);
	}

	void addFileInfo(binary_wo::BinWriter& binWriter)
	{
		wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
		if (!wb)
			return;
		wo::FileInfoCollector::AddComponentInfo(binWriter, wb->GetCoreWorkbook()->GetBook());
		binWriter.addStringField(wb->getFileId(), "fileid");
	}
    void downloadImageSync(const WebSlice *urlList, WebSlice *downloadList)
    {
        return gs_callback->downloadImageSync(urlList, downloadList);
    }

    void clearDownloadImage(const WebSlice *downloadList)
    {
        return gs_callback->clearDownloadImage(downloadList);
    }

    void setOpenConnID(PCWSTR connID)
    {
        m_openConnID = connID;
    }
    PCWSTR getOpenConnID()
    {
        return m_openConnID.c_str();
    }

    void setFileID(PCWSTR fileID)
    {
        m_fileID = fileID;
    }
    PCWSTR getFileID()
    {
        return m_fileID.c_str();
    }
    void setFromOpen(BOOL bOpen) 
    {
        m_bOpen = bOpen;
    }
    BOOL getFromOpen() 
    { 
        return m_bOpen; 
    }

	void CollectMemInfo(PCWSTR name, unsigned int memUsageK) override
	{
		wo::util::CollectMemInfo(name, memUsageK, getCollectInfoFunc());
	}

	void commonLog(const WebSlice* pSlice)
	{
		gs_callback->commonLog(pSlice);
	}

	void getSetting(PCWSTR key, BSTR* value)
	{
		binary_wo::BinWriter bw;
		bw.addKey("keys");
		bw.beginArray();
			bw.addString(key);
		bw.endArray();
		binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
		WebSlice input = {shbt.get(), bw.writeLength()};
		WebSlice output = {nullptr, 0};
		struct WebSliceDeleter
		{
			void operator()(WebSlice* pSlice) const
			{
				if (pSlice && pSlice->data)
				{
					gs_callback->onFreeWebSlice(pSlice);
				}
			}
		};
		std::unique_ptr<WebSlice, WebSliceDeleter> upOutput(&output);
		gs_callback->getSetting(&input, upOutput.get());
		BinReader rd(upOutput.get()->data, upOutput.get()->size);
		VarObjRoot root = rd.buildRoot();
		VarObj obj = root.cast();
		VarObj data = obj.get_s("data");
		if (data.empty())
			return;
		for (int i = 0; i < data.arrayLength_s(); i++)
		{
			VarObj item = data.at_s(i);
			if (!item.has("key"))
				continue;
			PCWSTR resKey = item.field_str("key");
			if (xstrcmp(key, resKey) != 0)
				continue;
			if (!item.has("value"))
				continue;

			*value = ks_bstr(item.field_str("value")).detach();
			return;
		}
	}
private:
    ks_wstring m_openConnID;
    ks_wstring m_fileID;
    BOOL m_bOpen = FALSE;
	ThreadLiteLib::FixedArray<BOOL> m_breakFlags;
};

WoCallBackImpl gs_WoCallBackImpl;

std::unique_ptr<wo::secdoc::SecurityDoc> gs_security_doc;

static inline IKDocument *GetCurrentDoc()
{
    wo::KEtWorkbooks *wbs = app->GetWorkbooks();
    if (nullptr != wbs && nullptr != wbs->GetWorkbook())
        return wbs->GetWorkbook()->GetCoreWorkbook();

    return nullptr;
}

static inline IKUserConn *GetUserConn(const char *connID)
{
    IKUserConns *userConns = app->getCoreApp()->getUserConns();
    if (userConns)
        return userConns->getUserConn(connID);

    return nullptr;
}

static void RepairOldDbSheet(wo::KEtWorkbook* wb)
{
	ks_stdptr<IBook> spBook = wb->GetCoreWorkbook()->GetBook();
	if (!spBook)
		return;

	IKWorksheets* pWorksheets = wb->GetCoreWorkbook()->GetWorksheets();
	if (!pWorksheets)
		return;

	INT shtCount = 0;
	spBook->GetSheetCount(&shtCount);
	for (INT isht = 0; isht < shtCount; ++isht)
	{
		ks_stdptr<ISheet> spSheet;
		spBook->GetSheet(isht, &spSheet);

		if (spSheet && spSheet->IsOldDbSheet())
		{
			RANGE rg(spSheet->GetBMP());
			ks_stdptr<IKWorksheet> spWorksheet = pWorksheets->GetSheetItem(isht);
			spWorksheet->GetUsedRange(&rg);
			CELL activeCell = { rg.RowFrom(), rg.ColFrom() };

			range_helper::ranges extListRgs = range_helper::ranges::create_instance();
			extListRgs.add(0, rg);
			ListObjHelp::extendListObj(spBook, extListRgs, activeCell, ListObjHelp::ExtendOppty::ExtAfterPasteExternal, nullptr, true);
		}
	}
}

static void OnAfterDocOpen(wo::KEtWorkbook* wb, const wo::secdoc::SecurityDocInfo &secdocInfo)
{
    if (gs_callback->onAfterDocOpen == NULL)
        return; // for unit test

	WebDocInfo docInfo = {0};
	docInfo.fileFormat = wb->GetCoreWorkbook()->GetFileFormat();
	if (wb->GetBMP()->bDbSheet || wb->GetBMP()->bKsheet)
	{
		ks_stdptr<IUnknown> spUnknown;
		wb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edDBUserGroups, &spUnknown);
		ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
		docInfo.isProtected = spDBUserGroups->IsProtected();

		// ksheet 还需要判断保护区域
		if (!docInfo.isProtected && wb->GetBMP()->bKsheet)
		{
			docInfo.isProtected = wb->isBookProtectedWithHiddenProperty();
		}
		if (wb->GetBMP()->bDbSheet)
			docInfo.fileFormat =  ffDBT;
		else if (wb->GetBMP()->bKsheet)
			docInfo.fileFormat =  ffKSHEET;
	}
	else
	{
		docInfo.isProtected = wb->isBookProtectedWithHiddenProperty();
	}

	RepairOldDbSheet(wb);

	wo::IBookSetting *pSetting = wb->GetCoreWorkbook()->GetBook()->GetWoStake()->getSetting();
	if (pSetting)
	{
		docInfo.isPicAttachments = pSetting->getIsInsertPicAsAttachments();
	}

	WOLOG_INFO << "[docInfo.isPicAttachments]: " << docInfo.isPicAttachments;

	IBook *pBook = wb->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    docInfo.isPicAttachments = docInfo.isPicAttachments || alg::IsBitUsed(info->data, BOOK_INFO_COLLECT::_maskImageAttachment);

	WOLOG_INFO << "[docInfo.isPicAttachments]: " << docInfo.isPicAttachments;

	docInfo.hasDashboard = wb->hasDashBoard();
	WOLOG_INFO << "[docInfo.hasDashboard]: " << docInfo.hasDashboard;

	WOLOG_INFO << "[protection] OnAfterDocOpen: " << WOLOG_MK_KV(docInfo.isProtected);

    if (!secdocInfo.isSecurityDoc) {
        docInfo.secdocInfo = NULL;
        gs_callback->onAfterDocOpen(&docInfo);
    } else {
		WebSecurityDocInfo webSecdocInfo = {0};
		webSecdocInfo.isOutwardDoc = secdocInfo.isOutwardDoc;
		webSecdocInfo.isMBDocIssued = secdocInfo.isMBDocIssued;
        webSecdocInfo.fileGUID = secdocInfo.fileGUID.c_str();
        webSecdocInfo.mbinfoGUID = secdocInfo.mbinfoGUID.c_str();
		webSecdocInfo.docmbguid = secdocInfo.docmbguid.c_str();
		webSecdocInfo.docmblevel = secdocInfo.docmblevel;
		webSecdocInfo.docmbstatus = secdocInfo.docmbstatus;
        webSecdocInfo.docID = secdocInfo.docID.c_str();
        webSecdocInfo.signature = secdocInfo.signature.c_str();
		webSecdocInfo.encData = secdocInfo.encData.c_str();
        webSecdocInfo.userID = secdocInfo.userID.c_str();
        webSecdocInfo.permission = static_cast<uint32_t>(secdocInfo.permission);
		webSecdocInfo.offlineDocEncData = secdocInfo.offlineDocEncData.c_str();
        docInfo.secdocInfo = &webSecdocInfo;
        gs_callback->onAfterDocOpen(&docInfo);
    }
}

static WebInt CheckWorkbookOpened(const char* tag)
{
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	if (!pKEtWorkbook)
	{
		WOLOG_INFO << '[' << tag << ']' << " File not open";
		return WO_FAIL;
	}

	return WO_OK;
}

void _InitBase(bool bBaseProcess, Callback *callback)
{
#ifdef Q_OS_UNIX
#if QT_VERSION < QT_VERSION_CHECK(5, 0, 0)
	QApplication::setGraphicsSystem("raster");
#endif
#endif
//	krt::locale::init();
//	krt::init("Kingsoft", "Office", "6.0", "et");

	//打开malloc内存采样
	// TODO_WebMergeToPC_ET [黄帅][暂时注释因端移动mem代码导致web对memfix的改动丢失, 找不到实现的调用方]
	// AlgEvaluateProxyStat(nullptr);

	ASSERT(app == nullptr);
	_kso_SetWoEtSettings(&gs_WoEtSettings);
	if (bBaseProcess)
	{
		wo::KWoEventDispatcher* pEventDispatcher = new wo::KWoEventDispatcher();
		QCoreApplication::setEventDispatcher(pEventDispatcher);
		wo::KEtApplication::setEventDispatcherCallInfo(pEventDispatcher->getCallInfo());
	}
	app = new wo::KEtApplication(callback == nullptr ? nullptr : callback->log);
	QCoreApplication::setLibraryPaths(QStringList(krt::dirs::resources() + "/qt/plugins/"));
	
	gs_callback = callback;
	_kso_SetWoCallBack(&gs_WoCallBackImpl);

	ks_stdptr<IFontHelper> sp4Init;
	_ettext_GetxtObject(IID_IFontHelper, (void**)&sp4Init);

	// 只有forkOpen模式下才需要进行的初始化
	if (bBaseProcess)
	{
		// 加载函数信息
		ks_stdptr<IFunction> sp4InitFunction;
		VS(_funclib_CreateObject(CLSID_KFunction, IID_IFunction, (void**)&sp4InitFunction));

		// 加载自定义字典（用于字符串对比）
		krt::locale::loadCustomizeDictSuccess();
	}
	callbackLog(WO_LOG_INFO, "[_InitBase] done");
}

WEB_EXPORT void InitBaseContext(Callback *callback)
{
	// Initializes the shared data
	if (WebProcTypeBase != gs_procType)
	{
		if (callback && callback->log)
			callback->log(WO_LOG_ERROR, "[InitBaseContext] Error Call");
		return;
	}

	if (callback && callback->log)
		callback->log(WO_LOG_INFO, QString("[InitBaseContext] proc_type: %1").arg(gs_procType).toUtf8().data());

	if (!gs_hasInitBaseContext)
	{
		_InitBase(true, callback);
		gs_hasInitBaseContext = true;
	}
}

WEB_EXPORT void Begin(Callback* callback, const WebConfigOptions* config)
{
	if (WebProcTypeBase == gs_procType)
	{
		if (callback && callback->log)
			callback->log(WO_LOG_ERROR, "[Begin] Error Call");
		return;
	}

	if (callback && callback->log)
		callback->log(WO_LOG_INFO, QString("[Begin] proc_type: %1").arg(gs_procType).toUtf8().data());

	// 如果不是forkOpen模式，则需要进行必要的初始操作，保证与原流程一致
	if (!gs_hasInitBaseContext)
		_InitBase(false, callback);

	wo::secdoc::SecurityDocArgs secdocArgs;
	if (gs_callback && gs_callback->log) {
		secdocArgs.logger = gs_callback->log;
		app->getCoreApp()->SetWebLogger(gs_callback->log);
	}

	secdocArgs.loginProvider.reset(new wo::secdoc::WebLoginAdapter(app->getCoreApp()));
	secdocArgs.needRefreshKsoTokenFunc = [] (const char* ksoToken) -> bool {
		if (gs_callback && gs_callback->needRefreshKsoToken) {
			return alg::BOOL2bool(gs_callback->needRefreshKsoToken(ksoToken));
		}
		return false;
	};
	secdocArgs.refreshKsoTokenFunc = [] (const char* old_kso_token, const char* refresh_token, IWebString* new_ksotoken) -> void {
		if (gs_callback && gs_callback->refreshKsoToken) {
			gs_callback->refreshKsoToken(old_kso_token, refresh_token, new_ksotoken);
		}
	};
	gs_security_doc.reset(new wo::secdoc::SecurityDoc(secdocArgs));
	if (config && config->securityDocDomain) {
		gs_security_doc->SetSecurityDocDomain(krt::utf16(QString::fromUtf8(config->securityDocDomain)));
	}

	bool bPersistDump = false;
	std::string coreLogDir = wo::isEnableCoreLog(callbackLog, bPersistDump);
	if (!coreLogDir.empty())
	{
		WEBLOG_INIT("webet", coreLogDir.c_str());
		WEBLOG_ENABLE_BT_DEFAULT();
		QString info = QString("core log will be written to file %1").arg(QString::fromUtf8(weblog::webLogFileName()));
		callbackLog(WO_LOG_INFO, info.toLocal8Bit().data());
	}
#if defined(__linux__) && defined(__x86_64__)
	InstallCrashReportForWebOffice("et_db", callbackLog, 
	!coreLogDir.empty() ? [](int lvl, const char *msg) 
	{
		WEBLOG_INFO(msg);
		if (lvl > 1)
			WEBLOG_DUMP_BT();
	} : nullptr);
#endif

	//const char* pEnv = getenv("ALLOW_REMOTE_DEBUG");
  	//if ((pEnv != NULL) && (0 == ks_stricmp(pEnv, "true")))
	//{
		//if (g_WebEtCoopCmd.empty())
		//{
			//QString pXmlPath = krt::dirs::resources() + QString("/cfgs/cooperatecmds/webetcmdlist.xml");
			//wo::BuildCoopCmd::buildCmds(pXmlPath.toLocal8Bit().data(), g_WebEtCoopCmd);
		//}
	//}
	//SetMultiThreadsCnt(1);

	if (::getenv("ET_ENABLE_V8_JSIDE"))
	{
		if (app->getCoreApp()->JsIDEPartInit() == S_OK)
		 	WOLOG_INFO << "[JsApi] JsIDE part init";
	}
}

WEB_EXPORT void End()
{
	BASE_PROCESS_CHECK_RET_VOID

#if defined(__linux__) && defined(__x86_64__)
	UninstallCrashReport();
#endif
}

WEB_EXPORT WebInt InitContext(const WebContextType type, WebSlice* config)
{
	BASE_PROCESS_CHECK_RET_FAIL
    return WO_OK;
}

static void GetCoreSetting(WebetSettings& setting)
{
	// 读取文件大小限制
	char* str = ::getenv("FILESIZE_LIMIT");
	double dbl = -1.0;
	char* stre = nullptr;
	if (str != nullptr)
	{
		dbl = strtod(str, &stre);
	}

	if (0.0 < dbl && stre != str)
	{
		setting.fileSizeLimit = static_cast<int64_t>(dbl);
	}
	else
	{
		setting.fileSizeLimit = -1;
	}
	WOLOG_INFO << "[GetCoreSetting] file size limit, env value: " << dbl 
		<< ", setting.fileSizeLimit: "<< setting.fileSizeLimit;
}

static void GetWoEtSettings()
{
	// General数字是否显示...
	if (char* str = ::getenv("ET_NUM_ELLIPSIS"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_NUM_ELLIPISIS: " << str;
		gs_WoEtSettings.setGeneralNumEllipOn(true);
	}

	// 是否开启单元格记录存储(存在文件，新方案), 没有配置时为关闭
	if (char* enbaleStr = ::getenv("ET_ENABLE_CELLHISTORY_STORE"))
	{
		gs_WoEtSettings.EnableCellHistoryStore(false);
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_CELLHISTORY_STORE: " << enbaleStr;
		if (enbaleStr != nullptr && 0 == strncmp(enbaleStr, "on", 3))
			gs_WoEtSettings.EnableCellHistoryStore(true);
	}

	// 条协作记录存储的最大数量（超过 7 天）
	if (char* str = ::getenv("ET_CELLHISTORY_MAXPURGETAG"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_CELLHISTORY_MAXPURGETAG: " << str;
		int maxPurgeTag = ::atoi(str);
		gs_WoEtSettings.SetCellHistoryMaxPurgeTag(maxPurgeTag);
	}

	if (char *cSupprot = ::getenv("isEnableCellMultiLinkCore"))
	{
		WOLOG_INFO << "[GetWoEtSettings] isEnableCellMultiLinkCore: " << cSupprot;
		if (cSupprot != nullptr && 0 == strncmp(cSupprot, "on", 3))
			gs_WoEtSettings.setSupportCellMultiLink(true);
	}

	// 保存时计算时间过长，中断计算继续保存
	if (char *str = ::getenv("ET_ENABLE_SAVING_CALBREAK"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_SAVING_CALBREAK: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.setEnableSavingBreakCal(true);
	}

	if (char *str = ::getenv("TIME_LIMIT_SAVE_BREAK_SECDOC"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] TIME_LIMIT_SAVE_BREAK_SECDOC: " << lTmp;
			gs_WoEtSettings.SetTimeLimitSaveBreakSecdoc(lTmp);
		}
	}

	if (char *str = ::getenv("TIME_LIMIT_SAVE_BREAK_GENERAL"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0) 
		{
			WOLOG_INFO << "[GetWoEtSettings] TIME_LIMIT_SAVE_BREAK_GENERAL: " << lTmp;
			gs_WoEtSettings.SetTimeLimitSaveBreakGeneral(lTmp);
		}
	}
	
	if (char *str = ::getenv("SLAVE_TIME_LIMIT_SAVE_BREAK_GENERAL"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] SLAVE_TIME_LIMIT_SAVE_BREAK_GENERAL: " << lTmp;
			gs_WoEtSettings.SetSlaveTimeLimitSaveBreakGeneral(lTmp);
		}
	}

	if (char *str = ::getenv("SLAVE_TIME_LIMIT_SAVE_BREAK_SECDOC"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0) 
		{
			WOLOG_INFO << "[GetWoEtSettings] SLAVE_TIME_LIMIT_SAVE_BREAK_SECDOC: " << lTmp;
			gs_WoEtSettings.SetSlaveTimeLimitSaveBreakSecdoc(lTmp);
		}
	}

	if (char *str = ::getenv("ET_ENABLE_NEW_UPDATE_RENDER"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_NEW_UPDATE_RENDER: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetEnableNewUpdateRender(false);
	}
	if (char *cSupprot = ::getenv("ET_ENABLE_CROSS_DYNAMIC_CALC"))
	{
		WOLOG_INFO << "[GetWoEtSettings] isEnableDynamicCalc: " << cSupprot;
		if (cSupprot != nullptr && 0 == strncmp(cSupprot, "on", 3))
			gs_WoEtSettings.setSupportDynamicCalc(true);
	}

    // 复制时是否执行BatchCopy流程
    if (char* enbaleStr = ::getenv("ET_ENABLE_NEW_BATCH_COPY"))
	{
		gs_WoEtSettings.SetEnableNewBatchCopy(TRUE);
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_NEW_BATCH_COPY: " << enbaleStr;
		if (enbaleStr != nullptr && 0 == strncmp(enbaleStr, "false", 6))
			gs_WoEtSettings.SetEnableNewBatchCopy(FALSE);
	}

    // 复制时是否允许条件格式计算
    if (char* enbaleStr = ::getenv("ET_ENABLE_APPLY_CONDITION_FORMAT_WHEN_COPY"))
	{
		gs_WoEtSettings.SetEnableCFApplyWhenCopy(TRUE);
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_APPLY_CONDITION_FORMAT_WHEN_COPY: " << enbaleStr;
		if (enbaleStr != nullptr && 0 == strncmp(enbaleStr, "false", 6))
			gs_WoEtSettings.SetEnableCFApplyWhenCopy(FALSE);
	}

	// AI Formula Limit
    if (char* str = ::getenv("ET_UDF_SINGLE_REQUEST_LIMIT"))
	{
		int limitSize = static_cast<int>(std::strtol(str, nullptr, 10));
		if (limitSize >= 0) 
		{
			WOLOG_INFO << "[GetWoEtSettings] ET_UDF_SINGLE_REQUEST_LIMIT: " << limitSize;
			gs_WoEtSettings.SetUDFSingleRequestLimit(limitSize);
		}
	}

	// 瘦身算法的控制参数
	if (char *str = ::getenv("DOCSLIM_BOUND_DENSITY"))
	{
		double dTmp = std::strtod(str, nullptr);
		if (0.0 <= dTmp && dTmp <= 1.0)
		{
			WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_BOUND_DENSITY: " << dTmp;
			gs_WoEtSettings.SetDocSlimBoundDensity(dTmp);
		}
	}

	if (char *str = ::getenv("DOCSLIM_NULL_CELLS_LIMIT"))
	{
		unsigned long long int llTmp = std::strtoull(str, nullptr, 10);
		if (llTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_NULL_CELLS_LIMIT: " << llTmp;
			gs_WoEtSettings.SetDocSlimNullCellsLimit(llTmp);
		}
	}

	if (char *str = ::getenv("DOCSLIM_NULL_CELLS_TRIGGER_VALUE"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_NULL_CELLS_TRIGGER_VALUE: " << uTmp;
		gs_WoEtSettings.SetDocSlimNullCellsTriggerValue(uTmp);
	}

	if (char *str = ::getenv("DOCSLIM_UNREFERENCED_PICTURE_TRIGGER_VALUE"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_UNREFERENCED_PICTURE_TRIGGER_VALUE: " << uTmp;
		gs_WoEtSettings.SetDocSlimUnreferencedPictureTriggerValue(uTmp);
	}

	if (char *str = ::getenv("DOCSLIM_INVISIBLE_OBJECT_TRIGGER_VALUE"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_INVISIBLE_OBJECT_TRIGGER_VALUE: " << uTmp;
		gs_WoEtSettings.SetDocSlimInvisibleObjectTriggerValue(uTmp);
	}

	if (char *str = ::getenv("DOCSLIM_OVERLAP_SHAPES_TRIGGER_VALUE"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_OVERLAP_SHAPES_TRIGGER_VALUE: " << uTmp;
		gs_WoEtSettings.SetDocSlimOverlapShapesTriggerValue(uTmp);
	}

	if (char *str = ::getenv("DOCSLIM_UNUSED_DUPLICATE_STYLE_TRIGGER_VALUE"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_UNUSED_DUPLICATE_STYLE_TRIGGER_VALUE: " << uTmp;
		gs_WoEtSettings.SetDocSlimUnusedDuplicateStyleTriggerValue(uTmp);
	}

	if (char *str = ::getenv("DOCSLIM_DUPLICATE_FORMAT_CONDITION_TRIGGER_VALUE"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_DUPLICATE_FORMAT_CONDITION_TRIGGER_VALUE: " << uTmp;
		gs_WoEtSettings.SetDocSlimDuplicateFormatConditionTriggerValue(uTmp);
	}

	if (char *str = ::getenv("ET_HTML_COPY_MAX_LIMIT_SIZE"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] ET_HTML_COPY_MAX_LIMIT_SIZE: " << uTmp;
		gs_WoEtSettings.SetHtmlCopyMaxLimitSize(uTmp);
	}

	if (char *str = ::getenv("DOCSLIM_ROW_LOW_DENSITY_TOLERANCE_INDEX"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_ROW_LOW_DENSITY_TOLERANCE_INDEX: " << lTmp;
			gs_WoEtSettings.SetDocSlimRowLowDensityToleranceIndex(lTmp);
		}
	}

	if (char *str = ::getenv("DOCSLIM_COL_LOW_DENSITY_TOLERANCE_INDEX"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_COL_LOW_DENSITY_TOLERANCE_INDEX: " << lTmp;
			gs_WoEtSettings.SetDocSlimColLowDensityToleranceIndex(lTmp);
		}
	}

	if (char *str = ::getenv("DOCSLIM_BOUND_ADVANCE"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] DOCSLIM_BOUND_ADVANCE: " << lTmp;
			gs_WoEtSettings.SetDocSlimBoundAdvance(lTmp);
		}
	}

	if (char *str = ::getenv("ET_ENABLE_AUTO_SLIM"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_AUTO_SLIM: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnableAutoSlim(TRUE);
	}

	if (char *str = ::getenv("ET_ENABLE_PIVOT_ITEM_MOVE"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_PIVOT_ITEM_MOVE: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnablePivotItemMove(TRUE);
		else
			gs_WoEtSettings.SetEnablePivotItemMove(FALSE);
	}

	if (char *str = ::getenv("ET_ENABLE_FIND_THOUSANDS_SEPARATORS"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_FIND_THOUSANDS_SEPARATORS: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnableFindThousandsSeparators(TRUE);
	}

	if (char *str = ::getenv("ET_DEFAULT_FILTER_SHARED"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_DEFAULT_FILTER_SHARED: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetDefaultFilterShared(FALSE);
	}

	if (char *str = ::getenv("ET_ENABLE_AUTO_SLIM_IO_COLLECT"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_AUTO_SLIM_IO_COLLECT: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.setEnableAutoSlimIOCollect(TRUE);
		else
			gs_WoEtSettings.setEnableAutoSlimIOCollect(FALSE);
	}

	if (char *str = ::getenv("ET_ENABLE_OPTIMIZ_NULL_CELLS"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_OPTIMIZ_NULL_CELLS: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.setEnableOptimizeNullCells(true);
	}

	if (char *str = ::getenv("ET_ENABLE_REGION_PROTECT"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_REGION_PROTECT: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnableRegionProtect(TRUE);
	}

	if (char *str = ::getenv("ET_ENABLE_OWNER_PROTECT"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_OWNER_PROTECT: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnableOwnerProtect(TRUE);
	}

	// 侧边栏文件夹允许的最大层级
	if (char *str = ::getenv("MAX_SIDEBAR_FOLDER_TREE_LEVEL"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 1) // 至少为1级
		{
			WOLOG_INFO << "[GetWoEtSettings] MAX_SIDEBAR_FOLDER_TREE_LEVEL: " << lTmp;
			gs_WoEtSettings.SetMaxSidebarFolderTreeLevel(lTmp);
		}
	}

	// 侧边栏文件夹允许的最大文件夹数量
	if (char *str = ::getenv("MAX_SIDEBAR_FOLDER_TREE_FOLDER_CNT"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] MAX_SIDEBAR_FOLDER_TREE_FOLDER_CNT: " << lTmp;
			gs_WoEtSettings.SetMaxSidebarFolderTreeFolderCnt(lTmp);
		}
	}


	if (char *str = ::getenv("DB_ENABLE_PARENT_RECORD"))
	{
		WOLOG_INFO << "[GetWoEtSettings] DB_ENABLE_PARENT_RECORD: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnableParentRecord(TRUE);
	}

	if (char *str = ::getenv("MAX_PARENT_RECORD_LEVEL"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] MAX_PARENT_RECORD_LEVEL: " << lTmp;
			gs_WoEtSettings.SetMaxParentRecordLevel(lTmp);
		}
	}
	
	if (char *str = ::getenv("DB_ENABLE_CLEAR_CUR_VERSION_CMD"))
	{
		WOLOG_INFO << "[GetWoEtSettings] DB_ENABLE_CLEAR_CUR_VERSION_CMD: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnableClearCurVersionCmd(TRUE);
	}

	// 大概一个月后删除
	if (char* enbaleStr = ::getenv("ET_ENABLE_IMPORTRANGE_CACHE_FALLBACK"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_IMPORTRANGE_CACHE_FALLBACK: " << enbaleStr;
		if (enbaleStr != nullptr && 0 == strncmp(enbaleStr, "true", 5))
			gs_WoEtSettings.EnableImportrangeFallback(TRUE);
	}

	// 大概一个月后删除
	if (char* enbaleStr = ::getenv("ET_ENABLE_CROSSBOOK_EMPTY_USER_FALLBACK"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_CROSSBOOK_EMPTY_USER_FALLBACK: " << enbaleStr;
		if (enbaleStr != nullptr && 0 == strncmp(enbaleStr, "true", 5))
			gs_WoEtSettings.EnableCrossBookEmptyUserFallback(TRUE);
	}

	if (char *str = ::getenv("ET_CUSTOM_LIST_CACHE_MAX_SIZE_PER_SHEET"))
	{
		long lTmp = std::strtol(str, nullptr, 10);
		if (lTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] ET_CUSTOM_LIST_CACHE_MAX_SIZE_PER_SHEET: " << lTmp;
			gs_WoEtSettings.SetCustomListCacheMaxSizePerSheet(lTmp);
		}
	}
	
	if (char* enbaleStr = ::getenv("ET_ENABLE_CUSTOM_LIST_CHANGED_NOTIFY"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_CUSTOM_LIST_CHANGED_NOTIFY: " << enbaleStr;
		if (enbaleStr != nullptr && 0 == strncmp(enbaleStr, "true", 5))
			gs_WoEtSettings.SetEnableCustomListChangeNotify(TRUE);
	}
	// 大概3个月后删除
	if (char* enbaleStr = ::getenv("DB_ENABLE_SHARE_FMLA"))
	{
		WOLOG_INFO << "[GetWoEtSettings] DB_ENABLE_SHARE_FMLA: " << enbaleStr;
		if (enbaleStr != nullptr && 0 == strncmp(enbaleStr, "false", 6))
			gs_WoEtSettings.EnableDbShareFmla(FALSE);
	}
	if (char* str = ::getenv("ET_LIMIT_EXCLUSIVE_ROW_COUNT"))
	{
		UINT limitRow = static_cast<UINT>(std::strtol(str, nullptr, 10));
		if (limitRow >= 0) 
		{
			WOLOG_INFO << "[GetWoEtSettings] ET_LIMIT_EXCLUSIVE_ROW_COUNT: " << limitRow;
			gs_WoEtSettings.SetLimitExclusiveRow(limitRow);
		}
	}

	if (char* str = ::getenv("ET_LIMIT_EXCLUSIVE_COL_COUNT"))
	{
		UINT limitCol = static_cast<UINT>(std::strtol(str, nullptr, 10));
		if (limitCol >= 0) 
		{
			WOLOG_INFO << "[GetWoEtSettings] ET_LIMIT_EXCLUSIVE_COL_COUNT: " << limitCol;
			gs_WoEtSettings.SetLimitExclusiveCol(limitCol);
		}
	}

	if (char* str = ::getenv("ET_LIMIT_EXCLUSIVE_CELL_COUNT"))
	{
		UINT limitCell = static_cast<UINT>(std::strtol(str, nullptr, 10));
		if (limitCell >= 0) 
		{
			WOLOG_INFO << "[GetWoEtSettings] ET_LIMIT_EXCLUSIVE_CELL_COUNT: " << limitCell;
			gs_WoEtSettings.SetLimitExclusiveCell(limitCell);
		}
	}

	if (char *str = ::getenv("ET_ENABLE_EXCLUSIVE_RANGE"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_EXCLUSIVE_RANGE: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetEnableExclusiveRange(FALSE);
	}

	if (char *str = ::getenv("ET_ENABLE_AUTO_UPDATE_USERINFO"))
	{
		if (str != nullptr && 0 == strncmp(str,  "true", 5))
		{
			WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_AUTO_UPDATE_USERINFO: " << str;
			gs_WoEtSettings.SetEnableAutoUpdateUserInfo(TRUE);
		}	
	}

	if (char *str = ::getenv("AUTO_UPDATE_USERINFO_TIME"))
	{
		double dValue = std::strtod(str, nullptr);
		if (dValue >=0.0) 
		{
			WOLOG_INFO << "[GetWoEtSettings] AUTO_UPDATE_USERINFO_TIME: " << str;
			gs_WoEtSettings.SetAutoUpdateUserInfoTime(dValue);
		}
	}

	if (char *str = ::getenv("ET_ENABLE_MANNUALCALC_BREAK"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_MANNUALCALC_BREAK: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetEnableMannualCalcBreak(FALSE);
	}
	// webhook cache Limit
    if (char* str = ::getenv("DB_MAX_HOOK_CACHE_SIZE"))
	{
		int maxCnt = static_cast<int>(std::strtol(str, nullptr, 10));
		if (maxCnt >= 0) 
		{
			WOLOG_INFO << "[GetWoEtSettings] DB_MAX_HOOK_CACHE_SIZE: " << maxCnt;
			gs_WoEtSettings.SetMaxHookCacheSize(maxCnt);
		}
	}

    if (char* str = ::getenv("DB_KEEP_LINKITEM_ORDERED"))
	{
		WOLOG_INFO << "[GetWoEtSettings] DB_KEEP_LINKITEM_ORDERED: " << str;
		if (str != nullptr && 0 == strncmp(str, "on", 3))
			gs_WoEtSettings.SetDbAutolinkItemOrdered(TRUE);
	}

	// 计算中断优化 二次改动 wo开关. 功能预计2024.12大版本发布, 稳定后开关可删除
	if (char *str = ::getenv("ET_ENABLE_CALC_BREAK_OPT"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_CALC_BREAK_OPT: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetEnabledCalcBreak(FALSE);
	}

	// 动态数组的GetCellInt是否支持枚举优化, 稳定后开关可删除
	if (char* str = ::getenv("ET_ENABLE_DYNAMIC_ARRAY_ENUM_OPTIMIZE"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_DYNAMIC_ARRAY_ENUM_OPTIMIZE: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnabledDynaArrEnumOpt(TRUE);
	}

	// 延迟加载XFS，小于等于0时全量序列化xfs
    if (char* str = ::getenv("ET_DELAY_SERIALIZE_XFS_INIT_SIZE"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_DELAY_SERIALIZE_XFS_INIT_SIZE: " << str;
		int maxCnt = static_cast<int>(std::strtol(str, nullptr, 10));
		gs_WoEtSettings.SetDelaySerializeXfsInitSize(maxCnt);
	}

	// 统计表启用开关
	if (char *str = ::getenv("DB_ENABLE_STATISTICSHEET"))
	{
		WOLOG_INFO << "[GetWoEtSettings] DB_ENABLE_STATISTICSHEET: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetEnableStatisticSheet(FALSE);
		else
			gs_WoEtSettings.SetEnableStatisticSheet(TRUE);
	}

	if (const char *const str {::getenv("DB_ENABLE_FIELD_REF_TOKEN")})
	{
		WOLOG_INFO << "[GetWoDbSettings] DB_ENABLE_FIELD_REF_TOKEN" << str;
		if (strncmp(str, "true", sizeof "true") == 0)
			gs_WoEtSettings.SetEnableDbFieldRefToken(TRUE);
	}

	if (char *str = ::getenv("ET_NEED_CHECK_ALLSHEETS_PERMISSION"))
	{
		if (str != nullptr && 0 == strncmp(str,  "true", 5))
		{
			WOLOG_INFO << "[GetWoEtSettings] ET_DISABLE_AIRSCRIPT_AREA_PROTECT: " << str;
			gs_WoEtSettings.SetNeedCheckAllSheetsPermission(TRUE);
		}
	}

	if (char* str = ::getenv("ENABLE_DB_RESIZE_ROW_OPT"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ENABLE_DB_RESIZE_ROW_OPT: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetEnableDBResizeRowOpt(FALSE);
	}

	if (char *str = ::getenv("DB_ENABLE_OPTIMIZE_CLEAR_CELL"))
	{
		WOLOG_INFO << "[GetWoEtSettings] DB_ENABLE_OPTIMIZE_CLEAR_CELL: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetEnableOptimizeClearCell(FALSE);
	}

	if (char* str = ::getenv("OPENBOOK_TRACKING_THOUSAND_THRESHOLD"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] OPENBOOK_TRACKING_THOUSAND_THRESHOLD: " << uTmp;
		gs_WoEtSettings.SetOpenTrackingThousandThreshold(uTmp);
	}

	if (char* str = ::getenv("OPENBOOK_TRACKING_ONE_THRESHOLD"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] OPENBOOK_TRACKING_ONE_THRESHOLD: " << uTmp;
		gs_WoEtSettings.SetOpenTrackingOneThreshold(uTmp);
	}

	if (char* str = ::getenv("DEL_OP_DISABLE_TRANS_OPT"))
	{
		WOLOG_INFO << "[GetWoEtSettings] DEL_OP_DISABLE_TRANS_OPT: " << str;
		if (str != nullptr && 0 == strncmp(str, "false", 6))
			gs_WoEtSettings.SetDelOpDisableTransOpt(FALSE);
	}

	// 是否启用新的通知服务端跨表引用的接口,稳定后删除老的接口和开关
    if (char* str = ::getenv("USE_BATCH_CROSSBOOK_NOTIFY"))
	{
    	WOLOG_INFO << "[GetWoEtSettings] USE_BATCH_CROSSBOOK_NOTIFY: " << str;
    	if (str != nullptr && 0 == strncmp(str, "false", 6))
    		gs_WoEtSettings.SetBatchNotifyCBRInfo(FALSE);
	}

	if (char *str = ::getenv("ET_ENABLE_VERSION_TAG"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_VERSION_TAG: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnableVersionTag(TRUE);
	}

	if (char* str = ::getenv("ET2DB_ENABLE_SUPPORT_IMAGES"))
	{
    	WOLOG_INFO << "[GetWoEtSettings] ET2DB_ENABLE_SUPPORT_IMAGES: " << str;
    	if (str != nullptr && 0 == strncmp(str, "false", 6))
    		gs_WoEtSettings.SetEt2DbEnableSupportImages(FALSE);
    }
    if (char *str = ::getenv("ENABLE_OPEN_PROGRESS"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ENABLE_OPEN_PROGRESS: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
			gs_WoEtSettings.SetEnableOpenProgress(TRUE);
	}


    if (char* str = ::getenv("OPEN_PROGRESS_DOZEN_THRESHOLD"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] OPEN_PROGRESS_DOZEN_THRESHOLD: " << uTmp;
		gs_WoEtSettings.SetOpenProgressDozenThreshold(uTmp);
	}

    if (char* str = ::getenv("OPEN_PROGRESS_NOTIFY_DURATION"))
	{
		unsigned int uTmp = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] OPEN_PROGRESS_NOTIFY_DURATION: " << uTmp;
		gs_WoEtSettings.SetOpenProgressNotifyDuration(uTmp);
	}

	if (char* str = ::getenv("ET_TIDY_IMAGEPOOL_THRESHOLD"))
	{
		unsigned int uVal = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] ET_TIDY_IMAGEPOOL_THRESHOLD: " << uVal;
		gs_WoEtSettings.SetTidyImagePoolThreshold(uVal);
	}

	if (char* str = ::getenv("DASHBOARD_SNAPSHOT_WIDTH"))
	{
		unsigned int width = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] DASHBOARD_SNAPSHOT_WIDTH: " << width;
		gs_WoEtSettings.SetDashBoardSnapshotWidth(width);
	}

	if (char* str = ::getenv("DASHBOARD_SNAPSHOT_MINHEIGHT"))
	{
		unsigned int minHeight = static_cast<unsigned int>(std::strtoul(str, nullptr, 10));
		WOLOG_INFO << "[GetWoEtSettings] DASHBOARD_SNAPSHOT_MINHEIGHT: " << minHeight;
		gs_WoEtSettings.SetDashBoardSnapshotMinHeight(minHeight);
	}
}

static bool IsIOCancelEnable()
{
	static int b = 2;
	if (b == 2)
	{
		char* str = ::getenv("ET_IO_CANCEL_ENABLE");
		if (str && strncmp(str, "false", 6) == 0)
			b = 0;
		else
			b = 1;
		WOLOG_INFO << "[IsIOCancelEnable]: " << b;
	}
	return b;
}

static const char* GetProductName(const QString& strName)
{
	if (strName.endsWith(".dbt"))
		return "db";
	else if (strName.endsWith(".ksheet"))
		return "ksheet";
	else
		return "webet";
}

static void CollectOpenFail(HRESULT hr, unsigned int ms, const WebOpenArguments *arg)
{
	if (!arg)
		return;

	// IO_E_OPENCANCEL(加密文档) 和 IO_E_NEED_MODIFY_PASSWORD 不能算失败
	if (SUCCEEDED(hr) || hr == IO_E_OPENCANCEL || hr == IO_E_NEED_MODIFY_PASSWORD)
		return;

	auto strIsEmpty = [](const char* str)
	{
		return str != nullptr && *(str) != '\0';
	};

	wo::WoOpenSaveCollector collector(__X("open_fail"), ms);
	collector.addFileid(arg->fileID);
	collector.addFileTypeByFileName(arg->fileName);
	collector.addResult(hr);
	collector.addUserId(arg->userID);
	collector.addHasPassword(strIsEmpty(arg->password));
	collector.addHasModifyPassword(strIsEmpty(arg->modifyPassword));
}

static void CollectOpenTime(unsigned int ms, const char * szTraceId)
{
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (!wb) return;
	IBook *pBook = wb->GetCoreWorkbook()->GetBook();
	if (!pBook) return;

	wo::IWoBookInfo *pBookInfo = pBook->GetWoStake()->GetBookInfo();
	wo::IWoOpenInfo * openInfo = pBookInfo->GetOpenInfo();
	openInfo->SetOpenTime(ms);
	BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
	pBookInfo->SetCellCompileCnt(info->cellCompileFmlCount);

	// open time
	{
		QString name = fullNameFromCmdName(wb, "Open", true);
		QString traceId = szTraceId == nullptr ? QString() : QString::fromUtf8(szTraceId);
		wo::WoFileIncludeCollector collector(wb, krt::utf16(name), ms);
		if (!traceId.isEmpty())
			collector.addCmdTraceId(krt::utf16(traceId));
		int threadCnt = openInfo->GetOpenThreadCnt() == wo::WoOpenThreadCnt::kInvalid ? 0 : (int)openInfo->GetOpenThreadCnt();
		collector.addThreadCount(threadCnt);
		collector.addCount2(pBookInfo->GetCellCompileCnt());
		collector.addFileSize1(pBookInfo->GetFileSize());
		collector.collect();
	}

	if (openInfo->IsThreadCntChanged())
	{ // 线程发生切换, 上报埋点。count为时间差值。
		unsigned int oldTime =  openInfo->GetOpenThreadCnt() == wo::WoOpenThreadCnt::kMainThread ?
			openInfo->GetThreadOpenTime() : openInfo->GetMainOpenTime();
		const char* name = openInfo->GetOpenThreadName();
		CollectCallTime2(name, oldTime - ms, ms, true, szTraceId);
	}
}

static void InitTruncateOpenConfig(const WebOpenArguments *arg)
{
	// 获取打开截断环境开关
	if (char *str = ::getenv("ET_ENABLE_TRUNCATE_OPEN"))
	{
		WOLOG_INFO << "[GetWoEtSettings] ET_ENABLE_TRUNCATE_OPEN: " << str;
		if (str != nullptr && 0 == strncmp(str, "true", 5))
		{
			gs_WoEtSettings.SetEnableTruncateOpen(TRUE);
		}
		else
		{
			gs_WoEtSettings.SetEnableTruncateOpen(FALSE);
		}
	}

	if (arg)
	{
		gs_WoEtSettings.SetTruncateRowOnFileOpen((INT32)arg->truncateRowOnFileOpen);
		gs_WoEtSettings.SetTruncateColOnFileOpen((INT32)arg->truncateColOnFileOpen);
	}
}

static void ResetTruncateOpenConfig()
{
	// 只有走weblink open才去获取打开截断标志，结束的时候全部置为初始值
	gs_WoEtSettings.SetTruncateRowOnFileOpen(0);
	gs_WoEtSettings.SetTruncateColOnFileOpen(0);
	gs_WoEtSettings.SetEnableTruncateOpen(FALSE);
}

class TruncateOpenConfigHelper
{
public:
	TruncateOpenConfigHelper(const WebOpenArguments *arg) 
	{
		InitTruncateOpenConfig(arg); 
	}
	~TruncateOpenConfigHelper() 
	{
		ResetTruncateOpenConfig(); 
	}
};

/*!
 * @brief 打开一个文件
 */
WEB_EXPORT WebInt Open(const WebOpenArguments *arg, const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	WOLOG_INFO << "[open_process_mode] " << (gs_hasInitBaseContext ? "fork" : "normal");

	QString strName = QString::fromUtf8(arg->fileName);
	const char * product_name = GetProductName(strName);
	wo::setupASAN(arg->fileID, arg->userID, product_name);
	wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
	if (arg->disableCalcBreak)
		g_isCanBreak = false; // 不进行计算中断
    // 如要修改打开文件时的SetBreak行为, 记得考察是否也要修改OpenWorkbookScope中的SetBreak
	SetBreak(true); // 打开文件时默认中断计算， 减少打开时间
	GetWoEtSettings(); // 从环境便令中获取相关功能开关配置
	{
		ICloudSettings* pCloudSettings = app->getCoreApp()->GetAppSettings()->GetCloudSettings();
		if (pCloudSettings)
			pCloudSettings->Reload();
	}
	
	app->getCoreApp()->GetAppSettings()->SetEnableEtOpenProgress(gs_WoEtSettings.IsEnableOpenProgress());

	// 如要修改打开文件时的SetBreak行为, 记得考察是否也要修改OpenWorkbookScope中的SetBreak
	app->getCoreApp()->GetAppSettings()->GetCloudSettings()->Reload();
	IWoCallBack *pWoCb = _kso_GetWoCallBack();
    pWoCb->setFromOpen(TRUE);
    wo::util::SendOpenProgress(arg->connID, __X("et_core_progress"), __X("EtIOBegin"), 0, 0, 0);
	wo::util::OpenProgressScope openProgressScope([&]() {
        pWoCb->setOpenConnID(NULL);
        pWoCb->setFromOpen(FALSE);
        wo::util::SendOpenProgress(arg->connID, __X("et_core_progress"), __X("EtIOEnd"), 100, 0, 0);
    });
	HRESULT hr = E_FAIL;
	wo::util::CallTimeStat callTime("Open", nullptr, [&hr, arg](unsigned int ms) { 
		if (SUCCEEDED(hr))
		{
			CollectOpenTime(ms, arg->connID);
		}
		else
			CollectOpenFail(hr, ms, arg); 
	});

	IWorkspace* pWs = app->getCoreApp()->GetWorkSpace();
	IEtEventTracking* pEventTracking = pWs->GetEventTracking();
	IEtCollectInfo* pCollectInfo = pEventTracking->GetCollectInfo();
	ks_wstring fileIdStr = krt::utf16(QString::fromUtf8(arg->fileID));
	etcore::TimeStat timeStat([&](unsigned int time) {
		KComVariant varTime(time);
		pCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::TOTAL_TIME_CONSUMING, varTime);
		QString qConnId = QString::fromUtf8(arg->connID);
		KComVariant varVal(ks_bstr(krt::utf16(qConnId)).detach());
		pCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::CONN_ID, varVal);
		IEtEventTrackingManager* pEventTrackingMgr = pEventTracking->GetManager();
		pEventTrackingMgr->SetFileId(fileIdStr.c_str());
		KComVariant varAutoSlim(alg::bool2BOOL(!arg->woAutoSlim));
		pCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::OPEN_AUTO_SLIM_INT, varAutoSlim);
		pEventTracking->SendInfoAfterOpen();
	});
	QString strPath = QString::fromUtf8(arg->filePath);
	QString strPassword = QString::fromUtf8(arg->password);
	QString strModifyPassword = QString::fromUtf8(arg->modifyPassword);
	QString strCreatorID = QString::fromUtf8(arg->creatorID);
	QString strUserID = QString::fromUtf8(arg->userID);

#if defined(__linux__) && defined(__x86_64__)
	SetCrashFileInfo(arg->fileID, arg->userID, product_name);
#endif
	if (alg::bool2BOOL(arg->disableCalc) != app->getCoreApp()->GetAppSettings()->GetDisableCalc())
	{
		app->getCoreApp()->GetAppSettings()->SetDisableCalc(alg::bool2BOOL(arg->disableCalc));
	}

	app->getCoreApp()->GetWoSecDocContext()->SetWebFileName(krt::utf16(QString::fromUtf8(arg->fileName)));
	gs_security_doc->OnBeforeDocOpen(arg->userID, arg->sessionID, userContext->userRequestInfo);
	gs_WoEtSettings.SetFileId(fileIdStr.c_str());
	TruncateOpenConfigHelper truncateOpenConfigHelper(arg);

    ks_wstring connIDStr = krt::utf16(QString::fromUtf8(arg->connID));
    ks_wstring fileIDStr = krt::utf16(QString::fromUtf8(arg->fileID));

    if (pWoCb)
    {
        pWoCb->setOpenConnID(connIDStr.c_str());
        pWoCb->setFileID(fileIDStr.c_str());
    }
    

	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	hr = wbs->Open(strPath, strName,
		0 != arg->readOnly, strPassword, strModifyPassword, arg->fileID, arg->cloudVer, strUserID, arg->woAutoSlim);
	if (FAILED(hr))
	{
        int errorCode = -1;
        ks_wstring erroMsg = app->getCoreApp()->GetWoSecDocContext()->GetWebSecDocErrorCode(&errorCode);
		if (gs_callback && gs_callback->onDocOpenFailed)
			gs_callback->onDocOpenFailed(hr, errorCode, QString::fromUtf16(erroMsg.c_str()).toUtf8());

		WEBLOG_DUMP_BT();
	}

    if (IO_E_OPENCANCEL == hr)
		return WO_NEED_PASSWORD;
	else if (IO_E_PASSWORD == hr)
		return WO_INVALID_PASSWORD;
	else if (IO_E_NEED_MODIFY_PASSWORD == hr)
		return WO_NEED_MODIFY_PASSWORD;
	else if (IO_E_INVALID_MODIFY_PASSWORD == hr)
		return WO_INVALID_MODIFY_PASSWORD;
	else if (IO_E_NOT_SUPPORT_SECURITY_DOC == hr)
		return WO_NOT_SUPPORT_SECURITY_DOC;
	else if (IO_E_NEED_PERMISSION == hr)
		return WO_SECDOC_NEED_PERMISSION;
	else if (IO_E_SDSERVER_ERROR == hr)
		return WO_SECDOC_NETWORK_UNREACHABLE;
	else if (IO_E_SEC_ALGORITHM_NOT_MATCH == hr)
		return WO_SECDOC_ALGORITHM_NOT_MATCH;
	else if (IO_E_OPENTRUNCATE == hr)
		return WO_ET_ERR_OPEN_TRUNCATE;

	wo::KEtWorkbook* wb = wbs->GetWorkbook();

	if (wb == nullptr || FAILED(hr))
	{
		if (gs_callback && gs_callback->log)
		{
			char msg[200] = {0};
			ks_snprintf_s(msg, 200, 199, "file open error, hr:%d", hr);
			gs_callback->log(WO_LOG_ERROR, msg);
		}

		WEBLOG_DUMP_BT();
		return WO_FAIL;
	}
	else
	{
		etcore::TimeStat timeStat([&](unsigned int time) {
			KComVariant varVal(time);
			pCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::WO_AFTER_OPEN_TIME_CONSUMING, varVal);
		});
		if (IBook* pBook = wb->GetCoreWorkbook()->GetBook())
		{
			BMP_PTR bmpPtr = pBook->GetBMP();
			PCWSTR componentName = nullptr;
			if (bmpPtr->bDbSheet)
				componentName = __X("dbt");
			else if (bmpPtr->bKsheet)
				componentName = __X("ksheet");
			else
				componentName = __X("et");
			KComVariant varStr(componentName);
			pCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::WO_OPEN_PROJECT, varStr);
		}

		WO_LOG_X(
			wb->getLogger(), 
			WO_LOG_INFO,
			"[file_open] readOnly:%d, disableCalc:%d, serviceType: %d, reserveCrossBookDbData: %d",
			arg->readOnly, 
			arg->disableCalc, 
			arg->serviceType,
			arg->reserveCrossBookDbData
		);

		wb->SetCreatorUserID(krt::utf16(strCreatorID));
		wb->SetExportFmla(arg->expFmla != 0);
		wb->setNeedGetAutoPasswordMaster(arg->withGetAutoPasswordMaster);
		wb->setReserveCrossBookDbData(arg->reserveCrossBookDbData);
		g_lastDateForMonitorDateChange = QDate::currentDate();
		wb->AfterFileOpen();

		WebetSettings webSettings;
		GetCoreSetting(webSettings);
		wb->SetCoreSettings(&webSettings);
		QFileInfo qFileInfo(strPath);
		wb->SetFileVersionSize(arg->cloudVer, qFileInfo.size(), true);

		wo::secdoc::SecurityDocInfo secdocInfo;
		secdocInfo.isSecurityDoc = false;
		_Workbook* coreWb = wb->GetCoreWorkbook();
		gs_security_doc->OnAfterDocOpen(coreWb, &secdocInfo);
		OnAfterDocOpen(wb, secdocInfo); // 注意 ！：open 成功时这个函数一定要被调用到，否则有安全问题
		if(arg->desensitizeInfo)
			coreWb->setDesensitizeInfo(arg->desensitizeInfo);
		if (arg->highlightMode)
		{
			wb->SetCellKeyWordHighlight();
		}
		VARIANT_BOOL bReadonly = VARIANT_FALSE;
		coreWb->get_ReadOnly(&bReadonly);
		wo::IBookStake* bkStake = coreWb->GetBook()->GetWoStake();
		bkStake->getCacheBuildingOnCell()->markPersist();
		if (bReadonly == VARIANT_TRUE)
			bkStake->getSetting()->setIsFilterShared(FALSE);

		handleCoreConquer(wb,strUserID);
		wo::FileInfoCollector(wb, arg->connID).Collect();
		if (wb->GetBMP()->bDbSheet)
		{
			// dbsheet判断是否校验权限
			if (arg->withoutPermission)
			{
				ks_stdptr<IUnknown> spUnknown;
				coreWb->GetBook()->GetExtDataItem(edDBUserGroups, &spUnknown);
				ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
				spDBUserGroups->SetProtectOn(FALSE);
			}
		}

		// wbs->Open后可能又有产生事务的操作 再清一遍
		app->getCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(wb->GetCoreWorkbook());
		bkStake->GetAreaTransformContainter()->Enable(false);
		if (ServiceType_QueryCellHistories == arg->serviceType)
		{
			bkStake->GetAreaTransformContainter()->Enable(true);
		}

		MistakenDocAuthorNames *mistakenAuthors = arg->mistakenDocAuthorNames;
		if (mistakenAuthors)
			wb->ClearMistakenDocAuthorName(mistakenAuthors->dateBefore, mistakenAuthors->authors, mistakenAuthors->authorCount);

		wb->setServiceType(arg->serviceType);
		// 从 Open 后开始统计内存增长情况
		gs_lastMemStatTime = std::chrono::steady_clock::now();
		gs_lastFullMemStatTime = gs_lastMemStatTime;
		wb->coreMetric().onOpenEnd();

		if (!wb->GetBMP()->bDbSheet)
			app->runTidyMemUsageTask(gs_WoEtSettings.GetTidyImagePoolThreshold());
		return WO_OK;
	}
}

/*!
 * @brief 关闭文件
 */
WEB_EXPORT WebInt Close()
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("Close");
	gs_security_doc->OnDocClose();

	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	{
		IBook* pBook = wbs->GetWorkbook()->GetCoreWorkbook()->GetBook();
		wo::util::DbCdcScope cdcScope(wbs->GetWorkbook(), true);
	}
	wbs->Close(false);

	class KEnumUserConn : public IKEnumUserConn
	{
	public:
		KEnumUserConn() {}
		virtual int Do(IKUserConn *pUserConn) override
		{
			pUserConn->reset();
			return S_OK;
		}
	};
	IKUserConns* userConns = app->getCoreApp()->getUserConns();
	if (userConns) {
		KEnumUserConn euc;
		VS(userConns->enumUserConn(&euc));
		userConns->clear();
	}
	return WO_OK;
}

static void	ClearCellShapeRefMark(wo::KEtWorkbook* wb)
{
	BOOK_INFO_COLLECT *info = wb->GetCoreWorkbook()->GetBook()->GetBookInfoCollect();
	if (!info) 
	{
		return;
	}
	// 单元格图片标记清除掉
	alg::ClearBits(info->data, BOOK_INFO_COLLECT::_maskCellPic);
	alg::ClearBits(info->data, BOOK_INFO_COLLECT::_maskCellImageRefError);
}

static void CollectCellShapeRef(wo::KEtWorkbook* wb)
{
	BOOK_INFO_COLLECT *info = wb->GetCoreWorkbook()->GetBook()->GetBookInfoCollect();
	if (!info) 
	{
		return;
	}

	// 如果没有单元格图片 不收集埋点
	if (!alg::IsBitUsed(info->data, BOOK_INFO_COLLECT::_maskCellPic))
	{
		return;
	}

	bool isRefError = alg::IsBitUsed(info->data, BOOK_INFO_COLLECT::_maskCellImageRefError);

	FILEFORMAT fileFormat = wb->GetCoreWorkbook()->GetFileFormat();
	ks_wstring sFileFormat = __X("unknown");
	switch(fileFormat)
	{
		case ffXLS:
			sFileFormat = __X("ffXLS");
			break;
		case ffXLSX:
			sFileFormat = __X("ffXLSX");
			break;	
		case ffDBT:
			sFileFormat = __X("ffDBT");
			break;	
		case ffKSHEET:
			sFileFormat = __X("ffKSHEET");
			break;	
		case ffET:
			sFileFormat = __X("ffET");
			break;	
	}

	ks_wstring collectInfoString = __X("behaviour_cell_image_ref_") + sFileFormat;
	if (isRefError)
	{
		collectInfoString += __X("_error");
	}

	wo::util::CollectInfo(wb, collectInfoString.c_str(), 0);
}

static WebInt DealSaveResult(HRESULT hr, wo::KEtWorkbooks* wbs, const char* filename)
{
	if(SUCCEEDED(hr))
		return WO_OK;

	WEBLOG_ERROR_FMT("Failed to save to %s, error = 0x%x", filename, hr);
	WEBLOG_DUMP_BT();

	if (hr != E_ABORT && hr != IO_E_CANCEL)
		WOLOG_ERROR << "file save error, hr:" << GetErrString(hr) << " " << hr;

	// 保存中断时，io模块返回的是 IO_E_CANCEL 计算中断返回的是 E_ABORT，
	// 外网似乎有返回值被覆盖的问题，为了保险再判断中断标记位,返回 WO_IO_CANCEL 后将fork保存
	if (hr == E_ABORT || hr == IO_E_CANCEL || g_isCanBreak && gs_isBreak.get())
	{
		ASSERT(hr == E_ABORT || hr == IO_E_CANCEL); // 错误码可能（不一定，也可能是时机问题）被覆盖了
		WOLOG_INFO << "cancel file save";
		return WO_IO_CANCEL;
	}

	if (IO_E_NEED_PERMISSION == hr)
		return WO_SECDOC_NEED_PERMISSION;
	else if (IO_E_SDSERVER_ERROR == hr)
		return WO_SECDOC_NETWORK_UNREACHABLE;
		
	return WO_FAIL;
}

static void ClearDocSlimTempData()
{
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	IBook* pBook = wbs->GetWorkbook()->GetCoreWorkbook()->GetBook();
	if (!pBook)
		return;
	ks_stdptr<IEtDocSlimData> spDocSlimData;
	pBook->GetExtDataItem(edBookDocSlimData, (IUnknown**)&spDocSlimData);
	if (!spDocSlimData)
		return;
	BookSlimData stData;
	stData.emptyCellCount = 0;
	stData.unusedShapesCount = 0;
	stData.overlapShapesCount = 0;
	stData.cellPicturesCount = 0;
	stData.duplicateFormatConditionCount = 0;
	stData.unusedDuplicateStyleCount = 0;
	stData.sameSheetProtectionUserRangeCount = 0;
	spDocSlimData->SetBookDocSlimTmpData(stData);
}

static void UpdateDocSlimData()
{
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	IBook* pBook = wbs->GetWorkbook()->GetCoreWorkbook()->GetBook();
	if (!pBook)
		return;
	ks_stdptr<IEtDocSlimData> spDocSlimData;
	pBook->GetExtDataItem(edBookDocSlimData, (IUnknown**)&spDocSlimData);
	if (!spDocSlimData)
		return;
	BookSlimData* pSlimData = spDocSlimData->GetBookDocSlimData();
	BookSlimData* pSlimTmpData = spDocSlimData->GetBookDocSlimTmpData();
	if (!pSlimData || !pSlimTmpData)
	{
		WOLOG_INFO << "[UpdateDocSlimData] get BookSlimData fail";
		return;
	}
	pSlimData->isUpdateSlimData = true;
	WOLOG_INFO << "[UpdateDocSlimData BookSlimData] emptyCellCount=" << pSlimData->emptyCellCount << " unusedShapesCount=" << 
	pSlimData->unusedShapesCount << " overlapShapesCount=" << pSlimData->overlapShapesCount << " cellPicturesCount=" << 
	pSlimTmpData->cellPicturesCount << " duplicateFormatConditionCount=" << pSlimTmpData->duplicateFormatConditionCount << " unusedDuplicateStyleCount=" << 
	pSlimTmpData->unusedDuplicateStyleCount<< " sameSheetProtectionUserRangeCount=" << pSlimTmpData->sameSheetProtectionUserRangeCount; 
	// 子进程走forkQuery机制，主进程直接更新
	if (gs_procType != WebProcTypeChild)
	{
		WOLOG_INFO << "[UpdateDocSlimData WebProcTypeParent]";
		pSlimData->emptyCellCount = pSlimTmpData->emptyCellCount;
		pSlimData->unusedShapesCount = pSlimTmpData->unusedShapesCount;
		pSlimData->overlapShapesCount = pSlimTmpData->overlapShapesCount;
		pSlimData->cellPicturesCount = pSlimTmpData->cellPicturesCount;
		pSlimData->duplicateFormatConditionCount = pSlimTmpData->duplicateFormatConditionCount;
		pSlimData->unusedDuplicateStyleCount = pSlimTmpData->unusedDuplicateStyleCount;
		pSlimData->sameSheetProtectionUserRangeCount = pSlimTmpData->sameSheetProtectionUserRangeCount;
		return;
	}
	WOLOG_INFO << "[UpdateDocSlimData WebProcTypeChild]";
	binary_wo::BinWriter bw;
	bw.addStringField(__X("updateDocSlimData"), "operate");
	if (pSlimTmpData->emptyCellCount <= UINT_MAX)
		bw.addUint32Field(pSlimTmpData->emptyCellCount, "emptyCellCount");
	else
	 	bw.addUint32Field(UINT_MAX, "emptyCellCount");

	bw.addUint32Field(pSlimTmpData->unusedShapesCount, "unusedShapesCount");
	bw.addUint32Field(pSlimTmpData->overlapShapesCount, "overlapShapesCount");
	bw.addUint32Field(pSlimTmpData->cellPicturesCount, "cellPicturesCount");
	bw.addUint32Field(pSlimTmpData->duplicateFormatConditionCount, "duplicateFormatConditionCount");
	bw.addUint32Field(pSlimTmpData->unusedDuplicateStyleCount, "unusedDuplicateStyleCount");
	bw.addUint32Field(pSlimTmpData->sameSheetProtectionUserRangeCount, "sameSheetProtectionUserRangeCount");

	binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice input = {shbt.get(), bw.writeLength()};
	WebSlice output = {nullptr, 0};
	gs_callback->onSyncParentProc(&input, &output);
	gs_callback->onFreeWebSlice(&output);
}
/*!
 * @brief 保存文件
 */
const WebInt INV_SAVE_TASk = -10000;
WebInt g_prevSaveTaskVer = INV_SAVE_TASk;

static void CollectSaveAsFail(HRESULT hr, unsigned int ms, const UserContext* pUserContext)
{
	if (SUCCEEDED(hr) || !pUserContext || !app->GetWorkbooks())
		return;

	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (!wb) 
		return;

	wo::WoOpenSaveCollector collector(wb, __X("saveAs_faile"), ms);
	collector.addResult(hr);
	collector.addUserId(pUserContext->userID);
}

WEB_EXPORT WebInt SaveAs(const char* filename, const UserContext* userContext, int tag)
{
	BASE_PROCESS_CHECK_RET_FAIL
    wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* wb = wbs->GetWorkbook();
    if (nullptr == wb)
    {
        WOLOG_INFO << "[file_saveas] save error, workbook not opened";
        return WO_FAIL;
	}

	VARIANT_BOOL bReadOnly = VARIANT_FALSE;
	wbs->GetWorkbook()->GetCoreWorkbook()->get_ReadOnly(&bReadOnly);
	if (bReadOnly)
		return WO_READ_ONLY_SAVE_FAIL;
	HRESULT hr = E_FAIL;
	wo::util::CallTimeStat callTime("SaveAs", nullptr, 
		[&hr, userContext](unsigned int ms) {
			unsigned int breakElapse = gs_breakTimeStat.takeElapseMS();
			CollectCallTimeApiRand("SaveAs", ms, breakElapse); 
			CollectSaveAsFail(hr, ms, userContext);
	});

	bool maskCellPic = false;
	// 清除标记
	BOOK_INFO_COLLECT *info = wb->GetCoreWorkbook()->GetBook()->GetBookInfoCollect();
	if (info) 
	{
		maskCellPic = alg::IsBitUsed(info->data, BOOK_INFO_COLLECT::_maskCellPic);
		// 单元格图片标记清除掉
		alg::ClearBits(info->data, BOOK_INFO_COLLECT::_maskCellPic);
		alg::ClearBits(info->data, BOOK_INFO_COLLECT::_maskCellImageRefError);
	}

	WebOpenArguments webopenarguments{};
	TruncateOpenConfigHelper truncateOpenConfigHelper(&webopenarguments);

	// wbs->GetWorkbook()->writePerfStatLog();
	SetBreak(false); // 开始保存后一小段时间（比如800ms）内不中断，由服务端控制
	gs_breakTimeStat.restart();
	wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
	if (!IsIOCancelEnable() || gs_procType != WebProcTypeMaster)
		g_isCanBreak = false;
	ClearDocSlimTempData();
	gs_security_doc->OnBeforeDocSave(GetCurrentDoc(),  userContext->userID, userContext->sessionID, userContext->userRequestInfo);
	QString fileName = QString::fromUtf8(filename);

	hr = wbs->GetWorkbook()->Save(krt::utf16(fileName), &g_prevSaveTaskVer);

	gs_security_doc->OnAfterDocSave(GetCurrentDoc());
	IKDocSlimHelper* pDocSlimHelper = wbs->GetWorkbook()->GetCoreWorkbook()->GetDocSlimHelper();
	if (SUCCEEDED(hr) && wo::ExecDocSlimHelper::isNeedSlim() && pDocSlimHelper)
	{
		pDocSlimHelper->UpdateWoSaveDocSlimData();
		UpdateDocSlimData();
	}
		
	if (SUCCEEDED(hr))
	{
		CollectCellShapeRef(wb);
	}
	
	if (info)
	{
		// 存盘被中断等情况，或非xls和xlsx格式的文档存盘时，需要还原单元格图片标记，这里统一还原
		alg::SetBit(info->data, BOOK_INFO_COLLECT::_maskCellPic, maskCellPic);
	}

	// 埋点需要这个返回值
	hr = DealSaveResult(hr, wbs, filename);
	return hr;
}

WEB_EXPORT WebInt ConnectPrevSaveTask(WebInt cloudVer)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (isWoRuntimeDebug())
	{
		WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_INFO,
			"ConnectPrevSaveTask clound:%d, task:%d", cloudVer, g_prevSaveTaskVer);
	}

	if (g_prevSaveTaskVer != INV_SAVE_TASk && wbs != nullptr)
	{
		wbs->ConnectCloudVer(cloudVer, g_prevSaveTaskVer);
		g_prevSaveTaskVer = INV_SAVE_TASk;
	}
	return WO_OK;
}

WEB_EXPORT WebInt SaveAsFormat(const char *filename, const char *format, const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook())
	{
		WOLOG_INFO << "[file_saveas_format] save error, workbook not opened";
		return WO_FAIL;
	}

	wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
	g_isCanBreak = false; // 先不支持中断

	API_TIME_STATE(SaveAsFormat, userContext->userID);
	gs_security_doc->OnBeforeDocSave(GetCurrentDoc(), userContext->userID, userContext->sessionID, userContext->userRequestInfo);
	QString qFilename = QString::fromUtf8(filename);
	QString qFormat = QString::fromUtf8(format);
	HRESULT hr = wbs->GetWorkbook()->SaveAsFormat(krt::utf16(qFilename), krt::utf16(qFormat));

	gs_security_doc->OnAfterDocSave(GetCurrentDoc());
	return DealSaveResult(hr, wbs, filename);
}

WEB_EXPORT WebInt SaveAsLiteVersion(const UserContext* userContext, const char *filename, int scene, const WebSlice *slice)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook())
	{
		WOLOG_INFO << "[SaveAsLiteVersion] save error, workbook not opened";
		return WO_FAIL;
	}

	VARIANT_BOOL bReadOnly = VARIANT_FALSE;
	wbs->GetWorkbook()->GetCoreWorkbook()->get_ReadOnly(&bReadOnly);
	if (bReadOnly)
	{
		WOLOG_INFO << "[SaveAsLiteVersion] save error, workbook is readonly";
		return WO_READ_ONLY_SAVE_FAIL;
	}

	if(wo::secdoc::IsSecurityDoc(wbs->GetWorkbook()->GetCoreWorkbook()))
	{
		WOLOG_INFO << "[SaveAsLiteVersion] save error, workbook is security doc";
		return WO_NOT_SUPPORT_SECURITY_DOC;
	}

	API_TIME_STATE(SaveAsLiteVersion, userContext->userID);
	wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
	g_isCanBreak = false; //先不支持保存中断
	gs_security_doc->OnBeforeDocSave(GetCurrentDoc(), userContext->userID, userContext->sessionID, userContext->userRequestInfo);

	wo::KEtWorkbook* wb = wbs->GetWorkbook();
	_Workbook* coreWb = wb->GetCoreWorkbook();
	wo::IBookStake* bkStake = coreWb->GetBook()->GetWoStake();
	wo::LiteFileUpdateParams params;
	if (slice && slice->size != 0 && slice->data != nullptr)
	{
		BinReader rd(slice->data, slice->size);
		VarObjRoot infoRoot = rd.buildRoot();
		VarObj info = infoRoot.cast();
		wo::LiteFileParamsHelper lfh(wb, info);
		lfh.Do(params);
	}
	ks_stdptr<IBookExportLiteVersionSetting> pSetting;
	bkStake->generateSaveAsLiteVersionParam(&pSetting);
	if (!pSetting)
	{
		WOLOG_INFO << "[SaveAsLiteVersion] save error, IBookExportLiteVersionSetting create failed!!!";
		return WO_FAIL;
	}
	pSetting->SetMode(params.mode);
	pSetting->SetIsTurnOnLiteVersion(true);
	if (BookExportLiteVersionMode_SpecifiedRanges == params.mode)
	{
		pSetting->SetUpdateRangeVec(std::move(params.updateRanges));
	}
	KComVariant saveAsLiteVersionParam(pSetting);

	QString fileName = QString::fromUtf8(filename);
	WOLOG_INFO << "[SaveAsLiteVersion] SaveAs process start: path: "<< fileName;
	HRESULT hr = wbs->GetWorkbook()->SaveAsLiteVersion(krt::utf16(fileName), &g_prevSaveTaskVer,saveAsLiteVersionParam);
	WOLOG_INFO << "[SaveAsLiteVersion] SaveAs process end";
	gs_security_doc->OnAfterDocSave(GetCurrentDoc());
	return DealSaveResult(hr, wbs, filename);
}

WEB_EXPORT WebInt ExecEncryptDecrypt(const WebSaveArguments *arg, const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook()) {
		WOLOG_ERROR << "[ExecEncryptDecrypt] save error, workbook not opened";
		return WO_FAIL;
	}

    if (nullptr == arg || ((int)SAVE_AS_MODE_SEC == arg->saveAsMode && nullptr == arg->companyID)) {
        WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_ERROR, "ExecEncryptDecrypt: invalid parameter");
        return WO_FAIL;
    }

	VARIANT_BOOL bReadOnly = VARIANT_FALSE;
	wbs->GetWorkbook()->GetCoreWorkbook()->get_ReadOnly(&bReadOnly);
	if (bReadOnly) {
		WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_ERROR, "ExecEncryptDecrypt: this file is readOnly, couldn't save");
		return WO_READ_ONLY_SAVE_FAIL;
	}

	API_TIME_STATE(ExecEncryptDecrypt, nullptr);

	wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
	g_isCanBreak = false; //先不支持保存中断
	if ((int)SAVE_AS_MODE_SEC == arg->saveAsMode && nullptr != arg->companyID) {
		app->getCoreApp()->GetWoSecDocContext()->SetWebCompanyID(krt::utf16(QString::fromUtf8(arg->companyID)));
	}

	gs_security_doc->OnBeforeDocSave(GetCurrentDoc(), arg->userID, arg->sessionID, userContext->userRequestInfo, (SaveAsMode)arg->saveAsMode);
	QString fileName = QString::fromUtf8(arg->filename);
	HRESULT hr = wbs->GetWorkbook()->ExecEncryptDecrypt(krt::utf16(fileName), (SaveAsMode)arg->saveAsMode);
	gs_security_doc->OnAfterDocSave(GetCurrentDoc());
	wo::secdoc::UpdateDocInfo(gs_security_doc.get(), GetCurrentDoc());

	if (FAILED(hr)) {
		WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_ERROR, "file encrypt/decrypt error, hr:%d", hr);
		return WO_FAIL;
	}
	return WO_OK;
}

typedef WebInt (wo::ExecDetail::*Arg0Func)();

inline bool IsStringEmpty(const char* s)
{
	return nullptr == s || '\0' == *s;
}

template<typename Func, typename ...Args>
WebInt inlDecorateProcVariadic(
	const UserContext* userContext,
	const WebSlice *commands,
	wo::DetailExtArgs extArgs,
	const WebSlice *serExtraParam,
	WebInt (Func::*func)(Args... args),
	Args... funcArgs)
{
	const char *connID = nullptr;
	const char *userID = nullptr;
	if (userContext)
	{
		connID = userContext->connID;
		userID = userContext->userID;
		extArgs.corePermissionID = userContext->corePermissionID;
		extArgs.connFrom = userContext->connFrom;
		extArgs.connScene = userContext->connScene;
		extArgs.connLife = userContext->connLife;
		extArgs.userRequestInfo = userContext->userRequestInfo;
	}
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook()) {
		if (gs_callback && gs_callback->log) {
			gs_callback->log(WO_LOG_WARN, "file not opened");
		}

		// 某些命令已经不需要“当前文档”了，比如文档转换
		// 有 connID/userID 时，必须要有当前文档
		if (!IsStringEmpty(connID) && !IsStringEmpty(userID))
			return WO_FAIL;
		
		KS_TRY {
			wo::ExecDetail ed(wbs, connID, userID, nullptr, extArgs, commands, serExtraParam, false);
			return (ed.*func)(std::forward<Args>(funcArgs)...);
		} 
		KS_CATCH(const binary_wo::ks_exception_msg& e) {
			HRESULT hr = e.get_result();
			WO_LOG_X(
				wbs->GetWorkbook()->getLogger(), WO_LOG_ERROR, "ks_exception, hr: %d, error: %s, errorMsg: %s", hr, GetErrString(hr), e.get_result_msg());
			wo::exec_detail::SignalError(
				gs_callback->signal, connID, getMsgTypeName(wo::msgType_Exec), hr, commands, GetErrWideString(hr), e.get_result_msg());
			return WO_FAIL;
		}
		KS_CATCH(const ks_exception& e) {
			HRESULT hr = e.get_result();
			wo::exec_detail::SignalError(gs_callback->signal, connID, getMsgTypeName(wo::msgType_Exec), hr, commands, GetErrWideString(hr));
			return WO_FAIL;
		}
		return WO_OK;
	}

	const std::string &sessionID = gs_security_doc->GetCurExecCmdSessionID();
	KS_TRY {
		wo::ExecDetail ed(wbs, connID, userID, sessionID.c_str(), extArgs, commands, serExtraParam, false);
		return (ed.*func)(std::forward<Args>(funcArgs)...);
	}
	KS_CATCH(const binary_wo::ks_exception_msg& e) {
		HRESULT hr = e.get_result();
		WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_ERROR, "ks_exception, hr: %d, error: %s, errorMsg: %s", hr, GetErrString(hr), e.get_result_msg());
		wo::exec_detail::SignalError(
			gs_callback->signal, connID, getMsgTypeName(wo::msgType_Exec), hr, commands, GetErrWideString(hr), e.get_result_msg());
		return WO_FAIL;
	}
	KS_CATCH(const ks_exception& e) {
		HRESULT hr = e.get_result();
		WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_ERROR, "ks_exception, hr: %d, error: %s", hr, GetErrString(hr));
		wo::exec_detail::SignalError(
			gs_callback->signal, connID, getMsgTypeName(wo::msgType_Exec), hr, commands, GetErrWideString(hr));
		return WO_FAIL;
	}
	return WO_OK;
}

inline WebInt inlDecorateProc(
	const UserContext* userContext, const WebSlice *commands, Arg0Func func, wo::DetailExtArgs args)
{
	return inlDecorateProcVariadic(userContext, commands, args, nullptr, func);
}

inline WebInt inlDecorateProc(
	const UserContext* userContext, const WebSlice *commands, Arg0Func func, wo::DetailExtArgs args, const WebSlice *serExtraParam)
{
	return inlDecorateProcVariadic(userContext, commands, args, serExtraParam, func);
}

inline WebInt inlDecorateProc(
	const UserContext* userContext, const WebSlice *commands, Arg0Func func)
{
	wo::DetailExtArgs dummy;
	return inlDecorateProc(userContext, commands, func, dummy);
}

/*!
 * @brief new 文件
 */
WEB_EXPORT WebInt New()
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wbs->New();
	return WO_OK;
}

WEB_EXPORT WebInt Query(const UserContext* userContext, const WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	BeginCoreMetricCollect(wo::KMetricsType::kQuery);
	ExecTimeCollect::gs_queryCount++;
	wo::CallTimeMemUsedStat callTime("Query", userContext->userID, [](unsigned int ms, unsigned int memusageKb, unsigned int beginMemKb) { 
		ExecTimeCollect::gs_querySum += ms; 
		CollectCallTimeApiRand(API_COLLECT_STR_NAME(Query), ms);
		
		EndCoreMetricCollect(API_COLLECT_STR_NAME(Query), ms, [ms, memusageKb, beginMemKb](wo::KCoreMetric & cm) {
			SyncCoreMetricTime(cm, wo::KCoreMetricSyncOp::kQuery, ms);
			cm.setAllCountAndTime(ExecTimeCollect::gs_queryCount, ExecTimeCollect::gs_querySum);
			cm.setU32Metrics(wo::KU32MetricItem::kMemUsedKb, memusageKb);
			cm.setU32Metrics(wo::KU32MetricItem::kBeginMemKb, beginMemKb);
		});
	});
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecQuery);
}

WEB_EXPORT WebInt QueryPlay(const UserContext* userContext, const WebSlice *commands, WebInt bForceInit)
{
	BASE_PROCESS_CHECK_RET_FAIL
	API_TIME_STATE(QueryPlay, userContext->userID);
	wo::DetailExtArgs args;
	args.commitVersion = WO_QUERY_PLAY_ID;

	// QueryPlay增加权限屏蔽, 以避免查看回放时因为dbsheet权限检查抛异常
	// dbsheet的权限设计仍然有纰漏. 但因为ExecDirect直接屏蔽了权限检查, 因此QueryPlay也可以屏蔽
	std::unique_ptr<wo::DbSheet::DisableDbProtectScope> upDisablePrt;
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
	if (pKEtWorkbook)
	{
		IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
		if (pBook->GetBMP()->bKsheet || pBook->GetBMP()->bDbSheet)
		{
			ks_stdptr<IUnknown> spUnknown;
			VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
			ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
			ASSERT(spDBUserGroups);
			if (spDBUserGroups)
			{
				ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
				spDBUserGroups->GetJudgement(&spProtectionJudgement);
				upDisablePrt.reset(new wo::DbSheet::DisableDbProtectScope(spProtectionJudgement));
			}
		}
	}
	if (bForceInit)
		return inlDecorateProc(userContext, NULL, &wo::ExecDetail::ExecInit, args);
	else
		return inlDecorateProc(userContext, NULL, &wo::ExecDetail::ExecQuery, args);
}

WEB_EXPORT WebInt QueryInit(const UserContext* userContext, const WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	BeginCoreMetricCollect(wo::KMetricsType::kQueryInit);
	IKUserConn* pUserConn = app->getCoreApp()->getUserConns()->getUserConn(userContext->connID);
	if (pUserConn)
	{
		WOLOG_ERROR << "[queryInit] already queryInit, must UserQuit before queryInit again. connId: " << userContext->connID;
	}

    wo::util::SendOpenProgress(userContext->connID, __X("queryinit"), __X("queryinit_exec"), 50, 0, 0);

	std::string connIDUserID;
	connIDUserID.reserve(64);
	connIDUserID.append(userContext->connID).append(",").append(userContext->userID);
	wo::CallTimeMemUsedStat callTime("QueryInit", connIDUserID.c_str(), [userContext](unsigned int ms, unsigned int memusageKb, unsigned int beginMemKb) { 
		ExecTimeCollect::gs_queryInitSum += ms;
		++ExecTimeCollect::gs_queryInitCount;
		
		wo::WoEtSetting * settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
		if (settings)
			CollectCallTimeRand("QueryInit", ms, false, userContext->connID, settings->GetQueryInitTimeRand());
		EndCoreMetricCollect(API_COLLECT_STR_NAME(QueryInit), ms, [memusageKb, beginMemKb](wo::KCoreMetric & cm) {
			cm.setAllCountAndTime(ExecTimeCollect::gs_queryInitCount, ExecTimeCollect::gs_queryInitSum);
			cm.setU32Metrics(wo::KU32MetricItem::kMemUsedKb, memusageKb);
			cm.setU32Metrics(wo::KU32MetricItem::kBeginMemKb, beginMemKb);
		});
	});

	const WebInt res = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecInit);
	
	if (!pUserConn)
		pUserConn = app->getCoreApp()->getUserConns()->getUserConn(userContext->connID);
	if (pUserConn)
	{
		IKETUserConn *pEtUserConn = static_cast<IKETUserConn*>(pUserConn);
		pEtUserConn->setHasCall(wo::UserConnFuncCall::kQueryInit);
	}
	return res;
}

WEB_EXPORT WebInt QuerySummaryTile(const UserContext* userContext, const WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("ExecSummaryTile");
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecSummaryTile);
}

WEB_EXPORT WebInt QuerySummary(const UserContext* userContext, const WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("QuerySummary");
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecSummary);
}

WEB_EXPORT WebInt QueryAttachments(const UserContext* userContext, const WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (WO_OK != CheckWorkbookOpened("QueryAttachments"))
		return WO_FAIL;
	
	wo::util::CallTimeStat callTime("QueryAttachments");
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecAttachments);
}

WEB_EXPORT WebInt QuerySheetsAttachments(const UserContext* userContext, const WebSlice* commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (WO_OK != CheckWorkbookOpened("QuerySheetsAttachments"))
		return WO_FAIL;

	wo::util::CallTimeStat callTime("QuerySheetsAttachments");
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecSheetsAttachments);
}

WEB_EXPORT WebInt QuerySubscriptionInfo(const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (WO_OK != CheckWorkbookOpened("QuerySubscriptionInfo"))
		return WO_FAIL;

	wo::util::CallTimeStat callTime("QuerySubscriptionInfo");
	return inlDecorateProc(userContext, nullptr, &wo::ExecDetail::ExecSubscriptionInfo);
}

WEB_EXPORT WebInt QueryMemoStat(const UserContext* userContext, const WebSlice* commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("QueryMemoStat");
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecMemoStat);
}

WEB_EXPORT WebInt QueryBlock(const UserContext* userContext, const WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("QueryBlock");
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecBlock);
}

WEB_EXPORT WebInt Exec(const UserContext* userContext, const WebSlice *commands, const WebSlice *serExtraParam)
{
	BASE_PROCESS_CHECK_RET_FAIL
	BeginCoreMetricCollect(wo::KMetricsType::kExec);
	ExecTimeCollect::gs_execCount++;
	wo::CallTimeMemUsedStat callTime("Exec", userContext->userID, [](unsigned int ms, unsigned int memusageKb, unsigned int beginMemKb) { 
		ExecTimeCollect::gs_execSum += ms; 
		CollectCallTimeApiRand(API_COLLECT_STR_NAME(Exec), ms);
		EndCoreMetricCollect(API_COLLECT_STR_NAME(Exec), ms, [memusageKb, beginMemKb](wo::KCoreMetric & cm) {
			cm.setAllCountAndTime(ExecTimeCollect::gs_execCount, ExecTimeCollect::gs_execSum);
			cm.setU32Metrics(wo::KU32MetricItem::kMemUsedKb, memusageKb);
			cm.setU32Metrics(wo::KU32MetricItem::kBeginMemKb, beginMemKb);
		});
	});
	wo::DetailExtArgs extArgs;
	extArgs.bNeedCleanChildProc = false;
	gs_security_doc->SetCurExecCmdUser(userContext->userID, userContext->sessionID);
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecCmd, extArgs, serExtraParam);
}

WEB_EXPORT WebInt ExecDirect(const UserContext* userContext, const WebSlice *commands, CommitID commitVersion)
{
	BASE_PROCESS_CHECK_RET_FAIL
	// 统计查看单元格记录时文件打开超过 2 秒的文件
	if (!gs_hasCollectLogOpenTime)
	{
		wo::KEtWorkbooks* wbs = app->GetWorkbooks();
		wo::KEtWorkbook* wb = wbs->GetWorkbook();
		if (wb && wb->getFileId()) 
		{
			const int openTimeSeconds = wb->GetMemoStat()->GetOpenTime() / 1000ULL / 1000ULL  / 1000ULL;
			if (openTimeSeconds > 2)
			{
				wo::util::CollectInfo(wb, __X("behaviour_log_file_open_time"), openTimeSeconds * 1000);
			}
		}

		gs_hasCollectLogOpenTime = true;
	}
	
	API_TIME_STATE(ExecDirect, userContext->userID);
	WOLOG_INFO << "[ExecDirect] commitVersion: " << commitVersion; // 方便分析回放失败、卡住
	wo::DetailExtArgs args;
	args.commitVersion = commitVersion;
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecCmdDirect, args);
}

WEB_EXPORT WebInt ExecComment(const UserContext* userContext, const WebSlice *commands, const WebSlice *serExtraParam)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("ExecComment", NULL, [](unsigned int ms) { ExecTimeCollect::gs_execSum += ms; });
	wo::DetailExtArgs dummy;
	dummy.bNeedCleanChildProc = false;
	gs_security_doc->SetCurExecCmdUser(userContext->userID, userContext->sessionID);
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecComment, dummy, serExtraParam);
}

/*
@brief 非前端接口，用于提供给转换服务调用
*/
WEB_EXPORT WebInt ExecExtraCommand(const UserContext* userContext, const WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("ExecExtraCommand", userContext->userID);
	WebInt ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExtraCommand);

	if (ret != WO_OK)
		WEBLOG_DUMP_BT();
	
	return ret;
}

/*
@brief 初始化连接状态
*/
WEB_EXPORT WebInt InitConnState(const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	return inlDecorateProc(userContext, nullptr, &wo::ExecDetail::InitConnState);
}

WEB_EXPORT WebInt UpdateShapes(const UserContext* userContext, const WebSlice *shapeIds)
{
	BASE_PROCESS_CHECK_RET_FAIL
	API_TIME_STATE(UpdateShapes, userContext->userID);
	return inlDecorateProc(userContext, shapeIds, &wo::ExecDetail::ExecUpdateShapes);
}

WEB_EXPORT WebInt UserJoin(const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	API_TIME_STATE(UserJoin, userContext->connID);
	IKETUserConns *pUserConns = static_cast<IKETUserConns*>(app->getCoreApp()->getUserConns());
	pUserConns->onUserJoin(userContext->connID);
	return gs_security_doc->OnUserJoin(GetCurrentDoc(), userContext->userID);
}

WebInt UsersQuit(WebSlice *users, bool isUserQuit);
WEB_EXPORT WebInt UserQuit(const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	QString strConnID = QString::fromUtf8(userContext->connID);
	QString strUserID = QString::fromUtf8(userContext->userID);
	BinWriter writer;
	writer.beginArray("users");
	{
		writer.beginStruct();
		{
			writer.addStringField(krt::utf16(strConnID), "connID");
			writer.addStringField(krt::utf16(strUserID), "userID");
		}
		writer.endStruct();
	}
	writer.endArray();
	BinWriter::StreamHolder stream = writer.buildStream();
	WebSlice slice = { stream.get(), writer.writeLength() };
	return UsersQuit(&slice, true);
}

WEB_EXPORT WebInt UsersQuit(WebSlice *users)
{
	BASE_PROCESS_CHECK_RET_FAIL
	return UsersQuit(users, false);
}

bool userQuit(
	PCWSTR userId,
	PCWSTR connId,
	ISharedLinkMgr* pSharedLinkMgr,
	IKUserConn* pUserConn,
	bool logConnId)
{
	QByteArray userIDUtf8 = QString::fromUtf16(userId).toUtf8();
	gs_security_doc->OnUserQuit(GetCurrentDoc(), userIDUtf8);
	if(pSharedLinkMgr)
		pSharedLinkMgr->ConnQuit(pUserConn);
	if (pUserConn)
	{
		if (logConnId)
		{
			IKETUserConn * pEtConn = static_cast<IKETUserConn*>(pUserConn);
			WOLOG_INFO << "conn quit: " << connId << " userId: " << userId 
					<< " hasQueryInit " << pEtConn->hasCall(wo::UserConnFuncCall::kQueryInit)
					<< " hasUserJoin " << pEtConn->hasCall(wo::UserConnFuncCall::kUserJoin)
					<< " isHttpCall " << pEtConn->hasCall(wo::UserConnFuncCall::kHttpCalling);
		}
		pUserConn->quit();
		return true;
	}
	return false;
}

WebInt UsersQuit(WebSlice *users, bool isUserQuit)
{
	BinReader reader(users->data, users->size);
	VarObjRoot root = reader.buildRoot();
	VarObj param = root.cast();
	VarObj varUsers = param.get_s("users");
	if (varUsers.type() != typeArray)
	{
		WOLOG_ERROR << "arguments error";
		return WO_FAIL;
	}

	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
	const char * szName = isUserQuit ? "UserQuit" : "UsersQuit";
	const char * szColName = isUserQuit ? API_COLLECT_STR_NAME(UserQuit) : API_COLLECT_STR_NAME(UsersQuit);
	wo::util::CallTimeStat callTime(szName, nullptr, [szColName](unsigned int ms) {
		CollectCallTimeApiRand(szColName, ms);
	});
	
	if (pKEtWorkbook)
	{
		IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
		pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	}
	IKUserConns* pUserConns = app->getCoreApp()->getUserConns();

	int quitCnt = 0;
	for(int32 i = 0; i < varUsers.arrayLength(); ++i)
	{
		VarObj item = varUsers.at(i);
		PCWSTR userID = item.field_str("userID");
		PCWSTR connID = item.field_str("connID");
		QByteArray connIDUtf8 = QString::fromUtf16(connID).toUtf8();
		IKUserConn* pUserConn = pUserConns->getUserConn(connIDUtf8);
		bool hasSharedId = pUserConn == nullptr ? false : !pUserConn->getSharedId().isEmpty();
		
		if (userQuit(userID, connID, spSharedLinkMgr, pUserConn, quitCnt < 10))
		{
			++quitCnt;
			if (pKEtWorkbook)
				pKEtWorkbook->collectInfo().onUserQuit(connID, hasSharedId);
		}
		else
		{
			WOLOG_ERROR << "connId not found: " << connIDUtf8.data();
			if (pKEtWorkbook)
				pKEtWorkbook->collectInfo().onNotFoundUserQuit(connID);
		}
	}

	WOLOG_INFO << "conn quit cnt: " << quitCnt << " conn cnt: " << pUserConns->count();

	if (gs_procType == WebProcTypeMaster && pUserConns->count() - gs_collectUserConnsCount > userConnsCollectFreq)
	{
		gs_collectUserConnsCount = pUserConns->count();
		wo::util::CollectInfo(pKEtWorkbook, __X("behaviour_user_conns"), gs_collectUserConnsCount);
	}
	return WO_OK;
}

/*!
*@brief 返回连接的最新命令序号
*@connID connect ID
*@return 返回0表示连接不存在或者未发生命令
*/
WEB_EXPORT WebInt GetConnSeq(const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	ks_stdptr<IKEtApplication> ptrApp = app->getCoreApp();
	IKUserConn* ptrUserConn = ptrApp->getUserConns()->getUserConn(userContext->connID);
	if (ptrUserConn)
		return ptrUserConn->getCmdSeq();
	else
		return 0;
}

WEB_EXPORT WebInt DoIdle()
{
	BASE_PROCESS_CHECK_RET_FAIL
	// 注意: 服务端调DoIdle的机制上可能有bug. importrange支持中断的改动中有一个bug 814617, 认为Open后会先发至少一个query到内核, 然后才会执行DoIdle,
	// 但实际上这一点并没有得到保证. 因此在bug修复之前, 于DoIdle中新增其他功能时, 如果需要依赖IKUserConn, 要考虑会话不存在的可能性
	// 此前也有少量其他现象可能与此有关, 是一个历史遗留问题

	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* wb = wbs->GetWorkbook();
	if (wb)
	{
		// importrange的初始化应当放在resumecalculate之前
		// 且文件下载时要求有用户存在
		if (not wb->ImportrangeCalcInited() && wb->GetCoreApp()->getUserConns()->getCurrentUserConn() && wb->InitImportrangeCalc())
		{
			wo::DetailExtArgs dummy;
			inlDecorateProcVariadic(nullptr, nullptr, dummy, nullptr, &wo::ExecDetail::CalcImportrangeOnOpen);
			return WO_OK; // 初始化通过task引发importrange的有效计算及提交文件下载请求. 结束本次DoIdle以完成本次事务, 不与无事务的resumecalculate混淆
		}
	}
	if (wb)
	{
		gs_isBreak.set(false);
		bool resumeCalcHappened = false;
		DWORD calcDetail = RCD_None;
		wb->DoIdle(calcDetail, &resumeCalcHappened);

		if (resumeCalcHappened)
		{
			// 子进程无法下载文件. 要求在主进程完成. 目前服务端保证DoIdle只会对主进程调用
			ASSERT(gs_procType == WebProcTypeMaster);
			ExecSubscribeCrossBooks();
		}

		INT64 sumMs = ExecTimeCollect::gs_querySum + ExecTimeCollect::gs_queryInitSum + 
			ExecTimeCollect::gs_execSum + ExecTimeCollect::gs_exportSum;
		static INT64 s_sumMs = 0;
		if (sumMs - s_sumMs > 10000)
		{
			s_sumMs = sumMs;
			WO_LOG_X(wb->getLogger(), WO_LOG_INFO, "[ExecTimeCollect]: query:%dms init:%dms exec:%dms export:%dms", 
				ExecTimeCollect::gs_querySum, ExecTimeCollect::gs_queryInitSum, 
				ExecTimeCollect::gs_execSum, ExecTimeCollect::gs_exportSum);
		}

		
		if (gs_lastMemStatTime.time_since_epoch().count() > 0)
		{
			std::chrono::steady_clock::time_point curTime = std::chrono::steady_clock::now();
			const auto timeInterval = curTime - gs_lastMemStatTime;
			const int timeThreshold = gs_WoEtSettings.GetCollectMemDiffTime();
			if (timeInterval / std::chrono::minutes(1) >= timeThreshold)
			{
				// 每隔4分钟统计一次, 用于统计内存增涨情况(只上报增涨超过10M的)
				wo::FileInfoCollector(wb, nullptr).CollectMemInfoDiff();
				gs_lastMemStatTime = curTime;
			}
			
			{
				// 文件打开后30/60/90分钟各统计一次, 用于统计整体内存占用情况
				if (gs_memFullStatCount < 3 && ((curTime - gs_lastFullMemStatTime) / std::chrono::minutes(1) >= 30))
				{
					const WCHAR * wszName = __X("mem_open_30");
					if (gs_memFullStatCount == 1)
						wszName = __X("mem_open_60");
					else if (gs_memFullStatCount == 2)
						wszName = __X("mem_open_90");
					
					wo::FileInfoCollector(wb, nullptr).CollectMemInfo(wszName);
					++gs_memFullStatCount;
					gs_lastFullMemStatTime = curTime;
				}
			}
		}

		bool hasDynamicArray = alg::IsBitUsed(calcDetail, RCD_HasDynamicArray);
		if (wb && hasDynamicArray)
		{
			wo::DetailExtArgs dummy;
			inlDecorateProcVariadic(nullptr, nullptr, dummy, nullptr, &wo::ExecDetail::GenTask4DynamicArrayFormulaResumeCalc);
		}
	}

	if (gs_needUpdateCrossBook)
	{
		wo::DetailExtArgs dummy;
		std::string connId = g_childProcCrossbookConnId.toUtf8().toStdString();
		std::string userId = g_childProcCrossbookUerdId.toUtf8().toStdString();
		const UserContext userContext {connId.c_str(), userId.c_str(), nullptr, nullptr};
		inlDecorateProcVariadic(&userContext, nullptr, dummy, nullptr, &wo::ExecDetail::UpdateSupBooks);
	}

	if (wb && wb->getCalcStatus() != wo::KEtWorkbook::CalcStatus::calculating)
	{
		wo::DetailExtArgs dummy;
		inlDecorateProcVariadic(nullptr, nullptr, dummy, nullptr, &wo::ExecDetail::UpdateDbDirty);
	}

	if (wb)
	{
		wb->broadcastDelayData();
		app->getIntervalTaskManager().OnIdle(wb, gs_procType == WebProcTypeMaster);
		
		wb->signalMetric().sendCollectInfo(wb);
	}
	return WO_OK;
}

static WebInt ExportToLocalFile(const UserContext* userContext, WebSlice* commands, FILEFORMAT fileformat)
{
    wo::DetailExtArgs dummy;
    return inlDecorateProcVariadic(userContext, commands, dummy, nullptr,
                                   &wo::ExecDetail::ExecExportToLocalFile, fileformat);
}

static bool IsSupportSecurityDocType(WebInt type)
{
    static const std::set<WebInt> supportSecurityDocTypes{WO_EXPORT_XLSX, WO_EXPORT_ET, WO_EXPORT_ETT, WO_EXPORT_XLS,
                                                          WO_EXPORT_XLT, WO_EXPORT_XLSM, WO_EXPORT_XLTX,
                                                          WO_EXPORT_XLTM};
    return supportSecurityDocTypes.count(type) > 0;
}

WEB_EXPORT WebInt ExportAs(const UserContext* userContext, WebInt type, WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (!userContext->connID || !userContext->userID)
	{
		WOLOG_INFO << "[ExportAs] invalid args";
		return WO_FAIL;
	}

	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}

	// db不做判断
	if (!wb->GetBMP()->bDbSheet)
	{
		WebInt checkRes =  CheckUserAllVisiblePermission(userContext->connID, userContext->userID);
		if (checkRes != WO_OK)
		{
			return checkRes;
		}
	}

	WOLOG_INFO << "[ExportAs] connID: " << userContext->connID << ", userID: " << userContext->userID << ", type: " << type;
	API_TIME_STATE(ExportAs, userContext->userID);

	// 关闭保存计算中断
	DisableCalcBreak disableCalcBreak;
    QString sessionId;
    if (IsSupportSecurityDocType(type))
    {
        BinReader reader(commands->data, commands->size); 
        VarObjRoot root = reader.buildRoot();
        VarObj info = root.cast();
        if (info.has("wpsSid"))
            sessionId = QString::fromUtf16(info.field_str("wpsSid"));

        WOLOG_INFO << "[ExportAs] sessionId: " << sessionId;
        gs_security_doc->OnBeforeDocSave(GetCurrentDoc(), userContext->userID, sessionId.toUtf8(), userContext->userRequestInfo);
    }
    auto clearUserInfo = qScopeGuard([&] {
            gs_security_doc->OnAfterDocSave(GetCurrentDoc());
    });
	WebInt ret = WO_FAIL;
	switch (type)
	{
		case WO_EXPORT_PDF:
		{
			wo::util::CallTimeStat callTime("ExportAs", "pdf", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
			ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportPdf);
			break;
		}
		case WO_EXPORT_OFD:
		{
			ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportOfd);
			break;
		}
		case WO_EXPORT_IMG:
		{
			wo::util::CallTimeStat callTime("ExportAs", "img", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
			ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportImg);
			break;
		}
		case WO_EXPORT_XLSX:
		{
			wo::util::CallTimeStat callTime("ExportAs", "xlsx", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
			bool bDbExport = false;
			if (commands && commands->size != 0 && commands->data != nullptr)
			{
				BinReader rd(commands->data, commands->size);
				VarObjRoot rootObj = rd.buildRoot();
				VarObj root = rootObj.cast();
				if (root.has("viewId"))
					bDbExport = true;
			}

			if (wb->GetBMP()->bDbSheet || bDbExport)
				ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportXlsx);
			else // ksheet or et
				ret = ExportToLocalFile(userContext, commands, ffXLSX);
			break;
		}
		case WO_EXPORT_DBT:
		{
			wo::util::CallTimeStat callTime("ExportAs", "dbt", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
			ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportDb);
			break;
		}
		case WO_EXPORT_CSV:
		{
			wo::util::CallTimeStat callTime("ExportAs", "csv", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
			ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportSheetToCSV);
			break;
		}
		case WO_EXPORT_TXT:
		{
			wo::util::CallTimeStat callTime("ExportAs", "txt", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
			ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportTxt);
			break;
		}
		case WO_EXPORT_KSHEET:
		{
			wo::util::CallTimeStat callTime("ExportAs", "ksheet", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
			if (wb->GetBMP()->bDbSheet)
				ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportDbToKSheet);
			else if (wb->GetBMP()->bKsheet)
				ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportKSheetToKSheet);
			else // xlsx
				ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportXlsxToKSheet);
			break;
		}
        case WO_EXPORT_ET:
        {
            wo::util::CallTimeStat callTime("ExportAs", "et", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
            return ExportToLocalFile(userContext, commands, ffET);
        }
        case WO_EXPORT_ETT:
        {
            wo::util::CallTimeStat callTime("ExportAs", "ett", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
            return ExportToLocalFile(userContext, commands, ffETT);
        }
        case WO_EXPORT_XLS:
        {
            wo::util::CallTimeStat callTime("ExportAs", "xls", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
            return ExportToLocalFile(userContext, commands, ffXLS);
        }
        case WO_EXPORT_XLT:
        {
            wo::util::CallTimeStat callTime("ExportAs", "xlt", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
            return ExportToLocalFile(userContext, commands, ffXLT);
        }
        case WO_EXPORT_XLSM:
        {
            wo::util::CallTimeStat callTime("ExportAs", "xlsm", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
            return ExportToLocalFile(userContext, commands, ffXLSM);
        }
        case WO_EXPORT_XLTX:
        {
            wo::util::CallTimeStat callTime("ExportAs", "xltx", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
            return ExportToLocalFile(userContext, commands, ffXLTX);
        }
        case WO_EXPORT_XLTM:
        {
            wo::util::CallTimeStat callTime("ExportAs", "xltm", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
            return ExportToLocalFile(userContext, commands, ffXLTM);
        }
		case WO_EXPORT_DBT_ATTACHMENT:
		{
			wo::util::CallTimeStat callTime("ExportAs", "dbt_attachment", [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
			ret = inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportDbAttachmnet);
			break;
		}
		default:
		{
			WOLOG_INFO << "[ExportAs] invalid type: " << type;
		}
	}

	if (ret != WO_OK)
		WEBLOG_DUMP_BT();
	return ret;
}

WEB_EXPORT WebInt ExportAsSvg(const UserContext* userContext, WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook()) 
	{
		if (gs_callback && gs_callback->log) {
			gs_callback->log(WO_LOG_WARN, "file not opened");
		}
		return WO_FAIL;
	}

	wo::util::CallTimeStat callTime("ExportAsSvg", userContext->userID, [wbs](unsigned int ms) { 
		ExecTimeCollect::gs_exportSum += ms;
		CollectCallTimeApiRand(API_COLLECT_STR_NAME(ExportAsSvg), ms);
	});

	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecExportSvg);
}

WEB_EXPORT WebInt OnExit()
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("OnExit");
	if (!app)
	{
		WOLOG_ERROR << "nullptr app on exit. Unable to collect info.";
		return WO_OK;
	}
	
	if (gs_procType == WebProcTypeMaster)
	{
		// OnExit 无法使用 gs_callback->collectInfo, 必须使用 signal 并和服务端约定解析规则
		// 除埋点外, 也可以添加其他参数. 需要和服务端约定好. 参考 https://kdocs.cn/l/cktkDfTa5VXv
		binary_wo::BinWriter binWriter;
		binWriter.beginArray("collectEvents");

		wo::util::CollectEtXfs(app->GetWorkbooks(), binWriter);
		wo::util::CollectEtFormula(app->GetWorkbooks(), binWriter);
		wo::util::CollectDbPrepareUser(app->GetWorkbooks(), binWriter);
		wo::util::CollectLinkFormInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectCondFmtCacheInfo(app->GetWorkbooks(), binWriter);
		// wo::util::CollectUsedRangeAccuracy(app->GetWorkbooks(), binWriter);
		wo::util::CollectUndoRedoFailInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectShapesInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectCrooBookInfo(app->GetWorkbooks(), binWriter);// 收集跨进程book公式
        wo::util::CollectQRColCount(app->GetWorkbooks(), binWriter);
		wo::util::CollectStaleInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectVersionInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectMaxUserAndConn(app->GetWorkbooks(), binWriter);
		wo::util::CollectOneWayLinkHiddenSheetInfo(app->GetWorkbooks(), binWriter); // 收集跨book关联字段隐藏底表信息
		wo::util::CollectXLookupCache(app->GetWorkbooks(), binWriter);
		wo::util::CollectDbFunnelInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectCommentCount(app->GetWorkbooks(), binWriter);
		wo::util::CollectDbAttachmentInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectDbFmlaFieldWillBeTransformed(app->GetWorkbooks(), binWriter);
		wo::util::CollectEtStyles(app->GetWorkbooks(), binWriter);
		wo::util::CollectDashboardThemeInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectDashboardInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectImagepoolInfo(app->GetWorkbooks(), binWriter);
		wo::util::CollectProcPSS(app->GetWorkbooks(), binWriter);
		wo::util::CollectDashboardViewInfo(app->GetWorkbooks(), binWriter);

		if (gs_hasInitBaseContext)  // forkOpen模式下才进行收集
			wo::util::CollectEventDispatcherInfo(app, binWriter);

		if (app->GetWorkbooks() && app->GetWorkbooks()->GetWorkbook())
		{
			wo::KEtWorkbook* pEtWorkbook = app->GetWorkbooks()->GetWorkbook();
			
			int64_t forkQueryCnt = pEtWorkbook->coreMetric().getAllForkQueryCount();
			if (ExecTimeCollect::gs_queryCount || forkQueryCnt)
				wo::util::WriteSvrFileinclude(binWriter, __X("behaviour_all_query_count"), pEtWorkbook, ExecTimeCollect::gs_queryCount, forkQueryCnt);
			int64_t allCnt = ExecTimeCollect::gs_queryCount + forkQueryCnt + ExecTimeCollect::gs_execCount;
			if (ExecTimeCollect::gs_execCount || allCnt)
				wo::util::WriteSvrFileinclude(binWriter, __X("behaviour_all_exec_count"), pEtWorkbook, ExecTimeCollect::gs_execCount, allCnt);
			
			{
				wo::WoExitCollector exitCollector(pEtWorkbook, binWriter, __X("svr_coreexit"));
				exitCollector.addUInt32("query_cnt", ExecTimeCollect::gs_queryCount);
				exitCollector.addUInt32("exec_cnt", ExecTimeCollect::gs_execCount);
				exitCollector.addUInt32("fork_query_cnt", forkQueryCnt);
				exitCollector.addUInt32("query_exec_cnt", allCnt);
				exitCollector.addUInt32("query_init_cnt", ExecTimeCollect::gs_queryInitCount);
				exitCollector.addUInt32("query_reinit_cnt", pEtWorkbook->coreMetric().get64Metrics(wo::K64MetricItem::kAllReInitCnt));
				exitCollector.addUInt32("transform_cnt", pEtWorkbook->coreMetric().get64Metrics(wo::K64MetricItem::kAllTransformCnt));
				exitCollector.addUInt32("task_transform_cnt", pEtWorkbook->coreMetric().get64Metrics(wo::K64MetricItem::kAllTaskTransformCnt));
				exitCollector.addUInt32("unredo_transform_cnt", pEtWorkbook->coreMetric().get64Metrics(wo::K64MetricItem::kAllUndoRedoTransformCnt));
			}
			
			{
				wo::FileInfoCollector collector(pEtWorkbook, nullptr);
				wo::WoCollectInfo cotInfo(binWriter, true);
				collector.CollectMemInfoOnExit(binWriter, &cotInfo);
				binWriter.beginStruct();
				collector.CollectDbSheetRecordInfo();
				binWriter.endStruct();
			}
			ks_stdptr<Workbooks> spWorkbooks;
			app->GetWorkbooks()->GetCoreApp()->get_Workbooks(&spWorkbooks);
			long workbookCount = 0;
			if (SUCCEEDED(spWorkbooks->get_Count(&workbookCount)) && workbookCount > 1)
				wo::util::WriteSvrFileinclude(binWriter, __X("behaviour_unexpected_workbook_count"), pEtWorkbook);
		}

		binWriter.endArray();
		if (gs_callback->signal)
		{
			binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
			WebSlice slice = {shbt.get(), binWriter.writeLength()};
			gs_callback->signal(nullptr, getMsgTypeName(wo::msgType_OnExit), &slice);
			WOLOG_INFO << "OnExit signal sent";
		}
	}


	return WO_OK;
}

WEB_EXPORT WebInt GetImages(const UserContext* userContext, const WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("GetImages");
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecGetImages);
}

WEB_EXPORT WebInt ExecDelayCopy(const UserContext* userContext, WebSlice *commands, const WebMimeData *pMimeData)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook()) {
		if (gs_callback && gs_callback->log) {
			gs_callback->log(WO_LOG_WARN, "file not opened");
		}

		return WO_FAIL;
	}
	IBook* pBook = wbs->GetWorkbook()->GetCoreWorkbook()->GetBook();
	wo::util::CallTimeStat callTime("ExecDelayCopy", nullptr, [&pBook, &wbs](unsigned int ms){
		IEtEventTracking* pEventTracking = pBook->LeakWorkspace()->GetEventTracking();
		if (pEventTracking)
		{
			IEtCollectInfo* pEtCollectInfo = pEventTracking->GetCollectInfo();
			KComVariant varTime(ms);
			pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::DELAY_COPY_TIME, varTime);
			pEventTracking->GetManager()->SetFileId(wbs->GetWorkbook()->getFileId());
			pEventTracking->SendInfoAfterCopy();
		}
		});
	wo::DetailExtArgs dummy;
	dummy.corePermissionID = userContext->corePermissionID;
	const std::string &sessionID = gs_security_doc->GetCurExecCmdSessionID();
	wo::ExecDetail ed(wbs, userContext->connID, userContext->userID, sessionID.c_str(), dummy, commands, nullptr, false);
	KS_TRY {
		return (ed.wo::ExecDetail::ExecDelayCopy)(pMimeData);
	}
	KS_CATCH(const ks_exception& e) {
		HRESULT hr = e.get_result();
		ed.FillError(hr);
		WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_ERROR, "ks_exception, hr: %d, error: %s", hr, GetErrString(hr));
		return WO_FAIL;
	}
	return WO_OK;
}

WEB_EXPORT WebInt BatchDelayCopy(const UserContext* userContext, WebSlice *commands, WebInt count, const WebMimeData **ppMimeData)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook()) {
		if (gs_callback && gs_callback->log) {
			gs_callback->log(WO_LOG_WARN, "file not opened");
		}
		return WO_FAIL;
	}

	API_TIME_STATE(BatchDelayCopy, userContext->userID);
	
	wo::DetailExtArgs dummy;
	dummy.corePermissionID = userContext->corePermissionID;
	const std::string &sessionID = gs_security_doc->GetCurExecCmdSessionID();
	wo::ExecDetail ed(wbs, userContext->connID, userContext->userID, sessionID.c_str(), dummy, commands, nullptr, true);

	KS_TRY {
		WebInt i = 0;
		WebInt r = WO_OK;
		for (i = 0; i < count; ++i) {
			r = (ed.wo::ExecDetail::ExecDelayCopy)(*ppMimeData + i);
			if (r != WO_OK) {
				QString format = ppMimeData[i]->mimeType != NULL && *(ppMimeData[i]->mimeType) != '\0' ? QString::fromUtf8(ppMimeData[i]->mimeType): QString("text/html");
				WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_INFO, QString("[BatchDelayCopy]: failed, format: %1").arg(format).toUtf8());
				return r;
			}
		}
	}
	KS_CATCH(const ks_exception& e) {
		HRESULT hr = e.get_result();
		ed.FillError(hr);
		WO_LOG_X(wbs->GetWorkbook()->getLogger(), WO_LOG_ERROR, "ks_exception, hr: %d, error: %s", hr, GetErrString(hr));
		return WO_FAIL;
	}
	return WO_OK;
}

WEB_EXPORT WebInt DownloadObjectDone(const WebDownloadArg* pDownloadArg, int code, const char *path)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook()) {
		if (gs_callback && gs_callback->log) {
			gs_callback->log(WO_LOG_WARN, "file not opened");
		}

		return WO_FAIL;
	}

	wo::util::CallTimeStat callTime("DownloadObjectDone");
	wo::KEtWorkbook *wb = wbs->GetWorkbook();
	wb->DownloadObjectDone(pDownloadArg, code, path);
	return WO_OK;
}

/*
* @brife 用于全局的日志输出（其他动态库使用），@see Coding/include/webbase/logger.h
*/
WEB_EXPORT void WoLogMessage(int level, const char *msg)
{
	if (gs_callback && gs_callback->log)
	{
		gs_callback->log(level, msg);
	}
}

WEB_EXPORT WebInt ExecSubscribeCrossBooks()
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (WO_OK != CheckWorkbookOpened("ExecSubscribeCrossBooks"))
		return WO_FAIL;

	wo::util::CallTimeStat callTime("ExecSubscribeCrossBooks");
	return inlDecorateProc(nullptr, nullptr, &wo::ExecDetail::ExecSubscribeCrossBooks);
}

WEB_EXPORT WebInt SetCrossBookAutoUpdateThreshold(int nCount)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::DetailExtArgs args;
	args.autoUpdateThreshold = nCount;
	return inlDecorateProc(nullptr, nullptr, &wo::ExecDetail::SetCrossBookAutoUpdateThreshold, args);
}

WEB_EXPORT WebInt RefreshInitVersion(const UserContext* userContext, const WebSlice* info)
{
	BASE_PROCESS_CHECK_RET_FAIL
	return inlDecorateProc(userContext, info, &wo::ExecDetail::RefreshInitVersion);
}

WEB_EXPORT WebInt OnNotifyFile(WebNotifyType type, const UserContext* userContext, const char* fileId, WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (WO_OK != CheckWorkbookOpened("OnNotifyFile"))
		return WO_FAIL;

	API_TIME_STATE(OnNotifyFile, userContext->userID);

	wo::DetailExtArgs args;
	args.fileId = fileId;
	args.notifyType = type;
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::OnNotify, args);
}

WEB_EXPORT WebInt OnSubscriptionCenter(WebNotifyType type, const char *task, const UserContext* userContext, const char* fileId, WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook())
	{
		WOLOG_INFO << "[OnSubscriptionCenter] file not opened";
		return WO_FAIL;
	}

	WOLOG_INFO << "[OnSubscriptionCenter] "
		<< "type:" << type << ",task:" << task << ",connID"  << userContext->connID << ",userID:" << userContext->userID << ",fileId:" << fileId;

	API_TIME_STATE(OnSubscriptionCenter, userContext->userID);
	
	wo::DetailExtArgs args;
	args.fileId = fileId;
	args.notifyType = type;
	args.task = task;
	return inlDecorateProc(userContext, commands, &wo::ExecDetail::OnNotify, args);
}

WEB_EXPORT WebInt NotifyUploadSaveResult()
{
	BASE_PROCESS_CHECK_RET_FAIL
	return WO_OK;
}

/*
* @ 中断 目前可以中断计算, 设置一个中断标记位，
*/
WEB_EXPORT WebInt SetBreak(bool b)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (gs_isBreak.get() == b)
	{
	#ifdef _DEBUG
		WOLOG_INFO << "setBreak: " << b << "(same as old), isCanBreak: " << g_isCanBreak;
	#endif
		return WO_OK;
	}
	
	WOLOG_INFO << "setBreak: " << b << " isCanBreak: " << g_isCanBreak;
	gs_isBreak.set(b);
	gs_breakTimeStat.onSetBreak();
	return WO_OK;
}

WEB_EXPORT void ResumeCalculate()
{
	BASE_PROCESS_CHECK_RET_VOID
	gs_isBreak.set(false);
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (wb)
	{
		wo::util::CallTimeStat callTime("ResumeCalculate");
		DWORD calcDetail = RCD_None;
		wb->resumeCalculate(calcDetail, nullptr);
	}
}

WEB_EXPORT WebInt ExportCensorData(const char *filename)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("ExportCensorData");
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	if(!pKEtWorkbook)
	{
		if (gs_callback && gs_callback->log)
		{
			gs_callback->log(WO_LOG_ERROR, "file not opened");
		}
		return WO_FAIL;
	}

	QFile file;
	file.setFileName(QString::fromUtf8(filename));
	if(file.open(QIODevice::WriteOnly | QIODevice::Truncate))
	{
        PCWSTR censorShareId = _appcore_GainDbSheetContext()->GetCensorShareId();
        wo::ExportCensorData dataExporter(pKEtWorkbook, &file, censorShareId);
        HRESULT hr = dataExporter.DoExportText();
        if (FAILED(hr))
        {
            ASSERT(FALSE);
            WOLOG_ERROR << "[ExportCensorData] export censor data failed";
            return WO_FAIL;
        }
	}
	else
	{
		ASSERT(FALSE);
		WO_LOG_X(pKEtWorkbook->getLogger(), WO_LOG_ERROR, QString("Open export file: %1 failed.").arg(filename).toUtf8());
		return WO_FAIL;
	}

	return WO_OK;
}

WEB_EXPORT WebInt ExportSheetCensorData(const WebSlice *param)
{
	BASE_PROCESS_CHECK_RET_FAIL
    wo::KEtWorkbook* pEtWorkbook = app->GetWorkbooks()->GetWorkbook();
    if(!pEtWorkbook)
    {
        if (gs_callback && gs_callback->log)
        {
            gs_callback->log(WO_LOG_ERROR, "file not opened");
        }
        return WO_FAIL;
    }

    if (!param || param->size == 0 || !param->data)
    {
        WOLOG_ERROR << "[ExportSheetCensorData] invalid param";
        return WO_FAIL;
    }

    BinReader reader(param->data, param->size);
    VarObjRoot root = reader.buildRoot();
    VarObj obj = root.cast();
    if (!obj.has("sheetId") || !obj.has("fileName"))
    {
        WOLOG_ERROR << "[ExportSheetCensorData] param not exist sheetId or fileName";
        return WO_FAIL;
    }

    wo::util::SlowCallTimeStat callTime("ExportSheetCensorData", 100);
    UINT sheetId = obj.field_uint32("sheetId");
    QString fileName = krt::fromUtf16(obj.field_str("fileName"));
    QFile file(fileName);
    if(file.open(QIODevice::WriteOnly | QIODevice::Truncate))
    {
        wo::ExportCensorData dataExporter(pEtWorkbook, &file);
        HRESULT hr = dataExporter.DoExportText(sheetId);
        if (FAILED(hr))
        {
            ASSERT(FALSE);
            WOLOG_ERROR << "[ExportSheetCensorData] export sheet censor data failed";
            return WO_FAIL;
        }
    }
    else
    {
        ASSERT(FALSE);
        WO_LOG_X(pEtWorkbook->getLogger(), WO_LOG_ERROR, QString("Open output file: %1 failed.").arg(fileName).toUtf8());
        return WO_FAIL;
    }
    return WO_OK;
}

WEB_EXPORT WebInt ExportAllShapes(const WebSlice *param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("ExportAllShapes");
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	if(!pKEtWorkbook)
	{
		if (gs_callback && gs_callback->log)
		{
			gs_callback->log(WO_LOG_ERROR, "file not opened");
		}
		return WO_FAIL;
	}

    PCWSTR censorShareId = _appcore_GainDbSheetContext()->GetCensorShareId();
	if (censorShareId != nullptr && xstrcmp(censorShareId, __X("")) != 0)
	{
		wo::ExportSharedLinkAllImages exporter(pKEtWorkbook->GetCoreWorkbook(), censorShareId);
		exporter.DoExportAllImages();
	}
	else
	{
		wo::ExportAllImages exporter(pKEtWorkbook->GetCoreWorkbook());
		bool isRandomSelect=false;//是否成功随机导出图片
		if (param && param->size != 0 && param->data)//如果传入有效参数
		{
			BinReader reader(param->data,param->size);
			VarObjRoot root = reader.buildRoot();
			VarObj obj = root.cast();
			if (obj.has("sampling_threshold"))//并且参数中有sampling_threshold
			{
				uint32 sampling_threshold = obj.field_uint32("sampling_threshold");
                if (obj.has("image_max_size"))
                {
                    uint32 image_max_size = obj.field_uint32("image_max_size");
                    exporter.DoExportRandomImages(sampling_threshold, image_max_size);
                }
                else
                {
                    exporter.DoExportRandomImages(sampling_threshold);
                }
				isRandomSelect=true;
			}
		}
		if(!isRandomSelect)
		{
			exporter.DoExportAllImages();
		}
	}
	return WO_OK;
}

WEB_EXPORT WebInt GetCellLastRevision(int sheetIdx, int row, int col)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (!gs_callback->signal) return WO_FAIL;

	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();

	if(!pKEtWorkbook)
	{
		if (gs_callback && gs_callback->log)
		{
			gs_callback->log(WO_LOG_ERROR, "file not opened");
		}
		return WO_FAIL;
	}

	if (row < 0 || col < 0) return WO_FAIL;

	API_TIME_STATE(GetCellLastRevision, nullptr);
	ks_stdptr<IKWorksheet> spWorksheet = pKEtWorkbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (spWorksheet == NULL) return WO_FAIL;
	ks_stdptr<ISheet> spSheet = spWorksheet->GetSheet();
	if (spSheet == NULL) return WO_FAIL;

	wo::ISheetStake *stake = spSheet->GetWoStake();
	if (stake == NULL) return WO_FAIL;
	wo::IHistoryInfo *pHistoryInfo = stake->getCellHistory();
	if (pHistoryInfo == NULL) return WO_FAIL;

	CommitID id = pHistoryInfo->getCommitID(row, col);
	if (id == WO_INVALID_COMMIT_ID)
		return WO_FAIL;

	BinWriter bw;
	bw.addInt32Field(id, "commitVersion");
	BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice slice = {shbt.get(), bw.writeLength()};
	gs_callback->signal(nullptr, wo::getMsgTypeName(wo::msgType_lastRevision), &slice);
	return WO_OK;
}

WEB_EXPORT WebInt GetSheetNames()
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	if(!pKEtWorkbook)
	{
		if (gs_callback && gs_callback->log)
		{
			gs_callback->log(WO_LOG_ERROR, "file not opened");
		}
		return WO_FAIL;
	}
	if (!gs_callback->signal) return WO_FAIL;

	IDX shtCnt = pKEtWorkbook->GetCoreWorkbook()->GetWorksheets()->GetSheetCount();
	IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();

	BinWriter bw;
	bw.beginArray("sheetNames");
	for(IDX shtIdx = 0; shtIdx < shtCnt; shtIdx++)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(shtIdx, &spSheet);
		if (!spSheet->IsGridSheet())
			continue;

		PCWSTR str = NULL;
		spSheet->GetName(&str);
		SHEETSTATE state = ssVisible;
		spSheet->GetVisible(&state);
		if(state == ssVisible)
			bw.addString(str);
	}
	bw.endArray();

	BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice slice = {shbt.get(), bw.writeLength()};
	gs_callback->signal(nullptr, wo::getMsgTypeName(wo::msgType_getSheetNames), &slice);
	return WO_OK;
}

WEB_EXPORT WebInt FetchUserInfoDone(const WebSlice* userInfoList)
{
	BASE_PROCESS_CHECK_RET_FAIL
	std::vector<ks_wstring> failedIds;
	std::vector<wo::DbSheet::UserInfo> userInfoVec;
	if (userInfoList && userInfoList->size != 0 && userInfoList->data != nullptr)
	{
		BinReader rd(userInfoList->data, userInfoList->size);
		VarObjRoot infoObj = rd.buildRoot();
		VarObj info = infoObj.cast();
		VarObj infoList = info.get_s("user_info_list");
		for (int i = 0; i < infoList.arrayLength_s(); ++i)
		{
			VarObj item = infoList.at_s(i);
			WebStr id = item.field_str("id");
			userInfoVec.emplace_back(wo::DbSheet::UserInfo{});
			auto& userInfo = userInfoVec.back();
			userInfo.id = id;
			userInfo.nickname = item.field_str("name");
			userInfo.avatar = item.field_str("avatar_url");
            if (item.has("companyId"))
                userInfo.companyId = std::make_unique<ks_wstring>(item.field_str("companyId"));
		}
		VarObj failedIdList = info.get_s("failed_list");
		for (int i = 0; i < failedIdList.arrayLength_s(); ++i)
		{
			VarObj item = failedIdList.at_s(i);
			WebStr id = item.value_str();
			failedIds.emplace_back(id);
		}
	}
	ks_stdptr<IDbUsersManager> spUsersMgr;
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);

	std::vector<KDbCstrUserInfo> cstrUserInfoVec;
	HRESULT hr = wo::DbSheet::GetCstrUserInfoVec(userInfoVec, cstrUserInfoVec);
	if (FAILED(hr))
		return WO_FAIL;
	std::vector<PCWSTR> cstrFailedIds;
	hr = wo::DbSheet::GetCstrFailedIds(failedIds, cstrFailedIds);
	if (FAILED(hr))
		return WO_FAIL;

	hr = spUsersMgr->UpdateUserInfoFromServer(cstrUserInfoVec.data(), cstrUserInfoVec.size()
		, cstrFailedIds.data(), cstrFailedIds.size());
	if (FAILED(hr))
		return WO_FAIL;
	return WO_OK;
}

WEB_EXPORT WebInt UploadAttachmentObjectsDone(const WebSlice* doneList)
{
	BASE_PROCESS_CHECK_RET_FAIL
	return WO_OK;
}

WEB_EXPORT WebInt PrepareExportAttachment(const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
    wo::KEtWorkbooks* wbs = app->GetWorkbooks();
    if (nullptr == wbs->GetWorkbook())
    {
        WOLOG_INFO << "ExportAttachmentDone error, workbook not opened";
        return WO_FAIL;
    }

	API_TIME_STATE(PrepareExportAttachment, userContext->userID);
	
    wo::DetailExtArgs args;
    args.isServerLocalExec   = false;
    args.bNeedSubscriptionOp = true;
    BinWriter writer;
    writer.addInt32Field(-1, "seq"); // -1 表示的是服务器本地调用
    writer.beginArray("commands");
    {
        writer.beginStruct();
        {
            writer.addStringField(__X("book.blockUploadAllPicturesToAttachment"), "name");
            writer.beginStruct("param");
            {
                writer.addBoolField(true, "emptyCommand");
            }
            writer.endStruct();
        }
		writer.endStruct();
	}
	writer.endArray();

	BinWriter::StreamHolder stream = writer.buildStream();
	WebSlice slice                 = { stream.get(), writer.writeLength() };
	inlDecorateProc(userContext, &slice, &wo::ExecDetail::ExecCmd, args, nullptr);

	return WO_OK;
}

WEB_EXPORT WebInt ExportAttachmentDone(const WebSlice* uploadObjectDone)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (!wb)
    {
		WOLOG_ERROR << "ExportAttachmentDone error, workbook not opened";
        return WO_FAIL;
    }

	auto UpdateConvertingStatus = [] (const char* connId, const char* userId) -> void {
		BinWriter writer;
		writer.addInt32Field(-1, "seq"); // -1 表示的是服务器本地调用
		writer.beginArray("commands");
		{
			writer.beginStruct();
			{
				writer.addStringField(__X("book.blockUploadAllPicturesToAttachment"), "name");
				writer.beginStruct("param");
				{
					writer.addBoolField(true, "applyUploadResult");
				}
				writer.endStruct();
			}
			writer.endStruct();
		}
		writer.endArray();

		BinWriter::StreamHolder stream = writer.buildStream();
		WebSlice slice                 = { stream.get(), writer.writeLength() };
		wo::DetailExtArgs args;
		args.isServerLocalExec   = true;
		args.bNeedSubscriptionOp = true;
		args.bWriteCooperationRecord = false;
		args.bNeedCleanChildProc = false;
		const UserContext userContext{connId, userId, nullptr, nullptr};
		inlDecorateProc(&userContext, &slice, &wo::ExecDetail::ExecCmd, args, nullptr);
	};
	API_TIME_STATE(ExportAttachmentDone, nullptr);
	wo::PictureUploadHelper* helper = wb->GetPictureUploadHelper();
    helper->setUpdateShapes(UpdateConvertingStatus);
    helper->parentProcessApplyUploadResult(uploadObjectDone);
    helper->setUploadDone();
    return WO_OK;
}

WEB_EXPORT WebInt CanExportAttachment()
{
	BASE_PROCESS_CHECK_RET_FAIL
    wo::KEtWorkbooks* wbs = app->GetWorkbooks();
    if (nullptr == wbs->GetWorkbook())
    {
        WOLOG_INFO << "CanExportAttachment error, workbook not opened";
        return WO_FAIL;
    }

    wo::PictureUploadHelper* helper = wbs->GetWorkbook()->GetPictureUploadHelper();
    return helper->hasUploadablePicture() ? WO_OK : WO_FAIL;
}

WEB_EXPORT WebInt ExportAttachment()
{
	BASE_PROCESS_CHECK_RET_FAIL
    wo::KEtWorkbooks* wbs = app->GetWorkbooks();
    if (nullptr == wbs->GetWorkbook())
    {
        WOLOG_INFO << "ExportAttachment error, workbook not opened";
        return WO_FAIL;
    }

    wo::util::CallTimeStat callTime("ExportAttachment");
    wo::PictureUploadHelper* helper = wbs->GetWorkbook()->GetPictureUploadHelper();
    helper->childProcessExportUploadResult();
    return WO_OK;
}

WEB_EXPORT WebInt MarkAsSharedLink(const UserContext* userContext, const char *sharedId)
{
	BASE_PROCESS_CHECK_RET_FAIL
	WOLOG_INFO << "[MarkAsSharedLink]";
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	if (nullptr == wbs->GetWorkbook())
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}

	API_TIME_STATE(MarkAsSharedLink, userContext->userID);
	wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
	IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	if(!spSharedLinkMgr)
		return WO_SHARED_LINK_INVALID;

	QString strSharedId =  QString(sharedId);
	ISharedLink* pLink = spSharedLinkMgr->GetItem(krt::utf16(strSharedId));
	if (!pLink)
		return WO_DBSHEET_VIEW_NOT_FOUND;

	if (FAILED(pLink->CheckPermission()))
		return WO_SHARED_LINK_INVALID;

	HRESULT hr = E_FAIL;
	IKUserConns *pUserConns = app->getCoreApp()->getUserConns();
	UserConnArgs exArgs(userContext);
	IKUserConn *pUserConn = pUserConns->setUserConn(userContext->connID, userContext->userID, userConnEndWeb, 1.0, &exArgs);
	ASSERT(pUserConn != nullptr);
	hr = spSharedLinkMgr->SetConnSharedLink(pUserConn, krt::utf16(strSharedId));
	if (FAILED(hr))
		return WO_SHARED_LINK_INVALID;

	ks_stdptr<IDBSharedLinkView> spSharedLinkView = pLink;
	if (spSharedLinkView)
	{
		binary_wo::BinWriter binWriter;

		ET_DBSheet_ViewType type = spSharedLinkView->GetView()->GetType();
		PCWSTR typeStr = nullptr;
		VS(_appcore_GainEncodeDecoder()->EncodeViewType(type, &typeStr));
		if (typeStr)
			binWriter.addStringField(krt::utf16(QStringLiteral("shareview_open_%1").arg(typeStr)), "event");
		binWriter.addStringField(krt::utf16(krt::fromUtf8(sharedId)), "fileid");
		binWriter.addStringField(pKEtWorkbook->getFileId(), "_fileid");
		if (userContext->userID)
		{
			// 埋点中增加 "_account_id" 信息. 服务端接受的字段名是 _account_id, 但以前的代码都是 account_id
			// 目前提交两个字段, 确保信息有被收集
			binWriter.addKey("account_id");
			binWriter.addString(krt::utf16(QString(userContext->userID)));
			binWriter.addKey("_account_id");
			binWriter.addString(krt::utf16(QString(userContext->userID)));
		}
		BMP_PTR bmpPtr = pBook->GetBMP();
		binWriter.addKey("project");
		if (bmpPtr->bDbSheet)
			binWriter.addString(__X("dbt"));
		else if (bmpPtr->bKsheet)
			binWriter.addString(__X("ksheet"));
		else
			binWriter.addString(__X("et"));

		binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};

		WOLOG_INFO << "index_experience: " << QStringLiteral("shareview_open_%1").arg(typeStr);
		gs_callback->collectInfo("index_experience", &slice);
	}

	WOLOG_INFO << "[MarkAsSharedLink] succeeded! connID:" << userContext->connID << " userID:" << userContext->userID << " sharedId:"<< sharedId;
	return WO_OK;
}

/*!
 * @return WebInt 0 无权限 1 无组权限 2 有组权限
*/
WEB_EXPORT WebInt IsCanManageSharedLink(const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* wb = wbs->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return 0;
	}

	// et支持分享工作表，无需其他权限判断
	if (!wb->GetBMP()->bDbSheet && !wb->GetBMP()->bKsheet)
		return 1;

	ks_stdptr<_Application> ptrApp = wb->GetCoreApp();
	IKUserConns* userConns = ptrApp->getUserConns();
	if (userConns) {
		UserConnArgs args(userContext);
		userConns->setCurrentUserConn(userContext->connID, userContext->userID, userConnEndUnknown, 1.0, &args);
	}
	std::unique_ptr<wo::KEtRevisionContext> ctx(new wo::KEtRevisionContext(wb));
	IBook* pBook = wb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IUnknown> spUnknown;
    VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if(FAILED(spProtectionJudgement->CheckCanManage()))
		return 0;

	return spProtectionJudgement->HasGroupProtection() ? 2 : 1;
}

static void UpdateCrossbookState(NetFileRes res, const UserContext* userContext, const char* fileId, const char* url)
{
	WOLOG_INFO << "[UpdateCrossbookState] res: " << res << ", fileId: " << fileId;

	BinWriter writer;
	writer.addInt32Field(-1, "seq"); // -1 表示的是服务器本地调用
	writer.beginArray("commands");
	{
		writer.beginStruct();
		{
			writer.addStringField(__X("book.updateCrossBookState"), "name");
			writer.beginStruct("param");
			{
				writer.addStringField(krt::utf16(QString::fromUtf8(fileId)), "fileid");
				writer.addInt32Field(res, "fileCode");
			}
			writer.endStruct();
		}
		writer.endStruct();
	}
	writer.endArray();
	wo::util::CallTimeStat callTime("UpdateCrossbookState");
	BinWriter::StreamHolder stream = writer.buildStream();
    WebSlice slice = { stream.get(), writer.writeLength() };
	wo::DetailExtArgs args;
	args.isServerLocalExec = true;
	args.bNeedSubscriptionOp = true;
	inlDecorateProc(userContext, &slice, &wo::ExecDetail::ExecCmd, args, nullptr);

	wo::DetailExtArgs dummy;
	inlDecorateProcVariadic(userContext, nullptr, dummy, nullptr,&wo::ExecDetail::OnAsyncGetCrossbookState, res, url, fileId);
}

static void UpdateUrlbookState(NetFileRes res, const UserContext* userContext, const char* fileId, const char* url)
{
	if (!userContext->connID || !fileId || !url)
	{
		WOLOG_INFO << "[UpdateUrlbookState] invalid args";
		ASSERT(false);
		return;
	}

	wo::util::CallTimeStat callTime("UpdateUrlbookState");
	WOLOG_INFO << "[UpdateUrlbookState] res: " << res << ", fileId: " << fileId;

	// 根据 gs_isForCalcRef 决定是否允许计算中断.
	std::unique_ptr<DisableCalcBreak> upDisableCalcBreak;
	if (gs_isForCalcRef.get() == 1)
	{
		WOLOG_DEBUG << "[UpdateUrlbookState] Disable break due to gs_isForCalcRef is 1";
		upDisableCalcBreak.reset(new DisableCalcBreak);
	}
	// 埋点上报是否允许中断的状态
	{
		binary_wo::BinWriter binWriter;

		binWriter.addStringField(__X("behaviour_UpdateUrlbookState_break"), "name");
		binWriter.addInt32Field(gs_isForCalcRef.get(), "count");
		
		binWriter.addStringField(krt::utf16(QString(fileId)), "fileid");
		if (userContext->userID)
		{
			binWriter.addKey("_account_id");
			binWriter.addString(krt::utf16(QString(userContext->userID)));
		}

		binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};

		WOLOG_INFO << "fileinclude: " << "behaviour_UpdateUrlbookState_break: " << gs_isForCalcRef.get();
		gs_callback->collectInfo("fileinclude", &slice);
	}
	

	BinWriter writer;
	writer.addInt32Field(-1, "seq"); // -1 表示的是服务器本地调用
	writer.beginArray("commands");
	{
		writer.beginStruct();
		{
			writer.addStringField(__X("book.updateSupbookData"), "name");
			writer.beginStruct("param");
			{
				writer.addStringField(krt::utf16(QString::fromUtf8(fileId)), "fileid");
				writer.addInt32Field(res, "fileCode");
				writer.addStringField(krt::utf16(QString::fromUtf8(url)), "url");
				writer.addBoolField(true, "isDownloadDone");

			}
			writer.endStruct();
		}
		writer.endStruct();
	}
	writer.endArray();

	BinWriter::StreamHolder stream = writer.buildStream();
    WebSlice slice = { stream.get(), writer.writeLength() };
	wo::DetailExtArgs args;
	args.isServerLocalExec = true;
	args.bNeedSubscriptionOp = true;
	inlDecorateProc(userContext, &slice, &wo::ExecDetail::ExecCmd, args, nullptr);
}

namespace
{
void processEafResult(VarObj rets, wo::KEtWorkbook*);
managed_token_ptr createTokenFromResult(VarObj res);
constexpr int ValidCode = 0;
int noPrivilegeErrorCode(VarObj res);
}

WEB_EXPORT WebInt OnCompleteEAFCalc(const WebSlice* ret)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* wb = wbs->GetWorkbook();
	if(!wb)
	{
		if (gs_callback && gs_callback->log)
		{
			gs_callback->log(WO_LOG_ERROR, "file not opened");
		}
		return WO_FAIL;
	}
	if (!gs_callback->signal) return WO_FAIL;

	_Workbook* coreWb = wb->GetCoreWorkbook();
	IBook* pBook = coreWb->GetBook();
	
	BinReader reader(ret->data, ret->size);
	VarObjRoot root = reader.buildRoot();
	VarObj param = root.cast();
	VarObj rets = param.get_s("udfCallRet");
	if (rets.type() != typeArray)
	{
		return WO_FAIL;
	}

	processEafResult(rets, wb);

	ks_stdptr<IWorkspace> spWS;
	pBook->GetWorkspace(&spWS);
	if (spWS != nullptr)
		spWS->CalculateNormal();

	return WO_OK;
}

namespace
{
struct EafTokenInfo
{
	INT32 inputTokens;
	INT32 outputTokens;
	INT32 totalTokens;
};
void processEafResult(VarObj rets, wo::KEtWorkbook* wb)
{
	int32 len = rets.arrayLength();
	if (len <= 0)
		return;

	std::vector<EafResultInfo> resultInfos;
	std::vector<alg::managed_token_assist> mtaVec;
	std::vector<EafTokenInfo> tokenInfos;
	resultInfos.reserve(len);
	// 之后考虑禁用 managed_token_assist 的拷贝构造, 提供移动构造. 如果只禁用拷贝构造, reserve的调用会有问题
	mtaVec.reserve(len);
	tokenInfos.reserve(len);

	struct PrivilegeErrorInfo
	{
		int privilegeCode = ValidCode;
		int firstPrivilegeErrorIdx = -1;
	} privilegeInfo;

	for (int32 i = 0; i < len; ++i)
	{
		VarObj item = rets.at(i);
		VarObj posItem = item.get_s("pos");
		VarObj token = item.get_s("token");

		EafResultInfo resInfo {-1, -1, nullptr, {}, nullptr, -1};

		resInfo.nodeId = posItem.field_int32("cell_node_id");
		resInfo.tokenIdx = posItem.field_int32("func_tok_pos");
		// managed_token_assist 未提供移动构造函数, 默认的定义不可信任

		VarObj itemRes = item.get_s("result");
		if (-1 == privilegeInfo.firstPrivilegeErrorIdx)
		{
			int code = noPrivilegeErrorCode(itemRes);
			if (ValidCode != code)
			{
				privilegeInfo.privilegeCode = code;
				privilegeInfo.firstPrivilegeErrorIdx = i;
			}
		}

		mtaVec.emplace_back(alg::managed_token_assist{});
		mtaVec.back().set(createTokenFromResult(itemRes));
		resInfo.token = static_cast<const_token_ptr>(mtaVec.back());
		resultInfos.push_back(resInfo);

		EafTokenInfo tokenInfo;
		tokenInfo.totalTokens = token.field_int32("total_tokens");
		tokenInfo.inputTokens = token.field_int32("input_tokens");
		tokenInfo.outputTokens = token.field_int32("output_tokens");
		tokenInfos.push_back(tokenInfo);
	}
	ASSERT(len == resultInfos.size());
	ASSERT(len == mtaVec.size());
	ASSERT(len == tokenInfos.size());

	IBook* pBook = wb->GetCoreWorkbook()->GetBook();
	pBook->LeakOperator()->SetEafResult(len, resultInfos.data());

	if (-1 != privilegeInfo.firstPrivilegeErrorIdx)
	{
		if (gs_callback->broadcast)
		{
			binary_wo::BinWriter bw;
			bw.addInt32Field(privilegeInfo.privilegeCode, "errorCode");
			bw.addStringField(resultInfos[privilegeInfo.firstPrivilegeErrorIdx].funcName, "funcName");
			const wo::MsgType msg = wo::msgType_OnEafResReceived;
			binary_wo::BinWriter::StreamHolder bt = bw.buildStream();
			WebSlice slice = {bt.get(), bw.writeLength()};
			gs_callback->broadcast(getMsgTypeName(msg), &slice, NULL);
		}
	}

	// 处理埋点
	using namespace wo;
	Database::FieldContext fieldContext(wb, nullptr);
	QString fileId  = QString::fromUtf16(wb->getFileId());
	for (int32 i = 0; i < len; ++i)
	{
		PCWSTR value = __X("cell");

		RANGE rg(resultInfos[i].cube, pBook->GetBMP());
		wo::util::VALIDATION_Wrap dv;
		int sheetIdx = rg.SheetTo(), row = rg.RowFrom(), col = rg.ColFrom();
		pBook->LeakOperator()->GetDataValidation(rg, sheetIdx, row, col, &dv, NULL, &rg, NULL);
		Database::IDbField *pField = nullptr;
		Database::FieldsManager *pDbMgr = Database::FieldsManager::Instance();
		if (pField = pDbMgr->IdentifyAll(&fieldContext, rg, dv))
		{
			const Database::FieldType type = pField->GetType();
			if (type == Database::dftAI)
				value = __X("column");
		}
		util::CollectAiEafInfo(fileId, resultInfos[i], tokenInfos[i].inputTokens, tokenInfos[i].outputTokens, tokenInfos[i].totalTokens
			, value, pBook);
	}
}

managed_token_ptr createTokenFromResult(VarObj res)
{
	switch (res.field_int32("type"))
	{
	case WEB_EAF_PARAM_T_INT:
		return etexec::managed_vint_token_assist().create(res.field_int32("value")).detach();
	case WEB_EAF_PARAM_T_DBL:
		return etexec::managed_vdbl_token_assist().create(res.field_double("value")).detach();
	case WEB_EAF_PARAM_T_STR:
		return etexec::managed_vstr_token_assist().create(res.field_str("value")).detach();
	case WEB_EAF_PARAM_T_BOOL:
		return etexec::managed_vbool_token_assist().create(res.field_bool("value")).detach();
	case WEB_EAF_PARAM_T_ERROR:
		switch(res.field_int32("value"))
		{
		case E_CALC:
			return etexec::managed_error_token_assist().create(etexec::EXV_E_CALC).detach();
		case E_BLOCKED:
			return etexec::managed_error_token_assist().create(etexec::EXV_E_BLOCKED).detach();
		case E_CONNECT:
			return etexec::managed_error_token_assist().create(etexec::EXV_E_CONNECT).detach();
		case E_GETTING_DATA:
			return etexec::managed_error_token_assist().create(etexec::EXV_E_GETTING_DATA).detach();
		case E_SUSPEND:
			return etexec::managed_error_token_assist().create(etexec::EXV_E_SUSPEND).detach();
		case E_PROHIBITED:
			return etexec::managed_error_token_assist().create(etexec::EXV_E_CONNECT).detach();
		default:
			return etexec::managed_error_token_assist().create(etexec::EXV_E_VALUE).detach();
		}
		break;
	default:
		// 其他场景都回落到#VALUE!
		return etexec::managed_error_token_assist().create(etexec::EXV_E_VALUE).detach();	
	}
}
int noPrivilegeErrorCode(VarObj res)
{
	if (WEB_EAF_PARAM_T_ERROR == res.field_int32("type"))
		if (E_BLOCKED == res.field_int32("value"))
			return res.field_int32("privilege");
	return ValidCode;
}
}

WEB_EXPORT void OnAsyncGetNetFileDone(NetFileRes res, const char* url, const UserContext* userContext, const char* fileId, DownloadFileTask task)
{
	BASE_PROCESS_CHECK_RET_VOID
	if (!userContext->connID || !fileId || !url)
	{
		WOLOG_INFO << "[OnAsyncGetNetFileDone] invalid args";
		ASSERT(false);
		return;
	}

	API_TIME_STATE(OnAsyncGetNetFileDone, userContext->userID);
	WOLOG_INFO << "[OnAsyncGetNetFileDone] res: " << res << ", fileId: " << fileId << ", task: " << task;
	wo::DetailExtArgs dummy;
	if (task == DownloadFileTask_CrossBook)
		UpdateCrossbookState(res, userContext, fileId, url);
	else if (task == DownloadFileTask_ImportRange)
		UpdateUrlbookState(res, userContext, fileId, url);
	else if (task == DownloadFileTask_MergeFile)
		inlDecorateProcVariadic(userContext, nullptr, dummy, nullptr, &wo::ExecDetail::OnAsyncGetMergeFileDone, res, url, fileId);
}

WEB_EXPORT WebInt CalculateReference(const UserContext* userContext, RecalculateType type)
{
	BASE_PROCESS_CHECK_RET_FAIL
	gs_isForCalcRef.set(1);
	WOLOG_DEBUG << "[UpdateUrlbookState] gs_isForCalcRef set to 1";

	wo::util::CallTimeStat callTime("CalculateReference");
	DisableCalcBreak disableCalcBreak;
	wo::DetailExtArgs dummy;
	return inlDecorateProcVariadic(userContext, nullptr, dummy, nullptr, &wo::ExecDetail::CrossRefRecalculate, type);
}


WEB_EXPORT WebInt CalculateMergeFile(const UserContext* userContext, RecalculateType type)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("CalculateMergeFile");
	wo::DetailExtArgs dummy;
	return inlDecorateProcVariadic(userContext, nullptr, dummy, nullptr, &wo::ExecDetail::MergeFileTaskRecalculate, type);
}

WEB_EXPORT void NotifyMergeFileUpdate(WebNetFileInfo* pFileInfos, int count, const UserContext* userContext, const char *updateSeq, bool bIncreament)
{
	BASE_PROCESS_CHECK_RET_VOID
	WOLOG_INFO << "[NotifyMergeFileUpdate]";
	if (!userContext->connID || !pFileInfos || !userContext->userID || !updateSeq)
	{
		WOLOG_INFO << "[NotifyMergeFileUpdate] invalid args";
		ASSERT(false);
		return;
	}

	API_TIME_STATE(NotifyMergeFileUpdate, userContext->userID);

	WOLOG_INFO << "[NotifyMergeFileUpdate] count: " << count;
	BinWriter writer;
	writer.addInt32Field(-1, "seq"); // -1 表示的是服务器本地调用
	writer.beginArray("commands");
	{
		writer.beginStruct();
		{
			writer.addStringField(__X("book.mergeFileAutoUpdate"), "name");
			writer.beginStruct("param");
			{
				writer.beginArray("fileInfos");
				for (int i = 0; i < count; i++)
				{
					WebNetFileInfo *pCur = pFileInfos + i;
					writer.beginStruct();
					writer.addInt32Field(pCur->res, "fileCode");
					writer.addStringField(krt::utf16(QString::fromUtf8(pCur->fileId)), "fileid");
					writer.endStruct();
				}
				writer.endArray();
				writer.addStringField(krt::utf16(QString::fromUtf8(updateSeq)), "updateSeq");
				writer.addBoolField(bIncreament, "Increament");
			}
			writer.endStruct();
		}
		writer.endStruct();
	}
	writer.endArray();

	BinWriter::StreamHolder stream = writer.buildStream();
    WebSlice slice = { stream.get(), writer.writeLength() };
	wo::DetailExtArgs args;
	args.isServerLocalExec = true;

	inlDecorateProc(userContext, &slice, &wo::ExecDetail::ExecCmd, args, nullptr);
}

WEB_EXPORT void NotifyInsertNetFile(const char* fileID, NetFileRes res, const UserContext* userContext, const char *updateSeq)
{
	BASE_PROCESS_CHECK_RET_VOID
	if (!userContext->connID || !fileID || !userContext->userID || !updateSeq)
	{
		WOLOG_INFO << "[NotifyInsertNetFile] invalid args";
		ASSERT(false);
		return;
	}

	API_TIME_STATE(NotifyInsertNetFile, userContext->userID);

	WOLOG_INFO << "[NotifyInsertNetFile] res: " << res << ", fileId: " << fileID;
	BinWriter writer;
	writer.addInt32Field(-1, "seq"); // -1 表示的是服务器本地调用
	writer.beginArray("commands");
	{
		writer.beginStruct();
		{
			writer.addStringField(__X("book.updateSupbookData"), "name");
			writer.beginStruct("param");
			{
				writer.addStringField(krt::utf16(QString::fromUtf8(fileID)), "fileid");
				writer.addInt32Field(res, "fileCode");
				writer.addStringField(krt::utf16(QString::fromUtf8(updateSeq)), "updateSeq");
			}
			writer.endStruct();
		}
		writer.endStruct();
	}
	writer.endArray();

	BinWriter::StreamHolder stream = writer.buildStream();
    WebSlice slice = { stream.get(), writer.writeLength() };
	wo::DetailExtArgs args;
	args.isServerLocalExec = true;
	args.bNeedSubscriptionOp = true;

	inlDecorateProc(userContext, &slice, &wo::ExecDetail::ExecCmd, args, nullptr);
}

WEB_EXPORT WebInt ExportChart(const UserContext* userContext)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (!userContext->connID || !userContext->userID) {
		return WO_FAIL;
	}

	wo::util::CallTimeStat callTime("ExportChart");
	wo::RemoteChartLinkManager* pInstance = wo::RemoteChartLinkManager::getInstance();
	if (pInstance->ExportChartData()) {
		return WO_OK;
	}

	return WO_FAIL;
}

WEB_EXPORT WebInt SetTriggerConf(const WebSlice *param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("SetTriggerConf");
	return inlDecorateProc(nullptr, param, &wo::ExecDetail::SetTriggerConf);
}

WEB_EXPORT WebInt NotifyMergeTaskSrcDataDirty(const char* fileID)
{
	BASE_PROCESS_CHECK_RET_FAIL
	API_TIME_STATE(NotifyMergeTaskSrcDataDirty, fileID);
	wo::DetailExtArgs dummy;
	return inlDecorateProcVariadic(nullptr, nullptr, dummy, nullptr, &wo::ExecDetail::OnMergeTaskSrcDataDirty, fileID);
}

WEB_EXPORT WebInt BindAttachmentIdsDone(const WebSlice *serData)
{
	BASE_PROCESS_CHECK_RET_FAIL
	return inlDecorateProc(nullptr, serData, &wo::ExecDetail::OnBindAttachmentIdsDone);
}

WebInt SetMultiThreadsCnt(WebInt calcThreadsCnt)
{
	if (calcThreadsCnt < 1)
		calcThreadsCnt = 1;
	if (app == nullptr)
		return WO_FAIL;

	ks_stdptr<etoldapi::MultiThreadedCalculation> multiThreadCalc;
	VS(app->GetApiApp()->get_MultiThreadedCalculation(&multiThreadCalc));
	multiThreadCalc->put_ThreadCount(calcThreadsCnt);
	multiThreadCalc->put_Enabled(calcThreadsCnt > 1 ? VARIANT_TRUE : VARIANT_FALSE);
	return WO_OK;
}

WEB_EXPORT WebInt GetRecommendMultiThreadCalcIndex(const char* connID)
{
	BASE_PROCESS_CHECK_RET_FAIL
	double index = 0;
	if (app)
	{
		IWorkspace* pWs = app->getCoreApp()->GetWorkSpace();
		index = pWs->GetRecommendMultiThreadCalcIndex();
	}

	BinWriter binWriter;
	binWriter.addKey("recommendMultiThreadCalcIndex");
	binWriter.addFloat64(index);
	BinWriter::StreamHolder shbt = binWriter.buildStream();
	WebSlice slice = {shbt.get(), binWriter.writeLength()};
	gs_callback->signal(connID, getMsgTypeName(wo::msgType_Query), &slice);
	return WO_OK;
}

WEB_EXPORT WebInt AuthQueryDbCoHistories(const UserContext* userContext, const WebSlice* param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::DetailExtArgs dummy;
	return inlDecorateProcVariadic(userContext, param, dummy, nullptr, &wo::ExecDetail::AuthQueryDbCoHistories, userContext->connID, userContext->userID);
}

WEB_EXPORT WebInt GetSheetsInfo()
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* wb = wbs->GetWorkbook();
	if(!wb)
	{
		if (gs_callback && gs_callback->log)
		{
			gs_callback->log(WO_LOG_ERROR, "file not opened");
		}
		return WO_FAIL;
	}
	if (!gs_callback->signal) return WO_FAIL;

	_Workbook* coreWb = wb->GetCoreWorkbook();
	ks_stdptr<IBook> ptrIBook = coreWb->GetBook();

	BinWriter bw;
	bw.beginArray("sheetsInfo");

	IDX shtCnt = coreWb->GetWorksheets()->GetSheetCount();
	for(IDX shtIdx = 0; shtIdx < shtCnt; shtIdx++)
	{
		ks_stdptr<ISheet> spSheet;
		ptrIBook->GetSheet(shtIdx, &spSheet);

		bool isVisible = false;
		SHEETSTATE state = ssVisible;
		spSheet->GetVisible(&state);
		if (state == ssVeryhidden)
			continue;
		else if (state == ssVisible)
			isVisible = true;

		PCWSTR eachSheetName = NULL;
		spSheet->GetName(&eachSheetName);
		RANGE rg(spSheet->GetBMP());

		ks_stdptr<IKWorksheet> spWorksheet = coreWb->GetWorksheets()->GetSheetItem(shtIdx);
		spWorksheet->GetUsedRange(&rg);
		ISheetProtection* pSheetProtection = spWorksheet->GetProtection();
		bool isProtected = pSheetProtection->IsProtected();
		UINT sheetId = spSheet->GetStId();

		bw.beginStruct();
			bw.addStringField(eachSheetName, "sheetName");
			bw.addInt32Field(shtIdx, "sheetIdx");
			bw.addUint32Field(sheetId, "sheetId");
			bw.addInt32Field(rg.RowFrom(), "rowFrom");
			bw.addInt32Field(rg.RowTo(), "rowTo");
			bw.addInt32Field(rg.ColFrom(), "colFrom");
			bw.addInt32Field(rg.ColTo(), "colTo");
			bw.addBoolField(isVisible, "isVisible");
			bw.addBoolField(isProtected, "isProtected");
			if (ptrIBook->GetBMP()->bKsheet)
			{
				if (spSheet->IsDbSheet())
					bw.addStringField(__X("db"), "sheetType");
				else if (spSheet->IsAppSheet())
					bw.addStringField(__X("airApp"), "sheetType");
                else if (spSheet->IsWorkbenchSheet())
                    bw.addStringField(__X("workbench"), "sheetType");
				else
					bw.addStringField(__X("et"), "sheetType");
			}
			else
			{
				bw.addStringField(__X("et"), "sheetType");
			}
		bw.endStruct();
	}	

	bw.endArray();

	BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice slice = {shbt.get(), bw.writeLength()};
	gs_callback->signal(nullptr, wo::getMsgTypeName(wo::msgType_getSheetsInfo), &slice);

	return WO_OK;

}

WEB_EXPORT WebInt SetFileVersionSize(WebInt version, WebFileSize size)
{
	BASE_PROCESS_CHECK_RET_FAIL
	WOLOG_INFO << "[SetFileVersionSize] version: " << version << ", fileSize: " << size;
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* wb = wbs->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return 0;
	}
	wb->SetFileVersionSize(version, size);
	return WO_OK;
}

class UserConnsLockGuard
{
public:
	UserConnsLockGuard(IKUserConns *pUserConns) : m_pUserConns(pUserConns) { m_pUserConns->lock(); }
	~UserConnsLockGuard() { m_pUserConns->unlock(); }
private:
	IKUserConns *m_pUserConns;
};

class SyncChildMetric
{
public:
	SyncChildMetric()
	{
		wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
		if (wb)
			wb->coreMetric().onBeginSyncChild();
	}
	~SyncChildMetric()
	{
		wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
		if (wb)
			wb->coreMetric().onEndSyncChild();
	}
};

WEB_EXPORT WebInt SyncChildProc(const WebSlice* input, WebSlice* output)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (gs_procType != WebProcTypeMaster)
	{
		WOLOG_ERROR << "[SyncChildProc] not master";
		return WO_FAIL;
	}

	output->data = nullptr;
	output->size = 0;

	if (input == nullptr || input->size == 0 || input->data == nullptr)
	{
		WOLOG_ERROR << "[SyncChildProc] empty input";
		return WO_FAIL;
	}
	SyncChildMetric metric;
	// 有时文件close而进程没退出, 此时如果有同步的请求, 可能遇见 GetWorkbook() 返回空指针的情形
	// 这种情况下同步没有意义, 返回失败给服务端
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
	if (nullptr == pKEtWorkbook)
	{
		WOLOG_ERROR << "[SyncChildProc] NULL work book";
		return WO_FAIL;
	}

	wo::util::SlowCallTimeStat callTime("SyncChildProc", 100);
	BinReader rd(input->data, input->size);
	VarObjRoot root = rd.buildRoot();
	VarObj obj = root.cast();
	if (!obj.has("operate"))
	{
		WOLOG_ERROR << "[SyncChildProc] unknown operate";
		return WO_FAIL;
	}
	ks_wstring op = obj.field_str("operate");
	constexpr unsigned int c_messageSizeWarnThreshold = 3000;
	IKUserConns *pUserConns = app->getCoreApp()->getUserConns();
	if (op == __X("getUserConn"))
	{
		QString connID = QString::fromUtf16(obj.field_str("connID"));
		IKUserConn *pUserConn = pUserConns->getUserConn(connID.toUtf8());
		if (pUserConn == nullptr)
		{
			output->data = nullptr;
			output->size = 0;
			return WO_OK;
		}

		ks_castptr<IKETUserConn> uc = pUserConn;
		BinWriter bw;
		{
			UserConnsLockGuard guard(pUserConns);
			uc->serialize(&bw, obj.field_int32("syncVer"), obj.field_int32("resetVer"));
		}
		BinWriter::StreamHolder shbt = bw.buildStream();
		output->data = shbt.detach();
		output->size = bw.writeLength();
		if (output->size > c_messageSizeWarnThreshold)
			WOLOG_WARN << "[SyncChildProc] getUserConn size over " << c_messageSizeWarnThreshold << ": " << output->size;
		return WO_OK;
	}
	else if (op == __X("setUserConn"))
	{
		QString connID = QString::fromUtf16(obj.field_str("connID"));
		IKUserConn *pUserConn = pUserConns->getUserConn(connID.toUtf8());
		if (pUserConn == nullptr)
		{
			WOLOG_INFO << "[SyncChildProc] create conn";
			QString userID = QString::fromUtf16(obj.field_str("userID"));
			UserConnEnd end = (UserConnEnd)obj.field_uint32("userConnEnd");
			double endCoopVer = obj.field_double("endCoopVer");
			WoConnFrom connFrom = (WoConnFrom)obj.field_uint32("connFrom");
			WoConnScene connScene = (WoConnScene)obj.field_uint32("connScene");
			WoConnLife connLife = (WoConnLife)obj.field_uint32("connLife");
			UserConnArgs connArgs(connFrom, connScene, connLife);
			pUserConn = pUserConns->setUserConn(connID.toUtf8(), userID.toUtf8(), end, endCoopVer, &connArgs);
			if (pUserConn == nullptr)
			{
				WOLOG_ERROR << "[SyncChildProc] conn not found";
				return WO_FAIL;
			}
		}

		ks_castptr<IKETUserConn> uc = pUserConn;
		{
			UserConnsLockGuard guard(pUserConns);
			uc->deserialize(&rd);
		}
		if (input->size > c_messageSizeWarnThreshold)
			WOLOG_WARN << "[SyncChildProc] setUserConn size over " << c_messageSizeWarnThreshold << ": " << input->size;
		output->data = nullptr;
		output->size = 0;
		return WO_OK;
	}
	else if (op == __X("updateCrossBookRef"))
	{
		WOLOG_INFO << "[crossbookref] SyncChildProc updateCrossBookRef";
		if (!gs_needUpdateCrossBook.exchange(true))
		{
			g_childProcCrossbookUerdId = QString::fromUtf16(obj.field_str("userID"));
			g_childProcCrossbookConnId = QString::fromUtf16(obj.field_str("connID"));
		}
		return WO_OK;
	}
	else if (op == __X("getDbSharedId"))
	{ // will be deprecated
		PCWSTR sharedLink = nullptr;
		IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
		
		ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
		pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
		if (spSharedLinkMgr)
		{
			QString connID = QString::fromUtf16(obj.field_str("connID"));
			IKUserConn *pUserConn = pUserConns->getUserConn(connID.toUtf8());
			sharedLink = spSharedLinkMgr->GetConnSharedLink(pUserConn);
		}

		BinWriter bw;
		if (sharedLink)
		{
			bw.addStringField(sharedLink, "sharedId");
		}
		BinWriter::StreamHolder shbt = bw.buildStream();
		output->data = shbt.detach();
		output->size = bw.writeLength();
		return WO_OK;
	}
	else if (op == __X("updateDocSlimData"))
	{
		WOLOG_INFO << "[DocSlim] SyncChildProc updateDocSlimData";
		IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
		ks_stdptr<IEtDocSlimData> spDocSlimData;
		pBook->GetExtDataItem(edBookDocSlimData, (IUnknown**)&spDocSlimData);
		if (!spDocSlimData)
		{
			WOLOG_ERROR << "[SyncChildProc updateDocSlimData] spDocSlimData not found";
			return WO_FAIL;
		}
		BookSlimData stData;
		stData.emptyCellCount = obj.field_uint32("emptyCellCount");
		stData.unusedShapesCount = obj.field_uint32("unusedShapesCount");
		stData.overlapShapesCount = obj.field_uint32("overlapShapesCount");
		stData.cellPicturesCount = obj.field_uint32("cellPicturesCount");
		stData.duplicateFormatConditionCount = obj.field_uint32("duplicateFormatConditionCount");
		stData.unusedDuplicateStyleCount = obj.field_uint32("unusedDuplicateStyleCount");
		stData.sameSheetProtectionUserRangeCount = obj.field_uint32("sameSheetProtectionUserRangeCount");
		spDocSlimData->SetBookDocSlimData(stData);
		return WO_OK;
	}
	else if (op == __X("getSignalSize"))
	{
		BinWriter bw;
		bw.addUint32Field(gs_signalSize, "signalSize");
		BinWriter::StreamHolder shbt = bw.buildStream();
		output->data = shbt.detach();
		output->size = bw.writeLength();
		return WO_OK;
	}
	else if (op == __X("setSignalSize"))
	{
		if (obj.has("size"))
		{
			unsigned int size= obj.field_uint32("size");
			if (size > gs_signalSize)
				gs_signalSize = size;
		}
		return WO_OK;
	}
	else if (op == __X("syncCoreMetric"))
	{
		BinWriter bw;
		pKEtWorkbook->coreMetric().onSyncChildMetric(obj, bw);
		BinWriter::StreamHolder shbt = bw.buildStream();
		output->data = shbt.detach();
		output->size = bw.writeLength();
		return WO_OK;
	}

	WOLOG_ERROR << "[SyncChildProc] unknown operate";
	return WO_FAIL;
}

WEB_EXPORT void FreeSyncProcWebSlice(WebSlice* output)
{
	BASE_PROCESS_CHECK_RET_VOID
	if (output && output->data)
		free(output->data);
}

WEB_EXPORT void SyncProcType(WebProcType procType)
{
	gs_procType = procType;
}

class LockMetric
{
public:
	LockMetric(bool isLock) 
	{
		wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
		if (wb)
			wb->coreMetric().onLockBefore(isLock);
	}
	~LockMetric()
	{
		wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
		if (wb)
			wb->coreMetric().onLockAfter();
	}
private:
};

WEB_EXPORT void LockSyncData()
{
	BASE_PROCESS_CHECK_RET_VOID
	wo::util::CallTimeStat callTime("LockSyncData");
	LockMetric metric(true);
	
	IKUserConns *pUserConns = app->getCoreApp()->getUserConns();
	if (pUserConns)
		pUserConns->lock();

	alg::msrLockStringResource();
}

WEB_EXPORT void UnlockSyncData()
{
	BASE_PROCESS_CHECK_RET_VOID
	wo::util::CallTimeStat callTime("UnlockSyncData");
	LockMetric metric(false);

	IKUserConns *pUserConns = app->getCoreApp()->getUserConns();
	if (pUserConns)
		pUserConns->unlock();

	alg::msrUnlockStringResource();
}

WEB_EXPORT WebInt GetLastPosition(WebInt sheetIdx, WebInt row, WebInt col)
{
	BASE_PROCESS_CHECK_RET_FAIL
	IDX sheetIdxTrans = sheetIdx;
	CELL cellTrans{ row, col };
	WebInt res = WO_FAIL;
	wo::util::CallTimeStat callTime("GetLastPosition", nullptr, [&] (unsigned int ms)  {
		WOLOG_INFO <<
			"[area_transform] GetLastPosition res: " << res <<
			", sheetIdx: " << sheetIdx << "->" << sheetIdxTrans <<
			", row: " << row << "->" << cellTrans.row <<
			", col: " << col << "->" << cellTrans.col;
	});

	wo::DetailExtArgs dummy;
	res = inlDecorateProcVariadic(nullptr, nullptr, dummy, nullptr, &wo::ExecDetail::GetLastPosition, &sheetIdxTrans, &cellTrans);
	return res;
}

static inline void *memalloc(uint32_t size)
{
	return (char *)malloc(size);
}

void printSlice(PCWSTR tag, const WebSlice *slice) {
	char *data = nullptr;
	WebInt size = 0;
	::WebSliceConvertTo(slice, &data, &size, T_JSON, memalloc);
	WOLOG_INFO << tag << data;
	if (nullptr != data)
		free(data);
}

WEB_EXPORT WebInt SubHookRules(const WebSlice* rule)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (rule == nullptr || rule->size == 0 || rule->data == nullptr)
	{
		WOLOG_ERROR << "[SubHookRules] empty input";
		return WO_FAIL;
	}
	WOLOG_INFO << "[SubHookRules]";
	printSlice(__X("SubHookRules"), rule);

	BinReader rd(rule->data, rule->size);
	VarObjRoot root = rd.buildRoot();
	VarObj obj = root.cast();
	VarObj rules = obj.get_s("rules");
	if (rules.empty())
		return WO_FAIL;
	ks_stdptr<IWebhookManager> spWebhookMgr;
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	WebhookDataCreateHelperOld createHelper(pBook);
	std::set<QString> userIds;
	binary_wo::BinWriter binWriter;
	binWriter.addKey("results");
	binWriter.beginArray();
	for (int i = 0; i < rules.arrayLength_s(); i++)
	{
		VarObj item = rules.at_s(i);
		ks_stdptr<IWebhookData> spData;
		HRESULT hr = createHelper.CreateWebhookData(item, binWriter, &spData);
		if (FAILED(hr))
			continue;
		Et_DbSheet_WebhookType type = DbSheet_WebhookType_invalid;
		spData->GetType(type);
		switch (type)
		{
			case DbSheet_WebhookType_createAndFillInRecord:
			{
				class UncheckRecordEnum : public IDbWebhookCreateAndFillUncheckRecordEnum
				{
				public:
					UncheckRecordEnum(IWebhookData* pData): m_pData(pData) {}
					STDPROC Do(EtDbId recId) override
					{
						m_pData->AddNeedCheckRecord(recId);
						return S_OK;
					}
				private:
					IWebhookData* m_pData;
				};
				UncheckRecordEnum uncheckRecordEnum(spData);
				spWebhookMgr->EnumAndEraseDbCreateAndFillUncheckRecord(spData->GetId(), &uncheckRecordEnum);
				break;
			}
			case DbSheet_WebhookType_updateSheetsAllChange:
			{
				PCWSTR userId = spData->GetUserId();
				if (userId != nullptr && xstrcmp(userId, __X("")) != 0)
					userIds.insert(krt::fromUtf16(userId));
				else
					WOLOG_INFO << "[SubHookRules] hook userId is empty";
				break;
			}
		}
		WOLOG_INFO << "[SubHookRules] hookid " << spData->GetId();
		spWebhookMgr->AddHook(spData);
	}
	binWriter.endArray();
	binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
	WebSlice slice = {shbt.get(), binWriter.writeLength()};
	gs_callback->signal("", "", &slice);

	WOLOG_INFO << "[SubHookRules] begin getCoreApp";
	IKEtApplication* ptrApp = app->getCoreApp();
	WOLOG_INFO << "[SubHookRules] begin getUserConns";
	IKUserConns* userConns = ptrApp->getUserConns();
	for (const auto& userId : userIds)
	{
		WOLOG_INFO << "[SubHookRules] begin setConnId, userId:" << userId;
		QString connId = QString("webhook_core") + userId;
		WOLOG_INFO << "[SubHookRules] begin getUserConn, connId:" << connId;
		IKUserConn* userConn = userConns->getUserConn(connId.toUtf8());
		WOLOG_INFO << "[SubHookRules] end getUserConn";
		if (userConn == nullptr)
		{
			constexpr int permissionIdBufMaxLen = 64;//目前服务端那边生成的permissionId只有30多个字节，预留数组为64，后续如果不够再加。
			char permissionIdBuf[permissionIdBufMaxLen] = {0};
			WebInt serverRes = WO_OK;
			WOLOG_INFO << "[SubHookRules] begin fetchPermissionId";
			serverRes = gs_callback->fetchPermissionId(userId.toUtf8(), permissionIdBuf, permissionIdBufMaxLen);
			WOLOG_INFO << "[SubHookRules] end fetchPermissionId";
			// 打开文件时调用由于服务端permissionId还没有准备好，因此会返回WO_FAIL
			if (serverRes != WO_OK)
			{
				WOLOG_ERROR << "[SubHookRules] fetchPermissionId error";
			}
			else
			{
				QString qPermissionId = QString::fromUtf8(permissionIdBuf);
				PCWSTR permissionId = krt::utf16(qPermissionId);
				UserConnArgs connArgs(WoConnFromCore, WoConnLifeProcess);
				userConn = userConns->setUserConn(connId.toUtf8(), userId.toUtf8(), userConnEndWeb, 1.0, &connArgs);
				userConn->setCorePermissionId(permissionId);
			}
		}
	}

	return WO_OK;
}


WEB_EXPORT WebInt UpdateHookRules(const WebSlice* rule)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (rule == nullptr || rule->size == 0 || rule->data == nullptr)
	{
		WOLOG_ERROR << "[ModifyHookRules] empty input";
		return WO_FAIL;
	}
	BinReader rd(rule->data, rule->size);
	VarObjRoot root = rd.buildRoot();
	VarObj obj = root.cast();
	VarObj rules = obj.get_s("rules");
	if (rules.empty())
		return WO_FAIL;

	WOLOG_INFO << "[UpdateHookRules]";
	printSlice(__X("UpdateHookRules"), rule);
	ks_stdptr<IWebhookManager> spWebhookMgr;
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	spWebhookMgr->setBookHasHidden(pKEtWorkbook->isBookProtectedWithHiddenProperty() ? TRUE : FALSE);
	WebhookDataCreateHelperOld createHelper(pBook);
	binary_wo::BinWriter binWriter;
	binWriter.addKey("results");
	binWriter.beginArray();
	for (int i = 0; i < rules.arrayLength_s(); i++)
	{
		VarObj item = rules.at_s(i);
		WebStr updateType = item.field_str("updateType");
		bool add = (xstrcmp(updateType, __X("add")) == 0);
		ks_stdptr<IWebhookData> spData;
		HRESULT hr = createHelper.CreateWebhookData(item, binWriter, &spData, true, add);
		if (FAILED(hr))
			continue;
		WOLOG_INFO << "[UpdateHookRules] hookid " << spData->GetId();
		spWebhookMgr->UpdateHook(spData, add);
	}
	binWriter.endArray();
	binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
	WebSlice slice = {shbt.get(), binWriter.writeLength()};
	gs_callback->signal("", "", &slice);

	return WO_OK;
}


WEB_EXPORT WebInt CheckCanHook(const WebSlice* rule)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (rule == nullptr || rule->size == 0 || rule->data == nullptr)
	{
		WOLOG_ERROR << "[CheckCanHook] empty input";
		return WO_FAIL;
	}
	BinReader rd(rule->data, rule->size);
	VarObjRoot root = rd.buildRoot();
	VarObj obj = root.cast();
	VarObj rules = obj.get_s("rules");
	if (rules.empty())
		return WO_FAIL;

	WOLOG_INFO << "[CheckCanHook]";
	printSlice(__X("CheckCanHook"), rule);
	ks_stdptr<IWebhookManager> spWebhookMgr;
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	spWebhookMgr->setBookHasHidden(pKEtWorkbook->isBookProtectedWithHiddenProperty() ? TRUE : FALSE);
	WebhookDataCreateHelperOld createHelper(pBook);
	binary_wo::BinWriter binWriter;
	binWriter.addKey("results");
	binWriter.beginArray();
	for (int i = 0; i < rules.arrayLength_s(); i++)
	{
		VarObj item = rules.at_s(i);
		WebStr updateType = item.field_str("updateType");
		bool add = (xstrcmp(updateType, __X("add")) == 0);
		ks_stdptr<IWebhookData> spData;
		HRESULT hr = createHelper.CreateWebhookData(item, binWriter, &spData, true, add);
		if (FAILED(hr))
			continue;
		WOLOG_INFO << "[CheckCanHook] hookid " << spData->GetId();
	}
	binWriter.endArray();
	binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
	WebSlice slice = {shbt.get(), binWriter.writeLength()};
	gs_callback->signal("", "", &slice);

	return WO_OK;
}


WEB_EXPORT WebInt UnSubHookRule(const char* hookId)
{
	BASE_PROCESS_CHECK_RET_FAIL
	ks_stdptr<IWebhookManager> spWebhookMgr;
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	QString id = QString::fromUtf8(hookId);
	spWebhookMgr->DeleteHook(krt::utf16(id));
	WOLOG_INFO << "[UnSubHookRule] hookId:" << id;
	return WO_OK;
}

WEB_EXPORT WebInt ExportInquirerData(const UserContext* userContext, const WebSlice *param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("ExportInquirerData");
	if (!(param && param->size != 0 && param->data != nullptr))
	{
		WOLOG_ERROR << "[ExportInquirerData] empty param";
		return WO_FAIL;
	}

	WOLOG_INFO << "[ExportInquirerData] begin parse param!";
	BinReader rd(param->data, param->size);
	VarObjRoot infoObj = rd.buildRoot();
	VarObj info = infoObj.cast();
	if (!info.has("phoneNum") || !info.has("sheetStId") )
	{
		WOLOG_ERROR << "[ExportInquirerData] param not exist phoneNum or sheetStId";
		return WO_FAIL;
	}

	WOLOG_INFO << "[ExportInquirerData] param has phoneNum and sheetStId!";
	ks_wstring phoneNum = info.field_str("phoneNum");
	UINT32 stId = info.field_uint32("sheetStId");
	WOLOG_INFO << "[ExportInquirerData] end parse param!";
	

	wo::DetailExtArgs dummy;
	return inlDecorateProcVariadic(userContext, nullptr, dummy, nullptr, &wo::ExecDetail::ExportInquirerData, stId, phoneNum);
}

WEB_EXPORT WebInt GetPixelImg(const WebSlice* param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("GetPixelImg");
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	if (!pKEtWorkbook)
	{
		WOLOG_INFO << "[GetPixelImg] File not open";
		return WO_FAIL;
	}
	if (!gs_callback || !gs_callback->uploadImage)
	{
		WOLOG_INFO << "[GetPixelImg] uploadImage interface not exist! ";
		return WO_FAIL;
	}
	if (pKEtWorkbook->GetBMP()->bDbSheet)
		return WO_FAIL;
	constexpr int defaultDpi = 30;
	constexpr double defaultScale = 0.2;
	constexpr int defaultMaxSize = 9000 * 9000;
	int dpi = defaultDpi;
	double scale = defaultScale;
	int maxSize = defaultMaxSize;
	const char* imageType = "png";
	UINT32 sampling_threshold = 0; //默认导出所有sheet中的pixel图片
	if (param && param->size != 0 && param->data)
	{
		BinReader rd(param->data, static_cast<int32>(param->size));
		VarObjRoot rootObj = rd.buildRoot();
		VarObj root = rootObj.cast();
		if (root.has("dpi"))
			dpi = root.field_int32("dpi");
		if (root.has("scale"))
			scale = root.field_double("scale");
		if (root.has("maxSize"))
			maxSize = root.field_int32("maxSize");
		if (root.has("sampling_threshold"))
			sampling_threshold = root.field_uint32("sampling_threshold");
	}

	IKWorksheets* pSheets = pKEtWorkbook->GetCoreWorkbook()->GetWorksheets();
	std::vector<int> randomSheetsIdx;//需要上传图片的sheetid
    if (sampling_threshold ==0 || sampling_threshold >= pSheets->GetSheetCount())
    {
        WOLOG_INFO << "[GetPixelImg] need to upload pixel images from all sheets";
    }
    else //需要抽样上传
    {
		std::vector<int> sheetsIdx;
		for (int i = 0, sheetCnt = pSheets->GetSheetCount(); i < sheetCnt; ++i)
		{
			IKWorksheet* pWorkSheet = pSheets->GetSheetItem(i);
			SHEETTYPE st = stUnknown;
			pWorkSheet->GetSheet()->GetFullType(&st);
			if (st != stGrid)
				continue;
			sheetsIdx.push_back(i);
		}
		std::random_device rd;
		std::mt19937 g(rd());
		std::sample(sheetsIdx.begin(), sheetsIdx.end(), std::back_inserter(randomSheetsIdx), sampling_threshold, g);

        WOLOG_INFO << "[GetPixelImg] need to upload pixel images from sampled sheets";
    }

	for (int i = 0, sheetCnt = pSheets->GetSheetCount(); i < sheetCnt; ++i)
	{
		IKWorksheet* pWorkSheet = pSheets->GetSheetItem(i);
		SHEETTYPE st = stUnknown;
		pWorkSheet->GetSheet()->GetFullType(&st);
		if (st != stGrid)
			continue;
        if (sampling_threshold != 0 && std::find(randomSheetsIdx.begin(), randomSheetsIdx.end(), i) == randomSheetsIdx.end())
            continue; //需要随机上传,但是当前sheetid不在需要上传图片的randomSheetIdx中,那么该sheet中的所有像素图就不上传
		IKWorksheetViews* pWsViews = pWorkSheet->GetWorksheetViews();
		for (int j = 0, viewCnt = pWsViews->GetWorksheetViewCount(); j < viewCnt; ++j)
		{
			IKWorksheetView* pSheetView = pWsViews->GetWorksheetViewItem(j);
			HRESULT hr = wo::util::getPixelImg(pSheetView, dpi, scale, maxSize, [imageType](const QImage& img) {
				QByteArray picData;
				QBuffer buffer(&picData);
				buffer.open(QIODevice::WriteOnly);
				img.save(&buffer, imageType);
				if (picData.isEmpty())
					return;
				QCryptographicHash hasher(QCryptographicHash::Sha1);
				hasher.addData(picData);
				QByteArray sha1 = hasher.result().toHex();
				WebSlice slice = { (WebByte*)(picData.data()), picData.size() };
				gs_callback->uploadImage(sha1.data(), imageType, &slice, nullptr);
			});
			if (FAILED(hr))
				return WO_FAIL;
		}
	}
	return WO_OK;
}

WEB_EXPORT WebInt ExportThumbnail(const UserContext* userContext, const WebSlice *param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (!userContext->connID || !userContext->userID)
	{
		WOLOG_INFO << "[ExportThumbnail] invalid args";
		return WO_FAIL;
	}

	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}

	WOLOG_INFO << "[ExportThumbnail] connID: " << userContext->connID << ", userID: " << userContext->userID;

	// 安全文档不导出缩略图
	if(wo::secdoc::IsSecurityDoc(wb->GetCoreWorkbook()))
		return WO_NOT_SUPPORT_SECURITY_DOC;
	
	wo::util::CallTimeStat callTime("ExportThumbnail", userContext->userID, [](unsigned int ms) {
		 ExecTimeCollect::gs_exportSum += ms; 
		 CollectCallTimeApiRand(API_COLLECT_STR_NAME(ExportThumbnail), ms);
	});

	return inlDecorateProc(userContext, param, &wo::ExecDetail::ExecExportThumbnail);
}

WEB_EXPORT WebInt NotifyContentPermission(const WebSlice *permissionConfig)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("NotifyContentPermission");
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}
	if (!wb->GetBMP()->bDbSheet)
		return WO_FAIL;

	if(FAILED(wo::DbSheet::ReadDBProtectionConfig(wb->GetCoreWorkbook()->GetBook(), *permissionConfig)))
	{
		WOLOG_ERROR << "[NotifyContentPermission] read permission config failed!";
		return WO_FAIL;
	}
	
	ks_stdptr<IWebhookManager> spWebhookMgr;
	IBook* pBook = wb->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	class WebhookUserIdEnum : public IWebhookDataEnum
	{
	public:
		WebhookUserIdEnum(std::set<QString>& userIds): m_userIds(userIds) {}
		BOOL Do(IWebhookData* pData) override
		{
			PCWSTR userId = pData->GetUserId();
			if (userId != nullptr && xstrcmp(userId, __X("")) != 0)
				m_userIds.insert(krt::fromUtf16(userId));
			return TRUE;
		}
	private:
		std::set<QString>& m_userIds;
	};
	std::set<QString> userIds;
	WebhookUserIdEnum webhookUserIdEnum(userIds);
	spWebhookMgr->EnumDbHook(&webhookUserIdEnum, DbSheet_WebhookType_updateSheetsAllChange);
	IKUserConns* userConns = app->getCoreApp()->getUserConns();
	for (const auto& userId : userIds)
	{
		QString connId = QString("webhook_core") + userId;
		IKUserConn* userConn = userConns->getUserConn(connId.toUtf8());
		if (userConn == nullptr)
		{
			constexpr int permissionIdBufMaxLen = 64;//目前服务端那边生成的permissionId只有30多个字节，预留数组为64，后续如果不够再加。
			char permissionIdBuf[permissionIdBufMaxLen] = {0};
			WebInt serverRes = WO_OK;
			serverRes = gs_callback->fetchPermissionId(userId.toUtf8(), permissionIdBuf, permissionIdBufMaxLen);
			if (serverRes != WO_OK)
			{
				WOLOG_ERROR << "[NotifyContentPermission] fetchPermissionId error";
			}
			else
			{
				QString qPermissionId = QString::fromUtf8(permissionIdBuf);
				PCWSTR permissionId = krt::utf16(qPermissionId);
				UserConnArgs connArgs(WoConnFromCore, WoConnLifeProcess);
				userConn = userConns->setUserConn(connId.toUtf8(), userId.toUtf8(), userConnEndUnknown, 1.0, &connArgs);
				userConn->setCorePermissionId(permissionId);
			}
		}
	}

	return WO_OK;
}


WEB_EXPORT WebInt CheckUserAllVisiblePermission(const char *connID, const char* userID)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (!userID || !connID)
	{
		WOLOG_INFO << "[CheckUserAllVisiblePermission] invalid args";
		return WO_FAIL;
	}

	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}

	WOLOG_INFO << "[CheckUserAllVisiblePermission] " << ", userID: " << userID << ", connID:" << connID;

	QString connIdQString = QString::fromUtf8(connID);
	QString userIDQString = QString::fromUtf8(userID);
	// 在测试过程中，有发现一些入口服务端传过来的 connID 是 "" 的现象，为了不要漏掉场景，内核需要统一拦截一下
	if (connIdQString == "" || userIDQString == "")
	{

		if (!wb->isBookProtectedWithHiddenProperty())
		{
			WOLOG_INFO << "[CheckUserAllVisiblePermission] userID or connID is empty but no Protected Hidden";
			return WO_OK;
		}

		WOLOG_INFO << "[CheckUserAllVisiblePermission] userID or connID is empty ";
		return WO_FAIL;
	}

	wo::DetailExtArgs dummy;
	const UserContext userContext {connID, userID, nullptr, nullptr};
	return inlDecorateProcVariadic(&userContext, nullptr, dummy, nullptr, &wo::ExecDetail::ExecCheckUserAllVisiblePermission);
}

WEB_EXPORT WebInt SetCensorShareId(const char *shareId)
{
	BASE_PROCESS_CHECK_RET_FAIL
    WOLOG_INFO << "[SetCensorShareId] shareId: " << shareId;
    QString oldShareId = QString::fromUtf16(_appcore_GainDbSheetContext()->GetCensorShareId());
    QString newShareId = QString::fromUtf8(shareId);
    if (oldShareId != newShareId)
    {
        _appcore_GainDbSheetContext()->SetCensorShareId(krt::utf16(newShareId));
        if (oldShareId.isEmpty())
        {
            // 审核时看到的内容是每个用户能看到内容的并集，包含“当前用户”时需要将其替换为任意用户
            wo::DetailExtArgs dummy;
            return inlDecorateProcVariadic(nullptr, nullptr, dummy, nullptr,
                                           &wo::ExecDetail::ConvertCurUserFilterToAnyUserFilter);
        }
    }
    return WO_OK;
}

WEB_EXPORT WebInt SetVerifiedValue(const WebSlice* slice)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (slice == nullptr || slice->size == 0 || slice->data == nullptr)
	{
		WOLOG_ERROR << "[SetVerifiedValue] empty input";
		return WO_FAIL;
	}

	BinReader rd(slice->data, slice->size);
	VarObjRoot root = rd.buildRoot();
	VarObj obj = root.cast();
	VarObj param = obj.get_s("param");
	if (param.empty())
	{
		WOLOG_ERROR << "[SetVerifiedValue] param is null!!";
		return WO_FAIL;
	}

	API_TIME_STATE(SetVerifiedValue, nullptr);
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<IDBUserGroups> spDBUserGroups;
	pBook->GetExtDataItem(edDBUserGroups, (IUnknown**)&spDBUserGroups);
	PCWSTR connId = param.field_str("connId");
	PCWSTR type = param.field_str("type");
	PCWSTR value = param.field_str("verifiedValue");
	QString qConnId = QString::fromUtf16(connId);
	if (xstrcmp(type, __X("phone")) == 0)
	{
		IKUserConns* pUsers = app->getCoreApp()->getUserConns();
		ks_castptr<IKETUserConn> spUser = pUsers->getUserConn(qConnId.toUtf8());
		if (spUser == nullptr)
		{
			WOLOG_ERROR << "[SetVerifiedValue] user is null!! connID: " << qConnId.toUtf8();
			return WO_FAIL;
		}
		spUser->addVerifiedPhone(value);
	}

	return WO_OK;
}


WEB_EXPORT WebInt NotifyTransferAutoPassword(const WebSlice *userIdList)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}
	wo::util::CallTimeStat callTime("NotifyTransferAutoPassword", nullptr, [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });

	return inlDecorateProc(nullptr, userIdList, &wo::ExecDetail::TransferAutoPassword);
}

WEB_EXPORT WebInt ExportAutofilterList(const WebSlice* param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}
	wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
	g_isCanBreak = false; // 先不支持中断
	wo::util::CallTimeStat callTime("ExportAutofilterList", nullptr, [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
	return inlDecorateProc(nullptr, param, &wo::ExecDetail::ExecExportAutofilterList);
}

struct WebStrHash
{
	UINT operator()(WebStr pswz) const { return alg::HashWString(pswz); }
};
struct WebStrEq
{
	bool operator()(WebStr lhs, WebStr rhs) const { return 0 == xstrcmp(lhs, rhs); }
};

using WebStrSet = std::unordered_set<WebStr, WebStrHash, WebStrEq>;

class KEnumInvalidUserConn : public IKEnumUserConn
{
public:
	KEnumInvalidUserConn(
		const WebStrSet & valid, 
		int maxCount, 
		unsigned int maxTimeMS,
		const std::chrono::steady_clock::time_point & curTime) 
		: m_valid(valid) 
		, m_maxCount(maxCount)
		, m_maxTimeMS(maxTimeMS)
		, m_curTime(curTime)
		, m_nFilterConnCnt(0)
		{}
	virtual int Do(IKUserConn *pUserConn) override
	{
		PCWSTR connId = pUserConn->connID();
		if (!wo::util::IsEnableRelease(connId))
		{
			++m_nFilterConnCnt;
			return S_OK;
		}

		ks_castptr<IKETUserConn> etUser = pUserConn;
		auto itr = m_valid.find(connId);
		if (itr == m_valid.end())
		{ // not find
			if (etUser->isDelayQuit())
			{
				unsigned int ms = (m_curTime - etUser->getDelayQuitStart()) / std::chrono::milliseconds(1);
				if (ms < m_maxTimeMS)
					return S_OK;
				
				m_invalidUsers.push_back(pUserConn);
			}
			else
			{
				etUser->markDelayQuit(true);
			}
		}
		else 
		{
			etUser->markDelayQuit(false);
		}
		return S_OK;
	}
	
	std::vector<IKUserConn *> invalidUsers() 
	{
		return m_invalidUsers;
	}
	
	int getFilterConnCnt() { return m_nFilterConnCnt; }
private:
	std::vector<IKUserConn *> m_invalidUsers;
	const std::chrono::steady_clock::time_point & m_curTime;
	const WebStrSet & m_valid;
	int m_maxCount;
	unsigned int m_maxTimeMS;
	int m_nFilterConnCnt;
};

void clearDeadConnectionCleanCollect(wo::KEtWorkbook* pKEtWorkbook, unsigned int ms, int quitCnt, int filterConnCnt, int svrCnt);
WebInt clearDeadConnection(const VarObj & obj)
{	
	if (!gs_WoEtSettings.EnableReleaseConn())
	{
		WOLOG_INFO << "[clearDeadConnection] clearConn is Disable.";
		return WO_OK;
	}
	
	VarObj infos = obj.get_s("connIds");
	if (infos.empty())
	{
		WOLOG_ERROR << "[clearDeadConnection] connids is null!!";
		return WO_FAIL;
	}
	
	int len = infos.arrayLength_s();
	IKUserConns *userConns = app->getCoreApp()->getUserConns();
	if (!userConns || len == userConns->count())
	{
		WOLOG_INFO << "[clearDeadConnection] conn len is same. svrCnt is: " << len;
		return S_OK;
	}
	
	WebStrSet validConns;	
	for (int i = 0; i < len; ++i)
	{
		WebStr str = infos.item_str(i);
		validConns.insert(str);
	}
	
	// find invalid connids
	std::chrono::steady_clock::time_point beginTime = std::chrono::steady_clock::now();
	int maxClearCount = gs_WoEtSettings.GetMaxClearUserConnCount();
	int timeout = gs_WoEtSettings.GetUserConnTimeout();
	int maxClearTime = gs_WoEtSettings.maxClearUserQuitTime();
	KEnumInvalidUserConn userEnum(validConns, maxClearCount, timeout, beginTime);
	userConns->enumUserConn(&userEnum);
	
	int filterConnCnt = userEnum.getFilterConnCnt();
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	std::vector<IKUserConn *> invalidUsers = userEnum.invalidUsers();
	if (invalidUsers.empty())
	{
		int markCnt = pKEtWorkbook ? pKEtWorkbook->collectInfo().getMarkQuitConnCnt() : 0;
		WOLOG_INFO << "[clearDeadConnection] no conn quit. maxClearCount " << maxClearCount << " ,timeout " << timeout
					<< " maxClearTime: " << maxClearTime
					<< " svrCnt: " << validConns.size()
					<< " filterCnt: " << filterConnCnt
					<< " markCnt: " << markCnt;
		return WO_OK;
	}
	
	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	if (pKEtWorkbook)
	{
		IBook* pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
		pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	}
	
	int quitCnt = 0;
	const int loopCnt = 50;
	for (int i = 0, sz = invalidUsers.size(); i < sz;)
	{
		int end = std::min(i + loopCnt, sz);
		for (; i < end; ++i)
		{
			ks_castptr<IKETUserConn> pUserConn = invalidUsers[i];
			PCWSTR userID = pUserConn->userID();
			PCWSTR connID = pUserConn->connID();
			if (pKEtWorkbook)
				pKEtWorkbook->collectInfo().addCleanConn(pUserConn); // userQuit不会返回false
			if (userQuit(userID, connID, spSharedLinkMgr, pUserConn, quitCnt < 10))
			{
				++quitCnt;
			}
			else
			{
				WOLOG_ERROR << "[clearDeadConnection] connId not found: " << connID;
			}
		}
		unsigned int msLoop = (std::chrono::steady_clock::now() - beginTime) / std::chrono::milliseconds(1);
		if (msLoop >= maxClearTime)
			break;
	}
	
	unsigned int ms = (std::chrono::steady_clock::now() - beginTime) / std::chrono::milliseconds(1);
	gs_WoEtSettings.UpdateClearUserQuitTime(ms);
	
	clearDeadConnectionCleanCollect(pKEtWorkbook, ms, quitCnt, filterConnCnt, validConns.size());
	return WO_OK;
}

void clearDeadConnectionCleanCollect(wo::KEtWorkbook* pKEtWorkbook, unsigned int ms, int quitCnt, int filterConnCnt, int svrCnt)
{
	IKUserConns *userConns = app->getCoreApp()->getUserConns();
	int leftUserConns = userConns->count();
	if (pKEtWorkbook)
	{
		wo::KBookCollectInfo & info = pKEtWorkbook->collectInfo();
		ks_wstring cleanInfo = info.takeCleanConnInfo();
		ks_wstring cleanSvrFrom = info.getCleanConnSvrFrom();
		ks_wstring cleanSvrScene = info.getCleanConnSvrScene();
		ks_wstring cleanSvrLife = info.getCleanConnSvrLife();
	
		wo::WoFileIncludeCollector collector(pKEtWorkbook, __X("behaviour_clean_conns"), quitCnt);
		collector.addUserConnCnt(leftUserConns)
				.addCount2(ms)
				.addCmdName(cleanInfo)
				.addConnSvrFrom(cleanSvrFrom)
				.addConnSvrScene(cleanSvrScene)
				.addConnSvrLife(cleanSvrLife)
				.collect();
		WOLOG_INFO << "[clearDeadConnection] conn quit cnt: " << quitCnt << ", conn cnt: " << leftUserConns << ", elapsed: " << ms 
					<< " svrCnt: " << svrCnt
					<< " filterCnt: " << filterConnCnt
					<< " markCnt: " << info.getMarkQuitConnCnt();
		pKEtWorkbook->collectInfo().resetCleanConnSvrInfo();
		
		if (info.getMarkQuitConnCnt())
		{
			int markCount = info.getMarkQuitConnCnt();
			ks_wstring markQuintInfo = info.takeMarkQuitConnInfo();
			ks_wstring markQuitSvrFrom = info.getMarkQuitConnSvrFrom();
			ks_wstring markQuitSvrScene = info.getMarkQuitConnSvrScene();
			ks_wstring markQuitSvrLife = info.getMarkQuitConnSvrLife();
		
			wo::WoFileIncludeCollector collector(pKEtWorkbook, __X("behaviour_mark_quit_conns"), markCount);
			collector.addName1(markQuintInfo)
				.addConnSvrFrom(markQuitSvrFrom)
				.addConnSvrScene(markQuitSvrScene)
				.addConnSvrLife(markQuitSvrLife)
				.collect();
			info.resetMarkQuitConnSvrInfo();
		}
	}
}

void clearDeadConnectionNewCollect()
{
	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	if (!pKEtWorkbook)
		return;
	
	wo::KBookCollectInfo & info = pKEtWorkbook->collectInfo();
	if (!info.checkIsCollectReuseAndNewConn())
		return;
	
	int reuseCnt = info.getReuseConnCnt();
	int newCnt = info.getNewConnCnt();
	ks_wstring reuseInfo = info.takeReuseConnInfo();
	ks_wstring newConnInfo = info.takeNewConnInfo();
	ks_wstring newConnSvrFrom = info.getNewConnSvrFrom();
	ks_wstring newConnSvrScene = info.getNewConnSvrScene();
	ks_wstring newConnSvrLife = info.getNewConnSvrLife();
	wo::WoFileIncludeCollector collector(pKEtWorkbook, __X("behaviour_collect_conns"), newCnt);
	collector.addCount2(reuseCnt)
			 .addName1(reuseInfo)
			 .addCmdName(newConnInfo)
			 .addConnSvrFrom(newConnSvrFrom)
			 .addConnSvrScene(newConnSvrScene)
			 .addConnSvrLife(newConnSvrLife)
			 .collect();
	info.resetNewConnSvrInfo();
}
 

WEB_EXPORT WebInt NotifyFromServer(ServerNotifyType type, const WebSlice* slice)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (slice == nullptr || slice->size == 0 || slice->data == nullptr)
	{
		WOLOG_ERROR << "[NotifyFromServer] empty input";
		return WO_FAIL;
	}

	BinReader rd(slice->data, slice->size);
	VarObjRoot root = rd.buildRoot();
	VarObj obj = root.cast();
	switch (type)
	{
		case ServerNotifyType::UserPermissionIdChanged:
		{
			VarObj infos = obj.get_s("infos");
			if (infos.empty())
			{
				WOLOG_ERROR << "[NotifyFromServer] permissionIds is null!!";
				return WO_FAIL;
			}
			wo::DetailExtArgs dummy;
			inlDecorateProcVariadic(nullptr, nullptr, dummy, nullptr, &wo::ExecDetail::ExecSetUserPermissionId, &infos);
			break;
		}
		case ServerNotifyType::ClearDeadConnection:
			clearDeadConnection(obj);
			clearDeadConnectionNewCollect();
			break;
		case ServerNotifyType::DocCreatorChanged:
		{
			wo::KEtWorkbook* pWb = app->GetWorkbooks()->GetWorkbook();
			if (nullptr == pWb)
			{
				if (gs_callback && gs_callback->log)
					gs_callback->log(WO_LOG_ERROR, "file not opened");
				return WO_FAIL;
			}
			WebStr creatorID = nullptr;
			if (obj.has("creatorID"))
			{
				creatorID = obj.field_str("creatorID");
				pWb->SetCreatorUserID(creatorID);
			}
			WOLOG_INFO << "[NotifyFromServer]  doc creator changed, new creatorID:" << creatorID;
			break;
		}
		default:
			return WO_FAIL;
	}

	return WO_OK;
}

WEB_EXPORT WebInt SplitToNewFile(const UserContext* userContext, const char* path, const WebSlice* param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	const char* userID = userContext->userID;
	const char* connID = userContext->connID;
	// 需要检验禁止查看 判断userId和connid
	if (!userID || !connID)
	{
		WOLOG_INFO << "[SplitToNewFile] invalid args";
		return WO_FAIL;
	}

	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}
	WOLOG_INFO << "[SplitToNewFile] " << ", userID: " << userID << ", connID:" << connID;

	QString connIdQString = QString::fromUtf8(connID);
	QString userIDQString = QString::fromUtf8(userID);
	// 在测试过程中，有发现一些入口服务端传过来的 connID 是 "" 的现象，为了不要漏掉场景，内核需要统一拦截一下 此接口需要判断是否禁止查看
	if (connIdQString == "" || userIDQString == "")
	{
		WOLOG_INFO << "[SplitToNewFile] userID or connID is empty ";
		return WO_FAIL;
	}

	wo::util::CallTimeStat callTime("SplitToNewFile", nullptr, [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });

	wo::DetailExtArgs dummy;
	return inlDecorateProcVariadic(userContext, param, dummy, nullptr, &wo::ExecDetail::SplitToNewFile, path);
}


WEB_EXPORT WebInt ExportAirApp(const WebSlice* param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");

		return WO_FAIL;
	}
	wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
	g_isCanBreak = false; // 先不支持中断
	wo::util::CallTimeStat callTime("ExportAirApp", nullptr, [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
	return inlDecorateProc(nullptr, param, &wo::ExecDetail::ExecExportAirApp);
}

WEB_EXPORT WebInt GetMergedSubRanges(const WebSlice* param)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");
		return WO_FAIL;
	}
	wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
	g_isCanBreak = false; // 先不支持中断
	wo::util::CallTimeStat callTime("GetMergedSubRanges", nullptr, [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });
	return inlDecorateProc(nullptr, param, &wo::ExecDetail::GetMergedSubRanges);
}

WEB_EXPORT WebInt ExecCopyImg(const UserContext* userContext, WebSlice *commands)
{
	BASE_PROCESS_CHECK_RET_FAIL
	if (!userContext->connID || !userContext->userID)
	{
		WOLOG_INFO << "[ExecCopyImg] invalid args";
		return WO_FAIL;
	}

	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	if (nullptr == wb)
	{
		if (gs_callback && gs_callback->log)
			gs_callback->log(WO_LOG_WARN, "file not opened");
		return WO_FAIL;
	}
	WOLOG_INFO << "[ExecCopyImg] connID: " << userContext->connID  << ", userID: " << userContext->userID;
	// 安全文档不导出缩略图
	if(wo::secdoc::IsSecurityDoc(wb->GetCoreWorkbook()))
		return WO_NOT_SUPPORT_SECURITY_DOC;

	wo::util::CallTimeStat callTime("ExecCopyImg", nullptr, [](unsigned int ms) { ExecTimeCollect::gs_exportSum += ms; });

	return inlDecorateProc(userContext, commands, &wo::ExecDetail::ExecCopyImg);
}

WEB_EXPORT WebInt GetLocalImg(const UserContext* userContext, const WebSlice* commands, WebSlice* output)
{
	BASE_PROCESS_CHECK_RET_FAIL
	using Sha1ToShapeName = std::unordered_map<PCWSTR, std::vector<PCWSTR>, wo::util::PcwstrIHash, wo::util::PcwstrICmp>;

	wo::util::CallTimeStat callTime("GetLocalImg");
	if (!userContext->userID || !userContext->connID)
	{
		WOLOG_INFO << "[GetLocalImg] userID or connID is empty";
		return WO_FAIL;
	}

	ks_wstring userIdStr(krt::utf16(QString::fromUtf8(userContext->userID)));
	output->data = nullptr;
	output->size = 0;

	wo::KEtWorkbook* pKEtWorkbook = app->GetWorkbooks()->GetWorkbook();
	if (!pKEtWorkbook)
	{
		WOLOG_INFO << "[GetLocalImg] File not open";
		return WO_FAIL;
	}
	if (pKEtWorkbook->GetBMP()->bDbSheet)
		return WO_FAIL;

	if (!commands || !commands->data || commands->size == 0)
	{
		WOLOG_INFO << "[GetLocalImg] Query commands is empty";
		return WO_FAIL;
	}

	BinReader rd(commands->data, commands->size);
	VarObjRoot root = rd.buildRoot();
	VarObj obj = root.cast();
	if (!obj.has("data"))
	{
		WOLOG_INFO << "[GetLocalImg] Invalid query param";
		return WO_FAIL;
	}

	VarObj dataVar = obj.get_s("data");
	int dataCnt = dataVar.arrayLength();
	if (dataCnt <= 0)
	{
		WOLOG_INFO << "[GetLocalImg] Query data is empty";
		return WO_FAIL;
	}

	// 进行权限检验
	IKWorksheets* pSheets = pKEtWorkbook->GetCoreWorkbook()->GetWorksheets();
	for (int sheetIdx = 0, cnt = pSheets->GetSheetCount(); sheetIdx < cnt; ++sheetIdx)
	{
		IKWorksheet *pKWorkSheet = pKEtWorkbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		if (!pKWorkSheet)
			return WO_FAIL;

		ISheetProtection* pSheetProtection = pKWorkSheet->GetProtection();
		if (pSheetProtection->IsProtected() && pSheetProtection->IsInHiddenRanges(userIdStr.c_str()))
		{
			WOLOG_INFO << "[GetLocalImg] Not have permission";
			return WO_FAIL;
		}
	}

	Sha1ToShapeName sha1ToShapeName;
	for (int i = 0; i < dataCnt; ++i)
	{
		VarObj itemVar = dataVar.at(i);
		if (itemVar.has("sha1") && itemVar.has("shapeName"))
		{
			PCWSTR sha1 = itemVar.get_s("sha1").value_str();
			PCWSTR shapeName = itemVar.get_s("shapeName").value_str();
			sha1ToShapeName[sha1].push_back(shapeName); 
		}
		else
		{
			WOLOG_INFO << "[GetLocalImg] sha1 or shapeName is missing";
			return WO_FAIL;
		}
	}

	ICellImages* pCellImages = pKEtWorkbook->GetCoreWorkbook()->GetCellImages();
	BinWriter bw;
	bw.beginArray("data");
	{
		// 1. 遍历单元格图片对象数
		for (auto sha1Itr = sha1ToShapeName.begin(); sha1Itr != sha1ToShapeName.end(); )
		{
			QString sha1;
			QByteArray picData;
			ks_wstring imageType(__X("image/"));
			PCWSTR imageName = __X("");

			std::vector<PCWSTR>& shapeNames = sha1Itr->second;
			for (auto snItr = shapeNames.begin(); snItr != shapeNames.end(); ++snItr)
			{
				ks_castptr<drawing::AbstractShape> cpAbsShape = pCellImages->GetImgByName(*snItr);
				if (!cpAbsShape || !cpAbsShape->isPicture())
					continue;
				cpAbsShape->getOriginalPictureInformation(sha1, picData);
				if (sha1.isEmpty())
					continue;
				ASSERT(xstrcmp(sha1Itr->first, krt::utf16(sha1)) == 0);
				if (xstrcmp(sha1Itr->first, krt::utf16(sha1)) != 0)
				{
					WOLOG_INFO << "[GetLocalImg] sha1 and shapeName don't match";
					return WO_FAIL;
				}
				const drawing::Blip& blip = cpAbsShape->picture()->blip();
				imageType.append(wo::util::GetBlipTypeStr(
						blip.blipSvg().isValid() ? blip.blipSvg().getCachePngBlipAtom() : blip.blipAtom()));
				imageName = cpAbsShape->nameValue();
				break;  // 找到对应图片数据则不需要继续遍历shapeNames，直接退出
			}

			// 对应图片不存在单元格图片对象树中，直接迭代下一个，不进行序列化
			if (picData.isEmpty())
			{
				++sha1Itr;
				continue;
			}

			bw.beginStruct();
			bw.addStringField(imageType.c_str(), "type");
			bw.addStringField(imageName, "name");
			bw.addStringField(krt::utf16(sha1), "sha1");
			bw.beginArray("shapeNames");
			for (auto snItr = shapeNames.begin(); snItr != shapeNames.end(); ++snItr)
			{
				bw.addString(*snItr);
			}
			bw.endArray();
			bw.addKey("data");
			bw.addArrayBuffer((WebByte*)(picData.data()), picData.size());
			bw.endStruct();

			// 序列化之后从集合中删除并获取下一个迭代对象
			sha1Itr = sha1ToShapeName.erase(sha1Itr);
		}
		/*
			2. todo 后续如果要支持非单元格图片数据获取可以直接在这里继续遍历sha1ToShapeName集合从其他对象树获取数据
		**/
	}
	bw.endArray();

	BinWriter::StreamHolder shbt = bw.buildStream();
	output->data = shbt.detach();
	output->size = bw.writeLength();

	WOLOG_INFO << "[GetLocalImg] Output data size: " << output->size;

	return WO_OK;
}

WEB_EXPORT WebInt ExecDataAnalyze(const UserContext* userContext, const WebSlice* commands, WebSlice* pOutput)
{
	BASE_PROCESS_CHECK_RET_FAIL
	wo::util::CallTimeStat callTime("ExecDataAnalyze");
	wo::KEtWorkbooks* wbs = app->GetWorkbooks();
	return wo::ExecDataAnalyzeInl(wbs, userContext, commands, pOutput);
}

WEB_EXPORT WebInt SetCancel(const WebSlice* param)
{
	if (!param || param->size == 0 || !param->data)
		return WO_OK;

	binary_wo::BinReader rd(param->data, param->size);
	binary_wo::VarObjRoot rootObj = rd.buildRoot();
	binary_wo::VarObj root = rootObj.cast();
	WebStr breakType = root.field_str("break_type");

	WoBreakType type =  WoBreakType::BreakNone;
	if (!xstrcmp(breakType, __X("ErrorRepairCancel")))
		type = WoBreakType::BreakErrorRepair;
	else if (!xstrcmp(breakType, __X("PasteCancel")))
		type = WoBreakType::BreakPaste;

	gs_WoCallBackImpl.setTypeBreak(type, TRUE);
	WOLOG_INFO << "[SetCancel] Setup successful, type: " << breakType;
	return WO_OK;
}

WEB_EXPORT WebInt GetInitCommonLog()
{
	wo::util::CallTimeStat callTime("GetInitCommonLog");
	if (WO_OK != CheckWorkbookOpened("GetInitCommonLog"))
		return WO_FAIL;
	wo::KEtWorkbook* wb = app->GetWorkbooks()->GetWorkbook();
	ks_stdptr<IDbCommonLog> spLog;
	IBook* pBook = wb->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookDbCommonLog, (IUnknown**)&spLog);
	if (!spLog)
	{
		WOLOG_INFO << "[GetInitCommonLog] IDbCommonLog nullptr";
		return WO_FAIL;
	}
	wo::KDbValueSerializeHelper stHelper;
	stHelper.Init(wb);
	spLog->SerializeInit(&stHelper);
	return WO_OK;
}

WEB_EXPORT WebInt SetEnableCommonLog(bool bEnable)
{
	auto* pSettings = _kso_GetWoEtSettings();
	if (!pSettings)
	{
		WOLOG_INFO << "[SetEnableCommonLog] WoEtSettings nullptr";
		return WO_FAIL;
	}
	pSettings->SetEnableCommonLog(bEnable);
	return WO_OK;
}
