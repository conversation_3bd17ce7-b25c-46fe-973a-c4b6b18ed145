﻿#include "etstdafx.h"
#include "etshareapplication.h"
#include "etentrys.h"

// etchart链接错误
namespace chart{
	class KCTChartDataSourceProvider;
}
void postShowDlgEvent(chart::KCTChartDataSourceProvider *provider){}
void postChartHostDocumentDestoriedEvent(IKWorkbook *){}
void postETSaveFile(IKWorkbook* ptrWorkbook, const QString &){}
void postRefreshChartDataEvent(){}
void postShowApplicationEvent(){}
void postETOpenFileEvent(const QString& file){}
void postBringWindowToTopEvent(){}
void postShowErrorBarCustomDlgEvent(chart::KCTChartDataSourceProvider* provider, uint seriesIndex, bool isErrorBarX) {}

namespace wo
{
namespace et
{
#ifdef X_OS_WINDOWS
static bool _wideWin32EventFilter( MSG * msg )
{
	_KWin32MsgEvent e(msg);
	return kxApp->sendEvent(kxApp, &e);
}
#endif

#if WEB_OFFICE_ENABLE
static int defaultArgc = 3;
static const char* constArgv[] = { "", "-platform", "offscreen" };
static char** defaultArgv = (char**)constArgv;
#else
static int defaultArgc = 0;
static char** defaultArgv = nullptr;
#endif

KConApplication::KConApplication(QString appName)
        : KConApplication(appName, defaultArgc, defaultArgv)
{

}

KConApplication::KConApplication(QString appName, int &argc, char **argv)
	: QApplication(argc, argv)
	, m_translators(new krt::KAppTranslators(this))
	, m_coreApplication(NULL)
	, KxAppCoreNotify(NULL, this)
	, KxWindowsCoreNotify(NULL, this)
	, KxDocumentsCoreNotify(NULL, this)
	, KxDocumentCoreNotify(NULL, this)
	, m_cursorIndex(0)
{

	krt::init("Kingsoft",
			  "Office",
			  "6.0",
			  appName,
			  QCoreApplication::applicationDirPath(),
			  QCoreApplication::arguments(),
			  96,
			  96,
			  96,
			  96,
			  QGuiApplication::primaryScreen()->availableGeometry(),
			  QGuiApplication::primaryScreen()->availableGeometry(),
			  QLocale::system()
			  );

	// 程序启动时需要加载拼音字典文件(linux), 这个在大多情况下是不必要的(占用约2M)
    // 拼音字典只有在排序和公式对字符串比较时才需要, 只有字符串不相同且比较的字符都不是ascii字符时才需要字典文件, 触发频率不高
	constexpr bool lazyLoadDict = true;
	krt::locale::init(lazyLoadDict);
	kfc::nf::_XNFInitMultilingualInfo();

	setOrganizationDomain("www.wps.cn");
	setOrganizationName("Kingsoft");
	m_productName = QString("Office");
	setApplicationVersion("6.0");
	setApplicationName(appName);

	QStringList qmList;
	qmList.append(appName);
	qmList.append("kso");

	// 翻译资源大约占用 2M 内存, 这里改为内存映射以便多进程共享内存
	constexpr bool useFileMap = true;
	m_translators->setup(qmList, useFileMap);

#ifdef X_OS_WINDOWS
	QAbstractEventDispatcher::instance()->setEventFilter((QAbstractEventDispatcher::EventFilter)&_wideWin32EventFilter);
#endif
}

KConApplication::~KConApplication()
{
	delete m_translators;
}

QString KConApplication::productName()
{
	return m_productName;
}

QString KConApplication::productVersion()
{
	return applicationVersion();
}

IKApplication* KConApplication::coreApplication()
{
	return m_coreApplication;
}

KxKsoModule* KConApplication::libKso()
{
	return &m_ksoLib;
}

void KConApplication::setCoreApplication(IKApplication *coreApp)
{
	if (m_coreApplication)
		return;
	m_coreApplication = coreApp;
	KxAppCoreNotify::addCoreObject(coreApp);
	KxWindowsCoreNotify::addCoreObject(coreApp->GetWindows());
	KxDocumentsCoreNotify::addCoreObject(coreApp->GetDocuments());
	m_coreApplication->SetShellService(this);
}

int KConApplication::exec()
{
	int r = QGuiApplication::exec();
	return r;
}

STDMETHODIMP KConApplication::NewDialog(KSO_DialogType,
										IUnknown* objParam,
										IUnknown* envParam,
										IGeneralEvent*,
										void* userData,
										IShellDialog** ppDlg
										)
{
	*ppDlg = &m_dialog;
	return S_OK;
}
STDMETHODIMP_(int) KConApplication::MessageBox(	const BSTR Text,
												const BSTR Caption,
												DWORD Flags
												)
{
	return 0;
}

STDMETHODIMP_(int) KConApplication::InputBox(	KSO_InputBoxID,
												IROStringsMap* inParams,
												IROStringsMap** outParams,
												IUnknown* Callback/* = NULL*/, 
												IUnknown* pDocument/* = NULL*/
												)
{
	return S_OK;
}


STDMETHODIMP_(IDataClipboard*)	KConApplication::GetDataClipboard()
{
	return NULL;
}
STDMETHODIMP_(IApcCompManagerSite*) KConApplication::GetApcCompManagerSite()
{
	return NULL;
}
STDMETHODIMP_(HWND)	KConApplication::GetActiveMainWindowHandle()
{
	return NULL;
}
STDMETHODIMP_(ISmartLabelsService*) KConApplication::GetSmartLabelService()
{
	return NULL;
}

STDMETHODIMP_(IIMEProperty*)  KConApplication::GetImeProperty()
{
	return NULL;
}

STDMETHODIMP KConApplication::Get_CursorIndex(OUT long* id)
{
	*id = m_cursorIndex;
	return S_OK;
}
STDMETHODIMP KConApplication::Set_CursorIndex(IN long id)
{
	m_cursorIndex = id;
	return S_OK;
}
STDMETHODIMP KConApplication::GetThemeColor(IN INT idx, OUT UINT* Argb)
{
	*Argb = 0;
	return S_FALSE;
}

STDMETHODIMP_(ShellVersion) KConApplication::GetShellVersion()
{
	return ShellVersion2018;
}

STDMETHODIMP_(int) KConApplication::ShowApiDefaultDialog(BSTR bstrInfo, BOOL* bNotShowAgain)
{
	return S_OK;
}

STDMETHODIMP KConApplication::GetThemeImagePath(int, BSTR* Path)
{
	 *Path = NULL;
	 return S_OK;
}


bool KConApplication::appQueryQuitNotify(IKApplication* coreApp, ksoNotify* ne)
{
	return true;
}
bool KConApplication::appCreateDocumentNotify(IKDocument* coreDoc, ksoNotify* ne)
{
	return true;
}
bool KConApplication::appCreateMainWindowNotify(IKMainWindow* coreMainWin, ksoNotify* ne)
{
	return true;
}
bool KConApplication::appDestroyNotify(IKApplication* coreApp, ksoNotify* ne)
{
	m_coreApplication = NULL;
	this->quit();
	return true;
}
bool KConApplication::appDataChangedNotify(IKApplication* coreApp, ksoNotify* ne)
{
	return true;
}
bool KConApplication::appBeforeOpenDocumentNotify(IKDocument* coreDoc, ksoOpenNotify* ne)
{
	return true;
}
bool KConApplication::appAfterOpenDocumentNotify(IKDocument* coreDoc, ksoOpenNotify* ne)
{
	return true;
}
bool KConApplication::appCoreNotify(IKApplication* coreApp, ksoNotify* neotify)
{
	return true;
}
bool KConApplication::addWindowNotify(IKWindow* coreWin, ksoNotify* ne)
{
	return true;
}
bool KConApplication::removeWindowNotify(IKWindow* coreWin, ksoNotify* ne)
{
	return true;
}
bool KConApplication::windowsActiveNotify(IKWindow* coreWin, ksoNotify* ne)
{
	return true;
}
bool KConApplication::windowsDeactiveNotify(IKWindow* coreWin, ksoNotify* ne)
{
	return true;
}
bool KConApplication::addDocumentNotify(IKDocument* coreDoc, ksoNotify* ne)
{
	KxDocumentCoreNotify::addCoreObject(coreDoc);
	return true;
}
bool KConApplication::removeDocumentNotify(IKDocument* coreDoc, ksoNotify* ne)
{
	KxDocumentCoreNotify::removeCoreObject(coreDoc);
	return true;
}
bool KConApplication::docsActiveNotify(IKDocument* coreDoc, ksoNotify* ne)
{
	return true;
}
bool KConApplication::docsDeactiveNotify(IKDocument* coreDoc, ksoNotify* ne)
{
	return true;
}
bool KConApplication::docBeforeOpenNotify(IKDocument* doc, ksoOpenNotify* ne)
{
	return true;
}
bool KConApplication::docAfterOpenNotify(IKDocument* doc, ksoOpenNotify* ne)
{
	return true;
}
bool KConApplication::docBeforeSaveNotify(IKDocument* doc, ksoSaveNotify* ne)
{
	return true;
}
bool KConApplication::docAfterSaveNotify(IKDocument* doc, ksoSaveNotify* ne)
{
	return true;
}
bool KConApplication::docQueryCloseNotify(IKDocument* doc, ksoQueryCloseNotify* ne)
{
	return true;
}
bool KConApplication::docDataChangedNotify(IKDocument* doc, ksoNotify* ne)
{
	return true;
}
bool KConApplication::docDestroyNotify(IKDocument* doc, ksoNotify* ne)
{
	return true;
}
bool KConApplication::event(QEvent* e)
{
	if (e->type() == QEvent::Quit)
	{
		return quitEvent();
	}
	else if (e->type() == LauchQuitApplication)
	{
		lauchQuitApplication();
	}
#ifdef X_OS_WINDOWS
	else if (e->type() == KEvent::_Win32Msg)
	{
		return false;
	}
#endif
	return QGuiApplication::event(e);
}
bool KConApplication::quitEvent()
{
	if (m_coreApplication)
	{
		QGuiApplication::postEvent(this, new QEvent(QEvent::Type(LauchQuitApplication)));
	}
	return true;
}
void KConApplication::lauchQuitApplication()
{
	ksoQueryQuitNotify ne(m_coreApplication, true, true);
	if (m_coreApplication->FireCoreNotify(&ne))
	{
		m_coreApplication->Destroy();
	}
}

HRESULT KConApplication::OnNofity( IN KPdfExportNotifyCode code, IN VARIANT iParam, OUT VARIANT* oParam )
{
	return S_OK;
}

STDMETHODIMP_(bool) KConApplication::QConnect( QObject *, const char *, const char * )
{
	return false;
}

STDMETHODIMP_(bool) KConApplication::IsCloudFile(const QString&)
{
	return false;
}

STDMETHODIMP_(bool) KConApplication::IsCloudCacheBackupFile(const QString& filePath)
{
	return false;
}

STDMETHODIMP_(AppShellMode) KConApplication::GetAppShellMode()
{
	return AppShellModeNormal;
}

STDMETHODIMP KConApplication::GetAppGUIFontName(const char *, QString&)
{
	return E_NOTIMPL;
}

STDMETHODIMP KConApplication::CheckFileEditingLock(BSTR bstrFilePath, BOOL &bSaveAs)
{
	return E_NOTIMPL;
}

STDMETHODIMP_(int) KConApplication::ShowEdittingLockMessageBox(IN const BSTR text)
{
	return 0;
}

STDMETHODIMP KConApplication::OpenPromeBrowser(QString& url, bool bFromDocArea)
{
	return E_NOTIMPL;
}

STDMETHODIMP KConApplication::SendDcInfoCollect(const QString& eventName,const QHash<QString,QString>& customArgs)
{
	return E_NOTIMPL;
}

STDMETHODIMP KConApplication::SendOpenFileSrcInfoCollect(const QString& eventName, const QString& appName, const QString& from)
{
	return E_NOTIMPL;
}

STDMETHODIMP KConApplication::GetAppUIThemePropFontsMap(const QString& className, QMap<QString, QFont>&)
{
	return E_NOTIMPL;
}

STDMETHODIMP KConApplication::NotifyPromeMainWindowStateChanged(IKMainWindow* coremw)
{
	return E_NOTIMPL;
}

STDMETHODIMP KConApplication::SetLeaveModalActivateProme(bool bActive)
{
	return E_NOTIMPL;
}

STDMETHODIMP_(bool) KConApplication::IsNeedLeaveModalActivateProme()
{
	return false;
}

STDMETHODIMP KConApplication::SetBlockModalActivateProme(bool bBlock)
{
	return E_NOTIMPL;
}

STDMETHODIMP_(bool) KConApplication::IsPromeShelllessMode()
{
	return false;
}

STDMETHODIMP KConApplication::EnsureLoadOfficespace()
{
	return E_NOTIMPL;
}

STDMETHODIMP_(IAppCenterDelegate*) KConApplication::GetAppCenterDelegate()
{
	return nullptr;
}

STDMETHODIMP_(int) KConApplication::Get_AppModalLevel()
{
	return 0;
}

STDMETHODIMP_(bool) KConApplication::IsDarkModeTheme()
{
	return false;
}

STDMETHODIMP_(bool) KConApplication::startAccessingSecurityScopedResourceWithFilePath(const QString & filepath, bool showAuthorizeDialog, bool bRemoveFileBookmark)
{
	return false;
}

STDMETHODIMP_(bool) KConApplication::hasAccessingSecurityScopedResourceWithFilePath(const QString & filepath)
{
	return false;
}

STDMETHODIMP_(bool) KConApplication::saveOpenFileToBookmarkWithFilePath(const QString & filepath)
{
	return false;
}

STDMETHODIMP_(bool) KConApplication::isExistWhiteListFonts(QStringList &missFontList)
{
	return false;
}

STDMETHODIMP_(BOOL) KConApplication::IsVbaReady()
{
	return FALSE;
}

STDMETHODIMP_(BOOL) KConApplication::IsJdeReady()
{
	return FALSE;
}

STDMETHODIMP_(BOOL) KConApplication::IsJdeMode()
{
	return FALSE;
}

STDMETHODIMP_(bool) KConApplication::ShowDevelopLanguage()
{
	return FALSE;
}

STDMETHODIMP KConApplication::CoreWaitChartUpdateFinish()
{
	return E_NOTIMPL;
}

STDMETHODIMP_(void) KConApplication::CoreLogMsg(const QString& moduleName, const QString &msg)
{
	return;
}

STDMETHODIMP_(void) KConApplication::NotifyPromeCloseMsg(
	const QString& moduleName,
	const QString& msg,
	PromeCloseMsgActionType action,
	const QMap<QString, QString> &extParams)
{
	return;
}

STDMETHODIMP_(int) KConApplication::ShowInsertPicFailedMessageBox(IN const BSTR Text,
	IN const BSTR Caption,
	IN DWORD Flags,
	IN bool bHasLableLink,
	IN bool bTransReselct,
	OUT bool* pClickedLabel)
{
	return 0;
}

STDMETHODIMP_(int) KConApplication::ShowQuitCoopEditMessageBox(
	IN DWORD Flags,
	IN bool bQuiting,
	IN bool bQuitFailed,
	IN bool bCoopNetOK)
{
	return 0;
}

// 判断是否支持触屏特性，该特性目前只在Windows10下生效
STDMETHODIMP_(bool) KConApplication::IsTouchEnabled() const
{
	return false;
}

STDMETHODIMP_(void) KConApplication::NotifyUrlHyperlinkClick(LPCWSTR pcwHyperAddress)
{
	return;
}

// from old IKShellServiceEx
STDMETHODIMP_(int) KConApplication::ExecMessageBox(
	const BSTR Text, 
	const BSTR Caption, 
	DWORD Flags,
	const std::vector<ks_wstring>& ButtonTexts)
{
	return 0;
}

STDMETHODIMP_(int) KConApplication::MessageBox4(
	const BSTR Text, const BSTR Caption,
	const BSTR AcceptBtn, const BSTR RejectBtn,
	const BSTR InputPromote, void* dataOut)
{
	return 0;
}

STDMETHODIMP_(int) KConApplication::MessageBox3(
	const BSTR Text,
	const BSTR Caption)
{
	return 0;
}

STDMETHODIMP_(bool) KConApplication::IsOfficialIndepentComponent()
{
	return false;
}

STDMETHODIMP_(bool) KConApplication::CheckAndOpenbyOtherComponent(const BSTR filePath)
{
	return false;
}
STDIMP KConApplication::OpenPromeBrowser(const QString& url, bool bFromDocArea)
{
	return S_OK;
}

STDIMP KConApplication::SendDcInfoCollect(const QString& eventName, const QHash<QString, QString>& customArgs, SendMsgProbability pb)
{
	return S_OK;
}

STDIMP KConApplication::SetMultiRecordInfoCollect(MultiRecordInfoType type, bool bBegin)
{
	return S_OK;
}

STDIMP_(bool) KConApplication::GetHoneycombConfig(qint64 moduleId) // 获取字段类型为bool的蜂巢
{
	return false;
}

STDIMP_(QVariant) KConApplication::GetHoneycombConfigValue(qint64 moduleId) // 获取全部字段类型的蜂巢
{
	return QVariant();
}

STDIMP_(int) KConApplication::ShowCustomMsgBox(CustomMessageBoxType cmbt, PCWSTR pcwsCaption, void* extra)
{
	return 0;
}

STDIMP_(bool) KConApplication::RestrictEmbedFont(IKDocument* pDoc, EmbedFontMode embedMode, bool bNotEmbSysFonts, bool bProtect)
{
	return false;
}

STDIMP KConApplication::EnterIoTask(UINT breakMode, ThreadLiteLib::IThreadpoolSink* sink)
{
	return S_OK;
}

STDIMP KConApplication::LeaveIoTask(bool isBroken)
{
	return S_OK;
}

STDIMP_(bool) KConApplication::IsLastIOBroken()
{
	return false;
}

STDIMP_(int) KConApplication::ShowMessageBox(MessageBoxParameterBase* pParameter) 
{
	return 0;
}

STDIMP_(QColor) KConApplication::GetColorFromTheme(const QString& className, const QString& propName, const QColor& def)
{
	return QColor();
}

STDIMP_(QFont) KConApplication::GetFontFromTheme(const QString& className, const QString& propName, const QFont& def)
{
	return QFont();
}

STDIMP_(int) KConApplication::GetHintFromTheme(const QString& className, const QString& propName, int def, bool* pOk)
{
	return 0;
}

STDIMP_(QIcon) KConApplication::GetIcon(const QString& name)
{
	return QIcon();
}

STDIMP_(bool) KConApplication::LoadApiModule()
{
	return KEntrys::getInstance()->loadApi();
}
//////////////////////////////////////////////////////////////////////////
}

}
