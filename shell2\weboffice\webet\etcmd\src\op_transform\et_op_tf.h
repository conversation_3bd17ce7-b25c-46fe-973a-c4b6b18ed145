﻿#ifndef __ET_OP_TF_H__
#define __ET_OP_TF_H__
#include <public_header/revision/src/kwrevisionctrl.h>
#include "operation.h"

namespace wo
{
class KEtOpTrsfmt : public OpTransformation
{
	typedef void(KEtOpTrsfmt::*TransformFunc)();

public:
	KEtOpTrsfmt();

	~KEtOpTrsfmt()
	{
		clear();
	}

	virtual bool Transformation(IRevisionContext* ctx, KwTasks& tasks, TaskContext* tskCtx) override;
	virtual bool Transformation_Reverser(IRevisionContext* ctx, KwTasks& tasks, TaskContext* tskCtx) override;	

	typedef std::unordered_map<IDX, VecOp*> SheetOps;

protected:
	void clear();
	AbsObject* GetMainObj(KwCommand*, WebName objidName, WebName idxName, WebName stIdName);
	VecOp* GetMainOps(AbsObject* mainObj, bool bReverser);
	bool TransTask(KwTask* tsk, bool bReverser);
	template <typename TransformOp>
	bool TransTaskInner(VecOp& vecOpCl, VecOp* vecOpSv, bool bReverser);
	bool TransFormular(OpBase* opCl, bool bReverser);
	bool TransFormulars(OpBase* opCl, bool bReverser);
	void ColectMainOps(const_token_ptr pTk,  SheetOps& res, bool bReverser);
	void ColectAllMainOps(std::vector<exec_token_vector> & tokVecs,  SheetOps& res, bool bReverser);
	bool TransformationInner(IRevisionContext* ctx, KwTasks& tasks, TaskContext* tskCtx, bool bReverser);
	bool adjustIdx(VecOp& opCl);
	bool adjustSheetIdx(VecOp& opCl);
	bool updateShapeIdxPath(VecOp& opCl);
	bool isSheetsLevelOp(KwTask* tsk);

	std::unordered_map<AbsObject*, VecOp*> m_mainOpsMap;
	TaskContext* m_ctx;
	OpEnv m_opEnv;
};

}

#endif // __ET_OP_TF_H__
