﻿#include "etstdafx.h"
#include "db_field_recognize_helper.h"
#include "uilogic/et_uilogic_basic_ift.h"
#include "uilogic/et_uilogic_global.h"
#include "kfc/et_numfmt_str.h"
#include "persist/persist_helper.h"

enum DBNumFmtType
{
	Num1,
	Num14,
	Num16,
	Num18,
	<PERSON>um24,
	<PERSON>um26,
	NumDouble,
	NUmPer,
	Date1,
	Date24,
	Euro,
	Euro2,
	Pound,
	Pound2
};

static PCWSTR getNumFmt(DBNumFmtType type)
{
	static const std::vector<PCWSTR> dbNUmFmtArr = {
		__X("0_ "),
		kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_NUM14), /* "￥#,##0;￥-#,##0" */
		kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_NUM16),
		kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_NUM18), /* "0%" */
		kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_NUM24), /* "$#,##0_);($#,##0)" */
		kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_NUM26),
		__X("0.0000_ "),
		__X("0.0000%"),
		kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_DATE1), /* "yyyy/m/d" */
		kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_DATE24), /* "h:mm" */
		__X("\"€\"#,##0;\"€\"-#,##0"),
		__X("\"€\"###0.00_);(\"€\"-#,##0.00)"),
		__X("£#,##0;£-#,##0"),
		__X("£###0.00_);(£-#,##0.00)")
	};
	return dbNUmFmtArr[type];
}

static bool checkEmail(const QString& email)
{
	static const QRegularExpression rxEmail(R"(^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$)");
	return email.contains(rxEmail);
}

static bool checkPhone(const QString& phone)
{
	static const QRegularExpression rxPhone("^(([+＋]86)|([(（][+＋]86[)）]))?1[3-9]\\d([-‐―−－  ­⁠　]{0,1}\\d{4}){2}$");
	return phone.contains(rxPhone);
}

namespace wo
{
	DBFieldTypeRecHelper::DBFieldTypeRecHelper()
	{
		VS(_applogic_CreateObject(CLSID_KNumberFormatter, IID_IET_NumberFormatter, (void**)&m_spNumberFormatter));
	}

	DBFieldTypeRecHelper::DBFieldTypeRecHelper(IET_NumberFormatter* pEtNumberFormatter)
	{
		ASSERT(pEtNumberFormatter);
		m_spNumberFormatter = pEtNumberFormatter;
	}

	void DBFieldTypeRecHelper::begin()
	{
		m_type = ET_DbSheet_FieldType_Invalid;
		m_numFmt.resetStr(nullptr);
		m_prec = 0;
		m_isRepeated = false;
		m_hasList = false;
		m_bSelectOption = true;
		m_selectSet.clear();
	}

	HRESULT DBFieldTypeRecHelper::addToken(const_token_ptr pToken, PCWSTR format, ET_DbSheet_FieldType& type, bool isContact)
	{
		if (getOptimalType() == Et_DbSheetField_MultiLineText)
			return S_OK;
		if (!pToken || !format)
			return S_CONTINUE;
		updateSelectOptionInfo(pToken, isContact, type);
		if (type != ET_DbSheet_FieldType_Invalid)
		{
			calcType(type, format, 0);
			return m_type != Et_DbSheetField_MultiLineText ? S_CONTINUE : S_OK;
		}
		HRESULT hr = S_OK;
		PCWSTR numFmt = nullptr;
		int precision = 0;
		switch (etexec::const_token_assist(pToken).major_type())
		{
			case etexec::ETP_VINT:
			case etexec::ETP_VDBL:
				hr = getTypeFormatDouble(pToken, format, type, numFmt, precision);
				break;
			case etexec::ETP_VSTR:
			{
				PCWSTR val = etexec::const_vstr_token_assist(pToken).get_value();
				if (!val)
					break;
				QString qStrVal = QString::fromUtf16((val));
				type = Et_DbSheetField_MultiLineText;
				ks_bstr bstrCellValue(val);
				if (checkEmail(qStrVal))
					type = Et_DbSheetField_Email;
				else if (CheckAutoFixHypeLink(bstrCellValue, nullptr))
					type = Et_DbSheetField_Url;
				else if (checkPhone(qStrVal))
					type = Et_DbSheetField_Phone;
				break;
			}
			case etexec::ETP_NONE:
				break;
			case etexec::ETP_VBOOL:
			case etexec::ETP_ERROR:
				type = Et_DbSheetField_MultiLineText;
				break;
            case etexec::ETP_HANDLE:
                if (alg::const_handle_token_assist(pToken).get_handleType() == alg::ET_HANDLE_QRLABEL)
                    type = Et_DbSheetField_MultiLineText;
                else
                    return E_FAIL;
                break;
			default:
				return E_FAIL; // 拿到不符合预期的意外类型token
		}
		if (FAILED(hr))
			return hr;
		calcType(type, numFmt, precision);
		if (getOptimalType() != Et_DbSheetField_MultiLineText)
			return S_CONTINUE;
		return S_OK;
	}

	void DBFieldTypeRecHelper::end()
	{
		if (m_type == ET_DbSheet_FieldType_Invalid)
			m_type = Et_DbSheetField_MultiLineText;
		m_type = getOptimalType();
		// 当有重复的才能转为单选项， 当选项个数大于1个才能转为单选项
		if (!m_hasList && (!m_isRepeated || m_selectSet.size() < 2) && (m_type == Et_DbSheetField_SingleSelect || m_type == Et_DbSheetField_MultipleSelect))
			m_type = Et_DbSheetField_MultiLineText;
		calcNumFmt();
	}

	void DBFieldTypeRecHelper::GetTypeInfo(ET_DbSheet_FieldType& type, PCWSTR& numFmt) const
	{
		type = m_type;
		numFmt = m_numFmt.c_str();
	}

	void DBFieldTypeRecHelper::addToSelectSet(PCWSTR val, bool isContact)
	{
		if (!val)
			return;
		if (isContact && val[0] == __Xc('@'))
			++val;
		if (!m_isRepeated && m_selectSet.find(GlobalSharedString(val)) != m_selectSet.end())
			m_isRepeated = true;
		if (val[0])
			m_selectSet.insert(GlobalSharedString(val));
	}

	void DBFieldTypeRecHelper::updateSelectOptionInfo(const_token_ptr pToken, bool isContact, ET_DbSheet_FieldType type)
	{
		ASSERT(pToken);
		if (!m_bSelectOption)
			return;
		switch (etexec::const_token_assist(pToken).major_type())
		{
			case etexec::ETP_VINT:
			case etexec::ETP_VDBL:
			{
				double val = etexec::const_vdbl_token_assist(pToken).get_value();
				constexpr int hex = 16;
				QString qstrVal = QString::number(val, 'g', hex);
				PCWSTR strVal = krt::utf16(qstrVal);
				// 已识别为单(多)选项时，需要将数字的值加入到选项列表中
				if (type == Et_DbSheetField_SingleSelect || type ==  Et_DbSheetField_MultipleSelect)
					addToSelectSet(strVal, false);
				else if (m_selectSet.find(GlobalSharedString(strVal)) == m_selectSet.end()) // 当前数字不在选项列表中，不识别为单选项
					m_bSelectOption = false;
				return;
			}
			case etexec::ETP_VSTR:
			{
				constexpr int maxSelectCnt = 10;
				PCWSTR val = etexec::const_vstr_token_assist(pToken).get_value();
				if (!val)
					return;
				if (type == Et_DbSheetField_SingleSelect)
				{
					addToSelectSet(val, isContact);
					return;
				}
				else if (type == Et_DbSheetField_MultipleSelect)
				{
					for (int i = 0, cur = 0; val[i] != 0; ++i)
					{
						WCHAR next = val[i + 1];
						if (next == 0 || next == __Xc(','))
						{
							if (i >= cur)
							{
								int len = i + 1 - cur;
								et_saptr<WCHAR> spSliceStr(new WCHAR[len + 1]);
								*(spSliceStr + len) = __Xc('\0');
								ks_memcpy_s(spSliceStr, len * sizeof(WCHAR), val + cur, len * sizeof(WCHAR));
								addToSelectSet(spSliceStr.get(), false);
							}
							cur = i + 2;
						}
					}
					return;
				}
				if (m_selectSet.size() >= maxSelectCnt)
				{
					m_bSelectOption = false;
					return;
				}
				addToSelectSet(val, isContact);
				return;
			}
			case etexec::ETP_NONE:
				return;
			case etexec::ETP_VBOOL:
			case etexec::ETP_ERROR:
			default:
				m_bSelectOption = false;
				break; // 拿到不符合预期的意外类型token
		}
	}

	HRESULT DBFieldTypeRecHelper::getTypeFormatDouble(const_token_ptr pToken, PCWSTR format, ET_DbSheet_FieldType& type, PCWSTR& numFmt, int& precision)
	{
		static const QRegularExpression rxDecimal("[\\.]"); // 包含小数点
		static const QRegularExpression rxRMB("[￥元]"); // 包含￥,元
		static const QRegularExpression rxDollar("[\\$]"); // 包含$
		static const QRegularExpression rxEuro("[€]"); // 包含€
		static const QRegularExpression rxPound("[£]"); // 包含£
		static const QRegularExpression rxDate("[yda]"); // 包含y/d/a
		static const QRegularExpression rxTime("[hs]"); // 包含h/s
		static const QRegularExpression rxPercentage("[%]"); // 包含%

		ASSERT(pToken && format);
		double val = etexec::const_vdbl_token_assist(pToken).get_value();
		constexpr int hex = 16;
		QString qstrVal = QString::number(val, 'g', hex);
		// 将手机号码样式的数字识别为手机号码
		if (checkPhone(qstrVal))
		{
			type = Et_DbSheetField_Phone;
			return S_OK;
		}

		NUMFMT_CAT_INFO numberFormatInfo{};
		VS(m_spNumberFormatter->GetCatInfo(format, &numberFormatInfo));
		QString numfmtStr = QString::fromUtf16(format);
		switch (numberFormatInfo.cat)
		{
			case NFCat_General:     // 常规
			case NFCat_Fraction:    // 分数
			case NFCat_Scientific:  // 科学计数法
			case NFCat_Text:        // 文本
			{
				type = Et_DbSheetField_Number;
				numFmt = getNumFmt(Num1);
				// 判断是否包含非零的小数部分
				if (std::trunc(val) != val)
					precision = 4;
				break;
			}
			case NFCat_Number:      // 数值
			{
				type = Et_DbSheetField_Number;
				numFmt = getNumFmt(Num1);
				// 判断数字格式是否包含小数点
				if (numfmtStr.contains(rxDecimal))
					precision = 4;
				break;
			}
			case NFCat_Currency:    // 货币
			case NFCat_Accounting:  // 会计专用
			{
				type = Et_DbSheetField_Currency;
				numFmt = getNumFmt(Num14);
				precision = 0;
				if (numfmtStr.contains(rxDollar))
					numFmt = getNumFmt(Num24);
				else if (numfmtStr.contains(rxEuro))
					numFmt = getNumFmt(Euro);
				else if (numfmtStr.contains(rxPound))
					numFmt = getNumFmt(Pound);
				if (numfmtStr.contains(rxDecimal))
					precision = 2;
				break;
			}
			case NFCat_Date:        // 日期
			{
				type = Et_DbSheetField_Date;
				numFmt = format;
				precision = 0;
				break;
			}
			case NFCat_Time:        // 时间
			{
				type = Et_DbSheetField_Time;
				numFmt = format;
				precision = 0;
				break;
			}
			case NFCat_Percentate:  // 百分比
			{
				type = Et_DbSheetField_Percentage;
				numFmt = getNumFmt(Num18);
				precision = 0;
				if (numfmtStr.contains(rxDecimal))
					precision = 4;
				break;
			}
			case NFCat_Custom:      // 自定义
			{
				numFmt = getNumFmt(Num1);
				type = Et_DbSheetField_Number;
				precision = 0;
				if (numfmtStr.contains(rxDollar))
				{
					numFmt = getNumFmt(Num24);
					type = Et_DbSheetField_Currency;
				}
				else if (numfmtStr.contains(rxEuro))
				{
					numFmt = getNumFmt(Euro);
					type = Et_DbSheetField_Currency;
				}
				else if (numfmtStr.contains(rxPound))
				{
					numFmt = getNumFmt(Pound);
					type = Et_DbSheetField_Currency;
				}
				else if (numfmtStr.contains(rxRMB))
				{
					numFmt = getNumFmt(Num14);
					type = Et_DbSheetField_Currency;
				}
				else if (numfmtStr.contains(rxDate))
				{
					numFmt = format;
					type = Et_DbSheetField_Date;
				}
				else if (numfmtStr.contains(rxTime))
				{
					numFmt = format;
					type = Et_DbSheetField_Time;
				}
				else if (numfmtStr.contains(rxPercentage))
				{
					numFmt = getNumFmt(Num18);
					type = Et_DbSheetField_Percentage;
				}
				if (numfmtStr.contains(rxDecimal))
					precision = type == Et_DbSheetField_Currency ? 2 : 4;
				break;
			}
			case NFCat_Special:     // 特殊. 中文的万元等会出现在此处
			{
				numFmt = getNumFmt(Num1);
				type = Et_DbSheetField_Number;
				precision = 0;
				if (numfmtStr.contains(rxRMB))
				{
					numFmt = getNumFmt(Num14);
					type = Et_DbSheetField_Currency;
				}
				if (numfmtStr.contains(rxDecimal))
					precision = 2;
				break;
			}
			default:
				return E_FAIL;
		}
		return S_OK;
	}

	void DBFieldTypeRecHelper::calcType(ET_DbSheet_FieldType type, PCWSTR numFmt, int precision)
	{
		if (type == ET_DbSheet_FieldType_Invalid) // 字段类型解析未生效. 跳过
			return;
		if (m_type == ET_DbSheet_FieldType_Invalid) // 分析结果的首次初始化
		{
			m_type = type;
			m_numFmt.resetStr(numFmt);
			m_prec = precision;
			return;
		}
		// 同时存在多选和单选的时候，默认设为多选
		if (m_type == Et_DbSheetField_MultipleSelect && type == Et_DbSheetField_SingleSelect)
		{
			return;
		}
		else if (m_type == Et_DbSheetField_SingleSelect && type == Et_DbSheetField_MultipleSelect)
		{
			m_type = Et_DbSheetField_MultipleSelect;
			return;
		}
		if (m_type != type) // 解析得到的字段类型不一致, 则选取为兼容它们的格式. 目前统一采用多行文本类型
		{
			m_type = Et_DbSheetField_MultiLineText;
			return;
		}
		if (!m_numFmt.get() && numFmt) // 格式的首次初始化
			m_numFmt.resetStr(numFmt);
		// 进行精度的比较. 采用大的精度值.
		if (m_prec < precision)
			m_prec = precision;
		if (m_numFmt == GlobalSharedString(numFmt))
			return;
		if (m_type == Et_DbSheetField_Currency) // 对货币类型, 要求币种(格式)一致
		{
			m_type = Et_DbSheetField_MultiLineText;
			return;
		}
		// 校验日期/时间的格式不一致, 则应用默认格式
		if (m_type == Et_DbSheetField_Date)
			m_numFmt.resetStr(getNumFmt(Date1));
		if (m_type == Et_DbSheetField_Time)
			m_numFmt.resetStr(getNumFmt(Date24));
	}

	ET_DbSheet_FieldType DBFieldTypeRecHelper::getOptimalType() const
	{
		if (m_type != Et_DbSheetField_MultiLineText)
			return m_type;
		if (m_bSelectOption)
			return Et_DbSheetField_SingleSelect;
		return Et_DbSheetField_MultiLineText;
	}

	void DBFieldTypeRecHelper::calcNumFmt()
	{
		switch (m_type)
		{
			case Et_DbSheetField_Email:
			case Et_DbSheetField_MultiLineText:
			case Et_DbSheetField_Phone:
			case Et_DbSheetField_SingleSelect:
			case Et_DbSheetField_Url:
				m_numFmt.resetStr(nullptr);
				return;
			default:
				break;
		}
		if (m_prec == 0)
			return;
		if (m_prec == 2)
		{
			if (m_numFmt == GlobalSharedString(getNumFmt(Num14)))
				m_numFmt.resetStr(getNumFmt(Num16));
			else if (m_numFmt == GlobalSharedString(getNumFmt(Num24)))
				m_numFmt.resetStr(getNumFmt(Num26));
			else if (m_numFmt == GlobalSharedString(getNumFmt(Euro)))
				m_numFmt.resetStr(getNumFmt(Euro2));
			else if (m_numFmt == GlobalSharedString(getNumFmt(Pound)))
				m_numFmt.resetStr(getNumFmt(Pound2));
			return;
		}
		if (m_prec == 4)
		{
			if (m_numFmt == GlobalSharedString(getNumFmt(Num1)))
				m_numFmt.resetStr(getNumFmt(NumDouble));
			else if (m_numFmt == GlobalSharedString(getNumFmt(Num18)))
				m_numFmt.resetStr(getNumFmt(NUmPer));
		}
	}
} // namespace wo
