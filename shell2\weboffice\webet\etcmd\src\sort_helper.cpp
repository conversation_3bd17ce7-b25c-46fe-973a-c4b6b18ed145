﻿#include "etstdafx.h"
#include "sort_helper.h"
#include "helpers/autofilter_helper.h"

#ifndef X_OS_WINDOWS
#include "appcore/et_appcore_helper.inl"
#endif

HRESULT ExpandRangeInWorksheet(
	IN IKWorksheet* pWorksheet,
	IN etoldapi::Sort* ptrSort,
	IN IKRanges* pSelection,
	IN bool bExpandRange,
	OUT IKRanges** ppSortRange,
	OUT bool& bTipExpand,
	OUT bool& bSortWithoutExpand)
{
	KCOMPTR(ISheet) ptrSheet = pWorksheet->GetSheet();
	KCOMPTR(_Worksheet) ptrWorksheet = pWorksheet;

	const RANGE* pRange = NULL;
	pSelection->GetItem(0, 0, &pRange);
	RANGE rgSortArea(*pRange);
	RANGE rgSortAreaOld(*pRange);

	// 如果是单个单元格，能扩展的就扩展
	BOOL bExpanded = FALSE;
	CELL cellSingle = {0};
	BOOL bOldSelectionSingleCell =
		app_helper::IsSingleCell(pSelection, &cellSingle, ptrSheet);
	BOOL bOldSelectionSingleRowCol = IsSingleRowCol(rgSortArea);

	if (bExpandRange)
	{
		if (bOldSelectionSingleCell)
		{
			//扩展区域
			bExpanded = ExpandDataRange(pWorksheet, cellSingle, rgSortArea);
		}
		else // 不是单个单元格
		{
			bExpanded = ExpandDataRange(pWorksheet, RANGE(rgSortArea), rgSortArea);
		}
	}

	// 若最初不是单元格则需要判断扩展，若是单元格直接扩展不用提示 
	bTipExpand = false;
	if (!bOldSelectionSingleCell && bOldSelectionSingleRowCol)
	{
		if (bExpanded)
		{
			bTipExpand = true;
		}
	}

	////////////////////////////////////////////
	// 裁剪用户选择区域，去掉无效的排序区域
	////////////////////////////////////////////

	// 和UsedRange求交
	RECT rcUsed = {0};
	VS(ptrSheet->CalcUsedScale(&rcUsed));
	RANGE rgUsed(rgSortArea);
	rgUsed.SetRowFromTo(rcUsed.top, rcUsed.bottom);
	rgUsed.SetColFromTo(rcUsed.left, rcUsed.right);
	rgSortArea = rgSortArea.Intersect(rgUsed);

	ks_stdptr<IBook> ptrBook;
	ks_stdptr<IBookOp> ptrBookOp;
	VS(ptrSheet->GetBook(&ptrBook));
	VS(ptrBook->GetOperator(&ptrBookOp));

	if ((!rgSortArea.IsValid()) ||
		(IsSingleCell(ptrWorksheet, rgSortArea) && 
		appcore_helper::IsNULLCell(ptrBookOp, rgSortArea.SheetFrom(), rgSortArea.RowFrom(), rgSortArea.ColFrom())))
	{
		return E_FAIL;
	}

	//不扩展的话，能否排序
	bSortWithoutExpand = true;
	rgSortAreaOld = rgSortAreaOld.Intersect(rgUsed);
	if ((!rgSortAreaOld.IsValid()) ||
		(IsSingleCell(ptrWorksheet, rgSortAreaOld) && 
		appcore_helper::IsNULLCell(ptrBookOp, rgSortAreaOld.SheetFrom(), rgSortAreaOld.RowFrom(), rgSortAreaOld.ColFrom())))
	{
		bSortWithoutExpand = false;
	}

	// #199903，单元格扩展之后的区域，需要裁掉边界上的空单元格行列
	if (bExpanded && bOldSelectionSingleCell)
		EraseEmptyRowColInRange(ptrWorksheet, rgSortArea);

	{
		ks_stdptr<IKSortData> ptrSortData;
		ks_stdptr<ISortInfo> ptrSortInfo = ptrSort;
		ptrSortInfo->GetSortData(&ptrSortData);

		// #203809, 如果是第一次排序，并且文件中记录的排序区域与本次排序区域相同，则认为没有标题行，否则需要重新判断标题行
		if (//todo 临时改动：让这里默认当做第一次排序处理。
			//!ptrSortInfo->GetHasSorted() &&
			ptrSortData->GetParentType() == spt_Worksheet)
		{
			const RANGE* pRgInFile = NULL;
			HRESULT hr = ptrSortData->GetRange(&pRgInFile);
			hr = E_FAIL;//todo 临时改动：让这里取不到保存的排序区域。因为排序时 ptrSort->SetRange(ptrRgSort); 排序区域没有成功保存进去。
			if (SUCCEEDED(hr) && (rgSortArea == *pRgInFile))
				ptrSortData->SetHeaderCount(0);
			else
				ptrSortData->SetHeaderCount(-1);
		}

		// 如果之前没有判断过标题行，并且是工作表排序，并且是通过扩展得到的排序区域，需要检查并去掉多余的标题行
		if (ptrSortData->GetHeaderCount() < 0 && 
			ptrSortData->GetParentType() == spt_Worksheet &&
			bExpanded)
		{
			EraseReduplicateHeaderRow(ptrWorksheet, rgSortArea);
		}

		// 如果上次排序包含标题行，并且本次选择区域与上次排序时相同，需要扩展一行标题行
		if (ptrSortData->GetHasHeader() &&
			ptrSortData->GetParentType() == spt_Worksheet)
		{
			const RANGE* pLastRg = NULL;
			if (SUCCEEDED(ptrSortData->GetRange(&pLastRg)))
			{
				RANGE rgLastSel(*pLastRg);
				rgLastSel.SetRowFrom(rgLastSel.RowFrom() + 1);
				if ((rgLastSel == rgSortArea) && (rgSortArea.RowFrom() > 0))
				{
					rgSortArea.SetRowFrom(rgSortArea.RowFrom() - 1);
				}
			}
		}
	}

	ks_stdptr<IKRanges> ptrRgsSort;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&ptrRgsSort);
	ptrRgsSort->Append(0, rgSortArea);

	if (ppSortRange) (*ppSortRange) = ptrRgsSort.detach();

	return S_OK;
}

HRESULT ExpandRangeInListObject(
	IKWorksheet* pWorksheet,
	etoldapi::Sort* pSort,
	IKRanges* pSelection,
	IKRanges** ppSortRange)
{
	ks_stdptr<ISortInfo> spInfo = pSort;
	ks_stdptr<IKSortData> spSortData;
	spInfo->GetSortData(&spSortData);
	const RANGE* pRg = NULL;
	spSortData->GetRange(&pRg);
	RANGE rgSortArea(*pRg);

	ks_stdptr<IKRanges> ptrRgsSort;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&ptrRgsSort);
	ptrRgsSort->Append(0, rgSortArea);

	if (ppSortRange) 
		(*ppSortRange) = ptrRgsSort.detach();
	return S_OK;
}


bool ExpandDataRange(
	IN IKWorksheet* pWorksheetInfo,
	IN const CELL& cell,
	OUT RANGE& rgNew)
{
	ks_stdptr<ISheet> ptrSheet = pWorksheetInfo->GetSheet();
	appcore_helper::GetContinualRangeMax(
		ptrSheet,
		cell.row,
		cell.col,
		&rgNew
		);

	RANGE rgFilter(ptrSheet->GetBMP());
	if (GetFilterDatabaseRange(ptrSheet, &rgFilter) &&
		rgNew.Intersect(rgFilter).IsValid())
	{
		RANGE rg = rgNew;
		rg.SetRowFrom(rgFilter.RowFrom());
		if (rg.IsValid())
			rgNew = rg;
	}

	return !IsSingleCell(pWorksheetInfo, rgNew);
}

bool ExpandDataRange(
	IN IKWorksheet* pWorksheetInfo,
	IN const RANGE& rgSingleCol,
	OUT RANGE& rgNew)
{
	if (!IsSingleRowCol(rgSingleCol)) return false;

	ks_stdptr<ISheet> ptrSheet = pWorksheetInfo->GetSheet();
	appcore_helper::GetContinualRangeMax(ptrSheet, rgSingleCol, &rgNew);

	RANGE rgFilter(ptrSheet->GetBMP());
	if (GetFilterDatabaseRange(ptrSheet, &rgFilter) &&
		rgNew.Intersect(rgFilter).IsValid())
	{
		RANGE rg = rgNew;
		rg.SetRowFrom(rgFilter.RowFrom());
		if (rg.IsValid())
			rgNew = rg;
	}

	if (IsSingleRowCol(rgNew)) rgNew = rgSingleCol;
	return !rgSingleCol.Compare(rgNew);
}

BOOL IsSingleCell(IKWorksheet* pWorksheet, const RANGE& rg)
{
	range_helper::ranges rgs = range_helper::ranges::create_instance();
	rgs.add(alg::STREF_THIS_BOOK, rg);
	return app_helper::IsSingleCell(rgs, NULL, pWorksheet->GetSheet());
}

void EraseReduplicateHeaderRow(
	IN IKWorksheet* pWorksheetInfo,
	IN OUT RANGE& rg)
{
	range_helper::ranges rgs;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&rgs);
	rgs.add(alg::STREF_THIS_BOOK, rg);

	ks_stdptr<Range> ptrRange;
	pWorksheetInfo->GetRangeByData(rgs, &ptrRange);

	INT nRows = app_helper::GuessRangeSortHeader(ptrRange, FALSE, FALSE, FALSE);

	// 标题行多于一行，进行裁剪。
	if (1 < nRows)
	{
		rg.SetRowFrom(rg.RowFrom() + nRows - 1);
	}

	return;
}

void EraseEmptyRowColInRange(
	IN IKWorksheet* pWorksheetInfo,
	IN OUT RANGE& rg)
{
	for (ROW r = rg.RowFrom(); r <= rg.RowTo(); ++r)
	{
		RANGE rowRg(rg);
		rowRg.SetRowFromTo(r);
		if (IsRangeEmpty(pWorksheetInfo, rowRg) && r < rg.RowTo())
			rg.SetRowFrom(r + 1);
		else
			break;
	}
	for (ROW r = rg.RowTo(); r >= rg.RowFrom(); --r)
	{
		RANGE rowRg(rg);
		rowRg.SetRowFromTo(r);
		if (IsRangeEmpty(pWorksheetInfo, rowRg) && r > rg.RowFrom())
			rg.SetRowTo(r - 1);
		else
			break;
	}
	for (COL c = rg.ColFrom(); c <= rg.ColTo(); ++c)
	{
		RANGE colRg(rg);
		colRg.SetColFromTo(c);
		if (IsRangeEmpty(pWorksheetInfo, colRg) && c < rg.ColTo())
			rg.SetColFrom(c + 1);
		else
			break;
	}
	for (COL c = rg.ColTo(); c >= rg.ColFrom(); --c)
	{
		RANGE colRg(rg);
		colRg.SetColFromTo(c);
		if (IsRangeEmpty(pWorksheetInfo, colRg) && c > rg.ColFrom())
			rg.SetColTo(c - 1);
		else
			break;
	}
}

BOOL GetFilterDatabaseRange(ISheet* pSheet, RANGE* pRg)
{
	ks_stdptr<IBook> spBook;
	ks_stdptr<IBookOp> spBookOp;
	IDX iSheet = -1;
	pSheet->GetIndex(&iSheet);
	pSheet->GetBook(&spBook);
	spBook->GetOperator(&spBookOp);
	ks_stdptr<IKRanges> spRanges;

	CS_COMPILE_PARAM prm(cpfSysDefault | cpfAllowName, iSheet, 0, 0);
	HRESULT hr = spBookOp->CompileRange(__X("_FilterDatabase"), prm, &spRanges, croNeedCalc);

	UINT nCount = 0;
	if (spRanges)
		spRanges->GetCount(&nCount);
	if (SUCCEEDED(hr) && nCount == 1)
	{
		const RANGE* pRgFilter = NULL;
		spRanges->GetItem(0, NULL, &pRgFilter);

		if (pRgFilter->IsValid())
		{
			*pRg = *pRgFilter;
			// _FilterDatabase 会截取合并单元格的最后一个单元格
			pSheet->MergeExpandComplete(*pRg);
			return TRUE;
		}
	}

	return FALSE;
}

BOOL IsRangeEmpty(IKWorksheet* pWorksheet, const RANGE& rg)
{
	ISheet* pSheet = pWorksheet->GetSheet();
	ks_stdptr<IBook> ptrBook;
	pSheet->GetBook(&ptrBook);
	ks_stdptr<IBookOp> ptrBookOp;
	ptrBook->GetOperator(&ptrBookOp);

	for (ROW r = rg.RowFrom(); r <= rg.RowTo(); ++r)
	{
		for (COL c = rg.ColFrom(); c <= rg.ColTo(); ++c)
		{
			CELL cell = {r, c};
			RANGE merge(rg);
			app_helper::GetMergeCell(pWorksheet, cell, merge);

			const_token_ptr token;
			ptrBookOp->GetCellValue(rg.SheetFrom(), merge.RowFrom(), merge.ColFrom(), &token);
			if (token != NULL)
				return FALSE;
		}
	}
	return TRUE;
}

BOOL GetSortByRange(_Worksheet* pSheet, const RANGE& rg, bool onlyEqual, etoldapi::Sort** ppSort, bool *pIsListObject)
{
	BOOL bFilterSort = FALSE;
	ks_stdptr<AutoFilter> spFilter;
	ks_stdptr<ListObject> spListObject;
	bool isListObject = AutoFilterHelper::GetFilterByRange(pSheet, NULL, rg, &spFilter, &spListObject);

	if (spFilter)
	{
		ks_stdptr<Range> spFilterRange;
		spFilter->get_Range(&spFilterRange);
		ks_stdptr<IKRanges> spFilterRgs;
		app_helper::GetIRanges(spFilterRange, &spFilterRgs);
		const RANGE* spFilterRg = NULL;
		spFilterRgs->GetItem(0, 0, &spFilterRg);

		if (onlyEqual)
		{
			if(spFilterRg->Compare(rg) && spFilterRg->Height() > 1)
			{
				bFilterSort = TRUE;
			}
		} 
		else 
		{
			if(spFilterRg->Intersect(rg).IsValid())
			{
				bFilterSort = TRUE;
			}
		}
	}
	if (bFilterSort)
		spFilter->get_Sort(ppSort);
	else
	{
		if (spListObject)
			spListObject->get_Sort(ppSort);
		else
			pSheet->get_Sort(ppSort);
	}

	if (pIsListObject)
		*pIsListObject = isListObject;
	return bFilterSort;
}