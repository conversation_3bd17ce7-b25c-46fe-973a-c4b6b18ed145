﻿#include "etstdafx.h"
#include "et_form_http_task_class.h"
#include "et_revision_context_impl.h"
#include "serialize_impl.h"
#include "workbook.h"
#include "util.h"
#include "kso/l10n/et/etshell.h"

namespace wo
{

#define WO_FORM_AREA_NAME_PREFIX  __X("WebWps_Form")

void FormHelp::GetNameRg(IBookOp* pBookOp, IDX iName, RANGE& rg)
{
	rg.Invalidate();
	ks_stdptr<IFormula> spFmla;
	pBookOp->GetDefinedNameContent(iName, &spFmla);
	if (!spFmla)
		return;

	BOOL bFmla = FALSE;
	ks_stdptr<ITokenVectorInstant> spVec;
	spFmla->GetContent(&bFmla, &spVec, NULL);
	if(!bFmla)
		return;

	int size = 0;
	spVec->GetSize(&size);
	if(size != 1)
		return;

	const_token_ptr pTok = NULL;
	spVec->GetItem(0, &pTok);
	if(!alg::const_stref_token_assist::is_type(pTok))
		return;

	alg::const_stref_token_assist rcs(pTok);
	if (rcs.get_sheet_id() < 0)	// 相对sheet引用
		return;

	rg.FromStrefToken(pTok);
	if (!rg.IsValid())
	{
		//只有sheet有效时需要知道
		rg.SetSheetFromTo(rcs.get_sheet_id());
	}
}

void FormHelp::SetNameRg(IBookOp* pBookOp, IDX iName, const RANGE& rg)
{
    alg::managed_stref_token_assist rms;
	rms.create(0, 0);
	rms.make_region(0, rg.SheetFrom(), rg.SheetFrom(), rg.RowFrom(), rg.RowTo(), rg.ColFrom(), rg.ColTo(), true, true, true, true, true);
	rms.set_cross_sheet(true);
	exec_token_vector etv = exec_token_vector::create_instance(TRUE);
	etv.add_manage(rms.detach());

	ks_stdptr<IFormula> spFmla;
	pBookOp->CreateFormula(&spFmla);
	spFmla->SetFormulaContent(etv);
	VS(pBookOp->SetDefinedNameContent(iName, spFmla));
}

HRESULT FormHelp::GetFormRg(IBook* pBook, KEtRevisionContext* ctx, RANGE& rg,const ks_wstring& areaName)
{
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);
	IDX iName = alg::STREF_INV_NAME;
	spBookOp->FindDefinedName(alg::STREF_NO_SHEET, areaName.c_str(), &iName);
	if(iName == alg::STREF_INV_NAME)
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_ERROR, "form name not found");
		return E_FAIL;
	}

	GetNameRg(spBookOp, iName, rg);
	if(!rg.IsValid())
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_ERROR, "form range invalid");
		return E_FAIL;
	}
	return S_OK;
}

ks_wstring FormHelp::getAreaName(binary_wo::VarObj obj)
{
	ks_wstring strAreaName = WO_FORM_AREA_NAME_PREFIX;
	if (obj.has("area_name"))
	{
		ks_wstring formId = obj.field_str("area_name");
		if(!formId.empty())
			strAreaName = strAreaName + __X("_") + formId;
	}
	return strAreaName;
}

// ================== EtFormHttpAddSheetTaskClass ==================
EtFormHttpAddSheetTaskClass::EtFormHttpAddSheetTaskClass(KEtWorkbook* wwb)
        : EtHttpTaskClassBase(wwb)
{
}

HRESULT EtFormHttpAddSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    CALL_TIME_STAT("TaskExecFormAddSheet");
	util::IgnoreHistoryGuard ignoreHistoryGuard(pCtx);
	binary_wo::VarObj param = cmd->cast().get("param");
	PCWSTR strShtName = nullptr;
	if (param.has("name"))
	{
		strShtName = param.field_str("name");
		if (!alg::IsValidSheetName((strShtName)))
		{
			WO_LOG_X(pCtx->getLogger(), WO_LOG_WARN, "[FORM] invalid sheet name");
			strShtName = et_sWoFormSheetName;
		}
	}
	else
	{
		strShtName = et_sWoFormSheetName;
	}

	ks_stdptr<etoldapi::Worksheets> spSheets;
	m_wwb->GetCoreWorkbook()->get_Worksheets(&spSheets);
	if (!spSheets)
		return E_FAIL;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);

	ks_wstring strAreaName = FormHelp::getAreaName(param);
	IDX iName = alg::STREF_INV_NAME;
	spBookOp->FindDefinedName(alg::STREF_NO_SHEET, strAreaName.c_str(), &iName);
	if(iName == alg::STREF_INV_NAME)
	{
		spBookOp->DefineName(alg::STREF_NO_SHEET, strAreaName.c_str(), DefNameAttr_Normal, &iName);
		if(iName == alg::STREF_INV_NAME)
			return E_FAIL;
	}
	else
	{
		RANGE nameRg(m_wwb->GetBMP());
		FormHelp::GetNameRg(spBookOp, iName, nameRg);
		if (nameRg.IsValid())
		{
			ks_stdptr<IKCoreObject> spObsoleteSheetObj;
			spSheets->get_Item(KComVariant(nameRg.SheetFrom() + 1), &spObsoleteSheetObj);
			ks_stdptr<etoldapi::_Worksheet> spObsoleteSheet = spObsoleteSheetObj;
			if (spObsoleteSheet)
			{
				ks_wstring wsObsoleteSheetName(et_sWoObsoleteFormSheetName);
				QString dateTime = QDateTime::currentDateTime().toStringEx("yyyyMMddhhmmss");
				wsObsoleteSheetName.AppendFormat(__X("%s"), krt::utf16(dateTime));
				ks_bstr bsObsoleteSheetName(wsObsoleteSheetName.c_str());
				for (int count = 1; S_FALSE == spObsoleteSheet->IsValidSheetName(bsObsoleteSheetName); count++)
				{
					ks_wstring strName(wsObsoleteSheetName);
					strName.AppendFormat(__X("(%d)"), count);
					bsObsoleteSheetName.assign(strName.c_str());
				}
				HRESULT hr = spObsoleteSheet->put_Name(bsObsoleteSheetName);
				if (FAILED(hr))
				{
					WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] sheet rename failed");
					return E_INVALIDARG;
				}
			}
		}
	}

	KComVariant vBefore;
	KComVariant vAfter;
	IDX iSheet = util::getLastSheetIdx(m_wwb->GetCoreWorkbook()->GetBook()) + 1;
	vAfter = iSheet;
	KComVariant vCount;
	KComVariant vType;

	ks_stdptr<IKCoreObject> spObj;
	HRESULT hr = spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid);
	if (FAILED(hr))
		return hr;

	ks_stdptr<etoldapi::_Worksheet> spSheet = spObj;
	if (spSheet == NULL)
		return E_FAIL;

	ks_bstr bstrName(strShtName);
	for (int count = 1; S_FALSE == spSheet->IsValidSheetName(bstrName); count++)
	{
		ks_wstring strName(strShtName);
		strName.AppendFormat(__X("(%d)"), count);
		bstrName.assign(strName.c_str());
	}
	hr = spSheet->put_Name(bstrName);
	if (FAILED(hr))
	{
		WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] invalid sheet name");
		return E_INVALIDARG;
	}

	RANGE rg(pBook->GetBMP());
	rg.SetSheetFromTo(iSheet);
	FormHelp::SetNameRg(spBookOp, iName, rg);

	binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, pCtx);
    acpt.addInt32("sheetId", spSheet->GetSheet()->GetStId());
	return S_OK;
}

PCWSTR EtFormHttpAddSheetTaskClass::GetTag()
{
    return __X("http.et.form.addSheet");
}

HRESULT EtFormHttpAddSheetTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	if (pCtx->getProtectionCtx()->isBookHasColProtect())
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_COL_BOOK; // 列保护

	if (pCtx->getProtectionCtx()->isBookHasHiddenProperty())
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE_BOOK; // 区域保护
	return S_OK;
}

} // end namespace wo