#ifndef __WO_CHART_HELPER_H__
#define __WO_CHART_HELPER_H__

namespace chart {
    struct RangeDivideResult;
    enum ChartType ENUM_DECL(int);
};

namespace wo {

class SeriesRangeSubscription;

//chart数据源管理
class RemoteChartLinkManager {
private:
    RemoteChartLinkManager();
    ~RemoteChartLinkManager();

public:
    static RemoteChartLinkManager* getInstance(IBook* pBook = nullptr, int sheetIdx = -1) {
        static RemoteChartLinkManager singleton;
        if (pBook && sheetIdx >= 0) {
            singleton.DoBindSheet(pBook, sheetIdx);
        }
        return &singleton;
    }

public:
    void LinkRemoteChart(int chartType, IKRanges*, IKRanges*, IKRanges*);
    void SetBubbleSize(IKRanges*);
    bool ProcessRangeDivide();
    void NotifySubsriber(const QString& connId, const QString& userId, const QString& cuid);
    bool ExportChartData();

    void DivideByFmla(const QString& nameFmla, const QString& cateFmla, const QString& valFmla);
protected:
    void DoBindSheet(IBook* pBook, int sheetIdx);
    void FillRanges(IKRanges* nameRg, IKRanges* cateRg, IKRanges* valRg);
    bool CheckRangeInPivotTable(const RANGE& range);
    bool CheckNeedDivide(IKRanges** ppRanges);
    RANGE GetBoundary(IKRanges* pRanges, int* pBookId = nullptr);
    bool CheckRange();

    void CompileDivideRanges(const chart::RangeDivideResult& divideResult, const CELL boundary);
private:
    IBook *m_pBook;
    int m_sheetIdx;

    ks_stdptr<IKRanges> m_spNameRgs;
    ks_stdptr<IKRanges> m_spCateRgs;
    ks_stdptr<IKRanges> m_spValueRgs;
    ks_stdptr<IKRanges> m_spBubbleRgs;

    SeriesRangeSubscription* m_pSubsriber;
};

}
#endif