﻿#ifndef __WEBET_EXPORT_AIR_APP_HELPER__
#define __WEBET_EXPORT_AIR_APP_HELPER__ 

//#include "et_revision_context_impl.h"
#include "etstdafx.h"
#include "workbook.h"

class KEtWorkbook;

namespace ExportAirAppHelper
{	
	HRESULT ExecExportAirApp(wo::KEtWorkbook* pWb, ISheet* pAppSheet, wo::KEtRevisionContext* pCtx, const ks_wstring& newSharedId, OUT std::set<ks_wstring>& attachmentIdSet);

} // namespace ExportAirAppHelper

#endif //__WEBET_EXPORT_AIR_APP_HELPER__
