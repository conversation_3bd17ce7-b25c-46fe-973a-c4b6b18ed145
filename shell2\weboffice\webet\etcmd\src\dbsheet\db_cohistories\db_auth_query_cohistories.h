﻿#pragma once

#include "binvariant/binvarobj.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "appcore/et_appcore_dbsheet.h"
#include <unordered_map>

using binary_wo::VarObj;
namespace DbAuthQueryCoHistories
{

struct HandlerContext
{
    IDBSheetCtx* m_pDbCtx;
    wo::DBSheetCommonHelper m_commonHelper;
    ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;

    HandlerContext(wo::KEtWorkbook* workbook);
    EtDbId GetEtDbId(const VarObj& obj, WebName name)
    {
        return m_commonHelper.GetEtDbId(obj, name);
    }
};

class HandlerBase
{
public:
    HandlerBase(wo::KEtWorkbook* workbook) : m_ctx(workbook) {}
    virtual ~HandlerBase() {}
    virtual std::string TypeName() const PURE;
    virtual WebInt Handle(const VarObj&) PURE;

protected:
    HandlerContext* getContext() { return &m_ctx; }

private:
    HandlerContext m_ctx;
};

class LineCohistoriesHandler : public HandlerBase
{
public:
    LineCohistoriesHandler(wo::KEtWorkbook* workbook) : HandlerBase(workbook) {}
    std::string TypeName() const override {
        return "lineCohistories";
    };
    WebInt Handle(const VarObj&) override;
};

class HandlerFactory
{
public:
    HandlerFactory() = default;
    ~HandlerFactory();

    void InitDefault(wo::KEtWorkbook* workbook);
    void Register(HandlerBase* handler);
    WebInt Handle(const VarObj& root);

private:
    std::unordered_map<std::string, HandlerBase*> m_handlers;
};

}