﻿#include "etstdafx.h"
#include "collect.h"
#include "webbase/logger.h"
#include "webetlink.h"
#include "workbook.h"
#include "hresult_to_string.h"

extern Callback* gs_callback;

namespace wo {
    WoCollectInfo::WoCollectInfo()
    : m_writer(m_wr)
    , m_ignoreCollect(false)
    {

    }
    
    WoCollectInfo::WoCollectInfo(binary_wo::BinWriter &wr, bool ignoreCollect)
    : m_writer(wr)
    , m_ignoreCollect(ignoreCollect)
    {
        
    }
    
    WoCollectInfo & WoCollectInfo::addString(const char * key, const ks_wstring & value, bool skipEmpty)
    {
        if (skipEmpty && value.empty())
            return *this;
        m_writer.addStringField(value.c_str(), key);
        return *this;
    }
    
    WoCollectInfo & WoCollectInfo::addString(const char * key, const QString & value, bool skipEmpty)
    {
        if (skipEmpty && value.isEmpty())
            return *this;
        m_writer.addStringField(krt::utf16(value), key);
        return *this;
    }

    WoCollectInfo & WoCollectInfo::addString(const char * key, const WCHAR * value, bool skipEmpty)
    {
        if (value)
        {
            if (skipEmpty && value[0] == '\0')
                return *this;
            m_writer.addStringField(value, key);
        }
        return *this;
    }

    WoCollectInfo & WoCollectInfo::addInt32(const char * key, int value)
    {
        m_writer.addInt32Field(value, key);
        return *this;
    }
    
    WoCollectInfo & WoCollectInfo::addUInt32(const char * key, unsigned int value)
    {
        m_writer.addUint32Field(value, key);
        return *this;
    }
    
    WoCollectInfo & WoCollectInfo::addInt64(const char * key, int64_t value)
    {
        m_writer.addAbsObjIDField(value, key);
        return *this;
    }

    WoCollectInfo & WoCollectInfo::addFloat64(const char * key, float64 d)
    {
        m_writer.addFloat64Field(d, key);
        return *this;
    }

    WoCollectInfo & WoCollectInfo::addBool(const char * key, bool d)
    {
        m_writer.addBoolField(d, key);
        return *this;
    }

    WoCollectInfo & WoCollectInfo::addComponentInfo(IBook *pBook)
    {
        if (pBook)
            return addComponentInfo(pBook->GetBMP());
        return *this;
    }

    WoCollectInfo & WoCollectInfo::addComponentInfo(BMP_PTR bmpPtr)
    {
        if (bmpPtr)
        {
            m_writer.addKey("project");
            if (bmpPtr->bDbSheet)
                m_writer.addString(__X("dbt"));
            else if (bmpPtr->bKsheet)
                m_writer.addString(__X("ksheet"));
            else
                m_writer.addString(__X("et"));
        }
        return *this;
    }
    
    WoCollectInfo & WoCollectInfo::addAppVersion()
    {
        // productInfo
        LPCWSTR p = _kso_GetProductInfo(ProcInfo_CurrentProductName);
        addString("app_version", p);
        return *this;
    }

    void WoCollectInfo::collect(const char * actionName)
    {
        if (m_ignoreCollect)
            return;
        
        binary_wo::BinWriter::StreamHolder shbt = m_writer.buildStream();
        WebSlice slice = {shbt.get(), m_writer.writeLength()};
        gs_callback->collectInfo(actionName, &slice);
    }

    WoFileIncludeCollector::WoFileIncludeCollector(KEtWorkbook * wb, const WCHAR * name, int count)
    : m_szName(name), m_count(count)
    {
        ASSERT(wb != nullptr);
        m_isValid = wb != nullptr && wb->getFileId() != nullptr;
        if (m_isValid)
        {
            m_collect.addString("name", name);
            m_collect.addString("fileid", wb->getFileId());
            m_collect.addInt32("count", count);
            m_collect.addComponentInfo(wb->GetBMP());
            m_collect.addAppVersion();
            
            wo::IEtRevisionContext* pEtCtx = _etcore_GetEtRevisionContext();
            if (pEtCtx && !pEtCtx->getTraceId().empty())
                addTraceId(pEtCtx->getTraceId());
        }
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addCmdTraceId(const WCHAR * v)
    {
        m_collect.addString("cmd_traceid", v);
#ifdef _DEBUG
        WOLOG_INFO << "cmd_traceid: " << v; 
#endif
        return *this;
    }
    
    WoFileIncludeCollector & WoFileIncludeCollector::addCmdTraceId(const QString & v)
    {
        m_collect.addString("cmd_traceid", v);
#ifdef _DEBUG
        WOLOG_INFO << "cmd_traceid: " << v; 
#endif
        return *this;
    }
    
    WoFileIncludeCollector & WoFileIncludeCollector::addTraceId(const ks_wstring & v)
    {
        m_collect.addString("traceid", v);
#ifdef _DEBUG
        WOLOG_INFO << "traceId: " << v; 
#endif
        return *this;
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addCount2(int cnt2)
    {
        if (cnt2 > 0)
            m_collect.addInt32("count2", cnt2);
        return *this;
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addSampleRate(int v)
    {
        if (v > 0)
            m_collect.addInt32("sample_rate", v);
        return *this;
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addThreadCount(int v)
    {// 1: 主线程; 2: 主线程 + 1 子线程
        if (v > 0)
            m_collect.addInt32("thread_cnt", v);
        return *this;
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addName1(const WCHAR * v)
    {
        m_collect.addString("name1", v);
        return *this;
    }
    
    WoFileIncludeCollector & WoFileIncludeCollector::addName1(const QString & v)
    {
        m_collect.addString("name1", v);
        return *this;
    }
    
    WoFileIncludeCollector & WoFileIncludeCollector::addName1(const ks_wstring & v)
    {
        m_collect.addString("name1", v);
        return *this;
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addCmdName(const WCHAR * v)
    {
        m_collect.addString("cmd_name", v);
        return *this;
    }
    
    WoFileIncludeCollector & WoFileIncludeCollector::addCmdName(const ks_wstring & v)
    {
        m_collect.addString("cmd_name", v);
        return *this;
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addFileSize1(int v)
    {
        m_collect.addInt32("filesize1", v);
        return *this;
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addFileSize2(int v)
    {
        m_collect.addInt32("filesize2", v);
        return *this;
    }

    WoFileIncludeCollector & WoFileIncludeCollector::addUserConnCnt(size_t count)
    {
        m_collect.addInt32("conn_cnt", count);
#ifdef _DEBUG
        WOLOG_INFO << "conn_cnt: " << count; 
#endif
        return *this;
    }
    
    WoFileIncludeCollector & WoFileIncludeCollector::addConnSvrFrom(const ks_wstring & v)
    {
        m_collect.addString("conn_from", v);
        return *this;
    }
    
    WoFileIncludeCollector & WoFileIncludeCollector::addConnSvrScene(const ks_wstring & v)
    {
        m_collect.addString("conn_scene", v);
        return *this;
    }
    
    WoFileIncludeCollector & WoFileIncludeCollector::addConnSvrLife(const ks_wstring & v)
    {
        m_collect.addString("conn_life", v);
        return *this;
    }

    void WoFileIncludeCollector::collect()
    {
        if (m_isValid)
        {
            WOLOG_INFO << "fileinclude: " << m_szName << ": " << m_count;
            m_collect.collect("fileinclude");
        }
        else
        {
            WOLOG_INFO << "fileinclude: " << m_szName << " invalid";
        }
    }

/**** 打开保存失败的埋点 *****/
    WoOpenSaveCollector::WoOpenSaveCollector(const WCHAR * name, int time)
    {
        m_collect.addString("name", name);
        m_collect.addInt32("time", time);
        m_collect.addAppVersion();
    }

    WoOpenSaveCollector::WoOpenSaveCollector(KEtWorkbook * wb, const WCHAR * name, int time)
    {
        ASSERT(wb != nullptr);
        m_isValid = wb != nullptr && wb->getFileId() != nullptr;
        if (m_isValid)
        {
            m_collect.addString("name", name);
            // wb->getFileId() 返回的并非是 char* 不能直接调用下面的addFiledid函数
            m_collect.addString("fileid", wb->getFileId());
            m_collect.addComponentInfo(wb->GetBMP());
            m_collect.addInt32("time", time);
            m_collect.addAppVersion();
        }
    }

    WoOpenSaveCollector::~WoOpenSaveCollector()
    {
        if (m_isValid)
            m_collect.collect("et_saveopen");
    }

    WoOpenSaveCollector & WoOpenSaveCollector::addFileid(const char* fileId)
    {
        m_collect.addString("fileid", fileId);
        return *this;
    }

    WoOpenSaveCollector & WoOpenSaveCollector::addFileTypeByFileName(const std::string& fileName)
    {
        size_t pos = fileName.rfind('.');
        if (pos != std::string::npos)
        {
            std::string extension = fileName.substr(pos + 1);
            m_collect.addString("filetype", extension.c_str());
        }

        return *this;
    }

    WoOpenSaveCollector & WoOpenSaveCollector::addResult(HRESULT hr)
    {
        m_collect.addInt32("hr", hr);
        m_collect.addString("hr_detail", GetErrWideString(hr));
        return *this;
    }

    WoOpenSaveCollector & WoOpenSaveCollector::addUserId(const char *userID)
    {
        m_collect.addString("user_id", userID);
        return *this;
    }

    WoOpenSaveCollector & WoOpenSaveCollector::addHasPassword(bool isHasPassword)
    {
        m_collect.addInt32("has_password", isHasPassword);
        return *this;
    }

    WoOpenSaveCollector & WoOpenSaveCollector::addHasModifyPassword(bool isHasModifyPassword)
    {
        m_collect.addInt32("has_modify_password", isHasModifyPassword);
        return *this;
    }
    
    ////////////////////////////////////////////////////
    WoCoreMetricsCollector::WoCoreMetricsCollector(KEtWorkbook * wb, const WCHAR * name, unsigned int ms)
    {
        m_isValid = wb != nullptr && wb->getFileId() != nullptr;
        if (m_isValid)
        {
            m_collect.addString("name", name);
            m_collect.addString("fileid", wb->getFileId());
            m_collect.addInt32("core_time", ms);
            m_collect.addComponentInfo(wb->GetBMP());
            m_collect.addAppVersion();
            
            _Application* pApp = wb->GetCoreApp();
            IKUserConns* pUserConns = pApp->getUserConns();
            size_t connsCnt = pUserConns->count();
            m_collect.addInt32("conn_cnt", connsCnt);
#ifdef _DEBUG
        WOLOG_INFO << "conn_cnt: " << connsCnt; 
#endif
        }
    }
    WoCoreMetricsCollector::~WoCoreMetricsCollector()
    {
        if (m_isValid)
        {
            m_collect.collect("svr_coremetrics");
        }
    }
    
    ////////////////////////////////////////////////////
    WoExitCollector::WoExitCollector(KEtWorkbook * wb, binary_wo::BinWriter &wr, const WCHAR * name)
    : m_collect(wr, true)
    , m_bw(wr)
    {
        m_isValid = wb != nullptr && wb->getFileId() != nullptr;
        if (m_isValid)
        {
            m_bw.beginStruct();
            m_bw.addStringField(name, "name");
            m_bw.beginStruct("params");
            
            {
                m_collect.addString("fileid", wb->getFileId());
                m_collect.addComponentInfo(wb->GetBMP());
                m_collect.addAppVersion();
            }
        }
    }
    
    WoExitCollector::~WoExitCollector()
    {
        if (m_isValid)
        {
            m_bw.endStruct();
            m_bw.endStruct();
        }
    }
}
