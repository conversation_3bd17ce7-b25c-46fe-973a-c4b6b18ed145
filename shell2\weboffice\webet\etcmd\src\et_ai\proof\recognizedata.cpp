﻿#include "etstdafx.h"
#include "recognizedata.h"
namespace etai
{
bool readString(const QJsonObject &obj, const QString &key, QString *value)
{
    QJsonObject::const_iterator it = obj.constFind(key);
    if (it != obj.constEnd() && it.value().isString())
    {
        *value = it.value().toString();
        return true;
    }

    return false;
};

bool readInteger(const QJsonObject &obj, const QString &key, int *value, int defaultVal)
{
    QJsonObject::const_iterator it = obj.constFind(key);
    if (it != obj.constEnd() && it.value().isDouble())
    {
        *value = it.value().toInt(defaultVal);
        return true;
    }

    *value = defaultVal;
    return false;
};

bool readStringList(const QJsonObject &obj, const QString &key, QStringList *list)
{
    QJsonObject::const_iterator it = obj.constFind(key);
    if (it != obj.constEnd() && it.value().isArray())
    {
        QJsonArray array = it.value().toArray();
        for (auto && i : array)
            list->append(i.toString());
        return true;
    }

    return false;
}

QString colIndexToName(int col)
{
    QString r;
    while (col >= 0)
    {
        if (col < 26)
        {
            char c = 'A' + col;
            return c + r;
        }
        
        int b = col % 26;
        col = (col / 26) - 1;
        char c = 'A' + b;
        r = c + r;
    }
    return r;
}

bool isNullTitle(const QString &title)
{
    if (title.isEmpty())
        return true;
    if (title.trimmed().isEmpty())
        return true;
    if (title.compare("-", Qt::CaseInsensitive) == 0)
        return true;
    if (title.compare("NULL", Qt::CaseInsensitive) == 0)
        return true;

    return false;
}

RangeList::RangeList(const QJsonObject &jsonObject, IKWorksheet *pWorkSheet, bool bMerge) : m_tableCnt(0)
{
    if (!pWorkSheet)
        return;
    init(jsonObject, pWorkSheet, bMerge);
}

TableRangeInfoList &RangeList::getTableInfoList()
{
    return m_tableInfoList;
}

QString RangeList::getSceneDescription()
{
    return m_sceneDescription;
}

void RangeList::getTableStructCollectInfo(
    OUT int &tableCnt, OUT std::vector<TableStructCollectInfo> &tableStructCollectInfoList)
{
    tableCnt = m_tableCnt;
    tableStructCollectInfoList = m_vecTableStructCollectInfo;
}

void RangeList::init(const QJsonObject &jsonObject, IKWorksheet *pWorkSheet, bool bMerge)
{
    HRESULT hr = E_FAIL;
    QVariantMap dataMap = jsonObject.toVariantMap();

    //场景类型
    m_sceneDescription = dataMap.value("sceneDescription").toString();

    //识别区域列表
    QJsonArray tablesListDataArray = dataMap.value("sheetAreaList").toJsonArray();

    //选区列表
    ES_CUBE eachZoneRangeCube;
    QJsonArray commendRgList = dataMap.value("recommendRgList").toJsonArray();
    QJsonArray rgDescribeList = dataMap.value("rgDescribeList").toJsonArray();
    for (int commendRgListIdx = 0; commendRgListIdx < commendRgList.size(); ++commendRgListIdx)
    {
        QJsonArray commendRange = commendRgList.at(commendRgListIdx).toArray();
        hr = createRange(commendRange, eachZoneRangeCube);
        if (SUCCEEDED(hr))
        {
            if (bMerge && commendRgListIdx > 0)
            {
                TableRangeInfo &mergedRange = m_tableInfoList.item(0);
                ES_CUBE &cube = mergedRange.allRangeInfo;
                cube.rowFrom = std::min(cube.rowFrom, eachZoneRangeCube.rowFrom);
                cube.rowTo = std::max(cube.rowTo, eachZoneRangeCube.rowTo);
                cube.colFrom = std::min(cube.colFrom, eachZoneRangeCube.colFrom);
                cube.colTo = std::max(cube.colTo, eachZoneRangeCube.colTo);
                continue;
            }
            TableRangeInfo eachTableInfoPara;
            eachTableInfoPara.setWorksheet(pWorkSheet);
            eachTableInfoPara.allRangeInfo = eachZoneRangeCube;
            //区域描述信息
            if (commendRgListIdx <= rgDescribeList.size())
            {
                QString rgDescribe = rgDescribeList.at(commendRgListIdx).toString();
                eachTableInfoPara.setRgDescribe(rgDescribe);
            }

            m_tableInfoList.insertRangeInfo(eachTableInfoPara);
        }
    }

    m_tableCnt = tablesListDataArray.size();
    //根据区域识别类型
    std::vector<Zone> zoneList;
    bool hasContent = false;
    for (int tableInx = 0; tableInx < m_tableCnt; ++tableInx)
    {
        QJsonArray eachTableJsonArray = tablesListDataArray.at(tableInx).toArray();
        TableStructCollectInfo tableStructCollectInfo;
        for (auto && eachTableJson : eachTableJsonArray)
        {
            QJsonArray eachZoneJsonArray = eachTableJson.toArray();
            Zone eachZone(eachZoneJsonArray);
            if (eachZone.m_type != Empty) //空白的不处理
            {
                tableStructCollectInfo.increaseZoneTypeCnt(&eachZone);
                zoneList.emplace_back(eachZone);
                hasContent |= eachZone.m_type == Content;
            }
        }
        m_vecTableStructCollectInfo.emplace_back(tableStructCollectInfo);
    }
    if (!hasContent)
        for (auto & zone : zoneList)
            zone.m_type = Content;

    //取交集
    for (auto & zone : zoneList)
        m_tableInfoList.setZoneTypeInfo(zone);
}

Zone::Zone(const QJsonArray &eachZone)
{
    if (eachZone.size() != 2) //暂定只有类型和范围
        return;
    QString zoneType = eachZone.at(0).toString();
    QJsonArray eachZoneRange = eachZone.at(1).toArray();
    HRESULT hr = createRange(eachZoneRange, m_range);
    if (FAILED(hr))
        return;

    if (zoneType == "rowTitle") //行标题
        m_type = RowTitle;
    else if (zoneType == "bigTitle") //大标题
        m_type = BigTitle;
    else if (zoneType == "content") //内容
        m_type = Content;
    else if (zoneType == "subTitle") //副标题
        m_type = SubTitle;
    else if (zoneType == "tableInfo") //表信息
        m_type = Info;
    else if (zoneType == "other") //其他
        m_type = Other;
    else
        m_type = Empty;
}

HRESULT createRange(IN const QJsonArray &rangeArray, OUT ES_CUBE &cube)
{
    if (rangeArray.size() != 4) //暂时目前还没指定sheet
        return E_FAIL;

    cube.sheetFrom = 0; //暂时默认值
    cube.sheetTo = 0;   //暂时默认值
    cube.rowFrom = rangeArray.at(0).toInt();
    cube.rowTo = rangeArray.at(1).toInt();
    cube.colFrom = rangeArray.at(2).toInt();
    cube.colTo = rangeArray.at(3).toInt();

    //处理服务端返回的异常数据
    if (cube.rowFrom > cube.rowTo || cube.colFrom > cube.colTo)
        return E_FAIL;
    return S_OK;
}

void TableRangeInfo::setWorksheet(IKWorksheet *pWorkSheet)
{
    m_pWorkSheet = pWorkSheet;
}

IKWorksheet *TableRangeInfo::getWorkSheet() const
{
    return m_pWorkSheet;
}

IKWorkbook *TableRangeInfo::getWorkBook() const
{
    return m_pWorkSheet->GetWorkbook();
}

bool TableRangeInfo::isEmptyTableInfo() const
{
    return vecTitleRangeInfo.empty() && vecHeadRangeInfo.empty() && vecContentRangeInfo.empty() &&
        vecSubTitleRangeInfo.empty() && vecOtherRangeInfo.empty() && vecInfoRangeInfo.empty();
}

void TableRangeInfo::insertZone(const Zone &zone)
{
    ZoneType type = zone.m_type;
    ES_CUBE cube = zone.m_range;
    if (cube.rowFrom < allRangeInfo.rowFrom)
        cube.rowFrom = allRangeInfo.rowFrom;
    if (cube.rowTo > allRangeInfo.rowTo)
        cube.rowTo = allRangeInfo.rowTo;
    if (cube.colFrom < allRangeInfo.colFrom)
        cube.colFrom = allRangeInfo.colFrom;
    if (cube.colTo > allRangeInfo.colTo)
        cube.colTo = allRangeInfo.colTo;

    if (cube.colFrom > cube.colTo || cube.rowFrom > cube.rowTo)
        return;

    switch (type)
    {
    case RowTitle:
        vecHeadRangeInfo.emplace_back(cube);
        break;
    case BigTitle:
        vecTitleRangeInfo.emplace_back(cube);
        break;
    case Content:
        vecContentRangeInfo.emplace_back(cube);
        break;
    case SubTitle:
        vecSubTitleRangeInfo.emplace_back(cube);
        break;
    case Other:
        vecOtherRangeInfo.emplace_back(cube);
        break;
    case Info:
        vecInfoRangeInfo.emplace_back(cube);
        break;
    default:
        break;
    }
}

ZoneType TableRangeInfo::getCellZoneType(int row, int col) const
{
    ZoneType type = Content;
    if (!isCellInZone(row, col, allRangeInfo))
        return type;
    auto isCellInRanges = [&](const std::vector<ES_CUBE>& vecCube)
    {
        return std::any_of(vecCube.begin(), vecCube.end(), [&](const ES_CUBE& cube) {
            return isCellInZone(row, col, cube);
        });
    };
    //内容区域的概率大点 放前面命中概率较大;
    if (isCellInRanges(vecContentRangeInfo))
        return Content;
    if (isCellInRanges(vecTitleRangeInfo))
        return BigTitle;
    if (isCellInRanges(vecHeadRangeInfo))
        return RowTitle;
    if (isCellInRanges(vecInfoRangeInfo))
        return Info;
    if (isCellInRanges(vecSubTitleRangeInfo))
        return SubTitle;
    if (isCellInRanges(vecOtherRangeInfo))
        return Other;

    return Empty;
}

void TableRangeInfo::setRgDescribe(const QString &rgDescribe)
{
    m_rgDescribe = rgDescribe;
}

QString TableRangeInfo::getRgDescribe() const
{
    return m_rgDescribe;
}

ES_CUBE TableRangeInfo::getFillAlterArea() const
{
    ES_CUBE ret = allRangeInfo;

    if (!vecTitleRangeInfo.empty())
        ret.rowFrom = vecTitleRangeInfo[0].rowTo + 1;
    if (!vecInfoRangeInfo.empty())
        ret.rowFrom = vecInfoRangeInfo[0].rowTo + 1;
    if (!vecHeadRangeInfo.empty())
        ret.rowFrom = vecHeadRangeInfo[0].rowFrom;

    if (ret.rowFrom > ret.rowTo || ret.rowFrom > allRangeInfo.rowTo)
    {
        //容错
        ret.rowFrom = ret.rowTo;
    }

    return ret;
}

ES_CUBE TableRangeInfo::getFillRowAlterArea() const
{
    ES_CUBE ret = allRangeInfo;

    if (!vecTitleRangeInfo.empty())
        ret.rowFrom = vecTitleRangeInfo[0].rowTo + 1;
    if (!vecInfoRangeInfo.empty())
        ret.rowFrom = vecInfoRangeInfo[0].rowTo + 1;
    if (!vecHeadRangeInfo.empty())
        ret.rowFrom = vecHeadRangeInfo[0].rowTo + 1;

    if (ret.rowFrom > ret.rowTo || ret.rowFrom > allRangeInfo.rowTo)
    {
        //容错
        ret.rowFrom = ret.rowTo;
    }

    return ret;
}

int TableRangeInfo::getFirstColIdx() const
{
    return allRangeInfo.colFrom;
}

bool TableRangeInfo::isCellInZone(int row, int col, const ES_CUBE &cube) const
{
    return (row >= cube.rowFrom && row <= cube.rowTo) && (col >= cube.colFrom && col <= cube.colTo);
}

void TableRangeInfoList::insertRangeInfo(const TableRangeInfo &tableInfo)
{
    //判断是否有重复
    for (const auto & i : m_rangeInfoList)
    {
        if (isCubeEqual(tableInfo.allRangeInfo, i.allRangeInfo))
        {
            return;
        }
    }
    m_rangeInfoList.push_back(tableInfo);
}

size_t TableRangeInfoList::elementCnt()
{
    return m_rangeInfoList.size();
}

TableRangeInfo &TableRangeInfoList::item(int idx)
{
    return m_rangeInfoList[idx];
}

void TableRangeInfoList::setZoneTypeInfo(const Zone &zone)
{
    size_t cnt = elementCnt();
    for (size_t rangeIdx = 0; rangeIdx < cnt; ++rangeIdx)
    {
        //判断区域范围是否属于此RANGE
        if (isZoneInRange(zone, m_rangeInfoList[rangeIdx].allRangeInfo))
        {
            m_rangeInfoList[rangeIdx].insertZone(zone);
        }
    }
}

bool TableRangeInfoList::isCubeEqual(const ES_CUBE &cube1, const ES_CUBE &cube2)
{
    bool bRet = (cube1.rowFrom == cube2.rowFrom) && (cube1.rowTo == cube2.rowTo) && (cube1.colFrom == cube2.colFrom) &&
                (cube1.colTo == cube2.colTo);
    return bRet;
}

bool TableRangeInfoList::isZoneInRange(const Zone &zone, const ES_CUBE &rangeCube)
{
    const ES_CUBE& zoneCube = zone.m_range;
    return (zoneCube.colTo >= rangeCube.colFrom) && (zoneCube.rowTo >= rangeCube.rowFrom) &&
                 (rangeCube.colTo >= zoneCube.colFrom) && (rangeCube.rowTo >= zoneCube.rowFrom);
}

TableZoneStyle::TableZoneStyle(const QString &textColor, const QString &fillColor,
                    std::unique_ptr<borderStyleInfo> pEdgeTopBorder, 
                    std::unique_ptr<borderStyleInfo> pEdgeBottomBorder,
                    std::unique_ptr<borderStyleInfo> pEdgeLeftBorder,
                    std::unique_ptr<borderStyleInfo> pEdgeRightBorder,
                    std::unique_ptr<borderStyleInfo> pInsideHBorder,
                    std::unique_ptr<borderStyleInfo> pInsideVBorder) :
                    m_textColor(textColor), m_fillColor(fillColor),
                    m_spEdgeTopBorder(std::move(pEdgeTopBorder)),
                    m_spEdgeBottomBorder(std::move(pEdgeBottomBorder)),
                    m_spEdgeLeftBorder(std::move(pEdgeLeftBorder)),
                    m_spEdgeRightBorder(std::move(pEdgeRightBorder)),
                    m_spInsideVBorder(std::move(pInsideVBorder)),
                    m_spInsideHBorder(std::move(pInsideHBorder))
{
}

borderStyleInfo *TableZoneStyle::getOneBorderStyle(oldapi::ETBorderIndex borderIdx)
{
    switch (borderIdx)
    {
    case etEdgeLeft:
        return m_spEdgeLeftBorder.get();
    case etEdgeRight:
        return m_spEdgeRightBorder.get();
    case etEdgeTop:
        return m_spEdgeTopBorder.get();
    case etEdgeBottom:
        return m_spEdgeBottomBorder.get();
    case etInsideVertical:
        return m_spInsideVBorder.get();
    case etInsideHorizontal:
        return m_spInsideHBorder.get();
    default:
        return nullptr;
    }
}

TableRangeStyle::TableRangeStyle(const QVariantMap &dataMap)
    : m_bFirstCol(false), m_bHeader(false), m_bCharge(false), m_fillAlterBase(NoneAlternation)
{
    m_bFirstCol = dataMap.value("bFirstCol", false).toBool();
    m_bHeader = dataMap.value("bHeader", true).toBool();
    m_bCharge = dataMap.value("bCharge", false).toBool();
    m_fillAlterBase = getAlterType(dataMap.value("alterType").toString());

    QVariantMap styleList = dataMap.value("style").toMap();

    for (auto iter = styleList.begin(); iter != styleList.end(); ++iter)
    {
        const QString& styleZoneName = iter.key();
        QVariantMap styleZoneDataMap = iter.value().toMap();
        if (styleZoneName.isEmpty() || styleZoneDataMap.isEmpty())
            continue;

        QString fontColor = styleZoneDataMap.value("fontColor").toString();
        QString fillColor = styleZoneDataMap.value("fillColor").toString();
        QVariantMap borderTopMap = styleZoneDataMap.value("borderTop").toMap();
        QVariantMap borderBottomMap = styleZoneDataMap.value("borderBottom").toMap();
        QVariantMap borderLeftMap = styleZoneDataMap.value("borderLeft").toMap();
        QVariantMap borderRightMap = styleZoneDataMap.value("borderRight").toMap();
        QVariantMap innerBorderHMap = styleZoneDataMap.value("innerBorderH").toMap();
        QVariantMap innerBorderVMap = styleZoneDataMap.value("innerBorderV").toMap();

        try
        {
            std::unique_ptr<borderStyleInfo> spEdgeTopBorder;
            std::unique_ptr<borderStyleInfo> spEdgeBottomBorder;
            std::unique_ptr<borderStyleInfo> spEdgeLeftBorder;
            std::unique_ptr<borderStyleInfo> spEdgeRightBorder;
            std::unique_ptr<borderStyleInfo> spInsideHBorder;
            std::unique_ptr<borderStyleInfo> spInsideVBorder;

            parseBorderStyleInfo(borderTopMap, spEdgeTopBorder);
            parseBorderStyleInfo(borderBottomMap, spEdgeBottomBorder);
            parseBorderStyleInfo(borderLeftMap, spEdgeLeftBorder);
            parseBorderStyleInfo(borderRightMap, spEdgeRightBorder);
            parseBorderStyleInfo(innerBorderHMap, spInsideHBorder);
            parseBorderStyleInfo(innerBorderVMap, spInsideVBorder);

            std::unique_ptr<TableZoneStyle> spZoneStyle;
            spZoneStyle = std::make_unique<TableZoneStyle>(fontColor, fillColor, std::move(spEdgeTopBorder),
                std::move(spEdgeBottomBorder), std::move(spEdgeLeftBorder), std::move(spEdgeRightBorder),
                std::move(spInsideHBorder), std::move(spInsideVBorder));

            StyleZoneType type = getStyleZoneType(styleZoneName);
            if (StyleInvalid == type)
                continue;
            m_mapTableRangeStyle.insert(std::make_pair(type, std::move(spZoneStyle)));
        }
        catch(...)
        {
            continue;
        }
    }

    m_styleName = dataMap.value("styleName").toString();
}

void TableRangeStyle::parseBorderStyleInfo(const QVariantMap &borderStyleMap, std::unique_ptr<borderStyleInfo>& ptr)
{
    QString borderColor = borderStyleMap.value("color").toString();
    QString borderStyle = borderStyleMap.value("borderStyle").toString();
    ptr = std::make_unique<borderStyleInfo>(borderColor, borderStyle);
}

TableZoneStyle *TableRangeStyle::getTableRangeStyle(StyleZoneType type)
{
    if (m_mapTableRangeStyle.find(type) != m_mapTableRangeStyle.end())
    {
        return m_mapTableRangeStyle.at(type).get();
    }
    return nullptr;
}

StyleZoneType TableRangeStyle::getStyleZoneType(const QString &strStyleZoneType)
{
    if (strStyleZoneType == "bigTitle")
        return StyleBigTitle;
    if (strStyleZoneType == "info")
        return StyleInfo;
    if (strStyleZoneType == "header")
        return StyleRowTitle;
    if (strStyleZoneType == "firstCol")
        return StyleFirstCol;
    if (strStyleZoneType == "rowAlterOdd")
        return StyleOddRowAlter;
    if (strStyleZoneType == "rowAlterEven")
        return StyleEvenRowAlter;
    if (strStyleZoneType == "colAlterOdd")
        return StyleOddColAlter;
    if (strStyleZoneType == "colAlterEven")
        return StyleEvenColAlter;
    if (strStyleZoneType == "noneAlter")
        return StyleNoneAlter;
    return StyleInvalid;
}

FillAlterBase TableRangeStyle::getAlterType(const QString &strAlterType)
{
    if (strAlterType == "colAlter")
        return ColAlternation;
    if (strAlterType == "rowAlter")
        return RowAlternation;
    if (strAlterType == "noneAlter")
        return NoneAlternation;
    return NoneAlternation;
}

ColorType TableRangeStyle::getColorType(const QString &strColorType)
{
    if (strColorType == "lightColor")
        return LightColor;
    if (strColorType == "mediumColor")
        return MediumColor;
    if (strColorType == "deepColor")
        return DeepColor;
    return LightColor;
}
} // namespace etai