﻿#include "etstdafx.h"
#include "quickset_helper.h"
#include "workbook.h"
#include "kfc/et_numfmt_str.h"
#include "database/database.h"
#include "database/database_def.h"
#include "database/database_utils.h"
#include "database/database_field_context.h"
#include "protection_helper.h"

// 已弃用不再维护, 改用Database下相关接口

namespace wo
{

KEtQuickSet::KEtQuickSet(KEtWorkbook* wwb)
	: m_wwb(wwb)
	, m_code(errNone)
{
}

KEtQuickSet::~KEtQuickSet()
{
}

HRESULT KEtQuickSet::operator()(QuickSetType qst, const RANGE &rg, IEtRevisionContext* pCtx)
{
	HRESULT hr = E_FAIL;
	Database::FieldContext fieldContext(m_wwb, pCtx);
	switch (qst)
	{
		case qstCheckbox:
		case qstDate:
		case qstScan:
		case qstCellPic:
		{
			hr = Database::Utils::ClearAll(&fieldContext, rg);
			if (FAILED(hr))
				return hr;
			Database::FieldsManager *pDbMgr = Database::FieldsManager::Instance();
			Database::FieldType fieldType = Qst2Dft(qst);
			ASSERT(fieldType != Database::dftInvalid);
			Database::IDbField *pField = pDbMgr->GetField(fieldType);
			if (pField == nullptr)
				return E_FAIL;
			hr = pField->Set(&fieldContext, rg);
			if (FAILED(hr))
				return hr;
			m_code = fieldContext.GetDVCode();
			return S_OK;
		}
		case qstTurnToDate:
		{
			hr = Database::Utils::ClearAll(&fieldContext, rg);
			if (FAILED(hr))
				return hr;
			return setNumberFormat(rg, ET_NF_COMBOBOX_SHORT_DATE);
		}
		case qstClear:
		{
			hr = Database::Utils::ClearAll(&fieldContext, rg);
			if (FAILED(hr))
				return hr;
			m_code = fieldContext.GetDVCode();
			return S_OK;
		}
		case qstInvalid:
		default:
			return E_FAIL;
	}
}

Database::FieldType KEtQuickSet::Qst2Dft(QuickSetType qst)
{
	switch (qst)
	{
		case qstCheckbox: return Database::dftCheckbox;
		case qstScan: return Database::dftScan;
		case qstDate: return Database::dftDate;
		case qstCellPic: return Database::dftCellPicture;
		case qstList: return Database::dftList;
		default: return Database::dftInvalid;
	}
}

SetDVReturnCode KEtQuickSet::GetDVReturnCode()
{
	return m_code;
}

HRESULT KEtQuickSet::FillValues(QuickSetType qst, const RANGE &rg, PCWSTR value, IEtRevisionContext* pCtx)
{
	class FillEnum : public Database::IFieldEnum
	{
	public:
		FillEnum(PCWSTR formula) : m_formula(formula) {}
		virtual ~FillEnum() {}

		virtual HRESULT Do(Database::FieldContext *pContext, Database::IDbField *pField, const RANGE &rg, const VALIDATION&) override
		{
			ks_stdptr<IRangeInfo> host = pContext->CreateRangeObj(rg);
			RANGE ref(pContext->GetBook()->GetBMP());
			ref.SetCell(rg.SheetFrom(), rg.RowFrom(), rg.ColFrom());
			CHECK_PROTECTION_FORMULA(rg, m_formula, pContext->GetRevisionContext());
			return host->SetFormula(m_formula, &ref);
		}
	private:
		PCWSTR m_formula;
	};

	Database::FieldsManager *pDbMgr = Database::FieldsManager::Instance();
	Database::FieldType fieldType = Qst2Dft(qst);
	Database::FieldContext fieldContext(m_wwb, pCtx);
	FillEnum fillEnum(value);
	return pDbMgr->EnumFieldsInRANGE(&fieldContext, rg, fieldType, Database::dbEnumType, &fillEnum);
}

QuickSetType KEtQuickSet::GetQuickSetType(WebStr str)
{
	if (xstrcmp(str, __X("checkbox")) == 0)
		return qstCheckbox;
	else if (xstrcmp(str, __X("scan")) == 0)
		return qstScan;
	else if (xstrcmp(str, __X("date")) == 0)
		return qstDate;
	else if (xstrcmp(str, __X("cellPic")) == 0)
		return qstCellPic;
	else if (xstrcmp(str, __X("turnToDate")) == 0)
		return qstTurnToDate;
	else if (xstrcmp(str, __X("clear")) == 0)
		return qstClear;
	
	ASSERT(FALSE);
	return qstInvalid;
}

HRESULT KEtQuickSet::setNumberFormat(const RANGE &rg, int nfIndex)
{
	ks_bstr nfStr(kfc::nf::_XNFGetEtStr(nfIndex));
	return setNumberFormat(rg, nfStr);
}

HRESULT KEtQuickSet::setNumberFormat(const RANGE &rg, BSTR nfStr)
{
	ks_stdptr<etoldapi::Range> spRange = m_wwb->CreateRangeObj(rg);
	if (spRange == nullptr)
		return E_FAIL;

	spRange->ForceIgnoreFilter(TRUE);
	HRESULT hr = spRange->put_NumberFormat(nfStr);
	spRange->ForceIgnoreFilter(FALSE);
	return hr;
}

}
