﻿#include "pasteinfocollect.h"
#include "et_revision_context_impl.h"
#include "hresult_to_string.h"
#include "woetsetting.h"
#include "workbook.h"
#include "etcore/et_core_event_tracking.h"
#include "wo/et_revision_context.h"

namespace wo {
WoPasteCollector::WoPasteCollector(ISheet* pSheet, wo::IEtRevisionContext* ctx, PASTE_SPECIAL pasteType)
    : m_pSheet(pSheet)
    , m_begin(std::chrono::steady_clock::now())
    , m_pasteType(pasteType) 
{
    m_commentCount = GetCommentCount();
    m_nameCount = GetNameCount();

    if (m_pSheet)
    {
        m_pEventTracking = m_pSheet->LeakBook()->LeakWorkspace()->GetEventTracking();
        m_pEventTracking->SetCopyPasting(true);
        m_pEventTracking->GetManager()->SetFileId(ctx->getFileId());
        m_pEtCollectInfo = m_pEventTracking->GetCollectInfo();
    }
}

WoPasteCollector::~WoPasteCollector() 
{
	if (m_pEtCollectInfo == nullptr || m_pEventTracking == nullptr)
		return;

    unsigned int usedTime = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
    KComVariant varTime(usedTime);
    KComVariant varRes(m_hr);
    KComVariant varPasteType(static_cast<int>(m_pasteType));
	KComVariant varComment(GetCommentCount() - m_commentCount);
	KComVariant varName(GetNameCount() - m_nameCount);
    m_pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_TOTAL_TIME_CONSUMING, varTime);
    m_pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_STATUS, varRes);
    m_pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::PASTE_TYPE, varPasteType);
	m_pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COMMENT_COUNT, varComment);
	m_pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::NAME_COUNT, varName);

    if (FAILED(m_hr))
    {
        KComVariant varErrName(GetErrWideString(m_hr));
        m_pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::ERROR_NAME, varErrName);
    }
    m_pEventTracking->SetCopyPasting(false);
    m_pEventTracking->SendInfoAfterPaste();
}


void WoPasteCollector::setResult(HRESULT hr)
{
    m_hr = hr;
}

INT WoPasteCollector::GetCommentCount()
{
    if (!m_pSheet)
	{
		return 0;
	}

	ks_stdptr<IUnknown> spUnk;
	m_pSheet->GetExtDataItem(edSheetCommentDrawingCanvas, (IUnknown**)&spUnk);
	ks_stdptr<ICellComments> spComments;
	if (spUnk != nullptr)
	{
		spUnk->QueryInterface(IID_ICellComments, (void**)&spComments);
	}
    if (spComments != nullptr)
        return 0;

    INT count = 0;
    spComments->GetCount(&count);
    return count;
}

INT WoPasteCollector::GetNameCount()
{
    if (m_pSheet == nullptr)
        return 0;

    ks_stdptr<IBook> spBook;
    m_pSheet->GetBook(&spBook);
    if (spBook == nullptr)
        return 0;
    
    ks_stdptr<IBookOp> spBookOp;

	if (FAILED(spBook->GetOperator(&spBookOp)))
	{
		return 0;
	}

	int nameCount = 0;
	if (FAILED(spBookOp->GetNameUdfCount(&nameCount)))
	{
		return 0;
	}

	return nameCount;
}


} // namespace wo