﻿/* -------------------------------------------------------------------------
//	文件名		：	identifytable.cpp
//	创建者		：
//	创建时间	：
//	功能描述	：
//
// -----------------------------------------------------------------------*/
#include "identifytable.h"
#include "applogic/et_api_convert.h"
#include "etstdafx.h"
#include "kso/api/smartparam.h"
#include "kso/numberformat/numberformatconvert.h"
#include "smartanalysis.h"
#include <kso/textstd/texttool.h>
#include "et_ai/teval.h"
#include "et_hard_define_strings.h"
#include <etcore/et_core_utility.h>

#define MaxColumn 500
#define MaxRow 60000
#define MaxCellCount 1000000
#define MaxCellCountSheetQA 300000
#define MaxCellCountBookQA 600000
#define MaxMergeRow 10000
namespace etai
{
namespace kidentifytable
{
//取得单元格数值格式与其他选项标记
HRESULT GetCellNumFmtType(long lSheet, long lRow, long lCol, IBookOp *pBookOp, kfc::nf::NF_FORMAT_TYPE *pType,
                          DWORD *pdwOpt, const VARIANT &var)
{
#pragma prompt("zzy : 这个函数需要让IBookOp提供")
    if (NULL == pBookOp)
    {
        ASSERT(false);
        return E_INVALIDARG;
    }

    HRESULT hr = S_OK;

    // 取单元格格式
    const XF *pXf = NULL;
    hr = pBookOp->GetCellFormat(lSheet, lRow, static_cast<COL>(lCol), &pXf, NULL);
    KS_CHECK(hr);

    {
        {
            // 取数值格式句柄
            kfc::nf::NFHANDLE nf = pXf->pNumFmt->hfmt;
            KS_CHECK(hr);

            // 取类型
            kfc::nf::NF_FORMAT_TYPE type;
            DWORD dwOpt;
            if (VT_EMPTY != V_VT(&var))
            {
                kfc::nf::NFSTYLE style;
                IBook *pBook = pBookOp->LeakBook();
                bool b1904 = etexec::BOOL2bool(pBook->Is1904DateSystem());
                hr = kfc::nf::_XNFFormatEx2(var, b1904, nf, NULL, &style);
                KS_CHECK(hr);
                type = style.FmtType;
                dwOpt = style.dwOpt;
            }
            else
            {
                type = kfc::nf::FMT_TYPE_NULL;
                dwOpt = 0;
            }
            if (pType != NULL)
                *pType = type;
            if (pdwOpt != NULL)
                *pdwOpt = dwOpt;
        }
    }
KS_EXIT:
    return hr;
}

class KEtRgValueTryConvert
{
  public:
    KEtRgValueTryConvert();
    ~KEtRgValueTryConvert();
    HRESULT TryConvert2Data(IDX iSheet, ROW row, COL col, IBookOp *pBookOp, VARIANT *pVar);

  private:
    HRESULT TryConvert2Date(IDX iSheet, ROW row, COL col, IBookOp *pBookOp, VARIANT *pVar);
    BOOL IsLocalCurrencyType(IDX iSheet, ROW row, COL col, IBookOp *pBookOp);
    bool TryConvert2Currency(IDX iSheet, ROW row, COL col, IBookOp *pBookOp, VARIANT *pVar);
};

KEtRgValueTryConvert::KEtRgValueTryConvert()
{
}

KEtRgValueTryConvert::~KEtRgValueTryConvert()
{
}

HRESULT KEtRgValueTryConvert::TryConvert2Data(IDX iSheet, ROW row, COL col, IBookOp *pBookOp, VARIANT *pVar)
{
    if (V_VT(pVar) == VT_R8)
    {
        if (TryConvert2Currency(iSheet, row, col, pBookOp, pVar))
            return S_OK;

        return TryConvert2Date(iSheet, row, col, pBookOp, pVar);
    }
    return S_OK;
}

HRESULT KEtRgValueTryConvert::TryConvert2Date(IDX iSheet, ROW row, COL col, IBookOp *pBookOp, VARIANT *pVar)
{
    // 将时间日期类型的值转化成 DATE 类型的 VARIANT
    kfc::nf::NF_FORMAT_TYPE tp = kfc::nf::FMT_TYPE_NULL;
    DWORD dw = 0;
    GetCellNumFmtType(iSheet, row, col, pBookOp, &tp, &dw, *pVar);
    if (kfc::nf::FMT_TYPE_DATA_TIME == tp && (kfc::nf::NFTYPE_DATE_DT & dw))
    {
        ks_stdptr<IBook> spBook;
        VS(pBookOp->GetBook(&spBook));
        bool b1904 = spBook->Is1904DateSystem();

        const int min_date = b1904 ? -658897 : -657435;        // 100-1-1
        const int max_date = b1904 ? MAX_DATE_1904 : MAX_DATE; // 9999-12-31

        double dt = V_R8(pVar);
        if (dt <= min_date || dt > max_date)
            return DISP_E_OVERFLOW;

        if (b1904)
            dt += 1462.0;

        V_VT(pVar) = VT_DATE;
        V_DATE(pVar) = dt;
    }
    return S_OK;
}

BOOL KEtRgValueTryConvert::IsLocalCurrencyType(IDX iSheet, ROW row, COL col, IBookOp *pBookOp)
{
    if (NULL == pBookOp)
        return FALSE;
    // 取单元格格式
    const XF *pXf = NULL;
    if (FAILED(pBookOp->GetCellFormat(iSheet, row, col, &pXf, NULL)))
        return FALSE;

    // 取数值格式句柄
    kfc::nf::NFHANDLE nf = pXf->pNumFmt->hfmt;
    return kfc::nf::_XNF_ET_IsLocalCurrencySymbol(nf);
}

bool KEtRgValueTryConvert::TryConvert2Currency(IDX iSheet, ROW row, COL col, IBookOp *pBookOp, VARIANT *pVar)
{
    if (IsLocalCurrencyType(iSheet, row, col, pBookOp))
    {
        VariantChangeType(pVar, pVar, 0, VT_CY);
        return true;
    }
    return false;
}

} // namespace kidentifytable

// 如果返回false则表示处理的区域在范围以外，则不处理
bool AmendRange(IdentifyRange &rg)
{
    if (rg.iLeft >= MaxColumn)
        return false;
    if (rg.iTop >= MaxRow)
        return false;
    if (rg.iRight >= MaxColumn)
        rg.iRight = MaxColumn - 1;
    if (rg.iBottom >= MaxRow)
        rg.iBottom = MaxRow - 1;
    return true;
}

bool AmendRange(const long row, const long col)
{
    return row * col <= MaxCellCount;
}

bool IsRightCell(const IdentifyRange &rg, ROW iRow, COL iCol)
{
    if (rg.IsOneCell())
        return true;
    if (rg.iBottom - rg.iTop <= MaxMergeRow)
        return true;
    if (rg.iTop == iRow && rg.iLeft == iCol)
        return true;
    return false;
}

IdentifyRange::IdentifyRange(Range *pRange)
{
    ResetRange(pRange);
}

IdentifyRange::IdentifyRange(const RANGE &rg)
{
    ResetRange(&rg);
}

void IdentifyRange::ResetRange(Range *pRange)
{
    if (!pRange)
        return;
    HRESULT hr = S_CONTINUE;
    pRange->get_Row(&iTop);
    pRange->get_Column(&iLeft);
    iTop -= 1;
    iLeft -= 1;
    iBottom = iTop;
    iRight = iLeft;
    KCOMPTR(Range) spRows;
    hr = pRange->get_Rows(&spRows);
    if (SUCCEEDED(hr) && spRows)
    {
        spRows->get_Count(&iBottom);
        iBottom += iTop - 1;
    }
    KCOMPTR(Range) spColumns;
    hr = pRange->get_Columns(&spColumns);
    if (SUCCEEDED(hr) && spColumns)
    {
        spColumns->get_Count(&iRight);
        iRight += iLeft - 1;
    }
}

void IdentifyRange::ResetRange(const RANGE *pRange)
{
    iTop = pRange->RowFrom();
    iLeft = pRange->ColFrom();
    iBottom = pRange->RowTo();
    iRight = pRange->ColTo();
}

bool removePattern(QString &showText, const QRegExp &rx)
{
    QString result;
    int prev = 0, curr = 0;
    bool bMatched = false;
    while ((curr = rx.indexIn(showText, curr)) != -1)
    {
        if (curr > prev)
            result += showText.mid(prev, curr - prev);
        prev = curr = curr + rx.matchedLength();
        bMatched = true;
    }
    if (bMatched)
    {
        result += showText.mid(prev);
        showText = result;
    }
    return bMatched;
}

QString Full2Half(const QString &source)
{
    const WCHAR *pcwcs = krt::utf16(source);
    ks_wstring wsRes;
    while (*pcwcs != 0)
    {
        WCHAR wchFollow = 0;
        wsRes.push_back(_TxFull2HalfWidth(*pcwcs, &wchFollow));
        if (wchFollow != 0)
            wsRes.push_back(wchFollow);
        ++pcwcs;
    }
    return krt::fromUtf16(wsRes.c_str());
}

bool DeleteWhitespace(QString &string)
{
    static QRegExp rx("[\\s]+");
    return removePattern(string, rx);
}

bool DeleteUnit(QString &string)
{
    static QString s_mathChars = krt::fromUtf16(__X("^(.+([(|（].*[)|）]).*)+$"));
    static QRegExp exp(s_mathChars);
    if (string.indexOf(exp) == -1)
        return false;
    static QString s_unitChars = krt::fromUtf16(__X("[(|（].*[(|（|)|）]*[)|）]"));
    static QRegExp rx(s_unitChars);
    return removePattern(string, rx);
}

int GetNonblankCharIndex(const ks_wstring &str)
{
    for (size_t i = 0; i < str.size(); ++i)
    {
        WCHAR ch = str[i];
        if (ch == 0x3000 || ch == 0x20 || ch == 0x09 || ch == 0x0e || ch == 0x0B || ch == 0x0D || ch == 0x0A ||
            ch == 0x0C || ch == 0xA0 || ch == 0xF0B7)
            continue;
        return (int)i;
    }
    return -1;
}

void CleanContent(const QString &strContent, QString &strCleanContent)
{
    strCleanContent = strContent;
    strCleanContent = Full2Half(strCleanContent);
    DeleteWhitespace(strCleanContent);
    DeleteUnit(strCleanContent);
}

QString variantToString(const KComVariant& variant)
{
	if (VT_EMPTY == V_VT(&variant))
		return QString();

	KComVariant varValue2Bstr;
	HRESULT hr = ::VariantChangeType(&varValue2Bstr, &variant, 0, VT_BSTR);
	if (SUCCEEDED(hr) && V_VT(&varValue2Bstr) == VT_BSTR)
		return krt::fromUtf16(V_BSTR(&varValue2Bstr));

	return QString();
}

void IdentifyCells::ExportJson(QJsonObject &jsonRow, HiddenRowColTool *pTool) const
{
    QJsonObject jsonCells;
    jsonCells.insert("content", strContent.toUtf8().constData());
    if (!strValue2.isEmpty())
        jsonCells.insert("value2", strValue2.toUtf8().constData());
	if (!varValue.isNull())
		jsonCells.insert("value", QJsonValue::fromVariant(varValue));
    if (!strNumberFormat.isEmpty())
        jsonCells["numberFormatLocal"] = strNumberFormat.toUtf8().constData();
    if (!qFuzzyCompare(fFontSize, 11.0f))
        jsonCells.insert("fontsize", fFontSize);
    if (nAlignment != etHAlignGeneral)
        jsonCells.insert("alignment", nAlignment);
    // 取单元格去掉隐藏行列后是否为合并单元格
    QString strCells;
    IdentifyRange rgCell = pTool ? pTool->GetExportJsonRange(*this) : (IdentifyRange) * this;
    if (!rgCell.IsOneCell())
    {
        jsonCells.insert("merge", true);
        if (iTop != iBottom)
            jsonCells.insert("mergerows",
                             QString("(%1, %2)").arg(rgCell.iTop).arg(rgCell.iBottom).toUtf8().constData());
    }
    strCells = QString("(%1, %2)").arg(rgCell.iLeft).arg(rgCell.iRight);
    jsonRow.insert(strCells.toUtf8().constData(), jsonCells);
}

const IdentifyCells *IdentifyMergeCellsList::FindCellInMergeCells(long iRow, long iCol)
{
    for (auto it = begin(); it != end(); ++it)
    {
        const IdentifyCells *pCells = &(*it);
        if (pCells && pCells->IsInRange(iRow, iCol))
            return pCells;
    }
    return NULL;
}

HiddenRowColTool::HiddenRowColTool(ISheet *pSheet, const RANGE &rg) : m_UserRange(rg)
{
    Init(pSheet, rg);
}

void HiddenRowColTool::Init(ISheet *pSheet, const RANGE &rg)
{
    if (!pSheet)
        return;
    ks_stdptr<IRowColOp> spRowColOp = pSheet->LeakOperator();
    if (!spRowColOp)
        return;
    for (long iRow = rg.RowFrom(); iRow <= rg.RowTo(); ++iRow)
    {
        if (!spRowColOp->GetRowHidden(iRow))
            continue;
        m_setHiddenRow.insert(iRow);
    }
    for (long iCol = rg.ColFrom(); iCol <= rg.ColTo(); ++iCol)
    {
        if (!spRowColOp->GetColHidden(iCol))
            continue;
        m_setHiddenCol.insert(iCol);
    }
}

bool HiddenRowColTool::IsHiddenRow(long iRow)
{
    if (m_setHiddenRow.find(iRow) != m_setHiddenRow.end())
        return true;
    return false;
}

bool HiddenRowColTool::IsHiddenCol(long iCol)
{
    if (m_setHiddenCol.find(iCol) != m_setHiddenCol.end())
        return true;
    return false;
}

long HiddenRowColTool::GetExportJsonRowIdx(long iRow)
{
    long nHiddenRowCnt = 0;
    for (auto it = m_setHiddenRow.begin(); it != m_setHiddenRow.end(); ++it)
    {
        if (*it >= iRow)
            return iRow - nHiddenRowCnt;
        nHiddenRowCnt += 1;
    }
    return iRow - nHiddenRowCnt;
}

long HiddenRowColTool::GetExportJsonColIdx(long iCol)
{
    long nHiddenColCnt = 0;
    for (auto it = m_setHiddenCol.begin(); it != m_setHiddenCol.end(); ++it)
    {
        if (*it >= iCol)
            return iCol - nHiddenColCnt;
        nHiddenColCnt += 1;
    }
    return iCol - nHiddenColCnt;
}

long HiddenRowColTool::GetExportJsonRowIdx1(long iRow)
{
    long nHiddenRowCnt = 0;
    for (auto it = m_setHiddenRow.begin(); it != m_setHiddenRow.end(); ++it)
    {
        if (*it == iRow)
            return iRow - nHiddenRowCnt - 1;
        if (*it > iRow)
            return iRow - nHiddenRowCnt;
        nHiddenRowCnt += 1;
    }
    return iRow - nHiddenRowCnt;
}

long HiddenRowColTool::GetExportJsonColIdx1(long iCol)
{
    long nHiddenColCnt = 0;
    for (auto it = m_setHiddenCol.begin(); it != m_setHiddenCol.end(); ++it)
    {
        if (*it == iCol)
            return iCol - nHiddenColCnt - 1;
        if (*it > iCol)
            return iCol - nHiddenColCnt;
        nHiddenColCnt += 1;
    }
    return iCol - nHiddenColCnt;
}
IdentifyRange HiddenRowColTool::GetExportJsonRange(IdentifyRange rg)
{
    IdentifyRange dest;
    dest.iTop = GetExportJsonRowIdx(rg.iTop);
    dest.iBottom = GetExportJsonRowIdx1(rg.iBottom);
    dest.iLeft = GetExportJsonColIdx(rg.iLeft);
    dest.iRight = GetExportJsonColIdx1(rg.iRight);
    return dest;
}

long HiddenRowColTool::GetRealRowIdx(long iRow)
{
    if (m_setHiddenRow.empty())
        return iRow;
    auto it = m_setHiddenRow.begin();
    long nRow = m_UserRange.iTop;
    for (long i = m_UserRange.iTop; i <= m_UserRange.iBottom; ++i)
    {
        if (it != m_setHiddenRow.end() && i == *it)
            it++;
        else
        {
            if (nRow >= iRow)
                return i;
            nRow++;
        }
    }
    // 不应该超出UserRange范围
    ASSERT_ONCE(false);
    return iRow;
}

long HiddenRowColTool::GetRealColIdx(long iCol)
{
    if (m_setHiddenCol.empty())
        return iCol;
    auto it = m_setHiddenCol.begin();
    long nCol = m_UserRange.iLeft;
    for (long i = m_UserRange.iLeft; i <= m_UserRange.iRight; ++i)
    {
        if (it != m_setHiddenCol.end() && i == *it)
            it++;
        else
        {
            if (nCol >= iCol)
                return i;
            nCol++;
        }
    }
    // 不应该超出UserRange范围
    ASSERT_ONCE(false);
    return iCol;
}

IdentifyRange HiddenRowColTool::GetRealRange(IdentifyRange rg)
{
    IdentifyRange dest;
    dest.iTop = GetRealRowIdx(rg.iTop);
    dest.iBottom = GetRealRowIdx(rg.iBottom);
    dest.iLeft = GetRealColIdx(rg.iLeft);
    dest.iRight = GetRealColIdx(rg.iRight);
    return dest;
}

QString IdentifyTool::GetCellText(ROW iRow, COL iCol, IETStringTools *pTools)
{
    if (!pTools)
        return QString();
    ks_bstr bstrText;
    pTools->GetCellText(NULL, iRow, iCol, &bstrText, NULL, -1, NULL);
    return krt::fromUtf16(bstrText.c_str());
}

KComVariant IdentifyTool::GetCellVariant(IDX iSheet, ROW iRow, COL iCol, IBookOp* pBookOp)
{
	if (!pBookOp)
		return KComVariant();

	const_token_ptr pt = NULL;
	HRESULT hr = pBookOp->GetCellValue(iSheet, iRow, iCol, &pt);
	if (!SUCCEEDED(hr))
		return KComVariant();

	KComVariant variant;
	hr = etexec::TokenToVariant(pt, variant);
	if (!SUCCEEDED(hr))
		return KComVariant();

	kidentifytable::KEtRgValueTryConvert convertValueType;
	hr = convertValueType.TryConvert2Data(iSheet, iRow, iCol, pBookOp, &variant);
	if (!SUCCEEDED(hr))
		return KComVariant();

	return variant;
}

QString IdentifyTool::GetCellValue2(IDX iSheet, ROW iRow, COL iCol, IBookOp* pBookOp)
{
	if (!pBookOp)
		return QString();

	KComVariant variant = GetCellVariant(iSheet, iRow, iCol, pBookOp);

	return variantToString(variant);
}

QString IdentifyTool::GetCellNumberFormatLocal(IDX iSheet, ROW iRow, COL iCol, IBookOp *pBookOp)
{
    if (!pBookOp)
        return QString();
    const XF *pXf = NULL;
    pBookOp->GetCellFormat(iSheet, iRow, iCol, &pXf, NULL);
    if (pXf && pXf->pNumFmt)
    {
        ks_bstr bsformat;
        _kso_NF_LocalToBuiltin(pXf->pNumFmt->fmt, &bsformat);
        if (!bsformat.empty() && xstricmp(bsformat.c_str(), __X("General")) != 0 || bsformat.empty())
        {
            return krt::fromUtf16(pXf->pNumFmt->fmt);
        }
    }
    return QString();
}

void IdentifyTool::GetTableRange(Range *pRange, RANGE *pTableRange)
{
    if (!pRange)
        return;
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = pRange->get_Worksheet(&spWorksheet);
    if (FAILED(hr) || !spWorksheet)
        return;
    ks_stdptr<IKWorksheet> spSheet = spWorksheet;
    if (!spSheet)
        return;
    IDX iSheet = 0;
    spSheet->GetSheet()->GetIndex(&iSheet);

    long iTop = 0, iBottom = 0, iLeft = 0, iRight = 0;
    pRange->get_Row(&iTop);
    pRange->get_Column(&iLeft);
    iTop -= 1;
    iLeft -= 1;
    iBottom = iTop;
    iRight = iLeft;
    KCOMPTR(Range) spRows;
    hr = pRange->get_Rows(&spRows);
    if (SUCCEEDED(hr) && spRows)
    {
        spRows->get_Count(&iBottom);
        iBottom += iTop - 1;
    }
    KCOMPTR(Range) spColumns;
    hr = pRange->get_Columns(&spColumns);
    if (SUCCEEDED(hr) && spColumns)
    {
        spColumns->get_Count(&iRight);
        iRight += iLeft - 1;
    }
    pTableRange->SetSheetFromTo(iSheet, iSheet);
    pTableRange->SetRowFromTo(iTop, iBottom);
    pTableRange->SetColFromTo(iLeft, iRight);
}

KIdentifyTable::KIdentifyTable(IN IKWorksheet* pWorkSheet, IN JsonDataType nJsonDataType)
    : m_pTool(NULL), m_nJsonDataType(nJsonDataType), m_identifyTableContinueProc(NULL)
{
    if (!pWorkSheet)
        return;
    m_spWorksheet = pWorkSheet;
    m_spWorksheet->GetWorkbook()->GetBook()->GetOperator(&m_spBookOp);
    if (!m_spBookOp)
        return;
    _etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void **)&m_spTools);
    if (!m_spTools)
        return;
    m_spTools->SetEnv(m_spWorksheet->GetSheet());

    m_spWorksheet->get_UsedRange(&m_spUserRange);
    m_UserRange.ResetRange(m_spUserRange);
    m_Range = m_UserRange;
    if (nJsonDataType == JsonDataArrangement || nJsonDataType == JsonDataProofreading)
    {
        ks_stdptr<ISheet> spSheet = m_spWorksheet->GetSheet();
        RANGE range(spSheet->GetBMP());
        IdentifyTool::GetTableRange(m_spUserRange, &range);
        m_pTool = new HiddenRowColTool(spSheet, range);
    }
}

KIdentifyTable::~KIdentifyTable()
{
    if (m_pTool)
        delete m_pTool;
}

bool KIdentifyTable::IsContinue() const
{
    return m_identifyTableContinueProc == NULL || m_identifyTableContinueProc();
}

// 取选取区域仅当选择是表格内容区域
void KIdentifyTable::GetSelectionRange()
{
    if (!m_vecSelectRangeList.empty() || !m_SelectRange.IsInvalid())
        return;

    ks_stdptr<IKWorksheetView> spView = m_spWorksheet->GetActiveWorksheetView();
    if (!spView)
        return;
    range_helper::ranges rgs;
    spView->GetSelectionRange(&rgs);
    if (!rgs || rgs.size() == 0)
        return;
    UINT nCount = 0;
    rgs->GetCount(&nCount);
    for (UINT i = 0; i < nCount; ++i)
    {
        const RANGE *pRg = rgs.at(i).second;
        if (!pRg)
            continue;
        IdentifyRange rg;
        rg.ResetRange(pRg);
        m_vecSelectRangeList.push_back(rg);

        if (m_SelectRange.IsInvalid())
            m_SelectRange = rg;
        m_SelectRange.ExpandRange(rg);
    }
}

bool KIdentifyTable::IsNotEmptyCell(IDX iSheet, ROW iRow, COL iCol)
{
    QString strText = IdentifyTool::GetCellText(iRow, iCol, m_spTools);
    if (strText.isEmpty())
    {
        ks_stdptr<ISheet> spSheet = m_spWorksheet->GetSheet();
        RANGE range(spSheet->GetBMP());
        range.SetCell(iSheet, iRow, iCol);
        spSheet->MergeExpandOnce(range);
        if (!range.IsSingleCell())
            strText = IdentifyTool::GetCellText(range.RowFrom(), range.ColFrom(), m_spTools);
    }
    QString strClean;
    CleanContent(strText, strClean);
    if (strClean.isEmpty())
        return false;
    return true;
}

void KIdentifyTable::GetTableCols(std::vector<std::pair<long, long>> &vecTableCols, ISmartAnalysisListener *pListener)
{
    long count = 0;
    std::set<long> setNotEmptyCol;
    ks_stdptr<ISmartAnalysisListener> spListener = pListener;
    IDX iSheet = 0;
    m_spWorksheet->GetSheet()->GetIndex(&iSheet);
    for (long iCol = m_Range.iLeft; iCol <= m_Range.iRight; ++iCol)
    {
        if (m_pTool && m_pTool->IsHiddenCol(iCol))
        { // 隐藏列不切割表格
            setNotEmptyCol.insert(iCol);
            continue;
        }
        for (long iRow = m_Range.iTop; iRow <= m_Range.iBottom; ++iRow)
        {
            if (IsNotEmptyCell(iSheet, iRow, iCol))
            {
                setNotEmptyCol.insert(iCol);
                break;
            }
            if (count++ == 1023)
            {
                QCoreApplication::processEvents();
                if (m_spWorksheet->IsDestroyed())
                    return;
                if (spListener)
                {
                    if (spListener->isStop())
                        return;
                    else
                        spListener->UpdateCommand();
                }
                count = 0;
            }
        }
    }

    if (setNotEmptyCol.empty())
        return;

    auto itCol = setNotEmptyCol.begin();
    long iLeft = *itCol;
    long iLastCol = iLeft;
    itCol++;
    for (; itCol != setNotEmptyCol.end(); ++itCol)
    {
        long iCol = *itCol;
        if (iCol - iLastCol > 1) // 有空列且非隐藏
        {
            bool hasNotHiddenCol = false;
            for (long i = iLeft; i <= iLastCol; ++i)
            {
                if (!(m_pTool && m_pTool->IsHiddenCol(i)))
                {
                    hasNotHiddenCol = true;
                    break;
                }
            }
            if (hasNotHiddenCol) // 仅有非空白列但都是隐藏列的话不push
                vecTableCols.push_back(std::make_pair(iLeft, iLastCol));
            iLeft = iCol;
        }
        iLastCol = iCol;
    }
    vecTableCols.push_back(std::make_pair(iLeft, iLastCol));
}

void KIdentifyTable::GetSelectionCols(IdentifyRange rg, std::vector<std::pair<long, long>> &vecTableCols)
{
    if (rg.IsInvalid())
        return;
    std::vector<size_t> vecToDel;
    for (size_t i = 0; i < vecTableCols.size(); ++i)
    {
        const std::pair<long, long> &cols = vecTableCols[i];
        if (rg.iRight < cols.first || rg.iLeft > cols.second)
            vecToDel.push_back(i);
    }
    for (int j = vecToDel.size() - 1; j >= 0; --j)
    {
        vecTableCols.erase(vecTableCols.begin() + vecToDel[j]);
    }
}

void KIdentifyTable::GetTableRanges(std::vector<IdentifyRange> &vecTables, ISmartAnalysisListener *pListener)
{
    GetSelectionRange();

    // 限制处理的行列数
    if (m_nJsonDataType != JsonDataQABookSource && !AmendRange(m_Range))
        return;

    // 以空列切表
    std::vector<std::pair<long, long>> vecTableCols;
    GetTableCols(vecTableCols, pListener);
    if (pListener && pListener->isStop())
        return;

    if (vecTableCols.empty())
        return;

    for (size_t i = 0; i < vecTableCols.size(); ++i)
        vecTables.push_back(
            IdentifyRange(m_Range.iTop, m_Range.iBottom, vecTableCols[i].first, vecTableCols[i].second));
}

void KIdentifyTable::BuildCells(IDX iSheet, ROW iRow, COL iCol, IdentifyCells &cells)
{
    // strContent
    cells.strContent = IdentifyTool::GetCellText(iRow, iCol, m_spTools);

    // strValue2
    QString strValue2 = IdentifyTool::GetCellValue2(iSheet, iRow, iCol, m_spBookOp);
    if (!strValue2.isEmpty() && strValue2 != cells.strContent)
        cells.strValue2 = strValue2;

    const XF *pXf = NULL;
    m_spBookOp->GetCellFormat(iSheet, iRow, iCol, &pXf, NULL);
    if (!pXf)
        return;
    // numberformat
    if (pXf->pNumFmt)
    {
        ks_bstr bsformat;
        _kso_NF_LocalToBuiltin(pXf->pNumFmt->fmt, &bsformat);
        if (!bsformat.empty() && xstricmp(bsformat.c_str(), __X("General")) != 0 || bsformat.empty())
            cells.strNumberFormat = krt::fromUtf16(pXf->pNumFmt->fmt);
    }
    // fontsize
    if (pXf->pFont)
        cells.fFontSize = ((double)pXf->pFont->dyHeight) / 20;
    // alignment
    HALIGNMENT_ETHAlign(pXf->alcH, (ETHAlign &)cells.nAlignment);

    // merge
    ks_stdptr<ISheet> spSheet = m_spWorksheet->GetSheet();
    RANGE range(spSheet->GetBMP());
    range.SetCell(iSheet, iRow, iCol);
    spSheet->MergeExpandOnce(range);
    if (!range.IsSingleCell())
        cells.ResetRange(&range);
}

void KIdentifyTable::ExportJsonTable(const IdentifyRange &rg, QJsonObject &jsonTable)
{
    IdentifyRange rgTable = m_pTool ? m_pTool->GetExportJsonRange(rg) : rg;
    QJsonArray jsonStart;
    jsonStart.append((qint64)rgTable.iTop);
    jsonStart.append((qint64)rgTable.iLeft);
    jsonTable.insert("tableStart", jsonStart);
    QJsonArray jsonEnd;
    jsonEnd.append((qint64)rgTable.iBottom);
    jsonEnd.append((qint64)rgTable.iRight);
    jsonTable.insert("tableEnd", jsonEnd);
}

bool KIdentifyTable::TransformTable(const IdentifyRange &rg, QJsonObject &jsonTable, ISmartAnalysisListener *pListener)
{
    HRESULT hr = S_CONTINUE;
    ks_stdptr<ISmartAnalysisListener> spListener = pListener;
    int count = 0;
    long nRowCount = rg.iBottom - rg.iTop + 1;
    long nColCount = rg.iRight - rg.iLeft + 1;
    IdentifyMergeCellsList mergeCellsList;
    IDX iSheet = 0;
    m_spWorksheet->GetSheet()->GetIndex(&iSheet);
    RANGE range(m_spWorksheet->GetSheet()->GetBMP());
    range.SetSheetFromTo(iSheet);
    QJsonObject jsonTableInfo;
    long nCells = 0;
    for (long i = 0; i < nRowCount; ++i)
    {
        long iRow = rg.iTop + i;
        if (m_pTool && m_pTool->IsHiddenRow(iRow))
            continue;

        bool isEmptyRow = true;
        QJsonObject jsonRow;
        for (long j = 0; j < nColCount; ++j)
        {
            if (!IsContinue())
                return false;

            if (count++ == 1023)
            {
                QCoreApplication::processEvents();
                if (m_spWorksheet->IsDestroyed())
                    return false;
                if (spListener)
                {
                    if (spListener->isStop())
                        return false;
                    else
                        spListener->UpdateCommand();
                }
                count = 0;
            }

            if (m_spUserRange->IsDestroyed() || !m_spUserRange->IsObjectValid())
                return false;

            long iCol = rg.iLeft + j;
            if (m_pTool && m_pTool->IsHiddenCol(iCol))
                continue;
            if (!AmendRange(iRow, iCol))
                continue;

            const IdentifyCells *pCells = mergeCellsList.FindCellInMergeCells(iRow, iCol);
            range.SetRowFromTo(iRow);
            range.SetColFromTo(iCol);
            IdentifyCells cells(range);
            if (!pCells)
            {
                BuildCells(iSheet, iRow, iCol, cells);
                pCells = &cells;
                if (!cells.IsOneCell())
                    mergeCellsList.push_back(cells);
            }
            if (pCells && IsRightCell(*pCells, iRow, iCol)) // 超过10000行的合并单元格，非左上角的单元格丢弃
            {
                // 将单元格写Json数据
                pCells->ExportJson(jsonRow, m_pTool);
                if (!pCells->strContent.isEmpty())
                {
                    nCells++;
                }
                if (isEmptyRow && !pCells->strContent.isEmpty())
                    isEmptyRow = false;
            }
        }
        // 将整行写Json数据
        if (jsonRow.empty() || isEmptyRow) // 空行不写
            continue;
        QString strKey = QString::number(m_pTool ? m_pTool->GetExportJsonRowIdx(iRow) : iRow);
        jsonTableInfo.insert(strKey, jsonRow);
    }
    // 将整表写Json数据
    ExportJsonTable(rg, jsonTable);
    jsonTable.insert("cellCount", (qint64)nCells);
    jsonTable.insert("tableInfo", jsonTableInfo);
    return true;
}

bool KIdentifyTable::GetTables(std::vector<IdentifyRange> &vecTables, QJsonArray &jsonTableList,
                               ISmartAnalysisListener *pListener)
{
    for (size_t i = 0; i < vecTables.size(); ++i)
    {
        if (!IsContinue())
            return false;
        QJsonObject jsonTable;
        jsonTable.insert("tableIndex", QString::number(i));
        if (!TransformTable(vecTables[i], jsonTable, pListener))
            return false;
        jsonTableList.append(jsonTable);
    }

    return true;
}

void KIdentifyTable::ExportJson(const QJsonArray &jsonTableList, QJsonObject &jsonObj)
{
    jsonObj.insert("tableList", jsonTableList);

    QJsonArray jsonSelectRangeList;
    for (size_t i = 0; i < m_vecSelectRangeList.size(); ++i)
    {
        if (!IsContinue())
            return;
        IdentifyRange rg = m_pTool ? m_pTool->GetExportJsonRange(m_vecSelectRangeList[i]) : m_vecSelectRangeList[i];
        QJsonArray jsonRange; // 上左下右
        jsonRange.append((qint64)rg.iTop);
        jsonRange.append((qint64)rg.iLeft);
        jsonRange.append((qint64)rg.iBottom);
        jsonRange.append((qint64)rg.iRight);
        jsonSelectRangeList.append(jsonRange);
    }
    jsonObj.insert("selectionRangeList", jsonSelectRangeList);
}

void KIdentifyTable::Identify(OUT QJsonObject &obj, ISmartAnalysisListener *pListener)
{
    if (pListener && pListener->isStop())
        return;
    // 根据选区方案获取需要处理的表格区域
    ks_stdptr<ISmartAnalysisListener> spListener = pListener;
    std::vector<IdentifyRange> vecTables;
    GetTableRanges(vecTables, spListener);
    if (pListener && pListener->isStop())
        return;
    // 提取属性
    QJsonArray jsonTableList;
    if (!GetTables(vecTables, jsonTableList, spListener))
        return;
    if (pListener && pListener->isStop())
        return;
    // 转换至Json结构
    ExportJson(jsonTableList, obj);
    // 打开推荐解读
    obj.insert("recommendChart", true);
}

void KIdentifyTable::SetIdentifyTableContinueProc(IdentifyTableContinueProc identifyTableContinueProc)
{
    m_identifyTableContinueProc = identifyTableContinueProc;
}

int KIdentifyTable::GetCellCount() const
{
    return m_nCellCount;
}

void KIdentifyTable::setMaxCellCount(int maxCellCount)
{
    m_maxCellCount = maxCellCount;
}

QString GetRangeString1(etoldapi::Range *pRange)
{
    if (!pRange)
        return QString();
    HRESULT hr = S_OK;
    KComVariant var;
    ks_bstr bstrRange;
    hr = pRange->get_Address(VARIANT_TRUE, VARIANT_TRUE, etA1, VARIANT_TRUE, var, &bstrRange);
    if (FAILED(hr) || bstrRange.empty())
        return QString();
    return krt::fromUtf16(bstrRange.c_str());
}

KIdentifyPivotTableSource::KIdentifyPivotTableSource(IN IKWorksheet* pWorksheet, IN etoldapi::Range *pApiRange)
    : KIdentifyTable(pWorksheet, JsonDataPivotTableSource)
{
    if (!pApiRange)
        return;
    IdentifyRange rg1(m_spUserRange);
    IdentifyRange rg2(pApiRange);
    if (!rg2.IsInRange(rg1))
        m_spUserRange = pApiRange;
    m_Range.ResetRange(m_spUserRange);
}

void KIdentifyPivotTableSource::GetTableRanges(std::vector<IdentifyRange> &vecTables)
{
    vecTables.push_back(m_Range);
}

void KIdentifyPivotTableSource::ExportJson(const QJsonArray &jsonTableList, QJsonObject &jsonObj)
{
    QJsonArray jsonPivotTableList;
    for (int i = 0; i < jsonTableList.size(); ++i)
    {
        if (!jsonTableList[i].isObject())
            continue;
        QJsonObject jsonSourceTable = jsonTableList[i].toObject();
        QJsonObject jsonTable;
        jsonTable.insert("sourceTable", jsonSourceTable);
        jsonPivotTableList.append(jsonTable);
    }
    jsonObj.insert("pivotTableList", jsonPivotTableList);
}

KIdentifyResult::KIdentifyResult(IN IKSheet *pSheet)
{
    if (!pSheet)
        return;
    m_spWorksheet = pSheet;
    if (m_spWorksheet->IsDestroyed())
        return;

    ks_stdptr<Range> spUserRange;
    m_spWorksheet->get_UsedRange(&spUserRange);
    ks_stdptr<ISheet> spSheet = m_spWorksheet->GetSheet();
    RANGE range(spSheet->GetBMP());
    IdentifyTool::GetTableRange(spUserRange, &range);
    m_pTool = new HiddenRowColTool(spSheet, range);
}

KIdentifyResult::~KIdentifyResult()
{
    if (m_pTool)
        delete m_pTool;
}

void KIdentifyResult::TransformRealRange(const QJsonArray &jsonArray, QJsonArray &jsonRange)
{
    if (jsonArray.size() != 4)
        return;
    IdentifyRange rg(jsonArray.at(0).toInt(), jsonArray.at(1).toInt(), jsonArray.at(2).toInt(),
                     jsonArray.at(3).toInt());
    IdentifyRange rgReal;
    if (m_pTool)
        rgReal = m_pTool->GetRealRange(rg);
    jsonRange.insert(0, (qint64)rgReal.iTop);
    jsonRange.insert(1, (qint64)rgReal.iBottom);
    jsonRange.insert(2, (qint64)rgReal.iLeft);
    jsonRange.insert(3, (qint64)rgReal.iRight);
}

void KIdentifyResult::TransformResultJson(JsonDataType nJsonDataType, QJsonObject &jsonObj)
{
    if (nJsonDataType == JsonDataArrangement)
        TransformResultJsonArrangement(jsonObj);
    else if (nJsonDataType == JsonDataProofreading)
        TransformResultJsonProofreading(jsonObj);
}

void KIdentifyResult::TransformResultJsonArrangement(QJsonObject &jsonObj)
{
    if (jsonObj.contains("recommendRgList") && jsonObj["recommendRgList"].isArray())
    {
        QJsonArray jsonRangeListArray = jsonObj["recommendRgList"].toArray();
        QJsonArray jsonRangeList;
        for (int i = 0; i < jsonRangeListArray.size(); ++i)
        {
            if (!jsonRangeListArray[i].isArray())
                continue;
            QJsonArray jsonRangeArray = jsonRangeListArray[i].toArray();
            QJsonArray jsonRange;
            TransformRealRange(jsonRangeArray, jsonRange);
            jsonRangeList.append(jsonRange);
        }
        jsonObj.insert("recommendRgList", jsonRangeList);
    }

    if (jsonObj.contains("sheetAreaList") && jsonObj["sheetAreaList"].isArray())
    {
        QJsonArray jsonTableListArray = jsonObj["sheetAreaList"].toArray();
        QJsonArray jsonTableList;
        for (int i = 0; i < jsonTableListArray.size(); ++i)
        {
            if (!jsonTableListArray[i].isArray())
                continue;
            QJsonArray jsonTableArray = jsonTableListArray[i].toArray();
            QJsonArray jsonTable;
            for (int j = 0; j < jsonTableArray.size(); ++j)
            {
                if (!jsonTableArray[j].isArray())
                    continue;
                QJsonArray jsonZoneArray = jsonTableArray[j].toArray();
                QJsonArray jsonZone;
                if (jsonZoneArray.size() != 2 || !jsonZoneArray[0].isString() || !jsonZoneArray[1].isArray())
                    continue;
                QJsonArray jsonRangeArray = jsonZoneArray[1].toArray();
                QJsonArray jsonRange;
                TransformRealRange(jsonRangeArray, jsonRange);
                jsonZone.append(jsonZoneArray[0]);
                jsonZone.append(jsonRange);
                jsonTable.append(jsonZone);
            }
            jsonTableList.append(jsonTable);
        }
        jsonObj.insert("sheetAreaList", jsonTableList);
    }
}

void KIdentifyResult::TransformError(QJsonObject &jsonCellErrorObj)
{
    if (!m_pTool)
        return;

    if (jsonCellErrorObj.contains("rowIndex"))
    {
        jsonCellErrorObj.insert("rowIndex", (qint64)m_pTool->GetRealRowIdx(jsonCellErrorObj["rowIndex"].toInt()));
    }
    if (jsonCellErrorObj.contains("range") && jsonCellErrorObj["range"].isArray() &&
        jsonCellErrorObj["range"].toArray().size() == 2)
    {
        QJsonArray jsonRangeArray = jsonCellErrorObj["range"].toArray();
        QJsonArray jsonRange;
        jsonRange.append((qint64)m_pTool->GetRealColIdx(jsonRangeArray[0].toInt()));
        jsonRange.append((qint64)m_pTool->GetRealColIdx(jsonRangeArray[1].toInt()));
        jsonCellErrorObj.insert("range", jsonRange);
    }
    if (jsonCellErrorObj.contains("mergeColumnRange") && jsonCellErrorObj["mergeColumnRange"].isArray() &&
        jsonCellErrorObj["mergeColumnRange"].toArray().size() == 2)
    {
        QJsonArray jsonRangeArray = jsonCellErrorObj["mergeColumnRange"].toArray();
        QJsonArray jsonRange;
        jsonRange.append((qint64)m_pTool->GetRealColIdx(jsonRangeArray[0].toInt()));
        jsonRange.append((qint64)m_pTool->GetRealColIdx(jsonRangeArray[1].toInt()));
        jsonCellErrorObj.insert("mergeColumnRange", jsonRange);
    }
}

void KIdentifyResult::TransformResultJsonProofreading(QJsonObject &jsonProofObj)
{
    if (!jsonProofObj.contains("tableErrorList") || !jsonProofObj["tableErrorList"].isArray() ||
        !jsonProofObj.contains("contentErrorList") || !jsonProofObj["contentErrorList"].isArray())
        return;
    QJsonArray jsonTableErrorListArray = jsonProofObj["tableErrorList"].toArray();
    QJsonArray jsonTableErrorList;
    for (int i = 0; i < jsonTableErrorListArray.size(); ++i)
    {
        if (!jsonTableErrorListArray[i].isObject())
            continue;
        QJsonObject jsonTableErrorObj = jsonTableErrorListArray[i].toObject();
        if (jsonTableErrorObj.contains("tableRg") && jsonTableErrorObj["tableRg"].isArray())
        {
            QJsonArray jsonRangeArray = jsonTableErrorObj["tableRg"].toArray();
            QJsonArray jsonRange;
            TransformRealRange(jsonRangeArray, jsonRange);
            jsonTableErrorObj.insert("tableRg", jsonRange);
        }
        if (jsonTableErrorObj.contains("columnErrorList") && jsonTableErrorObj["columnErrorList"].isArray())
        {
            QJsonArray jsonColumnErrorListArray = jsonTableErrorObj["columnErrorList"].toArray();
            QJsonArray jsonColumnErrorList;
            for (int j = 0; j < jsonColumnErrorListArray.size(); ++j)
            {
                QJsonObject jsonColumnErrorObj = jsonColumnErrorListArray[j].toObject();
                if (jsonColumnErrorObj.contains("columnIndex") && m_pTool)
                {
                    jsonColumnErrorObj.insert(
                        "columnIndex", (qint64)m_pTool->GetRealColIdx(jsonColumnErrorObj["columnIndex"].toInt()));
                }
                if (jsonColumnErrorObj.contains("unifiedCellErrorList") &&
                    jsonColumnErrorObj["unifiedCellErrorList"].isArray())
                {
                    QJsonArray jsonUnifiedCellErrorListArray = jsonColumnErrorObj["unifiedCellErrorList"].toArray();
                    QJsonArray jsonUnifiedCellErrorList;
                    for (int k = 0; k < jsonUnifiedCellErrorListArray.size(); ++k)
                    {
                        QJsonObject jsonUnifiedCellErrorObj = jsonUnifiedCellErrorListArray[k].toObject();
                        TransformError(jsonUnifiedCellErrorObj);
                        jsonUnifiedCellErrorList.append(jsonUnifiedCellErrorObj);
                    }
                    jsonColumnErrorObj.insert("unifiedCellErrorList", jsonUnifiedCellErrorList);
                }
                if (jsonColumnErrorObj.contains("normCellErrorList") &&
                    jsonColumnErrorObj["normCellErrorList"].isArray())
                {
                    QJsonArray jsonNormCellErrorListArray = jsonColumnErrorObj["normCellErrorList"].toArray();
                    QJsonArray jsonNormCellErrorList;
                    for (int k = 0; k < jsonNormCellErrorListArray.size(); ++k)
                    {
                        QJsonObject jsonNormCellErrorObj = jsonNormCellErrorListArray[k].toObject();
                        TransformError(jsonNormCellErrorObj);
                        jsonNormCellErrorList.append(jsonNormCellErrorObj);
                    }
                    jsonColumnErrorObj.insert("normCellErrorList", jsonNormCellErrorList);
                }
                jsonColumnErrorList.append(jsonColumnErrorObj);
            }
            jsonTableErrorObj.insert("columnErrorList", jsonColumnErrorList);
        }
        jsonTableErrorList.append(jsonTableErrorObj);
    }
    jsonProofObj.insert("tableErrorList", jsonTableErrorList);

    QJsonArray jsonContentErrorListArray = jsonProofObj["contentErrorList"].toArray();
    QJsonArray jsonContentErrorList;
    for (int j = 0; j < jsonContentErrorListArray.size(); ++j)
    {
        QJsonObject jsonContentErrorObj = jsonContentErrorListArray[j].toObject();
        TransformError(jsonContentErrorObj);
        jsonContentErrorList.append(jsonContentErrorObj);
    }
    jsonProofObj.insert("contentErrorList", jsonContentErrorList);
}

/**
 * @brief 用于获取worksheet中的表格区域识别需要的信息
    参数：nJsonDataType为数据类型，增加支持的功能时追加
        0：智能整理
        1：智能校对
        2：智能分析
 * @return
*/
void GetTableRecogInfo(IKWorksheet *pWorkheet, int nJsonDataType, IdentifyTableContinueProc identifyTableContinueProc,
                       QJsonObject &obj)
{
    KIdentifyTable identify(pWorkheet, (JsonDataType)nJsonDataType);
    identify.SetIdentifyTableContinueProc(identifyTableContinueProc);
    identify.Identify(obj, nullptr);
}

/**
 * @brief 将服务器返回的结果位置对应到worksheet，目前只处理隐藏行列的情况
    参数：nJsonDataType为数据类型，增加支持的功能时追加
        0：智能整理
        1：智能校对
 * @return
*/
void TransformResultJson(IKSheet *pSheet, QJsonObject &obj, int nJsonDataType)
{
    KIdentifyResult identify(pSheet);
    identify.TransformResultJson((JsonDataType)nJsonDataType, obj);
}

//////////////////////////////////////////////////////////////////////////////////////////////
class KGetJsonHelper : public ICellValueAcpt
{
public:
    KGetJsonHelper(QJsonObject &jsonTableInfo, int& nCellCount, ISheet* pSheet, 
        HiddenRowColTool* pTool, IBookOp* pBookOp, IETStringTools* pTools, int maxCellCount, 
        JsonDataType jsonDataType)
        : m_jsonTableInfo(jsonTableInfo)
        , m_nCellCount(nCellCount)
        , m_pSheet(pSheet)
        , m_pTool(pTool)
        , m_range(pSheet->GetBMP())
        , m_spBookOp(pBookOp)
        , m_spTools(pTools)
        , m_maxCellCount(maxCellCount)
        , m_nJsonDataType(jsonDataType)
    {
        pSheet->GetIndex(&m_iSheet);
        m_range.SetSheetFromTo(m_iSheet);
    }
    void finish()
    {
        flushRows(INVALID_ROW);
    }
private:
    STDPROC_(INT) Do(ROW row, COL col, const_token_ptr pToken)
    {
        if (m_pTool && m_pTool->IsHiddenRow(row))
            return 0;

        if (pToken)
        {
            if (m_nCellCount > m_maxCellCount)
            {
                if (m_nJsonDataType == JsonDataQABookSource)
				{
					m_nCellCount++;
					return 0;
				}
                return 1;
            }

            if (alg::const_vstr_token_assist::is_type(pToken))
            {
                QString strClean;
                alg::const_vstr_token_assist str(pToken);
                QString strText = krt::fromUtf16(str.get_value());
                CleanContent(strText, strClean);
                if (!strClean.isEmpty())
                    addCellCol(row, col);
            }
            else
            {
                addCellCol(row, col);
            }
        }
        return 0;
    }
    void addCellCol(ROW row, COL col)
    {
        BOOL bMerged = FALSE;
        m_pSheet->IsMerged(row, col, &bMerged);
        if (!bMerged)
        {
            flushRows(row);
            m_range.SetRowFromTo(row);
            m_range.SetColFromTo(col);
            IdentifyCells cells(m_range);
            BuildCells(row, col, cells);
            addRow(cells, row, col);
            return;
        }

        // 当合并单元格是A1:A3，setNotEmptyCol中需要加入0,1,2
        RANGE rg(m_pSheet->GetBMP());
        m_pSheet->RetieveMerge(row, col, &rg);
        if (row != rg.RowFrom() || col != rg.ColFrom())
            return;

        IdentifyCells cells(rg);
        BuildCells(row, col, cells);
        for (ROW r = rg.RowFrom(); r <= rg.RowTo(); ++r)
        {
            for (COL c = rg.ColFrom(); c <= rg.ColTo(); ++c)
            {
				if (m_nCellCount <= m_maxCellCount)
				{
					addRow(cells, r, c);
				}
				else
				{
					if (m_nJsonDataType == JsonDataQABookSource)
						m_nCellCount++;
					else
						break;
				}
            }
        }
    }

    void addRow(IdentifyCells& cells, ROW row, COL col)
    {
        if (!IsRightCell(cells, row, col))		// 超过10000行的合并单元格，非左上角的单元格丢弃
            return;

        // 将单元格写Json数据
        QJsonObject data = m_jsonRows[row].value("data").toObject();
        bool isEmptyRow = m_jsonRows[row].value("isEmptyRow").toBool(true);

        cells.ExportJson(data, m_pTool);
        if (!cells.strContent.isEmpty())
            m_nCellCount++;
        if (isEmptyRow && !cells.strContent.isEmpty())
            isEmptyRow = false;
        m_jsonRows[row].insert("data", data);
        m_jsonRows[row].insert("isEmptyRow", isEmptyRow);
    }

    void flushRows(ROW row)
    {
        for (auto iter = m_jsonRows.begin(); iter != m_jsonRows.end();)
        {
            ROW cuRow = iter->first;
            if (row == INVALID_ROW || cuRow < row)
            {
                bool isEmptyRow = iter->second.value("isEmptyRow").toBool(true);
                if (!isEmptyRow)
                {
                    QJsonObject data = iter->second.value("data").toObject();
                    QString strKey = QString::number(m_pTool ? m_pTool->GetExportJsonRowIdx(cuRow) : cuRow);
                    m_jsonTableInfo.insert(strKey, data);
                }
                iter = m_jsonRows.erase(iter);
            }
            else
            {
                break;
            }
        }
    }

    void BuildCells(ROW iRow, COL iCol, IdentifyCells& cells)
    {
        // strContent
        cells.strContent = IdentifyTool::GetCellText(iRow, iCol, m_spTools);

        // strValue2
		KComVariant cellVariant = IdentifyTool::GetCellVariant(m_iSheet, iRow, iCol, m_spBookOp);
		QString strValue2 = variantToString(cellVariant);
        if (!strValue2.isEmpty() && strValue2 != cells.strContent)
            cells.strValue2 = strValue2;

        const XF* pXf = NULL;
        m_spBookOp->GetCellFormat(m_iSheet, iRow, iCol, &pXf, NULL);
        if (!pXf)
            return;
        // numberformat
        if (pXf->pNumFmt)
        {
            ks_bstr bsformat;
            _kso_NF_LocalToBuiltin(pXf->pNumFmt->fmt, &bsformat);
            if (!bsformat.empty() && xstricmp(bsformat.c_str(), __X("General")) != 0 || bsformat.empty())
                cells.strNumberFormat = krt::fromUtf16(pXf->pNumFmt->fmt);
        }
        // fontsize
        if (pXf->pFont)
            cells.fFontSize = ((double)pXf->pFont->dyHeight) / 20;
        // alignment
        HALIGNMENT_ETHAlign(pXf->alcH, (ETHAlign&)cells.nAlignment);

		if (JsonDataQABookSource == m_nJsonDataType)
		{
			QVariant& tmp = cells.varValue;
			switch (V_VT(&cellVariant))
			{
			case VT_BOOL:
				tmp.setValue(VARIANT_TRUE == V_BOOL(&cellVariant));
				break;
			case VT_DATE:
				tmp.setValue(strValue2.isEmpty() ? cells.strContent : strValue2);
				break;
			case VT_R8:
				tmp.setValue(V_R8(&cellVariant));
				break;
			default:
				tmp.setValue(cells.strContent);
				break;
			}
		}
    }

private:
    QJsonObject& m_jsonTableInfo;
    int& m_nCellCount;
    ISheet* m_pSheet = nullptr;
    HiddenRowColTool* m_pTool = nullptr;
    RANGE m_range;
    std::unordered_map<ROW, QJsonObject> m_jsonRows;
    ks_stdptr<IBookOp> m_spBookOp;
    ks_stdptr<IETStringTools> m_spTools;
    IDX m_iSheet = 0;
    int m_maxCellCount = MaxCellCountSheetQA;
    JsonDataType m_nJsonDataType = JsonDataArrangement;
};

// 用于统计非空单元格数量
class KCellCountHelper : public ICellAcpt
{
public:
	KCellCountHelper(int& cellCount, bool bCountMergeRetieve, ISheet* pSheet) :
		m_cellCount(cellCount),
		m_bCountMergeRetieve(bCountMergeRetieve),
		m_pSheet(pSheet)
	{}

private:
	STDPROC_(INT) Do(ROW row, COL col)
	{
		if (!m_pSheet)
			return 1;

		BOOL bMerged = FALSE;
		m_pSheet->IsMerged(row, col, &bMerged);
		if (m_bCountMergeRetieve && bMerged)
		{
			RANGE rg(m_pSheet->GetBMP());
			m_pSheet->RetieveMerge(row, col, &rg);
            if (row != rg.RowFrom() || col != rg.ColFrom())
                return 0;

			m_cellCount += rg.CellCount();
		}
		else
		{
			m_cellCount++;
		}

		return 0;
	}

	int& m_cellCount;
	bool m_bCountMergeRetieve = false;
	ISheet* m_pSheet = nullptr;
};

KIdentifyQATable::KIdentifyQATable(IN IKWorksheet* pWorkSheet, IN JsonDataType nJsonDataType)
    : KIdentifyTable(pWorkSheet, nJsonDataType)
{

}

void KIdentifyQATable::Identify(OUT QJsonObject &obj, ISmartAnalysisListener *pListener)
{
    KIdentifyTable::Identify(obj, pListener);

    if (m_spWorksheet->GetSheet())
    {
        const WCHAR* pSheetName = nullptr;
	    m_spWorksheet->GetSheet()->GetName(&pSheetName);
	    obj.insert("sheetname", krt::fromUtf16(pSheetName));
    }

    if (m_spWorksheet->GetWorkbook() && m_spWorksheet->GetWorkbook()->GetBook())
    {
        const WCHAR* pBookName = nullptr;
        m_spWorksheet->GetWorkbook()->GetBook()->GetName(&pBookName);
        obj.insert("bookname", krt::fromUtf16(pBookName));
    }
}

bool KIdentifyQATable::TransformTable(const IdentifyRange &rg, QJsonObject &jsonTable, ISmartAnalysisListener *pListener)
{
    ISheet* pSheet = m_spWorksheet->GetSheet();
	if (!pSheet)
		return false;

	IDX iSheet = 0;
	pSheet->GetIndex(&iSheet);
	et_sptr<ISheetEnum> spEnum;
	pSheet->CreateEnum(&spEnum);
	if (!spEnum)
		return false;

	RANGE range(pSheet->GetBMP());
	range.SetSheetFromTo(iSheet);
	range.SetRowFromTo(rg.iTop, rg.iBottom);
	range.SetColFromTo(rg.iLeft, rg.iRight);

	QJsonObject jsonTableInfo;
	KGetJsonHelper jsonHelper(jsonTableInfo, m_nCellCount,
		pSheet, m_pTool, m_spBookOp, m_spTools, m_maxCellCount, 
        m_nJsonDataType);
	spEnum->EnumCellValueRowbyRow(range, &jsonHelper);
	jsonHelper.finish();

	// 将整表写Json数据
	ExportJsonTable(rg, jsonTable);
	jsonTable.insert("cellCount", m_nCellCount);
	jsonTable.insert("tableInfo", jsonTableInfo);
	return true;
}

KIdentifyQASource::KIdentifyQASource(IKWorkbook* pBook)
	: m_spWorkbook(pBook)
{
	ASSERT(pBook);
}

KIdentifyQASource::~KIdentifyQASource()
{
}

void KIdentifyQASource::Identify(QJsonArray& data, QSet<QString> incrementSheet)
{
	m_nCount = 0;
	IKWorksheets* pWorksheets = m_spWorkbook->GetWorksheets();
	if (!pWorksheets)
		return;

	for (int i = 0; i < pWorksheets->GetSheetCount(); i++)
	{
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(i);
		if (!pWorksheet)
			continue;

		ISheet* pSheet = pWorksheet->GetSheet();
		if (!pSheet)
			continue;
		const WCHAR* pSheetName = nullptr;
		pSheet->GetName(&pSheetName);
        QString name = krt::fromUtf16(pSheetName);

        if (0 == xstrcmp(pSheetName, STR_CELL_IMAGE_SHEET_NAME) ||
            (!incrementSheet.isEmpty() && !incrementSheet.contains(name)))
			continue;

		int cellCount = 0;
		if (!m_bOverFlow)
        {
			QJsonObject jsonObj;
			KIdentifyQATable identify(pWorksheet, JsonDataQABookSource);
        	identify.setMaxCellCount(m_maxSheetCell);
			identify.Identify(jsonObj);
			cellCount = identify.GetCellCount();
			m_nCount += cellCount;

            if (cellCount > m_maxSheetCell || m_nCount > m_maxBookCell)
				m_bOverFlow = true;
			else
				data.append(jsonObj);
        }
        else
        {
			et_sptr<ISheetEnum> spEnum;
			pSheet->CreateEnum(&spEnum);
			if (!spEnum)
				continue;
			RANGE rg(pSheet->GetBMP());
			pWorksheet->GetUsedRange(&rg);
			KCellCountHelper countHelper(cellCount, true, pSheet);
			spEnum->EnumNonEmptyCell(rg, &countHelper);
        }

        m_countCollect.insert(name, cellCount);
	}
}

bool KIdentifyQASource::HasOverflow() const
{
	return m_bOverFlow;
}

void KIdentifyQASource::SetMaxCellLimit(int maxSheetCell, int maxBookCell)
{
	m_maxSheetCell = maxSheetCell > MaxCellCountSheetQA ?
		maxSheetCell : MaxCellCountSheetQA;
	m_maxBookCell = maxBookCell > MaxCellCountBookQA ?
		maxBookCell : MaxCellCountBookQA;
}

QVariantMap KIdentifyQASource::GetCountCollect() const
{
    return m_countCollect;
}

} // namespace etai
