﻿#ifndef __SORT_HELPER_H__
#define __SORT_HELPER_H__

HRESULT ExpandRangeInWorksheet(
	IN IKWorksheet* pWorksheet,
	IN etoldapi::Sort* ptrSort,
	IN IKRanges* pSelection,
	IN bool bExpandRange,
	OUT IKRanges** ppSortRange,
	OUT bool& bTipExpand,
	OUT bool& bSortWithoutExpand
	);

HRESULT ExpandRangeInListObject(
	IKWorksheet* pWorksheet,
	etoldapi::Sort* pSort,
	IKRanges* pSelection,
	IKRanges** ppSortRange);

bool ExpandDataRange(
	IN IKWorksheet* pWorksheetInfo,
	IN const CELL& cell,
	OUT RANGE& rgNew
	);
// 扩展单列区域
bool ExpandDataRange(
	IN IKWorksheet* pWorksheetInfo,
	IN const RANGE& rgSingleCol,
	OUT RANGE& rgNew
	);
BOOL IsSingleCell(IKWorksheet* pWorksheet, const RANGE& rg);
void EraseReduplicateHeaderRow(
	IN IKWorksheet* pWorksheetInfo,
	IN OUT RANGE& rg
	);
// 去掉边界上的空单元格行列 (无值并且无格式)
void EraseEmptyRowColInRange(
	IN IKWorksheet* pWorksheetInfo,
	IN OUT RANGE& rg);
BOOL GetFilterDatabaseRange(ISheet* pSheet, RANGE* pRg);
inline bool IsSingleRowCol(const RANGE& rg)
{
	return (rg.RowFrom() == rg.RowTo()) || (rg.ColFrom() == rg.ColTo());
}
BOOL IsRangeEmpty(IKWorksheet* pWorksheet, const RANGE& rg);

//返回是不是在筛选里面
BOOL GetSortByRange(_Worksheet* pSheet, const RANGE& rg, bool onlyEqual, etoldapi::Sort** ppSort, bool *pIsListObject = NULL);

#endif //__SORT_HELPER_H__
