﻿#include "etstdafx.h"
#include "workbook.h"
#include "et_revision_context_impl.h"
#include "et_task_peripheral.h"
#include "insertpicture/insertpicture_helper.h"
#include "kso/l10n/et/etshell.h"
#include "et_form_task_class.h"
#include "helpers/webmime_helper.h"
#include "util.h"
#include "et_task_detail_xf.h"
#include "applogic/et_apihost.h"
#include "helpers/autofilter_helper.h"
#include "et_binvar_spec.h"
#include "webbase/memo_stat_common.h"

extern Callback* gs_callback;

namespace wo
{

#define WO_FORM_AREA_NAME_PREFIX  __X("WebWps_Form")
#define WO_FORM_AREA_NAME__PREFIX_LEN  11

bool isFormSheet(IBook* pBook, IDX sheetIdx)
{
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);

	INT nNameCount = 0;
	spBookOp->GetNameUdfCount(&nNameCount);
	for (INT iName = 0; iName < nNameCount; ++iName)
	{
		IDX iSheet = 0;
		PCWSTR strName = NULL;
		DEFNAME_ATTRIB Attr;
		HRESULT  hr = spBookOp->GetDefinedNameInfo(iName, &iSheet, &strName, &Attr);
		if (FAILED(hr) || iName == alg::STREF_INV_NAME)
			continue;

		if (xstrncmp(strName, WO_FORM_AREA_NAME_PREFIX, WO_FORM_AREA_NAME__PREFIX_LEN) != 0)
			continue;

		RANGE nameRg(pBook->GetBMP());
		TaskExecFormHelp::GetNameRg(spBookOp, iName, nameRg);
		ASSERT(nameRg.SheetFrom() == nameRg.SheetTo());
		if (nameRg.SheetFrom() == sheetIdx)
		{
			return true;
		}

	}
	return false;
}

bool  isFormBook(IBook* pBook)
{
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);

	INT nSheetCount = 0;
	pBook->GetSheetCount(&nSheetCount);
	INT nNameCount = 0;
	spBookOp->GetNameUdfCount(&nNameCount);
	for (INT iName = 0; iName < nNameCount; ++iName)
	{
		IDX iSheet = 0;
		PCWSTR strName = NULL;
		DEFNAME_ATTRIB Attr;
		HRESULT  hr = spBookOp->GetDefinedNameInfo(iName, &iSheet, &strName, &Attr);
		if (FAILED(hr) || iName == alg::STREF_INV_NAME)
			continue;

		if (xstrncmp(strName, WO_FORM_AREA_NAME_PREFIX, WO_FORM_AREA_NAME__PREFIX_LEN) != 0)
			continue;

		RANGE nameRg(pBook->GetBMP());
		TaskExecFormHelp::GetNameRg(spBookOp, iName, nameRg);
		ASSERT(nameRg.SheetFrom() == nameRg.SheetTo());
		int sheetIndex = nameRg.SheetFrom();
		if (sheetIndex >=0 && sheetIndex < nSheetCount)
		{
			return true;
		}

	}
	return false;
}

int  getFormCount(IBook* pBook)
{
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);

	int formCount = 0;
	INT nSheetCount = 0;
	pBook->GetSheetCount(&nSheetCount);
	INT nNameCount = 0;
	spBookOp->GetNameUdfCount(&nNameCount);
	for (INT iName = 0; iName < nNameCount; ++iName)
	{
		IDX iSheet = 0;
		PCWSTR strName = NULL;
		DEFNAME_ATTRIB Attr;
		HRESULT  hr = spBookOp->GetDefinedNameInfo(iName, &iSheet, &strName, &Attr);
		if (FAILED(hr) || iName == alg::STREF_INV_NAME)
			continue;

		if (xstrncmp(strName, WO_FORM_AREA_NAME_PREFIX, WO_FORM_AREA_NAME__PREFIX_LEN) != 0)
			continue;

		RANGE nameRg(pBook->GetBMP());
		TaskExecFormHelp::GetNameRg(spBookOp, iName, nameRg);
		ASSERT(nameRg.SheetFrom() == nameRg.SheetTo());
		int sheetIndex = nameRg.SheetFrom();
		if (sheetIndex >=0 && sheetIndex < nSheetCount)
		{
			++formCount;
		}

	}
	return formCount;
}

// ================== TaskExecFormHelp ==================
HRESULT TaskExecFormHelp::GetFormRg(IBook* pBook, KEtRevisionContext* ctx, RANGE& rg,const ks_wstring& areaName)
{
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);
	IDX iName = alg::STREF_INV_NAME;
	spBookOp->FindDefinedName(alg::STREF_NO_SHEET, areaName.c_str(), &iName);
	if(iName == alg::STREF_INV_NAME)
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_ERROR, "form name not found");
		return E_FAIL;
	}
	GetNameRg(spBookOp, iName, rg);
	if(!rg.IsValid())
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_ERROR, "form range invalid");
		return E_FAIL;
	}
	return S_OK;
}

void TaskExecFormHelp::GetNameRg(IBookOp* pBookOp, IDX iName, RANGE& rg) 
{
	rg.Invalidate();
	ks_stdptr<IFormula> spFmla;
	pBookOp->GetDefinedNameContent(iName, &spFmla);
	if (!spFmla)
	{
		return;
	}

	BOOL bFmla = FALSE;
	ks_stdptr<ITokenVectorInstant> spVec;
	spFmla->GetContent(&bFmla, &spVec, NULL);
	if(!bFmla)
		return;
	int size = 0;
	spVec->GetSize(&size);
	if(size != 1)
		return;
	const_token_ptr pTok = NULL;
	spVec->GetItem(0, &pTok);
	if(!alg::const_stref_token_assist::is_type(pTok))
		return;

	alg::const_stref_token_assist rcs(pTok);
	if (rcs.get_sheet_id() < 0)	// 相对sheet引用
		return;

	rg.FromStrefToken(pTok);
	if (!rg.IsValid()) {
		//只有sheet有效时需要知道
		rg.SetSheetFromTo(rcs.get_sheet_id());
	}
}
// ================== TaskExecFormHelp ==================

// ================== TaskExecFormBase ==================
TaskExecFormBase::TaskExecFormBase(KEtWorkbook* wwb)
	: EtTaskExecBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

bool TaskExecFormBase::SerialCommand(KwCommand* cmd, ISerialAcceptor* acpt)
{
	binary_wo::VarObjRoot root(new binary_wo::BinVarRoot());
	binary_wo::VarObj::_copyStruct(root.get(), cmd->getVar(), root.get());

	binary_wo::VarObj param = root.cast().get("param");
	if (param.has("items"))
	{
		addItemsMimeData(param.get("items"));
	}
	else if (param.has("rows"))
	{
		binary_wo::VarObj rows = param.get("rows");
		for (int i = 0; i < rows.arrayLength_s(); i++)
		{
			binary_wo::VarObj row = rows.at(i);
			if (row.has("items"))
				addItemsMimeData(row.get("items"));
		}
	}

	return TaskExecHelperSerial(m_wwb).CommandWithExtraData(root.cast(), acpt);
}

bool TaskExecFormBase::addItemsMimeData(binary_wo::VarObj objVec)
{
	WebMimeHelper wmh;
	wmh.strField = "mimeDataID";
	for (int32 i = 0, n = objVec.arrayLength_s(); i < n; ++i)
	{
		binary_wo::VarObj item = objVec.at_s(i);

		bool bItemsWithType = isItemsWithType(item);

		if (!item.has("type") || !bItemsWithType)
		{
			continue;
		}

		WebStr typeStr = item.field_str("type");
		if (typeStr == nullptr || xstrcmp(__X("cellPicture"), typeStr) != 0)
		{
			continue;
		}

		wmh.objParam = item;
		
		if (!wmh.addMimeStream(wmh.objParam, "mimeDataStream"))
		{
			return false;
		}
	}

	return true;
}

HRESULT TaskExecFormBase::handleFormula(binary_wo::VarObj param, const RANGE &rg, PCWSTR fmla, KEtRevisionContext* ctx)
{
	if(isFormulaStr(fmla))
	{
		ks_wstring strFmla;
		strFmla.clear();
		strFmla.push_back(__Xc('\''));
		strFmla.append(fmla);
		fmla = strFmla.c_str();
	}

	return SetCellFormula(param, rg, fmla, ctx);
}

//临时代码 {

static void collectFormAddRowError(HRESULT hr, PCWSTR strType, PCWSTR strMsg = nullptr)
{
	binary_wo::BinWriter binWriter;
	binWriter.addKey("hr");
	binWriter.addInt32(hr);
	binWriter.addKey("type");
	binWriter.addString(strType);
	if(strMsg)
	{
		binWriter.addKey("msg");
		binWriter.addString(strMsg);
	}

	binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
	WebSlice slice = {shbt.get(), binWriter.writeLength()};
	gs_callback->collectInfo("formaddrowerror", &slice);
}

//} 临时代码

HRESULT TaskExecFormBase::handleCellPicture(const RANGE &rg, bool isUrl, WebMimeHelper wmh)
{
	single width = -1;
	single height = -1;
	PCWSTR strUrl = NULL;
	PCWSTR strPath = NULL;
	QString path;

	binary_wo::VarObj widthVar = wmh.objParam.get_s("width");
	if (widthVar.type() != binary_wo::typeInvalid)
		width = widthVar.value_double();
	binary_wo::VarObj heightVar = wmh.objParam.get_s("height");
	if (heightVar.type() != binary_wo::typeInvalid)
		height = heightVar.value_double();

	if(isUrl)
	{
		strUrl = wmh.objParam.field_str("url");
	}
	else 
	{
		wmh.strField = "mimeDataID";
		WebInt count = 0;
		path = wmh.resolvePath(&count);
		if (path.isEmpty())
		{
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] invalid mime data");

			PCWSTR msg = NULL;
			ks_wstring str;
			str.Format(__X("count: %d"), count);
			msg = str.c_str();
			collectFormAddRowError(E_FAIL, __X("setCellPicture invalid mime data"), msg);
			return E_FAIL;
		}
		strPath = krt::utf16(path);
	}

	ks_wstring uuid;
	if (wmh.objParam.has("uuid"))
		uuid = wmh.objParam.field_str("uuid");

	ks_stdptr<_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(rg.SheetFrom());
	ks_stdptr<IKShape> spShape;
	// 生成的uuid需要存起来, 不然回放的时候会不一致
	HRESULT hr = spWorksheet->InsertCellPicture(rg.RowFrom(), rg.ColFrom(), strPath, uuid, strUrl, width, height, FALSE, &spShape);
	if (FAILED(hr)) 
	{
		collectFormAddRowError(hr, __X("setCellPicture Insert pic"), krt::utf16(path));
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] insert shape failed");
	}
	else
	{
		wmh.ctx->setIsRealTransform(true);
		wmh.objParam.add_field_str("uuid", uuid.c_str());
	}
	return hr;
}

HRESULT TaskExecFormBase::handleHyperlink(const RANGE &rg, binary_wo::VarObj item)
{
	QString strUrl = QString::fromUtf16(item.field_str("url"));
	strUrl.replace("\"", "\"\"");
	strUrl.replace("＂", "＂＂");

	QString strDispText;
	if (item.has("dispText"))
	{
		strDispText = QString::fromUtf16(item.field_str("dispText"));
		strDispText.replace("\"", "\"\"");
		strDispText.replace("＂", "＂＂");
	}

	ks_wstring fmla;
	if (strDispText.isEmpty())
		fmla.Format(__X("=HYPERLINK(\"%s\")"), krt::utf16(strUrl));
	else
		fmla.Format(__X("=HYPERLINK(\"%s\", \"%s\")"), krt::utf16(strUrl), krt::utf16(strDispText));

	ks_stdptr<IRangeInfo> host = m_wwb->CreateRangeObj(rg);

	RANGE ref(m_wwb->GetBMP());
	ref.SetCell(rg.SheetFrom(), rg.RowFrom(), rg.ColFrom());

	HRESULT hr = host->SetFormula(fmla.c_str(), &ref);
	if (FAILED(hr)) 
	{
		collectFormAddRowError(hr, __X("insert hyperlink SetFormula fail"), fmla.c_str());
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] insert hyperlink failed");
	}

	return hr;
}

HRESULT TaskExecFormBase::handleItems(
	WebMimeHelper wmh, RANGE &rg, IDX colFrom,  const std::vector<uint32>& colsIndex, bool bItemsWithType, KEtRevisionContext* ctx)
{
	binary_wo::VarObj objVec = wmh.objParam;
	ASSERT(objVec.type() == binary_wo::typeArray);

	HRESULT hr = S_OK;
	for (int i = 0, cnt = objVec.arrayLength_s(); i < cnt; ++i)
	{
		IDX col = colsIndex.size() < cnt ? colFrom + i : colFrom + colsIndex[i];
		rg.SetColFromTo(col);
		
		if (bItemsWithType)
		{
			binary_wo::VarObj item = objVec.at_s(i);
			if (item.type() == binary_wo::typeInvalid)
			{
				WO_LOG_X(m_wwb->getLogger(), WO_LOG_WARN, "[FORM] skipped invalid item");
				collectFormAddRowError(S_FALSE, __X("skipped invalid item"));
				continue;
			}

			WebStr typeStr = item.field_str("type");

			if (xstrcmp(__X("formula"), typeStr) == 0)
			{
				PCWSTR fmla = item.field_str("fmla");
				hr = handleFormula(item, rg, fmla, ctx);
				if(FAILED(hr))
				{
					WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] set formula failed, hr:%x", hr);
					collectFormAddRowError(hr, __X("set formula failed"));
					return hr;
				}
			}
			else if (xstrcmp(__X("cellPicture"), typeStr) == 0)
			{
				wmh.objParam = item;
				hr = handleCellPicture(rg, false, wmh);
				if(FAILED(hr))
				{
					WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] set cell picture failed, hr:%x", hr);
					return hr;
				}
			}
			else if (xstrcmp(__X("cellPictureUrl"), typeStr) == 0)
			{
				wmh.objParam = item;
				hr = handleCellPicture(rg, true, wmh);
				if(FAILED(hr))
				{
					WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] set cell picture URL failed, hr:%x", hr);
					return hr;
				}
			}
			else if (xstrcmp(__X("hyperlink"), typeStr) == 0)
			{
				hr = handleHyperlink(rg, item);
				if(FAILED(hr))
				{
					WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] set hyperlink failed, hr:%x", hr);
					return hr;
				}
			}
			else
			{
				WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] invalid item type");
				collectFormAddRowError(S_FALSE, __X("invalid item type"));
				continue;
			}
		}
		else
		{
			// 兼容旧数据
			PCWSTR fmla = objVec.item_str(i);

			hr = handleFormula(binary_wo::VarObj(), rg, fmla, ctx);
			if(FAILED(hr))
			{
				WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "[FORM] set formula failed (from compatible data), hr:%x", hr);
				collectFormAddRowError(hr, __X("set formula failed"));
				return hr;
			}
		}
	}

	return hr;
}

bool TaskExecFormBase::FindModifyRg(PCWSTR strKey, int iFieldFind, RANGE& rg)
{
	if (iFieldFind < 0)
		return false;

	RANGE rgFind(rg);
	rgFind.SetColFromTo(rg.ColFrom() + iFieldFind);
	ks_stdptr<IRangeInfo> spRgFind = m_wwb->CreateRangeObj(rgFind);
	ks_stdptr<IAppCoreRange> spCoreRgFind;
	spRgFind->GetAppCoreRange(&spCoreRgFind);

	FINDPARAM param ;
	param.sWhat.reserve(20);
	for (PCWSTR pCh = strKey; *pCh != __Xc('\0'); ++pCh)
	{
		switch(*pCh) 
		{
		case __Xc('?'):
		case __Xc('~'):
		case __Xc('*'):
			param.sWhat.push_back(__Xc('~'));
			break;
		}
		param.sWhat.push_back(*pCh);
	}

	param.cellAfter.row = rg.RowFrom();
	param.cellAfter.col = rg.ColFrom();
	param.nACinRANGE = 0;
	param.efcLookIn = etFind_Smart;
	param.efcLookAt = etFind_Whole;
	param.bMatchCase = TRUE;
	param.bMatchByte = TRUE;
	param.bfindAll = TRUE;
	CELL cellFound = {0};
	HRESULT hr = spCoreRgFind->Find(param, cellFound, param.nACinRANGE);
	if (SUCCEEDED(hr) && cellFound.row != -1)
	{
		ASSERT(rgFind.ColFrom() == cellFound.col);
		rg.SetRowFromTo(cellFound.row);
		return true;
	}
	return false;
}

bool TaskExecFormBase::isItemsWithType(binary_wo::VarObj param)
{
	if (param.has("itemsWithTypeInt"))
		return param.field_int32("itemsWithTypeInt");
	if (param.has("itemsWithType"))
		return param.field_bool("itemsWithType");
	return false;
}

int TaskExecFormBase::getIntS(const binary_wo::VarObj obj, WebName name, int defVal)
{
	binary_wo::VarObj objVal = obj.get_s(name);
	int32 val = defVal;
	if (objVal.type() != binary_wo::typeInvalid)
		val = objVal.value_int32();
	return val;
}

ks_wstring TaskExecFormBase::getAreaName(VarObj obj)
{
	ks_wstring strAreaName = WO_FORM_AREA_NAME_PREFIX;
	if (obj.has("area_name"))
	{
		ks_wstring formid = obj.field_str("area_name");
		if(!formid.empty())
		{
			strAreaName = strAreaName + __X("_") + formid;
		}
	}
	return strAreaName;
}
// ================== TaskExecFormBase ==================

// ================== TaskExecFormAddRow ==================
TaskExecFormAddRow::TaskExecFormAddRow(KEtWorkbook* wwb)
	: TaskExecFormBase(wwb)
{}

HRESULT TaskExecFormAddRow::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	CALL_TIME_STAT("TaskExecFormAddRow");
	util::IgnoreHistoryGuard ignoreHistoryGuard(pCtx);
	const binary_wo::VarObj param = cmd->cast().get("param");
	binary_wo::VarObj objVec = param.get("items");
	if(objVec.arrayLength() < 1)
	{
		WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] items empty");
		collectFormAddRowError(E_INVALIDARG, __X("items empty"));
		return E_INVALIDARG;
	}

    AddRowMode addRowMode = InsertMode;
	if(param.has("addRowMode"))
	{
		addRowMode = strToAddRowMode(param.field_str("addRowMode"));
	}
	SetBreak(true); 
	app_helper::KBatchUpdateCal batchUpdate(m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator()); // 不重复做计算扩散
	RANGE rg(m_wwb->GetCoreWorkbook()->GetBook()->GetBMP());
	HRESULT hr = GainWriteRg(param, addRowMode, 1, objVec.arrayLength(), rg, pCtx);
	if(!rg.IsValid())
	{
		WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] invalid range, hr:%x", hr);
		collectFormAddRowError(hr, __X("GainWriteRg"));
		return hr;
	}

	bool bItemsWithType = isItemsWithType(param);

	COL colFrom = rg.ColFrom();
	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx;
	wmh.objParam = objVec;
	wmh.setHasExtra();
	hr = handleItems(wmh, rg, colFrom, std::vector<uint32>(), bItemsWithType, pCtx);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT TaskExecFormAddRow::InitNewRg(IBookOp* pBookOp, IDX iName, int rowCnt, int fieldsCnt, RANGE& rg, KEtRevisionContext* pCtx)
{
	if (rg.SheetFrom() != rg.SheetTo() || rg.SheetFrom() <0)
	{
		ks_stdptr<ISheet> spSheet;
		m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(0, &spSheet);
		if (!spSheet->IsGridSheet())
			return E_FAIL;
		rg.SetSheetFromTo(0);
	}

	rg.SetRowFromTo(0, rowCnt - 1);
	rg.SetColFromTo(0, fieldsCnt - 1);
	HRESULT hr = InsertRangeEx(rg, etShiftDown, pCtx);
	if(SUCCEEDED(hr))
		SetNameRg(pBookOp, iName, rg);
	else
		rg.Invalidate();
	return hr;
}

HRESULT TaskExecFormAddRow::GainWriteRg(const binary_wo::VarObj& param, AddRowMode rowMode, int rowsCnt, int fieldsCnt, RANGE& rg, KEtRevisionContext* pCtx)
{
	int rowFrom = -1;
	if(param.has("rowFrom"))
	{
		rowFrom = param.field_int32("rowFrom");
	}
	const ks_wstring& areaName = getAreaName(param);
	rg.Invalidate();
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);
 
	IDX iName = alg::STREF_INV_NAME;
	spBookOp->FindDefinedName(alg::STREF_NO_SHEET, areaName.c_str(), &iName);
	if(iName == alg::STREF_INV_NAME)
	{
		if(WO_FORM_AREA_NAME_PREFIX != areaName) //删除指定的名称管理
			return E_FAIL;
		HRESULT hr = spBookOp->DefineName(alg::STREF_NO_SHEET, areaName.c_str(), DefNameAttr_Normal, &iName);
		if(iName != alg::STREF_INV_NAME)
			hr = InitNewRg(spBookOp, iName, rowsCnt, fieldsCnt, rg, pCtx);
		return hr;
	}
	TaskExecFormHelp::GetNameRg(spBookOp, iName, rg);
	if(!rg.IsValid())
	{
		if(rg.SheetFrom() < 0  && WO_FORM_AREA_NAME_PREFIX != areaName) //删除了关联的表格
			return E_FAIL;
		return InitNewRg(spBookOp, iName, rowsCnt, fieldsCnt, rg, pCtx);
	}
	
	rg.SetColTo(std::max<IDX>(rg.ColTo(), fieldsCnt - 1));

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(rg.SheetFrom(), &spSheet);

	RANGE rgData = rg;
	rgData.SetRowTo(rg.RowTo() + rowsCnt);
	int row = rowFrom;
	if (rowFrom < 0 || rowFrom > spSheet->GetBottom())
	{
		row = rg.RowTo() + 1 ;
	}
	rg.SetRowTo(row + rowsCnt -1);	
	rg.SetRowFrom(row);


	HRESULT hr = S_OK;
	if (rowMode == InsertMode && rg.RowFrom() <= spSheet->GetBottom())
	{
		hr = InsertRangeEx(rg, etShiftDown, pCtx);
	}

	if(SUCCEEDED(hr))
		SetNameRg(spBookOp, iName, rgData);
	else
		rg.Invalidate();
	return hr;
}

#define ADD_STR_TO_ROWMODE_ITEM(type) std::make_pair(_Key(__X(#type)), type)
TaskExecFormAddRow::AddRowMode  TaskExecFormAddRow::strToAddRowMode(WebStr str)
{
    using _Key = ks_wstring;
    using _Value = AddRowMode;
    using _Hasher = kfc::tools::str_hash_ic<_Key>;
    using _Cmp = kfc::tools::str_equal_ic<_Key>;
    using StringToRowModeMap = std::unordered_map<_Key, _Value, _Hasher, _Cmp>;

    static StringToRowModeMap s_map = 
    {
        ADD_STR_TO_ROWMODE_ITEM(InsertMode),
        ADD_STR_TO_ROWMODE_ITEM(CoverNullCellMode),
    };

    if (s_map.count(str) > 0)
        return s_map[str];

    ASSERT(FALSE);
    return InsertMode;
}

PCWSTR TaskExecFormAddRow::GetTag()
{
	return __X("form.addRow");
}
// ================== TaskExecFormAddRow ==================

// ================== TaskExecFormAddRows ==================
TaskExecFormAddRows::TaskExecFormAddRows(KEtWorkbook* wwb)
	: TaskExecFormAddRow(wwb), m_rowCount(0)
{}

HRESULT TaskExecFormAddRows::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	CALL_TIME_STAT("TaskExecFormAddRows");
	util::IgnoreHistoryGuard ignoreHistoryGuard(pCtx);
	const binary_wo::VarObj param = cmd->cast().get("param");
	binary_wo::VarObj rows = param.get("rows");
	ASSERT(rows.type() == binary_wo::typeArray);
	cmd->setHasExtra();
	pCtx->setIsRealTransform(true);

	if(rows.arrayLength() < 1)
	{
		WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] rows empty");
		collectFormAddRowError(E_INVALIDARG, __X("rows empty"));
		return E_INVALIDARG;
	}

	ks_stdptr<IBookOp> spBookOp;
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
	if (spBookOp == nullptr)
		return E_FAIL;
	
	bool bItemsWithType = isItemsWithType(param);
	int fieldsCnt = 0;

	//指定行数据插入的列索引
	std::vector<uint32> colsIndexVec;
	if (param.has("colsIndex"))
	{
		VarObj objVec = param.get("colsIndex");
		UINT count = objVec.arrayLength_s();
		colsIndexVec.reserve(count);
		for (UINT i = 0; i < count; ++i)
			colsIndexVec.push_back(objVec.item_uint32(i));
		if (!colsIndexVec.empty())
			fieldsCnt = *(std::max_element(colsIndexVec.begin(), colsIndexVec.end())) + 1;
	}
	for (int i = 0; i < rows.arrayLength_s(); i++)
	{
		VarObj row = rows.at_s(i);
		VarObj objVec = row.get("items");
		fieldsCnt = std::max(objVec.arrayLength(), fieldsCnt);
	}

    AddRowMode addRowMode = InsertMode;
	if(param.has("addRowMode"))
	{
		addRowMode = strToAddRowMode(param.field_str("addRowMode"));
	}
	SetBreak(true); 
	app_helper::KBatchUpdateCal batchUpdate(spBookOp); // 不重复做计算扩散
	RANGE rowsRg(m_wwb->GetCoreWorkbook()->GetBook()->GetBMP());
	HRESULT hr = GainWriteRg(param, addRowMode,rows.arrayLength_s(), fieldsCnt, rowsRg, pCtx);
	if(!rowsRg.IsValid())
	{
		WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] invalid range, hr:%x", hr);
		collectFormAddRowError(hr, __X("GainWriteRg"));
		return hr;
	}

    bool needDeduplication = false;
    ks_stdptr<IAppCoreRange> spCoreRgFind;
    int fieldFind = 0;
    FINDPARAM findParam;
    if (param.has("scanBackCount") && param.has("fieldFind"))
    {
        int scanBackCount = param.field_int32("scanBackCount");
        fieldFind = param.field_int32("fieldFind");
        if (scanBackCount < 0)
        {
            WO_LOG_X(pCtx->getLogger(), WO_LOG_WARN, "[FORM] scanBackCount invalid");
            return E_INVALIDARG;
        }
        if (fieldFind < 0)
        {
            WO_LOG_X(pCtx->getLogger(), WO_LOG_WARN, "[FORM] fieldFind invalid");
            return E_INVALIDARG;
        }
        // 没有scanBackCount参数时，服务端默认传0
        if (scanBackCount != 0)
        {
            needDeduplication = true;
            PreCheckForDuplicate(scanBackCount, fieldFind, &spCoreRgFind, findParam, rowsRg);
        }
    }

	IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(rowsRg.SheetFrom());
	ROW  startRow = rowsRg.RowFrom();
	int count = rows.arrayLength_s();
    int dupCount = 0;
	for (int i = 0; i < count; i++)
	{
		binary_wo::VarObj item = rows.at_s(i);
		binary_wo::VarObj objVec = item.get("items");

		if(objVec.arrayLength() < 1)
		{
			WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] items empty");
			collectFormAddRowError(E_INVALIDARG, __X("items empty"));
			return E_INVALIDARG;
		}

		RANGE rg(rowsRg);
        if (needDeduplication) 
        {
            if (fieldFind > objVec.arrayLength() - 1)
            {
                WO_LOG_X(pCtx->getLogger(), WO_LOG_WARN, "[FORM] fieldFind invalid");
                return E_INVALIDARG;
            }
            binary_wo::VarObj obj = objVec.at_s(fieldFind);
            if (obj.type() == binary_wo::typeInvalid)
                continue;

            WebStr typeStr = obj.field_str("type");
			if (xstrcmp(__X("formula"), typeStr) == 0)
            {
                PCWSTR fmla = obj.field_str("fmla");
                // 查找时比对的时cellInfo，需要去掉字符串前的“'”符号
                if (*fmla == __Xc('\''))
                    fmla++;
                findParam.sWhat = fmla;
            }
            CELL cellFound = {0};
            HRESULT hr = spCoreRgFind->Find(findParam, cellFound, findParam.nACinRANGE);
            if (SUCCEEDED(hr) && cellFound.row != -1)
            {
                ++dupCount;
                continue;
            }
        }

		if (addRowMode == CoverNullCellMode && startRow <= pWorkSheet->GetSheet()->GetBottom())
		{
			for(;startRow <= pWorkSheet->GetSheet()->GetBottom(); ++startRow)
			{
				rg.SetRowFromTo(startRow);
				if(!currentRowHasContent(rg, colsIndexVec))
					break;
			}	
		}

		if (addRowMode == CoverNullCellMode && i == count - 1)
		{
			updateNameRg(startRow,getAreaName(param));
		}
		rg.SetRowFromTo(startRow);
		++startRow;

		WebMimeHelper wmh;
		wmh.cmd = cmd, wmh.ctx = pCtx;
		wmh.objParam = objVec;
		hr = handleItems(wmh, rg, rg.ColFrom(), colsIndexVec, bItemsWithType, pCtx);
		if (FAILED(hr))
			return hr;	
	}
	m_rowCount += rows.arrayLength_s() - dupCount;

	if (m_rowCount > 2000 && MemoStatProcMemory() > 1024ULL * 1024ULL *1024ULL)
	{
		m_rowCount = 0;
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
	}
    
    if (needDeduplication && dupCount != 0)
    {
        updateNameRg(rowsRg.RowTo() - dupCount, getAreaName(param));
        if (dupCount == rows.arrayLength_s())
            return E_KSHEET_ADD_ROWS_ALL_DUP_VAL;
        else
            return E_KSHEET_ADD_ROWS_HAS_DUP_VAL;
    }

	return S_OK;
}

void TaskExecFormAddRows::updateNameRg(ROW lastRow,const ks_wstring& areaName)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);
 
	IDX iName = alg::STREF_INV_NAME;
	spBookOp->FindDefinedName(alg::STREF_NO_SHEET, areaName.c_str(), &iName);
	if(iName == alg::STREF_INV_NAME)
		return;
	RANGE rg(m_wwb->GetCoreWorkbook()->GetBook()->GetBMP());
	TaskExecFormHelp::GetNameRg(spBookOp, iName, rg);
	if(!rg.IsValid())
		return;
	rg.SetRowTo(lastRow);
	SetNameRg(spBookOp, iName, rg);
}

bool TaskExecFormAddRows::currentRowHasContent(const RANGE& rg, const std::vector<uint32>& colsIndexVec)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);

    if (!colsIndexVec.empty())
    {
		for (size_t i = 0; i < colsIndexVec.size(); ++i)
        {
            const_token_ptr pToken = nullptr;
            spBookOp->GetCellValue(rg.SheetFrom(), rg.RowFrom(), rg.ColFrom() + colsIndexVec[i], &pToken);
            if (pToken)
                return true;
        }
    }
    else
    {
        for (COL col = rg.ColFrom(); col <= rg.ColTo(); ++col)
        {
            const_token_ptr pToken = nullptr;
            spBookOp->GetCellValue(rg.SheetFrom(), rg.RowFrom(), col, &pToken);
            if (pToken)
                return true;
        }
    }

    return false;
}

void TaskExecFormAddRows::PreCheckForDuplicate(int scanBackCount, int field, IAppCoreRange** ppCoreRange, FINDPARAM& findParam, RANGE& rg)
{
    RANGE rgFind(rg);
    rgFind.SetColFromTo(rg.ColFrom() + field);
    ROW scanRowFrom = rg.RowTo() - scanBackCount > 0 ? rg.RowTo() - scanBackCount : 0;
    ROW scanRowTo = rg.RowTo() - 1 > 0 ? rg.RowTo() - 1 : 0;
    rgFind.SetRowFromTo(scanRowFrom, scanRowTo);
    ks_stdptr<IRangeInfo> spRgFind = m_wwb->CreateRangeObj(rgFind);
    ks_stdptr<IAppCoreRange> spCoreRgFind;
    spRgFind->GetAppCoreRange(ppCoreRange);

    findParam.cellAfter.row = rgFind.RowFrom();
    findParam.cellAfter.col = rgFind.ColFrom();
    findParam.next = dirDown;
    findParam.efcLookIn = etFind_Formulas;
    findParam.efcLookAt = etFind_Part;
    findParam.bMatchCase = TRUE;
    findParam.bMatchByte = TRUE;
    findParam.bAutoExpand = FALSE;
}

PCWSTR TaskExecFormAddRows::GetTag()
{
	return __X("form.addRows");
}
// ================== TaskExecFormAddRows ==================

// ================== TaskExecFormAddCol ==================
TaskExecFormAddCol::TaskExecFormAddCol(KEtWorkbook* wwb)
	: TaskExecFormBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecFormAddCol::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	SetBreak(true); 
	CALL_TIME_STAT("TaskExecFormAddCol");
	util::IgnoreHistoryGuard ignoreHistoryGuard(pCtx);
	binary_wo::VarObj param = cmd->cast().get("param");

	RANGE rg(m_wwb->GetCoreWorkbook()->GetBook()->GetBMP());
	HRESULT hr = GainWriteRg(param, rg, pCtx,getAreaName(param));
	if(!rg.IsValid())
		return hr;

	return S_OK;
}

HRESULT TaskExecFormAddCol::GainWriteRg(const binary_wo::VarObj& param, RANGE& rg, KEtRevisionContext* pCtx,const ks_wstring& areaName)
{
	binary_wo::VarObj items = param.get("items");
	if (items.arrayLength() < 1)
		return E_INVALIDARG;
	int fieldsCnt = items.arrayLength();
	rg.Invalidate();
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);
 
	IDX iName = alg::STREF_INV_NAME;
	spBookOp->FindDefinedName(alg::STREF_NO_SHEET, areaName.c_str(), &iName);
	if(iName == alg::STREF_INV_NAME)
		return E_FAIL;
	TaskExecFormHelp::GetNameRg(spBookOp, iName, rg);
	if(!rg.IsValid())
		return E_FAIL;

	rg.SetColTo(rg.ColTo() + fieldsCnt);
	RANGE rgData = rg;

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(rg.SheetFrom(), &spSheet);
	HRESULT hr = S_OK;
	for (int i = 0; i < fieldsCnt; i++)
	{
		IDX idx = items.item_int32(i);
		rg.SetColFromTo(idx);
		if (rg.RowFrom() <= spSheet->GetRight())
		{
			hr = InsertRangeEx(rg, etShiftToRight, pCtx);
			if (FAILED(hr))
				break;
		}
	}

	if(SUCCEEDED(hr))
		SetNameRg(spBookOp, iName, rgData);
	else
		rg.Invalidate();
	return hr;
}

PCWSTR TaskExecFormAddCol::GetTag()
{
	return __X("form.addCol");
}
// ================== TaskExecFormAddCol ==================

// ================== TaskExecFormAddSheet ==================
TaskExecFormAddSheet::TaskExecFormAddSheet(KEtWorkbook* wwb)
	: TaskExecFormBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecFormAddSheet::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	CALL_TIME_STAT("TaskExecFormAddSheet");
	util::IgnoreHistoryGuard ignoreHistoryGuard(pCtx);
	binary_wo::VarObj param = cmd->cast().get("param");
	PCWSTR strShtName = nullptr;
	if (param.has("name"))
	{
		strShtName = param.field_str("name");
		if (!alg::IsValidSheetName((strShtName)))
		{
			WO_LOG_X(pCtx->getLogger(), WO_LOG_WARN, "[FORM] invalid sheet name");
			strShtName = et_sWoFormSheetName;
		}
	}
	else
	{
		strShtName = et_sWoFormSheetName;
	}

	ks_stdptr<etoldapi::Worksheets> spSheets;
	m_wwb->GetCoreWorkbook()->get_Worksheets(&spSheets);
	if (!spSheets)
		return E_FAIL;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);

	ks_wstring strAreaName = getAreaName(param);
	IDX iName = alg::STREF_INV_NAME;
	spBookOp->FindDefinedName(alg::STREF_NO_SHEET, strAreaName.c_str(), &iName);
	if(iName == alg::STREF_INV_NAME)
	{
		HRESULT hr = spBookOp->DefineName(alg::STREF_NO_SHEET, strAreaName.c_str(), DefNameAttr_Normal, &iName);
		if(iName == alg::STREF_INV_NAME)
			return E_FAIL;
	}
	else
	{
		RANGE nameRg(m_wwb->GetBMP());
		TaskExecFormHelp::GetNameRg(spBookOp, iName, nameRg);
		if (nameRg.IsValid())
		{
			ks_stdptr<IKCoreObject> spObsoleteSheetObj;
			spSheets->get_Item(KComVariant(nameRg.SheetFrom() + 1), &spObsoleteSheetObj);
			ks_stdptr<etoldapi::_Worksheet> spObsoleteSheet = spObsoleteSheetObj;
			if (spObsoleteSheet)
			{
				ks_wstring wsObsoleteSheetName(et_sWoObsoleteFormSheetName);
				QString dateTime = QDateTime::currentDateTime().toStringEx("yyyyMMddhhmmss");
				wsObsoleteSheetName.AppendFormat(__X("%s"), krt::utf16(dateTime));
				ks_bstr bsObsoleteSheetName(wsObsoleteSheetName.c_str());
				for (int count = 1; S_FALSE == spObsoleteSheet->IsValidSheetName(bsObsoleteSheetName); count++)
				{
					ks_wstring strName(wsObsoleteSheetName);
					strName.AppendFormat(__X("(%d)"), count);
					bsObsoleteSheetName.assign(strName.c_str());
				}
				HRESULT hr = spObsoleteSheet->put_Name(bsObsoleteSheetName);
				if (FAILED(hr))
				{
					WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] sheet rename failed");
					return E_INVALIDARG;
				}
			}
		}
	}

	KComVariant vBefore;
	KComVariant vAfter;
	IDX iSheet = getLastSheetIdx() + 1;
	vAfter = iSheet;
	KComVariant vCount;
	KComVariant vType;

	ks_stdptr<IKCoreObject> spObj;
	HRESULT hr = spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid);
	if (FAILED(hr))
		return hr;

	ks_stdptr<etoldapi::_Worksheet> spSheet = spObj;
	if (spSheet == NULL)
		return E_FAIL;

	ks_bstr bstrName(strShtName);
	for (int count = 1; S_FALSE == spSheet->IsValidSheetName(bstrName); count++)
	{
		ks_wstring strName(strShtName);
		strName.AppendFormat(__X("(%d)"), count);
		bstrName.assign(strName.c_str());
	}
	hr = spSheet->put_Name(bstrName);
	if (FAILED(hr))
	{
		WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "[FORM] invalid sheet name");
		return E_INVALIDARG;
	}

	RANGE rg(pBook->GetBMP());
	rg.SetSheetFromTo(iSheet);
	SetNameRg(spBookOp, iName, rg);
	return hr;
}

PCWSTR TaskExecFormAddSheet::GetTag()
{
	return __X("form.addSheet");
}
// ================== TaskExecFormAddSheet ==================


TaskExecFormAddName::TaskExecFormAddName(KEtWorkbook* wwb): TaskExecFormBase(wwb)
{

}

HRESULT TaskExecFormAddName::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	CALL_TIME_STAT("TaskExecFormAddName");
	util::IgnoreHistoryGuard ignoreHistoryGuard(pCtx);
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetIdx = GetSheetIdx(param);
	if (INVALIDIDX == sheetIdx)
		return E_FAIL;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);

	ks_wstring strAreaName = getAreaName(param);
	IDX iName = alg::STREF_INV_NAME;
	spBookOp->FindDefinedName(alg::STREF_NO_SHEET, strAreaName.c_str(), &iName);
	if(iName == alg::STREF_INV_NAME)
	{
        HRESULT hr = spBookOp->DefineName(alg::STREF_NO_SHEET, strAreaName.c_str(), DefNameAttr_Normal, &iName);
        if (iName == alg::STREF_INV_NAME)
            return E_FAIL;
    }

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);

	RANGE rg(pBook->GetBMP());
	rg.SetSheetFromTo(sheetIdx);
	rg.SetRowFromTo(0, spSheet->GetBottom());
	rg.SetColFromTo(0, spSheet->GetRight());
	SetNameRg(spBookOp, iName, rg);

	return S_OK;
}

PCWSTR TaskExecFormAddName::GetTag()
{
	return __X("form.addName");
}



// ================== TaskExecFormEditRow ==================
TaskExecFormEditRow::TaskExecFormEditRow(KEtWorkbook* wwb)
	: TaskExecFormBase(wwb)
{}

HRESULT TaskExecFormEditRow::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	CALL_TIME_STAT("TaskExecFormEditRow");
	util::IgnoreHistoryGuard ignoreHistoryGuard(ctx);
	const binary_wo::VarObj param = cmd->cast().get("param");
	binary_wo::VarObj objVec = param.get("items");

	if(objVec.arrayLength() < 1)
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_ERROR, "[FORM] items empty");
		return E_INVALIDARG;
	}

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	RANGE rg(pBook->GetBMP());
	HRESULT hr = TaskExecFormHelp::GetFormRg(pBook, ctx, rg,getAreaName(param));
	if (FAILED(hr))
		return hr;

	int row = getIntS(param, "row", -1);
	if (row >= 0)
	{
		rg.SetRowFromTo(rg.RowFrom() + row);
	}
	else if(!FindModifyRg(param.field_str("key"), param.field_int32("fieldFind"), rg))
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_WARN, "[FORM] key not find");
		return E_FAIL;
	}

	int offset = getIntS(param, "offset", 0);
	if(offset < 0 || offset >= rg.Width())
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_WARN, "[FORM] offset invalid");
		return E_INVALIDARG;
	}

	bool bItemsWithType = isItemsWithType(param);

	COL colFrom = rg.ColFrom() + offset;
	RANGE xfRg(rg);

	SetBreak(true); 
	app_helper::KBatchUpdateCal batchUpdate(m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator()); // 不重复做计算扩散
	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = ctx, wmh.objParam = objVec;
	wmh.setHasExtra();
	hr = handleItems(wmh, rg, colFrom, std::vector<uint32>(), bItemsWithType, ctx);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

PCWSTR TaskExecFormEditRow::GetTag()
{
	return __X("form.editRow");
}
// ================== TaskExecFormEditRow ==================

// ================== TaskExecFormDeleteRow ==================
TaskExecFormDeleteRow::TaskExecFormDeleteRow(KEtWorkbook* wwb)
	: TaskExecFormBase(wwb)
{}

HRESULT TaskExecFormDeleteRow::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	CALL_TIME_STAT("TaskExecFormDeleteRow");
	util::IgnoreHistoryGuard ignoreHistoryGuard(ctx);
	const binary_wo::VarObj param = cmd->cast().get("param");
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	RANGE rg(pBook->GetBMP());
	HRESULT hr = TaskExecFormHelp::GetFormRg(pBook, ctx, rg,getAreaName(param));
	if (FAILED(hr))
		return hr;

	int row = getIntS(param, "row", -1);
	if (row >= 0)
	{
		rg.SetRowFromTo(row);
	}
	else if(!FindModifyRg(param.field_str("key"), param.field_int32("fieldFind"), rg))
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_WARN, "key not find");
		return E_FAIL;
	}

	ks_stdptr<Range> range = m_wwb->CreateRangeObj(rg);
	ks_stdptr<IRangeInfo> hostInfo = range;
	ks_stdptr<IBookOp> op;
	range_helper::ranges rgs = rgs.create_instance();
	hostInfo->GetIRanges(&rgs);
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&op);
	
	hr = op->QueryRangeOperation(NULL, rgs, roc_Remove, dirTop);
	if (FAILED(hr))
		return hr;

	SetBreak(true); 
	KComVariant var;
	var.AssignDouble(etShiftUp);
	return range->Delete(var);
}

PCWSTR TaskExecFormDeleteRow::GetTag()
{
	return __X("form.deleteRow");
}
// ================== TaskExecFormDeleteRow ==================

TaskExecFormDeleteRows::TaskExecFormDeleteRows(KEtWorkbook* wwb)
	: TaskExecFormBase(wwb)
{}

HRESULT TaskExecFormDeleteRows::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	CALL_TIME_STAT("TaskExecFormDeleteRows");
	util::IgnoreHistoryGuard ignoreHistoryGuard(ctx);
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	RANGE rg(pBook->GetBMP());
	const VarObj param = cmd->cast().get("param");
	HRESULT hr = TaskExecFormHelp::GetFormRg(pBook, ctx, rg,getAreaName(param));
	if (FAILED(hr))
		return hr;

	int rowForm = getIntS(param, "rowFrom", -1);
	int rowTo = getIntS(param, "rowTo", -1);

	rg.SetRowFromTo(rowForm,rowTo);

	ks_stdptr<Range> range = m_wwb->CreateRangeObj(rg);
	ks_stdptr<IRangeInfo> hostInfo = range;
	ks_stdptr<IBookOp> op;
	range_helper::ranges rgs = rgs.create_instance();
	hostInfo->GetIRanges(&rgs);
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&op);
	
	hr = op->QueryRangeOperation(NULL, rgs, roc_Remove, dirTop);
	if (FAILED(hr))
		return hr;

	SetBreak(true); 
	KComVariant var;
	var.AssignDouble(etShiftUp);
	return range->Delete(var);
}

PCWSTR TaskExecFormDeleteRows::GetTag()
{
	return __X("form.deleteRows");
}
// ================== TaskExecFormDeleteRows ==================


//---------------------------------------------------------------------------
TaskExecFileSetAutoFilter::TaskExecFileSetAutoFilter(KEtWorkbook* wwb)
	:TaskExecFormBase(wwb)
{
}

HRESULT TaskExecFileSetAutoFilter::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	util::IgnoreHistoryGuard ignoreHistoryGuard(ctx);
	binary_wo::VarObj param = cmd->cast().get("param");
	bool isOn = param.field_bool("turnOn");

	IWoFilterContext *pFilterCtx = ctx->getFilterContext();
	PCWSTR filterID = pFilterCtx->GetID();

	//获取表单sheet idx
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	RANGE rg(pBook->GetBMP());
	HRESULT hr = TaskExecFormHelp::GetFormRg(pBook, ctx, rg,getAreaName(param));
	if (FAILED(hr)) return hr;

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(rg.SheetFrom(), &spSheet);
	if (!spSheet) return E_FAIL;

	RECT rcData = {0};
	spSheet->CalcUsedScale(&rcData);

	RANGE rgData(spSheet->GetBMP());
	IDX iSheet = -1;
	spSheet->GetIndex(&iSheet);
	rgData.SetSheetFromTo(iSheet);
	rgData.SetRowFromTo(rcData.top, rcData.bottom);
	rgData.SetColFromTo(rcData.left, rcData.right);
	if (!rgData.IsValid()) return E_FAIL;

	addFilterExtraField(ctx, param);

	ks_stdptr<etoldapi::Range> host = m_wwb->CreateRangeObj(rgData);
	IKAutoFilter* pAutoFilter = AutoFilterHelper::GetUserFilter(m_wwb->GetCoreWorkbook(), filterID, rgData);
	if ((pAutoFilter != NULL) == isOn)
		return S_OK; // isOn状态和现在一样不用处理

	KComVariant varEmpty;
	return host->AutoFilter(filterID,  varEmpty, varEmpty, etAnd,
		varEmpty, varEmpty, &varEmpty, NULL);
}

PCWSTR TaskExecFileSetAutoFilter::GetTag()
{
	return __X("file.setAutoFilter");
}

// ================== TaskExecFormSort ==================
TaskExecFormSort::TaskExecFormSort(KEtWorkbook* wwb)
	: TaskExecFormBase(wwb)
{}

HRESULT TaskExecFormSort::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	CALL_TIME_STAT("TaskExecFormSort");	
	util::IgnoreHistoryGuard ignoreHistoryGuard(ctx);
	binary_wo::VarObj param = cmd->cast().get("param");
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	RANGE rgSort(pBook->GetBMP());
	HRESULT hr = TaskExecFormHelp::GetFormRg(pBook, ctx, rgSort,getAreaName(param));
	if (FAILED(hr))
		return hr;
	ks_stdptr<etoldapi::Range> ptrRgSort = m_wwb->CreateRangeObj(rgSort);

	ks_stdptr<_Worksheet> ptrWorksheet;
	ptrRgSort->get_Worksheet(&ptrWorksheet);
	ks_stdptr<etoldapi::Sort> ptrSort;
	ptrWorksheet->get_Sort(&ptrSort);
	ks_stdptr<SortFields> ptrSortFields;
	VS(ptrSort->get_SortFields(&ptrSortFields));
	ptrSortFields->Clear();

	//添加排序关键字
	binary_wo::VarObj sortFieldList = param.get("keyFields");
	int iFieldListSize = sortFieldList.arrayLength();
	for (int i = 0; i < iFieldListSize; i++)
	{
		int iField = sortFieldList.item_int32(i);
		RANGE cellKey = rgSort;
		cellKey.SetColFromTo(rgSort.ColFrom() + iField);
		cellKey.SetRowFrom(rgSort.RowFrom() + (rgSort.Height() > 1 ? 1 : 0));
		ks_stdptr<etoldapi::Range> ptrCellKey = m_wwb->CreateRangeObj(cellKey);

		KComVariant sortOn;
		KComVariant customOrder;
		KComVariant dataOption;
		KComVariant order(etAscending);
		ptrSortFields->Add(ptrCellKey, sortOn, order, customOrder, dataOption, NULL);
	}

	ptrSort->put_Header(etYes);
	ptrSort->put_MatchCase(VARIANT_FALSE);
	ptrSort->put_SortMethod(etPinYin);
	ptrSort->put_Orientation(etSortColumns);
	ptrSort->SetRange(ptrRgSort);
	return ptrSort->Apply();
}

PCWSTR TaskExecFormSort::GetTag()
{
	return __X("form.sort");
}
// ================== TaskExecFormSort ==================


//---------------------------------------------------------------------------
TaskExecFormSetColumnWidth::TaskExecFormSetColumnWidth(KEtWorkbook* wwb)
	:TaskExecFormBase(wwb)
{
}

HRESULT TaskExecFormSetColumnWidth::operator()(KwCommand* cmd, KEtRevisionContext*ctx)
{
	util::IgnoreHistoryGuard ignoreHistoryGuard(ctx);
	VarObj param = cmd->cast().get("param");
	VarObj bWidth = param.get("value");
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	RANGE rg(pBook->GetBMP());
	TaskExecFormHelp::GetFormRg(pBook, ctx, rg,getAreaName(param));
	rg.SetColFromTo(param.field_int32("col"));
	rg.SetRowFromTo(0,MAX_ROWS_COUNT_XTND -1);
	if (!rg.IsValid() || !bWidth)
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_ERROR, "param is wrong");
		return E_FAIL;
	}
	
	double width = 0.0;

	// 传入单位是缇，内核api单位是字符数，需要进行转换
	width = bWidth.value_double();
	width = app_helper::GetCharsWithColWidth(m_wwb->GetCoreWorkbook(), width);

	KComVariant var(width);
	ks_stdptr<etoldapi::Range> spRange = m_wwb->CreateRangeObj(rg);
	if (!spRange)
		return S_FALSE;

	return spRange->put_ColumnWidth(var);
}

PCWSTR TaskExecFormSetColumnWidth::GetTag()
{
	return __X("form.setColumnWidth");
}


} // wo
