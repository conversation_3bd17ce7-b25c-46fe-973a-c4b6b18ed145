﻿#ifndef __WEBET_DB_HTTP_TASK_CLASS_H__
#define __WEBET_DB_HTTP_TASK_CLASS_H__

#include "dbsheet/et_dbsheet_task_class.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "dbsheet/et_dbsheet_utils.h"

namespace wo 
{

class DbHttpTaskClassBase : public ETDbSheetTaskClassBase
{
public:
	using ETDbSheetTaskClassBase::ETDbSheetTaskClassBase;
	virtual HRESULT PreExecute(KwCommand*, KEtRevisionContext*) override;
	virtual HRESULT PostExecute(HRESULT, KwCommand*, KEtRevisionContext*) override;
protected:
    HRESULT SetSheetName(_Worksheet* pWorkSheet, PCWSTR name);
	BOOL IsAppSheetBook();
	HRESULT GetViewId(binary_wo::VarObj& obj, EtDbId& viewId);
	HRESULT GetAppSheetDBView(binary_wo::VarObj& obj, IDBSheetView** pDbSheetView);
protected:
	ks_wstring m_errMsg;
	std::unique_ptr<IDbErrInfoCollector> m_spErrInfoCollector;
};

class DbHttpCreateRecordsTaskBaseClass : public DbHttpTaskClassBase
{
public:
	using DbHttpTaskClassBase::DbHttpTaskClassBase;
protected:
	HRESULT createRecordsExec(KwCommand* cmd, KEtRevisionContext*, bool devSubmitFormRec);
};

class DbHttpCreateRecordsTaskClass : public DbHttpCreateRecordsTaskBaseClass
{
public:
	DbHttpCreateRecordsTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class DbHttpUpdateRecordsTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpUpdateRecordsTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

// 设置父子关系
class DbHttpSetParentRecordTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpSetParentRecordTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

// 启用父子关系
class DbHttpEnableParentRecordTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpEnableParentRecordTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

// 禁用父子关系
class DbHttpDisableParentRecordTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpDisableParentRecordTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class DbHttpDeleteRecordsTaskClass : public DbHttpTaskClassBase
{
enum DeleteMode
{
	IncludeMode,
	AllMode,
	Unknown
};
public:
	DbHttpDeleteRecordsTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
private:
	DeleteMode DecodeDeleteMode(WebStr modeStr);
	HRESULT DeleteIncludeRecords(binary_wo::VarObj param, KEtRevisionContext* ctx, UINT sheetStId, bool isBatch);
	HRESULT DeleteAllRecords(binary_wo::VarObj param, KEtRevisionContext* ctx, UINT sheetStId, bool isBatch);
};

class DbHttpCreateFieldsTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpCreateFieldsTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class DbHttpDeleteFieldsTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpDeleteFieldsTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class DbHttpUpdateFieldsTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpUpdateFieldsTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class DbHttpCreateViewTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpCreateViewTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class DbHttpUpdateViewTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpUpdateViewTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class DbHttpDeleteViewTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpDeleteViewTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};


class DbHttpAddViewSettingTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpAddViewSettingTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class DbHttpRemoveViewSettingTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpRemoveViewSettingTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};


class DbHttpCreateSheetTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpCreateSheetTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT InitDbSheet(etoldapi::_Worksheet *, bool, binary_wo::VarObj);
};

class DbHttpUpdateSheetTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpUpdateSheetTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class DbHttpDeleteSheetTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpDeleteSheetTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class DbHttpEditPermissonTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpEditPermissonTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT add(VarObj& param, KEtRevisionContext* ctx);
	HRESULT remove(WebStr permissionId);
	HRESULT modify(const VarObj& param);
};

class DbHttpSetQueryFieldsTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpSetQueryFieldsTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override 
	{ 
		return S_OK; //　内部限制
	} 
	PCWSTR GetTag() override;
};

// 开发者接口. 不对外暴露
class DbHttpDevSubmitFormRecordTaskClass : public DbHttpCreateRecordsTaskBaseClass
{
public:
	DbHttpDevSubmitFormRecordTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override { return S_OK; } // 开发者接口. 不进行权限检查
	PCWSTR GetTag() override;
};

// 开发者接口. 不对外暴露
class DbHttpDevCreateAppViewTaskClass : public DbHttpTaskClassBase
{
public:
	DbHttpDevCreateAppViewTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override { return S_OK; } // 开发者接口. 不进行权限检查
	PCWSTR GetTag() override;
};


class DbHttpFullUpdateSyncSheetBaseTaskClass : public DbHttpTaskClassBase
{
public:
	explicit DbHttpFullUpdateSyncSheetBaseTaskClass(KEtWorkbook*);
protected:
	HRESULT FullUpdateSyncSheet(KEtRevisionContext* pCtx, etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook, VarObj& param, ISerialAcceptor& acpt, PCWSTR filePath, bool isBatch = false);
	ks_wstring m_timeStatInfo;
};

// 单个表更新,支持分批更新，非传文件形式
class DbHttpSyncSheetTaskClass : public DbHttpTaskClassBase
{
public:
	explicit DbHttpSyncSheetTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

// 单个表更新
class DbHttpFullUpdateSyncSheetTaskClass : public DbHttpFullUpdateSyncSheetBaseTaskClass
{
public:
	explicit DbHttpFullUpdateSyncSheetTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

// 批量更新
class DbHttpBatchFullUpdateSyncSheetTaskClass : public DbHttpFullUpdateSyncSheetBaseTaskClass
{
public:
	explicit DbHttpBatchFullUpdateSyncSheetTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class FullUpdateMergeSyncDbSheetsAdapter;
class DbHttpFullUpdateSyncMergeSheetTaskClass : public DbHttpTaskClassBase
{
public:
	explicit DbHttpFullUpdateSyncMergeSheetTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	void fillHttpResponse(KEtRevisionContext*, const FullUpdateMergeSyncDbSheetsAdapter&);
};
class DbHttpSyncFromSqlTaskClass : public DbHttpTaskClassBase
{
public:
	explicit DbHttpSyncFromSqlTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class DbHttpSyncFormToDbTaskClass : public DbHttpTaskClassBase
{
public:
	explicit DbHttpSyncFormToDbTaskClass(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class DbHttpCreateDashboardTaskClass : public DbHttpTaskClassBase
{
public:
    DbHttpCreateDashboardTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
};

class DbHttpUpdateDashboardTaskClass : public DbHttpTaskClassBase
{
public:
    DbHttpUpdateDashboardTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
};

class DbHttpClearDashboardTaskClass : public DbHttpTaskClassBase
{
public:
    DbHttpClearDashboardTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
};

class DbHttpDeleteDashboardTaskClass : public DbHttpDeleteSheetTaskClass
{
public:
    DbHttpDeleteDashboardTaskClass(KEtWorkbook*);
    PCWSTR GetTag() override;
};

class DbHttpCreateWebExtensionTaskClass : public DbHttpTaskClassBase
{
public:
    DbHttpCreateWebExtensionTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
};

class DbHttpUpdateWebExtensionTaskClass : public DbHttpTaskClassBase
{
public:
    DbHttpUpdateWebExtensionTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
};

class DbHttpDeleteWebExtensionTaskClass : public DbHttpTaskClassBase
{
public:
    DbHttpDeleteWebExtensionTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
};

} // namespace wo

#endif // __WEBET_DB_HTTP_TASK_CLASS_H__
