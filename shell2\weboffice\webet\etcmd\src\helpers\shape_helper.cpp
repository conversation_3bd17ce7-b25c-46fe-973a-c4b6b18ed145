#include "etstdafx.h"
#include "opl/et_opl_helper.h"
#include <public_header/opl/mvc/chart/et_chart_layer.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include <public_header/opl/mvc/et_mvc_shape.h>
#include "shape_helper.h"
#include "etcore/little_alg.h"

namespace wo
{

/**
 *  判断两个区域之间有关系(互相包含、相交等)则返回true
 */
inline bool RgIntersectRc(const RANGE& rg, const RECT& rc)
{
    return Rect_Intersect(Range2Rect(rg), rc);
}

HRESULT GetRangeCoverShapes(ISheet *ptrSheet, IKDrawingCanvas *pCanvas, IKRanges *pRanges,
                                IKShapeRange **ppShapeRange, BOOL bInIntersect)
{
    // 找出随单元格的对象
    HRESULT hr = E_FAIL;
    ks_stdptr<IKShapeRange> ptrShapeRange;
    ks_stdptr<IKShapeContainer> ptrShapeContainer;
    VS(hr = pCanvas->CreateShapeRange(&ptrShapeRange));
    VS(hr = pCanvas->GetRootShape(&ptrShapeContainer));

    if (!ptrShapeRange || !ptrShapeContainer)
        return E_FAIL;

    UINT uRgs = 0;
    VS(pRanges->GetCount(&uRgs));
    if (0 == uRgs)
        return S_FALSE;

    std::vector<RANGE> vecRgs;
    vecRgs.reserve(uRgs);
    for (UINT i = 0; i < uRgs; ++i)
    {
        const RANGE *pRg = NULL;
        INT iBook = alg::STREF_INV_BOOK;
        pRanges->GetItem(i, &iBook, &pRg);
        vecRgs.push_back(*pRg);
    }
    if (uRgs > 1)
    {
        struct Cmp
        {
            bool operator()(const RANGE &lhs, const RANGE &rhs) const
            {
                if (lhs.ColFrom() < rhs.ColFrom())
                    return true;
                else if (rhs.ColFrom() < lhs.ColFrom())
                    return false;
                return lhs.RowFrom() < rhs.RowFrom();
            }
        } cmpObj;

        // 行优先
        std::sort(vecRgs.begin(), vecRgs.end(), cmpObj);
    }
    // ================
    //  遍历所有对象
    LONG lCount = 0;
    VS(ptrShapeContainer->GetShapeCount(&lCount));

    et_sdptr<ISheetEnum> spShtEnum;
    ptrSheet->CreateEnum(&spShtEnum);
    ks_stdptr<drawing::AbstractShape> spAbsShape;
    for (long s = 0; s < lCount; ++s)
    {
        spAbsShape.clear();
        VS(ptrShapeContainer->GetShapeByIndex(s, &spAbsShape));

        // 看看Shape是否随单元格
        ks_stdptr<IKClientAnchor> spClientAnchor;
        ks_stdptr<IETShapeAnchor> spEtAnchor;
        spAbsShape->GetClientAnchor(&spClientAnchor);
        spEtAnchor = spClientAnchor;
        if (!spEtAnchor)
            continue;

        OPLFLOATFLAG flag = spEtAnchor->GetFloatingFlag();
        RANGE lastRng(ptrSheet->GetBMP());
        ROW baseR = 0;
        COL baseC = 0;
        bool bFirst = true;
        bool bCoverLT = false;
        CELL baseCell = {0};
        for (auto itr = vecRgs.begin(); itr != vecRgs.end(); ++itr)
        {
            if (uRgs > 1)
            {
                if (!bFirst)
                {
                    if (itr->ColFrom() == lastRng.ColFrom())
                    {
                        baseR += lastRng.Height();
                    }
                    else
                    {
                        baseR = 0; //按行检索，在按列
                        baseC += lastRng.Width();
                    }
                }
                RECT shapeRect = { 0 };
                OplObjCoverFlags coverFlag = gopl_CheckObjCoverRange(
                    ptrSheet, spShtEnum, spAbsShape, spAbsShape->isVml(), spEtAnchor, flag, (*itr), &shapeRect);
                if (oploCoverLeftTop == coverFlag)
                {
                    if (!bCoverLT)
                    {
                        bCoverLT = true;
                    }
                }
                else if (bCoverLT && oploCoverRightBottom == coverFlag)
                {
                    ptrShapeRange->AddShape(spAbsShape);
                    break;
                }
                else if (oploCoverAll == coverFlag)
                {
                    ptrShapeRange->AddShape(spAbsShape);
                    break;
                }
                else if(bInIntersect && (coverFlag != oploCoverNone || RgIntersectRc(*itr, shapeRect)))
                {
                    ptrShapeRange->AddShape(spAbsShape);
                    break;
                }

                lastRng = (*itr);
                bFirst = false;
            }
            else
            {
                RECT shapeRect = { 0 };
                OplObjCoverFlags coverFlag 
                        = gopl_CheckObjCoverRange(ptrSheet, spShtEnum, spAbsShape, spAbsShape->isVml(),spEtAnchor, flag, (*itr), &shapeRect);
                if (coverFlag == oploCoverAll || (bInIntersect && (coverFlag != oploCoverNone || RgIntersectRc(*itr, shapeRect))))
                {
                    ptrShapeRange->AddShape(spAbsShape);
                    break;
                }
            }
        }
    }

	*ppShapeRange = ptrShapeRange;
	(*ppShapeRange)->AddRef();
    return S_OK;
}

} // namespace wo
