﻿#ifndef __WEBET_DB_FIELD_RECOGNIZE_HELPER_H__
#define __WEBET_DB_FIELD_RECOGNIZE_HELPER_H__

#include "wo/et_shared_str.h"
#include "etcore/et_core_dbsheet_enum.h"

namespace wo
{
	class DBFieldTypeRecHelper
	{
	public:
		DBFieldTypeRecHelper();
		DBFieldTypeRecHelper(IET_NumberFormatter*);
		void begin();
		HRESULT addToken(const_token_ptr, PCWSTR format, ET_DbSheet_FieldType&, bool isContact = false);
		void end();
		void GetTypeInfo(ET_DbSheet_FieldType&, PCWSTR& numFmt) const;
		void SetHasList(bool b) { m_hasList = b; }
		const GlobalSharedStringSet& GetSelectItem() const { return m_selectSet; }
		void CalcType(ET_DbSheet_FieldType type, PCWSTR numFmt) { calcType(type, numFmt, 0); }
	private:
		void addToSelectSet(PCWSTR, bool);
		void updateSelectOptionInfo(const_token_ptr, bool, ET_DbSheet_FieldType);
		HRESULT getTypeFormatDouble(const_token_ptr, PCWSTR, ET_DbSheet_FieldType&, PCWSTR&, int&);
		void calcType(ET_DbSheet_FieldType, PCWSTR, int);
		ET_DbSheet_FieldType getOptimalType() const;
		void calcNumFmt();

		ET_DbSheet_FieldType m_type = ET_DbSheet_FieldType_Invalid;
		GlobalSharedString m_numFmt;
		int m_prec = 0;
		bool m_bSelectOption = true;
		bool m_isRepeated = false;
		bool m_hasList = false;
		ks_stdptr<IET_NumberFormatter> m_spNumberFormatter;
		GlobalSharedStringSet m_selectSet;
	};
} // namespace wo

#endif
