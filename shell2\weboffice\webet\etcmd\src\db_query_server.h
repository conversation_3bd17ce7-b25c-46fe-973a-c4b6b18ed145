#ifndef __WEBET_DB_QUERY_SERVER_H__
#define __WEBET_DB_QUERY_SERVER_H__
#include "appcore/et_appcore_dbsheet.h"

namespace wo
{

class DbUserInfoQuery
{
public:
    DbUserInfoQuery(IDbUsersManager* spUserMgr, const QString &userID)
    {
        m_spUsersMgr = spUserMgr;
        m_userId = userID;
    }
    void QueryUserInfo();
    void QueryUserCompanyInfo(const std::vector<ks_wstring>& userIds);
    void QueryUserCompanyInfo();
private:
    void SendUserIds(int count);
private:
    ks_stdptr<IDbUsersManager> m_spUsersMgr;
    QString m_userId;
};

} // namespace wo

#endif //__WEBET_DB_QUERY_SERVER_H__