﻿#ifndef __WEBET_DATABASE_FIELD_INITIALISER_H__
#define __WEBET_DATABASE_FIELD_INITIALISER_H__

#include "database_def.h"

namespace wo
{
namespace Database
{

class FieldFormulaInitialiser : public IFieldInitialiser
{
public:
    FieldFormulaInitialiser(PCWSTR);
    virtual ~FieldFormulaInitialiser() {}
    virtual HRESULT Initialise(FieldContext *, const RANGE &) override;
    virtual HRESULT CheckPermission(FieldContext *, const RANGE &) override;
protected:
    FieldFormulaInitialiser();

    HRESULT SetFormula(FieldContext *, const RANGE &, PCWSTR);
private:
    ks_wstring m_formula;
};

} // Database
} // wo

#endif // __WEBET_DATABASE_FIELD_INITIALISER_H__