﻿#include "etstdafx.h"
#include "etcore/et_core_basic.h"
#include "binvariant/binwriter.h"
#include "exec_detail.h"

#include "block_point.h"
#include "Coding/core_bundle/framework/krt/krtstring.h"
#include "kern/errno.h"
#include "kfc/tools/rrid_wrap.h"
#include "etcore/little_alg.h"
#include "workbook.h"
#include "kfc/tools/smart_wrap.h"
#include "webetlink.h"
#include "webbase/binvariant/binreader.h"
#include <public_header/revision/src/kwrevisionctrl.h>
#include <public_header/revision/src/kwrepertoryproxy.h>
#include "et_task_executor.h"
#include "et_revision_context_impl.h"
#include "op_transform/et_op_tf.h"
#include "wo/bw_helper.h"
#include "wo/sa_helper.h"
#include "wo/core_stake.h"
#include "webbase/serialize_impl.h"
#include "webbase/memo_stat_common.h"
#include "revision_ext.h"
#include "workbooks.h"
#include <public_header/revision/src/kwcommand.h>
#include "wo/workbook_obj.h"
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include <public_header/etcore/mvc/et_workbook_layer.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include <public_header/opl/mvc/et_comment_individual_shape.h>
#include "malloc.h"
#include "et_query_executor.h"
#include <public_header/webcommon/src/wodocmemostat.h>
#include <public_header/webcommon/src/woexportimage.h>
#include <public_header/webcommon/src/woexportpdf.h>
#include <public_header/webcommon/src/woexportutils.h>
#include "condition_format_helper.h"
#include "condition_format_wrapper.h"
#include "pagesetup_helper.h"
#include "webbase/context_init_helper.h"
#include <public_header/drawing/wo/serialimage.h>
#include "drawing/hostshape/olecontrol_i.h"
#include "timeutil.h"
#include "et_query_class.h"
#include <public_header/webcommon/src/security_doc.h>
#include "et_binvar_spec.h"
#include "kfc/service/rts/alg_rts.h"
#include "kso/io/clipboard/ksoclipboard.h"
#include "supbooks_helper.h"
#include "wo/wo_msgType_helper.h"
#include "webbase/user_conn_end_helper.h"
#include "helpers/formula_idx_generator.h"
#include "helpers/shape_helper.h"
#include "webbase/logger.h" 
#include "helpers/subscription_helper.h"
#include "et_hard_define_strings.h"
#include <public_header/drawing/api/dghost_i.h>
#include <public_header/chart/src/model/kctchart.h>
#include <public_header/chart/src/mvc/kctchartlayer.h>
#include <public_header/chart/src/mvc/kctshapetree.h>
#include <public_header/chart/src/model/kctshape.h>
#include <public_header/opl/mvc/chart/et_chart_layer.h>
#include <public_header/opl/mvc/cellimage/et_cellimage_shape_tree.h>
#include "svg/woexportsvg.h"
#include "et_xva_cmd.h"
#include "pivot_core/pivot_core_x.h"
#include <public_header/drawing/model/extdata.h>
#include "autoupdateutil/mergefile_util.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "util.h"
#include "dbsheet/db2et_exporter.h"
#include "book_format_converter/et2ksheet_converter.h"
#include "book_format_converter/db2ksheet_converter.h"
#include "db_query_server.h"
#include "etcmd/src/censor/exportcensordata.h"
#include "helpers/attachment_helper.h"
#include "dbsheet/db_export_helper.h"
#include "kwebslice.h"
#include "dbsheet/et2db_exporter.h"
#include "helpers/picture_upload_helper.h"
#include "helpers/protection_helper.h"
#include "utils/attachment_utils.h"
#include "kso/kso_weboffice.h"
#include "helpers/inquirer_helper.h"
#include "etcore/wo_et_cell_collector.h"
#include "appcore/et_appcore_webhook.h"
#include <public_header/weblog/weblog.h>
#include "wo_medium.h"
#include "appcore/et_appcore_sparkline_itf.h"
#include "dbsheet/et_dbsheet_block_init_helper.h"

#include "thumbnail/woexportthumbnail.h"
#include "share_link_content_visibility_checker.h"
#include "exp_autofilter_list_helper.h"
#include "webhook/tracker_scope.h"
#include "collect.h"
#include "database/database_utils.h"
#include "split_book_opt.h"
#include "helpers/app/export_airapp_helper.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "utils/qrlabel_utils.h"
#include "helpers/slim_helper.h"
#include "applogic/et_applogic_doc_slim.h"
#include "helpers/copy_link_helper.h"
#include "helpers/varobject_helper.h"
#include "helpers/custom_storage_helper.h"
#include <kso/framework/docslim_helper.h>
#include "etcore/et_core_event_tracking.h"
#include "wo/wo_coremetrics_i.h"
#include "appcore/et_appcore_shared_link_sheet.h"
#include "webhook/webhook_enum.h"
#include "db/db_basic_itf.h"

extern Callback* gs_callback;
extern std::unique_ptr<wo::secdoc::SecurityDoc> gs_security_doc;
extern CoopCmd g_WebEtCoopCmd;
extern WebProcType gs_procType;
extern std::atomic<uint> gs_signalSize;
extern QDate g_lastDateForMonitorDateChange;

#define E_UNDOREDO_ERROR  500

namespace wo
{
using kfc::tools::vtoa;
using namespace binary_wo;
using KBinWriterSizeU32Mc = KBinSizeU32Mc<binary_wo::BinWriter>;

class DbLineHistoriesScope
{
public:
	explicit DbLineHistoriesScope(IBook *pBook, KEtWorkbook *pWoWb)
	: m_pWoWb(pWoWb)
	{
		pBook->GetExtDataItem(edBookDbLineHistories, (IUnknown**)&m_spLineHistories);
		if (m_spLineHistories)
		{
			m_begin = std::chrono::steady_clock::now();
			m_spLineHistories->Begin();
		}
	}
	~DbLineHistoriesScope()
	{
		if (m_spLineHistories)
		{
			BinWriter writer;
			KSerialWrapBinWriter acpt(writer, nullptr);
			m_spLineHistories->End(&acpt);
			BinWriter::StreamHolder stream = writer.buildStream();
			WebSlice slice = {stream.get(), writer.writeLength()};
			if (gs_callback->commitDbCohistories)
				gs_callback->commitDbCohistories(&slice);
			
			unsigned int ms = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
			m_pWoWb->coreMetric().setU32Metrics(KU32MetricItem::kLineHistoriesTime, ms);
		}
	}
private:
	ks_stdptr<IDbLineHistories> m_spLineHistories;
	KEtWorkbook * m_pWoWb;
	std::chrono::steady_clock::time_point m_begin;
};

bool isCommentHidden(AbstractModel* pAbsModel)
{
	EtCommentIndividualShape* pShape = static_cast<EtCommentIndividualShape*>(pAbsModel);
	CELL bindCell{};
	if (FAILED(pShape->GetBindCell(&bindCell)))
		return true;
	return pShape->IsCellHidden(bindCell);
}
constexpr unsigned int delta = 1024 * 1024;

void SyncSignalSize()
{
	if (gs_procType == WebProcTypeChild)
	{
		binary_wo::BinWriter bw;
		bw.addStringField(__X("getSignalSize"), "operate");

		binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
		WebSlice input = {shbt.get(), bw.writeLength()};
		WebSlice output = {nullptr, 0};
		gs_callback->onSyncParentProc(&input, &output);

		binary_wo::BinReader br(output.data, output.size);
		binary_wo::VarObjRoot root = br.buildRoot();
		binary_wo::VarObj obj = root.cast();
		if (obj.has("signalSize"))
		{
			uint size = obj.field_uint32("signalSize");
			if (size > gs_signalSize)
				gs_signalSize = size;
		}
		gs_callback->onFreeWebSlice(&output);
	}
}

void UpdateSignalSize(unsigned int signalSize)
{
	if (gs_procType == WebProcTypeChild)
	{
		if (signalSize <= gs_signalSize)
			return;
		binary_wo::BinWriter bw;
		bw.addStringField(__X("setSignalSize"), "operate");
		bw.addUint32Field(signalSize, "size");

		binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
		WebSlice input = {shbt.get(), bw.writeLength()};
		WebSlice output = {nullptr, 0};
		gs_callback->onSyncParentProc(&input, &output);
		gs_callback->onFreeWebSlice(&output);
		return;
	}
	if (signalSize > gs_signalSize)
		gs_signalSize = signalSize;
}

void ResetSlowCallCM(std::unique_ptr<wo::util::SlowCallTimeStat> & spState, 
		wo::KEtWorkbook * pWoWb,
		const char * tag,
		KU32MetricItem item) 
{
	spState.reset(new wo::util::SlowCallTimeStat(
		tag, util::kDefSlowCallThreshold, [pWoWb, item](unsigned int val) {
		pWoWb->coreMetric().addU32Metrics(item, val);
	}));
}


#define METRICS_TIME_BINSIZE(tag, wb, bw, timeItem, sizeItem) \
	util::SlowCallTimeMetrics slowCallMc(tag, wb, timeItem); \
	KBinWriterSizeU32Mc binSizeMc(&wb->coreMetric(), &bw, sizeItem);

#define METRICS_TIME_BINSIZE_RESTART(tag, wb, timeItem, sizeItem) \
	slowCallMc.restart(tag, wb, timeItem); \
	binSizeMc.restart(sizeItem);

class UserSyncScope
{
	IKETUserConn *m_pUserConn;

	int &getLevel()
	{
		static int level = 0;
		return level;
	}
	int m_baseVer;
	int m_resetVer;
public:
	UserSyncScope(IKETUserConn *pUserConn)
		: m_pUserConn(pUserConn)
		, m_baseVer(0)
		, m_resetVer(0)
	{
		if (gs_procType == WebProcTypeChild)
		{
			if (getLevel() == 0)
			{
				wo::util::SlowCallTimeStat callTime("UserSyncScope", 100);
				binary_wo::BinWriter bw;
				bw.addStringField(__X("getUserConn"), "operate");
				bw.addStringField(m_pUserConn->connID(), "connID");
				bw.addInt32Field(m_pUserConn->getSyncVer(), "syncVer");
				bw.addInt32Field(m_pUserConn->getResetVer(), "resetVer");

				binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
				WebSlice input = {shbt.get(), bw.writeLength()};
				WebSlice output = {nullptr, 0};
				{
					wo::util::SlowCallTimeStat callTime("UserSyncScope_syncParent", 100);
					gs_callback->onSyncParentProc(&input, &output);
				}
				binary_wo::BinReader br(output.data, output.size);
				m_pUserConn->deserialize(&br);
				m_baseVer = m_pUserConn->getSyncVer();
				m_resetVer = m_pUserConn->getResetVer();
				gs_callback->onFreeWebSlice(&output);
			}
			++getLevel();
		}
	}

	~UserSyncScope()
	{
		if (gs_procType == WebProcTypeChild)
		{
			--getLevel();
			ASSERT(getLevel() >= 0);
			if (getLevel() == 0)
			{
				wo::util::SlowCallTimeStat callTime("~UserSyncScope", 100);
				BinWriter bw;
				bw.addStringField(__X("setUserConn"), "operate");
				m_pUserConn->serialize(&bw, m_baseVer, m_resetVer);

				BinWriter::StreamHolder shbt = bw.buildStream();
				WebSlice input = {shbt.get(), bw.writeLength()};
				WebSlice output = {nullptr, 0};
				{
					wo::util::SlowCallTimeStat callTime("~UserSyncScope_syncParent", 100);
					gs_callback->onSyncParentProc(&input, &output);
				}
				gs_callback->onFreeWebSlice(&output);
			}
		}
	}
};

ExecDetail::ExecDetail(KEtWorkbooks* wbs, const char* connID, const char* userID, const char* sessionid, DetailExtArgs args, const WebSlice* slice, const WebSlice* serExtraParam, bool ignoreSeq)
	: m_workbook(wbs->GetWorkbook()), m_connID(connID), m_userID(userID)
	, m_slice(slice), m_wbs(wbs), m_curUserConn(NULL), m_seq(-1), m_signalSliceType(NULL)
	, m_bQueryOrCmd(false), m_bEverInit(false)
	, m_extArgs(args), m_bHttpCalling(false), m_bPrepareUser(false)
{
	UserConnEnd clientEnd = userConnEndUnknown;
	double clientCoopVersion = 1.0;
	if (slice && slice->size != 0 && slice->data != nullptr)
	{
		binary_wo::BinReader rd(slice->data, slice->size);
		m_rootObj = rd.buildRoot();
		m_root = m_rootObj.cast();
		binary_wo::VarObj seq = m_root.get_s("seq");
		if (!seq.empty() && !ignoreSeq)
			m_seq = seq.value_int32();
			
		binary_wo::VarObj extra = m_root.get_s("extra");

		binary_wo::VarObj varEnd = extra.get_s("end");
		if (!varEnd.empty())
			clientEnd = userConnEndFromStr(varEnd.value_str());
		binary_wo::VarObj varEndVer = extra.get_s("coop_ver");
		if (!varEndVer.empty())
			clientCoopVersion = varEndVer.value_double();
		if (m_root.has("httpCalling"))
			m_bHttpCalling = m_root.field_bool("httpCalling");
		
		binary_wo::VarObj varTraceId = m_root.get_s("traceId");
		if (!varTraceId.empty() && varTraceId.type() == binary_wo::typeString)
		{
			m_traceId = varTraceId.value_str();
			m_workbook->coreMetric().setTraceId(m_traceId);
		} 
		else
		{
			QString strUuid = QUuid::createUuid().toString();
			m_workbook->coreMetric().setTraceId(krt::utf16(strUuid));
		}
		m_workbook->coreMetric().setIsHttpCaling(m_bHttpCalling);
	}

	if (serExtraParam && serExtraParam->size != 0 && serExtraParam->data != nullptr)
	{
		binary_wo::BinReader binReader(serExtraParam->data, serExtraParam->size);
		m_serverExtraParamObj = binReader.buildRoot();
		m_serverExtraParam = m_serverExtraParamObj.cast();

		// Exec 执行时预分配的协作记录ID(目前用于单元格记录)
		if (m_serverExtraParam.has("revision"))
		{
			m_extArgs.commitVersion = m_serverExtraParam.field_int32("revision");
			int cmdCount = 0;
			// fimxe: 后面再考虑是否移除日志
			if (m_root.has("commands"))
				cmdCount = m_root.get_s("commands").arrayLength_s();

			WOLOG_INFO << "[cell_history] prealloc revision: " << m_extArgs.commitVersion << ", cmd count: " << cmdCount;
		}
	}

	ks_stdptr<_Application> ptrApp = m_wbs->GetCoreApp();	// 某些情况下没有当前文档
	IKUserConns* userConns = ptrApp->getUserConns();
	if (userConns && connID && m_workbook) {
		UserConnArgs connArgs(args.connFrom, args.connScene, args.connLife);
		userConns->setCurrentUserConn(m_connID, m_userID, clientEnd, clientCoopVersion, &connArgs);
		m_curUserConn = userConns->getCurrentUserConn();
		const auto *curUserId = m_curUserConn->userID();
		m_workbook->GetCoreWorkbook()->GetBook()->GetWoStake()->SetCurrentUserId(curUserId);
		if (m_curUserConn)
		{
			userConns->setCurrentUserConn(m_connID, m_userID, clientEnd, clientCoopVersion, &connArgs);
			m_curUserConn = userConns->getCurrentUserConn();
			const auto *curUserId = m_curUserConn->userID();
			m_workbook->GetCoreWorkbook()->GetBook()->GetWoStake()->SetCurrentUserId(curUserId);
			if (m_curUserConn)
			{
				if (sessionid)
					m_curUserConn->setSessionId(QString::fromUtf8(sessionid));

				m_curUserConn->setCorePermissionId(krt::utf16(QString::fromUtf8(m_extArgs.corePermissionID)));
				
				ks_castptr<IKETUserConn> pEtConn = m_curUserConn;
				if (m_bHttpCalling)
				{
					pEtConn->setHasCall(wo::UserConnFuncCall::kHttpCalling);
				}
				pEtConn->updateByUserConnArgs(connArgs);
			}

			if (m_root.has("visitingViews"))
			{
				IDBSheetCtx *pDbCtx = _appcore_GainDbSheetContext();
				ks_castptr<IKETUserConn> spEtUserConn = m_curUserConn;
				VarObj visitingViews = m_root.get_s("visitingViews");
				for (int i = 0, cnt = visitingViews.arrayLength_s(); i < cnt; i++)
				{
					VarObj item = visitingViews.at_s(i);
					UINT sheetId = item.field_uint32("sheetId");
					EtDbId viewId = INV_EtDbId;
					VS(pDbCtx->DecodeEtDbId(item.field_str("viewId"), &viewId));
					spEtUserConn->markVisitingView(sheetId, viewId);
				}
			}
		}
		else
		{
			userConns->setCurrentUserConn(nullptr);
			m_curUserConn = nullptr;
		}
	}
}

ExecDetail::~ExecDetail()
{
	if (IsUseSeq())
	{
		m_curUserConn->setCmdSeq(m_seq, m_spSignalSlice, m_signalSliceType);
	}
}

bool ExecDetail::IsUseSeq()
{
	return (m_curUserConn && m_seq > 0 && !m_bEverInit);
}

void ExecDetail::_SetInitRect(KEtRevisionContext& ctx, WebID activeSheet, binary_wo::VarObj& vExArgs) 
{
	CELL leftTop = {-1, -1};
	CELL frozenLeftTop = {-1, -1};
	binary_wo::VarObj vCellLeftTop = vExArgs.get_s("cellLeftTop");
	if (vCellLeftTop.type() == binary_wo::typeStruct)
	{
		leftTop.row = vCellLeftTop.field_int32("row");
		leftTop.col = vCellLeftTop.field_int32("col");
	}
	binary_wo::VarObj vfrozenCellLeftTop = vExArgs.get_s("frozenCellLeftTop");
	if (vfrozenCellLeftTop.type() == binary_wo::typeStruct)
	{
		frozenLeftTop.row = vfrozenCellLeftTop.field_int32("row");
		frozenLeftTop.col = vfrozenCellLeftTop.field_int32("col");
	}
	ctx.setInitRect(activeSheet, leftTop, frozenLeftTop);
}

void ExecDetail::_SetInitDefRect(KEtRevisionContext& ctx, WebID activeSheet, IKWorksheet *pWorksheet)
{
	if (activeSheet == INVALIDOBJID || pWorksheet == nullptr) return;

	CELL leftTop = {0, 0};
	CELL frozenLeftTop = {-1, -1};

	IKWorksheetView *wsView = pWorksheet->GetActiveWorksheetView();
	if (wsView->GetFreeze()) {
		const SHEETWNDINFO* pswi = NULL;
		ISheetWndInfo *pSheetWndInfo = wsView->GetSheetWndInfo();
		pSheetWndInfo->GetSheetWndInfo(&pswi);
		if (0 != pswi->PaneInfo.PanePosition.left)
			frozenLeftTop.col = pswi->cellLeftTop.col + pswi->PaneInfo.PanePosition.left;
		else
		 	frozenLeftTop.col = leftTop.col;

		if (0 != pswi->PaneInfo.PanePosition.top)
			frozenLeftTop.row = pswi->cellLeftTop.row + pswi->PaneInfo.PanePosition.top;
		else
		 	frozenLeftTop.row = leftTop.row;
	}
	ctx.setInitRect(activeSheet, leftTop, frozenLeftTop);
}

WebInt ExecDetail::ExecInit()
{
	WebInt res = verifyAndPrepareUserConn();
	if (res < 0)
	{
		WOLOG_ERROR << "Query init prepare user failed";
		return res;
	}
	m_workbook->incCmdCount();

	return ExecInitImpl(true);
}

WebInt ExecDetail::ExecInitImpl(bool bCallByInit)
{
	if (m_bHttpCalling)
		return WO_OK;

	wo::util::SlowCallTimeStat callTime("ExecInitImpl", 100, [this, bCallByInit] (unsigned int ms) {
		this->m_workbook->coreMetric().setExecInitImplTime(ms, !bCallByInit);
	});
	
	m_bEverInit = true;
	IKETUserConn* pUserConn = GetCurUser();
	UserSyncScope userSyncScope(pUserConn);
	WOLOG_INFO << "[execInit:web connId: " << pUserConn->connID() << "]";
	if (!bCallByInit)
		m_workbook->incReInitCount();
	return ExecInitWeb();
}

WebInt ExecDetail::ExecInitWindows(bool bCallByInit)
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	binary_wo::BinWriter binWriter;
	bool bCompatible = bCallByInit;
	if (bCallByInit)
	{
		bCompatible = (m_workbook->serializeInitData(ctx.get(), &binWriter) == WO_OK);
	}
	
	if (!bCompatible)
	{
		binWriter.addInt32Field(E_FAIL, "error");
		PCWSTR reason = bCallByInit ? __X("init-verison") : __X("delta-version");
		binWriter.addStringField(reason, "reason");
		FillOut(binWriter, bCallByInit ? msgType_Init : msgType_Exec);
		return WO_OK;
	}

	if (HasHyperlinkWithRun())
	{
		binWriter.addInt32Field(E_FAIL, "error");
		binWriter.addStringField(__X("hyperlinkWithRun"), "reason");
		FillOut(binWriter, bCallByInit ? msgType_Init : msgType_Exec);
		return WO_OK;
	}

	if (HasUnsupportSheetType())
	{
		binWriter.addInt32Field(E_FAIL, "error");
		binWriter.addStringField(__X("hasUnsupportSheetType"), "reason");
		FillOut(binWriter, bCallByInit ? msgType_Init : msgType_Exec);
		return WO_OK;
	}

	
	 IBookStake *pBookStake = m_workbook->GetCoreWorkbook()->GetBook()->GetWoStake();
    if (pBookStake->HasImportrangeFuncs())
    {
		binWriter.addInt32Field(E_FAIL, "error");
		binWriter.addStringField(__X("importrange"), "reason");
		FillOut(binWriter, bCallByInit ? msgType_Init : msgType_Exec);
		return WO_OK;
    }

	//需要客户端忽略的参数可以放这里(支持和param同级的或者param的下一级)
	SerializeIngoreParam(binWriter);
	SerializeSupBooksInfo(binWriter, ctx.get());
	KEtRevisionContext& _ctx = *ctx.get();
	AddExtState(binWriter, _ctx);
	SerializeBookSetting(binWriter);

	SerializeConnectingObjects(binWriter, ctx.get());
	SerializeCustomLists(binWriter, ctx.get());

	KSerialWrapBinWriter acpt(binWriter, ctx.get());
	IBookStake* bks = m_workbook->GetCoreWorkbook()->GetBook()->GetWoStake();
	if (FAILED(bks->SerializeClientSyncCell(ctx.get(), &acpt)))
	{
		binWriter.addInt32Field(E_FAIL, "error");
		binWriter.addStringField(__X("mutable-cells-result"), "reason");
		FillOut(binWriter, msgType_Init);
		return WO_OK;
	}

	FillOut(binWriter, msgType_Init);
	return WO_OK;
}

bool ExecDetail::InitWebDbSheetView(KEtRevisionContext& ctx, binary_wo::VarObj& vExArgs, IKWorksheet* pWorksheet)
{
	ISheet* pSheet = pWorksheet->GetSheet();
	ks_stdptr<IDBSheetView> spView;
	if (pSheet->IsAppSheet())
	{
		ks_stdptr<IAppSheetData> spAppSheetData;
		pSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spAppSheetData);
		IAirApp* pApp = spAppSheetData->GetApp(0);
		if (pApp->GetCategory() == KSheet_AirAppCat_DbSheetView)
		{
			ks_stdptr<IAirApp_DBView> spAppDbView = pApp;
			spView = spAppDbView->GetDBSheetView();
		}
	}
	else
	{
		if (vExArgs.has("activeViewId"))
		{
			IDBSheetCtx *pDbCtx = _appcore_GainDbSheetContext();
			EtDbId viewId = INV_EtDbId;
			VS(pDbCtx->DecodeEtDbId(vExArgs.field_str("activeViewId"), &viewId));
			DBSheetCommonHelper commonHelper(m_workbook);
			commonHelper.GetDBSheetView(pSheet->GetStId(), viewId, &spView, nullptr);
		}
	}
	if (!spView)
		return false;

	ET_DBSheet_ViewType type = spView->GetType();
	EtDbId ltRecId, ltFldId, fltFldId;
	ltRecId = ltFldId = fltFldId = INV_EtDbId;
	switch (type)
	{
		case et_DBSheetView_Grid:
		case et_DBSheetView_Query:
		case et_DBSheetView_Gantt:
			ConvertDbGridVisibleBlocks(spView, vExArgs, ltRecId, ltFldId, fltFldId);
			break;
		default:
			return false;
	}

	DBSheetInitHelper helper(m_workbook);
	helper.setDbView(spView);
	ks_stdptr<IDBSheetView_Grid> spDbGridView = spView;
	int frozenCols = 0;
    spDbGridView->GetFrozenCols(&frozenCols);
	helper.initDbGridBlocks(ctx, ltRecId, ltFldId, fltFldId, frozenCols);
	helper.afterInitBlocks(ctx, false);
	return true;
}

void ExecDetail::ConvertDbGridVisibleBlocks(IDBSheetView* pView, binary_wo::VarObj& vExArgs, EtDbId& ltRecId, EtDbId& ltFldId, EtDbId& fltFldId)
{
	CELL leftTop = {-1, -1};
	CELL frozenLeftTop = {-1, -1};
	binary_wo::VarObj vCellLeftTop = vExArgs.get_s("cellLeftTop");
	if (vCellLeftTop.type() == binary_wo::typeStruct)
	{
		leftTop.row = vCellLeftTop.field_int32("row");
		leftTop.col = vCellLeftTop.field_int32("col");
		ltRecId = pView->GetVisibleRecords()->IdAt(leftTop.row);
		ltFldId = pView->GetVisibleFields()->IdAt(leftTop.col);
	}
	binary_wo::VarObj vfrozenCellLeftTop = vExArgs.get_s("frozenCellLeftTop");
	if (vfrozenCellLeftTop.type() == binary_wo::typeStruct)
	{
		frozenLeftTop.col = vfrozenCellLeftTop.field_int32("col");
		fltFldId = pView->GetVisibleFields()->IdAt(frozenLeftTop.col);
	}
}

ISheet* ExecDetail::GetSharedSheet(IKETUserConn * pUserConn)
{
    IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
    pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    if (!spSharedLinkMgr)
        return nullptr;

    PCWSTR shareId = spSharedLinkMgr->GetConnSharedLink(pUserConn);
    if (!shareId)
        return nullptr;

    ISharedLink* pSharedLink = spSharedLinkMgr->GetItem(shareId);
    if (!pSharedLink)
        return nullptr;

    switch (pSharedLink->Type())
    {
        case SharedLinkType_DbView:
            return static_cast<IDBSharedLinkView*>(pSharedLink)->GetView()->GetSheetOp()->GetRawSheet();
        case SharedLinkType_Sheet:
            return static_cast<ISharedLinkSheet*>(pSharedLink)->GetSheet();
        default:
            return nullptr;
    }
    return nullptr;
}

WebID ExecDetail::InitWebDefaultSheet(KEtRevisionContext & ctx, IKETUserConn * pUserConn)
{
	binary_wo::VarObj vExArgs = m_root.get_s("extra");
	WebID defaultSheet = INVALIDOBJID;
    ISheet* pSharedSheet = GetSharedSheet(pUserConn);
    if (pSharedSheet)
    {
        IDX sheetIdx = INVALIDIDX;
        VS(pSharedSheet->GetIndex(&sheetIdx));
        IWorksheetObj* pWorksheetObj = m_workbook->GetCoreWorkbook()->GetWoObject()->getSheetItem(sheetIdx);
        IKWorksheet* pWorksheet = pWorksheetObj->GetWorksheet();
        defaultSheet = pWorksheetObj->objId();
    	_SetInitDefRect(ctx, defaultSheet, pWorksheet);
    }
	else if (vExArgs.has("activeSheet"))
	{
		binary_wo::VarObj vActiveSheetName = vExArgs.get("activeSheet");
		PCWSTR activeSheetName = NULL;
		if(vActiveSheetName.type() == binary_wo::typeString)
			activeSheetName = vActiveSheetName.value_str();
		ks_stdptr<IKWorksheet> spWorksheet;
		defaultSheet = m_workbook->GetDefInitSheet(activeSheetName, &spWorksheet);
		if (activeSheetName)
		{
			bool bInitDbView = false;
			if (spWorksheet->GetSheet()->IsDbSheet() || spWorksheet->GetSheet()->IsAppSheet())
				bInitDbView = InitWebDbSheetView(ctx, vExArgs, spWorksheet);

			if (!bInitDbView)
				_SetInitRect(ctx, defaultSheet, vExArgs);
		}
	}
	else
	{
		ks_stdptr<IKWorksheet> spWorksheet;
		defaultSheet = m_workbook->GetFirstVisibleSheet(&spWorksheet);
		_SetInitDefRect(ctx, defaultSheet, spWorksheet);
	}

	if (defaultSheet == INVALIDOBJID)
		defaultSheet = m_workbook->GetDefInitSheet(nullptr);
	pUserConn->getBlockPointCache()->addCompleteInstallSheet(defaultSheet);
	return defaultSheet;
}

WebInt ExecDetail::ExecInitWeb()
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr.get();

	KEtWorkbook::InitCache* pCache = m_workbook->getInitCache();

    bool readOnlyFilter =  hasReadonlyFilter();
	if(readOnlyFilter)
	{
		ctx.getFilterContext()->SetIsFilterReadOnly(true);
	}
				
	BOOL isFilterShared = m_workbook->GetCoreWorkbook()->GetBook()->GetWoStake()->getSetting()->getIsFilterShared();
	bool isCanUseCache = !m_bQueryOrCmd && // 不是query或exec
		isFilterShared && // 筛选不是非共享的
		!readOnlyFilter && // 没有只读筛选
		!ctx.getProtectionCtx()->isBookHasHidden() && // 没有不可见
		!isSharedLink() && // 不是分享视图
		!m_bPrepareUser; // 没有准备权限
	IKETUserConn* pUserConn = GetCurUser();
	bool permissionChanged = pUserConn->getCorePermissionChanged();

	if (!m_bQueryOrCmd)	
	{
		WebID defaultSheet = InitWebDefaultSheet(ctx, pUserConn);
		if (isCanUseCache && pCache && (!pCache->hasSheet(defaultSheet) || pCache->getSignalSlice()->Get()->size < 1024*1024))
		{
			m_workbook->clearInitCache();
			pCache = NULL;
		}
	}
	else if(m_root.has("visibleBlocks"))
	{
		VarObj vVisibleBlocks = m_root.get_s("visibleBlocks");
		WebID objSheet = vVisibleBlocks.field_web_id("objSheet");
		pUserConn->getBlockPointCache()->addCompleteInstallSheet(objSheet);
		ctx.setInitBlock(objSheet, vVisibleBlocks.get("blocks"));
	} else if (m_root.has("dbVisibleBlocks")) { // db visible blocks
		DBSheetInitHelper helper(m_workbook);
		helper.setDbInitBlocks(ctx, m_root.get("dbVisibleBlocks"));
	}
	
	if (pCache && isCanUseCache)
	{
		KwVersionManager* mgr = m_workbook->getVersionMgr();
		IKUserConn* userConn = ctx.getUser();
		if (userConn)
		{
			userConn->reset();
			userConn->setTaskIndex(mgr->getCurTaskVersionID());
			userConn->setDataIndex(mgr->getCurDataVersionID());
			userConn->setInitIndex(
				mgr->getCurDataVersionID(), mgr->getCurTaskVersionID());

			pCache->resetConnByCache(pUserConn);
		}
		WOLOG_INFO << "Init by Cache";
		FillOut(pCache->getSignalSlice(), msgType_Init);
		return WO_OK;
	}

	if (isCanUseCache)
		pCache = m_workbook->setupInitCache();

	binary_wo::BinWriter binWriter;
	if (isSharedLinkNotExist())
	{
		binWriter.addBoolField(true, "sharedLinkNotExist");
		FillOut(binWriter, msgType_Init);
		return WO_OK;
	}
	FillCallbackIds(binWriter);
	binWriter.addStringField(ctx.getUser()->userID(), "userid");

	m_workbook->serializeInitData(&ctx, &binWriter);

	//等后续重新初始化支持undo和redo之后再放开, 目前db,as权限变更后先禁用初始化后undo
	if (permissionChanged)
	{
		pUserConn->setCorePermissionChanged(false);
	}
	else
	{
		m_workbook->serializeCanUndoTransBeforeInit(&ctx, &binWriter);
		m_workbook->serializeCanRedoBeforeInit(ctx.getUser(), &binWriter);
	}
	

	CustomWebAcptHelp acptHlp(m_workbook, &binWriter, &ctx);
	acptHlp.begin();
	BP_Init(ctx, binWriter, acptHlp.getAcpt());
	AddExtState(binWriter, ctx);
	SerializeFmla(ctx, acptHlp.getAcpt());
	m_workbook->serializeSheets(&ctx, m_root, acptHlp.getAcpt());
	acptHlp.end();
	
	SerializeSupBookIds(ctx, binWriter);
	SerializeFileInfoCore(binWriter, ctx);
	SerializeConfigCore(binWriter, ctx);

	if (gs_security_doc)
	{
		KSerialWrapBinWriter acpt(binWriter, &ctx);
		gs_security_doc->serialSecDocInfo(m_workbook->GetCoreWorkbook(), &acpt);
	}
	
    if (m_extArgs.commitVersion == WO_QUERY_PLAY_ID)
        FillRevisionSelection(ctx, binWriter);

	if (isCanUseCache) {
		pCache->makeSignalSlice(binWriter, pUserConn);
		FillOut(pCache->getSignalSlice(), msgType_Init);
		return WO_OK;
	}
	
	if (m_bQueryOrCmd)
		MonitorRange_Proc(ctx, binWriter);

	FillOut(binWriter, msgType_Init);
	return WO_OK;
}

void ExecDetail::MakeUndoRedoRes(HRESULT ret, PCWSTR cmdName)
{
	EtTaskExecutor* te = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	te->ResetResForSelf();
	binary_wo::VarObj res = te->GainResItemForSelf();
	if(FAILED(ret)) 
		res.add_field_str("errName", __X("E_UNDOREDO_ERROR"));
	res.add_field_str("cmdName", cmdName);
	std::vector<ks_wstring> vecVersionTag = m_workbook->popUndoVersionTag();
	if (!vecVersionTag.empty())
	{
		binary_wo::VarObj tagArr = res.add_field_array("versionTags", binary_wo::typeString);
		for (auto tag : vecVersionTag)
			tagArr.add_item_str(tag.c_str());
	}

	if(m_root.has("callBackId"))
		res.add_field_uint32("callBackId", m_root.field_uint32("callBackId"));
}

void ExecDetail::FillCallbackIds(binary_wo::BinWriter &writer)
{
	if(m_root.has("callBackId"))
		writer.addUint32Field(m_root.field_uint32("callBackId"), "callBackId");

	if (m_root.has("commands"))
	{
		binary_wo::VarObj cmds = m_root.get_s("commands");
		writer.addKey("res");
		writer.beginArray();
		for (int i = 0, cnt = cmds.arrayLength_s(); i < cnt; i++)
		{
			writer.beginStruct();
			binary_wo::VarObj cmd = cmds.at_s(i);
			binary_wo::VarObj param = cmd.get_s("param");
			if(param.has("callBackId"))
				writer.addUint32Field(param.field_uint32("callBackId"), "callBackId");
			writer.endStruct();
		}
		writer.endArray();
	}

	if (m_root.has("extendQueryCmds"))
	{
		binary_wo::VarObj extendQueryCmds = m_root.get_s("extendQueryCmds");
		writer.addKey("extendQueryResult");
		writer.beginArray();
		for (int i = 0, cnt = extendQueryCmds.arrayLength_s(); i < cnt; i++)
		{
			writer.beginStruct();
			binary_wo::VarObj cmd = extendQueryCmds.at_s(i);
			binary_wo::VarObj param = cmd.get_s("param");
			if(param.has("callBackId"))
				writer.addUint32Field(param.field_uint32("callBackId"), "callBackId");
			writer.endStruct();
		}
		writer.endArray();
	}
}

WebInt ExecDetail::ExecCmd()
{
	ResetTypeBreaks();

	std::unique_ptr<wo::util::SlowCallTimeStat> cmStepTime;
	ResetSlowCallCM(cmStepTime, m_workbook, "ExecCmd_BeforeExec", KU32MetricItem::kBeforeExecTime);
	
	ApiCallTimeStat apiCallTime(m_workbook);
	if (!g_WebEtCoopCmd.empty())
		m_root.setVerifyRoot(&g_WebEtCoopCmd);

	if (m_bHttpCalling)
	{
		// server api没有query init, 没有时机触发prepare user
		WebInt res = verifyAndPrepareUserConn();
		if (res < 0)
		{
			WOLOG_ERROR << "Exec prepare user failed";
			return res;
		}
	}

	m_workbook->incCmdCount();

	DocMemoStat::MarkScope markScope(
		m_workbook->GetMemoStat(), __X("exec"), true, m_root.get_s("commands"));

	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	m_workbook->coreMetric().onBeginExec();

	TrackerScope trackerScope(pBook);
	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();
	m_workbook->clearInitCache();
	pBook->GetWoStake()->getCacheBuildingOnCell()->invalidate();

	SCOPE_AutoResume<bool> scopeBool(m_bQueryOrCmd, true);

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	constexpr bool forceConn = false; // default
	constexpr bool isExecCmd = true;
	InitContext(ctx, forceConn, isExecCmd);
	ctx.setState(CtxState::Exec);
	m_workbook->collectInfo().checkIllegalReuse(static_cast<IKETUserConn*>(ctx.getUser()), m_bHttpCalling);

	util::IgnoreHistoryGuard ignoreHistoryGuard(&ctx, false);
	if (m_bHttpCalling) // 服务端 API 调用不写单元格记录(后面有需要再去掉)
	{
		ignoreHistoryGuard.enable();
	}
	
	if (DoReExec())
		return WO_OK;

	ResetCbSubscriptions();
	m_workbook->clearCellImgHiddenCache();
    m_workbook->FetchCurrentUserOrganizeInfo();

	BOOL bEnableVersionTag = _kso_GetWoEtSettings()->IsEnableVersionTag();
	
	HRESULT ret = S_OK;
	WebInt prevSeq = m_curUserConn->getCmdSeq();
    BOOL bUndo = FALSE;
	BOOL bFillLog = FALSE;
	ResetSlowCallCM(cmStepTime, m_workbook, "ExecCmd_Exec", KU32MetricItem::kExecTime);
	if (m_seq > prevSeq || m_seq == -1) // -1表示的是服务器本地调用。
	{
		if (pBook->GetBMP()->bDbSheet || pBook->GetBMP()->bKsheet)
			updateEnableCommonLog();
	
		util::DbCdcScope cdcScope(m_workbook);
		DbLineHistoriesScope lineHistoriesScope(pBook, m_workbook);

		if (m_root.has("undo"))
		{
			int undoStep = 1;
			if (m_root.has("undoStep"))
				undoStep = m_root.field_int32("undoStep");
			WebStr versionTag = nullptr;
			if (bEnableVersionTag && m_root.has("versionTag"))
				versionTag = m_root.field_str("versionTag");
			bool allowPartialSucc = true;
			if (m_root.has("allowPartialSucc"))
				allowPartialSucc = m_root.field_bool("allowPartialSucc");
			if (m_workbook->undo(&ctx, versionTag, allowPartialSucc, undoStep) != WO_OK)
			{
				bUndo = TRUE;
				ret = E_UNDOREDO_ERROR;
			}		
			else
			{
				pExecutor->SetDirty(true);
			}

			MakeUndoRedoRes(ret, __X("undo"));
			bFillLog = TRUE;
		}
		else if (m_root.has("redo"))
		{
			WebStr versionTag = nullptr;
			if (bEnableVersionTag && m_root.has("versionTag"))
				versionTag = m_root.field_str("versionTag");
			int redoStep = 1;
			if (m_root.has("redoStep"))
				redoStep = m_root.field_int32("redoStep");
			if (m_workbook->redo(&ctx, versionTag, redoStep) != WO_OK)
			{
				ret = E_UNDOREDO_ERROR;
			}
			else
			{
				pExecutor->SetDirty(true);
			}
			MakeUndoRedoRes(ret, __X("redo"));
			bFillLog = TRUE;
		}
		// 有个别命令执行前要做一次undo,如重新执行选择性粘贴
		if (m_root.has("commands"))
		{
			if (bUndo)
			{
				Respond(ctx, ret);
			}	
			KwTasks tasks;
			binary_wo::VarObj cmds = m_root.get_s("commands");
			int32 len = cmds.arrayLength_s();
			tasks.reserve(len);
			for (int32 i = 0; i < len; ++i)	{
				binary_wo::VarObj cmdVar = cmds.at(i);
				if (i < len - 1 && (pBook->GetBMP()->bKsheet || pBook->GetBMP()->bDbSheet))
				{
					cmdVar.add_field_bool("__skipDbtViewsUpdate__", true);
				}
				if (m_serverExtraParam.has("isForbidden"))
				{
					binary_wo::VarObj cmdsCensorRes = m_serverExtraParam.get_s("isForbidden");
					int32 censorResLen = cmdsCensorRes.arrayLength_s();
					if (censorResLen != cmds.arrayLength_s())
						censorResLen = -1;
					if (censorResLen > 0 && i < censorResLen)
					{
						bool censorRes = cmdsCensorRes.item_bool(i);
						if(censorRes)
							cmdVar.add_field_bool("__serCensorRes__", true);
					}
				}
				if (m_serverExtraParam.has("copyAttachmentData"))
				{
					VarObj srcData = m_serverExtraParam.get_s("copyAttachmentData");
					VarObj param = cmdVar.get_s("param");
					int dataCnt = srcData.arrayLength_s();
					if (dataCnt > 0 && !param.has("copyAttachmentData"))
					{
						VarObj tarData = param.add_field_structArray("copyAttachmentData", dataCnt);
						VarObj::_copyArray(tarData.getVar(), srcData.getVar(), tarData.getRoot());
					}
				}
				if (m_serverExtraParam.has("passwordKeys"))
				{
					VarObj srcData = m_serverExtraParam.get_s("passwordKeys");
					VarObj param = cmdVar.get_s("param");
					int dataCnt = srcData.arrayLength_s();
					if (dataCnt > 0 && !param.has("passwordKeys"))
					{
						VarObj tarData = param.add_field_structArray("passwordKeys", dataCnt);
						VarObj::_copyArray(tarData.getVar(), srcData.getVar(), tarData.getRoot());
					}
				}
				if (m_serverExtraParam.has("smsCodeVerifyPass"))
				{
					cmdVar.add_field_bool("smsCodeVerifyPass", m_serverExtraParam.field_bool("smsCodeVerifyPass"));
				}
				
				KwTask* pTask = new KwTask(
					KwCommand::copyFrom(cmdVar),
					InitVersion,
					USER_INNER_INV_ID,
					NULL,
					ctx.getBaseDataVersion()
				);
				tasks.emplace_back(pTask);
			}

			if (m_root.has("transOpByServer"))
			{
				ASSERT(m_root.field_bool("transOpByServer"));
				int32 transDataBase = m_root.field_int32("transDataBase");
				int32 transTaskBase = m_root.field_int32("transTaskBase");
				m_workbook->transformClientTasks(tasks, transDataBase, transTaskBase, &ctx);
			}

			WebInt ret = m_workbook->executeTasks(tasks, &ctx);
			if (ret != WO_OK)
			{
				ExecSubscribeCrossBooks();
				NotifyCrossBooksUpdate();
				return ExecInitImpl(false);
			}
			m_workbook->getBatchTransHelper().moveFillLogToTasks(tasks);
			ClearApiCommandBeforeCommit(tasks);
			FillLog(&ctx, tasks);
			bFillLog = FALSE;
		}

		ctx.SubmitEventTrackingInfo();

		ExecSubscribeCrossBooks();
		NotifyCrossBooksUpdate();
        NotifyCustomListUpdate();
		UpdateUserColPermsStatus(&ctx);

		if (bFillLog)
		{
			KwTasks tasks;
			m_workbook->getBatchTransHelper().moveFillLogToTasks(tasks);
			if (tasks.empty())
				FillLog(&ctx);
			else
			 	FillLog(&ctx, tasks);
		}
		IKWorksheets* pSheets = m_workbook->GetCoreWorkbook()->GetWorksheets();
        ks_stdptr<IWebhookManager> spWebhookMgr;
        pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
        spWebhookMgr->setBookHasHidden(m_workbook->isBookProtectedWithHiddenProperty() ? TRUE : FALSE);
        util::CollectAndSendFormulaResChangeByWebhook(pBook, pSheets);
	}

	// 临时代码
	WOLOG_INFO << "[" << m_connID << "]" << " (exec) server version: " << m_workbook->getVersionMgr()->getCurTaskVersionID()
		<< " bHttpCalling: " << m_bHttpCalling
		<< " svrFrom: " << util::toSvrConnFromStr(m_extArgs.connFrom)
		<< " svrScene: " << util::toSvrConnSceneStr(m_extArgs.connScene)
		<< " svrLife: " << util::toSvrConnLifeStr(m_extArgs.connLife);

	ResetSlowCallCM(cmStepTime, m_workbook, "ExecCmd_AfterExec", KU32MetricItem::kAfterExecTime);
	if (m_bHttpCalling)
	{
		binary_wo::BinWriter *pResponse = ctx.getHttpResponse();
		
		unsigned int binSize = pResponse->getBufferWriteLength() + pResponse->getNameBufferWriteLength();
		m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kAfterExecBytes, binSize);
		
		FillOut(*pResponse, msgType_Exec);
		// task执行中可能会往 self res 写入信息, 但server api的调用不需要这些信息
		// 不进行读取, 直接清空 self res, 与websocket的事务执行保持一致
		EtTaskExecutor* te = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
		binary_wo::VarObj res = te->GetResForSelf();
		if (res.getVar())
			te->ResetResForSelf();

		FillOther();
	}
	else
	{
		std::unique_ptr<wo::DbSheet::DisableDbProtectScope> upDisablePrt;
		if (ctx.IsNoCheckPerms() && (pBook->GetBMP()->bKsheet || pBook->GetBMP()->bDbSheet))
		{
			ks_stdptr<IUnknown> spUnknown;
			VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
			ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
			if (spDBUserGroups)
			{
				ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
				spDBUserGroups->GetJudgement(&spProtectionJudgement);
				upDisablePrt.reset(new wo::DbSheet::DisableDbProtectScope(spProtectionJudgement));
			}
		}
		Respond(ctx, ret);
	}

	m_workbook->doClearJobAfterExecCmd();
	return WO_OK;	
}

WebInt ExecDetail::ExecUpdateShapes()
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	InitContext(ctx);

	INT16 dpi = 0;
	binary_wo::VarObj extra = m_root.get_s("extra");
	binary_wo::VarObj varDpi = m_root.get_s("dpi");
	if (varDpi.empty())
		varDpi = extra.get_s("dpi");
	if (!varDpi.empty())
		dpi = varDpi.value_int32();
	if (dpi)
		m_curUserConn->setDpi(dpi);

	WebInt ret = WO_OK;
	if (m_root.has("objSheetOrBook") && m_root.has("shapeIds"))
	{
		WebID objSheetOrBook = m_root.field_web_id("objSheetOrBook");
		binary_wo::VarObj ids = m_root.get_s("shapeIds");
		int32 len = ids.arrayLength_s();

		std::vector<uint32> shapeIds;
		for (int32 i = 0; i < len; ++ i)
		{
			shapeIds.push_back(ids.at(i).value_uint32());
		}

		ks_wstring updateType;
		if (m_root.has("updateType"))
		{
			updateType = m_root.field_str("updateType");
		}

		std::vector<uint32> shapeLimitSizes;
		if (m_root.has("limitSizes"))
		{
			binary_wo::VarObj litmitSize = m_root.get_s("limitSizes");
			int32 limitSizeLen = litmitSize.arrayLength_s();
			for (int32 i = 0; i < limitSizeLen; ++i)
			{
				shapeLimitSizes.push_back(litmitSize.at(i).value_uint32());
			}
		}

		binary_wo::BinWriter writer;
		m_workbook->UpdateShapes(objSheetOrBook, shapeIds, shapeLimitSizes, updateType, &ctx, writer);
		FillOut(writer, msgType_updateShapes);
	}

	return WO_OK;
}

WebInt ExecDetail::ExecComment()
{
	WOLOG_INFO << "[ExecDetail::ExecComment] start!!!";

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	InitContext(ctx);

    VarObj cmds = m_root.get_s("commands");
    for (int32 i = 0, len = cmds.arrayLength_s(); i < len; ++i) {
        const VarObj& cmd = cmds.at(i);
		WebStr name = cmd.field_str("name");
		WOLOG_INFO << "[ExecDetail::ExecComment] comment cmd name=" << name;
        if ((xstrcmp(name, __X("range.deleteCommentItem")) == 0)
			|| (xstrcmp(name, __X("range.editCommentItem")) == 0)
		)
		{// 评论只支持以上命令

            VarObj param = cmd.get_s("param");
			// comment_id 已经由服务器鉴权。这里只是防止伪造数据。
			if (!param.has("id"))//range.deleteCommentItem range.editCommentItem这两个命令必须得有要操作的id
			{
				WOLOG_INFO << "[ExecDetail::ExecComment] comment id is empty!!!!";
				Respond(ctx, WO_FAIL);
				return WO_FAIL;
			}
				
			//获取评论的信息
			IDX sheetIdx = GetSheetIdx(m_workbook->GetCoreWorkbook()->GetBook(), param);
			RANGE rg = wo::ReadRangeInl(m_workbook->GetBMP(), sheetIdx, param);
			if (!rg.IsValid())
			{
				WOLOG_INFO << "[ExecDetail::ExecComment] range is invalid!!!!";
				Respond(ctx, WO_FAIL);
				return WO_FAIL;
			}
			
			ks_stdptr<IWoComment> spCmt;
			m_workbook->getWoComment(rg, false, &spCmt);
			if(spCmt == nullptr)
			{
				Respond(ctx, WO_FAIL);
				return E_FAIL;
			}

			WebStr id = param.field_str("id");

			if (spCmt->Empty())
			{
				WOLOG_INFO << "[ExecDetail::ExecComment] comment is empty";
				Respond(ctx, WO_FAIL);
				return E_FAIL;
			}

			IWoCommentChain* pWoCommentChain = nullptr;
			if (param.has("chainId"))
			{
				pWoCommentChain = spCmt->GetChainById(false, param.field_str("chainId"));
				if (pWoCommentChain == nullptr)
				{
					pWoCommentChain = spCmt->GetChainById(true, param.field_str("chainId"));
				}
			}
			else
			{
				pWoCommentChain = spCmt->GetChainById(false, 0);
			}

			if (pWoCommentChain == nullptr)
			{
				WOLOG_INFO << "[ExecDetail::ExecComment] can not find a chain to get item";
				Respond(ctx, WO_FAIL);
				return E_FAIL;
			}

			IWoCommentItem* pItem = pWoCommentChain->GetItemById(id);
			if(pItem == nullptr)
			{
				WOLOG_INFO << "[ExecDetail::ExecComment] cant not find the comment object,comment id=" << id;
				Respond(ctx, WO_FAIL);
				return WO_FAIL;
			}

			//判断评论item的user与当前user是否匹配。

			if (xstrcmp(pItem->GetUserID(), ctx.getUser()->userID()) != 0)
			{
				WOLOG_INFO << "[ExecDetail::ExecComment] user id=" << (ctx.getUser()->userID()) << ",dont has right to operator=" << name;
				Respond(ctx, WO_FAIL);
                return WO_FAIL;
			}
			WOLOG_INFO << "[ExecDetail::ExecComment] user id=" << (ctx.getUser()->userID()) << ",has right to operator=" << name;

		}
		else if((xstrcmp(name, __X("range.setCommentProp")) == 0)
			|| (xstrcmp(name, __X("range.deleteComment")) == 0)
		)
		{
			//Todo:这种要删除整个单元格的comment，但是里面的每一个comment item可能是不同人的，这种情况怎么处理还得找产品
		}

		else if (xstrcmp(name, __X("range.insertCommentItem")) == 0)
		{
			// 插入评论无须鉴权
		}
		else
		{
			WOLOG_INFO << "[ExecDetail::ExecComment] invalid operator name=" << name;
			Respond(ctx, WO_FAIL);
			return WO_FAIL;
		}	
			
    }
	WOLOG_INFO << "[ExecDetail::ExecComment] pass comment cmd!!!";
	return ExecDetail::ExecCmd();
}

WebInt ExecDetail::ExecGetImages()
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext &ctx = *ctxPtr;
	InitContext(ctx);
	binary_wo::BinWriter writer;
	{
		
		binary_wo::VarObj rootImages = m_root.get_s("images");
		int imagelen = rootImages.arrayLength_s();
		if (imagelen <= 0)
		{
			writer.addStringField(__X("get picture failed."), "error");
			return WO_OK;
		}

		wo::Timer time(50);

		binary_wo::ArrayWriter images(writer, "images");
		for (int i = 0; i < imagelen; ++i)
		{
			binary_wo::VarObj imageObj = rootImages.at(i);
			if (!imageObj.has("image_id") || !imageObj.has("token"))
				continue;

			uint32 imageId = imageObj.field_uint32("image_id");
			WebStr token = imageObj.field_str("token");

			binary_wo::StructWriter imageInfo(writer);
			writer.addStringField(token, "token");

			// TODO 缓存sha1
			QByteArray sha1;
			QByteArray picData;
			int width = 0, height = 0;

			// find cache
			ImgsCacheMap::const_iterator it = m_ImgsCacheMap.find(imageId);
			if (it != m_ImgsCacheMap.end())
			{
				ImgCacheVal val = (*it).second;
				sha1 = val.sha1;
				width = val.width;
				height = val.height;

				writer.addStringField(krt::utf16(QString(sha1)), "sha1");
				writer.addFloat64Field(width, "width");
				writer.addFloat64Field(height, "height");
				continue;
			}

			bool ret = KImageSerializer::exportImage(imageId, sha1, picData, width, height);
			if (ret)
			{
				if (!picData.isEmpty())
				{
					WebSlice slice = {(WebByte *)(picData.data()), picData.size()};
					gs_callback->uploadImage(sha1.data(), NULL, &slice, nullptr);
				}
				writer.addStringField(krt::utf16(QString(sha1)), "sha1");
				writer.addFloat64Field(width, "width");
				writer.addFloat64Field(height, "height");

				// cache
				m_ImgsCacheMap.erase(imageId);
				ImgCacheVal val = {sha1, width, height};
				m_ImgsCacheMap.insert(std::make_pair(imageId, val));
			}
			else
			{
				writer.addStringField(__X("get picture failed."), "error");
			}

			if (time.isExpired())
				break;
		}
	}

	if (gs_callback->signal)
	{
		binary_wo::BinWriter::StreamHolder shbt = writer.buildStream();
		WebSlice slice = {shbt.get(), writer.writeLength()};
		gs_callback->signal(m_connID, getMsgTypeName(msgType_getImages), &slice);
	}

	return WO_OK;
}



void ExecDetail::Respond(KEtRevisionContext& ctx, WebInt ret)
{
	if (m_extArgs.isServerLocalExec)
		return;

	if (ret == S_OK) {
		wo::util::SlowCallTimeStat slowCall(
			"Respond", util::kDefSlowCallThreshold, [this](unsigned int val) {
			this->m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kRespondTime, val);
		});
		bool bSerializeDelta = false;
		if (m_root.has("serializeDelta"))
		{
			bSerializeDelta = m_root.field_bool("serializeDelta");
		}

		bool isReInit = !m_bHttpCalling && (!ctx.IsUserHasSerialObjs() || ctx.isReSerialize());
		binary_wo::BinWriter bw;
		KBinWriterSizeU32Mc mcBinSize(&m_workbook->coreMetric(), &bw, KU32MetricItem::kAfterExecBytes);
		SyncSignalSize();
		WebInt ret = bSerializeDelta && !isReInit ? m_workbook->serializeVersions(&ctx, &bw) : WO_OK;
		if (ret != WO_OK || isReInit)
		{
			if (!m_bHttpCalling && !ctx.IsUserHasSerialObjs())
			{
				WoFileIncludeCollector collector(m_workbook, __X("behaviour_respond_reinit_no_serail_obj"), 1);
				collector.addCmdName(ctx.getUser()->connID()).collect();
			}
			ExecInitImpl(false);
		}
		else
		{
			if (bSerializeDelta)
			{
				METRICS_TIME_BINSIZE("Respond_1", this->m_workbook, bw, KU32MetricItem::kRespondStep1Time, KU32MetricItem::kRespondStep1Bytes)
				
				CustomWebAcptHelp acptHlp(m_workbook, &bw, &ctx);
				acptHlp.begin();
				m_workbook->serializeSheets(&ctx, m_root, acptHlp.getAcpt());
				m_workbook->serializeXfs(&ctx, m_root, acptHlp.getAcpt());
				BP_Proc(ctx, bw, acptHlp.getAcpt());
				acptHlp.end();

				METRICS_TIME_BINSIZE_RESTART("Respond_2", this->m_workbook, KU32MetricItem::kRespondStep2Time, KU32MetricItem::kRespondStep2Bytes)
				SerialPageSetupData(ctx, bw);
				SerialCbReferAutoUpdateState(ctx, bw);
				SerialBeautifyOperators(ctx, bw);
				SerialExclusiveRangeData(ctx, bw);

				METRICS_TIME_BINSIZE_RESTART("Respond_3", this->m_workbook, KU32MetricItem::kRespondStep3Time, KU32MetricItem::kRespondStep3Bytes)
				AddExtState(bw, ctx);

				FillOut(bw, msgType_Exec);
			}
			else
			{
				bw.addBoolField(false, "serializeDelta");
				FillOut(bw, msgType_Exec);
			}
		}

		EtTaskExecutor* te = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
		if (te->IsDirty())
		{
			te->SetDirty(false);
			FillOther();
		}
	}
	else
	{
		FillError(ret);
	}
}

void ExecDetail::FillRevisionSelection(KEtRevisionContext &ctx, binary_wo::BinWriter &bw)
{
	bw.addBoolField(true, "queryPlay");
	const LastTaskSelection *lts = m_workbook->getLastTaskSelection();
	if (lts == NULL || lts->first < 0)
		return;

	bw.addKey("revisionSelection");
	bw.beginStruct();
	bw.addInt32Field(lts->first, "sheetIdx");
	bw.addKey("ranges");
	bw.beginArray();
	for (size_t i = 0; i < lts->second.size(); i++)
	{
		const RANGE *pRg = &(lts->second.at(i));
		bw.beginStruct();
		bw.addInt32Field(pRg->RowFrom(), "rowFrom");
		bw.addInt32Field(pRg->RowTo(), "rowTo");
		bw.addInt32Field(pRg->ColFrom(), "colFrom");
		bw.addInt32Field(pRg->ColTo(), "colTo");
		bw.endStruct();
	}
	bw.endArray();
	bw.endStruct();
}

void ExecDetail::InitContext(KEtRevisionContext& ctx, bool forceConn/* = false*/, bool isExecCmd/* = false*/)
{
	bool isKsheet = m_workbook->GetBMP()->bKsheet;

	// et 回放时也需要 commitVersion
	initContextVersion(m_root, forceConn, m_curUserConn, &ctx, m_extArgs.commitVersion);
	if (m_root.has("isAllMutableRes") && m_root.field_bool("isAllMutableRes"))
	{
		ctx.setAllMutableResult(true);
	}

	// fixme: 后面再考虑是否移除日志
	if (isKsheet && isExecCmd)
	{
		QDateTime date = QDateTime::fromSecsSinceEpoch(ctx.getCurCommitVersionTime());
		WOLOG_INFO << "[cell_history] ctx init: commitVersion: " << ctx.getCurCommitVersion() << ", date: " << date.toStringEx("yyyy-MM-ddTHH:mm:ssZ");
	}
	ctx.setNeedCleanChildProc(m_extArgs.bNeedCleanChildProc); 
	if (m_extArgs.userRequestInfo)
		ctx.setUserRequestInfo(m_extArgs.userRequestInfo);
}

WebInt ExecDetail::ExecCmdDirect()
{
	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();

	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();

	m_workbook->clearInitCache();
	pBook->GetWoStake()->getCacheBuildingOnCell()->invalidate();
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	ks_stdptr<IUnknown> spUnknown;
    pBook->GetExtDataItem(edDBUserGroups, &spUnknown);
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	if (spDBUserGroups)
	{
    	spDBUserGroups->GetJudgement(&spProtectionJudgement);
	}
	DbSheet::DisableDbProtectScope disPtScope(spProtectionJudgement);

	constexpr bool isExecCmd = true;
	InitContext(ctx, true, isExecCmd);

	ctx.setExecDirect();

	if (ctx.isInRevisionMode())
		m_workbook->resetLastTaskSelection();

	util::IgnoreHistoryGuard ignoreHistoryGuard(&ctx, false);
	if (m_bHttpCalling) // 服务端 API 调用不写单元格记录(后面有需要再去掉)
	{
		ignoreHistoryGuard.enable();
	}

	m_workbook->clearCellImgHiddenCache();
    m_workbook->FetchCurrentUserOrganizeInfo();

	BOOL bEnableVersionTag = _kso_GetWoEtSettings()->IsEnableVersionTag();
	util::DbCdcScope cdcScope(m_workbook);
	WebInt ret = WO_OK;
	if (m_root.has("undo"))
	{
		int undoStep = 1;
		if (m_root.has("undoStep"))
			undoStep = m_root.field_int32("undoStep");
		WebStr versionTag = nullptr;
		if (bEnableVersionTag && m_root.has("versionTag"))
			versionTag = m_root.field_str("versionTag");
		bool allowPartialSucc = true;
		if (m_root.has("allowPartialSucc"))
			allowPartialSucc = m_root.field_bool("allowPartialSucc");
		ret = m_workbook->undo(&ctx, versionTag, allowPartialSucc, undoStep);
		WO_LOG_X(ctx.getLogger(), WO_LOG_INFO,"undo");

	}
	else if (m_root.has("redo"))
	{
		WebStr versionTag = nullptr;
		if (m_root.has("versionTag"))
			versionTag = m_root.field_str("versionTag");
		int redoStep = 1;
		if (bEnableVersionTag && m_root.has("redoStep"))
			redoStep = m_root.field_int32("redoStep");
		ret = m_workbook->redo(&ctx, versionTag, redoStep);
		WO_LOG_X(ctx.getLogger(), WO_LOG_INFO,"redo");
	}
	// 有个别命令执行前要做一次undo,如重新执行选择性粘贴
	if (m_root.has("commands"))
	{
		KwTasks tasks;
		binary_wo::VarObj cmds = m_root.get_s("commands");
		for (int32 i = 0, len = cmds.arrayLength_s(); i < len; ++i)
		{
			binary_wo::VarObj cmdVar = cmds.at(i);
			if (i < len - 1 && (pBook->GetBMP()->bKsheet || pBook->GetBMP()->bDbSheet))
			{
				cmdVar.add_field_bool("__skipDbtViewsUpdate__", true);
			}
			KwTask* pTask = new KwTask(
					KwCommand::copyFrom(cmdVar),
					InitVersion,
					USER_INNER_INV_ID,
					NULL,
					ctx.getBaseDataVersion()
				);
			tasks.push_back(pTask);
		}
		m_workbook->execTasksDirect(tasks, &ctx);
	}

	m_workbook->resetConnState(&ctx);
	m_workbook->doClearJobAfterExecCmd();

	return WO_OK;
}

/* 执行XVA扩展命令
	* 输入:
	* {
	*     "command": "xxxx"
	*     "seq": x,
	*     "param":
	*      {
	*      }
	* }
	*
	* 输出:
	* {
	*     "command": "xxxx",
	*     "seq": x,
	*     "result":
	*     {
	*     }
	* }
	* */
WebInt ExecDetail::ExecExtraCommand()
{
	// log_printf("ExecDetail::ExecExtraCommand()");

	if (m_workbook == nullptr)
	{
		WebStr name = m_root.field_str("command");
		if (xstrlen(name) == 0)
		{
			return XVA_INVALID_ARG;
		}

		WebInt ret = WO_FAIL;
		binary_wo::BinWriter writer;
		writer.addStringField(m_root.field_str("command"), "command");
		if (m_root.has("seq"))
			writer.addInt32Field(m_root.field_int32("seq"), "seq");

		binary_wo::StructWriter result(writer, "result");
		{
			XvaCommand xva(m_wbs, m_workbook);
			VarObj param = m_root.get_s("param");	// 可以为空
			ret = xva.ExecExtraCmd(name, m_root, param, writer);
			
			if (ret == XVA_COMMAND_NOT_SUPPORT)
			{
				writer.addInt32Field(E_INVALIDARG, "error");
				ret = XVA_COMMAND_NOT_SUPPORT;
			}

			//构造数据通过回调回传
			binary_wo::BinWriter::StreamHolder shbt = writer.buildStream();
			WebSlice slice = {shbt.get(), writer.writeLength()};
			if (gs_callback->extraCommand)
				gs_callback->extraCommand(&slice);
		}

		return ret;
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	if (ctx.getProtectionCtx()->isBookHasHiddenProperty())
	{
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK;
	}

	if (!m_root.has("command"))
	{
		WO_LOG_X(ctx.getLogger(), WO_LOG_WARN, "command not found");
		return XVA_INVALID_ARG;
	}

	WebStr name = m_root.field_str("command");
	if (xstrlen(name) == 0)
	{
		WO_LOG_X(ctx.getLogger(), WO_LOG_WARN, "command is empty");
		return XVA_INVALID_ARG;
	}

	//binary_wo::VarObj param = m_root.get_s("param");

	WebInt ret = WO_FAIL;
	binary_wo::BinWriter writer;
	writer.addStringField(m_root.field_str("command"), "command");
	if (m_root.has("seq"))
		writer.addInt32Field(m_root.field_int32("seq"), "seq");

	binary_wo::StructWriter result(writer, "result");
	{
		XvaCommand xva(m_wbs, m_workbook);
		VarObj param = m_root.get_s("param");	// 可以为空
		ret = xva.ExecExtraCmd(name, m_root, param, writer);
		
		if (ret == XVA_COMMAND_NOT_SUPPORT)
		{
			WO_LOG_X(ctx.getLogger(), WO_LOG_WARN, "command not supported");
			writer.addInt32Field(E_INVALIDARG, "error");
			ret = XVA_COMMAND_NOT_SUPPORT;
		}

		//构造数据通过回调回传
		binary_wo::BinWriter::StreamHolder shbt = writer.buildStream();
		WebSlice slice = {shbt.get(), writer.writeLength()};
		if (gs_callback->extraCommand)
			gs_callback->extraCommand(&slice);
	}

	return ret;
}

void ExecDetail::SummaryTile_TabClr(ISerialAcceptor* acpt, ISheet* pSheet, IBook* pBook)
{
	// 标签颜色
	const EtColor* pColor = NULL;
	pSheet->GetTabClr(&pColor);

	if (!pColor || pColor->getType() == ectNONE)
	{
		return;
	}

	DWORD color = pBook->ToARGB(*pColor);
	acpt->addUint32("color", color);
}

void ExecDetail::SummaryTile_SheetType(ISerialAcceptor* acpt, ISheet* pSheet)
{
	SHEETTYPE type;
	pSheet->GetFullType(&type);
	ks_wstring sType = pSheet->GetWoStake()->getTypeStr(type);
	if (sType == __X("stUnknown")) 
	{
		sType = __X("xlUnknown");
	}
	acpt->addString("type", sType.c_str());

}

void ExecDetail::ExecSummaryTile_comment(ISerialAcceptor* acpt, IKWorksheets* pWss, int index)
{
	ks_stdptr<IKWorksheet> ws = pWss->GetSheetItem(index);
	if (ws.get() == nullptr) 
		return;

	ks_stdptr<ICellComments> pComments = ws->GetComments();
	if (pComments.get() == nullptr)
		return;
	
	INT nCount = 0;
	INT visibleCmtCnt = 0;
	pComments->GetCount(&nCount);
	for (size_t i = 0; i < nCount; ++i)
	{
		ks_stdptr<ICellComment> spComment;
		pComments->GetItem(i, &spComment);
		if (!spComment)
			continue;

		ks_stdptr<IWoComment> spWoCmt;
		spComment->GetWoComment(&spWoCmt);
		if (!spWoCmt || spWoCmt->Empty())
			continue;
		AbstractModel* pModel = spWoCmt->GetShape();
		if (isCommentHidden(pModel))
			continue;
		if (spWoCmt->GetChainCount(false) > 0)
			visibleCmtCnt++;
	}

	WOLOG_INFO << "commentCount: " << visibleCmtCnt;

	acpt->addInt32("commentCount", visibleCmtCnt);
}

void ExecDetail::SummaryTile_SheetIsShare(ISerialAcceptor* acpt, IKWorksheets* pWss, int index)
{
	ks_stdptr<IKWorksheet> spWorkSheet = pWss->GetSheetItem(index);
	if (spWorkSheet == nullptr) 
		return;

	bool bShared = false;
	ks_stdptr<ISheetProtection> spSheetProtection = spWorkSheet->GetProtection();
	if(spSheetProtection)
	{
		if(spSheetProtection->GetSharedLink())
			bShared = true;
	}
	acpt->addBool("isShare", bShared);
}

WebInt ExecDetail::ExecSummaryTile()
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	_Workbook* wb = m_workbook->GetCoreWorkbook();
	ks_stdptr<IKWorksheets> wss = wb->GetWorksheets();

	IBook* bk = wb->GetBook();
	IWorkbookObj* wbObj = wb->GetWoObject();

	binary_wo::BinWriter ww;	
	{
		KSerialWrapBinWriter acpt(ww, ctx.get());
		sa::Leave sheetsLeave = sa::enterArray(&acpt, "sheets");

		INT sheetCount = 0;
		bk->GetSheetCount(&sheetCount);
		for (INT32 i = 0; i < sheetCount; ++i)
		{

			ks_stdptr<ISheet> spSheet = NULL;
			if (FAILED(bk->GetSheet(i, &spSheet)))
			{
				continue;
			}

			// 隐藏的sheet过滤掉
			SHEETSTATE state = ssVisible;
			spSheet->GetVisible(&state);
			if (state != ssVisible)
				continue;

			sa::Leave anonymousLeave = sa::enterStruct(&acpt, nullptr);

			// 标签颜色
			SummaryTile_TabClr(&acpt, spSheet, bk);

			acpt.addInt32("id", i);

			PCWSTR str = NULL;
			spSheet->GetName(&str);
			acpt.addString("name", str);

			acpt.addBool("isProtected", spSheet->IsProtected());

			SummaryTile_SheetType(&acpt, spSheet);

			ExecSummaryTile_comment(&acpt, wss, i);

			SummaryTile_SheetIsShare(&acpt, wss, i);

			acpt.addBool("isMergeFile", spSheet->IsMergeFileSheet());

			acpt.addBool("hasFilter", (spSheet->GetAutoFilters()->GetFilter(nullptr) != nullptr));
		}
	}

	FillOut(ww, msgType_SummaryTile);

	return WO_OK;
}

WebInt ExecDetail::ExecSummary()
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	_Workbook* wb = m_workbook->GetCoreWorkbook();
	ks_stdptr<IKWorksheets> wss = wb->GetWorksheets();

	IBook* bk = wb->GetBook();
	IWorkbookObj* wbObj = wb->GetWoObject();

	bool bFilter = false;
	binary_wo::BinWriter ww;	
	{
		KSerialWrapBinWriter acpt(ww, ctx.get());
		wbObj->serialConstValue(&acpt);
		sa::Leave sheetsLeave = sa::enterArray(&acpt, "sheets");
		for (INT32 cnt = wbObj->getSheetCount(), i = 0; i < cnt; ++i)
		{
			AbsObject* shtObj = wbObj->getSheetItem(i);
			sa::Leave anonymousLeave = sa::enterStruct(&acpt, nullptr);
			acpt.addObjectID("id", shtObj);

			ks_stdptr<ISheet> sht;
			VS(bk->GetSheet(i, &sht));
			acpt.addUint32("sheetStId", sht->GetStId());
			PCWSTR str = NULL;
			sht->GetName(&str);
			acpt.addString("sheetName", str);

			ISheetStake* ss = sht->GetWoStake();
			ss->exportBlocksSummary("blocks", shtObj, &acpt);

			if (!bFilter)
			{
				bFilter =
					(sht->GetAutoFilters()->GetFilter(nullptr) != nullptr);
			}

			ks_stdptr<IKWorksheet> ws = wss->GetSheetItem(i);
			if (ws.get() == nullptr) continue;

			Summary_ShapeInfo(&acpt, ws.get(), shtObj);
			Summary_tableStyle(&acpt, sht);
			Summary_sheetBackgroundImg(&acpt, sht);
		}
	}
	Summary_cellImage(ww, ctx.get());

	IKWorksheet* pActiveWs = wb->GetActiveWorksheet();
	if (pActiveWs)
	{
		IDX iSheet = INVALIDIDX;
		pActiveWs->GetSheet()->GetIndex(&iSheet);
		ww.addAbsObjIDField(ctx->getSheetMain(iSheet)->objId(), "currentSheetId");
	}
	ww.addBoolField(bFilter, "filter");

	FillOut(ww, msgType_Summary);

	return WO_OK;
}

void ExecDetail::Summary_ShapeInfo(ISerialAcceptor* acpt, IKWorksheet* ws, AbsObject* shtObj)
{
	ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
	oplGetSheetOplData(ws->GetSheet(), &spCanvas);
	if (!spCanvas)
		return;
	ks_castptr<EtShapeTree> shapeTree = spCanvas;
	
	INT32 shapeCnt = shapeTree->childCount();
	// 导出shapeId
	{
		sa::Leave shapeLeave = sa::enterArray(acpt, "shapeIds");
		for (int i = 0; i < shapeCnt; ++i)
		{
			acpt->addUint32(nullptr, shapeTree->childAt(i)->id());
		}
	}
	// 导出ProcessOn信息
	{
		sa::Leave shapeLeave = sa::enterArray(acpt, "processOn");
		const ks_wstring guidProcessOn(__X("E657119C-6982-421D-8BA7-E74DEB70A7D9"));
		for (int i = 0; i < shapeCnt; ++i)
		{
			drawing::AbstractShape* pShape = shapeTree->childAt(i);
			if(pShape->hasExtData())
			{
				const ks_wstring guid = pShape->extData()->extType();
				if (guid == guidProcessOn) 
				{	
					acpt->beginStruct();
					acpt->addString("shapeName", pShape->nameValue());
					acpt->addObjectID("objShape", pShape);
					acpt->addObjectID("objSheet", shtObj);
					IDX shtIdx = -1;
					ws->GetSheet()->GetIndex(&shtIdx);
					ASSERT(shtIdx >= 0);
					acpt->addInt32("sheetIdx", shtIdx);
					acpt->addKey("shapeIdxPath");
					acpt->beginArray();
						acpt->addUint32(nullptr, i);
					acpt->endArray();
					acpt->endStruct();
				}
			}
		}
	}
}

void ExecDetail::Summary_tableStyle(ISerialAcceptor* acpt, ISheet* pSht)
{
	sa::Leave sheetsLeave = sa::enterArray(acpt, "tableStyle");
	ks_stdptr<ICoreListObjects> spLstObjs;
	pSht->GetExtDataItem(edSheetListObjects, (IUnknown**)&spLstObjs);
	size_t lstObjCnt = spLstObjs->GetCount();
	for (size_t idx = 0; idx < lstObjCnt; idx++)
	{
		ks_stdptr<ICoreListObject> spLstObj;
		spLstObjs->GetItem(idx, &spLstObj);
		ASSERT(spLstObj);
		acpt->addUint32(nullptr, spLstObj->GetTableStyle());
	}

	ks_stdptr<pivot_core::IPivotTableHost> spTableHost;
	VS(pSht->GetExtDataItem(edSheetPivotTablesHost, (IUnknown**)&spTableHost));
	ASSERT(spTableHost);
	ks_stdptr<pivot_core::IPivotTables> spPivotTables = spTableHost->GetPivotTables();
	ASSERT(spPivotTables);
	UINT pvtTabcnt = spPivotTables->Count();
	for (UINT i = 0; i < pvtTabcnt; i++)
	{
		ks_stdptr<pivot_core::IPivotTable> spPvtTable = spPivotTables->Item(i);
		acpt->addUint32(nullptr, spPvtTable->GetTableStyle());
	}
}

void ExecDetail::Summary_sheetBackgroundImg(ISerialAcceptor* acpt, ISheet* pSht)
{
	ks_stdptr<IUnknown> spUnk;
	pSht->GetExtDataItem(edSheetBackground, &spUnk);
	ks_stdptr<ISheetBGPicture> spBackground = spUnk;
	if (!spBackground)
	{
		acpt->addBool("background", false);
		return;
	}

	ks_stdptr<IKBlipAtom> spAtom;
	spBackground->GetBackgroundPicture(&spAtom);
	if (!spAtom)
	{
		acpt->addBool("background", false);
		return;
	}

	acpt->addBool("background", true);
}

void ExecDetail::Summary_cellImage(binary_wo::BinWriter& ww, KEtRevisionContext* pCtx)
{
	KSerialWrapBinWriter acpt(ww, pCtx);
	sa::Leave sheetsLeave = sa::enterStruct(&acpt, "cellEmbeddedShape");
	AbsObject* objBook = m_workbook->GetCoreWorkbook()->getDocumentObject();
	ASSERT(objBook);
	acpt.addObjectID("bookObjId", objBook);

	ks_stdptr<IKDrawingCanvas> spCanvas;
	oplGetCellImgOplData(m_workbook->GetCoreWorkbook()->GetBook(), &spCanvas);
	ks_castptr<drawing::ShapeTree> shapeTree = spCanvas;
	INT32 shapeCnt = shapeTree->childCount();
	sa::Leave shapeLeave = sa::enterArray(&acpt, "shapeIds");
	for (int i = 0; i < shapeCnt; ++i)
	{
		acpt.addUint32(nullptr, shapeTree->childAt(i)->id());
	}
}

namespace {

struct StrHasher {
	size_t operator()(const ks_wstring& x) const {
		return alg::HashWString(x.c_str());
	}
};

} // anonymous

static void getDbDashboardAttachmentIds(ISheet* pSheet, ISerialAcceptor* pAcpt, ICustomStorageManager* pCustomStorMgr)
{
	if (!pSheet->IsDbDashBoardSheet())
		return;

	PCWSTR pValue = pCustomStorMgr->GetValue(pSheet->GetStId(), __X("dashboardTitleIconId"));
	if (!pValue)
		return;

	sa::Leave itemLeave = sa::enterStruct(pAcpt, nullptr);
	pAcpt->addString("fileId", pValue);
	pAcpt->addString("source", __X("base_storage"));
	pAcpt->addString("category", __X("attachment_image"));
}

static void getDbSheetAttachmentIds(ISheet* pSheet, ShareLinkContentVisibilityChecker* pVisibilityChecker, 
	ISerialAcceptor* acpt, GlobalSharedStringUnorderedSet& fileIdSet)
{
	ks_stdptr<IDBSheetOp> spOp;
	HRESULT hr = DbSheet::GetDBSheetOp(pSheet, &spOp);
	if (FAILED(hr))
		return;

	ks_stdptr<IDBSheetViews> spDbSheetViews;
        	DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews);

	ks_stdptr<IDBSheetViewsEnum> spEnum;
	if (SUCCEEDED(spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
	{
		do
		{
			ks_stdptr<IDBSheetView> spDbSheetView;
			spEnum->GetCurView(&spDbSheetView);
			if (!spDbSheetView)
				continue;

			PCWSTR fileId = spDbSheetView->GetBackgroundImageAttachment();
			PCWSTR source = spDbSheetView->GetBackgroundImageSource();
			if(xstrcmp(fileId, __X("")) != 0 && xstrcmp(source, __X("")) != 0)
			{
				if (!fileId || fileIdSet.find(GlobalSharedString(fileId)) != fileIdSet.end())
					continue;
				fileIdSet.emplace(fileId);
				acpt->beginStruct();
				acpt->addString("fileId", fileId);
				acpt->addString("source", source);
				acpt->addString("category", __X("background_image"));
				acpt->endStruct();
			}
		}while (SUCCEEDED(spEnum->Next()));
	}

	UINT sheetId = pSheet->GetStId();
	const IDBIds* pAllRecIds = spOp->GetAllRecords();
	const IDBIds* pAllFldIds = spOp->GetAllFields();
	IDbFieldsManager* pFieldsMgr = spOp->GetFieldsManager();
	EtDbIdx recCnt = pAllRecIds->Count();
	EtDbIdx fldCnt = pAllFldIds->Count();
	for (EtDbIdx fldIdx = 0; fldIdx < fldCnt; ++fldIdx)
	{
		EtDbId fieldId = pAllFldIds->IdAt(fldIdx);
		if (pVisibilityChecker && !pVisibilityChecker->IsFieldVisible(sheetId, fieldId))
			continue;
		ks_stdptr<IDbField> spField;
		pFieldsMgr->GetField(fieldId, &spField);
		ET_DbSheet_FieldType fieldType = spField->GetType();
		// 分享视图没有全部数据表的数据，所以需要处理引用字段
		if (fieldType == Et_DbSheetField_Lookup)
		{
			ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
			ks_stdptr<IDbField> spBaseField;
			spFieldLookup->GetLookupBaseField(&spBaseField);
			if (spBaseField)
				fieldType = spBaseField->GetType();
		}
		if (fieldType != Et_DbSheetField_Attachment && fieldType != Et_DbSheetField_Note)
			continue;
		for (int recIdx = 0; recIdx < recCnt; ++recIdx)
		{
			EtDbId recordId = pAllRecIds->IdAt(recIdx);
			if (pVisibilityChecker && !pVisibilityChecker->IsRecordVisible(sheetId, recordId))
				continue;
			const_token_ptr pToken = nullptr;
			hr = spOp->GetValueToken(recordId, fieldId, &pToken);
			if (FAILED(hr))
				continue;
			DbSheet::WalkThroughHandleTokenArray(pToken, [&](alg::TOKEN_HANDLE handle, DWORD handleType) {
				if (handleType == alg::ET_HANDLE_DBATTACHMENT)
				{
					ks_stdptr<IDbAttachmentHandle> spAttachmentToken = handle->CastUnknown();
					PCWSTR fileId = spAttachmentToken->GetFileId();
					if (!fileId || fileIdSet.find(GlobalSharedString(fileId)) != fileIdSet.end())
						return;
					fileIdSet.emplace(fileId);
					acpt->beginStruct();
					acpt->addString("fileId", fileId);
					PCWSTR source = __X("upload_ks3");
					if (spAttachmentToken->GetSource() == AttachmentSource_cloud)
						source = __X("cloud");
					acpt->addString("source", source);
					acpt->addString("category", __X("dbattachment"));
					acpt->endStruct();
				}
				else if (handleType == alg::ET_HANDLE_DBNOTE)
				{
					ks_stdptr<IDbNoteHandle> spNoteToken = handle->CastUnknown();
					PCWSTR fileId = spNoteToken->GetFileId();
					if (!fileId || fileIdSet.find(GlobalSharedString(fileId)) != fileIdSet.end())
						return;
					fileIdSet.emplace(fileId);
					acpt->beginStruct();
					acpt->addString("fileId", fileId);
					acpt->addString("source", __X("base_storage"));
					acpt->addString("category", __X("dbnote"));
					acpt->endStruct();
				}
			});
		}
	}
}
/**
 * Export all attachment file ids
 */
WebInt ExecDetail::ExecAttachments()
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	_Workbook* wb = m_workbook->GetCoreWorkbook();

	IBook* pBook = wb->GetBook();
	IWorkbookObj* wbObj = wb->GetWoObject();
    PCWSTR censorShareId = _appcore_GainDbSheetContext()->GetCensorShareId();
    auto pVisibilityChecker = ShareLinkContentVisibilityChecker::CreateInstance(pBook, censorShareId);
	ks_stdptr<ICustomStorageManager> spCustomStorMgr;
	pBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
	if (!spCustomStorMgr)
		return WO_FAIL;

    std::vector<ks_wstring> attachmentImageIds;
	std::vector<ks_wstring> attachmentVideoIds;
    std::vector<ks_wstring> cloudSpaceImageUrls;
    if (!m_workbook->GetBMP()->bDbSheet)
    {
		if(!pVisibilityChecker)	 // 非分享链接送审情况下，as/et导出全部工作表中的图片
		{
			wo::ExportAllAttachment exporter(wb, ctx.get());
			exporter.CollectAllAttachment();
			attachmentImageIds = exporter.getAttachmentImageIds();
			attachmentVideoIds = exporter.getAttachmentVideoIds();
			cloudSpaceImageUrls = exporter.getCloudSpaceImageUrls();
		}
		else					// 分享工作表送审情况下, as/et导出分享工作表中的图片
		{
			wo::ExportSharedLinkAllAttachment exporter(wb, ctx.get(), censorShareId);
			exporter.CollectAllAttachment();
			attachmentImageIds = exporter.getAttachmentImageIds();
			attachmentVideoIds = exporter.getAttachmentVideoIds();
			cloudSpaceImageUrls = exporter.getCloudSpaceImageUrls();
		}
    }

    binary_wo::BinWriter ww;
	{
		KSerialWrapBinWriter acpt(ww, ctx.get());
		GlobalSharedStringUnorderedSet fileIdSet;

        {
            sa::Leave cloudspaceLeave = sa::enterArray(&acpt, "cloudspace");
            for (const auto& url : cloudSpaceImageUrls)
            {
                sa::Leave anonymousLeave = sa::enterStruct(&acpt, nullptr);
                acpt.addString("url", url.c_str());
                acpt.addString("source", __X("drive"));
            }
        }

		{
			sa::Leave cloudspaceLeave = sa::enterArray(&acpt, "videoAttachmentIds");
			for (const auto& attachmentId : attachmentVideoIds)
			{
				sa::Leave anonymousLeave = sa::enterStruct(&acpt, nullptr);
				acpt.addString("fileId", attachmentId.c_str());
				acpt.addString("source", __X("base_storage"));
				acpt.addString("category", __X("attachment_image"));
			}
		}

		sa::Leave attachmentsLeave = sa::enterArray(&acpt, "attachments");

		for (INT32 cnt = wbObj->getSheetCount(), i = 0; i < cnt; ++i)
		{
			ks_stdptr<ISheet> spSheet;
			pBook->GetSheet(i, &spSheet);
            UINT sheetId = spSheet->GetStId();
            if (pVisibilityChecker && !pVisibilityChecker->IsSheetVisible(sheetId))
                continue;
			// 说明页
			if (spSheet->IsFpSheet())
			{
				ks_stdptr<IFPSheetData> spFpSheetData;
				spSheet->GetExtDataItem(edSheetFpData, (IUnknown**)&spFpSheetData);
				if (!spFpSheetData)
					continue;

				acpt.beginStruct();
				acpt.addString("fileId", spFpSheetData->GetContentId());
				acpt.addString("source", __X("base_storage"));
                acpt.addString("category", __X("fpsheet"));
				acpt.endStruct();
				continue;
			}
			getDbSheetAttachmentIds(spSheet, pVisibilityChecker.get(), &acpt, fileIdSet);
			getDbDashboardAttachmentIds(spSheet, &acpt, spCustomStorMgr);
		}
        for (const auto& attachmentId : attachmentImageIds)
        {
            sa::Leave anonymousLeave = sa::enterStruct(&acpt, nullptr);
            acpt.addString("fileId", attachmentId.c_str());
            acpt.addString("source", __X("base_storage"));
			acpt.addString("category", __X("attachment_image"));
        }
	}

	FillOut(ww, msgType_Attachments);
	return WO_OK;
}

WebInt ExecDetail::ExecSheetsAttachments()
{
	// 供服务端批量查询附件 id，目前仅支持 dbsheet
	if (!m_root.has("sheetIdVec"))
		return WO_FAIL;
	if (!m_workbook)
		return WO_FAIL;
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	_Workbook* wb = m_workbook->GetCoreWorkbook();
	IBook* pBook = wb->GetBook();
	IKWorksheets* pWorksheets = wb->GetWorksheets();
	PCWSTR censorShareId = _appcore_GainDbSheetContext()->GetCensorShareId();
	auto pVisibilityChecker = ShareLinkContentVisibilityChecker::CreateInstance(pBook, censorShareId);
	VarObj stIdVec = m_root.get("sheetIdVec");
	int stCnt = stIdVec.arrayLength_s();
	std::vector<ISheet*> sheetVec;
	sheetVec.reserve(stCnt);
	std::map<UINT, ISheet*> srcSheetMap;
	for (int i = 0; i < stCnt; ++i)
	{
		UINT stId = stIdVec.item_uint32(i);
		IDX sheetIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(stId, &sheetIdx);
		if (sheetIdx == INVALIDIDX)
			return WO_FAIL;
		ISheet* pSheet = pWorksheets->GetSheetItem(sheetIdx)->GetSheet();
		if (!pSheet->IsDbSheet() && !pSheet->IsDbDashBoardSheet())
			return WO_FAIL;
		
		srcSheetMap.emplace(pSheet->GetStId(), pSheet);
		GetDashboardViewSrcSheet(pSheet, srcSheetMap);
	}

	for (auto it = srcSheetMap.cbegin(); it != srcSheetMap.cend(); ++it)
		sheetVec.emplace_back((*it).second);

	ks_stdptr<ICustomStorageManager> spCustomStorMgr;
	pBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
	if (!spCustomStorMgr)
		return WO_FAIL;

	binary_wo::BinWriter ww;
	KSerialWrapBinWriter acpt(ww, ctx.get());
	GlobalSharedStringUnorderedSet fileIdSet;
	{
		sa::Leave attachmentsLeave = sa::enterArray(&acpt, "attachments");
		for (auto pSheet : sheetVec)
		{
			getDbDashboardAttachmentIds(pSheet, &acpt, spCustomStorMgr);
			getDbSheetAttachmentIds(pSheet, pVisibilityChecker.get(), &acpt, fileIdSet);
		}
	}

	FillOut(ww, msgType_Attachments);
	return WO_OK;
}

WebInt ExecDetail::ExecSubscriptionInfo()
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	BinWriter ww;
	KSerialWrapBinWriter acpt(ww, ctx.get());
	IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
	IBookStake *pBookStake = pBook->GetWoStake();
	ICbSubscribeOp *pSubOp = pBookStake->GetCbSubscribeOp();
	pSubOp->SerialiseCbRefers(&acpt, "Refers");
	pSubOp->SerialiseCbSubscriptions(&acpt, "Subscriptions");
	FillOut(ww, msgType_Query);
	return WO_OK;
}

WebInt ExecDetail::ExecMemoStat()
{
	if (m_workbook == nullptr || m_workbook->GetCoreWorkbook() == nullptr)
		return WO_FAIL;

	ks_stdptr<_Application> ptrApp = m_workbook->GetCoreApp();

	binary_wo::BinWriter ww;
	ww.addStringField(__X("et"), "product");
	ww.addFloat64Field(MemoStatProcMemory(), "procMemory");
	
	IKUserConns* userConns = ptrApp->getUserConns();
	if (userConns != nullptr)
	{
		ww.addFloat64Field(userConns->count(), "userCount");
	}
	ww.addFloat64Field(
		m_workbook->getVersionMgr()->getTaskVersionCount(), "versionCount");

	_Workbook* wb = m_workbook->GetCoreWorkbook();
	IKWorksheets* wss = wb->GetWorksheets();

	IBook* bk = wb->GetBook();
	ks_stdptr<IKMediaManage> mediaMgr;
	VS(oplGetBookMediaMgr(bk, &mediaMgr));
	MemoStatMedia(mediaMgr.get(), ww);

	UINT64 cntShape = 0;
	{
		binary_wo::ArrayWriter sheetsShape(ww, "sheetShapeTree");
		for (int i = 0, cnt = wss->GetSheetCount(); i < cnt; ++i)
		{
			IKWorksheet* ws = wss->GetSheetItem(i);
			if (ws == nullptr) continue;
			ks_castptr<EtShapeTree> etShapeTree = ws->GetDrawingCanvas();
			MemoStatShapeTree(etShapeTree.get(), ww, &cntShape);
		}
	}
	ww.addFloat64Field(cntShape, "totalShapeCount");
	ww.addFloat64Field(MemoStat_chart(), "totalChartShapeCount");
	ww.addStringField(
		m_workbook->GetMemoStat()->QueryMallocInfo().c_str(), "mallocInfo");

	MemoStat_bookSheet(ww, wss);
	MemoStat_oleDoc(ww);
	MemoStat_transactEnv(ww);
	MemoStat_PivotCache(ww);
	m_workbook->GetMemoStat()->SerializeProxyMemo(ww);
	
	FillOut(ww, msgType_MemoStat);
	
	//强制记录信息
	m_workbook->GetMemoStat()->SetEvaluateForce();
	return WO_OK;
}

void ExecDetail::MemoStat_bookSheet(binary_wo::BinWriter& ww, IKWorksheets* wss)
{
	auto func = [&ww](const MemoStatSheet& info) {
		ww.addFloat64Field(info.cellBlockCount, "cellBlockCount");
		ww.addFloat64Field(info.cellBlockMemoSize, "cellBlockMemoSize");
		ww.addFloat64Field(info.entireRowCellCount, "entireRowCount");
	};

	int cntSheet = wss->GetSheetCount();
	MemoStatSheet statSheet;
	{
		binary_wo::ArrayWriter gridArray(ww, "girdArray");
		for (int i = 0; i < cntSheet; ++i)
		{
			IKWorksheet* ws = wss->GetSheetItem(i);
			ISheetStake* ss = (ws == nullptr) ? nullptr : ws->GetSheet()->GetWoStake();
			if (ss == nullptr) continue;

			MemoStatSheet tmp = ss->procMemoStat();
			binary_wo::StructWriter sw(ww, nullptr);
			func(tmp);

			statSheet.cellBlockCount += tmp.cellBlockCount;
			statSheet.cellBlockMemoSize += tmp.cellBlockMemoSize;
			statSheet.entireRowCellCount += tmp.entireRowCellCount;
		}
	}

	func(statSheet);

	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	ASSERT(bk != nullptr);

	MemoStatBook statBook = bk->GetWoStake()->procStatInfo();
	ww.addFloat64Field(statBook.cellNodeCount, "cellNodeCount");
	ww.addFloat64Field(statBook.cellNodeMemoSize, "cellNodeMemoSize");
	ww.addFloat64Field(statBook.singleFmlaCount, "singleFormulaCount");
	ww.addFloat64Field(statBook.singleFmlaMemoSize, "singleFormulaMemoSize");
	ww.addFloat64Field(statBook.shrFmlaCount, "shareFormulaCount");
	ww.addFloat64Field(statBook.shrFmlaMemoSize, "shareFormulaMemoSize");
	ww.addFloat64Field(statBook.arrFmlaCount, "arraytFormulaCount");
	ww.addFloat64Field(statBook.arrFmlaMemoSize, "arraytFormulaMemoSize");
	ww.addFloat64Field(statBook.nameCount, "nameCount");
	ww.addFloat64Field(statBook.condFmtCount, "conditionFormatCount");
	ww.addFloat64Field(statBook.supBookCachedSize, "supBookCachedSize");
	ww.addFloat64Field(statBook.openCalcDuration, "openCalcTiem");
	ww.addFloat64Field(statBook.condFmtCellsTravelled, "cellsConditionFormatTravelledCount");
	ww.addInt32Field(m_workbook->GetCoreWorkbook()->GetFileFormat(), "fileFormat");
	m_workbook->GetMemoStat()->SerializeMarkInfo(ww);
	
}

void ExecDetail::MemoStat_oleDoc(binary_wo::BinWriter& ww)
{
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	UINT64 memoOle = 0;
	ks_stdptr<IUnknown> unkOle;
	ks_stdptr<IKOleDocument> oleDoc;
	if (SUCCEEDED(bk->GetExtDataItem(edBookOleDocument, &unkOle)))
	{
		unkOle->QueryInterface(IID_IKOleDocument, (void**)&oleDoc);
	}

	DocMemoStat* bookMemoStat = m_workbook->GetMemoStat();
	ASSERT(bookMemoStat != nullptr);

	ULONG cntOleDoc = (oleDoc.get() == nullptr) ? 0 : oleDoc->GetOleObjCount();
	ww.addFloat64Field(bookMemoStat->GetOleMemory(), "oleStreamMemoSize");
	ww.addFloat64Field(cntOleDoc, "oleDocumentCount");
}

void ExecDetail::MemoStat_PivotCache(binary_wo::BinWriter& ww)
{
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<pivot_core::IPivotCacheHost> spCacheHost;
	bk->GetExtDataItem(edBookPivotCachesHost, (IUnknown**)&spCacheHost);

	pivot_core::IPivotCaches* pivotCaches =
		(spCacheHost.get() == nullptr) ? nullptr : spCacheHost->GetPivotCaches();
	UINT cntCaches = (pivotCaches == nullptr) ? 0 : pivotCaches->Count();
	UINT64 cntTable = 0;
	UINT64 szPivotCache = 0;
	for (UINT i = 0; i < cntCaches; ++i)
	{
		pivot_core::IPivotCache* ci = pivotCaches->Item(i);
		if (ci == nullptr) continue;

		cntTable += ci->GetPivotTableCount();
		szPivotCache += ci->ProcMemoStat();

	}
	ww.addFloat64Field(cntTable, "pivotTableCount");
	ww.addFloat64Field(szPivotCache, "pivotCacheMemoSize");
}

void ExecDetail::MemoStat_transactEnv(binary_wo::BinWriter& ww)
{
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();

	ww.addFloat64Field(AlgEvaluateSizeObj(), "shapePoolSize");
	ww.addFloat64Field(AlgEvaluateSize(), "algPoolSize");
	
	UINT64 szMemo = 0, cntUnk = 0, cntAtom = 0;
	ASSERT(bk->GetRtsRepository() != nullptr);
	UINT cntTransStep = bk->GetRtsRepository(
		)->ProcMemoStatInfo(&szMemo, &cntUnk, &cntAtom);
	ww.addFloat64Field(szMemo, "transactMemory");
	ww.addFloat64Field(cntTransStep, "transactStepCount");
	ww.addFloat64Field(cntUnk, "transactUnknowCount");
	ww.addFloat64Field(cntAtom, "transactAtomCount");
}

UINT64 ExecDetail::MemoStat_chart()
{
	UINT64 res = 0;
	ks_stdptr<IKWorksheets> wks = m_workbook->GetCoreWorkbook()->GetWorksheets();
	for (int iSht = 0, nSht = wks->GetSheetCount(); iSht < nSht; ++iSht)
	{
		ks_stdptr<IKWorksheet> ws = wks->GetSheetItem(iSht);
		if (ws.get() == nullptr) continue;

		ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
		oplGetSheetOplData(ws->GetSheet(), &spCanvas);
		if (!spCanvas)
			continue;
		ks_castptr<EtShapeTree> pshapeTree = spCanvas;

		res += MemoStat_chartShape(pshapeTree);
	}

	return res;
}

UINT64 ExecDetail::MemoStat_chartShape(const drawing::GroupShape* gs)
{
	UINT64 res = 0;
	for (int i = 0, n = gs->childCount(); i < n; ++i)
	{
		const drawing::AbstractShape *shape = gs->childAt(i);
		if (shape != nullptr && shape->hasChart())
		{
			++res;
		}
		else if (shape->isGroupShape())
		{
			res += MemoStat_chartShape(
				static_cast<const drawing::GroupShape*>(shape));
		}
	}

	return res;
}

WebInt ExecDetail::ExecBlock()
{
	ASSERT(!m_workbook->IsExportFmla());

	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	_Workbook* wb = m_workbook->GetCoreWorkbook();
	IBook* bk = wb->GetBook();

	binary_wo::VarObj blks = m_root.get("blocks");
	binary_wo::BinWriter ww;
	CustomWebAcptHelp acptHlp(m_workbook, &ww, ctx.get());
	acptHlp.begin();
	{
		ISerialAcceptor* acpt = acptHlp.getAcpt();

		sa::Leave blocksLeave = sa::enterArray(acpt, "blockPointsFill");
		for (int32 i = 0, len = blks.arrayLength(); i < len; ++i)
		{
			binary_wo::VarObj blk = blks.at(i);
			BlockPoint bp(blk.field_web_id("objSheet"),
				blk.field_int32("row"), blk.field_int32("col"));

			ASSERT(bp.row % BLK_ROWS_COUNT == 0 && bp.col % BLK_COLS_COUNT == 0);

			IDX idxSht = ctx->getSheetIndex(bp.objSheet);
			ks_stdptr<ISheet> sht;
			VS(wb->GetBook()->GetSheet(idxSht, &sht));

			sa::Leave itemLeave = sa::enterStruct(acpt, nullptr);
			{
				acpt->addObject("objSheet", ctx->getSheetMain(idxSht));
			}
			{
				sa::Leave areaLeave = sa::enterArray(acpt, "areas");
				sa::Leave structLeave = sa::enterStruct(acpt, nullptr);
				const RECT& rc = bp.GetRect();
				sa::addRect(acpt, rc);
				WOVW(acpt->addKey("cells"));
				sht->GetWoStake()->exportCells(rc, acpt);
				WOVW(acpt->addKey("hyperlinksBlock"));
				_SerialHyperlink(idxSht, rc, acpt);
				WOVW(acpt->addKey("sparklinesBlock"));
				_SerialSparklines(idxSht, rc, acpt);
				WOVW(acpt->addKey("cfBlock"));
				_SerialCellCondBlock(idxSht, rc, acpt);
				WOVW(acpt->addKey("cellProtectionsBlock"));
				_SerialCellProtection(idxSht, rc, acpt);
			}
		}
	}

	acptHlp.end();
	// blockPointsDelta
	{
		KSerialWrapBinWriter acpt(ww, ctx.get());
		sa::Leave blkDeltaLeave = sa::enterStruct(&acpt, "blockPointsDelta");

		sa::Leave addLeave = sa::enterArray(&acpt, "add");
		for (int32 i = 0, len = blks.arrayLength(); i < len; ++i)
		{
			binary_wo::VarObj blk = blks.at(i);
			BlockPoint bp(blk.field_web_id("objSheet"),
				blk.field_int32("row"), blk.field_int32("col"));

			IDX idxSht = ctx->getSheetIndex(bp.objSheet);

			sa::Leave itemLeave = sa::enterStruct(&acpt, nullptr);
			{
				acpt.addObject("objSheet", ctx->getSheetMain(idxSht));
				acpt.addInt32("row", bp.row);
				acpt.addInt32("col", bp.col);
			}
		}
	}

	FillOut(ww, msgType_Block);
	
	return WO_OK;
}

bool ExecDetail::DoReExec()
{
	WebInt prevSeq = m_curUserConn->getCmdSeq();
	if (m_seq != prevSeq || m_seq <= 0)
		return false; // 不是已经执行过的命令

	
	m_spSignalSlice.clear();
	m_curUserConn->getCmdSeq(&m_spSignalSlice, &m_signalSliceType);
	if(m_spSignalSlice == NULL || m_signalSliceType == NULL)
		return false; // 没有旧请求的缓存数据


	// 断网重联返回上次的数据。
	if (gs_callback->signal)
		gs_callback->signal(m_connID, m_signalSliceType, m_spSignalSlice->Get());

	return true;
}

bool ExecDetail::FetchUserProtectPermsChanged(KEtRevisionContext* ctx)
{
 	if (!ctx->isExecDirect())
	{
		IKETUserConn* pUserConn = GetCurUser();
		if (pUserConn->getCorePermissionChanged())
		{
			return true;
		}
	}

	return false;
}

WebInt ExecDetail::ExecQuery()
{
	std::unique_ptr<wo::util::SlowCallTimeStat> cmStepTime;
	ResetSlowCallCM(cmStepTime, m_workbook, "ExecQuery_BeforeExec", KU32MetricItem::kBeforeExecTime);
	
	ApiCallTimeStat apiCallTime(m_workbook);
	if (m_bHttpCalling)
	{
		// server api没有query init, 没有时机触发prepare user
		WebInt res = verifyAndPrepareUserConn();
		if (res < 0)
		{
			WOLOG_ERROR << "Query prepare user failed";
			return res;
		}
	}
	if (!m_bHttpCalling)
		m_workbook->incCmdCount();

	DocMemoStat::MarkScope markScope(m_workbook->GetMemoStat(),
		__X("query"), false, m_root.get_s("extendQueryCmds"));

	SCOPE_AutoResume<bool> scopeBool(m_bQueryOrCmd, true);
	m_workbook->coreMetric().onBeginQuery();
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	InitContext(ctx);
	ctx.setState(CtxState::Query);


    // 只读筛选,需要提前设置标记,不然后续序列化可能拿不到准确数据
	if(hasReadonlyFilter())
	{
		ctx.getFilterContext()->SetIsFilterReadOnly(true);
	}

	IKETUserConn *pEtUserConn = GetCurUser();
	m_workbook->collectInfo().checkIllegalReuse(pEtUserConn, m_bHttpCalling);
	UserSyncScope userSyncScope(pEtUserConn);
	// 临时代码
	WOLOG_INFO << "[" << m_connID << "]" << " (query) server version: " << m_workbook->getVersionMgr()->getCurTaskVersionID()
		<< " bHttpCalling: " << m_bHttpCalling
		<< " svrFrom: " << util::toSvrConnFromStr(m_extArgs.connFrom)
		<< " svrScene: " << util::toSvrConnSceneStr(m_extArgs.connScene)
		<< " svrLife: " << util::toSvrConnLifeStr(m_extArgs.connLife);
	if (m_root.has("taskVer") && m_workbook->getVersionMgr()->getCurTaskVersionID() < m_root.field_int32("taskVer"))
	{
		WOLOG_ERROR << "[" << m_connID << "]"
			<< " client version" << "(" << m_root.field_int32("taskVer") << ")"
			<< " newer than server" << "(" << m_workbook->getVersionMgr()->getCurTaskVersionID() << ")";
		if (gs_procType != WebProcTypeChild)
			return WO_FAIL;
	}

	ResetSlowCallCM(cmStepTime, m_workbook, "ExecQuery_Exec", KU32MetricItem::kExecTime);
	binary_wo::BinWriter bw;
	if(m_bHttpCalling)
	{
		m_workbook->GetQueryExecutor()->Query(m_root.get_s("extendQueryCmds"), &ctx, bw);

		binary_wo::BinWriter *pResponse = ctx.getHttpResponse();
		FillOut(*pResponse, msgType_Query);
		return WO_OK;

	}
	else
	{
		if (FetchUserProtectPermsChanged(&ctx))
			return ExecInitImpl(false);

		if(DoReExec())
			return WO_OK;
	
		if (m_root.has("async") && m_root.field_bool("async"))
		{
			if (m_root.has("extendQueryCmds"))
				m_workbook->GetQueryExecutor()->Query(m_root.get_s("extendQueryCmds"), &ctx, bw);
		}
		else
		{
			//表示query是否需要把内核的版本和前端的基线版本之间的差异给序列化回去
			bool bSerializeDelta = true;//默认为true
			if (m_root.has("serializeDelta"))
			{
				bSerializeDelta = m_root.field_bool("serializeDelta");
			}
			SyncSignalSize();
			
			WebInt ret = WO_OK;
			if (ctx.IsUserHasSerialObjs())
				ret = bSerializeDelta ? m_workbook->serializeVersions(&ctx, &bw) : WO_OK;
			else
				ret = WO_FAIL;
			bool isReSerialize = false;
			if (ret != WO_OK || (isReSerialize = ctx.isReSerialize())) 
			{
				if (!ctx.IsUserHasSerialObjs())
				{
					WoFileIncludeCollector collector(m_workbook, __X("behaviour_query_reinit_no_serail_obj"), 1);
					collector.addCmdName(ctx.getUser()->connID()).collect();
				}
				if (isReSerialize)
				{
					WOLOG_INFO << "ctx.isReSerialize(): " << isReSerialize;
					wo::util::CollectInfo(m_workbook, __X("isReSerialize_true"), 0);
				}
				if (m_root.has("extendQueryCmds"))
					WOLOG_ERROR << "unexpected, query cmd is skipped";
				//若无法把前端的基线版本和当前内核版本的之间的数据差异给增量序列化回去，则进行init重新初始化，拉取同步到最新的内核版本
				//注意走到这里时，后面的query命令是不会被执行到的！！！
				return ExecInitImpl(false);
			}
		}

	}

	ResetSlowCallCM(cmStepTime, m_workbook, "ExecQuery_AfterExec", KU32MetricItem::kAfterExecTime);
	KBinWriterSizeU32Mc mcBinSize(&m_workbook->coreMetric(), &bw, KU32MetricItem::kAfterExecBytes);
	
	CustomWebAcptHelp acptHlp(m_workbook, &bw, &ctx);
	acptHlp.begin();
	m_workbook->serializeSheets(&ctx, m_root, acptHlp.getAcpt());
	m_workbook->serializeXfs(&ctx, m_root, acptHlp.getAcpt());
	m_workbook->serializeCmtContainerBL(&ctx, m_root, acptHlp.getAcpt());
    m_workbook->serializeUserOrganizeInfo(&ctx, m_root, acptHlp.getAcpt());
    m_workbook->serializeVersionsModifyRanges(&ctx, m_root, acptHlp.getAcpt());
	BP_Proc(ctx, bw, acptHlp.getAcpt());
	acptHlp.end();

	SerialPageSetupData(ctx, bw);
	SerialCbReferAutoUpdateState(ctx, bw);

	AddExtState(bw, ctx);
	if (m_extArgs.commitVersion == WO_QUERY_PLAY_ID) {
		FillRevisionSelection(ctx, bw);
	}

	FillOut(bw, msgType_Query);
	InitUrlSupBooks(&ctx);

	return WO_OK;
}

WebInt ExecDetail::ExecDelayCopy(const WebMimeData *pMimeData)
{
	if (NULL == pMimeData || pMimeData->path == NULL)
		return WO_FAIL;
	
	std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	ks_stdptr<IETPersist> spPersist;
	spPersist = m_workbook->GetCoreApp()->GetAppPersist()->GetPersist();
	if(spPersist == NULL)
		return WO_FAIL;

	IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
	KComVariant varCopyType(static_cast<int>(et_event_tracking::COPY_TYPE::DELAY_COPY));
	pBook->LeakWorkspace()->GetEventTracking()->GetCollectInfo()->CollectBookInfoOnce(et_event_tracking::BOOK_FIELD_NAME::COPY_TYPE, varCopyType);
	
	binary_wo::VarObj param;
	BOOL bVaildCmd;
	bool isShapeCopy = false;
	bool isDbsheetCopy = false;
	bool isMultiRangeCopy = false;
	et_sptr<util::AutoCmdTraceId> etSpAutoCmdTraceId;
	if (m_root.has("extendQueryCmds"))
	{
		binary_wo::VarObj cmds = m_root.get_s("extendQueryCmds");
		int nCount = cmds.arrayLength_s();
		if (nCount <= 0)
			return WO_FAIL;

		for (int i = 0; i < nCount; ++i)
		{
			binary_wo::VarObj vCmd = cmds.at_s(i);
			if (!etSpAutoCmdTraceId)
				etSpAutoCmdTraceId.reset(new util::AutoCmdTraceId(&ctx, vCmd));

			WebStr name = vCmd.field_str("name");
			if (xstrcmp(__X("range.copy"), name) == 0)
			{
				param = vCmd.get_s("param");
				bVaildCmd = TRUE;
				break;
			} 
			else if(xstrcmp(__X("range.multiCopy"), name) == 0)
			{
				param = vCmd.get_s("param");
				bVaildCmd = TRUE;
				isMultiRangeCopy = true;
				break;
			}
			else if (xstrcmp(__X("shape.copy"), name) == 0)
			{
				isShapeCopy = true;
				param = vCmd.get_s("param");
				bVaildCmd = TRUE;
				break;
			}
			else if (xstrcmp(__X("dbSheet.rangeCopy"), name) == 0) 
			{
				isDbsheetCopy = true;
				param = vCmd.get_s("param");
				bVaildCmd = TRUE;
				break;
			}
		}	
	}

	if (!bVaildCmd)
		return WO_FAIL;

	if (isShapeCopy) {
		return _ExecDelayShapeCopy(pMimeData, param);
	}

	if (isDbsheetCopy) {
		return _ExecDelayDbsheetCopy(pMimeData, param);
	}

	IDX sheetIdx = GetSheetIdx(m_workbook->GetCoreWorkbook()->GetBook(), param);

	ks_stdptr<IRangeInfo> host;
	if(isMultiRangeCopy)
	{	
		std::vector<RANGE> rgVec;
		ReadRangesInl(m_workbook->GetBMP(), sheetIdx, param, rgVec, "srcRanges");
		if (rgVec.empty())
			return E_FAIL;
		ks_stdptr<Range> spRange = m_workbook->CreateRangeObj(rgVec);
		if (spRange == nullptr)
			return E_FAIL;
		host = spRange;

		for (UINT i = 0; i < rgVec.size(); ++i)
		{
			if (ctx.getProtectionCtx()->isRangeHasHidden(rgVec[i]))
			{
				return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
			}
		}
	}
	else
	{
		RANGE rg = wo::ReadRangeInl(m_workbook->GetBMP(), sheetIdx, param);
		if (!rg.IsValid())
			return WO_FAIL;
		host = m_workbook->CreateRangeObj(rg);
		if (host == nullptr)
		{
			WOLOG_INFO << "[delay_copy] " << " invalid range(host): " << rg;
			return WO_FAIL;
		}

		HRESULT hr = ctx.getProtectionCtx()->checkProtectionContent(rg);
		if (S_OK != hr)
		{
			return hr;
		}

	}
	
	ks_stdptr<IAppCoreRange> spCoreRange;
	host->GetAppCoreRange(&spCoreRange);

	if (spCoreRange == nullptr)
		return WO_FAIL;
	HRESULT hr = WO_FAIL;

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);

	ks_stdptr<IKAutoFilters> spFilters = spSheet->GetAutoFilters();
	bool hasUserFilterOn = false;
	if (spFilters)
	{
		PCWSTR userId = ctx.getUser()->userID();
		ks_stdptr<IKAutoFilter> spFilter = spFilters->GetFilter(userId);
		if (spFilter)
		{
			ctx.getFilterContext()->SetIsFilterReadOnly(TRUE);
			hasUserFilterOn = true;
		}
	}

	range_helper::ranges rgs;
	// 判断一下, 如果在筛选模式下, 不要把隐藏的也给选上
	if (hasUserFilterOn || spCoreRange->IsRangeInFilterMode())
		spCoreRange->GetFilteredIRanges(FALSE, &rgs);
	else
		host->GetIRanges(&rgs, TRUE);

	if (param.has("cloudPathPrefix"))
	{
		ks_wstring pathPrefix = param.field_str("cloudPathPrefix");
		ks_wstring pathPostfix;
		if (param.has("cloudPathPostfix"))
			pathPostfix = param.field_str("cloudPathPostfix");
		ctx.InitCopyShapeCtx(pathPrefix, pathPostfix);

		ks_stdptr<IKRanges> spRanges;
		host->GetIRanges(&spRanges);
		if(!spRanges)
			return E_FAIL;
		binary_wo::BinWriter binWriter;
		KSerialWrapBinWriter acpt(binWriter, &ctx);

		IKWorksheet *pWs = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		ks_stdptr<IKShapeRange> ptrShapeRange;
		if (S_OK == GetRangeCoverShapes(spSheet, pWs->GetDrawingCanvas(), spRanges, &ptrShapeRange))
			util::prepareCoverShapes(sheetIdx, ptrShapeRange, &ctx, &acpt);
	}

	COPYINFO	info = {0};
	IDX sheetid = INVALIDIDX;
	info.grbits.fCopy = 1;
	info.grbits.fCache  = 1;
	info.grbits.fWithObjs = 1;

	spSheet->GetWoStake()->setCopyHtmlBreakSize(15*1024*1024);
	ctx.clearAttachmentIds();
	STGMEDIUM medium = {0};
	QString format = pMimeData->mimeType != NULL && *(pMimeData->mimeType) != '\0' ? QString::fromUtf8(pMimeData->mimeType): QString("text/html");
	if (format == kso_cb_et_format_coop)
	{
		info.grbits.fCopyFmlaIsRc = 1;
	}
	hr = spPersist->WoCopy(m_workbook->GetCoreWorkbook()->GetBook(), sheetIdx, rgs, &info, format, &medium);
	if (FAILED(hr))
		return WO_FAIL;

	if (param.has("cloudPathPrefix"))
	{
		ctx.ResetCopyShapeCtx(nullptr);
	}

	//直接覆盖
	ks_stdptr<IStream> spStrm; 
	spStrm.attach(medium.pstm);
	KsoDataStream* dataStream = ks_castptr<KsoDataStream>(medium.pstm);
	if(medium.tymed != TYMED_ISTREAM || dataStream == NULL)
		return WO_FAIL;

	QByteArray arr = dataStream->getByteArray();

	FILE *clipBoardFile = fopen(pMimeData->path, "w");
	if (NULL == clipBoardFile)
		return WO_FAIL;

	if ("text/plain" == format)
	{
		// 复制出来的文本数据要带上 bom 方便粘贴方使用。目前WPP和WPS文本的延迟拷贝用的是 utf-8
		QByteArray utf8Txt = QString::fromUtf16(reinterpret_cast<PCWSTR>(arr.data())).toUtf8();
		fwrite("\xEF\xBB\xBF", sizeof(char), 3, clipBoardFile);
		fwrite(utf8Txt.data(), sizeof(char), utf8Txt.size(), clipBoardFile);
	}
	else 
	{
		fwrite(arr.data(), sizeof(char), arr.size(), clipBoardFile);
	}
	fclose(clipBoardFile);
	
	unsigned int timeElapse = (std::chrono::steady_clock::now() - begin) / std::chrono::milliseconds(1);
	if (timeElapse > wo::util::kDefCmdTimeThreshold)
	{
		QString strDelayCopyExecInfo = QString("behaviour_delaycopy_%1_%2").arg(format).arg(timeElapse);
		wo::util::CollectInfo(m_workbook, krt::utf16(strDelayCopyExecInfo), 1);
	}
	WOLOG_INFO << "[delay_copy] format: " << format << ", elapsed " << timeElapse << " ms"; 
	return WO_OK;
}

void ExecDetail::GetDashboardViewSrcSheet(ISheet* pSheet, std::map<UINT, ISheet*>& srcSheetMap)
{
	if (!pSheet->IsDbDashBoardSheet())
		return;

	_Workbook* pWorkbook = m_workbook->GetCoreWorkbook();
	IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetWebExtensionMgr();
	if (!pWebExtensionMgr)
		return;

	UINT webExtensionCount = 0;
	pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
	for (UINT j = 0; j < webExtensionCount; ++j)
	{
		ks_stdptr<IKWebExtension> spWebExtension;
		HRESULT hr = pWebExtensionMgr->GetWebExtension(pSheet, j, &spWebExtension);
		if (FAILED(hr))
			continue;
		if (spWebExtension->GetWebShapeType() != WET_DbView)
			continue;

		ks_stdptr<IEtWebExtension_View> spWebExtView = spWebExtension;
		UINT srcSheetId = spWebExtView->GetSheetId();
		if (srcSheetId == 0)
			continue;

		IDX srcSheetIdx = INVALIDIDX;
		pWorkbook->GetBook()->STSheetToRTSheet(srcSheetId, &srcSheetIdx);
		if (srcSheetIdx == INVALIDIDX)
			continue;

		ISheet* pSrcSheet = pWorkbook->GetWorksheets()->GetSheetItem(srcSheetIdx)->GetSheet();
		if (srcSheetMap.find(srcSheetId) == srcSheetMap.end())
			srcSheetMap.emplace(srcSheetId, pSrcSheet);
	}
}

void ExecDetail::WriteHookInValidResult(PCWSTR hookId, int code, PCWSTR message, binary_wo::BinWriter &res)
{
    res.beginStruct();
    res.addStringField(hookId, "hook_id");
    res.addInt16Field(code, "code");
    res.addStringField(message, "msg");
    res.endStruct();
    WOLOG_ERROR << "[WriteHookInValidResult]" << message;
}

void ExecDetail::WriteHookInValidResult(PCWSTR hookId, PCWSTR uuid, int code, PCWSTR message, binary_wo::BinWriter &res)
{
    res.beginStruct();
    res.addStringField(hookId, "hook_id");
    res.addStringField(uuid, "uuid");
    res.addInt16Field(code, "code");
    res.addStringField(message, "msg");
    res.endStruct();
    WOLOG_ERROR << "[WriteHookInValidResult]" << message;
}

void ExecDetail::ResetTypeBreaks()
{
	if (IWoCallBack* pWoCb = _kso_GetWoCallBack())
	{
		pWoCb->setTypeBreak(WoBreakType::BreakPaste, FALSE);
		pWoCb->setTypeBreak(WoBreakType::BreakErrorRepair, FALSE);
	}
}

WebInt ExecDetail::_ExecRangeImgCopy(const binary_wo::VarObj &param, binary_wo::BinWriter& binWriter)
{
	if (!param.has("rgs")) return WO_FAIL; 
	binary_wo::VarObj rgs = param.get_s("rgs");
	int32 l = rgs.arrayLength_s();
	HRESULT hr = WO_FAIL;
	bool supportDir = param.has("fileDir");
	binWriter.addKey("imgResults");
	binWriter.beginArray();
	if (m_workbook->isBookProtectedWithHiddenProperty())
	{
		WriteHookInValidResult(param.field_str("hook_id"), Webhook_PermissionDeny, __X("E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE"), binWriter);
		binWriter.endArray();
		WOLOG_ERROR << "[_ExecRangeImgCopy] hidden range error ";
		return WO_FAIL;
	} 
	for (int i = 0; i < l; i++) {
		binary_wo::VarObj rgObj = rgs.at(i);
        IDX sheetStId = INVALIDIDX;
        if (rgObj.has("sheetStId")) {
            sheetStId = rgObj.field_int32("sheetStId");
        }
        if (sheetStId == INVALIDIDX) {
			WOLOG_ERROR << "Invalid sheetStId";
			WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_Invalid_Argument, __X("Invalid sheetStId"), binWriter);
			continue;
		}
		IDX sheetIdx = INVALIDIDX;
		IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
		pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
		if (sheetIdx == INVALIDIDX) {
			WOLOG_ERROR << "Invalid sheetIdx";
			WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_SheetIdx_Invalid, __X("Invalid sheetIdx"), binWriter);
			continue;
		}
        RANGE rg(pBook->GetBMP());
        rg.SetSheetFromTo(sheetIdx);
        rg.SetRowFromTo(
		    rgObj.field_int32("rowFrom"), rgObj.field_int32("rowTo"));
	    rg.SetColFromTo(
		    rgObj.field_int32("colFrom"), rgObj.field_int32("colTo"));
		if (!rg.IsValid()) {
			WOLOG_ERROR << "Invalid range";
			WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_Invalid_Argument, __X("Invalid range"), binWriter);
			continue;
		}
		std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();    

		ks_stdptr<IKWorksheet> spISheet;
		spISheet = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		RANGE usedRange(rg);
		hr = spISheet->GetUsedRange(&usedRange);
		if (FAILED(hr)) return E_FAIL;
		if (usedRange.IsValid()) {
			RANGE backup(rg);
			RANGETYPE rangeType = rg.RangeType();
			if (rangeType == rtRows || rangeType == rtSheets)
				rg.SetColTo(rg.ColTo() > usedRange.ColTo() ? usedRange.ColTo() : rg.ColTo());
			if (rangeType == rtCols || rangeType == rtSheets)
				rg.SetRowTo(rg.RowTo() > usedRange.RowTo() ? usedRange.RowTo() : rg.RowTo());
			if ((!rg.IsValid()) || (rg.IsSingleCell() && !backup.IsSingleCell()))
				rg = backup;
		}
		util::DownloadCellImgForRange(m_workbook->GetCoreWorkbook(), spISheet->GetSheet(), rg);
		
		ks_stdptr<IRangeInfo> host = m_workbook->CreateRangeObj(rg);
		ks_stdptr<Range> spRange = host;
		ks_stdptr<IKRanges> spRanges;
		host->GetIRanges(&spRanges);

		IKWorksheetView* pSheetView;
		pSheetView = spISheet->GetActiveWorksheetView();
		

		COPYAPPEARANCE copyAppearance = caScreen;
		QImage qImage;
		ks_stdptr<IETPersist> spPersist;
		spPersist = m_workbook->GetCoreApp()->GetAppPersist()->GetPersist();
		qreal dpi = 96;
		if (param.has("dpi")) {
			dpi = param.field_double("dpi");
		}
		hr = spPersist->CopyRanges(spRanges, pSheetView, qImage, copyAppearance, dpi, TRUE);
		if (FAILED(hr)) {
			WOLOG_ERROR << "[_ExecRangeImgCopy] CopyRanges error";
			WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_Fail, __X("CopyRanges error"), binWriter);
			continue;
		}

		const int clipAlertType = spPersist->GetClipAlertType();
		if (MB_CLIPEDRANGE & clipAlertType) {
			//太大了，给个提示给前端
			WOLOG_ERROR << "[_ExecRangeImgCopy] too large waring";
			WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_Range_Too_Large, __X("CopyRanges too large"), binWriter);
			spPersist->SetClipAlertType(MB_CLIPDEFAULT, true);
		}


		QByteArray byteArray;
		QBuffer buffer(&byteArray);
		buffer.open(QIODevice::WriteOnly);
		if (!qImage.save(&buffer, "PNG")) {
			WOLOG_ERROR << "[_ExecRangeImgCopy]Image save as png error";
			WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_Fail, __X("Image save as png error"), binWriter);
			continue;
		}

		WebStr path = nullptr;
		QString qPath;
		if (supportDir) {
			WebStr dir = param.field_str("fileDir");
			QDir qdir(krt::fromUtf16(dir));
			if (!qdir.exists()) {
				WOLOG_ERROR << "[_ExecRangeImgCopy]Image save error dir not exists";
				WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_Fail, __X("dir not exists"), binWriter);
				continue;
			}
			QString uuid = krt::fromUtf16(rgObj.field_str("uuid"));
			qPath = krt::fromUtf16(dir) + QDir::separator() + uuid;
		} else {
			path = param.field_str("filePath");
			qPath =  krt::fromUtf16(path);
		}
		
		QFile file(qPath);
		if (!file.open(QIODevice::WriteOnly))
		{	
			WOLOG_ERROR << "[_ExecRangeImgCopy]Image open error:" << qPath;
			WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_Fail, __X("Image open error"), binWriter);
			continue;
		}
			
		bool sucess = file.write(byteArray);
		if (!sucess)
		{
			WOLOG_ERROR << "[_ExecRangeImgCopy]Image write error:" << qPath;
			WriteHookInValidResult(param.field_str("hook_id"), rgObj.field_str("uuid"), Webhook_Fail, __X("Image write error"), binWriter);
			file.close();
			continue;
		}
		file.close();

		WOLOG_INFO << "[_ExecRangeImgCopy] sucess: " << krt::utf16(qPath);
	}
	binWriter.endArray();
	return hr;
}


WebInt ExecDetail::_ExecShapeImgCopy(const binary_wo::VarObj &param, binary_wo::BinWriter& binWriter)
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	QByteArray sha1, picData;

	if (!param.has("chartUUIDs")) return WO_FAIL;
	binary_wo::VarObj chartUUIDs = param.get_s("chartUUIDs");
	int l =  chartUUIDs.arrayLength_s();
	HRESULT hr = WO_FAIL;
	bool supportDir = param.has("fileDir");
	binWriter.addKey("imgResults");
	binWriter.beginArray();
	if (m_workbook->isBookProtectedWithHiddenProperty())
	{
		WriteHookInValidResult(param.field_str("hook_id"), Webhook_PermissionDeny, __X("E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE"), binWriter);
		binWriter.endArray();
		WOLOG_ERROR << "[_ExecShapeImgCopy] hidden range error ";
		return WO_FAIL;
	}


	for (int i = 0; i < l; i++)
	{
		binary_wo::VarObj oneChart = chartUUIDs.at(i);
		IDX sheetIdx = GetSheetIdx(m_workbook->GetCoreWorkbook()->GetBook(), oneChart);
		if (sheetIdx == INVALIDIDX) 
		{
			WOLOG_ERROR << "Invalid sheet index";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_SheetIdx_Invalid, __X("Invalid sheet index"), binWriter);
			continue;
		}
		ks_stdptr<ISheet> spSheet;
		IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
		pBook->GetSheet(sheetIdx, &spSheet);
		if (spSheet == nullptr) 
		{
			WOLOG_ERROR << "Invalid spSheet";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_SheetIdx_Invalid, __X("Invalid spSheet"), binWriter);
			continue;
		}
		ks_stdptr<IUnknown> ptrUnk;
		ks_stdptr<IKDrawingCanvas> ptrDrawingCanvas;
		spSheet->GetExtDataItem(edSheetDrawingCanvas, &ptrUnk);
		if (ptrUnk == NULL) 
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy] ptrUnk NULL";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_SheetIdx_Invalid, __X("ptrUnk NULL"), binWriter);
			continue;
		}
		
		ptrDrawingCanvas = ptrUnk;
		
		ks_castptr<drawing::ShapeTree> cpTree = ptrDrawingCanvas;
		if (nullptr == cpTree)
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy]  cpTree null error";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("cpTree null error"), binWriter);
			continue;
		}

		QString uuid = krt::fromUtf16(oneChart.field_str("uuid"));
		const drawing::AbstractShape* shape = cpTree->getAbsShape(uuid);
		if (nullptr == shape)
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy]  shape null error";
			//流转出去写丢了，告诉一下服务端，服务端需要解绑
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Hook_lose, __X("E_HOOK_LOSE"), binWriter);
			continue;
		}
		AbstractLayer* layer = shape->getLayer();
		if (nullptr == layer)
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy]  EtChartShapeTree null error";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("EtChartShapeTree null error"), binWriter);
			continue;
		}

		layer = layer->getSupLayer();
		if (nullptr == layer)
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy]  EtChartLayer null error";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("EtChartLayer null error"), binWriter);
			continue;
		}

		AbstractModel* etShape = layer->getModel()->getParent();
		if (nullptr == etShape)
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy]  etShape null error";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("etShape null error"), binWriter);
			continue;
		}
	
		ks_castptr<drawing::AbstractShape> spEtShape = etShape;
		if (nullptr == spEtShape)
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy]  spEtShape null error";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("spEtShape null error"), binWriter);
			continue;
		}
		int16 dpi = ctx.getUser()->getDpi();
		hr = spEtShape->getPicture(sha1, picData, dpi / 96.0, false, true);
		if (FAILED(hr))
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy]  getPicture error";
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("getPicture error"), binWriter);
			continue;
		}
		WebStr path = nullptr;
		QString qPath;
		if (supportDir) {
			WebStr dir = param.field_str("fileDir");
			QDir qdir(krt::fromUtf16(dir));
			if (!qdir.exists()) {
				WOLOG_ERROR << "[_ExecShapeImgCopy]Image save error dir not exists";
				WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("Image save error dir not exists"), binWriter);
				continue;
			}
			qPath = krt::fromUtf16(dir) + QDir::separator() + uuid;
		} else {
			path = param.field_str("filePath");
			qPath =  krt::fromUtf16(path);
		}
		QFile file(qPath);
		if (!file.open(QIODevice::WriteOnly))
		{	
			WOLOG_ERROR << "[_ExecShapeImgCopy]Image open error:" << qPath;
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("Image open error"), binWriter);
			continue;
		}
			
		
		bool sucess = file.write(picData);
		if (!sucess)
		{
			WOLOG_ERROR << "[_ExecShapeImgCopy]Image write error:" << qPath;
			WriteHookInValidResult(param.field_str("hook_id"), oneChart.field_str("uuid"), Webhook_Fail, __X("Image write error"), binWriter);
			file.close();
			continue;
		}
		file.close();
		WOLOG_INFO << "[_ExecShapeImgCopy] sucess: " << qPath;
	}
	binWriter.endArray();
	return hr;
}

WebInt ExecDetail::ExecCopyImg()
{	
	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	if (!CopyLink::isSuppportCopyLink(pBook))
	{
		WOLOG_ERROR << "unsupport file formate";
		return WO_FAIL;
	}
	WebStr type = m_root.field_str("type");
	binary_wo::BinWriter binWriter;
	HRESULT hr;
	if (xstrcmp(__X("updateRanges"), type) == 0) {
		hr = _ExecRangeImgCopy(m_root, binWriter);
	} else if (xstrcmp(__X("updateCharts"), type) == 0) {
		hr = _ExecShapeImgCopy(m_root, binWriter);
	}
	FillOut(binWriter, msgType_Query);
	return hr;
}

void ExecDetail::_GetIKShape(IDX sheetIdx, binary_wo::VarObj objIdxPath, KEtRevisionContext* pCtx, IKShape** ppShape)
{
    if (objIdxPath.arrayLength() == 0)
        return;
    
	ShapeIdxPath idxPath;
	idxPath.reserve(objIdxPath.arrayLength());
	for (int i = 0, cnt = objIdxPath.arrayLength(); i < cnt; ++i)
		idxPath.push_back(objIdxPath.item_uint32(i));

	*ppShape = pCtx->getShapeByIdxPath(sheetIdx, idxPath);
}

void ExecDetail::_GetApiShape(IKShape* pShape, KsoShape** ppShape)
{
	drawing::ShapeTreeControl* pCtl = getShapeTreeControl(pShape);
	ks_stdptr<IKCoreObject> spParent;
	pCtl->getSupLayerControl()->getCoreObject(pCtl->getLayer()->getSupLayer()->getModel(), &spParent);
	if (spParent != nullptr)
		getShapeTreeApiFactory(pShape)->CreateShape(spParent, pShape, ppShape);
}

chart::KCTShape* ExecDetail::_GetChartShape(IKShape* pShape)
{
	if (!pShape)
	{
		return nullptr;
	}

	ks_stdptr<KsoShape> spShape;
	_GetApiShape(pShape, &spShape);
	if (!spShape)
		return nullptr;

	ks_stdptr<oldapi::KsoChart> spChart;
	spShape->get_Chart(&spChart);
	if (!spChart)
		return nullptr;

	return dynamic_cast<chart::KCTChart*>(spChart->coreChartShape());
}

WebInt ExecDetail::_ExecDelayDbsheetCopy(const WebMimeData *pMimeData, const binary_wo::VarObj &param)
{
	DBSheetCommonHelper dbsheetHelper(m_workbook);
    
    QString mimeType = QString::fromUtf8(pMimeData->mimeType);
	ks_wstring txt;
    HRESULT hr = E_FAIL;
    if (mimeType == "db_cp_json_format")
    {
        bool hasBreak = false;
        hr = dbsheetHelper.RangeCopy(param, txt, hasBreak);
    }
    else if (mimeType == "text/plain")
    {
        bool hasBreak = false;
        bool bAlterAttachmentField = false;
        bool bAlterNoteField = false;
        hr = dbsheetHelper.GetAllDataText(param, txt, hasBreak, bAlterAttachmentField, bAlterNoteField);
    }

	if (FAILED(hr)) return hr;

	FILE *clipBoardFile = fopen(pMimeData->path, "w");
	if (NULL == clipBoardFile)
		return WO_FAIL;	
	
	QByteArray utf8Txt = QString::fromUtf16(txt.c_str()).toUtf8();
	if ( (utf8Txt.size() < 3) || ((quint8)utf8Txt[0] != 0xEF) ||
	 ((quint8)utf8Txt[1] != 0xBB) || ((quint8)utf8Txt[2] != 0xBF) )
	{
		// add bom to help guess correct
		utf8Txt.insert(0, "\xEF\xBB\xBF", 3);
	}

	fwrite(utf8Txt.constData(), sizeof(char), utf8Txt.size(), clipBoardFile);
	fclose(clipBoardFile);
	return 0;
}

WebInt ExecDetail::_ExecDelayShapeCopy(const WebMimeData *pMimeData, const binary_wo::VarObj &param)
{
	if (NULL == pMimeData || pMimeData->path == NULL)
		return WO_FAIL;

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	ks_stdptr<IETPersist> spPersist;
	spPersist = m_workbook->GetCoreApp()->GetAppPersist()->GetPersist();
	if(spPersist == NULL)
		return WO_FAIL;

    IDX shapeSheetIdx = param.field_int32("shapeSheetIdx");
    ks_stdptr<ISheet> spSrcSheet;
	m_workbook->GetCoreWorkbook()->GetBook()->GetSheet(shapeSheetIdx, &spSrcSheet);
	ks_stdptr<IKDrawingCanvas> spSrcDrawCanvas;
	{
		ks_stdptr<IUnknown> spUnk;
		spSrcSheet->GetExtDataItem(edSheetDrawingCanvas, &spUnk);
		spSrcDrawCanvas = spUnk;
		ASSERT(spSrcDrawCanvas);
	}

    // get shape range
    ks_stdptr<IKShapeRange> ptrShapeRange;
	spSrcDrawCanvas->CreateShapeRange(&ptrShapeRange);
    binary_wo::VarObj shapes = param.get("shapes");
    for (int i = 0; i < shapes.arrayLength(); i++)
    {
        IKShape *pShape = nullptr;
        binary_wo::VarObj shape = shapes.at(i);
        _GetIKShape(shapeSheetIdx, shape.get("shapeIdxPath"), &ctx, &pShape);
        chart::KCTShape *chartRoot = _GetChartShape(pShape);
        if (chartRoot && ctx.getProtectionCtx()->isBookHasHiddenProperty())
        {
			return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
        }

        if (pShape)
            ptrShapeRange->AddShape(pShape);
        else
            return E_FAIL;
    }

    ks_stdptr<_Application> spEtApiApp = m_workbook->GetCoreApp();
    spEtApiApp->put_CutCopyMode(etCopyCutNone);

	KsoMimeData mimeData;
    // get clipboard data
	spPersist->WoExportGvmlDrawing(spSrcSheet, ptrShapeRange, &mimeData);

	QString mimeType = QString::fromUtf8(pMimeData->mimeType);
	KsoDataStream* dataStream = ks_castptr<KsoDataStream>(mimeData.ksoData(mimeType));
	if (!dataStream)
		return WO_FAIL;

	QByteArray arr = dataStream->getByteArray();
	FILE *clipBoardFile = fopen(pMimeData->path, "w");
	if (NULL == clipBoardFile)
		return WO_FAIL;
	
	ctx.clearAttachmentIds();
	LONG nCount = 0;
	ptrShapeRange->GetShapeCount(&nCount);
	for (INT32 i = 0; i < nCount; i++)
	{	
		ks_stdptr<drawing::AbstractShape> spAbsShape;
		ptrShapeRange->GetShapeByIndex(i, &spAbsShape);
		ctx.addCloudImgAttachmentId(spAbsShape);
	}

	fwrite(arr.data(), sizeof(char), arr.size(), clipBoardFile);
	fclose(clipBoardFile);
	return WO_OK;
}

/**
 * @brief 解析前端转过来的动态水印参数，代码复制于 execdetail.cpp/WpsExecDetail::ExecExportPdf()
 */
static void parseWatermarkOptions(const VarObj& m_root, PdfPrintWatermarkOptions& pdfPrintWatermarkOptions)
{
	// 接收watermark 参数
	if (!m_root.has("watermark"))
		return;
	
	VarObj waterMark = m_root.get_s("watermark");
	if (!waterMark.has("type"))
		return;
	
	INT type = waterMark.field_int32("type");
	// 0为无水印， 1为文字水印
	if (type == 1)
	{
		if (waterMark.has("value"))
		{
			pdfPrintWatermarkOptions.strText = waterMark.field_str("value");
		}
		if (waterMark.has("fontFamily"))
		{
			pdfPrintWatermarkOptions.strFontName = waterMark.field_str("fontFamily");
		}
		if (waterMark.has("fontSize"))
		{
			pdfPrintWatermarkOptions.fontSize = waterMark.field_int32("fontSize");
		}
		if (waterMark.has("fillstyle"))
		{
			ks_wstring strColor = waterMark.field_str("fillstyle");
			QString qstrColor = krt::fromUtf16(strColor.c_str());
			QColor fillColor(qstrColor);
			if (fillColor.isValid())
			{
				pdfPrintWatermarkOptions.color = fillColor;
			}
		}
		if (waterMark.has("rotate"))
		{
			pdfPrintWatermarkOptions.rotate = (float)waterMark.field_double("rotate");
		}
		if (waterMark.has("horizontal"))
		{
			pdfPrintWatermarkOptions.horAdvance = waterMark.field_int32("horizontal");
		}
		if (waterMark.has("vertical"))
		{
			pdfPrintWatermarkOptions.verAdvance = waterMark.field_int32("vertical");
		}
	}
}

WebInt ExecDetail::ExecExportPdf()
{
	if (ExecCheckUserAllVisiblePermission() != WO_OK)
	{
		WOLOG_INFO << "[ExportAs] " << " file protected";
		return WO_FILE_PROTECTED;
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	WebStr fileName = m_root.field_str("fileName");
	WOLOG_INFO << "[ExportAs] ExecExportPdf filename: " << fileName << ", user: " << ctx.getUser()->userID();

	IKWorksheet* pWs = NULL;
	if (m_root.has("objSheet") || m_root.has("sheetIndex")) //sheetIndex仅用于转换服务，因为objSheet没有顺序不能直接用于定位某一个sheet，所以新增sheetIndex，用于提供单独转换某一个sheet的功能。
	{
		WOLOG_INFO << "[ExportAs] " << "export sheet";
		//sheetIndex 从1开始
		IDX iSheet = !m_root.has("sheetIndex") ? ctx.getSheetIndex(m_root.field_web_id("objSheet")) : m_root.field_int32("sheetIndex") - 1;
		if (iSheet == INVALIDIDX)
		{
			WOLOG_INFO << "[ExportAs] " << "invalid sheetidx";
			return WO_FAIL;
		}
		util::QRLabelHelper qrLabelHelper(m_workbook->GetCoreWorkbook(), &ctx, util::QRLabelHelper::UseType_Export_KSheet2PDF, nullptr);
		qrLabelHelper.ConvertQRLabel2CellImg(iSheet);

		pWs = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
		if (pWs)
		{
			DownloadImgSyncRes downRes = CheckDownLoadImg(pWs->GetSheet());
			if (DownloadImgSync_TOO_LARGE == downRes)
			{
				WOLOG_INFO << "[ExportAs] CheckDownLoadImg, resCode:  " << downRes;
				return WO_EXPORT_DOWNLOAD_URLIMAGE_SIZE_EXCEED;
			}
			else if (DownloadImgSync_UNKNOWN == downRes)
			{
				WOLOG_INFO << "[ExportAs] CheckDownLoadImg, resCode:  " << downRes;
				return WO_EXPORT_DOWNLOAD_URLIMAGE_UNKNOWN;
			}
		}
	}
	else
	{
		WOLOG_INFO << "[ExportAs] " << "export book";
		IKWorksheets* pWorkshees = m_workbook->GetCoreWorkbook()->GetWorksheets();
		int cnt = pWorkshees->GetSheetCount();
		for (int i = 0; i < cnt; ++i)
		{
			util::QRLabelHelper qrLabelHelper(m_workbook->GetCoreWorkbook(), &ctx, util::QRLabelHelper::UseType_Export_KSheet2PDF, nullptr);
			qrLabelHelper.ConvertQRLabel2CellImg(i);

			ISheet* curSheet = pWorkshees->GetSheetItem(i)->GetSheet();
			// 分享工作表下隐藏的sheet不打印或导出pdf
			if (!curSheet->IsGridSheet() || ctx.getProtectionCtx()->isSheetHidden(curSheet->GetStId()))
				continue;
			
			if (curSheet->IsDbDashBoardSheet())
				continue;

			SHEETSTATE sheetState;
			HRESULT hr = curSheet->GetVisible(&sheetState);
			if (SUCCEEDED(hr) && ssVisible != sheetState)
				continue;

			DownloadImgSyncRes downRes = CheckDownLoadImg(curSheet);
			if (DownloadImgSync_TOO_LARGE == downRes)
			{
				WOLOG_INFO << "[ExportAs] CheckDownLoadImg, resCode:  " << downRes;
				return WO_EXPORT_DOWNLOAD_URLIMAGE_SIZE_EXCEED;
			}
			else if (DownloadImgSync_UNKNOWN == downRes)
			{
				WOLOG_INFO << "[ExportAs] CheckDownLoadImg, resCode:  " << downRes;
				return WO_EXPORT_DOWNLOAD_URLIMAGE_UNKNOWN;
			}
		}
	}

	//FIXME 需要以图片方式导出PDF，可以先使用新接口。
	bool fullImage = m_root.has("fullImage") ? m_root.field_bool("fullImage") : false;
	if (fullImage)
	{
		WOLOG_INFO << "[ExportAs] " << "export fullImage";
		if (pWs == NULL)
			return WO_INVALID_ARG;

		ks_stdptr<_Worksheet> spApiWorksheet = pWs;
		return WoExportPdf(spApiWorksheet, m_root, gs_callback->exportData);
	}

	bool bIsPrintExport = m_root.field_bool("isPrintExport");
	if (bIsPrintExport)
	{
		WOLOG_INFO << "[ExportAs] " << "is print export";
		if (!HasContentToPrint(m_workbook->GetCoreWorkbook(), pWs))
			return WO_NOTHING_TO_PRINT;
	}

	auto skipOrNot = [&](ISheet* pCurSheet) -> bool {
		return !pCurSheet->IsGridSheet();
	};
	TmpPageSetupDataHelper setupHlp(m_workbook);
	WebInt ret = setupHlp.RevisePageSetups(m_root, pWs, skipOrNot);
	if (WO_OK != ret)
		return ret;

	if (m_root.has("maxNumberOfPages"))
	{
		int nPages = m_root.field_int32("maxNumberOfPages");
		if (nPages > 0 && GetPageCount(m_workbook->GetCoreWorkbook(), pWs) > nPages)
		{
			WOLOG_INFO << "[ExportAs] " << "exceed page limit";
			return WO_EXCEED_EXPORT_PAGES_LIMIT;
		}
	}

	// 导出前重算。 针对工具woconvert导出后显示公式结果不正确的处理方式。
	// 不要在协作中使用该参数。
	if (m_root.has("calcBeforeExport") && m_root.field_bool("calcBeforeExport"))
	{
		ks_stdptr<_Application> spEtApp = m_workbook->GetCoreApp();
		ETCalculation calcMode = etCalculationAutomatic;
		spEtApp->get_Calculation(&calcMode);
		if(calcMode != etCalculationAutomatic || spEtApp->NeedResumeCalculate())
		{
			WOLOG_INFO << "[calcBeforeExport] begin";

			HRESULT hr = m_workbook->GetCoreWorkbook()->GetBook()->RecalculateAll(RCAF_NONE);
			if (FAILED(hr))
			{
				WOLOG_INFO << "[calcBeforeExport] RecalculateAll error: " << hr;
				return hr;
			}
			WOLOG_INFO << "[calcBeforeExport] end";
		}
	}

	bool bIncludeDocProperties = m_root.has("includeDocProperties") ? m_root.field_bool("includeDocProperties") : false;
	bool bIncludeComments = m_root.has("includeComments") ? m_root.field_bool("includeComments") : false;
	bool bIncludeHyperlinks = m_root.has("includeHyperlinks") ? m_root.field_bool("includeHyperlinks") : false;

	// 动态水印参数
	PdfPrintWatermarkOptions watermarkOptions;
	parseWatermarkOptions(m_root, watermarkOptions);

	IKWorkbook* pWb = m_workbook->GetCoreWorkbook();

	ETExportPdfOptions options = { 0 };
	options.bIncludeDocProperties = bIncludeDocProperties;
	options.bIncludeComments = bIncludeComments;
	options.bIncludeHyperlinks = bIncludeHyperlinks;
	options.pWatermarkOptions = &watermarkOptions;
	if (m_root.has("masterPassword"))
		options.szMasterPassword = m_root.field_str("masterPassword");
	if (m_root.has("userPassword"))
		options.szUserPassword = m_root.field_str("userPassword");

	HRESULT hr = pWb->ExportPdf(pWs, fileName, &options);
	WO_LOG_X(m_workbook->getLogger(), WO_LOG_INFO, "ExecExportPdf: %d", hr);
	return SUCCEEDED(hr) ? WO_OK : WO_FAIL;
}

// Coding/support/kdocframe/src/frame.h
#define _KsoFormatOFD                         102
#define XLNOCHANGE	3

WebInt ExecDetail::ExecExportOfd()
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	WebStr fileName = m_root.field_str("fileName");

	if (ctx.getProtectionCtx()->isBookHasHiddenProperty())
	{
		WEBLOG_ERROR("Workbook is protected");
		return WO_FILE_PROTECTED;
	}

	IKWorksheet* pWs = NULL;
	IKWorksheets* pWorkshees = m_workbook->GetCoreWorkbook()->GetWorksheets();
	int cnt = pWorkshees->GetSheetCount();
	for (int i = 0; i < cnt; ++i)
	{
		ISheet* curSheet = pWorkshees->GetSheetItem(i)->GetSheet();
		util::DownloadCellImgForSheet(m_workbook->GetCoreWorkbook(), curSheet);
		util::DownloadSheetAllUrlImg(curSheet);
	}

	bool bIsPrintExport = m_root.field_bool("isPrintExport");
	if (bIsPrintExport)
	{
		if (!HasContentToPrint(m_workbook->GetCoreWorkbook(), pWs))
		{
			WEBLOG_ERROR("Nothing to print");
			return WO_NOTHING_TO_PRINT;
		}
	}

	auto skipOrNot = [&](ISheet* pCurSheet) -> bool {
		return false;
	};
	TmpPageSetupDataHelper setupHlp(m_workbook);
	WebInt ret = setupHlp.RevisePageSetups(m_root, pWs, skipOrNot);
	if (WO_OK != ret)
		return ret;

	if (m_root.has("maxNumberOfPages"))
	{
		int nPages = m_root.field_int32("maxNumberOfPages");
		if (nPages > 0 && GetPageCount(m_workbook->GetCoreWorkbook(), pWs) > nPages)
		{
			WEBLOG_ERROR("Page count exceeded");
			return WO_EXCEED_EXPORT_PAGES_LIMIT;
		}
	}

	VARIANT FileFormat;
	V_VT(&FileFormat) = VT_I4;
	V_I4(&FileFormat) = _KsoFormatOFD;

	ks_bstr strTargetFileName(fileName);
	VARIANT FileName;
	V_VT(&FileName) = VT_BSTR;
	V_BSTR(&FileName) = strTargetFileName;

	VARIANT var = {0};
	HRESULT hr = m_workbook->GetCoreWorkbook()->SaveAs(FileName, FileFormat, var, var, var, var, (ETSaveAsAccessMode)XLNOCHANGE, var, var, var, var);
	WO_LOG_X(m_workbook->getLogger(), WO_LOG_INFO, "ExecExportOfd: %d", hr);
	if (FAILED(hr))
	{
		WEBLOG_ERROR_QSTRING("Failed to save to " + QString::fromUtf16(strTargetFileName.c_str()));
		WEBLOG_DUMP_BT();
	}
	return SUCCEEDED(hr) ? WO_OK : WO_FAIL;
}

WebInt ExecDetail::ExecExportImg()
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	//sheetIndex仅用于转换服务，因为objSheet没有顺序不能直接用于定位某一个sheet，所以新增sheetIndex，用于提供单独转换某一个sheet的功能。
	//sheetIndex 从1开始
	IDX iSheet = INVALIDIDX;
	if (m_root.has("sheetStId"))
		iSheet = GetSheetIdx( m_workbook->GetCoreWorkbook()->GetBook(), m_root);
	else if (m_root.has("sheetIndex"))
		iSheet = m_root.field_int32("sheetIndex") - 1;
	else
		iSheet = ctx.getSheetIndex(m_root.field_web_id("objSheet"));

	if (iSheet == INVALIDIDX)
	{
		WEBLOG_ERROR_FMT("Invalid sheet index: %d", iSheet);
		return WO_FAIL;
	}

	if (ExecCheckUserAllVisiblePermission() != WO_OK)
	{
		return WO_FILE_PROTECTED;
	}

	IKWorksheet* pWs = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWs)
	{
		WEBLOG_ERROR_FMT("Invalid sheet index: %d", iSheet);
		return WO_FAIL;
	}

	util::QRLabelHelper qrLabelHelper(m_workbook->GetCoreWorkbook(), &ctx, util::QRLabelHelper::UseType_Export_KSheet2IMG, nullptr);
	qrLabelHelper.ConvertQRLabel2CellImg(iSheet);

	ks_stdptr<_Worksheet> spApiWorksheet = pWs;	
	DownloadImgSyncRes downRes = CheckDownLoadImg(pWs->GetSheet());
	if (DownloadImgSync_TOO_LARGE == downRes)
	{
		WOLOG_INFO << "[ExportAs] CheckDownLoadImg, resCode:  " << downRes;
		WEBLOG_ERROR("Failed to download image, size exceeded");
		return WO_EXPORT_DOWNLOAD_URLIMAGE_SIZE_EXCEED;
	}
	else if (DownloadImgSync_UNKNOWN == downRes)
	{
		WOLOG_INFO << "[ExportAs] CheckDownLoadImg, resCode:  " << downRes;
		WEBLOG_ERROR("Failed to download image, unknown error");
		return WO_EXPORT_DOWNLOAD_URLIMAGE_UNKNOWN;
	}

	WebStr fileName = m_root.field_str("fileName");
	WebStr fmt = m_root.field_str("format");
	int dpi = m_root.has("dpi") ? m_root.field_int32("dpi") : 96;
	bool bWaterMark = m_root.has("waterMark") ? m_root.field_bool("waterMark") : false;
	bool bCombine2LongPic = m_root.has("combine2LongPic") ? m_root.field_bool("combine2LongPic") : false;
	bool bFirstPage = m_root.has("firstPage") ? m_root.field_bool("firstPage") : false;
	int nCombineLimit = m_root.has("combineLimit") ? m_root.field_int32("combineLimit") : 0; //如果指定了combineLimit，合并长图时，最大页数为combineLimit，-1表示不限定
	int nScale = m_root.has("scale") ? m_root.field_int32("scale") : -1; //-1 不生效
	int nQuality = m_root.has("quality") ? m_root.field_int32("quality") : -1; //-1 不生效
	bool bXva = m_root.has("xva");

	HRESULT hr = E_FAIL;
	if (!bXva)
	{
		hr = WoExportImage(spApiWorksheet, fileName, fmt, dpi, bWaterMark, bCombine2LongPic, bFirstPage);
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_INFO, "ExecExportImg hr: %d", hr);
	}
	else
	{
		// 表格按页导出
		std::vector<int> vtPageIndex = KExportUtils::parseExportPages(m_root);
		hr = WoExportImageEx(spApiWorksheet, fileName, fmt, dpi, bWaterMark, bCombine2LongPic, bFirstPage,
			nCombineLimit, vtPageIndex, nScale, nQuality, m_root, bXva ? gs_callback->exportData : nullptr);
	}

	if (FAILED(hr))
		WEBLOG_ERROR("Failed to export image");
	return SUCCEEDED(hr) ? WO_OK : WO_FAIL;
}

HRESULT ExecDetail::ExtractDbAttachmentDataToServer(UINT sheetId, EtDbId viewId, bool isDbData2Xlsx, bool isCompressedBundle, bool isFilterVideo, 
	const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes, DbSheetsAttachmentInfoMap& dbAttachmentData)
{
    binary_wo::BinWriter binWriter;
    DbAttachmentDataExporter dbAttachmentDataExporter(m_workbook);
	dbAttachmentDataExporter.SetIsFilterVideo(isFilterVideo);
	dbAttachmentDataExporter.SetUnsupportedAttachmentFileSuffixTypes(unsupportedAttachmentFileSuffixTypes);
	HRESULT hr = S_OK;
	binWriter.beginArray("data");
	if (isDbData2Xlsx) //全表
	{
		hr = dbAttachmentDataExporter.ExtractAllSheetDataToServer(m_workbook, binWriter);
	}
    else //单表导出
	{
		hr = dbAttachmentDataExporter.ExtractDataToServer(sheetId, viewId, binWriter);
	}
	binWriter.endArray();
    if (FAILED(hr))
    {
        WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in dbAttachmentDataExporter.ExtractDataToServer()!");
        return WO_FAIL;
    }

    // 将这些字段透传给服务端
    static const std::vector<WebName> transparentTransmissionFieldNames{"dirPath", "namingRule", "downloadId",
                                                                        "failFormat", "tooLargeFormat",
                                                                        "keywordFormat"};
    for (const auto fieldName: transparentTransmissionFieldNames)
    {
        if (m_root.has(fieldName))
        {
            WebStr str = m_root.field_str(fieldName);
            binWriter.addStringField(str, fieldName);
        }
    }
	binWriter.addBoolField(!isCompressedBundle,"withOnlyImage");
    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice attachmentData = {shbt.get(), binWriter.writeLength()};
    WebSlice responseData = {nullptr, 0};
    gs_callback->downloadDbAttachment(&attachmentData, &responseData);
    binary_wo::BinReader rd(responseData.data, responseData.size);
    binary_wo::VarObjRoot responseRoot = rd.buildRoot();
    binary_wo::VarObj response = responseRoot.cast();
    binary_wo::VarObj result = response.get_s("result");

    // 获取附件名称，附件导出失败会有前缀，附件重名会添加后缀
    for (int i = 0; i < result.arrayLength_s(); i++)
    {
		AttachmentInfo attachmentInfo;
		AttachmentPos attachmentPos;
        binary_wo::VarObj item = result.at_s(i);
		bool isImage = item.field_bool("isImage");

        bool ok = false;
        QString objectKey = QString::fromUtf16(item.field_str("objectKey"));
        UINT itemIndex = objectKey.toUInt(&ok);
        if (!ok)
            continue;
        UINT sheetId = item.field_int32("sheetId");
		attachmentPos.index = itemIndex;

        EtDbId fieldId = INV_EtDbId;
        hr = _appcore_GainDbSheetContext()->DecodeEtDbId(item.field_str("fieldId"), &fieldId);
        if (FAILED(hr))
            continue;
		attachmentPos.fieldId = fieldId;

        EtDbId recordId = INV_EtDbId;
        hr = _appcore_GainDbSheetContext()->DecodeEtDbId(item.field_str("recordId"), &recordId);
        if (FAILED(hr))
            continue;
		attachmentPos.recordId = recordId;

		WebInt code = item.field_int32("code");
		if (code != WO_OK)
		{
			attachmentInfo.fileName = item.field_str("fileName");
			attachmentInfo.isSuccess = false;
		}
		else
		{
			if (isImage) //图片类型附件，返回下载的地址
			{
				attachmentInfo.filePath  = item.field_str("filePath");
			}
			else //返回文件名
			{
				attachmentInfo.fileName = item.field_str("fileName");
			}
			attachmentInfo.fieldName = item.field_str("fieldName");
			attachmentInfo.isImage = isImage;
			attachmentInfo.attachmentWidth = static_cast<int64_t>(item.field_double("width"));
			attachmentInfo.attachmentHeight = static_cast<int64_t>(item.field_double("height"));
		}
		dbAttachmentData[sheetId].emplace(attachmentPos, std::move(attachmentInfo));
    }
    return S_OK;
}

HRESULT ExecDetail::ExtractDbViewAttachmentDataToServer(UINT sheetId, EtDbId viewId, bool isFilterVideo, const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes)
{
	binary_wo::BinWriter binWriter;
    DbAttachmentDataExporter dbAttachmentDataExporter(m_workbook);
	dbAttachmentDataExporter.SetIsFilterVideo(isFilterVideo);
	dbAttachmentDataExporter.SetUnsupportedAttachmentFileSuffixTypes(unsupportedAttachmentFileSuffixTypes);
	HRESULT hr = S_OK;
	binWriter.beginArray("data");
	hr = dbAttachmentDataExporter.ExtractDataToServer(sheetId, viewId, binWriter);
	binWriter.endArray();
    if (FAILED(hr))
    {
        WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in dbAttachmentDataExporter.ExtractDataToServer()!");
        return hr;
    }

    // 将这些字段透传给服务端
    static const std::vector<WebName> transparentTransmissionFieldNames{"dirPath", "namingRule", "downloadId",
                                                                        "failFormat", "tooLargeFormat",
                                                                        "keywordFormat"};
    for (const auto fieldName: transparentTransmissionFieldNames)
    {
        if (m_root.has(fieldName))
        {
            WebStr str = m_root.field_str(fieldName);
            binWriter.addStringField(str, fieldName);
        }
    }
	binWriter.addBoolField(false,"withOnlyImage");
    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice attachmentData = {shbt.get(), binWriter.writeLength()};
    WebSlice responseData = {nullptr, 0};
    gs_callback->downloadDbAttachment(&attachmentData, &responseData);
	return S_OK;
}

// 将 dbsheet 导出为 xlsx 文件
WebInt ExecDetail::ExecExportXlsx()
{
	bool isMasterProc = false;
	if (m_root.has("isMasterProc"))
	{
		// #1225542 提供给jsapi调用，不强制子进程导出
		isMasterProc = m_root.field_bool("isMasterProc");
		WOLOG_INFO << "[ExecExportXlsx] exec in master process";
	}
		
	if (!isMasterProc && gs_procType != WebProcTypeChild)
	{
		WOLOG_INFO << "[ExecExportXlsx] Not in child process";
		return WO_FAIL;
	}

	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	// 如果当前打开的文件是et, 报错退出.
	bool bKsheet = m_workbook->GetBMP()->bKsheet;
	if (!m_workbook->GetBMP()->bDbSheet && !bKsheet)
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Exporting ET to XLSX!");
		return WO_FAIL;
	}

	if (!m_root.has("filePath"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No filePath parameter!");
		return WO_FAIL;
	}
	PCWSTR filePath = m_root.field_str("filePath");

	ks_stdptr<Workbooks> spWorkbooks;
	m_workbook->GetCoreApp()->get_Workbooks(&spWorkbooks);
	if (!spWorkbooks)
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail to get Workbooks!");
		return WO_FAIL;
	}

	UINT sheetStId = 0;
	// 默认导出 defaultView
	EtDbId viewId = EtDbId_DefaultView;
	bool isDbData2Xlsx = false;
	bool copyContent = true;
	// 增量导入模板适配
	bool isForAppendTemplate = false;
	if (m_root.has("copyContent"))
		copyContent = m_root.field_bool("copyContent");
	if (m_root.has("isForAppendTemplate"))
		isForAppendTemplate = m_root.field_bool("isForAppendTemplate");
	HRESULT hr = S_OK;
	int exportOption = ExportXlsxOptions::XlsxEmbedImages;
	if (m_root.has("exportOption"))
		exportOption = m_root.field_int32("exportOption");
	if (m_root.has("withAttachment"))
		exportOption = exportOption | ExportXlsxOptions::ZipWithAttachment;

	if (m_root.has("sheetStId"))
	{
		sheetStId = m_root.field_uint32("sheetStId");
		if (m_root.has("viewId"))
		{
			hr = _appcore_GainDbSheetContext()->DecodeEtDbId(m_root.field_str("viewId"), &viewId);
			if (FAILED(hr))
			{
				WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Failed in decoding viewId!");
				return WO_FAIL;
			}
		}
	}
	else
		isDbData2Xlsx = true;

	// 权限检查
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<IUnknown> spUnknown;
	VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if (!isDbData2Xlsx)
		hr = spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No enough jurisdiction!");
		return WO_FAIL;
	}

    bool isCompressedBundle = false;
	bool isFilterVideo = false;
	std::set<ks_wstring> unsupportedAttachmentFileSuffixTypes;
    DbSheetsAttachmentInfoMap attachmentData;
	if (!(exportOption & ExportXlsxOptions::XlsxWithoutAttachmentCol))
	{
		if (!isDbData2Xlsx && ((exportOption & ExportXlsxOptions::ZipWithAttachment) || (exportOption & ExportXlsxOptions::ZipWithImages)))
		{
			isCompressedBundle = true;
			//DB导出为压缩包：若用户未非白名单企业的用户，将亦不导出视频至表格/本地，所有视频做过滤处理
			isFilterVideo = !(m_root.has("isWhiteListCompanyUser") && m_root.field_bool("isWhiteListCompanyUser"));
			if(m_root.has("unsupportedAttachmentFileSuffixTypes"))
				DbSheet::ParseUnsupportedAttachmentFileSuffixTypes(m_root, unsupportedAttachmentFileSuffixTypes);
		}
		hr = ExtractDbAttachmentDataToServer(sheetStId, viewId, isDbData2Xlsx, isCompressedBundle, isFilterVideo, unsupportedAttachmentFileSuffixTypes, attachmentData);
		if (FAILED(hr))
			return WO_FAIL;
	}

	// 创建一个 XLSX 文件.
	int sheetCnt = 1;
	if (isDbData2Xlsx)
		VS(pBook->GetSheetCount(&sheetCnt));
	int needCreateSheetCnt = sheetCnt;
	for (int i = 0; i < sheetCnt && !bKsheet; i++)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(i, &spSheet);
		ASSERT(spSheet);
		SHEETSTATE state;
		spSheet->GetVisible(&state);
		if (state != ssVisible || !spSheet->IsDbSheet())
			needCreateSheetCnt -= 1;
	}

	AddWorkbookScope newWbScope(spWorkbooks.get(), m_workbook, ctx, static_cast<UINT>(needCreateSheetCnt));
	if (!newWbScope.GetWorkbook())
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in AddWorkbookScope!");
		return WO_FAIL;
	}

	et_sptr<Db2XlsxExporter> exporter;
	if (isDbData2Xlsx)
    {
        exporter = new DbData2XlsxExporter(m_workbook, newWbScope.GetWorkbook(), &ctx);
    }
	else
    {
        exporter = new DbViewExporter(m_workbook, newWbScope.GetWorkbook(), sheetStId, viewId);
    }
	exporter->setIsExportAttachment(!(exportOption & ExportXlsxOptions::XlsxWithoutAttachmentCol));
	exporter->setAttachmentData(isCompressedBundle, &attachmentData);
	if (m_root.has("needCopyDataFormat"))
		exporter->setNeedCopyDataFormat(m_root.field_bool("needCopyDataFormat"));
	hr = exporter->Init();
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in Db2XlsxExporter::Init()!");
		return WO_FAIL;
	}
	exporter->SetCopyContent(copyContent);
	exporter->SetIsForAppendTemplate(isForAppendTemplate);
	hr = exporter->Exec();
	gs_callback->clearDownloadDbAttachment();
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in Db2XlsxExporter::Exec()!");
		return WO_FAIL;
	}

	std::vector<INT32> leftTopCellBackup;
	KComVariant varFileName(filePath);
	// copy 自 shell2/weboffice/webet/etcmd/src/workbook.cpp, 写死format
	XlFileFormat fileFormat = xlOpenXMLWorkbook;
	KComVariant varFormat(fileFormat, VT_I4);
	VARIANT VAREMPTY = {VT_EMPTY};
	m_workbook->SetExportLeftTopCell(newWbScope.GetWorkbook(), leftTopCellBackup);
	VS(hr = newWbScope.GetWorkbook()->SaveAs(varFileName, varFormat,
								VAREMPTY,	VAREMPTY,
								VAREMPTY,	VAREMPTY,
								etExclusive, 
								VAREMPTY,	KComVariant().AssignBOOL(FALSE),
								VAREMPTY,	VAREMPTY));
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in virtual _Workbook::SaveAs(args...)!");
		return WO_FAIL;
	}
	if (exporter->getIsExceedMaxColumns()) //导出附件超出最大列限制时，返回错误码
		return WO_EXPORT_EXCEEDED_MAX_COLS;
	return WO_OK;
}

// 将dbsheet中的附件单独导出为压缩包
WebInt ExecDetail::ExecExportDbAttachmnet()
{
	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();

	// 如果当前打开的文件是et, 报错退出.
	bool bKsheet = m_workbook->GetBMP()->bKsheet;
	if (!m_workbook->GetBMP()->bDbSheet && !bKsheet)
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Exporting is not supported!");
		return WO_FAIL;
	}

	UINT sheetStId = 0;
	EtDbId viewId = EtDbId_DefaultView;
	HRESULT hr = S_OK;

	if (!m_root.has("sheetStId") || !m_root.has("viewId"))
	{
		WOLOG_INFO << "[ExecExportDbAttachmnet] Is not sheetStId or viewId ";
		return WO_FAIL;
	}

	sheetStId = m_root.field_uint32("sheetStId");
	hr = _appcore_GainDbSheetContext()->DecodeEtDbId(m_root.field_str("viewId"), &viewId);
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Failed in decoding viewId!");
		return WO_FAIL;
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	
	// 权限检查
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<IUnknown> spUnknown;
	VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	spDBUserGroups->GetJudgement(&spProtectionJudgement);
	hr = spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No enough jurisdiction!");
		return WO_FAIL;
	}

	std::set<ks_wstring> unsupportedAttachmentFileSuffixTypes;
	//DB导出为压缩包：若用户未非白名单企业的用户，将亦不导出视频至表格/本地，所有视频做过滤处理
	bool isFilterVideo = !(m_root.has("isWhiteListCompanyUser") && m_root.field_bool("isWhiteListCompanyUser"));
	if(m_root.has("unsupportedAttachmentFileSuffixTypes"))
		DbSheet::ParseUnsupportedAttachmentFileSuffixTypes(m_root, unsupportedAttachmentFileSuffixTypes);
	hr = ExtractDbViewAttachmentDataToServer(sheetStId, viewId,isFilterVideo, unsupportedAttachmentFileSuffixTypes);
	if (FAILED(hr))
		return WO_FAIL;

	return WO_OK;
}

WebInt ExecDetail::ExecExportDbToKSheet()
{
	WOLOG_INFO << "[ExecExportDbToKSheet] start";
	if (gs_procType != WebProcTypeChild)
	{
		WOLOG_INFO << "[ExecExportDbToKSheet] Not in child process";
		return WO_FAIL;
	}
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();

	// 如果当前打开的文件是et, 报错退出.
	bool bKsheet = m_workbook->GetBMP()->bKsheet;
	if (!m_workbook->GetBMP()->bDbSheet && !bKsheet)
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "[ExecExportDbToKSheet] Exporting ET to KSheet!");
		return WO_FAIL;
	}
	_Workbook* pDbWb = m_workbook->GetCoreWorkbook();
	if (!pDbWb)
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "[ExecExportDbToKSheet] File not open");
		return WO_FAIL;
	}
	if (!m_root.has("filePath"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "[ExecExportDbToKSheet] No filePath parameter!");
		return WO_FAIL;
	}

	ks_stdptr<Workbooks> spWorkbooks;
	m_workbook->GetCoreApp()->get_Workbooks(&spWorkbooks);
	if (!spWorkbooks)
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "[ExecExportDbToKSheet] Fail to get Workbooks!");
		return WO_FAIL;
	}
	UINT sheetStId = 0;
	EtDbId viewId = INV_EtDbId;
	bool isDbData2KSheet = false;
	HRESULT hr = S_OK;

	if (m_root.has("sheetStId"))
	{
		sheetStId = m_root.field_uint32("sheetStId");
		if (!m_root.has("viewId"))
		{
			WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No viewId parameter!");
			return WO_FAIL;
		}
		hr = _appcore_GainDbSheetContext()->DecodeEtDbId(m_root.field_str("viewId"), &viewId);
		if (FAILED(hr))
		{
			WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Failed in decoding viewId!");
			return WO_FAIL;
		}
	}
	else
		isDbData2KSheet = true;

	// 权限检查
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<IUnknown> spUnknown;
	VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if (!isDbData2KSheet)
		hr = spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No enough jurisdiction!");
		return WO_FAIL;
	}

    bool isExportAttachment = false;
    std::map<std::tuple<EtDbId, EtDbId, UINT>, ks_wstring> attachmentNames;

	PCWSTR filePath =  m_root.field_str("filePath");
	PCWSTR userId = ctxPtr->getUser()->userID();
	Db2KsBookConverter converter(pDbWb, userId, spProtectionJudgement, ctxPtr.get());
	std::set<ks_wstring> unsupportedAttachmentFileSuffixTypes;
	if(m_root.has("unsupportedAttachmentFileSuffixTypes"))
	{
		DbSheet::ParseUnsupportedAttachmentFileSuffixTypes(m_root, unsupportedAttachmentFileSuffixTypes);
		converter.SetUnsupportedAttachmentFileSuffixTypes(unsupportedAttachmentFileSuffixTypes);
	}

	hr = converter.Init();
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportDbToKSheet] Fail in Db2KsBookConverter::Init!";
		return WO_FAIL;
	}
	converter.setClearDefaultValue(false);
	hr = converter.Exec();
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportDbToKSheet] Fail in Db2KsBookConverter::Exec!";
		return WO_FAIL;
	}

	hr = m_workbook->ExportDbToKSheet(filePath);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportDbToKSheet] SaveAs fail: " << hr;
		return WO_FAIL;
	}
	return WO_OK;
}

WebInt ExecDetail::ExecExportKSheetToKSheet()
{
	WOLOG_INFO << "[ExecExportKSheetToKSheet] start";
	if (gs_procType != WebProcTypeChild)
	{
		WOLOG_INFO << "[ExecExportKSheetToKSheet] Not in child process";
		return WO_FAIL;
	}
	_Workbook* pWb = m_workbook->GetCoreWorkbook();
	if (!pWb)
	{
		WOLOG_INFO << "[ExecExportKSheetToKSheet] File not open";
		return WO_FAIL;
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	if (!m_root.has("filePath"))
	{
		WOLOG_INFO << "[ExecExportKSheetToKSheet] No filePath parameter!";
		return WO_FAIL;
	}
	PCWSTR filePath =  m_root.field_str("filePath");
	HRESULT hr = S_OK;

	if (m_root.has("sheetIdVec"))
	{
		std::set<UINT> sheetStIds;
		ParseExportSheetStIds(sheetStIds);
		RemoveNoExportSheet(pWb, sheetStIds);
	}

	hr = m_workbook->ExportKSheetToKSheet(filePath);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExportKSheetToKSheet] SaveAs fail: " << hr;
		return WO_FAIL;
	}
	return WO_OK;
}

WebInt ExecDetail::ExecExportSheetToCSV()
{
    WOLOG_INFO << "[ExecExportSheetToCSV] start";

    if (!m_workbook->GetCoreWorkbook())
    {
        WOLOG_INFO << "[ExecExportSheetToCSV] file not open";
        return WO_FAIL;
	}

	if (!m_root.has("filePath") 
		|| (!m_root.has("objSheet") && !m_root.has("sheetStId") && !m_root.has("sheetIdx")))
	{
		WOLOG_INFO << "[ExecExportSheetToCSV] lack parameter!";
		return WO_FAIL;
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	if (ExecCheckUserAllVisiblePermission() != WO_OK)
	{
		WOLOG_INFO << "[ExecExportSheetToCSV] unsupport export file protected!";
		return WO_FILE_PROTECTED;
	}

	ctxPtr->SetIsNoCheckPerms(true);
	

	PCWSTR fileName =  m_root.field_str("filePath");
	IDX sheetIdx = 0;
	if (m_root.has("sheetIdx"))
		sheetIdx = m_root.field_int32("sheetIdx");
	if (m_root.has("sheetStId"))
	{
		// 若有传StId,则优先使用StId
		IDX sheetStId = m_root.field_int32("sheetStId");
		m_workbook->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(sheetStId, &sheetIdx);
	}

	if (sheetIdx == INVALIDIDX)
	{
		WOLOG_INFO << "[ExecExportSheetToCSV] SaveAs fail: sheetIdx is invalid";
		return WO_SHEET_NOT_FOUND;
	};
	IKWorksheets *pWorksheets = m_workbook->GetCoreWorkbook()->GetWorksheets();
	OBJID objId = -1;
    if (sheetIdx < pWorksheets->GetSheetCount())
        pWorksheets->GetSheetItem(sheetIdx)->GetSheet()->GetObjID(&objId);
	if (m_root.has("objSheet"))
	{
		WebID objSheetID = m_root.field_web_id("objSheet");
		if(objId != objSheetID)
			sheetIdx = ctxPtr->getSheetIndex(objSheetID);
	}

	if(sheetIdx == INVALIDIDX)
		return E_FAIL;
	bool skipBlankCols = false;
	if (m_root.has("skipBlankCols"))
	{
		WOLOG_INFO << "[ExecExportSheetToCSV] have skipBlankCols param : " << skipBlankCols;
		skipBlankCols = m_root.field_bool("skipBlankCols");
	}
	ctxPtr->SetUTF_8_BOM(true);
	HRESULT hr = m_workbook->ExportSheetToCSV(fileName, sheetIdx, skipBlankCols);
	ctxPtr->SetUTF_8_BOM(false);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportSheetToCSV] SaveAs fail: " << hr;
		return WO_FAIL;
	}
	return WO_OK;
}

static WebInt SaveStreamToFile(IStream* pStream, const WCHAR* fileName)
{
	STATSTG stat;
	if (pStream->Stat(&stat, 0) != S_OK)
		return WO_FAIL;
	
	int64_t cbSize = stat.cbSize.QuadPart;

	QFile file(QString::fromUtf16(fileName));
	if (file.open(QIODevice::WriteOnly))
	{
		LARGE_INTEGER iMove = { 0 };
		pStream->Seek(iMove, STREAM_SEEK_SET, nullptr);

		char* buf = new char[8192];
		while (true)
		{
			ULONG cb = 8192;
			ULONG cbRead = 0;
			pStream->Read(buf, cb, &cbRead);
			if (cbRead == 0)
				break;
			
			file.write(buf, cbRead);
		}
		delete[] buf;
		return WO_OK;
	}
	else
	{
		return WO_FAIL;
	}
}

static QString generateFileNameNoDot(KEtWorkbook* book)
{
	ks_bstr bookName;
	book->GetCoreWorkbook()->get_Name(&bookName);
	QString qstrBookName;
	if (bookName.c_str() && *bookName.c_str())
	{
		qstrBookName = QString::fromUtf16(bookName.c_str());
		int dot = qstrBookName.lastIndexOf('.');
		if (dot > 0)
			qstrBookName = qstrBookName.left(dot);
	}
	else
		qstrBookName = "sheet";
	
	return qstrBookName;
}

WebInt ExecDetail::ExecExportTxt()
{
    if (!m_workbook->GetCoreWorkbook())
    {
        WEBLOG_ERROR("[ExecExportTxt] file not open");
        return WO_FAIL;
	}

	if (!m_root.has("fileName"))
	{
		WEBLOG_ERROR("[ExecExportTxt] lack fileName parameter!");
		return WO_INVALID_ARG;
	}

	WCHAR delimiter = '\t';
	if (m_root.has("delimiter"))
	{
		const WCHAR* strDelim = m_root.field_str("delimiter");
		if (*strDelim)
			delimiter = *strDelim;
	}

	bool ignoreProtect = false;
	if (m_root.has("ignoreProtect"))
	{
		ignoreProtect = m_root.field_bool("ignoreProtect");
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	if (!ignoreProtect && ctxPtr->getProtectionCtx()->isBookHasHiddenProperty())
	{
		WEBLOG_ERROR("[ExecExportTxt] unsupport export file protected!");
		return WO_FILE_PROTECTED;
	}

	PCWSTR fileName =  m_root.field_str("fileName");
	if (!*fileName)
		return WO_INVALID_ARG;
	bool combine2LongTxt = m_root.has("combine2LongTxt") ? m_root.field_bool("combine2LongTxt") : false;
	bool holdLineFeed = m_root.has("holdLineFeed") ? m_root.field_bool("holdLineFeed") : false;
	bool firstPage = m_root.has("firstPage") ? m_root.field_bool("firstPage") : false;

	IKWorksheets *pWorksheets = m_workbook->GetCoreWorkbook()->GetWorksheets();
	int sheetCount = pWorksheets->GetSheetCount();

	std::vector<int> ranges;
	if (m_root.has("ranges") || m_root.has("fromPage"))
	{
		ranges = KExportUtils::parseExportPages(m_root);
		if (ranges.empty())
		{
			WEBLOG_ERROR("Failed to parse sheet ranges");
			return WO_INVALID_ARG;
		}
	}
	else
	{
		// 不填页码就导出全部
		for (int i = 0; i < sheetCount; i++)
			ranges.push_back(i + 1);
	}

	int successCount = 0;
	if (!combine2LongTxt)
	{
		QDir dir;
		QString destDir = QString::fromUtf16(fileName);
		dir.mkpath(destDir);
		QString strBookName = generateFileNameNoDot(m_workbook);

		for (int i = 0; i < ranges.size(); i++)
		{
			int sheetIdx = ranges[i] - 1;
			if (sheetIdx < 0 || sheetIdx >= sheetCount)
				continue;
			QString destFilePath = destDir + "/" + strBookName + "(" + QString::number(i + 1) + ").txt";
			wo::MediumGuard medium(krt::utf16(destFilePath));
			HRESULT hr = m_workbook->ExportSheetToTxt(medium.get(), sheetIdx, delimiter);
			if (FAILED(hr))
				WEBLOG_ERROR("[ExecExportTxt] failed")
			else
			{
				successCount++;
				if (firstPage)
					break;
			}
		}

		if (successCount == 0)
			return WO_FAIL;
	}
	else
	{
		wo::MediumGuard mergedText;
		for (int i = 0; i < ranges.size(); i++)
		{
			int sheetIdx = ranges[i] - 1;
			if (sheetIdx < 0 || sheetIdx >= sheetCount)
				continue;
			wo::MediumGuard medium;
			HRESULT hr = m_workbook->ExportSheetToTxt(medium.get(), sheetIdx, delimiter);
			if (FAILED(hr))
				WEBLOG_ERROR("[ExecExportTxt] failed")
			else
			{
				successCount++;
				STATSTG stat;
				LARGE_INTEGER zero = { 0 };
				medium.get()->pstm->Seek(zero, STREAM_SEEK_SET, nullptr);
				if (medium.get()->pstm->Stat(&stat, 0) == S_OK)
					medium.get()->pstm->CopyTo(mergedText.get()->pstm, stat.cbSize, nullptr, nullptr);
				if (firstPage)
					break;
			}
		}

		if (successCount == 0)
			return WO_FAIL;
		IStream* pStream = mergedText.get()->pstm;
		return SaveStreamToFile(pStream, fileName);
	}

	return WO_OK;
}

WebInt ExecDetail::ExecExportToLocalFile(FILEFORMAT fileFormat)
{
    WOLOG_INFO << "[ExecExportToLocalFile] start";

    if (!m_workbook->GetCoreWorkbook())
    {
        WOLOG_INFO << "[ExecExportToLocalFile] file not open";
        return WO_FAIL;
	}

	if (!m_root.has("filePath"))
	{
		WOLOG_INFO << "[ExecExportToLocalFile] No filePath parameter!";
		return WO_FAIL;
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	if (ExecCheckUserAllVisiblePermission() != WO_OK)
	{
		WOLOG_INFO << "[ExecExportToLocalFile] unsupport export file protected!";
		return WO_FILE_PROTECTED;
	}

	ctxPtr->SetIsNoCheckPerms(true);
	IDX activeSheetIdx = INVALIDIDX;
	if (m_root.has("activeSheetId"))
	{
		_Workbook* wb = m_workbook->GetCoreWorkbook();
		IBook* pBook = wb->GetBook();
		pBook->STSheetToRTSheet(m_root.field_int32("activeSheetId"), &activeSheetIdx);
		if (activeSheetIdx != INVALIDIDX)
			wb->GetWndInfos()->SetActiveSheet(0, activeSheetIdx);
	}

	PCWSTR fileName =  m_root.field_str("filePath");	
	HRESULT hr = m_workbook->ExportToLocalFile(fileName, fileFormat, ctx);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportToLocalFile] SaveAs fail: " << hr;
		switch (hr)
		{
		case E_DOWNLOAD_IMAGE_SIZE_EXCEED_LIMIT:
			return WO_EXPORT_DOWNLOAD_URLIMAGE_SIZE_EXCEED;
		case E_DOWNLOAD_IMAGE_UNKNONW:
			return WO_EXPORT_DOWNLOAD_URLIMAGE_UNKNOWN;
		default:
			break;
		}
		return WO_FAIL;
	}
	return WO_OK;
}

WebInt ExecDetail::ExecExportXlsxToKSheet()
{
	WOLOG_INFO << "[ExecExportXlsxToKSheet] start";
	if (gs_procType != WebProcTypeChild)
	{
		WOLOG_INFO << "[ExecExportXlsxToKSheet] Not in child process";
		return WO_FAIL;
	}
	_Workbook* pEtWb = m_workbook->GetCoreWorkbook();
	if (!pEtWb)
	{
		WOLOG_INFO << "[ExecExportXlsxToKSheet] File not open";
		return WO_FAIL;
	}
	if (!m_root.has("filePath"))
	{
		WOLOG_INFO << "[ExecExportXlsxToKSheet] No filePath parameter!";
		return WO_FAIL;
	}

	PCWSTR filePath =  m_root.field_str("filePath");
	Et2KsBookConverter converter(pEtWb);
	HRESULT hr = converter.Init();
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportXlsxToKSheet] Fail in Et2KsBookConverter::Init!";
		return WO_FAIL;
	}
	hr = converter.Exec();
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportXlsxToKSheet] Fail in Et2KsBookConverter::Exec!";
		return WO_FAIL;
	}
	hr = m_workbook->ExportXlsxToKSheet(filePath);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportXlsxToKSheet] SaveAs fail: " << hr;
		return WO_FAIL;
	}
	return WO_OK;
}

WebInt ExecDetail::ExecExportDb()
{
	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	if (m_workbook->GetBMP()->bDbSheet)
		return ExecExportDb2Db();

	if (false == m_root.has("filePath"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No filePath parameter!");
		return WO_FAIL;
	}
	PCWSTR filePath = m_root.field_str("filePath");

	// 服务端要求强制传入 sheetStId.
	if (false == m_root.has("sheetStId"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No sheetStId parameter!");
		return WO_FAIL;
	}
	UINT sheetStId = m_root.field_uint32("sheetStId");
	IDX sheetIdx = 0;
	bool isWholeBook = false;
	if (0 == sheetStId)
		isWholeBook = true;
	else
	{
		m_workbook->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(sheetStId, &sheetIdx);
		if (sheetIdx == INVALIDIDX)
			return WO_FAIL;
	}

	if (false == m_root.has("defaultName"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No defaultName parameter!");
		return WO_FAIL;
	}
	binary_wo::VarObj defaultName = m_root.get("defaultName");

	if (false == defaultName.has("field_name"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No field_name parameter!");
		return WO_FAIL;
	}

	if (false == defaultName.has("group_name"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No group_name parameter!");
		return WO_FAIL;
	}

	if (false == defaultName.has("grid_name"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No grid_name parameter!");
		return WO_FAIL;
	}

	if (false == defaultName.has("kanban_name"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No kanban_name parameter!");
		return WO_FAIL;
	}

	if (false == m_root.has("font"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No font parameter!");
		return WO_FAIL;
	}
	PCWSTR font = m_root.field_str("font");

	if (false == m_root.has("fontSize"))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "No fontSize parameter!");
		return WO_FAIL;
	}
	int fontSize = m_root.field_int32("fontSize");

	ks_stdptr<Workbooks> spWorkbooks;
	m_workbook->GetCoreApp()->get_Workbooks(&spWorkbooks);
	if (nullptr == spWorkbooks)
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail to get Workbooks!");
		return WO_FAIL;
	}

	// todo: 应该是没必要在 et 导出 db 的环节里进行 dbsheet 的权限检查
	HRESULT hr = S_OK;
	// 判断服务端指定的(模板)文件大小，若文件大小为0，新建一个DB文件，若不为0，打开文件并将导入的数据表插入到文件内
	bool toEmptySheet = true;
	ks_stdptr<_Workbook> spDbWorkbook;
	QString qFilePath = QString::fromUtf16(filePath);
	QFileInfo fileInfo(qFilePath);
	// todo: 导入时会创建看板视图, 并在不存在单选项字段时创建一个. 这个字段的配置信息需要前端传, 此处暂时使用自己构造的
	VarObj kanbanCfg = m_root.add_field_struct("kanbanCfg");
	if (fileInfo.size() == 0)
	{
		// 创建一个 DB 文件.
		AddWorkbookScope newWbScope(spWorkbooks.get(), m_workbook, ctx, 1, font, fontSize, TRUE);
		if (nullptr == newWbScope.GetWorkbook())
		{
			WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in AddWorkbookScope!");
			return WO_FAIL;
		}
		spDbWorkbook = newWbScope.GetWorkbook();
		hr = RunExportDb(spDbWorkbook, toEmptySheet, kanbanCfg, defaultName, sheetIdx, filePath, isWholeBook);
	}
	else
	{
		// 打开模板页的逻辑
		OpenWorkbookScope openWorkbookScope(spWorkbooks, m_workbook->GetCoreApp()->GetAppPersist()->GetPersist(), filePath);
		//打开源文件
		ks_stdptr<_Workbook> spTemplateWorkbook = openWorkbookScope.GetWorkbook();
		if (!spTemplateWorkbook)
			return WO_FAIL;
		m_workbook->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(spTemplateWorkbook);
		spDbWorkbook = spTemplateWorkbook;
		toEmptySheet = false;
		hr = RunExportDb(spDbWorkbook, toEmptySheet, kanbanCfg, defaultName, sheetIdx, filePath, isWholeBook);
	}
	if (FAILED(hr))
		return WO_FAIL;

	return WO_OK;
}

WebInt ExecDetail::ExecExportDb2Db()
{
	WOLOG_INFO << "[ExecExportDb2Db] start";
	if (gs_procType != WebProcTypeChild)
	{
		WOLOG_INFO << "[ExecExportDb2Db] Not in child process";
		return WO_FAIL;
	}
	_Workbook* pWb = m_workbook->GetCoreWorkbook();
	if (!pWb)
	{
		WOLOG_INFO << "[ExecExportDb2Db] File not open";
		return WO_FAIL;
	}
	if (!m_root.has("filePath"))
	{
		WOLOG_INFO << "[ExecExportDb2Db] No filePath parameter!";
		return WO_FAIL;
	}
	PCWSTR filePath =  m_root.field_str("filePath");
	HRESULT hr = S_OK;

	ExportDbtScope exportScope(pWb->GetBook());
	RemoveNoPermissionDbSheetData(pWb);
	if (m_root.has("sheetIdVec"))
	{
		std::set<UINT> sheetStIds;
		ParseExportSheetStIds(sheetStIds);
		RemoveNoExportSheet(pWb, sheetStIds);
	}
	hr = m_workbook->ExportToDb(filePath);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[ExecExportDb2Db] SaveAs fail: " << hr;
		return WO_FAIL;
	}
	return WO_OK;
}

void ExecDetail::ParseExportSheetStIds(std::set<UINT>& sheetStIds)
{
	binary_wo::VarObj sheetIdVecObj = m_root.get_s("sheetIdVec");
	int cnt = sheetIdVecObj.arrayLength_s();
	for (int i = 0; i < cnt; ++i)
	{
		UINT stId = sheetIdVecObj.at_s(i).value_uint32();
		sheetStIds.insert(stId);
	}
}

WebInt ExecDetail::RunExportDb(_Workbook* pDbWorkbook, bool toEmptySheet, 
	VarObj& kanbanCfg, VarObj& defaultName, IDX sheetIdx, PCWSTR filePath, bool isWholeBook)
{
	// todo: 从前端传 field
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	InitContext(ctx);
	Et2DbExporter exporter(m_workbook->GetCoreWorkbook(), pDbWorkbook, &ctx, toEmptySheet, defaultName, kanbanCfg, sheetIdx, isWholeBook);
	HRESULT hr = S_OK;

	exporter.SetFilePath(filePath);
	hr = exporter.Exec();
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in Exec()!");
		return WO_FAIL;
	}

	KComVariant varFileName(filePath);
	// copy 自 shell2/weboffice/webet/etcmd/src/workbook.cpp, 写死format
	XlFileFormat apiff = xlOpenXMLWorkbook;
	KComVariant varFormat(apiff, VT_I4);
	VARIANT VAREMPTY = {VT_EMPTY};
	VS(hr = pDbWorkbook->SaveAs(varFileName, varFormat,
								VAREMPTY,	VAREMPTY,
								VAREMPTY,	VAREMPTY,
								etExclusive, 
								VAREMPTY,	KComVariant().AssignBOOL(FALSE),
								VAREMPTY,	VAREMPTY));
	if (FAILED(hr))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in virtual _Workbook::SaveAs(args...)!");
		return WO_FAIL;
	}

	return WO_OK;
}

void ExecDetail::ExtendSvgRange(ISheet *pSheet, int rowCount, int colCount, CELL &cell)
{
	ASSERT(rowCount > 0);
	ASSERT(colCount > 0);

	et_sptr<ISheetEnum> spEnum;
	pSheet->CreateEnum(&spEnum);

	const int maxRow = pSheet->GetBMP()->cntRows - 1;
	const int maxCol = pSheet->GetBMP()->cntCols - 1;

	while (rowCount > 0 && cell.row <= maxRow)
	{
		BOOL bHidden = FALSE;
		const INT32 cnt = spEnum->GetRowHidden(cell.row, &bHidden); 
		if (cnt <= 0)
			break;

		if (bHidden)
		{
			cell.row += cnt;
		}
		else
		{
			int s = std::min(cnt, rowCount);
			cell.row += s;
			rowCount -= s;
		}
	}

	while (colCount > 0 && cell.col <= maxCol)
	{
		BOOL bHidden = FALSE;
		const INT32 cnt = spEnum->GetColHidden(cell.col, &bHidden);
		if (cnt <= 0)
			break;

		if (bHidden)
		{
			cell.col += cnt;
		}
		else
		{
			int s = std::min(cnt, colCount);
			cell.col += s;
			colCount -= s;
		}
	}

	--cell.row;
	--cell.col;
}

WebInt ExecDetail::ExecExportSvg()
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	if (ctx.getProtectionCtx()->isBookHasHiddenProperty())
		return WO_FILE_PROTECTED;

	ks_castptr<IKETUserConn> etUser = ctx.getUser();
	etUser->setHasCall(UserConnFuncCall::kExportAsSvg);
	ks_stdptr<IKWorksheet> spWorksheet;
	if (m_root.has("sheetStId"))
	{
		IKWorkbook* pWb = m_workbook->GetCoreWorkbook();
		IBook* pBook = pWb->GetBook();
		IDX sheetIdx = GetSheetIdx(pBook, m_root);
		if (sheetIdx == INVALIDIDX)
			return WO_FAIL;
		spWorksheet = pWb->GetWorksheets()->GetSheetItem(sheetIdx);
	}
	else if (m_root.has("defaultLefttop") && m_root.field_bool("defaultLefttop"))
	{
		//若带有defaultLefttop参数，则默认取当前activesheet
		spWorksheet = m_workbook->GetCoreWorkbook()->GetActiveWorksheet();
	}
	else
	{
		WebStr sheetName = m_root.has("sheet_name") ? m_root.field_str("sheet_name") : nullptr;
		if (!sheetName) // 没有时拿第一个sheet
		{
			IKWorkbook* pWb = m_workbook->GetCoreWorkbook();
			if (pWb->GetWorksheets()->GetSheetCount() <= 0)
				return WO_FAIL;
			if (m_workbook->GetFirstVisibleSheet(&spWorksheet) == INVALIDOBJID)
				spWorksheet = pWb->GetWorksheets()->GetSheetItem(0);
		}
		else
		{
			m_workbook->GetDefInitSheet(sheetName, &spWorksheet);
		}
	}

	if (!spWorksheet)
		return WO_FAIL;

	ISheet *pSheet = spWorksheet->GetSheet();
	if (pSheet == nullptr)
		return WO_FAIL;

	if (!pSheet->IsGridSheet())
		return WO_SVG_EXPORT_CANCELLED;

	IKWorksheetView *pSheetView = spWorksheet->GetActiveWorksheetView();
	ks_stdptr<_Worksheet> spApiWorksheet = spWorksheet;

	ISheetWndInfo *pSheetWndInfo = pSheetView->GetSheetWndInfo();
	if (pSheetWndInfo == nullptr)
		return WO_FAIL;

	constexpr int maxWidth = 20000;
	constexpr int maxHeight = 20000;
	int width = -1;
	int height = -1;
	CELL LTCell = { 0, 0 };

	int zoom = m_root.field_int32("zoom");
	if (zoom < MIN_ZOOM || zoom > MAX_ZOOM)
		zoom = pSheetWndInfo->GetNormalZoom();
	else
		pSheetWndInfo->SetNormalZoom(zoom);

	IRenderView* rdView = pSheetView->GetActiveRenderView();

	const bool hasExportRange = m_root.has("exportRange");
	if (hasExportRange)
	{
		if (pSheetView->GetFreeze())
		{
			WOLOG_INFO << "[ExecExportSvg] sheet view is frozen";
			return WO_SVG_EXPORT_CANCELLED;
		}

		CELL RBCell = { 0, 0 };
		binary_wo::VarObj exportRange = m_root.get("exportRange");
		if (m_root.has("defaultLefttop") && m_root.field_bool("defaultLefttop"))
		{
			pSheetWndInfo->GetLeftTopCell(LTCell);
			RBCell.row = std::max(0, LTCell.row + exportRange.field_int32("rowCount") - 1);
			RBCell.col = std::max(0, LTCell.col + exportRange.field_int32("colCount") - 1);

			WOLOG_INFO << "[ExecExportSvg] defaultLefttop: " << LTCell << ", rightbottom:" << RBCell;
		}
		else
		{
			LTCell.row = std::max(0, exportRange.field_int32("rowFrom"));
			LTCell.col = std::max(0, exportRange.field_int32("colFrom"));
			RBCell.row = std::max(0, exportRange.field_int32("rowTo"));
			RBCell.col = std::max(0, exportRange.field_int32("colTo"));

			WOLOG_INFO << "[ExecExportSvg] lefttop: " << LTCell << ", rightbottom:" << RBCell;
		}

		RBCell.row = std::min(pSheet->GetBMP()->cntRows - 1, RBCell.row);
		RBCell.col = std::min(pSheet->GetBMP()->cntCols - 1, RBCell.col);

		if (RBCell.row < LTCell.row || RBCell.col < LTCell.col)
		{
			WOLOG_INFO << "[ExecExportSvg] invalid arg, lefttop: " << LTCell << ", rightbottom:" << RBCell;
			return WO_INVALID_ARG;
		}

		if (rdView)
		{
			IRenderMeasure *renderMeasure = rdView->GetRenderMeasure();
			if (renderMeasure)
			{
				const int maxSize = 1000;
				const int rowCount = std::min(RBCell.row - LTCell.row + 1, maxSize);
				const int colCount = std::min(RBCell.col - LTCell.col + 1, maxSize);
				RBCell.row = LTCell.row;
				RBCell.col = LTCell.col;
				ExtendSvgRange(pSheet, rowCount, colCount, RBCell);
				WOLOG_INFO << "[ExecExportSvg] export extend range, lefttop: " << LTCell << ", rightbottom:" << RBCell;

				const double scale = zoom / 100.0;
				const double logWidth = renderMeasure->MeasureWidth(LTCell.col, RBCell.col, scale, FALSE);
				const double logHeight = renderMeasure->MeasureHeight(LTCell.row, RBCell.row, scale);     

				IRenderNormalView* rdNormalView = rdView->GetNormalView();
				IRenderData* renderData = rdNormalView->GetRenderData();
				
				double rowHeaderWidth = renderMeasure->CalcRowHeaderWidth(RBCell.row + 1, scale) + renderMeasure->CalcRowGroupWidth(scale);
				double colHeaderHeight = renderMeasure->CalcColHeaderHeight(scale) + renderMeasure->CalcColGroupHeight(scale);
				width = ceil((logWidth + rowHeaderWidth) * scale * 96.0 / 1440.0);
				height = ceil((logHeight + colHeaderHeight) * scale * 96.0 / 1440.0);
			}
		}
	}
	else
	{
		width = m_root.field_int32("width"); // 单位是像素
		height = m_root.field_int32("height"); // 单位是像素
	}

	if (width < 0 || width > maxWidth)
		return WO_INVALID_ARG;

	if (height < 0 || height > maxHeight)
		return WO_INVALID_ARG;

	constexpr double maxDpi = 20.0;
	double dpi = m_root.field_double("dpi");
	if (dpi < 0.0 || dpi > maxDpi)
		return WO_INVALID_ARG;
		
	if (!hasExportRange)
	{
		LTCell.row = m_root.field_int32("row");
		LTCell.col = m_root.field_int32("col");
	}

	if (LTCell.row < 0 || LTCell.col < 0)
		pSheetWndInfo->GetLeftTopCell(LTCell);

	bool bIsMobile = false;
	if (m_root.has("device_type"))
	{
		WebStr deviceStr = m_root.field_str("device_type");
		if (xstrcmp(__X("mobile"), deviceStr) == 0)
			bIsMobile = true;
	}

	if (rdView)
	{
		IRenderData* rdData = rdView->GetNormalView()->GetRenderData();
		if (rdData)
		{
			rdData->GetRowColMeassureData()->ClearRowHeightBuf();
			rdData->GetCellRenderData()->ClearCellRenderDataCache();
		}
	}

	EXPORT_SVG_PARAM param;
	param.LTCell = LTCell;
	param.exportSize = {width, height};
	param.devicePixelRatio = dpi;
	param.zoom = zoom / 100.0;
	param.bIsMobile = bIsMobile;
	param.bCanDrawGridHeadBorder = m_workbook->GetBMP()->bKsheet || bIsMobile;	// et（移动端除外）新视觉左上角没有边框
	param.bHeadBorderGrandientOn = m_root.has("headBorderGrandientOn") ? m_root.field_bool("headBorderGrandientOn") : false;

	QBuffer buffer;

	WO_LOG_X(m_workbook->getLogger(), WO_LOG_DEBUG, 
		QString("ExecExportSvg: left top: (%1, %2) export size: (%3, %4) devicePixelRatio: %5 zoom: %6")
			.arg(param.LTCell.row).arg(param.LTCell.col)
			.arg(param.exportSize.cx).arg(param.exportSize.cy)
			.arg(param.devicePixelRatio)
			.arg(param.zoom)
			.toUtf8());

	HRESULT hr = WoExportSvg(&buffer, pSheetView, param);
	WebInt ret = SUCCEEDED(hr) ? WO_OK : WO_FAIL;
	if (SUCCEEDED(hr))
	{
		constexpr qint64 KiB = 1024;
		constexpr qint64 MiB = 1024 * KiB;
		constexpr qint64 maxSvgDataSize = 10 * MiB;
		if (buffer.size() > maxSvgDataSize)
		{
			ret = WO_SVG_EXPORT_CANCELLED;
			WO_LOG_X(m_workbook->getLogger(), WO_LOG_WARN,
				QString("ExecExportSvg: %1 MiB, exceeded limit")
					.arg(buffer.size())
					.toUtf8());
		}
		else
		{
			WebByte *data = reinterpret_cast<WebByte *>(buffer.buffer().data());
			WebSlice slice = {data, buffer.size()};
			gs_callback->signal(m_connID, getMsgTypeName(msgType_exportSvg), &slice);
		}

		if (rdView)
		{
			// 清空下绘制的单元格缓存, 目前导出之前需要清空,导出后保留也没什么必要了(减少内存占用)
			IRenderData* rdData = rdView->GetNormalView()->GetRenderData();
			if (rdData)
			{
				rdData->GetCellRenderData()->ClearCellRenderDataCache();
			}
		}
	}

	return ret;
}

WebInt ExecDetail::ExecExportThumbnail()
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	// 有区域权限不导出
	if (ctx.getProtectionCtx()->isBookHasHiddenProperty())
		return WO_FILE_PROTECTED;

	ks_stdptr<IKWorksheet> spWorksheet;
	m_workbook->GetFirstVisibleSheet(&spWorksheet, stGrid);
	// 没有普通表格则不导出
	if (!spWorksheet)
		return WO_THUMBNAIL_EXPORT_CANCELED;
	
	int width = m_root.field_int32("width");
	int height = m_root.field_int32("height");

	if (width < 0 || height < 0)
		return WO_INVALID_ARG;

	IKWorksheetView *pSheetView = spWorksheet->GetActiveWorksheetView();
	if (pSheetView == nullptr)
		return WO_FAIL;

	WebStr fileName = m_root.field_str("fileName");

	HRESULT hr = WoExportThumbnail(pSheetView, { width, height }, fileName);
	WebInt ret = SUCCEEDED(hr) ? WO_OK : WO_FAIL;

	if (SUCCEEDED(hr))
		WOLOG_INFO << "[ExecExportThumbnail] Export Success!";
	else
		WOLOG_ERROR << "[ExecExportThumbail] Export Error!";

	return ret;
}

WebInt ExecDetail::TransferAutoPassword()
{
	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<IAutoPasswordMgr> spAutoPasswordMgr;
	pBook->GetExtDataItem(edBookAutoPasswordManager, (IUnknown**)&spAutoPasswordMgr);
	if (!spAutoPasswordMgr)
		return WO_OK;

	binary_wo::VarObj userIdList = m_root.get_s("list");
	WebStr master = m_root.field_str("newMasterId");
	m_workbook->UpdateAutoPasswordMaster(userIdList, master, spAutoPasswordMgr);

	return WO_OK;
}

WebInt ExecDetail::CheckDownLoadImg(ISheet *spSheet, bool bForceAttachmentReDownload/* = true*/)
{
	WebInt downloadRes = DownloadImgSync_OK;
	downloadRes = util::DownloadCellImgForSheet(m_workbook->GetCoreWorkbook(), spSheet);
	if (DownloadImgSync_OK != downloadRes)
		return downloadRes;

	downloadRes = util::DownloadSheetAllUrlImg(spSheet, bForceAttachmentReDownload);
	if (DownloadImgSync_OK != downloadRes)
		return downloadRes;

	downloadRes = util::DownloadCommentImage(spSheet);
	if (DownloadImgSync_OK != downloadRes)
		return downloadRes;
	
	return downloadRes;
}

WebInt ExecDetail::InitConnState()
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	InitContext(*ctx);
	m_workbook->resetConnState(ctx.get(), true);
	return WO_OK;
}

void ExecDetail::AddExtState(binary_wo::BinWriter& bw, KEtRevisionContext &ctx)
{
	KBinWriterSizeU32Mc mcBinSize(&m_workbook->coreMetric(), &bw, KU32MetricItem::kExtStateResBytes);
	EtTaskExecutor* te = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	if (te->IsChangeDisabled()) {
		bw.addBoolField(true, "changeDisabled");
	}

	DenyType denyType = te->Deny();
	bw.addKey("denyType");
	switch(denyType){
		case DenyType::EverDeny:
			bw.addString(__X("everDeny"));
			break;
		case DenyType::NoDeny:
		default:
			bw.addString(__X("noDeny"));
			break;
	}
	te->ClearDeny();

	m_workbook->SerialCalcStatus(bw);
	m_workbook->SerialDiagnosisStatus(bw);

	binary_wo::VarObj res = te->GetResForSelf();
	if(res.getVar()) {
		bw.addKey("res");
		KSerialWrapBinWriter wbw(bw, NULL);
		res.serialContent(&wbw);
		te->ResetResForSelf();
	}

	{
		bw.addKey("automationRes");
		KSerialWrapBinWriter wbw(bw, NULL);
		ctx.serialiseDbAutomationTriggeredInfo(&wbw);
	}
}

//---------------------------------------------------------------------------
void ExecDetail::BP_Init(
	KEtRevisionContext& ctx, binary_wo::BinWriter& writer, ISerialAcceptor* acpt)
{
	METRICS_TIME_BINSIZE("BP_Init", this->m_workbook, writer, KU32MetricItem::kBPInitTime, KU32MetricItem::kBPInitBytes)

	std::vector<BlockPoint> vbp;
	ctx.collectInitBlockPoints(true, vbp);

	wo::IBlockPointsCache* bpc = GetCurUser()->getBlockPointCache();
	bpc->reset(kfc::tools::vtoa(vbp), vbp.size());
	
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPCnt, bpc->getBpCnt());
	
	KEtWorkbook::InitCache* pCache = m_workbook->getInitCache();
	if (pCache != nullptr) {
		pCache->makeBlockPoint(vbp);
	}

	{
		bw::Leave leave = bw::enterArray(&writer, "blockPoints");
		std::for_each(vbp.begin(), vbp.end(),
			[&writer](const BlockPoint& bp){bp.Write(writer);});
		m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPInitBlocksCnt, vbp.size());
	}
	writer.addInt32Field(bpc->getConfirmedVersion(), "blockPointsVersion");
	BPInit_SerialHyperlinks(ctx, acpt, vbp);
	BPInit_SerialSparklines(ctx, acpt, vbp);
	BPInit_SerialCellCondition(ctx, acpt, vbp);
	BPInit_SerialCellProtection(ctx, acpt, vbp);

	KEtVersionManager* mgr = static_cast<KEtVersionManager*>(m_workbook->getVersionMgr());
	WebInt fmlaResVer = InitVersion - 1; // 比 InitVersion 小 表示自动计算且初始化时计算未完成 或 手动计算结果已失效
	if (m_workbook->getCalcStatus() != KEtWorkbook::CalcStatus::calculating)
	{
		KwVersion* curVersion = mgr->getCurDataVersion();
		fmlaResVer = curVersion ? curVersion->getTaskIndex() : InitVersion;
	}
	writer.addInt32Field(fmlaResVer, "fmlaResVer");

	writer.addInt32Field(m_workbook->getEafResVersion(), "eafResVer");
	_WriteDaaysChangeArray(writer);


	WebInt sparklineRefAreaVer = sparklineRefAreaVer =
	 mgr->getSparklineRefAreaVersionCount() > 0 ? mgr->getCurSparklineRefAreaVersionID() : InitVersion;
	writer.addInt32Field(sparklineRefAreaVer, "sparklineRefAreaVer");

	writer.addUint32Field(m_workbook->getImportrangeContext().getRecalsResVer(), "recalcResVer");	
}

static void debug_out(KEtRevisionContext& ctx, const std::vector<BlockPoint>& v, const char* tag) {
	std::vector<WebID> buff;

	size_t idx = 0;
	while (buff.size() < 8u && idx < v.size()) {
		if (std::find(buff.begin(), buff.end(), v[idx].objSheet) == buff.end()) {
			buff.push_back(v[idx].objSheet);
		}
		++idx;
	}

	WO_LOG_X(ctx.getLogger(), WO_LOG_DEBUG, "tag:%s,size:%u", tag, buff.size());

	for (size_t i = 0; i < buff.size(); ++i) {
		long tmp = buff[i];
		WO_LOG_X(ctx.getLogger(), WO_LOG_DEBUG, "objID:%ld", tmp);
	}
}

void ExecDetail::BP_Proc(KEtRevisionContext& ctx, binary_wo::BinWriter& ww, ISerialAcceptor* acpt)
{
	METRICS_TIME_BINSIZE("BP_Proc", this->m_workbook, ww, KU32MetricItem::kBPProcTime, KU32MetricItem::kBPProcBytes)
	
	wo::IBlockPointsCache* bpc = GetCurUser()->getBlockPointCache();
	INT32 blockPointsVersion = bpc->getConfirmedVersion();
	if (m_root.has("blockPointsVersion"))
		blockPointsVersion = m_root.field_int32("blockPointsVersion");
	bool bApply = (blockPointsVersion == bpc->getDanglingVersion(nullptr));
	bpc->confirmDangingOperation(bApply);

	BP_Vec bpm, bpn;
	WoBlockPoint tmpBp;
	bool bEnum = bpc->getFirstItem(&tmpBp);
	while (bEnum)
	{
		bpm.push_back(tmpBp);
		bEnum = bpc->getNextItem(&tmpBp);
	}

	if (!std::is_sorted(bpm.begin(), bpm.end(), BlockPoint::LessOp())) {
		std::sort(bpm.begin(), bpm.end(), BlockPoint::LessOp());
	}
	bpm.erase( std::unique(
		bpm.begin(), bpm.end(), BlockPoint::KeyEq()), bpm.end());

	binary_wo::VarObj newBlocks = m_root.get_s("newBlocks");
	for (int32 i = 0, len = newBlocks.arrayLength_s(); i < len; ++i)
	{
		binary_wo::VarObj tmp = newBlocks.at(i);
		bpn.push_back(BlockPoint(tmp.field_web_id("objSheet"),
			tmp.field_int32("row"), tmp.field_int32("col")));
	}
	debug_out(ctx, bpm, "bpm");
	debug_out(ctx, bpn, "bpn");
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kNewBlocksCnt, bpn.size());
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPCnt, bpc->getBpCnt());

	std::vector<SsRect> fresh;
	BP_Vec monitor;
	bool bChanged = BP_Build(ctx, bpm, bpn, fresh, monitor);

	if (ctx.getProtectionCtx()->isBookHasHidden())
	{
		IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
		std::vector<SsRect> freshVisible;
		freshVisible.reserve(fresh.size());
		for (auto it = fresh.begin(); it != fresh.end(); ++it)
		{
			const SsRect &rc = *it;
			INT idxSht = ctx.getSheetIndex(rc.objSheet);
			if (idxSht == INVALIDIDX)
				continue;
			ks_stdptr<ISheet> sht;
			VS(pBook->GetSheet(idxSht, &sht));
			bool isHidden = ctx.getProtectionCtx()->isSheetHidden(sht->GetStId());
			if (!isHidden)
				freshVisible.push_back(rc);
		}
		fresh = std::move(freshVisible);
	}

	if (bChanged)
	{
		std::sort(monitor.begin(), monitor.end(), BlockPoint::LessOp());
		monitor.erase( std::unique(
			monitor.begin(), monitor.end(), BlockPoint::KeyEq()),
			monitor.end() );
		BP_Update(ww, bpm, monitor);
		BP_Fill(ctx, ww, acpt, vtoa(fresh), fresh.size());
		BP_Formula(ctx, fresh, acpt);
	}

	BP_CalcCells(ctx, ww, monitor);
	BP_UpdateSparklineRefArea(ctx, ww, monitor, fresh);

	Proc_ExtAppendInfo(ctx, ww);
	Proc_AutoSlim(ctx, ww);
}

void ExecDetail::BP_Uniform(
	const SsRect* arr, size_t cnt, BP_Cluser& rm, bool bPrecision)
{
	for (size_t i = 0; i < cnt; ++i)
	{
		SsRect ssr = arr[i];
		BlockPointEnum bpe(ssr.objSheet, ssr.rc);
		for (; bpe.IsValid(); bpe.Next())
		{
			BlockPoint tmp = bpe.Current();
			auto x = rm.insert(std::make_pair(tmp, std::vector<RECT>()));
			std::vector<RECT>& vr = x.first->second;
			if (bPrecision)
			{
				RECT sub = {0};
				VERIFY(Rect_Intersect(tmp.GetRect(), ssr.rc, sub));
				vr.push_back(sub);
			}
			else if (x.second)
			{
				ASSERT(vr.empty());
				vr.push_back(tmp.GetRect());
			}
		}
	}
}

//fresh 为需要监视的区域, monitor为执行任务后监视的区域
bool ExecDetail::BP_Build(KEtRevisionContext& ctx,
	const BP_Vec& bpm, const BP_Vec& bpn,
	std::vector<SsRect>& fresh, BP_Vec& monitor)
{
	auto conv = [](const std::vector<BlockPoint>& bps)->std::vector<SsRect>{
		std::vector<SsRect> vs;
		std::for_each(bps.begin(), bps.end(), [&vs](const BlockPoint& r){
			vs.push_back(SsRect(r.objSheet, r.GetRect()));
		});
		return std::move(vs);
	};

	std::vector<SsRect> vm = conv(bpm), vn = conv(bpn);

	bool bm = BP_Adjust(ctx, vm), bn = BP_Adjust(ctx, vn);
	if (!bm && !bn)
	{
		monitor = bpm;
		monitor.insert(monitor.end(), bpn.begin(), bpn.end());
		std::transform(bpn.begin(), bpn.end(), std::back_inserter(fresh),
			[](const BlockPoint& bp)->SsRect{
				return SsRect(bp.objSheet, bp.GetRect());});
		return !bpn.empty();
	}

	BP_Cluser cm, cn;
	BP_Uniform(vtoa(vm), vm.size(), cn, false);
	BP_Uniform(vtoa(vn), vn.size(), cn, false);
	BP_Uniform(vtoa(vm), vm.size(), cm, true);

	for (auto it = cn.begin(); it != cn.end(); ++it) {
		auto itm = cm.find(it->first);
		if (itm == cm.end()) continue;

		std::vector<RECT>& r = itm->second;
		ASSERT(it->second.size() == 1);
		RECT rc = it->second.back(); it->second.pop_back();
		CollectFragment(rc, vtoa(r), r.size(), it->second);
	}

	monitor = BP_Collect(cm, cn);

	for (auto it = cn.begin(); it != cn.end(); ++it)
	{
		WebID objSheet = it->first.objSheet;
		std::for_each(it->second.begin(), it->second.end(),
			[&fresh, objSheet](const RECT& rc){
				fresh.push_back(SsRect(objSheet, rc));});
	}

	return true;
}

bool ExecDetail::BP_Adjust(KEtRevisionContext& ctx, std::vector<SsRect>& sss)
{
	std::unordered_map<WebID, std::vector<RECT>> cluster;
	for (size_t i = 0; i < sss.size(); ++i)
	{
		MergeFragment(sss[i].rc, cluster[sss[i].objSheet]);
	}

	KwTasks tasks;
	for (auto it = cluster.begin(); it != cluster.end(); ++it)
	{
		std::vector<RECT>& r = it->second;
		for (size_t i = 0; i < r.size(); ++i)
		{
			KSerialToVarObj varBuilder;
			varBuilder.addString("name", __X("range.monitorSplit"));
			sa::Leave paramLeave = sa::enterStruct(&varBuilder, "param");
			sa::addRect(&varBuilder, r[i]);
			IDX idxSht = ctx.getSheetIndex(it->first);
			if (idxSht == INVALIDIDX)
			{
				long tmp = it->first;
				WO_LOG_X(ctx.getLogger(),
					WO_LOG_ERROR, "sheet index invalid. webid:%ld", tmp);
				continue;
			}
			AbsObject* objSheetMain = ctx.getSheetMain(idxSht);
			if (objSheetMain == nullptr)
			{
				WO_LOG_X(ctx.getLogger(), WO_LOG_ERROR, "sheet object null");
				continue;
			}
			varBuilder.addObject("objSheet", objSheetMain);
			binary_wo::VarObjRoot objRoot(varBuilder.buildVarObj());
			binary_wo::VarObj obj(objRoot.get(), objRoot.get());
			tasks.push_back(new KwTask(
				KwCommand::copyFrom(obj), InitVersion, USER_INNER_INV_ID, NULL, ctx.getBaseDataVersion()
			));
		}
	}

	m_workbook->transformTasks(tasks, &ctx);

	std::vector<SsRect> out;

	BMP_PTR bmp = m_workbook->GetBMP();
	bool bDirty = false;
	for (auto it = tasks.begin(); it != tasks.end(); ++it)
	{
		KwTask* p = (*it);
		for (size_t i = 0; i < p->size(); ++i)
		{
			KwCommand* cmd = p->at(i);
			binary_wo::VarObj param = cmd->cast().get("param");
			WebID id = param.field_web_id("objSheet");
			RECT rc = VarReadRect(param);
			rc.top = std::max<INT32>(0, rc.top);
			rc.bottom = std::min<INT32>(bmp->cntRows-1, rc.bottom);
			rc.left = std::max<INT32>(0, rc.left);
			rc.right = std::min<INT32>(bmp->cntCols-1, rc.right);
			if (Rect_IsValid(rc))
				out.push_back(SsRect(id, rc));
		}

		bDirty = (bDirty || (p->size() != 1));
		if (!bDirty)
		{
			RECT lhs = VarReadRect(p->getSrcCommand()->cast().get("param"));
			RECT rhs = VarReadRect(p->front()->cast().get("param"));
			bDirty = !Rect_Equal(lhs, rhs);
		}

		delete p;
	}

	if (bDirty) sss = std::move(out);

	return bDirty;
}

void ExecDetail::BP_Update(binary_wo::BinWriter& ww, BP_Vec& vOld, BP_Vec& vNew)
{
	ASSERT(is_sorted(vOld.begin(), vOld.end(), BlockPoint::LessOp()));
	ASSERT(is_sorted(vNew.begin(), vNew.end(), BlockPoint::LessOp()));
	ASSERT(adjacent_find(
		vOld.begin(), vOld.end(), BlockPoint::KeyEq()) == vOld.end());
	ASSERT(adjacent_find(
		vNew.begin(), vNew.end(), BlockPoint::KeyEq()) == vNew.end());

	wo::IBlockPointsCache* bpc = GetCurUser()->getBlockPointCache();
	bw::Leave bpd = bw::enterStruct(&ww, "blockPointsDelta");

	BP_Vec res(std::max(vOld.size(), vNew.size()));
	std::vector<WoBlockPoint> wbpNew, wbpDiscard;

	auto ite = std::set_difference(vOld.begin(), vOld.end(),
		vNew.begin(), vNew.end(), res.begin(), BlockPoint::LessOp());
	{
		bw::Leave leave = bw::enterArray(&ww, "remove");
		for (auto it = res.begin(); it != ite; ++it)
		{
			(*it).Write(ww);
		}
		wbpDiscard.reserve(ite-res.begin());
		std::copy(res.begin(), ite, std::back_inserter(wbpDiscard));
	}

	ite = std::set_difference(vNew.begin(), vNew.end(),
		vOld.begin(), vOld.end(), res.begin(), BlockPoint::LessOp());
	{
		bw::Leave leave = bw::enterArray(&ww, "add");
		for (auto it = res.begin(); it != ite; ++it)
		{
			(*it).Write(ww);
		}
		wbpNew.reserve(ite-res.begin());
		std::copy(res.begin(), ite, std::back_inserter(wbpNew));
	}

	bpc->addDanglingOperation(kfc::tools::vtoa(wbpNew), wbpNew.size(),
		kfc::tools::vtoa(wbpDiscard), wbpDiscard.size());

	ww.addInt32Field(bpc->getDanglingVersion(nullptr), "version");
}

void ExecDetail::BP_Fill(KEtRevisionContext& ctx,
	binary_wo::BinWriter& ww, ISerialAcceptor* acpt, const wo::SsRect* arr, size_t len)
{
	if (len == 0) return;

	sa::Leave bpf = sa::enterArray(acpt, "blockPointsFill");

	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();

	std::unordered_map<WebID, std::vector<RECT>> cluster;
	for (size_t i = 0; i < len; ++i)
	{
		MergeFragment(arr[i].rc, cluster[arr[i].objSheet]);
	}

	int clusterCnt = 0, allBpFillCnt = 0;
	int beginFillSize = ww.getBufferWriteLength() + ww.getNameBufferWriteLength();
	for (auto it = cluster.begin(); it != cluster.end(); ++it)
	{
		INT idxSht = ctx.getSheetIndex(it->first);
		if (idxSht == INVALIDIDX) return;
		++clusterCnt;

		IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
		ks_stdptr<ISheet> sht; VS(bk->GetSheet(idxSht, &sht));
		ISheetStake* ss = sht->GetWoStake();

		AbsObject* shtMain = ctx.getSheetMain(idxSht);
		sa::Leave leave = sa::enterStruct(acpt, nullptr);

		acpt->addObject("objSheet", shtMain);

		sa::Leave inn = sa::enterArray(acpt, "areas");

		allBpFillCnt += it->second.size();
		for (size_t i = 0; i < it->second.size(); ++i)
		{
			const RECT& rc = it->second.at(i);
			sa::Leave areaLeave = sa::enterStruct(acpt, nullptr);
			sa::addRect(acpt, rc);
			WOVW(acpt->addKey("cells"));
			ss->exportCells(rc, acpt);
			WOVW(acpt->addKey("hyperlinksBlock"));
			_SerialHyperlink(idxSht, rc, acpt);
			WOVW(acpt->addKey("sparklinesBlock"));
			_SerialSparklines(idxSht, rc, acpt);
			WOVW(acpt->addKey("cellProtectionsBlock"));
			_SerialCellProtection(idxSht, rc, acpt);

			{
				// 缓存预览模式下，跟随block带出条件格式， conId/userId 都为空
				ks_stdptr<IKUserConn> spUser = acpt->getContext()->getUser();
				const WCHAR* conId = spUser->connID();
				const WCHAR* userId = spUser->userID();
				if(!xstrcmp(conId, __X("previewConn")) && !xstrcmp(userId, __X("")))
				{
					WOVW(acpt->addKey("cfBlock"));
					_SerialCellCondBlock(idxSht, rc, acpt);
				}
			}
		}
	}
	
	unsigned fillSize = ww.getBufferWriteLength() + ww.getNameBufferWriteLength() - beginFillSize;
	if (allBpFillCnt > 10 || fillSize > 1024 * 1024) 
	{
		WOLOG_INFO << "BP_Fill: clusterCnt: " << clusterCnt << ", allBpCnt: " << allBpFillCnt << ", fillSize: " << fillSize;
	}
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPFillBlocksCnt, allBpFillCnt);
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPFillBytes, fillSize);
}

bool ExecDetail::HasUnsupportSheetType() const
{
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	INT n = 0;
	VS(bk->GetSheetCount(&n));

	for (INT i = 0; i < n; ++i)
	{
		ks_stdptr<ISheet> spISheet;
		VS(bk->GetSheet(i, &spISheet));

		SHEETTYPE sheetType = stUnknown;
		spISheet->GetFullType(&sheetType);
		if (sheetType == stOldDashBoard || sheetType == stGrid_DB || 
			sheetType == stDashBoard || sheetType == stApp || sheetType == stWorkbench)
			return true;
	}
	return false;
}


bool ExecDetail::HasHyperlinkWithRun()
{
	class EnumHasLink : public IHyperlinksEnumAdaptor
	{
	public:
		bool doEnum(const RANGE& rg, HANDLE) //返回true终止枚举
		{
			m_hasLink = true;
			return m_hasLink;
		}

		bool m_hasLink = false;
	};

	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	INT n = 0; VS(bk->GetSheetCount(&n));
	
	EnumHasLink linkx;
	RANGE rg(bk->GetBMP());
	for (INT i = 0; i < n && !linkx.m_hasLink; ++i)
	{
		ks_stdptr<ISheet> sht;
		VS(bk->GetSheet(i, &sht));

		ks_stdptr<IUnknown> unk;
		sht->GetExtDataItem(edSheetHyperlinks, &unk);
		if (unk == nullptr) continue;
		
		ks_stdptr<IKHyperlinks> hls;
		VS(unk->QueryInterface(IID_IKHyperlinks, (void**)&hls));

		rg.SetSheets(i, i);
		hls->EnumHyperLinksWithRuns(rg, &linkx);
	}

	return linkx.m_hasLink;
}

void ExecDetail::_SerialHyperlink(IDX sheetId, const RECT& rc, ISerialAcceptor* acpt)
{
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISheet> ptrSheet; 
	VS(bk->GetSheet(sheetId, &ptrSheet));

	ks_stdptr<IUnknown> ptrUnk;
	ptrSheet->GetExtDataItem(edSheetHyperlinks, &ptrUnk);
	ASSERT(ptrUnk);
	ks_stdptr<IKHyperlinks> ptrHpLinks = ptrUnk;
	ASSERT(ptrHpLinks);
	ptrHpLinks->serialCellHyperlink(rc, acpt);
}


void ExecDetail::_SerialSparklines(IDX sheetId, const RECT& rc, ISerialAcceptor* acpt)
{
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISheet> ptrSheet; 
	VS(bk->GetSheet(sheetId, &ptrSheet));

	ks_stdptr<IUnknown> ptrUnk;
	ptrSheet->GetExtDataItem(edSheetSparklineGroups, &ptrUnk);
	ASSERT(ptrUnk);
	ks_stdptr<ICoreSparklineGroups> ptrSparklineGroups = ptrUnk;
	ASSERT(ptrSparklineGroups);
	ptrSparklineGroups->serialCellSparklines(rc, acpt);
}


void ExecDetail::_SerialCellProtection(IDX sheetId, const RECT& rc, ISerialAcceptor* acpt)
{
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISheet> ptrSheet; 
	VS(bk->GetSheet(sheetId, &ptrSheet));
	ptrSheet->GetCellProtection()->Serialize(rc, acpt);
}

void ExecDetail::_WriteDaaysChangeArray(binary_wo::BinWriter& bw)
{
	IKWorkbook *pWb = m_workbook->GetCoreWorkbook();
	if (!pWb)
		return;

	bw.beginArray("daaysChangeArray");
	int sheetCount = pWb->GetWorksheets()->GetSheetCount();
	for (int i = 0; i < sheetCount; ++i)
	{
		IKWorksheet *pWorksheet = pWb->GetWorksheets()->GetSheetItem(i);
		if (pWorksheet)
		{
			const WCHAR *pcswSheetName = NULL;
			pWorksheet->GetSheet()->GetName(&pcswSheetName);
			if ((i + 1) == sheetCount && 0 == xstrcmp(pcswSheetName, STR_CELL_IMAGE_SHEET_NAME))
				continue;

			bw.beginStruct();
			{
				OBJID sheetID = INVALIDOBJID;
				pWorksheet->GetSheet()->GetObjID(&sheetID);
				bw.addInt32Field(sheetID, "sheetId");
				bw.addStringField(pcswSheetName, "sheetName");
				bw.addInt32Field(pWorksheet->GetDaaysDataChangeVer(), "version");
			}
			bw.endStruct();
		}
	}
	bw.endArray();
}

void ExecDetail::_SerialCellCondBlock(IDX sheetIdx, const RECT& rc, ISerialAcceptor* acpt)
{
	RANGE range(m_workbook->GetBMP());
	range.SetSheetFromTo(sheetIdx);
	range.SetRowFromTo(rc.top, rc.bottom);
	range.SetColFromTo(rc.left, rc.right);
	if (!range.IsValid())
	{
		acpt->beginStruct();
		acpt->endStruct();
		return;
	}
	IBook* ptrBook = m_workbook->GetCoreWorkbook()->GetBook();
	acpt->beginStruct();
	{
		ApplyConditionalFormat(acpt, range, ptrBook);
		acpt->addInt32("sheetIdx", sheetIdx);
		acpt->addInt32("rowFrom", rc.top);
		acpt->addInt32("rowTo", rc.bottom);
		acpt->addInt32("colFrom", rc.left);
		acpt->addInt32("colTo", rc.right);
	}
	acpt->endStruct();
}

void ExecDetail::BP_Formula(
	KEtRevisionContext& ctx, std::vector<SsRect>& newRc, ISerialAcceptor* acpt)
{
	std::unordered_map<WebID, std::vector<RECT>> cluster;
	
	for (size_t i = 0; i < newRc.size(); ++i)
		MergeFragment(newRc[i].rc, cluster[newRc[i].objSheet]);

	for (auto it = cluster.begin(); it != cluster.end(); ++it)
	{
		INT idxSht = ctx.getSheetIndex(it->first);
		if (idxSht == INVALIDIDX) return;

		IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
		ks_stdptr<ISheet> sht; VS(bk->GetSheet(idxSht, &sht));
		ISheetStake* ss = sht->GetWoStake();

		for (size_t i = 0; i < it->second.size(); ++i)
		{
			const RECT& rc = it->second.at(i);
			ss->markFormulas(rc, &ctx);
		}
	}

	SerializeFmla(ctx, acpt);
}

static uint32_t GetCellCalcStatus(IBook *bk, IDX nSheet, ROW row, COL col)
{
	IBookStake *bks = bk->GetWoStake();
	return bks->GetImportrangeCellCalcSatus(nSheet, row, col);
}

bool ExecDetail::_SerialCell(KEtRevisionContext& ctx, BPS& bps, IBook* bk, CellPos& cellPos, ISerialAcceptor* acpt, bool &isVisibleCell)
{
	BlockPoint bp(ctx.getSheetMain(cellPos.sht)->objId(), 0, 0);
	bp.ResetByCell(cellPos.row, cellPos.col);
	if (bps.find(bp) == bps.end()) return false;

	ks_stdptr<IBookOp> op;
	VS(bk->GetOperator(&op));

	const_token_ptr pt = nullptr;
	HRESULT hr = op->GetCellValue(
		cellPos.sht, cellPos.row, cellPos.col, &pt);
	if (pt == nullptr && hr != S_OK) return false;

	sa::Leave itemLeave = sa::enterStruct(acpt, nullptr);

	WOVW(acpt->addObject("objSheet", ctx.getSheetMain(cellPos.sht)));
	WOVW(acpt->addInt32("row", cellPos.row));
	WOVW(acpt->addInt32("col", cellPos.col));
	
	isVisibleCell = false;
	ks_stdptr<ISheet> spSheet;
	bk->GetSheet(cellPos.sht, &spSheet);
	if (!ctx.getProtectionCtx()->isCellHidden(spSheet, cellPos.row, cellPos.col))
	{
		isVisibleCell = true;
		VERIFY(sa::addToken(acpt, "value", pt) == WO_OK);
		if (acpt != nullptr && op != nullptr)
		{
			DWORD flag;
			hr = op->GetCellNodeFlags(cellPos.sht, cellPos.row, cellPos.col, &flag);
			if (hr == S_OK) {
				WOVW(acpt->addInt32("cellFlags", flag));
			}
		}
		ks_stdptr<IRuns> spRuns;
		op->GetCellRuns(cellPos.sht, cellPos.row, cellPos.col, &spRuns);
		if (spRuns)
			VERIFY(wo::sa::addIRuns(acpt, "runs", spRuns) == WO_OK);

		IBookStake* bks = bk->GetWoStake();
		uint32_t calcStatus = 0;
		if (bks->HasImportrangeFuncs())
		{
			calcStatus = GetCellCalcStatus(bk, cellPos.sht, cellPos.row, cellPos.col);
		}
		if (calcStatus == 0)
		{
			calcStatus = bks->GetEAFCellCalcStatus(cellPos.sht, cellPos.row, cellPos.col);	
		}
		if (0 != calcStatus)
		{
			WOVW(acpt->addUint32("calcStatus", calcStatus));
		}
	}
	return true;
}

void ExecDetail::SerialFmlaRes(KEtRevisionContext& ctx, const BP_Vec& bpVec, IBook* bk, ISerialAcceptor* acpt)
{
	IBookStake* bks = bk->GetWoStake();
	struct CellAcpt : public ICellValueAcpt
	{
		STDPROC_(INT) Do(ROW row, COL col, const_token_ptr pt)
		{
			++serAllCellCnt;
			sa::Leave itemLeave = sa::enterStruct(acpt, nullptr);
			AbsObject* objSheet = workSheetObj;
			WOVW(acpt->addObject("objSheet", objSheet));
			WOVW(acpt->addInt32("row", row));
			WOVW(acpt->addInt32("col", col));

			ISheet* pSheet = workSheetObj->GetWorksheet()->GetSheet();
			if (!pCtx->getProtectionCtx()->isCellHidden(pSheet, row, col))
			{
				++serVisibleCellCnt;
				VERIFY(sa::addToken(acpt, "value", pt) == WO_OK);
				if (bk != nullptr) 
				{
					ks_stdptr<IBookOp> op;
					VS(bk->GetOperator(&op));
					if (op != nullptr) 
					{
						DWORD flag;
						HRESULT hr = op->GetCellNodeFlags(nSheet, row, col, &flag);
						if (hr == S_OK) 
						{
							WOVW(acpt->addInt32("cellFlags", flag));
						}
					}
				}
				
				uint32_t calcStatus = 0;
				if (hasImportrangeFuncs)
				{
					calcStatus = GetCellCalcStatus(pSheet->LeakBook(), nSheet, row, col);
					
				}
				if (calcStatus == 0)
				{
					IBookStake* bks = bk->GetWoStake();
					calcStatus = bks->GetEAFCellCalcStatus(nSheet, row, col);	
				}
				if (0 != calcStatus)
				{
					WOVW(acpt->addUint32("calcStatus", calcStatus));
				}
			}
			return 0;
		}

		IWorksheetObj* workSheetObj;
		ISerialAcceptor* acpt;
		KEtRevisionContext* pCtx;
		IBook* bk;
		IDX nSheet = INVALIDIDX;
		bool hasImportrangeFuncs = false;
		int serVisibleCellCnt = 0;
		int serAllCellCnt = 0;
	};

	CellAcpt cellAcpt;
	cellAcpt.acpt = acpt;
	cellAcpt.pCtx = &ctx;
	cellAcpt.bk = bk;
	cellAcpt.hasImportrangeFuncs = bks->HasImportrangeFuncs();
	

	for (auto it = bpVec.begin(); it != bpVec.end(); ++it)
	{
		RECT rc = it->GetRect();
		IDX iSheet = ctx.getSheetIndex(it->objSheet);
		cellAcpt.nSheet = iSheet;
		cellAcpt.workSheetObj = ctx.getSheetMain(iSheet);

		RANGE rg = Rect2Range(rc, iSheet, bk->GetBMP());
		bks->EnumFmlaRes(rg, &cellAcpt);
	}
	
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPCalcCellsAllCellCnt, cellAcpt.serAllCellCnt);
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPCalcCellsVisibleCellCnt, cellAcpt.serVisibleCellCnt);
}

void ExecDetail::BP_UpdateSparklineRefArea(
	KEtRevisionContext& ctx, binary_wo::BinWriter& ww, 
	const BP_Vec& bpVec, std::vector<SsRect>& fresh)
{
	if (!m_root.has("sparklineRefAreaVer")) 
	{
		return;
	}

	BPS bps;   // 构造被监听的bp集合，提升排重效率
	std::unordered_set<WebID> sheetsOfBps; // 被监听的bp涉及的工作表集合
	for (auto it = bpVec.begin() ; it != bpVec.end() ; ++it)
	{
		bps.insert(*it);
		sheetsOfBps.insert(it->objSheet);
	}

	KSparklineRefAreaVersion::DirtyRefAreas dirtyRefAreas;
	WebInt clientVerID = m_root.field_int32("sparklineRefAreaVer");

	// 升级迷你图引用区域版本
	WebInt newClientVerID = _CollectDirtySparklineRefAreas(dirtyRefAreas, bps, sheetsOfBps, clientVerID, fresh);

	KSerialWrapBinWriter acpt(ww, &ctx);
	_SerialDirtySparklineRefAreas(&acpt, dirtyRefAreas, newClientVerID);
}

WebInt ExecDetail::_CollectDirtySparklineRefAreas(KSparklineRefAreaVersion::KSparklineRefAreaVersion::DirtyRefAreas& dirtyRefAreas,
											  	const BPS& bps,
												const std::unordered_set<WebID>& sheetsOfBps,
											 	WebInt clientVerID,
												std::vector<SsRect>& fresh)
{
	KEtVersionManager* mgr = static_cast<KEtVersionManager*>(m_workbook->getVersionMgr());
	KSparklineRefAreaVersion* curVersion = mgr->getCurSparklineRefAreaVersion();
	WebInt curVersionID = NULL != curVersion ? mgr->getCurSparklineRefAreaVersionID() : InitVersion;
	
	for (WebInt v = clientVerID + 1; v <= curVersionID; ++v)
	{
		KSparklineRefAreaVersion* version = static_cast<KSparklineRefAreaVersion*>(mgr->getSparklineRefAreaVersion(v));
		_CollectDirtySparklineRefAreasByVersion(dirtyRefAreas, version, bps, sheetsOfBps, fresh);
	}

	return curVersionID;
}

void ExecDetail::_CollectDirtySparklineRefAreasByVersion(KSparklineRefAreaVersion::KSparklineRefAreaVersion::DirtyRefAreas& dst,
														 KSparklineRefAreaVersion* version,
													     const BPS& bps,
														 const std::unordered_set<WebID>& sheetsOfBps,
														 std::vector<SsRect>& fresh)
{
	if (NULL == version)
		return;

	KSparklineRefAreaVersion::KSparklineRefAreaVersion::DirtyRefAreas& src = version->GetDirtySparklineRefAreas();

	KSparklineRefAreaVersion::KSparklineRefAreaVersion::DirtyRefAreas::iterator it;
	for (it = src.begin() ; it != src.end() ; ++it)
	{
		IWorksheetObj* pObjSheet = it->first;
		if (sheetsOfBps.find(pObjSheet->objId()) == sheetsOfBps.end())
			continue;

		KSparklineRefAreaVersion::HostSet* pHostSet = &(it->second);

		// 收集引用区域脏的迷你图
		SPARKLINE_SET& spkSet = pHostSet->m_SparklineSet;
		for (auto it2 = spkSet.begin() ; it2 != spkSet.end() ; ++it2)
		{
			CELL cell;
			ICoreSparkline* pAtom = (*it2);

			if (NULL == pAtom)
				continue;
				
			pAtom->GetLocation(cell);

			BlockPoint bp(pObjSheet->objId(), 0, 0);
			bp.ResetByCell(cell.row, cell.col);
			if (bps.find(bp) == bps.end()) 
				continue;

			RECT tmpRect = { cell.col, cell.row, cell.col, cell.row };
			SsRect tmp(pObjSheet->objId(), tmpRect);
			if (_IsInFreshBP(fresh, tmp))
				continue;

			// 如果脏标记在监控的block point范围内，才收集去序列化
			KSparklineRefAreaVersion::HostSet* pDstHostSet = dst.GetHostSet(pObjSheet, true);
			pDstHostSet->m_SparklineSet.insert(pAtom);
		}

		// 收集引用区域脏的迷你图组合
		SPARKLINE_GROUP_SET& grpSet = pHostSet->m_SparklineGroupSet;
		if (!grpSet.empty())
		{
			KSparklineRefAreaVersion::HostSet* pDstHostSet = dst.GetHostSet(pObjSheet, true);
			pDstHostSet->m_SparklineGroupSet.insert(grpSet.begin(), grpSet.end());
		}
	}
}

bool ExecDetail::_IsInFreshBP(std::vector<SsRect>& fresh, SsRect& target)
{
	for (auto it : fresh) 
	{
		if (it.objSheet != target.objSheet)
			continue;

		if (Rect_Contain(it.rc, target.rc))
			return true;
	}

	return false;
}

void ExecDetail::_SerialDirtySparklineRefAreas(ISerialAcceptor* acpt,
											   KSparklineRefAreaVersion::DirtyRefAreas& dirtyRefAreas,
											   WebInt newClientVer)
{
	if (dirtyRefAreas.empty())
		return;

	acpt->addInt32("sparklineRefAreaVer", newClientVer);

	KSparklineRefAreaVersion::DirtyRefAreas::iterator it;

	acpt->addKey("dirtySparklineRefAreas");
	acpt->beginArray();

	for (it = dirtyRefAreas.begin(); it != dirtyRefAreas.end(); ++it)
	{
		KSparklineRefAreaVersion::HostSet& hostSet = it->second;

		SPARKLINE_SET& sparklineSet = hostSet.m_SparklineSet;
		SPARKLINE_GROUP_SET& sparklineGroupSet = hostSet.m_SparklineGroupSet;

		acpt->beginStruct();
		acpt->addObject("objSheet", it->first);
		
		// 序列化脏的迷你图
		if (!sparklineSet.empty())
			_SerialDirtySparklineSet(sparklineSet, acpt);
		
		// 序列化脏的迷你图组合
		if (!sparklineGroupSet.empty())
			_SerialDirtySparklineGroupSet(sparklineGroupSet, acpt);
		
		acpt->endStruct();
	}

	acpt->endArray();
}

void ExecDetail::_SerialDirtySparklineGroupSet(SPARKLINE_GROUP_SET& sparklineGroupSet, ISerialAcceptor* acpt)
{
	if (NULL == acpt)
	{
		return;
	}
	
	acpt->addKey("sparklineGroupSet");
	acpt->beginArray();

	for (auto it = sparklineGroupSet.begin() ; it != sparklineGroupSet.end() ; ++it)
	{
		ICoreSparklineGroup* pAtom = (*it);
		pAtom->serialDateRangeValue(acpt, true);
	}

	acpt->endArray();
}

void ExecDetail::_SerialDirtySparklineSet(SPARKLINE_SET& sparklineSet, ISerialAcceptor* acpt)
{
	if (NULL == acpt)
	{
		return;
	}
	
	acpt->addKey("sparklineSet");
	acpt->beginArray();

	for (auto it = sparklineSet.begin() ; it != sparklineSet.end() ; ++it)
	{
		ICoreSparkline* pAtom = (*it);
		pAtom->serialDataSourceValue(acpt, true);
	}

	acpt->endArray();
}

void ExecDetail::BP_CalcCells(
	KEtRevisionContext& ctx, binary_wo::BinWriter& ww, const BP_Vec& bpVec)
{
	METRICS_TIME_BINSIZE("BP_CalcCells", this->m_workbook, ww, KU32MetricItem::kBPCalcCellsTime, KU32MetricItem::kBPCalcCellsBytes)
	
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	IBookStake* bks = bk->GetWoStake();
	ks_stdptr<IBookOp> op; VS(bk->GetOperator(&op));

	std::vector<const CellNode*> cns;
	bool bNormalFmlaAll = false, bAllEaf = false, bAllImportrange = false;
	WebInt fmlaResVer = InitVersion - 1;
	WebInt eafResVer = EafVersionInitialId - 1;
	BP_EafCellsFetch(ctx, cns, bAllEaf, eafResVer);
	BP_CalcCellsFetch(ctx, cns, bNormalFmlaAll, fmlaResVer);
	bool bAll = bNormalFmlaAll || bAllEaf || bAllImportrange;
	KSerialWrapBinWriter acpt(ww, &ctx);
	// 没有新的计算结果，这个版本也可以有，减少以后枚举版本的个数
	if(fmlaResVer >= InitVersion)
		acpt.addInt32("fmlaResVer", fmlaResVer);
	if (eafResVer >= EafVersionInitialId)
		acpt.addInt32("eafResVer", eafResVer);

	acpt.addUint32("recalcResVer", m_workbook->getImportrangeContext().getRecalsResVer());

	_WriteDaaysChangeArray(ww);

	sa::Leave leave = sa::enterArray(&acpt, "formulasResult");

	uint32_t recalcResVer = 0;
	if (m_root.has("recalcResVer"))
	{
		recalcResVer = m_root.field_uint32("recalcResVer");
	}

	if (recalcResVer != m_workbook->getImportrangeContext().getRecalsResVer())
	{
		bAll = true;
	}

	if (bAll)
	{
		m_workbook->coreMetric().setU32Metrics(KU32MetricItem::kBPCalcCellsIsAll, 1);
		return SerialFmlaRes(ctx, bpVec, bk, &acpt);
	}

	BPS bps;
	bps.insert(bpVec.begin(), bpVec.end());

	int serCellNodeCnt = 0, serVisibleCellCnt = 0;
	for (size_t i = 0; i < cns.size(); ++i)
	{
		const CellNode* cn = cns[i];
		wo::CellPos cellPos = {0};
		if (!bks->getCellNodePosWhenFmla(cn, &cellPos, NULL, NULL)) continue;

		bool isVisibleCell = false;
		bool isSer = _SerialCell(ctx, bps, bk, cellPos, &acpt, isVisibleCell);
		if (isSer)
		{
			++serCellNodeCnt;
			if (isVisibleCell)
				++serVisibleCellCnt;
		}
	}
	if ((serCellNodeCnt | serVisibleCellCnt) > 0)
		m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPCalcCellsBpCnt, bpVec.size());
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPCalcCellsAllCellCnt, serCellNodeCnt);
	m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kBPCalcCellsVisibleCellCnt, serVisibleCellCnt);
}

// 手动计算，没完成时只拿新增公式的结果（手动计算时，新添加的公式会马上计算好）, 不做计算扩散。
void ExecDetail::BP_CalcCellsFetchNoDiffusing(KEtRevisionContext& ctx, std::vector<const CellNode*>& cns, bool& bAll, WebInt& fmlaResVer)
{
	KwVersionManager* mgr = m_workbook->getVersionMgr();
	KwVersion* curVersion = mgr->getCurDataVersion();
	WebInt curTaskVersion = curVersion ? curVersion->getTaskIndex() : InitVersion;

	WebInt clientVer = m_root.field_int32("fmlaResVer");
	if (clientVer < InitVersion)
	{
		// 手动计算，保存引发重算时，会更新所有前端的fmlaResVer 从而拿全部最新结果
		bAll = true;
		fmlaResVer = curTaskVersion;
		return;
	}

	bool bHasRecalculateAll = false;
	std::vector<const UNDO_TAG*> vec;
	m_workbook->collectVersionUndoTag(&ctx, vec, &bHasRecalculateAll);
	
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	IBookStake* bks = bk->GetWoStake();
	bks->getCalcCells(vtoa(vec), vec.size(), &ctx);

	ctx.collectCachedCalc(cns);
}

// 分析是否存在即时计算，取即时计算的节点 (这些公式会马上计算好), 不做计算扩散。
void ExecDetail::BP_CalcCellsFetchInstantly(KEtRevisionContext& ctx, std::vector<const CellNode*>& cns)
{
	bool bHasRecalculateAll = false;
	std::vector<const UNDO_TAG*> vec;
	m_workbook->collectVersionUndoTag(&ctx, vec, &bHasRecalculateAll);
	
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	IBookStake* bks = bk->GetWoStake();
	bks->getInstantlyCalcCells(vtoa(vec), vec.size(), &ctx);

	ctx.collectCachedCalc(cns);
}

void ExecDetail::BP_CalcCellsFetchDiffusing(KEtRevisionContext& ctx, std::vector<const CellNode*>& cns, bool& bAll, WebInt& fmlaResVer)
{
	KwVersionManager* mgr = m_workbook->getVersionMgr();

	KwVersion* curVersion = mgr->getCurDataVersion();
	WebInt curTaskVersion = curVersion ? curVersion->getTaskIndex() : InitVersion;
	fmlaResVer = curTaskVersion;
	WebInt clientVer = m_root.field_int32("fmlaResVer");

	if(clientVer == curTaskVersion)
	{
		//前端已经拿到最新的结果了
		bAll = false;
		return;
	}

	if (clientVer < InitVersion || std::abs(curTaskVersion - clientVer) > 20)
	{
		// 前端当前计算结果是未完成的（初始化时计算是没完成的），拿全部最新结果
		// 当前端计算结果和当前版本相差太大时，拿全部最新结果
		bAll = true;
		return;
	}

	// webbug#34155 之前前端同步的那个版本已经因为undo被废弃了。undo_tag内存已经释放了。这里如果以后要优化，可以把undoTag改成引用计数的，确保undoTag有效，并做进一步处理。
	if (clientVer > InitVersion)
	{
		KEtVersion* version = static_cast<KEtVersion*>(mgr->getTaskVersion(clientVer));
		if (version && version->isObsolete())
		{
			bAll = true;
			return;
		}
	}

	WebInt taskFrom = clientVer + 1;
	WebInt taskTo = curTaskVersion;
	if (taskFrom > taskTo)
	{
		taskFrom = curTaskVersion + 1;
		taskTo = clientVer;
	}

	// 容错，前端传过来的 clientVer 也不知道对不对
	WebInt taskCnt = mgr->getTaskVersionCount();
	if (taskTo >= taskCnt || taskFrom < 0)
	{
		bAll = true;
		return;
	}

	std::vector<const UNDO_TAG*> vec;
	for (WebInt v = taskFrom; v <= taskTo; ++v)
	{
		KEtVersion* version = static_cast<KEtVersion*>(mgr->getTaskVersion(v));
		ASSERT(version != nullptr);
		if(version->getHasRecalculate())
		{
			bAll = true;
			return;
		}
		if (!version->isValidate() || version->isObsolete())
			continue;

		const UNDO_TAG* pTag = version->getUndoTag();
		if (pTag)
			vec.push_back(pTag);
	}

	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	IBookStake* bks = bk->GetWoStake();
	bks->diffusingCalcCells(vtoa(vec), vec.size(), &ctx);

	ctx.collectCachedCalc(cns);
	bAll = false;
}

void ExecDetail::BP_EafCellsFetch(KEtRevisionContext& ctx, std::vector<const CellNode*>& cns
	, bool& bAllEaf, WebInt& eafResVer)
{
	if (not m_root.has("eafResVer"))
	{
		return;
	}
	WebInt clientVer = m_root.field_int32("eafResVer");
	INT32 curEafResVer = m_workbook->getEafResVersion();
	eafResVer = curEafResVer;

	if (clientVer == curEafResVer)
	{
		// 前端已经拿到最新的结果了
		bAllEaf = false;
		return;
	}

	if (clientVer < EafVersionInitialId || std::abs(curEafResVer - clientVer) >= EafVersionVectorMaxLen)
	{
		// 前端当前计算结果是未完成的（初始化时计算是没完成的），拿全部最新结果
		// eaf的计算变更可能很快, 因此不考虑设置版本差的阈值
		// 但是eaf只会管理最近128次计算的结果(这个值需与etcore内KEafVersionManager中的定义
		// 保持一致. 考虑将常数提取到webbase/webet/et头文件中), 超出这个值, 则取全部结果
		bAllEaf = true;
		return;
	}

	WebInt verFrom = clientVer + 1;
	WebInt verTo = curEafResVer;
	// eaf版本只会增加, 因此正确的参数值不可能导致 verFrom > verTo
	if (verFrom > verTo)
	{
		bAllEaf = true;
		return;
	}

	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	IBookStake* bks = bk->GetWoStake();
	bks->diffuseEafCell(verFrom, verTo, &ctx);
	ctx.collectCachedCalc(cns);
	bAllEaf = false;
}

void ExecDetail::BP_CalcCellsFetch(KEtRevisionContext& ctx, std::vector<const CellNode*>& cns, bool& bAll, WebInt& fmlaResVer)
{
	if (!m_root.has("fmlaResVer"))
	{
		bAll = false;
		return;
	}

	if (m_workbook->getCalcStatus() == KEtWorkbook::CalcStatus::finish)
	{
		BP_CalcCellsFetchDiffusing(ctx, cns, bAll, fmlaResVer);
	}
	else
	{
		ETCalculation calcMode = etCalculationAutomatic;
		m_workbook->GetCoreApp()->get_Calculation(&calcMode);
		if(calcMode != etCalculationAutomatic)
		{
			// 手动计算，没完成时只拿新增公式的结果。
			BP_CalcCellsFetchNoDiffusing(ctx, cns, bAll, fmlaResVer);
		}
		// 自动重算时，如果计算没完成，结果不同步，等后面完成了合并到一起
		else
		{
			// 自动计算中的"即时计算", 也需要取结果. 
			// 手动计算下新增/修改的公式全部都是即时计算的, 范围上包含自动即时计算
			BP_CalcCellsFetchInstantly(ctx, cns);
		}
	}
}

ExecDetail::BP_Vec ExecDetail::BP_Collect(const BP_Cluser& x, const BP_Cluser& y)
{
	typedef std::pair<BlockPoint, std::vector<RECT>> BVR;

	auto func = [](const BVR& x)->BlockPoint{return x.first;};

	BP_Vec monitor;
	std::transform(x.begin(), x.end(), std::back_inserter(monitor), func);
	std::transform(y.begin(), y.end(), std::back_inserter(monitor), func);
	return std::move(monitor);
}

void ExecDetail::MonitorRange_Proc(KEtRevisionContext& ctx, binary_wo::BinWriter& ww)
{
	if (!m_root.has("monitorRangesWhenInit"))
		return;

	KwTasks tasks;
	MonitorRange_BuildTasks(tasks, ctx);

	m_workbook->transformTasks(tasks, &ctx);

	MonitorRange_ApplyTasks(tasks, ctx, ww);
}

void ExecDetail::MonitorRange_BuildTasks(KwTasks& tasks, KEtRevisionContext& ctx)
{
	binary_wo::VarObj objMr = m_root.get("monitorRangesWhenInit");
	AbsObject* objSheet = ctx.getSheetMain(
		ctx.getSheetIndex(objMr.field_web_id("objSheet")));
	if (objSheet == NULL)
	{
		return;
	}
	binary_wo::VarObj objItems = objMr.get("items");

	for (int i = 0, in = objItems.arrayLength(); i < in; ++i)
	{
		binary_wo::VarObj objItem = objItems.at(i);
		binary_wo::VarObj objValue = objItem.get("value");

		for (int j = 0, jn = objValue.arrayLength(); j < jn; ++j)
		{
			KSerialToVarObj varBuilder;
			varBuilder.addString("name", __X("range.monitor"));

			sa::Leave paramLeave = sa::enterStruct(&varBuilder, "param");

			varBuilder.addString("key", objItem.field_str("key"));
			varBuilder.addObject("objSheet", objSheet);
			sa::addRect(&varBuilder, VarReadRect(objValue.at(j)));

			binary_wo::VarObjRoot objNew(varBuilder.buildVarObj());
			tasks.push_back(new KwTask(
				KwCommand::copyFrom(objNew.cast()), InitVersion, USER_INNER_INV_ID, NULL, ctx.getBaseDataVersion()
			));
		}
	}
}

void ExecDetail::MonitorRange_ApplyTasks(
	KwTasks& tasks, KEtRevisionContext& ctx, binary_wo::BinWriter& ww)
{
	binary_wo::VarObj objMr = m_root.get("monitorRangesWhenInit");
	WebID objID = objMr.field_web_id("objSheet");

	std::unordered_map<ks_wstring, std::vector<RECT>, StrHasher> cluster;
	for (auto it = tasks.begin(); it != tasks.end(); ++it)
	{
		KwTask* p = (*it);
		for (size_t i = 0; i < p->size(); ++i)
		{
			binary_wo::VarObj param = p->at(i)->cast().get("param");
			RECT rcRead = VarReadRect(param);
			if (Rect_IsValid(rcRead)) {
				ks_wstring kk(param.field_str("key"));
				cluster[kk].push_back(VarReadRect(param));
			}
		}
		delete p;
	}
	if (cluster.empty()) return;

	bw::Leave leaveMr = bw::enterStruct(&ww, "monitorRanges");
	ww.addAbsObjIDField(objID, "objSheet");
	bw::Leave leaveArr = bw::enterArray(&ww, "items");
	for (auto it = cluster.begin(); it != cluster.end(); ++it)
	{
		bw::Leave leaveItem = bw::enterStruct(&ww, nullptr);

		ww.addStringField(it->first.c_str(), "key");
		bw::Leave leaveArr = bw::enterArray(&ww, "value");
		for (size_t i = 0; i < it->second.size(); ++i)
		{
			bw::Leave leave = bw::enterStruct(&ww, nullptr);
			BinWriterAddRect(ww, it->second[i]);
		}
	}
}

//---------------------------------------------------------------------------
::IKETUserConn* ExecDetail::GetCurUser()
{
	ks_stdptr<_Application> ptrApp = m_workbook->GetCoreApp();
	ks_castptr<IKETUserConn> uc = ptrApp->getUserConns()->getCurrentUserConn();
	return uc.get();
}

std::unique_ptr<KEtRevisionContext> ExecDetail::CreateContext()
{
	KEtWorkbook* wb = m_workbook;
	KEtRevisionContext * p = new KEtRevisionContext(wb);
	if (!m_traceId.empty())
		p->setTraceId(m_traceId);
	return std::unique_ptr<KEtRevisionContext>(p);
}

void ExecDetail::FillError(WebInt errCode)
{
	binary_wo::BinWriter binWriter;
	binWriter.addKey("error");
	binWriter.addInt32(errCode);

	FillCallbackIds(binWriter);

	FillOut(binWriter, msgType_Exec);
}

void freeWebSlice(WebSlice& slice)
{
	if (slice.data != NULL)
		free(slice.data);
}

void ExecDetail::FillOut(IWebSlice* signalSlice, MsgType type)
{
	if (m_extArgs.isServerLocalExec)
		return;
	
	if (gs_callback->signal)
	{
		wo::util::SlowCallTimeStat callTime("FillOut", 100, [this](unsigned int ms) {
			if (this->m_workbook)
				this->m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kSignalTime, ms);
		});
		
		const char* msgType = getMsgTypeName(type);
		gs_callback->signal(m_connID, msgType, signalSlice->Get());

		if (type != msgType_Exec)
		{
			UpdateSignalSize(signalSlice->Get()->size);
		}
		
		if (IsUseSeq())
		{
			m_signalSliceType = msgType;
			m_spSignalSlice = signalSlice;
		}
		
		onFillOut(signalSlice->Get()->size, type, msgType, signalSlice);
	}
	FillOutPic();
}

void ExecDetail::FillOut(binary_wo::BinWriter& ww, MsgType type)
{
	if (m_extArgs.isServerLocalExec)
		return;

	if (gs_callback->signal) {
		if (m_workbook)
			m_workbook->coreMetric().addMetric(m_workbook, ww, gs_procType);
		wo::util::SlowCallTimeStat callTime("FillOut", 100, [this](unsigned int ms) {
			if (this->m_workbook)
				this->m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kSignalTime, ms);
		});

		const char* msgType = getMsgTypeName(type);
		binary_wo::BinWriter::StreamHolder shbt = ww.buildStream();
		WebSlice slice = {shbt.get(), ww.writeLength()};
		gs_callback->signal(m_connID, msgType, &slice);
		if (type != msgType_Exec)
		{
			UpdateSignalSize(slice.size);
		}

		ks_stdptr<IWebSlice> spSlice;
		if (IsUseSeq())
		{
			m_signalSliceType = msgType;
			m_spSignalSlice.attach(KS_NEW(KWebSlice));
			m_spSignalSlice->Attach(shbt.detach(), slice.size);
			spSlice = m_spSignalSlice;
		}
		else
		{
			if (m_workbook && m_workbook->signalMetric().isNeedMetrics(slice.size, type, gs_procType))
			{
				spSlice.attach(KS_NEW(KWebSlice));
				spSlice->Attach(shbt.detach(), slice.size);
			}
		}
		
		onFillOut(slice.size, type, msgType, spSlice);
	}
	FillOutPic();
}

void ExecDetail::onFillOut(WebSize sliceSize, MsgType type, const char * msgType, IWebSlice *pSlice)
{
	if (!m_workbook)
		return;
	
	QString qMsgType = msgType ? QString::fromUtf8(msgType) : QString();
	bool hasDbViewsCollectInfo = m_workbook->collectInfo().hasDbViewsCollectInfo();
#ifndef _DEBUG
	if (sliceSize > 1024 * 1024)
#endif
	{
		wo::WoFileIncludeCollector collector(m_workbook, __X("behaviour_fill_out_size"), sliceSize);
		if (hasDbViewsCollectInfo)
		{
			ks_wstring info = m_workbook->collectInfo().getSingalDbViewsCollectInfo();
			collector.addName1(info);
			WOLOG_INFO << "signal dbViewsInfo: " << info;
		}
		
		if (msgType)
		{
			WOLOG_INFO << "signal size: " << sliceSize << ", msgType: " << msgType;
			collector.addCmdName(krt::utf16(qMsgType));
		}
		else
		{
			WOLOG_INFO << "signal size: " << sliceSize;
		}
		collector.addCount2(m_workbook->coreMetric().isReinit());
		collector.collect();
	}
	if (hasDbViewsCollectInfo)
		m_workbook->collectInfo().resetDbViewsInfo();
	
	m_workbook->coreMetric().setWstrMetrics(KWStrMetricItem::kSignalMsgType, krt::utf16(qMsgType));
	m_workbook->coreMetric().set64Metrics(K64MetricItem::kSignalSize, sliceSize);
	
	if (pSlice)
	{
		m_workbook->signalMetric().collectMetricData(pSlice, type, gs_procType, m_workbook->coreMetric().traceId().c_str());
	}
}

void  ExecDetail::FillOutPic() {
	if (gs_callback->uploadImage) {
		WebPicData* picDatas = NULL;
		WebInt size = m_workbook->getPicDatas(&picDatas);
		for (WebInt i = 0; i < size; i++)
		{
			WebSlice slice = {picDatas[i].btPic, picDatas[i].btLenPic};
			gs_callback->uploadImage(picDatas[i].strSha1, NULL, &slice, nullptr);
		}
		m_workbook->deletePicDatas(size, picDatas);
	}
}

void ExecDetail::FillOther()
{	
	if (m_extArgs.isServerLocalExec)
		return;

	if (gs_callback->broadcast) {
		binary_wo::BinWriter ww;
		m_workbook->SerialVerStatusInfo(ww);
		EtTaskExecutor* te = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
		binary_wo::VarObj res = te->GetResForOthers();
		if(res.getVar()){
			ww.addKey("res");
			KSerialWrapBinWriter wbw(ww, NULL);
			res.serialContent(&wbw);
			te->ResetResForOthers();
		}

		m_workbook->BroadcastChanged(ww, m_connID);
	}
}

constexpr const char* s_emptyUser = "";

void ExecDetail::FillLog(KEtRevisionContext* ctx, KwTasks& tasks)
{
	if (m_extArgs.isServerLocalExec || !m_extArgs.bWriteCooperationRecord)
		return;

	wo::util::SlowCallTimeStat callTime("FillLog", 100, [this](unsigned int ms) {
		if (this->m_workbook)
			this->m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kCommitTime, ms);
	});
	if (tasks.size() == 1)
	{
        KwTask* pTask = tasks.front();
		if (pTask->empty())
			return;

        // 大部分task都只有一个command，所以这里简单点处理，仅有一个command时才判断是否不写协作记录
        if (pTask->size() == 1)
        {
            KwCommand* pCmd = pTask->front();
            if (!pCmd->isWriteCooperationRecord())
                return;
        }
	}

	if (gs_callback->commit)
	{
		if (ctx->isRealTransform() || tasks.size() > 1)
		{
			for (auto it = tasks.begin(); it != tasks.end(); it++)
			{
				// 避免提交空记录
				if ((*it)->empty())
					continue;

				binary_wo::BinWriter ww;
				KSerialWrapBinWriter acpt(ww, ctx);
				KwTasks taskCmt;
				taskCmt.push_back(*it);
				taskCmt.serialCommands(&acpt);
				binary_wo::BinWriter::StreamHolder shbt = ww.buildStream();
				if (shbt.get() != nullptr) {
					WebSlice slice = {shbt.get(), ww.writeLength()};
					const char* userID = m_userID == NULL ? s_emptyUser : m_userID;
					const binary_wo::VarObjRoot & apiRootLog = (*it)->getApiFillLog();
					binary_wo::VarObj apiLog = apiRootLog.cast();
					if (!apiRootLog.get() || !apiLog.has("dependCommitId") || !apiLog.has("scriptIds"))
					{
						gs_callback->commit(m_connID, userID, &slice);
					}
					else
					{
						int dependCommitId = apiLog.field_int32("dependCommitId");
						binary_wo::VarObj arr = apiLog.get("scriptIds");
						binary_wo::BinWriter ww;
						ww.beginArray("scriptIds");
						for (int i = 0; i < arr.arrayLength_s(); ++i)
						{
							ww.addString(arr.item_str(i));
						}
						ww.endArray();
						binary_wo::BinWriter::StreamHolder shbt = ww.buildStream();
						WebSlice scriptIdsSlice = {shbt.get(), ww.writeLength()};
						gs_callback->commitApi(m_connID, userID, &slice, &scriptIdsSlice, dependCommitId);
					}
				}
			}
		}
		else
		{
			FillLog(ctx);
		}
	}
}

void ExecDetail::FillLog(KEtRevisionContext* ctx)
{
	if (m_extArgs.isServerLocalExec)
		return;

	if (m_slice && m_connID && m_slice->data != NULL) {
		const char* userID = m_userID == NULL ? s_emptyUser : m_userID;
		gs_callback->commit(m_connID, userID, m_slice);
	}
}

void ExecDetail::ClearApiCommandBeforeCommit(KwTasks& tasks)
{
	for (auto &iter : tasks)
	{
		KwTask& task = *iter;
		for (auto command = task.begin(); command != task.end();)
		{
			KwCommand* cmd = *command;
			ks_wstring name = cmd->cast().get("name").value_str();
			if (name == __X("http.et.ExecuteApi"))
				command = task.erase(command);
			else
				command++;
		}
	}
}


void ExecDetail::SerializeFmla(
	KEtRevisionContext& ctx, ISerialAcceptor* acpt)
{
	if (!m_workbook->IsExportFmla()) return;

	KBinSizeU32Mc<ISerialAcceptor> mcBinSize(&m_workbook->coreMetric(), acpt, KU32MetricItem::kSerialFmlaKb);

	std::vector<const CellNode*> cns;
	ctx.collectFmla(cns);

	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	IBookStake* bks = bk->GetWoStake();
	ks_stdptr<IBookOp> op; VS(bk->GetOperator(&op));

	
	FormulaIdxGenerator fmlaIdxGen;
	{
		int allCellCnt = 0, visibleCellCnt = 0;
		sa::Leave leave = sa::enterArray(acpt, "formulas");
		auto func = [bks, bk, op, &ctx, &acpt, &fmlaIdxGen, &allCellCnt, &visibleCellCnt](const CellNode* cn){
			wo::CellPos cellPos = {0};
			ITokenVectorPersist* fmla = NULL;
			AbsObject* pFmlaNode = NULL;
			if (!bks->getCellNodePosWhenFmla(cn, &cellPos, &fmla, &pFmlaNode)) return;

			sa::Leave leave = sa::enterStruct(acpt, nullptr);
			acpt->addObject("objSheet", ctx.getSheetMain(cellPos.sht));
			acpt->addInt32("row", cellPos.row);
			acpt->addInt32("col", cellPos.col);

			++allCellCnt;
			ks_stdptr<ISheet> spSheet;
			bk->GetSheet(cellPos.sht, &spSheet);
			if (!ctx.getProtectionCtx()->isCellHidden(spSheet, cellPos.row, cellPos.col))
			{
				++visibleCellCnt;
				if (pFmlaNode)
					acpt->addObject("value", pFmlaNode);
				else
					acpt->addUint32("idx", fmlaIdxGen.gainIdx(fmla));
			}
		};
		std::for_each(cns.begin(), cns.end(), func);
		
		m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kSerialFmlAllCellCnt, allCellCnt);
		m_workbook->coreMetric().addU32Metrics(KU32MetricItem::kSerialFmlVisibleCellCnt, visibleCellCnt);
	}
	{
		sa::Leave leave = sa::enterArray(acpt, "formulasList");
		for (size_t i = 0; i < fmlaIdxGen.size(); ++i)
			sa::addFormular(acpt, NULL, fmlaIdxGen.at(i));
	}
}

void ExecDetail::BPInit_SerialHyperlinks(KEtRevisionContext& ctx, ISerialAcceptor* acpt, std::vector<BlockPoint>& vbp)
{
	acpt->addKey("hyperlinks");
	acpt->beginArray();
	std::vector<BlockPoint>::iterator itor = vbp.begin();
	for (; itor != vbp.end(); itor++)
	{
		IDX sheetIdx = ctx.getSheetIndex(itor->objSheet);
		RECT rc = itor->GetRect();
		acpt->beginStruct();
			AbsObject* objSheetMain = ctx.getSheetMain(sheetIdx);
			if (objSheetMain == nullptr)
			{
				WO_LOG_X(ctx.getLogger(), WO_LOG_ERROR, "sheet object null");
				continue;
			}
			acpt->addObject("objSheet", objSheetMain);
			acpt->addKey("hyperlinksBlock");
			_SerialHyperlink(sheetIdx, rc, acpt);
		acpt->endStruct();
	}
	acpt->endArray();
}

void ExecDetail::BPInit_SerialSparklines(KEtRevisionContext& ctx, ISerialAcceptor* acpt, std::vector<BlockPoint>& vbp)
{
	acpt->addKey("sparklines");
	acpt->beginArray();
	std::vector<BlockPoint>::iterator itor = vbp.begin();
	for (; itor != vbp.end(); itor++)
	{
		IDX sheetIdx = ctx.getSheetIndex(itor->objSheet);
		RECT rc = itor->GetRect();
		acpt->beginStruct();
			AbsObject* objSheetMain = ctx.getSheetMain(sheetIdx);
			if (objSheetMain == nullptr)
			{
				WO_LOG_X(ctx.getLogger(), WO_LOG_ERROR, "sheet object null");
				continue;
			}
			acpt->addObject("objSheet", objSheetMain);
			acpt->addKey("sparklinesBlock");
			_SerialSparklines(sheetIdx, rc, acpt);
		acpt->endStruct();
	}
	acpt->endArray();
}

void ExecDetail::BPInit_SerialCellProtection(KEtRevisionContext& ctx, ISerialAcceptor* acpt, std::vector<BlockPoint>& vbp)
{
	acpt->addKey("cellProtections");
	acpt->beginArray();
	std::vector<BlockPoint>::iterator itor = vbp.begin();
	for (; itor != vbp.end(); itor++)
	{
		IDX sheetIdx = ctx.getSheetIndex(itor->objSheet);
		RECT rc = itor->GetRect();
		acpt->beginStruct();
			AbsObject* objSheetMain = ctx.getSheetMain(sheetIdx);
			if (objSheetMain == nullptr)
			{
				WO_LOG_X(ctx.getLogger(), WO_LOG_ERROR, "sheet object null");
				continue;
			}
			acpt->addObject("objSheet", objSheetMain);
			acpt->addKey("cellProtectionsBlock");
			_SerialCellProtection(sheetIdx, rc, acpt);
		acpt->endStruct();
	}
	acpt->endArray();
}

void ExecDetail::BPInit_SerialCellCondition(KEtRevisionContext& ctx, ISerialAcceptor* acpt, std::vector<BlockPoint>& vbp)
{
	// 缓存预览模式下，跟随block带出条件格式， conId/userId 都为空
	ks_stdptr<IKUserConn> spUser = acpt->getContext()->getUser();
	const WCHAR* conId = spUser->connID();
	const WCHAR* userId = spUser->userID();
	if(!xstrcmp(conId, __X("previewConn")) && !xstrcmp(userId, __X("")))
	{
		acpt->addKey("conditionFormat");
		acpt->beginArray();
		std::vector<BlockPoint>::iterator itor = vbp.begin();
		for (; itor != vbp.end(); itor++)
		{
			IDX sheetIdx = ctx.getSheetIndex(itor->objSheet);
			RECT rc = itor->GetRect();
			acpt->beginStruct();
			AbsObject* objSheetMain = ctx.getSheetMain(sheetIdx);
			if (objSheetMain == nullptr)
			{
				WO_LOG_X(ctx.getLogger(), WO_LOG_ERROR, "sheet object null");
				continue;
			}
			acpt->addObject("objSheet", objSheetMain);
			acpt->addKey("cfBlock");
			_SerialCellCondBlock(sheetIdx, rc, acpt);
			acpt->endStruct();
		}
		acpt->endArray();
	}
}

WebStr ExecDetail::getCmdName(const KwCommand* cmd)
{
	return cmd->cast().field_str("name");
}

void ExecDetail::Proc_ExtAppendInfo(KEtRevisionContext& ctx, binary_wo::BinWriter& bw)
{
	if (m_root.has("windowInfo"))
		Proc_ExtWindowInfo(ctx, bw);

	if (m_root.has("extendQueryCmds"))
	{
		m_workbook->GetQueryExecutor()->Query(m_root.get_s("extendQueryCmds"), &ctx, bw);
	}
}

void ExecDetail::Proc_ExtWindowInfo(KEtRevisionContext& ctx, binary_wo::BinWriter& bw)
{
	binary_wo::VarObj vWinInfo = m_root.get_s("windowInfo");
	int activeSheetIdx = ctx.getSheetIndex(vWinInfo.field_web_id("activeSheet"));
	IKWorkbook* pWb = m_workbook->GetCoreWorkbook();
	IKWorksheet* pSht = pWb->GetWorksheets()->GetSheetItem(activeSheetIdx);
	if (!pSht)
		return;

	ks_stdptr<etoldapi::_Worksheet> spSheet = pSht;
	if (!spSheet)
		return;

	spSheet->Activate();

	if (vWinInfo.has("activeSheetWindowInfo"))
	{
		ISheetWndInfo* pShtWndInfo = pSht->GetActiveWorksheetView()->GetSheetWndInfo();
		ASSERT(pShtWndInfo);
		binary_wo::VarObj vShtWinInfo = vWinInfo.get_s("activeSheetWindowInfo");
		binary_wo::VarObj vCellLT = vShtWinInfo.get_s("cellLeftTop");
		binary_wo::VarObj vFrozen = vShtWinInfo.get_s("frozen");
		if (!vCellLT.empty() && !vFrozen.empty())
		{
			BOOL bFrozen = vFrozen.value_bool();
			if (bFrozen == pSht->GetActiveWorksheetView()->GetFreeze())
			{
				CELL cell;
				cell.row = vCellLT.field_int32("row");
				cell.col = vCellLT.field_int32("col");

				if (bFrozen)
					pShtWndInfo->SetPaneLTCell(cell);
				else
					pShtWndInfo->SetLeftTopCell(cell);
			}
		}

		if (vShtWinInfo.has("zoomNormal"))
		{
			WORD zoomNormal = vShtWinInfo.field_int32("zoomNormal");
			if (pShtWndInfo->GetNormalZoom() != zoomNormal)
				pShtWndInfo->SetNormalZoom(zoomNormal);
		}
	}
}

void ExecDetail::SerializeSupBookIds(KEtRevisionContext& ctx, binary_wo::BinWriter& writer)
{
	//执行命令导致的重新初始化不导出
	if (m_bQueryOrCmd || !m_workbook->IsExportFmla())
		return;

	writer.addKey("supBooks");
	writer.beginArray();

	int sheetIdx = -1;
	ISheet* pSheet = util::getConnSharedSheet(m_workbook, &ctx);
	if (pSheet)
	{
		uint sheetId = pSheet->GetStId();
		m_workbook->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(sheetId, &sheetIdx);
	}

	ks_stdptr<ISupEditLinks> spEditlinks;
	getSupEditLinks(m_workbook, &spEditlinks);
	if(spEditlinks->BeginGetSourceName(sheetIdx) == S_OK) 
	{
		while (true)
		{
			ks_bstr fullName;
			if (spEditlinks->GetNextSourceName(&fullName) != S_OK)
				break;

			SUP_LINKS_INFO_STATUS slis = SLIS_Unknown;
			IDX iBook = -1;
			if (S_OK != spEditlinks->GetStatus(fullName, &slis, &iBook))
				continue;	
			writer.addInt16Field(iBook, NULL);
		}
	} 

	writer.endArray();
}

void ExecDetail::SerializeFileInfoCore(binary_wo::BinWriter& writer, KEtRevisionContext& ctx)
{
	writer.addKey("fileInfoCore");
	writer.beginStruct();

	const FileSaveInfo& fileInfo = m_workbook->GetFileSaveInfo();
	writer.addFloat64Field(fileInfo.size, "fileSize");
	writer.endStruct();
}

void ExecDetail::SerializeConfigCore(binary_wo::BinWriter& writer, KEtRevisionContext& ctx)
{
	IWoETSettings* pWoEtSettings = _kso_GetWoEtSettings();
	if (!pWoEtSettings) return;

	writer.addKey("featConfigCore");
	writer.beginStruct();
	bool bEnableCellMultiLink = pWoEtSettings->getSupportCellMultiLink();
	writer.addBoolField(bEnableCellMultiLink, "isEnableCellMultiLinkCore");
	writer.endStruct();
}

void ExecDetail::SerializeBookSetting(binary_wo::BinWriter& bw)
{
	auto pBook = m_workbook->GetCoreWorkbook()->GetBook();
	wo::IBookSetting* setting = pBook->GetWoStake()->getSetting();

	bw.beginStruct("woBookSetting");
	bw.addBoolField(setting->getIsFilterShared(), "isFilterShared");
	bw.addBoolField(setting->getHasForm(), "hasForm");
	bw.addBoolField(setting->isSubscriptionAutoUpdatePaused(), "isSubscriptionAutoUpdatePaused");

	ks_stdptr<IWorkspace> spWs;
	pBook->GetWorkspace(&spWs);
	ks_stdptr<IAppSettings> spAppSetting;
	spWs->GetAppSettings(&spAppSetting);

	SerializeCalcOption(bw, spAppSetting.get());

	PCWSTR strStyle = nullptr;
	switch (setting->getRefStyle())
	{
	case RS_A1: strStyle = __X("A1"); break;
	case RS_R1C1: strStyle = __X("R1C1"); break;
	case RS_SYSDEF: strStyle = __X("SYSDEF"); break;
	default: break;
	}

	if (strStyle != nullptr)
	{
		bw.addStringField(strStyle, "refStyle");
	}

	VARIANT_BOOL bReadOnly = VARIANT_FALSE;
	m_workbook->GetCoreWorkbook()->get_ReadOnly(&bReadOnly);
	if (VARIANT_FALSE != bReadOnly)
		bw.addBoolField(true, "serverReadOnly");

	bw.endStruct();
}

void ExecDetail::SerializeCalcOption(binary_wo::BinWriter& bw, IAppSettings* appSettings)
{
	bw.addBoolField(appSettings->GetCalcCtrlFlags() == ccfoManual, "manualCalc");
	bw.addBoolField(alg::BOOL2bool(appSettings->GetCalculateBeforeSave()), "calcBeforeSave");

	if (m_curUserConn != nullptr && m_curUserConn->getEndCoopVer() > 1.)
	{
		bw.beginStruct("iterateCalc");
		bw.addBoolField(alg::BOOL2bool(appSettings->GetEnableIteration()), "enable");
		bw.addInt32Field(appSettings->GetMaxIterations(), "maxCount");
		bw.addFloat64Field(appSettings->GetIterationMaxChange().Get(), "maxChange");
		bw.endStruct();
	}
	else
	{
		bw.addBoolField(alg::BOOL2bool(appSettings->GetEnableIteration()), "enableIterate");
	}
}

void ExecDetail::SerializeConnectingObjects(binary_wo::BinWriter& bw, KEtRevisionContext* ctx)
{
	KSerialWrapBinWriter acpt(bw, ctx);
	_Workbook* wb = m_workbook->GetCoreWorkbook();
	IBookStake* bks = wb->GetBook()->GetWoStake();
	{
		sa::Leave clusterLeave = sa::enterStruct(&acpt, "idCluster");
		acpt.addObject("book", wb->getDocumentObject());
		
		IWorkbookObj* wbObj = wb->GetWoObject();
		sa::Leave sheetsLeave = sa::enterArray(&acpt, "sheets");
		for (INT32 n = wbObj->getSheetCount(), i = 0; i < n; ++i)
		{
			ks_stdptr<ISheet> sht;
			VS(wb->GetBook()->GetSheet(i, &sht));
			const WCHAR* pcswSheetName = NULL;
			sht->GetName(&pcswSheetName);

			sa::Leave shtLeave = sa::enterStruct(&acpt, nullptr);
			AbsObject* shtObj = wbObj->getSheetItem(i);
			acpt.addObject("id", shtObj);
			if ((i+1) == n && 0 == xstrcmp(pcswSheetName, STR_CELL_IMAGE_SHEET_NAME))
			{
				acpt.addBool("cellImageSheet", true);
			}

			ISheetStake* ss = sht->GetWoStake();
			std::vector<AbsObject*> vecCF;
			VS(ss->exportCfItems(vecCF));
			{
				sa::Leave cfLeave = sa::enterArray(&acpt, "condFmts");
				for (auto it = vecCF.begin(); it != vecCF.end(); ++it)
				{
					acpt.addObject(nullptr, *it);
				}
			}
		}
	}
}

void ExecDetail::SerializeCustomLists(binary_wo::BinWriter& bw, KEtRevisionContext* ctx)
{
	ICustomLists* pCustomLists = m_workbook->GetCoreApp()->GetCustomLists();
	if (!pCustomLists)
		return;
	long cnt = 0;
	pCustomLists->GetCount(&cnt);
	if (0 == cnt)
		return;

	KSerialWrapBinWriter acpt(bw, ctx);
	sa::Leave clsLeave = sa::enterArray(&acpt, "customLists");
	for (long i = 0; i < cnt; ++i)
	{
		sa::Leave clLeave = sa::enterArray(&acpt, nullptr);
		ks_stdptr<ICustomList> spCL;
		pCustomLists->GetList(i, &spCL);
		long eachCnt = 0;
		spCL->GetCount(&eachCnt);
		for (long j = 0; j < eachCnt; ++j)
		{
			const WCHAR* pValue = nullptr;
			spCL->GetItem(j, &pValue);
			acpt.addString(nullptr, pValue);
		}
	}
}

void ExecDetail::SerializeIngoreParam(binary_wo::BinWriter& bw)
{
		bw.beginStruct("ignoreParam");

		//param的同级
		bw.beginArray("brothers");
		bw.addStringField(__X("dbSheetIdHistory"), nullptr);
		bw.endArray();

		//param的下级
		bw.beginArray("children");
			bw.addStringField(__X("rangePasteSrcRangeSheetName"), nullptr);
			bw.addStringField(__X("rangeSetValueByCustomListTextValue"), nullptr);
		bw.endArray();

		bw.endStruct();
}

void ExecDetail::SerializeSupBooksInfo(binary_wo::BinWriter& bw, KEtRevisionContext* ctx)
{
	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISupBooks> spSupBooks;
	pBook->GetSupBooks(&spSupBooks);

	INT nCnt = 0;
	spSupBooks->GetCount(&nCnt);
	if (0 == nCnt)
		return;

	KSerialWrapBinWriter acpt(bw, ctx);
	sa::Leave arrLeave = sa::enterArray(&acpt, "supBooksInfo");

	for (INT i = 1; i < nCnt; ++i)
	{
		ks_stdptr<ISupBook> spSupBook;
		spSupBooks->GetSupBook(i, &spSupBook);
		PCWSTR fileId = spSupBook->getNetFileId();
		if (!fileId)
			continue;

		const WCHAR* pName = nullptr;
		spSupBook->GetName(&pName);

		sa::Leave bookleave = sa::enterStruct(&acpt, nullptr);
		acpt.addString("fileId", fileId);
		acpt.addString("name", pName);
	}
}

void ExecDetail::_BinWriteRange(binary_wo::BinWriter& bw, const RANGE& rg, WebID objSheet)
{
	bw.beginStruct();
	bw.addFloat64Field(objSheet, "objSheet");
	bw.addInt32Field(rg.RowFrom(),"rowFrom");
	bw.addInt32Field(rg.RowTo(),"rowTo");
	bw.addInt32Field(rg.ColFrom(),"colFrom");
	bw.addInt32Field(rg.ColTo(),"colTo");
	bw.endStruct();
}


void ExecDetail::SerialPageSetRanges(IPageSetupData* spPageSetupData, ISheet* spISheet, WebID objSheet, binary_wo::BinWriter& bw)
{
	// PrintTitleRows
	RANGE rgPrintNames(spISheet->GetBMP());
	spPageSetupData->GetPrintTitleRows(rgPrintNames);
	bw.beginArray("printTitleRows");
	if(rgPrintNames.IsValid())
	{
		_BinWriteRange(bw, rgPrintNames, objSheet);
	}
	bw.endArray();

	//PrintTitleColumns
	spPageSetupData->GetPrintTitleColumns(rgPrintNames);
	bw.beginArray("printTitleCols");
	if(rgPrintNames.IsValid())
	{
		_BinWriteRange(bw, rgPrintNames, objSheet);
	}
	bw.endArray();

	// printArea
	ks_stdptr<IKRanges> spRanges;
	spPageSetupData->GetPrintAreas(&spRanges);
	uint rgsCount;
	spRanges->GetCount(&rgsCount);
	const RANGE* rgPrintArea = NULL;
	bw.beginArray("printArea");
	for (size_t i = 0; i < rgsCount; i++)
	{
		spRanges->GetItem(i, NULL, &rgPrintArea);
		if(rgPrintArea->IsValid())
		{
			_BinWriteRange(bw, *rgPrintArea, objSheet);
		}
	}
	bw.endArray();
}

void ExecDetail::SerialPageSetupData(KEtRevisionContext& ctx, binary_wo::BinWriter& bw)
{
	if(!m_root.has("pgstVerShtIdx"))
		return;
	binary_wo::VarObj webDataPgst = m_root.get_s("pgstVerShtIdx");
	WebID objSheet = webDataPgst.field_web_id("objSheet");
	IDX sheetIdx = ctx.getSheetIndex(objSheet);
	if(sheetIdx == INVALIDIDX)
		return;

    EnableProtectCompileFmla enableClFlmGuard;

	// 序列化页面设置数据前，先触发分页，更新zoom值
	ks_stdptr<IKWorksheet> spIWorkSheet;
	ks_stdptr<IKWorksheetView> spIKWorkShtView;
	spIWorkSheet = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	ASSERT(spIWorkSheet);
	spIKWorkShtView = spIWorkSheet->GetActiveWorksheetView();
	if (_kso_GetWoEtSettings()->IsEnableNewUpdateRender())
		m_workbook->updateRenderShape(spIWorkSheet);
	spIKWorkShtView->GetRenderPaginate()->Paginate(0);
	
	// 序列化页面设置数据
	{
		ks_stdptr<ISheet> spISheet;
		m_workbook->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spISheet);
		ks_stdptr<IPageSetupData> spPageSetupData;
		{
			ks_stdptr<IUnknown> spUnk;
			spISheet->GetExtDataItem(edSheetPageSetup, &spUnk);
			spPageSetupData = spUnk;
			ASSERT(spPageSetupData);
		}

		IWoPAGESETUP* pIWoPgStup = spPageSetupData->GetIWoPAGESETUP();	
		if(pIWoPgStup->GetWoPgStupVersion() != webDataPgst.field_uint32("pageSetVersion"))
		{
			bw.addFloat64Field(objSheet, "objSheetPageSt");
			pIWoPgStup->SerialWoPgStData(webDataPgst.field_uint32("pageSetVersion"), bw);
			SerialPageSetRanges(spPageSetupData, spISheet, objSheet, bw);
		}
	}

	// 序列化分页结果
	{
		if(!m_root.has("pageInfo"))
			return;
		binary_wo::VarObj webDataPgInfo = m_root.get_s("pageInfo");
		uint64 webPageInfoVer = webDataPgInfo.field_web_id("pageInfoVersion");
		uint64 serverPageInfoVer = spIKWorkShtView->GetRenderPaginate()->GetRenderSelfVersion();
		if(webPageInfoVer != serverPageInfoVer)
		{
			bw.beginStruct("serverPageInfo");
			bw.addFloat64Field(objSheet, "objSheet");
			bw.addFloat64Field(serverPageInfoVer, "pageInfoVersion");
			spIWorkSheet->GetPageInfo()->SerialPageInfo(bw);
			bw.endStruct();
		}
	}
}

void ExecDetail::SerialExclusiveRangeData(KEtRevisionContext& ctx, binary_wo::BinWriter& bw)
{
	INT32 sheetIdx = ctx.GetExclusiveRangeShtIdx();
	if (sheetIdx < 0)
		return;
	IKWorksheet* pWorksheet = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (nullptr == pWorksheet)
		return;

	ISheetProtection* pSheetProtection = pWorksheet->GetProtection();
	ExclusiveRangeMode mode = pSheetProtection->GetExclusiveRangeMode();
	if (mode == ermNone)
		return;
	KSerialWrapBinWriter acpt(bw, &ctx);
	acpt.addInt32("exclusiveSheetIdx", sheetIdx);
	acpt.addKey("exclusiveRanges");
	pSheetProtection->SerialExclusiveRanges(&acpt);
}

void ExecDetail::SerialBeautifyOperators(KEtRevisionContext& ctx, binary_wo::BinWriter& bw)
{
	if (!m_root.has("execBeautify"))
		return;

	std::vector<std::pair<ks_wstring, ks_wstring>>* pVecOperators = ctx.getBeautifyOperators();

	if (pVecOperators->empty())
		return;

	bw.beginArray("beautifyOperators");
	for (auto oper = pVecOperators->begin(); oper != pVecOperators->end(); ++oper)
	{
		bw.beginStruct();
		bw.addStringField(oper->first.c_str(), "cmdName");
		bw.addStringField(oper->second.c_str(), "params");
		bw.endStruct();
	}

	bw.endArray();
}

WebInt ExecDetail::ExecSubscribeCrossBooks()
{
	if (!gs_callback->notifyFile)
		return WO_FAIL;

	if (!m_extArgs.bNeedSubscriptionOp)
		return WO_OK;

	Subscription::Utils utils(m_workbook);
	return utils.ReSubscribeWithAsyncGetFile(m_workbook, m_userID);
}

WebInt ExecDetail::SetCrossBookAutoUpdateThreshold()
{
	Subscription::Utils utils(m_workbook);
	ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();
	if (pSubOp == nullptr)
		return WO_OK;

	pSubOp->SetAutoUpdateThreshold(m_extArgs.autoUpdateThreshold);
	return WO_OK;
}

WebInt ExecDetail::ResetCbSubscriptions()
{
	if (!m_extArgs.bNeedSubscriptionOp)
		return WO_OK;

	Subscription::Utils utils(m_workbook);
	ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();
	if (pSubOp == nullptr)
		return WO_OK;

	pSubOp->ResetCbSubscriptions();
	pSubOp->ResetUpdatedFiles();
	pSubOp->ResetUpdateSeq();
	return WO_OK;
}

WebInt ExecDetail::NotifyCrossBooksUpdate()
{
	if (!m_extArgs.bNeedSubscriptionOp)
		return WO_OK;

	m_workbook->NotifyCrossBooksUpdate();
	return WO_OK;
}

void ExecDetail::NotifyCustomListUpdate()
{
    m_workbook->NotifyCustomListUpdate();
}

PCWSTR ExecDetail::_GetExcludeUserColPermsChange(KEtRevisionContext* ctx)
{
	PCWSTR excludeId = NULL;
	if (!m_root.has("commands"))
		return excludeId;
	
	const VarObj cmds = m_root.get_s("commands");
	if (cmds.arrayLength_s() > 1)
		return excludeId;

	const VarObj cmd = cmds.at(0);
	const VarObj param = cmd.get_s("param");
	if (!param.has("objSheet"))
		return excludeId;
	
	WebID objSheet = param.field_web_id("objSheet");
	IDX idxSheet = ctx->getSheetIndex(objSheet);
	if (idxSheet == INVALIDIDX)
		return excludeId;

	ks_stdptr<ISheetProtection> pSheetProtection;
	IKWorksheet* pWorksheet = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(idxSheet);
	ks_castptr<_Worksheet> pApiWs = pWorksheet;
	if (pApiWs)
	{
		pSheetProtection = pApiWs->GetProtection();
	}

	if (pSheetProtection)
	{
		excludeId = pSheetProtection->GetMaster();
	}
	return excludeId;
}

void ExecDetail::UpdateUserColPermsStatus(KEtRevisionContext* ctx)
{
	PCWSTR excludeId = _GetExcludeUserColPermsChange(ctx);
	ctx->getProtectionCtx()->updateUserColPermsStatus(excludeId);
}

WebInt ExecDetail::OnNotify()
{
	switch (m_extArgs.notifyType)
	{
		case WoNotifySubscribeCb:
			return OnCbSubscribe();
		case WoNotifyUnSubscribeCb:
			return OnCbUnSubscribe();
		case WoNotifyUpdateCb:
			return OnCbUpdate();
		default:
			ASSERT(FALSE);
			return WO_FAIL;
	}
}

WebInt ExecDetail::RefreshInitVersion()
{
	int32 taskVer = m_root.field_int32("taskVer");
	int32 dataVer = m_root.field_int32("dataVer");
	if (m_curUserConn != nullptr)
	{
		m_curUserConn->setInitIndex(dataVer, taskVer);
	}
	return WO_OK;
}

WebInt ExecDetail::OnCbSubscribe()
{
	ASSERT(m_extArgs.fileId);
	WO_LOG_X(m_workbook->getLogger(), WO_LOG_INFO, QString("OnCbSubscribe: fileId: %1").arg(m_extArgs.fileId).toUtf8());

	if (secdoc::IsSecurityDoc(m_workbook->GetCoreWorkbook()))
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_INFO, QString("OnCbSubscribe: cancelled due to SecurityDoc").toUtf8());
		return WO_OK;
	}

	Subscription::Utils utils(m_workbook);
	ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();
	if (pSubOp == nullptr)
		return WO_OK;

	ks_string subscribeTask = m_extArgs.task;
	pSubOp->UnRegisterCbSubscription(m_extArgs.fileId, subscribeTask);
	if (m_root.has("ranges"))
	{
		binary_wo::VarObj ranges = m_root.get_s("ranges");
		for (int i = 0; i < ranges.arrayLength_s(); i++)
		{
			BMP_PTR pBmp = m_workbook->GetBMP();
			RANGE rg(pBmp);
			binary_wo::VarObj item = ranges.at_s(i);
			if(item.has("sheetName"))
			{
				ks_wstring shtName = item.field_str("sheetName");
				IDX sheetIdx = alg::STREF_INV_SHEET;
				ks_stdptr<IBook> ptrIBook = m_workbook->GetCoreWorkbook()->GetBook();
				HRESULT hr = ptrIBook->GetSheetIdxByName(shtName.c_str(), &sheetIdx);
				if(FAILED(hr)) continue;
				if (item.has("rowFrom"))
				{
					rg.SetSheetFromTo(item.field_int32("sheetFrom"), item.field_int32("sheetTo"));
					rg.SetRowFromTo(std::min(pBmp->cntRows - 1, item.field_int32("rowFrom")), std::min(pBmp->cntRows - 1, item.field_int32("rowTo")));
					rg.SetColFromTo(std::min(pBmp->cntCols - 1, item.field_int32("colFrom")), std::min(pBmp->cntCols - 1, item.field_int32("colTo")));
				}
				else
				{
					rg.SetSheetFromTo(sheetIdx, sheetIdx);
					rg.SetRowFromTo(0, pBmp->cntRows - 1);
					rg.SetColFromTo(0, pBmp->cntCols - 1);
				}
				
				pSubOp->RegisterCbSubscription(m_extArgs.fileId, rg, subscribeTask);
				continue;
			}

			rg.SetSheetFromTo(item.field_int32("sheetFrom"), item.field_int32("sheetTo"));
			rg.SetRowFromTo(std::min(pBmp->cntRows - 1 , item.field_int32("rowFrom")), std::min(pBmp->cntRows - 1 , item.field_int32("rowTo")));
			rg.SetColFromTo(std::min(pBmp->cntCols - 1, item.field_int32("colFrom")), std::min(pBmp->cntCols - 1, item.field_int32("colTo")));
			pSubOp->RegisterCbSubscription(m_extArgs.fileId, rg, subscribeTask);
		}
	}
	if (m_root.has("names"))
	{
		binary_wo::VarObj names = m_root.get_s("names");
		for (int i = 0; i < names.arrayLength_s(); i++)
		{
			INT iName = names.item_int32(i);
			pSubOp->RegisterCbSubscription(m_extArgs.fileId, iName, subscribeTask);
		}
	}
	return WO_OK;
}

WebInt ExecDetail::OnCbUnSubscribe()
{
	ASSERT(m_extArgs.fileId);
	WO_LOG_X(m_workbook->getLogger(), WO_LOG_INFO, QString("OnCbUnSubscribe: fileId: %1").arg(m_extArgs.fileId).toUtf8());

	Subscription::Utils utils(m_workbook);
	ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();
	if (pSubOp == nullptr)
		return WO_OK;

	pSubOp->UnRegisterCbSubscription(m_extArgs.fileId, m_extArgs.task);
	return WO_OK;
}

WebInt ExecDetail::OnCbUpdate()
{
	ASSERT(m_connID && m_extArgs.fileId);

	if (!gs_callback->signal)
		return WO_FAIL;

	WO_LOG_X(m_workbook->getLogger(), WO_LOG_INFO, QString("OnCbUpdate: fileId: %1").arg(m_extArgs.fileId).toUtf8());
	if(m_extArgs.task)
		return ParseThenSignal();
	else
		return CbReferTask();
}

WebInt ExecDetail::CbReferTask()
{
	Subscription::Utils utils(m_workbook);
	QString qsNotifyFileId = QString::fromUtf8(m_extArgs.fileId);
	INT nBook = utils.GetSupBookIndex(krt::utf16(qsNotifyFileId));
	if (nBook != alg::STREF_INV_BOOK)
	{
		binary_wo::BinWriter bw;
		KSerialWrapBinWriter acpt(bw, nullptr);
		acpt.addString("task", __X("cbRefers"));
		acpt.addString("fileId", krt::utf16(qsNotifyFileId));
		acpt.addInt32("nBook", nBook);

		acpt.addBool("isPaused", utils.IsAutoUpdatePaused());
		acpt.addBool("isForbidden", utils.IsAutoUpdateForbidden());

		if (m_root.has("updatedFiles"))
		{
			binary_wo::VarObj updatedFiles = m_root.get_s("updatedFiles");
			int nFiles = updatedFiles.arrayLength_s();
			if (nFiles > 0)
			{
				Subscription::Utils utils(m_workbook);
				ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();

				acpt.addKey("updatedFiles");
				acpt.beginArray();
				for (int i = 0; i < nFiles; i++)
					acpt.addString(nullptr, updatedFiles.item_str(i));
				acpt.endArray();
			}
		}

		binary_wo::BinWriter::StreamHolder sh = bw.buildStream();
		WebSlice slice = { sh.get(), bw.writeLength() };
		gs_callback->signal(m_connID, getMsgTypeName(msgType_subscription), &slice);
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_INFO, QString("OnCbUpdate: connID: %1, fileId: %2, nBook: %3").arg(m_connID).arg(m_extArgs.fileId).arg(nBook).toUtf8());

		return WO_OK;
	}
	return WO_FAIL;
}

WebInt ExecDetail::ParseThenSignal()
{
	ASSERT(m_extArgs.task);
	ASSERT(gs_callback->signal);

	binary_wo::VarObj updateInfos, updatedFiles;
	if(m_root.has("updatedInfo"))
		updateInfos = m_root.get_s("updatedInfo");
	
	if(m_root.has("updatedFiles"))
		updatedFiles = m_root.get_s("updatedFiles");
	
	if(strcmp(m_extArgs.task, "mergefile") == 0)
		return ParseMergefileTaskMsg(updateInfos, updatedFiles);

	return WO_OK;
}

WebInt ExecDetail::ParseMergefileTaskMsg(binary_wo::VarObj& updateInfo, binary_wo::VarObj& updatedFiles)
{
	if(updateInfo.empty())
	{
		binary_wo::BinWriter bw;
		KSerialWrapBinWriter acpt(bw, nullptr);
		acpt.addString("task", __X("mergeFile")); 
		acpt.addBool("mergeall", true);
		binary_wo::BinWriter::StreamHolder sh = bw.buildStream();
		WebSlice slice = { sh.get(), bw.writeLength() };
		gs_callback->broadcast(getMsgTypeName(msgType_subscription), &slice, nullptr);
		return WO_OK;
	}

	QString qsNotifyFileId = QString::fromUtf8(m_extArgs.fileId);
	std::vector<ks_wstring> dirtySheetNames;
	binary_wo::VarObj dirtyRg = updateInfo.get_s("dirtyRanges");
	for(int i = 0; i < dirtyRg.arrayLength(); i++)
	{
		binary_wo::VarObj item = dirtyRg.at_s(i);
		if(item.has("sheetName"))
			dirtySheetNames.push_back(item.field_str("sheetName"));
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;

	// 获取该book下所有合并任务，有给fileId的合并任务则需要更新, 如果所有任务都没该fileId，则取消订阅
	int sheetCnt = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetCount();
	std::vector<WebID> dirtyObjId;
	for(int i = 0; i < sheetCnt; i++)
	{
		ks_stdptr<ISheet> spISheet;
		m_workbook->GetCoreWorkbook()->GetBook()->GetSheet(i, &spISheet);
		ks_stdptr<IMergeFile> spMergefile;
		{
			ks_stdptr<IUnknown> spUnk;
			spISheet->GetExtDataItem(edSheetMergeFile, &spUnk);
			spMergefile = spUnk;
			ASSERT(spMergefile);
		}
		
		if(!spMergefile || !spMergefile->GetIsAutoRefresh())
			continue;

		for(int nameCnt = 0; nameCnt < dirtySheetNames.size(); nameCnt++)
		{
			QString dirtySheetName = QString::fromUtf16(dirtySheetNames[nameCnt].c_str());
			if(spMergefile->IsSourceData(krt::utf16(qsNotifyFileId), krt::utf16(dirtySheetName)))
			{
				WebID objId = ctx.getSheetMain(i)->objId();
				dirtyObjId.push_back(objId);
				break;
			}		
		}
	}

	if(dirtyObjId.empty()) return WO_OK;

	binary_wo::BinWriter bw;
	KSerialWrapBinWriter acpt(bw, nullptr);
	acpt.addString("task", __X("mergeFile"));
	acpt.addString("fileId", krt::utf16(qsNotifyFileId));
	acpt.addKey("dirtySheet");
	acpt.beginArray();
	for(auto objId:dirtyObjId)
		acpt.addFloat64(nullptr, objId);
	acpt.endArray();

	if (!updatedFiles.empty())
	{
		int nFiles = updatedFiles.arrayLength_s();
		if (nFiles > 0)
		{
			Subscription::Utils utils(m_workbook);
			ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();

			acpt.addKey("updatedFiles");
			acpt.beginArray();
			for (int i = 0; i < nFiles; i++)
				acpt.addString(nullptr, updatedFiles.item_str(i));
			acpt.endArray();
		}
	}
	binary_wo::BinWriter::StreamHolder sh = bw.buildStream();
	WebSlice slice = { sh.get(), bw.writeLength() };
	gs_callback->broadcast(getMsgTypeName(msgType_subscription), &slice, nullptr);
	return WO_OK;
}

WebInt ExecDetail::SetTriggerConf()
{
	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	if (pBook == nullptr)
		return WO_FAIL;

	ks_stdptr<IDbAutomations> spAutomations;
	pBook->GetExtDataItem(edBookDbAutomations, (IUnknown**)&spAutomations);
	if (spAutomations)
	{
		IDbAutomationConfig *pConfig = spAutomations->GetConfig();
		// 每个到期提醒自动任务允许有的最大定时任务
		if (m_root.has("cron_limit"))
			pConfig->SetMaxTimerTasks(m_root.field_int32("cron_limit"));
		// 到期提醒自动任务的最大值
		if (m_root.has("single_time_cron_limit"))
			pConfig->SetMaxDueDateAutomations(m_root.field_int32("single_time_cron_limit"));
		// 有到期提醒自动任务时允许单次批量操作的最大记录数
		if (m_root.has("once_paste_limit"))
			pConfig->SetMaxBatchRecords(m_root.field_int32("once_paste_limit"));
		// 服务端没有定时任务时, 清空内核已有的定时任务状态 (用于创建文件副本的场景)
		if (m_root.has("cron_exist") && !m_root.field_bool("cron_exist"))
			spAutomations->ResetTimerTasks();
	}

	// TODO: 修改后广播出去

	return WO_OK;
}

WebInt ExecDetail::OnBindAttachmentIdsDone()
{
	if (gs_callback && gs_callback->broadcast)
	{
		binary_wo::BinWriter bw;
		KSerialWrapBinWriter acpt(bw, nullptr);
		acpt.addString("srcFileId", m_root.field_str("srcFileId"));
		acpt.addBool("isReady", 0 == m_root.field_int32("code"));
		
		binary_wo::VarObj vecAttachmentIds = m_root.get_s("attachmentIds");
		acpt.addKey("attachmentIds");
		acpt.beginArray();
		for (int i = 0; i < vecAttachmentIds.arrayLength_s(); i++)
		{
			acpt.addString(nullptr, vecAttachmentIds.item_str(i));
		}
		acpt.endArray();

		binary_wo::BinWriter::StreamHolder sh = bw.buildStream();
		WebSlice slice = { sh.get(), bw.writeLength() };
		gs_callback->broadcast(getMsgTypeName(msgType_Attachments), &slice, nullptr);
		return WO_OK;
	}
	return WO_FAIL;
}

/**
 * !!! 注意，影响单元格查看权限
 * */
WebInt ExecDetail::PrepareCurUserConn()
{
	::IKETUserConn* etUserConn = GetCurUser();
	etUserConn->SetPreparedCurConn();

	if (!m_workbook->GetBMP()->bDbSheet && !m_workbook->GetBMP()->bKsheet)
		return WO_OK;

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	InitContext(ctx, true);

	if(!_appcore_GainDbSheetContext()->IsFilterDynamicInfo(m_workbook->GetCoreWorkbook()->GetBook()))
		return WO_OK;

	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();
	SCOPE_AutoResume<bool> scopeBool(m_bQueryOrCmd, true);
	
	KwTasks tasks;
	KwCommand* pCmd = new KwCommand;
	binary_wo::VarObj objCmd = pCmd->cast();
	objCmd.add_field_str("name", __X("dbsheet.prepareCurUser"));
	binary_wo::VarObj param = objCmd.add_field_struct("param");
	tasks.push_back(new KwTask(pCmd, InitVersion, USER_INNER_INV_ID, NULL, ctx.getBaseDataVersion()));
	m_workbook->execTasksDirect(tasks, &ctx);
	m_workbook->doClearJobAfterExecCmd();
	if (pExecutor->IsDirty())
	{
		pExecutor->SetDirty(false);
		m_bPrepareUser = true;
		return WO_OK;
	}
	WOLOG_ERROR << "PrepareCurUserConn failed !!!";
	// 权限更新失败，直接显示错误页面。
	return  WO_FAIL;
}

WebInt ExecDetail::ConvertCurUserFilterToAnyUserFilter()
{
    if (!m_workbook->GetBMP()->bDbSheet && !m_workbook->GetBMP()->bKsheet)
        return WO_OK;

    std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
    KEtRevisionContext& ctx = *ctxPtr;
    InitContext(ctx);

    EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
    pExecutor->ResetResForOthers();
    pExecutor->ResetResForSelf();
    SCOPE_AutoResume<bool> scopeBool(m_bQueryOrCmd, true);

    KwTasks tasks;
    KwCommand* pCmd = new KwCommand;
    binary_wo::VarObj objCmd = pCmd->cast();
    objCmd.add_field_str("name", __X("dbsheet.convertCurUserFilterToAnyUserFilter"));
    binary_wo::VarObj param = objCmd.add_field_struct("param");
    tasks.push_back(new KwTask(pCmd, InitVersion, USER_INNER_INV_ID, nullptr, ctx.getBaseDataVersion()));

    m_workbook->execTasksDirect(tasks, &ctx);
    BinWriter ww;
    m_workbook->SerialVerStatusInfo(ww);
    m_workbook->BroadcastChanged(ww, NULL);
    return WO_OK;
}

WebInt ExecDetail::UpdateDbDirty()
{
	if (!m_workbook->GetBMP()->bDbSheet && !m_workbook->GetBMP()->bKsheet)
		return WO_OK;

	bool dateChanged = false;
	QDate currentDate = QDate::currentDate();
	if (currentDate != g_lastDateForMonitorDateChange)
	{
		g_lastDateForMonitorDateChange = currentDate;
		WOLOG_INFO << "[CheckDateChange] date change detected";
		dateChanged = true;
	}
	ResetCbSubscriptions();
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	InitContext(ctx);

	IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
	bool needGenerateTask = false;
	bool needAdjustStatSheet = util::IsNeedAdjustStatSheet(m_workbook);
	needGenerateTask |= needAdjustStatSheet;

	if (!needGenerateTask && dateChanged)
	{
		// 日期改变时检查有没有仪表盘图表包含动态日期筛选，有的话执行task
		IKWorksheets* pWorksheets = m_workbook->GetCoreWorkbook()->GetWorksheets();
		int sheetCount = pWorksheets->GetSheetCount();
		for (int i = 0; i < sheetCount; ++i)
		{
			ISheet* pSheet = pWorksheets->GetSheetItem(i)->GetSheet();
			if (pSheet->IsDbDashBoardSheet())
			{
				ks_stdptr<IDBChartStatisticMgr> spChartStatisticMgr;
				DbSheet::GetDBChartStatisticMgr(pSheet, &spChartStatisticMgr);
				if (spChartStatisticMgr && spChartStatisticMgr->HasDynamicDateFilter())
				{
					needGenerateTask = true;
					break;
				}
			}
		}
	}
	if (!needGenerateTask && m_workbook->DbtCalcCellsNeedAddDirty())
	{
		size_t cells = DbSheet::CountCollectedFmlCells2DbSheet(pBook);
		if (cells > 0)
			needGenerateTask = true;
	}
	if (!needGenerateTask)
	{
		ks_stdptr<IDbSheetDirtyManager> spSheetDirtyMananger;
		pBook->GetExtDataItem(edBookDbDirtyManager, (IUnknown**)&spSheetDirtyMananger);
		if (spSheetDirtyMananger == nullptr || !spSheetDirtyMananger->HasDirtySheet())
			return WO_OK;

		class SheetStIdEnum : public ISheetStIdEnum
		{
		public:
			explicit SheetStIdEnum(IBook* pBook) : m_pBook(pBook) {}
			HRESULT Do(UINT sheetId) override
			{
				IDX sheetIdx = INVALIDIDX;
				m_pBook->STSheetToRTSheet(sheetId, &sheetIdx);
				if (sheetIdx == INVALIDIDX)
					return S_OK;
				ks_stdptr<ISheet> spSheet;
				m_pBook->GetSheet(sheetIdx, &spSheet);
				if (!spSheet)
					return S_OK;
				if (spSheet->IsDbDashBoardSheet())
				{
					ks_stdptr<IDBChartStatisticMgr> spChartStaticMgr;
					DbSheet::GetDBChartStatisticMgr(spSheet, &spChartStaticMgr);
					if (spChartStaticMgr && spChartStaticMgr->NeedUpdate())
						bNeedUpdate = true;
				}
				else if (spSheet->IsDbSheet())
				{
					ks_stdptr<IDBSheetViews> spDbSheetViews;
					DbSheet::GetDBSheetViews(spSheet.get(), &spDbSheetViews);
					if (spDbSheetViews && spDbSheetViews->NeedUpdate(DbSheetViewUpdateFlag::UpdateAll))
						bNeedUpdate = true;
				}
				return bNeedUpdate ? E_FAIL : S_OK;
			}
			bool bNeedUpdate = false;
		private:
			IBook* m_pBook = nullptr;
		};
		SheetStIdEnum sheetIdEnum(m_workbook->GetCoreWorkbook()->GetBook());
		// 枚举所有脏sheet, 判断是否需要更新
		// TODO: sheet标脏和用户访问视图都没有改变时, 记录下状态, 跳过枚举
		spSheetDirtyMananger->EnumDirtySheetId(&sheetIdEnum);
		if (!sheetIdEnum.bNeedUpdate)
			return WO_OK;

		needGenerateTask = true; // 存在需要更新的视图, 需要准备事务
	}

	if (not needGenerateTask)
		return WO_OK;

	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();
	SCOPE_AutoResume<bool> scopeBool(m_bQueryOrCmd, true);
	
	KwTasks tasks;
	KwCommand* pCmd = new KwCommand;
	binary_wo::VarObj objCmd = pCmd->cast();
	objCmd.add_field_str("name", __X("dbsheet.empty"));
	binary_wo::VarObj param = objCmd.add_field_struct("param");
	if (dateChanged)
		param.add_field_bool("dateChanged", dateChanged);
	param.add_field_bool("afterDbGroupby", needAdjustStatSheet);
	tasks.push_back(new KwTask(pCmd, InitVersion, USER_INNER_INV_ID, NULL, ctx.getBaseDataVersion()));

	util::DbCdcScope cdcScope(m_workbook);
	m_workbook->execTasksDirect(tasks, &ctx);

	NotifyCrossBooksUpdate();
	//Todo:在ExecCmd里面，是先ResetCbSubscriptions 再去NotifyCrossBooksUpdate的，但是在KetWorkbook::resumeCalculate这里，却没有“ResetCbSubscriptions 再去NotifyCrossBooksUpdate”
	//为了控制影响面，这里先NotifyCrossBooksUpdate(可能会把上一次的跨book区域的脏给继续带上)，确保本次已经notify了，再去清空跨book区域的脏，避免带到下次更新。
	ResetCbSubscriptions();

	BinWriter ww;
	m_workbook->SerialVerStatusInfo(ww);
	m_workbook->BroadcastChanged(ww, NULL);

	m_workbook->doClearJobAfterExecCmd();
	return WO_OK;
}

WebInt ExecDetail::GenTask4DynamicArrayFormulaResumeCalc()
{
	// 外部如果已创建 KEtRevisionContext 则此处不再创建
	std::unique_ptr<KEtRevisionContext> ctxPtr;
	IRevisionContext* pCtx = _kso_GetRevisionContext();
	if (nullptr == pCtx)
	{
		ctxPtr = CreateContext();
		InitContext(*ctxPtr);
	}
	KEtRevisionContext& ctx = ctxPtr ? *ctxPtr : *(static_cast<KEtRevisionContext*>(pCtx));

	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();
	SCOPE_AutoResume<bool> scopeBool(m_bQueryOrCmd, true);
	
	KwTasks tasks;
	KwCommand* pCmd = new KwCommand;
	binary_wo::VarObj objCmd = pCmd->cast();
	objCmd.add_field_str("name", __X("et.empty"));
	binary_wo::VarObj param = objCmd.add_field_struct("param");
	param.add_field_str("purpose", __X("dynamicArrayFmlaResumeCalc"));

	ks_stdptr<_Application> ptrApp = m_wbs->GetCoreApp();
	IKUserConns* userConns = ptrApp->getUserConns();
	if (userConns)
	{
		ks_castptr<IKUserConn> userConn = userConns->getUserConn4EmptyTask();
		tasks.push_back(new KwTask(pCmd, InitVersion, userConn->innerID(), userConn, ctx.getBaseDataVersion()));
		m_workbook->execTasksDirect(tasks, &ctx);
	}

	return WO_OK;
}

WebInt ExecDetail::CalcImportrangeOnOpen()
{
	if (m_workbook->GetBMP()->bDbSheet)
		return WO_OK;

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	InitContext(ctx);

	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();
	SCOPE_AutoResume<bool> scopeBool(m_bQueryOrCmd, true);

	KwTasks tasks;
	KwCommand* pCmd = new KwCommand;
	binary_wo::VarObj objCmd = pCmd->cast();
	objCmd.add_field_str("name", __X("book.initImportrangeCalc"));
	binary_wo::VarObj param = objCmd.add_field_struct("param");
	tasks.push_back(new KwTask(pCmd, InitVersion, USER_INNER_INV_ID, NULL, ctx.getBaseDataVersion()));

	m_workbook->execTasksDirect(tasks, &ctx);
	BinWriter ww;
	m_workbook->SerialVerStatusInfo(ww);
	m_workbook->BroadcastChanged(ww, NULL);

	m_workbook->doClearJobAfterExecCmd();
	return WO_OK;
}

WebInt ExecDetail::verifyAndPrepareUserConn()
{
	WebInt res = WO_OK;
	::IKETUserConn* etUserConn = GetCurUser();
	if (nullptr == etUserConn)
	{
		WOLOG_ERROR << "[PrepareUser] GetCurUser() returns nullptr!";
		return WO_FAIL;
	}

	// 于当前commit提交之时, 服务端在调用部分server api时以userID作为connID. 利用这一特性, 对这种场景下的 prepare user 做去重处理.
	// 当且进当userID和connID相同的场景, 使用标记位判断prepare与否, 已prepare的则不再次prepare
	// 其他场景则无条件prepare, 和原有逻辑保持相同
	bool needPrepare = true;
	PCWSTR connId = etUserConn->connID();
	PCWSTR userId = etUserConn->userID();
	if (0 == xstrcmp(connId, userId) && etUserConn->IsCurConnPrapared())
		needPrepare = false;
	if (needPrepare)
	{
		res = PrepareCurUserConn();
		if (res < 0)
			WOLOG_ERROR << "prepare user failed";
	}
	return res;
}

WebInt ExecDetail::OnAsyncGetCrossbookState(NetFileRes res, const char* url, const char* fileId)
{
	if (!url || !fileId)
	{
		WOLOG_INFO << "[cbrefers] OnAsyncGetCrossbookState, invalid fileId";
		return WO_FAIL;
	}

	WOLOG_INFO << "[cbrefers] OnAsyncGetCrossbookState fileId: " << fileId << ", url" << std::hash<std::string>{}(url);

	QString qFileId = QString::fromUtf8(fileId);
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	m_workbook->onGetNetDiskFileDone(ctx->getUser()->userID(), krt::utf16(qFileId), DownloadFileTask_CrossBook);

	ImportrangeContext& irCtx = m_workbook->getImportrangeContext();
	if (irCtx.downloadFileIdFiles.empty())
		irCtx.bCbReferInitDone = true;

	m_workbook->AfterCrossRefRecalculate();
	return WO_OK;
}

WebInt ExecDetail::AuthQueryDbCoHistories(const char *connID, const char *userID)
{
	if (!connID || *connID == '\0')
		return WebDbCoHistoriesPermission::WoCoHistoriesDeny;

	if (!userID || *userID == '\0')
		return WebDbCoHistoriesPermission::WoCoHistoriesDeny;

	ks_stdptr<_Application> ptrApp = m_workbook->GetCoreApp();
	IKUserConns* userConns = ptrApp->getUserConns();
	if (userConns) {
		UserConnArgs connArgs(m_extArgs.connFrom, m_extArgs.connScene, m_extArgs.connLife);
		userConns->setCurrentUserConn(connID, userID, userConnEndUnknown, 1.0, &connArgs);
	}
	std::unique_ptr<wo::KEtRevisionContext> ctx(new wo::KEtRevisionContext(m_workbook));

	auto factory = m_workbook->GetDbAuthQueryCoHistoriesHandlerFactory();
	if (!factory)
		return WebDbCoHistoriesPermission::WoCoHistoriesDeny;
	
	return factory->Handle(m_root);
}

WebInt ExecDetail::UpdateSupBooks()
{
	std::unique_ptr<wo::KEtRevisionContext> ctx(new wo::KEtRevisionContext(m_workbook));
	InitUrlSupBooks(ctx.get());
	return WO_OK;
}

WebInt ExecDetail::ExportInquirerData(UINT32 stId, ks_wstring cond)
{
	IBook* pBook = m_workbook->GetCoreWorkbook()->GetBook();
	if(!pBook)
	{
		WOLOG_ERROR << "[ExportInquirerData] pBook nullptr";
		return WO_FAIL;
	}

	ks_stdptr<Worksheets> spWorksheets = m_workbook->GetCoreWorkbook()->GetWorksheets();
	if(!spWorksheets)
		return E_FAIL;
    long sheetCount = 0;
    spWorksheets->get_Count(&sheetCount);
	IDX sheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(stId, &sheetIdx);
	if(sheetIdx < 0 || sheetIdx >= sheetCount)
	{
		WOLOG_ERROR << "[ExportInquirerData] sheetIdx < 0 || sheetIdx >= sheetCount error";
		return WO_FAIL;
	}

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if(!spSheet)
	{
		WOLOG_ERROR << "[ExportInquirerData] spSheet nullptr";
		return WO_FAIL;
	}

	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();

	WOLOG_INFO << "[ExportInquirerData] begin call helper.exportInquirData()!!";
	wo::InquirerHelper helper(m_workbook, spSheet, sheetIdx);
	QString html;
	HRESULT hr = helper.exportInquirData(cond, html);
	WOLOG_INFO << "[ExportInquirerData] end call helper.exportInquirData()!!!";
	
	BinWriter writer;;
	writer.addStringField(krt::utf16(html), "html");

	BinWriter::StreamHolder shbt = writer.buildStream();
	WebSlice slice = {shbt.get(), writer.writeLength()};
	gs_callback->signal(nullptr, wo::getMsgTypeName(wo::msgType_ExportInquirerData), &slice);

	return hr;
}

void ExecDetail::InitUrlSupBooks(KEtRevisionContext* ctx)
{
	if (m_workbook->GetCoreApp()->GetAppSettings()->GetDisableCalc())
		return;

	bool bInitMergeFileNetDisk = m_workbook->InitMergeFileNetDisk();
	bool bNotifyParent = bInitMergeFileNetDisk;
	if (gs_procType == WebProcTypeChild && bNotifyParent && m_connID && m_userID)
	{
		binary_wo::BinWriter bw;
		bw.addStringField(__X("updateCrossBookRef"), "operate");
		bw.addStringField(krt::utf16(QString::fromUtf8(m_userID)), "userID");
		bw.addStringField(krt::utf16(QString::fromUtf8(m_connID)), "connID");

		binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
		WebSlice input = {shbt.get(), bw.writeLength()};
		WebSlice output = {nullptr, 0};
		gs_callback->onSyncParentProc(&input, &output);
		gs_callback->onFreeWebSlice(&output);

		return;
	}

	if (bInitMergeFileNetDisk)
	{
		InitMergeFileAutoUpdate(ctx);
	}
}

void ExecDetail::InitMergeFileAutoUpdate(KEtRevisionContext* ctx)
{
	autoupdate_util::MergeFileUtil mergeFileUtil(m_workbook, m_userID);
	mergeFileUtil.PrepareForMergeTask();
}

void ExecDetail::SerialCbReferAutoUpdateState(KEtRevisionContext& ctx, binary_wo::BinWriter& bw)
{
	if(!m_root.has("cbReferInitTip"))
		return;
	ImportrangeContext& irCtx = m_workbook->getImportrangeContext();
	if (irCtx.bCbReferInitDone)
		bw.addBoolField(!IsSupBookStateOK(), "cbReferInitFailed");
}

bool ExecDetail::IsSupBookStateOK()
{
	ks_stdptr<ISupEditLinks> spEditlinks;
	getSupEditLinks(m_workbook, &spEditlinks);

	if (spEditlinks && spEditlinks->BeginGetSourceName() == S_OK)
	{
		while (true)
		{
			ks_bstr fullName;
			if (spEditlinks->GetNextSourceName(&fullName) != S_OK)
				break;

			SUP_LINKS_INFO_STATUS slis = SLIS_Unknown;
			IDX iBook = etexec::STREF_INV_BOOK;
			if (S_OK != spEditlinks->GetStatus(fullName, &slis, &iBook) || etexec::STREF_INV_BOOK == iBook)
				continue;

			if (slis != SLIS_OK)
				return false;
		}
	}
	return true;
}

WebInt ExecDetail::CrossRefRecalculate(RecalculateType type)
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	WOLOG_INFO << "[importrange] CrossRefRecalculate type: " << type;
	
	if (RecalculateType_CrossBook != type)
	{
		return WO_FAIL;
	}

	ImportrangeContext& irCtx = m_workbook->getImportrangeContext();
	irCtx.crossReclculating = true;
	irCtx.downloadUrlFiles.clear();
	
	m_workbook->UpdateCrossBookLinks(ctx.get());
	m_workbook->RecalculateImportRange(ctx.get());
	m_workbook->AfterCrossRefRecalculate();
	return WO_OK;
}

WebInt ExecDetail::OnAsyncGetMergeFileDone(NetFileRes res, const char* url, const char* fileId)
{
	if (!url || !fileId)
	{
		WOLOG_INFO << "[OnAsyncGetMergeFileDone] download file failed, invalid url or fileId";
		return WO_FAIL;
	}

	QString qFileId = QString::fromUtf8(fileId);
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	m_workbook->onGetNetDiskFileDone(ctx->getUser()->userID(), krt::utf16(qFileId), DownloadFileTask_MergeFile);

	ImportrangeContext& irCtx = m_workbook->getImportrangeContext();
	WOLOG_INFO 	<< "[OnAsyncGetMergeFileDone] res: " << res 
				<< ", fileid: " << fileId 
				<< ", downloadMergeFiles: " << irCtx.downloadMergeFiles.size()
				<< ", mergeFileReclculating: " << irCtx.mergeFileReclculating;

	if (irCtx.downloadMergeFiles.empty() && !irCtx.mergeFileReclculating)
	{
		EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
		pExecutor->ResetResForOthers();
		pExecutor->ResetResForSelf();

		ResetCbSubscriptions();
		KwTasks tasks;
		KwCommand* pCmd = new KwCommand;
		VarObj objCmd = pCmd->cast();
		objCmd.add_field_str("name", __X("book.doAllMergeTask"));
		VarObj param = objCmd.add_field_struct("param");
		tasks.push_back(new KwTask(pCmd, InitVersion, USER_INNER_INV_ID, NULL, ctx->getBaseDataVersion()));
		m_workbook->execTasksDirect(tasks, ctx.get());
		NotifyCrossBooksUpdate();

		BinWriter ww;
		m_workbook->SerialVerStatusInfo(ww);
		m_workbook->BroadcastChanged(ww, NULL);

		m_workbook->doClearJobAfterExecCmd();
	}
	return WO_OK;
}

WebInt ExecDetail::MergeFileTaskRecalculate(RecalculateType type)
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
	WOLOG_INFO << "[mergefile] Recalculate type: " << type;
	
	if (RecalculateType_MergeFile != type)
	{
		return WO_FAIL;
	}

	m_workbook->InitMergeFileNetDisk();
	ImportrangeContext& irCtx = m_workbook->getImportrangeContext();
	irCtx.mergeFileReclculating = true;
	irCtx.downloadMergeFiles.clear();

	std::vector<ks_wstring> waitingFiles;
	std::vector<ks_wstring> allFiles;
	autoupdate_util::MergeFileUtil mergeUtil(m_workbook, m_userID);
	mergeUtil.GetMergeFilesAndUnready(waitingFiles, allFiles);

	WOLOG_INFO << "[MergeFileTaskRecalculate] MergeFilesUnready, waitingFiles.size: " << waitingFiles.size();
	if (irCtx.mergeFileReclculating)
	{
		irCtx.mergeFileReclculating = false;
		bool complete = false;
		if (waitingFiles.empty())
		{
			mergeUtil.DoMergeAllMergeTask(true);
			mergeUtil.MergeFileSubscribe();
			complete = true;
		}

		std::vector<QByteArray> utf8FileIds;
		std::vector<const char*> fileIdsArray;
		wo::util::ToUtf8Array(allFiles, &utf8FileIds, &fileIdsArray);
		if (gs_callback->calculateReferenceDone)
		{
			gs_callback->calculateReferenceDone(
				fileIdsArray.empty() ? nullptr : fileIdsArray.data(), 
				static_cast<int>(fileIdsArray.size()), 
				nullptr, 0, complete
			);
		}
	}
	return WO_OK;
}

WebInt ExecDetail::OnMergeTaskSrcDataDirty(const char* fileId)
{
	if (!fileId)
	{
		WOLOG_INFO << "[OnMergeTaskSrcDataDirty] fileId nullptr";
		return WO_FAIL;
	}

	if (!gs_callback->broadcast)
		return WO_OK;

	// 根据fileId标记源文件
	WOLOG_INFO << "[OnMergeTaskSrcDataDirty] fileId :" << fileId;

	QString qFileId = QString::fromUtf8(fileId);
	_Workbook* pCoreWorkbook = m_workbook->GetCoreWorkbook();
    ks_stdptr<IKWorksheets> spWorksheets = pCoreWorkbook->GetWorksheets();
	INT worksheet_count = spWorksheets->GetSheetCount();

	std::vector<SMergeFileInfo> vecData;
	std::vector<WebID> vecDirtySheets;
    for(INT nIndex = 0; nIndex < worksheet_count; nIndex++)
    {
        ks_castptr<etoldapi::_Worksheet> pWorksheet = spWorksheets->GetSheetItem(nIndex);
		ks_stdptr<IMergeFile> spMergeFile;
		{
			ks_stdptr<IUnknown> spUnk;
			pWorksheet->GetSheet()->GetExtDataItem(edSheetMergeFile, &spUnk);
			spMergeFile = spUnk;
			ASSERT(spMergeFile);
		}
		if (!spMergeFile)
			return WO_FAIL;

		if (spMergeFile->GetIsAutoRefresh())
			continue;
		if (spMergeFile->IsSourceData(krt::utf16(qFileId), __X("")) && spMergeFile->SetTaskState(true))
		{
			IWorksheetObj* pObjSheet = pCoreWorkbook->GetWoObject()->getSheetItem(nIndex);
			if (pObjSheet)
				vecDirtySheets.push_back(pObjSheet->objId());
		}
    }

	if (!vecDirtySheets.empty())
	{
		BinWriter bw;
		bw.addStringField(__X("taskToBeUpdated"), "fileMergeEvent");
		bw.beginArray("dirtySheets");
		for (WebID objID : vecDirtySheets)
		{
			bw.addFloat64(objID);
		}
		bw.endArray();

		BinWriter::StreamHolder bt = bw.buildStream();
		WebSlice slice = {bt.get(), bw.writeLength()};
		gs_callback->broadcast(getMsgTypeName(msgType_MergeFiles), &slice, NULL);
	}

	return WO_OK;
}

bool ExecDetail::hasReadonlyFilter() 
{
    int sheetCtn = 0;
    IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
    pBook->GetSheetCount(&sheetCtn);
    bool readOnly = false;
    for (int i = 0; i < sheetCtn; ++i)
    {
		ks_stdptr<ISheet> pSheet;
        pBook->GetSheet(i, &pSheet);
        if (pSheet)
        {
            auto filter = pSheet->GetAutoFilters()->GetFilter(
                m_curUserConn->userID(), false);
            if (filter)
            {
                readOnly = filter->IsReadOnly();
            }
        }
		if (readOnly)
		{
			return true;
		}
		   
    }
	return false;
}

bool ExecDetail::isSharedLinkNotExist()
{
	IKETUserConn *pUser = GetCurUser();
	if (nullptr == pUser)
		return false;

	IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	if (!spSharedLinkMgr)
		return false;
	PCWSTR shareId = spSharedLinkMgr->GetConnSharedLink(pUser);
	if (!shareId)
		return false;
	ISharedLink* pLink = spSharedLinkMgr->GetItem(shareId);
	if (!pLink)
		return true;
	return false;
}

bool ExecDetail::isSharedLink()
{
	IKETUserConn *pUser = GetCurUser();
	if (nullptr == pUser)
		return false;

	IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	if (spSharedLinkMgr)
		return nullptr != spSharedLinkMgr->GetConnSharedLink(pUser);
	return false;
}

WebInt ExecDetail::GetLastPosition(IDX *sheetIdx, CELL *cell)
{
	IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
	if (!wo::util::IsValidSheetIdx(pBook, *sheetIdx))
	{
		WOLOG_INFO << "[GetLastPosition] invalid sheetidx " << *sheetIdx;
		return WO_AREATRANSFORM_ERROR;
	}

	if (!wo::util::IsValidCell(pBook, *cell))
	{
		WOLOG_INFO << "[GetLastPosition] invalid cell " << *cell;
		return WO_AREATRANSFORM_ERROR;
	}

	IAreaTransformContainter *trans = m_workbook->GetCoreWorkbook()->GetBook()->GetWoStake()->GetAreaTransformContainter();
	bool reachOriginal = false;
	if (!trans->Invert(*sheetIdx, *cell, reachOriginal))
	{
		WOLOG_INFO << "[GetLastPosition] Invert failed";
		return WO_AREATRANSFORM_ERROR;
	}

	BinWriter bw;
	bw.addInt32Field(*sheetIdx, "sheetIdx");
	bw.addInt32Field(cell->row, "row");
	bw.addInt32Field(cell->col, "col");
	bw.addBoolField(reachOriginal, "finished");

	BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice slice = {shbt.get(), bw.writeLength()};
	gs_callback->signal(nullptr, wo::getMsgTypeName(wo::msgType_GetLastPosition), &slice);
	return WO_OK;
}


WebInt ExecDetail::ExecExportAutofilterList()
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	InitContext(ctx);
	RANGE rg = wo::ReadRangeInl(m_workbook->GetBMP(), m_root);
	if (!rg.IsValid()) return WO_OK;

	ks_stdptr<Workbooks> spWorkbooks;
	m_workbook->GetCoreApp()->get_Workbooks(&spWorkbooks);

	EtTaskExecutor* pExecutor = static_cast<EtTaskExecutor*>(m_workbook->getTaskExecutor());
	pExecutor->ResetResForOthers();
	pExecutor->ResetResForSelf();

	AddWorkbookScope newWbScope(spWorkbooks.get(), m_workbook, ctx, 1);

	HRESULT hr = WO_OK;
	_Workbook* pWorkbook = newWbScope.GetWorkbook();
	if (!pWorkbook)
	{
		WO_LOG_X(m_workbook->getLogger(), WO_LOG_ERROR, "Fail in AddWorkbookScope!");
		return WO_FAIL;
	}

	ks_stdptr<etoldapi::Worksheets> spWorksheets;
	pWorkbook->get_Worksheets(&spWorksheets);

	ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorksheets->GetSheetItem(0);
	ExportAfListHelper helper(m_root, rg, spWorksheet, m_workbook->GetCoreWorkbook(), pWorkbook, &ctx);
    bool exportCategory = false;
	if (m_root.has("exportType"))
	{
		PCWSTR type = m_root.field_str("exportType");
		//list  result  category
		if (xstrcmp(type, __X("list")) == 0)
		{
			hr = helper.Export();
		}
		else if (xstrcmp(type, __X("result")) == 0)
		{
			hr = helper.ExportResult();
		}
		else if (xstrcmp(type, __X("category")) == 0)
		{
            exportCategory = true;
			hr = helper.ExportCategoryFilterResultToBook(ctx, m_workbook);
		}
	}
	else
	{
		hr = helper.Export();
	}
    //exportCategory的时候,导出的是一个目录,已经保存过了
	if (SUCCEEDED(hr) && !exportCategory && wo::VarObjFieldValidation::expectString(m_root, "filePath"))
	{
		ks_bstr filePath(m_root.field_str("filePath"));
		FILTERMEDIUM fm = {0};
		fm.tymed = FILTER_TYMED_FILE;
		fm.lpszFileName = filePath;

		ETSAVEARGUMENT saveArg;
		saveArg.fm = &fm;
		saveArg.bSilent = TRUE;
		saveArg.bSaveAs = TRUE;
		saveArg.bCrashSave = TRUE;
		saveArg.bAddtoMru = FALSE;
		saveArg.bChangeModifyFlag = FALSE;
		saveArg.nFileFormat = ffXLSX;
		saveArg.bRemember = TRUE;
		saveArg.bCalculateComplete = FALSE;
		hr = pWorkbook->Save(&saveArg);
	}

	util::CloseFile(pWorkbook);
	return hr;
}

WebInt ExecDetail::ExecCheckUserAllVisiblePermission()
{

	if (!m_workbook)
	{
		return WO_FAIL;
	}

	// 此函数可能会被嵌套调用，且在在外面的 CreateContext之后，故这里需要提前判断一下
	IEtProtectionCtx* iEtProtectionCtx = nullptr;
	wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext();
	std::unique_ptr<KEtRevisionContext> ctxPtr;
	if (ctx == NULL)
	{
		ctxPtr = CreateContext();
		iEtProtectionCtx = ctxPtr->getProtectionCtx();
	}
	else 
	{
		iEtProtectionCtx = ctx->getProtectionCtx();
	}
	
	// 如果是db，则判断文档是否有禁止查看的区域，不调用isRangeHasHidden。
	// 这里只是一个还原操作，因为 ExecCheckUserAllVisiblePermission 之前替换了 isBookHasHiddenProperty
	if (m_workbook->GetBMP()->bDbSheet)
	{
		return iEtProtectionCtx->isBookHasHiddenProperty() ? WO_NO_ALL_VISIBLES_PERMISSION : WO_OK;
	}

	IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
	RANGE range(pBook->GetBMP());
	INT count = 0;
	pBook->GetSheetCount(&count);
	range.SetSheets(0, count - 1);
	
	if (iEtProtectionCtx->isRangeHasHidden(range))
	{
		return WO_NO_ALL_VISIBLES_PERMISSION;
	}

	return WO_OK;
}

WebInt ExecDetail::ExecSetUserPermissionId(VarObj* pInfos)
{
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();
	KEtRevisionContext& ctx = *ctxPtr;
	InitContext(ctx);
	_Workbook* pCoreWorkbook = m_workbook->GetCoreWorkbook();
	util::DbCdcScope cdcScope(m_workbook);
	ks_stdptr<IWebhookManager> spWebhookMgr;
	pCoreWorkbook->GetBook()->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	ks_stdptr<_Application> ptrApp = m_workbook->GetCoreApp();
	IKUserConns* userConns = ptrApp->getUserConns();
	ks_stdptr<IDbCollector> spDbCollector;
	pCoreWorkbook->GetBook()->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
	for (int i = 0; i < pInfos->arrayLength_s(); i++)
	{
		VarObj item = pInfos->at_s(i);
		PCWSTR userId = item.field_str("userId");
		PCWSTR permissionId = item.field_str("permissionId");
		QString connId = QString("webhook_core") + QString::fromUtf16(userId);
		IKUserConn* userConn = userConns->getUserConn(connId.toUtf8());
		if (userConn)
		{
			if (spWebhookMgr && spWebhookMgr->HaveDbHookWithUserId(DbSheet_WebhookType_updateSheetsAllChange, userId))
			{
				// 标记全量更新
				IDbWebhookChangedRange* pDbRange = spWebhookMgr->GetDbWebhookChangedRange();
				pDbRange->CollectUpdateRegion();
				
				// 收集埋点
				ctx.collectInfo("behavior.dbwebhook.updateRegion.setUserPermissionId");
			}
			userConn->setCorePermissionId(permissionId);
		}
		if (spDbCollector)
			spDbCollector->CollectUpdateUserPermission(userId, permissionId);
	}
	return WO_OK;
}

WebInt ExecDetail::SplitToNewFile(const char* path)
{
	class AutoSetting
	{
	public:
		AutoSetting()
		{
			m_isEnable = _kso_GetWoEtSettings()->IsEnableSavingBreakCal();
			m_limitValue = _kso_GetWoEtSettings()->GetTimeLimitSaveBreakGeneral();
			
			_kso_GetWoEtSettings()->setEnableSavingBreakCal(true);
			if (gs_procType == WebProcTypeChild)
				_kso_GetWoEtSettings()->SetSlaveTimeLimitSaveBreakGeneral(0);
			else
			 	_kso_GetWoEtSettings()->SetTimeLimitSaveBreakGeneral(0);
		}
		~AutoSetting()
		{
			_kso_GetWoEtSettings()->setEnableSavingBreakCal(m_isEnable);
			if (gs_procType == WebProcTypeChild)
				_kso_GetWoEtSettings()->SetSlaveTimeLimitSaveBreakGeneral(m_limitValue);
			else
			 	_kso_GetWoEtSettings()->SetTimeLimitSaveBreakGeneral(m_limitValue);
		}
	private:
		bool m_isEnable;
		unsigned int m_limitValue;
	};

	if (!gs_callback->signal)
	{
		WOLOG_INFO << "!gs_callback->signal";
		return WO_FAIL;
	}

	// 防止在其他地方调用导致context错乱 保险点
	IEtProtectionCtx* iEtProtectionCtx = nullptr;
	wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext();
	std::unique_ptr<KEtRevisionContext> ctxPtr;
	if (ctx == NULL)
	{
		ctxPtr = CreateContext();
		iEtProtectionCtx = ctxPtr->getProtectionCtx();
	}
	else 
	{
		iEtProtectionCtx = ctx->getProtectionCtx();
	}

	AutoSetting autoSetting;
	KEtRevisionContext* pEtCtx = ctxPtr.get();


	ks_wstring wPath = krt::utf16(path);
	SplitBookCallBack splitBookCallBack(pEtCtx);

	IBook *pBook = m_workbook->GetCoreWorkbook()->GetBook();
	RANGE rg(pBook->GetBMP());
	rg.SetSheetFrom(-1);
	if (m_root.has("distribute") && m_root.field_bool("distribute")) {
		if (m_root.has("sheetIdx") && m_root.has("rowFrom")) {
			//局部分发
			IDX sheetIdx = GetSheetIdx(m_workbook->GetCoreWorkbook()->GetBook(), m_root);
			rg = wo::ReadRangeInl(m_workbook->GetBMP(), sheetIdx, m_root);
			if (rg.IsValid()) {
				splitBookCallBack.init(true, &rg);
			} else {
				WOLOG_INFO << "DeliverToNewFile fail: ";
			}
		} else {
			//整表分发
			splitBookCallBack.init(true, nullptr);
		}
	}
	HRESULT hr = SplitBookOpt::splitOptBook(m_root, wPath, &splitBookCallBack, m_workbook, pEtCtx);

	BinWriter bw;
	KSerialWrapBinWriter acpt(bw, ctxPtr.get());

	if (FAILED(hr))
	{
		WOLOG_INFO << "SplitToNewFile fail: " << hr;
		WOLOG_INFO << "err: " << splitBookCallBack.GetErrStr().c_str();
		acpt.addString("status", __X("fail"));
		acpt.addString("splitErrName", splitBookCallBack.GetErrStr().c_str());
	}
	else 
	{		
		WOLOG_INFO << "SplitToNewFile success";
		acpt.addString("status", __X("success"));
		splitBookCallBack.write(acpt);
	}

	binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice slice = {shbt.get(), bw.writeLength()};
	gs_callback->signal(m_connID, getMsgTypeName(msgType_SplitBook), &slice);

	return WO_OK;
}

WebInt ExecDetail::ExecExportAirApp()
{
	WOLOG_INFO << "[ExecExportAirApp] start";
	if (gs_procType != WebProcTypeChild)
	{
		WOLOG_INFO << "[ExecExportAirApp] Not in child process";
		return WO_FAIL;
	}
	std::unique_ptr<KEtRevisionContext> ctxPtr = CreateContext();

	if (!(m_workbook->GetBMP()->bKsheet))
	{
		WOLOG_ERROR << "[[ExecExportAirApp] is not KSheet!";
		return WO_FAIL;
	}
	_Workbook* pWb = m_workbook->GetCoreWorkbook();
	if (!pWb)
	{
		WOLOG_ERROR << "[ExecExportAirApp] File not open";
		return WO_FAIL;
	}

	if (!m_root.has("filePath"))
	{
		WOLOG_ERROR << "[ExecExportAirApp] No filePath parameter!";
		return WO_FAIL;
	}


	EtDbId viewId = INV_EtDbId;
	if (!m_root.has("sheetStId"))
	{
		WOLOG_ERROR << "[ExecExportAirApp] No sheetStId parameter!";
		return WO_FAIL;
	}
	UINT sheetStId = m_root.field_uint32("sheetStId");
	WOLOG_INFO << "[ExecExportAirApp] sheetStId :" << sheetStId;
	IDX iSheet = GetSheetIdx( m_workbook->GetCoreWorkbook()->GetBook(), m_root); 
	if (iSheet == INVALIDIDX)
	{
		WOLOG_ERROR << "[ExecExportAirApp] Invalid sheet index:" << iSheet;
		return WO_FAIL;
	}

	IKWorksheet* pWs = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWs)
	{
		WOLOG_ERROR << "[ExecExportAirApp] Invalid sheet index:" << iSheet;
		return WO_FAIL;
	}

	ISheet* pSheet = pWs->GetSheet();
	if(!pSheet || !pSheet->IsAppSheet())
	{
		WOLOG_ERROR << "[ExecExportAirApp] not a airAppSheet";
		return WO_FAIL;
	}

	ks_wstring newSharedId;
	if(m_root.has("newSharedId"))
	{
		newSharedId = m_root.field_str("newSharedId");
	}

	//屏蔽权限检查，防止因为权限还没准备好，后面的对dbview操作失败。
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	IBook* pBook = pWb->GetBook();
	ks_stdptr<IUnknown> spUnknown;
	pBook->GetExtDataItem(edDBUserGroups, &spUnknown);
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	spDBUserGroups->GetJudgement(&spProtectionJudgement);
	wo::DbSheet::DisableDbProtectScope disPtScope(spProtectionJudgement);
	
	std::set<ks_wstring> attachmentIdSet;
	HRESULT hr = ExportAirAppHelper::ExecExportAirApp(m_workbook, pSheet, ctxPtr.get(), newSharedId, attachmentIdSet);
	if(FAILED(hr))
	{
		WOLOG_ERROR << "[ExecExportAirApp] ExecExportAirApp failed!";
		return WO_FAIL;
	}

	//save as
	ks_bstr filePath(m_root.field_str("filePath"));
	FILTERMEDIUM fm = {0};
	fm.tymed = FILTER_TYMED_FILE;
	fm.lpszFileName = filePath;

	ETSAVEARGUMENT saveArg;
	saveArg.fm = &fm;
	saveArg.bSilent = TRUE;
	saveArg.bSaveAs = TRUE;
	saveArg.bCrashSave = TRUE;
	saveArg.bAddtoMru = FALSE;
	saveArg.bChangeModifyFlag = FALSE;
	saveArg.nFileFormat = ffKSHEET;
	saveArg.bRemember = TRUE;
	saveArg.bCalculateComplete = FALSE;

	ks_stdptr<IKDocument> doc = pWb;
	if (!doc)
		return WO_FAIL;
	hr = doc->Save(&saveArg);
	if(FAILED(hr))
	{
		WOLOG_ERROR << "[ExecExportAirApp] saveAs failed";
		return WO_FAIL;
	}

	//把收集到的db附件通过signal给服务端
	binary_wo::BinWriter bw; 
	bw.beginArray("attachmentIds");
	for (auto it = attachmentIdSet.begin(); it != attachmentIdSet.end(); ++it)
	{
		bw.addString(it->c_str());
	}
	bw.endArray(); 
	FillOut(bw, msgType_Attachments);
	return WO_OK;
}

WebInt ExecDetail::GetMergedSubRanges()
{
	struct MergedSubItem
	{
		IDX idx;
		WebStr fileId;
		std::vector<RANGE> ranges;
		MergedSubItem(IDX iIdx, WebStr pStr, std::vector<RANGE>&& vRange)
			: idx(iIdx) , fileId(pStr), ranges(std::move(vRange)) {}
	};
	
	IBook *wb = m_workbook->GetCoreWorkbook()->GetBook();
	Subscription::Utils utils(m_workbook);
	ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();
	if (nullptr == pSubOp)
		return WO_FAIL;
	if (!m_root.has("data"))
		return WO_FAIL;
	VarObj data = m_root.get_s("data");
	std::vector<WebStr> skipHandleVec;
	std::vector<MergedSubItem> handleVec;
	if (!data.empty())
	{
		int inputLength = data.arrayLength_s();
		handleVec.reserve(inputLength);
		for (int i = 0; i < inputLength; i++)
		{
			VarObj item = data.at_s(i);
			if (!item.has("file_id"))
				continue;
			WebStr fileId = item.field_str("file_id");
			IDX nBook = utils.GetSupBookIndex(fileId);
			if (nBook == alg::STREF_INV_BOOK || nBook == alg::STREF_THIS_BOOK || !item.has("ranges"))
			{
				skipHandleVec.emplace_back(fileId);
				continue;
			}
			VarObj ranges = item.get_s("ranges");
			int rangesLength = ranges.arrayLength_s();
			if (rangesLength == 0)
			{
				skipHandleVec.emplace_back(fileId);
				continue;			
			}
			std::vector<RANGE> rangeVec;
			rangeVec.reserve(rangesLength);
			for (int j = 0; j < rangesLength; j++)
			{
				VarObj rangeItem = ranges.at_s(j);
				RANGE rg(wb->GetBMP());
				IDX sheetFrom = rangeItem.field_int32("sheetFrom");
				IDX sheetTo = rangeItem.field_int32("sheetTo");
				ROW rowFrom = rangeItem.field_int32("rowFrom");
				ROW rowTo = rangeItem.field_int32("rowTo");
				COL colFrom = rangeItem.field_int32("colFrom");
				COL colTo = rangeItem.field_int32("colTo");
				try
				{
					rg.SetRowFromTo(rowFrom, rowTo);
					rg.SetColFromTo(colFrom, colTo);
					rg.SetSheetFromTo(sheetFrom, sheetTo);
				}
				catch (et_exception e)
				{
					continue;
				}
				rangeVec.emplace_back(rg);
			}
			handleVec.emplace_back(nBook, fileId, std::move(rangeVec));
		}
	}
	else
	{
		WOLOG_INFO << "[GetMergedSubRanges] empty data";
		return WO_FAIL;
	}
	std::unordered_set<INT> needCollectFiles;
	for (const auto &item: handleVec)
		needCollectFiles.insert(item.idx);
	std::unordered_map<INT, std::vector<RANGE>> res;
	pSubOp->CollectCrossBookRefersRanges(needCollectFiles, res);
	for (auto &item: handleVec)
	{
		if (res.find(item.idx) == res.end())
			continue;
		auto& subRange = res[item.idx];
		item.ranges.insert(item.ranges.cend(), subRange.cbegin(), subRange.cend());
	}
	BinWriter binWriter;
	binWriter.addKey("data");
	binWriter.beginArray();
	for (const auto fileId: skipHandleVec)
	{
		binWriter.beginStruct();
		binWriter.addStringField(fileId, "file_id");
		binWriter.endStruct();
	}
	for (const auto &item: handleVec)
	{
		binWriter.beginStruct();
		binWriter.addStringField(item.fileId, "file_id");
		const auto &rgs = item.ranges;
		binWriter.addKey("ranges");
		binWriter.beginArray();
		for (const auto &rg: rgs)
		{
			if (!rg.IsValid())
				continue;
			binWriter.beginStruct();
			binWriter.addInt32Field(rg.SheetFrom(), "sheetFrom");
			binWriter.addInt32Field(rg.SheetFrom(), "sheetTo");
			binWriter.addInt32Field(rg.RowFrom(), "rowFrom");
			binWriter.addInt32Field(rg.RowTo(), "rowTo");
			binWriter.addInt32Field(rg.ColFrom(), "colFrom");
			binWriter.addInt32Field(rg.ColTo(), "colTo");
			binWriter.endStruct();
		}
		binWriter.endArray();
		binWriter.endStruct();
	}
	binWriter.endArray();
	if (gs_callback->signal)
	{
		binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};
		gs_callback->signal(nullptr, getMsgTypeName(wo::msgType_Query), &slice);
	}
	return WO_OK;
}

void ExecDetail::Proc_AutoSlim(KEtRevisionContext& ctx, binary_wo::BinWriter& ww)
{
	IWoETSettings* pWoEtSettings = _kso_GetWoEtSettings();
	if (gs_procType != WebProcTypeMaster)
		return;
	if (ctx.isExecDirect() || !m_workbook->GetBookDocSlimNeedCheck())
		return;
	if (!ExecDocSlimHelper::isNeedSlim())
		return;

	IKDocSlimHelper* pDocSlimHelper = m_workbook->GetCoreWorkbook()->GetDocSlimHelper();
	if (pDocSlimHelper && !pDocSlimHelper->IsCanWoAutoSlim())
		return;

	if (pDocSlimHelper && pDocSlimHelper->IsProtectedDoc())
		return;

	const FileSaveInfo& fileInfo = m_workbook->GetFileSaveInfo();
	IBook* bk = m_workbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<IEtDocSlimData> spDocSlimData;
	bk->GetExtDataItem(edBookDocSlimData, (IUnknown **)&spDocSlimData);
	if (!spDocSlimData)
		return;

	BookSlimData* pBookSlimData = spDocSlimData->GetBookDocSlimData();
	bool bNeedAutoSlim = (pWoEtSettings->IsEnableAutoSlim() && pBookSlimData->emptyCellCount >= pWoEtSettings->GetDocSlimNullCellsTriggerValue()) || 
	                     pBookSlimData->unusedShapesCount >= pWoEtSettings->GetDocSlimInvisibleObjectTriggerValue() ||
						 pBookSlimData->cellPicturesCount >= pWoEtSettings->GetDocSlimUnreferencedPictureTriggerValue()||
						 pBookSlimData->overlapShapesCount >= pWoEtSettings->GetDocSlimOverlapShapesTriggerValue() ||
						 pBookSlimData->duplicateFormatConditionCount >= pWoEtSettings->GetDocSlimDuplicateFormatConditionTriggerValue() ||
						 pBookSlimData->unusedDuplicateStyleCount + pBookSlimData->sameSheetProtectionUserRangeCount>= pWoEtSettings->GetDocSlimUnusedDuplicateStyleTriggerValue();
	if (!bNeedAutoSlim || !pBookSlimData->isUpdateSlimData)
		return;
	time_t now = std::time(nullptr);
	if (spDocSlimData->CheckBookDocSlimNeedSend(now))
	{
		spDocSlimData->SetBookDocSlimSent(now);
		m_workbook->SetBookDocSlimNeedCheck(false);
		KSerialWrapBinWriter acpt(ww, &ctx);
		acpt.addBool("needAutoSlim", bNeedAutoSlim);
		WOLOG_INFO << "[Proc_AutoSlim] needAutoSlim";
	}
}

void ExecDetail::updateEnableCommonLog()
{
	IWoETSettings* pSettings = _kso_GetWoEtSettings();
	if (pSettings && pSettings->IsNeedUpdateEnableCommonLog())
	{
		ks_bstr value;
		_kso_GetWoCallBack()->getSetting(__X("cdc_common_log"), &value);
		PCWSTR str = value.c_str();
		if (str)
		{
			bool enable = xstrcmp(str, __X("true")) == 0;
			pSettings->SetEnableCommonLog(enable);
		}
	}
}
} // namespace wo
