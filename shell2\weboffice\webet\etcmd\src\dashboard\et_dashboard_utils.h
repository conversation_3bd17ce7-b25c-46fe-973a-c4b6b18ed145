﻿#ifndef __WEBET_DASHBOARD_UTILS_H__
#define __WEBET_DASHBOARD_UTILS_H__

#include "webbase/binvariant/binvarobj.h"

namespace wo
{

using binary_wo::VarObj;

namespace DashBoard
{
	int GetDashBoardModuleLimit();

	HRESULT initDashBoardSheet(IKEtWindow* pEtWindow, _Worksheet* pWorksheet, SHEETTYPE st);
	HRESULT SetWebextentionProperty(const VarObj& param, IKWebExtension* pWebExtension, IDBDashBoardDataOp* = nullptr);
	HRESULT SetWebextentionExtData(const VarObj& param, IWorksheetChart* pWebExtension);
	HRESULT SetEtDashboardChartSetting(const VarObj& param, IKWebExtension* pWebExtension);
	HRESULT SetWorkbookDataSource(IBook* pBook, IKWebExtension* pWebExtension, IRangeInfo* pRangeInfo);
	HRESULT SetPivotTableDataSource(IKWebExtension* pWebExtension);

	void CalcBoundaryRowColAndSeriesDirection(IBookOp* pBookOp, IKRanges* pRanges, UINT& boundaryRow, UINT& boundaryCol, BOOL& seriesByColumn);
	HRESULT GetPivotTableFromRange(IBook* pBook, const RANGE& range, pivot_core::IPivotTable** ppTbl);
	
	bool IsDashboardChart(IKWebExtension* pWebExtension);
}// namespace DashBoard

} // namespace wo

#endif // __WEBET_DASHBOARD_UTILS_H__