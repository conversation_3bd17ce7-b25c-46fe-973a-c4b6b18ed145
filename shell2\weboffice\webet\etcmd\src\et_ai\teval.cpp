﻿#include "teval.h"

namespace etai
{
CalcCellCount::CalcCellCount(const QJsonObject &obj) : m_obj(obj), m_cellCount(0)
{
    calculate();
}

int CalcCellCount::count() const
{
    return m_cellCount;
}
int CalcCellCount::rangeCount() const
{
    return m_rangeCellCount;
}

void CalcCellCount::calculate()
{
    QJsonObject::const_iterator it = m_obj.constFind("tableList");
    if (it != m_obj.constEnd() && it.value().isArray())
    {
        calculateTableList(it.value().toArray());
        return;
    }
    QJsonObject::const_iterator itPivotTableList = m_obj.constFind("pivotTableList");
    if (itPivotTableList != m_obj.constEnd() && itPivotTableList.value().isArray())
    {
        calculatePivotTableList(itPivotTableList.value().toArray());
        return;
    }
    QJsonObject::const_iterator itPivotTable = m_obj.constFind("pivotTable");
    if (itPivotTable != m_obj.constEnd() && itPivotTable.value().isObject())
    {
        calculateTable(itPivotTable.value().toObject());
        return;
    }
};

void CalcCellCount::calculateTableList(const QJsonArray &tableList)
{
    for (int i = 0; i < tableList.size(); i++)
    {
        if (!tableList.at(i).isObject())
            continue;
        const QJsonObject &table = tableList.at(i).toObject();
        calculateTable(table);
    }
}

void CalcCellCount::calculatePivotTableList(const QJsonArray &tableList)
{
    for (int i = 0; i < tableList.size(); i++)
    {
        if (!tableList.at(i).isObject())
            continue;
        const QJsonObject &table = tableList.at(i).toObject();
        QJsonObject::const_iterator itPivotTable = table.constFind("sourceTable");
        if (itPivotTable != table.constEnd() && itPivotTable.value().isObject())
        {
            calculateTable(itPivotTable.value().toObject());
        }
    }
}

void CalcCellCount::calculateTable(const QJsonObject &table)
{
    QJsonObject::const_iterator itCellCount = table.constFind("cellCount");
    if (itCellCount != table.constEnd() && itCellCount->isDouble())
        m_cellCount += (int)itCellCount->toDouble();
    bool bRange = true;
    if (table.contains("rangeCellCount"))
    {
        int nRangeCells = table["rangeCellCount"].toInt();
        if (nRangeCells > 0)
        {
            m_rangeCellCount += nRangeCells;
            bRange = false;
        }
    }
    if (bRange && table.contains("tableEnd") && table["tableEnd"].isArray() && table.contains("tableStart") &&
        table["tableStart"].isArray())
    {
        const QJsonArray &jsonStart = table["tableStart"].toArray();
        const QJsonArray &jsonEnd = table["tableEnd"].toArray();
        if (jsonStart.size() == 2 && jsonEnd.size() == 2)
        {
            int rowStart = jsonStart[0].toInt();
            int rowEnd = jsonEnd[0].toInt();
            int colStart = jsonStart[1].toInt();
            int colEnd = jsonEnd[1].toInt();
            if (rowStart >= 0 && rowEnd >= 0 && colStart >= 0 && colEnd >= 0 && rowEnd >= rowStart &&
                colEnd >= colStart)
                m_rangeCellCount += (rowEnd - rowStart + 1) * (colEnd - colStart + 1);
        }
    }
}
} // namespace etai