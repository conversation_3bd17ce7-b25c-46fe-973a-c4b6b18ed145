﻿#ifndef __ET_DBSHEET_BLOCK_INIT_HELPER_H__
#define __ET_DBSHEET_BLOCK_INIT_HELPER_H__

#include <unordered_set>
#include "et_dbsheet_common_helper.h"

interface IDBSheetView;

namespace binary_wo {
    class VarObj;
}

namespace wo
{

    class KEtWorkbook;
    class KEtRevisionContext;

    class DBSheetInitHelper 
    {
    public:
        explicit DBSheetInitHelper(KEtWorkbook *);

        bool setDbInitBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &blocks);
        void setDbView(IDBSheetView*);
        void initDbGridBlocks(KEtRevisionContext& ctx, EtDbId ltRecId, EtDbId ltFldId, EtDbIdx fltFldId, int frozenCols);
        void afterInitBlocks(KEtRevisionContext& ctx, bool isEmptyAddZero);

    protected:
        void clear();
        void initDbGridBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &param);
        void initDbKanBanBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &param);
        void initDbGalleryBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &param);
        void initDbCalendarBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &param);
        void appendRows(EtDbIdx from, EtDbIdx to);
        void appendCols(EtDbIdx from, EtDbIdx to);
        void makeBlocks(std::vector<RECT> &, bool isEmptyAddZero);

    private:
        KEtWorkbook * m_workbook;
        DBSheetCommonHelper m_commonHelper;
        ks_stdptr<IDBSheetView> m_spDbView;
        std::unordered_set<ROW> m_rows;
        std::unordered_set<COL> m_cols;
    };

}
#endif // __ET_DBSHEET_BLOCK_INIT_HELPER_H__
