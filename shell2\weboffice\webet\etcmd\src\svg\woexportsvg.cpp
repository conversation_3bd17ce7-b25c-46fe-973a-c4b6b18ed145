﻿#include "etstdafx.h"
#include "woexportsvg.h"
#include <QSvgGenerator>

namespace wo
{

static
int pixelToTwip(int pixel, int dpi)
{
    return pixel * 1440 / dpi;
}

HRESULT WoExportSvg(QIODevice *device, IKWorksheetView *pSheetView, EXPORT_SVG_PARAM param)
{
    const int defaultDpi = 96;
    int dpi = defaultDpi * param.devicePixelRatio;

    IKWorksheet *pWorksheet = pSheetView->GetWorksheet();
    IRenderView* rdView = pSheetView->GetActiveRenderView();
    IRenderNormalView* rdNormalView = rdView->GetNormalView();

    QSvgGenerator generator;
    generator.setTitle("");
    generator.setDescription("");
    generator.setOutputDevice(device);
    generator.setResolution(dpi);
    generator.setClipPathEnabled(true);

    SIZE actualSize = {param.exportSize.cx * param.devicePixelRatio, param.exportSize.cy * param.devicePixelRatio};
    generator.setSize(QSize(actualSize.cx, actualSize.cy));
    generator.setViewBox(QRect(0, 0, actualSize.cx, actualSize.cy));

    kpt::PainterExt painter(&generator);
    painter.setRenderHints(QPainter::SmoothPixmapTransform);
    painter.setupPageCoordinate(kpt::UnitPixel, 1);
	painter.scale(param.zoom, param.zoom);

    SIZE drawSize = {pixelToTwip(actualSize.cx, defaultDpi), pixelToTwip(actualSize.cy, defaultDpi)};
    painter.fill(Qt::white);

    DWORD dwDSO = dso_drawWoCheckBox;
    dwDSO |= param.bIsMobile ? dso_isMobile : 0;
    dwDSO |= param.bCanDrawGridHeadBorder ? dso_drawGridHeadBorder : 0;
    dwDSO |= param.bHeadBorderGrandientOn ? dso_headBorderGradient : 0;

    HRESULT hr = rdView->DrawSvg(&painter, param.LTCell, &drawSize, param.zoom, dwDSO);
    painter.end();

    return hr;
}

} // wo