﻿#ifndef __QUICKSET_HELPER_H__
#define __QUICKSET_HELPER_H__

// 已弃用不再维护, 改用Database下相关接口
#include "database/database_def.h"

namespace wo
{

enum QuickSetType
{
    qstInvalid,
    qstCheckbox,
    qstScan,
    qstDate,
    qstCellPic,
    qstList,
    qstTurnToDate,
    qstClear
};

interface IEtRevisionContext;
class KEtWorkbook;
class KEtQuickSet
{
public:
    KEtQuickSet(KEtWorkbook *);
    ~KEtQuickSet();

    static QuickSetType GetQuickSetType(WebStr);

    HRESULT operator()(QuickSetType, const RANGE &, IEtRevisionContext*);
    HRESULT FillValues(QuickSetType, const RANGE &, PCWSTR, IEtRevisionContext*);
    SetDVReturnCode GetDVReturnCode();
private:
    Database::FieldType Qst2Dft(QuickSetType);
    HRESULT setNumberFormat(const RANGE &, int);
    HRESULT setNumberFormat(const RANGE &, BSTR);

    KEtWorkbook *m_wwb;
    SetDVReturnCode m_code;
    bool m_allowSetDataValidation;
};

} // wo

#endif // __QUICKSET_HELPER_H__
