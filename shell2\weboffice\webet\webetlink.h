﻿#ifndef __WEB_ET_API_H__
#define __WEB_ET_API_H__
#include "webdeclare.h"

/*
 * @brief 发送消息给单个用户(异步通知)
 * @param name 消息名，前段根据消息名分发请求
 */
typedef void (*SignalFunc)(const char *connID, const char *name, const WebSlice *msg);


/*
 * @brief 发送消息给单个用户(异步通知)(signal承担太多功能, 复用pdf的NotifyJson来给用户发送信息)
 * @param name 消息名，前段根据消息名分发请求
 */
typedef void (*NotifyJsonFunc)(const char* connId, const char *cmdName, const WebSlice* data); 

/*
 * @brief 发送消息给全体用户
 * @param name 消息名，前段根据消息名分发请求
 * @param exclude 不发送给此用户；如果不需要排除，填NULL
 */
typedef void (*BroadcastFunc)(const char *name, const WebSlice *msg, const char *exclude);


/*
 * @brief 触发特定行为
 * @param action 行为类型,webhook代表回调url
 * @param triggerId 触发器id
 * @param triggerTime 定时触发时间戳，如果是-1则是马上触发
 */
typedef void (*TriggerFunc)(WebTriggerAction action, const char *groupId, const char *triggerId, long triggerTime, const WebSlice *msg);

/*
 * @brief 提交操作日志(需要为操作变换过后的commands)
 * @connID connect id
 * @msg  如果为空，表示commands无需变换
 */
typedef void (*CommitFunc)(const char *connID, const char* userID, const WebSlice *msg);

/*
 * @brief 是否空闲
 */
typedef WebInt (*HasRequestFunc)();

/*
 * @brief 添加文件内的图片
 * @param strSha1 图片的sha1摘要(16进制)
 * @param data
 × @param size
 */
typedef void (*UploadImageFunc)(const char* strSha1, const char* imageType, const WebSlice *image, const WebSlice *extra);

/*
* @ 获取的WebMimeData
*/
typedef void (*GetWebMimeData)(const char *mimeDataID, WebInt *count, WebMimeData **ppMimeData);

// 埋点，信息收信
typedef void (*CollectInfo)(const char *actionName, const WebSlice *data);


// 安全文档信息搜集
// @actionName "copy"
typedef void (*CollectSecDocInfo)(const char *userid, const char *actionName, const WebSlice *data);

/*
@brife 是否需要执行延迟拷贝
*/
typedef void (*SetNeedDelayCopy)(const int8_t bNeedDelay);
/*
* @brife 当前云盘的名称 如：yunwps
*/
typedef const char* (*GetNetDiskName)();

/*
* @brife 通过fileId拿到之前要求下载的文件路径和版本。返回的localPath和version 必需在 本次命令执行时有效。
*/
typedef NetFileRes (*GetNetFile)(const char* fileId, const char* userId, const char** localPath, const char** version, const char** fileName);

/*
* @brife 通过 URL 下载文件
*/
typedef NetFileRes (*AsyncGetNetFile)(const char* url, const char* userId, DownloadFileTask task, bool bNotifyCore, const WebSlice *data);

/*
* @brife 获取最近一个版本的文件信息
*/
typedef void (*GetVersionAndSize)(int *version, int64_t *size);

/*
* @brife 向fileId发送消息
*/
typedef void (*NotifyFile)(WebNotifyType type, const char* fileId, const WebSlice *data);

/*
* @brife 向服务端发送跨表格引用更新信息
*/
typedef void (*CrossBookNotify)(WebNotifyType type, const WebSlice *data);

/*
 * @brief 删除文件
 * @param pDeleteArg 删除参数
 */
typedef void (*DeleteObject)(const WebDeleteArg *pDeleteArg);

/*
 * @brief 下载文件
 * @param pDownloadArg 下载参数
 */
typedef void (*DownloadObject)(const WebDownloadArg *pDownloadArg);

/*
 * @brief 同步下载图片
 * @param urlList {list:[{objectKey,object}, {objectKey,object}, ...]}
 *     objectKey: 图片shape保存的url，用于标识作用; object:{tag,objectLocator}
 *     tag：为0时表示url图片，为1时表示附件图片; objectLocator：当tag为0时，此值表示url；当tag为1时，此值为附件id
 * @param downloadList {list:[{objectKey, code, path}, {objectKey, code, path}, ...]}
 *     objectKey: 图片shape保存的url，用于标识作用;    code:错误码    path：图片本地路径（失败则为空串）
 */
typedef void (*DownloadImageSync)(const WebSlice *urlList, WebSlice *downloadList);

/*
 * @brief 清除同步下载的图片
 * @param downloadList {list:[{objectKey, code, path}, {objectKey, code, path}, ...]}
 *     objectKey: 图片shape保存的url，用于标识作用;    code:错误码    path：图片本地路径（失败则为空串）
 */
typedef void (*ClearDownloadImage)(const WebSlice *downloadList);

/*
 * @brife 获取下载好的路径
 * @param pDownloadArg 下载的参数
 * @param path 返回下载的文件本地路径
 * @return 错误码
 */
typedef WebInt (*GetDownloadedObjectPath)(const WebDownloadArg *pDownloadArg, const char** path);

/*
 * @brief 獲取用戶訊息
 * @param useridList {list:[userid, userid, ...]}
 *     userid: 用戶id
 */
typedef void (*FetchUserInfo)(const char* connId, const char* userId, const WebSlice *useridList);

/*
 * @brife 保护工作表: 更新文件保护标志
 * @param isProtected 是否开启保护
 */
typedef WebInt (*UpdateProtectionInfo)(const char *connID, bool isProtected);

/*
 * @brife 获取用户是否可以设置文件保护标志的权限
 * @return 0无权限，1有权限
 */
typedef WebInt (*GetUserProtectPermission)(const char *connID);

/*
* @brife 获取用户是否文档所有者
@ @return 0不是，1是
*/
typedef WebInt (*GetUserProtectManagePermission)(const char * userId);

/*
@brief 用于提供XVA功能，类似于Query或者Exec接口，单独实现一个专门用于对接第三方需求的接口。
*/
typedef void (*ExtraCommandFunc)(const WebSlice *data);

typedef void (*CalculateReferenceDone)(const char **reliedFileId, unsigned int reliedFileIdLen, const char **reliedUrl, unsigned int reliedUrlLen, int complete);

/***
 * 合并表格自动更新，告知服务端自动更新开关状态变更
 * @info { subList: [fileId, fileId, ... ] }
 */
typedef void (*FileMergeInfoChanged)(const WebSlice *info);

/***
 * 生成自动密码
 * @password 密码
 */
typedef WebInt (*GetAutoPasswordFunc)(const char *connID, const char *uuid, const char **password);

/***
 * 向订阅服务中心发送消息
 * @WebNotifyType 订阅/取消订阅/通知更新
 */
typedef void (*SignalSubscriptionCenterFunc)(WebNotifyType type, const char* task, const char* fileId, const WebSlice *data);
/*
*   @ 复制粘贴时，通知服务端从源文件绑定附件
*/
typedef void (*GetAttachmentFromOtherFile)(const WebSlice *attachmentInfo);

/*
 *@brief 上传附件
 */
typedef void (*UploadAttachmentObjects)(const WebSlice *data);

/*
 * @brief DbSheet 提交协作记录
 */
typedef void (*CommitDbCohistories)(const WebSlice*);

/*
 * @brief DbSheet 写CommonLog
 */
typedef void (*WebCommonLog)(const WebSlice*);

/*
*@brief ET 从服务端获取单元格中关键词的文本位置和颜色数据
*/
typedef WebInt (*HighlightKeyword)(const WebSlice* wordSlice, HighlightWordData** data);

/*
 * @brief 父子进程通讯
 */
typedef WebInt (*OnSyncParentProc)(const WebSlice* input, WebSlice* output);

/*
 * @brief 让服务端释放申请的webslice中的堆内存的通用方法
 */
typedef void (*OnFreeWebSlice)(WebSlice* output);

/*
 * @brief 通知清空子进程
 */
typedef void (*OnCleanChildProc)();

/*
 * @brief Webhook 触发订阅事件，通知返回给订阅方
 */
typedef void (*OnNotifyHookEvent)(WebSlice* output);

/*
 * @brief 下载附件
 */
typedef void (*DownloadDbAttachment)(const WebSlice* args, WebSlice* doneList);

/*
 * @brief 同步拷貝附件
 */
typedef void (*CopyAttachmentSync)(const WebSlice* args, WebSlice* doneList);

/**
 * @brief 同步拷貝附件完成后,回调清理变量
 */
typedef void (*OnCopyAttachmentSyncDone)();

/*
 * @brief 清理变量
 */
 typedef void(*ClearDownloadDbAttachment)();

/*
 * @brief 上报版本标记
 */
typedef void (*ReportVersionTag)(const WebSlice* tag);

/*
 * @brief 获取区域权限自动密码的开启者
 * @param uuidList {list: [uuid, uuid, ...]}
 * @param userIdList {list: [{uuid, userId}, {uuid, userId}, ...]}
 */
typedef void (*GetAutoPasswordMaster)(const WebSlice *uuidList, WebSlice *userIdList);

/*
 * @brief 清除服务端缓存的自动密码，减少内存占用
 */
typedef void (*ClearAutoPasswordMasterCache)();

/*
 * @brief  et升级到as失败回调
 */
typedef void (*OnConvertEtToAsFailed)(WebInt hr); 

/*
 * @brief 向服务端获取sharedId
 * @param viewType 入参，视图类型
 * @param sharedIdBuf 出参，服务端生成的sharedId
 * @param sharedIdBufLen 入参，sharedIdBuf数组的大小
 */
typedef void (*FetchSharedId)(const char *viewType, char *sharedIdBuf, int sharedIdBufLen);

/*
 * @brief 把进行应用的版本升级的结果返回给服务端
 */
typedef void(*UpdateAppVersionDone)(const WebSlice* result);

/*
 * @brief 删除应用sheet时将被删除的应用shareId给到服务端
 */
typedef void(*OnAfterDeleteAppSheet)(const WebSlice* result);

/*
 * @brief 发送EAF计算请求
*/
typedef WebInt (*CalculateEAF)(const WebSlice* input);

/*
 * @brief 设置文档标记
 */
typedef WebInt (*SetFileTag)(const WebSlice* data);

/*
 * @brief 本地图片转附件
 */
typedef WebInt (*ConvertShapes)(const WebSlice* input, WebSlice* output);

/*
 * @brief 查询本地/云图信息
 */
typedef WebInt (*QueryShapes)(const WebSlice* input, WebSlice* output);

/*
 * @brief 判断用户是否在部门/团队里面
 */
typedef WebInt (*UserIsInOrganize)(const WebSlice* input, WebSlice* output);

/*
 * @brief 获取用户内容权限组id
 * @brief 向服务端获取permissionId
 * @param userId 入参，用户id
 * @param permissionIdBuf 出参，服务端生成的permissionId
 * @param permissionIdBufLen 入参，permissionIdBuf数组的大小
 */
typedef WebInt (*FetchPermissionId)(const char *userId, char *permissionIdBuf, int permissionIdBufLen);

/*
 * @brief 生成二维码图片并上传
 */
typedef WebInt (*GenerateAndUploadQRLabelImg)(const WebSlice *input, WebSlice *output);

/*
 * @brief 获取文档信息（当前仅支持文件名）
 * @param 入参connId
 * @param 出参fileInfo
 */
typedef WebInt (*GetFileInfo)(const WebSlice* input, WebSlice *fileInfo);

/* 
 * @brief 准备权限组数据，创建"编辑者"和"填报者";准备用户权限数据，用户id，迁移到哪个权限组
 * @param slice 入参 权限组数据，用户权限数据，执行结果
 *  {
        "connId": "***",
        "permissions": [
          {
            "corePermissionId": "***",
            "permissionName": "***",
            "permissionContent": [{...}]
          },
          {...}
        ],
        "usersPermission": {
            "userId": {
                "permissionType": "preset"/"thirdapp",  // 系统的还是自定义的
                "corePermissionId": "",
                "permissionContentType": ""
            },
            ...
        }
 *  }
 * @return 0成功，1失败
 */
typedef WebInt (*ConvertUsersPermission)(const WebSlice* slice);


/*
批量获取用户的企业信息，包括部门，上级，工号，企业邮箱, 结果通过dbsheet.updateUserOrganizeInfo给到内核
*/
typedef void (*GetUserOrganizeInfo)(const char* connID, const WebSlice* userIdList);
/**
 * @brief 提交Api的命令
 * @param connID connect id
 * @param msg  命令及参数
 * @param scriptIds 当前提交命令中包含的脚本taskId
 * @param dependCommitId 当前提交依赖的commitId, -1表示无依赖
 */
typedef void (*CommitApiTaskFunc)(const char *connID, const char* userID, const WebSlice *msg, const WebSlice* scriptIds, int dependCommitId);

/*
 * @brief 判断ksokoken是否需要刷新
 */
typedef WebInt (*NeedRefreshKsoTokenFunc)(const char *ksoToken);

/**
 * @brief 刷新kso_token
 * @param input 服务端之前传递的kso_token&refresh_token
 * @return 0成功，1失败
 */
typedef WebInt (*RefreshKsoTokenFunc)(const char* old_kso_token, const char* refresh_token, IWebString* new_ksotoken);
/*
 * @brief 异步执行完成数据分析-合表任务后，去通知服务端的回调函数
 * @param slice 入参 ,数据分析-合表任务任务完成后，返回不同的数据结构给服务端，服务端再根据场景去执行不同的后续动作
 * {
        "isPreview":true,
        "rangeData": [//当"isPreview":true时，才会有此字段,表示合表结果的预览数据
                ["1","2"],
                ["1","2"],
        ],
        "filePath":"/tmp/afdfdf",//当"isPreview":false时，才会有此字段,表示合表结果的导出csv后的path
    }
 */
typedef void (*DataAnalyzeMergeTaskDone)(const WebSlice* slice);

/*
 * @brief 从服务端查询配置
 */
typedef void (*GetSetting)(const WebSlice* input, WebSlice* output);

typedef struct Callback
{
    SignalFunc signal;
    BroadcastFunc broadcast;
    UploadImageFunc uploadImage;
    CommitFunc commit;
    HasRequestFunc hasRequest;
    WebLogFunc log;
    GetWebMimeData getMimeDatas;
    CollectInfo collectInfo;
    OnAfterDocOpenFunc onAfterDocOpen;
    SetNeedDelayCopy setNeedDelayCopy;
    GetNetDiskName getNetDiskName;
    GetNetFile getNetFile;
    GetVersionAndSize getVersionAndSize;
    CollectSecDocInfo collectSecDocInfo;
    DownloadObject downloadObject;
    DownloadImageSync downloadImageSync;
    ClearDownloadImage clearDownloadImage;
    GetDownloadedObjectPath getDownloadedObjectPath;
    UpdateProtectionInfo updateProtectionInfo;
    GetUserProtectPermission getUserProtectPermission;
    NotifyFile notifyFile;
    CrossBookNotify crossBookNotify;
    OnDocOpenFailed onDocOpenFailed;
    ExtraCommandFunc extraCommand;
    ExportDataFunc exportData;
    GetAutoPasswordFunc getAutoPasswd;
    TriggerFunc trigger;
    SignalSubscriptionCenterFunc signalSubscriptionCenter;
    FetchUserInfo fetchUserInfo;
    GetAttachmentFromOtherFile getAttachmentFromOtherFile;
    AsyncGetNetFile asyncGetNetFile;
    CalculateReferenceDone calculateReferenceDone;
    FileMergeInfoChanged fileMergeInfoChanged;
    CommitDbCohistories commitDbCohistories;
    HighlightKeyword highlightKeyword;
    UploadAttachmentObjects uploadAttachmentObjects;
    OnSyncParentProc onSyncParentProc;
    OnFreeWebSlice onFreeWebSlice;
    OnCleanChildProc onCleanChildProc;
    OnNotifyHookEvent onNotifyHook;
    DownloadDbAttachment downloadDbAttachment;
    ClearDownloadDbAttachment clearDownloadDbAttachment;
    ReportVersionTag reportVersionTag;
    OnConvertEtToAsFailed  onConvertEtToAsFailed;
    FetchSharedId fetchSharedId;
    UpdateAppVersionDone updateAppVersionDone;
    OnAfterDeleteAppSheet onAfterDeleteAppSheet;
    CalculateEAF calculateEAF;
    CopyAttachmentSync copyAttachmentSync;
    OnCopyAttachmentSyncDone onCopyAttachmentSyncDone;
    GetAutoPasswordMaster getAutoPasswordMaster;
    ClearAutoPasswordMasterCache clearAutoPasswordMasterCache;
    SetFileTag setFileTag;
    ConvertShapes convertShapes;
    QueryShapes queryShapes;
    FetchPermissionId fetchPermissionId;
    GenerateAndUploadQRLabelImg generateAndUploadQRLabelImg;
    GetFileInfo getFileInfo;
    UserIsInOrganize userIsInOrganize;
    ConvertUsersPermission convertUsersPermission;
    GetUserOrganizeInfo  getUserOrganizeInfo;
    CommitApiTaskFunc commitApi;
    NeedRefreshKsoTokenFunc needRefreshKsoToken;
    RefreshKsoTokenFunc refreshKsoToken;
    DataAnalyzeMergeTaskDone dataAnalyzeMergeTaskDone;
    GetUserProtectManagePermission getUserProtectManagePermission;
    NotifyJsonFunc notifyJson;
	DeleteObject deleteObject;
    WebCommonLog commonLog;
    GetSetting getSetting;
} Callback;

/*!
 * @brief 基线进程初始化，在基线进程fork之前初始化可共享的数据
 */
WEB_EXPORT void InitBaseContext(Callback *callback);

/*!
 * @brief 初始化
 */
WEB_EXPORT void Begin(Callback *callback, const WebConfigOptions* config);
/*!
 * @brief 清理
 */
WEB_EXPORT void End();

/*!
* @brief 初始化进程上下文配置
*/
WEB_EXPORT WebInt InitContext(const WebContextType type, WebSlice* config);

/*!
 * @brief 打开一个文件, expFmla = 0  --> 不导出公式
 */
WEB_EXPORT WebInt Open(const WebOpenArguments *arg, const UserContext* userContext);
/*!
 * @brief 关闭文件
 */
WEB_EXPORT WebInt Close();
/*!
 * @brief new 文件
 */
WEB_EXPORT WebInt New();

enum SaveAsTag
{
    SaveAsUnusedTag             = -1,
    SaveAsUploadObjectFinishTag = 1
};
/*!
 * @brief 保存文件
 */
WEB_EXPORT WebInt SaveAs(const char* filename, const UserContext* userContext, int tag);
/*!
 * @brief 服务端通知内核保存成功
 */
WEB_EXPORT WebInt NotifyUploadSaveResult();
/*!
 * @brief 连接上次保存的task与服务器中文件的版本
 */
WEB_EXPORT WebInt ConnectPrevSaveTask(WebInt cloudVer);

/*!
 * @brief 指定格式另存文件
 */
WEB_EXPORT WebInt SaveAsFormat(const char *filename, const char *format, const UserContext* userContext);

/*!
 * @brief  另存为碎片文件
 */
WEB_EXPORT WebInt SaveAsLiteVersion(const UserContext* userContext, const char *filename, int scene, const WebSlice *rangeData);

/*!
 * @brief 普通文档加密成安全文档或加密文档解密成普通文档
 */
WEB_EXPORT WebInt ExecEncryptDecrypt(const WebSaveArguments *saveArg, const UserContext* userContext);

/*!
 * @brief 执行命令（只读操作）；分离读/写命令， 方便做权限区分
 */
WEB_EXPORT WebInt QueryInit(const UserContext* userContext, const WebSlice *commands);
/*!
 * @brief 执行命令（只读操作）；分离读/写命令， 方便做权限区分
 */
WEB_EXPORT WebInt Query(const UserContext* userContext, const WebSlice *commands);
/*!
 * @brief 执行命令（只读操作）；分离读/写命令， 方便做权限区分
 */
WEB_EXPORT WebInt QueryPlay(const UserContext* userContext, const WebSlice *commands, WebInt bForceInit);
/*!
 * @brief 在只读的情况下，缓存数据： 获取摘要信息，
 */
WEB_EXPORT WebInt QuerySummary(const UserContext* userContext, const WebSlice *commands);
/*!
 * @brief 获取sheet的摘要相关数据给到前端在第一版页面时进行绘制
 */
WEB_EXPORT WebInt QuerySummaryTile(const UserContext* userContext, const WebSlice *commands);
/*!
 * @brief 在只读的情况下，缓存数据： 获取摘要信息，
 */
WEB_EXPORT WebInt QueryMemoStat(const UserContext* userContext, const WebSlice* commands);

/*!
 * @brief List all attachment ids
 */
WEB_EXPORT WebInt QueryAttachments(const UserContext* userContext, const WebSlice *commands);

/*!
 * @brief 获取指定 sheets 的附件 id
 * @param sheetIdVec 要获取附件 id 的数据表 ID
 */
WEB_EXPORT WebInt QuerySheetsAttachments(const UserContext* userContext, const WebSlice* commands);

/*!
 * @brief 在只读的情况下，缓存数据： 获取block的信息
 */
WEB_EXPORT WebInt QueryBlock(const UserContext* userContext, const WebSlice *commands);

/*!
 * @brief 查询跨表引用订阅区域数据
 */
WEB_EXPORT WebInt QuerySubscriptionInfo(const UserContext* userContext);

/*!
 * @brief 执行命令（写操作）
 * @param commitVersion 服务器预分配协作记录 ID
 */
WEB_EXPORT WebInt Exec(const UserContext* userContext, const WebSlice *commands, const WebSlice *serCensorRes);
/*
 *@brief 不经变换直接执行命令，一般用于版本恢复
 */
WEB_EXPORT WebInt ExecDirect(const UserContext* userContext, const WebSlice *commands, CommitID commitVersion);
/*!
 * @brief 执行评论命令（写操作）
 */
WEB_EXPORT WebInt ExecComment(const UserContext* userContext, const WebSlice *commands, const WebSlice *serExtraParam);

/*
@brief 执行Query或者Exec等操作，专门给外部接口调用（非前端），用于提供XVA功能
*/
WEB_EXPORT WebInt ExecExtraCommand(const UserContext* userContext, const WebSlice *commands);

/*
@brief 初始化连接状态
*/
WEB_EXPORT WebInt InitConnState(const UserContext* userContext);
/*!
 * @brief 更新对象
 */
WEB_EXPORT WebInt UpdateShapes(const UserContext* userContext, const WebSlice*);
/*!
 * @brief 用户加入
 * @param sessionID 用户会话 ID
 * @param secdocPermission 安全文档权限, @see webdeclare.h WebSecurityDocInfo
 */
WEB_EXPORT WebInt UserJoin(const UserContext* userContext);
/*!
 * @brief 用户离开
 */
WEB_EXPORT WebInt UserQuit(const UserContext* userContext);
/*!
 * @brief 多个用户离开
 * @param users { users: [ {connID: xxxx, userID: xxxx}, ... ] }
 */
WEB_EXPORT WebInt UsersQuit(WebSlice *users);
/*
@brief 返回连接的最新命令序号
@connID connect ID
@return 返回0表示连接不存在或者未发生命令
*/
WEB_EXPORT WebInt GetConnSeq(const UserContext* userContext);
/*
@brife 当所有用户不操作时被调用
*/
WEB_EXPORT WebInt DoIdle();
/*
@brife 输出PDF、图片
*/
WEB_EXPORT WebInt ExportAs(const UserContext* userContext, WebInt type, WebSlice *commands);
/*
@brife 输出SVG
 * @param commands
 * {
 *      sheet_name: string,
 *      dpi: double, // 是 devicePixelRatio
 *      zoom: int, // 缩放，如 100 为不缩放
 *      exportRange: { //需要导出的区域(行列位置)，设置该属性时忽略 row, col, width, height属性, 没有该参数时使用(默认不设置) row/col/width/height属性
 *          rowFrom: int,
 *          colFrom: int,
 *          rowTo: int,
 *          colTo: int,
 *      }，
 *      row: int,
 *      col: int,
 *      width: int, // 单位是像素
 *      height: int // 单位是像素
 * }
 *
*/
WEB_EXPORT WebInt ExportAsSvg(const UserContext* userContext, WebSlice *commands);
/*
@brife 进程退出前调用
*/
WEB_EXPORT WebInt OnExit();

WEB_EXPORT WebInt GetImages(const UserContext* userContext, const WebSlice *commands);
/*
@brife 延迟复制
*/
WEB_EXPORT WebInt ExecDelayCopy(const UserContext* userContext, WebSlice *commands, const WebMimeData *pMimeData);
/*
@brief 批量延迟拷贝
*/
WEB_EXPORT WebInt BatchDelayCopy(const UserContext* userContext, WebSlice *commands, WebInt count, const WebMimeData **ppMimeData);
/*
 *@brief 订阅所有跨book引用到的文件
 */
WEB_EXPORT WebInt ExecSubscribeCrossBooks();
/*
 *@brief 设置跨book自动更新最大允许订阅的文件数量
 */
WEB_EXPORT WebInt SetCrossBookAutoUpdateThreshold(int nCount);
/*
 *@brief 刷新一下初始化版本的信息
 */
WEB_EXPORT WebInt RefreshInitVersion(const UserContext* userContext, const WebSlice*);
/*
 *@brief 处理其他文件发来的消息
 */
WEB_EXPORT WebInt OnNotifyFile(WebNotifyType type, const UserContext* userContext, const char* fileId, WebSlice *commands);

/*
 *@brief 上传附件完成后调用
 */
WEB_EXPORT WebInt UploadAttachmentObjectsDone(const WebSlice *uploadObjectDone);

// /*
// * @brife 删除完成后调用
// * @param pDeleteArg 上传参数
// * @param code 错误码
// * @param fileId 删除的文件ID或URL（失败则为空串）
// */
// WEB_EXPORT WebInt DeleteObjectDone(const WebDeleteArg* pDeleteArg, int code, const char* fileId);

/*
* @brife 下载完成后调用
* @param pDownloadArg 下载参数信息
* @param code 错误码
* @param path 下载的本地路径（失败则为空串）
*/
WEB_EXPORT WebInt DownloadObjectDone(const WebDownloadArg* pDownloadArg, int code, const char *path);

/*
* @ 设置一个中断标记位，目前可以中断计算, 表示内核需不需要中断计算，设置false并不会触发计算
*/
WEB_EXPORT WebInt SetBreak(bool b);
/*
* @ 恢复计算, 当计算结果已是最新时,快速返回。如果需要计算，在计算完成或中断后返回
*/
WEB_EXPORT void ResumeCalculate();
/*
* @ 安全文档审核, 输出文档中文字信息
*/
WEB_EXPORT WebInt ExportCensorData(const char *filename);
/*
 * @brief 安全文档审核, 输出sheet中文字信息，目前仅支持仪表盘sheet
 * @param
 * {
 *     sheetId: int
 *     fileName: string
 * }
 */
WEB_EXPORT WebInt ExportSheetCensorData(const WebSlice *param);
/*
* @ 安全文档审核, 输出文档中所有图片
*/
WEB_EXPORT WebInt ExportAllShapes(const WebSlice *param);
/*
* @ 查询单元格最后修改者
*/
WEB_EXPORT WebInt GetCellLastRevision(int sheetIdx, int row, int col);

/*
* @ 处理订阅服务发来的信息
*/
WEB_EXPORT WebInt OnSubscriptionCenter(WebNotifyType type, const char* task, const UserContext* userContext, const char* fileId, WebSlice *commands);
/*
* @ 获取sheetNames
*/
WEB_EXPORT WebInt GetSheetNames();
/**
 * 设置触发器的配置
 */
WEB_EXPORT WebInt SetTriggerConf(const WebSlice *param);
/*
*   @ server 擷取用戶信息後調用, 將用戶信息送至 core
*   @ server calls after fetching user info successfully.
*/
WEB_EXPORT WebInt FetchUserInfoDone(const WebSlice* userInfoList);
/*
* 通知内核，本链接是通过分享链接访问。内核将对该链接做特殊的权限控制, 而不是使用用户。
* ！！！需要在 QueryInit 之前调用
* @return WO_SHARED_LINK_INVALID|WO_SHARED_LINK_ACCESS_DENIED
*/
WEB_EXPORT WebInt MarkAsSharedLink(const UserContext* userContext, const char *sharedId);
/*
* 判断用户是否可以开启，取消分享链接。
* ！！！需要在 开启，取消分享时调用。
* @return 0：不允许分享，1：没有开启组件内权限，允许分享 2：有开启组件内权限且当前用户有权限分享
*/
WEB_EXPORT WebInt IsCanManageSharedLink(const UserContext* userContext);
/*
*   @ server 服务端拷贝附件ID完成
*/
WEB_EXPORT WebInt BindAttachmentIdsDone(const WebSlice* serData);

/*
 * @ server EAF函数计算完成后回调内核
*/
WEB_EXPORT WebInt OnCompleteEAFCalc(const WebSlice* ret);
/*
* @brife 下载文件完成，参数和 AsyncGetNetFileByUrl 传入的一致
* @param DownloadFileTask 是否通过fileId下载文件
*/
WEB_EXPORT void OnAsyncGetNetFileDone(NetFileRes res, const char* url, const UserContext* userContext, const char* fileId, DownloadFileTask task);

/*
* @brife 通知重新计算引用
*/
WEB_EXPORT WebInt CalculateReference(const UserContext* userContext, RecalculateType type);

/*
* @brife 合并表格是否更新完毕
*/
WEB_EXPORT WebInt CalculateMergeFile(const UserContext* userContext, RecalculateType type);

/*
* @brife 通知更新引用的文件
*/
WEB_EXPORT void NotifyInsertNetFile(const char* fileID, NetFileRes res, const UserContext* userContext, const char *updateSeq);

/*!*
* @brief 通知导出图表数据
*/
WEB_EXPORT WebInt ExportChart(const UserContext* userContext);
/*
* @brife 通知更新合并工作表
*/
WEB_EXPORT void NotifyMergeFileUpdate(WebNetFileInfo* pFileInfos, int count, const UserContext* userContext, const char *updateSeq, bool bIncreament);
/*
* @brife 通知合并任务，数据源有更新
*/
WEB_EXPORT WebInt NotifyMergeTaskSrcDataDirty(const char* fileID);

/*
* @brife 行協作記錄查詢使用者權限
*/
WEB_EXPORT WebInt AuthQueryDbCoHistories(const UserContext* userContext, const WebSlice *param);

/*
* @ 获取当前工作表的相关总览信息
*/
WEB_EXPORT WebInt GetSheetsInfo();

/*
* @brife 文件保存后，记录下版本以及size
*/
WEB_EXPORT WebInt SetFileVersionSize(WebInt version, WebFileSize size);


WEB_EXPORT WebInt PrepareExportAttachment(const UserContext* userContext);

WEB_EXPORT WebInt CanExportAttachment();

WEB_EXPORT WebInt ExportAttachment();

/*
 * @brief 上传本地图片完成时，服务端会回调这一接口让内核进行后续处理
 * @param uploadObjectDone 返回上传图片的结果，成功返回sha-附件id；失败返回sha-失败原因
 */
WEB_EXPORT WebInt ExportAttachmentDone(const WebSlice* uploadObjectDone);

/*
* @brife 获取文件打开时单元格位置, {sheetIdx, row, col} 为当前位置
* 返回错误时，不需要继续回溯查找（可能是查找参数有误或者当前版本是数据输入源），当前版本仍然需要进行查看单元格历史操作
* @return msgtype: "workbook.getLastPosition", msgbinvar: { sheetIdx: int32, row: int32, col: int32 }
*/
WEB_EXPORT WebInt GetLastPosition(WebInt sheetIdx, WebInt row, WebInt col);

/*
* @brife 处理子进程的请求
*/
WEB_EXPORT WebInt SyncChildProc(const WebSlice* input, WebSlice* output);

/*
* @brife 处理完子进程的请求之后释放
*/
WEB_EXPORT void FreeSyncProcWebSlice(WebSlice* output);

/*
* @brife 在master被fork前调用, 将需要同步的数据加锁
*/
WEB_EXPORT void LockSyncData();

/*
* @brife 在master被fork后对master和slave调用, 将需要同步的数据解锁
*/
WEB_EXPORT void UnlockSyncData();

/*
* @brife 通知进程类型
*/
WEB_EXPORT void SyncProcType(WebProcType procType);

/*
* @brife Webhook 订阅事件
*/
WEB_EXPORT WebInt SubHookRules(const WebSlice* rule);

/*
* @brife Webhook 修改订阅事件
*/
WEB_EXPORT WebInt UpdateHookRules(const WebSlice* rule);

/*
* @brife Webhook 查询是否能订阅
*/
WEB_EXPORT WebInt CheckCanHook(const WebSlice* rule);

/*
* @brife Webhook 注销订阅事件
*/
WEB_EXPORT WebInt UnSubHookRule(const char* hookId);

/*
* @brife 调用查询器查询信息
*/
WEB_EXPORT WebInt ExportInquirerData(const UserContext* userContext, const WebSlice *param);

/*
* @brife 获取表格et/ksheet内部所有的像素画
*/
WEB_EXPORT WebInt GetPixelImg(const WebSlice* param);

/*
 * @brief 导出表格缩略图，导出图片格式为PNG
 * @param
 * {
 *     width: int //单位是像素
 *     height: int //单位是像素
 *     fileName: string
 * }
 */
WEB_EXPORT WebInt ExportThumbnail(const UserContext* userContext, const WebSlice *param);

/*
 * @brief 打开db文件后，附件权限数据准备好将权限数据通知内核
 * @param permissionConfig : {
 *  permissions:[
        {
        "corePermissionId":"", 
        "permissionName":"",
        "permissionContent":[],
        },
        ... 
    ]
}
 */
WEB_EXPORT WebInt NotifyContentPermission(const WebSlice *permissionConfig);

/**
 * 用户是否有查看全部的权限
 */
WEB_EXPORT WebInt CheckUserAllVisiblePermission(const char *connID, const char* userID);

/*
* @brife 切换审查的ShareId，ShareId不为空时只导出分享链接能看到的内容，而且会处理等于"我"的筛选
*/
WEB_EXPORT WebInt SetCensorShareId(const char *shareId);

/*
* @brife 设置查询应用验证码数据
*/
WEB_EXPORT WebInt SetVerifiedValue(const WebSlice* slice);

/*
 * @brief 通知自动密码转让
 * @param userIdList {list: [{uuid, userId}, {uuid, userId}, ...]}
 */
WEB_EXPORT WebInt NotifyTransferAutoPassword(const WebSlice *userIdList);


/*!
 * @brief 导出筛选列表到新文档
 * @param param : WebSlice
 * {
 *     filePath: string // 要保存的文件位置
 *     bExportChart: bool // 是否导出图表
 *     countName: string // 计数列列名
 *     sheetName: string // 新sheet名
 *     colLabel: string // 筛选表头为空时，默认列名
 *     blankString: string // 筛选项未空时，默认值
 *     chartType: int // 图表类型
 *     chartStyle: int // 图表样式
 *     rect // 图表放置位置 单位为缇
 *     {
 *          left: int
 *          top: int
 *          width: int
 *          height: int
 *     }
 * }
 */
WEB_EXPORT WebInt ExportAutofilterList(const WebSlice* param);

/*!
 * @brief 服务端向内核发送数据
 * @param type : ServerNotifyType
 * @param slice : WebSlice
 */
WEB_EXPORT WebInt NotifyFromServer(ServerNotifyType type, const WebSlice *slice);

/*!
 * @brief 拆分数据到新文档
 * @param path : 拆分到指定目录 
 */
WEB_EXPORT WebInt SplitToNewFile(const UserContext* userContext, const char* path, const WebSlice* param);


/*!
* @brief 把一个应用信息给导出到另外一个文件上去。
*/
WEB_EXPORT WebInt ExportAirApp(const WebSlice* param);


/*!
* @brief 服务端向内核发送新增的订阅区域，内核返回已有订阅区域和新增区域的整合结果
*/
WEB_EXPORT WebInt GetMergedSubRanges(const WebSlice* param);
 /*
 * @brief 组件间数据联动导出图片，导出图片格式为PNG
 */
WEB_EXPORT WebInt ExecCopyImg(const UserContext* userContext, WebSlice *commands);

/*!
* @brief 服务端向内核查询获取本地图片数据
*/
WEB_EXPORT WebInt GetLocalImg(const UserContext* userContext, const WebSlice* commands, WebSlice* output);

/*!
* @brief 数据分析与内核无关接口调用, 规避协作场景下的undo redo
*/
WEB_EXPORT WebInt ExecDataAnalyze(const UserContext* userContext, const WebSlice* commands, WebSlice* output);

/*!
* @brief 服务端中断内核功能执行
*/
WEB_EXPORT WebInt SetCancel(const WebSlice* param);

/*!
* @brief 服务端向内核请求全量CommonLog数据
*/
WEB_EXPORT WebInt GetInitCommonLog();

/*!
* @brief 服务端向内核请求开启/关闭CommonLog
*/
WEB_EXPORT WebInt SetEnableCommonLog(bool bEnable);

#endif //__WEB_ET_API_H__
