#ifndef __LIST_OBJECTS_HELPER_H__
#define __LIST_OBJECTS_HELPER_H__

#include "etstdafx.h"

namespace ListObjectsHelper
{
    HRESULT GetListObjects(IKWorkbook *workbook, IDX sheetIdx, ListObjects **listObjects);
    HRESULT GetListObject(IKWorkbook *workbook, IDX sheetIdx, WebStr name, ListObject **listObject);
    HRESULT GetListObject(IKWorkbook *workbook, IDX sheetIdx, IDX tableIdx, ListObject **listObject);
    bool IsValidFristChar(WCHAR x);
    bool IsValidChar(WCHAR x);
}

#endif // __LIST_OBJECTS_HELPER_H__