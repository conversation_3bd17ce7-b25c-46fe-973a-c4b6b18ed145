﻿#ifndef __KCON_ENTRYS_H__
#define __KCON_ENTRYS_H__

#include "etshared/etshareapplication.h"

namespace wo
{

class KApiEntry : public QLibrary
{
public:
	KApiEntry() : QLibrary(krt::dirs::resources() + "/etapi") {}
	~KApiEntry(){}
};

class KApiEntryV8 : public QLibrary
{
public:
	KApiEntryV8() : QLibrary(krt::dirs::resources() + "/etapiv8") {}
	~KApiEntryV8(){}
};

class KEntrysBase : public QLibrary
{
public:
	KEntrysBase();
	~KEntrysBase();

	IKCoreGlobalEntry*	coreEntry();

protected:
	void TerminateCoreEntry();

private:
	IKCoreGlobalEntry*	m_coreEntry;

private:
	typedef IKCoreGlobalEntry* (WINAPI*_func_KSGetCoreGlobalEntry)();
	static _func_KSGetCoreGlobalEntry m_funcCoreEntry;
};

class KEntrys : public KEntrysBase
{
public:
	KEntrys();
	~KEntrys();

	static KEntrys*	getInstance();
	IKApiGlobalEntry* apiEntry();
	IKApiGlobalEntry* apiEntryV8();
	bool loadApi();
private:
	static KEntrys*	g_entry;
	IKApiGlobalEntry*	m_apiEntry;
	IKApiGlobalEntry*	m_apiEntryV8;
	static KApiEntry*	g_apiEntryModule;
	static KApiEntryV8*	g_apiEntryModuleV8;
	bool m_bLoadApi;

private:
	typedef IKApiGlobalEntry* (WINAPI* _func_KSGetApiGlobalEntry)();
	static _func_KSGetApiGlobalEntry m_funcApiEntry;
	static _func_KSGetApiGlobalEntry m_funcApiEntryV8;

	void _LoadMsoApiEntry();
	void _LoadKsoApiEntry();

};
}


#endif // __KCON_ENTRYS_H__
