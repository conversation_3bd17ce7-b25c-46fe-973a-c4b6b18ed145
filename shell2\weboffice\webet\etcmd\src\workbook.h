#ifndef __WORKBOOK_H__
#define __WORKBOOK_H__

#include "etstdafx.h"
#include "etshared/etshareapplication.h"
#include "webbase/binvariant/binwriter.h"
#include "kso/theme/theme_i.h"
#include "etcore/et_core_basic.h"
#include "wo/core_stake.h"

#include "revision_ext.h"
#include "et_task_executor.h"
#include "op_transform/et_op_tf.h"
#include "webetlink.h"
#include "webbase/revision_context.h"
#include "wo/et_revision_context.h"
#include "wo/wo_msgType_helper.h"
#include <public_header/webcommon/src/woidset.h>
#include "dbsheet/db_cohistories/db_auth_query_cohistories.h"

#include "condition_format_cache.h"
#include "userorganizes.h"
#include "dv_custom_list_cache.h"
#include "wobatchtrans.h"
#include "util.h"
#include "helpers/jsapi_record.h"
#include "kcoremetrics.h"
#include "ksignalmetrics.h"

#ifdef Q_OS_UNIX
#include <chrono>
#include "autofilteritemshelper.h"
#else //windws 下简单模拟
namespace std
{
	namespace chrono 
	{
		namespace system_clock
		{
			typedef ULONGLONG time_point;
			typedef ULONGLONG duration;

			inline time_point now() 
			{
				return ::GetTickCount64();
			}
		}
		typedef ULONGLONG milliseconds;
	}
}
#endif

struct WebPicData;

namespace wo
{
struct BlockPoint;
class KwCommand;
class EtQueryExecutor;
class DocMemoStat;
class PictureUploadHelper;
class KUserOrganizesCache;

namespace RangeSliceHelper
{
	class KRangeSliceHelper;
}

// 增加单元格修改记录后， BLOCK 中的 CellRec 数量变少了，导致批量操作数量变多了。
// 在 master 上填充一整列大约是 8257536， 现在是 9830400. 需要调大避免无法 redo
// 要修正这个问题，可能需要后续把 BLOCK 大小改为 2 页（8K）
constexpr UINT SerialComplexityThreshold = 10*1024*1024; // 10M
constexpr int kCalcDirtyRegionThreshold = 100; // ms

struct SImageDownload
{
	std::vector<ks_stdptr<drawing::AbstractShape>> listShape;
	std::vector<ks_string> listConid;
	std::vector<WebID> listSheetObj;
};

struct ImportrangeContext
{
	uint32_t recalcResVer = 0;
	std::map<ks_wstring, std::chrono::steady_clock::time_point> downloadUrlFiles;
	std::map<ks_wstring, std::chrono::steady_clock::time_point> downloadFileIdFiles;
	bool crossReclculating = false;
	bool isImportrangeCalcInited = false;
	bool isMergeFileNetDiskInit = false;
	bool mergeFileReclculating = false;
	std::set<ks_wstring> downloadMergeFiles;


	// 打开文件更新时跨book状态
	bool bCbReferInitDone = false;
	bool bCbReferUpdateFailed = false;
	
	uint32_t getRecalsResVer()
	{
		return recalcResVer;
	}

	void advanceRecalsResVer()
	{
		++recalcResVer;
	}
};

struct CoreServSettings
{
	WebFileSize fileSizeLimit = 2 * 1024 * 1024;
};

struct FileSaveInfo {
	WebFileSize size;
	int version;
	bool bSaved = false;
};

struct KEtPerfStat
{
	uint32_t writeCellValueCount = 0;
	uint32_t writeCellFormatCount = 0;
};

class EtWebIdSet : public WebIdSet
{
public:
	void setSyncObjs(std::vector<WebID>* pSyncObjs)
	{
		m_pSyncObjs = pSyncObjs;
	}
	void setRemoveObjs(std::unordered_set<WebID>* pRemoveObjs)
	{
		m_pRemoveObjs = pRemoveObjs;
	}
	virtual void insert(AbsObject* obj) override
	{
		if (!obj)
			return;

		if (m_pRemoveObjs && !m_pRemoveObjs->empty())
		{
			ASSERT(FALSE);
			WOLOG_ERROR << "RemoveObjs.size:" << m_pRemoveObjs->size();
		}

		if (idset()) {
			auto p = idset()->insert(obj->objId());
			if (p.second && m_pSyncObjs)
			{
				m_pSyncObjs->emplace_back(obj->objId());
				/*if(m_pSyncObjs->size() != idset()->size()) 
				{
					ASSERT(FALSE);
					WOLOG_ERROR << "SyncObjs.size:" << m_pSyncObjs->size() << " idset.size:" << idset()->size();
				}*/
			}
		}
	}
	virtual void remove(AbsObject* obj) override 
	{
		if (!obj)
			return;
		removeById(obj->objId());
	}
	bool removeById(WebID objId) 
	{
		// 先记起来删了哪些. 最后再统一调整syncObjs, 这里先不调
		if (idset()) {
			auto res = idset()->erase(objId);
			if (res != 0 && m_pRemoveObjs)
				m_pRemoveObjs->emplace(objId);
			return res != 0;
		}
		return false;
	}
    virtual void clear() override { ASSERT(FALSE); }
private:
	std::vector<WebID> *m_pSyncObjs;
	std::unordered_set<WebID> *m_pRemoveObjs;
};

class ApiCallTimeStat;
class KEtWorkbook;

#ifdef _DEBUG
constexpr int MAX_RECORD_CONNS_SIZE = 5;
#else
constexpr int MAX_RECORD_CONNS_SIZE = 2000; // 最多记录最近2000个。
#endif

class KConnsSvrInfo
{
public:
	KConnsSvrInfo();
	void addSvrInfo(WoConnFrom from, WoConnScene scene, WoConnLife life, int v);
	int getSvrConnFromCnt(int idx) const;
	int getSvrConnSceneCnt(int idx) const;
	int getSvrConnLifeCnt(int idx) const;
	bool getHasSvrConnInfo() const { return m_hasInfo; }
	void resetSvrInfo();
	
private:
	bool m_hasInfo;
	int m_svrConnFrom[_WoConnFromCount];
	int m_svrConnScene[_WoConnSceneCount];
	int m_svrConnLife[_WoConnLifeCount];
};

class KConnsReuseInfo
{
	struct ConnInfo
	{
		PWSTR m_connID;
		bool m_hasEverSharedId;
		ConnInfo(): m_connID(nullptr), m_hasEverSharedId(false)
		{}
		void clearConn() { delete [] m_connID; resetConn(); }
		void resetConn() { m_connID = nullptr; }
	};
public:
	KConnsReuseInfo(const char * tag);
	~KConnsReuseInfo();
	
	bool onNewUserConn(PCWSTR connId, PCWSTR userId, bool isCommon);
	void addConn(PCWSTR connId, bool hasSharedId);
	bool removeConn(PCWSTR connId, bool & outHasSharedId);
	void resetCnt() { m_reuseCnt = m_reuseCommonCnt = m_illReuseCommonCnt = m_illReuseHasSharedIdCnt = 0; }
	int getReuseCnt() { return m_reuseCnt; }
	int getReuseCommonCnt() { return m_reuseCommonCnt; }
	int getIllReuseCommonCnt() { return m_illReuseCommonCnt; }
	int getIllReuseHasSharedCommCnt() { return m_illReuseHasSharedIdCnt; }
	void incIllReuseCommonCnt(IKETUserConn * conn);
private:
	PWSTR createWstr(PCWSTR wsz);
private:
	std::unordered_map<PCWSTR, int, util::StrHasher, util::StrEqual> m_conns; 
	ConnInfo m_connsLst[MAX_RECORD_CONNS_SIZE];
	const char * m_tag;
	
	int m_curLstIdx;
	int m_reuseCnt;
	int m_reuseCommonCnt;
	int m_illReuseCommonCnt;
	int m_illReuseHasSharedIdCnt;
	bool m_hasEverSharedId;
};

class KConnsTypeInfo
{
public:
	bool hasCnt() { return m_cnt != 0; }
	int getCnt() { return m_cnt; }
	int getCommCnt() { return m_commCnt; }
	int getHasQueryInitCnt() { return m_commHasQueryInitCnt; }
	int getHasExportSvgCnt() { return m_commHasExportSvgCnt; }
	int getHasQueryInitOrExportSvgCnt() { return m_commHasQueryInitOrExportSvgCnt; }
	int getHttpCallCnt() { return m_hasHttpCallCnt; }
	int getCommHasSharedId() { return m_commHasSharedId; }
	int getHasSharedId() { return m_hasSharedId; }
	int getHasUserJoinCnt() { return m_commHasUserJoinCnt; }
	int getHasSharedIdNoQueryInitCnt() { return m_hasSharedIdNotQueryInitCnt; }
	void addValue(IKETUserConn * conn, int v);
	void resetCoreInfo();

	const KConnsSvrInfo & getSvrConnInfo() { return m_svrConnInfo; }
	void resetSvrInfo() { m_svrConnInfo.resetSvrInfo(); }
	
private:
	int m_cnt = 0;
	int m_commCnt = 0;
	int m_commHasQueryInitCnt = 0;
	int m_commHasExportSvgCnt = 0;
	int m_commHasQueryInitOrExportSvgCnt = 0;
	int m_commHasUserJoinCnt = 0;
	int m_hasHttpCallCnt = 0;
	int m_commHasSharedId = 0;
	int m_hasSharedId = 0;
	int m_hasSharedIdNotQueryInitCnt = 0;
	
	// svr conn info
	KConnsSvrInfo m_svrConnInfo;
};

class KBookCollectInfo: public IBookCollectInfo
{
public:
	KBookCollectInfo();
	~KBookCollectInfo();
	void addDbSheetCount(int v) override;
	void addDbViewCount(int v) override;
	void addVisibleRecordCount(int v) override;
	void addVisibleFieldCount(int v) override;
	
	bool hasDbViewsCollectInfo();
	ks_wstring getSingalDbViewsCollectInfo();
	
	void onNewUserConn(IKETUserConn * conn, bool & outIsCleanReuse, bool & outIsQuitReuse) override;
	void onMarkDelayQuit(IKETUserConn * conn, bool markValue) override;
	void addCleanConn(IKETUserConn * conn);
	void onNotFoundUserQuit(PCWSTR connId);
	
	void onUserQuit(PCWSTR connId, bool hasSharedId);
	void checkIllegalReuse(IKETUserConn * conn, bool isHttpCall);
	
	bool checkIsCollectReuseAndNewConn();
	int getReuseConnCnt() { return m_reuseCnt; }
	int getNewConnCnt() { return m_newConnCnt; }
	
	ks_wstring takeCleanConnInfo();
	ks_wstring takeMarkQuitConnInfo();
	
	ks_wstring getCleanConnSvrFrom();
	ks_wstring getCleanConnSvrScene();
	ks_wstring getCleanConnSvrLife();
	void resetCleanConnSvrInfo();
	
	ks_wstring getMarkQuitConnSvrFrom();
	ks_wstring getMarkQuitConnSvrScene();
	ks_wstring getMarkQuitConnSvrLife();
	void resetMarkQuitConnSvrInfo();
	int getMarkQuitConnCnt() { return m_markQuitTypeInfo.getCnt(); }
	bool hasMarkQuitConnSvrInfo() { return m_markQuitTypeInfo.getSvrConnInfo().getHasSvrConnInfo(); }
	
	ks_wstring takeNewConnInfo();
	ks_wstring getNewConnSvrFrom();
	ks_wstring getNewConnSvrScene();
	ks_wstring getNewConnSvrLife();
	void resetNewConnSvrInfo() { m_newConnSvrInfo.resetSvrInfo(); }
	
	ks_wstring takeReuseConnInfo();
	
	void resetDbViewsInfo();
private:
	void resetReuseInfo();
	void resetNewConnInfo();
	
	ks_wstring getConnSvrFrom(const KConnsSvrInfo & typeInfo, const char * logTag);
	ks_wstring getConnSvrScene(const KConnsSvrInfo & typeInfo, const char * logTag);
	ks_wstring getConnSvrLife(const KConnsSvrInfo & typeInfo, const char * logTag);
	
private:
	KConnsReuseInfo m_cleanConnsReuseInfo;
	KConnsReuseInfo m_userQuitConnsReuseInfo;
	
	KConnsTypeInfo m_markQuitTypeInfo;
	KConnsTypeInfo m_cleanTypeInfo;
	
	int m_newConnCnt = 0;
	int m_newCommConnCnt = 0;
	KConnsSvrInfo m_newConnSvrInfo;
	
	int m_reuseCnt = 0;
	int m_collectNewReuseCnt = 0;
	
	int m_dbSheetCont;
	int m_dbViewCount;
	int m_dbViewVisibleRecCnt;
	int m_dbViewVisibleFieldCnt;
};

typedef std::pair<IDX, std::vector<RANGE>> LastTaskSelection;
class KEtWorkbook : public KRevisionControl
{
	friend class CustomWebAcptHelp;
	friend class KEtBatchTransHelper;
	typedef KRevisionControl base_type;
public:
	enum CalcStatus
	{
		finish,
		suspend,
		calculating,
	};
	class InitCache
	{
	public:
		IWebSlice* getSignalSlice();
		void makeSignalSlice(binary_wo::BinWriter& bw, IKETUserConn* pUserConn);
		void makeBlockPoint(std::vector<BlockPoint>& vbp);
		void resetConnByCache(IKETUserConn* pConn);

		bool hasSheet(WebID objSheet);
	private:
		ks_stdptr<IWebSlice> m_spSignalSlice;
		std::vector<::WoBlockPoint> m_vbp;
		std::vector<WebID> m_serialObj;
		std::unordered_set<WebID> m_sheetsCompleteInit;
	};

	class BroadcastData
	{
	public:
		BroadcastData()
		{
			m_slice.data = nullptr;
			m_slice.size = 0;
		}

		~BroadcastData()
		{
			if(m_slice.data)
				free(m_slice.data);
		}

		void reset(WebName name, binary_wo::BinWriter& bin, const char* connID);
		void broadcast();

	private:
		std::string m_connID;
		WebSlice m_slice;
		WebName m_name;
	};
	struct UndoRedoStat
	{
		UINT m_undoFailTime = 0;
		UINT m_undoTotalCnt = 0;
		UINT m_redoFailTime = 0;
		UINT m_redoTotalCnt = 0;
	};

	KEtWorkbook(etoldapi::_Workbook*, et::KConApplication*, WebLogFunc, QString);
	~KEtWorkbook();
	FILEFORMAT GetFileFormat();
	HRESULT	Save(PCWSTR savePath, int32*);
	HRESULT	SaveAsFormat(PCWSTR savePath, PCWSTR format);
	HRESULT	SaveAsLiteVersion(PCWSTR savePath, int32*, KComVariant saveAsLiteVersionParam);
	HRESULT ExecEncryptDecrypt(PCWSTR savePath, SaveAsMode saveAsMode);
	HRESULT ExportSheetToCSV(PCWSTR savePath, IDX sheetIdx, bool bSkipBlankCols = false);
	HRESULT ExportToLocalFile(PCWSTR savePath, FILEFORMAT fileFormat, KEtRevisionContext& ctx);
	HRESULT ExportSheetToTxt(FILTERMEDIUM* fm, IDX sheetIdx, WCHAR delimiter);
	HRESULT ExportKSheetToXlsx(PCWSTR savePath, KEtRevisionContext& ctx);
	HRESULT ExportToDb(PCWSTR) const;
	HRESULT ExportXlsxToKSheet(PCWSTR) const;
	HRESULT ExportDbToKSheet(PCWSTR) const;
	HRESULT ExportKSheetToKSheet(PCWSTR) const;

	WebInt CheckDownLoadImg(IKWorkbook* pBook, ISheet *spSheet, bool bForceAttachmentReDownload = true);
	WebInt transformTasks(KwTasks& tasks, IEtRevisionContext* ctx);
	WebInt transformClientTasks(KwTasks& tasks, WebInt transDataBase, WebInt transTaskBase, IEtRevisionContext* ctx);
	WebInt executeTasks(KwTasks& tasks, IEtRevisionContext* ctx);
	WebInt serializeInitData(IEtRevisionContext* ctx, binary_wo::BinWriter* bw);
	WebInt serializeVersions(IEtRevisionContext* ctx, binary_wo::BinWriter* bw);
	WebInt execTasksDirect(KwTasks& tasks, IRevisionContext* ctx);	 // no transform
	WebInt undo(IRevisionContext* ctx, PCWSTR versionTag, bool allowPartialSucc = true, int undoStep = 1);
	WebInt redo(IRevisionContext* ctx, PCWSTR versionTag, int redoStep = 1);
	WebInt CalculateEAF(const WebSlice* slice);
    IWebIdSet* getConnSerialObjsSet(IRevisionContext* ctx);
	void notifyStale(const WebID objId);
	using base_type::getVersionMgr;

	bool isBookProtectedWithHiddenProperty();
	bool isRangeIncludeProtectedCols(ISheet* pSheet, const RANGE& rg);
 	bool isSecDoc() const;
	void doClearJobAfterExecCmd();  //命令执行完后(executeTasks,execTasksDirect)，释放满足释放条件的命令(undo,redo也不会访问的命令)
	void rebuildSyncObjsVec();
	void pushVersionTagUndoIndex(PCWSTR tag, WebInt idx) override;
	void getVersionTagUndoIndex(PCWSTR tag, std::vector<WebInt>& undoIndex) override;
	void markUndoVersionTag(PCWSTR tag) override { m_undoVersionTag.push_back(tag); };
	std::vector<ks_wstring> popUndoVersionTag();
public:	
	virtual void onSubmitShapePicture() override;
	virtual bool getShapePicData(drawing::AbstractShape* shape, QByteArray& sha1, QByteArray& data, int dpi) override;
	virtual KwVersion* confirmTask(UINT32 userInnerID, KwTask* task, KwVersion::BanUndoType banUndo, WebInt baseVersion, bool isRollback) override;
	virtual void beginTransact(WebInt);
	virtual void endTransact(bool, IRevisionContext* pCtx);
private:
	void serialModifyShapePics(KWebAcceptor* acpt) override;
	int serialNewObjects(IRevisionContext* ctx, KwVersion* ver, KWebAcceptor* acpt) override;
	int serialModifyObjects(AbsObjects& objVec, KWebAcceptor* acpt) override;
	void onSerialVersionStart() override;
	void onSerialVersionEnd(int cnt, int binSize) override;
	
private:
	void baseBeginTransact(WebInt v) { base_type::beginTransact(v); }
	void baseEndTransact(bool v, IRevisionContext * pCtx) { base_type::endTransact(v, pCtx); }
	KwVersion* baseConfirmTask(UINT32 userInnerID, KwTask* task, KwVersion::BanUndoType banUndo, WebInt baseVersion, bool isRollback)
	{ return base_type::confirmTask(userInnerID, task, banUndo, baseVersion, isRollback); }
	
private:
	virtual bool isOptBySerializeInit(IRevisionContext* ctx) override;
	void onSerialVersionFailed(SerialVersionCode code) override;
	virtual uint getSerializeLimit() override;

public:
	etoldapi::_Application*		GetCoreApp();
	etoldapi::_Workbook* GetCoreWorkbook() const;
	IBook * GetBook() { return m_book; }
	KEtOpTrsfmt* 		GetOpTransform();
	KRevisionControl* 	GetRevisionCtrl();
	EtQueryExecutor*	GetQueryExecutor();
	std::shared_ptr<DbAuthQueryCoHistories::HandlerFactory> GetDbAuthQueryCoHistoriesHandlerFactory();
	DocMemoStat*		GetMemoStat();
	void				SetExportFmla(bool);
	void				SetCreatorUserID(PCWSTR);
	void				UpdateDbRecords(ISheet *, double);
	void				UpdateDbFields(ISheet *);
	void				UpdateDbViews(ISheet *);
	void				UpdateDbSheets();
	void				RemoveInvalidTimerTasks();
	bool				IsExportFmla();
	PCWSTR				getFileId();
	IET_DocSlim*		GetDocSlim();
	PictureUploadHelper * GetPictureUploadHelper();
	RangeSliceHelper::KRangeSliceHelper*  GetRangeSliceHelper();
	void				SetCoreSettings(const WebetSettings *setting);
	void 				SetFileVersionSize(WebInt version, WebFileSize size, bool bInit = false);

	WebID				GetDefInitSheet(PCWSTR name, IKWorksheet **ppWorksheet = NULL);
	WebID				GetFirstVisibleSheet(IKWorksheet **ppWorksheet = NULL, SHEETTYPE st = stUnknown);
	IShapeCollect*		GetShapeCollect() { return &m_shapeCollect; }
	void setStaleNotify(IStaleNotify* pNotify) override;
//tools
public:
	BMP_PTR GetBMP();
	void SetValidateName(PCWSTR name, UINT uNameLen);
	void ViewRect2CoreRect(drawing::ShapeVisual* pShapeVisual, QRectF& view, QRectF& core);
	void CoreRect2ViewRect(drawing::ShapeVisual* pShapeVisual, QRectF& core, QRectF& view);
	void SerialVerStatusInfo(binary_wo::BinWriter& bw, bool invalidateFmlaRes = false);
	void SerialCalcStatus(binary_wo::BinWriter& bw);
	void SerialDiagnosisStatus(binary_wo::BinWriter& bw);

public:
	ks_stdptr<etoldapi::Range> CreateRangeObj(const RANGE&);
	ks_stdptr<etoldapi::Range> CreateRangeObj(const std::vector<RANGE> &rgVec);
	ks_stdptr<etoldapi::Range> CreateRangeObj(interface IKRanges *pRgs);
	void CreateIKRangeObj(const std::vector<RANGE> &rgVec, IKRanges** spRanges);

public:
	WebInt UpdateShapes(WebID objSheetOrBook, const std::vector<uint32>& vecShapeIds, const std::vector<uint32>& shapeLimitSizes, const ks_wstring& updateType, IEtRevisionContext* ctx, binary_wo::BinWriter& writer);
    HRESULT QueryAutoFilterInfos(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj param, ISerialAcceptor* acpt);
	HRESULT QueryAutoFilterCustomValues(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, ISerialAcceptor* acpt);
	HRESULT QueryAutoFilterValues(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj param, ISerialAcceptor* acpt);
    HRESULT QueryAutoFilterInverse(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj param, ISerialAcceptor* acpt);
    HRESULT QueryAutoFilterSetTmpValues(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj param, ISerialAcceptor* acpt);
    HRESULT QueryAutoFilterCategoryCount(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj& param, ISerialAcceptor* acpt);
    HRESULT QueryAutoFilterFormats(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, ISerialAcceptor* acpt);
	HRESULT QueryAutoFilterCondition(PCWSTR filterID, const RANGE& rg, ISerialAcceptor* acpt);
    HRESULT SetAutoFilterNodeStatus(binary_wo::VarObj values, ValuesNode* pValuesRoot);

	HRESULT getCellComments(const RANGE& rg, bool bCreate, ICellComments** ppCmt);
	HRESULT getWoComment(const RANGE& rg, bool bCreate, IWoComment** ppCmt);

	void clearInitCache();
	InitCache* setupInitCache();
	InitCache* getInitCache() { return m_initCache; }
	void markSyncCellListDirty();
	
	void BroadcastChanged(binary_wo::BinWriter& bin, const char* connID);
	void SignalNow(MsgType msg, binary_wo::BinWriter& bin, const char *connID);
	void DoIdle(DWORD& calcDetail, bool* pResumeCalcHappened);
	void NotifyCrossBooksUpdate();
    void NotifyCustomListUpdate();

	int GetThemesCount();
	ITheme* GetTheme(size_t);

	virtual void setLastTaskSelection(KwTask *pTask) override;
	LastTaskSelection* getLastTaskSelection();
	void setLastTaskSelection(binary_wo::VarObj param);
	void setLastTaskSheet(INT32 sheetIdx);
	void resetLastTaskSelection();

	void serializeSheets(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor*);
	void serializeXfs(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor*);
	void serializeCmtContainerBL(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor*);
    void serializeUserOrganizeInfo(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor* acpt);
	void serializeVersionsModifyRanges(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor* acpt);

    HRESULT FetchCurrentUserOrganizeInfo();
    HRESULT ResetCurrentUserOrganizeInfo();
    bool RefetchAllUserOrganizes();

	CalcStatus getCalcStatus();
	CALC_DIAGNOSIS_STATUS GetCalcDiagnosisStatus() const { return m_calcDiagnosisStatus; }
	void updateCalcStatus(bool isCurVerReCalc, KEtRevisionContext*); // 注意！！！目前仅供EtTaskExecutor调用，调用时机很重要，调用前请搞清楚原理。
	void updateCalcDiagnosisStatus();
	void resumeCalculate(DWORD& calcDetail, bool* pResumeCalcHappened);
	bool DbtCalcCellsNeedAddDirty() const;
	void SetDbtCalcCellsNeednotAddDirty();
	void DownloadObjectDone(const WebDownloadArg* pDownloadArg, int code, const char *path);
	INT32 getEafResVersion() const;
	bool hasCalculatingEaf() const;
	
	void clearCellImgHiddenCache();
	void ensureCellImgHiddenCache(IEtRevisionContext* ctx);
	bool checkCellImgProtection(const drawing::AbstractShape* shape, IEtRevisionContext* ctx);

	bool ImportrangeCalcInited() const { return m_importrangeCtx.isImportrangeCalcInited; } 
	bool InitImportrangeCalc();
	bool InitMergeFileNetDisk();
	bool asyncGetNetDiskFile(PCWSTR userid, PCWSTR url, DownloadFileTask, std::vector<RANGE>* ranges = nullptr);
	void onGetNetDiskFileDone(PCWSTR userid, PCWSTR url, DownloadFileTask);
	ImportrangeContext& getImportrangeContext() { return m_importrangeCtx; }
	void collectVersionUndoTag(IRevisionContext* ctx, std::vector<const UNDO_TAG*>&, bool* bHasRecalculate);

	HRESULT OnGetNetFileByUrlDone(KEtRevisionContext *ctx, NetFileRes res, PCWSTR url, PCWSTR fileId);
	void UpdateCrossBookLinks(KEtRevisionContext *ctx);
	bool CollectCrossBookRefers(std::set<ks_wstring> *fileIds, bool *isComplete);
	void RecalculateImportRange(KEtRevisionContext *ctx);
	void AfterCrossRefRecalculate();
	void AfterFileOpen();
	void SetCellKeyWordHighlight();
	const FileSaveInfo& GetFileSaveInfo();
	const CoreServSettings& GetSerCoreSettings();
	bool isFileSizeExceedLimit();
	bool hasDashBoard();

	KEtPerfStat& mutablePerfStat()
	{
		return m_perfStat;
	}

	void writePerfStatLog();
	static IKAutoFilter* getAutoFilterInRange(ISheet* pCoreSheet, const RANGE& rg, PCWSTR filterID);
	void ClearMistakenDocAuthorName(const char* dateBefore, const char** cleanAuthors, int authorCount);

	void SetExportLeftTopCell(IKWorkbook*, std::vector<INT32>& backup);
	void RecoveryExportLeftTopCell(IKWorkbook*, const std::vector<INT32>& backup);

	ServiceType getServiceType() const;
	void setServiceType(ServiceType s);

    KDvCustomListCache* getCustomListCalcCache() { return m_pDvCustomListCache; }
	ConditionFormatCache *getCfCache() { return &m_cfCache; }
	void CollectAutoPasswordMaster();
	void CollectAutoPasswordMasterInner(const std::vector<ks_wstring>& vecUuid);

	void updateRenderShape(IKWorksheet * pWorksheet);
	void readThemesIfNeed();
	
	UndoRedoStat GetUndoRedoStat() const { return m_undoRedoStat; }

	void UpdateAutoPasswordMaster(const binary_wo::VarObj& list, PCWSTR master, IAutoPasswordMgr* pMgr);
	void setNeedGetAutoPasswordMaster(bool bNeedGetAutoPasswordMaster) { m_bNeedGetAutoPasswordMaster = bNeedGetAutoPasswordMaster; };

	int getStaleOnNotExecCount() { return m_staleOnNotExecCount; }
	void incReInitCount() { ++m_reInitCount; }
	int getReinitCount() { return m_reInitCount; }
	int getInspectFailCount() { return m_inspectCount; }
	int getOptInitCount() { return m_optInitCount; }
	int getMaxSerialVerCount() { return m_maxSerVerCount; }
	WebInt getSerialVerCount() { return m_serVerCount; }
	WebInt getHitVerCount() { return m_hitVerCount; }
	int getMaxSerialVerBwLen() { return m_maxSerVerBwLen; }
	
	KCoreMetric & coreMetric() { return m_coreMeric; }
	KBookCollectInfo & collectInfo() { return m_collectInfo; }
	KSignalMetrics & signalMetric() { return m_signalMetrics; }

	bool GetBookDocSlimNeedCheck() { return m_bBookDocSlimNeedCheck; }
	void SetBookDocSlimNeedCheck(bool bNeedCheck) { m_bBookDocSlimNeedCheck = bNeedCheck; }

	ApiCallTimeStat& getApiCallTime() { return *m_apiCallTime; }
	void setApiCallTime(ApiCallTimeStat* callTime);
	void moveObjectRefToTask(KwTask * pTask) { pTask->addObjectRef(std::move(m_refObj)); m_refObj.clear();}
	void addObjectRef(IDispatch* p) { m_refObj.insert(p); }
	std::vector<JsApiExecRecord> & getApiRecords() { return m_vecCacheApi; }
	KEtBatchTransHelper& getBatchTransHelper() { return m_batchTransHelper; }

	void incCmdCount() { m_cmdCount++; }
	void broadcastDelayData();

	void setReserveCrossBookDbData(bool bNeedReserve) { m_bReserveCrossBookDbData = bNeedReserve; }
private:
	bool isDelayBroadcast();
	void _updateRenderView();
	void readThemes();
	void _updateShapeImage(WebID objSheet, const drawing::AbstractShape* shape, double scale, IEtRevisionContext* ctx, binary_wo::BinWriter& writer);
	void _downloadPictureImage(const drawing::AbstractShape* shape, IKBlipAtom* pBlipAtom, ks_bstr& path, WebID objSheet, IEtRevisionContext* ctx);
	bool _downloadImage(const drawing::AbstractShape* shape, WebID objSheet, IEtRevisionContext* ctx);
	void _updateShapeProps(const drawing::AbstractShape* shape, IEtRevisionContext* ctx, binary_wo::BinWriter& writer);
	void _serialShapeRect(const drawing::AbstractShape* shape, binary_wo::BinWriter& writer, IEtRevisionContext* ctx);
	void _updateShapeOriginalPic(const drawing::AbstractShape* shape, binary_wo::BinWriter& writer, IEtRevisionContext* ctx);
    void _serialiseSortInfo(IKAutoFilter *, int, ISerialAcceptor *);
	void _serialiseSortInfo(IKAutoFilter *, int, ISerialAcceptor *, AutoFilterFormatItemsHelper& helper);
    void _SerialiseAutoFilterSearchSortInfo(const binary_wo::VarObj varObj, ISerialAcceptor* acpt);
	drawing::ShapeTree* getShapeTree(WebID objSheetOrBook, IEtRevisionContext* ctx, binary_wo::BinWriter& writer);
	CalcStatus _updateCalcStatus(bool isCurVerReCalc, KEtRevisionContext*); // 自动重算时只要计算链为空就返回真，手动计算时计算链为空，且最后一次重算完成后没有引发过计算才能算。
	void _updateCalcStatusSimple();
	void _updateCalcStatusOnOpen();
	void _updateCalcStatusOnSave();
	void _setCalcStatus(CalcStatus);
	void _collectDirtySparklineRefAreas();   // 生成迷你图引用区域版本，并收集当前工作薄引用区域标脏的迷你图和迷你图组合
	void _clearDirtySparklineParts(KSparklineRefAreaVersion* pVersion = NULL);
	void _recoredFileSizeChange(PCWSTR savePath);
	HRESULT checkQueryFilterRange(IEtRevisionContext *ctx, const RANGE& filterRg, const RANGE& fieldRg);
	bool GetBuildInPropertyValue(DocumentProperties *pDocumentProperties, LPCWSTR pPropName, DocumentProperty **ptrDoc, QString *pValue);
    HRESULT DownloadOnlineImage(IKWorkbook* pBook);
	void onCleanChildProc(IRevisionContext* ctx);
	void onDataChanged(IRevisionContext* ctx);
	void initLastTaskSelection();
	void setLastTaskSelectionRanges(binary_wo::VarObj param, std::vector<RANGE> &);
	void readSortParam(const binary_wo::VarObj varObj, SortParam& sortParam);

private:
	IBook* m_book;
	PictureUploadHelper *		m_ptrPictureUploadHelper;
	RangeSliceHelper::KRangeSliceHelper* m_pRangeSliceHelper;
	ks_stdptr<etoldapi::_Application> 	m_ptrApp;
	ks_stdptr<etoldapi::_Workbook> 		m_ptrWorkbook;
	et::KConApplication* 			m_consoleApp;
	std::vector<ITheme *>		m_themes;
	bool						m_bExpFmla;
	InitCache*					m_initCache;
	std::chrono::system_clock::time_point	m_lastBroadcastTime;
	BroadcastData*				m_delayBroadcatData;
	EtQueryExecutor*			m_queryExecutor;
	LastTaskSelection           m_lastTaskSelection;
	ks_wstring					m_fileId;
	ks_stdptr<IET_DocSlim>		m_spDocSlim;
	bool						m_hasProtectionCache;
	bool						m_isBookHasHiddenCache;
	std::map<ks_string, SImageDownload*> m_mapDownloadImage;
	std::map<ks_string, SImageDownload*> m_mapDownloadAttachmentImage;
	std::set<ks_wstring> m_hiddenCellImgSetCahce;
	bool m_hasHiddenCellImgSetCache;
    EtWebIdSet m_idset;
	CalcStatus m_calcStatus;
	CALC_DIAGNOSIS_STATUS m_calcDiagnosisStatus = CALC_DIAGNOSIS_STATUS::HaventDiagnosed;
	FileSaveInfo m_lastSaveInfo;
	CoreServSettings m_serverSettings;
	
	ImportrangeContext m_importrangeCtx;
	std::unique_ptr<DocMemoStat> m_memoStat;
	std::shared_ptr<DbAuthQueryCoHistories::HandlerFactory> m_dbAuthQueryCoHistoiresHandlerFactory;
	KEtPerfStat m_perfStat;
	ServiceType m_serviceType{ ServiceType_Normal };
	
	KCoreMetric m_coreMeric;
	KBookCollectInfo m_collectInfo;
	KSignalMetrics m_signalMetrics;

	std::unordered_set<WebID> m_staleObjs;
	int m_staleOnNotExecCount;
	int m_reInitCount;
	int m_inspectCount;
	int m_optInitCount;
	int m_maxSerVerCount;
	WebInt m_serVerCount;
	WebInt m_hitVerCount;
	int m_maxSerVerBwLen;
	int m_cmdCount = 0;
	int m_lastDelayTime = 0;
	
	bool m_hasRemovedObjs;
	bool m_staleOnNotExec;

	bool m_dbtCalcCellNeedAddDirty;
	bool m_bNeedGetAutoPasswordMaster = false;
	bool m_batchTrans = false;

    KDvCustomListCache* m_pDvCustomListCache = NULL;
	ConditionFormatCache m_cfCache;
	mutable UndoRedoStat m_undoRedoStat;
	KEtBatchTransHelper m_batchTransHelper;
	std::vector<JsApiExecRecord> m_vecCacheApi; 

    et_sptr<class KUserOrganizesCache> m_pUserOrganizesCache;
    UserOrganizesInfo m_userOrganizesInfo;
	bool m_bBookDocSlimNeedCheck = false;
	
	ApiCallTimeStat* m_apiCallTime = nullptr;
	std::set<ks_stdptr<IDispatch>> m_refObj;
	double m_lastCmdCountUnit = 0;
	bool m_bReserveCrossBookDbData = false;
	std::unordered_map<ks_wstring, std::vector<WebInt>, util::StrHasher> m_versionTagUndoIdx;
	std::vector<ks_wstring> m_undoVersionTag;
};

class ApiCallTimeStat
{
public:
	ApiCallTimeStat(KEtWorkbook* pWorkbook)
		: m_pWorkbook(pWorkbook),
		m_begin(std::chrono::steady_clock::now())
	{
		m_pWorkbook->setApiCallTime(this);
	}
	~ApiCallTimeStat()
	{
		if (m_collect)
		{
			unsigned int curTime = m_pWorkbook->GetCoreWorkbook()->GetRunJsApiCoreTime();
			curTime += ((std::chrono::steady_clock::now()) - m_begin) / std::chrono::microseconds(1);
			m_pWorkbook->GetCoreWorkbook()->SetRunJsApiCoreTime(curTime);
		}
	}
	void ApiEnd()
	{
		m_pWorkbook->GetCoreWorkbook()->SetRunJsApiCoreTime(0);
	}
	void SetCollect(bool bCollect)
	{
		m_collect = bCollect;
	}
private:
	std::chrono::steady_clock::time_point m_begin;
	KEtWorkbook* m_pWorkbook = nullptr;
	bool m_collect = false;
};

class CustomWebAcptHelp
{
public:
	CustomWebAcptHelp(KEtWorkbook* wb, binary_wo::BinWriter* bw, IRevisionContext* ctx)
		: m_acpt(wb->GetShapeCollect(), bw, ctx, false, false)
		, m_wb(wb)
		, m_ctx(ctx)
	{

	}

	void begin()
	{
		m_acpt.setSerialObjsSet(m_wb->getConnSerialObjsSet(m_ctx));
		m_acpt.beginCollectNew();
	}

	void end()
	{
		KAutoCustomNewObjsFlag mcAutoFlag(m_wb->coreMetric());
		m_acpt.endCollectNew();
		m_acpt.addKey("newCustomObjs");
		m_acpt.beginArray();
		{
			m_wb->serialNewObjects(m_ctx, NULL, &m_acpt);
		}
		m_acpt.endArray();
	}

	ISerialAcceptor* getAcpt()
	{
		return &m_acpt;
	}

private:
	KEtWorkbook* m_wb;
	KWebAcceptor m_acpt;
	IRevisionContext* m_ctx;
};

}

#endif //__WORKBOOK_H__
