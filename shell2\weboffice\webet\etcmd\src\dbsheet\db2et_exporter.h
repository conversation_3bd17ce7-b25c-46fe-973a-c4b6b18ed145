﻿#ifndef __WEBET_DATABASE_DB2XLSX_EXPORTER_H__
#define __WEBET_DATABASE_DB2XLSX_EXPORTER_H__

namespace wo
{
class KEtWorkbook;

struct AttachmentInfo
{
	ks_wstring fileName;
	ks_wstring filePath;
	ks_wstring fieldName;
	int64_t attachmentWidth = 0;
	int64_t attachmentHeight = 0;
	bool isSuccess = true;
	bool isImage = true;
};

struct AttachmentPos
{
	EtDbId fieldId;
	EtDbId recordId;
	UINT index;

	bool operator==(const AttachmentPos& key) const
	{
		return fieldId == key.fieldId && recordId == key.recordId && index == key.index;
	}
	struct Hasher
	{
		size_t operator()(const AttachmentPos& key) const
		{
			size_t val = alg::HashCombine(std::hash<EtDbId>()(key.fieldId), std::hash<EtDbId>()(key.recordId));
			return alg::HashCombine(val, std::hash<UINT>()(key.index));
		};
	};
};

struct RecordInfo
{
	UINT sheetId;
	EtDbId fieldId;
	EtDbId recordId;
	int rowPos;
	int colPos;

	RecordInfo(UINT sheetId, EtDbId fieldId, EtDbId recordId, int rowPos, int colPos)
		: sheetId(sheetId), fieldId(fieldId), recordId(recordId), rowPos(rowPos), colPos(colPos)
		{
		}
};

using DbSheetsAttachmentInfoMap = std::unordered_map<UINT,std::unordered_map<AttachmentPos, AttachmentInfo, AttachmentPos::Hasher>>;

class KEtRevisionContext;
class Db2XlsxExporter
{
	ks_stdptr<IBookOp> m_spEtBookOp;

	PCWSTR getNumberFormat(IDbField*) const;
protected:
	IBook* m_pBook;
	_Workbook* m_pDbWorkBook;
	_Workbook* m_pEtWorkBook;
	KEtRevisionContext* m_pCtx = nullptr;

	virtual int getRowHeight() const;
	virtual int getColWidth(EtDbId, IDBSheetOp*) const;
	bool isFieldFiltered(ET_DbSheet_FieldType baseFieldType, ET_DbSheet_FieldType fieldType);
	bool isPersonalVisible(IDBSheetView *pView, KEtRevisionContext* ctx);
	HRESULT dbViewExportData(IDX, PCWSTR, const IDBIds*, const IDBIds*, IDBSheetOp*, IDBSheetView*, bool isColumnHeaderFormatExported = true);
public:
	Db2XlsxExporter(wo::KEtWorkbook*, _Workbook*, KEtRevisionContext* pCtx = nullptr);
	Db2XlsxExporter(_Workbook* pDbWorkBook, _Workbook* pEtWorkBook, KEtRevisionContext* pCtx = nullptr);
	virtual HRESULT Init();
	virtual HRESULT Exec() = 0;
	virtual ~Db2XlsxExporter();

    void setAttachmentData(bool exportCompressedBundle, DbSheetsAttachmentInfoMap* attachmentData = nullptr)
    {
        m_exportCompressedBundle = exportCompressedBundle;
        m_pAttachmentData = attachmentData;
    }

	void setIsExportAttachment(bool needExportAttachment)
	{
		m_exportAttachment = needExportAttachment;
	}

	void setNeedCopyDataFormat(bool needCopyDataFormat)
	{
		m_needCopyDataFormat = needCopyDataFormat;
	}

	bool getIsExceedMaxColumns()
	{
		return m_exceedMaxColumns;
	}

	void SetCopyContent(bool b) { m_copyContent = b; }
	void SetIsForAppendTemplate(bool b) { m_isForAppendTemplate = b; }

private:
	virtual HRESULT CopySheetName(_Worksheet*, PCWSTR);

	HRESULT removeEtField(IDX sheetIDX, int etColPos);
	HRESULT setHyperlink(_Worksheet*, RANGE&, const_token_ptr, IDbField*, IKHyperlinks*);
	HRESULT setHyperlinkRuns(_Worksheet*, RANGE&, const_token_ptr, IDbField*, IKHyperlinks*);
	HRESULT setNewAttachmentCol(_Worksheet*, IDbField*, UINT, int&);
	HRESULT setAttachmentHyperlink(_Worksheet*, RANGE&, IKHyperlinks* , const ks_wstring&, const ks_wstring&);
	void getAttachmentFileData(UINT sheetId, EtDbId fieldId, EtDbId recordId, int i, AttachmentInfo&);
	void setCellValueForAttachment(const_token_ptr, _Worksheet*, IKHyperlinks*, RecordInfo, int, UINT&);
	bool isExceedMaxColumns(const IDBIds* records, const IDBIds* fields, IDBSheetOp* sheetOp, std::map <EtDbId, UINT>&);
	void getExportAttachmentCnt(const_token_ptr pToken, UINT&);
	

	bool m_copyContent = true;
	bool m_isForAppendTemplate = false;
	bool m_exportAttachment = false;
	bool m_exceedMaxColumns = false;
    // 附件导出时，名称可能需要改变，例如导出失败时会加前缀，重名时添加后缀
    DbSheetsAttachmentInfoMap* m_pAttachmentData{nullptr};
    bool m_exportCompressedBundle{false};
	bool m_needCopyDataFormat{true};
};

class DbViewExporter : public Db2XlsxExporter
{
	UINT m_dbSheetID;
	EtDbId m_viewID;
	ks_stdptr<IDBSheetOp> m_spDbSheetOp;
	ks_stdptr<IDBSheetView> m_spView;

	int getRowHeight() const override;
	int getColWidth(EtDbId, IDBSheetOp*) const override;
public:
	DbViewExporter(wo::KEtWorkbook*, _Workbook*, UINT, EtDbId);
	DbViewExporter(_Workbook*, _Workbook*, UINT, EtDbId);
	HRESULT Init() override;
	HRESULT Exec() override;
public:
	HRESULT ExecToEtSheet(IDX destEtSheetIdx = 0);
	UINT GetDbSheetStId() {	return m_dbSheetID; }
};

class DbData2XlsxExporter : public Db2XlsxExporter
{
public:
	DbData2XlsxExporter(wo::KEtWorkbook*, _Workbook*, KEtRevisionContext* pCtx);
	HRESULT Exec() override;
};

class DbSheetViewDataToEtSheetExporter : public DbViewExporter
{
public:
	DbSheetViewDataToEtSheetExporter(wo::KEtWorkbook*, UINT dbSheetStId, EtDbId viewID);
	DbSheetViewDataToEtSheetExporter(_Workbook*, UINT dbSheetStId, EtDbId viewID);
	HRESULT Exec() override;

	UINT GetResultSheetStId() { return m_resultSheetStId; }
private:
	HRESULT CopySheetName(_Worksheet*, PCWSTR) override;
	bool _CheckDbSheetIsValid();

	UINT m_resultSheetStId;
};

class KSheet2XlsxExporter : public Db2XlsxExporter
{
public:
	KSheet2XlsxExporter(_Workbook* pDbWorkBook, _Workbook* pEtWorkBook, KEtRevisionContext* pCtx);
	KSheet2XlsxExporter( wo::KEtWorkbook*, _Workbook*, KEtRevisionContext* pCtx);
	HRESULT Exec() override;
    using Db2XlsxExporter::dbViewExportData;
private:
	_Workbook* m_pSrcBook;
	_Workbook* m_pTarWorkBook;
};

} // namespace wo

#endif
