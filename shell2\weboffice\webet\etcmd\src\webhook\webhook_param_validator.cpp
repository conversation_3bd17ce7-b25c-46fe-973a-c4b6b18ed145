﻿#include "etstdafx.h"
#include "etcore/et_core_basic.h"
#include "framework/krt/krtstring.h"
#include "kfc/com/sptr.h"
#include "webhook_param_validator.h"
#include "ettools/ettools_encode_decoder.h"
#include "dbsheet/et_dbsheet_filter_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "webbase/binvariant/binwriter.h"
#include "webbase/binvariant/binvarobj.h"
#include <vector>
#include "et_revision_context_impl.h"
#include <public_header/opl/mvc/et_shape_tree.h>
#include <public_header/opl/mvc/chart/et_chart_layer.h>
#include "webhook/webhook_basic_itf.h"
#include "webhook/webhook_common.h"
#include "webhook/webhook_enum.h"

void ParamsValidator::WriteHookInValidResult(PCWSTR hookId, PCWSTR message, binary_wo::BinWriter &res)
{
    res.beginStruct();
    res.addStringField(hookId, "hook_id");
    res.addInt16Field(Webhook_Invalid_Argument, "code");
    res.addStringField(message, "msg");
    res.endStruct();
    WOLOG_ERROR << "[ParamsValidator]" << message;
}

void ParamsValidator::WriteHookInValidSheetIds(PCWSTR hookId, PCWSTR message, const std::set<UINT>& sheetIds, binary_wo::BinWriter &res)
{
    res.beginStruct();
    res.addStringField(hookId, "hook_id");
    res.addInt16Field(Webhook_Invalid_Argument, "code");
    res.addStringField(message, "msg");
    res.beginArray("sheet_ids");
    for (UINT sheetId : sheetIds)
        res.addInt32(sheetId);
    res.endArray();
    res.endStruct();
    WOLOG_ERROR << "[ParamsValidator]" << message;
}

void ParamsValidator::WriteHookInValidResult(PCWSTR hookId, PCWSTR uuid, int code, PCWSTR message, binary_wo::BinWriter &res)
{
    res.beginStruct();
    res.addStringField(hookId, "hook_id");
    res.addStringField(uuid, "uuid");
    res.addInt16Field(code, "code");
    res.addStringField(message, "msg");
    res.endStruct();
    WOLOG_ERROR << "[ParamsValidator]" << message;
}

bool ParamsValidator::validateParam(PCSTR paramName, PCWSTR hookId, binary_wo::VarObj &obj, binary_wo::BinWriter &res)
{
    if (!obj.has(paramName))
    {
        QString str = paramName + QString(" is nil");
        WriteHookInValidResult(hookId, krt::utf16(str), res);
        return false;
    }
    return true;
}

bool WebhookParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    bool b = validateParam("hook_id", __X(""), hook, res);
    if (!b)
        return b;

    PCWSTR hookId = hook.field_str("hook_id");
    b = validateParam("rule", hookId, hook, res);
    if (!b)
        return b;
    
    binary_wo::VarObj ruleObj = hook.get_s("rule");
    b = validateParam("type", hookId, ruleObj, res);
    if (!b)
        return b;

    return true;
}

bool UserIdValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    binary_wo::VarObj rule = hook.get_s("rule");

    if (rule.has("user_id"))
        pData->SetUserId(rule.field_str("user_id"));
    return true;
}

bool SheetIdValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = validateParam("sheetId", hookId, rule, res);
    if (!b)
        return b;

    UINT sheetId = 0;
    sheetId = rule.field_uint32("sheetId");
    IDX sheetIdx = INVALIDIDX;
    m_pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
    {
        WriteHookInValidResult(hookId, __X("sheetId is invalid"), res);
        return false;
    }
    ks_stdptr<ISheet> spSheet;
    m_pBook->GetSheet(sheetIdx, &spSheet);
    if (!spSheet->IsDbSheet())
    {
        WriteHookInValidResult(hookId,  __X("sheet is not dbSheet"), res);
        return false;
    }

    DbWebhookRange rg;
    pData->GetRange(&rg);
    rg.sheetId = spSheet->GetStId();
    pData->SetRange(rg);
    return true;
}

bool EtSheetIdValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = validateParam("sheetId", hookId, rule, res);
    if (!b)
        return b;

    UINT sheetId = 0;
    sheetId = rule.field_uint32("sheetId");
    IDX sheetIdx = INVALIDIDX;
    m_pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
    {
        WriteHookInValidResult(hookId, __X("sheetId is invalid"), res);
        return false;
    }
    ks_stdptr<ISheet> spSheet;
    m_pBook->GetSheet(sheetIdx, &spSheet);
    if (!spSheet->IsGridSheet())
    {
        WriteHookInValidResult(hookId,  __X("sheet is not et"), res);
        return false;
    }

    EtWebhookRange rg;
    pData->GetRange(&rg);
    rg.sheetId = spSheet->GetStId();
    pData->SetRange(rg);
    return true;
}

bool SheetIdsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = validateParam("allSheets", hookId, rule, res);
    if (!b)
        return b;

    bool bAllSheets = rule.field_bool("allSheets");
    pData->SetAllSheets(bAllSheets);
    if (!bAllSheets)
    {
        b = validateParam("sheetIds", hookId, rule, res);
        if (!b)
            return b;

        std::set<UINT> sheetIds;
        std::set<UINT> invalidSheetIds;
        binary_wo::VarObj sheetIdsObj = rule.get_s("sheetIds");
        int32 sheetIdCnt = sheetIdsObj.arrayLength_s();
        if (sheetIdCnt > 1)
        {
            WriteHookInValidSheetIds(hookId, __X("sheetIds unsupport array"), invalidSheetIds, res);
            return false;
        }
        for (int i = 0; i < sheetIdsObj.arrayLength_s(); ++i)
        {
            binary_wo::VarObj sheetIdObj = sheetIdsObj.at_s(i);
            UINT sheetId = 0;
            sheetId = sheetIdObj.value_uint32();
            IDX sheetIdx = INVALIDIDX;
            m_pBook->STSheetToRTSheet(sheetId, &sheetIdx);
            if (sheetIdx == INVALIDIDX)
            {
                WOLOG_ERROR << "[SheetIdsValidator] sheetId invalid ";
                invalidSheetIds.insert(sheetId);
                continue;
            }
            ks_stdptr<ISheet> spSheet;
            m_pBook->GetSheet(sheetIdx, &spSheet);
            if (m_bCheckDbsheet && !spSheet->IsDbSheet())
            {
                WOLOG_ERROR << "[SheetIdsValidator] sheet is not dbsheet ";
                invalidSheetIds.insert(sheetId);
                continue;
            }
            if (!m_bCheckDbsheet && !spSheet->IsGridSheet())
            {
                WOLOG_ERROR << "[SheetIdsValidator] sheet is not gridsheet ";
                invalidSheetIds.insert(sheetId);
                continue;
            }
            sheetIds.insert(sheetId);
        }
        if (sheetIds.empty())
        {
            WriteHookInValidSheetIds(hookId, __X("sheetIds is invalid"), invalidSheetIds, res);
            return false;
        }
        for (auto id : sheetIds)
        {
            pData->InsertSheetId(id);
            DbWebhookRange rg;
            pData->GetRange(&rg);
            // 目前只支持一个sheet, 这样写也没问题
            // TODO: 如果改成多个sheet，这里也要同步改一下
            rg.sheetId = id;
            pData->SetRange(rg);
        }
            
    }

    return true;
}

bool ViewIdValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = validateParam("viewId", hookId, rule, res);
    if (!b)
        return b;

    EtDbId viewId = INV_EtDbId;
    if (rule.has("viewId"))
        _appcore_GainDbSheetContext()->DecodeEtDbId(rule.field_str("viewId"), &viewId);

    ks_stdptr<IDBSheetView> spDbSheetView;
    ks_stdptr<IDBSheetViews> spDbSheetViews;
    wo::DBSheetCommonHelper commonHelper(m_pBook);
    commonHelper.GetDBSheetViews(rule.field_uint32("sheetId"), &spDbSheetViews);
    if (spDbSheetViews)
        spDbSheetViews->GetItemById(viewId, &spDbSheetView);
    if (spDbSheetView == nullptr)
    {
        WriteHookInValidResult(hookId, __X("viewId is invalid"), res);
        return false;
    }

    DbWebhookRange rg;
    pData->GetRange(&rg);
    rg.viewId = viewId;
    pData->SetRange(rg);
    return true;
}

bool FieldIdValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = validateParam("fieldId", hookId, rule, res);
    if (!b)
        return b;

    EtDbId fieldId = INV_EtDbId;
    _appcore_GainDbSheetContext()->DecodeEtDbId(rule.field_str("fieldId"), &fieldId);
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    wo::DBSheetCommonHelper commonHelper(m_pBook);
    commonHelper.GetDBSheetOp(rule.field_uint32("sheetId"), &spDbSheetOp);
    EtDbIdx col = spDbSheetOp->GetAllFields()->Id2Idx(fieldId);
    if (col == INV_EtDbIdx)
    {
        WriteHookInValidResult(hookId, __X("fieldId is invalid"), res);
        return false;
    }
    DbWebhookRange rg;
    pData->GetRange(&rg);
    rg.fieldIds.insert(fieldId);
    pData->SetRange(rg);
    return true;
}

bool FieldIdsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = validateParam("fieldIds", hookId, rule, res);
    if (!b)
        return b || m_canEmpty;

    ks_stdptr<IDBSheetOp> spDbSheetOp;
    std::set<EtDbId> fieldIds;
    wo::DBSheetCommonHelper commonHelper(m_pBook);
    commonHelper.GetDBSheetOp(rule.field_uint32("sheetId"), &spDbSheetOp);
    binary_wo::VarObj fieldsObj = rule.get_s("fieldIds");
    for (int i = 0; i < fieldsObj.arrayLength_s(); ++i)
    {
        binary_wo::VarObj field = fieldsObj.at_s(i);
        EtDbId id = INV_EtDbId;
        _appcore_GainDbSheetContext()->DecodeEtDbId(field.value_str(), &id);
        if (id == INV_EtDbId)
        {
            WOLOG_ERROR << "[FieldIdsValidator] fieldId invalid ";
            continue;
        }
        
        EtDbIdx col = spDbSheetOp->GetAllFields()->Id2Idx(id);
        if (col == INV_EtDbIdx)
        {
            WOLOG_ERROR << "[FieldIdsValidator] fieldId invalid ";
            continue;
        }
        fieldIds.insert(id);
    }
    if (fieldIds.empty() && m_canEmpty == false)
    {
        WriteHookInValidResult(hookId, __X("fieldIds is invalid"), res);
        return false;
    }
    DbWebhookRange rg;
    pData->GetRange(&rg);
    rg.fieldIds.insert(fieldIds.begin(), fieldIds.end());
    pData->SetRange(rg);
    return true;
}

bool ActionFieldIdsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    if (!rule.has("queryRanges"))
        return false;
    bool b = validateParam("queryRanges", hookId, rule, res);
    if (!b)
        return b || m_canEmpty;
    binary_wo::VarObj queryRanges = rule.get("queryRanges");
    if (queryRanges.arrayLength_s() <= 0)
        return false;
    const auto rg = pData->GetQueryRange();
    IDBSheetCtx* pDBSheetCtx = _appcore_GainDbSheetContext();
    if (!pDBSheetCtx)
        return false;
    // 根据服务端的结构来写的，查询区域是个数组，但内核只支持一组数据
    for (int q = 0; q < queryRanges.arrayLength_s(); q++)
    {
        binary_wo::VarObj qr = queryRanges.at_s(q);
        if (!qr.has("sheetId"))
            continue;
        if (!qr.has("fieldIds"))
            continue;
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        wo::DBSheetCommonHelper commonHelper(m_pBook);
        UINT sheetId = qr.field_uint32("sheetId");
        HRESULT hr = commonHelper.GetDBSheetOp(sheetId, &spDbSheetOp);
        if (FAILED(hr))
        {
            WOLOG_ERROR << "queryRange sheetId is invalid";
            continue;
        }
        binary_wo::VarObj fieldsObj = qr.get_s("fieldIds");
        for (int i = 0; i < fieldsObj.arrayLength_s(); ++i)
        {
            binary_wo::VarObj field = fieldsObj.at_s(i);
            EtDbId id = INV_EtDbId;
            pDBSheetCtx->DecodeEtDbId(field.value_str(), &id);
            if (id == INV_EtDbId)
            {
                WOLOG_ERROR << "[ActionFieldIdsValidator] fieldId invalid ";
                continue;
            }
            
            EtDbIdx col = spDbSheetOp->GetAllFields()->Id2Idx(id);
            if (col == INV_EtDbIdx)
            {
                WOLOG_ERROR << "[FieldIdsValidator] fieldId invalid ";
                continue;
            }
            (*rg)[sheetId].insert(id);
        }
    }
    return true;
}

bool RecordIdsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = validateParam("recordIds", hookId, rule, res);
    if (!b)
        return b;

    ks_stdptr<IDBSheetOp> spDbSheetOp;
    std::set<EtDbId> recordIds;
    wo::DBSheetCommonHelper commonHelper(m_pBook);
    commonHelper.GetDBSheetOp(rule.field_uint32("sheetId"), &spDbSheetOp);
    binary_wo::VarObj recordsObj = rule.get_s("recordIds");
    for (int i = 0; i < recordsObj.arrayLength_s(); ++i)
    {
        binary_wo::VarObj record = recordsObj.at_s(i);
        EtDbId id = INV_EtDbId;
        _appcore_GainDbSheetContext()->DecodeEtDbId(record.value_str(), &id);
        if (id == INV_EtDbId)
        {
            WOLOG_ERROR << "[RecordIdsValidator] record invalid ";
            continue;
        }
        
        EtDbIdx row = spDbSheetOp->GetAllRecords()->Id2Idx(id);
        if (row == INV_EtDbIdx)
        {
            WOLOG_ERROR << "[RecordIdsValidator] record invalid ";
            continue;
        }
        
        recordIds.insert(id);
    }
    if (recordIds.empty() && !m_canEmpty)
    {
        WriteHookInValidResult(hookId, __X("recordIds is invalid"), res);
        return false;
    }
    DbWebhookRange rg;
    pData->GetRange(&rg);
    rg.recordIds.insert(recordIds.begin(), recordIds.end());
    pData->SetRange(rg);

    return true;
}

bool FilterValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = rule.has("filter");
    bool bExtra = rule.has("filterResult");
    if (!b && !bExtra)
    {
        if (m_canEmpty == false)
        {
            WriteHookInValidResult(hookId, __X("both filter and filterResult are nil"), res);
            return false;
        }
        else
            return true;
    }

    HRESULT hr = S_OK;
    wo::DBSheetCommonHelper commonHelper(m_pBook);
    ks_stdptr<IDbExtraFilter> spExtraFilter;
    ks_stdptr<IDbExtraFilter> spExtraFilterResult;
    commonHelper.GetDBSheetOp(rule.field_uint32("sheetId"), &m_spDbSheetOp);
    if (b)
    {
        binary_wo::VarObj filter = rule.get_s("filter");
        hr = getFilter(hookId, filter, commonHelper, res, &spExtraFilter);
        if (FAILED(hr))
            return false;
        pData->SetFilter(spExtraFilter);
    }

    if (bExtra)
    {
        binary_wo::VarObj filter = rule.get_s("filterResult");
        hr = getFilter(hookId, filter, commonHelper, res, &spExtraFilterResult);
        if (FAILED(hr))
            return false;
        pData->SetFilterResult(spExtraFilterResult);
    }
    return true;
}

HRESULT FilterValidator::getFilter(PCWSTR hookId, binary_wo::VarObj& filter, wo::DBSheetCommonHelper& commonHelper, binary_wo::BinWriter &res, IDbExtraFilter** ppFilter)
{
    if (binary_wo::typeStruct != filter.type())
    {
        WriteHookInValidResult(hookId, __X("filter type is invalid"), res);
        return E_DBSHEET_FILTER_INVALID;
    }

    ks_stdptr<IDbExtraFilter> spExtraFilter;
    VS(_appcore_CreateObject(CLSID_KDbExtraFilter, IID_IDbExtraFilter, (void**)& spExtraFilter));
    spExtraFilter->Init(m_spDbSheetOp);
    const IDbExtraFilter* pFailedFilter = nullptr;
    HRESULT hr = wo::DbSheet::filtrateByExtraFilter(spExtraFilter, filter, commonHelper, m_spDbSheetOp, true, &pFailedFilter);
    if (FAILED(hr))
    {
        ASSERT(pFailedFilter);
        UINT depth = pFailedFilter->GetDepth();
        if (0 == depth)
        {
            WriteHookInValidResult(hookId, __X("invalid filter detected at root filter"), res);
        }
        else
        {
            std::vector<UINT> indexes;
            indexes.resize(depth);
            pFailedFilter->GetCurFilterIndexesInfo(indexes.data(), depth);
            ks_wstring errMsg;
            errMsg.append(__X("invalid filter detected at indexes: "));
            for (int i = 0; i < indexes.size(); ++i)
            {
                errMsg.append(krt::utf16(QString::number(indexes[i])));
                errMsg.push_back(__Xc(','));
            }
            errMsg.pop_back();
            WriteHookInValidResult(hookId,  errMsg.c_str(), res);
        }
        return hr;
    }
    *ppFilter = spExtraFilter.detach();

    return S_OK;
}

bool FormulaChangeValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    binary_wo::VarObj rule = hook.get("rule");
    bool includeFormulaResultChange = false;
    if (rule.has("includeFormulaResultChange"))
        includeFormulaResultChange = rule.field_bool("includeFormulaResultChange");
    pData->SetCollectFormulaResChange(includeFormulaResultChange);
    return true;
}

bool EtRangeValidator::ValidateRange(binary_wo::VarObj &range, PCWSTR hookId, binary_wo::BinWriter &res, IWebhookData* pData)
{
    bool b = validateParam("rowFrom", hookId, range, res);
    if (!b)
        return b;
    b = validateParam("rowTo", hookId, range, res);
    if (!b)
        return b;
    b = validateParam("colFrom", hookId, range, res);
    if (!b)
        return b;
    b = validateParam("colTo", hookId, range, res);
    if (!b)
        return b;
    
    EtWebhookRange rg;
    pData->GetRange(&rg);
    EtWebhookRange::Range rowColRange;
    rowColRange.rowFrom = range.field_uint32("rowFrom");
    rowColRange.rowTo = range.field_uint32("rowTo");
    rowColRange.colFrom = range.field_uint32("colFrom");
    rowColRange.colTo = range.field_uint32("colTo");
    rg.ranges.push_back(rowColRange);
    pData->SetRange(rg);
    return true;
}

bool EtRangeValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get("rule");
    if(!rule.has("ranges"))
    {
        bool b = validateParam("range", hookId, rule, res);
        if (!b)
            return b;

        binary_wo::VarObj range = rule.get("range");
        b = ValidateRange(range, hookId, res, pData);
        if (!b)
            return b;
    }
    else
    {
        bool b = validateParam("ranges", hookId, rule, res);
        if (!b)
            return b;
        
        binary_wo::VarObj ranges = rule.get_s("ranges");
        for(int i = 0; i < ranges.arrayLength_s(); i++)
        {
            binary_wo::VarObj range = ranges.at_s(i);
            bool b = ValidateRange(range, hookId, res, pData);
            if (!b)
                return b;
        }
    }
    return true;
}

bool EtRowColIndexArrayValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
    bool b = validateParam(m_pArrayName, hookId, rule, res);
    if (!b)
        return b;

    std::vector<IDX> rowColIdxVector;
    binary_wo::VarObj rowColIdxArray = rule.get_s(m_pArrayName);
    for(int i = 0; i < rowColIdxArray.arrayLength_s(); i++)
    {
        rowColIdxVector.push_back(rowColIdxArray.at_s(i).value_int32());
    }
    if (rowColIdxVector.empty())
    {
        WriteHookInValidResult(hookId, __X("returnExtraCellValueCols is invalid"), res);
        return false;
    }
    pData->SetExtraReturnRowCols(rowColIdxVector.data(), rowColIdxVector.size());
    return true;
}

bool SkipAfterMatchCreateAndFillValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get("rule");
    bool skipAfterMatchCreateAndFill = false;
    if (rule.has("skipAfterMatchCreateAndFill"))
        skipAfterMatchCreateAndFill = rule.field_bool("skipAfterMatchCreateAndFill");
    pData->SetSkipAfterMatchCreateAndFill(skipAfterMatchCreateAndFill);
    return true;
}

// =======================================================================================

bool UpdateSheetDescParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateSheetIconParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool RenameSheetParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool RemoveSheetParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool CreateViewParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool RenameViewParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    ViewIdValidator viewIdValidator(m_pBook);
    b = viewIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool RemoveViewParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    ViewIdValidator viewIdValidator(m_pBook);
    b = viewIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool CreateFieldParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateFieldParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FieldIdValidator fieldIdValidator(m_pBook);
    b = fieldIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdatePrimaryFieldParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool RemoveFieldParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FieldIdValidator fieldIdValidator(m_pBook);
    b = fieldIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool CreateRecordParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool RemoveRecordParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    RecordIdsValidator recordIdsValidator(m_pBook, true);
    b = recordIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateRecordsParentParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    RecordIdsValidator recordIdsValidator(m_pBook, true);
    b = recordIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateSheetParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FormulaChangeValidator formulaChangeValidator(m_pBook);
    b = formulaChangeValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    SkipAfterMatchCreateAndFillValidator skipAfterMatchCreateAndFillValidator(m_pBook);
    b = skipAfterMatchCreateAndFillValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateCellsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    RecordIdsValidator recordIdsValidator(m_pBook);
    b = recordIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FieldIdsValidator fieldIdsValidator(m_pBook, false);
    b = fieldIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    ActionFieldIdsValidator actionFieldIdsValidator(m_pBook, true);
    actionFieldIdsValidator.ValidateParams(hook, res, pData);

    FormulaChangeValidator formulaChangeValidator(m_pBook);
    b = formulaChangeValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    SkipAfterMatchCreateAndFillValidator skipAfterMatchCreateAndFillValidator(m_pBook);
    b = skipAfterMatchCreateAndFillValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateFieldCellsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FieldIdsValidator fieldIdsValidator(m_pBook, false);
    b = fieldIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    ActionFieldIdsValidator actionFieldIdsValidator(m_pBook, true);
    actionFieldIdsValidator.ValidateParams(hook, res, pData);

    FormulaChangeValidator formulaChangeValidator(m_pBook);
    b = formulaChangeValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    SkipAfterMatchCreateAndFillValidator skipAfterMatchCreateAndFillValidator(m_pBook);
    b = skipAfterMatchCreateAndFillValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateRecordCellsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    RecordIdsValidator recordIdsValidator(m_pBook);
    b = recordIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FormulaChangeValidator formulaChangeValidator(m_pBook);
    b = formulaChangeValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    SkipAfterMatchCreateAndFillValidator skipAfterMatchCreateAndFillValidator(m_pBook);
    b = skipAfterMatchCreateAndFillValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateRecordsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FieldIdsValidator fieldIdsValidator(m_pBook, true);
    b = fieldIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    ActionFieldIdsValidator actionFieldIdsValidator(m_pBook, true);
    actionFieldIdsValidator.ValidateParams(hook, res, pData);
    
    FilterValidator filterValidator(m_pBook, true);
    b = filterValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FormulaChangeValidator formulaChangeValidator(m_pBook);
    b = formulaChangeValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    SkipAfterMatchCreateAndFillValidator skipAfterMatchCreateAndFillValidator(m_pBook);
    b = skipAfterMatchCreateAndFillValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool UpdateSheetsAllChangeParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdsValidator sheetIdsValidator(m_pBook);
    bool b = sheetIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    FormulaChangeValidator formulaChangeValidator(m_pBook);
    b = formulaChangeValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    UserIdValidator userIdValidator(m_pBook);
    b = userIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool CreateAndFillInRecordParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    binary_wo::VarObj rule = hook.get("rule");
    if(!rule.has("fieldIds"))
        return true;

    FieldIdsValidator fieldIdsValidator(m_pBook, true);
    b = fieldIdsValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    ActionFieldIdsValidator actionFieldIdsValidator(m_pBook, true);
    actionFieldIdsValidator.ValidateParams(hook, res, pData);
    
    return true;
}

bool EtUpdateSheetParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    EtSheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool EtUpdateRangesParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");

    bool b = validateParam("rgs", hookId, rule, res);
    if (!b) {
        WOLOG_ERROR << "[EtUpdateRangesParamsValidator] rgs INVALID";
        return b;
    }

    binary_wo::VarObj rgs = rule.get_s("rgs");
    int32 l = rgs.arrayLength_s();
    EtWebhookRanges ranges;
    WOLOG_INFO << "[EtUpdateRangesParamsValidator] rg count " << l;
    for (int i = 0; i < l; i++) {
        binary_wo::VarObj rg = rgs.at(i);
        EtWebhookRangeW rangeW;
        rangeW.uuid = krt::fromUtf16(rg.field_str("uuid"));

        RANGE range(m_pBook->GetBMP());
        int32 stId = rg.field_int32("sheetStId");
        IDX sheetIdx = INVALIDIDX;
        m_pBook->STSheetToRTSheet(stId, &sheetIdx);
        if (sheetIdx == INVALIDIDX) {
            WOLOG_ERROR << "[EtUpdateRangesParamsValidator] sheetIdx INVALIDIDX " << rangeW.uuid;
            WriteHookInValidResult(hookId, krt::utf16(rangeW.uuid), Webhook_Invalid_Argument, __X("INVALIDIDX"), res);
            continue;
        }

        range.SetSheetFromTo(stId);//后续查找匹配的时候用stid
        range.SetRowFromTo(
		    rg.field_int32("rowFrom"), rg.field_int32("rowTo"));
	    range.SetColFromTo(
		    rg.field_int32("colFrom"), rg.field_int32("colTo"));

        rangeW.range = range;
        ranges.push_back(rangeW);
        WOLOG_INFO << "[EtUpdateRangesParamsValidator] reg sucess: " << rangeW.uuid;
    }
    if (!ranges.empty()) {
        pData->SetRanges(ranges);
    }
    return !ranges.empty();
}

bool EtUpdateChartsParamsValidator::isChartExist(ISheet* isheet, const WCHAR* id)
{
    ks_stdptr<IUnknown> ptrUnk;
    ks_stdptr<IKDrawingCanvas> ptrDrawingCanvas;
    isheet->GetExtDataItem(edSheetDrawingCanvas, &ptrUnk);
    if (ptrUnk == NULL) 
    {
        return false;
    }
    
    ptrDrawingCanvas = ptrUnk;
    
    ks_castptr<drawing::ShapeTree> cpTree = ptrDrawingCanvas;
    if (nullptr == cpTree)
    {
       return false;
    }

    QString uuid = krt::fromUtf16(id);
    const drawing::AbstractShape* shape = cpTree->getAbsShape(uuid);
    if (nullptr == shape)
    {
        return false;
    }
    return true;
}

bool EtUpdateChartsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{   
    PCWSTR hookId = hook.field_str("hook_id");
    binary_wo::VarObj rule = hook.get_s("rule");
        
    bool b = validateParam("chartUUIDs", hookId, rule, res);
    if (!b) {
        WOLOG_ERROR << "[EtUpdateChartsParamsValidator] chartUUIDs invalid";
        return b;
    }
    
    binary_wo::VarObj uuids = rule.get_s("chartUUIDs");
    int32 l = uuids.arrayLength_s();
    EtWebhookCharts charts;
    WOLOG_INFO << "[EtUpdateChartsParamsValidator] chart count " << l;
    for (int i = 0; i < l; i++) {
        binary_wo::VarObj item = uuids.at(i);

        bool b = validateParam("uuid", hookId, item, res);
        if (!b) {
            WOLOG_ERROR << "[EtUpdateChartsParamsValidator] uuid INVALID";
            return b;
        }

        b = validateParam("sheetStId", hookId, item, res);
        if (!b) {
            WOLOG_ERROR << "[EtUpdateChartsParamsValidator] sheetStId INVALIDIDX";
            return b;
        }

        UINT sheetId = item.field_uint32("sheetStId");
        IDX sheetIdx = INVALIDIDX;
        m_pBook->STSheetToRTSheet(sheetId, &sheetIdx);

        if (sheetIdx == INVALIDIDX) {
            WOLOG_ERROR << "[EtUpdateChartsParamsValidator] sheetIdx INVALIDIDX";
            WriteHookInValidResult(hookId, item.field_str("uuid"), Webhook_Invalid_Argument, __X("INVALIDIDX"), res);
            continue;
        }
        ks_stdptr<ISheet> isheet;
        m_pBook->GetSheet(sheetIdx, &isheet);
        b = isChartExist(isheet, item.field_str("uuid"));
        if (!b) {
            WOLOG_ERROR << "[EtUpdateChartsParamsValidator] chart uuid INVALIDIDX";
            WriteHookInValidResult(hookId, item.field_str("uuid"), Webhook_Invalid_Argument, __X("INVALIDIDX"), res);
            continue;
        }

        EtWebhookChart chart;
        chart.sheetId = sheetId;
        chart.uuid = krt::fromUtf16(item.field_str("uuid"));
        charts.push_back(chart);
        WOLOG_INFO << "[EtUpdateChartsParamsValidator] reg sucess: " << chart.uuid;
    }
    pData->SetCharts(charts);
    return !charts.empty();
}


bool EtRemoveSheetParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    EtSheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;
    return true;
}

bool EtInsertRowsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    EtSheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;
    return true;
}

bool EtRemoveRowsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    EtSheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;
    binary_wo::VarObj rule = hook.get("rule");
    if (rule.has("returnValueCols"))
    {
        EtRowColIndexArrayValidator rowColIndexArrayValidator(m_pBook, "returnValueCols");
        b = rowColIndexArrayValidator.ValidateParams(hook, res, pData);
        if (!b)
            return b;
    }
    return true;
}

bool EtInsertColsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    EtSheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    return true;
}

bool EtRemoveColsParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    EtSheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    binary_wo::VarObj rule = hook.get("rule");
    if (rule.has("returnValueCols"))
    {
        EtRowColIndexArrayValidator rowColIndexArrayValidator(m_pBook, "returnValueCols");
        b = rowColIndexArrayValidator.ValidateParams(hook, res, pData);
        if (!b)
            return b;
    }

    return true;
}

bool EtUpdateRangeParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    EtSheetIdValidator sheetIdValidator(m_pBook);
    bool b = sheetIdValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    EtRangeValidator rangeValidator(m_pBook);
    b = rangeValidator.ValidateParams(hook, res, pData);
    if (!b)
        return b;

    binary_wo::VarObj rule = hook.get("rule");
    if (rule.has("returnExtraCellValueCols"))
    {
        EtRowColIndexArrayValidator rowColIndexArrayValidator(m_pBook, "returnExtraCellValueCols");
        b = rowColIndexArrayValidator.ValidateParams(hook, res, pData);
        if (!b)
            return b;
    }
    return true;
}

bool EtUpdateSheetsAllChangeParamsValidator::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData)
{
    SheetIdsValidator sheetIdsValidator(m_pBook);
    sheetIdsValidator.SetCheckDbsheet(false);
    return sheetIdsValidator.ValidateParams(hook, res, pData);
}
