﻿#include "etstdafx.h"
#include "db_dashboard_http_task_class.h"
#include "helpers/varobject_helper.h"
#include "src/workbook.h"
#include "src/et_revision_context_impl.h"
#include "webbase/serialize_impl.h"
#include "webbase/wo_sa_helper.h"
#include "dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h"
#include "dbsheet/dashboard/db_chart_wrapper.h"
#include "appcore/et_appcore_enum.h"

namespace wo
{

// ================== DbDashboardHttpCreateFiltersTaskClass ==================
DbDashboardHttpCreateFiltersTaskClass::DbDashboardHttpCreateFiltersTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbDashboardHttpCreateFiltersTaskClass::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALID_REQUEST;

    hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
    if (FAILED(hr))
        return hr;

    VAR_OBJ_EXPECT_ARRAY(param, "filters")
    binary_wo::VarObj filters = param.get_s("filters");
    int32 count = filters.arrayLength_s();
    if (count == 0)
        return E_INVALID_REQUEST;

    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    IDbDashboardFilterMgr* pFilterMgr = spDbDashboard->GetFilterMgr();
    UINT16 maxFilterCount = _kso_GetWoEtSettings()->GetDbDashboardMaxFilterCount();
    if (!pCtx->isExecDirect() && (pFilterMgr->GetCount() + count > maxFilterCount))
           return E_DBSHEET_DASHBOARD_FILTER_COUNT_LIMIT;

    std::vector<IDbDashboardFilter*> newFilters;
    for (int32 i = 0; i < count; ++i)
    {
        binary_wo::VarObj item = filters.at(i);
        VAR_OBJ_EXPECT_STRING(item, "name");
        VAR_OBJ_EXPECT_BOOL(item, "multiSheetFilter");
        VAR_OBJ_EXPECT_ARRAY(item, "filterFields");
        PCWSTR name = item.field_str("name");
        if (!pCtx->isExecDirect() && !KDbDashboardModuleMgrWrapper::IsValidFilterName(name))
            return E_DBSHEET_DASHBOARD_INVALID_FILTER_NAME;

        ks_stdptr<IDbDashboardFilter> spFilter;
        hr = pFilterMgr->CreateFilter(name, &spFilter);
        if (FAILED(hr))
            return hr;

        bool multiSheetFilter = item.field_bool("multiSheetFilter");
        hr = spFilter->SetMultiSheetFilter(multiSheetFilter);
        if (FAILED(hr))
            return hr;

        binary_wo::VarObj filterFields = item.get_s("filterFields");
        hr = spFilter->SetFilterFields(filterFields);
        if (FAILED(hr))
            return hr;

        newFilters.push_back(spFilter.get());
    }
    binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, pCtx);
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);;
    sa::Leave filtersLeave(sa::enterArray(&acpt, "filters"));
    for (auto* pFilter : newFilters)
    {
        sa::Leave leave(sa::enterStruct(&acpt, nullptr));
        dashboardModuleMgrWrapper.SerializeFilter(pFilter, &acpt);
    }
    return S_OK;
}

HRESULT DbDashboardHttpCreateFiltersTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR DbDashboardHttpCreateFiltersTaskClass::GetTag()
{
    return __X("http.db.dashboard.createFilters");
}

// ================== DbDashboardHttpUpdateFiltersTaskClass ==================
DbDashboardHttpUpdateFiltersTaskClass::DbDashboardHttpUpdateFiltersTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbDashboardHttpUpdateFiltersTaskClass::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALID_REQUEST;

    hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
    if (FAILED(hr))
        return hr;

    VAR_OBJ_EXPECT_ARRAY(param, "filters")
    binary_wo::VarObj filters = param.get_s("filters");
    int32 count = filters.arrayLength_s();
    if (count ==0)
        return E_INVALID_REQUEST;

    std::vector<IDbDashboardFilter*> updatedFilters;
    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    IDbDashboardFilterMgr* pFilterMgr = spDbDashboard->GetFilterMgr();
    for (int32 i = 0; i < count; ++i)
    {
        binary_wo::VarObj item = filters.at(i);
        VAR_OBJ_EXPECT_STRING(item, "id");
        EtDbId filterId = GetEtDbId(item, "id");
        ks_stdptr<IDbDashboardFilter> spFilter;
        hr = pFilterMgr->GetFilter(filterId, &spFilter);
        if (FAILED(hr))
            return hr;

        if (item.has("name"))
        {
            VAR_OBJ_EXPECT_STRING(item, "name");
            PCWSTR name = item.field_str("name");
            if (!pCtx->isExecDirect() && !KDbDashboardModuleMgrWrapper::IsValidFilterName(name))
                return E_DBSHEET_DASHBOARD_INVALID_FILTER_NAME;

            hr = spFilter->SetName(name);
            if (FAILED(hr))
                return hr;
        }

        if (param.has("multiSheetFilter"))
        {
            VAR_OBJ_EXPECT_BOOL(item, "multiSheetFilter");
            bool multiSheetFilter = param.field_bool("multiSheetFilter");
            hr = spFilter->SetMultiSheetFilter(multiSheetFilter);
            if (FAILED(hr))
                return hr;
        }

        if (item.has("filterFields"))
        {
            VAR_OBJ_EXPECT_ARRAY(item, "filterFields");
            binary_wo::VarObj filterFields = item.get_s("filterFields");
            hr = spFilter->SetFilterFields(filterFields);
            if (FAILED(hr))
                return hr;
        }
        updatedFilters.push_back(spFilter.get());
    }
    binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, pCtx);
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    sa::Leave filtersLeave(sa::enterArray(&acpt, "filters"));
    for (auto* pFilter : updatedFilters)
    {
        sa::Leave leave(sa::enterStruct(&acpt, nullptr));
        dashboardModuleMgrWrapper.SerializeFilter(pFilter, &acpt);
    }
    return S_OK;
}

HRESULT DbDashboardHttpUpdateFiltersTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR DbDashboardHttpUpdateFiltersTaskClass::GetTag()
{
    return __X("http.db.dashboard.updateFilters");
}

// ================== DbDashboardHttpDeleteFiltersTaskClass ==================
DbDashboardHttpDeleteFiltersTaskClass::DbDashboardHttpDeleteFiltersTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbDashboardHttpDeleteFiltersTaskClass::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALID_REQUEST;

    hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
    if (FAILED(hr))
        return hr;

    VAR_OBJ_EXPECT_ARRAY(param, "filters")
    std::vector<EtDbId> filterIds;
    hr = m_commonHelper.GetEtDbIds(param, "filters", filterIds);
    if (FAILED(hr))
        return hr;

    if (filterIds.empty())
        return E_INVALID_REQUEST;

    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    IDbDashboardFilterMgr* pFilterMgr = spDbDashboard->GetFilterMgr();
    for (EtDbId filterId : filterIds)
    {
        hr = pFilterMgr->RemoveFilter(filterId);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT DbDashboardHttpDeleteFiltersTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR DbDashboardHttpDeleteFiltersTaskClass::GetTag()
{
    return __X("http.db.dashboard.deleteFilters");
}


}