#ifndef __WEBOFFICE_WEBET_MERGEFILE_UTIL_H__
#define __WEBOFFICE_WEBET_MERGEFILE_UTIL_H__

namespace wo
{
using binary_wo::BinWriter;
class KEtWorkbook;
namespace autoupdate_util
{

struct NetFileInfo
{
    ks_wstring path;
    ks_wstring version;
    ks_wstring filename;
	ks_wstring fileId;
    SUPBOOK_STAT stat;
};

struct DisableTransHlp
{
	DisableTransHlp(IWorkspace *pWS)
		: m_pWS(pWS)
	{
		if (m_pWS)
			m_pWS->EnableUndo(nullptr, FALSE);
	}
	~DisableTransHlp()
	{
		if (m_pWS)
			m_pWS->EnableUndo(nullptr, TRUE);
	}

	IWorkspace *m_pWS;
};

struct EtRevisionCtxResetScope
{
	EtRevisionCtxResetScope()
		: m_oldCtx(NULL)
	{
		wo::IEtRevisionContext* ctx = _etcore_GetEtRevisionContext();
		if(ctx)
		{
			m_oldCtx = ctx;
			_kso_SetRevisionContext(NULL);
		}
	}

	~EtRevisionCtxResetScope()
	{
		_kso_SetRevisionContext(m_oldCtx);
		m_oldCtx = NULL;
	}

	wo::IEtRevisionContext* m_oldCtx;
};

// typedef struct MergeFileInfo
// {
// 	ks_wstring strId;
// 	ks_wstring strDocName;
// 	ks_wstring strAuthor;
// 	ks_wstring strTime;
// 	UINT32 fileVersion;
// 	std::vector<SMergeSrcInfo> vecSrcInfo;
// } SMergeFileInfo;
class MergeFileUtil
{
public:
	struct sourceDataInfo
	{
		ROW start;
		int cnt;
		ks_wstring srcSheetName;
		ks_wstring srcBookName;
 	};

	struct STR_HASH
	{
		size_t operator()(const ks_wstring& str) const
		{
			return alg::HashWString(str.c_str());
		}
	};
	struct STR_EQUAL
	{
		bool operator()(const ks_wstring& lhs, const ks_wstring& rhs) const
		{
			return xstricmp(lhs.c_str(), rhs.c_str()) == 0;
		}
	};

	typedef std::unordered_map<PCWSTR, int, STR_HASH, STR_EQUAL> FileInfos;

	class HasCellValueAcpt : public ICellValueAcpt
	{
	public:
		HasCellValueAcpt()
			: m_row(-1)
			, m_col(-1)
		{
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if (pToken)
			{
				DWORD typeToken = alg::GetExecTokenMajorType(pToken);
				if(typeToken == alg::ETP_VSTR)
				{
					alg::const_vstr_token_assist strToken(pToken);
					if(strToken.get_length() <= 0)
						return 0;
				}
				m_row = row;
				m_col = col;
				return 1;//停止枚举
			}
			return 0;//继续枚举
		};

		ROW m_row;
		COL m_col;
	};

	using wstrMap = std::unordered_map<ks_wstring, ks_wstring, STR_HASH>;
	using wstrSet = std::unordered_set<ks_wstring, STR_HASH>;
	using SubscriberRanges = std::unordered_map<ks_wstring, wstrSet, STR_HASH, STR_EQUAL>;

	static void BroadcastNow(wo::MsgType name, BinWriter& ww);
	static void GetMergeFileError(_Worksheet*, ks_wstring&, ks_wstring&, ks_wstring&, NetFileRes&);

public:
	MergeFileUtil(KEtWorkbook* wb, const char* userID = nullptr);

	bool GetMergeFileInfo(_Worksheet*, std::vector<SMergeFileInfo>&);
	bool GetIsAutoRefresh(_Worksheet*);
	void MergeFileSubscribe();

public:
    void PrepareForMergeTask();
    bool GetSrcFileNetInfo(_Worksheet*, std::vector<NetFileInfo>&);
    bool IsReadyToMerge(_Worksheet* pSheet);
	WebInt DoMergeAllMergeTask(bool isCaculating = false);
    WebInt DoMerge(_Worksheet* pSheet);
	WebInt DoMergeWithoutUser(_Worksheet* pSheet);
	WebInt DoMergeByIncreament(_Worksheet* pMergeSheet, const FileInfos& fileInfos);
    _Workbook* OpenFile(Workbooks* ptrWorkbooks, ks_wstring fileName);

	void GetWaitingFiles(std::vector<ks_wstring>& waitingFiles);
	void GetMergeFilesAndUnready(std::vector<ks_wstring>& , std::vector<ks_wstring>&);

	bool GetSingleSrcInfoByfileId(const ks_wstring&, const std::vector<SMergeFileInfo>& ,SMergeFileInfo&);

private:
    WebInt ClearDstSheet(_Worksheet*);
    HRESULT CheckSecurityDoc(_Workbook*);
    HRESULT CopyBookData(_Workbook*, _Worksheet*, _Workbook*, bool& , bool isRemoveDuplicates, SMergeFileInfo&);
	HRESULT CopyBookDataIncr(_Workbook*, _Worksheet*, _Workbook*, SMergeFileInfo&);
	HRESULT RemoveDuplicates(_Worksheet*);
	HRESULT TrasformSafeArray2Vector(VARIANT arr, std::vector<long>& parray);
	HRESULT AddDataSrcFields(_Worksheet*);
	HRESULT SetDataSrcColTitle(_Worksheet*, RANGE);
	HRESULT CreateDstSheet(Worksheets*, _Worksheet*, ks_stdptr<etoldapi::_Worksheet> &);
	HRESULT CopyAssitSheetData(_Workbook*, _Worksheet*, _Worksheet*);
	INT32 getCopyRange(_Worksheet*, bool, etoldapi::Range**);
    void CopyColWidth(ISheet*, ISheet*);
	void DelUseLessSheet(_Workbook* ptrWorkbookDst);
	void InitVariabelState();
	void InitVariabelState(_Worksheet*);

	INT32 GetNextRegionPos(IMergeFile*, const ks_wstring& fileId);


	std::unique_ptr<KEtRevisionContext> CreateContext()
	{
		KEtWorkbook* wb = m_wwb;
		return std::unique_ptr<KEtRevisionContext>(new KEtRevisionContext(wb));
	}

private:
    KEtWorkbook* 	m_wwb;
	const char* 	m_userID;
	wstrSet 		m_waitingFiles;

    int m_nCurRow;
	bool m_isMarkSrcData;
	bool m_autoRefresh;
	bool m_isRemoveDuplicates;
	bool m_hasCopyTitle;
	bool m_updateByIncreament;
	UINT32 m_titleRowCnt;
	std::vector<sourceDataInfo> m_srcDataInfo;
	int m_newSheetIdx;
};




} // namespace autoupdate_util
} // namespace wo

#endif //__WEBOFFICE_WEBET_MERGEFILE_UTIL_H__

