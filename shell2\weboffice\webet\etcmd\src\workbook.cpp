#include "etstdafx.h"
#include "appcore/et_appcore_basic_itf.h"
#include "workbook.h"
#include "kfc/tools/smart_wrap.h"
#include "kso/io/filterinfo_table.h"
#include "block_point.h"
#include "webetlink.h"
#include "wo/core_stake.h"
#include "wo/delay_obj_xfs.h"
#include <public_header/revision/src/kwcommand.h>
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include <public_header/etcore/mvc/et_workbook_layer.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include <public_header/opl/mvc/cellimage/et_cellimage_shape_tree.h>
#include <public_header/opl/mvc/cellimage/et_cellimage_shape.h>
#include "wo/et_revision_context.h"
#include "et_revision_context_impl.h"
#include "autofilteritemshelper.h"
#include "autofilterparamhelper.h"
#include "et_query_executor.h"
#include "drawing/utility/shapesave.h"
#include <public_header/drawing/media/mediamanage/mediamanage.h>
#include <public_header/drawing/io/import/simple_theme_parse.h>
#include "serialize_impl.h"
#include <public_header/drawing/wo/serialdrawing.h>
#include "wo/workbook_obj.h"
#include "webbase/context_init_helper.h"
#include "wo/wo_msgType_helper.h"
#include "webbase/logger.h" 
#include <public_header/webcommon/src/wodocmemostat.h>
#include "kwebslice.h"
#include "mvc/et_mvc.h"
#include <public_header/opl/mvc/chart/et_chart_layer.h>
#include "appcore/et_appcore_dbsheet.h"
#include "helpers/subscription_helper.h"
#include "supbooks_helper.h"
#include "util.h"
#include <public_header/webcommon/src/security_doc.h>
#include "censor/censordatahighlight.h"
#include "helpers/picture_upload_helper.h"
#include "helpers/range_operator_helper.h"
#include "utils/attachment_utils.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "dbsheet/db2et_exporter.h"
#include "dbsheet/db_export_helper.h"
#include "kso/api/smartparam.h"
#include "wo_medium.h"
#include "woetsetting.h"
#include "book_format_converter/db_sync_adapter.h"
#include "helpers/app/remove_app_related_sheet.h"
#include "helpers/app/update_app_version_helper.h"
#include <public_header/opl/mvc/et_shape_tree_visual.h>
#include "webhook/tracker_scope.h"
#include "applogic/et_applogic_helper.h"
#include "wobatchtrans.h"
#include "condition_format_helper.h"
#include "wo/serial_xf_helper.h"
#include "helpers/varobject_helper.h"
#include <kso/framework/docslim_helper.h>
#include "dbsheet/et_dbsheet_syncsheet_utils.h"
#include "collect.h"
#include "etcore/et_core_timestat.h"
#include "etcore/et_core_event_tracking.h"
#include "et_ai_watcher.h"
#include "helpers/merge_range_helper.h"
#include "wo/wo_coremetrics_i.h"

extern QString g_savePath;

extern Callback* gs_callback;
extern std::atomic<uint> gs_signalSize;
extern WebProcType gs_procType;

namespace wo
{

class ShapeDrawingContextScope
{
public:
	ShapeDrawingContextScope(_Application* pApp, const drawing::AbstractShape* shape, int dpi, double scale)
	{
		drawing::ShapeVisual* pShapeVisual = shape->getActiveShapeVisual();
		if(pShapeVisual == NULL)
			return;
		
		QRectF rcShape = pShapeVisual->boundingRectInLayer(pShapeVisual->getLayerControl(), TRUE); 
		bool bRootShapeTree = pShapeVisual->getLayerControl()->getLayer()->getLayerType() == ET_LAYER_SHAPETREE
			|| pShapeVisual->getLayerControl()->getLayer()->getLayerType() == ET_LAYER_COMMENTSHAPETREE;

		if (bRootShapeTree)
		{
			m_cpEtShapeTreeVisual = pShapeVisual->getLayerControl()->getVisual();
			m_drawCnt.bDrawSlantAsOleobject = FALSE;
			m_drawCnt.pUnknown = pApp->GetActiveView();

			m_txtData.fSLWidth = 1440.0 / dpi / scale;
			m_txtData.fSLHeight = 1440.0 / dpi / scale;
			m_txtData.ptA1OffsetTwip.rx() = -rcShape.left();
			m_txtData.ptA1OffsetTwip.ry() = -rcShape.top();
			m_txtData.rcViewPort = rcShape;
			m_textBoxDraw.m_pfnTextDrawer = SetDrawContext;
			m_textBoxDraw.m_pUserData = (void*)&m_txtData;
			m_drawCnt.pTxtDrawer = &m_textBoxDraw;
			m_cpEtShapeTreeVisual->setDrawingContext(m_drawCnt);
		}
	}

	~ShapeDrawingContextScope()
	{
		if (m_cpEtShapeTreeVisual)
			m_cpEtShapeTreeVisual->setDrawingContext(DrawingContext());
	}

private:
	ks_castptr<drawing::ShapeTreeVisual> m_cpEtShapeTreeVisual;
	DrawingContext m_drawCnt;
	DrawTxtData m_txtData;
	TxtDrawer m_textBoxDraw;
};

static bool hasVBAMacro(IBook *pBook)
{
    IKApplication *pApp = kxApp->coreApplication();
    if (!pApp)
        return false;

    ks_castptr<IKEtKde> cpKde = pApp->GetKde();
    if (!cpKde || !cpKde->QueryKdeValid())
        return false;

    ks_stdptr<IUnknown> spMarcos;
    pBook->GetExtDataItem(edBookVBACode, &spMarcos);
    return spMarcos != NULL;
};

static void MakeEtSaveArgument(ETSAVEARGUMENT &arg,
							FILTERMEDIUM *fm,
							BOOL bSilent,
							BOOL bSaveAs,
							BOOL bAddtoMru,
							BOOL bChangeModifyFlag,
							DWORD fileFormat,
							IDX iActiveSheet = INVALIDIDX,
							BOOL bCalculateComplete = TRUE,
							IOBreakType ioBreakType = IOESCBREAK,
							BOOL bRemember = TRUE,
							BOOL bSaveWithoutPermission = FALSE,
							BOOL bAutoSave = FALSE,
							BOOL bCrashSave = FALSE,
							ETSaveAsAccessMode accessMode = etNoChange,
							BOOL bExport = FALSE)
{
    arg.fm = fm;
    arg.bSilent = bSilent;
    arg.bSaveAs = bSaveAs;
    arg.bAddtoMru = bAddtoMru;
    arg.bChangeModifyFlag = bChangeModifyFlag;
    arg.nFileFormat = fileFormat;
    arg.iActiveSheet = iActiveSheet;
    arg.bCalculateComplete = bCalculateComplete;
    arg.ioBreakType = ioBreakType;
	arg.bRemember = bRemember;
	arg.bSaveWithoutPermission = bSaveWithoutPermission;
    arg.bAutoSave = bAutoSave;
    arg.bCrashSave = bCrashSave;
    arg.accessMode = accessMode;
	arg.bExport = bExport;
}

static void UpdateDbDashBoardModules(ISheet* pSheet)
{
	ASSERT(pSheet->IsDbDashBoardSheet());
	ks_stdptr<IDBChartStatisticMgr> spChartStatisticMgr;
	VS(DbSheet::GetDBChartStatisticMgr(pSheet, &spChartStatisticMgr));
	if (!spChartStatisticMgr)
		return;
	for (EtDbIdx i = 0, cnt = spChartStatisticMgr->GetSize(); i < cnt; ++i)
	{
		ks_stdptr<IDBChartStatisticModule> spModule;
		spChartStatisticMgr->GetItemAt(i, &spModule);
		if (spModule)
			VS(spModule->Update());
	}
}

static void UpdateDbDashBoardViews(IBook* pBook, ISheet* pDashboardSheet)
{
	if (!pDashboardSheet)
		return;

	UINT dashbordSheetId = pDashboardSheet->GetStId();
	ks_stdptr<IKWebExtensionMgr> spWebExtMgr;
	pBook->GetExtDataItem(edBookWebExtensionMgr, (IUnknown**)&spWebExtMgr);
    ASSERT(spWebExtMgr != nullptr);
	UINT webextensionCount = 0;
	spWebExtMgr->GetWebExtensionCount(pDashboardSheet, &webextensionCount);
	for (int i = 0; i < webextensionCount; ++i)
	{
		ks_stdptr<IKWebExtension> spWebExtension;
		HRESULT hr = spWebExtMgr->GetWebExtension(pDashboardSheet, i, &spWebExtension);
		if (FAILED(hr))
            continue;

		if (spWebExtension->GetWebShapeType() != WET_DbView)
			continue;

		PCWSTR webextensionKey = spWebExtension->GetWebExtensionKey();
		ks_stdptr<IEtWebExtension_View> spWebExtView = spWebExtension;
		IDX sheetIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(spWebExtView->GetSheetId(), &sheetIdx);
		if (sheetIdx == INVALIDIDX)
			continue;
		ks_stdptr<ISheet> pSheet;
    	pBook->GetSheet(sheetIdx, &pSheet);
		if (!pSheet)
			continue;
		ks_stdptr<IDBSheetViews> spDbSheetViews;
		pSheet->GetExtDataItem(edSheetDbViews, (IUnknown**)&spDbSheetViews);
		if (!spDbSheetViews)
			continue;
		ks_stdptr<IDBSheetView> spDBSheetView;
		spDbSheetViews->GetItemById(spWebExtView->GetViewId(), &spDBSheetView);
		if (!spDBSheetView)
			continue;

		WebExtInfo info;
		info.sheetStId = dashbordSheetId;
		info.webExtKey = webextensionKey;
		spDBSheetView->SetWebExtInfo(info);
	}
}

static void UnsyncSheetWhenTheBookSavedFromOther(_Workbook* pWorkbook)
{
	if (pWorkbook == nullptr)
		return;
	IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
	IBook* pBook = pWorkbook->GetBook();
	INT sheetCount {};
	pBook->GetSheetCount(&sheetCount);
	for (INT i = 0; i < sheetCount; ++i)
	{
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(i);
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		if (FAILED(DbSheet::GetDBSheetOp(pWorksheet->GetSheet(), &spDbSheetOp)))
			continue;
		ET_DbSheet_Sync_Type syncType = spDbSheetOp->GetSheetSyncType();
		if (syncType == DbSheet_St_DB || syncType == DbSheet_St_Merge_DB || syncType == DbSheet_St_GridSheet)
		{
			ks_stdptr<IUnknown> spUnknown;
			pBook->GetExtDataItem(edDBUserGroups, &spUnknown);
			ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
			ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
			if (spDBUserGroups)
				spDBUserGroups->GetJudgement(&spProtectionJudgement);
			DbSheet::DisableDbProtectScope disableDBProtectionHelper(spProtectionJudgement);
			std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher> attachmentIdMap;
			GlobalSharedString nullStr(static_cast<PCWSTR>(nullptr));
			attachmentIdMap[nullStr] = nullStr;
			UnsyncDbSheetHelper helper(pWorksheet);
			VS(helper.Init(&attachmentIdMap));
			VS(helper.Exec());
		}
		else if(spDbSheetOp->GetSheetSyncType() == DbSheet_St_Sql)
		{
			DbSyncSheet::ClearSyncRecordFlag(spDbSheetOp);
			DbSyncSheet::ClearSyncFieldFlag(spDbSheetOp);
			VS(spDbSheetOp->SetSheetSyncType(DbSheet_St_None));
		}
	}
}

KEtWorkbook::KEtWorkbook(_Workbook* wb,
		et::KConApplication* consoleApp, WebLogFunc func, QString fileId)
	: KRevisionControl(wb, new EtTaskExecutor(this), new KEtOpTrsfmt(), func)
	, m_ptrPictureUploadHelper(new PictureUploadHelper(this))
	, m_pRangeSliceHelper(new RangeSliceHelper::KRangeSliceHelper(this))
	, m_consoleApp(consoleApp)
	, m_bExpFmla(true)
	, m_lastBroadcastTime(std::chrono::system_clock::now())
	, m_delayBroadcatData(nullptr)
	, m_initCache(nullptr)
	, m_queryExecutor(NULL)
	, m_fileId(krt::utf16(fileId))
	, m_calcStatus(finish)
	, m_hasHiddenCellImgSetCache(false)
	, m_serviceType(ServiceType_Normal)
	, m_dbtCalcCellNeedAddDirty(false)
	, m_staleOnNotExec(false)
	, m_staleOnNotExecCount(0)
	, m_reInitCount(0)
	, m_inspectCount(0)
	, m_optInitCount(0)
	, m_maxSerVerCount(0)
	, m_serVerCount(0)
	, m_hitVerCount(0)
	, m_maxSerVerBwLen(0)
	, m_batchTransHelper(this)
{
	m_ptrWorkbook = wb;
	m_ptrApp = m_ptrWorkbook->GetApplication();
	m_book = ks_castptr<IKWorkbook>(m_ptrWorkbook)->GetBook();
	m_memoStat.reset(new DocMemoStat());

	m_book->GetWoStake()->initLogger(getLogger());

	static_cast<EtTaskExecutor*>(getTaskExecutor())->Init();

	initLastTaskSelection();

    REF_STYLE refStyle = RS_A1;
    IAppSettings* pSettings = m_ptrApp->GetAppSettings();
    refStyle = pSettings->GetReferenceStyle();
    IBookSetting* pSetting = m_book->GetWoStake()->getSetting();
    pSetting->setRefStyle(refStyle);

    m_book->GetWoStake()->SetUserOrganizes(&m_userOrganizesInfo);
    m_pUserOrganizesCache = new KUserOrganizesCache(m_book, &m_userOrganizesInfo);

	m_versionMgr = new KEtVersionManager(m_repProxy, m_ptrWorkbook, func);

	m_spDocSlim = m_ptrWorkbook->GetDocSlimHelper()->GetDocSlim();
	if (nullptr == m_spDocSlim)
	{
		_applogic_CreateObject(CLSID_KEtDocSlim, IID_IET_DocSlim, (void**)&m_spDocSlim);
		m_spDocSlim->Init(wb);
	}
	_updateCalcStatusOnOpen();

	ks_stdptr<IUnknown> unkOle;
	ks_stdptr<IKOleDocument> oleDoc;
	if (SUCCEEDED(m_book->GetExtDataItem(edBookOleDocument, &unkOle)))
	{
		unkOle->QueryInterface(IID_IKOleDocument, (void**)&oleDoc);
	}
	m_memoStat->UpdateOleDocMemory(oleDoc.get());

    m_pDvCustomListCache = new KDvCustomListCache(m_ptrWorkbook);

	m_cfCache.Init(m_book);
	IWorkbookObj* wbObj = m_ptrWorkbook->GetWoObject();
	wbObj->setCollectInfo(&m_collectInfo);
}

void KEtWorkbook::initLastTaskSelection()
{
	std::vector<RANGE> initRgs;
	m_lastTaskSelection = std::make_pair(INVALIDIDX, initRgs);
}

void KEtWorkbook::_updateCalcStatusOnOpen()
{
	ETCalculation calcMode = etCalculationAutomatic;
	m_ptrApp->get_Calculation(&calcMode);
	if (calcMode != etCalculationAutomatic)
	{
		BOOKERRORS* pBookErrors = NULL;
		m_book->GetBookErrors(&pBookErrors);
		bool bNeedRecalAll = false;
		if (pBookErrors)
			bNeedRecalAll = pBookErrors->fNeedRecalculateAll;
		_setCalcStatus(m_ptrApp->GetAppSettings()->GetCalculateBeforeSave() 
				&& !bNeedRecalAll ? finish : suspend);
	}
	else
	{
		_setCalcStatus(m_ptrApp->NeedResumeCalculate() ? calculating : finish);
	}
	updateCalcDiagnosisStatus();
}

KEtWorkbook::~KEtWorkbook()
{
	if (gs_procType == WebProcTypeMaster)
		m_signalMetrics.interruptAndWait();
	if (m_ptrWorkbook->GetWoObject())
		m_ptrWorkbook->GetWoObject()->setCollectInfo(nullptr);
	clearInitCache();
	delete m_ptrPictureUploadHelper;
	delete m_pRangeSliceHelper;
	delete m_delayBroadcatData;
	delete m_queryExecutor;
	delete m_pDvCustomListCache;
	for (auto it = m_themes.begin(); it != m_themes.end(); ++it)
		(*it)->deleteThis();
	for (auto it = m_mapDownloadImage.begin(); it != m_mapDownloadImage.end(); ++it)
		delete it->second;
	for (auto it = m_mapDownloadAttachmentImage.begin(); it != m_mapDownloadAttachmentImage.end(); ++it)
		delete it->second;
}

_Application* KEtWorkbook::GetCoreApp() 
{
	return m_ptrApp;
}

PictureUploadHelper * KEtWorkbook::GetPictureUploadHelper()
{
	return m_ptrPictureUploadHelper;
}

RangeSliceHelper::KRangeSliceHelper* KEtWorkbook::GetRangeSliceHelper()
{
	return m_pRangeSliceHelper;
}

_Workbook*	KEtWorkbook::GetCoreWorkbook() const
{
	return m_ptrWorkbook;
}

IET_DocSlim* KEtWorkbook::GetDocSlim()
{
	return m_spDocSlim;
}

void KEtWorkbook::SetCoreSettings(const WebetSettings *setting)
{
	m_serverSettings.fileSizeLimit = setting->fileSizeLimit;
}

void KEtWorkbook::SetFileVersionSize(WebInt version, WebFileSize size, bool bInit)
{
	if (PictureUploadHelper::hasUploaded() && gs_callback->collectInfo)
	{
		PictureUploadHelper::setUploaded(false);
		WebFileSize before = m_lastSaveInfo.size;
		WebFileSize after = size;

		QString name = QString("behaviour_picture_switch_size_%1_%2").arg(before).arg(after);
		BinWriter binWriter;
		binWriter.addKey("name");
		binWriter.addString(krt::utf16(name));
		binWriter.addKey("fileid");
		binWriter.addString(m_fileId.c_str());
		binWriter.addKey("count");
		binWriter.addInt32(1);

		BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};
		gs_callback->collectInfo("fileinclude", &slice);
	}

	m_lastSaveInfo.bSaved = true;
	m_lastSaveInfo.size = size;
	m_lastSaveInfo.version = version;

	if (GetBMP()->bDbSheet)
		return;

	if (!bInit && gs_callback->broadcast)
	{
		BinWriter bw;
		bw.addKey("fileSize");
		bw.addFloat64(m_lastSaveInfo.size);
		BinWriter::StreamHolder bt = bw.buildStream();
		WebSlice slice = {bt.get(), bw.writeLength()};
		gs_callback->broadcast(getMsgTypeName(msgType_SaveInfo), &slice, nullptr);
	}
}

WebID KEtWorkbook::GetDefInitSheet(PCWSTR name, IKWorksheet **ppWorksheet)
{
	ks_castptr<IKWorkbook> workbook(m_ptrWorkbook);
	IWorkbookObj* wbObj = workbook->GetWoObject();

	if (name != NULL)
	{
		IBook* bk = workbook->GetBook();
		for (int i = 0, cnt = wbObj->getSheetCount(); i < cnt; ++i)
		{
			ks_stdptr<ISheet> spSheet;
			bk->GetSheet(i, &spSheet);
			PCWSTR sheetName = NULL;
			spSheet->GetName(&sheetName);
			if(xstrcmp(name, sheetName) == 0)
			{
				IWorksheetObj *shtObj = wbObj->getSheetItem(i);
				if (ppWorksheet)
				{
					*ppWorksheet = shtObj->GetWorksheet();
					(*ppWorksheet)->AddRef();
				}
				return shtObj->objId();
			}
		}
	}

	IDX iSheet = -1;
	workbook->GetActiveWorksheet()->GetSheet()->GetIndex(&iSheet);
	IWorksheetObj *shtObj = wbObj->getSheetItem(iSheet);
	if (ppWorksheet)
	{
		*ppWorksheet = shtObj->GetWorksheet();
		(*ppWorksheet)->AddRef();
	}
	return shtObj->objId();
}

WebID KEtWorkbook::GetFirstVisibleSheet(IKWorksheet **ppWorksheet, SHEETTYPE st)
{
	ks_castptr<IKWorkbook> workbook = m_ptrWorkbook;
	IWorkbookObj* wbObj = workbook->GetWoObject();
	IKWorksheets * wss = workbook->GetWorksheets();
	IBook* bk = workbook->GetBook();
	for (int i = 0, cnt = wbObj->getSheetCount(); i < cnt; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		bk->GetSheet(i, &spSheet);
		BOOL visible = FALSE;
		spSheet->GetVisible(&visible);
		if (!visible) continue;

		if (st != stUnknown)
		{
			SHEETTYPE curSt;
			spSheet->GetFullType(&curSt);
			if (curSt != st) continue;
		}
		
		IWorksheetObj *shtObj = wbObj->getSheetItem(i);
		if (ppWorksheet)
		{
			*ppWorksheet = shtObj->GetWorksheet();
			(*ppWorksheet)->AddRef();
		}
		return shtObj->objId();
	}
	return INVALIDOBJID;
}

void KEtWorkbook::SetExportFmla(bool bExpFmla)
{
	m_bExpFmla = bExpFmla;
}

void KEtWorkbook::SetCreatorUserID(PCWSTR userId)
{
	m_book->GetWoStake()->SetCreatorUserID(userId);
}

void KEtWorkbook::UpdateDbRecords(ISheet *pSheet, double dateTime)
{
	if (!pSheet->IsDbSheet())
		return;

	PCWSTR userId = m_book->GetWoStake()->GetCreatorUserID();

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	VS(DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp));
	ASSERT(spDbSheetOp);
	IDbRecordsManager *pRecordsManager = spDbSheetOp->GetRecordsManager();
	if (pRecordsManager == nullptr || !pRecordsManager->haveNull())
		return;

	const IDBIds *pIds = spDbSheetOp->GetAllRecords();
	for (int i = 0; i < pIds->Count(); ++i)
	{
		EtDbId id = pIds->IdAt(i);
		pRecordsManager->UpdateRecord(id, userId, dateTime);
	}
}

void KEtWorkbook::UpdateDbFields(ISheet *pSheet)
{
	if (!pSheet->IsDbSheet())
		return;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	VS(DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp));
	ASSERT(spDbSheetOp);
	IDbFieldsManager *pFieldsManager = spDbSheetOp->GetFieldsManager();
	if (pFieldsManager == nullptr)
		return;
	VS(pFieldsManager->Update(TRUE));

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	VS(DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews));
	if (spDbSheetViews == nullptr)
		return;

	ks_stdptr<IDbtBookCtx> spDbBookCtx;
	VS(pSheet->LeakBook()->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbBookCtx));
	ET_DbSheet_Sync_Type syncType = spDbSheetOp->GetSheetSyncType();
	if ((syncType == DbSheet_St_DB || syncType == DbSheet_St_GridSheet) && spDbBookCtx->GetOpenIODataVersion() < DbIoDataVersion_Sync_FIELD)
	{
		class AllFieldsEnum : public IDbFieldEnum
		{
			using CallBack = std::function<HRESULT(IDbField*)>;
		public:
			AllFieldsEnum(const CallBack &cb)
				: m_cb(cb)
			{}

			STDPROC Do(IDbField *pField) override
			{
				return m_cb(pField);
			}
		private:
			const CallBack m_cb;
		};

		AllFieldsEnum fieldsEnum([](IDbField *pField) -> HRESULT { pField->SetSyncField(TRUE);return S_OK;});
    	pFieldsManager->EnumFields(&fieldsEnum);
	}
	
	class AllDbViewsEnum : public IDbViewEnum
	{
		using CallBack = std::function<HRESULT(IDBSheetView*)>;
	public:
		AllDbViewsEnum(const CallBack &cb)
			: m_cb(cb)
		{}

		STDPROC Do(IDBSheetView *pView) override
		{
			return m_cb(pView);
		}
	private:
		const CallBack m_cb;
	};

	AllDbViewsEnum viewsEnum([&spDbBookCtx](IDBSheetView *pView) -> HRESULT {
		// 确保 ResetVisibleFields内,  gainMutableFinalVisibleFields调用,初始时最终可见列与可见列一样  //后续可添加单独接口处理 
		const IDBIds* visiFields = pView->GetVisibleFields();
		UINT cnt = visiFields->Count();
		std::vector<EtDbId> vecId;
		for (INT i = 0; i < cnt; ++i)
		{
			EtDbId id = visiFields->IdAt(i);
			vecId.push_back(id);
		}
		pView->ResetVisibleFields(&vecId[0], cnt);
		return S_OK;
	});
	spDbSheetViews->EnumViews(&viewsEnum);
}

void KEtWorkbook::UpdateDbViews(ISheet *pSheet)
{
	if (!pSheet->IsDbSheet())
		return;

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	VS(DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews));
	if (spDbSheetViews == nullptr)
		return;

	ks_stdptr<IDbtBookCtx> spDbBookCtx;
	VS(pSheet->LeakBook()->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbBookCtx));
	
	class AllDbViewsEnum : public IDbViewEnum
	{
		using CallBack = std::function<HRESULT(IDBSheetView*)>;
	public:
		AllDbViewsEnum(const CallBack &cb)
			: m_cb(cb)
		{}

		STDPROC Do(IDBSheetView *pView) override
		{
			return m_cb(pView);
		}
	private:
		const CallBack m_cb;
	};

	AllDbViewsEnum viewsEnum([&spDbBookCtx](IDBSheetView *pView) -> HRESULT {
		// todo: 这里的清洗一定要在update里做吗? 如果不是, 应当移动到文件清洗环节; 如果是, 在update的读盘重构中要一并解决
		if (spDbBookCtx->GetOpenIODataVersion() < DbIoDataVersion_Date_Group_Adjust)
		{
			// 对数字格式与筛选条件、分组条件不一致的情况进行调整
			pView->AdjustInconsistentFilters();
			pView->AdjustInconsistentGroupConditions();
		}
		VS(pView->Update(DbSheetViewUpdateFlag::UpdateAll));
		return S_OK;
	});
	spDbSheetViews->EnumViews(&viewsEnum);
}

void KEtWorkbook::UpdateDbSheets()
{
	// 恢复对所在组件的判断。这个判断在 4fc2d50870 上有过，但后来莫名其妙地消失了，file history无法追踪消失代码所在commit
	if (!GetBMP()->bDbSheet && !GetBMP()->bKsheet)
		return;

	alg::ETDOUBLE time;
	QDateTime qtTime = QDateTime::currentDateTime();
	alg::VDS_BuildDateTime(qtTime.date().year(), qtTime.date().month(), qtTime.date().day(),
		qtTime.time().hour(), qtTime.time().minute(), qtTime.time().second(), m_book->Is1904DateSystem(), time);
	double dateTime = time.Get();

	int sheetCnt = 0;
	m_book->GetSheetCount(&sheetCnt);
	for (int i = 0; i < sheetCnt; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		m_book->GetSheet(i, &spSheet);
		if (!spSheet)
			continue;
		if (spSheet->IsDbDashBoardSheet())
		{
			UpdateDbDashBoardModules(spSheet);
			UpdateDbDashBoardViews(m_book, spSheet);
			continue;
		}
		if (!spSheet->IsDbSheet())
			continue;
		UpdateDbRecords(spSheet, dateTime);
		UpdateDbFields(spSheet);
		UpdateDbViews(spSheet);
	}

	VS(_appcore_GainDbSheetContext()->AfterFileOpen());
#ifdef _DEBUG
	ks_stdptr<IDbtBookCtx> spDbBookCtx;
	VS(m_book->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbBookCtx));
	spDbBookCtx->SetFileOpenFinished();
#endif
}

void KEtWorkbook::RemoveInvalidTimerTasks()
{
	if (!GetBMP()->bDbSheet && !GetBMP()->bKsheet)
		return;

	ks_stdptr<IDbAutomations> spAutomations;
	m_book->GetExtDataItem(edBookDbAutomations, (IUnknown**)&spAutomations);
	if (spAutomations)
		spAutomations->RemoveExpiredTimerTasks();
}

bool KEtWorkbook::IsExportFmla()
{
	return m_bExpFmla;
}

BMP_PTR KEtWorkbook::GetBMP()
{
	return m_book->GetBMP();
}

void KEtWorkbook::SetValidateName(PCWSTR name, UINT uNameLen)
{
	if (name == nullptr || uNameLen == 0) return;
	
	m_book->SetName(name, uNameLen);
	if (m_book->GetWoStake()->fetchFlags()->bSheetNameFromFile)
	{
		PCWSTR chr = xstrrchr(name, __Xc('.'));
		std::vector<WCHAR> tmp;
		if (chr != nullptr && chr != name)
		{
			tmp.assign(name, chr);
			tmp.push_back(0);
		}
		else
		{
			tmp.assign(name, name+uNameLen+1);
		}
		
		alg::ValidateSheetName(&tmp[0]);
		ks_stdptr<ISheet> sht; VS(m_book->GetSheet(0, &sht));
		sht->SetName(&tmp[0], ks_wcsnlen_s(&tmp[0], _MAX_FNAME));
		m_book->GetWoStake()->fetchFlags()->bSheetNameFromFile = false;
	}
}

void KEtWorkbook::ViewRect2CoreRect(drawing::ShapeVisual* pShapeVisual, QRectF& view, QRectF& core)
{
	if (!pShapeVisual) return;

	ks_castptr<drawing::ShapeTreeControl> spShapeTreeCtrl = pShapeVisual->getLayerControl();
	ks_stdptr<IKDrawingSession> spDrawingSession = spShapeTreeCtrl->getDrawingSession();
	ASSERT(spDrawingSession);

	ks_stdptr<IKShapeAbsolutePos> spShapeAbsPos;
	HRESULT hr = spDrawingSession->GetShapeAbsolutePos(&spShapeAbsPos);
	ASSERT(hr == S_OK);
	RECT rcCore;
	RECT rcView = drawing::QRect2RECT(view.toRect());
	hr = spShapeAbsPos->Absolute2Anchor(pShapeVisual->coreShape(), &rcView, &rcCore);
	if (hr == S_OK) { // 先容下错，新建图表后，执行撤销再恢复会崩溃
		core = drawing::RECT2QRect(rcCore);
	}
}

void KEtWorkbook::CoreRect2ViewRect(drawing::ShapeVisual* pShapeVisual, QRectF& core, QRectF& view)
{
	if (!pShapeVisual) return;

	ks_castptr<drawing::ShapeTreeControl> spShapeTreeCtrl = pShapeVisual->getLayerControl();
	ks_stdptr<IKDrawingSession> spDrawingSession = spShapeTreeCtrl->getDrawingSession();
	ASSERT(spDrawingSession);

	ks_stdptr<IKShapeAbsolutePos> spShapeAbsPos;
	HRESULT hr = spDrawingSession->GetShapeAbsolutePos(&spShapeAbsPos);
	ASSERT(hr == S_OK);
	RECT rcView;
	RECT rcCore = drawing::QRect2RECT(core.toRect());
	hr = spShapeAbsPos->Anchor2Absolute(pShapeVisual->coreShape(), &rcCore, &rcView);
	view = drawing::RECT2QRect(rcView);
}

ks_stdptr<etoldapi::Range> KEtWorkbook::CreateRangeObj(const RANGE& rg)
{
    return util::CreateRangeObj(m_ptrWorkbook.get(), rg);
}

ks_stdptr<etoldapi::Range> KEtWorkbook::CreateRangeObj(const std::vector<RANGE> &rgVec)
{
	ks_stdptr<etoldapi::Range> host;
	if (rgVec.empty())
		return host;

	IDX sheetIdx = rgVec[0].SheetFrom();
	ks_stdptr<IKRanges> spRanges;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRanges);
	for (auto it = rgVec.begin(); it != rgVec.end(); ++it)
	{
		ASSERT(it->SheetFrom() == it->SheetTo());
		ASSERT(it->SheetFrom() == sheetIdx);
		spRanges->Append(alg::STREF_THIS_BOOK, *it);
	}

	ks_castptr<IKWorkbook> wb(m_ptrWorkbook);
	IKWorksheet* ws = wb->GetWorksheets()->GetSheetItem(sheetIdx);
	VS(ws->GetRangeByData(spRanges, &host));
	return host;
}

void KEtWorkbook::CreateIKRangeObj(const std::vector<RANGE> &rgVec, IKRanges** spRanges)
{
	if (rgVec.empty())
		return;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)spRanges);
	for (auto it = rgVec.begin(); it != rgVec.end(); ++it)
	{
		ASSERT(it->SheetFrom() == it->SheetTo());
		(*spRanges)->Append(alg::STREF_THIS_BOOK, *it);
	}
}

ks_stdptr<etoldapi::Range> KEtWorkbook::CreateRangeObj(interface IKRanges *pRgs)
{
	ASSERT(NULL != pRgs);
	UINT count = 0;
	pRgs->GetCount(&count);
	if (count <= 0 )
		return NULL;
	
	const RANGE *pRg = NULL;
	pRgs->GetItem(0, NULL, &pRg);
	ASSERT(pRg->SheetFrom() == pRg->SheetTo());
	ks_castptr<IKWorkbook> wb(m_ptrWorkbook);
	IKWorksheet* ws = wb->GetWorksheets()->GetSheetItem(pRg->SheetFrom());
	ks_stdptr<etoldapi::Range> host;
	VS(ws->GetRangeByData(pRgs, &host));
	return host;
}

drawing::ShapeTree* KEtWorkbook::getShapeTree(WebID objSheetOrBook, IEtRevisionContext* ctx, binary_wo::BinWriter& writer)
{
	if (objSheetOrBook == GetCoreWorkbook()->getDocumentObject()->objId())
	{
		ks_stdptr<IKDrawingCanvas> spCanvas;
		oplGetCellImgOplData(GetCoreWorkbook()->GetBook(), &spCanvas);
		ks_castptr<drawing::ShapeTree> pShapeTree = spCanvas;
		return pShapeTree;
	}

	IDX sheetIndex = ctx->getSheetIndex(objSheetOrBook);
	if (sheetIndex < 0)
	{
		writer.addStringField(__X("worksheet not found."), "error");
		return NULL;
	}

	ks_stdptr<IKWorksheets> ptrWorksheets = m_ptrWorkbook->GetWorksheets();
	ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIndex);
	if (!ptrWorksheet)
	{
		writer.addStringField(__X("worksheet not found."), "error");
		return NULL;
	}

	updateRenderShape(ptrWorksheet);

	ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
	oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
	if (!spCanvas)
		return NULL;
	ks_castptr<EtShapeTree> pshapeTree = spCanvas;
	// 注意: 取出spCanvas时增加的引用计数, 在裸指针返回时已经减回去了. 对调用方来说, 引用计数没有变化,
	// 内部的计数增减是无感知的. 语义上虽然符合, 不过这样的设计好不好呢?
	return pshapeTree.get();
}

WebInt KEtWorkbook::UpdateShapes(
	WebID objSheetOrBook,
	const std::vector<uint32>& vecShapeIds,
	const std::vector<uint32>& shapeLimitSizes,
	const ks_wstring& updateType,
	IEtRevisionContext* ctx,
	binary_wo::BinWriter& writer)
{
	writer.addKey("objSheetOrBook");
	writer.addAbsObjID(objSheetOrBook, true);
	drawing::ShapeTree* shapeTree = getShapeTree(objSheetOrBook, ctx, writer);
	if (!shapeTree)
		return WO_FAIL;

	writer.addKey("shapes");
	writer.beginArray();

#ifndef Q_OS_WIN
	auto start_time = std::chrono::steady_clock::now();
	std::chrono::milliseconds max_duration(50);
	auto end_time = start_time + max_duration;
#else
	auto start_time = ::GetTickCount64();
	auto end_time = start_time + 50;
#endif
	size_t i = 0;
	for (auto it = vecShapeIds.cbegin(); it != vecShapeIds.cend(); ++it, ++i)
	{
		uint32 shapeId = *it;
		writer.beginStruct();
		writer.addUint32Field(shapeId, "shapeId");
		const drawing::AbstractShape* shape = shapeTree->find(shapeId);
		if (shape)
		{
			writer.addStringField(updateType.c_str(), "type");
			if (__X("ShapeProps") == updateType)
			{
				_updateShapeProps(shape, ctx, writer);
			}
			else if (__X("ShapeOriginalPic") == updateType)
			{
				_updateShapeOriginalPic(shape, writer, ctx);
			}
			else if (__X("ShapeCellImage") == updateType)
			{
				double scale = ctx->getUser()->getDpi() / 96.0;
				uint32 limitSize = 0;
				if (i < shapeLimitSizes.size() && shapeLimitSizes[i] > 0)
				{
					const double px2ti = 15;
					const QRectF rcfShape = shape->getShapeBounds();
					const double shapeSize = rcfShape.width() * rcfShape.height();
					limitSize = shapeLimitSizes[i];
					double maxImgSize = limitSize * px2ti * px2ti;

					if (shapeSize > maxImgSize)
					{
						scale *= std::sqrt(maxImgSize / shapeSize);
					}
				}


				_updateShapeImage(objSheetOrBook, shape, scale, ctx, writer);
				writer.addUint32Field(limitSize, "limitSize");
			}
			else
			{
				double dpiScale = ctx->getUser()->getDpi() / 96.0;
				_updateShapeImage(objSheetOrBook, shape, dpiScale, ctx, writer);
			}
		}
		else
		{
			WOLOG_WARN << "[UpdateShapes] failed to find shape, shapeId: " << shapeId;
			writer.addStringField(__X("find shape failed."), "error");
		}
		writer.endStruct();

#ifndef Q_OS_WIN
		auto current_time = std::chrono::steady_clock::now();
#else
		auto current_time = ::GetTickCount64();
#endif
		if (current_time >= end_time)
			break;
	}

	writer.endArray();
	return WO_OK;
}

void KEtWorkbook::_updateShapeOriginalPic(const drawing::AbstractShape* shape, binary_wo::BinWriter& writer, IEtRevisionContext* ctx)
{
	if (!shape->isPicture())
	{
		WOLOG_WARN << "[updateShapeOriginalPic] is not picture shape, shapeId: " << (shape->hasId() ? shape->id() : -1);
		writer.addStringField(__X("is not picture shape."), "error");
		return;
	}

	if (!checkCellImgProtection(shape, ctx))
	{
		WOLOG_WARN << "[updateShapeOriginalPic] cell image has protected, shapeId: " << (shape->hasId() ? shape->id() : -1);
		writer.addStringField(__X("get picture failed."), "error");
		return;
	}

	QString sha1 = util::UploadImage(shape);
	writer.addStringField(krt::utf16(sha1), "sha1");
}

void KEtWorkbook::_downloadPictureImage(const drawing::AbstractShape *shape, IKBlipAtom *pBlipAtom, ks_bstr &path,
                                 WebID objSheet, IEtRevisionContext *ctx)
{
    bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(path.c_str());

    int downloadExternalPicCount = 0;
    SImageDownload *pImageDownload = NULL;
    if (isAttachment)
    {
        ks_string strAttachFileId = (QString::fromUtf16(util::getAttachmentId(path))).toStdString();

        auto it = m_mapDownloadAttachmentImage.find(strAttachFileId);
        if (it == m_mapDownloadAttachmentImage.end())
        {
            pImageDownload = new SImageDownload;
            m_mapDownloadAttachmentImage.insert(std::make_pair(strAttachFileId, pImageDownload));

            pBlipAtom->SetUsage(ksoblipUsageUrlDownloading);

            WebDownloadArg downloadImgArg;
            downloadImgArg.pObjectLocator = strAttachFileId.c_str();
            downloadImgArg.tag = WebDownloadByAttachmentId;
            gs_callback->downloadObject(&downloadImgArg);
        }
        else
        {
            pImageDownload = it->second;
        }
    }
    else
    {
        QString qTarget = QString::fromUtf16(path.c_str());
        ks_string strUrl = qTarget.toStdString();

        auto it = m_mapDownloadImage.find(strUrl);
        if (it == m_mapDownloadImage.end())
        {
            pImageDownload = new SImageDownload;
            m_mapDownloadImage.insert(std::make_pair(strUrl, pImageDownload));

            pBlipAtom->SetUsage(ksoblipUsageUrlDownloading);

            WebDownloadArg downloadImgArg;
            downloadImgArg.pObjectLocator = strUrl.c_str();
            downloadImgArg.tag = WebDownloadByUrl;
            gs_callback->downloadObject(&downloadImgArg);

            if (shape->getModelType() == ET_MODEL_CELLIMAGESHAPE)
                ++downloadExternalPicCount;
        }
        else
        {
            pImageDownload = it->second;
        }
    }

    pImageDownload->listShape.push_back((drawing::AbstractShape *)shape);
    QString qConnID = QString::fromUtf16(ctx->getUser()->connID());
    pImageDownload->listConid.push_back(qConnID.toStdString());
    pImageDownload->listSheetObj.push_back(objSheet);

    if (downloadExternalPicCount > 0)
    {
        ctx->collectInfo("behaviour_request_download_pic", downloadExternalPicCount);
    }
}

bool KEtWorkbook::_downloadImage(const drawing::AbstractShape* shape, WebID objSheet, IEtRevisionContext* ctx)
{
	bool downloading = false;
	ks_bstr path;
	HGBL hgbl = NULL;
	IKBlipAtom* pBlipAtom = shape->picID();
	if (shape->isGroupShape())
	{
		size_t childCount = shape->getChildCount();
		for (size_t i = 0; i < childCount; i++)
		{
			ks_castptr<drawing::AbstractShape> child = shape->getChild(i);
			downloading |= _downloadImage(child, objSheet, ctx);
		}
	}
	else if (shape->isPicture() && pBlipAtom
			&& pBlipAtom->GetLinkPath(&path) == S_OK && !path.empty()
			&& pBlipAtom->GetHGlobal(&hgbl) != S_OK)
	{
		_downloadPictureImage(shape, pBlipAtom, path, objSheet, ctx);
		downloading = true;
	}
	return downloading;
}

void KEtWorkbook::_updateShapeImage(WebID objSheet, const drawing::AbstractShape* shape, double scale, IEtRevisionContext* ctx, binary_wo::BinWriter& writer)
{
    if (!checkCellImgProtection(shape, ctx))
    {
		WOLOG_WARN << "[updateShapeImage] cell image has protected, shapeId: " << (shape->hasId() ? shape->id() : -1);
		writer.addStringField(__X("get picture failed."), "error");
		_serialShapeRect(shape, writer, ctx);
		return;
    }

	bool downloading = _downloadImage(shape, objSheet, ctx);
	if(downloading)
	{
		writer.addBoolField(true, "downloading");
		return;
	}

	if (ctx->getProtectionCtx()->isBookHasHiddenProperty() && shape->hasChart())
	{
		ks_castptr<EtChartLayer> chartLayer = shape->getChild(0);
		if (chartLayer)
		{
			shape->clearSha1Cache();
		}
	}

	QByteArray sha1;
	QByteArray picData;
	ShapeDrawingContextScope shapeDrawingContext(GetCoreApp(), shape, 96, scale);
	if (shape->getPicture(sha1, picData, (qreal)scale, true, false) == WO_OK)
	{
		if (!picData.isEmpty())
		{
			WebSlice slice = { (WebByte*)(picData.data()), picData.size() };
			gs_callback->uploadImage(sha1.data(), NULL, &slice, nullptr);
		}
		writer.addStringField(krt::utf16(QString(sha1)), "sha1");
		writer.addInt16Field(ctx->getUser()->getDpi(), "dpi");
	}
	else
	{
		WOLOG_WARN << "[updateShapeImage] get picture failed, shapeId: " << (shape->hasId() ? shape->id() : -1);
		writer.addStringField(__X("get picture failed."), "error");
	}

	if (shape->getModelType() != ET_MODEL_CELLIMAGESHAPE && shape->hasTransform2D())
	{
		KSerialWrapBinWriter acpt(writer, ctx);
		wo::KDrawingSerializer::serialTransform2D("transform2D", shape, &acpt);
	}
	_serialShapeRect(shape, writer, ctx);
}

bool KEtWorkbook::getShapePicData(drawing::AbstractShape* shape, QByteArray& sha1, QByteArray& data, int dpi)
{
	ShapeDrawingContextScope shapeDrawingContext(GetCoreApp(), shape, dpi, 1.0);
	return shape->getPicture(sha1, data, dpi / 96.0) == WO_OK;
}

void KEtWorkbook::_serialShapeRect(const drawing::AbstractShape* shape, binary_wo::BinWriter& writer, IEtRevisionContext* ctx)
{
	KSerialWrapBinWriter acpt(writer, ctx);
	shape->serialRect(&acpt);
}

void KEtWorkbook::_updateShapeProps(const drawing::AbstractShape* shape, IEtRevisionContext* ctx, binary_wo::BinWriter& writer)
{
	if (shape->hasTransform2D())
	{
		KSerialWrapBinWriter acpt(writer, ctx);
		wo::KDrawingSerializer::serialTransform2D("transform2D", shape, &acpt);
	}

	_serialShapeRect(shape, writer, ctx);
}

void KEtWorkbook::onSubmitShapePicture()
{
	if (gs_callback->uploadImage && getShapePicCount() > 0) {
		WebPicData* picDatas = NULL;
		WebInt size = base_type::getPicDatas(&picDatas);
		for (WebInt i = 0; i < size; i++)
		{
			if (picDatas[i].btLenPic <= 0)
				continue;

			WebSlice slice = {picDatas[i].btPic, picDatas[i].btLenPic};
			gs_callback->uploadImage(picDatas[i].strSha1, NULL, &slice, nullptr);
		}
		base_type::deletePicDatas(size, picDatas);
	}	
}

WebInt KEtWorkbook::transformTasks(KwTasks& tasks, IEtRevisionContext* ctx)
{
	return base_type::_transformTasks(tasks, ctx);
}

WebInt KEtWorkbook::transformClientTasks(KwTasks& tasks, WebInt transDataBase, WebInt transTaskBase, IEtRevisionContext* ctx)
{
	return base_type::_transformClientTasks(tasks, transDataBase, transTaskBase, ctx);
}

WebInt KEtWorkbook::executeTasks(KwTasks& tasks, IEtRevisionContext* ctx)
{
	WebInt res = WO_OK;
	{
		m_coreMeric.onBeforeExecTasks();
		wo::util::CallTimeStat callTime("WbExecuteTasks", nullptr, [this](unsigned int ms) { 
				this->m_coreMeric.setTasksTime(ms);
			}, false, m_coreMeric.isEnable());

		IWoETSettings* pWoSettings = _kso_GetWoEtSettings();
		res = base_type::_executeTasks(tasks, ctx, pWoSettings->IsEnableVersionTag());
	}
	onDataChanged(ctx);
	return res;
}

WebInt KEtWorkbook::serializeInitData(IEtRevisionContext* ctx, binary_wo::BinWriter* bw)
{
	ctx->setUseObjSet(true);
	_updateRenderView();
	rebuildSyncObjsVec();
	
	KBinSizeU32Mc<binary_wo::BinWriter> mcSize(&m_coreMeric, bw, KU32MetricItem::kSerialInitBytes);
	wo::util::CallTimeStat callTime(nullptr, nullptr, [this](unsigned int us) { 
			this->m_coreMeric.setSerInitTime(us);
		}, false, m_coreMeric.isEnable());
		
	WebInt ret = base_type::_serializeInitData(ctx, bw);
	return ret;
}

WebInt KEtWorkbook::serializeVersions(IEtRevisionContext* ctx, binary_wo::BinWriter* bw)
{
	m_coreMeric.onSerializeVersionsBegin();
	ctx->setUseObjSet(true);
	_updateRenderView();
	rebuildSyncObjsVec();
	int32 oldBwLen = bw->getBufferWriteLength() + bw->getNameBufferWriteLength();
	
	WebInt ret = WO_OK;
	{
		wo::util::CallTimeStat callTime(nullptr, nullptr, [this](unsigned int us) { 
			this->m_coreMeric.setSerVersionTime(us);
		}, false, m_coreMeric.isEnable());
	
		ret = base_type::_serializeVersions(ctx, bw);
	}

	{
		WebInt beginIndex = ctx->getBaseTaskVersion() + 1;
		int serVer = m_versionMgr->getTaskVersionCount() - beginIndex;
		if (serVer > 0) 
		{
			m_maxSerVerCount = std::max(m_maxSerVerCount, serVer);
			m_serVerCount += serVer;
			
			int hitVer = ctx->initVersionCount() - beginIndex;
			hitVer = std::max(hitVer, 0);
			m_hitVerCount += hitVer;
			
			int32 newBwLen = bw->getBufferWriteLength() + bw->getNameBufferWriteLength();
			int32 bwLen = (newBwLen - oldBwLen);
			if (bwLen > 1024 * 1024)
			{
				WOLOG_INFO << "serializeVersions: byte: " << bwLen;
			}
			m_coreMeric.addU32Metrics(KU32MetricItem::kSerVersBytes, bwLen);
			m_maxSerVerBwLen = std::max(m_maxSerVerBwLen, bwLen);
			
			m_coreMeric.onSerialVersions(serVer, hitVer);
		}
	}
	return ret;
}

WebInt KEtWorkbook::execTasksDirect(KwTasks& tasks, IRevisionContext* ctx)
{
	tasks.defaultTransCmd();
	WebInt res = base_type::_execTasksDirect(tasks, ctx);
    if (res == WO_OK)
        _updateRenderView();
	onDataChanged(ctx);
    return res;
}

WebInt KEtWorkbook::undo(IRevisionContext* ctx, PCWSTR versionTag, bool allowPartialSucc, int undoStep)
{
	ks_castptr<KEtRevisionContext> pCtx = ctx;
	m_coreMeric.onBeginUndoRedo(true);
	util::CmdTimeStat cmdTimeStat(this, pCtx, [this](unsigned int ms) {
		this->coreMetric().addUndoRedoTime(ms, true);
	});
	cmdTimeStat.start(__X("undo"), true);
	
	m_batchTransHelper.onUndoRedo(pCtx);
	
	IWoETSettings* pWoSettings = _kso_GetWoEtSettings();
	WebInt res = WO_OK;
	if (pWoSettings->IsEnableVersionTag())
		res = base_type::_undoByTag(ctx, versionTag, allowPartialSucc, undoStep);
	else
		res = base_type::_undo(ctx, undoStep);

	WOLOG_INFO << "[undo]" << " res: " << (res == WO_OK ? "OK" : "FAIL");
	_updateCalcStatusSimple();
	onDataChanged(ctx);

	++(m_undoRedoStat.m_undoTotalCnt);
	if (res < 0)
		++(m_undoRedoStat.m_undoFailTime);

	return res;
}

WebInt KEtWorkbook::redo(IRevisionContext* ctx, PCWSTR versionTag, int redoStep)
{
	ks_castptr<KEtRevisionContext> pCtx = ctx;
	m_coreMeric.onBeginUndoRedo(false);
	util::CmdTimeStat cmdTimeStat(this, pCtx, [this](unsigned int ms) {
		this->coreMetric().addUndoRedoTime(ms, false);
	});
	cmdTimeStat.start(__X("redo"), true);
	
	m_batchTransHelper.onUndoRedo(pCtx);
	
	IWoETSettings* pWoSettings = _kso_GetWoEtSettings();
	WebInt res = WO_OK;
	if (pWoSettings->IsEnableVersionTag())
		res = base_type::_redoByTag(ctx, versionTag, redoStep);
	else
		res = base_type::_redo(ctx);

	WOLOG_INFO << "[redo]" << " res: " << (res == WO_OK ? "OK" : "FAIL");
	_updateCalcStatusSimple();
	onDataChanged(ctx);

	++(m_undoRedoStat.m_redoTotalCnt);
	if (res < 0)
		++(m_undoRedoStat.m_redoFailTime);

	return res;
}

//static
IKAutoFilter* KEtWorkbook::getAutoFilterInRange(ISheet* pCoreSheet, const RANGE& rg, PCWSTR filterID)
{
	ks_stdptr<ICoreListObjects> spLstObjs;
	ks_stdptr<ICoreListObject> spLstObj;
	pCoreSheet->GetExtDataItem(edSheetListObjects, (IUnknown**)&spLstObjs);
	spLstObjs->FindFirstItem(rg, &spLstObj);

	if (spLstObj != NULL)
		return spLstObj->GetAutoFilters()->GetFilter(filterID);
	else
		return pCoreSheet->GetAutoFilters()->GetFilter(filterID);
}

HRESULT KEtWorkbook::checkQueryFilterRange(IEtRevisionContext *ctx, const RANGE &filterRg, const RANGE &fieldRg)
{
    if (ctx->getProtectionCtx()->isBookHasHiddenProperty())
    {
        RANGE rg = filterRg.Intersect(fieldRg);
        if (rg.IsValid())
        {
            rg.SetRowFromTo(filterRg.RowFrom(), filterRg.RowTo());
            if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
            {
                return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
            }
        }
    }

    return S_OK;
}

HRESULT KEtWorkbook::QueryAutoFilterInfos(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj param, ISerialAcceptor* acpt)
{
    ASSERT(rg.IsValid());
	ks_stdptr<etoldapi::Range> host = CreateRangeObj(rg);
	ks_stdptr<_Worksheet> spSheet;
	host->get_Worksheet(&spSheet);
	ISheet* pCoreSheet = spSheet->GetSheet();
	IKAutoFilter* pAutoFilter = getAutoFilterInRange(pCoreSheet, rg, filterID);
	if (pAutoFilter == NULL)
		return E_FAIL;

	RANGE filterRg(pCoreSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.Contain(rg))
		return E_FAIL;

	ks_castptr<IEtRevisionContext> ctx = acpt->getContext();
	HRESULT hr = checkQueryFilterRange(ctx, filterRg, rg);
    if (FAILED(hr)) return E_FAIL;

	int nField = rg.ColFrom() - filterRg.ColFrom();

	ks_stdptr<IBook> spBook;
	pCoreSheet->GetBook(&spBook);

	acpt->addKey("fieldData");
	acpt->beginStruct();

	ks_stdptr<IAutoFilterValues> spFilterValues;
	ValuesNode* pValuesRoot = NULL;

    AutofilterParamSerialHelper paramSerialHelper;
    paramSerialHelper.InitForWidget(nField, pAutoFilter, NULL, spBook);
    paramSerialHelper.Serialize(acpt);

    ks_stdptr<IAutoFilterFormatItems> spFmtItems;
    pAutoFilter->GetFilterFormatItems(nField, ETStringToolsOpt_None, 0, EOp_None, &spFmtItems);
    AutoFilterFormatItemsHelper fortmateItemsHelper(spFmtItems, spBook, pAutoFilter, nField);
    fortmateItemsHelper.Serialize(acpt);

    SortParam sortparam(BYNAME, ASC, AUTO, false);

    readSortParam(param, sortparam);

    //不开启置顶功能
    if (!sortparam.pinedItems())
    {
        sortparam.setSortTrigger(MANU);
    }
    hr = pUser->gainFilterCacheData(pAutoFilter, nField, FALSE, &spFilterValues, &pValuesRoot, sortparam);
    if (SUCCEEDED(hr))
    {
        AutoFilterItemsHelper itemsHelper(pAutoFilter, spFilterValues, pValuesRoot, param, this, nField);
        itemsHelper.setResort(false);
        itemsHelper.Serialize(acpt);
    }

	acpt->endStruct();

	_serialiseSortInfo(pAutoFilter, nField, acpt, fortmateItemsHelper);
    AutofilterOperatorSerializeHelper filterOphelper(pAutoFilter, nField);
    filterOphelper.Serialize(acpt);

    wo::sa::addRANGE("rg", filterRg, acpt);
	return hr;
}

HRESULT KEtWorkbook::QueryAutoFilterCustomValues(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, ISerialAcceptor* acpt)
{
	ASSERT(rg.IsValid());
	ks_stdptr<etoldapi::Range> host = CreateRangeObj(rg);
	ks_stdptr<_Worksheet> spSheet;
	host->get_Worksheet(&spSheet);
	ISheet* pCoreSheet = spSheet->GetSheet();
	IKAutoFilter* pAutoFilter = getAutoFilterInRange(pCoreSheet, rg, filterID);
	if (pAutoFilter == NULL)
		return E_FAIL;

	RANGE filterRg(pCoreSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.Contain(rg))
		return E_FAIL;

	ks_castptr<IEtRevisionContext> ctx = acpt->getContext();
	VS(checkQueryFilterRange(ctx, filterRg, rg));

	int nField = rg.ColFrom() - filterRg.ColFrom();
	ks_stdptr<IAutoFilterCustom> spValues;
	HRESULT hr = pAutoFilter->GetCustomFilterData(nField, ETStringToolsOpt_None, &spValues);
	if (FAILED(hr))
		return hr;

	acpt->addKey("CustomValues");
	acpt->beginArray();
	for(int i = 0; i < spValues->GetItemCount(); ++i) 
	{
		ks_bstr str;
		spValues->GetItem(i, &str);
		acpt->addString(NULL, str.c_str());
	}
	acpt->endArray();
    AutofilterOperatorSerializeHelper filterOphelper(pAutoFilter, nField);
    filterOphelper.Serialize(acpt);
	return S_OK;
}


void KEtWorkbook::_serialiseSortInfo(IKAutoFilter *pAutoFilter, int nField, ISerialAcceptor* acpt)
{
	ET_SORT_ORDER sortOrder = sot_None;
	bool bSortColor = false;
	pAutoFilter->GetSortInfo(nField, sortOrder, bSortColor);
	acpt->addKey("sortInfo");
	acpt->beginStruct();
	switch(sortOrder)
	{
		case sot_None:
			acpt->addString("sortOrder", __X("none"));
			break;

		case sot_Ascending:
			acpt->addString("sortOrder", __X("ascending"));
			break;

		case sot_Descending:
			acpt->addString("sortOrder", __X("descending"));
			break;
	}
	acpt->addBool("sortColor", bSortColor);
	acpt->endStruct();
}

void KEtWorkbook::_serialiseSortInfo(IKAutoFilter *pAutoFilter, int nField, ISerialAcceptor* acpt, AutoFilterFormatItemsHelper& helper)
{
	ET_SORT_ORDER sortOrder = sot_None;
	bool bSortColor = false;
	pAutoFilter->GetSortInfo(nField, sortOrder, bSortColor);
	acpt->addKey("sortInfo");
	acpt->beginStruct();
	switch(sortOrder)
	{
		case sot_None:
			acpt->addString("sortOrder", __X("none"));
			break;

		case sot_Ascending:
			acpt->addString("sortOrder", __X("ascending"));
			break;

		case sot_Descending:
			acpt->addString("sortOrder", __X("descending"));
			break;
	}
	acpt->addBool("sortColor", bSortColor);
	if (bSortColor)
	{
		switch(helper.getSortBy())
		{
			case SortBy::CELL_FILL:
			{
				KXFMASK mask(XFMASK::_catFills);
				acpt->addKey("cellColor");
				optFill(acpt, NULL, helper.getFill(), mask, helper.getBook());
			}

			break;
			case SortBy::CELL_FONT:
			{
				acpt->addKey("fontColor");
				wo::SerialColour(acpt, NULL, helper.getBook(), *(helper.getFontColor()), GdiAutoTextColor, helper.getFontColor()->getType() == ectAUTO ? TRUE : FALSE);
			}
			break;
			case SortBy::INCON_SET:
			{
				acpt->addKey("iconSet");
				optIconSet(acpt, binary_wo::VarObj(), NULL, helper.getIconSet());
			}
				
			break;
			default:
			break;
		}
	}
	acpt->endStruct();
}

void KEtWorkbook::_SerialiseAutoFilterSearchSortInfo(const binary_wo::VarObj varObj, ISerialAcceptor* acpt)
{
    PCWSTR searchData = NULL;
	PCWSTR searchType = NULL;
    PCWSTR searchMode = NULL;
    VarObj vSc = varObj.get_s("search");
	if (vSc.type() != binary_wo::typeInvalid)
	{
		if (vSc.has("value")) searchData = vSc.field_str("value");
		if (vSc.has("type")) searchType = vSc.field_str("type");
        if (vSc.has("mode")) searchMode = vSc.field_str("mode");
	}
    acpt->addKey("search");
	acpt->beginStruct();
	acpt->addString("value", searchData == NULL ? __X("") : searchData);
	acpt->addString("type", searchType == NULL ? __X("") : searchType);
    acpt->addString("mode", searchMode == NULL ? __X("") : searchMode);
	acpt->endStruct();
    PCWSTR sortType = NULL;
    PCWSTR sortOrder = NULL;
    VarObj vSort = varObj.get_s("sort");
    if (vSort.type() != binary_wo::typeInvalid)
    {
        if (vSort.has("type")) sortType = vSort.field_str("type");
        if (vSort.has("order")) sortOrder = vSort.field_str("order");
    }
    acpt->addKey("sort");
	acpt->beginStruct();
	acpt->addString("type", sortType == NULL ? __X("") : sortType);
	acpt->addString("order", sortOrder == NULL ? __X("") : sortOrder);
	acpt->endStruct();
}


void KEtWorkbook::readSortParam(const binary_wo::VarObj varObj, SortParam& sortParam)
{
	//排序相关参数
	binary_wo::VarObj vSort = varObj.get_s("sort");
	if (vSort.type() != binary_wo::typeInvalid)
	{
		if (vSort.has("type"))
		{
			WebStr typeStr = vSort.field_str("type");//名称/byCount 0 1
			if (xstrcmp(typeStr, __X("byCount")) == 0)
			{
                sortParam.setSortType(BYCOUNT);
			}
		}
		
		if (vSort.has("order")) 
		{
			WebStr sortOrderStr = vSort.field_str("order");//Des, Asc 0 1
			if (xstrcmp(sortOrderStr, __X("desc")) == 0)
			{
                sortParam.setSortOrder(DESC);
			}
		}
		if (vSort.has("trigger")) 
		{
			WebStr sortTypeStr = vSort.field_str("trigger");//manu, auto 0 1
			if (xstrcmp(sortTypeStr, __X("manu")) == 0)
			{
                sortParam.setSortTrigger(MANU);
			}
		}
        if (vSort.has("pinedItem"))
        {
            sortParam.setPinedItems(vSort.field_bool("pinedItem"));
        }
	}
}


HRESULT KEtWorkbook::QueryAutoFilterValues(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj varObj, ISerialAcceptor* acpt)
{
	ASSERT(rg.IsValid());
	ks_stdptr<etoldapi::Range> host = CreateRangeObj(rg);
	ks_stdptr<_Worksheet> spSheet;
	host->get_Worksheet(&spSheet);
	ISheet* pCoreSheet = spSheet->GetSheet();
	IKAutoFilter* pAutoFilter = getAutoFilterInRange(pCoreSheet, rg, filterID);
	if (pAutoFilter == NULL)
		return E_FAIL;

	RANGE filterRg(pCoreSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.Contain(rg))
		return E_FAIL;

	ks_castptr<IEtRevisionContext> ctx = acpt->getContext();
	VS(checkQueryFilterRange(ctx, filterRg, rg));

	int nField = rg.ColFrom() - filterRg.ColFrom();
	ks_stdptr<IAutoFilterValues> spFilterValues;
	ValuesNode* pValuesRoot = NULL;


    SortParam sortParam(BYNAME, ASC, AUTO, false);

	readSortParam(varObj, sortParam);
    if (!sortParam.pinedItems())
    {
        sortParam.setSortTrigger(MANU);
    }
    HRESULT hr = E_FAIL;
	VS(hr = pUser->gainFilterCacheData(pAutoFilter, nField, FALSE, &spFilterValues, &pValuesRoot, sortParam));
    if (FAILED(hr))
    {
        //协作者 “把空行后纳入赛选”，恰好有无权限的区域，但是能够“纳入”成功（线上设计），但是再取数据的时候，就崩溃
        return hr;
    }


    AutoFilterItemsHelper helper(pAutoFilter, spFilterValues, pValuesRoot, varObj, this, nField);
    //排序的时候,需要考虑临时勾选状态
    if (varObj.has("operationValues"))
    {
        binary_wo::VarObj operationValues = varObj.get_s("operationValues");
        helper.SetAutoFilterNodeStatus(operationValues, pValuesRoot);
    }
    bool bResort = true;
    //1. 分页不需要重排
    //2. 清空搜索栏,不需要重排
    if (helper.pageOffset() > 0 || helper.emptySearch())
    {
        bResort = false;
    }
    helper.setResort(bResort);
    helper.setSortParam(sortParam);

    acpt->addKey("fieldData");
	acpt->beginStruct();

	helper.Serialize(acpt);
    _SerialiseAutoFilterSearchSortInfo(varObj, acpt);

	acpt->endStruct();
    _serialiseSortInfo(pAutoFilter, nField, acpt);
    AutofilterOperatorSerializeHelper filterOphelper(pAutoFilter, nField);
    filterOphelper.Serialize(acpt);
	
	return S_OK;
}

HRESULT KEtWorkbook::QueryAutoFilterInverse(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj varObj, ISerialAcceptor* acpt)
{
	ASSERT(rg.IsValid());
	ks_stdptr<etoldapi::Range> host = CreateRangeObj(rg);
	ks_stdptr<_Worksheet> spSheet;
	host->get_Worksheet(&spSheet);
    if (!spSheet)
        return E_FAIL;
	ISheet* pCoreSheet = spSheet->GetSheet();
    if (!pCoreSheet)
        return E_FAIL;
    
	IKAutoFilter* pAutoFilter = getAutoFilterInRange(pCoreSheet, rg, filterID);
	if (pAutoFilter == NULL)
		return E_FAIL;

	RANGE filterRg(pCoreSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.Contain(rg))
		return E_FAIL;

	ks_castptr<IEtRevisionContext> ctx = acpt->getContext();
	VS(checkQueryFilterRange(ctx, filterRg, rg));

	int nField = rg.ColFrom() - filterRg.ColFrom();
    ks_stdptr<IAutoFilterValues> spFilterValues;
    ValuesNode* pValuesRoot = NULL;
    HRESULT hr = E_FAIL;
	VS(hr = pUser->gainFilterCacheData(pAutoFilter, nField, FALSE, &spFilterValues, &pValuesRoot));
    if (FAILED(hr))
    {
        //协作者 “把空行后纳入赛选”，恰好有无权限的区域，但是能够“纳入”成功（线上设计），但是再取数据的时候，就崩溃
        return hr;
    }

    AutoFilterItemsHelper helper(pAutoFilter, spFilterValues, pValuesRoot, varObj, this, nField);
    //如果是反选，需要处理一下前端传过来的临时状态
    if (varObj.has("operationValues"))
    {
        binary_wo::VarObj operationValues = varObj.get_s("operationValues");
        helper.SetAutoFilterNodeStatus(operationValues, pValuesRoot);
    }
    helper.SelectInvertItems(pValuesRoot);
    helper.setResort(false);
    helper.SetInverse();
    acpt->addKey("fieldData");
	acpt->beginStruct();
	helper.Serialize(acpt);
    _SerialiseAutoFilterSearchSortInfo(varObj, acpt);
	acpt->endStruct();
    _serialiseSortInfo(pAutoFilter, nField, acpt);
    AutofilterOperatorSerializeHelper filterOphelper(pAutoFilter, nField);
    filterOphelper.Serialize(acpt);
	return S_OK;
}


HRESULT KEtWorkbook::QueryAutoFilterSetTmpValues(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj varObj, ISerialAcceptor* acpt)
{
	ASSERT(rg.IsValid());
	ks_stdptr<etoldapi::Range> host = CreateRangeObj(rg);
	ks_stdptr<_Worksheet> spSheet;
	host->get_Worksheet(&spSheet);
    if (!spSheet)
        return E_FAIL;
	ISheet* pCoreSheet = spSheet->GetSheet();
    if (!pCoreSheet)
        return E_FAIL;
    
	IKAutoFilter* pAutoFilter = getAutoFilterInRange(pCoreSheet, rg, filterID);
	if (pAutoFilter == NULL)
		return E_FAIL;

	RANGE filterRg(pCoreSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.Contain(rg))
		return E_FAIL;

	ks_castptr<IEtRevisionContext> ctx = acpt->getContext();
	VS(checkQueryFilterRange(ctx, filterRg, rg));

	int nField = rg.ColFrom() - filterRg.ColFrom();
    ks_stdptr<IAutoFilterValues> spFilterValues;
    ValuesNode* pValuesRoot = NULL;
    HRESULT hr = E_FAIL;
	hr = pUser->gainFilterCacheData(pAutoFilter, nField, FALSE, &spFilterValues, &pValuesRoot);
    if (FAILED(hr))
    {
        //协作者 “把空行后纳入赛选”，恰好有无权限的区域，但是能够“纳入”成功（线上设计），但是再取数据的时候，就崩溃
        return hr;
    }

    AutoFilterItemsHelper helper(pAutoFilter, spFilterValues, pValuesRoot, varObj, this, nField);
    
    if (varObj.has("operationValues"))
    {
        binary_wo::VarObj operationValues = varObj.get_s("operationValues");
        helper.SetAutoFilterNodeStatus(operationValues, pValuesRoot);
    }
    
    helper.setResort(false);
    acpt->addKey("fieldData");
	acpt->beginStruct();
	helper.Serialize(acpt);
    _SerialiseAutoFilterSearchSortInfo(varObj, acpt);
	acpt->endStruct();
    _serialiseSortInfo(pAutoFilter, nField, acpt);
    AutofilterOperatorSerializeHelper filterOphelper(pAutoFilter, nField);
    filterOphelper.Serialize(acpt);
	return S_OK;
}

HRESULT KEtWorkbook::QueryAutoFilterCategoryCount(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, const binary_wo::VarObj& varObj, ISerialAcceptor* acpt)
{
	ASSERT(rg.IsValid());

    bool hasLimit = wo::VarObjFieldValidation::expectIntegral(varObj, "categoryLimit");
    if (!hasLimit)
        return E_FAIL;

	ks_stdptr<etoldapi::Range> host = CreateRangeObj(rg);
    if (!host) 
        return E_FAIL;
	ks_stdptr<_Worksheet> spSheet;
	host->get_Worksheet(&spSheet);
    if (!spSheet)
        return E_FAIL;
	ISheet* pCoreSheet = spSheet->GetSheet();
    if (!pCoreSheet)
        return E_FAIL;
    
	IKAutoFilter* pAutoFilter = getAutoFilterInRange(pCoreSheet, rg, filterID);
	if (pAutoFilter == NULL)
		return E_FAIL;

	RANGE filterRg(pCoreSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.Contain(rg))
		return E_FAIL;

	ks_castptr<IEtRevisionContext> ctx = acpt->getContext();
    if (!ctx)
       return E_FAIL;

    HRESULT hr = checkQueryFilterRange(ctx, filterRg, rg);
    if (FAILED(hr))
        return E_FAIL;

	int fieldIndex = rg.ColFrom() - filterRg.ColFrom();

    IKWorksheets * pWorksheets = m_ptrWorkbook->GetWorksheets();
    if (!pWorksheets)
        return E_FAIL;

	if (rg.SheetFrom() >= pWorksheets->GetSheetCount())
		return E_FAIL;

	IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(rg.SheetFrom());
	if (!pWorksheet)
		return E_FAIL;

    ks_stdptr<IKFilterExport> spExporter;
	VS(_applogic_CreateObject(CLSID_KFilterExport, IID_IKFilterExport, (void**)&spExporter));
	spExporter->Init(pWorksheet, fieldIndex, pAutoFilter);
    int nCategoryLimit = varObj.field_int32("categoryLimit");
    WOLOG_INFO << "[filter][KEtWorkbook::QueryAutoFilterCategoryCount] nCategoryLimit:" << nCategoryLimit;
    bool bExceed = false;
    QStringList listData;
	if (FAILED(spExporter->PrepareCategoryFilterData(listData, nCategoryLimit, &bExceed)))
    {
        return E_FAIL;
    }
    
    acpt->addKey("categoryCount");
    acpt->beginStruct();
    acpt->addInt32("categoryLimit", nCategoryLimit);//回写
    acpt->addBool("exceed", bExceed);
    acpt->endStruct();
    
	return S_OK;
}

HRESULT KEtWorkbook::QueryAutoFilterCondition(PCWSTR filterID, const RANGE& rg, ISerialAcceptor* acpt)
{
	ASSERT(rg.IsValid());
	ks_stdptr<etoldapi::Range> host = CreateRangeObj(rg);
	ks_stdptr<_Worksheet> spSheet;
	host->get_Worksheet(&spSheet);
	ISheet* pCoreSheet = spSheet->GetSheet();
	IKAutoFilter* pAutoFilter = getAutoFilterInRange(pCoreSheet, rg, filterID);
	if (pAutoFilter == NULL)
		return E_FAIL;

	RANGE filterRg(pCoreSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.Contain(rg))
		return E_FAIL;

	ks_castptr<IEtRevisionContext> ctx = acpt->getContext();
	HRESULT hr = checkQueryFilterRange(ctx, filterRg, rg);
	if (FAILED(hr))
	{
		AutofilterParamSerialHelper().SerializeEmptyTips(acpt);
		return S_OK;
	}

	int nField = rg.ColFrom() - filterRg.ColFrom();
	ks_stdptr<IBook> spBook;
	pCoreSheet->GetBook(&spBook);
	AutofilterParamSerialHelper param;
	param.InitForTips(nField, pAutoFilter, spBook);
	param.Serialize(acpt);
    AutofilterOperatorSerializeHelper filterOphelper(pAutoFilter, nField);
    filterOphelper.Serialize(acpt);
	return S_OK;
}
 
HRESULT KEtWorkbook::QueryAutoFilterFormats(IKETUserConn* pUser, PCWSTR filterID, const RANGE& rg, ISerialAcceptor* acpt)
{
	ASSERT(rg.IsValid());
	ks_stdptr<etoldapi::Range> host = CreateRangeObj(rg);
	ks_stdptr<_Worksheet> spSheet;
	host->get_Worksheet(&spSheet);
	ISheet* pCoreSheet = spSheet->GetSheet();
	IKAutoFilter* pAutoFilter = getAutoFilterInRange(pCoreSheet, rg, filterID);
	if (pAutoFilter == NULL)
		return E_FAIL;

	RANGE filterRg(pCoreSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.Contain(rg))
		return E_FAIL;

	ks_castptr<IEtRevisionContext> ctx = acpt->getContext();
	VS(checkQueryFilterRange(ctx, filterRg, rg));

	int nField = rg.ColFrom() - filterRg.ColFrom();

	ks_stdptr<IBook> spBook;
	pCoreSheet->GetBook(&spBook);

	acpt->addKey("fieldData");
	acpt->beginStruct();

	ks_stdptr<IAutoFilterFormatItems> spFmtItems;
	pAutoFilter->GetFilterFormatItems(nField, ETStringToolsOpt_None, 0, EOp_None, &spFmtItems);
	AutoFilterFormatItemsHelper helper(spFmtItems, spBook, pAutoFilter, nField);
	helper.Serialize(acpt);

	acpt->endStruct();
    _serialiseSortInfo(pAutoFilter, nField, acpt, helper);
    AutofilterOperatorSerializeHelper filterOphelper(pAutoFilter, nField);
    filterOphelper.Serialize(acpt);
	return S_OK;
}

HRESULT KEtWorkbook::getCellComments(const RANGE& rg, bool bCreate, ICellComments** ppCmt)
{
	ks_stdptr<ISheet> spSheet;
	IBook* pBook = GetCoreWorkbook()->GetBook();
	pBook->GetSheet(rg.SheetFrom(), &spSheet);

	ks_stdptr<IBookOplData> spOplData;
	HRESULT hr = oplGetBookOplData(pBook, &spOplData);
	if(FAILED(hr))
		return E_FAIL;

	ks_stdptr<IKDrawingCanvas> spCommentCanvas;

	if(bCreate)
	{
		spOplData->CreateSheetCommentOplData(spSheet, &spCommentCanvas);
	}
	else
	{
		ks_stdptr<IUnknown> spUnk;
		if (SUCCEEDED(spSheet->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk)))
			spUnk->QueryInterface(IID_IKDrawingCanvas, (void**)&spCommentCanvas);
	}
	if (!spCommentCanvas)
		return E_FAIL;

	ks_stdptr<ICellComments> spCMTs = spCommentCanvas;
	*ppCmt = spCMTs.detach();
	return S_OK;
}

HRESULT KEtWorkbook::getWoComment(const RANGE& rg, bool bCreate, IWoComment** ppCmt)
{
	ks_stdptr<ICellComments> spCMTs;
	this->getCellComments(rg, bCreate, &spCMTs);
	if(spCMTs == NULL)
		return E_FAIL;

	ks_stdptr<ICellComment> spCurCMT;
	CELL c = {};
	c.row = rg.RowFrom();
	c.col = rg.ColFrom();
	spCMTs->GetCellComment(c, &spCurCMT);

	if (NULL == spCurCMT && bCreate)
		spCMTs->Add(c, &spCurCMT);
	
	if (!spCurCMT)
		return E_FAIL;

	return spCurCMT->GetWoComment(ppCmt);
}

void KEtWorkbook::clearInitCache()
{
	if(m_initCache != nullptr)
	{
		delete m_initCache;
		m_initCache = nullptr;
	}
}

KEtWorkbook::InitCache* KEtWorkbook::setupInitCache()
{
	ASSERT(m_initCache == nullptr);
	m_initCache = new InitCache;
	return m_initCache;
}

bool KEtWorkbook::isDelayBroadcast()
{
	using namespace std::chrono;
	system_clock::time_point now = system_clock::now();

	size_t ms = 0;
	system_clock::duration dur = now - m_lastBroadcastTime;
	double curCmdCountUnit = m_cmdCount / ((dur / milliseconds(1)) / 1000.0);
	if (curCmdCountUnit > m_lastCmdCountUnit)
		ms = m_lastDelayTime + 100;
	else
	 	ms = m_lastDelayTime - 100;

	m_lastCmdCountUnit = curCmdCountUnit;

	if (ms < 400)
		ms = 400;
	else if(ms > 2000)
		ms = 2000;
	m_lastDelayTime = ms;

	if (dur < milliseconds(1) * ms)
		return true;

	m_lastBroadcastTime = now;
	m_cmdCount = 0;
	return false; 
}


void KEtWorkbook::_updateRenderView()
{
	ks_castptr<IKEtView> pActView = GetCoreApp()->GetActiveView();
	if (!pActView)
		return;

	AbstractLayerControl* layerCtrl = pActView->GetActiveLayerControl();
	if (layerCtrl)
	{
		util::SlowCollectCallTimeStat callTime(
			"workbook calcDirtyRegion", 
			kCalcDirtyRegionThreshold,
			this,
			__X("calc_dirty_region_book")
		);

		layerCtrl->getVisual()->calcDirtyRegion();
	}
}

void KEtWorkbook::updateRenderShape(IKWorksheet * pWorksheet)
{
	util::SlowCollectCallTimeStat callTime(
		"workbook calcDirtyRegion", 
		kCalcDirtyRegionThreshold,
		this,
		__X("calc_dirty_region_book")
	);
	
	IRenderViews *rdViews = pWorksheet->GetActiveWorksheetView()->GetRenderViews();
	for (int i = 0; i < rdViews->GetRenderViewCount(); ++i) {
		IRenderView * renderView = rdViews->GetRenderViewItem(i);
		if (renderView != nullptr && renderView->GetRootLayerControl() != nullptr) {
			renderView->GetRootLayerControl()->getVisual()->calcDirtyRegion();
		}
	}
}

void KEtWorkbook::BroadcastChanged( binary_wo::BinWriter& ww, const char* connID)
{
	const MsgType msg = msgType_Changed;
	if (isDelayBroadcast())
	{
		if (!m_delayBroadcatData)
			m_delayBroadcatData = new BroadcastData();
	
		m_delayBroadcatData->reset(getMsgTypeName(msg), ww, connID);
	}
	else 
	{
		if (m_delayBroadcatData)
		{
			delete m_delayBroadcatData;
			m_delayBroadcatData = nullptr;
		}

		binary_wo::BinWriter::StreamHolder bt = ww.buildStream();
		WebSlice slice = {bt.get(), ww.writeLength()};
		gs_callback->broadcast(getMsgTypeName(msg), &slice, connID);
	}
}

void KEtWorkbook::SignalNow(MsgType msg, binary_wo::BinWriter& ww, const char* connID)
{
	binary_wo::BinWriter::StreamHolder bt = ww.buildStream();
	WebSlice slice = {bt.get(), ww.writeLength()};
	gs_callback->signal(connID, getMsgTypeName(msg), &slice);
}

void KEtWorkbook::SerialCalcStatus(binary_wo::BinWriter& bw)
{
	switch(m_calcStatus)
	{
		case finish:
			bw.addStringField(__X("finish"), "calcStatus");
			break;
		case suspend:
			bw.addStringField(__X("suspend"), "calcStatus");
			break;
		case calculating:
			bw.addStringField(__X("calculating"), "calcStatus");
			break;
		default:
			bw.addStringField(__X("error"), "calcStatus");
			break;
	}
}

void KEtWorkbook::SerialDiagnosisStatus(binary_wo::BinWriter& bw)
{
	switch (m_calcDiagnosisStatus)
	{
		case CALC_DIAGNOSIS_STATUS::HaventDiagnosed:
		case CALC_DIAGNOSIS_STATUS::HaveDiagnosed:
			bw.addStringField(__X("notInDiagnosis"), "diagnosisStatus");
			break;
		case CALC_DIAGNOSIS_STATUS::Diagnosing:
			bw.addStringField(__X("diagnosing"), "diagnosisStatus");
			break;
		default:
			bw.addStringField(__X("error"), "diagnosisStatus");
			break;
	}
}

void KEtWorkbook::SerialVerStatusInfo(BinWriter& bw, bool invalidateFmlaRes)
{
	KwVersionManager* mgr = getVersionMgr();
	bw.addInt32Field(mgr->getCurTaskVersionID(), gwsTaskVer);
	bw.addInt32Field(mgr->getCurDataVersionID(), gwsDataVer);
	if(invalidateFmlaRes) // 手动计算结果已失效
		bw.addInt32Field(InitVersion - 1, "fmlaResVer");

	bw.addUint32Field(m_importrangeCtx.recalcResVer, "recalcResVer");
}

void KEtWorkbook::NotifyCrossBooksUpdate()
{
	wo::util::CallTimeStat callTime("NotifyCrossBooksUpdate");

	Subscription::Utils utils(this);
	ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();
	if (pSubOp == nullptr)
		return;
	std::vector<PCSTR> vec;
	pSubOp->CollectCbsToBeNotified(vec);

	if (!vec.empty())
	{
		// 跨book引用
		bool bUseBatchNotify = _kso_GetWoEtSettings()->IsBatchNotifyCBRInfo();
		if (bUseBatchNotify)
		{
			if (gs_callback->crossBookNotify)
			{
				BinWriter bw;
				KSerialWrapBinWriter acpt(bw, nullptr);
				pSubOp->SerialiseCbUpdateRangesByGroup(&acpt, "data");
				pSubOp->SerialiseUpdatedFiles(&acpt, "updatedFiles");
				BinWriter::StreamHolder sh = bw.buildStream();
				WebSlice slice = { sh.get(), bw.writeLength() };
				gs_callback->crossBookNotify(WoNotifyUpdateCb, &slice);
			}
		}
		else
		{
			if (gs_callback->notifyFile)
			{
				for (auto it = vec.begin(); it != vec.end(); ++it)
				{
					BinWriter bw;
					KSerialWrapBinWriter acpt(bw, nullptr);
					pSubOp->SerialiseUpdatedFiles(&acpt, "updatedFiles");
					pSubOp->SerialiseUpdateSeq(&acpt, "updateSeq");
					pSubOp->SerialiseCbUpdateRanges(&acpt, "ranges", *it);
					BinWriter::StreamHolder sh = bw.buildStream();
					WebSlice slice = { sh.get(), bw.writeLength() };
					gs_callback->notifyFile(WoNotifyUpdateCb, *it, &slice);
					WOLOG_INFO << "[subscription] WoNotifyUpdateCb, notifyFile fileid: " << *it;
				}
			}
		}
	}

	{
		// 获取task，根据不同的task名称分发更新数据
		std::vector<ks_string> vecTask;
		pSubOp->CollectCbsTaskOffline(vecTask);
		if(gs_callback->signalSubscriptionCenter)
		{
			for(auto itT = vecTask.begin(); itT != vecTask.end(); itT++)
			{
				// 获取当前task下的fileID,根据filid发送dirtyRgName
				std::vector<PCSTR> vecFildId;
				pSubOp->CollectCbsToBeNotifiedOffline(*itT, vecFildId);
				for(auto itF = vecFildId.begin(); itF != vecFildId.end(); itF++)
				{
					BinWriter bw;
					std::unordered_set<HANDLE> dirtyRgs, dirtyNames;
					KSerialWrapBinWriter acpt(bw, nullptr);
					pSubOp->SerialiseUpdatedFiles(&acpt, "updatedFiles");
					pSubOp->SerialiseUpdateSeq(&acpt, "updateSeq");
					pSubOp->SerialiseUpdateInfo(&acpt, "updatedInfo", *itT, *itF);	// 执行命令后，脏数据
					BinWriter::StreamHolder sh = bw.buildStream();
					WebSlice slice = { sh.get(), bw.writeLength() };
					gs_callback->signalSubscriptionCenter(WoNotifyUpdateCb, (*itT).c_str(), QString(*itF).toUtf8(), &slice);

					WOLOG_INFO << "[subscription] signalSubscriptionCenter, WoNotifyUpdateCb, fileid: " << *itF;
				}
			}
		}
	}
}

void KEtWorkbook::NotifyCustomListUpdate()
{
    wo::util::CallTimeStat callTime("NotifyCustomListUpdate");

	if (m_pDvCustomListCache->GetDirtyRangeCount() > 0)
	{
		binary_wo::BinWriter bw;
		KSerialWrapBinWriter wbw(bw, NULL);
		m_pDvCustomListCache->SerialDirtyRanges(&wbw);
		m_pDvCustomListCache->ClearDirtys();
		BroadcastChanged(bw, NULL);
	}
}

void KEtWorkbook::DoIdle(DWORD& calcDetail, bool* pResumeCalcHappened)
{
	// 尝试恢复计算
	resumeCalculate(calcDetail, pResumeCalcHappened);
}

void KEtWorkbook::broadcastDelayData()
{
	if (!isDelayBroadcast() && m_delayBroadcatData)
	{
		using namespace std::chrono;
		m_lastBroadcastTime = system_clock::now();
		m_delayBroadcatData->broadcast();
		delete m_delayBroadcatData;
		m_delayBroadcatData = nullptr;
	}
}

KEtWorkbook::CalcStatus KEtWorkbook::getCalcStatus()
{
	return m_calcStatus;
}

// 只用于更新当前版本状态用，用于确认版本时用，版本状态更新后可以从里面直接拿。
void KEtWorkbook::updateCalcStatus(bool isCurVerReCalc, KEtRevisionContext* ctx)
{
	wo::util::SlowCallTimeStat callTime("updateCalcStatus", 100);
	
	_setCalcStatus(_updateCalcStatus(isCurVerReCalc, ctx));
	if (m_calcStatus == calculating)
	{
		ETCalculation calcMode = etCalculationAutomatic;
		m_ptrApp->get_Calculation(&calcMode);
		if(calcMode == etCalculationAutomatic)
			ctx->collectInfo("behaviour_calc_break");
	}

	updateCalcDiagnosisStatus();
}

void KEtWorkbook::updateCalcDiagnosisStatus()
{
	IBookStake* pBookStake = m_book->GetWoStake();
	m_calcDiagnosisStatus = pBookStake->GetCalcDiagnosisInfo().status;
}

KEtWorkbook::CalcStatus KEtWorkbook::_updateCalcStatus(bool isCurVerReCalc, KEtRevisionContext* ctx)
{
	bool isDirty = m_ptrApp->NeedResumeCalculate();
	ETCalculation calcMode = etCalculationAutomatic;
	m_ptrApp->get_Calculation(&calcMode);

	if (calcMode == etCalculationAutomatic || isCurVerReCalc)
		return isDirty ? calculating : finish;

	ASSERT(!isCurVerReCalc && calcMode != etCalculationAutomatic);
	if (m_calcStatus == suspend)
		return suspend; // 以前就是挂起的， 当前肯定也是挂起的
	
	if(ctx->isUndoRedo())
		return suspend; // 手动计算，undo/redo 时重新执行命令

	// 从自动模式切换成手动模式时 isDirty是false,为PC的逻辑，会导致状态是计算中，并且不需要重算的情况发生
	if (!isDirty && m_calcStatus == calculating)
	{
		return suspend;
	}

	// undo可能不会进这里，因为undo最后一个版本不用先生成新的task并确认。
	// 这里的undoTag 确实是当前执行的undoTag,因为命令已经执行完了，只是还没提交而已
	// 当前修改是否影响了公式计算结果，这个判断可能比较耗时
	// 如果不影响结果，就保老的状态不变。
	ASSERT(isDirty ? m_calcStatus == calculating : m_calcStatus == finish);
	return m_book->GetWoStake()->isCurrentUndoTagNeedCalc() ? suspend : m_calcStatus;
}

void KEtWorkbook::_updateCalcStatusSimple()
{
	ETCalculation calcMode = etCalculationAutomatic;
	m_ptrApp->get_Calculation(&calcMode);
	if (calcMode != etCalculationAutomatic)
	{
		_setCalcStatus(suspend);
	}
	else
	{
		_setCalcStatus(m_ptrApp->NeedResumeCalculate() ? calculating : finish);
	}
}

void KEtWorkbook::_collectDirtySparklineRefAreas()
{
	KEtVersionManager* pEtMgr = static_cast<KEtVersionManager*>(m_versionMgr);
	KSparklineRefAreaVersion* pVersion = pEtMgr->createSparklineRefAreaVersion();
	if (NULL == pVersion)
		return;

	// 闭环。收集完毕后，才清空appcore层的迷你图脏标记。
	_clearDirtySparklineParts(pVersion);

	// 减少空版本的生成
	if (pVersion->HasDirtyRefAreas())
		pEtMgr->confirmSparklineRefAreaVersion(pVersion);
	else
		delete pVersion;
}

void KEtWorkbook::resumeCalculate(DWORD& calcDetail, bool* pResumeCalcHappened)
{
	ETCalculation calcMode = etCalculationAutomatic;
	m_ptrApp->get_Calculation(&calcMode);
	if(calcMode != etCalculationAutomatic && m_calcStatus == suspend || !m_ptrApp->NeedResumeCalculate())
		return;

	util::CmdTimeStat cmdTimeStat(this, nullptr);
	cmdTimeStat.start(__X("resumeCalculate"), false);

	TrackerScope trackerScope(m_book);
	util::DbCdcScope cdcScope(this);
	m_ptrApp->ResumeCalculate(&calcDetail);
	if (pResumeCalcHappened)
		*pResumeCalcHappened = true;

	if (!m_ptrApp->NeedResumeCalculate())
	{
		m_dbtCalcCellNeedAddDirty = true;
		IBookStake* pBookStake = m_book->GetWoStake();
		pBookStake->getCacheBuildingOnCell()->invalidate();

		_setCalcStatus(finish);
		binary_wo::BinWriter ww;
		SerialVerStatusInfo(ww);
		BroadcastChanged(ww, NULL);
		onDataChanged(nullptr);
		
		NotifyCrossBooksUpdate();
		IKWorksheets* pSheets = GetCoreWorkbook()->GetWorksheets();
		util::CollectAndSendFormulaResChangeByWebhook(m_book, pSheets);
	}
	updateCalcDiagnosisStatus();
}

bool KEtWorkbook::DbtCalcCellsNeedAddDirty() const
{
	return m_dbtCalcCellNeedAddDirty;
}

void KEtWorkbook::SetDbtCalcCellsNeednotAddDirty()
{
	m_dbtCalcCellNeedAddDirty = false;
}

EtQueryExecutor* KEtWorkbook::GetQueryExecutor()
{
	if (!m_queryExecutor)
		m_queryExecutor = new EtQueryExecutor(this);
	return m_queryExecutor;
}

std::shared_ptr<DbAuthQueryCoHistories::HandlerFactory> KEtWorkbook::GetDbAuthQueryCoHistoriesHandlerFactory()
{
	if (!m_dbAuthQueryCoHistoiresHandlerFactory)
	{
		m_dbAuthQueryCoHistoiresHandlerFactory = std::make_shared<DbAuthQueryCoHistories::HandlerFactory>();
		m_dbAuthQueryCoHistoiresHandlerFactory->InitDefault(this);
	}
	return m_dbAuthQueryCoHistoiresHandlerFactory;
}

DocMemoStat* KEtWorkbook::GetMemoStat()
{
	return m_memoStat.get();
}

void KEtWorkbook::BroadcastData::reset(WebName name, binary_wo::BinWriter& ww, const char* connID)
{
	m_name = name;
	if (connID)
		m_connID = connID;
	else
		m_connID.clear();

	if(m_slice.data)
		free(m_slice.data);

	m_slice.data = ww.buildStream().detach();
	m_slice.size = ww.writeLength();
}

void KEtWorkbook::BroadcastData::broadcast()
{
	gs_callback->broadcast(m_name, &m_slice, m_connID.empty() ? NULL : m_connID.c_str());
}

IWebSlice* KEtWorkbook::InitCache::getSignalSlice()
{
	return m_spSignalSlice;
}

void KEtWorkbook::InitCache::makeSignalSlice(binary_wo::BinWriter& bw, IKETUserConn* pUserConn)
{
	ASSERT(m_spSignalSlice == nullptr);
	m_spSignalSlice.attach(KS_NEW(KWebSlice));
	m_spSignalSlice->Attach(bw.buildStream().detach(), bw.writeLength());

	std::unordered_set<WebID>* pSet = pUserConn->getSerialObjsSet();
	m_serialObj.reserve(pSet->size());
	for (auto it = pSet->begin(); it != pSet->end(); ++it)
	{
		m_serialObj.push_back(*it);
	}
	IBlockPointsCache* pBP = pUserConn->getBlockPointCache();
	for(WebID id = pBP->getFirstCompleteInstallSheet(); id != 0; id = pBP->getNextCompleteInstallSheet())
	{
		m_sheetsCompleteInit.insert(id);
	}
}

void KEtWorkbook::InitCache::makeBlockPoint(std::vector<BlockPoint>& vbp)
{
	m_vbp.reserve(vbp.size());
	std::copy(vbp.begin(), vbp.end(), std::back_inserter(m_vbp));
}

void KEtWorkbook::InitCache::resetConnByCache(IKETUserConn* pConn)
{
	wo::IBlockPointsCache* bpc = pConn->getBlockPointCache();
	bpc->reset(kfc::tools::vtoa(m_vbp), m_vbp.size());
	
	std::unordered_set<WebID>* pSet = pConn->getSerialObjsSet();
	std::vector<WebID>* pVec = pConn->getSyncObjs();
	
	ASSERT(pSet->empty());
	ASSERT(pVec->empty());
	for (auto it = m_serialObj.begin(); it != m_serialObj.end(); ++it)
	{
		pSet->insert(*it);
		pVec->emplace_back(*it);
	}
	ASSERT(pSet->size() == pVec->size());

	for (auto it = m_sheetsCompleteInit.begin(); it != m_sheetsCompleteInit.end(); ++it)
		bpc->addCompleteInstallSheet(*it);
}

bool KEtWorkbook::InitCache::hasSheet(WebID objSheet)
{
	return m_sheetsCompleteInit.find(objSheet) != m_sheetsCompleteInit.end();
}

void KEtWorkbook::readThemes()
{
	for (auto it = m_themes.begin(); it != m_themes.end(); ++it)
		(*it)->deleteThis();
	m_themes.clear();

	ks_stdptr<IDefaultThemeSet> spDefThemeSet;
	m_ptrApp->GetDefaultThemeSet(&spDefThemeSet);
	m_themes.push_back(spDefThemeSet->GetDefaultTheme(THEME_VERSION_2013)->Clone());
	
	QStringList colorSchemeDirs;
	colorSchemeDirs << m_consoleApp->applicationDirPath() + "/document theme/";

	ks_stdptr<IKMediaManage> spMediaManage;
	_dg_GetMediaManage(&spMediaManage);
	ASSERT(spMediaManage);

	foreach(auto dir, colorSchemeDirs)
	{
		QStringList filelist = QDir(dir).entryList(QDir::Files);
		foreach(auto filename, filelist)
		{
			QString filePath = dir + filename;
			ITheme* pTheme = reinterpret_cast<ITheme *>(drawing::SimpleThemeParse::readTheme(krt::utf16(filePath), spMediaManage));
			if (pTheme)
				m_themes.push_back(pTheme);
		}
	}
}


int KEtWorkbook::GetThemesCount()
{
	return m_themes.size();
}

ITheme* KEtWorkbook::GetTheme(size_t idx)
{
	ASSERT(idx >= 0  && idx < m_themes.size());
	return m_themes[idx];
}

// 审阅模式下 记录命令操作的选区 用于回放的时候更新选区
// 默认情况下先从param里面拿sheet/row/col|From/To
// 对于特殊命令 涉及选区调整的 在et_task_class里面再手动调整
void KEtWorkbook::setLastTaskSelection(KwTask *pTask)
{
	KwCommand *pCmd = pTask->getSrcCommand();
	binary_wo::VarObj param = pCmd->cast().get("param");
	setLastTaskSelection(param);
}

LastTaskSelection* KEtWorkbook::getLastTaskSelection()
{
	return &m_lastTaskSelection;
}

void KEtWorkbook::setLastTaskSheet(INT32 sheetIdx)
{
	m_lastTaskSelection.first = sheetIdx;
}

void KEtWorkbook::setLastTaskSelection(binary_wo::VarObj param)
{
	if (param.has("sheetIdx"))
	{
		INT32 sheetIdx = param.field_int32("sheetIdx");
		if (sheetIdx >= 0)
			setLastTaskSheet(sheetIdx);
	}
	std::vector<RANGE> rgVec;
	if (param.has("rgs"))
	{
		binary_wo::VarObj rgs = param.get_s("rgs");
		ASSERT(rgs.type() == binary_wo::typeArray);
		for (int32 i = 0; i < rgs.arrayLength_s(); ++i)
			setLastTaskSelectionRanges(rgs.at_s(i), rgVec);
	}
	else
		setLastTaskSelectionRanges(param, rgVec);

	if (rgVec.size() > 0)
	{
		m_lastTaskSelection.second.clear();
		m_lastTaskSelection.second.insert(m_lastTaskSelection.second.end(), rgVec.begin(), rgVec.end());
	}
}

void KEtWorkbook::setLastTaskSelectionRanges(binary_wo::VarObj param, std::vector<RANGE> &rgVec)
{
	bool has_set = false;
	if (param.has("rowFrom") && param.has("rowTo") && param.has("colFrom") && param.has("colTo"))
	{
		INT32 rowFrom = param.field_int32("rowFrom");
		INT32 rowTo = param.field_int32("rowTo");
		INT32 colFrom = param.field_int32("colFrom");
		INT32 colTo = param.field_int32("colTo");
		
		RANGE rg(m_ptrWorkbook->GetBook()->GetBMP());
		if (rowFrom >= 0 && rowTo >= rowFrom)
		{
			rg.SetRowFromTo(rowFrom, rowTo);
			has_set = true;
		}
		if (colFrom >= 0 && colTo >= colFrom)
		{
			rg.SetColFromTo(colFrom, colTo);
			has_set = true;
		}
		if (has_set)
		{
			rg.SetSheetFromTo(m_lastTaskSelection.first);
			rgVec.push_back(rg);
		}
	}
}

void KEtWorkbook::resetLastTaskSelection()
{
	initLastTaskSelection();
}

IWebIdSet* KEtWorkbook::getConnSerialObjsSet(IRevisionContext* ctx)
{
    ks_castptr<IKETUserConn> uc = ctx->getUser();
    m_idset.setIdSet(uc->getSerialObjsSet());
    m_idset.setSyncObjs(uc->getSyncObjs());
    m_idset.setRemoveObjs(uc->getRemoveObjs());
    return &m_idset;
}

void KEtWorkbook::setStaleNotify(IStaleNotify* pNotify)
{
	ks_castptr<WoEtSetting> pWoSetting = _kso_GetWoEtSettings();
	if (pWoSetting->isEnableStaleNotify())
	{
		base_type::setStaleNotify(pNotify);
	}
}

void KEtWorkbook::notifyStale(const WebID objId)
{
	wo::IEtRevisionContext* ctx = _etcore_GetEtRevisionContext();
	if (ctx && (ctx->state() != CtxState::Exec && ctx->state() != CtxState::ExecDirect)) {
		// 只能在exec/execDirect. 如果是query会出问题.
		ASSERT(FALSE);
		WOLOG_ERROR << "[onStale] state: " << (int)ctx->state();
		m_staleOnNotExec = true;
		return;
	}
	//WOLOG_INFO << "[onStale] stale id: " << objId;
	m_staleObjs.insert(objId);
}

void KEtWorkbook::serializeSheets(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor* acpt)
{
	if (!args.has("querySheets"))
		return;

	binary_wo::VarObj varSheets = args.get_s("querySheets");
	int32 len = varSheets.arrayLength_s();
	if(len <= 0)
		return;

	ks_castptr<IKETUserConn> uc = ctx->getUser();
	wo::IBlockPointsCache* bpc = uc->getBlockPointCache();

	acpt->addKey("delaySheets");
	acpt->beginArray();

	int shtCnt = 0;
	for (int32 i = 0; i < len; ++i)
	{
		WebID objSheetId = varSheets.at(i).value_web_id();
		bpc->addCompleteInstallSheet(objSheetId);

		IDX iSheet = ctx->getSheetIndex(objSheetId);
		if (iSheet < 0)
			continue;

		++shtCnt;
		acpt->beginStruct();
		IWorksheetObj* pObjSheet = ctx->getSheetMain(iSheet);
		acpt->addInt16("iSheet", iSheet);
		acpt->addKey("data");
		pObjSheet->serialDelayContent(acpt);
		acpt->endStruct();
	}

	acpt->endArray();

	bool canUndoToInitVersion = true;
	int dataVerID = m_versionMgr->getCurDataVersionID();
	for (WebInt i =  m_versionMgr->getTaskVersionCount() - 1, endIdx = uc->getInitTaskIndex() + 1 ; i >= endIdx; --i)
	{
		KwVersion* version = m_versionMgr->getTaskVersion(i);
		if(version == nullptr)
			break;
		if(version->getUserInnerID() != uc->innerID() && version->isUndoDependOthers())
		{
			canUndoToInitVersion = false;
			break;
		}
	}
	if (!canUndoToInitVersion)
		uc->setInitIndex(dataVerID, m_versionMgr->getCurTaskVersionID());
}

void KEtWorkbook::serializeXfs(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor* acpt)
{
	if (!args.has("queryXfs"))
		return;

	binary_wo::VarObj varXfs = args.get_s("queryXfs");
	int32 len = varXfs.arrayLength_s();
	if (len <= 0)
		return;
	
	m_coreMeric.addU32Metrics(KU32MetricItem::kSerialXfsCnt, len);
	
	std::vector<size_t> ixfVec;
	ixfVec.reserve(len);
	for (int32 i = 0; i < len; ++i)
		ixfVec.push_back(static_cast<size_t>(varXfs.at(i).value_uint32()));

	acpt->addKey("delayXfData");
	IDelayObjXFs* pDelayObjXfs = ctx->getBook()->GetWoStake()->getDelayObjXfs();
	pDelayObjXfs->serialDelayContent(acpt, ixfVec.data(), ixfVec.size());
}

void KEtWorkbook::serializeVersionsModifyRanges(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor* acpt)
{
	if (!args.has("queryModifyRange"))
		return;

	WebStr versionTag = args.field_str("queryModifyRange");
	std::vector<KwVersion*> verVec = getVersionTagLastDataVersion(versionTag, -1, true, false);
	IEtAiWatcher* pAiWatcher = static_cast<IEtAiWatcher*>(m_ptrApp->GetEtAiWatcher(m_ptrWorkbook));
	for (auto ver : verVec)
	{
		ver->getTransact()->review(pAiWatcher);
	}
	auto modifyRanges = pAiWatcher->GetModifyRanges();
	if (modifyRanges.empty())
		return;
	MergeRangeHelper helper(modifyRanges);
	helper.executeMerge();
	helper.serialize("modifyRanges", acpt);
}

void KEtWorkbook::serializeCmtContainerBL(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor* acpt)
{
	if (!args.has("commentContainer"))
		return;
	
	size_t oldSz = acpt->getNewObjectCount();
	IBookStake* pBookStake = m_book->GetWoStake();
	ICommentContainerBL* pICmtContainerBL = pBookStake->GetCommentContainerBL();
	pICmtContainerBL->serializeInitData(acpt);
	m_coreMeric.addU32Metrics(KU32MetricItem::kSerialCmtsNewsObjCnt, acpt->getNewObjectCount() - oldSz);
}

void KEtWorkbook::serializeUserOrganizeInfo(KEtRevisionContext* ctx, const binary_wo::VarObj args, ISerialAcceptor* acpt)
{
    if (args.has("resetUserOrganizeInfo") && args.field_bool("resetUserOrganizeInfo"))
    {
        ResetCurrentUserOrganizeInfo();
    }
    HRESULT hr = FetchCurrentUserOrganizeInfo();

    if (!args.has("serializeUserOrganizeInfo") || !args.field_bool("serializeUserOrganizeInfo"))
        return;

    acpt->addKey("userOrganizeInfo");
    acpt->beginStruct();
    acpt->addBool("result", hr == S_OK);
	m_pUserOrganizesCache->SerialContent(acpt);    
    acpt->endStruct();
}

HRESULT KEtWorkbook::FetchCurrentUserOrganizeInfo()
{
    return m_pUserOrganizesCache->FetchCurrentUserOrganizeInfo();
}

HRESULT KEtWorkbook::ResetCurrentUserOrganizeInfo()
{
    return m_pUserOrganizesCache->ResetCurrentUserOrganizeInfo();
}

bool KEtWorkbook::RefetchAllUserOrganizes()
{
    return m_pUserOrganizesCache->FetchAll();
}

void KEtWorkbook::DownloadObjectDone(const WebDownloadArg* pDownloadArg, int code, const char *path)
{
	SImageDownload *pImageDownload = NULL;
	if(pDownloadArg->tag == WebDownloadByUrl)
	{
		auto it = m_mapDownloadImage.find(pDownloadArg->pObjectLocator);
		if (it == m_mapDownloadImage.end())
			return;
		pImageDownload = it->second;
		m_mapDownloadImage.erase(it);
	}
	else if(pDownloadArg->tag == WebDownloadByAttachmentId)
	{
		auto it = m_mapDownloadAttachmentImage.find(pDownloadArg->pObjectLocator);
		if (it == m_mapDownloadAttachmentImage.end())
			return;
		pImageDownload = it->second;
		m_mapDownloadAttachmentImage.erase(it);
	}
	else
	{
		return;
	}

	ks_stdptr<IStream> spStream = NULL;
	ULONG cbSize = 0;
	if (code == WO_OK)
	{
		QString strFileName = path;
		HRESULT hr = _XCreateStreamOnFile(
			krt::utf16(strFileName),
			STGM_READ | STGM_SHARE_DENY_NONE,
			&spStream);
		ASSERT(SUCCEEDED(hr));
		if (FAILED(hr) || !spStream)
			return;

		STATSTG stg;
		ULISet32(stg.cbSize, 0);
		spStream->Stat(&stg, STATFLAG_NONAME);
		ASSERT(stg.cbSize.HighPart == 0);
		cbSize = stg.cbSize.LowPart;
	}

	for (int i = 0; i < pImageDownload->listShape.size(); i++)
	{
		drawing::AbstractShape* shape = pImageDownload->listShape[i];
		IKBlipAtom* pBlipAtom = shape->picID();
		if (code == WO_OK)
		{
			HGBL hImg = NULL;
			ULARGE_INTEGER offset;
			ULISet32(offset, 0);
			HRESULT hr = CreateHGblFromStream(&hImg, spStream, offset, cbSize);
			ASSERT(SUCCEEDED(hr));

			kso_md4 md4 = { 0 };
			hr = pBlipAtom->SetHGlobal(md4, hImg);
			ASSERT(SUCCEEDED(hr));
		}
		pBlipAtom->SetUsage(code == WO_OK ? ksoblipUsageDefault : ksoblipUsageUrlDownloadFail);

		binary_wo::BinWriter bw;
		bw.addAbsObjIDField(shape->objId(), "objShape");
		bw.addInt32Field(code, "errCode");
		bw.addAbsObjIDField(pImageDownload->listSheetObj[i], "objSheetOrBook");
		SignalNow(msgType_downloadImageDone, bw, pImageDownload->listConid[i].c_str());
	}

	delete pImageDownload;
	
}

INT32 KEtWorkbook::getEafResVersion() const
{
    IBookStake *pBookStake = m_book->GetWoStake();
	return pBookStake->getEafResVersion();
}

bool KEtWorkbook::hasCalculatingEaf() const
{
    IBookStake *pBookStake = m_book->GetWoStake();
	return alg::BOOL2bool(pBookStake->hasCalculatingEaf());
}

PCWSTR KEtWorkbook::getFileId()
{
	return m_fileId.c_str();
}

bool KEtWorkbook::isBookProtectedWithHiddenProperty()
{
	IKWorksheets *wss = GetCoreWorkbook()->GetWorksheets();
	int wsCount = wss->GetSheetCount();
	for (int i = 0; i < wsCount; ++i)
	{
		IKWorksheet *ws = wss->GetSheetItem(i);
		if (ws) 
		{
			ks_castptr<_Worksheet> pApiWs = ws;
			ISheetProtection* pSheetProtection = pApiWs->GetProtection();
			if (pSheetProtection && pSheetProtection->IsProtected() && pSheetProtection->HasHiddenPropety()) {
				return true;
			}
		}
	}

    return false;
}

bool KEtWorkbook::isRangeIncludeProtectedCols(ISheet* pSheet, const RANGE& rg)
{
	et_sptr<ISheetEnum> spEnum;
	pSheet->CreateEnum(&spEnum);
	
	COL from = rg.ColFrom();
	COL to = rg.ColTo();
	while (from <= to)
	{
		BOOL isProtect = FALSE;
		INT cnt = spEnum->GetColProtect(from, &isProtect);
		if (isProtect)
			return true;

		if (cnt <= 0)
			break;

		from += cnt;
	}

	return false;

}

void KEtWorkbook::clearCellImgHiddenCache()
{
	m_hiddenCellImgSetCahce.clear();
	m_hasHiddenCellImgSetCache = false;
}

void KEtWorkbook::ensureCellImgHiddenCache(IEtRevisionContext* ctx)
{
	if (m_hasHiddenCellImgSetCache)
	{
		return;
	}

    IBookStake *pBookStake = m_book->GetWoStake();
    std::set<ks_wstring> visibleSet;
    std::set<ks_wstring> hiddenSet;
    pBookStake->EnumCellImg([this, ctx, &visibleSet, &hiddenSet] (const ES_POS &pos, ITokenVectorPersist *, LPCWSTR name) mutable -> bool {
        if (name && visibleSet.find(name) == visibleSet.end())
        {
            RANGE rg(m_book->GetBMP());
            rg.SetSheetFromTo(pos.nSheet, pos.nSheet);
            rg.SetRowFromTo(pos.nRow, pos.nRow);
            rg.SetColFromTo(pos.nCol, pos.nCol);
			if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
			{
				hiddenSet.insert(name);
			}
			else
			{
				hiddenSet.erase(name);
				visibleSet.insert(name);
			}
        }
		return false;
    });

	m_hiddenCellImgSetCahce = std::move(hiddenSet);
	m_hasHiddenCellImgSetCache = true;
}

bool KEtWorkbook::checkCellImgProtection(const drawing::AbstractShape* shape, IEtRevisionContext* ctx)
{
    if (shape && ctx->getProtectionCtx()->isBookHasHidden() && shape->getModelType() == ET_MODEL_CELLIMAGESHAPE)
    {
		ensureCellImgHiddenCache(ctx);
		const ks_wstring &name = shape->getName();
		if (m_hiddenCellImgSetCahce.find(name) != m_hiddenCellImgSetCahce.end())
		{
			return false;
		}
    }

	return true;
}

void KEtWorkbook::collectVersionUndoTag(
	IRevisionContext* ctx, std::vector<const UNDO_TAG*>& vec, bool* bHasRecalculate)
{
	KwVersionManager* mgr = getVersionMgr();
	KwVersion* curVersion = mgr->getCurDataVersion();
	WebInt curTaskVersion = curVersion ? curVersion->getTaskIndex() : InitVersion;

	WebInt ee = mgr->getTaskVersionCount();
	WebInt bb = std::min(curTaskVersion, ctx->getBaseTaskVersion()) + 1;
	ASSERT(0 <= bb);
	
	for (; bb < ee; ++bb)
	{
		KEtVersion* version = static_cast<KEtVersion*>(mgr->getTaskVersion(bb));
		if (version == nullptr || !version->isValidate() || version->isObsolete())
			continue;

		if(version->getHasRecalculate())
			(*bHasRecalculate) = true;

		const UNDO_TAG* pTag = version->getUndoTag();
		if (pTag)
			vec.push_back(pTag);
	}
}

void KEtWorkbook::_updateCalcStatusOnSave()
{
	if (m_ptrApp->NeedResumeCalculate())
		return;

	ETCalculation calcMode = etCalculationAutomatic;
	m_ptrApp->get_Calculation(&calcMode);
	bool invalidateFmlaRes = calcMode != etCalculationAutomatic && m_ptrApp->GetAppSettings()->GetCalculateBeforeSave();
	if(calcMode == etCalculationAutomatic || invalidateFmlaRes)
	{
		_setCalcStatus(finish);

		binary_wo::BinWriter ww;
		SerialVerStatusInfo(ww, invalidateFmlaRes);
		BroadcastChanged(ww, NULL);
	}
}

void KEtWorkbook::_setCalcStatus(CalcStatus calcStatus)
{
	m_calcStatus = calcStatus;

	// 每次计算完毕，产生迷你图引用区域版本，用于存储计算过程产生的迷你图脏标记
	if (m_calcStatus == finish)
	{
		_collectDirtySparklineRefAreas();
	}
}

void KEtWorkbook::_recoredFileSizeChange(PCWSTR savePath)
{
	QFileInfo fileInfo(QString::fromUtf16(savePath));
	m_lastSaveInfo.bSaved = true;
	m_lastSaveInfo.size = fileInfo.size();

	if (gs_callback->broadcast) 
	{
		BinWriter bw;
		bw.addKey("fileSize");
		bw.addFloat64(m_lastSaveInfo.size);
		BinWriter::StreamHolder bt = bw.buildStream();
		WebSlice slice = {bt.get(), bw.writeLength()};
		gs_callback->broadcast(getMsgTypeName(msgType_SaveInfo), &slice, nullptr);
	}
}

STDIMP_(FILEFORMAT) KEtWorkbook::GetFileFormat()
{
	return m_ptrWorkbook->GetFileFormat();
}

HRESULT KEtWorkbook::Save(PCWSTR savePath, int32* taskVer)
{
	UINT64 memoBefore = 0, memoAfter = 0;

	HRESULT hr = S_OK;
	if (m_ptrWorkbook.get() != nullptr) {
		m_ptrWorkbook->GetBook()->GetWoStake()->CreateCellCommentByWoComment();
		DocMemoStat::MarkScope ms(m_memoStat.get(), __X("save"), false, VarObj());
		if (savePath == nullptr) {
			hr = m_ptrWorkbook->Save();
		} else {
			KComVariant var;
			var.AssignString(savePath);
			hr = m_ptrWorkbook->SaveCopyAs(var, 0);
		}
	}

	if (SUCCEEDED(hr))
	{
		if (taskVer != nullptr)
		{
			KwVersionManager* verMgr = getVersionMgr();
			if (verMgr->getCurDataVersionID() == InitVersion)
			{
				(*taskVer) = InitVersion;
			}
			else
			{
				(*taskVer) = verMgr->getCurDataVersion()->getTaskIndex();
			}
		}
		_updateCalcStatusOnSave();
	}
	return hr;
}

HRESULT	KEtWorkbook::SaveAsLiteVersion(PCWSTR savePath, int32* taskVer, KComVariant saveAsLiteVersionParam)
{
	WOLOG_INFO << "[file_save_As_liteVersion][KEtWorkbook::SaveAsLiteVersion] start!!!";
	//实现方式基本同该文件的 KEtWorkbook::Save

	HRESULT hr = S_OK;
	if (m_ptrWorkbook.get() != nullptr) {
		DocMemoStat::MarkScope ms(m_memoStat.get(), __X("save"), false, VarObj());
		if (savePath == nullptr) {
			hr = m_ptrWorkbook->Save();
		} else {
			KComVariant var;
			var.AssignString(savePath);
			//Todo:调用轻量化存盘的方法。
			hr = m_ptrWorkbook->SaveCopyAsLiteVersion(var, 0, saveAsLiteVersionParam);
			WOLOG_INFO << "[file_save_As_liteVersion][KEtWorkbook::SaveAsLiteVersion] result: " << hr;
		}
	}

	if (FAILED(hr)){
		ks_wstring wstrErr;
		wstrErr.Format(__X("[file_save_As_liteVersion][KEtWorkbook::SaveAsLiteVersion] fail! file: %s, error: %d"), savePath, hr);
		ks_string strErr(QString::fromUtf16(wstrErr.c_str()).toLocal8Bit());
		WO_LOG_X(getLogger(), WO_LOG_ERROR, strErr.c_str());
	}
	else
	{
		if (taskVer != nullptr)
		{
			KwVersionManager* verMgr = getVersionMgr();
			if (verMgr->getCurDataVersionID() == InitVersion)
			{
				(*taskVer) = InitVersion;
			}
			else
			{
				(*taskVer) = verMgr->getCurDataVersion()->getTaskIndex();
			}
		}
		_updateCalcStatusOnSave();
	}
	return hr;
}

static HRESULT ETFileFormatToXlFileFormat(FILEFORMAT etFmt, XlFileFormat* pXlFmt)
{
	VERIFY_IN_POINTER(pXlFmt);
	switch (etFmt)
	{
	case ffXLS:		*pXlFmt = xlExcel8;  break;
	case ffASC:		*pXlFmt = xlCurrentPlatformText; break;
	case ffCSV:		*pXlFmt = xlCSV;	break;
	case ffDBF:		*pXlFmt = xlDBF4;	break;
	case ffXLT:		*pXlFmt = xlTemplate; break;
	case ffUNI:		*pXlFmt = xlUnicodeText; break;
	case ffHTM:		*pXlFmt = xlHtml;	break;
	case ffMHT:		*pXlFmt = xlWebArchive;	break;
	case ffXML:		*pXlFmt = xlXMLSpreadsheet;	break;
	case ffXLSX:	*pXlFmt = xlOpenXMLWorkbook; break;
	case ffXLSM:	*pXlFmt = xlOpenXMLWorkbookMacroEnabled; break;
	case ffXLSB:	*pXlFmt = xlExcel12;  break;
	case ffPRN:		*pXlFmt = xlTextPrinter;	break;
	case ffET:		*pXlFmt = (XlFileFormat)0xfff1;	break;
	case ffETT:		*pXlFmt = (XlFileFormat)0xfff2;	break;
	case ffUOF:		*pXlFmt = (XlFileFormat)0xfff3;	break;
	case ffUOF2:	*pXlFmt = (XlFileFormat)0xfff4;	break;
	case ffDIF:		*pXlFmt = xlDIF;	break;
	default:
		return E_FAIL;
	}
	return S_OK;
}

HRESULT KEtWorkbook::SaveAsFormat(PCWSTR savePath, PCWSTR format)
{
	HRESULT hr = S_OK;
	if (m_ptrWorkbook.get() != nullptr)
	{
		m_ptrWorkbook->GetBook()->GetWoStake()->CreateCellCommentByWoComment();

		KComVariant varFileName;
		varFileName.AssignString(savePath);
		FILEFORMAT ff = KETFilterUtils::GetFileFormatByFileExt(format, FILTER_IMPORT_EXPORT);
		XlFileFormat apiff = xlOpenXMLWorkbook;
		ETFileFormatToXlFileFormat(ff, &apiff);
		KComVariant varFormat(apiff, VT_I4);
		VARIANT VAREMPTY = {VT_EMPTY};
		hr = m_ptrWorkbook->SaveAs(varFileName, varFormat,
									VAREMPTY,	VAREMPTY,
									VAREMPTY,	VAREMPTY,
									etNoChange,
									VAREMPTY,	KComVariant().AssignBOOL(FALSE),
									VAREMPTY,	VAREMPTY);
	}

	if(SUCCEEDED(hr))
	{
		_updateCalcStatusOnSave();
	}
	return hr;
}


HRESULT KEtWorkbook::ExecEncryptDecrypt(PCWSTR savePath, SaveAsMode saveAsMode)
{
	ETSAVEARGUMENT arg;
	ks_memset_s(&arg, 0, sizeof(arg));
    if (SaveAsMode::SAVE_AS_MODE_SEC == saveAsMode) {
        arg.nFileFormat = ffSECDOC;
        arg.bSaveToSecurityDocDirectly = true;
    } else if (SaveAsMode::SAVE_AS_MODE_NORMAL == saveAsMode) {
        arg.nFileFormat = hasVBAMacro(m_ptrWorkbook->GetBook()) ? ffXLSM : ffXLSX;
    } else {
        qDebug() << "ExecEncryptDecrypt: invalid parameters.";
        return E_FAIL;
    }

	wo::MediumGuard mediumNewGuard(savePath);
	FILTERMEDIUM* fm = mediumNewGuard.get();
	if (!fm)
		return E_FAIL;
	MakeEtSaveArgument(arg, fm, FALSE, FALSE, TRUE, TRUE, arg.nFileFormat, INVALIDIDX, TRUE, IODENYBREAK);
	ks_stdptr<IKDocument> doc = m_ptrWorkbook;
	if (doc)
		return doc->Save(&arg);
	return E_FAIL;
}

HRESULT KEtWorkbook::ExportSheetToCSV(PCWSTR savePath, IDX sheetIdx, bool bSkipBlankCols)
{
	if (isSecDoc())
	{
		ASSERT(false);
		return E_FAIL;
	}

	ETSAVEARGUMENT arg;
	ks_memset_s(&arg, 0, sizeof(arg));
	arg.nFileFormat = ffCSV;
	arg.bSkipBlankCol = alg::bool2BOOL(bSkipBlankCols);
	wo::MediumGuard mediumNewGuard(savePath);
	FILTERMEDIUM* fm = mediumNewGuard.get();
	if (!fm)
		return E_FAIL;
	MakeEtSaveArgument(arg, fm, FALSE, FALSE, TRUE, TRUE, arg.nFileFormat, sheetIdx, 
				TRUE, IODENYBREAK, FALSE, FALSE, FALSE, FALSE, etNoChange, TRUE);

	ks_stdptr<IKDocument> doc = m_ptrWorkbook;
	if (doc)
		return doc->Save(&arg);
	return E_FAIL;
}

HRESULT KEtWorkbook::ExportSheetToTxt(FILTERMEDIUM* fm, IDX sheetIdx, WCHAR delimiter)
{
	if (isSecDoc())
	{
		ASSERT(false);
		return E_FAIL;
	}

	ETSAVEARGUMENT arg;
	ks_memset_s(&arg, 0, sizeof(arg));
	arg.nFileFormat = ffCSV;
	if (!fm)
		return E_FAIL;
	
	MakeEtSaveArgument(arg, fm, FALSE, FALSE, TRUE, TRUE, arg.nFileFormat, sheetIdx, 
				TRUE, IODENYBREAK, FALSE, FALSE, FALSE, FALSE, etNoChange, TRUE);

	arg.SetCsvDelimiter(delimiter);		// #if WEB_OFFICE_ENABLE

	ks_stdptr<IKDocument> doc = m_ptrWorkbook;
	if (doc)
		return doc->Save(&arg);
	return E_FAIL;
}

void KEtWorkbook::SetExportLeftTopCell(IKWorkbook* pWorkbook, std::vector<INT32>& backup)
{
	IKWorksheets* pWorkshees = pWorkbook->GetWorksheets();
	if(pWorkshees->GetSheetCount() == 0)
		return;

	ISheet* pSheet = pWorkshees->GetSheetItem(0)->GetSheet();
	if(!pSheet)
		return;

	ks_stdptr<IUnknown> ptrUnk = nullptr;	
	pSheet->GetExtDataItem(edSheetWndInfos, &ptrUnk);
	ks_stdptr<ISheetWndInfos> ptrSheetWndInfos = NULL;
	if (ptrUnk != NULL)
		ptrUnk->QueryInterface(IID_ISheetWndInfos, (void**)&ptrSheetWndInfos);
	if (NULL == ptrSheetWndInfos) 
		return;

	INT nCount = 0;
	VS( ptrSheetWndInfos->GetCount(&nCount) );
	for (INT i = 0; i < nCount; ++i)
	{
		ks_stdptr<ISheetWndInfo> ptrSheetWndInfo;
		SHEETWNDINFO* pWNDINFO = NULL;
		VS( ptrSheetWndInfos->GetItem(i, &ptrSheetWndInfo) );
		VS( ptrSheetWndInfo->GetSheetWndInfo((const SHEETWNDINFO**)&pWNDINFO) );

		backup.push_back(pWNDINFO->cellLeftTop.row);
		backup.push_back(pWNDINFO->cellLeftTop.col);
		backup.push_back(pWNDINFO->PaneInfo.LTCell.row);
		backup.push_back(pWNDINFO->PaneInfo.LTCell.col);
		if (!pWNDINFO->fHasPaneInfo)
		{
			pWNDINFO->cellLeftTop.row = 0;
			pWNDINFO->cellLeftTop.col = 0;
			continue;
		}

		if(pWNDINFO->PaneInfo.PanePosition.left == 0)
			pWNDINFO->cellLeftTop.col = 0;
		if(pWNDINFO->PaneInfo.PanePosition.top == 0)
			pWNDINFO->cellLeftTop.row = 0;
		
		pWNDINFO->PaneInfo.LTCell.row = 
			MIN(pWNDINFO->cellLeftTop.row + pWNDINFO->PaneInfo.PanePosition.top, MAX_ROWS_COUNT_XTND - 1);
		pWNDINFO->PaneInfo.LTCell.col = 
			MIN(pWNDINFO->cellLeftTop.col + pWNDINFO->PaneInfo.PanePosition.left, MAX_COLS_COUNT_XTND - 1);
	}
}

void KEtWorkbook::RecoveryExportLeftTopCell(IKWorkbook* pWorkbook, const std::vector<INT32>& backup)
{
	IKWorksheets* pWorkshees = pWorkbook->GetWorksheets();
	if(pWorkshees->GetSheetCount() == 0)
		return;

	ISheet* pSheet = pWorkshees->GetSheetItem(0)->GetSheet();
	if(!pSheet)
		return;

	ks_stdptr<IUnknown> ptrUnk = nullptr;	
	pSheet->GetExtDataItem(edSheetWndInfos, &ptrUnk);
	ks_stdptr<ISheetWndInfos> ptrSheetWndInfos = NULL;
	if (ptrUnk != NULL)
		ptrUnk->QueryInterface(IID_ISheetWndInfos, (void**)&ptrSheetWndInfos);
	if (NULL == ptrSheetWndInfos)
		return;

	INT nCount = 0;
	VS( ptrSheetWndInfos->GetCount(&nCount) );
	ASSERT(backup.size() == nCount * 4);
	if(backup.size() != nCount * 4)
	{
		WOLOG_INFO << "recovery export lefttop cell failed";
		return;
	}
	size_t backupIndex = 0;
	for (INT i = 0; i < nCount; ++i)
	{
		ks_stdptr<ISheetWndInfo> ptrSheetWndInfo;
		SHEETWNDINFO* pWNDINFO = NULL;
		VS( ptrSheetWndInfos->GetItem(i, &ptrSheetWndInfo) );
		VS( ptrSheetWndInfo->GetSheetWndInfo((const SHEETWNDINFO**)&pWNDINFO) );
		pWNDINFO->cellLeftTop.row = backup[backupIndex++];
		pWNDINFO->cellLeftTop.col = backup[backupIndex++];
		pWNDINFO->PaneInfo.LTCell.row = backup[backupIndex++];
		pWNDINFO->PaneInfo.LTCell.col = backup[backupIndex++];
	}
}

HRESULT KEtWorkbook::ExportToLocalFile(PCWSTR savePath, FILEFORMAT fileFormat, KEtRevisionContext& ctx)
{
    ETSAVEARGUMENT arg;
    ks_memset_s(&arg, 0, sizeof(arg));
    arg.nFileFormat = fileFormat;
    if (isSecDoc())
    {
        if (GetBMP()->bKsheet)
            return E_FAIL;
        arg.nFileFormat = ffSECDOC;
    }

	wo::MediumGuard mediumNewGuard(savePath);
	FILTERMEDIUM* fm = mediumNewGuard.get();
	if (!fm)
		return E_FAIL;

    MakeEtSaveArgument(arg, fm, FALSE, FALSE, TRUE, TRUE, arg.nFileFormat, INVALIDIDX,
                       TRUE, IODENYBREAK, FALSE, FALSE, FALSE, FALSE, etNoChange, TRUE);

    std::unique_ptr<AddWorkbookScope> newWorkbookScope;
    ks_stdptr<IKWorkbook> spWorkbook = m_ptrWorkbook;
    if (GetBMP()->bKsheet)
    {
        ks_stdptr<Workbooks> spWorkbooks;
        GetCoreApp()->get_Workbooks(&spWorkbooks);
        if (!spWorkbooks)
        {
            WO_LOG_X(getLogger(), WO_LOG_ERROR, "Fail to get Workbooks!");
            return E_FAIL;
        }

        newWorkbookScope = std::make_unique<AddWorkbookScope>(spWorkbooks.get(), this, ctx, static_cast<UINT>(1),
                                                              __X("微软雅黑"), 11);
        _Workbook* pNewWorkbook = newWorkbookScope->GetWorkbook();
        if (!pNewWorkbook)
        {
            WO_LOG_X(getLogger(), WO_LOG_ERROR, "Fail in AddWorkbookScope!");
            return E_FAIL;
        }

        et_sptr<KSheet2XlsxExporter> exporter;
        exporter = new KSheet2XlsxExporter(this, pNewWorkbook, &ctx);
        HRESULT hr = exporter->Init();
        if (FAILED(hr))
        {
            WO_LOG_X(getLogger(), WO_LOG_ERROR, "Fail in Db2XlsxExporter::Init()!");
            return E_FAIL;
        }
        hr = exporter->Exec();
        if (FAILED(hr))
        {
            WO_LOG_X(getLogger(), WO_LOG_ERROR, "Fail in Db2XlsxExporter::Exec()!");
            return E_FAIL;
        }
        spWorkbook = newWorkbookScope->GetWorkbook();
    }
    std::vector<INT32> leftTopCellBackup;
    SetExportLeftTopCell(spWorkbook, leftTopCellBackup);
    HRESULT hr = DownloadOnlineImage(spWorkbook);
    if (FAILED(hr))
        return hr;
	if (hasDashBoard())
	{
		hr = util::DownloadedDashBoardSnapshot(&ctx, this);
		if (FAILED(hr))
		return hr;
	}

    hr = spWorkbook->Save(&arg);
    RecoveryExportLeftTopCell(spWorkbook, leftTopCellBackup);
    return hr;
}

HRESULT KEtWorkbook::ExportToDb(PCWSTR savePath) const
{
	ETSAVEARGUMENT arg;
	wo::MediumGuard mediumNewGuard(savePath);
	arg.fm = mediumNewGuard.get();
	if (!arg.fm)
		return E_FAIL;
	arg.nFileFormat = ffDBT;
	arg.bRemember = FALSE;
	arg.ioBreakType = IODENYBREAK;

	return m_ptrWorkbook->Save(&arg);
}

HRESULT KEtWorkbook::ExportXlsxToKSheet(PCWSTR savePath) const
{
	if (isSecDoc())
	{
		ASSERT(false);
		return E_FAIL;
	}

	ETSAVEARGUMENT arg;
	wo::MediumGuard mediumNewGuard(savePath);
	arg.fm = mediumNewGuard.get();
	if (!arg.fm)
		return E_FAIL;
	arg.nFileFormat = ffKSHEET;
	arg.bRemember = FALSE;
	arg.ioBreakType = IODENYBREAK;

	VS(m_ptrWorkbook->put_Password(nullptr));
	return m_ptrWorkbook->Save(&arg);
}

HRESULT KEtWorkbook::ExportDbToKSheet(PCWSTR savePath) const
{
	ETSAVEARGUMENT arg;
	MediumGuard mediumNewGuard(savePath);
	arg.fm = mediumNewGuard.get();
	if (!arg.fm)
		return E_FAIL;
	arg.nFileFormat = ffKSHEET;
	arg.bRemember = FALSE;
	arg.ioBreakType = IODENYBREAK;

	ks_stdptr<IKDocument> doc = m_ptrWorkbook;
	if (doc)
		return doc->Save(&arg);
	return E_FAIL;
}

HRESULT KEtWorkbook::ExportKSheetToKSheet(PCWSTR savePath) const
{
	ETSAVEARGUMENT arg;
	MediumGuard mediumNewGuard(savePath);
	arg.fm = mediumNewGuard.get();
	if (!arg.fm)
		return E_FAIL;
	arg.nFileFormat = ffKSHEET;
	arg.bRemember = FALSE;
	arg.ioBreakType = IODENYBREAK;

	return m_ptrWorkbook->Save(&arg);
}

HRESULT KEtWorkbook::DownloadOnlineImage(IKWorkbook* pBook)
{
    IKWorksheets* pWorksheets = pBook->GetWorksheets();
    int sheetCount = pWorksheets->GetSheetCount();
    for (int i = 0; i < sheetCount; ++i)
    {
        ISheet* pSheet = pWorksheets->GetSheetItem(i)->GetSheet();
        DownloadImgSyncRes downRes = CheckDownLoadImg(pBook, pSheet);
        if (DownloadImgSync_TOO_LARGE == downRes)
        {
            WO_LOG_X(getLogger(), WO_LOG_ERROR, "download images size exceed limit");
            return E_DOWNLOAD_IMAGE_SIZE_EXCEED_LIMIT;
        }
        else if (DownloadImgSync_UNKNOWN == downRes)
        {
            WO_LOG_X(getLogger(), WO_LOG_ERROR, "download images unknown reason");
            return E_DOWNLOAD_IMAGE_UNKNONW;
        }
    }
    return S_OK;
}

WebInt KEtWorkbook::CheckDownLoadImg(IKWorkbook* pBook, ISheet *spSheet, bool bForceAttachmentReDownload/* = true*/)
{
	WebInt downloadRes = DownloadImgSync_OK;
	downloadRes = util::DownloadCellImgForSheet(pBook, spSheet);
	if (DownloadImgSync_OK != downloadRes)
		return downloadRes;

	downloadRes = util::DownloadSheetAllUrlImg(spSheet, bForceAttachmentReDownload);
	if (DownloadImgSync_OK != downloadRes)
		return downloadRes;

	downloadRes = util::DownloadCommentImage(spSheet);
	if (DownloadImgSync_OK != downloadRes)
		return downloadRes;

	return downloadRes;
}

void KEtWorkbook::AfterFileOpen()
{
	etcore::TimeStat timeStat([&](unsigned int time) {	
		IWorkspace* pWs = m_book->LeakWorkspace();
		IEtEventTracking* pEventTracking = pWs->GetEventTracking();
		IEtCollectInfo* pCollectInfo = pEventTracking->GetCollectInfo();
		KComVariant varVal(time);
		pCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::WO_AFTER_FILE_OPEN_TIME_CONSUMING, varVal);
	});
	// 合并表格
	{
		IBookSetting* pSetting = m_book->GetWoStake()->getSetting();
		BOOL bTasksAutoUpdate =  pSetting->isMergeTasksAutoUpdate();
		if (!bTasksAutoUpdate)
		{
			int sheetCnt = 0;
			m_book->GetSheetCount(&sheetCnt);
			for (int i = 0; i < sheetCnt; ++i)
			{
				ks_stdptr<ISheet> spSheet;
				m_book->GetSheet(i, &spSheet);
				if (spSheet == NULL) return;

				std::vector<SMergeFileInfo> listMergeFile;
				ks_stdptr<IMergeFile> spMergeFile;
				spSheet->GetExtDataItem(edSheetMergeFile, (IUnknown**)&spMergeFile);
				if (!spMergeFile) return;

				spMergeFile->GetMergeFileInfo(listMergeFile);
				if(listMergeFile.empty()) continue;
				spMergeFile->SetAutoRefresh(false);
			}
		}
	}

	// 当文件来源为另存为或者上传时, 回落相关处理
	// 解除同步表关联; 回落跨 book 关联相关字段，删除同步底表
	BOOKERRORS* error {};
	m_book->GetBookErrors(&error);
	if (error and error->fSavedFromOtherDocument)
	{
		app_helper::KBatchUpdateCal _(this->m_book->LeakOperator());
		const char* const value = ::getenv("DB_DISABLE_UNSYNC_WHEN_SAVED_FROM_OTHER_BOOK");
		if (value == nullptr || strcmp(value, "true") != 0)
			UnsyncSheetWhenTheBookSavedFromOther(m_ptrWorkbook.get());
		WO_LOG_X(getLogger(), WO_LOG_INFO, "[KEtWorkbook::AfterFileOpen()] m_bReserveCrossBookDbData: %d", m_bReserveCrossBookDbData);
		if (!m_bReserveCrossBookDbData)
			ProcessCrossBookDbData(m_ptrWorkbook);
	}
	
	if (m_book->GetBMP()->bKsheet)
		RemoveVeryHiddenSheetOfQueryApp(this);

	UpdateDbSheets();
	RemoveInvalidTimerTasks();
	CollectAutoPasswordMaster();

    m_pUserOrganizesCache->FetchAll();
	m_pDvCustomListCache->OnFileOpened();
}

void KEtWorkbook::_clearDirtySparklineParts(KSparklineRefAreaVersion* pVersion)
{
	IWorkbookObj* wbObj = this->GetCoreWorkbook()->GetWoObject();
	for (int i = 0, cnt = wbObj->getSheetCount(); i < cnt; ++i)
	{
		IWorksheetObj* pSheetObj = wbObj->getSheetItem(i);
		IKWorksheet* pKSheet = pSheetObj->GetWorksheet();
		ks_stdptr<ISheet> pSheet = pKSheet->GetSheet();

		ks_stdptr<ICoreSparklineGroups> sparklineGrps;
		pSheet->GetExtDataItem(edSheetSparklineGroups, (IUnknown**) &sparklineGrps);
		if (!sparklineGrps)
			continue;

		if (pVersion)
		{
			pVersion->AddDirtySparklines(pSheetObj, sparklineGrps->GetVersioningDirtySparklines());
			pVersion->AddDirtySparklineGroups(pSheetObj, sparklineGrps->GetVersioningDirtySparklineGroups());
		}

		sparklineGrps->ClearVersioningDirtySparklines();
		sparklineGrps->ClearVersioningDirtySparklineGroups();
	}
}

void KEtWorkbook::SetCellKeyWordHighlight()
{
	CensorKeyWordHighLightHandler handler(this);
	handler.StartHandleETCell();
}

const FileSaveInfo& KEtWorkbook::GetFileSaveInfo()
{
	return m_lastSaveInfo;
}

const CoreServSettings& KEtWorkbook::GetSerCoreSettings()
{
	return m_serverSettings;
}

bool KEtWorkbook::isFileSizeExceedLimit()
{
	if (!m_lastSaveInfo.bSaved)
		return false;

	if (GetBMP()->bDbSheet)
		return false;

	const int64_t fileSizeLimit = m_serverSettings.fileSizeLimit;
	if (fileSizeLimit < 0)
		return false;

	if (m_lastSaveInfo.size >= fileSizeLimit)
		return true;

	return false;
}

bool KEtWorkbook::hasDashBoard()
{
	int nSheet = 0;
	m_book->GetSheetCount(&nSheet);
	for (int i = 0; i < nSheet; i++)
	{
		ks_stdptr<ISheet> spSheet;
		m_book->GetSheet(i, &spSheet);
		if (spSheet && spSheet->IsDbDashBoardSheet())
			return true;
	}
	return false;
}

bool KEtWorkbook::InitImportrangeCalc()
{
	if (m_importrangeCtx.isImportrangeCalcInited)
	{
		return false;
	}

	m_importrangeCtx.isImportrangeCalcInited = true;
	return true;
}

bool KEtWorkbook::InitMergeFileNetDisk()
{
	if (m_importrangeCtx.isMergeFileNetDiskInit)
	{
		return false;
	}

	m_importrangeCtx.isMergeFileNetDiskInit = true;
	return true;
}

WebInt KEtWorkbook::CalculateEAF(const WebSlice* slice)
{
	gs_callback->calculateEAF(slice);
	return WO_OK;
}

bool KEtWorkbook::asyncGetNetDiskFile(PCWSTR userid, PCWSTR url, DownloadFileTask task, std::vector<RANGE>* ranges)
{
	QString qUrl = QString::fromUtf16(url);
	QString qUserId = QString::fromUtf16(userid);
	WebSlice dummy = { nullptr, 0 };
	
	if (task == DownloadFileTask_ImportRange && m_importrangeCtx.isImportrangeCalcInited)
	{
		if (m_importrangeCtx.downloadUrlFiles.find(krt::utf16(qUrl)) == m_importrangeCtx.downloadUrlFiles.end())
		{
			std::string urlUtf8 = qUrl.toUtf8().data();
			gs_callback->asyncGetNetFile(urlUtf8.c_str(), qUserId.toUtf8(), DownloadFileTask_ImportRange, true, &dummy);
			m_importrangeCtx.downloadUrlFiles.emplace(krt::utf16(qUrl), std::chrono::steady_clock::now());

			WOLOG_INFO << "[importrange] download file, url: " << std::hash<std::string>{}(urlUtf8.c_str()) << ", userid: " << userid;
		}
	}
	else if (task == DownloadFileTask_CrossBook && m_importrangeCtx.isImportrangeCalcInited)
	{
		if (m_importrangeCtx.downloadFileIdFiles.find(krt::utf16(qUrl)) == m_importrangeCtx.downloadFileIdFiles.end())
		{
			std::string urlUtf8 = qUrl.toUtf8().data();
			Subscription::Utils utils(this);
			INT idx = utils.GetSupBookIndex(url);
			if (nullptr != ranges && alg::STREF_INV_BOOK != idx)
			{
				BinWriter bw;
				bw.addKey("ranges");
				bw.beginArray();
				for (auto it = ranges->cbegin(); it != ranges->cend(); ++it)
				{
					if (!it->IsValid())
						continue;
					bw.beginStruct();
					bw.addInt32Field(it->SheetFrom(), "sheetFrom");
					bw.addInt32Field(it->SheetFrom(), "sheetTo");
					bw.addInt32Field(it->RowFrom(), "rowFrom");
					bw.addInt32Field(it->RowTo(), "rowTo");
					bw.addInt32Field(it->ColFrom(), "colFrom");
					bw.addInt32Field(it->ColTo(), "colTo");
					bw.endStruct();
				}
				bw.endArray();
				ks_stdptr<ISupBooks> spSupbooks;
				GetCoreWorkbook()->GetBook()->GetSupBooks(&spSupbooks);
				ks_stdptr<ISupBook> spSupbook;
				spSupbooks->GetSupBook(idx, &spSupbook);
				INT nCount = 0;
				spSupbook->GetSheetCount(&nCount);
				bw.addKey("sheetNames");
				bw.beginArray();
				for (INT iSheet = 0; iSheet < nCount; iSheet++)
				{
					LPCWSTR pwszSheetName = NULL;
					spSupbook->GetSheetName(iSheet, &pwszSheetName);
					bw.addString(pwszSheetName);
				}
				bw.endArray();
				BinWriter::StreamHolder bt = bw.buildStream();
				WebSlice slice = {bt.get(), bw.writeLength()};
				gs_callback->asyncGetNetFile(urlUtf8.c_str(), qUserId.toUtf8(), DownloadFileTask_CrossBook, true, &slice);
			}
			else
			{
				gs_callback->asyncGetNetFile(urlUtf8.c_str(), qUserId.toUtf8(), DownloadFileTask_CrossBook, true, &dummy);
			}
			m_importrangeCtx.downloadFileIdFiles.emplace(krt::utf16(qUrl), std::chrono::steady_clock::now());

			WOLOG_INFO << "[cbrefers] download file, url: " << std::hash<std::string>{}(urlUtf8.c_str()) << ", userid: " << userid;
		}
	}
	else if (task == DownloadFileTask_MergeFile && m_importrangeCtx.isMergeFileNetDiskInit)
	{
		if (m_importrangeCtx.downloadMergeFiles.find(krt::utf16(qUrl)) == m_importrangeCtx.downloadMergeFiles.end())
		{
			std::string urlUtf8 = qUrl.toUtf8().data();
			gs_callback->asyncGetNetFile(urlUtf8.c_str(), qUserId.toUtf8(), DownloadFileTask_MergeFile, true, &dummy);
			m_importrangeCtx.downloadMergeFiles.emplace(krt::utf16(qUrl));

			WOLOG_INFO << "[mergefileautoupdate] download file, url: " << std::hash<std::string>{}(urlUtf8.c_str()) << ", userid: " << userid;
		}
	}
	else
	{
		if (task != DownloadFileTask_ImportRange && 
			task != DownloadFileTask_CrossBook && 
			task != DownloadFileTask_MergeFile)
		{
			WOLOG_INFO << "[importrange] asyncGetNetDiskFile invalid task: " << task;	
		}
		return false;
	}
	return true;
}

void KEtWorkbook::onGetNetDiskFileDone(PCWSTR userid, PCWSTR url, DownloadFileTask task)
{
	if (task == DownloadFileTask_ImportRange)
	{
		auto it = m_importrangeCtx.downloadUrlFiles.find(url);
		if (it != m_importrangeCtx.downloadUrlFiles.cend())
		{
			unsigned int ms = (std::chrono::steady_clock::now() - it->second) / std::chrono::milliseconds(1);
			if (ms > 100)
			{
				util::CollectInfo(this, __X("behaviour_importrange_request_cost"), ms);
			}
			m_importrangeCtx.downloadUrlFiles.erase(it);
		}
	}
	else if (task == DownloadFileTask_MergeFile)
	{
		m_importrangeCtx.downloadMergeFiles.erase(url);
	}
	else if (task == DownloadFileTask_CrossBook)
	{
		auto it = m_importrangeCtx.downloadFileIdFiles.find(url);
		if (it != m_importrangeCtx.downloadFileIdFiles.cend())
		{
			unsigned int ms = (std::chrono::steady_clock::now() - it->second) / std::chrono::milliseconds(1);
			if (ms > 100)
			{
				util::CollectInfo(this, __X("behaviour_crossbook_request_cost"), ms);
			}
			m_importrangeCtx.downloadFileIdFiles.erase(url);
		}
	}
}

void KEtWorkbook::UpdateCrossBookLinks(KEtRevisionContext *ctx)
{
	WOLOG_INFO << "[importrange] CrossRefRecalculate -> UpdateCrossBookLinks: start";

	ks_stdptr<ISupEditLinks> spEditlinks;
	getSupEditLinks(this, &spEditlinks);
	KComVariant type(oldapi::etLinkTypeETLinks);
	Subscription::Utils utils(this);

	if (spEditlinks && spEditlinks->BeginGetSourceName() == S_OK)
	{
		while (true)
		{
			ks_bstr fullName;
			if (spEditlinks->GetNextSourceName(&fullName) != S_OK)
				break;

			KComVariant bookPath(fullName);
			GetCoreWorkbook()->UpdateLink(bookPath, type, 0);
		}
	}
	WOLOG_INFO << "[importrange] CrossRefRecalculate -> UpdateCrossBookLinks: end";
}

bool KEtWorkbook::CollectCrossBookRefers(std::set<ks_wstring> *fileIds, bool *isComplete)
{
	WOLOG_INFO << "[importrange] CollectCrossBookRefers";
	ks_stdptr<ISupEditLinks> spEditlinks;
	getSupEditLinks(this, &spEditlinks);
	KComVariant type(oldapi::etLinkTypeETLinks);
	Subscription::Utils utils(this);
	*isComplete = true;

	if (spEditlinks && spEditlinks->BeginGetSourceName() == S_OK)
	{
		while (true)
		{
			ks_bstr fullName;
			if (spEditlinks->GetNextSourceName(&fullName) != S_OK)
				break;

			SUP_LINKS_INFO_STATUS slis = SLIS_Unknown;
			IDX iBook = etexec::STREF_INV_BOOK;
			if (S_OK != spEditlinks->GetStatus(fullName, &slis, &iBook) || etexec::STREF_INV_BOOK == iBook)
				continue;

			PCWSTR fileId = utils.GetSupBookFileId(iBook);
			if (fileId)
			{
				fileIds->emplace(fileId);
			}

			if (slis == SLIS_E_SourceNotFound || slis == SLIS_E_FileNotReady)
			{
				WOLOG_INFO << "[cbautoupdate] CollectCrossBookRefers, slisState: " << slis << ", fileId: " << fileId;
				*isComplete = false;
			}
		}
	}

	return true;
}

void KEtWorkbook::RecalculateImportRange(KEtRevisionContext *ctx)
{
	struct RecalcImpRgScope
	{
		ks_stdptr<IWorkspace> m_spWs;
		KEtRevisionContext* m_ctx;
		RecalcImpRgScope(KEtWorkbook* wwb, KEtRevisionContext* ctx) : m_ctx(ctx)
		{
			wwb->GetCoreWorkbook()->GetBook()->GetWorkspace(&m_spWs);
			m_spWs->BeginProc(m_ctx, TRUE);
		}
		~RecalcImpRgScope()
		{
			m_spWs->EndProc();
		}
	} scope(this, ctx);

	WOLOG_INFO << "[importrange] RecalculateImportRange";
	ks_stdptr<ISupEditLinks> spSupLinks;
	getSupEditLinks(this, &spSupLinks);
	if (!spSupLinks)
		return;

	InitImportrangeCalc();
	spSupLinks->UpdateUrlSupBooks(true, true);
}

void KEtWorkbook::AfterCrossRefRecalculate()
{
	ImportrangeContext& irCtx = getImportrangeContext();
	WOLOG_INFO << "[importrange] AfterCrossRefRecalculate： waiting urlfiles: " << irCtx.downloadUrlFiles.size()
		<< " , waiting cb files:" << irCtx.downloadFileIdFiles.size() << ", crossReclculating: " << irCtx.crossReclculating;

	if (irCtx.crossReclculating && irCtx.downloadUrlFiles.empty() && irCtx.downloadFileIdFiles.empty())
	{
		irCtx.crossReclculating = false;
		IBookStake *pBookStake = GetCoreWorkbook()->GetBook()->GetWoStake();
		std::set<ks_wstring> urls;
		std::set<ks_wstring> fileIds;

		Subscription::Utils utils(this);
		ICbSubscribeOp *pSubOp = utils.GetSubscribeOp();
		if (pSubOp)
		{
			pSubOp->CollectImportrangeRefers(&urls);
		}

		bool crossRefComplete = false;
		CollectCrossBookRefers(&fileIds, &crossRefComplete);

		const bool irComplete = !pBookStake->HasImportrangeIntItems();
		const bool complete = irComplete && crossRefComplete;
		WOLOG_INFO << "[importrange] complete: " << complete;

		std::vector<QByteArray> utf8Urls;
		std::vector<const char*> urlsArray;
		wo::util::ToUtf8Array(urls, &utf8Urls, &urlsArray);

		std::vector<QByteArray> utf8FileIds;
		std::vector<const char*> fileIdsArray;
		wo::util::ToUtf8Array(fileIds, &utf8FileIds, &fileIdsArray);

		if (gs_callback->calculateReferenceDone)
		{
			gs_callback->calculateReferenceDone(
				fileIdsArray.empty() ? nullptr : fileIdsArray.data(), 
				static_cast<int>(fileIdsArray.size()), 
				urlsArray.empty() ? nullptr : urlsArray.data(), 
				static_cast<int>(urlsArray.size()), 
				complete
			);

			if (complete)
			{
				utils.ReSubscribe(false);
			}
		}
	}

	return;
}

HRESULT KEtWorkbook::OnGetNetFileByUrlDone(KEtRevisionContext *ctx, NetFileRes res, PCWSTR url, PCWSTR fileId)
{
	if (!url || !fileId)
	{
		WOLOG_INFO << "[importrange] OnAsyncGetNetFileByUrlDone, invalid url";
		return E_FAIL;
	}

	QString qUrl = QString::fromUtf16(url);
	std::string urlUtf8 = qUrl.toUtf8().data();

	WOLOG_INFO << "[importrange] OnAsyncGetNetFileByUrlDone, res: " 
		<< res << ", url: " << std::hash<std::string>{}(urlUtf8) << ", fileId:" << fileId << ", userid:" << ctx->getUser()->userID();

	wo::util::CallTimeStat callTime("importrange", "calculate importrange");
	onGetNetDiskFileDone(ctx->getUser()->userID(), url, DownloadFileTask_ImportRange);

	if (res != NetFileRes_OK)
	{
		WOLOG_INFO << "[importrange] download file failed, res: " << res;
	}

	// crossbook processor 中初始化时，没有文件, 不需要处理
	if (res != NetFileRes_NotInLocal)
	{
		ks_stdptr<ISupEditLinks> spSupLinks;
		getSupEditLinks(this, &spSupLinks);
		if (spSupLinks)
		{
			spSupLinks->UpdateUrlSource(url, res == NetFileRes_OK ? fileId : nullptr, res);
			getImportrangeContext().advanceRecalsResVer();

			if (gs_callback->broadcast) 
			{
				binary_wo::BinWriter bw;
				SerialVerStatusInfo(bw);
				BroadcastChanged(bw, nullptr);
			}
		}
	}

	AfterCrossRefRecalculate();
	return S_OK;
}

bool KEtWorkbook::isSecDoc() const
{
	return wo::secdoc::IsSecurityDoc(GetCoreWorkbook());
}

void KEtWorkbook::doClearJobAfterExecCmd()
{
	KEtVersionManager* versionMgr = static_cast<KEtVersionManager*>(getVersionMgr());
	versionMgr->clearUnusedVersionData();

	rebuildSyncObjsVec();
}

void KEtWorkbook::writePerfStatLog()
{
	WOLOG_INFO << "[writePerfStatLog] writeCellValueCount: " << m_perfStat.writeCellValueCount << ", writeCellFormatCount:" << m_perfStat.writeCellFormatCount;

	if (getFileId())
	{
		util::CollectInfo(this, __X("behaviour_write_cellvalue_count"), m_perfStat.writeCellValueCount);
		util::CollectInfo(this, __X("behaviour_write_cellformat_count"), m_perfStat.writeCellFormatCount);
	}

	m_perfStat.writeCellValueCount = 0;
	m_perfStat.writeCellFormatCount = 0;
}

bool KEtWorkbook::GetBuildInPropertyValue(DocumentProperties *pDocumentProperties, LPCWSTR pPropName, DocumentProperty **ptrDoc, QString *pValue)
{
	KComVariant vName(pPropName);
	KSmartParam paramValue;
	if (SUCCEEDED(pDocumentProperties->get_Item(vName, ptrDoc)) &&
		*ptrDoc &&
		SUCCEEDED((*ptrDoc)->get_Value(&paramValue.VarData())))
	{
		if (paramValue.GetStringQuick())
		{
			*pValue = QString::fromUtf16(paramValue.GetStringQuick());
			return pValue->size() > 0;
		}
	}

	return false;
}

void KEtWorkbook::ClearMistakenDocAuthorName(const char* strDateBefore, const char** strCleanAuthors, int authorCount)
{
	ks_stdptr<_Workbook> spWorkbook = GetCoreWorkbook();
	ks_stdptr<IKCoreObject> spCoreObj;
	spWorkbook->get_BuiltinDocumentProperties(&spCoreObj);
	if (!spCoreObj)
		return;

	ks_stdptr<oldapi::DocumentProperties> spDocProps = spCoreObj;
	if (!spDocProps)
		return;

	long nCount = 0;
	if (S_OK != spDocProps->get_Count(&nCount) || nCount <= 0)
		return;

	QString strAuthor;
	ks_stdptr<oldapi::DocumentProperty> spAuthor;
	if (!GetBuildInPropertyValue(spDocProps, __X("Author"), &spAuthor, &strAuthor))
		return;

	QString strCreated;
	ks_stdptr<oldapi::DocumentProperty> spCreated;
	if (!GetBuildInPropertyValue(spDocProps, __X("Creation date"), &spCreated, &strCreated))
		return;

	QDateTime dateCreated = QDateTime::fromString(strCreated, "yyyy/M/d h:m:s");
	if (!dateCreated.isValid())
	{
		WOLOG_INFO << "[docauthor_clear] invalid doc date:" << strCreated;
		return;
	}

	QDateTime dateBefore = QDateTime::fromString(QString::fromUtf8(strDateBefore), "yyyy/M/d");
	if (!dateBefore.isValid())
	{
		WOLOG_INFO << "[docauthor_clear] invalid arg date:" << strDateBefore;
		return;
	}

	if (dateBefore <= dateCreated)
		return;

	bool exist = false;
	for (int i = 0; i < authorCount; ++i)
	{
		if (strAuthor == QString::fromUtf8(strCleanAuthors[i]))
		{
			exist = true;
			break;
		}
	}

	if (!exist)
		return;

	KComVariant paramValue(__X(""));
	spAuthor->put_Value(paramValue);

	WOLOG_INFO << "[docauthor_clear] clear author:" << strAuthor << ", date:" << strCreated;
}

ServiceType KEtWorkbook::getServiceType() const
{
	return m_serviceType;
}

void KEtWorkbook::setServiceType(ServiceType s)
{
	m_serviceType = s;
}

bool KEtWorkbook::isOptBySerializeInit(IRevisionContext* ctx)
{
	UINT sum = 0;
	for (WebInt i = ctx->getBaseTaskVersion() + 1; i < m_versionMgr->getTaskVersionCount(); i++)
	{
		KwVersion* version = m_versionMgr->getTaskVersion(i);
		ASSERT(version != nullptr);
		UINT sumTmp = sum + version->getSerialComplexity();
		sum = (sumTmp >= sum ? sumTmp : UINT_MAX );
		if (sum > SerialComplexityThreshold)
		{
			WOLOG_INFO << "OptBySerializeInit! SerialComplexity: " << sum;
			return true;
		}
	}
	return false;
}

void KEtWorkbook::onSerialVersionFailed(SerialVersionCode code)
{
	switch (code) 
	{
	case SerialVersionCode::inspectBaseFail:
		m_inspectCount++;
		m_coreMeric.setWstrMetrics(KWStrMetricItem::kSerialVersionFailed, __X("inspectFail"));
		break;
	case SerialVersionCode::optBySerialInit:
		m_optInitCount++;
		m_coreMeric.setWstrMetrics(KWStrMetricItem::kSerialVersionFailed, __X("optByInit"));
		break;
	case SerialVersionCode::outOfSerialLimit:
		m_coreMeric.add64MetricsGlb(K64MetricItem::kAllOutOfLimitSerVerCnt, 1);
		m_coreMeric.setWstrMetrics(KWStrMetricItem::kSerialVersionFailed, __X("outOfLimit"));
		break;
	}
}

void KEtWorkbook::onCleanChildProc(IRevisionContext* ctx)
{
	IEtRevisionContext* pEtCtx = IEtRevisionContext::cast(ctx);
	if (!pEtCtx || pEtCtx->GetNeedCleanChildProc())
	{
		WOLOG_INFO << "[KEtWorkbook] onCleanChildProc";
		gs_callback->onCleanChildProc();
	}
}

void KEtWorkbook::rebuildSyncObjsVec()
{
	if (m_staleOnNotExec) 
	{
		m_staleOnNotExecCount++;
		m_staleOnNotExec = false;
	}

	class UseConnRebuildSyncObjEnum: public IKEnumUserConn
	{
	public:
		UseConnRebuildSyncObjEnum(std::unordered_set<WebID>& stale)
		: m_stales(stale), m_logOnce(true)
		{
		}

		int Do(IKUserConn* user) override
		{
			ks_castptr<IKETUserConn> etConn = user;
			int removeCnt = handleStaleObjs(etConn);
			std::unordered_set<WebID> * objRemoveSet = etConn->getRemoveObjs();
			removeCnt += objRemoveSet->size();
			if (removeCnt)
			{
				ASSERT(objRemoveSet->empty());
				if (m_logOnce)
				{
					WOLOG_INFO << "[onStale][" << user->connID() << "] rebuild sync. staleCount: " << m_stales.size()
					<< ", removeAllCount: " << removeCnt
					<<", removeOjb size: " << objRemoveSet->size();
					m_logOnce = false;
				}

				rebuildSyncObjs(etConn);
				objRemoveSet->clear();
			}
			return 0;
		}
			
		int handleStaleObjs(IKETUserConn * etConn)
		{
			std::unordered_set<WebID> * objSet = etConn->getSerialObjsSet();
			if (objSet->empty())
				return 0;
			int cnt = 0;
			for (auto itr = m_stales.cbegin(); itr != m_stales.cend(); ++itr)
			{
				auto res = objSet->erase(*itr);
				cnt += res;
			}
			return cnt;
		}

		void rebuildSyncObjs(IKETUserConn * etConn)
		{
			std::unordered_set<WebID> * objSet = etConn->getSerialObjsSet();
			std::vector<WebID> * pSyncObj = etConn->getSyncObjs();
			pSyncObj->clear();

			if (!objSet->empty())
			{
				pSyncObj->reserve(objSet->size());
				pSyncObj->insert(pSyncObj->begin(), objSet->cbegin(), objSet->cend());
			}
		}

		private:
			std::unordered_set<WebID>& m_stales;
			bool m_logOnce;
	};

	// 如果有stale对象
	if (!m_staleObjs.empty())
	{
		// 根据set重建vector
		IKUserConns* userConns = m_ptrApp->getUserConns();
		UseConnRebuildSyncObjEnum resetEnum(m_staleObjs);
		userConns->enumUserConn(&resetEnum);
		m_staleObjs.clear();
	}
}

void KEtWorkbook::onDataChanged(IRevisionContext* ctx)
{
	onCleanChildProc(ctx);
	m_cfCache.Reset(GetCoreWorkbook()->GetBook());
	rebuildSyncObjsVec();
	clearInitCache();
	SetBookDocSlimNeedCheck(true);
}
class KAutoPasswordEnum : public IAutoPasswordEnum
{
public:
	STDPROC_(BOOL) Do(PCWSTR uuid) override;
	void Init(std::vector<ks_wstring>* pVec) { m_pVecUuid = pVec; }
private:
	std::vector<ks_wstring>* m_pVecUuid = nullptr;
};

STDIMP_(BOOL) KAutoPasswordEnum::Do(PCWSTR uuid)
{
	ASSERT(uuid);
	m_pVecUuid->push_back(uuid);
	return TRUE;
}

void KEtWorkbook::CollectAutoPasswordMaster()
{
	ks_stdptr<IAutoPasswordMgr> spAutoPasswordMgr;
	m_book->GetExtDataItem(edBookAutoPasswordManager, (IUnknown**)&spAutoPasswordMgr);
	if (!spAutoPasswordMgr || spAutoPasswordMgr->Empty())
		return;
	KAutoPasswordEnum autoPasswordEnum;
	std::vector<ks_wstring> vecUuid;
	autoPasswordEnum.Init(&vecUuid);
	spAutoPasswordMgr->Enum(&autoPasswordEnum);
	
	CollectAutoPasswordMasterInner(vecUuid);
}

void KEtWorkbook::CollectAutoPasswordMasterInner(const std::vector<ks_wstring>& vecUuid)
{
	if (!m_bNeedGetAutoPasswordMaster)
		return;
	ks_stdptr<IAutoPasswordMgr> spAutoPasswordMgr;
	m_book->GetExtDataItem(edBookAutoPasswordManager, (IUnknown**)&spAutoPasswordMgr);
	if (!spAutoPasswordMgr || spAutoPasswordMgr->Empty())
		return;
	binary_wo::BinWriter binWriter;

    binWriter.addKey("list");
    binWriter.beginArray();
    for (auto & it : vecUuid)
    {
		binWriter.addString(it.c_str());
    }
    binWriter.endArray();

    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice uuidList = {shbt.get(), binWriter.writeLength()};
    WebSlice userIdList = {nullptr, 0};
    gs_callback->getAutoPasswordMaster(&uuidList, &userIdList);

    if (userIdList.size == 0 || userIdList.data == nullptr)
        return;

    binary_wo::BinReader rd(userIdList.data, userIdList.size);
    binary_wo::VarObjRoot infoObj = rd.buildRoot();
    binary_wo::VarObj info = infoObj.cast();

    binary_wo::VarObj objList = info.get_s("list");
	UpdateAutoPasswordMaster(objList, NULL, spAutoPasswordMgr);
}

void KEtWorkbook::UpdateAutoPasswordMaster(const binary_wo::VarObj& list, PCWSTR newMasterId, IAutoPasswordMgr* pMgr)
{
	for (int i = 0; i < list.arrayLength_s(); ++i)
    {
        binary_wo::VarObj item = list.at_s(i);
        WebStr uuid = item.field_str("uuid");
        WebStr userId = item.field_str("userId");
		pMgr->SetMaster(uuid, userId);
    }
	gs_callback->clearAutoPasswordMasterCache();

	IKWorksheets * pWorksheets = m_ptrWorkbook->GetWorksheets();
	for (int i = 0, cnt = pWorksheets->GetSheetCount(); i < cnt; ++i)
	{
		IKWorksheet *pWorksheet = pWorksheets->GetSheetItem(i);
		if (!pWorksheet)
			continue;

		ks_castptr<_Worksheet> pApiWs = pWorksheet;
		ISheetProtection* pSheetProtection = pApiWs->GetProtection();
		if (!pSheetProtection || !pSheetProtection->IsProtected())
			continue;

		PCWSTR master = newMasterId;
		if (pSheetProtection->HasPasswordUUID())
			master = pMgr->GetItem(pSheetProtection->GetPasswordUUID());
		if (!master || xstrlen(master) == 0)
			continue;

		pSheetProtection->SetMaster(master);
	}
}

uint KEtWorkbook::getSerializeLimit()
{
	const uint delta = 1024 * 1024;
	IWoETSettings* pWoSettings = _kso_GetWoEtSettings();
	if (gs_signalSize > pWoSettings->GetCmdSerializeSizeLimit())
		return gs_signalSize + delta;
	return pWoSettings->GetCmdSerializeSizeLimit();
}

void KEtWorkbook::setApiCallTime(ApiCallTimeStat* callTime)
{
	m_apiCallTime = callTime;
}

KwVersion* KEtWorkbook::confirmTask(UINT32 userInnerID, KwTask* task, KwVersion::BanUndoType banUndo, WebInt baseVersion, bool isRollback)
{
	wo::util::SlowCallTimeStat callTime("confirmTask", 100);
	return m_batchTransHelper.confirmTask(userInnerID, task, banUndo, baseVersion, isRollback);
}

void KEtWorkbook::beginTransact(WebInt version)
{
	wo::util::SlowCallTimeStat callTime("beginTransact", 100);
	m_batchTransHelper.beginTransact(version);
}

void KEtWorkbook::endTransact(bool isRollback, IRevisionContext* pCtx)
{
	wo::util::SlowCallTimeStat callTime("endTransact", 100);
	m_batchTransHelper.endTransact(isRollback, pCtx);
}

void KEtWorkbook::serialModifyShapePics(KWebAcceptor* acpt)
{
	KBinSizeU32Mc<ISerialAcceptor> mcBin(&m_coreMeric, acpt, KU32MetricItem::kSerialShapePicsBytes);
	util::SlowCallTimeMetrics mcSlow("modifyShapePics", this, KU32MetricItem::kSerialShapePicsTime);
	base_type::serialModifyShapePics(acpt);
}

int KEtWorkbook::serialNewObjects(IRevisionContext* ctx, KwVersion* ver, KWebAcceptor* acpt)
{
	KU32MetricItem bytesItem, timeItem, cntItem;
	m_coreMeric.getSerNewObjMetricItem(bytesItem, timeItem, cntItem);
	
	KBinSizeU32Mc<ISerialAcceptor> mcBin(&m_coreMeric, acpt, bytesItem);
	util::SlowCallTimeMetrics mcSlow("newObj", this, timeItem);
	int cnt = base_type::serialNewObjects(ctx, ver, acpt);
	m_coreMeric.addU32Metrics(cntItem, cnt);
	return cnt;
}

int KEtWorkbook::serialModifyObjects(AbsObjects& objVec, KWebAcceptor* acpt)
{
	KBinSizeU32Mc<ISerialAcceptor> mcBin(&m_coreMeric, acpt, KU32MetricItem::kSerialOrgObjBytes);
	util::SlowCallTimeMetrics mcSlow("modifyObj", this, KU32MetricItem::kSerialOrgObjTime);
	int cnt = base_type::serialModifyObjects(objVec, acpt);
	m_coreMeric.addU32Metrics(KU32MetricItem::kSerialOrgObjCnt, cnt);
	m_coreMeric.addU32Metrics(KU32MetricItem::kSerialOrgObjOrigCnt, objVec.size());
	return cnt;
}

void KEtWorkbook::onSerialVersionStart()
{
	m_coreMeric.onSerialOnlyVersionStart();
}

void KEtWorkbook::onSerialVersionEnd(int cnt, int binSize)
{
	m_coreMeric.onSerialOnlyVersionEnd(cnt, binSize);
}

void KEtWorkbook::pushVersionTagUndoIndex(PCWSTR tag, WebInt idx)
{
	m_versionTagUndoIdx[tag].push_back(idx);
}

void KEtWorkbook::getVersionTagUndoIndex(PCWSTR tag, std::vector<WebInt>& undoIndex)
{
	if (m_versionTagUndoIdx.find(tag) != m_versionTagUndoIdx.end())
		undoIndex = m_versionTagUndoIdx[tag];
}

std::vector<ks_wstring> KEtWorkbook::popUndoVersionTag()
{
	std::vector<ks_wstring> vecTag = m_undoVersionTag;
	m_undoVersionTag.clear();
	return vecTag;
}


// =================================
KConnsSvrInfo::KConnsSvrInfo()
{
	m_hasInfo = true;
	resetSvrInfo();
}

void KConnsSvrInfo::addSvrInfo(WoConnFrom from, WoConnScene scene, WoConnLife life, int v)
{
	if (from < 0 || from >= _WoConnFromCount)
		from = WoConnFromUnknown;
	m_svrConnFrom[from] += v;

	if (scene < 0 || scene >= _WoConnSceneCount)
		scene = WoConnSceneUnknown;
	m_svrConnScene[scene] += v;

	if (life < 0 || life >= _WoConnLifeCount)
		life = WoConnLifeUnknown;
	m_svrConnLife[life] += v;
	
	m_hasInfo = true;
}

void KConnsSvrInfo::resetSvrInfo()
{
	if (!m_hasInfo)
		return;
	m_hasInfo = false;
	ks_memset_s(m_svrConnFrom, WoConnFromUnknown, sizeof(m_svrConnFrom));
	ks_memset_s(m_svrConnScene, WoConnSceneUnknown, sizeof(m_svrConnScene));
	ks_memset_s(m_svrConnLife, WoConnLifeUnknown, sizeof(m_svrConnLife));
}

int KConnsSvrInfo::getSvrConnFromCnt(int idx) const 
{
	if (idx < 0 || idx >= _WoConnFromCount)
	{
		idx = WoConnFromUnknown;
	}
	return m_svrConnFrom[idx]; 
}

int KConnsSvrInfo::getSvrConnSceneCnt(int idx) const 
{
	if (idx < 0 || idx >= _WoConnSceneCount)
	{
		idx = WoConnSceneUnknown;
	}
	return m_svrConnScene[idx]; 
}

int KConnsSvrInfo::getSvrConnLifeCnt(int idx) const 
{
	if (idx < 0 || idx >= _WoConnLifeCount)
	{
		idx = WoConnLifeUnknown;
	}
	return m_svrConnLife[idx]; 
}

// =================================
#ifdef _DEBUG
constexpr int MAX_CONN_LOG_CNT = 100;
#else
constexpr int MAX_CONN_LOG_CNT = 15;
#endif
KConnsReuseInfo::KConnsReuseInfo(const char * tag)
: m_tag(tag)
, m_reuseCnt(0)
, m_reuseCommonCnt(0)
, m_curLstIdx(0)
, m_illReuseCommonCnt(0)
, m_illReuseHasSharedIdCnt(0)
, m_hasEverSharedId(false)
{
	if (!m_tag)
		m_tag = ".";
}

KConnsReuseInfo::~KConnsReuseInfo()
{
	for (int i = 0; i < MAX_RECORD_CONNS_SIZE; ++i)
	{
		m_connsLst[i].clearConn();
	}
}

bool KConnsReuseInfo::onNewUserConn(PCWSTR connId, PCWSTR userId, bool isCommon)
{
	auto itr = m_conns.find(connId);
	if (itr == m_conns.end())
		return false;
	
	++m_reuseCnt;
	if (isCommon)
		++m_reuseCommonCnt;
	
	if (m_reuseCnt <= MAX_CONN_LOG_CNT)
	{
		WOLOG_INFO << "[ConnReuse] " << m_tag << " connId " << connId << " userId " << userId << " isComm " << isCommon;
	}
	return true;
}

void KConnsReuseInfo::incIllReuseCommonCnt(IKETUserConn * conn)
{
	++m_illReuseCommonCnt;
	if (m_illReuseCommonCnt <= MAX_CONN_LOG_CNT)
	{
		WOLOG_INFO << "[ConnReuse] " << m_tag << " illReuse. connId " << conn->connID() << " userId " << conn->userID() << " isComm " << conn->isCommonConnId();
	}
	if (m_hasEverSharedId)
	{
		auto itr = m_conns.find(conn->connID());
		if (itr != m_conns.end())
		{
			int ndx = itr->second;
			if (m_connsLst[ndx].m_hasEverSharedId)
				++m_illReuseHasSharedIdCnt;
		}
	}
}

PWSTR KConnsReuseInfo::createWstr(PCWSTR wsz)
{
	int sz = xstrlen(wsz);
	PWSTR res = new WCHAR[sz + 1];
	xstrncpy_s(res, sz + 1, wsz, sz);
	res[sz] = __Xc('\0');
	return res;
}

void KConnsReuseInfo::addConn(PCWSTR connId, bool hasSharedId)
{
	auto itr = m_conns.find(connId);
	if (itr == m_conns.end())
	{
		PWSTR wsz = createWstr(connId);
		if (m_connsLst[m_curLstIdx].m_connID)
		{
			size_t n = m_conns.erase(m_connsLst[m_curLstIdx].m_connID);
			m_connsLst[m_curLstIdx].clearConn();
			ASSERT(n == 1);
		}
		
		m_connsLst[m_curLstIdx].m_connID = wsz;
		m_connsLst[m_curLstIdx].m_hasEverSharedId =  hasSharedId;
		m_conns.insert(std::make_pair(wsz, m_curLstIdx));
		m_curLstIdx = (m_curLstIdx + 1) % MAX_RECORD_CONNS_SIZE;
		m_hasEverSharedId |= hasSharedId;
	}
}

bool KConnsReuseInfo::removeConn(PCWSTR connId, bool & outHasSharedId)
{
	auto itr = m_conns.find(connId);
	if (itr == m_conns.end())
		return false;
	
	int ndx = itr->second;
	outHasSharedId = m_connsLst[ndx].m_hasEverSharedId;
	
	m_conns.erase(itr);
	m_connsLst[ndx].clearConn();
	return true;
}

// =================================
void KConnsTypeInfo::addValue(IKETUserConn * conn, int v)
{
	m_cnt += v;
	bool hasSharedId = !conn->getSharedId().isEmpty();
	if (conn->isCommonConnId())
	{
		m_commCnt += v;
		if (conn->hasCall(wo::UserConnFuncCall::kQueryInit))
			m_commHasQueryInitCnt += v;
		
		if (conn->hasCall(wo::UserConnFuncCall::kExportAsSvg))
			m_commHasExportSvgCnt += v;
		
		if (conn->hasCall(wo::UserConnFuncCall::kQueryInit) || conn->hasCall(wo::UserConnFuncCall::kExportAsSvg))
			m_commHasQueryInitOrExportSvgCnt += v;
		
		if (conn->hasCall(wo::UserConnFuncCall::kUserJoin))
			m_commHasUserJoinCnt += v;
		if (hasSharedId)
			m_commHasSharedId += v;
	}
	
	if (conn->hasCall(wo::UserConnFuncCall::kHttpCalling))
		m_hasHttpCallCnt += v;
	
	if (hasSharedId)
	{
		m_hasSharedId += v;
		if (!conn->hasCall(wo::UserConnFuncCall::kQueryInit))
			m_hasSharedIdNotQueryInitCnt += v;
	}
	
	m_svrConnInfo.addSvrInfo(conn->getConnFrom(), conn->getConnScene(), conn->getConnLife(), v);
}

void KConnsTypeInfo::resetCoreInfo()
{
	m_cnt = 0;
	m_commCnt = 0;
	m_commHasQueryInitCnt = 0;
	m_commHasExportSvgCnt = 0;
	m_commHasQueryInitOrExportSvgCnt = 0;
	m_commHasUserJoinCnt = 0;
	m_hasHttpCallCnt = 0;
	m_commHasSharedId = 0;
	m_hasSharedId = 0;
	m_hasSharedIdNotQueryInitCnt = 0;
}

// =================================

KBookCollectInfo::KBookCollectInfo()
: m_cleanConnsReuseInfo("cleanReuse")
, m_userQuitConnsReuseInfo("userQuitReuse")
{
	resetDbViewsInfo();
	resetNewConnInfo();
}

KBookCollectInfo::~KBookCollectInfo()
{
}

void KBookCollectInfo::addDbSheetCount(int v)
{
	m_dbSheetCont += v;
}

void KBookCollectInfo::addDbViewCount(int v)
{
	m_dbViewCount += v;
}

void KBookCollectInfo::addVisibleRecordCount(int v)
{
	m_dbViewVisibleRecCnt += v;
}

void KBookCollectInfo::addVisibleFieldCount(int v)
{
	m_dbViewVisibleFieldCnt += v;
}

bool KBookCollectInfo::hasDbViewsCollectInfo()
{
	return m_dbViewCount > 0;
}

ks_wstring KBookCollectInfo::getSingalDbViewsCollectInfo()
{
	ks_wstring res;
	res.Format(__X("%d_%d_%d_%d"),
			m_dbSheetCont,
			m_dbViewCount,
			m_dbViewVisibleRecCnt,
			m_dbViewVisibleFieldCnt);
	return res;
}

void KBookCollectInfo::resetDbViewsInfo()
{
	m_dbSheetCont = 0;
	m_dbViewCount = 0;
	m_dbViewVisibleRecCnt = 0;
	m_dbViewVisibleFieldCnt = 0;
}

void KBookCollectInfo::onNewUserConn(IKETUserConn * conn, bool & outIsCleanReuse, bool & outIsQuitReuse)
{
	LPCWSTR connId = conn->connID();
	LPCWSTR userId = conn->userID();
	bool isCommon = conn->isCommonConnId();
	
	if (m_userQuitConnsReuseInfo.onNewUserConn(connId, userId, isCommon))
		outIsQuitReuse = true;
	else if (m_cleanConnsReuseInfo.onNewUserConn(connId, userId, isCommon))
		outIsCleanReuse = true;
	if (outIsQuitReuse || outIsCleanReuse)
	{
		++m_reuseCnt;
	}
	
	++m_newConnCnt;
	if (isCommon)
		++m_newCommConnCnt;
	
	WoConnFrom from = conn->getConnFrom();
	WoConnScene scene = conn->getConnScene();
	WoConnLife life = conn->getConnLife();
	m_newConnSvrInfo.addSvrInfo(from, scene, life, 1);
	
	//if (m_newConnCnt <= MAX_CONN_LOG_CNT)
	{
		WOLOG_INFO << "[ConnOnNew] connId " << connId << " userId " << userId << " isComm " << isCommon
				<< " svrFrom " << util::toSvrConnFromStr(from)
				<< " svrScene " << util::toSvrConnSceneStr(scene)
				<< " svrLife " << util::toSvrConnLifeStr(life);
	}
}

void KBookCollectInfo::onMarkDelayQuit(IKETUserConn * conn, bool markValue)
{
	int v = markValue ? 1 : -1;
	m_markQuitTypeInfo.addValue(conn, v);
}

void KBookCollectInfo::addCleanConn(IKETUserConn * conn)
{
	LPCWSTR connId = conn->connID();
	bool hasSharedId = !conn->getSharedId().isEmpty();
	bool userQuitSharedId = false;
	
	m_userQuitConnsReuseInfo.removeConn(connId, userQuitSharedId);
	
	m_cleanConnsReuseInfo.addConn(connId, hasSharedId | userQuitSharedId);
	m_cleanTypeInfo.addValue(conn, 1);
}

void KBookCollectInfo::onNotFoundUserQuit(PCWSTR connId)
{
	// 同时有清理和UserQuit。算UserQuit的。
	bool cleanSharedId = false;
	if (m_cleanConnsReuseInfo.removeConn(connId, cleanSharedId))
		m_userQuitConnsReuseInfo.addConn(connId, cleanSharedId);
}

ks_wstring KBookCollectInfo::takeReuseConnInfo()
{
	ks_wstring res;
	if (m_reuseCnt == 0)
		return res;

	int cleanReuse = m_cleanConnsReuseInfo.getReuseCnt();
	int quitReuse = m_userQuitConnsReuseInfo.getReuseCnt();
	int cleanIllCommReuse = m_cleanConnsReuseInfo.getIllReuseCommonCnt();
	int quitIllCommReuse = m_userQuitConnsReuseInfo.getIllReuseCommonCnt();	
	int cleanIllCommReuseSharedId = m_cleanConnsReuseInfo.getIllReuseHasSharedCommCnt();
	int quitIllCommReuseSharedId = m_userQuitConnsReuseInfo.getIllReuseHasSharedCommCnt();
	int cleanCommReuse = m_cleanConnsReuseInfo.getReuseCommonCnt();
	int quitCommReuse = m_userQuitConnsReuseInfo.getReuseCommonCnt();
	res.Format(__X("%d_%d_%d_%d_%d_%d_%d_%d_%d"), 
		cleanReuse,
		cleanCommReuse,
		quitReuse,
		quitCommReuse,
		cleanIllCommReuse,
		quitIllCommReuse,
		cleanIllCommReuseSharedId,
		quitIllCommReuseSharedId,
		m_reuseCnt);
		
	WOLOG_INFO << "[clearDeadConnection] reuse "
			<< m_reuseCnt
			<< " cleanReuseCnt_commCnt_quitCnt_quitCommCnt_cleanIllComm_quitIllComm_cleanIllCommShared_quitIllCommShared_allCnt " 
			<< res.c_str();
	resetReuseInfo();
	return res;
}

void KBookCollectInfo::resetReuseInfo()
{
	m_cleanConnsReuseInfo.resetCnt();
	m_userQuitConnsReuseInfo.resetCnt();
	
	m_reuseCnt = 0;
}

bool KBookCollectInfo::checkIsCollectReuseAndNewConn()
{ // 减少收集频率
#ifdef _DEBUG
	const int COLLECT_DATA_CNT = 1;
	const int COLLECT_COUNT = 1;
#else
	const int COLLECT_DATA_CNT = 10;
	const int COLLECT_COUNT = 6;
#endif
	int dataCnt = m_reuseCnt + m_newConnCnt;
	if (dataCnt == 0)
		return false;
	
	if (dataCnt > COLLECT_DATA_CNT || m_collectNewReuseCnt > COLLECT_COUNT)
	{
		m_collectNewReuseCnt = 0;
		return true; 
	}
	else
	{
		++m_collectNewReuseCnt;
		return false;
	}
}

ks_wstring KBookCollectInfo::takeCleanConnInfo()
{
	if (!m_cleanTypeInfo.hasCnt())
		return ks_wstring();
	
	ks_wstring res;
	res.Format(__X("%d_%d_%d_%d_%d_%d_%d_%d_%d_%d"),
		m_cleanTypeInfo.getCommCnt(),
		m_cleanTypeInfo.getHasQueryInitCnt(),
		m_cleanTypeInfo.getHasExportSvgCnt(),
		m_cleanTypeInfo.getHasQueryInitOrExportSvgCnt(),
		m_cleanTypeInfo.getHasUserJoinCnt(),
		m_cleanTypeInfo.getHttpCallCnt(),
		m_cleanTypeInfo.getCommHasSharedId(),
		m_cleanTypeInfo.getHasSharedId(),
		m_cleanTypeInfo.getHasSharedIdNoQueryInitCnt(),
		m_cleanTypeInfo.getCnt()
		);
	WOLOG_INFO << "[clearDeadConnection] cleanCoreInfo "
			<< m_cleanTypeInfo.getCnt()
			<< " comm_queryInit_exportSvg_initOrSvg_userJoin_httpCall_commSharedId_sharedId_sharedIdNoInit_allCnt " 
			<< res.c_str();
	m_cleanTypeInfo.resetCoreInfo();
	return res;
}

ks_wstring KBookCollectInfo::getCleanConnSvrFrom()
{
	return getConnSvrFrom(m_cleanTypeInfo.getSvrConnInfo(), "cleanSvrFrom");
}

ks_wstring KBookCollectInfo::getCleanConnSvrScene()
{
	return getConnSvrScene(m_cleanTypeInfo.getSvrConnInfo(), "cleanSvrScene");
}

ks_wstring KBookCollectInfo::getCleanConnSvrLife()
{
	return getConnSvrLife(m_cleanTypeInfo.getSvrConnInfo(), "cleanSvrLife");
}

ks_wstring KBookCollectInfo::getMarkQuitConnSvrFrom()
{
	return getConnSvrFrom(m_markQuitTypeInfo.getSvrConnInfo(), "delayCleanSvrFrom");
}

ks_wstring KBookCollectInfo::getMarkQuitConnSvrScene()
{
	return getConnSvrScene(m_markQuitTypeInfo.getSvrConnInfo(), "delayCleanSvrScene");
}

ks_wstring KBookCollectInfo::getMarkQuitConnSvrLife()
{
	return getConnSvrLife(m_markQuitTypeInfo.getSvrConnInfo(), "delayCleanSvrLife");
}

void KBookCollectInfo::resetMarkQuitConnSvrInfo()
{
	m_markQuitTypeInfo.resetSvrInfo();
}

ks_wstring KBookCollectInfo::getNewConnSvrFrom()
{
	return getConnSvrFrom(m_newConnSvrInfo, "newConnSvrFrom");
}

ks_wstring KBookCollectInfo::getNewConnSvrScene()
{
	return getConnSvrScene(m_newConnSvrInfo, "newConnSvrScene");
}

ks_wstring KBookCollectInfo::getNewConnSvrLife()
{
	return getConnSvrLife(m_newConnSvrInfo, "newConnSvrLife");
}

ks_wstring KBookCollectInfo::getConnSvrFrom(const KConnsSvrInfo & typeInfo, const char * logTag)
{
    if (!typeInfo.getHasSvrConnInfo())
        return ks_wstring();
	
	ks_wstring res;
	res.Format(__X("%d_%d_%d_%d_%d_%d_%d_%d_%d_%d"),
		typeInfo.getSvrConnFromCnt(WoConnFromUnknown),
		typeInfo.getSvrConnFromCnt(WoConnFromSession),
		typeInfo.getSvrConnFromCnt(WoConnFromPreOpenApi),
		typeInfo.getSvrConnFromCnt(WoConnFromServerApi),
		typeInfo.getSvrConnFromCnt(WoConnFromServer),
		typeInfo.getSvrConnFromCnt(WoConnFromAirscript2),
		typeInfo.getSvrConnFromCnt(WoConnFromAirscript1Session),
		typeInfo.getSvrConnFromCnt(WoConnFromAirscript1PreOpenApi),
		typeInfo.getSvrConnFromCnt(WoConnFromCore),
		typeInfo.getSvrConnFromCnt(WoConnFromSharedViewSession)
		);
		
	WOLOG_INFO << "[clearDeadConnection] "
		<< logTag
		<< " unknown_session_preOpen_svrApi_svr_as2_as1session_as1PreOpen_core_sharedView " 
		<< res.c_str();
	return res;
}

ks_wstring KBookCollectInfo::getConnSvrScene(const KConnsSvrInfo & typeInfo, const char * logTag)
{
	if (!typeInfo.getHasSvrConnInfo())
		return ks_wstring();
	
	ks_wstring res;
	res.Format(__X("%d_%d_%d_%d_%d_%d_%d_%d_%d_%d_%d"),
		typeInfo.getSvrConnSceneCnt(WoConnSceneUnknown),
		typeInfo.getSvrConnSceneCnt(WoConnSceneExport),
		typeInfo.getSvrConnSceneCnt(WoConnSceneAsyncJob),
		typeInfo.getSvrConnSceneCnt(WoConnSceneAttachmentSaveAs),
		typeInfo.getSvrConnSceneCnt(WoConnSceneNewAttachment),
		typeInfo.getSvrConnSceneCnt(WoConnSceneUpdateRange),
		typeInfo.getSvrConnSceneCnt(WoConnScenePaste),
		typeInfo.getSvrConnSceneCnt(WoConnSceneFileMerge),
		typeInfo.getSvrConnSceneCnt(WoConnSceneForm),
		typeInfo.getSvrConnSceneCnt(WoConnSceneAirSheetApp),
		typeInfo.getSvrConnSceneCnt(WoConnSceneAiRecognize)
		);
		
	WOLOG_INFO << "[clearDeadConnection] "
		<< logTag
		<< " unknown_export_asyncJob_attachSaveAs_newAttach_updateRange_paste_fileMr_form_asApp_aiRec " 
		<< res.c_str();
	return res;
}

ks_wstring KBookCollectInfo::getConnSvrLife(const KConnsSvrInfo & typeInfo, const char * logTag)
{
	if (!typeInfo.getHasSvrConnInfo())
		return ks_wstring();
	
	ks_wstring res;
	res.Format(__X("%d_%d_%d_%d_%d"),
		typeInfo.getSvrConnLifeCnt(WoConnLifeUnknown),
		typeInfo.getSvrConnLifeCnt(WoConnLifeSession),
		typeInfo.getSvrConnLifeCnt(WoConnLifeApi),
		typeInfo.getSvrConnLifeCnt(WoConnLifeCommand),
		typeInfo.getSvrConnLifeCnt(WoConnLifeProcess)
		);
		
	WOLOG_INFO << "[clearDeadConnection] "
		<< logTag
		<< " unknown_session_api_command_process " 
		<< res.c_str();
	return res;
}

void KBookCollectInfo::resetCleanConnSvrInfo()
{
	m_cleanTypeInfo.resetSvrInfo();
}

void KBookCollectInfo::onUserQuit(PCWSTR connId, bool hasSharedId)
{
	bool cleanHasShareded = false;
	m_cleanConnsReuseInfo.removeConn(connId, cleanHasShareded);
	
	m_userQuitConnsReuseInfo.addConn(connId, hasSharedId | cleanHasShareded);
}

void KBookCollectInfo::checkIllegalReuse(IKETUserConn * conn, bool isHttpCall)
{
	if (isHttpCall || !conn)
		return;
	if (conn->isCommonConnId() && !conn->hasCall(wo::UserConnFuncCall::kQueryInit) &&
		!conn->hasMarkReuseCollect())
	{
		if (conn->isReuseAfterClean())
		{
			m_cleanConnsReuseInfo.incIllReuseCommonCnt(conn);
			conn->markReuseCollect();
		}
		else if (conn->isReuseAfterQuit())
		{
			m_userQuitConnsReuseInfo.incIllReuseCommonCnt(conn);
			conn->markReuseCollect();
		}
	}
}

void KBookCollectInfo::resetNewConnInfo()
{
	m_newConnCnt = m_newCommConnCnt = 0;
}

ks_wstring KBookCollectInfo::takeMarkQuitConnInfo()
{
	if (!m_markQuitTypeInfo.hasCnt())
		return ks_wstring();
	
	ks_wstring res;
	res.Format(__X("%d_%d_%d_%d_%d_%d_%d_%d_%d_%d"),
		m_markQuitTypeInfo.getCommCnt(),
		m_markQuitTypeInfo.getHasQueryInitCnt(),
		m_markQuitTypeInfo.getHasExportSvgCnt(),
		m_markQuitTypeInfo.getHasQueryInitOrExportSvgCnt(),
		m_markQuitTypeInfo.getHasUserJoinCnt(),
		m_markQuitTypeInfo.getHttpCallCnt(),
		m_markQuitTypeInfo.getCommHasSharedId(),
		m_markQuitTypeInfo.getHasSharedId(),
		m_markQuitTypeInfo.getHasSharedIdNoQueryInitCnt(),
		m_markQuitTypeInfo.getCnt()
		);
	WOLOG_INFO << "[clearDeadConnection] delayCleanCoreInfo " 
			<< m_markQuitTypeInfo.getCnt()
			<< " comm_queryInit_exportSvg_initOrSvg_userJoin_httpCall_commSharedId_sharedId_sharedIdNoInit_allCnt " 
			<< res.c_str();
	m_markQuitTypeInfo.resetCoreInfo();
	return res;
}

ks_wstring KBookCollectInfo::takeNewConnInfo()
{
	if (m_newConnCnt == 0)
		return ks_wstring();
	
	ks_wstring res;
	res.Format(__X("%d"),
		m_newCommConnCnt
		);
	WOLOG_INFO << "[clearDeadConnection] newConn "
			<< m_newConnCnt
			<< " comm " << res.c_str();
	resetNewConnInfo();
	return res;
}

} //namespace wo
