﻿#ifndef __WEBET_DATABASE_FIELD_IDENTIFIER_H__
#define __WEBET_DATABASE_FIELD_IDENTIFIER_H__

#include "database_def.h"

namespace wo
{
namespace Database
{

class FieldValidationTypeIdentifier : public IFieldIdentifier
{
public:
    FieldValidationTypeIdentifier(DVValueType);

    virtual BOOL Identify(FieldContext *, const RANGE &, VALIDATION) override;
private:
    DVValueType m_type;
};

class FieldFormulaValidationIdentifier : public IFieldIdentifier
{
public:
    FieldFormulaValidationIdentifier(PCWSTR);

    virtual BOOL Identify(FieldContext *, const RANGE &, VALIDATION) override;
private:
    QString m_formula;
};

class FieldFormulaValidationRegexIdentifier : public IFieldIdentifier
{
public:
    FieldFormulaValidationRegexIdentifier(PCWSTR);

    virtual BOOL Identify(FieldContext *, const RANGE &, VALIDATION) override;
private:
    QRegExp m_formulaRegex;
};

class FieldNumberFormatIdentifier : public IFieldIdentifier
{
public:
    FieldNumberFormatIdentifier(NumFmtCat nfc, bool bAllowGeneral);

    virtual BOOL Identify(FieldContext *, const RANGE &, VALIDATION) override;
private:
    NumFmtCat m_nfc;
    bool m_bAllowGeneral;
};

} // Database
} // wo

#endif // __WEBET_DATABASE_FIELD_IDENTIFIER_H__