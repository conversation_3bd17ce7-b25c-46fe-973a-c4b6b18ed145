﻿#include "etstdafx.h"
#include "sortitem_loader.h"
#include <appcore/et_appcore_cellcolor_itf.h>

namespace wo
{
namespace range_sort
{

static void GetCustomListString(_Application *app, int list_id, ks_wstring &customList)
{
	long nListCount = 0;
	app->get_CustomListCount(0, &nListCount);

	if (list_id <= 0 || list_id > nListCount)
		return;

	ks_stdptr<ICustomLists> spCustomLists = app->GetCustomLists();
	ks_stdptr<ICustomList> spCustomList;
	spCustomLists->GetList(list_id - 1, &spCustomList);
	ASSERT(spCustomList);

	long nItemCount = 0;
	spCustomList->GetCount(&nItemCount);

	for (long i = 0; i < nItemCount; ++i)
	{
		PCWSTR str = NULL;
		spCustomList->GetItem(i, &str);
		ASSERT(str);
		customList.append(str);
		if (i < nItemCount - 1)
		{
			customList.append(1, __Xc(','));
		}
	}
}

static void LoadCondition(_Application *app, IKSortCondition *cond, SortItem &item)
{
	const RANGE *krange = nullptr;
	cond->GetKeyRange(&krange);
	if (krange == nullptr || !krange->IsValid()) 
		return;

	item.keyRange = *krange;
	// 自定义序列
	ks_wstring customList;
	cond->GetCustomList(customList);
	if (customList.empty())
		GetCustomListString(app, cond->GetCustomListID(), customList);
	if (!customList.empty())
		item.customList = QString::fromUtf16(customList.c_str()).split(',', QString::SkipEmptyParts);


	item.sortOn = cond->GetSortOn();
	item.bAscending = cond->GetAscending(); // 升序
	switch (cond->GetSortOn())
	{
	case sot_Values:
		break;
	case sot_CellColor:
	{
		ks_stdptr<ICellColor> spCell;
		VS(cond->GetCellColor(&spCell));
		ASSERT(spCell);
		item.spCellColor = spCell;
	}
	break;
	case sot_FontColor:
	{
		ks_stdptr<IFontColor> spFont;
		VS(cond->GetFontColor(&spFont));
		ASSERT(spFont);
		item.spFontColor = spFont;
	}
	break;
	case sot_Icon:
	{
		ks_stdptr<ICellIcon> spIcon;
		VS(cond->GetCellIcon(&spIcon));
		ASSERT(spIcon);
		item.spCellIcon = spIcon;
	}
	break;
	default:
		ASSERT(FALSE);
		break;
	}
}

bool LoadSortItems(_Application *app, IKSortData *sort_data, SortItems *sort_items)
{
	for (UINT i = 0, n = sort_data->GetConditionCount(); i < n; ++i)
	{
		ks_stdptr<IKSortCondition> cond;
		VS(sort_data->GetCondition(i, &cond));
		ASSERT(cond);

		SortItem item;
		LoadCondition(app, cond, item);
		sort_items->push_back(item);
	}

	return true;
}

} // namespace range_sort
} // namespace wo