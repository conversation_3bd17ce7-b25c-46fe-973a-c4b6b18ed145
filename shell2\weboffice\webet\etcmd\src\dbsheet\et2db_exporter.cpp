﻿#include "etstdafx.h"
#include "et2db_exporter.h"

#include "binvariant/binreader.h"
#include "database/database.h"
#include "db_export_helper.h"
#include "etcmd/src/helpers/autofilter_helper.h"
#include "etcmd/src/helpers/pivot_tables_helper.h"
#include "et_dbsheet_utils.h"
#include "util.h"
#include "webetlink.h"
#include "wo/et_revision_context.h"
#include "helpers/varobject_helper.h"
#include "etcmd/src/et_revision_context_impl.h"
#include "etcmd/src/protection_context_impl.h"
#include "etcmd/src/hresult_to_string.h"
#include "helpers/table_struct_rec_helper.h"
#include "ettools/ettools_encode_decoder.h"
#include "helpers/db_field_recognize_helper.h"
#include "helpers/picture_upload_helper.h"
#include "dbsheet/et_dbsheet_copysheet_helper.h"
#include <public_header/opl/mvc/et_shape_tree.h>
#include "utils/attachment_utils.h"
#include "utils/et_gridsheet_utils.h"
#include "utils/qrlabel_utils.h"
#include "utils/sheet_rename_utils.h"
#include "helpers/db_field_copy_helper.h"
#include "db/db_basic_itf.h"

extern Callback* gs_callback;

namespace wo
{

PCWSTR EtExportError::GetErrName() const
{
	if (ProtectedTable())
		return __X("ProtectedTableError");
	if (HasBanCreateBlankSheet())
		return __X("BanCreateBlankSheetError");
	if (InvalidSheet())
		return __X("InvalidSheetError");
	if (ExceptionThrown())
		return __X("ExceptionThrownError");
	if (HasFailedMsg())
		return __X("HasFailedMsgError");
	if (SheetCntLimit())
		return __X("SheetCntLimitError");
	if (RowCntLimit())
		return __X("RowCntLimitError");
	if (!SucceedSheets())
		return __X("UnknowRessonError");
	return nullptr;
}

Et2DbConverter::Et2DbConverter(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, KEtRevisionContext* ctx, bool toEmptySheet, VarObj& defaultName, bool bIgnoreChangeFieldType)
    : m_spEtApiWb(pEtWorkbook)
	, m_spDbApiWb(pDbWorkbook)
	, m_pEtBookOp(pEtWorkbook->GetBook()->LeakOperator())
	, m_pDbBookOp(pDbWorkbook->GetBook()->LeakOperator())
	, m_ctx(ctx)
	, m_2EmptySheet(toEmptySheet)
	, m_defaultName(defaultName)
    , m_etSheetIdx(alg::STREF_INV_SHEET), m_dbSheetIdx(0), m_etRange(pEtWorkbook->GetBook()->GetBMP())
    , m_fieldContext(m_spEtApiWb.get(), m_ctx)
    , m_idxColsNeedRename(0), m_fieldTypeChange(false), m_bIgnoreChangeFieldType(bIgnoreChangeFieldType)
{
    m_defaultFieldName = defaultName.field_str("field_name");
    m_defaultGroupName = defaultName.field_str("group_name");
    m_gridViewName = defaultName.field_str("grid_name");
    m_kanbanViewName = defaultName.field_str("kanban_name");
	VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
}

HRESULT Et2DbConverter::_ImportRangeFromSheet()
{
    try
    {
        return _ImportRangeInner();
    } // end try
    catch (et_exception ex)
    {
        m_errorStatus.ToggleExceptionThrown(true);
        HRESULT hr = ex.get_result();
        WOLOG_ERROR << "[Et2DbImporter] Exception thrown in _ImportRangeInner: " << hr << ", " << GetErrWideString(hr);
        m_spDbApiWs->DeleteDirectly();
        m_spDbApiWs.clear();
        return hr;
    }
}

HRESULT Et2DbConverter::_ImportRangeInner()
{
    HRESULT hr = S_OK;
    // 进行 dbsheet 其他内容的初始化
    hr = _InitDbSheetContent(m_spDbApiWs.get(), m_etRange);
    if (FAILED(hr))
    {
        WOLOG_ERROR << "[Et2DbImporter] Fail in _InitDbSheetContent!";
        return hr;
    }

    // 存在多于1行的情形, 则将更多的行作为数据导入.
    if (m_etRange.RowTo() != m_etRange.RowFrom())
    {
        hr = _ImportDataFromRange();
        if (FAILED(hr))
        {
            WOLOG_ERROR << "[Et2DbImporter] Fail in _ImportDataFromRange!";
            return hr;
        }
    }

    // 设置列宽
    ks_stdptr<IDBSheetView_Grid> spGridView = m_spView;
    int defaultTwip = 2508; // 产品定的值，写死，后面有需要再改，新增字段的列宽
    int minTwip = 1572; // 产品定的值，写死，后面有需要再改，字段最短列宽
    ks_stdptr<ISheet> spEtSheet;
    m_pEtBookOp->GetSheet(m_etSheetIdx, &spEtSheet);
    const IDBIds* fldIds = m_spDbSheetOp->GetAllFields();
    ks_stdptr<IKWorksheet> spWorksheet = m_spEtApiWb->GetWorksheets()->GetSheetItem(m_etSheetIdx);
    for (int i = 0; i < fldIds->Count(); ++i)
    {
        INT twip = 0;
        spEtSheet->GetColWidth(m_validCols[i], &twip);
        if (twip < minTwip)
            twip = minTwip;
        EtDbId fldId = fldIds->IdAt(i);
        spGridView->SetFieldWidth(fldId, twip, FALSE);
        m_spDbApiWs->GetSheet()->SetColWidth(i, i, twip);
    }

    // 设置关键字段
    EtDbId fldId = fldIds->IdAt(0);
    IDbFieldsManager* pFieldMgr = m_spView->GetFieldsManager();
	hr = pFieldMgr->SetPrimaryField(fldId);
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[Et2DbImporter] Fail in SetPrimaryField!";
		return hr;
	}

    //Todo:除了创建看板视图 后续还需要创建其他视图。
    hr = _CreateKanbanView(m_spDbApiWs->GetSheet(), defaultTwip);
    if (FAILED(hr))
    {
        WOLOG_ERROR << "[Et2DbImporter] Fail in _CreateKanbanView!";
        return hr;
    }

    return hr;
}

HRESULT Et2DbConverter::_ImportDataFromRange()
{
    HRESULT hr = S_OK;
    app_helper::KBatchUpdateCal buc(m_pDbBookOp);
    const IDBIds* fldIds = m_spDbSheetOp->GetAllFields();
    const IDBIds* recIds = m_spDbSheetOp->GetAllRecords();
    ICellImages* spImages = m_spEtApiWb->GetCellImages();
    ks_stdptr<_Worksheet> spEtWorksheet = m_spEtApiWb->GetWorksheets()->GetSheetItem(m_etSheetIdx);
	ISheet* pEtSheet = spEtWorksheet->GetSheet();
	if (!pEtSheet)
		return E_FAIL;
	ks_stdptr<IUnknown> spUnk;
	hr = pEtSheet->GetExtDataItem(edSheetHyperlinks, &spUnk);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IKHyperlinks> spHyperlinks = spUnk;

    DbSheet::DisableDbUpdateLastModifiedInfoScope scope;
    std::vector<ks_wstring> nameVec;
    nameVec.reserve(m_validCols.size());
    BMP_PTR pBMP = m_pEtBookOp->LeakBook()->GetBMP();
	int primaryCol = 0;
	size_t colCnt = m_validCols.size();
	if (colCnt > 0)
		primaryCol = m_validCols.front();
    Et2DbFieldCopyHelper stFieldCopyHelper(m_spEtApiWb, spEtWorksheet.get(), m_spDbApiWs.get(), m_ctx, m_etRange);
    stFieldCopyHelper.Init(m_filePath, m_requestFromApp, &m_defaultName);
    stFieldCopyHelper.SetIterRowsByIdx(&m_validRows);
    bool canSaveImg = m_bSupportImg && gs_callback && gs_callback->convertShapes && gs_callback->queryShapes;
	for (int i = 0, idxColNotEmpty = 0; i < colCnt; ++i)
    {
        const int col = m_validCols[i];
        EtDbId fldId = fldIds->IdAt(i);
        IDbFieldsManager *pFieldsMgr = m_spDbSheetOp->GetFieldsManager();
        ks_stdptr<IDbField> spField;
        pFieldsMgr->GetField(fldId, &spField);
        if(!spField)
            continue;

        // 如果 col 列为空，则跳过其类型推断和数据导出
        ASSERT(m_notEmptyCols.empty() || idxColNotEmpty < m_notEmptyCols.size());
        if (false == m_notEmptyCols.empty() && col != m_notEmptyCols[idxColNotEmpty])
        {
            _AssignName4UnnamedCol(spField.get(), col, nameVec);
            continue;
        }
        ++idxColNotEmpty;
        stFieldCopyHelper.BeginDeduceFieldType();
        for (;stFieldCopyHelper.Valid();stFieldCopyHelper.Next())
        {
            int row = stFieldCopyHelper.GetCurRow();
            if (_IsSkippedCell(row, col))
                continue;
            hr = stFieldCopyHelper.DeduceFieldType(col);
            if (hr == S_OK)
                break;
        }
        stFieldCopyHelper.EndDeduceFieldType();
		ET_DbSheet_FieldType fldType = ET_DbSheet_FieldType_Invalid;
		PCWSTR numFmt = nullptr;
		stFieldCopyHelper.GetTypeInfo(fldType, numFmt);
		if (col == primaryCol)
			stFieldCopyHelper.CorrectionForPrimaryField(fldType, numFmt);

		if (spField->GetType() != fldType)
            m_fieldTypeChange = true;
        // 设置格式
        ET_DbSheet_FieldType oldType = spField->GetType();
        spField->SetTypeForIO(fldType);
        if (m_bIgnoreChangeFieldType == false)
            spField->OnChangeFieldType(oldType, fldType, nullptr);
        if (numFmt)
            spField->SetNumberFormat(numFmt);
		bool isAttachmentType = fldType == Et_DbSheetField_Attachment;
        stFieldCopyHelper.CopyFieldExDetail(spField, col, m_bIgnoreChangeFieldType, m_bNeedOptimizeSelectField);
		_AssignName4UnnamedCol(spField.get(), col, nameVec);
		if (m_requestFromApp == false and (isAttachmentType && !canSaveImg))
			continue;
        stFieldCopyHelper.BeginCopyData(isAttachmentType, col);
        // 设值
        DbSheet::DbtBatchSubmitOptimizer batchSetValue(m_spDbSheetOp.get());
        for (int j = 0; stFieldCopyHelper.Valid(); stFieldCopyHelper.Next(), j++)
        {
            const int row = stFieldCopyHelper.GetCurRow();
            if (_IsSkippedCell(row, col))
                continue;
            EtDbId recId = recIds->IdAt(j);    
            stFieldCopyHelper.CopyCellData(spField, recId, fldId, col, isAttachmentType);
        }
        stFieldCopyHelper.EndCopyData(fldId);
	}

    // 批处理重命名
    if (false == m_colsNeedRename.empty())
    {
        const IDBIds *pFldIds = m_spDbSheetOp->GetAllFields();
        EtDbIdx fldCnt = pFldIds->Count();
        std::vector<EtDbId> fldVec;
        fldVec.reserve(fldCnt);
        for (EtDbIdx fidIdx = 0; fidIdx < fldCnt; ++fidIdx)
        {
            EtDbId fldId = pFldIds->IdAt(fidIdx);
            fldVec.push_back(fldId);
        }
        IDbFieldsManager *pFieldsMgr = m_spView->GetFieldsManager();
        std::vector<PCWSTR> namePtrs;
        namePtrs.reserve(nameVec.size());
        for(const auto& str: nameVec)
            namePtrs.push_back(str.c_str());
        VS(pFieldsMgr->BatchSetFieldsName(fldVec.data(), namePtrs.data(), m_validCols.size()));
    }

    return S_OK;
}

HRESULT Et2DbConverter::_InitDbSheetContent(_Worksheet* pDbWs, RANGE& rg)
{
    HRESULT hr = S_OK;

    ISheet* pDbSheet = pDbWs->GetSheet();
    m_spDbSheetOp.clear();
	VS(DbSheet::GetDBSheetOp(pDbSheet, &m_spDbSheetOp));

    int rowCnt = m_validRows.size(); // dbsheet 的数据区域.
    int colCnt = m_validCols.size();
    if (rowCnt == 0)
    {
        //非无记录模式初始时候,当range只有一行，为数据表新增一空行
        if(!m_spDbSheetOp->IsNoRecordsMode())
            rowCnt = 1; // 如果range只有一行，为数据表新增一空行
    }


    m_spDbSheetOp->InitRecords(rowCnt);

    const IDBIds* pFldIds = m_spDbSheetOp->GetAllFields();
    ks_stdptr<IDBSheetViews> spViews;
    VS(DbSheet::GetDBSheetViews(pDbSheet, &spViews));
    // 在查询应用更新的情况下,为了使原有的应用仍然有效，原本的dbsheet并没有被完全清空，而是保留了fields和views。这种情况下不用新增field和view。
    ks_stdptr<IDBSheetView> spView;
    spViews->GetLastView(&spView);
    if (pFldIds->Count() == 0 || !spView)//什么时候!spView会满足呢？例如 当基于et去创建应用时
    {
        ks_stdptr<IDBSheetRange> spDbRange;
        hr = m_spDbSheetOp->InitFields(colCnt, &spDbRange);
        if (FAILED(hr))
        {
            wo::IEtRevisionContext *pContext = _etcore_GetEtRevisionContext();
            if (pContext && !pContext->isExecDirect())
            {
                ks_bstr rangeText;
                CS_COMPILE_PARAM ccp(cpfNormal, 0, 0, 0);
                util::Range2FormulaText(m_pDbBookOp, rg, ccp, &rangeText);
                QString value(QStringLiteral("behaviour_et_convert_db_fail_sheetId_%1_rowCnt_%2_colCnt_%3_range_%4")
                    .arg(m_spEtSheet->GetStId()).arg(rowCnt).arg(colCnt)
                    .arg(QString::fromUtf16(rangeText.c_str())));
                pContext->collectInfo(value.toUtf8());
            }
            return hr;
        }
        if(m_spView)
            m_spView.clear();
        if (!spView)
        {
            KSheet_Book_Version bookVersion = KSheet_Book_Version_Base;
            ks_stdptr<IUnknown> spUnk;
            pDbSheet->LeakBook()->GetExtDataItem(edFileVersion, &spUnk);
            ks_stdptr<IKEtFileVersion> spEtFileVersion = spUnk;
            if (spEtFileVersion)
                bookVersion = spEtFileVersion->GetKSheetBookVersion();
            if (pDbSheet->GetBMP()->bKsheet && bookVersion >= KSheet_Book_Version_AppSheet)
            {
                m_spView = spViews->GetDefaultView();
            }
            else
            {
                hr = spViews->CreateView(et_DBSheetView_Grid, m_gridViewName, false, Et_DBSheetViewUse_ForDb, &m_spView);
                if (FAILED(hr))
                    return hr;
            }
        }
        else
        {
            m_spView = spView;
        }

        IDbFieldsManager *pFieldsMgr = m_spView->GetFieldsManager();
        const EtDbId* pIds = NULL;
        UINT32 cnt = 0;
        spDbRange->GetFieldIds(&pIds, &cnt);
        std::vector<ks_wstring> nameVec;
        m_colsNeedRename.clear();
        m_idxColsNeedRename = 0;
        for (int i = 0; i < colCnt; ++i)
        {
            EtDbId fldId = spDbRange->GetFieldId(i);
            ks_stdptr<IDbField> spField;
            VS(pFieldsMgr->GetField(fldId, &spField));
            // 设置字段类型
            ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
            VS(spField->SetType(type, m_spView));
			// todo: 自测et导出. 可能在规则上有漏考虑的部分
			_PreprocessFldName(rg.RowFrom(), m_validCols[i], nameVec);
        }
        // 设置字段名称
        {
            std::vector<PCWSTR> namePtrs;
            namePtrs.reserve(nameVec.size());
            for(const auto& str: nameVec)
                namePtrs.push_back(str.c_str());
            VS(pFieldsMgr->BatchSetFieldsName(pIds, namePtrs.data(), colCnt));
        }
    }
    else
    {
        if(m_spView)
            m_spView.clear();
        m_spView = spView;
    }

    return S_OK;
}

HRESULT Et2DbConverter::_InsertListObject(etoldapi::_Worksheet* pDbWs, int rowCnt, int colCnt)
{
    ISheet *pSheet = pDbWs->GetSheet();
    IDX iSheet = alg::STREF_INV_SHEET;
    HRESULT hr = pSheet->GetIndex(&iSheet);
    if (FAILED(hr))
        return hr;

    RANGE dbRANGE(pDbWs->GetWorkbook()->GetBook()->GetBMP());
    dbRANGE.SetSheetFromTo(iSheet, iSheet);
    dbRANGE.SetRowFromTo(0, rowCnt); /* dbsheet 额外有用于字段名的首行, 因此 RANGE 的行数需要 +1 */
    dbRANGE.SetColFromTo(0, colCnt - 1);
    ks_stdptr<etoldapi::Range> spDbApiRange;
    VS(pDbWs->GetRangeByData(&dbRANGE, &spDbApiRange));

    ks_stdptr<ListObjects> listObjects;
    pDbWs->get_ListObjects(&listObjects);
    KComVariant varSource(spDbApiRange);
    ks_stdptr<ListObject> spLstObj;
    hr = listObjects->Add(xlSrcRange, varSource, KComVariant(), xlYes, KComVariant(), KComVariant(), &spLstObj);
    if(FAILED(hr))
        return hr;
    // 设置表格样式为空
    hr = spLstObj->SetTableStyle(DB_DEFAULT_TABLE_STYLE);
    if (FAILED(hr))
        return hr;

    hr = spLstObj->put_ShowHeaders(VARIANT_FALSE);
    if (FAILED(hr))
        return hr;

    // 删除第一行，因为默认新建dbsheet的时候第一行默认是表名
    RANGE dltRg(dbRANGE);
    dltRg.SetRowFromTo(0);
    hr = m_pDbBookOp->RemoveRange(dltRg, dirUp);
    if (FAILED(hr))
        return hr;
    return hr;
}

HRESULT Et2DbConverter::getCellText(IKWorksheet* pWorksheet, int row, int col, BSTR* pBstrVal) const
{
	return GridSheet::GetCellText(m_spStringTools, pWorksheet, row, col, GridSheet::Fill, pBstrVal);
}

bool Et2DbConverter::_Valid() const
{
    return nullptr != m_spEtApiWb
        && nullptr != m_spDbApiWb
        && nullptr != m_ctx
        && false == m_defaultName.empty();
}

Et2DbExporter::Et2DbExporter(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, KEtRevisionContext* ctx, bool toEmptySheet, VarObj& defaultName
    , VarObj& kanbanCfg, IDX sheetIdx, bool isWholeBook)
    : Et2DbConverter(pEtWorkbook, pDbWorkbook, ctx, toEmptySheet, defaultName, true)
    , m_kanbanCfg(kanbanCfg)
    , m_isWholeBook(isWholeBook)
{
    if (m_spDbApiWb)
    {
        IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->BeginExportDbt(m_spDbApiWb->GetBook());
    }
    m_etSheetIdx = sheetIdx;
    m_bSupportImg = _kso_GetWoEtSettings()->IsEt2DbEnableSupportImages();
}

Et2DbExporter::~Et2DbExporter()
{
    if (m_spDbApiWb)
    {
        IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->EndExportDbt(m_spDbApiWb->GetBook());
    }
}

HRESULT Et2DbExporter::Exec()
{
    // 初始化检测
    if (false == _Valid())
        return E_FAIL;

    HRESULT hr = S_OK;
    // 导出优化. 导出后进行自适应的批处理.
    ExportOptimizer exportOptimizer(m_pDbBookOp);

    hr = DbSheet::copyDbUsersManager(m_spEtApiWb->GetBook(), m_spDbApiWb->GetBook());
    if (FAILED(hr))
        return hr;

    UINT etSheetCnt = m_isWholeBook ? util::getLastSheetIdx(m_spEtApiWb->GetBook()) + 1 : 1;
    UINT succeedSheets = 0;
    for (IDX sheetIdx = 0; sheetIdx < etSheetCnt; ++sheetIdx)
    {
        // 导出当前sheet
        if (!m_isWholeBook)
            sheetIdx = m_etSheetIdx;

		m_spEtSheet.clear();
        m_pEtBookOp->GetSheet(sheetIdx, &m_spEtSheet);
        if (!m_spEtSheet)
            continue;

		SHEETSTATE stState = ssVisible;
		m_spEtSheet->GetVisible(&stState);
		if (stState == ssVeryhidden || !IsSupport())
			continue;

        if (!m_isFirstDbSheet)
        {
            // 整表导出时,第一个sheet为数据表
            if (m_isWholeBook && sheetIdx == 0 && m_spEtSheet->IsDbSheet())
                m_isFirstDbSheet = true;
            // 单表导出时,sheet为数据表
            if (!m_isWholeBook && m_spEtSheet->IsDbSheet())
                m_isFirstDbSheet = true;
        }

        m_spStringTools->SetEnv(m_spEtSheet);
        hr = ExportOneSheet(sheetIdx, succeedSheets);
		if (FAILED(hr))
			return hr;

        // 和导入保持一直,上限50
        if (succeedSheets >= 50)
        {
            m_errorStatus.ToggleSheetCntLimit(true);
            break;
        }
    }
    if (false == m_errorStatus.SucceedSheets())
        return E_FAIL;

    if (m_isFirstDbSheet)
    {
        hr = DeleteFirstDbSheet();
        if (FAILED(hr))
            return hr;
    }

    // 一期赶发版暂时不做
    return DelayCopyDbSheets();
}

HRESULT Et2DbExporter::_GetValidRange(ISheet* pEtSheet, RANGE& rg)
{
    ks_stdptr<IRowColOp> pRowColOp;
    VS(pEtSheet->GetCurUserOperator(&pRowColOp));

    // 缺省将range的第一行当作列头, 后续行当作数据; 存在"空列头"的情况下, 将全部range当作数据.
    int rowBeg = rg.RowFrom() + 1, rowLast = rg.RowTo();
    int seekFrom = rg.RowFrom(); // 列判空时的判定起始行, 总是从rg的第一行开始

    if (false == m_validRows.empty())
        m_validRows.clear();
    m_validRows.reserve(std::min(rowLast - rowBeg + 1, static_cast<int>(maxRowCnt)));
    // 不导出隐藏行(包括筛选结果, 右键设置行隐藏及调整行高导致的隐藏结果)
    for (int row = rowBeg; row <= rowLast; ++row)
    {
        if (FALSE == pRowColOp->GetRowHidden(row))
            m_validRows.push_back(row);
        if (m_validRows.size() >= maxRowCnt)
        {
            m_errorStatus.ToggleRowCntLimit(true);
            break;
        }
    }

    int colBeg = rg.ColFrom(), colLast = rg.ColTo();
    if (false == m_validCols.empty())
        m_validCols.clear();
    // 分析并记录非空列. 后续可跳过对空列的类型推断
    m_notEmptyCols.clear();
    m_notEmptyCols.reserve(colLast - colBeg + 1);
    // 可以跳过有列宽信息, 但没有内容的列
    for (int col = colBeg; col <= colLast; ++col)
        if (RCB_NONE_LT != pEtSheet->SeekNextCellInColumn(seekFrom, col))
            m_notEmptyCols.push_back(col);

    // 导出单表内筛选区域时，不导出隐藏列
    for (int col = colBeg; col <= colLast; ++col)
        if (FALSE == pRowColOp->GetColHidden(col))
            m_validCols.push_back(col);

    // 更新 rg 的范围
    if (false == m_validRows.empty())
        rg.SetRowTo(m_validRows.back());
    if (false == m_validCols.empty())
        rg.SetColTo(m_validCols.back());
    
    return S_OK;
}

HRESULT Et2DbExporter::_InitializeDestDbSheet()
{
    return prepareDestDbWorksheet(false);
}

void Et2DbExporter::_AssignName4UnnamedCol(IDbField* pField, int col, std::vector<ks_wstring>& nameVec)
{
    // todo: ET筛选区域导出, 也根据列类型重命名空列头
    return;
}

void Et2DbExporter::_PreprocessFldName(int row, int col, std::vector<ks_wstring>& nameVec)
{
    ks_bstr fldName;
	m_spStringTools->GetCellText(m_spEtSheet, row, col, &fldName, nullptr, -1, nullptr);
    ks_wstring nameStr(fldName.c_str());
    if (nameStr.empty())
    {
        nameStr = m_defaultFieldName;
    }
    nameVec.emplace_back(std::move(nameStr));
}

HRESULT Et2DbExporter::prepareDestDbWorksheet(bool)
{
    // 获取 DBT 的 worksheet. 新建的表格文档总是自带一张表, 无需新建一张.
    ks_castptr<IKWorkbook> spDbIKWb(m_spDbApiWb);
    ks_stdptr<_Worksheet> spDbWs;
    ks_stdptr<IKWorksheets> spKSheets = spDbIKWb->GetWorksheets();
    ks_stdptr<Worksheets> spApiSheets = spKSheets;
    if (nullptr == spApiSheets)
        return E_FAIL;
    if (false == m_2EmptySheet)
    {
        KComVariant vBefore;
        KComVariant vAfter = spKSheets->GetSheetCount();

        KComVariant vCount = 1;
        KComVariant vType(xlWorksheet);
        ks_stdptr<IKCoreObject> spObj;
        spApiSheets->Add(vBefore, vAfter, vCount, vType ,&spObj, stGrid_DB);
        spDbWs = spObj;
        spDbWs->GetSheet()->GetIndex(&m_dbSheetIdx);
    }
    else
    {
        spDbWs = spDbIKWb->GetWorksheets()->GetSheetItem(m_dbSheetIdx);
    }
    if (nullptr == spDbWs) {
        ASSERT(!"Get _Worksheet from IKWorksheets::GetSheetItem failed!");
        WOLOG_WARN << "Get _Worksheet from IKWorksheets::GetSheetItem failed: sheetFrom:" << m_dbSheetIdx;
        return E_FAIL;
    }
    m_spDbApiWs = spDbWs;

    int rowCnt = m_validRows.size(); // dbsheet 的数据区域.
    int colCnt = m_validCols.size();
    if (colCnt != 0)
    {
        // 插入list object
        HRESULT hr = _InsertListObject(spDbWs, rowCnt, colCnt);
        if (FAILED(hr))
        {
            WOLOG_ERROR << "[Et2DbImporter] Fail in _InsertListObject!";
            return hr;
        }
    }
    // 在外部显式指定 sheet 的类型
    m_spDbApiWs->GetSheet()->UpdateSheetType(stGrid_DB);
    // 设置dbsheet的名称和描述. db对sheet名的限制不强于et, 因此名称总是可用的, 且导出场景中不需要担心重名问题
    PCWSTR pcwsName = NULL;
    m_spEtSheet->GetName(&pcwsName);
    ks_bstr bstrSheetName(pcwsName);
    VS(m_spDbApiWs->put_Name(bstrSheetName));
    return S_OK;
}

HRESULT Et2DbExporter::_CreateKanbanView(ISheet* pDbSheet, int defaultTwip)
{
    // todo 一期赶发版，暂时不创建看板视图
    return S_OK;

    // 创建看板视图
    ks_stdptr<IDBSheetViews> spViews;
	VS(DbSheet::GetDBSheetViews(pDbSheet, &spViews));
    // todo: 目前使用的 m_kanbanCfg 是自己构造的, 供新建看板视图时创建一个单选项字段. 这个参数之后需要前端给. default_name里增加参数
    {{
        bool isAddField = false;

        ks_stdptr<IDBSheetRange> spRange;
        DbSheet::AddNecessarySelectField(spViews.get(), &spRange, isAddField, m_kanbanCfg);
        if (isAddField)
            m_spView->SetFieldsHidden(spRange->GetFieldId(0), true);
    }}
    ks_stdptr<IDBSheetView> spKanbanView;
    HRESULT hr = spViews->CreateView(et_DBSheetView_Kanban, m_kanbanViewName, false, Et_DBSheetViewUse_ForDb, &spKanbanView);
    if (FAILED(hr))
        return hr;

    // 看板视图仅显示前三个字段
    const IDBIds* fields = spKanbanView->GetAllFields();
    int fieldCnt = fields->Count();
    if (fieldCnt > 3)
    {
        for (int i = 3; i < fieldCnt; ++i)
        {
            spKanbanView->SetFieldsHidden(fields->IdAt(i), TRUE);
        }
    }
    return hr;
}

bool Et2DbExporter::_IsSkippedCell(int row, int col)
{
    return false;
}

HRESULT Et2DbExporter::ExportOneSheet(IDX sheetIdx, UINT& succeedSheets)
{
    m_validRows.clear();
    m_validCols.clear();
    HRESULT hr = S_OK;
    
    // sheet中存在禁止查看区域，则不允许导出
    bool isProtect = false;
    {
        et_sptr<KProtectionContext> upNewProtectionCtx(new KProtectionContext(m_spEtApiWb.get(), m_spEtApiWb->GetWoObject(), m_ctx));
        std::unique_ptr<KEtRevisionContext::ProtectionCtxExchanger> upExchanger(m_ctx->generateExchanger(upNewProtectionCtx));

        // 验证对象数量及所有权
        ASSERT(nullptr == upNewProtectionCtx);
        ASSERT(upExchanger);

        if (upExchanger)
        {
            RANGE rg(m_spEtSheet->GetBMP());
            rg.SetSheets(sheetIdx, sheetIdx);
            isProtect = m_ctx->getProtectionCtx()->isRangeHasHidden(rg); // 查看整表是否存在禁止查看区域
        }
    }
    if (isProtect)
    {
        m_errorStatus.ToggleProtectedTable(true);
        return S_OK;
    }
    m_etSheetIdx = sheetIdx;

    RECT rect;
    hr = m_spEtSheet->CalcUsedScale(&rect);
    if (FAILED(hr))
    {
        m_errorStatus.ToggleInvalidSheet(true);
        return S_OK;
    }

    // 若rect为空
    bool isEmptySheet = false;
    if (0 == rect.bottom && 0 == rect.left && 0 == rect.right && 0 == rect.top)
    {
        // rect为 {0,0,0,0} 不代表sheet全空, 也可能仅在A1单元格有值, 因此要追加判定
        ks_bstr bstrVal;
        VS(getCellText(m_spEtApiWb->GetWorksheets()->GetSheetItem(m_etSheetIdx), 0, 0, &bstrVal));
        isEmptySheet = bstrVal.empty();
    }

    if (m_spEtSheet->IsDbSheet())
    {
        hr = CopySheet();
    }
    else if (m_spEtSheet->IsGridSheet())
    {
        // 空表创建一张空白表
        if (isEmptySheet)
        {
            hr = CreateBlankDbSheet();
            if (sheetIdx == 0)
                m_isFirstDbSheet = true;
        }
        else
        {
            RANGE rg(m_spEtSheet->GetBMP());
            rg.SetSheetFromTo(sheetIdx);
            rg.SetRowFromTo(rect.top, rect.bottom);
            rg.SetColFromTo(rect.left, rect.right);
            m_etRange = rg;

            hr = ExportSheet();
        }
    }

    // 导出失败，标记
    if (FAILED(hr))
    {
        m_errorStatus.ToggleInvalidSheet(true);
        return S_OK;
    }

    // 导出成功
    m_errorStatus.ToggleSucceedSheets(true);
    ++succeedSheets;
    m_2EmptySheet = false;
    return S_OK;
}

HRESULT Et2DbExporter::CopySheet()
{
    IKWorksheets* pSrcWorksheets = m_spEtApiWb->GetWorksheets();
	IKWorksheets* pDstWorksheets = m_spDbApiWb->GetWorksheets();
	if (!pSrcWorksheets || !pDstWorksheets)
		return E_FAIL;
	ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = pSrcWorksheets->GetSheetItem(m_etSheetIdx);
	ISheet* pSrcSheet = spSrcWorksheet->GetSheet();
	IDX idx = util::getLastSheetIdx(m_spDbApiWb->GetBook());
	ks_stdptr<etoldapi::_Worksheet> spDstLastSheet = pDstWorksheets->GetSheetItem(idx);

	KComVariant emptyVar;
	KComVariant vAfter(spDstLastSheet);
	ks_stdptr<IKCoreObject> spNewSheetObject;
	HRESULT hr = S_OK;
	if (pSrcSheet->IsDbSheet())
    {
		hr = spSrcWorksheet->CopyDbSheet(emptyVar, vAfter, true, &spNewSheetObject);
        if (FAILED(hr))
            return hr;
    }

	ks_stdptr<etoldapi::_Worksheet> spDstWorksheet = spNewSheetObject;
    if (m_needClearFormat)
	{
		hr = ClearAllDataFormat(spDstWorksheet);
		if (FAILED(hr))
			return hr;
	}
	m_sheetIdMap[pSrcSheet->GetStId()] = spDstWorksheet->GetSheet()->GetStId();
	m_delayedCopyDbSheets.emplace_back(spSrcWorksheet, spDstWorksheet);
	return S_OK;
}

HRESULT Et2DbExporter::ExportSheet()
{
    HRESULT hr = _GetValidRange(m_spEtSheet.get(), m_etRange);
    if (FAILED(hr))
        return hr;
    
    hr = _InitializeDestDbSheet();
    if (FAILED(hr))
        return hr;

    return _ImportRangeFromSheet();
}

HRESULT Et2DbExporter::DelayCopyDbSheets()
{
    HRESULT hr = S_OK;
    CopyDBSheetDealCrossSheetDependenceHelper DealCrossSheetDependenceHelper;
    for(auto&& pair : m_delayedCopyDbSheets)
    {
        ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = pair.first;
        ks_stdptr<etoldapi::_Worksheet> spDstWorksheet = pair.second;

        // todo attachmentId需要从前端传入
        GlobalSharedStringHashMap<GlobalSharedString> attachmentIdMap;
        attachmentIdMap[GlobalSharedString((PCWSTR)nullptr)] = GlobalSharedString((PCWSTR)nullptr);
		CopyDBSheetHelper copyHelper(spSrcWorksheet, spDstWorksheet, &DealCrossSheetDependenceHelper);
		DbSheetCopyParam copyParam;
		copyParam.needResetAutoValue = false;
		copyParam.resetModifiedInfoByCurUser = true;
		copyParam.pStIdMap = &m_sheetIdMap;
		copyParam.pAttachmentIdMap = &attachmentIdMap;
        copyParam.copyDefaultView = true;
		hr = copyHelper.Init(copyParam);
		if (FAILED(hr))
			return hr;
		hr = copyHelper.ExecCopy();
		if (FAILED(hr))
            return hr;
    }
    DealCrossSheetDependenceHelper.DealCrossSheetDependence(&m_sheetIdMap);
    return S_OK;
}

HRESULT Et2DbExporter::DeleteFirstDbSheet()
{
    app_helper::KBatchUpdateCal buc(m_pDbBookOp);

    WebID sheetObjId = 0;
    if (const AbsObject *obj = m_ctx->getSheetMain(0))
    {
        sheetObjId = obj->objId();
        AbsKeepRepertory* keepRepertory = m_ctx->getDocument()->getKeepRepertory();
        keepRepertory->onCloseMainObject(m_ctx->getSheetMain(0));
    }
    IKWorksheet* pWorkSheet = m_spDbApiWb->GetWorksheets()->GetSheetItem(0);

    HRESULT hr = pWorkSheet->DeleteDirectly();
    if (SUCCEEDED(hr) && sheetObjId > 0)
        m_ctx->onSheetDelete(sheetObjId);

    return S_OK;
}

HRESULT Et2DbExporter::ClearAllDataFormat(etoldapi::_Worksheet* pWorkSheet)
{
    ISheet* pSheet = pWorkSheet->GetSheet();
    IDX sheetIdx = 0;
    HRESULT hr = pSheet->GetIndex(&sheetIdx);
    if (FAILED(hr))
        return hr;

	RANGE rg(pSheet->GetBMP());
    rg.SetSheets(sheetIdx, sheetIdx);
    ks_stdptr<etoldapi::Range> spCellRange;
    VS(pWorkSheet->GetRangeByData(&rg, &spCellRange));
    ks_stdptr<etoldapi::Range> host = spCellRange;
    hr = host->ClearFormats();
    if (FAILED(hr))
        return hr;
    return S_OK;
}

HRESULT Et2DbExporter::CreateBlankDbSheet()
{
    // 必须有已定义的 blankDbSheetCfg
    if (false == m_defaultName.has("blankDbSheetCfg"))
        return E_FAIL;

    m_2EmptySheet = false;
    HRESULT hr = prepareDestDbWorksheet(true);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IUnknown> spUnknown;
	VS(m_spDbApiWb->GetBook()->GetExtDataItem(edDBUserGroups, &spUnknown));
	if(!spUnknown)
		return E_FAIL;

	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	if (spDBUserGroups)
		spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if(!spProtectionJudgement)
		return E_FAIL;

    VarObj blankDbSheetCfg = m_defaultName.get("blankDbSheetCfg");
    hr = DbSheet::initDbSheet(blankDbSheetCfg, m_spDbApiWs.get(), spProtectionJudgement.get());
    return hr;
}

bool Et2DbExporter::IsSupport()
{
    SHEETTYPE stType = stUnknown;
	VS(m_spEtSheet->GetFullType(&stType));
    bool isSupport = true;
    // todo 目前仅支持工作表、数据表，其它sheet均不支持
    switch (stType)
	{
		case stGrid:
		case stGrid_DB:
			break;
		case stFlexPaper:
		case stDialog:
		case stChart:
		case stOldDashBoard:
		default:
            isSupport = false;
			break;
	}

    if (m_isWholeBook || stType != stGrid_DB)
        return isSupport;

    // 单表，若含有引用、单向关联、双向关联字段，导出后打不开（引用其它数据表）
    ks_stdptr<IDBSheetOp> spData;
    VS(m_spEtSheet->GetExtDataItem(edSheetDbOp, (IUnknown**)&spData));
    if (!spData)
        return false;

    IDbFieldsManager* pFieldsMgr = spData->GetFieldsManager();
    const IDBIds* pFields = spData->GetAllFields();
    UINT curSheetStId = m_spEtSheet->GetStId();
    for (EtDbIdx fld = 0; fld < pFields->Count(); ++fld)
    {
        EtDbId fldId = pFields->IdAt(fld);
        ks_stdptr<IDbField> spField;
        HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
        if (FAILED(hr))
            return false;

        ET_DbSheet_FieldType type = spField->GetType();
        switch (type)
        {
            case Et_DbSheetField_Lookup:
            {
                ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
                if (nullptr == spFieldLookup)
                    return false;
                if (curSheetStId != spFieldLookup->GetLookupSheetId())
                    return false;
            }
            case Et_DbSheetField_Link:
            case Et_DbSheetField_OneWayLink: // 单向关联未开发，与双向关联共用一份
            {
                ks_stdptr<IDbField_Link> spFieldLink = spField;
                if (nullptr == spFieldLink)
                    return false;
                if (curSheetStId != spFieldLink->GetLinkSheet())
                    return false;
            }
            default:
                break;
        }
    }
    return isSupport;
}

Et2DbImporter::Et2DbImporter(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, KEtRevisionContext* ctx, bool toEmptySheet, VarObj& defaultName
    , VarObj& xlsxImport, IDBProtectionJudgement* pProtectionJudgement, bool bIgnoreChangeFieldType)
    : Et2DbConverter(pEtWorkbook, pDbWorkbook, ctx, toEmptySheet, defaultName, bIgnoreChangeFieldType)
    , m_xlsxImport(xlsxImport)
    , m_hasEmptyNameRow(false)
    , m_pProtectionJudgement(pProtectionJudgement)
    , m_pDbWorkbook(pDbWorkbook)
    , m_needClearFormat(false)
{
    m_bSupportImg = true;
}

Et2DbImporter::~Et2DbImporter()
{

}

HRESULT Et2DbImporter::Exec()
{
    // 初始化检测
    if (false == _Valid() || m_xlsxImport.empty() || nullptr == m_pProtectionJudgement)
    {
        WOLOG_ERROR << "[Et2DbImporter] Improper initial parameters!";
        return E_FAIL;
    }
    HRESULT hr = S_OK;
    // 导出优化. 导出后进行自适应的批处理.
    ExportOptimizer exportOptimizer(m_pDbBookOp);

    // todo: sheetsCfg 留待二期预览功能启用后从前端获取
    int etSheetCnt = util::getLastSheetIdx(m_spEtApiWb->GetBook()) + 1;
    int etDstSheetOriCnt = util::getLastSheetIdx(m_spDbApiWb->GetBook()) + 1;
    hr = DbSheet::copyDbUsersManager(m_spEtApiWb->GetBook(), m_spDbApiWb->GetBook());
    if (FAILED(hr))
        return hr;
	IKWorksheets* pSrcWorksheets = m_spEtApiWb->GetWorksheets();
	IKWorksheets* pDstWorksheets = m_spDbApiWb->GetWorksheets();
	if (!pSrcWorksheets || !pDstWorksheets)
		return E_FAIL;
    ks_stdptr<etoldapi::Worksheets> spSrcWorksheets;
    m_spEtApiWb->get_Worksheets(&spSrcWorksheets);
    ks_stdptr<etoldapi::Worksheets> spDstWorksheets;
    m_spDbApiWb->get_Worksheets(&spDstWorksheets);
    int tarSheetCount = 0;
    if (spSrcWorksheets && spDstWorksheets)
    {   
        const std::unordered_set<UINT>& importSheetIds = GetImportSheets();
        tarSheetCount = spDstWorksheets->GetSheetCount();
        DBSheetRenameHelper::RenameConflictDBSheetName(spDstWorksheets, spSrcWorksheets, importSheetIds);
    }
	int remainFpSheetCnt = DbSheet::GetDBFpsheetLimit() - pDstWorksheets->GetSheetCount(stFlexPaper, TRUE);
    for (int i = 0, succeedSheets = 0; i < etSheetCnt; ++i)
    {
		ISheet* pSrcSheet = pSrcWorksheets->GetSheetItem(i)->GetSheet();
		SHEETSTATE stState = ssVisible;
		pSrcSheet->GetVisible(&stState);
		if (stState == ssVeryhidden)
			continue;

		if (!CheckSheetImport(pSrcSheet->GetStId()))
			continue;

		if (pSrcSheet->IsFpSheet())
		{
			--remainFpSheetCnt;
			if (remainFpSheetCnt < 0)
				continue;
		}
		hr = _ExecOneSheet(i, true, succeedSheets);
		if (FAILED(hr))
			return hr;

        if (succeedSheets >= 50)
        {
            m_errorStatus.ToggleSheetCntLimit(true);
            break;
        }
    }
    if (false == m_errorStatus.SucceedSheets())
        return E_FAIL;

    hr = _DelayCopyDbSheets();
    if (spDstWorksheets)
    {
        int count = spDstWorksheets->GetSheetCount();
        DBSheetRenameHelper::ForceRenameDBSheets(spDstWorksheets, etDstSheetOriCnt, count - 1);
    }
    return hr;
}

namespace {
    bool lessRect(const RECT& lhs, const RECT& rhs) {
        return lhs.left < rhs.left;
    };
}

HRESULT Et2DbImporter::_DelayCopyDbSheets()
{
	HRESULT hr = S_OK;
    CopyDBSheetDealCrossSheetDependenceHelper DealCrossSheetDependenceHelper;
    for(auto&& pair : m_delayedCopyDbSheets)
    {
        ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = pair.first;
        ks_stdptr<etoldapi::_Worksheet> spDstWorksheet = pair.second;

        DbSheet::DisableDbSheetProtectScope disPtScope(m_pProtectionJudgement, spDstWorksheet->GetSheet()->GetStId());

		// copy sheet
		CopyDBSheetHelper copyHelper(spSrcWorksheet, spDstWorksheet, &DealCrossSheetDependenceHelper);
		DbSheetCopyParam copyParam;
		copyParam.needResetAutoValue = false;
		copyParam.resetModifiedInfoByCurUser = true;
		copyParam.pStIdMap = &m_sheetIdMap;
		copyParam.pAttachmentIdMap = m_pAttachmentIdMap;
        copyParam.copyDefaultView = true;
		hr = copyHelper.Init(copyParam);
		if (FAILED(hr))
			return hr;
		hr = copyHelper.ExecCopy();
		if (FAILED(hr))
            return hr;
    }
    DealCrossSheetDependenceHelper.DealCrossSheetDependence(&m_sheetIdMap);
    return S_OK;
}

HRESULT Et2DbImporter::_ClearAllDataFormat(etoldapi::_Worksheet* pWorkSheet)
{
    ISheet* pSheet = pWorkSheet->GetSheet();
    BMP_PTR pBMP = pSheet->GetBMP();
	RANGE etRANGE(pBMP);
    IDX sheetIDX = 0;
    HRESULT hr = pSheet->GetIndex(&sheetIDX);
    if (FAILED(hr))
        return hr;
    etRANGE.SetSheets(sheetIDX, sheetIDX);
    ks_stdptr<etoldapi::Range> spEtApiCellRange;
    VS(pWorkSheet->GetRangeByData(&etRANGE, &spEtApiCellRange));
    ks_stdptr<etoldapi::Range> host = spEtApiCellRange;
    hr = host->ClearFormats();
    if (FAILED(hr))
        return hr;
    return S_OK;
}

HRESULT Et2DbImporter::copySheet()
{
	IKWorksheets* pSrcWorksheets = m_spEtApiWb->GetWorksheets();
	IKWorksheets* pDstWorksheets = m_spDbApiWb->GetWorksheets();
	if (!pSrcWorksheets || !pDstWorksheets)
		return E_FAIL;
	ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = pSrcWorksheets->GetSheetItem(m_etSheetIdx);
	ISheet* pSrcSheet = spSrcWorksheet->GetSheet();;
	IDX idx = util::getLastSheetIdx(m_spDbApiWb->GetBook());
	ks_stdptr<etoldapi::_Worksheet> spDstLastSheet = pDstWorksheets->GetSheetItem(idx);

	KComVariant emptyVar;
	KComVariant vAfter(spDstLastSheet);
	ks_stdptr<IKCoreObject> spNewSheetObject;
	HRESULT hr = S_OK;
	if (pSrcSheet->IsDbSheet())
		hr = spSrcWorksheet->CopyDbSheet(emptyVar, vAfter, true, &spNewSheetObject);
	else
		hr = spSrcWorksheet->Copy(emptyVar, vAfter, &spNewSheetObject);
	if (FAILED(hr))
		return hr;
	ks_stdptr<etoldapi::_Worksheet> spDstWorksheet = spNewSheetObject;
	if (pSrcSheet->IsFpSheet())
	{
		CopyDBFpSheetHelper copyHelper;
		hr = copyHelper.Init(spSrcWorksheet, spDstWorksheet);
		if (FAILED(hr))
			return hr;
		copyHelper.SetAttachmentIdMap(m_pAttachmentIdMap);
		return copyHelper.ExecCopy();
	}
	ASSERT(pSrcSheet->IsDbSheet());
	if (m_needClearFormat)
	{
		hr = _ClearAllDataFormat(spDstWorksheet);
		if (FAILED(hr))
			return hr;
	}
	m_sheetIdMap[pSrcSheet->GetStId()] = spDstWorksheet->GetSheet()->GetStId();
	m_delayedCopyDbSheets.emplace_back(spSrcWorksheet, spDstWorksheet);
	return S_OK;
}

HRESULT Et2DbImporter::_ImportSheet()
{
    HRESULT hr = S_OK;

    // db进程导入et时，会有一些区域考虑跳过处理（目前仅有数据透视表）
    m_skippedRects.clear();

    ks_stdptr<pivot_core::IPivotTableHost> spTableHost;
    VS(m_spEtSheet->GetExtDataItem(edSheetPivotTablesHost, (IUnknown**)&spTableHost));
    ASSERT(spTableHost);
    ks_stdptr<pivot_core::IPivotTables> spPivotTables = spTableHost->GetPivotTables();
    ASSERT(spPivotTables);
    UINT pvtTabcnt = spPivotTables->Count();
    for (UINT i = 0; i < pvtTabcnt; i++)
    {
        ks_stdptr<pivot_core::IPivotTable> spPvtTable = spPivotTables->Item(i);
        ks_stdptr<pivot_core::IPivotTableStyleArea> spPvtTblStyleArea;
        if (SUCCEEDED(spPvtTable->GetTableStyleArea(&spPvtTblStyleArea)))
        {
            RECT rect;
            if (SUCCEEDED(spPvtTblStyleArea->GetBody(false, &rect)))
                m_skippedRects.push_back(rect);
            std::vector<RECT> rects;
            if (SUCCEEDED(spPvtTblStyleArea->GetReportFilter(rects)))
                m_skippedRects.insert(m_skippedRects.end(), rects.begin(), rects.end());
        }
    }
    // todo: 进行优化. 类型推断的分析顺序是逐列的, 因此可以将 m_skippedRects 的元素拆分成 x行1列 的矩形
    std::sort(m_skippedRects.begin(), m_skippedRects.end(), lessRect);

    hr = _GetValidRange(m_spEtSheet.get(), m_etRange);
    if (FAILED(hr))
        return hr;
    
    hr = _InitializeDestDbSheet();
    if (FAILED(hr))
        return hr;

    // 在db进程中, 创建视图, SetValue, SetFieldWidth等操作需要临时关闭权限. 外部已进行过权限的检查
    DbSheet::DisableDbSheetProtectScope disPtScope(m_pProtectionJudgement, m_spDbApiWs->GetSheet()->GetStId());
    SetNeedOptimizeField(!m_hasEmptyNameRow);
    return _ImportRangeFromSheet();
}

HRESULT Et2DbImporter::_CreateBlankDbSheet()
{
    // 要求创建空白数据表时, m_xlsxImport 必须有已定义的 blankDbSheetCfg
    if (false == m_xlsxImport.has("blankDbSheetCfg"))
        return E_FAIL;

    HRESULT hr = prepareDestDbWorksheet(true);
    if (FAILED(hr))
        return hr;

    VarObj blankDbSheetCfg = m_xlsxImport.get("blankDbSheetCfg");
    hr = DbSheet::initDbSheet(blankDbSheetCfg, m_spDbApiWs.get(), m_pProtectionJudgement);

    return S_OK;
}

HRESULT Et2DbImporter::_GetValidRange(ISheet* pEtSheet, RANGE& rg)
{
    ks_stdptr<IRowColOp> pRowColOp;
    VS(pEtSheet->GetCurUserOperator(&pRowColOp));

    // 缺省将range的第一行当作列头, 后续行当作数据; 存在"空列头"的情况下, 将全部range当作数据.
    int rowBeg = m_hasEmptyNameRow ? rg.RowFrom() : rg.RowFrom() + 1, rowLast = rg.RowTo();
    int seekFrom = rg.RowFrom(); // 列判空时的判定起始行, 总是从rg的第一行开始

    if (false == m_validRows.empty())
        m_validRows.clear();
    // 进行文件导入时, 分析非空行. 仅有格式等信息但单元格无数据的行预期跳过
    class NotEmptyRows : public ICellValueAcpt
    {
    public:
        NotEmptyRows() {}
        ~NotEmptyRows() {};
        STDPROC_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
        {
            if (nullptr == pToken)
                return FALSE;
            m_empty = false;
            return TRUE; // 停止枚举
        }
        bool emptyRow() const { return m_empty; }
        void reset() { m_empty = true; }
    private:
        bool m_empty = true;
    };
    {
        RANGE rowRange(rg);
        NotEmptyRows notEmptyRows{};
        et_sdptr<ISheetEnum> pSheetEnum;
        pEtSheet->CreateEnum(&pSheetEnum);
        int row = rowLast;
        for ( ; row >= rowBeg; --row)
        {
            rowRange.SetRowFromTo(row);
            pSheetEnum->EnumCellValueRowbyRow(rowRange, &notEmptyRows);
            if (false == notEmptyRows.emptyRow())
            {
                rowLast = row;
                rg.SetRowTo(rowLast);
                break;
            }
        }
        row = -1;
        // 一期跳过顶部的空白行, 二期允许指定起始行之后, 再取消顶部空白行的跳过
        if (m_hasEmptyNameRow)
        {
            row = rowBeg;
        }
        else
        {
            // 判定列头行是否为空，如果非空则不跳过顶部的空白行
            if (RCB_NONE_LT == pEtSheet->SeekNextCellInRow(rg.RowFrom(), rg.ColFrom(), FALSE))
                row = rowBeg;
        }
        if (-1 != row)
        {
            notEmptyRows.reset();
            for ( ; row <= rowLast; ++row)
            {
                rowRange.SetRowFromTo(row);
                pSheetEnum->EnumCellValueRowbyRow(rowRange, &notEmptyRows);
                if (false == notEmptyRows.emptyRow())
                {
                    ASSERT(rowBeg <= (m_hasEmptyNameRow ? rg.RowFrom() : rg.RowFrom() + 1));
                    rg.SetRowFrom(row);
                    rowBeg = m_hasEmptyNameRow ? rg.RowFrom() : rg.RowFrom() + 1;
                    seekFrom = rg.RowFrom(); // 列判空时的判定起始行, 总是从rg的第一行开始
                    break;
                }
            }
        }
    }
    m_validRows.reserve(std::min(rowLast - rowBeg + 1, static_cast<int>(maxRowCnt)));
    // 不导出隐藏行(包括筛选结果, 右键设置行隐藏及调整行高导致的隐藏结果)
    for (int row = rowBeg; row <= rowLast; ++row)
    {
        if (FALSE == pRowColOp->GetRowHidden(row))
            m_validRows.push_back(row);
        if (m_validRows.size() >= maxRowCnt)
        {
            m_errorStatus.ToggleRowCntLimit(true);
            break;
        }
    }
    // 由于et最大行数1048576远大于允许导入的行数(50000), 可能发生数据截断, 因此获取导入行后, 再重置区域行范围, 并进行第二次非空行的排除
    if (false == m_validRows.empty())
    {
        rowLast = m_validRows.back();
        rg.SetRowTo(rowLast);

        RANGE rowRange(rg);
        NotEmptyRows notEmptyRows{};
        et_sdptr<ISheetEnum> pSheetEnum;
        pEtSheet->CreateEnum(&pSheetEnum);
        for (int row = rowLast; row >= rowBeg; --row)
        {
            rowRange.SetRowFromTo(row);
            pSheetEnum->EnumCellValueRowbyRow(rowRange, &notEmptyRows);
            if (false == notEmptyRows.emptyRow())
            {
                rowLast = row;
                rg.SetRowTo(rowLast);
                int pos = std::lower_bound(m_validRows.begin(), m_validRows.end(), rowLast) - m_validRows.begin();
                if (pos + 1 < m_validRows.size())
                    m_validRows.resize(pos + 1);
                break;
            }
        }
    }

    int colBeg = rg.ColFrom(), colLast = rg.ColTo();
    if (false == m_validCols.empty())
        m_validCols.clear();
    // 分析并记录非空列. 后续可跳过对空列的类型推断. 进一步地, 从文件导入时, 跳过前后的空列
    m_notEmptyCols.clear();
    m_notEmptyCols.reserve(colLast - colBeg + 1);
    // 可以跳过有列宽信息, 但没有内容的列
    for (int col = colBeg; col <= colLast; ++col)
        if (RCB_NONE_LT != pEtSheet->SeekNextCellInColumn(seekFrom, col))
            m_notEmptyCols.push_back(col);
    if (false == m_notEmptyCols.empty())
    {
        colBeg = m_notEmptyCols.front();
        colLast = m_notEmptyCols.back();
    }
    m_validCols.reserve(colLast - colBeg + 1);

    // db导入et文件时，导出隐藏列
    for (int col = colBeg; col <= colLast; ++col)
        m_validCols.push_back(col);

    // 更新 rg 的范围
    if (false == m_validRows.empty())
        rg.SetRowTo(m_validRows.back());
    if (false == m_validCols.empty())
        rg.SetColTo(m_validCols.back());
    
    return S_OK;
}

HRESULT Et2DbImporter::_InitializeDestDbSheet()
{
    if (m_dbSheetIdx == INVALIDIDX)
        return prepareDestDbWorksheet(false);
    else
        return prepareDestDbWorksheet(true);
}

void Et2DbImporter::_AssignName4UnnamedCol(IDbField* pField, int col, std::vector<ks_wstring>& nameVec)
{
    PCWSTR colName = pField->GetName();
    // 对空的列头使用类型推断赋予列名
    if (false == m_colsNeedRename.empty() && m_idxColsNeedRename < m_colsNeedRename.size() && col == m_colsNeedRename[m_idxColsNeedRename])
    {
        PCWSTR typeStr = nullptr;
        VS(_appcore_GainEncodeDecoder()->EncodeFieldType(pField->GetType(), &typeStr));
        QString qStr(QString::fromUtf16(typeStr));
        const char* key = qStr.toUtf8().data(); // 生命周期
        if (m_defaultName.has(key) && VarObjFieldValidation::expectString(m_defaultName, key))
        {
            colName = m_defaultName.field_str(key);
        }
        else
        {
            // 若前端未给[列类型-列名]的参数, 则用列类型的枚举作为名称. et导出的情形尚未设计 _AssignName4UnnamedCol 的实现
            colName = typeStr;
        }
        ++m_idxColsNeedRename;
    }
    nameVec.push_back(colName);
}

void Et2DbImporter::_PreprocessFldName(int row, int col, std::vector<ks_wstring>& nameVec)
{
    ks_bstr fldName;
	if (!m_hasEmptyNameRow && !_IsSkippedCell(row, col))
		getCellText(m_spEtApiWb->GetWorksheets()->GetSheetItem(m_etSheetIdx), row, col, &fldName);
    ks_wstring nameStr(fldName.c_str());
    if (nameStr.empty())
    {
        nameStr = m_defaultFieldName;
        m_colsNeedRename.push_back(col); // 记录空单元格所在列, 留待导入完成后使用类型推断命名
    }
    nameVec.emplace_back(std::move(nameStr));
}

HRESULT Et2DbImporter::prepareDestDbWorksheet(bool bJustBlankSheet)
{
    // 获取 DBT 的 worksheet. 新建的表格文档总是自带一张表, 无需新建一张.
    ks_castptr<IKWorkbook> spDbIKWb(m_spDbApiWb);
    ks_stdptr<_Worksheet> spDbWs;
    ks_stdptr<IKWorksheets> spKSheets = spDbIKWb->GetWorksheets();
    ks_stdptr<Worksheets> spSheets = spKSheets;

    if (nullptr == spSheets)
        return E_FAIL;
    if (false == m_2EmptySheet)
    {

        KComVariant vBefore;
        KComVariant vAfter = util::getLastSheetIdx(spDbIKWb->GetBook()) + 1; // 供 oldapi::Worksheets使用，其计数从1开始

        KComVariant vCount = 1;
        KComVariant vType(xlWorksheet);
        ks_stdptr<IKCoreObject> spObj;
        spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid_DB);
        spDbWs = spObj;
    }
    else
    {
        spDbWs = spDbIKWb->GetWorksheets()->GetSheetItem(m_dbSheetIdx);
    }
    if (nullptr == spDbWs) {
        ASSERT(!"Get _Worksheet from IKWorksheets::GetSheetItem failed!");
        WOLOG_ERROR << "[Et2DbImporter] Get _Worksheet from IKWorksheets::GetSheetItem failed: sheetFrom:" << m_dbSheetIdx;
        return E_FAIL;
    }
    m_spDbApiWs = spDbWs;
	ISheet* pDbSheet = m_spDbApiWs->GetSheet();

    int rowCnt = m_validRows.size(); // dbsheet 的数据区域.
    int colCnt = m_validCols.size();
	if (rowCnt == 0)
		rowCnt = 1; // 如果range只有一行，为数据表新增一空行
    HRESULT hr = S_OK;
    if (colCnt != 0)
    {
        // 插入list object
        hr = _InsertListObject(spDbWs, rowCnt, colCnt);
        if (FAILED(hr))
        {
            WOLOG_ERROR << "[Et2DbImporter] Fail in _InsertListObject!";
            return hr;
        }
    }

    // 在外部显式指定 sheet 的类型
    pDbSheet->UpdateSheetType(stGrid_DB);
    ks_bstr bstrSheetName;
	PCWSTR pcwsName = nullptr;
	m_spEtSheet->GetName(&pcwsName);
	hr = GetValidSheetName(spSheets, m_spDbApiWs, pcwsName, &bstrSheetName);
    if (FAILED(hr))
        return hr;
    VS(m_spDbApiWs->put_Name(bstrSheetName)); // 重名时虽然会自动增加序号, 但并不符合dbsheet的预期。去重后，用 VS 预期不重名

    return S_OK;
}

HRESULT Et2DbImporter::_CreateKanbanView(ISheet* pDbSheet, int defaultTwip)
{
    return S_OK;
}

bool Et2DbImporter::_IsSkippedCell(int row, int col)
{
    if (m_skippedRects.empty())
        return false;

    RECT cell = {col, row, col, row};
    auto itEnd = std::upper_bound(m_skippedRects.begin(), m_skippedRects.end(), cell, lessRect); // it can be end() iterator
    for (auto it = m_skippedRects.begin(); it != itEnd; ++it)
    {
        const RECT& tableRect = *it;
        if (col <= tableRect.right && row >= tableRect.top && row <= tableRect.bottom)
            return true;
    }
    return false;
}

HRESULT Et2DbImporter::_ExecOneSheet(IN int sheetIdx, IN bool bAllowCreateBlankSheet, OUT int& succeedSheets)
{
	m_validRows.clear();
	m_validCols.clear();

    // VarObj sheetCfg = sheetsCfg.at_s(i);
    // VAR_OBJ_EXPECT_INTEGRAL(sheetCfg, "firstRow")
    // VAR_OBJ_EXPECT_INTEGRAL(sheetCfg, "sheetIdx")
    // // 前端指定0-100的firstRow, 它对应表格中显示的行号, 但在内核中行的索引是行号-1. 前端的sheetIdx则是从零开始的
    // int firstRow = sheetCfg.field_int32("firstRow") - 1, sheetIdx = sheetCfg.field_int32("sheetIdx");

    int firstRow = 1 - 1;

    if (-1 == firstRow)
    {
        firstRow = 0;
        m_hasEmptyNameRow = true;
    }
    else
    {
        m_hasEmptyNameRow = false;
    }
    m_spEtSheet.clear();
    HRESULT hr = m_pEtBookOp->GetSheet(sheetIdx, &m_spEtSheet);
    if (FAILED(hr))
        return hr;

    SHEETSTATE ssVisible;
    m_spEtSheet->GetVisible(&ssVisible);
    if (ssVisible == ssVeryhidden)
        return S_OK;
	BMP_PTR pSrcBmp = m_spEtSheet->GetBMP();
    // db导入时，如果整个表内存在禁止查看区域，则该表不予导入
    bool isProtect = false;
    {
        et_sptr<KProtectionContext> upNewProtectionCtx(new KProtectionContext(m_spEtApiWb.get(), m_spEtApiWb->GetWoObject(), m_ctx));
        std::unique_ptr<KEtRevisionContext::ProtectionCtxExchanger> upExchanger(m_ctx->generateExchanger(upNewProtectionCtx));

        // 验证对象数量及所有权
        ASSERT(nullptr == upNewProtectionCtx);
        ASSERT(upExchanger);
#ifdef _DEBUG
        et_sptr<KProtectionContext> upNewProtectionCtx2(new KProtectionContext(m_spEtApiWb.get(), m_spEtApiWb->GetWoObject(), m_ctx));
        std::unique_ptr<KEtRevisionContext::ProtectionCtxExchanger> upExchanger2(m_ctx->generateExchanger(upNewProtectionCtx));
        ASSERT(upNewProtectionCtx2);
        ASSERT(nullptr == upExchanger2);
#endif

        if (upExchanger)
        {
            RANGE rg(pSrcBmp);
            rg.SetSheets(sheetIdx, sheetIdx);
            isProtect = m_ctx->getProtectionCtx()->isRangeHasHidden(rg); // 查看整表是否存在禁止查看区域
        }
    }
    if (isProtect)
    {
        m_errorStatus.ToggleProtectedTable(true);
        return S_OK;
    }
	m_spStringTools->SetEnv(m_spEtSheet);

    m_etSheetIdx = sheetIdx;
    RECT rect;
    hr = m_spEtSheet->CalcUsedScale(&rect);
    if (FAILED(hr))
    {
        // 存在无法导出的sheet, 标记, 并跳过当前表
        m_errorStatus.ToggleInvalidSheet(true);
        return S_OK;
    }

    // 若rect为空, 或表类型为dashboard, chart, dialog等，则创建一个空白的 dbsheet
    bool createBlankSheet = (0 == rect.bottom && 0 == rect.left && 0 == rect.right && 0 == rect.top);
    if (createBlankSheet)
    {
        // rect为 {0,0,0,0} 不代表sheet全空, 也可能仅在A1单元格有值, 因此要追加判定
        ks_bstr bstrVal;
		VS(getCellText(m_spEtApiWb->GetWorksheets()->GetSheetItem(m_etSheetIdx), 0, 0, &bstrVal));
        createBlankSheet = bstrVal.empty();
    }
	SHEETTYPE stType = stUnknown;
	VS(m_spEtSheet->GetFullType(&stType));
	// db 导入 as 时只导入工作表、数据表和说明页面
	bool isDbImportAs = pSrcBmp->bKsheet;
	bool bSkip = false;
	switch (stType)
	{
		case stGrid:
		case stGrid_DB:
		case stFlexPaper:
			break;
		case stDialog:
		case stChart:
		case stOldDashBoard:
			createBlankSheet = true;
			if (isDbImportAs)
				bSkip = true;
			break;
		case stDashBoard:
			bSkip = true;
			break;
		default:
			if (isDbImportAs)
				bSkip = true;
			break;
	}
	if (bSkip)
		return S_OK;
	if (m_spEtSheet->IsDbSheet() || m_spEtSheet->IsFpSheet())
	{
		hr = copySheet();
	}
	else if (createBlankSheet)
    {
        if(!bAllowCreateBlankSheet)
        {
            //若当前不允许创建空表 标记, 并跳过当前表
            m_errorStatus.ToggleBanCreateBlankSheet(true);
            return S_OK;
        }
        hr = _CreateBlankDbSheet();
    }
    else
    {
        int val1 = firstRow, val2 = rect.bottom;
        // 如果用户指定的起始行已经超出有数据的区域范围, 则为用户导出一个空行
        if (val2 < val1)
            val2 = val1;
        RANGE rg(pSrcBmp);
        rg.SetSheetFromTo(sheetIdx);
        rg.SetRowFromTo(val1, val2);
        rg.SetColFromTo(rect.left, rect.right);
        COL contentRgLeftBorder = -1, contentRgRightBorder = -1;
        int titleCnt = 0;
        auto errCode = TableStructRecHelper::calcTitleCnt(m_spEtSheet, rg, false, contentRgLeftBorder, contentRgRightBorder, titleCnt);
        if (errCode == TableStructRecHelper::TableStructRecSuccessed)
            rg.SetRowFrom(titleCnt - 1);
        m_etRange = rg;

        hr = _ImportSheet();
    }
    if (FAILED(hr))
    {
        // 存在无法导出的sheet, 标记, 并跳过当前表
        m_errorStatus.ToggleInvalidSheet(true);
        return S_OK;
    }
    m_2EmptySheet = false; // 成功导入任意一张表之后, 将继续在已有文件上追加导入, 因此 m_2EmptySheet 总需要置false
    m_errorStatus.ToggleSucceedSheets(true);
    ++succeedSheets;

    return S_OK;
}

Et2DbImporterSameProcess::Et2DbImporterSameProcess(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, int iEtSheetIdx, int iDbSheetIdx, KEtRevisionContext* ctx, bool toEmptySheet, VarObj& defaultName
        , VarObj& xlsxImport, IDBProtectionJudgement* pProtectionJudgement, bool bAllowCreateBlankSheet, bool bIgnoreChangeFieldType)
        : Et2DbImporter(pEtWorkbook, pDbWorkbook, ctx, toEmptySheet, defaultName, xlsxImport, pProtectionJudgement, bIgnoreChangeFieldType)
        , m_iEtSheetIdx(iEtSheetIdx)
{
    m_dbSheetIdx = iDbSheetIdx;
    m_bAllowCreateBlankSheet = bAllowCreateBlankSheet;
}

HRESULT Et2DbImporterSameProcess::Exec()
{
    int succeedSheets = 0;
    HRESULT hr = _ExecOneSheet(m_iEtSheetIdx, m_bAllowCreateBlankSheet, succeedSheets);
    if (FAILED(hr))
        return hr;

    if (false == m_errorStatus.SucceedSheets())
        return E_FAIL;
    return S_OK;
}

PCWSTR Et2DbImporterSameProcess::GetErrName() const
{
	return m_errorStatus.GetErrName();
}

void Et2DbImporterSameProcess::setRequestIsFromApp() noexcept
{
    m_requestFromApp = true;
}

bool isValidType(ET_DbSheet_FieldType type)
{
	switch (type)
	{
		case Et_DbSheetField_Address:
		case Et_DbSheetField_Attachment:
		case Et_DbSheetField_CellPicture:
		case Et_DbSheetField_Link:
		case Et_DbSheetField_Lookup:
		case Et_DbSheetField_Note:
		case Et_DbSheetField_SingleLineText:
		// 自动字段暂时不支持
		case Et_DbSheetField_Automations:
		case Et_DbSheetField_AutoNumber:
        case Et_DbSheetField_Button:
		case Et_DbSheetField_CreatedBy:
		case Et_DbSheetField_CreatedTime:
		case Et_DbSheetField_Formula:
        case Et_DbSheetField_ParentRecord:
		case Et_DbSheetField_LastModifiedBy:
		case Et_DbSheetField_LastModifiedTime:
			return false;
		default:
			break;
	}
	return true;
}

bool CreateDbSheetFromEtWithSpecifyInfo::Param::isValid(BMP_PTR bmp) const
{
	if (!pFieldNameVec || !pFieldTypeVec)
		return false;
	size_t fldCnt = pFieldNameVec->size();
	if (fldCnt != pFieldTypeVec->size() || fldCnt == 0)
		return false;
	if (etSheetIdx == INVALIDIDX)
		return false;
	return IsRowValid(rowFrom, bmp) && IsColValid(colFrom, bmp) && IsColValid(colFrom + fldCnt - 1, bmp);
}

CreateDbSheetFromEtWithSpecifyInfo::CreateDbSheetFromEtWithSpecifyInfo(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, KEtRevisionContext* pCtx, IDBProtectionJudgement* pProtectionJudgement)
	: m_pEtWorkbook(pEtWorkbook)
	, m_pDbWorkbook(pDbWorkbook)
	, m_pCtx(pCtx)
	, m_pProtectionJudgement(pProtectionJudgement)
{
	VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
}

HRESULT CreateDbSheetFromEtWithSpecifyInfo::Init(const Param& param)
{
	if (!m_pEtWorkbook || !m_pDbWorkbook || !m_pCtx || !m_pProtectionJudgement)
		return E_FAIL;
	IBook* pSrcBook = m_pEtWorkbook->GetBook();
	if (!pSrcBook)
		return E_FAIL;
	if (!param.isValid(pSrcBook->GetBMP()))
		return E_INVALIDARG;
	m_param = param;
	HRESULT hr = gainSrcWorksheet();
	if (FAILED(hr))
		return hr;
	return S_OK;
}

HRESULT CreateDbSheetFromEtWithSpecifyInfo::Exec()
{
	HRESULT hr = exec();
	if (FAILED(hr))
	{
		rollback();
		return hr;
	}
	m_errorStatus.ToggleSucceedSheets(true);
	return S_OK;
}

PCWSTR CreateDbSheetFromEtWithSpecifyInfo::GetErrName() const
{
	return m_errorStatus.GetErrName();
}

HRESULT CreateDbSheetFromEtWithSpecifyInfo::gainSrcWorksheet()
{
	IKWorksheets* pWorksheets = m_pEtWorkbook->GetWorksheets();
	if (!pWorksheets)
		return E_FAIL;
	m_pSrcWorksheet = pWorksheets->GetSheetItem(m_param.etSheetIdx);
	if (!m_pSrcWorksheet)
		return E_KSHEET_SHEET_NOT_FOUND;
	m_spStringTools->SetEnv(m_pSrcWorksheet->GetSheet());
	return S_OK;
}

HRESULT CreateDbSheetFromEtWithSpecifyInfo::gainTarWorksheet()
{
	KComVariant vBefore;
	int idx = util::getLastSheetIdx(m_pDbWorkbook->GetBook()) + 1;
	KComVariant vAfter(idx);
	KComVariant vCount(1);
	KComVariant vType(xlWorksheet);
	ks_stdptr<etoldapi::Worksheets> spTarWorksheets = m_pDbWorkbook->GetWorksheets();
	ks_stdptr<IKCoreObject> spObj;
	HRESULT hr = spTarWorksheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid_DB);
	if (FAILED(hr))
		return hr;
	ks_stdptr<etoldapi::_Worksheet> spTarWorksheet = spObj;
	m_pNewWorksheet = spTarWorksheet;
	Database::SheetInitConfig config;
	UINT nRow = 1;
	UINT nCol = m_param.pFieldNameVec->size();
	hr = DbSheet::ConfigureNewSheet(spTarWorksheet, m_pProtectionJudgement, nRow, nCol, config);
	if (FAILED(hr))
		return hr;
	return S_OK;
}

void CreateDbSheetFromEtWithSpecifyInfo::rollback()
{
	if (m_pNewWorksheet)
		m_pNewWorksheet->DeleteDirectly();
}

HRESULT CreateDbSheetFromEtWithSpecifyInfo::exec()
{
	HRESULT hr = gainTarWorksheet();
	if (FAILED(hr))
		return hr;
	ISheet* pSheet = m_pNewWorksheet->GetSheet();
	ks_stdptr<IDBSheetOp> spTarDBSheetOp;
	hr = DbSheet::GetDBSheetOp(pSheet, &spTarDBSheetOp);
	if (FAILED(hr))
		return hr;
	app_helper::KBatchUpdateCal buc(m_pDbWorkbook->GetBook()->LeakOperator());
	DbSheet::DisableDbTrackHistoryScope historyScope;
	DbSheet::DisableDbUpdateLastModifiedInfoScope scope;
	DbSheet::DisableDbSheetProtectScope disPtScope(m_pProtectionJudgement, pSheet->GetStId());
	IDbFieldsManager* pFieldsMgr = spTarDBSheetOp->GetFieldsManager();
	const IDBIds* pFields = spTarDBSheetOp->GetAllFields();
	const IDBIds* pRecords = spTarDBSheetOp->GetAllRecords();
	UINT nCol = pFields->Count();
	std::vector<EtDbId> fldVec;
	fldVec.reserve(nCol);
	std::vector<PCWSTR> nameStrVec;
	nameStrVec.reserve(nCol);
	for (int i = 0; i < nCol; ++i)
	{
		ks_stdptr<IDbField> spField;
		EtDbId fldId = pFields->IdAt(i);
		hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;
		ET_DbSheet_FieldType type = m_param.pFieldTypeVec->at(i);
		if (!isValidType(type))
			return E_INVALIDARG;
		hr = spField->SetTypeForIO(type);
		if (FAILED(hr))
			return hr;
		fldVec.emplace_back(fldId);
		nameStrVec.emplace_back(m_param.pFieldNameVec->at(i));
	}
	// 批处理重命名
	hr = pFieldsMgr->BatchSetFieldsName(fldVec.data(), nameStrVec.data(), nCol);
	if (FAILED(hr))
		return hr;
	ISheet* pSrcSheet = m_pSrcWorksheet->GetSheet();
	ROW rowBegin = std::max(pSrcSheet->GetTop(), m_param.rowFrom);
	ROW rowEnd = pSrcSheet->GetBottom();
	COL colBegin = m_param.colFrom;
	if (rowEnd < rowBegin)
		return S_OK;
	int newRecordCnt = rowEnd - rowBegin + 1;
	if (newRecordCnt > 1)
	{
		hr = spTarDBSheetOp->InsertRecords(newRecordCnt - 1, nullptr);
		if (FAILED(hr))
			return hr;
	}
	ICellImages* pCellImages = m_pEtWorkbook->GetCellImages();
	ks_stdptr<IUnknown> spUnk;
	hr = pSrcSheet->GetExtDataItem(edSheetHyperlinks, &spUnk);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IKHyperlinks> spHyperlinks = spUnk;
	for (int col = 0; col < nCol; ++col)
	{
		EtDbId fldId = fldVec[col];
		ks_stdptr<IDbField> spField;
		pFieldsMgr->GetField(fldId, &spField);
		ET_DbSheet_FieldType fldType = spField->GetType();
		bool needAddSelectItem = fldType == Et_DbSheetField_MultipleSelect || fldType == Et_DbSheetField_SingleSelect;
		bool needSetUrl = fldType == Et_DbSheetField_Email || fldType == Et_DbSheetField_Url;
		COL srcCol = col + colBegin;
        // <file ID, <row indices, file source>>
        std::unordered_map<PCWSTR, std::pair<std::vector<int>, const PCWSTR>, util::StrHasher, util::StrEqual> attachments;
		for (int row = 0; row < newRecordCnt; ++row)
		{
			ROW srcRow = row + rowBegin;

            // 图片数据特殊处理
            if (spField->GetType() == Et_DbSheetField_Attachment and
                    pCellImages->IsImgCell(m_param.etSheetIdx, srcRow, srcCol))
            {
                processImage(srcRow, srcCol, pCellImages, fldId, attachments);
                continue;
            }

			ks_bstr bstrVal;
			GridSheet::GetCellText(m_spStringTools, m_pSrcWorksheet, srcRow, srcCol, GridSheet::Fill, &bstrVal);
			if (bstrVal.empty())
				continue;
			PCWSTR val = bstrVal.c_str();
			EtDbId recId = pRecords->IdAt(row);
			// 允许失败
			if (needAddSelectItem)
			{
				ks_stdptr<IDbField_Select> spFieldSelect = spField;
				hr = spFieldSelect->AppendItem(val, DBSelectField::Colors.at(spFieldSelect->Count() % DBSelectField::ColorsCnt));
				if (FAILED(hr) && hr != E_DBSHEET_SELECT_ITEM_CONFLICT)
					continue;
			}
			else if (needSetUrl)
			{
				ks_stdptr<IKHyperlink> spHyperlink;
				spHyperlinks->HasHyperLink(srcRow, srcCol, &spHyperlink);
				ks_bstr address;
				if (spHyperlink)
					spHyperlink->GetAddress(&address);
				if (fldType == Et_DbSheetField_Url)
				{
					etexec::managed_token_assist hyperlinkToken;
					IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
					PCWSTR hyperlink = address.empty() ? val : address.c_str();
					pCtx->Text2DbHyperlinkToken(hyperlink, hyperlink, &hyperlinkToken);
					VS(spTarDBSheetOp->SetTokenValue(recId, fldId, hyperlinkToken));
					continue;
				}
				else
				{
					hr = spTarDBSheetOp->SetHyperlinkAddress(recId, fldId, address.empty() ? val : address.c_str());
					if (FAILED(hr))
						continue;
				}
			}
			hr = spTarDBSheetOp->SetValue(recId, fldId, val);
			if (FAILED(hr))
				VS(spTarDBSheetOp->SetTokenValue(recId, fldId, nullptr));
		}
	}
	return S_OK;
}

void CreateDbSheetFromEtWithSpecifyInfo::processImage(int row, int col, ICellImages* pCellImages, EtDbId fldId,
        std::unordered_map<PCWSTR, std::pair<std::vector<int>, const PCWSTR>, util::StrHasher, util::StrEqual>& attachments)
{
    constexpr std::size_t queryBatchSize = 20;
    const_token_ptr pToken {};
    ks_stdptr<IBookOp> spBookOp;
    m_pEtWorkbook->GetBook()->GetOperator(&spBookOp);
    VS(GridSheet::GetCellToken(spBookOp, m_param.etSheetIdx, row, col, GridSheet::Fill, &pToken));
    alg::const_vstr_token_assist assist(pToken);
    drawing::AbstractShape* pShape = pCellImages->GetImgCellAtom(m_param.etSheetIdx, row, col);
    IKBlipAtom* pBlipAtom = PictureUploadHelper::GetBlipAtom(pShape);
    if (PictureUploadHelper::IsAttachment(pBlipAtom, pShape->picture().blip().linkMode()))
    {
        ks_bstr imageURL;
        pBlipAtom->GetLinkPath(&imageURL);
        PCWSTR source {};
        PCWSTR fileID = util::getFileId(imageURL.c_str(), &source);
        if (source == nullptr or fileID == nullptr)
            return;
        auto pos {attachments.find(fileID)};        // 下面操控了 pos 持有的数据, 必须是 iterator
        if (pos == attachments.end())       // 不需要改成 cend, 编译器优化之后是一样的效果
        {
            attachments.emplace(fileID, std::decay_t<decltype(pos->second)>{{row}, source});
            if (attachments.size() == 20)
            {
                // 向服务端请求附件数据
                binary_wo::BinWriter binaryWriter;
                // 在服务端处理的时候 userId 也会转成 connId, 所以优先用 connId
                const PCWSTR connectionID = m_pCtx->getUser()->connID();
                if (xstrcmp(__X("fake_connection_id"), connectionID) == 0)
                    binaryWriter.addStringField(m_pCtx->getUser()->userID(), "userId");
                else
                    binaryWriter.addStringField(connectionID, "connId");
                binaryWriter.beginArray("data");
                for (const auto& attachment : attachments)
                {
                    binaryWriter.beginStruct();
                    binaryWriter.addStringField(attachment.first, "key");
                    binaryWriter.addStringField(attachment.second.second, "source");
                    binaryWriter.endStruct();
                }
                binaryWriter.endArray();
                binary_wo::BinWriter::StreamHolder streamHolder = binaryWriter.buildStream();
                const WebSlice input {streamHolder.get(), binaryWriter.writeLength()};
                WebSlice output {};
                gs_callback->queryShapes(&input, &output);

                // 处理请求得到的数据
                binary_wo::BinReader binaryReader(output.data, output.size);
                const auto root {binaryReader.buildRoot()};
                const auto cast_result {root.cast()};
                const auto data {cast_result.get_s("data")};
                const int length = data.arrayLength_s();
                for (int i = 0; i < length; ++i)
                {
                    binary_wo::VarObj item = data.at(i);
                    if(item.field_int32("status") not_eq 0)
                        return;
                    PCWSTR key = item.field_str("key");
                    if (key == nullptr)
                        return;
                    ImgInfo imageInformation {
                        .fileId = item.field_str("mapKey"),
                        .source = item.field_str("source"),
                        .type = item.field_str("type"),
                        .name = item.field_str("name"),
                        .width = item.field_uint32("width"),
                        .height = item.field_uint32("height"),
                        .size = item.field_uint32("size")
                    };
                    ks_stdptr<IDbTokenArrayHandle> spTokenArray;
                    if (FAILED(GetAttachmentHandle(imageInformation, &spTokenArray)))
                        return;
                    alg::managed_handle_token_assist assist;
                    assist.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);

                    // 设置和该附件有关的所有单元格的值
                    ISheet* pNewSheet = m_pNewWorksheet->GetSheet();
                    ks_stdptr<IDBSheetOp> spNewBookOp;
                    VS(DbSheet::GetDBSheetOp(pNewSheet, &spNewBookOp));
                    for (const int rowIndex : pos->second.first)
                    {
                        VS(spNewBookOp->SetTokenValue(rowIndex, fldId, assist));
                    }
                }
                // 本批数据处理完成
                attachments.clear();
            }
        }
        else
        {
            pos->second.first.emplace_back(row);
        }
    }
}

} // namespace wo
