﻿#ifndef __WEBET_UTIL_H__
#define __WEBET_UTIL_H__

#include "wo/et_revision_context.h"
#include "wo_calltime.h"
#include "alg/service/parallel/synchrolock.h"
#include <random>
#include "appcore/et_appcore_dbsheet.h"
#include "webbase/memo_stat_common.h"

extern bool g_isCanBreak;
typedef void (*CollectInfoFunc)(const char *actionName, const WebSlice *data);

class DisableCalcBreak
{
public:
	DisableCalcBreak()
	{
		m_oldCanBread = g_isCanBreak;
		g_isCanBreak = false;
	}

	~DisableCalcBreak()
	{
		g_isCanBreak = m_oldCanBread;
	}

private:
	DisableCalcBreak(const DisableCalcBreak&) = delete;
	DisableCalcBreak& operator=(const DisableCalcBreak&) = delete;

	bool m_oldCanBread = true;
};

namespace wo
{
class KEtWorkbook;
class KEtWorkbooks;
class KEtRevisionContext;
enum class KU32MetricItem;
class KEtApplication;
class KDbValueSerializeHelper;

namespace util
{

bool IsEnableRelease(PCWSTR connId);
class GuardImportrangeCalc;
constexpr int kDefCmdTimeThreshold = 100;
constexpr int kDefSlowCallThreshold = 100;
namespace
{

bool BanImportrangeCalcMixedGuard()
{
    static int b = 2;
    if (b == 2)
    {
        char* str = ::getenv("ET_BAN_IMPORTRANGE_MIXED_GUARD");
        if (str && strncmp(str, "true", 5) == 0)
            b = 1;
        else
            b = 0;
        WOLOG_INFO << "[ET importrange mixed guard]: bool: " << b << ", ET_BAN_IMPORTRANGE_MIXED_GUARD: " << str;
    }
    return b;
}
}

class CalcBatchUpdate
{
public:
	explicit CalcBatchUpdate(IBookOp* pBookOp, bool specialForImportRange = false);

	~CalcBatchUpdate()
	{
		if (m_spBookOp)
		{
			m_spBookOp->EndBatchUpdate();
		}
	}
    
private:
	CalcBatchUpdate(const CalcBatchUpdate&) = delete;
	CalcBatchUpdate& operator=(const CalcBatchUpdate&) = delete;

	IBookOp* m_spBookOp = nullptr;
    // 按设计意图, MarkNeedCalcImportrange(false)需要在batch update之后调用. 
    // 成员 m_upImportrangeCalcGUard 的释放晚于 ~CalcBatchUpdate() 的调用, 符合预期
    std::unique_ptr<GuardImportrangeCalc> m_upImportrangeCalcGUard;
};

// 这个类的析构函数预期在一次 BatchUpdate 计算完成后才调用
// 作用域内, 计算不可中断, 计算引擎内标记importrange公式的计算性质为"需要计算". 截止到提交代码的时刻, 
// 这个计算性质只在非事务的场景会被读取 (如导出, 打开, 存盘等)

// 外部预期不单独使用这个类, 通过 CalcBatchUpdate 使用. 但class只能管控所在作用域, 外部通过其他方式调用
// 计算引擎的 BatchUpdate, 仍然会导致实际发生计算时, 计算中断, 计算性质等不符合预期

// todo: 这个类内部的计算不允许中断. 行为上相比之前没有更坏, 以前importrange会阻止中断
// 但之后可以考虑是否可能优化, 把粒度精确到importrange本身 (公式及溢出区可以有下游公式, 
// 因此不一定可以简单地修改)
class GuardImportrangeCalc
{
public:
    explicit GuardImportrangeCalc(IBookOp* pBookOp)
    {
        if (pBookOp)
        {
            m_spBookOp = pBookOp;
            m_spBookOp->MarkNeedCalcImportrange(TRUE);

            m_oldCanBread = g_isCanBreak;
            g_isCanBreak = false;
        }
    }

    ~GuardImportrangeCalc()
    {
        if (m_spBookOp)
        {
            m_spBookOp->MarkNeedCalcImportrange(FALSE);

            g_isCanBreak = m_oldCanBread;
        }
    }

private:
    GuardImportrangeCalc(const GuardImportrangeCalc&) = delete;
    GuardImportrangeCalc& operator=(const GuardImportrangeCalc&) = delete;

    IBookOp* m_spBookOp = nullptr;
    bool m_oldCanBread = true;
};

using wo::CallTimeStat;
class SlowCallTimeStat
{
    using SlowCallCallback = std::function<void(unsigned int)>;
public:
    SlowCallTimeStat(const char* tag, unsigned int threshold, SlowCallCallback callback = nullptr)
        : m_tag(tag)
        , m_threshold(threshold)
        , m_begin(std::chrono::steady_clock::now())
        , m_callback(callback)
    {
        ASSERT(m_tag != nullptr);
    }

    virtual ~SlowCallTimeStat()
    {
        end();
    }
    
    void restart(const char * tag, SlowCallCallback callback = nullptr)
    {
        end();
        m_begin = std::chrono::steady_clock::now();
        m_callback = callback;
        m_tag = tag;
        ASSERT(m_tag != nullptr);
    }

protected:
    void end() 
    {
        unsigned int ms = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
        if (ms > m_threshold)
            WOLOG_WARN << '[' << m_tag << ']' << " slow call detected, elapsed: " << ms << " ms";
        if (m_callback)
            m_callback(ms);
    }
protected:
    const char* m_tag = nullptr;
    unsigned int m_threshold;
    std::chrono::steady_clock::time_point m_begin;
    SlowCallCallback m_callback;
};

class SlowCollectCallTimeStat
{
public:
    SlowCollectCallTimeStat(
        const char* tag, 
        unsigned int threshold, 
        KEtWorkbook * wb,
        const WCHAR * collectName);

    ~SlowCollectCallTimeStat();

private:
    const char* m_tag = nullptr;
    unsigned int m_threshold;
    KEtWorkbook * m_wb;
    const WCHAR * m_collectName;
    std::chrono::steady_clock::time_point m_begin;
};

class SlowCallTimeMetrics: public SlowCallTimeStat
{
public:
    SlowCallTimeMetrics(
        const char* tag,  
        KEtWorkbook * wb,
        KU32MetricItem mcItem,
        unsigned int threshold = util::kDefSlowCallThreshold);
    
    void restart(const char * tag, KEtWorkbook * wb, KU32MetricItem mcItem);
};

class RandDist
{
public:
    RandDist(unsigned int seed, double probability);
    bool is();
    double GetProbability();
private:
    std::default_random_engine m_gen;
    std::bernoulli_distribution m_dist;
};

class CmdTimeMemStat
{
    using CmdCallback = std::function<void(unsigned int ms, unsigned int memUsedKb, unsigned int memKb)>;
public:
    explicit CmdTimeMemStat(KEtWorkbook * wb, KEtRevisionContext* pCtx, CmdCallback callback = nullptr);
    void start(PCWSTR cmdName, bool isExec);
    ~CmdTimeMemStat();

private:
	CmdTimeMemStat(const CmdTimeMemStat&) = delete;
	CmdTimeMemStat& operator=(const CmdTimeMemStat&) = delete;

    QString makeCmdName(const char* prefixName);

    QString m_cmdName;
    KEtRevisionContext* m_pCtx;
    KEtWorkbook * m_wb;
    INT64 m_memBegin;
    CmdCallback m_callback;
    std::chrono::steady_clock::time_point m_begin;
    bool m_isExec;
};

class CmdTimeStat
{
    using CmdCallback = std::function<void(unsigned int ms)>;
public:
    explicit CmdTimeStat(KEtWorkbook * wb, KEtRevisionContext* pCtx, CmdCallback callback = nullptr);
    void start(PCWSTR cmdName, bool isExec) { m_memStat.start(cmdName, isExec); }
    
private:
	CmdTimeStat(const CmdTimeStat&) = delete;
	CmdTimeStat& operator=(const CmdTimeStat&) = delete;

private:
    CmdTimeMemStat m_memStat;
};

class CFTimeStat
{
public:
    CFTimeStat(KEtWorkbook * wb, PCWSTR name);
    ~CFTimeStat();
    void setStatCount(int cellCnt, int travelCnt, int calcCnt, int calcOverThresholdCnt);

private:
    QString makeName();

    KEtWorkbook * m_wb;
    QString m_name;
    std::chrono::steady_clock::time_point m_begin;
    int m_cellCnt;
    int m_travelCnt;
    int m_calcCnt;
    int m_calcOverThresholdCnt;
};

class BreakTimeStat
{
public:
    BreakTimeStat();

    void restart();
    void onSetBreak();
    unsigned int takeElapseMS();

private:
    std::chrono::steady_clock::time_point m_begin;
    ThreadLiteLib::SpinlockExp m_lock;
    bool m_isStart;
    bool m_isBreak;
};

void CollectInfo(wo::KEtWorkbook* wb, const WCHAR *szName, int count);
void CollectInfo(wo::KEtWorkbook* wb, const WCHAR *szName, int count, int count2);
void CollectInfoRand(wo::KEtWorkbook* wb, const WCHAR *szName, int count, wo::util::RandDist * rand);
void CollectInfo(wo::KEtWorkbook* wb, const WCHAR *szName, int count, const WCHAR *szTraceId);
void CollectCmdInfo(KEtRevisionContext * ctx, const WCHAR *szName, int count);

void CollectAiColInfo(INT32 count, IBook* pBook = nullptr);
void CollectAiEafInfo(const QString &fileId, const EafResultInfo&, INT32 tokenInput, INT32 tokenOutput, INT32 tokenTotal
    , PCWSTR value, IBook* pBook = nullptr);
void CollectEtXfs(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectEtStyles(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectEtFormula(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectDbPrepareUser(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectLinkFormInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectCondFmtCacheInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectUsedRangeAccuracy(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectUndoRedoFailInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectAiFmlStat(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectShapesInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectCrooBookInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectQRColCount(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectStaleInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectVersionInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectMaxUserAndConn(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectXLookupCache(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectOneWayLinkHiddenSheetInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void WriteSvrFileinclude(binary_wo::BinWriter& binWriter, PCWSTR name, wo::KEtWorkbook* pEtWorkbook, int cnt = 0, int cnt2 = -1); 
void CollectDbFunnelInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectCommentCount(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectDbAttachmentInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectDashboardThemeInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectDashboardInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectDbFmlaFieldWillBeTransformed(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectImagepoolInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectProcPSS(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectDashboardViewInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter);
void CollectEventDispatcherInfo(wo::KEtApplication* app, binary_wo::BinWriter& binWriter);

void prepareCoverShapes(IDX sheetIdx, IKShapeRange* ptrShapeRange, KEtRevisionContext* ctx, ISerialAcceptor* acpt);

bool ForEachSheet(IBook *pBook, IDX from, IDX to, const std::function<bool(ISheet *, IDX)> &);
bool ForEachSheet(IBook *pBook, const std::function<bool(ISheet *, IDX)> &);

bool ForEachSheetRange(IBook *pBook,
                       const RANGE &,
                       const std::function<bool(ISheet *, IDX, const RANGE &)> &);

void Range2FormulaText(IBookOp *spBookOp, const RANGE &rg, CS_COMPILE_PARAM ccp, BSTR *outBstr);

ks_stdptr<IFormula> CreateFormula(IBook* pBook, PCWSTR str, CS_COMPILE_PARAM ccp);

void EnumRangeCells(ISheet *spSheet, const RANGE &rg, const std::function<bool (ROW row, COL col, const_token_ptr pToken)> &func);
void EnumRangeVisibleCells(ISheet *spSheet, const RANGE &rg, IEtRevisionContext* ctx, const std::function<bool (ROW row, COL col, const_token_ptr pToken)> &func);

_Workbook* OpenFile(KEtWorkbook* pWwb, Workbooks* ptrWorkbooks, const ks_wstring& fileName);
void CloseFile(_Workbook* ptrWorkbook);

struct OpenWorkbookScope
{
    OpenWorkbookScope()
    {
    }

    _Workbook* OpenWorkbook(KEtWorkbook* pWwb, Workbooks* ptrWorkbooks, const ks_wstring& fileName)
    {
        m_spWorkbook = OpenFile(pWwb, ptrWorkbooks, fileName);
        return m_spWorkbook;
    }
    ~OpenWorkbookScope() 
    {
        if(m_spWorkbook)
            CloseFile(m_spWorkbook);
    }
private:
    ks_stdptr<_Workbook> m_spWorkbook;
};

inline VARIANT_BOOL bool2VarBool(bool b)
{
	return b ? VARIANT_TRUE : VARIANT_FALSE;
}

inline int CORE_TO_API_UNIT_ET(int _core)
{
	return _core / 20;
}

template<typename C>
void ToUtf8Array(const C& wstrList, std::vector<QByteArray>* qarray, std::vector<const char*>* carray = nullptr)
{
    qarray->reserve(wstrList.size());
    if (carray)
        carray->reserve(wstrList.size());

    for (const ks_wstring& s : wstrList)
    {
        qarray->push_back(QString::fromUtf16(s.c_str()).toUtf8());
        if (carray)
        {
            carray->push_back(qarray->back());
        }
    }
}

inline void ToUtf8Array(PCWSTR *wstrList, size_t size, std::vector<QByteArray>* qarray, std::vector<const char*>* carray = nullptr)
{
    qarray->reserve(size);
    if (carray)
        carray->reserve(size);

    for (size_t i = 0;  i < size; ++i)
    {
        qarray->push_back(QString::fromUtf16(wstrList[i]).toUtf8());
        if (carray)
        {
            carray->push_back(qarray->back());
        }
    }
}

inline RECT GetSheetRect(ISheet* pSheet)
{
    BMP_PTR pBmp = pSheet->GetBMP();
    RECT r { 0 };
    r.bottom = pBmp->cntRows - 1;
    r.right = pBmp->cntCols - 1;
    return r;
}

ks_stdptr<etoldapi::Range> CreateRangeObj(etoldapi::_Workbook* pWorkbook, const RANGE& rg);

// 摘自 dbsheet_filter_core.h
struct PcwstrICmp
{
    bool operator()(PCWSTR str1, PCWSTR str2) const
    {
        if(str1 == NULL)
            str1 = __X("");
        if(str2 == NULL)
            str2 = __X("");

        return xstricmp(str1, str2) == 0;
    }
};

struct PcwstrIHash
{
    size_t operator()(PCWSTR str) const
    {
        unsigned long h = 0;
        if(str == NULL)
            str = __X("");
        while (*str)
        {
            h = h + (h >> 16) + (h << 4) + toxupper(*(str++));
        }
        return h;
    }
};

struct StrHasher
{
    size_t operator()(PCWSTR wsz) const
    {
        return alg::HashWString(wsz);
    }
    size_t operator()(const ks_wstring& wsz) const
    {
        return alg::HashWString(wsz.c_str());
    }
};

struct StrEqual
{
    bool operator()(const PCWSTR lhs, const PCWSTR rhs) const
    {
        return xstrcmp(lhs, rhs) == 0;
    }
};

bool isCellImgListSheet(IBook* pBook, IDX iSheet);

bool hasCellImgListSheet(IBook* pBook);

// 获取文件内最后一个有效sheet的idx。当最后一个sheet是 CellImgListSheet 时忽略它。
IDX getLastSheetIdx(IBook* pBook);

bool IsValidSheetIdx(IBook* pBook, IDX idxSheet);
bool IsValidCell(IBook* pBook, const CELL &cell);


class IgnoreHistoryGuard
{
public:
	explicit IgnoreHistoryGuard(KEtRevisionContext* ctx, bool enable = true);
    ~IgnoreHistoryGuard();

    void enable();

private:
    KEtRevisionContext *m_ctx = nullptr;
    bool m_enable = true;
};
WebInt DownloadedDashBoardSnapshot(KEtRevisionContext* pCtx, KEtWorkbook* pETWorkbook);
WebInt DownloadedAndLoadImg(std::map<ks_wstring, std::vector<ks_stdptr<IKBlipAtom>> *> &mapShapeUrl);
WebInt DownloadSheetAllUrlImg(ISheet *spSheet, bool bForceAttachmentReDownload = true);
WebInt DownloadCellImgForSheet(IKWorkbook* pBook, ISheet *pSheet);
WebInt DownloadCellImgForRange(IKWorkbook* pBook, ISheet *pSheet, RANGE& range);
WebInt DownloadCommentImage(ISheet* pSheet);

bool UploadImg(int16 dpi, drawing::AbstractShape* pShape, OUT QByteArray& sha1);


class TemporaryDisableRevisionCtx
{
public:
    TemporaryDisableRevisionCtx()
    {
        m_pCtx = _kso_GetRevisionContext();
        _kso_SetRevisionContext(nullptr);
    }

    ~TemporaryDisableRevisionCtx()
    {
        _kso_SetRevisionContext(m_pCtx);
    }

private:
    IRevisionContext *m_pCtx = nullptr;
};

drawing::Color decodeColorARGB(DWORD argb, BOOL isAutoColor = FALSE);

void PrintLog_ranges(PCWSTR tag, range_helper::ranges& rgs);

ISheet* getConnSharedSheet(KEtWorkbook* pWb, KEtRevisionContext*pCtx);
bool IsRangeEmpty(KEtRevisionContext* pCtx, ISheet *spSheet, const RANGE &rg);
HRESULT SetNumberFormat(KEtWorkbook* pEtWorkbook, const RANGE &rg, int nfIndex);
HRESULT SetNumberFormat(KEtWorkbook* pEtWorkbook, const RANGE &rg, BSTR nfStr);
bool HasMergedCell(ISheet* pSheet, const RANGE &rg);
void PrintLog_ranges(PCWSTR tag, range_helper::ranges& rgs);

SUPBOOK_STAT errorCode2SupbookStat(int errorCode);
SUPBOOK_STAT getNetDiskFile(PCWSTR netDiskName, PCWSTR fileId,
	ks_wstring& path, ks_wstring& version, ks_wstring& fileName, const QString& userId);
PCWSTR getNetDiskName();
ks_stdptr<ISheetProtection> getSheetProtection(ISheet *pSheet);
HRESULT ClearRangeFormatsAndValidation(KEtWorkbook* pEtWb, const RANGE& range);
ROW FindLastNonEmptyRow(KEtRevisionContext* pCtx, ROW headerRow, IKWorksheet* pWorkSheet);
PCWSTR GetBlipTypeStr(IKBlipAtom* pBlipAtom);
QString UploadImage(const drawing::AbstractShape* shape);
QString UploadImage(IKBlipAtom* pBlipAtom);

void CollectAndSendFormulaResChangeByWebhook(IBook* pBook, IKWorksheets* pSheets);
void ClearCollectedWebhookChange(IBook* pBook);
int64_t MemoStatProcPSS();

class AutoCmdTraceId
{
public:
	AutoCmdTraceId(KEtRevisionContext * ctx, const binary_wo::VarObj& vCmd);
	~AutoCmdTraceId();

private:
    WebStr getTraceIdInCmd(const binary_wo::VarObj & cmd, KEtRevisionContext*pCtx);
private:
	KEtRevisionContext * m_ctx;
};

struct VALIDATION_Wrap : public VALIDATION
{
    VALIDATION_Wrap();
    ~VALIDATION_Wrap();
};

class DbCdcScope
{
public:
    DbCdcScope(KEtWorkbook* pWorkbook, bool bIgnoreCache = false);
	~DbCdcScope();

private:
	ks_stdptr<IDbAutomations> m_spAutomations;
    bool m_bIgnoreCache = false;
	ks_stdptr<IDbCommonLog> m_spDbCommonLog;
    bool m_bEnableCommonLog = false;
    std::unique_ptr<KDbValueSerializeHelper> m_upSerializeHelper;
};

struct EtVectorBSTRGuard
{
	explicit EtVectorBSTRGuard(const std::etvector<BSTR> &vecBstr) : m_vecBstr(vecBstr)
	{
	}

	~EtVectorBSTRGuard()
	{
		for (size_t i = 0; i < m_vecBstr.size(); ++i)
		{
			::SysFreeString(m_vecBstr.at(i));
		}
	}

private:
	EtVectorBSTRGuard(const EtVectorBSTRGuard &);
	EtVectorBSTRGuard &operator=(const EtVectorBSTRGuard &);

	const std::etvector<BSTR> &m_vecBstr;
};

class OpenProgressScope
{
    using CallBack = std::function<void()>;
public:
    explicit OpenProgressScope(CallBack callBack = nullptr): m_callBack(callBack)
    {
        
    }

    ~OpenProgressScope()
    {
        if (m_callBack)
            m_callBack();
    }

private:
	OpenProgressScope(const OpenProgressScope&) = delete;
	OpenProgressScope& operator=(const OpenProgressScope&) = delete;

    CallBack m_callBack;
};

const char * toSvrConnFromStr(WoConnFrom from);
const char * toSvrConnSceneStr(WoConnScene scene);
const char * toSvrConnLifeStr(WoConnLife life);

void hanldeEtDashBoardSheet(IKWorksheets* pWorksheets);

bool IsNeedAdjustStatSheet(KEtWorkbook* pWorkbook);

void SendOpenProgress(const char* connID, PCWSTR stage, PCWSTR subStage, 
        UINT nProcess, UINT64 nCurrent, UINT64 nTotal);

void CollectMemInfo(PCWSTR name, unsigned int memUsageK, CollectInfoFunc collectFunc);

HRESULT BookRecalculateBehaviour(KEtWorkbook* wwb, const binary_wo::VarObj& param);

} // namespace util
} // namespace wo

#define CALL_TIME_STAT(class_name) util::CallTimeStat fs_calltime_stat_var_##__LINE__(class_name, __FUNCTION__);

#endif // __WEBET_UTIL_H__
