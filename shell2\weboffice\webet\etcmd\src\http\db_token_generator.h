﻿#ifndef __DB_TOKEN_GENERATOR_H__
#define __DB_TOKEN_GENERATOR_H__

#include "helpers/db_token_helper.h"
#include "ettools/ettools_encode_decoder.h"
#include "db/db_basic_itf.h"

template <ET_DbSheet_FieldType FieldType>
struct TokenGenerator;

static alg::managed_token_assist generateToken(IBook* pBook, IDbField* pField, const PCWSTR val)
{
    IBookOp* pBookOp = pBook->LeakOperator();
    //精简自KDBSheetData::setValueInner
    ks_stdptr<IFormula> spFmla;
    pBookOp->CreateFormula(&spFmla);

    COMPILE_RESULT crx;
    spFmla->SetFormula(val, pField->GetCompileParam(FML_CPL_THISROW), &crx);
    if (COMPILE_SUCCESS != crx.nErrCode)
        return {};

    HRESULT hr = pField->GetDbSheetData()->PreprocessToken(pField, spFmla);
    if(FAILED(hr))
        return {};

    BOOL bIsFormula = FALSE;
    const_token_ptr pToken = nullptr;
    ks_stdptr<ITokenVectorInstant> tokenVector;
    spFmla->GetContent(&bIsFormula, &tokenVector, &pToken);

    alg::managed_token_assist assist; 
    assist.clone_from(pToken);
    return alg::managed_token_assist {assist.detach()};
}

template <>
struct TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Contact>>
{
    // generator 内部处理了数组的就调用这个
    template <typename Generator>
    alg::managed_token_assist operator()(const binary_wo::VarObj& object, Generator&& generator) const
    {
        const UINT count = object.arrayLength_s();
        if (count == 0)
            return {};
        if (object.at_s(0).type() != binary_wo::typeArray)     // 单层 array
            return generator(object);
        ks_stdptr<IDbTokenArrayHandle> spTokenArray;
        _db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle,
                reinterpret_cast<void**>(&spTokenArray));
        for (UINT i = 0; i < count; ++i)
        {
            const binary_wo::VarObj subobject = object.at_s(i);
            if (subobject.type() == binary_wo::typeArray)
            {
                const auto result {(*this)(subobject, std::forward<Generator>(generator)).detach()};
                if (result)
                    spTokenArray->Add(result);
            }
        }
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
        return alg::managed_token_assist {assist.detach()};
    }
};
template <>
struct TokenArrayGenerator<TokenGenerator<Et_DbSheetField_MultipleSelect>> :
        TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Contact>> {};

template <>
struct TokenGenerator<Et_DbSheetField_MultiLineText> :
        TokenArrayGenerator<TokenGenerator<Et_DbSheetField_MultiLineText>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_MultiLineText>>;

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        alg::managed_vstr_token_assist assist;
        assist.create(object.value_str());
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};
template <>
struct TokenGenerator<Et_DbSheetField_SingleLineText> : TokenGenerator<Et_DbSheetField_MultiLineText> {};
template <>
struct TokenGenerator<Et_DbSheetField_ID> : TokenGenerator<Et_DbSheetField_MultiLineText> {};
template <>
struct TokenGenerator<Et_DbSheetField_Phone> : TokenGenerator<Et_DbSheetField_MultiLineText> {};
template <>
struct TokenGenerator<Et_DbSheetField_BarCode> : TokenGenerator<Et_DbSheetField_MultiLineText> {};

template <>
struct TokenGenerator<Et_DbSheetField_Date> :
        TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Date>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Date>>;

    IBook* m_pBook;
    IDbField* m_pField;
    TokenGenerator(IBook* pBook, IDbField* pField) : 
        m_pBook {pBook},
        m_pField {pField} 
    {}

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        if (object.type() == binary_wo::typeString)
            return generateToken(m_pBook, m_pField, object.value_str());
        else if (object.type() == binary_wo::typeFloat64 || object.type() == binary_wo::typeFloat32)
        {
            alg::managed_vdbl_token_assist assist;
            assist.create(object.value_double());
            return alg::managed_token_assist {assist.detach()};
        }
        else 
        {
            ASSERT(false);
            return {};
        }

    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_Time> : TokenGenerator<Et_DbSheetField_Date> 
{
    using base = TokenGenerator<Et_DbSheetField_Date> ;
    TokenGenerator(IBook* pBook, IDbField* pField) : 
        base(pBook, pField)
    {}
};

template <>
struct TokenGenerator<Et_DbSheetField_Cascade> : TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Cascade>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Cascade>>;

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        using namespace wo::VarObjFieldValidation;
        if (!expectArray(object, "districts"))
            return {};
        const binary_wo::VarObj districtArray = object.get_s("districts");
        const UINT count = districtArray.arrayLength_s();
        if (count == 0)
            return {};
        PCWSTR detail = __X("");
        std::vector<PCWSTR> districts;
        districts.reserve(static_cast<std::size_t>(count));
        for (UINT i = 0; i < count; ++i)
        {
            const binary_wo::VarObj value_object = districtArray.at_s(i);
            if (value_object.type() == binary_wo::typeString)
                districts.emplace_back(value_object.value_str());
        }
        ks_stdptr<IDbCascadeHandle> spCascadeHandle;
        _db_CreateObject(CLSID_KDbCascadeHandle, IID_IDbCascadeHandle,
                reinterpret_cast<void**>(&spCascadeHandle));
        spCascadeHandle->Init(districts.data(), districts.size(), detail, DBCT_Common);
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_DBADDRESS, spCascadeHandle);
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};
template <>
struct TokenGenerator<Et_DbSheetField_Address> : TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Address>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Address>>;

    ks_stdptr<IDbField> spField;
    TokenGenerator(ks_stdptr<IDbField> spField) : spField {spField} {}

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        using namespace wo::VarObjFieldValidation;
        if (!expectArray(object, "districts"))
            return {};
        const binary_wo::VarObj districtArray = object.get_s("districts");
        const UINT count = districtArray.arrayLength_s();
        if (count == 0)
            return {};
        PCWSTR detail = __X("");
        ks_stdptr<IDbField_Cascade> spField_Cascade = spField;
        if (spField_Cascade->GetWithDetailedInfo())
        {
            if (expectString(object, "detail"))
            {
                detail = object.field_str("detail");
                if (xstrcmp(detail, __X("")) == 0)
                    return {};
            }
        }
        std::vector<PCWSTR> districts;
        districts.reserve(static_cast<std::size_t>(count));
        for (UINT i = 0; i < count; ++i)
        {
            const binary_wo::VarObj value_object = districtArray.at_s(i);
            if (value_object.type() == binary_wo::typeString)
                districts.emplace_back(value_object.value_str());
        }
        ks_stdptr<IDbCascadeHandle> spCascadeHandle;
        _db_CreateObject(CLSID_KDbCascadeHandle, IID_IDbCascadeHandle,
                reinterpret_cast<void**>(&spCascadeHandle));
        spCascadeHandle->Init(districts.data(), districts.size(), detail, DBCT_Address);
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_DBADDRESS, spCascadeHandle);
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_Department> : TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Department>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Department>>;

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        using namespace wo::VarObjFieldValidation;
        if (!expectArray(object, "districts"))
            return {};
        const binary_wo::VarObj districtArray = object.get_s("districts");
        const UINT count = districtArray.arrayLength_s();
        if (count == 0)
            return {};
        PCWSTR detail = __X("");
        if (object.has("detail"))
            detail = object.field_str("detail");
        std::vector<PCWSTR> districts;
        districts.reserve(static_cast<std::size_t>(count));
        for (UINT i = 0; i < count; ++i)
        {
            const binary_wo::VarObj value_object = districtArray.at_s(i);
            if (value_object.type() == binary_wo::typeString)
                districts.emplace_back(value_object.value_str());
        }
        ks_stdptr<IDbCascadeHandle> spCascadeHandle;
        _db_CreateObject(CLSID_KDbCascadeHandle, IID_IDbCascadeHandle,
                reinterpret_cast<void**>(&spCascadeHandle));
        spCascadeHandle->Init(districts.data(), districts.size(), detail, DBCT_Department);
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_DBADDRESS, spCascadeHandle);
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_Attachment> :
        TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Attachment>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Attachment>>;

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        using namespace wo::VarObjFieldValidation;
        const bool verify = expectString(object, "uploadId") &&
                expectString(object, "fileName") && expectString(object, "source")
                && expectString(object, "type") && expectNumeric(object, "size");
        if (verify == false)
            return {};
        ks_stdptr<IDbAttachmentHandle> spAttachmentHandle;
        _db_CreateObject(CLSID_KDbAttachmentHandle, IID_IDbAttachmentHandle,
                reinterpret_cast<void**>(&spAttachmentHandle));
        const PCWSTR uploadId = object.field_str("uploadId");
        const PCWSTR fileName = object.field_str("fileName");
        const PCWSTR sourceArg = object.field_str("source");
        DbSheet_AttachmentSource source = AttachmentSource_cloud;
        _appcore_GainEncodeDecoder()->DecodeDbAttachmentSource(sourceArg, &source);
        const PCWSTR type = object.field_str("type");
        const UINT size = object.field_uint32("size");
        PCWSTR linkUrl = __X("");
        if (object.has("linkUrl"))
            linkUrl = object.field_str("linkUrl");
        PCWSTR imgSize = __X("");
        if (object.has("imgSize"))
            imgSize = object.field_str("imgSize");
        if (FAILED(spAttachmentHandle->Init(uploadId, source, type,
                fileName, size, linkUrl, imgSize)))
            return {};
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_DBATTACHMENT, spAttachmentHandle);
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_Url> : TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Url>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Url>>;

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        alg::managed_vstr_token_assist assist;
        switch (object.type())
        {
        case binary_wo::typeString:
        {
            const WebStr value = object.value_str();
            alg::managed_token_assist result;
            if (FAILED(_appcore_GainDbSheetContext()->Text2DbHyperlinkToken(value, value, &result)))
                return {};
            return result;
        }
        case binary_wo::typeStruct:
            if (!wo::VarObjFieldValidation::expectString(object, "displayText") or
                    !wo::VarObjFieldValidation::expectString(object, "address"))
                return {};
            {
                alg::managed_token_assist result;
                if(FAILED(_appcore_GainDbSheetContext()->Text2DbHyperlinkToken(object.field_str("displayText"),
                        object.field_str("address"), &result)))
                    return {};
                return result;
            }
        default:
            ASSERT(false);
            return {};
        }
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};
template <>
struct TokenGenerator<Et_DbSheetField_Email> : TokenGenerator<Et_DbSheetField_Url> {};

template <>
struct TokenGenerator<Et_DbSheetField_SingleSelect> :
        TokenArrayGenerator<TokenGenerator<Et_DbSheetField_SingleSelect>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_SingleSelect>>;

    ks_stdptr<IDbField> spField;
    IDBSheetCtx* const pCtx {};
    const bool preferID {};
    TokenGenerator(ks_stdptr<IDbField> spField, IDBSheetCtx* pCtx, bool preferID) :
            spField {spField}, pCtx {pCtx}, preferID {preferID} {}

    auto valueInID() const
    {
        return [pCtx = pCtx](WebStr value, IDbField_Select *pField_Selection) -> std::pair<HRESULT, PCWSTR> {
            EtDbId itemId = INV_EtDbId;
            pCtx->DecodeEtDbId(value, &itemId);
            PCWSTR itemValue = __X("");
            HRESULT hr = pField_Selection->ItemById(itemId, &itemValue, nullptr);
            return {hr, itemValue};
        };
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        WebStr value = object.value_str();
        ks_stdptr<IDbField_Select> spField_Selection = spField;
        alg::managed_vstr_token_assist assist;
        if (preferID)
        {
            const std::pair<HRESULT, PCWSTR> result = valueInID()(value, spField_Selection.get());
            if (FAILED(result.first))
                return {};
            assist.create(result.second);
            return alg::managed_token_assist {assist.detach()};
        }
        if (spField_Selection->HasItem(value) == FALSE)
        {
            if (spField_Selection->GetAllowAddItemWhenInputting() == TRUE)
                spField_Selection->AppendItemWithAutoColor(value);
            else
                return {};
        }
        assist.create(value);
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_Number> : TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Number>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Number>>;

    static alg::managed_vdbl_token_assist doubleToken(double value)
    {
        alg::managed_vdbl_token_assist assist;
        assist.create(value);
        return assist;
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        switch (object.type())
        {
        case binary_wo::typeFloat32:
        case binary_wo::typeFloat64:
            return alg::managed_token_assist {doubleToken(object.value_double()).detach()};
        case binary_wo::typeUint32:
        case binary_wo::typeUint16:
        case binary_wo::typeUint8:
            return alg::managed_token_assist {doubleToken(static_cast<double>(object.value_uint32())).detach()};
        case binary_wo::typeInt32:
        case binary_wo::typeInt16:
        case binary_wo::typeInt8:
            return alg::managed_token_assist {doubleToken(static_cast<double>(object.value_int32())).detach()};
        case binary_wo::typeString:
        {
            alg::managed_vstr_token_assist assist;
            assist.create(object.value_str());
            return alg::managed_token_assist {assist.detach()};
        }
        default:
            ASSERT(false);
            break;
        }
        return {};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};
template <>
struct TokenGenerator<Et_DbSheetField_Currency> : TokenGenerator<Et_DbSheetField_Number> {};
template <>
struct TokenGenerator<Et_DbSheetField_Percentage> : TokenGenerator<Et_DbSheetField_Number> {};
template <>
struct TokenGenerator<Et_DbSheetField_Rating> : TokenGenerator<Et_DbSheetField_Number> {};
template <>
struct TokenGenerator<Et_DbSheetField_Complete> : TokenGenerator<Et_DbSheetField_Number> {};

template <>
struct TokenGenerator<Et_DbSheetField_Checkbox> : TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Checkbox>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Checkbox>>;

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        alg::managed_vdbl_token_assist assist;
        assist.create(object.value_bool() ? 1.0 : 0.0);
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_MultipleSelect> :
        TokenArrayGenerator<TokenGenerator<Et_DbSheetField_MultipleSelect>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_MultipleSelect>>;

    ks_stdptr<IDbField> spField;
    IDBSheetCtx* const pCtx {};
    const bool preferID {};
    TokenGenerator(ks_stdptr<IDbField> spField, IDBSheetCtx* pCtx, bool preferID) :
            spField {spField}, pCtx {pCtx}, preferID {preferID} {}
    
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        ks_stdptr<IDbSelectItemHandle> spSelectionHandle;
        _db_CreateObject(CLSID_KDbSelectItemHandle, IID_IDbSelectItemHandle,
                reinterpret_cast<void**>(&spSelectionHandle));
        ks_stdptr<IDbField_Select> spFieldSelect = spField;
        const UINT count = object.arrayLength_s();
        for (UINT i = 0; i < count; ++i)
        {
            const binary_wo::VarObj value_object = object.at_s(i);
            if (value_object.type() != binary_wo::typeString)
                continue;
            if (preferID)
            {
                EtDbId ID = INV_EtDbId;
                pCtx->DecodeEtDbId(value_object.value_str(), &ID);
                PCWSTR value = __X("");
                if (FAILED(spFieldSelect->ItemById(ID, &value, nullptr)))
                    continue;
                spSelectionHandle->AppendByItem(value);
            }
            else
            {
                const PCWSTR value = value_object.value_str();
                if (!spFieldSelect->HasItem(value))
                {
                    if (spFieldSelect->GetAutoAddItem())
                        spFieldSelect->AppendItem(value, DEFAULT_COLOR);
                    else
                        continue;
                }
                spSelectionHandle->AppendByItem(value);
            }
        }
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_SELECTITEMS, spSelectionHandle);
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_Contact> : TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Contact>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Contact>>;

    IBook* const pBook {};
    IDbField* const pField;
    TokenGenerator(IBook* pBook, IDbField* pField) : pBook {pBook}, pField(pField) {}

    kfc::ks_wstring cellString(const binary_wo::VarObj& object) const
    {
        kfc::ks_wstring result;
        std::vector<PCWSTR> failed;
        std::vector<wo::DbSheet::UserInfo> information;
        const auto processor {[&failed, &information, &result](const binary_wo::VarObj &value) -> bool {
            if (value.type() != binary_wo::typeStruct)
                return false;
            using namespace wo::VarObjFieldValidation;
            if (!expectString(value, "id"))
                return false;
            result.append(value.field_str("id"));
            result.push_back(__Xc(','));
            const VarObj nickname = value.has("nickname") ? value.get_s("nickname") : value.get_s("nickName");
            const VarObj avatar_url = value.has("avatar_url") ? value.get_s("avatar_url") : value.get_s("avatar");
            if (nickname.type() == binary_wo::typeString && avatar_url.type() == binary_wo::typeString)
            {
                information.emplace_back();
                wo::DbSheet::UserInfo &back = information.back();
                back.id = value.field_str("id");
                back.nickname = nickname.value_str();
                back.avatar = avatar_url.value_str();
                if (value.has("companyId"))
                    back.companyId = std::make_unique<kfc::ks_wstring>(value.field_str("companyId"));
            }
            return true;
        }};
        if (object.type() == binary_wo::typeStruct)
        {
            // 一个就不需要提前 reserve 了
            if (!processor(object))
                return {};
        }
        else
        {
            const UINT count = object.arrayLength_s();
            if (count == 0)
                return {};
            information.reserve(count);
            for (UINT i = 0; i < count; ++i)
            {
                const binary_wo::VarObj value = object.at_s(i);
                processor(value);
            }
        }
        if (!result.empty())
            result.pop_back();
        ks_stdptr<IDbUsersManager> spUserManager;
        pBook->GetExtDataItem(edBookDbUserManager, reinterpret_cast<IUnknown**>(&spUserManager));
        if (spUserManager)
        {
            std::vector<KDbCstrUserInfo> c_strUserInformation;
            if (FAILED(wo::DbSheet::GetCstrUserInfoVec(information, c_strUserInformation)))
                return {};
            if (FAILED(spUserManager->UpdateUserInfoFromServer(c_strUserInformation.data(),
                    c_strUserInformation.size(), failed.data(), failed.size())))
                return {};
        }
        return result;
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        ks_wstring result = cellString(object);
        if (result.empty())
            return {};
        return generateToken(pBook, pField, result.c_str());
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_Note> : TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Note>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_Note>>;

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        using namespace wo::VarObjFieldValidation;
        const bool verify = expectString(object, "fileId") && expectString(object, "summary") &&
                expectString(object, "modifyDate");
        if (verify == false)
            return {};
        ks_stdptr<IDbNoteHandle> spNoteHandle;
        VS(_db_CreateObject(CLSID_KDbNoteHandle, IID_IDbNoteHandle,
                reinterpret_cast<void**>(&spNoteHandle)));
        const QDateTime date = QDateTime::fromString(QString::fromUtf16(object.field_str("modifyDate")),
                "yyyy/MM/dd hh:mm:ss");
        if (FAILED(spNoteHandle->Init4IO(object.field_str("fileId"),
                object.field_str("summary"), date.toMSecsSinceEpoch())))
            return {};
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_DBNOTE, spNoteHandle);
        return alg::managed_token_assist {assist.detach()};
    }
    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

template <>
struct TokenGenerator<Et_DbSheetField_FormulaResult> :
        TokenArrayGenerator<TokenGenerator<Et_DbSheetField_FormulaResult>>
{
    using base = TokenArrayGenerator<TokenGenerator<Et_DbSheetField_FormulaResult>>;

    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        using namespace wo::VarObjFieldValidation;
        const bool verify = object.type() == binary_wo::typeStruct &&
                (object.has("value") or object.has("code")) &&
                expectString(object, "type");
        if (verify == false)
            return TokenArrayGenerator<EmptyArrayGenerator> {}();

        using namespace db_token_helper;
        // AF 针对空值进行了特殊处理, 给到内核的可能是空字符串, 这里假设只要是字符串, 必定是空, 不管里面是否有值
        if (object.type() == binary_wo::typeString)
            return TokenArrayGenerator<EmptyArrayGenerator> {}();
        const WebStr type = object.field_str("type");
        if (xstrcmp(type, __X("ETP_VINT")) == 0)
            return alg::managed_token_assist {helper<alg::ETP_VINT> {}(object.get_s("value"))};
        else if (xstrcmp(type, __X("ETP_VDBL")) == 0)
            return alg::managed_token_assist {helper<alg::ETP_VDBL> {}(object.get_s("value"))};
        else if (xstrcmp(type, __X("ETP_VBOOL")) == 0)
            return alg::managed_token_assist {helper<alg::ETP_VBOOL> {}(object.get_s("value"))};
        else if (xstrcmp(type, __X("ETP_VSTR")) == 0)
            return alg::managed_token_assist {helper<alg::ETP_VSTR> {}(object.get_s("value"))};
        else if (xstrcmp(type, __X("ETP_ERROR")) == 0)
            return alg::managed_token_assist {helper<alg::ETP_ERROR> {}(object)};
        else if (xstrcmp(type, __X("ETP_HANDLE")) == 0)
            return alg::managed_token_assist {helper<alg::ETP_HANDLE> {}(object)};

        ASSERT("其它类型还未支持, 若需要请在此实现" == nullptr);
        return TokenArrayGenerator<EmptyArrayGenerator> {}();
    }

    alg::managed_token_assist array(const binary_wo::VarObj& object) const
    {
        return static_cast<const base &>(*this)(object, *this);
    }
};

#endif // __DB_TOKEN_GENERATOR_H__