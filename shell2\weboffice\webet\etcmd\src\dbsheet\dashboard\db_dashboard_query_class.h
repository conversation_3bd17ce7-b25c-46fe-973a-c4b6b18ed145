﻿#ifndef __WEBET_DB_DASHBOARD_QUERY_CLASS_H__
#define __WEBET_DB_DASHBOARD_QUERY_CLASS_H__

#include "dbsheet/et_dbsheet_query_class.h"

namespace wo
{

class QueryDbDashboardModuleContent : public ETDbSheetQueryClassBase
{
public:
    explicit QueryDbDashboardModuleContent(KEtWorkbook*);
    HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx) override;
};

class QueryDbDashboardModulesContent : public ETDbSheetQueryClassBase
{
public:
    explicit QueryDbDashboardModulesContent(KEtWorkbook*);
    HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx) override;
};

class QueryDbDashboardFilterValues : public ETDbSheetQueryClassBase
{
public:
    explicit QueryDbDashboardFilterValues(KEtWorkbook*);
    HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx) override;
private:
    bool IsShareDashboardConn(IKUserConn* pUser, UINT sheetId);
};

};
#endif // __WEBET_DB_DASHBOARD_QUERY_CLASS_H__