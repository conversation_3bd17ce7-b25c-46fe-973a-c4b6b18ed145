﻿#ifndef __ET_APPSHEET_COPYSHEET_HELPER_H__
#define __ET_APPSHEET_COPYSHEET_HELPER_H__

#include "appcore/et_appcore_dbsheet.h"
#include "workbook.h"

namespace wo
{
class AppSharedInfo
{
public:
	AppSharedInfo(AirAppInfo appInfo, PCWSTR oldSharedId, PCWSTR newSharedId)
	: m_appInfo(appInfo)
	, m_oldSharedId(oldSharedId)
	, m_newSharedId(newSharedId)
	{}
	void Serialize(binary_wo::VarObj* pObj);
	AirAppInfo GetAppInfo() { return m_appInfo; }
private:
	AirAppInfo m_appInfo;
	ks_wstring m_oldSharedId;
	ks_wstring m_newSharedId;
};

class CopyAppSheetHelper
{
public:
	HRESULT Init(etoldapi::_Worksheet*, etoldapi::_Worksheet*, std::vector<AppSharedInfo>*, binary_wo::VarObj*);
	HRESULT InitForTemplate(std::unordered_map<UINT, UINT>*);
	HRESULT ExecCopy();

private:
	HRESULT copyDbView(IAirApp* pSrcApp, IAirApp* pTarApp);
	HRESULT fetchSharedId(ET_DBSheet_ViewType viewType, IAirApp* pTarApp, ks_wstring* sharedId);
	HRESULT fetchSharedIdForDirect(IAirApp* pTarApp, ks_wstring* sharedId);

private:
	etoldapi::_Worksheet* m_pWorkSheet = nullptr;
	etoldapi::_Worksheet* m_pNewWorkSheet = nullptr;
	const std::unordered_map<UINT, UINT>* m_pSheetIdMap = nullptr;
	std::vector<AppSharedInfo>* m_pSharedInfo = nullptr;
	ks_stdptr<ISharedLinkMgr> m_spSharedLinkMgr;
	binary_wo::VarObj* m_pParam = nullptr;
};

class CopyAppSheetForLocal :public CopyAppSheetHelper
{
public:
	HRESULT Init(etoldapi::_Worksheet*, etoldapi::_Worksheet*, binary_wo::VarObj*);
	void SerialAppSharedInfo(binary_wo::VarObj* pObj);
private:
	std::vector<AppSharedInfo> m_sharedInfo;
};

};
#endif // __ET_APPSHEET_COPYSHEET_HELPER_H__