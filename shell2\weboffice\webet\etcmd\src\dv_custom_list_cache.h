﻿#ifndef __DV_CUSTOM_LIST_CALCULATION_CACHE_H__
#define __DV_CUSTOM_LIST_CALCULATION_CACHE_H__

#include "wo/et_revision_context.h"
#include "wo/workbook_obj.h"
#include "wo/worksheet_obj.h"

namespace wo 
{
class KDvCustomListCache : public IDvCustomListNotifyScheme
{
	
	struct CellResult
	{
		CELL cell = {-1, -1};
		std::vector<INT> result;
		bool bMulitiCompare = false;
		alg::MSR_HANDLE hd = NULL;
	};
    using CellResultVector = std::vector<CellResult>;
    using SheetCellResultMap = std::map<WebID, CellResultVector>;
public:
    KDvCustomListCache(etoldapi::_Workbook* pBook);
	virtual ~KDvCustomListCache();
	HRESULT CalculateAndSerializeCellResults(ISerialAcceptor* acpt, IDX sheetIdx, std::vector<CELL>& cells);
	void OnFileOpened();

	// IDvCustomListNotifyScheme
	STDPROC_(void) OnRangeDirty(const RANGE& rg) override;
	STDPROC_(void) ClearDirtys() override;
	STDPROC_(UINT) GetDirtyRangeCount() override;
	STDPROC_(void) SerialDirtyRanges(ISerialAcceptor *acpt) override;
private:
    bool findCache(IDX sheetIdx, ROW row, COL col, CellResult& ret);
	void removeCache(const RANGE& rg);
    bool removeCache(IDX sheetIdx, ROW row, COL col);
	void removeAllCache();
    void saveCache(IDX sheetIdx, CellResult& ret);
    wo::IWorksheetObj* getSheetMain(IDX sheetIdx);
	UINT getCacheMaxSizePerSheet();
	HRESULT getValidation(IDX idxSheet, ROW row, COL col, ICoreValidation **ppv);
	void calculateCellResult(BSTR bstrText, std::etvector<BSTR>& vecValue, bool bMultiCompare, std::vector<INT>& ret);
    void serialCellResult(ISerialAcceptor* acpt, IDX sheetIdx, CellResult& cr);

	void getCellText(ISheet* pSheet, IDX idxSheet, ROW row, COL col, BSTR* pBstr, bool bMultiCompare);

	BOOL m_bEnableCollectDirtys = FALSE;
    IBook* m_pBook;
    ks_stdptr<etoldapi::_Workbook> m_ptrWorkbook;
	ks_stdptr<IETStringTools> m_spStringTools;
	ks_stdptr<IKRanges> m_spDirtyRanges;
    SheetCellResultMap m_cache;
};

}

#endif // __DV_CUSTOM_LIST_CALCULATION_CACHE_H__