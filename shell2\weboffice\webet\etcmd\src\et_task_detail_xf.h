﻿#ifndef __WEBET_ET_TASK_DETAIL_XF_H__
#define __WEBET_ET_TASK_DETAIL_XF_H__

#include <public_header/revision/src/kwrevisionctrl.h>

#define BLS_INVALID -1

namespace wo
{

struct BorderXF
{
	BorderXF()
		: dgLeft(BLS_INVALID)
		, dgRight(BLS_INVALID)
		, dgTop(BLS_INVALID)
		, dgBottom(BLS_INVALID)
		, dgDiagUp(BLS_INVALID)
		, dgDiagDown(BLS_INVALID)
		, dgInsideVert(BLS_INVALID)
		, dgInsideHorz(BLS_INVALID)
	{
	}

	bool IsValid() const
	{
		return BLS_INVALID != dgLeft || BLS_INVALID != dgRight || BLS_INVALID != dgTop
			|| BLS_INVALID != dgBottom || BLS_INVALID != dgDiagUp || BLS_INVALID != dgDiagDown
			|| BLS_INVALID != dgInsideVert || BLS_INVALID != dgInsideHorz
			|| spClrLeft.get() || spClrRight.get() || spClrTop.get()
			|| spClrBottom.get() || spClrDiagUp.get() || spClrDiagDown.get()
			|| spClrInsideVert.get() || spClrInsideHorz.get();
	}

	std::unique_ptr<EtColor> spClrLeft;
	std::unique_ptr<EtColor> spClrRight;
	std::unique_ptr<EtColor> spClrTop;
	std::unique_ptr<EtColor> spClrBottom;
	std::unique_ptr<EtColor> spClrDiagUp;
	std::unique_ptr<EtColor> spClrDiagDown;
	std::unique_ptr<EtColor> spClrInsideVert;
	std::unique_ptr<EtColor> spClrInsideHorz;
	int dgLeft;
	int dgRight;
	int dgTop;
	int dgBottom;
	int dgDiagUp;
	int dgDiagDown;
	int dgInsideVert;
	int dgInsideHorz;
};

HRESULT _SetRangeBorderXf(ks_stdptr<etoldapi::Range> spRange, const BorderXF& borderXf);
void _SetBorderLineStyle(ks_stdptr<etoldapi::Border> spBorder, int bls);
void _SetBorderColor(ks_stdptr<etoldapi::Border> spBorder, const EtColor* pColor);
void DecodeColor(const binary_wo::VarObj& vClr, EtColor& clr);
void DecodeFill(const binary_wo::VarObj& vFill, EtFill& fill, KXFMASK& mask);
void DecodeIconSet(const binary_wo::VarObj& vIconSet, EtIconSet& iconSet);
void DecodeFont(const binary_wo::VarObj& vFont, FONT& font, KXFMASK& mask);
void DecodeXf(const binary_wo::VarObj& varXf, KXF& xf, KXFMASK& mask, bool decodeBdr = false);
bool DecodeBorderXf(const binary_wo::VarObj& varXf, BorderXF& borderXf);
bool DecodeColor_s(const binary_wo::VarObj& vClrRoot, WebName clrFieldName, std::unique_ptr<EtColor>& spClr);
bool DecodeColor_s(const binary_wo::VarObj& vClrRoot, WebName clrFieldName, EtColor& spClr);

void EncodeColor(const EtColor& clr, WebName name, binary_wo::VarObj& resObj);
void EncodeFill(const EtFill& fill, WebName name, binary_wo::VarObj& resObj);
void EncodeFont(const FONT& font, WebName name, binary_wo::VarObj& resObj);
void EncodeXf(const KXF& xf, WebName name, binary_wo::VarObj& resObj);

}//namespace wo

#endif //__WEBET_ET_TASK_DETAIL_XF_H__
