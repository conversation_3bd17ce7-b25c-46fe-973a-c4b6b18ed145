
#include "etstdafx.h"
#include "et_binvar_spec.h"
#include "appcore/et_appcore_cellcolor_itf.h"
#include "et_task_detail_xf.h"
#include "tools/et_combstr.h"
#include "alg/alg.h"

#include "sort_protoc.h"

#include <cassert>

namespace wo
{
namespace range_sort
{

static inline bool ReadCaseSensitive(const binary_wo::VarObj &param, bool *res)
{
    if (!param.has("caseSensitive"))
        return false;

    *res = param.field_bool("caseSensitive");
    return true;
}

static inline bool ReadHasHeader(const binary_wo::VarObj &param, bool *res)
{
    if (!param.has("hasHeader"))
        return false;

    *res = param.field_bool("hasHeader");
    return true;
}

static bool ReadSortOrientation(const binary_wo::VarObj &param, ETSortOrientation *res)
{
    if (!param.has("sortOrientation"))
        return false;

    const int32_t val = param.field_int32("sortOrientation");
    switch (val)
    {
    case range_sort::etSortRows:
        *res = ::etSortRows;
        return true;
    case range_sort::etSortColumns:
        *res = ::etSortColumns;
        return true;
    default:
        assert(false);
    }

    return false;
}

static bool ReadSortMethod(const binary_wo::VarObj &param, ETSortMethod *res)
{
    if (!param.has("sortMethod"))
        return false;

    const int32_t val = param.field_int32("sortMethod");
    switch (val)
    {
    case range_sort::etPinYin:
        *res = ::etPinYin;
        return true;
    case range_sort::etStroke:
        *res = ::etStroke;
        return true;
    default:
        assert(false);
    }

    return false;
}

bool ReadSortOption(const binary_wo::VarObj &param, bool *case_sensitive, ETSortOrientation *orientation, ETSortMethod *sort_method, ETYesNoGuess *header_guess)
{
    if (!param.has("sortOption"))
        return false;

    const binary_wo::VarObj sort_option = param.get("sortOption");
    ReadCaseSensitive(sort_option, case_sensitive);
    ReadSortMethod(sort_option, sort_method);
    ReadSortOrientation(sort_option, orientation);
    bool has_header = false;
    if (ReadHasHeader(sort_option, &has_header)) {
        *header_guess = has_header ? ::etYes : ::etNo;
    }

    return true;
}

static bool ReadSortOn(const binary_wo::VarObj &param, int32_t *res)
{
    if (res == NULL)
        return false;
    if (!param.has("sortOn"))
    {
        *res = ::xlSortOnValues;
        return false;
    }

    const int32_t val = param.field_int32("sortOn");
    switch (val)
    {
    case range_sort::xlSortOnValues:
        *res = ::xlSortOnValues;
        return true;
    case range_sort::xlSortOnCellColor:
        *res = ::xlSortOnCellColor;
        return true;
    case range_sort::xlSortOnFontColor:
        *res = ::xlSortOnFontColor;
        return true;
    case range_sort::xlSortOnIcon:
        *res = ::xlSortOnIcon;
        return true;

    default:
        assert(false);
    }

    return false;
}

static bool ReadSortOrder(const binary_wo::VarObj &param, int32_t *res)
{
    if (res == NULL)
        return false;
    if (!param.has("order"))
    {
        *res = ::etAscending;
        return false;
    }

    const int32_t val = param.field_int32("order");
    switch (val)
    {
    case range_sort::etAscending:
        *res = ::etAscending;
        return true;
    case range_sort::etDescending:
        *res = ::etDescending;
        return true;
    default:
        assert(false);
    }

    return false;
}

static bool ReadCustomOrder(const binary_wo::VarObj &param, KComVariant *res)
{
    if (res == NULL)
        return false;
    if (!param.has("customOrder"))
        return false;

    const WebStr val = param.field_str("customOrder");
    res->AssignString(val);
    return true;
}

bool ReadSortField(const binary_wo::VarObj &param, int32_t *sort_on, int32_t *sort_order, KComVariant *custom_order)
{
    ReadSortOn(param, sort_on);
    ReadSortOrder(param, sort_order);
    ReadCustomOrder(param, custom_order);
    return true;
}

bool ReadSortOnValue(IBook *book, const binary_wo::VarObj &param, int32_t sort_on, IKSortField *sort_field)
{
    switch (sort_on)
    {
    case ::xlSortOnValues:
        break;
    case ::xlSortOnCellColor:
    {
        ks_stdptr<ICellColor> spCellColor;
        _appcore_CreateObject(CLSID_KCellColor, IID_ICellColor, (void **)&spCellColor);
        if (spCellColor != NULL) {
            EtFill fill;
            KXFMASK mask;
            DecodeFill(param.get("cellFill"), fill, mask);
            spCellColor->SetFill(fill);
            sort_field->SortOnCellColor(spCellColor);            
        }
        break;
    }
    case ::xlSortOnFontColor:
    {
        ks_stdptr<IFontColor> spFontColor;
        _appcore_CreateObject(CLSID_KFontColor, IID_IFontColor, (void **)&spFontColor);
        if (spFontColor != NULL) {
            spFontColor->SetBookForIO(book);
            EtColor color;
            DecodeColor(param.get("cellFontColor"), color);
            spFontColor->SetColor(color);
            sort_field->SortOnFontColor(spFontColor);
        }
        break;
    }

    case ::xlSortOnIcon:
    {
        ks_stdptr<ICellIcon> spCellIcon;
        _appcore_CreateObject(CLSID_KCellIcon, IID_ICellIcon, (void **)&spCellIcon);
        if (spCellIcon != NULL) {
            
            EtIconSet is = {CFIS_None, 0};
            DecodeIconSet(param.get("cellIcon"), is);
			spCellIcon->SetIcon(is);
            sort_field->SortOnCellIcon(spCellIcon);
        }
        break;
    }
    default:
        assert(false);
    }

    return true;
}

static inline void WriteCaseSensitive(ISerialAcceptor *acpt, bool const case_sensitive)
{
    acpt->addBool("caseSensitive", case_sensitive);
}

static void WriteSortOrientation(ISerialAcceptor *acpt, ETSortOrientation const orientation)
{
    int32_t val = ::etSortColumns;
    switch (orientation)
    {
    case ::etSortRows:
        val = range_sort::etSortRows;
        break;
    case ::etSortColumns:
        val = range_sort::etSortColumns;
        break;
    default:
        assert(false);
    }

    acpt->addInt32("sortOrientation", val);
}

static void WriteSortMethod(ISerialAcceptor *acpt, ETSortMethod const sort_method)
{
    int32_t val = ::etPinYin;
    switch (sort_method)
    {
    case ::etPinYin:
        val = range_sort::etPinYin;
        break;
    case ::etStroke:
        val = range_sort::etStroke;
        break;
    default:
        assert(false);
    }

    acpt->addInt32("sortMethod", val);
}

void WriteSortOption(ISerialAcceptor *acpt, bool case_sensitive, ETSortOrientation orientation, ETSortMethod sort_method, bool hasHeader, bool hasFilterHeader)
{
    acpt->addKey("sortOption");
    acpt->beginStruct();

    WriteCaseSensitive(acpt, case_sensitive);
    WriteSortOrientation(acpt, orientation);
    WriteSortMethod(acpt, sort_method);
    acpt->addBool("hasHeader", hasHeader);
    acpt->addBool("hasFilterHeader", hasFilterHeader);
    acpt->endStruct();
}

void WriteColor(ISerialAcceptor *acpt, const EtColor &ec, IBook *bk, KeyName tag)
{
    if (tag != NULL)
        acpt->addKey(tag);
    acpt->beginStruct();

    EtColor &cc = const_cast<EtColor &>(ec);

    VERIFY(acpt->addFloat64("tint", cc.getTint()) == WO_OK);
    VERIFY(acpt->addInt32("type", cc.getType()) == WO_OK);
    switch (cc.getType())
    {
    case ectICV:
        VERIFY(acpt->addUint8("value", cc.getICV()) == WO_OK);
        break;
    case ectTHEME:
        VERIFY(acpt->addInt32("value", cc.getTheme()) == WO_OK);
        break;
    case ectARGB:
        VERIFY(acpt->addUint32("value", cc.getARGB()) == WO_OK);
        break;
    case ectEMPTY:
    case ectNONE:
    case ectAUTO:
        break;
    }

    DWORD argb = bk->ToARGB(ec);
    if ((argb & 0xFF000000) != 0)
    {
        double alpha = ((argb >> 24) & 0xFF);
        auto conv = [alpha, argb](INT x) -> DWORD {
            DWORD tmp = ((argb >> x) & 0xFF);
            return static_cast<DWORD>(alpha * tmp / 255) << x;
        };
        argb = (conv(16) | conv(8) | conv(0));
    }

    ks_wstring buff;
    buff.Format(__X("#%06X"), (argb & 0xFFFFFF));
    VERIFY(acpt->addString("rgbValue", buff.data()) == WO_OK);
    acpt->endStruct();
}

void WriteFill(ISerialAcceptor *acpt, const EtFill &fill, IBook *bk, KeyName tag)
{
    if (tag != NULL)
        acpt->addKey(tag);
    acpt->beginStruct();
    acpt->addUint32("type", fill.getType());
    WriteColor(acpt, fill.getBack(), bk, "back");
    WriteColor(acpt, fill.getFore(), bk, "fore");
    acpt->endStruct();
}

static void WriteSortOn(ISerialAcceptor *acpt, const int32_t sort_on)
{
    int32_t val = sort_on;
    switch (sort_on)
    {
    case ::xlSortOnValues:
        val = range_sort::xlSortOnValues;
        break;
    case ::xlSortOnCellColor:
        val = range_sort::xlSortOnCellColor;
        break;
    case ::xlSortOnFontColor:
        val = range_sort::xlSortOnFontColor;
        break;
    case ::xlSortOnIcon:
        val = range_sort::xlSortOnIcon;
        break;

    default:
        assert(false);
    }

    acpt->addUint32("sortOn", val);
}

static void WriteSortOrder(ISerialAcceptor *acpt, const int32_t order)
{
    int32_t val = order;
    switch (order)
    {
    case ::etAscending:
        val = range_sort::etAscending;
        break;
    case ::etDescending:
        val = range_sort::etDescending;
        break;
    default:
        assert(false);
    }

    acpt->addUint32("order", val);
}

static void WriteCustomOrder(ISerialAcceptor *acpt, const QStringList &custom_order)
{
    if (custom_order.count() == 0)
        return;

    acpt->addString("customOrder", krt::utf16(custom_order.join(QString(','))));
}

void WriteSortField(ISerialAcceptor *acpt, int32_t sort_on, int32_t sort_order, const QStringList &custom_order)
{
    WriteSortOn(acpt, sort_on);
    WriteSortOrder(acpt, sort_order);
    WriteCustomOrder(acpt, custom_order);
}

bool WriteSortOnValue(IBook *book, ISerialAcceptor *acpt, int32_t sort_on, ks_stdptr<ICellColor> &spCellColor,
                      ks_stdptr<IFontColor> &spFontColor, ks_stdptr<ICellIcon> &spCellIcon)
{
    switch (sort_on)
    {
    case ::xlSortOnValues:
        return true;
    case ::xlSortOnCellColor:
    {
        const EtFill *pFill = NULL;
        if (!spCellColor || FAILED(spCellColor->GetFill(&pFill)) || (pFill == NULL))
            return false;

        WriteFill(acpt, *pFill, book, "cellFill");
        return true;
    }
    case ::xlSortOnFontColor:
    {
        const EtColor *clr = NULL;
        if (!spFontColor || FAILED(spFontColor->GetColor(&clr)) || (clr == NULL))
            return false;

        WriteColor(acpt, *clr, book, "cellFontColor");
        return true;
    }
    default:
        assert(false);
    }
    return false;
}

void WriteSortKeyRange(ISerialAcceptor *acpt, ETSortOrientation sortOrientation, const RANGE& range) 
{
    acpt->addKey("keyRange");
	acpt->beginStruct();
	acpt->addUint32("rowFrom", range.RowFrom());
	acpt->addUint32("rowTo", range.RowTo());
	acpt->addUint32("colFrom", range.ColFrom());
	acpt->addUint32("colTo", range.ColTo());
	acpt->endStruct();

    if (::etSortRows == sortOrientation) {
        acpt->addUint32("key", range.RowFrom());
    } else if (::etSortColumns == sortOrientation) {
        acpt->addUint32("key", range.ColFrom());
    }
}

} // namespace range_sort

} // namespace wo