好的，这是严格根据您提供的 exec_detail.cpp 文件内容，对 m_root 中的可用命令和参数重新进行的分类汇总，以多级大纲形式呈现：
1. 核心执行命令 (ExecCmd)
•	undo: 撤销操作
•	undoStep: 撤销步数
•	versionTag: 版本标签
•	allowPartialSucc: 是否允许部分成功
•	redo: 重做操作
•	redoStep: 重做步数
•	versionTag: 版本标签
•	commands: 一个或多个命令组成的数组，是执行具体操作的主要方式
•	transOpByServer: 标记操作是否由服务端转换
•	transDataBase: 数据基线版本
•	transTaskBase: 任务基线版本
2. 查询与数据获取 (ExecQuery, ExecBlock)
•	extendQueryCmds: 扩展查询命令数组，用于执行各种数据查询
•	async: 标记查询是否为异步
•	serializeDelta: 标记响应中是否序列化增量数据
•	blockPointsVersion: 客户端已有的数据块版本
•	newBlocks: 客户端请求的新数据块
•	blocks: 客户端请求的数据块 (用于 ExecBlock)
•	visibleBlocks: 初始化时可见的数据块
•	dbVisibleBlocks: 初始化时可见的数据库视图数据块
•	monitorRangesWhenInit: 初始化时需要监控的范围
3. 导出与文件操作
•	通用导出参数
•	fileName / filePath: 导出文件的名称或路径
•	sheetStId / sheetIndex / objSheet: 指定要导出的工作表
•	isPrintExport: 是否为打印目的的导出
•	maxNumberOfPages: 导出页数上限
•	PDF 导出 (ExecExportPdf)
•	fullImage: 是否以整张图片的形式导出
•	calcBeforeExport: 导出前是否强制重算
•	includeDocProperties: 是否包含文档属性
•	includeComments: 是否包含评论
•	includeHyperlinks: 是否包含超链接
•	masterPassword / userPassword: 设置密码
•	watermark: 水印参数
•	type, value, fontFamily, fontSize, fillstyle, rotate, horizontal, vertical
•	图片导出 (ExecExportImg)
•	format: 图片格式 (e.g., "PNG")
•	dpi: 分辨率
•	waterMark: 是否添加水印
•	combine2LongPic: 是否合并为长图
•	firstPage: 是否只导出第一页
•	scale / quality: 缩放与质量
•	SVG 导出 (ExecExportSvg)
•	zoom: 缩放比例
•	width / height: 导出尺寸
•	row / col: 左上角单元格
•	exportRange: 导出的单元格范围
•	device_type: 设备类型 (e.g., "mobile")
•	XLSX / KSheet / CSV / TXT 导出
•	copyContent: 是否复制内容 (用于 ExecExportXlsx)
•	withAttachment: 是否包含附件 (用于 ExecExportXlsx)
•	delimiter: 分隔符 (用于 ExecExportTxt)
•	skipBlankCols: 是否跳过空列 (用于 ExecExportSheetToCSV)
•	应用（小程序）导出 (ExecExportAirApp)
•	newSharedId: 新的分享ID
4. 特定功能命令
•	图片与附件
•	images: 获取图片信息 (ExecGetImages)
•	image_id, token
•	sheetIdVec: 批量查询工作表的附件 (ExecSheetsAttachments)
•	图形与评论
•	objSheetOrBook / shapeIds: 更新图形 (ExecUpdateShapes)
•	commands 中 name 为 range.xxxCommentxxx 的命令 (ExecComment)
•	XVA 扩展命令 (ExecExtraCommand)
•	command: 命令名称
•	param: 命令参数
•	文件拆分 (SplitToNewFile)
•	distribute: 是否为分发模式
•	sheetIdx / rowFrom: 局部分发的范围
5. 初始化与上下文参数
•	extra: 附加信息
•	activeSheet: 激活的工作表名
•	activeViewId: 激活的数据库视图ID
•	coop_ver: 协作版本
•	end: 客户端类型
•	visitingViews: 用户正在访问的数据库视图
•	isAllMutableRes: 是否返回所有可变单元格结果
•	taskVer / dataVer: 客户端的基线版本
•	fmlaResVer / eafResVer / recalcResVer: 公式、外部函数、重算结果的版本
6. 控制与元数据
•	seq: 命令序列号
•	httpCalling: 是否为 HTTP API 调用
•	traceId: 用于追踪请求的ID
•	callBackId: 回调ID
•	isMasterProc: 是否在主进程执行
•	ignoreProtect: 是否忽略保护