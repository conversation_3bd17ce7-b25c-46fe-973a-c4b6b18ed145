﻿#ifndef __WORKBOOKS_H__
#define __WORKBOOKS_H__

#include "etstdafx.h"
#include "etshared/etshareapplication.h"
#include "etentrys.h"
#include "workbook.h"

interface IStaleNotify;
namespace wo
{
class KEtRevisionControl;
class EtTaskExecutor;
class KEtOpTrsfmt;

class KEtWorkbooks
{
public:
	KEtWorkbooks(et::KConApplication*, WebLogFunc);
	~KEtWorkbooks();
public:
	int		Open(QString filePath, QString fileName, bool ReadOnly,
		QString passwd, QString modifyPasswd, QString fileId, int32 cloudVer, QString userID, bool bWoAutoslim);
	void	SetApp(etoldapi::_Application *app);
	int		Close(bool SaveChanges);
	void	New();
    void	ConnectCloudVer(int32 cloudVer, int32 taskVer);

	KEtWorkbook*	GetWorkbook();

	etoldapi::_Application* GetCoreApp() {
		return m_ptrApp;
	}
public:
	void notifyStale(const WebID objId);
	void setStaleNotify(IStaleNotify* pNotify);
private:
	et::KConApplication* m_consoleApp;
	std::unique_ptr<KEtWorkbook>	m_Workbook;

	ks_stdptr<Workbooks>			m_ptrWorkbooks;
	ks_stdptr<etoldapi::_Application>			m_ptrApp;
	ks_stdptr<etoldapi::_Workbook>			m_ptrWorkbook;
	WebLogFunc						m_logFunc;
	IStaleNotify	*m_pStaleNotify;
};
}

#endif // __WORKBOOKS_H__
