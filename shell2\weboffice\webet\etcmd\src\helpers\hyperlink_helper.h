﻿#ifndef __HYPERLINK_HELPER_H__
#define __HYPERLINK_HELPER_H__


#include "webbase/binvariant/binvarobj.h"


namespace etoldapi{
interface  Range;
interface  _Worksheet;
}
using etoldapi::Range;
using etoldapi::_Worksheet;
interface IKCoreObject;

namespace wo {
interface IEtRevisionContext;


namespace Database{
  class FieldContext;
}


class AutoFixToHyperlinkHelper
{
public:

  static HRESULT AutoFix(binary_wo::VarObj param, BSTR bstrCellValue, Range *pCellRg, bool &bURL, IEtRevisionContext *ctx);


  static HRESULT EnsureDbHyperlinkFields(Database::FieldContext *pContext,BSTR bstrCellValue, Range *pRange);

  static HRESULT AddHyperlink(_Worksheet* pWorksheet,IKCoreObject *pRgOrShape, <PERSON><PERSON> bstrAddress,VARIANT varSubAddress, VARIANT varScreenTip,VARIANT varDispText);

private:

  static BOOL IsSameRange(Range *pRg1, Range *pRg2);

  AutoFixToHyperlinkHelper(){}

};

}


#endif
