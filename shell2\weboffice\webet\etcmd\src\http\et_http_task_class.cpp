﻿#include "etstdafx.h"
#include "et_http_task_class.h"
#include "et_task_class.h"
#include "workbook.h"
#include "http_error.h"
#include "webbase/serialize_impl.h"
#include "webbase/wo_sa_helper.h"
#include "et_revision_context_impl.h"
#include "et_binvar_spec.h"
#include "et_task_detail_xf.h"
#include "applogic/et_apihost.h"
#include "helpers/webmime_helper.h"
#include "et_task_peripheral.h"
#include "applogic/cell_image_helper.h"
#include "helpers/varobject_helper.h"
#include <drawing/model/abstract_shape.h>
#include "utils/attachment_utils.h"
#include "helpers/hyperlink_helper.h"
#include "helpers/pivot_tables_helper.h"
#include "util.h"
#include "utils/file_merge_utils.h"
#include "helpers/protection_helper.h"
#include "etcore/little_alg.h"
#include "kso/l10n/et/et_app.h"
#include "appcore/et_appcore_webhook.h"
#include "helpers/sheet_operator_helper.h"
#include "helpers/col_operator_helper.h"
#include "autoupdateutil/mergefile_util.h"
#include "database/database_field_context.h"
#include "database/database_utils.h"
#include "database/database_field_modifier.h"
#include "helpers/jsapi_helper.h"
#include "api/jsapi/jde/jde_errorcode.h"
#include "api/jsapi/jde/jside.h"
#include "wobatchtrans.h"
#include "context_init_helper.h"
#include "helpers/jsapi_record.h"
#include "helpers/jsapi_context.h"
#include "helpers/table_struct_rec_helper.h"
#include "helpers/db_field_copy_helper.h"
#include "helpers/autofilter_helper.h"

#include "dataanalyze/et_dataanalyze.h"
#include "dataanalyze/et_dataanalyze_errorinfo.h"
#include "dataanalyze/etdacoreexport.h"
#include "wo_data_analyze/data_analyze_helper.h"
#include "wo_data_analyze/data_analyze.h"
#include "kso/l10n/et/etshell.h"
extern Callback* gs_callback;

namespace wo
{
HRESULT EtHttpTaskClassBase::PreExecute(KwCommand *pCmd, KEtRevisionContext *pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    PCWSTR traceId = __X("");
    if (param.has("traceId"))
        traceId = param.field_str("traceId");
    ks_stdptr<IWebhookManager> spWebhookMgr;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
    pRange->SetTraceId(traceId);

    //检测是否是操作的db表， db数据表不允许调用
    IDX sheetIdx = GetSheetIdx(param);
    if (sheetIdx >= 0)
    {
        HRESULT hr = CheckDBSheet(sheetIdx);
        if (FAILED(hr))
            return hr;
    }

    return EtTaskExecBase::PreExecute(pCmd, pCtx);
}

HRESULT EtHttpTaskClassBase::PostExecute(HRESULT hr, KwCommand *pCmd, KEtRevisionContext *pCtx)
{
    pCtx->postExecute(hr);

    if (FAILED(hr))
    {
        binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
        KSerialWrapBinWriter acpt(*pResponse, pCtx);
        AddHttpError(hr, &acpt, m_errMsg.empty() ? nullptr : m_errMsg.c_str());
        m_errMsg.clear();
    }
    return EtTaskExecBase::PostExecute(hr, pCmd, pCtx);
}

HRESULT EtHttpTaskClassBase::CheckDBSheet(IDX sheetIdx)
{
    IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    if (pWorksheet == NULL) 
    {
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ks_stdptr<ISheet> spSheet = pWorksheet->GetSheet();
    if (spSheet == NULL) 
    {
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    if (spSheet->IsDbSheet())
    {
        return E_KSHEET_NOT_GRIDSHEET;
    }
    return S_OK;
}

// ================== EtHttpUpdateRangeDataTaskClass ==================
EtHttpUpdateRangeDataTaskClass::EtHttpUpdateRangeDataTaskClass(KEtWorkbook* wwb)
    : EtHttpTaskClassBase(wwb)
{
}

HRESULT EtHttpUpdateRangeDataTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    HRESULT hr = S_OK;
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    VAR_OBJ_EXPECT_ARRAY(param, "rangeData")
    binary_wo::VarObj rgData = param.get_s("rangeData");

    for (int i = 0; i < rgData.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = rgData.at_s(i);
        RANGE rg = ReadRangeInl(m_wwb->GetBMP(), sheetIdx, item);

        auto accessPerm = PTAAP_None;
        if (!pCtx->getProtectionCtx()->isAllowEdit(rg, &accessPerm))  
        {
            WO_LOG_X(pCtx->getLogger(), WO_LOG_INFO, "[range.update] range list index %d is not allowedit",i);
            return accessPerm == PTAAP_Exclusive ? E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE : E_NOT_HAVE_PERMISSION;
        }

        ks_stdptr<etoldapi::Range> spRange = m_wwb->CreateRangeObj(rg);
        if(!spRange)
            return E_INVALID_REQUEST;
        
        VAR_OBJ_EXPECT_STRING(item, "opType")
        WebStr type = item.field_str("opType");
        if(xstrcmp(__X("formula"), type) == 0)
        {
            VAR_OBJ_EXPECT_STRING(item, "formula")
            hr = handleFormula(rg, spRange.get(), item, pCtx);
        }
        else if (xstrcmp(__X("format"), type) == 0) 
        {
            binary_wo::VarObj varXf = item.get("xf");
            KXF xf;
            KXFMASK mask;
            DecodeXf(varXf, xf, mask);
            BorderXF borderXf;
            HRESULT hrDecode = DecodeBorderXf(varXf, borderXf);
            if (hrDecode)
            {
                hr = _SetRangeBorderXf(spRange, borderXf);
            }

            if (!mask.IsMaskNone())
            {
                ks_stdptr<IFormatHost> host = spRange;
                hr = host->SetXF(&mask, &xf);
            }
        }
        else if	(xstrcmp(__X("merge"), type) == 0)
        {
            ks_wstring strType = __X("MergeSelection");
            if (item.has("type"))
                strType = item.field_str("type");

            ks_stdptr<IRangeInfo> spRangeInfo = spRange;
            hr = spRangeInfo->QueryCanMerge();
            if (FAILED(hr))
                return E_INVALID_REQUEST;
            hr = E_FAIL;

            if (strType == __X("MergeCenter")){
                hr = spRange->Merge(VARIANT_FALSE);
                hr = spRange->put_HorizontalAlignment(etHAlignCenter);
            } else if (strType == __X("MergeSelection"))
                hr = spRange->Merge(VARIANT_FALSE);
            else if (strType == __X("MergeColumns"))
                hr = spRange->Merge(VARIANT_TRUE);
            else if (strType == __X("MergeContent"))
                hr = spRange->MergeContent();
            else if (strType == __X("MergeSame"))
                hr = spRange->MergeSame();
            else if (strType == __X("ColumnsCenter")){
                ETHAlign eth = etHAlignGeneral;
                spRange->get_HorizontalAlignment(&eth);
                if (etHAlignCenterAcrossSelection == eth)
                    eth = etHAlignGeneral;
                else
                    eth = etHAlignCenterAcrossSelection;
                hr = spRange->put_HorizontalAlignment(eth);
            }
        }
        else if (xstrcmp(__X("picture"), type) == 0)
        {
            hr = handlePicture(rg, item, cmd, pCtx);
        }
    }

    return hr;
}


HRESULT EtHttpUpdateRangeDataTaskClass::handleFormula(const RANGE & rg,etoldapi::Range* rangeApi,binary_wo::VarObj& item,KEtRevisionContext* ctx)
{
    PCWSTR fmla = item.field_str("formula");
    ks_stdptr<IRangeInfo> host = rangeApi;
    CELL activeCell = {rg.RowFrom(), rg.ColFrom()};
    host->ExtendListObject(activeCell);
    HRESULT hr = host->SetFormula(fmla, &rg);

    if (S_OK == hr && fmla && xstrlen(fmla) > 0 )
    {
        ks_stdptr<Range> spCellRange = host;
        ks_bstr bstrCellValue(fmla);
        bool bUrl = false;
        AutoFixToHyperlinkHelper::AutoFix(item, bstrCellValue, spCellRange, bUrl, ctx);
        if (!bUrl)
        {
            Database::FieldContext fieldContext(m_wwb, nullptr);
            AutoFixToHyperlinkHelper::EnsureDbHyperlinkFields(&fieldContext, bstrCellValue, spCellRange);
        }
    }
    return hr;
}

HRESULT EtHttpUpdateRangeDataTaskClass::handlePicture(const RANGE & rg, binary_wo::VarObj& item, KwCommand*cmd, KEtRevisionContext* pCtx)
{
    HRESULT hr = S_OK;
    ks_stdptr<_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    if (!spWorksheet)
        return E_INVALID_REQUEST;
    
    single width = -1;
    single height = -1;
    ks_wstring uuid;

    binary_wo::VarObj picObj = item.get_s("cellPicInfo");
    binary_wo::VarObj widthVar = picObj.get_s("width");
    if (widthVar.type() != typeInvalid)
        width = widthVar.value_double();
    binary_wo::VarObj heightVar = picObj.get_s("height");
    if (heightVar.type() != typeInvalid)
        height = heightVar.value_double();

    if (picObj.has("uuid"))
        uuid = picObj.field_str("uuid");

    ks_stdptr<IKShape> spShape;
    WebStr tag = picObj.field_str("tag");
    if (xstrcmp(__X("local"), tag) == 0)
    {
        WebMimeHelper wmh;
        wmh.cmd = cmd;
        wmh.ctx =pCtx; 
        wmh.objParam = picObj;
        wmh.strField = "uploadId";

        // 生成的uuid需要存起来, 不然回放的时候会不一致
        hr = spWorksheet->InsertCellPicture(rg.RowFrom(), rg.ColFrom(), krt::utf16(wmh.resolvePath()), uuid, NULL, width, height, FALSE, &spShape);
    }
    else if (xstrcmp(__X("attachment"), tag) == 0)
    {
        PCWSTR attachmentId = picObj.field_str("attachmentId");
        if (!attachmentId || *attachmentId == __Xc('\0'))
        {
            WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "opType attachment attachmentId is empty");
            return E_INVALID_REQUEST;
        }
        
        PCWSTR strPath = NULL;
        QString strPathData;
        const char* szPath = NULL;

        WebDownloadArg downloadImgArg;
        QByteArray strAttachmentUtf8 = QString::fromUtf16(attachmentId).toUtf8();
        downloadImgArg.pObjectLocator = strAttachmentUtf8;
        downloadImgArg.tag = WebDownloadByAttachmentId;

        WebInt errCode = gs_callback->getDownloadedObjectPath(&downloadImgArg, &szPath);

        if (errCode != WO_OK || szPath == NULL)
        {
            if (!pCtx->isExecDirect())
            {
                EtTaskPeripheral* peripheral = GetPeripheral();
                if (peripheral)
                {
                    binary_wo::VarObj resObj = peripheral->resSelf();
                    resObj.add_field_int32("downloadErrCode", errCode);
                }
            }
            WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "download picture error");
            return hr;
        }
        else
        {
            strPathData = QString::fromUtf8(szPath);
            strPath = krt::utf16(strPathData);
        }

        ks_wstring fakeUrl = util::generateFakeAttachmentUrl(attachmentId);
        hr = spWorksheet->InsertCellPicture(rg.RowFrom(), rg.ColFrom(), strPath, uuid, fakeUrl.c_str(), width, height, FALSE, &spShape);
        if (SUCCEEDED(hr) && !pCtx->isExecDirect())
        {
            if (width < 0 || height < 0)
            {
                SIZE sz = cellimg::GetImageSize(spShape);
                pCtx->setIsRealTransform(true);
                picObj.add_field_double("width", sz.cx);
                picObj.add_field_double("height", sz.cy);
            }
        }

    }
    else if	(xstrcmp(__X("url"), tag) == 0)
    {
        ks_wstring strUrl = picObj.field_str("url");
        PCWSTR strPath = NULL;
        QString strPathData;
        const char* szPath = NULL;

        WebDownloadArg downloadImgArg;
        QByteArray strUrlUtf8 = QString::fromUtf16(strUrl.c_str()).toUtf8();
        downloadImgArg.pObjectLocator = strUrlUtf8;
        downloadImgArg.tag = WebDownloadByUrl;

        WebInt errCode = gs_callback->getDownloadedObjectPath(&downloadImgArg, &szPath);
        if (errCode != WO_OK || szPath == NULL)
        {
            if (!pCtx->isExecDirect())
            {
                EtTaskPeripheral* peripheral = GetPeripheral();
                if (peripheral)
                {
                    binary_wo::VarObj resObj = peripheral->resSelf();
                    resObj.add_field_int32("downloadErrCode", errCode);
                }
            }
            WO_LOG_X(pCtx->getLogger(), WO_LOG_ERROR, "download picture error");
            return hr;
        }
        else
        {
            strPathData = QString::fromUtf8(szPath);
            strPath = krt::utf16(strPathData);
        }

        hr = spWorksheet->InsertCellPicture(rg.RowFrom(), rg.ColFrom(), strPath, uuid, strUrl.c_str(), width, height, FALSE, &spShape);
        if (SUCCEEDED(hr) && !pCtx->isExecDirect())
        {
            if (width < 0 || height < 0)
            {
                SIZE sz = cellimg::GetImageSize(spShape);
                pCtx->setIsRealTransform(true);
                picObj.add_field_double("width", sz.cx);
                picObj.add_field_double("height", sz.cy);
            }
        }
    }
    

    if (SUCCEEDED(hr) && spShape)
    {
        ks_castptr<drawing::AbstractShape> cpShape = spShape;
        exportShapeImage(m_wwb, pCtx, picObj, cpShape);
        pCtx->setIsRealTransform(true);
        WCHAR name[256] = {0};
        spShape->GetName(name);
        picObj.add_field_str("cellPicName", name);
        picObj.add_field_str("uuid", uuid.c_str());
    }

    return hr;
}

void EtHttpUpdateRangeDataTaskClass::exportShapeImage(KEtWorkbook* wwb, KEtRevisionContext* ctx, VarObj& param, drawing::AbstractShape* pShape)
{
    QByteArray sha1, picData;
    int16 dpi = ctx->getUser()->getDpi();
    if (pShape->getPicture(sha1, picData, dpi / 96.0) != WO_OK)
        return;

    param.add_field_str("sha1", krt::utf16(QString(sha1)));
    ctx->setIsRealTransform(true);
    wwb->exportShapePicture(sha1.data(), picData.data(), picData.size());
}

PCWSTR EtHttpUpdateRangeDataTaskClass::GetTag()
{
    return __X("http.et.updateRangeData");
}


EtHttpAddRowTaskClass::EtHttpAddRowTaskClass(KEtWorkbook* wwb)
    : EtHttpUpdateRangeDataTaskClass(wwb)
{
}

HRESULT EtHttpAddRowTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    HRESULT hr = S_OK;
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    VAR_OBJ_EXPECT_ARRAY(param, "rangeData")
    binary_wo::VarObj rgData = param.get_s("rangeData");

    ks_stdptr<IKWorksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    BMP_PTR bmp = spWorksheet->GetSheet()->GetBMP();
    RANGE rg(bmp);
    spWorksheet->GetUsedRange(&rg);
    ROW insertRow = rg.RowTo() + 1;
    if (insertRow >= bmp->cntRows)
    {
        return E_INVALID_REQUEST;
    }

    rg.SetRowFromTo(insertRow,insertRow);
    COL col = -1;
    for (int i = 0; i < rgData.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = rgData.at_s(i);
        if(item.has("col"))
            col = item.field_int32("col");
        else
            ++col;
        if (col < 0 || col >= bmp->cntCols)
            return E_INVALID_REQUEST;

        rg.SetColFromTo(col, col);
        auto accessPerm = PTAAP_None;
        if (!pCtx->getProtectionCtx()->isAllowEdit(rg, &accessPerm))  
        {
            return accessPerm == PTAAP_Exclusive ? E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE : E_NOT_HAVE_PERMISSION;
        }

        ks_stdptr<etoldapi::Range> spRange = m_wwb->CreateRangeObj(rg);
        if(!spRange)
            return E_INVALID_REQUEST;
        
        VAR_OBJ_EXPECT_STRING(item, "opType")
        WebStr type = item.field_str("opType");
        if(xstrcmp(__X("formula"), type) == 0)
        {
            VAR_OBJ_EXPECT_STRING(item, "formula")
            hr = handleFormula(rg, spRange.get(), item, pCtx);
        }
        else if (xstrcmp(__X("picture"), type) == 0)
        {
            hr = handlePicture(rg, item, cmd, pCtx);
        }
    }

    return hr;
}


PCWSTR EtHttpAddRowTaskClass::GetTag()
{
    return __X("http.et.addRow");
}


// ================== EtHttpSetRangeWidthHeight ==================
EtHttpSetRangeWidthHeightTaskClass::EtHttpSetRangeWidthHeightTaskClass(KEtWorkbook* wwb)
    : EtHttpTaskClassBase(wwb)
{
}

HRESULT EtHttpSetRangeWidthHeightTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

    HRESULT hr = S_OK;
    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    VAR_OBJ_EXPECT_STRUCT(param, "range")
    binary_wo::VarObj varRange = param.get("range");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")
	RANGE rg = ReadRangeInl(m_wwb->GetBMP(), sheetIdx, varRange);
	if (!rg.IsValid()) 
        return E_INVALID_REQUEST;

    auto accessPerm = PTAAP_None;
    if (!pCtx->getProtectionCtx()->isAllowEdit(rg, &accessPerm))  
    {
        return accessPerm == PTAAP_Exclusive ? E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE : E_NOT_HAVE_PERMISSION;
    }
    ks_stdptr<etoldapi::Range> spRange = m_wwb->CreateRangeObj(rg);
	if (!spRange) 
		return E_INVALID_REQUEST;

    if(param.has("height"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "height")
        double height = param.field_double("height") / 20.0;
        KComVariant var(height);
	    hr = spRange->put_RowHeight(var);
    }
	if(param.has("width")) 
    {
        // 传入单位是缇，内核api单位是字符数，需要进行转换
        VAR_OBJ_EXPECT_NUMERIC(param, "width")
		double width = app_helper::GetCharsWithColWidth(m_wwb->GetCoreWorkbook(), param.field_double("width"));
        KComVariant var(width);
        hr = spRange->put_ColumnWidth(var);
    }

	return hr;
}

PCWSTR EtHttpSetRangeWidthHeightTaskClass::GetTag()
{
    return __X("http.et.setRangeWidthHeight");
}


// ================== EtHttpPivotTableRefreshTaskClass ==================
EtHttpPivotTableRefreshTaskClass::EtHttpPivotTableRefreshTaskClass(KEtWorkbook* wwb)
    : EtHttpTaskClassBase(wwb)
{
}

HRESULT EtHttpPivotTableRefreshTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

    HRESULT hr = S_OK;
    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    VAR_OBJ_EXPECT_STRUCT(param, "range")
    binary_wo::VarObj varRange = param.get("range");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")
	RANGE rg = ReadRangeInl(m_wwb->GetBMP(), sheetIdx, varRange);
	if (!rg.IsValid()) 
        return E_INVALID_REQUEST;

	ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(sheetIdx);
	if (S_FALSE == pSheetProtection->IsAllowed(et_appcore::atPivotTable, NULL, NULL))
	{
		return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;
	}


    ks_stdptr<etoldapi::Range> spRange = m_wwb->CreateRangeObj(rg);
	if (!spRange) 
		return E_INVALID_REQUEST;


    ks_stdptr<pivot_core::IPivotTable> spCorePivotTable;
    ks_stdptr<etoldapi::PivotTable> spPivotTable;

    ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    if (!spWorksheet)
	{
        return E_FAIL;
	}

    pivot_helper::GetCorePivotTableByCell(spRange, &spCorePivotTable);
    if (!spCorePivotTable)
    {
        return E_FAIL;
    }

    PivotHelpers::GetApiPivotTable(spWorksheet, spCorePivotTable, &spPivotTable);
    if (!spPivotTable)
	{
        return E_FAIL;
	}

    auto sectionRanges = PivotHelpers::getPivotTableSectionRanges(spWorksheet->GetSheet(), spCorePivotTable);
    for (auto it = sectionRanges.begin(); it != sectionRanges.end(); ++it)
    {
        if (pCtx->getProtectionCtx()->isRangeHasHidden(*it))
        {
            return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
        }
    }

	util::CallTimeStat stat(nullptr, nullptr, [&hr, &spCorePivotTable,pCtx](unsigned int ms) -> void {
		if (SUCCEEDED(hr) && !pCtx->isExecDirect())
		{
           	pivot_core::IPivotCache* cache = spCorePivotTable->GetPivotCache(); 
            for (UINT i = 0; i < cache->GetPivotTableCount(); ++i)
            {
                pivot_core::IPivotTable*  pTbl = cache->GetPivotTable(i);
                pTbl->SetUpdateTime(ms);
            }
		}		
	});

	PivotTableHelper helper(m_wwb->GetCoreWorkbook(), spWorksheet, spCorePivotTable, spPivotTable);
	hr = helper.refresh();

	return hr;
}

PCWSTR EtHttpPivotTableRefreshTaskClass::GetTag()
{
    return __X("http.et.pivotTableRefresh");
}

// ================== EtHttpSheetAddHyperlinkTaskClass ==================
EtHttpSheetAddHyperlinkTaskClass::EtHttpSheetAddHyperlinkTaskClass(KEtWorkbook* wb)
    : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpSheetAddHyperlinkTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId")
    UINT sheetStId = param.field_uint32("sheetStId");

    HRESULT hr = S_OK;
    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    VAR_OBJ_EXPECT_STRUCT(param, "anchorRange")
    binary_wo::VarObj varRange = param.get("anchorRange");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")
    RANGE rg = ReadRangeInl(m_wwb->GetBMP(), sheetIdx, varRange);
    if (!rg.IsValid()) 
        return E_INVALID_REQUEST;

    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(sheetIdx);
    if (S_FALSE == pSheetProtection->IsAllowed(et_appcore::atPivotTable, NULL, NULL))
    {
        return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;
    }

    IKWorksheet* pSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    ks_stdptr<_Worksheet> spWorksheet = pSheet;
    if (!spWorksheet)
        return E_INVALID_REQUEST;

    ks_stdptr<IKCoreObject> spAnchor;
    ks_stdptr<etoldapi::Range> spRange = m_wwb->CreateRangeObj(rg);
    if (!spRange) 
        return E_INVALID_REQUEST;

    if (spRange->hasHyperlinksWithRuns())
    {
        return E_OPERATION_NOT_SUPPORTED_ON_RANGE_WITH_HYPERLINK_RUNS;
    }

    if (ctx->isInRevisionMode())
        m_wwb->setLastTaskSelection(varRange);

    spAnchor = spRange;
    if (NULL != m_peri)
    {
        std::vector<RANGE> rgsTmp;
        rgsTmp.push_back(rg);
        m_peri->setEffectedRg(rgsTmp);
    }

    ks_stdptr<IBookOp> spBookOp;
    m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
    util::CalcBatchUpdate calcBatchUpdate(spBookOp, m_wwb->GetCoreWorkbook()->GetBook()->GetWoStake()->HasImportrangeFuncs());

    ks_bstr bstrAddress;
    if (param.has("address"))
        bstrAddress.assign(param.field_str("address"));
    else
        return E_INVALID_REQUEST;

    KComVariant varDispText;
    if (param.has("textToDisplay"))
        varDispText.AssignString(param.field_str("textToDisplay"));
    else
        return E_INVALID_REQUEST;

    return AutoFixToHyperlinkHelper::AddHyperlink(spWorksheet,
                spAnchor,
                bstrAddress,
                KComVariant(),
                KComVariant(),
                varDispText);
}

PCWSTR EtHttpSheetAddHyperlinkTaskClass::GetTag()
{
    return __X("http.et.addHyperlink");
}


// ================== EtHttpSheetSetNameTaskClass ==================
EtHttpSheetSetNameTaskClass::EtHttpSheetSetNameTaskClass(KEtWorkbook* wb)
    : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpSheetSetNameTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    VAR_OBJ_EXPECT_STRING(param, "name")
    UINT sheetStId = param.field_uint32("sheetId");
    ks_bstr strName(param.field_str("name"));

    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    ks_stdptr<_Worksheet> spSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!spSheet)
		return E_FAIL;

	if (isCellImgListSheet(sheetIdx))
		return E_FAIL;

	if (!etexec::IsValidSheetName(strName.c_str()))
	{
		return E_INVALID_CHAR;
	}

	IDX idx = INVALIDIDX;
	if (SUCCEEDED(pBook->GetSheetIdxByName(strName.c_str(), &idx)) && idx != INVALIDIDX &&  idx != sheetIdx)
	{
	    return E_NAME_CONFLICT;
	}

	if (xstricmp(strName.c_str(), TX_History_Reserved_SheetName) == 0)
	{
		return E_FAIL;
	}

	return spSheet->put_Name(strName);
}

PCWSTR EtHttpSheetSetNameTaskClass::GetTag()
{
    return __X("http.et.setSheetName");
}

// ================== EtHttpSheetsAddTaskClass ==================
EtHttpSheetsAddTaskClass::EtHttpSheetsAddTaskClass(KEtWorkbook* wb)
    : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpSheetsAddTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    if (m_wwb->GetBMP()->bDbSheet)
        return  E_API_NOT_SUPPORTED_ON_FILE;
    binary_wo::VarObj param = cmd->cast().get("param");
    ks_stdptr<IKCoreObject> spCoreObj;
    HRESULT hr = SheetOperatorHelper::AddSheet(m_wwb, param, &spCoreObj);
    if (FAILED(hr))
        return hr;

    ks_stdptr<etoldapi::_Worksheet> spWorksheet = spCoreObj;
    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet)
        return E_FAIL;
    if (param.has("defColWidth"))
        pSheet->SetDefColWidth(param.field_double("defColWidth"));

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

    acpt.addInt32("sheetId", pSheet->GetStId());
    return hr;
}

PCWSTR EtHttpSheetsAddTaskClass::GetTag()
{
    return __X("http.et.addSheet");
}

HRESULT EtHttpSheetsAddTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isConnSharedLink())
		return E_OPERATION_NOT_SUPPORTED_ON_SHARED_SHEET;
	return S_OK;
}

// ================== EtHttpSheetsCopyTaskClass ==================
EtHttpSheetsCopyTaskClass::EtHttpSheetsCopyTaskClass(KEtWorkbook* wb)
    : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpSheetsCopyTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    if (m_wwb->GetBMP()->bDbSheet)
        return  E_API_NOT_SUPPORTED_ON_FILE;
    binary_wo::VarObj param = cmd->cast().get("param");
    ks_stdptr<IKCoreObject> spCoreObj;
    HRESULT hr = SheetOperatorHelper::CopySheet(m_wwb, param, &spCoreObj);
    if (FAILED(hr))
        return hr;

    ks_stdptr<etoldapi::_Worksheet> spWorksheet = spCoreObj;
    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet)
        return E_FAIL;

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

    acpt.addUint32("sheetId", pSheet->GetStId());
    return hr;
}

PCWSTR EtHttpSheetsCopyTaskClass::GetTag()
{
    return __X("http.et.copySheet");
}

HRESULT EtHttpSheetsCopyTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isConnSharedLink())
		return E_OPERATION_NOT_SUPPORTED_ON_SHARED_SHEET;
	return S_OK;
}

// ================== EtHttpCopySheetsFromBookTaskClass ==================
EtHttpCopySheetsFromBookTaskClass::EtHttpCopySheetsFromBookTaskClass(KEtWorkbook* wb)
    : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpCopySheetsFromBookTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    if (m_wwb->GetBMP()->bDbSheet)
        return  E_API_NOT_SUPPORTED_ON_FILE;
    binary_wo::VarObj param = cmd->cast().get("param");

    _Workbook* ptrWorkbookDst = m_wwb->GetCoreWorkbook();

    ks_stdptr<Workbooks> ptrWorkbooks;
	m_wwb->GetCoreApp()->get_Workbooks(&ptrWorkbooks);
    ks_wstring filePath;
    if (param.has("fileId") && xstrlen(param.field_str("fileId")))
    {
        WebStr fileId = param.field_str("fileId");
        QString strUserId = QString::fromUtf16(ctx->getUser()->userID());
        const char *pPath = nullptr;
        const char *pVersion = nullptr;
        const char *pFileName = nullptr;

        QString strFileId = QString::fromUtf16(fileId);
        NetFileRes res = gs_callback->getNetFile(strFileId.toUtf8(), strUserId.toUtf8(), &pPath, &pVersion, &pFileName);
        if (res != NetFileRes_OK)
        {
            WOLOG_INFO << "[CopySheetsFromBook] getNetFile error: " << res;
            return E_FAIL;
        }
        filePath = krt::utf16(QString(pPath));
    }
    else if (param.has("filePath") && xstrlen(param.field_str("filePath")))
    {
        filePath = param.field_str("filePath");
    }
    else
    {
        return E_INVALIDARG;
    }

    _Workbook* ptrWorkbookSrc = util::OpenFile(m_wwb, ptrWorkbooks, filePath);
    if (!ptrWorkbookSrc)
    {
         WOLOG_INFO << "[CopySheetsFromBook] Open workbook error";
         return E_FAIL;
    }

    std::vector<int> vecSheetIdx;
    if (param.has("copyFirstSheet") && param.field_bool("copyFirstSheet"))
    {
        int sheetCount = ptrWorkbookSrc->GetWorksheets()->GetSheetCount();
        for (int sheetIdx = 0; sheetIdx < sheetCount && vecSheetIdx.empty(); ++sheetIdx)
        {
            if (isCellImgListSheet(sheetIdx))
                continue;
            vecSheetIdx.push_back(sheetIdx);
        }
    }
    else
    {
        IBook* pSrcBook = ptrWorkbookSrc->GetBook();
        binary_wo::VarObj sheetIds = param.get_s("sheetIds");
        for (int i = 0; i < sheetIds.arrayLength(); ++i)
        {
            IDX sheetIdx = INVALIDIDX;
            pSrcBook->STSheetToRTSheet(sheetIds.item_uint32(i), &sheetIdx);
            if (sheetIdx == INVALIDIDX)
            {
                WOLOG_INFO << "[CopySheetsFromBook] Invalid sheet index";
                continue;
            }
            vecSheetIdx.push_back(sheetIdx);
        }
    }

    if (vecSheetIdx.empty())
    {
        WOLOG_INFO << "[CopySheetsFromBook] vecSheetIdx empty";
        return E_INVALIDARG;
    }

    bool bActivateSheet = true;
    if (param.has("bNotActivateSheet"))
        bActivateSheet = !param.field_bool("bNotActivateSheet");

    util::CalcBatchUpdate calcBatchUpdate(m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator());
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(ptrWorkbookDst);
	ks_stdptr<IWorkspace> spWS;
    IBook* pDstBook = m_wwb->GetCoreWorkbook()->GetBook();
	pDstBook->GetWorkspace(&spWS);
	autoupdate_util::DisableTransHlp _disable(spWS);

    std::vector<int> vecNewSheetIdx;
    HRESULT hr = SheetOperatorHelper::CopySheetsFromBook(
        m_wwb, ptrWorkbookSrc, ptrWorkbookDst, vecSheetIdx, vecNewSheetIdx, true, bActivateSheet);

    util::CloseFile(ptrWorkbookSrc);
    m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(ptrWorkbookDst);
    m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(ptrWorkbookSrc);

    if (FAILED(hr) || vecNewSheetIdx.empty())
        return hr;

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

    acpt.addKey("sheetIds");
    acpt.beginArray();
    for (int sheetIdx : vecNewSheetIdx)
    {
        uint sheetId = 0;
        pDstBook->RTSheetToSTSheet(sheetIdx, &sheetId);
        acpt.addUint32(nullptr, sheetId);
    }
    acpt.endArray();
    return S_OK;
}

PCWSTR EtHttpCopySheetsFromBookTaskClass::GetTag()
{
    return __X("http.et.copySheetsFromBook");
}

// ================== EtHttpDeleteSheetsTaskClass ==================
EtHttpDeleteSheetsTaskClass::EtHttpDeleteSheetsTaskClass(KEtWorkbook* wb)
    : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpDeleteSheetsTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    if (m_wwb->GetBMP()->bDbSheet)
        return  E_API_NOT_SUPPORTED_ON_FILE;
    binary_wo::VarObj param = cmd->cast().get("param");

    binary_wo::VarObj sheetIds = param.get_s("sheetIds");
    std::vector<uint> vecSheetId;
    vecSheetId.reserve(sheetIds.arrayLength());
    for (int i = 0; i < sheetIds.arrayLength(); ++i)
    {
        vecSheetId.push_back(sheetIds.item_uint32(i));
    }

    return SheetOperatorHelper::DeleteSheets(m_wwb, ctx, vecSheetId);
}

PCWSTR EtHttpDeleteSheetsTaskClass::GetTag()
{
    return __X("http.et.deleteSheets");
}

HRESULT EtHttpDeleteSheetsTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isConnSharedLink())
		return E_OPERATION_NOT_SUPPORTED_ON_SHARED_SHEET;
	return S_OK;
}

// ================== EtHttpSetQRColTaskClass ==================
EtHttpSetQRColTaskClass::EtHttpSetQRColTaskClass(KEtWorkbook* wb)
        : EtHttpTaskClassBase(wb)
{
    m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

void EtHttpSetQRColTaskClass::AdjustQRCol(Database::GenQRLabelCol& qrCol, COL originalCol)
{
    if (qrCol.col == originalCol)
        return;

    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    if (qrCol.keyCol > originalCol && qrCol.keyCol < pBook->GetBMP()->cntCols - 1)
        qrCol.keyCol++;
    for (auto& refCol: qrCol.refCols)
    {
        if (refCol > originalCol && refCol < pBook->GetBMP()->cntCols - 1)
            refCol++;
    }
}

HRESULT EtHttpSetQRColTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");

    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    Database::GenQRLabelCol oldQRCol;
    Database::GenQRLabelCol newQRCol;
    VAR_OBJ_EXPECT_STRUCT(param, "colConfig");
    VarObj colConfig = param.get_s("colConfig");
    if (!Database::Utils::ParseQRColFromVarObj(pBook->GetBMP(), colConfig, newQRCol))
        return E_INVALID_REQUEST;

    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(sheetIdx, &spSheet);
    COL originalCol = INVALID_COL;
    bool targetColDerivedFromPageId = false;
    bool isUpdateQRCol = false;
    if (param.has("col"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "col");
        originalCol = param.field_int32("col");
        if (originalCol < 0 || originalCol > pBook->GetBMP()->cntCols - 1)
            return E_INVALID_REQUEST;
    }
    else if (param.has("pageId"))
    {
        targetColDerivedFromPageId = true;
        VAR_OBJ_EXPECT_STRING(param, "pageId");
        PCWSTR pageId = param.field_str("pageId");
        Database::FieldContext fieldContext(m_wwb, ctx);
        HRESULT hr = Database::Utils::GetQRColByPageId(&fieldContext, sheetIdx, pageId, oldQRCol);
        if (FAILED(hr) || oldQRCol.col == INVALID_COL)
            return E_QRCOL_NOT_FOUND;

        isUpdateQRCol = true;
        originalCol = oldQRCol.col;
    }
    else
    {
        originalCol = 0;
    }

    newQRCol.col = originalCol;
    RANGE range(pBook->GetBMP());
    range.SetCols(sheetIdx, sheetIdx, newQRCol.col, newQRCol.col);
    app_helper::KBatchUpdateCal batchUpdate(m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator());
    Database::FieldContext fieldContext(m_wwb, ctx);
    if (!targetColDerivedFromPageId)
    {
        Database::Utils::GetQRCol(&fieldContext, sheetIdx, newQRCol.col, oldQRCol);
        if (oldQRCol.pageId == newQRCol.pageId)
        {
            isUpdateQRCol = true;
        }
        else
        {
            if (!util::IsRangeEmpty(ctx, spSheet, range) || util::HasMergedCell(spSheet, range))
            {
                if (newQRCol.col < spSheet->GetBMP()->cntCols - 1)
                {
                    newQRCol.col++;
                    range.SetColFromTo(newQRCol.col);
                }

                HRESULT hr = InsertRangeEx(range, etShiftToRight, ctx, true);
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    Database::FieldType fieldType = Database::dftGenQRLabel;
    HRESULT hr = Database::Utils::ClearExcludeType(&fieldContext, fieldType, range);
    if (FAILED(hr))
        return hr;

    Database::FieldsManager* pDbMgr = Database::FieldsManager::Instance();
    Database::IDbField* pField = pDbMgr->GetField(fieldType);
    if (!pField)
        return E_FAIL;

    AdjustQRCol(newQRCol, originalCol);
    binary_wo::VarObjRoot objRoot(new binary_wo::BinVarRoot());
    binary_wo::VarObj obj = objRoot.cast();
    Database::Utils::WriteQRColToVarObj(newQRCol, obj);
    Database::FieldContext fieldContextForSet(m_wwb, ctx, obj);
    range.SetRowFrom(1);
    hr = pField->Set(&fieldContextForSet, range);
    if (FAILED(hr))
        return hr;

    ks_wstring colName;
    if (param.has("colName"))
    {
        VAR_OBJ_EXPECT_STRING(param, "colName");
        colName = param.field_str("colName");
    }
    else
    {
        // 表头行改变时取原有表头名称
        if (isUpdateQRCol && oldQRCol.headerRow != newQRCol.headerRow)
        {
            ks_bstr oldColName;
            ctx->getStringTools()->GetCellText(spSheet, oldQRCol.headerRow, newQRCol.col, &oldColName, nullptr, 0, nullptr);
            colName = oldColName.c_str();
        }
    }

    if (!colName.empty())
    {
        RANGE colNameRange(range);
        colNameRange.SetRowFromTo(newQRCol.headerRow);
        auto accessPerm = PTAAP_None;
        if (!ctx->getProtectionCtx()->isAllowEdit(colNameRange, &accessPerm))
            return accessPerm == PTAAP_Exclusive ? E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE : E_NOT_HAVE_PERMISSION;
        hr = ctx->getProtectionCtx()->checkFormula(colNameRange, colName.c_str());
        if (hr != S_OK)
            return E_NOT_HAVE_PERMISSION;

        ks_stdptr<IRangeInfo> spColNameRange = m_wwb->CreateRangeObj(colNameRange);
        if (!spColNameRange)
            return E_INVALID_REQUEST;

        hr = spColNameRange->SetFormula(colName.c_str(), &colNameRange);
        if (FAILED(hr))
            return hr;
    }
    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addInt32("col", newQRCol.col);
    return S_OK;
}

PCWSTR EtHttpSetQRColTaskClass::GetTag()
{
    return __X("http.et.setQRCol");
}

// ================== EtHttpFillQRColTaskClass ==================
EtHttpFillQRColTaskClass::EtHttpFillQRColTaskClass(KEtWorkbook* wb)
        : EtHttpTaskClassBase(wb)
{
    m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT EtHttpFillQRColTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    VAR_OBJ_EXPECT_STRING(param, "pageId");
    PCWSTR pageId = param.field_str("pageId");
    Database::GenQRLabelCol qrCol;
    Database::FieldContext fieldContext(m_wwb, ctx);
    HRESULT hr = Database::Utils::GetQRColByPageId(&fieldContext, sheetIdx, pageId, qrCol);
    if (FAILED(hr) || qrCol.col == INVALID_COL)
        return E_QRCOL_NOT_FOUND;

    binary_wo::VarObjRoot objRoot(new binary_wo::BinVarRoot());
    binary_wo::VarObj obj = objRoot.cast();
    Database::Utils::WriteQRColToVarObj(qrCol, obj);
    Database::FieldContext fillFieldContext(m_wwb, ctx, obj);
    Database::FieldGenQRLabelFormulaFillModifier fillModifier;
    RANGE range(pBook->GetBMP());
    range.SetCols(sheetIdx, sheetIdx, qrCol.col, qrCol.col);
    hr = fillModifier.CheckPermission(&fillFieldContext, range);
    if (FAILED(hr))
        return hr;

    app_helper::KBatchUpdateCal batchUpdate(m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator());
    return fillModifier.Set(&fillFieldContext, range);
}

PCWSTR EtHttpFillQRColTaskClass::GetTag()
{
    return __X("http.et.fillQRCol");
}

// ================== EtHttpClearColTypeTaskClass ==================
EtHttpClearColTypeTaskClass::EtHttpClearColTypeTaskClass(KEtWorkbook* wb)
        : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpClearColTypeTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    VAR_OBJ_EXPECT_ARRAY(param, "cols");
    binary_wo::VarObj cols = param.get("cols");
    int colCount = cols.arrayLength();
    if (colCount <= 0)
        return E_INVALIDARG;

    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    Database::FieldContext fieldContext(m_wwb, ctx);
    RANGE range(pBook->GetBMP());
    for (int i = 0; i < colCount; ++i)
    {
        COL col = cols.at(i).value_int32();
        range.SetCols(sheetIdx, sheetIdx, col, col);
        HRESULT hr = Database::Utils::ClearAll(&fieldContext, range);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

PCWSTR EtHttpClearColTypeTaskClass::GetTag()
{
    return __X("http.et.clearColType");
}

// ================== EtHttpRecalculateRangeDataTaskClass ==================
EtHttpRecalculateRangeDataTaskClass::EtHttpRecalculateRangeDataTaskClass(KEtWorkbook* wb)
        : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpRecalculateRangeDataTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");

    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    VAR_OBJ_EXPECT_STRUCT(param, "range")
    binary_wo::VarObj varRange = param.get("range");
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "rowTo")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colFrom")
    VAR_OBJ_EXPECT_NUMERIC(varRange, "colTo")
    RANGE rg = ReadRangeInl(m_wwb->GetBMP(), sheetIdx, varRange);
    if (!rg.IsValid())
        return E_INVALID_REQUEST;

    bool diffusing = false;
    if (param.has("diffusing"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "diffusing");
        diffusing = param.field_bool("diffusing");
    }
    pBook->Recalculate(rg, alg::bool2BOOL(diffusing));
    return S_OK;
}

PCWSTR EtHttpRecalculateRangeDataTaskClass::GetTag()
{
    return __X("http.et.recalculateRangeData");
}

// ================== EtHttpUpdateWorkbenchSheetTaskClass ==================
EtHttpUpdateWorkbenchSheetTaskClass::EtHttpUpdateWorkbenchSheetTaskClass(KEtWorkbook* wb)
        : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpUpdateWorkbenchSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");

    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(sheetIdx, &spSheet);
    if (!spSheet->IsWorkbenchSheet())
        return E_INVALID_REQUEST;

    if (!param.has("pageId"))
        return E_INVALID_REQUEST;

    ks_stdptr<IWorkbenchSheetData> spWorkbenchSheetData;
    VS(spSheet->GetExtDataItem(edSheetWorkbenchData, (IUnknown**)&spWorkbenchSheetData));
    VAR_OBJ_EXPECT_STRING(param, "pageId")
    PCWSTR pageId = param.field_str("pageId");
    spWorkbenchSheetData->SetPageId(pageId);
    return S_OK;
}

PCWSTR EtHttpUpdateWorkbenchSheetTaskClass::GetTag()
{
    return __X("http.et.updateWorkbenchSheet");
}

// 删除range
EtHttpDeleteRangeTaskClass::EtHttpDeleteRangeTaskClass(KEtWorkbook* wb)
        : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpDeleteRangeTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");

    IDX sheetIdx = -1;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == -1)
        return E_KSHEET_SHEET_NOT_FOUND;

    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(sheetIdx, &spSheet);

    // 保护的sheet 不让删除
    if (spSheet->IsProtected())
    {
        return E_SHEET_PROTECTED;
    }

    // 只支持普通的sheet
    if (!spSheet->IsGridSheet()) 
    {
        return E_OPERATION_NOT_SUPPORTED_ON_FORM_SHEET;
    }

    BMP_PTR bmp = spSheet->GetBMP();

    VAR_OBJ_EXPECT_ARRAY(param, "rangeData")
    std::vector<RANGE> rgVec;
    // 这里不能调用ReadRangesInl 里面没有做参数校验
    binary_wo::VarObj rangeDataVar = param.get_s("rangeData");
	for (int i = 0; i < rangeDataVar.arrayLength_s(); i++)
	{
		binary_wo::VarObj item = rangeDataVar.at_s(i);
        VAR_OBJ_EXPECT_NUMERIC(item, "rowFrom");
        VAR_OBJ_EXPECT_NUMERIC(item, "rowTo");
        VAR_OBJ_EXPECT_NUMERIC(item, "colFrom");
        VAR_OBJ_EXPECT_NUMERIC(item, "colTo");
        ROW rowFrom = item.field_int32("rowFrom");
        ROW rowTo = item.field_int32("rowTo");
        COL colFrom = item.field_int32("colFrom");
        COL colTo = item.field_int32("colTo");
        if (   rowFrom < 0 || rowFrom >= bmp->cntRows 
            || rowTo < 0   || rowTo >= bmp->cntRows
            || colFrom < 0 || colFrom >= bmp->cntCols
            || colTo < 0   || colTo >= bmp->cntCols) 
        {
            return E_RANGE_INVALID;
        }

        RANGE rg(bmp);
        rg.SetSheetFromTo(sheetIdx);
        rg.SetRowFromTo(rowFrom, rowTo);
        rg.SetColFromTo(colFrom, colTo);
		if (!rg.IsValid())
        {
            return E_RANGE_INVALID;
        }
			
        rgVec.push_back(rg);
	}

	if (rgVec.empty()) 
	{
		return S_OK;
	}

    // 默认往上移动     
    INT32 opt = etShiftUp;
	DIR dir = dirTop;

    if (param.has("type"))
    {
        VAR_OBJ_EXPECT_STRING(param, "type")
        PCWSTR strDir = param.field_str("type");

        if (xstricmp(strDir, __X("etShiftToLeft")) == 0)
        {
            opt = etShiftToLeft;
            dir = dirLeft;
        } 
    }

    KComVariant var;
	var.AssignDouble(opt);

	ks_stdptr<Range> range = m_wwb->CreateRangeObj(rgVec);
	if (range == NULL) 
	{
		return S_OK;
	}

	ks_stdptr<IRangeInfo> hostInfo = range;
	ks_stdptr<IBookOp> op;
	range_helper::ranges rgs;
	hostInfo->GetIRanges(&rgs);
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&op);

	HRESULT hr = op->QueryRangeOperation(NULL, rgs, roc_Remove, dir);
	if (FAILED(hr))
		return hr;

    if (hostInfo->IsMultiLaped()) 
    {
        return E_ET_INVALID_MULTI_RANGE_OPTION;
    }

    if (rgs.size() > 1 && ListObjHelp::IsListObjectSelected(spSheet, rgs))
    {
        return E_AREA_ALTERLISTOBJECT;
    }

    // 设置活跃单元格是为了解决在table中删除，会一起把隐藏行删除掉。而table中没有多选区删除，所以取第0个是ok的
	const CELL active_cell = {
		rgVec[0].RowFrom(),
		rgVec[0].ColFrom()
	};

	ks_stdptr<Window> spWnd;
	hr = m_wwb->GetCoreApp()->get_ActiveWindow(&spWnd);
	if (FAILED(hr) || spWnd == NULL) return hr;

	ks_stdptr<IKWorksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	spWorksheet->Activate(FALSE);
	ks_stdptr<ISheetWndInfos> spWndsInfo = spWorksheet->GetWndInfos();
		spWndsInfo->SetActiveCell(spWnd->GetIndex(), active_cell);

    return range->Delete(var);
}

PCWSTR EtHttpDeleteRangeTaskClass::GetTag()
{
    return __X("http.et.deleteRange");
}

// ================== EtHttpV8JsEvaluateTaskClass ==================
EtHttpV8JsEvaluateTaskClass::EtHttpV8JsEvaluateTaskClass(KEtWorkbook* wb)
    : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpV8JsEvaluateTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    if (!param.has("jsStr")) {
        WOLOG_INFO << "[JsApi] Not has jsStr";
        return E_FAIL;
    }

    ks_stdptr<IKEtApplication> ptrApp = m_wwb->GetCoreApp();
    IKJSIDE* jsIde = ptrApp->GetJsIDE(TRUE);
    if (!jsIde) {
        WOLOG_ERROR << "[JsApi] JsIDE is null";
        return E_FAIL;
    }

    INT sheetCount = 0;
    m_wwb->GetCoreWorkbook()->GetBook()->GetSheetCount(&sheetCount);
    if (sheetCount <= 0)
        return E_FAIL;

    const BMP_PTR bpm = m_wwb->GetBMP();
    RANGE rg(bpm);
    rg.SetSheets(0, sheetCount - 1); // 在每个表的整表范围内，查找是否有设置权限
    auto accessPerm = PTAAP_None;
    if (!ctx->getProtectionCtx()->isAllowEdit(rg, &accessPerm))  
    {
        WO_LOG_X(ctx->getLogger(), WO_LOG_INFO, "[JsApi] Not have permission");
        return accessPerm == PTAAP_Exclusive ? E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE : E_NOT_HAVE_PERMISSION;
    }

    QString jsQstr = QString("(()=>{%1\n})()").arg(QString::fromUtf16(param.field_str("jsStr")));
    WebStr jsStr = krt::utf16(jsQstr);
    V8InspectorEvaluateResult evalRes;
    BOOL bRes = jsIde->V8InspectorEvaluate(jsStr, evalRes);
    if (bRes) {
        WOLOG_INFO << "[JsApi] JsIDE V8 evaluate succeed";
    } else {
        WOLOG_ERROR << "[JsApi] JsIDE V8 evaluate failed";
    }

    binary_wo::BinWriter* bw = ctx->getHttpResponse();
    bw->addBoolField(bRes, "result");
    bw->addStringField(evalRes.response, "responseDetail");
    bw->addStringField(evalRes.responseResult, "response");
    bw->beginArray("consoleLog");
    for(int i = 0; i < evalRes.consoleLogVec.size(); i++)
    {
        if (evalRes.consoleLogVec[i].size() > 0) {
            bw->beginArray();
            for (int j = 0; j < evalRes.consoleLogVec[i].size(); j++) {
                bw->addString(evalRes.consoleLogVec[i][j]);
            }
            bw->endArray();
        }
    }
    bw->endArray();

    m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
    return S_OK;
}

PCWSTR EtHttpV8JsEvaluateTaskClass::GetTag()
{
    return __X("http.et.v8JsEvaluate");
}

HRESULT EtHttpV8JsEvaluateTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isConnSharedLink())
		return E_OPERATION_NOT_SUPPORTED_ON_SHARED_SHEET;
	return S_OK;
}

// ================== EtHttpInsertNumberColTaskClass ==================
EtHttpInsertNumberColTaskClass::EtHttpInsertNumberColTaskClass(KEtWorkbook* wb)
    : EtHttpTaskClassBase(wb)
{
}

HRESULT EtHttpInsertNumberColTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    if (!pBook && !pBook->GetBMP()->bKsheet)
        return E_FAIL;

    binary_wo::VarObj param = cmd->cast().get("param");

    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId");
    IDX sheetIdx = INVALIDIDX;
    pBook->STSheetToRTSheet(param.field_int32("sheetId"), &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_KSHEET_SHEET_NOT_FOUND;

    IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
    if (!pWorkSheet)
        return E_INVALIDARG;

    app_helper::KBatchUpdateCal batchUpdate(pBook->LeakOperator());

    VAR_OBJ_EXPECT_NUMERIC(param, "col");
    COL col = param.field_int32("col");
    if (col < 0 || col > pBook->GetBMP()->cntCols - 1)
        return E_INVALID_REQUEST;
    RANGE rg(pBook->GetBMP());
    rg.SetCols(sheetIdx, sheetIdx, col, col);

    VAR_OBJ_EXPECT_BOOL(param, "isInsertBefore");
    bool isInsertBefore = param.field_bool("isInsertBefore");
    if (!isInsertBefore)
    {
        if (col < pBook->GetBMP()->cntCols - 1)
            rg.SetColFromTo(++col);
    }

    HRESULT hr = InsertRangeEx(rg, etShiftToRight, ctx, !isInsertBefore);
    if (FAILED(hr))
        return hr;

    if (!param.has("numberFormat"))
    {
        param.add_field_str("numberFormat", __X("000000"));
        ctx->setIsRealTransform(true);
    }

    VAR_OBJ_EXPECT_NUMERIC(param, "headerRow");
    hr = ColOperatorHelper::FillNumberColData(m_wwb, param, ctx, rg);
    if (FAILED(hr))
        return hr;

    NotifyHistoryCellPosChange(ctx);
    return S_OK;
}

PCWSTR EtHttpInsertNumberColTaskClass::GetTag()
{
    return __X("http.et.insertNumberCol");
}

// ================== EtHttpSetCustomStorageTaskClass ==================
EtHttpSetCustomStorageTaskClass::EtHttpSetCustomStorageTaskClass(KEtWorkbook* wwb)
        : EtHttpTaskClassBase(wwb)
{
}

HRESULT EtHttpSetCustomStorageTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    if (!pBook)
        return E_FAIL;

    ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    pBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    if (!spCustomStorMgr)
        return E_FAIL;

    binary_wo::VarObj param = cmd->cast().get("param");
    if (param.has("book"))
    {
        HRESULT hr = HandleBookInfo(param, spCustomStorMgr);
        if (FAILED(hr))
            return hr;
    }

    if (param.has("sheets"))
    {
        HRESULT hr = HandleSheetInfo(param, spCustomStorMgr, ctx);
        if (FAILED(hr))
            return hr;
    }

    return S_OK;
}

PCWSTR EtHttpSetCustomStorageTaskClass::GetTag()
{
    return __X("http.et.setCustomStorage");
}

HRESULT EtHttpSetCustomStorageTaskClass::HandleBookInfo(const binary_wo::VarObj& param, 
                                                        ks_stdptr<ICustomStorageManager>& spCustomStorMgr)
{
    binary_wo::VarObj bookObj = param.get_s("book");
    for (EtDbIdx i = 0 ; i < bookObj.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = bookObj.at_s(i);

        VAR_OBJ_EXPECT_STRING(item, "key")
        VAR_OBJ_EXPECT_STRING(item, "group")
        VAR_OBJ_EXPECT_STRING(item, "value")
        spCustomStorMgr->UpdateStorage(-1, item.field_str("key"), item.field_str("group"), 
                                    item.field_str("value"), FALSE); // -1代表book级存储
    }

    return S_OK;
}

HRESULT EtHttpSetCustomStorageTaskClass::HandleSheetInfo(const binary_wo::VarObj& param, 
                                                        ks_stdptr<ICustomStorageManager>& spCustomStorMgr, 
                                                        KEtRevisionContext*& ctx)
{
    binary_wo::VarObj sheetsObj = param.get_s("sheets");
    for (EtDbIdx i = 0 ; i < sheetsObj.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = sheetsObj.at_s(i);

        VAR_OBJ_EXPECT_NUMERIC(item, "sheetId")
        const IDX stId = item.field_int32("sheetId");
        IDX sheetIdx = INVALIDIDX;
        IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
        pBook->STSheetToRTSheet(stId, &sheetIdx);
        if (sheetIdx == INVALIDIDX) // stId对应的sheet是否存在
            continue;

        if (ctx->getProtectionCtx()->isSheetHidden(stId)) // 是否拥有stId对应的sheet的权限
            continue;

        VAR_OBJ_EXPECT_STRING(item, "key")
        VAR_OBJ_EXPECT_STRING(item, "group")
        VAR_OBJ_EXPECT_STRING(item, "value")
        VAR_OBJ_EXPECT_BOOL(item, "isFallback2Book")
        spCustomStorMgr->UpdateStorage(stId, item.field_str("key"), item.field_str("group"), 
                                    item.field_str("value"), item.field_bool("isFallback2Book"));
    }

    return S_OK;
}

class HttpExecuteApiBatchTask: public IBatchTask, public wo::IJsApiBatchResult
{
public:
    HttpExecuteApiBatchTask(EtHttpExecuteApiTaskClass * pTask, KEtWorkbook * pWb)
    : m_pTask(pTask)
    , m_pWb(pWb)
    {
        
    }
    
    KwTask * CreateBatchTask(KEtRevisionContext * pCtx) override
    {
        std::vector<JsApiExecRecord> & apiRecords = m_pWb->getApiRecords();
        int recordCnt = apiRecords.size();
        KwCommand* pCmd = new KwCommand;
        binary_wo::VarObj objCmd = pCmd->cast();
        objCmd.add_field_str("name", __X("http.et.playbackApi"));
        binary_wo::VarObj param = objCmd.add_field_struct("param");
        binary_wo::VarObj apiArr = param.add_field_structArray("apis", recordCnt);
        ks_wstring curTaskId, curConnId;
        std::set<ks_wstring> taskSet;
        for (int i = 0; i < recordCnt; ++i)
        {
            JsApiExecRecord& record = apiRecords[i];
            binary_wo::VarObj api = apiArr.at(i);
            // 一个脚本执行结束触发提交
            if (JSRecordType::End == record.type)
            {
                curConnId = record.userContext.connId;
                // 该字段用于协作记录展示
                param.add_field_bool("end", true);
                param.add_field_str("scriptName", record.scriptName.c_str());
                if (!record.language.empty())
                    param.add_field_str("language", record.language.c_str());
                api.add_field_bool("end", true);
                break;
            }
            if (JSRecordType::UpdateContext == record.type)
            {
                api.add_field_bool("updateContext", true);
                api.add_field_buffer("content", (byte*)record.content.data(), record.content.length());
                continue;
            }
            if (JSRecordType::PicUuid == record.type)
            {
                api.add_field_bool("picUuid", true);
                api.add_field_str("pic", record.scriptName.c_str());
                api.add_field_str("uuid", record.taskId.c_str());
                continue;
            }
            if (curTaskId.empty() || curTaskId != record.taskId)
            {
                curTaskId = record.taskId;
                taskSet.insert(curTaskId);
                curConnId = record.userContext.connId;
                binary_wo::VarObj userContext = api.add_field_struct("userContext");
                userContext.add_field_str("connId", record.userContext.connId.c_str());
                userContext.add_field_str("userId", record.userContext.userId.c_str());
                userContext.add_field_str("sessionId", record.userContext.sessionId.c_str());
                userContext.add_field_str("permissionId", record.userContext.permissionId.c_str());
            }

            api.add_field_str("taskId", record.taskId.c_str());
            api.add_field_buffer("content", (byte*)record.content.data(), record.content.length());
            // getRootProperties的api会伴随着begin
            if (JSRecordType::Begin == record.type)
            {
                // 该字段用于协作记录展示
                param.add_field_bool("begin", true);
                param.add_field_str("scriptName", record.scriptName.c_str());
                if (record.contextInfo.length() > 0)
                    param.add_field_buffer("ContextInfo", (byte*)record.contextInfo.data(), record.contextInfo.length());
                if (!record.language.empty())
                    param.add_field_str("language", record.language.c_str());
                break;
            }
        }
        apiRecords.clear();
        m_apiRecordCnt = 0;

        IKUserConns* pUserConns = m_pWb->GetCoreApp()->getUserConns();
        IKUserConn* pUserConn = pUserConns->getUserConn(krt::fromUtf16(curConnId.c_str()).toUtf8());
        KwTask* task = new KwTask(new KwCommand, InitVersion, pUserConn->innerID(), NULL, pCtx->getBaseDataVersion());

        if (!taskSet.empty())
        {
            binary_wo::VarObjRoot binRoot(new binary_wo::BinVarRoot());
            binary_wo::VarObj var = binRoot.cast();
            var.add_field_int32("dependCommitId", getDependCommitId());
            binary_wo::VarObj arr = var.add_field_array("scriptIds", typeString);
            for (const auto& iter : taskSet)
            {
                arr.add_item_str(iter.c_str());
            }
            task->setApiFillLog(std::move(binRoot));
        }

        if (m_curApiTaskCnt == 0)
            setDependCommitId(-1);
        task->push_back(pCmd);
        m_pWb->moveObjectRefToTask(task);
        m_isNeedCommitApi = false;
        return task;
    }
    
	WoBeginBatchTransFlag GetBeginBatchFlag(IBatchTask * pPreBatchTask) override
    {
        if (pPreBatchTask == nullptr)
            return WoBeginBatchTransFlag::restart;
        else if (xstrcmp(GetTag(), pPreBatchTask->GetTag()) == 0)
            return WoBeginBatchTransFlag::keep;
        else
            return WoBeginBatchTransFlag::restart;
    }
    
	bool needStopInEndBatch() override
    {
        return m_isNeedCommitApi;
    }

    virtual bool MarkJSContextChange(bool value) override
    {
        if (m_bContextChanged == value)
            return m_bContextChanged;
        bool old = m_bContextChanged;
        m_bContextChanged = value;
        return old;
    }

    PCWSTR GetTag() override { return m_pTask->GetTag(); }
    bool isAllowMultiUserConns() override { return true; }
    KwVersion::BanUndoType getBanUndoType() { return KEtVersion::banUndoOver; }
    
    void setNeedCommitApi(bool bNeed) override { m_isNeedCommitApi = bNeed; }
    void setDependCommitId(int commitId) override { m_dependCommitId = commitId; }
    int getDependCommitId() const { return m_dependCommitId; }
    int getCurApiTaskCnt() override { return m_curApiTaskCnt; }
    void addCurApiTaskCnt(int v) override { m_curApiTaskCnt += v; }
    void addApiRecord(const JsApiExecRecord & record) override { m_pWb->getApiRecords().push_back(record); ++m_apiRecordCnt;}
    size_t getApiRecordsSize() override { return m_apiRecordCnt; }
    virtual void SetJSContextChange(bool value) override { MarkJSContextChange(value); }

private:
    EtHttpExecuteApiTaskClass * m_pTask;
    KEtWorkbook * m_pWb;
    int m_dependCommitId = -1;
    int m_curApiTaskCnt = 0;
    int m_apiRecordCnt = 0;
    bool m_isNeedCommitApi = false;
    bool m_bContextChanged = true;
};

EtHttpExecuteApiTaskClass::EtHttpExecuteApiTaskClass(KEtWorkbook* wb)
        : EtHttpTaskClassBase(wb)
{
    m_spBatchTask.reset(new HttpExecuteApiBatchTask(this, wb));
    KJsApiHelper::InitEnv();
}

EtHttpExecuteApiTaskClass::~EtHttpExecuteApiTaskClass()
{
    
}

HRESULT EtHttpExecuteApiTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    KJsApiHelper helper(ctx, m_wwb, false, m_spBatchTask.get());
    return helper.Execute(param);
}

IBatchTask * EtHttpExecuteApiTaskClass::GetBatchTask()
{ 
    return m_spBatchTask.get(); 
}

PCWSTR EtHttpExecuteApiTaskClass::GetTag()
{
    return __X("http.et.ExecuteApi");
}

EtHttpPlaybackApiTaskClass::EtHttpPlaybackApiTaskClass(KEtWorkbook* wb)
        : EtHttpTaskClassBase(wb)
{
    m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT EtHttpPlaybackApiTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    JSRevisionContextScope rcScope(pCtx);
    binary_wo::VarObj param = cmd->cast().get("param");
    bool bBegin = param.has("begin") && param.field_bool("begin");
    binary_wo::VarObj apiArr = param.get_s("apis");
    for (int i = 0; i < apiArr.arrayLength(); ++i)
    {
        binary_wo::VarObj api = apiArr.at(i);
        if (api.has("end") && api.field_bool("end"))
        {
            m_taskCnt--;
            if (m_taskCnt == 0) 
            {
                m_funcMap.clear();
                m_objMap.clear();
                KJsApiContextManager::Instance()->CloseAll();
            }
            continue;
        }
        if (api.has("updateContext") && api.field_bool("updateContext"))
        {
            std::pair<const byte*, int32> content = api.get("content").as_array_buffer_s();
            KJsApiHelper helper(pCtx, m_wwb, false);
            helper.GainObjMapContext(m_objMap, content.first, content.second / sizeof(void*));
            continue;
        }
        if (api.has("picUuid") && api.field_bool("picUuid"))
        {
            WebStr pic = api.field_str("pic");
            WebStr uuid = api.field_str("uuid");
            KJsApiHelper::AddPicUuid(pic, uuid);
            continue;
        }
        WebStr taskId = api.field_str("taskId");

        if (api.has("userContext"))
        {
            binary_wo::VarObj userContext = api.get_s("userContext");
            WebStr connId = userContext.field_str("connId");
            WebStr userId = userContext.field_str("userId");
            WebStr sessionId = userContext.field_str("sessionId");
            WebStr permissionId = userContext.field_str("permissionId");
            SwitchUser(connId, userId, sessionId, permissionId, pCtx);
        }
        QString qTaskId = krt::fromUtf16(taskId);
	    JSTaskScope scope(qTaskId);
        std::pair<const byte*, int32> content = api.get("content").as_array_buffer_s();
        bool bEnter = bBegin && i == 0;
        KJsApiContextScope ctxScope(m_wwb, pCtx, qTaskId, param, bEnter);
        ExecuteSingleApi((char*)content.first, pCtx);
    }
    return S_OK;
}

PCWSTR EtHttpPlaybackApiTaskClass::GetTag()
{
    return __X("http.et.playbackApi");
}

void EtHttpPlaybackApiTaskClass::ExecuteSingleApi(char* pData, KEtRevisionContext* pCtx)
{
    int nHead = sizeof(JsApiIpcRpcCall);
    JsApiIpcRpcCall rpcCall = *(JsApiIpcRpcCall*)pData;
    pData += nHead;
    if (rpcCall.type == JAT_GetRootProperties)
        m_taskCnt++;

    // 回放时，参数转换失败，兜底
    if (!TransformHeader(rpcCall) && pCtx->isExecDirect())
        return;
    JsApiIpcArgument* pArgs = (JsApiIpcArgument*)pData;
	KJSVariantList valueList;
    JsApiIpcArgument* pTransArgs = pArgs;
    if (!TransformArgument(rpcCall.nCount, pTransArgs) && pCtx->isExecDirect())
        return;
    std::vector<IJSONValue*> nestJson;
    ArgumentsToValueList(rpcCall.nCount, pArgs, valueList, nestJson);
    nestJson.clear();
    if (!TransformValueList(valueList) && pCtx->isExecDirect())
        return;

    KJSVariantList retList;
    KJsApiHelper helper(pCtx, m_wwb, false);
    helper.ExecuteApi(&rpcCall, &valueList, &retList);
    std::unordered_map<IJSONValue*, size_t> cnestJson;
    pData += CalculateSize(valueList, cnestJson);
    cnestJson.clear();

    if (rpcCall.type == JAT_Set)
        return;
    nHead = sizeof(JsApiIpcRpcReturn);
    JsApiIpcRpcReturn ret = *(JsApiIpcRpcReturn*)pData;
    pData += nHead;
    JsApiIpcArgument* pRes = (JsApiIpcArgument*)pData;
    KJSVariantList oldRes;
    ArgumentsToValueList(ret.nCount, pRes, oldRes, nestJson);

    GainObjMap(rpcCall, oldRes, retList);
}

void EtHttpPlaybackApiTaskClass::SwitchUser(PCWSTR connId, PCWSTR userId, PCWSTR sessionId, PCWSTR permissionId, KEtRevisionContext* pCtx)
{
    UserConnEnd clientEnd = userConnEndUnknown;
	double clientCoopVersion = 1.0;
    ks_stdptr<_Application> ptrApp = m_wwb->GetCoreApp();
	IKUserConns* pUserConns = ptrApp->getUserConns();
    if (!pUserConns)
        return;
    QByteArray sConnId = krt::fromUtf16(connId).toUtf8();
    QByteArray sUserId = krt::fromUtf16(userId).toUtf8();
    UserConnArgs args(WoConnFromAirscript2, WoConnLifeSession);
    pUserConns->setCurrentUserConn(sConnId.data(), sUserId.data(), clientEnd, clientCoopVersion, &args);
    IKUserConn* pCurUserConn = pUserConns->getCurrentUserConn();
    if (!pCurUserConn)
        return;
    const auto *curUserId = pCurUserConn->userID();
    m_wwb->GetCoreWorkbook()->GetBook()->GetWoStake()->SetCurrentUserId(curUserId);

    if (sessionId)
        pCurUserConn->setSessionId(QString::fromUtf16(sessionId));
    if (permissionId)
        pCurUserConn->setCorePermissionId(permissionId);

    pCtx->setUser(pCurUserConn);
}

template <typename E, typename T>
inline bool findAndReplace(E& obj, T& map)
{
    if (!obj) return true;
    auto iter = map.find(obj);
    if (iter != map.end())
        obj = iter->second;
    return iter != map.end();
}

bool EtHttpPlaybackApiTaskClass::TransformHeader(JsApiIpcRpcCall& rpcCall)
{
    if (rpcCall.func == nullptr && rpcCall.pObj == nullptr)
        return true;

    return findAndReplace(rpcCall.func, m_funcMap) && findAndReplace(rpcCall.pObj, m_objMap);
}

bool EtHttpPlaybackApiTaskClass::TransformArgument(int nCount, JsApiIpcArgument* pVal)
{
    for (size_t i = 0; i < nCount; ++i)
    {
        switch (pVal->header.type)
        {
        case VT_DISPATCH_JS_ALIAS:
        {
            ApiObjectInterface pObj = reinterpret_cast<IDispatch*>(*(PVOID*)(pVal->data));
            if (!findAndReplace(pObj, m_objMap))
                return false;

            KJSVariant var;
            var.Assign(pObj);
            PVOID data =V_BYREF(&var);
            ks_memcpy_s(pVal->data, sizeof(PVOID), &data, sizeof(PVOID));
            break;
        }
        default:
            break;
        }
        pVal = (JsApiIpcArgument*)((BYTE*)pVal + pVal->header.len);
    }
    return true;
}

bool EtHttpPlaybackApiTaskClass::TransformValueList(KJSVariantList& args)
{
    
    for (size_t i = 0, cnt = args.size(); i < cnt; ++i)
    {
        KJSVariant &var = args.GetItem(i);
        if (V_VT(&var) == VT_BYREF)
        {
            ApiObjectInterface data = var.GetByRef();
            if (!findAndReplace(data, m_objMap))
                return false;
            var = data;
        }
    }
    return true;
}

void EtHttpPlaybackApiTaskClass::GainObjMap(const JsApiIpcRpcCall& rpcCall, KJSVariantList& oldRes, KJSVariantList& retList)
{
    switch (rpcCall.type)
        {
        case JAT_GetProperties:
        case JAT_GetRootProperties:
        {
            m_funcMap[V_BYREF(&oldRes.GetItem(0))] = V_BYREF(&retList.GetItem(0));
            m_funcMap[V_BYREF(&oldRes.GetItem(1))] = V_BYREF(&retList.GetItem(1));
            m_funcMap[V_BYREF(&oldRes.GetItem(2))] = V_BYREF(&retList.GetItem(2));
            break;
        }
        case JAT_Execute:
        {
            if (oldRes.size() == 0)
                break;
        }
        case JAT_Get:
        case JAT_CallDefaultMember:
        case JAT_ToString:
        case JAT_ValueOf:
        {
            m_funcMap[V_BYREF(&oldRes.GetItem(1))] = V_BYREF(&retList.GetItem(3));
            KJSVariant& pObj = retList.GetItem(1);
            if (!pObj.IsEmpty() && pObj.IsInterfaceType())
                m_objMap[V_BYREF(&oldRes.GetItem(0))] = (IUnknown *)V_BYREF(&pObj);
            break;
        }
    }
}


// ================== EtHttpFullUpdateSourceSheetTaskClass ==================
EtHttpFullUpdateSourceSheetTaskClass::EtHttpFullUpdateSourceSheetTaskClass(KEtWorkbook* wwb)
        : EtHttpTaskClassBase(wwb)
{
    m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT EtHttpFullUpdateSourceSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    IKWorksheets* pSheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    binary_wo::VarObj param = cmd->cast().get("param");
    INT iDefaultColRowCount = 6;
    if (param.has("defualtColRowCount"))
        iDefaultColRowCount = param.field_int32("defualtColRowCount");
	bool bHasSheetIdVec = param.has("sheetIdVec");
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    HRESULT hr = S_OK;
    int len = 0;
    VarObj sheetIdVec;
    if (bHasSheetIdVec)
    {
        sheetIdVec = param.get("sheetIdVec");
        len = sheetIdVec.arrayLength();
    }
    wo::sa::Leave leaveArray = wo::sa::enterArray(&acpt, "sheets");
	if (len > 0)
	{
        VAR_OBJ_EXPECT_ARRAY(param, "sheetIdVec");
		for (int i = 0; i < len; ++i)
        {
            IDX sheetIdx = INVALIDIDX;
            pBook->STSheetToRTSheet(sheetIdVec.at(i).value_int32(), &sheetIdx);
            if (sheetIdx == INVALIDIDX)
                continue;
			hr = writeEtSyncData(ctx, &acpt, sheetIdx, iDefaultColRowCount);
            if (FAILED(hr))
                return hr;
        }
	}
	else
	{
		for (int i = 0, len = pSheets->GetSheetCount(); i < len; ++i)
        {
            hr = writeEtSyncData(ctx, &acpt, i, iDefaultColRowCount);
            if (FAILED(hr))
                return hr;
        }
	}
    return S_OK;
}

PCWSTR EtHttpFullUpdateSourceSheetTaskClass::GetTag()
{
    return __X("http.et.fullUpdateSourceSheet");
}

HRESULT EtHttpFullUpdateSourceSheetTaskClass::recognizeTableStruct(ISheet* pSheet, RANGE& rg, INT iDefaultColRowCount)
{
    COL contentRgLeftBorder = -1, contentRgRightBorder = -1;
	int titleCnt = 0;
	auto errCode = TableStructRecHelper::calcTitleCnt(pSheet, rg, false, contentRgLeftBorder, contentRgRightBorder, titleCnt);
    // 当表头中包含空单元格时会返回TableStructRecFailed，产品要求这种情况下给空单元格填充一个固定的列名，因此不返回失败
	if (errCode == TableStructRecHelper::TableStructRecSuccessed || errCode == TableStructRecHelper::TableStructRecFailed)
		rg.SetRowFrom(titleCnt - 1);
    else if (errCode == TableStructRecHelper::TableStructRecNull)
    {
        // 如果是空表，就构造一个默认的表结构
        const int iDefaultTo = iDefaultColRowCount - 1;
        rg.SetRowFromTo(0, iDefaultTo + 1);
	    rg.SetColFromTo(0, iDefaultTo);
    }
    else if (errCode != TableStructRecHelper::TableStructRecOverLimit)
        return TableStructRecHelper::GetResult(errCode);
    return S_OK;
}

namespace
{
// 去掉没有数据的区域
void fitRange(ISheet* pSheet, RANGE& rg)
{
    et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
    RECT rc{0};
    rc.top = rg.RowFrom(), rc.bottom = rg.RowTo(),
    rc.left = rg.ColFrom(), rc.right = rg.ColTo();
    ROW bottomRow = spSheetEnum->GetLastNonEmptyRow(rc);
    if (INVALIDIDX != bottomRow && bottomRow < rc.bottom)
    {
        rc.bottom = bottomRow;
        rg.SetRowTo(bottomRow);
    }
    COL rightCol =  spSheetEnum->GetLastNonEmptyCol(rc);
    if (INVALIDIDX != rightCol && rightCol < rc.right)
        rg.SetColTo(rightCol);
}

class BanEtIdContainerTrackerScope
{
public:
    explicit BanEtIdContainerTrackerScope(IEtIdContainer* pEtIdContainer) :
        m_pEtIdContainer(pEtIdContainer)
    {
        m_pEtIdContainer->SetEnableTracker(FALSE);
    }

    ~BanEtIdContainerTrackerScope()
    {
        m_pEtIdContainer->SetEnableTracker(TRUE);
    }

private:
    IEtIdContainer* m_pEtIdContainer = nullptr;
};
}  // namespace anonymous

HRESULT EtHttpFullUpdateSourceSheetTaskClass::writeEtSyncData(IN KEtRevisionContext* ctx, IN ISerialAcceptor* acpt, IN IDX sheetIdx, INT iDefaultColRowCount)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet || !spSheet->IsGridSheet())
		return E_KSHEET_NOT_GRIDSHEET;
	
    PCWSTR sheetName = nullptr;
    spSheet->GetName(&sheetName);
    UINT stId = spSheet->GetStId();

    HRESULT hr = S_OK;
	RECT usedRect;
	hr = spSheet->CalcUsedScale(&usedRect);
    if (FAILED(hr))
        return hr;
	RANGE rg(spSheet->GetBMP());
	rg.SetSheetFromTo(sheetIdx);
	rg.SetRowFromTo(usedRect.top, usedRect.bottom);
	rg.SetColFromTo(usedRect.left, usedRect.right);
    if (!rg.IsValid())
		return E_INVALIDARG;
    if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
		return E_NOT_HAVE_PERMISSION;
    wo::sa::Leave leave = wo::sa::enterStruct(acpt, nullptr);
	acpt->addInt32("id", stId);
    acpt->addString("name", sheetName);
    wo::ISheetStake* pSheetStake = spSheet->GetWoStake();
	ks_stdptr<IEtIdContainer> spEtIdContainer = pSheetStake->GetEtIdContainer();
    if (checkNeedRecognizeTitle(spSheet.get(), spEtIdContainer.get(), usedRect))
    {
        // 表头Id非法时，重新进行表头识别
        hr = recognizeTableStruct(spSheet, rg, iDefaultColRowCount);
        if (FAILED(hr))
            return hr;
    }
    else
    {
        ROW rowFrom = spEtIdContainer->GetHeaderIndex();
        rg.SetRowFrom(rowFrom);
        if (rowFrom > rg.RowTo())
            rg.SetRowTo(rowFrom);
    }
    IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	int colCnt = rg.ColTo() - rg.ColFrom() + 1;
    ks_stdptr<IEt2DbSyncData> spEt2DbSyncData;
    spSheet->GetExtDataItem(edSheetEt2DbSyncData, (IUnknown**)&spEt2DbSyncData);
	if (!spEt2DbSyncData)
	{
		spEt2DbSyncData.clear();
		VS(_appcore_CreateObject(CLSID_KEt2DbSyncData, IID_IEt2DbSyncData, (void**)&spEt2DbSyncData));
		VS(spSheet->SetExtDataItem(edSheetEt2DbSyncData, spEt2DbSyncData));
        spEt2DbSyncData->Init(spSheet);
	}
    ks_stdptr<IEtSyncCtx> spEtSyncCtx;
    pBook->GetExtDataItem(edBookEtSyncCtx, (IUnknown**)&spEtSyncCtx);
    if (!spEtSyncCtx)
	{
		VS(_appcore_CreateObject(CLSID_KEtSyncCtx, IID_IEtSyncCtx, (void**)&spEtSyncCtx));
		VS(pBook->SetExtDataItem(edBookEtSyncCtx, spEtSyncCtx));
	}
    if (!spEtIdContainer)
	{
		VS(_appcore_CreateObject(CLSID_KEtIdContainer, IID_IEtIdContainer, (void**)(&spEtIdContainer)));
		spEtIdContainer->Init(spSheet);
    	pSheetStake->SetEtIdContainer(spEtIdContainer.get());
	}
    BanEtIdContainerTrackerScope stScope(spEtIdContainer.get());
    fitRange(spSheet.get(), rg);
    int iRowCnt = spEtIdContainer->GetRowCount();
    int iColumnCnt = spEtIdContainer->GetColumnCount();
    if (rg.RowTo() >= iRowCnt)
        VS(spEtIdContainer->InsertRows(iRowCnt, rg.RowTo() - iRowCnt + 1));
    if (rg.ColTo() >= iColumnCnt)
        VS(spEtIdContainer->InsertColumns(iColumnCnt, rg.ColTo() - iColumnCnt + 1));
    EtDbId headerId = spEtIdContainer->RowIdAt(rg.RowFrom());
    if (INV_EtDbId == headerId)
        return E_DBSHEET_SYNC_GRIDSHEET_HEADER_ROW_INVALID;
    spEtIdContainer->SetHeaderId(headerId);
    // 根据最新计算出来的区域，清理掉多余的Id
    spEtIdContainer->ShrinkToFit(rg.RowTo(), rg.ColTo());
    spEt2DbSyncData->CalcSkipRanges();
    int primaryCol = rg.ColFrom();
	Et2DbFieldCopyHelper stFieldCopyHelper(m_wwb->GetCoreWorkbook(), static_cast<etoldapi::_Worksheet*>(pWorksheet), nullptr, ctx, rg);
	// 推导类型的时候要去掉表头区域
    stFieldCopyHelper.SetIterRowsByRange(rg.RowFrom() + 1, rg.RowTo());
    {
        wo::sa::Leave leaveArray = wo::sa::enterArray(acpt, "fields");
        for (int i = 0, col = rg.ColFrom(); i < colCnt && col <= rg.ColTo(); ++i, ++col)
        {
            if (spEt2DbSyncData->IsSkipCol(col))
                continue;
            stFieldCopyHelper.BeginDeduceFieldType();
            for (;stFieldCopyHelper.Valid();stFieldCopyHelper.Next())
            {
                ROW row = stFieldCopyHelper.GetCurRow();
                if (spEt2DbSyncData->IsSkipCell(row, col))
                    continue;
                hr = stFieldCopyHelper.DeduceFieldType(col);
                if (hr == S_OK)
                    break;
            }
            stFieldCopyHelper.EndDeduceFieldType();
            ET_DbSheet_FieldType fldType = ET_DbSheet_FieldType_Invalid;
            PCWSTR numFmt = nullptr;
            stFieldCopyHelper.GetTypeInfo(fldType, numFmt);
            if (col == primaryCol)
                stFieldCopyHelper.CorrectionForPrimaryField(fldType, numFmt);
            EtDbId id = spEtIdContainer->ColumnIdAt(col);
            ks_stdptr<IEtFieldItem> spEtFieldItem;
            spEt2DbSyncData->GetFieldItem(id, &spEtFieldItem);
            if (!spEtFieldItem)
                spEt2DbSyncData->AddFieldItem(id, &spEtFieldItem);
            spEtFieldItem->SetFieldType(fldType);
            PCWSTR typeStr = nullptr;
            VS(_appcore_GainEncodeDecoder()->EncodeFieldType(fldType, &typeStr));
            wo::sa::Leave leaveStruct = wo::sa::enterStruct(acpt, nullptr);
            acpt->addString("type", typeStr);
            EtDbIdStr buf;
            VS(_appcore_GainDbSheetContext()->EncodeEtDbId(id, &buf)); 
            acpt->addString("fieldId", buf);
        }
    }
    EtDbId id = spEtIdContainer->GetHeaderId();
    EtDbIdStr buf;
    VS(_appcore_GainDbSheetContext()->EncodeEtDbId(id, &buf)); 
    acpt->addString("headerId", buf);
    return S_OK;
}

bool EtHttpFullUpdateSourceSheetTaskClass::checkNeedRecognizeTitle(ISheet* pSheet, IEtIdContainer* pEtIdContainer, const RECT& rc)
{
    // 首次创建或者旧表头被删除，重新识别表头
    if (!pEtIdContainer || INV_EtDbId == pEtIdContainer->GetHeaderId())
        return true;
    ROW headerRow = pEtIdContainer->GetHeaderIndex();
	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
    int iFirstNonEmptyRow = spSheetEnum->GetFirstNonEmptyRow(rc);
    // 旧表头为空行，也重新识别表头
    return iFirstNonEmptyRow == INVALIDIDX || iFirstNonEmptyRow > headerRow;
}

EtHttpImportWorkBookClass::EtHttpImportWorkBookClass(KEtWorkbook* wwb)
        : EtHttpTaskClassBase(wwb)
{
}

HRESULT EtHttpImportWorkBookClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] begin";
    binary_wo::VarObj param = cmd->cast().get("param");
    WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = ctx, wmh.objParam = param, wmh.strField = "mimeDataId";
	QString path = wmh.resolvePath();
    if (!QFile(path).exists())
    {
        WOLOG_ERROR << "[ETDA][EtHttpImportWorkBookClass] file is not exist!";
        return E_DATAANALYZE_FAILED;
    }
    else
    {
        WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] file : "<< path <<"exist";
    }
    int targetSheetStId = -1;
    if (param.has("sheetId"))
    {
        targetSheetStId = param.field_int32("sheetId");
        if (targetSheetStId == -1)
            return E_DATAANALYZE_FAILED;
    }

    ks_wstring sheetName;
    if (param.has("sheetName"))
        sheetName = param.field_str("sheetName");

    auto start = std::chrono::high_resolution_clock::now();
    DataAnalyseImportHelper importer(m_wwb, targetSheetStId);
    HRESULT hr = importer(this, cmd, ctx, sheetName, path);
    std::chrono::duration<double> duration = std::chrono::duration_cast<std::chrono::duration<double>>(std::chrono::high_resolution_clock::now() - start);
    WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] csv import workbook , duration: " << duration.count();

    ISheet* pSheet = importer.getTargetSheet();
    if (SUCCEEDED(hr))
    {
        hr = DealWithImportInfo(param, pSheet, true);
        if (SUCCEEDED(hr))
        {
            IDX sheetIdx = -1;
            importer.getTargetSheet()->GetIndex(&sheetIdx);
            IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
            //删除重复项
            CheckRemoveDuplicates(param, ks_castptr<etoldapi::_Worksheet>(pWorksheet));
            //导出工作簿需要删除默认的空表
            CheckNewFileEmptySheet(param, ctx, pSheet);
            if (ctx->isInRevisionMode())
            {
                importer.getTargetSheet()->GetIndex(&sheetIdx);
                WOLOG_INFO<< "[ETDA][EtHttpImportWorkBookClass] sheetIdx: "<< sheetIdx;
                m_wwb->setLastTaskSheet(sheetIdx);
            }
            _Workbook* pwb = m_wwb->GetCoreWorkbook();
            m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(pwb);

            binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
            KSerialWrapBinWriter acpt(*pResponse, ctx);
            acpt.addInt32("activeSheetId", importer.getTargetSheet()->GetStId());
        }
    }
    else
    {
        DealWithImportInfo(param, pSheet, false); // add failed log
    }
    WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] end hr:" << hr;
    return hr;
}

HRESULT EtHttpImportWorkBookClass::CheckRemoveDuplicates(const binary_wo::VarObj& param, etoldapi::_Worksheet* pWorksheet)
{
    bool isTagSource = false, isRemoveDuplicates = false;
    if (param.has("isTagSource"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "isTagSource")
        isTagSource = param.field_bool("isTagSource");
    }
    if (param.has("isDeduplication"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "isDeduplication")
        isRemoveDuplicates = param.field_bool("isDeduplication");
    }

    if (isRemoveDuplicates)
    {
        HRESULT hr = filemerge::RemoveDuplicates(m_wwb, pWorksheet, isTagSource);
        if (FAILED(hr))
            WOLOG_ERROR << "[ETDA][RemoveDuplicates] Remove Duplicates fail hr: " << hr;
        WOLOG_INFO << "[ETDA][RemoveDuplicates] Remove Duplicates Succeed";
    }
    return S_OK;
}

HRESULT EtHttpImportWorkBookClass::CheckNewFileEmptySheet(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISheet* pSheet)
{
    if (param.has("isNewFile"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "isNewFile")
        bool isNewFile = param.field_bool("isNewFile");
        WOLOG_INFO << "[ETDA][CheckNewFileEmptySheet] isNewFile: "<< isNewFile;
        if (!isNewFile)
            return S_OK;

        INT nCount = 0, shtIdx = 0;
        IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
        pBook->GetSheetCount(&nCount);
        pSheet->GetIndex(&shtIdx);
        std::vector<UINT> vecSheetStId;
        for (int i = 0; i < nCount; ++i)
        {
            UINT delSheetStIdx = -1;
            if (i != shtIdx && SUCCEEDED(pBook->RTSheetToSTSheet(i, &delSheetStIdx)))
                vecSheetStId.push_back(delSheetStIdx);
        }
        HRESULT hr = SheetOperatorHelper::DeleteSheets(m_wwb, ctx, vecSheetStId);
        if (FAILED(hr))
            WOLOG_ERROR << "[ETDA][CheckNewFileEmptySheet] new book Remove old sheet fail hr: " << hr;
        return hr;
    }
    return S_OK;
}

HRESULT EtHttpImportWorkBookClass::DealWithImportInfo(binary_wo::VarObj& param, ISheet* pSheet, bool bSuccess)
{
    if (!pSheet)
        return E_DATAANALYZE_FAILED;
    if (param.has("importData"))
    {
	    binary_wo::VarObj varImportData = param.get_s("importData");
        auto importDataPair = varImportData.as_array_buffer_s();
        if (importDataPair.first == nullptr)
        {
            WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] importData type error";
        }
        std::string importDataBuffer(reinterpret_cast<const char*>(importDataPair.first), importDataPair.second);
        if (importDataBuffer.empty())
        {
            WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] importData is null";
        }
        else 
        {
            pSheet->SetAutoMergeSheet(true);
        }
        WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] importData.size:  " << importDataPair.second;
        ks_stdptr<IEtDataAnalyzeData> spData;
        pSheet->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
        if (!spData)
        {
            WOLOG_ERROR << "[ETDA][EtHttpImportWorkBookClass] GetExtDataItem failed!";
            return E_DATAANALYZE_FAILED;
        }

        auto importData = getDAClientInstance()->DeserializationImportData(importDataBuffer);
        spData->ClearDataAnalyseInfo();
        for (auto [taskId, mergeInfo]: importData.first)
        {
            WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] taskList.size:  " << importDataPair.second;
            mergeInfo.mergeFileId = krt::fromUtf16(m_wwb->getFileId());
            spData->AddDataAnalyseInfo(taskId, mergeInfo);
        }
        spData->ClearDataAnalyseLog();
        for (auto it = importData.second.begin(); it != importData.second.end(); ++it)
        {
            auto& log = *it;
            WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] logList.size:  " << importDataPair.second;
            if (!bSuccess && (it == importData.second.end() - 1))//调整最新的一个日志为失败
                log.status == "failed";

            spData->AddDataAnalyseLog(log);
        }
    }
    else
    {
        WOLOG_INFO << "[ETDA][EtHttpImportWorkBookClass] no importData";
    }
    return S_OK;
}

PCWSTR EtHttpImportWorkBookClass::GetTag()
{
    return __X("http.et.dataAnalyzeImportWorkBook");
}

EtHttpDeleteDataAnalyzeMergeTaskClass::EtHttpDeleteDataAnalyzeMergeTaskClass(KEtWorkbook* wwb)
        : EtHttpTaskClassBase(wwb)
{
}

HRESULT EtHttpDeleteDataAnalyzeMergeTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    if (ctx->isUndoRedo())
        return S_FALSE;
    WOLOG_INFO << "[ETDA][EtHttpDeleteDataAnalyzeMergeTaskClass] begin";
    binary_wo::VarObj param = cmd->cast().get("param");

    VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId")
    IDX sheetIdx = GetSheetIdx(param);
    if (INVALIDIDX == sheetIdx)
    {
        WOLOG_ERROR << "[ETDA][EtHttpDeleteDataAnalyzeMergeTaskClass] SHEET_NOT_FOUND";
        return E_KSHEET_SHEET_NOT_FOUND;
    }
	ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);
    if (!spSheet)
    {
        WOLOG_ERROR << "[ETDA][EtHttpDeleteDataAnalyzeMergeTaskClass] SHEET_NOT_FOUND";
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    VAR_OBJ_EXPECT_NUMERIC(param, "taskId")
    int taskId = param.field_int32("taskId");
    ks_stdptr<IEtDataAnalyzeData> spData;
    spSheet->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
    if (!spData)
    {
        WOLOG_ERROR << "[ETDA][EtHttpDeleteDataAnalyzeMergeTaskClass] GetExtDataItem failed";
        return E_FAIL;
    }
    spData->DeleteDataAnalyseInfo(taskId);
    spSheet->SetAutoMergeSheet(false);
    WOLOG_INFO << "[ETDA][EtHttpDeleteDataAnalyzeMergeTaskClass] end";
    return S_OK;
}

PCWSTR EtHttpDeleteDataAnalyzeMergeTaskClass::GetTag()
{
    return __X("http.et.deleteDataAnalyzeMergeTask");
}

EtHttpAddDataAnalyzeLogClass::EtHttpAddDataAnalyzeLogClass(KEtWorkbook* wwb)
        : EtHttpTaskClassBase(wwb)
{
}

PCWSTR EtHttpAddDataAnalyzeLogClass::GetTag()
{
    return __X("http.et.addDataAnalyzeLog");
}

HRESULT EtHttpAddDataAnalyzeLogClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    if (ctx->isUndoRedo())
        return S_FALSE;
    WOLOG_INFO << "[ETDA][EtHttpAddDataAnalyzeLogClass] begin";
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId")
    IDX sheetIdx = GetSheetIdx(param);
    if (INVALIDIDX == sheetIdx)
    {
        WOLOG_ERROR << "[ETDA][EtHttpAddDataAnalyzeLogClass] SHEET_NOT_FOUND";
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);
    if (!spSheet)
    {
        WOLOG_ERROR << "[ETDA][EtHttpAddDataAnalyzeLogClass] SHEET_NOT_FOUND";
        return E_KSHEET_SHEET_NOT_FOUND;
    }
    VAR_OBJ_EXPECT_NUMERIC(param, "taskId")
    int taskId = param.field_int32("taskId");

    ks_stdptr<IEtDataAnalyzeData> spData;
    spSheet->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
    if (!spData)
    {
        WOLOG_ERROR << "[ETDA][EtHttpAddDataAnalyzeLogClass] GetExtDataItem failed";
        return E_FAIL;
    }
    etda::MergeInfo* pInfo = spData->GetDataAnalyseInfo(taskId);
    if (!pInfo)
    {
        WOLOG_ERROR << "[ETDA][EtHttpAddDataAnalyzeLogClass] NO_TASKID";
        return E_DATAANALYZE_NO_TASKID;
    }

    VAR_OBJ_EXPECT_STRING(param, "date")
    QString date = krt::fromUtf16(param.field_str("date"));
    VAR_OBJ_EXPECT_STRING(param, "time")
    QString time = krt::fromUtf16(param.field_str("time"));
    VAR_OBJ_EXPECT_STRING(param, "status")
    QString status = krt::fromUtf16(param.field_str("status"));
    VAR_OBJ_EXPECT_NUMERIC(param, "updateType")
    int updateType = param.field_int32("updateType");
    VAR_OBJ_EXPECT_ARRAY(param, "sources")
    binary_wo::VarObj sources = param.get_s("sources");
    std::vector<etda::ImportFileLog> sourcesLog;
    for (int i = 0; i < sources.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = sources.at_s(i);
        etda::ImportFileLog fileLog;
        VAR_OBJ_EXPECT_STRING(item, "fileName")
        fileLog.fileName = krt::fromUtf16(item.field_str("fileName"));
        VAR_OBJ_EXPECT_STRING(item, "sheetName")
        fileLog.sheetName = krt::fromUtf16(item.field_str("sheetName"));
        VAR_OBJ_EXPECT_STRING(item, "status")
        fileLog.status = krt::fromUtf16(item.field_str("status"));
        VAR_OBJ_EXPECT_NUMERIC(item, "cols")
        fileLog.cols = item.field_int32("cols");
        VAR_OBJ_EXPECT_STRING(item, "failReason")
        fileLog.failReason = krt::fromUtf16(item.field_str("failReason"));
        if (item.has("errorCode"))
        {
            fileLog.errorCode = krt::fromUtf16(item.field_str("errorCode"));
        }
        sourcesLog.push_back(fileLog);
    }
    etda::MergeLog log;
    log.date = date;
    log.time = time;
    log.status = status;
    log.updateType = updateType;
    log.taskId = taskId;
    log.sources = sourcesLog;
    spData->AddDataAnalyseLog(log);
    WOLOG_INFO << "[ETDA][EtHttpAddDataAnalyzeLogClass] end";
    return S_OK;
}

EtHttpUpdateDataAnalyzeDataSourceNameClass::EtHttpUpdateDataAnalyzeDataSourceNameClass(KEtWorkbook* wwb)
    : EtHttpTaskClassBase(wwb)
{

}

PCWSTR EtHttpUpdateDataAnalyzeDataSourceNameClass::GetTag()
{
    return __X("http.et.updateDataAnalyzeDataSourceName");
}

HRESULT EtHttpUpdateDataAnalyzeDataSourceNameClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    if (ctx->isUndoRedo())
        return S_FALSE;
    WOLOG_INFO << "[ETDA][EtHttpUpdateDataAnalyzeDataSourceNameClass] begin";
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_BOOL(param, "isNewTask")
    bool isNewTask = param.field_bool("isNewTask");
    etda::MergeInfo* pInfo = nullptr;
    
    VAR_OBJ_EXPECT_NUMERIC(param, "taskId")
    int taskId = param.field_int32("taskId");
    if (!isNewTask)
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId")
        IDX sheetIdx = GetSheetIdx(param);
        ks_stdptr<IEtDataAnalyzeData> spData;
        ks_stdptr<ISheet> spSheet;
        if (INVALIDIDX == sheetIdx)
        {
            WOLOG_ERROR << "[ETDA][EtHttpUpdateDataAnalyzeDataSourceNameClass] SHEET_NOT_FOUND";
            return E_KSHEET_SHEET_NOT_FOUND;
        }
        IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
        pBook->GetSheet(sheetIdx, &spSheet);
        if (!spSheet)
        {
            WOLOG_ERROR << "[ETDA][EtHttpUpdateDataAnalyzeDataSourceNameClass] SHEET_NOT_FOUND";
            return E_KSHEET_SHEET_NOT_FOUND;
        }
        spSheet->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
        if (!spData)
        {
            WOLOG_ERROR << "[ETDA][EtHttpUpdateDataAnalyzeDataSourceNameClass] GetExtDataItem failed";
            return E_FAIL;
        }
        pInfo = spData->GetDataAnalyseInfo(taskId);
    }
    else
    {
        pInfo = getDAClientInstance()->GetMergeInfo(taskId);
    }
    
    if (!pInfo)
    {
        WOLOG_ERROR << "[ETDA][EtHttpUpdateDataAnalyzeDataSourceNameClass] NO_TASKID";
        return E_DATAANALYZE_NO_TASKID;
    }
    VAR_OBJ_EXPECT_ARRAY(param, "sources")
    binary_wo::VarObj sources = param.get_s("sources");
    for (int i = 0; i < sources.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = sources.at_s(i);
        VAR_OBJ_EXPECT_STRING(item, "bookName")
        QString bookName = krt::fromUtf16(item.field_str("bookName"));
        VAR_OBJ_EXPECT_STRING(item, "sheetName")
        QString sheetName = krt::fromUtf16(item.field_str("sheetName"));
        VAR_OBJ_EXPECT_NUMERIC(item, "sheetStId")
        int sheetStId = item.field_int32("sheetStId");
        VAR_OBJ_EXPECT_STRING(item, "fileId")
        QString fileId = krt::fromUtf16(item.field_str("fileId"));
        for (auto& source : pInfo->dataSourceConfig)
        {
            if (source.fileId == fileId && source.sheetStId == sheetStId)
            {
                source.bookName = bookName;
                source.sheetName = sheetName;
                WOLOG_INFO << "[ETDA][EtHttpUpdateDataAnalyzeDataSourceNameClass] update file source success";
            }
        }
    }
    WOLOG_INFO << "[ETDA][EtHttpUpdateDataAnalyzeDataSourceNameClass] end";
    return S_OK;
}

} // wo

