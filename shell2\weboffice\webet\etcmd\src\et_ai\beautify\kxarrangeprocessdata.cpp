﻿#include "kxarrangeprocessdata.h"
#include "etstdafx.h"
#include <smartparam.h>

#define COL_WIDTH_FEW 15
#define MAX_COL_DEFAULT_CHAR_CNT 50 //表示超过此阈值就要考虑折行处理
#define COL_WIDTH_MODERATE 30
#define MAX_TABLEHEAD_COL_MAX_CHAR_CNT 20
#define COL_ALIGN_LEFT_MIN_WIDTH 18

static const LPCWSTR CELLIMG_FMLA_PATTERN = __X("=DISPIMG(\"");
namespace etai
{
float viewWidth2CharsWidth(int viewWidth, IKWorkbook *pWorkbook, bool bNeedDeviation /* = true*/)
{
    INT poundWidth = viewWidth * 20 + 1;
    float charsWidth = app_helper::GetCharsWithColWidth(pWorkbook, poundWidth);
    if (!bNeedDeviation)
        return charsWidth;
    float deviation = 2; //字符的误差
    if (charsWidth <= 20)
        deviation = 2;
    else if (charsWidth <= 35)
        deviation = 0;
    else if (charsWidth <= 55)
        deviation = 1;
    else if (charsWidth <= 75)
        deviation = -0.5;
    else if (charsWidth <= 90)
        deviation = 0;
    else if (charsWidth <= 110)
        deviation = -1.5;
    else if (charsWidth <= 140)
        deviation = -2;
    else if (charsWidth <= 200)
        deviation = -3.5;
    charsWidth = charsWidth + deviation;
    return charsWidth;
}

bool TableInfo::isEmptyTableInfo()
{
    return vecTitleRangeInfo.empty() && vecHeadRangeInfo.empty() && vecContentRangeInfo.empty() &&
        vecSubTitleRangeInfo.empty() && vecOtherRangeInfo.empty();
}

TableApplyParam::TableApplyParam(WholeEffectType effectType /*= AdaptEffect*/)
{
    setEffectType(effectType);
    bProcessBlank = true;
    m_bApplyFont = true;
}

void TableApplyParam::setEffectType(WholeEffectType effectType)
{
    if (m_spWholeEffect)
        m_spWholeEffect.reset(nullptr);

    m_effectType = effectType;

    if (effectType == CompactEffect)
        m_spWholeEffect = std::make_unique<CompactWholeEffect>();
    else if (effectType == LooseEffect)
        m_spWholeEffect = std::make_unique<LooseWholeEffect>();
    else if (effectType == AdaptScreenEffect)
        m_spWholeEffect = std::make_unique<AdaptScreenWholeEffect>();
    else
        m_spWholeEffect = std::make_unique<AdaptWholeEffect>();
}

void TableApplyParam::setApplyFont(bool bApplyFont)
{
    m_bApplyFont = bApplyFont;
}

bool TableApplyParam::getApplyFont() const
{
    return m_bApplyFont;
}

WholeEffectType TableApplyParam::getEffectType() const
{
    return m_effectType;
}

WholeEffectBase *TableApplyParam::getWholeEffectInfo() const
{
    return m_spWholeEffect.get();
}

bool TableApplyParam::isNeedProcessBlank() const
{
    return bProcessBlank;
}

QString TableApplyParam::getEffectTypeName() const
{
    QString effect_type;
    switch (m_effectType)
    {
    case AdaptEffect:
        effect_type = "fit";
        break;
    case CompactEffect:
        effect_type = "retrench";
        break;
    case LooseEffect:
        effect_type = "loose";
        break;
    case AdaptScreenEffect:
        effect_type = "adapt_screen";
        break;
    case DefaultEffect:
        break;
    }
    return effect_type;
}

AtomicRange::AtomicRange() : iTop(-1), iBottom(-1), iLeft(-1), iRight(-1)
{
}

AtomicRange::AtomicRange(long nT, long nB, long nL, long nR) : iTop(nT), iBottom(nB), iLeft(nL), iRight(nR)
{
}

AtomicRange::AtomicRange(const ES_CUBE &cube)
{
    iTop = cube.rowFrom;
    iBottom = cube.rowTo;
    iLeft = cube.colFrom;
    iRight = cube.colTo;
}

AtomicRange::AtomicRange(Range *pRange)
{
    ResetRange(pRange);
}

AtomicRange::AtomicRange(const AtomicRange &rg) : iTop(rg.iTop), iBottom(rg.iBottom), iLeft(rg.iLeft), iRight(rg.iRight)
{
}

bool AtomicRange::IsOneCell() const
{
    return (iTop == iBottom && iLeft == iRight);
}

bool AtomicRange::IsInvalid() const
{
    return iTop < 0 || iBottom < 0 || iLeft < 0 || iRight < 0 || iTop > iBottom || iLeft > iRight;
}

bool AtomicRange::IsInRange(long iRow, long iCol) const
{
    return (iRow >= iTop && iRow <= iBottom && iCol >= iLeft && iCol <= iRight);
}

bool AtomicRange::IsEqualRange(AtomicRange *pAtomicRange) const
{
    return (iTop == pAtomicRange->iTop && iLeft == pAtomicRange->iLeft && iBottom == pAtomicRange->iBottom &&
            iRight == pAtomicRange->iRight);
}

bool AtomicRange::IsOneRowCell() const
{
    return iTop == iBottom;
}

bool AtomicRange::isOneColCell() const
{
    return iLeft == iRight;
}

int AtomicRange::getRowCnt() const
{
    return iBottom - iTop + 1;
}

int AtomicRange::getColCnt() const
{
    return iRight - iLeft + 1;
}

void AtomicRange::ResetRange(Range *pRange)
{
    if (!pRange)
        return;
    HRESULT hr = S_CONTINUE;
    pRange->get_Row(&iTop);
    pRange->get_Column(&iLeft);
    iTop -= 1;
    iLeft -= 1;
    iBottom = iTop;
    iRight = iLeft;
    KCOMPTR(Range) spRows;
    hr = pRange->get_Rows(&spRows);
    if (SUCCEEDED(hr) && spRows)
    {
        spRows->get_Count(&iBottom);
        iBottom += iTop - 1;
    }
    KCOMPTR(Range) spColumns;
    hr = pRange->get_Columns(&spColumns);
    if (SUCCEEDED(hr) && spColumns)
    {
        spColumns->get_Count(&iRight);
        iRight += iLeft - 1;
    }
}

AtomicCells *MergeCellsList::FindCellInMergeCells(long iRow, long iCol) const
{
    for (auto it = begin(); it != end(); ++it)
    {
        AtomicCells *pCells = *it;
        if (pCells && pCells->IsInRange(iRow, iCol))
            return pCells;
    }
    return nullptr;
}

AtomicCells *ImgFmlaCellsList::FindCellInImgFmlaCells(AtomicCells *pAtomicRange) const
{
    if (!pAtomicRange)
        return nullptr;
    for (auto it = begin(); it != end(); ++it)
    {
        AtomicCells *pCells = *it;
        if (pCells && pCells->IsEqualRange(pAtomicRange))
            return pCells;
    }
    return nullptr;
}

void AtomicTable::BuildTable(Range *pRange, TableRangeInfo &tblInfo, TableApplyParam *pTableApplyParam)
{
    if (!pTableApplyParam)
        return;

    m_tblInfo = tblInfo;
    m_pTableApplyParam = pTableApplyParam;

    HRESULT hr = S_CONTINUE;
    AtomicRange userRange(pRange);

    ks_stdptr<Range> spRows;
    hr = pRange->get_Rows(&spRows);
    if (FAILED(hr) || !spRows)
        return;

    ks_stdptr<Range> spCols;
    hr = pRange->get_Columns(&spCols);
    if (FAILED(hr) || !spCols)
        return;

    VARIANT varEmpty;
    V_VT(&varEmpty) = VT_ERROR;
    V_ERROR(&varEmpty) = DISP_E_PARAMNOTFOUND;

    long nRowCount = iBottom - iTop + 1;
    long nColCount = iRight - iLeft + 1;
    for (long i = 0; i < nRowCount; ++i)
    {
        long iRow = iTop + i;
        // todo 这里空行能不能优化？
        std::unique_ptr<AtomicRow> spAtomicRow = std::make_unique<AtomicRow>(iRow, nColCount);

        //判断是否为隐藏行
        KComVariant varRow(iRow - userRange.iTop + 1, VT_I4);
        KComVariant var;
        hr = spRows->get_Item(varRow, varEmpty, &var);
        if (FAILED(hr))
            continue;
        ks_stdptr<Range> spRow = KSmartParam(var).GetInterfaceValue();
        if (!spRow)
            continue;
        KComVariant varHidden;
        spRow->get_Hidden(&varHidden);
        spAtomicRow->setHidden(KSmartParam(varHidden).GetBooleanValue());

        for (long j = 0; j < nColCount; ++j)
        {
            long iCol = iLeft + j;
            AtomicCells *pCells = m_mergeCellsList.FindCellInMergeCells(iRow, iCol);
            if (!pCells)
            {
                KComVariant varCol(iCol - userRange.iLeft + 1, VT_I4);
                KComVariant varCell;
                hr = pRange->get_Item(varRow, varCol, &varCell);
                if (SUCCEEDED(hr))
                {
                    ks_stdptr<Range> spCell = KSmartParam(varCell).GetInterfaceValue();
                    if (spCell)
                    {
                        // todo 空单元格能不能不创建？
                        pCells = NewCells(spCell);
                        if (pCells && !pCells->IsOneCell())
                        {
                            // 如果单元格是被合并的单元格，需要取合并单元格左上角的单元格才能取到内容
                            if (pCells->iTop != iRow || pCells->iLeft != iCol)
                            {
                                KComVariant varRowM(pCells->iTop - userRange.iTop + 1, VT_I4);
                                KComVariant varColM(pCells->iLeft - userRange.iLeft + 1, VT_I4);
                                KComVariant varCellM;
                                hr = pRange->get_Item(varRowM, varColM, &varCellM);
                                pCells = NULL;
                                if (SUCCEEDED(hr))
                                {
                                    ks_stdptr<Range> spCellM = KSmartParam(varCellM).GetInterfaceValue();
                                    if (spCellM)
                                    {
                                        pCells = NewCells(spCellM);
                                        if (pCells)
                                            m_mergeCellsList.push_back(pCells);
                                    }
                                }
                            }
                            else
                            {
                                m_mergeCellsList.push_back(pCells);
                            }
                        }
                        //收集单元格
                        spAtomicRow->vecCells.push_back(pCells);

                        //按列收集单元格
                        if (m_colList.find(iCol) != m_colList.end())
                        {
                            AtomicCol *pCol = m_colList.at(iCol).get();
                            if (pCol)
                                pCol->vecCells.push_back(pCells);
                        }
                        else
                        {
                            std::unique_ptr<AtomicCol> spAtomicCol = std::make_unique<AtomicCol>(iCol, nRowCount);

                            KComVariant var;
                            hr = spCols->get_Item(varCol, varEmpty, &var);
                            if (FAILED(hr))
                                continue;
                            ks_stdptr<Range> spCol = KSmartParam(var).GetInterfaceValue();
                            if (!spCol)
                                continue;
                            KComVariant varHidden;
                            spCol->get_Hidden(&varHidden);
                            spAtomicCol->setHidden(KSmartParam(varHidden).GetBooleanValue());

                            spAtomicCol->vecCells.push_back(pCells);
                            m_colList.insert(std::make_pair(iCol, std::move(spAtomicCol)));
                        }
                    }
                }
            }
            if (pCells != NULL)
            {
                //收集含有图片的单元格
                if (pCells->isImgFmlaCell() && m_imgFmlaCellsList.FindCellInImgFmlaCells(pCells) == NULL)
                    m_imgFmlaCellsList.push_back(pCells);

                if (pCells->iRight > pCells->iLeft)
                    j += pCells->iRight - pCells->iLeft;
            }
        }
        // Todo:空行要不要过滤掉 目前suyihong那边返回的行范围还会包含空行,后续如果能保证，这部分代码就可不用
        m_rowList.insert(std::make_pair(iRow, std::move(spAtomicRow)));
    }
}

AtomicCells *AtomicTable::NewCells(Range *pCellRange)
{
    std::unique_ptr<AtomicCells> spCells = std::make_unique<AtomicCells>(pCellRange, m_pWorkSheet->GetWorkbook());

    ZoneType zoneType = m_tblInfo.getCellZoneType(spCells->iTop, spCells->iLeft);
    spCells->BuildCells(pCellRange, zoneType);
    float fontSize = 10.0f;
    WholeEffectBase *pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
    if (pWholeEffect)
        fontSize = pWholeEffect->getFontSizeByZoneType(zoneType);
    bool bAllowProcessBlank = m_pTableApplyParam->isNeedProcessBlank();
    spCells->InitCellsProp(fontSize, bAllowProcessBlank);

    AtomicCells* pCells = spCells.get();
    m_vecTableCells.push_back(std::move(spCells));
    return pCells;
}

AtomicCells *AtomicTable::getMergeCell(long iRow, long iCol) const
{
    AtomicCells *pCells = m_mergeCellsList.FindCellInMergeCells(iRow, iCol);
    return pCells;
}

bool isInRange(const AtomicCells *pMergeCells, const ES_CUBE &area)
{
    if (!pMergeCells)
        return false;
    if (area.rowFrom <= pMergeCells->iTop && pMergeCells->iTop <= area.rowTo && area.rowFrom <= pMergeCells->iBottom &&
        pMergeCells->iBottom <= area.rowTo && area.colFrom <= pMergeCells->iLeft && pMergeCells->iLeft <= area.colTo &&
        area.colFrom <= pMergeCells->iRight && pMergeCells->iRight <= area.colTo)
        return true;
    return false;
}

int AtomicTable::getFirstMergeColIdx() const
{
    ES_CUBE fillAlterArea = m_tblInfo.getFillRowAlterArea();

    int idx = -1;
    for (size_t i = 0; i < m_mergeCellsList.size(); ++i)
    {
        AtomicCells *pMergeCells = m_mergeCellsList[i];
        if (pMergeCells && pMergeCells->iTop < pMergeCells->iBottom && isInRange(pMergeCells, fillAlterArea) &&
            (pMergeCells->iLeft < idx || idx < 0))
            idx = pMergeCells->iLeft;
    }
    return MAX(idx, m_tblInfo.getFirstColIdx());
}

int AtomicTable::getFirstHeaderRowIdx() const
{
    if (!m_tblInfo.vecHeadRangeInfo.empty())
        return m_tblInfo.vecHeadRangeInfo[0].rowFrom;
    return -1;
}

void AtomicTable::checkAndExpandMergeCellHeight(AtomicCells *pEachAtomicCells, bool bEnableAlignLeft)
{
    double eachLineHeight = pEachAtomicCells->getCellCharHeight();

    //再调行高,判断行高是否需要调整
    double sumWidth = getCellWidth(pEachAtomicCells);
    double calcSumHeight = pEachAtomicCells->estimatedCellHeight(sumWidth);

    //判断是否发生了折行，对单元格进行水平居左设置
    if (bEnableAlignLeft && calcSumHeight > eachLineHeight)
        pEachAtomicCells->setHAlign(etHAlignLeft);

    //计算合并单元格所占的高度
    double realSumHeight = getMergeCellHeight(pEachAtomicCells);
    if (realSumHeight < calcSumHeight) //判断会存在遮挡情况
    {
        double dMoreRowHeight = calcSumHeight - realSumHeight;
        expandMergeCellHeight(pEachAtomicCells, dMoreRowHeight);
    }
}

double AtomicTable::getCellWidth(AtomicRange *pEachAtomicRange) const
{
    HRESULT hr = S_CONTINUE;
    double sumWidth = 0;
    for (int colIdx = pEachAtomicRange->iLeft; colIdx <= pEachAtomicRange->iRight; colIdx++)
    {
        KComVariant colWidth;
        double dWidth = getPresetColWidth(colIdx);
        sumWidth += dWidth;
    }
    return sumWidth;
}

double AtomicTable::getPresetColWidth(int iCol) const
{
    if (m_colList.find(iCol) != m_colList.end())
    {
        AtomicCol *pEachAtomicCol = m_colList.at(iCol).get();
        return pEachAtomicCol->getColResultWidth();
    }

    return 0;
}

double AtomicTable::getPresetRowHeight(int iRow) const
{
    if (m_rowList.find(iRow) != m_rowList.end())
    {
        AtomicRow *pEachAtomicRow = m_rowList.at(iRow).get();
        return pEachAtomicRow->getRowResultHeight();
    }

    return 0;
}

void AtomicTable::presetRowHeight(int iRow, double dHeight)
{
    if (m_rowList.find(iRow) != m_rowList.end())
    {
        AtomicRow *pEachAtomicRow = m_rowList.at(iRow).get();
        return pEachAtomicRow->setRowResultHeight(dHeight);
    }
}

void AtomicTable::getTableSizeAndAve(double &w, double &h, double &wAve, double &hAve)
{
    //更新列宽
    w = 0;
    for (auto iter = m_colList.begin(); iter != m_colList.end(); iter++)
    {
        AtomicCol *pEachAtomicCol = iter->second.get();
        if (!pEachAtomicCol)
            continue;
        double dWidth = pEachAtomicCol->getColResultWidth();
        w += dWidth;
    }

    //更新列宽平均值
    wAve = 0;
    int colCnt = m_colList.size();
    if (colCnt > 0)
        wAve = w / colCnt;

    //更新行高
    h = 0;
    for (auto iter = m_rowList.begin(); iter != m_rowList.end(); iter++)
    {
        AtomicRow *pEachAtomicRow = iter->second.get();
        if (!pEachAtomicRow)
            continue;
        double dHeight = pEachAtomicRow->getRowResultHeight();
        h += dHeight;
    }

    //更新行高平均值
    hAve = 0;
    int rowCnt = m_rowList.size();
    if (rowCnt > 0)
        hAve = h / rowCnt;
}

double AtomicTable::getMergeCellHeight(AtomicRange *pEachAtomicRange) const
{
    HRESULT hr = S_CONTINUE;
    double realSumHeight = 0;
    for (int rowIdx = pEachAtomicRange->iTop; rowIdx <= pEachAtomicRange->iBottom; rowIdx++)
    {
        double dHeight = getPresetRowHeight(rowIdx);
        realSumHeight += dHeight;
    }
    return realSumHeight;
}

void AtomicTable::expandMergeCellHeight(AtomicRange *pEachAtomicRange, double dMoreRowHeight)
{
    HRESULT hr = S_CONTINUE;
    int rowCnt = pEachAtomicRange->getRowCnt();
    if (rowCnt <= 0)
        return;
    double dEachMoreRowHeight = dMoreRowHeight / rowCnt;
    //每一行平均分配
    for (int rowIdx = pEachAtomicRange->iTop; rowIdx <= pEachAtomicRange->iBottom; rowIdx++)
    {
        double dHeight = getPresetRowHeight(rowIdx);
        dHeight += dEachMoreRowHeight;
        presetRowHeight(rowIdx, dHeight);
    }
}

WholeEffectBase::WholeEffectBase()
    : m_TitleRangeFontSize(-1), m_SubTitleRangeFontSize(-1), m_HeadRangeFontSize(-1), m_ContentRangeFontSize(-1),
      m_OtherRangeFontSize(-1), m_InfoRangeFontSize(-1), m_RowHeightSpacing(0), m_ColMaxWidth(MAX_COL_DEFAULT_CHAR_CNT),
      m_ImgCellColMinWidth(20), m_ImgCellRowMinHeight(120)
{
}

WholeEffectBase::~WholeEffectBase()
{
}

double WholeEffectBase::getColMaxWidthWithChar() const
{
    return m_ColMaxWidth;
}

double WholeEffectBase::getImgCellColMinWidth() const
{
    return m_ImgCellColMinWidth;
}

double WholeEffectBase::getImgCellRowMinHeight() const
{
    return m_ImgCellRowMinHeight;
}

double WholeEffectBase::getRowSpacingWithChar() const
{
    return m_RowHeightSpacing;
}

single WholeEffectBase::getFontSizeByZoneType(ZoneType zoneType) const
{
    single fontSize = 10.0f;
    switch (zoneType)
    {
    case RowTitle:
        fontSize = getHeadRangeFontSize();
        break;
    case BigTitle:
        fontSize = getTitleRangeFontSize();
        break;
    case Content:
        fontSize = getContentRangeFontSize();
        break;
    case SubTitle:
        fontSize = getSubTitleRangeFontSize();
        break;
    case Other:
        fontSize = getOtherRangeFontSize();
        break;
    case Info:
        fontSize = getInfoRangeFontSize();
        break;
    case Empty:
        break;
    default:
        break;
    }
    return fontSize;
}

single WholeEffectBase::getTitleRangeFontSize() const
{
    return m_TitleRangeFontSize;
}

single WholeEffectBase::getHeadRangeFontSize() const
{
    return m_HeadRangeFontSize;
}

single WholeEffectBase::getContentRangeFontSize() const
{
    return m_ContentRangeFontSize;
}

single WholeEffectBase::getSubTitleRangeFontSize() const
{
    return m_SubTitleRangeFontSize;
}

single WholeEffectBase::getOtherRangeFontSize() const
{
    return m_OtherRangeFontSize;
}

single WholeEffectBase::getInfoRangeFontSize() const
{
    return m_InfoRangeFontSize;
}

AdaptWholeEffect::AdaptWholeEffect()
{
    m_TitleRangeFontSize = 16;
    m_SubTitleRangeFontSize = 14;
    m_HeadRangeFontSize = 11;
    m_ContentRangeFontSize = 10;
    m_OtherRangeFontSize = 10;
    m_InfoRangeFontSize = 10;

    m_RowHeightSpacing = 6;
    m_ColMaxWidth = MAX_COL_DEFAULT_CHAR_CNT;

    m_ImgCellColMinWidth = 20;
    m_ImgCellRowMinHeight = 120;
}

double AdaptWholeEffect::getColSpacingWithChar(double colWidthWidthChar) const
{
    double minSpacing = 0;
    double maxSpacing = 0;

    if (colWidthWidthChar < COL_WIDTH_FEW)
    {
        minSpacing = 1;
        maxSpacing = 3;
    }
    else if (colWidthWidthChar < COL_WIDTH_MODERATE)
    {
        minSpacing = 1;
        maxSpacing = 5;
    }
    else
    {
        minSpacing = 2;
        maxSpacing = 6;
    }
    // Todo:最终的列宽间距计算得再考虑超出一屏的规则
    double spacingWithChar = maxSpacing;
    return spacingWithChar;
}

CompactWholeEffect::CompactWholeEffect()
{
    m_TitleRangeFontSize = 16;
    m_SubTitleRangeFontSize = 14;
    m_HeadRangeFontSize = 11;
    m_ContentRangeFontSize = 10;
    m_OtherRangeFontSize = 10;
    m_InfoRangeFontSize = 10;

    m_RowHeightSpacing = 2;
    m_ColMaxWidth = 40;

    m_ImgCellColMinWidth = 15;
    m_ImgCellRowMinHeight = 110;
}

double CompactWholeEffect::getColSpacingWithChar(double colWidthWidthChar) const
{
    double minSpacing = 0;
    double maxSpacing = 0;

    if (colWidthWidthChar < COL_WIDTH_FEW)
    {
        minSpacing = 0;
        maxSpacing = 1;
    }
    else if (colWidthWidthChar < COL_WIDTH_MODERATE)
    {
        minSpacing = 0;
        maxSpacing = 2;
    }
    else
    {
        minSpacing = 0;
        maxSpacing = 2;
    }
    // Todo:最终的列宽间距计算得再考虑超出一屏的规则
    double spacingWithChar = maxSpacing;
    return spacingWithChar;
}

LooseWholeEffect::LooseWholeEffect()
{
    m_TitleRangeFontSize = 18;
    m_SubTitleRangeFontSize = 14;
    m_HeadRangeFontSize = 12;
    m_ContentRangeFontSize = 11;
    m_OtherRangeFontSize = 11;
    m_InfoRangeFontSize = 11;

    m_RowHeightSpacing = 14;
    m_ColMaxWidth = MAX_COL_DEFAULT_CHAR_CNT;

    m_ImgCellColMinWidth = 25;
    m_ImgCellRowMinHeight = 150;
}

double LooseWholeEffect::getColSpacingWithChar(double colWidthWidthChar) const
{
    double minSpacing = 0;
    double maxSpacing = 0;

    if (colWidthWidthChar < COL_WIDTH_FEW)
    {
        minSpacing = 4;
        maxSpacing = 5;
    }
    else if (colWidthWidthChar < COL_WIDTH_MODERATE)
    {
        minSpacing = 4;
        maxSpacing = 7;
    }
    else
    {
        minSpacing = 4;
        maxSpacing = 9;
    }
    // Todo:最终的列宽间距计算得再考虑超出一屏的规则
    double spacingWithChar = maxSpacing;
    return spacingWithChar;
}

void AtomicCells::BuildCells(Range *pCell, ZoneType zoneType)
{
    // 合并单元格要更新range
    KCOMPTR(Range) spMerge;
    HRESULT hr = pCell->get_MergeArea(&spMerge);
    if (SUCCEEDED(hr) && spMerge)
    {
        long nCellCnt = 0;
        spMerge->get_Count(&nCellCnt);
        if (nCellCnt > 1)
        {
            AtomicRange mergeRange(spMerge);
            *(AtomicRange *)this = mergeRange;
        }
    }

    m_zoneType = zoneType;
    if (Info == m_zoneType)
    {
        // TableInfo类型保持原来的对齐方式
        pCell->get_HorizontalAlignment(&m_hAlign);
        pCell->get_VerticalAlignment(&m_VAlign);
    }

    KComVariant varCell;
    hr = pCell->get_Value(etRangeValueDefault, &varCell);
    if (FAILED(hr) || VarIsEmpty(varCell))
        return;

    VARIANT_BOOL hasFormula;
    pCell->get_HasFormula(&hasFormula);
    if (hasFormula)
    {
        m_bHasFormula = true;
        ks_bstr bsFormula;
        pCell->get_Formula(&bsFormula);
        ks_wstring strFormula = bsFormula.c_str();
        m_bImgFmlaCell = strFormula.find(CELLIMG_FMLA_PATTERN) != ks_wstring::npos;
        if (m_bImgFmlaCell)
            return;
    }

    ks_bstr bstrText;
    hr = pCell->get_Text(&bstrText); //这种方式能取到数值类型日期类型等的显示值
    if (SUCCEEDED(hr) && !bstrText.empty())
        m_strContent = bstrText;
}

void AtomicCells::InitCellsProp(float fontSize, bool bAllowProcessBlank)
{
    m_fFontSize = fontSize;
    m_indentLevel = 0;
    m_bBold = BigTitle == m_zoneType || RowTitle == m_zoneType || SubTitle == m_zoneType;

    initCharHeight();
    if (bAllowProcessBlank && !m_bHasFormula)
        processCellBlank(); //处理空格
    doEstWidth();
}

double AtomicCells::estimatedCellHeight(double dCurWidth, bool bTextOverflow /* = false*/)
{
    double eachLineHeight = getCellCharHeight();

    int sumLineCnt = 0;
    if (dCurWidth < 0)
        return eachLineHeight;
    for (int i = 0; i < m_vecEachParaTextWidth.size(); i++)
    {
        float eachParaTextWidthCharCnt = viewWidth2CharsWidth(m_vecEachParaTextWidth[i], m_pWorkbook);
        // Todo:目前这种算法估算高度会有误差(尤其是在英文放不下的情况)
        int lineCnt = 1;
        if (!bTextOverflow)
        {
            lineCnt = ceil(eachParaTextWidthCharCnt / dCurWidth);
            if (lineCnt < 1)
                lineCnt = 1;
        }
        sumLineCnt += lineCnt;
    }
    //最少也要为一行
    if (sumLineCnt < 1)
        sumLineCnt = 1;
    return eachLineHeight * sumLineCnt;
}

bool AtomicCells::isNeedSpecialApplyProp() const
{
    if (!isWrapText() || isModifiedText() || etHAlignCenter != getHAlign() || etVAlignCenter != getVAlign())
    {
        return true;
    }
    return false;
}

float AtomicCells::getEstWidthWithChar() const
{
    return m_contentWidthWithChar;
}

void AtomicCells::getEachParaTextWidthVec(std::vector<int>& vec) const
{
    vec = m_vecEachParaTextWidth;
}

void AtomicCells::doEstWidth()
{
    int cellWidth = 0; //返回值单位:磅
    int cellCharCnt = 0;
    m_bLineBreak = false;
    m_vecEachParaTextWidth.clear();
    m_emptyLineCnt = 0;

    ks_wstring text = m_strContent;
    //获取该单元格最长的字符数
    if (!text.empty())
    {
        size_t size = text.size();
        int startPos = 0;
        size_t multiLinePos = 0;
        bool bFindManualLineBreak = false;
        int maxCharEachPara = 0; //若存在换行符用户换行的情形下，统计每一行的字符最大数
        do
        {
            multiLinePos = text.find('\n', startPos); //换行符所在的位置
            bFindManualLineBreak = (multiLinePos < size);
            if (bFindManualLineBreak)
                m_bLineBreak = true; //存在手动换行
            int endPos = bFindManualLineBreak ? multiLinePos : size - 1;

            //去获取子串
            int eachLineCharCnt = endPos - startPos;
            eachLineCharCnt = eachLineCharCnt + (bFindManualLineBreak ? 0 : 1);

            if (eachLineCharCnt > maxCharEachPara) //更新该段的最大值，注意这个值是否准确
                maxCharEachPara = eachLineCharCnt;

            ks_wstring eachParaText = text.substr(startPos, eachLineCharCnt); //注意这里endPos是否要加一
            if (eachParaText.empty())
                m_emptyLineCnt++;
            else
            {
                int eachParaTextWidth = estTextWidth(eachParaText);
                if (eachParaTextWidth > 0)
                    m_vecEachParaTextWidth.push_back(eachParaTextWidth);

                if (cellWidth < eachParaTextWidth)
                {
                    cellWidth = eachParaTextWidth;
                }
            }

            startPos = endPos + 1; //更新起始值

        } while (bFindManualLineBreak);

        cellCharCnt = maxCharEachPara;
    }
    m_contentWidthWithChar = viewWidth2CharsWidth(cellWidth, m_pWorkbook);
}

void AtomicCells::processCellBlank()
{
    //仅对表头和内容区进行处理
    if (RowTitle == m_zoneType || Content == m_zoneType)
    {
        int consecutiveSpacesCnt = 0; //连续空格的数目
        QString strResult;            //最终结果
        QString strBlankBuff;         //空格缓存
        size_t textIdx = 0;
        bool bAppearRealText = false; //真正内容前面的连续空格不处理

        ks_wstring text = m_strContent;
        while (textIdx < text.size())
        {
            wchar_t eachChar = text.at(textIdx);
            if (eachChar == ' ')
            {
                consecutiveSpacesCnt++;
                strBlankBuff.append(' ');
            }
            else //非空格
            {
                if (consecutiveSpacesCnt >= 5 && eachChar != '\n' &&
                    bAppearRealText) //达到了五个且最后不为手动回车且前面已出现过非空格的内容字符
                {
                    strResult.append('\n');
                    strBlankBuff.clear();
                    consecutiveSpacesCnt = 0;
                    m_bModifyText = true;
                }

                else if (!strBlankBuff.isEmpty()) //把原本的空格给添加进去
                {
                    strResult.append(strBlankBuff);
                    strBlankBuff.clear();
                    consecutiveSpacesCnt = 0;
                }
                if (!bAppearRealText) //标记出现过非空格
                    bAppearRealText = true;
                strResult.append(eachChar);
            }

            textIdx++;
        }

        if (m_bModifyText)
        {
            //别漏了最后的空格
            if (!strBlankBuff.isEmpty())
            {
                strResult.append(strBlankBuff);
                strBlankBuff.clear();
            }
            m_strContent = krt::utf16(strResult);
        }
    }
}

double AtomicCells::getCellCharHeight() const
{
    return m_dCharHeight;
}

double AtomicCells::getCellEstimatedHeight(double dCurWidth) const
{
    int sumLineCnt = 0;
    if (dCurWidth < 0)
        return m_dCharHeight;
    for (int i = 0; i < m_vecEachParaTextWidth.size(); i++)
    {
        float eachParaTextWidthCharCnt = viewWidth2CharsWidth(m_vecEachParaTextWidth[i], m_pWorkbook);
        // Todo:目前这种算法估算高度会有误差(尤其是在英文放不下的情况)
        int lineCnt = 1;
        if (!m_bTextOverflow)
        {
            lineCnt = ceil(eachParaTextWidthCharCnt / dCurWidth);
            if (lineCnt < 1)
                lineCnt = 1;
        }
        sumLineCnt += lineCnt;
    }
    sumLineCnt += m_emptyLineCnt; //加上回车造成的空行数目
    //最少也要为一行
    if (sumLineCnt < 1)
        sumLineCnt = 1;
    return m_dCharHeight * sumLineCnt;
}

void AtomicCells::setTextOverflow(bool bTextOverflow)
{
    m_bTextOverflow = bTextOverflow;
}

bool AtomicCells::isContainLineBreak() const
{
    return m_bLineBreak;
}

bool AtomicCells::isModifiedText() const
{
    return m_bModifyText;
}

PCWSTR AtomicCells::getModifiedText() const
{
    return m_strContent.c_str();
}

bool AtomicCells::isImgFmlaCell() const
{
    return m_bImgFmlaCell;
}

ZoneType AtomicCells::getZoneType() const
{
    return m_zoneType;
}

void AtomicCells::setWrapText(bool bWrapText)
{
    m_bWrapText = bWrapText;
}

bool AtomicCells::isWrapText() const
{
    return m_bWrapText;
}

void AtomicCells::setHAlign(ETHAlign hAlign)
{
    m_hAlign = hAlign;
}

oldapi::ETHAlign AtomicCells::getHAlign() const
{
    return m_hAlign;
}

void AtomicCells::setVAlign(ETVAlign vAlign)
{
    m_VAlign = vAlign;
}

oldapi::ETVAlign AtomicCells::getVAlign() const
{
    return m_VAlign;
}

float AtomicCells::getFontSize() const
{
    return m_fFontSize;
}

int AtomicCells::getIndentLevel() const
{
    return m_indentLevel;
}

bool AtomicCells::isFontBold() const
{
    return m_bBold;
}

QString AtomicCells::getFontName() const
{
    return m_fontName;
}

void AtomicCells::initCharHeight()
{
    if (m_fFontSize <= 11)
        m_dCharHeight = 16.5;
    else if (m_fFontSize <= 12)
        m_dCharHeight = 17.25;
    else if (m_fFontSize <= 14)
        m_dCharHeight = 20.25;
    else if (m_fFontSize <= 16)
        m_dCharHeight = 22.5;
    else if (m_fFontSize <= 18)
        m_dCharHeight = 24.75;
}

int AtomicCells::estTextWidth(const ks_wstring& text) const
{
    QFont font;
    font.setFamily(m_fontName);

    font.setPixelSize(m_fFontSize);
    QFontMetrics fm(font);

    QString txt(krt::fromUtf16(text.c_str()));
    int width = fm.width(txt);
    return width;
}

double AtomicCol::getColResultWidth() const
{
    return m_dResultWidth;
}

int AtomicCol::getColIdx() const
{
    return m_iCol;
}

void AtomicCol::calcColResultWidth(WholeEffectBase *pWholeEffect)
{
    calcColResultTextWidth();
    if (!pWholeEffect)
        return;
    double dSpacing = pWholeEffect->getColSpacingWithChar(m_dResultWidth);
    m_dResultWidth = m_dResultWidth + dSpacing;
    m_dMaxWidth = pWholeEffect->getColMaxWidthWithChar();
    if (m_dResultWidth > m_dMaxWidth) //对列进行最大值的限制
        m_dResultWidth = m_dMaxWidth;
}

void AtomicCol::setColResultWidth(double dWidth)
{
    if (dWidth > m_dMaxWidth) //对列进行最大值的限制
        dWidth = m_dMaxWidth;
    m_dResultWidth = dWidth;
}

void AtomicCol::setColOriginWidth(double dWidth)
{
    m_dOriginWidth = dWidth;
}

void AtomicCol::setColContentAlignLeft()
{
    m_hAlign = etHAlignLeft;
}

void AtomicCol::calcColResultTextWidth()
{
    //内容区域
    m_bNeedAlignLeft = false;
    float contentMaxCharCnt = 0;
    float tableHeadMaxWidthMaxCharCnt = 0;
    float infoMaxCharCnt = 0;

    for (size_t i = 0; i < vecCells.size(); i++)
    {
        AtomicCells *pEachAtomicCells = vecCells.at(i);
        if (!pEachAtomicCells->IsOneCell()) //合并单元格 跳过
            continue;
        ZoneType zoneType = pEachAtomicCells->getZoneType();
        float estCellWidth = pEachAtomicCells->getEstWidthWithChar();
        if (zoneType == Content)
        {
            if (contentMaxCharCnt < estCellWidth)
                contentMaxCharCnt = estCellWidth;
            if (pEachAtomicCells->isContainLineBreak())
                m_bNeedAlignLeft = true;
        }
        else if (zoneType == ZoneType::RowTitle)
        {
            if (tableHeadMaxWidthMaxCharCnt < estCellWidth)
                tableHeadMaxWidthMaxCharCnt = estCellWidth;
        }
        else if (zoneType == ZoneType::Info)
        {
            if (estCellWidth >= m_dOriginWidth * 2)
            {
                pEachAtomicCells->setTextOverflow(true);
                pEachAtomicCells->setWrapText(false);
            }
            else if (infoMaxCharCnt < estCellWidth)
                infoMaxCharCnt = estCellWidth;
        }
        else
        {
            continue;
        }
    }

    //当表头的列宽字符数在20以上，且超过表体内容最大列宽字符数的2倍，表头换行
    //此时整列的列宽字符数为表头列宽字符数的一半（向上取整）
    if (tableHeadMaxWidthMaxCharCnt >= MAX_TABLEHEAD_COL_MAX_CHAR_CNT &&
        tableHeadMaxWidthMaxCharCnt > contentMaxCharCnt * 2)
        tableHeadMaxWidthMaxCharCnt = ceil((float)tableHeadMaxWidthMaxCharCnt / 2);

    //计算最终的字符数
    float newColCharCnt = std::max(contentMaxCharCnt, tableHeadMaxWidthMaxCharCnt);
    newColCharCnt = std::max(newColCharCnt, infoMaxCharCnt);

    if (newColCharCnt > 50)
    {
        newColCharCnt = 50;
        m_bNeedAlignLeft = true;
    }

    if (m_bNeedAlignLeft && newColCharCnt > COL_ALIGN_LEFT_MIN_WIDTH)
        setColContentAlignLeft();

    m_dResultWidth = newColCharCnt;
}

void AtomicRow::setRowResultHeight(double dHeight)
{
    m_dResultHeight = dHeight;
}

double AtomicRow::getRowResultHeight() const
{
    return m_dResultHeight;
}

int AtomicRow::getRowIdx() const
{
    return m_iRow;
}

AdaptScreenWholeEffect::AdaptScreenWholeEffect()
{
    m_TitleRangeFontSize = 16;
    m_SubTitleRangeFontSize = 14;
    m_HeadRangeFontSize = 11;
    m_ContentRangeFontSize = 10;
    m_OtherRangeFontSize = 10;
    m_InfoRangeFontSize = 10;

    m_RowHeightSpacing = 0;
    m_ColMaxWidth = 40;

    m_ImgCellColMinWidth = 15;
    m_ImgCellRowMinHeight = 110;
}

double AdaptScreenWholeEffect::getColSpacingWithChar(double colWidthWidthChar) const
{
    return 0; //适应屏幕不需要间距
}

AdaptScreenInfo::AdaptScreenInfo()
{
    m_Type = AdaptScreenNormal;
    m_wViewWithChar = -1;
    m_hViewWithPound = -1;
    m_wTableTotal = -1;
    m_hTableTotal = -1;
    m_wTableAve = -1;
    m_hTableAve = -1;
}

void AdaptScreenInfo::setViewSize(double w, double h)
{
    m_wViewWithChar = w;
    m_hViewWithPound = h;
}

void AdaptScreenInfo::setTableW(double w)
{
    m_wTableTotal = w;
}

void AdaptScreenInfo::setTableH(double h)
{
    m_hTableTotal = h;
}

void AdaptScreenInfo::setTableWidthAve(double ave)
{
    m_wTableAve = ave;
}

void AdaptScreenInfo::setTableHeightAve(double ave)
{
    m_hTableAve = ave;
}

AdaptScreenType AdaptScreenInfo::calcAdaptScreenType()
{
    if (m_wTableTotal < m_wViewWithChar * 0.5)
    {
        if (m_hTableTotal < m_hViewWithPound * 0.5)
            m_Type = AdaptScreenNormal;
        else
        {
            if (m_hTableTotal < m_hViewWithPound)
                m_Type = AdaptScreenRowFirst;
            else
                m_Type = AdaptScreenColFirst;
        }
    }
    else
        m_Type = AdaptScreenColFirst;
    QVector<QString> typeNames = {"Normal", "RowFirst", "ColFirst"};
    QString name = typeNames[m_Type];
    return m_Type;
}

adaptScreenStrategy::adaptScreenStrategy(AtomicTable *pTable, AdaptScreenInfo *pAdaptInfo)
    : m_pAtomicTable(pTable), m_pAdaptInfo(pAdaptInfo)
{
}

void adaptScreenStrategy::updateTableTotalWidth()
{
    //计算数据
    double wTableAdapt = 0;
    for (auto iter = m_pAtomicTable->m_colList.begin(); iter != m_pAtomicTable->m_colList.end(); iter++)
    {
        AtomicCol *pEachAtomicCol = iter->second.get();
        if (!pEachAtomicCol)
            continue;
        double dWidth = pEachAtomicCol->getColResultWidth();
        wTableAdapt += dWidth;
    }
    m_pAdaptInfo->setTableW(wTableAdapt);

    //更新列宽平均值
    double ave = 0;
    int colCnt = m_pAtomicTable->m_colList.size();
    if (colCnt > 0)
    {
        ave = m_pAdaptInfo->getTableW() / colCnt;
        m_pAdaptInfo->setTableWidthAve(ave);
    }
}

void adaptScreenStrategy::updateTableTotalHeight()
{
    //计算数据
    double hTableAdapt = 0;
    for (auto iter = m_pAtomicTable->m_rowList.begin(); iter != m_pAtomicTable->m_rowList.end(); iter++)
    {
        AtomicRow *pEachAtomicRow = iter->second.get();
        if (!pEachAtomicRow)
            continue;
        double dHeight = pEachAtomicRow->getRowResultHeight();
        hTableAdapt += dHeight;
    }
    m_pAdaptInfo->setTableH(hTableAdapt);

    //更新行高平均值
    double ave = 0;
    int rowCnt = m_pAtomicTable->m_rowList.size();
    if (rowCnt > 0)
    {
        ave = m_pAdaptInfo->getTableH() / rowCnt;
        m_pAdaptInfo->setTableHeightAve(ave);
    }
}

void adaptScreenStrategy::printfDebugInfo()
{
    //更新
    updateTableTotalWidth();
    updateTableTotalHeight();
}

double adaptScreenStrategy::adaptColWidth(double wTable, double W)
{
    double wNewTable = 0;
    //优化列宽
    for (auto iter = m_pAtomicTable->m_colList.begin(); iter != m_pAtomicTable->m_colList.end(); iter++)
    {
        AtomicCol *pEachAtomicCol = iter->second.get();
        if (!pEachAtomicCol)
            continue;
        double dWidth = pEachAtomicCol->getColResultWidth();
        double dNewWidth = (dWidth / wTable) * W;
        pEachAtomicCol->setColResultWidth(dNewWidth);
        wNewTable += dNewWidth;
    }
    // Todo:考虑一下要不要在这里更新表格最新
    return wNewTable;
}

double adaptScreenStrategy::adaptRowHeight(double hTable, double H)
{
    double hNewTable = 0;
    //优化行高
    for (auto iter = m_pAtomicTable->m_rowList.begin(); iter != m_pAtomicTable->m_rowList.end(); iter++)
    {
        AtomicRow *pEachAtomicRow = iter->second.get();
        if (!pEachAtomicRow)
            continue;
        double dHeight = pEachAtomicRow->getRowResultHeight();
        double dNewHeight = (dHeight / hTable) * H;
        pEachAtomicRow->setRowResultHeight(dNewHeight);
        hNewTable += dNewHeight;
    }
    // Todo:考虑一下要不要在这里更新表格最新
    return hNewTable;
}

adaptScreenNormalStrategy::adaptScreenNormalStrategy(AtomicTable *pTable, AdaptScreenInfo *pAdaptInfo)
    : adaptScreenStrategy(pTable, pAdaptInfo)
{
}


void adaptScreenNormalStrategy::doAdaptScreen()
{
    adaptColWidth(m_pAdaptInfo->getTableW(), m_pAdaptInfo->getViewW() / 2);
    adaptRowHeight(m_pAdaptInfo->getTableH(), m_pAdaptInfo->getViewH() / 2);
}

adaptScreenColFirstStrategy::adaptScreenColFirstStrategy(AtomicTable *pTable, AdaptScreenInfo *pAdaptInfo)
    : adaptScreenStrategy(pTable, pAdaptInfo)
{
}

void adaptScreenColFirstStrategy::doAdaptScreen()
{
    double wView = m_pAdaptInfo->getViewW(); //单位：字符数
    double hView = m_pAdaptInfo->getViewH(); //单位：磅

    if (wView < m_pAdaptInfo->getTableW())
    {
        //对超过平均列宽的列进行折行处理
        double wAve = m_pAdaptInfo->getWidthAve();
        for (auto iter = m_pAtomicTable->m_colList.begin(); iter != m_pAtomicTable->m_colList.end(); iter++)
        {
            AtomicCol *pEachAtomicCol = iter->second.get();
            if (!pEachAtomicCol)
                continue;
            double dWidth = pEachAtomicCol->getColResultWidth();
            if (dWidth > wAve * 2)
            {
                //对该列进行折行处理
                adaptCellsInCol(pEachAtomicCol, wAve * 2);
            }
        }
        // Todo:之后得更新表格的新高度宽度以及其平均值
        updateTableTotalWidth();
        updateTableTotalHeight();
        if (wView < m_pAdaptInfo->getTableW())
        {
            //行高优化
            adaptRowHeightToViewH();
        }
    }

    // Todo:之后得更新表格的新高度宽度以及其平均值
    updateTableTotalWidth();
    updateTableTotalHeight();

    if (wView >= m_pAdaptInfo->getTableW())
    {
        adaptColWidth(m_pAdaptInfo->getTableW(), wView);
        //行高优化
        adaptRowHeightToViewH();
    }
}

void adaptScreenColFirstStrategy::adaptCellsInCol(AtomicCol *pAtomicCol, double newWidth)
{
    //设置新的列宽
    pAtomicCol->setColResultWidth(newWidth);
    //设置了列宽之后，这一列的行高需要重新调整
    std::vector<AtomicCells *>& vecCells = pAtomicCol->vecCells;
    for (size_t i = 0; i < vecCells.size(); ++i)
    {
        AtomicCells *pEachAtomicCells = vecCells.at(i);
        if (!pEachAtomicCells)
            continue;
        m_pAtomicTable->checkAndExpandMergeCellHeight(pEachAtomicCells, false);
    }
    //对齐方式设置为 水平居左
    pAtomicCol->setColContentAlignLeft();
}

void adaptScreenColFirstStrategy::adaptRowHeightToViewH()
{
    double wView = m_pAdaptInfo->getViewW(); //单位：字符数
    double hView = m_pAdaptInfo->getViewH(); //单位：磅

    double scaleH = hView / m_pAdaptInfo->getTableH();
    if (scaleH > 2) //情况1
        adaptRowHeight(m_pAdaptInfo->getTableH(), hView / 2);
    else if (scaleH >= 1 && scaleH <= 2) //情况2
        adaptRowHeight(m_pAdaptInfo->getTableH(), hView);
    else //情况3
    {
        for (auto iter = m_pAtomicTable->m_rowList.begin(); iter != m_pAtomicTable->m_rowList.end(); iter++)
        {
            AtomicRow *pEachAtomicRow = iter->second.get();
            if (!pEachAtomicRow)
                continue;
            double dHeight = pEachAtomicRow->getRowResultHeight();
            pEachAtomicRow->setRowResultHeight(dHeight + 2);
        }
    }
}

adaptScreenRowFirstStrategy::adaptScreenRowFirstStrategy(AtomicTable *pTable, AdaptScreenInfo *pAdaptInfo)
    : adaptScreenStrategy(pTable, pAdaptInfo)
{
}

void adaptScreenRowFirstStrategy::doAdaptScreen()
{
    double wView = m_pAdaptInfo->getViewW(); //单位：字符数
    double hView = m_pAdaptInfo->getViewH(); //单位：磅

    adaptRowHeight(m_pAdaptInfo->getTableH(), hView);
    //列宽优化
    if (wView / m_pAdaptInfo->getTableW() <= 2)
        adaptColWidth(m_pAdaptInfo->getTableW(), wView);
    else
        adaptColWidth(m_pAdaptInfo->getTableW(), wView / 2);
}

adaptScreenProcessProxy::adaptScreenProcessProxy(AtomicTable *pTable, const ViewSize &viewSz)
{
    init(pTable, viewSz);
}

void adaptScreenProcessProxy::doAdaptScreen()
{
    if (m_spStrategy)
    {
        m_spStrategy->doAdaptScreen();
        m_spStrategy->printfDebugInfo();
    }
}

void adaptScreenProcessProxy::init(AtomicTable *pTable, const ViewSize &viewSz)
{
    double w = 0, h = 0, wAve = 0, hAve = 0;
    pTable->getTableSizeAndAve(w, h, wAve, hAve);
    if (!m_spAdaptInfo)
        m_spAdaptInfo = std::make_unique<AdaptScreenInfo>();

    m_spAdaptInfo->setViewSize(viewSz.m_ViewWidth, viewSz.m_ViewHeight);
    m_spAdaptInfo->setTableW(w);
    m_spAdaptInfo->setTableH(h);
    m_spAdaptInfo->setTableWidthAve(wAve);
    m_spAdaptInfo->setTableHeightAve(hAve);

    AdaptScreenType adaptType = m_spAdaptInfo->calcAdaptScreenType();
    m_spStrategy.reset(nullptr);
    if (adaptType == AdaptScreenNormal)
        m_spStrategy = std::make_unique<adaptScreenNormalStrategy>(pTable, m_spAdaptInfo.get());
    else if (adaptType == AdaptScreenColFirst)
        m_spStrategy = std::make_unique<adaptScreenColFirstStrategy>(pTable, m_spAdaptInfo.get());
    else if (adaptType == AdaptScreenRowFirst)
        m_spStrategy = std::make_unique<adaptScreenRowFirstStrategy>(pTable, m_spAdaptInfo.get());
}
} // namespace etai