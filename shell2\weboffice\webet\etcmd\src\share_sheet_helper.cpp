﻿#include "etstdafx.h"
#include "workbook.h"
#include "share_sheet_helper.h"
#include "appcore/et_appcore_shared_link_sheet.h"
#include "et_revision_context_impl.h"

namespace wo
{
namespace ShareSheetHelper
{

HRESULT ShareSheet(KEtWorkbook* pWorkbook, IEtRevisionContext* pCtx, PCWSTR sharedId, UINT sheetStId)
{
    IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
	IDX iSheet = 0;
	pBook->STSheetToRTSheet(sheetStId, &iSheet);
	IKWorksheet* pWorksheet = pWorkbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWorksheet)
		return E_FAIL;
	ISheetProtection* pSheetProtection = pWorksheet->GetProtection();
	ISharedLinkSheet* pSharedLinkOld = pSheetProtection->GetSharedLink();

	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	if(pSharedLinkOld)
	{
		HRESULT hr = spSharedLinkMgr->Remove(pSharedLinkOld->Id());
		if(FAILED(hr))
			return hr;
	}
	ks_stdptr<ISharedLink> spSharedLink;
	VS(spSharedLinkMgr->CreateSharedLink(sharedId, pCtx->getUser()->userID(), false, SharedLinkType_Sheet,
										&spSharedLink, sheetStId, -1));

	VS(spSharedLinkMgr->Add(spSharedLink));

    return S_OK;
}

HRESULT CancelShareSheet(KEtWorkbook* pWorkbook, UINT sheetStId)
{
    IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
	IDX iSheet = 0;
	pBook->STSheetToRTSheet(sheetStId, &iSheet);

	IKWorksheet* pWorksheet = pWorkbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWorksheet)
		return E_FAIL;
	ISheetProtection* pSheetProtection = pWorksheet->GetProtection();
	ISharedLinkSheet* pSharedLink = pSheetProtection->GetSharedLink();

	if (pSharedLink == nullptr)
		return E_FAIL;

	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	HRESULT hr = spSharedLinkMgr->Remove(pSharedLink->Id());

    return hr;
}

}
}