﻿#include "woetsetting.h"
#include "etconfig.h"
#include <cstdlib>
#include "webbase/logger.h"
#include <random>
#include "util.h"

extern WebProcType gs_procType;
namespace wo {

constexpr int32_t OPEN_ALL_SHEET_SIZE = 5 * 1024 * 1024; // M
constexpr int32_t OPEN_BOOK_COMPILE_FML = 50 * 1000; // w

constexpr int32_t OPEN_SHEET_SIZE = 2 * 1024 * 1024; // M
constexpr int32_t OPEN_SHEET_COMPILE_FML = 10 * 1000; //  w

constexpr int32_t OPEN_BOOK_FAST_DEF = 500; // ms
constexpr int32_t OPEN_MIN_FAST_DEF = 5; // ms
constexpr int32_t OPEN_TIME = 9000;
constexpr int32_t DEF_USER_TIMEOUT = 10 * 60 * 1000 + 30;
constexpr int32_t DEFAULT_DB_FILTER_LOOKUP_RES_CACHE_ENABLE_THRESHOLD = 0;  // 默认关闭，因此将默认阈值定义为0
constexpr int32_t COMMON_LOG_MAX_ENTRY_SIZE = 32 << 20; // 32MB
PCSTR DEFAULT_COMMON_LOG_DIR = "/tmp/common_log";

int32_t getEnvInt(const char * env, int32_t def)
{
    if (char* str = ::getenv(env))
    {
        WOLOG_INFO << "[WoEtSetting] "<< env << ": " << str;
        return ::atoi(str);
    }
    else
    {
        WOLOG_INFO << "[WoEtSetting] "<< env << "(def): " << def;
        return def;
    }
}

double getEnvDbl(const char * env, double def)
{
    if (char* str = ::getenv(env))
    {
        WOLOG_INFO << "[WoEtSetting] "<< env << ": " << str;
        return ::atof(str);
    }
    else
    {
        WOLOG_INFO << "[WoEtSetting] "<< env << "(def): " << def;
        return def;
    }
}

WoEtSetting::WoEtSetting()
    : m_numEllipis(FALSE)
    , m_bSupportCellMultiHyperlink(FALSE)
    , m_bEnableSavingBreakCal(FALSE)
    , m_bEnableAutoSlim(FALSE)
    , m_bEnableAutoSlimIOCollect(TRUE)
    , m_nTimeLimitSaveBreakSecdoc(DEFAULT_TIME_LIMIT_SAVE_BREAK_SECDOC)
    , m_nTimeLimitSaveBreakGeneral(DEFAULT_TIME_LIMIT_SAVE_BREAK_GENERAL)
    , m_nSlaveTimeLimitSaveBreakSecdoc(DEFAULT_TIME_LIMIT_SAVE_BREAK_SECDOC)
    , m_nSlaveTimeLimitSaveBreakGeneral(DEFAULT_TIME_LIMIT_SAVE_BREAK_GENERAL)
    , m_DocSlimBoundDensity(DEFAULT_DOCSLIM_BOUND_DENSITY)
    , m_nDocSlimDocNullCellsLimit(DEFAULT_DOCSLIM_NULL_CELLS_LIMIT)
    , m_nDocSlimDocNullCellsTriggerValue(DEFAULT_DOCSLIM_NULL_CELLS_TRIGGER_VALUE)
    , m_nDocSlimDocInvisibleObjectTriggerValue(DEFAULT_DOCSLIM_INVISIBLE_OBJECT_TRIGGER_VALUE)
    , m_nDocSlimDocUnreferencedPictureTriggerValue(DEFAULT_DOCSLIM_UNREFERENCED_PICTURE_TRIGGER_VALUE)
    , m_nDocSlimOverlapShapesTriggerValue(DEFAULT_DOCSLIM_OVERLAP_SHAPES_TRIGGER_VALUE)
    , m_nDocSlimUnusedDuplicateStyleTriggerValue(DEFAULT_DOCSLIM_UNUSED_DUPLICATE_STYLE_TRIGGER_VALUE)
    , m_nDocSlimDuplicateFormatConditionTriggerValue(DEFAULT_DOCSLIM_DUPLICATE_FORMAT_CONDITION_TRIGGER_VALUE)
    , m_nHtmlCopyMaxLimitSize(DEFAULT_HTML_COPY_LIMIT_SIZE)
    , m_nDocSlimRowLowDensityToleranceIndex(DEFAULT_DOCSLIM_ROW_LOW_DENSITY_TOLERANCE_INDEX)
    , m_nDocSlimColLowDensityToleranceIndex(DEFAULT_DOCSLIM_COL_LOW_DENSITY_TOLERANCE_INDEX)
    , m_nDocSlimBoundAdvance(DEFAULT_DOCSLIM_BOUND_ADVANCE)
    , m_copySpreadsheet6FormatMaxSize(-1)
    , m_bEnableSubscriptionNotifyToUnoTag(-1)
    , m_CommentRecordAddCmtJudgeTransact(-1)
    , m_IsOpenSetFileTagProtected(-1)
    , m_bEnableSecBreakIo(-1)
    , m_openThreadMode(WoIoThreadMode::kInvalid)
    , m_atuoOpenThreadMode(WoIoThreadMode::kInvalid)
    , m_openTime(INVALID_VAL)
    , m_openBookCompileFml(INVALID_VAL)
    , m_openSheetCompileFml(INVALID_VAL)
    , m_openAllSheetsSize(INVALID_VAL)
    , m_openSheetSize(INVALID_VAL)
    , m_openBookFastThreshold(INVALID_VAL)
    , m_openMultiBySheet(INVALID_VAL)
    , m_openMinFastThreshold(INVALID_VAL)
    , m_collectTimeThreshold(INVALID_VAL)
    , m_bInitSeed(FALSE)
    , m_randSeed(0)
    , m_cfCalcTimeProbability(0)
    , m_cfCollectFmlaProbability(0)
    , m_collectProbability(0)
    , m_nIsEnableStaleNotify(INVALID_VAL)
    , m_isSendCoreTime(INVALID_VAL)
    , m_userConnTimeout(INVALID_VAL)
    , m_clearUserConnCount(INVALID_VAL)
    , m_maxClearUseConnCount(INVALID_VAL)
    , m_maxClearUserConnTime(INVALID_VAL)
	, m_nCustomListCacheMaxSizePerSheet(DEFAULT_CUSTOM_LIST_CACHE_MAX_SIZE_PER_SHEET)
    , m_dbFunnelChartMaxLayerCount(0)
    , m_dbDashboardPluginMax(0)
    , m_dbDashboardMaxFilterCount(0)
    , m_dbChartMaxConnCacheCount(INVALID_VAL)
    , m_bEnableReleaseConn(INVALID_VAL)
    , m_delayUpdateDbChart(INVALID_VAL)
    , m_bEnableStatictisSheet(TRUE)
    , m_nEnableGridSheetSyncSheet(INVALID_VAL)
    , m_nEnableEventTrackingCollect(INVALID_VAL)
    , m_nEnableDBViewUpdateCollect(INVALID_VAL)
    , m_collectDiffMemTime(INVALID_VAL)
    , m_nDbFilterTokenResCacheEnableThreshold(INVALID_VAL)
    , m_coreMetricsProbability(0)
    , m_coreMetricsCollectThreshold(INVALID_VAL)
    , m_cmSignalThreshold(INVALID_VAL)
    , m_isBlockNonNeedRdView(INVALID_VAL)
    , m_enableCFNotify(INVALID_VAL)
    , m_pasteProgressNotifyDuration(INVALID_VAL)
    , m_isEnableMetricSignal(INVALID_VAL)
    , m_minMetricSignalSz(INVALID_VAL)
    , m_maxMetricSignalSz(INVALID_VAL)
    , m_maxMetricSignalCnt(INVALID_VAL)
{
    m_cellHistorySetting.bEnable = false; // 默认关闭
    m_cellHistorySetting.nMaxPurgeTag = ksheet_config::cell_history::kCellHistoryMaxPurgeTag;
    m_cellHistorySetting.nReserveCount = ksheet_config::cell_history::kCellHistoryReserve;
    m_cellHistorySetting.nExpireSeconds = ksheet_config::cell_history::kCellHistoryExpireSeconds;
}

STDIMP_(BOOL) WoEtSetting::getGeneralNumEllipOn()
{
    return m_numEllipis;
}

STDIMP_(void) WoEtSetting::setGeneralNumEllipOn(BOOL b)
{
    m_numEllipis = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableSavingBreakCal()
{
    return m_bEnableSavingBreakCal;
}

STDIMP_(void) WoEtSetting::setEnableSavingBreakCal(BOOL b)
{
    m_bEnableSavingBreakCal = b;
}

void WoEtSetting::SetSlaveTimeLimitSaveBreakSecdoc(unsigned int timeLimit)
{
    m_nSlaveTimeLimitSaveBreakSecdoc = timeLimit;
}

void WoEtSetting::SetSlaveTimeLimitSaveBreakGeneral(unsigned int timeLimit)
{
    m_nSlaveTimeLimitSaveBreakGeneral = timeLimit;
}

STDIMP_(void) WoEtSetting::SetTimeLimitSaveBreakSecdoc(unsigned int timeLimit)
{
    m_nTimeLimitSaveBreakSecdoc = timeLimit;
}

STDIMP_(unsigned int) WoEtSetting::GetTimeLimitSaveBreakSecdoc()
{
    return gs_procType == WebProcTypeMaster ? m_nTimeLimitSaveBreakSecdoc : m_nSlaveTimeLimitSaveBreakSecdoc;
}

STDIMP_(void) WoEtSetting::SetTimeLimitSaveBreakGeneral(unsigned int timeLimit)
{
    m_nTimeLimitSaveBreakGeneral = timeLimit;
}

STDIMP_(unsigned int) WoEtSetting::GetTimeLimitSaveBreakGeneral()
{
    return gs_procType == WebProcTypeMaster ? m_nTimeLimitSaveBreakGeneral : m_nSlaveTimeLimitSaveBreakGeneral;
}

STDIMP_(INT) WoEtSetting::GetCellHistoryMaxPurgeTag()
{
    return m_cellHistorySetting.nMaxPurgeTag;
}

STDIMP_(INT) WoEtSetting::GetCellHistoryReserveCount()
{
    return m_cellHistorySetting.nReserveCount;
}

STDIMP_(INT) WoEtSetting::GetCellHistoryExpireSecs()
{
    return m_cellHistorySetting.nExpireSeconds;
}

void WoEtSetting::SetCellHistoryMaxPurgeTag(INT n)
{
    m_cellHistorySetting.nMaxPurgeTag = n;
}

void WoEtSetting::SetCellHistoryReserveCount(INT n)
{
    m_cellHistorySetting.nReserveCount = n;
}

void WoEtSetting::SetCellHistoryExpireSecs(INT ExpireSeconds, bool addTimeBias)
{
    m_cellHistorySetting.nExpireSeconds = ExpireSeconds;
    if (addTimeBias)
    {
        m_cellHistorySetting.nExpireSeconds += ksheet_config::cell_history::kTimeBias;
    }
}

STDIMP_(BOOL) WoEtSetting::IsCommentRecordAddCmtJudgeTransact()
{
    if (m_CommentRecordAddCmtJudgeTransact < 0)
    {
        if (char* str = ::getenv("ET_COMMENT_RECORDADDCMT_JUDGE_TRANSACT"))
        {
            int enable = ::atoi(str);
            if (enable != 0)
            {
                m_CommentRecordAddCmtJudgeTransact = 1;
            }
            else 
            {
                m_CommentRecordAddCmtJudgeTransact = 2;
            }
        } 
        // 没有环境变量 默认打开
        if (m_CommentRecordAddCmtJudgeTransact < 0)
        {
            m_CommentRecordAddCmtJudgeTransact  = 2;
        }

        WOLOG_INFO << "m_CommentRecordAddCmtJudgeTransact: " << (m_CommentRecordAddCmtJudgeTransact != 2);

    }

    // 不等于2 其他都需要判断
    return m_CommentRecordAddCmtJudgeTransact != 2;

}


STDIMP_(BOOL) WoEtSetting::IsEnableSubscriptionNotifyToUnoTag()
{
    if (m_bEnableSubscriptionNotifyToUnoTag < 0)
    {
        if (char* str = ::getenv("ET_SUBSCRIPTION_NOTIFY_TO_UNOTAG"))
        {
            int enable = ::atoi(str);
            if (enable != 0)
            {
                m_bEnableSubscriptionNotifyToUnoTag = 1;
            }
            else 
            {
                m_bEnableSubscriptionNotifyToUnoTag = 0;
            }
        } 
        // 没有环境变量 默认打开
        if (m_bEnableSubscriptionNotifyToUnoTag < 0)
        {
            m_bEnableSubscriptionNotifyToUnoTag  = 1;
        }
    }
    return m_bEnableSubscriptionNotifyToUnoTag == 1;
}


STDIMP_(INT) WoEtSetting::GetCopySpreadsheet6FormatMaxSize()
{
    if (m_copySpreadsheet6FormatMaxSize <= 0) 
    {
        if (char* str = ::getenv("ET_COPY_SPREADSHEET6_FORMAT_MAX_SIZE"))
        {
            int size = ::atoi(str);
            if (size >= 1 && size <= 300)
            {
                m_copySpreadsheet6FormatMaxSize = size;
            } 
        }
        if (m_copySpreadsheet6FormatMaxSize <= 0) 
        {
            m_copySpreadsheet6FormatMaxSize = 300;
        }
    }
    return m_copySpreadsheet6FormatMaxSize;
}


STDIMP_(BOOL) WoEtSetting::IsEnablingCellHistoryStore()
{
#ifdef WEB_OFFICE_DISABLE_CELL_EXTRA
    return FALSE;
#else
    return m_cellHistorySetting.bEnable;
#endif
}

void WoEtSetting::EnableCellHistoryStore(bool enable)
{
    m_cellHistorySetting.bEnable = enable;
}
STDIMP_(BOOL) WoEtSetting::getSupportCellMultiLink()
{
    return m_bSupportCellMultiHyperlink;
}

STDIMP_(void) WoEtSetting::setSupportCellMultiLink(BOOL b)
{
    m_bSupportCellMultiHyperlink = b;
}

STDIMP_(BOOL) WoEtSetting::getSupportDynamicCalc()
{
    return m_bSupportDynamicCalc;
}
 
STDIMP_(void) WoEtSetting::setSupportDynamicCalc(BOOL b)
{
    m_bSupportDynamicCalc = b;
}

STDIMP_(LPCWSTR) WoEtSetting::GetFileId()
{
    return m_fileId.c_str();
}

void WoEtSetting::SetFileId(LPCWSTR fileid)
{
    m_fileId = fileid;
}

STDIMP_(bool) WoEtSetting::IsEnableNewUpdateRender()
{
    return m_bEnableNewUpdateRender;
}

void WoEtSetting::SetEnableNewUpdateRender(bool b)
{
    m_bEnableNewUpdateRender = b;
}

void WoEtSetting::SetEnableCFApplyWhenCopy(BOOL bApplyCF)
{
    m_bEnableCFApplyWhenCopy = bApplyCF;
}

BOOL WoEtSetting::IsEnableCFApplyWhenCopy()
{
    return m_bEnableCFApplyWhenCopy;
}

STDIMP_(void) WoEtSetting::SetUDFSingleRequestLimit(INT32 limitSize)
{
    m_udfSingleRequestLimit = limitSize;
}

STDIMP_(INT32) WoEtSetting::GetUDFSingleRequestLimit() const
{
    return m_udfSingleRequestLimit;
}

void WoEtSetting::SetEnableNewBatchCopy(BOOL bEnable)
{
    m_bEnableNewBatchCopy = bEnable;
}

BOOL WoEtSetting::IsEnableNewBatchCopy()
{
    return m_bEnableNewBatchCopy;
}
STDIMP_(BOOL) WoEtSetting::IsOpenSetFileTagProtected()
{
    if (m_IsOpenSetFileTagProtected < 0) 
    {
        if (char* str = ::getenv("ET_ENABLE_SETFILET_AGPROTECTED"))
        {
            m_IsOpenSetFileTagProtected = ::atoi(str);
        }
        if (m_IsOpenSetFileTagProtected < 0)
        {
            m_IsOpenSetFileTagProtected = 0;
        }
    }
    return m_IsOpenSetFileTagProtected > 0;
}

BOOL WoEtSetting::IsSecEnableSaveBreak()
{
    if (m_bEnableSecBreakIo < 0) 
    {
        if (char* str = ::getenv("ET_DISABLE_SEC_BREAKIO"))
        {
            WOLOG_INFO << "[GetWoEtSettings] ET_DISABLE_SEC_BREAKIO: " << str;
            int v = ::atoi(str);
            m_bEnableSecBreakIo = v != 0 ? 0 : 1;
        }
        else
        {
            m_bEnableSecBreakIo = 1;
        }
    }
    return m_bEnableSecBreakIo != 0;
}
STDIMP_(WoIoThreadMode) WoEtSetting::GetOpenIoThreadMode()
{
    if (WoIoThreadMode::kInvalid == m_openThreadMode) 
    {
        int32_t v = getEnvInt("ET_OPEN_THREAD_MODE", (uint32_t)WoIoThreadMode::kAuto);
        if (v <= 0)
            m_openThreadMode = WoIoThreadMode::kAuto;
        else if (v == 1)
            m_openThreadMode = WoIoThreadMode::kMainThread;
        else if (v == 2)
            m_openThreadMode = WoIoThreadMode::kOneThread;
        else
            m_openThreadMode = WoIoThreadMode::kTwoThread;
    }
    return m_openThreadMode;
}
STDIMP_(WoIoThreadMode) WoEtSetting::GetAutoOpenIoThreadMode()
{
    if (WoIoThreadMode::kInvalid == m_atuoOpenThreadMode) 
    {
        int32_t v = getEnvInt("ET_AUTO_OPEN_THREAD_MODE", (uint32_t)WoIoThreadMode::kOneThread);
        if (v == 1)
            m_atuoOpenThreadMode = WoIoThreadMode::kMainThread;
        else if (v == 2)
            m_atuoOpenThreadMode = WoIoThreadMode::kOneThread;
        else
            m_atuoOpenThreadMode = WoIoThreadMode::kTwoThread;
    }
    return m_atuoOpenThreadMode;
}
STDIMP_(int32_t) WoEtSetting::GetOpenTimeThreshold()
{
    if (INVALID_VAL == m_openTime)
    {
        int32_t v = getEnvInt("ET_OPEN_TIME_THRESHOLD", OPEN_TIME);
        m_openTime = v <= 0 ? 0 : v;
    }
    return m_openTime;
}
STDIMP_(int32_t) WoEtSetting::GetOpenBookCompileFmlThreshold()
{
    if (INVALID_VAL == m_openBookCompileFml)
    {
        int32_t v = getEnvInt("ET_OPEN_BOOK_COMPILE_FML", OPEN_BOOK_COMPILE_FML);
        m_openBookCompileFml = v <= 0 ? 0 : v;
    }
    return m_openBookCompileFml;
}
STDIMP_(int32_t) WoEtSetting::GetOpenSheetCompileFmlThreshold()
{
    if (INVALID_VAL == m_openSheetCompileFml)
    {
        int32_t v = getEnvInt("ET_OPEN_SHEET_COMPILE_FML", OPEN_SHEET_COMPILE_FML);
        m_openSheetCompileFml = v <= 0 ? 0 : v;
    }
    return m_openSheetCompileFml;
}
STDIMP_(int32_t) WoEtSetting::GetOpenAllSheetsSize()
{
    if (INVALID_VAL == m_openAllSheetsSize)
    {
        int32_t v = getEnvInt("ET_OPEN_ALL_SHEET_SIZE", OPEN_ALL_SHEET_SIZE);
        m_openAllSheetsSize = v <= 0 ? 0 : v;
    }
    return m_openAllSheetsSize;
}
STDIMP_(int32_t) WoEtSetting::GetOpenSheetSize()
{
    if (INVALID_VAL == m_openSheetSize)
    {
        int32_t v = getEnvInt("ET_OPEN_SHEET_SIZE", OPEN_SHEET_SIZE);
        m_openSheetSize = v <= 0 ? 0 : v;
    }
    return m_openSheetSize;
}
STDIMP_(bool) WoEtSetting::IsOpenMultiThreadBySheet()
{
     if (INVALID_VAL == m_openMultiBySheet)
    {
        int32_t v = getEnvInt("ET_OPEN_MULTI_BY_SHEET", 1);
        m_openMultiBySheet = v <= 0 ? 0 : v;
    }
    return m_openMultiBySheet != 0;
}
STDIMP_(int) WoEtSetting::GetOpenBookFastThreshold()
{
     if (INVALID_VAL == m_openBookFastThreshold)
    {
        int32_t v = getEnvInt("ET_OPEN_BOOK_FAST_DEF", OPEN_BOOK_FAST_DEF);
        m_openBookFastThreshold = v <= 0 ? 0 : v;
    }
    return m_openBookFastThreshold;
}
STDIMP_(int) WoEtSetting::GetOpenMinFastThreshold()
{
    if (INVALID_VAL == m_openMinFastThreshold)
    {
        int32_t v = getEnvInt("ET_OPEN_MIN_FAST_DEF", OPEN_MIN_FAST_DEF);
        m_openMinFastThreshold = v <= 0 ? 0 : v;
    }
    return m_openMinFastThreshold;
}

int WoEtSetting::GetCollectTimeThreshold()
{
    if (INVALID_VAL == m_collectTimeThreshold)
    {
        const int vDef = wo::util::kDefCmdTimeThreshold;
        int32_t v = getEnvInt("ET_CMD_TIME_THRESHOLD", vDef);
        m_collectTimeThreshold = v;
        WOLOG_INFO << "[WoEtSetting] m_collectTimeThreshold"<< m_collectTimeThreshold;
    }
    return m_collectTimeThreshold;
}

unsigned int WoEtSetting::GetRandSeed()
{
    if (!m_bInitSeed)
    {
        m_bInitSeed = TRUE;
        std::random_device rd;
        m_randSeed = rd();
        WOLOG_INFO << "[WoEtSetting] rand seed " << m_randSeed;
    }
    return m_randSeed;
}

double WoEtSetting::GetCollectProbability()
{
    if (0 == m_collectProbability)
    {
        const double vDef = 0.01;
        double v = getEnvDbl("ET_CMD_TIME_PROB", vDef);
        m_collectProbability = v <= 0 ? 0 : (v >= 1.0 ? 1.0 : v);
         WOLOG_INFO << "[WoEtSetting] m_collectProbability"<< m_collectProbability;
    }
    return m_collectProbability;
}

double WoEtSetting::GetCollectFormulaComplexityProbability()
{
    if (0 == m_collectFormulaComplexityProbability)
    {
        const double vDef = 0.001;
        double v = getEnvDbl("ET_FORMULA_COMPLEXITY_PROB", vDef);
        m_collectFormulaComplexityProbability = v <= 0 ? vDef : (v > 1.0 ? vDef : v);
        WOLOG_INFO << "[WoEtSetting] collectFormulaComplexityProbability: "<< m_collectFormulaComplexityProbability;
    }
    return m_collectFormulaComplexityProbability;
}

double WoEtSetting::GetCFCollectFmlaProbability()
{
    if (fabs(m_cfCollectFmlaProbability) < DBL_EPSILON)
    {
        const double vDef = 0.001;
        double v = getEnvDbl("ET_CF_COLLECT_FMLA_PROB", vDef);
        m_cfCollectFmlaProbability = v <= 0 ? vDef : (v >= 1.0 ? vDef : v);
         WOLOG_INFO << "[WoEtSetting] m_cfCollectFmlaProbability: "<< m_cfCollectFmlaProbability;
    }
    return m_cfCollectFmlaProbability;
}

double WoEtSetting::GetCFCalcTimeProbability()
{
    if (fabs(m_cfCalcTimeProbability) < DBL_EPSILON)
    {
        const double vDef = 0.0005;
        double v = getEnvDbl("ET_CF_CALC_TIME_PROB", vDef);
        m_cfCalcTimeProbability = v <= 0 ? vDef : (v >= 1.0 ? vDef : v);
         WOLOG_INFO << "[WoEtSetting] m_cfCalcTimeProbability: "<< m_cfCalcTimeProbability;
    }
    return m_cfCalcTimeProbability;
}


util::RandDist * WoEtSetting::GetCmdTimeRand()
{
    if (!m_spCmdTimeRand)
    {
        m_spCmdTimeRand.reset(new util::RandDist(GetRandSeed(), GetCollectProbability()));
    }
    return m_spCmdTimeRand.get();
}

util::RandDist * WoEtSetting::GetQueryInitTimeRand()
{
    if (!m_spQueryInitRand)
    {
        m_spQueryInitRand.reset(new util::RandDist(GetRandSeed(), GetCollectProbability()));
    }
    return m_spQueryInitRand.get();
}

util::RandDist * WoEtSetting::GetApiTimeRand()
{
    if (!m_spApiRand)
    {
        m_spApiRand.reset(new util::RandDist(GetRandSeed(), GetCollectProbability()));
    }
    return m_spApiRand.get();
}

double WoEtSetting::GetCoreMetricsProbability()
{
    if (0 == m_coreMetricsProbability)
    {
        const double vDef = 0.001;
        double v = getEnvDbl("ET_CORE_METRICS_PROB", vDef);
        m_coreMetricsProbability = v <= 0 ? 0 : (v >= 1.0 ? 1.0 : v);
         WOLOG_INFO << "[WoEtSetting] m_coreMetricsProbability"<< m_coreMetricsProbability;
    }
    return m_coreMetricsProbability;
}

int WoEtSetting::GetCoreMetricsCollectThreshold()
{
    if (INVALID_VAL == m_coreMetricsCollectThreshold)
    {
        constexpr int vDef = 500;
        int32_t v = getEnvInt("ET_CORE_METRICS_THRESHOLD", vDef);
        m_coreMetricsCollectThreshold = v;
    }
    return m_coreMetricsCollectThreshold;
}

int WoEtSetting::GetCMSignalThreshold()
{
    if (INVALID_VAL == m_cmSignalThreshold)
    {
        constexpr int vDef = 10;
        int32_t v = getEnvInt("ET_CORE_METRICS_CLT_SIGNAL_MB", vDef);
        m_cmSignalThreshold = v;
    }
    return m_cmSignalThreshold;
}

util::RandDist * WoEtSetting::GetCoreMetricsRand()
{
    if (!m_spCoreMetricsRand)
    {
        m_spCoreMetricsRand.reset(new util::RandDist(GetRandSeed(), GetCoreMetricsProbability()));
    }
    return m_spCoreMetricsRand.get();
}

util::RandDist * WoEtSetting::GetCFQueryTimeRand()
{
    if (!m_spCFQueryTimeRand)
    {
        m_spCFQueryTimeRand.reset(new util::RandDist(GetRandSeed(), GetCollectProbability()));
    }
    return m_spCFQueryTimeRand.get();
}

util::RandDist * WoEtSetting::GetFormulaComplexityRand()
{
    if (!m_spFormulaComplexityRand)
    {
        m_spFormulaComplexityRand.reset(new util::RandDist(GetRandSeed(), GetCollectFormulaComplexityProbability()));
    }
    return m_spFormulaComplexityRand.get();
}

util::RandDist * WoEtSetting::GetCFCollectFmlaRand()
{
    if (!m_spCFCollectFmlaRand)
    {
        m_spCFCollectFmlaRand.reset(new util::RandDist(GetRandSeed(), GetCFCollectFmlaProbability()));
    }
    return m_spCFCollectFmlaRand.get();
}

util::RandDist * WoEtSetting::GetCFCalcTimeRand()
{
    if (!m_spCFCalcTimeRand)
    {
        m_spCFCalcTimeRand.reset(new util::RandDist(GetRandSeed(), GetCFCalcTimeProbability()));
    }
    return m_spCFCalcTimeRand.get();
}

STDIMP_(util::RandDist *) WoEtSetting::GetPasteRand()
{
    if (!m_spPasteRand)
    {
        m_spPasteRand.reset(new util::RandDist(GetRandSeed(), GetPasteProbability()));
    }
    return m_spPasteRand.get();
}

STDIMP_(wo::util::RandDist *) WoEtSetting::GetCollectTimeRand(CollectTimeRandType type)
{
    static_assert(CTRT_DBViewUpdate + 1 == CTRT_MAX, "should modify here");
    switch (type)
    {
        case CTRT_CFCollectFmla:
            return GetCFCollectFmlaRand();
        case CTRT_CFCalc:
            return GetCFCalcTimeRand();
        case CTRT_DBViewUpdate:
            return getDBViewUpdateTimeRand();
        default:
            ASSERT(false);
            break;
    }

    return nullptr;
}

STDIMP_(bool) WoEtSetting::isCollect(int ms, int * pSampleRate, util::RandDist *rand, const int * pThreshold/* = nullptr*/)
{
    int rate = 0;
    bool res = false;
    int threshold = GetCollectTimeThreshold();
    if (pThreshold)
    {
        threshold = *pThreshold;
    }

    if (ms > threshold)
    {
        rate = 1;
        res = true;
    } 
    else if (rand && rand->is())
    {
        rate = (int) (1.0 / rand->GetProbability() + 0.5);
        res = true;
    }
    if (pSampleRate) *pSampleRate = rate;
    return res;
}

STDIMP_(int) WoEtSetting::GetEnvInt(const char * env, const int def)
{
    return getEnvInt(env, def);
}

bool WoEtSetting::isEnableStaleNotify()
{
    if (INVALID_VAL == m_nIsEnableStaleNotify)
    {
        int32_t v = getEnvInt("ET_ENABLE_OBJ_STALE", 1);
        m_nIsEnableStaleNotify = v != 0;
    }
    return m_nIsEnableStaleNotify;
}

STDIMP_(DOUBLE) WoEtSetting::GetDocSlimBoundDensity()
{
    return m_DocSlimBoundDensity;
}

void WoEtSetting::SetDocSlimBoundDensity(DOUBLE density)
{
    m_DocSlimBoundDensity = density;
}

STDIMP_(UINT64) WoEtSetting::GetDocSlimNullCellsLimit()
{
    return m_nDocSlimDocNullCellsLimit;
}

void WoEtSetting::SetDocSlimNullCellsLimit(UINT64 limit)
{
    m_nDocSlimDocNullCellsLimit = limit;
}

STDIMP_(UINT32) WoEtSetting::GetDocSlimNullCellsTriggerValue()
{
    return m_nDocSlimDocNullCellsTriggerValue;
}

void WoEtSetting::SetDocSlimNullCellsTriggerValue(UINT32 limit)
{
    m_nDocSlimDocNullCellsTriggerValue = limit;
}

STDIMP_(UINT32) WoEtSetting::GetDocSlimInvisibleObjectTriggerValue()
{
    return m_nDocSlimDocInvisibleObjectTriggerValue;
}

void WoEtSetting::SetDocSlimInvisibleObjectTriggerValue(UINT32 limit)
{
    m_nDocSlimDocInvisibleObjectTriggerValue = limit;
}

STDIMP_(UINT32) WoEtSetting::GetDocSlimUnreferencedPictureTriggerValue()
{
    return m_nDocSlimDocUnreferencedPictureTriggerValue;
}

void WoEtSetting::SetDocSlimUnreferencedPictureTriggerValue(UINT32 limit)
{
    m_nDocSlimDocUnreferencedPictureTriggerValue = limit;

}

STDIMP_(UINT32) WoEtSetting::GetDocSlimOverlapShapesTriggerValue()
{
    return m_nDocSlimOverlapShapesTriggerValue;
}

void WoEtSetting::SetDocSlimOverlapShapesTriggerValue(UINT32 limit)
{
    m_nDocSlimOverlapShapesTriggerValue = limit;
}

STDIMP_(UINT32) WoEtSetting::GetDocSlimUnusedDuplicateStyleTriggerValue()
{
    return m_nDocSlimUnusedDuplicateStyleTriggerValue;
}

void WoEtSetting::SetDocSlimUnusedDuplicateStyleTriggerValue(UINT32 limit)
{
    m_nDocSlimUnusedDuplicateStyleTriggerValue = limit;
}

STDIMP_(UINT32) WoEtSetting::GetDocSlimDuplicateFormatConditionTriggerValue()
{
    return m_nDocSlimDuplicateFormatConditionTriggerValue;
}

void WoEtSetting::SetDocSlimDuplicateFormatConditionTriggerValue(UINT32 limit)
{
    m_nDocSlimDuplicateFormatConditionTriggerValue = limit;
}

void WoEtSetting::SetHtmlCopyMaxLimitSize(UINT32 limit)
{
    m_nHtmlCopyMaxLimitSize = limit;
}

STDIMP_(UINT32) WoEtSetting::GetHtmlCopyMaxLimitSize()
{
    return m_nHtmlCopyMaxLimitSize;
}

STDIMP_(UINT16) WoEtSetting::GetDocSlimRowLowDensityToleranceIndex()
{
    return m_nDocSlimRowLowDensityToleranceIndex;
}

void WoEtSetting::SetDocSlimRowLowDensityToleranceIndex(UINT16 index)
{
    m_nDocSlimRowLowDensityToleranceIndex = index;
}

STDIMP_(UINT16) WoEtSetting::GetDocSlimColLowDensityToleranceIndex()
{
    return m_nDocSlimColLowDensityToleranceIndex;
}

void WoEtSetting::SetDocSlimColLowDensityToleranceIndex(UINT16 index)
{
    m_nDocSlimColLowDensityToleranceIndex = index;
}

STDIMP_(UINT16) WoEtSetting::GetDocSlimBoundAdvance()
{
    return m_nDocSlimBoundAdvance;
}

void WoEtSetting::SetDocSlimBoundAdvance(UINT16 advance)
{
    m_nDocSlimBoundAdvance = advance;
}

STDIMP_(BOOL) WoEtSetting::IsEnableAutoSlim()
{
    return m_bEnableAutoSlim;
}

STDIMP_(void) WoEtSetting::SetEnableAutoSlim(BOOL b)
{
    m_bEnableAutoSlim = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnablePivotItemMove()
{
    return m_bEnablePivotItemMove;
}

STDIMP_(void) WoEtSetting::SetEnablePivotItemMove(BOOL b)
{
    m_bEnablePivotItemMove = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableFindThousandsSeparators()
{
    return m_bEnableFindThousandsSeparators;
}

STDIMP_(void) WoEtSetting::SetEnableFindThousandsSeparators(BOOL b)
{
    m_bEnableFindThousandsSeparators = b;
}

STDIMP_(BOOL) WoEtSetting::GetDefaultFilterShared()
{
    return m_bDefaultFilterShared;
}

STDIMP_(void) WoEtSetting::SetDefaultFilterShared(BOOL b)
{
    m_bDefaultFilterShared = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableParentRecord()
{
    return m_bEnableParentRecord;
}

STDIMP_(void) WoEtSetting::SetEnableParentRecord(BOOL bEnable)
{
    m_bEnableParentRecord = bEnable;
}

STDIMP_(UINT8) WoEtSetting::GetMaxParentRecordLevel()
{
    return m_maxParentRecordLevel;
}

void WoEtSetting::SetMaxParentRecordLevel(UINT8 level)
{
    m_maxParentRecordLevel = level;
}

STDIMP_(void) WoEtSetting::SetEnableClearCurVersionCmd(BOOL bEnable)
{
    m_bEnableClearCurVersionCmd = bEnable;
}

STDIMP_(BOOL) WoEtSetting::IsEnableClearCurVersionCmd()
{
    return m_bEnableClearCurVersionCmd;
}


STDIMP_(BOOL) WoEtSetting::IsEnableAutoSlimIOCollect()
{
    return m_bEnableAutoSlimIOCollect;
}

STDIMP_(void) WoEtSetting::setEnableAutoSlimIOCollect(BOOL b)
{
    m_bEnableAutoSlimIOCollect = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableOptimizeNullCells()
{
    return m_bEnableOptimizeNullCells;
}

STDIMP_(void) WoEtSetting::setEnableOptimizeNullCells(BOOL b)
{
    m_bEnableOptimizeNullCells = b;
}


STDIMP_(BOOL) WoEtSetting::IsEnableImportrangeFallback()
{
    return m_bEnableImportrangeFallback;
}

void WoEtSetting::EnableImportrangeFallback(BOOL enable)
{
    m_bEnableImportrangeFallback = enable;
}


STDIMP_(BOOL) WoEtSetting::IsCrossBookEmptyUserFallback()
{
     return m_bCrossBookEmptyUserFallback;
}

void WoEtSetting::EnableCrossBookEmptyUserFallback(BOOL enable)
{
     m_bCrossBookEmptyUserFallback = enable;
}

STDIMP_(BOOL) WoEtSetting::GetCmdSerializeSizeLimit()
{
    if (m_cmdSerializeSizeLimit == 0)
    {
        int32_t v = getEnvInt("ET_SERIALIZE_LIMIT", 10);
        m_cmdSerializeSizeLimit = 1024 * 1024 * v;
    }
    return m_cmdSerializeSizeLimit;
}

BOOL WoEtSetting::IsSendCoreTime()
{
    if (m_isSendCoreTime == INVALID_VAL) 
    {
        int32_t v = getEnvInt("ET_IS_SEND_CORE_TIME", 1);
        m_isSendCoreTime = v;
    }
    return m_isSendCoreTime != 0;
}

STDIMP_(UINT16) WoEtSetting::GetCustomListCacheMaxSizePerSheet()
{
	return m_nCustomListCacheMaxSizePerSheet;
}
STDIMP_(void) WoEtSetting::SetCustomListCacheMaxSizePerSheet(UINT16 size)
{
	m_nCustomListCacheMaxSizePerSheet = size;
}
STDIMP_(BOOL) WoEtSetting::IsEnableCustomListChangedNotify()
{
	return m_bEnableCustomListChangedNotify;
}
STDIMP_(void) WoEtSetting::SetEnableCustomListChangeNotify(BOOL b)
{
	m_bEnableCustomListChangedNotify = b;
}

BOOL WoEtSetting::IsEnableDbShareFmla()
{
    return m_bEnableDbShareFmla;
}

void WoEtSetting::EnableDbShareFmla(BOOL enable)
{
    m_bEnableDbShareFmla = enable;
}
STDIMP_(UINT16) WoEtSetting::GetDbFunnelChartMaxLayerCount()
{
    if (m_dbFunnelChartMaxLayerCount == 0)
    {
        m_dbFunnelChartMaxLayerCount = 20;
        if (char* str = ::getenv("DB_FUNNEL_CHART_MAX_LAYER_COUNT"))
        {
            long maxLayerCount = std::strtol(str, nullptr, 10);
            if (maxLayerCount > 20 && maxLayerCount <= 1000)
                m_dbFunnelChartMaxLayerCount = maxLayerCount;
        }
        WOLOG_INFO << "[WoEtSetting] dbFunnelChartMaxLayerCount: "<< m_dbFunnelChartMaxLayerCount;
    }
    return m_dbFunnelChartMaxLayerCount;
}

STDIMP_(BOOL) WoEtSetting::IsEnableDbFieldRefToken() const
{
    return this->m_dbFieldRefEnabled;
}
STDIMP_(void) WoEtSetting::SetEnableDbFieldRefToken(BOOL value)
{
    this->m_dbFieldRefEnabled = value;
}

STDIMP_(UINT16) WoEtSetting::GetDbDashboardPluginMax()
{
    if (m_dbDashboardPluginMax == 0)
    {
        m_dbDashboardPluginMax = 10;
        if (char* str = ::getenv("DB_DASHBOARD_PLUGIN_MAX"))
            m_dbDashboardPluginMax = std::strtol(str, nullptr, 10);
        WOLOG_INFO << "[WoEtSetting] dbDashboardPluginMax: " << m_dbDashboardPluginMax;
    }
    return m_dbDashboardPluginMax;
}

STDIMP_(UINT16) WoEtSetting::GetDbDashboardMaxFilterCount()
{
    if (m_dbDashboardMaxFilterCount == 0)
    {
        constexpr int32_t defaultValue = 4;
        int32_t v = getEnvInt("DB_DASHBOARD_MAX_FILTER_COUNT", defaultValue);
        m_dbDashboardMaxFilterCount = (v > 0 && v < 1000) ? v : defaultValue;
    }
    return m_dbDashboardMaxFilterCount;
}

STDIMP_(INT32) WoEtSetting::GetDbChartMaxConnCacheCount()
{
    if (m_dbChartMaxConnCacheCount == INVALID_VAL)
    {
        constexpr INT32 defaultValue = 100;
        int32_t v = getEnvInt("DB_CHART_MAX_CONN_CACHE_COUNT", defaultValue);
        m_dbChartMaxConnCacheCount = v != INVALID_VAL ? v : defaultValue;
    }
    return m_dbChartMaxConnCacheCount;
}

int WoEtSetting::GetUserConnTimeout()
{
    if (INVALID_VAL == m_userConnTimeout)
    {
        int32_t v = getEnvInt("ET_USER_CONN_TIMEOUT", DEF_USER_TIMEOUT);
        m_userConnTimeout = v;
    }
    return m_userConnTimeout;
}
STDIMP_(BOOL) WoEtSetting::EnableReleaseConn()
{
    if (INVALID_VAL == m_bEnableReleaseConn)
    {
        int32_t v = getEnvInt("ET_ENABLE_RELEASE_CONN", 1);
        m_bEnableReleaseConn = v;
    }
    return m_bEnableReleaseConn != 0;
}

int WoEtSetting::GetMaxClearUserConnCount()
{
    if (INVALID_VAL == m_clearUserConnCount)
        m_clearUserConnCount = maxClearUserConnCount();
    return m_clearUserConnCount;
}

void WoEtSetting::UpdateClearUserQuitTime(unsigned int ms)
{
    unsigned int t = maxClearUserQuitTime();
    if (ms < t)
    {
        m_clearUserConnCount = std::min(maxClearUserConnCount(), m_clearUserConnCount + 1);
    }
    else if (ms < (t << 1))
    {
        m_clearUserConnCount = std::max(1, m_clearUserConnCount - 1);
    }
    else
    {
        m_clearUserConnCount = std::max(1, m_clearUserConnCount - 5);
    }
}

bool WoEtSetting::isCollectPasteInfo()
{
    if (m_enableCollectPasteInfo == -1)
    {
        const double vDef = 0;
        double v = getEnvDbl("ET_COLLECT_PASTE_PROB", vDef);
        if (fabs(v) < DBL_EPSILON)
            m_enableCollectPasteInfo = 0;
        else
            m_enableCollectPasteInfo = 1;
        WOLOG_INFO << "[WoEtSetting] m_enableCollectPasteInfo: "<< m_enableCollectPasteInfo;
    }
    return m_enableCollectPasteInfo == 1;
}

int WoEtSetting::maxClearUserConnCount()
{
    if (INVALID_VAL == m_maxClearUseConnCount)
    {
        int32_t v = getEnvInt("ET_MAX_CLEAR_USER_COUNT", 20);
        m_maxClearUseConnCount = std::max(v, 1);
    }
    return m_maxClearUseConnCount;
}

int WoEtSetting::maxClearUserQuitTime()
{
    if (INVALID_VAL == m_maxClearUserConnTime)
    {
        int32_t v = getEnvInt("ET_MAX_CLEAR_USER_TIME", 2000);
        m_maxClearUserConnTime = v;
    }
    return m_maxClearUserConnTime;
}

STDIMP_(INT32) WoEtSetting::GetDbFilterLookupResCacheEnableThreshold()
{
    // 引用字段筛选handle筛选结果缓存启用阈值
    if (m_nDbFilterTokenResCacheEnableThreshold == INVALID_VAL)
    {
        if (char* str = ::getenv("DB_FILTER_LOOKUP_RES_CACHE_ENABLE_THRESHOLD"))
        {
            int threshold = static_cast<int>(std::strtol(str, nullptr, 10));
            if (threshold > 0) 
                m_nDbFilterTokenResCacheEnableThreshold = threshold;
            else
                m_nDbFilterTokenResCacheEnableThreshold = DEFAULT_DB_FILTER_LOOKUP_RES_CACHE_ENABLE_THRESHOLD;
            WOLOG_INFO << "[WoEtSetting] DB_FILTER_LOOKUP_RES_CACHE_ENABLE_THRESHOLD: " << threshold;
        }
        else
        {
            m_nDbFilterTokenResCacheEnableThreshold = DEFAULT_DB_FILTER_LOOKUP_RES_CACHE_ENABLE_THRESHOLD;
        }
    }
    return m_nDbFilterTokenResCacheEnableThreshold;
}

STDIMP_(BOOL) WoEtSetting::GetDelOpDisableTransOpt()
{
    return m_bDelOpDisableTransOpt;
}

STDIMP_(void) WoEtSetting::SetDelOpDisableTransOpt(BOOL b)
{
    m_bDelOpDisableTransOpt = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableRegionProtect()
{
    return m_bEnableRegionProtect;
}

STDIMP_(void) WoEtSetting::SetEnableRegionProtect(BOOL b)
{
    m_bEnableRegionProtect = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableOwnerProtect()
{
    return m_bEnableOwnerProtect;
}

STDIMP_(void) WoEtSetting::SetEnableOwnerProtect(BOOL b)
{
    m_bEnableOwnerProtect = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableAutoUpdateUserInfo()
{
    return m_bEnableAutoUpdateUserInfo;
}

STDIMP_(void) WoEtSetting::SetEnableAutoUpdateUserInfo(BOOL b)
{
    m_bEnableAutoUpdateUserInfo = b;
}

STDIMP_(DOUBLE) WoEtSetting::GetAutoUpdateUserInfoTime()
{
    return m_autoUpdateUserInfoTime;
}

STDIMP_(void) WoEtSetting::SetAutoUpdateUserInfoTime(DOUBLE time)
{
    m_autoUpdateUserInfoTime = time;
}

void WoEtSetting::SetLimitExclusiveRow(UINT row) 
{
    m_limitExclusiveRow = row;
}

UINT WoEtSetting::GetLimitExclusiveRow() 
{
    return m_limitExclusiveRow;
}

void WoEtSetting::SetLimitExclusiveCol(UINT col) 
{
    m_limitExclusiveCol = col;
}

UINT WoEtSetting::GetLimitExclusiveCol() 
{
    return m_limitExclusiveCol;
}


void WoEtSetting::SetLimitExclusiveCell(UINT cell)
{
    m_limitExclusiveCell = cell;
}

UINT  WoEtSetting::WoEtSetting::GetLimitExclusiveCell()
{
    return m_limitExclusiveCell;
}

STDIMP_(BOOL) WoEtSetting::IsEnableExclusiveRange()
{
    return m_bEnableExclusiveRange;
}

STDIMP_(void) WoEtSetting::SetEnableExclusiveRange(BOOL b)
{
    m_bEnableExclusiveRange = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableMannualCalcBreak()
{
    return m_bEnableMannualCalcBreak;
}

STDIMP_(void) WoEtSetting::SetEnableMannualCalcBreak(BOOL b)
{
    m_bEnableMannualCalcBreak = b;
}

STDIMP_(BOOL) WoEtSetting::IsDelayUpdateDbChart()
{
    if (INVALID_VAL == m_delayUpdateDbChart)
    {
        int32_t v = getEnvInt("DB_DELAY_UPDATE_CHART", 1);
        m_delayUpdateDbChart = v;
    }
    return m_delayUpdateDbChart;
}

STDIMP_(void) WoEtSetting::SetMaxHookCacheSize(INT32 maxHookCacheSize)
{
    m_maxHookCacheSize = maxHookCacheSize;
}

STDIMP_(INT32) WoEtSetting::GetMaxHookCacheSize() const
{
    return m_maxHookCacheSize;
}

STDIMP_(BOOL) WoEtSetting::IsDbAutolinkItemOrdered()
{
    return m_bKeepDbLinkItemOrdered;
}

STDIMP_(void) WoEtSetting::SetDbAutolinkItemOrdered(BOOL b)
{
    m_bKeepDbLinkItemOrdered = b;
}

BOOL WoEtSetting::IsBatchNotifyCBRInfo()
{
    return m_bBatchNotifyCBRInfo;
}

void WoEtSetting::SetBatchNotifyCBRInfo(BOOL b)
{
    m_bBatchNotifyCBRInfo = b;
}

STDIMP_(UINT8) WoEtSetting::GetMaxSidebarFolderTreeLevel()
{
    return m_maxSidebarFolderTreeLevel;
}

void WoEtSetting::SetMaxSidebarFolderTreeLevel(UINT8 level)
{
    m_maxSidebarFolderTreeLevel = level;
}

STDIMP_(UINT8) WoEtSetting::GetMaxSidebarFolderTreeFolderCnt()
{
    return m_maxSidebarFolderTreeFolderCnt;
}

void WoEtSetting::SetMaxSidebarFolderTreeFolderCnt(UINT8 count)
{
    m_maxSidebarFolderTreeFolderCnt = count;
}

STDIMP_(BOOL) WoEtSetting::IsEnableOptimizeClearCell()
{
    return m_bEnableOptimizeClearCell;
}

STDIMP_(void) WoEtSetting::SetEnableOptimizeClearCell(BOOL b)
{
    m_bEnableOptimizeClearCell = b;
}

STDIMP_(BOOL) WoEtSetting::GetEnabledCalcBreak() const
{
    return alg::bool2BOOL(m_enabledCalcBreak);
}

STDIMP_(void) WoEtSetting::SetEnabledCalcBreak(BOOL b)
{
    m_enabledCalcBreak = b;
}

STDIMP_(BOOL) WoEtSetting::GetEnabledDynaArrEnumOpt() const
{
    return m_enableDynaArrEnumOpt;
}

STDIMP_(void) WoEtSetting::SetEnabledDynaArrEnumOpt(BOOL b)
{
    m_enableDynaArrEnumOpt = b;
}

STDIMP_(INT32) WoEtSetting::GetDelaySerializeXfsInitSize()
{
	return m_delaySerializeXfsInitSize;
}

STDIMP_(void) WoEtSetting::SetDelaySerializeXfsInitSize(INT32 sz)
{
	m_delaySerializeXfsInitSize = sz;
}

STDIMP_(BOOL) WoEtSetting::IsEnableStatisticSheet()
{
    return m_bEnableStatictisSheet;
}

STDIMP_(void) WoEtSetting::SetEnableStatisticSheet(BOOL b)
{
    m_bEnableStatictisSheet = b;
}
STDIMP_(BOOL) WoEtSetting::NeedCheckAllSheetsPermission()
{
    return m_bNeedCheckAllSheetsPermission;
}

void WoEtSetting::SetNeedCheckAllSheetsPermission(BOOL checkAllSheets)
{
    m_bNeedCheckAllSheetsPermission = checkAllSheets;
}


STDIMP_(BOOL) WoEtSetting::IsEnableGridSheetSyncSheet()
{
    if (INVALID_VAL == m_nEnableGridSheetSyncSheet)
    {
        int32_t v = getEnvInt("DB_ENABLE_GRIDSHEET_SYNC_SHEET", 1);
        m_nEnableGridSheetSyncSheet = v != 0;
    }
    return m_nEnableGridSheetSyncSheet != 0;
}
void WoEtSetting::SetEnableDBResizeRowOpt(BOOL b)
{
    m_bEnableDBResizeRowOpt = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableDBResizeRowOpt()
{
    return m_bEnableDBResizeRowOpt;
}
void WoEtSetting::SetEnableVersionTag(BOOL b)
{
    m_bEnableVersionTag = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableVersionTag()
{
    return m_bEnableVersionTag;
}

util::RandDist* WoEtSetting::getDBViewUpdateTimeRand()
{
    if (!m_spDBViewUpdateTimeRand)
    {
        m_spDBViewUpdateTimeRand.reset(new util::RandDist(GetRandSeed(), getDBViewUpdateCollectProbability()));
    }
    return m_spDBViewUpdateTimeRand.get();
}

double WoEtSetting::getDBViewUpdateCollectProbability()
{
    if (fabs(m_collectDBViewUpdateProbability) < DBL_EPSILON)
    {
        const double vDef = 0.001;
        double v = getEnvDbl("ET_DB_VIEW_UPDATE_TIME_PROB", vDef);
        m_collectDBViewUpdateProbability = v <= 0 ? 0 : (v >= 1.0 ? 1.0 : v);
        WOLOG_INFO << "[WoEtSetting] m_collectDBViewUpdateProbability"<< m_collectDBViewUpdateProbability;
    }
    return m_collectDBViewUpdateProbability;
}

STDIMP_(BOOL) WoEtSetting::IsEnableEventTrackingCollect()
{
    if (INVALID_VAL == m_nEnableEventTrackingCollect)
    {
        int32_t v = getEnvInt("ET_ENABLE_EVENT_TRACKING_COLLECT", 0);
        m_nEnableEventTrackingCollect = v != 0;
    }
    return m_nEnableEventTrackingCollect != 0;
}

STDIMP_(BOOL) WoEtSetting::IsEnableDBViewUpdateCollect()
{
    if (INVALID_VAL == m_nEnableDBViewUpdateCollect)
    {
        int32_t v = getEnvInt("DB_ENABLE_VIEW_UPDATE_COLLECT", 1);
        m_nEnableDBViewUpdateCollect = v != 0;
    }
    return m_nEnableDBViewUpdateCollect != 0;
}

STDIMP_(void) WoEtSetting::SetOpenTrackingThousandThreshold(UINT ms)
{
	m_OpenTrackingThousandThreshold = ms;
}
STDIMP_(UINT) WoEtSetting::GetOpenTrackingThousandThreshold()
{
    return m_OpenTrackingThousandThreshold;
}

STDIMP_(void) WoEtSetting::SetOpenTrackingOneThreshold(UINT ms)
{
    m_OpenTrackingOneThreshold = ms;
}

STDIMP_(UINT) WoEtSetting::GetOpenTrackingOneThreshold()
{
    return m_OpenTrackingOneThreshold;
}

STDIMP_(BOOL) WoEtSetting::IsEt2DbEnableSupportImages()
{
    return m_bEt2DbEnableSupportImages;
}

STDIMP_(void) WoEtSetting::SetEt2DbEnableSupportImages(BOOL b)
{
    m_bEt2DbEnableSupportImages = b;
}

STDIMP_(BOOL) WoEtSetting::IsEnableTruncateOpen()
{
    return m_bEnableTruncateOpen;
}

STDIMP_(void) WoEtSetting::SetEnableTruncateOpen(BOOL b)
{
    m_bEnableTruncateOpen = b;
}

STDIMP_(INT32) WoEtSetting::GetTruncateRowOnFileOpen() const
{
	return m_truncateRowOnFileOpen;
}

STDIMP_(void) WoEtSetting::SetTruncateRowOnFileOpen(INT32 r)
{
	m_truncateRowOnFileOpen = r;
	WOLOG_INFO << "[GetWoEtSettings] TRUNCATE_ROW_ONFILEOPEN: " << r;
}

STDIMP_(INT32) WoEtSetting::GetTruncateColOnFileOpen() const
{
	return m_truncateColOnFileOpen;
}

STDIMP_(void) WoEtSetting::SetTruncateColOnFileOpen(INT32 c)
{
	m_truncateColOnFileOpen = c;
	WOLOG_INFO << "[GetWoEtSettings] TRUNCATE_COL_ONFILEOPEN: " << c;
}

// 因为只有打开截断才使用，且只使用一次，用到才进行加载
STDIMP_(DOUBLE) WoEtSetting::GetTruncateCompressRate() const
{
	if (char *str = ::getenv("TRUNCATE_COMPRESS_RATE"))
	{
		double dTmp = std::strtod(str, nullptr);
		if (0.0 <= dTmp && dTmp <= 1.0)
		{
			WOLOG_INFO << "[GetWoEtSettings] TRUNCATE_COMPRESS_RATE: " << dTmp;
			return dTmp;
		}
	}
    return 0.0;
}

// 因为只有打开截断才使用，且只使用一次，用到才进行加载
STDIMP_(UINT64) WoEtSetting::GetTruncateFileSize() const
{
	if (char *str = ::getenv("TRUNCATE_FILE_SIZE"))
	{
		unsigned long long int llTmp = std::strtoull(str, nullptr, 10);
		if (llTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] TRUNCATE_FILE_SIZE: " << llTmp;
			return llTmp;
		}
	}
    return 0;
}

// 因为只有打开截断才使用，且只使用一次，用到才进行加载
STDIMP_(UINT64) WoEtSetting::GetTruncateMemorySize() const
{
	if (char *str = ::getenv("TRUNCATE_MEMORY_SIZE"))
	{
		unsigned long long int llTmp = std::strtoull(str, nullptr, 10);
		if (llTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] TRUNCATE_MEMORY_SIZE: " << llTmp;
			return llTmp;
		}
	}
    return 0;
}

// 因为只有打开截断才使用，且只使用一次，用到才进行加载
STDIMP_(UINT) WoEtSetting::GetTruncateMemoryCheckCnt() const
{
	if (char *str = ::getenv("TRUNCATE_MEMORY_CHECK_COUNT"))
	{
		unsigned long long int llTmp = std::strtoull(str, nullptr, 10);
		if (llTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] TRUNCATE_MEMORY_CHECK_COUNT: " << llTmp;
			return llTmp;
		}
	}
    return 0;
}

// 因为只有打开截断才使用，且只使用一次，用到才进行加载
STDIMP_(UINT32) WoEtSetting::GetTruncateValidCell() const
{
	if (char *str = ::getenv("TRUNCATE_FILESUMMARY_VALIDCELL"))
	{
		unsigned int uTmp = std::strtoul(str, nullptr, 10);
		if (uTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] TRUNCATE_FILESUMMARY_VALIDCELL: " << uTmp;
			return uTmp;
		}
	}
    return 0;
}

// 因为只有打开截断才使用，且只使用一次，用到才进行加载
STDIMP_(UINT32) WoEtSetting::GetTruncateCellblockMem() const
{
	if (char *str = ::getenv("TRUNCATE_FILESUMMARY_CELLMEM"))
	{
		unsigned int uTmp = std::strtoul(str, nullptr, 10);
		if (uTmp >= 0)
		{
			WOLOG_INFO << "[GetWoEtSettings] TRUNCATE_FILESUMMARY_CELLMEM: " << uTmp;
			return uTmp;
		}
	}
    return 0;
}

int WoEtSetting::GetCollectMemDiffTime()
{
    if (INVALID_VAL == m_collectDiffMemTime)
    {
        int32_t v = getEnvInt("ET_COLLECT_MEM_DIFF_TIME", 4);
        m_collectDiffMemTime = v;
    }
    return m_collectDiffMemTime;
}

STDIMP_(BOOL) WoEtSetting::IsBlockNonNeedRdView()
{
    if (INVALID_VAL == m_isBlockNonNeedRdView)
    {
        int32_t v = getEnvInt("ET_BLOCK_NON_NEED_RD_VIEW", 1);
        m_isBlockNonNeedRdView = v ? 1 : 0;
    }
    return m_isBlockNonNeedRdView;
}

STDIMP_(double) WoEtSetting::GetPasteProbability()
{
	if (fabs(m_collectPasteProbability) < DBL_EPSILON)
	{
		const double vDef = 0.001;
		double v = getEnvDbl("ET_COLLECT_PASTE_PROB", vDef);
		m_collectPasteProbability = (v <= 0 || v >= 1.0) ? vDef : v;
		WOLOG_INFO << "[WoEtSetting] m_collectPasteProbability: " << m_collectPasteProbability;
	}
	return m_collectPasteProbability;
}
BOOL WoEtSetting::IsEnableOpenProgress()
{
    return m_enableOpenProgress;
}
void WoEtSetting::SetEnableOpenProgress(BOOL enable)
{
    m_enableOpenProgress = enable;
}


STDIMP_(void) WoEtSetting::SetOpenProgressDozenThreshold(UINT ms)
{
    m_OpenProgressDozenThreshold = ms;
}

STDIMP_(UINT) WoEtSetting::GetOpenProgressDozenThreshold()
{
    return m_OpenProgressDozenThreshold;
}

STDIMP_(void) WoEtSetting::SetOpenProgressNotifyDuration(UINT ms)
{
    m_OpenProgressNotifyDuration = ms;
}

STDIMP_(UINT) WoEtSetting::GetOpenProgressNotifyDuration()
{
    return m_OpenProgressNotifyDuration;
}

STDIMP_(UINT) WoEtSetting::GetPasteProgressNotifyDuration()
{
    if (INVALID_VAL == m_pasteProgressNotifyDuration)
    {
        int32_t v = getEnvInt("PASTE_PROGRESS_NOTIFY_DURATION", 0);
        m_pasteProgressNotifyDuration = v;
    }
    return m_pasteProgressNotifyDuration;
}

STDIMP_(UINT16) WoEtSetting::GetDbDashboardViewMax()
{
    if (m_dbDashboardViewMax == 0)
    {
        m_dbDashboardViewMax = 5;
        if (char* str = ::getenv("DB_DASHBOARD_VIEW_MAX"))
            m_dbDashboardViewMax = std::strtol(str, nullptr, 10);
        WOLOG_INFO << "[WoEtSetting] dbDashboardViewMax: " << m_dbDashboardViewMax;
    }
    return m_dbDashboardViewMax;
}

STDIMP_(BOOL) WoEtSetting::IsEnableCfNotify()
{
    if (INVALID_VAL == m_enableCFNotify)
    {
        m_enableCFNotify = 0;
        char* str = ::getenv("CF_CACHE_UPDATE_MODE");
        if (str)
        {
            if (strncmp(str, "del", 3) == 0)
                m_enableCFNotify = 1;
            else if (strncmp(str, "rc4del", 6) == 0)
                m_enableCFNotify = 1;
        }
        WOLOG_INFO << "[GetCfCacheUpdateMode]: " << m_enableCFNotify;
    }
    return m_enableCFNotify == 1 ? TRUE : FALSE;
}

int WoEtSetting::GetTidyImagePoolThreshold()
{
    return m_nTidyImagePoolThreshold;
}

void WoEtSetting::SetTidyImagePoolThreshold(int v)
{
    m_nTidyImagePoolThreshold = v;
}

STDIMP_(void) WoEtSetting::SetDashBoardSnapshotWidth(UINT width)
{
    m_nDashBoardSnapshotWidth = width;
}

STDIMP_(UINT) WoEtSetting::GetDashBoardSnapshotWidth()
{
    return m_nDashBoardSnapshotWidth;
}
int WoEtSetting::GetCollectMemUsageThreshold()
{
    if (m_nCollectCmdMemUsageThreshold == 0)
    {
        int uVal = getEnvInt("COLLECT_MEM_USAGE_THRESHOLD", 1024 * 1024 * 5); // 5M
        WOLOG_INFO << "[GetWoEtSettings] COLLECT_MEM_USAGE_THRESHOLD: " << uVal;
        m_nCollectCmdMemUsageThreshold = std::max(uVal, 1024 * 1024);
    }
    return m_nCollectCmdMemUsageThreshold;
}
STDIMP_(void) WoEtSetting::SetDashBoardSnapshotMinHeight(UINT minHeight)
{
    m_nDashBoardSnapshotMinHeight = minHeight;
}
STDIMP_(UINT) WoEtSetting::GetDashBoardSnapshotMinHeight()
{
    return m_nDashBoardSnapshotMinHeight;
}
STDIMP_(BOOL) WoEtSetting::IsEnableMetricSignalData()
{
    if (INVALID_VAL == m_isEnableMetricSignal)
    {
#ifdef _DEBUG
        const int nDef = 1;
#else
        const int nDef = 0;
#endif        
        int32_t v = getEnvInt("ET_ENABLE_METRIC_SIGNAL", nDef);
        m_isEnableMetricSignal = v != 0;
    }
    return m_isEnableMetricSignal;
}

STDIMP_(void) WoEtSetting::GetMetricSignalRange(INT & min, INT & max)
{
    if (INVALID_VAL == m_minMetricSignalSz)
    {
#ifdef _DEBUG
        const int nDefMin = 102400;
#else
        const int nDefMin = DEFAULT_MIN_METRIC_SIGNAL_SIZE;
#endif 
        int32_t min = getEnvInt("ET_MIN_METRIC_SIGNAL_SZ", nDefMin);
        m_minMetricSignalSz = min; 
        
        int32_t max = getEnvInt("ET_MAX_METRIC_SIGNAL_SZ", DEFAULT_MAX_METRIC_SIGNAL_SIZE);
        m_maxMetricSignalSz = max; 
        if (m_maxMetricSignalSz < m_minMetricSignalSz)
            m_maxMetricSignalSz = m_minMetricSignalSz;
    }
    min = m_minMetricSignalSz;
    max = m_maxMetricSignalSz;
}

STDIMP_(INT32) WoEtSetting::GetMetricSignalMaxCnt()
{
    if (INVALID_VAL == m_maxMetricSignalCnt)
    {
#ifdef _DEBUG
        const int nDef = 1000;
#else
        const int nDef = 5;
#endif   
        int32_t v = getEnvInt("ET_MAX_METRIC_SIGNAL_CNT", nDef);
        m_maxMetricSignalCnt = v; 
    }
    return m_maxMetricSignalCnt;
}

STDIMP_(BOOL) WoEtSetting::IsBanTracker()
{
    if (INVALID_VAL == m_nBanTracker)
    {
        int32_t v = getEnvInt("BAN_CELL_TRACKER", 0);  // 默认是false
        m_nBanTracker = v != 0;
    }
    return m_nBanTracker != 0;
}

STDIMP_(BOOL) WoEtSetting::IsEnableCommonLog()
{
    if (INVALID_VAL == m_nEnableCommonLog.first)
    {
        int32_t v = getEnvInt("ENABLE_COMMON_LOG", 0);
        m_nEnableCommonLog.first = v != 0;
    }
    return m_nEnableCommonLog.first != 0;
}

STDIMP_(BOOL) WoEtSetting::IsNeedUpdateEnableCommonLog()
{
    if (m_nEnableCommonLog.second == std::chrono::steady_clock::time_point())
        return TRUE;
    const auto now = std::chrono::steady_clock::now();
    const auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - m_nEnableCommonLog.second);
    constexpr auto MIN_INTERVAL = std::chrono::minutes(5);
    return duration >= MIN_INTERVAL;
}

STDIMP_(PCSTR) WoEtSetting::GetCommonLogDir()
{
    if (m_strCommonLogDir.empty())
    {
        PCSTR str = ::getenv("COMMON_LOG_DIR");
        if (str)
            m_strCommonLogDir = str;
        else
            m_strCommonLogDir = DEFAULT_COMMON_LOG_DIR;
    }
    return m_strCommonLogDir.c_str();
}

STDIMP_(int) WoEtSetting::GetCommonLogMaxEntrySize()
{
    if (INVALID_VAL == m_nCommonLogMaxEntrySize)
    {
        int32_t v = getEnvInt("COMMON_LOG_MAX_ENTRY_SIZE", COMMON_LOG_MAX_ENTRY_SIZE);
        m_nCommonLogMaxEntrySize = v;
    }
    return m_nCommonLogMaxEntrySize;
}

STDIMP_(void) WoEtSetting::SetEnableCommonLog(BOOL bEnable)
{
    if (IsNeedUpdateEnableCommonLog())
    {
        m_nEnableCommonLog.first = bEnable ? 1 : 0;
        m_nEnableCommonLog.second = std::chrono::steady_clock::now();
    }
}
}  // end namespace wo
