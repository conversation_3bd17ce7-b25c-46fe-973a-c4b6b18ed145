﻿#ifndef __WOETSETTINGS_H__
#define __WOETSETTINGS_H__

#include "webbase/calback.h"


constexpr unsigned int DEFAULT_TIME_LIMIT_SAVE_BREAK_SECDOC = 2000;
constexpr unsigned int DEFAULT_TIME_LIMIT_SAVE_BREAK_GENERAL = 15 * 60 * 1000;
constexpr int UDF_SINGLE_REQUEST_LIMIT = 5000;
constexpr int DEFAULT_MAX_HOOK_CACHE_SIZE = 200;

constexpr DOUBLE DEFAULT_DOCSLIM_BOUND_DENSITY = 0.05;
constexpr UINT64 DEFAULT_DOCSLIM_NULL_CELLS_LIMIT = 3435973;
constexpr UINT32 DEFAULT_DOCSLIM_UNREFERENCED_PICTURE_TRIGGER_VALUE = 10;
constexpr UINT32 DEFAULT_DOCSLIM_INVISIBLE_OBJECT_TRIGGER_VALUE = 500;
constexpr UINT16 DEFAULT_DOCSLIM_ROW_LOW_DENSITY_TOLERANCE_INDEX = 10;
constexpr UINT16 DEFAULT_DOCSLIM_COL_LOW_DENSITY_TOLERANCE_INDEX = 10;
constexpr UINT16 DEFAULT_DOCSLIM_BOUND_ADVANCE = 4;
constexpr UINT8  DEFAULT_MAX_PARENT_RECORD_LEVEL = 6; 
constexpr UINT32 DEFAULT_DOCSLIM_NULL_CELLS_TRIGGER_VALUE = 100000;
constexpr UINT32 DEFAULT_DOCSLIM_OVERLAP_SHAPES_TRIGGER_VALUE = 500;
constexpr UINT32 DEFAULT_DOCSLIM_UNUSED_DUPLICATE_STYLE_TRIGGER_VALUE = 3000;
constexpr UINT32 DEFAULT_DOCSLIM_DUPLICATE_FORMAT_CONDITION_TRIGGER_VALUE = 10;
constexpr UINT32 DEFAULT_HTML_COPY_LIMIT_SIZE = 2 * 1024 *1024; //2m

constexpr UINT16 DEFAULT_CUSTOM_LIST_CACHE_MAX_SIZE_PER_SHEET = 1000;
constexpr UINT8  DEFAULT_MAX_SIDEBAR_FOLDER_TREE_LEVEL = 3;
constexpr UINT8  DEFAULT_MAX_SIDEBAR_FOLDER_TREE_FOLDER_CNT = 100;

constexpr INT DEFAULT_MIN_METRIC_SIGNAL_SIZE = 20 * 1024 * 1024;
constexpr INT DEFAULT_MAX_METRIC_SIGNAL_SIZE = 30 * 1024 * 1024;

namespace wo {

namespace util {
    class RandDist;
}

class WoEtSetting : public IWoETSettings
{
public:
    WoEtSetting();

    STDPROC_(BOOL) getGeneralNumEllipOn() override;
	STDPROC_(void) setGeneralNumEllipOn(BOOL b) override;

    STDPROC_(BOOL) IsEnablingCellHistoryStore() override;
    STDPROC_(INT) GetCellHistoryMaxPurgeTag() override;
	STDPROC_(INT) GetCellHistoryReserveCount() override;
	STDPROC_(INT) GetCellHistoryExpireSecs() override;

	STDPROC_(BOOL) IsEnableSavingBreakCal() override;
	STDPROC_(void) setEnableSavingBreakCal(BOOL b) override;

    STDPROC_(unsigned int) GetTimeLimitSaveBreakSecdoc() override;
	STDPROC_(void) SetTimeLimitSaveBreakSecdoc(unsigned int) override;

    STDPROC_(unsigned int) GetTimeLimitSaveBreakGeneral() override;
	STDPROC_(void) SetTimeLimitSaveBreakGeneral(unsigned int) override;

    STDPROC_(BOOL) getSupportCellMultiLink() override;
	STDPROC_(void) setSupportCellMultiLink(BOOL b) override;

	STDPROC_(BOOL) getSupportDynamicCalc() override;
	STDPROC_(void) setSupportDynamicCalc(BOOL b) override;

	STDPROC_(LPCWSTR) GetFileId() override;
	STDPROC_(bool) IsEnableNewUpdateRender() override;

    STDPROC_(void) SetEnableNewBatchCopy(BOOL bEnable) override;
    STDPROC_(BOOL) IsEnableNewBatchCopy() override;

    STDPROC_(void) SetEnableCFApplyWhenCopy(BOOL bApplyCF) override;
    STDPROC_(BOOL) IsEnableCFApplyWhenCopy() override;

    STDPROC_(void) SetUDFSingleRequestLimit(INT32 limitSize) override;
    STDPROC_(INT32) GetUDFSingleRequestLimit() const override;

    STDPROC_(DOUBLE) GetDocSlimBoundDensity() override;
    STDPROC_(void) SetDocSlimBoundDensity(DOUBLE density) override;

    STDPROC_(UINT64) GetDocSlimNullCellsLimit() override;
    STDPROC_(void) SetDocSlimNullCellsLimit(UINT64 limit) override;

    STDPROC_(UINT32) GetDocSlimNullCellsTriggerValue() override;
    STDPROC_(void) SetDocSlimNullCellsTriggerValue(UINT32 limit) override;

    STDPROC_(UINT32) GetDocSlimInvisibleObjectTriggerValue() override;
    STDPROC_(void) SetDocSlimInvisibleObjectTriggerValue(UINT32 limit) override;

    STDPROC_(UINT32) GetDocSlimUnreferencedPictureTriggerValue() override;
    STDPROC_(void) SetDocSlimUnreferencedPictureTriggerValue(UINT32 limit) override;

    STDPROC_(UINT32) GetDocSlimOverlapShapesTriggerValue() override;
    STDPROC_(void) SetDocSlimOverlapShapesTriggerValue(UINT32 limit) override;

    STDPROC_(UINT32) GetDocSlimUnusedDuplicateStyleTriggerValue() override;
    STDPROC_(void) SetDocSlimUnusedDuplicateStyleTriggerValue(UINT32 limit) override;

    STDPROC_(UINT32) GetDocSlimDuplicateFormatConditionTriggerValue() override;
    STDPROC_(void) SetDocSlimDuplicateFormatConditionTriggerValue(UINT32 limit) override;

    STDPROC_(UINT32) GetHtmlCopyMaxLimitSize() override;
    STDPROC_(void) SetHtmlCopyMaxLimitSize(UINT32 limit) override;

    STDPROC_(UINT16) GetDocSlimRowLowDensityToleranceIndex() override;
    STDPROC_(void) SetDocSlimRowLowDensityToleranceIndex(UINT16 index) override;

    STDPROC_(UINT16) GetDocSlimColLowDensityToleranceIndex() override;
    STDPROC_(void) SetDocSlimColLowDensityToleranceIndex(UINT16 index) override;

    STDPROC_(UINT16) GetDocSlimBoundAdvance() override;
    STDPROC_(void) SetDocSlimBoundAdvance(UINT16 advance) override;

    STDPROC_(BOOL) IsEnableAutoSlim() override;
	STDPROC_(void) SetEnableAutoSlim(BOOL b) override;

    STDPROC_(BOOL) IsEnablePivotItemMove() override;
    STDPROC_(void) SetEnablePivotItemMove(BOOL b) override;

    STDPROC_(BOOL) IsEnableFindThousandsSeparators() override;
    STDPROC_(void) SetEnableFindThousandsSeparators(BOOL b) override;

    STDPROC_(BOOL) GetDefaultFilterShared() override;
    STDPROC_(void) SetDefaultFilterShared(BOOL b) override;

    STDPROC_(BOOL) IsEnableAutoSlimIOCollect() override;
	STDPROC_(void) setEnableAutoSlimIOCollect(BOOL b) override;

    STDPROC_(void) SetEnableParentRecord(BOOL bEnable) override;
    STDPROC_(BOOL) IsEnableParentRecord() override;

	STDPROC_(UINT8) GetMaxParentRecordLevel() override;
    STDPROC_(void) SetMaxParentRecordLevel(UINT8 level) override;

    STDPROC_(void) SetEnableClearCurVersionCmd(BOOL bEnable) override;
    STDPROC_(BOOL) IsEnableClearCurVersionCmd() override;

    void EnableCellHistoryStore(bool enable);
    void SetCellHistoryMaxPurgeTag(INT);
	void SetCellHistoryReserveCount(INT);
	void SetCellHistoryExpireSecs(INT ExpireSeconds, bool addTimeBias = true);
	void SetFileId(LPCWSTR fileid);
	void SetEnableNewUpdateRender(bool b);
    STDPROC_(INT) GetCopySpreadsheet6FormatMaxSize() override;
    STDPROC_(BOOL) IsEnableSubscriptionNotifyToUnoTag() override;
    STDPROC_(BOOL) IsOpenSetFileTagProtected() override;
    STDPROC_(BOOL) IsSecEnableSaveBreak() override;

	STDPROC_(WoIoThreadMode) GetOpenIoThreadMode() override;
    STDPROC_(WoIoThreadMode) GetAutoOpenIoThreadMode() override;
	STDPROC_(bool) IsOpenMultiThreadBySheet() override;
	STDPROC_(int) GetOpenTimeThreshold() override; // 单位ms 单线程切换的时间阈值

	STDPROC_(int) GetOpenBookCompileFmlThreshold() override; // 最近一次单元格公式编译数量
	STDPROC_(int) GetOpenAllSheetsSize() override; // 所有sheet的文件大小
	STDPROC_(int) GetOpenBookFastThreshold() override;

	STDPROC_(int) GetOpenSheetCompileFmlThreshold() override; // 最近一次单元格公式编译数量
	STDPROC_(int) GetOpenSheetSize() override;
    STDPROC_(int) GetOpenMinFastThreshold() override;

	STDPROC_(BOOL) IsCommentRecordAddCmtJudgeTransact() override;

    STDPROC_(bool) isCollect(int ms, int * pSampleRate, util::RandDist *rand, const int * pThreshold = nullptr) override;
    STDPROC_(int) GetEnvInt(const char * env, const int def) override;
    STDPROC_(wo::util::RandDist *) GetCollectTimeRand(CollectTimeRandType) override;
    util::RandDist * GetCFCollectFmlaRand();
    util::RandDist * GetCFCalcTimeRand();
    util::RandDist * GetCFQueryTimeRand();
    util::RandDist * GetCmdTimeRand();
    util::RandDist * GetQueryInitTimeRand();
    util::RandDist * GetApiTimeRand();
    util::RandDist * GetFormulaComplexityRand();
    STDPROC_(util::RandDist *) GetPasteRand() override;
    bool isEnableStaleNotify();
    STDPROC_(BOOL) IsEnableImportrangeFallback() override;
    void EnableImportrangeFallback(BOOL);

    STDPROC_(BOOL) IsCrossBookEmptyUserFallback() override;
    void EnableCrossBookEmptyUserFallback(BOOL);
    STDPROC_(BOOL) IsEnableOptimizeNullCells() override;
	STDPROC_(void) setEnableOptimizeNullCells(BOOL b) override;
    
    STDPROC_(BOOL) IsSendCoreTime() override;

    STDPROC_(UINT8) GetMaxSidebarFolderTreeLevel() override;
    STDPROC_(void) SetMaxSidebarFolderTreeLevel(UINT8 level) override;
	STDPROC_(UINT8) GetMaxSidebarFolderTreeFolderCnt() override;
	STDPROC_(void) SetMaxSidebarFolderTreeFolderCnt(UINT8 level) override;

	STDPROC_(BOOL) GetEnabledCalcBreak() const override;
	STDPROC_(void) SetEnabledCalcBreak(BOOL b) override;

	STDPROC_(BOOL) GetEnabledDynaArrEnumOpt() const override;
	STDPROC_(void) SetEnabledDynaArrEnumOpt(BOOL b) override;

	STDPROC_(UINT16) GetCustomListCacheMaxSizePerSheet() override;
	STDPROC_(void) SetCustomListCacheMaxSizePerSheet(UINT16) override;
	STDPROC_(BOOL) IsEnableCustomListChangedNotify() override;
	STDPROC_(void) SetEnableCustomListChangeNotify(BOOL) override;
    STDPROC_(BOOL) IsEnableDbShareFmla() override;
    void EnableDbShareFmla(BOOL);
    STDPROC_(BOOL) GetCmdSerializeSizeLimit() override;

    STDPROC_(UINT16) GetDbFunnelChartMaxLayerCount() override;
    STDPROC_(UINT16) GetDbDashboardPluginMax() override;
    STDPROC_(UINT16) GetDbDashboardMaxFilterCount() override;
    STDPROC_(INT32) GetDbChartMaxConnCacheCount() override;
	STDPROC_(BOOL) IsEnableDbFieldRefToken() const override;
	STDPROC_(void) SetEnableDbFieldRefToken(BOOL) override;
    int GetUserConnTimeout();
    int GetMaxClearUserConnCount();
    void UpdateClearUserQuitTime(unsigned int ms);
    STDPROC_(bool) isCollectPasteInfo() override;
    STDPROC_(BOOL) EnableReleaseConn() override;

    STDPROC_(BOOL) IsEnableRegionProtect() override;
	STDPROC_(void) SetEnableRegionProtect(BOOL b) override;

    STDPROC_(BOOL) IsEnableOwnerProtect() override;
	STDPROC_(void) SetEnableOwnerProtect(BOOL b) override;

    STDPROC_(BOOL) IsEnableAutoUpdateUserInfo() override;
	STDPROC_(void) SetEnableAutoUpdateUserInfo(BOOL b) override;

	STDPROC_(DOUBLE) GetAutoUpdateUserInfoTime() override;
	STDPROC_(void) SetAutoUpdateUserInfoTime(DOUBLE time) override;

    STDPROC_(void) SetLimitExclusiveRow(UINT row) override;
	STDPROC_(UINT) GetLimitExclusiveRow() override;

	STDPROC_(void) SetLimitExclusiveCol(UINT col) override;
	STDPROC_(UINT) GetLimitExclusiveCol() override;

	STDPROC_(void) SetLimitExclusiveCell(UINT cell) override;
	STDPROC_(UINT) GetLimitExclusiveCell() override;

    STDPROC_(BOOL) IsEnableExclusiveRange() override;
	STDPROC_(void) SetEnableExclusiveRange(BOOL b) override;

    STDPROC_(BOOL) IsDelayUpdateDbChart() override;

    STDPROC_(BOOL) IsEnableMannualCalcBreak() override;
	STDPROC_(void) SetEnableMannualCalcBreak(BOOL b) override;

	STDPROC_(INT32) GetDbFilterLookupResCacheEnableThreshold() override;
    
    STDPROC_(void) SetSlaveTimeLimitSaveBreakSecdoc(unsigned int) override;
    STDPROC_(void) SetSlaveTimeLimitSaveBreakGeneral(unsigned int) override;

    STDPROC_(void) SetMaxHookCacheSize(INT32 maxHookCnt) override;
    STDPROC_(INT32) GetMaxHookCacheSize() const override;

    STDPROC_(BOOL) IsDbAutolinkItemOrdered() override;
    STDPROC_(void) SetDbAutolinkItemOrdered(BOOL b) override;

	STDPROC_(INT32) GetDelaySerializeXfsInitSize() override;
	STDPROC_(void) SetDelaySerializeXfsInitSize(INT32 sz) override;

	STDPROC_(BOOL) IsEnableStatisticSheet() override;
	STDPROC_(void) SetEnableStatisticSheet(BOOL b) override;
    // airsrcipt 支持区域权限开发
    STDPROC_(BOOL) NeedCheckAllSheetsPermission() override;
	STDPROC_(void) SetNeedCheckAllSheetsPermission(BOOL checkAllSheets) override;
    STDPROC_(BOOL) IsEnableGridSheetSyncSheet() override;
    
	STDPROC_(BOOL) GetDelOpDisableTransOpt() override;
	STDPROC_(void) SetDelOpDisableTransOpt(BOOL b) override;

    STDPROC_(BOOL) IsEnableOptimizeClearCell() override;
	STDPROC_(void) SetEnableOptimizeClearCell(BOOL b) override;

    STDPROC_(void) SetEnableDBResizeRowOpt(BOOL b) override;
    STDPROC_(BOOL) IsEnableDBResizeRowOpt() override;

    STDPROC_(BOOL) IsEnableEventTrackingCollect() override;
    STDPROC_(BOOL) IsEnableDBViewUpdateCollect() override;

	STDPROC_(void) SetOpenTrackingThousandThreshold(UINT ms) override;
	STDPROC_(UINT) GetOpenTrackingThousandThreshold() override;

	STDPROC_(void) SetOpenTrackingOneThreshold(UINT ms) override;
	STDPROC_(UINT) GetOpenTrackingOneThreshold() override;

    STDPROC_(BOOL) IsEt2DbEnableSupportImages() override;
	STDPROC_(void) SetEt2DbEnableSupportImages(BOOL b) override;

    STDPROC_(BOOL) IsEnableOpenProgress() override;
    STDPROC_(void) SetEnableOpenProgress(BOOL enable) override;

    STDPROC_(void) SetOpenProgressDozenThreshold(UINT ms) override;
	STDPROC_(UINT) GetOpenProgressDozenThreshold() override;

    STDPROC_(void) SetOpenProgressNotifyDuration(UINT ms) override;
	STDPROC_(UINT) GetOpenProgressNotifyDuration() override;

	STDPROC_(void) SetDashBoardSnapshotWidth(UINT width) override;
	STDPROC_(UINT) GetDashBoardSnapshotWidth() override;
	STDPROC_(void) SetDashBoardSnapshotMinHeight(UINT minHeight) override;
	STDPROC_(UINT) GetDashBoardSnapshotMinHeight() override;

	STDPROC_(UINT) GetPasteProgressNotifyDuration() override;

	STDPROC_(void) SetEnableTruncateOpen(BOOL b) override;
	STDPROC_(BOOL) IsEnableTruncateOpen() override;

    STDPROC_(void) SetTruncateRowOnFileOpen(INT32 r) override;
    STDPROC_(INT32) GetTruncateRowOnFileOpen() const override;

	STDPROC_(BOOL) IsEnableMetricSignalData() override;
	STDPROC_(void) GetMetricSignalRange(INT & min, INT & max) override;
    STDPROC_(INT32) GetMetricSignalMaxCnt() override;

    STDPROC_(void) SetTruncateColOnFileOpen(INT32 c) override;
    STDPROC_(INT32) GetTruncateColOnFileOpen() const override;
    STDPROC_(DOUBLE) GetTruncateCompressRate() const override;
    STDPROC_(UINT64) GetTruncateFileSize() const override;
    STDPROC_(UINT64) GetTruncateMemorySize() const override;
	STDPROC_(UINT) GetTruncateMemoryCheckCnt() const override;
    STDPROC_(UINT32) GetTruncateValidCell() const override;
    STDPROC_(UINT32) GetTruncateCellblockMem() const override;

    STDPROC_(UINT16) GetDbDashboardViewMax() override;
    
public:
    int GetCollectMemDiffTime();
    util::RandDist * GetCoreMetricsRand();
    int GetCoreMetricsCollectThreshold();
    int GetCMSignalThreshold();
    int GetTidyImagePoolThreshold();
    void SetTidyImagePoolThreshold(int);
    int GetCollectMemUsageThreshold();

    STDPROC_(BOOL) IsBatchNotifyCBRInfo() override;
    STDPROC_(void) SetBatchNotifyCBRInfo(BOOL) override;
    STDPROC_(BOOL) IsBlockNonNeedRdView() override;
    STDPROC_(BOOL) IsEnableCfNotify() override;

    STDPROC_(double) GetPasteProbability() override;


	STDPROC_(BOOL) IsEnableVersionTag() override;
	STDPROC_(void) SetEnableVersionTag(BOOL b) override;
    
    int maxClearUserQuitTime();
    // 收集器开关
    STDPROC_(BOOL) IsBanTracker() override;
    // 收集以及写log开关，用于CDC服务
	STDPROC_(BOOL) IsEnableCommonLog() override;
    // CommonLog路径
    STDPROC_(PCSTR) GetCommonLogDir() override;
    STDPROC_(int) GetCommonLogMaxEntrySize() override;
    STDPROC_(void) SetEnableCommonLog(BOOL) override;
    STDPROC_(BOOL) IsNeedUpdateEnableCommonLog() override;
private:
    int maxClearUserConnCount();
    
    int GetCollectTimeThreshold();
    unsigned int GetRandSeed();
    double GetCFCollectFmlaProbability();
    double GetCFCalcTimeProbability();
    double GetCollectProbability();
    double GetCollectFormulaComplexityProbability();
    double GetCoreMetricsProbability();

    // DB视图埋点相关
    util::RandDist* getDBViewUpdateTimeRand();
    double getDBViewUpdateCollectProbability();
private:
    static constexpr int32_t INVALID_VAL = -1;
    struct CellHistorySetting
    {
        bool bEnable; // 是否开启单元格记录存储(存在文件，新方案)
        int nMaxPurgeTag;
        int nExpireSeconds;
        int nReserveCount;
    };

    BOOL m_numEllipis = FALSE;
    BOOL m_bSupportCellMultiHyperlink = FALSE;
	BOOL m_bSupportDynamicCalc = FALSE;
	BOOL m_bEnableSavingBreakCal = FALSE;
    BOOL m_bEnableNewBatchCopy = TRUE;
    BOOL m_bEnableCFApplyWhenCopy = TRUE;
    BOOL m_bInitSeed;
    bool m_bEnableNewUpdateRender = true;
    BOOL m_bEnableAutoSlim = FALSE;
    BOOL m_bEnablePivotItemMove = FALSE;
    BOOL m_bEnableFindThousandsSeparators = FALSE;
    BOOL m_bDefaultFilterShared = TRUE;
    BOOL m_bEnableAutoSlimIOCollect = TRUE;
    BOOL m_bEnableOptimizeNullCells = FALSE;
    BOOL m_bEnableRegionProtect = FALSE;
    BOOL m_bEnableOwnerProtect = FALSE;
    int m_enableCFNotify = -1;
    int m_enableCollectPasteInfo = -1;
    CellHistorySetting m_cellHistorySetting;
	ks_wstring m_fileId;
    double m_cfCalcTimeProbability;
    double m_cfCollectFmlaProbability;
    double m_collectProbability;
    double m_collectPasteProbability = 0;
    double m_coreMetricsProbability;
    uint m_cmdSerializeSizeLimit = 0;
    double m_collectFormulaComplexityProbability;
    double m_collectDBViewUpdateProbability = 0;
    INT m_copySpreadsheet6FormatMaxSize;
    unsigned int m_nTimeLimitSaveBreakSecdoc;
    unsigned int m_nSlaveTimeLimitSaveBreakSecdoc;
    unsigned int m_nTimeLimitSaveBreakGeneral;
    unsigned int m_nSlaveTimeLimitSaveBreakGeneral;
    int m_bEnableSubscriptionNotifyToUnoTag;
    int m_CommentRecordAddCmtJudgeTransact;
    INT m_IsOpenSetFileTagProtected;
    INT32 m_udfSingleRequestLimit = UDF_SINGLE_REQUEST_LIMIT;
    WoIoThreadMode m_openThreadMode;
    WoIoThreadMode m_atuoOpenThreadMode;
    int m_bEnableSecBreakIo;
    int m_openTime;
    int m_openBookCompileFml;
    int m_openSheetCompileFml;
    int m_openAllSheetsSize;
    int m_openSheetSize;
    int m_openBookFastThreshold;
    int m_openMinFastThreshold;
    int m_openMultiBySheet;
    int m_collectTimeThreshold;
    int m_collectDiffMemTime;
    int m_coreMetricsCollectThreshold;
    int m_cmSignalThreshold;
    unsigned int m_randSeed;
    int m_isSendCoreTime;
    int m_isBlockNonNeedRdView;
    int m_pasteProgressNotifyDuration;
    int m_isEnableMetricSignal;
    int m_minMetricSignalSz;
    int m_maxMetricSignalSz;
    int m_maxMetricSignalCnt;
    std::unique_ptr<util::RandDist> m_spCmdTimeRand;
    std::unique_ptr<util::RandDist> m_spQueryInitRand;
    std::unique_ptr<util::RandDist> m_spCFQueryTimeRand;
    std::unique_ptr<util::RandDist> m_spCFCalcTimeRand;
    std::unique_ptr<util::RandDist> m_spCFCollectFmlaRand;
    std::unique_ptr<util::RandDist> m_spApiRand;
    std::unique_ptr<util::RandDist> m_spCoreMetricsRand;
    std::unique_ptr<util::RandDist> m_spFormulaComplexityRand;
    std::unique_ptr<util::RandDist> m_spPasteRand;
    std::unique_ptr<util::RandDist> m_spDBViewUpdateTimeRand;
    int m_nIsEnableStaleNotify;
    int m_userConnTimeout;
    int m_clearUserConnCount;
    int m_maxClearUseConnCount;
    unsigned int m_maxClearUserConnTime;

    DOUBLE m_DocSlimBoundDensity;
    UINT64 m_nDocSlimDocNullCellsLimit;
    UINT32 m_nDocSlimDocInvisibleObjectTriggerValue;
    UINT32 m_nDocSlimDocUnreferencedPictureTriggerValue;
    UINT16 m_nDocSlimRowLowDensityToleranceIndex;
    UINT16 m_nDocSlimColLowDensityToleranceIndex;
    UINT16 m_nDocSlimBoundAdvance;
    UINT32 m_nDocSlimDocNullCellsTriggerValue;
    UINT32 m_nDocSlimOverlapShapesTriggerValue;
    UINT32 m_nDocSlimUnusedDuplicateStyleTriggerValue;
    UINT32 m_nDocSlimDuplicateFormatConditionTriggerValue;
    UINT32 m_nHtmlCopyMaxLimitSize;
    
    BOOL m_bEnableImportrangeFallback = FALSE;
    BOOL m_bCrossBookEmptyUserFallback = FALSE;
    BOOL m_bEnableAutoUpdateUserInfo = FALSE;
    double m_autoUpdateUserInfoTime = 1; //默认1h间隔自动请求更新文件中用户缓存信息

    BOOL m_bEnableParentRecord = FALSE;
    UINT8 m_maxParentRecordLevel = DEFAULT_MAX_PARENT_RECORD_LEVEL; 
    BOOL m_bEnableClearCurVersionCmd = FALSE;
	
	UINT16 m_nCustomListCacheMaxSizePerSheet;
	BOOL m_bEnableCustomListChangedNotify = FALSE;
    BOOL m_bEnableDbShareFmla = TRUE;       // DB支持共享公式开关
    UINT16 m_dbFunnelChartMaxLayerCount;
    UINT16 m_dbDashboardPluginMax;
    UINT16 m_dbDashboardMaxFilterCount;
    INT32 m_dbChartMaxConnCacheCount;
    UINT m_limitExclusiveRow = 10;
    UINT m_limitExclusiveCol = 10;
	UINT m_limitExclusiveCell = 100;
    BOOL m_bEnableExclusiveRange = TRUE;
    BOOL m_bEnableMannualCalcBreak = TRUE;
    BOOL m_bEnableStatictisSheet = TRUE;
    int m_bEnableReleaseConn;
    int m_delayUpdateDbChart;
    INT32 m_maxHookCacheSize = DEFAULT_MAX_HOOK_CACHE_SIZE;
    UINT8 m_maxSidebarFolderTreeLevel = DEFAULT_MAX_SIDEBAR_FOLDER_TREE_LEVEL;
    UINT8 m_maxSidebarFolderTreeFolderCnt = DEFAULT_MAX_SIDEBAR_FOLDER_TREE_FOLDER_CNT;
	BOOL m_bKeepDbLinkItemOrdered = FALSE;
    bool m_enabledCalcBreak = true; // 缺省启用
    BOOL m_enableRegexpParamExtType = TRUE;
    BOOL m_enableDynaArrEnumOpt = FALSE; //缺省禁用
	INT32 m_delaySerializeXfsInitSize = 512; // 延迟加载xfs的初始大小
    BOOL m_bNeedCheckAllSheetsPermission = FALSE;
    int m_nEnableGridSheetSyncSheet;
    BOOL m_bEnableDBResizeRowOpt = TRUE;
    BOOL m_bEnableOptimizeClearCell = TRUE;
    BOOL m_bDelOpDisableTransOpt = TRUE;
    BOOL m_bEt2DbEnableSupportImages = TRUE;

    int m_nEnableEventTrackingCollect;
    int m_nEnableDBViewUpdateCollect;

    UINT m_OpenTrackingThousandThreshold = 1000;
    UINT m_OpenTrackingOneThreshold = 5000;
    UINT m_nDashBoardSnapshotWidth = 0; // 仪表盘sheet导出node server截图宽度，为0时不截图。
    UINT m_nDashBoardSnapshotMinHeight = 1080; // 仪表盘sheet导出node server截图最小高度。

    INT32 m_nDbFilterTokenResCacheEnableThreshold;
	BOOL m_bBatchNotifyCBRInfo = TRUE;
    BOOL m_dbFieldRefEnabled {};
    BOOL m_bEnableVersionTag = FALSE;
    ks_wstring m_nConnID;

    BOOL m_enableOpenProgress = FALSE;
    UINT m_OpenProgressDozenThreshold = 3000;//3000ms
    UINT m_OpenProgressNotifyDuration = 200;//200ms
    int m_nTidyImagePoolThreshold = 1024 * 800; // 800K
    int m_nCollectCmdMemUsageThreshold = 0;

    BOOL m_bEnableTruncateOpen = FALSE;
    INT32 m_truncateRowOnFileOpen = 0;
    INT32 m_truncateColOnFileOpen = 0;

    UINT16 m_dbDashboardViewMax = 0;
    int m_nBanTracker = INVALID_VAL;
    std::pair<int, std::chrono::steady_clock::time_point> m_nEnableCommonLog {INVALID_VAL, std::chrono::steady_clock::time_point()};
    ks_string m_strCommonLogDir;
    int m_nCommonLogMaxEntrySize = INVALID_VAL;
};
}

#endif // __WOETSETTINGS_H__
