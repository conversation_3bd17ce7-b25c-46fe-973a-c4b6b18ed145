#include "etstdafx.h"
#include "etcore/little_alg.h"
#include "workbook.h"

#include <public_header/etcore/mvc/et_workbook_layer.h>
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include <public_header/opl/mvc/cellimage/et_cellimage_shape_tree.h>
#include <public_header/opl/mvc/cellimage/et_cellimage_shape.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include "et_revision_context_impl.h"
#include "src/dbsheet/et_dbsheet_utils.h"

#include "appcore/et_appcore_dbsheet.h"

#include "exportcensordata.h"

#include <public_header/chart/src/datasource/kctchartdatasource.h>
#include <public_header/chart/src/mvc/kctchartlayer.h>
#include <public_header/chart/src/model/kctchart.h>
#include <public_header/chart/src/model/kcttitle.h>
#include <public_header/chart/src/model/kctaxes.h>
#include <public_header/chart/src/model/kctaxis.h>
#include <public_header/drawing/text/data/abstract_text_stream.h>
#include <kfc/et_numfmt_str.h>
#include <io/iok/iostring.h>
#include <et_hard_define_strings.h>
#include <mvc/et_mvc.h>
#include "utils/attachment_utils.h"
#include "pivot_core/pivot_core_x.h"
#include <public_header/drawing/model/extdata.h>
#include <public_header/webcommon/src/urlutils.h>
#include "share_link_content_visibility_checker.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"
#include <webextension/webextension_i.h>
#include <public_header/drawing/webextension/datasource/kdatarange.h>
#include <json/value.h>
#include <json/reader.h>
#include <etcore/et_core_dbsheet_autolink.h>
#include "helpers/custom_storage_helper.h"
#include "util.h"

extern Callback* gs_callback;
namespace wo {

ExportCensorData::ExportCensorData(KEtWorkbook* pWorkbook, QIODevice* ioDevice, PCWSTR shareId, bool removeDu /* = false */)
	: m_spWorkbook(pWorkbook->GetCoreWorkbook())
	, m_QTextStream(ioDevice)
	, m_bRemoveDuplicates(removeDu)
    , m_shareId(shareId)
{
	_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStrTools);
}

HRESULT ExportCensorData::DoExportText(UINT sheetId)
{
	IKWorksheets* pWorksheets = m_spWorkbook->GetWorksheets();
	int cnt = pWorksheets->GetSheetCount();
	if(cnt <= 0)
	{
		return S_OK;
	}

    // shareId和sheetId不会同时有值
    if (!m_shareId.empty() && sheetId > 0)
        return E_FAIL;

    auto pVisibilityChecker = ShareLinkContentVisibilityChecker::CreateInstance(m_spWorkbook->GetBook(), m_shareId.c_str());

	// etDashBoard暂不支持DBDashBoardOp，仅在CollectCustomStorage导出标题相关信息送审。
	auto isEtDashBoardSheet = [] (IBook* pBook, UINT sheetId) -> bool
	{
		ks_stdptr<ISheet> spSheet;
		IDX sheetIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(sheetId, &sheetIdx);
		if (sheetIdx == INVALIDIDX)
			return false;
		pBook->GetSheet(sheetIdx, &spSheet);
		return spSheet && spSheet->IsDbDashBoardSheet() ? 
			!spSheet->GetBMP()->bKsheet && !spSheet->GetBMP()->bDbSheet : false;
	};

    if (sheetId > 0 && !isEtDashBoardSheet(m_spWorkbook->GetBook(), sheetId))
    {
        ASSERT(pVisibilityChecker == nullptr);
        pVisibilityChecker = ShareLinkContentVisibilityChecker::CreateInstance(m_spWorkbook->GetBook(), sheetId);
        if (!pVisibilityChecker)
            return E_FAIL;
    }

    ks_stdptr<ICoreDataDumper> ptrDumper;
    _etcore_CreateObject(CLSID_KCoreDataDumper, IID_ICoreDataDumper, (void**)&ptrDumper);
    COPYINFO cpinfo = {0};
    ptrDumper->Init(m_spWorkbook->GetBook(), INVALIDIDX, NULL, &cpinfo, CDF_BMP_OOXML, true);
    ptrDumper->CollectCoreData();

    for(int shtIdx = 0; shtIdx < cnt; shtIdx++)
	{
		ks_castptr<etoldapi::_Worksheet> spWorksheet = pWorksheets->GetSheetItem(shtIdx);
        UINT sheetId = spWorksheet->GetSheet()->GetStId();
        if (pVisibilityChecker && !pVisibilityChecker->IsSheetVisible(sheetId))
            continue;

        if (!pVisibilityChecker || pVisibilityChecker->IsSheetPropertyVisible(sheetId))
        {
            ks_bstr sheetName;
            spWorksheet->get_Name(&sheetName);
            if (xstrcmp(sheetName, STR_CELL_IMAGE_SHEET_NAME) == 0)
                continue;
            ExportString(sheetName);
        }

        auto checkCellVisible = [&pVisibilityChecker, sheetId](EtDbId fieldId, EtDbId recordId) {
            return !pVisibilityChecker || (pVisibilityChecker->IsFieldVisible(sheetId, fieldId) &&
                    pVisibilityChecker->IsRecordVisible(sheetId, recordId));
        };
        CollectCellData(spWorksheet, checkCellVisible);
        CollectDbSheet(spWorksheet, pVisibilityChecker.get());
        CollectDashBoardSheet(spWorksheet, pVisibilityChecker.get());
        if (!pVisibilityChecker)
        {
            CollectComments(spWorksheet);
            //CollectCommentsWithoutVoice(spWorksheet);
            CollectDataValidation(spWorksheet, ptrDumper);
			CollectPivotTable(spWorksheet);
            CollectHyperlinks(spWorksheet);
        }
	}
	CollectCustomStorage();
    if (!pVisibilityChecker)
    {
        CollectDefineName();
        CollectAllShapeText();
		CollectSidebarFolderTreeNames();
    }
	ExportContent();
	ptrDumper->ReleaseCollection();
	return S_OK;
}

ExportCensorData::EnumEtDbCellValueAllStrs::~EnumEtDbCellValueAllStrs()
{
	m_exporter = nullptr;
}

HRESULT ExportCensorData::CollectCellData(etoldapi::_Worksheet* pWorksheet,
                                          const std::function<bool(EtDbId,EtDbId)>& checkCellVisible)
{
	ks_stdptr<ISheet> spISheet = pWorksheet->GetSheet();
	IDX shtIdx = INVALIDIDX;
	spISheet->GetIndex(&shtIdx);
	RECT rcUsed = {0};
	spISheet->CalcUsedScale(&rcUsed);
	RANGE rgUsed = Rect2Range(rcUsed, shtIdx, spISheet->GetBMP());
	if(!rgUsed.IsValid())
	{
		return S_OK;
	}

	et_sdptr<ISheetEnum> spSheetEnum;
	spISheet->CreateEnum(&spSheetEnum);

	m_spStrTools->SetEnv(spISheet.get());
	IBookOp* pBookOp = pWorksheet->GetWorkbook()->GetBook()->LeakOperator();

	if (spISheet->IsDbSheet())
	{
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		VS(DbSheet::GetDBSheetOp(spISheet.get(), &spDbSheetOp));

        EnumDbSheetCellValueAllStrs strsCollector(this, pBookOp, shtIdx, spDbSheetOp.get(), checkCellVisible);
		spSheetEnum->EnumCellValueRowbyRow(rgUsed, &strsCollector);
	}
	else
	{
		EnumEtCellValueAllStrs strsCollector(this, pBookOp, shtIdx);
		spSheetEnum->EnumCellValueRowbyRow(rgUsed, &strsCollector);
	}

	return S_OK;
}

inline bool GetValidationRangesLT(IKRanges* pRanges, ROW* pRow, COL* pCol, BMP_PTR bp)
{
	UINT count = 0;
	VS(pRanges->GetCount(&count));
	if (count == 0)
		return false;
	const RANGE* pRange;
	ROW row = bp->cntRows; COL col = bp->cntCols;
	for (UINT i = 0; i < count; ++i)
	{
		pRanges->GetItem(i, NULL, &pRange);
		if (pRange->RowFrom() < row)
			row = pRange->RowFrom();
		if (pRange->ColFrom() < col)
			col = pRange->ColFrom();
	}
	*pRow = row;
	*pCol = col;
	return true;
}

inline void GetCondFmtRangesLT(RECT* pRect, UINT nRgs, ROW* pRow, COL* pCol,BMP_PTR pBMP)
{
	ROW row = pBMP->cntRows;
	COL col = pBMP->cntCols;
	for (UINT i = 0; i < nRgs; ++i)
	{
		if (pRect[i].top < row)
			row = pRect[i].top;
		if (pRect[i].left < col)
			col = pRect[i].left;
	}
	*pRow = row;
	*pCol = col;
}

inline bool TryGetFmlaVstrText(const_token_vector pIns, ks_wstring& result)
{
	exec_token_vector tokvec(pIns);
	if (1 != tokvec.size() || alg::ETP_VSTR != alg::GetExecTokenMajorType(tokvec[0]))
		return false;

	result.clear();
	alg::const_vstr_token_assist vstrTok(tokvec[0]);
	result.assign(vstrTok.get_value(), vstrTok.get_length());
	return true;
}

HRESULT ExportCensorData::CollectDataValidation(etoldapi::_Worksheet *pWorksheet, ICoreDataDumper*ptrDumper)
{
	ks_stdptr<ISheet> spISheet = pWorksheet->GetSheet();
	IDX shtIdx = INVALIDIDX;
	spISheet->GetIndex(&shtIdx);
    
	if (ptrDumper)
	{
        //导出数据有效性条件和提示
        UINT dvCount = ptrDumper->GetDVCount(shtIdx);
		for (size_t i = 0; i < dvCount; i++)
		{
			_DVINFO dvInfo;
			dvInfo.Init();
			ptrDumper->DVGetCriteria(shtIdx, i, &dvInfo);	
			
			if(dvInfo.spFormula1 || dvInfo.spFormula2)
			{
				ks_stdptr<IKRanges> ptrRanges;
				ptrDumper->DVGetRanges(shtIdx, i, &ptrRanges);
				ROW rangesTop;
				COL rangesLeft;
				GetValidationRangesLT(ptrRanges,&rangesTop,&rangesLeft,pWorksheet->GetSheet()->GetBMP());
				ks_bstr bstrFormula1,bstrFormula2;
				CS_COMPILE_PARAM  prm;
				prm.nSheet = shtIdx;
				prm.nRow = rangesTop;
				prm.nCol = rangesLeft;
				if(dvInfo.spFormula1)
				{
					dvInfo.spFormula1->GetFormula(&bstrFormula1,prm);
					ExportString(bstrFormula1);
				}
				if (dvInfo.spFormula2)
				{
					dvInfo.spFormula2->GetFormula(&bstrFormula2,prm);
					ExportString(bstrFormula2);
				}
			}

			ExportString(dvInfo.pcwcsInputTitle);
			ExportString(dvInfo.pcwcsInputText);
			ExportString(dvInfo.pcwcsErrorTitle);
			ExportString(dvInfo.pcwcsErrorText);		
		}

		//导出条件格式
		RECT* pRects = NULL;
		UINT nRects = 0;
		CONDFMT* pCondFmts = NULL;
		UINT nCondFmts = 0;
		HRESULT hr = ptrDumper->GetFirstCondFmt(shtIdx, &pRects, &nRects, &pCondFmts, &nCondFmts, FALSE);
		while (SUCCEEDED(hr) && hr != S_FALSE)
		{
			CELL lt = {0};
			GetCondFmtRangesLT(pRects, nRects, &lt.row, &lt.col, pWorksheet->GetSheet()->GetBMP());

			for (UINT i = 0; i < nCondFmts; ++i)
			{
				if (pCondFmts[i].cft == CFT_TEXT && pCondFmts[i].etv2 != NULL && pCondFmts[i].etv2.size() > 0)
				{
					ks_wstring strFmla;
					TryGetFmlaVstrText(pCondFmts[i].etv2, strFmla);
					ExportString(strFmla.c_str());
				}
				else if (pCondFmts[i].cft == CFT_CELLIS && pCondFmts[i].etv1 != NULL && pCondFmts[i].etv1.size() > 0)
				{
					ks_wstring strFmla;
					TryGetFmlaVstrText(pCondFmts[i].etv1, strFmla);
					ExportString(strFmla.c_str());
				}
			}
			delete []pRects;
			pRects = NULL;
			nRects = 0;
			delete []pCondFmts;
			pCondFmts = NULL;
			nCondFmts = 0;
			hr = ptrDumper->GetNextCondFmt(&pRects, &nRects, &pCondFmts, &nCondFmts);
		}
	
	}

	return S_OK;
}

HRESULT ExportCensorData::CollectPivotTable(etoldapi::_Worksheet *pWorksheet)
{	
	// 导出数据透视表
	ks_stdptr<ISheet> spISheet = pWorksheet->GetSheet();
	ks_stdptr<pivot_core::IPivotTables> spCorePivotTables;
	pivot_core::GetPivotTablesFromSheet(spISheet, &spCorePivotTables);
	if (!spCorePivotTables)
		return S_OK;
	UINT pvtTabcnt = spCorePivotTables->Count();
	for (UINT i = 0; i < pvtTabcnt; i++)
	{
		pivot_core::IPivotTable* pCorePivotTable = spCorePivotTables->Item(i);
		if (!pCorePivotTable)
			continue;
		ks_stdptr<etoldapi::PivotTable> spPivotTable;
		pWorksheet->GainPivotTable(pCorePivotTable, &spPivotTable);
		if (!spPivotTable)
			continue;

		ks_bstr bstrError, bstrNull;
		if (SUCCEEDED(spPivotTable->get_ErrorString(&bstrError)) && !bstrError.empty())
			ExportString(bstrError.c_str());
		if (SUCCEEDED(spPivotTable->get_NullString(&bstrNull)) && !bstrNull.empty())
			ExportString(bstrNull.c_str());

		ks_stdptr<etoldapi::CalculatedFields> spCalculatedFields;
		spPivotTable->CalculatedFields(&spCalculatedFields);
		if (!spCalculatedFields)
			continue;

		long nCount = 0;
		spCalculatedFields->get_Count(&nCount);
		for (long i = 1; i <= nCount; ++i)
		{
			ks_bstr bstrName, bstrFmla;
			ks_stdptr<etoldapi::PivotField> spPivotField;
			spCalculatedFields->Item(KComVariant(i), &spPivotField);
			if (!spPivotField)
				continue;
			spPivotField->get_Name(&bstrName);
			spPivotField->get_Formula(&bstrFmla);
			if (!bstrName.empty())
				ExportString(bstrName.c_str());
			if (!bstrFmla.empty())
				ExportString(bstrFmla.c_str());
		}	
	}

	return S_OK;
}

HRESULT ExportCensorData::CollectHyperlinks(etoldapi::_Worksheet *pWorksheet)
{
	ks_stdptr<Hyperlinks> spHyperlinks;
	pWorksheet->get_Hyperlinks(&spHyperlinks);
	if (spHyperlinks)
	{
		long cntLinks = 0;
		spHyperlinks->get_Count(&cntLinks);

		for (long i = 1; i <= cntLinks; i++)
		{
			ks_stdptr<Hyperlink> tempLink;
			spHyperlinks->get_Item(KComVariant(i, VT_I4), &tempLink);

			ks_bstr bstrCellAddress;
			tempLink->get_Address(&bstrCellAddress);
			if(!bstrCellAddress.empty())
				ExportString(bstrCellAddress);

			ks_bstr bstrCellDisplay;
			tempLink->get_TextToDisplay(&bstrCellDisplay);
			if(!bstrCellDisplay.empty())
				ExportString(bstrCellDisplay);

			ks_bstr bstrCellSubAddress;
			tempLink->get_SubAddress(&bstrCellSubAddress);
			if(!bstrCellSubAddress.empty())
				ExportString(bstrCellSubAddress);

			ks_bstr bstrCellSubject;
			tempLink->get_EmailSubject(&bstrCellSubject);
			if(!bstrCellSubject.empty())
				ExportString(bstrCellSubject);
		}
	}
	return S_OK;
}

HRESULT ExportCensorData::CollectComments(etoldapi::_Worksheet *pWorksheet)
{
	ks_stdptr<ISheet> spISheet = pWorksheet->GetSheet();

	ks_stdptr<IKDrawingCanvas> spCommentCanvas;

	ks_stdptr<IUnknown> spUnk;
	if (SUCCEEDED(spISheet->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk)))
		spUnk->QueryInterface(IID_IKDrawingCanvas, (void**)&spCommentCanvas);

	if (!spCommentCanvas)
		return S_OK;

	ks_stdptr<ICellComments> spCMTs = spCommentCanvas;

	INT numCellComment = 0;
	spCMTs->GetCount(&numCellComment);
	for (INT i = 0; i < numCellComment; i++)
	{
		ks_stdptr<ICellComment> spICellComment;
		spCMTs->GetItem(i, &spICellComment);
		ASSERT(spICellComment);

		ks_bstr bstrCellComment;
		spICellComment->GetContent(&bstrCellComment);
		if(bstrCellComment.empty()) continue;

		ExportString(bstrCellComment);
	}
	return S_OK;
}

HRESULT ExportCensorData::CollectCommentsWithoutVoice(etoldapi::_Worksheet *pWorksheet)
{
	ks_stdptr<ISheet> spISheet = pWorksheet->GetSheet();
	ks_stdptr<IKDrawingCanvas> spCommentCanvas;
	ks_stdptr<IUnknown> spUnk;
	if (SUCCEEDED(spISheet->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk)))
		spUnk->QueryInterface(IID_IKDrawingCanvas, (void**)&spCommentCanvas);
	if (!spCommentCanvas)
		return S_OK;

	INT cmtCnt;
	ks_stdptr<ICellComments> spCMTs = spCommentCanvas;
	spCMTs->GetCount(&cmtCnt);
	for (INT cmtIdx = 0; cmtIdx < cmtCnt; cmtIdx++)
	{
		ks_stdptr<ICellComment> spCellCmt;
		spCMTs->GetItem(cmtIdx, &spCellCmt);
		if(!spCellCmt) continue;

		ks_stdptr<IWoComment> spWoCmt;
		spCellCmt->GetWoComment(&spWoCmt);
		if(!spWoCmt) continue;

		auto exportCommentFromChains = [&](bool bIsResolved)
		{
			size_t chainCount = spWoCmt->GetChainCount(bIsResolved);
			for (size_t i = 0; i < chainCount; ++i)
			{
				IWoCommentChain* pWoCommentChain = spWoCmt->GetChainByIndex(bIsResolved, i);
				size_t itemCount = pWoCommentChain->Count();
				for (size_t j = 0; j < itemCount; ++j)
				{
					IWoCommentItem* pItem = pWoCommentChain->GetItem(j);
					INT32 voiceDuration = pItem->GetVoiceDuration();
					if (0 < voiceDuration) continue;

					ExportString(pItem->GetUserName());
					ExportString(pItem->GetText());
				}
			}
		};

		exportCommentFromChains(false);
		exportCommentFromChains(true);
	}
	return S_OK;
}

void ExportCensorData::CollectWebExtension(IKWebExtension* pWebExtension)
{
    ks_stdptr<IKWebExtensionPropertyEnum> spPropertyEnum;
    if (SUCCEEDED(pWebExtension->GetPropertyEnumerator(&spPropertyEnum)) && spPropertyEnum && S_OK == spPropertyEnum->Reset())
    {
        do
        {
            LPCWSTR pKey = spPropertyEnum->GetCurKey();
            ExportString(pKey);

            LPCWSTR pValue = spPropertyEnum->GetCurValue();
            ExportString(pValue);
        }while (SUCCEEDED(spPropertyEnum->Next()));
    }
    if (!pWebExtension->IsDatasourceSupportingDashboardModule())
        return;

    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
    if (!DbDashboard::IsEtDataSourceType(dataSourceType))
        return;

    ks_castptr<IKWorkbookDataSource> spDataSource = spDataSourceHost->GetDataSource();
    ks_stdptr<IKDataRangeEnum> spDataRangeEnum;
    if (SUCCEEDED(spDataSource->GetDataRangeEnumerator(&spDataRangeEnum)) && spDataRangeEnum && S_OK == spDataRangeEnum->Reset())
    {
        do
        {
            ks_castptr<KDataRange> pDataRange = spDataRangeEnum->GetDataRange();
            CollectDataRange(pDataRange);
        } while (SUCCEEDED(spDataRangeEnum->Next()));
    }
}

void ExportCensorData::CollectRichTextContent(IKWebExtension* pWebExtension)
{
	LPCWSTR pValue = nullptr;
	pWebExtension->GetProperty(__X("richTextContent"), &pValue);

	Json::Value jsonObject;
	if (!Json::Reader().parse(krt::fromUtf16(pValue).toUtf8().data(), jsonObject) || !jsonObject.isObject())
		return;

	std::stack<const Json::Value*> objStack;
	std::stack<const Json::Value*> textStack;
	objStack.push(&jsonObject);
	while (!objStack.empty())
	{
		const Json::Value& currentObj = *objStack.top();
        objStack.pop();

		const Json::Value& typeObj = currentObj["type"];
		if (typeObj.isString())
		{
			std::string text = typeObj.asString();
			if (text == "text" || text == "kdocsLink")
			{
				textStack.push(&currentObj);
				continue;
			}
		}

		const Json::Value& contentArr = currentObj["content"];
		if (!contentArr.isArray())
			continue;

		for (const auto& obj : contentArr)
		{
			if (obj.isObject())
				objStack.push(&obj);
		}
	}

	while (!textStack.empty())
	{
		const Json::Value& currentObj = *textStack.top();
        textStack.pop();

		std::string contextText;
		std::string type = currentObj["type"].asString();
		if (type == "text")
		{
			const Json::Value& textObj = currentObj["text"];
			if (textObj.isString())
				contextText = textObj.asString();
		}
		else if (type == "kdocsLink")
		{
			const Json::Value& attrsObj = currentObj["attrs"];
			if (attrsObj.isObject())
			{
				const Json::Value& nameObj = attrsObj["name"];
				const Json::Value& linkObj = attrsObj["link"];
				if (nameObj.isString() && linkObj.isString())
					contextText = nameObj.asString() + linkObj.asString();
			}
		}
		if (!contextText.empty())
			ExportString(krt::utf16(QString::fromUtf8(contextText.c_str())));
	}
}

void ExportCensorData::CollectDataRange(KDataRange* pDataRange)
{
    webextension::KWEMultiSglCells* pWECells = pDataRange->GetCells();
    if (!pWECells)
        return;

    BOOL b1904 = pDataRange->Get1904DateSystem();
    size_t rowCount = pWECells->GetCount();
    for (size_t row = 0; row < rowCount; ++row)
    {
        const webextension::KWESglCells* pRowCells = pWECells->GetItem(row);
        if (!pRowCells)
            continue;

        size_t colCount = pRowCells->GetCount();
        for (size_t col = 0; col < colCount; ++col)
        {
            const webextension::KWECell* pCell = pRowCells->GetItem(col);
            if (!pCell)
                continue;

            QString cellText = pCell->GetFormatedQString(b1904);
            ExportString(krt::utf16(cellText));
        }
    }
}

HRESULT ExportCensorData::CollectDbFilterCriteria(const IDbFilterCriteria* pCriteria)
{
    UINT valueCount = pCriteria->GetValuesCount();
    for (UINT i = 0; i < valueCount; ++i)
    {
        const IDbFcValueBase* pValue = pCriteria->GetValue(i);
        KDbFcValueType valueType = pValue->GetType();
        switch (valueType)
        {
            case DBFCVT_Text:
            {
                auto* pTextValue = static_cast<const IDbFcValueText*>(pValue);
                ExportString(pTextValue->GetValue());
                break;
            }
            case DBFCVT_Contact:
            {
                auto* pContactValue = static_cast<const IDbFcValueContact*>(pValue);
                ExportString(pContactValue->GetValue());
                break;
            }
            case DBFCVT_Link:
            {
                auto* pLinkValue = static_cast<const IDbFcValueLink*>(pValue);
                ExportString(pLinkValue->GetContent());
                break;
            }
        }
    }
    return S_OK;
}

HRESULT ExportCensorData::CollectDbFilter(const IDbFilter* pFilter)
{
    UINT filterCount = pFilter->GetFiltersCount();
    for (UINT i = 0; i < filterCount; ++i)
    {
        const IDbFieldFilter* pFieldFilter = nullptr;
        pFilter->GetFilter(i, &pFieldFilter);
        if (!pFieldFilter)
            continue;

        const IDbFilterCriteria* pCriteria = pFieldFilter->GetCriteria();
        if (!pCriteria)
            continue;

        HRESULT hr = CollectDbFilterCriteria(pCriteria);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT ExportCensorData::CollectCustomStorage()
{
	static const PCWSTR sNeedCensorKeyArr[] = { __X("dashboardTitle"), __X("dashboardSubTitle") };

	IBook* pBook = m_spWorkbook->GetBook();
	INT sheetCount = 0;
	pBook->GetSheetCount(&sheetCount);
	if (sheetCount == 0)
		return S_OK;

	ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    pBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    if (!spCustomStorMgr)
        return E_FAIL;

	for (int i = 0; i < sheetCount; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(i, &spSheet);
		if (!spSheet || !spSheet->IsDbDashBoardSheet())
			continue;

		for (int j = 0; j < countof(sNeedCensorKeyArr); ++j)
		{
			PCWSTR pValue = spCustomStorMgr->GetValue(spSheet->GetStId(), sNeedCensorKeyArr[j]);
			if (!pValue)
				continue;
			ExportString(pValue);
		}
	}
	return S_OK;
}

HRESULT ExportCensorData::CollectDashBoardSheet(etoldapi::_Worksheet *pWorksheet, ShareLinkContentVisibilityChecker* pVisibilityChecker)
{
    ks_stdptr<ISheet> spSheet = pWorksheet->GetSheet();
    if (FALSE == spSheet->IsDbDashBoardSheet())
        return S_OK;

	bool isETDashBorad = !spSheet->GetBMP()->bKsheet && !spSheet->GetBMP()->bDbSheet;
    ks_stdptr<IDBDashBoardDataOp> spDbDashBoard;
    DbSheet::GetDBDashBoardOp(spSheet.get(), &spDbDashBoard);

	if(!spDbDashBoard && !isETDashBorad)
		return S_OK;
	
    // 分享仪表盘不导出仪表盘说明
    if (spDbDashBoard && !pVisibilityChecker)
        ExportString(spDbDashBoard->GetSheetDescription());


    IKWebExtensionMgr* pWebExtensionMgr = m_spWorkbook->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return S_OK;

    UINT webExtensionCount = 0;
    pWebExtensionMgr->GetWebExtensionCount(spSheet, &webExtensionCount);
    for (UINT i = 0; i < webExtensionCount; ++ i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        HRESULT hr = pWebExtensionMgr->GetWebExtension(spSheet, i, &spWebExtension);
        if (FAILED(hr))
            return hr;

		if (spWebExtension->GetWebShapeType() == WET_DbRichText)
			CollectRichTextContent(spWebExtension);
		else
        	CollectWebExtension(spWebExtension);
    }
	if (!spDbDashBoard)
		return S_OK;

    IDBChartStatisticMgr* pChartStatisticMgr = spDbDashBoard->GetChartStatisticMgr();
    for (EtDbIdx i = 0, cnt = pChartStatisticMgr->GetSize(); i < cnt; ++i)
    {
        ks_stdptr<IDBChartStatisticModule> spModule;
        pChartStatisticMgr->GetItemAt(i, &spModule);
        if (!spModule)
            continue;

        const IDbFilter* pFilter = spModule->GetConstFilter();
        if (pFilter)
        {
            HRESULT hr = CollectDbFilter(pFilter);
            if (FAILED(hr))
                return hr;
        }
    }

    IDbDashboardFilterMgr* pFilterMgr = spDbDashBoard->GetFilterMgr();
    for (UINT i = 0, count = pFilterMgr->GetCount(); i < count; ++i)
    {
        ks_stdptr<IDbDashboardFilter> spDashboardFilter;
        pFilterMgr->GetFilterAt(i, &spDashboardFilter);
        ExportString(spDashboardFilter->GetName());
    }
    return S_OK;
}

HRESULT ExportCensorData::CollectDbSheet(etoldapi::_Worksheet* pWorksheet, ShareLinkContentVisibilityChecker* pVisibilityChecker)
{
	ks_stdptr<ISheet> spSheet = pWorksheet->GetSheet();
    UINT sheetId =spSheet->GetStId();
	if (FALSE == spSheet->IsDbSheet())
		return S_OK;

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	VS(DbSheet::GetDBSheetViews(spSheet.get(), &spDbSheetViews));
	if (spDbSheetViews != nullptr)
	{
		ks_stdptr<IDBSheetViewsEnum> spEnum;
		if (SUCCEEDED(spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
		{
			do
			{
				ks_stdptr<IDBSheetView> spDbSheetView;
				spEnum->GetCurView(&spDbSheetView);
				if (!spDbSheetView)
					continue;

				if (pVisibilityChecker && !pVisibilityChecker->IsSheetViewVisible(sheetId, spDbSheetView->GetId()))
                	continue;

				ExportString(spDbSheetView->GetName());

				//送审视图内部的配置。
				CollectViewInnerSetting(spDbSheetView);

				// 分享视图不导出视图说明
				if (!pVisibilityChecker)
					ExportString(spDbSheetView->GetDescription());
				ExportString(spDbSheetView->GetNotice());
				const IDbFilter* pFilter = spDbSheetView->GetConstFilter();
				if (pFilter)
				{
					HRESULT hr = CollectDbFilter(pFilter);
					if (FAILED(hr))
						return hr;
				}
			}while (SUCCEEDED(spEnum->Next()));
		}
	}

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	VS(DbSheet::GetDBSheetOp(spSheet.get(), &spDbSheetOp));
	if (spDbSheetOp != nullptr)
	{
        if (!pVisibilityChecker || pVisibilityChecker->IsSheetPropertyVisible(sheetId))
        {
            ExportString(spDbSheetOp->GetSheetDescription());
        }
		IDbFieldsManager *pFieldManager = spDbSheetOp->GetFieldsManager();
		ASSERT(pFieldManager);
        const IDBIds* pFieldIds = spDbSheetOp->GetAllFields();
		ASSERT(pFieldIds);
		for (EtDbIdx i = 0; i < pFieldIds->Count(); i++)
		{
			EtDbId fldId = pFieldIds->IdAt(i);
            if (pVisibilityChecker && !pVisibilityChecker->IsFieldVisible(sheetId, fldId))
                continue;

			ks_stdptr<IDbField> spField;
			pFieldManager->GetField(fldId, &spField);
			ASSERT(spField);
			ExportString(spField->GetName());
			ExportString(spField->GetDescription());
			ExportString(spField->GetCustomConfig());
			switch (spField->GetType())
			{
			case Et_DbSheetField_Url:
			{
				ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = spField;
				ExportString(spFieldHyperlink->GetDisplayText());
				break;
			}
			case Et_DbSheetField_SingleSelect:
			case Et_DbSheetField_MultipleSelect:
			{
				ks_stdptr<IDbField_Select> spFieldSingleSelect = spField;
				PCWSTR item = nullptr;
				for (UINT j = 0, cnt = spFieldSingleSelect->Count(); j < cnt; ++j)
				{
					VS(spFieldSingleSelect->Item(j, &item, nullptr, nullptr));
					ExportString(item);
				}
				break;
			}
			case Et_DbSheetField_Button:
			{
				ks_stdptr<IDbField_Button> spFieldButton = spField;
				ExportString(spFieldButton->GetButtonText());
				ExportString(spFieldButton->GetSuccessText());
				break;
			}
			case Et_DbSheetField_Link:
			case Et_DbSheetField_OneWayLink:
			{
				// 自动匹配条件作为单元格公式已导出送审，这里只处理自定义关联配置
				ks_stdptr<IDbField_Link> spFieldLink = spField;
				if (spFieldLink->IsLinkCustomConfig())
				{
					ks_stdptr<IDbLinkCustomConfig> spLinkCustomConfig;
					spFieldLink->GetLinkCustomConfig(&spLinkCustomConfig);
					CollectDbAutoLinkCondProps(spLinkCustomConfig->GetCondGroups());
				}
				break;
			}
			default:
				break;
			}
		}
	}
	return S_OK;
}

void ExportCensorData::CollectDbAutoLinkCondProps(const IDbAutolinkCondProps* pProps)
{
	if (!pProps)
		return;

	size_t condGroupCnt = 0;
	const size_t* condGroupSizes = nullptr;
	pProps->GetConditionGroupArray(&condGroupSizes, &condGroupCnt);
	size_t processedCondCnt = 0;
	for (size_t groupIdx = 0; groupIdx < condGroupCnt; ++groupIdx)
	{
		const size_t condGroupSize = condGroupSizes[groupIdx];
		for (size_t condIdx = 0; condIdx < condGroupSize; ++condIdx)
		{
			const IDbAutolinkCondProp* pProp = pProps->Get(processedCondCnt + condIdx);
			if (!pProp || pProp->GetCurSheetCondType() != ET_DBSheet_Autolink_CondType::Literal || !pProp->IsTransferredString())
				continue;

			ExportString(pProp->GetTransferredString());
		}
		processedCondCnt += condGroupSize;
	}
}

HRESULT ExportCensorData::CollectDefineName()
{
	HRESULT hr = S_FALSE;
	IBook* pBook = m_spWorkbook->GetBook();
	ks_stdptr<IBookOp> spBookOp;
	pBook->GetOperator(&spBookOp);
	if(!spBookOp)
		return hr;
	INT count = 0;
	spBookOp->GetNameUdfCount(&count);
	ks_bstr name;

	PCWSTR strComment = nullptr;
	for (INT idx = 0; idx < count; idx++)
	{
		spBookOp->GetDefinedNameFullName(idx,&name);
		ExportString(name);			
		spBookOp->GetDefineNameComment(idx,&strComment);
		ExportString(strComment);
	}

    //自定义数字格式
	ks_stdptr<INumberFmts> spNumFmts;
    pBook->GetNumberFmts(&spNumFmts);
	if(spNumFmts)
	{
		std::set<WStr> defNumFmts;
		defNumFmts.insert(__X("mm:ss"));
		defNumFmts.insert(__X("[h]:mm:ss"));
		defNumFmts.insert(__X("¥#,##0;¥-#,##0"));
		defNumFmts.insert(__X("¥#,##0;[红色]¥-#,##0"));
		defNumFmts.insert(__X("¥#,##0.00;¥-#,##0.00"));
		defNumFmts.insert(__X("¥#,##0.00;[红色]¥-#,##0.00"));
		defNumFmts.insert(__X(R"(_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_))"));
		defNumFmts.insert(__X(R"(_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_))"));
		for (size_t i = 0; i < EtNFIndex_Count; i++)
		{
			defNumFmts.insert(kfc::nf::_XNFGetEtStr(i));		
		}
	
		const NUMFMT * numfmt = nullptr;
		spNumFmts->ResetEnum();
		while (S_OK == spNumFmts->Next(&numfmt))
		{			
			if (defNumFmts.find((WStr)numfmt->fmt) == defNumFmts.end())
			{
				ExportString(numfmt->fmt);
			}			
		}
	}

	
	return S_OK;
}

HRESULT ExportCensorData::CollectAllShapeText()
{
	ks_stdptr<IKWorksheets> ptrWorksheets = m_spWorkbook->GetWorksheets();
	int sheetCnt = ptrWorksheets->GetSheetCount();
	for(int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
	{
		ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
		if (!ptrWorksheet)
			continue;
		ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
		oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
		if (!spCanvas)
			continue;
		ks_castptr<EtShapeTree> pshapeTree = spCanvas;
		if(!pshapeTree) continue;

		walkThroughTree(pshapeTree);
	}


	return S_OK;
}

const ks_wstring guidQRCode(__X("44B7C0F4-79DB-4F8B-9303-0E098D69D8BE"));
const ks_wstring guidBarCode(__X("11875FA4-B263-4FB5-9052-7E12DCA7BDF3"));

void ExportCensorData::CollectShapeExtData(drawing::AbstractShape* pShape)
{
	const drawing::ExtData& extData = pShape->extData();
	const ks_wstring data = extData->extData();
	if (data.empty())
		return;
	const ks_wstring type = extData->extType();

	QString qData = QString::fromUtf16(data.c_str());
	QByteArray jsonData = QByteArray::fromBase64(qData.toUtf8());
	QJsonDocument doc = QJsonDocument::fromJson(jsonData);
	QJsonObject obj = doc.object();

	if (type == guidQRCode)
		CollectQRCodeText(obj);
	else if (type == guidBarCode)
		CollectBarCodeText(obj);
}

void ExportCensorData::CollectQRCodeText(const QJsonObject& obj)
{
	if (!obj.contains("LastUrl"))
		return;

	QString originUrl = obj.value("LastUrl").toString();
	std::unordered_map<std::string, std::string> parameters = GetUrlParameters(originUrl.toStdString());
	auto iter = parameters.find("text");
	if (iter == parameters.end())
		return;
	QString QRCodeText = QString::fromStdString(iter->second);
	ExportString(krt::utf16(QRCodeText));
}

void ExportCensorData::CollectBarCodeText(const QJsonObject& obj)
{
	if (!obj.contains("Text"))
		return;

	QString barCodeText = obj.value("Text").toString();
	ExportString(krt::utf16(barCodeText));
}

void ExportCensorData::walkThroughTree(drawing::AbstractShape* shape)
{
	if(!shape) return;

	if(shape->isGroupShape())
	{
		drawing::GroupShape* shapeGp = static_cast<drawing::GroupShape*>(shape);
		int childCnt = shapeGp->childCount();
		for (int childIdx = 0; childIdx < childCnt; childIdx++)
		{
			drawing::AbstractShape* childShape = shapeGp->childAt(childIdx);
			walkThroughTree(childShape);
		}
	}
	else
	{
		if (shape->hasExtData())
			CollectShapeExtData(shape);

		if(shape->hasChart())
		{
			hanldeChart(shape);
		}
		else 
		{
			ks_stdptr<IKTextFrame> spTextFrame;
			shape->GetTextFrame(&spTextFrame);
			if (!spTextFrame) 
				return;
			ks_castptr<AbstractTextStream> cpStream = spTextFrame->GetTextStream();
			if (!cpStream) 
				return;
			ExportString(krt::utf16(cpStream->text()));
		}
		
	}
}

void ExportCensorData::hanldeChart(drawing::AbstractShape* shape)
{
	ks_castptr<chart::KCTChartLayer> chartLayer = shape->getChild(0);
	if (!chartLayer) 
		return;

	chart::KCTChart* pChart = chartLayer->ensureChart();
	if (!pChart)
		return;

	QString strText ;
	chart::KCTChartTitle* pTitle = pChart->chartTitle();
	if (pTitle) {
		strText += pTitle->text() + "\r\n";
	}

	chart::KCTAxes* pAxes = pChart->axes();
	if (pAxes) {
		size_t count = pAxes->count();
		for (size_t i = 0; i < count; ++i) {
			chart::KCTAxis* pAxis = pAxes->itemAtIndex(i);
			if (pAxis) {
				chart::KCTAxisTitle* pAxisTitle = pAxis->title();
				if (pAxisTitle) {
					strText += pAxisTitle->text() + "\r\n";
				}
			}
		}
	}

	ExportString(krt::utf16(strText));
}

void ExportCensorData::ExportString(PCWSTR str)
{
	if (str == NULL || str[0] == __Xc('\0'))
		return;

	if(m_bRemoveDuplicates)
		insertMSRHandle(alg::msrIdentifyStringResource(str));
	else
		m_QTextStream << QString::fromUtf16(str) << " ";
}

HRESULT ExportCensorData::ExportContent()
{
	if(m_bRemoveDuplicates)
	{
		auto itor = m_MsrHandles.begin();
		for (; itor != m_MsrHandles.end(); itor++)
		{
			m_QTextStream << QString::fromUtf16(alg::msrGetStringResourceValue(*itor)) << " ";
			alg::msrUnreferStringResource(*itor);
		}
		m_MsrHandles.clear();
	}
	return S_OK;
}

HRESULT ExportCensorData::CollectViewInnerSetting(IDBSheetView* pView)
{
	if(pView->GetType() == et_DBSheetView_Query)
	{
		class QueryViewExportCensorDataEnum : public IDbFieldIdEnum
		{
		public:
			QueryViewExportCensorDataEnum(ExportCensorData* exportWorker) : m_exportWorker(exportWorker) {}
			STDPROC Do(EtDbId id, KDbFilterCriteriaOpType tp, PCWSTR customPrompt, 
				BOOL bEnableScanCodeToInput, BOOL bConditionCanBlank, BOOL bNeedSecondCheck) override
			{
				m_exportWorker->ExportString(customPrompt);
				return S_OK;
			}
		private:
			ExportCensorData* m_exportWorker;

		};
		
		ks_stdptr<IDBSheetView_Query> spQueryView = pView;
		QueryViewExportCensorDataEnum queryViewEnum(this);
		spQueryView->EnumFieldIds(&queryViewEnum);
	}
	return S_OK;
}

HRESULT ExportCensorData::CollectSidebarFolderTreeNames()
{
	IBook* pBook = m_spWorkbook->GetBook();
	if (!pBook)
		return E_FAIL;

	ks_stdptr<IDbSidebarFolderTreeManager> spMgr;
	pBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spMgr);
	if (!spMgr)
		return E_FAIL;

	class DbSidebarCensorEnum : public IDbSidebarIoEnum
	{
		using CallBackBegin = std::function<HRESULT(EtDbId, DbSidebarFolderTreeType, UINT, PCWSTR)>;
		using CallBackEnd = std::function<HRESULT(UINT)>;
	public:
		DbSidebarCensorEnum(const CallBackBegin &cbBegin, const CallBackEnd &cbEnd)
			: m_cbBegin(cbBegin)
			, m_cbEnd(cbEnd)
		{}

		STDPROC DoIoBegin(EtDbId id, DbSidebarFolderTreeType type, UINT stId, PCWSTR name) override
		{
			return m_cbBegin(id, type, stId, name);
		}

		STDPROC DoIoEnd(UINT stId) override
		{
			return m_cbEnd(stId);
		}
	private:
		const CallBackBegin m_cbBegin;
		const CallBackEnd m_cbEnd;
	};

	DbSidebarCensorEnum sidebarCensorEnum([this](EtDbId id, DbSidebarFolderTreeType type, UINT stId, PCWSTR name) -> HRESULT {
		ExportString(name);
		return S_OK;
	}, 
	[](UINT stId) -> HRESULT {
		// Do nothing
		return S_OK;
	});

	return spMgr->EnumDbSidebarForIo(&sidebarCensorEnum);
}

// -----------------------ExportAllImages------------------- //

ExportAllImages::ExportAllImages(_Workbook* pWorkbook)
	: m_spWorkbook(pWorkbook)
{
    m_spWorkbook->GetMediaMgr(&m_spMediaMgr);
}

void ExportAllImages::DoExportAllImages()
{
    ExportCellImages();
    ExportFloatImages();
    WOLOG_INFO << "[ExportAllImages] uploaded all images.";
}

void ExportAllImages::ExportCellImages()
{
	ks_stdptr<IKDrawingCanvas> spCanvas;
	oplGetCellImgOplData(m_spWorkbook->GetBook(), &spCanvas);
	ks_castptr<drawing::ShapeTree> pShapeTree = spCanvas;
	if(!pShapeTree) return;

	walkThroughTree(pShapeTree);
}

void ExportAllImages::ExportFloatImages()
{
	ks_stdptr<IKWorksheets> ptrWorksheets = m_spWorkbook->GetWorksheets();
	int sheetCnt = ptrWorksheets->GetSheetCount();
	if(sheetCnt <= 0) return;

	for(int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
	{
		ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
		if (!ptrWorksheet)
			continue;
		ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
		oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
		if (!spCanvas)
			continue;
		ks_castptr<EtShapeTree> pshapeTree = spCanvas;
		if(!pshapeTree) continue;

		ExportBackgroundImage(ptrWorksheet->GetSheet());
		walkThroughTree(pshapeTree);
	}
}

void ExportAllImages::ExportBackgroundImage(ISheet *sheet)
{
	if(!sheet)
	   return;
	ks_stdptr<IUnknown> ptrUnknown;
	sheet->GetExtDataItem(edSheetBackground, &ptrUnknown);
	ks_stdptr<ISheetBGPicture> spBackground = ptrUnknown;
	if (!spBackground)
		return;

	ks_stdptr<IKBlipAtom> spBlipAtom;
	spBackground->GetBackgroundPicture(&spBlipAtom);
	util::UploadImage(spBlipAtom);
}

void ExportAllImages::walkThroughTree(drawing::AbstractShape* shape)
{
	if(!shape) return;

	if(shape->isGroupShape())
	{
		drawing::GroupShape* shapeGp = static_cast<drawing::GroupShape*>(shape);
		int childCnt = shapeGp->childCount();
		for (int childIdx = 0; childIdx < childCnt; childIdx++)
		{
			drawing::AbstractShape* childShape = shapeGp->childAt(childIdx);
			walkThroughTree(childShape);
		}
	}
	else
	{
		if (shape->isPicture())
		{
			util::UploadImage(shape);
		}
		else
		{
		   	drawing::Fill fill =  shape->fillProp();
			if(fill.type() == drawing::FillTypeBlip && fill.hasBlip())
			{
				util::UploadImage(fill.blip().blipAtom()); 
		   	}	   
		}
	}
}

void ExportAllImages::DoExportRandomImages(UINT32 sampling_threshold, UINT32 image_max_size/* = 0*/)
{
	if (!gs_callback || !gs_callback->uploadImage)
		return;

	auto uploadImageByIdx = [this](const std::vector<LONG>& needSendDataIdx)->void{
		for(const auto& sendIdx:needSendDataIdx)
		{
			ks_stdptr<IKBlipAtom> spBlipAtom;
			this->m_spMediaMgr->GetBlipByIndex(sendIdx, &spBlipAtom);
			if (spBlipAtom == nullptr)
				continue;
            util::UploadImage(spBlipAtom);
		}
	};

	LONG blipCount = 0;
	m_spMediaMgr->GetBlipAtomCount(&blipCount);

    if (sampling_threshold == 0 || blipCount <= sampling_threshold)
    {
 		// 抽样阈值超过了所有图片数量，全部返回
        std::vector<LONG> sendImageIdx(blipCount);
        std::iota(sendImageIdx.begin(), sendImageIdx.end(), 0);
		uploadImageByIdx(sendImageIdx);
        WOLOG_INFO << "[ExportAllImages] uploaded all images. upload_image_nums = " << blipCount << ", sampling_threshold = " << sampling_threshold;
    }
    else
    {
        // 根据图片的md4值进行去重，根据审核策略将图片分类成大图小图
        struct kso_md4_compare{
            bool operator()(const std::pair<kso_md4, LONG>& lhs, const std::pair<kso_md4, LONG>& rhs) const{
                return std::memcmp(lhs.first.md4, rhs.first.md4, sizeof(lhs.first.md4))<0;
            }
        };
        std::set<std::pair<kso_md4, LONG>, kso_md4_compare> setMd4Idx;
        std::set<std::pair<kso_md4, LONG>, kso_md4_compare> setMd4IdxBigImg; // 大图放在这个set中
        for(LONG i = 0; i < blipCount; i++)
        {
            ks_stdptr<IKBlipAtom> blip;
            m_spMediaMgr->GetBlipByIndex(i, &blip);
            if (blip == nullptr)
                continue;
            if (!blip->HasImage())
                continue;

            kso_md4 md4 = blip->GetMD4();

            kpt::VariantImage img = blip->GetImage();
            const int imgH = img.orgHeight();
            const int imgW = img.orgWidth();
            const qint64 imgSize = img.rawByteCount(); //单位：字节
            
            if ((image_max_size != 0 && (imgSize > UINT32_MAX || imgSize > image_max_size)) 
                    || imgH == 0 
                    || imgW == 0 
                    || imgH / imgW >= 100 
                    || imgW / imgH >= 100) 
                setMd4IdxBigImg.insert({md4, i});
            else
                setMd4Idx.insert({md4, i});
        }

        // 获取图片在m_spMediaMgr中的索引
        auto _collectImageIdx = [](const std::set<std::pair<kso_md4, LONG>, kso_md4_compare>& setMd4Idx, std::vector<LONG>& ret){
            for (const auto& t : setMd4Idx)
                ret.push_back(t.second);
        };
        std::vector<LONG> sendVec;
        _collectImageIdx(setMd4Idx, sendVec);
        std::vector<LONG> sendVecBigImg; 
        _collectImageIdx(setMd4IdxBigImg, sendVecBigImg);

        auto _randomSelectVector = [](const std::vector<LONG>& vec, std::vector<LONG>& ret, int sample_num){
            std::random_device rd;
            std::mt19937 g(rd());
            std::sample(vec.begin(), vec.end(), std::back_inserter(ret), sample_num, g);
        };

        // 如果抽样阈值没有超过大图的数量，优先上传大图
        if (sampling_threshold <= sendVecBigImg.size())
        {
            std::vector<LONG> sampleResult;
            _randomSelectVector(sendVecBigImg, sampleResult, sampling_threshold);
            uploadImageByIdx(sampleResult);
        }
        else  // 超过了大图的数量。先上传全部大图，再抽样上传小图
        {
            uploadImageByIdx(sendVecBigImg);
            std::vector<LONG> sampleResult;
            _randomSelectVector(sendVec, sampleResult, sampling_threshold - sendVecBigImg.size());
            uploadImageByIdx(sampleResult);
        }
        WOLOG_INFO << "[ExportAllImages] uploaded random sampling images. upload_image_nums = sampling_threshold = " << sampling_threshold;
    }
}

void SharedLinkExporter::ExportSharedLinkCellImages()
{
	//枚举所有图片
    class KCellValueAcpt : public ICellValueAcpt
    {
        STDIMP_(INT) Do(ROW, COL, const_token_ptr pToken)
        {
            if(!alg::const_vstr_token_assist::is_type(pToken))
                return 0;

            if (!m_pCellImages)
                return 0;

            CellImg_Param param;
            ks_wstring strCellValue;
            strCellValue = alg::const_vstr_token_assist(pToken).get_value();
            if (m_pCellImages->GetCellImgParamFromCellValue(strCellValue.c_str(), &param) == FALSE)
                return 0;
            
            IKShape* pShape = m_pCellImages->GetImgByName(param.getNameStr());
            ks_castptr<drawing::AbstractShape> spShape = pShape;
            if (!spShape)
                return 0;

            m_pExporter->DealShape(spShape);
			return 0;
        };

    public:
        KCellValueAcpt(IKWorkbook* pBook, SharedLinkExporter* pExporter)
		: m_pExporter(pExporter)
        {
            m_pCellImages = pBook->GetCellImages();
        }

	private:
		SharedLinkExporter* m_pExporter;
        ICellImages *m_pCellImages;
    };

	ks_stdptr<IKWorksheets> ptrWorksheets = m_spWorkbook->GetWorksheets();
	int sheetCnt = ptrWorksheets->GetSheetCount();
	if(sheetCnt <= 0) return;

	for(int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
	{
		ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
		if (!ptrWorksheet)
			continue;

		ISheet* pSheet = ptrWorksheet->GetSheet();
		if(!IsSheetVisible(pSheet->GetStId()))
			continue;

		RANGE rgCheck(pSheet->GetBMP());
		IDX idxSheet = INVALIDIDX;
		pSheet->GetIndex(&idxSheet);
		rgCheck.SetSheetFromTo(idxSheet);
		rgCheck.SetRowFromTo(0, pSheet->GetBMP()->cntRows - 1);
		rgCheck.SetColFromTo(0, pSheet->GetBMP()->cntCols - 1);

		et_sdptr<ISheetEnum> spSheetEnum;
		pSheet->CreateEnum(&spSheetEnum);

		KCellValueAcpt cellValue(m_spWorkbook, this);
		spSheetEnum->EnumCellValue(rgCheck, &cellValue);
	}
}

void SharedLinkExporter::ExportSharedLinkFloatImages()
{
	ks_stdptr<IKWorksheets> ptrWorksheets = m_spWorkbook->GetWorksheets();
	int sheetCnt = ptrWorksheets->GetSheetCount();
	if(sheetCnt <= 0) return;

	for(int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
	{
		ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
		if (!ptrWorksheet)
			continue;
		if(!IsSheetVisible(ptrWorksheet->GetSheet()->GetStId()))
			continue;

		const WCHAR* pSheetName = nullptr;
        ptrWorksheet->GetSheet()->GetName(&pSheetName);
        if (xstrcmp(pSheetName, STR_CELL_IMAGE_SHEET_NAME) == 0)
            continue;
		ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
		oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
		if (!spCanvas)
			continue;
		ks_castptr<EtShapeTree> pshapeTree = spCanvas;
		if(!pshapeTree) continue;

		DealShape(pshapeTree);
	}
}

void ExportSharedLinkAllImages::ExportCellImages()
{
	ExportSharedLinkCellImages();
}

void ExportSharedLinkAllImages::ExportFloatImages()
{
	ExportSharedLinkFloatImages();
}

void ExportSharedLinkAllImages::DealShape(drawing::AbstractShape* shape)
{
	walkThroughTree(shape);
}

void ExportAllAttachment::CollectAllAttachment()
{
	m_attachmentIdVec.clear();
	m_attachmentIdVideoVec.clear();
	m_urlVec.clear();
	ExportAttachmentCellImages();
	ExportAttachmentFloatImages();
	ExportAttachmentAnnex();
	ExportAttachmentCommentImages();
}

void ExportAllAttachment::ExportAttachmentAnnex()
{
	ks_stdptr<IKWorksheets> ptrWorksheets = m_spWorkbook->GetWorksheets();
	int sheetCnt = ptrWorksheets->GetSheetCount();
	if(sheetCnt <= 0) return;

	for(int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
	{
		ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
		if (!ptrWorksheet)
			continue;
		const WCHAR* pSheetName = nullptr;
        ptrWorksheet->GetSheet()->GetName(&pSheetName);
        if (xstrcmp(pSheetName, STR_CELL_IMAGE_SHEET_NAME) == 0)
            continue;

		ExportAttachmenBySheet(ptrWorksheet->GetSheet());
	}
}

void ExportAllAttachment::ExportAttachmenBySheet(ISheet* spSheet)
{
	if (m_ctx)
	{
		std::vector<ks_wstring> vecIds;
		std::vector<ks_wstring> vecIdsVideo;
		m_ctx->getAttachmentAnnexIdBySheet(spSheet, vecIds, vecIdsVideo);
		for (int i = 0; i < vecIds.size(); i++)
		{
			m_attachmentIdVec.emplace_back(vecIds[i]);
		}
		for (int i = 0; i < vecIdsVideo.size(); i++)
		{
			m_attachmentIdVideoVec.emplace_back(vecIdsVideo[i]);
		}
	}
}

void ExportAllAttachment::ExportAttachmentCellImages()
{
	ks_stdptr<IKDrawingCanvas> spCanvas;
	oplGetCellImgOplData(m_spWorkbook->GetBook(), &spCanvas);
	ks_castptr<drawing::ShapeTree> pShapeTree = spCanvas;
	if(!pShapeTree) return;

	walkThroughTree(pShapeTree);
}

void ExportAllAttachment::ExportAttachmentFloatImages()
{
	ks_stdptr<IKWorksheets> ptrWorksheets = m_spWorkbook->GetWorksheets();
	int sheetCnt = ptrWorksheets->GetSheetCount();
	if(sheetCnt <= 0) return;

	for(int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
	{
		ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
		if (!ptrWorksheet)
			continue;
		const WCHAR* pSheetName = nullptr;
        ptrWorksheet->GetSheet()->GetName(&pSheetName);
        if (xstrcmp(pSheetName, STR_CELL_IMAGE_SHEET_NAME) == 0)
            continue;
		ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
		oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
		if (!spCanvas)
			continue;
		ks_castptr<EtShapeTree> pshapeTree = spCanvas;
		if(!pshapeTree) continue;

		walkThroughTree(pshapeTree);
	}
}

void ExportAllAttachment::ExportAttachmentCommentImages()
{
	ks_stdptr<IKWorksheets> spWorksheets = m_spWorkbook->GetWorksheets();
	for(int sheetIdx = 0; sheetIdx < spWorksheets->GetSheetCount(); sheetIdx++)
	{
		ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(sheetIdx);
		if (!spWorksheet || !spWorksheet->GetSheet() || !checkSheetStatus(sheetIdx))
			continue;

		ks_stdptr<IKDrawingCanvas> spCommentCanvas = nullptr;
		oplGetSheetCommentOplData(spWorksheet->GetSheet(), &spCommentCanvas);
		if (!spCommentCanvas)
			continue;

		ks_stdptr<ICellComments> spCMTs = spCommentCanvas;
		INT cmtCnt;
		spCMTs->GetCount(&cmtCnt);
		for (INT cmtIdx = 0; cmtIdx < cmtCnt; cmtIdx++)
		{
			ks_stdptr<ICellComment> spCellCmt;
			spCMTs->GetItem(cmtIdx, &spCellCmt);
			if(!spCellCmt)
				continue;

			ks_stdptr<IWoComment> spWoCmt;
			spCellCmt->GetWoComment(&spWoCmt);
			if(!spWoCmt)
				continue;

			auto exportCommentFromChains = [&](bool bIsResolved)
			{
				for (size_t i = 0; i < spWoCmt->GetChainCount(bIsResolved); ++i)
				{
					IWoCommentChain* pWoCommentChain = spWoCmt->GetChainByIndex(bIsResolved, i);
					if (pWoCommentChain == nullptr)
						continue;

					for (size_t j = 0; j < pWoCommentChain->Count(); ++j)
					{
						IWoCommentItem* pItem = pWoCommentChain->GetItem(j);
						if (pItem == nullptr)
							continue;

						for (INT k = 0; k < pItem->GetImageCount(); ++k)
						{
							if (IWoCommentImage* pImage = pItem->GetImage(k))
								m_attachmentIdVec.emplace_back(pImage->GetInfo().id);
						}
					}
				}
			};

			exportCommentFromChains(false);
			exportCommentFromChains(true);
		}
	}
}
void ExportAllAttachment::walkThroughTree(drawing::AbstractShape* shape)
{
	if(!shape) return;

	if(shape->isGroupShape())
	{
		drawing::GroupShape* shapeGp = static_cast<drawing::GroupShape*>(shape);
		int childCnt = shapeGp->childCount();
		for (int childIdx = 0; childIdx < childCnt; childIdx++)
		{
			drawing::AbstractShape* childShape = shapeGp->childAt(childIdx);
			if (ET_MODEL_CELLIMAGESHAPE == childShape->getModelType())
			{
				ks_castptr<EtCellImageIndividualShape> pImgShape = childShape;
				if (pImgShape->ref() <= 0)
					continue;
			}
			walkThroughTree(childShape);
		}
	}
	else
	{
		if(shape->isPicture())
		{
			exportAttachmentPicData(shape);
		}
	}
}

void ExportAllAttachment::exportAttachmentPicData(const drawing::AbstractShape* shape)
{
	if (!shape->isPicture())
		return;
	ks_bstr path;

	IKBlipAtom* pBlipAtom = shape->picID();
	if (pBlipAtom && pBlipAtom->GetLinkPath(&path) == S_OK && !path.empty())
	{
		bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(path.c_str());
		if (isAttachment)
			m_attachmentIdVec.emplace_back(util::getAttachmentId(path));
		else
			m_urlVec.emplace_back(path.c_str());
	}
}

void ExportSharedLinkAllAttachment::ExportAttachmentCellImages()
{
	ExportSharedLinkCellImages();
}

void ExportSharedLinkAllAttachment::ExportAttachmentFloatImages()
{
	ExportSharedLinkFloatImages();
}

void ExportSharedLinkAllAttachment::ExportAttachmentAnnex()
{
	ks_stdptr<IKWorksheets> ptrWorksheets = GetWorkBook()->GetWorksheets();
	int sheetCnt = ptrWorksheets->GetSheetCount();
	if(sheetCnt <= 0) return;

	for(int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
	{
		ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
		if (!ptrWorksheet)
			continue;
		if(!IsSheetVisible(ptrWorksheet->GetSheet()->GetStId()))
			continue;

		const WCHAR* pSheetName = nullptr;
        ptrWorksheet->GetSheet()->GetName(&pSheetName);
        if (xstrcmp(pSheetName, STR_CELL_IMAGE_SHEET_NAME) == 0)
            continue;

		ExportAttachmenBySheet(ptrWorksheet->GetSheet());
	}
}

void ExportSharedLinkAllAttachment::DealShape(drawing::AbstractShape* shape)
{
	walkThroughTree(shape);
}

bool ExportSharedLinkAllAttachment::checkSheetStatus(UINT sheetId)
{
	return IsSheetVisible(sheetId);
}

bool SharedLinkExporter::IsSheetVisible(UINT sheetId)
{
	auto pVisibilityChecker = ShareLinkContentVisibilityChecker::CreateInstance(m_spWorkbook->GetBook(), m_shareId.c_str());
	if(!pVisibilityChecker)
	{
		ASSERT(false);
		return false;
	}

	return pVisibilityChecker->IsSheetVisible(sheetId);
}

}// wo