﻿#include "etstdafx.h"
#include "db2ksheet_converter.h"

#include <et_hard_define.h>
#include "etcore/et_core_dbsheet.h"
#ifndef X_OS_WINDOWS
#include "kern/errno.h"
#endif
#include "dbsheet/et_dbsheet_utils.h"
#include "util.h"
#include "dbsheet/db_export_helper.h"
#include "helpers/statsheet_helper.h"
#include "dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h"
#include "db/db_basic_itf.h"

namespace wo
{

void Db2KsBookConverter::convertUnsupportedDbFields(IDBSheetOp* pSheetOp)
{
    const IDBIds* pFields = pSheetOp->GetAllFields();
    const IDBIds* pRecords = pSheetOp->GetAllRecords();
    IDbFieldsManager* pFieldsManager = pSheetOp->GetFieldsManager();
    EtDbIdx fieldCount = pFields->Count();
    EtDbIdx recordCount = pRecords->Count();
    for (EtDbIdx fieldIndex = 0; fieldIndex < fieldCount; ++fieldIndex)
    {
        EtDbId fieldId = pFields->IdAt(fieldIndex);
        ks_stdptr<IDbField> spField;
        pFieldsManager->GetField(fieldId, &spField);

        if (spField->GetType() == Et_DbSheetField_Note)
        {
            // 富文本字段转为多行文本字段，内容为富文本摘要
            spField->SetTypeForIO(Et_DbSheetField_MultiLineText);
            for (EtDbIdx recordIndex = 0; recordIndex < recordCount; ++recordIndex)
            {
                EtDbId recordId = pRecords->IdAt(recordIndex);
                ks_bstr bstrVal;
                HRESULT hr = pSheetOp->GetDisplayString(recordId, fieldId, &bstrVal);
                if (FAILED(hr))
                    continue;
                pSheetOp->SetValue(recordId, fieldId, bstrVal.c_str());
            }
        }
        else if (spField->GetType() == Et_DbSheetField_Automations)
        {
            // 自动任务字段转为多行文本字段，自动任务列数据是空的，不需要清除
            spField->SetTypeForIO(Et_DbSheetField_MultiLineText);
        }
        else if (spField->GetType() == Et_DbSheetField_ParentRecord)
        {
            spField->SetTypeForIO(Et_DbSheetField_MultiLineText);
            spField->OnChangeFieldType(Et_DbSheetField_ParentRecord, Et_DbSheetField_MultiLineText, nullptr);
        }
    }
}

void Db2KsBookConverter::clearDefaultValue(IDBSheetOp* pSheetOp)
{
	const IDBIds* pFields = pSheetOp->GetAllFields();
    IDbFieldsManager* pFieldsManager = pSheetOp->GetFieldsManager();
    EtDbIdx fieldCount = pFields->Count();
    for (EtDbIdx fieldIndex = 0; fieldIndex < fieldCount; ++fieldIndex)
    {
        EtDbId fieldId = pFields->IdAt(fieldIndex);
        ks_stdptr<IDbField> spField;
        pFieldsManager->GetField(fieldId, &spField);
        spField->SetDefaultValTypeForIO(DbSheet_Field_Dvt_Normal);
        spField->SetDefaultValForIO(__X(""));
    }
}

void Db2KsBookConverter::clearFieldPropNotSupportInKsBook(IDBSheetOp* pSheetOp)
{
    // 暂时没有，后续再加上
}

HRESULT Db2KsBookConverter::onBeforeExport()
{
    IBook* pBook = m_pDbWb->GetBook();
    if (!pBook)
        return E_FAIL;

    ks_stdptr<etoldapi::Worksheets> spWorksheets = m_pDbWb->GetWorksheets();
    if (!spWorksheets)
        return E_FAIL;

    HRESULT hr = S_OK;
	ProcessCrossBookDbData(m_pDbWb);
    if (pBook->GetBMP()->bDbSheet)
    {
        RemoveNoPermissionDbSheetData(m_pDbWb);
        // 将ksheet不支持的db字段转化为其它字段
        int sheetCount = spWorksheets->GetSheetCount();
        for (int i = 0; i < sheetCount; i++)
        {
            ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorksheets->GetSheetItem(i);
            if (!spWorksheet)
                return E_FAIL;

            ISheet* pSheet = spWorksheet->GetSheet();
            if (pSheet->IsDbSheet())
            {
                hr = handleDbSheet(pSheet);
                if (FAILED(hr))
                    return hr;
            }
            else if (pSheet->IsDbDashBoardSheet())
            {
                hr = handleDbDashboardSheet(spWorksheet);
                if (FAILED(hr))
                    return hr;
            }
        }
    }
    return S_OK;
}

void Db2KsBookConverter::collectDbAttachmentId(IDBSheetOp* pSheetOp)
{
    const IDBIds* pFields = pSheetOp->GetAllFields();
    const IDBIds* pRecords = pSheetOp->GetAllRecords();
    IDbFieldsManager* pFieldsManager = pSheetOp->GetFieldsManager();
    EtDbIdx fieldCount = pFields->Count();
    EtDbIdx recordCount = pRecords->Count();
    for (EtDbIdx fieldIndex = 0; fieldIndex < fieldCount; ++fieldIndex)
    {
        EtDbId fieldId = pFields->IdAt(fieldIndex);
        ks_stdptr<IDbField> spField;
        pFieldsManager->GetField(fieldId, &spField);
        if (spField->GetType() != Et_DbSheetField_Attachment)
            continue;
        if (spField->IsSyncLookupField())
            continue;

        for (EtDbIdx recordIndex = 0; recordIndex < recordCount; ++recordIndex)
        {
            EtDbId recordId = pRecords->IdAt(recordIndex);
            const_token_ptr pToken = nullptr;
            HRESULT hr = pSheetOp->GetValueToken(recordId, fieldId, &pToken);
            if (FAILED(hr) || !pToken)
                continue;

            if (not alg::const_handle_token_assist::is_type(pToken))
                continue;

            alg::const_handle_token_assist chta(pToken);
            alg::TOKEN_HANDLE handle = chta.get_handle();
            if (!handle || chta.get_handleType() != etexec::ET_HANDLE_TOKENARRAY)
                continue;

            ks_stdptr <IDbTokenArrayHandle> spTokenArrayHandleCopy;
            _db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle, (void**)&spTokenArrayHandleCopy);
            ks_stdptr<IDbTokenArrayHandle> spHandleArray = handle->CastUnknown();
            UINT handleCount = spHandleArray->GetCount();
            for (int i = 0; i < handleCount; ++i)
            {
                const_token_ptr pItem = nullptr;
                spHandleArray->Item(i, &pItem);
                alg::const_handle_token_assist itemChta(pItem);
                alg::TOKEN_HANDLE itemHandle = itemChta.get_handle();
                if (!itemHandle || itemChta.get_handleType() != etexec::ET_HANDLE_DBATTACHMENT)
                    continue;

                ks_stdptr<IDbAttachmentHandle> spAttachmentHandle = itemHandle->CastUnknown();
                //ksheet暂时不支持mp4，先过滤
                if(m_unsupportedAttachmentFileSuffixTypes.find(spAttachmentHandle->GetContentType()) != m_unsupportedAttachmentFileSuffixTypes.end())
                    continue;

                ks_stdptr<IDbAttachmentHandle> spTokenAttachmentHandleCopy;
                _db_CreateObject(CLSID_KDbAttachmentHandle, IID_IDbAttachmentHandle, (void **)&spTokenAttachmentHandleCopy);
                spTokenAttachmentHandleCopy->Init(spAttachmentHandle->GetFileId(), spAttachmentHandle->GetSource(), spAttachmentHandle->GetContentType(), 
                spAttachmentHandle->GetName(), spAttachmentHandle->GetSize(), spAttachmentHandle->GetLinkUrl(), spAttachmentHandle->GetImgSize());
                alg::managed_handle_token_assist mhta;
                mhta.create(alg::ET_HANDLE_DBATTACHMENT, spTokenAttachmentHandleCopy);
                spTokenArrayHandleCopy->Add(mhta.detach());

                PCWSTR attachmentId = spAttachmentHandle->GetFileId();
                m_pCtx->addCloudImgAttachmentIdDirectly(attachmentId);
            }
            alg::managed_handle_token_assist mhtaNew;
            mhtaNew.create(alg::ET_HANDLE_TOKENARRAY, spTokenArrayHandleCopy);
            VS(pSheetOp->SetTokenValue(recordId, fieldId, mhtaNew));
        }
    }
}

void Db2KsBookConverter::setClearDefaultValue(bool clearDefaultValue)
{
    m_clearDefaultValue = clearDefaultValue;
}

HRESULT Db2KsBookConverter::Init()
{
	if (!m_pDbWb)
		return E_INVALIDARG;
    m_pDbSheets = m_pDbWb->GetWorksheets();
	return S_OK;
}

Db2KsBookConverter::Db2KsBookConverter(_Workbook* pDbWb, PCWSTR userId, IDBProtectionJudgement* pProtectionJudgement, KEtRevisionContext* ctx) : m_pDbWb(pDbWb), m_userId(userId), m_spProtectionJudgement(pProtectionJudgement), m_pCtx(ctx) 
{
    if (m_pDbWb)
    {
        IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->BeginExportDbt(m_pDbWb->GetBook());
    }
}

Db2KsBookConverter::~Db2KsBookConverter()
{
    if (m_pDbWb)
    {
        IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->EndExportDbt(m_pDbWb->GetBook());
    }
}

HRESULT Db2KsBookConverter::Exec()
{
    // 获取worksheets
    ks_stdptr<etoldapi::Worksheets> spWorksheets = m_pDbWb->GetWorksheets();
    if (!spWorksheets)
        return E_FAIL;

    // 检测sheet数量是否为空
    int sheetCount = spWorksheets->GetSheetCount();
    if (sheetCount <= 0)
        return E_FAIL;

    HRESULT hr = onBeforeExport();
    if (FAILED(hr))
        return hr;

    std::vector<int> fpSheetIndexVec;
    // 遍历源sheets
	sheetCount = spWorksheets->GetSheetCount();
    for (int i = 0; i < sheetCount; ++i)
    {
        ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorksheets->GetSheetItem(i);
        if (!spWorksheet)
            return E_FAIL;

        ISheet* pSheet = spWorksheet->GetSheet();

        const WCHAR* pSheetName = nullptr;
        pSheet->GetName(&pSheetName);
        if (!xstrcmp(pSheetName, STR_CELL_IMAGE_SHEET_NAME))
            continue;

#ifdef _DEBUG
        SHEETSTATE ss;
        pSheet->GetVisible(&ss);
        ASSERT(ss == ssVisible);
#endif

        if (pSheet->IsDbDashBoardSheet())
        {
            continue;
        }
        else if (pSheet->IsFpSheet())
        {
            fpSheetIndexVec.push_back(i);
            continue;
        }
        else if (pSheet->IsDbSheet())
        {
            WCHAR wszSheetName[MAX_SHEET_NAME_CCH + 1] = {0};
            const WCHAR* pcwsName = NULL;
            pSheet->GetName(&pcwsName);
            if (spWorksheet->IsValidSheetName(pcwsName) != S_OK)
            {
                hr = spWorksheets->ValidateSheetName(stGrid_DB, pcwsName, wszSheetName, countof(wszSheetName));
                if (FAILED(hr))
                    return hr;
                ks_bstr strName(wszSheetName);
                hr = spWorksheet->put_Name(strName);
                if (FAILED(hr))
                    return hr;
            }
        }
        else
        {
            ASSERT(!"Unexpected sheet type");
        }
	} 
    for (auto it = fpSheetIndexVec.rbegin(); it != fpSheetIndexVec.rend(); ++it)
    {
        ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorksheets->GetSheetItem(*it);
        hr = spWorksheet->DeleteDirectly();
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT Db2KsBookConverter::copyFieldTitleFormatToDefaultView(ISheet* pSheet)
{
    ks_stdptr<IDBSheetViews> spDbSheetViews;
    VS(DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews));
    class CopyFieldTitleFormatEnum : public IDbSheetFieldTitleFormatEnum
    {
        public:
            CopyFieldTitleFormatEnum(IDBSheetView_Grid *pView): m_pView(pView) {};
            HRESULT Do(EtDbId id, DWORD bgColor) override
            {
                m_pView->SetFieldTitleFormat(id, bgColor);
                return S_OK;
            };
        private:
            IDBSheetView_Grid *m_pView;
    };
    IDBSheetView* pDefaultView = spDbSheetViews->GetDefaultView();
    if (pDefaultView == nullptr)
        return S_FALSE;

    ks_stdptr<IDBSheetViewsEnum> spEnum;
    if (SUCCEEDED(spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
    {
        do
        {
            ks_stdptr<IDBSheetView> spDbSheetView;
            spEnum->GetCurView(&spDbSheetView);
            if (!spDbSheetView)
                continue;

            if (spDbSheetView && spDbSheetView->GetType() == et_DBSheetView_Grid && pDefaultView->GetType() == et_DBSheetView_Grid)
            {
                ks_stdptr<IDBSheetView_Grid> spDefaultGridView = pDefaultView;
                ks_stdptr<IDBSheetView_Grid> spGridView = spDbSheetView;
                CopyFieldTitleFormatEnum cftfe(spDefaultGridView);
                HRESULT hr = spGridView->EnumFieldTitleFormat(&cftfe);
                if (FAILED(hr))
                    return hr;
                break;
            }
        }while (SUCCEEDED(spEnum->Next()));
    }
    return S_OK;
}

HRESULT Db2KsBookConverter::clearFieldTitleFormat(ISheet* pSheet)
{
    ks_stdptr<IDBSheetViews> spDbSheetViews;
    VS(DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews));
    ks_stdptr<IDBSheetViewsEnum> spEnum;
    if (SUCCEEDED(spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
    {
        do
        {
            ks_stdptr<IDBSheetView> spDbSheetView;
            spEnum->GetCurView(&spDbSheetView);
            if (!spDbSheetView)
                continue;

            if (spDbSheetView->GetType() == et_DBSheetView_Grid)
            {
                ks_stdptr<IDBSheetView_Grid> spGridView = spDbSheetView;
                HRESULT hr = spGridView->ClearFieldTitleFormat();
                if (FAILED(hr))
                    return hr;
            }
        }while (SUCCEEDED(spEnum->Next()));
    }
    return S_OK;
}

void Db2KsBookConverter::SetUnsupportedAttachmentFileSuffixTypes(const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes)
{
    m_unsupportedAttachmentFileSuffixTypes = std::move(unsupportedAttachmentFileSuffixTypes);
}

HRESULT Db2KsBookConverter::handleUnsupportedDbViews(ISheet* pSheet)
{
    if (!pSheet)
        return E_FAIL;

    ks_stdptr<IDBSheetViews> spDbSheetViews;
    VS(DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews));
    if (!spDbSheetViews)
        return E_FAIL;

    std::vector<EtDbId> unSupportedDbViewIds;
    for (int i = 0, cnt = spDbSheetViews->GetSize(Et_DBSheetViewUse_ForDb); i < cnt; ++i)
    {
        ks_stdptr<IDBSheetView> spDbSheetView;
        spDbSheetViews->GetItemAt(i, Et_DBSheetViewUse_ForDb, &spDbSheetView);
        if (spDbSheetView && spDbSheetView->GetType() == et_DBSheetView_Calendar)
            unSupportedDbViewIds.push_back(spDbSheetView->GetId());
    }

    for (size_t i = 0; i < unSupportedDbViewIds.size(); i++)
    {
        HRESULT hr = spDbSheetViews->DelItem(unSupportedDbViewIds.at(i));
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT Db2KsBookConverter::handleDbSheet(ISheet* pSheet)
{
    ks_stdptr<IDBSheetOp> spSheetOp;
    VS(DbSheet::GetDBSheetOp(pSheet, &spSheetOp));
    if (spSheetOp)
    {
        convertUnsupportedDbFields(spSheetOp);
        // 采集轻维表附件id以便导入
        collectDbAttachmentId(spSheetOp);
        if (m_clearDefaultValue)
            clearDefaultValue(spSheetOp);
        clearFieldPropNotSupportInKsBook(spSheetOp);
        HRESULT hr = copyFieldTitleFormatToDefaultView(pSheet);
        if (FAILED(hr))
            return hr;
        hr = clearFieldTitleFormat(pSheet);
        if (FAILED(hr))
            return hr;
        hr = handleUnsupportedDbViews(pSheet);
        if (FAILED(hr))
            return hr;

        ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
        pSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
        if(spDBStatisticSheetData)
        {
            hr = RecoverStatSheet2NormalDbSheet(pSheet);
            if (FAILED(hr))
                return hr;
        }
    }
    return S_OK;
}

HRESULT Db2KsBookConverter::handleDbDashboardSheet(_Worksheet* pWorksheet)
{
    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pWorksheet->GetSheet(), &spDbDashboard));
    if (spDbDashboard)
    {
        HRESULT hr = spDbDashboard->GetFilterMgr()->RemoveAllFilters();
        if (FAILED(hr))
            return hr;

        KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(nullptr, pWorksheet);
        hr = dashboardModuleMgrWrapper.DeleteAllDbPlugin();
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

} // namespace wo
