﻿#include "binvariant/binvardef.h"
#include "etcore/et_core_dbsheet.h"
#include "etstdafx.h"
#include "workbook.h"
#include "db_plugin_wrapper.h"
#include "webbase/serialize.h"
#include "dbsheet/et_dbsheet_filter_helper.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "src/et_revision_context_impl.h"
#include "etcore/et_core_dbsheet_enum.h"
#include "webbase/wo_sa_helper.h"
#include "wo/db_id_encode_decode.h"

namespace wo
{

KDbDashboardPluginWrapper::KDbDashboardPluginWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension, IDBChartStatisticModule* pStatisticModule)
    : KDbDashboardModuleWrapper(pEtWorkbook, pWebExtension)
    , m_spStatisticModule(pStatisticModule)
{
}

KDbDashboardModuleWrapper::Status KDbDashboardPluginWrapper::GetStatus() const
{
    WebExtensionDataSourceType dataSourceType = GetDataSourceType();
    if (dataSourceType == dst_Unknown)
        return Status::NoDataSource;

    UINT dataSourceId = m_spStatisticModule->GetDataSourceId();
    if (dataSourceId == 0)
        return Status::NoDataSource;

    const IDBRecordsOrderManager* pMgr = m_spStatisticModule->GetConstOrderManager();
    if (!pMgr || pMgr->GetGroupConditionCount() == 0)
        return Status::InvalidConfig;
    const IDbChartStatisticOptions* pStatisticOptions = m_spStatisticModule->GetConstStatisticOptions();
    if (!pStatisticOptions || (!m_spStatisticModule->GetOnlyCountDimension() && pStatisticOptions->Count() == 0))
        return Status::InvalidConfig;

    return Status::Normal;
}

HRESULT KDbDashboardPluginWrapper::SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang,
                                             KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) const
{
    Status status = GetStatus();
    if (status != Status::Normal)
    {
        PCWSTR resultTypeStr = __X("");
        VS(_appcore_GainEncodeDecoder()->EncodeDbChartResultType(DbSheet_Chart_RT_Invalid, &resultTypeStr));
        pAcpt->addString("type", resultTypeStr);
    }
    else
    {
        m_spStatisticModule->SerializeResult(nullptr, lang, pAcpt, true);
    }
    return S_OK;
}

static void addEtDbIdStr(ISerialAcceptor* pAcpt, const char* name, EtDbId id)
{
    EtDbIdStr buf;
    VS(db_id::EncodeEtDbId(id, &buf));
    pAcpt->addString(name, buf);
}

static void serialiseDataConditionDataRange(IDBChartStatisticModule* pDbChartStatisticModule, ISerialAcceptor* pAcpt)
{
    IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
    wo::sa::Leave dataRange(sa::enterStruct(pAcpt, "dataRange"));
    const IDbFilter* pDbFilter = pDbChartStatisticModule->GetConstFilter();
    KDbFilterOpType filterOp = pDbFilter->GetOperator();
    PCWSTR filterOpStr = nullptr;
    pDecoder->EncodeKDbFilterOpType(filterOp, &filterOpStr);
    pAcpt->addString("filtersOp", filterOpStr);
    {
        wo::sa::Leave filterInfo(sa::enterStruct(pAcpt, "filterInfo"));
        for (int i = 0, filterCnt = pDbFilter->GetFiltersCount(); i < filterCnt; ++i)
        {
            const IDbFieldFilter* pFieldFilter = nullptr;
            pDbChartStatisticModule->GetConstFilter()->GetFilter(i, &pFieldFilter);
            if (!pFieldFilter)
                continue;

            addEtDbIdStr(pAcpt, "fieldId", pFieldFilter->GetFieldId());
            {
                wo::sa::Leave criteria(sa::enterStruct(pAcpt, "criteria"));
                const IDbFilterCriteria* pFilterCriteria = pFieldFilter->GetCriteria();
                if (!pFilterCriteria)
                    continue;

                KDbFilterCriteriaOpType criteriaOpType = pFilterCriteria->GetCriteriaOp();
                PCWSTR criteriaOpTypeStr = nullptr;
                pDecoder->EncodeKDbFilterCriteriaOpType(criteriaOpType, &criteriaOpTypeStr);
                pAcpt->addString("op", criteriaOpTypeStr);
                wo::sa::Leave values(sa::enterArray(pAcpt, "values"));
                for (int j = 0, valueCnt = pFilterCriteria->GetValuesCount(); j < valueCnt; ++j)
                {
                    auto pValue = const_cast<IDbFcValueBase*>(pFilterCriteria->GetValue(j));
                    pValue->SerialContent(pAcpt);
                }
            }
        }
    }
}

static void serialiseDataConditionGroups(IDBChartStatisticModule* pDbChartStatisticModule, ISerialAcceptor* pAcpt)
{
    wo::sa::Leave groups(sa::enterArray(pAcpt, "groups"));
    {
        const IDBRecordsOrderManager* pMgr = pDbChartStatisticModule->GetConstOrderManager();
        for (int i = 0, length = pMgr->GetGroupConditionCount(); i < length; ++i)
        {
            wo::sa::Leave group(sa::enterStruct(pAcpt, nullptr));
            const IDBRecordsOrderCondition* pCondition = pMgr->GetGroupCondition(0);
            addEtDbIdStr(pAcpt, "fieldId", pCondition->GetKeyFieldId());

            PCWSTR unitStr = nullptr;
            _appcore_GainEncodeDecoder()->EncodeGroupUnit(pCondition->GetUnit(), &unitStr);
            pAcpt->addString("groupUnit", unitStr);

            PCWSTR groupModeStr = pCondition->IsSplitMultiple() ? __X("Enumerated") : __X("Integrated");
            pAcpt->addString("groupMode", groupModeStr);

            {
                wo::sa::Leave sort(sa::enterStruct(pAcpt, "sort"));
                PCWSTR sortTypeStr = nullptr;
                ET_DBSheet_ChartSortType sortType = pDbChartStatisticModule->GetSortType();
                if (sortType == DbSheet_Chart_ST_SortByHorizontalAxis)
                    sortTypeStr = __X("Group");
                else if (sortType == DbSheet_Chart_ST_SortByVerticalAxis)
                    sortTypeStr = __X("Value");
                else
                    sortTypeStr = __X("");
                pAcpt->addString("sortType", sortTypeStr);
                PCWSTR orderStr = pDbChartStatisticModule->GetSortAscending() ? __X("Ascend") : __X("Descend");
                pAcpt->addString("order", orderStr);
            }
        }
    }
}

static void serialiseDataConditionSeries(IDBChartStatisticModule* pDbChartStatisticModule, ISerialAcceptor* pAcpt)
{
    bool onlyCountDimension = pDbChartStatisticModule->GetOnlyCountDimension();
    if (onlyCountDimension)
    {
        pAcpt->addString("series", __X("COUNTA"));
    }
    else
    {
        class StatisticOptionsEnum : public IDbChartStatisticOptionsEnum
        {
        public:
            StatisticOptionsEnum(ISerialAcceptor* pAcpt) : m_pAcpt(pAcpt) {}
            STDMETHODIMP Do(const IDbChartStatisticOption* pStatisticOption) override
            {
                addEtDbIdStr(m_pAcpt, "fieldId", pStatisticOption->GetFieldId());
                ET_DBSheet_StatisticOption option = pStatisticOption->GetStatisticOption();
                PCWSTR optionStr = nullptr;
                _appcore_GainEncodeDecoder()->EncodeStatisticOption(option, &optionStr);
                m_pAcpt->addString("rollup", optionStr);
                return S_OK;
            }
        private:
            ISerialAcceptor* m_pAcpt;
        };
        wo::sa::Leave series(sa::enterArray(pAcpt, "series"));
        StatisticOptionsEnum statisticEnum(pAcpt);
        pDbChartStatisticModule->GetConstStatisticOptions()->Enum(&statisticEnum);
    }
}

void KDbDashboardPluginWrapper::SerializeSettings(ISerialAcceptor* pAcpt)
{
    WebExtensionDataSourceType dataSourceType = GetDataSourceType();
    if (dataSourceType != dst_dbTable)
        return;
    wo::sa::Leave pluginConfig(sa::enterStruct(pAcpt, "pluginConfig"));
    {
        wo::sa::Leave dataConditions(sa::enterArray(pAcpt, "dataConditions"));
        UINT dataSourceId = m_spStatisticModule->GetDataSourceId();
        if (dataSourceId == 0)
            return;
        {
            wo::sa::Leave dataCondition(sa::enterStruct(pAcpt, nullptr));
            pAcpt->addUint32("dataSourceSheetId", dataSourceId);
            serialiseDataConditionDataRange(m_spStatisticModule, pAcpt);
            serialiseDataConditionGroups(m_spStatisticModule, pAcpt);
            serialiseDataConditionSeries(m_spStatisticModule, pAcpt);
        }
    }
}

HRESULT KDbDashboardPluginWrapper::SetPluginConfig(const binary_wo::VarObj& param, IEtRevisionContext* pCtx)
{
    // 全量修改，参数必传
    if (!param.has("dataConditions") || !param.has("customConfig"))
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;

    binary_wo::VarObj dataConditions = param.get("dataConditions");
    UINT dataConditionCnt = dataConditions.arrayLength();
    if (dataConditionCnt > 1)
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;

    HRESULT hr = SetCustomConfig(param, pCtx);
    if (FAILED(hr))
        return hr;

    if (dataConditionCnt == 0)
        return S_OK;

    binary_wo::VarObj dataConditionObj = dataConditions.at(0);
    if (!dataConditionObj.has("sheetId"))
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
    UINT dataSourceId = dataConditionObj.field_uint32("sheetId");
    if (dataSourceId != m_spStatisticModule->GetDataSourceId())
    {
        hr = m_spStatisticModule->SetDataSourceId(dataSourceId);
        if (FAILED(hr))
            return hr;
    }
    if (dataSourceId == 0)
        return S_OK;

    if (!dataConditionObj.has("dataRange") || !dataConditionObj.has("groups") || !dataConditionObj.has("series"))
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
    hr = SetPluginConfigDataRange(dataConditionObj.get("dataRange"));
    if (FAILED(hr))
        return hr;

    hr = SetPluginConfigGroups(dataConditionObj.get("groups"));
    if (FAILED(hr))
        return hr;

    hr = SetPluginConfigSeries(dataConditionObj.get("series"));
    if (FAILED(hr))
        return hr;
    m_spStatisticModule->ModuleChange();
    return S_OK;
}

HRESULT KDbDashboardPluginWrapper::SetPluginConfigDataRange(const binary_wo::VarObj& dataRange)
{
    bool isChange = false;
    HRESULT hr = IsPluginConfigDataRangeChange(dataRange, isChange);
    if (FAILED(hr))
        return hr;
    if (!isChange)
        return S_OK;

    hr = m_spStatisticModule->ClearFilter();
    if (FAILED(hr))
        return hr;
    if (!dataRange.has("filterInfo") || !dataRange.has("filtersOp"))
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
    binary_wo::VarObj filterInfo = dataRange.get("filterInfo");
    if (filterInfo.empty())
        return S_OK;

    m_spStatisticModule->GetMutableFilter()->SetEnableMultiFieldFilter(true);
    for (int i = 0, length = filterInfo.arrayLength(); i < length; ++i)
    {
        binary_wo::VarObj item = filterInfo.at(i);
        if (!item.has("criteria") || !item.has("fieldId"))
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        ks_stdptr<IDbFilterCriteria> spCriteria;
        hr = CreateDbFilterCriteria(item.get("criteria"), m_pEtWorkbook->GetCoreWorkbook()->GetBook(), &spCriteria);
        if (FAILED(hr))
            return hr;

        hr = m_spStatisticModule->AddFilterCriterior(DbSheet::GetEtDbId(item, "fieldId"), spCriteria);
        if (FAILED(hr))
            return hr;
    }

    KDbFilterOpType filterOp = DBFOT_And;
    hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterOpType(dataRange.field_str("filtersOp"), &filterOp);
    if (FAILED(hr))
        return hr;
    hr = m_spStatisticModule->SetFiltersOperator(filterOp);
    if (FAILED(hr))
        return hr;
    m_spStatisticModule->ModuleChange();
    return S_OK;
}

HRESULT KDbDashboardPluginWrapper::SetPluginConfigGroups(const binary_wo::VarObj& groups)
{
    bool isChange = false;
    HRESULT hr = IsPluginConfigGroupsChange(groups, isChange);
    if (FAILED(hr))
        return hr;
    if (!isChange)
        return S_OK;

    hr = m_spStatisticModule->ClearGroupConditions();
    if (FAILED(hr))
        return hr;
    IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
    IDBRecordsOrderManager* pMgr = m_spStatisticModule->GetMutableOrderManager();
    if (!pMgr)
        return E_FAIL;
    for (int i = 0, length = groups.arrayLength(); i < length; ++i)
    {
        binary_wo::VarObj group = groups.at(i);
        if (!group.has("fieldId") || !group.has("groupUnit") || !group.has("groupMode") || !group.has("sort"))
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        EtDbId fieldId = DbSheet::GetEtDbId(group, "fieldId");
        ks_stdptr<IDBRecordsOrderCondition> spCondition;
        hr = pMgr->AddGroupCondition(fieldId, &spCondition);
        if (FAILED(hr))
            return hr;
        KDBGroupUnit groupUnit = DBGU_Text;
        hr = pDecoder->DecodeGroupUnit(group.field_str("groupUnit"), &groupUnit);
        if (FAILED(hr))
            return hr;
        spCondition->SetUnit(groupUnit);

        PCWSTR groupMode = group.field_str("groupMode");
        bool splitMultiple = xstrcmp(groupMode, __X("Enumerated")) == 0;
        // 分组暂不支持拆分
        if (i >= 1 && splitMultiple)
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        spCondition->SetSplitMultiple(alg::bool2BOOL(splitMultiple));

        binary_wo::VarObj sort = group.get("sort");
        if (!sort.has("order") || !sort.has("sortType"))
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        bool ascending = xstrcmp(sort.field_str("order"), __X("Ascend")) == 0;
        // 维度
        if (i == 0)
        {
            m_spStatisticModule->SetSortAscending(ascending);
        }
        else
        {
            if (!ascending) // 分组暂不支持设置排序
                return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
            spCondition->SetAscending(alg::bool2BOOL(ascending));
        }

        ET_DBSheet_ChartSortType sortType = MappingSortType(sort);
        if (sortType == DbSheet_Chart_ST_NoSort || (i >= 1 && sortType!= DbSheet_Chart_ST_SortByHorizontalAxis))
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        hr = m_spStatisticModule->SetSortType(sortType);
        if (FAILED(hr))
            return hr;
    }
    if (FAILED(hr))
        return hr;
    m_spStatisticModule->ModuleChange();
    return S_OK;
}

HRESULT KDbDashboardPluginWrapper::SetPluginConfigSeries(const binary_wo::VarObj& series)
{
    bool isChange = false;
    HRESULT hr = IsPluginConfigSeriesChange(series, isChange);
    if (FAILED(hr))
        return hr;
    if (!isChange)
        return S_OK;

    hr = m_spStatisticModule->ClearStatisticField();
    if (FAILED(hr))
        return hr;
    // 字符串,表示统计记录总数
    if (series.type() == typeString && xstrcmp(series.value_str(), __X("COUNTA")) == 0)
    {
        hr = m_spStatisticModule->SetOnlyCountDimension(true);
        if (FAILED(hr))
            return hr;
    }
    else if (series.type() == typeArray)
    {
        hr = m_spStatisticModule->SetOnlyCountDimension(false);
        if (FAILED(hr))
            return hr;
        // 数组,表示统计指定字段
        for (int i = 0, length = series.arrayLength(); i < length; ++i)
        {
            binary_wo::VarObj item = series.at(i);
            if (!item.has("fieldId") || !item.has("rollup"))
                return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
            EtDbId fieldId = DbSheet::GetEtDbId(item, "fieldId");
            ET_DBSheet_StatisticOption option = DBSSO_null;
            hr = _appcore_GainEncodeDecoder()->DecodeStatisticOption(item.field_str("rollup"), &option);
            if (FAILED(hr))
                return hr;
            hr = m_spStatisticModule->AddStatisticField(fieldId, option);
            if (FAILED(hr))
                return hr;
        }
    }
    else
    {
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
    }
    m_spStatisticModule->ModuleChange();
    return S_OK;
}

HRESULT KDbDashboardPluginWrapper::IsPluginConfigDataRangeChange(const binary_wo::VarObj& dataRange, bool& isChange)
{
    if (!dataRange.has("filterInfo") || !dataRange.has("filtersOp"))
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;

    HRESULT hr = S_OK;
    binary_wo::VarObj filterInfo = dataRange.get("filterInfo");
    const IDbFilter* pDbFilter = m_spStatisticModule->GetConstFilter();
    if (!pDbFilter)
        return E_FAIL;
    if (pDbFilter->GetFiltersCount() != filterInfo.arrayLength())
    {
        isChange = true;
        return S_OK;
    }

    KDbFilterOpType filterOp = DBFOT_And;
    hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterOpType(dataRange.field_str("filtersOp"), &filterOp);
    if (FAILED(hr))
        return hr;
    if (filterOp != pDbFilter->GetOperator())
    {
        isChange = true;
        return S_OK;
    }

    for (int i = 0, length = filterInfo.arrayLength(); i < length; ++i)
    {
        binary_wo::VarObj item = filterInfo.at(i);
        if (!item.has("fieldId") || !item.has("criteria"))
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        const IDbFieldFilter* pFieldFilter = nullptr;
        m_spStatisticModule->GetConstFilter()->GetFilter(i, &pFieldFilter);
        if (!pFieldFilter)
            return E_FAIL;
        if (pFieldFilter->GetFieldId() != DbSheet::GetEtDbId(item, "fieldId"))
        {
            isChange = true;
            return S_OK;
        }

        ks_stdptr<IDbFilterCriteria> spNewCriteria;
        hr = CreateDbFilterCriteria(item.get("criteria"), m_pEtWorkbook->GetCoreWorkbook()->GetBook(), &spNewCriteria);
        if (FAILED(hr))
            return hr;
        const IDbFilterCriteria* pFilterCriteria = pFieldFilter->GetCriteria();
        if (!pFilterCriteria)
            return E_FAIL;

        if (spNewCriteria->GetCriteriaOp() != pFilterCriteria->GetCriteriaOp())
        {
            isChange = true;
            return S_OK;
        }

        if (spNewCriteria->GetValuesCount() != pFilterCriteria->GetValuesCount())
        {
            isChange = true;
            return S_OK;
        }

        for (int j = 0, valueCnt = spNewCriteria->GetValuesCount(); j < valueCnt; ++j)
        {
            if (!spNewCriteria->GetValue(j)->IsEqual(pFilterCriteria->GetValue(j)))
            {
                isChange = true;
                return S_OK;
            }
        }
    }
    return S_OK;
}

HRESULT KDbDashboardPluginWrapper::IsPluginConfigGroupsChange(const binary_wo::VarObj& groups, bool& isChange)
{
    // 第一项表示维度,第二项表示分组
    int groupCnt = groups.arrayLength();
    if (groupCnt == 0)
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;

    if (groupCnt > s_pluginGroupLimitNum)
        return E_DBSHEET_DASHBOARD_PLUGIN_GROUP_LIMIT;

    HRESULT hr = S_OK;
    IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
    const IDBRecordsOrderManager* pMgr = m_spStatisticModule->GetConstOrderManager();
    if (!pMgr)
        return E_FAIL;
    if (pMgr->GetGroupConditionCount() != groupCnt)
    {
        isChange = true;
        return S_OK;
    }

    for (int i = 0; i < groupCnt; ++i)
    {
        binary_wo::VarObj group = groups.at(i);
        if (!group.has("fieldId") || !group.has("groupUnit") || !group.has("groupMode") || !group.has("sort"))
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        EtDbId fieldId = DbSheet::GetEtDbId(group, "fieldId");
        const IDBRecordsOrderCondition* pCondition = pMgr->GetGroupCondition(i);
        if (fieldId != pCondition->GetKeyFieldId())
        {
            isChange = true;
            return S_OK;
        }

        KDBGroupUnit groupUnit = DBGU_Text;
        hr = pDecoder->DecodeGroupUnit(group.field_str("groupUnit"), &groupUnit);
        if (FAILED(hr))
            return hr;
        if (groupUnit != pCondition->GetUnit())
        {
            isChange = true;
            return S_OK;
        }

        PCWSTR groupMode = group.field_str("groupMode");
        bool splitMultiple = xstrcmp(groupMode, __X("Enumerated")) == 0;
        // 分组暂不支持拆分
        if (i >= 1 && splitMultiple)
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        if (alg::bool2BOOL(splitMultiple) != pCondition->IsSplitMultiple())
        {
            isChange = true;
            return S_OK;
        }

        binary_wo::VarObj sortObj = group.get("sort");
        if (!sortObj.has("order") || !sortObj.has("sortType"))
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        bool ascending = xstrcmp(sortObj.field_str("order"), __X("Ascend")) == 0;
        // 分组暂不支持设置排序
        if (i >= 1 && !ascending)
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        if (ascending != m_spStatisticModule->GetSortAscending())
        {
            isChange = true;
            return S_OK;
        }
        ET_DBSheet_ChartSortType sortType = MappingSortType(sortObj);
        if (sortType == DbSheet_Chart_ST_NoSort || (i >= 1 && sortType!= DbSheet_Chart_ST_SortByHorizontalAxis))
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        if (sortType != m_spStatisticModule->GetSortType())
        {
            isChange = true;
            return S_OK;
        }
    }
    return S_OK;
}

HRESULT KDbDashboardPluginWrapper::IsPluginConfigSeriesChange(const binary_wo::VarObj& series, bool& isChange)
{
    if (series.type() == typeString)
    {
        if (xstrcmp(series.value_str(), __X("COUNTA")) != 0)
            return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
        isChange = !m_spStatisticModule->GetOnlyCountDimension();
        return S_OK;
    }
    else if (series.type() == typeArray)
    {
        int seriesCnt = series.arrayLength_s();
        if (seriesCnt > s_pluginStatsLimitNum)
            return E_DBSHEET_DASHBOARD_PLUGIN_STATS_LIMIT;

        if (m_spStatisticModule->GetOnlyCountDimension())
        {
            isChange = true;
            return S_OK;
        }

        const IDbChartStatisticOptions* pStatisticOps = m_spStatisticModule->GetConstStatisticOptions();
        if (!pStatisticOps)
            return E_FAIL;
        if (pStatisticOps->Count() != seriesCnt)
        {
            isChange = true;
            return S_OK;
        }

        for (int i = 0, length = seriesCnt; i < length; ++i)
        {
            binary_wo::VarObj item = series.at_s(i);
            if (!item.has("fieldId") || !item.has("rollup"))
                return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
            const IDbChartStatisticOption* pStatisticOp = pStatisticOps->GetConstChartStatisticOption(i);
            if (pStatisticOp->GetFieldId() != DbSheet::GetEtDbId(item, "fieldId"))
            {
                isChange = true;
                return S_OK;
            }
            ET_DBSheet_StatisticOption option = DBSSO_null;
            HRESULT hr = _appcore_GainEncodeDecoder()->DecodeStatisticOption(item.field_str("rollup"), &option);
            if (FAILED(hr))
                return hr;
            if (pStatisticOp->GetStatisticOption() != option)
            {
                isChange = true;
                return S_OK;
            }
        }
    }
    return S_OK;
}

ET_DBSheet_ChartSortType KDbDashboardPluginWrapper::MappingSortType(const binary_wo::VarObj& param)
{
    PCWSTR sortTypeStr = param.field_str("sortType");
    if (xstrcmp(sortTypeStr, __X("Group")) == 0)
    {
        return DbSheet_Chart_ST_SortByHorizontalAxis;
    }
    else if (xstrcmp(sortTypeStr, __X("Value")) == 0)
    {
        return DbSheet_Chart_ST_SortByVerticalAxis;
    }
    return DbSheet_Chart_ST_NoSort;
}

HRESULT KDbDashboardPluginWrapper::SetCustomConfig(const binary_wo::VarObj& param, IEtRevisionContext* pCtx)
{
    constexpr static int kMaxDbDashboardPluginCustomKVLenth = 10000;

    // 命令回放时不做检查
    if (pCtx && pCtx->isExecDirect())
        return S_OK;

    if (!param.has("customConfig"))
        return E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID;
    if (ks_wcslen(param.field_str("customConfig")) > kMaxDbDashboardPluginCustomKVLenth)
        return E_DBSHEET_DASHBOARD_PLUGIN_CUSTOMKV_LIMIT;

    HRESULT hr = m_spWebExtension->SetProperty(__X("plugin-custom-kv"), param.field_str("customConfig"));
    if (FAILED(hr))
        return hr;
    m_spStatisticModule->ModuleChange();
    return S_OK;
}

}