﻿#include "etstdafx.h"
#include "webhook_data_create_helper_old.h"
#include "etcore/et_core_dbsheet.h"
#include "appcore/et_appcore_dbsheet.h"
#include "etcore/et_core_basic.h"
#include "etx_result.h"
#include "webhook_data_old.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "webhook_param_validator.h"
#include <vector>
#include "webhook/webhook_enum.h"

HRESULT WebhookDataCreateHelperOld::writeHookResult(INT code, PCWSTR message)
{
    m_pRes->beginStruct();
    m_pRes->addStringField(m_hookId.c_str(), "hook_id");
    m_pRes->addInt16Field(code, "code");
    m_pRes->addStringField(message, "msg");
    m_pRes->endStruct();
    return S_OK;
}

HRESULT WebhookDataCreateHelperOld::CreateWebhookData(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData** ppData, bool update, bool add)
{
    HRESULT hr = S_OK;
    m_hookId = __X("");
    m_pRes = &res;
    WebhookParamsValidator webhookParamsValidator(m_pBook);
    bool b = webhookParamsValidator.ValidateParams(hook, res, nullptr);
    if (!b)
        return E_FAIL;

    m_hookId = hook.field_str("hook_id");
    ks_stdptr<IWebhookData> spData;
    binary_wo::VarObj ruleObj = hook.get("rule");
    WebStr ruleType = ruleObj.field_str("type");
    Et_DbSheet_WebhookType dbType = DbSheet_WebhookType_invalid;
    _appcore_GainEncodeDecoder()->DecodeWebhookType(ruleType, &dbType);
    Et_WebhookType etType = Et_WebhookType_invalid;
    _appcore_GainEncodeDecoder()->DecodeEtWebhookType(ruleType, &etType);
    if (dbType != DbSheet_WebhookType_invalid)
    {
        hr = createDbData(dbType, hook, &spData);
        if (FAILED(hr))
            return hr;
    }
    else if (etType != Et_WebhookType_invalid)
    {
        hr = createEtData(etType, hook, &spData, update, add);
        if (FAILED(hr))
            return hr;
    }
    if (!spData) return E_FAIL;

    writeHookResult(Webhook_Success, __X("success"));
    spData->SetId(m_hookId.c_str());
    *ppData = spData.detach();
    return S_OK;
}

HRESULT WebhookDataCreateHelperOld::CheckWebhookData(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData** ppData, bool update, bool add)
{
    HRESULT hr = S_OK;
    m_hookId = __X("");
    m_pRes = &res;
    WebhookParamsValidator webhookParamsValidator(m_pBook);
    bool b = webhookParamsValidator.ValidateParams(hook, res, nullptr);
    if (!b)
        return E_FAIL;

    m_hookId = hook.field_str("hook_id");
    ks_stdptr<IWebhookData> spData;
    binary_wo::VarObj ruleObj = hook.get("rule");
    WebStr ruleType = ruleObj.field_str("type");
    Et_DbSheet_WebhookType dbType = DbSheet_WebhookType_invalid;
    _appcore_GainEncodeDecoder()->DecodeWebhookType(ruleType, &dbType);
    Et_WebhookType etType = Et_WebhookType_invalid;
    _appcore_GainEncodeDecoder()->DecodeEtWebhookType(ruleType, &etType);
    if (dbType != DbSheet_WebhookType_invalid)
    {
        hr = checkDbData(dbType, hook, &spData);
        if (FAILED(hr))
            return hr;
    }
    else if (etType != Et_WebhookType_invalid)
    {
        hr = checkEtData(etType, hook, &spData, update, add);
        if (FAILED(hr))
            return hr;
    }
    if (!spData) return E_FAIL;

    writeHookResult(Webhook_Success, __X("success"));
    spData->SetId(m_hookId.c_str());
    *ppData = spData.detach();
    return S_OK;
}

HRESULT WebhookDataCreateHelperOld::createDbData(Et_DbSheet_WebhookType type, binary_wo::VarObj &hook, IWebhookData** ppData)
{
    ks_stdptr<IWebhookData> spOutData;
    switch (type)
    {
        case DbSheet_WebhookType_createSheet:
        {
            spOutData.attach(KS_NEW(WebhookDataCreateSheet));
            break;
        }
        case DbSheet_WebhookType_renameSheet:
        {
            ks_stdptr<WebhookDataRenameSheet> spData;
            spData.attach(KS_NEW(WebhookDataRenameSheet));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateSheetDesc:
        {
            ks_stdptr<WebhookDataUpdateSheetDesc> spData;
            spData.attach(KS_NEW(WebhookDataUpdateSheetDesc));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateSheetIcon:
        {
            ks_stdptr<WebhookDataUpdateSheetIcon> spData;
            spData.attach(KS_NEW(WebhookDataUpdateSheetIcon));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_removeSheet:
        {
            ks_stdptr<WebhookDataRemoveSheet> spData;
            spData.attach(KS_NEW(WebhookDataRemoveSheet));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_createView:
        {
            ks_stdptr<WebhookDataCreateView> spData;
            spData.attach(KS_NEW(WebhookDataCreateView));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_renameView:
        {
            ks_stdptr<WebhookDataRenameView> spData;
            spData.attach(KS_NEW(WebhookDataRenameView));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_removeView:
        {
            ks_stdptr<WebhookDataRemoveView> spData;
            spData.attach(KS_NEW(WebhookDataRemoveView));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_createField:
        {
            ks_stdptr<WebhookDataCreateField> spData;
            spData.attach(KS_NEW(WebhookDataCreateField));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateField:
        {
            ks_stdptr<WebhookDataUpdateField> spData;
            spData.attach(KS_NEW(WebhookDataUpdateField));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updatePrimaryField:
        {
            ks_stdptr<WebhookDataUpdatePrimaryField> spData;
            spData.attach(KS_NEW(WebhookDataUpdatePrimaryField));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_removeField:
        {
            ks_stdptr<WebhookDataRemoveField> spData;
            spData.attach(KS_NEW(WebhookDataRemoveField));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_createRecord:
        {
            ks_stdptr<WebhookDataCreateRecord> spData;
            spData.attach(KS_NEW(WebhookDataCreateRecord));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_removeRecord:
        {
            ks_stdptr<WebhookDataRemoveRecord> spData;
            spData.attach(KS_NEW(WebhookDataRemoveRecord));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateCells:
        {
            ks_stdptr<WebhookDataUpdateCells> spData;
            spData.attach(KS_NEW(WebhookDataUpdateCells));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateSheet:
        {
            ks_stdptr<WebhookDataUpdateSheet> spData;
            spData.attach(KS_NEW(WebhookDataUpdateSheet));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateFieldCells:
        {
            ks_stdptr<WebhookDataUpdateFieldCells> spData;
            spData.attach(KS_NEW(WebhookDataUpdateFieldCells));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateRecordCells:
        {
            ks_stdptr<WebhookDataUpdateRecordCells> spData;
            spData.attach(KS_NEW(WebhookDataUpdateRecordCells));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateRecords:
        {
            ks_stdptr<WebhookDataUpdateRecords> spData;
            spData.attach(KS_NEW(WebhookDataUpdateRecords));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateRecordsParent:
        {
            ks_stdptr<WebhookDataUpdateRecordsParent> spData;
            spData.attach(KS_NEW(WebhookDataUpdateRecordsParent));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_updateSheetsAllChange:
        {
            ks_stdptr<WebhookDataUpdateSheetsAllChange> spData;
            spData.attach(KS_NEW(WebhookDataUpdateSheetsAllChange));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case DbSheet_WebhookType_createAndFillInRecord:
        {
            ks_stdptr<WebhookDataCreateAndFillInRecord> spData;
            spData.attach(KS_NEW(WebhookDataCreateAndFillInRecord));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        default:
            return E_FAIL;
    }
    *ppData = spOutData.detach();
    return S_OK;
}

HRESULT WebhookDataCreateHelperOld::checkDbData(Et_DbSheet_WebhookType type, binary_wo::VarObj &hook, IWebhookData** ppData)
{
    return S_OK;
}

HRESULT WebhookDataCreateHelperOld::createEtData(Et_WebhookType type, binary_wo::VarObj &hook, IWebhookData** ppData, bool update, bool add)
{
    ks_stdptr<IWebhookData> spOutData;
    switch (type)
    {
        case Et_WebhookType_etUpdateSheet:
        {
            ks_stdptr<EtWebhookDataUpdateSheet> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateSheet));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }

        case Et_WebhookType_updateRanges:
        {
            //权限
            if (!hasEtUpdateHookPermission(update, add)) {
                writeHookResult(Webhook_PermissionDeny, __X("Webhook_PermissionDeny"));
                WOLOG_INFO << "[createEtData] updateRanges Webhook_PermissionDeny" << m_hookId.c_str();
                return E_FAIL;
            }
            ks_stdptr<EtWebhookDataUpdateRanges> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateRanges));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        
        case Et_WebhookType_updateCharts:
        {
            //权限
            if (!hasEtUpdateHookPermission(update, add)) {
                writeHookResult(Webhook_PermissionDeny, __X("Webhook_PermissionDeny"));
                WOLOG_INFO << "[createEtData] updateCharts Webhook_PermissionDeny" << m_hookId.c_str();
                return E_FAIL;
            }
            ks_stdptr<EtWebhookDataUpdateCharts> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateCharts));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etRemoveSheet:
        {
            ks_stdptr<EtWebhookDataRemoveSheet> spData;
            spData.attach(KS_NEW(EtWebhookDataRemoveSheet));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etInsertRows:
        {
            ks_stdptr<EtWebhookDataInsertRows> spData;
            spData.attach(KS_NEW(EtWebhookDataInsertRows));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etRemoveRows:
        {
            ks_stdptr<EtWebhookDataRemoveRows> spData;
            spData.attach(KS_NEW(EtWebhookDataRemoveRows));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etInsertCols:
        {
            ks_stdptr<EtWebhookDataInsertCols> spData;
            spData.attach(KS_NEW(EtWebhookDataInsertCols));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etRemoveCols:
        {
            ks_stdptr<EtWebhookDataRemoveCols> spData;
            spData.attach(KS_NEW(EtWebhookDataRemoveCols));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etUpdateRange:
        {
            ks_stdptr<EtWebhookDataUpdateRange> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateRange));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etUpdateSheetsAllChange:
        {
            IWoETSettings *pWoSettings = _kso_GetWoEtSettings();
	        if (pWoSettings && !pWoSettings->IsEnableGridSheetSyncSheet())
                return E_FAIL;
            ks_stdptr<EtWebhookDataUpdateSheetsAllChange> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateSheetsAllChange));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
    }
    *ppData = spOutData.detach();
    return S_OK;
}

HRESULT WebhookDataCreateHelperOld::checkEtData(Et_WebhookType type, binary_wo::VarObj &hook, IWebhookData** ppData, bool update, bool add)
{
    ks_stdptr<IWebhookData> spOutData;
    switch (type)
    {
        case Et_WebhookType_etUpdateSheet:
        {
            ks_stdptr<EtWebhookDataUpdateSheet> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateSheet));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }

        case Et_WebhookType_updateRanges:
        {
            //权限
            if (isBookHasHidden()) {
                writeHookResult(Webhook_PermissionDeny, __X("Webhook_PermissionDeny"));
                WOLOG_INFO << "[createEtData] updateRanges Webhook_PermissionDeny" << m_hookId.c_str();
                return E_FAIL;
            }
            ks_stdptr<EtWebhookDataUpdateRanges> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateRanges));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        
        case Et_WebhookType_updateCharts:
        {
            //权限
            if (isBookHasHidden()) {
                writeHookResult(Webhook_PermissionDeny, __X("Webhook_PermissionDeny"));
                WOLOG_INFO << "[createEtData] updateCharts Webhook_PermissionDeny" << m_hookId.c_str();
                return E_FAIL;
            }
            ks_stdptr<EtWebhookDataUpdateCharts> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateCharts));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etRemoveSheet:
        {
            ks_stdptr<EtWebhookDataRemoveSheet> spData;
            spData.attach(KS_NEW(EtWebhookDataRemoveSheet));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etInsertRows:
        {
            ks_stdptr<EtWebhookDataInsertRows> spData;
            spData.attach(KS_NEW(EtWebhookDataInsertRows));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etRemoveRows:
        {
            ks_stdptr<EtWebhookDataRemoveRows> spData;
            spData.attach(KS_NEW(EtWebhookDataRemoveRows));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etInsertCols:
        {
            ks_stdptr<EtWebhookDataInsertCols> spData;
            spData.attach(KS_NEW(EtWebhookDataInsertCols));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etRemoveCols:
        {
            ks_stdptr<EtWebhookDataRemoveCols> spData;
            spData.attach(KS_NEW(EtWebhookDataRemoveCols));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
        case Et_WebhookType_etUpdateRange:
        {
            ks_stdptr<EtWebhookDataUpdateRange> spData;
            spData.attach(KS_NEW(EtWebhookDataUpdateRange));
            spData->Init(m_pBook);
            bool b = spData->ValidateParams(hook, *m_pRes);
            if (!b)
                return E_FAIL;

            spOutData = spData;
            break;
        }
    }
    *ppData = spOutData.detach();
    return S_OK;
}

bool WebhookDataCreateHelperOld::hasEtUpdateHookPermission(bool update, bool add)
{
    if (!update) return true;
    if (!add) return true;
    ks_stdptr<IWebhookManager> spWebhookMgr;
    m_pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    return !spWebhookMgr->isBookHasHidden();
}

bool WebhookDataCreateHelperOld::isBookHasHidden()
{
    ks_stdptr<IWebhookManager> spWebhookMgr;
    m_pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    return spWebhookMgr->isBookHasHidden();
}