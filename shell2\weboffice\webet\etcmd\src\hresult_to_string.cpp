﻿#include "etstdafx.h"
#include "hresult_to_string.h"

namespace
{
struct String
{
	PCWSTR wstring;
	PCSTR string;
};

#define WO_HRESULT(hr) case hr: return String{__X(#hr), #hr}
String _getErrorString(HRESULT hr)
{
	switch(hr)
	{
		WO_HRESULT(E_FAIL);

		WO_HRESULT(E_AREA_ALTERPIVOTCELL);
		WO_HRESULT(E_AREA_ALTERMERGECELL);
		WO_HRESULT(E_AREA_ALTERARRAYFML);
		WO_HRESULT(E_IO_INVALIDPASTERG);
		WO_HRESULT(E_IO_MULTY_PASTERG);
		WO_HRESULT(IO_E_NEED_PERMISSION);
		WO_HRESULT(IO_E_SDSERVER_ERROR);
		WO_HRESULT(IO_E_SEC_NO_READ_PERMISSION);
		WO_HRESULT(IO_E_NOT_SUPPORT_SECURITY_DOC);
		WO_HRESULT(IO_E_OPENCANCEL);
		WO_HRESULT(IO_E_NEED_MODIFY_PASSWORD);
		WO_HRESULT(IO_E_INVALID_MODIFY_PASSWORD);
		WO_HRESULT(E_ACCESSDENIED);
		WO_HRESULT(E_AREA_HASUSEDCELLS);
		WO_HRESULT(E_NAME_ALREADY_EXIST);
		WO_HRESULT(E_OPERATE_INVALID_SHEET); 
		WO_HRESULT(E_OPERATE_INVALID_RANGE);
		WO_HRESULT(E_CALC_COMPILE);
		WO_HRESULT(E_AREA_ALTERLISTOBJECT);
		WO_HRESULT(E_AREA_ALTERAUTOFILTER);
		WO_HRESULT(E_AREA_INCLUDEARRAYFMLA);
		WO_HRESULT(E_AREA_INCLUDEPIVOTTBL);
		WO_HRESULT(E_AREA_INCLUDELISTOBJECT);
		WO_HRESULT(E_DELETE_DATABASE_TABLE);
		WO_HRESULT(E_INVALID_DATABASE_TABLE);
		WO_HRESULT(E_AREA_LISTOBJECT_OVERLAP);
		WO_HRESULT(E_OPERATION_RESIZE_LISTOBJECT);
		WO_HRESULT(E_OPERATION_CONVER_MULTI_LISTOBJ);
		WO_HRESULT(E_AREA_INCLUDELISTTITLE);
		WO_HRESULT(E_CREATE_LISTOBJ_SINGLEROW);
		WO_HRESULT(E_ET_SORT_WITH_MERGECELL);
		WO_HRESULT(E_IO_OVERLAPPASTERG);
		WO_HRESULT(E_INVALID_REFERENCE);
		WO_HRESULT(E_INVALIDARG);
		WO_HRESULT(E_INVALID_CHAR);
		WO_HRESULT(E_NAME_CONFLICT);
		WO_HRESULT(E_NOT_HAVE_PERMISSION);
		WO_HRESULT(E_NOTIMPL);
		WO_HRESULT(E_NEED_SAME_MERGE_SIZE);

		// Pivot Table
		WO_HRESULT(E_PIVOTTABLE_16384DENIED);
		WO_HRESULT(E_PIVOTTABLE_DENIED_OVERLAPPED_OTHER);
		WO_HRESULT(E_PIVOTTABLE_DENIED_EDIT_BECAUSE_DENAY_UPDATE);
		WO_HRESULT(E_PIVOTTABLE_DENIED_EDIT_BECAUSE_GROUP_OR_CALC_FLD);
		WO_HRESULT(E_PIVOTTABLE_DENIED_EDIT_THIS_PART);
		WO_HRESULT(E_PIVOTTABLE_DENIED_EDIT_BECAUSE_GROUP_SHEETS);
		WO_HRESULT(E_PIVOTTABLE_DENIED_EDIT_BECAUSE_SHARED_WORKBOOK);
		WO_HRESULT(E_PIVOTTABLE_DENIED_MOVE_TO_THIS_AXIS);
		WO_HRESULT(E_PIVOTTABLE_DENIED_MOVE_TO_COL_AXIS_MORE_THAN_8000);
		WO_HRESULT(E_PIVOTTABLE_DENIED_MOVE_TO_COL_AXIS_MORE_THAN_16384);
		WO_HRESULT(E_PIVOTTABLE_DENIED_MOVE_TO_AXIS_TOO_MUCH_ITEM_COMPATIBLE);
		WO_HRESULT(E_PIVOTTABLE_DENIED_INPUT_TEXT_WITH_CALCITEM);
		WO_HRESULT(E_PIVOTTABLE_DENIED_LOCATION_UNFIT);
		WO_HRESULT(E_PIVOTTABLE_CONFLICT_CALCITEM_AND_FIELD_MULTIUSEED);
		WO_HRESULT(E_PIVOTTABLE_CONFLICT_CALCITEM_AND_VALUE_SUBTOTAL);
		WO_HRESULT(E_PIVOTTABLE_CONFLICT_CALCITEM_AND_CUSTOM_SUBTOTAL);
		WO_HRESULT(E_PIVOTTABLE_CONFLICT_CALCITEM_AND_GROUP_FIELD);
		WO_HRESULT(E_PIVOTTABLE_CONFLICT_CALCITEM_AND_GROUP_MOVE_VALUE);
		WO_HRESULT(E_PIVOTTABLE_CONFLICT_FIELD_NAME_EXIST);
		WO_HRESULT(E_PIVOTTABLE_CPL_ITEM_NOT_FOUND);
		WO_HRESULT(E_PIVOTTABLE_CPL_ITEM_AMBIGUOUS);
		WO_HRESULT(E_PIVOTTABLE_CPL_ITEM_NOT_IN_SPEC_FIELD);
		WO_HRESULT(E_PIVOTTABLE_CPL_REFER_TO_MULTI_ITEMS);
		WO_HRESULT(E_PIVOTTABLE_CPL_HAS_REF_NAME_ARRAY);
		WO_HRESULT(E_PIVOTTABLE_CPL_FUNC_ILLEGAL);
		WO_HRESULT(E_PIVOTTABLE_CPL_FAIL);
		WO_HRESULT(E_PIVOTTABLE_GROUP_CONFLICT_FIELD_TOOMANYITEMS);
		WO_HRESULT(E_PIVOTTABLE_GROUP_INVALIDSELECTION);
		WO_HRESULT(E_PIVOTTABLE_FILTER_VALUE_PERCENT_ERROR);  //百分比值错误
		WO_HRESULT(E_PIVOTTABLE_CREATE_DENIED_INVALID_FIELD_NAME);
		WO_HRESULT(E_PIVOTTABLE_SOURCE_INVALID);
		WO_HRESULT(E_PIVOTTABLE_SOURCE_SINGLE_ROW);
		WO_HRESULT(E_PIVOTTABLE_REPORT_INVALID_NEED_REFRESH);
		WO_HRESULT(E_PIVOTTABLE_SOURCE_FROM_DISK_INVALID);
		WO_HRESULT(E_PIVOTTABLE_SOURCE_NOT_ON_DISK);
		WO_HRESULT(E_PIVOTTABLE_SOURCE_NAME_WITH_RELSHEET);
		WO_HRESULT(E_PIVOTTABLE_SOURCE_INVALID_NAME);
		WO_HRESULT(E_PIVOTTABLE_CANNOT_SHOWDETAIL);
		WO_HRESULT(E_PIVOTTABLE_FIELD_ITEM_NAME_CANNOT_EMPTY);
		WO_HRESULT(E_PIVOTTABLE_CONSOLIDATE_AT_LEAST_TWO_ROW);
		WO_HRESULT(E_PIVOTTABLE_CONSOLIDATE_INVALID_REF);
		WO_HRESULT(E_PIVOTTABLE_DENIED_MOVE_SUBTOTAL_OR_GRAND);
		WO_HRESULT(E_PIVOTTABLE_MASS_OPERATION);
		WO_HRESULT(E_PIVOTTABLE_OPERATION_CANCEL);
		WO_HRESULT(E_PIVOTTABLE_DENIED_OVERLAPPED_TABLE);
		WO_HRESULT(E_PIVOTTABLE_DENIED_TextToColumns);
		WO_HRESULT(E_PIVOTTABLE_CELL_CIRCLE_REFER);
		WO_HRESULT(E_PIVOTTABLE_FIELD_CIRCLE_REFER);
		WO_HRESULT(E_PIVOTTABLE_FMLA_CANNOT_BE_FIELDITEM_NAME);
		WO_HRESULT(E_PIVOTTABLE_SHEET_PROTECT);  //pivotcache中含保护工作表的数据透视表
		WO_HRESULT(E_PIVOTTABLE_DENIED_ADDITEM);
		WO_HRESULT(E_PIVOTTABLE_CANNOT_HIDE_SELECTION);
		WO_HRESULT(E_PIVOTTABLE_CANNOT_HIDE_PAGEALL);
		WO_HRESULT(E_PIVOTTABLE_CANNOT_HIDE_ALLITEMS);
		WO_HRESULT(E_PIVOTTABLE_CANNOT_HIDE_ALLITEMS_FIELD);
		WO_HRESULT(E_PIVOTTABLE_DENIED_CALCULATEDITEM_MOVE_TO_PAGE);
		WO_HRESULT(E_PIVOTTABLE_DATABASE_CONNECTION_IS_DELETED);
		WO_HRESULT(E_PIVOTTABLE_REMOVE_CALCITEM_BY_SAME_NAME);
		WO_HRESULT(E_PIVOTTABLE_MULTIPLE_SAVE_SOURCE);
		WO_HRESULT(E_PIVOTTABLE_MULTIPLE_NOT_SAVE_SOURCE);
		WO_HRESULT(E_PIVOTTABLE_NOT_SHOW_IN_BOUNDARY);
		WO_HRESULT(E_PIVOTTABLE_CACHE_SLICER_LINK);
		WO_HRESULT(E_PIVOTTABLE_ODBC_NO_DEFAULT);
		WO_HRESULT(E_PIVOTTABLE_REPLACE_CELL_CONTENT);
		WO_HRESULT(E_PIVOTTABLE_DENIED_CREATE_ON_DBSHEET);
		WO_HRESULT(E_PIVOTTABLE_DENIED_SOURCE);
		WO_HRESULT(E_PIVOTTABLE_CREATE_DENIED_EMPTY_FIELD_NAME);
		WO_HRESULT(E_PIVOTTABLE_REFRESH_DENIED_EMPTY_DATASOURCE);
		WO_HRESULT(E_PIVOTTABLE_DENIED_BOOK_NAME);
		WO_HRESULT(E_PIVOTTABLE_FILTER_VALUE_DOUBLE_ERROR);
		WO_HRESULT(E_PIVOTTABLE_FILTER_VALUE_ENDLESSTHANBEGIN);
		
		WO_HRESULT(E_ET_COMPILE_BRACKET_NO_PAIR);
		WO_HRESULT(E_ET_COMPILE_OPND_NEED_RIGHTBRACKET);
		WO_HRESULT(E_ET_COMPILE_BOPTR_NEED_OPND);
		WO_HRESULT(E_ET_COMPILE_FUNC_PARAM_ERROR);
		WO_HRESULT(E_ET_COMPILE_FUNC_NEST_ERROR);
		WO_HRESULT(E_ET_COMPILE_CONST_ARRAY);
		WO_HRESULT(E_ET_COMPILE_INVALID_ROWCOLNO);
		WO_HRESULT(E_ET_COMPILE_CELLTEXT_TOO_LONG);
		WO_HRESULT(E_ET_COMPILE_GENERAL_ERROR);
		WO_HRESULT(E_ET_COMPILE_STRING_IN_FORMULA_TOO_LONG);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_BOOK);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_COL_BOOK);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE_BOOK);
		WO_HRESULT(E_FORMULA_NOT_SUPPORTED_ON_HIDDEN_BOOK);
		WO_HRESULT(E_APPLY_CONTENT_NOT_SUPPORTED_ON_HIDDEN_BOOK);
		WO_HRESULT(E_ET_FUNCTION_INVALID);
		WO_HRESULT(E_NAME_ALREADY_EXIST_IN_TABLE);
		WO_HRESULT(E_HAS_FORMULA_REF_HIDDEN_PROP_RANGE);
		WO_HRESULT(E_FORMULA_HAS_FORBIDDEN_FUNC);
		WO_HRESULT(E_NOT_SUPPORTED_APPLY_RANGE_ON_HIDDEN_BOOK);
		WO_HRESULT(E_PROTECTION_AUTO_PASSEWORD_WRONG);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_RANGE_WITH_HYPERLINK);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_RANGE_WITH_HYPERLINK_RUNS);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_RANGE_WITH_MULTICELL_HYPERLINK);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_CELLIMAGE_SCRIPTRUNS);
		WO_HRESULT(E_MERGEFILES_OPEN_FILE_FAILED);
		WO_HRESULT(E_MERGEFILES_SOURCEBOOK_WITH_HIDDEN_RANGE);
		WO_HRESULT(E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND);
		WO_HRESULT(E_MERGEFILES_COPYDATA_FAILED);
		WO_HRESULT(E_MERGEFILES_NETFILE_INVALID);
		WO_HRESULT(E_IMPORT_OPEN_FILE_FAILED);
		WO_HRESULT(E_IMPORT_SOURCEBOOK_WITH_HIDDEN_RANGE);
        WO_HRESULT(E_OPERATION_NOT_SUPPORTED_READONLY_CLOSE_AUTOFILTER);
		WO_HRESULT(E_FORMULA_NOT_SUPPORTED_ON_SHARED_SHEET);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_SHARED_SHEET);
		WO_HRESULT(E_API_NOT_SUPPORTED_ON_FILE);
        WO_HRESULT(E_OPERATION_NOT_SUPPORTED_COMMA_IN_CUSTOM_LIST);
		WO_HRESULT(E_DATA_VALIDATION_CUSTOM_LIST_MODIFY_AFFECT_RANGE_VALUE);
		WO_HRESULT(E_SHEET_NO_PERMISSION);
		WO_HRESULT(E_ET_SHEET_COPY_REMOVE_INVALID);
		WO_HRESULT(E_DATA_VALIDATION_CUSTOM_LIST_MUST_SINGLE_LINE);
		WO_HRESULT(E_PROTECTION_SECRET_KEY_ERROR);
		WO_HRESULT(E_COPY_COLS_INTERSECT_WITH_LISTOBJ_BUT_NOT_CONTAIN);
	
		//flash fill
		WO_HRESULT(E_FLASHFILL_RANGE_INVALID);//选择区域不可用，无法执行快速填充
		WO_HRESULT(E_FLASHFILL_NO_EMPTY_CELLS);//选择区域没有空白单元格，无法执行快速填充
		WO_HRESULT(E_FLASHFILL_NO_EXAMPLE);//选择区域没有示例数据，无法执行快速填充

		WO_HRESULT(E_FLOAT_TO_CELLIMG_SAME_CELL);
		WO_HRESULT(E_FLOAT_TO_CELLIMG_HAS_CONTENT);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_FORM_SHEET);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_SECDOC);
		WO_HRESULT(E_CMD_CENSOR_FAILED);	// 命令审核未通过
		WO_HRESULT(E_FILESIZE_EXCEED_LIMIT);	// 文件大小超过限制

		WO_HRESULT(E_ET_PUBLISH_CHANGED);
		WO_HRESULT(E_ET_INQUIRER_FAILED_REC_HAS_HIDDEN);
		WO_HRESULT(E_ET_INQUIRER_FAILED_REC_TBL_HEAD);
		WO_HRESULT(E_ET_INQUIRER_FAILED_REC_TBLTITLE_HOR_MERGE);
		WO_HRESULT(E_ET_INQUIRER_FAILED_REC_TBL_COLS_EXCEEDS_THRESHOLD);
		WO_HRESULT(E_ET_INQUIRER_FAILED_REC_PHONE_COL);

		//dbSheet 
		WO_HRESULT(E_DBSHEET_DELETE_LAST_VIEW);
		WO_HRESULT(E_DBSHEET_VIEW_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_VIEW_NAME_CONFLICT);
		WO_HRESULT(E_DBSHEET_VIEW_INVALID_NAME);
		WO_HRESULT(E_DBSHEET_INVALID_INPUT);
		WO_HRESULT(E_DBSHEET_ALTER_PRIMARY_FIELD);
		WO_HRESULT(E_DBSHEET_ILLEGAL_PRIMARY_FIELD);
		WO_HRESULT(E_DBSHEET_FIELD_INVALID_NAME);
		WO_HRESULT(E_DBSHEET_FIELD_MISMATCH);
		WO_HRESULT(E_DBSHEET_FIELD_NAME_CONFLICT);
		WO_HRESULT(E_DBSHEET_FIELD_NO_MULTIPLE_NOTE);
		WO_HRESULT(E_DBSHEET_FILTER_INVALID);
		WO_HRESULT(E_DBSHEET_ABNORMAL_ID_IDX_MAP);
		WO_HRESULT(E_DBSHEET_SELECT_ITEM_INVALID);
		WO_HRESULT(E_DBSHEET_SELECT_ITEM_CONFLICT);
		WO_HRESULT(E_DBSHEET_SELECT_ITEM_EMPTY);
		WO_HRESULT(E_DBSHEET_SELECT_FIELD_BAN_ADDITEM_WHEN_INPUTTING);
		WO_HRESULT(E_DBSHEET_RANGE_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_PASTE_NEED_EXPAND_FIELDS);
		WO_HRESULT(E_DBSHEET_PASTE_NEED_EXPAND_RECORDS);
		WO_HRESULT(E_DBSHEET_PASTE_NEED_EXPAND);
		WO_HRESULT(E_DBSHEET_ALTER_AUTO_FIELD);
		WO_HRESULT(E_DBSHEET_LINK_NOT_MULTIPLE);

		WO_HRESULT(E_INVALID_REQUEST);
		WO_HRESULT(E_DBSHEET_RECORD_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_SET_PARENT_RECORD_NOT_SUPPORT);
		WO_HRESULT(E_DBSHEET_RECORD_EXCEED_MAX_LEVEL);
		WO_HRESULT(E_DBSHEET_FIELD_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_SHEET_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_AUTOMATION_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_DUE_DATE_AUTOMATION_EXCEEDED_LIMITS);
		WO_HRESULT(E_DBSHEET_EMPTY_CELL);
		WO_HRESULT(E_DBSHEET_UNSUPPORTED_MUTIPLE);

		WO_HRESULT(E_DBSHEET_USER_GROUP_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_USER_IN_OTHER_GROUP);
		WO_HRESULT(E_DBSHEET_REMOVE_DEFAULT_USER_GROUP);
		WO_HRESULT(E_DBSHEET_REMOVE_ADMIN_USER_GROUP);
		WO_HRESULT(E_DBSHEET_FORM_REQUIRED_FIELD);
		WO_HRESULT(E_DBSHEET_FORM_FORCE_HIDE);
		WO_HRESULT(E_DBSHEET_FORM_VER_CONFLICT);
		WO_HRESULT(E_DBSHEET_QUERY_PHONE_INVALID);

		WO_HRESULT(E_DBSHEET_AD_FAIL);
		WO_HRESULT(E_DBSHEET_AD_MANAGE);
		WO_HRESULT(E_DBSHEET_AD_EXPORT);
		WO_HRESULT(E_DBSHEET_AD_SHEET_ADD);
		WO_HRESULT(E_DBSHEET_AD_SHEET_REMOVE);
		WO_HRESULT(E_DBSHEET_AD_SHEET_EDIT);
		WO_HRESULT(E_DBSHEET_AD_SHEET_MANAGE);
		WO_HRESULT(E_DBSHEET_AD_VIEW_MANAGE);
		WO_HRESULT(E_DBSHEET_AD_FIELD_MANAGE);
		WO_HRESULT(E_DBSHEET_AD_FIELD_VISIT);
		WO_HRESULT(E_DBSHEET_AD_FIELD_EDIT);
		WO_HRESULT(E_DBSHEET_AD_RECORD_ADD);
		WO_HRESULT(E_DBSHEET_AD_RECORD_REMOVE);
		WO_HRESULT(E_DBSHEET_AD_RECORD_VISIT);
		WO_HRESULT(E_DBSHEET_AD_RECORD_EDIT);
		WO_HRESULT(E_DBSHEET_AD_RECORD_MOVE);
		WO_HRESULT(E_DBSHEET_AD_FIELD_PROTECT_CANNOT_PRIMARY);
		WO_HRESULT(E_DBSHEET_PERMISSION_CHART_NOT_ALLOW);
		WO_HRESULT(E_DBSHEET_PERMISSION_NOT_ALLOW_SHARE);
		WO_HRESULT(E_DBSHEET_PERMISSION_ID_NOT_EXIST);
		WO_HRESULT(E_DBSHEET_PERSONALVIEW_COUNT_LIMIT);
		WO_HRESULT(E_DBSHEET_AT_LEAST_ONE_COLLABORATIVE_VIEW);
		WO_HRESULT(E_DBSHEET_NAME_FIRST_CHAR_INVALID);
		WO_HRESULT(E_DBSHEET_NAME_CHAR_INVALID);
		WO_HRESULT(E_DBSHEET_DASHBOARD_COUNT_LIMIT);
		WO_HRESULT(E_DBSHEET_DASHBOARD_CHART_COUNT_LIMIT);
		WO_HRESULT(E_DBSHEET_DASHBOARD_MODULE_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_DASHBOARD_MODULE_NO_DBSHEET_DATASOURCE);
		WO_HRESULT(E_DBSHEET_DASHBOARD_MODULE_DATAPOINT_NOT_FOUND);
		WO_HRESULT(E_DBSHEET_SHEET_MISMATCH);
		WO_HRESULT(E_DBSHEET_SYNCSHEET_EXCEEDED_LIMITS);

		WO_HRESULT(E_DBSHEET_DATASOURCE_NOT_MATCH_UNIQUE_TO_PASTE_SPEC_FIELD);
		WO_HRESULT(E_DBSHEET_VALUE_NOT_UNIQUE_IN_FIELD);
		WO_HRESULT(E_DBSHEET_PASTECELL_ERROR_EXCEEDED_LIMITS);
		WO_HRESULT(E_DBSHEET_PASTECELL_DST_ONLY_ALLOW_UPLOAD_BY_PHONE);
		WO_HRESULT(E_DBSHEET_RANGEFILL_DST_ONLY_ALLOW_UPLOAD_BY_PHONE);
		WO_HRESULT(E_DBSHEET_PASTECELL_DST_ONLY_ALLOW_SCAN_BY_CAMERA);
		WO_HRESULT(E_DBSHEET_RANGEFILL_DST_ONLY_ALLOW_SCAN_BY_CAMERA);
		
		WO_HRESULT(E_DBSHEET_SET_UNIQUE_PROP_TP_NOT_SUPPORT);
		WO_HRESULT(E_DBSHEET_SET_UNIQUE_PROP_DEF_VAL_NOT_EMPTY);
		WO_HRESULT(E_DBSHEET_SET_UNIQUE_PROP_HAS_DUP_VAL);
		WO_HRESULT(E_DBSHEET_SET_UNIQUE_PROP_OTHER_HAS_SET);
		WO_HRESULT(E_DBSHEET_FAILED_CIRCULAR_REFERENCE);
		WO_HRESULT(E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT);
		WO_HRESULT(E_DBSHEET_SYNC_RESULT_TO_STAT_SHEET_CELLVALUE);

		// 仪表盘插件
		WO_HRESULT(E_DBSHEET_DASHBOARD_PLUGIN_COUNT_LIMIT);
		WO_HRESULT(E_DBSHEET_DASHBOARD_PLUGIN_CUSTOMKV_LIMIT);
		WO_HRESULT(E_DBSHEET_DASHBOARD_PLUGIN_STATISTIC_CONFIG_INVALID);
		WO_HRESULT(E_DBSHEET_DASHBOARD_PLUGIN_GROUP_LIMIT);
		WO_HRESULT(E_DBSHEET_DASHBOARD_PLUGIN_STATS_LIMIT);

		WO_HRESULT(E_DBSHEET_MODIFY_ORDER_FIELDS_NOT_MATCH_ALL_FIELDS);
		WO_HRESULT(E_DBSHEET_FORMULA_FIELD_NO_PERMISSION_IN_EDIT);

		WO_HRESULT(E_DBSHEET_WAIT_UPGRADE_PERMISSION_TIMEOUT);
		WO_HRESULT(E_DBSHEET_NO_PERMISSION_UPGRADE_ADVANCED_PERMISSION);
		WO_HRESULT(E_DBSHEET_MERGE_DB_REACH_MAX_SHEET_ROWS);

		WO_HRESULT(E_COMMAND_NOT_FOUND);

		WO_HRESULT(E_DBSHEET_EXCEEDED_MAX_ROWS);
		WO_HRESULT(E_DBSHEET_EXCEEDED_MAX_COLS);
		WO_HRESULT(E_DBSHEET_FIELD_TYPE_INCORRECT);
        WO_HRESULT(E_DBSHEET_DASHBOARD_FILTER_NOT_FOUND);
        WO_HRESULT(E_DBSHEET_DASHBOARD_FILTER_COUNT_LIMIT);
        WO_HRESULT(E_DBSHEET_DASHBOARD_INVALID_FILTER_NAME);
		// ksheet
		WO_HRESULT(E_KSHEET_NOT_DBSHEET);
		WO_HRESULT(E_KSHEET_SHEET_NOT_FOUND);
		WO_HRESULT(E_UNKNOWN_ENUM);
		WO_HRESULT(E_KSHEET_NOT_GRIDSHEET);
		WO_HRESULT(E_RANGE_INVALID);
        WO_HRESULT(E_QRCOL_NOT_FOUND);
		WO_HRESULT(E_KSHEET_COPY_FORMAT_ERROR);
		WO_HRESULT(E_KSHEET_RANGE_ERROR);
		WO_HRESULT(E_KSHEET_COPY_FAILED);
        WO_HRESULT(E_KSHEET_ADD_ROWS_HAS_DUP_VAL);
		WO_HRESULT(E_KSHEET_ADD_ROWS_ALL_DUP_VAL);
		WO_HRESULT(E_KSHEET_TITLE_RECORD_NULL);
		WO_HRESULT(E_KSHEET_TITLE_RECORD_OVERLIMIT);
		WO_HRESULT(E_KSHEET_TITLE_RECORD_EMPTY_CELL);
		WO_HRESULT(E_KSHEET_TITLE_UNKNOWN);

		// 独占单元格
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_ON_EXCLUSIVE);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_EXCLUSIVE_COL);
		WO_HRESULT(E_OPERATION_NOT_SUPPORTED_EXCLUSIVE_ROW);
		WO_HRESULT(E_OPERATION_EXCLUSIVE_ROW_EXCEED);
		WO_HRESULT(E_OPERATION_EXCLUSIVE_COL_EXCEED);
		WO_HRESULT(E_OPERATION_EXCLUSIVE_CELL_EXCEED);
		WO_HRESULT(E_OPERATION_SEIZE_EXCLUSIVE_RANGE);
		WO_HRESULT(E_OPERATION_CELLPIC2FLOATPIC_FAILED_ON_EXCLUSIVE);
		WO_HRESULT(E_OPERATION_FLOATPIC2CELLPIC_FAILED_ON_EXCLUSIVE);
		WO_HRESULT(E_OPERATION_EXCLUSIVE_EXPANDRANGE_INVALID);
		
		// sidebar folder tree
		WO_HRESULT(E_SIDEBARFOLDERTREE_ROOT);
		WO_HRESULT(E_SIDEBARFOLDERTREE_NOT_FOUND);
		WO_HRESULT(E_SIDEBARFOLDERTREE_ALREADY_EXIST);
		WO_HRESULT(E_SIDEBARFOLDERTREE_MAX_LEVEL);
		WO_HRESULT(E_SIDEBARFOLDERTREE_MAX_FOLDER_CNT);
		WO_HRESULT(E_SIDEBARFOLDERTREE_TYPE);
		WO_HRESULT(E_SIDEBARFOLDERTREE_FOLDER_NAME);
		WO_HRESULT(E_SIDEBARFOLDERTREE_ILLEGAL_ADD);
		WO_HRESULT(E_SIDEBARFOLDERTREE_ILLEGAL_MOVE);

		// 从GridSheet同步到db
		WO_HRESULT(E_DBSHEET_SYNC_GRIDSHEET_DATA_INVALID);
		WO_HRESULT(E_DBSHEET_SYNC_GRIDSHEET_ID_CONTAINER_INVALID);
		WO_HRESULT(E_DBSHEET_SYNC_GRIDSHEET_HEADER_ROW_INVALID);
		WO_HRESULT(E_DBSHEET_SYNC_GRIDSHEET_ID_CONTAINER_EMPTY);

		WO_HRESULT(E_DATAANALYZE_NO_TASKID);
		WO_HRESULT(E_DATAANALYZE_INVAILD_CONNID);
		WO_HRESULT(E_DATAANALYZE_PERMISSION_FORBIDDEN);
		WO_HRESULT(E_DATAANALYZE_FAILED);

		WO_HRESULT(E_DBSHEET_DASHBOARD_VIEW_COUNT_LIMIT);

		WO_HRESULT(E_IO_PASTE_CANCEL);
	default:
		ASSERT(!"unknown error code"); // 无法将当前错误码转换成字符串, 请完善
		return {__X("E_FAIL"), "E_FAIL"};
	}
}
};

PCWSTR GetErrWideString(HRESULT hr)
{
	return _getErrorString(hr).wstring;
}

PCSTR GetErrString(HRESULT hr)
{
	return _getErrorString(hr).string;
}
