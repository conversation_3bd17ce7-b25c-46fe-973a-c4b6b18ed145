﻿#include "range_subscription.h"
#include "remote_chart_helper.h"
#include <public_header/chart/src/model/kctnumberformat.h>
#include <etchart/kdatasourcehelper.h>
#include "webetlink.h"

extern Callback* gs_callback;

static const int MAX_SERIES_NAME_LEN = 255;
typedef chart::KDataSourceHelper::RCHiddenInfo RCHidden;

namespace wo {

inline bool RegionFromStref(IBookOp* pBookOp, const_token_ptr pToken, RCHidden& hidden, bool& bHidden)
{
    alg::const_stref_token_assist rcs(pToken);
    //隐藏、显示行列判定
    if (!chart::KDataSourceHelper::strefTokenToRange(pBookOp, &rcs, true, &hidden)) {
        return false;
    }

    bHidden = false;
    if (hidden.rVec.empty() || hidden.cVec.empty()) {
        bHidden = true;
        return true;
    }

    return true;
}

inline bool RegionFromVec(IBookOp* pBookOp, const_token_ptr pToken, std::vector<RCHidden>& vec, UINT& count)
{
    alg::const_vector_token_assist vecToken(pToken);
    int tokenCnt = vecToken.get_count();
    for (int i = 0; i < tokenCnt; ++i) {
        const_token_ptr token = vecToken.get_item(i);
        if (!alg::const_stref_token_assist::is_type(token))
            continue;

        alg::const_stref_token_assist rcs(token);
        RCHidden hidden;
        if (!chart::KDataSourceHelper::strefTokenToRange(pBookOp, &rcs, false, &hidden))
            return false;

        vec.push_back(hidden);
        count += hidden.GetWidth() * hidden.GetHeight();
    }

    return true;
}

inline void UpdateValue(IBook* pBook, chart::KCTSglCells* pCells, RCHidden* pHidden)
{
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(pHidden->idxBook, &spSheet);
    if (!spSheet) {
        return;
    }

    ks_stdptr<IBookOp> spBookOp;
    pBook->GetOperator(&spBookOp);

    int counter = 0;
    for (int row = pHidden->ResetEnumRow(); row >= 0; row = pHidden->GetNextEnumRow())
    for (int col = pHidden->ResetEnumCol(); col >= 0; col = pHidden->GetNextEnumCol())
    {

        IDX idxSheet = pHidden->idxSheet;
        IDX idxBook = pHidden->idxBook;

        et_sptr<chart::KCTCell> spX;
        if (!chart::KDataSourceHelper::getCellData(spBookOp, idxBook, idxSheet, row, col, false, &spX)) {
            return;
        }

        if (counter == 0 && !spX) {
            chart::KDataSourceHelper::ensureCellByNumFmt(spBookOp, nullptr, false, idxSheet, row, col, &spX);
        }

        if (spX) {
            pCells->AttachValue(counter, spX.detach());
        }

        counter++;
    }
}

bool hiddenValid(IBook* pBook, chart::KCTMultiSglCells* pMSC, RCHidden* pHidden, IBookOp** ppBookOp)
{
    if (!pBook || !pMSC || !pHidden) {
        return false;
    }

    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(pHidden->idxBook, &spSheet);
    if (!spSheet) {
        return false;
    }

    ks_stdptr<IBookOp> spBookOp;
    pBook->GetOperator(&spBookOp);

    *ppBookOp = spBookOp.detach();
    return true;
}

void doUpdateValueMulti(IBookOp* pBookOp, chart::KCTMultiSglCells* pMSC, RCHidden* pHidden, int row, int col, size_t position)
{
    IDX idxSheet = pHidden->idxSheet;
    IDX idxBook = pHidden->idxBook;

    et_sptr<chart::KCTCell> spX;
    if (!chart::KDataSourceHelper::getCellData(pBookOp, idxBook, idxSheet, row, col, false, &spX)) {
        return;
    }

    if (position == 0 && !spX) {
        chart::KDataSourceHelper::ensureCellByNumFmt(pBookOp, nullptr, false, idxSheet, row, col, &spX);
    }

    if (spX) {
        chart::KCTSglCells* pSC = new chart::KCTSglCells(1);
        pSC->AttachValue(0, spX.detach());
        pMSC->AttachSglCell(position, pSC);
    }
}

inline void updateValueMultiSingle(IBook* pBook, chart::KCTMultiSglCells* pMSC, RCHidden* pHidden, size_t& position)
{
    ks_stdptr<IBookOp> spBookOp;
    if (!hiddenValid(pBook, pMSC, pHidden, &spBookOp)) {
        return;
    }

    for (int row = pHidden->ResetEnumRow(); row >= 0; row = pHidden->GetNextEnumRow())
    for (int col = pHidden->ResetEnumCol(); col >= 0; col = pHidden->GetNextEnumCol(), ++position)
    {
        doUpdateValueMulti(spBookOp, pMSC, pHidden, row, col, position);
    }

}
inline void UpdateValueMultiRow(IBook* pBook, chart::KCTMultiSglCells* pMSC, RCHidden* pHidden, size_t& position)
{
    ks_stdptr<IBookOp> spBookOp;
    if (!hiddenValid(pBook, pMSC, pHidden, &spBookOp)) {
        return;
    }

    for (int col = pHidden->ResetEnumCol(); col >= 0; col = pHidden->GetNextEnumCol(), ++position)
    for (int row = pHidden->ResetEnumRow(); row >= 0; row = pHidden->GetNextEnumRow())
    {
        doUpdateValueMulti(spBookOp, pMSC, pHidden, row, col, position);
    }
}

inline void UpdateValueMultiCol(IBook* pBook, chart::KCTMultiSglCells* pMSC, RCHidden* pHidden, size_t& position)
{
    ks_stdptr<IBookOp> spBookOp;
    if (!hiddenValid(pBook, pMSC, pHidden, &spBookOp)) {
        return;
    }

    for (int row = pHidden->ResetEnumRow(); row >= 0; row = pHidden->GetNextEnumRow(), ++position)
    for (int col = pHidden->ResetEnumCol(); col >= 0; col = pHidden->GetNextEnumCol())
    {
        doUpdateValueMulti(spBookOp, pMSC, pHidden, row, col, position);
    }
}

inline bool CrossSheetInValid(IBook* pBook, const_token_ptr pToken)
{
    if (!alg::const_stref_token_assist::is_type(pToken)) {
        return false;
    }

    alg::const_stref_token_assist rcs(pToken);
    if (alg::STREF_THIS_BOOK == rcs.get_book_id()) {
        return false;
    }

    if (rcs.is_cell() || rcs.is_region()) {
        ks_stdptr<ISupBooks> spSupBooks;
        pBook->GetSupBooks(&spSupBooks);

        ks_stdptr<ISupBook> spSupbook;
        spSupBooks->GetSupBook(rcs.get_book_id(), &spSupbook);
        if (!spSupbook) {
            return false;
        }

        SUPBOOK_STAT state = sbsNone;
        spSupbook->GetStat(&state);
        if (state != sbsActive) {
            return false;
        }

        if (!spSupbook->IsSheetIdxValid(rcs.get_sheet_id())) {
            return true;
        }
    }

    return false;
}

RemoteCalcNotifyBase::RemoteCalcNotifyBase(SourceCalcType nCalcType, bool bRegion)
    : m_calcType(nCalcType)
    , m_idx(0)
    , m_bHidden(false)
    , m_bRegion(bRegion)
    , m_pBook(nullptr)
    , m_pAreaService(nullptr)
    , m_pSubsriber(nullptr)
    , m_producer(nullptr)
{

}

RemoteCalcNotifyBase::~RemoteCalcNotifyBase()
{
    UnRegNotifyAll();
}

void RemoteCalcNotifyBase::SetSubscription(SeriesRangeSubscription* pSubsriber)
{
    m_pSubsriber = pSubsriber;
}

void RemoteCalcNotifyBase::InitContext(IBook* pBook, const int sheetIdx, const int idx)
{
    m_pBook = pBook;
    m_sheetIdx = sheetIdx;
    m_idx = idx;
}

int RemoteCalcNotifyBase::GetIdx()
{
    return m_idx;
}

bool RemoteCalcNotifyBase::IsHidden()
{
    return m_bHidden;
}

IKRanges* RemoteCalcNotifyBase::GetRange()
{
    return m_spRanges;
}

const chart::KCTSglCells* RemoteCalcNotifyBase::GetValues()
{
    return m_spCache;
}

const QString RemoteCalcNotifyBase::GetNFmla()
{
    return m_nFmla;
}

const QString RemoteCalcNotifyBase::GetBFmla()
{
    return m_bFmla;
}

void RemoteCalcNotifyBase::SetDirection(const SeriesDirection dir)
{
    m_dir = dir;
}

SeriesDirection RemoteCalcNotifyBase::GetDirection()
{
    return m_dir;
}

void RemoteCalcNotifyBase::EnableRegion(bool bRegion)
{
    m_bRegion = bRegion;
}

const_token_ptr RemoteCalcNotifyBase::prepareUpdate()
{
    ks_stdptr<IBookOp> spBkOp;
	m_pBook->GetOperator(&spBkOp);

    ks_stdptr<IFormula> spFmla;
    spBkOp->CreateFormula(&spFmla);
    spFmla->SetFormulaContent(m_vecToken);

    modifyContext(spFmla);

    return m_token.tag();
}

bool RemoteCalcNotifyBase::onCalcUpdate(const_token_ptr pToken)
{
    if (!pToken) {
        pToken = prepareUpdate();
    }

    if (!pToken) {
        return false;
    }

    et_sptr<chart::KCTSglCells> spSC;
    switch (alg::GetExecTokenMajorType(pToken))
    {
        case alg::ETP_STREF:
            modifyByStref(pToken, &spSC);
            break;
        case alg::ETP_VECTOR:
            modifyByVector(pToken, &spSC);
            break;
        case alg::ETP_ERROR:
            break;
        default:
            modifyByDefault(pToken, &spSC);
            break;
    }

    if (spSC.get()) {
        m_spCache.reset(spSC.detach());
        //hidden
    }

    return true;
}

void RemoteCalcNotifyBase::modifyByStref(const_token_ptr pToken, chart::KCTSglCells** ppCells)
{
    RCHidden hidden;
    bool bHidden = false;

    ks_stdptr<IBookOp> spBookOp;
    m_pBook->GetOperator(&spBookOp);

    if (!RegionFromStref(spBookOp, pToken, hidden, bHidden)
        || bHidden) {
        return;
    }

    *ppCells = new chart::KCTSglCells(hidden.GetWidth() * hidden.GetHeight());
    UpdateValue(m_pBook, *ppCells, &hidden);
}

void RemoteCalcNotifyBase::modifyByVector(const_token_ptr pToken, chart::KCTSglCells** ppCells)
{
    UINT count = 0;
    std::vector<RCHidden> hiddenVec;

    ks_stdptr<IBookOp> spBookOp;
    m_pBook->GetOperator(&spBookOp);
    if (!RegionFromVec(spBookOp, pToken, hiddenVec, count)) {
        return;
    }

    if (count <= 0) {
        return;
    }

    size_t idx = 0;
    *ppCells = new chart::KCTSglCells(count);
    for (auto it = hiddenVec.begin(); it != hiddenVec.end(); ++it) {
        UpdateValue(m_pBook, *ppCells, &(*it));
    }
}

void RemoteCalcNotifyBase::modifyByDefault(const_token_ptr pToken, chart::KCTSglCells** ppCells)
{
    ks_stdptr<IBookOp> spBkOp;
    m_pBook->GetOperator(&spBkOp);

    BOOL b1904 = m_pBook->Is1904DateSystem();

    switch (alg::GetExecTokenMajorType(pToken)) {
        case alg::ETP_MATRIX:
            handleMatrix(pToken, ppCells);
            break;

        case alg::ETP_VINT:
        case alg::ETP_VDBL:
        case alg::ETP_VBOOL:
        case alg::ETP_VSTR:
            handleCommon(pToken, ppCells);
            break;
        default:
            break;
    }
}

IAreaService* RemoteCalcNotifyBase::GetAreaService()
{
    if (!m_pAreaService) {
        m_pBook->GetAreaService(&m_pAreaService);
    }

    return m_pAreaService;
}

void RemoteCalcNotifyBase::UnRegNotifyAll()
{
    for (auto it = m_hVec.begin(); it != m_hVec.end(); ++it) {
        HandleEle& ele = (*it);

        if (ele.bName) {
            GetAreaService()->UnRegisterWatchedNameRegion(ele.hd);
            continue;
        }

        GetAreaService()->UnRegisterImmobileRegion(ele.hd);
    }

    m_hVec.clear();
    m_spRanges.clear();
}

void RemoteCalcNotifyBase::ReRegHandle()
{
    UnRegNotifyAll();

    m_token.clear();
    BOOL bOffset = m_vecToken.is_offset();

    exec_token_vector tokenV;
    if (m_vecToken.size() == 1 && alg::const_vector_token_assist::is_type(m_vecToken.get(0)))
    {
        CreateInstantTokenVector(TRUE, &tokenV);
        alg::const_vector_token_assist vvv(m_vecToken.get(0));
        for (int i = 0; i < vvv.get_count(); ++i) {
            tokenV.add_manage(CloneExecTokenI(vvv.get_item(i)));
        }
    } else {
        tokenV = m_vecToken;
    }

    int tokenCount = tokenV.size();
    for (int i = 0; i < tokenCount; ++i)
    {
        const_token_ptr pToken = tokenV.get(i);
        if (!alg::const_stref_token_assist::is_type(pToken)) {
            continue;
        }

        alg::const_stref_token_assist rcs(pToken);
        int bookIdx = rcs.get_book_id();

        HandleEle tmpEle;
        tmpEle.idxFmla = i;
        if (rcs.is_def_name() && rcs.get_name_id() >= 0) {
            tmpEle.bName = true;
            GetAreaService()->RegisterWatchedNameRegion(TRUE, bookIdx, rcs.get_name_id(), this, &tmpEle.hd, TRUE);
        } else if ((rcs.is_cell() && rcs.is_valid_cell(bOffset)) ||
            (rcs.is_region() && rcs.is_valid_region(bOffset))) {

            RANGE rg(m_pBook->GetBMP());
            rg.FromStrefToken(pToken);
            GetAreaService()->RegisterImmobileRegion(TRUE, bookIdx, rg, this, &tmpEle.hd, TRUE);
        }

        if (tmpEle.hd) {
            m_hVec.push_back(tmpEle);
        }
    }
}

chart::ContextResult RemoteCalcNotifyBase::RegNotify(const QString& context, bool bUpdate, ITokenVectorInstant** ppIns)
{
    if (context.isEmpty()) {
        m_nFmla = QString();
        m_bFmla = QString();
        return chart::ContextResultOK;
    }

    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    ks_stdptr<IFormula> spFormula;
    CompileErrorCode compileCode = chart::KDataSourceHelper::qstringToFormula(spBookOp, m_sheetIdx, false, context, &spFormula, m_bFmla, QString(""));
    if (compileCode != COMPILE_SUCCESS) {
        return chart::ContextResultCompileError;
    }

    m_vecToken = nullptr;
    spFormula->GetTokenVecForce(&m_vecToken);

    ReRegHandle();

    chart::ContextResult result = modifyContext(spFormula);
    if (ppIns) {
        *ppIns = m_vecToken;
        (*ppIns)->AddRef();
    }

    if (bUpdate) {
        onCalcUpdate(m_token.tag());
    }

    return result;
}

HRESULT RemoteCalcNotifyBase::RegRegionMonitor(HANDLE hd, INT bookId, const RANGE& afterOp, RegionOperationCode code)
{
    for (auto iter = m_hVec.begin(); iter != m_hVec.end(); ++iter)
    {
        HandleEle& ele = (*iter);
        if (hd == ele.hd) {
            RANGE rg = afterOp;
            if (FAILED(GetAreaService()->UnRegisterImmobileRegion(hd))) {
                return E_FAIL;
            }
            ele.hd = nullptr;
            alg::mutable_stref_token_assist rms;
            if (m_vecToken.size() == 1 && alg::ETP_VECTOR == alg::GetExecTokenMajorType(m_vecToken.get(0)))
            {
                alg::mutable_vector_token_assist vec(m_vecToken.get_non_const(0));
                rms.set(vec.get_item_direct(ele.idxFmla));
            } else {
                rms.set(m_vecToken.get_non_const(ele.idxFmla));
            }

            rms.set_book_id(bookId);
            if (!rg.IsValid()) {
                rms.invalidate_row_col();
                m_hVec.erase(iter);

                return S_OK;
            }

            //undo/redo;
            GetAreaService()->RegisterImmobileRegion(TRUE, bookId, rg, this, &ele.hd, TRUE);
            rms.set_begin_sheet(rg.SheetFrom());
            rms.set_end_sheet(rg.SheetTo());

            if (rg.IsSingleCell()) {
                rms.set_end_row(alg::STREF_INV_RC);
				rms.set_end_col(alg::STREF_INV_RC);
				rms.mark_cell();
				rms.set_cell_row(rg.RowFrom());
				rms.set_cell_col(rg.ColFrom());
            } else {
                rms.mark_region();
				rms.set_begin_row(rg.RowFrom());
				rms.set_end_row(rg.RowTo());
				rms.set_begin_col(rg.ColFrom());
				rms.set_end_col(rg.ColTo());
            }

            return S_OK;
        }
    }

    return E_FAIL;
}

HRESULT RemoteCalcNotifyBase::RegWatchedName(HANDLE hd, INT bookId, INT nameId)
{
    for (auto iter = m_hVec.begin(); iter != m_hVec.end(); ++iter) {
        HandleEle& ele = (*iter);

        if (hd == ele.hd) {
            GetAreaService()->UnRegisterWatchedNameRegion(hd);
            ele.hd = nullptr;
            alg::mutable_stref_token_assist rms(m_vecToken.get_non_const(ele.idxFmla));
            rms.set_book_id(bookId);
            if (nameId >= 0) {
                GetAreaService()->RegisterWatchedNameRegion(TRUE, bookId, nameId, this, &ele.hd, TRUE);
            }
            rms.set_name_id(nameId);
            return S_OK;
        }
    }

    return E_FAIL;
}

void RemoteCalcNotifyBase::connectProducer(ChartMessgeProducer* producer)
{
    m_producer = producer;
}

STDIMP RemoteCalcNotifyBase::OnValueChange(HANDLE)
{
    if (m_bRegion) {
        return S_OK;
    }

    if (m_producer) {
        m_producer->BeginMarkDirty(m_idx, m_calcType);
        m_producer->EndMarkDirty();
    }
    return S_OK;
}

STDIMP RemoteCalcNotifyBase::OnRegionOperation(HANDLE hd, INT bookID, const RANGE& afterOp, RegionOperationCode code)
{
    if (!m_bRegion) {
        return S_OK;
    }

    HRESULT hr = RegRegionMonitor(hd, bookID, afterOp, code);
    if (FAILED(hr)) {
        return hr;
    }

    hr = OnFmlaChanged();
    if (SUCCEEDED(hr)) {
        if (m_pSubsriber) {
            m_pSubsriber->NotifyRangeDivide();
        }
    }
    return hr;
}

STDIMP RemoteCalcNotifyBase::OnNameIdChanged(HANDLE hd, INT bookID, INT nameID)
{
    if (!m_bRegion) {
        return S_OK;
    }

    HRESULT hr = RegWatchedName(hd, bookID, nameID);
    if (FAILED(hr)) {
        return hr;
    }

    if (m_producer) {
        m_producer->BeginMarkDirty(m_idx, m_calcType);
        m_producer->EndMarkDirty();
    }

    return OnFmlaChanged();
}

STDIMP RemoteCalcNotifyBase::OnCalculate(HANDLE hd)
{
    if (m_bRegion) {
        return S_OK;
    }

    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    ks_stdptr<IFormula> spFmla;
    spBookOp->CreateFormula(&spFmla);
    spFmla->SetFormulaContent(m_vecToken);

    chart::ContextResult result = modifyContext(spFmla);
    if (result == chart::ContextResultOK) {

        if (m_producer) {
            m_producer->BeginMarkDirty(m_idx, m_calcType);
            m_producer->EndMarkDirty();
        }
    }

    return (result == chart::ContextResultOK) ? S_OK : E_FAIL;
}

HRESULT RemoteCalcNotifyBase::OnFmlaChanged()
{
    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    ks_stdptr<IFormula> spFmla;
    spBookOp->CreateFormula(&spFmla);
    spFmla->SetFormulaContent(m_vecToken);

    ReRegHandle();

    chart::ContextResult result = modifyContext(spFmla);
    return (result == chart::ContextResultOK) ? S_OK : E_FAIL;
}

bool RemoteCalcNotifyBase::unSupportFmla(ITokenVectorInstant* pVec)
{
    if (!pVec) {
        return false;
    }

    exec_token_vector tokenV(pVec);
    for (int i = 0; i < tokenV.size(); ++i) {
        if (etexec::const_opt_token_assist::is_type(tokenV.get(i))) {
            etexec::const_opt_token_assist rco(tokenV.get(i));
            if (rco.get_opt() != (etexec::EON_BINARYOPT | etexec::EOP_UNION)) {
                return true;
            }
        } else if (etexec::const_function_token_assist::is_type(tokenV.get(i))) {
            return true;
        }
    }

    return false;
}

void RemoteCalcNotifyBase::handleMatrix(const_token_ptr pToken, chart::KCTSglCells** ppCells)
{
    alg::const_matrix_token_assist matrix(pToken);

    int height = matrix.get_height();
    int width = matrix.get_width();
    int count = width * height;
    if (count <= 0) {
        return;
    }

    ks_stdptr<IBookOp> spBkOp;
    m_pBook->GetOperator(&spBkOp);

    *ppCells = new chart::KCTSglCells(count);
    for (int row = 0; row < height; ++row) {
        for (int col = 0; col < width; ++col) {
            chart::KCTCell* pX = chart::KDataSourceHelper::getMatrixValidValue(spBkOp, matrix.get_item(col, row));

            int position = row * width + col;
            if (pX) {
                (*ppCells)->AttachValue(position, pX);
            }
        }
    }
}

void RemoteCalcNotifyBase::handleMatrix(const_token_ptr pToken, chart::KCTMultiSglCells** ppMSC)
{
    alg::const_matrix_token_assist matrix(pToken);

    int height = matrix.get_height();
    int width = matrix.get_width();
    int count = width * height;
    if (count <= 0) {
        return;
    }

    ks_stdptr<IBookOp> spBkOp;
    m_pBook->GetOperator(&spBkOp);

    *ppMSC = new chart::KCTMultiSglCells(count);
    for (int row = 0; row < height; ++row) {
        for (int col = 0; col < width; ++col) {
            et_sptr<chart::KCTSglCells> spSC(new chart::KCTSglCells(1));

            chart::KCTCell* pX = chart::KDataSourceHelper::getMatrixValidValue(spBkOp, matrix.get_item(col, row));

            int position = row * width + col;
            if (pX) {
                spSC->AttachValue(0, pX);
            }

            (*ppMSC)->AttachSglCell(position, spSC.detach());
        }
    }
}

void RemoteCalcNotifyBase::handleCommon(const_token_ptr pToken, chart::KCTSglCells** ppCells)
{
    et_sptr<chart::KCTSglCells> spSC(new chart::KCTSglCells(1));
    spSC->SetValue(0, pToken);

    *ppCells = spSC.detach();
}

void RemoteCalcNotifyBase::handleCommon(const_token_ptr pToken, chart::KCTMultiSglCells** ppMSC)
{
    *ppMSC = new chart::KCTMultiSglCells(1);
    et_sptr<chart::KCTSglCells> spSC(new chart::KCTSglCells(1));
    spSC->SetValue(0, pToken);

    (*ppMSC)->AttachSglCell(0, spSC.detach());
}

chart::ContextResult RemoteCalcNotifyBase::modifyContext(IFormula* pFmla, const QString& inputContext)
{
    exec_token_vector tokenV;
    pFmla->GetTokenVecForce(&tokenV);
    if (tokenV && unSupportFmla(tokenV)) {
        return chart::ContextResultInternalError;
    }

    bool bNameFmla = false;
    if (tokenV &&
        (tokenV.size() == 1) &&
        etexec::const_stref_token_assist::is_type(tokenV.get(0)))
    {
        etexec::const_stref_token_assist rcs(tokenV.get(0));
        bNameFmla = rcs.is_def_name();
    }

    CS_CALCULATE_PARAM calcParam(ccfNPRefer, m_sheetIdx, 0, 0);
    alg::managed_token_assist mToken;
    HRESULT hr = pFmla->Calculate(&mToken, calcParam);
    if (FAILED(hr)) {
        return chart::ContextResultInvalidFomular;
    }

    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    ks_bstr bstrNormal, bstrBook;
    CS_COMPILE_PARAM ccp(cpfRgForceSheet, m_sheetIdx, 0, 0);
    pFmla->GetFormula(&bstrNormal, ccp);
    ccp.of |= cpfRgForceBook;
    pFmla->GetFormula(&bstrBook, ccp);

    if (mToken.major_type() == etexec::ETP_ERROR)
    {
        m_nFmla = QString::fromUtf16(bstrNormal);
        m_bFmla = QString::fromUtf16(bstrBook);
        m_token.set(mToken.detach());
        return chart::ContextResultInvalidReferenceRange;
    }

    if (mToken.major_type() == etexec::ETP_STREF ||
        mToken.major_type() == etexec::ETP_VECTOR)
    {
        ks_stdptr<IKRanges> spRanges;
        chart::KDataSourceHelper::getTokenRanges(spBookOp, mToken.tag(), &spRanges);
        chart::ContextResult result = checkRangeType(spRanges);
        if (result != chart::ContextResultOK) {
            return result;
        }

        if (!m_spRanges.get()) {
            _etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&m_spRanges);
        }
        m_spRanges->CopyFrom(spRanges);

        m_nFmla = QString::fromUtf16(bstrNormal);
        m_bFmla = QString::fromUtf16(bstrBook);

        if (CrossSheetInValid(m_pBook, mToken)) {
            m_token.set(etexec::managed_error_token_assist().create().detach());
            return chart::ContextResultInvalidReferenceRange;
        }

        m_token.set(mToken.detach());
        return chart::ContextResultOK;

    } else if (m_token.major_type() == etexec::ETP_CELLINT) {
        m_token.set(etexec::managed_error_token_assist().create().detach());
        if (!inputContext.isEmpty()) {
            m_nFmla = inputContext;
            m_bFmla = inputContext;
        }
        return chart::ContextResultInvalidReferenceRange;
    }

    // 非引用区域
    QString oContext;
    if (inputContext.isEmpty()) {
        oContext = QString::fromUtf16(bstrNormal);
    }
    else
    {
        oContext = inputContext;
    }

    QString finalContext;
    chart::ContextResult checkResult = checkValueType(oContext, mToken.tag(), finalContext, bNameFmla);
    if (checkResult != chart::ContextResultOK) {
        return checkResult;
    }
    else
    {
        if (bNameFmla) {
            m_nFmla = QString::fromUtf16(bstrNormal);
            m_bFmla = QString::fromUtf16(bstrBook);
        } else {
            m_nFmla = finalContext;
            m_bFmla = finalContext;
        }
    }

    m_token.set(mToken.detach());
    return chart::ContextResultOK;
}

chart::ContextResult RemoteCalcNotifyBase::checkValueType(const QString &context, const_token_ptr token, QString &oContext, bool bNameFmla)
{
    return chart::ContextResultOK;
}

chart::ContextResult RemoteCalcNotifyBase::checkRangeType(IKRanges *pRanges)
{
    UINT count = 0;
	HRESULT hr = pRanges->GetCount(&count);
	if (count == 0) {
        return chart::ContextResultValueCannotEmpty;
    }

	if ((count > 0) && (pRanges->GetSameBookID() == etexec::STREF_INV_BOOK)) {
        return chart::ContextResultInvalidFomular;
    }

    int dirtyMask = 0;
    if (m_producer) {
        m_producer->GainRangeMask(m_idx);
    }

    for (size_t idx = 0; idx < count; ++idx) {
        const RANGE *range = nullptr;
        hr = pRanges->GetItem(idx, nullptr, &range);
        if (!range) {
            return ((dirtyMask == CalcNotifyValue) ? chart::ContextResultRefNotSingle : chart::ContextResultOK);
        }

        // 每个RANGE不能多Sheet
        if (range->SheetFrom() != range->SheetTo()) {
            return chart::ContextResultInvalidFomular;
        }

        if (m_bRegion) {
            continue;
        }

        // //必须是单行或单列
        if ((range->ColTo() > range->ColFrom()) &&
            (range->RowTo() > range->RowFrom()))
        {
            return chart::ContextResultRefNotSingle;
        }
    }

    return chart::ContextResultOK;
}

RemoteCalcNameNotify::RemoteCalcNameNotify(SourceCalcType nCalcType, bool bRegion)
    :RemoteCalcNotifyBase(nCalcType, bRegion)
{

}

std::vector<QString>& RemoteCalcNameNotify::GetNames()
{
    return m_names;
}

bool RemoteCalcNameNotify::onCalcUpdate(const_token_ptr pToken)
{
    if (!pToken) {
        pToken = prepareUpdate();
    }

    if (!pToken) {
        return false;
    }

    ks_stdptr<IBookOp> spBookOp;
    m_pBook->GetOperator(&spBookOp);

    BOOL b1904 = m_pBook->Is1904DateSystem();

    if (etexec::const_stref_token_assist::is_type(pToken))
    {
        etexec::const_stref_token_assist stRefToken(pToken);

        et_sptr<RANGE> spRg;
        if (!chart::KDataSourceHelper::strefTokenToRange(spBookOp, &stRefToken, &spRg)) {
            return false;
        }

        bool bExpand = true;
        if (m_dir == sdirColumn && spRg->RowFrom() == spRg->RowTo()) {
            bExpand = false;
        } else if (m_dir == sdirRow && spRg->ColFrom() == spRg->ColTo()) {
            bExpand = false;
        } else {
            et_sptr<chart::KCTCell> spX;
            chart::KDataSourceHelper::getCellData(spBookOp, stRefToken.get_book_id(), spRg->SheetFrom(), spRg->RowFrom(), spRg->ColFrom(), false, &spX);
            if (spX) {
                bExpand = false;
            }
        }

        if (bExpand) {
            return valueFromToken(stRefToken);
        }
    }

    //fetch name values
    et_sptr<chart::KCTSglCells> spCells;
    if (chart::KDataSourceHelper::getTokenValue(spBookOp, pToken, &spCells) && spCells) {
        int nCount = 0;

        et_sdptr<chart::IEnumCell> spEnum;
        spCells->CreateEnum(&spEnum);
        m_names.clear();
        for (spEnum->Reset(); spEnum->IsValid(); spEnum->Next()) {
            const chart::KCTCell* pX = spEnum->Current();
            if (!pX) {
                m_names.push_back(QString(""));
                continue;
            }

            QString qs = pX->GetFormatedQString(b1904);
            m_names.push_back(qs.left(MAX_SERIES_NAME_LEN));
        }
    }

    return true;
}

chart::ContextResult RemoteCalcNameNotify::checkRangeType(IKRanges *pRanges)
{
    UINT count = -1;
    HRESULT ret = pRanges->GetCount(&count);

    if (count <= 0) {
        return chart::ContextResultOK;
    }

    if (count > 0 && pRanges->GetSameBookID() == etexec::STREF_INV_BOOK) {
        return chart::ContextResultInvalidFomular;
    }

    if (count > 1) {
        return chart::ContextResultRefNotSingle;
    }

    const RANGE* range = nullptr;
    ret = pRanges->GetItem(0, nullptr, &range);
    if (!range) {
        return chart::ContextResultOK;
    }

    if (range->SheetFrom() != range->SheetTo()) {
        return chart::ContextResultInvalidFomular;
    }

    if (range->ColTo() > range->ColFrom() && range->RowTo() > range->RowFrom()) {
        return chart::ContextResultRefNotSingle;
    }

    return chart::ContextResultOK;
}

chart::ContextResult RemoteCalcNameNotify::checkValueType(const QString &context, const_token_ptr token, QString &oContext, bool bNameFormula)
{
    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    KCOMPTR(IFormula) ptrFormula;
    HRESULT hr = spBookOp->CreateFormula(&ptrFormula);
    if (FAILED(hr)) {
        return chart::ContextResultInvalidFomular;
    }

    COMPILE_RESULT compileRet;
    CS_COMPILE_PARAM ccp(cpfReForceResult | cpfText2Formular, 0, 0, 0);

    switch (etexec::const_token_assist(token).major_type())
    {
        case etexec::ETP_VINT:
        case etexec::ETP_VBOOL:
        case etexec::ETP_VDBL:
            {
                etexec::managed_token_assist textToken;
                hr = etexec::V_ToText(token, &textToken);
                if (SUCCEEDED(hr)) {
                    exec_token_vector etv;
                    CreateInstantTokenVector(TRUE, &etv);
                    etv.add_manage(textToken);
                    hr = ptrFormula->SetFormula(etv.detach(), ccp);
                }
            }
            break;
        case etexec::ETP_VSTR:
            hr = ptrFormula->SetFormula(krt::utf16(context), ccp, &compileRet);
            break;
        default:
            hr = E_FAIL;
            break;
    }

    chart::ContextResult result = chart::ContextResultInvalidFomular;
    if (SUCCEEDED(hr)) {
        ks_bstr bstrFmla;
        CS_COMPILE_PARAM ccp(cpfForceText | cpfText2Formular, 0, 0, 0);
        ptrFormula->GetFormula(&bstrFmla, ccp);

        oContext = QString::fromUtf16(bstrFmla.c_str());
        result = chart::ContextResultOK;
    }

    return result;
}

RemoteCalcCateNotify::RemoteCalcCateNotify(SourceCalcType nCalcType, bool bRegion)
    :RemoteCalcNotifyBase(nCalcType, bRegion)
{

}

bool RemoteCalcCateNotify::onCalcUpdate(const_token_ptr pToken)
{
    if (!pToken) {
        pToken = prepareUpdate();
    }

    if (!pToken) {
        return false;
    }

    et_sptr<chart::KCTMultiSglCells> spMSC;
    switch (alg::GetExecTokenMajorType(pToken))
    {
        case alg::ETP_STREF:
            modifyByStref(pToken, &spMSC);
            break;
        case alg::ETP_VECTOR:
            modifyByVector(pToken, &spMSC);
            break;
        case alg::ETP_ERROR:
            break;
        default:
            modifyByDefault(pToken, &spMSC);
            break;
    }

    if (spMSC.get()) {
        m_spCategories.reset(spMSC.detach());
    }

    //hidden category
    return true;
}

const chart::KCTMultiSglCells* RemoteCalcCateNotify::GetCategory()
{
    return m_spCategories;
}

void RemoteCalcCateNotify::modifyByStref(const_token_ptr pToken, chart::KCTMultiSglCells** ppMSC)
{
    RCHidden hidden;
    alg::const_stref_token_assist rcs(pToken);

    ks_stdptr<IBookOp> spBkOp;
    m_pBook->GetOperator(&spBkOp);
    if (!chart::KDataSourceHelper::strefTokenToRange(spBkOp, &rcs, false, &hidden)) {
        return;
    }

    if (hidden.rVec.empty() || hidden.cVec.empty()) {
        return;
    }

    int count = 0;
    size_t position = 0;

    if (hidden.bSglRowCol) {
        count = hidden.GetHeight() * hidden.GetWidth();

        //仅支持单行或者单列的类别
        *ppMSC = new chart::KCTMultiSglCells(count);
        updateValueMultiSingle(m_pBook, *ppMSC, &hidden, position);
    } else {
        SeriesDirection dir = GetDirection();
        count = (dir == sdirColumn) ? hidden.GetHeight() : hidden.GetWidth();
        *ppMSC = new chart::KCTMultiSglCells(count);
        if (dir == sdirColumn) {
            UpdateValueMultiCol(m_pBook, *ppMSC, &hidden, position);
        } else {
            UpdateValueMultiRow(m_pBook, *ppMSC, &hidden, position);
        }
    }
}

void RemoteCalcCateNotify::modifyByVector(const_token_ptr pToken, chart::KCTMultiSglCells** ppMSC)
{
    int count = 0;
    SeriesDirection dir = GetDirection();

    ks_stdptr<IBookOp> spBkOp;
    m_pBook->GetOperator(&spBkOp);

    std::vector<RCHidden> hiddenVec;

    alg::const_vector_token_assist vecToken(pToken);
    int tokenNum = vecToken.get_count();
    for (int i = 0; i < tokenNum; ++i) {
        const_token_ptr pToken = vecToken.get_item(i);

        if (!alg::const_stref_token_assist::is_type(pToken)) {
            continue;
        }

        alg::const_stref_token_assist rcs(pToken);
        RCHidden hidden;
        if (!chart::KDataSourceHelper::strefTokenToRange(spBkOp, &rcs, false, &hidden)) {
            return;
        }

        if (hidden.rVec.empty() || hidden.cVec.empty()) {
            continue;
        }

        hiddenVec.push_back(hidden);
        if (dir == sdirColumn) {
            count += hidden.GetHeight();
        } else {
            count + hidden.GetWidth();
        }
    }

    if (count > 0) {
        size_t position = 0;
        *ppMSC = new chart::KCTMultiSglCells(count);
        for (auto it = hiddenVec.begin(); it != hiddenVec.end(); ++it) {
            if (dir == sdirColumn) {
                UpdateValueMultiCol(m_pBook, *ppMSC, &(*it), position);
            } else {
                UpdateValueMultiRow(m_pBook, *ppMSC, &(*it), position);
            }
        }
    }
}

void RemoteCalcCateNotify::modifyByDefault(const_token_ptr pToken, chart::KCTMultiSglCells** pMSC)
{
    switch (alg::GetExecTokenMajorType(pToken))
    {
        case alg::ETP_MATRIX:
            handleMatrix(pToken, pMSC);
            break;

        case alg::ETP_VINT:
        case alg::ETP_VDBL:
        case alg::ETP_VBOOL:
        case alg::ETP_VSTR:
            handleCommon(pToken, pMSC);
            break;
        default:
            break;
    }
}

chart::ContextResult RemoteCalcCateNotify::checkRangeType(IKRanges *pRanges)
{
    if (!pRanges) {
        return chart::ContextResultOK;
    }

    UINT count = 0;
    HRESULT hr = pRanges->GetCount(&count);

    if (count > 0 && pRanges->GetSameBookID() == etexec::STREF_INV_BOOK) {
        return chart::ContextResultInvalidFomular;
    }

    for (size_t idx = 0; idx < count; ++idx) {
        const RANGE* pRange = nullptr;
        hr = pRanges->GetItem(idx, nullptr, &pRange);
        if (pRange->SheetFrom() != pRange->SheetTo()) {
            return chart::ContextResultInvalidFomular;
        }
    }

    return chart::ContextResultOK;
}

chart::ContextResult RemoteCalcCateNotify::checkValueType(const QString &context, const_token_ptr token, QString &oContext, bool bNameFmla)
{
    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    et_sptr<chart::KCTSglCells> spCell;
    if (!chart::KDataSourceHelper::getLiteralCells(spBookOp, token, &spCell)) {
        return chart::ContextResultInternalError;
    }

    et_sdptr<chart::IEnumCell> spEnum;
    spCell->CreateEnum(&spEnum);
    for (spEnum->Reset(); spEnum->IsValid(); spEnum->Next()) {
        const chart::KCTCell* pCell = spEnum->Current();
        if (chart::KCTCell::IsValidData(pCell)) {
            if (!bNameFmla) {
                if (pCell->IsString()) {
                    oContext.append("\"" + QString::fromUtf16(pCell->GetString()) + "\",");
                } else {
                    oContext.append(pCell->GetFormatedQString(FALSE) + ",");
                }
            }
            continue;
        }

        return chart::ContextResultInternalError;
    }

    if (!bNameFmla) {
        oContext = oContext.mid(0, oContext.length() - 1);
    }

    return chart::ContextResultOK;
}

bool RemoteCalcNameNotify::valueFromToken(etexec::const_stref_token_assist &stRefToken)
{
    return true;
}

SeriesRangeSubscription::SeriesRangeSubscription(RemoteChartLinkManager* pManager)
    : m_producer(nullptr)
    , m_pManager(pManager)
    , m_b1904(FALSE)
    , m_chartType(chart::SurfaceWireFrame)
    , m_nGroups(0)
    , m_pRegion(nullptr)
    , m_bModified(false)
{
    EnsureProducer();
}

SeriesRangeSubscription::~SeriesRangeSubscription()
{
    if (m_producer) {
        delete m_producer;
        m_producer = nullptr;
    }

    for (auto it = m_notifyGroup.begin(); it != m_notifyGroup.end(); ++it) {
        delete (*it);
    }

    m_nGroups = 0;
    m_notifyGroup.clear();
}

void SeriesRangeSubscription::InitUser(const QString& connId, const QString& userId, const QString& cuid)
{
    m_connId = connId;
    m_userId = userId;

    if (m_producer) {
        m_producer->UpdateUUID(cuid);
    }
}

void SeriesRangeSubscription::EnsureProducer()
{
    if (!m_producer) {
        m_producer = new ChartMessgeProducer();
        m_producer->AttachSubscriber(this);
    }
}

void SeriesRangeSubscription::ConnectProducer(NotifierGroup* group)
{
    RemoteCalcNameNotify* nameNotifier = group->nameNotifier;
    nameNotifier->connectProducer(m_producer);

    RemoteCalcCateNotify* cateNotifier = group->cateNotifier;
    cateNotifier->connectProducer(m_producer);

    RemoteCalcNotifyBase* valNotifier = group->valueNotifier;
    valNotifier->connectProducer(m_producer);

    RemoteCalcNotifyBase* bubbleNotifier = group->bubbleNotifier;
    bubbleNotifier->connectProducer(m_producer);
}

void SeriesRangeSubscription::SetGroupSize(const int size)
{
    m_nGroups = size;
}

int SeriesRangeSubscription::GetGroupSize()
{
    return m_nGroups;
}

NotifierGroup* SeriesRangeSubscription::GetNotifyGroup(const int idx, bool bInsert)
{
    if (m_notifyGroup.contains(idx)) {
        return m_notifyGroup.value(idx);
    }

    if (!bInsert) {
        return nullptr;
    }

    //no have
    NotifierGroup *group = new NotifierGroup();
    ConnectProducer(group);

    m_notifyGroup.insert(idx, group);
    return group;
}

void SeriesRangeSubscription::NotifyRangeDivide()
{
    NotifierGroup* pRegion = GetRegionGroup();

    RemoteCalcNameNotify* nameNotifier = pRegion->nameNotifier;
    QString nameFmla = nameNotifier->GetNFmla();

    RemoteCalcNotifyBase* valNotifier = pRegion->valueNotifier;
    QString valueFmla = valNotifier->GetNFmla();

    RemoteCalcCateNotify* cateNotifier = pRegion->cateNotifier;
    QString cateFmla = cateNotifier->GetNFmla();

    m_pManager->DivideByFmla(nameFmla, cateFmla, valueFmla);
}

NotifierGroup* SeriesRangeSubscription::GetRegionGroup()
{
    if (!m_pRegion) {
        m_pRegion = new NotifierGroup(true);
        RemoteCalcNameNotify* nameNotifier = m_pRegion->nameNotifier;
        nameNotifier->SetSubscription(this);

        RemoteCalcNotifyBase* valNotifier = m_pRegion->valueNotifier;
        valNotifier->SetSubscription(this);

        RemoteCalcCateNotify* cateNotifier = m_pRegion->cateNotifier;
        cateNotifier->SetSubscription(this);
    }

    return m_pRegion;
}

void SeriesRangeSubscription::UpdateRegion(SeriesDirection dir, const QString& nameFmla, const QString& valueFmla, const QString& cateFmla)
{
    NotifierGroup* pRegion = GetRegionGroup();

    RemoteCalcNameNotify* nameNotifier = pRegion->nameNotifier;
    nameNotifier->SetDirection(dir);
    nameNotifier->RegNotify(nameFmla, true);

    RemoteCalcCateNotify* cateNotifier = pRegion->cateNotifier;
    cateNotifier->SetDirection(dir);
    cateNotifier->RegNotify(cateFmla, true);

    RemoteCalcNotifyBase* valNotifier = pRegion->valueNotifier;
    valNotifier->SetDirection(dir);
    valNotifier->RegNotify(valueFmla, true);
}

ChartMessgeProducer* SeriesRangeSubscription::GetProducer()
{
    return m_producer;
}

BOOL SeriesRangeSubscription::b1904()
{
    return m_b1904;
}

void SeriesRangeSubscription::Setb1904(BOOL b1904)
{
    m_b1904 = b1904;
}

void SeriesRangeSubscription::SetChartType(int chartType)
{
    m_chartType = (chart::ChartType)chartType;
}

chart::ChartType SeriesRangeSubscription::GetChartType()
{
    return m_chartType;
}

void SeriesRangeSubscription::CheckProducer()
{
    bool isPivot = false;
    bool needUpdate = false;

    for (QMap<int, NotifierGroup*>::iterator it = m_notifyGroup.begin(); it != m_notifyGroup.end(); ++it)
    {
        NotifierGroup* group = it.value();
        int index = it.key();

        int dirtyMask = m_producer->GainRangeMask(index);

        RemoteCalcNameNotify* nameNotifier = group->nameNotifier;
        if (dirtyMask & CalcNotifyName) {
            needUpdate = nameNotifier->onCalcUpdate();
        }

        RemoteCalcCateNotify* cateNotifier = group->cateNotifier;
        if (dirtyMask & CalcNotifyCategory) {
            needUpdate = cateNotifier->onCalcUpdate();
        }

        RemoteCalcNotifyBase* valNotifier = group->valueNotifier;
        if (dirtyMask & CalcNotifyValue) {
            needUpdate = valNotifier->onCalcUpdate();
        }

        RemoteCalcNotifyBase* bubbleNotifier = group->bubbleNotifier;
        if (dirtyMask & CalcNotifyBubble) {
            needUpdate = bubbleNotifier->onCalcUpdate();
        }
    }

    if (needUpdate) {
        m_bModified = true;
        m_message = m_producer->create();
        m_producer->ResetMask();
    }
}

bool SeriesRangeSubscription::DoExportData()
{
    if (gs_callback && gs_callback->signal) {
        if (!m_bModified) {
            WebSlice dummy = {nullptr, 0};
            gs_callback->signal(nullptr, "et.exportchart", &dummy);
            return true;
        }

        chart::wo_transport::ThriftBuffer& buffer = m_message.buffer;
        WebSlice slice = {buffer.buffer, buffer.length};
        if (buffer.length <= 0) {
            m_bModified = false;
            return false;
        }

        gs_callback->signal(nullptr, "et.exportchart", &slice);
    }

    m_bModified = false;
    return true;
}

}
