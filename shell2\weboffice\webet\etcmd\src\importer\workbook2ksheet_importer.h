﻿#ifndef __WEBET_WORKBOOK2KSHEET_IMPORTER_H__
#define __WEBET_WORKBOOK2KSHEET_IMPORTER_H__

#include "workbook_importer.h"
#include "dbsheet/et2db_exporter.h"
#include "ksheet/appsheet_copysheet_helper.h"
#include "etcore/et_core_dbsheet_enum.h"
#include "appcore/et_appcore_enum.h"

namespace wo
{
class KEtRevisionContext;

class Workbook2KSheetImporter : public WorkbookImporter
{
public:
    Workbook2KSheetImporter(etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook, PCWSTR userId,
                            IDBProtectionJudgement* pProtectionJudgement, KEtRevisionContext* ctx, VarObj& param);
    void setClearDefaultValue(bool clearDefaultValue);
    void setIsSupportVideoImport(bool isSupportVideoImport);
    void setCopyFieldTitleFormat(bool bCopyFieldTitleFormat);
    void setCopyFieldTitleFormatToDefaultView(bool bCopyFieldTitleFormatToDefaultView);
    std::set<ET_DbSheet_FieldType> getUnsupportedDbFieldTypes();
    std::set<ET_DBSheet_ViewType> getUnsupportedDbViews();
    void setUnsupportedDbFieldTypes(const std::set<ET_DbSheet_FieldType>& unsupportedDbFieldTypes);
    void setUnsupportedDbViews(const std::set<ET_DBSheet_ViewType>& unsupportedDbViews);
    std::set<ks_wstring> getUnsupportedAttachmentFileSuffixTypes();
    void setUnsupportedAttachmentFileSuffixTypes(const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes);
    void SerialAppSharedInfo(binary_wo::VarObj* pObj) override;

private:
    HRESULT copySheet(etoldapi::_Worksheet* pSrcWorkSheet, VARIANT before, VARIANT after,
                      IKCoreObject** ppNewSheetObj) override;
    HRESULT onBeforeImport() override;
    HRESULT onAfterImportOneSheet(etoldapi::_Worksheet* pSrcWorkSheet,
                                  etoldapi::_Worksheet* pNewWorkSheet) override;
    HRESULT onAfterImport(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                          const std::vector<etoldapi::_Worksheet*>& newWorkSheets) override;
private:
    static void clearDefaultValue(IDBSheetOp* pSrcSheetOp);
    // 将ksheet不支持的db字段转化为其它字段
    HRESULT handleAllDbSheetFields(etoldapi::Worksheets * pWss, bool isCvtUnsupport);
    // 处理自定义属性中的附件
    void handleCustomStorageAttachments();
    // 处理AS不支持的视图
    HRESULT handleAllDbSheetViews(etoldapi::Worksheets * pWss, bool isCvtUnsupport);
    // 处理AS不支持的仪表盘组件
    HRESULT handleAllDbSheetWebExtensions();
    void convertUnsupportedDbFields(IDBSheetOp* pSrcSheetOp);
    HRESULT handleUnsupportedDbViews(ISheet* pSrcSheet);
    void collectDbAttachmentId(IDBSheetOp* pSrcSheetOp);
    void collectDbViewAttachmentId(IDBSheetOp* pSrcSheetOp);
    HRESULT copyOldDbSheets();
    HRESULT copyAppSheets(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                          const std::vector<etoldapi::_Worksheet*>& newWorkSheets);
    HRESULT copyDbDashBoardSheets(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                          const std::vector<etoldapi::_Worksheet*>& newWorkSheets);
    HRESULT copyFieldTitleFormatToDefaultView(etoldapi::_Worksheet* pWorkSheet, etoldapi::_Worksheet* pNewWorkSheet);
private:
    ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
    std::unordered_map<UINT, UINT> m_sheetIdMap;
    KEtRevisionContext* m_pCtx;
    bool m_clearDefaultValue{false};
    bool m_isSupportVideoImport{false};
    bool m_bCopyFieldTitleFormat{true};
    bool m_bCopyFieldTitleFormatToDefaultView{false};
    std::set<ET_DbSheet_FieldType> m_unsupportedDbFieldTypes{Et_DbSheetField_Note, Et_DbSheetField_Automations,
                                                             Et_DbSheetField_ParentRecord};
    std::set<ks_wstring> m_unsupportedAttachmentFileSuffixTypes{};
    std::vector<AppSharedInfo> m_appSharedInfo;
    std::set<ET_DBSheet_ViewType> m_unsupportedDbViews{et_DBSheetView_Calendar};
};

} // namespace wo

#endif
