﻿#ifndef __WEBOFFICE_WEBET_EXPORTCENSORDATA_H__
#define __WEBOFFICE_WEBET_EXPORTCENSORDATA_H__
#include "db/db_basic_itf.h"

class KDataRange;
namespace wo
{
    class KEtWorkbook;
    class KEtRevisionContext;
    class ShareLinkContentVisibilityChecker;
    //class KSerialWrapBinWriter;
    class ExportCensorData
    {
    public:
        ExportCensorData(KEtWorkbook* pWorkbook, QIODevice* ioDevice, PCWSTR shareId = nullptr, bool removeDu = false);
        HRESULT DoExportText(UINT sheetId = 0);
    private:
        class EnumEtDbCellValueAllStrs : public ICellValueAcpt
        {
        public:
            EnumEtDbCellValueAllStrs(ExportCensorData* exportWorker, IBookOp* pBookOp, IDX sheetIdx)
                : m_exporter(exportWorker), m_spBookOp(pBookOp), m_sheetIdx(sheetIdx)
            {
            }
            virtual ~EnumEtDbCellValueAllStrs() PURE;
        protected:
            void exportFormula(ROW row, COL col)
            {
                ks_bstr bstrCellFormula;
                HRESULT hr = m_spBookOp->GetCellFormulaText(m_sheetIdx, row, col, &bstrCellFormula, NULL);
                if (S_OK == hr)
                {
                    m_exporter->ExportString(bstrCellFormula);
                }
            }
        protected:
            ExportCensorData* m_exporter;
            ks_stdptr<IBookOp> m_spBookOp;
            IDX m_sheetIdx;
        };
        class EnumDbSheetCellValueAllStrs : public EnumEtDbCellValueAllStrs
        {
        public:
            EnumDbSheetCellValueAllStrs(ExportCensorData* exportWorker, IBookOp* pBookOp, IDX sheetIdx,
                                        IDBSheetOp* pSheetOp, const std::function<bool(EtDbId,EtDbId)>& checkCellVisible)
                    : EnumEtDbCellValueAllStrs(exportWorker, pBookOp, sheetIdx), m_spSheetOp(pSheetOp),
                      m_checkCellVisible(checkCellVisible)
            {
                m_recs = m_spSheetOp->GetAllRecords();
                m_flds = m_spSheetOp->GetAllFields();
            }

            STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
            {
                if (pToken)
                {
                    EtDbId recId = m_recs->IdAt(row);
                    EtDbId fldId = m_flds->IdAt(col);
                    if (m_checkCellVisible(fldId, recId))
                    {
                        ks_bstr bstrCellText;
                        // dbsheet 调 GetValueString 再取一次 string。效率较低，但无需修改已有接口
                        m_spSheetOp->GetValueString(recId, fldId, &bstrCellText);
                        m_exporter->ExportString(bstrCellText);
                        ks_bstr bstrAddress;
                        HRESULT hr = m_spSheetOp->GetHyperlinkAddress(recId, fldId, &bstrAddress);
                        if (SUCCEEDED(hr))
                            m_exporter->ExportString(bstrAddress);
                        exportFormula(row, col);
                        ExportDbHyperlinkAddress(pToken);
                    }
                }
                return 0;
            };

            void ExportDbHyperlinkAddress(const_token_ptr pToken)
            {
                if (pToken && alg::const_handle_token_assist::is_type(pToken))
                {
                    alg::const_handle_token_assist chta(pToken);
                    alg::TOKEN_HANDLE handle = chta.get_handle();
                    if (!handle || chta.get_handleType() != alg::ET_HANDLE_DBHYPERLINK)
                        return;

                    ks_stdptr<IDbHyperlinkHandle> spHyperlinkHandle = handle->CastUnknown();
                    m_exporter->ExportString(spHyperlinkHandle->GetAddress());
                }
            };
        private:
            ks_stdptr<IDBSheetOp> m_spSheetOp;
            const IDBIds *m_recs, *m_flds;
            const std::function<bool(EtDbId,EtDbId)>& m_checkCellVisible;
        };
        friend class EnumDbSheetCellValueAllStrs;
        class EnumEtCellValueAllStrs : public EnumEtDbCellValueAllStrs
        {
        public:
            EnumEtCellValueAllStrs(ExportCensorData* exportWorker, IBookOp* pBookOp, IDX sheetIdx)
                : EnumEtDbCellValueAllStrs(exportWorker, pBookOp, sheetIdx)
            {
            }

            STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
            {
                if (pToken)
                {
                    ks_bstr bstrCellText;
                    m_exporter->m_spStrTools->GetCellText(pToken, NULL, &bstrCellText);
                    m_exporter->ExportString(bstrCellText);

                    exportFormula(row, col);
                    return 0;
                }
                return 0;
            };
        };
        friend class EnumEtCellValueAllStrs;
    private:
        // 输出单个单元格
        HRESULT CollectCellData(etoldapi::_Worksheet* pWorksheet,
                                const std::function<bool(EtDbId,EtDbId)>& checkCellVisible);

        // 输出超链接
        HRESULT CollectHyperlinks(etoldapi::_Worksheet *pWorksheet);

        // 输出评论
        HRESULT CollectComments(etoldapi::_Worksheet *pWorksheet);
        HRESULT CollectCommentsWithoutVoice(etoldapi::_Worksheet *pWorksheet);

        // 输出数据有效性校验, 条件格式
        HRESULT CollectDataValidation(etoldapi::_Worksheet *pWorksheet, ICoreDataDumper*ptrDumper);

        // 输出数据透视表相关内容
        HRESULT CollectPivotTable(etoldapi::_Worksheet *pWorksheet);

        //输出名称管理
        HRESULT CollectDefineName();

        //输出图表,文本框等文本内容
        HRESULT CollectAllShapeText();

        // 输出WebExtension
        void CollectWebExtension(IKWebExtension* pWebExtension);

        // 输出文本组件内容
        void CollectRichTextContent(IKWebExtension* pWebExtension);

        void CollectDataRange(KDataRange* pDataRange);

        // 输出DbFilterCriteria
        HRESULT CollectDbFilterCriteria(const IDbFilterCriteria* criteria);

        // 输出DbFilter
        HRESULT CollectDbFilter(const IDbFilter* pFilter);

        // 输出自定义属性相关内容
        HRESULT CollectCustomStorage();

        // 输出仪表盘相关内容
        HRESULT CollectDashBoardSheet(etoldapi::_Worksheet *pWorksheet, ShareLinkContentVisibilityChecker* pVisibilityChecker);

        // 输出DbSheet相关内容
        HRESULT CollectDbSheet(etoldapi::_Worksheet* pWorksheet, ShareLinkContentVisibilityChecker* pVisibilityChecker);

        // 将收集到是数据输出到文档
        HRESULT ExportContent();

        // 收集视图的相关用户配置
        HRESULT CollectViewInnerSetting(IDBSheetView *pView);

        // 输出侧边栏文件夹名
        HRESULT CollectSidebarFolderTreeNames();

        void ExportString(PCWSTR);

        // 插入MSR_HANDLE
        void insertMSRHandle(alg::MSR_HANDLE handle)
        {
            if(m_MsrHandles.find(handle) != m_MsrHandles.end())
            {
                alg::msrUnreferStringResource(handle);
            }
            else
            {
                m_MsrHandles.insert(handle);
            }
        }

        void CollectShapeExtData(drawing::AbstractShape* pShape);
        void CollectQRCodeText(const QJsonObject& obj);
        void CollectBarCodeText(const QJsonObject& obj);

        void walkThroughTree(drawing::AbstractShape* shape);

        void hanldeChart(drawing::AbstractShape* shape);

        void CollectDbAutoLinkCondProps(const IDbAutolinkCondProps*);

    private:
        ks_stdptr<_Workbook> m_spWorkbook;
        ks_stdptr<IETStringTools> m_spStrTools;

        QTextStream m_QTextStream;
        bool m_bRemoveDuplicates;
        std::unordered_set<alg::MSR_HANDLE> m_MsrHandles;
        ks_wstring m_shareId;
    };

    class ExportAllImages
    {
    public:
        ExportAllImages(_Workbook* pWorkbook);

        virtual void DoExportAllImages();
        virtual void ExportCellImages();
        virtual void ExportFloatImages();
        void ExportBackgroundImage(ISheet *sheet);
        void DoExportRandomImages(UINT32 sampling_threshold, UINT32 image_max_size = 0); //导出随机数量的图片

    protected:
        void walkThroughTree(drawing::AbstractShape* shape);

    protected:
        ks_stdptr<_Workbook> m_spWorkbook;
        ks_stdptr<IKMediaManage> m_spMediaMgr;
    };

    class SharedLinkExporter
    {
    public:
        SharedLinkExporter(_Workbook* pWorkbook, PCWSTR shareId)
        : m_spWorkbook(pWorkbook)
        , m_shareId(shareId)
        {}

    protected:
        void ExportSharedLinkCellImages();
        void ExportSharedLinkFloatImages();
        bool IsSheetVisible(UINT sheetId);
        virtual void DealShape(drawing::AbstractShape* shape) = 0;

        _Workbook* GetWorkBook() { return m_spWorkbook; }

    protected:
        ks_stdptr<_Workbook> m_spWorkbook;
        ks_wstring m_shareId;
    };

    class ExportSharedLinkAllImages : public ExportAllImages, public SharedLinkExporter
    {
    public:
        ExportSharedLinkAllImages(_Workbook* pWorkbook, PCWSTR shareId)
            : ExportAllImages(pWorkbook)
            , SharedLinkExporter(pWorkbook, shareId)
        {}

        void ExportCellImages() override;
        void ExportFloatImages() override;

    protected:
        void DealShape(drawing::AbstractShape* shape) override;
    };

    class ExportAllAttachment
    {
    public:
        ExportAllAttachment(_Workbook* spWorkbook, KEtRevisionContext* ctx)
            : m_spWorkbook(spWorkbook)
            , m_ctx(ctx)
        {}

        virtual void CollectAllAttachment();

        std::vector<ks_wstring>  getAttachmentImageIds() const { return m_attachmentIdVec;}
        std::vector<ks_wstring>  getAttachmentVideoIds() const { return m_attachmentIdVideoVec;}
        std::vector<ks_wstring>  getCloudSpaceImageUrls() const { return m_urlVec;}

    protected:
        virtual void ExportAttachmentCellImages();
        virtual void ExportAttachmentFloatImages();
        virtual void ExportAttachmentAnnex();
        virtual void ExportAttachmentCommentImages();
        
        void ExportAttachmenBySheet(ISheet* spSheet);
        void walkThroughTree(drawing::AbstractShape* shape);
        void exportAttachmentPicData(const drawing::AbstractShape* shape);

		virtual bool checkSheetStatus(UINT sheetId) { return true; }

    protected:
        ks_stdptr<_Workbook> m_spWorkbook;
        KEtRevisionContext* m_ctx;
        std::vector<ks_wstring> m_attachmentIdVec; //附件
        std::vector<ks_wstring> m_attachmentIdVideoVec; //附件视频

        std::vector<ks_wstring> m_urlVec; //云空间图片
    };

    class ExportSharedLinkAllAttachment : public ExportAllAttachment, public SharedLinkExporter
    {
    public:
        ExportSharedLinkAllAttachment(_Workbook* spWorkbook, KEtRevisionContext* ctx, PCWSTR shareId)
            : ExportAllAttachment(spWorkbook, ctx)
            , SharedLinkExporter(spWorkbook, shareId)
        {}

    protected:
        void ExportAttachmentCellImages() override;
        void ExportAttachmentFloatImages() override;
        void ExportAttachmentAnnex() override;
        void DealShape(drawing::AbstractShape* shape) override;
		bool checkSheetStatus(UINT sheetId) override;
    };
}   // wo

#endif // __WEBOFFICE_WEBET_EXPORTCENSORDATA_H__