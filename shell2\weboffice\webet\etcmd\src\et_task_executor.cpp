#include "etstdafx.h"
#include "et_task_executor.h"
#include "kfc/string.h"
#include "workbook.h"
#include "applogic/et_applogic_helper.h"
#include "et_revision_context_impl.h"
#include "et_task_class.h"
#include "form/et_form_task_class.h"
#include "database/et_database_task_class.h"
#include "dbsheet/et_dbsheet_task_class.h"
#include "dbsheet/dashboard/db_dashboard_task_class.h"
#include "http/db_http_task_class.h"
#include "http/et_http_task_class.h"
#include "http/et_form_http_task_class.h"
#include "http/db_dashboard_http_task_class.h"
#include "http/http_error.h"
#include "et_task_peripheral.h"
#include <public_header/webcommon/src/security_doc.h>
#include "revision_ext.h"
#include "webbase/serialize_impl.h"
#include "util.h"
#include "webbase/memo_stat_common.h"
#include "hresult_to_string.h"
#include "krt/krtstring.h"

extern CoopCmd g_WebEtCoopCmd;
namespace wo
{

class TaskExecuteWrap // 更新视图筛选、排序、分组的辅助类
{
public:
    TaskExecuteWrap(wo::KEtWorkbook* pWb, EtTaskExecutor* pExecutor, EtTaskExecBase* pExec, KwCommand* cmd, KEtRevisionContext* ctx, HRESULT& hr)
		: m_wwb(pWb)
		, m_pExecutor(pExecutor)
		, m_pExec(pExec)
		, m_cmd(cmd)
		, m_ctx(ctx)
		, m_hr(hr)
    {
		m_hr = m_pExec->PreExecute(m_cmd, m_ctx);
		if (SUCCEEDED(hr))
		{
			WebStr cmdName = cmd->cast().get("name").value_str();
			m_hr = CheckFileExceedLimitSize(ctx, cmdName) ? E_FILESIZE_EXCEED_LIMIT : S_OK;
		}
    }
    ~TaskExecuteWrap()
    {
		try
		{
			m_hr = m_pExec->PostExecute(m_hr, m_cmd, m_ctx);
		}
		catch (et_exception ex)
		{
			WOLOG_ERROR << "TaskExecuteWrap Exception caught: " << ex.get_result();
			m_hr = ex.get_result();
		}
    }

    TaskExecuteWrap(const TaskExecuteWrap&) = delete;
    const TaskExecuteWrap& operator=(const TaskExecuteWrap&) = delete;

private:
	HRESULT CheckFileExceedLimitSize(KEtRevisionContext*, const ks_wstring&);

protected:
	EtTaskExecutor* m_pExecutor;
    EtTaskExecBase*	m_pExec;
	KwCommand* m_cmd;
	KEtRevisionContext* m_ctx;
	wo::KEtWorkbook* m_wwb;
	HRESULT& m_hr;
};

HRESULT TaskExecuteWrap::CheckFileExceedLimitSize(KEtRevisionContext* ctx, const ks_wstring& cmdName)
{
	if (ctx->isExecDirect()) return false;

	bool bExceed = m_wwb->isFileSizeExceedLimit();
	bool bCmdAllowed = 	m_pExecutor->IsCmdAllowed(cmdName);
	if (bExceed && !bCmdAllowed)
		return true;

	return false;
}


EtTaskExecutor::EtTaskExecutor(wo::KEtWorkbook* wwb)
  : m_wwb(wwb)
  , m_ctx(nullptr)
  , m_bDirty(false)
  , m_bChangeDisabled(false)
  , m_denyType(DenyType::NoDeny)
{
}

void EtTaskExecutor::Init()
{
	if (m_wwb->GetBMP()->bDbSheet)
		initDbExecMap();
	else if (m_wwb->GetBMP()->bKsheet)
	{
		// 此前db内的sheet操作, 譬如新建sheet, 使用形如sheets.add的tag, 它们与et的相关事务同名. as (之前称为ksheet) 融合 db 时, 
		// 为兼容命令回放, 在db中新增了一些兼容性的事务, 它们继续沿用sheets.add形式的tag, 同时为db提供形如dbsheets.add的新tag (dbsheets.add)
		// 由于as可以执行et及db的事务, 又因为它们以tag为key, 对于相同tag的这些sheet事务, 当时的策略是使用et的
		// 因此以下代码中, 先插入et的事务, 后插入db的事务, tag相同时, 后插入的事务因为std::unordered_map::insert的实现, 会插入失败
		initEtExecMap();
		initDbExecMap();

		// 对于"从文件导入"及"重新计算"功能, 此前它们都是et事务, 后来因业务需求, 简单地将它们"改造"成db事务并直接使用
		// 实际上在as中它们预期视作db事务而非et事务. 现将事务进行拆分, 提供db版本的事务, 且要求在as中使用db的事务
		// todo: 应当尽快设计另外的tag, 在as和db中以新tag调用事务, 而非使用tag相同的旧事务. 新事务上线一定时间后,
		// 可考虑删除旧的兼容事务, 不再支持相关的命令回放
		{
			std::shared_ptr<TaskExecImportWorkbookLegacy> ins(new TaskExecImportWorkbookLegacy(m_wwb));
			m_execMap[ks_wstring(ins->GetTag())] = ins;
		}
		{
			std::shared_ptr<TaskExecDbtBookRecalculateLegacy> ins(new TaskExecDbtBookRecalculateLegacy(m_wwb));
			m_execMap[ks_wstring(ins->GetTag())] = ins;
		}
	}
	else
		initEtExecMap();

	initOverSizeWhiteCmdSet();
}

EtTaskExecutor::~EtTaskExecutor()
{
}

#define LOG_WARN(info) WO_LOG_X(m_wwb->getLogger(), WO_LOG_WARN, (info))

WebInt EtTaskExecutor::Exec(
	KwTask* task, IRevisionContext* ctx, KwVersionManager* versionMgr, KwVersion::BanUndoType* banUndo, IUnknown** ppCustomVerInfo)
{
	_Workbook* workbook = m_wwb->GetCoreWorkbook();
	m_ctx = static_cast<KEtRevisionContext*>(ctx);
	HRESULT hr = E_FAIL;
	bool hasRecalculate = false;
	bool hasImportrangeUpdate = false;
	bool hasDynamicArrayFmlaResumeCalc = false;

	KwCommand* firstCmd = nullptr;
	EtTaskPeripheral* firstCmdPeri = nullptr;

	if (!task->empty()) {
		firstCmd = task->front();
		WebStr name = firstCmd->cast().get("name").value_str();
		ExecMap::iterator itFunc = m_execMap.find(name);
		if (itFunc != m_execMap.end()) {
			EtTaskExecBase* exec = itFunc->second.get();
			firstCmdPeri = exec->GetPeripheral();
		}
	}

	m_wwb->getBatchTransHelper().appBeginUndoTrans();
	
	auto it = task->begin();
	try
	{
		(*banUndo) = task->empty() || m_bChangeDisabled ? KwVersion::banUndoCur : KwVersion::banUndoNone;
		for (; it != task->end() && !m_bChangeDisabled;)
		{
			KwCommand* cmd = (*it);
			binary_wo::VarObj vCmd = cmd->cast();
			WebStr name = vCmd.get("name").value_str();
			binary_wo::VarObj param = vCmd.get("param");
			util::AutoCmdTraceId autoCmdTraceId(m_ctx, vCmd);
			util::CmdTimeMemStat cmdTimeStat(m_wwb, m_ctx, [this](unsigned int ms, unsigned int memUsedKb, unsigned int memKb) {
				this->m_wwb->coreMetric().addCmdTime(ms, memUsedKb, memKb);
			});
			cmdTimeStat.start(name, true);
			m_wwb->coreMetric().onBeforeCmdExec(name, m_ctx->getCmdTraceId());

			bool isRecalculate = false;
			if (xstrcmp(__X("book.recalculate"), name) == 0 || // 重算工作簿。直接触发计算，不需要在外层触发
				xstrcmp(__X("sheet.calculate"), name) == 0)    // 重算工作表。直接触发计算，不需要在外层触发
			{
				isRecalculate = true;
			}

			if (xstrcmp(__X("book.updateSupbookData"), name) == 0)
			{
				hasImportrangeUpdate = true;
			}

			bool isDynamicArrayFmlaResumeCalc = false;
			WebStr purpose = param.has("purpose") ? param.get("purpose").value_str() : NULL;
			if (purpose && xstrcmp(__X("dynamicArrayFmlaResumeCalc"), purpose) == 0)
			{
				isDynamicArrayFmlaResumeCalc = true;
			}

			CoopCmd* pRoot = NULL;
			if (!g_WebEtCoopCmd.empty())
				pRoot = g_WebEtCoopCmd.verifyName("commands");
				
			if (NULL != pRoot)
			{
				CoopCmd* pVerifyCmd = pRoot->verifyName(QString::fromUtf16(name).toLocal8Bit().data());
				cmd->setCoopComds(pVerifyCmd);
			}

			ExecMap::iterator itFunc = m_execMap.find(name);
			if (itFunc != m_execMap.end())
			{
				EtTaskExecBase* exec = itFunc->second.get();
				EtTaskPeripheral* peri = exec->GetPeripheral();
				m_wwb->getBatchTransHelper().tryBeginBatch(m_ctx, exec->GetBatchTask(), task);

				bool bDeny = false;
				if (peri != nullptr) peri->PreProgress(cmd, &bDeny, GainResItemForOthers(), GainResItemForSelf());
				if (bDeny)
				{
					m_denyType = DenyType::EverDeny;
					eraseFailedCmd(task, it, ctx, hr);
					m_wwb->getBatchTransHelper().tryEndBatch(m_ctx, task, hr, nullptr);
					LOG_WARN("mass cells changed, operation deny");
					break;
				}

				{{
					TaskExecuteWrap execWrap(m_wwb, this, exec, cmd, m_ctx, hr);
					if (SUCCEEDED(hr))
					{
						hr = (*exec)(cmd, m_ctx);
						if (SUCCEEDED(hr))
						{
							if (isRecalculate && hr == S_OK)
								hasRecalculate = true;
							
							if (isDynamicArrayFmlaResumeCalc && hr == S_OK)
								hasDynamicArrayFmlaResumeCalc = true;

							if (peri != nullptr)
								peri->cacheCurFilterHiddenIfNeeded(cmd, m_ctx);
						}
					}
				}}

				exec->AddDErrorStrDefault(hr);
				exec->AddCallbackId(cmd);

				if (peri != nullptr) peri->PostProgress(cmd);

				bool isRemoveCmd = false;
				m_wwb->getBatchTransHelper().tryEndBatch(m_ctx, task, hr, &isRemoveCmd);
				if (FAILED(hr))
				{
					it = eraseFailedCmd(task, it, ctx, hr);
				}
				else
				{
					if (isRemoveCmd)
					{
						delete (*it);
						it = task->erase(it);
					}
					else
						++it;
				}

				if (peri != nullptr && (*banUndo) == KwVersion::banUndoNone)
					(*banUndo) = peri->getBanUndo();
			}
			else
			{
				WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "exec command not found: %s", krt::fromUtf16(name).toUtf8().data());
				hr = E_COMMAND_NOT_FOUND;
				m_wwb->getBatchTransHelper().tryBeginBatch(m_ctx, nullptr, task);
				m_wwb->getBatchTransHelper().tryEndBatch(m_ctx, task, hr, nullptr);
				m_ctx->postExecute(hr);
				binary_wo::BinWriter *pResponse = m_ctx->getHttpResponse();
				KSerialWrapBinWriter acpt(*pResponse, m_ctx);
				AddHttpError(hr, &acpt, nullptr);
			}

			if (FAILED(hr)) break;

			m_bDirty = true;
		} // end for
	} // end try
	catch (et_exception ex)
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "Exception caught");
		hr = ex.get_result();
		binary_wo::BinWriter *pResponse = m_ctx->getHttpResponse();
		KSerialWrapBinWriter acpt(*pResponse, m_ctx);
		AddHttpError(hr, &acpt, nullptr);
		eraseFailedCmd(task, it, ctx, hr);
		m_wwb->getBatchTransHelper().tryEndBatch(m_ctx, task, hr, nullptr);
	}
	if (FAILED(hr))
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "Execution failed, code: %ld error: %s", hr, GetErrString(hr));

	const UINT64 calcComplexity = m_ctx->getBook()->GetWoStake()->GetCalcSerialComplexity();
	m_ctx->getBook()->GetWoStake()->ClearCalcSerialComplexity();
	bool calcResTooComplex = calcComplexity >= SerialComplexityThreshold;
	if (calcResTooComplex) // 不判断是否为 exec direct
	{
		m_ctx->addSerialComplexity(static_cast<UINT>(calcComplexity));
		WOLOG_INFO << "Calculate complexity: " << calcComplexity << ", threshold: " << SerialComplexityThreshold;
	}

	if ((m_ctx->getSerialComplexity() >= SerialComplexityThreshold 
		&& !m_ctx->isExecDirect()
		&& !m_wwb->getBatchTransHelper().isSkipSerialComplexity() && hr != E_IO_PASTE_CANCEL))
	{
		WOLOG_INFO << "Atom commands count is great! SerialComplexity: " << m_ctx->getSerialComplexity();
		if(MemoStatProcMemory() > 1024ULL * 1024ULL *1024ULL)
		{
			WOLOG_INFO << "ClearAllUndoRedoSteps!";
			m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(workbook);
		}
	}
	
	m_wwb->getBatchTransHelper().appEndUndoTrans(m_ctx, hr);
	m_wwb->updateCalcStatus(hasRecalculate || hasImportrangeUpdate || hasDynamicArrayFmlaResumeCalc, m_ctx);
	if (hasRecalculate)
	{
		ks_stdptr<KEtCustomVerInfo> m_spInfo;
		m_spInfo.attach(KS_NEW(KEtCustomVerInfo));
		m_spInfo->reset(hasRecalculate);
		*ppCustomVerInfo = m_spInfo.get();
		(*ppCustomVerInfo)->AddRef();
	}

	if (FAILED(hr))
	{
		util::ClearCollectedWebhookChange(m_wwb->GetCoreWorkbook()->GetBook());
	}

	// 目前用于单元格记录追踪， Exec 的 commands 参数如果有多条命令，commit version 默认按命令数递增（和服务端约定）
	// FillLog(KEtRevisionContext* ctx, KwTasks& tasks) 有多条命令时(多个task)，每个task会提交一次协作记录（服务端依赖这个递增协作记录版本）
	bool isKsheet = m_wwb->GetCoreWorkbook()->GetBook()->GetBMP()->bKsheet;
	if (isKsheet && SUCCEEDED(hr))
	{
		CommitID curCommitId = m_ctx->getCurCommitVersion();
		if (curCommitId >= 0 && !task->empty()) // task->empty() 为空不提交协作记录，参考 FillLog
		{
			m_ctx->setCurCommitVersion(curCommitId + 1);
		}
	}

	return FAILED(hr) ? WO_FAIL : WO_OK;
}

KwTask::iterator EtTaskExecutor::eraseFailedCmd(KwTask* task, KwTask::iterator failedIter, IRevisionContext* ctx, HRESULT hr)
{
	if (m_ctx)
	{
		KwCommand* cmd = (*failedIter);
		binary_wo::VarObj vCmd = cmd->cast();
		WebStr name = vCmd.get("name").value_str();

		QString collectName = krt::fromUtf16(name);
		m_ctx->collectTasksDirectFailedInfo(hr, collectName.toUtf8());
	}

	// 避免产生协作记录, see ExecDetail::FillLog
	ctx->setIsRealTransform(true);
	for (auto it = failedIter; it != task->end(); ++it) {
		delete (*it);
	}
	return task->erase(failedIter, task->end());
}

WebInt EtTaskExecutor::MakeEmptyTransact(IRevisionContext* ctx)
{
	APP_BeginUndoTrans(m_wwb->GetCoreWorkbook(), FALSE, nullptr);
	APP_EndUndoTRANS(S_OK, TRUE, FALSE);
	return WO_OK;
}

bool EtTaskExecutor::SerialCommand(KwCommand* cmd, ISerialAcceptor* acpt)
{
	WebStr name = cmd->cast().get("name").value_str();
	ExecMap::iterator itFunc = m_execMap.find(name);
	ASSERT(itFunc != m_execMap.end());
	EtTaskExecBase* exec = itFunc->second.get();
	return exec->SerialCommand(cmd, acpt);
}

bool EtTaskExecutor::IsChangeDisabled() const
{
	return m_bChangeDisabled;
}

bool EtTaskExecutor::IsDirty() const
{
	return m_bDirty;
}

void EtTaskExecutor::SetDirty(bool b)
{
	m_bDirty = b;
}

DenyType EtTaskExecutor::Deny() const
{
    return m_denyType;
}

void EtTaskExecutor::ClearDeny()
{
    m_denyType = DenyType::NoDeny;
}

bool EtTaskExecutor::IsCmdAllowed(const ks_wstring& cmdName)
{
	auto itor = m_overSizeWhiteCmdSet.find(cmdName);
	return itor != m_overSizeWhiteCmdSet.end();
}

template <typename ExecClass_>
void EtTaskExecutor::mapAddExecClass()
{
	std::shared_ptr<ExecClass_> ins(new ExecClass_(m_wwb));
	m_execMap.insert(std::make_pair(ks_wstring(ins->GetTag()), ins));
}

void EtTaskExecutor::initEtExecMap()
{
	mapAddExecClass<TaskExecSetCellFormula>();
	mapAddExecClass<TaskExecSetDuplicateRemind>();
	mapAddExecClass<TaskExecRangeInsert>();
    mapAddExecClass<TaskExecRangeInsertNumberCol>();
	mapAddExecClass<TaskExecRangeMergeCellsInsert>();
	mapAddExecClass<TaskExecRangeUnMergeInsert>();
	mapAddExecClass<TaskExecRangeDelete>();
	mapAddExecClass<TaskExecMultiRangeDelete>();
	mapAddExecClass<TaskExecRangeMerge>();
	mapAddExecClass<TaskExecMultiRangeMerge>();
	mapAddExecClass<TaskExecRangeMergeCells>();
	mapAddExecClass<TaskExecRangeIntelliMerge>();
	mapAddExecClass<TaskExecRangeUnMerge>();
	mapAddExecClass<TaskExecRangeSetRowHeight>();
	mapAddExecClass<TaskExecRangeSetColumnWidth>();
	mapAddExecClass<TaskExecRangeSetFormula>();
	mapAddExecClass<TaskExecRangeSetXf>();
	mapAddExecClass<TaskExecRangeClearContents>();
	mapAddExecClass<TaskExecRangeClearFormats>();
	mapAddExecClass<TaskExecRangeClear>();
	mapAddExecClass<TaskExecRangeUpdate>();
	mapAddExecClass<TaskExecRangeAutoFill>();
	mapAddExecClass<TaskExecRangeDblClickFill>();
	mapAddExecClass<TaskExecRangeFill>();
	mapAddExecClass<TaskExecRangeAutoFilter>();
	mapAddExecClass<TaskExecRangeSetAutoFilter>();
	mapAddExecClass<TaskExecFileSetAutoFilter>();
	mapAddExecClass<TaskExecRangePastText>();
	mapAddExecClass<TaskExecRangeAutoFit>();
	mapAddExecClass<TaskExecRangeReplace>();
	mapAddExecClass<TaskExecRangeSetHidden>();
	mapAddExecClass<TaskExecRangeSetValue2>();
	mapAddExecClass<TaskExecMultiRangeSetHidden>();
	mapAddExecClass<TaskExecRangeSetPreferredView>();
	mapAddExecClass<TaskExecRangeClearAllCFRules>();
	mapAddExecClass<TaskExecSheetsAdd>();
	mapAddExecClass<TaskExecSheetsMove>();
	mapAddExecClass<TaskExecSheetsCopy>();
	mapAddExecClass<TaskExecSheetsAddTemplate>();
	mapAddExecClass<TaskExecSheetSetVisible>();
	mapAddExecClass<TaskExecSheetSetStandardWidth>();
	mapAddExecClass<TaskExecSheetSetStandardHeight>();
	mapAddExecClass<TaskExecSheetAddHyperlink>();
	mapAddExecClass<TaskExecSheetEditHyperlink>();
	mapAddExecClass<TaskExecSheetDeleteHyperlink>();
	mapAddExecClass<TaskExecSheetReapplyFilter>();
	mapAddExecClass<TaskExecSheetCalculate>();
	mapAddExecClass<TaskExecEtAddWebExtension>();
	mapAddExecClass<TaskExecEtModifyWebExtension>();
	mapAddExecClass<TaskExecEtDeleteWebExtension>();
	mapAddExecClass<TaskExecEtBatchModifyWebExtension>();
	mapAddExecClass<TaskExecInsertPicture>();
	mapAddExecClass<TaskExecInsertPictures>();
	mapAddExecClass<TaskExecInsertPictureAttachment>();
	mapAddExecClass<TaskExecInsertPictureAttachments>();
	mapAddExecClass<TaskExecChangePicture>();
	mapAddExecClass<TaskExecChangePictureAttachment>();
	mapAddExecClass<TaskExecInsertProcessOn>();
	mapAddExecClass<TaskExecModifyProcessOn>();
	mapAddExecClass<TaskExecSetShapeAnchor>();
	mapAddExecClass<TaskExecSetShapeSize>();
	mapAddExecClass<TaskExecSetShapePos>();
	mapAddExecClass<TaskExecDeleteShape>();
	mapAddExecClass<TaskExecRangeInsertCommentItem>();
	mapAddExecClass<TaskExecRangDeleteCommentItem>();
	mapAddExecClass<TaskExecRangEditCommentItem>();
	mapAddExecClass<TaskExecRangSetCommentProp>();
	mapAddExecClass<TaskExecRangDeleteComment>();
	mapAddExecClass<TaskExecRangeSetCommentChainResolved>();
	mapAddExecClass<TaskExecRangeDeleteCommentChain>();
	mapAddExecClass<TaskExecRangPaste>();
	mapAddExecClass<TaskExecRangeSlice>();
	mapAddExecClass<TaskExecProofreadModifyText>();
	mapAddExecClass<TaskExecImportCsvFile>();
	mapAddExecClass<TaskExecRangFmtPaintPaste>();
	mapAddExecClass<TaskExecSheetSetName>();
	mapAddExecClass<TaskExecSheetDel>();
	mapAddExecClass<TaskExecRangeSort>();
	mapAddExecClass<TaskExecPutFreezePanes>();
	mapAddExecClass<TaskExecFreezeFirstVisibleRowOrCol>();
	mapAddExecClass<TaskExecToggleGroup>();
	mapAddExecClass<TaskExecSetCollapseByGroup>();
	mapAddExecClass<TaskExecSetCollapseByLevel>();
	mapAddExecClass<TaskExecSetTableStyle>();
	mapAddExecClass<TaskExecBatchSetTableStyle>();
	mapAddExecClass<TaskExecFormAddRow>();
	mapAddExecClass<TaskExecFormAddRows>();
	mapAddExecClass<TaskExecFormAddCol>();
	mapAddExecClass<TaskExecFormEditRow>();
	mapAddExecClass<TaskExecFormDeleteRow>();
	mapAddExecClass<TaskExecFormDeleteRows>();
	mapAddExecClass<TaskExecFormSort>();
	mapAddExecClass<TaskExecFormAddSheet>();
	mapAddExecClass<TaskExecFormAddName>();
	mapAddExecClass<TaskExecFormSetColumnWidth>();
	mapAddExecClass<TaskExecRangPasteSmart>();
	mapAddExecClass<TaskExecRangPasteSpecail>();
	mapAddExecClass<TaskExecRangPasteSpecailByHtml>();
	mapAddExecClass<TaskExecRangPasteSpreadSheetSmart>();
	mapAddExecClass<TaskExecRangeGainLinkName>();
	mapAddExecClass<TaskExecNewCFRule>();
	mapAddExecClass<TaskExecDelCFRule>();
	mapAddExecClass<TaskExecClearAllCFRules>();
	mapAddExecClass<TaskExecEditCFRule>();
	mapAddExecClass<TaskExecText2Col>();
	mapAddExecClass<TaskExecSwapCFRule>();
	mapAddExecClass<TaskExecSetDataValidation>();
	mapAddExecClass<TaskExecRangeRepairError>();
	mapAddExecClass<TaskExecSetDuplicateValueRemind>();
	mapAddExecClass<TaskExecClearDuplicateValueRemind>();
	mapAddExecClass<TaskExecRemoveDuplicates>();
	mapAddExecClass<TaskExecAddSmartAnalysisChart>();
	mapAddExecClass<TaskExecSheetDataProof>();
	mapAddExecClass<TaskExecSheetBeautify>();
	mapAddExecClass<TaskExecSheetBeautifyAIFormat>();
	mapAddExecClass<TaskExecAddChart>();
	mapAddExecClass<TaskExecChartSetDataSource>();
	mapAddExecClass<TaskExecChartSetSeriesDataSource>();
	mapAddExecClass<TaskExecCategoryDataSource>();
	mapAddExecClass<TaskExecNewSeries>();
	mapAddExecClass<TaskExecAddSeriesByRange>();
	mapAddExecClass<TaskExecSetSeriesVisible>();
	mapAddExecClass<TaskExecDeleteSeries>();
	mapAddExecClass<TaskExecMoveSeries>();
	mapAddExecClass<TaskExecChartSetElement>();
	mapAddExecClass<TaskExecChartApplyLayout>();
	mapAddExecClass<TaskExecChartSetChartColor>();
	mapAddExecClass<TaskExecChartSetChartType>();
	mapAddExecClass<TaskExecChartSetChartStyle>();
	mapAddExecClass<TaskExecChartSetChartTitle>();
	mapAddExecClass<TaskExecChartSetAxisTitle>();
	mapAddExecClass<TaskExecChartSetPlotBy>();
	mapAddExecClass<TaskExecChartSetPloVisibleOnly>();
	mapAddExecClass<TaskExecChartSetDisplayBlanksAs>();
	mapAddExecClass<TaskExecChartSetTextProperty>();
	mapAddExecClass<TaskExecChartSetAxisOption>();
	mapAddExecClass<TaskExecChartSetNumFmt>();
	mapAddExecClass<TaskExecChartSetFill>();
	mapAddExecClass<TaskExecChartSetOutline>();
	mapAddExecClass<TaskExecChartSetMarkerOption>();
	mapAddExecClass<TaskExecChartSetSeriesOption>(); 
	mapAddExecClass<TaskExecTrendlineOption>(); 
	mapAddExecClass<TaskExecDeleteTrendline>();
	mapAddExecClass<TaskExecDataLabelOption>(); 
	mapAddExecClass<TaskExecLegendOption>(); 
	mapAddExecClass<TaskExecSetTheme>();
	mapAddExecClass<TaskExecFileMerge>();
	mapAddExecClass<TaskExecPixelate>();
	mapAddExecClass<TaskExecSetPixelator>();
	mapAddExecClass<TaskExecAutoSum>();
	mapAddExecClass<TaskExecInsertCellPicture>();
	mapAddExecClass<TaskExecBlockUploadAllPictureToAttachment>();
	mapAddExecClass<TaskExecInsertCellPictureAttachment>();
	mapAddExecClass<TaskExecInsertCellPictureUrl>();
	mapAddExecClass<TaskExecFloatPic2CellPic>();
	mapAddExecClass<TaskExecCellPic2FloatPic>();
	mapAddExecClass<TaskExecInsertCopied>();
	mapAddExecClass<TaskExecInsertCut>();
	mapAddExecClass<TaskExecRangeTextToNumber>();
	mapAddExecClass<TaskExecBookSetFilterShared>();
	mapAddExecClass<TaskExecShapePaste>();
	mapAddExecClass<TaskExecBookSetOptions>();
	mapAddExecClass<TaskExecBookConvertEtToAs>();
	mapAddExecClass<TaskExecSetSheetProtection>();
	mapAddExecClass<TaskExecSetProtectionOptions>();
	mapAddExecClass<TaskExecSetAllowEditRange>();
	mapAddExecClass<TaskExecRemovePartRangeProtection>();
	mapAddExecClass<TaskExecSetExclusiveRangeMode>();
	mapAddExecClass<TaskExecMultiRangeRemoveExclusiveRange>();
	mapAddExecClass<TaskExecSheetSetInterline>();
    mapAddExecClass<TaskExecSetRefStyle>();
	mapAddExecClass<TaskExecPivotTableAddField>();
	mapAddExecClass<TaskExecPivotTableRemoveField>();
	mapAddExecClass<TaskExecSupBooksChangeSource>();
	mapAddExecClass<TaskExecSupBooksUpdateLinks>();
	mapAddExecClass<TaskExecSetTabColor>();
	mapAddExecClass<TaskExecRemoveCustomNF>();
	mapAddExecClass<TaskExecBookRecalculate>();
	mapAddExecClass<TaskExecEtEmpty>();
	mapAddExecClass<TaskExecInterruptEAF>();
	mapAddExecClass<TaskExecRecalculateEAF>();
	mapAddExecClass<TaskExecSheetSetWndInfo>();
	mapAddExecClass<TaskExecBookAddName>();
	mapAddExecClass<TaskExecBookEditName>();
	mapAddExecClass<TaskExecSheetEditName>();
	mapAddExecClass<TaskExecBookDeleteName>();
	mapAddExecClass<TaskExecSheetDeleteName>();
	mapAddExecClass<TaskExecDocSlim>();
	mapAddExecClass<TaskExecIsAllocAutoSlim>();
	mapAddExecClass<TaskExecChartMove>();
	mapAddExecClass<TaskExecChartReisze>();
	mapAddExecClass<TaskExecChartSetText>();
	mapAddExecClass<TaskExecSetValueByCustomList>();
	mapAddExecClass<TaskExecMultiRangeSetXf>();
	mapAddExecClass<TaskExecMultiRangeClearFormats>();
	mapAddExecClass<TaskExecRangeQuickSet>();
	mapAddExecClass<TaskExecRangeFillQuickSet>();
	mapAddExecClass<TaskExecSetSubscriptionOption>();
	mapAddExecClass<TaskExecSetDocumentCustomProperty>();
	mapAddExecClass<TaskExecSetPrintArea>();
	mapAddExecClass<TaskExecSetPageSetting>();
	mapAddExecClass<TaskExecCopyFromBook>();
	mapAddExecClass<TaskExecImportWorkbookInEt>();
	mapAddExecClass<TaskExecBookSetCustomStorage>();
	mapAddExecClass<TaskExecDbAddCol>();
	mapAddExecClass<TaskExecDbEditCol>();
	mapAddExecClass<TaskExecDbSetField>();
	mapAddExecClass<TaskExecDbFillValues>();
	mapAddExecClass<TaskExecDbAddRow>();
	mapAddExecClass<TaskExecModifySheetProtection>();
	mapAddExecClass<TaskExecSheetSetPadding>();
	mapAddExecClass<TaskExecSetListObjTotalsCal>();
	mapAddExecClass<TaskExecCreatePivotTable>();
	mapAddExecClass<TaskExecRemovePivotTable>();
	mapAddExecClass<TaskExecPivotTableMoveField>();
	mapAddExecClass<TaskExecPivotTableSetFieldName>();
	mapAddExecClass<TaskExecPivotTableSetFieldFunction>();
	mapAddExecClass<TaskExecPivotTableSetFieldSubtotals>();
	mapAddExecClass<TaskExecPivotTableRefresh>();
	mapAddExecClass<TaskExecRefreshAllPivotTable>();
	mapAddExecClass<TaskExecPivotTableSetSourceData>();
	mapAddExecClass<TaskExecPivotTableClearTable>();
	mapAddExecClass<TaskExecPivotTableShowFieldList>();
	mapAddExecClass<TaskExecPivotTableSetShowValuesAs>();
	mapAddExecClass<TaskExecPivotTableShowPages>();
	mapAddExecClass<TaskExecPivotTableShowDetailAtArea>();
	mapAddExecClass<TaskExecPivotTableSetOptions>();
	mapAddExecClass<TaskExecPivotFieldSetOptions>();
	mapAddExecClass<TaskExecPivotTableSetFieldLayoutPrint>();
	mapAddExecClass<TaskExecPivotTableSortField>();
	mapAddExecClass<TaskExecPivotTablePivotCollapseButton>();
	mapAddExecClass<TaskExecPivotTableGroup>();
	mapAddExecClass<TaskExecPivotTableUnGroup>();
	mapAddExecClass<TaskExecAddSlicer>();
	mapAddExecClass<TaskExecPivotSelectSlicerItem>();
	mapAddExecClass<TaskExecPivotClearSlicerItem>();
	mapAddExecClass<TaskExecPivotSetSlicerStartIndex>();
	mapAddExecClass<TaskExecPivotSetSlicerOptions>();
	mapAddExecClass<TaskExecPivotConnectPivotTable>();
	mapAddExecClass<TaskExecPivotAddCalculatedItem>();
	mapAddExecClass<TaskExecPivotModifyCalculatedItem>();
	mapAddExecClass<TaskExecPivotDelCalculatedItem>();
	mapAddExecClass<TaskExecPivotAddCalculatedField>();
	mapAddExecClass<TaskExecPivotModifyCalculatedField>();
	mapAddExecClass<TaskExecPivotDelCalculatedField>();
	mapAddExecClass<TaskExecPivotExpandRange>();
	mapAddExecClass<TaskExecPivotSlicerRefresh>();
	mapAddExecClass<TaskExecPivotItemMove>();
	mapAddExecClass<TaskExecSetTextLink>();
	mapAddExecClass<TaskExecClearTransactions>();
	mapAddExecClass<TaskExecBookSetInsertPicAsAttachment>();
	mapAddExecClass<TaskExecUpdateImportrangeUrl>();
	mapAddExecClass<TaskExecInitImportrangeCalc>();
	mapAddExecClass<TaskExecUpdateSupbookData>();
	mapAddExecClass<TaskExecMergeFileAllTask>();
	mapAddExecClass<TaskExecAutoUpdateMergeFile>();
	mapAddExecClass<TaskExecUpdateCrossbookState>();
	mapAddExecClass<TaskExecUpdateRemoteChart>();
	mapAddExecClass<TaskExecSetProtectedCol>();
	mapAddExecClass<TaskExecReCalcSubtotal>();
	mapAddExecClass<EtHttpPivotTableRefreshTaskClass>();
	mapAddExecClass<EtHttpSetRangeWidthHeightTaskClass>();
	mapAddExecClass<EtHttpUpdateRangeDataTaskClass>();
	mapAddExecClass<TaskExecClearCellHistory>();
	mapAddExecClass<TaskExecSetCellHistoryPurgeOptions>();
	mapAddExecClass<EtHttpAddRowTaskClass>();
	mapAddExecClass<EtHttpDeleteRangeTaskClass>();
	mapAddExecClass<EtHttpSheetAddHyperlinkTaskClass>();
	mapAddExecClass<EtHttpSheetSetNameTaskClass>();
	mapAddExecClass<EtHttpSheetsAddTaskClass>();
	mapAddExecClass<EtHttpSheetsCopyTaskClass>();
	mapAddExecClass<EtHttpCopySheetsFromBookTaskClass>();
	mapAddExecClass<EtHttpDeleteSheetsTaskClass>();
    mapAddExecClass<EtHttpSetQRColTaskClass>();
    mapAddExecClass<EtHttpFillQRColTaskClass>();
    mapAddExecClass<EtHttpClearColTypeTaskClass>();
    mapAddExecClass<EtHttpRecalculateRangeDataTaskClass>();
    mapAddExecClass<EtHttpUpdateWorkbenchSheetTaskClass>();
	mapAddExecClass<TaskExecPublishInquirer>();
	mapAddExecClass<TaskExecRemoveInquirer>();
	mapAddExecClass<TaskExecPageBreak>();
    mapAddExecClass<TaskExecMultiSheetsOp>();
    mapAddExecClass<TaskExecKSheetShareSheet>();
	mapAddExecClass<TaskExecKSheetCancelShareSheet>();
	mapAddExecClass<TaskExecEtShareSheet>();
	mapAddExecClass<TaskExecEtCancelShareSheet>();
    mapAddExecClass<TaskExecRangeExportAutoFilterValues>();
	mapAddExecClass<TaskExecCleanAIColumn>();
    mapAddExecClass<TaskExecMultiRangeClearContents>();
    mapAddExecClass<TaskExecMultiRangeClearAll>();
    mapAddExecClass<TaskExecMultiRangeClearSpecialChars>();
    mapAddExecClass<TaskExecMultiRangeClearComments>();
	mapAddExecClass<TaskExecSplitSheet>();
	mapAddExecClass<EtHttpV8JsEvaluateTaskClass>();
	mapAddExecClass<TaskExecEtV8JsEvaluate>();
	mapAddExecClass<EtHttpInsertNumberColTaskClass>();
	mapAddExecClass<EtHttpExecuteApiTaskClass>();
	mapAddExecClass<EtHttpPlaybackApiTaskClass>();
	mapAddExecClass<TaskExecAppendSheet>();
	mapAddExecClass<EtFormHttpAddSheetTaskClass>();
	mapAddExecClass<EtHttpFullUpdateSourceSheetTaskClass>();
	mapAddExecClass<EtHttpImportWorkBookClass>();
	mapAddExecClass<EtHttpDeleteDataAnalyzeMergeTaskClass>();
	mapAddExecClass<EtHttpAddDataAnalyzeLogClass>();
	mapAddExecClass<EtHttpUpdateDataAnalyzeDataSourceNameClass>();
	mapAddExecClass<TaskExecWebChartSetStyle>();
	mapAddExecClass<TaskExecWebChartRemoveStyle>();
	mapAddExecClass<TaskExecWebChartSetOrder>();
}

void EtTaskExecutor::initDbExecMap()
{
	mapAddExecClass<TaskExecDbSheetsAddLegacy>();
	mapAddExecClass<TaskExecDbSheetsMoveLegacy>();
	mapAddExecClass<TaskExecDbSheetsCopyLegacy>();
	mapAddExecClass<TaskExecCopyAppSheet>();
    mapAddExecClass<TaskExecCopyDashBoardSheet>();
	mapAddExecClass<TaskExecDbSheetDelLegacy>();
	mapAddExecClass<TaskExecDbSheetSetNameLegacy>();

	mapAddExecClass<TaskExecDbSheetsAdd>();
	mapAddExecClass<TaskExecDbSheetsMove>();
	mapAddExecClass<TaskExecDbSheetsCopy>();
	mapAddExecClass<TaskExecDbSheetDel>();
	mapAddExecClass<TaskExecDbSheetsDel>();
	mapAddExecClass<TaskExecDbSheetSetName>();
	mapAddExecClass<TaskExecDbtBookRecalculateLegacy>();
	mapAddExecClass<TaskExecImportWorkbookLegacy>();
	mapAddExecClass<TaskExecSetCustomStorage>();

	mapAddExecClass<TaskExecDbInsertRecords>();
	mapAddExecClass<TaskExecDbRemoveRecords>();
	mapAddExecClass<TaskExecDbSetParentRecord>();
	mapAddExecClass<TaskExecDbSetCellHyperlink>();
	mapAddExecClass<TaskExecDbSetCellValue>();
	mapAddExecClass<TaskExecDbSetRangeValues>();
	mapAddExecClass<TaskExecDbSetCellPic>();
	mapAddExecClass<TaskExecDbModifyWebExtension>();
	mapAddExecClass<TaskExecDbModifyPluginConfig>();
	mapAddExecClass<TaskExecDbDashboardAddView>();
	mapAddExecClass<TaskExecDbDashboardModifyView>();
	mapAddExecClass<TaskExecDbAddWebExtension>();
	mapAddExecClass<TaskExecDbDeleteWebExtension>();
	mapAddExecClass<TaskExecDbBatchModifyWebExtension>();
	mapAddExecClass<TaskExecDbAddField>();
	mapAddExecClass<TaskExecDbRemoveFields>();
	mapAddExecClass<TaskExecDbClearRange>();
	mapAddExecClass<TaskExecDbMoveRange>();
	mapAddExecClass<TaskExecDbMoveGroup>();
	mapAddExecClass<TaskExecDbRemoveGroup>();
	mapAddExecClass<TaskExecDbModifyField>();
	mapAddExecClass<TaskExecDbSetFieldHidden>();
	mapAddExecClass<TaskExecDbSetFieldWidth>();
	mapAddExecClass<TaskExecDbSetRecordsHeight>();
	mapAddExecClass<TaskExecDbCreateView>();
	mapAddExecClass<TaskExecDbCopyView>();
	mapAddExecClass<TaskExecDbDeleteView>();
	mapAddExecClass<TaskExecDbRenameView>();
	mapAddExecClass<TaskExecDbSetViewOption>();
	mapAddExecClass<TaskExecDbSetViewDescription>();
	mapAddExecClass<TaskExecDbSetViewNotice>();
	mapAddExecClass<TaskExecDbSetSheetDescription>();
	mapAddExecClass<TaskExecDbSetSheetIcon>();
	mapAddExecClass<TaskExecDbSetStatistic>();
	mapAddExecClass<TaskExecDbRemoveStatistic>();
	mapAddExecClass<TaskExecDbMoveView>();
	mapAddExecClass<TaskExecDbAddGroupCondition>();
	mapAddExecClass<TaskExecDbRemoveGroupCondition>();
	mapAddExecClass<TaskExecDbSetGroupPriority>();
	mapAddExecClass<TaskExecDbChangeGroupField>();
	mapAddExecClass<TaskExecDbSetGroupAscending>();
	mapAddExecClass<TaskExecDbAddSortCondition>();
	mapAddExecClass<TaskExecDbRemoveSortCondition>();
	mapAddExecClass<TaskExecDbSetSortPriority>();
	mapAddExecClass<TaskExecDbChangeSortField>();
	mapAddExecClass<TaskExecDbSetSortAscending>();
	mapAddExecClass<TaskExecDbSetAutoSort>();
	mapAddExecClass<TaskExecDbSetUngroupedPositionOption>();
	mapAddExecClass<TaskExecDbExecSort>();
	mapAddExecClass<TaskExecDbSetGridFrozenCount>();
	mapAddExecClass<TaskExecDbRangePaste>();
	mapAddExecClass<TaskExecDbSetFilterCriteria>();
	mapAddExecClass<TaskExecDbRemoveFilter>();
	mapAddExecClass<TaskExecDbClearFilters>();
	mapAddExecClass<TaskExecDbSetFiltersOp>();
	mapAddExecClass<TaskExecDbPrepareCurUser>();
	mapAddExecClass<TaskExecDbEmpty>();
    mapAddExecClass<TaskExecDbConvertCurUserFilterToAnyUserFilter>();
	mapAddExecClass<TaskExecDbUserGroupAddUsers>();
	mapAddExecClass<TaskExecDbUserGroupRemoveUsers>();
	mapAddExecClass<TaskExecDbUserGroupSetProtection>();
	mapAddExecClass<TaskExecDbProtectionSwitch>();
	mapAddExecClass<TaskExecDbSetupProtection>();
	mapAddExecClass<TaskExecDbShareView>();
	mapAddExecClass<TaskExecDbCancelShareView>();
	mapAddExecClass<TaskExecDbShareSheet>();
	mapAddExecClass<TaskExecDbCancelShareSheet>();
	mapAddExecClass<TaskExecDbModifyShareView>();
	mapAddExecClass<TaskExecDbAddTemplateSheets>();
	mapAddExecClass<TaskExecDbImportXlsxSheets>();
	mapAddExecClass<TaskExecDbImportCsv>();
	mapAddExecClass<TaskExecDbImportDbSheets>();
	mapAddExecClass<TaskExecDbAppendData>();
	mapAddExecClass<TaskExecDbAddSyncSheets>();
	mapAddExecClass<TaskExecDbUnsyncSheets>();
	mapAddExecClass<TaskExecCleanUnusedSyncSheets>();
	mapAddExecClass<TaskExecUpdateSyncInfoSheets>();
	mapAddExecClass<TaskExecDbClearTransactions>();
	mapAddExecClass<TaskExecDbSetGalleryCoverField>();
	mapAddExecClass<TaskExecDbSetGalleryCoverDispType>();
	mapAddExecClass<TaskExecDbSetKanbanCoverField>();
	mapAddExecClass<TaskExecDbSetKanbanCoverDispType>();
	mapAddExecClass<TaskExecDbSetDbLayout>();
	mapAddExecClass<TaskExecDbSetTimerTask>();
	mapAddExecClass<TaskExecDbFetchUserInfoDone>();
	mapAddExecClass<TaskExecDbUpdateUserOrganizeInfo>();
	mapAddExecClass<TaskExecDbassignTimeLine>();
	mapAddExecClass<TaskExecDbConfigureGanttView>();
	mapAddExecClass<TaskExecDbSetFormFieldOption>();
	mapAddExecClass<TaskExecDbSetPersonalView>();
	mapAddExecClass<TaskExecDbSetFieldNameVisible>();
	mapAddExecClass<TaskExecDbSetViewDisplayFromType>();
	mapAddExecClass<TaskExecDbSetQueryFieldIds>();
	mapAddExecClass<TaskExecDbSetQueryFields>();
	mapAddExecClass<TaskExecDbSetQueryConditionCanBlank>();
	mapAddExecClass<TaskExecDbSetQueryNeedSecondCheck>();
	mapAddExecClass<TaskExecDbSetQueryCriteria>();
	mapAddExecClass<TaskExecUpdateAppSheetData>();
	mapAddExecClass<TaskExecDbOnAfterDeleteApp>();
	mapAddExecClass<TaskExecDbConvertToEtSheet>();
	mapAddExecClass<TaskExecDbConvertFromEtSheet>();
	mapAddExecClass<TaskExecUpdateAppVersion>();
	mapAddExecClass<TaskExecResetAppSheetSharedIds>();
	mapAddExecClass<TaskExecDbRangeFill>();
	mapAddExecClass<TaskExecDbSetRangeXf>();
	mapAddExecClass<TaskExecDbClearRangeXf>();
	mapAddExecClass<TaskExecDbSetFieldTitleFormat>();
	mapAddExecClass<TaskExecDbRemoveFieldTitleFormat>();
	mapAddExecClass<TaskExecDbSetViewBackgroundImage>();
	mapAddExecClass<TaskExecSetCustomCalendar>();
	mapAddExecClass<TaskExecDbClearTempSettings>();
	mapAddExecClass<TaskExecDbSyncTempSettings>();
	mapAddExecClass<TaskExecDbSaveAsNewViewByTempSettings>();
	mapAddExecClass<TaskExecDbSetComposeFilter>();
	mapAddExecClass<TaskExecDbPermissionsUpgrade>();
	mapAddExecClass<TaskExecDbSetCalendarCriteria>();
	mapAddExecClass<TaskExecDbConfigureCalendarView>();
	mapAddExecClass<TaskExecDbAssignCalendarTimeLine>();
    mapAddExecClass<TaskExecDbChartSetStyle>();
    mapAddExecClass<TaskExecDbChartRemoveStyle>();
    mapAddExecClass<TaskExecDbChartSetOrder>();
	mapAddExecClass<TaskExecModidyStatSheetSetting>();
	mapAddExecClass<TaskExecActivateStatSheet>();
	mapAddExecClass<TaskExecRecoverStatSheet2NormalDbSheet>();
	mapAddExecClass<TaskExecDbAddSidebarFolder>();
	mapAddExecClass<TaskExecDbDeleteSidebarFolder>();
	mapAddExecClass<TaskExecDbMoveSidebarTree>();
	mapAddExecClass<TaskExecDbSetNameSidebarFolder>();
	mapAddExecClass<TaskExecDbCommonLogInit>();
	mapAddExecClass<TaskExecDbDashboardCreateFilter>();
	mapAddExecClass<TaskExecDbDashboardUpdateFilter>();
	mapAddExecClass<TaskExecDbDashboardDeleteFilter>();
	mapAddExecClass<TaskExecDbDashboardMoveFilter>();
	mapAddExecClass<TaskExecDbDashboardSetFilterCriteria>();
	mapAddExecClass<TaskExecDbDashboardClearFilterCriteria>();
	mapAddExecClass<DbHttpCreateRecordsTaskClass>();
	mapAddExecClass<DbHttpUpdateRecordsTaskClass>();
	mapAddExecClass<DbHttpSetParentRecordTaskClass>();
	mapAddExecClass<DbHttpEnableParentRecordTaskClass>();
	mapAddExecClass<DbHttpDisableParentRecordTaskClass>();
	mapAddExecClass<DbHttpDeleteRecordsTaskClass>();
	mapAddExecClass<DbHttpCreateFieldsTaskClass>();
	mapAddExecClass<DbHttpDeleteFieldsTaskClass>();
	mapAddExecClass<DbHttpUpdateFieldsTaskClass>();
	mapAddExecClass<DbHttpCreateViewTaskClass>();
	mapAddExecClass<DbHttpUpdateViewTaskClass>();
	mapAddExecClass<DbHttpDeleteViewTaskClass>();
	mapAddExecClass<DbHttpAddViewSettingTaskClass>();
	mapAddExecClass<DbHttpRemoveViewSettingTaskClass>();
	mapAddExecClass<DbHttpSetQueryFieldsTaskClass>();
	mapAddExecClass<DbHttpCreateSheetTaskClass>();
	mapAddExecClass<DbHttpUpdateSheetTaskClass>();
	mapAddExecClass<DbHttpDeleteSheetTaskClass>();
	mapAddExecClass<DbHttpEditPermissonTaskClass>();
	mapAddExecClass<DbHttpDevSubmitFormRecordTaskClass>();
	mapAddExecClass<DbHttpDevCreateAppViewTaskClass>();
	mapAddExecClass<DbHttpFullUpdateSyncSheetTaskClass>();
	mapAddExecClass<DbHttpSyncSheetTaskClass>();
	mapAddExecClass<DbHttpBatchFullUpdateSyncSheetTaskClass>();
	mapAddExecClass<DbHttpSyncFromSqlTaskClass>();
	mapAddExecClass<DbHttpSyncFormToDbTaskClass>();
    mapAddExecClass<DbHttpCreateDashboardTaskClass>();
    mapAddExecClass<DbHttpUpdateDashboardTaskClass>();
    mapAddExecClass<DbHttpClearDashboardTaskClass>();
    mapAddExecClass<DbHttpDeleteDashboardTaskClass>();
    mapAddExecClass<DbHttpCreateWebExtensionTaskClass>();
    mapAddExecClass<DbHttpUpdateWebExtensionTaskClass>();
    mapAddExecClass<DbHttpDeleteWebExtensionTaskClass>();
    mapAddExecClass<EtHttpSetCustomStorageTaskClass>();
	mapAddExecClass<TaskExecDbAddSyncMergeSheet>();
	mapAddExecClass<DbHttpFullUpdateSyncMergeSheetTaskClass>();
    mapAddExecClass<DbDashboardHttpCreateFiltersTaskClass>();
    mapAddExecClass<DbDashboardHttpUpdateFiltersTaskClass>();
    mapAddExecClass<DbDashboardHttpDeleteFiltersTaskClass>();
}

// TODO : 补充白名单
void EtTaskExecutor::initOverSizeWhiteCmdSet()
{
	m_overSizeWhiteCmdSet.insert(__X("range.delete"));								// TaskExecRangeDelete
	m_overSizeWhiteCmdSet.insert(__X("book.docSlim"));								// TaskExecDocSlim
	m_overSizeWhiteCmdSet.insert(__X("sheet.delSheet"));							// TaskExecSheetDel
	m_overSizeWhiteCmdSet.insert(__X("range.clearContents"));						// TaskExecRangeClearContents
	m_overSizeWhiteCmdSet.insert(__X("range.clearFormats"));						// TaskExecRangeClearFormats
	m_overSizeWhiteCmdSet.insert(__X("book.blockUploadAllPicturesToAttachment"));	// TaskExecBlockUploadAllPictureToAttachment
	m_overSizeWhiteCmdSet.insert(__X("sheet.deleteShape"));							// TaskExecDeleteShape
	m_overSizeWhiteCmdSet.insert(__X("range.deleteCommentItem"));					// TaskExecRangDeleteCommentItem
	m_overSizeWhiteCmdSet.insert(__X("range.deleteComment"));						// TaskExecRangDeleteComment

	// widowinfo 前端无法回滚，故添加进来
	m_overSizeWhiteCmdSet.insert(__X("sheet.setWndInfo"));							// TaskExecRangDeleteComment
}

VarObj EtTaskExecutor::gainResItem(VarObj& arr)
{
	if (arr.arrayLength() > 0)
	{
		binary_wo::VarObj item = arr.at(arr.arrayLength() - 1);
		if(item.elemSize() == 0)
			return item;
	}
	return arr.add_item_struct();
}

binary_wo::VarObj EtTaskExecutor::GainResItemForSelf()
{
	return gainResItem(m_resForSelfArr);
}

binary_wo::VarObj EtTaskExecutor::GainResItemForOthers()
{
	return gainResItem(m_resForOthersArr);
}

bool EtTaskExecutor::isNeedReset(binary_wo::VarObjRoot& objRoot, binary_wo::VarObj& arr)
{
	if (objRoot.get() == nullptr)
		return true;

	if (arr.arrayLength() != 1)
		return true;

	return arr.at(0).elemSize() != 0;
}


void EtTaskExecutor::ResetResForOthers()
{
	if(isNeedReset(m_resForOthers, m_resForOthersArr))
	{
		m_resForOthers = binary_wo::VarObjRoot(new binary_wo::BinVarRoot());
		m_resForOthersArr = m_resForOthers.cast().add_field_array("cmds", binary_wo::typeStruct);
	}
}

void EtTaskExecutor::ResetResForSelf()
{
	if(isNeedReset(m_resForSelf, m_resForSelfArr))
	{
		m_resForSelf = binary_wo::VarObjRoot(new binary_wo::BinVarRoot());
		m_resForSelfArr = m_resForSelf.cast().add_field_array("cmds", binary_wo::typeStruct);
	}
}

binary_wo::VarObj EtTaskExecutor::getRes(binary_wo::VarObj& arr)
{
	int32 len = arr.arrayLength_s();
	if (len == 0 || len == 1 && arr.at(0).elemSize() == 0)
	{
		return binary_wo::VarObj(); // 如果是1且为空，不删除下次 ResetRes 会更快
	}

	if (len > 1 && arr.at(len - 1).elemSize() == 0) {
		arr.remove_item(len - 1); // 删除最后一个空的。
	}

	return arr;
}

} // namespace wo
