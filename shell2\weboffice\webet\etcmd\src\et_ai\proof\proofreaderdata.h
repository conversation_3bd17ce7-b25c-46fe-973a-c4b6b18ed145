﻿// -------------------------------------------------------------------------- //
//	文件名		：	proofreaderdata.h
//	创建者		：	laiw
//	创建时间	：	2021-02-03 14:50:50
//	功能描述	：	表格识别结果对象类
//
// -------------------------------------------------------------------------- //

#ifndef __ETAI_SMARTDATASERVICE_PROOFREADER_PROOFREADERDATA_H__
#define __ETAI_SMARTDATASERVICE_PROOFREADER_PROOFREADERDATA_H__

#include "recognizedata.h"

namespace range_helper{
  class ranges;
}

namespace etai
{

enum CorrectType
{
    CorrectType_Uniform_Format = 0, // 格式统一
    CorrectType_Specification = 1,  // 格式规范
    CorrectType_Content = 2         // 内容问题
};
enum Status
{
    Status_Untreated = 0, // 未处理
    Status_Already_Deal,  // 已处理
    Status_Ignore,        // 忽略
    Status_User_Edited    // 用户编辑过了
};

enum ErrorType
{
    ErrorType_Undefined = -1,    // 未定义
    ErrorType_UMultipleDate = 0, // 多种日期格式，建议统一
    ErrorType_UMultipleTime,     // 多种时间格式，建议统一
    ErrorType_UTextDate,         // 文本型日期无法分析，建议转换为日期类型
    ErrorType_UTextTime,         // 文本型时间无法分析，建议转换为时间类型
    ErrorType_NTextDate,         // 文本型日期无法分析，建议转换为日期类型
    ErrorType_NTextTime,         // 文本型时间无法分析，建议转换为时间类型
    ErrorType_NTextNumber,       // 数字位数过长，建议转换为文本类型
    ErrorType_NMoneyNumber,      // 货币不规范，建议转换为
    ErrorType_NThousand,         // 千分位不规范，建议转换为
    ErrorType_NNormDate,         // 日期类型不规范，建议转换为规范日期类型"
    ErrorType_NNormTime,         // 时间类型不规范，建议转换为规范时间类型"
    ErrorType_NPercentNumber,    // 文本型百分比无法计算，建议转换为百分比类型"
    ErrorType_NNumber,           // 文本型数值无法计算，建议转换为数值类型"
    ErrorType_End,
};

class CellErrorBase
{
  public:
    CellErrorBase(const QJsonValue &val);
    virtual ~CellErrorBase();

    int rowIndex() const
    {
        return m_rowIndex;
    }
    const ES_CUBE &cube() const
    {
        return m_cube;
    }
    const ES_CUBE &mergeCube() const
    {
        return m_mergeCube;
    }
    const QString &text() const
    {
        return m_text;
    };
    Range *range() const
    {
        return m_spRange;
    }
    bool isMerge() const
    {
        return m_isMerge;
    }
    const QString location() const;

  public:
    virtual void setWorksheet(IKWorksheet *worksheet);
    bool readCube(const QJsonObject &obj, const QString &key, ES_CUBE *cube);

  public:
    const KComVariant &oldValue2() const
    {
        return m_oldValue2;
    }
    void setOldValue2(const KComVariant &v)
    {
        m_oldValue2 = v;
        m_hasOldValue2 = true;
    };
    bool hasOldValue2() const
    {
        return m_hasOldValue2;
    }
    const ks_wstring &oldText() const
    {
        return m_oldText;
    }
    void setOldText(const ks_wstring &v)
    {
        m_oldText = v;
        m_hasOldText = true;
    };
    bool hasOldText() const
    {
        return m_hasOldText;
    }
    const ks_wstring &oldNumberFormatLocal() const
    {
        return m_oldNumberFormatLocal;
    };
    void setOldNumberFormatLocal(const ks_wstring &v)
    {
        m_oldNumberFormatLocal = v;
        m_hasOldNumberFormatLocal = true;
    };
    bool hasOldNumberFormatLocal() const
    {
        return m_hasOldNumberFormatLocal;
    }
    const QString &content() const
    {
        return m_content;
    }

  private:
    int m_rowIndex;
    ES_CUBE m_cube;
    ES_CUBE m_mergeCube;
    QString m_text;
    ks_stdptr<Range> m_spRange;
    bool m_isMerge;
    KComVariant m_oldValue2;
    bool m_hasOldValue2;
    ks_wstring m_oldText;
    bool m_hasOldText;
    ks_wstring m_oldNumberFormatLocal;
    bool m_hasOldNumberFormatLocal;
    QString m_content;
};

class ErrorWordInfo
{
  public:
    ErrorWordInfo(const QJsonValue &val);

  public:
    int count() const
    {
        return m_count;
    }
    const QString &type() const
    {
        return m_type;
    }
    bool hasErrorType(const ErrorType errorType) const;

  private:
    int m_count;
    QString m_type;
    std::vector<ErrorType> m_errorTypeList;
};

class ErrorWordInfoList : public Container<ErrorWordInfo>
{
  public:
    ErrorWordInfoList(const QJsonArray &array);
    ErrorWordInfoList(const ErrorWordInfoList &o);
    ~ErrorWordInfoList();
};

enum IdentifyType
{
    IdentifyType_Date = 0,        // 日期
    IdentifyType_Time = 1,        // 时间
    IdentifyType_DateTime = 2,    // 日期时间
    IdentifyType_MoneyNumber = 3, // 货币
    IdentifyType_Number = 4,      // 数值
};

class ContentError : public CellErrorBase
{
  public:
    ContentError(const QJsonValue &val);
    ~ContentError();

    const IdentifyType errorType() const
    {
        return m_errorType;
    }
    CorrectType correctType() const
    {
        return CorrectType_Content;
    }
    const QVariant &newValue() const
    {
        return m_newValue;
    }
    void setNewValue(const QVariant &newValue)
    {
        m_newValue = newValue;
    }

    static IdentifyType toIdentifyType(const QString &name);

  private:
    IdentifyType m_errorType;
    QVariant m_newValue;
};

class ContentErrorList : public Container<ContentError>
{
  public:
    ContentErrorList(const QJsonArray &array);
    ContentErrorList(const ContentErrorList &o);
    ~ContentErrorList();

  public:
    void setWorksheet(IKWorksheet *worksheet);
};

class CellError : public CellErrorBase
{
  public:
    CellError(const QJsonValue &val);
    ~CellError();

    const IdentifyType identifyType() const
    {
        return m_identifyType;
    }
    const ErrorType errorType() const
    {
        return m_errorType;
    }
    const QString &value2() const
    {
        return m_value2;
    }
    const QString &numberFormatLocal() const
    {
        return m_numberFormatLocal;
    }
    static ErrorType toErrorType(const QString &name);

  public:
    static bool compare(const CellError *a, const CellError *b)
    {
        return a->cube().rowFrom < b->cube().rowFrom;
    }

  private:
    IdentifyType m_identifyType;
    ErrorType m_errorType;
    QString m_value2;
    QString m_numberFormatLocal;
};

class CellErrorList : public Container<CellError>
{
  public:
    CellErrorList(const QJsonArray &array);
    CellErrorList(const CellErrorList &o);
    ~CellErrorList();

  public:
    void setWorksheet(IKWorksheet *worksheet);
};

class UnifiedNormalFormatLocal
{
  public:
    UnifiedNormalFormatLocal(const QJsonValue &val);
    ~UnifiedNormalFormatLocal();

  public:
    int frequency() const
    {
        return m_frequency;
    }
    const QString &formatLocal() const
    {
        return m_formatLocal;
    }

  private:
    int m_frequency;
    QString m_formatLocal;
};

class UnifiedNormalFormatLocalList : public Container<UnifiedNormalFormatLocal>
{
  public:
    UnifiedNormalFormatLocalList(const QJsonArray &array);
    UnifiedNormalFormatLocalList(const UnifiedNormalFormatLocalList &o);
    ~UnifiedNormalFormatLocalList();
};

class ColumnError
{
  public:
    typedef std::vector<std::vector<const CellError *> *> CellErrorGroup;

  public:
    ColumnError(const QJsonValue &val);
    ~ColumnError();

  public:
    int columnIndex() const
    {
        return m_columnIndex;
    }
    const QString columnName() const;
    const QStringList &rowTitleContentList() const
    {
        return m_rowTitleContentList;
    };
    const UnifiedNormalFormatLocalList *unifiedNormalFormatLocalList() const
    {
        return m_unifiedNormalFormatLocalList;
    };
    //统一
    const CellErrorList *unifiedCellErrorList() const
    {
        return m_unifiedCellErrorList;
    };
    //规范
    const CellErrorList *normCellErrorList() const
    {
        return m_normCellErrorList;
    };
    const std::vector<const CellError *> &cellErrorList() const
    {
        return m_cellErrorList;
    };
    const CellErrorGroup &cellErrorGroup() const
    {
        return m_cellErrorGroup;
    };
    const ES_CUBE cube() const;
    const ES_CUBE columnCube() const;
    const ErrorWordInfoList *errorWordInfoList() const
    {
        return m_errorWordInfoList;
    };
    Range *range() const
    {
        return m_spRange;
    }
    Range *columnRange() const
    {
        return m_spColumnRange;
    }
    CorrectType correctType() const;
    const QString rowTitle() const;
    int correcteUnifiedCellCount() const
    {
        return m_correcteUnifiedCellCount;
    }
    void setCorrecteUnifiedCellCount(int correcteUnifiedCellCount)
    {
        m_correcteUnifiedCellCount = correcteUnifiedCellCount;
    }
    int correcteNormCellCount() const
    {
        return m_correcteNormCellCount;
    }
    void setCorrecteNormCellCount(int correcteNormCellCount)
    {
        m_correcteNormCellCount = correcteNormCellCount;
    }

  public:
    void setTableRange(const ES_CUBE *tableRange);
    void setWorksheet(IKWorksheet *worksheet);

  private:
    void groupCellError();
    static bool compareCellErrorList(std::vector<const CellError *> *a, std::vector<const CellError *> *b)
    {
        return a->at(0)->rowIndex() < b->at(0)->rowIndex();
    }

  private:
    int m_columnIndex;
    QStringList m_rowTitleContentList;
    UnifiedNormalFormatLocalList *m_unifiedNormalFormatLocalList;
    CellErrorList *m_unifiedCellErrorList;
    CellErrorList *m_normCellErrorList;
    const ES_CUBE *m_tableRange;
    ErrorWordInfoList *m_errorWordInfoList;
    ks_stdptr<Range> m_spRange;
    ks_stdptr<Range> m_spColumnRange;
    CellErrorGroup m_cellErrorGroup;
    std::vector<const CellError *> m_cellErrorList;
    int m_correcteUnifiedCellCount;
    int m_correcteNormCellCount;
};

class ColumnErrorList : public Container<ColumnError>
{
  public:
    ColumnErrorList(const QJsonArray &array);
    ColumnErrorList(const ColumnErrorList &o);
    ~ColumnErrorList();

  public:
    void setTableRange(const ES_CUBE *tableRange);
    void setWorksheet(IKWorksheet *worksheet);
};

class TableError
{
  public:
    TableError(const QJsonValue &val);
    ~TableError();

  public:
    void setWorksheet(IKWorksheet *worksheet);

    const ColumnErrorList *columnErrorList() const
    {
        return m_columnErrorList;
    };
    const ES_CUBE &tableRange() const
    {
        return m_tableRange;
    }
    const ES_CUBE &rowTitleRange() const
    {
        return m_rowTitleRange;
    }
    const ES_CUBE &contentRange() const
    {
        return m_contentRange;
    }

  private:
    bool parseRange(const QJsonObject &obj, const QString &key, ES_CUBE *range);
    bool parseRange(ES_CUBE *range, const QJsonValue &value);

  private:
    ES_CUBE m_tableRange;
    ES_CUBE m_rowTitleRange;
    ES_CUBE m_contentRange;
    ColumnErrorList *m_columnErrorList;
};

//格式统一问题
class TableErrorList : public Container<TableError>
{
  public:
    TableErrorList(const QJsonArray &array);
    TableErrorList(const TableErrorList &o);
    ~TableErrorList();

  public:
    void setWorksheet(IKWorksheet *worksheet);
};

class Correcte
{
  public:
    Correcte(const ColumnError *cellError);
    Correcte(const ColumnError *cellError, const ContentError *contentError);
    ~Correcte();

  public:
    const ColumnError *columnError() const
    {
        return m_columnError;
    }
    const ContentError *contentError() const
    {
        return m_contentError;
    }
    const Status status() const
    {
        return m_status;
    };

  public:
    void deal();
    void ignore();
    void untreated();
    void userEdited();

  private:
    const ColumnError *m_columnError;
    const ContentError *m_contentError;
    Status m_status;
};

class CorrecteData
{
  public:
    CorrecteData(const QJsonObject &obj);
    ~CorrecteData();

  public:
    IKWorksheet *worksheet() const
    {
        return m_spWorksheet;
    }
    IKWorkbook *workbook() const
    {
        return m_spWorkbook;
    }
    void adjustSelectedRange(range_helper::ranges& ranges);
    void setWorksheet(IKWorksheet *worksheet);

  public:
    const ContentErrorList *contentErrorList() const
    {
        return m_contentErrorList;
    };
    const TableErrorList *tableErrorList() const
    {
        return m_tableErrorList;
    };
    const std::vector<Correcte *> &correcteList() const
    {
        return m_correcteList;
    }

  public:
    void AddRef();
    void Release();

  private:
    CorrecteData(const CorrecteData &o);
    CorrecteData &operator=(const CorrecteData &o);

  private:
    int m_refCount;
    ContentErrorList *m_contentErrorList;
    std::vector<Correcte *> m_correcteList;
    TableErrorList *m_tableErrorList;
    ks_stdptr<IKWorksheet> m_spWorksheet;
    ks_castptr<IKWorkbook> m_spWorkbook;
};

} // namespace etai

#endif // __KAIET_SMARTDATASERVICE_PROOFREADER_PROOFREADERDATA_H__