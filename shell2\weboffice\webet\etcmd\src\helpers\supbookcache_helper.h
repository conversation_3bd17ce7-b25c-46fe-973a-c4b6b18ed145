﻿#ifndef __WEBET_SUPBOOKCACHE_HELPER_H__
#define __WEBET_SUPBOOKCACHE_HELPER_H__

#include "webbase/webdeclare.h"
#include "webbase/binvariant/binvarobj.h"
#include "et_revision_context_impl.h"

namespace wo
{
	class KEtRevisionContext;

	//一些命令会触发supBookCache更新，需要把更新的内容随命令发送到客户端
	class SyncClientSupBookCacheHelper
	{
	public:
		SyncClientSupBookCacheHelper(KEtRevisionContext*, KwCommand* cmd, const HRESULT& hr);
		~SyncClientSupBookCacheHelper();

		static bool addCacheStream(binary_wo::VarObj& param, IBookStake* pBookStake);
		static bool addCache(binary_wo::VarObj& param, IBookStake* pBookStake);

	protected:
		void buildCacheStream(const wo::SupBookCacheUpdateInfos& infos, binary_wo::BinWriter::StreamHolder& holder, int32& length);
		void serialCache(const wo::SupBookCacheUpdateInfos& infos, ISerialAcceptor* acpt);

		void serialSheetNames(ISerialAcceptor* acpt, ISupBook* pSupBook);
		void serialDefNames(ISerialAcceptor* acpt, ISupBook* pSupBook, const std::vector<IDX>& defNameIds);
		void serialSheetsData(ISerialAcceptor* acpt, ISupBook* pSupBook, const wo::SupBookCacheUpdateInfo& info);
		void serialRowData(ISerialAcceptor* acpt, ISupBook* pSupBook, IDX iSht, ROW r,
			const wo::SupBookCacheUpdateInfo::ROWDATA& rowData);
		void serialCellRegion(ISerialAcceptor* acpt, const_token_ptr);

	private:
		KEtRevisionContext* m_pCtx;
		const HRESULT& m_hr;
		KwCommand* m_pCmd;
	};

	// 前端跨book相关错误提示需要序列化supbookState到前端
	class SyncSupBookStateHelper
	{
	public:
		SyncSupBookStateHelper(KEtRevisionContext* pCtx)
			: m_pCtx(pCtx)
		{}

		~SyncSupBookStateHelper()
		{
			m_pCtx->clearDirtySupBook();
		}

		void addSupBookToSync(PCWSTR fileId)
		{
			m_pCtx->markDirtySupBook(fileId);
		}
	
	private:
		KEtRevisionContext* m_pCtx;	
	};

} // wo

#endif // __WEBET_SUPBOOKCACHE_HELPER_H__
