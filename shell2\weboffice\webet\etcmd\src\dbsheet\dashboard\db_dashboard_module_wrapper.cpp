﻿#include "etstdafx.h"
#include "db_dashboard_module_wrapper.h"
#include "webbase/binvariant/binvarobj.h"
#include "json/json.h"
#include "webbase/wo_sa_helper.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"

namespace wo
{

KDbDashboardModuleWrapper::KDbDashboardModuleWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension) :
        m_pEtWorkbook(pEtWorkbook), m_spWebExtension(pWebExtension)
{

}

PCWSTR KDbDashboardModuleWrapper::GetConfigStr() const
{
    LPCWSTR propertyValue = nullptr;
    HRESULT hr = m_spWebExtension->GetProperty(__X("chart-info"), &propertyValue);
    if (SUCCEEDED(hr) && propertyValue)
        return propertyValue;
    return __X("");
}

PCWSTR KDbDashboardModuleWrapper::GetProperty(LPCWSTR keyName) const
{
    LPCWSTR propertyValue = nullptr;
    HRESULT hr = m_spWebExtension->GetProperty(keyName, &propertyValue);
    if (SUCCEEDED(hr) && propertyValue)
        return propertyValue;
    return __X("");
}

WebExtensionDataSourceType KDbDashboardModuleWrapper::GetDataSourceType() const
{
    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = m_spWebExtension;
    return spDataSourceHost->GetDataSourceType();
}

PCWSTR KDbDashboardModuleWrapper::GetWebExtensionKey() const
{
    return m_spWebExtension->GetWebExtensionKey();
}

WebExtType KDbDashboardModuleWrapper::GetWebExtType() const
{
    return static_cast<WebExtType>(m_spWebExtension->GetWebShapeType());
}

POINT KDbDashboardModuleWrapper::GetPosition() const
{
    std::string str = krt::fromUtf16(GetConfigStr()).toStdString();
    Json::Value root;
    if (Json::Reader().parse(str, root) && root.isObject())
    {
        const Json::Value& layout = root["layout"];
        if (layout.isObject())
        {
            const Json::Value& x = layout["x"];
            const Json::Value& y = layout["y"];
            if (x.isInt() && y.isInt())
                return {x.asInt(), y.asInt()};
        }
    }
    return {-1, -1};
}

void KDbDashboardModuleWrapper::SerializeProperties(ISerialAcceptor* pAcpt) const
{
    PCWSTR ignoreKey = GetWebofficeUniquePropertyKey();
    ks_stdptr<IKWebExtensionPropertyEnum> spEnum;
    if (SUCCEEDED(m_spWebExtension->GetPropertyEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
    {
        do
        {
            PCWSTR curKey = spEnum->GetCurKey();
            if (xstrcmp(curKey, ignoreKey) != 0)
            {
                std::string key = krt::fromUtf16(curKey).toStdString();
                PCWSTR value = spEnum->GetCurValue();
                if (!key.empty() && value)
                {
                    pAcpt->addKey(key.c_str(), true);
                    pAcpt->addString(nullptr, value);
                }
            }
        }while (SUCCEEDED(spEnum->Next()));
    }
}

bool KDbDashboardModuleWrapper::SerializeBaseInfo(ISerialAcceptor* pAcpt)
{
    PCWSTR webExtensionKey = GetWebExtensionKey();
    pAcpt->addString("id", webExtensionKey);
    auto webExtensionType = GetWebExtType();
    PCWSTR typeStr = __X("");
    VS(_webextension_GainEncodeDecoder()->EncodeWebExtType(webExtensionType, &typeStr));
    pAcpt->addString("webExtType", typeStr);
    return true;
}

PCWSTR KDbDashboardModuleWrapper::GetStatusStr() const
{
    Status status = GetStatus();
    switch (status)
    {
        case Status::Normal:
            return __X("Normal");
        case Status::NoDataSource:
            return __X("NoDataSource");
        case Status::InvalidConfig:
            return __X("InvalidConfig");
        default:
            ASSERT(FALSE);
            return __X("");
    }
}

bool KDbDashboardModuleWrapper::IsChart()
{
    return m_spWebExtension->GetWebShapeType() == WET_DbDataSource;
}

}