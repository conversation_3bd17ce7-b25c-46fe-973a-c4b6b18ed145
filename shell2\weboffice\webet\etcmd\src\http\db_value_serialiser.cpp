﻿#include "etstdafx.h"
#include "http_macro.h"
#include "db_value_serialiser.h"
#include <public_header/drawing/api/dghost_i.h>
#include "webbase/binvariant/binvarobj.h"
#include "webbase/wo_sa_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/varobject_helper.h"
#include "Coding/core_bundle/framework/krt/krtstring.h"
#include "ettools/ettools_encode_decoder.h"
#include "dbsheet/et_dbsheet_filter_helper.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"
#include "helpers/db_token_helper.h"
#include "db_token_generator.h"
#include "helpers/statsheet_helper.h"
#include "webhook/webhook_helper.h"
#include "webhook/webhook_enum.h"
#include "common_log/common_log_helper.h"
#include "db/db_basic_itf.h"

namespace wo
{

namespace
{

// 当且仅当keyName非空时调用addKey
void AddKey(ISerialAcceptor* acpt, WebName keyName)
{
    if (keyName)
        acpt->addKey(keyName, true); // 使用变量作为key时, key需要深拷贝
}

}

DbSheetFieldRetriever::DbSheetFieldRetriever()
    : m_pFieldsManager(nullptr)
    , m_bPreferId(false)
{}

void DbSheetFieldRetriever::Reset(IDbFieldsManager *pFieldsManager, bool bPreferId)
{
    m_pFieldsManager = pFieldsManager;
    m_bPreferId = bPreferId;
    m_fieldNameIdMap.clear();
}

HRESULT DbSheetFieldRetriever::GetField(PCWSTR fieldKey, IDbField **ppField)
{
    ASSERT(m_pFieldsManager);
    if (m_bPreferId)
    {
        EtDbId id = INV_EtDbId;
        _appcore_GainDbSheetContext()->DecodeEtDbId(fieldKey, &id);
        return m_pFieldsManager->GetField(id, ppField);
    }
    const auto it = m_fieldNameIdMap.find(fieldKey);
    if (it != m_fieldNameIdMap.cend())
        return m_pFieldsManager->GetField(it->second, ppField);

    HRESULT hr = m_pFieldsManager->FindField(fieldKey, ppField);
    if (FAILED(hr))
        return hr;
    m_fieldNameIdMap.insert({fieldKey, (*ppField)->GetID()});
    return S_OK;
}

QString DbSheetFieldRetriever::GetFieldName(EtDbId fieldId)
{
    ASSERT(m_pFieldsManager);
    if (m_bPreferId)
    {
        EtDbIdStr buf;
        VS(_appcore_GainDbSheetContext()->EncodeEtDbId(fieldId, &buf));
        return krt::fromUtf16(buf);
    }
    ks_stdptr<IDbField> spField;
    VS(m_pFieldsManager->GetField(fieldId, &spField));
    return krt::fromUtf16(spField->GetName());
}

HRESULT DbSheetFieldRetriever::GainFieldValUniqueGuard(EtDbId fldId)
{
    auto it = m_fieldValCacheMap.find(fldId);
    if (it != m_fieldValCacheMap.end())
        return S_OK;

    ASSERT(m_pFieldsManager);
    auto result = m_fieldValCacheMap.emplace(fldId, std::make_unique<KDbFieldValCacheGuard>(fldId, m_pFieldsManager));
    if (!result.second)
        return E_FAIL;

    return S_OK;
}

// ================== DbSheetValueSerialiser ==================
DbSheetValueSerialiser::DbSheetValueSerialiser(IKWorkbook *pWorkbook, IKWorksheet *pWorksheet, ISerialAcceptor *acpt, const VarObj& param, ks_wstring* pErrorMsg)
    : m_pCellImages(pWorkbook->GetCellImages())
    , m_acpt(acpt)
    , m_pDbCtx(_appcore_GainDbSheetContext())
    , m_pEncodeDecoder(_appcore_GainEncodeDecoder())
    , m_pBook(pWorkbook->GetBook())
    , m_bEnableValCacheOpt(false)
    , m_pErrorMsg(pErrorMsg)
{
    m_pSheet = pWorksheet->GetSheet();
	VS(DbSheet::GetDBSheetOp(m_pSheet, &m_spDbSheetOp));
	VS(DbSheet::GetDBSheetViews(m_pSheet, &m_spDbSheetViews));
    m_pFieldManager = m_spDbSheetOp->GetFieldsManager();
    parseBoolParams(param);
    m_fieldRetriever.Reset(m_pFieldManager, m_bPreferId);

    _etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools);
    m_spStringTools->SetEnv(m_pSheet);

    parseFields(param);
    ks_stdptr<IKEtFileVersion> spEtFileVersion;
    m_pBook->GetExtDataItem(edFileVersion, (IUnknown**)&spEtFileVersion);
    if (spEtFileVersion)
        m_bookVersion = spEtFileVersion->GetKSheetBookVersion();
		
	ks_stdptr<IUnknown> spUnknown;
	VS(m_pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	spDBUserGroups->GetJudgement(&m_spProtectionJudgement);
}

HRESULT DbSheetValueSerialiser::SerialiseView(IDBSheetView *pView)
{
    PCWSTR typeStr = nullptr;
    VS(m_pEncodeDecoder->EncodeViewType(pView->GetType(), &typeStr));
    m_acpt->addString("type", typeStr);
    EtDbIdStr buf;
    VS(m_pDbCtx->EncodeEtDbId(pView->GetId(), &buf));
    m_acpt->addString("id", buf);
    m_acpt->addString("name", pView->GetName());
    m_acpt->addString("notice", pView->GetNotice());

    const IDBIds *pRecords = pView->GetVisibleRecords();
    m_acpt->addUint32("recordsCount", pRecords->Count());

    VS(SerialiseViewSettings(pView));

    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseViewSettings(IDBSheetView *pView)
{
    VS(SerialiseViewGroupSetting(pView));
    VS(SerialiseViewSortSetting(pView));
    VS(SerialiseViewFilterSetting(pView));
    VS(SerialiseViewStatisticsSetting(pView));
    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseViewGroupSetting(IDBSheetView *pView)
{
    IDBRecordsOrderManager* pMgr = pView->GetMutableOrderManager();
    UINT groupCondCnt = pMgr->GetGroupConditionCount();
    if (groupCondCnt > 0)
    {
        m_acpt->addKey("group");
        m_acpt->beginStruct();
            m_acpt->addKey("conditions");
            m_acpt->beginArray();
            for (int i = 0; i < groupCondCnt; i++)
            {
                IDBRecordsOrderCondition *pCond = pMgr->GetGroupCondition(i);
                m_acpt->beginStruct();
                    QString fieldName = m_fieldRetriever.GetFieldName(pCond->GetKeyFieldId());
                    m_acpt->addString("field", krt::utf16(fieldName));
                    m_acpt->addBool("ascending", pCond->GetAscending());
                m_acpt->endStruct();
            }
            m_acpt->endArray();
        m_acpt->endStruct();
    }

    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseViewSortSetting(IDBSheetView *pView)
{
    IDBRecordsOrderManager* pMgr = pView->GetMutableOrderManager();
    UINT sortCondCnt = pMgr->GetSortConditionCount();
    if (sortCondCnt > 0)
    {
        m_acpt->addKey("sort");
        m_acpt->beginStruct();
            m_acpt->addBool("autoSort", pMgr->GetAutoSort());
            m_acpt->addKey("conditions");
            m_acpt->beginArray();
            for (int i = 0; i < sortCondCnt; i++)
            {
                IDBRecordsOrderCondition *pCond = pMgr->GetSortCondition(i);
                m_acpt->beginStruct();
                    QString fieldName = m_fieldRetriever.GetFieldName(pCond->GetKeyFieldId());
                    m_acpt->addString("field", krt::utf16(fieldName));
                    m_acpt->addBool("ascending", pCond->GetAscending());
                m_acpt->endStruct();
            }
            m_acpt->endArray();
        m_acpt->endStruct();
    }

    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseViewFilterSetting(IDBSheetView *pView)
{
    IDbFilter* pFilter = pView->GetMutableFilter();
    UINT filtersCnt = pFilter->GetFiltersCount();
    if (filtersCnt > 0)
    {
        m_acpt->addKey("filter");
        m_acpt->beginStruct();
            KDbFilterOpType mode = pFilter->GetOperator();
            PCWSTR modeStr = nullptr;
            VS(m_pEncodeDecoder->EncodeKDbFilterOpType(mode, &modeStr));
            m_acpt->addString("mode", modeStr);
            m_acpt->addKey("conditions");
            m_acpt->beginArray();
            for (int i = 0; i < filtersCnt; i++)
            {
                ks_stdptr<IDbFieldFilter> spFieldFilter;
                VS(pFilter->GetFilter(i, &spFieldFilter));
                m_acpt->beginStruct();
                    QString fieldName = m_fieldRetriever.GetFieldName(spFieldFilter->GetFieldId());
                    m_acpt->addString("field", krt::utf16(fieldName));
                    VS(SerialiseViewFilterCriteria(spFieldFilter->GetCriteria()));
                m_acpt->endStruct();
            }
            m_acpt->endArray();
        m_acpt->endStruct();
    }

    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseViewFilterCriteria(const IDbFilterCriteria *pCriteria)
{
    m_acpt->addKey("criteria");
    m_acpt->beginStruct();
        KDbFilterCriteriaOpType op = pCriteria->GetCriteriaOp();
        PCWSTR opStr = nullptr;
        VS(m_pEncodeDecoder->EncodeKDbFilterCriteriaOpType(op, &opStr));
        m_acpt->addString("op", opStr);
        UINT valuesCnt = pCriteria->GetValuesCount();
        m_acpt->addKey("values");
        m_acpt->beginArray();
        for (int i = 0; i < valuesCnt; i++)
        {
            m_acpt->beginStruct();
            auto pValue = const_cast<IDbFcValueBase*>(pCriteria->GetValue(i));
            SerialiseFilterCriteriaValue(pValue, m_acpt);
            m_acpt->endStruct();
        }
        m_acpt->endArray();
    m_acpt->endStruct();

    return S_OK;
}

void SerialiseFilterCriteriaValue(IDbFcValueBase* pValue, ISerialAcceptor* pAcpt)
{
    if (!pValue || !pAcpt)
        return;

    KDbFcValueType valueType = pValue->GetType();
    PCWSTR valueTypeStr = nullptr;
    VS(_appcore_GainEncodeDecoder()->EncodeKDbFcValueType(valueType, &valueTypeStr));
    pAcpt->addString("type", valueTypeStr ? valueTypeStr : __X(""));
    switch (valueType)
    {
        case DBFCVT_Null:
        case DBFCVT_Any:
            break;
        case DBFCVT_Text:
        {
            ks_stdptr<IDbFcValueText> spText = pValue;
            ASSERT(spText);
            pAcpt->addString("value", spText->GetValue());
            break;
        }
        case DBFCVT_Num:
        {
            ks_stdptr<IDbFcValueNum> spNum = pValue;
            ASSERT(spNum);
            pAcpt->addFloat64("value", spNum->GetValue());
            break;
        }
        case DBFCVT_Date:
        {
            ks_stdptr<IDbFcValueDate> spDate = pValue;
            ASSERT(spDate);
            const KDbFcValueDateInfo &value = spDate->GetValue();
            pAcpt->addInt32("year", value.year);
            pAcpt->addInt32("mon", value.mon);
            pAcpt->addInt32("mday", value.mday);
            pAcpt->addInt32("hour", value.hour);
            pAcpt->addInt32("min", value.min);
            pAcpt->addInt32("sec", value.sec);
            break;
        }
        case DBFCVT_Time:
        {
            ks_stdptr<IDbFcValueTime> spTime = pValue;
            ASSERT(spTime);
            const KDbFcValueTimeInfo &value = spTime->GetValue();
            pAcpt->addInt32("hour", value.hour);
            pAcpt->addInt32("min", value.min);
            pAcpt->addInt32("sec", value.sec);
            break;
        }
        case DBFCVT_Contact:
        {
            ks_stdptr<IDbFcValueContact> spVal = pValue;
            ASSERT(spVal);
            pAcpt->addString("value", spVal->GetValue());
            break;
        }
        case DBFCVT_Checkbox:
        {
            ks_stdptr<IDbFcValueCheckbox> spVal = pValue;
            ASSERT(spVal);
            pAcpt->addBool("value", spVal->GetValue());
            break;
        }
        case DBFCVT_Link:
        {
            ks_stdptr<IDbFcValueLink> spVal = pValue;
            ASSERT(spVal);
            EtDbId fieldId = spVal->GetValue();
            EtDbIdStr buf;
            VS(_appcore_GainDbSheetContext()->EncodeEtDbId(fieldId, &buf));
            pAcpt->addString("value", buf);
            break;
        }
        case DBFCVT_DynamicSimple:
        {
            ks_stdptr<IDbFcValueDynamicSimple> spVal = pValue;
            ASSERT(spVal);
            PCWSTR dynamicTypeStr = nullptr;
            VS(_appcore_GainEncodeDecoder()->EncodeKDbFcDynamicType(spVal->GetDynamicType(), &dynamicTypeStr));
            pAcpt->addString("value", dynamicTypeStr ? dynamicTypeStr : __X(""));
            break;
        }
        default:
            ASSERT(FALSE);
            break;
    }
}

HRESULT DbSheetValueSerialiser::SerialiseViewStatisticsSetting(IDBSheetView *pView)
{
    IDBStatisticOptions *pStatOp = pView->GetMutableStatisticOptions();
    UINT statOpCnt = pStatOp->Count();
    if (statOpCnt > 0)
    {
        class StatOptionsEnum : public IDBStatisticOptionsEnum
        {
        public:
            StatOptionsEnum(ISerialAcceptor *acpt, IDBSheetCtx *pDbCtx, IEncodeDecoder *pEncodeDecoder)
                : m_acpt(acpt)
                , m_pDbCtx(pDbCtx)
                , m_pEncodeDecoder(pEncodeDecoder)
            {}

            STDPROC Do(EtDbId id, ET_DBSheet_StatisticOption option) override
            {
                m_acpt->beginStruct();
                    VS(m_pDbCtx->EncodeEtDbId(id, &m_buf));
                    m_acpt->addString("field", m_buf);
                    PCWSTR opStr = nullptr;
                    VS(m_pEncodeDecoder->EncodeStatisticOption(option, &opStr));
                    m_acpt->addString("option", opStr);
                m_acpt->endStruct();
                return S_OK;
            }
        private:
            EtDbIdStr m_buf;
            ISerialAcceptor *m_acpt;
            IDBSheetCtx *m_pDbCtx;
            IEncodeDecoder* m_pEncodeDecoder;
        } soe(m_acpt, m_pDbCtx, m_pEncodeDecoder);

        m_acpt->addKey("statistics");
        m_acpt->beginStruct();
            m_acpt->addKey("conditions");
            m_acpt->beginArray();
                pStatOp->Enum(&soe);
            m_acpt->endArray();
        m_acpt->endStruct();
    }

    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseSheet()
{
    PCWSTR sheetName = nullptr;
    m_pSheet->GetName(&sheetName);
    UINT iStSheet = m_pSheet->GetStId();

    m_acpt->addString("name", sheetName);
    m_acpt->addUint32("id", iStSheet);
    m_acpt->addString("description", m_spDbSheetOp->GetSheetDescription());
    m_acpt->addString("icon", m_spDbSheetOp->GetSheetIcon());

    const IDBIds *pRecords = m_spDbSheetOp->GetAllRecords();
    uint32 recordsCount = pRecords->Count() - GetNoPermissionRecord().size();
    m_acpt->addUint32("recordsCount", recordsCount);

    if (m_bIncludeAllRecIds)
    {
        m_acpt->addKey("recordIds");
        std::unordered_set<EtDbId>  noPermissionRecord = GetNoPermissionRecord();
        m_acpt->beginArray();
        for (int i = 0; i < pRecords->Count(); ++i)
        {
            EtDbId recordId = pRecords->IdAt(i);
            if (noPermissionRecord.find(recordId) == noPermissionRecord.end())
            {
                EtDbIdStr buf;
                VS(m_pDbCtx->EncodeEtDbId(pRecords->IdAt(i), &buf));
                m_acpt->addString(nullptr, buf);
            }
        }
        m_acpt->endArray();
    }

    EtDbIdStr buf;
    VS(m_pDbCtx->EncodeEtDbId(m_pFieldManager->GetPrimaryField(), &buf));
    m_acpt->addString("primaryFieldId", buf);
	VS(m_pDbCtx->EncodeEtDbId(m_pFieldManager->GetSyncFieldSourceId(), &buf));
	m_acpt->addString("syncFieldSourceId", buf);
    VS(m_pDbCtx->EncodeEtDbId(m_pFieldManager->GetSyncFieldSourceNameId(), &buf));
	m_acpt->addString("syncFieldSourceNameId", buf);

    PCWSTR syncTypeStr = nullptr;
    VS(_appcore_GainEncodeDecoder()->EncodeDbSheetSyncType(m_spDbSheetOp->GetSheetSyncType(), &syncTypeStr));
    m_acpt->addString("syncType", syncTypeStr);

    PCWSTR subTypeStr = nullptr;
    VS(_appcore_GainEncodeDecoder()->EncodeDbSheetSubType(m_spDbSheetOp->GetSheetSubType(), &subTypeStr));
    m_acpt->addString("subType", subTypeStr);
    
    if (m_spDbSheetOp->GetSheetSyncType() == DbSheet_St_DB || m_spDbSheetOp->GetSheetSyncType() == DbSheet_St_Cross_DB)
    {
        PCWSTR fullupdateModeStr = nullptr;
        VS(_appcore_GainEncodeDecoder()->EncodeDbSyncSheetFullupdateMode(m_spDbSheetOp->GetSyncSheetFullupdateMode(), &fullupdateModeStr));
        m_acpt->addString("syncSheetFullupdateMode", fullupdateModeStr);
    }

    ASSERT(!m_customFields);
    SerialiseFields("fields");

    m_acpt->addKey("views");
    m_acpt->beginArray();
    BMP_PTR pBmp = m_pBook->GetBMP();
    if (pBmp->bKsheet && m_bookVersion >= KSheet_Book_Version_AppSheet)
    {
        IDBSheetView* pDefaultView = m_spDbSheetViews->GetDefaultView();
        ASSERT(pDefaultView);
        m_acpt->beginStruct();
        VS(SerialiseView(pDefaultView));
        m_acpt->endStruct();
    }
    else
    {
        ks_stdptr<IDBSheetViewsEnum> spEnum;
        if (SUCCEEDED(m_spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
        {
            do
            {
                if (spEnum->GetCurType() == Et_DBSheetViewUse_ForDashboard)
                {
                    if (FAILED(spEnum->SkipCurType()))
                        break;
                    continue;
                }
                ks_stdptr<IDBSheetView> spDbSheetView;
                spEnum->GetCurView(&spDbSheetView);
                if (!spDbSheetView)
                    continue;

                m_acpt->beginStruct();
                VS(SerialiseView(spDbSheetView));
                m_acpt->endStruct();

            }while (SUCCEEDED(spEnum->Next()));
        }
    }
    m_acpt->endArray();

    return S_OK;
}

HRESULT DbSheetValueSerialiser::serializeField_impl(IDbField* pField, bool needBaseFieldInfo)
{
    const auto basicInformationSerializer {[&](const ET_DbSheet_FieldType &type) {
        PCWSTR typeStr = nullptr;
        VS(m_pEncodeDecoder->EncodeFieldType(type, &typeStr));
        ASSERT(typeStr);
        m_acpt->addString("type", typeStr);
        EtDbIdStr buf;
        VS(m_pDbCtx->EncodeEtDbId(pField->GetID(), &buf));
        m_acpt->addString("id", buf);
        m_acpt->addString("name", pField->GetName());
        m_acpt->addString("numberFormat", pField->GetNumberFormat());
        m_acpt->addString("description", pField->GetDescription());
        SerialiseFieldDefaultValue(pField);
        m_acpt->addBool("syncField", alg::BOOL2bool(pField->IsSyncField()));
        m_acpt->addBool("arraySupport", alg::BOOL2bool(pField->IsArray()));
        VS(m_pDbCtx->EncodeEtDbId(pField->GetAutoFillSourceField(), &buf));
        m_acpt->addString("autoFillSourceField", buf);
        m_acpt->addString("customConfig", pField->GetCustomConfig());
        if (Et_DbSheetField_FormulaResult == type)
        {
            PCWSTR valueTypeLiteral = __X("");
            _appcore_GainEncodeDecoder()->EncodeDbFieldValueType(pField->GetValueType(), &valueTypeLiteral);
            m_acpt->addString("valueType", valueTypeLiteral);
        }
    }};
    const auto type {pField->GetType()};
    if (pField->IsSyncLookupField())
    {
        basicInformationSerializer(Et_DbSheetField_Lookup);
        this->m_acpt->addKey("baseFieldInfo");
        this->m_acpt->beginStruct();
            basicInformationSerializer(type);
            SerialiseExtraFieldInfo(pField, m_acpt, m_pDbCtx, m_pBook);
        this->m_acpt->endStruct();
    }
    else
    {
        basicInformationSerializer(type);
    }
    SerialiseFieldValueUnique(pField);
    //如果字段带有Array属性，则需要当作一个lookup字段来看待，只是里面的数据是dummy的。
    if(pField->IsSyncLookupField() || Et_DbSheetField_FormulaResult == pField->GetType())
    {
        EtDbIdStr buf;
        VS(m_pDbCtx->EncodeEtDbId(INV_EtDbId, &buf));
        ET_DbSheet_FieldType type = pField->GetType();
        switch (type)
        {
            case Et_DbSheetField_Formula:
            case Et_DbSheetField_FormulaResult:
            case Et_DbSheetField_Lookup:
                type = pField->ConvertFieldTypeByFormulaValueType();
                break;
            default:
                break;
        }
        PCWSTR typeStr = __X("");
        VS(_appcore_GainEncodeDecoder()->EncodeFieldType(type, &typeStr));
        m_acpt->addString("linkField", buf);
        m_acpt->addString("lookupField", buf);
        m_acpt->addString("baseType", typeStr);
        PCWSTR valueTypeLiteral = __X("");
        _appcore_GainEncodeDecoder()->EncodeDbFieldValueType(pField->GetValueType(), &valueTypeLiteral);
        m_acpt->addString("valueType", valueTypeLiteral);
        m_acpt->addString("aggregation", __X(""));
    }
    else
    {
        SerialiseExtraFieldInfo(pField, m_acpt, m_pDbCtx, m_pBook);
    }
    if (needBaseFieldInfo)
    {
        switch (type)
        {
        case Et_DbSheetField_Lookup:
        {
            ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
            ks_stdptr<IDbField> spBaseField;
            spField_Lookup->GetLookupBaseField(&spBaseField);
            if (spBaseField)
            {
                m_acpt->addKey("baseFieldInfo");
                m_acpt->beginStruct();
                this->serializeField_impl(spBaseField.get(), false);
                m_acpt->endStruct();
                PCWSTR valueTypeLiteral = __X("");
                _appcore_GainEncodeDecoder()->EncodeDbFieldValueType(pField->GetValueType(), &valueTypeLiteral);
                m_acpt->addString("valueType", valueTypeLiteral);
            }
            break;
        }
        default:
            break;
        }
    }
    return S_OK;
}
HRESULT DbSheetValueSerialiser::SerialiseField(EtDbId fieldId)
{
    ks_stdptr<IDbField> spField;
    m_pFieldManager->GetField(fieldId, &spField);
    if (spField == nullptr)
        return E_DBSHEET_FIELD_NOT_FOUND;
    return this->serializeField_impl(spField);
}

HRESULT DbSheetValueSerialiser::SerialiseFields(const char* keyName)
{
    std::vector<EtDbId> fldIds;
    getSerialiseFieldIds(fldIds);

    AddKey(m_acpt, keyName);
    m_acpt->beginArray();
    for (EtDbIdx fld = 0, cnt = fldIds.size(); fld < cnt; fld++)
    {
        EtDbId fieldId = fldIds.at(fld);
        ASSERT(fieldId != INV_EtDbId);
        m_acpt->beginStruct();
        VS(SerialiseField(fieldId));
        m_acpt->endStruct();
    }
    m_acpt->endArray();

    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseRecord(EtDbId recordId)
{
    const IDBIds *pRecords = m_spDbSheetOp->GetAllRecords();
    if (pRecords->Id2Idx(recordId) == INV_EtDbIdx)
        return E_DBSHEET_RECORD_NOT_FOUND;

    std::vector<EtDbId> fldIds;
    getSerialiseFieldIds(fldIds);
    serialiseRecord(recordId, fldIds);
    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseRecords(const std::vector<EtDbId>& recordIds)
{
    std::vector<EtDbId> fldIds;
    getSerialiseFieldIds(fldIds);

    const IDBIds* pRecords = m_spDbSheetOp->GetAllRecords();
    std::unordered_set<EtDbId>  noPermissionRecord = GetNoPermissionRecord();
    AddKey(m_acpt, "records");
    m_acpt->beginArray();
    for (size_t i = 0, count = recordIds.size(); i < count; ++i)
    {
        const EtDbId& recordId = recordIds[i];
		if (pRecords->Id2Idx(recordId) == INV_EtDbIdx)
            continue;
        if (noPermissionRecord.find(recordId) == noPermissionRecord.end())
        {
            m_acpt->beginStruct();
            serialiseRecord(recordId, fldIds);
            m_acpt->endStruct();
        }
    }
    m_acpt->endArray();

    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseRecord(EtDbId recordId, const std::vector<EtDbId>& fieldIds)
{
    EtDbIdStr buf;
    VS(m_pDbCtx->EncodeEtDbId(recordId, &buf));
    m_acpt->addString("id", buf);

    if (m_showRecordExtraInfo)
    {
        IDbRecordsManager *pRecordsManager = m_spDbSheetOp->GetRecordsManager();
        m_acpt->addString("creator", pRecordsManager->GetRecordCreatorId(recordId));
        double createdTime = 0;
        if (SUCCEEDED(pRecordsManager->GetRecordCreatedTime(recordId, createdTime)))
        {
            alg::managed_vdbl_token_assist mvta;
            mvta.create(createdTime);
            _SerialiseDateTime("createdTime", mvta);
        }
        double lastModifiedTime = 0;
        PCWSTR lastModifierId = __X("");
        pRecordsManager->GetRecordLastModifiedInfo(recordId, lastModifiedTime, lastModifierId);
        m_acpt->addString("lastModifiedBy", lastModifierId);
        if (lastModifiedTime == 0)
        {
            m_acpt->addString("lastModifiedTime", __X(""));
        }
        else
        {
            alg::managed_vdbl_token_assist mvta;
            mvta.create(lastModifiedTime);
            _SerialiseDateTime("lastModifiedTime", mvta);
        }
    }

    if (fieldIds.size() > 0)
    {
        m_acpt->addKey("fields");
        m_acpt->beginStruct();
        for (size_t i = 0, c = fieldIds.size(); i < c; ++i)
            VS(serialiseCell(recordId, fieldIds.at(i)));
        m_acpt->endStruct();
    }
    return S_OK;
}

HRESULT DbSheetValueSerialiser::SerialiseAllRecords()
{
    const IDBIds* pRecords = m_spDbSheetOp->GetAllRecords();
    m_acpt->beginArray();
    for (size_t i = 0, count = pRecords->Count(); i < count; ++i)
    {
        const EtDbId& recordId = pRecords->IdAt(i);
        m_acpt->beginStruct();
        VS(SerialiseRecord(recordId));
        m_acpt->endStruct();
    }
    m_acpt->endArray();

    return S_OK;
}

HRESULT DbSheetValueSerialiser::getSerialiseFieldIds(std::vector<EtDbId>& filedIds)
{
    filedIds.clear();
    std::unordered_set<EtDbId>  noPermissionFieldId = GetNoPermissionField();
    if (!m_customFields)
    {
        const IDBIds *pFields = m_pView ? m_pView ->GetVisibleFields() : m_spDbSheetOp->GetAllFields();
        filedIds.reserve(pFields->Count());
        for (EtDbIdx i = 0, c = pFields->Count(); i < c; ++i)
        {
            EtDbId fieldId = pFields->IdAt(i);
            ASSERT(fieldId != INV_EtDbId);
            if (noPermissionFieldId.find(fieldId) == noPermissionFieldId.end())
                filedIds.emplace_back(fieldId);
        }     
    }
    else
    {
        filedIds.reserve(m_fieldIds.size());
        for (EtDbId fieldId : m_fieldIds)
        {
            if (noPermissionFieldId.find(fieldId) == noPermissionFieldId.end())
                filedIds.emplace_back(fieldId);
        }
    }

    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseCellForceText(EtDbId recordId, EtDbId fieldId)
{
    ks_stdptr<IDbField> spField;
    HRESULT hr = m_pFieldManager->GetField(fieldId, &spField);
    if (spField == nullptr)
        return E_DBSHEET_FIELD_NOT_FOUND;

    ks_bstr displayText;
    hr = m_spDbSheetOp->GetDisplayString(recordId, fieldId, &displayText);
    if (FAILED(hr))
        return hr;

    if (displayText.empty())
        return S_OK;

    QString qFieldName;
    if (m_bPreferId)
    {
        EtDbIdStr buf;
        VS(m_pDbCtx->EncodeEtDbId(fieldId, &buf));
        qFieldName = krt::fromUtf16(buf);
    }
    else
    {
        qFieldName = krt::fromUtf16(spField->GetName());
    }
    ks_string fieldName(qFieldName.toUtf8());
    return serialiseText(fieldName.c_str(), displayText.c_str());
}

bool DbSheetValueSerialiser::isNeedSerialiseForLookup(IDbField* pField)
{
    if (pField->GetType() != Et_DbSheetField_Lookup)
        return false;
    constexpr struct {
        bool operator()(IDbField *pField) const {
            ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
            ks_stdptr<IDbField> spBaseField;
            spField_Lookup->GetLookupBaseField(&spBaseField);
            if(!spBaseField)//base field not exist(eg: base field deleted)
                return false;

            const auto type {spBaseField->GetType()};
            return type == Et_DbSheetField_Formula;
        }
    } isLookingupFormula {};
    ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
    const ET_DbSheet_Lookup_Function lookupFunction = spField_Lookup->GetLookupFunction();
    ks_stdptr<IDbField> spNextLookupBaseField;
    spField_Lookup->GetNextLookupBaseField(&spNextLookupBaseField);
    bool isSerialiseForLookup = false;
    if (spNextLookupBaseField)
    {
        if (spNextLookupBaseField->GetType() == Et_DbSheetField_Lookup)
        {
            ks_stdptr<IDbField> spBaseField;
            spField_Lookup->GetLookupBaseField(&spBaseField);
            if (spBaseField)    
            {
                //引用引用类型，多层嵌套，但不能转换到基础类型，需要转公式结果字段，token单独序列化
                if (spBaseField->GetType() == Et_DbSheetField_Lookup)
                    isSerialiseForLookup = true;
                else if (lookupFunction != DbSheet_Lookup_Function_Origin && lookupFunction != DbSheet_Lookup_Function_Unique)
                    isSerialiseForLookup = true;
            }
        }
        else
        {
            if (lookupFunction != DbSheet_Lookup_Function_Origin && lookupFunction != DbSheet_Lookup_Function_Unique)
            {
                isSerialiseForLookup = true; //引用非引用类型，聚合函数为非原始值/唯一值， 需要转公式结果字段，token单独序列化
            }
        }
    }
    return isSerialiseForLookup || isLookingupFormula(pField);
}

inline bool isNeedSerialiseForFakeFormula(IDbField* pField)
{
    return !pField->IsArray() && pField->GetType() == Et_DbSheetField_FormulaResult;
}

bool DbSheetValueSerialiser::isNeedSerialiseForFormula(IDbField* pField)
{
    return pField->GetType() == Et_DbSheetField_Formula || isNeedSerialiseForFakeFormula(pField);
}

void DbSheetValueSerialiser::_serialiseDataKey(ISerialAcceptor* acpt, const_token_ptr pToken)
{
    acpt->addKey("data", true);
    switch (GetExecTokenMajorType(pToken))
    {
        using namespace alg;
        using namespace db_token_helper;
        case ETP_HANDLE:
        {
            helper<ETP_HANDLE> {pToken}.serialize(acpt);
            break;
        }
        case ETP_VSTR:
        {
            helper<ETP_VSTR> {pToken}.serialize(acpt);
            break;
        }
        case ETP_VINT:
        {
            helper<ETP_VINT> {pToken}.serialize(acpt);
            break;
        }
        case ETP_VDBL:
        {
            helper<ETP_VDBL> {pToken}.serialize(acpt);
            break;
        }
        case ETP_ERROR:
        {
            helper<ETP_ERROR> {pToken}.serialize(acpt);
            break;
        }
        case ETP_VBOOL:
        {
            helper<ETP_VBOOL> {pToken}.serialize(acpt);
            break;
        }
        default:
            ASSERT("其它情况若涉及请自行添加" == nullptr);
            break;
    }
}

HRESULT DbSheetValueSerialiser::serialiseCell(EtDbId recordId, EtDbId fieldId)
{
    ks_stdptr<IDbField> spField;
    HRESULT hr = m_pFieldManager->GetField(fieldId, &spField);
    if (spField == nullptr)
        return E_DBSHEET_FIELD_NOT_FOUND;

    QString fieldName = m_fieldRetriever.GetFieldName(fieldId);
    const_token_ptr pToken = nullptr;
    hr = m_spDbSheetOp->GetValueToken(recordId, fieldId, &pToken);
    if (FAILED(hr))
        return hr;
    if (nullptr == pToken)
        return S_FALSE;

    if (wo::DbSheet::statistic_sheet_helper::needDiscardZeroDoubleToken(spField, pToken))
        return S_FALSE;

    if (m_textValue == TVT_Original)
        return serialiseContent(fieldName.toUtf8(), recordId, spField.get(), pToken);

    ks_bstr displayText;
    hr = m_spDbSheetOp->GetDisplayString(recordId, fieldId, &displayText);
    if (FAILED(hr))
        return hr;

    switch (m_textValue)
    {
        case TVT_Text:
        {
            return serialiseText(fieldName.toUtf8(), displayText.c_str());
        }
        case TVT_NumberToText:
        {
            ET_DbSheet_FieldType type = spField->GetType();
            switch (type)
            {
                case Et_DbSheetField_Date:
                case Et_DbSheetField_Time:
                case Et_DbSheetField_CreatedTime:
                case Et_DbSheetField_LastModifiedTime:
                case Et_DbSheetField_Number:
                case Et_DbSheetField_Currency:
                case Et_DbSheetField_Percentage:
                case Et_DbSheetField_Rating:
                case Et_DbSheetField_Complete:
                case Et_DbSheetField_AutoNumber:
                case Et_DbSheetField_Formula:
                    return serialiseText(fieldName.toUtf8(), displayText.c_str());
                default:
                    return serialiseContent(fieldName.toUtf8(), recordId, spField.get(), pToken);
            }
        }
        case TVT_Compound:
        {
            AddKey(m_acpt, fieldName.toUtf8());
            wo::sa::Leave leave = wo::sa::enterStruct(m_acpt, nullptr);
            m_acpt->addString("text", displayText.c_str());
            VS(serialiseContent("value", recordId, spField.get(), pToken));
            if (isNeedSerialiseForLookup(spField) || isNeedSerialiseForFakeFormula(spField))
                _serialiseDataKey(m_acpt, pToken);
            return S_OK;
        }
        case TVT_FormulaCompound:
        {
            AddKey(m_acpt, fieldName.toUtf8());
            wo::sa::Leave leave = wo::sa::enterStruct(m_acpt, nullptr);
            m_acpt->addString("text", displayText.c_str());
            VS(serialiseContent("value", recordId, spField.get(), pToken));
            if (isNeedSerialiseForLookup(spField) || isNeedSerialiseForFormula(spField))
                _serialiseDataKey(m_acpt, pToken);
            return S_OK;
        }
        default:
            ASSERT(FALSE);
            return S_OK;
    }
}

HRESULT DbSheetValueSerialiser::serialiseContent(WebName fieldName, EtDbId recordId, IDbField* pField, const_token_ptr pToken, bool serializeDirectly)
{
    const ET_DbSheet_FieldType type = pField->GetType();
    if (!serializeDirectly && pField->IsSyncLookupField())
    {
        switch(type)
        {
        case Et_DbSheetField_FormulaResult:
            AddKey(this->m_acpt, fieldName);
            switch (GetExecTokenMajorType(pToken))
            {
                using namespace alg;
                using namespace db_token_helper;
            case ETP_VINT:
                helper<ETP_VINT> {pToken}.serialize(this->m_acpt);
                break;
            case ETP_VDBL:
                helper<ETP_VDBL> {pToken}.serialize(this->m_acpt);
                break;
            case ETP_VBOOL:
                helper<ETP_VBOOL> {pToken}.serialize(this->m_acpt);
                break;
            case ETP_VSTR:
                helper<ETP_VSTR> {pToken}.serialize(this->m_acpt);
                break;
            case ETP_ERROR:
                helper<ETP_ERROR> {pToken}.serialize(this->m_acpt);
                break;
            case ETP_HANDLE:
                helper<ETP_HANDLE> {pToken}.serialize(this->m_acpt);
                break;
            default:
                ASSERT("其它类型还未支持, 若需要请在此实现" == nullptr);
                break;
            }
            return S_OK;
        case Et_DbSheetField_CreatedBy:
        case Et_DbSheetField_LastModifiedBy:
        case Et_DbSheetField_Contact:
        case Et_DbSheetField_MultipleSelect:
        case Et_DbSheetField_Attachment:
            // 序列化支持直接写在对应字段处理的里面, 这里不作处理
            break;
        default:
        {
            if (alg::const_handle_token_assist::is_type(pToken))
            {
                alg::const_handle_token_assist assist(pToken);
                if (assist.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
                    return this->serialiseLookup(fieldName, pToken, pField);
            }
            break;
        }
        }
    }
    switch (type)
    {
        case Et_DbSheetField_MultiLineText:
        case Et_DbSheetField_SingleLineText:
        case Et_DbSheetField_ID:
        case Et_DbSheetField_Phone:
        case Et_DbSheetField_Email:
        case Et_DbSheetField_SingleSelect:
        case Et_DbSheetField_BarCode:
            return serialiseValueStr(fieldName, pToken, pField);
        case Et_DbSheetField_Attachment:
            return _SerialiseAttachment(fieldName, pToken);
        case Et_DbSheetField_Address:
        case Et_DbSheetField_Cascade:
            return _SerialiseAddress(fieldName, pToken);
        case Et_DbSheetField_Department:
            return _SerialiseDepartment(fieldName, pToken);
        case Et_DbSheetField_Note:
            return _SerialiseNote(fieldName, pToken);
        case Et_DbSheetField_Link:
        case Et_DbSheetField_OneWayLink:
            return serialiseLink(fieldName, pToken);
        case Et_DbSheetField_Lookup:
            return serialiseLookup(fieldName, pToken, pField);
        case Et_DbSheetField_ParentRecord:
            return serialiseParentRecord(fieldName, pToken);
        case Et_DbSheetField_Url:
            return serialiseUrl(fieldName, recordId, pField->GetID(), pToken);
        case Et_DbSheetField_Date:
        {
            if (DbSheet::ContainTimeFormat(pField->GetNumberFormat()))
                return _SerialiseDateTime(fieldName, pToken);
            else
                return _SerialiseDate(fieldName, pToken);
        }
        case Et_DbSheetField_Time:
            return _SerialiseTime(fieldName, pToken);
        case Et_DbSheetField_CreatedTime:
            return _SerialiseDateTime(fieldName, pToken);
        case Et_DbSheetField_LastModifiedTime:
            return _SerialiseDateTime(fieldName, pToken);
        case Et_DbSheetField_Number:
        case Et_DbSheetField_Currency:
        case Et_DbSheetField_Percentage:
        case Et_DbSheetField_Rating:
        case Et_DbSheetField_Complete:
        case Et_DbSheetField_AutoNumber:
            return _SerialiseNumber(fieldName, pToken);
        case Et_DbSheetField_Checkbox:
            return _SerialiseBool(fieldName, pToken);
        case Et_DbSheetField_MultipleSelect:
            return serialiseMultipleSelect(fieldName, pToken, pField);
        case Et_DbSheetField_CellPicture:
            return serialiseCellPicture(fieldName, pToken, pField);
        case Et_DbSheetField_CreatedBy:
            return serializeContactToken(fieldName, pToken, pField);
        case Et_DbSheetField_LastModifiedBy:
            return serializeContactToken(fieldName, pToken, pField);
        case Et_DbSheetField_Contact:
        {
            return serializeContactToken(fieldName, pToken, pField);
        }
        case Et_DbSheetField_Formula:
        {
            switch (pField->GetValueType())
            {
            case DbSheet_Fvt_Text:
                return serialiseValueStr(fieldName, pToken, pField);
            case DbSheet_Fvt_Number:
                return serialiseFormulaNumeric(fieldName, pToken, {alg::ETP_VDBL, alg::ETP_VINT}, 
                    std::bind(&DbSheetValueSerialiser::_SerialiseNumber, this, std::placeholders::_1, std::placeholders::_2));
            case DbSheet_Fvt_Contact:
                return serialiseContactTypeFormula(fieldName, pToken, pField);
            // datetime格式, 因为公式字段没有处理, 这里也不处理
            case DbSheet_Fvt_Date:
                if (DbSheet::ContainTimeFormat(pField->GetNumberFormat()))
                    return serialiseFormulaFormattedText(fieldName, pToken, __X("yyyy/mm/dd hh:mm:ss"),
                        std::bind(&DbSheetValueSerialiser::_SerialiseDateTime, this, std::placeholders::_1, std::placeholders::_2));
                else
                    return serialiseFormulaFormattedText(fieldName, pToken, __X("yyyy/mm/dd"),
                        std::bind(&DbSheetValueSerialiser::_SerialiseDate, this, std::placeholders::_1, std::placeholders::_2));
            case DbSheet_Fvt_Time:
                return serialiseFormulaFormattedText(fieldName, pToken, __X("hh:mm:ss"), 
                    std::bind(&DbSheetValueSerialiser::_SerialiseTime, this, std::placeholders::_1, std::placeholders::_2));
            case DbSheet_Fvt_Logic:
                return serialiseFormulaNumeric(fieldName, pToken, {alg::ETP_VDBL, alg::ETP_VINT, alg::ETP_VBOOL}, 
                    std::bind(&DbSheetValueSerialiser::_SerialiseBool, this, std::placeholders::_1, std::placeholders::_2));
            default:
                return serialiseText(fieldName, __X(""));
            }
        }
        default:
            ASSERT(FALSE);
            return E_FAIL;
    }
}

static void serialize_multipleSelection(const_token_ptr pToken, ISerialAcceptor* acpt) 
{
    if (pToken == nullptr)
        return;
    alg::const_handle_token_assist assist {pToken};
    if (assist.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
    {
        const IDbTokenArrayHandle *const pTokenArrayHandle = assist.get_handle()->CastArray();
        const UINT count = pTokenArrayHandle->GetCount();
        wo::sa::Leave leave = wo::sa::enterArray(acpt, nullptr);
        for (UINT i = 0; i < count; ++i)
        {
            const_token_ptr token {};
            pTokenArrayHandle->Item(i, &token);
            serialize_multipleSelection(token, acpt);
        }
        return;
    }
    alg::const_handle_token_assist chta(pToken);
    ASSERT(chta.get_handleType() == alg::ET_HANDLE_SELECTITEMS);
    ks_stdptr<IDbSelectItemHandle> spTokenSelect = chta.get_handle()->CastUnknown();
    wo::sa::Leave leave = wo::sa::enterArray(acpt, nullptr);
    for (UINT i = 0, c = spTokenSelect->Count(); i < c; ++i)
        acpt->addString(nullptr, spTokenSelect->ItemText(i));
}

HRESULT DbSheetValueSerialiser::serialiseText(WebName fieldName, PCWSTR text)
{
    AddKey(m_acpt, fieldName);
    m_acpt->addString(nullptr, text);
    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseValueStr(WebName fieldName, const_token_ptr pToken, IDbField* pField)
{
    ks_bstr text;
    HRESULT hr = pField->GetDbSheetData()->GetValueString(pToken, pField->GetID(), &text);
    if (FAILED(hr))
        return hr;
    if (text.empty())
        return S_FALSE;

    AddKey(m_acpt, fieldName);
    m_acpt->addString(nullptr, text);
    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseUrl(WebName fieldName, EtDbId recordId, EtDbId fieldId, const_token_ptr pToken)
{
    ks_bstr text;
    ks_bstr address;
    HRESULT hr = S_OK;
    ks_stdptr<IDbHyperlinkHandle> spTokenHyperlink;
    if (recordId == INV_EtDbId)
    {
        if (!alg::const_handle_token_assist::is_type(pToken))
            return S_OK;
        alg::const_handle_token_assist chta(pToken);
        alg::TOKEN_HANDLE handle = chta.get_handle();
        if (nullptr == handle || alg::ET_HANDLE_DBHYPERLINK != chta.get_handleType())
            return S_OK;
        spTokenHyperlink = handle->CastUnknown();
        text.assign(spTokenHyperlink->GetDisplayText());
        address.assign(spTokenHyperlink->GetAddress());
    }
    else
    {
        const_token_ptr pTokenHyperlink = nullptr;
        hr = m_spDbSheetOp->GetValueToken(recordId, fieldId, &pTokenHyperlink);
        if (FAILED(hr))
            return hr;
        if (!alg::const_handle_token_assist::is_type(pTokenHyperlink))
            return E_FAIL;
        alg::const_handle_token_assist chta(pToken);
        alg::TOKEN_HANDLE handle = chta.get_handle();
        if (handle == nullptr || alg::ET_HANDLE_DBHYPERLINK != chta.get_handleType())
            return E_FAIL;
        spTokenHyperlink = handle->CastUnknown();
        if (xstrlen(spTokenHyperlink->GetDisplayText()) == 0)
            return S_FALSE;
    }
    text.assign(spTokenHyperlink->GetDisplayText());
    address.assign(spTokenHyperlink->GetAddress());
    AddKey(m_acpt, fieldName);
    m_acpt->beginArray();
        m_acpt->beginStruct();
            m_acpt->addString("address", address);
            m_acpt->addString("displayText", text);
        m_acpt->endStruct();
    m_acpt->endArray();
    return hr;
}

HRESULT DbSheetValueSerialiser::_SerialiseDate(WebName fieldName, const_token_ptr pToken)
{
    kfc::nf::NFHANDLE hNf = m_pBook->GetNumFmtHandle(__X("yyyy/mm/dd"));
    if (hNf == nullptr)
        return E_FAIL;
    ks_bstr text;
    HRESULT hr = m_spStringTools->GetCellText(pToken, hNf, &text, NULL);
    if (FAILED(hr))
        return hr;

    return serialiseText(fieldName, text);
}

HRESULT DbSheetValueSerialiser::_SerialiseTime(WebName fieldName, const_token_ptr pToken)
{
    kfc::nf::NFHANDLE hNf = m_pBook->GetNumFmtHandle(__X("hh:mm:ss"));
    if (hNf == nullptr)
        return E_FAIL;
    ks_bstr text;
    HRESULT hr = m_spStringTools->GetCellText(pToken, hNf, &text, NULL);
    if (FAILED(hr))
        return hr;

    return serialiseText(fieldName, text);
}

HRESULT DbSheetValueSerialiser::_SerialiseDateTime(WebName fieldName, const_token_ptr pToken)
{
    kfc::nf::NFHANDLE hNf = m_pBook->GetNumFmtHandle(__X("yyyy/mm/dd hh:mm:ss"));
    if (hNf == nullptr)
        return E_FAIL;
    ks_bstr text;
    HRESULT hr = m_spStringTools->GetCellText(pToken, hNf, &text, NULL);
    if (FAILED(hr))
        return hr;

    return serialiseText(fieldName, text);
}

HRESULT DbSheetValueSerialiser::_SerialiseNumber(WebName fieldName, const_token_ptr pToken)
{
    switch (GetExecTokenMajorType(pToken))
    {
        case alg::ETP_VDBL:
        {
            alg::const_vdbl_token_assist dta(pToken);
            AddKey(m_acpt, fieldName);
            m_acpt->addFloat64(nullptr, dta.get_value());
            return S_OK;
        }
        case alg::ETP_VINT:
        {
            alg::const_vint_token_assist ita(pToken);
            AddKey(m_acpt, fieldName);
            m_acpt->addInt32(nullptr, ita.get_value());
            return S_OK;
        }
        default:
            return E_FAIL;
    }
}

HRESULT DbSheetValueSerialiser::_SerialiseBool(WebName fieldName, const_token_ptr pToken)
{
    switch (GetExecTokenMajorType(pToken))
    {
        case alg::ETP_VDBL:
        {
            alg::const_vdbl_token_assist dta(pToken);
            AddKey(m_acpt, fieldName);
            m_acpt->addBool(nullptr, dta.get_smp_type().Eq(1.0));
            return S_OK;
        }
        case alg::ETP_VINT:
        {
            alg::const_vint_token_assist ita(pToken);
            AddKey(m_acpt, fieldName);
            m_acpt->addBool(nullptr, ita.get_smp_type().Eq(1));
            return S_OK;
        }
        case alg::ETP_VBOOL:
        {
            alg::const_vbool_token_assist bta(pToken);
            AddKey(m_acpt, fieldName);
            m_acpt->addBool(nullptr, bta.get_value());
            return S_OK;
        }
        default:
            return E_FAIL;
    }
}

HRESULT DbSheetValueSerialiser::serialiseMultipleSelect(WebName fieldName, const_token_ptr pToken, IDbField* pField)
{
    if (nullptr == pToken)
    {
        ASSERT(FALSE);
        return S_FALSE;
    }
    if (!alg::const_handle_token_assist::is_type(pToken))
    {
        switch (alg::GetExecTokenMajorType(pToken))
        {
        case alg::ETP_VDBL: // 引用字段, 新创建的公式
        {
            alg::const_vdbl_token_assist cvta(pToken);
            ASSERT(cvta.get_value() == 0.0);
            return S_FALSE;
        }
        case alg::ETP_VSTR: // 引用字段, 尚未完成计算, 残留了老数据
            return S_FALSE;
        default:
            ASSERT(FALSE);
            return S_FALSE;
        }
    }

    AddKey(m_acpt, fieldName);
    serialize_multipleSelection(pToken, this->m_acpt);
    return S_OK;
}

static void serializeAttachment(ISerialAcceptor* acpt, const_token_ptr pToken, IEncodeDecoder* ed)
{
    alg::const_handle_token_assist assist {pToken};
    ASSERT(assist);
    const auto serializer {[acpt, ed, &assist](const_token_ptr pToken) -> void {
        const IDbAttachmentHandle* const pDbAttachmentHandle = alg::const_handle_token_assist {pToken}.get_handle()->CastAttachment();
        ASSERT(pDbAttachmentHandle);
        if (0 == xstrcmp(__X(""), pDbAttachmentHandle->GetName())) // 只序列化存储了文件名的单元格
            return;
        wo::sa::Leave cellStruct(wo::sa::enterStruct(acpt, nullptr));
        acpt->addString("uploadId", pDbAttachmentHandle->GetFileId());
        PCWSTR sourceStr = nullptr;
        VS(ed->EncodeDbAttachmentSource(pDbAttachmentHandle->GetSource(), &sourceStr));
        acpt->addString("source", sourceStr);
        acpt->addString("type", pDbAttachmentHandle->GetContentType());
        acpt->addString("fileName", pDbAttachmentHandle->GetName());
        acpt->addUint32("size", pDbAttachmentHandle->GetSize());
 
        PCWSTR linkUrl = pDbAttachmentHandle->GetLinkUrl();
        if (linkUrl && *linkUrl != __Xc('\0'))
            acpt->addString("linkUrl", linkUrl);
 
        PCWSTR imgSize = pDbAttachmentHandle->GetImgSize();
        if (imgSize && *imgSize != __Xc('\0'))
            acpt->addString("imgSize", imgSize);
    }};
    if (assist.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
    {
        const IDbTokenArrayHandle* const handle = assist.get_handle()->CastArray();
        const UINT count = handle->GetCount();
        wo::sa::Leave leave = wo::sa::enterArray(acpt, nullptr);
        for (UINT i = 0; i < count; ++i)
        {
            const_token_ptr token {};
            handle->Item(i, &token);
            if (token && alg::const_handle_token_assist {token}.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
                serializeAttachment(acpt, token, ed);
            else
                serializer(token);
        }
    }
    else
    {
        wo::sa::Leave leave = wo::sa::enterArray(acpt, nullptr);
        serializer(pToken);
    }
}

HRESULT DbSheetValueSerialiser::serialiseCellPicture(WebName fieldName, const_token_ptr pToken, IDbField* pField)
{
    ks_bstr text;
    HRESULT hr = pField->GetDbSheetData()->GetValueString(pToken, pField->GetID(), &text);
    if (FAILED(hr))
        return hr;
    if (text.empty())
        return S_FALSE;

    ASSERT(!m_pCellImages);
    CellImg_Param param;
    hr = m_pCellImages->GetCellImgParamFromCellValue(text, &param);
    if (FAILED(hr))
        return S_FALSE;

    IKShape* pShape = m_pCellImages->GetImgByName(param.getNameStr());
    ks_castptr<drawing::AbstractShape> spShape = pShape;
    if (!spShape)
        return S_FALSE;

    IKBlipAtom* spBlipAtom = spShape->picID();
    if (!spBlipAtom)
        return S_FALSE;

    ks_bstr strUrl;
    spBlipAtom->GetLinkPath(&strUrl);
    if (strUrl.empty())
        return S_FALSE;

    AddKey(m_acpt, fieldName);
    m_acpt->beginArray();
        m_acpt->beginStruct();
            m_acpt->addString("url", strUrl.c_str());
        m_acpt->endStruct();
    m_acpt->endArray();
    return S_OK;
}
 
HRESULT DbSheetValueSerialiser::_SerialiseAttachment(WebName fieldName, const_token_ptr pToken)
{
    if (!alg::const_handle_token_assist::is_type(pToken))
        return E_FAIL;
    alg::const_handle_token_assist chta(pToken);
    if (alg::ET_HANDLE_TOKENARRAY != chta.get_handleType())
        return E_FAIL;
    AddKey(this->m_acpt, fieldName);
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (nullptr == handle)
        return S_FALSE;
    ks_stdptr<IDbTokenArrayHandle> spTokenArray = handle->CastUnknown();
    UINT cnt = spTokenArray->GetCount();
    if (0 == cnt)
    {
        this->m_acpt->beginArray();
        this->m_acpt->endArray();
        return S_OK;
    }
 
    serializeAttachment(m_acpt, pToken, m_pEncodeDecoder);
    return S_OK;
}

HRESULT DbSheetValueSerialiser::_SerialiseAddress(WebName fieldName, const_token_ptr pToken)
{
    if (!alg::const_handle_token_assist::is_type(pToken))
        return E_FAIL;
    alg::const_handle_token_assist chta(pToken);
    if (alg::ET_HANDLE_DBADDRESS != chta.get_handleType())
        return E_FAIL;
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (nullptr == handle)
        return S_FALSE;
    ks_stdptr<IDbCascadeHandle> spTokenAddress = handle->CastUnknown();
    AddKey(m_acpt, fieldName);
    spTokenAddress->SerialContent(m_acpt, false);
    return S_OK;
}

HRESULT DbSheetValueSerialiser::_SerialiseDepartment(WebName fieldName, const_token_ptr pToken)
{
    if (!alg::const_handle_token_assist::is_type(pToken))
        return E_FAIL;
    alg::const_handle_token_assist chta(pToken);
    if (alg::ET_HANDLE_TOKENARRAY != chta.get_handleType())
        return E_FAIL;
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (nullptr == handle)
        return S_FALSE;

    AddKey(m_acpt, fieldName);
    ks_stdptr<IDbTokenArrayHandle> spArray = handle->CastUnknown();
    wo::sa::Leave leave = wo::sa::enterArray(m_acpt, nullptr);
    for (UINT i = 0, cnt = spArray->GetCount(); i < cnt; ++i)
    {
        const_token_ptr pItem = nullptr;
        VS(spArray->Item(i, &pItem));

        if (!alg::const_handle_token_assist::is_type(pItem))
            return E_FAIL;
        alg::const_handle_token_assist chta(pItem);
        if (alg::ET_HANDLE_DBADDRESS != chta.get_handleType())
            return E_FAIL;
        alg::TOKEN_HANDLE handle = chta.get_handle();
        if (nullptr == handle)
            return S_FALSE;
        ks_stdptr<IDbCascadeHandle> spTokenAddress = handle->CastUnknown();
        spTokenAddress->SerialContent(m_acpt, false);
    }
    return S_OK;
}

 
HRESULT DbSheetValueSerialiser::_SerialiseNote(WebName fieldName, const_token_ptr pToken)
{
    if (!alg::const_handle_token_assist::is_type(pToken))
        return E_FAIL;
    alg::const_handle_token_assist chta(pToken);
    if (alg::ET_HANDLE_DBNOTE != chta.get_handleType())
        return E_FAIL;
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (nullptr == handle)
        return S_FALSE;
    ks_stdptr<IDbNoteHandle> spTokenNote = handle->CastUnknown();

    if (0 == xstrcmp(__X(""), spTokenNote->GetSummary()))
        return S_FALSE;
    AddKey(m_acpt, fieldName);
    m_acpt->beginStruct();
    m_acpt->addString("fileId", spTokenNote->GetFileId());
    m_acpt->addString("summary", spTokenNote->GetSummary());
    QDateTime modifyDate = QDateTime::fromMSecsSinceEpoch(spTokenNote->GetModifyDate());
    QString format("yyyy/MM/dd hh:mm:ss");
    QString modifyDateStr = modifyDate.toStringEx(format);
    m_acpt->addString("modifyDate", krt::utf16(modifyDateStr));
    m_acpt->endStruct();
    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseLink(WebName fieldName, const_token_ptr pToken)
{
    if (!alg::const_handle_token_assist::is_type(pToken))
        return E_FAIL;
    alg::const_handle_token_assist chta(pToken);
    if (alg::ET_HANDLE_DBLINK != chta.get_handleType())
        return E_FAIL;
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (nullptr == handle)
        return S_FALSE;
    ks_stdptr<IDbLinkHandle> spTokenLink = handle->CastUnknown();

    if (0 == spTokenLink->GetCount())
        return S_FALSE;
    AddKey(m_acpt, fieldName);
    m_acpt->beginArray();
    for (UINT i = 0, c = spTokenLink->GetCount(); i < c; ++i)
    {
        EtDbId id = spTokenLink->GetItemId(i);
        EtDbIdStr buf;
        VS(m_pDbCtx->EncodeEtDbId(id, &buf));
        switch (m_linkValue)
        {
        case LinkValueType::ID:
            m_acpt->addString(nullptr, buf);
            break;
        case LinkValueType::All:
            m_acpt->beginStruct();
                m_acpt->addString("id", buf);
                m_acpt->addString("text", spTokenLink->GetItemString(i));
            m_acpt->endStruct();
            break;
        default:        // this situation is unreasonable
            ASSERT(false);
            break;
        }
    }
    m_acpt->endArray();
    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseParentRecord(WebName fieldName, const_token_ptr pToken)
{
    if (!alg::const_handle_token_assist::is_type(pToken))
        return E_FAIL;
    alg::const_handle_token_assist chta(pToken);
    if (alg::ET_HANDLE_DBLINK != chta.get_handleType())
        return E_FAIL;
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (nullptr == handle)
        return S_FALSE;
    ks_stdptr<IDbLinkHandle> spTokenLink = handle->CastUnknown();
    if (spTokenLink->GetCount() > 1)
        return E_FAIL;
    AddKey(m_acpt, fieldName);

    EtDbId id = spTokenLink->GetItemId(0);
    EtDbIdStr buf;
    m_pDbCtx->EncodeEtDbId(id, &buf);
    m_acpt->beginStruct();
    m_acpt->addString("id", buf);
    m_acpt->addString("text", spTokenLink->GetItemString(0));
    m_acpt->endStruct();

    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseLookup(WebName fieldName, const_token_ptr pToken, IDbField* pField)
{
    if (pField->IsSyncLookupField())
        return this->serialiseOriginLookup(fieldName, pToken, pField);
    ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
    const ET_DbSheet_Lookup_Function aggregation = spField_Lookup->GetLookupFunction();

    if (aggregation != DbSheet_Lookup_Function_Origin && aggregation != DbSheet_Lookup_Function_Unique)
    {
        switch (alg::GetExecTokenMajorType(pToken))
        {
        case alg::ETP_VSTR:
            return serialiseValueStr(fieldName, pToken, pField);
        case alg::ETP_VDBL:
        case alg::ETP_VINT:
            return _SerialiseNumber(fieldName, pToken);
        default:
            ASSERT(FALSE);
            return S_FALSE;
        }
    }

    if (!alg::const_handle_token_assist::is_type(pToken))
    {
        //引用可能会失效
        if (m_bServerApiSyncSheetMode)
            wo::sa::Leave leave(wo::sa::enterArray(this->m_acpt, fieldName, true));
        return S_OK;
    }

    // 在引用的实现中, 如果遇见空单元格, 则空单元格不会被引用. 这个设计导致无法简单地通过关联字段将引用token array中各项溯源
    // 因此依赖 recordId 的字段 (超链接) 无法取得预期的效果. 暂且屏蔽
    {
        if (spField_Lookup->IsValid() == FALSE)
        {
            if (m_bServerApiSyncSheetMode)
                wo::sa::Leave leave(wo::sa::enterArray(this->m_acpt, fieldName, true));
            return S_FALSE;
        }
    }

    if (aggregation == DbSheet_Lookup_Function_Origin)
        return serialiseOriginLookup(fieldName, pToken, pField);
    ASSERT(aggregation == DbSheet_Lookup_Function_Unique);
    return serialiseUniqueLookup(fieldName, pToken, pField);
}

HRESULT DbSheetValueSerialiser::serialize_lookup(const IDbTokenArrayHandle* pTokenArrayHandle, IDbField* pField, WebName fieldName) {
    wo::sa::Leave leave(wo::sa::enterArray(this->m_acpt, fieldName, true)); // 使用变量作为key时, key需要深拷贝
    if (0 == pTokenArrayHandle->GetCount())
        return S_FALSE;
    for (UINT i = 0, c = pTokenArrayHandle->GetCount(); i < c; ++i)
    {
        const_token_ptr item = nullptr;
        VS(pTokenArrayHandle->Item(i, &item));
        bool bSerializeDirectly = true;
        if (alg::const_handle_token_assist::is_type(item))
        {
            alg::const_handle_token_assist assist(item);
            if (assist.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
                bSerializeDirectly = false;
        }

        HRESULT hr = this->serialiseContent(nullptr, INV_EtDbId, pField, item, bSerializeDirectly);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseOriginLookup(WebName fieldName, const_token_ptr pToken, IDbField* pField)
{
    if (pField->IsSyncLookupField())
    {
        alg::const_handle_token_assist assist {pToken};
        if (assist)     // 避免引用失效
            return this->serialize_lookup(assist.get_handle()->CastArray(), pField, fieldName);
        return S_OK;        // 引用失效序列化为空, 直接 return
    }
    ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
    const ET_DbSheet_Lookup_Function aggregation = spField_Lookup->GetLookupFunction();
    ASSERT(aggregation == DbSheet_Lookup_Function_Origin);

    // 取每一个子token, 对子token递归调用 serialiseLookup
    // 递归调用需要的除了token之外, 还有源token的所在字段 (例如多级引用场景)
    ks_stdptr<IDbField_Link> spLinkField_Link;
    UINT linkSheetId = INVALIDIDX;
    HRESULT hr = spField_Lookup->GetLinkField(&spLinkField_Link);
    if (FAILED(hr))
    {
        if (!spLinkField_Link && 
            spField_Lookup->GetLookupType() > LT_NORMAL && 
            spField_Lookup->GetLookupSheetId() > 0)
        {
            linkSheetId = spField_Lookup->GetLookupSheetId();
        }
        else
        {
            return S_FALSE;
        }
    }
    else
    {
        linkSheetId = spLinkField_Link->GetLinkSheet();
    }
    IDX sheetIdx = INVALIDIDX;
    m_pBook->STSheetToRTSheet(linkSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return S_FALSE;
    ks_stdptr<ISheet> spSheet;
    m_pBook->GetSheet(sheetIdx, &spSheet);
    if (nullptr == spSheet)
        return S_FALSE;
    ks_stdptr<IDBSheetOp> spLinkSheetOp;
	VS(DbSheet::GetDBSheetOp(spSheet.get(), &spLinkSheetOp));
    ks_stdptr<IDbField> spLookupField;
    hr = spLinkSheetOp->GetFieldsManager()->GetField(spField_Lookup->GetLookupFieldId(), &spLookupField);
    if (FAILED(hr))
        return S_FALSE;

    ASSERT(alg::const_handle_token_assist::is_type(pToken));
    alg::const_handle_token_assist chtaLookup(pToken);
    ASSERT(chtaLookup.get_handleType() == alg::ET_HANDLE_TOKENARRAY);
    ks_stdptr<IDbTokenArrayHandle> spTokenArray = chtaLookup.get_handle()->CastUnknown();

    return this->serialize_lookup(spTokenArray.get(), spLookupField.get(), fieldName);
}

HRESULT DbSheetValueSerialiser::serialiseUniqueLookup(WebName fieldName, const_token_ptr pToken, IDbField* pField)
{
    ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
    const ET_DbSheet_Lookup_Function aggregation = spField_Lookup->GetLookupFunction();
    ASSERT(aggregation == DbSheet_Lookup_Function_Unique);

    // "去重"会将多级引用全部展开, 因此单元格内容必定是包含非引用单元格的1层tokenArray
    ks_stdptr<IDbField> spLookupBaseField;
    HRESULT hr = spField_Lookup->GetLookupBaseField(&spLookupBaseField);
    if (FAILED(hr) || nullptr == spLookupBaseField)
        return S_FALSE;
    ET_DbSheet_FieldType srcType = spLookupBaseField->GetType();

    if (!alg::const_handle_token_assist::is_type(pToken))
    {
        ASSERT(FALSE);
        return S_FALSE;
    }
    alg::const_handle_token_assist chta(pToken);
    if (chta.get_handleType() != alg::ET_HANDLE_TOKENARRAY)
    {
        ASSERT(FALSE);
        return S_FALSE;
    }
    wo::sa::Leave leave(wo::sa::enterArray(m_acpt, fieldName, true)); // 使用变量作为key时, key需要深拷贝
    ks_stdptr<IDbTokenArrayHandle> spTokenArray = chta.get_handle()->CastUnknown();
    if (0 == spTokenArray->GetCount())
        return S_FALSE;

    // 由于结构的缘故, 将tokenArray展开后得到的各项可能不是一个单元格内的完整内容,
    // 譬如附件/联系人, 在单元格内它们是由 tokenArray 包裹的, 但此处展开引用array得到的是裸token
    // 因此对于使用 tokenArray 包裹的场景, 直接将当前token array视作引用源字段的单元格来序列化,
    // 其他场景则展开 array
    switch (srcType)
    {
        case Et_DbSheetField_Contact:
        case Et_DbSheetField_Attachment:
            return serialiseContent(nullptr, INV_EtDbId, spLookupBaseField.get(), pToken);
        case Et_DbSheetField_CreatedBy:
        case Et_DbSheetField_LastModifiedBy:
        {
            if (spTokenArray->GetType() == DBTAT_Mix)
            {
                const_token_ptr item = nullptr;
                VS(spTokenArray->Item(0, &item));
                alg::const_handle_token_assist chtaItem(item);
                alg::TOKEN_HANDLE handle = chtaItem.get_handle();
                if (nullptr == handle || chtaItem.get_handleType() != alg::ET_HANDLE_CONTACT)
                    return S_FALSE;
                ks_stdptr<IDbContactHandle> spContactHandle = handle->CastUnknown();
                return serializeContactItem(spContactHandle->GetId());
            }
            else
            {
                return serialiseContent(nullptr, INV_EtDbId, spLookupBaseField.get(), pToken);
            }
        }
    }

    Et_DbSheet_Field_Value_Type valueType = spLookupBaseField->GetValueType();
    for (UINT i = 0, c = spTokenArray->GetCount(); i < c; ++i)
    {
        const_token_ptr item = nullptr;
        VS(spTokenArray->Item(i, &item));
        if (srcType != Et_DbSheetField_Lookup)
        {
            hr = serialiseContent(nullptr, INV_EtDbId, spLookupBaseField.get(), item);
        }
        else
        {
            switch (valueType)
            {
                case DbSheet_Fvt_Text:
                    hr = serialiseValueStr(nullptr, item, spLookupBaseField.get());
                    break;
                case DbSheet_Fvt_Number:
                    hr = _SerialiseNumber(nullptr, item);
                    break;
                case DbSheet_Fvt_Date:
                    if (DbSheet::ContainTimeFormat(pField->GetNumberFormat()))
                        hr = _SerialiseDateTime(nullptr, item);
                    else
                        hr = _SerialiseDate(nullptr, item);
                    break;
                case DbSheet_Fvt_Time:
                    hr = _SerialiseTime(nullptr, item);
                    break;
                case DbSheet_Fvt_Logic:
                    hr = serialiseFormulaNumeric(nullptr, item, {alg::ETP_VDBL, alg::ETP_VINT, alg::ETP_VBOOL}, 
                        std::bind(&DbSheetValueSerialiser::_SerialiseBool, this, std::placeholders::_1, std::placeholders::_2));
                    break;
            }
        }
        if (FAILED(hr))
            return hr;
    }

    return S_OK;
}

//序列化单个联系人的结构单项
HRESULT DbSheetValueSerialiser::serializeContactItem(PCWSTR id)
{
    ks_stdptr<IDbUsersManager> spUsersMgr;
    m_pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);
    PCWSTR nickname = spUsersMgr->GetNickname(id);
    PCWSTR avatar = spUsersMgr->GetAvatar(id);
    PCWSTR companyId = spUsersMgr->GetCompanyId(id);
    m_acpt->beginStruct();
        m_acpt->addString("id", id);
        if (spUsersMgr->IsExist(id))
        {
            m_acpt->addString("nickName", nickname);
            m_acpt->addString("avatar", avatar);
            if (xstrlen(companyId) > 0)
                m_acpt->addString("companyId", companyId);
        }
    m_acpt->endStruct();
    return S_OK;
}

template <typename Getter>
HRESULT DbSheetValueSerialiser::serializeContactArray(const_token_ptr pToken, Getter getter)
{
    sa::Leave leave = sa::enterArray(m_acpt, nullptr);
    if (!alg::const_handle_token_assist::is_type(pToken))
        return S_OK;
    if (alg::const_handle_token_assist {pToken}.get_handleType() != alg::ET_HANDLE_TOKENARRAY)
        return S_OK;
    alg::const_handle_token_assist assist {pToken};
    const auto handle {assist.get_handle()->CastArray()};
    const UINT count {handle->GetCount()};
    if (count > 0)
    {
        const auto first {getter(handle, 0)};
        if (first && alg::const_handle_token_assist {first}.get_handleType() == alg::ET_HANDLE_CONTACT)
        {
            for (UINT i = 0; i < count; ++i)
            {
                const auto token {getter(handle, i)};
                ASSERT(alg::const_handle_token_assist {token}.get_handleType() == alg::ET_HANDLE_CONTACT);
                const auto contactHandle {alg::const_handle_token_assist {token}.get_handle()->CastContact()};
                this->serializeContactItem(contactHandle->GetId());
            }
        }
        else
        {
            for (UINT i = 0; i < count; ++i)
            {
                const auto token {getter(handle, i)};
                ASSERT(alg::const_handle_token_assist {token}.get_handleType() == alg::ET_HANDLE_TOKENARRAY);
                this->serializeContactArray(token, std::add_lvalue_reference_t<Getter>(getter));
            }
        }
    }
    return S_OK;
}

//总入口
HRESULT DbSheetValueSerialiser::serializeContactToken(WebName fieldName, const_token_ptr pToken, IDbField* pField)
{
    IDBSheetCtx* pCtx = _appcore_GainDbSheetContext();
    AddKey(m_acpt, fieldName);

    ks_stdptr<IDbTokenArrayHandle> spHandleArray;
    HRESULT hr = pCtx->GetContactTokenArray(pToken, &spHandleArray);
    const auto supportMulttipleGetter {[](const IDbTokenArrayHandle* handle, UINT i) -> const_token_ptr {
        const_token_ptr token {};
        handle->Item(i, &token);
        return token;
    }};
    const auto unsupportMulttipleGetter {[](const IDbTokenArrayHandle* handle, UINT i) -> const_token_ptr {
        const_token_ptr token {};
        handle->Item(i, &token);
        ASSERT(alg::const_handle_token_assist::is_type(token));
        alg::const_handle_token_assist assist {token};
        const auto subhandle {assist.get_handle()->CastArray()};
        if (subhandle && subhandle->GetCount() > 0)
        {
            const_token_ptr subtoken {};
            subhandle->Item(0, &subtoken);
            ASSERT(alg::const_handle_token_assist::is_type(subtoken));
            if (alg::const_handle_token_assist {subtoken}.get_handleType() not_eq alg::ET_HANDLE_TOKENARRAY)
                return subtoken;
        }
        else
        {
            return {};
        }
        return token;
    }};

    BOOL bSupportMulti = FALSE;
    if(pField->GetType() == Et_DbSheetField_Contact)
    {
        ks_stdptr<IDbField_Contact> spContactField = pField;
        bSupportMulti = spContactField->GetSupportMulti();
    }

    if (m_bServerApiSyncSheetMode) //db同步表serverapi同步方式查询记录时，联系人都按照数组形式序列化
        return this->serializeContactArray(pToken, supportMulttipleGetter);   

    if (bSupportMulti == FALSE)
    {
        if (pField->IsSyncLookupField() == FALSE)
        {      // 联系人关闭多值且非引用
            ks_stdptr<IDbContactHandle> spContactHandle;
            VS(pCtx->GetContactTokenItem(spHandleArray.get(), 0, &spContactHandle));
            return this->serializeContactItem(spContactHandle->GetId());
        }
        else
        {     // 联系人关闭多值且引用
            return this->serializeContactArray(pToken, unsupportMulttipleGetter);
        }
    }
    return this->serializeContactArray(pToken, supportMulttipleGetter);
}

HRESULT DbSheetValueSerialiser::serialiseFormulaNumeric(WebName fieldName, const_token_ptr pToken, 
        const std::vector<DWORD>& tokenTypes, std::function<HRESULT(WebName, const_token_ptr)> tokenSerializer)
{
    if (!alg::const_handle_token_assist::is_type(pToken))
        return tokenSerializer(fieldName, pToken);

    alg::const_handle_token_assist chta(pToken);
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (handle == nullptr)
        return serialiseText(fieldName, __X(""));
    if (chta.get_handleType() != alg::ET_HANDLE_TOKENARRAY)
        return serialiseText(fieldName, __X(""));
    ks_stdptr<IDbTokenArrayHandle> spHandleArray = handle->CastUnknown();

    auto desiredToken = [&](const DWORD type) -> bool {
        for (const DWORD tokenType: tokenTypes)
            if (type == tokenType)
                return true;
        return false;
    };

    std::vector<const_token_ptr> tokens;
    std::function<bool(IDbTokenArrayHandle* pArray)> proc = [&](IDbTokenArrayHandle* pArray)->bool {
        for (UINT i = 0, cnt = pArray->GetCount(); i < cnt; ++i)
        {
            const_token_ptr pItem = nullptr;
            VS(pArray->Item(i, &pItem));
            DWORD type = GetExecTokenMajorType(pItem);
            if (desiredToken(type))
            {
                tokens.push_back(pItem);
                break;
            }
            switch (GetExecTokenMajorType(pItem))
            {
                case alg::ETP_HANDLE:
                {
                    alg::const_handle_token_assist chta(pItem);
                    alg::TOKEN_HANDLE handle = chta.get_handle();
                    if (handle == nullptr)
                        return false;
                    if (chta.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
                    {
                        ks_stdptr<IDbTokenArrayHandle> spArray = handle->CastUnknown();
                        bool b = proc(spArray.get());
                        if (!b)
                            return b;
                    }
                    else
                    {
                        return false;
                    }
                }
                default:
                    return false;
            }
        }
        return true;
    };
    bool b = proc(spHandleArray.get());
    if (!b)
        return serialiseText(fieldName, __X(""));

    AddKey(m_acpt, fieldName);
    m_acpt->beginArray();
    for (UINT i = 0, cnt = tokens.size(); i < cnt; ++i)
    {
        tokenSerializer(nullptr, tokens[i]);
    }
    m_acpt->endArray();
    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseFormulaFormattedText(WebName fieldName, const_token_ptr pToken, 
        PCWSTR format, std::function<HRESULT(WebName, const_token_ptr)> tokenSerializer)
{
    if (!alg::const_handle_token_assist::is_type(pToken))
        return tokenSerializer(fieldName, pToken);

    alg::const_handle_token_assist chta(pToken);
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (handle == nullptr)
        return serialiseText(fieldName, __X(""));
    if (chta.get_handleType() != alg::ET_HANDLE_TOKENARRAY)
        return serialiseText(fieldName, __X(""));
    ks_stdptr<IDbTokenArrayHandle> spHandleArray = handle->CastUnknown();

    std::vector<ks_wstring> texts;
    std::function<bool(IDbTokenArrayHandle* pArray)> proc = [&](IDbTokenArrayHandle* pArray)->bool {
        for (UINT i = 0, cnt = pArray->GetCount(); i < cnt; ++i)
        {
            const_token_ptr pItem = nullptr;
            VS(pArray->Item(i, &pItem));
            switch (GetExecTokenMajorType(pItem))
            {
                case alg::ETP_HANDLE:
                {
                    alg::const_handle_token_assist chta(pItem);
                    alg::TOKEN_HANDLE handle = chta.get_handle();
                    if (handle == nullptr)
                        return false;
                    if (chta.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
                    {
                        ks_stdptr<IDbTokenArrayHandle> spArray = handle->CastUnknown();
                        bool b = proc(spArray.get());
                        if (!b)
                            return b;
                    }
                    else
                    {
                        return false;
                    }
                }
                default:
                {
                    kfc::nf::NFHANDLE hNf = m_pBook->GetNumFmtHandle(format);
                    if (hNf == nullptr)
                        return false;
                    ks_bstr text;
                    HRESULT hr = m_spStringTools->GetCellText(pItem, hNf, &text, NULL);
                    if (FAILED(hr))
                        return false;

                    texts.push_back(text.c_str());
                }
            }
        }
        return true;
    };
    bool b = proc(spHandleArray.get());
    if (!b)
        return serialiseText(fieldName, __X(""));

    AddKey(m_acpt, fieldName);
    m_acpt->beginArray();
    for (UINT i = 0, cnt = texts.size(); i < cnt; ++i)
    {
        serialiseText(nullptr, texts[i].c_str());
    }
    m_acpt->endArray();
    return S_OK;
}

HRESULT DbSheetValueSerialiser::serialiseContactTypeFormula(WebName fieldName, const_token_ptr pToken, IDbField* pField)
{
    // 非预期的单元格内容不予序列化
    if (!alg::const_handle_token_assist::is_type(pToken))
        return S_FALSE;

    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    ks_stdptr<IDbTokenArrayHandle> spHandleArray;
    HRESULT hr = pCtx->GetContactTokenArray(pToken, &spHandleArray);
    if (FAILED(hr))
    {
        AddKey(m_acpt, fieldName);
        m_acpt->beginArray();
        m_acpt->endArray();
        return hr;
    }
    if (0 == spHandleArray->GetCount())
    {
        AddKey(m_acpt, fieldName);
        m_acpt->beginArray();
        m_acpt->endArray();
        return S_FALSE;
    }

    const KDBTokenArrayType arrayType = spHandleArray->GetType();
    if (DBTAT_Contact == arrayType)
        return serializeContactToken(fieldName, pToken, pField);

    std::vector<IDbContactHandle*> contactHandleVec;
    std::function<bool(IDbTokenArrayHandle* pArray)> proc = [&](IDbTokenArrayHandle* pArray)->bool {
        for (UINT i = 0, cnt = pArray->GetCount(); i < cnt; ++i)
        {
            const_token_ptr pItem = nullptr;
            VS(pArray->Item(i, &pItem));
            if (!alg::const_handle_token_assist::is_type(pItem))
                return false;
            alg::const_handle_token_assist chta(pItem);
            alg::TOKEN_HANDLE handle = chta.get_handle();
            if (handle == nullptr)
                return false;
            if (chta.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
            {
                ks_stdptr<IDbTokenArrayHandle> spArray = handle->CastUnknown();
                bool b = proc(spArray.get());
                if (!b)
                    return b;
            }
            else if (chta.get_handleType() == alg::ET_HANDLE_CONTACT)
            {
                ks_stdptr<IDbContactHandle> spContactHandle;
                VS(pCtx->GetContactTokenItem(pArray, i, &spContactHandle));
                contactHandleVec.push_back(spContactHandle.get());
            }
            else
            {
                return false;
            }
        }
        return true;
    };
    bool b = proc(spHandleArray.get());
    if (!b)
        return S_FALSE;

    AddKey(m_acpt, fieldName);
    m_acpt->beginArray();
    for (UINT i = 0, cnt = contactHandleVec.size(); i < cnt; ++i)
    {
        serializeContactItem(contactHandleVec[i]->GetId());
    }
    m_acpt->endArray();
    return S_OK;
}

HRESULT DbSheetValueSerialiser::Set(EtDbId recId, WebName fieldKey, binary_wo::VarObj obj)
{
    HRESULT hr = _SetInner(recId, nullptr, fieldKey, obj);
    if (FAILED(hr) && m_omitFailure)
        hr = S_FALSE;
    return hr;
}

HRESULT DbSheetValueSerialiser::Set(EtDbId recId, IDbField* pField, WebName fieldKey, binary_wo::VarObj obj)
{
    HRESULT hr = _SetInner(recId, pField, fieldKey, obj);
    if (FAILED(hr) && m_omitFailure)
        hr = S_FALSE;
    return hr;
}

HRESULT DbSheetValueSerialiser::SetDBSyncAutoValue(EtDbId recId, IDbField* pField, WebName fieldKey, const binary_wo::VarObj& obj)
{
    if (pField == nullptr)
        return E_DBSHEET_FIELD_NOT_FOUND;

    binary_wo::VarObj objValue = obj.get_s(fieldKey);
    switch (m_textValue)
    {
    case TVT_FormulaCompound:
    {
        if (objValue.has("data"))
            objValue = objValue.get_s("data");
        else if(objValue.has("value"))
            objValue = objValue.get_s("value");
        else if (objValue.has("text"))
            objValue = objValue.get_s("text");
        break;
    }
    default:
        break;
    }

    if (!pField->IsSyncField())
        return E_FAIL;

    ET_DbSheet_FieldType type = pField->GetType();
    EtDbId fldId = pField->GetID();
    switch (type)
    {
    case Et_DbSheetField_AutoNumber:
    {
        constexpr TokenGenerator<Et_DbSheetField_Number> generate;
        switch (objValue.type())
        {
            case binary_wo::typeFloat32:
            case binary_wo::typeFloat64:
            case binary_wo::typeUint32:
            case binary_wo::typeUint16:
            case binary_wo::typeUint8:
            case binary_wo::typeInt32:
            case binary_wo::typeInt16:
            case binary_wo::typeInt8:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
            case binary_wo::typeArray:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
            default:
                ASSERT(FALSE);
                return E_INVALID_REQUEST;
        }
    }
    case Et_DbSheetField_CreatedBy:
    case Et_DbSheetField_LastModifiedBy:
    {
        const TokenGenerator<Et_DbSheetField_Contact> generate(m_pBook, pField);
        switch (objValue.type())
        {
            case binary_wo::typeStruct:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
            case binary_wo::typeArray:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
            default:
                ASSERT(FALSE);
                return E_INVALID_REQUEST;
        }
    }
    case Et_DbSheetField_CreatedTime:
    case Et_DbSheetField_LastModifiedTime:
    {
        const TokenGenerator<Et_DbSheetField_Date> generate(m_pBook, pField);
        switch (objValue.type())
        {
            case binary_wo::typeString:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
            case binary_wo::typeArray:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
            default:
                ASSERT(FALSE);
                return E_INVALID_REQUEST;
        }
    }
    case Et_DbSheetField_Formula:
    case Et_DbSheetField_FormulaResult:
    {
        constexpr TokenGenerator<Et_DbSheetField_FormulaResult> generate;
        return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
    }
    default:
        ASSERT(FALSE);
    }

    return S_OK;
}

HRESULT DbSheetValueSerialiser::ClearUniqueField(EtDbId recId, WebName fieldKey)
{
    ks_stdptr<IDbField> spField;
    m_fieldRetriever.GetField(krt::utf16(krt::fromUtf8(fieldKey)), &spField);
    if (spField == nullptr)
        return E_DBSHEET_FIELD_NOT_FOUND;

    if (spField->IsValueUnique() == false)
        return S_OK;

    if (spField->IsAuto())
        return E_DBSHEET_ALTER_AUTO_FIELD;

    EtDbId fldId = spField->GetID();

    ks_stdptr<IDBSheetRange> spDBSheetRange;
    m_spDbSheetOp->CreateDBSheetRange(&spDBSheetRange);
    spDBSheetRange->AddRecordId(recId);
    spDBSheetRange->AddFieldId(fldId);
    return m_spDbSheetOp->Clear(spDBSheetRange);
}

HRESULT DbSheetValueSerialiser::_SetInner(EtDbId recId, IDbField* pField, WebName fieldKey, binary_wo::VarObj obj)
{
    ASSERT(obj.has(fieldKey));

    ks_stdptr<IDbField> spField;
    if (pField)
        spField = pField;
    else 
        m_fieldRetriever.GetField(krt::utf16(krt::fromUtf8(fieldKey)), &spField);
    if (spField == nullptr)
        return E_DBSHEET_FIELD_NOT_FOUND;

    if (!spField->IsSyncLookupField() && spField->IsAuto())
        return E_DBSHEET_ALTER_AUTO_FIELD;

    ET_DbSheet_FieldType type = spField->GetType();
    EtDbId fldId = spField->GetID();

    if (m_bEnableValCacheOpt && spField->IsValueUnique())
        VS(m_fieldRetriever.GainFieldValUniqueGuard(fldId));

    binary_wo::VarObj objValue = obj.get_s(fieldKey);
    switch (m_textValue)
    {
    case TVT_FormulaCompound:
    {
        if (objValue.has("data"))
            objValue = objValue.get_s("data");
        else if(objValue.has("value"))
            objValue = objValue.get_s("value");
        else if (objValue.has("text"))
            objValue = objValue.get_s("text");
        break;
    }
    default:
        break;
    }

	if (objValue.type() == binary_wo::typeString)
	{
		WebStr value = objValue.value_str();
		if (!value || *value == __Xc('\0'))
		{
			ks_stdptr<IDBSheetRange> spDBSheetRange;
			m_spDbSheetOp->CreateDBSheetRange(&spDBSheetRange);
			spDBSheetRange->AddRecordId(recId);
			spDBSheetRange->AddFieldId(fldId);
			return m_spDbSheetOp->Clear(spDBSheetRange);
		}
	}
    if (type == Et_DbSheetField_Lookup)
    {
        Et_DbSheet_Field_Value_Type valueType = spField->GetValueType();
        switch (valueType)
        {
            case DbSheet_Fvt_Number:
                type = Et_DbSheetField_Number;
                break;
            case DbSheet_Fvt_Text:
                type = Et_DbSheetField_MultiLineText;
                break;
        }
    }
    switch (type)
    {
        case Et_DbSheetField_MultiLineText:
        case Et_DbSheetField_SingleLineText:
        case Et_DbSheetField_ID:
        case Et_DbSheetField_Phone:
        case Et_DbSheetField_BarCode:
        {
            constexpr TokenGenerator<Et_DbSheetField_MultiLineText> generate;
            switch (objValue.type())
            {
                case binary_wo::typeString:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }

        case Et_DbSheetField_Date:
        {
            const TokenGenerator<Et_DbSheetField_Date> generate(m_pBook, spField);
            switch (objValue.type())
            {
                case binary_wo::typeString:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }

        case Et_DbSheetField_Time:
        {
            const TokenGenerator<Et_DbSheetField_Time> generate(m_pBook, spField);
            switch (objValue.type())
            {
                case binary_wo::typeString:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }

        case Et_DbSheetField_Address:
        {
            const TokenGenerator<Et_DbSheetField_Address> generate(spField);
            switch (objValue.type())
            {
                case binary_wo::typeStruct:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Cascade:
        {
            constexpr TokenGenerator<Et_DbSheetField_Cascade> generate;
            switch (objValue.type())
            {
                case binary_wo::typeStruct:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Department:
        {
            constexpr TokenGenerator<Et_DbSheetField_Department> generate;
            switch (objValue.type())
            {
                case binary_wo::typeStruct:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    if (objValue.arrayLength_s() > 1  && !spField->IsSyncField())
                    {
                        ks_stdptr<IDbField_Cascade> spFieldDepartment = spField;
                        if (!spFieldDepartment->GetMultiValue())
                           return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue.at_s(0)));
                    }
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Attachment:
        {
            constexpr TokenGenerator<Et_DbSheetField_Attachment> generate;
            switch (objValue.type())
            {
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Email:
        {
            // std::function 是通过 vtbl 做的 type erasure, 有一定性能损耗
            std::function<HRESULT (const binary_wo::VarObj&)> processor {[&](
                    const binary_wo::VarObj& objValue) -> HRESULT
            {
                switch (objValue.type())
                {
                case binary_wo::typeString:
                {
                    WebStr value = objValue.value_str();
                    HRESULT hr = m_spDbSheetOp->SetHyperlinkAddress(recId, fldId, value);
                    if (FAILED(hr))
                        return hr;
                    return m_spDbSheetOp->SetValue(recId, fldId, value);
                }
                case binary_wo::typeStruct:
                {
                    VAR_OBJ_EXPECT_STRING(objValue, "address");
                    WebStr address = objValue.field_str("address");
                    VAR_OBJ_EXPECT_STRING(objValue, "displayText");
                    WebStr displayText = objValue.field_str("displayText");
                    HRESULT hr = m_spDbSheetOp->SetHyperlinkAddress(recId, fldId, address);
                    if (FAILED(hr))
                        return hr;
                    return m_spDbSheetOp->SetValue(recId, fldId, displayText);
                }
                case binary_wo::typeArray:
                {
                    if (spField->IsSyncLookupField())
                    {
                        // 引用字段对应单元格不用生成IKHyperlink对象，会从baseField字段中查找
                        constexpr TokenGenerator<Et_DbSheetField_MultiLineText> generate;
                        return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                    }
                    else
                    {
                        if (objValue.arrayLength_s() > 0)
                            processor(objValue.at_s(0));
                        else
                            return S_OK;
                    }
                }
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
                }
            }};
            return processor(objValue);
        }
        case Et_DbSheetField_Url:
        {
            constexpr TokenGenerator<Et_DbSheetField_Url> generator;
            switch (objValue.type())
            {
            case binary_wo::typeString:
            case binary_wo::typeStruct:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generator(objValue));
            case binary_wo::typeArray:
                if (spField->IsSyncLookupField())
                {
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generator.array(objValue));
                }
                else
                {
                    int cnt = objValue.arrayLength_s();
                    if (cnt == 0)
                        return S_OK;
                    if (cnt > 1)
                        return E_INVALID_REQUEST;
                    binary_wo::VarObj item = objValue.at_s(0);
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generator(item));
                }
            default:
                ASSERT(false);
                break;
            }
        }
        case Et_DbSheetField_SingleSelect:
        {
            const TokenGenerator<Et_DbSheetField_SingleSelect> generate(spField, m_pDbCtx, m_bValuePreferId);
            switch (objValue.type())
            {
                case binary_wo::typeString:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Number:
        case Et_DbSheetField_Currency:
        case Et_DbSheetField_Percentage:
        case Et_DbSheetField_Rating:
        case Et_DbSheetField_Complete:
        {
            constexpr TokenGenerator<Et_DbSheetField_Number> generate;
            switch (objValue.type())
            {
                case binary_wo::typeString:
                    return m_spDbSheetOp->SetValue(recId, fldId, objValue.value_str());
                case binary_wo::typeFloat32:
                case binary_wo::typeFloat64:
                case binary_wo::typeUint32:
                case binary_wo::typeUint16:
                case binary_wo::typeUint8:
                case binary_wo::typeInt32:
                case binary_wo::typeInt16:
                case binary_wo::typeInt8:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Checkbox:
        {
            constexpr TokenGenerator<Et_DbSheetField_Checkbox> generate;
            switch (objValue.type())
            {
            case binary_wo::typeBool:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
            case binary_wo::typeArray:
                return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
            default:
                ASSERT(FALSE);
                return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_MultipleSelect:
        {
            const TokenGenerator<Et_DbSheetField_MultipleSelect> generate(spField, m_pDbCtx, m_bValuePreferId);
            switch (objValue.type())
            {
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                case binary_wo::typeString:
                    return m_spDbSheetOp->SetValue(recId, fldId, objValue.value_str());
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Contact:
        {
            const TokenGenerator<Et_DbSheetField_Contact> generate(m_pBook, spField);
            switch (objValue.type())
            {
                case binary_wo::typeStruct:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Link:
        case Et_DbSheetField_OneWayLink:
        {
            // 这里没有处理多层数组嵌套的情况, 因为目前不支持关联回落为关联的场景
            switch (objValue.type())
            {
                case binary_wo::typeStruct: // 传入 recordIds[] 数组
                {
                    VAR_OBJ_EXPECT_ARRAY(objValue, "recordIds");

                    ks_stdptr<IDbField_Link> spFieldLink = spField;
                    UINT linkSheet = spFieldLink->GetLinkSheet();
                    
                    VarObj recordIds = objValue.get_s("recordIds");
                    int cnt = recordIds.arrayLength_s();
                    if (cnt > 0 && binary_wo::typeString != recordIds.at_s(0).type())
                        return S_OK;
                    
                    if (nullptr == m_upDbLinkHlp && m_dbLinkParam)
                        m_upDbLinkHlp.reset(new DbSheet::DbLinkHlp(m_pBook, *m_dbLinkParam));

                    std::vector<EtDbId> recIds;
                    recIds.reserve(cnt);
                    
                    for (int i = 0; i < cnt; i++)
                    {
                        EtDbId decodedId = INV_EtDbId;
                        if (FAILED(m_pDbCtx->DecodeEtDbId(recordIds.item_str(i), &decodedId)))
                            continue;
                        recIds.push_back(decodedId);
                    }
                    ks_stdptr<IFormula> spFmla;
                    VS(spFieldLink->GenerateLinkFormula(linkSheet, recIds.data(), recIds.size(), &spFmla));
                    return m_spDbSheetOp->SetFormula(recId, fldId, spFmla.get());
                }
                case binary_wo::typeArray: // 少套一层recordIds, 跟传出形式保持一致
                {
                    ks_stdptr<IDbField_Link> spFieldLink = spField;
                    UINT linkSheet = spFieldLink->GetLinkSheet();

                    int cnt = objValue.arrayLength_s();
                    if (cnt > 0 && binary_wo::typeString != objValue.at_s(0).type())
                        return E_INVALID_REQUEST;

                    if (nullptr == m_upDbLinkHlp && m_dbLinkParam)
                        m_upDbLinkHlp.reset(new DbSheet::DbLinkHlp(m_pBook, *m_dbLinkParam));

                    std::vector<EtDbId> recIds;
                    recIds.reserve(cnt);

                    for (int i = 0; i < cnt; i++)
                    {
                        EtDbId decodedId = INV_EtDbId;
                        if (FAILED(m_pDbCtx->DecodeEtDbId(objValue.item_str(i), &decodedId)))
                            continue;
                        recIds.push_back(decodedId);
                    }
                    ks_stdptr<IFormula> spFmla;
                    VS(spFieldLink->GenerateLinkFormula(linkSheet, recIds.data(), recIds.size(), &spFmla));
                    return m_spDbSheetOp->SetFormula(recId, fldId, spFmla.get());
                }
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_Note:
        {
            constexpr TokenGenerator<Et_DbSheetField_Note> generate;
            switch (objValue.type())
            {
                case binary_wo::typeStruct:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_FormulaResult:
        {
            constexpr TokenGenerator<Et_DbSheetField_FormulaResult> generate;
            switch (objValue.type())
            {
                case binary_wo::typeStruct:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate(objValue));
                case binary_wo::typeArray:
                    return m_spDbSheetOp->SetTokenValue(recId, fldId, generate.array(objValue));
                default:
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
            }
        }
        case Et_DbSheetField_CellPicture:
        {
            return E_NOTIMPL;
        }
        default:
            ASSERT(FALSE);
            return E_INVALID_REQUEST;
    }
}

HRESULT CreateView(binary_wo::VarObj obj, bool bPreferId, IDBSheetOp * pDbSheetOp, IDbFieldsManager* pFieldsManager, IDBSheetViews *pViews, ET_DBSheet_ViewUseType useType, IDBSheetView **ppView, ks_wstring* pErrorMsg)
{
    DbSheetFieldRetriever fieldRetriever;
    fieldRetriever.Reset(pFieldsManager, bPreferId);

    VAR_OBJ_EXPECT_STRING(obj, "name");
    WebStr name = obj.field_str("name");

    VAR_OBJ_EXPECT_STRING(obj, "type")
    ET_DBSheet_ViewType type = et_DBSheetView_Grid;
    DECODE_STR_OBJ(obj, "type", ViewType, type, pErrorMsg);
    
    HRESULT hr = S_OK;
    ks_stdptr<IDbField> spGroupField;
    if (obj.has("groupField"))
    {
        VAR_OBJ_EXPECT_STRING(obj, "groupField");
        hr = fieldRetriever.GetField(obj.field_str("groupField"), &spGroupField);
        if (FAILED(hr))
            return hr;
    }
    if (spGroupField == nullptr && obj.has("groupFieldId"))
    {
        VAR_OBJ_EXPECT_STRING(obj, "groupFieldId");
        EtDbId fieldId = INV_EtDbId;
        VS(_appcore_GainDbSheetContext()->DecodeEtDbId(obj.field_str("groupFieldId"), &fieldId));

        hr = pFieldsManager->GetField(fieldId, &spGroupField);
        if (FAILED(hr))
            return hr;
    }

    ks_stdptr<IDBSheetView> spDbSheetView;
    hr = pViews->CreateView(type, name, false, useType, &spDbSheetView);
    if (FAILED(hr))
        return hr;

    if (spGroupField)
    {
        IDBRecordsOrderManager* pOrderMgr = spDbSheetView->GetMutableOrderManager();
        ks_stdptr<IDBRecordsOrderCondition> spCondition;
        hr = pOrderMgr->AddGroupCondition(spGroupField->GetID(), &spCondition);
        if (FAILED(hr))
            return hr;
    }
    *ppView = spDbSheetView.detach();
    return S_OK;
}

HRESULT CreateField(binary_wo::VarObj obj, bool bPreferId, IDBSheetOp *pDbSheetOp, IDbField **ppField, ks_wstring* pErrorMsg)
{
    DbSheetFieldRetriever fieldRetriever;
    fieldRetriever.Reset(pDbSheetOp->GetFieldsManager(), bPreferId);

    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    VAR_OBJ_EXPECT_STRING(obj, "name")
    WebStr name = obj.field_str("name");
    VAR_OBJ_EXPECT_STRING(obj, "type")
    ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
    DECODE_STR_OBJ(obj, "type", FieldType, type, pErrorMsg);
    const bool isSyncSheet = (pDbSheetOp->IsSyncSheet());

    //对于统计表sheet，不支持增加普通字段，仅支持增加 公式、单向关联(仅限自动关联)、引用 字段
    if(!DbSheet::CheckFieldTypeValidForStatDbSheet(pDbSheetOp->GetBook(), pDbSheetOp->GetSheetId(), type))
    {
        WOLOG_ERROR << "[CreateField] For statSheet, it is not allow to normal field!";
        return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
    }

    HRESULT hr = S_OK;

    ks_stdptr<IWebhookManager> spWebhookMgr;
    pDbSheetOp->GetBook()->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
    KDisableDbWebhook disableDbWebhook(pRange, DbWebhookMask_updateField);
    ks_stdptr<IDbCollector> spDbCollector;
    pDbSheetOp->GetBook()->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
    KDisableDbCollector disableDbCollector(spDbCollector, DCM_Webhook, CDOM_UpdateField);

    ks_stdptr<IDBSheetRange> spDbRange;
    hr = pDbSheetOp->InsertFields(1, &spDbRange);
    if(FAILED(hr))
        return hr;

    IDbFieldsManager *pFieldsMgr = pDbSheetOp->GetFieldsManager();
    EtDbId fieldId = spDbRange->GetFieldId(0);
    ks_stdptr<IDbField> spField;
    hr = pFieldsMgr->GetField(fieldId, &spField);
    if (FAILED(hr))
        return hr;
    hr = spField->SetName(name, TRUE, TRUE);
    if (FAILED(hr))
        return hr;
    hr = wo::DbSheet::correctFieldType(spField.get(), obj, type);
    if (FAILED(hr))
        return hr;

    hr = spField->SetType(type, nullptr);
    if (FAILED(hr))
        return hr;

    bool arraySupport {};
    if (isSyncSheet && obj.has("arraySupport"))
    {
        VAR_OBJ_EXPECT_BOOL(obj, "arraySupport");
        arraySupport = obj.field_bool("arraySupport");
        spField->SetArray(alg::bool2BOOL(arraySupport));
    }

    // syncField需要在linkSheet之前
    if (obj.has("syncField"))
    {
        VAR_OBJ_EXPECT_BOOL(obj, "syncField");
        bool syncField = obj.field_bool("syncField");
        hr = spField->SetSyncField(syncField);
        if (FAILED(hr))
            return hr;
    }

    if (obj.has("width"))
    {
        VAR_OBJ_EXPECT_NUMERIC(obj, "width");
        int twip = obj.field_int32("width");
        if (twip > 0)
        {
            hr = pDbSheetOp->SetFieldWidth(fieldId, twip);
            if(FAILED(hr))
                return hr;
        }
    }

    if (obj.has("uniqueValue"))
    {
        VAR_OBJ_EXPECT_BOOL(obj, "uniqueValue");
        bool uniqueValue = obj.field_bool("uniqueValue");
        hr = spField->SetValueUnique(uniqueValue, FALSE);
        if(FAILED(hr))
            return hr;
    }

    if (obj.has("numberFormat"))
    {
        VAR_OBJ_EXPECT_STRING(obj, "numberFormat");
        WebStr numberFormat = obj.field_str("numberFormat");
        if (xstrlen(numberFormat) > 0)
        {
            hr = spField->SetNumberFormat(numberFormat);
            if(FAILED(hr))
                return hr;
        }
    }

    if (obj.has("customConfig") && obj.get_s("customConfig").type() == binary_wo::typeString)
    {
        hr = spField->SetCustomConfig(obj.field_str("customConfig"));
        if (FAILED(hr))
            return hr;
    }

    switch (type)
    {
        case Et_DbSheetField_SingleSelect:
        case Et_DbSheetField_MultipleSelect:
        {
            ks_stdptr<IDbField_Select> spFieldSelect = spField;
            VAR_OBJ_EXPECT_ARRAY(obj, "items")
            binary_wo::VarObj items = obj.get_s("items");
            for (int i = 0, cnt = items.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = items.at_s(i);
                VAR_OBJ_EXPECT_STRING(item, "value")
                WebStr value = item.field_str("value");
                if (item.has("color"))
                {
                    VAR_OBJ_EXPECT_NUMERIC(item, "color");
                    ARGB color = item.field_uint32("color");
                    hr = spFieldSelect->AppendItem(value, color);
                }
                else
                {
                    hr = spFieldSelect->AppendItemWithAutoColor(value);
                }
                if (FAILED(hr))
                    return hr;
            }
            if (spFieldSelect->Count() == 0)
                return E_DBSHEET_SELECT_ITEM_EMPTY;

            if(obj.has("allowAddItemWhenInputting"))
            {
                //创建字段: 对单/多选项 "允许填写时新增选项"属性进行初始化。
                BOOL bAllowAddItemWhenInputting = TRUE;
                VAR_OBJ_EXPECT_BOOL(obj, "allowAddItemWhenInputting");
                bAllowAddItemWhenInputting = alg::bool2BOOL(obj.field_bool("allowAddItemWhenInputting"));
                hr = spFieldSelect->SetAllowAddItemWhenInputting(bAllowAddItemWhenInputting);
                if (FAILED(hr))
                    return hr;
            }

            if (obj.has("autoAddItem"))
            {
                BOOL bAutoAddItem = TRUE;
                VAR_OBJ_EXPECT_BOOL(obj, "autoAddItem");
                bAutoAddItem = alg::bool2BOOL(obj.field_bool("autoAddItem"));
                hr = spFieldSelect->SetAutoAddItem(bAutoAddItem);
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Rating:
        {
            ks_stdptr<IDbField_Rating> spFieldRating = spField;
            VAR_OBJ_EXPECT_NUMERIC(obj, "max")
            hr = spFieldRating->SetMaxRating(obj.field_uint32("max"));
            if (FAILED(hr))
                return hr;
            break;
        }
        case Et_DbSheetField_Date:
        {
            ks_stdptr<IDbField_Date> spFieldDate = spField;
            if (obj.has("loadLegalHoliday"))
            {
                VAR_OBJ_EXPECT_NUMERIC(obj, "loadLegalHoliday")
                hr = spFieldDate->SetLoadLegalHoliday(obj.field_uint32("loadLegalHoliday"));
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Url:
        {
            ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = spField;
            if (obj.has("displayText"))
            {
                VAR_OBJ_EXPECT_STRING(obj, "displayText");
                hr = spFieldHyperlink->SetDisplayText(obj.field_str("displayText"));
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Formula:
        {
            ks_stdptr<IDbField_Formula> spFieldFormula = spField;
            if (!obj.has("formula"))
                break;
            VAR_OBJ_EXPECT_STRING(obj, "formula")
            ks_stdptr<IDbtBookCtx> spDbtBookCtx;
            VS(pDbSheetOp->GetBook()->GetExtDataItem(edBookDbBookCtx, (IUnknown**) &spDbtBookCtx));
            DB_FML_CPL_TYPE fType = spDbtBookCtx->IsRefColMode() ? FML_CPL_ET_COMPATIBLE : FML_CPL_THISROW;
            hr = spFieldFormula->SetFormula(obj.field_str("formula"), fType);
            if (FAILED(hr))
                return hr;
            if (obj.has("showPercentAsProgress"))
            {
                VAR_OBJ_EXPECT_BOOL(obj, "showPercentAsProgress");
                hr = spFieldFormula->SetShowPercentAsProgress(obj.field_bool("showPercentAsProgress"));
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Contact:
            hr = DbSheet::ConfigureDbContactField(spField.get(), obj, true, "multipleContacts", "noticeNewContact");
            if (FAILED(hr))
                return hr;
            if (!spField->IsSyncField())
            {
                hr = DbSheet::ConfigureDbContactExtendField(spField.get(), obj);
                if (FAILED(hr))
                    return hr;
            }
            break;
        case Et_DbSheetField_CreatedBy:
            if (!spField->IsSyncField())
            {
                hr = DbSheet::ConfigureDbContactExtendField(spField.get(), obj);
                if (FAILED(hr))
                    return hr;
            }
            break;
        case Et_DbSheetField_Link:
        case Et_DbSheetField_OneWayLink:
            hr = DbSheet::ConfigureDbLinkField(spField.get(), obj, true, "multipleLinks");
            if (FAILED(hr))
                return hr;
            break;
        case Et_DbSheetField_LastModifiedTime:
        case Et_DbSheetField_LastModifiedBy:
        {
            ks_stdptr<IDbField_WatchedField> spWatchedField = spField;
            if (obj.has("watchedAll"))
            {
                spWatchedField->SetWatchedAll(obj.field_bool("watchedAll"));
            }
            if (obj.has("watchedField") && !spWatchedField->IsWatchedAll())
            {
               binary_wo::VarObj objVec = obj.get("watchedField");
               std::vector<EtDbId> dbIds;
               IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
               for(int32 i = 0, count = objVec.arrayLength_s(); i < count; ++i)
               {
                    EtDbId id = INV_EtDbId;
                    pCtx->DecodeEtDbId(objVec.at(i).value_str(), &id);
                    dbIds.push_back(id);
               }
               spWatchedField->SetWatchedFieldId(dbIds.data(), dbIds.size());
            }
            break;
        }
        case Et_DbSheetField_Lookup:
        {
            ks_stdptr<IDbField> spLinkField;
            ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
            DBLookupType ltType = LT_NORMAL;
            UINT sheetStId = 0;
            if (obj.has("lookupType"))
            {
                UINT iType = obj.field_uint32("lookupType");
                ltType = static_cast<DBLookupType>(iType);
                hr = spFieldLookup->SetLookupType(ltType);
                if (FAILED(hr))
                    return hr;
            }
            if (obj.has("lookupSheetId"))
            {
                sheetStId = obj.field_uint32("lookupSheetId");
                hr = spFieldLookup->SetLookupSheetId(sheetStId);
                if (FAILED(hr))
                    return hr;
            }
            if (obj.has("linkField"))
            {
                VAR_OBJ_EXPECT_STRING(obj, "linkField");
                WebStr fieldName = obj.field_str("linkField");
                hr = fieldRetriever.GetField(fieldName, &spLinkField);
                if (FAILED(hr))
                    return hr;

                hr = spFieldLookup->SetLinkFieldId(spLinkField->GetID());
                if (FAILED(hr))
                    return hr;
            }

            {
                UINT uLinkSheetId = sheetStId;
                if (spFieldLookup->IsNormalType())
                {
                    if (!spLinkField)
                        return E_INVALIDARG;
                    ks_stdptr<IDbField_Link> spFieldLink = spLinkField;
                    uLinkSheetId = spFieldLink->GetLinkSheet();
                }
                if (uLinkSheetId == 0)
                    return E_INVALIDARG;
                resetLookupFieldRetrieverBySid(pDbSheetOp, uLinkSheetId, bPreferId, fieldRetriever);
                VAR_OBJ_EXPECT_STRING(obj, "lookupField");
                WebStr fieldName = obj.field_str("lookupField");
                ks_stdptr<IDbField> spLookupField;
                hr = fieldRetriever.GetField(fieldName, &spLookupField);
                if (FAILED(hr))
                    return hr;

                hr = spFieldLookup->SetLookupFieldId(spLookupField->GetID());
                if (FAILED(hr))
                    return hr;
            }

            if (obj.has("aggregation"))
            {
                VAR_OBJ_EXPECT_STRING(obj, "aggregation");
                ET_DbSheet_Lookup_Function func = DbSheet_Lookup_Function_Origin;
                DECODE_STR_OBJ(obj, "aggregation", DbLookupFunction, func, pErrorMsg);
                hr = spFieldLookup->SetLookupFunction(func);
                if (FAILED(hr))
                    return hr;
            }

            if (obj.has("filter"))
            {
                hr = DbSheet::ConfigureLookupCondProps(spField, pDbSheetOp, obj);
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Address:
        {
            ks_stdptr<IDbField_Cascade> spFieldAddress = spField;
            {
                VAR_OBJ_EXPECT_NUMERIC(obj, "addressLevel");
                UINT addressLevel = obj.field_uint32("addressLevel");
                hr = spFieldAddress->SetCascadeLevel(addressLevel);
                if (FAILED(hr))
                    return hr;
            }
            {
                VAR_OBJ_EXPECT_BOOL(obj, "detailedAddress");
                hr = spFieldAddress->SetWithDetailedInfo(obj.field_bool("detailedAddress"));
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Cascade:
        {
            ks_stdptr<IDbField_Cascade> spFieldCascade = spField;
            {
                VAR_OBJ_EXPECT_BOOL(obj, "displayAllLevel");
                spFieldCascade->SetDisplayAllLevel(obj.field_bool("displayAllLevel"));
            }
            {
                VAR_OBJ_EXPECT_ARRAY(obj, "allCascadeOption");
                hr = spFieldCascade->SetAllCascadeOption(obj.get_s("allCascadeOption"));
                if (FAILED(hr))
                    return hr;
            }
            if (obj.has("cascadeTitle"))
            {
                binary_wo::VarObj titleArray = obj.get_s("cascadeTitle");
                int titleCount = titleArray.arrayLength_s();
                std::vector<PCWSTR> titleList;
                titleList.reserve(titleCount);
                for (UINT i = 0; i < titleCount; ++i)
                    titleList.push_back(titleArray.item_str(i));
                hr = spFieldCascade->SetCascadeTitle(titleList.data(), titleList.size());
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Attachment:
        {
            ks_stdptr<IDbField_Attachment> spFieldAttachment = spField;
            if (obj.has("displayStyle"))
            {
                VAR_OBJ_EXPECT_STRING(obj, "displayStyle");
                ET_DbSheet_Attachment_Display_Style style = DbSheet_Attachment_Display_Style_Pic;
                _appcore_GainEncodeDecoder()->DecodeDbAttachmentDisplayStyle(
                        obj.field_str("displayStyle"), &style);
                spFieldAttachment->SetAttachmentDisplayStyle(style);
            }
            if (obj.has("onlyUploadByCamera"))
            {
                VAR_OBJ_EXPECT_BOOL(obj, "onlyUploadByCamera");
                spFieldAttachment->SetOnlyUploadByCamera(obj.field_bool("onlyUploadByCamera"));
            }
            break;
        }
        case Et_DbSheetField_Department:
        {
            ks_stdptr<IDbField_Cascade> spFieldDepartment = spField;
            if (obj.has("supportMulti"))
            {
                spFieldDepartment->SetMultiValue(obj.field_bool("supportMulti"));
            }
            if (obj.has("displayAllLevel"))
            {
                spFieldDepartment->SetDisplayAllLevel(obj.field_bool("displayAllLevel"));
            }
            break;
        }
        case Et_DbSheetField_FormulaResult:
            hr = DbSheet::ConfigureDbFormulaResultField(spField.get(), obj, isSyncSheet);
            if (FAILED(hr))
                return hr;
            break;
        case Et_DbSheetField_Button:
        {
            ks_stdptr<IDbField_Button> spFieldButton = spField;
            if (obj.has("icon")) spFieldButton->SetButtonIcon(obj.field_str("icon"));
            if (obj.has("text")) spFieldButton->SetButtonText(obj.field_str("text"));
            if (obj.has("successText")) spFieldButton->SetSuccessText(obj.field_str("successText"));
            if (obj.has("textColor")) spFieldButton->SetTextColor(obj.field_uint32("textColor"));
            if (obj.has("backgroundColor")) spFieldButton->SetBackgroundColor(obj.field_uint32("backgroundColor"));
            break;
        }
        case Et_DbSheetField_BarCode:
        {
            ks_stdptr<IDbField_BarCode> spFieldBarCode = spField;
            if (obj.has("onlyScanByCamera"))
            {
                VAR_OBJ_EXPECT_BOOL(obj, "onlyScanByCamera");
                spFieldBarCode->SetOnlyScanByCamera(obj.field_bool("onlyScanByCamera"));
            }
            break;
        }
    }

    if (obj.has("defaultValue"))
    {
        binary_wo::VarObj objDefaultVal = obj.get_s("defaultValue");
        ks_wstring defaultValue;
        if (objDefaultVal.type() == binary_wo::typeString)
        {
            defaultValue = obj.field_str("defaultValue");
        }
        else if (objDefaultVal.type() == binary_wo::typeArray) // 序列化默认值时可能是数组形式
        {
            int32 cnt = objDefaultVal.arrayLength_s();
            if (cnt > 0)
            {
                binary_wo::VarObj item = objDefaultVal.at_s(0);
                if (spField->GetType() == Et_DbSheetField_Contact)
                {
                    if (item.has("id"))
                        defaultValue = item.field_str("id");
                }
                else if (spField->GetType() == Et_DbSheetField_MultipleSelect)
                {
                    ks_stdptr<IDbSelectItemHandle> spSelectHandle;
                    _db_CreateObject(CLSID_KDbSelectItemHandle, IID_IDbSelectItemHandle, (void **)&spSelectHandle);
                    for (int i = 0; i < cnt; i++)
                    {
                        binary_wo::VarObj item = objDefaultVal.at_s(i);
                        if (item.type() == binary_wo::typeString)
                            spSelectHandle->AppendByItem(item.value_str());
                    }
                    if (spSelectHandle->Count() > 0)
                        defaultValue = spSelectHandle->GetConvertedString();
                }
                else
                    ASSERT(false);
            }
        }
        VAR_OBJ_EXPECT_STRING(obj, "defaultValueType");
        WebStr defaultValueType = obj.field_str("defaultValueType");
        ET_DbSheet_Field_Default_Value_Type defType = DbSheet_Field_Dvt_Normal;
        DECODE_STR_OBJ(obj, "defaultValueType", DbFieldDefaultValueType, defType, pErrorMsg);
        hr = spField->SetDefaultVal(defType, defaultValue.c_str());
        if(FAILED(hr))
            return hr;
    }
	if (spField->IsSyncField() || pDbSheetOp->IsSyncSheet())
	{
		// 第一次设置为同步字段时为插入的时候，同步数据插入默认显示
		ks_stdptr<IDBSheetViews> spDBSheetViews;
		hr = pDbSheetOp->GetDbSheetViews(&spDBSheetViews);
		if (FAILED(hr))
			return hr;
        class AllDbViewsEnum : public IDbViewEnum
        {
            using CallBack = std::function<HRESULT(IDBSheetView*)>;
        public:
            AllDbViewsEnum(const CallBack &cb)
                : m_cb(cb)
            {}

            STDPROC Do(IDBSheetView *pView) override
            {
                return m_cb(pView);
            }
        private:
            const CallBack m_cb;
        };
		AllDbViewsEnum viewsEnum([fieldId](IDBSheetView* pView) -> HRESULT {
			ET_DBSheet_ViewType type = pView->GetType();
			if (type == et_DBSheetView_Grid)
				pView->SetFieldsHidden(fieldId, FALSE);
			return S_OK;
		});
		spDBSheetViews->EnumViews(&viewsEnum);
	}

    *ppField = spField.detach();
    return S_OK;
}

HRESULT UpdateField(binary_wo::VarObj obj, bool bPreferId, IDBSheetOp *pDbSheetOp, IDbField *pField, ks_wstring* pErrorMsg)
{
    IDbFieldsManager *pFieldsManager = pDbSheetOp->GetFieldsManager();
    DbSheetFieldRetriever fieldRetriever;
    fieldRetriever.Reset(pFieldsManager, bPreferId);

    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    HRESULT hr = S_OK;
    const bool isSyncSheet = pDbSheetOp->IsSyncSheet();

    if (obj.has("name"))
    {
        VAR_OBJ_EXPECT_STRING(obj, "name")
        WebStr name = obj.field_str("name");
        if (xstrcmp(pField->GetName(), name) != 0)
        {
            hr = pField->SetName(name, TRUE, TRUE);
            if (FAILED(hr))
                return hr;
        }
    }

    if (obj.has("width"))
    {
        // TODO: 目前只针对createSheet后的UpdateField, 如果在表格视图下单独设置过宽度, 则新修改的宽度无法生效
        VAR_OBJ_EXPECT_NUMERIC(obj, "width");
        int twip = obj.field_int32("width");
        if (twip > 0)
        {
            hr = pDbSheetOp->SetFieldWidth(pField->GetID(), twip);
            if(FAILED(hr))
                return hr;
        }
    }

    HRESULT updateHr = S_OK;
    {
        DbSheet::DbFieldUpdateScope updateScope(pField, pFieldsManager->GetBook(), updateHr);
        if (FAILED(updateHr))
            return updateHr;

        if (obj.has("type"))
        {
            VAR_OBJ_EXPECT_STRING(obj, "type")
            ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
            DECODE_STR_OBJ(obj, "type", FieldType, type, pErrorMsg);
            hr = wo::DbSheet::correctFieldType(pField, obj, type);
            if (FAILED(hr))
                return hr;
            hr = pField->SetType(type, nullptr);
            if (FAILED(hr))
                return hr;
        }

        if (obj.has("syncField"))
        {
            VAR_OBJ_EXPECT_BOOL(obj, "syncField");
            bool syncField = obj.field_bool("syncField");
            hr = pField->SetSyncField(syncField);
            if(FAILED(hr))
                return hr;
        }
        if (obj.has("uniqueValue"))
        {
            VAR_OBJ_EXPECT_BOOL(obj, "uniqueValue");
            bool uniqueValue = obj.field_bool("uniqueValue");
            hr = pField->SetValueUnique(uniqueValue, TRUE);
            if(FAILED(hr))
                return hr;
        }

        if (obj.has("numberFormat"))
        {
            VAR_OBJ_EXPECT_STRING(obj, "numberFormat");
            WebStr numberFormat = obj.field_str("numberFormat");
            if (xstrlen(numberFormat) > 0)
            {
                hr = pField->SetNumberFormat(numberFormat);
                if(FAILED(hr))
                    return hr;
            }
        }

        if (obj.has("customConfig") && obj.get_s("customConfig").type() == binary_wo::typeString)
        {
            hr = pField->SetCustomConfig(obj.field_str("customConfig"));
            if (FAILED(hr))
                return hr;
        }

        bool arraySupport {};
        if (isSyncSheet && obj.has("arraySupport"))
        {
            VAR_OBJ_EXPECT_BOOL(obj, "arraySupport");
            arraySupport = obj.field_bool("arraySupport");
            pField->SetArray(alg::bool2BOOL(arraySupport));
        }

        switch (pField->GetType())
        {
            case Et_DbSheetField_SingleSelect:
            case Et_DbSheetField_MultipleSelect:
            {
                ks_stdptr<IDbField_Select> spFieldSelect = pField;
                IDbField *pOldField = updateScope.GetOldField();
                // TODO: 这块整合到appcore内处理
                bool bGenerateSelectItemsFromContents = DbSheet::needGenerateSelectItemsFromContents(pOldField, pField);
                if (bGenerateSelectItemsFromContents)
                {
                    GlobalSharedStringUnorderedSet selectItems;
                    DbSheet::GetFieldValues(pDbSheetOp, pField->GetID(), pOldField->GetType(), pField->GetType(), selectItems);
                    for (auto it = selectItems.begin(); it != selectItems.end(); ++it)
                    {
                        PCWSTR str = it->c_str();
                        if (!str || *str == __Xc('\0'))
                            continue;
                        hr = spFieldSelect->AppendItemWithAutoColor(str);
                        if (FAILED(hr))
                            return hr;
                    }
                    if (!selectItems.empty())
                        break;
                }
                if (obj.has("items"))
                {
                    std::vector<SelectFieldItem> itemVec;
                    VAR_OBJ_EXPECT_ARRAY(obj, "items")
                    binary_wo::VarObj items = obj.get_s("items");
                    UINT fieldItemCnt = 0;
                    for (int i = 0, cnt = items.arrayLength_s(); i < cnt; i++)
                    {
                        binary_wo::VarObj item = items.at_s(i);
                        VAR_OBJ_EXPECT_STRING(item, "value")
                        SelectFieldItem selectItem(INV_EtDbId, item.field_str("value"), DEFAULT_COLOR);
                        if (item.has("id"))
                        {
                            VAR_OBJ_EXPECT_STRING(item, "id")
                            hr = _appcore_GainDbSheetContext()->DecodeEtDbId(item.field_str("id"), &selectItem.id);
                            if (FAILED(hr))
                                return hr;
                            hr = spFieldSelect->ItemById(selectItem.id, nullptr, &selectItem.color);
                            if (FAILED(hr))
                            {
                                if(pField->IsSyncField())   //同步表同步多选项字段时，新创建的多选项字段没有item，可以直接添加
                                    spFieldSelect->AppendItemForIO(selectItem.value, selectItem.color, selectItem.id);
                                else 
                                    return hr;
                            }
                        }
                        if (item.has("color"))
                        {
                            VAR_OBJ_EXPECT_NUMERIC(item, "color");
                            selectItem.color = item.field_uint32("color");
                        }
                        else if (!item.has("id"))
                        {
                            selectItem.color = spFieldSelect->GetItemAutoColor(fieldItemCnt);
                        }
                        ++fieldItemCnt;
                        itemVec.push_back(selectItem);
                    }
                    hr = spFieldSelect->BatchEditItems(&itemVec[0], itemVec.size());
                    if (FAILED(hr))
                        return hr;
                }
                if(obj.has("allowAddItemWhenInputting"))
                {
                    BOOL bAllowAddItemWhenInputting = TRUE;
                    VAR_OBJ_EXPECT_BOOL(obj, "allowAddItemWhenInputting");
                    bAllowAddItemWhenInputting = alg::bool2BOOL(obj.field_bool("allowAddItemWhenInputting"));
                    hr = spFieldSelect->SetAllowAddItemWhenInputting(bAllowAddItemWhenInputting);
                    if (FAILED(hr))
                        return hr;
                }
                if (obj.has("autoAddItem"))
                {
                    BOOL bAutoAddItem = TRUE;
                    VAR_OBJ_EXPECT_BOOL(obj, "autoAddItem");
                    bAutoAddItem = alg::bool2BOOL(obj.field_bool("autoAddItem"));
                    hr = spFieldSelect->SetAutoAddItem(bAutoAddItem);
                    if (FAILED(hr))
                        return hr;
                }
                break;
            }
            case Et_DbSheetField_Rating:
            {
                if (obj.has("max"))
                {
                    ks_stdptr<IDbField_Rating> spFieldRating = pField;
                    VAR_OBJ_EXPECT_NUMERIC(obj, "max")
                    spFieldRating->SetMaxRating(obj.field_uint32("max"));
                }
                break;
            }
            case Et_DbSheetField_Date:
            {
                if (obj.has("loadLegalHoliday"))
                {
                    ks_stdptr<IDbField_Date> spFieldDate = pField;
                    VAR_OBJ_EXPECT_NUMERIC(obj, "loadLegalHoliday")
                    spFieldDate->SetLoadLegalHoliday(obj.field_uint32("loadLegalHoliday"));
            
                }
                break;
            }
            case Et_DbSheetField_Url:
            {
                ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = pField;
                if (obj.has("displayText"))
                {
                    VAR_OBJ_EXPECT_STRING(obj, "displayText");
                    hr = spFieldHyperlink->SetDisplayText(obj.field_str("displayText"));
                    if (FAILED(hr))
                        return hr;
                }
                break;
            }
            case Et_DbSheetField_Formula:
            {
                ks_stdptr<IDbField_Formula> spFieldFormula = pField;
                if(obj.has("formula"))
                {
                    VAR_OBJ_EXPECT_STRING(obj, "formula");
                    ks_stdptr<IDbtBookCtx> spDbtBookCtx;
                    VS(pDbSheetOp->GetBook()->GetExtDataItem(edBookDbBookCtx, (IUnknown**) &spDbtBookCtx));
                    DB_FML_CPL_TYPE fType = spDbtBookCtx->IsRefColMode() ? FML_CPL_ET_COMPATIBLE : FML_CPL_THISROW;
                    hr = spFieldFormula->SetFormula(obj.field_str("formula"), fType);
                    if (FAILED(hr))
                        return hr;
                }
                if (obj.has("showPercentAsProgress"))
                {
                    VAR_OBJ_EXPECT_BOOL(obj, "showPercentAsProgress");
                    hr = spFieldFormula->SetShowPercentAsProgress(obj.field_bool("showPercentAsProgress"));
                    if (FAILED(hr))
                        return hr;
                }
                break;
            }
            case Et_DbSheetField_Contact:
                hr = DbSheet::ConfigureDbContactField(pField, obj, false, "multipleContacts", "noticeNewContact");
                if (FAILED(hr))
                    return hr;
                if (!pField->IsSyncField())
                {
                    hr = DbSheet::ConfigureDbContactExtendField(pField, obj);
                    if (FAILED(hr))
                        return hr;
                }
                break;
            case Et_DbSheetField_CreatedBy:
                if (!pField->IsSyncField())
                {
                    hr = DbSheet::ConfigureDbContactExtendField(pField, obj);
                    if (FAILED(hr))
                        return hr;
                }
                break;
            case Et_DbSheetField_Link:
            case Et_DbSheetField_OneWayLink:
                hr = DbSheet::ConfigureDbLinkField(pField, obj, false, "multipleLinks");
                if (FAILED(hr))
                    return hr;
                break;
            case Et_DbSheetField_LastModifiedTime:
            case Et_DbSheetField_LastModifiedBy:
            {
                ks_stdptr<IDbField_WatchedField> spWatchedField = pField;
                if (obj.has("watchedAll"))
                {
                    spWatchedField->SetWatchedAll(obj.field_bool("watchedAll"));
                }
                if (obj.has("watchedField") && !spWatchedField->IsWatchedAll())
                {
                    binary_wo::VarObj objVec = obj.get("watchedField");
                    std::vector<EtDbId> dbIds;
                    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
                    for(int32 i = 0, count = objVec.arrayLength_s(); i < count; ++i)
                    {
                            EtDbId id = INV_EtDbId;
                            pCtx->DecodeEtDbId(objVec.at(i).value_str(), &id);
                            dbIds.push_back(id);
                    }
                    spWatchedField->SetWatchedFieldId(dbIds.data(), dbIds.size());
                }
                break;
            }
            case Et_DbSheetField_Lookup:
            {
                ks_stdptr<IDbField_Lookup> spFieldLookup = pField;
                DBLookupType ltType = LT_NORMAL;
                if (obj.has("lookupType"))
                {
                    UINT iType = obj.field_uint32("lookupType");
                    ltType = static_cast<DBLookupType>(iType);
                    hr = spFieldLookup->SetLookupType(ltType);
                    if (FAILED(hr))
                        return hr;
                }
                if (obj.has("lookupSheetId"))
                {
                    UINT sheetStId = obj.field_uint32("lookupSheetId");
                    hr = spFieldLookup->SetLookupSheetId(sheetStId);
                    if (FAILED(hr))
                        return hr;
                }
                if (obj.has("linkField"))
                {
                    VAR_OBJ_EXPECT_STRING(obj, "linkField");
                    WebStr fieldName = obj.field_str("linkField");
                    ks_stdptr<IDbField> spLinkField;
                    hr = fieldRetriever.GetField(fieldName, &spLinkField);
                    if (FAILED(hr))
                        return hr;

                    hr = spFieldLookup->SetLinkFieldId(spLinkField->GetID());
                    if (FAILED(hr))
                        return hr;
                }

                if (obj.has("lookupField"))
                {
                    resetLookupFieldRetriever(pDbSheetOp, pField, bPreferId, fieldRetriever);
                    VAR_OBJ_EXPECT_STRING(obj, "lookupField");
                    WebStr fieldName = obj.field_str("lookupField");
                    ks_stdptr<IDbField> spLookupField;
                    hr = fieldRetriever.GetField(fieldName, &spLookupField);
                    if (FAILED(hr))
                        return hr;

                    hr = spFieldLookup->SetLookupFieldId(spLookupField->GetID());
                    if (FAILED(hr))
                        return hr;
                }

                if (obj.has("aggregation"))
                {
                    VAR_OBJ_EXPECT_STRING(obj, "aggregation");
                    ET_DbSheet_Lookup_Function func = DbSheet_Lookup_Function_Origin;
                    DECODE_STR_OBJ(obj, "aggregation", DbLookupFunction, func, pErrorMsg);
                    hr = spFieldLookup->SetLookupFunction(func);
                    if (FAILED(hr))
                        return hr;
                }

                if (obj.has("filter"))
                {
                    hr = DbSheet::ConfigureLookupCondProps(pField, pDbSheetOp, obj);
                    if (FAILED(hr))
                        return hr;
                }
                break;
            }
            case Et_DbSheetField_Address:
            {
                ks_stdptr<IDbField_Cascade> spFieldAddress = pField;
                if (obj.has("addressLevel"))
                {
                    VAR_OBJ_EXPECT_NUMERIC(obj, "addressLevel");
                    UINT addressLevel = obj.field_uint32("addressLevel");
                    hr = spFieldAddress->SetCascadeLevel(addressLevel);
                    if (FAILED(hr))
                        return hr;
                }
                if (obj.has("detailedAddress"))
                {
                    VAR_OBJ_EXPECT_BOOL(obj, "detailedAddress");
                    hr = spFieldAddress->SetWithDetailedInfo(obj.field_bool("detailedAddress"));
                    if (FAILED(hr))
                        return hr;
                }
                break;
            }
            case Et_DbSheetField_Cascade:
            {
                ks_stdptr<IDbField_Cascade> spFieldCascade = pField;
                if (obj.has("displayAllLevel"))
                {
                    spFieldCascade->SetDisplayAllLevel(obj.field_bool("displayAllLevel"));
                }
                if (obj.has("allCascadeOption"))
                {
                    hr = spFieldCascade->SetAllCascadeOption(obj.get_s("allCascadeOption"));
                    if (FAILED(hr))
                        return hr;
                }
                if (obj.has("cascadeTitle"))
                {
                    binary_wo::VarObj titleArray = obj.get_s("cascadeTitle");
                    int titleCount = titleArray.arrayLength_s();
                    std::vector<PCWSTR> titleList;
                    titleList.reserve(titleCount);
                    for (UINT i = 0; i < titleCount; ++i)
                        titleList.push_back(titleArray.item_str(i));
                    hr = spFieldCascade->SetCascadeTitle(titleList.data(), titleList.size());
                    if (FAILED(hr))
                        return hr;
                }
                break;
            }
            case Et_DbSheetField_Attachment:
            {
                ks_stdptr<IDbField_Attachment> spFieldAttachment = pField;
                if (obj.has("displayStyle"))
                {
                    VAR_OBJ_EXPECT_STRING(obj, "displayStyle");
                    ET_DbSheet_Attachment_Display_Style style = DbSheet_Attachment_Display_Style_Pic;
                    _appcore_GainEncodeDecoder()->DecodeDbAttachmentDisplayStyle(
                            obj.field_str("displayStyle"), &style);
                    spFieldAttachment->SetAttachmentDisplayStyle(style);
                }
                if (obj.has("onlyUploadByCamera"))
                {
                    VAR_OBJ_EXPECT_BOOL(obj, "onlyUploadByCamera");
                    spFieldAttachment->SetOnlyUploadByCamera(obj.field_bool("onlyUploadByCamera"));
                }
                break;
            }
            case Et_DbSheetField_Department:
            {
                ks_stdptr<IDbField_Cascade> spFieldDepartment = pField;
                if (obj.has("supportMulti"))
                {
                    spFieldDepartment->SetMultiValue(obj.field_bool("supportMulti"));
                }
                if (obj.has("displayAllLevel"))
                {
                    spFieldDepartment->SetDisplayAllLevel(obj.field_bool("displayAllLevel"));
                }
                break;
            }
            case Et_DbSheetField_FormulaResult:
                hr = DbSheet::ConfigureDbFormulaResultField(pField, obj, isSyncSheet);
                if (FAILED(hr))
                    return hr;
                break;
            case Et_DbSheetField_Button:
            {
                ks_stdptr<IDbField_Button> spFieldButton = pField;
                if (obj.has("icon")) spFieldButton->SetButtonIcon(obj.field_str("icon"));
                if (obj.has("text")) spFieldButton->SetButtonText(obj.field_str("text"));
                if (obj.has("successText")) spFieldButton->SetSuccessText(obj.field_str("successText"));
                if (obj.has("textColor")) spFieldButton->SetTextColor(obj.field_uint32("textColor"));
                if (obj.has("backgroundColor")) spFieldButton->SetBackgroundColor(obj.field_uint32("backgroundColor"));
                break;
            }
            case Et_DbSheetField_BarCode:
            {
                ks_stdptr<IDbField_BarCode> spFieldBarCode = pField;
                if (obj.has("onlyScanByCamera"))
                {
                    VAR_OBJ_EXPECT_BOOL(obj, "onlyScanByCamera");
                    spFieldBarCode->SetOnlyScanByCamera(obj.field_bool("onlyScanByCamera"));
                }
                break;
            }
            default:
                break;
        }

        if (obj.has("defaultValue") || obj.has("defaultValueType"))
        {
            ks_wstring defaultValue;
            if (obj.has("defaultValue"))
            {
                binary_wo::VarObj objDefaultVal = obj.get_s("defaultValue");
                if (objDefaultVal.type() == binary_wo::typeString)
                {
                    defaultValue = obj.field_str("defaultValue");
                }
                else if (objDefaultVal.type() == binary_wo::typeArray) // 序列化默认值时可能是数组形式
                {
                    int32 cnt = objDefaultVal.arrayLength_s();
                    if (cnt > 0)
                    {
                        binary_wo::VarObj item = objDefaultVal.at_s(0);
                        if (pField->GetType() == Et_DbSheetField_Contact)
                        {
                            if (item.has("id"))
                                defaultValue = item.field_str("id");
                        }
                        else if (pField->GetType() == Et_DbSheetField_MultipleSelect)
                        {
                            ks_stdptr<IDbSelectItemHandle> spSelectHandle;
                            _db_CreateObject(CLSID_KDbSelectItemHandle, IID_IDbSelectItemHandle, (void **)&spSelectHandle);
                            for (int i = 0; i < cnt; i++)
                            {
                                binary_wo::VarObj item = objDefaultVal.at_s(i);
                                if (item.type() == binary_wo::typeString)
                                    spSelectHandle->AppendByItem(item.value_str());
                            }
                            if (spSelectHandle->Count() > 0)
                                defaultValue = spSelectHandle->GetConvertedString();
                        }
                        else
                            ASSERT(false);
                    }
                }
            }
            if (obj.has("defaultValueType"))
            {
                ET_DbSheet_Field_Default_Value_Type defType = DbSheet_Field_Dvt_Normal;
                VAR_OBJ_EXPECT_STRING(obj, "defaultValueType");
                DECODE_STR_OBJ(obj, "defaultValueType", DbFieldDefaultValueType, defType, pErrorMsg);
                hr = pField->SetDefaultVal(defType, defaultValue.c_str());
                if(FAILED(hr))
                    return hr;
            }
        }
    } // DbFieldUpdateScope
    if (FAILED(updateHr))
        return updateHr;

    if (obj.has("uniqueValue"))
    {
        VAR_OBJ_EXPECT_BOOL(obj, "uniqueValue");
        bool uniqueValue = obj.field_bool("uniqueValue");
        hr = pField->SetValueUnique(uniqueValue, TRUE);
        if(FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT SetPrimaryField(PCWSTR field, bool bPreferId, IDBSheetOp *pDbSheetOp, ks_wstring* pErrorMsg)
{
    IDbFieldsManager *pFieldsManager = pDbSheetOp->GetFieldsManager();
    DbSheetFieldRetriever fieldRetriever;
    fieldRetriever.Reset(pFieldsManager, bPreferId);
    ks_stdptr<IDbField> spField;
    HRESULT hr = fieldRetriever.GetField(field, &spField);
    if (FAILED(hr))
        return hr;
    EtDbId primaryFieldId = spField->GetID();
    if (primaryFieldId != pFieldsManager->GetPrimaryField())
    {
        hr = pFieldsManager->SetPrimaryField(primaryFieldId);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT UpdateView(binary_wo::VarObj obj, bool bPreferId, IDBSheetView *pView)
{
    DbSheetFieldRetriever fieldRetriever;
    fieldRetriever.Reset(pView->GetFieldsManager(), bPreferId);

    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    if (obj.has("name"))
    {
        VAR_OBJ_EXPECT_STRING(obj, "name");
        WebStr name = obj.field_str("name");
        HRESULT hr = pView->SetName(name);
        if (FAILED(hr))
            return hr;
    }

    if (obj.has("description"))
    {
        VAR_OBJ_EXPECT_STRING(obj, "description");
        WebStr description = obj.field_str("description");
        HRESULT hr = pView->SetDescription(description);
        if (FAILED(hr))
            return hr;
    }

    if (obj.has("orderFields"))
    {
        VAR_OBJ_EXPECT_ARRAY_FOR_TYPE(obj, "orderFields", binary_wo::typeString);
        binary_wo::VarObj orderFields = obj.get_s("orderFields");
        std::vector<EtDbId> orderFldIdsVec;
        for (int i = 0, cnt = orderFields.arrayLength_s(); i < cnt; i++)
        {
            binary_wo::VarObj item = orderFields.at_s(i);
            WebStr fieldName = item.value_str();
            ks_stdptr<IDbField> spField;
            HRESULT hr = fieldRetriever.GetField(fieldName, &spField);
            if (FAILED(hr))
                return hr;
            orderFldIdsVec.push_back(spField->GetID());
        }
        if (orderFldIdsVec.size() != pView->GetOrderFields()->Count())
        {
            return E_DBSHEET_MODIFY_ORDER_FIELDS_NOT_MATCH_ALL_FIELDS;
        }
        if (orderFldIdsVec.size() != 0)
        {
            pView->ResetOrderFields(orderFldIdsVec.data(), orderFldIdsVec.size(), FALSE);
            IDbFieldsManager *pFieldsMgr = pView->GetFieldsManager();
            if (pView->GetType() != et_DBSheetView_Form && pFieldsMgr->GetPrimaryField() != orderFldIdsVec[0])
            {
                HRESULT hr = pFieldsMgr->SetPrimaryField(orderFldIdsVec[0]);
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    if (obj.has("fieldsAttribute"))
    {
        VAR_OBJ_EXPECT_ARRAY_FOR_TYPE(obj, "fieldsAttribute", binary_wo::typeStruct);
        binary_wo::VarObj fieldsAttribute = obj.get_s("fieldsAttribute");
        for (int i = 0, cnt = fieldsAttribute.arrayLength_s(); i < cnt; i++)
        {
            binary_wo::VarObj item = fieldsAttribute.at_s(i);
            VAR_OBJ_EXPECT_STRING(item, "field");
            WebStr fieldName = item.field_str("field");
            ks_stdptr<IDbField> spField;
            HRESULT hr = fieldRetriever.GetField(fieldName, &spField);
            if (FAILED(hr))
                return hr;
            
            VAR_OBJ_EXPECT_BOOL(item, "hidden");
            bool isHidden = item.field_bool("hidden");
            hr = pView->SetFieldsHidden(spField->GetID(), isHidden);
            if (FAILED(hr))
                return hr;
        }
    }

    switch (pView->GetType())
    {
        case et_DBSheetView_Grid:
        {
            ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = pView;
            if (obj.has("height"))
            {
                VAR_OBJ_EXPECT_NUMERIC(obj, "height");
                int height = obj.field_int32("height");
                HRESULT hr = spDbSheetViewGrid->SetRecordsHeight(height);
                if (FAILED(hr))
                    return hr;
            }
            if (obj.has("widths"))
            {
                VAR_OBJ_EXPECT_ARRAY(obj, "widths");
                binary_wo::VarObj widths = obj.get_s("widths");
                for (int i = 0, cnt = widths.arrayLength_s(); i < cnt; i++)
                {
                    binary_wo::VarObj item = widths.at_s(i);
                    VAR_OBJ_EXPECT_STRING(item, "field");
                    WebStr fieldName = item.field_str("field");
                    ks_stdptr<IDbField> spField;
                    HRESULT hr = fieldRetriever.GetField(fieldName, &spField);
                    if (FAILED(hr))
                        return hr;

                    VAR_OBJ_EXPECT_NUMERIC(item, "width");
                    int twip = item.field_int32("width");
                    if (twip > 0)
                    {
                        hr = spDbSheetViewGrid->SetFieldWidth(spField->GetID(), twip, FALSE);
                        if (FAILED(hr))
                            return hr;
                    }
                }
            }
            break;
        }
        case et_DBSheetView_Kanban:
        {
            ks_stdptr<IDBSheetView_Kanban> spDBSheetViewKanban = pView;
            if (obj.has("coverField"))
            {
                VAR_OBJ_EXPECT_STRING(obj, "coverField");
                ks_stdptr<IDbField> spField;
                HRESULT hr = fieldRetriever.GetField(obj.field_str("coverField"), &spField);
                if (FAILED(hr))
                    return hr;

                hr = spDBSheetViewKanban->SetCoverField(spField->GetID());
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case et_DBSheetView_Gallery:
        {
            ks_stdptr<IDBSheetView_Gallery> spDBSheetViewGallery = pView;
            if (obj.has("coverField"))
            {

                VAR_OBJ_EXPECT_STRING(obj, "coverField");
                ks_stdptr<IDbField> spField;
                HRESULT hr = fieldRetriever.GetField(obj.field_str("coverField"), &spField);
                if (FAILED(hr))
                    return hr;

                hr = spDBSheetViewGallery->SetCoverField(spField->GetID());
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case et_DBSheetView_Gantt:
        {
            ks_stdptr<IDBSheetView_Gantt> spDBSheetViewGantt = pView;
            if (obj.has("beginField"))
            {

                VAR_OBJ_EXPECT_STRING(obj, "beginField");
                ks_stdptr<IDbField> spField;
                HRESULT hr = fieldRetriever.GetField(obj.field_str("beginField"), &spField);
                if (FAILED(hr))
                    return hr;

                hr = spDBSheetViewGantt->SetBeginField(spField->GetID());
                if (FAILED(hr))
                    return hr;
            }
            if (obj.has("endField"))
            {

                VAR_OBJ_EXPECT_STRING(obj, "endField");
                ks_stdptr<IDbField> spField;
                HRESULT hr = fieldRetriever.GetField(obj.field_str("endField"), &spField);
                if (FAILED(hr))
                    return hr;

                hr = spDBSheetViewGantt->SetEndField(spField->GetID());
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
    }

    return S_OK;
}

HRESULT UpdateViewSetting(binary_wo::VarObj obj, bool bPreferId, IDBSheetView *pView, ks_wstring* pErrorMsg)
{
    DbSheetFieldRetriever fieldRetriever;
    fieldRetriever.Reset(pView->GetFieldsManager(), bPreferId);

    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();

    IDBRecordsOrderManager* pMgr = pView->GetMutableOrderManager();
    if (obj.has("group"))
    {
        VAR_OBJ_EXPECT_STRUCT(obj, "group");
        binary_wo::VarObj vGroup = obj.get_s("group");

        if (vGroup.has("conditions"))
        {
            VAR_OBJ_EXPECT_ARRAY(vGroup, "conditions");
            binary_wo::VarObj vConditions = vGroup.get_s("conditions");
            for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = vConditions.at_s(i);

                ks_stdptr<IDbField> spField;
                VAR_OBJ_EXPECT_STRING(item, "field");
                HRESULT hr = fieldRetriever.GetField(item.field_str("field"), &spField);
                if (FAILED(hr))
                    return hr;

                ks_stdptr<IDBRecordsOrderCondition> spCondition;
                hr = pMgr->GetGroupConditionById(spField->GetID(), &spCondition);
                if (FAILED(hr))
                {
                    hr = pMgr->AddGroupCondition(spField->GetID(), &spCondition);
                    if (FAILED(hr))
                        return hr;
                }

                KDBGroupUnit unit = DBGU_Text;
                if (item.has("unit"))
                {
                    VAR_OBJ_EXPECT_STRING(item, "unit");
                    DECODE_STR_OBJ(item, "unit", GroupUnit, unit, pErrorMsg);
                }
                hr = spCondition->SetUnit(unit);
                if (FAILED(hr))
                    return hr;

                VAR_OBJ_EXPECT_BOOL(item, "isAscending")
                hr = spCondition->SetAscending(alg::bool2BOOL(item.field_bool("isAscending")));
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    if (obj.has("sort"))
    {
        VAR_OBJ_EXPECT_STRUCT(obj, "sort");
        binary_wo::VarObj vSort = obj.get_s("sort");

        if (vSort.has("autoSort"))
        {
            VAR_OBJ_EXPECT_BOOL(vSort, "autoSort");
            HRESULT hr = pMgr->SetAutoSort(alg::bool2BOOL(vSort.field_bool("autoSort")));
            if (FAILED(hr))
                return hr;
        }

        if (vSort.has("conditions"))
        {
            VAR_OBJ_EXPECT_ARRAY(vSort, "conditions");
            binary_wo::VarObj vConditions = vSort.get_s("conditions");
            for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = vConditions.at_s(i);

                ks_stdptr<IDbField> spField;
                VAR_OBJ_EXPECT_STRING(item, "field");
                HRESULT hr = fieldRetriever.GetField(item.field_str("field"), &spField);
                if (FAILED(hr))
                    return hr;

                ks_stdptr<IDBRecordsOrderCondition> spCondition;
                hr = pMgr->GetSortConditionById(spField->GetID(), &spCondition);
                if (FAILED(hr))
                {
                    hr = pMgr->AddSortCondition(spField->GetID(), &spCondition);
                    if (FAILED(hr))
                        return hr;
                }

                VAR_OBJ_EXPECT_BOOL(item, "isAscending")
                hr = spCondition->SetAscending(alg::bool2BOOL(item.field_bool("isAscending")));
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    if (obj.has("filter"))
    {
        IDbFilter* pFilter = pView->GetMutableFilter();
        VAR_OBJ_EXPECT_STRUCT(obj, "filter");
        binary_wo::VarObj vFilter = obj.get_s("filter");

        if (vFilter.has("mode"))
        {
            KDbFilterOpType op = DBFOT_And;
            DECODE_STR_OBJ(vFilter, "mode", KDbFilterOpType, op, pErrorMsg);
            HRESULT hr = pFilter->SetOperator(op);
            if (FAILED(hr))
                return hr;
        }

        if (vFilter.has("conditions"))
        {
            VAR_OBJ_EXPECT_ARRAY(vFilter, "conditions");
            binary_wo::VarObj vConditions = vFilter.get_s("conditions");
            IBook *pBook = pView->GetSheetOp()->GetBook();
            for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = vConditions.at_s(i);

                ks_stdptr<IDbField> spField;
                VAR_OBJ_EXPECT_STRING(item, "field");
                HRESULT hr = fieldRetriever.GetField(item.field_str("field"), &spField);
                if (FAILED(hr))
                    return hr;

                ks_stdptr<IDbFieldFilter> spFieldFilter;  
                if(item.has("filterId"))
                {
                    EtDbId filterId = INVALID_ID;
                    _appcore_GainDbSheetContext()->DecodeEtDbId(item.field_str("filterId"), &filterId);
                    hr = pFilter->GetFieldFilter(filterId, &spFieldFilter);
                }
                else
                    hr = pFilter->GetFieldFilterByFieldId(spField->GetID(), &spFieldFilter);
                
                if (FAILED(hr))
                {
                    ASSERT(hr == E_DBSHEET_FILTER_NOT_FOUND);
                    ASSERT(spFieldFilter == nullptr);;
                    hr = pFilter->AddFilter(spField->GetID(), &spFieldFilter);
                    if (FAILED(hr))
                        return hr;
                }

                ks_stdptr<IDbFilterCriteria> spCriteria;
                VAR_OBJ_EXPECT_STRUCT(item, "criteria");
                hr = CreateDbFilterCriteria(item.get("criteria"), pBook, &spCriteria, true);
                if (FAILED(hr))
                    return hr;

                hr = spFieldFilter->SetCriteria(spCriteria);
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    if (obj.has("statistics"))
    {
        IDBStatisticOptions *pStatOp = pView->GetMutableStatisticOptions();
        VAR_OBJ_EXPECT_STRUCT(obj, "statistics");
        binary_wo::VarObj vStat = obj.get_s("statistics");

        if (vStat.has("conditions"))
        {
            VAR_OBJ_EXPECT_ARRAY(vStat, "conditions");
            binary_wo::VarObj vConditions = vStat.get_s("conditions");
            for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = vConditions.at_s(i);

                ks_stdptr<IDbField> spField;
                VAR_OBJ_EXPECT_STRING(item, "field");
                HRESULT hr = fieldRetriever.GetField(item.field_str("field"), &spField);
                if (FAILED(hr))
                    return hr;

                ET_DBSheet_StatisticOption option = DBSSO_null;
                VAR_OBJ_EXPECT_STRING(item, "options");
                DECODE_STR_OBJ(item, "options", StatisticOption, option, pErrorMsg);
                hr = pStatOp->SetStatistic(spField->GetID(), option);
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    return S_OK;
}

HRESULT RemoveViewSetting(binary_wo::VarObj obj, bool bPreferId, IDBSheetView *pView)
{
    DbSheetFieldRetriever fieldRetriever;
    fieldRetriever.Reset(pView->GetFieldsManager(), bPreferId);

    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();

    IDBRecordsOrderManager* pMgr = pView->GetMutableOrderManager();
    if (obj.has("group"))
    {
        VAR_OBJ_EXPECT_STRUCT(obj, "group");
        binary_wo::VarObj vGroup = obj.get_s("group");

        if (vGroup.has("conditions"))
        {
            VAR_OBJ_EXPECT_ARRAY(vGroup, "conditions");
            binary_wo::VarObj vConditions = vGroup.get_s("conditions");
            for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = vConditions.at_s(i);

                ks_stdptr<IDbField> spField;
                VAR_OBJ_EXPECT_STRING(item, "field");
                HRESULT hr = fieldRetriever.GetField(item.field_str("field"), &spField);
                if (FAILED(hr))
                    return hr;

                hr = pMgr->RemoveGroupCondition(spField->GetID());
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    if (obj.has("sort"))
    {
        VAR_OBJ_EXPECT_STRUCT(obj, "sort");
        binary_wo::VarObj vSort = obj.get_s("sort");

        if (vSort.has("conditions"))
        {
            VAR_OBJ_EXPECT_ARRAY(vSort, "conditions");
            binary_wo::VarObj vConditions = vSort.get_s("conditions");
            for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = vConditions.at_s(i);

                ks_stdptr<IDbField> spField;
                VAR_OBJ_EXPECT_STRING(item, "field");
                HRESULT hr = fieldRetriever.GetField(item.field_str("field"), &spField);
                if (FAILED(hr))
                    return hr;

                hr = pMgr->RemoveSortCondition(spField->GetID());
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    if (obj.has("filter"))
    {
        IDbFilter* pFilter = pView->GetMutableFilter();
        VAR_OBJ_EXPECT_STRUCT(obj, "filter");
        binary_wo::VarObj vFilter = obj.get_s("filter");

        if (vFilter.has("conditions"))
        {
            VAR_OBJ_EXPECT_ARRAY(vFilter, "conditions");
            binary_wo::VarObj vConditions = vFilter.get_s("conditions");
            for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = vConditions.at_s(i);

                ks_stdptr<IDbField> spField;
                VAR_OBJ_EXPECT_STRING(item, "field");
                HRESULT hr = fieldRetriever.GetField(item.field_str("field"), &spField);
                if (FAILED(hr))
                    return hr;

                ks_stdptr<IDbFieldFilter> spFieldFilter;
                if(item.has("filterId"))
                {
                    EtDbId filterId = INVALID_ID;
                    _appcore_GainDbSheetContext()->DecodeEtDbId(item.field_str("filterId"), &filterId);
                    hr = pFilter->GetFieldFilter(filterId, &spFieldFilter);
                }
                else
                    hr = pFilter->GetFieldFilterByFieldId(spField->GetID(), &spFieldFilter);

                if (FAILED(hr))
                    return hr;
                hr = spFieldFilter->Remove();
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    if (obj.has("statistics"))
    {
        IDBStatisticOptions *pStatOp = pView->GetMutableStatisticOptions();
        VAR_OBJ_EXPECT_STRUCT(obj, "statistics");
        binary_wo::VarObj vStat = obj.get_s("statistics");

        if (vStat.has("conditions"))
        {
            VAR_OBJ_EXPECT_ARRAY(vStat, "conditions");
            binary_wo::VarObj vConditions = vStat.get_s("conditions");
            for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = vConditions.at_s(i);

                ks_stdptr<IDbField> spField;
                VAR_OBJ_EXPECT_STRING(item, "field");
                HRESULT hr = fieldRetriever.GetField(item.field_str("field"), &spField);
                if (FAILED(hr))
                    return hr;

                hr = pStatOp->RemoveStatistic(spField->GetID());
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    return S_OK;
}

HRESULT DbSheetValueSerialiser::parseFields(VarObj param)
{
    m_customFields = false;
	if (false == param.has("fields"))
        return S_OK;

    VAR_OBJ_EXPECT_ARRAY(param, "fields")
    VarObj fields = param.get_s("fields");

    m_customFields = true;

    std::unordered_set<EtDbId> _s;
    for (int i = 0, cnt = fields.arrayLength_s(); i < cnt; i++)
    {
        WebStr fieldName = fields.item_str(i);
        ks_stdptr<IDbField> spField;
        m_fieldRetriever.GetField(fieldName, &spField);

        if (nullptr == spField)
            continue;
        if (_s.count(spField->GetID()))
            continue;
        _s.insert(spField->GetID());
        m_fieldIds.push_back(spField->GetID());
    }
    if (m_showPrimary)
    {
        EtDbId primaryFieldId = m_pFieldManager->GetPrimaryField();
        if (_s.find(primaryFieldId) == _s.end())
        {
            _s.insert(primaryFieldId);
            m_fieldIds.push_back(primaryFieldId);
        }
    }
    return S_OK;
}

HRESULT DbSheetValueSerialiser::parseBoolParams(VarObj param)
{
    m_bPreferId = false;
    if (param.has("preferId"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "preferId")
        m_bPreferId = param.field_bool("preferId");
    }
    if (param.has("valuePreferId"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "valuePreferId")
        m_bValuePreferId = param.field_bool("valuePreferId");
    }
    else
    {
        m_bValuePreferId = m_bPreferId;
    }
    // textValue与formattedText类似，是请求将单元格内容格式化为文本
    // 由于formattedText已有行为只针对部分字段类型生效，为了不影响已有功能，故不做修改
    m_textValue = TVT_Original;
    if (param.has("textValue"))
    {
        VAR_OBJ_EXPECT_STRING(param, "textValue")
        PCWSTR textValue = param.field_str("textValue");
        if (xstricmp(__X("Text"), textValue) == 0)
            m_textValue = TVT_Text;
        else if (xstricmp(__X("Compound"), textValue) == 0)
            m_textValue = TVT_Compound;
        else if (0 == xstricmp(__X("formulaCompound"), textValue))
            m_textValue = TVT_FormulaCompound;
    }

    m_linkValue = LinkValueType::ID;
    if (param.has("linkValue"))
    {
        VAR_OBJ_EXPECT_STRING(param, "linkValue");
        if (xstricmp(__X("all"), param.field_str("linkValue")) == 0)
            m_linkValue = LinkValueType::All;
    }

    if (param.has("formattedText"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "formattedText")
        if (param.field_bool("formattedText"))
            m_textValue = TVT_NumberToText;
    }
    m_showPrimary = false;
    if (param.has("showPrimary"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "showPrimary")
        m_showPrimary = param.field_bool("showPrimary");
    }
    m_showRecordExtraInfo = false;
    if (param.has("showRecordExtraInfo"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "showRecordExtraInfo")
        m_showRecordExtraInfo = param.field_bool("showRecordExtraInfo");
    }
    m_omitFailure = false;
    if (param.has("omitFailure"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "omitFailure")
        m_omitFailure = param.field_bool("omitFailure");
    }
    m_bIncludeAllRecIds = false;
    if (param.has("includeAllRecordIds"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "includeAllRecordIds");
        m_bIncludeAllRecIds = param.field_bool("includeAllRecordIds");
    }
    m_bServerApiSyncSheetMode = false;
    if (param.has("serverapiSyncSheetMode"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "serverapiSyncSheetMode");
        m_bServerApiSyncSheetMode = param.field_bool("serverapiSyncSheetMode");
    }
    // 同步表查询link需要序列化id和text
    if (m_bServerApiSyncSheetMode)
        m_linkValue = LinkValueType::All;
    return S_OK;
}

void DbSheetValueSerialiser::SerialiseFieldDefaultValue(IDbField* pField)
{
    ET_DbSheet_Field_Default_Value_Type defaultValType = pField->GetDefaultValType();
    PCWSTR defaultValTypeStr = nullptr;
    VS(_appcore_GainEncodeDecoder()->EncodeDbFieldDefaultValueType(defaultValType, &defaultValTypeStr));
    m_acpt->addString("defaultValueType", defaultValTypeStr);

    PCWSTR defaultVal = pField->GetDefaultVal();
    if (!defaultVal || xstrlen(defaultVal) == 0)
        return;

    ET_DbSheet_FieldType fieldType = pField->GetType();
    if (fieldType == Et_DbSheetField_Contact)
    {
        m_acpt->addKey("defaultValue");
        m_acpt->beginArray();
        if (defaultValType != DbSheet_Field_Dvt_RecordCreator)
        {
            QString contacts = krt::fromUtf16(defaultVal);
            QStringList contactList = contacts.split(",");
            for (const auto& contact: contactList)
            {
                if (!contact.isEmpty())
                    serializeContactItem(krt::utf16(contact));
            }
        }
        m_acpt->endArray();
    }
    else if (fieldType == Et_DbSheetField_MultipleSelect)
    {
        ks_stdptr<IDbSelectItemHandle> spSelectHandle;
        _db_CreateObject(CLSID_KDbSelectItemHandle, IID_IDbSelectItemHandle, (void**) &spSelectHandle);
        HRESULT hr = spSelectHandle->CreateFromCellStr(defaultVal);
        if (FAILED(hr))
            return;

        m_acpt->addKey("defaultValue");
        m_acpt->beginArray();
        for (UINT i = 0, cnt = spSelectHandle->Count(); i < cnt; ++i)
            m_acpt->addString(nullptr, spSelectHandle->ItemText(i));
        m_acpt->endArray();
    }
    else
    {
        m_acpt->addString("defaultValue", defaultVal);
    }
}

void DbSheetValueSerialiser::SerialiseFieldValueUnique(IDbField* pField)
{
    if (!pField->IsFieldTypeCanSwitchOnValUnique())
        return;
    m_acpt->addBool("uniqueValue", alg::BOOL2bool(pField->IsValueUnique()));
}

bool DbSheetValueSerialiser::IsPreferId() const
{
    return m_bPreferId;
}

std::unordered_set<EtDbId> DbSheetValueSerialiser::GetNoPermissionRecord() const
{
    ks_stdptr<IDBSheetRange> spRange;
    m_spProtectionJudgement->GetNoPermissionRecords(m_pSheet->GetStId(), &spRange);
    const EtDbId* pIds = NULL;
    UINT32 cnt = 0;
    if (spRange)
    {
        spRange->GetRecordIds(&pIds, &cnt);
    }
    return std::unordered_set<EtDbId>(pIds, pIds+cnt);
}

std::unordered_set<EtDbId> DbSheetValueSerialiser::GetNoPermissionField() const
{
    ks_stdptr<IDBSheetRange> spRange;
    m_spProtectionJudgement->GetNoPermissionFields(m_pSheet->GetStId(), &spRange);
    const EtDbId* pIds = NULL;
    UINT32 cnt = 0;
    if (spRange)
    {
        spRange->GetFieldIds(&pIds, &cnt);
    }
    return std::unordered_set<EtDbId>(pIds, pIds+cnt);
}

void SerialiseExtraFieldInfo(IDbField* pField, ISerialAcceptor* acpt, IDBSheetCtx* pDbCtx, IBook* pBook)
{
    switch (pField->GetType())
    {
        case Et_DbSheetField_SingleSelect:
        case Et_DbSheetField_MultipleSelect:
        {
            ks_stdptr<IDbField_Select> spFieldSelect = pField;
            acpt->addKey("items");
            acpt->beginArray();
            for (int i = 0, cnt = spFieldSelect->Count(); i < cnt; i++)
            {
                EtDbId id = INV_EtDbId;
                PCWSTR value = __X("");
                ARGB color = 0;
                spFieldSelect->Item(i, &value, &color, &id);
                EtDbIdStr buf;
                VS(pDbCtx->EncodeEtDbId(id, &buf));
                acpt->beginStruct();
                acpt->addString("id", buf);
                acpt->addString("value", value);
                acpt->addUint32("color", color);
                acpt->endStruct();
            }
            acpt->endArray();
            acpt->addBool("allowAddItemWhenInputting", alg::BOOL2bool(spFieldSelect->GetAllowAddItemWhenInputting()));
            acpt->addBool("autoAddItem", alg::BOOL2bool(spFieldSelect->GetAutoAddItem()));
            break;
        }
        case Et_DbSheetField_Rating:
        {
            ks_stdptr<IDbField_Rating> spFieldRating = pField;
            acpt->addUint32("max", spFieldRating->GetMaxRating());
            break;
        }
        case Et_DbSheetField_Date:
        {
             ks_stdptr<IDbField_Date> spFieldDate = pField;
            acpt->addUint32("loadLegalHoliday", spFieldDate->GetLoadLegalHoliday());
            break;
        }
        case Et_DbSheetField_Url:
        {
            ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = pField;
            acpt->addString("displayText", spFieldHyperlink->GetDisplayText());
            break;
        }
        case Et_DbSheetField_Contact:
        {
            ks_stdptr<IDbField_Contact> spFieldContact = pField;
            bool bMulti = alg::BOOL2bool(spFieldContact->GetSupportMulti());
            acpt->addBool("supportMulti", bMulti);
            acpt->addBool("multipleContacts", bMulti);
            acpt->addBool("noticeNewContact", alg::BOOL2bool(spFieldContact->GetSupportNotice()));
            break;
        }
        case Et_DbSheetField_Link:
        case Et_DbSheetField_OneWayLink:
        {
            ks_stdptr<IDbField_Link> spFieldLink = pField;
            acpt->addUint32("linkSheet", spFieldLink->GetLinkSheet());
            acpt->addBool("isSyncLink", alg::BOOL2bool(spFieldLink->IsSyncLink()));

            if (pField->GetType() == Et_DbSheetField_Link)
            {
                EtDbId fldId = spFieldLink->GetReversedLinkField();
                EtDbIdStr buf;
                VS(pDbCtx->EncodeEtDbId(fldId, &buf));
                acpt->addString("linkField", buf);
            }
            bool bMulti = alg::BOOL2bool(spFieldLink->GetSupportMultiLinks());
            acpt->addBool("supportMulti", bMulti);
            acpt->addBool("multipleLinks", bMulti);
            EtDbIdStr buf;
            VS(pDbCtx->EncodeEtDbId(spFieldLink->GetLinkView(), &buf));
            acpt->addString("linkView", buf);
            break;
        }
        case Et_DbSheetField_Lookup:
        {
            ks_stdptr<IDbField_Lookup> spLookupField = pField;
            DBLookupType dlType = spLookupField->GetLookupType();
            EtDbId fldId = spLookupField->GetLinkFieldId();
            if (fldId != INV_EtDbId)
            {
                EtDbIdStr buf;
                VS(pDbCtx->EncodeEtDbId(fldId, &buf));
                acpt->addString("linkField", buf);
            }
            fldId = spLookupField->GetLookupFieldId();
            EtDbIdStr buf;
            VS(pDbCtx->EncodeEtDbId(fldId, &buf));
            acpt->addString("lookupField", buf);
            DBLookupType lookupType = spLookupField->GetLookupType();
            if (lookupType > LT_NORMAL)
            {
                acpt->addUint32("lookupSheetId", spLookupField->GetLookupSheetId());
                acpt->addUint32("lookupType", spLookupField->GetLookupType());
            }
            ks_stdptr<IDbField> spBaseField;
            HRESULT hr = spLookupField->GetLookupBaseField(&spBaseField);
            if (SUCCEEDED(hr))
            {
                ET_DbSheet_FieldType baseType = spBaseField->GetType();
                PCWSTR baseTypeStr = nullptr;
                VS(_appcore_GainEncodeDecoder()->EncodeFieldType(baseType, &baseTypeStr));
                acpt->addString("baseType", baseTypeStr);
                PCWSTR valueTypeStr = nullptr;
                // TODO: 新建修改完字段立刻进来拿到的valueType是不准的, 目前流程上是要等到update后才进行推导
                VS(_appcore_GainEncodeDecoder()->EncodeDbFieldValueType(pField->GetValueType(), &valueTypeStr));
                if (valueTypeStr)
                    acpt->addString("valueType", valueTypeStr);
            }

            ET_DbSheet_Lookup_Function aggregation = spLookupField->GetLookupFunction();
            PCWSTR strAggregation = nullptr;
            VS(_appcore_GainEncodeDecoder()->EncodeDbLookupFunction(aggregation, &strAggregation));
            acpt->addString("aggregation", strAggregation);

            break;
        }
        case Et_DbSheetField_Attachment:
        {
            ks_stdptr<IDbField_Attachment> spFieldAttachment = pField;
            ks_wstring type(spFieldAttachment->GetAttachmentType());
            if (!type.empty())
            {
                acpt->addString("attachmentType", type.c_str());
            }
            acpt->addBool("onlyUploadByCamera", spFieldAttachment->GetOnlyUploadByCamera());
            PCWSTR displayStyle = __X("");
            _appcore_GainEncodeDecoder()->EncodeDbAttachmentDisplayStyle(
                spFieldAttachment->GetAttachmentDisplayStyle(), &displayStyle);
            acpt->addString("displayStyle", displayStyle);
            break;
        }
        case Et_DbSheetField_Address:
        {
            ks_stdptr<IDbField_Cascade> spFieldAddress = pField;
            acpt->addUint32("addressLevel", spFieldAddress->GetCascadeLevel());
            acpt->addBool("detailedAddress", alg::BOOL2bool(spFieldAddress->GetWithDetailedInfo()));
            acpt->addBool("withDetailedAddress", alg::BOOL2bool(spFieldAddress->GetWithDetailedInfo()));
            IDbCascadeHandle* pPresetAddressHandle = spFieldAddress->GetDefValueHandle();
            if(pPresetAddressHandle)
            {
                acpt->addKey("presetAddress");
                pPresetAddressHandle->SerialContent(acpt, false);
            }
            break;
        }
        case Et_DbSheetField_Department:
        {
            ks_stdptr<IDbField_Cascade> spFieldDepartment = pField;
            acpt->addBool("supportMulti", spFieldDepartment->GetMultiValue());
            acpt->addBool("displayAllLevel", spFieldDepartment->GetDisplayAllLevel());
            break;
        }
        case Et_DbSheetField_Cascade:
        {
            ks_stdptr<IDbField_Cascade> spFieldCascade = pField;
            acpt->addBool("displayAllLevel", alg::BOOL2bool(spFieldCascade->GetDisplayAllLevel()));
            acpt->addKey("cascadeTitle");
            acpt->beginArray();
            IDbCascadeHandle* pTitleValueHandle = spFieldCascade->GetCascadeTitleHandle();
            if(pTitleValueHandle && pTitleValueHandle->GetCascadeLevel())
            {         
                for (UINT i = 0, count = pTitleValueHandle->GetCascadeLevel(); i < count; ++i)
                    acpt->addString(nullptr, pTitleValueHandle->GetCascadeItemValue(i));
            }
            acpt->endArray();

            class KDbCascadeOptionEnum : public IDbCascadeOptionEnum
            {
            public:
                KDbCascadeOptionEnum(ISerialAcceptor *acpt,  IDBSheetCtx* pCtx): m_acpt(acpt), m_pDbCtx(pCtx){}
                STDPROC TravelNode(EtDbId id, PCWSTR value, BOOL isLeafNode) override
                {
                    m_acpt->beginStruct();
                    
                    EtDbIdStr buf;
                    VS(m_pDbCtx->EncodeEtDbId(id, &buf));
                    m_acpt->addString("id", buf);
                    m_acpt->addString("value", value);
                    if (isLeafNode)
                    {
                         m_acpt->endStruct();
                    }                     
                    else
                    {
                        m_acpt->addKey("children");
                        m_acpt->beginArray();
                    }

                    return S_OK;
                }

                STDPROC DoAfterLeafNodeTraveled() override
                {
                    m_acpt->endArray();
                    m_acpt->endStruct();
                    return S_OK;
                }

            private:
               ISerialAcceptor* m_acpt;
               IDBSheetCtx* m_pDbCtx;
            } enumer(acpt, pDbCtx);

            acpt->addKey("allCascadeOption");
            acpt->beginArray();
            spFieldCascade->EnumAllCascadeOption(&enumer);
            acpt->endArray();
            break;
        }
        case Et_DbSheetField_Formula:
        {
            ks_stdptr<IDbField_Formula> spFieldFormula = pField;
            PCWSTR valueTypeStr = nullptr;
            // TODO: 新建修改完字段立刻进来拿到的valueType是不准的, 目前流程上是要等到update后才进行推导
            VS(_appcore_GainEncodeDecoder()->EncodeDbFieldValueType(pField->GetValueType(), &valueTypeStr));
            if (valueTypeStr)
                acpt->addString("valueType", valueTypeStr);
            // 新写法的表公式支持跨列跨表引用，@符号表示当前行，[]表示整理列
            // 导出时，本应该兼容，但评估后成本太高，且兼容多发生在灰度版本。
            // 新版本增量同步cpfExNormal反编译公式，旧版本只能用cpfDbThisRow来编译，反之亦然。
            // 这里的应对措施就是什么都不做，等用户发现公式有问题的时候，全量同步一次即可。
            // 全量同步的话要走IO，IO已对此做出兼容，撑过灰度就可以了
            ks_bstr formula;
            VS(spFieldFormula->GetFormula(&formula, DB_FML_CPL_TYPE::FML_CPL_ET_COMPATIBLE));
            if (!formula.empty())
                acpt->addString("formula", formula.c_str());
            else
                acpt->addString("formula", __X(""));
            acpt->addBool("showPercentAsProgress", alg::BOOL2bool(spFieldFormula->GetShowPercentAsProgress()));
            // 跨表引用是要回落成文本的。
            // 一是表名同步后很可能在target表里找不到对应的sheet或源sheet已被改名。
            // 二是用户很可能没将被引用sheet一起同步。
            ks_stdptr<ITokenVectorInstant> spTokenVector;
            spFieldFormula->GetFormula(&spTokenVector);
            int size = 0;
            if (spTokenVector)
                spTokenVector->GetSize(&size);
            bool bCrossSheetFormula = false;
            std::unordered_set<IDX> sheetIdxs;
            acpt->addKey("crossSheetIds");
                acpt->beginArray();
                for (int i = 0; i < size; i++)
                {
                    alg::const_token_assist token;
                    spTokenVector->GetItem(i, &token);
                    DWORD tokenType = alg::GetExecTokenMajorType(token);
                    IDX nameId {alg::STREF_INV_NAME};
                    switch (tokenType)
                    {
                        case alg::ETP_TABREF:
                        {
                            alg::const_tabref_token_assist tabRef(token);
                            nameId = tabRef.get_name_id();
                            break;
                        }
                        case alg::ETP_DBFIELDREF:
                        {
                            alg::const_dbfieldref_token_assist dbFldRef(token);
                            nameId = dbFldRef.get_name_id();
                            break;
                        }
                        default:
                            continue;
                    }
                    RANGE rg(pBook->GetBMP());
                    pBook->LeakOperator()->GetTableRange(nameId, &rg);
                    IDX refSheetIdx = rg.SheetFrom();
                    IDX sheetIdx = INVALIDIDX;
                    pBook->STSheetToRTSheet(pField->GetDbSheetData()->GetSheetId(), &sheetIdx);
                    if (sheetIdx == INVALIDIDX || refSheetIdx == INVALIDIDX)
                        continue;

                    if (sheetIdx != refSheetIdx && sheetIdxs.find(refSheetIdx) == sheetIdxs.end())
                    {
                        sheetIdxs.emplace(refSheetIdx);
                        UINT sheetStId = 0;
                        VS(pBook->RTSheetToSTSheet(refSheetIdx, &sheetStId));
                        acpt->addUint32(nullptr, sheetStId);
                        bCrossSheetFormula = true;
                    }
                }
                acpt->endArray();
            acpt->addBool("isCrossSheetFormula", bCrossSheetFormula);
            break;
        }
        case Et_DbSheetField_LastModifiedTime:
        case Et_DbSheetField_LastModifiedBy:
        {
            ks_stdptr<IDbField_WatchedField> spWatchedField = pField;

            acpt->addBool("watchedAll", spWatchedField->IsWatchedAll());
            if (!spWatchedField->IsWatchedAll())
            {
                acpt->addKey("watchedField");
                acpt->beginArray();
                UINT count = spWatchedField->GetWatchedFieldCount();
                std::vector<EtDbId> dbIds;
                dbIds.resize(count);
                spWatchedField->GetWatchedFieldId(dbIds.data(), count);
                for (UINT i = 0; i < count; i++)
                {
                    EtDbIdStr buf;
                    VS(pDbCtx->EncodeEtDbId(dbIds[i], &buf));
                    acpt->addString(nullptr, buf);
                }
                acpt->endArray();
            }
            break;
        }
        case Et_DbSheetField_AutoNumber:
        {
            ks_stdptr<IDbtBookCtx> spDbBookCtx;
            VS(pField->GetDbSheetData()->GetBook()->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbBookCtx));
            UINT nextNumber = 1;
            spDbBookCtx->PeekNextAutoNumber(pField, &nextNumber);
            acpt->addUint32("nextNumber", nextNumber);
            break;
        }
        case Et_DbSheetField_Button:
        {
            ks_stdptr<IDbField_Button> spFieldButton = pField;
            acpt->addString("icon", spFieldButton->GetButtonIcon());
            acpt->addString("text", spFieldButton->GetButtonText());
            acpt->addString("successText", spFieldButton->GetSuccessText());
            acpt->addUint32("textColor", spFieldButton->GetTextColor());
            acpt->addUint32("backgroundColor", spFieldButton->GetBackgroundColor());
            break;
        }
        case Et_DbSheetField_BarCode:
        {
            ks_stdptr<IDbField_BarCode> spFieldBarCode = pField;
            acpt->addBool("onlyScanByCamera", spFieldBarCode->GetOnlyScanByCamera());
            break;
        }
        default:
            break;
    }
}

void SerialiseSheetInfo(IKWorkbook* pWorkbook, IKWorksheet* pWorksheet, ISerialAcceptor* pAcpt, ks_wstring* pErrorMsg, const VarObj* pParam)
{
    ISheet* pSheet = pWorksheet->GetSheet();
    if (pSheet->IsDbSheet())
    {
        pAcpt->addString("sheetType", __X("xlEtDataBaseSheet"));
        DbSheetValueSerialiser dbSerialiser(pWorkbook, pWorksheet, pAcpt, pParam ? *pParam : VarObj{}, pErrorMsg);
        VS(dbSerialiser.SerialiseSheet());
    }
    else if (pSheet->IsDbDashBoardSheet())
    {
        PCWSTR sheetName = nullptr;
        pSheet->GetName(&sheetName);
        pAcpt->addString("name", sheetName);
        pAcpt->addUint32("id", pSheet->GetStId());
        ks_stdptr<IDBDashBoardDataOp> spDashBoardDataOp;
        VS(pSheet->GetExtDataItem(edSheetDbDashBoardOp, (IUnknown**)&spDashBoardDataOp));
        pAcpt->addString("description", spDashBoardDataOp->GetSheetDescription());
        pAcpt->addString("icon", spDashBoardDataOp->GetSheetIcon());
        pAcpt->addString("sheetType", __X("xlDbDashBoardSheet"));
        std::unordered_set<UINT> sheetIds;
        ks_stdptr<IDBChartStatisticMgr> spDbChartStatisticMgr;
        VS(DbSheet::GetDBChartStatisticMgr(pSheet, &spDbChartStatisticMgr));
        EtDbIdx size = spDbChartStatisticMgr->GetSize();
        for (EtDbIdx i = 0; i < size; ++i)
        {
            ks_stdptr<IDBChartStatisticModule> spDbDashBoardModule;
            spDbChartStatisticMgr->GetItemAt(i, &spDbDashBoardModule);
            if (spDbDashBoardModule)
            {
                UINT sheetId = spDbDashBoardModule->GetDataSourceId();
                if (sheetId && sheetIds.find(sheetId) == sheetIds.end())
                    sheetIds.emplace(sheetId);
            }
        }
        IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetWebExtensionMgr();
        UINT webextensionCount = 0;
        pWebExtensionMgr->GetWebExtensionCount(pSheet, &webextensionCount);
        for (int i = 0; i < webextensionCount; ++i)
        {
            ks_stdptr<IKWebExtension> spWebExtension;
            HRESULT hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
            if (FAILED(hr))
                continue;

            if (spWebExtension->GetWebShapeType() != WET_DbView)
                continue;

            ks_stdptr<IEtWebExtension_View> spWebExtView = spWebExtension;
            UINT srcSheetId = spWebExtView->GetSheetId();
            if (srcSheetId && sheetIds.find(srcSheetId) == sheetIds.end())
                sheetIds.emplace(srcSheetId);
        }

        if (sheetIds.size() > 0)
        {
            sa::Leave dataSourceIds(sa::enterArray(pAcpt, "dataSourceIds"));
            for (auto sheetId : sheetIds)
                pAcpt->addUint32(nullptr, sheetId);
        }
    }
    else if (pSheet->IsFpSheet())
    {
        PCWSTR sheetName = nullptr;
        pSheet->GetName(&sheetName);
        pAcpt->addString("name", sheetName);
        pAcpt->addUint32("id", pSheet->GetStId());
        ks_stdptr<IFPSheetData> spFpSheetData;
        VS(pSheet->GetExtDataItem(edSheetFpData, (IUnknown**)&spFpSheetData));
        pAcpt->addString("icon", spFpSheetData->GetIcon());
        pAcpt->addString("sheetType", __X("xlEtFlexPaperSheet"));
    }
}

HRESULT SerialiseWebExtension(UINT sheetId, IKWebExtension* pWebExtension, ISerialAcceptor* pAcpt, DBSheetCommonHelper* pHelper)
{
    auto webExtensionType = static_cast<WebExtType>(pWebExtension->GetWebShapeType());
    PCWSTR typeStr = __X("");
    VS(_webextension_GainEncodeDecoder()->EncodeWebExtType(webExtensionType, &typeStr));
    pAcpt->addString("webExtensionType", typeStr);
    pAcpt->addString("webExtensionKey", pWebExtension->GetWebExtensionKey());
    pAcpt->addKey("properties");
    pAcpt->beginStruct();
    ks_stdptr<IKWebExtensionPropertyEnum> spEnum;
    if (SUCCEEDED(pWebExtension->GetPropertyEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
    {
        do
        {
            QString key = krt::fromUtf16(spEnum->GetCurKey());
            PCWSTR value = spEnum->GetCurValue();
            if (!key.isEmpty() && value)
            {
                pAcpt->addKey(key.toUtf8(), true);
                pAcpt->addString(nullptr, value);
            }
        }while (SUCCEEDED(spEnum->Next()));
    }
    pAcpt->endStruct();

    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    auto dataSourceType = spDataSourceHost->GetDataSourceType();
    PCWSTR dataSourceTypeStr = __X("");
    VS(_webextension_GainEncodeDecoder()->EncodeWebExtensionDataSourceType(dataSourceType, &dataSourceTypeStr));
    pAcpt->addString("dataSourceType", dataSourceTypeStr);

    if (!pWebExtension->IsDatasourceSupportingDashboardModule() || dataSourceType != dst_dbTable)
        return S_OK;

    EtDbId moduleId = INV_EtDbId;
    HRESULT hr = DbDashboard::GetDbChartStatisticModuleId(pWebExtension, moduleId);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDBChartStatisticMgr> spDbChartStatisticMgr;
    hr = pHelper->GetDBChartStatisticMgr(sheetId, &spDbChartStatisticMgr);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDBChartStatisticModule> spDbChartStatisticModule;
    hr = spDbChartStatisticMgr->GetItemById(moduleId, &spDbChartStatisticModule);
    if (FAILED(hr))
        return hr;

    if (webExtensionType == WET_DbPlugin)
    {
        pAcpt->addKey("pluginConfig");
        pAcpt->beginStruct();
        SerialisePluginConfig(spDbChartStatisticModule, pWebExtension, pAcpt, pHelper);
        pAcpt->endStruct();
    }
    else
    {
        pAcpt->addKey("statisticsModule");
        pAcpt->beginStruct();
        SerialiseChartStatisticModule(spDbChartStatisticModule, pAcpt, pHelper);
        pAcpt->endStruct();
    }
    return S_OK;
}

void SerialiseChartStatisticModule(IDBChartStatisticModule* pDbChartStatisticModule, ISerialAcceptor* pAcpt, DBSheetCommonHelper* pHelper)
{
    pAcpt->addUint32("dataSourceSheetId", pDbChartStatisticModule->GetDataSourceId());
    pHelper->AcceptorAddEtDbId(pAcpt, "id", pDbChartStatisticModule->GetId());
    pHelper->AcceptorAddEtDbId(pAcpt, "dimensionFieldId", pDbChartStatisticModule->GetDimensionId());
    pHelper->AcceptorAddEtDbId(pAcpt, "groupFieldId", pDbChartStatisticModule->GetGroupConditionId());
    ET_DBSheet_ChartSortType sortType = pDbChartStatisticModule->GetSortType();
    // 表单不再使用该字段时才可将其移除
    pAcpt->addBool("sortByRow", sortType == DbSheet_Chart_ST_SortByHorizontalAxis);
    PCWSTR sortTypeStr = __X("");
    VS(_appcore_GainEncodeDecoder()->EncodeDbChartSortType(sortType, &sortTypeStr));
    pAcpt->addString("sortType", sortTypeStr);
    pAcpt->addBool("ascending", pDbChartStatisticModule->GetSortAscending());
    pAcpt->addBool("onlyCountDimension", pDbChartStatisticModule->GetOnlyCountDimension());
}

void SerialisePluginConfig(IDBChartStatisticModule* pDbChartStatisticModule, IKWebExtension* pWebExtension, ISerialAcceptor* pAcpt, DBSheetCommonHelper* pHelper)
{
    PCWSTR customConfig = nullptr;
    pWebExtension->GetProperty(__X("plugin-custom-kv"), &customConfig);
    pAcpt->addString("customConfig", customConfig ? customConfig : __X(""));

    {
        sa::Leave dataConditions(sa::enterArray(pAcpt, "dataConditions"));
        UINT dataSourceId = pDbChartStatisticModule->GetDataSourceId();
        if (dataSourceId == 0)
            return;

        {
            sa::Leave dataCondition(sa::enterStruct(pAcpt, nullptr));
            pAcpt->addUint32("sheetId", dataSourceId);
            SerialiseDataConditionDataRange(pDbChartStatisticModule, pAcpt, pHelper);
            SerialiseDataConditionGroups(pDbChartStatisticModule, pAcpt, pHelper);
            SerialiseDataConditionSeries(pDbChartStatisticModule, pAcpt, pHelper);
        }
    }
}

void SerialiseDataConditionDataRange(IDBChartStatisticModule* pDbChartStatisticModule, ISerialAcceptor* pAcpt, DBSheetCommonHelper* pHelper)
{
    IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
    sa::Leave dataRange(sa::enterStruct(pAcpt, "dataRange"));
    const IDbFilter* pDbFilter = pDbChartStatisticModule->GetConstFilter();
    KDbFilterOpType filterOp = pDbFilter->GetOperator();
    PCWSTR filterOpStr = nullptr;
    pDecoder->EncodeKDbFilterOpType(filterOp, &filterOpStr);
    pAcpt->addString("filtersOp", filterOpStr);
    {
        sa::Leave filterInfo(sa::enterStruct(pAcpt, "filterInfo"));
        for (int i = 0, filterCnt = pDbFilter->GetFiltersCount(); i < filterCnt; ++i)
        {
            const IDbFieldFilter* pFieldFilter = nullptr;
            pDbChartStatisticModule->GetConstFilter()->GetFilter(i, &pFieldFilter);
            if (!pFieldFilter)
                continue;

            pHelper->AcceptorAddEtDbId(pAcpt, "fieldId", pFieldFilter->GetFieldId());
            {
                sa::Leave criteria(sa::enterStruct(pAcpt, "criteria"));
                const IDbFilterCriteria* pFilterCriteria = pFieldFilter->GetCriteria();
                if (!pFilterCriteria)
                    continue;

                KDbFilterCriteriaOpType criteriaOpType = pFilterCriteria->GetCriteriaOp();
                PCWSTR criteriaOpTypeStr = nullptr;
                pDecoder->EncodeKDbFilterCriteriaOpType(criteriaOpType, &criteriaOpTypeStr);
                pAcpt->addString("op", criteriaOpTypeStr);
                sa::Leave values(sa::enterArray(pAcpt, "values"));
                for (int j = 0, valueCnt = pFilterCriteria->GetValuesCount(); j < valueCnt; ++j)
                {
                    sa::Leave value(sa::enterStruct(pAcpt, nullptr));
                    auto pValue = const_cast<IDbFcValueBase*>(pFilterCriteria->GetValue(j));
                    SerialiseFilterCriteriaValue(pValue, pAcpt);
                }
            }
        }
    }
}

void SerialiseDataConditionGroups(IDBChartStatisticModule* pDbChartStatisticModule, ISerialAcceptor* pAcpt, DBSheetCommonHelper* pHelper)
{
    sa::Leave groups(sa::enterArray(pAcpt, "groups"));
    {
        const IDBRecordsOrderManager* pMgr = pDbChartStatisticModule->GetConstOrderManager();
        for (int i = 0, length = pMgr->GetGroupConditionCount(); i < length; ++i)
        {
            sa::Leave group(sa::enterStruct(pAcpt, nullptr));
            const IDBRecordsOrderCondition* pCondition = pMgr->GetGroupCondition(0);
            pHelper->AcceptorAddEtDbId(pAcpt, "fieldId", pCondition->GetKeyFieldId());

            PCWSTR unitStr = nullptr;
            _appcore_GainEncodeDecoder()->EncodeGroupUnit(pCondition->GetUnit(), &unitStr);
            pAcpt->addString("groupUnit", unitStr);

            PCWSTR groupModeStr = pCondition->IsSplitMultiple() ? __X("Enumerated") : __X("Integrated");
            pAcpt->addString("groupMode", groupModeStr);

            {
                sa::Leave sort(sa::enterStruct(pAcpt, "sort"));
                PCWSTR sortTypeStr = nullptr;
                ET_DBSheet_ChartSortType sortType = pDbChartStatisticModule->GetSortType();
                if (sortType == DbSheet_Chart_ST_SortByHorizontalAxis)
                    sortTypeStr = __X("Group");
                else if (sortType == DbSheet_Chart_ST_SortByVerticalAxis)
                    sortTypeStr = __X("Value");
                else
                    sortTypeStr = __X("");
                pAcpt->addString("sortType", sortTypeStr);
                PCWSTR orderStr = pDbChartStatisticModule->GetSortAscending() ? __X("Ascend") : __X("Descend");
                pAcpt->addString("order", orderStr);
            }
        }
    }
}

void SerialiseDataConditionSeries(IDBChartStatisticModule* pDbChartStatisticModule, ISerialAcceptor* pAcpt, DBSheetCommonHelper* pHelper)
{
    bool onlyCountDimension = pDbChartStatisticModule->GetOnlyCountDimension();
    if (onlyCountDimension)
    {
        pAcpt->addString("series", __X("COUNTA"));
    }
    else
    {
        class StatisticOptionsEnum : public IDbChartStatisticOptionsEnum
        {
        public:
            StatisticOptionsEnum(ISerialAcceptor* pAcpt, DBSheetCommonHelper* pHelper) : m_pAcpt(pAcpt), m_pHelper(pHelper) {}
            STDMETHODIMP Do(const IDbChartStatisticOption* pStatisticOption) override
            {
                m_pHelper->AcceptorAddEtDbId(m_pAcpt, "fieldId", pStatisticOption->GetFieldId());
                ET_DBSheet_StatisticOption option = pStatisticOption->GetStatisticOption();
                PCWSTR optionStr = nullptr;
                _appcore_GainEncodeDecoder()->EncodeStatisticOption(option, &optionStr);
                m_pAcpt->addString("rollup", optionStr);
                return S_OK;
            }
        private:
            ISerialAcceptor* m_pAcpt;
            DBSheetCommonHelper* m_pHelper;
        };
        sa::Leave series(sa::enterArray(pAcpt, "series"));
        StatisticOptionsEnum statisticEnum(pAcpt, pHelper);
        pDbChartStatisticModule->GetConstStatisticOptions()->Enum(&statisticEnum);
    }
}

void resetLookupFieldRetrieverBySid(IDBSheetOp* pDbSheetOp, UINT linkSheetId, bool bPreferId, DbSheetFieldRetriever& fieldRetriever)
{
    if (linkSheetId != pDbSheetOp->GetSheetId())
    {
        IBook* pBook = pDbSheetOp->GetBook();
        IDX sheetIdx = INVALIDIDX;
        pBook->STSheetToRTSheet(linkSheetId, &sheetIdx);
        if (sheetIdx == INVALIDIDX)
            return;
        ks_stdptr<ISheet> spLinkSheet;
        pBook->GetSheet(sheetIdx, &spLinkSheet);
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        spLinkSheet->GetExtDataItem(edSheetDbOp, (IUnknown**)&spDbSheetOp);
        fieldRetriever.Reset(spDbSheetOp->GetFieldsManager(), bPreferId);
    }
}

void resetLookupFieldRetriever(IDBSheetOp* pDbSheetOp, IDbField* pField, bool bPreferId, DbSheetFieldRetriever& fieldRetriever)
{
    ks_stdptr<IDbField_Link> spFieldLink = pField;
    UINT linkSheetId = spFieldLink->GetLinkSheet();
    resetLookupFieldRetrieverBySid(pDbSheetOp, linkSheetId, bPreferId, fieldRetriever);
}

HRESULT DbSheetValueSerialiser::SerialiseAllFieldsBaseSchema()
{
    std::unordered_set<EtDbId>  noPermissionFieldId = GetNoPermissionField();
    
    const IDBIds *pFields = m_spDbSheetOp->GetAllFields();
    wo::sa::Leave leave(wo::sa::enterArray(m_acpt, "fields"));
    for (EtDbIdx i = 0, c = pFields->Count(); i < c; ++i)
    {
        EtDbId fieldId = pFields->IdAt(i);
        ASSERT(fieldId != INV_EtDbId);
        ks_stdptr<IDbField> spField;
        m_pFieldManager->GetField(fieldId, &spField);
        if (spField == nullptr)
            return E_DBSHEET_FIELD_NOT_FOUND;
        
        wo::sa::Leave leave(wo::sa::enterStruct(m_acpt, nullptr));
        ET_DbSheet_FieldType type = spField->GetType();
        PCWSTR typeStr = nullptr;
        VS(m_pEncodeDecoder->EncodeFieldType(type, &typeStr));
        ASSERT(typeStr);
        m_acpt->addString("type", typeStr);
        EtDbIdStr buf;
        VS(m_pDbCtx->EncodeEtDbId(spField->GetID(), &buf));
        m_acpt->addString("id", buf);
        m_acpt->addString("name", spField->GetName());
        PCWSTR permission = nullptr;
        if (noPermissionFieldId.find(fieldId) != noPermissionFieldId.end())
        {
            permission = __X("noPermission");
        }
        else if (FAILED(m_spProtectionJudgement->CheckFieldCanSetValue(spField->GetDbSheetData()->GetSheetId(), fieldId)))
        {
            permission = __X("access");
        }
        else
        {
            permission = __X("edit");
        }
        m_acpt->addString("permission", permission);
    }

    return S_OK;
}

} // namespace wo
