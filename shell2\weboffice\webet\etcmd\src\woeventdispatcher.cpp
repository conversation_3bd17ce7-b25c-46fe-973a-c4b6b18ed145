﻿#include "etstdafx.h"
#include "woeventdispatcher.h"

namespace wo
{

bool KWoEventDispatcher::processEvents(QEventLoop::ProcessEventsFlags flags)
{
    ASSERT(FALSE);
    ++(m_callInfo.processEventsCnt);
    return true;
}

bool KWoEventDispatcher::hasPendingEvents()
{
    ASSERT(FALSE);
    ++(m_callInfo.hasPendingEventsCnt);
    return false;
}

void KWoEventDispatcher::registerSocketNotifier(QSocketNotifier* notifier)
{
    ASSERT(FALSE);
    ++(m_callInfo.registerSocketNotifierCnt);
}

void KWoEventDispatcher::unregisterSocketNotifier(QSocketNotifier* notifier)
{
    ASSERT(FALSE);
    ++(m_callInfo.unregisterSocketNotifierCnt);
}

void KWoEventDispatcher::registerTimer(int timerId, int interval, Qt::TimerType timerType, QObject* object)
{
    ASSERT(FALSE);
    ++(m_callInfo.registerTimerCnt);
}

bool KWoEventDispatcher::unregisterTimer(int timerId)
{
    ASSERT(FALSE);
    ++(m_callInfo.unregisterTimerCnt);
    return true;
}

bool KWoEventDispatcher::unregisterTimers(QObject* object)
{
    ASSERT(FALSE);
    ++(m_callInfo.unregisterTimersCnt);
    return true;
}

QList<QAbstractEventDispatcher::TimerInfo> KWoEventDispatcher::registeredTimers(QObject* object) const
{
    ASSERT(FALSE);
    ++(m_callInfo.registeredTimersCnt);
    return QList<TimerInfo>{};
}

int KWoEventDispatcher::remainingTime(int timerId)
{
    ASSERT(FALSE);
    ++(m_callInfo.remainingTimeCnt);
    return 0;
}

void KWoEventDispatcher::wakeUp()
{
    ASSERT(FALSE);
    ++(m_callInfo.wakeUpCnt);
}

void KWoEventDispatcher::interrupt()
{
    ASSERT(FALSE);
    ++(m_callInfo.interruptCnt);
}

void KWoEventDispatcher::flush()
{
    ASSERT(FALSE);
    ++(m_callInfo.flushCnt);
}

const EventDispatcherCallInfo* KWoEventDispatcher::getCallInfo() const
{
    return &m_callInfo;
}

}