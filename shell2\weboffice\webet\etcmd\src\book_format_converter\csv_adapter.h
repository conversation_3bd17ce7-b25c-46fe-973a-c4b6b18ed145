﻿#ifndef __WEBET_CSV_ADAPTER_H__
#define __WEBET_CSV_ADAPTER_H__

#include "wo/et_shared_str.h"

interface IDBProtectionJudgement;

namespace wo
{
	class CsvAdapter
	{
	protected:
		struct CsvParam
		{
			TextToColumns_Type ttcType = ttc_OpenFile;
			ET_DataSplitType splitType = et_Delimited;
			BOOL bParseFormulaAsText = FALSE;
			BOOL bParseNumAsText = FALSE;
			BOOL bParseSpecialNumAsText = TRUE;
			BOOL bIgnoreAllLeadingBlankLines = FALSE;
		};
	public:
		explicit CsvAdapter(PCWSTR filePath);
		HRESULT Init(CsvParam);
		HRESULT Exec(ISheet*, const RANGE*);
		void GetRowColInfo(ROW&, COL&) const;
		ITextImport* GetTextImport() const { return m_spTextImport; }
	private:
		ROW m_rowCnt = 0;
		COL m_colCnt = 0;
		PCWSTR m_filePath = nullptr;
		ks_stdptr<ITextImport> m_spTextImport;
	};
	class Csv2DbAdapter : public CsvAdapter
	{
	public:
		Csv2DbAdapter(PCWSTR filePath, _Workbook* pTarWorkbook, BMP_PTR, IDBProtectionJudgement*, const binary_wo::VarObj&);
		HRESULT Init();
		HRESULT Exec(UINT& activeStId);
		void rollback();
		bool GetLostFlag() const { return m_lostFlag; }
	private:
		HRESULT adjustFieldType(IDX, IDBSheetView*) const;
		HRESULT adjustFieldSettings(IDBSheetView*) const;

		bool m_lostFlag = false;
		_Workbook* m_pTarWorkbook = nullptr;
		BMP_PTR m_pBMP = nullptr;
		IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
		const binary_wo::VarObj& m_param;
		std::vector<PCWSTR> m_defaultFieldName;
		std::vector<GlobalSharedString> m_fldNames;
		ks_stdptr<_Worksheet> m_spNewWorksheet;
		ks_stdptr<IETStringTools> m_spStringTools;
	};

	//数据库同步到DB，也是csv文件格式
	class KEtWorkbook;
	class SyncSqlCsv2DbAdapter : public CsvAdapter 
	{
		struct PrimaryKey
		{
			std::vector<ks_wstring> m_fieldValueVec;	//可能多个列值作为主键
			bool operator<(const PrimaryKey& other) const { return m_fieldValueVec < other.m_fieldValueVec; }
		};
		struct PrimaryKeyParam
		{
			std::vector<ks_wstring> m_primaryFieldNameVec; //主键列名称
			bool m_isValid = false;
		};
	public:
		SyncSqlCsv2DbAdapter(KEtWorkbook* wb, PCWSTR filePath, _Workbook* pTarWorkbook, BMP_PTR, IDBProtectionJudgement*, const binary_wo::VarObj&);
		~SyncSqlCsv2DbAdapter();
		HRESULT Init();
		HRESULT Exec();
	private:
		HRESULT ParseParams();

		HRESULT SyncFieldData(ISheet* pSheet);
		HRESULT SyncRecordData(ISheet* pSheet);
		
		ISheet* GetCsvSheet();
		void GetCsvSheetRowColInfo(ROW& rowCnt, COL& colCnt);
		HRESULT ImportSheetFromCsv(ROW rowCnt, COL colCnt);
		HRESULT BuildCsvSheetIdMap();

		ks_wstring GetCsvSheetValue(EtDbId rowId, PCWSTR fieldName);

		void CheckPrimaryKeyIsValid();
		bool IsPrimaryKeyValid();
		bool MakePrimaryKey(IDBSheetOp* pOp, EtDbId rowId, const std::map<ks_wstring, EtDbId>& fieldMap, PrimaryKey& pk);

		void UpdateTarSheetRecordData(EtDbId tarSheetRowId, EtDbId csvSheetRowId, bool exceptPrimaryField = false);
		
		bool SqlDataType2DbType(const ks_wstring& sqlType, ET_DbSheet_FieldType& fieldType);
		bool IsSameType(const ks_wstring& fieldName, ET_DbSheet_FieldType fieldType);
		ET_DbSheet_FieldType GetSyncDbType(const ks_wstring& fieldName);
		void ModifyDbTypeFormat(binary_wo::VarObj& obj, ET_DbSheet_FieldType type);

		HRESULT ClearEmptyRow(ISheet* pSheet);
		HRESULT ClearOldNumberFormat();
	private:
		KEtWorkbook* m_wwb;
		_Workbook* m_pTarWorkbook = nullptr;
		BMP_PTR m_pBMP = nullptr;
		IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
		const binary_wo::VarObj& m_param;
		bool m_lostFlag = false;
		bool m_firstSync = false;
		UINT32 m_targetSheetId = INVALID_ID;
		std::map<ks_wstring, ks_wstring> m_sqlFieldTypeMap;

		std::vector<ks_wstring> m_fldNames;
		ks_stdptr<_Worksheet> m_spCsvWorksheet;
		ks_stdptr<IDBSheetOp> m_spCsvDbSheetOp;
		ks_stdptr<IDBSheetOp> m_spDbSheetOp;
		ks_stdptr<IDBSheetView> m_spDbSheetView;
		ks_stdptr<IETStringTools> m_spStringTools;

		PrimaryKeyParam m_primaryKeyParam;
		std::map<ks_wstring, EtDbId> m_tarSheetSyncFieldNameMap; // <syncSourceFieldName, fieldId>
		std::map<ks_wstring, EtDbId> m_csvSheetFieldMap; // <fieldname, fieldId>
		struct RecordInfo
		{
			EtDbId m_rowId = 0;
			bool m_isRecordMatched = false;
		};
		std::map<PrimaryKey, RecordInfo> m_csvSheetRecordMap;
	};
} // namespace wo

#endif
