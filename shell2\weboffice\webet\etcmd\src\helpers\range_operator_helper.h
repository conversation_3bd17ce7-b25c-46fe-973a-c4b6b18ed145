﻿#ifndef __RANGE_OPERATOR_HELPER_H__
#define __RANGE_OPERATOR_HELPER_H__

#include "webbase/binvariant/binvarobj.h"
namespace wo
{
class KEtWorkbook;
namespace RangeOperatorHelper
{
    // 若找不到满足要求的row，则返回-1
	INT32 GetEndEmptyRowInRg(KEtWorkbook* pWorkBook, const binary_wo::VarObj& param, const IDX sheetIdx, bool bSkipEmptyMergeCell = false);
    // 获取sheet中usedRange区域，返回rowCnt
    INT32 GetSheetDataRange(IKWorksheet* pSheet, etoldapi::Range** ppRgSrc);
}

namespace RangeSliceHelper
{

struct SliceCellInfo
{
    ROW row = 0;
    COL col = 0;
    IDX sheetIdx = INVALIDIDX;
    int begin = 0; //当前切片文本在单元格文本的起始位置
    int end = 0;//当前切片文本在单元格文本的结束位置
};

class KRangeSliceHelper : public ICellValueAcpt
{
public:
    explicit KRangeSliceHelper(KEtWorkbook* pWorkbook);
    ~KRangeSliceHelper();

    void clearSliceCache();
    HRESULT beginSliceRange(etoldapi::_Worksheet* pWorksheet);
    HRESULT beginBatchSliceRange();
    bool getSliceInfo(int index, QMap<int, QString>& sliceInfo, int count = 1);
    void getErrorItemCells(const QMultiMap<std::pair<int, int>, std::pair<int, int>>& errPos, QVector<std::pair<int, SliceCellInfo>>& cellVec);
    bool modifyText(int sliceIndex, CELL cell, int sheetIdx, const QString& modifyText, int start, int end);
    int getTotalCount() const;

private:
    STDPROC_(INT) Do(ROW, COL, const_token_ptr) override;
    void startCreateText(ISheet* sheet, const RANGE& rg);
    void addSliceCell(int row, int col, int begin, int end);
    bool getCellText(CELL cell, int sheetIdx, QString& cellText);
    bool isCellFormula(ROW row, COL col);
    
    KEtWorkbook* m_pWorkbook = nullptr;
    IDX m_sheetIdx = INVALIDIDX;
    QVector<QVector<SliceCellInfo>> m_sliceVec; //存放所有切片信息
    int m_currentTextIndex = 0;
    ISheet* m_pSheet = nullptr;
};
}
}

#endif