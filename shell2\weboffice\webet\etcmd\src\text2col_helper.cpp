﻿#include "etstdafx.h"
#include "workbook.h"
#include "wo/workbook_obj.h"
#include "applogic/et_apihost.h"
#include "et_revision_context_impl.h"
#include "autofilterparamhelper.h"
#include "et_binvar_spec.h"
#include "et_task_peripheral.h"
#include "et_task_detail_xf.h"
#include <public_header/drawing/api/dghost_i.h>
#include <public_header/drawing/view/shape_visual.h>
#include "kso/io/clipboard/ksomimetype.h"
#include "etcore/little_alg.h"
#include "kso/l10n/et/et_app.h"
#include "sort_helper.h"
#include "applogic/et_applogic_infos.h"
#include "text2col_helper.h"
#include "webbase/wo_sa_helper.h"
#include "helpers/protection_helper.h"
#include "util.h"
#include "applogic/et_applogic_helper.h"

namespace wo
{
namespace text2col
{
using namespace appcore_smartsplite;
HRESULT packageRslt(ISerialAcceptor *acpt, const std::vector<std::vector<ks_wstring>> &rslt);

HRESULT getDelimParam(const binary_wo::VarObj &param, SplitParam &splitparam)
{
	auto delmParam = param.get("t2cPrm").get("param");
	auto textQlfr = delmParam.field_uint32("txtQlfr");
	//js text2col_param.js
	switch (textQlfr)
	{
	case 1:
		splitparam.TextQualifier = et_TextQualifierDoubleQuote;
		break;
	case 2:
		splitparam.TextQualifier = et_TextQualifierSingleQuote;
		break;
	default:
		splitparam.TextQualifier = et_TextQualifierNone;
	};
	splitparam.Comma = delmParam.field_bool("coma");
	splitparam.Tab = delmParam.field_bool("tab");
	splitparam.ConsecutiveDelimiter = delmParam.field_bool("consDelim");
	splitparam.Semicolon = delmParam.field_bool("smcl");
	splitparam.Space = delmParam.field_bool("space");
	splitparam.Other = delmParam.field_bool("other");
	ks_wstring delCh = delmParam.field_str("othChr");
	if (!delCh.empty())
	{
		splitparam.OtherChar = delCh[0];
	}
	return S_OK;
};

//ref et_applogic_helper.inl line 2016
class KCellValueAcpt : public ICellValueAcpt
{
	STDIMP_(INT)
	Do(ROW, COL, const_token_ptr pToken)
	{
		if (pToken)
			return 1;
		return 0;
	};
};

//ref: et_applogic_helper.inl line 2016
BOOL IsDestRgUsed(const RANGE *pDestRg, ISheet *pSheet, IBookOp *pBookOp)
{
	if (!pDestRg || !pSheet || !pBookOp)
	{
		return FALSE;
	}

	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
	KCellValueAcpt cellValue;
	if (spSheetEnum->EnumCellValue(*pDestRg, &cellValue))
	{
		return TRUE;
	}

	const XF *pNormalXF = NULL;
	ks_stdptr<IKStyles> spStyles;
	ks_stdptr<IBook> spBook;
	pBookOp->GetBook(&spBook);
	spBook->GetStyles(&spStyles);
	spStyles->GetNormalXF(&pNormalXF);
	ASSERT(NULL != pNormalXF);

	if (pSheet->GetFmtedTopHdr() >= 0)
	{
		ROW rTop = MAX(pDestRg->RowFrom(), pSheet->GetFmtedTopHdr());
		ROW rBottom = MIN(pDestRg->RowTo(), pSheet->GetFmtedBottomHdr());
		COL cLeft = MAX(pDestRg->ColFrom(), pSheet->GetFmtedLeftHdr());
		COL cRight = MIN(pDestRg->ColTo(), pSheet->GetFmtedRightHdr());
		bool bValid = cLeft <= cRight;
		for (ROW r = rTop; bValid && r <= rBottom; ++r)
		{
			for (COL c = cLeft; c <= cRight; ++c)
			{
				const XF *pXF = NULL;
				pBookOp->GetCellFormat(pDestRg->SheetFrom(), r, c, &pXF, NULL);
				if (0 != memcmp(pXF, pNormalXF, sizeof(XF)))
				{
					return TRUE;
				}
			}
		}
	}
	return FALSE;
}

void getFieldInfo(const binary_wo::VarObj &obj, std::vector<INT> &colInfo, std::vector<INT> &fieldInfo)
{
	colInfo.clear();
	fieldInfo.clear();
	auto colInfo_Obj = obj.get("t2cPrm").get("colInfo");
	INT sz = colInfo_Obj.arrayLength();
	for (INT i = 0; i < sz; ++i)
	{
		//�ں˷����������������1�±꿪ʼ�ģ�ǰ̨colinfo�õ�0�±꣬���Լ�1
		colInfo.push_back(i + 1);
		fieldInfo.push_back(colInfo_Obj.item_int32(i));
	}
};

BOOL HasListObject(ISheet *pSheet, const RANGE &rg)
{
	ks_stdptr<ICoreListObjects> spLstObjs;
	pSheet->GetExtDataItem(edSheetListObjects, (IUnknown **)&spLstObjs);
	if (spLstObjs == NULL)
	{
		return FALSE;
	}
	UINT uCount = spLstObjs->GetCount();
	for (UINT i = 0; i < uCount; ++i)
	{
		ks_stdptr<ICoreListObject> spLstObj;
		spLstObjs->GetItem(i, &spLstObj);
		RANGE rgLstObj(rg);
		spLstObj->GetRange(&rgLstObj);
		if (rgLstObj.Intersect(rg).IsValid())
			return TRUE;
	}
	return FALSE;
}

struct DelList
{
	DelList(const ks_stdptr<IKWorksheet> &spWorksheet,
			const ks_stdptr<ICoreListObjects> &spListObjs,
			const ks_stdptr<ICoreListObject> &spTable) : _pWorksheet(spWorksheet),
														 _spListObjs(spListObjs),
														 _spTable(spTable) {}
	ks_stdptr<IKWorksheet> _pWorksheet;
	ks_stdptr<ICoreListObjects> _spListObjs;
	ks_stdptr<ICoreListObject> _spTable;
	HRESULT Delete();
};

HRESULT DelList::Delete()
{
	_pWorksheet->GetSheet()->GetBMP();
	RANGE rg(_pWorksheet->GetSheet()->GetBMP());
	ks_stdptr<Range> spRg;
	_spTable->GetRange(&rg);
	_pWorksheet->GetRangeByData(&rg, &spRg);
	HRESULT hr = _spTable->Delete();
	if (SUCCEEDED(hr) && spRg)
	{
		spRg->Clear();
	}
	return hr;
}

HRESULT DeleteTableOverlappedWithRg(ks_stdptr<IKWorksheet> &spWorkSheet, const RANGE &rg, BOOL bTotalContained)
{
	ISheet *pSheet = spWorkSheet->GetSheet();
	VERIFY_IN_POINTER(pSheet);
	ks_stdptr<IBook> spBook;
	pSheet->GetBook(&spBook);
	std::vector<wo::text2col::DelList> vecDelTables;
	ks_stdptr<ICoreListObjects> spListObjs;
	pSheet->GetExtDataItem(edSheetListObjects, (IUnknown **)&spListObjs);
	if (!spListObjs)
	{
		return S_FALSE;
	}
	for (size_t i = 0; i < spListObjs->GetCount(); ++i)
	{
		ks_stdptr<ICoreListObject> spTable;
		spListObjs->GetItem(i, &spTable);
		RANGE rgTable(pSheet->GetBMP());
		spTable->GetRange(&rgTable);
		BOOL bDelete = bTotalContained ? rg.Contain(rgTable) : rg.Intersect(rgTable).IsValid();
		if (bDelete)
		{
			vecDelTables.push_back(wo::text2col::DelList(spWorkSheet, spListObjs, spTable));
		}
	}
	std::for_each(vecDelTables.begin(), vecDelTables.end(), [](DelList &lb) {
		lb.Delete();
	});
	return S_OK;
}

RANGE getDestRG(IBook* pBook, const binary_wo::VarObj &param)
{
	BMP_PTR bmp = pBook->GetBMP();
	if (param.has("dest"))
	{
		binary_wo::VarObj dest = param.get("dest");
		RANGE rg(bmp);
		IDX sheetIdxDest = GetSheetIdx(pBook, dest);
		rg.SetSheetFromTo(sheetIdxDest);
		rg.SetRowFromTo(
			dest.field_int32("rowFrom"), dest.field_int32("rowTo"));
		rg.SetColFromTo(
			dest.field_int32("colFrom"), dest.field_int32("colTo"));
		return rg;
	}
	else
	{
		IDX sheetIdx = GetSheetIdx(pBook, param);
		return ReadRangeInl(bmp, sheetIdx, param);
	}
}

bool text2colCanContinue(std::set<ErrType> &tps)
{
	if (tps.find(invalidParm) != tps.end())
	{
		return false;
	}
	if (tps.find(mergedCell) != tps.end())
	{
		return false;
	}
	return true;
}
void createReturnData(ISerialAcceptor *acpt, const std::set<ErrType> &tps,
					  const RANGE &rg_source, const IDX idxSouce,
					  const RANGE &rg_dest, const IDX idxDest,
					  const RANGE &chkrg)
{
	{
		auto xx = sa::enterArray(acpt, "error_types");
		for (auto it = tps.begin(); it != tps.end(); ++it)
		{
			acpt->addInt32(nullptr, static_cast<int>(*it));
		}
	}
	{
		auto xx = sa::enterStruct(acpt, "rg_souce");
		acpt->addUint32("sheetIdx", idxSouce);
		acpt->addUint32("rowFrom", rg_source.RowFrom());
		acpt->addUint32("rowTo", rg_source.RowTo());
		acpt->addUint32("col", rg_source.ColFrom());
	}
	{
		auto xx = sa::enterStruct(acpt, "rg_dest");
		acpt->addUint32("sheetIdx", idxDest);
		acpt->addUint32("rowFrom", rg_dest.RowFrom());
		acpt->addUint32("rowTo", rg_dest.RowTo());
		acpt->addUint32("col", rg_dest.ColFrom());
	}
	{
		auto xx = sa::enterStruct(acpt, "chkrg");
		acpt->addUint32("sheetIdx", idxDest);
		acpt->addUint32("rowFrom", chkrg.RowFrom());
		acpt->addUint32("rowTo", chkrg.RowTo());
		acpt->addUint32("colFrom", chkrg.ColFrom());
		acpt->addUint32("colTo", chkrg.ColTo());
	}
}

//ref: et_applogic_helper.inl :  RangeTextSplit_CheckUserOp()
std::set<ErrType> RangeTextSplitCheckRangeValid(
	ks_stdptr<IKWorksheet> &spSheet, const RANGE *pSrcRg, RANGE *pDestRg,
	long lRows, long lCols, long lIgnoreCols, bool onlyCheck)
{
	ISheet *pSheet = spSheet->GetSheet();
	std::set<ErrType> ret;
	if (!pDestRg || !pSheet || !pSrcRg)
	{
		ret.emplace(invalidParm);
		return ret;
	}
	if (lIgnoreCols == lCols)
	{
		return ret;
	}

	ROW rT = pDestRg->RowFrom() + lRows - 1;
	COL cT = pDestRg->ColFrom() + lCols - 1 - lIgnoreCols;

	if (cT >= pDestRg->GetBMP()->cntCols ||
		rT >= pDestRg->GetBMP()->cntRows)
	{
		ret.emplace(overRange);
	}
	pDestRg->SetColTo(MIN(cT, pDestRg->GetBMP()->cntCols - 1));
	pDestRg->SetRowTo(MIN(rT, pDestRg->GetBMP()->cntRows - 1));
	// if (pDestRg->ColFrom() == pDestRg->ColTo()) {
	// 	return ret;
	// }

	range_helper::ranges rgsMerge;
	pSheet->FindEffectMergeCell(*pDestRg, FALSE, &rgsMerge);
	for (int loop = 0; loop < rgsMerge.size(); ++loop)
	{
		std::pair<INT, const RANGE*> p = rgsMerge.at(loop);
		if (!(pDestRg->ColFrom() == p.second->ColFrom() && p.second->ColTo() == p.second->ColFrom()))
		{
			ret.emplace(mergedCell);
			return ret;
		}
	}

	ks_stdptr<IBook> spBook;
	pSheet->GetBook(&spBook);
	ks_stdptr<IBookOp> spBookOp;
	spBook->GetOperator(&spBookOp);

	range_helper::ranges rgsArray;
	spBookOp->GetArrayFormulaInfoEx(*pDestRg, &rgsArray, FALSE, AFT_AllArray);
	int nArrayCount = rgsArray.size();
	for (int i = 0; i < nArrayCount; ++i)
	{
		std::pair<INT, const RANGE *> p = rgsArray[i];
		const RANGE *pRg = p.second;
		if (pRg->ColFrom() < pDestRg->ColFrom() ||
			pRg->RowFrom() < pDestRg->RowFrom() ||
			pDestRg->ColTo() < pRg->ColTo() ||
			pDestRg->RowTo() < pRg->RowTo())
		{
			ret.emplace(arrFmlr);
			break;
		}
	}
	if (pSrcRg->ColFrom() != pDestRg->ColFrom() || pDestRg->Width() > 1)
	{
		RANGE rgDest(*pDestRg);
		if (pSrcRg->ColFrom() == pDestRg->ColFrom())
		{
			rgDest.SetColFrom(rgDest.ColFrom() + 1);
		}
		if (IsDestRgUsed(&rgDest, pSheet, spBookOp))
		{
			ret.emplace(hasData);
		}
	}

	if (onlyCheck)
	{
		return ret;
	}

	for (int i = 0; i < nArrayCount; ++i)
	{
		std::pair<INT, const RANGE *> p = rgsArray.at(i);
		spBookOp->SetCellText(*(p.second), NULL, cvoNormal);
	}

	RANGE rgTestTable = pDestRg->Union(*pSrcRg);
	if (rgTestTable.IsValid() && wo::text2col::HasListObject(pSheet, rgTestTable))
	{
		wo::text2col::DeleteTableOverlappedWithRg(spSheet, rgTestTable, TRUE);
	}
	return ret;
}

HRESULT text2colProc(KEtWorkbook *wwb, binary_wo::VarObj &param, ISerialAcceptor *acpt, const bool isQuery, KEtRevisionContext* ctx)
{
	if (!wwb)
	{
		return E_FAIL;
	}
	IBook* pBook = wwb->GetCoreWorkbook()->GetBook();
	RANGE destRG = getDestRG(pBook, param);
	auto text2ColParam = param.get("t2cPrm").get("param");
	bool bUseSmartSplitCol = false;
	if (text2ColParam.has("bUseSmartSplitCol"))
		bUseSmartSplitCol = text2ColParam.field_bool("bUseSmartSplitCol");

	IDX sheetIdx = GetSheetIdx(pBook, param);
	RANGE rgSelection = ReadRangeInl(wwb->GetBMP(), sheetIdx, param);
	const RANGE *prgSelection = &rgSelection;
	if (prgSelection->ColFrom() != prgSelection->ColTo())
	{
		return S_FALSE;
	}

	if (isQuery)
	{
		CHECK_PROTECTION_ALLOW_READ(rgSelection, ctx);
		CHECK_PROTECTION_ALLOW_READ(destRG, ctx);
	}
	else
	{
		CHECK_PROTECTION_ALLOW_EDIT(rgSelection, ctx);
		CHECK_PROTECTION_ALLOW_EDIT(destRG, ctx);
	}

	ks_stdptr<IKRanges> ptrIRanges;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void **)&ptrIRanges);
	ptrIRanges->Append(alg::STREF_THIS_BOOK, rgSelection);
	KCOMPTR(IRangeTextSplit)
	spRangeTextSplit;
	_appcore_CreateObject(CLSID_KRangeTextSplit, IID_IRangeTextSplit, (void **)&spRangeTextSplit);
	if (!spRangeTextSplit)
	{
		return E_FAIL;
	}

	ks_stdptr<IKWorksheets> spWorksheets = wwb->GetCoreWorkbook()->GetWorksheets();
	if (!spWorksheets)
	{
		return E_FAIL;
	}
	ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(sheetIdx);
	spRangeTextSplit->Init(ttc_DataSplit, spWorksheet->GetSheet(), ptrIRanges, false);

	if (!spWorksheet)
		return E_FAIL;
	ISheet* pSheet = spWorksheet->GetSheet();
	if (!pSheet)
		return E_FAIL;

	ks_stdptr<IBook> spBook;
	spWorksheet->GetSheet()->GetBook(&spBook);
	ks_stdptr<IBookOp> spBookOp;
	spBook->GetOperator(&spBookOp);

	util::CalcBatchUpdate calcBatchUpdate(spBookOp, spBook->GetWoStake()->HasImportrangeFuncs());
	if (appcore_helper::HasPivotTable(pSheet, destRG)
			|| appcore_helper::HasPivotTable(pSheet, rgSelection))
		return E_PIVOTTABLE_DENIED_TextToColumns;

	HRESULT hr = E_FAIL;
	
	const RANGE *pDstRg = NULL;
	ks_stdptr<IKRanges> spResultRg;
	smartSplit_helper::checkRangeInfo info;
	std::vector<std::vector<ks_wstring>> preViewDataList;
	if (bUseSmartSplitCol)
	{
		ks_stdptr<etoldapi::Range> spRange = wwb->CreateRangeObj(rgSelection);
		hr = smartSplit_helper::DataSmartSplit(spRange, destRG, &spResultRg, isQuery, &info, preViewDataList);
		if (FAILED(hr))
			return hr;
		
		UINT nCnt = 0;
		spResultRg->GetCount(&nCnt);
		if (nCnt != 1)
			return E_FAIL;
		spResultRg->GetItem(0, NULL, &pDstRg);
	}
	else
	{
		spRangeTextSplit->Init(ttc_DataSplit, pSheet, ptrIRanges);
		ks_stdptr<IET_TextDataEngine> spShellTextToColumns;
		spRangeTextSplit->QueryInterface(IID_IET_TextDataEngine, (void **)&spShellTextToColumns);
		spShellTextToColumns->SetSplitType(et_Delimited); // current only support delim
		wo::text2col::SplitParam delimParam;
		hr = getDelimParam(param, delimParam);
		if (!SUCCEEDED(hr))
		{
			return hr;
		}
		spShellTextToColumns->SetParam(
			delimParam.TextQualifier, //web pass
			delimParam.ConsecutiveDelimiter,
			delimParam.Tab,
			delimParam.Semicolon,
			delimParam.Comma,
			delimParam.Space,
			delimParam.Other,
			delimParam.OtherChar);
		BOOL bTrailingMinusNum = true;
		spShellTextToColumns->SetAdvanceParam(NULL, NULL, bTrailingMinusNum);
		spShellTextToColumns->SetDestRange(&destRG);

		std::vector_s<INT> colInfo, fieldInfo;
		text2col::getFieldInfo(param, colInfo, fieldInfo);
		hr = spShellTextToColumns->SetFieldInfo(colInfo, fieldInfo);
		if (!SUCCEEDED(hr))
		{
			return hr;
		}
		hr = spShellTextToColumns->SplitTextData(FALSE);
		if (!SUCCEEDED(hr))
		{
			return hr;
		}
		spRangeTextSplit->GetDisplayRect(&info.row, &info.col);
		info.nIgnoreCol = spRangeTextSplit->GetIgnoreCols();
		spRangeTextSplit->GetDestRange(&pDstRg);
	}
	
	//ref: et_applogic_helper.inl :  RangeTextSplit_CheckUserOp()
	RANGE chkrg(*pDstRg);
	auto errTypes = text2col::RangeTextSplitCheckRangeValid(spWorksheet, prgSelection, &chkrg, info.row, info.col, info.nIgnoreCol, isQuery);

	ks_castptr<_Worksheet> spDestWorksheet = spWorksheets->GetSheetItem(chkrg.SheetFrom());
	ks_bstr destSheetName;
	spDestWorksheet->get_Name(&destSheetName);
	binary_wo::VarObj objRgSplit = param.add_field_struct("rgSplit");
	objRgSplit.add_field_str("sheetName", destSheetName.c_str());
	objRgSplit.add_field_int32("sheetIdx", chkrg.SheetFrom());
	objRgSplit.add_field_int32("rowFrom", chkrg.RowFrom());
	objRgSplit.add_field_int32("rowTo", chkrg.RowTo());
	objRgSplit.add_field_int32("colFrom", chkrg.ColFrom());
	objRgSplit.add_field_int32("colTo", chkrg.ColTo());

	if (isQuery)
	{
		createReturnData(acpt, errTypes, rgSelection, sheetIdx, destRG, sheetIdx, chkrg);
		packageRslt(acpt, preViewDataList);
		if (bUseSmartSplitCol)
		{
			ROW first_row = pSheet->SeekNextCellInColumn(rgSelection.RowFrom(), rgSelection.ColFrom());
			if (first_row < rgSelection.RowFrom())
				first_row = rgSelection.RowFrom();

			ROW last_row = pSheet->SeekPrevCellInColumn(rgSelection.RowTo(), rgSelection.ColFrom());
			if (last_row > rgSelection.RowTo())
				last_row = rgSelection.RowTo();

			auto leave = sa::enterStruct(acpt, "newRange");
			acpt->addUint32("sheetIdx", sheetIdx);
			acpt->addUint32("rowFrom", first_row);
			acpt->addUint32("rowTo", last_row);
			acpt->addUint32("col", rgSelection.ColFrom());
		}
		return S_OK;
	}
	else
	{
		if (bUseSmartSplitCol)
		{
			return hr;
		}
		if (text2colCanContinue(errTypes))
		{
			spRangeTextSplit->SetDestRange(&chkrg);
			hr = spRangeTextSplit->SetValue();
		}
		else
		{
			return S_FALSE;
		}
		return hr;
	}
}

//package the result of splited text
HRESULT packageRslt(ISerialAcceptor *acpt, const std::vector<std::vector<ks_wstring>> &rslt)
{
	auto arrbg = wo::sa::enterArray(acpt, "result");
	size_t sz = rslt.size();
	for (size_t i = 0; i < sz; ++i)
	{
		auto arrbg_in = wo::sa::enterArray(acpt, nullptr);
		size_t sz_col = rslt[i].size();
		for (size_t j = 0; j < sz_col; ++j)
		{
			acpt->addString(nullptr, rslt[i][j].c_str(), rslt[i][j].length());
		}
	}
	return S_OK;
};
ks_stdptr<IETDataSource> getDataSource(ISheet *pISheet, RANGE &rgSelection)
{
	range_helper::ranges rgs = range_helper::ranges::create_instance();
	rgs.add(alg::STREF_THIS_BOOK, rgSelection);

	ks_stdptr<IAppCoreRange> spCoreRange;
	KCOMPTR(IETDataSource)
	spDataSource;
	_appcore_CreateObject(CLSID_KAppCoreRange, IID_IAppCoreRange, (void **)&spCoreRange);
	if (!spCoreRange)
	{
		return spDataSource;
	}

	spCoreRange->CreateInSheet(pISheet, NULL, rgs);
	_appcore_CreateObject(CLSID_KAppcoreTextSource, IID_IETDataSource, (void **)&spDataSource);
	spDataSource->Init(spCoreRange);
	return spDataSource;
}
HRESULT getPreview(KEtWorkbook *wwb, const binary_wo::VarObj &param, ISerialAcceptor *acpt)
{
	if (!wwb || !acpt)
	{
		return S_FALSE;
	}
	IDX sheetIdx = GetSheetIdx(wwb->GetCoreWorkbook()->GetBook(), param);
	ks_stdptr<IDataSplit> spDataSplit;
	_appcore_CreateObject(CLSID_KETDataSplit, IID_IDataSplit, (void **)&spDataSplit);
	if (!spDataSplit)
	{
		return S_FALSE;
	}
	//get range
	RANGE rgSelection = ReadRangeInl(wwb->GetBMP(), sheetIdx, param);
	//get sheet
	ks_stdptr<IKWorksheets> spWorksheets = wwb->GetCoreWorkbook()->GetWorksheets();
	ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(sheetIdx);

	//set data source
	ks_stdptr<IETDataSource> spDataSource = getDataSource(spWorksheet->GetSheet(), rgSelection);
	if (!spDataSource)
	{
		return S_FALSE;
	}
	spDataSplit->SetDataSource(spDataSource);

	SplitParam delimParam;
	getDelimParam(param, delimParam);
	std::vector<INT> colInfo;
	std::vector<INT> fieldInfo;
	getFieldInfo(param, colInfo, fieldInfo);
	ks_wstring strDelimiter_ptr = delimParam.getStrDelimiter();

	spDataSplit->SetSplitParam(
		delimParam.ConsecutiveDelimiter, et_Delimited, delimParam.TextQualifier, strDelimiter_ptr.c_str(), 0, 0);

	std::vector<std::vector<ks_wstring>> data_rslt;
	long rowCount = 0;
	spDataSplit->GetRowCount(&rowCount);
	for (long r = 0; r < rowCount; ++r)
	{
		long colCount = 0;
		spDataSplit->GetColCount(r, &colCount);
		std::vector<ks_wstring> row;
		for (long c = 0; c < colCount; ++c)
		{
			const WCHAR *ch_rslt;
			int len_rslt = 0;
			spDataSplit->GetItemValue(r, c, &ch_rslt, &len_rslt);
			row.emplace_back(ks_wstring(ch_rslt, len_rslt));
		}
		data_rslt.emplace_back(std::move(row));
	}
	packageRslt(acpt, data_rslt);
	return S_OK;
}

const WCHAR *GetSpaceChar()
{
	static const WCHAR gc_wcsTrimBlank[9] = {
		0x20,   // SPACE
		0xA0,   // NO-BREAK SPACE
		0x2002, // EN SPACE
		0x2003, // EM SPACE
		0x2004, // THREE-PER-EM SPACE
		0x2005, // FOUR-PER-EM SPACE
		0x2006, // SIX-PER-EM SPACE
		0x3000, // IDEOGRAPHIC SPACE
		0};
	return gc_wcsTrimBlank;
}
ks_wstring SplitParam::getStrDelimiter()
{
	ks_wstring strDelimiter;
	if (Tab)
	{
		strDelimiter += __X("\t");
	}
	if (this->Semicolon)
	{
		strDelimiter += __X(";");
	}
	if (this->Comma)
	{
		strDelimiter += __X(",");
	}
	if (this->Space)
	{
		strDelimiter += GetSpaceChar();
	}
	if (this->Other && this->OtherChar)
	{
		strDelimiter += this->OtherChar;
	}
	return strDelimiter;
}

HRESULT subRangeGetAllData(KEtWorkbook *wwb, INT32 sheetIdx, INT32 rowFrom, INT32 rowTo, INT32 col, ISerialAcceptor *acpt)
{
	//防止传的数据太多卡住，所以前台做分裂的时候限制1024行
	if (rowTo - rowFrom > 1023)
	{
		rowTo = rowFrom + 1023;
	}
	RANGE rg(wwb->GetBMP());
	rg.SetSheetFromTo(sheetIdx);
	rg.SetRowFromTo(rowFrom, rowTo);
	rg.SetColFromTo(col, col);

	std::unordered_map<WCHAR, size_t> countMap;
	countMap.insert(std::make_pair(__Xc('\t'), 0));
	countMap.insert(std::make_pair(__Xc(';'), 0));
	countMap.insert(std::make_pair(__Xc(','), 0));
	const WCHAR *spaces = GetSpaceChar();
	while (*spaces) 
		countMap.insert(std::make_pair(*spaces++, 0));

	ks_stdptr<IKWorksheets> spWorksheets = wwb->GetCoreWorkbook()->GetWorksheets();
	ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(sheetIdx);
	ks_stdptr<IETDataSource> spDataSource = getDataSource(spWorksheet->GetSheet(), rg);
	{
		auto tmp = wo::sa::enterArray(acpt, "data");
		long lRow = 0;
		spDataSource->GetRow(&lRow);
		for (long i = 0; i < lRow; ++i)
		{
			const WCHAR *pData = nullptr;
			spDataSource->GetItem(i, &pData);
			acpt->addString(nullptr, pData);

			const WCHAR *str = pData;
			while (*str)
			{
				auto it = countMap.find(*str);
				if (it != countMap.end())
					++it->second;
				++str;
			}
		}
	} //end array

	WCHAR res = __Xc(' ');
	size_t max = 0;
	for (auto it = countMap.begin(); it != countMap.end(); ++it)
	{
		if (it->second > max)
		{
			max = it->second;
			res = it->first;
		}
	}

	auto tmp = wo::sa::enterStruct(acpt, "delimiters");
	acpt->addBool("tab", res == __Xc('\t'));
	acpt->addBool("smcl", res == __Xc(';'));
	acpt->addBool("coma", res == __Xc(','));
	acpt->addBool("other", false);
	acpt->addBool("space", res != __Xc('\t') && res != __Xc(';') && res != __Xc(','));
	return S_OK;
}

} //end namespace text2col
} //end namespace wo
