﻿#include "etstdafx.h"
#include "database.h"
#include "database_utils.h"
#include "database_field_context.h"
#include "kfc/tools/smart_wrap.h"
#include "applogic/et_apihost.h"
#include "util.h"

namespace wo
{
namespace Database
{
namespace Utils
{

#define CASE_FIELD_TYPE_TO_STRING(type) case dft##type: return __X(#type);
WebStr FieldTypeToStr(FieldType type)
{
    switch (type)
    {
        CASE_FIELD_TYPE_TO_STRING(Invalid)
        CASE_FIELD_TYPE_TO_STRING(Date)
        CASE_FIELD_TYPE_TO_STRING(Time)
        CASE_FIELD_TYPE_TO_STRING(Number)
        CASE_FIELD_TYPE_TO_STRING(Currency)
        CASE_FIELD_TYPE_TO_STRING(Percentage)
        CASE_FIELD_TYPE_TO_STRING(Text)
        CASE_FIELD_TYPE_TO_STRING(ID)
        CASE_FIELD_TYPE_TO_STRING(Phone)
        CASE_FIELD_TYPE_TO_STRING(Email)
        CASE_FIELD_TYPE_TO_STRING(Hyperlink)
        CASE_FIELD_TYPE_TO_STRING(Checkbox)
        CASE_FIELD_TYPE_TO_STRING(List)
        CASE_FIELD_TYPE_TO_STRING(Rating)
        CASE_FIELD_TYPE_TO_STRING(Schedule)
        CASE_FIELD_TYPE_TO_STRING(CellPicture)
        CASE_FIELD_TYPE_TO_STRING(Scan)
        CASE_FIELD_TYPE_TO_STRING(AI)
        CASE_FIELD_TYPE_TO_STRING(GenQRLabel)
        default:
            ASSERT(FALSE);
            return __X("Invalid");
    }
}

#define ADD_STR_TO_FIELD_TYPE_ITEM(type) std::make_pair(_Key(__X(#type)), dft##type)
FieldType StrToFieldType(WebStr str)
{
    using _Key = ks_wstring;
    using _Value = FieldType;
    using _Hasher = kfc::tools::str_hash_ic<_Key>;
    using _Cmp = kfc::tools::str_equal_ic<_Key>;
    using StringToFieldTypeMap = std::unordered_map<_Key, _Value, _Hasher, _Cmp>;

    static StringToFieldTypeMap s_map = 
    {
        ADD_STR_TO_FIELD_TYPE_ITEM(Date),
        ADD_STR_TO_FIELD_TYPE_ITEM(Time),
        ADD_STR_TO_FIELD_TYPE_ITEM(Number),
        ADD_STR_TO_FIELD_TYPE_ITEM(Currency),
        ADD_STR_TO_FIELD_TYPE_ITEM(Percentage),
        ADD_STR_TO_FIELD_TYPE_ITEM(Text),
        ADD_STR_TO_FIELD_TYPE_ITEM(ID),
        ADD_STR_TO_FIELD_TYPE_ITEM(Phone),
        ADD_STR_TO_FIELD_TYPE_ITEM(Email),
        ADD_STR_TO_FIELD_TYPE_ITEM(Hyperlink),
        ADD_STR_TO_FIELD_TYPE_ITEM(Checkbox),
        ADD_STR_TO_FIELD_TYPE_ITEM(List),
        ADD_STR_TO_FIELD_TYPE_ITEM(Rating),
        ADD_STR_TO_FIELD_TYPE_ITEM(Schedule),
        ADD_STR_TO_FIELD_TYPE_ITEM(CellPicture),
        ADD_STR_TO_FIELD_TYPE_ITEM(Scan),
        ADD_STR_TO_FIELD_TYPE_ITEM(AI),
        ADD_STR_TO_FIELD_TYPE_ITEM(GenQRLabel),
    };

    if (s_map.count(str) > 0)
        return s_map[str];

    ASSERT(FALSE);
    return dftInvalid;
}

class ClearEnum : public IFieldEnum
{
public:
    ClearEnum() {}
    virtual ~ClearEnum() {}
    virtual HRESULT Do(FieldContext *pContext, IDbField *pField, const RANGE &rg, const VALIDATION&) override
    {
        return pField->Clear(pContext, rg);
    }
};

HRESULT ClearType(FieldContext *pContext, FieldType type, const RANGE &rg)
{
    ClearEnum clearEnum;
    FieldsManager *pDbMgr = FieldsManager::Instance();
    return pDbMgr->EnumFieldsInRANGE(pContext, rg, type, dbEnumType, &clearEnum);
}

HRESULT ClearExcludeType(FieldContext *pContext, FieldType type, const RANGE &rg)
{
    ClearEnum clearEnum;
    FieldsManager *pDbMgr = FieldsManager::Instance();
    return pDbMgr->EnumFieldsInRANGE(pContext, rg, type, dbEnumExcludeType, &clearEnum);
}

HRESULT ClearAll(FieldContext *pContext, const RANGE &rg)
{
    ClearEnum clearEnum;
    FieldsManager *pDbMgr = FieldsManager::Instance();
    return pDbMgr->EnumFieldsInRANGE(pContext, rg, dftPlaceholder, dbEnumAll, &clearEnum);
}

static
HRESULT DbEditColInner(FieldContext *pContext, ListColumn *pLstCol, const RANGE &colDataRg, FieldType fieldType)
{
    // 设置列名
    binary_wo::VarObj extObj = pContext->GetExtObj();
    if (extObj.has("fieldName"))
    {
        ks_bstr fieldName(extObj.field_str("fieldName"));
        if (FAILED(pLstCol->put_Name(fieldName)))
            return E_FAIL;
    }

    if (extObj.has("fieldWidth"))
    {
        double fieldWidth = extObj.field_double("fieldWidth");
        ks_stdptr<Range> spRange = pContext->CreateRangeObj(colDataRg);
        if (spRange == nullptr)
            return E_FAIL;
        KComVariant var(fieldWidth);
        spRange->put_ColumnWidth(var);
    }

    HRESULT hr = ClearAll(pContext, colDataRg);
    if (FAILED(hr))
        return hr;

    // 设置列数据类型
    FieldsManager *pDbMgr = FieldsManager::Instance();
    IDbField *pField = pDbMgr->GetField(fieldType);
    if (pField == nullptr)
        return E_FAIL;

    hr = pField->Set(pContext, colDataRg);
    if (FAILED(hr))
        return hr;

    // 初始化内容
    return pField->Initialise(pContext, colDataRg);
}

HRESULT DbAddCol(ListObject *pListObject, FieldContext *pContext, FieldType fieldType, RANGE &colRg)
{
    RANGE dataRg(pContext->GetBMP());
    pListObject->get_DataBodyRange(&dataRg);
    if (!dataRg.IsValid())
        return E_FAIL;

    // 插入新的表列, 直接插到最后
    IDX index = dataRg.Width() + 1; // index从1开始
    ks_stdptr<ListColumns> spLstCols;
    ks_stdptr<ListColumn> spLstCol;
    HRESULT hr = pListObject->get_ListColumns(&spLstCols);
    if (FAILED(hr))
        return hr;
    hr = spLstCols->Add(KComVariant(index), &spLstCol);
    if (FAILED(hr))
        return hr;

    pListObject->get_Range(&colRg);
    colRg.SetColFromTo(dataRg.ColFrom() + index - 1);
    RANGE colDataRg(dataRg);
    colDataRg.SetColFromTo(dataRg.ColFrom() + index - 1);

    hr = DbEditColInner(pContext, spLstCol, colDataRg, fieldType);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IFormatHost> spHost = pContext->CreateRangeObj(colRg);
    hr = DbInitDefFont(__X("微软雅黑"), 200, spHost);
    if (FAILED(hr))
        return hr;
    
    return S_OK;
}

HRESULT DbAddRow(ListObject *pListObject, FieldContext *pContext)
{
    RANGE dataRg(pContext->GetBMP());
    pListObject->get_DataBodyRange(&dataRg);
    if (!dataRg.IsValid())
        return E_FAIL;

    // 插入新的表行
    IDX index = dataRg.Height() + 1;  // index从1开始
    ks_stdptr<ListRows> spLstRows;
    ks_stdptr<ListRow> spLstRow;
    HRESULT hr = pListObject->get_ListRows(&spLstRows);
    if (FAILED(hr))
        return hr;
    hr = spLstRows->Add(KComVariant(index), KComVariant(), &spLstRow);
    if (FAILED(hr))
        return hr;

    // 对新增行进行初始化
    RANGE rg(dataRg);
    rg.SetRowFromTo(dataRg.RowFrom() + index - 1);

    hr = DbInitEmptyRow(rg, pContext);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IFormatHost> spHost = pContext->CreateRangeObj(rg);
    return DbInitDefFont(__X("微软雅黑"), 200, spHost);
}

HRESULT DbEditCol(ListObject *pListObject, IDX index, FieldContext *pContext, FieldType fieldType, RANGE &colRg)
{
    // 编辑已有的表列
    ks_stdptr<ListColumns> spLstCols;
    ks_stdptr<ListColumn> spLstCol;
    HRESULT hr = pListObject->get_ListColumns(&spLstCols);
    if (FAILED(hr))
        return hr;
    hr = spLstCols->get_Item(KComVariant(index), &spLstCol);
    if (FAILED(hr))
        return hr;

    RANGE dataRg(pContext->GetBMP());
    pListObject->get_DataBodyRange(&dataRg);
    if (!dataRg.IsValid())
        return E_FAIL;

    pListObject->get_Range(&colRg);
    colRg.SetColFromTo(dataRg.ColFrom() + index - 1);
    RANGE colDataRg(dataRg);
    colDataRg.SetColFromTo(dataRg.ColFrom() + index - 1);

    hr = DbEditColInner(pContext, spLstCol, colDataRg, fieldType);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IFormatHost> spHost = pContext->CreateRangeObj(colRg);
    hr = DbInitDefFont(__X("微软雅黑"), 200, spHost);
    if (FAILED(hr))
        return hr;
    
    return S_OK;
}

class InitialiseEnum : public IFieldEnum
{
public:
    InitialiseEnum() {}
    virtual ~InitialiseEnum() {}

    virtual HRESULT Do(FieldContext *pContext, IDbField *pField, const RANGE &rg, const VALIDATION&) override
    {
        return pField->Initialise(pContext, rg);
    }
};

HRESULT DbInitEmptyRow(RANGE rg, FieldContext *pContext)
{
    FieldsManager *pDbMgr = FieldsManager::Instance();
    InitialiseEnum initialiseEnum;
    HRESULT hr = pDbMgr->EnumFieldsInRANGE(pContext, rg, dftPlaceholder, dbEnumAll, &initialiseEnum);
    if (FAILED(hr))
        return hr;

    return S_OK;
}

HRESULT DbInitEmptyCol(RANGE rg, FieldContext *pContext)
{
    FieldsManager *pDbMgr = FieldsManager::Instance();
    IDbField *pField = pDbMgr->GetField(dftText);
    if (pField == nullptr)
        return E_FAIL;

    HRESULT hr = pField->Set(pContext, rg);
    if (FAILED(hr))
        return hr;

    return S_OK;
}

HRESULT GetDbTable(IKWorkbook *workbook, IDX sheetIdx, ListObject **listObject)
{
    ks_stdptr<_Worksheet> spWorksheet = workbook->GetWorksheets()->GetSheetItem(sheetIdx);
    if (spWorksheet == nullptr)
        return E_FAIL;
    ISheet *pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsOldDbSheet())
        return E_FAIL;

    ks_stdptr<ListObjects> spListObjects;
    HRESULT hr = spWorksheet->get_ListObjects(&spListObjects);
    if (FAILED(hr))
        return hr;

    long nCount = 0;
    spListObjects->get_Count(&nCount);
    if (nCount != 1)
        return E_FAIL;
    return spListObjects->get_Item(KComVariant(1), listObject);
}

HRESULT GetDbTable(IBook *pBook, IDX sheetIdx, ICoreListObject **listObject)
{
    ks_stdptr<ISheet> spSheet;
    HRESULT hr = pBook->GetSheet(sheetIdx, &spSheet);
    if (FAILED(hr) || !spSheet->IsOldDbSheet())
        return E_FAIL;

    ks_stdptr<ICoreListObjects> spListObjects;
    spSheet->GetExtDataItem(edSheetListObjects, (IUnknown **)&spListObjects);
    if (spListObjects == nullptr)
        return E_FAIL;

    size_t nCount = spListObjects->GetCount();
    if (nCount != 1)
        return E_FAIL;
    return spListObjects->GetItem(0, listObject);
}

HRESULT DbInitDefFont(PCWSTR family, WORD height, IFormatHost *pHost)
{
    if (pHost == nullptr)
        return E_INVALIDARG;

    KXF defXf;
    defXf.font.themeFontType = etThemeFontNone;
    defXf.mask.inc_theme_name = 1;
    defXf.font.dyHeight = height;
    defXf.mask.inc_dyHeight = 1;
    xstrncpy_s(defXf.font.name, family, MAX_FONTNAME_CCH);
    defXf.font.name[MAX_FONTNAME_CCH] = __Xc('\0');
    defXf.mask.inc_bFamily = 1;
    return pHost->SetXF(&defXf.mask, &defXf);
}

template<class T>
const T& clamp(const T& value, const T& low, const T& high)
{
    return (value < low) ? low : ((high < value) ? high : value);
}

SheetInitConfig ParseDbSheetInitConfig(const binary_wo::VarObj& param)
{
    SheetInitConfig config;
    if (param.has("initConfig"))
    {
        binary_wo::VarObj initConfig = param.get_s("initConfig");
        if (initConfig.has("nEmptyRows"))
            config.nEmptyRows = clamp(initConfig.field_int32("nEmptyRows"), 0, MAX_ROWS_COUNT_CPTB - 1);
        if (initConfig.has("nZoom"))
            config.nZoom = clamp(initConfig.field_int32("nZoom"), MIN_ZOOM, MAX_ZOOM);
        if (initConfig.has("viewName"))
            config.strViewName = initConfig.field_str("viewName");
        if (initConfig.has("rowHeight"))
            config.rowHeight = initConfig.field_int32("rowHeight");
        if (initConfig.has("viewConfigs"))
            config.bHasViewConfigs = true;
    }
    return config;
}

bool  IsFieldTypeContainCf(FieldContext *pContext, const RANGE &rg)
{
    ks_stdptr<IRangeInfo> spRangeInfo = pContext->CreateRangeObj(rg);
    if(!spRangeInfo)
        return false;
    ks_stdptr<IKRanges> spKRanges;
    spRangeInfo->GetIRanges(&spKRanges);

    ks_stdptr<IBook> spBook = pContext->GetBook();
    ks_stdptr<IBookOp> spBookOp;
    if (spBook)
        spBook->GetOperator(&spBookOp);
    if (!spBookOp)
        return false;

    ks_stdptr<IKRanges> spDVRanges;
    spBookOp->GetDataValidationRanges(spKRanges, &spDVRanges);

    UINT nCnt = 0;
    spDVRanges->GetCount(&nCnt);
    for (int i = 0; i < nCnt; i++)
    {
        const RANGE *pRange = NULL;
        spDVRanges->GetItem(i, NULL, &pRange);
        if (pRange == NULL || !pRange->IsValid())
            continue;

        wo::util::VALIDATION_Wrap dv;
        spBookOp->GetDataValidation(*pRange, pRange->SheetFrom(), pRange->RowFrom(), pRange->ColFrom(), &dv, NULL, NULL, NULL);
        IDbField *pField  = FieldsManager::Instance()->IdentifyAll(pContext, *pRange, dv);
        if (pField)
        {
            FieldType type = pField->GetType();
            if(dftSchedule == type || dftRating == type)
                return true;
        }
    }

    return false;
}

RANGE Str2Range(IBook* pBook, PCWSTR str)
{
    RANGE invalidRange(pBook->GetBMP());
    range_helper::ranges ranges;
    CS_COMPILE_FLAGS ccf = cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo | cpfForbidCrossBook;
    CS_COMPILE_PARAM compileParam(ccf, 0, 0, 0);
    HRESULT hr = pBook->LeakOperator()->CompileRange(str, compileParam, &ranges, croOnlyRefer);
    if (FAILED(hr))
        return invalidRange;

    UINT rangeCount = 0;
    ranges->GetCount(&rangeCount);
    if (rangeCount != 1)
        return invalidRange;
    return *ranges.at(0).second;
}

class FindQRColEnum : public IFieldEnum
{
public:
    FindQRColEnum(GenQRLabelCol& qrCol, PCWSTR pageId) : m_qrCol(qrCol)
    {
        if (pageId)
        {
            m_pageIdPosSptr = QString("\"%1\"&").arg(pageId);
            qrCol.pageId =pageId;
        }
        m_genQRLabelPosSptr = krt::fromUtf16(__X("=GENQRLABEL\",FALSE),\""));
    }
    virtual ~FindQRColEnum() = default;
    HRESULT Do(FieldContext *pContext, IDbField *pField, const RANGE& rg, const VALIDATION& dv) override
    {
        if (!dv.bsFormula1)
            return S_OK;

        QString formula = krt::fromUtf16(dv.bsFormula1);
        QString configStr;
        if (m_pageIdPosSptr.isEmpty())
        {
            int pos = formula.indexOf(m_genQRLabelPosSptr, 0, Qt::CaseInsensitive);
            if (pos == -1)
                return S_OK;

            QString str = formula.mid(pos + m_genQRLabelPosSptr.size());
            // pageid"&$0:$0+$F:$F)
            pos = str.indexOf("\"&");
            if (pos == -1)
                return S_OK;

            QString pageId = str.mid(0, pos);
            if (pageId.isEmpty())
                return S_OK;

            m_qrCol.pageId = krt::utf16(pageId);
            configStr = str.mid(pos + 2);
        }
        else
        {
            int pos = formula.indexOf(m_pageIdPosSptr);
            if (pos == -1)
                return S_OK;

            configStr = formula.mid(pos + m_pageIdPosSptr.size());
        }
        configStr.chop(1); // 移除最后的括号
        QStringList configItems = configStr.split("+");
        if (configItems.size() < 2)
            return S_OK;

        m_qrCol.col = rg.ColFrom();
        IBook* pBook = pContext->GetBook();
        RANGE headerRowRange = Str2Range(pBook, krt::utf16(configItems[0]));
        m_qrCol.headerRow = headerRowRange.RangeType() == rtRows ? headerRowRange.RowFrom() : INVALID_ROW;
        RANGE keyColRange = Str2Range(pBook, krt::utf16(configItems[1]));
        m_qrCol.keyCol = keyColRange.RangeType() == rtCols ? keyColRange.ColFrom() : INVALID_COL;
        for (int i = 2, itemCount = configItems.size(); i < itemCount; ++i)
        {
            RANGE colRange = Str2Range(pBook, krt::utf16(configItems[i]));
            COL refCol = colRange.RangeType() == rtCols ? colRange.ColFrom() : INVALID_COL;
            m_qrCol.refCols.push_back(refCol);
        }
        return S_OK;
    }

    bool ShouldStopEnum() override
    {
        return m_qrCol.col != INVALID_COL;
    }
private:
    QString m_pageIdPosSptr;
    QString m_genQRLabelPosSptr;
    GenQRLabelCol& m_qrCol;
};

HRESULT GetQRColByPageId(FieldContext *pFieldContext, IDX sheetIdx, PCWSTR pageId, GenQRLabelCol& qrCol)
{
    IBook* pBook = pFieldContext->GetBook();
    RANGE usedRange(pBook->GetBMP());
    IKWorksheet* pWorkSheet = pFieldContext->GetWorksheets()->GetSheetItem(sheetIdx);
    pWorkSheet->GetUsedRange(&usedRange);
    usedRange.SetRowFromTo(1);
    FieldsManager *pDbMgr = FieldsManager::Instance();
    FindQRColEnum findQrColEnum(qrCol, pageId);
    return pDbMgr->EnumFieldsInRANGE(pFieldContext, usedRange, dftGenQRLabel, dbEnumType, &findQrColEnum);
}

HRESULT GetQRCol(FieldContext* pFieldContext, IDX sheetIdx, COL col, GenQRLabelCol& qrCol)
{
    IBook* pBook = pFieldContext->GetBook();
    RANGE range(pBook->GetBMP());
    range.SetCell(sheetIdx, 1, col);
    FieldsManager *pDbMgr = FieldsManager::Instance();
    FindQRColEnum findQrColEnum(qrCol, nullptr);
    return pDbMgr->EnumFieldsInRANGE(pFieldContext, range, dftGenQRLabel, dbEnumType, &findQrColEnum);
}

UINT GetQRColCount(FieldContext* pFieldContext, IDX sheetIdx)
{
    IBook* pBook = pFieldContext->GetBook();
    RANGE range(pBook->GetBMP());
    range.SetRows(sheetIdx, sheetIdx, 1, 1);
    class QRColCountEnum : public IFieldEnum
    {
    public:
        HRESULT Do(FieldContext *pContext, IDbField *pField, const RANGE& rg, const VALIDATION& dv) override
        {
            if (rg.IsValid())
            {
                int colCount = rg.ColTo() - rg.ColFrom() + 1;
                m_count += colCount;
            }
            return S_OK;
        }
        UINT m_count = 0;
    };
    QRColCountEnum qrColCountEnum;
    FieldsManager::Instance()->EnumFieldsInRANGE(pFieldContext, range, dftGenQRLabel, dbEnumType, &qrColCountEnum);
    return qrColCountEnum.m_count;
}

bool ParseQRColFromVarObj(BMP_PTR pBmp, const binary_wo::VarObj& obj, GenQRLabelCol& qrCol)
{
    constexpr int maxQRRefColCount = 20;
    if (!obj.has("pageId") || !obj.has("headerRow") || !obj.has("keyCol") || !obj.has("refCols"))
        return false;

    qrCol.pageId = obj.field_str("pageId");
    if (qrCol.pageId.empty())
        return false;

    QRegExp regex(krt::fromUtf16(__X("^[a-zA-Z0-9]+$")));
    if (!regex.exactMatch(krt::fromUtf16(qrCol.pageId.c_str())))
        return false;

    qrCol.headerRow = obj.field_int32("headerRow");
    if (qrCol.headerRow < 0 || qrCol.headerRow > pBmp->cntRows -1)
        return false;

    qrCol.keyCol = obj.field_int32("keyCol");
    if (qrCol.keyCol < 0 || qrCol.keyCol > pBmp->cntCols -1)
        return false;

    // 二维码列的关键列不能是自己
    if (qrCol.col != INVALID_COL && qrCol.keyCol == qrCol.col)
        return false;

    VarObj refCols = obj.get_s("refCols");
    INT32 refColLen = refCols.arrayLength();
    if (refColLen > maxQRRefColCount)
        return false;

    for (int i = 0; i < refColLen; i++)
    {
        INT32 col = refCols.item_int32(i);
        if (col < 0 || col > pBmp->cntCols -1)
            return false;

        qrCol.refCols.push_back(col);
    }
    return true;
}

void WriteQRColToVarObj(const GenQRLabelCol& qrCol, binary_wo::VarObj& obj)
{
    obj.add_field_str("pageId", qrCol.pageId.c_str());
    obj.add_field_int32("headerRow", qrCol.headerRow);
    obj.add_field_int32("keyCol", qrCol.keyCol);
    binary_wo::VarObj refCols = obj.add_field_array("refCols", binary_wo::typeInt32);
    for (COL col: qrCol.refCols)
        refCols.add_item_int32(col);
}

HRESULT EnumQRColPageId(FieldContext *pContext, const RANGE& range, const std::function<void(PCWSTR pageId)>& func)
{
    class QRColEnum : public IFieldEnum
    {
    public:
        explicit QRColEnum(const std::function<void(PCWSTR pageId)>& callback) : m_callback(callback)
        {
            m_genQRLabelPosSptr = krt::fromUtf16(__X("=GENQRLABEL\",FALSE),\""));
        }

        HRESULT Do(FieldContext *pContext, IDbField *pField, const RANGE& rg, const VALIDATION& dv) override
        {
            if (!dv.bsFormula1)
                return S_OK;

            QString formula = krt::fromUtf16(dv.bsFormula1);
            int pos = formula.indexOf(m_genQRLabelPosSptr, 0, Qt::CaseInsensitive);
            if (pos == -1)
                return S_OK;

            QString str = formula.mid(pos + m_genQRLabelPosSptr.size());
            // pageid"&$0:$0+$F:$F)
            pos = str.indexOf("\"&");
            if (pos == -1)
                return S_OK;

            QString pageId = str.mid(0, pos);
            if (!pageId.isEmpty())
                m_callback(krt::utf16(pageId));
            return S_OK;
        }
    private:
        QString m_genQRLabelPosSptr;
        const std::function<void(PCWSTR pageId)>& m_callback;
    };
    FieldsManager *pDbMgr = FieldsManager::Instance();
    QRColEnum qrColEnum(func);
    return pDbMgr->EnumFieldsInRANGE(pContext, range, dftGenQRLabel, dbEnumType, &qrColEnum);
}

FieldType GetColType(FieldContext* pFieldContext, IDX sheetIdx, COL col)
{
    IBook* pBook = pFieldContext->GetBook();
    RANGE range(pBook->GetBMP());
    range.SetCell(sheetIdx, 1, col);
    wo::util::VALIDATION_Wrap dv;
    ks_stdptr<IBookOp> spBookOp;
    pBook->GetOperator(&spBookOp);
    RANGE* pRgn = nullptr;
    spBookOp->GetDataValidation(range, sheetIdx, range.RowFrom(), range.ColFrom(), &dv, nullptr, pRgn, nullptr);
    FieldsManager* pFieldsManager = FieldsManager::Instance();
    IDbField* pField = pFieldsManager->IdentifyAll(pFieldContext, range, dv);
    return pField ? pField->GetType() : dftInvalid;
}

bool IsCellTextHasSeparatorSymbol(ISheet* pSheet, const RANGE& rg, WCHAR symChr)
{
	if (pSheet == NULL || !rg.IsValid())
		return false;

	class CheckSeparatorSymbolAcpt : public ICellAcpt
	{
	public:
		CheckSeparatorSymbolAcpt(IETStringTools *pStringTools, ISheet *pSheet, WCHAR symbol)
			: m_pStringTools(pStringTools), m_pSheet(pSheet), m_symbol(symbol)
		{
		}
		STDPROC_(INT) Do(ROW row, COL col)
		{
			ks_bstr bsText;
			m_pStringTools->GetCellText(m_pSheet, row, col, &bsText, NULL, -1, NULL);
			if (!bsText.empty())
			{
				PCWSTR pText = bsText.c_str();
				for (int i = 0; pText[i] != 0; ++i)
				{
					if (pText[i] == m_symbol)
						return -1;
				}
			}
			return 0;
		}

	protected:
		IETStringTools *m_pStringTools;
		ISheet *m_pSheet;
		WCHAR m_symbol;
	};

	ks_stdptr<IETStringTools> spStringTools;
	_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void **)&spStringTools);
	spStringTools->SetEnv(pSheet);

	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
	CheckSeparatorSymbolAcpt cssAcpt(spStringTools, pSheet, symChr);
	if (-1 == spSheetEnum->EnumCell(rg, &cssAcpt))
		return true;
	
	return false;
}

DWORD CSTR2ARGB(const WCHAR* pwszColor)
{
	DWORD argb = 0;
	unsigned int r = 0xff;
	unsigned int g = 0xff;
	unsigned int b = 0xff;
	unsigned int a = 0xff;

	//防止传入常量字符串，造成swscanf崩溃
	ks_wstring strColor(pwszColor);

	if ((pwszColor != NULL) && (*pwszColor == '#') && strColor.length() == 7)//RGB
	{

#ifdef X_OS_WINDOWS
		swscanf(strColor.c_str(), L"#%2x%2x%2x", &b, &g, &r);
#else
		QString str = krt::fromUtf16(strColor.c_str());
		ks_sscanf_s(str.toLocal8Bit(), "#%2x%2x%2x", &b, &g, &r);
#endif
		//请注意这里并不是将上面三种(r,g,b)基本颜色组成RGB颜色
		//而是组成ARGB颜色的低三个字节。组成RGB颜色应该写成
		//rgb = RGB(r,g,b),下同
		argb = RGB(r, g, b);
		argb |= 0xff000000;

	}
	return argb;
}

} // Utils
} // Database
} // wo
