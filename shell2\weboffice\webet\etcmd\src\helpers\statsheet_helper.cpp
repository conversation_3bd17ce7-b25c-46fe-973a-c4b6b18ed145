﻿#include "statsheet_helper.h"
#include "etstdafx.h"
#include "workbook.h"
#include "wo/core_stake.h"

#include "util.h"

#include "et_hard_define_strings.h"
#include "et_revision_context_impl.h"
#include "database/database_utils.h"
#include "dbsheet/et_dbsheet_filter_helper.h"

namespace wo
{

static HRESULT GetSheetById(KEtWorkbook* pWorkbook, UINT sheetId, OUT ISheet** ppSheet)
{
	ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
	if(!spWorksheets)
		return E_FAIL;

	_Workbook* pWb = pWorkbook->GetCoreWorkbook();
	IBook* pBook = pWb->GetBook();

	long sheetCount = 0;
    	spWorksheets->get_Count(&sheetCount);
	IDX sheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(sheetId, &sheetIdx);
	if(sheetIdx < 0 || sheetIdx >= sheetCount)
	{
		WOLOG_ERROR << "[CreateStatSheet] sheetIdx < 0 || sheetIdx >= sheetCount error";
		return E_FAIL;
	}

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if(!spSheet || !spSheet->IsDbSheet())
	{
		WOLOG_ERROR << "[CreateStatSheet] spSheet nullptr";
		return E_FAIL;
	}

	*ppSheet = spSheet.detach();
	return S_OK;
}

HRESULT initStatDbSheet(const VarObj&param , KEtWorkbook*pWorkbook, _Worksheet* pWorksheet, IDBProtectionJudgement*pProtectionJudgement)
{
	HRESULT hr = E_FAIL;
	Database::SheetInitConfig config = Database::Utils::ParseDbSheetInitConfig(param);

	//1.创建统计表sheet
	ISheet *pStatSheet = pWorksheet->GetSheet();
	VarObj statSheetInfo = param.get("statSheetInfo");
	UINT datasourceDbSheetId = statSheetInfo.field_uint32("datasourceDbSheetId");
	
	VarObj groupConditionFieldInitList = statSheetInfo.get("groupConditionFieldInitList");
	int groupFieldsCnt = groupConditionFieldInitList.arrayLength_s();
	VarObj statisticSettingFieldInitList = statSheetInfo.get("statisticSettingFieldInitList");
	int statFieldsCnt = statisticSettingFieldInitList.arrayLength_s();

	if (groupFieldsCnt == 0 || statFieldsCnt == 0)
		return E_FAIL;

	int nFields = groupFieldsCnt + statFieldsCnt;
	DbSheet::DisableDbProtectScope disPtScope(pProtectionJudgement);
	DbSheet::DisableDbTrackHistoryScope scope;
	app_helper::KBatchUpdateCal buc(pWorkbook->GetCoreWorkbook()->GetBook()->LeakOperator());
	hr = DbSheet::ConfigureNewSheet(pWorksheet, pProtectionJudgement, config.nEmptyRows, nFields, config);
	if (FAILED(hr))
		return hr;

	// 添加视图
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	hr = DbSheet::GetDBSheetViews(pStatSheet, &spDbSheetViews);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	KSheet_Book_Version bookVersion = KSheet_Book_Version_Base;
	ks_stdptr<IUnknown> spUnk;
	pStatSheet->LeakBook()->GetExtDataItem(edFileVersion, &spUnk);
	ks_stdptr<IKEtFileVersion> spEtFileVersion = spUnk;
	if (spEtFileVersion)
		bookVersion = spEtFileVersion->GetKSheetBookVersion();
	if (pStatSheet->GetBMP()->bKsheet && bookVersion == KSheet_Book_Version_AppSheet)
		spDbSheetView = spDbSheetViews->GetDefaultView();
	else
	{
		hr = spDbSheetViews->CreateView(et_DBSheetView_Grid, config.strViewName, false, Et_DBSheetViewUse_ForDb, &spDbSheetView);
		if (FAILED(hr))
			return E_FAIL;
	}

	if(statSheetInfo.has("viewNotice"))
	{
		hr = spDbSheetView->SetNotice(statSheetInfo.field_str("viewNotice"));
		if (FAILED(hr))
			return E_FAIL;
	}

	ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = spDbSheetView;
	if (spDbSheetViewGrid)
	{
		hr = spDbSheetViewGrid->SetRecordsHeight(config.rowHeight);
		if (FAILED(hr))
			return hr;
	}

	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
	const IDBIds *pFields = spDbSheetView->GetAllFields();

	//ext上挂载一个IDBStatisticSheetData
	ks_stdptr<IUnknown> spUnknown;
	ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
	if (FAILED(pStatSheet->GetExtDataItem(edStatisticSheetData, &spUnknown)))
	{
		VS(_appcore_CreateObject(CLSID_KDBStatisticSheetData, IID_IDBStatisticSheetData, (void**)&spDBStatisticSheetData));
		VS(pStatSheet->SetExtDataItem(edStatisticSheetData, spDBStatisticSheetData));
		spDBStatisticSheetData->Init(pStatSheet);
	}
	if(!spDBStatisticSheetData)
		return E_FAIL;

	//2.给数据源dbsheet添加一个视图，并且在上面设置分组和统计。
	ks_stdptr<ISheet> spSourceSheet;
	hr = GetSheetById(pWorkbook, datasourceDbSheetId, &spSourceSheet);
	if(FAILED(hr) || !spSourceSheet)
		return hr;
	
	//统计表绑定数据源的信息。
	spDBStatisticSheetData->BindDataSourceSheet(spSourceSheet);

	ks_stdptr<IDBSheetOp> spSourceSheetOp;
	ks_stdptr<IDBSheetOp> spStatSheetOp;
	VS(DbSheet::GetDBSheetOp(spSourceSheet, &spSourceSheetOp));
	VS(DbSheet::GetDBSheetOp(pStatSheet, &spStatSheetOp));
	DbSheet::DbtBatchSubmitOptimizer batchSourceSheetSubmitter(spSourceSheetOp.get());
	DbSheet::DbtBatchSubmitOptimizer batchStatSheetSubmitter(spStatSheetOp.get());

	//设置统计条件和分组条件。
	IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
	int curFieldIdx = 0;
	for (int i = 0, length = groupConditionFieldInitList.arrayLength(); i < length; ++i)
	{
		binary_wo::VarObj item = groupConditionFieldInitList.at(i);
		EtDbId dataSourceSheetFieldId = DbSheet::GetEtDbId(item, "fieldId");
		ks_stdptr<IDbField> spField;
		EtDbId fldId = pFields->IdAt(curFieldIdx++);
		HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return E_FAIL;

		KDBGroupUnit groupUnit = DBGU_Text;
		if (item.has("groupUnit"))
		{
			hr = pDecoder->DecodeGroupUnit(item.field_str("groupUnit"), &groupUnit);
			if (FAILED(hr))
			return hr;
		}

		hr = spDBStatisticSheetData->AddGroupFieldSetting(dataSourceSheetFieldId, fldId, groupUnit);
		if (FAILED(hr))
			return E_FAIL;

		UINT fieldWidth = item.field_uint32("fieldWidth");
		EtDbId fieldIdInStatSheet = spDBStatisticSheetData->GetGroupFieldId(dataSourceSheetFieldId);
		if(fieldIdInStatSheet == INV_EtDbId)
			return E_FAIL;
		hr = spDbSheetViewGrid->SetFieldWidth(fieldIdInStatSheet, fieldWidth, TRUE); 
		if (FAILED(hr))
			return hr;
	}

	for (int i = 0, length = statisticSettingFieldInitList.arrayLength(); i < length; ++i)
	{
		binary_wo::VarObj item = statisticSettingFieldInitList.at(i);
		EtDbId dataSourceSheetFieldId = DbSheet::GetEtDbId(item, "fieldId");
		ks_stdptr<IDbField> spField;
		EtDbId fldId = pFields->IdAt(curFieldIdx++);
		HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return E_FAIL;

		ET_DBSheet_StatisticOption option = DBSSO_null;
		hr = pDecoder->DecodeStatisticOption(item.field_str("statisticOptions"), &option);
		if (FAILED(hr))
			return hr;
		hr = spDBStatisticSheetData->AddStatisticFieldSetting(dataSourceSheetFieldId, fldId, option);
		if (FAILED(hr))
			return E_FAIL;
		
		UINT fieldWidth = item.field_uint32("fieldWidth");
		EtDbId fieldIdInStatSheet = spDBStatisticSheetData->GetStatisticFieldId(dataSourceSheetFieldId);
		if(fieldIdInStatSheet == INV_EtDbId)
			return E_FAIL;
		hr = spDbSheetViewGrid->SetFieldWidth(fieldIdInStatSheet, fieldWidth, TRUE); 
		if (FAILED(hr))
			return hr;
	}

	spDBStatisticSheetData->CallDbGroupbyFunc();
	return S_OK;
}

HRESULT SetStatSheetSetting(const VarObj& param, KEtWorkbook* pWorkbook, ISheet* pStatSheet)
{
	HRESULT hr = E_FAIL;
	if(!pStatSheet->IsDbSheet())
		return E_FAIL;

	ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
	pStatSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
	if(!spDBStatisticSheetData)
		return E_FAIL;

	IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
	_Workbook* pWb = pWorkbook->GetCoreWorkbook();
	IBook* pBook = pWb->GetBook();

	DbSheet::DisableDbTrackHistoryScope scope;
	app_helper::KBatchUpdateCal buc(pWorkbook->GetCoreWorkbook()->GetBook()->LeakOperator());
	ks_stdptr<IDBSheetOp> spStatSheetOp;
	VS(DbSheet::GetDBSheetOp(pStatSheet, &spStatSheetOp));
	DbSheet::DbtBatchSubmitOptimizer batchStatSheetSubmitter(spStatSheetOp.get());
	
	if (param.has("groupConditionFieldAddList"))
	{
		binary_wo::VarObj array = param.get("groupConditionFieldAddList");
		for (int i = 0, length = array.arrayLength(); i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			KDBGroupUnit groupUnit = DBGU_Text;
			if (item.has("groupUnit"))
			{
				hr = pDecoder->DecodeGroupUnit(item.field_str("groupUnit"), &groupUnit);
				if (FAILED(hr))
				return hr;
			}
			hr = spDBStatisticSheetData->AddGroupField(DbSheet::GetEtDbId(item, "fieldId"), groupUnit);
			if (FAILED(hr))
				return hr;
		}
	}
	
	if (param.has("groupConditionFieldRemoveList"))
	{
		binary_wo::VarObj array = param.get("groupConditionFieldRemoveList");
		for (int i = 0, length = array.arrayLength(); i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			//删除统计表对应的生成的分组字段 以及 数据源的虚拟视图上的groupSetting
			hr = spDBStatisticSheetData->RemoveGroupField(DbSheet::GetEtDbId(item, "fieldId"));
			if (FAILED(hr))
				return hr;
		}
	}

	if (param.has("groupConditionFieldChangeList"))
	{
		binary_wo::VarObj array = param.get("groupConditionFieldChangeList");
		for (int i = 0, length = array.arrayLength(); i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			EtDbId oldFieldId = DbSheet::GetEtDbId(item, "oldFieldId");
			EtDbId newFieldId = DbSheet::GetEtDbId(item, "newFieldId");
			KDBGroupUnit groupUnit = DBGU_Text;
			if (item.has("groupUnit"))
			{
				hr = pDecoder->DecodeGroupUnit(item.field_str("groupUnit"), &groupUnit);
				if (FAILED(hr))
					return hr;
			}
			hr = spDBStatisticSheetData->ChangeGroupField(oldFieldId, newFieldId, groupUnit);
			if (FAILED(hr))
				return hr;
		}
	}

	if (param.has("statisticSettingFieldAddList"))
	{
		binary_wo::VarObj array = param.get("statisticSettingFieldAddList");
		int length = array.arrayLength();
		for (int i = 0; i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			ET_DBSheet_StatisticOption option = DBSSO_null;
			hr = pDecoder->DecodeStatisticOption(item.field_str("statisticOptions"), &option);
			if (FAILED(hr))
				return hr;
			hr = spDBStatisticSheetData->AddStatisticField(DbSheet::GetEtDbId(item, "fieldId"), option);
			if (FAILED(hr))
				return hr;
		}
	}
	if (param.has("statisticSettingFieldRemoveList"))
	{
		binary_wo::VarObj array = param.get("statisticSettingFieldRemoveList");
		for (int i = 0, length = array.arrayLength(); i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			//删除统计表对应的生成的统计字段 以及 数据源的虚拟视图上的statSetting
			hr = spDBStatisticSheetData->RemoveStatisticField(DbSheet::GetEtDbId(item, "fieldId"));
			if (FAILED(hr))
			return hr;
		}
	}

	if (param.has("statisticSettingFieldChangeList"))
	{
		binary_wo::VarObj array = param.get("statisticSettingFieldChangeList");
		int length = array.arrayLength();
		for (int i = 0; i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			EtDbId oldFieldId = DbSheet::GetEtDbId(item, "oldFieldId");
			EtDbId newFieldId = DbSheet::GetEtDbId(item, "newFieldId");

			ET_DBSheet_StatisticOption option = DBSSO_null;
			if (item.has("statisticOptions"))
			{
				hr = pDecoder->DecodeStatisticOption(item.field_str("statisticOptions"), &option);
				if (FAILED(hr))
					return hr; 
			}
			else
			{
				return E_FAIL;
			}
			
			hr = spDBStatisticSheetData->ChangeStatisticField(oldFieldId, newFieldId, option);
			if (FAILED(hr))
				return hr;
		}
	}

	//筛选相关
	if (param.has("filtersOp")) 
	{
		KDbFilterOpType op = DBFOT_And;
		hr = pDecoder->DecodeKDbFilterOpType(param.field_str("filtersOp"), &op);
		if (FAILED(hr))
			return hr;
		hr = spDBStatisticSheetData->SetFiltersOperator(op);
		if (FAILED(hr))
			return hr;
	}
	if (param.has("clearFilter") && param.field_bool("clearFilter"))
	{
		hr = spDBStatisticSheetData->ClearFilter();
		if (FAILED(hr))
			return hr;
	}
	if (param.has("filterCriteriasRemoveList"))
	{
		binary_wo::VarObj array = param.get("filterCriteriasRemoveList");
		for (int i = 0, length = array.arrayLength(); i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			hr = spDBStatisticSheetData->RemoveFilterCriterior(DbSheet::GetEtDbId(item, "fieldId"));
			if (FAILED(hr))
				return hr;
		}
	}
	if (param.has("filterCriteriasAddList"))
	{
		binary_wo::VarObj array = param.get("filterCriteriasAddList");
		for (int i = 0, length = array.arrayLength(); i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			ks_stdptr<IDbFilterCriteria> spCriteria;
			hr = CreateDbFilterCriteria(item.get("criteria"), pBook, &spCriteria);
			if (FAILED(hr))
				return hr;
			hr = spDBStatisticSheetData->AddFilterCriterior(DbSheet::GetEtDbId(item, "fieldId"), spCriteria);
			if (FAILED(hr))
				return hr;
		}
	}
	spDBStatisticSheetData->ChangeStatSheetFormulaStatus(DbSheet_ResetDbgroupby);
	return S_OK;
}

HRESULT ActivateStatSheetByResetSettingField(const VarObj& param, KEtWorkbook*pWorkbook, ISheet* pStatSheet)
{
	HRESULT hr = E_FAIL;
	if(!pStatSheet->IsDbSheet())
		return E_FAIL;

	ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
	pStatSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
	if(!spDBStatisticSheetData)
		return E_FAIL;

	IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();

	std::vector<EtDbId> dataSourceGroupFieldIdVec;
	std::vector<KDBGroupUnit> groupUnitVec;

	std::vector<EtDbId> dataSourceStatFieldIdVec;
	std::vector<ET_DBSheet_StatisticOption> optionVec;

	if (!param.has("groupConditionFieldAddList") || !param.has("statisticSettingFieldAddList"))
	{
		WOLOG_ERROR << "[ActivateStatSheetByResetSettingField] param named groupConditionFieldAddList or statisticSettingFieldAddList invalid!";
		return E_FAIL;
	}

	{
		binary_wo::VarObj array = param.get("groupConditionFieldAddList");
		int length = array.arrayLength();
		if(length <= 0)
		{
			WOLOG_ERROR << "[ActivateStatSheetByResetSettingField] param named groupConditionFieldAddList array is empty error!";
			return E_FAIL;
		}

		for (int i = 0, length = array.arrayLength(); i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			KDBGroupUnit groupUnit = DBGU_Text;
			if (item.has("groupUnit"))
			{
				hr = pDecoder->DecodeGroupUnit(item.field_str("groupUnit"), &groupUnit);
				if (FAILED(hr))
				return hr;
			}
			EtDbId dataSourceGroupFieldId =  DbSheet::GetEtDbId(item, "fieldId");
			dataSourceGroupFieldIdVec.push_back(dataSourceGroupFieldId);
			groupUnitVec.push_back(groupUnit);
		}
	}

	{
		binary_wo::VarObj array = param.get("statisticSettingFieldAddList");
		int length = array.arrayLength();
		if(length <= 0)
		{
			WOLOG_ERROR << "[ActivateStatSheetByResetSettingField] param named statisticSettingFieldAddList array is empty error!";
			return E_FAIL;
		}
		for (int i = 0; i < length; ++i)
		{
			binary_wo::VarObj item = array.at(i);
			ET_DBSheet_StatisticOption option = DBSSO_null;
			hr = pDecoder->DecodeStatisticOption(item.field_str("statisticOptions"), &option);
			if (FAILED(hr))
				return hr;
			EtDbId dataSourceStatFieldId =  DbSheet::GetEtDbId(item, "fieldId");
			dataSourceStatFieldIdVec.push_back(dataSourceStatFieldId);
			optionVec.push_back(option);
		}
	}

	hr = spDBStatisticSheetData->ResetSettingField2Activate(dataSourceGroupFieldIdVec.data(), groupUnitVec.data(), dataSourceGroupFieldIdVec.size(),
		dataSourceStatFieldIdVec.data(), optionVec.data(), dataSourceStatFieldIdVec.size());
	spDBStatisticSheetData->ChangeStatSheetFormulaStatus(DbSheet_ResetDbgroupby);
	return hr;
}

HRESULT RecoverStatSheet2NormalDbSheet(ISheet* pStatSheet)
{
	HRESULT hr = E_FAIL;
	if(!pStatSheet->IsDbSheet())
		return E_FAIL;
    ks_stdptr<IDBSheetOp> spDbSheetOp;
	VS(DbSheet::GetDBSheetOp(pStatSheet, &spDbSheetOp));
	ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
	pStatSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
	if(!spDBStatisticSheetData)
		return E_FAIL;

	std::unordered_map<std::pair<EtDbId, EtDbId>, alg::managed_token_assist> values {};
	ks_stdptr<IDBSheetRange> spRange;
	app_helper::KBatchUpdateCal _(pStatSheet->LeakBook()->LeakOperator());
	{
		auto position {values.end()};
		const auto pRecordIDs {spDbSheetOp->GetAllRecords()}, pFieldIDs {spDbSheetOp->GetAllFields()};
		const auto recordCount {pRecordIDs->Count()}, fieldCount {pFieldIDs->Count()};
		spDbSheetOp->CreateDBSheetRange(&spRange);
		const auto formulaColumnFrom {spDBStatisticSheetData->GetFormulaColIdx()};
		const IDbFieldsManager* const pAllFieldsManager {spDbSheetOp->GetFieldsManager()};
		for (EtDbIdx fieldIndex {0}; fieldIndex < fieldCount; ++fieldIndex)
		{
			if (fieldIndex < formulaColumnFrom)
				continue;
			const auto fieldID {pFieldIDs->IdAt(fieldIndex)};
			spRange->AddFieldId(fieldID);
			for (EtDbIdx recordIndex {0}; recordIndex < recordCount; ++recordIndex)
			{
				const auto recordID {pRecordIDs->IdAt(recordIndex)};
				spRange->AddRecordId(recordID);
				const_token_ptr token {};
				spDbSheetOp->GetValueToken(recordID, fieldID, &token);
				ks_stdptr<IDbField> spField {};
				pAllFieldsManager->GetField(fieldID, &spField);
				ASSERT(spField);
				if (DbSheet::statistic_sheet_helper::needDiscardZeroDoubleToken(spField, token))
					continue;
				alg::managed_token_assist assist {};
				assist.clone_from(token);
				if (token)
					position = values.emplace_hint(position, std::make_pair(recordID, fieldID), assist.detach());
			}
		}
		spDbSheetOp->Clear(spRange, TRUE);
	}

	//在移除ext拓展对象之前，先调用RecoverStatSheet2NormalDbSheet来把一些信息（例如 数据源表的虚拟视图 统计表的subSheetType等）给recover
	hr = spDBStatisticSheetData->RecoverStatSheet2NormalDbSheet();
	if(FAILED(hr))
		return hr;
	
	//这里必须得把统计表edStatisticSheetData拓展对象给置为空，后续存盘后，此拓展对象不在的话，就不会去走init，
	//也就不会通过SetSheetSubType设置设置dbsheet的子类型为统计表。
	//保证了普通sheet的subType一定是 DbSheet_Sub_Type_Normal
	//减少引用记数，让其后面引用记数为0手动释放拓展对象。
	hr = pStatSheet->SetExtDataItem(edStatisticSheetData, nullptr);
	if(FAILED(hr))
		return hr;

	// 在 etcore 中将 CellNode 的 result value 转成 const value 也需要经过一次取值设值
	// 效率差异应该不大, 暂时采用这一套方案
	for (const auto &pair : values)
	{
		spDbSheetOp->SetTokenValue(pair.first.first, pair.first.second, pair.second);
	}
	using namespace DbSheet::array_field_fall_back;
	const auto pFields {spDbSheetOp->GetFieldsManager()};
	const auto fieldCount {spRange->GetFieldCnt()};
	for (UINT32 i = 0; i < fieldCount; ++i)
	{
		ks_stdptr<IDbField> spField;
		pFields->GetField(spRange->GetFieldId(i), &spField);
		if (spField)
		{
			spField->SetArray(FALSE);
			fallback(spField);
		}
	}

	// 恢复计算时, context 为空, 所以要清洗单元格中的所有创建者
	const auto pContext {_kso_GetRevisionContext()};
	if (pContext)
	{
		const auto pRecordManager {spDbSheetOp->GetRecordsManager()};
		const auto pRecordIDs {spDbSheetOp->GetAllRecords()};
		const auto recordCount {pRecordIDs->Count()};
		const auto userID {pContext->getUser()->userID()};
		for (UINT i = 0; i < recordCount; ++i)
		{
			const auto ID {pRecordIDs->IdAt(i)};
			double createdTime {};
			pRecordManager->GetRecordCreatedTime(ID, createdTime);
			pRecordManager->UpdateRecord(ID, userID, createdTime);
		}
	}

	return S_OK;
}

bool IsStatSheet(IBook* pBook, UINT sheetStId)
{
	IDX sheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return false;
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet)
		return false;

	if(!spSheet->IsDbSheet())
		return false;

	ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
        	spSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);

	return spDBStatisticSheetData != nullptr;
}

bool IsStatSheetSettingField(IBook* pBook, UINT sheetStId, EtDbId fldId)
{
	IDX sheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return false;

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet)
		return false;

	if(!spSheet->IsDbSheet())
		return false;
	
	ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
	spSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
	if(!spDBStatisticSheetData)
		return false;
	
	return spDBStatisticSheetData->IsSettingFieldId(fldId);
}

void UpdateStatSheetFormula(IBook *pBook)
{
	if (pBook == nullptr)
        return;

	INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    if (sheetCount <= 0)
        return;

	HRESULT hr = S_OK;
	// 遍历所有sheet,如果统计表需要重新设置公式，则调用CallDbGroupbyFunc，减少中间进行创建公式的操作
	for (INT idx = 0; idx < sheetCount; ++idx)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(idx, &spSheet);
		if (spSheet && spSheet->IsDbSheet())
		{
			ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
			spSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
			if(spDBStatisticSheetData)
			{
				if (spDBStatisticSheetData->GetStatSheetFormulaStatus() == DbSheet_ResetDbgroupby)
				{
					spDBStatisticSheetData->ChangeStatSheetFormulaStatus(DbSheet_DoNothing);
					hr = spDBStatisticSheetData->CallDbGroupbyFunc();
					if (FAILED(hr))
						WOLOG_ERROR << "[UpdateStatSheetFormula] Update Failed idxb : " << int(idx);
				}
				// 重新设置公式的等级高于submitchange
				if (spDBStatisticSheetData->GetStatSheetFormulaStatus() == DbSheet_SubmitDbgroupby)
				{
					spDBStatisticSheetData->ChangeStatSheetFormulaStatus(DbSheet_DoNothing);
					spDBStatisticSheetData->SubmitDbGroupbyFunc();
				}
			}
		}
	}
}

}// end namespace wo
