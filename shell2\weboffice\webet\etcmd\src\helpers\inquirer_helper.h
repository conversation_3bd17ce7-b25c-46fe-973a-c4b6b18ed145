#ifndef __WEBET_INQUIRER_HELPER__
#define __WEBET_INQUIRER_HELPER__

#include "etstdafx.h"
#include "workbook.h"

namespace wo
{
class KEtWorkbook;

enum CollectInquirerInfoStatus
{
	CollectInquirerInfoStatus_Success,
	CollectInquirerInfoStatus_Fail_Has_Hidden,
	CollectInquirerInfoStatus_Fail_Rec_TableHead,
	CollectInquirerInfoStatus_Fail_Rec_PhoneCols,
	CollectInquirerInfoStatus_Fail_TblTitle_HorMerge,
	CollectInquirerInfoStatus_Fail_Tbl_Cols_Exceed_Threshold,
	CollectInquirerInfoStatus_Fail_Other_Reason,
};

struct PhoneColsInfo
{
	PhoneColsInfo(int colIdx, const QString& colName)
		:m_colIdx(colIdx)
		,m_colName(colName)
	{

	}	
	int m_colIdx;
	QString m_colName;
};

struct CollectInquirerInfoResult
{
	CollectInquirerInfoResult(CollectInquirerInfoStatus status, const RANGE& rg)
		:m_status(status)
		,m_tableHeadRg(rg)
		,m_titleCnt(0)
	{

	}
	CollectInquirerInfoStatus m_status;//结果状态
	RANGE m_tableHeadRg;//表头的范围
	int m_titleCnt;//标题行数
	std::vector<PhoneColsInfo> m_phoneColsInfo;//列类型为phone类型的信息
};


class InquirerHelper
{
public:
	InquirerHelper(wo::KEtWorkbook* pWb, ISheet* pSheet, int sheetIdx);
	CollectInquirerInfoResult collectInquirerInfo(IDX tblHeadRowFrom, IDX tblHeadRowTo);
	HRESULT exportInquirData(const ks_wstring& condStr, OUT QString& html);
	
private:
	HRESULT _exportInquirData(const RANGE& tableHeadRg, int colIdx, const ks_wstring& condStr, OUT QString& html);
	HRESULT _filterData(const RANGE& tableHeadRg, int colIdx,const ks_wstring& condStr, OUT Range** ppDataAreaRgHost);
	HRESULT _clearFilter();
	HRESULT _cancleRangeColHidden();
	HRESULT _copyHtml(const RANGE& tableHeadRg, QString &html);
	void _clipCopyRgs(RANGE& cpRg, INT32 limitRowCnt);

	bool _checkUserHasHiddenAccess(RANGE& rg);
	HRESULT _setStyle();
private:
	wo::KEtWorkbook* m_pWb;
	ISheet* m_pSheet;
	int m_sheetIdx;
};
} // end namespace wo

#endif // __WEBET_INQUIRER_HELPER__
