#include "etstdafx.h"
#include "operation.h"
#include "kfc/tools/smart_wrap.h"
#include "rect_tf.h"
#include "etcore/et_core_sheet.h"
#include "wo/core_stake.h"
#include "wo/struct_op.h"
#include "etcore/little_alg.h"
#include "et_revision_context_impl.h"
#include "ss_rect.h"
#include "webbase/webdeclare.h"
#include "webetlink.h"
#include "database/database_utils.h"
#include <public_header/drawing/model/abstract_shape.h>

extern Callback* gs_callback;

namespace wo
{
namespace
{

typedef OpBase* (*NewOpFunc)(OpEnv* env,const KwCommand* cmd);
typedef std::unordered_map<ks_wstring, NewOpFunc,
		kfc::tools::str_hash_ic<ks_wstring>, kfc::tools::str_equal_ic<ks_wstring>> NewOpFuncMap;

NewOpFuncMap s_newOpMap;

#define DEF_NEW_OP_FUNC(name) \
	OpBase* New##name(OpEnv* env, const KwCommand* cmd) \
	{\
		return new name(env, cmd); \
	}

DEF_NEW_OP_FUNC(OpOneObjSheet)
DEF_NEW_OP_FUNC(OpSetCellFormula)
DEF_NEW_OP_FUNC(OpRangeSetFormula)
DEF_NEW_OP_FUNC(OpRangeDelete)
DEF_NEW_OP_FUNC(OpRangeInsert)
DEF_NEW_OP_FUNC(OpRangeInsertNumberCol)
DEF_NEW_OP_FUNC(OpRangeExpandAbridge)
DEF_NEW_OP_FUNC(OpPivotGroupRangeExpandAbridge)
DEF_NEW_OP_FUNC(OpRangeMove)
DEF_NEW_OP_FUNC(OpRangeMoveAbridgeSplit)
DEF_NEW_OP_FUNC(OpRangeMoveAbridgeSplitNoSheetIdx)
DEF_NEW_OP_FUNC(OpRangeExpandAbridgeNoSheetIdx)
DEF_NEW_OP_FUNC(OpAutoFill)
DEF_NEW_OP_FUNC(OpSheetsAdd)
DEF_NEW_OP_FUNC(OpSheetsMove)
DEF_NEW_OP_FUNC(OpSheetsCopy)
DEF_NEW_OP_FUNC(OpShapeObj)
DEF_NEW_OP_FUNC(OpHyperlink)
DEF_NEW_OP_FUNC(OpRangePaste)
DEF_NEW_OP_FUNC(OpFreezePanes)
DEF_NEW_OP_FUNC(OpNewConditionFormat)
DEF_NEW_OP_FUNC(OpEditConditionFormat)
DEF_NEW_OP_FUNC(OpDelConditionFormat)
DEF_NEW_OP_FUNC(OpSwapConditionFormat)
DEF_NEW_OP_FUNC(OpRangeAutoSum)
DEF_NEW_OP_FUNC(OpAllowEditRange)
DEF_NEW_OP_FUNC(OpShapePaste)
DEF_NEW_OP_FUNC(OpChartShape)
DEF_NEW_OP_FUNC(OpMultiRangeExpandAbridge)
DEF_NEW_OP_FUNC(OpMultiRangeMoveAbridgeSplit)
DEF_NEW_OP_FUNC(OpSetPageSetting)
DEF_NEW_OP_FUNC(OpDbTableColumn)
DEF_NEW_OP_FUNC(OpDeleteShape)
DEF_NEW_OP_FUNC(OpPivotTableRangeFieldName)
DEF_NEW_OP_FUNC(OpPivotTableSetSourceData)
DEF_NEW_OP_FUNC(OpCreatePivotTable)
DEF_NEW_OP_FUNC(OpsetSubscriptionOption)
DEF_NEW_OP_FUNC(OpMultiSheetsOp)
DEF_NEW_OP_FUNC(OpSetDataSource)
DEF_NEW_OP_FUNC(OpSetSeriesDataSource)
DEF_NEW_OP_FUNC(OpSetCategoryDataSource)
DEF_NEW_OP_FUNC(OpDataLabelOption)
DEF_NEW_OP_FUNC(OpChartDeleteSeries)
DEF_NEW_OP_FUNC(OpChartMoveSeries)

void ValidNewOpMap(bool bDbSheet, bool bKsheet)
{
	if (!s_newOpMap.empty())
		return;

	// et/db共有的命令
	s_newOpMap.insert(std::make_pair(__X("book.recalculate"), nullptr));
	s_newOpMap.insert(std::make_pair(__X("book.importWorkbook"), nullptr));
	s_newOpMap.insert(std::make_pair(__X("sheets.add"),				&NewOpSheetsAdd));
	s_newOpMap.insert(std::make_pair(__X("sheets.move"),			&NewOpSheetsMove));
	s_newOpMap.insert(std::make_pair(__X("sheets.copy"),			&NewOpSheetsCopy));
	s_newOpMap.insert(std::make_pair(__X("sheet.setName"),		&NewOpOneObjSheet));
	s_newOpMap.insert(std::make_pair(__X("sheet.delSheet"),		&NewOpOneObjSheet));
	s_newOpMap.insert(std::make_pair(__X("book.setCustomStorage"), nullptr));

	s_newOpMap.insert(std::make_pair(__X("http.et.setCustomStorage"), nullptr));

	if (!bDbSheet)
	{
		s_newOpMap.insert(std::make_pair(__X("setCellFormula"),			&NewOpSetCellFormula));
		s_newOpMap.insert(std::make_pair(__X("range.setFormula"),		&NewOpRangeSetFormula));
		s_newOpMap.insert(std::make_pair(__X("range.setArrayFormula"),	&NewOpRangeSetFormula));
		s_newOpMap.insert(std::make_pair(__X("range.insert"),			&NewOpRangeInsert));
        s_newOpMap.insert(std::make_pair(__X("range.insertNumberCol"),  &NewOpRangeInsertNumberCol));
		s_newOpMap.insert(std::make_pair(__X("range.mergeinsert"),		&NewOpRangeInsert));
		s_newOpMap.insert(std::make_pair(__X("range.unmergeinsert"),	nullptr));
		s_newOpMap.insert(std::make_pair(__X("range.delete"),			&NewOpRangeDelete));
		s_newOpMap.insert(std::make_pair(__X("multiRange.delete"),		&NewOpMultiRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("range.setRowHeight"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.setColumnWidth"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.merge"),			&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("multiRange.merge"),		&NewOpMultiRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.unMerge"),			&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.monitor"),			&NewOpRangeExpandAbridgeNoSheetIdx));
		s_newOpMap.insert(std::make_pair(__X("range.monitorSplit"),		&NewOpRangeMoveAbridgeSplitNoSheetIdx));
		s_newOpMap.insert(std::make_pair(__X("range.clearContents"),	&NewOpRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("range.clearFormats"),		&NewOpRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("multiRange.clearFormats"),&NewOpMultiRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("range.clear"),			&NewOpRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("range.autoFilter"),		&NewOpRangeMove));
		s_newOpMap.insert(std::make_pair(__X("range.setAutoFilter"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("file.setAutoFilter"),		nullptr));
		s_newOpMap.insert(std::make_pair(__X("range.setXf"),			&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("multiRange.setXf"),		&NewOpMultiRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("range.autoFill"),			&NewOpAutoFill));
		s_newOpMap.insert(std::make_pair(__X("range.fill"),				&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.pasteText"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.autoFit"),			&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.setHidden"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("multiRange.setHidden"),	&NewOpMultiRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.insertCellPicture"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.insertCellPictureUrl"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.insertCellPictureAttachment"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.clearConditionFormatRules"), &NewOpMultiRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("sheet.setStandardWidth"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.setStandardHeight"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.setVisible"),		&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.addHyperlink"),		&NewOpHyperlink));
		s_newOpMap.insert(std::make_pair(__X("sheet.editHyperlink"),	&NewOpHyperlink));
		s_newOpMap.insert(std::make_pair(__X("sheet.deleteHyperlink"),	&NewOpHyperlink));
		s_newOpMap.insert(std::make_pair(__X("sheet.insertPicture"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.insertPictures"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.changePicture"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.changePictureAttachment"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.insertProcessOn"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.insertPictureAttachment"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.insertPictureAttachments"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.calculate"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("etSheet.addWebExtension"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("etSheet.modifyWebExtension"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("book.blockUploadAllPicturesToAttachment"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.setShapeAnchor"),	&NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("sheet.setShapePos"),	&NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("sheet.setShapeSize"),	&NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("sheet.modifyProcessOn"),	&NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("sheet.deleteShape"),	&NewOpDeleteShape));
		s_newOpMap.insert(std::make_pair(__X("sheet.reapplyFilter"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("range.insertCommentItem"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.deleteCommentItem"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.editCommentItem"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.setCommentProp"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.deleteComment"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.setCommentChainResolved"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.deleteCommentChain"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.sort"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.paste"),				&NewOpRangePaste));
		s_newOpMap.insert(std::make_pair(__X("range.importCsvFile"),		&NewOpRangePaste));
		s_newOpMap.insert(std::make_pair(__X("range.setTableStyle"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.batchSetTableStyle"),	&NewOpMultiRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.replace"),				&NewOpRangeMove));
		s_newOpMap.insert(std::make_pair(__X("range.gainLinkName"),			&NewOpRangeMove));
		s_newOpMap.insert(std::make_pair(__X("range.autoSum"),			&NewOpRangeAutoSum));
		s_newOpMap.insert(std::make_pair(__X("form.addRow"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("form.addRows"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("form.addCol"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("form.editRow"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("form.deleteRow"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("form.sort"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("form.addSheet"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("form.addName"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("form.setColumnWidth"),				nullptr));
		s_newOpMap.insert(std::make_pair(__X("range.fmtPaintPaste"),		&NewOpRangePaste));
		s_newOpMap.insert(std::make_pair(__X("range.pasteSpecail"),			&NewOpRangePaste));
		s_newOpMap.insert(std::make_pair(__X("range.pasteSpecailByHtml"),	&NewOpRangePaste));
		s_newOpMap.insert(std::make_pair(__X("range.pasteSmart"),			&NewOpRangePaste));
		s_newOpMap.insert(std::make_pair(__X("range.pasteSpreadSheetSmart"),		&NewOpRangePaste));
		s_newOpMap.insert(std::make_pair(__X("range.fmtPaintPaste"),		&NewOpRangePaste));
		s_newOpMap.insert(std::make_pair(__X("sheet.freezeFirstVisibleRowOrCol"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.putFreezePanes"), &NewOpFreezePanes));
		s_newOpMap.insert(std::make_pair(__X("range.toggleGroup"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("sheet.setCollapseByGroup"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.setCollapseByLevel"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.newConditionFormatRule"), &NewOpNewConditionFormat));
		s_newOpMap.insert(std::make_pair(__X("sheet.delConditionFormatRule"), &NewOpDelConditionFormat));
		s_newOpMap.insert(std::make_pair(__X("sheet.clearAllConditionFormatRules"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.editConditionFormatRule"), &NewOpEditConditionFormat));
		s_newOpMap.insert(std::make_pair(__X("range.text2col.exec"),		&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("sheet.swapConditionFormatRule"), &NewOpSwapConditionFormat));
		s_newOpMap.insert(std::make_pair(__X("range.setDataValidation"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("sheet.setDuplicateValueRemind"), &NewOpRangeMove));
		s_newOpMap.insert(std::make_pair(__X("sheet.clearDuplicateValueRemind"), &NewOpRangeMove));
		s_newOpMap.insert(std::make_pair(__X("sheet.removeDuplicates"), &NewOpRangeMove));
		s_newOpMap.insert(std::make_pair(__X("sheet.addChart"), &NewOpMultiRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("sheet.addSmartAnalysisChart"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.beautify"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.beautifyAIFormat"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.dataProof"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.setChartDataSoucre"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivottable.addFieldToArea"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("pivottable.removeTableField"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("book.setTheme"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("file.merge"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.setFilterShared"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.setOptions"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.rangeSlice.exec"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.proofreadModifyText"), &NewOpRangeMove));
		s_newOpMap.insert(std::make_pair(__X("app.setRefStyle"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("supbooks.changeSource"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("supbooks.updateLinks"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("range.setPixelator"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.insertCopied"),		nullptr));
		s_newOpMap.insert(std::make_pair(__X("range.insertCut"),		nullptr));
		s_newOpMap.insert(std::make_pair(__X("range.update"),		nullptr));
		s_newOpMap.insert(std::make_pair(__X("range.textToNumber"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.pixelate"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("chart.setDataSource"), &NewOpSetDataSource));
		s_newOpMap.insert(std::make_pair(__X("chart.setSeriesSource"), &NewOpSetSeriesDataSource));
		s_newOpMap.insert(std::make_pair(__X("chart.addSeries"), &NewOpSetSeriesDataSource));
		s_newOpMap.insert(std::make_pair(__X("chart.addSeriesByRange"), &NewOpSetDataSource));
		s_newOpMap.insert(std::make_pair(__X("chart.setCategoryDataSource"), &NewOpSetCategoryDataSource));
		s_newOpMap.insert(std::make_pair(__X("chart.setSeriesVisible"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.deleteSeries"), &NewOpChartDeleteSeries));
		s_newOpMap.insert(std::make_pair(__X("chart.moveSeries"), &NewOpChartMoveSeries));
		s_newOpMap.insert(std::make_pair(__X("chart.setElement"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.applyLayout"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.setChartColor"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.setChartType"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.setChartStyle"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.setChartTitle"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.setAxisTitle"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.setPlotBy"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.setPlotVisibleOnly"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("chart.setDisplayBlanksAs"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("shape.paste"), &NewOpShapePaste));
		s_newOpMap.insert(std::make_pair(__X("sheet.floatPic2CellPic"), &NewOpDeleteShape));
		s_newOpMap.insert(std::make_pair(__X("sheet.cellPic2FloatPic"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("sheet.setProtection"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.setProtectionOptions"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.modifyProtection"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.setAllowEditRange"), &NewOpAllowEditRange));
		s_newOpMap.insert(std::make_pair(__X("sheet.removePartRangeProtection"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("sheet.setInterline"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.setTabColor"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("book.removeCustomNF"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.setWndInfo"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("names.add"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.names.edit"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.names.edit"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("book.names.delete"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.names.delete"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("sheet.removeInquirer"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.pageBreak"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("book.docSlim"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.SetIsCanWoAutoSlim"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("chart.shapeMove"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.shapeResize"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setChartText"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setTextProperty"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setAxisOption"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setNumFmt"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setFill"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setOutline"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setMarkerOption"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setSeriesOption"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setTrendlineOption"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.deleteTrendline"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("chart.setDataLabelOption"), &NewOpDataLabelOption));
		s_newOpMap.insert(std::make_pair(__X("chart.setLegendOption"), &NewOpChartShape));
		s_newOpMap.insert(std::make_pair(__X("range.setValueByCustomList"), &NewOpRangeMove));
		s_newOpMap.insert(std::make_pair(__X("range.quickSet"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.quickSetFill"), &NewOpRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("range.setValue2"), &NewOpRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("book.setSubscriptionOption"), &NewOpsetSubscriptionOption));
		s_newOpMap.insert(std::make_pair(__X("book.setDocumentCustomProperty"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.setPrintArea"), &NewOpMultiRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("sheet.setPageSetting"), &NewOpSetPageSetting));
		s_newOpMap.insert(std::make_pair(__X("file.copyFromBook"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.addCol"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("db.editCol"), &NewOpDbTableColumn));
		s_newOpMap.insert(std::make_pair(__X("db.addRow"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("db.setField"), &NewOpRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("db.fillValues"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("sheet.setPadding"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("range.createPivotTable"),	&NewOpCreatePivotTable));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.deleteTable"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.moveField"),	&NewOpPivotTableRangeFieldName));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setFieldName"),	&NewOpPivotTableRangeFieldName));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setFieldFunction"),	&NewOpPivotTableRangeFieldName));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setFieldSubtotals"),	&NewOpPivotTableRangeFieldName));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.refresh"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("book.refreshAllPivotTables"),	nullptr));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setSourceData"),	&NewOpPivotTableSetSourceData));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.clearTable"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("book.showPivotTableFieldList"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setShowValuesAs"),	&NewOpPivotTableRangeFieldName));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setFieldOptions"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setOptions"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setFieldLayoutPrint"), &NewOpPivotTableRangeFieldName));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.sortField"), &NewOpPivotTableRangeFieldName));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setCollapseButton"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.showPages"),	&NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.showDetailAtDataArea"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.groupPivotTable"), NewOpPivotGroupRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.unGroupPivotTable"), NewOpPivotGroupRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.addSlicer"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.selectSlicerItem"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.clearSlicerItem"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setSlicerStartIndex"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.setSlicerOptions"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.connectPivotTable"), &NewOpMultiRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.addCalculatedItem"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.modifyCalculatedItem"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.delCalculatedItem"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.addCalculatedField"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.modifyCalculatedField"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.delCalculatedField"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.expandRange"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.itemMove"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("pivot_table.slicerRefresh"), &NewOpShapeObj));
		s_newOpMap.insert(std::make_pair(__X("range.setTextLink"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("range.setPreferredView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.clearTransactions"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.setInserPicAsAttachment"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.updateUrlSource"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.initImportrangeCalc"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.updateSupbookData"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.doAllMergeTask"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.mergeFileAutoUpdate"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.updateCrossBookState"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.updateRemoteChart"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.convertEtToAs"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.setProtectedCol"), &NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("http.et.updateRangeData"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.clearCellHistories"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.setCellHistoryPurgeOptions"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.addRow"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.setRangeWidthHeight"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.pivotTableRefresh"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.addHyperlink"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.et.setQRCol"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.et.fillQRCol"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.et.clearColType"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.et.recalculateRangeData"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.et.updateWorkbenchSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.shareSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.cancelShareSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.etShareSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.etCancelShareSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.setSheetName"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.addSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.copySheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.copySheetsFromBook"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.deleteSheets"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.deleteRange"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("sheet.multiSheetsOp"), &NewOpMultiSheetsOp));
		s_newOpMap.insert(std::make_pair(__X("book.interruptEAF"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("book.recalculateEAF"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("range.exportAfValues"), &NewOpRangeMoveAbridgeSplit));
        s_newOpMap.insert(std::make_pair(__X("range.repair"), &NewOpRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("multiRange.clearAll"), &NewOpMultiRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("multiRange.clearContents"), &NewOpMultiRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("multiRange.clearSpecialChars"), &NewOpMultiRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("multiRange.clearComments"), &NewOpMultiRangeMoveAbridgeSplit));
		s_newOpMap.insert(std::make_pair(__X("et.empty"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("range.splitSheet"), &NewOpRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("http.et.v8JsEvaluate"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.insertNumberCol"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.setExclusiveRangeMode"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("multiRange.RemoveExclusiveUserRange"), &NewOpMultiRangeExpandAbridge));
		s_newOpMap.insert(std::make_pair(__X("http.et.ExecuteApi"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("sheet.appendSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.form.addSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.fullUpdateSourceSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.dataAnalyzeImportWorkBook"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.deleteDataAnalyzeMergeTask"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.addDataAnalyzeLog"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.et.updateDataAnalyzeDataSourceName"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("webchart.setStyle"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("webchart.removeStyle"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("webchart.setOrder"), nullptr));
	}
	if (bDbSheet || bKsheet)
	{
		s_newOpMap.insert(std::make_pair(__X("range.monitor"),		&NewOpRangeExpandAbridgeNoSheetIdx));
		s_newOpMap.insert(std::make_pair(__X("range.monitorSplit"),	&NewOpRangeMoveAbridgeSplitNoSheetIdx));

		s_newOpMap.insert(std::make_pair(__X("dbsheets.add"),			&NewOpSheetsAdd));
		s_newOpMap.insert(std::make_pair(__X("dbsheets.move"),		&NewOpSheetsMove));
		s_newOpMap.insert(std::make_pair(__X("dbsheets.copy"),		&NewOpSheetsCopy));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.copyAppSheet"),	&NewOpSheetsCopy));
        s_newOpMap.insert(std::make_pair(__X("dbsheet.copyDashBoardSheet"),	&NewOpSheetsCopy));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.delSheet"),		&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.delSheets"),		&NewOpMultiSheetsOp));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setName"),		&NewOpOneObjSheet));

		s_newOpMap.insert(std::make_pair(__X("dbSheet.insertRecords"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.removeRecords"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setParentRecord"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.modifyRecord"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.insertFields"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.removeFields"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.clearRange"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.moveRange"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.moveGroup"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.removeGroup"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.modifyField"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setFieldHidden"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setFieldWidth"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setRecordsHeight"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.createView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.copyView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setCellPic"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.modifyWebExtension"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.addWebExtension"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.batchModifyWebExtension"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.deleteWebExtension"), &NewOpDeleteShape));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.addGroupCondition"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.removeGroupCondition"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setGroupPriority"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.changeGroupField"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setGroupAscending"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.execSort"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.addSortCondition"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.removeSortCondition"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setSortPriority"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.changeSortField"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setSortAscending"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setCellHyperlink"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setCellValue"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setRangeValues"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.deleteView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.renameView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setViewDescription"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setViewNotice"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setViewOption"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setSheetDescription"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setSheetIcon"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setStatistic"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.removeStatistic"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.moveView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setAutoSort"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setUngroupedPositionOption"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setGridFrozenCols"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.rangePaste"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setFilterCriteria"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.removeFilter"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.clearFilters"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setFiltersOp"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.prepareCurUser"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.empty"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.userGroupAddUsers"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.userGroupRemoveUsers"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.userGroupSetProtection"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.protectionSwitch"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.setupProtection"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.shareView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.cancelShareView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.shareSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.cancelShareSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.modifyShareView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.addTemplateSheets"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.importXlsxSheets"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.importCsv"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.importDbSheets"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.appendData"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.addSyncSheets"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.unsyncSheets"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.addSyncMergeSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.clearTransactions"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setGalleryCoverField"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setKanbanCoverField"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setGalleryCoverDispType"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setKanbanCoverDispType"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setDbLayout"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setTimerTask"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setFormFieldOption"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.fetchUserInfoDone"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.updateUserOrganizeInfo"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.assignTimeLine"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.configureGanttView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setPersonalView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setFieldNameVisible"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setDisplayFormTp"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setQueryFieldIds"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setQueryFields"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setQueryConditionCanBlank"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setQueryNeedSecondCheck"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.setQueryCriteria"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.updateAppSheetData"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.onAfterDeleteApp"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.convertToEtSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.convertFromEtSheet"),	&NewOpOneObjSheet));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.updateAppVersion"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbsheet.resetAppSheetSharedIds"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.rangeFill"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setRangeXf"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.clearRangeXf"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setFieldTitleFormat"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.removeFieldTitleFormat"),	nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setViewBackgroundImage"),	nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.clearTempSettings"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.syncTempSettings"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.saveAsNewViewByTempSettings"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setComposeFilter"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setCalendarCriteria"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.configureCalendarView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.assignCalendarTimeLine"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.modifyStatSheetSetting"),	nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.activateStatSheet"),	nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.recoverStatSheet2NormalDbSheet"),	nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.addSidebarFolder"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.deleteSidebarFolder"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.moveSidebarTree"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.setNameSidebarFolder"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("dbSheet.commonLogInit"), nullptr));

        // db图表
        s_newOpMap.insert(std::make_pair(__X("db.chart.setStyle"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("db.chart.removeStyle"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("db.chart.setOrder"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.dashboard.modifyPluginConfig"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.dashboard.addView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("db.dashboard.modifyView"), nullptr));
        // db仪表盘筛选器
        s_newOpMap.insert(std::make_pair(__X("db.dashboard.createFilter"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("db.dashboard.updateFilter"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("db.dashboard.deleteFilter"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("db.dashboard.moveFilter"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("db.dashboard.setFilterCriteria"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("db.dashboard.clearFilterCriteria"), nullptr));

		s_newOpMap.insert(std::make_pair(__X("http.db.createRecords"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.updateRecords"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.deleteRecords"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.bindParentRecord"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.enableParentRecord"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.disableParentRecord"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.createFields"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.deleteFields"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.updateFields"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.createView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.updateView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.deleteView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.addViewSetting"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.removeViewSetting"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.setQueryFields"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.createSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.updateSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.deleteSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.editpermission"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.devSubmitFormRecord"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.devCreateAppView"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.syncSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.fullUpdateSyncSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.batchFullUpdateSyncSheet"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.createDashboard"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.updateDashboard"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.clearDashboard"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.deleteDashboard"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.createWebExtension"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.updateWebExtension"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.deleteWebExtension"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.fullUpdateSyncMergeSheet"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.syncFromSql"), nullptr));
		s_newOpMap.insert(std::make_pair(__X("http.db.syncFormToDb"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.dashboard.createFilters"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.dashboard.updateFilters"), nullptr));
        s_newOpMap.insert(std::make_pair(__X("http.db.dashboard.deleteFilters"), nullptr));
	}
}

void makeCode(RegionOperationCode& code, const RECT& rc, OpDir dir, BMP_PTR bmp)
{
	if (dir == dir_hori)
	{
		if (rc.top == 0 && rc.bottom == bmp->cntRows - 1)
			code = (RegionOperationCode)(code | rop_Cols);
		else
			code = (RegionOperationCode)(code | rop_Cells | rop_Horz);
	}
	else
	{
		if (rc.left == 0 && rc.right == bmp->cntCols - 1)
			code = (RegionOperationCode)(code | rop_Rows);
		else
			code = (RegionOperationCode)(code | rop_Cells | rop_Vert);
	}
}

bool isRowsOrCols(const RECT& rc, BMP_PTR bmp)
{
	return rc.top == 0 && rc.bottom == bmp->cntRows - 1 ||
		rc.left == 0 && rc.right == bmp->cntCols - 1;
}

int webidToPriority(OpEnv* env, WebID id, IDX idxSht)
{
	if (idxSht < 0)
		return -1;

	RANGE rg(env->m_bmp);
	rg.SetSheetFromTo(idxSht);
	rg.SetRowFromTo(0, env->m_bmp->cntRows - 1);
	rg.SetColFromTo(0, env->m_bmp->cntCols - 1);

	ks_stdptr<ICFItems> spItms;
	env->m_bookOp->GetRangeCF(rg, &spItms);
	if (NULL == spItms)
		return -1;

	std::unordered_map<ICFItem*, CELL> cfs;
	spItms->GetItems(cfs);

	for (auto it = cfs.begin(); it != cfs.end(); ++it)
	{
		ICFItem* pItm = it->first;
		for (size_t i = 0, n = pItm->GetRuleCount(); i < n; ++i)
		{
			ks_stdptr<ICondFmt> spCF;
			pItm->GetRule(i, &spCF);
			if (spCF->GetObject()->objId() == id)
			{
				return spCF->GetPriority();
			}
		}
	}

	return -1;
}

int getPriority(OpEnv* env, const binary_wo::VarObj& varRule, IDX iSheet)
{
	int iPriority = -1;
	if(varRule.has("priority"))
	{
		iPriority = varRule.field_int32("priority");
	}
	else if(varRule.has("id"))
	{
		WebID id = varRule.field_web_id("id");
		iPriority = webidToPriority(env, id, iSheet);
	}
	return iPriority;
}

static void MakeCutInsertRange(REGION_OPERATION_PARAM& param, const RANGE& range, INT offRow, INT offCol, DIR dir)
{
	param.source = range;
	param.target = ES_POS(-1, 0, 0);

	// 参见 A146，target 成员的含义变为“操作完成后目标的位置”

	switch (range.RangeType())
	{
	case rtRows:
		{
			ASSERT(dir == dirDown);
			ASSERT(offRow != 0 && offCol == 0);
			param.code = rop_CutInsertRows;
			param.target.nRow = param.source.RowFrom() + offRow;
		}
		break;

	case rtCols:
		{
			ASSERT(dir == dirRight);
			ASSERT(offRow == 0 && offCol != 0);
			param.code = rop_CutInsertCols;
			param.target.nCol = param.source.ColFrom() + offCol;
		}
		break;

	case rtCells:
	case rtCell:
		{
			if (offCol == 0)	// Type A, Vert
			{
				param.code = rop_CutInsertCellsDown;
				param.target.nRow = param.source.RowFrom() + offRow;
				param.target.nCol = param.source.ColFrom();
			}
			else if (offRow == 0)	// Type A, Horz
			{
				param.code = rop_CutInsertCellsRight;
				param.target.nCol = param.source.ColFrom() + offCol;
				param.target.nRow = param.source.RowFrom();
			}
			else					// Type B
			{
				throw_et_exception(E_NOTIMPL);
			}
		}
		break;

	default:
		throw_et_exception(E_AREA_INVALIDADJUSTPRM);
		break;
	}
}


}//namespace

void resetOpMap()
{
	s_newOpMap.clear();
}

bool OpBase::updateSheetIdxHelper(binary_wo::VarObj& param, WebName objSheet, WebName sheetIdx)
{
	IDX newIdx = m_env->m_ctx->getSheetIndex(param.field_web_id(objSheet));
	IDX oldIdx = param.field_int32(sheetIdx);

	if (newIdx == oldIdx)
		return false; //no change

	param.add_field_int32(sheetIdx, newIdx);
	return true;
}

bool OpBase::updateShapeIdxPathHelper(binary_wo::VarObj& param, WebName objShapeName, WebName idxPathName)
{
	if (!param.has(objShapeName))
		return false;
	IDX iSheet = param.field_int32("sheetIdx");
	WebID id = param.field_web_id(objShapeName);
	binary_wo::VarObj idxPathObj = param.get(idxPathName);

	ShapeIdxPath idxPath;
	ShapeCountPath pathInfo;
	m_env->m_ctx->getShapeIdxPathById(iSheet, id, idxPath, pathInfo);

	const WebName pathInfoName = "shapeCount";
	bool bChanged = (idxPathObj.arrayLength() != idxPath.size());
	if (!param.has(pathInfoName))
		bChanged = true;
	if (!bChanged)
	{
		for (size_t i = 0; !bChanged && i < idxPath.size(); ++i)
			if (idxPathObj.item_uint32(i) != idxPath[i])
				bChanged = true;
	}

	if (bChanged)
	{
		binary_wo::VarObj arr = param.add_field_array(idxPathName, binary_wo::typeInt32);
		for (size_t i = 0;  i < idxPath.size(); ++i)
			arr.add_item_int32(idxPath[i]);

		if (pathInfo.size() > 1)
		{
			binary_wo::VarObj infos = param.add_field_array(pathInfoName, binary_wo::typeInt32);
			for (size_t i = 0; i < pathInfo.size(); ++i)
				infos.add_item_int32(pathInfo[i]);
		}
	}

	return bChanged;
}

OpBase* CreateOp(OpEnv* env, const KwCommand* cmd)
{
	ValidNewOpMap(env->m_bmp->bDbSheet, env->m_bmp->bKsheet);
	auto it = s_newOpMap.find(ks_wstring(cmd->cast().field_str("name")));
	if (it == s_newOpMap.end())
	{
		ASSERT(FALSE);
		PCWSTR strCmd = cmd->cast().field_str("name");
		QString str = QString("command \"") +  QString::fromUtf16(strCmd) + QString("\" no Transformation!\n");

		//tmp {
		for (const WCHAR* pCh = strCmd; *pCh != 0; ++pCh)
			str.append(QString("%1,").arg(*pCh));
		str.append("\n");
		//} tmp

		gs_callback->log(WO_LOG_ERROR, str.toUtf8());
		return NULL;
	}
	else if (it->second == NULL)
	{
		return NULL;
	}
	else
	{
		return (*it->second)(env, cmd);
	}
}

///////////////////////////////////////////////////



bool OpBaseHasOneObjSheet::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if (param.has("sheetStId"))
		return false;
	return updateSheetIdxHelper(param , "objSheet", "sheetIdx");
}

/////////////////////////////////////////////////////////////////////////////

OpRangeInsert::OpRangeInsert(OpEnv* env, const KwCommand* src)
	: OpBaseHasOneObjSheet(env, src, ot_range_Insert)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	m_rc = VarReadRect(param);

	PCWSTR strDir = param.field_str("direction");
	if (xstricmp(strDir, __X("etShiftDown")) == 0)
		m_dir = dir_vert;
	else if (xstricmp(strDir, __X("etShiftToRight")) == 0)
		m_dir = dir_hori;
	else
		ks_throw(E_INVALIDARG);
}

OpRangeInsert::OpRangeInsert(OpEnv* env, const KwCommand* src, const RECT& rc, OpDir dir)
	: OpBaseHasOneObjSheet(env, src, ot_range_Insert)
	, m_rc(rc)
	, m_dir(dir)
{

}

KwCommand* OpRangeInsert::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_rc, param);
	if (m_dir == dir_vert)
		param.add_field_str("direction",  __X("etShiftDown"));
	else if (m_dir == dir_hori)
		param.add_field_str("direction",  __X("etShiftToRight"));

	return OpBase::detachCmd();
}

bool OpRangeInsert::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);
			if (pSv->m_dir != m_dir &&
				!isRowsOrCols(m_rc, m_env->m_bmp) && !isRowsOrCols(pSv->m_rc, m_env->m_bmp))
				return false;

			RectTrans::Param param = pSv->m_dir != m_dir ? RectTrans::ts_move_split : RectTrans::ts_move_split_exDf;
			RectTrans rectTrans(param, m_env->m_bmp);
			std::vector<RECT> vecIst;
			if (pSv->m_dir == dir_hori)
				rectTrans.InsertRight(pSv->m_rc, m_rc, vecIst);
			else
				rectTrans.InsertDown(pSv->m_rc, m_rc, vecIst);
			return MakeRes(vecIst, vecOpCl);
		}
	case ot_range_Delete:
		{
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			if (pSv->m_dir != m_dir &&
				(isRowsOrCols(m_rc, m_env->m_bmp) || !isRowsOrCols(pSv->m_rc, m_env->m_bmp)))
				return false;

			RectTrans rectTrans(RectTrans::ts_move_split_exDf , m_env->m_bmp);
			std::vector<RECT> vecIst;
			if (pSv->m_dir == dir_hori)
				rectTrans.DeleteLeft(pSv->m_rc, m_rc, vecIst);
			else
				rectTrans.DeleteUp(pSv->m_rc, m_rc, vecIst);
			return MakeRes(vecIst, vecOpCl);
		}
	case ot_range_CutInsertVert:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_range_CutInsertHorz:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	default:
		return false;
	}
}

bool OpRangeInsert::tfByCl(const OpBase* opCl, VecOp& vecOpSv)
{
	switch (opCl->tp())
	{
	case ot_range_Insert:
		{
			const OpRangeInsert* pCl = static_cast<const OpRangeInsert*>(opCl);
			if (pCl->m_dir == m_dir &&
				(m_dir == dir_hori && pCl->m_rc.left == m_rc.left ||
				 m_dir == dir_vert && pCl->m_rc.top == m_rc.top))
				return false; // 相同操作地位相等，服务端不变换

			if (pCl->m_dir != m_dir &&
				!isRowsOrCols(m_rc, m_env->m_bmp) && !isRowsOrCols(pCl->m_rc, m_env->m_bmp))
				return false;

			RectTrans::Param param = pCl->m_dir != m_dir ? RectTrans::ts_move_split_rowCol : RectTrans::ts_move_split_rowCol_exDf;
			RectTrans rectTrans(param, m_env->m_bmp);
			std::vector<RECT> vecRes;
			if (pCl->m_dir == dir_hori)
				rectTrans.InsertRight(pCl->m_rc, m_rc, vecRes);
			else
				rectTrans.InsertDown(pCl->m_rc, m_rc, vecRes);

			return MakeRes(vecRes, vecOpSv);
		}
	case ot_range_Delete:
		{
			std::vector<RECT> vecRes;
			const OpRangeDelete* pCl = static_cast<const OpRangeDelete*>(opCl);
			if (pCl->m_dir == m_dir)
			{
				RectTrans rectTrans(RectTrans::ts_move_split_rowCol , m_env->m_bmp);
				if (pCl->m_dir == dir_hori)
					rectTrans.DeleteLeft(pCl->m_rc, m_rc, vecRes);
				else
					rectTrans.DeleteUp(pCl->m_rc, m_rc, vecRes);
			}
			else
			{
				if (!isRowsOrCols(m_rc, m_env->m_bmp) || isRowsOrCols(pCl->m_rc, m_env->m_bmp))
					return false;

				RectTrans rectTrans(RectTrans::ts_move_abridge , m_env->m_bmp);
				if (pCl->m_dir == dir_hori)
					rectTrans.DeleteLeft(pCl->m_rc, m_rc, vecRes);
				else
					rectTrans.DeleteUp(pCl->m_rc, m_rc, vecRes);
			}
			return MakeRes(vecRes, vecOpSv);
		}
	default:
		return false;
	}
}

bool OpRangeInsert::MakeRes(std::vector<RECT>& vec, VecOp& vecOp)
{
	if (vec.empty() || vec.size() == 1 && Rect_Equal(vec[0], m_rc))
		return false;

	for (size_t i = 0; i < vec.size(); ++i)
	{
		vecOp.m_v.push_back(new OpRangeInsert(m_env, m_cmd, vec[i], m_dir));
	}
	return true;
}


OpRangeDelete::OpRangeDelete(OpEnv* env, const KwCommand* src)
	: OpBaseHasOneObjSheet(env, src, ot_range_Delete)
	, m_bShapeIdxChange(false)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	m_rc = VarReadRect(param);
	OpShapeHelper::loadShapesFromVarObj(param.get_s("shapesSeq"), m_delShapes);

	PCWSTR strDir = param.field_str("direction");
	if (xstricmp(strDir, __X("etShiftUp")) == 0)
		m_dir = dir_vert;
	else if (xstricmp(strDir, __X("etShiftToLeft")) == 0)
		m_dir = dir_hori;
	else
		ks_throw(E_INVALIDARG);
}

OpRangeDelete::OpRangeDelete(OpEnv* env, const KwCommand* src, const RECT& rc, OpDir dir)
	: OpBaseHasOneObjSheet(env, src, ot_range_Delete)
	, m_rc(rc)
	, m_dir(dir)
{

}

KwCommand* OpRangeDelete::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_rc, param);
	if (m_dir == dir_vert)
		param.add_field_str("direction",  __X("etShiftUp"));
	else if (m_dir == dir_hori)
		param.add_field_str("direction",  __X("etShiftToLeft"));

	if (m_bShapeIdxChange && !m_delShapes.empty())
	{
		binary_wo::VarObj objShapes = param.add_field_array("shapesSeq", binary_wo::typeStruct);
		OpShapeHelper::saveShapesToVarObj(m_delShapes, objShapes);
	}

	return OpBase::detachCmd();
}

bool OpRangeDelete::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);
			if (m_dir != pSv->m_dir &&
				!isRowsOrCols(m_rc, m_env->m_bmp) && !isRowsOrCols(pSv->m_rc, m_env->m_bmp))
				return false;

			RectTrans rectTrans(RectTrans::ts_move_split , m_env->m_bmp);
			std::vector<RECT> res;
			if (pSv->m_dir == dir_hori)
				rectTrans.InsertRight(pSv->m_rc, m_rc, res);
			else
				rectTrans.InsertDown(pSv->m_rc, m_rc, res);
			return MakeRes(res, vecOpCl);

		}
	case ot_range_Delete:
		{
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			if (m_dir != pSv->m_dir &&
				!isRowsOrCols(m_rc, m_env->m_bmp) && !isRowsOrCols(pSv->m_rc, m_env->m_bmp))
				return false;

			RectTrans rectTrans(RectTrans::ts_move_abridge_split , m_env->m_bmp);
			std::vector<RECT> res;
			if (pSv->m_dir == dir_hori)
				rectTrans.DeleteLeft(pSv->m_rc, m_rc, res);
			else
				rectTrans.DeleteUp(pSv->m_rc, m_rc, res);

			return MakeRes(res, vecOpCl);
		}
	case ot_range_CutInsertVert:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_range_CutInsertHorz:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_shape_Insert:
	case ot_shape_Delete:
	case ot_shape_Move:
		if (!m_delShapes.empty())
		{
			OpShapesPaths cutShapes;
			if (EtOpShapeHelper::tfShapesBySv(opSv, m_delShapes, cutShapes))
				return makeShapesRes(cutShapes, vecOpCl);
		}
		break;
	default:
		break;
	}
	return false;
}

bool OpRangeDelete::tfByCl(const OpBase* opCl, VecOp& vecOpSv)
{
	switch (opCl->tp())
	{
	case ot_range_Insert:
		{

			const OpRangeInsert* pCl = static_cast<const OpRangeInsert*>(opCl);
			if (m_dir != pCl->m_dir &&
				(isRowsOrCols(m_rc, m_env->m_bmp) || !isRowsOrCols(pCl->m_rc, m_env->m_bmp)))
				return false;

			RectTrans rectTrans(RectTrans::ts_move_split_rowCol, m_env->m_bmp);
			std::vector<RECT> res;
			if (pCl->m_dir == dir_hori)
				rectTrans.InsertRight(pCl->m_rc, m_rc, res);
			else
				rectTrans.InsertDown(pCl->m_rc, m_rc, res);

			return MakeRes(res, vecOpSv);
		}
	case ot_range_Delete:
		{
			const OpRangeDelete* pCl = static_cast<const OpRangeDelete*>(opCl);
			if (m_dir != pCl->m_dir &&
				!isRowsOrCols(m_rc, m_env->m_bmp) && !isRowsOrCols(pCl->m_rc, m_env->m_bmp))
				return false;

			RectTrans rectTrans(RectTrans::ts_move_abridge_split_rowCol , m_env->m_bmp);
			std::vector<RECT> res;
			if (pCl->m_dir == dir_hori)
				rectTrans.DeleteLeft(pCl->m_rc, m_rc, res);
			else
				rectTrans.DeleteUp(pCl->m_rc, m_rc, res);

			return MakeRes(res, vecOpSv);
		}
	default:
		return false;
	}

}

bool OpRangeDelete::MakeRes(std::vector<RECT>& vec, VecOp& vecOp)
{
	if (vec.empty() || vec.size() == 1 && Rect_Equal(vec[0], m_rc))
		return false;

	for (size_t i = 0; i < vec.size(); ++i)
	{
		vecOp.m_v.push_back(new OpRangeDelete(m_env, m_cmd, vec[i], m_dir));
	}
	return true;
}

bool OpRangeDelete::makeShapesRes(OpShapesPaths& delShapes, VecOp& vecOp)
{
	if (!delShapes.empty())
	{
		vecOp.m_v.push_back(nullptr);
	}
	else
	{
		OpRangeDelete* pNew = new OpRangeDelete(m_env, m_cmd, m_rc, m_dir);
		pNew->m_delShapes.swap(delShapes);
		pNew->m_bShapeIdxChange = true;
		vecOp.m_v.push_back(pNew);
	}
	return true;
}

OpRangeBase::OpRangeBase(OpEnv* env, const KwCommand* src, OpType tp, RectTrans::Param tsp)
	: OpBaseHasOneObjSheet(env, src, tp)
	, m_tsp(tsp)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	m_rc = VarReadRect(param);
}

OpRangeBase::OpRangeBase(OpEnv* env, const KwCommand* src, const RECT& rc, OpType tp, RectTrans::Param tsp)
	: OpBaseHasOneObjSheet(env, src, tp)
	, m_tsp(tsp)
	, m_rc(rc)
{

}

KwCommand* OpRangeBase::detachCmd()
{
	ASSERT(m_cmd != NULL);
	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_rc, param);

	return OpBase::detachCmd();
}

bool OpRangeBase::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			std::vector<RECT> vecRes;
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			RectTrans rectTrans(m_tsp , m_env->m_bmp);
			if (pSv->m_dir == dir_hori)
				rectTrans.InsertRight(pSv->m_rc, m_rc, vecRes);
			else
				rectTrans.InsertDown(pSv->m_rc, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_range_Delete:
		{
			std::vector<RECT> vecRes;
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			RectTrans rectTrans(m_tsp , m_env->m_bmp);
			if (pSv->m_dir == dir_hori)
				rectTrans.DeleteLeft(pSv->m_rc, m_rc, vecRes);
			else
				rectTrans.DeleteUp(pSv->m_rc, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_range_CutInsertVert:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_range_CutInsertHorz:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	default:
		return false;
	}
}

bool OpRangeBase::MakeRes(std::vector<RECT>& vec, VecOp& vecOp)
{
	if (vec.empty() || vec.size() == 1 && Rect_Equal(vec[0], m_rc))
		return false;

	for (size_t i = 0; i < vec.size(); ++i)
	{
		vecOp.m_v.push_back(this->clone(vec[i]));
	}
	return true;
}

///////////////////////////////////////////////////////


OpPivotGroupRangeExpandAbridge::OpPivotGroupRangeExpandAbridge(OpEnv* env, const KwCommand* src)
	: OpBaseHasOneObjSheet(env, src, ot_range)
	, m_tsp(RectTrans::ts_move_expand_abridge)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	m_rcCell = VarReadRect(param.get("activeCell"));
	ASSERT(m_rcCell.bottom == m_rcCell.top && m_rcCell.left == m_rcCell.right);
	m_spCellOp.reset(new OpRangeExpandAbridge(env, src, m_rcCell));

	binary_wo::VarObj rgs = param.get_s("selection");
	for (int i = 0; i < rgs.arrayLength_s(); i++)
	{
		binary_wo::VarObj rg = rgs.at_s(i);
		m_rcSelect.push_back(VarReadRect(rg));
	}
	m_spSelectOp.reset(new OpMultiRangeExpandAbridge(env, src, m_rcSelect));
}

OpPivotGroupRangeExpandAbridge::OpPivotGroupRangeExpandAbridge(OpEnv* env, const KwCommand* src, const RECT& rcCell, std::vector<RECT>& rcSelect)
	: OpBaseHasOneObjSheet(env, src, ot_range)
	, m_tsp(RectTrans::ts_move_expand_abridge)
	, m_rcCell(rcCell)
	, m_rcSelect(rcSelect)
{
	m_spCellOp.reset(new OpRangeExpandAbridge(env, src, m_rcCell));
	m_spSelectOp.reset(new OpMultiRangeExpandAbridge(env, src, m_rcSelect));
}

KwCommand* OpPivotGroupRangeExpandAbridge::detachCmd()
{
	ASSERT(m_cmd != NULL);
	binary_wo::VarObj param = m_cmd->cast().get("param");
	binary_wo::VarObj paramCell = param.get("activeCell");
	binary_wo::VarObj paramSelect = param.add_field_array("selection", binary_wo::typeStruct);
	VarWriteRect(m_rcCell, paramCell);

	for (auto it = m_rcSelect.begin(); it != m_rcSelect.end(); ++it)
	{
		binary_wo::VarObj rg = paramSelect.add_item_struct();
		VarWriteRect(*it, rg);
	}
	return OpBase::detachCmd();
}

bool OpPivotGroupRangeExpandAbridge::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	VecOp vecOpClSelect;
	VecOp vecOpClCell;

	bool cellChanged = m_spCellOp->tfBySv(opSv, vecOpClCell);
	bool selectChanged = m_spSelectOp->tfBySv(opSv, vecOpClSelect);
	
	MakeRes(vecOpClCell, vecOpClSelect, vecOpCl);
	return selectChanged | cellChanged;
}

bool OpPivotGroupRangeExpandAbridge::MakeRes(VecOp& vecOpCell, VecOp& vecOpSelect, VecOp& vecOp)
{
	OpRangeExpandAbridge* cell = (OpRangeExpandAbridge*)vecOpCell.m_v[0];//单个cell变换出来数组一定大小为1
	OpMultiRangeExpandAbridge* select = (OpMultiRangeExpandAbridge*)vecOpSelect.m_v[0];//变换出来数组一定大小为1
	vecOp.m_v.push_back(clone(cell->m_rc, select->m_rcVec));
	return true;
}

///////////////////////////////////////////////////////



OpFormulaBase::OpFormulaBase(OpEnv* env, const KwCommand* src, OpType tp)
	: OpRangeBase(env, src, RECT(), tp, RectTrans::ts_move_abridge_split)
	, m_isChangedByTfFml(false)
{

}

OpFormulaBase::OpFormulaBase(OpEnv* env, const KwCommand* src, OpType tp, RectTrans::Param tsp)
	: OpRangeBase(env, src, RECT(), tp, tsp)
	, m_isChangedByTfFml(false)
{

}

OpFormulaBase::OpFormulaBase(const OpFormulaBase& ref, const RECT& rc)
	: OpRangeBase(ref.m_env, ref.m_cmd, rc, ref.m_tp, RectTrans::ts_move_abridge_split)
	, m_idxSht(ref.m_idxSht)
	, m_isChangedByTfFml(false)
{
	if (const_cast<OpFormulaBase&>(ref).m_tokVec != nullptr)
		m_tokVec = const_cast< OpFormulaBase&>(ref).m_tokVec.clone();
}

bool OpFormulaBase::tfFormularBySv(IDX iSheet, const OpBase* opSv)
{
	REGION_OPERATION_PARAM param(m_env->m_bmp);
	switch (opSv->tp())
	{
	case ot_range_Delete:
		{
			const OpRangeDelete* pOp = static_cast<const OpRangeDelete*>(opSv);
			param.code = (RegionOperationCode)(param.code | rop_Remove);
			makeCode(param.code, pOp->m_rc, pOp->m_dir, m_env->m_bmp);
			param.source = Rect2Range(pOp->m_rc, iSheet, iSheet, m_env->m_bmp);
		}
		break;
	case ot_range_Insert:
		{
			const OpRangeInsert* pOp = static_cast<const OpRangeInsert*>(opSv);
			param.code = (RegionOperationCode)(param.code | rop_Insert);
			makeCode(param.code, pOp->m_rc, pOp->m_dir, m_env->m_bmp);
			param.source = Rect2Range(pOp->m_rc, iSheet, iSheet, m_env->m_bmp);
		}
		break;
	case ot_range_CutInsertVert:
		{
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RECT rcTop = pSv->m_rcTop, rcBottom = pSv->m_rcBottom;
			RANGE rgSrc = Rect2Range(rcTop, iSheet, m_env->m_bmp);
			INT offRow = rcBottom.bottom - rcBottom.top + 1;
			MakeCutInsertRange(param, rgSrc, offRow, 0, dirDown);
		}
		break;
	case ot_range_CutInsertHorz:
		{
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RECT rcLeft = pSv->m_rcLeft, rcRight = pSv->m_rcRight;
			RANGE rgSrc = Rect2Range(rcLeft, iSheet, m_env->m_bmp);
			INT offCol = rcRight.right - rcRight.left + 1;
			MakeCutInsertRange(param, rgSrc, 0, offCol, dirRight);
		}
		break;
	default:
		return false;
	}

	exec_token_vector tmp;
	m_env->m_bk->GetWoStake()->adjFmla(iSheet, m_tokVec, &tmp, param);
	if (tmp == nullptr)
		return false;
	m_tokVec = tmp;
	m_isChangedByTfFml = true;
	return true;
}

void OpFormulaBase::setFmla(PCWSTR fmla, const binary_wo::VarObj& booksCtx)
{
	m_env->m_ctx->getSupBookCtx()->reset(booksCtx);

	ks_stdptr<IFormula> spFmla;
	m_env->m_bookOp->CreateFormula(&spFmla);

	CS_COMPILE_PARAM	ccp(
		cpfSysDefault, m_idxSht, m_rc.top, m_rc.left);

	COMPILE_RESULT cr;
	spFmla->SetFormula(fmla, ccp, &cr);

	BOOL bFmla = FALSE;
	const_token_ptr pVal = NULL;
	spFmla->GetContent(&bFmla, &m_tokVec, &pVal);

	if (!bFmla)
		m_tokVec = nullptr;
}

void OpFormulaBase::getFmla(BSTR* bstrFmla)
{
	if (m_tokVec)
	{
		ks_stdptr<IFormula> spFmla;
		m_env->m_bookOp->CreateFormula(&spFmla);
		spFmla->SetFormulaContent(m_tokVec);
		CS_COMPILE_PARAM param;
		param.nSheet = m_idxSht;
		param.nRow = m_rc.top;
		param.nCol = m_rc.left;
		spFmla->GetFormula(bstrFmla, param);
	}
}

void OpFormulaBase::setSheetIdx(binary_wo::VarObj param)
{
	if (param.has("objSheet"))
	{
		WebID id = param.field_web_id("objSheet");
		m_idxSht = m_env->m_ctx->getSheetIndex(id);
	}
	else if (param.has("sheetStId"))
	{
		IDX sheetIdx = INVALIDIDX;
		UINT sheetStId = param.field_uint32("sheetStId");
		m_env->m_bk->STSheetToRTSheet(sheetStId, &sheetIdx);
		m_idxSht = sheetIdx;
	}
}

OpRangeSetFormula::OpRangeSetFormula(OpEnv* env, const KwCommand* src)
	: OpFormulaBase(env, src, ot_range_SetFormula)
{
	binary_wo::VarObj param = src->cast().get("param");
	setSheetIdx(param);
	m_rc = VarReadRect(param);
	PCWSTR fmla = param.field_str("formula");
	setFmla(fmla, param.get_s("supBooksCtx"));
}

KwCommand* OpRangeSetFormula::detachCmd()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_rc, param);
	ks_bstr str;
	getFmla(&str);
	if (!str.empty())
		param.add_field_str("formula", str.c_str());
	return OpBase::detachCmd();
}

bool OpRangeSetFormula::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	bool bCg = updateSheetIdxHelper(param, "objSheet", "sheetIdx");

	bool bCgRef = false;
	if(param.has("refObjSheet"))
		bCgRef = updateSheetIdxHelper(param, "refObjSheet", "refSheetIdx");

	return bCg || bCgRef;
}

OpSetCellFormula::OpSetCellFormula(OpEnv* env, const KwCommand* src) : OpFormulaBase(env, src, ot_range_SetFormula)
{
	binary_wo::VarObj param = src->cast().get("param");
	setSheetIdx(param);
	m_rc = VarReadRect(param);
	PCWSTR fmla = param.field_str("formula");
	setFmla(fmla, param.get_s("supBooksCtx"));
}

KwCommand* OpSetCellFormula::detachCmd()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_rc, param);

	ks_bstr str;
	getFmla(&str);
	if (!str.empty())
		param.add_field_str("formula", str.c_str());
	return OpBase::detachCmd();
}

OpAutoFill::OpAutoFill(OpEnv* env, const KwCommand* src)
	: OpBase(env, src, ot_range)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	m_src = VarReadRect(param);

	if (param.has("type") && param.field_int32("type") == etFlashFill)
	{
		m_isFlashFill = true;
		m_dir = dir_hori;
		m_dst = Rect_CreateScaleNone();
	}
	else
	{
		m_isFlashFill = false;
		binary_wo::VarObj dst = param.get("destination");
		m_dst = VarReadRect(dst);

		if (m_src.top ==  m_dst.top && m_src.bottom == m_dst.bottom)
			m_dir = dir_hori;
		else if (m_src.left == m_dst.left && m_src.right == m_dst.right)
			m_dir = dir_vert;
		else
			ks_throw(E_INVALIDARG);
	}
}

OpAutoFill::OpAutoFill(OpEnv* env, const KwCommand* cmd,
	const RECT& src, const RECT& dst, OpDir dir, bool isFlashFill)
	: OpBase(env, cmd, ot_range)
{
	m_src = src;
	m_dst = dst;
	m_dir = dir;
	m_isFlashFill = isFlashFill;
}

KwCommand* OpAutoFill::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_src, param);
	if (!m_isFlashFill)
	{
		binary_wo::VarObj dst = param.get("destination");
		VarWriteRect(m_dst, dst);
	}
	return OpBase::detachCmd();
}

bool OpAutoFill::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			if (pSv->m_dir != m_dir)
				rtp = RectTrans::ts_move_split;

			RectTrans rectTrans(rtp, m_env->m_bmp);
			std::vector<RECT> resSrc;
			std::vector<RECT> resDst;
			if (pSv->m_dir == dir_hori)
			{
				rectTrans.InsertRight(pSv->m_rc, m_src, resSrc);
				rectTrans.InsertRight(pSv->m_rc, m_dst, resDst);
			}
			else
			{
				rectTrans.InsertDown(pSv->m_rc, m_src, resSrc);
				rectTrans.InsertDown(pSv->m_rc, m_dst, resDst);
			}

			return MakeRes(resSrc, resDst, vecOpCl);

		}
	case ot_range_Delete:
		{
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			std::vector<RECT> resSrc;
			std::vector<RECT> resDst;
			if (pSv->m_dir == dir_hori)
			{
				rectTrans.DeleteLeft(pSv->m_rc, m_src, resSrc);
				rectTrans.DeleteLeft(pSv->m_rc, m_dst, resDst);
			}
			else
			{
				rectTrans.DeleteUp(pSv->m_rc, m_src, resSrc);
				rectTrans.DeleteUp(pSv->m_rc, m_dst, resDst);
			}
			return MakeRes(resSrc, resDst, vecOpCl);
		}
	case ot_range_CutInsertVert:
		{
			std::vector<RECT> resSrc;
			std::vector<RECT> resDst;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_src, resSrc);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_dst, resDst);

			return MakeRes(resSrc, resDst, vecOpCl);
		}
	case ot_range_CutInsertHorz:
		{
			std::vector<RECT> resSrc;
			std::vector<RECT> resDst;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_src, resSrc);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_dst, resDst);

			return MakeRes(resSrc, resDst, vecOpCl);
		}
	default:
		return false;
	}
}

bool OpAutoFill::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	bool dstChange = false;
	if (param.has("destination"))
	{
		binary_wo::VarObj dst = param.get("destination");
		dstChange = updateSheetIdxHelper(dst, "objSheet", "sheetIdx");
	}
	return updateSheetIdxHelper(param, "objSheet", "sheetIdx") || dstChange;
}

bool OpAutoFill::MakeRes(std::vector<RECT>& resSrc, std::vector<RECT>& resDst, VecOp& vecOp)
{
	if (resSrc.size() != resDst.size() || resSrc.empty())
		return false;

	if (resSrc.size() == 1 && Rect_Equal(resSrc[0], m_src) && Rect_Equal(resDst[0], m_dst))
		return false;

	for (size_t i = 0; i < resSrc.size(); ++i)
	{
		if (m_dir == dir_hori)
		{
			if (resSrc[0].top != resDst[0].top ||
				resSrc[0].bottom != resDst[0].bottom)
				return false;
		}
		else
		{
			if (resSrc[0].left != resDst[0].left ||
				resSrc[0].right != resDst[0].right)
				return false;
		}
	}

	for (size_t i = 0; i < resSrc.size(); ++i)
	{
		vecOp.m_v.push_back(new OpAutoFill(m_env, m_cmd, resSrc[i], resDst[i], m_dir, m_isFlashFill));
	}
	return true;
}

EtOpShapeHelper::EtOpShapeHelper(binary_wo::VarObj param)
	: OpShapeHelper(param)
{

}

EtOpShapeHelper::EtOpShapeHelper(const OpShapePath& path)
	: OpShapeHelper(path)
{

}

EtOpShapeHelper::EtOpShapeHelper(const OpShapeAtomBase& atomOp)
	: OpShapeHelper()
{
	m_extPos = atomOp.moveToPos();
	m_bInsert = ot_shape_Insert == atomOp.tp();
	m_bSvOp = true;
	m_shapeIdxPath = atomOp.shapeIdxPathV();
	m_shapeCount = atomOp.shapeCountV();
	ASSERT(m_shapeCount.empty() || m_shapeIdxPath.size() == m_shapeCount.size());
}

bool EtOpShapeHelper::tfBySv(const OpBase* opSv)
{
	// 非atom命令应该没有具体的moveTo用于调整
	switch (opSv->tp())
	{
	case ot_shape_Insert:
		{
			const OpShapeAtomInsert* pSv = static_cast<const OpShapeAtomInsert*>(opSv);
			if (!pSv->isReverseDel())
				return tfByInsert(pSv->shapeIdxPath());
			else
				return tfByReverseDelete(pSv->shapeIdxPath(), pSv->shapeCount());
		}
		break;
	case ot_shape_Delete:
		{
			const OpShapeAtomDelete* pSv = static_cast<const OpShapeAtomDelete*>(opSv);
			return tfByDelete(pSv->shapeIdxPath());
		}
		break;
	case ot_shape_Move:
		{
			const OpShapeAtomMove* pSv = static_cast<const OpShapeAtomMove*>(opSv);
			return tfByMove(pSv->shapeIdxPath(), pSv->moveToPos(), pSv->isReverse());
		}
		break;
	default:
		break;
	}
	return false;
}

bool EtOpShapeHelper::tfByCl(CmdShapeOpType opType, const OpShapePath& shape)
{
	binary_wo::VarInt32Array shapeIdxPathCl(shape.path.data(), (int32)shape.path.size());
	switch (opType)
	{
	case CmdShapeOpInsert:
		return tfByInsert(shapeIdxPathCl);
	case CmdShapeOpDelete:
		return tfByDelete(shapeIdxPathCl);
	case CmdShapeOpMoveZOrder:
		return tfByMove(shapeIdxPathCl, m_extPos, false);
	default:
		break;
	}
	return false;
}

bool EtOpShapeHelper::tfShapesBySv(const OpBase* opSv, const OpShapesPaths& pathsCl, OpShapesPaths& result)
{
	bool bChange = false;
	for (auto iter = pathsCl.begin(); iter != pathsCl.end(); ++iter)
	{
		const OpShapePath& shape = *iter;

		EtOpShapeHelper helper(shape);
		if (helper.tfBySv(opSv))
			bChange = true;
		else
			helper.copyFromRef();

		result.emplace_back(OpShapePath());
		helper.detachResult(result.back().path, result.back().count);
	}
	return bChange;
}

OpShapeObj::OpShapeObj(OpEnv* env, const KwCommand* src, OpType tp /*= ot_shape_Modify*/)
	: OpBaseHasOneObjSheet(env, src, tp)
	, m_bShapeIdxChanged(false)
{
	OpShapeHelper::loadShapeFromVarObj(src->cast().get("param"), m_shapePath);
}

bool OpShapeObj::makeResult(OpShapeHelper& helper, VecOp& vecOpCl)
{
	if (helper.hasNewIndex())
	{
		OpShapeObj* pNew = clone();
		pNew->m_bShapeIdxChanged = true;
		helper.detachResult(pNew->m_shapePath.path, pNew->m_shapePath.count);
		vecOpCl.m_v.push_back(pNew);
	}
	else
	{
		vecOpCl.m_v.push_back(nullptr);
	}
	return true;
}

bool OpShapeObj::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	// 非atom命令应该没有具体的moveTo用于调整
	switch (opSv->tp())
	{
	case ot_shape_Insert:
	case ot_shape_Delete:
	case ot_shape_Move:
		{
			binary_wo::VarObj param = m_cmd->cast().get("param");
			EtOpShapeHelper helper(param);
			if (helper.tfBySv(opSv))
				return makeResult(helper, vecOpCl);
		}
		break;
	default:
		break;
	}
	return false;
}

bool OpShapeObj::updateShapeIdxPath()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	return updateShapeIdxPathHelper(param , "objShape", "shapeIdxPath");
}

wo::KwCommand* OpShapeObj::detachCmd()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if (m_bShapeIdxChanged && !m_shapePath.path.empty())
	{
		OpShapeHelper::saveShapeToVarObj(m_shapePath, param);
	}
	return OpBase::detachCmd();
}

wo::OpShapeObj* OpShapeObj::clone()
{
	return new OpShapeObj(*this);
}

OpShapeAtomBase::OpShapeAtomBase(OpEnv* env, const EtShapeAtomOp& op, OpType tp)
	: OpBase(env, nullptr, tp)
	, m_extPos(op.extPos)
{
	m_shapePath.path = op.path;
	m_shapePath.count = op.count;
}

bool OpShapeAtomBase::tfByCl(const OpBase* opCl, VecOp& vecOpSv)
{
	switch (opCl->tp())
	{
	case ot_range_Delete:
	{
		const OpRangeDelete* pCl = static_cast<const OpRangeDelete*>(opCl);
		return tfByShapesDelSeq(pCl->m_delShapes, vecOpSv);
	}
	case ot_shape_Delete:
	{
		const OpShapeObj* pCl = dynamic_cast<const OpShapeObj*>(opCl);
		if (pCl)
		{
			return tfByDelShape(pCl->shapePath(), vecOpSv);
		}
		break;
	}
	case ot_shape_Insert: // 未执行的 push back 不需要变换其他操作
	case ot_shape_Move: // 还没有属于这种类型的命令
	default:
		break;
	}
	return false;
}

bool OpShapeAtomBase::tfByClCrossSht(const OpBase* opCl, VecOp& vecOpSv)
{
	switch (opCl->tp())
	{
	case ot_range:
	{
		// shape.paste 剪切
		const OpShapePaste* pOpShapePaste = dynamic_cast<const OpShapePaste*>(opCl);
		if (pOpShapePaste)
		{
			const OpShapesPaths& cutShapes = pOpShapePaste->cutShapes();
			return tfByShapesDelSet(cutShapes, vecOpSv);
		}
		break;
	}
	default:
		break;
	}
	return false;
}

const binary_wo::VarInt32Array OpShapeAtomBase::shapeIdxPath() const
{
	return binary_wo::VarInt32Array(m_shapePath.path.data(), m_shapePath.path.size());
}

const binary_wo::VarInt32Array OpShapeAtomBase::shapeCount() const
{
	return binary_wo::VarInt32Array(m_shapePath.count.data(), m_shapePath.count.size());
}

const std::vector<int32>& OpShapeAtomBase::shapeIdxPathV() const
{
	return m_shapePath.path;
}

const std::vector<int32>& OpShapeAtomBase::shapeCountV() const
{
	return m_shapePath.count;
}

bool OpShapeAtomBase::tfByShapesDelSeq(const OpShapesPaths& shapesSeq, VecOp& vecOpSv)
{
	bool bChange = false;
	for (auto iter = shapesSeq.begin(); iter != shapesSeq.end(); ++iter)
	{
		const OpShapePath& shape = *iter;
		if (tfByDelShape(shape, vecOpSv))
			bChange = true;
	}
	return bChange;
}

bool OpShapeAtomBase::tfByShapesDelSet(const OpShapesPaths& shapesSet, VecOp& vecOpSv)
{
	if (shapesSet.empty())
		return false;

	// 把selection转换为删除命令序列
	OpShapesPaths shapesSeq = shapesSet;
	for (auto itOp = shapesSeq.begin(); itOp != shapesSeq.end(); ++itOp)
	{
		OpShapePath& opPath = *itOp;
		for (auto itTrans = itOp + 1; itTrans != shapesSeq.end(); ++itTrans)
		{
			OpShapePath& transPath = *itTrans;
			EtOpShapeHelper helper(transPath);
			if (helper.tfByCl(OpShapeHelper::CmdShapeOpDelete, opPath))
			{
				helper.detachResult(transPath.path, transPath.count);
			}
		}
	}
	return tfByShapesDelSeq(shapesSeq, vecOpSv);
}

bool OpShapeAtomBase::tfByDelShape(const OpShapePath& shape, VecOp& vecOpSv)
{
	EtOpShapeHelper helper(*this);
	if (helper.tfByCl(OpShapeHelper::CmdShapeOpDelete, shape))
	{
		if (!helper.hasNewIndex())
		{
			vecOpSv.m_v.push_back(nullptr);
		}
		else
		{
			OpShapeAtomBase* pNew = clone();
			helper.detachResult(pNew->m_shapePath.path, pNew->m_shapePath.count);
			pNew->m_extPos = helper.extPos();
		}
		return true;
	}
	return false;
}


OpChartShapeAtomBase::OpChartShapeAtomBase(OpEnv* env, const EtChartShapeAtomOp& op, OpType tp)
	: OpShapeAtomBase(env, op, tp)
{
	shapeObjId = op.shapeObjId;
}

bool OpChartShapeAtomBase::tfByCl(const OpBase* opCl, VecOp& vecOpSv)
{
	
	return false;
}

bool OpChartShapeAtomBase::tfByClCrossSht(const OpBase* opCl, VecOp& vecOpSv)
{
	
	return false;
}

const binary_wo::VarInt32Array OpChartShapeAtomBase::shapeIdxPath() const
{
	return binary_wo::VarInt32Array(m_shapePath.path.data(), m_shapePath.path.size());
}

const binary_wo::VarInt32Array OpChartShapeAtomBase::shapeCount() const
{
	return binary_wo::VarInt32Array(m_shapePath.count.data(), m_shapePath.count.size());
}

const std::vector<int32>& OpChartShapeAtomBase::shapeIdxPathV() const
{
	return m_shapePath.path;
}

const std::vector<int32>& OpChartShapeAtomBase::shapeCountV() const
{
	return m_shapePath.count;
}

const WebID OpChartShapeAtomBase::getShapeObjId() const
{
	return shapeObjId;
}

bool OpChartShapeAtomBase::tfByShapesDelSeq(const OpShapesPaths& shapesSeq, VecOp& vecOpSv)
{
	return false;
}

bool OpChartShapeAtomBase::tfByShapesDelSet(const OpShapesPaths& shapesSet, VecOp& vecOpSv)
{
	return false;
}

bool OpChartShapeAtomBase::tfByDelShape(const OpShapePath& shape, VecOp& vecOpSv)
{
	return false;
}


OpShapeObj* OpDeleteShape::clone()
{
	return new OpDeleteShape(*this);
}

OpHyperlink::OpHyperlink(OpEnv* env, const KwCommand* src)
	: OpBaseHasOneObjSheet(env, src, ot_range)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	if (param.has("anchorRange"))
		m_rc = VarReadRect(param.get_s("anchorRange"));
	else
		m_rc.left = m_rc.bottom = m_rc.right = m_rc.top = -1;
}

OpHyperlink::OpHyperlink(OpEnv* env, const KwCommand* src, RECT& rc)
	: OpBaseHasOneObjSheet(env, src, ot_range)
	, m_rc(rc)
{

}

bool OpHyperlink::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if (param.has("anchorRange"))
	{
		binary_wo::VarObj anchorRange = param.get_s("anchorRange");
		updateSheetIdxHelper(anchorRange, "objSheet", "sheetIdx");
	}
	return OpBaseHasOneObjSheet::updateSheetIdx();
}

bool OpHyperlink::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	if (!isValidRect(m_rc))
		return false;

	switch (opSv->tp())
	{
	case ot_range_Insert:
	{
		std::vector<RECT> vecRes;
		const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

		RectTrans::Param tsp = RectTrans::ts_move_expand_abridge;
		RectTrans rectTrans(tsp, m_env->m_bmp);
		if (pSv->m_dir == dir_hori)
			rectTrans.InsertRight(pSv->m_rc, m_rc, vecRes);
		else
			rectTrans.InsertDown(pSv->m_rc, m_rc, vecRes);

		return MakeRes(vecRes, vecOpCl);
	}
	case ot_range_Delete:
	{
		std::vector<RECT> vecRes;
		const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);

		RectTrans::Param tsp = RectTrans::ts_move_expand_abridge;
		RectTrans rectTrans(tsp, m_env->m_bmp);
		if (pSv->m_dir == dir_hori)
			rectTrans.DeleteLeft(pSv->m_rc, m_rc, vecRes);
		else
			rectTrans.DeleteUp(pSv->m_rc, m_rc, vecRes);

		return MakeRes(vecRes, vecOpCl);
	}
	case ot_range_CutInsertVert:
	{
		std::vector<RECT> vecRes;
		const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
		RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
		rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_rc, vecRes);

		return MakeRes(vecRes, vecOpCl);
	}
	case ot_range_CutInsertHorz:
	{
		std::vector<RECT> vecRes;
		const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
		RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
		rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_rc, vecRes);

		return MakeRes(vecRes, vecOpCl);
	}
	default:
		return false;
	}
}

bool OpHyperlink::MakeRes(std::vector<RECT>& vec, VecOp& vecOp)
{
	if (vec.empty() || vec.size() == 1 && Rect_Equal(vec[0], m_rc))
		return false;

	for (size_t i = 0; i < vec.size(); ++i)
	{
		vecOp.m_v.push_back(new OpHyperlink(m_env, m_cmd, vec[i]));
	}
	return true;
}

bool OpHyperlink::isValidRect(const RECT& rc)
{
	return rc.left >= 0 && rc.bottom >= 0 && rc.top >= 0 && rc.right >= 0;
}

KwCommand* OpHyperlink::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	if (param.has("anchorRange"))
	{
		binary_wo::VarObj anchor = param.get_s("anchorRange");
		wo::VarWriteRect(m_rc, anchor);
	}

	return OpBase::detachCmd();
}

OpSheetsAdd::OpSheetsAdd(OpEnv* env, const KwCommand* cmd)
	: OpBase(env, cmd, ot_sheets_Add)
{

}

KwCommand* OpSheetsAdd::detachCmd()
{
	return OpBase::detachCmd();
}

bool OpSheetsAdd::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	bool bRet = false;
	if (param.has("before"))
	{
		binary_wo::VarObj vBefore = param.get("before");
		if (updateSheetIdxHelper(vBefore, "objSheet", "sheetIdx"))
			bRet = true;
	}
	if (param.has("after"))
	{
		binary_wo::VarObj vAfter = param.get("after");
		if (updateSheetIdxHelper(vAfter, "objSheet", "sheetIdx"))
			bRet = true;
	}
	return bRet;
}

OpSheetsMove::OpSheetsMove(OpEnv* env, const KwCommand* cmd)
	: OpBase(env, cmd, ot_sheets_Move)
{

}

KwCommand* OpSheetsMove::detachCmd()
{
	return OpBase::detachCmd();
}

bool OpSheetsMove::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	bool bRet = false;

	if (updateSheetIdxHelper(param, "objSheet", "fromIdx"))
		bRet = true;
	if (param.has("before"))
	{
		binary_wo::VarObj vBefore = param.get("before");
		if (updateSheetIdxHelper(vBefore, "objSheet", "sheetIdx"))
			bRet = true;
	}
	if (param.has("after"))
	{
		binary_wo::VarObj vAfter = param.get("after");
		if (updateSheetIdxHelper(vAfter, "objSheet", "sheetIdx"))
			bRet = true;
	}
	return bRet;
}

OpSheetsCopy::OpSheetsCopy(OpEnv* env, const KwCommand* cmd)
	: OpBase(env, cmd, ot_sheets_Add)
{
}

KwCommand* OpSheetsCopy::detachCmd()
{
	return OpBase::detachCmd();
}

bool OpSheetsCopy::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	return updateSheetIdxHelper(param, "objSheet", "fromIdx");
}

OpRangePaste::OpRangePaste(OpEnv* env, const KwCommand* cmd)
	: OpBase(env, cmd, ot_range)
	, m_multiRangeMode(false)
{
	ASSERT(cmd != NULL);
	binary_wo::VarObj param = cmd->cast().get("param");
	m_dest = VarReadRect(param);

	if (param.has("srcRange"))
	{
		binary_wo::VarObj src = param.get("srcRange");
		m_src = VarReadRect(src);
		m_multiRangeMode = false;//单选区
	}
	else if(param.has("srcRanges"))
	{
		binary_wo::VarObj rgs = param.get_s("srcRanges");
		for (int i = 0; i < rgs.arrayLength_s(); i++)
		{
			binary_wo::VarObj rg = rgs.at_s(i);
			m_rcVec.push_back(VarReadRect(rg));
		}
		m_multiRangeMode = true;//多选区
	}
	else
	{
		m_src.left = m_src.bottom = m_src.right = m_src.top = -1;
	}
}

//单选区
OpRangePaste::OpRangePaste(OpEnv* env, const KwCommand* cmd, const RECT& src, const RECT& dst, OpDir dir)
	:OpBase(env, cmd, ot_range)
	, m_multiRangeMode(false)
{
	m_src = src;
	m_dest = dst;
	m_dir = dir;
}

//多选区
OpRangePaste::OpRangePaste(OpEnv* env, const KwCommand* cmd, const std::vector<RECT>& rcVec, const RECT &dst, OpDir dir)
	:OpBase(env, cmd, ot_range)
	,m_multiRangeMode(true)
{
	m_rcVec = rcVec;
	m_dest = dst;
	m_dir = dir;
}

KwCommand* OpRangePaste::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_dest, param);
	if (param.has("srcRange"))//单复制选区
	{
		binary_wo::VarObj src = param.get("srcRange");
		VarWriteRect(m_src, src);
	}
	else if(param.has("rgs"))//多复制选区
	{
		binary_wo::VarObj rgs = param.add_field_array("rgs", binary_wo::typeStruct);
		for (auto it = m_rcVec.begin(); it != m_rcVec.end(); ++it)
		{
			binary_wo::VarObj rg = rgs.add_item_struct();
			VarWriteRect(*it, rg);
		}
	}
	return OpBase::detachCmd();
}

bool OpRangePaste::updateSheetIdx()
{
	auto updateIdx = [&](binary_wo::VarObj& obj)
	{
		if (obj.has("sheetStId"))
			return false;
		else
			return updateSheetIdxHelper(obj, "objSheet", "sheetIdx");
	};
	bool bRet = false;
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if (param.has("srcRange"))//单复制选区
	{
		binary_wo::VarObj src = param.get("srcRange");
		bRet = updateIdx(src);
	}
	else if (param.has("rgs"))//多复制选区
	{
		binary_wo::VarObj rgs = param.get_s("rgs");
		ASSERT(rgs.type() == binary_wo::typeArray);
		for (int i = 0; i < rgs.arrayLength_s(); i++)
		{
			binary_wo::VarObj rgItem = rgs.at_s(i);
			bRet |= updateIdx(rgItem);
		}
	}
	return (updateIdx(param) || bRet);
}

bool OpRangePaste::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	if(m_multiRangeMode)
		return multiSrcRangeTfBySv(opSv,vecOpCl);
	else
		return oneSrcRangeTfBySv(opSv,vecOpCl);
}

//单选区
bool OpRangePaste::oneSrcRangeTfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	if (!isValidRect(m_dest))
		return false;

	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			std::vector<RECT> resSrc;
			std::vector<RECT> resDst;
			if (pSv->m_dir == dir_hori)
			{
				if (isValidRect(m_src))
					rectTrans.InsertRight(pSv->m_rc, m_src, resSrc);
				rectTrans.InsertRight(pSv->m_rc, m_dest, resDst);
			}
			else
			{
				if (isValidRect(m_src))
					rectTrans.InsertDown(pSv->m_rc, m_src, resSrc);
				rectTrans.InsertDown(pSv->m_rc, m_dest, resDst);
			}

			return MakeRes(resSrc, resDst, vecOpCl);

		}
	case ot_range_Delete:
		{
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			std::vector<RECT> resSrc;
			std::vector<RECT> resDst;
			if (pSv->m_dir == dir_hori)
			{
				if (isValidRect(m_src))
					rectTrans.DeleteLeft(pSv->m_rc, m_src, resSrc);
				rectTrans.DeleteLeft(pSv->m_rc, m_dest, resDst);
			}
			else
			{
				if (isValidRect(m_src))
					rectTrans.DeleteUp(pSv->m_rc, m_src, resSrc);
				rectTrans.DeleteUp(pSv->m_rc, m_dest, resDst);
			}
			return MakeRes(resSrc, resDst, vecOpCl);
		}
	case ot_range_CutInsertVert:
		{
			std::vector<RECT> resSrc;
			std::vector<RECT> resDst;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			if (isValidRect(m_src))
				rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_src, resSrc);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_dest, resDst);

			return MakeRes(resSrc, resDst, vecOpCl);
		}
	case ot_range_CutInsertHorz:
		{
			std::vector<RECT> resSrc;
			std::vector<RECT> resDst;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			if (isValidRect(m_src))
				rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_src, resSrc);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_dest, resDst);

			return MakeRes(resSrc, resDst, vecOpCl);
		}
	default:
		return false;
	}
}

//多选区的判断是否发生了操作变换的方法。
bool OpRangePaste::multiSrcRangeTfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	std::vector<RECT> vecRes;
	bool bChanged = false;
	//检查每一个复制的区域是否发生了操作变换。
	for (auto it = m_rcVec.begin(); it != m_rcVec.end(); ++it)
	{
		if (tfSingleRcBySv(opSv, *it, vecRes))
			bChanged = true;
	}

	//检查粘贴的区域是否发生了操作变换
	RECT newDest = m_dest;
	if(tfDestRangeBySv(opSv, vecOpCl, newDest))
		bChanged = true;
	
	vecOpCl.m_v.push_back(new OpRangePaste(m_env, m_cmd, vecRes, newDest, m_dir));
	return bChanged;
}

//vec保存执行操作变换后的RECT位置。
bool OpRangePaste::tfSingleRcBySv(const OpBase* opSv, const RECT &rc, std::vector<RECT> &vec)
{
	switch (opSv->tp())
	{
		case ot_range_Insert:
		{
			std::vector<RECT> vecRes;
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);
			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			if (pSv->m_dir == dir_hori)
				rectTrans.InsertRight(pSv->m_rc, rc, vecRes);
			else
				rectTrans.InsertDown(pSv->m_rc, rc, vecRes);

			vec.insert(vec.end(), vecRes.begin(), vecRes.end());//收集之
			return isRectTransformed(vecRes, rc);//判断是否发生了操作变换。
		}
		case ot_range_Delete:
		{
			std::vector<RECT> vecRes;
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			if (pSv->m_dir == dir_hori)
				rectTrans.DeleteLeft(pSv->m_rc, rc, vecRes);
			else
				rectTrans.DeleteUp(pSv->m_rc, rc, vecRes);

			vec.insert(vec.end(), vecRes.begin(), vecRes.end());
			return isRectTransformed(vecRes, rc);
		}
		case ot_range_CutInsertVert:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none, m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, rc, vecRes);

			vec.insert(vec.end(), vecRes.begin(), vecRes.end());
			return isRectTransformed(vecRes, rc);
		}
		case ot_range_CutInsertHorz:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none, m_env->m_bmp);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, rc, vecRes);

			vec.insert(vec.end(), vecRes.begin(), vecRes.end());
			return isRectTransformed(vecRes, rc);
		}
		default:
			return false;
	}
}

bool OpRangePaste::tfDestRangeBySv(const OpBase* opSv, VecOp& vecOpCl, RECT& newDest)
{
	newDest = m_dest;
	if(!isValidRect(m_dest))
		return false;

	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			std::vector<RECT> resDst;
			if (pSv->m_dir == dir_hori)
			{
				rectTrans.InsertRight(pSv->m_rc, m_dest, resDst);
			}
			else
			{
				rectTrans.InsertDown(pSv->m_rc, m_dest, resDst);
			}

			return MakeDestRes(resDst, vecOpCl, newDest);

		}
	case ot_range_Delete:
		{
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			std::vector<RECT> resDst;
			if (pSv->m_dir == dir_hori)
			{
				rectTrans.DeleteLeft(pSv->m_rc, m_dest, resDst);
			}
			else
			{
				rectTrans.DeleteUp(pSv->m_rc, m_dest, resDst);
			}
			return MakeDestRes(resDst, vecOpCl, newDest);
		}
	case ot_range_CutInsertVert:
		{
			std::vector<RECT> resDst;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_dest, resDst);

			return MakeDestRes(resDst, vecOpCl, newDest);
		}
	case ot_range_CutInsertHorz:
		{
			std::vector<RECT> resDst;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_dest, resDst);

			return MakeDestRes(resDst, vecOpCl, newDest);
		}
	default:
		return false;
	}
}

bool OpRangePaste::MakeDestRes(std::vector<RECT>& resDst, VecOp& vecOp, RECT& newDest)
{
	//多复制区域 检查dest目标区域是否发生了操作变换
	if (resDst.empty() || resDst.size()>1)
		return false;
	//Todo:源区域和目标区域的数目和值都和原本的一样。
	if (resDst.size() == 1 && Rect_Equal(resDst[0], m_dest))
		return false;

	//Todo:走到来了这里说明发生了操作变换。(要么是复制区域发生了操作变换 或者 粘贴的目标区域需要发生操作变换)
	//Todo:重新构造参数
	newDest = resDst[0];
	if (!isValidRect(resDst[0]))
		newDest = m_dest;
	
	return true;
}

//Todo:resSrc表示复制区域，resDst表示粘贴区域
bool OpRangePaste::MakeRes(std::vector<RECT>& resSrc, std::vector<RECT>& resDst, VecOp& vecOp)
{
	if (resDst.empty() || resDst.size()>1 || resSrc.size() > 1)
		return false;
	//Todo:源区域和目标区域的数目和值都和原本的一样。
	if (resDst.size() == 1 && Rect_Equal(resDst[0], m_dest) &&
		(resSrc.empty() || resSrc.size() == 1 && Rect_Equal(resSrc[0], m_src)))
		return false;

	//Todo:走到来了这里说明发生了操作变换。(要么是复制区域发生了操作变换 或者 粘贴的目标区域需要发生操作变换)
	//Todo:重新构造参数
	RECT src;
	if (resSrc.empty() || (resSrc.size() > 0 && !isValidRect(resSrc[0])))
		src = m_src;
	else
		src = resSrc[0];

	RECT dest = resDst[0];
	if (!isValidRect(resDst[0]))
		dest = m_dest;
	
	vecOp.m_v.push_back(new OpRangePaste(m_env, m_cmd, src, dest, m_dir));
	return true;
}

bool OpRangePaste::isValidRect(const RECT& rc)
{
	return rc.left >= 0 && rc.bottom >= 0 && rc.top >= 0 && rc.right >= 0 && rc.left <= rc.right && rc.top <= rc.bottom;
}

bool OpRangePaste::isRectTransformed(const std::vector<RECT>& vec, const RECT &rc)
{
	if (vec.empty() || vec.size() == 1 && Rect_Equal(vec[0], rc))
		return false;

	return true;
}

//----------------------------------OpFreezePanes-----------------------------------------
namespace freeze_panes_op
{

//transform rect on range insertion, not split
static bool OnRangeInsert(const OpRangeInsert &srv_op, BMP_PTR bmp, const RECT &rect, RECT *out_rect)
{
	RectTrans rectTrans(RectTrans::ts_move_expand_abridge, bmp);
	std::vector<RECT> vec_res;
	if (srv_op.m_dir == dir_hori) {
		rectTrans.InsertRight(srv_op.m_rc, rect, vec_res);
	}
	else {
		rectTrans.InsertDown(srv_op.m_rc, rect, vec_res);
	}

    if (!vec_res.empty() && !Rect_Equal(rect, vec_res[0])) {
        *out_rect = vec_res[0];
        return true;
    }

    return false;
}

//transform rect on range deletion, not split
static bool OnRangeDelete(const OpRangeDelete &srv_op, BMP_PTR bmp, const RECT &rect, RECT *out_rect)
{
	RectTrans rectTrans(RectTrans::ts_move_expand_abridge, bmp);
	std::vector<RECT> vec_res;
	if (srv_op.m_dir == dir_hori) {
		rectTrans.DeleteLeft(srv_op.m_rc, rect, vec_res);
	}
	else {
		rectTrans.DeleteUp(srv_op.m_rc, rect, vec_res);
	}

    if (!vec_res.empty() && !Rect_Equal(rect, vec_res[0])) {
        *out_rect = vec_res[0];
        return true;
    }

    return false;
}

//transform rect on range deletion, not split
static bool OnRangeCutInsertVert(const OpRangeCutInsertVert &srv_op, BMP_PTR bmp, const RECT &rect, RECT *out_rect)
{
	std::vector<RECT> vecRes;
	RectTrans rectTrans(RectTrans::ts_none , bmp);
	rectTrans.SwapVert(srv_op.m_rcTop, srv_op.m_rcBottom, rect, vecRes);

    if (!vecRes.empty() && !Rect_Equal(rect, vecRes[0])) {
        *out_rect = vecRes[0];
        return true;
    }

    return false;
}

static bool OnRangeCutInsertHorz(const OpRangeCutInsertHorz &srv_op, BMP_PTR bmp, const RECT &rect, RECT *out_rect)
{
	std::vector<RECT> vecRes;
	RectTrans rectTrans(RectTrans::ts_none , bmp);
	rectTrans.SwapHorz(srv_op.m_rcLeft, srv_op.m_rcRight, rect, vecRes);

	if (!vecRes.empty() && !Rect_Equal(rect, vecRes[0])) {
		*out_rect = vecRes[0];
		return true;
	}

	return false;
}

} // namespace freeze_panes_op

OpFreezePanes::OpFreezePanes(OpEnv *env, const KwCommand *src)
    : OpBaseHasOneObjSheet(env, src, ot_range)
{
	const binary_wo::VarObj param = src->cast().get("param");
	const bool frozen = param.field_bool("frozen");
	range_rect_.left = range_rect_.top = 0;
	range_rect_.right = range_rect_.bottom = 1;
	if (frozen) {
		range_rect_ = VarReadRect(param);
	}

	active_rect_.top = param.field_int32("activeCellRow");
	active_rect_.left = param.field_int32("activeCellCol");
	active_rect_.right = param.field_int32("activeCellCol");
	active_rect_.bottom = param.field_int32("activeCellRow");
}

//transform by server side operations
bool OpFreezePanes::tfBySv(const OpBase *srv_op, VecOp &/*client_ops*/)
{
	using namespace wo::freeze_panes_op;
	switch (srv_op->tp())
	{
	case ot_range_Insert:
		OnRangeInsert(static_cast<const OpRangeInsert&>(*srv_op), m_env->m_bmp, range_rect_, &range_rect_);
        OnRangeInsert(static_cast<const OpRangeInsert&>(*srv_op), m_env->m_bmp, active_rect_, &active_rect_);
        break;
	case ot_range_Delete:
		OnRangeDelete(static_cast<const OpRangeDelete&>(*srv_op), m_env->m_bmp, range_rect_, &range_rect_);
        OnRangeDelete(static_cast<const OpRangeDelete&>(*srv_op), m_env->m_bmp, active_rect_, &active_rect_);
        break;
	case ot_range_CutInsertVert:
		OnRangeCutInsertVert(static_cast<const OpRangeCutInsertVert&>(*srv_op), m_env->m_bmp, range_rect_, &range_rect_);
        OnRangeCutInsertVert(static_cast<const OpRangeCutInsertVert&>(*srv_op), m_env->m_bmp, active_rect_, &active_rect_);
		break;
	case ot_range_CutInsertHorz:
		OnRangeCutInsertHorz(static_cast<const OpRangeCutInsertHorz&>(*srv_op), m_env->m_bmp, range_rect_, &range_rect_);
		OnRangeCutInsertHorz(static_cast<const OpRangeCutInsertHorz&>(*srv_op), m_env->m_bmp, active_rect_, &active_rect_);
		break;
	default:
		return false;
	}

	//not cause more operations
    return false;
}

KwCommand *OpFreezePanes::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

    binary_wo::VarObj param = m_cmd->cast().get("param");
	const bool frozen = param.field_bool("frozen");
	if (frozen) {
		VarWriteRect(range_rect_, param);
	}

    param.add_field_int32("activeCellCol", active_rect_.right);
	param.add_field_int32("activeCellRow", active_rect_.bottom);

	return OpBase::detachCmd();
}

//////////////////////////////////////////////////////////////////////////////////////////
OpConditionFormatBase::OpConditionFormatBase(OpEnv* env, const KwCommand* cmd, OpType tp)
	: OpBase(env, cmd, tp)
	, m_iPriority(-1)
{
	ASSERT(cmd != NULL);
}

void OpConditionFormatBase::initRgsFromCmd()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	binary_wo::VarObj varRule = param.get_s("rule");
	binary_wo::VarObj varRgs = varRule.get_s("ranges");
	for (int i = 0; i < varRgs.arrayLength(); i++)
	{
		binary_wo::VarObj rg = varRgs.at(i);
		m_vecSrc.push_back(VarReadRect(rg));
	}
}

void OpConditionFormatBase::initPriorityFromCmd()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	binary_wo::VarObj varRule = param.get_s("rule");
	IDX iSheet = param.field_int32("sheetIdx");
	m_iPriority = getPriority(m_env, varRule, iSheet);
}

KwCommand* OpConditionFormatBase::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	binary_wo::VarObj varRule = param.get_s("rule");
	//binary_wo::VarObj varRgs = varRule.get_s("ranges");
	binary_wo::VarObj varRgs = varRule.add_field_array("ranges", binary_wo::typeStruct);

	//ASSERT(varRgs.arrayLength() == m_vecSrc.size());
	for (int i = 0; i < varRgs.arrayLength(); i++)
	{
		binary_wo::VarObj rg = varRgs.at(i);
		VarWriteRect(m_vecSrc[i], rg);
	}

	ASSERT(m_iPriority >= 0);
	varRule.add_field_int32("priority", m_iPriority);

	return OpBase::detachCmd();
}

bool OpConditionFormatBase::MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp)
{
	return false;
}

int OpConditionFormatBase::priority() const
{
	return m_iPriority;
}

bool OpConditionFormatBase::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	return updateSheetIdxHelper(param, "objSheet", "sheetIdx");
}

bool OpConditionFormatBase::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);

			std::vector<RECT> resSrc;
			for (size_t i = 0; i < m_vecSrc.size(); i++)
			{
				RECT src = m_vecSrc[i];
				std::vector<RECT> tmp;
				if (pSv->m_dir == dir_hori)
				{
					rectTrans.InsertRight(pSv->m_rc, src, tmp);
				}
				else
				{
					rectTrans.InsertDown(pSv->m_rc, src, tmp);
				}
				if (!tmp.empty())
					resSrc.push_back(tmp[0]);
			}
			return MakeRes(resSrc, vecOpCl);
		}
	case ot_range_Delete:
		{
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);

			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);

			std::vector<RECT> resSrc;
			for (size_t i = 0; i < m_vecSrc.size(); i++)
			{
				RECT src = m_vecSrc[i];
				std::vector<RECT> tmp;
				if (pSv->m_dir == dir_hori)
				{
					rectTrans.DeleteLeft(pSv->m_rc, src, tmp);
				}
				else
				{
					rectTrans.DeleteUp(pSv->m_rc, src, tmp);
				}
				if (!tmp.empty())
					resSrc.push_back(tmp[0]);
			}
			return MakeRes(resSrc, vecOpCl);
		}
	default:
		return false;
	}
}

//////////////////////////////////////////////////////////////////////////////////////////
OpNewConditionFormat::OpNewConditionFormat(OpEnv* env, const KwCommand* cmd)
	: OpConditionFormatBase(env, cmd, ot_condfmt_New)
{
	initRgsFromCmd();
}

OpNewConditionFormat::OpNewConditionFormat(OpEnv* env, const KwCommand* cmd, const std::vector<RECT>& vecSrc)
	: OpConditionFormatBase(env, cmd, ot_condfmt_New)
{
	m_vecSrc.assign(vecSrc.begin(), vecSrc.end());
}

bool OpNewConditionFormat::MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp)
{
	if (resSrc.empty())
		return false;

	vecOp.m_v.push_back(new OpNewConditionFormat(m_env, m_cmd, resSrc));
	return true;
}

//////////////////////////////////////////////////////////////////////////////////////////
OpDelConditionFormat::OpDelConditionFormat(OpEnv* env, const KwCommand* cmd)
	: OpConditionFormatBase(env, cmd, ot_condfmt_Delete)
{
	initRgsFromCmd();
	initPriorityFromCmd();
}

OpDelConditionFormat::OpDelConditionFormat(OpEnv* env, const KwCommand* cmd, const std::vector<RECT>& vecSrc)
	: OpConditionFormatBase(env, cmd, ot_condfmt_Delete)
{
	m_vecSrc.assign(vecSrc.begin(), vecSrc.end());
	initPriorityFromCmd();
}

OpDelConditionFormat::OpDelConditionFormat(OpEnv* env, const KwCommand* cmd, int iPriority)
	: OpConditionFormatBase(env, cmd, ot_condfmt_Delete)
{
	initRgsFromCmd();
	m_iPriority = iPriority;
}

bool OpDelConditionFormat::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_condfmt_New:
		{
			int iPriority = -1;
			bool bRet = CondFmtTrans::TransByAdd(m_iPriority, iPriority);
			return bRet && MakeRes(iPriority, vecOpCl);
		}
	case ot_condfmt_Delete:
		{
			int iPriority = -1;
			const OpDelConditionFormat* pSv = static_cast<const OpDelConditionFormat*>(opSv);
			bool bRet = CondFmtTrans::TransByDel(pSv->m_iPriority, m_iPriority, iPriority);
			return bRet && MakeRes(iPriority, vecOpCl);
		}
	case ot_condfmt_Swap:
		{
			int iPriority = -1;
			const OpSwapConditionFormat* pSv = static_cast<const OpSwapConditionFormat*>(opSv);
			bool bRet = CondFmtTrans::TransBySwap(pSv->swapPara(), m_iPriority, iPriority);
			return bRet && MakeRes(iPriority, vecOpCl);
		}
	case ot_condfmt_Edit:
		return false;
	default:
		return OpConditionFormatBase::tfBySv(opSv, vecOpCl);
	}
}

bool OpDelConditionFormat::MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp)
{
	if (resSrc.empty())
		return false;

	vecOp.m_v.push_back(new OpDelConditionFormat(m_env, m_cmd, resSrc));
	return true;
}

bool OpDelConditionFormat::MakeRes(int iPriority, VecOp& vecOp)
{
	if (iPriority < 0)
		return false;

	vecOp.m_v.push_back(new OpDelConditionFormat(m_env, m_cmd, iPriority));
	return true;
}

//////////////////////////////////////////////////////////////////////////////////////////
OpEditConditionFormat::OpEditConditionFormat(OpEnv* env, const KwCommand* cmd)
	: OpConditionFormatBase(env, cmd, ot_condfmt_Edit)
{
	initRgsFromCmd();
	initPriorityFromCmd();
}

OpEditConditionFormat::OpEditConditionFormat(OpEnv* env, const KwCommand* cmd, const std::vector<RECT>& vecSrc)
	: OpConditionFormatBase(env, cmd, ot_condfmt_Edit)
{
	m_vecSrc.assign(vecSrc.begin(), vecSrc.end());
	initPriorityFromCmd();
}

OpEditConditionFormat::OpEditConditionFormat(OpEnv* env, const KwCommand* cmd, int iPriority)
	: OpConditionFormatBase(env, cmd, ot_condfmt_Edit)
{
	initRgsFromCmd();
	m_iPriority = iPriority;
}

bool OpEditConditionFormat::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_condfmt_New:
		{
			int iPriority = -1;
			bool bRet = CondFmtTrans::TransByAdd(m_iPriority, iPriority);
			return bRet && MakeRes(iPriority, vecOpCl);
		}
	case ot_condfmt_Delete:
		{
			int iPriority = -1;
			const OpDelConditionFormat* pSv = static_cast<const OpDelConditionFormat*>(opSv);
			bool bRet = CondFmtTrans::TransByDel(pSv->priority(), m_iPriority, iPriority);
			return bRet && MakeRes(iPriority, vecOpCl);
		}
	case ot_condfmt_Swap:
		{
			int iPriority = -1;
			const OpSwapConditionFormat* pSv = static_cast<const OpSwapConditionFormat*>(opSv);
			bool bRet = CondFmtTrans::TransBySwap(pSv->swapPara(), m_iPriority, iPriority);
			return bRet && MakeRes(iPriority, vecOpCl);
		}
	case ot_condfmt_Edit:
		return false;
	default:
		return OpConditionFormatBase::tfBySv(opSv, vecOpCl);
	}
}

bool OpEditConditionFormat::MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp)
{
	if (resSrc.empty())
		return false;

	vecOp.m_v.push_back(new OpEditConditionFormat(m_env, m_cmd, resSrc));
	return true;
}

bool OpEditConditionFormat::MakeRes(int iPriority, VecOp& vecOp)
{
	if (iPriority < 0)
		return false;

	vecOp.m_v.push_back(new OpEditConditionFormat(m_env, m_cmd, iPriority));
	return true;
}

///////////////////////////////////////////////////////////////////////////////////////////
OpSwapConditionFormat::OpSwapConditionFormat(OpEnv* env, const KwCommand* cmd)
	: OpBase(env, cmd, ot_condfmt_Swap)
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	binary_wo::VarObj varRuleMove = param.get_s("rule_move");
	IDX iSheet = param.field_int32("sheetIdx");
	m_swapPara.m_iMove = getPriority(m_env, varRuleMove, iSheet);
	if(param.has("rule_below"))
	{
		binary_wo::VarObj varRuleBelow = param.get_s("rule_below");
		m_swapPara.m_iBelow = getPriority(m_env, varRuleBelow, iSheet);
	}
}

OpSwapConditionFormat::OpSwapConditionFormat(OpEnv* env, const KwCommand* cmd, const CfSwapPara& para)
	: OpBase(env, cmd, ot_condfmt_Swap)
{
	m_swapPara = para;
}

bool OpSwapConditionFormat::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_condfmt_New:
		{
			CfSwapPara para;
			bool bRet = CondFmtTrans::SwapByAdd(m_swapPara, para);
			return bRet && MakeRes(para, vecOpCl);
		}
	case ot_condfmt_Delete:
		{
			CfSwapPara para;
			const OpDelConditionFormat* pSv = static_cast<const OpDelConditionFormat*>(opSv);
			bool bRet = CondFmtTrans::SwapByDel(pSv->priority(), m_swapPara, para);
			return bRet && MakeRes(para, vecOpCl);
		}
	case ot_condfmt_Swap:
		{
			CfSwapPara para;
			const OpSwapConditionFormat* pSv = static_cast<const OpSwapConditionFormat*>(opSv);
			bool bRet = CondFmtTrans::SwapBySwap(pSv->swapPara(), m_swapPara, para);
			return bRet && MakeRes(para, vecOpCl);
		}
	case ot_condfmt_Edit:
		return false;
	default:
		return false;
	}
}

bool OpSwapConditionFormat::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	return updateSheetIdxHelper(param, "objSheet", "sheetIdx");
}

KwCommand* OpSwapConditionFormat::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	if(m_swapPara.isValid())
	{
		binary_wo::VarObj param = m_cmd->cast().get("param");
		binary_wo::VarObj varRuleMove = param.get_s("rule_move");
		varRuleMove.add_field_int32("priority", m_swapPara.m_iMove);
		if(param.has("rule_below"))
		{
			binary_wo::VarObj varRuleBelow = param.get_s("rule_below");
			varRuleBelow.add_field_int32("priority", m_swapPara.m_iBelow);
		}
	}

	return OpBase::detachCmd();
}

bool OpSwapConditionFormat::MakeRes(const CfSwapPara& para, VecOp& vecOp)
{
	if (!para.isValid())
		return false;

	vecOp.m_v.push_back(new OpSwapConditionFormat(m_env, m_cmd, para));
	return true;
}

const CfSwapPara& OpSwapConditionFormat::swapPara() const
{
	return m_swapPara;
}


///////////////////////////////////////////////////////
OpRangeCutInsertVert::OpRangeCutInsertVert(OpEnv* env, const RECT& rcTop, const RECT& rcBottom)
	: OpBase(env, NULL, ot_range_CutInsertVert)
	, m_rcTop(rcTop)
	, m_rcBottom(rcBottom)
{
}

OpRangeCutInsertHorz::OpRangeCutInsertHorz(OpEnv* env, const RECT& rcLeft, const RECT& rcRight)
	: OpBase(env, NULL, ot_range_CutInsertHorz)
	, m_rcLeft(rcLeft)
	, m_rcRight(rcRight)
{
}

///////////////////////////////////////////////////////
OpRangeActiveCell::OpRangeActiveCell(OpEnv *env, const KwCommand *src)
	: OpBaseHasOneObjSheet(env, src, ot_range)
{
}

bool OpRangeActiveCell::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
		case ot_range_Insert:
		{
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			bool bChangeAC = false;
			{
				// transform active cell
				std::vector<RECT> vecRes;
				RectTrans rectTrans(m_tsp , m_env->m_bmp);
				if (pSv->m_dir == dir_hori)
					rectTrans.InsertRight(pSv->m_rc, m_ac, vecRes);
				else
					rectTrans.InsertDown(pSv->m_rc, m_ac, vecRes);

				if (vecRes.size() != 1)
					return false;
				if (!Rect_Equal(vecRes[0], m_rc))
				{
					m_ac = vecRes[0];
					bChangeAC = true;
				}
			}

			{
				// transform range
				std::vector<RECT> vecRes;
				RectTrans rectTrans(m_tsp , m_env->m_bmp);
				if (pSv->m_dir == dir_hori)
					rectTrans.InsertRight(pSv->m_rc, m_rc, vecRes);
				else
					rectTrans.InsertDown(pSv->m_rc, m_rc, vecRes);

				return MakeRes(vecRes, vecOpCl, bChangeAC);
			}
		}
		case ot_range_Delete:
		{
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			bool bChangeAC = false;
			{
				// transform active cell
				std::vector<RECT> vecRes;
				RectTrans rectTrans(m_tsp , m_env->m_bmp);
				if (pSv->m_dir == dir_hori)
					rectTrans.DeleteLeft(pSv->m_rc, m_ac, vecRes);
				else
					rectTrans.DeleteUp(pSv->m_rc, m_ac, vecRes);

				if (vecRes.size() != 1)
					return false;
				if (!Rect_Equal(vecRes[0], m_rc))
				{
					m_ac = vecRes[0];
					bChangeAC = true;
				}
			}

			{
				// transform range
				std::vector<RECT> vecRes;
				RectTrans rectTrans(m_tsp , m_env->m_bmp);
				if (pSv->m_dir == dir_hori)
					rectTrans.DeleteLeft(pSv->m_rc, m_rc, vecRes);
				else
					rectTrans.DeleteUp(pSv->m_rc, m_rc, vecRes);

				return MakeRes(vecRes, vecOpCl, bChangeAC);
			}
		}
		case ot_range_CutInsertVert:
		{
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			bool bChangeAC = false;
			{
				// transform active cell
				std::vector<RECT> vecRes;
				RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
				rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_ac, vecRes);
				if (vecRes.size() != 1)
					return false;
				if (!Rect_Equal(vecRes[0], m_rc))
				{
					m_ac = vecRes[0];
					bChangeAC = true;
				}
			}

			{
				// transform range
				std::vector<RECT> vecRes;
				RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
				rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_rc, vecRes);
				return MakeRes(vecRes, vecOpCl, bChangeAC);
			}
		}
		case ot_range_CutInsertHorz:
		{
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			bool bChangeAC = false;
			{
				// transform active cell
				std::vector<RECT> vecRes;
				RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
				rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_ac, vecRes);
				if (vecRes.size() != 1)
					return false;
				if (!Rect_Equal(vecRes[0], m_rc))
				{
					m_ac = vecRes[0];
					bChangeAC = true;
				}
			}

			{
				// transform range
				std::vector<RECT> vecRes;
				RectTrans rectTrans(RectTrans::ts_none , m_env->m_bmp);
				rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_rc, vecRes);
				return MakeRes(vecRes, vecOpCl, bChangeAC);
			}
		}
		default:
			return false;
	}
}

bool OpRangeActiveCell::MakeRes(std::vector<RECT>& res, VecOp& vecOp, bool bChangeAC)
{
	if (res.empty())
		return false;

	if (!bChangeAC && res.size() == 1 && Rect_Equal(res[0], m_rc))
		return false;

	for (size_t i = 0; i < res.size(); ++i)
		vecOp.m_v.push_back(this->clone(res[i], m_ac));

	return true;
}

///////////////////////////////////////////////////////
OpRangeAutoSum::OpRangeAutoSum(OpEnv *env, const KwCommand *src)
	: OpRangeActiveCell(env, src)
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	m_rc = VarReadRect(param);
	binary_wo::VarObj acObj = param.get("activeCell");
	ROW row = acObj.field_int32("row");
	COL col = acObj.field_int32("col");
	m_ac.top = m_ac.bottom = row;
	m_ac.left = m_ac.right = col;
	m_tsp = RectTrans::ts_move_expand_abridge;
}

OpRangeAutoSum::OpRangeAutoSum(OpEnv *env, const KwCommand *src, const RECT& rc, const RECT& ac, RectTrans::Param tsp)
	: OpRangeActiveCell(env, src)
{
	m_rc = rc;
	m_ac = ac;
	m_tsp = tsp;
}

KwCommand* OpRangeAutoSum::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_rc, param);
	binary_wo::VarObj acObj = param.get("activeCell");
	acObj.add_field_int32("row", m_ac.top);
	acObj.add_field_int32("col", m_ac.left);

	return OpBase::detachCmd();
}

OpBase* OpRangeAutoSum::clone(const RECT& rc, const RECT& ac)
{
	return new OpRangeAutoSum(m_env, m_cmd, rc, ac, m_tsp);
}

//////////////////////////////////////////////////////////////////////////
OpAllowEditRange::OpAllowEditRange(OpEnv* env, const KwCommand* src)
	: OpBase(env, src, ot_range)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	binary_wo::VarObj varRgs = param.get_s("ranges");
	for (int i = 0; i < varRgs.arrayLength_s(); i++)
	{
		binary_wo::VarObj rg = varRgs.at(i);
		m_vecSrc.push_back(VarReadRect(rg));
	}
}

OpAllowEditRange::OpAllowEditRange(OpEnv* env, const KwCommand* src, const std::vector<RECT>& vecSrc)
	: OpBase(env, src, ot_range)
{
	m_vecSrc.assign(vecSrc.begin(), vecSrc.end());
}

bool OpAllowEditRange::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	switch (opSv->tp())
	{
	case ot_range_Insert:
	{
		const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

		RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
		RectTrans rectTrans(rtp, m_env->m_bmp);

		std::vector<RECT> resSrc;
		for (size_t i = 0; i < m_vecSrc.size(); i++)
		{
			RECT src = m_vecSrc[i];
			std::vector<RECT> tmp;
			if (pSv->m_dir == dir_hori)
			{
				rectTrans.InsertRight(pSv->m_rc, src, tmp);
			}
			else
			{
				rectTrans.InsertDown(pSv->m_rc, src, tmp);
			}
			if (!tmp.empty())
				resSrc.push_back(tmp[0]);
		}
		return MakeRes(resSrc, vecOpCl);
	}
	case ot_range_Delete:
	{
		const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);

		RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
		RectTrans rectTrans(rtp, m_env->m_bmp);

		std::vector<RECT> resSrc;
		for (size_t i = 0; i < m_vecSrc.size(); i++)
		{
			RECT src = m_vecSrc[i];
			std::vector<RECT> tmp;
			if (pSv->m_dir == dir_hori)
			{
				rectTrans.DeleteLeft(pSv->m_rc, src, tmp);
			}
			else
			{
				rectTrans.DeleteUp(pSv->m_rc, src, tmp);
			}
			if (!tmp.empty())
				resSrc.push_back(tmp[0]);
		}
		return MakeRes(resSrc, vecOpCl);
	}
	default:
		return false;
	}
}

bool OpAllowEditRange::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	return updateSheetIdxHelper(param, "objSheet", "sheetIdx");
}

KwCommand* OpAllowEditRange::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	binary_wo::VarObj varRgs = param.get_s("ranges");

	ASSERT(varRgs.arrayLength_s() == m_vecSrc.size());
	for (int i = 0; i < varRgs.arrayLength_s(); i++)
	{
		binary_wo::VarObj rg = varRgs.at(i);
		VarWriteRect(m_vecSrc[i], rg);
	}
	return OpBase::detachCmd();
}

bool OpAllowEditRange::MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp)
{
	if (resSrc.empty())
		return false;

	vecOp.m_v.push_back(new OpAllowEditRange(m_env, m_cmd, resSrc));
	return true;
}


///////////////////////////////////////////////////////
OpShapePaste::OpShapePaste(OpEnv* env, const KwCommand* cmd)
	: OpBase(env, cmd, ot_range)
	, m_bCutShapesChanged(false)
	, m_bSelShapesChanged(false)
{
	ASSERT(cmd != NULL);
	binary_wo::VarObj param = cmd->cast().get("param");
	m_dest = VarReadRect(param);
	if (param.has("isCut") && param.field_bool("isCut"))
		OpShapeHelper::loadShapesFromVarObj(param.get_s("shapes"), m_cutShapes);
	if (param.has("selShapes"))
		OpShapeHelper::loadShapesFromVarObj(param.get_s("selShapes"), m_selShapes);
}

OpShapePaste::OpShapePaste(OpEnv* env, const KwCommand* cmd, const RECT& dst, OpDir dir)
	: OpBase(env, cmd, ot_range)
	, m_bCutShapesChanged(false)
	, m_bSelShapesChanged(false)
{
	m_dest = dst;
	m_dir = dir;
}

KwCommand* OpShapePaste::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_dest, param);

	if (m_bCutShapesChanged && !m_cutShapes.empty())
	{
		ASSERT(param.has("isCut") && param.field_bool("isCut"));
		binary_wo::VarObj objShapes = param.add_field_array("shapes", binary_wo::typeStruct);
		OpShapeHelper::saveShapesToVarObj(m_cutShapes, objShapes);
	}
	if (m_bSelShapesChanged)
	{
		binary_wo::VarObj objShapes = param.add_field_array("selShapes", binary_wo::typeStruct);
		OpShapeHelper::saveShapesToVarObj(m_selShapes, objShapes);
	}

	return OpBase::detachCmd();
}

bool OpShapePaste::updateSheetIdx()
{
	bool bRet = false;
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if (updateSheetIdxHelper(param, "objSheet", "sheetIdx"))
		bRet = true;
	if (updateSheetIdxHelper(param, "shapeObjSheet", "shapeSheetIdx"))
		bRet = true;
	return bRet;
}

bool OpShapePaste::updateShapeIdxPath()
{
	bool bRet = false;
	binary_wo::VarObj param = m_cmd->cast().get("param");
	IDX shapeSheetIdx = param.field_int32("shapeSheetIdx");
	binary_wo::VarObj shapes = param.get("shapes");
	for (int i = 0; i < shapes.arrayLength(); i++)
    {
		binary_wo::VarObj shape = shapes.at(i);
		shape.add_field_int32("sheetIdx", shapeSheetIdx);
		if (updateShapeIdxPathHelper(shape , "objShape", "shapeIdxPath"))
			bRet = true;
    }
	return bRet;
}

bool OpShapePaste::isValidRect(const RECT& rc)
{
	return rc.left >= 0 && rc.bottom >= 0 && rc.top >= 0 && rc.right >= 0 && rc.left <= rc.right && rc.top <= rc.bottom;
}

bool OpShapePaste::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	if (!isValidRect(m_dest))
		return false;

	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			std::vector<RECT> resDst;
			if (pSv->m_dir == dir_hori)
				rectTrans.InsertRight(pSv->m_rc, m_dest, resDst);
			else
				rectTrans.InsertDown(pSv->m_rc, m_dest, resDst);

			return MakeRes(resDst, vecOpCl);

		}
	case ot_range_Delete:
		{
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			RectTrans::Param rtp = RectTrans::ts_move_expand_abridge;
			RectTrans rectTrans(rtp, m_env->m_bmp);
			std::vector<RECT> resDst;
			if (pSv->m_dir == dir_hori)
				rectTrans.DeleteLeft(pSv->m_rc, m_dest, resDst);
			else
				rectTrans.DeleteUp(pSv->m_rc, m_dest, resDst);

			return MakeRes(resDst, vecOpCl);
		}
	case ot_range_CutInsertVert:
		{
			std::vector<RECT> resDst;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_move_abridge_split , m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_dest, resDst);

			return MakeRes(resDst, vecOpCl);
		}
	case ot_shape_Insert:
	case ot_shape_Delete:
	case ot_shape_Move:
		if (!m_selShapes.empty())
		{
			OpShapesPaths selShapes;
			if (EtOpShapeHelper::tfShapesBySv(opSv, m_selShapes, selShapes))
				return makeShapesRes(selShapes, true, vecOpCl);
		}
		break;
	default:
		break;
	}
	return false;
}

bool OpShapePaste::tfBySvCrossSht(const OpBase* opSv, VecOp& vecOpCl)
{
	if (!isValidRect(m_dest))
		return false;

	switch (opSv->tp())
	{
	case ot_shape_Insert:
	case ot_shape_Delete:
	case ot_shape_Move:
		if (!m_cutShapes.empty())
		{
			OpShapesPaths cutShapes;
			if (EtOpShapeHelper::tfShapesBySv(opSv, m_cutShapes, cutShapes))
				return makeShapesRes(cutShapes, false, vecOpCl);
		}
		break;
	default:
		break;
	}
	return false;
}

bool OpShapePaste::makeShapesRes(OpShapesPaths& shapes, bool bSelShapes, VecOp& vecOp)
{
	bool bInvalid = !bSelShapes && shapes.empty();
	if (bInvalid)
	{
		vecOp.m_v.push_back(nullptr);
	}
	else
	{
		OpShapePaste* pNew = new OpShapePaste(m_env, m_cmd, m_dest, m_dir);
		if (bSelShapes)
		{
			pNew->m_selShapes.swap(shapes);
			pNew->m_bSelShapesChanged = true;
		}
		else
		{
			pNew->m_cutShapes.swap(shapes);
			pNew->m_bCutShapesChanged = true;
		}
		vecOp.m_v.push_back(pNew);
	}
	return true;
}

bool OpShapePaste::MakeRes(std::vector<RECT>& resDst, VecOp& vecOp)
{
	if (resDst.empty() || resDst.size() > 1)
		return false;

	if (resDst.size() == 1 && Rect_Equal(resDst[0], m_dest))
		return false;

	RECT dest = resDst[0];
	if (!isValidRect(resDst[0]))
		dest = m_dest;

	OpShapePaste* pNew = new OpShapePaste(m_env, m_cmd, dest, m_dir);
	pNew->m_cutShapes = m_cutShapes;
	pNew->m_selShapes = m_selShapes;
	vecOp.m_v.push_back(pNew);
	return true;
}

bool OpChartShape::updateShapeIdxPath()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	bool changed = OpShapeObj::updateShapeIdxPath();
	bool chartShapeChanged = updateChartShapeIdxPath(param );
	return changed || chartShapeChanged;
}

OpShapeObj* OpChartShape::clone()
{
	return new OpChartShape(*this);
}

bool OpChartShape::updateChartShapeIdxPath(binary_wo::VarObj& param)
{
    if (!param.has("chartShapeIdxPath") || !param.has("objChartShape")) {
        return false;
    }

    IDX iSheet = param.field_int32("sheetIdx");
    drawing::AbstractShape* chartRoot = nullptr;
    {
        binary_wo::VarObj idxPathObj = param.get("shapeIdxPath");
        ShapeIdxPath idxPath;
        idxPath.reserve(idxPathObj.arrayLength());
        for (int i = 0, cnt = idxPathObj.arrayLength(); i < cnt; ++i) {
            idxPath.push_back(idxPathObj.item_uint32(i));
        }
        chartRoot = m_env->m_ctx->getChartRootByIdxPath(iSheet, idxPath);
    }
	if (!chartRoot) return false;

	WebID chartShapeID = param.field_web_id("objChartShape");
	binary_wo::VarObj idxPathObj = param.get("chartShapeIdxPath");

	if (chartRoot->objId() == chartShapeID) { // 是否是chart root
		if (idxPathObj.arrayLength() == 0) return false;
		
		param.add_field_array("chartShapeIdxPath", binary_wo::typeUint32);
		return true;
	}

	{ // 尝试已有idxPath
		ShapeIdxPath idxPath;
		idxPath.reserve(idxPathObj.arrayLength());
		for (int i = 0, cnt = idxPathObj.arrayLength(); i < cnt; ++i) {
			idxPath.push_back(idxPathObj.item_int32(i));
		}

		drawing::AbstractShape* shape = m_env->m_ctx->getChartShapeByIdxPath(chartRoot, idxPath);
		if (shape != nullptr && shape->objId() == chartShapeID) return false;
	}

	ShapeIdxPath idxPath;
	m_env->m_ctx->getChartShapeIdxPathById(chartRoot, chartShapeID, idxPath);
	binary_wo::VarObj arr = param.add_field_array("chartShapeIdxPath", binary_wo::typeUint32);
	for (size_t i = 0; i < idxPath.size(); ++i)
		arr.add_item_uint32(idxPath[i]);

	return true;
}

// ================== OpChartMoveSeries ==================
int OpChartMoveSeries::fromOrder() const
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if (param.has("moveFrom"))
		return param.field_uint32("moveFrom");
	return -1;
}

int OpChartMoveSeries::toOrder() const
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if (param.has("moveTo"))
		return param.field_uint32("moveTo");
	return -1;
}
bool OpChartMoveSeries::tfAddSv(int pos, int& fOrder, int& tOrder)
{
	bool changed = false;
	if (pos < tOrder)
	{
		tOrder++;
		changed = true;
	} 
	else if (pos == tOrder)
	{
		if (fOrder < tOrder)
		{
			tOrder++;
			changed = true;
		}
	}
	//from  如果刚好被删掉，无效
	if (pos < fOrder)
	{
		fOrder++;
		changed = true;
	}
	return changed;
}

bool OpChartMoveSeries::tfDelSv(int pos, int& fOrder, int& tOrder)
{
	bool changed = false; 
	if (pos < tOrder)
	{
		tOrder--;
		changed = true;
	} 
	else if (pos == tOrder)
	{
		if (fOrder < tOrder)
		{
			tOrder--;
			changed = true;
		}
	}
	//from  如果刚好被删掉，无效
	if (pos < fOrder)
	{
		fOrder--;
		changed = true;
	}
	return changed;
}
bool OpChartMoveSeries::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	// 非atom命令应该没有具体的moveTo用于调整
	switch (opSv->tp())
	{
	case ot_shape_Insert:
	case ot_shape_Delete:
	case ot_shape_Move:
	{
		return OpShapeObj::tfBySv(opSv, vecOpCl);
	}
		break;
	case ot_chart_Delete_series:
	{
		const OpChartShapeAtomDelete* opChartShapeSv = static_cast<const OpChartShapeAtomDelete*>(opSv);
		binary_wo::VarObj param = m_cmd->cast().get("param");

		if (opChartShapeSv->getShapeObjId() != param.field_web_id("objShape"))
			return false;
		
		int tOrder = toOrder();
		int fOrder = fromOrder();
		int idxSv = opChartShapeSv->moveToPos();//delete的位置
		bool changed = tfDelSv(idxSv, fOrder, tOrder);		
		if (changed)
		{
			binary_wo::VarObj param = m_cmd->cast().get("param");
			if (tOrder >= 1)
				param.add_field_uint32("moveTo", tOrder);
			if (fOrder >= 1)
				param.add_field_uint32("moveFrom", fOrder);
			vecOpCl.m_v.push_back(clone());
			return true;
		}
	}
		break;
	case ot_chart_Move_series:
	{
		const OpChartShapeAtomMove* opMoveSeries = static_cast<const OpChartShapeAtomMove*>(opSv);
		binary_wo::VarObj param = m_cmd->cast().get("param");
		if (opMoveSeries->getShapeObjId() != param.field_web_id("objShape"))
			return false;
		int tOrder = toOrder();
		int fOrder = fromOrder();
		

		int orderBind = opMoveSeries->moveToPos();
		size_t toOrderSv = ((orderBind & 0xFF00) >> 8) + 1;
		size_t fromOrderSV = (orderBind & 0x00FF) + 1;
		if (opMoveSeries->isReverse())
		{
			int tmp = toOrderSv;
			toOrderSv = fromOrderSV;
			fromOrderSV = tmp;
		}
		bool changed = false;

		if ((toOrderSv == tOrder && fromOrderSV == fOrder) || (fromOrderSV == tOrder && fOrder == tOrder))
		{	//client和server完全相等，只执行一次
			//client的to和from相等, toOrderSv > tOrder 和 toOrderSv < tOrder 和 toOrderSv == tOrder 合并得到 
			tOrder = toOrderSv;
			fOrder = toOrderSv;
			binary_wo::VarObj param = m_cmd->cast().get("param");
			if (tOrder >= 1)
			param.add_field_uint32("moveTo", tOrder);
			if (fOrder >= 1)
				param.add_field_uint32("moveFrom", fOrder);
			vecOpCl.m_v.push_back(clone());
			return true;
		}

		if (fromOrderSV<tOrder && toOrderSv>=tOrder)//先调出去了
		{
			tOrder--;
			changed = true;
		}
		else if (fromOrderSV>tOrder && toOrderSv<=tOrder)//先调进来了
		{
			tOrder++;
			changed = true;
		}
		else if (fromOrderSV == tOrder && toOrderSv > tOrder && fOrder < tOrder)//之前刚好把to调走，如果是往后，-1
		{
			tOrder--;
			changed = true;
		}
		else if (fromOrderSV == tOrder && toOrderSv < tOrder && fOrder > tOrder)//之前刚好把to调走，如果是往前，+1
		{
			tOrder++;
			changed = true;
		}

		/////////////from
		if (fromOrderSV > fOrder && toOrderSv <= fOrder)
		{
			fOrder++;
			changed = true;
		}
		else if (fromOrderSV < fOrder && toOrderSv >= fOrder)
		{
			fOrder--;
			changed = true;
		}
		else if (fromOrderSV == fOrder)//调到哪就是哪
		{
			fOrder = toOrderSv;
			changed = true;
		}

		if (changed)
		{
			binary_wo::VarObj param = m_cmd->cast().get("param");
			if (tOrder >= 1)
			param.add_field_uint32("moveTo", tOrder);
			if (fOrder >= 1)
				param.add_field_uint32("moveFrom", fOrder);
			vecOpCl.m_v.push_back(clone());
			return true;
		}
		return false;
	}
		break;
	case ot_chart_Add_series:
	{
		const OpChartShapeAtomAdd* opChartShapeSv = static_cast<const OpChartShapeAtomAdd*>(opSv);
		binary_wo::VarObj param = m_cmd->cast().get("param");

		if (opChartShapeSv->getShapeObjId() != param.field_web_id("objShape"))
			return false;
		
		int tOrder = toOrder();
		int fOrder = fromOrder();
		int idxSv = opChartShapeSv->moveToPos();//add的位置
		bool changed = tfAddSv(idxSv, fOrder, tOrder);;	
		
		if (changed)
		{
			binary_wo::VarObj param = m_cmd->cast().get("param");
			if (tOrder >= 1)
				param.add_field_uint32("moveTo", tOrder);
			if (fOrder >= 1)
				param.add_field_uint32("moveFrom", fOrder);
			vecOpCl.m_v.push_back(clone());
			return true;
		}
	}

		break;
	default:
		break;
	}
	return false;
}


// ================== Multi-Range Op Single ==================
OpMultiRangeSimple::OpMultiRangeSimple(OpEnv* env, const KwCommand* src, OpType tp, RectTrans::Param tsp, WebName name)
	: OpMultiRangeBase(env, src, tp, tsp)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	binary_wo::VarObj rgs = param.get_s(name);
	for (int i = 0; i < rgs.arrayLength_s(); i++)
	{
		binary_wo::VarObj rg = rgs.at_s(i);
		m_rcVec.push_back(VarReadRect(rg));
	}
}

OpMultiRangeSimple::OpMultiRangeSimple(OpEnv* env, const KwCommand* src, const std::vector<RECT>& rcVec, OpType tp, RectTrans::Param tsp)
	: OpMultiRangeBase(env, src, rcVec, tp, tsp)
{
}

KwCommand* OpMultiRangeSimple::detachCmd()
{
	ASSERT(m_cmd != NULL);
	binary_wo::VarObj param = m_cmd->cast().get("param");
	binary_wo::VarObj rgs = param.add_field_array("rgs", binary_wo::typeStruct);
	for (auto it = m_rcVec.begin(); it != m_rcVec.end(); ++it)
	{
		binary_wo::VarObj rg = rgs.add_item_struct();
		VarWriteRect(*it, rg);
	}

	return OpBase::detachCmd();
}

// ================== OpSetDataSourceFml ==================
OpSetDataSourceFml::OpSetDataSourceFml(OpEnv* env, const KwCommand* src, WebStr fml) 
: OpFormulaBase(env, src, ot_range_SetFormula, RectTrans::ts_move_expand_abridge)
, m_hasEqualSign(true)
{
	binary_wo::VarObj param = src->cast().get("param");
	setSheetIdx(param);
	m_rc = {0};
	ks_wstring wstrFml;
	if (*fml != __Xc('=')) {
		wstrFml.assign(__X("="));
		m_hasEqualSign = false;
	}
	wstrFml.append(fml);
	setFmla(wstrFml.c_str(), param.get_s("supBooksCtx"));
}

OpBase* OpSetDataSourceFml::clone(const RECT& rc)
{
	ASSERT(false); // 不可能走这里
	const ks_wstring & fml = dataSourceFml();
	OpSetDataSourceFml * pData = new OpSetDataSourceFml(m_env, m_cmd, fml.c_str());
	pData->m_rc = rc;
	return pData;
}

ks_wstring OpSetDataSourceFml::dataSourceFml()
{
	ks_bstr bstr;
	getFmla(&bstr);
	size_t len = bstr.size();
	if (len <= 0) return ks_wstring();
	if (m_hasEqualSign)
		return ks_wstring(bstr.c_str(), len);
	return ks_wstring(bstr.c_str() + 1, len - 1);
}

// ================== DataSourceFetcher ==================
DataSouceObjKeyFetcher::DataSouceObjKeyFetcher(WebName objName, WebName key)
: m_objName(objName), m_key(key)
{

}

bool DataSouceObjKeyFetcher::hasDataSource(const binary_wo::VarObj & param)
{
	if (!param.has(m_objName)) return false;
	const binary_wo::VarObj & obj = param.get(m_objName);
	return obj.has(m_key); 
}

LPCWSTR DataSouceObjKeyFetcher::dataSource(const binary_wo::VarObj & param)
{
	if (!param.has(m_objName)) return __X("");
	const binary_wo::VarObj & obj = param.get(m_objName);
	return obj.field_str(m_key); 
}
void DataSouceObjKeyFetcher::setDataSource(binary_wo::VarObj & param, LPCWSTR fml)
{
	if (!param.has(m_objName)) return;
	binary_wo::VarObj obj = param.get(m_objName);
	obj.add_field_str(m_key, fml);
}

// ================== OpSetDataSource ==================
OpSetDataSource::OpSetDataSource(OpEnv* env, const KwCommand* src) 
: OpChartShape(env, src, ot_chart_trans_source)
{
	initSourceFml(new DataSouceKeyFetcher("dataSources"));
}

OpSetDataSource::OpSetDataSource(OpEnv* env, const KwCommand* src, IDataSourceFetcher * fetcher)
: OpChartShape(env, src, ot_chart_trans_source)
{
	initSourceFml(fetcher);
}

void OpSetDataSource::initSourceFml(IDataSourceFetcher * fetcher)
{
	if (fetcher == nullptr) return;
	
	m_spFetcher.reset(fetcher);
	const binary_wo::VarObj & param = m_cmd->cast().get("param");
	if (m_spFetcher->hasDataSource(param)) {
		m_spSourceFml.reset(new OpSetDataSourceFml(m_env, m_cmd, m_spFetcher->dataSource(param)));
	}
}

KwCommand* OpSetDataSource::detachCmd()
{
	KwCommand * cmd = OpChartShape::detachCmd();
	if (m_spSourceFml && m_spSourceFml->isChangedByTfFml()) {
		binary_wo::VarObj param = cmd->cast().get("param");
		const ks_wstring & fml = m_spSourceFml->dataSourceFml();
		m_spFetcher->setDataSource(param, fml.c_str());
	}
	return cmd;
}

bool OpSetDataSource::updateSheetIdx()
{
	if (m_spSourceFml) {
		return m_spSourceFml->updateSheetIdx() | OpChartShape::updateSheetIdx();
	}
	return OpChartShape::updateSheetIdx();
}

// ================== OpDataLabelOption ==================
OpDataLabelOption::OpDataLabelOption(OpEnv* env, const KwCommand* src)
: OpSetDataSource(env, src, new DataSouceObjKeyFetcher("dataLabelOption", "rangefml"))
{
}

// ================== OpSetCategoryDataSource ==================
OpSetCategoryDataSource::OpSetCategoryDataSource(OpEnv* env, const KwCommand* src)
: OpSetDataSource(env, src)
{
}

// ================== OpSetSeriesDataSourceFml ==================
OpSetSeriesDataSourceFml::OpSetSeriesDataSourceFml(OpEnv* env, const KwCommand* src) : OpMultiFormulasBase(env, src, NULL)
{
	m_pack = this;
	binary_wo::VarObj varParam = m_cmd->cast().get("param");
	m_idxSht = m_env->m_ctx->getSheetIndex(varParam.field_web_id("objSheet"));
	m_tokVecs = m_pack->unwrapTokenVecs(varParam);
}

std::vector<exec_token_vector> OpSetSeriesDataSourceFml::unwrapTokenVecs(binary_wo::VarObj& param)
{
	std::vector<exec_token_vector> res;
	if (param.has("seriesSource"))
	{	
		binary_wo::VarObj source = param.get("seriesSource");
		const char* keys[] = {"name", "value", "category", "bubbleSize"};
		int keyLength = sizeof(keys) / sizeof(keys[0]);
		for (size_t i = 0; i < keyLength; i++)
		{
			const char* key = keys[i];
			if (source.has(key))
			{
				exec_token_vector tokVecName;
				ks_wstring wDataSources(source.field_str(key));
				getFmlaToken(wDataSources.c_str(), param.get_s("supBooksCtx"), tokVecName);
				res.push_back(tokVecName);
			}
		}
	}
	return res;
}

void OpSetSeriesDataSourceFml::wrapTokenVecs(std::vector<exec_token_vector>& tokenVecs, binary_wo::VarObj& param)
{
	if (param.has("seriesSource")) 
	{
		binary_wo::VarObj source = param.get("seriesSource");
		const char* keys[] = {"name", "value", "category", "bubbleSize"};
		int keyLength = sizeof(keys) / sizeof(keys[0]);
		int index = 0;
		for (size_t i = 0; i < keyLength; i++)
		{
			const char* key = keys[i];
			if (source.has(key))
			{
				ks_bstr str;
				getFmla(tokenVecs[index++], &str);
				if (!str.empty())
					source.add_field_str(key, str.c_str());
			}
		}
	}
}


// ================== OpSetSeriesDataSource ==================
OpSetSeriesDataSource::OpSetSeriesDataSource(OpEnv* env, const KwCommand* src) 
: OpChartShape(env, src, ot_chart_trans_series_source)
{
	m_spSourceFml.reset(new OpSetSeriesDataSourceFml(env, src));
}

bool OpSetSeriesDataSource::updateSheetIdx()
{
	if (m_spSourceFml)
		return m_spSourceFml->updateSheetIdx() | OpChartShape::updateSheetIdx();
	return OpChartShape::updateSheetIdx();
}

KwCommand* OpSetSeriesDataSource::detachCmd()
{
	KwCommand * cmd = OpChartShape::detachCmd();
	if (m_spSourceFml && m_spSourceFml->isChangedByTfFml()) {
		binary_wo::VarObj param = cmd->cast().get("param");
		m_spSourceFml->wrapTokenVecs(m_spSourceFml->m_tokVecs, param);
	}
	return cmd;
}



// ================== OpMultiRangeBase ==================
OpMultiRangeBase::OpMultiRangeBase(OpEnv* env, const KwCommand* src, OpType tp, RectTrans::Param tsp)
	: OpBaseHasOneObjSheet(env, src, tp), m_tsp(tsp)
{}

OpMultiRangeBase::OpMultiRangeBase(OpEnv* env, const KwCommand* src, const std::vector<RECT>& rcVec, OpType tp, RectTrans::Param tsp)
	: OpBaseHasOneObjSheet(env, src, tp)
	, m_tsp(tsp)
	, m_rcVec(rcVec)
{
}

bool OpMultiRangeBase::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	std::vector<RECT> vecRes;
	bool bChanged = false;
	for (auto it = m_rcVec.begin(); it != m_rcVec.end(); ++it)
		if (tfSingleRcBySv(opSv, *it, vecRes))
			bChanged = true;
	vecOpCl.m_v.push_back(clone(vecRes));
	return bChanged;
}

bool OpMultiRangeBase::tfSingleRcBySv(const OpBase* opSv, const RECT &rc, std::vector<RECT> &vec)
{
	switch (opSv->tp())
	{
		case ot_range_Insert:
		{
			std::vector<RECT> vecRes;
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			RectTrans rectTrans(m_tsp, m_env->m_bmp);
			if (pSv->m_dir == dir_hori)
				rectTrans.InsertRight(pSv->m_rc, rc, vecRes);
			else
				rectTrans.InsertDown(pSv->m_rc, rc, vecRes);

			vec.insert(vec.end(), vecRes.begin(), vecRes.end());
			return isRectTransformed(vecRes, rc);
		}
		case ot_range_Delete:
		{
			std::vector<RECT> vecRes;
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			RectTrans rectTrans(m_tsp, m_env->m_bmp);
			if (pSv->m_dir == dir_hori)
				rectTrans.DeleteLeft(pSv->m_rc, rc, vecRes);
			else
				rectTrans.DeleteUp(pSv->m_rc, rc, vecRes);

			vec.insert(vec.end(), vecRes.begin(), vecRes.end());
			return isRectTransformed(vecRes, rc);
		}
		case ot_range_CutInsertVert:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none, m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, rc, vecRes);

			vec.insert(vec.end(), vecRes.begin(), vecRes.end());
			return isRectTransformed(vecRes, rc);
		}
		case ot_range_CutInsertHorz:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none, m_env->m_bmp);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, rc, vecRes);

			vec.insert(vec.end(), vecRes.begin(), vecRes.end());
			return isRectTransformed(vecRes, rc);
		}
		default:
			return false;
	}
}

bool OpMultiRangeBase::isRectTransformed(const std::vector<RECT>& vec, const RECT &rc)
{
	if (vec.empty() || vec.size() == 1 && Rect_Equal(vec[0], rc))
		return false;

	return true;
}

// ================== opSetPageSetting ==================
OpSetPageSetting::OpSetPageSetting(OpEnv* env, const KwCommand* src)
	: OpMultiRangeBase(env, src, ot_range, RectTrans::ts_move_expand_abridge)
{
	initRcVecfromSrcCmd(src);
}

void OpSetPageSetting::initRcVecfromSrcCmd(const KwCommand* src)
{
	// 判断命令是否为区域设置命令，写入
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	binary_wo::VarObj rgs;
	if(param.has("printArea"))
		rgs = param.get_s("printArea");
	else if(param.has("printTitleCols"))
		rgs = param.get_s("printTitleCols");
	else if(param.has("printTitleRows"))
		rgs = param.get_s("printTitleRows");
	else
		m_rcVec.clear();

	for (int i = 0; i < rgs.arrayLength_s(); i++)
	{
		binary_wo::VarObj rg = rgs.at_s(i);
		m_rcVec.push_back(VarReadRect(rg));
	}
}

void OpSetPageSetting::writeBackNewCmd()
{
	ASSERT(m_cmd != NULL);
	binary_wo::VarObj param = m_cmd->cast().get("param");
	binary_wo::VarObj rgs;
	if(param.has("printArea"))
		rgs = param.add_field_array("printArea", binary_wo::typeStruct);
	else if(param.has("printTitleCols"))
		rgs = param.add_field_array("printTitleCols", binary_wo::typeStruct);
	else if(param.has("printTitleRows"))
		rgs = param.add_field_array("printTitleRows", binary_wo::typeStruct);
	else
		;

	for (auto it = m_rcVec.begin(); it != m_rcVec.end(); ++it)
	{
		binary_wo::VarObj rg = rgs.add_item_struct();
		VarWriteRect(*it, rg);
	}
}

KwCommand* OpSetPageSetting::detachCmd()
{
	writeBackNewCmd();
	return OpBase::detachCmd();
}

// ================== OpDbTableColumn ==================
OpDbTableColumn::OpDbTableColumn(OpEnv* env, const KwCommand* src)
	: OpBaseHasOneObjSheet(env, src, ot_range)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	IDX sheetIdx = param.field_int32("sheetIdx");
	IDX colIndex = param.field_int32("colIndex");
	RANGE colRg(env->m_bmp);
	colRg.SetCols(sheetIdx, sheetIdx, colIndex, colIndex);
	m_rc = Range2Rect(colRg);
}

OpDbTableColumn::OpDbTableColumn(OpEnv* env, const KwCommand* src, const RECT& rc)
	: OpBaseHasOneObjSheet(env, src, ot_range)
	, m_rc(rc)
{}

KwCommand* OpDbTableColumn::detachCmd()
{
	ASSERT(m_cmd != NULL);
	binary_wo::VarObj param = m_cmd->cast().get("param");
	IDX sheetIdx = param.field_int32("sheetIdx");

	IDX colIndex = INVALIDIDX;
	if (Rect_IsValid(m_rc))
	{
		ks_stdptr<ICoreListObject> spListObject;
		HRESULT hr = Database::Utils::GetDbTable(m_env->m_bk, sheetIdx, &spListObject);
		if (SUCCEEDED(hr))
		{
			RANGE tableRg(m_env->m_bmp);
			spListObject->GetRange(&tableRg);
			if (m_rc.left >= tableRg.ColFrom() && m_rc.left <= tableRg.ColTo())
				colIndex = m_rc.left;
		}
	}
	param.add_field_int32("colIndex", colIndex);

	return OpBase::detachCmd();
}

bool OpDbTableColumn::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	constexpr RectTrans::Param tsp = RectTrans::Param::ts_move_abridge;
	switch (opSv->tp())
	{
	case ot_range_Insert:
		{
			std::vector<RECT> vecRes;
			const OpRangeInsert* pSv = static_cast<const OpRangeInsert*>(opSv);

			RectTrans rectTrans(tsp , m_env->m_bmp);
			if (pSv->m_dir == dir_hori)
				rectTrans.InsertRight(pSv->m_rc, m_rc, vecRes);
			else
				rectTrans.InsertDown(pSv->m_rc, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_range_Delete:
		{
			std::vector<RECT> vecRes;
			const OpRangeDelete* pSv = static_cast<const OpRangeDelete*>(opSv);
			RectTrans rectTrans(tsp , m_env->m_bmp);
			if (pSv->m_dir == dir_hori)
				rectTrans.DeleteLeft(pSv->m_rc, m_rc, vecRes);
			else
				rectTrans.DeleteUp(pSv->m_rc, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_range_CutInsertVert:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none, m_env->m_bmp);
			rectTrans.SwapVert(pSv->m_rcTop, pSv->m_rcBottom, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	case ot_range_CutInsertHorz:
		{
			std::vector<RECT> vecRes;
			const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
			RectTrans rectTrans(RectTrans::ts_none, m_env->m_bmp);
			rectTrans.SwapHorz(pSv->m_rcLeft, pSv->m_rcRight, m_rc, vecRes);

			return MakeRes(vecRes, vecOpCl);
		}
	default:
		return false;
	}
}

bool OpDbTableColumn::MakeRes(std::vector<RECT>& vec, VecOp& vecOp)
{
	if (vec.empty() || vec.size() != 1 || Rect_Equal(vec[0], m_rc))
		return false;

	vecOp.m_v.push_back(new OpDbTableColumn(m_env, m_cmd, vec[0]));
	return true;
}

OpMultiFormulasBase::OpMultiFormulasBase(OpEnv* env, const KwCommand* src, IOpMultiFormulasPack* pack)
	: OpBase(env, src, ot_trans_formulas), m_pack(pack), m_isChangedByTfFml(false)
{
}

bool OpMultiFormulasBase::tfFormularsBySv(IDX iSheet, const OpBase* opSv)
{
	if (m_cmd == nullptr)
	{
		return false;
	}

	if (m_tokVecs.empty())
	{
		binary_wo::VarObj varParam = m_cmd->cast().get("param");
		m_tokVecs = m_pack->unwrapTokenVecs(varParam);
	}

	if (m_tokVecs.empty())
	{
		return false;
	}

	REGION_OPERATION_PARAM param(m_env->m_bmp);
	switch (opSv->tp())
	{
	case ot_range_Delete:
	{
		const OpRangeDelete* pOp = static_cast<const OpRangeDelete*>(opSv);
		param.code = (RegionOperationCode)(param.code | rop_Remove);
		makeCode(param.code, pOp->m_rc, pOp->m_dir, m_env->m_bmp);
		param.source = Rect2Range(pOp->m_rc, iSheet, iSheet, m_env->m_bmp);
	}
	break;
	case ot_range_Insert:
	{
		const OpRangeInsert* pOp = static_cast<const OpRangeInsert*>(opSv);
		param.code = (RegionOperationCode)(param.code | rop_Insert);
		makeCode(param.code, pOp->m_rc, pOp->m_dir, m_env->m_bmp);
		param.source = Rect2Range(pOp->m_rc, iSheet, iSheet, m_env->m_bmp);
	}
	break;
	case ot_range_CutInsertVert:
	{
		const OpRangeCutInsertVert* pSv = static_cast<const OpRangeCutInsertVert*>(opSv);
		RECT rcTop = pSv->m_rcTop, rcBottom = pSv->m_rcBottom;
		RANGE rgSrc = Rect2Range(rcTop, iSheet, m_env->m_bmp);
		INT offRow = rcBottom.bottom - rcBottom.top + 1;
		MakeCutInsertRange(param, rgSrc, offRow, 0, dirDown);
	}
	break;
	case ot_range_CutInsertHorz:
	{
		const OpRangeCutInsertHorz* pSv = static_cast<const OpRangeCutInsertHorz*>(opSv);
		RECT rcLeft = pSv->m_rcLeft, rcRight = pSv->m_rcRight;
		RANGE rgSrc = Rect2Range(rcLeft, iSheet, m_env->m_bmp);
		INT offCol = rcRight.right - rcRight.left + 1;
		MakeCutInsertRange(param, rgSrc, 0, offCol, dirRight);
	}
	break;
	default:
		return false;
	}

	for (auto it = m_tokVecs.begin(); it != m_tokVecs.end(); ++it)
	{
		exec_token_vector tmp;
		m_env->m_bk->GetWoStake()->adjFmla(iSheet, *it, &tmp, param);
		if (tmp)
			m_isChangedByTfFml = true;
	}
	return m_isChangedByTfFml;
}

KwCommand* OpMultiFormulasBase::detachCmd()
{
	if (m_cmd == NULL)
	{
		return NULL;
	}

	binary_wo::VarObj param = m_cmd->cast().get("param");
	m_pack->wrapTokenVecs(m_tokVecs, param);
	return OpBase::detachCmd();
}

void OpMultiFormulasBase::getFmlaToken(PCWSTR fmla, const binary_wo::VarObj& booksCtx, exec_token_vector& outTokVec)
{
	m_env->m_ctx->getSupBookCtx()->reset(booksCtx);

	ks_stdptr<IFormula> spFmla;
	m_env->m_bookOp->CreateFormula(&spFmla);

	CS_COMPILE_PARAM ccp = m_pack->getCCP();
	COMPILE_RESULT cr;
	spFmla->SetFormula(fmla, ccp, &cr);

	BOOL bFmla = FALSE;
	const_token_ptr pVal = NULL;
	spFmla->GetContent(&bFmla, &outTokVec, &pVal);
}

void OpMultiFormulasBase::getFmla(exec_token_vector& tokVec, BSTR* bstrFmla)
{
	if (tokVec)
	{
		ks_stdptr<IFormula> spFmla;
		m_env->m_bookOp->CreateFormula(&spFmla);
		spFmla->SetFormulaContent(tokVec);
		CS_COMPILE_PARAM param = m_pack->getCCP();
		spFmla->GetFormula(bstrFmla, param);
	}
}

OpCreatePivotTable::OpCreatePivotTable(OpEnv* env, const KwCommand* src) : OpMultiFormulasBase(env, src, this), m_rangeOp(env, src)
{
}

bool OpCreatePivotTable::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	bool res = false;
	if (param.has("destObjSheet") && param.has("destSheetIndex"))
	{
		res = updateSheetIdxHelper(param, "destObjSheet", "destSheetIndex");
	}

	return m_rangeOp.updateSheetIdx() || res;
}

std::vector<exec_token_vector> OpCreatePivotTable::unwrapTokenVecs(binary_wo::VarObj& param)
{
	std::vector<exec_token_vector> res;

	if (param.has("srcRangeText"))
	{
		exec_token_vector tokVecSrc;
		getFmlaToken(param.field_str("srcRangeText"), param.get_s("supBooksCtx"), tokVecSrc);
		if (!tokVecSrc)
		{
			return res;
		}

		res.push_back(tokVecSrc);
	}

	exec_token_vector tokVecDest;
	getFmlaToken(param.field_str("destRangeText"), param.get_s("supBooksCtx"), tokVecDest);
	if (tokVecDest)
	{
		res.push_back(tokVecDest);
	}

	return res;
}

void OpCreatePivotTable::wrapTokenVecs(std::vector<exec_token_vector>& tokenVecs, binary_wo::VarObj& param)
{
	if (tokenVecs.size() == 0)
	{
		return;
	}

	if (tokenVecs.size() == 2 && param.has("srcRangeText"))
	{
		ks_bstr strSrc;
		getFmla(tokenVecs[0], &strSrc);
		if (strSrc)
		{
			param.add_field_str("srcRangeText", strSrc.c_str());
		}
	}

	ks_bstr strDest;
	getFmla(tokenVecs[1], &strDest);
	if (strDest)
	{
		param.add_field_str("destRangeText", strDest.c_str());
	}
}

KwCommand* OpCreatePivotTable::detachCmd()
{
	if (m_cmd == NULL)
	{
		return NULL;
	}

	binary_wo::VarObj param = m_cmd->cast().get("param");
	wrapTokenVecs(m_tokVecs, param);
	VarWriteRect(m_rangeOp.m_rc, param);
	return OpBase::detachCmd();
}

OpPivotTableSetSourceData::OpPivotTableSetSourceData(OpEnv* env, const KwCommand* src) : OpMultiFormulasBase(env, src, this), m_rangeOp(env, src)
{
	binary_wo::VarObj varParam = m_cmd->cast().get("param");
	m_tokVecs = m_pack->unwrapTokenVecs(varParam);
}

std::vector<exec_token_vector> OpPivotTableSetSourceData::unwrapTokenVecs(binary_wo::VarObj& param)
{
	std::vector<exec_token_vector> res;

	exec_token_vector tokVecSrc;
	getFmlaToken(param.field_str("sourceRangeText"), param.get_s("supBooksCtx"), tokVecSrc);
	if (tokVecSrc)
	{
		res.push_back(tokVecSrc);
	}

	return res;
}

void OpPivotTableSetSourceData::wrapTokenVecs(std::vector<exec_token_vector>& tokenVecs, binary_wo::VarObj& param)
{
	if (tokenVecs.size() != 1)
	{
		return;
	}

	ks_bstr strSrc;
	getFmla(tokenVecs[0], &strSrc);
	if (strSrc)
	{
		param.add_field_str("sourceRangeText", strSrc);
	}
}

KwCommand* OpPivotTableSetSourceData::detachCmd()
{
	if (m_cmd == NULL)
	{
		return NULL;
	}

	binary_wo::VarObj param = m_cmd->cast().get("param");
	wrapTokenVecs(m_tokVecs, param);
	VarWriteRect(m_rangeOp.m_rc, param);
	return OpBase::detachCmd();
}

OpPivotTableRangeFieldName::OpPivotTableRangeFieldName(OpEnv* env, const KwCommand* src)
	: OpRangeBase(env, src, ot_pivot_table_field_name, RectTrans::ts_move_abridge_split)
{
	ASSERT(src != NULL);
	binary_wo::VarObj param = src->cast().get("param");
	m_fieldName = param.field_str("fieldName");
	m_objPivotTable = param.field_web_id("objPivotTable");

	if (param.has("newFieldName"))
	{
		m_newFieldName = param.field_str("newFieldName");
	}
}

OpPivotTableRangeFieldName::OpPivotTableRangeFieldName(OpEnv* env, WebID objPivotTable, WebStr fieldName, WebStr newFieldName)
	: OpRangeBase(env, nullptr, RECT(), ot_pivot_table_field_name, RectTrans::ts_move_abridge_split)
{
	m_objPivotTable = objPivotTable;
	m_fieldName = fieldName;
	m_newFieldName = newFieldName;
}

OpPivotTableRangeFieldName::OpPivotTableRangeFieldName(OpEnv* env, const KwCommand* src, const RECT& rc, WebID objPivotTable, WebStr fieldName, WebStr newFieldName)
	: OpRangeBase(env, src, rc, ot_pivot_table_field_name, RectTrans::ts_move_abridge_split)
{
	m_objPivotTable = objPivotTable;
	m_fieldName = fieldName;
	m_newFieldName = newFieldName;
}

bool OpPivotTableRangeFieldName::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
	bool res = OpRangeBase::tfBySv(opSv, vecOpCl);
	switch (opSv->tp())
	{
	case ot_pivot_table_field_name:
	{
		const OpPivotTableRangeFieldName* pSv = static_cast<const OpPivotTableRangeFieldName*>(opSv);
		if (m_objPivotTable == pSv->m_objPivotTable && getFieldName() == pSv->getFieldName())
		{
			m_fieldName = pSv->getNewFieldName();
			return true;
		}
	}
	}

	return res;
}

KwCommand* OpPivotTableRangeFieldName::detachCmd()
{
	if (m_cmd == NULL)
		return NULL;

	binary_wo::VarObj param = m_cmd->cast().get("param");
	VarWriteRect(m_rc, param);

	if (!m_fieldName.empty())
	{
		param.add_field_str("fieldName", m_fieldName.c_str());
	}

	return OpBase::detachCmd();
}


///////////////////////////////////////////////////
bool OpsetSubscriptionOption::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if(param.has("bMergeTaskSubscribe"))
	{
		return updateSheetIdxHelper(param , "objSheet", "sheetIdx");
	}
	return false;
}

bool OpMultiSheetsOp::updateSheetIdx()
{
	binary_wo::VarObj param = m_cmd->cast().get("param");
	if(param.has("sheets"))
	{
		binary_wo::VarObj idsObj = param.get_s("sheets");
        if (idsObj.type() == binary_wo::typeArray) 
        {
            bool update = false;
            INT32 sheetCount = idsObj.arrayLength_s();
            for (IDX i = 0; i < sheetCount; ++i) 
            {
                binary_wo::VarObj sheetObj = idsObj.at(i);
                update |= updateSheetIdxHelper(sheetObj , "objSheet", "sheetIdx");
            }
            return update;
	    } 
	}
	return false;
}

///////////////////////////////////////////////////
OpRangeInsertNumberCol::OpRangeInsertNumberCol(OpEnv* env, const KwCommand* src)
        : OpRangeInsert(env, src)
{
    ASSERT(src != nullptr);
    binary_wo::VarObj param = src->cast().get("param");
    m_rcCell = m_rc;
    m_rcCell.top = param.field_int32("headerRow");
    m_rcCell.bottom = m_rcCell.top;
    m_pCellOp.reset(new OpRangeExpandAbridge(env, src, m_rcCell));
}

OpRangeInsertNumberCol::OpRangeInsertNumberCol(OpEnv* env, const KwCommand* src, const RECT& rc, OpDir dir, const RECT& rcCell)
        : OpRangeInsert(env, src, rc, dir), m_rcCell(rcCell)
{

}

KwCommand* OpRangeInsertNumberCol::detachCmd()
{
    if (!m_cmd)
        return nullptr;

    binary_wo::VarObj param = m_cmd->cast().get("param");
    param.add_field_int32("headerRow", m_rcCell.top);
    return OpRangeInsert::detachCmd();
}

bool OpRangeInsertNumberCol::tfBySv(const OpBase* opSv, VecOp& vecOpCl)
{
    VecOp vecOpCol;
    VecOp vecOpCell;
    bool colChanged = OpRangeInsert::tfBySv(opSv, vecOpCol);
    bool cellChanged = m_pCellOp->tfBySv(opSv, vecOpCell);
    if (colChanged || cellChanged)
        return MakeRes(vecOpCol, vecOpCell, vecOpCl);
    return false;
}

bool OpRangeInsertNumberCol::MakeRes(VecOp& vecOpCol, VecOp& vecOpCell, VecOp& vecOp)
{
    RECT rc = m_rc;
    if (!vecOpCol.m_v.empty())
    {
        // OpRangeInsert不会拆分区域，所以这里肯定只有一个元素
        ASSERT(vecOpCol.m_v.size() == 1);
        auto* pOpRangeInsert = static_cast<OpRangeInsert*>(vecOpCol.m_v[0]);
        rc = pOpRangeInsert->m_rc;
    }
    RECT cell = m_rcCell;
    if (!vecOpCell.m_v.empty())
    {
        // OpRangeExpandAbridge不会拆分区域，所以这里肯定只有一个元素
        ASSERT(vecOpCell.m_v.size() == 1);
        auto* pOpRangeExpandAbridge = static_cast<OpRangeExpandAbridge*>(vecOpCell.m_v[0]);
        cell = pOpRangeExpandAbridge->m_rc;
    }
    bool colChanged = !Rect_Equal(rc, m_rc);
    bool cellChanged = !Rect_Equal(cell, m_rcCell);
    if (colChanged || cellChanged)
    {
        auto* pOpRangeInsertNumberCol = new OpRangeInsertNumberCol(m_env, m_cmd, rc, m_dir, cell);
        // 列号改变时才需要新增ot_range_Insert类型的Op
        if (!colChanged)
            pOpRangeInsertNumberCol->m_tp = ot_range;
        vecOp.m_v.push_back(pOpRangeInsertNumberCol);
        return true;
    }
    return false;
}

}//namespace wo
