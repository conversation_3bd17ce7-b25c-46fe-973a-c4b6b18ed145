//********************************************************************
//    KARRANGEPROCESS.H    文件注释
//    文件名		:    KARRANGEPROCESS.H
//    作者		:    Chenyongquan2
//    创建时间	:    2021/1/13
//    文件描述	:    et组件表格整理功能文件
//*********************************************************************

#ifndef __KAIET_ARRANGE_KARRANGEPROCESS_H__
#define __KAIET_ARRANGE_KARRANGEPROCESS_H__

#include "../proof/recognizedata.h"
#include "kxarrangeprocessdata.h"
#include "et_revision_context_impl.h"

namespace etai
{
class KArrangeProcess
{
public:
    KArrangeProcess() = delete;
    KArrangeProcess(const TableRangeInfo &tbinfo, TableApplyParam *pTblApplyInfo, TableRangeStyle *pTableRangeStyle, wo::KEtRevisionContext* ctx);
    ~KArrangeProcess();

    HRESULT process();
    void resetParam(const TableRangeInfo &tbinfo, TableApplyParam *pTblApplyInfo, TableRangeStyle *pTableRangeStyle);
    HRESULT resetColorOrStyle(TableRangeStyle *pTableRangeStyle);
    BOOL coreObjectIsDestroyed();
    IKWorksheet *getWorkSheet()
    {
        return m_spWorksheet;
    }
    IKWorkbook *getWorkBook()
    {
        return m_spWorkbook;
    }
    void initViewSize(int width, int height, double dpi);
    void setFontName(PCWSTR fontName)
    {
        m_fontName = krt::fromUtf16(fontName);
    }
private:
    HRESULT init();
    //获取表格识别数据
    HRESULT initRecData();
    //获取表格每一行的单元格以及合并单元格的信息
    HRESULT initAtomicTable();

    void printfColWidthTestInfo();
    
    //前置处理
    HRESULT doPreProcess();
    //中置处理
    HRESULT doMidProcess();
    //后置处理
    HRESULT doPostProcess();
    //算法落地应用
    HRESULT applyProcessResult();

    //获取指定区域的Range
    HRESULT getSpecifiedRange(OUT Range **ppRange, IN int rowBegin, IN int rowEnd, IN int colBegin, IN int colEnd);

    //标准化调整
    HRESULT standardProcess();
    //结构标准化
    HRESULT structStandardProcess();

    //结构化调整
    HRESULT structProcess();

    //根据每列的字符数估算要设置的列宽
    HRESULT processRowHAndColW();

    //适应屏幕调整
    void doAdaptProcess();

    //获取当前的实际列宽
    double getColRealWidth(int col);
    //获取当前的实际行高
    double getRowRealHeight(int row);

    //调整列宽(单位:磅)
    void adjustColumnsWidth();
    //调整每一列的列宽
    void processEachAtomicCol(AtomicCol *pEachAtomicCol);

    //调整行高
    HRESULT adjustRowsHeight();
    //调整每一行的行高
    void processEachAtomicRow(AtomicRow *pEachAtomicRow);

    //调整行高留白
    HRESULT adjustRowHeightWithSpace();

    //获取行高上下间距
    double getRowSpacingWithChar();
    //获取列宽的最大字符数
    double getColMaxWidthWithChar();

    //计算具体单元格里面内容所应该占的宽度(单位:磅)
    void getCellInfo(IN int row, IN int col, IN ZoneType zoneType, OUT int &cellWidth, OUT int &cellCharCnt,
                             OUT bool &bLineBreak, OUT std::vector<int>&);

    //根据文字内容返回文本宽度
    int getTextWidth(ks_wstring text, ZoneType zoneType);
    //获取单元格的预设宽度
    double getCellWidth(AtomicRange *pEachAtomicRange);

    //获取单元格的估算的高度(与内核的排版会有误差) bAllowOverflow表示是否允许内容溢出
    double estimatedCellHeight(const QVector<int> &eachParaTextWidthVec, double dCurWidth, double eachLineHeight,
                               bool bTextOverflow = false);
    double getCustomCellEstimatedHeight(AtomicRange *pEachAtomicRange, double eachLineHeight,
                                        const QVector<int> &eachParaTextWidthVec, bool bTextOverflow);

    QString getFontName();

    //合并区域整理
    HRESULT mergeAreaProcess();
    HRESULT mergeRange(Range *pRange, IETStringTools *pTools);

    //判断一个cell是否存在用户手动折行的情况
    bool bContainManualLineBreak(ROW cellRow, COL cellCol);
    //获取某一个单元格的内容
    HRESULT getCellText(IN ROW cellRow, IN COL cellCol, OUT ks_wstring &text);

    //获取指定的列
    HRESULT getSpecifiedCol(IN COL colIdx, OUT Range **ppRange);
    //获取一个特定的单元格
    HRESULT getSpecifiedCell(IN ROW cellRow, IN COL cellCol, OUT Range **ppRange);

    //初始化字号设置
    void initFontSizeInfo();
    single getCellFontSize(int row, int col);

    //初始化et相关信息参数
    void getRangeData();

    /////////处理合并单元格相关方法
    void processMergeCells();
    void processEachMergeCell(AtomicCells *pEachAtomicCells);

    double getMergeCellHeight(AtomicRange *pEachAtomicRange);
    void checkAndExpandMergeCellHeight(AtomicCells *pEachAtomicCells, bool bEnableAlignLeft);
    double getVerMergeCellWidth(IN const double dWidth, IN const int iRow);
    double getHorAndVerMergeCellWidth(double dWidth, int iRow, int iCol);
    void expandMergeCellWidth(AtomicRange *pEachAtomicRange, double dMoreColWidth);
    void expandMergeCellHeight(AtomicRange *pEachAtomicRange, double dMoreRowHeight);
    bool isMergeCell(int row, int col);
    bool isHorMergeCell(int row, int col);
    //////////////
    //处理内嵌图片的相关方法
    void processCellImgFmla();
    double getImgCellColMinWidth();
    double getImgCellRowMinHeight();

    //收集所有列的列宽以及水平对齐方式
    void presetColWidth(int iCol, double dWidth);
    double getPresetColWidth(int iCol);
    HRESULT syncColPresetWidth2RealWidth();
    //收集所有行的行高
    void presetRowHeight(int iRow, double dHeight);
    double getPresetRowHeight(int iRow);
    HRESULT syncRowPresetHeight2RealHeight();
    void restoreColHidden(int iCol);

    //应用相关属性落地
    HRESULT applyAtomicTableProp();

    HRESULT applyAtomicTableFontProp();
    HRESULT applyRangeFontProp(const std::vector<Range*>& vecRange, const std::vector<ES_CUBE>& vecCube, double fontSize, bool bBold);
    HRESULT applyAtomicTableAlignProp();

    void applyAtomicTableStyle();
    void applyFillAlterPlan();
    void applyFillTblHeadArea();
    void applyFillFirstColArea();
    void applyFillOtherArea();

    void applyRangeStyle(Range *pRange, TableZoneStyle *pStyle);
    void applyRangeBorderStyle(etoldapi::Borders *pBorders, ETBorderIndex borderIdx, TableZoneStyle *pStyle);
    void doApplyRangeBorderStyle(Border *pBorder, TableZoneStyle *pStyle, borderStyleInfo *pBorderStyle);
    UINT getTransNestLevel();

    QJsonObject templateRangeJsonObject(int rowFrom, int rowTo, int colFrom, int colTo);
    QJsonObject templateSetXfJsonObject(int rowFrom, int rowTo, int colFrom, int colTo);

private:
    ks_stdptr<IKWorkbook> m_spWorkbook;
    ks_stdptr<IKWorksheet> m_spWorksheet;
    ks_stdptr<Range> m_spAllRangeCell;

    std::vector<Range *> m_vecTitleRangeCell;
    std::vector<Range *> m_vecHeadRangeCell;
    std::vector<Range *> m_vecContentRangeCell;
    std::vector<Range *> m_vecSubTitleRangeCell;
    std::vector<Range *> m_vecOtherRangeCell;
    std::vector<Range *> m_vecInfoRangeCell;
    TableRangeInfo m_tableInfo;

    int m_headRangeFontSize;
    int m_titleRangeFontSize;
    int m_contentRangeFontSize;
    int m_subTitleRangeFontSize;
    int m_otherRangeFontSize;
    int m_infoRangeFontSize;
    ViewSize m_viewSize;

    std::unique_ptr<AtomicTable> m_spAtomicTable;
    std::unique_ptr<adaptScreenProcessProxy> m_spAdaptScreenProxy;

    TableRangeStyle *m_pTableRangeStyle;
    TableApplyParam *m_pTableApplyParam;
    wo::KEtRevisionContext *m_pCtx;
    QString m_fontName;
	int m_originTransNestLevel;
};
} // namespace etai
#endif //__KAIET_ARRANGE_KARRANGEPROCESS_H__
