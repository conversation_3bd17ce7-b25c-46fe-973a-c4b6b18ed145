#ifndef __WEBET_DATABASE_SHEET_ET2DB_EXPORTER_H__
#define __WEBET_DATABASE_SHEET_ET2DB_EXPORTER_H__

#include "appcore/et_appcore_dbsheet.h"
#include "database/database_field_context.h"
#include "etcore/et_core_basic.h"
#include "wo/et_shared_str.h"
#include "database/database_def.h"
#include "importer/sheets_importer.h"
#include "util.h"

namespace wo
{

struct EtExportError
{
    enum State {
        Empty =                 0x00,

        HasInvalidSheet =       0x01,
        ReachSheetCntLimit =    0x02,
        ReachRowCntLimit =      0x04,
        HasProtectedTable =     0x08,

        ThrowException =        0x10,

        FailedMsgMask =         0x1F,

        BanCreateBlankSheet =    0x20,
        
        HasSucceedSheets =      0x80,
    };
    DWORD m_state;
    EtExportError() { m_state = Empty; }

    void ToggleSucceedSheets(bool on) { return alg::SetBit(m_state, HasSucceedSheets, on); }
    bool SucceedSheets() const { return alg::IsBitUsed(m_state, HasSucceedSheets); }

    void ToggleInvalidSheet(bool on) { return alg::SetBit(m_state, HasInvalidSheet, on); }
    bool InvalidSheet() const { return alg::IsBitUsed(m_state, HasInvalidSheet); }
    void ToggleSheetCntLimit(bool on) { return alg::SetBit(m_state, ReachSheetCntLimit, on); }
    bool SheetCntLimit() const { return alg::IsBitUsed(m_state, ReachSheetCntLimit); }
    void ToggleRowCntLimit(bool on) { return alg::SetBit(m_state, ReachRowCntLimit, on); }
    bool RowCntLimit() const { return alg::IsBitUsed(m_state, ReachRowCntLimit); }
    void ToggleProtectedTable(bool on) { return alg::SetBit(m_state, HasProtectedTable, on); }
    bool ProtectedTable() const { return alg::IsBitUsed(m_state, HasProtectedTable); }
    
    void ToggleExceptionThrown(bool on) { return alg::SetBit(m_state, ThrowException, on); }
    bool ExceptionThrown() const { return alg::IsBitUsed(m_state, ThrowException); }

    bool HasFailedMsg() const { return alg::IsBitUsed(m_state, FailedMsgMask); }

    void ToggleBanCreateBlankSheet(bool on) { return alg::SetBit(m_state, BanCreateBlankSheet, on); }
    bool HasBanCreateBlankSheet() const { return alg::IsBitUsed(m_state, BanCreateBlankSheet); }

	PCWSTR GetErrName() const;
};

class KEtRevisionContext;

class Et2DbConverter
{
public:
    Et2DbConverter(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, KEtRevisionContext* ctx, bool toEmptySheet, VarObj& defaultName, bool bIgnoreChangeFieldType);
	virtual ~Et2DbConverter() = default;
    virtual HRESULT Exec() PURE;

	void SetFilePath(PCWSTR path) { m_filePath = path; }
    void SetNeedOptimizeField(bool bNeedOptimizeSelectField) { m_bNeedOptimizeSelectField = bNeedOptimizeSelectField; }
    const EtExportError& GetErrorStatus() const { return m_errorStatus; }
protected:
    virtual HRESULT _GetValidRange(ISheet*, RANGE&) PURE;
    virtual HRESULT _InitializeDestDbSheet() PURE;
    virtual void _AssignName4UnnamedCol(IDbField* pField, int i, std::vector<ks_wstring>& nameVec) PURE;
    virtual void _PreprocessFldName(int row, int col, std::vector<ks_wstring>& nameVec) PURE;
    virtual HRESULT prepareDestDbWorksheet(bool bJustBlankSheet) PURE;
    virtual HRESULT _CreateKanbanView(ISheet*, int defaultTwip) PURE;
    virtual bool _IsSkippedCell(int row, int col) PURE;

    HRESULT _ImportRangeFromSheet();
    HRESULT _ImportRangeInner();
    HRESULT _ImportDataFromRange();
    HRESULT _InitDbSheetContent(_Worksheet*, RANGE&);
    HRESULT _InsertListObject(etoldapi::_Worksheet *pWorkSheet, int rowCnt, int colCnt);
	HRESULT getCellText(IKWorksheet*, ROW, COL, BSTR*) const;

    bool _Valid() const;

    // 参数
    ks_stdptr<_Workbook> m_spEtApiWb;
    ks_stdptr<_Workbook> m_spDbApiWb;
	IBookOp* m_pEtBookOp = nullptr;
	IBookOp* m_pDbBookOp = nullptr;
    KEtRevisionContext* m_ctx;
    bool m_2EmptySheet;
    VarObj& m_defaultName;
    PCWSTR m_defaultFieldName;
    PCWSTR m_defaultGroupName;
    PCWSTR m_gridViewName;
    PCWSTR m_kanbanViewName;
	PCWSTR m_filePath = nullptr;
    // 错误信息记录. 默认初始化
    EtExportError m_errorStatus;
    // sheet相关
    IDX m_etSheetIdx;
    IDX m_dbSheetIdx;
    RANGE m_etRange;
    Database::FieldContext m_fieldContext; // 注意, 它的IEtRevisionContext*构造参数在"从et导出"和"从db导入"两种情形下将指向两种不同的对象
    ks_stdptr<ISheet> m_spEtSheet;
	ks_stdptr<IETStringTools> m_spStringTools;
    ks_stdptr<etoldapi::_Worksheet> m_spDbApiWs;
    ks_stdptr<IDBSheetOp> m_spDbSheetOp;
    ks_stdptr<IDBSheetView> m_spView;
    // 行列相关
    std::vector<int> m_validRows;
    std::vector<int> m_validCols;
    std::vector<int> m_notEmptyCols;

	GlobalSharedStringSet m_strSet;
    // 列命名策略
    std::vector<int> m_colsNeedRename;
    int m_idxColsNeedRename;
    bool m_fieldTypeChange;
    bool m_bIgnoreChangeFieldType;
    bool m_requestFromApp {};
    static const int maxRowCnt = 1048576;

    bool m_bSupportImg;
    bool m_bNeedOptimizeSelectField = true;
};

// 在Et进程执行导出
class Et2DbExporter : public Et2DbConverter
{
public:
    Et2DbExporter(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, KEtRevisionContext* ctx, bool toEmptySheet, VarObj& defaultName
        , VarObj& kanbanCfg, IDX sheetIdx, bool isWholeBook);
    virtual ~Et2DbExporter();

    virtual HRESULT Exec() override;
protected:
    virtual HRESULT _GetValidRange(ISheet*, RANGE&) override;
    virtual HRESULT _InitializeDestDbSheet() override;
    virtual void _AssignName4UnnamedCol(IDbField* pField, int i, std::vector<ks_wstring>& nameVec) override;
    virtual void _PreprocessFldName(int row, int col, std::vector<ks_wstring>& nameVec) override;
    virtual HRESULT prepareDestDbWorksheet(bool bJustBlankSheet) override;
    virtual HRESULT _CreateKanbanView(ISheet*, int defaultTwip) override;
    virtual bool _IsSkippedCell(int row, int col) override;
protected:
    HRESULT ExportOneSheet(IDX sheetIdx, UINT& succeedSheets);
    HRESULT CopySheet();
    HRESULT ExportSheet();
    HRESULT DelayCopyDbSheets();
    // 新建DB默认会有一张数据表
    // 因为数据表导出走的是CopySheet,会追加一个数据表
    // 所以单表或整表第一个sheet为数据表时,需要删除默认的那个数据表
    HRESULT DeleteFirstDbSheet();
    HRESULT ClearAllDataFormat(etoldapi::_Worksheet* pWorkSheet);
    HRESULT CreateBlankDbSheet();
    bool IsSupport();
protected:
    VarObj& m_kanbanCfg;
    bool m_isWholeBook;
    bool m_isFirstDbSheet = false;
    // todo 默认清除格式
    bool m_needClearFormat = true;
    std::unordered_map<UINT, UINT> m_sheetIdMap;
    std::vector<std::pair<etoldapi::_Worksheet*, etoldapi::_Worksheet*>> m_delayedCopyDbSheets;
};

// 在Db进程执行导入
class Et2DbImporter : public Et2DbConverter, public SheetsImporter
{
public:
    Et2DbImporter(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, KEtRevisionContext* ctx, bool toEmptySheet, VarObj& defaultName
        , VarObj& xlsxImport, IDBProtectionJudgement* pProtectionJudgement, bool bIgnoreChangeFieldType);
    virtual ~Et2DbImporter();

    virtual HRESULT Exec() override;
	void SetAttachmentIdMap(GlobalSharedStringHashMap<GlobalSharedString>* idMap) { m_pAttachmentIdMap = idMap; }

    void SetNeedClearFormat(bool needClearFormat) { m_needClearFormat = needClearFormat; }
protected:
    virtual HRESULT _GetValidRange(ISheet*, RANGE&) override;
    virtual HRESULT _InitializeDestDbSheet() override;
    virtual void _AssignName4UnnamedCol(IDbField* pField, int i, std::vector<ks_wstring>& nameVec) override;
    virtual void _PreprocessFldName(int row, int col, std::vector<ks_wstring>& nameVec) override;
    virtual HRESULT prepareDestDbWorksheet(bool bJustBlankSheet) override;
    virtual HRESULT _CreateKanbanView(ISheet*, int defaultTwip) override;
    virtual bool _IsSkippedCell(int row, int col) override;
protected:
    HRESULT _ImportSheet();
	HRESULT copySheet();
    HRESULT _CreateBlankDbSheet();
    HRESULT _ExecOneSheet(IN int sheetIdx, IN bool bAllowCreateBlankSheet, OUT int& succeedSheets);

    HRESULT _DelayCopyDbSheets();
    HRESULT _ClearAllDataFormat(etoldapi::_Worksheet* pWorkSheet);
protected:
    bool m_hasEmptyNameRow;
    VarObj& m_xlsxImport;
    IDBProtectionJudgement* m_pProtectionJudgement;
    std::vector<RECT> m_skippedRects;
    _Workbook* m_pDbWorkbook;
	GlobalSharedStringHashMap<GlobalSharedString>* m_pAttachmentIdMap = nullptr;
    std::unordered_map<UINT, UINT> m_sheetIdMap;
    std::vector<std::pair<etoldapi::_Worksheet*, etoldapi::_Worksheet*>> m_delayedCopyDbSheets;
    bool m_needClearFormat;
};

//ksheet 用于进程内把一个et sheet导出为db sheet
class Et2DbImporterSameProcess : public Et2DbImporter
{
public:
    Et2DbImporterSameProcess(_Workbook* pEtWorkbook, _Workbook* pDbWorkbook, int iEtSheetIdx, int iDbSheetIdx, KEtRevisionContext* ctx, bool toEmptySheet, VarObj& defaultName
        , VarObj& xlsxImport, IDBProtectionJudgement* pProtectionJudgement, bool bAllowCreateBlankSheet, bool bIgnoreChangeFieldType);

    virtual HRESULT Exec() override;

	PCWSTR GetErrName() const;
    ROW GetImporterRangeRowFrom()//获取导入RANGE的起始行号
    {
        return m_etRange.RowFrom();
    }

    etoldapi::_Worksheet* GetDbWorksheet()
    {
        return m_spDbApiWs.get();
    }

    void setRequestIsFromApp() noexcept;

private:
    int m_iEtSheetIdx;
    bool m_bAllowCreateBlankSheet;
};

class CreateDbSheetFromEtWithSpecifyInfo
{
public:
	struct Param
	{
		IDX etSheetIdx = INVALIDIDX;
		ROW rowFrom = INVALID_ROW;
		COL colFrom = INVALID_COL;
		std::vector<PCWSTR>* pFieldNameVec = nullptr;
		std::vector<ET_DbSheet_FieldType>* pFieldTypeVec = nullptr;

		bool isValid(BMP_PTR) const;
	};

	CreateDbSheetFromEtWithSpecifyInfo(_Workbook*, _Workbook*, KEtRevisionContext*, IDBProtectionJudgement*);

	HRESULT Init(const Param&);
	HRESULT Exec();
	PCWSTR GetErrName() const;
	IKWorksheet* GetNewWorksheet() const { return m_pNewWorksheet; }
private:
	HRESULT gainSrcWorksheet();
	HRESULT gainTarWorksheet();
	void rollback();
	HRESULT exec();
    void processImage(int row, int col, ICellImages* pCellImages, EtDbId fldId,
            std::unordered_map<PCWSTR, std::pair<std::vector<int>, const PCWSTR>, util::StrHasher, util::StrEqual>& attachments);

	Param m_param;
	EtExportError m_errorStatus;
	_Workbook* m_pEtWorkbook = nullptr;
	_Workbook* m_pDbWorkbook = nullptr;
	IKWorksheet* m_pSrcWorksheet = nullptr;
	IKWorksheet* m_pNewWorksheet = nullptr;
	KEtRevisionContext* m_pCtx = nullptr;
	IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
	ks_stdptr<IETStringTools> m_spStringTools;
};

} // namespace wo

#endif // __WEBET_DATABASE_SHEET_ET2DB_EXPORTER_H__
