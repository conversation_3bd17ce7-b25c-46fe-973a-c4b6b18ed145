﻿#include "et_dbsheet_utils.h"
#include "et_dbsheet_filter_helper.h"
#include "et_binvar_spec.h"
#include "workbook.h"
#include "ettools/ettools_encode_decoder.h"
#include "../et_revision_context_impl.h"
#include "et_dbsheet_dashboard_utils.h"
#include <public_header/drawing/model/abstract_shape.h>
#include <public_header/drawing/webextension/datasource/kdbdatarange.h>
#include "pivot_core/pivot_core_x.h"
#include "dashboard/et_dashboard_utils.h"
#include "dashboard/webchart_config.h"


namespace wo
{
namespace DbDashboard
{

bool IsEtDataSourceType(WebExtensionDataSourceType dataSourceType)
{
    return dataSourceType == dst_Workbook || dataSourceType == dst_PivotTable;
}

HRESULT SetStatsModuleDataSource(const VarObj& param, IDBChartStatisticMgr* pModuleMgr, IKWebExtension* pWebExtension,
                                 EtDbId* id, PCWSTR dataRangeKey)
{
    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    ks_castptr<IKWorkbookDataSource> pDbDataSource = spDataSourceHost->GetDataSource();
    if (!pDbDataSource)
        return E_FAIL;

    ks_stdptr<IDBChartStatisticModule> spStatisticModule;
    HRESULT hr = pModuleMgr->CreateModule(&spStatisticModule);
    if (FAILED(hr))
        return hr;
    ks_stdptr<IKDataRange> spDbDataRange;
    hr = pDbDataSource->CreateRange(KComVariant(spStatisticModule), &spDbDataRange);
    if (FAILED(hr))
        return hr;

    if (!dataRangeKey)
        dataRangeKey = param.field_str("dataRangeKey");
    hr = pDbDataSource->RegisterRange(dataRangeKey, spDbDataRange);
    if (FAILED(hr))
        return hr;

    WebExtensionInfo info;
    info.webExtensionKey = pWebExtension->GetWebExtensionKey();
    info.dataRangeKey = dataRangeKey;
    VS(spStatisticModule->SetWebExtensionInfo(info));
    if (id)
        *id = spStatisticModule->GetId();
    return S_OK;
}

HRESULT SetStatsModuleSetting(const VarObj& param, _Workbook* pWorkbook, IDBChartStatisticModule* pModule, KEtRevisionContext* pCtx, ET_DBSheet_ChartType chartType)
{
    if (!pModule || !pWorkbook)
        return E_FAIL;
    IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
    HRESULT hr = S_OK;

    if(param.has("multiFieldFilter") && param.field_bool("multiFieldFilter"))
        pModule->GetMutableFilter()->SetEnableMultiFieldFilter(true);

    if (param.has("dataSourceSheetId"))
    {
        hr = pModule->SetDataSourceId(param.field_uint32("dataSourceSheetId"));
        if (FAILED(hr))
            return hr;
    }
    if (param.has("groupLimitNum"))
        VS(pModule->SetGroupLimitNum(param.field_uint32("groupLimitNum")));
    if (param.has("statsLimitNum"))
        VS(pModule->SetStatsLimitNum(param.field_uint32("statsLimitNum")));
    if (param.has("onlyTopNDimensions"))
    {
        hr =  pModule->SetOnlyTopNDimensions(param.field_uint32("onlyTopNDimensions"));
        if (FAILED(hr))
            return hr;
    }
    if (param.has("retainEqualNthDimension"))
    {
        hr =  pModule->SetRetainEqualNthDimension(param.field_bool("retainEqualNthDimension"));
        if (FAILED(hr))
            return hr;
    }
    if (param.has("dimensionFieldId"))
    {
        const IDBRecordsOrderManager* pGroupCondition = pModule->GetConstOrderManager();
        if (!pGroupCondition)
            return E_FAIL;
        UINT groupConditionCount = pGroupCondition->GetGroupConditionCount();
        // 新增和修改都会调用，仅=0时为维度字段新增
        if (groupConditionCount == 0 && groupConditionCount >= pModule->GetGroupLimitNum())
            return E_FAIL;
        KDBGroupUnit dimensionUnit = DBGU_Text;
        if (param.has("dimensionUnit"))
        {
            hr = pDecoder->DecodeGroupUnit(param.field_str("dimensionUnit"), &dimensionUnit);
            if (FAILED(hr))
                return hr;
        }
        bool splitMultiple = param.has("dimensionSplitMultiple") && param.field_bool("dimensionSplitMultiple");
        hr = pModule->SetDimension(DbSheet::GetEtDbId(param, "dimensionFieldId"), dimensionUnit, alg::bool2BOOL(splitMultiple));
        if (FAILED(hr))
            return hr;
    }
    if (param.has("ascending"))
    {
        hr = pModule->SetSortAscending(param.field_bool("ascending"));
        if (FAILED(hr))
            return hr;
    }
    if (param.has("sortByRow"))
    {
        ET_DBSheet_ChartSortType sortType = param.field_bool("sortByRow") ? DbSheet_Chart_ST_SortByHorizontalAxis
                                                                          : DbSheet_Chart_ST_SortByVerticalAxis;
        hr = pModule->SetSortType(sortType);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("sortType"))
    {
        ET_DBSheet_ChartSortType sortType = DbSheet_Chart_ST_SortByHorizontalAxis;
        hr = pDecoder->DecodeDbChartSortType(param.field_str("sortType"), &sortType);
        if (FAILED(hr))
            return hr;

        hr = pModule->SetSortType(sortType);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("seriesSortType"))
    {
        ET_DBSheet_ChartSortType seriesSortType = DbSheet_Chart_ST_NoSort;
        hr = pDecoder->DecodeDbChartSortType(param.field_str("seriesSortType"), &seriesSortType);
        if (FAILED(hr))
            return hr;

        hr = pModule->SetSeriesSortType(seriesSortType);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("onlyCountDimension"))
    {
        hr = pModule->SetOnlyCountDimension(param.field_bool("onlyCountDimension"));
        if (FAILED(hr))
            return hr;
    }
    if (param.has("removeGroupConditionFieldId"))
    {
        hr = pModule->RemoveGroupCondition(DbSheet::GetEtDbId(param, "removeGroupConditionFieldId"));
        if (FAILED(hr))
            return hr;
    }
    if (param.has("groupConditionFieldId"))
    {
        KDBGroupUnit groupUnit = DBGU_Text;
        if (param.has("groupUnit"))
        {
            hr = pDecoder->DecodeGroupUnit(param.field_str("groupUnit"), &groupUnit);
            if (FAILED(hr))
                return hr;
        }
        const IDBRecordsOrderManager* pGroupCondition = pModule->GetConstOrderManager();
        if (!pGroupCondition)
            return E_FAIL;
        UINT groupConditionCount = pGroupCondition->GetGroupConditionCount();
        // 新增和修改都会调用，仅=1时为分组新增
        if (groupConditionCount == 1 && groupConditionCount >= pModule->GetGroupLimitNum())
            return E_FAIL;
        hr = pModule->AddGroupCondition(DbSheet::GetEtDbId(param, "groupConditionFieldId"), groupUnit);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("filtersOp"))
    {
        KDbFilterOpType op = DBFOT_And;
        hr = pDecoder->DecodeKDbFilterOpType(param.field_str("filtersOp"), &op);
        if (FAILED(hr))
            return hr;
        hr = pModule->SetFiltersOperator(op);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("clearFilter") && param.field_bool("clearFilter"))
    {
        hr = pModule->ClearFilter();
        if (FAILED(hr))
            return hr;
    }
    if (param.has("filterCriteriasRemoveList"))
    {
        binary_wo::VarObj array = param.get("filterCriteriasRemoveList");
        for (int i = 0, length = array.arrayLength(); i < length; ++i)
        {
            binary_wo::VarObj item = array.at(i);
            if(param.has("filterId"))
                hr = pModule->RemoveFilterCriterior(DbSheet::GetEtDbId(item, "filterId"));
            else
                 hr = pModule->RemoveFilterCriteriorByFieldId(DbSheet::GetEtDbId(item, "fieldId"));

            if (FAILED(hr))
                return hr;
        }
    }
    if (param.has("filterCriteriasAddList"))
    {
        binary_wo::VarObj array = param.get("filterCriteriasAddList");
        for (int i = 0, length = array.arrayLength(); i < length; ++i)
        {
            binary_wo::VarObj item = array.at(i);
            ks_stdptr<IDbFilterCriteria> spCriteria;
            hr = CreateDbFilterCriteria(item.get("criteria"), pWorkbook->GetBook(), &spCriteria);
            if (FAILED(hr))
                return hr;

            hr = pModule->AddFilterCriterior(DbSheet::GetEtDbId(item, "fieldId"), spCriteria);
            if (FAILED(hr))
                return hr;
        }
    }
    if (param.has("statisticSettingChangeList"))
    {
        binary_wo::VarObj array = param.get("statisticSettingChangeList");
        for (int i = 0, length = array.arrayLength(); i < length; ++i)
        {
            binary_wo::VarObj item = array.at(i);
            if (item.has("statisticOptions"))
            {
                ET_DBSheet_StatisticOption option = DBSSO_null;
                hr = pDecoder->DecodeStatisticOption(item.field_str("statisticOptions"), &option);
                if (FAILED(hr))
                    return hr;

                if (item.has("fieldId"))
                {
                    hr = pModule->ChangeStatisticOption(DbSheet::GetEtDbId(item, "fieldId"), option);
                }
                else
                {
                    hr = pModule->ChangeStatisticField(DbSheet::GetEtDbId(item, "oldFldId"),
                                                       DbSheet::GetEtDbId(item, "newFldId"), option);
                }
                if (FAILED(hr))
                    return hr;
            }
            if (item.has("yAxisIndex"))
            {
                UINT yAxisIndex = item.field_uint32("yAxisIndex");
                hr = pModule->ChangeStatisticYAxisIndex(DbSheet::GetEtDbId(item, "fieldId"), yAxisIndex);
                if (FAILED(hr))
                    return hr;
            }
            if (item.has("plotType"))
            {
                ET_DBSheet_ChartPlotType plotType = DbSheet_Chart_PT_Bar;
                hr = pDecoder->DecodeDbChartPlotType(item.field_str("plotType"), &plotType);
                if (FAILED(hr))
                    return hr;

                hr = pModule->ChangeStatisticPlotType(DbSheet::GetEtDbId(item, "fieldId"), plotType);
                if (FAILED(hr))
                    return hr;
            }
        }
    }
    if (param.has("statisticSettingRemoveList"))
    {
        binary_wo::VarObj array = param.get("statisticSettingRemoveList");
        for (int i = 0, length = array.arrayLength(); i < length; ++i)
        {
            binary_wo::VarObj item = array.at(i);
            hr = pModule->RemoveStatisticField(DbSheet::GetEtDbId(item, "fieldId"));
            if (FAILED(hr))
                return hr;
        }
    }
    if (param.has("statisticSettingAddList"))
    {
        // 除了新增统计字段，前端还会通过该参数修改option
        binary_wo::VarObj array = param.get("statisticSettingAddList");
        int length = array.arrayLength();
        const IDbChartStatisticOptions* pStatisticOps = pModule->GetConstStatisticOptions();
        if (!pStatisticOps)
            return E_FAIL;
        for (int i = 0; i < length; ++i)
        {
            binary_wo::VarObj item = array.at(i);
            ET_DBSheet_StatisticOption option = DBSSO_null;
            hr = pDecoder->DecodeStatisticOption(item.field_str("statisticOptions"), &option);
            if (FAILED(hr))
                return hr;

            EtDbId fieldId = DbSheet::GetEtDbId(item, "fieldId");
            if (!pCtx->isExecDirect() && !pStatisticOps->HasStatistic(fieldId) && pStatisticOps->Count() >= GetChartMaxStatisticsCount(chartType))
                return E_FAIL;
            hr = pModule->AddStatisticField(fieldId, option);
            if (FAILED(hr))
                return hr;

            if (item.has("yAxisIndex"))
            {
                UINT yAxisIndex = item.field_uint32("yAxisIndex");
                hr = pModule->ChangeStatisticYAxisIndex(fieldId, yAxisIndex, true);
                if (FAILED(hr))
                    return hr;
            }

            if (item.has("plotType"))
            {
                ET_DBSheet_ChartPlotType plotType = DbSheet_Chart_PT_Bar;
                hr = pDecoder->DecodeDbChartPlotType(item.field_str("plotType"), &plotType);
                if (FAILED(hr))
                    return hr;

                hr = pModule->ChangeStatisticPlotType(fieldId, plotType);
                if (FAILED(hr))
                    return hr;
            }
        }
    }
    if (param.has("clearCustomOrder") && param.field_bool("clearCustomOrder"))
    {
        hr = pModule->ClearCustomCategoryOrder();
        if (FAILED(hr))
            return hr;
    }
    if (param.has("visibleFields"))
    {
        binary_wo::VarObj item = param.get("visibleFields");
        if (!item.has("mode"))
            return E_FAIL;
        ET_DBSheet_FieldRangeMode mode = DbSheet_FR_include;
        hr = pDecoder->DecodeDbFieldRangeMode(item.field_str("mode"), &mode);
        if (FAILED(hr))
            return hr;
        std::vector<EtDbId> visibleFields;
        if (mode == DbSheet_FR_include) 
        {
            if (!item.has("ids"))
                return E_FAIL;
            binary_wo::VarObj array = item.get("ids");
            int length = array.arrayLength();
            for (int i = 0; i < length; ++i)
            {
                EtDbId fieldId = DbSheet::GetEtDbId(array.at(i));
                visibleFields.emplace_back(fieldId); 
            }
        }
        hr = pModule->SetVisibleFields(mode,visibleFields.data(),visibleFields.size());
        if (FAILED(hr))
            return hr;    
    }
    return S_OK;
}

IKWebExtension* CreateWebExtension(etoldapi::Shapes* pShapes, PCWSTR WebExtKey, WebExtType webExtType)
{
    ks_bstr webExtensionKey(WebExtKey);
    if (webExtensionKey.empty())
        return nullptr;

    ks_stdptr<KsoShape> spKsoShape;
    HRESULT hr = pShapes->AddWebExtension(webExtensionKey, nullptr, webExtType, &spKsoShape);
    if (FAILED(hr))
        return nullptr;

    ks_stdptr<IKsoShapeEx> spShapeEx = spKsoShape;
    if (!spShapeEx)
        return nullptr;

    ks_stdptr<IKShape> spCoreShape;
    spShapeEx->GetInterface(IID_IKShape, (void**) &spCoreShape);
    drawing::AbstractShape* pAbsShape = static_cast<drawing::AbstractShape*>(spCoreShape.get());
    IKWebExtension* pWebExtension = pAbsShape->getWebExtension();
    return pWebExtension;
}

HRESULT DeleteWebExtensionHostShape(_Worksheet* pWorkSheet, IKWebExtension* pWebExtension)
{
    ks_stdptr<IKShape> spHostShape;
    HRESULT hr = pWebExtension->GetHostShape(&spHostShape);
    if (FAILED(hr))
        return hr;

    long hostShapeId = 0;
    hr = spHostShape->GetShapeID(&hostShapeId);
    if (FAILED(hr))
        return hr;

    ks_stdptr<etoldapi::Shapes> spShapes;
    hr = pWorkSheet->get_Shapes(FALSE, &spShapes);
    if (!spShapes)
        return E_FAIL;

    int shapeCount = 0;
    hr = spShapes->get_Count(&shapeCount);
    if (FAILED(hr))
        return hr;

    for (int i = 1; i <= shapeCount; i++)
    {
        ks_stdptr<oldapi::KsoShape> spShape;
        spShapes->_Item(i, &spShape);
        ASSERT(spShape);
        if (!spShape)
            continue;

        int shapeId = 0;
        hr = spShape->get_Id(&shapeId);
        if (FAILED(hr))
            continue;

        if (shapeId == hostShapeId)
            return spShape->Delete();
    }
    return E_FAIL;
}

HRESULT GetDbChartStatisticModuleId(IKWebExtension* pWebExtension, EtDbId& moduleId)
{
    if (!pWebExtension->IsDatasourceSupportingDashboardModule())
        return E_FAIL;

    LPCWSTR propertyValue = nullptr;
    HRESULT hr = pWebExtension->GetProperty(GetWebofficeUniquePropertyKey(), &propertyValue);
    if (FAILED(hr) || !propertyValue)
        return E_FAIL;

	ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
	ks_castptr<IKWorkbookDataSource> spDataSource = spDataSourceHost->GetDataSource();
	if (!spDataSource)
        return E_FAIL;

    WebExtensionDataSourceType dataSourceType = spDataSource->GetType();
    if (dataSourceType != dst_dbTable)
        return E_FAIL;

    VS(spDataSource->GetRangeCount() == 1);
	ks_stdptr<IKDataRangeEnum> spEnum;
    hr = spDataSource->GetDataRangeEnumerator(&spEnum);
    if (FAILED(hr) || !spEnum || spEnum->Reset() != S_OK)
        return E_FAIL;

    ks_castptr<KDBDataRange> pDataRange = spEnum->GetDataRange();
    if (!pDataRange)
        return E_FAIL;

	hr = _appcore_GainDbSheetContext()->DecodeEtDbId(pDataRange->GetModuleId(), &moduleId);
    if (FAILED(hr))
        return E_FAIL;
    return S_OK;
}

HRESULT CreateWebExtensionDataSource(IKWebExtension* pWebExtension, WebExtensionDataSourceType dataSourceType)
{
    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    return spDataSourceHost->CreateDataSource(dataSourceType);
}

HRESULT SetEtDataSource(KEtWorkbook* pEtWorkbook, IDbtBookCtx* pDbtBookCtx, IKWebExtension* pWebExtension, IDBDashBoardDataOp* pDashboardData, RANGE& rg)
{
    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
    if (!IsEtDataSourceType(dataSourceType))
        return E_FAIL;

    ks_castptr<IKWorkbookDataSource> pDataSource = spDataSourceHost->GetDataSource();
    IBook* pBook = pEtWorkbook->GetCoreWorkbook()->GetBook();
    ks_stdptr<IKDataRange> spDataRange;
    UINT boundaryRow = 0;
    UINT boundaryCol = 0;
    BOOL seriesByColumn = TRUE;
    if (dataSourceType == dst_Workbook)
    {
        ks_stdptr<IRangeInfo> spRangeInfo = pEtWorkbook->CreateRangeObj(rg);
        HRESULT hr = pDataSource->CreateRange(KComVariant(spRangeInfo.get()), &spDataRange);
        if (FAILED(hr))
            return hr;

        ks_stdptr<IBookOp> spBookOp;
        pBook->GetOperator(&spBookOp);
        ks_stdptr<IKRanges> spRanges;
        spRangeInfo->GetIRanges(&spRanges);
        DashBoard::CalcBoundaryRowColAndSeriesDirection(spBookOp, spRanges, boundaryRow, boundaryCol, seriesByColumn);
    }
    else if (dataSourceType == dst_PivotTable)
    {
        ks_stdptr<pivot_core::IPivotTable> spCorePvtTbl;
        HRESULT hr = DashBoard::GetPivotTableFromRange(pBook, rg, &spCorePvtTbl);
        if (hr != S_OK)
            return E_FAIL;

        hr = pDataSource->CreateRange(KComVariant(spCorePvtTbl.get()), &spDataRange);
        if (FAILED(hr))
            return hr;
    }
    ks_bstr dataRangeKey;
    HRESULT hr = pDataSource->GetFirstRangeKey(&dataRangeKey);
    if (FAILED(hr))
    {
        PCWSTR uuidStr = pDbtBookCtx->GainUuidStr();
        VS(uuidStr != nullptr);
        dataRangeKey.assign(uuidStr);
    }

    hr = pDataSource->RegisterRange(dataRangeKey, spDataRange);
    if (FAILED(hr))
        return hr;

    EtDbId moduleId = INV_EtDbId;
    VS(_appcore_GainDbSheetContext()->DecodeEtDbId(pWebExtension->GetModuleId(), &moduleId));
    ks_stdptr<IDbWorksheetChart> spWorksheetChart;
    hr = pDashboardData->GetWorksheetChartMgr()->GetChart(moduleId, &spWorksheetChart);
    if (FAILED(hr))
        return hr;

    spWorksheetChart->ResetSeries();
    VS(spWorksheetChart->SetSeriesByColumn(seriesByColumn));
    if (dataSourceType == dst_Workbook)
        spWorksheetChart->SetBoundaryRowCol(boundaryRow, boundaryCol);
    if (dataSourceType == dst_PivotTable)
        VS(spWorksheetChart->SetShowHiddenData(FALSE));
    return S_OK;
}

HRESULT CheckAndChangeDataSourceType(IBook* pBook, UINT dbSheetId, const RANGE& workSheetRange,
                                     IDbtBookCtx* pDbtBookCtx, IKWebExtension* pWebExtension,
                                     IDBDashBoardDataOp* pDashboardData, bool forceNormalRange, EtDbId* pNewModuleId)
{
    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
    WebExtensionDataSourceType newDataSourceType = dst_Unknown;
    if (workSheetRange.IsValid())
    {
        if (forceNormalRange)
        {
            newDataSourceType = dst_Workbook;
        }
        else
        {
            ks_stdptr<pivot_core::IPivotTable> spCorePvtTbl;
            HRESULT hr = DashBoard::GetPivotTableFromRange(pBook, workSheetRange, &spCorePvtTbl);
            if (FAILED(hr))
                return hr;
            newDataSourceType = hr == S_OK ? dst_PivotTable : dst_Workbook;
        }
    }
    else if (dbSheetId != 0)
    {
        newDataSourceType = dst_dbTable;
    }
    if (newDataSourceType == dst_Unknown || newDataSourceType == dataSourceType)
        return S_OK;

    if (IsEtDataSourceType(dataSourceType) && !IsEtDataSourceType(newDataSourceType))
    {
        EtDbId moduleId = INV_EtDbId;
        VS(_appcore_GainDbSheetContext()->DecodeEtDbId(pWebExtension->GetModuleId(), &moduleId));
        HRESULT hr = pDashboardData->GetWorksheetChartMgr()->RemoveChart(moduleId);
        if (FAILED(hr))
            return hr;
        pWebExtension->SetModuleId(__X(""));
    }
    else if (dataSourceType == dst_dbTable && newDataSourceType != dst_dbTable)
    {
        EtDbId moduleId = INV_EtDbId;
        HRESULT hr = GetDbChartStatisticModuleId(pWebExtension, moduleId);
        if (FAILED(hr))
            return hr;

        hr = pDashboardData->GetChartStatisticMgr()->DelItem(moduleId);
        if (FAILED(hr))
            return hr;
    }

    HRESULT hr = spDataSourceHost->CreateDataSource(newDataSourceType);
    if (FAILED(hr))
        return hr;

    ks_castptr<IKWorkbookDataSource> pDataSource = spDataSourceHost->GetDataSource();
    if (!IsEtDataSourceType(dataSourceType) && IsEtDataSourceType(newDataSourceType))
    {
        ks_stdptr<IDbWorksheetChart> spWorksheetChart;
        hr = pDashboardData->GetWorksheetChartMgr()->CreateChart(pWebExtension->GetWebExtensionKey(),
                                                                 &spWorksheetChart);
        if (FAILED(hr))
            return hr;

        if (pNewModuleId)
            *pNewModuleId = spWorksheetChart->GetId();

        EtDbIdStr buf;
        VS(_appcore_GainDbSheetContext()->EncodeEtDbId(spWorksheetChart->GetId(), &buf));
        hr = pWebExtension->SetModuleId(buf);
        if (FAILED(hr))
            return hr;
    }
    else if (dataSourceType != dst_dbTable && newDataSourceType == dst_dbTable)
    {
        ks_stdptr<IDBChartStatisticModule> spStatisticModule;
        hr = pDashboardData->GetChartStatisticMgr()->CreateModule(&spStatisticModule);
        if (FAILED(hr))
            return hr;

        if (pNewModuleId)
            *pNewModuleId = spStatisticModule->GetId();

        ks_stdptr<IKDataRange> spDbDataRange;
        hr = pDataSource->CreateRange(KComVariant(spStatisticModule), &spDbDataRange);
        if (FAILED(hr))
            return hr;

        PCWSTR dataRangeKey = pDbtBookCtx->GainUuidStr();
        VS(dataRangeKey != nullptr);
        hr = pDataSource->RegisterRange(dataRangeKey, spDbDataRange);
        if (FAILED(hr))
            return hr;

        WebExtensionInfo info;
        info.webExtensionKey = pWebExtension->GetWebExtensionKey();
        info.dataRangeKey = dataRangeKey;
        VS(spStatisticModule->SetWebExtensionInfo(info));
    }
    return S_OK;
}

HRESULT SetWorksheetChartSetting(const VarObj& param, IKWebExtension* pWebExtension, IDBDashBoardDataOp* pDashboardData)
{
    EtDbId moduleId = INV_EtDbId;
    VS(_appcore_GainDbSheetContext()->DecodeEtDbId(pWebExtension->GetModuleId(), &moduleId));
    ks_stdptr<IDbWorksheetChart> spWorksheetChart;
    HRESULT hr = pDashboardData->GetWorksheetChartMgr()->GetChart(moduleId, &spWorksheetChart);
    if (FAILED(hr))
        return hr;

    hr = DashBoard::SetWebextentionProperty(param, pWebExtension);
    if (FAILED(hr))
        return hr;

	ks_stdptr<IWorksheetChart> spChart = spWorksheetChart;
	hr = DashBoard::SetWebextentionExtData(param, spChart);
	return hr;
}

HRESULT DeleteWebExtension(IKWebExtension* pWebExtension, _Worksheet* pWorkSheet, IDBDashBoardDataOp* pDashboardData)
{
    if (pWebExtension->IsDatasourceSupportingDashboardModule())
    {
        ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
        WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
        if (dataSourceType == dst_dbTable)
        {
            EtDbId moduleId = INV_EtDbId;
            HRESULT hr = GetDbChartStatisticModuleId(pWebExtension, moduleId);
            if (FAILED(hr))
                return hr;

            hr = pDashboardData->GetChartStatisticMgr()->DelItem(moduleId);
            if (FAILED(hr))
                return hr;
        }
        else if (IsEtDataSourceType(dataSourceType))
        {
            EtDbId moduleId = INV_EtDbId;
            VS(_appcore_GainDbSheetContext()->DecodeEtDbId(pWebExtension->GetModuleId(), &moduleId));
            HRESULT hr = pDashboardData->GetWorksheetChartMgr()->RemoveChart(moduleId);
            if (FAILED(hr))
                return hr;

            ks_stdptr<IKWorkbookDataSource> spDataSource = spDataSourceHost->GetDataSource();
            hr = spDataSource->RemoveAllRange();
            if (FAILED(hr))
                return hr;
        }
    }

    HRESULT hr = DbDashboard::DeleteWebExtensionHostShape(pWorkSheet, pWebExtension);
    if (FAILED(hr))
        return hr;
    ks_wstring wstrWebExtensionKey = pWebExtension->GetWebExtensionKey();
    hr = pWebExtension->Delete(TRUE, FALSE);
    if (SUCCEEDED(hr))
		pDashboardData->OnRemoveWebExtension(wstrWebExtensionKey.c_str());
    return hr;
}

ET_DBSheet_ChartType GetChartType(IKWebExtension* pWebExtension)
{
    PCWSTR pValue = nullptr;
    pWebExtension->GetProperty(__X("chart-info"), &pValue);
    KWebChartConfig config(pValue);
    ET_DBSheet_ChartType chartType = config.GetChartType();
    return chartType;
}

INT GetChartMaxStatisticsCount(ET_DBSheet_ChartType chartType)
{
    DashboardMaxStatsFieldCount dashboardMaxStatsFieldCount;
    switch (chartType)
    {
        case DbSheet_Chart_Unknown:
            break;
        case DbSheet_Chart_Value:
            return dashboardMaxStatsFieldCount.value;
        case DbSheet_Chart_Funnel:
            return dashboardMaxStatsFieldCount.funnel;
        case DbSheet_Chart_BasicPie:
            return dashboardMaxStatsFieldCount.basicPie;
        case DbSheet_Chart_CountDown:
            return dashboardMaxStatsFieldCount.countDown;
        case DbSheet_Chart_Progress:
            return dashboardMaxStatsFieldCount.progress;
        case DbSheet_Chart_MatrixBubble:
            return dashboardMaxStatsFieldCount.matrixBubble;
        default:
            return dashboardMaxStatsFieldCount.defaultValue;
    }
    return 0;
}


}

} // wo
