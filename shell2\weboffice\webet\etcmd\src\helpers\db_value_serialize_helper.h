﻿#ifndef __WEBET_DB_VALUE_SERIALIZE_HELPER__
#define __WEBET_DB_VALUE_SERIALIZE_HELPER__
#include "appcore/et_appcore_dbsheet.h"
namespace wo
{
class KEtWorkbook;
class KDbValueSerializeHelper : public IDbValueSerializeHelper
{
public:
    STDPROC_(void) SerializeAllSheets(Log_ChangeData*, IDbSerializeMultipartHelper* = nullptr) override;
    STDPROC_(void) SerializeSheets(Log_ChangeData*, const UINT*, UINT, IDbSerializeMultipartHelper* = nullptr) override;
    STDPROC_(void) SerializeRecords(Log_sheet*, UINT, const EtDbId*, UINT) override;
    STDPROC_(void) SerializeField(Log_Field*, UINT, EtDbId) override;
    STDPROC_(void) SerializeView(Log_View*, UINT, IDBSheetView*) override;
    STDPROC_(void) SerializeDashboardWebExtension(Log_WebExtension*, UINT, PCWSTR) override;
    STDPROC_(void) SerializeDashboardFilters(Log_sheet*, UINT, const EtDbId*, UINT) override;
    STDPROC_(void) SerializeViewProp(Log_View*, UINT, IDBSheetView*) override;
    // TODO(zhangpeng27) 测一下序列化的性能，看看有没有热点
    STDPROC_(void) SerialiseViewCondition(Log_GroupCondition*, UINT, IDBRecordsOrderCondition*) override;
    STDPROC_(void) SerialiseViewSortSetting(Log_View*, UINT, IDBSheetView*) override;
    STDPROC_(void) SerialiseViewGroupSetting(Log_View*, UINT, IDBSheetView*) override;
    STDPROC_(void) SerialiseViewFilterSetting(Log_FilterCondition*, UINT, IDbFieldFilter*) override;
    STDPROC_(void) SerialisePermission(Log_permissionId*, PCWSTR = NULL) override;
    // dbsheet只关注颜色相关的样式信息，为db特化实现一个序列化函数
    STDPROC_(void) SerialiseXfForDb(Log_Xf*, const XF*, const XFMASK&) override;
    STDPROC_(void) SerializeCell(Log_Col*, UINT, EtDbId, EtDbId) override;

    void Init(wo::KEtWorkbook* pWorkbook)
    {
        ASSERT(pWorkbook);
        m_pWorkbook = pWorkbook;
    }
private:
    void serializeFirstPart(Log_ChangeData*, IDbSerializeMultipartHelper*, const std::vector<IKWorksheet*>&);
    void serializeMiddlePart(Log_ChangeData*, IDbSerializeMultipartHelper*);
    void serializeSidebarFolder(Log_ChangeData*);
    void serialiseXfs(Log_ChangeData*, const std::vector<IKWorksheet*>&);
private:
    wo::KEtWorkbook* m_pWorkbook = nullptr;
};
}  // namespace wo
#endif  // __WEBET_DB_VALUE_SERIALIZE_HELPER__