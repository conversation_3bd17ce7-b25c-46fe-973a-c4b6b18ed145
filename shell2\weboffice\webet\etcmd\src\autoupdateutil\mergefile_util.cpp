#include "etstdafx.h"
#include "etcore/little_alg.h"
#include "workbook.h"
#include "et_revision_context_impl.h"
#include "helpers/attachment_helper.h"
#include "webetlink.h"
#include "webbase/binvariant/binwriter.h"
#include "wo/et_revision_context.h"
#include "wo/workbook_obj.h"
#include "security/utils.h"
#include <public_header/webcommon/src/security_doc.h>
#include "mergefile_util.h"
#include "kso/api/smartparam.h"
#include "util.h"

extern Callback* gs_callback;

namespace wo
{
namespace autoupdate_util
{


static void CloseFile(_Workbook* ptrWorkbook)
{
	VARIANT SaveChanges;
	V_VT(&SaveChanges) = VT_BOOL;
	V_BOOL(&SaveChanges) = VARIANT_FALSE;

	VARIANT Filename = {0};
	VARIANT RouteWorkbook = {0};

	ptrWorkbook->Close(SaveChanges, Filename, RouteWorkbook);
}

static INT64 GetCurTime()
{
	return QDateTime::currentDateTime().toMSecsSinceEpoch();
}


////////////////////////////  MergeFileUtil  ////////////////////////////
MergeFileUtil::MergeFileUtil(KEtWorkbook* wb, const char* userID)
    : m_wwb(wb)
    , m_userID(userID)
{
    InitVariabelState();
}

void MergeFileUtil::PrepareForMergeTask()
{
    _Workbook* pCoreWorkbook = m_wwb->GetCoreWorkbook();
    ks_stdptr<IKWorksheets> spWorksheets = pCoreWorkbook->GetWorksheets();
	INT worksheet_count = spWorksheets->GetSheetCount();

    std::vector<SMergeFileInfo> vecData;
    for(INT nIndex = 0; nIndex < worksheet_count; nIndex++)
    {
        ks_castptr<etoldapi::_Worksheet> pWorksheet = spWorksheets->GetSheetItem(nIndex);
        ASSERT(pWorksheet);
        GetMergeFileInfo(pWorksheet, vecData);
		WOLOG_INFO << "[PrepareForMergeTask] merge file info, sheetIdx: " << nIndex << ", vecData.size: " << vecData.size();
        for (const SMergeFileInfo& item : vecData)
        {
            const char* userID = m_userID == NULL ? "" : m_userID;
			QString qUserID = QString::fromUtf8(userID);
            m_wwb->asyncGetNetDiskFile(krt::utf16(qUserID), item.strId.c_str(), DownloadFileTask_MergeFile);
        }
    }
}

void MergeFileUtil::MergeFileSubscribe()
{
	if (!gs_callback->signalSubscriptionCenter)
		return;
	
	WOLOG_INFO << "[MergeFileSubscribe] merge file subscribe.";
	
	SubscriberRanges sourceToSubscribe;
	_Workbook* pCoreWorkbook = m_wwb->GetCoreWorkbook();
    ks_stdptr<IKWorksheets> spWorksheets = pCoreWorkbook->GetWorksheets();
	INT worksheet_count = spWorksheets->GetSheetCount();

	std::vector<SMergeFileInfo> vecData;
    for(INT nIndex = 0; nIndex < worksheet_count; nIndex++)
    {
        ks_castptr<etoldapi::_Worksheet> pWorksheet = spWorksheets->GetSheetItem(nIndex);
        ASSERT(pWorksheet);
        GetMergeFileInfo(pWorksheet, vecData);
        for (const SMergeFileInfo& item : vecData)
        {
			auto itor = sourceToSubscribe.find(item.strId);
			if (itor != sourceToSubscribe.end())
			{
				for (const SMergeSrcInfo& srcInfo : item.vecSrcInfo)
				{
					itor->second.insert(srcInfo.sheetName);
				}
			}
			else
			{
				wstrSet tempSet;
				for (const SMergeSrcInfo& srcInfo : item.vecSrcInfo)
				{
					tempSet.insert(srcInfo.sheetName);
				}
				sourceToSubscribe.insert({item.strId, tempSet});
			}
        }
    }

	for (auto item : sourceToSubscribe)
	{
		ks_wstring fileId = item.first;
		QString qFileId(QString::fromUtf16(fileId.c_str()));
		BinWriter binWriter;
		binWriter.beginArray("ranges");
		wstrSet& tempSet = item.second;
		for (const ks_wstring& shtName : tempSet)
		{
			binWriter.beginStruct();
				binWriter.addKey("sheetName");
				binWriter.addString(shtName.c_str());
			binWriter.endStruct();
		}
		binWriter.endArray();
		BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};
		gs_callback->signalSubscriptionCenter(WoNotifySubscribeCb, "mergefile", qFileId.toUtf8(), &slice);
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[MergeFileSubscribe] (signalSubscriptionCenter): subscribe fileId: %1").arg(qFileId).toUtf8());
	}
}

bool MergeFileUtil::GetSrcFileNetInfo(_Worksheet* pSheet, std::vector<NetFileInfo>& netFileInfos)
{
    std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
    if (!ctx) return false;

    std::vector<SMergeFileInfo> srcFilesInfo;
    if (!GetMergeFileInfo(pSheet, srcFilesInfo))
        return false;

    netFileInfos.clear();
    for (const SMergeFileInfo& item : srcFilesInfo)
    {
        NetFileInfo netFileInfo;
        netFileInfo.stat = ctx->getNetDiskFile(__X("yunwps"), item.strId.c_str(),
                                netFileInfo.path, netFileInfo.version, netFileInfo.filename);
		netFileInfo.fileId = item.strId;
		if (netFileInfo.stat == sbsFileNotReady || netFileInfo.stat == sbsNotExist)
		{
			m_waitingFiles.insert(item.strId);
		}
        netFileInfos.push_back(netFileInfo);
    }
    return true;
}

void MergeFileUtil::GetWaitingFiles(std::vector<ks_wstring>& waitingFiles)
{
	for (const ks_wstring& item : m_waitingFiles)
	{
		waitingFiles.push_back(item);
	}
	WOLOG_INFO << "[GetWaitingFiles] waitingFiles.size(): " << waitingFiles.size();
}

void MergeFileUtil::GetMergeFilesAndUnready(std::vector<ks_wstring>& waitingFiles, std::vector<ks_wstring>& allFiles)
{
	std::unique_ptr<KEtRevisionContext> ctx = CreateContext();
    if (!ctx)
	{
		WOLOG_ERROR << "[GetMergeFilesNotReady] CreateContext Failed";
		return;
	}

	_Workbook* pCoreWorkbook = m_wwb->GetCoreWorkbook();
    ks_stdptr<IKWorksheets> spWorksheets = pCoreWorkbook->GetWorksheets();
	INT worksheet_count = spWorksheets->GetSheetCount();
	
	wstrSet waitingSet;
	wstrSet allSet;
    std::vector<SMergeFileInfo> vecData;
    for(INT nIndex = 0; nIndex < worksheet_count; nIndex++)
    {
        ks_castptr<etoldapi::_Worksheet> pWorksheet = spWorksheets->GetSheetItem(nIndex);
        ASSERT(pWorksheet);
        if (!GetMergeFileInfo(pWorksheet, vecData) || !GetIsAutoRefresh(pWorksheet))
			continue;

		for (const SMergeFileInfo& item : vecData)
		{
			NetFileInfo netFileInfo;
			netFileInfo.stat = ctx->getNetDiskFile(__X("yunwps"), item.strId.c_str(),
									netFileInfo.path, netFileInfo.version, netFileInfo.filename);
			if (netFileInfo.stat == sbsFileNotReady || netFileInfo.stat == sbsNotExist)
			{
				waitingSet.insert(item.strId);
			}
			allSet.insert(item.strId);
		}
    }
	waitingFiles.clear();
	for (const ks_wstring& item : waitingSet)
	{
		waitingFiles.push_back(item);
	}
	allFiles.clear();
	for (const ks_wstring& item : allSet)
	{
		allFiles.push_back(item);
	}
}

// 后续是否需要将失败原因返回？
bool MergeFileUtil::IsReadyToMerge(_Worksheet* pSheet)
{
    std::vector<NetFileInfo> netFileInfos;
    if (!GetSrcFileNetInfo(pSheet, netFileInfos))
        return false;
    
    for (const NetFileInfo& item : netFileInfos)
    {
        if (item.stat != sbsValidFile)
            return false;
    }
    return true;
}

bool MergeFileUtil::GetMergeFileInfo(_Worksheet* pSheet, std::vector<SMergeFileInfo>& vecData)
{
	ks_stdptr<IMergeFile> spMergeFile;

    ks_stdptr<IUnknown> spUnk;
    pSheet->GetSheet()->GetExtDataItem(edSheetMergeFile, &spUnk);
	spMergeFile = spUnk;
	 if (!spMergeFile) return false;

    spMergeFile->GetMergeFileInfo(vecData);
    if (vecData.empty()) return false;

    return true;
}

bool MergeFileUtil::GetIsAutoRefresh(_Worksheet* pSheet)
{
	ks_stdptr<IMergeFile> spMergeFile;

    ks_stdptr<IUnknown> spUnk;
    pSheet->GetSheet()->GetExtDataItem(edSheetMergeFile, &spUnk);
	spMergeFile = spUnk;
	if (!spMergeFile) return false;

	return spMergeFile->GetIsAutoRefresh();
}

void MergeFileUtil::GetMergeFileError(_Worksheet* pSheet, ks_wstring& err, ks_wstring& fileId, ks_wstring& bkName, NetFileRes& res)
{
	ks_stdptr<IMergeFile> spMergeFile;

    ks_stdptr<IUnknown> spUnk;
    pSheet->GetSheet()->GetExtDataItem(edSheetMergeFile, &spUnk);
	spMergeFile = spUnk;
	if (!spMergeFile) return;

	spMergeFile->GetMFError(err, fileId, bkName, res);
}

WebInt MergeFileUtil::DoMergeAllMergeTask(bool isCaculating)
{
	if (!gs_callback->broadcast)
		return WO_OK;

	_Workbook* pCoreWorkbook = m_wwb->GetCoreWorkbook();
    ks_stdptr<IKWorksheets> spWorksheets = pCoreWorkbook->GetWorksheets();
	INT worksheet_count = spWorksheets->GetSheetCount();
	WOLOG_INFO << "[DoMergeAllMergeTask] merge file start.";

	std::vector<SMergeFileInfo> vecData;
	std::vector<WebID> vecDirtySheets;
    for(INT nIndex = 0; nIndex < worksheet_count; nIndex++)
    {
        ks_castptr<etoldapi::_Worksheet> pWorksheet = spWorksheets->GetSheetItem(nIndex);
        ASSERT(pWorksheet);
        if (!GetMergeFileInfo(pWorksheet, vecData) || !GetIsAutoRefresh(pWorksheet))
			continue;

		IWorksheetObj* pObjSheet = pCoreWorkbook->GetWoObject()->getSheetItem(nIndex);
		if (pObjSheet)
			vecDirtySheets.push_back(pObjSheet->objId());
    }

	if (vecDirtySheets.empty())
	{
		WOLOG_INFO << "[DoMergeAllMergeTask] no sheet need update.";
		return WO_OK;
	}

	if (!isCaculating)
	{
		BinWriter bw;
		bw.addStringField(__X("startAutoUpdate"), "fileMergeEvent");
		bw.addKey("dirtySheets");
		bw.beginArray();
		for (auto objID : vecDirtySheets)
		{
			bw.addFloat64(objID);
		}
		bw.endArray();
		BroadcastNow(msgType_MergeFiles, bw);
	}

	std::unique_ptr<KEtRevisionContext> pCtx = CreateContext();
	for (auto objID : vecDirtySheets)
	{
		INT idxSheet = pCtx->getSheetIndex(objID);
		ks_castptr<etoldapi::_Worksheet> pWorksheet = spWorksheets->GetSheetItem(idxSheet);
		if (isCaculating)
		{
			DoMergeWithoutUser(pWorksheet);
		}
		else
		{
			DoMerge(pWorksheet);
		}

		if (!isCaculating)
		{
			BinWriter bw;
			bw.addStringField(__X("endAutoUpdate"), "fileMergeEvent");
			ks_wstring err;
			ks_wstring fileId;
			ks_wstring bkName;
			NetFileRes res;
			GetMergeFileError(pWorksheet, err, fileId, bkName, res);
			bw.addStringField(err.c_str(), "mergeResult");
			if (xstricmp(err.c_str(), __X("S_OK")) != 0)
			{
				bw.addStringField(fileId.c_str(), "errFileId");
				bw.addStringField(bkName.c_str(), "errBookName");
				bw.addInt32Field(res, "errFileNetState");
			}
			bw.addFloat64Field(objID, "objSheet");
			BroadcastNow(msgType_MergeFiles, bw);
		}
	}

	WOLOG_INFO << "[DoMergeAllMergeTask] waiting files number: " << m_waitingFiles.size();
	return WO_OK;
}

WebInt MergeFileUtil::DoMerge(_Worksheet* pSheetDst)
{
	wo::util::CallTimeStat callTime("DoMerge");
    std::vector<SMergeFileInfo> mergeTaskInfo;
    GetMergeFileInfo(pSheetDst, mergeTaskInfo);

    std::vector<NetFileInfo> netFileInfos;
    if (!GetSrcFileNetInfo(pSheetDst, netFileInfos))
        return WO_FAIL;
	
	// Validate files
	for (const NetFileInfo& item : netFileInfos)
    {
		QString fileID = QString::fromUtf16(item.fileId.c_str());
		SMergeFileInfo curMergeFileInfo;
		GetSingleSrcInfoByfileId(item.fileId, mergeTaskInfo, curMergeFileInfo);
        if (item.stat != sbsValidFile)
		{			
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] invalid file %1/%2").arg(fileID).arg(curMergeFileInfo.strDocName.c_str()).toUtf8());
            return WO_FAIL;
		}
	}
    WOLOG_INFO << "[DoMerge] after GetSrcFileNetInfo";

    std::unique_ptr<KEtRevisionContext> pCtx = CreateContext();
    if (!pCtx) return WO_FAIL;

	InitVariabelState(pSheetDst);
	ks_stdptr<IMergeFile> spMergeFile;
	{
		ks_stdptr<IUnknown> spUnk;
		pSheetDst->GetSheet()->GetExtDataItem(edSheetMergeFile, &spUnk);
		spMergeFile = spUnk;
		ASSERT(spMergeFile);
	}
	PCWSTR dstBookFileId = pCtx->getFileId();
	ks_bstr dstBookName;
	m_wwb->GetCoreWorkbook()->get_Name(&dstBookName);

    ks_stdptr<Workbooks> ptrWorkbooks = NULL;
	m_wwb->GetCoreApp()->get_Workbooks(&ptrWorkbooks);
	ks_stdptr<_Workbook> ptrWorkbookDst;
	int nDstBookIdx = 1;
	ptrWorkbooks->get_Item(KComVariant(nDstBookIdx, VT_I4), &ptrWorkbookDst);
	if (!ptrWorkbookDst) return WO_FAIL;

    const bool isWorkbookDstHidden = pCtx->getProtectionCtx()->isBookHasHiddenProperty(ptrWorkbookDst->GetBook());
	if (isWorkbookDstHidden)
	{
		WOLOG_INFO << "[mergefileAutoupdate] merge failed, E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK";
		spMergeFile->RecordMFError(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK, dstBookFileId, dstBookName.c_str(), 0);
		return WO_FAIL;
	}
	// 减少计算
	ks_stdptr<IBookOp> spBookOp;
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
	util::CalcBatchUpdate calcBatchUpdate(spBookOp);
    
    // 清空sheet
    if (FAILED(ClearDstSheet(pSheetDst)))
        return WO_FAIL;

	// 关闭下事务
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(ptrWorkbookDst);
	ks_stdptr<IWorkspace> spWS;
	ks_stdptr<IBook> spBook = ptrWorkbookDst->GetBook();
	spBook->GetWorkspace(&spWS);
	autoupdate_util::DisableTransHlp _disable(spWS);

	spMergeFile->ClearRegionData();
    wo::CloudImgAttachmentCollector attchmentHelper(pCtx.get());
    bool bFirstFile = true;
	bool bMergeSucc = false;
    for (const NetFileInfo& item : netFileInfos)
    {
		QString fileID = QString::fromUtf16(item.fileId.c_str());
		SMergeFileInfo curMergeFileInfo;
		GetSingleSrcInfoByfileId(item.fileId, mergeTaskInfo, curMergeFileInfo);
        if (item.stat != sbsValidFile)
		{			
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 skipped").arg(fileID).toUtf8());
			spMergeFile->RecordMFError(E_MERGEFILES_NETFILE_INVALID, krt::utf16(fileID), curMergeFileInfo.strDocName.c_str(), item.stat);
            return WO_FAIL;
		}
        // 打开文件
		QString qPathItem = QString::fromUtf16(item.path.c_str());
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] before OpenFile, fileId %1, path %2").arg(fileID).arg(qPathItem).toUtf8());
        ks_stdptr<_Workbook> ptrWorkbookSrc = OpenFile(ptrWorkbooks, item.path);
        if (ptrWorkbookSrc == NULL)
		{
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 open null").arg(fileID).toUtf8());
			spMergeFile->RecordMFError(E_MERGEFILES_OPEN_FILE_FAILED, krt::utf16(fileID), curMergeFileInfo.strDocName.c_str(), 0);
			return WO_FAIL;
		}

        // 源文件权限检查
        HRESULT hrCheckSec = CheckSecurityDoc(ptrWorkbookSrc);
		if(hrCheckSec != S_OK)
		{
			spMergeFile->RecordMFError(E_OPERATION_NOT_SUPPORTED_ON_SECDOC, krt::utf16(fileID), curMergeFileInfo.strDocName.c_str(), 0);
			CloseFile(ptrWorkbookSrc);
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 SecurityDoc").arg(fileID).toUtf8());
			return WO_FAIL;
		}

        const bool isWorkbookSrcHidden = pCtx->getProtectionCtx()->isBookHasHiddenProperty(ptrWorkbookSrc->GetBook());
		if (isWorkbookSrcHidden)
		{
			spMergeFile->RecordMFError(E_MERGEFILES_SOURCEBOOK_WITH_HIDDEN_RANGE, krt::utf16(fileID), curMergeFileInfo.strDocName.c_str(), 0);
			CloseFile(ptrWorkbookSrc);
			WOLOG_INFO << "[mergefileAutoupdate] merge failed, [protection] "  << WOLOG_MK_KV(isWorkbookSrcHidden);
			return WO_FAIL;
		}

        //复制数据
		WOLOG_INFO << "[mergefileAutoupdate] start CopyBookData";
		attchmentHelper.begin(item.fileId);
		HRESULT hrCopyBookData = CopyBookData(ptrWorkbookDst, pSheetDst, ptrWorkbookSrc, bFirstFile, true, curMergeFileInfo);
		attchmentHelper.end();
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
		if (hrCopyBookData != S_OK)
		{
			HRESULT hrErr = hrCopyBookData == E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND ? 
					E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND : E_MERGEFILES_COPYDATA_FAILED;
			spMergeFile->RecordMFError(hrErr, krt::utf16(fileID), curMergeFileInfo.strDocName.c_str(), 0);
			CloseFile(ptrWorkbookSrc);
			WOLOG_INFO << "[mergefileAutoupdate] merge failed, CopyBookData failed "  << hrCopyBookData;
			return WO_FAIL;
		}

        //关闭源文件
		CloseFile(ptrWorkbookSrc);
        bMergeSucc = true;
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merged, file %1 done, bMergeSucc: %2").arg(fileID).arg(bMergeSucc).toUtf8());
    }
    attchmentHelper.filloutAttachmentIds(bMergeSucc);

	// 是否标记数据源
	AddDataSrcFields(pSheetDst);

	//去重复去空行
	if (m_isRemoveDuplicates)
	{
		RemoveDuplicates(pSheetDst);
	}
    
    spMergeFile->SetMergeTime(GetCurTime());
	spMergeFile->SetTaskState(false);
	spMergeFile->RecordMFError(S_OK, __X(""), __X(""), 0);
    return WO_OK;
}

// 无用户态文件合并；
// 合并到新sheet(A)，成功，则重命名为(B)，删除原合并sheet(B)
//				    失败，则删除新sheet(A), 保留原Sheet中数据
WebInt MergeFileUtil::DoMergeWithoutUser(_Worksheet* pMergeSheet)
{
	wo::util::CallTimeStat callTime("DoMergeWithoutUser");

    std::vector<SMergeFileInfo> mergeTaskInfo;
    GetMergeFileInfo(pMergeSheet, mergeTaskInfo);

    std::vector<NetFileInfo> netFileInfos;
    if (!GetSrcFileNetInfo(pMergeSheet, netFileInfos))
        return WO_FAIL;
	WOLOG_INFO << "[DoMerge] after GetSrcFileNetInfo";
    
    std::unique_ptr<KEtRevisionContext> pCtx = CreateContext();
    if (!pCtx) return WO_FAIL;

	InitVariabelState(pMergeSheet);
	ks_stdptr<IMergeFile> spMergeFile;
	{
		ks_stdptr<IUnknown> spUnk;
		pMergeSheet->GetSheet()->GetExtDataItem(edSheetMergeFile, &spUnk);
		spMergeFile = spUnk;
		ASSERT(spMergeFile);
	}
	PCWSTR dstBookFileId = pCtx->getFileId();
	ks_bstr dstBookName;
	m_wwb->GetCoreWorkbook()->get_Name(&dstBookName);

    ks_stdptr<Workbooks> ptrWorkbooks = NULL;
	m_wwb->GetCoreApp()->get_Workbooks(&ptrWorkbooks);
	ks_stdptr<_Workbook> ptrWorkbookDst;
	int nDstBookIdx = 1;
	ptrWorkbooks->get_Item(KComVariant(nDstBookIdx, VT_I4), &ptrWorkbookDst);
	if (!ptrWorkbookDst) return WO_FAIL;

    const bool isWorkbookDstHidden = pCtx->getProtectionCtx()->isBookHasHiddenProperty(ptrWorkbookDst->GetBook());
	if (isWorkbookDstHidden)
	{
		WOLOG_INFO << "[mergefileAutoupdate] merge failed, E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK";
		return WO_FAIL;
	}

	//获取目标sheets
	ks_stdptr<etoldapi::Worksheets> spSheetsDst = NULL;
	ptrWorkbookDst->get_Worksheets(&spSheetsDst);
	if (!spSheetsDst) return E_FAIL;
	
	ks_stdptr<etoldapi::_Worksheet> spSheetDst;
	HRESULT hr = CreateDstSheet(spSheetsDst, pMergeSheet, spSheetDst);
	if (FAILED(hr)) return hr;

	// 减少计算
	ks_stdptr<IBookOp> spBookOp;
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
	util::CalcBatchUpdate calcBatchUpdate(spBookOp);

	// 关闭下事务
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(ptrWorkbookDst);
	ks_stdptr<IWorkspace> spWS;
	ks_stdptr<IBook> spBook = ptrWorkbookDst->GetBook();
	spBook->GetWorkspace(&spWS);
	autoupdate_util::DisableTransHlp _disable(spWS);

    wo::CloudImgAttachmentCollector attchmentHelper(pCtx.get());
    bool bFirstFile = true;
	bool bMergeSucc = false;
    for (const NetFileInfo& item : netFileInfos)
    {
		QString fileID = QString::fromUtf16(item.fileId.c_str());
        if (item.stat != sbsValidFile)
		{			
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 skipped").arg(fileID).toUtf8());
			DelUseLessSheet(ptrWorkbookDst);
            return WO_FAIL;
		}
        // 打开文件
		QString qPathItem = QString::fromUtf16(item.path.c_str());
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] withoutuser before OpenFile, fileId %1, path %2").arg(fileID).arg(qPathItem).toUtf8());
        ks_stdptr<_Workbook> ptrWorkbookSrc = OpenFile(ptrWorkbooks, item.path);
        if (ptrWorkbookSrc == NULL)
		{
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 open null").arg(fileID).toUtf8());
			DelUseLessSheet(ptrWorkbookDst);
			return WO_FAIL;
		}

        // 源文件权限检查
        HRESULT hrCheckSec = CheckSecurityDoc(ptrWorkbookSrc);
		if(hrCheckSec != S_OK)
		{
			CloseFile(ptrWorkbookSrc);
			DelUseLessSheet(ptrWorkbookDst);
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 SecurityDoc").arg(fileID).toUtf8());
			return WO_FAIL;
		}

        const bool isWorkbookSrcHidden = pCtx->getProtectionCtx()->isBookHasHiddenProperty(ptrWorkbookSrc->GetBook());
		if (isWorkbookSrcHidden)
		{
			CloseFile(ptrWorkbookSrc);
			DelUseLessSheet(ptrWorkbookDst);
			WOLOG_INFO << "[mergefileAutoupdate] merge failed, [protection] "  << WOLOG_MK_KV(isWorkbookSrcHidden);
			return WO_FAIL;
		}

        //复制数据
		SMergeFileInfo curMergeFileInfo;
		GetSingleSrcInfoByfileId(item.fileId, mergeTaskInfo, curMergeFileInfo);
		attchmentHelper.begin(item.fileId);
		WOLOG_INFO << "[mergefileAutoupdate] start CopyBookData";
		HRESULT hrCopyBookData = CopyBookData(ptrWorkbookDst, spSheetDst, ptrWorkbookSrc, bFirstFile, true, curMergeFileInfo);
		attchmentHelper.end();
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
		if (hrCopyBookData != S_OK)
		{
			HRESULT hrErr = hrCopyBookData == E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND ? 
					E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND : E_MERGEFILES_COPYDATA_FAILED;
			spMergeFile->RecordMFError(hrErr, krt::utf16(fileID), curMergeFileInfo.strDocName.c_str(), 0);
			CloseFile(ptrWorkbookSrc);
			DelUseLessSheet(ptrWorkbookDst);
			WOLOG_INFO << "[mergefileAutoupdate] merge failed, CopyBookData failed "  << hrCopyBookData;
			return WO_FAIL;
		}

        //关闭源文件
		CloseFile(ptrWorkbookSrc);
        bMergeSucc = true;
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merged, file %1 done, bMergeSucc: %2").arg(fileID).arg(bMergeSucc).toUtf8());
    }
    attchmentHelper.filloutAttachmentIds(bMergeSucc);

	// 是否标记数据源
	AddDataSrcFields(spSheetDst);

	//去重复去空行
	if (m_isRemoveDuplicates)
	{
		RemoveDuplicates(spSheetDst);
	}

	// TODO: 获取原sheet名称，赋值给新sheet, 删除原有sheet
	ks_bstr bstrMergeSheetName;
	pMergeSheet->get_Name(&bstrMergeSheetName);
	pMergeSheet->DeleteDirectly();
	spSheetDst->put_Name(bstrMergeSheetName);

	ks_stdptr<IMergeFile> spDstMergeFile;
	{
		ks_stdptr<IUnknown> spUnk;
		spSheetDst->GetSheet()->GetExtDataItem(edSheetMergeFile, &spUnk);
		spDstMergeFile = spUnk;
		ASSERT(spDstMergeFile);
	}
	spDstMergeFile->SetMergeFileInfo(mergeTaskInfo);
	spDstMergeFile->SetIsMarkDataSrc(m_isMarkSrcData);
	spDstMergeFile->SetTitleRowCnt(m_titleRowCnt);
	spDstMergeFile->SetAutoRefresh(m_autoRefresh);
	spDstMergeFile->SetIsRemoveDuplicates(m_isRemoveDuplicates);
	spDstMergeFile->SetMergeTime(GetCurTime());
    return WO_OK;
}

WebInt MergeFileUtil::DoMergeByIncreament(_Worksheet* pMergeSheet, const FileInfos& fileInfos)
{
	ks_stdptr<IMergeFile> spMergeFile;
	pMergeSheet->GetSheet()->GetExtDataItem(edSheetMergeFile, reinterpret_cast<IUnknown **>(&spMergeFile));
	if (!spMergeFile)
		return WO_FAIL;
	if (!spMergeFile->CanMergeByIncrementway())
		return DoMerge(pMergeSheet);

	FileInfos filesNeedUpdate;
	FileInfos filesStateInvalid;

	for (auto fileInfo : fileInfos)
	{
		if (spMergeFile->IsSourceData(fileInfo.first))
		{
			filesNeedUpdate.insert(fileInfo);
			if (fileInfo.second != 0)
				filesStateInvalid.insert(fileInfo);
		}
	}

	if (filesNeedUpdate.empty())
		return WO_OK;

	std::vector<SMergeFileInfo> mergeTaskInfo;
    GetMergeFileInfo(pMergeSheet, mergeTaskInfo);
	
	if (!filesStateInvalid.empty())
	{
		ks_wstring fileId = filesStateInvalid.begin()->first;
		int serStat = filesStateInvalid.begin()->second;
		SUPBOOK_STAT stat = util::errorCode2SupbookStat(serStat);
		SMergeFileInfo curMergeFileInfo;
		GetSingleSrcInfoByfileId(fileId, mergeTaskInfo, curMergeFileInfo);
		spMergeFile->RecordMFError(E_MERGEFILES_NETFILE_INVALID, fileId, curMergeFileInfo.strDocName, stat);
		return WO_FAIL;
	}

	wo::util::CallTimeStat callTime("DoMergeByIncreament");
	// 更新dataRange中的数据
	ks_stdptr<Workbooks> ptrWorkbooks = NULL;
	m_wwb->GetCoreApp()->get_Workbooks(&ptrWorkbooks);
	ks_stdptr<_Workbook> ptrWorkbookDst;
	int nDstBookIdx = 1;
	ptrWorkbooks->get_Item(KComVariant(nDstBookIdx, VT_I4), &ptrWorkbookDst);
	if (!ptrWorkbookDst) return WO_FAIL;

	std::unique_ptr<KEtRevisionContext> pCtx = CreateContext();
    if (!pCtx) return WO_FAIL;

    const bool isWorkbookDstHidden = pCtx->getProtectionCtx()->isBookHasHiddenProperty(ptrWorkbookDst->GetBook());
	if (isWorkbookDstHidden)
	{
		WOLOG_INFO << "[mergefileAutoupdate] merge failed, E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK";
		spMergeFile->RecordMFError(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK, __X(""), __X(""), 0);
		return WO_FAIL;
	}

	// 减少计算
	ks_stdptr<IBookOp> spBookOp;
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
	util::CalcBatchUpdate calcBatchUpdate(spBookOp);
	
	// 关闭下事务
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(ptrWorkbookDst);
	ks_stdptr<IWorkspace> spWS;
	ks_stdptr<IBook> spBook = ptrWorkbookDst->GetBook();
	spBook->GetWorkspace(&spWS);
	autoupdate_util::DisableTransHlp _disable(spWS);

	InitVariabelState(pMergeSheet);
	wo::CloudImgAttachmentCollector attchmentHelper(pCtx.get());
	bool bMergeSucc = false;
	for (auto updateItem : filesNeedUpdate)
	{
		ks_wstring ItemFileId = updateItem.first;
		QString qFileID = QString::fromUtf16(ItemFileId.c_str());

		SMergeFileInfo curMergeFileInfo;
		GetSingleSrcInfoByfileId(ItemFileId, mergeTaskInfo, curMergeFileInfo);
        NetFileInfo netFileInfo;
        netFileInfo.stat = pCtx->getNetDiskFile(__X("yunwps"), ItemFileId.c_str(),
                                netFileInfo.path, netFileInfo.version, netFileInfo.filename);
		netFileInfo.fileId = ItemFileId;
		SUPBOOK_STAT ItemStat = netFileInfo.stat;

		if (ItemStat != sbsValidFile)
		{			
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 skipped").arg(qFileID).toUtf8());
			spMergeFile->RecordMFError(E_MERGEFILES_NETFILE_INVALID, ItemFileId, curMergeFileInfo.strDocName, ItemStat);
            return WO_FAIL;
		}

		QString qPathItem = QString::fromUtf16(netFileInfo.path.c_str());
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] before OpenFile, fileId %1, path %2").arg(qFileID).arg(qPathItem).toUtf8());
        ks_stdptr<_Workbook> ptrWorkbookSrc = OpenFile(ptrWorkbooks, netFileInfo.path);
		if (ptrWorkbookSrc == NULL)
		{
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 open null").arg(qFileID).toUtf8());
			spMergeFile->RecordMFError(E_MERGEFILES_OPEN_FILE_FAILED, ItemFileId, curMergeFileInfo.strDocName, ItemStat);
			return WO_FAIL;
		}

		HRESULT hrCheckSec = CheckSecurityDoc(ptrWorkbookSrc);
		if(hrCheckSec != S_OK)
		{
			spMergeFile->RecordMFError(E_OPERATION_NOT_SUPPORTED_ON_SECDOC, ItemFileId, curMergeFileInfo.strDocName, ItemStat);
			CloseFile(ptrWorkbookSrc);
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merge failed, file %1 SecurityDoc").arg(qFileID).toUtf8());
			return WO_FAIL;
		}

		const bool isWorkbookSrcHidden = pCtx->getProtectionCtx()->isBookHasHiddenProperty(ptrWorkbookSrc->GetBook());
		if (isWorkbookSrcHidden)
		{
			spMergeFile->RecordMFError(E_MERGEFILES_SOURCEBOOK_WITH_HIDDEN_RANGE, ItemFileId, curMergeFileInfo.strDocName, ItemStat);
			CloseFile(ptrWorkbookSrc);
			WOLOG_INFO << "[mergefileAutoupdate] merge failed, [protection] "  << WOLOG_MK_KV(isWorkbookSrcHidden);
			return WO_FAIL;
		}

		//复制数据
		WOLOG_INFO << "[mergefileAutoupdate] start CopyBookData";
		attchmentHelper.begin(ItemFileId);
		HRESULT hrCopyBookData = CopyBookDataIncr(ptrWorkbookDst, pMergeSheet, ptrWorkbookSrc, curMergeFileInfo);
		attchmentHelper.end();
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
		if (hrCopyBookData != S_OK)
		{
			HRESULT hrErr = hrCopyBookData == E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND ? 
					E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND : E_MERGEFILES_COPYDATA_FAILED;
			spMergeFile->RecordMFError(hrErr, ItemFileId, curMergeFileInfo.strDocName, ItemStat);
			CloseFile(ptrWorkbookSrc);
			WOLOG_INFO << "[mergefileAutoupdate] merge failed, CopyBookData failed "  << hrCopyBookData;
			return WO_FAIL;
		}

        //关闭源文件
		CloseFile(ptrWorkbookSrc);
        bMergeSucc = true;
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_INFO, QString("[mergefileAutoupdate] merged, file %1 done, bMergeSucc: %2").arg(qFileID).arg(bMergeSucc).toUtf8());
    }
    attchmentHelper.filloutAttachmentIds(bMergeSucc);

	// 是否标记数据源
	AddDataSrcFields(pMergeSheet);

	//去重复去空行
	if (m_isRemoveDuplicates)
	{
		RemoveDuplicates(pMergeSheet);
	}
    
    spMergeFile->SetMergeTime(GetCurTime());
	spMergeFile->SetTaskState(false);
	spMergeFile->RecordMFError(S_OK, __X(""), __X(""), 0);
    return WO_OK;
}

bool MergeFileUtil::GetSingleSrcInfoByfileId(const ks_wstring& fileId, const std::vector<SMergeFileInfo>& vecData, SMergeFileInfo& data)
{
	bool bFind = false;
	for (auto item : vecData)
	{
		if (xstricmp(item.strId.c_str(), fileId.c_str()) == 0)
		{
			data = item;
			bFind = true;
			break;
		}
	}
	return bFind;
}

HRESULT MergeFileUtil::ClearDstSheet(_Worksheet* pSheet)
{
	if (pSheet->GetSheet()->IsProtected())
		return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;

	long sheetIdx = 1;
	pSheet->get_Index(0, &sheetIdx);
	sheetIdx--;// based on 1

	RANGE rg(m_wwb->GetBMP());
	rg.SetRowFromTo(0, m_wwb->GetBMP()->cntRows - 1);
	rg.SetColFromTo(0, m_wwb->GetBMP()->cntCols - 1);
	rg.SetSheetFromTo(sheetIdx);

	return m_wwb->CreateRangeObj(rg)->ClearContents();
}

HRESULT MergeFileUtil::CheckSecurityDoc(_Workbook* ptrWorkbookSrc)
{
	bool bSecurity = wo::secdoc::IsSecurityDoc(ptrWorkbookSrc);
	if(!bSecurity)	return S_OK;

	UINT docPermission = 0;
	bool bSuc = wo::secdoc::GetDocPermission(ptrWorkbookSrc, &docPermission);
	if(!bSuc || !wo::secdoc::CanCopy(docPermission))
	{
		return E_OPERATION_NOT_SUPPORTED_ON_SECDOC;
	}
	return S_OK;
}


HRESULT MergeFileUtil::CopyBookData(_Workbook* ptrWorkbookDst, _Worksheet* spSheetDst, _Workbook* ptrWorkbookSrc, bool& bFirstFile, 
						bool isRemoveDuplicates, SMergeFileInfo& curMergeFileInfo)
{
	bool bCopySucc = false;

	IDX idxDstSheet = -1;
	spSheetDst->GetSheet()->GetIndex(&idxDstSheet);

	//获取目标bookop
	ks_stdptr<IBookOp> spDstBookOp;
	ptrWorkbookDst->GetBook()->GetOperator(&spDstBookOp);

	//获取源bookop
	ks_stdptr<IBookOp> spSrcBookOp;
	ptrWorkbookSrc->GetBook()->GetOperator(&spSrcBookOp);

	//获取源sheets
	ks_stdptr<etoldapi::Worksheets> spSheetsSrc = NULL;
	ptrWorkbookSrc->get_Worksheets(&spSheetsSrc);
	if (!spSheetsSrc) return E_FAIL;

	ks_wstring srcDocName = curMergeFileInfo.strDocName;
	BMP_PTR bmpPtr = spSheetDst->GetSheet()->GetBMP();
	int regionRowFrom = m_nCurRow;
	//遍历源sheets
	std::vector<SMergeSrcInfo> vecSrcInfo = curMergeFileInfo.vecSrcInfo;
	long cntSheetSrc = vecSrcInfo.size();
	for (int i = 0; i < cntSheetSrc; i++)
	{
		//获取源sheet
		ks_stdptr<IKCoreObject> spCoreObjSrc = NULL;
		ks_wstring shtName = vecSrcInfo[i].sheetName;
		IDX sheetIdx = alg::STREF_INV_SHEET;
		ks_stdptr<IBook> ptrIBook = ptrWorkbookSrc->GetBook();
		HRESULT hr = ptrIBook->GetSheetIdxByName(shtName.c_str(), &sheetIdx);
		if(FAILED(hr))
		{
			return E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND;
		}

		spSheetsSrc->get_Item(KComVariant(sheetIdx + 1, VT_I4), &spCoreObjSrc);

		ks_stdptr<etoldapi::_Worksheet> spSheetSrc = spCoreObjSrc;
		if (!spSheetSrc) return E_FAIL;

		//隐藏sheet不合并
		BOOL bVisible = FALSE;
		ISheet* pSheet = spSheetSrc->GetSheet();
		pSheet->GetVisible(&bVisible);
		if (!bVisible)
			continue;
		if (pSheet->IsDbSheet() || pSheet->IsAppSheet() || pSheet->IsWorkbenchSheet())
			continue;

		//获取源数据区域
		ks_stdptr<Range> rgDataSrc;
		INT32 rowCnt = getCopyRange(spSheetSrc, isRemoveDuplicates, &rgDataSrc);
		if (rowCnt == 0)
		{
			bCopySucc = true;
			continue;
		}
		if (!rgDataSrc) continue;

		if (m_nCurRow + rowCnt > bmpPtr->cntRows)
			continue;

		// 拷贝列宽 webbug 32363 先复制列宽，再粘贴，粘贴时会自适应行高
		if (bFirstFile)
		{
			bFirstFile = false;
			CopyColWidth(spSheetSrc->GetSheet(), spSheetDst->GetSheet());
		}

		//计算粘贴区域
		RANGE rgDataPaste(bmpPtr);
		IDX iSheet = -1;
		spSheetDst->GetSheet()->GetIndex(&iSheet);
		rgDataPaste.SetCell(iSheet, m_nCurRow, 0);
		KCOMPTR(IRangeInfo) ptrRangeInfo = m_wwb->CreateRangeObj(rgDataPaste);

		//设置复制区域
		IKAppPersist* pAppPersist = m_wwb->GetCoreApp()->GetAppPersist();
		if (NULL == pAppPersist) return E_FAIL;
		pAppPersist->SetCutCopyRange(etCopy, rgDataSrc);

		//粘贴参数
		ks_stdptr<IETPersist> spPersist = pAppPersist->GetPersist();
		if (NULL == spPersist) return E_FAIL;

		range_helper::ranges rgs;
		ptrRangeInfo->GetIRanges(&rgs, TRUE);

		PASTEINFO info = {0};
		{
			info.grbits.fPaste = TRUE;						// 复制
			info.grbits.fRemote = 0;
			info.grbits.fSkipBlanks  = FALSE;
			info.grbits.fTranspose   = FALSE;
			info.grbits.PasteSpecial = (PASTE_SPECIAL)psValNumFmts;
			info.grbits.Operation    = poNone;
			info.grbits.fFillByDest	 = FALSE;
			info.grbits.fHasCells	 = TRUE;
		}

		ks_stdptr<IKRanges> spPasteRgs;
		hr = spPersist->CreatePasteRanges(ptrWorkbookDst->GetBook(), rgs, &info, NULL, NULL, &spPasteRgs);
		if(FAILED(hr))
			return hr;

		//粘贴
		hr = spPersist->WoPaste(NULL, NULL, NULL);
		if(FAILED(hr))
		{
			return E_FAIL;
		}

		ks_stdptr<IKRanges> spRgs;
		spPersist->GetLastPasteRanges(&spRgs, FALSE);
		if (!spRgs)
			return E_FAIL;
		UINT cntRgs = 0;
		spRgs->GetCount(&cntRgs);
		if (cntRgs != 1)
		{
			ASSERT(FALSE);
			return E_FAIL;
		}
		const RANGE* pSlect = NULL;
		spRgs->GetItem(0, NULL, &pSlect);

		// 插入数据源信息 sheetName bookName
		{
			ks_bstr bstrSheetName;
			spSheetSrc->get_Name(&bstrSheetName);

			sourceDataInfo srcdataInfo;
			srcdataInfo.start = m_nCurRow;
			srcdataInfo.cnt = pSlect->RowTo() - m_nCurRow + 1;
			srcdataInfo.srcSheetName = bstrSheetName;
			srcdataInfo.srcBookName = srcDocName;
			m_srcDataInfo.push_back(srcdataInfo);
		}

		m_nCurRow = pSlect->RowTo() + 1;
		
		pAppPersist->SetCutCopyRange(etCopyCutNone, NULL);

		bCopySucc = true;
	}

	// 记录当前文件region
	if (m_nCurRow != regionRowFrom)
	{
		RANGE rgDataRegion(bmpPtr);
		rgDataRegion.SetSheetFromTo(idxDstSheet, idxDstSheet);
		rgDataRegion.SetColFromTo(0, bmpPtr->cntCols - 1); //整行复制
		rgDataRegion.SetRowFromTo(regionRowFrom, m_nCurRow - 1);
		
		ks_stdptr<IMergeFile> spMergeFile;
		spSheetDst->GetSheet()->GetExtDataItem(edSheetMergeFile, reinterpret_cast<IUnknown **>(&spMergeFile));
		if (!spMergeFile)
			return E_FAIL;
				
		if (rgDataRegion.IsValid())
			spMergeFile->RegisterRegion(curMergeFileInfo.strId, rgDataRegion);
	}

	return bCopySucc ? S_OK : E_FAIL;
}

INT32 MergeFileUtil::GetNextRegionPos(IMergeFile* pMergeFile, const ks_wstring& fileId)
{
	if (!pMergeFile) return -1;
	std::vector<SMergeFileInfo> vecData;
    pMergeFile->GetMergeFileInfo(vecData);
    if (vecData.empty()) return -1;

	int target = -1;
	for (int i = 0; i < vecData.size(); i++)
	{
		if (0 == xstricmp(fileId.c_str(), vecData[i].strId.c_str()))
			target = i;
	}

	RANGE temp(m_wwb->GetBMP());
	target--;
	while (target >= 0)
	{
		ks_wstring curFileId = vecData[target].strId;
		if (pMergeFile->GetRegisterRegion(curFileId, temp))
		{
			return temp.RowTo() + 1;
		}
		target--;
	}
	return 0;
}

HRESULT MergeFileUtil::CopyBookDataIncr(_Workbook* ptrWorkbookDst, _Worksheet* spSheetDst, _Workbook* ptrWorkbookSrc, SMergeFileInfo& curMergeFileInfo)
{
	bool bCopySucc = false;

	IDX idxDstSheet = -1;
	spSheetDst->GetSheet()->GetIndex(&idxDstSheet);

	//获取目标bookop
	ks_stdptr<IBookOp> spDstBookOp;
	ptrWorkbookDst->GetBook()->GetOperator(&spDstBookOp);

	//获取源bookop
	ks_stdptr<IBookOp> spSrcBookOp;
	ptrWorkbookSrc->GetBook()->GetOperator(&spSrcBookOp);

	//获取源sheets
	ks_stdptr<etoldapi::Worksheets> spSheetsSrc = NULL;
	ptrWorkbookSrc->get_Worksheets(&spSheetsSrc);
	if (!spSheetsSrc) return E_FAIL;

	ks_stdptr<IMergeFile> spMergeFile;
	spSheetDst->GetSheet()->GetExtDataItem(edSheetMergeFile, reinterpret_cast<IUnknown **>(&spMergeFile));
	if (!spMergeFile) return E_FAIL;

	ks_wstring srcDocName = curMergeFileInfo.strDocName;
	BMP_PTR bmpPtr = spSheetDst->GetSheet()->GetBMP();

	// 判断是否空白book、是否需要跳过标题行
	bool bBlankBook = false;
	bool bNeedRegister = false;
	bool bFirstFile = false;
	INT32 oldRegionStartPos = -1;
	INT32 rowsReserved = -1;
	RANGE curBookOldRg(bmpPtr);
	if (!spMergeFile->GetRegisterRegion(curMergeFileInfo.strId,  curBookOldRg))
	{
		bBlankBook = true;
		bNeedRegister = true;
		oldRegionStartPos = GetNextRegionPos(spMergeFile, curMergeFileInfo.strId);
		if (oldRegionStartPos == -1) return E_FAIL;
		m_nCurRow = oldRegionStartPos;
		WOLOG_INFO << "[CopyBookDataIncr] empty book, curBookOldRg rowFrom: " << m_nCurRow;
	}
	else
	{
		m_nCurRow = curBookOldRg.RowFrom();
		oldRegionStartPos = curBookOldRg.RowFrom();
		WOLOG_INFO << "[CopyBookDataIncr] curBookOldRg rowFrom: " << m_nCurRow << ", rowTo : " << curBookOldRg.RowTo();
	}
	if (m_nCurRow != 0)
		m_hasCopyTitle = true;
	else
	{
		m_hasCopyTitle = false;
		bFirstFile = true;
	}
	rowsReserved = bBlankBook ? 0 : curBookOldRg.Height();		

	//遍历源sheets
	std::vector<SMergeSrcInfo> vecSrcInfo = curMergeFileInfo.vecSrcInfo;
	long cntSheetSrc = vecSrcInfo.size();
	for (int i = 0; i < cntSheetSrc; i++)
	{
		//获取源sheet
		ks_stdptr<IKCoreObject> spCoreObjSrc = NULL;
		ks_wstring shtName = vecSrcInfo[i].sheetName;
		IDX sheetIdx = alg::STREF_INV_SHEET;
		ks_stdptr<IBook> ptrIBook = ptrWorkbookSrc->GetBook();
		HRESULT hr = ptrIBook->GetSheetIdxByName(shtName.c_str(), &sheetIdx);
		if(FAILED(hr))
		{
			return E_MERGEFILES_SOURCEBOOK_SHEETNAME_NOT_FOUND;
		}

		spSheetsSrc->get_Item(KComVariant(sheetIdx + 1, VT_I4), &spCoreObjSrc);

		ks_stdptr<etoldapi::_Worksheet> spSheetSrc = spCoreObjSrc;
		if (!spSheetSrc) return E_FAIL;

		//隐藏sheet不合并
		BOOL bVisible = FALSE;
		ISheet* pSheet = spSheetSrc->GetSheet();
		pSheet->GetVisible(&bVisible);
		if (!bVisible)
			continue;
		if (pSheet->IsDbSheet() || pSheet->IsAppSheet() || pSheet->IsWorkbenchSheet())
			continue;

		ks_stdptr<Range> rgDataSrc;
		INT32 rowCnt = getCopyRange(spSheetSrc, true, &rgDataSrc);
		if (rowCnt == 0)
		{
			bCopySucc = true;
			continue;
		}
		if (!rgDataSrc) continue;

		if (m_nCurRow + rowCnt > bmpPtr->cntRows)
			continue;

		// 处理当前sheet粘贴区域大小，调整下大小，并将数据粘贴进去
		if (rowsReserved  < rowCnt)
		{
			KComVariant var;
			var.AssignDouble(etShiftDown);
			INT rowsToGain = rowCnt - rowsReserved;

			RANGE rgToInsert(bmpPtr);
			rgToInsert.SetSheetFromTo(idxDstSheet, idxDstSheet);
			rgToInsert.SetColFromTo(0, bmpPtr->cntCols - 1);
			if (m_nCurRow + rowsToGain - 1 >= bmpPtr->cntRows)
				continue;
			rgToInsert.SetRowFromTo(m_nCurRow, m_nCurRow + rowsToGain - 1);
			ks_stdptr<etoldapi::Range> range = m_wwb->CreateRangeObj(rgToInsert);
			if (FAILED(range->Insert(var, KComVariant())))
				return E_FAIL;
			if (rowsReserved > 0)
			{
				INT32 temp = rowsReserved - rowCnt;
				rowsReserved = (temp >= 0) ? temp : 0;
			}

			bNeedRegister = true;
		}

		// 清空region内的数据
		RANGE curSrSheetRg(bmpPtr);
		curSrSheetRg.SetSheetFromTo(idxDstSheet, idxDstSheet);
		curSrSheetRg.SetRowFromTo(m_nCurRow, m_nCurRow + rowCnt - 1);
		curSrSheetRg.SetColFromTo(0, bmpPtr->cntCols - 1);
		if (!bBlankBook)
		{
			ks_stdptr<Range> curSheetRgHost = m_wwb->CreateRangeObj(curSrSheetRg);
			if (FAILED(curSheetRgHost->ClearContents())) return E_FAIL;
		}

		// 拷贝列宽 webbug 32363 先复制列宽，再粘贴，粘贴时会自适应行高
		if (bFirstFile)
		{
			bFirstFile = false;
			CopyColWidth(spSheetSrc->GetSheet(), spSheetDst->GetSheet());
		}

		//计算粘贴区域
		RANGE rgDataPaste(bmpPtr);
		IDX iSheet = -1;
		spSheetDst->GetSheet()->GetIndex(&iSheet);
		rgDataPaste.SetCell(iSheet, m_nCurRow, 0);
		KCOMPTR(IRangeInfo) ptrRangeInfo = m_wwb->CreateRangeObj(rgDataPaste);

		//设置复制区域
		IKAppPersist* pAppPersist = m_wwb->GetCoreApp()->GetAppPersist();
		if (NULL == pAppPersist) return E_FAIL;
		pAppPersist->SetCutCopyRange(etCopy, rgDataSrc);

		//粘贴参数
		ks_stdptr<IETPersist> spPersist = pAppPersist->GetPersist();
		if (NULL == spPersist) return E_FAIL;

		range_helper::ranges rgs;
		ptrRangeInfo->GetIRanges(&rgs, TRUE);

		PASTEINFO info = {0};
		{
			info.grbits.fPaste = TRUE;						// 复制
			info.grbits.fRemote = 0;
			info.grbits.fSkipBlanks  = FALSE;
			info.grbits.fTranspose   = FALSE;
			info.grbits.PasteSpecial = (PASTE_SPECIAL)psValNumFmts;
			info.grbits.Operation    = poNone;
			info.grbits.fFillByDest	 = FALSE;
			info.grbits.fHasCells	 = TRUE;
		}

		ks_stdptr<IKRanges> spPasteRgs;
		hr = spPersist->CreatePasteRanges(ptrWorkbookDst->GetBook(), rgs, &info, NULL, NULL, &spPasteRgs);
		if(FAILED(hr))
			return hr;

		//粘贴
		hr = spPersist->WoPaste(NULL, NULL, NULL);
		if(FAILED(hr))
		{
			return E_FAIL;
		}

		ks_stdptr<IKRanges> spRgs;
		spPersist->GetLastPasteRanges(&spRgs, FALSE);
		if (!spRgs)
			return E_FAIL;
		UINT cntRgs = 0;
		spRgs->GetCount(&cntRgs);
		if (cntRgs != 1)
		{
			ASSERT(FALSE);
			return E_FAIL;
		}
		const RANGE* pSlect = NULL;
		spRgs->GetItem(0, NULL, &pSlect);

		// 插入数据源信息 sheetName bookName
		{
			ks_bstr bstrSheetName;
			spSheetSrc->get_Name(&bstrSheetName);

			sourceDataInfo srcdataInfo;
			srcdataInfo.start = m_nCurRow;
			srcdataInfo.cnt = pSlect->RowTo() - m_nCurRow + 1;
			srcdataInfo.srcSheetName = bstrSheetName;
			srcdataInfo.srcBookName = srcDocName;
			m_srcDataInfo.push_back(srcdataInfo);
		}

		m_nCurRow = pSlect->RowTo() + 1;
		
		pAppPersist->SetCutCopyRange(etCopyCutNone, NULL);

		bCopySucc = true;
	}

	// 合并完成后，仍然有区域剩余，需要删除行
	if (m_nCurRow <= curBookOldRg.RowTo())
	{
		KComVariant var;
		var.AssignDouble(etShiftUp);
		RANGE rgToDel(bmpPtr);
		rgToDel.SetSheetFromTo(idxDstSheet, idxDstSheet);
		rgToDel.SetColFromTo(0, bmpPtr->cntCols - 1);
		rgToDel.SetRowFromTo(m_nCurRow, curBookOldRg.RowTo());

		ks_stdptr<etoldapi::Range> range = m_wwb->CreateRangeObj(rgToDel);
		if (FAILED(range->Delete(var)))
			return E_FAIL;
	}
	// 重新注册区域
	if (bNeedRegister)
	{
		RANGE registerRg(bmpPtr);
		registerRg.SetSheetFromTo(idxDstSheet, idxDstSheet);
		registerRg.SetColFromTo(0, bmpPtr->cntCols - 1);
		registerRg.SetRowFromTo(oldRegionStartPos, m_nCurRow - 1);
		spMergeFile->RegisterRegion(curMergeFileInfo.strId, registerRg);
		WOLOG_INFO << "[CopyBookDataIncr] bNeedRegister rowFrom: " << registerRg.RowFrom() << ", rowTo : " << registerRg.RowTo();
	}

	return bCopySucc ? S_OK : E_FAIL;
}

HRESULT MergeFileUtil::CopyAssitSheetData(_Workbook* pWorkbook, _Worksheet* pAssitantSheet, _Worksheet* pDstSheet)
{
	//获取目标bookop
	ks_stdptr<IBookOp> spDstBookOp;
	pWorkbook->GetBook()->GetOperator(&spDstBookOp);

	//获取源sheets
	ks_stdptr<etoldapi::Worksheets> spWorkSheets = NULL;
	pWorkbook->get_Worksheets(&spWorkSheets);
	if (!spWorkSheets) return E_FAIL;

	//获取源数据区域
	BMP_PTR ptrBMP = pAssitantSheet->GetSheet()->GetBMP();
	IDX iSheet = -1;
	pAssitantSheet->GetSheet()->GetIndex(&iSheet);
	RECT rcData = {0};
	pAssitantSheet->GetSheet()->CalcUsedScale(&rcData);
	RANGE rgDataToCopy(ptrBMP);
	rgDataToCopy.SetSheetFromTo(iSheet);
	rgDataToCopy.SetRowFromTo(rcData.top, rcData.bottom);
	rgDataToCopy.SetColFromTo(0, ptrBMP->cntCols - 1); //整行复制

	ks_stdptr<etoldapi::Range> rgDataSrc;
	pAssitantSheet->GetRangeByData(&rgDataToCopy, &rgDataSrc);
	if (!rgDataSrc)
		return E_FAIL;

	//设置复制区域
	IKAppPersist* pAppPersist = m_wwb->GetCoreApp()->GetAppPersist();
	if (NULL == pAppPersist) return E_FAIL;
	pAppPersist->SetCutCopyRange(etCopy, rgDataSrc);

	//计算粘贴区域
	RANGE rgDataPaste(pDstSheet->GetSheet()->GetBMP());
	IDX iDstSheet = -1;
	pDstSheet->GetSheet()->GetIndex(&iDstSheet);
	rgDataPaste.SetCell(iDstSheet, 0, 0);
	KCOMPTR(IRangeInfo) ptrRangeInfo = m_wwb->CreateRangeObj(rgDataPaste);

	range_helper::ranges rgs;
	ptrRangeInfo->GetIRanges(&rgs, TRUE);

	PASTEINFO info = {0};
	{
		info.grbits.fPaste = TRUE;						// 复制
		info.grbits.fRemote = 0;
		info.grbits.fSkipBlanks  = FALSE;
		info.grbits.fTranspose   = FALSE;
		info.grbits.PasteSpecial = (PASTE_SPECIAL)psValNumFmts;
		info.grbits.Operation    = poNone;
		info.grbits.fFillByDest	 = FALSE;
		info.grbits.fHasCells	 = TRUE;
	}
	
	ks_stdptr<IETPersist> spPersist = pAppPersist->GetPersist();
	if (NULL == spPersist) return E_FAIL;

	ks_stdptr<IKRanges> spPasteRgs;
	HRESULT hr = spPersist->CreatePasteRanges(pWorkbook->GetBook(), rgs, &info, NULL, NULL, &spPasteRgs);
	if(FAILED(hr))
		return hr;

	//粘贴
	hr = spPersist->WoPaste(NULL, NULL, NULL);
	if(FAILED(hr))
	{
		return E_FAIL;
	}

	pAppPersist->SetCutCopyRange(etCopyCutNone, NULL);

	CopyColWidth(pAssitantSheet->GetSheet(), pDstSheet->GetSheet());
	return S_OK;
}

INT32 MergeFileUtil::getCopyRange(etoldapi::_Worksheet* pSheetSrc, bool bSkipEmptyRow , etoldapi::Range** ppRgSrc)
{
	INT32 rowCnt = 0;
	ISheet* pSheet = pSheetSrc->GetSheet();
	IDX idxSrcSheet = -1;
	pSheet->GetIndex(&idxSrcSheet);
	RECT rcUsed = {0};
	pSheet->CalcUsedScale(&rcUsed);
	if(m_hasCopyTitle && rcUsed.top < m_titleRowCnt) 
	{
		rcUsed.top = m_titleRowCnt;
		if(rcUsed.top > rcUsed.bottom)
			return 0;
	}
	RANGE rgUsed = Rect2Range(rcUsed, idxSrcSheet, pSheet->GetBMP());
	rgUsed.SetColFromTo(0, pSheet->GetBMP()->cntCols - 1); //整行复制
	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
	HasCellValueAcpt acpt;

	if (bSkipEmptyRow)
	{
		ks_stdptr<IKRanges> spRanges;
		_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRanges);

		RANGE rg(rgUsed);
		ROW firstCopyRowInBlock = -1;
		ROW lastCopyRowInBlock = -1;
		for (ROW row = rgUsed.RowFrom(); row <= rgUsed.RowTo();)
		{
			rg.SetRowFrom(row);
			if (!spSheetEnum->EnumCellValueRowbyRow(rg, &acpt))
			{
				break;
			}

			ASSERT(acpt.m_row >= rg.RowFrom());
			if(acpt.m_row > rg.RowFrom() && firstCopyRowInBlock >= 0) 
			{
				// 当前行从 [row, acpt.m_row - 1] 没有内容。添加一个复制区域
				RANGE rgHasContent(rg);
				rgHasContent.SetRowFromTo(firstCopyRowInBlock, lastCopyRowInBlock);
				spRanges->Append(alg::STREF_THIS_BOOK, rgHasContent);
				lastCopyRowInBlock = firstCopyRowInBlock = acpt.m_row;
				rowCnt += rgHasContent.Height();
			}
			else
			{
				if(firstCopyRowInBlock < 0)
					firstCopyRowInBlock = acpt.m_row;
				lastCopyRowInBlock = acpt.m_row;
			}
			row = acpt.m_row + 1;
		}

		if( firstCopyRowInBlock >= 0) 
		{
			RANGE rgHasContent(rg);
			rgHasContent.SetRowFromTo(firstCopyRowInBlock, lastCopyRowInBlock);
			spRanges->Append(alg::STREF_THIS_BOOK, rgHasContent);
			rowCnt += rgHasContent.Height();
		}

		UINT cnt = 0;
		spRanges->GetCount(&cnt);
		if(cnt > 0)
			pSheetSrc->GetRangeByData(spRanges, ppRgSrc);
	}
	else
	{
		if (spSheetEnum->EnumCellValue(rgUsed, &acpt))
		{
			rowCnt = rgUsed.Height();
			pSheetSrc->GetRangeByData(&rgUsed, ppRgSrc);
		}
	}

	if(rowCnt >= m_titleRowCnt)
		m_hasCopyTitle = true;

	return rowCnt;
}

void MergeFileUtil::CopyColWidth(ISheet* pSheetSrc, ISheet* pSheetDst)
{
    for (COL col = pSheetSrc->GetUsedLeftHdr(), colEnd = std::min(pSheetSrc->GetUsedRightHdr() + 1, pSheetDst->GetBMP()->cntCols); col < colEnd; col++)
	{
		INT iColWidth = 0;
		pSheetSrc->GetColWidth(col, &iColWidth);
		pSheetDst->SetColWidth(col, col, iColWidth);
	}
}

_Workbook* MergeFileUtil::OpenFile(Workbooks* ptrWorkbooks, ks_wstring fileName)
{
	autoupdate_util::EtRevisionCtxResetScope etRevisionCtxScope;
	ks_stdptr<IETPersist> ptrPersist = m_wwb->GetCoreApp()->GetAppPersist()->GetPersist();
	if (!ptrPersist->IsSupportedFormat(fileName.c_str()))
	{
		return NULL;
	}

	ks_bstr SingleFileName(fileName.c_str());
	VARIANT varfn = {0};
	VARIANT vpas = {0};
	VARIANT vModifyPas = { 0 };

	V_VT(&varfn) = VT_BSTR;
	V_VT(&vpas) = VT_BSTR;
	V_BSTR(&varfn) = SingleFileName;
	VARIANT varAddToMru;
	V_VT(&varAddToMru) = VT_BOOL;
	V_BOOL(&varAddToMru) = VARIANT_TRUE;
	VARIANT var;
	V_VT(&var) = VT_BOOL;
	V_BOOL(&var) = VARIANT_TRUE;

	VARIANT varUpdateLinks = {0};
	V_VT(&varUpdateLinks) = VT_BOOL;
	V_BOOL(&varUpdateLinks) = VARIANT_FALSE;

	HRESULT hr = E_FAIL;
	ks_stdptr<_Workbook> ptrWorkbook = NULL;
	hr = ptrWorkbooks->Open(varfn, varUpdateLinks,
		var,	VARIANT(),
		vpas,	vModifyPas,
		VARIANT(),	VARIANT(),
		VARIANT(),	VARIANT(),
		VARIANT(),	VARIANT(),
		varAddToMru,  &ptrWorkbook, FALSE, FALSE);

	return ptrWorkbook;
}

void MergeFileUtil::InitVariabelState()
{
    m_nCurRow = 0;
	m_isMarkSrcData = false;
	m_autoRefresh = false;
	m_isRemoveDuplicates = false;
	m_hasCopyTitle = false;
	m_updateByIncreament = false;
	m_titleRowCnt = 0;
	m_srcDataInfo.clear();
	m_newSheetIdx = -1;
}

void MergeFileUtil::InitVariabelState(_Worksheet* pSheet)
{
	InitVariabelState();

	ks_stdptr<IMergeFile> spMergeFile;
    ks_stdptr<IUnknown> spUnk;
    pSheet->GetSheet()->GetExtDataItem(edSheetMergeFile, &spUnk);
	spMergeFile = spUnk;

	m_titleRowCnt = spMergeFile->GetTitleRowCnt();
	m_autoRefresh = spMergeFile->GetIsAutoRefresh();
	m_isMarkSrcData = spMergeFile->GetIsMarkDataSrc();
	m_isRemoveDuplicates = spMergeFile->GetIsRemoveDuplicates();
	m_updateByIncreament = spMergeFile->CanMergeByIncrementway();
}

HRESULT MergeFileUtil::RemoveDuplicates(_Worksheet* spSheet)
{
	RECT rcData = {0};
	spSheet->GetSheet()->CalcUsedScale(&rcData);

	RANGE rgData(spSheet->GetSheet()->GetBMP());
	IDX iSheet = -1;
	spSheet->GetSheet()->GetIndex(&iSheet);
	rgData.SetSheetFromTo(iSheet);
	rgData.SetRowFromTo(rcData.top, rcData.bottom);
	rgData.SetColFromTo(rcData.left, m_isMarkSrcData ? rcData.right - 2 : rcData.right);
	if (!rgData.IsValid()) return E_FAIL;

	ks_stdptr<Range> ptrRange = m_wwb->CreateRangeObj(rgData);
	if (!ptrRange)
	{
		return E_FAIL;
	}

	/*统计被勾选的列数,除全选外*/
	QList<int> checkColunms;
	for (COL col = 1; col <= rgData.Width(); col++)
	{
		checkColunms.append(col);
	}

	//1 初始化多维数组;
	SAFEARRAYBOUND rgsabound[1];
	rgsabound[0].cElements = checkColunms.count(); //元素个数;
	rgsabound[0].lLbound = 0;//下标;
	SAFEARRAY *pSa = SafeArrayCreate(VT_VARIANT, 1, rgsabound);

	//2 放置元素;
	for (SIZE32 i = 0; i < checkColunms.count(); i++)
	{
		KComVariant val(checkColunms.at(i), VT_I4);
		SafeArrayPutElement(pSa, &i, &val);
	}

	//3 将数组封装成 VARIANT;
	KComVariant vtListArray;
	V_VT(&vtListArray) = VT_ARRAY | VT_VARIANT;
	V_ARRAY(&vtListArray) = pSa;

	if(m_isMarkSrcData)
	{
		RANGE rgSrcFields(spSheet->GetSheet()->GetBMP());
		rgSrcFields.SetSheetFromTo(iSheet);
		rgSrcFields.SetRowFromTo(rcData.top, rcData.bottom);
		rgSrcFields.SetColFromTo(rcData.right - 1, rcData.right);
		std::vector<long> srcFieldCols = {rcData.right - 1, rcData.right};
		std::vector<long> dataCols;
		ks_stdptr<IRemoveDuplicateItems> ptrUniqueTool;
		HRESULT hr = _appcore_CreateObject(CLSID_KRemoveDuplicateItems, IID_IRemoveDuplicateItems, (void**)&ptrUniqueTool);
		if (FAILED(hr) || !ptrUniqueTool)
			return E_INVALIDARG;
		ptrUniqueTool->Initialize(spSheet->GetSheet());

		range_helper::ranges uniqueRgs = range_helper::ranges::create_instance();
		range_helper::ranges repeatRgs = range_helper::ranges::create_instance();
		TrasformSafeArray2Vector(vtListArray, dataCols);
		if ((long)dataCols.size() > rgData.Width())
			return E_INVALIDARG;
		//此刻的数组只是标记选中range的第几列，需要转换成列号
		for (int i = 0; i < (int)dataCols.size(); i++)
		{
			COL col = rgData.ColFrom() + dataCols[i]  - 1;
			if (col > rgData.ColTo() || col < rgData.ColFrom())				//参数不能越界
				return E_INVALIDARG;
			dataCols[i] = col;
		}
		ptrUniqueTool->GetDuplicateUniqueRanges(rgData, &dataCols[0], dataCols.size(), repeatRgs, uniqueRgs);

		KComVariant var;
		var.AssignDouble(etShiftUp);
		for(int idx = repeatRgs.size() -1 ; idx >= 0; idx--)
		{
			RANGE tempRg = *repeatRgs.at(idx).second;
			tempRg.SetColFromTo(rcData.right - 1, rcData.right);
			ks_stdptr<Range> range = m_wwb->CreateRangeObj(tempRg);
			hr = range->Delete(var);
			if(FAILED(hr)) return hr;
		}
	}

	HRESULT hr = ptrRange->RemoveDuplicates(vtListArray, xlNo);
	return hr;
}

HRESULT MergeFileUtil::TrasformSafeArray2Vector(VARIANT arr, std::vector<long>& parray)
{
	parray.clear();
	KSmartParam Param(arr);

	if(Param.IsErrorType())		//api未传columns值
		return S_FALSE;

	long numValue = Param.GetNumberValue(-1);
	if (!Param.IsArrayType() && numValue != -1)
	{
		parray.push_back(numValue);
		return S_OK;
	}

	if (!Param.IsArrayType())
		return E_INVALIDARG;

	SIZE32 LBound = 0;
	SIZE32 UBound = 0;
	SafeArrayGetLBound(V_ARRAY(&arr), 1, &LBound);
	SafeArrayGetUBound(V_ARRAY(&arr), 1, &UBound);
	UINT nDim = SafeArrayGetDim(V_ARRAY(&arr));
	ASSERT(nDim == 1);
	KComVariant VarElement;
	for (SIZE32 i = LBound; i <= UBound; ++i)
	{
		SafeArrayGetElement(V_ARRAY(&arr), &i, &VarElement);
		long lvalue = KSmartParam(VarElement).GetNumberValue(-1);
		if (lvalue == -1)
			return E_INVALIDARG;
		parray.push_back(lvalue);
	}
	return S_OK;
}

HRESULT MergeFileUtil::AddDataSrcFields(_Worksheet* spSheet)
{
	if(!m_isMarkSrcData) return S_OK;
	RECT rcData = {0};
	spSheet->GetSheet()->CalcUsedScale(&rcData);
	IDX iSheet = -1;
	spSheet->GetSheet()->GetIndex(&iSheet);
	BMP_PTR pBmp = spSheet->GetSheet()->GetBMP();
	if(rcData.right >= pBmp->cntCols - 2)
		return S_OK;
	RANGE rgData(pBmp);
	rgData.SetSheetFromTo(iSheet);
	rgData.SetRowFromTo(rcData.top, rcData.bottom);
	rgData.SetColFromTo(rcData.left, rcData.right);

	et_sdptr<ISheetEnum> spSheetEnum;
	spSheet->GetSheet()->CreateEnum(&spSheetEnum);
	HasCellValueAcpt acpt;
	COL lastColHasVale = -1;
	RANGE rgForTemp(rgData);
	for(COL col = rgData.ColTo(); col >= rgData.ColFrom(); col--)
	{
		rgForTemp.SetColFromTo(col, col);
		if (spSheetEnum->EnumCellValueColbyCol(rgForTemp, &acpt))
		{
			lastColHasVale = acpt.m_col;
			break;
		}
	}
	rgData.SetColTo(lastColHasVale);

	COL colBook = rgData.ColTo() + 1;
	COL colSheet = colBook + 1;
	REF_STYLE refStyle = RS_A1;
	for(int i = 0; i < m_srcDataInfo.size(); i++)
	{
		sourceDataInfo info = m_srcDataInfo[i];
		RANGE rgSheet(spSheet->GetSheet()->GetBMP());
		rgSheet.SetSheetFromTo(iSheet);
		rgSheet.SetRowFromTo(info.start, info.start + info.cnt - 1);		
		rgSheet.SetColFromTo(colSheet, colSheet);
		if (!rgSheet.IsValid()) continue;
		RANGE rgBook(rgSheet);
		rgBook.SetColFromTo(colBook, colBook);
		RANGE refSheet(rgSheet), refBook(rgBook);
		refSheet.SetRowFromTo(info.start);
		refBook.SetRowFromTo(info.start);

		ks_stdptr<Range> spSheetRange = m_wwb->CreateRangeObj(rgSheet);
		ks_stdptr<Range> spBookRange = m_wwb->CreateRangeObj(rgBook);
		ks_stdptr<IRangeInfo> hostSheet = spSheetRange;
		ks_stdptr<IRangeInfo> hostBook = spBookRange;
		
		HRESULT hr = hostSheet->SetFormula(info.srcSheetName.c_str(), &refSheet, refStyle);
		if(FAILED(hr)) return hr;
		hr = hostBook->SetFormula(info.srcBookName.c_str(), &refBook, refStyle);
		if(FAILED(hr)) return hr;
	}
	// 添加标题列
	return SetDataSrcColTitle(spSheet, rgData);
}

HRESULT MergeFileUtil::SetDataSrcColTitle(_Worksheet* spSheet, RANGE rg)
{
	if(!m_isMarkSrcData || m_titleRowCnt == 0) return S_OK;

	IDX iSheet = -1;
	spSheet->GetSheet()->GetIndex(&iSheet);
	RANGE rgTemp(spSheet->GetSheet()->GetBMP());
	rgTemp.SetSheetFromTo(iSheet);
	if(m_titleRowCnt == 1)
	{
		// 添加标题列
		COL colBook = rg.ColTo() + 1;
		COL colSheet = rg.ColTo() + 2;
		RANGE rgBook(rgTemp);
		rgBook.SetRowFromTo(0, 0);		
		rgBook.SetColFromTo(colBook, colBook);
		if (!rgBook.IsValid()) return S_FALSE;
		RANGE rgSheet(rgBook);
		rgSheet.SetColFromTo(colSheet, colSheet);
		RANGE refSheet(rgSheet), refBook(rgBook);

		ks_stdptr<Range> spSheetRange = m_wwb->CreateRangeObj(rgSheet);
		ks_stdptr<Range> spBookRange = m_wwb->CreateRangeObj(rgBook);
		ks_stdptr<IRangeInfo> hostSheet = spSheetRange;
		ks_stdptr<IRangeInfo> hostBook = spBookRange;

		REF_STYLE refStyle = RS_A1;
		HRESULT hr = hostSheet->SetFormula(__X("Worksheet"), &refSheet, refStyle);
		if(FAILED(hr)) return hr;
		hr = hostBook->SetFormula(__X("Workbook"), &refBook, refStyle);
		if(FAILED(hr)) return hr;
	}
	else
	{
		RANGE clearRg(rgTemp);
		clearRg.SetRowFromTo(0, m_titleRowCnt - 1);
		clearRg.SetColFromTo(rg.ColTo() + 1, rg.ColTo() + 2);
		ks_stdptr<etoldapi::Range> range_api = m_wwb->CreateRangeObj(clearRg);
		if (!range_api)
			return S_FALSE;
		range_api->ClearContents();
	}
	return S_OK;
}

void MergeFileUtil::DelUseLessSheet(_Workbook* ptrWorkbookDst)
{
	if(m_newSheetIdx < 0)
		return;
	
	IKWorksheet* pSheet = ptrWorkbookDst->GetWorksheets()->GetSheetItem(m_newSheetIdx);
	if (pSheet == NULL)
		return;
	pSheet->DeleteDirectly();
}

HRESULT MergeFileUtil::CreateDstSheet(Worksheets* pSheetsDst, _Worksheet* pMergeSheet, ks_stdptr<etoldapi::_Worksheet> &spSheetDst)
{
	long idxMergeSheet = 0;
	pMergeSheet->get_Index(0, &idxMergeSheet); // based on 1

	//创建目标sheet
	if (spSheetDst == NULL)
	{
		KComVariant vBefore;
		KComVariant vAfter = idxMergeSheet;
		KComVariant vCount;
		KComVariant vType;
		ks_stdptr<IKCoreObject> spCoreObjDst = NULL;
		HRESULT hr = pSheetsDst->Add(vBefore, vAfter, vCount, vType, &spCoreObjDst, stGrid);
		if (FAILED(hr)) return hr;

		spSheetDst = spCoreObjDst;
		if (!spSheetDst) return E_FAIL;
		m_newSheetIdx = idxMergeSheet;
	}

	return S_OK;
}

void MergeFileUtil::BroadcastNow(wo::MsgType name, BinWriter& ww)
{
	BinWriter::StreamHolder bt = ww.buildStream();
	WebSlice slice = {bt.get(), ww.writeLength()};
	gs_callback->broadcast(getMsgTypeName(name), &slice, NULL);
}


} // namespace autoupdate_util
} // namespace wo
