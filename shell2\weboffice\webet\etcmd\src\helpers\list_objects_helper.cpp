#include "list_objects_helper.h"

namespace
{
    bool IsValidUnicode(WCHAR x)
    {
        if (x < 128)
            return false;

        if(x <= 255)
        {
            switch(x)
            {
                case __Xc('￠'): case __Xc('￡'): case __Xc('¤'): case __Xc('￥'): case __Xc('|'): case __Xc('§'):
                case __Xc('¨'): case __Xc('ˉ'): case __Xc('°'): case __Xc('±'): case __Xc('′'): case __Xc('μ'):
                case __Xc('·'): case __Xc('à'): case __Xc('á'): case __Xc('è'): case __Xc('é'): case __Xc('ê'):
                case __Xc('ì'): case __Xc('í'): case __Xc('D'): case __Xc('ò'): case __Xc('ó'): case __Xc('×'):
                case __Xc('ù'): case __Xc('ú'): case __Xc('ü'): case __Xc('÷'):
                    return true;
                default:
                    return false;
            }
        }

        switch(x)
        {
            case __Xc('－'): case __Xc('＝'): case __Xc('＇'): case __Xc('，'): case __Xc('／'): case __Xc('～'):
            case __Xc('！'): case __Xc('＠'): case __Xc('＃'): case __Xc('＄'): case __Xc('％'): case __Xc('＾'):
            case __Xc('＆'): case __Xc('＊'): case __Xc('（'): case __Xc('）'): case __Xc('＋'): case __Xc('｛'):
            case __Xc('｝'): case __Xc('｜'): case __Xc('：'): case __Xc('＜'): case __Xc('＞'): case __Xc('　'):
            case __Xc('；'):
                return false;
            default:
                return true;
        }
    }

    bool IsFullWidthNumberChar(WCHAR x)
    {
        return (x >= __Xc('０') && x <= __Xc('９'));
    }

    bool IsLetterChar(WCHAR x)
    {
        return (x >= __Xc('a') && x <= __Xc('z')) ||
               (x >= __Xc('A') && x <= __Xc('Z'));
    }

    bool IsNumberChar(WCHAR x)
    {
        return (x >= __Xc('0') && x <= __Xc('9'));
    }
}
namespace ListObjectsHelper
{
	// List Objects Helper
	HRESULT GetListObjects(IKWorkbook *workbook, IDX sheetIdx, ListObjects **listObjects)
	{
		if (sheetIdx == INVALIDIDX)
			return E_FAIL;
		ks_stdptr<_Worksheet> spSheet = workbook->GetWorksheets()->GetSheetItem(sheetIdx);

		if (!spSheet)
			return E_INVALIDARG;

		return spSheet->get_ListObjects(listObjects);
	}

	HRESULT GetListObject(IKWorkbook *workbook, IDX sheetIdx, WebStr name, ListObject **listObject)
	{
		ks_stdptr<ListObjects> listObjects;
		HRESULT hr = GetListObjects(workbook, sheetIdx, &listObjects);
		if (FAILED(hr))
			return hr;

		KComVariant var(name);
		return listObjects->get_Item(var, listObject);
	}

	HRESULT GetListObject(IKWorkbook *workbook, IDX sheetIdx, IDX tableIdx, ListObject **listObject)
	{
		ks_stdptr<ListObjects> listObjects;
		HRESULT hr = GetListObjects(workbook, sheetIdx, &listObjects);
		if (FAILED(hr))
			return hr;

		if(tableIdx == INVALIDIDX)
			return E_INVALIDARG;
		KComVariant var(tableIdx);
		return listObjects->get_Item(var, listObject);
	}

    // copy from office/et/appcore/listobject/listobject.cpp
    bool IsValidChar(WCHAR x)
    {
        return (IsValidUnicode(x) ||
                IsLetterChar(x) ||
                IsNumberChar(x) ||
                x == __Xc('_') || x == __Xc('?') || x == __Xc('.') ||
                x == __Xc('￥') || x == __Xc('\\')
        );
    }

    bool IsValidFristChar(WCHAR x)
    {
        return ((IsValidUnicode(x) && !IsFullWidthNumberChar(x) && x != __Xc('？') && x != __Xc('．'))
                || IsLetterChar(x) || x == __Xc('_'));
    }
}