#ifndef __WEBET_ET_TASK_EXECUTOR_H__
#define __WEBET_ET_TASK_EXECUTOR_H__
#include <public_header/revision/src/kwrevisionctrl.h>
#include "kfc/tools/smart_wrap.h"

#define BLS_INVALID -1

namespace wo
{
using namespace binary_wo;
class KEtWorkbook;
class KEtRevisionContext;
class EtTaskExecBase;

enum class DenyType
{
	NoDeny,
	EverDeny,
};

class EtTaskExecutor: public TaskExecutor
{
public:
	typedef std::unordered_map<						\
		ks_wstring, std::shared_ptr<EtTaskExecBase>,	\
		kfc::tools::str_hash_ic<ks_wstring>,		\
		kfc::tools::str_equal_ic<ks_wstring>> ExecMap;

	typedef std::unordered_set<ks_wstring,	\
		kfc::tools::str_hash_ic<ks_wstring>,	\
		kfc::tools::str_equal_ic<ks_wstring>> CmdNameSet;

	public:
	EtTaskExecutor(wo::KEtWorkbook*);
	~EtTaskExecutor();

	void Init();
protected:
	void initEtExecMap();
	void initDbExecMap();
	void initOverSizeWhiteCmdSet();

	KwTask::iterator eraseFailedCmd(KwTask* task, KwTask::iterator failedIter, IRevisionContext* ctx, HRESULT hr);
public:
	virtual WebInt Exec(
		KwTask*, IRevisionContext* ctx, KwVersionManager* verions, KwVersion::BanUndoType* banUndo, IUnknown** ppCustomVerInfo) override;
	virtual WebInt MakeEmptyTransact(IRevisionContext* ctx) override;
	virtual bool SerialCommand(KwCommand*, ISerialAcceptor*) override;

	bool IsChangeDisabled() const;
	bool IsDirty() const;
	void SetDirty(bool);
	DenyType Deny() const;
	void ClearDeny();
	bool IsCmdAllowed(const ks_wstring& cmdName);

	binary_wo::VarObj GetResForOthers() { return getRes(m_resForOthersArr); }
	binary_wo::VarObj GetResForSelf() { return getRes(m_resForSelfArr); }
	void ResetResForOthers();
	void ResetResForSelf();
	binary_wo::VarObj GainResItemForSelf();
	binary_wo::VarObj GainResItemForOthers();

protected:
	template <typename ExecClass_>
	void mapAddExecClass();
	static binary_wo::VarObj gainResItem(binary_wo::VarObj& arr);
	static bool isNeedReset(binary_wo::VarObjRoot& objRoot, binary_wo::VarObj& arr);
	static binary_wo::VarObj getRes(binary_wo::VarObj& arr);

protected:
	wo::KEtWorkbook* m_wwb;
	KEtRevisionContext* m_ctx;
	bool m_bDirty;
	bool m_bChangeDisabled;
	DenyType m_denyType;
	ExecMap m_execMap;
	CmdNameSet m_overSizeWhiteCmdSet;
	VarObjRoot m_resForOthers;
	VarObj m_resForOthersArr;
	VarObjRoot m_resForSelf;
	VarObj m_resForSelfArr;
};

}

#endif //__WEBET_ET_TASK_EXECUTOR_H__
