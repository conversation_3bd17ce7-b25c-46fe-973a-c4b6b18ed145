﻿#ifndef __WEBET_WORKBOOK_IMPORTER_H__
#define __WEBET_WORKBOOK_IMPORTER_H__

interface IDBProtectionJudgement;
namespace wo
{

class WorkbookImporter
{
public:
    virtual ~WorkbookImporter() = default;
    HRESULT exec();
    void setMaxImportSheetCount(int maxImportSheetCount);
    void setFallBackSyncLookupFlag(bool b) {this->m_fallBackLookupField = b;}
    bool isExceededMaxImportSheetCount() const;
    bool isExceededMaxDbDashboardCount() const;
    UINT getFirstImportSheetId() const;
    void rollback();
    virtual void SerialAppSharedInfo(binary_wo::VarObj* pObj) {}
protected:
    WorkbookImporter(etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook, PCWSTR userId, binary_wo::VarObj& param);
private:
    virtual HRESULT copySheet(etoldapi::_Worksheet* pSrcWorkSheet, VARIANT before, VARIANT after,
                              IKCoreObject** ppNewSheetObj)
    {
        return pSrcWorkSheet->Copy(before, after, ppNewSheetObj);
    }

    virtual HRESULT onBeforeImport()
    {
        return S_OK;
    }

    virtual HRESULT onAfterImportOneSheet(etoldapi::_Worksheet* pSrcWorkSheet,
                                          etoldapi::_Worksheet* pNewWorkSheet)
    {
        return S_OK;
    }

    virtual HRESULT onAfterImport(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                                  const std::vector<etoldapi::_Worksheet*>& newWorkSheets)
    {
        return S_OK;
    }

protected:
    etoldapi::_Workbook* m_pSrcWorkbook;
    etoldapi::_Workbook* m_pTarWorkbook;
    std::vector<etoldapi::_Worksheet*> m_srcWorkSheets;
    std::vector<etoldapi::_Worksheet*> m_newWorkSheets;
    binary_wo::VarObj& m_param;
    bool m_fallBackLookupField {};
private:
    int m_maxImportSheetCount{0};
    bool m_exceededMaxImportSheetCount{false};
    bool m_exceededMaxDbDashboardCount{false};
    UINT m_firstImportSheetId{0};
    PCWSTR m_userId;
};


} // namespace wo

#endif
