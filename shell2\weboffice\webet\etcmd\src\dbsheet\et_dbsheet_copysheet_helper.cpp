﻿#include "etstdafx.h"
#include "et_dbsheet_copysheet_helper.h"
#include "et_dbsheet_utils.h"
#include "util.h"
#include "kfc/et_numfmt_str.h"
#include "db/db_basic_itf.h"

namespace
{

struct CustomCtxController
{
	CustomCtxController(IBook* pBook1, IBook* pBook2)
	{
		ASSERT(pBook1 && pBook2);
		ks_stdptr<IUnknown> spUnknown1;
		VS(pBook1->GetExtDataItem(edBookDbBookCtx, &spUnknown1));
		m_spDbBookCtx1 = spUnknown1;
		if (pBook1 != pBook2)
		{
			ks_stdptr<IUnknown> spUnknown2;
			VS(pBook2->GetExtDataItem(edBookDbBookCtx, &spUnknown2));
			m_spDbBookCtx2 = spUnknown2;
		}
		if (m_spDbBookCtx1)
			m_spDbBookCtx1->GetViewCustomCtx()->SetCurCommon();
		if (m_spDbBookCtx2)
			m_spDbBookCtx2->GetViewCustomCtx()->SetCurCommon();
	}
	~CustomCtxController()
	{
		if (m_spDbBookCtx1)
			m_spDbBookCtx1->GetViewCustomCtx()->SetCurAuto();
		if (m_spDbBookCtx2)
			m_spDbBookCtx2->GetViewCustomCtx()->SetCurAuto();
	}
	ks_stdptr<IDbtBookCtx> m_spDbBookCtx1;
	ks_stdptr<IDbtBookCtx> m_spDbBookCtx2;
};
// TODO: 整理代码

std::unique_ptr<IImportSheetIdMap> GetImportSheetIdMap(const std::unordered_map<UINT, UINT>* pSheetIdMap)
{
    class KImportSheetIdMap : public IImportSheetIdMap
    {
    public:
        explicit KImportSheetIdMap(const std::unordered_map<UINT, UINT>* pSheetIdMap) : m_pSheetIdMap(pSheetIdMap) {}
        STDPROC_(UINT) GetNewSheetId(UINT sheetId) const override
        {
            auto it = m_pSheetIdMap->find(sheetId);
            return it == m_pSheetIdMap->end() ? 0 : it->second;

        }
    private:
        const std::unordered_map<UINT, UINT>* m_pSheetIdMap;
    };
    return pSheetIdMap ? std::make_unique<KImportSheetIdMap>(pSheetIdMap) : nullptr;
}

} // namespace

namespace wo
{
namespace DBSyncUtil
{
void encodeToBase64(QString& str)
{
	const QRegularExpression reBase64("{base64:.*?}");
	constexpr int leftPos = 8;
	constexpr int holderLen = 9;
	QRegularExpressionMatchIterator i = reBase64.globalMatch(str);
	while (i.hasNext())
	{
		QRegularExpressionMatch match = i.next();
		QString originStr = match.captured();
		QString posStr = originStr.mid(leftPos, originStr.size() - holderLen);
		QByteArray byte = posStr.toUtf8().toBase64();
		str.replace(originStr, QString(byte.data()));
	}
}

EtDbId decodeRecordId(PCWSTR str)
{
	QString urlStr = krt::fromUtf16(str);
	int idx = urlStr.indexOf("?R=");
	if (idx == -1)
		return INV_EtDbId;
	QString posInfo = urlStr.mid(idx + 3);
	QByteArray byte = QByteArray::fromBase64(posInfo.toUtf8());
	QString decodedStr(byte.data());
	idx = decodedStr.lastIndexOf("/");
	if (idx == -1)
		return INV_EtDbId;
	QString idStr = decodedStr.mid(idx + 1);
	EtDbId id = INV_EtDbId;
	_appcore_GainDbSheetContext()->DecodeEtDbId(krt::utf16(idStr), &id);
	return id;
}
} // namespace DBSyncUtil

HRESULT GetNewAttachmentToken(const wo::DbSheetCopyParam::AttachmentIdMap* pAttachmentIdMap, const_token_ptr pToken, IDbTokenArrayHandle** ppTokenArray)
{
	if (!pToken || !ppTokenArray)
		return E_FAIL;
	ASSERT(pAttachmentIdMap);
	if (!alg::const_handle_token_assist::is_type(pToken))
		return E_FAIL;
	alg::const_handle_token_assist chta(pToken);
	alg::TOKEN_HANDLE handle = chta.get_handle();
	if (!handle)
		return E_FAIL;
	ks_stdptr<IDbTokenArrayHandle> spTokenArray = handle->CastUnknown();
	ASSERT(spTokenArray && spTokenArray->GetType() == DBTAT_Attachment);
	ks_stdptr<IDbTokenArrayHandle> spNewTokenArray;
	_db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle, (void**)&spNewTokenArray);
	for (UINT i = 0, attachmentCnt = spTokenArray->GetCount(); i < attachmentCnt; ++i)
	{
		const_token_ptr pItem = nullptr;
		spTokenArray->Item(i, &pItem);
		if (!alg::const_handle_token_assist::is_type(pItem))
			return E_FAIL;
		alg::const_handle_token_assist chta(pItem);
		alg::TOKEN_HANDLE handle = chta.get_handle();
		if (!handle)
			return E_FAIL;
		ks_stdptr<IDbAttachmentHandle> spAttachmentHandle = handle->CastUnknown();
		ASSERT(spAttachmentHandle);
		PCWSTR fileId = spAttachmentHandle->GetFileId();
		auto itFileId = pAttachmentIdMap->find(GlobalSharedString(fileId));
		alg::managed_handle_token_assist assist;
		/*
			对于附件，区分两种：
				1. 文档型的，服务端会分配一个新的映射id
				2. 图片和其它文件，服务端会使用原先的附件
			服务端返回新的附件的映射列表时，会过滤掉附件id保持不变且复制成功的类型
			所以这里附件中不在映射列表的直接保留；失败的为空值；其余使用对应的映射后的 id 替换即可
		*/
		if (itFileId == pAttachmentIdMap->end())
		{
			assist.create(alg::ET_HANDLE_DBATTACHMENT, spAttachmentHandle);
			spNewTokenArray->Add(assist.detach());
			continue;
		}
		PCWSTR newFileId = itFileId->second.c_str();
		if (!newFileId)
			return E_FAIL;
		ks_stdptr<IDbAttachmentHandle> spNewAttachmentHandle;
		_db_CreateObject(CLSID_KDbAttachmentHandle, IID_IDbAttachmentHandle, (void**)&spNewAttachmentHandle);
		spNewAttachmentHandle->Init(newFileId,
			spAttachmentHandle->GetSource(), spAttachmentHandle->GetContentType(),
			spAttachmentHandle->GetName(), spAttachmentHandle->GetSize(),
			spAttachmentHandle->GetLinkUrl(), spAttachmentHandle->GetImgSize());
		assist.create(alg::ET_HANDLE_DBATTACHMENT, spNewAttachmentHandle);
		spNewTokenArray->Add(assist.detach());
	}
	*ppTokenArray = spNewTokenArray.detach();
	return S_OK;
}

HRESULT GetNewNoteToken(const wo::DbSheetCopyParam::AttachmentIdMap* pAttachmentIdMap, const_token_ptr pToken, IDbNoteHandle** ppNoteHandle)
{
	if (!pToken || !ppNoteHandle)
		return E_FAIL;
	ASSERT(pAttachmentIdMap);
	if (!alg::const_handle_token_assist::is_type(pToken))
		return E_FAIL;
	alg::const_handle_token_assist chta(pToken);
	alg::TOKEN_HANDLE handle = chta.get_handle();
	if (!handle)
		return E_FAIL;
	ks_stdptr<IDbNoteHandle> spNoteHandle = handle->CastUnknown();
	ASSERT(spNoteHandle);
	PCWSTR noteId = spNoteHandle->GetFileId();
	ks_stdptr<IDbNoteHandle> spNewNoteHandle;
	_db_CreateObject(CLSID_KDbNoteHandle, IID_IDbNoteHandle, (void**)&spNewNoteHandle);
	auto itNoteId = pAttachmentIdMap->find(GlobalSharedString(noteId));
	if (itNoteId == pAttachmentIdMap->end())
		return E_FAIL;
	PCWSTR newNoteId = itNoteId->second.c_str();
	if (!newNoteId)
		return E_FAIL;
	spNewNoteHandle->Init4IO(itNoteId->second.c_str(), spNoteHandle->GetSummary(), spNoteHandle->GetModifyDate());

	*ppNoteHandle = spNewNoteHandle.detach();
	return S_OK;
}

HRESULT UpdateAttachmentByMapping(EtDbId fldId, IDBSheetOp* pDBSheetOp, const DbSheetCopyParam::AttachmentIdMap* pAttachmentIdMap, bool& bLostInfo)
{
	if (!pAttachmentIdMap || !pDBSheetOp)
		return E_INVALIDARG;
	IDbFieldsManager* pFieldsMgr = pDBSheetOp->GetFieldsManager();
	ks_stdptr<IDbField> spField;
	HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
	if (FAILED(hr))
		return hr;
	if (spField->GetType() != Et_DbSheetField_Attachment)
		return E_INVALIDARG;
	const IDBIds* records = pDBSheetOp->GetAllRecords();
	for (EtDbIdx i = 0, recCnt = records->Count(); i < recCnt; ++i)
	{
		EtDbId recId = records->IdAt(i);
		const_token_ptr pToken = nullptr;
		VS(pDBSheetOp->GetValueToken(recId, fldId, &pToken));
		if (!pToken)
			continue;
		ks_stdptr<IDbTokenArrayHandle> spTokenArray;
		hr = GetNewAttachmentToken(pAttachmentIdMap, pToken, &spTokenArray);
		if (FAILED(hr))
		{
			VS(pDBSheetOp->SetTokenValue(recId, fldId, nullptr));
			bLostInfo = true;
			continue;
		}
		alg::managed_handle_token_assist tokenAssist;
		tokenAssist.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
		VS(pDBSheetOp->SetTokenValue(recId, fldId, tokenAssist));
	}
	return S_OK;
}

HRESULT UpdateNoteByMapping(EtDbId fldId, IDBSheetOp* pDBSheetOp, const DbSheetCopyParam::AttachmentIdMap* pAttachmentIdMap, bool& bLostInfo)
{
	if (!pAttachmentIdMap || !pDBSheetOp)
		return E_INVALIDARG;
	IDbFieldsManager* pFieldsMgr = pDBSheetOp->GetFieldsManager();
	ks_stdptr<IDbField> spField;
	HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
	if (FAILED(hr))
		return hr;
	if (spField->GetType() != Et_DbSheetField_Note)
		return E_INVALIDARG;
	const IDBIds* records = pDBSheetOp->GetAllRecords();
	for (EtDbIdx i = 0, recCnt = records->Count(); i < recCnt; ++i)
	{
		EtDbId recId = records->IdAt(i);
		const_token_ptr pToken = nullptr;
		VS(pDBSheetOp->GetValueToken(recId, fldId, &pToken));
		if (!pToken)
			continue;
		ks_stdptr<IDbNoteHandle> spNoteHandle;
		hr = GetNewNoteToken(pAttachmentIdMap, pToken, &spNoteHandle);
		if (FAILED(hr))
		{
			VS(pDBSheetOp->SetTokenValue(recId, fldId, nullptr));
			bLostInfo = true;
			continue;
		}
		alg::managed_handle_token_assist tokenAssist;
		tokenAssist.create(alg::ET_HANDLE_DBNOTE, spNoteHandle);
		VS(pDBSheetOp->SetTokenValue(recId, fldId, tokenAssist));
	}
	return S_OK;
}

////////////////////////////////////////////////////////////////////////////////
CopyDBSheetDealCrossSheetDependenceHelper::CopyDBSheetDealCrossSheetDependenceHelper()
{

}

HRESULT CopyDBSheetDealCrossSheetDependenceHelper::AddCopyDBSheet(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet)
{
	m_dbMap.insert({pSrcWorkSheet, pTarWorkSheet});
	return S_OK;
}

HRESULT CopyDBSheetDealCrossSheetDependenceHelper::DealCrossSheetDependence(const std::unordered_map<UINT, UINT>* pStIdMap)
{
	HRESULT hr = S_OK;
	for(auto itr = m_dbMap.begin(); itr != m_dbMap.end(); ++itr)
	{
		etoldapi::_Worksheet* pTarWorkSheet = itr->second;
		ks_stdptr<IDBSheetOp> spTarDbSheetOp;
		VS(DbSheet::GetDBSheetOp(pTarWorkSheet->GetSheet(), &spTarDbSheetOp));

		IDbFieldsManager* pFieldsMgr = spTarDbSheetOp->GetFieldsManager();
		const IDBIds* pIds = spTarDbSheetOp->GetAllFields();
		EtDbIdx cnt = pIds->Count();
		for (EtDbIdx i = 0; i < cnt; ++i)
		{
			EtDbId id = pIds->IdAt(i);
			ks_stdptr<IDbField> spField;
			pFieldsMgr->GetField(id, &spField);
			ET_DbSheet_FieldType type = spField->GetType();

			switch(type)
			{
				case Et_DbSheetField_Link:
				{
					ks_stdptr<IDbField_Link> spFieldLink = spField;
					if (not spFieldLink->IsAutoLink())
					{
						// 创建副本后, 如果字段没有被清空, 则枚举单元格, 并将公式内的关联sheetStId更新
						DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TrySetValueAndPassState);
						std::unique_ptr<DbSheet::DbLinkHlp> upDbLinkHlp;
						// 如果是文件内创建副本, 则在事务内维护双向关联; 如果是导入文件, 则不需要维护双向关联 (数据是直接导入的, 用不着节外生枝, 除非
						// 产品要求导入单向关联时再拓展为双向关联. P.S. 如果产品真的提了这个需求记得反对一下, 有说法称我们强制的双向关联在使用上并不方便,
						// 可能之后还会允许单独创建单向关联)
						if (!pStIdMap)
							upDbLinkHlp = std::make_unique<DbSheet::DbLinkHlp>(pTarWorkSheet->GetSheet()->LeakBook(), dbLinkParam);
						
						const IDBIds* records = spTarDbSheetOp->GetAllRecords();
						EtDbId fldId = spField->GetID();
						UINT linkedSheetId = spFieldLink->GetLinkSheet();
						for (EtDbIdx i = 0, c = records->Count(); i < c; ++i)
						{
							EtDbId recId = records->IdAt(i);
							ks_stdptr<IFormula> spFormula;
							VS(spTarDbSheetOp->GetFormula(recId, fldId, &spFormula));
							if (nullptr == spFormula)
								continue;
							const_token_vector ctv;
							spFormula->GetContent(nullptr, &ctv, nullptr);
							if (nullptr == ctv)
								continue;
							exec_token_vector etv(exec_token_vector(ctv).clone());
							alg::managed_vint_token_assist mvta;
							mvta.create(linkedSheetId);
							etv.set_manage(0, mvta.detach());
							spFormula->SetFormulaContent(etv);
							VS(spTarDbSheetOp->SetFormula(recId, fldId, nullptr));
							VS(spTarDbSheetOp->SetFormula(recId, fldId, spFormula.get()));
						}
					}
					break;
				}
				case Et_DbSheetField_OneWayLink:
				{
					ks_stdptr<IDbField_Link> spFieldLink = spField;
					if (not spFieldLink->IsAutoLink())
					{
						// 更新关联公式
						const IDBIds* records = spTarDbSheetOp->GetAllRecords();
						EtDbId fldId = spField->GetID();
						UINT linkedSheetId = spFieldLink->GetLinkSheet();
						for (EtDbIdx i = 0, c = records->Count(); i < c; ++i)
						{
							EtDbId recId = records->IdAt(i);
							ks_stdptr<IFormula> spFormula;
							VS(spTarDbSheetOp->GetFormula(recId, fldId, &spFormula));
							if (nullptr == spFormula)
								continue;

							const_token_vector ctv;
							spFormula->GetContent(nullptr, &ctv, nullptr);
							if (nullptr == ctv)
								continue;
							exec_token_vector etv(exec_token_vector(ctv).clone());
							alg::managed_vint_token_assist mvta;
							mvta.create(linkedSheetId);
							etv.set_manage(0, mvta.detach());
							spFormula->SetFormulaContent(etv);
							VS(spTarDbSheetOp->SetFormula(recId, fldId, nullptr));
							VS(spTarDbSheetOp->SetFormula(recId, fldId, spFormula.get()));
						}
					}
					break;
				}
				default:
					break;
			}
		}
	}
	return hr;
}

/////////////////////////////////////////////////////////////////////////////////
CopyDBSheetHelper::CopyDBSheetHelper(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet,
	CopyDBSheetDealCrossSheetDependenceHelper* pDependenceHelper, bool bNewLookupConvert)
	: m_pSrcWorkSheet(pSrcWorkSheet)
	, m_pTarWorkSheet(pTarWorkSheet)
	, m_pSrcSheet(m_pSrcWorkSheet->GetSheet())
	, m_pTarSheet(m_pTarWorkSheet->GetSheet())
	, m_pSrcBook(m_pSrcSheet->LeakBook())
	, m_pTarBook(m_pTarSheet->LeakBook())
	, m_bNewLookupConvert(bNewLookupConvert)
{
	VS(DbSheet::GetDBSheetOp(m_pSrcSheet, &m_spSrcDbSheetOp));
	VS(DbSheet::GetDBSheetOp(m_pTarSheet, &m_spTarDbSheetOp));
	
	if(pDependenceHelper)
		pDependenceHelper->AddCopyDBSheet(pSrcWorkSheet, pTarWorkSheet);
}

HRESULT CopyDBSheetHelper::Init(const DbSheetCopyParam& param)
{
	m_param = param;
	return S_OK;
}

HRESULT CopyDBSheetHelper::ExecCopy()
{
	CustomCtxController customCtxController(m_pSrcBook, m_pTarBook);
	HRESULT hr = ResetTableName();
	if (FAILED(hr))
		return hr;
	hr = CopyBookCtxId();
	if (FAILED(hr))
		return hr;
	hr = CopyDbSheetData();
	if (FAILED(hr))
		return hr;
	hr = CopyDbSheetViews();
	if (FAILED(hr))
		return hr;
	hr = CopySheetProperty();
	if (FAILED(hr))
		return hr;
	hr = this->postProcess();
	if (FAILED(hr))
		return hr;
	return S_OK;
}

HRESULT CopyDBSheetHelper::ResetTableName() const
{
	HRESULT hr = S_OK;
	ks_bstr sheetName;
	VS(m_pTarWorkSheet->get_Name(&sheetName));
	ks_stdptr<IUnknown> spUnknown;
	m_pTarSheet->GetExtDataItem(edSheetListObjects, &spUnknown);
	ks_stdptr<ICoreListObjects> spLists = spUnknown;
	ks_stdptr<ICoreListObject> spList;
	if (spLists)
		spLists->GetItem(0, &spList);
	if (spList)
	{
		hr = spList->SetDisplayName(sheetName.c_str(), TRUE);
		if (FAILED(hr))
			return hr;
		PCWSTR pwszDisplayName = NULL;
		spList->GetDisplayName(&pwszDisplayName);
		hr = spList->SetName(pwszDisplayName, FALSE);
		if (FAILED(hr))
			return hr;
	}
	return hr;
}

HRESULT CopyDBSheetHelper::CopyBookCtxId() const
{
	std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
	// 于不同book之间拷贝sheet时, 受限于当前id分配, sheet副本创建及dbt关联公式的实现, 暂时需要原样复制
	// 源sheet的记录/字段id, 此时需要在目标book中更新id分配器, 否则目标book中id分配器所分配的id可能小于被拷贝进来的id
	if (m_pSrcBook == m_pTarBook)
		return S_OK;

	ks_stdptr<IUnknown> spUnknown1;
	VS(m_pSrcBook->GetExtDataItem(edBookDbBookCtx, &spUnknown1));
	ks_stdptr<IDbtBookCtx> spSrcDbtBookCtx = spUnknown1;
	ks_stdptr<IUnknown> spUnknown2;
	VS(m_pTarBook->GetExtDataItem(edBookDbBookCtx, &spUnknown2));
	ks_stdptr<IDbtBookCtx> spTarDbtBookCtx = spUnknown2;
	ASSERT(spTarDbtBookCtx != spSrcDbtBookCtx);

	EtDbId srcViewId = MIN_EtDbId;
	EtDbId srcFieldId = MIN_EtDbId;
	EtDbId srcRecordId = MIN_EtDbId;
	EtDbId srcCustomId = MIN_EtDbId;
	EtDbId srcAppId = MIN_EtDbId;
	spSrcDbtBookCtx->PeekNextId(&srcViewId, &srcFieldId, &srcRecordId, &srcCustomId, &srcAppId);
	EtDbId dstViewId = MIN_EtDbId;
	EtDbId dstFieldId = MIN_EtDbId;
	EtDbId dstRecordId = MIN_EtDbId;
	EtDbId dstCustomId = MIN_EtDbId;
	EtDbId dstAppId = MIN_EtDbId;
	spTarDbtBookCtx->PeekNextId(&dstViewId, &dstFieldId, &dstRecordId, &dstCustomId, &dstAppId);

	spTarDbtBookCtx->SetNextId(
		std::max(srcViewId, dstViewId),
		std::max(srcFieldId, dstFieldId),
		std::max(srcRecordId, dstRecordId),
		std::max(srcCustomId, dstCustomId),
		std::max(srcAppId, dstAppId));
	return S_OK;
}

HRESULT CopyDBSheetHelper::CopyDbSheetData()
{
	DbSheet::DisableDbTrackHistoryScope scope;

	VS(m_spTarDbSheetOp->SetSheetDescription(m_spSrcDbSheetOp->GetSheetDescription()));
	VS(m_spTarDbSheetOp->SetSheetIcon(m_spSrcDbSheetOp->GetSheetIcon()));
	HRESULT hr = S_OK;
	// 调整record/field的复制先后顺序. record先复制并在recordManager里应用当前编辑者的信息, 使field的自动字段可应用新的值.
	if (m_param.copyContent)
		hr = CopyDbSheetRecords();
	else
		hr = m_spTarDbSheetOp->InitRecords(m_param.recordCnt);
	if (FAILED(hr))
		return hr;
	hr = CopyDbSheetFields();
	if (FAILED(hr))
		return hr;
	if (m_param.copyContent)
	{
		if (m_param.resetModifiedInfoByCurUser)
			hr = SetDbSheetRecordModifiedInfo();
		else
			hr = CopyDbSheetRecordModifiedInfo();
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

HRESULT CopyDBSheetHelper::CopyDbSheetViews() const
{
	ks_stdptr<IDBSheetViews> spSrcDbSheetViews;
	ks_stdptr<IDBSheetViews> spTarDbSheetViews;
	VS(DbSheet::GetDBSheetViews(m_pSrcSheet, &spSrcDbSheetViews));
	if (!spSrcDbSheetViews)
		return S_FALSE;
	VS(DbSheet::GetDBSheetViews(m_pTarSheet, &spTarDbSheetViews));
	HRESULT hr = spSrcDbSheetViews->CopyTo(spTarDbSheetViews, TRUE, m_param.copyContent, m_param.copyFieldTitleFormat);
	if (FAILED(hr))
		return hr;

	// 当目标是db文件时，需要给没有views的文件加上iew
	if (m_pTarBook->GetBMP()->bDbSheet)
	{
		if (spTarDbSheetViews->GetSize(Et_DBSheetViewUse_ForDb) == 0 || m_param.copyDefaultView)
		{
			PCWSTR sheetName = __X("");
			m_pTarSheet->GetName(&sheetName);
			ks_stdptr<IDBSheetView> spNewDbSheetView;
			hr = spTarDbSheetViews->CreateView(et_DBSheetView_Grid, sheetName, false, Et_DBSheetViewUse_ForDb, &spNewDbSheetView);
			if (FAILED(hr))
				return hr;

			hr = spSrcDbSheetViews->GetDefaultView()->CopyTo(spNewDbSheetView, m_param.copyContent, m_param.copyFieldTitleFormat);
			if (FAILED(hr))
            	return hr;

			spNewDbSheetView->SetUseType(Et_DBSheetViewUse_ForDb);
		}
	}
	return S_OK;
}

HRESULT CopyDBSheetHelper::CopySheetProperty()
{
	m_pTarSheet->SetRowTopPadding(m_pSrcSheet->GetRowTopPadding());
	m_pTarSheet->SetRowBottomPadding(m_pSrcSheet->GetRowBottomPadding());
	m_pTarSheet->SetColLeftPadding(m_pSrcSheet->GetColLeftPadding());
	m_pTarSheet->SetColRightPadding(m_pSrcSheet->GetColRightPadding());
	return S_OK;
}

HRESULT CopyDBSheetHelper::postProcess()
{
	this->postProcess_statisticSheet();
	return S_OK;
}

void CopyDBSheetHelper::postProcess_statisticSheet()
{
	 auto isZeroAsBlank = [](ET_DbSheet_FieldType type) noexcept -> bool {
		switch(type)
		{
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
		case Et_DbSheetField_Contact:
		case Et_DbSheetField_Address:
		case Et_DbSheetField_Cascade:
		case Et_DbSheetField_Department:
			return true;
		default:
			break;
		}
		return false;
	};

	if (this->m_spSrcDbSheetOp->GetSheetSubType() == DbSheet_Sub_Type_StatSheet)
	{
		using namespace DbSheet::array_field_fall_back;
		ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
		m_pSrcSheet->GetExtDataItem(edStatisticSheetData, reinterpret_cast<IUnknown **>(&spDBStatisticSheetData));
		ASSERT(spDBStatisticSheetData);

		app_helper::KBatchUpdateCal _(this->m_spTarDbSheetOp->GetBook()->LeakOperator());
		const auto pTargetFieldIDs {m_spTarDbSheetOp->GetAllFields()};
		const auto pTargetRecordIDs {m_spTarDbSheetOp->GetAllRecords()};
		const auto recordCount {pTargetRecordIDs->Count()}, fieldCount {pTargetFieldIDs->Count()};
		const IDbFieldsManager* const pAllFieldsManager {this->m_spTarDbSheetOp->GetFieldsManager()};
		const auto formulaColumnFrom {spDBStatisticSheetData->GetFormulaColIdx()};
		for (auto i {static_cast<UINT>(formulaColumnFrom)}; i < fieldCount; ++i)
		{
			const auto fieldID {pTargetFieldIDs->IdAt(i)};
			ks_stdptr<IDbField> spField {};
			pAllFieldsManager->GetField(fieldID, &spField);
			ASSERT(spField);
			const auto typeBeforeFallBack {spField->GetType()};
			if (typeBeforeFallBack == Et_DbSheetField_FormulaResult)
			{
				if (checkErrorToken(spField))
				{
					convertToText(spField);
					spField->SetArray(FALSE);
					continue;
				}
				const auto oldNumberFormat {spField->GetNumberFormat()};
				ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = spField;
				switch (spField_FormulaResult->GetFormulaResultType())
				{
				case DbSheet_Fvt_Text:
				case DbSheet_Fvt_Contact:
				case DbSheet_Fvt_Date:
				case DbSheet_Fvt_Time:
					convertToText(spField);
					spField->SetArray(FALSE);
					continue;
				case DbSheet_Fvt_Number:
				case DbSheet_Fvt_Logic:
					spField->SetTypeForIO(Et_DbSheetField_Number);
					spField->SetNumberFormat(oldNumberFormat);
					break;
				default:
					break;
				}
			}
			if (isZeroAsBlank(typeBeforeFallBack))
			{
				const auto pRecordIDs {m_spTarDbSheetOp->GetAllRecords()};
				ks_stdptr<IDBSheetRange> spDbRange;
				m_spTarDbSheetOp->CreateDBSheetRange(&spDbRange);
				for (UINT i {0}; i < recordCount; ++i)
				{
					const auto recordID {pRecordIDs->IdAt(i)};
					const_token_ptr pToken {};
					m_spTarDbSheetOp->GetValueToken(recordID, fieldID, &pToken);
					if (alg::const_vdbl_token_assist::is_type(pToken))
					{
						spDbRange->AddRecordId(recordID);
						spDbRange->AddFieldId(fieldID);
					}
				}
				if (spDbRange->GetFieldCnt() != 0)
					m_spTarDbSheetOp->Clear(spDbRange, TRUE);
			}
			if (spField->IsArray())
			{
				fallback(spField);
				spField->SetArray(FALSE);
			}
			if (spField->GetType() == Et_DbSheetField_Email)
			{
				for (UINT i = 0; i < recordCount; ++i)
				{
					const auto recordID {pTargetRecordIDs->IdAt(i)};
					ks_bstr cellString {};
					m_spTarDbSheetOp->GetDisplayString(recordID, fieldID, &cellString);
					m_spTarDbSheetOp->SetHyperlinkAddress(recordID, fieldID, cellString.c_str());
				}
			}
		}
	}
}

HRESULT CopyDBSheetHelper::CopyDbSheetRecords()
{
	const IDBIds* pIds = m_spSrcDbSheetOp->GetAllRecords();
	m_spTarDbSheetOp->ResetRecords(pIds);
	CopyDbSheetRecordsRelation();
	if (m_param.resetCreationInfoByCurUser)
		return S_OK;
	IDbRecordsManager* pSrcDbRecordsManager = m_spSrcDbSheetOp->GetRecordsManager();
	IDbRecordsManager* pTarDbRecordsManager = m_spTarDbSheetOp->GetRecordsManager();
	for (EtDbIdx i = 0, cnt = pIds->Count(); i < cnt; ++i)
	{
		EtDbId recId = pIds->IdAt(i);
		PCWSTR creatorId = pSrcDbRecordsManager->GetRecordCreatorId(recId);
		double createdTime = 0;
		VS(pSrcDbRecordsManager->GetRecordCreatedTime(recId, createdTime));
		VS(pTarDbRecordsManager->UpdateRecord(recId, creatorId, createdTime));
	}
	return S_OK;
}

HRESULT CopyDBSheetHelper::CopyDbSheetRecordsRelation()
{
    if (!m_pTarBook->GetBMP()->bDbSheet)
        return S_OK;
    IDbRecordsManager* pSrcDbRecordsManager = m_spSrcDbSheetOp->GetRecordsManager();
	IDbRecordsManager* pTarDbRecordsManager = m_spTarDbSheetOp->GetRecordsManager();
	return pSrcDbRecordsManager->CopyRecordsRelation(pTarDbRecordsManager);
}

HRESULT CopyDBSheetHelper::SetDbSheetRecordModifiedInfo() const
{
	const IDBIds* pFields = m_spTarDbSheetOp->GetAllFields();
	const IDBIds* pRecords = m_spTarDbSheetOp->GetAllRecords();
    EtDbIdx fieldCount = pFields->Count();
    EtDbIdx recordCount = pRecords->Count();
	IDbFieldsManager* pFieldsManager = m_spTarDbSheetOp->GetFieldsManager();
    for (EtDbIdx fieldIdx = 0; fieldIdx < fieldCount; ++fieldIdx)
    {
        EtDbId fieldId = pFields->IdAt(fieldIdx);
        ks_stdptr<IDbField> spField;
        HRESULT hr = pFieldsManager->GetField(fieldId, &spField);
        if (FAILED(hr))
            return hr;

        if (spField->IsAuto())
            continue;

        for (EtDbIdx recordIdx = 0; recordIdx < recordCount; ++recordIdx)
        {
            EtDbId recordId = pRecords->IdAt(recordIdx);
            const_token_ptr pToken = nullptr;
			hr = m_spTarDbSheetOp->GetValueToken(recordId, fieldId, &pToken);
            if (FAILED(hr))
                return hr;

            if (!pToken)
                continue;

			hr = m_spTarDbSheetOp->UpdateLastModifiedInfo(recordId, fieldId);
            if (FAILED(hr))
                return hr;
        }
    }
    return S_OK;
}

HRESULT CopyDBSheetHelper::CopyDbSheetRecordModifiedInfo() const
{
    class RecordModifiedInfoEnum : public IDbRecordModifiedInfoEnum
    {
    public:
        RecordModifiedInfoEnum(IDBSheetOp* dbsheetOp) : m_pDbSheetOp(dbsheetOp) {}
        STDPROC Do(EtDbId recordId, EtDbId fieldId, double time, PCWSTR modifierId) override
        {
            m_pDbSheetOp->GetRecordsManager()->SetCellLastModifiedInfoForIO(recordId, fieldId, time, modifierId);
            return S_OK;
        }

    private:
        IDBSheetOp* m_pDbSheetOp;
    };

	RecordModifiedInfoEnum enumer(m_spTarDbSheetOp);
	m_spSrcDbSheetOp->GetRecordsManager()->EnumeRecordModifiedInfo(&enumer);

    return S_OK;
}

HRESULT CopyDBSheetHelper::CopyDbSheetFields()
{
	IDbFieldsManager* pFieldsMgr = m_spSrcDbSheetOp->GetFieldsManager();
	IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	const IDBIds* pIds = m_spSrcDbSheetOp->GetAllFields();
	m_spTarDbSheetOp->ResetAllFields(pIds);
	// 先拷贝基本字段信息
	EtDbIdx cnt = pIds->Count();
	HRESULT hr = S_OK;
	for (EtDbIdx i = 0; i < cnt; ++i)
	{
		EtDbId id = pIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		ks_stdptr<IDbField> spNewField;
		pFieldsMgr->GetField(id, &spField);
		pNewFieldsMgr->AddField(id, &spNewField);
		hr = onCopyFieldBaseInfo(spField, spNewField);
		if (FAILED(hr))
			return hr;
		// 基于"引用字段监视特定单元格"的设计, 创建副本时, 需要基于当前的关联单元格, 清空引用字段以实现反注册.
		// 由于此时只拷贝了基本字段信息, 关联-引用的关系尚未记录, 此时无法实现上述"关联单元格变更前后更新引用"
		// 因此对引用字段补充相关的逻辑
		if (spNewField->GetType() == Et_DbSheetField_Lookup)
		{
			ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
			ks_stdptr<IDbField_Lookup> spNewFieldLookup = spNewField;
			spNewFieldLookup->SetLinkFieldIdForIO(spFieldLookup->GetLinkFieldId());
		}
	}
	// 拷贝基本字段信息后即设置关键列
	pNewFieldsMgr->SetPrimaryFieldForIO(pFieldsMgr->GetPrimaryField());
	// 然后再拷贝拓展字段信息
	for (EtDbIdx i = 0; i < cnt; ++i)
	{
		EtDbId id = pIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		ks_stdptr<IDbField> spNewField;
		pFieldsMgr->GetField(id, &spField);
		pNewFieldsMgr->GetField(id, &spNewField);
		hr = CopyDbSheetField(spField, spNewField);
		if (FAILED(hr))
			return hr;

		AdjustFieldData(spField, spNewField);
	}
	return S_OK;
}

static void fallBackArraySupportField_copy_sheet(IDbField* pTargetField, IDbField* pSourceField, OUT bool* pIsFallBack2Txt)
{
	pTargetField->SetNumberFormatForIOAndDiffuse(pSourceField->GetNumberFormat()); // 先根据srcField设置numFmt，如有需要会在fallback中更新numFmt

	using namespace DbSheet::array_field_fall_back;
	IDBSheetOp* const pSheetOp = pSourceField->GetDbSheetData();
	if (pSheetOp->GetSheetSubType() == DbSheet_Sub_Type_StatSheet)
	{
		pTargetField->SetArray(pSourceField->IsArray());
		return;
	}
	if (pSheetOp->GetSheetSyncType() == DbSheet_St_None)
		return;
	IDBSheetOp* pTarSheetOp = pTargetField->GetDbSheetData();
	if (!pTarSheetOp->IsSyncSheet() && (pSourceField->IsArray() || pSourceField->GetType() == Et_DbSheetField_FormulaResult))
	{
		if(pSourceField->GetType() == Et_DbSheetField_FormulaResult) {
			// 回落前如果源表字段是公式结果字段, 要把 value type 带过去
			ks_stdptr<IDbField_FormulaResult> spTargetField_FormulaResult = pTargetField;
			ks_stdptr<IDbField_FormulaResult> spSourceField_FormulaResult = pSourceField;
			spTargetField_FormulaResult->SetFormulaResultType(spSourceField_FormulaResult->GetFormulaResultType());
		}
		DbSheet::array_field_fall_back::fallback(pTargetField, pIsFallBack2Txt);
		return;
	}
	// 本表是同步表, 那么照常拷贝就可以了, 包括公式结果字段, 只需要注意 arraySupport
	pTargetField->SetArray(pSourceField->IsArray());
}

HRESULT CopyDBSheetHelper::onCopyFieldBaseInfo(IDbField* pSrcField, IDbField* pTarField)
{
	HRESULT hr = S_OK;
	if (m_param.needResetAutoValue && pSrcField->IsAuto())
		hr = pTarField->SetType(pSrcField->GetType(), nullptr);
	else
		hr = pTarField->SetTypeForIO(pSrcField->GetType(), TRUE);
	if (FAILED(hr))
		return hr;
	
	//先把源表的字段类型给拷贝过来，因为里面回落的逻辑依赖于源表的字段类型来进行format
	//再考虑是否要回落为文本字段。
	bool bIsFallBack2Txt = false; // 判断pTarField在fallBack方法中，是否回落成了文本字段
	if (m_bNewLookupConvert)
		fallBackArraySupportField_copy_sheet(pTarField, pSrcField, &bIsFallBack2Txt);

	const IDBIds* pIds = m_spTarDbSheetOp->GetAllFields();
	VS(pTarField->SetName(pSrcField->GetName(), TRUE, TRUE));
	VS(pTarField->SetDescription(pSrcField->GetDescription()));
	if (!m_bNewLookupConvert) // m_bNewLookupConvert为true时，在fallBack方法里特殊处理NumberFormat
	{
		hr = pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat());
		if (FAILED(hr))
			return hr;
	}
	if (!bIsFallBack2Txt)
	{
		VS(pTarField->SetDefaultValForIO(pSrcField->GetDefaultVal()));
		VS(pTarField->SetDefaultValTypeForIO(pSrcField->GetDefaultValType()));
		VS(pTarField->SetValueUniqueForIO(pSrcField->IsValueUnique()));
	}
	if (pSrcField->GetType() == Et_DbSheetField_MultiLineText 
		|| pSrcField->GetType() == Et_DbSheetField_SingleLineText)
	{
		VS(pTarField->SetPromptId(pSrcField->GetPromptId()));
	}
	pTarField->SetCustomConfig(pSrcField->GetCustomConfig());

	return S_OK;
}

HRESULT CopyDBSheetHelper::AdjustFieldData(IDbField*, IDbField* pTarField)
{
	ET_DbSheet_FieldType type = pTarField->GetType();
	HRESULT hr = S_OK;
	if (type < ET_DbSheet_FieldType_Count && m_param.clearType[type])
	{
		hr = pTarField->Clear();
		if (FAILED(hr))
			return hr;
		if (pTarField->GetDefaultValType() == DbSheet_Field_Dvt_Normal)
			pTarField->SetDefaultVal(DbSheet_Field_Dvt_Normal, __X(""));
		return S_OK;
	}
	EtDbId fldId = pTarField->GetID();
	switch (type)
	{
		case Et_DbSheetField_Attachment:
			if (m_param.pAttachmentIdMap)
				VS(UpdateAttachmentByMapping(fldId, m_spTarDbSheetOp, m_param.pAttachmentIdMap, m_lostNoteFlag));
			break;
		case Et_DbSheetField_Note:
			if (m_param.pAttachmentIdMap)
				VS(UpdateNoteByMapping(fldId, m_spTarDbSheetOp, m_param.pAttachmentIdMap, m_lostNoteFlag));
			break;
		default:
			break;
	}
	return S_OK;
}

static std::pair<IDbField*, ET_DbSheet_FieldType> GetLookupBaseField(IDbField* pField)
{
	ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
	ks_stdptr<IDbField> spField_Lookup_base;
	spField_Lookup->GetLookupBaseField(&spField_Lookup_base);
	if (spField_Lookup_base == nullptr)
		return {};
	const ET_DbSheet_FieldType type = spField_Lookup_base->GetType();
	return {spField_Lookup_base.get(), type};
}

HRESULT CopyDBSheetHelper::CopyDbSheetContactExtendField(IDbField* pField, IDbField* pNewField)
{
	HRESULT hr = S_OK;
	if (!pField || !pNewField)
		return E_FAIL;

	ks_stdptr<IDbField_Contact> spFieldContact = pField;
	if (!spFieldContact)
		return E_FAIL;

	if (pField->IsSyncField()) //同步字段不需要配置扩展字段，当作普通字段处理
		return S_OK;

	IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	if (spFieldContact->ExistExtendField())
	{
		EtDbId extendField = spFieldContact->GetDepartmentField();
		if (extendField != INV_EtDbId)
			DbSheet::SetDbContactExtendField(pNewFieldsMgr, extendField, pNewField->GetID());
		extendField = spFieldContact->GetLeaderField();
		if (extendField != INV_EtDbId)
			DbSheet::SetDbContactExtendField(pNewFieldsMgr, extendField, pNewField->GetID());
		extendField = spFieldContact->GetEmailField();
		if (extendField != INV_EtDbId)
			DbSheet::SetDbContactExtendField(pNewFieldsMgr, extendField, pNewField->GetID());
		extendField = spFieldContact->GetEmployeeIdField();
		if (extendField != INV_EtDbId)
			DbSheet::SetDbContactExtendField(pNewFieldsMgr, extendField, pNewField->GetID());
	}
	return hr;
}

HRESULT CopyDBSheetHelper::CopyDbSheetField(IDbField* pField, IDbField* pNewField)
{
	ET_DbSheet_FieldType type = pField->GetType();
	bool sourceFieldIsLookup {};
	if (m_bNewLookupConvert)
	{
		// 同步表下递归修正 lookup field base type 和 pField
		ET_DbSheet_Sync_Type sheetSyncType = pNewField->GetDbSheetData()->GetSheetSyncType();
		if ((sheetSyncType == DbSheet_St_DB || sheetSyncType == DbSheet_St_Cross_DB) && type == Et_DbSheetField_Lookup)
		{
			sourceFieldIsLookup = true;
			ks_stdptr<IDbField_Lookup> spNewFieldLookup = pNewField;
			const auto [baseField, baseFieldType] = GetLookupBaseField(pField);
			if (baseField == nullptr)
			{
				if (spNewFieldLookup->GetLinkFieldId() == INV_EtDbId)
					return S_OK;
				else
					return E_FAIL;
			}
			
			// 同步表下引用聚合 (非原始值/原始值去重) 的情形要回落
			// 对于原始值/原始值去重的情形, DBSyncHelper::onCopyFieldBaseInfo 已经对 pNewField 的 type 进行了回落
			if (pNewField->GetType() == Et_DbSheetField_Lookup)
			{
				ks_stdptr<IDbField_Lookup> spFieldLookup = pField;
				const auto setNumberFormat {[](IDbField* pNewField, IDbField*baseField,
						ET_DbSheet_FieldType baseFieldType) {
					switch (baseFieldType)
					{
					case Et_DbSheetField_Date:
					case Et_DbSheetField_Complete:
					case Et_DbSheetField_Percentage:
					case Et_DbSheetField_Time:
					case Et_DbSheetField_CreatedTime:
					case Et_DbSheetField_LastModifiedTime:
					case Et_DbSheetField_Currency:
					case Et_DbSheetField_AutoNumber:
					case Et_DbSheetField_Formula:		// 实际上是针对公式结果字段的 number format
						pNewField->SetNumberFormatForIOAndDiffuse(baseField->GetNumberFormat());
						break;
					default:
						break;
					}
				}};
				switch (spFieldLookup->GetLookupFunction())
				{
				case DbSheet_Lookup_Function_Sum:
				case DbSheet_Lookup_Function_Max:
				case DbSheet_Lookup_Function_Min:
				case DbSheet_Lookup_Function_Average:
				{
					pNewField->SetTypeForIO(Et_DbSheetField_FormulaResult);
					ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pNewField;
					if (baseFieldType != Et_DbSheetField_AutoNumber) /*[[likely]]*/
						spField_FormulaResult->SetFormulaResultType(pField->GetValueType());
					else /*[[unlikely]]*/
						spField_FormulaResult->SetFormulaResultType(DbSheet_Fvt_Number);
					pNewField->SetNumberFormatForIOAndDiffuse(pField->GetNumberFormat());
					break;
				}
				case DbSheet_Lookup_Function_Counta:
				case DbSheet_Lookup_Function_CountaUnique:
				{
					pNewField->SetTypeForIO(Et_DbSheetField_FormulaResult);
					ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pNewField;
					spField_FormulaResult->SetFormulaResultType(DbSheet_Fvt_Number);
					pNewField->SetNumberFormatForIOAndDiffuse(pField->GetNumberFormat());
					break;
				}
				case DbSheet_Lookup_Function_ToString:
				{
					pNewField->SetTypeForIO(Et_DbSheetField_FormulaResult);
					ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pNewField;
					spField_FormulaResult->SetFormulaResultType(DbSheet_Fvt_Text);
					break;
				}
				case DbSheet_Lookup_Function_Origin:
				case DbSheet_Lookup_Function_Unique:
					if (baseFieldType == Et_DbSheetField_AutoNumber)
					{
						pNewField->SetNumberFormatForIOAndDiffuse(baseField->GetNumberFormat());
					}
					else		// 走到这里的原始值和去重原始值说明引用了引用聚合 (统计字段)
					{
						pNewField->SetTypeForIO(Et_DbSheetField_FormulaResult);
						ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pNewField;
						spField_FormulaResult->SetFormulaResultType(baseField->GetValueType());
						pNewField->SetNumberFormatForIOAndDiffuse(baseField->GetNumberFormat());
					}
					break;
				default:
					ASSERT("新增的引用聚合类型请修改这里" == nullptr);
					break;
				}
			}
			// built-in type 不需要 move
			type = baseFieldType;
			pField = baseField;
		}
	}
	if (type != pNewField->GetType())
		return S_OK;
	switch (type)
	{
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
		{
			ks_stdptr<IDbField_Select> spFieldSelect = pField;
			ks_stdptr<IDbField_Select> spNewFieldSelect = pNewField;
			spNewFieldSelect->ResetItems();
			UINT cnt = spFieldSelect->Count();
			if (cnt > 0)
			{
				for (UINT i = 0; i < cnt; ++i)
				{
					PCWSTR value = nullptr;
					EtDbId id = INV_EtDbId;
					ARGB color = 0;
					spFieldSelect->Item(i, &value, &color, &id);
					spNewFieldSelect->AppendItemForIO(value, color, id);
				}
			}
			spNewFieldSelect->SetAllowAddItemWhenInputting(spFieldSelect->GetAllowAddItemWhenInputting());
			if (m_param.needAutoAddItem)
				spNewFieldSelect->SetAutoAddItem(TRUE);
			break;
		}
		case Et_DbSheetField_Rating:
		{
			ks_stdptr<IDbField_Rating> spFieldRating = pField;
			ks_stdptr<IDbField_Rating> spNewFieldRating = pNewField;
			spNewFieldRating->SetMaxRating(spFieldRating->GetMaxRating());
			break;
		}
		case Et_DbSheetField_Date:
		{
			ks_stdptr<IDbField_Date> spFieldDate = pField;
			ks_stdptr<IDbField_Date> spNewFieldDate = pNewField;
			spNewFieldDate->SetLoadLegalHoliday(spFieldDate->GetLoadLegalHoliday());
			break;
		}
		case Et_DbSheetField_Url:
		{
			ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = pField;
			ks_stdptr<IDbField_Hyperlink> spNewFieldHyperlink = pNewField;
			spNewFieldHyperlink->SetDisplayText(spFieldHyperlink->GetDisplayText());
			break;
		}
		case Et_DbSheetField_CreatedBy:
			CopyDbSheetContactExtendField(pField, pNewField);
			break;
		case Et_DbSheetField_Contact:
		{
			if (sourceFieldIsLookup)
			{
				ks_stdptr<IDbField_Contact> spField_Contact = pNewField;
				spField_Contact->SetSupportMulti(TRUE);
			}
			else
			{
				ks_stdptr<IDbField_Contact> spFieldContact = pField;
				ks_stdptr<IDbField_Contact> spNewFieldContact = pNewField;
				spNewFieldContact->SetSupportMulti(spFieldContact->GetSupportMulti());
				CopyDbSheetContactExtendField(pField, pNewField);
			}
			break;
		}
		case Et_DbSheetField_Complete:
		{
			ks_stdptr<IDbField_Complete> spFieldComplete = pField;
			ks_stdptr<IDbField_Complete> spNewFieldComplete = pNewField;
			spNewFieldComplete->SetColor(spFieldComplete->GetColor());
			break;
		}
		case Et_DbSheetField_AutoNumber:
		{
			if (!m_param.needResetAutoValue)
			{
				ks_stdptr<IUnknown> spUnknown1;
				VS(m_pSrcBook->GetExtDataItem(edBookDbBookCtx, &spUnknown1));
				ks_stdptr<IDbtBookCtx> spSrcDbtBookCtx = spUnknown1;
				ks_stdptr<IUnknown> spUnknown2;
				VS(m_pTarBook->GetExtDataItem(edBookDbBookCtx, &spUnknown2));
				ks_stdptr<IDbtBookCtx> spTarDbtBookCtx = spUnknown2;
				ASSERT(spTarDbtBookCtx != spSrcDbtBookCtx);

				UINT nextNumber = 1;
				spSrcDbtBookCtx->PeekNextAutoNumber(pField, &nextNumber);
				spTarDbtBookCtx->SetNextAutoNumber(pNewField, nextNumber);
			}
			break;
		}
		case Et_DbSheetField_Automations:
		{
			ks_stdptr<IDbField_Automations> spFieldAutomations = pField;
			ks_stdptr<IDbField_Automations> spNewFieldAutomations = pNewField;

			UINT cnt = spFieldAutomations->GetAutomationsCount();
			for (EtDbIdx i = 0; i < cnt; i++)
			{
				ks_stdptr<IDbAutomation> spAutomation;
				HRESULT hr = spFieldAutomations->GetAutomation(i, &spAutomation);
				if (FAILED(hr))
					return hr;

				ks_stdptr<IDbAutomation> spNewAutomation;
				hr = spNewFieldAutomations->AddAutomation(&spNewAutomation);
				if (FAILED(hr))
					return hr;
				hr = spAutomation->CopyTo(spNewAutomation);
				if (FAILED(hr))
					return hr;
			}
			break;
		}
		case Et_DbSheetField_Formula:
		{
			ks_stdptr<IDbField_Formula> spFieldLFormula = pField;
			ks_stdptr<IDbField_Formula> spNewFieldFormula = pNewField;
            spNewFieldFormula->SetShowPercentAsProgress(spFieldLFormula->GetShowPercentAsProgress());
			if (m_param.copyFormulaCustom)
			{
				spNewFieldFormula->SetFormula(__X(""), FML_CPL_ET_COMPATIBLE);
				spNewFieldFormula->OnAfterCopy();
				break;
			}
			ks_bstr bstrFormula;
			spFieldLFormula->GetFormula(&bstrFormula, FML_CPL_ET_COMPATIBLE); // 复制时取公式和设公式都用同一个参数
			if (bstrFormula == nullptr)
				spNewFieldFormula->SetFormula(__X(""), FML_CPL_ET_COMPATIBLE);
			else
				spNewFieldFormula->SetFormula(bstrFormula.c_str(), FML_CPL_ET_COMPATIBLE);
            spNewFieldFormula->OnAfterCopy();
			break;
		}
		case Et_DbSheetField_Link:
		{
			// 上线一段时间后, 预期 Et_DbSheetField_Link 类型的字段都是双向的, 且都不是自动关联
			// 目前则仍然保持原先的逻辑不变

			ks_stdptr<IDbField_Link> spFieldLink = pField;
			ks_stdptr<IDbField_Link> spNewFieldLink = pNewField;
			spNewFieldLink->SetSupportMultiLinks(spFieldLink->GetSupportMultiLinks());
			// 先用 SetLinkSheetDirectly 令 spNewFieldLink 具有和原始字段相同的配置
			// 这里要特殊处理的是，如果源表的关联字段关联的是源表自身，那么副本同样关联自身
			if (spFieldLink->GetLinkSheet() == pField->GetDbSheetData()->GetSheetId())
				spNewFieldLink->SetLinkSheetDirectly(pNewField->GetDbSheetData()->GetSheetId());
			else
			{
				// 如果是从模板文件导入, 则关联字段需要指向导入后文件内正确的sheetStId
				// 同时, 模板文件内的关联字段认为是"已配置好的", 换言之, 字段的 reservedLink信息也要一并拷贝
				if (m_param.pStIdMap)
				{
					UINT dstSheetId = 0;
					auto it = m_param.pStIdMap->find(spFieldLink->GetLinkSheet());
					if (it != m_param.pStIdMap->end())
						dstSheetId = it->second;
					// 关联的sheet有可能不存在
					if (dstSheetId != 0)
					{
						spNewFieldLink->SetLinkSheetDirectly(dstSheetId);
						spNewFieldLink->SetReversedLinkFieldDirectly(spFieldLink->GetReversedLinkField());
					}
				}
				else
				{
					spNewFieldLink->SetLinkSheetDirectly(spFieldLink->GetLinkSheet());
				}
			}
			if (not spFieldLink->IsAutoLink())
			{
				// 再用 SetLinkSheet 在原始字段关联到的数据表内创建对应的反向关联字段. 细则已经在 SetLinkSheet 里处理好了
				// 从其他文件导入时, 当发生了关联字段中的隐式创建字段行为, 若被关联表尚未得到处理,
				// 则由于该表的fields信息未完全加载, 此时字段新建行为会有问题
				// 初步考虑对单向关联字段不使用SetLinkSheet隐式地创建反向关联
				// 这里可能会改变文件内的创建副本行为. 已找产品确认, 在线上先将历史的单向关联导入, 日后再支持将单向关联修改为
				// 双向关联(需要先在各sheet里将fields初始化, 再继续复制fields的后续信息)
				// 特别地, 复制出来的字段信息不完全, 因此判断字段是否为单向关联字段时, 要使用老字段
				if (spFieldLink->IsBidirectionalLink())
					spNewFieldLink->SetLinkSheet(spNewFieldLink->GetLinkSheet(), m_param.biLinkPrefix);
				spNewFieldLink->SetLinkView(spFieldLink->GetLinkView());
				if (spFieldLink->IsLinkCustomConfig())
					VS(copyLinkCustomConfig(spFieldLink, spNewFieldLink));
			}
			else
				copyAutoLinkProp(spFieldLink, spNewFieldLink);
			break;
		}
		case Et_DbSheetField_OneWayLink:
			copyOneWayLinkField(pField, pNewField);
			break;
		case Et_DbSheetField_Lookup:
			copyLookupLinkField(pField, pNewField);
			break;
		case Et_DbSheetField_ParentRecord:
			pNewField->UpdateAutoValue();
			break;
		case Et_DbSheetField_Attachment:
		{
			ks_stdptr<IDbField_Attachment> spFieldAttachment = pField;
			ks_stdptr<IDbField_Attachment> spNewFieldAttachment = pNewField;
			spNewFieldAttachment->SetAttachmentType(spFieldAttachment->GetAttachmentType());
            spNewFieldAttachment->SetAttachmentDisplayStyle(spFieldAttachment->GetAttachmentDisplayStyle());
			spNewFieldAttachment->SetOnlyUploadByCamera(spFieldAttachment->GetOnlyUploadByCamera());
			break;
		}
        case Et_DbSheetField_Address:
        {
            ks_stdptr<IDbField_Cascade> spFieldAddress = pField;
            ks_stdptr<IDbField_Cascade> spNewFieldAddress = pNewField;
            spNewFieldAddress->SetCascadeLevel(spFieldAddress->GetCascadeLevel());
            spNewFieldAddress->SetWithDetailedInfo(spFieldAddress->GetWithDetailedInfo());
			IDbCascadeHandle* pPresetAddressHandle = spFieldAddress->GetDefValueHandle();
			if(pPresetAddressHandle)
			{
				std::vector<PCWSTR> districtPointers;
				UINT presetAddressItemCount = pPresetAddressHandle->GetCascadeLevel();
				districtPointers.reserve(presetAddressItemCount);
				for (UINT i = 0; i < presetAddressItemCount; ++i)
					districtPointers.push_back(pPresetAddressHandle->GetCascadeItemValue(i));
				spNewFieldAddress->SetDefValueItemList(districtPointers.data(), districtPointers.size());
			}
			break;
        }
		case Et_DbSheetField_Cascade:
		{
			ks_stdptr<IDbField_Cascade> spFieldCascade = pField;
			ks_stdptr<IDbField_Cascade> spNewFieldCascade = pNewField;
            spNewFieldCascade->SetDisplayAllLevel(spFieldCascade->GetDisplayAllLevel());
            IDbCascadeHandle* cascadeTitleHandle = spFieldCascade->GetCascadeTitleHandle();
            if(cascadeTitleHandle)
            {
                std::vector<PCWSTR> titles;
                UINT count = cascadeTitleHandle->GetCascadeLevel();
                titles.reserve(count);
                for (UINT i = 0; i < count; ++i)
                    titles.push_back(cascadeTitleHandle->GetCascadeItemValue(i));
                VS(spNewFieldCascade->SetCascadeTitle(titles.data(), titles.size()));
            }
            spFieldCascade->CopyAllCascadeOption(pNewField);
            break;
		}
		case Et_DbSheetField_Department:
		{	
			ks_stdptr<IDbField_Cascade> spFieldDepartment = pField;
			ks_stdptr<IDbField_Cascade> spNewFieldDepartment = pNewField;
			spNewFieldDepartment->SetMultiValue(spFieldDepartment->GetMultiValue());
			spNewFieldDepartment->SetDisplayAllLevel(spFieldDepartment->GetDisplayAllLevel());
			break;
		}
		case Et_DbSheetField_LastModifiedBy:
		case Et_DbSheetField_LastModifiedTime:
        {
            ks_stdptr<IDbField_WatchedField> spFieldLastModified = pField;
            ks_stdptr<IDbField_WatchedField> spNewFieldLastModified = pNewField;
            spNewFieldLastModified->SetWatchedAllForIO(spFieldLastModified->IsWatchedAll());
			if(!spNewFieldLastModified->IsWatchedAll())
			{
				std::vector<EtDbId> dbIds;
				dbIds.resize(spFieldLastModified->GetWatchedFieldCount());
				spFieldLastModified->GetWatchedFieldId(dbIds.data(),dbIds.size());
				spNewFieldLastModified->SetWatchedFieldIdForIO(dbIds.data(),dbIds.size());
			}
			break;
        }
		case Et_DbSheetField_FormulaResult:
		{
			ks_stdptr<IDbField_FormulaResult> spSourceField_FormulaResult = pField;
			ks_stdptr<IDbField_FormulaResult> spTargetField_FormulaResult = pNewField;
			spTargetField_FormulaResult->SetFormulaResultType(spSourceField_FormulaResult->GetFormulaResultType());
			break;
		}
		case Et_DbSheetField_BarCode:
		{
			ks_stdptr<IDbField_BarCode> spFieldScanCode = pField;
			ks_stdptr<IDbField_BarCode> spNewFieldScanCode = pNewField;
			spNewFieldScanCode->SetOnlyScanByCamera(spFieldScanCode->GetOnlyScanByCamera());
			break;
		}
		case Et_DbSheetField_Button:
		{
            ks_stdptr<IDbField_Button> spFieldButton = pField;
            ks_stdptr<IDbField_Button> spNewFieldButton = pNewField;
            spNewFieldButton->SetButtonText(spFieldButton->GetButtonText());
            spNewFieldButton->SetButtonIcon(spFieldButton->GetButtonIcon());
            spNewFieldButton->SetSuccessText(spFieldButton->GetSuccessText());
            spNewFieldButton->SetTextColor(spFieldButton->GetTextColor());
            spNewFieldButton->SetBackgroundColor(spFieldButton->GetBackgroundColor());
			break;
		}
		default:
			break;
	}
	return S_OK;
}

void CopyDBSheetHelper::copyOneWayLinkField(IDbField* pField, IDbField* pNewField) const
{
	ks_stdptr<IDbField_Link> spFieldLink = pField;
	ks_stdptr<IDbField_Link> spNewFieldLink = pNewField;
	spNewFieldLink->SetSupportMultiLinks(spFieldLink->GetSupportMultiLinks());

	// 先用 SetLinkSheetDirectly 令 spNewFieldLink 具有和原始字段相同的配置
	// 这里要特殊处理的是，如果源表的关联字段关联的是源表自身，那么副本同样关联自身
	if (spFieldLink->GetLinkSheet() == pField->GetDbSheetData()->GetSheetId())
		spNewFieldLink->SetLinkSheetDirectly(pNewField->GetDbSheetData()->GetSheetId());
	else
	{
		// 如果是从模板文件导入, 则关联字段需要指向导入后文件内正确的sheetStId
		if (m_param.pStIdMap)
		{
			UINT dstSheetId = 0;
			auto it = m_param.pStIdMap->find(spFieldLink->GetLinkSheet());
			if (it != m_param.pStIdMap->end())
				dstSheetId = it->second;
			// 关联的sheet有可能不存在
			if (dstSheetId != 0)
			{
				spNewFieldLink->SetLinkSheetDirectly(dstSheetId);
			}
		}
		else
		{
			spNewFieldLink->SetLinkSheetDirectly(spFieldLink->GetLinkSheet());
		}
	}

	// 复制自动/手动关联的其他配置
	if (spFieldLink->IsAutoLink())
		copyAutoLinkProp(spFieldLink, spNewFieldLink);
	else if (spFieldLink->IsLinkCustomConfig())
		VS(copyLinkCustomConfig(spFieldLink, spNewFieldLink));
	else
		spNewFieldLink->SetLinkView(spFieldLink->GetLinkView());
}

void CopyDBSheetHelper::copyAutoLinkProp(IAutoLinkCond* pSrcField, IAutoLinkCond* pNewField)
{
		ks_stdptr<IDbAutolinkCondProps> spAutoLinkCondProps;
		_appcore_CreateObject(CLSID_KDbAutolinkCondProps, IID_IDbAutolinkCondProps, (void **)&spAutoLinkCondProps);
		pSrcField->GetAutoLinkProp(spAutoLinkCondProps.get());
		if (0 == spAutoLinkCondProps->Count())
		{
			pNewField->SetAutoLinkProp(TRUE, nullptr);
		}
		else
		{
			pNewField->SetAutoLinkProp(TRUE, spAutoLinkCondProps.get());
		}
		// todo: 验证新内核导入旧数据的正确性. 预期是字段配置上都走新逻辑, 存读盘通过环境变量处理
}

HRESULT CopyDBSheetHelper::copyLinkCustomConfig(IDbField_Link* pSrcLinkField, IDbField_Link* pNewLinkField) const
{
	ks_stdptr<IDbLinkCustomConfig> spLinkCustomConfig;
	pSrcLinkField->GetLinkCustomConfig(&spLinkCustomConfig);
	return pNewLinkField->SetLinkCustomConfig(pNewLinkField->GetLinkSheet(), spLinkCustomConfig.get());
}

void CopyDBSheetHelper::copyLookupLinkField(IDbField* pField, IDbField* pNewField) const
{
	ks_stdptr<IDbField_Lookup> spFieldLookup = pField;
	ks_stdptr<IDbField_Lookup> spNewFieldLookup = pNewField;
	spNewFieldLookup->SetLinkFieldId(spFieldLookup->GetLinkFieldId());
	spNewFieldLookup->SetLookupType(spFieldLookup->GetLookupType());
	spNewFieldLookup->SetLookupFunction(spFieldLookup->GetLookupFunction());
	// DBLOOKUP2支持跨表导入了，这里得兼容一下index调整
	if (!spNewFieldLookup->IsNormalType())
	{
		spNewFieldLookup->SetLookupSheetId(spFieldLookup->GetLookupSheetId());
		if (spFieldLookup->GetLookupSheetId() == pField->GetDbSheetData()->GetSheetId())
		{
			spNewFieldLookup->SetLookupSheetId(pNewField->GetDbSheetData()->GetSheetId());
		}
		else if (m_param.pStIdMap)
		{
			UINT dstSheetId = 0;
			auto it = m_param.pStIdMap->find(spFieldLookup->GetLookupSheetId());
			if (it != m_param.pStIdMap->end())
				dstSheetId = it->second;
			// 关联的sheet有可能不存在
			if (dstSheetId != 0)
				spNewFieldLookup->SetLookupSheetId(dstSheetId);
			else if (m_param.allowInvalidValueWhenSheetNotInStIdMap)
				spNewFieldLookup->SetLookupSheetId(dstSheetId);
		}
		copyAutoLinkProp(spFieldLookup, spNewFieldLookup);
	}
	spNewFieldLookup->SetLookupFieldId(spFieldLookup->GetLookupFieldId());
}

DBSyncHelper::DBSyncHelper(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet, bool bNewLookupConvert)
	: CopyDBSheetHelper(pSrcWorkSheet, pTarWorkSheet, nullptr, bNewLookupConvert)
{
}

HRESULT DBSyncHelper::Init(const DBSyncParam& syncParam)
{
	if (!syncParam.urlTemplate || !syncParam.fldSourceName)
		return E_INVALIDARG;
	DbSheetCopyParam param;
	param.copyFormulaCustom = true;
	param.needAutoAddItem = true;
	param.needResetAutoValue = false;
	m_param = syncParam;
	return CopyDBSheetHelper::Init(param);
}

HRESULT DBSyncHelper::ExecCopy()
{
	CustomCtxController customCtxController(m_pSrcBook, m_pTarBook);
	HRESULT hr = S_OK;
	if (m_param.needCopySheetInfo)
	{
		hr = CopySheetProperty();
		if (FAILED(hr))
			return hr;
		hr = ResetTableName();
		if (FAILED(hr))
			return hr;
	}
	hr = adjustFields();
	if (FAILED(hr))
		return hr;
	hr = adjustRecords();
	if (FAILED(hr))
		return hr;
	hr = CopyDbSheetData();
	if (FAILED(hr))
		return hr;
	hr = setInfoField();
	if (FAILED(hr))
		return hr;
	VS(CopyBookCtxId());
	return S_OK;
}

bool DBSyncHelper::isValidType(ET_DbSheet_FieldType type)
{
	switch (type)
	{
		case Et_DbSheetField_Automations:
		case Et_DbSheetField_CellPicture:
		case Et_DbSheetField_SingleLineText:
			return false;
		default:
			break;
	}
	return true;
}

HRESULT DBSyncHelper::CopySheetProperty()
{
	ISheet* pSrcSheet= m_pSrcWorkSheet->GetSheet();
	ISheet* pTarSheet= m_pTarWorkSheet->GetSheet();
	// 初始化列宽
	EtDbIdx fieldCnt = m_spSrcDbSheetOp->GetAllFields()->Count();
	for (int i = 0; i < fieldCnt; ++i)
	{
		INT initFieldWidth = 0;
		pSrcSheet->GetColWidth(i, &initFieldWidth);
		pTarSheet->SetColWidth(i, i, initFieldWidth);
	}
	return CopyDBSheetHelper::CopySheetProperty();
}

HRESULT DBSyncHelper::CopyDbSheetData()
{
	std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
	app_helper::KBatchUpdateCal buc(m_pTarBook->LeakOperator());
	DbSheet::DbtBatchSubmitOptimizer batchSubmitter(m_spTarDbSheetOp.get());

	VS(m_spTarDbSheetOp->SetSheetDescription(m_spSrcDbSheetOp->GetSheetDescription()));
	VS(m_spTarDbSheetOp->SetSheetIcon(m_spSrcDbSheetOp->GetSheetIcon()));
	HRESULT hr = CopyDbSheetFieldsProperty();
	if (FAILED(hr))
		return hr;
	IDbFieldsManager* pSrcFieldsMgr = m_spSrcDbSheetOp->GetFieldsManager();
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	for (auto itFld : m_fldMap)
	{
		ks_stdptr<IDbField> spTarField;
		pTarFieldsMgr->GetField(itFld.second, &spTarField);
		if (m_bNewLookupConvert && spTarField->GetType() == Et_DbSheetField_Lookup)
		{
			ks_stdptr<IDbField_Lookup> spField_lookup = spTarField;
			if (spField_lookup->GetLinkFieldId() == INV_EtDbId)
			{
				spTarField->SetArray(FALSE);
				continue;
			}
		}
		ks_stdptr<IDbField> spSrcField;
		pSrcFieldsMgr->GetField(itFld.first, &spSrcField);
		AdjustFieldData(spSrcField, spTarField);
	}

	auto ms = (std::chrono::steady_clock::now() - begin) / std::chrono::milliseconds(1);
	if(m_param.pTimeStatInfo)
		m_param.pTimeStatInfo->AppendFormat(__X("copyDbSheetData_%d_"), ms);

	return S_OK;
}

HRESULT DBSyncHelper::CopyDbSheetFieldsProperty()
{
	HRESULT hr = S_OK;
	IDbFieldsManager* pSrcFieldsMgr = m_spSrcDbSheetOp->GetFieldsManager();
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	std::vector<EtDbId> formulaFldIds;
	for (auto itFld : m_fldMap)
	{
		ks_stdptr<IDbField> spSrcField;
		pSrcFieldsMgr->GetField(itFld.first, &spSrcField);
		ks_stdptr<IDbField> spTarField;
		pTarFieldsMgr->GetField(itFld.second, &spTarField);
		if (spSrcField->GetType() == Et_DbSheetField_Formula)
			formulaFldIds.emplace_back(itFld.first);
		{
			// 增加一步检测字段转换的情况，用于更新引用表相关的 views 配置
			DbSheet::DbFieldUpdateScope updateScope(spTarField, m_pTarBook, hr);
			if (FAILED(hr))
				return hr;
			hr = onCopyFieldBaseInfo(spSrcField, spTarField);
			if (FAILED(hr))
				return hr;
			hr = CopyDbSheetField(spSrcField, spTarField);
			if (FAILED(hr))
				return hr;
		}
		if (FAILED(hr))
			return hr;
	}
	for (auto fldId : formulaFldIds)
	{
		ks_stdptr<IDbField> spSrcField;
		pSrcFieldsMgr->GetField(fldId, &spSrcField);
		ks_stdptr<IDbField> spTarField;
		pTarFieldsMgr->GetField(m_fldMap[fldId], &spTarField);
		if (spTarField->GetType() != Et_DbSheetField_Formula)
			continue;
		ks_stdptr<IDbField_Formula> spSrcFieldFormula = spSrcField;
		ks_stdptr<IDbField_Formula> spTarFieldFormula = spTarField;
		ks_bstr bstrFormula;
		spSrcFieldFormula->GetFormula(&bstrFormula, FML_CPL_ET_COMPATIBLE);
		if (!bstrFormula)
			spTarFieldFormula->SetFormula(__X(""), FML_CPL_ET_COMPATIBLE);
		else
			spTarFieldFormula->SetFormula(bstrFormula.c_str(), FML_CPL_ET_COMPATIBLE);
	}
	return S_OK;
}

HRESULT DBSyncHelper::onCopyFieldBaseInfo_impl(IDbField* pSrcField, IDbField* pTarField)
{
	if (m_bNewLookupConvert)
	{
		HRESULT hr = S_OK;
		if (pSrcField == nullptr)
		{
			DbSheet::DbFieldUpdateScope updateScope(pTarField, pTarField->GetDbSheetData()->GetBook(), hr);
			if (FAILED(hr))
				return hr;
			pTarField->SetType(Et_DbSheetField_Lookup, nullptr);
			ks_stdptr<IDbField_Lookup> spLookupField = pTarField;
			spLookupField->SetLinkFieldId(INV_EtDbId);
			return S_OK;
		}
		// DbFieldUpdateScope 析构之后
		if (FAILED(hr))
			return hr;
	}
	const IDBIds* pIds = m_spTarDbSheetOp->GetAllFields();
	EtDbIdx idx = pIds->Id2Idx(pTarField->GetID());
	switch (pSrcField->GetType())
	{
	case Et_DbSheetField_Lookup:
	{
		// 如果是新版本，则走下方回落的新逻辑。
		// 否则，保持原有逻辑不变，同下方的Et_DbSheetField_AutoNumber、Et_DbSheetField_Link、Et_DbSheetField_OneWayLink三个case走相同的逻辑
		if (m_bNewLookupConvert)
		{
			ks_stdptr<IDbField_Lookup> spSrcField_Lookup = pSrcField;
			switch (spSrcField_Lookup->GetLookupFunction())
			{
			case DbSheet_Lookup_Function_Origin:
			case DbSheet_Lookup_Function_Unique:
			{
				decltype(GetLookupBaseField({})) baseFieldInfo = GetLookupBaseField(pSrcField);
				switch (baseFieldInfo.second)
				{
				case Et_DbSheetField_AutoNumber:
					VS(pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText));
					VS(pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat()));
					return S_OK;
				case Et_DbSheetField_Formula:
					VS(pTarField->SetTypeForIO(Et_DbSheetField_FormulaResult));
					{
						VS(baseFieldInfo.first->GetDbSheetData()->GetFieldsManager()->Update(TRUE));
						ks_stdptr<IDbField_FormulaResult> spFormulaResultField = pTarField;
						spFormulaResultField->SetFormulaResultType(baseFieldInfo.first->GetValueType());
					}
					VS(pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat()));
					return S_OK;
				default:
					return this->onCopyFieldBaseInfo_impl(baseFieldInfo.first, pTarField);
				}
			}
			case DbSheet_Lookup_Function_Sum:
			case DbSheet_Lookup_Function_Average:
			case DbSheet_Lookup_Function_Max:
			case DbSheet_Lookup_Function_Min:
			case DbSheet_Lookup_Function_Counta:
			case DbSheet_Lookup_Function_CountaUnique:
			case DbSheet_Lookup_Function_ToString:
				// 同步表引用字段回落之前, 这里的类型应该是文本, 回落之后这里的类型只是作一个标记, 后面会重新修正
				VS(pTarField->SetTypeForIO(Et_DbSheetField_Lookup, TRUE));
				return S_OK;
			default:
				ASSERT(false);
				break;
			}
		}
		[[fallthrough]];
	}
	case Et_DbSheetField_AutoNumber:
	case Et_DbSheetField_Link:
	case Et_DbSheetField_OneWayLink:
	case Et_DbSheetField_ParentRecord:
		VS(pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText, TRUE));
		return S_OK;
	case Et_DbSheetField_CreatedBy:
	case Et_DbSheetField_LastModifiedBy:
		VS(pTarField->SetTypeForIO(Et_DbSheetField_Contact, TRUE));
		return S_OK;
	case Et_DbSheetField_CreatedTime:
	case Et_DbSheetField_LastModifiedTime:
		VS(pTarField->SetTypeForIO(Et_DbSheetField_Date));
		VS(pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat()));
		return S_OK;
	case Et_DbSheetField_Formula:
	{
		ks_stdptr<IDbField_Formula> spSrcFormulaField = pSrcField;
		if (spSrcFormulaField->IsCrossSheetFormula())
		{
			VS(pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText, TRUE));
			return S_OK;
		}
		break;
	}
	default:
		break;
	}
	return CopyDBSheetHelper::onCopyFieldBaseInfo(pSrcField, pTarField);
}

HRESULT DBSyncHelper::onCopyFieldBaseInfo(IDbField* pSrcField, IDbField* pTarField)
{
	const IDBIds* pIds = m_spTarDbSheetOp->GetAllFields();
	EtDbIdx idx = pIds->Id2Idx(pTarField->GetID());
	HRESULT hr = onCopyFieldBaseInfo_impl(pSrcField, pTarField);
	switch (pSrcField->GetType())
	{
	case Et_DbSheetField_AutoNumber:
	case Et_DbSheetField_Link:
	case Et_DbSheetField_OneWayLink:
	case Et_DbSheetField_ParentRecord:
	case Et_DbSheetField_CreatedBy:
	case Et_DbSheetField_LastModifiedBy:
	case Et_DbSheetField_CreatedTime:
	case Et_DbSheetField_LastModifiedTime:
	case Et_DbSheetField_Lookup:
	case Et_DbSheetField_Formula:
		VS(pTarField->SetName(pSrcField->GetName(), TRUE, TRUE));
		VS(pTarField->SetDescription(pSrcField->GetDescription()));
		break;
	default:
		break;
	}
	return hr;
}

HRESULT DBSyncHelper::AdjustFieldData(IDbField* pSrcField, IDbField* pTarField)
{
	EtDbId srcFldId = pSrcField->GetID();
	EtDbId tarFldId = pTarField->GetID();
	HRESULT hr = S_OK;
	if(m_bNewLookupConvert && pSrcField->IsSyncLookupField())
	{
		//把源表源字段的Array属性给带到同步表的生成字段上
		pTarField->SetArray(TRUE);
	}

	switch (pSrcField->GetType())
	{
		case Et_DbSheetField_AutoNumber:
			// 编号字段会转换为文本，需要直接取文本来进行赋值
			for (const auto& itRec : m_recMap)
			{
				ks_bstr valStr;
				VS(m_spSrcDbSheetOp->GetValueString(itRec.first, srcFldId, &valStr));
				if (valStr.empty())
					continue;
				hr = m_spTarDbSheetOp->SetValue(itRec.second, tarFldId, valStr.c_str());
				if (FAILED(hr))
					VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, nullptr));
			}
			return S_OK;
		case Et_DbSheetField_Email:
			for (const auto& itRec : m_recMap)
			{
				ks_bstr address;
				VS(m_spSrcDbSheetOp->GetHyperlinkAddress(itRec.first, srcFldId, &address));
				hr = m_spTarDbSheetOp->SetHyperlinkAddress(itRec.second, tarFldId, address.c_str());
				if (FAILED(hr))
				{
					VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, nullptr));
					continue;
				}
				const_token_ptr pToken = nullptr;
				VS(m_spSrcDbSheetOp->GetValueToken(itRec.first, srcFldId, &pToken));
				VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, pToken));
			}
			return S_OK;
		case Et_DbSheetField_Lookup:
			// 如果是新版本，就走下面的逻辑。 
			// 否则，就保持原来逻辑，和下方的Et_DbSheetField_Link以及Et_DbSheetField_OneWayLink这两个case走相同的逻辑。
			if (m_bNewLookupConvert)
			{
				//数据源表的字段为引用字段，在同步表这边的生成字段需要回落，因此Array属性要为True
				pTarField->SetArray(TRUE);
				const auto [_, type] {GetLookupBaseField(pSrcField)};
				if (type != Et_DbSheetField_AutoNumber) /*[[likely]]*/
				{
					for (const auto& itRec : m_recMap)
					{
						const_token_ptr token {};
						VS(m_spSrcDbSheetOp->GetValueToken(itRec.first, srcFldId, &token));
						VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, token));
					}
				}
				else /*[[unlikely]]*/
				{
					ks_stdptr<IDbField_Lookup> spField_Lookup = pSrcField;
					switch (spField_Lookup->GetLookupFunction())
					{
					default:
						ASSERT("新加的引用聚合类型注意修改这里" == nullptr);
						[[fallthrough]];
					case DbSheet_Lookup_Function_Counta:
					case DbSheet_Lookup_Function_Average:
					case DbSheet_Lookup_Function_CountaUnique:
						for (const auto& itRec : m_recMap)
						{
							const_token_ptr token {};
							VS(m_spSrcDbSheetOp->GetValueToken(itRec.first, srcFldId, &token));
							VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, token));
						}
						break;
					case DbSheet_Lookup_Function_Origin:
					case DbSheet_Lookup_Function_Sum:
					case DbSheet_Lookup_Function_Max:
					case DbSheet_Lookup_Function_Min:
					case DbSheet_Lookup_Function_Unique:
					case DbSheet_Lookup_Function_ToString:
						for (const auto& itRec : m_recMap)
						{
							ks_bstr valStr;
							VS(m_spSrcDbSheetOp->GetValueString(itRec.first, srcFldId, &valStr));
							if (valStr.empty())
								continue;
							alg::managed_vstr_token_assist assist;
							assist.create(valStr.c_str());
							m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, assist);
						}
						break;
					}
				}
				return S_OK;
			}
			[[fallthrough]];
		case Et_DbSheetField_Link:
		case Et_DbSheetField_OneWayLink:
		case Et_DbSheetField_ParentRecord:
			// 关联、引用字段直接转为文本字段，相应值需要做转换处理
			for (const auto& itRec : m_recMap)
			{
				ks_bstr oldVal;
				VS(m_spSrcDbSheetOp->GetDisplayString(itRec.first, srcFldId, &oldVal));
				if (oldVal.empty())
				{
					VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, nullptr));
					continue;
				}
				alg::managed_vstr_token_assist tokenAssist;
				tokenAssist.create(oldVal.c_str());
				VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, tokenAssist));
			}
			return S_OK;
		case Et_DbSheetField_Formula:
			if (ks_stdptr<IDbField_Formula> spSrcFieldFormula = pSrcField; !spSrcFieldFormula->IsCrossSheetFormula())
				return S_OK;
			for (const auto& itRec : m_recMap)
			{
				ks_bstr oldVal;
				VS(m_spSrcDbSheetOp->GetDisplayString(itRec.first, srcFldId, &oldVal));
				if (oldVal.empty())
				{
					VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, nullptr));
					continue;
				}
				alg::managed_vstr_token_assist tokenAssist;
				tokenAssist.create(oldVal.c_str());
				VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, tokenAssist));
			}
			return S_OK;
		default:
			break;
	}
	// 值拷贝，不走 io，避免公式删除出错
	for (const auto& itRec : m_recMap)
	{
		const_token_ptr pToken = nullptr;
		VS(m_spSrcDbSheetOp->GetValueToken(itRec.first, srcFldId, &pToken));
		VS(m_spTarDbSheetOp->SetTokenValue(itRec.second, tarFldId, pToken));
	}
	return S_OK;
}

HRESULT DBSyncHelper::adjustFields()
{
	std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
	HRESULT hr = S_OK;
	std::unordered_map<EtDbId, EtDbId> oldFldMap;
	if (m_param.pOldFieldMap)
		oldFldMap = *m_param.pOldFieldMap;
	auto itEnd = oldFldMap.end();
	IDbFieldsManager* pSrcFieldsMgr = m_spSrcDbSheetOp->GetFieldsManager();
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	const IDBIds* pSrcIds = m_spSrcDbSheetOp->GetAllFields();
	const IDBIds* pTarIds = m_spTarDbSheetOp->GetAllFields();
	EtDbIdx fldCnt = pSrcIds->Count();
	std::unordered_set<EtDbId> holdFldId;
	holdFldId.reserve(fldCnt + 1);
	std::vector<EtDbId> insertFldId;
	insertFldId.reserve(fldCnt);
	EtDbId srcFldSourceId = pSrcFieldsMgr->GetSyncFieldSourceId();
	EtDbId srcFldSourceNameId = pSrcFieldsMgr->GetSyncFieldSourceNameId();
	EtDbId oldPrimaryFldId = pTarFieldsMgr->GetPrimaryField();
	EtDbId newPrimaryFldId = pSrcFieldsMgr->GetPrimaryField();
	m_fldSourceId = pTarFieldsMgr->GetSyncFieldSourceId();
	for (EtDbIdx i = 0; i < fldCnt; ++i)
	{
		EtDbId id = pSrcIds->IdAt(i);
		// 一张同步表只需要一个数据源字段，源表存在的时候忽略该字段，同时也忽略数据源名称字段
		if (id == srcFldSourceId || id == srcFldSourceNameId)
			continue;
		ks_stdptr<IDbField> spField;
		pSrcFieldsMgr->GetField(id, &spField);
		if (!isValidType(spField->GetType()))
			continue;
		auto it = oldFldMap.find(id);
		if (it == itEnd || pTarIds->Id2Idx(it->second) == INV_EtDbIdx)
			insertFldId.emplace_back(id);
		else
		{
			m_fldMap.emplace(id, it->second);
			holdFldId.emplace(it->second);
		}
	}
	size_t insertFldCnt = insertFldId.size();
	size_t insertRangeCnt = insertFldCnt;
	if (m_fldSourceId == INV_EtDbId)
		++insertRangeCnt;
	ks_stdptr<IDBSheetRange> spDbRange;
	if (insertRangeCnt > 0)
	{
		hr = m_spTarDbSheetOp->InsertFields(insertRangeCnt, &spDbRange);
		if (FAILED(hr))
			return hr;
	}
	ks_stdptr<IDBSheetViews> spDBSheetViews;
	hr = m_spTarDbSheetOp->GetDbSheetViews(&spDBSheetViews);
	if (FAILED(hr))
		return hr;
	for (int i = 0; i < insertFldCnt; ++i)
	{
		EtDbId newFldId = spDbRange->GetFieldId(i);
		ks_stdptr<IDbField> spField;
		hr = pTarFieldsMgr->GetField(newFldId, &spField);
		if (FAILED(hr))
			return hr;
		spField->SetSyncField(true);

		m_fldMap.emplace(insertFldId[i], newFldId);
		holdFldId.emplace(newFldId);
		class AllDbViewsEnum : public IDbViewEnum
		{
			using CallBack = std::function<HRESULT(IDBSheetView*)>;
		public:
			explicit AllDbViewsEnum(CallBack cb) : m_cb(std::move(cb)) {}
			HRESULT Do(IDBSheetView* pView) override
			{
				return m_cb(pView);
			}
		private:
			CallBack m_cb;
		};
		AllDbViewsEnum viewsEnum([newFldId](IDBSheetView* pView) -> HRESULT {
			ET_DBSheet_ViewType type = pView->GetType();
			if (type == et_DBSheetView_Grid)
				pView->SetFieldsHidden(newFldId, FALSE);
			return S_OK;
		});
		spDBSheetViews->EnumViews(&viewsEnum);
	}
	if (m_fldSourceId == INV_EtDbId)
		m_fldSourceId = spDbRange->GetFieldId(insertFldCnt);
	holdFldId.emplace(m_fldSourceId);
	hr = pTarFieldsMgr->SetSyncFieldSourceIdForIO(m_fldSourceId);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField> spField;
	hr = pTarFieldsMgr->GetField(m_fldSourceId, &spField);
	if (FAILED(hr))
		return hr;
	spField->SetSyncField(true);
	auto itHoldFldIdEnd = holdFldId.end();
	if (oldPrimaryFldId != m_fldMap[newPrimaryFldId])
	{
		hr = pTarFieldsMgr->SetPrimaryField(m_fldMap[newPrimaryFldId]);
		if (FAILED(hr))
			return hr;
	}
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetRecordIds(m_spTarDbSheetOp->GetAllRecords());
	for (EtDbIdx i = 0, tarFldCnt = pTarIds->Count(); i < tarFldCnt; ++i)
	{
		EtDbId id = pTarIds->IdAt(i);
		if (holdFldId.find(id) == itHoldFldIdEnd)
		{
			ks_stdptr<IDbField> spField;
			hr = pTarFieldsMgr->GetField(id, &spField);
			if (FAILED(hr))
				return hr;
			if (spField->IsSyncField())
				spRemoveRange->AddFieldId(id);
		}		
	}
	if (spRemoveRange->GetFieldCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveFields(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}

	auto ms = (std::chrono::steady_clock::now() - begin) / std::chrono::milliseconds(1);
	if(m_param.pTimeStatInfo)
		m_param.pTimeStatInfo->AppendFormat(__X("adjustFields_%d_"), ms);
	return S_OK;
}

HRESULT DBSyncHelper::adjustRecords()
{
	std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
	HRESULT hr = S_OK;
	const IDBIds* pSrcIds = m_spSrcDbSheetOp->GetAllRecords();
	const IDBIds* pTarIds = m_spTarDbSheetOp->GetAllRecords();
	EtDbIdx srcRecCnt = pSrcIds->Count();
	EtDbIdx tarRecCnt = pTarIds->Count();
	std::unordered_map<EtDbId, EtDbId> oldRecMap;
	for (EtDbIdx i = 0; i < tarRecCnt; ++i)
	{
		EtDbId recId = pTarIds->IdAt(i);
		ks_bstr source;
		m_spTarDbSheetOp->GetValueString(recId, m_fldSourceId, &source);
		if (source.empty())
			continue;
		EtDbId srcRecId = DBSyncUtil::decodeRecordId(source.c_str());
		if (srcRecId != INV_EtDbId)
			oldRecMap.emplace(srcRecId, recId);
	}
	auto itEnd = oldRecMap.end();

	std::unordered_set<EtDbId> holdRecId;
	holdRecId.reserve(srcRecCnt);
	std::vector<EtDbId> insertRecId;
	insertRecId.reserve(srcRecCnt);
	for (EtDbIdx i = 0; i < srcRecCnt; ++i)
	{
		EtDbId id = pSrcIds->IdAt(i);
		auto it = oldRecMap.find(id);
		if (it == itEnd)
			insertRecId.emplace_back(id);
		else
		{
			m_recMap.emplace(id, it->second);
			holdRecId.emplace(it->second);
		}
	}
	auto itHoldRecIdEnd = holdRecId.end();
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetFieldIds(m_spTarDbSheetOp->GetAllFields());
	for (EtDbIdx i = 0; i < tarRecCnt; ++i)
	{
		EtDbId id = pTarIds->IdAt(i);
		if (holdRecId.find(id) == itHoldRecIdEnd)
			spRemoveRange->AddRecordId(id);
	}
	if (spRemoveRange->GetRecordCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveRecords(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	size_t insertRecCnt = insertRecId.size();
	if (insertRecCnt > 0)
	{
		ks_stdptr<IDBSheetRange> spDbRange;
		hr = m_spTarDbSheetOp->InsertRecords(insertRecCnt, &spDbRange);
		if (FAILED(hr))
			return hr;
		for (int i = 0; i < insertRecCnt; ++i)
			m_recMap.emplace(insertRecId[i], spDbRange->GetRecordId(i));
	}
	ks_stdptr<IDBSheetRange> spClearRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spClearRange);
	spClearRange->SetRecordIds(pTarIds);
    const IDBIds* pIds = m_spTarDbSheetOp->GetAllFields();
    EtDbIdx cnt = pIds->Count();
    for (EtDbIdx i = 0; i < cnt; ++i)
    {
        EtDbId id = pIds->IdAt(i);
        ks_stdptr<IDbField> spField;
        hr = m_spTarDbSheetOp->GetFieldsManager()->GetField(id, &spField);
        if (FAILED(hr))
            return hr;
		if (spField->IsSyncField())
			spClearRange->AddFieldId(id);
    }
	
	hr = m_spTarDbSheetOp->Clear(spClearRange);

	auto ms = (std::chrono::steady_clock::now() - begin) / std::chrono::milliseconds(1);
	if(m_param.pTimeStatInfo)
		m_param.pTimeStatInfo->AppendFormat(__X("adjustRecords_%d_"), ms);

	return hr;
}

HRESULT DBSyncHelper::setInfoField()
{
	std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
	// 初次建立引用关系的时候，引用表需要冗余一个’记录来源‘的超链接字段
	IDBSheetCtx* pCtx = _appcore_GainDbSheetContext();
	HRESULT hr = S_OK;
	IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	ks_stdptr<IDbField> spInfoField;
	hr = pNewFieldsMgr->GetField(m_fldSourceId, &spInfoField);
	if (FAILED(hr))
		return hr;
	VS(spInfoField->SetTypeForIO(Et_DbSheetField_Url, TRUE));
	hr = spInfoField->SetName(m_param.fldSourceName, TRUE, TRUE);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField_Hyperlink> spInfoUrl = spInfoField;
	spInfoUrl->SetDisplayText(m_param.fldSourceName);

	QString templateStr = krt::fromUtf16(m_param.urlTemplate);
	templateStr.replace(QRegularExpression(":sheetStId"), QString::number(m_pSrcSheet->GetStId()));
	const QRegularExpression reRecId(":recordId");
	EtDbIdStr buf;
	for (const auto& itRec : m_recMap)
	{
		VS(pCtx->EncodeEtDbId(itRec.first, &buf));
		QString info = templateStr;
		info.replace(reRecId, krt::fromUtf16(buf));
		DBSyncUtil::encodeToBase64(info);
		PCWSTR valueStr = krt::utf16(info);
		alg::managed_token_assist mta;
		hr = pCtx->Text2DbHyperlinkToken(valueStr, valueStr, &mta);
		if (FAILED(hr))
			return hr;
		hr = m_spTarDbSheetOp->SetTokenValue(itRec.second, m_fldSourceId, mta);
		if (FAILED(hr))
			return hr;
	}

	auto ms = (std::chrono::steady_clock::now() - begin) / std::chrono::milliseconds(1);
	if(m_param.pTimeStatInfo)
		m_param.pTimeStatInfo->AppendFormat(__X("setInfoField_%d_"), ms);
	return S_OK;
}

HRESULT CopyDBDashboardHelper::Init(IKWorksheet* pWorkSheet, IKWorksheet* pNewWorkSheet)
{
	if (!pWorkSheet || !pNewWorkSheet)
		return E_FAIL;
	m_pWorkSheet = pWorkSheet;
	m_pNewWorkSheet = pNewWorkSheet;
	return S_OK;
}

HRESULT CopyDBDashboardHelper::Prepare4Template(std::unordered_map<UINT, UINT>* pMap)
{
	m_pSheetIdMap = pMap;
	return S_OK;
}

HRESULT CopyDBDashboardHelper::CopyStatisticModules(ISheet* pSheet, ISheet* pNewSheet)
{
    ks_stdptr<IDBChartStatisticMgr> spModules;
    ks_stdptr<IDBChartStatisticMgr> spNewModules;
    DbSheet::GetDBChartStatisticMgr(pSheet, &spModules);
    DbSheet::GetDBChartStatisticMgr(pNewSheet, &spNewModules);
    if (!spModules || !spNewModules)
        return E_FAIL;

    std::unique_ptr<IImportSheetIdMap> spImportSheetIdMap = GetImportSheetIdMap(m_pSheetIdMap);
    HRESULT hr = S_OK;
    for (EtDbIdx i = 0, cnt = spModules->GetSize(); i < cnt; ++i)
    {
        ks_stdptr<IDBChartStatisticModule> spSourceModule;
        spModules->GetItemAt(i, &spSourceModule);
        if (!spSourceModule)
            return E_FAIL;
        ks_stdptr<IDBChartStatisticModule> spTargetModule;
        hr = spNewModules->CreateModuleForIo(spSourceModule->GetId(), &spTargetModule);
        if (FAILED(hr))
            return hr;
        hr = spTargetModule->SetWebExtensionInfo(spSourceModule->GetWebExtensionInfo());
        if (FAILED(hr))
            return hr;
        UINT srcStId = spSourceModule->GetDataSourceId();
        if (m_pSheetIdMap)
        {
            // 图表导入时srcStId可能发生改变
            auto it = m_pSheetIdMap->find(srcStId);
            if (it == m_pSheetIdMap->end())
            {
                VS(spTargetModule->SetGroupLimitNum(spSourceModule->GetGroupLimitNum()));
                VS(spTargetModule->SetStatsLimitNum(spSourceModule->GetStatsLimitNum()));
                continue;
            }
            spTargetModule->SetDataSourceIdForIo(it->second);
            hr = spSourceModule->CopyTo(spTargetModule, spImportSheetIdMap.get());
            if (FAILED(hr))
                return hr;
        }
        else
        {
            // 图表复制
            if (srcStId == 0)
            {
                VS(spTargetModule->SetGroupLimitNum(spSourceModule->GetGroupLimitNum()));
                VS(spTargetModule->SetStatsLimitNum(spSourceModule->GetStatsLimitNum()));
                continue;
            }
            hr = spSourceModule->CopyTo(spTargetModule);
            if (FAILED(hr))
                return hr;
        }
    }
    return S_OK;
}

HRESULT CopyDBDashboardHelper::CopyWorksheetCharts(IDBDashBoardDataOp* pSrcDashboardData, IDBDashBoardDataOp* pDstDashboardData)
{
    IDbWorksheetChartMgr* pSrcWorksheetChartMgr = pSrcDashboardData->GetWorksheetChartMgr();
    IDbWorksheetChartMgr* pDstWorksheetChartMgr = pDstDashboardData->GetWorksheetChartMgr();
    EtDbIdx worksheetChartCount = pSrcWorksheetChartMgr->GetSize();
    for (EtDbIdx i = 0; i < worksheetChartCount; ++i)
    {
        ks_stdptr<IDbWorksheetChart> spSrcChart;
        pSrcWorksheetChartMgr->GetChartAt(i, &spSrcChart);
        ks_stdptr<IDbWorksheetChart> spNewChart;
        HRESULT hr = pDstWorksheetChartMgr->CreateChartForIo(spSrcChart->GetId(), spSrcChart->GetWebExtensionKey(), &spNewChart);
        if (FAILED(hr))
            return hr;

        hr = spSrcChart->CopyTo(spNewChart);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT CopyDBDashboardHelper::CopyViews()
{
	IBook* pBook = m_pNewWorkSheet->GetWorkbook()->GetBook();
	ks_stdptr<IKWebExtensionMgr> spWebExtMgr;
	pBook->GetExtDataItem(edBookWebExtensionMgr, (IUnknown**)&spWebExtMgr);
    ASSERT(spWebExtMgr != nullptr);
	ISheet* pNewSheet = m_pNewWorkSheet->GetSheet();
	UINT webextensionCount = 0;
	spWebExtMgr->GetWebExtensionCount(pNewSheet, &webextensionCount);
	for (int i = 0; i < webextensionCount; ++i)
	{
		ks_stdptr<IKWebExtension> spWebExtension;
		HRESULT hr = spWebExtMgr->GetWebExtension(pNewSheet, i, &spWebExtension);
		if (FAILED(hr))
            continue;

		if (spWebExtension->GetWebShapeType() != WET_DbView)
			continue;

		PCWSTR webextensionKey = spWebExtension->GetWebExtensionKey();
		ks_stdptr<IEtWebExtension_View> spWebExtView = spWebExtension;
		UINT srcSheetId = 0;
		if (m_pSheetIdMap)
		{
			// 仪表盘导入时srcStId可能没有导入
			auto it = m_pSheetIdMap->find(spWebExtView->GetSheetId());
			srcSheetId = it == m_pSheetIdMap->end() ? 0 : it->second;
		}
		else
		{
			srcSheetId = spWebExtView->GetSheetId();
		}

		if (srcSheetId == 0)
		{
			spWebExtView->SetSheetId(0);
			spWebExtView->SetViewId(0);
			continue;
		}

		IDX srcSheetIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(srcSheetId, &srcSheetIdx);
		if (srcSheetIdx == INVALIDIDX)
			continue;
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(srcSheetIdx, &spSheet);
		ks_stdptr<IDBSheetViews> spDbSheetViews;
		spSheet->GetExtDataItem(edSheetDbViews, (IUnknown**)&spDbSheetViews);
		if (spDbSheetViews == nullptr)
			continue;

		ks_stdptr<IDBSheetView> spSrcView;
		spDbSheetViews->GetItemById(spWebExtView->GetViewId(), &spSrcView);
		if (!spSrcView)
			continue;

		ks_wstring nameStr;
		hr = GetValidName(spDbSheetViews, spSrcView->GetName(), nameStr);
		if (hr == E_DBSHEET_VIEW_INVALID_NAME)
			return hr;

		ks_stdptr<IDBSheetView> spNewView;
		hr = spDbSheetViews->CreateView(spSrcView->GetType(), nameStr.c_str(), false, Et_DBSheetViewUse_ForDashboard, &spNewView);
		if (!spNewView)
			continue;

		hr = spSrcView->CopyTo(spNewView, TRUE, TRUE);
		if (FAILED(hr))
			return hr;

		WebExtInfo info;
		info.sheetStId = m_pNewWorkSheet->GetSheet()->GetStId();
		info.webExtKey = webextensionKey;
		spNewView->SetWebExtInfo(info);

		spWebExtView->SetSheetId(spSheet->GetStId());
		spWebExtView->SetViewId(spNewView->GetId());
	}
    return S_OK;
}

HRESULT CopyDBDashboardHelper::CopyFilters(IDBDashBoardDataOp* pSrcDashboardData, IDBDashBoardDataOp* pDstDashboardData)
{
    IDbDashboardFilterMgr* pSrcFilterMgr = pSrcDashboardData->GetFilterMgr();
    IDbDashboardFilterMgr* pDstFilterMgr = pDstDashboardData->GetFilterMgr();
    std::unique_ptr<IImportSheetIdMap> spImportSheetIdMap = GetImportSheetIdMap(m_pSheetIdMap);
    return pSrcFilterMgr->CopyTo(pDstFilterMgr, spImportSheetIdMap.get());
}

HRESULT CopyDBDashboardHelper::GetValidName(IDBSheetViews* pViews, PCWSTR pBaseName, ks_wstring& pValidName)
{
	HRESULT hr = pViews->IsValidViewName(pBaseName);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
		return hr;

    pValidName = pBaseName;
	if (hr == E_DBSHEET_VIEW_NAME_CONFLICT)
	{
		int postfix = 2;
		do
		{
			QString newName = QString::fromUtf16(pBaseName) + QString::number(postfix, 10);
			postfix++;
			pValidName = krt::utf16(newName);
		} while (E_DBSHEET_VIEW_NAME_CONFLICT == pViews->IsValidViewName(pValidName.c_str()));
	}
    return S_OK;
}

HRESULT CopyDBDashboardHelper::ExecCopy()
{
	ISheet* pSheet = m_pWorkSheet->GetSheet();
	ISheet* pNewSheet = m_pNewWorkSheet->GetSheet();
	ks_stdptr<IDBDashBoardDataOp> spDataOp;
	DbSheet::GetDBDashBoardOp(pSheet, &spDataOp);
	ks_stdptr<IDBDashBoardDataOp> spNewDataOp;
	DbSheet::GetDBDashBoardOp(pNewSheet, &spNewDataOp);
	if (!spDataOp || !spNewDataOp)
		return E_FAIL;
	spNewDataOp->SetSheetDescription(spDataOp->GetSheetDescription());
	spNewDataOp->SetSheetIcon(spDataOp->GetSheetIcon());
    HRESULT hr = CopyStatisticModules(pSheet, pNewSheet);
    if (FAILED(hr))
        return hr;

    hr = CopyWorksheetCharts(spDataOp, spNewDataOp);
    if (FAILED(hr))
        return hr;

	hr = CopyViews();
	if (FAILED(hr))
		return hr;

    // as暂不支持仪表盘筛选器
    if (pNewSheet->GetBMP()->bDbSheet)
    {
        hr = CopyFilters(spDataOp, spNewDataOp);
        if (FAILED(hr))
            return hr;
    }
	return S_OK;
}

HRESULT CopyDBFpSheetHelper::Init(IKWorksheet* pWorkSheet, IKWorksheet* pNewWorkSheet)
{
	if (!pWorkSheet || !pNewWorkSheet)
		return E_FAIL;
	m_pWorkSheet = pWorkSheet;
	m_pNewWorkSheet = pNewWorkSheet;
	return S_OK;
}

HRESULT CopyDBFpSheetHelper::ExecCopy()
{
	if (!m_pAttachmentIdMap)
		return E_FAIL;
	ISheet* pSheet = m_pWorkSheet->GetSheet();
	ISheet* pNewSheet = m_pNewWorkSheet->GetSheet();
	ks_stdptr<IFPSheetData> spDataOp;
	DbSheet::GetDBFpSheetOp(pSheet, &spDataOp);
	ks_stdptr<IFPSheetData> spNewDataOp;
	DbSheet::GetDBFpSheetOp(pNewSheet, &spNewDataOp);
	if (!spDataOp || !spNewDataOp)
		return E_FAIL;
	GlobalSharedString srcContendId(spDataOp->GetContentId());
	auto itContendId = m_pAttachmentIdMap->find(srcContendId);
	if (itContendId == m_pAttachmentIdMap->end())
		return E_FAIL;
	PCWSTR newNoteId = itContendId->second.c_str();
	if (!newNoteId)
		return E_FAIL;
	spNewDataOp->SetContentId(newNoteId);
	spNewDataOp->SetIcon(spDataOp->GetIcon());

	return S_OK;
}

DBSyncMergeHelper::DBSyncMergeHelper(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet, bool bNewLookupConvert)
	: CopyDBSheetHelper(pSrcWorkSheet, pTarWorkSheet, nullptr, bNewLookupConvert)
{
	VS(_applogic_CreateObject(CLSID_KNumberFormatter, IID_IET_NumberFormatter, (void**)&m_spNumberFormatter));
}

HRESULT DBSyncMergeHelper::Init(const DBSyncMergeParam& syncParam)
{
	if (!syncParam.pcwUrlTemplate || !syncParam.pcwFldSourceName || !syncParam.pcwFldSourceFileName)
		return E_INVALIDARG;
	DbSheetCopyParam param;
	param.copyFormulaCustom = true;
	param.needResetAutoValue = false;
	param.needValidateName = true;
	m_param = syncParam;
	return CopyDBSheetHelper::Init(param);
}

HRESULT DBSyncMergeHelper::PrepareMerge()
{
	CustomCtxController customCtxController(m_pSrcBook, m_pTarBook);
	HRESULT hr = S_OK;
	CopyDBSheetHelper::CopySheetProperty();
	const IDBIds* pTarRecIds = m_spTarDbSheetOp->GetAllRecords();
	EtDbIdx curRecCnt = pTarRecIds->Count();
	m_stHoldFieldIdSet.clear();
	m_stRecordMap.clear();
	ClearOldField();
	if (curRecCnt > 0)
	{
		fillOldRecordMap();
		ks_stdptr<IDBSheetRange> spClearRange;
		m_spTarDbSheetOp->CreateDBSheetRange(&spClearRange);
		spClearRange->SetFieldIds(m_spTarDbSheetOp->GetAllFields());
		spClearRange->SetRecordIds(pTarRecIds);
		if (spClearRange->GetRecordCnt() > 0)
		{
			// 为了保证关联记录不失效，不能直接删除旧记录，只调用Clear清理内容
			hr = m_spTarDbSheetOp->Clear(spClearRange);
			if (FAILED(hr))
				return hr;
		}
	}
	m_curRecCnt = 0;
	return S_OK;
}

HRESULT DBSyncMergeHelper::ExecMerge()
{
	CustomCtxController customCtxController(m_pSrcBook, m_pTarBook);
	HRESULT hr = S_OK;
	if (nullptr == m_spTarDbSheetOp)
		return E_FAIL;
	if (NULL == m_pcwCurSourceFileName)
		return  E_FAIL;
	size_t srcSheetCnt = m_srcWorksheetsVec.size();
	m_stFieldMappingItemsVec.clear();
	ISheet* pTarSheet= m_pTarWorkSheet->GetSheet();
	IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	bool bIsFirstInsert = false;
	bool bFromOldField = false;
	int initFieldWidth = 0;
	const IDBIds* pTarFieldIds = m_spTarDbSheetOp->GetAllFields();
	EtDbIdx curFieldCnt = pTarFieldIds->Count();
	for (int i = 0; i < srcSheetCnt; ++i)
	{
		etoldapi::_Worksheet* pSrcWorksheet = static_cast<etoldapi::_Worksheet*>(m_srcWorksheetsVec[i]);
		ISheet* pSrcSheet = pSrcWorksheet->GetSheet();
		ks_stdptr<IDBSheetOp> spSrcDbSheetOp;
		VS(DbSheet::GetDBSheetOp(pSrcSheet, &spSrcDbSheetOp));
		UINT32 uStId = pSrcSheet->GetStId();
		pSrcSheet->GetName(&m_pcwCurSourceSheetName);
		m_pcwCurSourceSheetName = addHoldName(m_pcwCurSourceSheetName);
		if (NULL == m_pcwCurSourceSheetName)
			return  E_FAIL;
		IDbFieldsManager* pSrcFieldsMgr = spSrcDbSheetOp->GetFieldsManager();
		pSrcFieldsMgr->Update(TRUE);
		EtDbId srcFldSourceId = pSrcFieldsMgr->GetSyncFieldSourceId();
		EtDbId srcFldSourceNameId = pSrcFieldsMgr->GetSyncFieldSourceNameId();
		const IDBIds* pSrcFieldIds = spSrcDbSheetOp->GetAllFields();
		const IDBIds* pSrcRecordIds = spSrcDbSheetOp->GetAllRecords();
		EtDbIdx srcFieldCnt = pSrcFieldIds->Count();
		EtDbIdx srcRecordsCnt = pSrcRecordIds->Count();
		EtDbIdx totalNeedCnt = m_curRecCnt + srcRecordsCnt;
		if (m_param.m_iMaxSheetRows <= totalNeedCnt)
			return E_DBSHEET_MERGE_DB_REACH_MAX_SHEET_ROWS;
		m_stFieldMappingItemsVec.emplace_back(uStId);
		for (EtDbIdx j = 0; j < srcFieldCnt; ++j)
		{
			EtDbId id = pSrcFieldIds->IdAt(j);
			// 源表的数据源字段不需要拷贝，数据源名称字段其实也不需要同步
			if (id == srcFldSourceId || id == srcFldSourceNameId)
				continue;
			ks_stdptr<IDbField> spField;
			ks_stdptr<IDbField> spNewField;
			hr = pSrcFieldsMgr->GetField(id, &spField);
			if (FAILED(hr))
				return hr;
			// 触发器及按钮类型的字段不支持合并
			if (Et_DbSheetField_Automations == spField->GetType() || Et_DbSheetField_Button == spField->GetType())
				continue;
			EtDbId tarFieldId = INV_EtDbId;
			bIsFirstInsert = false;
			bFromOldField = false;
			if (!checkCanReuseCurField(spField, tarFieldId))
			{
				bFromOldField = findFromOldFieldMapping(uStId, id, tarFieldId);
				if (!bFromOldField)
				{
					bIsFirstInsert = true;
					hr = createField(tarFieldId);
					if (FAILED(hr))
						return hr;
				}
			}
			hr = pNewFieldsMgr->GetField(tarFieldId, &spNewField);
			if (FAILED(hr))
				return hr;
			bool bIsFirstAdjust = bIsFirstInsert || bFromOldField;
			hr = adjustFieldBaseInfo(spField, spNewField, bIsFirstAdjust);
			if (FAILED(hr))
				return hr;
			addOriginFieldInfoMapping(tarFieldId, spField);
			m_stFieldMappingItemsVec[i].m_fieldIdMap[id] = tarFieldId;
			if (bIsFirstInsert)
			{
				initFieldWidth = 0;
				pSrcSheet->GetColWidth(j, &initFieldWidth);
				if (initFieldWidth < MIN_FIELD_WIDTH)
					initFieldWidth = MIN_FIELD_WIDTH;
				pTarSheet->SetColWidth(curFieldCnt, curFieldCnt, initFieldWidth);
				curFieldCnt++;
			}
			if (0 == m_curRecCnt && id == pSrcFieldsMgr->GetPrimaryField())
			{
				CopySheetProperty();
				pNewFieldsMgr->SetPrimaryField(tarFieldId);
			}
			// 然后再拷贝拓展字段信息
			hr = adjustFieldExInfo(spField, spNewField, bIsFirstAdjust);
			if (FAILED(hr))
				return hr;
			if (m_param.m_bNeedCopySheetData)
			{
				hr = adjustRecords(spSrcDbSheetOp, uStId);
				if (FAILED(hr))
					return hr;
				hr = copyRecords(spSrcDbSheetOp, spField, spNewField, uStId);
				if (FAILED(hr))
					return hr;
			}
		}
		m_curRecCnt += srcRecordsCnt;
	}
	return S_OK;
}

HRESULT DBSyncMergeHelper::AdjustMerge()
{
	CustomCtxController customCtxController(m_pSrcBook, m_pTarBook);
	HRESULT hr = adjustViews();
	if (FAILED(hr))
		return hr;
	if (m_param.m_bNeedCopySheetData)
	{
		hr = addSourceInfoField();
		if (FAILED(hr))
			return hr;
	}
	return removeUnusedField();
}

HRESULT DBSyncMergeHelper::removeUnusedField()
{
	const IDBIds* pTarFieldIds = m_spTarDbSheetOp->GetAllFields();
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetRecordIds(m_spTarDbSheetOp->GetAllRecords());
	for (EtDbIdx i = 0, tarFldCnt = pTarFieldIds->Count(); i < tarFldCnt; ++i)
	{
		EtDbId id = pTarFieldIds->IdAt(i);
		if (m_stHoldFieldIdSet.find(id) == m_stHoldFieldIdSet.end())
			spRemoveRange->AddFieldId(id);
	}
	if (spRemoveRange->GetFieldCnt() > 0)
	{
		HRESULT hr = m_spTarDbSheetOp->RemoveFields(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

HRESULT DBSyncMergeHelper::createField(EtDbId& fldId)
{
	ks_stdptr<IDBSheetRange> spDbRange;
	HRESULT hr = m_spTarDbSheetOp->InsertFields(1, &spDbRange);
	if (FAILED(hr))
		return hr;
	fldId = spDbRange->GetFieldId(0);
	m_stNewFieldIdSet.insert(fldId);
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustSourceField()
{
	IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	HRESULT hr = pNewFieldsMgr->SetSyncFieldSourceIdForIO(m_sourceFldId);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField> spInfoField;
	hr = pNewFieldsMgr->GetField(m_sourceFldId, &spInfoField);
	if (FAILED(hr))
		return hr;
	VS(spInfoField->SetTypeForIO(Et_DbSheetField_Url));
	VS(spInfoField->SetSyncFieldForIO(TRUE));
	VS(spInfoField->SetName(m_param.pcwFldSourceName, TRUE, TRUE));
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustSourceNameField()
{
	IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	HRESULT hr = pNewFieldsMgr->SetSyncFieldSourceNameIdForIO(m_sourceNameFldId);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField> spSourceFileNameField;
	hr = pNewFieldsMgr->GetField(m_sourceNameFldId, &spSourceFileNameField);
	if (FAILED(hr))
		return hr;
	VS(spSourceFileNameField->SetTypeForIO(Et_DbSheetField_MultiLineText));
	VS(spSourceFileNameField->SetSyncFieldForIO(TRUE));
	VS(spSourceFileNameField->SetName(m_param.pcwFldSourceFileName, TRUE, TRUE));
	return S_OK;
}

HRESULT DBSyncMergeHelper::addSourceInfoField()
{
	if (nullptr == m_spTarDbSheetOp)
		return E_FAIL;
	const IDBIds* pTarFieldIds = m_spTarDbSheetOp->GetAllFields();
	EtDbIdx oldFieldCnt = pTarFieldIds->Count();
	HRESULT hr = S_OK;
	if (INV_EtDbId == m_sourceFldId)
	{
		hr = createField(m_sourceFldId);
		if (FAILED(hr))
			return hr;
	}	
	if (INV_EtDbId == m_sourceNameFldId)
	{
		hr = createField(m_sourceNameFldId);
		if (FAILED(hr))
			return hr;
	}
	if (INV_EtDbId == m_sourceFldId || INV_EtDbId == m_sourceNameFldId)
		return E_FAIL;
	VS(adjustSourceField());
	VS(adjustSourceNameField());
	m_stHoldFieldIdSet.insert(m_sourceFldId);
	m_stHoldFieldIdSet.insert(m_sourceNameFldId);
	IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	ks_stdptr<IDbField> spInfoField;
	hr = pNewFieldsMgr->GetField(m_sourceFldId, &spInfoField);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField_Hyperlink> spInfoUrl = spInfoField;
	spInfoUrl->SetDisplayText(m_param.pcwFldSourceName);
	IDBSheetCtx* pCtx = _appcore_GainDbSheetContext();
	const IDBIds* pTarRecordIds = m_spTarDbSheetOp->GetAllRecords();
	QString templateStr = krt::fromUtf16(m_param.pcwUrlTemplate);
	const QRegularExpression reRecId(":recordId");
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetFieldIds(pTarFieldIds);
	for (EtDbIdx i = 0, recCnt = pTarRecordIds->Count(); i < recCnt; ++i)
	{
		EtDbId tarRecId = pTarRecordIds->IdAt(i);
		auto iter = m_stRecordMap.find(tarRecId);
		if (iter == m_stRecordMap.end())
		{
			spRemoveRange->AddRecordId(tarRecId);
			continue;
		}
		const RecordInfo& stSrcRecordInfo = iter->second;
		EtDbIdStr buf;
		VS(pCtx->EncodeEtDbId(stSrcRecordInfo.m_recId, &buf));
		QString info = templateStr;
		info.replace(QRegularExpression(":sheetStId"), QString::number(stSrcRecordInfo.m_uStId));
		info.replace(QRegularExpression(":fileId"), krt::fromUtf16(stSrcRecordInfo.m_pcwFileId));
		info.replace(QRegularExpression(":fileType"), krt::fromUtf16(stSrcRecordInfo.m_pcwFileType));
		info.replace(reRecId, krt::fromUtf16(buf));
		DBSyncUtil::encodeToBase64(info);
		PCWSTR valueStr = krt::utf16(info);
		alg::managed_token_assist mta;
		hr = pCtx->Text2DbHyperlinkToken(valueStr, valueStr, &mta);
		if (FAILED(hr))
			return hr;
		hr = m_spTarDbSheetOp->SetTokenValue(tarRecId, m_sourceFldId, mta);
		if (FAILED(hr))
			return hr;

		// 设置数据源名称记录
		ks_wstring wsSourceFileName;
		// TODO: 考虑国际化场景
		wsSourceFileName.Format(__X("%s，%s"), stSrcRecordInfo.m_pcwFileName, stSrcRecordInfo.m_pcwSheetName);
		hr = m_spTarDbSheetOp->SetValue(tarRecId, m_sourceNameFldId, wsSourceFileName.c_str());
		if (FAILED(hr))
			return hr;
	}
	EtDbIdx newFieldCnt = pTarFieldIds->Count();
	if (oldFieldCnt < newFieldCnt)
	{
		// setColWidth
		ISheet* pTarSheet= m_pTarWorkSheet->GetSheet();
		pTarSheet->SetColWidth(oldFieldCnt, newFieldCnt - 1, MIN_FIELD_WIDTH);
	}
	if (spRemoveRange->GetRecordCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveRecords(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

void DBSyncMergeHelper::MoveFieldMappingItems(std::vector<FieldMappingItem>& stFieldMappingItemsVec)
{
	stFieldMappingItemsVec = std::move(m_stFieldMappingItemsVec);
}

HRESULT DBSyncMergeHelper::copyRecords(IDBSheetOp* pSrcDbSheetOp, IDbField* pSrcField, IDbField* pTarField, UINT32 uStId)
{
	const IDBIds* srcRecordIds = pSrcDbSheetOp->GetAllRecords();
	const IDBIds* tarRecordIds = m_spTarDbSheetOp->GetAllRecords();
	DBSyncMergeHelper::CopyFieldDataInfo stInfo;
	stInfo.m_srcFldId = pSrcField->GetID();
	stInfo.m_tarFldId = pTarField->GetID();
	EtDbIdx recCnt = srcRecordIds->Count();
	PCWSTR pcwFileType = m_param.m_pcwDbSheetUrlFileType;
	if (m_pSrcBook->GetBMP()->bKsheet)
		pcwFileType = m_param.m_pcwKSheetUrlFileType;
	for (EtDbIdx i =  0; i < recCnt; ++i)
	{
		stInfo.m_srcRecId = srcRecordIds->IdAt(i);
		RecordInfo stSrcRecordInfo{stInfo.m_srcRecId, uStId, m_pcwCurSourceFileId, m_pcwCurSourceFileName, m_pcwCurSourceSheetName, pcwFileType};
		stInfo.m_tarRecId = m_stOldRecordMap[stSrcRecordInfo];
		stInfo.m_bIsArray = pTarField->IsArray();
		VS(copyRecordDetail(pSrcDbSheetOp, pSrcField->GetType(), stInfo));
		m_stRecordMap[stInfo.m_tarRecId] = stSrcRecordInfo;
	}
	return S_OK;
}

void DBSyncMergeHelper::addOriginFieldInfoMapping(EtDbId tarFldId, IDbField* pSrcField)
{
	m_stHoldFieldIdSet.insert(tarFldId);
	FieldBaseInfo stFieldBaseInfo(pSrcField->GetType(), pSrcField->GetName(), __X(""), pSrcField->IsArray());
	getCurrencySymbol(pSrcField, stFieldBaseInfo.m_strCurrencySymbol);
	switch (pSrcField->GetType())
	{
		case Et_DbSheetField_Lookup:
			{
				// 记录引用字段与实质字段的映射关系
				auto& stLookupBaseFieldMap = m_stLookupFieldMapping[stFieldBaseInfo];
				ks_stdptr<IDbField_Lookup> spLookupField = pSrcField;
				ks_stdptr<IDbField> spLookupBaseField;
				HRESULT hr = spLookupField->GetLookupBaseField(&spLookupBaseField);
				if (FAILED(hr) || nullptr == spLookupBaseField)
					return;
				LookupFieldTypeInfo stLookupFieldTypeInfo(spLookupBaseField->GetType(), spLookupBaseField->GetValueType());
				stLookupBaseFieldMap[stLookupFieldTypeInfo] = tarFldId;
			}
			break;
		case Et_DbSheetField_Formula:
		case Et_DbSheetField_FormulaResult:
			{
				// 记录公式的映射关系
				FieldValueTypeInfo stFieldValueInfo(pSrcField->GetValueType(), pSrcField->GetName(),
													stFieldBaseInfo.m_strCurrencySymbol.c_str(), getNumFmtCat(pSrcField->GetNumberFormat()));
				m_stFormulaValueTypeMap[stFieldValueInfo] = tarFldId;
			}
			break;
		default:
			m_stOriginFieldBaseInfoMapping[stFieldBaseInfo] = tarFldId;
	}
}

bool DBSyncMergeHelper::checkCanReuseCurField(IDbField* pField, EtDbId& tarFieldId)
{
	if (nullptr == pField)
		return false;
	FieldBaseInfo stFieldBaseInfo(pField->GetType(), pField->GetName(), __X(""), pField->IsArray());
	getCurrencySymbol(pField, stFieldBaseInfo.m_strCurrencySymbol);
	switch (pField->GetType())
	{
		case Et_DbSheetField_Lookup:
			{
				// 当引用字段的字段名和实质类型相同时，才作为同一字段合并
				ks_stdptr<IDbField_Lookup> spLookupField = pField;
				ks_stdptr<IDbField> spLookupBaseField;
				HRESULT hr = spLookupField->GetLookupBaseField(&spLookupBaseField);
				if (FAILED(hr) || nullptr == spLookupBaseField)
					return false;
				auto& stLookupBaseFieldMap = m_stLookupFieldMapping[stFieldBaseInfo];
				LookupFieldTypeInfo stLookupFieldTypeInfo(spLookupBaseField->GetType(), spLookupBaseField->GetValueType());
				auto iter = stLookupBaseFieldMap.find(stLookupFieldTypeInfo);
				if (iter == stLookupBaseFieldMap.end())
					return false;
				tarFieldId = iter->second;
				return true;
			}
			break;
		case Et_DbSheetField_Formula:
		case Et_DbSheetField_FormulaResult:
			{
				// 当字段名、值类型和数字格式类型完全一致时，才作为同一字段合并
				FieldValueTypeInfo stFieldValueTypeInfo(pField->GetValueType(), pField->GetName(), 
														stFieldBaseInfo.m_strCurrencySymbol.c_str(), getNumFmtCat(pField->GetNumberFormat()));
				return findFromFormulaFieldInfoMap(m_stFormulaValueTypeMap, stFieldValueTypeInfo, tarFieldId);
			}
			break;
		default:
			break;
	}
	return findFromFieldBaseInfoMap(m_stOriginFieldBaseInfoMapping, stFieldBaseInfo, tarFieldId);
}

bool DBSyncMergeHelper::findFromOldFieldMapping(UINT uSheetStId, EtDbId srcFldId, EtDbId& tarFldId)
{
	auto iter = m_stOldFieldMapping.find(uSheetStId);
	if (iter == m_stOldFieldMapping.end())
		return false;
	auto& stOldFieldMap = iter->second->m_fieldIdMap;
	auto iter2 = stOldFieldMap.find(srcFldId);
	if (iter2 == stOldFieldMap.end())
		return false;
	// 此时旧字段已经被使用了，而且源字段通过合并规则找不到对应字段，就不使用旧的映射关系
	if (m_stHoldFieldIdSet.find(iter2->second) != m_stHoldFieldIdSet.end())
		return false;
	tarFldId = iter2->second;
	return true;
}

bool DBSyncMergeHelper::findFromFieldBaseInfoMap(const std::map<FieldBaseInfo, EtDbId>& stFieldBaseInfoMap,
                                                 const FieldBaseInfo& stFieldBaseInfo, EtDbId& tarFieldId)
{
	auto iter = stFieldBaseInfoMap.find(stFieldBaseInfo);
	if (iter == stFieldBaseInfoMap.end())
		return false;

	tarFieldId = iter->second;
	return true;
}

bool DBSyncMergeHelper::findFromFormulaFieldInfoMap(const std::map<FieldValueTypeInfo, EtDbId>& stFormulaFieldInfoMap,
                                     const FieldValueTypeInfo& stFieldValueInfo, EtDbId& tarFieldId)
{
	auto iter = stFormulaFieldInfoMap.find(stFieldValueInfo);

	if (iter == stFormulaFieldInfoMap.end())
		return false;

	tarFieldId = iter->second;
	return true;
}

HRESULT DBSyncMergeHelper::copyBaseDataTo(IDbField* pSrcField, IDbField* pTarField)
{
    HRESULT hr = S_OK;
    hr = pTarField->SetTypeForIO(pSrcField->GetType());
    if (FAILED(hr))
        return hr;
    const IDBIds* pIds = pTarField->GetDbSheetData()->GetAllFields();
    EtDbIdx tarIdx = pIds->Id2Idx(pTarField->GetID());
    VS(pTarField->SetName(pSrcField->GetName(), tarIdx, TRUE));
    VS(pTarField->SetDescription(pSrcField->GetDescription()));
	hr = pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat());
    if (FAILED(hr))
        return hr;
	VS(pTarField->SetDefaultValTypeForIO(pSrcField->GetDefaultValType()));
	VS(pTarField->SetValueUniqueForIO(pSrcField->IsValueUnique()));
    VS(pTarField->SetArray(pSrcField->IsArray()));
	hr = pTarField->SetCustomConfig(pSrcField->GetCustomConfig());
	if (FAILED(hr))
        return hr;
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustFieldBaseInfo(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust)
{
	if (nullptr == pSrcField || nullptr == pTarField)
		return E_FAIL;

	if (!bIsFirstAdjust)
	{
		adjustNumFormat(pSrcField, pTarField);
		// 如果后面不需要细节调整，可以提前退出
		if (Et_DbSheetField_Address != pSrcField->GetType() &&
	    	Et_DbSheetField_Cascade != pSrcField->GetType())
			return S_OK;
	}
	HRESULT hr = copyBaseDataTo(pSrcField, pTarField);
	if (FAILED(hr))
		return hr;
	VS(pTarField->SetDefaultValForIO(__X("")));
	VS(pTarField->SetSyncFieldForIO(TRUE));
	switch (pSrcField->GetType())
	{
		case Et_DbSheetField_MultiLineText:
		case Et_DbSheetField_SingleLineText:  				// Et_DbSheetField_SingleLineText目前已废弃，做一下兼容
		case Et_DbSheetField_Link:							// 双向关联
		case Et_DbSheetField_OneWayLink:					// 单向关联
		case Et_DbSheetField_AutoNumber:					// 编号
		case Et_DbSheetField_ParentRecord:
			VS(pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText));
			VS(pTarField->SetValueUniqueForIO(FALSE));
			break;
		case Et_DbSheetField_ID:							// 身份证
		case Et_DbSheetField_Phone:							// 电话
		case Et_DbSheetField_BarCode:						// 条码
			// 禁止重复=false
			VS(pTarField->SetValueUniqueForIO(FALSE));
			break;
		case Et_DbSheetField_SingleSelect:					// 单选项
		case Et_DbSheetField_MultipleSelect:				// 多选项
		case Et_DbSheetField_Contact:						// 联系人
			break;
		case Et_DbSheetField_Address:						// 地址,特殊的级联字段
			// 直接合并 格式不一致时，转换为文本类型
			{
				if (bIsFirstAdjust)
					break;
				bool bNeedSetText = true;
				if (Et_DbSheetField_Address == pTarField->GetType())
				{
					ks_stdptr<IDbField_Cascade> spFieldAddress = pSrcField;
					ks_stdptr<IDbField_Cascade> spNewFieldAddress = pTarField;
					bNeedSetText = !(spNewFieldAddress->GetCascadeLevel() == spFieldAddress->GetCascadeLevel() && 
									 spFieldAddress->GetWithDetailedInfo() == spNewFieldAddress->GetWithDetailedInfo());
				}
				if (bNeedSetText)
				{
					VS(pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText));
					VS(pTarField->SetValueUniqueForIO(FALSE));
				}
			}
			break;
		case Et_DbSheetField_Cascade:					// 级联字段
			// 细节不一致时，转换为文本类型
			{
				if (bIsFirstAdjust)
					break;
				// 之前的级联字段已经回落成文本了，直接也回落成文本
				bool bNeedSetText = true;
				if (Et_DbSheetField_Cascade == pTarField->GetType())
				{
					ks_stdptr<IDbField_Cascade> spNewFieldCascade = pTarField;
					bNeedSetText = !alg::BOOL2bool(spNewFieldCascade->IsCascadeTitleEqual(pSrcField) &&
												   spNewFieldCascade->IsCascadeOptionEqual(pSrcField));
				}
				if (bNeedSetText)
				{
					VS(pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText));
					VS(pTarField->SetValueUniqueForIO(FALSE));
				}
			}
			break;
		case Et_DbSheetField_CreatedBy:					// 创建者
		case Et_DbSheetField_LastModifiedBy:				// 最后修改者
			// 合并后改为'联系人字段' 默认值=null
			VS(pTarField->SetTypeForIO(Et_DbSheetField_Contact));
			break;
		case Et_DbSheetField_CreatedTime:				// 创建时间
		case Et_DbSheetField_LastModifiedTime:			// 最后修改时间
			// 合并后改为'日期字段' 格式=yyyy/mm/dd,星期=False；时间=True；默认值=null
			VS(pTarField->SetTypeForIO(Et_DbSheetField_Date));
			VS(pTarField->SetNumberFormatForIOAndDiffuse(kfc::nf::_XNFGetExcelStr(Xls_AUTONF_DB_YMDHM)));
			break;
		case Et_DbSheetField_Lookup:						// 引用
			{
				if (!m_bNewLookupConvert)
				{
					VS(pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText));
					VS(pTarField->SetValueUniqueForIO(FALSE));
				}
			}
			break;
		// 由于公式字段更新时会产生非源字段的值，不符合产品预期，因此合并表不保留公式字段，统一转换为公式结果字段
		case Et_DbSheetField_Formula:
		case Et_DbSheetField_FormulaResult:
			VS(pTarField->SetTypeForIO(Et_DbSheetField_FormulaResult));
			if (isNeedOldFormat(pSrcField->GetValueType()))
				VS(pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat()));
			break;
		default:
			break;
	}
	return S_OK;
}

void DBSyncMergeHelper::getCurrencySymbol(IDbField* pField, ks_wstring& strCurrencySymbol)
{
	ks_bstr strSymbol; 
	HRESULT hr = m_spNumberFormatter->GetCurrencySymbol(pField->GetNumberFormat(), &strSymbol);
	if (S_OK != hr)
		return;
	strCurrencySymbol.assign(strSymbol.c_str());
}
// TODO: 去除正则表达式，改用数字格式的处理方法
static NumFmtCat recongnizeCustomNumFmt(PCWSTR pcwNumberFormat, NumFmtCat nfc)
{
	if (nfc != NFCat_Custom)
		return nfc;
	const QRegularExpression rxDate("[yda]"); // 包含y/d/a
	const QRegularExpression rxTime("[hs]"); // 包含h/s
	const QRegularExpression rxPercentage("[%]"); // 包含%
	QString numfmtStr = QString::fromUtf16(pcwNumberFormat);
	if (numfmtStr.contains(rxDate))
		return NFCat_Date;
	else if (numfmtStr.contains(rxTime))
		return NFCat_Time;
	else if (numfmtStr.contains(rxPercentage))
		return NFCat_Percentate;
	return nfc;
}

NumFmtCat DBSyncMergeHelper::getNumFmtCat(PCWSTR pcwNumberFormat)
{
	NUMFMT_CAT_INFO nci;
	VS(m_spNumberFormatter->GetCatInfo(pcwNumberFormat, &nci));
	return recongnizeCustomNumFmt(pcwNumberFormat, nci.cat);
}

bool DBSyncMergeHelper::compareNumformatPrecision(IDbField* pSrcField, IDbField* pTarField)
{
	kfc::nf::NF_INFO stSrcFieldNF;
	kfc::nf::NF_INFO stTarFieldNF;
	_XNF_GetSectsInfo(pSrcField->GetNumberFormatHandle(), &stSrcFieldNF);
	_XNF_GetSectsInfo(pTarField->GetNumberFormatHandle(), &stTarFieldNF);
	for (int i = 0; i < stSrcFieldNF.nSects && i < stTarFieldNF.nSects; i++)
	{
		if (stSrcFieldNF.SectInfos[i].nPrecision > stTarFieldNF.SectInfos[i].nPrecision)
			return true;
	}
	return false;
}

// TODO：优化成比较通用的实现；考虑国际化场景
void DBSyncMergeHelper::adjustDateTimeNumFormat(IDbField* pSrcField, IDbField* pTarField)
{
	const QRegularExpression rxWeek("[a]");
	const QRegularExpression rxTime("[h]");
	const QRegularExpression rxSecond("[s]");
	const QRegularExpression rxYear("[y]");
	const QRegularExpression rxDay("[d]");
	QString srcNumfmtStr = QString::fromUtf16(pSrcField->GetNumberFormat());
	QString tarNumfmtStr = QString::fromUtf16(pTarField->GetNumberFormat());
	bool bNeedWeek = false;
	bool bNeedTime = false;
	bool bNeedSecond = false;
	if (srcNumfmtStr.contains(rxWeek) && !tarNumfmtStr.contains(rxWeek))
		bNeedWeek = true;
	if (srcNumfmtStr.contains(rxTime) && !tarNumfmtStr.contains(rxTime))
		bNeedTime = true;
	if (srcNumfmtStr.contains(rxSecond) && !tarNumfmtStr.contains(rxSecond))
		bNeedSecond = true;
	bool bSrcHasYear = srcNumfmtStr.contains(rxYear), bTarHasYear = tarNumfmtStr.contains(rxYear),
	     bSrcHasDay = srcNumfmtStr.contains(rxDay), bTarHasDay = tarNumfmtStr.contains(rxDay);
	bool bNeedYear = bSrcHasYear && !bTarHasYear;
	bool bNeedDay = bSrcHasDay && !bTarHasDay;
	if (bNeedSecond)
	{
		int iIndexOfTime = tarNumfmtStr.indexOf("h:mm");
		if (-1 == iIndexOfTime)
		{
			int iIndexOfEnd = tarNumfmtStr.indexOf(";@");
			QString sec = " hh:mm:ss";
			if (-1 == iIndexOfEnd)  // 公式字段存在不包含“;@”的情况
				tarNumfmtStr += sec;
			else
				tarNumfmtStr.insert(iIndexOfEnd, sec);
		}
		else
		{
			QString sec = ":ss";
			tarNumfmtStr.insert(iIndexOfTime + 4, sec);
		}
	}
	else if (bNeedTime)
	{
		int iIndexOfEnd = tarNumfmtStr.indexOf(";@");
		QString time = " hh:mm";
		if (-1 == iIndexOfEnd)
			tarNumfmtStr += time;
		else
			tarNumfmtStr.insert(iIndexOfEnd, time);
	}
	if (bNeedWeek)
	{
		int iIndexOfTime = tarNumfmtStr.indexOf("h:mm");
		if (-1 == iIndexOfTime)
		{
			int iIndexOfEnd = tarNumfmtStr.indexOf(";@");
			QString week = " aaaa";
			if (-1 == iIndexOfEnd)
				tarNumfmtStr += week;
			else
				tarNumfmtStr.insert(iIndexOfEnd, week);
		}
		else
		{
			if (iIndexOfTime > 0)
			{
				int tmp = iIndexOfTime - 1;
				if (tarNumfmtStr[tmp] == 'h')
					iIndexOfTime = tmp;
			}
			QString week = " aaaa ";
			tarNumfmtStr.insert(iIndexOfTime, week);
		}
	}
	if ((bNeedYear || bNeedDay) && !bTarHasYear && !bTarHasDay)
	{
		// “yyyy/mm/dd xxx;@”
		QStringList strList = srcNumfmtStr.split(";");
		if (strList.size() == 0)
		{
			ASSERT(false);
			return;
		}
		// “yyyy/mm/dd xxx”
		QStringList strList2 = strList.at(0).split(" ");
		if (strList2.size() == 0)
		{
			ASSERT(false);
			return;
		}
		QString strSrcDateFormat = strList2.at(0) + " ";
		tarNumfmtStr.insert(0, strSrcDateFormat);
	}
	else if (bNeedYear)
	{
		int iIndexOfSplit= tarNumfmtStr.indexOf("/");
		if (iIndexOfSplit > 0)
			tarNumfmtStr.insert(0, "yyyy/");
		else
			tarNumfmtStr.insert(0, "yyyy-");
	}
	else if (bNeedDay)
	{
		int tmp = tarNumfmtStr.indexOf("yyyy\"年\"m\"月\"");
		if (-1 == tmp)
		{
			ASSERT(false);
			return;
		}
		int iInsertIndex = tarNumfmtStr.indexOf(" ");
		if (-1 == iInsertIndex)
		{
			iInsertIndex = tarNumfmtStr.indexOf(";@");
			if (-1 == iInsertIndex)
			{
				ASSERT(false);
				return;
			}
			tarNumfmtStr.insert(iInsertIndex, "d\"日\"");
		}
		else
			tarNumfmtStr.insert(iInsertIndex, "d\"日\" ");
	}
	if (bNeedYear || bNeedDay || bNeedWeek || bNeedTime || bNeedSecond)
		pTarField->SetNumberFormatForIOAndDiffuse(krt::utf16(tarNumfmtStr));
}

void DBSyncMergeHelper::adjustNumFormat(IDbField* pSrcField, IDbField* pTarField)
{
	switch (pSrcField->GetType())
	{
		case Et_DbSheetField_Number:
		case Et_DbSheetField_Currency:
		case Et_DbSheetField_Percentage:
			{
				if (compareNumformatPrecision(pSrcField, pTarField))
					pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat());
			}
			break;
		case Et_DbSheetField_Formula:
		case Et_DbSheetField_FormulaResult:
			{
				if (DbSheet_Fvt_Number == pSrcField->GetValueType() && compareNumformatPrecision(pSrcField, pTarField))
					pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat());
				if (DbSheet_Fvt_Date == pSrcField->GetValueType() || DbSheet_Fvt_Time == pSrcField->GetValueType())
					adjustDateTimeNumFormat(pSrcField, pTarField);
			}
			break;
		case Et_DbSheetField_Date:
		case Et_DbSheetField_Time:
			adjustDateTimeNumFormat(pSrcField, pTarField);
			break;
		case Et_DbSheetField_Lookup:
			adjustLookupNumFormat(pSrcField, pTarField);
			break;
		default:
			break;
	}
	return;
}

// 拷贝细节设置项，一般以第一个为准
HRESULT DBSyncMergeHelper::adjustFieldExInfo(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust)
{
	if (nullptr == pSrcField || nullptr == pTarField)
		return E_FAIL;
	HRESULT hr = S_OK;
	if (pSrcField->GetType() == Et_DbSheetField_Lookup)
		return adjustLookupFieldExDetail(pSrcField, pTarField, bIsFirstAdjust);
	switch (pTarField->GetType())
	{
		case Et_DbSheetField_Date:					// 日期
			if (bIsFirstAdjust)
				hr = adjustDateFieldExDetail(pSrcField, pTarField);
			break;
		case Et_DbSheetField_SingleSelect:				// 单选项
		case Et_DbSheetField_MultipleSelect:				// 多选项
			if (pSrcField->GetType() == Et_DbSheetField_SingleSelect || pSrcField->GetType() == Et_DbSheetField_MultipleSelect)
				hr = adjustSelectFieldExDetail(pSrcField, pTarField, bIsFirstAdjust);
			break;
		case Et_DbSheetField_Rating:						// 等级
			if (pSrcField->GetType() == pTarField->GetType())
				hr = adjustRatingFieldExDetail(pSrcField, pTarField, bIsFirstAdjust);
			break;
		case Et_DbSheetField_Contact:					// 联系人
			if (bIsFirstAdjust)
				hr = adjustContactFieldExDetail(pSrcField, pTarField);
			break;
		case Et_DbSheetField_Address:					// 地址,特殊的级联字段
			if (bIsFirstAdjust && pSrcField->GetType() == pTarField->GetType())
				hr = adjustAddressFieldExDetail(pSrcField, pTarField);
			break;
		case Et_DbSheetField_Cascade:					// 级联字段
			if (bIsFirstAdjust && pSrcField->GetType() == pTarField->GetType())
				hr = adjustCascadeFieldExDetail(pSrcField, pTarField);
			break;
		case Et_DbSheetField_Department:
			if (pSrcField->GetType() == pTarField->GetType())
				hr = adjustDepartmentFieldExDetail(pSrcField, pTarField);
			break;
		case Et_DbSheetField_Formula:					// 公式
		case Et_DbSheetField_FormulaResult:
			if (bIsFirstAdjust)
				hr = adjustFormulaResultFieldExDetail(pSrcField, pTarField);
			break;
		default:
			if (bIsFirstAdjust && pSrcField->GetType() == pTarField->GetType())
				hr = pSrcField->CopyExDataTo(pTarField);
			break;
	}
	return hr;
}
void DBSyncMergeHelper::adjustLookupNumFormat(IDbField* pSrcField, IDbField* pTarField)
{
	ET_DbSheet_FieldType type = pTarField->GetType();
	switch (pTarField->GetType())
	{
		case Et_DbSheetField_Number:
		case Et_DbSheetField_Currency:
		case Et_DbSheetField_Percentage:
			{
				if (compareNumformatPrecision(pSrcField, pTarField))
					pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat());
			}
			break;
		case Et_DbSheetField_FormulaResult:
			{
				if (DbSheet_Fvt_Number == pTarField->GetValueType() && compareNumformatPrecision(pSrcField, pTarField))
					pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat());
				if (DbSheet_Fvt_Date == pTarField->GetValueType() || DbSheet_Fvt_Time == pTarField->GetValueType())
					adjustDateTimeNumFormat(pSrcField, pTarField);
			}
			break;
		case Et_DbSheetField_Date:
		case Et_DbSheetField_Time:
			adjustDateTimeNumFormat(pSrcField, pTarField);
			break;
		default:
			break;
	}
}

HRESULT DBSyncMergeHelper::adjustLookupFieldExDetail(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust)
{
	if (!m_bNewLookupConvert)
		return S_OK;
	if (bIsFirstAdjust)
	{
		pTarField->SetArray(TRUE);
		pTarField->SetNumberFormatForIOAndDiffuse(pSrcField->GetNumberFormat());
	}
	adjustFieldTypeByLookupFunction(pSrcField, pTarField, bIsFirstAdjust);
	return S_OK;
}

bool DBSyncMergeHelper::isNeedOldFormat(Et_DbSheet_Field_Value_Type enmValueType)
{
	switch (enmValueType)
	{
		case DbSheet_Fvt_Date:
		case DbSheet_Fvt_Time:
		case DbSheet_Fvt_Number:
		case DbSheet_Fvt_Logic:
			return true;
		default:
			break;
	}
	return false;
}
void DBSyncMergeHelper::adjustFieldTypeByLookupFunction(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust)
{
	ASSERT(Et_DbSheetField_Lookup == pSrcField->GetType());
	ks_stdptr<IDbField_Lookup> spSrcField_Lookup = pSrcField;
	ks_stdptr<IDbField> spNextLookupBaseField;
	spSrcField_Lookup->GetNextLookupBaseField(&spNextLookupBaseField);
	if (!spNextLookupBaseField)
	{
		if (bIsFirstAdjust)
		{
			VS(pTarField->SetTypeForIO(Et_DbSheetField_FormulaResult));
			ks_stdptr<IDbField_FormulaResult> spFormulaResultField = pTarField;
			VS(spFormulaResultField->SetFormulaResultType(pSrcField->GetValueType()));
		}
		return;
	}
	ET_DbSheet_FieldType enmBaseFieldType = spNextLookupBaseField->GetType();
	ET_DbSheet_Lookup_Function enmSrcLookupFunction = spSrcField_Lookup->GetLookupFunction();
	ks_wstring wstrOldNumFmt = pTarField->GetNumberFormat();
	switch (enmSrcLookupFunction)
	{
		case DbSheet_Lookup_Function_Origin:
		case DbSheet_Lookup_Function_Unique:
			{
				if (!bIsFirstAdjust)
					break;
				switch (enmBaseFieldType)
				{
					case Et_DbSheetField_Link:
					case Et_DbSheetField_OneWayLink:
					case Et_DbSheetField_AutoNumber:
						VS(pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText));
						break;
					case Et_DbSheetField_CreatedBy:					// 创建者
					case Et_DbSheetField_LastModifiedBy:				// 最后修改者
						VS(pTarField->SetTypeForIO(Et_DbSheetField_Contact));
						break;
					case Et_DbSheetField_CreatedTime:				// 创建时间
					case Et_DbSheetField_LastModifiedTime:			// 最后修改时间
						VS(pTarField->SetTypeForIO(Et_DbSheetField_Date));
						break;
					case Et_DbSheetField_Formula:
					case Et_DbSheetField_Lookup:
						VS(pTarField->SetTypeForIO(Et_DbSheetField_FormulaResult));
						{
							VS(spNextLookupBaseField->GetDbSheetData()->GetFieldsManager()->Update(TRUE));
							ks_stdptr<IDbField_FormulaResult> spFormulaResultField = pTarField;
							VS(spFormulaResultField->SetFormulaResultType(spNextLookupBaseField->GetValueType()));
						}
						break;
					default:
						VS(pTarField->SetTypeForIO(enmBaseFieldType));
						break;
				}
				if (isNeedOldFormat(pTarField->GetValueType()))
					VS(pTarField->SetNumberFormatForIOAndDiffuse(wstrOldNumFmt.c_str()));
			}
			break;
		// 特殊的引用函数会引入错误类型的token，需要把字段类型设置成公式结果字段，否则会设值失败。
		case DbSheet_Lookup_Function_Sum:
		case DbSheet_Lookup_Function_Average:
		case DbSheet_Lookup_Function_Max:
		case DbSheet_Lookup_Function_Min:
			{
				VS(pTarField->SetTypeForIO(Et_DbSheetField_FormulaResult));
				ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pTarField;
				if (enmBaseFieldType != Et_DbSheetField_AutoNumber)
					VS(spField_FormulaResult->SetFormulaResultType(spNextLookupBaseField->GetValueType()));
				else
					VS(spField_FormulaResult->SetFormulaResultType(DbSheet_Fvt_Text));
				if (isNeedOldFormat(pTarField->GetValueType()))
					VS(pTarField->SetNumberFormatForIOAndDiffuse(wstrOldNumFmt.c_str()));
			}
			break;
		case DbSheet_Lookup_Function_Counta:
		case DbSheet_Lookup_Function_CountaUnique:
			{
				VS(pTarField->SetTypeForIO(Et_DbSheetField_FormulaResult));
				ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pTarField;
				VS(spField_FormulaResult->SetFormulaResultType(DbSheet_Fvt_Number));
				VS(pTarField->SetNumberFormatForIOAndDiffuse(wstrOldNumFmt.c_str()));
			}
			break; 
		case DbSheet_Lookup_Function_ToString:
			{
				VS(pTarField->SetTypeForIO(Et_DbSheetField_FormulaResult));
				ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pTarField;
				VS(spField_FormulaResult->SetFormulaResultType(DbSheet_Fvt_Text));
			}
			break;
		default:
			ASSERT(false);
			break;
	}

	if (nullptr != spNextLookupBaseField && spNextLookupBaseField->GetType() != Et_DbSheetField_Lookup && pTarField->GetType() != Et_DbSheetField_FormulaResult)
		adjustFieldExInfo(spNextLookupBaseField, pTarField, bIsFirstAdjust);
}

HRESULT DBSyncMergeHelper::adjustFormulaResultFieldExDetail(IDbField* pSrcField, IDbField* pTarField)
{
	pTarField->SetTypeForIO(Et_DbSheetField_FormulaResult);
	BOOL bIsArray = Et_DbSheetField_Formula != pSrcField->GetType() && pSrcField->IsArray();
	pTarField->SetArray(bIsArray);
	ks_stdptr<IDbField_FormulaResult> spFormulaResultField = pTarField;
	spFormulaResultField->SetFormulaResultType(pSrcField->GetValueType());
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustSelectFieldExDetail(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust)
{
	ks_stdptr<IDbField_Select> spFieldSelect = pSrcField;
	ks_stdptr<IDbField_Select> spNewFieldSelect = pTarField;
	UINT cnt = spFieldSelect->Count();
	if (cnt > 0)
	{
		for (UINT i = 0; i < cnt; ++i)
		{
			PCWSTR value = nullptr;
			EtDbId id = INV_EtDbId;
			ARGB color = 0;
			spFieldSelect->Item(i, &value, &color, &id);
			spNewFieldSelect->AppendItem(value, color);
		}
	}
	if (bIsFirstAdjust)
	{
		VS(spNewFieldSelect->SetAllowAddItemWhenInputting(FALSE));
		VS(spNewFieldSelect->SetAutoAddItem(FALSE));
	}
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustRatingFieldExDetail(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust)
{
	ks_stdptr<IDbField_Rating> spFieldRating = pSrcField;
	ks_stdptr<IDbField_Rating> spNewFieldRating = pTarField;
	if (bIsFirstAdjust)
		spNewFieldRating->SetMaxRating(spFieldRating->GetMaxRating());
	else
	{
		UINT uSrcMaxRating = spFieldRating->GetMaxRating();
		UINT uNewMaxRating = spNewFieldRating->GetMaxRating();
		if (uSrcMaxRating > uNewMaxRating)
			spNewFieldRating->SetMaxRating(uSrcMaxRating);
	}
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustContactFieldExDetail(IDbField* pSrcField, IDbField* pTarField)
{
	ks_stdptr<IDbField_Contact> spNewFieldContact = pTarField;
	VS(spNewFieldContact->SetSupportMulti(TRUE));
	VS(spNewFieldContact->SetSupportNotice(FALSE));
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustAddressFieldExDetail(IDbField* pSrcField, IDbField* pTarField)
{
	ks_stdptr<IDbField_Cascade> spFieldAddress = pSrcField;
	ks_stdptr<IDbField_Cascade> spNewFieldAddress = pTarField;
	spNewFieldAddress->SetCascadeLevel(spFieldAddress->GetCascadeLevel());
	spNewFieldAddress->SetWithDetailedInfo(spFieldAddress->GetWithDetailedInfo());
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustCascadeFieldExDetail(IDbField* pSrcField, IDbField* pTarField)
{
	ks_stdptr<IDbField_Cascade> spFieldCascade = pSrcField;
	ks_stdptr<IDbField_Cascade> spNewFieldCascade = pTarField;
	spNewFieldCascade->SetDisplayAllLevel(TRUE);
	IDbCascadeHandle* cascadeTitleHandle = spFieldCascade->GetCascadeTitleHandle();
	if(cascadeTitleHandle)
	{
		std::vector<PCWSTR> titles;
		UINT count = cascadeTitleHandle->GetCascadeLevel();
		titles.reserve(count);
		for (UINT i = 0; i < count; ++i)
			titles.push_back(cascadeTitleHandle->GetCascadeItemValue(i));
		VS(spNewFieldCascade->SetCascadeTitle(titles.data(), titles.size()));
	}
	spFieldCascade->CopyAllCascadeOption(pTarField);
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustDepartmentFieldExDetail(IDbField* pSrcField, IDbField* pTarField)
{
	ks_stdptr<IDbField_Cascade> spFieldDepartment = pSrcField;
	ks_stdptr<IDbField_Cascade> spNewFieldDepartment = pTarField;
	spNewFieldDepartment->SetDisplayAllLevel(spNewFieldDepartment->GetDisplayAllLevel() || spFieldDepartment->GetDisplayAllLevel());
	spNewFieldDepartment->SetMultiValue(TRUE);
	return S_OK;
}

HRESULT DBSyncMergeHelper::adjustDateFieldExDetail(IDbField* pSrcField, IDbField* pTarField)
{
	ks_stdptr<IDbField_Date> spNewFieldDate = pTarField;
	spNewFieldDate->SetLoadLegalHoliday(TRUE);
	return S_OK;
}

bool DBSyncMergeHelper::decodeSrcRecordInfo(PCWSTR str, DBSyncMergeHelper::RecordInfo& stSrcRecordInfo)
{
	QString urlStr = krt::fromUtf16(str);
	int fileId_left_idx = urlStr.indexOf("/d/");
	if (-1 == fileId_left_idx)
		fileId_left_idx = urlStr.indexOf("/k/");
	int idx = urlStr.indexOf("?R=");
	if (fileId_left_idx == -1 || idx == -1)
		return false;
	int fileId_start_idx = fileId_left_idx + 3;
	QString fileId = urlStr.mid(fileId_start_idx, idx - fileId_start_idx);
	QString posInfo = urlStr.mid(idx + 3);
	QByteArray byte = QByteArray::fromBase64(posInfo.toUtf8());
	QString decodedStr(byte.data());
	// {base64:/C/:sheetStId/:recordId}
	idx = decodedStr.lastIndexOf("/");
	if (idx == -1 || idx < 3)
		return false;
	QString EtDbIdStr = decodedStr.mid(idx + 1);
	QString StIdStr = decodedStr.mid(3, idx - 3);
	uint32 uStId = StIdStr.toUInt();
	EtDbId id = INV_EtDbId;
	_appcore_GainDbSheetContext()->DecodeEtDbId(krt::utf16(EtDbIdStr), &id);
	if (INV_EtDbId == id)
		return false;
	ks_wstring wsFileId = krt::utf16(fileId);
	auto iter = m_stOldFileIdxMap.find(wsFileId);
	int old_fileId_idx = 0;
	if (iter == m_stOldFileIdxMap.end())
	{
		m_stOldFileIdxMap[wsFileId] = m_stOldFileIdVec.size();
		m_stOldFileIdVec.emplace_back(std::move(wsFileId));
		old_fileId_idx = m_stOldFileIdVec.size() - 1;
	}
	else
		old_fileId_idx = iter->second;
	stSrcRecordInfo.m_recId = id;
	stSrcRecordInfo.m_uStId = uStId;
	stSrcRecordInfo.m_pcwFileId = m_stOldFileIdVec[old_fileId_idx].c_str();
	return true;
}

HRESULT DBSyncMergeHelper::fillOldRecordMap()
{
	HRESULT hr = S_OK;
	const IDBIds* pTarIds = m_spTarDbSheetOp->GetAllRecords();
	EtDbIdx tarRecCnt = pTarIds->Count();
	if (INV_EtDbId == m_sourceFldId)
		return S_FALSE;
	for (EtDbIdx i = 0; i < tarRecCnt; ++i)
	{
		EtDbId recId = pTarIds->IdAt(i);
		ks_bstr source;
		m_spTarDbSheetOp->GetValueString(recId, m_sourceFldId, &source);
		if (source.empty())
			continue;
		RecordInfo stSrcRecordInfo;
		if (decodeSrcRecordInfo(source.c_str(), stSrcRecordInfo))
			m_stOldRecordMap[stSrcRecordInfo] = recId;
	}
	return hr;
}

HRESULT DBSyncMergeHelper::ClearOldField()
{
	HRESULT hr = S_OK;
	const IDBIds* pTarFieldIds = m_spTarDbSheetOp->GetAllFields();
	int tarFldCnt = pTarFieldIds->Count();
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	m_sourceFldId = pTarFieldsMgr->GetSyncFieldSourceId();
	m_sourceNameFldId = pTarFieldsMgr->GetSyncFieldSourceNameId();
	for (EtDbIdx i = 0; i < tarFldCnt; ++i)
	{
		EtDbId oldFldId = pTarFieldIds->IdAt(i);
		ASSERT(oldFldId != INV_EtDbId);
		ks_stdptr<IDbField> spOldField;
		pTarFieldsMgr->GetField(oldFldId, &spOldField);
		// 不确定这个字段后面是否会被用到，先将名字置为空字段，避免名字冲突
		VS(spOldField->SetNameForIO(__X(""), i));
	}
	return hr;
}

HRESULT DBSyncMergeHelper::adjustRecords(IDBSheetOp* pSrcDbSheetOp, uint32 uStId)
{
	HRESULT hr = S_OK;
	const IDBIds* pSrcIds = pSrcDbSheetOp->GetAllRecords();
	EtDbIdx srcRecCnt = pSrcIds->Count();
	std::vector<EtDbId> insertRecId;
	insertRecId.reserve(srcRecCnt);
	for (EtDbIdx i = 0; i < srcRecCnt; ++i)
	{
		EtDbId srcRecId = pSrcIds->IdAt(i);
		RecordInfo stSrcRecordInfo{srcRecId, uStId, m_pcwCurSourceFileId};
		auto iter = m_stOldRecordMap.find(stSrcRecordInfo);
		if (iter == m_stOldRecordMap.end())
			insertRecId.emplace_back(srcRecId);
	}
	size_t insertRecCnt = insertRecId.size();
	if (insertRecCnt > 0)
	{
		ks_stdptr<IDBSheetRange> spDbRange;
		hr = m_spTarDbSheetOp->InsertRecords(insertRecCnt, &spDbRange);
		if (FAILED(hr))
			return hr;
		for (int i = 0; i < insertRecCnt; ++i)
		{
			RecordInfo stSrcRecordInfo{insertRecId[i], uStId, m_pcwCurSourceFileId};
			m_stOldRecordMap[stSrcRecordInfo] = spDbRange->GetRecordId(i);
		}
	}
	return hr;
}

PCWSTR DBSyncMergeHelper::addHoldName(PCWSTR pcwName)
{
	ks_wstring wsName(pcwName);
	auto iter = m_stHoldNameSet.find(wsName);
	if (iter != m_stHoldNameSet.end())
		return (*iter).c_str();
	auto result = m_stHoldNameSet.insert(wsName);
	bool bIsSuccess = result.second;
	if (!bIsSuccess)
		return NULL;
	iter = result.first;
	return (*iter).c_str();
}

HRESULT DBSyncMergeHelper::adjustViews()
{
	ks_stdptr<IDBSheetViews> spDBSheetViews;
	HRESULT hr = m_spTarDbSheetOp->GetDbSheetViews(&spDBSheetViews);
	if (FAILED(hr))
		return hr;
	class AllDbViewsEnum : public IDbViewEnum
	{
		using CallBack = std::function<HRESULT(IDBSheetView*)>;
	public:
		explicit AllDbViewsEnum(CallBack cb) : m_cb(std::move(cb)) {}
		HRESULT Do(IDBSheetView* pView) override
		{
			return m_cb(pView);
		}
	private:
		CallBack m_cb;
	};
	for (const auto& fldId : m_stNewFieldIdSet)
	{
		AllDbViewsEnum viewsEnum([fldId](IDBSheetView* pView) -> HRESULT {
			ET_DBSheet_ViewType type = pView->GetType();
			if (type == et_DBSheetView_Grid)
				pView->SetFieldsHidden(fldId, FALSE);
			return S_OK;
		});
		spDBSheetViews->EnumViews(&viewsEnum);
	}
	return S_OK;
}

STDIMP DBSyncMergeHelper::copyRecordDetail(IDBSheetOp* pSrcDbSheetOp, ET_DbSheet_FieldType enmFieldType, const CopyFieldDataInfo& stInfo)
{
	HRESULT hr = S_OK;
	if (stInfo.m_bIsArray)
		return copyDefaultRecordDetail(pSrcDbSheetOp, stInfo);
	switch (enmFieldType)
	{
		case Et_DbSheetField_AutoNumber:
			hr = copyAutoNumberRecordDetail(pSrcDbSheetOp, stInfo);
			break;
		case Et_DbSheetField_Email:
			hr = copyEmailRecordDetail(pSrcDbSheetOp, stInfo);
			break;
		case Et_DbSheetField_Url:
			hr = copyUrlRecordDetail(pSrcDbSheetOp, stInfo);
			break;
		case Et_DbSheetField_Lookup:
			{
				if (m_bNewLookupConvert)
					hr = copyDefaultRecordDetail(pSrcDbSheetOp, stInfo);
				else
					hr = copyLinkRecordDetail(pSrcDbSheetOp, stInfo);
			}
			break;
		case Et_DbSheetField_Link:
		case Et_DbSheetField_OneWayLink:
		case Et_DbSheetField_ParentRecord:
			hr = copyLinkRecordDetail(pSrcDbSheetOp, stInfo);
			break;
		default:
			hr = copyDefaultRecordDetail(pSrcDbSheetOp, stInfo);
			break;
	}
	return hr;
}

HRESULT DBSyncMergeHelper::copyDefaultRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo)
{
	// 值拷贝，不走 io，避免公式删除出错
	const_token_ptr pToken = nullptr;
	VS(pSrcDbSheetOp->GetValueToken(stInfo.m_srcRecId, stInfo.m_srcFldId, &pToken));
	VS(m_spTarDbSheetOp->SetTokenValue(stInfo.m_tarRecId, stInfo.m_tarFldId, pToken));
	return S_OK;
}

HRESULT DBSyncMergeHelper::copyLinkRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo)
{
	// 关联、引用字段直接转为文本字段，相应值需要做转换处理
	ks_bstr oldVal;
	VS(pSrcDbSheetOp->GetDisplayString(stInfo.m_srcRecId, stInfo.m_srcFldId, &oldVal));
	if (oldVal.empty())
	{
		VS(m_spTarDbSheetOp->SetTokenValue(stInfo.m_tarRecId, stInfo.m_tarFldId, nullptr));
		return S_OK;
	}
	alg::managed_vstr_token_assist tokenAssist;
	tokenAssist.create(oldVal.c_str());
	VS(m_spTarDbSheetOp->SetTokenValue(stInfo.m_tarRecId, stInfo.m_tarFldId, tokenAssist));
	return S_OK;
}

HRESULT DBSyncMergeHelper::copyUrlRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo)
{
	const_token_ptr pToken = nullptr;
	HRESULT hr = pSrcDbSheetOp->GetValueToken(stInfo.m_srcRecId, stInfo.m_srcFldId, &pToken);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDbHyperlinkHandle> spHyperlinkHandle;
	if (pToken && alg::const_handle_token_assist::is_type(pToken))
	{
		alg::const_handle_token_assist chta(pToken);
		alg::TOKEN_HANDLE handle = chta.get_handle();
		if (!handle || chta.get_handleType() != alg::ET_HANDLE_DBHYPERLINK)
			return E_INVALIDARG;
		else
			spHyperlinkHandle = handle->CastUnknown();
	}
	PCWSTR displayText = spHyperlinkHandle ? spHyperlinkHandle->GetDisplayText() : __X("");
	PCWSTR address = spHyperlinkHandle ? spHyperlinkHandle->GetAddress() : __X("");
	alg::managed_token_assist mta;
	hr = _appcore_GainDbSheetContext()->Text2DbHyperlinkToken(displayText, address, &mta);
	if (FAILED(hr))
		return hr;
	hr = m_spTarDbSheetOp->SetTokenValue(stInfo.m_tarRecId, stInfo.m_tarFldId, mta);
	if (FAILED(hr))
		return hr;
	return S_OK;
}

HRESULT DBSyncMergeHelper::copyEmailRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo)
{
	ks_bstr address;
	VS(pSrcDbSheetOp->GetHyperlinkAddress(stInfo.m_srcRecId, stInfo.m_srcFldId, &address));
	if (FAILED(m_spTarDbSheetOp->SetHyperlinkAddress(stInfo.m_tarRecId, stInfo.m_tarFldId, address.c_str())))
	{
		VS(m_spTarDbSheetOp->SetTokenValue(stInfo.m_tarRecId, stInfo.m_tarFldId, nullptr));
		return S_OK;
	}
	const_token_ptr pToken = nullptr;
	VS(pSrcDbSheetOp->GetValueToken(stInfo.m_srcRecId, stInfo.m_srcFldId, &pToken));
	VS(m_spTarDbSheetOp->SetTokenValue(stInfo.m_tarRecId, stInfo.m_tarFldId, pToken));
	return S_OK;
}

HRESULT DBSyncMergeHelper::copyAutoNumberRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo)
{
	// 编号字段会转换为文本，需要直接取文本来进行赋值
	ks_bstr valStr;
	VS(pSrcDbSheetOp->GetValueString(stInfo.m_srcRecId, stInfo.m_srcFldId, &valStr));
	if (valStr.empty())
		return S_OK;
	HRESULT hr = m_spTarDbSheetOp->SetValue(stInfo.m_tarRecId, stInfo.m_tarFldId, valStr.c_str());
	if (FAILED(hr))  // 失败尝试设空，再失败就跳过
		VS(m_spTarDbSheetOp->SetTokenValue(stInfo.m_tarRecId, stInfo.m_tarFldId, nullptr));
	return S_OK;
}

DBAddMergeSyncHelper::DBAddMergeSyncHelper(etoldapi::_Worksheet* pTarWorkSheet)
	: m_pTarWorkSheet(pTarWorkSheet)
{
}

HRESULT DBAddMergeSyncHelper::Init(const DBAddMergeSyncParam& syncParam)
{
	if (!syncParam.pcwFldSourceFileName || !syncParam.pcwFldSourceName)
		return E_INVALIDARG;
	m_param = syncParam;
	return S_OK;
}

HRESULT DBAddMergeSyncHelper::PrepareMerge()
{
	// initialize tarOp
	VS(DbSheet::GetDBSheetOp(m_pTarWorkSheet->GetSheet(), &m_spTarDbSheetOp));
	m_stHoldFieldIdSet.clear();
	return S_OK;
}

HRESULT DBAddMergeSyncHelper::ExecMerge()
{
	HRESULT hr = S_OK;
	if (nullptr == m_spTarDbSheetOp)
		return E_FAIL;
	m_stFieldMappingItemsMap.clear();
	ISheet* pTarSheet = m_pTarWorkSheet->GetSheet();
	IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	ks_stdptr<IDBSheetRange> spDbRange;
	bool bIsFirstInsert = false;

	IEncodeDecoder* pEncodeDecoder = _appcore_GainEncodeDecoder();
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	int iTotalNeedFieldCnt = 0;
	auto& srcFileIdMap = *m_param.m_pSrcFileIdMap;
	for (const auto& iter : srcFileIdMap)
	{
		VarObj* pSrcSheetsInfo = iter.second;
		int srcSheetCnt = pSrcSheetsInfo->arrayLength();
		std::vector<FieldMappingItem>& stFieldMappingItemsVec = m_stFieldMappingItemsMap[iter.first];
		for (int i = 0, k = 0; i < srcSheetCnt; ++i)
		{
			VarObj srcSheetInfo = pSrcSheetsInfo->at(i);
			UINT32 uStId = srcSheetInfo.field_uint32("sheetId");
			VarObj srcFields = srcSheetInfo.get_s("fields");
			int srcFieldCnt = srcFields.arrayLength();
			iTotalNeedFieldCnt += srcFieldCnt;
			const IDBIds* pTarFieldIds = m_spTarDbSheetOp->GetAllFields();
			EtDbIdx tarFldCnt = pTarFieldIds->Count();
			if (iTotalNeedFieldCnt > tarFldCnt)
			{
				hr = m_spTarDbSheetOp->InsertFields(iTotalNeedFieldCnt - tarFldCnt);
				if (FAILED(hr))
					return hr;
			}
			stFieldMappingItemsVec.emplace_back(uStId);
			for (int j = 0; j < srcFieldCnt; j++)
			{
				VarObj srcFieldItem = srcFields.at(j);
				VAR_OBJ_EXPECT_STRING(srcFieldItem, "id")
				EtDbId srcFldId = INV_EtDbId;
				dbctx->DecodeEtDbId(srcFieldItem.field_str("id"), &srcFldId);
				if (srcFldId == INV_EtDbId)
					return E_INVALIDARG;
				ks_stdptr<IDbField> spField;
				EtDbId tarFldId = INV_EtDbId;
				ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
				if (srcFieldItem.has("type"))
				{
					VAR_OBJ_EXPECT_STRING(srcFieldItem, "type")
					pEncodeDecoder->DecodeFieldType(srcFieldItem.field_str("type"), &type);
				}
				if (Et_DbSheetField_Automations == type || Et_DbSheetField_Button == type)
					continue;
				bIsFirstInsert = false;
				if (!checkCanReuseOldField(type, srcFieldItem.field_str("name"), tarFldId))
				{
					bIsFirstInsert = true;
					tarFldId = pTarFieldIds->IdAt(k++);
				}
				hr = pNewFieldsMgr->GetField(tarFldId, &spField);
				if (FAILED(hr))
					return hr;
				switch (type)
				{
					case Et_DbSheetField_AutoNumber:
					case Et_DbSheetField_Link:
					case Et_DbSheetField_Lookup:
					case Et_DbSheetField_OneWayLink:
						hr = spField->SetType(Et_DbSheetField_MultiLineText, nullptr);
						if (FAILED(hr))
							return hr;
						break;
					case Et_DbSheetField_CreatedBy:
					case Et_DbSheetField_LastModifiedBy:
						hr = spField->SetType(Et_DbSheetField_Contact, nullptr);
						if (FAILED(hr))
							return hr;
						break;
					case Et_DbSheetField_CreatedTime:
					case Et_DbSheetField_LastModifiedTime:
						hr = spField->SetType(Et_DbSheetField_Date, nullptr);
						if (FAILED(hr))
							return hr;
						break;
					case Et_DbSheetField_Formula:
						hr = spField->SetType(Et_DbSheetField_FormulaResult, nullptr);
						if (FAILED(hr))
							return hr;
					default:
						hr = spField->SetType(type, nullptr);
						if (FAILED(hr))
							return hr;
						break;
				}
				if (bIsFirstInsert)
				{
					hr = spField->SetName(srcFieldItem.field_str("name"), TRUE, FALSE);
					if (FAILED(hr))
						return hr;
					if (srcFieldItem.has("customConfig") && srcFieldItem.get_s("customConfig").type() == binary_wo::typeString)
					{
						hr = spField->SetCustomConfig(srcFieldItem.field_str("customConfig"));
						if (FAILED(hr))
							return hr;
					}
					spField->SetSyncFieldForIO(TRUE);
					addOriginFieldInfoMapping(tarFldId, type, srcFieldItem.field_str("name"));
				}
				stFieldMappingItemsVec[i].m_fieldIdMap[srcFldId] = tarFldId;
			}
		}
	}
	int initFieldWidth = 1572;
	const IDBIds* pTarFieldIds = m_spTarDbSheetOp->GetAllFields();
	int fieldCnt = pTarFieldIds->Count();
	if (fieldCnt > 0)
		pTarSheet->SetColWidth(0, fieldCnt - 1, initFieldWidth);
	return S_OK;
}

HRESULT DBAddMergeSyncHelper::AdjustMerge()
{
	return removeUnusedField();
}

void DBAddMergeSyncHelper::addOriginFieldInfoMapping(EtDbId tarFieldId, ET_DbSheet_FieldType type, PCWSTR pcwFieldName)
{
	m_stHoldFieldIdSet.insert(tarFieldId);
	FieldBaseInfo stFieldBaseInfo(type, pcwFieldName, __X(""), FALSE);
	m_stOriginFieldBaseInfoMapping[stFieldBaseInfo] = tarFieldId;
}

bool DBAddMergeSyncHelper::checkCanReuseOldField(ET_DbSheet_FieldType type, PCWSTR pcwFieldName, EtDbId& tarFieldId)
{
	FieldBaseInfo stFieldBaseInfo(type, pcwFieldName, __X(""), FALSE);
	auto iter = m_stOriginFieldBaseInfoMapping.find(stFieldBaseInfo);
	if (iter == m_stOriginFieldBaseInfoMapping.end())
		return false;
	tarFieldId = iter->second;
	return true;
}

HRESULT DBAddMergeSyncHelper::removeUnusedField()
{
	const IDBIds* pTarFieldIds = m_spTarDbSheetOp->GetAllFields();
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetRecordIds(m_spTarDbSheetOp->GetAllRecords());
	for (EtDbIdx i = 0, tarFldCnt = pTarFieldIds->Count(); i < tarFldCnt; ++i)
	{
		EtDbId id = pTarFieldIds->IdAt(i);
		if (m_stHoldFieldIdSet.find(id) == m_stHoldFieldIdSet.end())
			spRemoveRange->AddFieldId(id);
	}
	if (spRemoveRange->GetFieldCnt() > 0)
	{
		if (spRemoveRange->GetFieldCnt() == pTarFieldIds->Count())
		{
			IDbFieldsManager* pNewFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
			ks_stdptr<IDBSheetRange> spDbRange;
			HRESULT hr = m_spTarDbSheetOp->InsertFields(1, &spDbRange);
			if (FAILED(hr))
				return hr;
			pNewFieldsMgr->SetPrimaryField(spDbRange->GetFieldId(0));
		}
		HRESULT hr = m_spTarDbSheetOp->RemoveFields(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

void DBAddMergeSyncHelper::MoveFieldMappingItems(std::map<ks_wstring, std::vector<FieldMappingItem>>& stFieldMappingItemsMap)
{
	stFieldMappingItemsMap = std::move(m_stFieldMappingItemsMap);
}
} // namespace wo
