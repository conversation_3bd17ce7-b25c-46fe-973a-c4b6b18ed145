﻿#ifndef __ETAI_TEVAL_H__
#define __ETAI_TEVAL_H__

#include "etstdafx.h"

namespace etai
{
class CalcCellCount
{
  public:
    CalcCellCount(const QJsonObject &obj);

  public:
    int count() const;
    int rangeCount() const;

  private:
    void calculate();
    void calculateTableList(const QJsonArray &tableList);
    void calculatePivotTableList(const QJsonArray &tableList);
    void calculateTable(const QJsonObject &table);

  private:
    const QJsonObject &m_obj;
    int m_cellCount;
    int m_rangeCellCount = 0;
};
} // namespace etai
#endif
