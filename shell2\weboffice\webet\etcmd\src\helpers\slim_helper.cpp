﻿#include "slim_helper.h"
#include "etcore/little_alg.h"
#include "webetlink.h"
#include "webbase/binvariant/binwriter.h"
#include "wo/wo_msgType_helper.h"
#include "wo/et_revision_context.h"
#include "workbook.h"

extern Callback* gs_callback;

#define NULL_CELLS_NAME "nullCells"
#define UNUSED_STYLES_NAME "unusedDuplicateStyles"
#define UNUSED_SHAPES_NAME "unusedShapes"
#define UNUSED_CELL_PICS_NAME "unusedCellPictures"
#define PICS_TO_COMPRESSED_NAME "potentialPicturesToBeCompressed"
#define UNUSED_OVERLAP_SHAPES_NAME "unusedOverlapShapes"
#define UNUSED_DUPLICATE_FORMAT_CONDITION_NAME "unusedDuplicateFormatCondition"
namespace
{

void BroadcastNow(wo::MsgType name, binary_wo::BinWriter& ww)
{
    binary_wo::BinWriter::StreamHolder bt = ww.buildStream();
    WebSlice slice = {bt.get(), ww.writeLength()};
    gs_callback->broadcast(getMsgTypeName(name), &slice, NULL);
}

template<class T>
constexpr const T& clamp( const T& val, const T& low, const T& high)
{
    return (val < low) ? low : (high < val) ? high : val;
}

class SlimTimer
{
    using Duration = std::chrono::duration<double>;
    using TimePoint = std::chrono::system_clock::time_point;
public:
    SlimTimer(std::chrono::system_clock::duration interval)
        : m_interval(interval)
        , m_start(std::chrono::system_clock::from_time_t(0))
    {}
    void reset()
    {
        m_start = std::chrono::system_clock::now();
    }
    Duration elapsed()
    {
        return std::chrono::system_clock::now() - m_start;
    }
    bool timeout()
    {
        return elapsed() >= m_interval;
    }
    double elapsedPercentage()
    {
        return clamp(elapsed() / m_interval, 0.0, 1.0);
    }
private:
    TimePoint m_start;
    Duration m_interval;
};

} // unnamed

namespace wo
{

std::chrono::time_point<std::chrono::high_resolution_clock> ExecDocSlimHelper::s_autoSlimTime = std::chrono::high_resolution_clock::time_point::min();
int ExecDocSlimHelper::s_autoSlimCount = 0;

HRESULT ExecDocSlimHelper::Slim(IEtRevisionContext* pCtx, UINT lType, WebStr userID, KEtWorkbook* pWb)
{
    class SlimProgressNotify : public ISlimProgressNotify
    {
    public:
        SlimProgressNotify(WebName name, WebStr userID)
            : m_name(name), m_userID(userID)
            , m_broadcastTimer(std::chrono::milliseconds(500))
        {}
        STDPROC_(BOOL) notify(int count) override
        {
            if (m_broadcastTimer.timeout())
            {
                binary_wo::BinWriter bw;
                bw.addStringField(m_userID, "user");
                bw.addInt32Field(count, m_name);
                BroadcastNow(msgType_docslim, bw);
                m_broadcastTimer.reset();
            }
            return FALSE;
        }
    private:
        WebName m_name;
        WebStr m_userID;
        SlimTimer m_broadcastTimer;
    };

    // ================== start ==================
    {
        binary_wo::BinWriter bw;
        bw.addBoolField(true, "startSlimming");

        int version = 0;
        WebFileSize size = 0;
        const FileSaveInfo& fileInfo = pWb->GetFileSaveInfo();
        bw.addFloat64Field(fileInfo.size, "size");
        BroadcastNow(msgType_docslim, bw);
    }
     
    INT64 nClearNullCellsCount = 0;
    INT32 nDuplicateStylesCount = 0;
    INT32 nUnusedShapesCount = 0;
    INT32 nCompressedPicturesCount = 0;
    INT32 nUnusedCellPicturesCount = 0;
    INT32 nOverlapShapeCount = 0;
    INT32 nDuplicateFormatConditionCount = 0;
    // ================== slimming ==================
    if (alg::IsBitUsed(lType, EST_NullCell))
    {
        SlimProgressNotify cb(NULL_CELLS_NAME, userID);
        m_pDocSlim->DeleteNullCellsOnlyHasXf(&cb);
        nClearNullCellsCount = m_pDocSlim->GetClearNullCellsCount();
    }
    if (alg::IsBitUsed(lType, EST_DuplicateStyles))
    {
        SlimProgressNotify cb(UNUSED_STYLES_NAME, userID);
        m_pDocSlim->DeleteUnusedDuplicateStyle(&cb);
        m_pDocSlim->DeleteSameSheetProtectionUserRange(&cb);
        nDuplicateStylesCount = m_pDocSlim->GetUnusedDuplicateStylesCount();
    }
    if (alg::IsBitUsed(lType, EST_HideShape))
    {
        SlimProgressNotify cb(UNUSED_SHAPES_NAME, userID);
        m_pDocSlim->DeleteUnsedShapes(&cb);
        nUnusedShapesCount = m_pDocSlim->GetUnusedShapesCount();
    }
    if (alg::IsBitUsed(lType, EST_UnusedCellPicture))
    {
        SlimProgressNotify cb(UNUSED_CELL_PICS_NAME, userID);
        m_pDocSlim->DeleteUnusedCellPictures(&cb);
        nUnusedCellPicturesCount = m_pDocSlim->GetUnusedCellPicturesCount();
    }

    if (alg::IsBitUsed(lType, EST_OverlapShapes))
    {
        SlimProgressNotify cb(UNUSED_OVERLAP_SHAPES_NAME, userID);
        m_pDocSlim->DeleteOverlapShapes(&cb);
        nOverlapShapeCount = m_pDocSlim->GetOverlapShapeCount();
    }

    if (alg::IsBitUsed(lType, EST_DuplicateFormatCondition))
    {
        SlimProgressNotify cb(UNUSED_DUPLICATE_FORMAT_CONDITION_NAME, userID);
        m_pDocSlim->DeleteDuplicateFormatCondition(&cb);
        nDuplicateFormatConditionCount = m_pDocSlim->GetDuplicateFormatConditionCount();
    }

    // 要清空已压缩图片的计数 不然如果不选压缩图片的话 计数还是上次的值(如果恰好之前有压缩过图片的话)
    m_pDocSlim->ResetCompressedPicturesCount();
    if (alg::IsBitUsed(lType, EST_CompressPicture))
    {
        SlimProgressNotify cb(PICS_TO_COMPRESSED_NAME, userID);
        m_pDocSlim->CompressPicture(&cb);
        nCompressedPicturesCount = m_pDocSlim->GetCompressedPicturesCount();
    }

    binary_wo::BinWriter bw;
    // ================== end ==================
    {    
        bw.addBoolField(true, "finishSlimming");
        bw.addFloat64Field(nClearNullCellsCount, NULL_CELLS_NAME);
        bw.addInt32Field(nDuplicateStylesCount, UNUSED_STYLES_NAME);
        bw.addInt32Field(nUnusedShapesCount, UNUSED_SHAPES_NAME);
        // 完成瘦身之后发送的是已压缩的图片, 复用了potentialPicturesToBeCompressed这个字段
        bw.addInt32Field(nCompressedPicturesCount, PICS_TO_COMPRESSED_NAME);
        bw.addInt32Field(nUnusedCellPicturesCount, UNUSED_CELL_PICS_NAME);
        bw.addInt32Field(nOverlapShapeCount, UNUSED_OVERLAP_SHAPES_NAME);
        bw.addInt32Field(nDuplicateFormatConditionCount, UNUSED_DUPLICATE_FORMAT_CONDITION_NAME);
        BroadcastNow(msgType_docslim, bw);
        //埋点上报 
        
        QString strDocSlimInfo = QString("nc%1_us%2_ucp%3_cp%4_uds%5_ols%6_dfc%7") 
            .arg(nClearNullCellsCount) 
            .arg(nUnusedShapesCount) 
            .arg(nUnusedCellPicturesCount) 
            .arg(nCompressedPicturesCount) 
            .arg(nDuplicateStylesCount)
            .arg(nOverlapShapeCount)
            .arg(nDuplicateFormatConditionCount); 
        pCtx->collectInfo(QString("fileslimmanual_processed_%1").arg(strDocSlimInfo).toUtf8());
    }

    return S_OK;
}

bool ExecDocSlimHelper::isNeedSlim()
{
    if (s_autoSlimTime == std::chrono::high_resolution_clock::time_point::min())
        return true;
    constexpr double interval = 3600.00;      // 时间间隔, 产品定义, 单位为秒
    constexpr double reset_count_interval = 24.0 * 60.0 * 60.0;
    const auto now {std::chrono::high_resolution_clock::now()};
    const auto elapsed = std::chrono::duration<double>(now - s_autoSlimTime).count();
    if (elapsed > reset_count_interval)
        ExecDocSlimHelper::s_autoSlimCount = 0;
    return ExecDocSlimHelper::s_autoSlimCount < ExecDocSlimHelper::autoSlimFrequency &&
           elapsed >= interval;
}
void ExecDocSlimHelper::recordSlimTime()
{
    s_autoSlimTime = std::chrono::high_resolution_clock::now();
}

HRESULT ExecDocSlimHelper::AutoSlim(IEtRevisionContext* pCtx, binary_wo::VarObj& param, KEtWorkbook* pWb)
{
    class SlimProgressInterrupter : public ISlimProgressNotify
    {
    public:
        SlimProgressInterrupter(SlimTimer &executeTimer)
            : m_executeTimer(executeTimer)
            , m_broadcastTimer(std::chrono::milliseconds(1000))
            , m_counter(0)
        {}
        STDPROC_(BOOL) notify(int count) override
        {
            if (m_executeTimer.timeout())
                return TRUE;
            return FALSE;
        }
    private:
        SlimTimer &m_executeTimer;
        SlimTimer m_broadcastTimer;
        size_t m_counter;
    };

    // ================== checking ==================
    IWoETSettings* const settings = _kso_GetWoEtSettings();

    if (settings->IsEnableAutoSlim())
        m_pDocSlim->CheckNullCellsOnlyHasXf(NULL, TRUE);

    m_pDocSlim->CheckUnusedShapes();
    size_t unusedShapes = m_pDocSlim->GetUnusedShapesCount();

    m_pDocSlim->CheckUnusedCellPictures();
    size_t unusedCellPictures = m_pDocSlim->GetUnusedCellPicturesCount();

    m_pDocSlim->CheckOverlapShapes();
    size_t unusedOverlapShapes = m_pDocSlim->GetOverlapShapeCount();

    m_pDocSlim->CheckDuplicateFormatCondition();
    size_t unusedDuplicateFormatCondition = m_pDocSlim->GetDuplicateFormatConditionCount();

    m_pDocSlim->CheckUnusedDuplicateStyle();
    m_pDocSlim->CheckSameSheetProtectionUserRange();
    size_t unusedDuplicateStyles = m_pDocSlim->GetUnusedDuplicateStylesCount();

    if (unusedDuplicateStyles + unusedDuplicateFormatCondition + unusedOverlapShapes + unusedShapes + unusedCellPictures + m_pDocSlim->GetNullCellsCount() == 0)
        return E_FAIL;
    ExecDocSlimHelper::s_autoSlimCount++;
    // ================== start ==================
    {
        binary_wo::BinWriter bw;
        bw.addBoolField(true, "bAutoSlim");
        bw.addBoolField(true, "startSlimming");
        BroadcastNow(msgType_docslim, bw);
    }
    const FileSaveInfo& fileInfo = pWb->GetFileSaveInfo();
    EtAutoSlimParam stParam1;
    bool bIsBreak  = false;
    bool bTimeOut  = false;
    // ================== slimming ==================
    {
        SlimTimer executeTimer(std::chrono::milliseconds(10000));
        executeTimer.reset();
        SlimProgressInterrupter cb(executeTimer);
        EtAutoSlimParam stParam;
        stParam.m_bIsExecDirect = pCtx->isExecDirect();
        if (pCtx->isExecDirect())
            fillAutoSlimParam(param, stParam);

        bool bNeedBreak  = false;
        if (param.has("bNeedBreak"))
	        bNeedBreak = param.field_bool("bNeedBreak");

        m_pDocSlim->SetAutoSlimParam(stParam);

        if (settings->IsEnableOptimizeNullCells() && !executeTimer.timeout())
            m_pDocSlim->OptimizeNullCellsOnlyHasXf(&cb, TRUE);
        
        if (settings->IsEnableAutoSlim() && !executeTimer.timeout())
            m_pDocSlim->DeleteNullCellsOnlyHasXf(&cb, TRUE);
        
        if (!executeTimer.timeout() && !(bNeedBreak && _kso_GetWoCallBack()->isBreak()))
        {
            m_pDocSlim->DeleteUnsedShapes(&cb, TRUE);
            stParam1.m_bLastDeletedUnusedShapes = TRUE;
        }

        if (!executeTimer.timeout() && !(bNeedBreak && _kso_GetWoCallBack()->isBreak()))
        {
            m_pDocSlim->DeleteUnusedCellPictures(&cb, TRUE);
            stParam1.m_bLastDeletedCellPictures = TRUE;
        }

        if (!executeTimer.timeout() && !(bNeedBreak && _kso_GetWoCallBack()->isBreak()))
        {
            m_pDocSlim->DeleteOverlapShapes(&cb, TRUE);
            stParam1.m_bLastDeletedOverlapShapes = TRUE;
        }

        if (!executeTimer.timeout() && !(bNeedBreak && _kso_GetWoCallBack()->isBreak()))
        {
            m_pDocSlim->DeleteUnusedDuplicateStyle(&cb, TRUE);
            stParam1.m_bLastDeleteUnusedDuplicateStyle = TRUE;
        }

        if (!executeTimer.timeout() && !(bNeedBreak && _kso_GetWoCallBack()->isBreak()))
        {
            m_pDocSlim->DeleteSameSheetProtectionUserRange(&cb, TRUE);
            stParam1.m_bLastDeleteSameSheetProtectionUserRange = TRUE;
        }

        if (!executeTimer.timeout() && !(bNeedBreak && _kso_GetWoCallBack()->isBreak()))
        {
            m_pDocSlim->DeleteDuplicateFormatCondition(&cb, TRUE);
            stParam1.m_bLastDeleteDuplicateFormatCondition = TRUE;
        }
        bTimeOut = executeTimer.timeout();
        if (bNeedBreak)
            bIsBreak = _kso_GetWoCallBack()->isBreak();
    }
    EtAutoSlimResult* pResult = m_pDocSlim->GetAutoSlimResult();
    int iLastClearUnusedShapesCount = 0;
    int iLastClearPicturesCount = 0;
    int iLastClearOverlapShapesCount = 0;
    int iLastClearDuplicateFormatConditionCount = 0;
    int iLastClearUnusedDuplicateStyleCount = 0;
    if (pResult)
    {
        stParam1.m_uLastBlkCount = pResult->m_uLastBlkCount;
        addAutoSlimParam(param, stParam1);
        iLastClearUnusedShapesCount = pResult->m_uLastDeletedUnusedShapesCount;
        iLastClearPicturesCount = pResult->m_uLastDeletedCellPicturesCount;
        iLastClearOverlapShapesCount = pResult->m_uLastDeletedOverlapShapesCount;
        iLastClearDuplicateFormatConditionCount = pResult->m_uLastDeleteDuplicateFormatConditionCount;
        iLastClearUnusedDuplicateStyleCount = pResult->m_uLastDeleteUnusedDuplicateStyleCount + pResult->m_uLastDeleteSameSheetProtectionUserRangeCount;
    }

    m_pDocSlim->ChangeDocAutoSlimState();
    // ================== end ==================
    {
        binary_wo::BinWriter bw;
        bw.addBoolField(true, "bAutoSlim");
        bw.addBoolField(true, "finishSlimming");
        double improvement = estimateImprovement(m_pDocSlim->GetClearNullCellsCount(), unusedShapes, unusedCellPictures);
        bw.addFloat64Field(improvement, "improvement");
        bw.addFloat64Field(m_pDocSlim->GetClearNullCellsCount(), "emptyCell");
        bw.addInt32Field(iLastClearUnusedShapesCount, "invisibleObj");
        bw.addInt32Field(iLastClearPicturesCount, "uselessCellImg");
        bw.addInt32Field(iLastClearOverlapShapesCount, "overlapShapes");
        bw.addInt32Field(iLastClearUnusedDuplicateStyleCount, "duplicateStyle");
        bw.addInt32Field(iLastClearDuplicateFormatConditionCount, "duplicateFormatCondition");
        bw.addFloat64Field(fileInfo.size, "old_size");
        BroadcastNow(msgType_docslim, bw);

        QString strDocSlimInfo = QString("nc%1_us%2_ucp%3_ols%4_uds%5_dfc%6_timeout%7_break%8")
            .arg(m_pDocSlim->GetClearNullCellsCount())
            .arg(iLastClearUnusedShapesCount)
            .arg(iLastClearPicturesCount)
            .arg(iLastClearOverlapShapesCount)
            .arg(iLastClearUnusedDuplicateStyleCount)
            .arg(iLastClearDuplicateFormatConditionCount)
            .arg(bTimeOut)
            .arg(bIsBreak);
        pCtx->collectInfo(QString("fileslimauto_processed_%1").arg(strDocSlimInfo).toUtf8());
    }

    return S_OK;
}

double ExecDocSlimHelper::estimateImprovement(INT64 nullCells, size_t unusedShapes, size_t unusedCellPictures)
{
    double f = nullCells / 20000.0 + unusedShapes / 100.0 + unusedCellPictures / 50.0;

    if (f <= 20.0) return 0.1;
    else if (f <= 50.0) return 0.2;
    else if (f <= 80.0) return 0.3;
    else if (f <= 100.0) return 0.4;
    else if (f <= 200.0) return 0.5;
    else if (f <= 300.0) return 0.6;
    else if (f <= 400.0) return 0.7;
    else return 0.8;
}

HRESULT CheckDocSlimHelper::Check(ISerialAcceptor* acpt, WebStr connID)
{
    class CheckProgressNotify : public ISlimProgressNotify
    {
    public:
        CheckProgressNotify(WebName name, WebStr connID)
            : m_name(name), m_connID(connID)
            , m_broadcastTimer(std::chrono::milliseconds(500))
        {}
        STDPROC_(BOOL) notify(int count) override
        {
            if (m_broadcastTimer.timeout())
            {
                binary_wo::BinWriter bw;
                bw.addBoolField(true, "checking");
                bw.addStringField(m_connID, "connID");
                bw.addInt32Field(count, m_name);
                BroadcastNow(msgType_docslim, bw);
                m_broadcastTimer.reset();
            }
            return FALSE;
        }
    private:
        WebName m_name;
        WebStr m_connID;
        SlimTimer m_broadcastTimer;
    };

    // ================== checking ==================
    {
        CheckProgressNotify cb(NULL_CELLS_NAME, connID);
        m_pDocSlim->CheckNullCellsOnlyHasXf(&cb);
    }
    {
        CheckProgressNotify cb(UNUSED_STYLES_NAME, connID);
        m_pDocSlim->CheckUnusedDuplicateStyle(&cb);
        m_pDocSlim->CheckSameSheetProtectionUserRange();
    }
    {
        CheckProgressNotify cb(UNUSED_SHAPES_NAME, connID);
        m_pDocSlim->CheckUnusedShapes(&cb);
    }
    {
        CheckProgressNotify cb(PICS_TO_COMPRESSED_NAME, connID);
        m_pDocSlim->CheckPotentialPicturesToBeCompressed(&cb);
    }
    {
        CheckProgressNotify cb(UNUSED_CELL_PICS_NAME, connID);
        m_pDocSlim->CheckUnusedCellPictures(&cb);
    }
    {
        CheckProgressNotify cb(UNUSED_OVERLAP_SHAPES_NAME, connID);
        m_pDocSlim->CheckOverlapShapes(&cb);
    }
    {
        CheckProgressNotify cb(UNUSED_DUPLICATE_FORMAT_CONDITION_NAME, connID);
        m_pDocSlim->CheckDuplicateFormatCondition(&cb);
    }

    // ================== end ==================
    {
        acpt->addBool("finishChecking", true);
        acpt->addFloat64(NULL_CELLS_NAME, m_pDocSlim->GetNullCellsCount());
        acpt->addInt32(UNUSED_STYLES_NAME, m_pDocSlim->GetUnusedDuplicateStylesCount());
        acpt->addInt32(UNUSED_SHAPES_NAME, m_pDocSlim->GetUnusedShapesCount());
        acpt->addInt32(PICS_TO_COMPRESSED_NAME, m_pDocSlim->GetPotentialPicturesToBeCompressedCount());
        acpt->addInt32(UNUSED_CELL_PICS_NAME, m_pDocSlim->GetUnusedCellPicturesCount());
        acpt->addInt32(UNUSED_OVERLAP_SHAPES_NAME, m_pDocSlim->GetOverlapShapeCount());
        acpt->addInt32(UNUSED_DUPLICATE_FORMAT_CONDITION_NAME, m_pDocSlim->GetDuplicateFormatConditionCount());
    }

    return S_OK;
}

void ExecDocSlimHelper::fillDeletedRectInfo(const binary_wo::VarObj& param, EtAutoSlimParam& stParam)
{
    if (!param.has("lastDeletedRectsInfo"))
        return;
    binary_wo::VarObj rectArray = param.get("lastDeletedRectsInfo");
    int size = rectArray.arrayLength();
    ES_CUBE cube;
    for (int i = 0; i < size; i++)
    {
        VarObj rectInfo = rectArray.at(i);
        cube.sheetFrom = cube.sheetTo =  rectInfo.field_int32("sheetIdx");
	    cube.rowFrom = rectInfo.field_int32("top");
        cube.rowTo = rectInfo.field_int32("bottom");
	    cube.colFrom = rectInfo.field_int32("left");
        cube.colTo = rectInfo.field_int32("right");
        m_pDocSlim->AddAutoSlimDeletedRect(cube);
        if (rectInfo.has("lastBlkCount"))
            stParam.m_uLastBlkCount = rectInfo.field_uint32("lastBlkCount");
    }
}

void ExecDocSlimHelper::fillAutoSlimParam(const binary_wo::VarObj& param, EtAutoSlimParam& stParam)
{
    fillDeletedRectInfo(param, stParam);

    if (param.has("bLastDeletedUnusedShapes"))
        stParam.m_bLastDeletedUnusedShapes = param.field_uint32("bLastDeletedUnusedShapes");

    if (param.has("bLastDeletedOverlapShapes"))
        stParam.m_bLastDeletedOverlapShapes = param.field_uint32("bLastDeletedOverlapShapes");

    if (param.has("bLastDeletedCellPictures"))
        stParam.m_bLastDeletedCellPictures = param.field_uint32("bLastDeletedCellPictures");

    if (param.has("bLastDeleteDuplicateFormatCondition"))
        stParam.m_bLastDeleteDuplicateFormatCondition = param.field_uint32("bLastDeleteDuplicateFormatCondition");

    if (param.has("bLastDeleteUnusedDuplicateStyle"))
        stParam.m_bLastDeleteUnusedDuplicateStyle = param.field_uint32("bLastDeleteUnusedDuplicateStyle");

    if (param.has("bLastDeleteSameSheetProtectionUserRange"))
        stParam.m_bLastDeleteSameSheetProtectionUserRange = param.field_uint32("bLastDeleteSameSheetProtectionUserRange");

}

void ExecDocSlimHelper::addAutoSlimParam(binary_wo::VarObj& param, const EtAutoSlimParam& stParam)
{
    addDeletedRectInfo(param, stParam);
    param.add_field_bool("bLastDeletedUnusedShapes", stParam.m_bLastDeletedUnusedShapes);
    param.add_field_bool("bLastDeletedOverlapShapes", stParam.m_bLastDeletedOverlapShapes);
    param.add_field_bool("bLastDeletedCellPictures", stParam.m_bLastDeletedCellPictures);
    param.add_field_bool("bLastDeleteDuplicateFormatCondition", stParam.m_bLastDeleteDuplicateFormatCondition);
    param.add_field_bool("bLastDeleteUnusedDuplicateStyle", stParam.m_bLastDeleteUnusedDuplicateStyle);
    param.add_field_bool("bLastDeleteSameSheetProtectionUserRange", stParam.m_bLastDeleteSameSheetProtectionUserRange);
}

void ExecDocSlimHelper::addDeletedRectInfo(binary_wo::VarObj& param, const EtAutoSlimParam& stParam)
{
    int size = 0;
    m_pDocSlim->GetAutoSlimDeletedRectSize(&size);
    if (size == 0)
        return;
    ES_CUBE cube;
    VarObj rectArray = param.add_field_structArray("lastDeletedRectsInfo", size);
    for (int i = 0; i < size; ++i)
    {
        m_pDocSlim->GetAutoSlimDeletedRect(i, &cube);
        VarObj rectInfo = rectArray.at(i);
        rectInfo.add_field_int32("sheetIdx", cube.sheetFrom);
        rectInfo.add_field_int32("top", cube.rowFrom);
        rectInfo.add_field_int32("left", cube.colFrom);
        rectInfo.add_field_int32("bottom", cube.rowTo);
        rectInfo.add_field_int32("right", cube.colTo);
        if (i == size - 1)
            rectInfo.add_field_uint32("lastBlkCount", stParam.m_uLastBlkCount);
    }
}
} // wo
