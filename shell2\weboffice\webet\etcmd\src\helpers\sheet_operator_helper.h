﻿#ifndef __SHEET_OPERATOR_HELPER_H__
#define __SHEET_OPERATOR_HELPER_H__

namespace wo
{
class KEtWorkbook;
namespace SheetOperatorHelper
{
    IDX GetSheetIdx(KEtWorkbook* pEtWb, const binary_wo::VarObj& var);
    HRESULT GetSheetType(const binary_wo::VarObj& param, KComVariant& vType, SHEETTYPE& sheetType);
    HRESULT AddSheet(KEtWorkbook* pEtWb, const binary_wo::VarObj& param, IKCoreObject** ppObj);
    HRESULT CopySheet(KEtWorkbook* pEtWb, const binary_wo::VarObj& param, IKCoreObject** ppObj);
    HRESULT CopySheetsFromBook(KEtWorkbook* pEtWb, _Workbook* pSrcWb, _Workbook* pDstWb,
        const std::vector<int>& vecSrcSheetIdx, std::vector<int>& vecDstSheetIdx, bool bAddNew = false, bool bActivateSheet = true);
    //bIgnoreProtection为true时，表示即使book含有protection属性或sheet是IsProtected，也依旧允许删除。
    HRESULT DeleteSheets(KEtWorkbook* pEtWb, KEtRevisionContext* pCtx, const std::vector<uint>& vecSheetId, bool bIgnoreProtection = false);

    int GetDashBoardSheetLimit();
    IDX GetCopySheetIdxFromParam(KEtWorkbook* pEtWb, const binary_wo::VarObj& param);
    HRESULT RefreshDashBoardBoundaryRowCol(KEtWorkbook* pEtWb, const binary_wo::VarObj& param, IDX dstSheetIdx);
}
}

#endif