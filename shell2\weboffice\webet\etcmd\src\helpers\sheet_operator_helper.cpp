﻿#include "etstdafx.h"
#include "et_revision_context_impl.h"
#include "sheet_operator_helper.h"
#include "binvariant/binvarobj.h"
#include "webdef.h"
#include "workbook.h"
#include "util.h"
#include "app/delete_app_helper.h"
#include "utils/et_gridsheet_utils.h"
#include "appcore/et_appcore_enum.h"
#include "app/find_app_helper.h"

namespace wo
{
namespace SheetOperatorHelper
{
    IDX GetSheetIdx(KEtWorkbook* pEtWb, const binary_wo::VarObj& var)
    {
        IDX sheetIdx = INVALIDIDX;
        IBook* pBook = pEtWb->GetCoreWorkbook()->GetBook();
        if (var.has("sheetStId"))
        {
            UINT32 stId = var.field_uint32("sheetStId");
            pBook->STSheetToRTSheet(stId, &sheetIdx);
        }
        else if (var.has("sheetId")) // server api 统一用的sheetId
        {
            UINT32 stId = var.field_uint32("sheetId");
            pBook->STSheetToRTSheet(stId, &sheetIdx);
        }
        else if (var.has("sheetIdx"))
        {
            sheetIdx = var.field_int32("sheetIdx");
        }
        return sheetIdx;
    }

    HRESULT GetSheetType(const binary_wo::VarObj& param, KComVariant& vType, SHEETTYPE& sheetType)
    {
        if (!param.has("type"))
            return S_OK;

        WebStr strType = param.field_str("type");
        if (xstrcmp(strType, __X("xlEtDataBaseSheet")) == 0)
        {
            sheetType = stGrid_DB;
            vType.Assign(xlWorksheet);
        }
        else if (xstrcmp(strType, __X("xlWorksheet")) == 0)
        {
            sheetType = stGrid;
            vType.Assign(xlWorksheet);
        }
        else if (xstrcmp(strType, __X("xlEtDashBoardSheet")) == 0)
        {
            sheetType = stOldDashBoard;
            vType.Assign(xlWorksheet);
        }
        else if (xstrcmp(strType, __X("xlEtFlexPaperSheet")) == 0)
        {
            sheetType = stFlexPaper;
            vType.Assign(xlWorksheet);
        }
        else if (xstrcmp(strType, __X("xlDbDashBoardSheet")) == 0)
        {
            sheetType = stDashBoard;
            vType.Assign(xlWorksheet);
        }
        else if (xstrcmp(strType, __X("xlEtAppSheet")) == 0)
        {
            sheetType = stApp;
            vType.Assign(xlWorksheet);
        }
        else if (xstrcmp(strType, __X("xlEtWorkbenchSheet")) == 0)
        {
            sheetType = stWorkbench;
            vType.Assign(xlWorksheet);
        }
        else
        {
            ASSERT(FALSE); // 参数错误还不支持
            return E_INVALIDARG;
        }
        return S_OK;
    }

    HRESULT AddSheet(KEtWorkbook* pEtWb, const binary_wo::VarObj& param, IKCoreObject** ppObj)
    {
        ks_stdptr<etoldapi::Worksheets> spSheets;
        pEtWb->GetCoreWorkbook()->get_Worksheets(&spSheets);
        if (!spSheets)
            return E_FAIL;

        KComVariant vType;
        SHEETTYPE sheetType = stGrid;
        HRESULT hr = GetSheetType(param, vType, sheetType);
        if (FAILED(hr))
            return hr;

        switch (sheetType)
        {
		case stDashBoard:
		{
			if (spSheets->GetSheetCount(stDashBoard, TRUE) >= GetDashBoardSheetLimit())
				return E_DBSHEET_DASHBOARD_COUNT_LIMIT;
			break;
		}
		default:
			break;
        }

        KComVariant vBefore;
        if (param.has("before"))
        {
            binary_wo::VarObj before = param.get_s("before");
            IDX sheetIdx = GetSheetIdx(pEtWb, before);
            vBefore = INVALIDIDX == sheetIdx ? 1 : sheetIdx + 1;
        }

        IBook* pBook = pEtWb->GetCoreWorkbook()->GetBook();
        KComVariant vAfter;
        if (param.has("after"))
        {
            binary_wo::VarObj after = param.get_s("after");
            IDX sheetIdx = GetSheetIdx(pEtWb, after);
            vAfter = INVALIDIDX == sheetIdx ? util::getLastSheetIdx(pBook) + 1 : sheetIdx + 1;
        }

        if (param.has("end") && param.field_bool("end"))
            vAfter = util::getLastSheetIdx(pBook) + 1;

        KComVariant vCount;
        if (param.has("count"))
            vCount = param.field_int32("count");
        

        if (sheetType == stWorkbench && !pBook->GetBMP()->bKsheet)
            return E_FAIL;

        ks_stdptr<IKCoreObject> spObj;
        SHEETTYPE initSheetType = sheetType == stWorkbench ? stWorkbench : stGrid;
        hr = spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, initSheetType);
        ks_stdptr<_Worksheet> spWorkSheet = spObj;
        if (!spWorkSheet)
            return E_FAIL;

        if (S_OK == hr && param.has("name"))
        {
			ks_bstr strName;
			GetValidSheetName(spSheets, spWorkSheet, param.field_str("name"), &strName);
			spWorkSheet->put_Name(strName);
        }
        // 有宏就把宏关掉
        pEtWb->GetCoreWorkbook()->GetBook()->GetWoStake()->getSetting()->markMacrosLost();

        if (ppObj)
        {
            *ppObj = spObj.detach();
        }
        return hr;
    }

    HRESULT CopySheet(KEtWorkbook* pEtWb, const binary_wo::VarObj& param, IKCoreObject** ppObj)
    {
        ks_stdptr<etoldapi::Worksheets> spSheets;
        pEtWb->GetCoreWorkbook()->get_Worksheets(&spSheets);
        if (!spSheets)
            return E_FAIL;
        
        IDX copySheetIdx = GetCopySheetIdxFromParam(pEtWb, param);
        if (INVALIDIDX == copySheetIdx)
            return E_FAIL;

        ks_stdptr<IKCoreObject> spCoreObj;
        spSheets->get_Item(KComVariant(copySheetIdx, VT_I4), &spCoreObj);
        if (!spCoreObj)
            return E_FAIL;
        ks_stdptr<etoldapi::_Worksheet> spSheet = spCoreObj;
        if (spSheet->GetSheet()->IsDbDashBoardSheet() && spSheets->GetSheetCount(stDashBoard, TRUE) >= GetDashBoardSheetLimit())
            return E_DBSHEET_DASHBOARD_COUNT_LIMIT;
        
        HRESULT hr = S_OK;

        VARIANT emptyVar;
        V_VT(&emptyVar) = VT_EMPTY;
        ASSERT(!pEtWb->GetBMP()->bDbSheet);   // et
        {
            ks_stdptr<IBookOp> spBookOp;
            pEtWb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
            IBookStake *pBookStake = pEtWb->GetCoreWorkbook()->GetBook()->GetWoStake();
            util::CalcBatchUpdate calcBatchUpdate(spBookOp, pBookStake->HasImportrangeFuncs());

            if (param.has("before"))
            {
                binary_wo::VarObj before = param.get_s("before");
                int sheetIdx = before.field_int32("sheetIdx");
                if (INVALIDIDX == sheetIdx)
                    return E_FAIL;
                sheetIdx += 1;
                ks_stdptr<IKCoreObject> vBefore;
                spSheets->get_Item(KComVariant(sheetIdx, VT_I4), &vBefore);
                if (!vBefore)
                    return E_FAIL;
                KComVariant worksheetVar(static_cast<IKCoreObject*>(vBefore));
                hr = spSheet->Copy(worksheetVar, emptyVar, ppObj);
            }
            else if (param.has("after"))
            {
                binary_wo::VarObj after = param.get_s("after");
                int sheetIdx = after.field_int32("sheetIdx");
                if (INVALIDIDX == sheetIdx)
                    return E_FAIL;
                sheetIdx += 1;
                ks_stdptr<IKCoreObject> vAfter;
                spSheets->get_Item(KComVariant(sheetIdx, VT_I4), &vAfter);
                if (!vAfter)
                    return E_FAIL;
                KComVariant worksheetVar(static_cast<IKCoreObject*>(vAfter));
                hr = spSheet->Copy(emptyVar, worksheetVar, ppObj);
            }
            else
            {
                KComVariant vAfter(static_cast<IKCoreObject*>(spCoreObj));
                hr = spSheet->Copy(emptyVar, vAfter, ppObj);
            }
        }
        return hr;
    }

    HRESULT CopySheetData(KEtWorkbook* pEtWb, _Workbook *ptrWorkbookSrc, _Worksheet *spSheetSrc, _Workbook *ptrWorkbookDst, _Worksheet *spSheetDst)
    {
        IDX idxDstSheet = -1;
        spSheetDst->GetSheet()->GetIndex(&idxDstSheet);

        IDX idxSrcSheet = -1;
        spSheetSrc->GetSheet()->GetIndex(&idxSrcSheet);

        //获取目标bookop
        ks_stdptr<IBookOp> spDstBookOp;
        ptrWorkbookDst->GetBook()->GetOperator(&spDstBookOp);

        //获取源bookop
        ks_stdptr<IBookOp> spSrcBookOp;
        ptrWorkbookSrc->GetBook()->GetOperator(&spSrcBookOp);

        int maxRows = std::min(spSheetSrc->GetSheet()->GetBMP()->cntRows, spSheetDst->GetSheet()->GetBMP()->cntRows);
        int maxCols = std::min(spSheetSrc->GetSheet()->GetBMP()->cntCols, spSheetDst->GetSheet()->GetBMP()->cntCols);

        //获取源数据区域
        RANGE rgDataCopy(spSheetSrc->GetSheet()->GetBMP());
        rgDataCopy.SetRowFromTo(0, maxRows - 1);
        rgDataCopy.SetColFromTo(0, maxCols - 1);
        rgDataCopy.SetSheetFromTo(idxSrcSheet);

        ks_stdptr<etoldapi::Range> rgDataSrc;
        spSheetSrc->GetRangeByData(&rgDataCopy, &rgDataSrc);
        if (!rgDataSrc)
            return E_FAIL;

        //计算粘贴区域
        RANGE rgDataPaste(spSheetDst->GetSheet()->GetBMP());
        rgDataPaste.SetRowFromTo(0, maxRows - 1);
        rgDataPaste.SetColFromTo(0, maxCols - 1);
        rgDataPaste.SetSheetFromTo(idxDstSheet);

        ks_stdptr<etoldapi::Range> rgDataDest;
        spSheetDst->GetRangeByData(&rgDataPaste, &rgDataDest);
        if (!rgDataDest)
            return E_FAIL;

        KCOMPTR(IRangeInfo)
        ptrRangeInfo = rgDataDest;
        if (!ptrRangeInfo)
            return E_FAIL;

        //设置复制区域
        IKAppPersist *pAppPersist = pEtWb->GetCoreApp()->GetAppPersist();
        if (!pAppPersist)
            return E_FAIL;

        pAppPersist->SetCutCopyRange(etCopy, rgDataSrc);

        //粘贴参数
        ks_stdptr<IETPersist> spPersist = pAppPersist->GetPersist();
        if (!spPersist)
            return E_FAIL;

        range_helper::ranges rgs;
        ptrRangeInfo->GetIRanges(&rgs, TRUE);

        PASTEINFO info = {0};
        {
            info.grbits.fPaste = TRUE; // 复制
            info.grbits.fPasteShape = TRUE;
            info.grbits.fRemote = 0;
            info.grbits.fSkipBlanks = FALSE;
            info.grbits.fTranspose = FALSE;
            info.grbits.Operation = poNone;
            info.grbits.fFillByDest = FALSE;
            info.grbits.fHasCells = TRUE;
        }

        ks_stdptr<IKRanges> spPasteRgs;
        HRESULT hr = spPersist->CreatePasteRanges(ptrWorkbookDst->GetBook(), rgs, &info, NULL, NULL, &spPasteRgs);
        if (FAILED(hr))
            return hr;

        ks_stdptr<IKDrawingCanvas> spDestDrawCanvas;
        {
            ks_stdptr<IUnknown> spUnk;
            spSheetDst->GetSheet()->GetExtDataItem(edSheetDrawingCanvas, &spUnk);
            spDestDrawCanvas = spUnk;
            ASSERT(spDestDrawCanvas);
        }
        ks_stdptr<IKDrawingCanvas> spDestCmtDrawCanvas;
        {
            ks_stdptr<IUnknown>	spUnk;
            spSheetDst->GetSheet()->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk);
            spDestCmtDrawCanvas = spUnk;
            ASSERT(spDestCmtDrawCanvas);
        }

        //粘贴
        hr = spPersist->WoPaste(spDestDrawCanvas, spDestCmtDrawCanvas, nullptr);
        if (FAILED(hr))
        {
            return E_FAIL;
        }

        pAppPersist->SetCutCopyRange(etCopyCutNone, nullptr);

        ROW preferRow = INVALID_ROW;
        COL preferCol = INVALID_COL;
        spSheetSrc->GetSheet()->GetPreferredView(&preferRow, &preferCol);
        if (preferRow != INVALID_ROW && preferCol != INVALID_COL)
            spSheetDst->GetSheet()->SetPreferredView(preferRow, preferCol);
        return S_OK;
    }

    HRESULT CopySheetsFromBook(KEtWorkbook* pEtWb, _Workbook* pSrcWb, _Workbook* pDstWb,
        const std::vector<int>& vecSrcSheetIdx, std::vector<int>& vecDstSheetIdx, bool bAddNew, bool bActivateSheet)
    {
        HRESULT hr = S_OK;
        IBook* pCurrentBook = pDstWb->GetBook();
        ks_stdptr<etoldapi::Worksheets> spDstSheets;
        ks_stdptr<etoldapi::Worksheets> spSrcSheets;
        pDstWb->get_Worksheets(&spDstSheets);
        pSrcWb->get_Worksheets(&spSrcSheets);

        for (int i = 0; i < vecSrcSheetIdx.size(); ++i)
        {
            ks_castptr<_Worksheet> spSrcWs = spSrcSheets->GetSheetItem(vecSrcSheetIdx[i]);
            ks_castptr<_Worksheet> spDstWs;
            if (bAddNew)
            {
                ks_stdptr<IKCoreObject> spCoreObj;
                KComVariant varEmpty;
                KComVariant varEnd = util::getLastSheetIdx(pCurrentBook) + 1;
                KComVariant vType;
                SHEETTYPE sheetType = stGrid;
                vType.Assign(xlWorksheet);
                hr = spDstSheets->Add(varEmpty, varEnd, varEmpty, vType, &spCoreObj, sheetType, bActivateSheet);
                spDstWs = spCoreObj;
                if (FAILED(hr))
                    continue;
            }
            else
            {
                spDstWs = spDstSheets->GetSheetItem(vecDstSheetIdx[i]);
            }

            hr = CopySheetData(pEtWb, pSrcWb, spSrcWs, pDstWb, spDstWs);
            if (SUCCEEDED(hr) && bAddNew)
            {
                IDX sheetIdx = INVALIDIDX;
                ks_bstr sheetName;
                spSrcWs->get_Name(&sheetName);
                GetValidSheetName(spDstSheets, spDstWs, sheetName, &sheetName);
                spDstWs->put_Name(sheetName);
                spDstWs->GetSheet()->GetIndex(&sheetIdx);
                vecDstSheetIdx.push_back(sheetIdx);
            }
        }
        return hr;
    }

    ISheetProtection* getSheetProtection(KEtWorkbook* pWb, IDX idxSheet)
    {
        ASSERT(idxSheet >= 0);
        ISheetProtection* res = nullptr;
        if (idxSheet >= pWb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount())
        {
            WOLOG_INFO << "[protection] getSheetProtection failed: " << idxSheet;
            return res;
        }

        IKWorksheet* pWorksheet = pWb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(idxSheet);
        ks_castptr<_Worksheet> pApiWs = pWorksheet;
        if (pApiWs)
        {
            res = pApiWs->GetProtection();
        }

        return res;
    }

    HRESULT DeleteSheets(KEtWorkbook* pEtWb, KEtRevisionContext* pCtx, const std::vector<uint>& vecSheetId, bool bIgnoreProtection/*= false*/)
    {
        //bIgnoreProtection为true时，表示即使book含有protection属性或sheet是IsProtected，也依旧允许删除。
        ks_stdptr<IBookProtection> ptrBookProtection = pEtWb->GetCoreWorkbook()->GetProtection();
        if (!bIgnoreProtection && ptrBookProtection)
        {
            BOOKPROTECTION bookProtection = {0};
            ptrBookProtection->GetProperty(&bookProtection);
            if (bookProtection.bProtectStruct)
                return S_FALSE;
        }

        IBook* pBook = pEtWb->GetCoreWorkbook()->GetBook();
        if (!pBook)
            return S_FALSE;

        INT iListSize = 0;
        pBook->GetSheetCount(&iListSize);
        int iVisibleCnt = 0;
        int visibleDataSourceSheetCnt = 0;
        for (int i = 0; i < iListSize; ++i)
        {
            ks_stdptr<ISheet> spSheet;
            pBook->GetSheet(i, &spSheet);
            if (!spSheet)
                continue;

            if (std::find(vecSheetId.begin(), vecSheetId.end(), spSheet->GetStId()) != vecSheetId.end())
                continue;

            BOOL bVisible = FALSE;
            spSheet->GetVisible(&bVisible);
            if (bVisible)
            {
                iVisibleCnt++;
                if (spSheet->IsAppSheet() || spSheet->IsFpSheet() || spSheet->IsDbDashBoardSheet() || spSheet->IsWorkbenchSheet())
                    continue;
                visibleDataSourceSheetCnt++;
            }
        }

        if (iVisibleCnt < 1 || visibleDataSourceSheetCnt < 1)
            return S_FALSE;

        ks_stdptr<IBookOp> ptrBookOp;
        pBook->GetOperator(&ptrBookOp);
        app_helper::KBatchUpdateCal buc(ptrBookOp);

        HRESULT hr = S_FALSE;
        for (uint sheetId : vecSheetId)
        {
            IDX iSheet = INVALIDIDX;
            pBook->STSheetToRTSheet(sheetId, &iSheet);
            if (iSheet == INVALIDIDX)
                continue;

            if (util::isCellImgListSheet(pBook, iSheet))
                continue;

            IKWorksheet* pWorksheet = pEtWb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
            if (!pWorksheet)
                continue;

            ISheet* pSheet = pWorksheet->GetSheet();
            bool bDashBoard = pSheet->IsDbDashBoardSheet();
            ISheetProtection* pSheetProtection = getSheetProtection(pEtWb, iSheet);
            if (!bDashBoard && !bIgnoreProtection && pSheetProtection && pSheetProtection->IsProtected())
                continue;

            if (pSheet->IsAppSheet())
            {
                ks_stdptr<IAppSheetData> spAppSheetData;
                pSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spAppSheetData);
                IAirApp* pApp = spAppSheetData->GetApp(0);
                PCWSTR sharedId = pApp->GetSharedId();
                if (pApp->GetCategory() == KSheet_AirAppCat_DbSheetView)
                {
                    ks_stdptr<IAirApp_DBView> spAppDbView = pApp;
                    IDBSheetView* pView = spAppDbView->GetDBSheetView();
                    if (pView)
                    {
                        UINT dbsheetId = pView->GetSheetOp()->GetSheetId();
                        UINT relatedEtSheetStId = 0;
                        // 有依赖的et代表是查询应用，删除时需要将查询应用对应的et表删除，因此viewId需要传INV_EtDbId
                        bool bHasRelatedEtSheet = FindAppHelper::FindAppRelatedEtSheetStIdByDbSheetStId(pEtWb, dbsheetId, relatedEtSheetStId);
                        EtDbId viewId = bHasRelatedEtSheet ? INV_EtDbId : pView->GetId();
                        DeleteAppHelper::DelOneAppRelatedDbSheet(pEtWb, dbsheetId, viewId);
                        // 删除查询应用对应的et表后，iSheet会变化，需要更新
                        if (bHasRelatedEtSheet)
                            pBook->STSheetToRTSheet(sheetId, &iSheet);
                    }
                }
                DeleteAppHelper::OnAfterDeleteAppSheet(sharedId);
            }

            WebID sheetObjId = 0;
            AbsObject *obj = pCtx->getSheetMain(iSheet);
            if (obj)
                sheetObjId = obj->objId();

            hr = pWorksheet->DeleteDirectly();
            if (FAILED(hr))
                return hr;

            AbsKeepRepertory* keepRepertory = pCtx->getDocument()->getKeepRepertory();
		    keepRepertory->onCloseMainObject(obj);
            pCtx->onSheetDelete(sheetObjId);
        }
        return hr;
    }

    int GetDashBoardSheetLimit()
    {
        constexpr int maxDashBoardSheet = 30;
        return maxDashBoardSheet;
    }

    IDX GetCopySheetIdxFromParam(KEtWorkbook* pEtWb, const binary_wo::VarObj& param)
    {
        int copySheetIdx = INVALIDIDX;
        ks_stdptr<etoldapi::Worksheets> spSheets;
        pEtWb->GetCoreWorkbook()->get_Worksheets(&spSheets);
        if (!spSheets)
            return INVALIDIDX;

        if (param.has("fromIdx"))
            copySheetIdx = param.field_int32("fromIdx");
        else if (param.has("copyFirstSheet") && param.field_bool("copyFirstSheet"))
        {
            int nCount = pEtWb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount();
            IBook* pBook = pEtWb->GetCoreWorkbook()->GetBook();
            int sheetIdx = 0;
            while (sheetIdx < nCount && util::isCellImgListSheet(pBook, sheetIdx))
                ++sheetIdx;
            copySheetIdx = sheetIdx;
        }
        else
            copySheetIdx = GetSheetIdx(pEtWb, param);

        if (INVALIDIDX == copySheetIdx)
            return INVALIDIDX;

        return copySheetIdx + 1;
    }

    HRESULT RefreshDashBoardBoundaryRowCol(KEtWorkbook* pEtWb, const binary_wo::VarObj& param, IDX dstSheetIdx)
    {
        ks_stdptr<etoldapi::Worksheets> spSheets;
        pEtWb->GetCoreWorkbook()->get_Worksheets(&spSheets);
        if (!spSheets)
            return E_FAIL;
        
        IDX copySheetIdx = GetCopySheetIdxFromParam(pEtWb, param);
        if (copySheetIdx == INVALIDIDX)
            return E_FAIL;
        ks_stdptr<IKCoreObject> spCoreObj;
        spSheets->get_Item(KComVariant(copySheetIdx, VT_I4), &spCoreObj);
        if (!spCoreObj)
            return E_FAIL;
        ks_stdptr<etoldapi::_Worksheet> spSourceWorkSheet = spCoreObj;
        if (!spSourceWorkSheet)
            return E_FAIL;
        ISheet* pSourceSheet = spSourceWorkSheet->GetSheet();
        if (!pSourceSheet)
            return E_FAIL;

        if (pSourceSheet->IsDbDashBoardSheet())
        {
            _Worksheet* pDstWorksheet = static_cast<_Worksheet*>(spSheets->GetSheetItem(dstSheetIdx));
            if (!pDstWorksheet)
                return E_FAIL;
            ISheet* pDstSheet = pDstWorksheet->GetSheet();
            if (!pDstSheet)
                return E_FAIL;
            IKWebExtensionMgr* pWebExtMgr = spSourceWorkSheet->GetWorkbook()->GetWebExtensionMgr();
            if (!pWebExtMgr)
                return E_FAIL;

            UINT nWebExtension = 0;
            pWebExtMgr->GetWebExtensionCount(pDstSheet, &nWebExtension);
            if (nWebExtension >= GetDashBoardSheetLimit())
                return E_DBSHEET_DASHBOARD_CHART_COUNT_LIMIT;

            for (UINT i = 0; i < nWebExtension; ++ i)
            {
                ks_stdptr<IKWebExtension> spDstWebExt;
                HRESULT hr = pWebExtMgr->GetWebExtension(pDstSheet, i, &spDstWebExt);
                if (FAILED(hr))
                    return hr;

                ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = spDstWebExt;
                if (!spDataSourceHost)
                    return E_FAIL;
                WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
                if (dataSourceType == dst_PivotTable)
                {
                    ks_stdptr<IKWebExtension> spSourceWebExt;
                    hr = pWebExtMgr->GetWebExtension(pSourceSheet, i, &spSourceWebExt);
                    if (FAILED(hr))
                        return hr;
                    ks_stdptr<IWorksheetChart> spDstChart = spDstWebExt;
                    ks_stdptr<IWorksheetChart> cpSourceChart = spSourceWebExt;
                    if (spDstChart && cpSourceChart)
                    {
                        UINT boundaryRow = cpSourceChart->GetBoundaryRow();
                        UINT boundaryCol = cpSourceChart->GetBoundaryCol();
                        spDstChart->SetBoundaryRowCol(boundaryRow, boundaryCol);
                    }
                }
            }
        }
        return S_OK;
    }

}  // namespace SheetOperatorHelper
}  // namespace wo
