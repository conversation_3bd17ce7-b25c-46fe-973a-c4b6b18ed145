#ifndef __WEBET_ET_XVA_CMD_H__
#define __WEBET_ET_XVA_CMD_H__
#include "workbook.h"

namespace wo
{
class KEtWorkbooks;
class KEtWorkbook;

/**
 * @brief Dispatch ExecDetail::ExecExtraCommand
 * 
 */
class XvaCommand
{
private:
    typedef WebInt (XvaCommand::*CmdFuncPtr)(const VarObj&, const VarObj&, BinWriter&);

    std::unordered_map<std::string, CmdFuncPtr> funcMap;

public:
	XvaCommand(KEtWorkbooks* wbs, KEtWorkbook* wb);
	~XvaCommand();

	WebInt ExecExtraCmd(WebStr command, const VarObj& root, const VarObj& param, BinWriter &writer);
	WebInt ExecQueryDocProperties(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);

private:
	WebInt ExecQueryDocPageCount(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);
	WebInt ExecQueryFonts(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);

	WebInt ExecDocSlim(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);
	WebInt ExecQuerySheets(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);

	WebInt ExecMergeDocument(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);
	WebInt ExecSplitDocument(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);
    WebInt ExecUpdatePassword(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);

	WebInt ExecAddWatermark(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);
	WebInt ExecJsApi(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);

	WebInt ExecExtractContent(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);
    WebInt ExecCellImg2FloatImg(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer);
private:
	bool getExportSheet(int iSheet, ks_stdptr<_Worksheet> &spSheet);
	_Application* GetCoreApplication();
	_Workbook* GetCoreWorkbook();
	WebInt writeDocumentProperty(DocumentProperty* prop, BinWriter& writer);
	WebInt writeDocumentProperties(DocumentProperties* props, BinWriter& writer);

protected:
	KEtWorkbooks* 	m_wbs;
	KEtWorkbook* 	m_workbook;
};

}

#endif //__WEBET_ET_XVA_CMD_H__
