﻿set(CRYPTOPP)
if(CPU_AARCH64)
    set(<PERSON><PERSON><PERSON><PERSON> "cryptopp")
endif()

wps_package(webet SHARED)
	wps_add_definitions(
		QT_CORE_LIB
		QT_GUI_LIB
		QT_XML_LIB
		_USRDLL
		__SHELL_MODULE__
		OPL_LIB
		APPLOGIC_LIB
		)
	wps_include_directories(
		../include
		../../include
		Coding/core_bundle/include
		Coding/core_bundle/include/webbase
		Coding/core_bundle/office/include
		Coding/core_bundle/office/et/include
		Coding/misc_bundle/3rdparty/include
		Coding/misc_bundle/3rdparty
		Coding/plugin_bundle/shell2/plugins/include
		Coding/core_bundle/office
		../webcommon/include
		etcmd/src
		etcmd
		WIN(
			../../kxshare
			)
		Coding/api_bundle/api/common
		Coding/core_bundle
		Coding/shell_bundle/shell2/weboffice
		Coding/shell_bundle/shell2/weboffice/webcommon
        Coding/io_bundle/io/ooxml
		)
	wps_include_packages(
		${qt_packages}
		WIN(
			ksaddndr
			)
		)
	wps_set_delayload(
		DELAYLOAD_PACKAGE etsolver
		DELAYLOAD_FILE WIN(gdiplus.dll msdrm.dll)
		)
	wps_use_packages(
		Qt5Gui
		Qt5Gui
		Qt5Svg
		Qt5Widgets
		thrift
		LINUX(libsafec)
		jsoncpp
		tinyxml
		${CRYPTOPP})
	wps_add_sources(
		PCH etcmd/etstdafx.h etcmd/etstdafx.cpp
		QT_AUTOMOC
		Coding/core_bundle/office/et/include/wo/bw_helper.h
		Coding/core_bundle/office/et/include/wo/sa_helper.h
		Coding/core_bundle/office/et/include/wo/workbook_obj.h
		etcmd/src/etentrys.cpp
		etcmd/src/etentrys.h
		etcmd/src/et_head.h
		etcmd/src/helpers/db_field_recognize_helper.cpp
		etcmd/src/helpers/db_field_recognize_helper.h
		etcmd/src/helpers/db_field_copy_helper.cpp
		etcmd/src/helpers/db_field_copy_helper.h
		etcmd/src/helpers/picture_upload_helper.h
		etcmd/src/helpers/picture_upload_helper.cpp
		etcmd/src/helpers/litefile_helper.h
		etcmd/src/helpers/litefile_helper.cpp
		etcmd/src/helpers/pivot_tables_helper.h
		etcmd/src/helpers/pivot_tables_helper.cpp
		etcmd/src/helpers/list_objects_helper.h
		etcmd/src/helpers/list_objects_helper.cpp
		etcmd/src/helpers/autofilter_helper.h
		etcmd/src/helpers/autofilter_helper.cpp
		etcmd/src/helpers/quickset_helper.h
		etcmd/src/helpers/formula_idx_generator.h
		etcmd/src/helpers/formula_idx_generator.cpp
		etcmd/src/helpers/quickset_helper.cpp
		etcmd/src/helpers/subscription_helper.h
		etcmd/src/helpers/subscription_helper.cpp
		etcmd/src/helpers/webmime_helper.h
		etcmd/src/helpers/webmime_helper.cpp
		etcmd/src/helpers/supbookcache_helper.h
		etcmd/src/helpers/supbookcache_helper.cpp
		etcmd/src/helpers/slim_helper.h
		etcmd/src/helpers/slim_helper.cpp
		etcmd/src/helpers/shape_helper.h
		etcmd/src/helpers/shape_helper.cpp
		etcmd/src/helpers/hyperlink_helper.h
		etcmd/src/helpers/hyperlink_helper.cpp
		etcmd/src/helpers/protection_helper.h
		etcmd/src/helpers/protection_helper.cpp
		etcmd/src/helpers/attachment_helper.h
		etcmd/src/helpers/attachment_helper.cpp
		etcmd/src/helpers/coreconquer_helper.cpp
		etcmd/src/helpers/coreconquer_helper.h
		etcmd/src/helpers/table_struct_rec_helper.cpp
		etcmd/src/helpers/table_struct_rec_helper.h
		etcmd/src/helpers/inquirer_helper.cpp
		etcmd/src/helpers/inquirer_helper.h
		etcmd/src/helpers/copy_helper.cpp
		etcmd/src/helpers/copy_helper.h
		etcmd/src/helpers/copy_link_helper.cpp
		etcmd/src/helpers/copy_link_helper.h
		etcmd/src/helpers/varobject_helper.h
		etcmd/src/helpers/sheet_operator_helper.cpp
		etcmd/src/helpers/sheet_operator_helper.h
		etcmd/src/helpers/col_operator_helper.cpp
		etcmd/src/helpers/col_operator_helper.h
		etcmd/src/helpers/custom_storage_helper.cpp
		etcmd/src/helpers/custom_storage_helper.h
		etcmd/src/helpers/range_operator_helper.cpp
		etcmd/src/helpers/range_operator_helper.h
		etcmd/src/helpers/serialize_variant_helper.cpp
		etcmd/src/helpers/serialize_variant_helper.h
		etcmd/src/helpers/qrlabel_helper.h
		etcmd/src/helpers/qrlabel_helper.cpp
		etcmd/src/helpers/jsapi_helper.h
		etcmd/src/helpers/jsapi_helper.cpp
		etcmd/src/helpers/jsapi_context.h
		etcmd/src/helpers/jsapi_context.cpp
		etcmd/src/helpers/jsapi/KjEtRangeEx.h
		etcmd/src/helpers/jsapi/KjEtRangeEx.cpp
		etcmd/src/helpers/app/create_app_view_helper.cpp
		etcmd/src/helpers/app/create_app_view_helper.h
		etcmd/src/helpers/app/update_app_view_helper.cpp
		etcmd/src/helpers/app/update_app_view_helper.h
		etcmd/src/helpers/app/delete_app_helper.cpp
		etcmd/src/helpers/app/delete_app_helper.h
		etcmd/src/helpers/app/find_app_helper.cpp
		etcmd/src/helpers/app/find_app_helper.h
		etcmd/src/helpers/app/update_app_version_helper.cpp
		etcmd/src/helpers/app/update_app_version_helper.h
		etcmd/src/helpers/app/remove_app_related_sheet.cpp
		etcmd/src/helpers/app/remove_app_related_sheet.h
		etcmd/src/helpers/app/export_airapp_helper.cpp
		etcmd/src/helpers/app/export_airapp_helper.h
		etcmd/src/helpers/db_token_helper.h
		etcmd/src/helpers/statsheet_helper.cpp
		etcmd/src/helpers/statsheet_helper.h
		etcmd/src/helpers/merge_range_helper.cpp
		etcmd/src/helpers/merge_range_helper.h
		etcmd/src/dashboard/et_dashboard_utils.h
		etcmd/src/dashboard/et_dashboard_utils.cpp
		etcmd/src/dashboard/webchart_config.h
		etcmd/src/dashboard/webchart_config.cpp
		etcmd/src/helpers/db_value_serialize_helper.h
		etcmd/src/helpers/db_value_serialize_helper.cpp
		etcmd/src/database/database_def.h
		etcmd/src/database/database_utils.h
		etcmd/src/database/database_utils.cpp
		etcmd/src/database/database.h
		etcmd/src/database/database.cpp
		etcmd/src/database/database_field_context.h
		etcmd/src/database/database_field_context.cpp
		etcmd/src/database/database_field.h
		etcmd/src/database/database_field.cpp
		etcmd/src/database/database_field_modifier.h
		etcmd/src/database/database_field_modifier.cpp
		etcmd/src/database/database_field_identifier.h
		etcmd/src/database/database_field_identifier.cpp
		etcmd/src/database/database_field_initialiser.h
		etcmd/src/database/database_field_initialiser.cpp
		etcmd/src/database/et_database_task_class.h
		etcmd/src/database/et_database_task_class.cpp
		etcmd/src/dbsheet/et_dbsheet_filter_helper.cpp
		etcmd/src/dbsheet/et_dbsheet_filter_helper.h
		etcmd/src/dbsheet/et_dbsheet_copysheet_helper.h
		etcmd/src/dbsheet/et_dbsheet_copysheet_helper.cpp
		etcmd/src/dbsheet/copy_dashboard_sheet_helper.h
		etcmd/src/dbsheet/copy_dashboard_sheet_helper.cpp
		etcmd/src/dbsheet/et_dbsheet_task_class.h
		etcmd/src/dbsheet/et_dbsheet_task_class.cpp
		etcmd/src/dbsheet/et_dbsheet_query_class.h
		etcmd/src/dbsheet/et_dbsheet_query_class.cpp
		etcmd/src/dbsheet/et_dbsheet_common_helper.h
		etcmd/src/dbsheet/et_dbsheet_common_helper.cpp
		etcmd/src/dbsheet/et_dbsheet_block_init_helper.h
		etcmd/src/dbsheet/et_dbsheet_block_init_helper.cpp
		etcmd/src/dbsheet/et_dbsheet_utils.h
		etcmd/src/dbsheet/et_dbsheet_utils.cpp
		etcmd/src/dbsheet/et_dbsheet_syncsheet_utils.h
		etcmd/src/dbsheet/et_dbsheet_syncsheet_utils.cpp
		etcmd/src/dbsheet/et_dbsheet_dashboard_utils.h
		etcmd/src/dbsheet/et_dbsheet_dashboard_utils.cpp
		etcmd/src/dbsheet/db_gridsheet_sync_helper.h
		etcmd/src/dbsheet/db_gridsheet_sync_helper.cpp
		etcmd/src/dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h
		etcmd/src/dbsheet/dashboard/db_dashboard_module_mgr_wrapper.cpp
		etcmd/src/dbsheet/dashboard/db_dashboard_module_wrapper.h
		etcmd/src/dbsheet/dashboard/db_dashboard_module_wrapper.cpp
		etcmd/src/dbsheet/dashboard/db_richtext_wrapper.h
		etcmd/src/dbsheet/dashboard/db_richtext_wrapper.cpp
		etcmd/src/dbsheet/dashboard/db_plugin_wrapper.h
		etcmd/src/dbsheet/dashboard/db_plugin_wrapper.cpp
		etcmd/src/dbsheet/dashboard/db_view_wrapper.h
		etcmd/src/dbsheet/dashboard/db_view_wrapper.cpp
		etcmd/src/dbsheet/dashboard/db_chart_wrapper.h
		etcmd/src/dbsheet/dashboard/db_chart_wrapper.cpp
		etcmd/src/dbsheet/dashboard/db_dashboard_query_class.h
		etcmd/src/dbsheet/dashboard/db_dashboard_query_class.cpp
		etcmd/src/dbsheet/dashboard/db_dashboard_task_class.h
		etcmd/src/dbsheet/dashboard/db_dashboard_task_class.cpp
		etcmd/src/dbsheet/et_dbsheet_provider.h
		etcmd/src/dbsheet/et_dbsheet_provider.cpp
		etcmd/src/dbsheet/common_log/common_log_value_serialiser.h
		etcmd/src/dbsheet/common_log/common_log_value_serialiser.cpp
		etcmd/src/http/db_dashboard_http_query_class.h
		etcmd/src/http/db_dashboard_http_query_class.cpp
		etcmd/src/http/db_dashboard_http_task_class.h
		etcmd/src/http/db_dashboard_http_task_class.cpp
		etcmd/src/http/db_http_task_class.h
		etcmd/src/http/db_http_task_class.cpp
		etcmd/src/http/db_http_query_class.h
		etcmd/src/http/db_http_query_class.cpp
		etcmd/src/http/db_value_serialiser.h
		etcmd/src/http/db_value_serialiser.cpp
		etcmd/src/http/http_error.h
		etcmd/src/http/http_error.cpp
		etcmd/src/http/http_macro.h
		etcmd/src/http/et_http_task_class.h
		etcmd/src/http/et_http_task_class.cpp
		etcmd/src/http/et_http_query_class.h
		etcmd/src/http/et_http_query_class.cpp
		etcmd/src/http/et_http_underStandable_type_helper.h
		etcmd/src/http/et_http_underStandable_type_helper.cpp
		etcmd/src/http/et_form_http_task_class.h
		etcmd/src/http/et_form_http_task_class.cpp
		etcmd/src/http/db_token_generator.h
		etcmd/src/webhook/tracker_scope.h
		etcmd/src/webhook/webhook_data_create_helper.h
		etcmd/src/webhook/webhook_data_create_helper.cpp
		etcmd/src/webhook/webhook_data_create_helper_old.h
		etcmd/src/webhook/webhook_data_create_helper_old.cpp
		etcmd/src/webhook/webhook_data_old.h
		etcmd/src/webhook/webhook_data_old.cpp
		etcmd/src/webhook/webhook_param_validator.h
		etcmd/src/webhook/webhook_param_validator.cpp
		etcmd/src/webhook/webhook_data_validator.h
		etcmd/src/webhook/webhook_data_validator.cpp
		etcmd/src/dbsheet/db_export_helper.h
		etcmd/src/dbsheet/db_export_helper.cpp
		etcmd/src/dbsheet/et2db_exporter.h
		etcmd/src/dbsheet/et2db_exporter.cpp
		etcmd/src/dbsheet/db2et_exporter.h
		etcmd/src/dbsheet/db2et_exporter.cpp
		etcmd/src/dbsheet/db_sidebar_folder_tree_helper.h
		etcmd/src/dbsheet/db_sidebar_folder_tree_helper.cpp
		etcmd/src/book_format_converter/et2ksheet_converter.h
		etcmd/src/book_format_converter/et2ksheet_converter.cpp
		etcmd/src/book_format_converter/db2ksheet_converter.h
		etcmd/src/book_format_converter/db2ksheet_converter.cpp
		etcmd/src/book_format_converter/db_append_data_adapter.h
		etcmd/src/book_format_converter/db_append_data_adapter.cpp
		etcmd/src/book_format_converter/csv_adapter.h
		etcmd/src/book_format_converter/csv_adapter.cpp
		etcmd/src/book_format_converter/form2db_converter.h
		etcmd/src/book_format_converter/form2db_converter.cpp
		etcmd/src/book_format_converter/db_sync_adapter.h
		etcmd/src/book_format_converter/db_sync_adapter.cpp
		etcmd/src/utils/attachment_utils.h
		etcmd/src/utils/attachment_utils.cpp
		etcmd/src/utils/qrlabel_utils.h
		etcmd/src/utils/qrlabel_utils.cpp
		etcmd/src/utils/sheet_rename_utils.h
		etcmd/src/utils/sheet_rename_utils.cpp
		etcmd/src/utils/et_gridsheet_utils.h
		etcmd/src/utils/et_gridsheet_utils.cpp
		etcmd/src/utils/pixel_img_util.h
		etcmd/src/utils/pixel_img_util.cpp
		etcmd/src/utils/file_merge_utils.h
		etcmd/src/utils/file_merge_utils.cpp
		etcmd/src/dbsheet/txt_parser.h
		etcmd/src/dbsheet/txt_parser.cpp
		etcmd/src/dbsheet/db_json_data_helper.h
		etcmd/src/dbsheet/db_json_data_helper.cpp
		etcmd/src/dbsheet/db_cohistories/db_auth_query_cohistories.h
		etcmd/src/dbsheet/db_cohistories/db_auth_query_cohistories.cpp
		etcmd/src/db_query_server.h
		etcmd/src/db_query_server.cpp
		etcmd/src/dbsheet/db_protection_context_impl.cpp
		etcmd/src/dbsheet/db_protection_context_impl.h
		etcmd/src/dbsheet/db_view_identify.cpp
		etcmd/src/dbsheet/db_view_identify.h
		etcmd/src/ksheet/ksheet_protection_context_impl.cpp
		etcmd/src/ksheet/ksheet_protection_context_impl.h
		etcmd/src/ksheet/appsheet_copysheet_helper.cpp
		etcmd/src/ksheet/appsheet_copysheet_helper.h
		etcmd/src/importer/workbook2et_importer.h
		etcmd/src/importer/workbook2et_importer.cpp
		etcmd/src/importer/workbook2ksheet_importer.h
		etcmd/src/importer/workbook2ksheet_importer.cpp
		etcmd/src/importer/workbook_importer.h
		etcmd/src/importer/workbook_importer.cpp
		etcmd/src/importer/webextension_context_restorer.h
        etcmd/src/importer/webextension_context_restorer.cpp
		etcmd/src/importer/sheets_importer.h
		etcmd/src/importer/sheets_importer.cpp
		etcmd/src/addTemplateSheets.h
		etcmd/src/addTemplateSheets.cpp
		etcmd/src/autofilteritemshelper.h
		etcmd/src/autofilteritemshelper.cpp
		etcmd/src/autofilterparamhelper.h
		etcmd/src/autofilterparamhelper.cpp
		etcmd/src/block_point.cpp
		etcmd/src/block_point.h
		etcmd/src/ss_rect.h
		etcmd/src/etapplication.cpp
		etcmd/src/etapplication.h
		etcmd/src/exec_detail.cpp
		etcmd/src/exec_detail.h
		etcmd/src/exp_autofilter_list_helper.cpp
		etcmd/src/exp_autofilter_list_helper.h
		etcmd/src/svg/woexportsvg.cpp
		etcmd/src/svg/woexportsvg.h
		etcmd/src/thumbnail/woexportthumbnail.cpp
		etcmd/src/thumbnail/woexportthumbnail.h
		etcmd/src/et_binvar_spec.h
		etcmd/src/et_query_class.cpp
		etcmd/src/et_query_class.h
		etcmd/src/split_book_opt.cpp
		etcmd/src/split_book_opt.h
		etcmd/src/et_query_executor.cpp
		etcmd/src/et_query_executor.h
		etcmd/src/filter_context_imp.cpp
		etcmd/src/filter_context_imp.h
		etcmd/src/hresult_to_string.cpp
		etcmd/src/hresult_to_string.h
		etcmd/src/et_revision_context_impl.cpp
		etcmd/src/et_revision_context_impl.h
		etcmd/src/et_task_detail_xf.cpp
		etcmd/src/et_task_detail_xf.h
		etcmd/src/et_task_class.cpp
		etcmd/src/et_task_class.h
		etcmd/src/form/et_form_task_class.cpp
		etcmd/src/form/et_form_task_class.h
		etcmd/src/pagesetup_helper.cpp
		etcmd/src/pagesetup_helper.h
		etcmd/src/sort_helper.cpp
		etcmd/src/sort_helper.h
		etcmd/src/condition_format_helper.h
		etcmd/src/condition_format_helper.cpp
		etcmd/src/condition_format_wrapper.h
		etcmd/src/condition_format_wrapper.cpp
		etcmd/src/condition_format_cache.h
		etcmd/src/condition_format_cache.cpp
		etcmd/src/dv_custom_list_cache.cpp
		etcmd/src/dv_custom_list_cache.h
		etcmd/src/et_task_executor.cpp
		etcmd/src/et_task_executor.h
		etcmd/src/text2col_helper.cpp
		etcmd/src/text2col_helper.h
		etcmd/src/et_task_peripheral.cpp
		etcmd/src/et_task_peripheral.h
		etcmd/src/et_xva_cmd.cpp
		etcmd/src/et_xva_cmd.h
		etcmd/src/revision_ext.cpp
		etcmd/src/revision_ext.h
		etcmd/src/workbooks.cpp
		etcmd/src/workbooks.h
		etcmd/src/workbook.cpp
		etcmd/src/workbook.h
		etcmd/src/wobatchtrans.h
		etcmd/src/wobatchtrans.cpp
		etcmd/src/kmetrics_i.h
		etcmd/src/ksignalbinwebstr.h
		etcmd/src/kcoremetrics.h
		etcmd/src/kcoremetrics.cpp
		etcmd/src/ksignalmetrics.h
		etcmd/src/ksignalmetrics.cpp
		etcmd/src/ksignalbinnode.h
		etcmd/src/ksignalbinnode.cpp
		etcmd/src/kbinreadertravel.h
		etcmd/src/kbinreadertravel.cpp
        etcmd/src/userorganizes.cpp
        etcmd/src/userorganizes.h
		etcmd/src/autofill/dblclickfillhandler.cpp
		etcmd/src/autofill/dblclickfillhandler.h
		etcmd/src/op_transform/condfmt_tf.cpp
		etcmd/src/op_transform/condfmt_tf.h
		etcmd/src/op_transform/et_op_tf.cpp
		etcmd/src/op_transform/et_op_tf.h
		etcmd/src/op_transform/operation.cpp
		etcmd/src/op_transform/operation.h
		etcmd/src/op_transform/rect_tf.cpp
		etcmd/src/op_transform/rect_tf.h
		etcmd/src/smart_tips/smart_tips.cpp
		etcmd/src/smart_tips/smart_tips.h
		etcmd/src/sort/sortitem_loader.h
		etcmd/src/sort/sortitem_loader.cpp
		etcmd/src/sort/sort_protoc.h
		etcmd/src/sort/sort_protoc.cpp
		etcmd/src/censor/exportcensordata.cpp
		etcmd/src/censor/exportcensordata.h
		etcmd/src/censor/censordatahighlight.h
		etcmd/src/censor/censordatahighlight.cpp
		etcmd/src/autoupdateutil/mergefile_util.h
		etcmd/src/autoupdateutil/mergefile_util.cpp
		etcmd/src/insertpicture/insertpicture_helper.h
		etcmd/src/insertpicture/insertpicture_helper.cpp
		etcmd/src/share_link_content_visibility_checker.h
        etcmd/src/share_link_content_visibility_checker.cpp
        etcmd/src/share_sheet_helper.cpp
		etcmd/src/share_sheet_helper.h
		etcmd/src/supbooks_helper.h
		etcmd/src/supbooks_helper.cpp
		etcmd/src/fileInfoCollect.h
		etcmd/src/fileInfoCollect.cpp
		etcmd/src/pasteinfocollect.h
		etcmd/src/pasteinfocollect.cpp
		etcmd/src/remote_chart/message_producer.h
		etcmd/src/remote_chart/message_producer.cpp
		etcmd/src/remote_chart/range_subscription.h
		etcmd/src/remote_chart/range_subscription.cpp
		etcmd/src/remote_chart/remote_chart_helper.h
		etcmd/src/remote_chart/remote_chart_helper.cpp
		etcmd/etshared/etshareapplication.cpp
		etcmd/etshared/etshareapplication.h
		etcmd/etshared/etcomaddinproxy.h
		etcmd/etshared/etcomaddinproxy.cpp
		etcmd/etshared/etlinkeddeque.h
		etcmd/etshared/etmodules.h
		etcmd/etshared/etmodules.cpp
		webet.def
		et_link.cpp
		webetlink.cpp
		webetlink.h
		webettest.cpp
		webettest.h
		etcmd/src/protection_context_impl.h
		etcmd/src/protection_context_impl.cpp
		etcmd/src/util.h
		etcmd/src/util.cpp
		etcmd/src/woetsetting.h
		etcmd/src/woetsetting.cpp
		etcmd/src/etconfig.h
		etcmd/src/collect.h
		etcmd/src/collect.cpp
		etcmd/src/woeventdispatcher.h
		etcmd/src/woeventdispatcher.cpp
		etcmd/src/et_ai/common/identifytable.h
		etcmd/src/et_ai/common/identifytable.cpp
		etcmd/src/et_ai/common/kdocutils.h
		etcmd/src/et_ai/common/kdocutils.cpp
		etcmd/src/et_ai/smart_analysis/identifytable.h
		etcmd/src/et_ai/smart_analysis/identifytable.cpp
		etcmd/src/et_ai/smart_analysis/smartanalysis.h
		etcmd/src/et_ai/smart_analysis/analysistable.h
		etcmd/src/et_ai/smart_analysis/analysistable.cpp
		etcmd/src/et_ai/teval.cpp
		etcmd/src/et_ai/teval.h
		etcmd/src/et_ai/proof/proofreaderdata.cpp
		etcmd/src/et_ai/proof/proofreaderdata.h
		etcmd/src/et_ai/proof/recognizedata.cpp
		etcmd/src/et_ai/proof/recognizedata.h
		etcmd/src/et_ai/proof/proofreader.h
		etcmd/src/et_ai/proof/proofreader.cpp
		etcmd/src/et_ai/beautify/karrangeprocess.cpp
		etcmd/src/et_ai/beautify/karrangeprocess.h
		etcmd/src/et_ai/beautify/kxarrangeprocessdata.cpp
		etcmd/src/et_ai/beautify/kxarrangeprocessdata.h
		etcmd/src/wo_data_analyze/data_analyze.h
		etcmd/src/wo_data_analyze/data_analyze.cpp
		etcmd/src/wo_data_analyze/data_analyze_helper.h
		etcmd/src/wo_data_analyze/data_analyze_helper.cpp
		etcmd/src/wo_data_analyze/data_analyze_task.h
		etcmd/src/wo_data_analyze/data_analyze_task.cpp
		)
	
	wps_link_packages(
		webcommon
		algdiag
		kso
		kbase
		krt
		revision
		etcore
		weblog
		etdacore
		parser
		webhook_core
		ettools
		db
		)
	if (OS_UNIX)
		wps_link_dl()
		wps_link_pthread()
	endif()
	wps_set_delayload(
		DELAYLOAD_PACKAGE etsolver
		DELAYLOAD_FILE WIN(gdiplus.dll msdrm.dll)
		)
	wps_set_incompatible_dep()
	wps_declare_fileinfo(
		FILE_DESCRIPTION "Web ET"
		)
wps_end_package()
