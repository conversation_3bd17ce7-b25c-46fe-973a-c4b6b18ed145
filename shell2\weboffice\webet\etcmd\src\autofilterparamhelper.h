﻿/* -------------------------------------------------------------------------
//	文件名		：	autofilterparamhelper.h
//	创建者		：	陈嘉丰
//	创建时间	：	2018-4-22
//	功能描述	：
//
// -----------------------------------------------------------------------*/

#ifndef __AUTO_FILTER_PARAM_HELPER_H__
#define __AUTO_FILTER_PARAM_HELPER_H__

#include "appcore/et_appcore_basic_itf.h"
#include "appcore/et_appcore_cellcolor_itf.h"
#include "webbase/binvariant/binvarobj.h"

interface IFilterRowContainter;

namespace wo
{
using namespace binary_wo;
enum CUSTOM_CONDITION_TYPE
{
	cct_null	= 0,
	cct_equals	= 1, //等于
	cct_notEqu	= 2, //等于
	cct_greater	= 3, //大于
	cct_greaterEqu	= 4, //大于等于
	cct_less	= 5, //大于
	cct_lessEqu	= 6, //大于等于
	cct_beginWith	= 7, //开头是
	cct_notBeginWith	= 8, //开头是
	cct_endWith	= 9, //开头不是
	cct_notEndWith	= 10, //结尾不是
	cct_contains	= 11, //包含
	cct_notContains	= 12, //不包含
	cct_between	= 13, //介于
	cct_notBetween = 14, //不介于
};

class AutofilterParamSerialHelper
{
public:
	enum Options
	{
		opt_widget,
		opt_tips,
	};

	AutofilterParamSerialHelper();
	void InitForWidget(int nField, IKAutoFilter* pAutoFilter, 
		IAutoFilterValues* pFilterValues, IBook* pBook);
	void InitForTips(int nField, IKAutoFilter* pAutoFilter, IBook* pBook);


	void SerializeEmptyTips(ISerialAcceptor* acpt);
	void Serialize(ISerialAcceptor* acpt, binary_wo::VarObj obj = binary_wo::VarObj());
	static PCWSTR ETFilterOperatorToStr(ETFilterOperator ty);
private:
	static PCWSTR CustomConditionTypeToStr(CUSTOM_CONDITION_TYPE ty);
	static PCWSTR DynamicFilterCriteriaToStr(ET_DYNAMIC_FILTER_CRITERIA ty);
	void serializeValuesForWidgit(ISerialAcceptor* acpt, binary_wo::VarObj obj = binary_wo::VarObj());
	void serializeValuesForTips(ISerialAcceptor* acpt);
	void serializeValuesForTips(binary_wo::VarObj& obj);
	void serializeTextSet(ICriteriaTextSet * pTextSet, binary_wo::VarObj & resObj);
	void serializeDateList(ICriteriaDateList * pList, binary_wo::VarObj & resObj);
	PCWSTR ETDataTimeGroupingToStr(ETDateTimeGrouping dtGrouping);
	void serializeDynamic(ISerialAcceptor * acpt, binary_wo::VarObj obj = binary_wo::VarObj());
	void serializeCellColor(ISerialAcceptor * acpt, binary_wo::VarObj obj = binary_wo::VarObj());
	void serializeFontColor(ISerialAcceptor * acpt, binary_wo::VarObj obj = binary_wo::VarObj());
	void SerializeIcon(ISerialAcceptor* acpt, binary_wo::VarObj obj = binary_wo::VarObj());
	void serializeCustom(ETFilterOperator fOp, ISerialAcceptor* acpt, binary_wo::VarObj obj = binary_wo::VarObj());
	BOOL ParserCustomCriteria(const QString& str, ET_CUSTOM_FILTER_TYPE filterType, CUSTOM_CONDITION_TYPE& condType, QString& val);
	bool doubleStrToDate(QString& val);

private:
	int m_nField;
	IKAutoFilter* m_pAutoFilter;
	ks_stdptr<IAutoFilterValues> m_spFilterValues;
	IBook* m_pBook;
	Options m_opt;
};

class AutofilterParamGenerator
{
public:
	AutofilterParamGenerator(binary_wo::VarObj condition, IBook* pBook, ValuesNode* pRootNode);
	KCriteriaParam1* param1() { return  &m_param1; }
	KCriteriaParam2* param2() { return  &m_param2; }
	ETFilterOperator fop() { return m_fop; }

private:
	CUSTOM_CONDITION_TYPE strToCustomConditionType(PCWSTR str);
	ETFilterOperator strToETFilterOperator(PCWSTR str);
	ET_DYNAMIC_FILTER_CRITERIA strToDynamicFilterCriteria(PCWSTR str);
	ETDateTimeGrouping strToETDateTimeGrouping(PCWSTR str);
	CF_ICONSET strToEtIconSet(PCWSTR str);

	void genCustomCriteria(CUSTOM_CONDITION_TYPE nIndex, PCWSTR val, ks_wstring& data);
	void genCustom(binary_wo::VarObj condition);
	void genValues(binary_wo::VarObj condition);
	void genCellColor(binary_wo::VarObj condition);
	void genFontColor(binary_wo::VarObj condition);
	void genCellIcon(binary_wo::VarObj condition);

private:
	ks_stdptr<ICriteriaTextSet> m_spCriteriaTextSet;
	ks_stdptr<ICriteriaDateList> m_spCriteriaDateList;
	ks_stdptr<IFilterCellColorItems> m_spCellColors;
	ks_stdptr<IFilterFontColorItems> m_spFontColors;
	ks_stdptr<IFilterIconItems> m_spCellIcons;
	ks_wstring m_strParam1;
	KCriteriaParam1 m_param1;
	ETFilterOperator m_fop;
	ks_wstring m_strParam2;
	KCriteriaParam2 m_param2;
	IBook* m_pBook;
    IKAutoFilter* m_pAutoFilter;
    int m_nField;
    ValuesNode* m_pRootNode;
};

class RowRangeHelper
{
public:
	struct RowRangeItem
	{

		RowRangeItem(IDX idxSheetTmp, ROW rowFromTmp, ROW rowToTmp)
		{
			idxSheet = idxSheetTmp;
			rowFrom = rowFromTmp;
			rowTo = rowToTmp;
		}

		RowRangeItem(const RowRangeItem& other)
		{
			idxSheet = other.idxSheet;
			rowFrom = other.rowFrom;
			rowTo = other.rowTo;
		}

		IDX idxSheet;
		ROW rowFrom;
		ROW rowTo;
	};

	struct CmpRowFrom
	{
		bool operator()(const RowRangeItem& first, const RowRangeItem& second)
		{
			return first.rowFrom < second.rowFrom;
		}
	};

	struct CmpRowTo
	{
		bool operator()(const RowRangeItem& first, const RowRangeItem& second)
		{
			return first.rowTo < second.rowTo;
		}
	};

	typedef std::vector<RowRangeItem> RowRanges;
	typedef RowRanges::iterator RowRangeIter;
	typedef std::pair<IDX, RowRanges> RowRangesPair;
	typedef std::map<IDX, RowRanges> RowRangesMap;

public:
	RowRangeHelper(IBook* pBook, binary_wo::VarObj obj);
	~RowRangeHelper();

	void Add(const RANGE& rg);
private:
	bool isFirst(RowRanges& rowRgs, RowRangeIter& rgRowIter);

	RowRangeHelper::RowRangeIter getPre(RowRanges& rowRgs, RowRangeIter& rgRowIter);

	void add4HasPre(const RANGE& rg, RowRangeIter& rgRowFromIter, RowRangeIter& rgRowToIter, RowRanges& rowRgs);

	void add4First(const RANGE& rg, RowRangeIter& rgRowFromIter, RowRangeIter& rgRowToIter, RowRanges& rowRgs);

	void add4RowToIsEnd(const RANGE& rg, RowRangeIter& rgRowFromIter, RowRangeIter& rgRowToIter, RowRanges& rowRgs);

	void serialize();

public:
	static HRESULT collectFilterResult(binary_wo::VarObj& param, binary_wo::VarObj& target, ISheet* pSht, IFilterRowContainter* pFilterRowCol,
		const RowRangeItem& rg);
	static BOOL checkWithCoreFilter(ISheet*pSht,const RowRangeItem& rgItem,PCWSTR currentId,PCWSTR coreUserId);
private:
	std::map<IDX, RowRanges> m_mapRowRanges;
	IBook* m_pBook;
	binary_wo::VarObj m_obj;
};

}

#endif //__AUTO_FILTER_PARAM_HELPER_H__
