﻿#include "etstdafx.h"
#include "db_append_data_adapter.h"
#include "dbsheet/et_dbsheet_copysheet_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/table_struct_rec_helper.h"
#include "utils/et_gridsheet_utils.h"
#include "db/db_basic_itf.h"

namespace wo
{

namespace
{

bool isDbSourceBlankRow(IDBSheetOp* pDBSheetOp, EtDbIdx rowIdx, const std::vector<EtDbId>& fldIds)
{
	const IDBIds* pRecIds = pDBSheetOp->GetAllRecords();
	EtDbId recId = pRecIds->IdAt(rowIdx);
	for (EtDbId fldId : fldIds)
	{
		const_token_ptr pToken = nullptr;
		VS(pDBSheetOp->GetValueToken(recId, fldId, &pToken));
		if (pToken)
			return false;
	}
	return true;
}

bool checkTypeConsistency(ET_DbSheet_FieldType srcType, ET_DbSheet_FieldType tarType)
{
	switch (tarType)
	{
		case Et_DbSheetField_Attachment:
		case Et_DbSheetField_Contact:
		case Et_DbSheetField_Note:
			return srcType == tarType;
		default:
			break;
	}
	return true;
}

void preprocessField(IDbField* pSrcField, IDbField* pTarField, IDBProtectionJudgement* pProtectionJudgement, HRESULT& hr)
{
	ET_DbSheet_FieldType srcType = pSrcField->GetType();
	// 关联转为其它类型时，可能导致相关引用列丢失;不做转换操作，直接取文本做为输入
	if (srcType == Et_DbSheetField_Link || srcType == Et_DbSheetField_OneWayLink)
	{
		hr = S_OK;
		return;
	}
	ET_DbSheet_FieldType tarType = pTarField->GetType();
	switch (tarType)
	{
		case Et_DbSheetField_Cascade:
			// 统一取文本来设值
			if (srcType != Et_DbSheetField_MultipleSelect)
			{
				hr = S_OK;
				return;
			}
			// 多选项字段转级联只处理第一个选项；故先统一转为单选项字段
			tarType = Et_DbSheetField_SingleSelect;
			break;
		case Et_DbSheetField_MultipleSelect:
		case Et_DbSheetField_SingleSelect:
			if (srcType != Et_DbSheetField_SingleSelect && srcType != Et_DbSheetField_MultipleSelect)
			{
				// 目标为单/多选项字段时
				// 	1. 非选项->选项，做转换处理，逻辑较为复杂
				// 	2. 选项->选项，需要重置对应item内容，逻辑也比较复杂；仅进行单选和多选的类型转换即可
				// 后续直接取对应输入的字符串来设值
				hr = S_OK;
				return;
			}
			break;
		case Et_DbSheetField_CellPicture:
		case Et_DbSheetField_Link:
		case Et_DbSheetField_OneWayLink:
			hr = S_FALSE;
			return;
		default:
			if (pTarField->IsAuto())
			{
				hr = S_FALSE;
				return;
			}
			break;
	}
	DbSheet::DisableDbProtectScope dbProtectScope(pProtectionJudgement);
	DbSheet::DisableDbUpdateLastModifiedInfoScope lastModifiedInfoScope;
	DbSheet::DbFieldUpdateScope updateScope(pSrcField, pSrcField->GetDbSheetData()->GetBook(), hr);
	if (FAILED(hr))
		return;
	hr = pSrcField->SetType(tarType, nullptr);
	if (FAILED(hr))
		return;
	// 只需要转换后拷贝影响数据的属性
	switch (tarType)
	{
		case Et_DbSheetField_Rating:
		{
			ks_stdptr<IDbField_Rating> spSrcFieldRating = pSrcField;
			ks_stdptr<IDbField_Rating> spTarFieldRating = pTarField;
			hr = spSrcFieldRating->SetMaxRating(spTarFieldRating->GetMaxRating());
			if (FAILED(hr))
				return;
			break;
		}
		case Et_DbSheetField_Url:
		{
			ks_stdptr<IDbField_Hyperlink> spSrcFieldHyperlink = pSrcField;
			ks_stdptr<IDbField_Hyperlink> spTarFieldHyperlink = pTarField;
			hr = spSrcFieldHyperlink->SetDisplayText(spTarFieldHyperlink->GetDisplayText());
			if (FAILED(hr))
				return;
			break;
		}
		case Et_DbSheetField_Contact:
		{
			ks_stdptr<IDbField_Contact> spSrcFieldContact = pSrcField;
			ks_stdptr<IDbField_Contact> spTarFieldContact = pTarField;
			hr = spSrcFieldContact->SetSupportMulti(spTarFieldContact->GetSupportMulti());
			if (FAILED(hr))
				return;
			break;
		}
		case Et_DbSheetField_Address:
		{
			ks_stdptr<IDbField_Cascade> spSrcFieldAddress = pSrcField;
			ks_stdptr<IDbField_Cascade> spTarFieldAddress = pTarField;
			hr = spSrcFieldAddress->SetCascadeLevel(spTarFieldAddress->GetCascadeLevel());
			if (FAILED(hr))
				return;
			hr = spSrcFieldAddress->SetWithDetailedInfo(spTarFieldAddress->GetWithDetailedInfo());
			if (FAILED(hr))
				return;
			break;
		}
		case Et_DbSheetField_Department:
		{
			ks_stdptr<IDbField_Cascade> spSrcFieldDeaprtment = pSrcField;
			ks_stdptr<IDbField_Cascade> spTarFieldDeaprtment = pTarField;
			hr = spSrcFieldDeaprtment->SetMultiValue(spTarFieldDeaprtment->GetMultiValue());
			if (FAILED(hr))
				return;
			break;
		}
		default:
			break;
	}
}

} // namespace

DbAppendDataAdapter::DbAppendDataAdapter(IKWorkbook* pSrcWorkbook, IKWorkbook* pTarWorkbook, IDBProtectionJudgement* pProtectionJudgement)
	: m_pSrcWorkbook(pSrcWorkbook)
	, m_pTarWorkbook(pTarWorkbook)
	, m_pProtectionJudgement(pProtectionJudgement)
{
	VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
}

HRESULT DbAppendDataAdapter::Init(const DbAppendDataParam& param)
{
	if (param.srcStIdx == INVALIDIDX || param.tarStIdx == INVALIDIDX)
		return E_INVALIDARG;
	if (!m_pSrcWorkbook || !m_pTarWorkbook)
		return E_FAIL;
	m_pSrcWorksheet = m_pSrcWorkbook->GetWorksheets()->GetSheetItem(param.srcStIdx);
	m_pTarWorksheet = m_pTarWorkbook->GetWorksheets()->GetSheetItem(param.tarStIdx);
	if (!m_pSrcWorksheet || !m_pTarWorksheet)
		return E_FAIL;
	m_pSrcSheet = m_pSrcWorksheet->GetSheet();
	m_pTarSheet = m_pTarWorksheet->GetSheet();
	SHEETSTATE state = ssVisible;
	m_pSrcSheet->GetVisible(&state);
	if (state == ssVeryhidden)
		return E_FAIL;
	if (!m_pSrcSheet->IsGridSheet() && !m_pSrcSheet->IsOldDbSheet() && !m_pSrcSheet->IsDbSheet())
		return E_FAIL;
	if (!m_pTarSheet->IsDbSheet())
		return E_FAIL;
	HRESULT hr = DbSheet::GetDBSheetOp(m_pTarSheet, &m_spTarDBSheetOp);
	if (FAILED(hr))
		return hr;
	if (m_spTarDBSheetOp->IsSyncSheet())
		return E_INVALIDARG;
	m_spStringTools->SetEnv(m_pSrcSheet);
	m_param = param;
	return S_OK;
}

HRESULT DbAppendDataAdapter::getSourceCellText(ROW row, COL col, BSTR* pBstrVal) const
{
	return GridSheet::GetCellText(m_spStringTools, m_pSrcWorksheet, row, col, GridSheet::Fill, pBstrVal);
}

bool DbAppendDataAdapter::isEtSourceBlankRow(ROW row, const std::vector<COL>& cols) const
{
	constexpr std::array<WCHAR, 12> gcWcsTrimBlank = {
		0x20,	// SPACE
		__Xc('\r'),
		__Xc('\n'),
		__Xc('\t'),
		0xA0,	// NO-BREAK SPACE
		0x2002,	// EN SPACE
		0x2003,	// EM SPACE
		0x2004,	// THREE-PER-EM SPACE
		0x2005,	// FOUR-PER-EM SPACE
		0x2006,	// SIX-PER-EM SPACE
		0x3000,	// IDEOGRAPHIC SPACE
		0		// 字符串结束
	};
	for (COL col : cols)
	{
		ks_bstr bstrVal;
		getSourceCellText(row, col, &bstrVal);
		if (bstrVal.empty())
			continue;
		ks_wstring valStr(bstrVal);
		valStr.Trim(gcWcsTrimBlank.data());
		if (valStr.empty())
			continue;
		return false;
	}
	return true;
}

bool DbAppendDataAdapter::IsSupportField(IDbField* pField, bool isDbSheet)
{
	if (!pField)
		return false;
	switch (pField->GetType())
	{
		case Et_DbSheetField_Address:
		case Et_DbSheetField_Attachment:
		case Et_DbSheetField_Contact:
		case Et_DbSheetField_Note:
			return isDbSheet;
		case Et_DbSheetField_CellPicture:
		case Et_DbSheetField_Link:
		case Et_DbSheetField_OneWayLink:
			return false;
		default:
			break;
	}
	return !pField->IsAuto();
}

std::unordered_map<GlobalSharedString, EtDbId, GlobalSharedStringHasher> DbAppendDataAdapter::getTarFieldMap() const
{
	const IDBIds* pFields = m_spTarDBSheetOp->GetAllFields();
	IDbFieldsManager* pFieldsMgr = m_spTarDBSheetOp->GetFieldsManager();
	UINT sheetStId = m_spTarDBSheetOp->GetSheetId();
	std::unordered_map<GlobalSharedString, EtDbId, GlobalSharedStringHasher> tarFieldMap;
	for (EtDbIdx col = 0, colCnt = pFields->Count(); col < colCnt; ++col)
	{
		EtDbId fldId = pFields->IdAt(col);
		ks_stdptr<IDbField> spField;
		pFieldsMgr->GetField(fldId, &spField);
		if (!spField)
			continue;
		if (FAILED(m_pProtectionJudgement->CheckFieldCanSetValue(sheetStId, fldId)))
			continue;
		tarFieldMap.emplace(GlobalSharedString(spField->GetName()), fldId);
	}
	return tarFieldMap;
}

HRESULT DbAppendDataAdapter::execForGridSheet()
{
	RANGE rg(m_pSrcSheet->GetBMP());
	VS(m_pSrcWorksheet->GetUsedRange(&rg));
	COL contentRgLeftBorder = -1, contentRgRightBorder = -1;
	int titleCnt = 0;
	auto errCode = TableStructRecHelper::calcTitleCnt(m_pSrcSheet, rg, false, contentRgLeftBorder, contentRgRightBorder, titleCnt);
	if (errCode == TableStructRecHelper::TableStructRecSuccessed)
		rg.SetRowFrom(titleCnt - 1);
	if (!rg.IsValid())
		return E_FAIL;
	ROW rowBegin = rg.RowFrom();
	ROW rowEnd = rg.RowTo();
	COL colBegin = rg.ColFrom();
	COL colEnd = rg.ColTo();
	std::unordered_map<GlobalSharedString, COL, GlobalSharedStringHasher> fldNameMap;
	for (int col = colBegin; col <= colEnd; ++col)
	{
		ks_bstr bstrVal;
		getSourceCellText(rowBegin, col, &bstrVal);
		if (bstrVal.empty())
			continue;
		GlobalSharedString str(bstrVal.c_str());
		if (fldNameMap.find(str) != fldNameMap.end())
			continue;
		fldNameMap.emplace(str, col);
	}
	auto tarFldMap = getTarFieldMap();
	std::vector<COL> srcCols;
	std::vector<EtDbId> tarFldIds;
	auto itSrcFldMapEnd = fldNameMap.end();
	for (const auto& it : tarFldMap)
	{
		auto itSrcFld = fldNameMap.find(it.first);
		if (itSrcFld != itSrcFldMapEnd)
		{
			srcCols.emplace_back(itSrcFld->second);
			tarFldIds.emplace_back(it.second);
		}
	}
	if (srcCols.empty())
		return E_DBSHEET_FIELD_MISMATCH;
	// 去除下结尾的空行，避免可能导入太多空行；仅含空格的视为空单元格
	for (; rowEnd > rowBegin; --rowEnd)
		if (!isEtSourceBlankRow(rowEnd, srcCols))
			break;
	UINT newRecordCnt = rowEnd - rowBegin;
	UINT leftRecordCnt = m_pTarSheet->GetBMP()->cntRows - m_spTarDBSheetOp->GetAllRecords()->Count();
	if (newRecordCnt > leftRecordCnt)
	{
		newRecordCnt = leftRecordCnt;
		m_bReachRowLimit = true;
	}
	if (newRecordCnt == 0)
		return S_OK;
	HRESULT hr = S_OK;
	ks_stdptr<IUnknown> spUnk;
	hr = m_pSrcSheet->GetExtDataItem(edSheetHyperlinks, &spUnk);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IKHyperlinks> spHyperlinks = spUnk;
	ks_stdptr<IDBSheetRange> spDbRange;
	m_spTarDBSheetOp->InsertRecords(newRecordCnt, &spDbRange);
	if (FAILED(hr))
		return hr;
	m_newRecordIds.reserve(newRecordCnt);
	for (EtDbIdx idx = 0; idx < newRecordCnt; ++idx)
		m_newRecordIds.emplace_back(spDbRange->GetRecordId(idx));
	DbSheet::DisableDbRecordProtectScope disableDbRecordProtectScope(m_pProtectionJudgement, m_newRecordIds.data(), newRecordCnt);
	ICellImages* pCellImages = m_pSrcWorksheet->GetWorkbook()->GetCellImages();
	IDbFieldsManager* pFieldsMgr = m_spTarDBSheetOp->GetFieldsManager();
	for (size_t i = 0, colCnt = srcCols.size(); i < colCnt; ++i)
	{
		COL srcCol = srcCols[i];
		EtDbId fldId = tarFldIds[i];
		ks_stdptr<IDbField> spField;
		pFieldsMgr->GetField(fldId, &spField);
		ET_DbSheet_FieldType tarFldType = spField->GetType();
		if (!IsSupportField(spField, false))
			continue;
		if (tarFldType == Et_DbSheetField_BarCode)
		{
			ks_stdptr<IDbField_BarCode> spTarFieldBarCode = spField;
			if (spTarFieldBarCode->GetOnlyScanByCamera())
				continue;
		}
		ET_DbSheet_FieldType fldType = spField->GetType();
		if (!checkTypeConsistency(Et_DbSheetField_MultiLineText, fldType))
			continue;
		bool needSetUrl = fldType == Et_DbSheetField_Email;
		GlobalSharedString fieldName(spField->GetName());
		auto itNameMap = fldNameMap.find(fieldName);
		if (itNameMap == fldNameMap.end())
			continue;
		KDbFieldValCacheGuard guard(fldId, pFieldsMgr);
		for (int row = 0; row < newRecordCnt; ++row)
		{
			ROW srcRow = row + rowBegin + 1;
			if (pCellImages->IsImgCell(m_param.srcStIdx, srcRow, srcCol))
				continue;
			ks_bstr bstrVal;
			getSourceCellText(srcRow, srcCol, &bstrVal);
			if (bstrVal.empty())
				continue;
			// 允许失败
			PCWSTR val = bstrVal.c_str();
			if (needSetUrl)
			{
				ks_stdptr<IKHyperlink> spHyperlink;
				spHyperlinks->HasHyperLink(srcRow, srcCol, &spHyperlink);
				ks_bstr address;
				if (spHyperlink)
					spHyperlink->GetAddress(&address);
				hr = m_spTarDBSheetOp->SetHyperlinkAddress(m_newRecordIds[row], fldId, address.empty() ? val : address.c_str());
				if (FAILED(hr))
					continue;
			}
			else if (fldType == Et_DbSheetField_Url)
			{
				ks_stdptr<IKHyperlink> spHyperlink;
				spHyperlinks->HasHyperLink(srcRow, srcCol, &spHyperlink);
				ks_bstr address;
				if (spHyperlink)
					spHyperlink->GetAddress(&address);
				alg::managed_token_assist mta;
				IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
				if (FAILED(pCtx->Text2DbHyperlinkToken(val, address.empty() ? val : address.c_str(), &mta)))
					continue;
				m_spTarDBSheetOp->SetTokenValue(m_newRecordIds[row], fldId, mta);
				continue;
			}
			hr = m_spTarDBSheetOp->SetValue(m_newRecordIds[row], fldId, val);
			if (FAILED(hr) && needSetUrl)
				m_spTarDBSheetOp->SetHyperlinkAddress(m_newRecordIds[row], fldId, nullptr);
		}
	}
	return S_OK;
}

HRESULT DbAppendDataAdapter::execForDbSheet()
{
	if (!m_param.pAttachmentIdMap)
		return E_INVALIDARG;
	IBook* pSrcBook = m_pSrcSheet->LeakBook();
	ks_stdptr<IDBSheetOp> spSrcDBSheetOp;
	HRESULT hr = DbSheet::copyDbUsersManager(pSrcBook, m_pTarSheet->LeakBook());
	if (FAILED(hr))
		return hr;
	hr = DbSheet::GetDBSheetOp(m_pSrcSheet, &spSrcDBSheetOp);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IUnknown> spUnknown;
	VS(pSrcBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	if (!spDBUserGroups)
		return E_FAIL;
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if (!spProtectionJudgement)
		return E_FAIL;
	const IDBIds* pSrcRecords = spSrcDBSheetOp->GetAllRecords();
	const IDBIds* pSrcFields = spSrcDBSheetOp->GetAllFields();
	IDbFieldsManager* pSrcFieldsMgr = spSrcDBSheetOp->GetFieldsManager();
	IDbFieldsManager* pTarFieldsMgr = m_spTarDBSheetOp->GetFieldsManager();
	std::unordered_map<GlobalSharedString, EtDbId, GlobalSharedStringHasher> fldNameMap;
	for (EtDbIdx col = 0, colCnt = pSrcFields->Count(); col < colCnt; ++col)
	{
		EtDbId fldId = pSrcFields->IdAt(col);
		ks_stdptr<IDbField> spField;
		pSrcFieldsMgr->GetField(fldId, &spField);
		if (!spField)
			continue;
		GlobalSharedString str(spField->GetName());
		if (fldNameMap.find(str) != fldNameMap.end())
			continue;
		fldNameMap.emplace(str, fldId);
	}
	auto tarFldMap = getTarFieldMap();
	std::vector<EtDbId> srcFldIds;
	std::vector<EtDbId> tarFldIds;
	auto itSrcFldMapEnd = fldNameMap.end();
	for (const auto& it : tarFldMap)
	{
		auto itSrcFld = fldNameMap.find(it.first);
		if (itSrcFld != itSrcFldMapEnd)
		{
			srcFldIds.emplace_back(itSrcFld->second);
			tarFldIds.emplace_back(it.second);
		}
	}
	if (srcFldIds.empty())
		return E_DBSHEET_FIELD_MISMATCH;
	UINT newRecordCnt = pSrcRecords->Count();
	// 去除下结尾的空行，避免可能导入太多空行；仅含空格的视为空单元格
	for (; newRecordCnt > 0; --newRecordCnt)
		if (!isDbSourceBlankRow(spSrcDBSheetOp, newRecordCnt - 1, srcFldIds))
			break;
	UINT leftRecordCnt = m_pTarSheet->GetBMP()->cntRows - m_spTarDBSheetOp->GetAllRecords()->Count();
	if (newRecordCnt > leftRecordCnt)
	{
		newRecordCnt = leftRecordCnt;
		m_bReachRowLimit = true;
	}
	if (newRecordCnt == 0)
		return S_OK;
	ks_stdptr<IDBSheetRange> spDbRange;
	hr = m_spTarDBSheetOp->InsertRecords(newRecordCnt, &spDbRange);
	if (FAILED(hr))
		return hr;
	m_newRecordIds.reserve(newRecordCnt);
	for (EtDbIdx idx = 0; idx < newRecordCnt; ++idx)
		m_newRecordIds.emplace_back(spDbRange->GetRecordId(idx));
	DbSheet::DisableDbRecordProtectScope disableDbRecordProtectScope(m_pProtectionJudgement, m_newRecordIds.data(), newRecordCnt);
	for (size_t i = 0, colCnt = srcFldIds.size(); i < colCnt; ++i)
	{
		EtDbId srcFldId = srcFldIds[i];
		EtDbId tarFldId = tarFldIds[i];
		ks_stdptr<IDbField> spSrcField;
		pSrcFieldsMgr->GetField(srcFldId, &spSrcField);
		ks_stdptr<IDbField> spTarField;
		pTarFieldsMgr->GetField(tarFldId, &spTarField);
		if (!IsSupportField(spTarField, true))
			continue;
		if (spSrcField->IsSyncLookupField())
			continue;
		preprocessField(spSrcField, spTarField, spProtectionJudgement, hr);
		// 有问题的字段或者不需要处理的字段直接跳过即可
		if (FAILED(hr) || hr == S_FALSE)
			continue;
		ET_DbSheet_FieldType srcFldType = spSrcField->GetType();
		ET_DbSheet_FieldType tarFldType = spTarField->GetType();
		if (tarFldType == Et_DbSheetField_Attachment)
		{
			ks_stdptr<IDbField_Attachment> spTarFieldAttachment = spTarField;
			if (spTarFieldAttachment->GetOnlyUploadByCamera())
				continue;
		}
		if (tarFldType == Et_DbSheetField_BarCode)
		{
			ks_stdptr<IDbField_BarCode> spTarFieldBarCode = spTarField;
			if (spTarFieldBarCode->GetOnlyScanByCamera())
				continue;
		}
		if (!checkTypeConsistency(srcFldType, tarFldType))
			continue;
		bool isSrcLink = srcFldType == Et_DbSheetField_Link || srcFldType == Et_DbSheetField_OneWayLink;
		bool isSelect = tarFldType == Et_DbSheetField_MultipleSelect || tarFldType == Et_DbSheetField_SingleSelect || tarFldType == Et_DbSheetField_Cascade;
		bool isUrl = tarFldType == Et_DbSheetField_Email;
		KDbFieldValCacheGuard guard(tarFldId, pTarFieldsMgr);
		for (int row = 0; row < newRecordCnt; ++row)
		{
			EtDbId srcRecId = pSrcRecords->IdAt(row);
			const_token_ptr pToken = nullptr;
			spSrcDBSheetOp->GetValueToken(srcRecId, srcFldId, &pToken);
			if (!pToken)
				continue;
			// 允许失败
			if (isSelect || isSrcLink)
			{
				ks_bstr valStr;
				spSrcDBSheetOp->GetValueString(srcRecId, srcFldId, &valStr);
				if (valStr.empty())
					continue;
				if (isUrl)
				{
					hr = m_spTarDBSheetOp->SetHyperlinkAddress(m_newRecordIds[row], tarFldId, valStr.c_str());
					if (FAILED(hr))
						continue;
				}
				else if (tarFldType == Et_DbSheetField_Url)
				{
					alg::managed_token_assist mta;
					IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
					if (FAILED(pCtx->Text2DbHyperlinkToken(valStr.c_str(), valStr.c_str(), &mta)))
						continue;
					m_spTarDBSheetOp->SetTokenValue(m_newRecordIds[row], tarFldId, mta);
					continue;
				}
				hr = m_spTarDBSheetOp->SetValue(m_newRecordIds[row], tarFldId, valStr.c_str());
			}
			else
			{
				switch (tarFldType)
				{
					case Et_DbSheetField_Attachment:
					{
						ks_stdptr<IDbTokenArrayHandle> spTokenArray;
						hr = GetNewAttachmentToken(m_param.pAttachmentIdMap, pToken, &spTokenArray);
						if (FAILED(hr))
							break;
						alg::managed_handle_token_assist tokenAssist;
						tokenAssist.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
						hr = m_spTarDBSheetOp->SetTokenValue(m_newRecordIds[row], tarFldId, tokenAssist);
						break;
					}
					case Et_DbSheetField_Note:
					{
						ks_stdptr<IDbNoteHandle> spNoteHandle;
						hr = GetNewNoteToken(m_param.pAttachmentIdMap, pToken, &spNoteHandle);
						if (FAILED(hr))
							break;
						alg::managed_handle_token_assist tokenAssist;
						tokenAssist.create(alg::ET_HANDLE_DBNOTE, spNoteHandle);
						hr = m_spTarDBSheetOp->SetTokenValue(m_newRecordIds[row], tarFldId, tokenAssist);
						break;
					}
					case Et_DbSheetField_Email:
					{
						ks_bstr address;
						hr = spSrcDBSheetOp->GetHyperlinkAddress(srcRecId, srcFldId, &address);
						if (FAILED(hr))
							break;
						hr = m_spTarDBSheetOp->SetHyperlinkAddress(m_newRecordIds[row], tarFldId, address.c_str());
						if (FAILED(hr))
							break;
						hr = m_spTarDBSheetOp->SetTokenValue(m_newRecordIds[row], tarFldId, pToken);
						if (FAILED(hr))
							m_spTarDBSheetOp->SetHyperlinkAddress(m_newRecordIds[row], tarFldId, nullptr);
						break;
					}
					default:
						hr = m_spTarDBSheetOp->SetTokenValue(m_newRecordIds[row], tarFldId, pToken);
						break;
				}
			}
		}
	}
	return S_OK;
}

HRESULT DbAppendDataAdapter::Exec()
{
	m_newRecordIds.clear();
	DbSheet::DisableDbTrackHistoryScope scope;
	app_helper::KBatchUpdateCal buc(m_spTarDBSheetOp->GetBook()->LeakOperator());
	DbSheet::DbtBatchSubmitOptimizer batchSubmitter(m_spTarDBSheetOp.get());

	if (!m_pSrcSheet->IsDbSheet())
		return execForGridSheet();
	return execForDbSheet();
}

} // namespace wo
