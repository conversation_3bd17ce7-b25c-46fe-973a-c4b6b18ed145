﻿#ifndef __WEBET_ET_FORM_TASK_CLASS_H__
#define __WEBET_ET_FORM_TASK_CLASS_H__
#include "et_task_class.h"

namespace wo
{

bool isFormSheet(IBook* pBook, IDX sheetIdx);
bool isFormBook(IBook* pBook);
int  getFormCount(IBook* pBook);

class TaskExecFormHelp
{
public:
	static void GetNameRg(IBookOp* pBookOp, IDX iName, RANGE& rg);
	static HRESULT GetFormRg(IBook* pBook, KEtRevisionContext*, RANGE& rg,const ks_wstring& areaName);
};

class TaskExecFormBase : public EtTaskExecBase
{
public:
	TaskExecFormBase(KEtWorkbook*);
	bool SerialCommand(KwCommand* cmd, ISerialAcceptor* acpt) override;
	static ks_wstring getAreaName(VarObj obj);
protected:
	bool addItemsMimeData(binary_wo::VarObj objVec);
	HRESULT handleFormula(binary_wo::VarObj, const RANGE &, PCWSTR, KEtRevisionContext*);
	HRESULT handleCellPicture(const RANGE &, bool isUrl, WebMimeHelper wmh);
	HRESULT handleItems(WebMimeHelper, RANGE &rg, IDX colFrom, const std::vector<uint32>& colsIndex, bool bItemsWithType, KEtRevisionContext*);
	HRESULT handleHyperlink(const RANGE &, binary_wo::VarObj);
	bool FindModifyRg(PCWSTR strKey, int iUniqueField, RANGE& rg);
	static bool isItemsWithType(binary_wo::VarObj);
	static int getIntS(const binary_wo::VarObj obj, WebName name, int defVal);
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecFormAddRow : public TaskExecFormBase
{
public:
	enum AddRowMode
	{
		InsertMode = 0,        //默认插入
		CoverNullCellMode      //在空单元格写入
	};
public:
	TaskExecFormAddRow(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT GainWriteRg(const binary_wo::VarObj& param, AddRowMode rowMode,int rowsCnt, int fieldsCnt, RANGE& rg, KEtRevisionContext* pCtx);
	HRESULT InitNewRg(IBookOp* pBookOp, IDX iName, int rowCnt, int fieldsCnt, RANGE& rg, KEtRevisionContext* pCtx);
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}

	AddRowMode  strToAddRowMode(WebStr str);
};

class TaskExecFormAddRows : public TaskExecFormAddRow
{
public:
	TaskExecFormAddRows(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
private:
	void updateNameRg(ROW lastRow,const ks_wstring& areaName);
	bool currentRowHasContent(const RANGE& rg, const std::vector<uint32>& colsIndexVec);
    void PreCheckForDuplicate(int scanBackCount, int field, IAppCoreRange** ppCoreRange, FINDPARAM& findParam, RANGE& rg);
private:
	UINT  m_rowCount;
};

class TaskExecFormAddCol : public TaskExecFormBase
{
public:
	TaskExecFormAddCol(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT GainWriteRg(const binary_wo::VarObj& param, RANGE& rg, KEtRevisionContext* pCtx,const ks_wstring& areaName);
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecFormAddSheet : public TaskExecFormBase
{
public:
	TaskExecFormAddSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecFormAddName : public TaskExecFormBase
{
public:
	TaskExecFormAddName(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};


class TaskExecFormEditRow : public TaskExecFormBase
{
public:
	TaskExecFormEditRow(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecFormDeleteRow : public TaskExecFormBase
{
public:
	TaskExecFormDeleteRow(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};


class TaskExecFormDeleteRows : public TaskExecFormBase
{
public:
	TaskExecFormDeleteRows(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};


class TaskExecFileSetAutoFilter: public TaskExecFormBase
{
public:
	TaskExecFileSetAutoFilter(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KRange::CheckAutoFilterState 时判断权限
	}
};

class TaskExecFormSort : public TaskExecFormBase
{
public:
	TaskExecFormSort(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecFormSetColumnWidth: public TaskExecFormBase
{
public:
	TaskExecFormSetColumnWidth(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;  
	}
};

} // wo

#endif // __WEBET_ET_FORM_TASK_CLASS_H__