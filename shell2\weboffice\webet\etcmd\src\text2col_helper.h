﻿#ifndef __WEBET_TEXT2COL_HELPER_H__
#define __WEBET_TEXT2COL_HELPER_H__

namespace wo
{
namespace text2col
{
//���²����������е�ԭ��
//ע�⣬�����ڵ��������µ�ö��ֵ!!!
enum ErrType
{
	noErr = 0,
	overRange = 1,
	arrFmlr = 2,
	hasData = 3,
	invalidParm = 4,
	mergedCell = 5,
	unknown = 6,
};

struct SplitParam
{
	ks_wstring getStrDelimiter();
	ET_TextQualifier TextQualifier;
	bool ConsecutiveDelimiter;
	bool Tab;
	bool Semicolon;
	bool Comma;
	bool Space;
	bool Other;
	WCHAR OtherChar;
};

//��Ҫ��������
//���һ��onlyCheck�������Ƿ�ֻҪ���һ�¾Ϳ����˻��Ǽ�����˾ͺ��Դ���ִ��
HRESULT text2colProc(KEtWorkbook *wwb, binary_wo::VarObj &param, ISerialAcceptor *acpt, const bool onlyCheck, KEtRevisionContext* ctx);
//ref: et_applogic_helper.inl :  RangeTextSplit_CheckUserOp()
//�����������ܷ���ɣ����ܳ��ֵĴ����������ö��
std::set<ErrType> RangeTextSplitCheckRangeValid(ks_stdptr<IKSheet> pSheet, const RANGE *pSrcRg, RANGE *pDestRg, long lRows, long lCols, long lIgnoreCols, bool onlyChk);
//��Ԥ���е���Ϣ
HRESULT getPreview(KEtWorkbook *wwb, const binary_wo::VarObj &param, ISerialAcceptor *acpt);

//��subrange��ʱ��˳�������е����ݷ�ֹǰ�����ݲ�ȫ������1024�У�
HRESULT subRangeGetAllData(KEtWorkbook *wwb, INT32 sheetIdx, INT32 rowFrom, INT32 rowTo, INT32 col, ISerialAcceptor *acpt);
} // namespace text2col
} // namespace wo

#endif
