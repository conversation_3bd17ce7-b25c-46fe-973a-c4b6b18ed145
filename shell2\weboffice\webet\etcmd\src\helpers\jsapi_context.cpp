﻿#include "etstdafx.h"
#include "et_revision_context_impl.h"
#include "jsapi_context.h"
#include "workbook.h"
#include "varobject_helper.h"
#include <api/jsapi/pub/jsapiutility.h>
#include <api/jsapi/ipc/kjsipc.h>
#include <kso/api/smartparam.h>
#include <Coding/api_bundle/api/common/kycorenotify.h>
#include "util.h"
#include "kso/io/clipboard/ksoclipboard.h"
#include "jsapi_helper.h"


namespace wo
{

KJsApiContextScope::KJsApiContextScope(KEtWorkbook* pWb, KEtRevisionContext* pRC, const QString& taskId, const binary_wo::VarObj& param, bool bEnter)
	: m_pWb(pWb)
	, m_curTaskId(taskId)
    , m_bEnter(bEnter)
{
	KS_TRY
	{
        if (m_bEnter)
		    KJsApiContextManager::Instance()->Enter(m_pWb, pRC, m_curTaskId, param);
	}
	KS_CATCH(const ks_exception& /*e*/)
	{
	}
}

KJsApiContextScope::~KJsApiContextScope()
{
	KS_TRY
	{
		KJsApiContextManager::Instance()->Exit(m_pWb, m_curTaskId);
	}
	KS_CATCH(const ks_exception& /*e*/)
	{
	}
}


KJsApiContextManager::KJsApiContextManager()
{
	
}

KJsApiContextManager::~KJsApiContextManager()
{
	for (auto iter = m_contexts.begin(); iter != m_contexts.end();)
	{
		delete iter->second;
		iter = m_contexts.erase(iter);
	}
}

KJsApiContextManager* KJsApiContextManager::Instance()
{
	static KJsApiContextManager s_mgr;
	return &s_mgr;
}

bool KJsApiContextManager::GetRange(const binary_wo::VarObj& param, const char* name, _Worksheet* pSheet, etoldapi::Range** ppRange)
{
	if (!param.has(name))
		return false;
	return GetRange(param.field_str(name), pSheet, ppRange);
}

bool KJsApiContextManager::GetRange(WebStr name, _Worksheet* pSheet, etoldapi::Range** ppRange)
{
	KJSVariant cell1(name);
	KJSVariant cell2;
	if (FAILED(pSheet->get_Range(cell1, cell2, ppRange)))
		return false;
	return true;
}

bool KJsApiContextManager::GetCell(const binary_wo::VarObj& param, const char* name, _Worksheet* pSheet, CELL& cell)
{
	if (!param.has(name))
		return false;
	return GetCell(param.field_str(name), pSheet, cell);
}

bool KJsApiContextManager::GetCell(WebStr name, _Worksheet* pSheet, CELL& cell)
{
	ks_stdptr<etoldapi::Range> rg;
	if (!GetRange(name, pSheet, &rg))
		return false;

	long row = INVALIDIDX, col = INVALIDIDX;
	if (FAILED(rg->get_Row(&row)))
		return false;
	if (FAILED(rg->get_Column(&col)))
		return false;
	cell.row = row - 1;
	cell.col = col - 1;
	return true;
}

void KJsApiContextManager::Enter(KEtWorkbook* pWb, KEtRevisionContext* pRC, const QString& taskId, const binary_wo::VarObj& param)
{
	auto iter = m_contexts.find(taskId);
	if (iter != m_contexts.end())
	{
		JsApiContext* ctx = iter->second;
		SyncTaskContext(pWb, pRC, ctx);
		return;
	}
	
	JsApiContext* ctx = new JsApiContext();
    m_contexts[taskId] = ctx;
	ParseContext(pWb, param, ctx);
	SyncTaskContext(pWb, pRC, ctx);
}

void KJsApiContextManager::Exit(KEtWorkbook* pWb, const QString& taskId)
{
	auto iter = m_contexts.find(taskId);
	if (iter == m_contexts.end())
	{
		WOLOG_ERROR << "[JsApi] JsApi context Exit: id not found";
		return;
	}

	JsApiContext* ctx = iter->second;

	ks_stdptr<etoldapi::Window> spWnd = pWb->GetCoreApp()->GetActiveWindow();
	spWnd->GetSize(&ctx->nWidth, &ctx->nHeight);
	if (ctx->spWindow)
	{
		ctx->spWindow->SetSize(0, 0);
		IKViews* pViews = ctx->spWindow->GetViews();
		if (pViews)
		{
			for (int i = 0, cnt = pViews->GetViewCount(); i < cnt; ++i)
			{
				IKView* pView = pViews->GetViewItem(i);
				if (pView)
					pView->SetSize(0, 0);
			}
		}
		ctx->spWindow.clear();
	}

	ks_stdptr<_Worksheet> spWorksheet;
	ks_stdptr<IKCoreObject> spDispatch;
	pWb->GetCoreApp()->get_ActiveSheet(&spDispatch);
	if (spDispatch)
		spDispatch->QI(_Worksheet, &spWorksheet);
	ISheet* pSheet = spWorksheet->GetSheet();
	ctx->curSheetId = pSheet->GetStId();
	ctx->sheetsMap.clear();

	ks_stdptr<etoldapi::_Workbook> spWorkbook = pWb->GetCoreWorkbook();
	ks_stdptr<IKWorksheets> spWorksheets = spWorkbook->GetWorksheets();
	if (!spWorksheets)
		return;

	int nSheets = spWorksheets->GetSheetCount();
	for (int idx = 0; idx < nSheets; ++idx)
	{
		ks_stdptr<_Worksheet> spWS = spWorksheets->GetSheetItem(idx);
		if (!SupportSheet(spWS))
			continue;
		const UINT stId = spWS->GetSheet()->GetStId();
		std::unique_ptr<JsApiSheetContext> sc(new JsApiSheetContext());

		sc->spSheet = spWS;
		ks_stdptr<IKWorksheetView> spSheetView = spWS->GetActiveWorksheetView();
		ks_stdptr<IKRanges> spRanges;
		spSheetView->GetSelectionRange(&spRanges);
		spWorksheet->GetRangeByData(spRanges, &(sc->spSelectionRange));
		sc->activeCell = spSheetView->GetActiveCell(NULL);

		ks_stdptr<IKCoreObject> spSelection;
		spSheetView->get_Selection(&spSelection);
		ks_stdptr<etoldapi::Range> spRange = spSelection;
		if (!spRange)
		{
			sc->spSelectionObject = spSelection;
		}

		ISheetWndInfo *pSheetWndInfo = spSheetView->GetSheetWndInfo();
		if (pSheetWndInfo)
			pSheetWndInfo->GetLeftTopCell(sc->leftTop);

		ctx->sheetsMap[stId] = std::move(sc);
	}
}

void KJsApiContextManager::Close(const QString& taskId)
{
	auto iter = m_contexts.find(taskId);
	if (iter == m_contexts.end())
	{
		WOLOG_ERROR << "[JsApi] JsApi context Close: id not found";
		return;
	}

    CleanContext(iter->second);
	m_contexts.erase(iter);
	KsoClipboard::clean();
}

void KJsApiContextManager::CloseAll()
{
    for (auto iter = m_contexts.begin(); iter != m_contexts.end();)
    {
        CleanContext(iter->second);
        iter = m_contexts.erase(iter);
    }
}

void KJsApiContextManager::CleanContext(JsApiContext* ctx)
{
	if (ctx->spWindow)
	{
		ctx->spWindow->SetSize(0, 0);
		IKViews* pViews = ctx->spWindow->GetViews();
		if (pViews)
		{
			for (int i = 0, cnt = pViews->GetViewCount(); i < cnt; ++i)
			{
				IKView* pView = pViews->GetViewItem(i);
				if (pView)
					pView->SetSize(0, 0);
			}
		}
		ctx->spWindow.clear();
	}
	delete ctx;
}

bool KJsApiContextManager::ParseContext(KEtWorkbook* pWb, const binary_wo::VarObj& param, JsApiContext *ctx)
{
    if (!param.has("ContextInfo"))
        return false;
	binary_wo::VarObj ctxInfo = param.get_s("ContextInfo");
	std::pair<const byte*, int32> buff = ctxInfo.as_array_buffer_s();
	size_t nHeader = sizeof(JsApiIpcRpcCall);
	const int32 nBlob = buff.second;
	if (nBlob < nHeader)
	{
		WOLOG_ERROR << "[JsApi] JsApi parse context failed";
		//m_pCtx->collectInfo("behaviour_jsapi_error_contextInfo");
		return false;
	}
	const char* pBUffer = reinterpret_cast<const char*>(buff.first);
	const char* pRpcCall = pBUffer;
	const JsApiIpcRpcCall rpcCall = *(JsApiIpcRpcCall*)pBUffer;
	pBUffer += nHeader;
	JsApiIpcArgument* pArgs = (JsApiIpcArgument*)pBUffer;
	KJSVariantList argList;
	std::vector<IJSONValue*> nestJson;
	ArgumentsToValueList(rpcCall.nCount, pArgs, argList, nestJson);
	nestJson.clear();
	ks_stdptr<IJSONValue> spJson;
	if (argList[0].IsInterfaceType())
		spJson = argList[0].GetInterface();
	if (!spJson)
	{
		WOLOG_ERROR << "[JsApi] JsApi parse context: arg0 is not json";
		return false;
	}

	int sheetIdx = -1;
	KJSVariant varId;
	spJson->GetValue("active_sheetId", &varId);
	if (varId.IsIntergertype())
	{
		ctx->curSheetId = varId.GetInt();
	}
	else
	{
		KJSVariant varIdx;
		spJson->GetValue("active_sheet", &varIdx);
		if (varId.IsIntergertype())
			sheetIdx = varIdx.GetInt();
	}
	KJSVariant varWidth;
	spJson->GetValue("width", &varWidth);
	ctx->nWidth = varWidth.GetInt();
	KJSVariant varHeight;
	spJson->GetValue("height", &varHeight);
	ctx->nHeight = varHeight.GetInt();

	KJSVariant varSheets;
	spJson->GetValue("sheets", &varSheets);
	if (!varSheets.VarIsArrayType(varSheets))
		return false;
	SAFEARRAY* pArray = V_ARRAY(&varSheets);
	if (pArray->cDims != 1)
	{
		WOLOG_ERROR << "[JsApi] JsApi parse context: dims != 1";
		return false;
	}
	ks_stdptr<etoldapi::_Workbook> spWorkbook = pWb->GetCoreWorkbook();
	IKWorksheets* pWorksheets = spWorkbook->GetWorksheets();
	IBook* pBook = spWorkbook->GetBook();
	SIZE32 aryIndex = 0;
	SAFEARRAYBOUND* rgsabound = pArray->rgsabound;
	for (int i = 0, cnt = rgsabound->cElements; i < cnt; ++i)
	{
		aryIndex = rgsabound->lLbound + i;
		VARIANT var = { 0 };
		HRESULT hr = SafeArrayGetElement(pArray, &aryIndex, (void*)&var);
		if (V_VT(&var) != VT_UNKNOWN)
			continue;
		ks_stdptr<IJSONValue> spJsonItem = V_UNKNOWN(&var);
		if (!spJsonItem)
			continue;
		UINT32 stId = spJsonItem->GetInt("id");
		if (i == sheetIdx && ctx->curSheetId == INVALIDIDX)
		{
			ctx->curSheetId = stId;
		}
		IDX sheetIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(stId, &sheetIdx);
		// 回放时sheet已经被删了
		if (sheetIdx == INVALIDIDX)
			continue;
		ks_stdptr<_Worksheet> spWS = pWorksheets->GetSheetItem(sheetIdx);
		if (!SupportSheet(spWS))
			continue;
		std::unique_ptr<JsApiSheetContext> sc(new JsApiSheetContext());
		sc->spSheet = spWS;
		GetRange(krt::utf16(spJsonItem->GetString("sel")), sc->spSheet, &(sc->spSelectionRange));
		GetCell(krt::utf16(spJsonItem->GetString("ac")), sc->spSheet, sc->activeCell);
		GetCell(krt::utf16(spJsonItem->GetString("lt")), sc->spSheet, sc->leftTop);
		ctx->sheetsMap[stId]= std::move(sc);
	}
	return true;
}

void KJsApiContextManager::SyncTaskContext(KEtWorkbook* pWb, KEtRevisionContext* pRC, JsApiContext* ctx)
{
    JsRunScope runScope(pRC);
	auto active = ctx->sheetsMap.end();
	for (auto it = ctx->sheetsMap.begin(); it != ctx->sheetsMap.end(); ++it)
	{
		if (it->first == ctx->curSheetId)
		{
			active = it;
			continue;
		}
		SyncSheetContext(pWb, it->second.get());
	}
	if (active != ctx->sheetsMap.end())
		SyncSheetContext(pWb, active->second.get());

	ks_stdptr<etoldapi::Window> spWnd = pWb->GetCoreApp()->GetActiveWindow();
	if (spWnd)
	{
		ctx->spWindow = spWnd;
		int nWidth = std::min(ctx->nWidth > 0 ? ctx->nWidth : 800, 4000);
		int nHeight = std::min(ctx->nHeight > 0 ? ctx->nHeight : 400, 2000);
		spWnd->SetSize(nWidth, nHeight);
		IKViews* pViews = spWnd->GetViews();
		if (pViews)
		{
			for (int i = 0, cnt = pViews->GetViewCount(); i < cnt; ++i)
			{
				IKView* pView = pViews->GetViewItem(i);
				if (pView)
					pView->SetSize(nWidth, nHeight);
			}
		}
	}
}

bool KJsApiContextManager::SyncSheetContext(KEtWorkbook* pWb, const JsApiSheetContext* sc)
{
	if (!sc->spSheet)
		return false;

	sc->spSheet->Select();
	if (sc->spSelectionRange)
		sc->spSelectionRange->Select();

	ks_stdptr<IKWorksheetView> spSheetView = sc->spSheet->GetActiveWorksheetView();
	ISheetWndInfo *pSheetWndInfo = spSheetView ? spSheetView->GetSheetWndInfo() : nullptr;
	if (pSheetWndInfo)
	{
		pSheetWndInfo->SetActiveCell(sc->activeCell);
		pSheetWndInfo->SetLeftTopCell(sc->leftTop);
	}

	if (sc->spSelectionObject)
		SyncSelectObject(sc->spSelectionObject);
	return true;
}

bool KJsApiContextManager::SyncSelectObject(IKCoreObject* pCoreObject)
{
	ks_stdptr<KsoShapeRange> spShapeRange = pCoreObject;
	const int type = pCoreObject->GetType();
	switch (type)
	{
	case etCoDrawingObjects:
	{
		ks_stdptr<etoldapi::DrawingObjects> spDraws = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spDraws->Select(replace, &ret);
	}
	break;
	case etCoOval:
	{
		ks_stdptr<etoldapi::Oval> spOval = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spOval->Select(replace, &ret);
	}
	break;
	case etCoArc:
	{
		ks_stdptr<etoldapi::Arc> spArc = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spArc->Select(replace, &ret);
	}
	break;
	case etCoRectangle:
	{
		ks_stdptr<etoldapi::Rectangle> spRectangle = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spRectangle->Select(replace, &ret);
	}
	break;
	case etCoDrawing:
	{
		ks_stdptr<etoldapi::Drawing> spDrawing = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spDrawing->Select(replace, &ret);
	}
	break;
	case etCoLine:
	{
		ks_stdptr<etoldapi::Line> spLine = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spLine->Select(replace, &ret);
	}
	break;
	case etCoTextBox:
	{
		ks_stdptr<etoldapi::TextBox> spTextBox = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spTextBox->Select(replace, &ret);
	}
	break;
	case etCoGroupObject:
	{
		ks_stdptr<etoldapi::GroupObject> spGroupObject = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spGroupObject->Select(replace, &ret);
	}
	break;
	//ksoIndependentControl
	//ksoEmbeddedOLEObject
	//ksoOLEControlObject
	//ksoLinkedOLEObject
	case etCoChart:
	{
		ks_stdptr<etoldapi::Chart> spChart = pCoreObject;
		KJSVariant replace;
		spChart->Select(replace, 0);
	}
	break;
	case etCoChartObject:
	{
		ks_stdptr<etoldapi::ChartObject> spChartObject = pCoreObject;
		KJSVariant replace;
		KJSVariant ret;
		spChartObject->Select(replace, &ret);
	}
	break;
	//ksoFormControl
	case etCoPicture:
	{
		ks_stdptr<etoldapi::Picture> spPicture = pCoreObject;
		KJSVariant ret;
		spPicture->Select(ksoTrue, &ret);
	}
	break;
	default:
	{
		WOLOG_ERROR << "[JsApi] selection not supported: " << type;
		return false;
	}
	}
	return true;
}

bool KJsApiContextManager::SupportSheet(_Worksheet* pWS)
{
	if (!pWS)
		return false;
	ISheet* pSheet = pWS->GetSheet();
	if (!pSheet)
		return false;
	SHEETTYPE type;
	pSheet->GetFullType(&type);
	if (stGrid == type || stDialog == type || stChart == type || stMacro == type || stModule == type)
		return true;
	return false;
}

void KJsApiContextManager::SetRunning(const QString& taskId)
{
    auto iter = m_contexts.find(taskId);
	if (iter == m_contexts.end())
        return;

    iter->second->m_bIsRunning = true;
}

bool KJsApiContextManager::IsRunning(const QString& taskId) const
{
    auto iter = m_contexts.find(taskId);
	if (iter == m_contexts.end())
        return false;

    return iter->second->m_bIsRunning;
}

} // namespace wo
