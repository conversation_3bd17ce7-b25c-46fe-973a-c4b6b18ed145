﻿#ifndef __WEBET_DATABASE_SHEET_QUERY_CLASS_H__
#define __WEBET_DATABASE_SHEET_QUERY_CLASS_H__

#include "et_query_class.h"
#include "appcore/et_appcore_dbsheet.h"
#include "et_dbsheet_common_helper.h"

namespace wo
{

class ETDbSheetQueryClassBase : public EtQueryExecBase
{
public:
    ETDbSheetQueryClassBase(wo::KEtWorkbook*, PCWSTR tag);

    virtual HRESULT PreExecute(const VarObj& , KEtRevisionContext*) override;

    IDX GetIdxById(UINT sheetStId);
    void GetDBSheetView(UINT sheetStId, EtDbId viewId, IDBSheetView** ppIDbSheetView, IDBSheetViews** ppIDbSheetViews);
	void GetDBSheetViews(UINT sheetStId, IDBSheetViews** ppIDbSheetViews);
	void GetDBPos(binary_wo::VarObj pos, DbIdPostion& tar);
	HRESULT GetDBRange(IDBSheetView* pView, binary_wo::VarObj vRg, 
		bool extendRecordsViewAllToGridAll, bool extendFieldsViewAllToGridAll, IDBSheetRange** ppRg);
    EtDbId GetEtDbId(const binary_wo::VarObj& obj, WebName name);
	EtDbId GetEtDbId(binary_wo::VarObj obj);

protected:
    IDBSheetCtx* m_pDbCtx;
    DBSheetCommonHelper m_commonHelper;
	ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
};

class QueryExecDbRangeCopy : public ETDbSheetQueryClassBase
{
public:
	QueryExecDbRangeCopy(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryExecDbFilterList : public ETDbSheetQueryClassBase
{
public:
	QueryExecDbFilterList(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;

private:
	WebInt m_version = 0;
};

class QueryExecDbPermissionFilterList : public ETDbSheetQueryClassBase
{
public:
	QueryExecDbPermissionFilterList(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryExecDbChartFilterList : public ETDbSheetQueryClassBase
{
public:
	explicit QueryExecDbChartFilterList(wo::KEtWorkbook*);
	HRESULT Exec(const binary_wo::VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj&, KEtRevisionContext*) override;
private:
	WebInt m_version = 0;
};

class QueryExecDbFieldItems : public ETDbSheetQueryClassBase
{
public:
	QueryExecDbFieldItems(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryDbAllAccessibleFields : public ETDbSheetQueryClassBase
{
public:
	QueryDbAllAccessibleFields(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryExecDbFieldTypeModifyCheck : public ETDbSheetQueryClassBase
{
public:
	QueryExecDbFieldTypeModifyCheck(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryExecDbSearchNext : public ETDbSheetQueryClassBase
{
public:
	QueryExecDbSearchNext(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override;
    DBRowColSearchType GetSearchType(WebStr typeStr);
};

class QueryDbTimerTask : public ETDbSheetQueryClassBase
{
public:
	QueryDbTimerTask(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryDbSheetSummary : public ETDbSheetQueryClassBase
{
public:
	QueryDbSheetSummary(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryDbSheetShareLinksInfo : public ETDbSheetQueryClassBase
{
public:
	QueryDbSheetShareLinksInfo(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
private:
	static bool SerialSharedLinksInfo(ISharedLink* pSharedLink, IBook* pBook, KEtWorkbook* pWorkbook, ISerialAcceptor* acpt, IDBSheetCtx* pDbCtx);
	struct KSharedLinkEnum : ISharedLinkEnum
	{
		KSharedLinkEnum(IBook* pBook, KEtWorkbook* pWorkbook, ISerialAcceptor* acpt, IDBSheetCtx* pDbCtx);
		STDPROC_(BOOL) Do(ISharedLink* pSharedLink) override;
	private:
		ISerialAcceptor* m_acpt;
		IBook* m_pBook;
		KEtWorkbook* m_pWorkbook;
		IDBSheetCtx* m_pDbCtx;
	};
};

class QueryDbPermission : public ETDbSheetQueryClassBase
{
public:
	QueryDbPermission(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryDbSheetViews : public ETDbSheetQueryClassBase
{
public:
	QueryDbSheetViews(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
	bool IsPersonalVisible(IDBSheetView *pView, KEtRevisionContext* ctx);
};

class QueryDbLinkRecords : public ETDbSheetQueryClassBase
{
public:
	QueryDbLinkRecords(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt) override;
	HRESULT SerialiseLinkHandle(const IDbLinkHandle* pLink, ISerialAcceptor *acpt, bool bNeedPrimaryFieldStr);
	HRESULT SerialiseLinkTokenAry(const IDbTokenArrayHandle* pTokenAry, ISerialAcceptor *acpt, bool bNeedPrimaryFieldStr, bool bCutOff);
	static bool IsTokenArrayCutOff();

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryExecDbVirtualViewQueryRecords : public ETDbSheetQueryClassBase
{
public:
	QueryExecDbVirtualViewQueryRecords(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class QueryExecDbVirtualViewFilterValuesList : public ETDbSheetQueryClassBase
{
public:
	QueryExecDbVirtualViewFilterValuesList(wo::KEtWorkbook*);
	virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

} // namespace wo

#endif
