#include "etstdafx.h"
#include "database_field_context.h"
#include "workbook.h"
#include "uilogic/et_uilogic_global.h"
#include "uilogic/et_uilogic_basic_ift.h"
#include "util.h"

namespace wo
{
namespace Database
{
ks_stdptr<IET_NumberFormatter> FieldContext::m_spNumberFormatter;

FieldContext::FieldContext(KEtWorkbook *wwb, IEtRevisionContext *pRevisionContext, VarObj extObj)
    : m_wwb(wwb)
    , m_pRevisionContext(pRevisionContext)
    , m_pApiWb(wwb->GetCoreWorkbook())
    , m_pBook(m_pApiWb->GetBook())
    , m_dvReturnCode(errNone)
    , m_bEnumAny(false)
    , m_bClear(false)
    , m_extObj(extObj)
    , m_outExtObj(new binary_wo::BinVarRoot())
{
    if(!m_spNumberFormatter)
        _applogic_CreateObject(CLSID_KNumberFormatter, IID_IET_NumberFormatter, (void**)&m_spNumberFormatter);
}

FieldContext::FieldContext(KEtWorkbook *wwb, IEtRevisionContext *pRevisionContext)
    : FieldContext(wwb, pRevisionContext, VarObj())
{}

FieldContext::FieldContext(etoldapi::_Workbook* pApiWb, IEtRevisionContext *pRevisionContext)
    : m_wwb(nullptr)
    , m_pRevisionContext(pRevisionContext)
    , m_pApiWb(pApiWb)
    , m_pBook(m_pApiWb->GetBook())
    , m_dvReturnCode(errNone)
    , m_bEnumAny(false)
    , m_bClear(false)
    , m_extObj(VarObj())
    , m_outExtObj(new binary_wo::BinVarRoot())
{
    if(!m_spNumberFormatter)
        _applogic_CreateObject(CLSID_KNumberFormatter, IID_IET_NumberFormatter, (void**)&m_spNumberFormatter);
}

ks_stdptr<etoldapi::Range> FieldContext::CreateRangeObj(const RANGE& rg)
{
    if (m_wwb)
        return m_wwb->CreateRangeObj(rg); // 尽量兼容老逻辑
    else
        return util::CreateRangeObj(m_pApiWb, rg);
}

IEtRevisionContext *FieldContext::GetRevisionContext()
{
    ASSERT(m_pRevisionContext != nullptr);
    return m_pRevisionContext;
}

BMP_PTR FieldContext::GetBMP()
{
    return m_pBook->GetBMP();
}

IBook *FieldContext::GetBook()
{
    return m_pBook;
}

IKWorksheets *FieldContext::GetWorksheets()
{
    return m_pApiWb->GetWorksheets();
}

IET_NumberFormatter *FieldContext::GetNumberFormatter()
{
    return m_spNumberFormatter;
}

VarObj FieldContext::GetExtObj()
{
    return m_extObj;
}

VarObj FieldContext::GetOutExtObj() const
{
    return m_outExtObj.cast();
}

SetDVReturnCode FieldContext::GetDVCode()
{
    return m_dvReturnCode;
}

void FieldContext::SetDVCode(SetDVReturnCode code)
{
    m_dvReturnCode = code;
}

bool FieldContext::IsEnumAny()
{
    return m_bEnumAny;
}

void FieldContext::SetEnumAny()
{
    m_bEnumAny = true;
}

void FieldContext::SetClear(bool bClear)
{
    m_bClear = bClear;
}

bool FieldContext::IsClear()
{
    return m_bClear;
}

} // Database
} // wo
