﻿#ifndef __RECT_TF_H__
#define __RECT_TF_H__

namespace wo
{
class RectTrans
{
public:
	enum Param 
	{
		ts_none = 0x0000,
		ts_move = 0x0001,
		ts_expand = 0x0002,
		ts_abridge = 0x0004,
		ts_split = 0x0008,
		ts_exDf = 0x0010,	//切割方向不能和移动方向垂直
		ts_rowCol = 0x0020, //拆分时允行把整行整列拆成非整行整列

		ts_move_split = ts_move | ts_split,
		ts_move_abridge = ts_move | ts_abridge,
		ts_move_abridge_split = ts_move | ts_abridge | ts_split,
		ts_move_abridge_split_rowCol = ts_move | ts_abridge | ts_split | ts_rowCol,
		ts_move_split_rowCol = ts_move | ts_split | ts_rowCol,
		ts_move_expand_abridge = ts_move | ts_expand | ts_abridge,
		ts_move_split_exDf = ts_move | ts_split | ts_exDf,
		ts_move_split_rowCol_exDf = ts_move | ts_split | ts_rowCol | ts_exDf,
	};

	RectTrans(Param param, BMP_PTR bmp)
		: m_param(param)
		, m_bmp(bmp)
	{

	}

	void DeleteUp(const RECT& rcDel, const RECT& rcTrs, std::vector<RECT>& vecRc);
	void DeleteLeft(const RECT& rcDel, const RECT& rcTrs, std::vector<RECT>& vecRc);
	void InsertDown(const RECT& rcIst, const RECT& rcTrs, std::vector<RECT>& vecRc);
	void InsertRight(const RECT& rcIst, const RECT& rcTrs, std::vector<RECT>& vecRc);
	void SwapVert(const RECT& tRc, const RECT& bRc, const RECT& op, std::vector<RECT>& vecRc);
	void SwapHorz(const RECT& lRc, const RECT& rRc, const RECT& op, std::vector<RECT>& vecRc);

private:

	// 与平移方向相同的为 smFrom smTo 与移动方向不同的为 dfFrom dfTo
	class Rect
	{
	public:
		int smFrom;
		int smTo;
		int dfFrom;
		int dfTo;

		inline void fromVert(const RECT& rc)
		{
			smFrom = rc.top;
			smTo = rc.bottom;
			dfFrom = rc.left;
			dfTo = rc.right;
		}

		inline void toVert(RECT& rc) const
		{
			rc.top = smFrom;
			rc.bottom = smTo;
			rc.left = dfFrom;
			rc.right = dfTo;
		}

		inline void fromHori(const RECT& rc)
		{
			dfFrom = rc.top;
			dfTo = rc.bottom;
			smFrom = rc.left;
			smTo = rc.right;
		}

		inline void toHori(RECT& rc) const
		{
			rc.top = dfFrom;
			rc.bottom = dfTo;
			rc.left = smFrom;
			rc.right = smTo;
		}

		inline bool isContain(const Rect& rc) const
		{
			return smFrom <= rc.smFrom && smTo >= rc.smTo &&
				dfFrom <= rc.dfFrom && dfTo >= rc.dfTo;
		}

		inline bool isSmContain(const Rect& rc) const
		{
			return smFrom <= rc.smFrom && smTo >= rc.smTo;
		}

		inline bool isDfContain(const Rect& rc) const
		{
			return dfFrom <= rc.dfFrom && dfTo >= rc.dfTo;
		}

		inline bool isCross(const Rect& rc) const
		{
			return smFrom <= rc.smTo && smTo >= rc.smFrom &&
				dfFrom <= rc.dfTo && dfTo >= rc.dfFrom;
		}

		inline bool isSmCross(const Rect& rc) const
		{
			return smFrom <= rc.smTo && smTo >= rc.smFrom; 
		}

		inline bool isDfCross(const Rect& rc) const
		{
			return dfFrom <= rc.dfTo && dfTo >= rc.dfFrom;
		}

		inline int smSize() const
		{
			return smTo - smFrom + 1;
		}

		inline int dfSize() const
		{
			return dfTo - dfFrom + 1;
		}
	};

	void VertMod();
	void HoriMod();
	void getVertRes(const Rect& res, std::vector<RECT>& vecRes);
	void getHoriRes(const Rect& res, std::vector<RECT>& vecRes);

	bool MoveByDel(const Rect& dRc, Rect& op);
	bool Abridge(const Rect& dRc, Rect& op);
	void SplitByDel(const Rect& dRc, Rect& op);
	void Delete(const Rect& dRc, Rect& op);

	bool MoveByInsert(const Rect& dRc, Rect& op);
	bool Expand(const Rect& iRc, Rect& op);
	bool SplitByInsertSimple(const Rect& dRc, Rect& op);
	void SplitByInsert(const Rect& dRc, Rect& op);
	void Insert(const Rect& dRc, Rect& op);
	bool isValidRect(const RECT& rc);

	void Swap(const Rect& fstRc, const Rect& scdRc, Rect& op);


	int m_maxSm;
	int m_maxDf;
	BMP_PTR m_bmp;
	Param m_param;
	std::vector<Rect> m_res; // 有序，df 从大到小，sm从大到小排序
};
}

#endif // __RECT_TF_H__
