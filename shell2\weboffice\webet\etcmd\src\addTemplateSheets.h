#ifndef __WEBET_ADD_TEMPLATE_SHEETS_H__
#define __WEBET_ADD_TEMPLATE_SHEETS_H__

#include "wo/et_shared_str.h"
#include "ksheet/appsheet_copysheet_helper.h"
#include "importer/sheets_importer.h"

namespace
{
struct SidebarFolderTreeHelper
{
    UINT id = 0;
    DbSidebarFolderTreeType type = DbSidebarFolderTreeType_Folder;
    UINT stId = 0;
    PCWSTR name = nullptr; 
    UINT parentId = 0;
};
}

interface IDBProtectionJudgement;
namespace wo
{
class KEtWorkbook;
class AddTemplateSheetsHelper : public SheetsImporter
{
	using AttachmentIdMap = std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher>;
public:
    AddTemplateSheetsHelper(wo::KEtWorkbook* wwb, wo::KEtRevisionContext* ctx, PCWSTR filePath, bool copyContent, bool delAllSheet, IDBProtectionJudgement*, VarObj& param);
    HRESULT Exec(UINT& activeSheetId);
    bool GetHasDbSheet() { return m_hasDbSheet; }
	void rollback();
	bool GetReachFpSheetLimit() const { return m_bReachFpSheetLimit; }
	bool GetReachDashboardLimit() const { return m_bReachDashboardLimit; }
	void setMaxImportSheetCount(int maxImportSheetCount) { m_maxImportSheetCount = maxImportSheetCount; }
	bool isExceededMaxImportSheetCount() const { return m_exceededMaxImportSheetCount; }
	void SetIsForImport(bool b) { m_forImport = b; }
	void SetClearAttachment(bool b) { m_clearAttachment = b; }
	void setFallBackLookUp(bool b) {this->m_fallBackLookUp = b;}
	void SetAttachmentIdMap(const AttachmentIdMap* pAttachmentIdMap) { m_pAttachmentIdMap = pAttachmentIdMap; }
	bool GetLostFlag() const { return m_lostFlag; }
	void SerialAppSharedInfo(binary_wo::VarObj* pObj);
	bool HasCopyAirAppSheet();
	AirAppInfo GetActiveAppInfo();
	UINT GetLastDbSheetStId() const;

private:
    HRESULT deleteSheet(etoldapi::Worksheets* pTemplateWorksheets);

	HRESULT SidebarFolderTreeExec(IBook* pSrcBook, IBook* pTarBook, std::unordered_map<UINT, UINT>& sheetIdMap);
	HRESULT EnumSrcSidebarFolderTreeVec(IDbSidebarFolderTreeManager* pMgr,
										std::vector<SidebarFolderTreeHelper>& sidebarFolderTreeVec);

    wo::KEtWorkbook* m_wwb;
    PCWSTR m_filePath;
    bool m_copyContent;
    bool m_delAllSheet;
    bool m_hasDbSheet;
	int m_maxImportSheetCount = 0;
	bool m_exceededMaxImportSheetCount = false;
	bool m_forImport = false;
	bool m_clearAttachment = true;
	bool m_bReachFpSheetLimit = false;
	bool m_bReachDashboardLimit = false;
	bool m_lostFlag = false;
	bool m_fallBackLookUp {};
	std::vector<IKWorksheet*> m_newWorksheets;
    ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
    std::vector<UINT> m_delSheetVec;
	const AttachmentIdMap* m_pAttachmentIdMap = nullptr;
	std::vector<AppSharedInfo> m_appSharedInfo;
	UINT m_lastDbSheetStId;
	VarObj& m_param;
	wo::KEtRevisionContext* m_ctx;
};
} // namespace wo

#endif //__WEBET_ADD_TEMPLATE_SHEETS_H__
