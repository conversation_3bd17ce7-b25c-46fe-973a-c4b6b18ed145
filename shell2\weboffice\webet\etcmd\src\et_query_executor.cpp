#include "et_query_executor.h"
#include "kfc/string.h"
#include "workbook.h"
#include "et_revision_context_impl.h"
#include "et_query_class.h"
#include "serialize_impl.h"
#include "wo/sa_helper.h"
#include "dbsheet/et_dbsheet_query_class.h"
#include "dbsheet/dashboard/db_dashboard_query_class.h"
#include "http/db_http_query_class.h"
#include "http/et_http_query_class.h"
#include "http/db_dashboard_http_query_class.h"
#include "http/http_error.h"
#include "util.h"
#include "hresult_to_string.h"
#include "krt/krtstring.h"
#include "wo/wo_coremetrics_i.h"

namespace wo
{

EtQueryExecutor::EtQueryExecutor(wo::KEtWorkbook* wwb)
	: m_wwb(wwb), m_ctx(nullptr)
{
	Init();
}

void EtQueryExecutor::Init()
{
	initBaseExecMap();
	if (m_wwb->GetBMP()->bDbSheet)
		initDbSheetExecMap();
	else if (m_wwb->GetBMP()->bKsheet)
	{
		initDbSheetExecMap();
		initExecMap();
	}
	else
		initExecMap();
}

EtQueryExecutor::~EtQueryExecutor()
{
}

#define LOG_WARN(info) WO_LOG_X(m_wwb->getLogger(), WO_LOG_WARN, (info))

WebInt EtQueryExecutor::Query(binary_wo::VarObj cmds, IRevisionContext* ctx, binary_wo::BinWriter& bw)
{
	m_ctx = static_cast<KEtRevisionContext*>(ctx);
	WebInt res = WO_OK;
	int nCount = cmds.arrayLength_s();
	if (nCount <= 0)
		return res;

	m_wwb->coreMetric().onBeforeExecTasks();
	KBinSizeU32Mc<binary_wo::BinWriter> mcBin(&m_wwb->coreMetric(), &bw, KU32MetricItem::kSerialExQueryCmdsByte);
	wo::util::CallTimeStat callTime(nullptr, nullptr, [this](unsigned int ms) { 
		this->m_wwb->coreMetric().setTasksTime(ms);
	});
	
	KSerialWrapBinWriter acpt(bw, ctx);
	sa::Leave warpArray = sa::enterArray(&acpt, "extendQueryResult");
	for (int i = 0; i < nCount; ++i)
	{
		binary_wo::VarObj vCmd = cmds.at_s(i);
		WebStr name = vCmd.field_str("name");
		util::AutoCmdTraceId autoCmdTraceId(m_ctx, vCmd);
		util::CmdTimeMemStat cmdTimeStat(m_wwb, m_ctx, [this](unsigned int ms, unsigned int memUsedKb, unsigned int memKb) {
				this->m_wwb->coreMetric().addCmdTime(ms, memUsedKb, memKb);
			});
		cmdTimeStat.start(name, false);
		m_wwb->coreMetric().onBeforeCmdExec(name, m_ctx->getCmdTraceId());

		ExecMap::iterator itFunc = m_execMap.find(name);
		if (itFunc != m_execMap.end())
		{
			sa::Leave wrapStruct = sa::enterStruct(&acpt, nullptr);
			binary_wo::VarObj vParam = vCmd.get_s("param");
			EtQueryExecBase* exec = itFunc->second.get();
			HRESULT hr = E_FAIL;
			{
				sa::Leave wrapResult = sa::enterStruct(&acpt, "result");

				hr = exec->PreExecute(vParam, m_ctx);
				if (SUCCEEDED(hr))
				{
					try
					{
						hr = exec->Exec(vParam, m_ctx, &acpt);
					}
					catch (et_exception ex)
					{
						WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "Exception caught");
						hr = ex.get_result();
					}
				}
				hr = exec->PostExecute(hr, vParam, m_ctx);

			}
			acpt.addInt32("error", hr);
			acpt.addString("name", name);
			if (FAILED(hr)) 
				acpt.addString("errName", GetErrWideString(hr));
			if (vParam.has("callBackId")) {
				acpt.addUint32("callBackId", vParam.field_uint32("callBackId"));
			}
			if (vCmd.has("seq"))
				acpt.addInt32("seq", vCmd.field_int32("seq"));
			if (FAILED(hr))
			{
				WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "Execution failed, code: %ld error: %s", hr, GetErrString(hr));
				res = WO_FAIL;
			}
		}
		else
		{
			WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "query command not found: %s", krt::fromUtf16(name).toUtf8().data());
			HRESULT hr = E_COMMAND_NOT_FOUND;
			res = WO_FAIL;
			m_ctx->postExecute(hr);
			binary_wo::BinWriter *pResponse = m_ctx->getHttpResponse();
			KSerialWrapBinWriter acpt(*pResponse, m_ctx);
			AddHttpError(hr, &acpt, nullptr);
			break;
		}
	}

	return res;
}

template <typename ExecClass_>
void EtQueryExecutor::mapAddExecClass()
{
	std::shared_ptr<ExecClass_> ins(new ExecClass_(m_wwb));
	m_execMap.insert(std::make_pair(ks_wstring(ins->GetTag()), ins));
}

void EtQueryExecutor::initBaseExecMap()
{
	mapAddExecClass<QueryFuncInfo>();
	mapAddExecClass<QueryEmpty>();
	mapAddExecClass<QueryWebExtension>();
	mapAddExecClass<QueryUsersInfo>();
}

void EtQueryExecutor::initExecMap()
{
	mapAddExecClass<QueryChartPos>();
	mapAddExecClass<QuerySmartTipsValue>();
	mapAddExecClass<QueryRangeEnd>();
	mapAddExecClass<QueryRangeFind>();
	mapAddExecClass<QueryRangeFindAll>();
	mapAddExecClass<QueryRangCopy>();
	mapAddExecClass<QueryMultiRangCopy>();
	mapAddExecClass<QuerySortExpand>();
	mapAddExecClass<QueryCustomSortFields>();
	mapAddExecClass<QueryCellColorsInRange>();
	mapAddExecClass<QueryNumfmt>();
	mapAddExecClass<QueryShowConditionRules>();
	mapAddExecClass<QueryPicUploadProgress>();
	mapAddExecClass<QueryConvertStatus>();
	mapAddExecClass<QueryApplyConditionFormat>();
	mapAddExecClass<QueryText2ColPreView>();
	mapAddExecClass<QueryText2ColCheck>();
	mapAddExecClass<QueryRangeSliceInfo>();
	mapAddExecClass<QueryRangeCanAIFill>();
	mapAddExecClass<QueryDataRowsOutOfRange>();
	mapAddExecClass<QueryRangeContents>();
	mapAddExecClass<QueryTxt2ColSubRange>();
	mapAddExecClass<QuerySameDvRange>();
	mapAddExecClass<QueryIsSameDvInRange>();
	mapAddExecClass<QueryCheckValidation>();
	mapAddExecClass<QueryDvCustomList>();
	mapAddExecClass<QueryDvCustomListByFormula>();
    mapAddExecClass<QueryDvCustomListCellResult>();
	mapAddExecClass<QueryTableStyle>();
	mapAddExecClass<QueryGetRemoveDuplicate>();
	mapAddExecClass<QueryDuplicatesCount>();
	mapAddExecClass<QueryDataValidation>();
	mapAddExecClass<QueryMergeFileList>();
	mapAddExecClass<QueryProcessOnInfo>();
	mapAddExecClass<QueryAutoFilter>();
	mapAddExecClass<QueryGuessFormTitles>();
	mapAddExecClass<QueryCellHistory>();
	mapAddExecClass<QueryTotalHistoryCount>();
	mapAddExecClass<EnumHistoryCell>();
	mapAddExecClass<QueryNextUnlock>();
	mapAddExecClass<QueryValidateProtectSheetPassword>();
	mapAddExecClass<QueryValidateProtectSheetSecretKey>();
	mapAddExecClass<QueryCanUnlockArea>();
	mapAddExecClass<QuerySupBooksList>();
	mapAddExecClass<QueryChartData>();
	mapAddExecClass<QuerySlicerData>();
	mapAddExecClass<QuerySheetBackground>();
	mapAddExecClass<QuerySingleCellHistory>();
	mapAddExecClass<QueryCustomNF>();
	mapAddExecClass<QueryShapeCopy>();
	mapAddExecClass<QueryDocSlimStat>();
	mapAddExecClass<QueryNeedDocSlim>();
	mapAddExecClass<CollectSecDocInfoCmd>();
	mapAddExecClass<QueryHyperlink>();
	mapAddExecClass<QuerySubscriptionInfo>();
	mapAddExecClass<QueryDocumentCustomProperty>();
	mapAddExecClass<QueryRangeValues>();
	mapAddExecClass<QueryCommentInfoById>();
	mapAddExecClass<QueryPivotTableDefaultRange>();
	mapAddExecClass<QueryPivotTableExtendRange>();
	mapAddExecClass<QueryNewCommentCount>();
	mapAddExecClass<QueryMergeTasksInfo>();
	mapAddExecClass<QueryIsMergeTaskValid>();
	mapAddExecClass<QuerySheetNameList>();
	mapAddExecClass<QueryDiagnosisResult>();
	mapAddExecClass<QueryDiagnosisStart>();
	mapAddExecClass<QueryDiagnosisStop>();
	mapAddExecClass<QueryTextLinkPosition>();
	mapAddExecClass<QueryImportrangeRefCellUrls>();
	mapAddExecClass<QueryRangeData>();
	mapAddExecClass<QueryMapCellPos>();
	mapAddExecClass<QueryControlARange>();
	mapAddExecClass<QueryFormDataRow>();
    mapAddExecClass<QueryRangeFirstRow>();
	mapAddExecClass<QueryRangeFirstCol>();
    mapAddExecClass<QueryRangeQRLabelCount>();
	mapAddExecClass<QueryCollectInquireInfo>();
	mapAddExecClass<QueryGetAllInquirer>();
	mapAddExecClass<QueryAiRecognizedInfo>();
    mapAddExecClass<QuerySmartSheetRecognition>();
    mapAddExecClass<QueryGetTableTitleCnt>();
	mapAddExecClass<QueryGetAppRelatedSheetDirty>();
	mapAddExecClass<QueryNeedUpdateAppVersion>();
	mapAddExecClass<QueryEt2DBConvertable>();

	mapAddExecClass<EtHttpSheetsInfoQueryClass>();
	mapAddExecClass<EtHttpRetrieveRecordQueryClass>();
	mapAddExecClass<EtHttpGetColTypeQueryClass>();
	mapAddExecClass<EtHttpJudgeRangeIsPicQueryClass>();

	mapAddExecClass<EtHttpRangeDataQueryClass>();
	mapAddExecClass<EtHttpDefinedNameQueryClass>();
	mapAddExecClass<EtHttpTitleContentQueryClass>();
	mapAddExecClass<EtHttpDocumentInfoQueryClass>();
	mapAddExecClass<EtHttpAiRecognizedInfoQueryClass>();
	mapAddExecClass<EtHttpGetTitleContentByAIQueryClass>();
	mapAddExecClass<EtHttpIdentifyFieldTypeQueryClass>();
    mapAddExecClass<EtHttpGetQRColQueryClass>();
    mapAddExecClass<EtHttpFindFirstEmptyColQueryClass>();
    mapAddExecClass<EtHttpFindQRKeyColQueryClass>();
    mapAddExecClass<EtHttpColsInfoQueryClass>();
	mapAddExecClass<EtHttpCopyRangeDataClass>();
	mapAddExecClass<EtHttpGetRangeQRLabelCountQueryClass>();
	mapAddExecClass<EtHttpExecuteQueryApiClass>();
	mapAddExecClass<EtHttpGetLocalImagesSha1Class>();
	mapAddExecClass<EtHttpQueryProtectionInfoClass>();
	mapAddExecClass<QuerySingleCellHistoryList>();
	mapAddExecClass<QueryRefRangeByCommitVer>();
	mapAddExecClass<QueryCellHistoryPurgeOptions>();
	mapAddExecClass<QuerySpecialCells>();
	mapAddExecClass<QueryWorksheetFunction>();
	mapAddExecClass<QueryCalculate>();
	mapAddExecClass<QueryRangeValue2>();
	mapAddExecClass<QueryPivotRangeGroupType>();
	mapAddExecClass<QueryRangeShowConditionRules>();
	mapAddExecClass<QuerySheetHasCellTextOverflow>();
	mapAddExecClass<QueryIsEmptyCell>();
	mapAddExecClass<QueryEndEmptyRowInRg>();
	mapAddExecClass<QuerySplitSheet>();
	mapAddExecClass<QueryBookHasAiFormula>();
	mapAddExecClass<EtHttpGetFieldInfoQueryClass>();
	mapAddExecClass<EtHttpGetHeaderInfoQueryClass>();
	mapAddExecClass<QueryDataAnalyzeMergeStatus>();
	mapAddExecClass<EtHttpRangeMatrixQueryClass>();
	mapAddExecClass<EtHttpDataAnalyzeMergeInfoQueryClass>();
	mapAddExecClass<EtHttpDataAnalyzeMergeLogQueryClass>();
	mapAddExecClass<EtHttpCheckHasHiddenQueryClass>();
}

void EtQueryExecutor::initDbSheetExecMap()
{
	mapAddExecClass<QueryCustomCalendar>();
	mapAddExecClass<QueryExecDbRangeCopy>();
	mapAddExecClass<QueryExecDbFilterList>();
	mapAddExecClass<QueryExecDbPermissionFilterList>();
	mapAddExecClass<QueryExecDbChartFilterList>();
	mapAddExecClass<QueryExecDbFieldItems>();
	mapAddExecClass<QueryExecDbSearchNext>();
	mapAddExecClass<QueryExecDbFieldTypeModifyCheck>();
	mapAddExecClass<QueryExecDbVirtualViewQueryRecords>();
	mapAddExecClass<QueryExecDbVirtualViewFilterValuesList>();
	mapAddExecClass<QueryDbTimerTask>();
	mapAddExecClass<QueryDbSheetSummary>();
	mapAddExecClass<QueryDbSheetShareLinksInfo>();
	mapAddExecClass<QueryDbSheetViews>();
	mapAddExecClass<QueryDbPermission>();
	mapAddExecClass<QueryDbAllAccessibleFields>();
    mapAddExecClass<QueryDbDashboardModuleContent>();
	mapAddExecClass<QueryDbDashboardModulesContent>();
	mapAddExecClass<QueryDbLinkRecords>();
	mapAddExecClass<QueryAiRecognizedInfo>();
    mapAddExecClass<QueryDbDashboardFilterValues>();

	mapAddExecClass<DbHttpInquireParentRecordTaskClass>();
	mapAddExecClass<DbHttpIsEnableParentRecordTaskClass>();
	mapAddExecClass<DbHttpListRecordsQueryClass>();
	mapAddExecClass<DbHttpListRecordsByPageQueryClass>();
	mapAddExecClass<DbHttpRetrieveRecordQueryClass>();
	mapAddExecClass<DbHttpRetrieveRecordsQueryClass>();
	mapAddExecClass<DbHttpPermissionQueryClass>();
	mapAddExecClass<DbHttpPermissionListSchemaQueryClass>();
	mapAddExecClass<DbHttpAttachmentPermissionQueryClass>();
	mapAddExecClass<DbHttpBaseSchemaQueryClass>();
	mapAddExecClass<DbHttpRetrieveStatisticsResultQueryClass>();
	mapAddExecClass<DbHttpDevRetrieveFormQueryClass>();
	mapAddExecClass<DbHttpDevListRecordsQueryClass>();
    mapAddExecClass<DbHttpListSheetsQueryClass>();
    mapAddExecClass<DbHttpListWebExtensionsQueryClass>();
    mapAddExecClass<DbHttpGetDashboardChartResultQueryClass>();
	mapAddExecClass<DbHttpGetFilterValuesQueryClass>();
    mapAddExecClass<DbDashboardHttpListChartsQueryClass>();
    mapAddExecClass<DbDashboardHttpGetChartsDataQueryClass>();
    mapAddExecClass<DbDashboardHttpListFiltersQueryClass>();
	mapAddExecClass<DbChartHttpListRecordsQueryClass>();
	mapAddExecClass<DbHttpGetFilterValuesListQueryClass>();
    mapAddExecClass<DbHttpGetServerRenderDataQueryClass>();
	mapAddExecClass<DbHttpFieldBaseSchemaQueryClass>();
}

} // namespace wo
