﻿
#ifndef __WEBET_SHEETS_IMPORTER_H__
#define __WEBET_SHEETS_IMPORTER_H__

#include "binvariant/binvarobj.h"

using binary_wo::VarObj;
class SheetsImporter
{
public:
    SheetsImporter();
    virtual ~SheetsImporter() {};
public:
    void InitImportSheets(const VarObj& param);
    bool CheckSheetImport(UINT sheetStId);
    bool IsEnableImportSheets() const;
    const std::unordered_set<UINT>& GetImportSheets() const;
protected:
    bool m_useImportSheets;
    std::unordered_set<UINT> m_importSheets;
};

#endif // __WEBET_SHEETS_IMPORTER_H__