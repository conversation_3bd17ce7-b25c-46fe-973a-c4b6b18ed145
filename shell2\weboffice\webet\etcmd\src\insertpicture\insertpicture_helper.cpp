﻿#include "etstdafx.h"
#include "insertpicture_helper.h"
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include "applogic/cell_image_helper.h"
#include <public_header/drawing/api/dghost_i.h>

InsertPictureHelper::InsertPictureHelper(_Worksheet* pWs, Shapes* pShapes)
	: m_pWorksheet(pWs)
	, m_pShapes(pShapes)
{
	ASSERT(m_pWorksheet);
}

HRESULT InsertPictureHelper::Insert(LPCWSTR path, single Left, single Top,
	single width, single height, Shape** ppShape)
{
	ks_stdptr<Shapes> spShapes = m_pShapes;
	if (!spShapes)
	{
		m_pWorksheet->get_Shapes(FALSE, &spShapes);
		if (!spShapes)
			return E_FAIL;
	}	

	HRESULT hr = S_OK;
	HGBL hGbl = NULL;
	hr = CreateHGblFromFile(&hGbl, path);
	if (FAILED(hr) || hGbl == NULL)
		return E_FAIL;

	ks_stdptr<Shape> spShape;
	return spShapes->AddPictureByHGBL(hGbl, Left, Top, width, height, ppShape);
}

HRESULT InsertPictureHelper::InsertFloatPictureAsAttachment(PCWSTR filePath, ks_wstring &name, PCWSTR url,single Left, single Top,
		float& width, float& height, BOOL bCompress, IKShape** Picture)
{
	int nWidth = 0;
	int nHeight = 0;
	ks_stdptr<IKShape> spKShape;
	HRESULT hr = _CreatePictureByUrl(filePath, name , url, &nWidth, &nHeight, &spKShape);
	if(FAILED(hr) || !spKShape )
		return E_FAIL;

	if(width > 0)
		nWidth = width;
	if(height > 0)
		nHeight = height;

	RECT rcPic = {0};
	if (Left  < 0 || Top < 0)
	{
		int nL = 0, nT = 0;
		GetActiveCellLtPoint(nL, nT);
		rcPic.left = nL;
		rcPic.top  = nT;
	}
	else
	{
		rcPic.left = Left;
		rcPic.top  = Top;
	}

	//给一个默认大小吧，如果从图片中没有获取到大小的话
	if (nWidth <= 0)
		nWidth = 100;
	if (nHeight <= 0)
		nHeight = 100;

	rcPic.right  = rcPic.left + nWidth;
	rcPic.bottom = rcPic.top + nHeight;

	// 根据MSO规则：若长或宽其一大于11 inch，则插入的图片要以锁定纵横比的方式调整缩放至最长边为11 inch。
	LONG rcWidth = rcPic.right - rcPic.left;
	LONG rcHeight = rcPic.bottom - rcPic.top;
	if (rcWidth > 11 * 1440 || rcHeight > 11 * 1440)
	{
		double shrinkRatio = fmin(11.0 * 1440 / rcWidth, 11.0 * 1440 / rcHeight);
		rcPic.right = rcPic.left + rcWidth * shrinkRatio;
		rcPic.bottom = rcPic.top + rcHeight * shrinkRatio;
	}

	RECT rcAnchor = {0};
	{
		drawing::ShapeTreeControl* pCtl = getShapeTreeControl(m_pWorksheet->GetDrawingCanvas());
		ASSERT(pCtl);
		ks_stdptr<IKDrawingSession> spDrawingSession = pCtl->getDrawingSession();
		ASSERT(spDrawingSession);
		ks_stdptr<IKShapeAbsolutePos> spShapeAbsPos;
		HRESULT hr = spDrawingSession->GetShapeAbsolutePos(&spShapeAbsPos);
		ASSERT(hr == S_OK);
		hr = spShapeAbsPos->Absolute2Anchor(spKShape, &rcPic, &rcAnchor);
	}

	//设置大小
	ks_stdptr<IKClientAnchor> spClientAnchor; 
	spKShape->GetClientAnchor(&spClientAnchor);
	if (spClientAnchor)
	{
		spClientAnchor->SetRect(spKShape, &rcAnchor);
		ks_stdptr<IETShapeAnchor> spEtAnchor;
		spClientAnchor->QueryInterface(IID_IETShapeAnchor, (void**)&spEtAnchor);
		if (spEtAnchor)
		{
			spEtAnchor->SetFloatingFlag(oploMove);
			spEtAnchor->SetOPLComplexFlag(oplComplexTwoCell);
		}
	}

	if(spKShape)
	{
		*Picture = spKShape.detach();
	}

	return S_OK;

}

HRESULT InsertPictureHelper::_CreatePictureByUrl(IN PCWSTR filePath, IN ks_wstring &name,IN PCWSTR url, OUT INT* pnWidth,
		OUT INT* pnHeight, OUT IKShape** Picture)
{
	IKWorkbook* pWorkbook = m_pWorksheet->GetWorkbook();

	ks_stdptr<IKMediaManage> spMediaMgr;
	oplGetBookMediaMgr(pWorkbook->GetBook(), &spMediaMgr);
	if (!spMediaMgr)
		return E_FAIL;
	
	if (name.empty())
		cellimg::CreateUUIDName(name);

	HRESULT hr = S_OK;
	ks_stdptr<IKBlipAtom> spBlipAtom;
	if (url)
		hr = spMediaMgr->AddAtomExternal(koplBlipUNKNOWN, name.c_str(), filePath, &spBlipAtom); // filePath 可以为NULL，这样显示的时候才下载，表单就是这个行为
	else
		hr = spMediaMgr->AddAtom(koplBlipUNKNOWN, filePath, &spBlipAtom);

	if(FAILED(hr))
		return E_FAIL;


	if (url)
	{
		spBlipAtom->SetLinkPath(url);
		spBlipAtom->SetBlipName(url);
	}

	kpt::VariantImage img = spBlipAtom->GetImage();
	*pnWidth = qRound(img.widthIn(kpt::Twip));
	*pnHeight = qRound(img.heightIn(kpt::Twip));

	//创建对象
	
	ks_castptr<IKDrawingCanvas> cpCanvas = m_pWorksheet->GetDrawingCanvas();
	ks_stdptr<IKShape> spKShape;
	hr = cpCanvas->CreateShape(sptPictureFrame,
						  &spKShape,
						  FALSE,
						  FALSE);
	if (FAILED(hr))
		return E_FAIL;

	ks_castptr<drawing::AbstractShape> pAbsShape = spKShape;

	pAbsShape->setPicID(spBlipAtom);
	pAbsShape->mutablePicture()->setStretched(true);
	pAbsShape->setNameValue(name.c_str());

	WCHAR fileName[MAX_PATH + 1] = { 0 };
	etSplitPath(filePath, NULL, NULL, fileName, NULL);
	pAbsShape->setDescription(fileName);

	if(spKShape)
	{
		*Picture = spKShape.detach();
	}

	return S_OK;
}

void InsertPictureHelper::GetActiveCellLtPoint(int& nL, int& nT)
{
	//Todo：这个方法后面待补充
	nL = nT = 100;
}

namespace PicTfHelper
{
	void CreateShapeRange(IKWorksheet* pWs, IKShapeRange** ppShapeRange)
	{
		IKDrawingCanvas* pDrawingCanvas = pWs->GetDrawingCanvas();
		ks_stdptr<IKShapeRange> spShapeRange;
		pDrawingCanvas->CreateShapeRange(&spShapeRange);
		*ppShapeRange = spShapeRange.detach();
	}

	drawing::ElementId PickOneShapeInRange(const RANGE& rg, IKShapeRange* pShapeRange)
	{
		LONG cnt = 0;
		pShapeRange->GetShapeCount(&cnt);
		for (LONG i = 0; i < cnt; ++i)
		{
			ks_stdptr<IKShape> spShape;
			pShapeRange->GetShapeByIndex(i, &spShape);
			if (!spShape)
				continue;

			CELL ltCell = cellimg::GetPicLtCell(spShape);
			if (rg.Contain(ltCell.row, ltCell.col))
			{
				LONG id = 0;
				spShape->GetShapeID(&id);
				return id;
			}
		}
		return 0;
	}
}
