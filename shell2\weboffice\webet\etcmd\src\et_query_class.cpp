#include "etstdafx.h"
#include "et_query_class.h"
#include "Coding/core_bundle/framework/krt/krtstring.h"
#include "kfc/string.h"
#include "weberror.h"
#include "workbook.h"
#include "et_revision_context_impl.h"
#include "et_binvar_spec.h"
#include "smart_tips/smart_tips.h"
#include "wo/sa_helper.h"
#include "sort_helper.h"
#include "kso/io/clipboard/ksoclipboard.h"
#include "kso/io/clipboard/ksomimetype.h"
#include "condition_format_helper.h"
#include "condition_format_wrapper.h"
#include "kfc/numfmt.h"
#include "text2col_helper.h"
#include "sort/sort_protoc.h"
#include "sort/sortitem_loader.h"
#include "etcore/et_core_sheet.h"
#include "etcore/et_core_utility.h"
#include <public_header/drawing/wo/serialtheme.h>
#include <public_header/drawing/model/abstract_shape.h>
#include <public_header/webcommon/src/processondata.h>
#include "etcore/wo_et_cell_history.h"
#include "funclib/et_funclib_forstatus.h"
#include "helpers/autofilter_helper.h"
#include "supbooks_helper.h"
#include <public_header/chart/src/mvc/kctchartlayer.h>
#include <public_header/chart/src/mvc/kctshapetree.h>
#include <vector>
#include <algorithm>
#include <cctype>
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include "mvc/et_mvc.h"
#include <vector>
#include <public_header/drawing/api/dghost_i.h>
#include "pivot_core/pivot_core_x.h"
#include "helpers/pivot_tables_helper.h"
#include "helpers/slim_helper.h"
#include "webbase/logger.h"
#include "helpers/protection_helper.h"
#include "util.h"
#include "helpers/webmime_helper.h"
#include "kfc/tools/smart_wrap.h"
#include "helpers/shape_helper.h"
#include "helpers/picture_upload_helper.h"
#include "utils/attachment_utils.h"
#include "./form/et_form_task_class.h"
#include "webbase/serialize_impl.h"
#include "et_ai/common/identifytable.h"
#include "et_ai/common/kdocutils.h"
#include "et_ai/teval.h"
#include "et_ai/smart_analysis/identifytable.h"
#include "helpers/copy_helper.h"
#include "helpers/inquirer_helper.h"
#include "woetsetting.h"
#include "helpers/table_struct_rec_helper.h"
#include "helpers/app/update_app_version_helper.h"
#include "binvariant/binvarobjhelper.h"
#include <public_header/opl/mvc/slicer/et_slicer_layer.h>
#include "helpers/serialize_variant_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"
#include "tools/et_combstr.h"
#include "collect.h"
#include "etcore/little_alg.h"
#include "split_book_opt.h"
#include "applogic/et_applogic_doc_slim.h"
#include "helpers/qrlabel_helper.h"
#include "hresult_to_string.h"
#include "helpers/range_operator_helper.h"
#include <public_header/chart/src/model/kctchart.h>
#include "helpers/copy_link_helper.h"
#include "dbsheet/db_view_identify.h"
#include <kso/framework/docslim_helper.h>
#include "dataanalyze/et_dataanalyze.h"
#include "dataanalyze/etdacoreexport.h"
#include "etcore/et_core_event_tracking.h"
#include "dashboard/webchart_config.h"

extern Callback* gs_callback;
extern WebProcType gs_procType;

namespace wo
{
static void WriteRANGE(const RANGE &range, ISerialAcceptor* acpt)
{
	acpt->addInt32("sheetIdx", range.SheetFrom());
	acpt->addInt32("rowFrom", range.RowFrom());
	acpt->addInt32("rowTo", range.RowTo());
	acpt->addInt32("colFrom", range.ColFrom());
	acpt->addInt32("colTo", range.ColTo());
}

//---------------------------------------------------------------------------
EtTaskExecutor::CmdNameSet EtQueryExecBase::m_cmdPermissionCheckWhiteList;

EtQueryExecBase::EtQueryExecBase(wo::KEtWorkbook* wwb, PCWSTR tag)
	: m_wwb(wwb)
	, m_tag(tag)
{
}

EtQueryExecBase::~EtQueryExecBase()
{

}

IDX EtQueryExecBase::GetSheetIdx(VarObj var, KEtRevisionContext* ctx)
{
	IDX sheetIdx = INVALIDIDX;
	if (var.has("sheetStId"))
	{
		UINT32 stId = var.field_uint32("sheetStId");
		IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
		pBook->STSheetToRTSheet(stId, &sheetIdx);
	}
	else if (var.has("sheetId"))
	{
		UINT32 stId = var.field_uint32("sheetId");
		IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
		pBook->STSheetToRTSheet(stId, &sheetIdx);
	}
	else if (var.has("objSheet"))
	{
		WebID objSheet = var.field_web_id("objSheet");
		sheetIdx = ctx->getSheetIndex(objSheet);
	}
	else if (var.has("sheetIdx"))
	{
		sheetIdx = var.field_int32("sheetIdx");
	}
	return sheetIdx;
}

RANGE EtQueryExecBase::ReadRange(VarObj var)
{
	return ReadRangeInl(m_wwb->GetBMP(), var);
}

PCWSTR EtQueryExecBase::GetTag()
{
	return m_tag;
}

bool EtQueryExecBase::ReadRanges(const VarObj& var, IKRanges** ppRanges)
{
	ASSERT(var.type() == binary_wo::typeArray);
	range_helper::ranges rgs = range_helper::ranges::create_instance();
	for (int i = 0; i < var.arrayLength_s(); ++i)
	{
		VarObj vRg = var.at_s(i);
		RANGE rg = ReadRange(vRg);
		rgs.add(alg::STREF_THIS_BOOK, rg);
	}
	if (rgs.size() > 0 && ppRanges)
	{
		*ppRanges = rgs;
		(*ppRanges)->AddRef();
	}
	return true;
}

ks_stdptr<etoldapi::Range> EtQueryExecBase::CreateRangeObj(VarObj var)
{
	RANGE rg = ReadRange(var);
	if (!rg.IsValid())
		return ks_stdptr<etoldapi::Range>();

	return m_wwb->CreateRangeObj(rg);
}

void EtQueryExecBase::WriteRange(ISerialAcceptor* acpt, const RANGE& rg)
{
	ASSERT(rg.IsValid());
	acpt->addInt32("sheetIdx", rg.SheetFrom());
	acpt->addInt32("rowFrom", rg.RowFrom());
	acpt->addInt32("rowTo", rg.RowTo());
	acpt->addInt32("colFrom", rg.ColFrom());
	acpt->addInt32("colTo", rg.ColTo());
}

bool EtQueryExecBase::WriteRangeObj(ISerialAcceptor* acpt, etoldapi::Range* pRange)
{
	if (!pRange)
		return false;

	ks_stdptr<IRangeInfo> spRgInfo = pRange;
	ks_stdptr<IKRanges> spIRanges;
	spRgInfo->GetIRanges(&spIRanges);
	UINT count = 0;
	spIRanges->GetCount(&count);
	if (count > 0)
	{
		const RANGE* pRG = NULL;
		spIRanges->GetItem(0, NULL, &pRG);

		if (pRG)
		{
			acpt->addInt32("sheetIdx", pRG->SheetFrom());
			acpt->addInt32("rowFrom", pRG->RowFrom());
			acpt->addInt32("rowTo", pRG->RowTo());
			acpt->addInt32("colFrom", pRG->ColFrom());
			acpt->addInt32("colTo", pRG->ColTo());
		}
	}

	return true;
}

bool EtQueryExecBase::WriteRangesObj(ISerialAcceptor* acpt, etoldapi::Range* pRange)
{
	if (!pRange)
		return false;

	ks_stdptr<IRangeInfo> spRgInfo = pRange;
	ks_stdptr<IKRanges> spIRanges;
	spRgInfo->GetIRanges(&spIRanges);
	UINT count = 0;
	spIRanges->GetCount(&count);
	acpt->addKey("rgs");
	acpt->beginArray();
	if (count > 0)
	{
		for (int i=0; i<count; i++) 
		{
			const RANGE* pRG = NULL;
			spIRanges->GetItem(i, NULL, &pRG);
			if (pRG)
			{
				acpt->beginStruct();
				WriteRANGE(*pRG, acpt);
				acpt->endStruct();
			}
		}
	}
	acpt->endArray();
	return true;	
}



drawing::AbstractShape* EtQueryExecBase::GetShape(VarObj param, KEtRevisionContext* pCtx)
{
	IDX iSheet = param.field_int32("sheetIdx");
	VarObj objIdxPath = param.get("shapeIdxPath");
	ShapeIdxPath idxPath;
	idxPath.reserve(objIdxPath.arrayLength());
	for (int i = 0, cnt = objIdxPath.arrayLength(); i < cnt; ++i)
		idxPath.push_back(objIdxPath.item_uint32(i));

	drawing::AbstractShape* pShape = pCtx->getShapeByIdxPath(iSheet, idxPath);
	return pShape;
}

void EtQueryExecBase::AddErrorStr(ISerialAcceptor* acpt, PCWSTR err)
{
	acpt->addString("errName", err);
	acpt->addString("cmdName", GetTag());
}

void EtQueryExecBase::AddErrorStr(ISerialAcceptor* acpt, HRESULT hr)
{
#define _LOCAL_ADD_ERR_NAME(ERRHR) case ERRHR: acpt->addString("errName",  __X(#ERRHR)); break;

	if (SUCCEEDED(hr))
	{
		return;
	}

	acpt->addString("cmdName", GetTag());
	switch (hr)
	{
		_LOCAL_ADD_ERR_NAME(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE);
	}

#undef _LOCAL_ADD_ERR_NAME
}

bool EtQueryExecBase::GetFileSizeAndVersion(int& version, WebFileSize& size)
{
	const FileSaveInfo& fileInfo = m_wwb->GetFileSaveInfo();
	ASSERT(fileInfo.bSaved);
	version = fileInfo.version;
	size = fileInfo.size;
	return fileInfo.bSaved;
}

void EtQueryExecBase::CollectSecDocInfo(KEtRevisionContext *ctx, const char *action, WebStr cmd)
{
	if (!m_wwb->isSecDoc()) {
		return;
	}

	if (!ctx || !gs_callback || !gs_callback->collectSecDocInfo)
		return;

	if (ctx->getUser() && ctx->getUser()->userID())
	{
		QString userid = krt::fromUtf16(ctx->getUser()->userID());
		binary_wo::BinWriter BinWriter;
		BinWriter.addKey("type");
		BinWriter.addString(cmd);
		binary_wo::BinWriter::StreamHolder shbt = BinWriter.buildStream();
		WebSlice slice = {shbt.get(), BinWriter.writeLength()};

		gs_callback->collectSecDocInfo(userid.toUtf8().constData(), action, &slice);
		WO_LOG_X(ctx->getLogger(), WO_LOG_INFO, "collectSecDocInfo: %s copy, cmd: %s",
				 userid.toUtf8().constData(), krt::fromUtf16(cmd).toUtf8().constData());
	}
}

HRESULT EtQueryExecBase::PreExecute(const VarObj& param, KEtRevisionContext* ctx)
{
	ctx->getProtectionCtx()->setCurrentCommand(GetTagNumber());

    if (m_cmdPermissionCheckWhiteList.find(GetTag()) == m_cmdPermissionCheckWhiteList.end())
    {
        IDX sheetIdx = GetSheetIdx(param, ctx);
        if (sheetIdx >= 0)
        {
            UINT sheetId = 0;
            HRESULT hr = m_wwb->GetCoreWorkbook()->GetBook()->RTSheetToSTSheet(sheetIdx, &sheetId);
            if (SUCCEEDED(hr))
            {
                hr = CheckSheetPermission(sheetId, ctx);
                if (FAILED(hr))
                    return hr;
            }
        }
    }

	const HRESULT hr = CheckCmdPermission(param, ctx);
	if (S_OK == hr)
	{
		ctx->getProtectionCtx()->setCompileFormula(!IsCompileFormulaAsText(ctx));
	}

	return hr;
}

HRESULT EtQueryExecBase::PostExecute(HRESULT hr, const VarObj& param, KEtRevisionContext* ctx)
{
	return hr;
}

bool EtQueryExecBase::IsCompileFormulaAsText(KEtRevisionContext* ctx)
{
	return ctx->getProtectionCtx()->isBookHasHiddenProperty();
}

int EtQueryExecBase::GetTagNumber() const
{
	return wo::IEtProtectionCtx::wocmdUnknow;
}

ks_stdptr<ISheetProtection> EtQueryExecBase::getSheetProtection(IDX idxSheet)
{
	ASSERT(idxSheet >= 0);
	ks_stdptr<ISheetProtection> res;
	if (idxSheet >= m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount())
	{
		WOLOG_INFO << "getSheetProtection failed: " << idxSheet;
		return res;
	}

	IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(idxSheet);
	ks_castptr<_Worksheet> pApiWs = pWorksheet;
	if (pApiWs)
	{
		res = pApiWs->GetProtection();
	}

	return res;
}

HRESULT EtQueryExecBase::CheckProtectionCell(ISheet *isheet, INT32 row, INT32 col, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isCellHidden(isheet, row, col))
	{
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
	}

	return S_OK;
}

HRESULT EtQueryExecBase::CheckProtectionRange(const RANGE &rg, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
	{
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
	}

	return S_OK;
}

HRESULT EtQueryExecBase::CheckProtectionRange(const VarObj& param, KEtRevisionContext* ctx)
{
	const RANGE range = ReadRange(param);
	return CheckProtectionRange(range, ctx);
}

HRESULT EtQueryExecBase::CheckProtectionRanges(IKRanges* rgs, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isRangeHasHidden(rgs))
	{
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
	}

	return S_OK;
}

HRESULT EtQueryExecBase::CheckBookHasHiddenPropRange(KEtRevisionContext* ctx)
{
	CHECK_BOOK_HAS_HIDDEN_PROP_RANGE(ctx);
	return S_OK;
}

HRESULT EtQueryExecBase::CheckProtectionIsMaster(IDX idxSheet,  KEtRevisionContext* ctx)
{
	ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(idxSheet);
	if (!pSheetProtection || !pSheetProtection->IsProtected() || pSheetProtection->IsMaster())
	{
		return S_OK;
	}

	return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;
}

bool EtQueryExecBase::isBookHasHidden(KEtRevisionContext* ctx)
{
	return ctx->getProtectionCtx()->isBookHasHidden();
}

bool EtQueryExecBase::isThisBookHasHiddenProp(KEtRevisionContext* ctx)
{
	return ctx->getProtectionCtx()->isBookHasHiddenProperty();
}

HRESULT EtQueryExecBase::CheckSheetPermission(UINT sheetId, KEtRevisionContext* ctx)
{
    return ctx->getProtectionCtx()->isSheetHidden(sheetId) ? E_SHEET_NO_PERMISSION : S_OK;
}

//////////////////////////////////////////////////////////////////////////

QuerySmartTipsValue::QuerySmartTipsValue(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("smartTips"))
	, m_pSmartTips(NULL)
{

}

QuerySmartTipsValue::~QuerySmartTipsValue()
{
	delete m_pSmartTips;
}

HRESULT QuerySmartTipsValue::transformVisibleRanges(IKRanges *spRanges, KEtRevisionContext* ctx)
{
    IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
    std::vector<RANGE> vctVisibleRg;
    UINT rangeCount = 0;
    spRanges->GetCount(&rangeCount);
    for (int i = 0; i < rangeCount; ++i)
    {
        const RANGE *rg = nullptr;
        spRanges->GetItem(i, NULL, &rg);

		if (!util::IsValidSheetIdx(pBook, rg->SheetFrom()))
			continue;

        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(rg->SheetFrom(), &spSheet);
        ctx->getProtectionCtx()->splitVisibleRange(spSheet, *rg, vctVisibleRg);
    }

    if (vctVisibleRg.empty())
    {
        return E_INVALIDARG;
    }

    if (vctVisibleRg.size() == 1)
    {
        if (vctVisibleRg[0].IsSingleCell())
        {
            return E_INVALIDARG;
        }
    }

    spRanges->Clear();
    for (auto i = vctVisibleRg.begin(); i != vctVisibleRg.end(); ++i)
    {
        const auto &rg = *i;
        spRanges->Append(alg::STREF_THIS_BOOK, rg);
    }

	return S_OK;
}

HRESULT QuerySmartTipsValue::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	VarObj vRanges = param.get_s("ranges");
	ks_stdptr<IKRanges> spRanges;
	ReadRanges(vRanges, &spRanges);
	if (!spRanges)
		return E_INVALIDARG;

	if (!spRanges)
		return E_INVALIDARG;

	if (ctx->getProtectionCtx()->isBookHasHidden())
	{
		HRESULT hr = transformVisibleRanges(spRanges, ctx);
		if (hr != S_OK)
		{
			return hr;
		}
	}

    if (!m_pSmartTips)
	{
		m_pSmartTips = new KSmartTips();
		m_pSmartTips->Init(m_wwb->GetCoreApp());
	}

	KSmartTips::WO_RESULT_VEC vecResult;
	KSmartTips::DOUBLE_VEC dbResult;
	m_pSmartTips->WO_GetTips(spRanges, vecResult, dbResult);

	if (vecResult.size() == 6)
	{
		acpt->addString("sum", vecResult.at(0).c_str());
		acpt->addString("average", vecResult.at(1).c_str());
		acpt->addString("count", vecResult.at(2).c_str());
		acpt->addString("numberCount", vecResult.at(3).c_str());
		acpt->addString("min", vecResult.at(4).c_str());
		acpt->addString("max", vecResult.at(5).c_str());

		acpt->addFloat64("fsum", dbResult.at(FSI_SUM));
		acpt->addFloat64("faverage", dbResult.at(FSI_AVERAGE));
		acpt->addFloat64("fcount", dbResult.at(FSI_COUNT));
		acpt->addFloat64("fnumberCount", dbResult.at(FSI_NUMCOUNT));
		acpt->addFloat64("fmin", dbResult.at(FSI_MIN));
		acpt->addFloat64("fmax", dbResult.at(FSI_MAX));
	}
	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
QueryRangeEnd::QueryRangeEnd(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.end"))
{
}

HRESULT QueryRangeEnd::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<etoldapi::Range> spRange = CreateRangeObj(param);
	if (!spRange)
		return E_INVALIDARG;

	ks_wstring strDir = param.field_str("direction");
	ETDirection dir = etDown;
	if (__X("etDown") == strDir)
		dir = etDown;
	else if (__X("etUp") == strDir)
		dir = etUp;
	else if (__X("etToLeft") == strDir)
		dir = etToLeft;
	else if (__X("etToRight") == strDir)
		dir = etToRight;
	else
		return E_INVALIDARG;

	ks_stdptr<etoldapi::Range> spResult;
	HRESULT hr = spRange->get_End(dir, &spResult);
	if (SUCCEEDED(hr))
		WriteRangeObj(acpt, spResult);

	return hr;
}

//////////////////////////////////////////////////////////////////////////
QueryRangeFindBase::QueryRangeFindBase(wo::KEtWorkbook* wwb, PCWSTR tag)
	: EtQueryExecBase(wwb, tag)
{
}

HRESULT QueryRangeFindBase::GetFindParam(const VarObj& param
	, ks_stdptr<etoldapi::Range>& spRange
	, KComVariant& varWhat
	, KComVariant& varAfter
	, KComVariant& varLookIn
	, KComVariant& varLookAt
	, KComVariant& varSearchOrder
	, ETSearchDirection& searchDir
	, KComVariant& varMatchCase
	, KComVariant& varMatchByte
	, KComVariant& varSearchFormat
	, KComVariant& varSearchRange
	, CELL& cellActive)
{
	spRange = CreateRangeObj(param);
	if (!spRange)
		return E_INVALIDARG;

	WebStr strWhat = param.field_str("what");
	varWhat = KComVariant(ks_bstr(strWhat));

	if (param.has("after"))
	{
		VarObj vAfter = param.get_s("after");
		V_VT(&varAfter) = VT_UNKNOWN;
		V_UNKNOWN(&varAfter) = NULL;
		V_UNKNOWN(&varAfter) = CreateRangeObj(vAfter).detach();
		if (V_UNKNOWN(&varAfter) == NULL) {
			return E_INVALIDARG;
		}
	}

	varLookIn = etSmart;
	if (param.has("lookIn"))
	{
		WebStr strLookIn = param.field_str("lookIn");
		if (xstrcmp(__X("etSmart"), strLookIn) == 0)
			varLookIn = etSmart;
		else if (xstrcmp(__X("etFormulas"), strLookIn) == 0)
			varLookIn = etFormulas;
		else if (xstrcmp(__X("etValues"), strLookIn) == 0)
			varLookIn = etValues;
		else if (xstrcmp(__X("etComments"), strLookIn) == 0)
			varLookIn = etComments;
	}

	varLookAt = etPart;
	if (param.has("lookAt"))
	{
		WebStr strLookAt = param.field_str("lookAt");
		if (xstrcmp(__X("etPart"), strLookAt) == 0)
			varLookAt = etPart;
		else if (xstrcmp(__X("etWhole"), strLookAt) == 0)
			varLookAt = etWhole;
	}

	varSearchOrder = etByRows;
	if (param.has("searchOrder"))
	{
		WebStr strSearchOrder = param.field_str("searchOrder");
		if (xstrcmp(__X("etByRows"), strSearchOrder) == 0)
			varSearchOrder = etByRows;
		else if (xstrcmp(__X("etByColumns"), strSearchOrder) == 0)
			varSearchOrder = etByColumns;
	}

	searchDir = etNext;
	if (param.has("searchDirection"))
	{
		WebStr strSearchDir = param.field_str("searchDirection");
		if (xstrcmp(__X("etNext"), strSearchDir) == 0)
			searchDir = etNext;
		else if (xstrcmp(__X("etPrevious"), strSearchDir) == 0)
			searchDir = etPrevious;
	}

	varMatchCase.AssignBOOL(FALSE);
	if (param.has("matchCase"))
	{
		varMatchCase.AssignBOOL( param.field_bool("matchCase") ? TRUE : FALSE);
	}

	varMatchByte.AssignBOOL(FALSE);
	if (param.has("matchByte"))
	{
		varMatchByte.AssignBOOL( param.field_bool("matchByte") ? TRUE : FALSE);
	}

	varSearchFormat.AssignBOOL(FALSE);
	if (param.has("searchFormat"))
	{

	}

	varSearchRange = 0;
	if (param.has("searchRange"))
	{
		varSearchRange = param.field_int32("searchRange");
	}

	if (param.has("activeCell"))
	{
		VarObj varActiveCell = param.get("activeCell");
		cellActive.row = varActiveCell.field_int32("row");
		cellActive.col = varActiveCell.field_int32("col");
	}

	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
QueryRangeFind::QueryRangeFind(KEtWorkbook* wb)
	: QueryRangeFindBase(wb, __X("range.find"))
{
}

HRESULT QueryRangeFind::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	HRESULT hr = S_OK;
	ks_stdptr<etoldapi::Range> spRange;
	KComVariant varWhat;
	KComVariant varAfter;
	KComVariant varLookIn;
	KComVariant varLookAt;
	KComVariant varSearchOrder;
	ETSearchDirection searchDir;
	KComVariant varMatchCase;
	KComVariant varMatchByte;
	KComVariant varSearchFormat;
	KComVariant varSearchRange;
	CELL cellActive = {-1, -1};
	hr = GetFindParam(param, spRange, varWhat, varAfter, varLookIn, varLookAt, varSearchOrder
		, searchDir, varMatchCase, varMatchByte, varSearchFormat, varSearchRange, cellActive);
	if (FAILED(hr))
		return hr;

	if(ctx)
	{
		bool bSearchCellImg = param.has("searchCellImg") && param.field_bool("searchCellImg");
		ctx->SetSearchCellImg(bSearchCellImg);
	}

	ks_stdptr<etoldapi::Range> spResult;
	hr = spRange->Find(
			varWhat, varAfter, varLookIn, varLookAt, varSearchOrder, searchDir, varMatchCase, varMatchByte, varSearchFormat, &spResult);
		
	if (FAILED(hr) || !spResult)
		return hr;

	ks_stdptr<IRangeInfo> spRgInfo = spResult;
	ks_stdptr<IKRanges> spIRanges;
	spRgInfo->GetIRanges(&spIRanges);
	WriteRangeObj(acpt, spResult);
	return hr;
}

//////////////////////////////////////////////////////////////////////////
QueryRangeFindAll::QueryRangeFindAll(KEtWorkbook* wb)
	: QueryRangeFindBase(wb, __X("range.findAll"))
{
}

HRESULT QueryRangeFindAll::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	HRESULT hr = S_OK;
	ks_stdptr<etoldapi::Range> spRange;
	KComVariant varWhat;
	KComVariant varAfter;
	KComVariant varLookIn;
	KComVariant varLookAt;
	KComVariant varSearchOrder;
	ETSearchDirection searchDir;
	KComVariant varMatchCase;
	KComVariant varMatchByte;
	KComVariant varSearchFormat;
	KComVariant varSearchRange;
	CELL cellActive = {-1, -1};
	hr = GetFindParam(param, spRange, varWhat, varAfter, varLookIn, varLookAt, varSearchOrder
		, searchDir, varMatchCase, varMatchByte, varSearchFormat, varSearchRange, cellActive);
	if (FAILED(hr))
		return hr;

	if(ctx)
	{
		bool bSearchCellImg = param.has("searchCellImg") && param.field_bool("searchCellImg");
		ctx->SetSearchCellImg(bSearchCellImg);
	}

	ks_stdptr<_Worksheet> ptrWorksheet = NULL;
	spRange->get_Worksheet(&ptrWorksheet);
	if (!ptrWorksheet) return E_FAIL;
	ptrWorksheet->Activate();
	spRange->Select();

	if (cellActive.row >= 0 && cellActive.col >= 0)
	{
		RANGE rgActiveCell(m_wwb->GetBMP());
		rgActiveCell.SetSheetFromTo(param.field_int32("sheetIdx"));
		rgActiveCell.SetRowFromTo(cellActive.row);
		rgActiveCell.SetColFromTo(cellActive.col);
		m_wwb->CreateRangeObj(rgActiveCell)->Activate();
	}

	m_vctRecord.clear();
	ks_castptr<IFindAllCellIsContinue> spFindAllCellIsContinue(this);
	ks_castptr<IInterfaceSearchAllListReference> spReference(this);
	hr = m_wwb->GetCoreWorkbook()->FindAll(spFindAllCellIsContinue, varWhat, varLookIn, varLookAt
		, varSearchOrder, varMatchCase, varMatchByte, varSearchRange, spReference);
	if (FAILED(hr))
		return hr;

	acpt->addKey("findResult");
	acpt->beginArray();
	int iListSize = m_vctRecord.size();
	for (int i = 0; i < iListSize; i++)
	{
		FindALLResulteWo record = m_vctRecord[i];
		acpt->beginStruct();
		acpt->addString("worksheet", record.wszWorksheet);

		acpt->addKey("address");
		acpt->beginStruct();
		acpt->addInt32("row", record.cellAddress.row);
		acpt->addInt32("col", record.cellAddress.col);
		acpt->endStruct();

		acpt->endStruct();
	}
	acpt->endArray();
	m_vctRecord.clear();

	return hr;
}

BOOL QueryRangeFindAll::IsContinue()
{
	/*if (FIsRun)
	{
		EventProcessHelper helper;
		helper.processExcludeUserEvents(5);
	}
	return FIsRun;*/
	return TRUE;
}

STDIMP_(BOOL) QueryRangeFindAll::QueryInterrupts()
{
	/*grabMouse();
	QCoreApplication::processEvents();
	releaseMouse();
	ks_bstr title(krt::utf16(tr("WPS Spreadsheets")));
	ks_bstr text(krt::utf16(tr("You have interrupted the Find All command. Do you want to continue searching?")));
	int res = kxApp->MessageBox(text, title, MB_ICONEXCLAMATION | MB_YESNO);

	bool bInterrupts = (IDNO == res);
	if (bInterrupts)
		FIsRun = false;

	return bInterrupts;*/
	return FALSE;
}

int QueryRangeFindAll::size()
{
	return m_vctRecord.size();
}

HRESULT QueryRangeFindAll::addItem(FindALLResulteWo record)
{
	m_vctRecord.push_back(record);
	return S_OK;
}

////////////////////////////////////////////////
QueryRangeCopyBase::QueryRangeCopyBase(wo::KEtWorkbook* wwb, PCWSTR tag)
	: EtQueryExecBase(wwb, tag)
	, m_oneMillonSize(1024*1024)
{
	
}

void QueryRangeCopyBase::CollectCopyInfos(const VarObj& param, KEtRevisionContext* ctx, bool bMultiCopy, const CopyTypeInfo& txtCopyInfo, const CopyTypeInfo& htmlCopyInfo, int totalTime)
{
    if (!bMultiCopy && totalTime > 10)
    {
        IDX sheetIdx = GetSheetIdx(param, ctx);
		if (sheetIdx == INVALIDIDX)
			return;
	    ks_stdptr<IBook> spBook = m_wwb->GetCoreWorkbook()->GetBook();
        ks_stdptr<ISheet> spSheet;
	    spBook->GetSheet(sheetIdx, &spSheet);
        BMP_PTR bmp = m_wwb->GetBMP();
        // 埋点：copy耗时
        binary_wo::BinWriter binWriter;
        binWriter.addStringField(m_wwb->getFileId(), "fileid");
        binWriter.addStringField(bmp->bKsheet ? __X("ksheet") : bmp->bDbSheet ? __X("db") : __X("et"), "project");
        // 只记录单选区复制的行列数, 包含隐藏行列
        RANGE rg = ReadRange(param);
        RECT rx = {0};
        RANGE rgUsed(bmp);
        rgUsed.SetSheetFromTo(sheetIdx);
        rgUsed.SetRowFromTo(spSheet->GetTop(), spSheet->GetBottom());
        rgUsed.SetColFromTo(spSheet->GetLeft(), spSheet->GetRight());
		rg = rgUsed.Intersect(rg);
        ks_wstring nameStr;
        nameStr.Format(__X("behaviour_etcopy_%ld"), (long)(rg.Width()*rg.Height()));
        binWriter.addStringField(nameStr.c_str(), "name");


        ks_wstring txtStr;
        txtStr.Format(__X("txt_%d_%d_%d"), txtCopyInfo.isBreak ? 1 : 0, txtCopyInfo.exportCells, txtCopyInfo.lostTime);
        binWriter.addStringField(txtStr.c_str(), "name1");

        ks_wstring htmlStr;
        htmlStr.Format(__X("html_%d_%d_%d"), htmlCopyInfo.isBreak ? 1 : 0, htmlCopyInfo.exportCells, htmlCopyInfo.lostTime);
        binWriter.addStringField(htmlStr.c_str(), "cmd_name");

        // 总时长
        binWriter.addInt32Field(totalTime, "count");
        binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
        WebSlice slice = {shbt.get(), binWriter.writeLength()};
        gs_callback->collectInfo("fileinclude", &slice);
    }
}

HRESULT QueryRangeCopyBase::execCopy(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt, bool bMultiCopy)
{
	class CellValueAcpt: public ICellValueAcpt
	{
	private:
		bool* m_bFileLink;
		KEtRevisionContext* m_ctx;
		ISheet* m_pSheet;

	public:
		CellValueAcpt(bool* bFileLink, KEtRevisionContext* ctx, ISheet *pSheet)
			: m_bFileLink(bFileLink), m_ctx(ctx), m_pSheet(pSheet)
		{
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if (*m_bFileLink)
			{
				//多range中如果已经是包含文档超链接了，那就停止吧
				return 1;
			}
			if (pToken == NULL)
			{
				// 继续枚举
				return 0;
			}

			//判断读取的区域是否在保护区域内，若在，禁止读取
            bool bCellInvisibleForCurUser = m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col);
            if(bCellInvisibleForCurUser)
                return 0;//继续枚举

			DWORD dwType = alg::GetExecTokenMajorType(pToken);
			if (dwType == etexec::ETP_NONE)
			{
				// 继续枚举
				return 0;
			}

			ks_stdptr<IUnknown> spUnk;
			m_pSheet->GetExtDataItem(edSheetHyperlinks, &spUnk);
			ASSERT(spUnk != NULL); // excelrw没有正确处理
			if (spUnk == NULL)
			{
				return 0;
			}

			ks_stdptr<IKHyperlinks> spKHLs;
			VS(spUnk->QueryInterface(IID_IKHyperlinks, (void**)&spKHLs));
			ks_stdptr<IKHyperlink> spKHL;
			if (S_OK == spKHLs->HasHyperLinkWithRuns(row, col, &spKHL) && nullptr != spKHL)
			{
				ks_stdptr<IHyperlinkRuns> spRuns;
				spKHL->GetRuns(&spRuns);
				if (spRuns)
				{
					UINT count = spRuns->GetRunsCount();
					for (UINT i = 0; i < count; ++i)
					{
						IHyperlinkRun* run = spRuns->GetRun(i);
						if (run)
						{
							ks_stdptr<IHyperlinkRunAddress> address(run->GetProperty());
							ASSERT(address); // only support address now
							if (address)
							{
								LINKRUNSTYPE type = address->GetLinkRunsType();
								if (LRT_Mention == type)
								{
									*m_bFileLink = true;
									return 1;
								}
							}
						}
					}

				}
			}
			return 0;
		}
	};
	std::chrono::steady_clock::time_point startTime = std::chrono::steady_clock::now();
	m_coverShape = false;
	bool bFileLink = false;
	acpt->addInt32("copyId", param.field_int32("copyId"));
	ks_stdptr<IETPersist> spPersist;
	spPersist = m_wwb->GetCoreApp()->GetAppPersist()->GetPersist();
	if(spPersist == NULL)
		return E_FAIL;

	IDX sheetIdx = GetSheetIdx(param, ctx);
	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if(!pBook)
	{
		WOLOG_INFO << "[QueryRangeCopyBase::execCopy] pBook empty!!!";
		return E_FAIL;
	}
	if (!wo::util::IsValidSheetIdx(pBook, sheetIdx))
	{
		WOLOG_INFO << "[QueryRangeCopyBase::execCopy] invalid sheetidx " << sheetIdx;
		return E_FAIL;
	}

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);

	ks_stdptr<IRangeInfo> host;
	if(bMultiCopy)
	{
		std::vector<RANGE> rgVec;
		ReadRangesInl(m_wwb->GetBMP(), GetSheetIdx(param, ctx), param, rgVec, "srcRanges");
		if (rgVec.empty())
			return E_FAIL;

		for (UINT i = 0; i < rgVec.size(); ++i)
		{
			if (!rgVec[i].IsValid())
			{
				WOLOG_INFO << "[QueryRangeCopyBase::execCopy] invalid RANGE!!!";
				return E_FAIL;
			}

			if (ctx->getProtectionCtx()->isRangeHasHidden(rgVec[i]))
			{
				return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
			}
			if (!bFileLink)
			{
				et_sdptr<ISheetEnum> spSheetEnum;
				spSheet->CreateEnum(&spSheetEnum);
				CellValueAcpt cellValueAcpt(&bFileLink, ctx, spSheet);
				spSheetEnum->EnumCellValue(rgVec[i], &cellValueAcpt);
			}

		}

		ks_stdptr<Range> spRange = m_wwb->CreateRangeObj(rgVec);
		if (spRange == nullptr)
			return E_FAIL;
		
		host = spRange;
	}
	else
	{
		RANGE rg = ReadRange(param);
		if (!rg.IsValid())
			return E_FAIL;

		if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
		{
			return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
		}

		host = m_wwb->CreateRangeObj(rg);

		et_sdptr<ISheetEnum> spSheetEnum;
		spSheet->CreateEnum(&spSheetEnum);
		CellValueAcpt cellValueAcpt(&bFileLink, ctx, spSheet);
		spSheetEnum->EnumCellValue(rg, &cellValueAcpt);
	}
	
	ks_stdptr<IAppCoreRange> spCoreRange;
	host->GetAppCoreRange(&spCoreRange);

	if (spCoreRange == nullptr)
		return WO_FAIL;

    ks_stdptr<IKAutoFilters> spFilters = spSheet->GetAutoFilters();
     bool hasUserFilterOn = false;
    if (spFilters != NULL)
    {
        PCWSTR filterId = ctx->getFilterContext()->GetID();
        ks_stdptr<IKAutoFilter> spFilter = spFilters->GetFilter(filterId);
        if (spFilter != NULL && spFilter->HasFilterOn())
            hasUserFilterOn = true;
    }

	range_helper::ranges rgs;
	// 判断一下, 如果在筛选模式下, 不要把隐藏的也给选上
	if (hasUserFilterOn || spCoreRange->IsRangeInFilterMode())
		spCoreRange->GetFilteredIRanges(FALSE, &rgs);
	else
		host->GetIRanges(&rgs, TRUE);


	COPYINFO	info = {0};
	IDX sheetid = INVALIDIDX;
	info.grbits.fCopy = 1;
	info.grbits.fCache  = 1;
	info.grbits.fWithObjs = 1;
	info.grbits.fNoEmptyShape = 1;

	spSheet->GetWoStake()->setCopyHtmlBreakSize(_kso_GetWoEtSettings() ? _kso_GetWoEtSettings()->GetHtmlCopyMaxLimitSize() : 2 * m_oneMillonSize);


	if (param.has("cloudPathPrefix"))
	{
		ks_wstring pathPrefix = param.field_str("cloudPathPrefix");
		ks_wstring pathPostfix;
		if (param.has("cloudPathPostfix"))
			pathPostfix = param.field_str("cloudPathPostfix");
		ctx->InitCopyShapeCtx(pathPrefix, pathPostfix);

		ks_stdptr<IKRanges> spRanges;
		host->GetIRanges(&spRanges);
		if(!spRanges)
			return E_FAIL;

		PrepareCopyContext(sheetIdx, spRanges, ctx, acpt);
	}

    HRESULT hr;
    bool bGenerateHtml = true;
    if (param.has("shouldGenerateHtml"))
    {
        bGenerateHtml = param.field_bool("shouldGenerateHtml");
    }
    WOLOG_INFO << "[range_copy] bGenerateHtml: " << bGenerateHtml;
	info.extraTime = (std::chrono::steady_clock::now() - startTime) / std::chrono::milliseconds(1);
    if (_kso_GetWoEtSettings()->IsEnableNewBatchCopy())
        hr = BatchCopy(spPersist, sheetIdx, rgs, &info, param, ctx, bMultiCopy, bGenerateHtml, acpt);
    else
        hr = NormalCopy(spPersist, sheetIdx, rgs, &info, param, ctx, bMultiCopy, bGenerateHtml, acpt);

	acpt->addBool("coverShape", m_coverShape);
	acpt->addBool("bFileLink", bFileLink);
	acpt->addBool("hasHidden", m_wwb->isBookProtectedWithHiddenProperty());
	CollectSecDocInfo(ctx, "copy", GetTag());
	return hr;
}

void QueryRangeCopyBase::PrepareCopyContext(IDX sheetIdx, IKRanges* pRanges, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	// 导出图片路径为ks3路径
	ks_stdptr<ISheet> spSheet;
	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->GetSheet(sheetIdx, &spSheet);

	IKWorksheet *pWs = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	ks_stdptr<IKShapeRange> ptrShapeRange;
	if (S_OK == GetRangeCoverShapes(spSheet, pWs->GetDrawingCanvas(), pRanges, &ptrShapeRange))
	{
		LONG count = 0;
		ptrShapeRange->GetShapeCount(&count);
		if (count > 0) m_coverShape = true;
		util::prepareCoverShapes(sheetIdx, ptrShapeRange, ctx, acpt);
	}
		
}

HRESULT QueryRangeCopyBase::NormalCopy(IETPersist* pPersist, IDX sheetid, IUnknown* pRanges, COPYINFO* pCopyInfo, const VarObj& param, KEtRevisionContext* ctx, bool bMultiCopy, bool bGenerateHtml, ISerialAcceptor* acpt)
{
    ks_stdptr<ISheet> spSheet;
	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->GetSheet(sheetid, &spSheet);

    CopyTypeInfo txtCopyInfo = {0};
    QTime time;
	time.start();
	QString txt;
	HRESULT text_hr = E_FAIL;
    bool hasBreak = false;
	text_hr = Copy(pPersist, sheetid, pRanges, pCopyInfo, kso_cb_text_format, txt, hasBreak);
    txtCopyInfo.lostTime =  time.elapsed();
	if (S_OK == text_hr)
	{
		if (spSheet->GetWoStake()->getCopyHtmlBreakRow() > 0)
			txtCopyInfo.isBreak = TRUE;

        txtCopyInfo.exportCells = spSheet->GetWoStake()->getCopyCellsCount();
        spSheet->GetWoStake()->setCopyCellsCount(0);
		spSheet->GetWoStake()->setCopyHtmlBreakRow(-1);
	}
    int totalTime = txtCopyInfo.lostTime;

    CopyTypeInfo htmlCopyInfo = {0};
    QString html;
	HRESULT html_hr = E_FAIL;
    if (bGenerateHtml)
    {
        time.restart();
        hasBreak = false;
        html_hr = Copy(pPersist, sheetid, pRanges, pCopyInfo, kso_cb_html_format, html, hasBreak);
        htmlCopyInfo.lostTime =  time.elapsed();
        int nBreakRow = -1;
        bool bNeedDelay = true;
        if (S_OK == html_hr)
        {
            nBreakRow = spSheet->GetWoStake()->getCopyHtmlBreakRow();
            if (nBreakRow == -1)
                bNeedDelay = false;
            else
                htmlCopyInfo.isBreak = TRUE;

            htmlCopyInfo.exportCells = spSheet->GetWoStake()->getCopyCellsCount();
            spSheet->GetWoStake()->setCopyCellsCount(0);
            spSheet->GetWoStake()->setCopyHtmlBreakRow(-1);
        }

        if (gs_callback && gs_callback->setNeedDelayCopy)
		    gs_callback->setNeedDelayCopy(bNeedDelay? 1: 0);

        totalTime += htmlCopyInfo.lostTime;
    }

	if(FAILED(text_hr) && FAILED(html_hr))
		return E_FAIL;

	acpt->addKey("mineData");
	acpt->beginStruct();
	if (S_OK == text_hr)
		acpt->addString(kso_cb_text_format, krt::utf16(txt));

	if (S_OK == html_hr)
		acpt->addString(kso_cb_html_format, krt::utf16(html));
	acpt->endStruct();
	acpt->addBool("hasBreak", txtCopyInfo.isBreak || htmlCopyInfo.isBreak);

    acpt->addKey("copy_core_time");
	acpt->beginStruct();
    acpt->addInt32("copy_core_txt", txtCopyInfo.lostTime);
    acpt->addInt32("copy_core_html", htmlCopyInfo.lostTime);
    acpt->addInt32("copy_core_total", totalTime);
    acpt->endStruct();

    CollectCopyInfos(param, ctx, bMultiCopy, txtCopyInfo, htmlCopyInfo, totalTime);

    if (param.has("cloudPathPrefix"))
	{
		ctx->ResetCopyShapeCtx(S_OK == html_hr ? acpt : nullptr);
	}

	IEtEventTracking* pEtEventTracking = pBook->LeakWorkspace()->GetEventTracking();
	if (pEtEventTracking)
	{
		KComVariant varVal(pCopyInfo->extraTime);
		pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_EXTRA_TIME, varVal);
		pEtEventTracking->GetCollectInfo()->CollectBookInfoIncrease(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_TOTAL_TIME_CONSUMING, pCopyInfo->extraTime);
		pEtEventTracking->GetManager()->SetFileId(ctx->getFileId());
		pEtEventTracking->SendInfoAfterCopy();
	}
    return S_OK;
}

HRESULT QueryRangeCopyBase::BatchCopy(IETPersist* pPersist, IDX sheetid, IUnknown* pRanges, COPYINFO* pCopyInfo, const VarObj& param, KEtRevisionContext* ctx, bool bMultiCopy, bool bGenerateHtml, ISerialAcceptor* acpt)
{
    ks_stdptr<ISheet> spSheet;
	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->GetSheet(sheetid, &spSheet);

    QTime time;
    time.start();

    std::vector<CopyTypeInfo*> ctis;
    BOOL bApplyCondFmt;

    CopyTypeInfo txtCti = {0};
    txtCti.format = kso_cb_text_format;
    txtCti.applyFormat = FALSE;
    STGMEDIUM txtMedium = {0};
    txtCti.medium = &txtMedium;
    ctis.push_back(&txtCti);
    bApplyCondFmt = FALSE;

    CopyTypeInfo htmlCti = {0};
	STGMEDIUM htmlMedium = {0};
    if (bGenerateHtml)
    {
        htmlCti.format = kso_cb_html_format;
        htmlCti.applyFormat = TRUE;
        htmlCti.medium = &htmlMedium;
        ctis.push_back(&htmlCti);
        bApplyCondFmt = TRUE;
    }

    bApplyCondFmt = _kso_GetWoEtSettings()->IsEnableCFApplyWhenCopy() ? bApplyCondFmt : FALSE; // 通过开关控制复制时是否应用条件格式
    pCopyInfo->grbits.fBatchCopy = 1;
    pCopyInfo->grbits.fApplyCondFmt = bApplyCondFmt ? 1 : 0;
    pCopyInfo->grbits.fWithTableStyle = 1;
    HRESULT hr = pPersist->WoBatchCopy(pBook, sheetid, pRanges, pCopyInfo, ctis.data(), ctis.size());

    int totalTime = time.elapsed();
    if (S_OK == hr)
    {   
        BOOL hasBreak = FALSE;
        acpt->addKey("mineData");
        acpt->beginStruct();

        for (CopyTypeInfo* pCti : ctis)
        {
            if (!pCti->successed)
                continue;
            if (pCti->isBreak) 
                hasBreak = TRUE;
            ks_stdptr<IStream> spStrm;
            spStrm.attach(pCti->medium->pstm);
            KsoDataStream* dataStream = static_cast<KsoDataStream*>(pCti->medium->pstm);
            Q_ASSERT(pCti->medium->tymed == TYMED_ISTREAM && dataStream != NULL);
            QByteArray arr = dataStream->getByteArray();
            if (0 == strncmp(kso_cb_html_format,pCti->format, ks_strnlen_s(kso_cb_html_format, 15)))
                acpt->addString(pCti->format, krt::utf16(QString::fromUtf8(arr)));
            else
                acpt->addString(pCti->format, reinterpret_cast<PCWSTR>(arr.data()));
        }

        acpt->endStruct();
        acpt->addBool("hasBreak", hasBreak);

        acpt->addKey("copy_core_time");
        acpt->beginStruct();
        acpt->addInt32("copy_core_txt", txtCti.lostTime);
        acpt->addInt32("copy_core_html", htmlCti.lostTime);
        acpt->addInt32("copy_core_total", totalTime);
        acpt->endStruct();

        if (gs_callback && gs_callback->setNeedDelayCopy)
		    gs_callback->setNeedDelayCopy(hasBreak? 1: 0);
    }
    CollectCopyInfos(param, ctx, bMultiCopy, txtCti, htmlCti, totalTime);

    if (param.has("cloudPathPrefix"))
	{
		ctx->ResetCopyShapeCtx(S_OK == hr ? acpt : nullptr);
	}

    return hr;
}


HRESULT QueryRangeCopyBase::Copy(IETPersist* pPersist, IDX sheetid, IUnknown* pRanges, const COPYINFO* pCopyInfo,
	const char* format, QString& text, bool &hasBreak)
{
	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	return Copy_Helper::Copy(pPersist, sheetid, pRanges, pCopyInfo, format, text, hasBreak, pBook);

}

///////////////////////////////////////////////

QueryRangCopy::QueryRangCopy(KEtWorkbook* wwb)
	: QueryRangeCopyBase(wwb, __X("range.copy"))
{

}

HRESULT QueryRangCopy::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	return execCopy(param, ctx, acpt, false);
}

QueryMultiRangCopy::QueryMultiRangCopy(KEtWorkbook* wwb)
	: QueryRangeCopyBase(wwb, __X("range.multiCopy"))
{

}

HRESULT QueryMultiRangCopy::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	return execCopy(param, ctx, acpt, true);
}

/////////////////////////////////////////////

QueryPicUploadProgress::QueryPicUploadProgress(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.getBatchUploadPictureProgress"))
{
}

HRESULT QueryPicUploadProgress::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    if (param.has("callBackId"))
    {
        acpt->addUint32("callBackId" ,param.field_uint32("callBackId"));
    }
    PictureUploadHelper* helper = m_wwb->GetPictureUploadHelper();
    helper->queryResponse(acpt);
    return S_OK;
}

QueryConvertStatus::QueryConvertStatus(KEtWorkbook* wb)
  : EtQueryExecBase(wb, __X("query.getConvertStatus"))
{
}

HRESULT QueryConvertStatus::Exec(const VarObj& param, KEtRevisionContext* ctx,
                                 ISerialAcceptor* acpt)
{
    PictureUploadHelper* helper = m_wwb->GetPictureUploadHelper();
    acpt->addBool("status", false);
    return S_OK;
}

QueryWebExtension::QueryWebExtension(KEtWorkbook* wb) : EtQueryExecBase(wb, __X("query.getWebExtension"))
{
}

HRESULT QueryWebExtension::HasHiddenContent(IKDataRange* pDataRange, KEtRevisionContext* pCtx)
{
    PCWSTR context = pDataRange->GetContext();
    ASSERT(context != nullptr);
    if (xstrlen(context) && context[0] == __Xc('='))
        ++context;
    range_helper::ranges rgs;
    CS_COMPILE_FLAGS ccf = cpfAllowName | cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo | cpfForbidCrossBook;
    CS_COMPILE_PARAM ccp(ccf, 0, 0, 0);
    IBookOp* pBookOp = m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator();
    HRESULT hr = pBookOp->CompileRange(context, ccp, &rgs, croNeedCalc);
    if (FAILED(hr))
        return hr;
    return pCtx->getProtectionCtx()->isRangeHasHidden(rgs, true) ? S_OK : S_FALSE;
}

HRESULT QueryWebExtension::Exec(const VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* acpt)
{
	UINT stId = param.field_int32("sheetStId");
	IDX sheetIdx = INVALIDIDX;
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	pWorkbook->GetBook()->STSheetToRTSheet(stId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_INVALIDARG;
	_Worksheet* pWorksheet = static_cast<_Worksheet*>(pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx));
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!(pSheet->IsDbDashBoardSheet()))
		return E_FAIL;

	IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetWebExtensionMgr();
	if (!param.has("webExtensionKey") || !pWebExtensionMgr)
		return E_FAIL;

	PCWSTR webExtensionKey = param.field_str("webExtensionKey");
	IKWebExtension* pWebExtension = pWebExtensionMgr->FindWebExtension(sheetIdx, webExtensionKey);
	if (!pWebExtension)
        return E_FAIL;

	int webExtType = pWebExtension->GetWebShapeType();
	switch (webExtType)
	{
		case WET_DbDataSource:
		{
			return SerializeWebChart(param, pWebExtension, pCtx, acpt);
		}
		case WET_DbRichText:
		{
			return SerializeRichText(pWebExtension, acpt);
		}
		case WET_DbPlugin:
		default:
			ASSERT(FALSE);
			break;
	}

	return S_OK;
}

HRESULT QueryWebExtension::SerializeWebChart(const VarObj& param,IKWebExtension* pWebExtension, KEtRevisionContext* pCtx, ISerialAcceptor* acpt)
{
	ks_stdptr<IWorksheetChart> spDashboardChart = pWebExtension;
    if (!spDashboardChart)
        return E_FAIL;

    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    ks_castptr<IKWorkbookDataSource> spDataSource = spDataSourceHost->GetDataSource();
    if (!spDataSource)
        return E_FAIL;

	VS(spDataSource->GetRangeCount() == 1);
	ks_stdptr<IKDataRangeEnum> spEnum;
	HRESULT hr = spDataSource->GetDataRangeEnumerator(&spEnum);
	if (FAILED(hr) || !spEnum || spEnum->Reset() != S_OK)
		return E_FAIL;

	ks_castptr<IKDataRange> pDataRange = spEnum->GetDataRange();
	if (!pDataRange)
		return E_FAIL;

	hr = HasHiddenContent(pDataRange, pCtx);
	if (FAILED(hr))
		return hr;

	if (hr == S_FALSE)
	{
		binary_wo::VarObj lang = param.get_s("lang");
		WebExtensionDataSourceType dataSourceType = spDataSource->GetType();
		LPCWSTR configStr = nullptr;
		pWebExtension->GetProperty(__X("chart-info"), &configStr);
		KWebChartConfig chartConfig(configStr);
		spDashboardChart->SerializeResult(lang, acpt, pDataRange, dataSourceType, chartConfig.GetChartType());
	}
	else
	{
		acpt->addString("type", __X("DataIsProtected"));
	}

	return S_OK;
}

HRESULT QueryWebExtension::SerializeRichText(IKWebExtension* pWebExtension, ISerialAcceptor* acpt)
{
	LPCWSTR propertyValue = nullptr;
	HRESULT hr = pWebExtension->GetProperty(__X("richTextContent"), &propertyValue);
	if(SUCCEEDED(hr) && propertyValue)
		acpt->addString("richTextContent", propertyValue);
	else
		acpt->addString("richTextContent", __X(""));

	return S_OK;
}

QueryFuncInfo::QueryFuncInfo(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("getFuncInfo"))
{
}

HRESULT QueryFuncInfo::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IKEtFunctionMgr* pFuncMgr = m_wwb->GetCoreApp()->GetFunctionMgr();
	VarObj ids = param.get("ids");
	acpt->addKey("infos");
	acpt->beginArray();
	for (int i = 0, cnt = ids.arrayLength(); i < cnt; ++i)
	{
		int id = ids.item_int32(i);
		IKEtFunction* pFunc = pFuncMgr->GetFunction(id);
		acpt->beginStruct();
		acpt->addInt32("id", id);
		acpt->addKey("paramsInfo");
		acpt->beginArray();
		int nParam = pFunc ? pFunc->GetParamCount() : 0;
		for (int j = 1; j <= nParam; ++j)
		{
			acpt->beginStruct();
			PCWSTR tmp = pFunc->GetFuncParaInfo(j);
			acpt->addString("info", tmp ? tmp : __X(""));
			tmp = pFunc->GetFuncParaDesc(j);
			acpt->addString("desc", tmp ? tmp : __X(""));
			tmp = pFunc->GetFuncParaType(j);
			acpt->addString("type", tmp ? tmp : __X(""));
			acpt->endStruct();
		}
		acpt->endArray();
		acpt->endStruct();
	}
	acpt->endArray();
	return S_OK;
}

QuerySortExpand::QuerySortExpand(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.sortExpand"))
{
}

HRESULT QuerySortExpand::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	bool bExpandSucc = false;
	bool bTipExpand = false;
	bool bSortWithoutExpand = true;
	EnableProtectCompileFmla enableClFlmGuard;

	RANGE rgSel = ReadRange(param);
	if (!rgSel.IsValid()) return S_FALSE;
	ks_stdptr<etoldapi::Range> spRangeSel = m_wwb->CreateRangeObj(rgSel);
	if (!spRangeSel) return S_FALSE;


	ks_stdptr<_Worksheet> ptrWorksheet;
	spRangeSel->get_Worksheet(&ptrWorksheet);

	const RANGE* pSortRange = &rgSel;
	KCOMPTR(IKRanges) ptrRgsSelectionOld;
	KCOMPTR(IKRanges) ptrRgsSelectionNew;

	if (PivotHelpers::HasPivotTableInRange(spRangeSel))
	{
		bExpandSucc = true;
		bTipExpand = false;
		bSortWithoutExpand = true;
	}
	else
	{
		ks_stdptr<etoldapi::Sort> ptrSort;
		bool bListObject = false;
		GetSortByRange(ptrWorksheet, rgSel, false, &ptrSort, &bListObject);

		_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&ptrRgsSelectionOld);
		ptrRgsSelectionOld->Append(0, rgSel);
		HRESULT hr = S_OK;
		if(bListObject)
			hr = ExpandRangeInListObject(ptrWorksheet, ptrSort, ptrRgsSelectionOld, &ptrRgsSelectionNew);
		else
			hr = ExpandRangeInWorksheet(ptrWorksheet, ptrSort, ptrRgsSelectionOld, true, &ptrRgsSelectionNew, bTipExpand, bSortWithoutExpand);

		if (SUCCEEDED(hr))
		{
			if (ptrRgsSelectionNew != NULL)
			{
				bExpandSucc = true;
				ptrRgsSelectionNew->GetItem(0, 0, &pSortRange);
			}
		}
	}

	RANGE rgSortArea(*pSortRange);

	acpt->addBool("expandSucc", bExpandSucc);
	acpt->addBool("tipExpand", bTipExpand);
	acpt->addBool("sortWithoutExpand", bSortWithoutExpand);

	acpt->addKey("rangeExpand");
	acpt->beginStruct();
	acpt->addUint32("rowFrom", rgSortArea.RowFrom());
	acpt->addUint32("rowTo", rgSortArea.RowTo());
	acpt->addUint32("colFrom", rgSortArea.ColFrom());
	acpt->addUint32("colTo", rgSortArea.ColTo());
	acpt->endStruct();

	return S_OK;
}

QueryShowConditionRules::QueryShowConditionRules(KEtWorkbook* wb)
	: QueryRangeShowConditionRules(wb, __X("sheet.showConditionFormats"))
{
}

HRESULT QueryShowConditionRules::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX idxSheet = -1;
	if (!param.has("sheetIdx"))
		return E_INVALIDARG;

	idxSheet = param.field_int32("sheetIdx");

	RANGE rg(m_wwb->GetBMP());
	rg.SetRowFromTo(0, m_wwb->GetBMP()->cntRows - 1);
	rg.SetColFromTo(0, m_wwb->GetBMP()->cntCols - 1);
	rg.SetSheetFromTo(idxSheet);
	if (!rg.IsValid())
	{
		return E_FAIL;
	}

	range_helper::ranges rgs = range_helper::ranges::create_instance();
	rgs->Append(0, rg);

	return QueryRangeShowConditionRules::ExecBy(idxSheet, rgs, acpt);
}

QueryApplyConditionFormat::QueryApplyConditionFormat(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("get_cells_condition_format"))
{
}

HRESULT QueryApplyConditionFormat::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	util::CFTimeStat cfTimeStat(m_wwb, __X("get_cells_condition_format_cnt"));
	ks_stdptr<IBook> ptrBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> pBookOp;
	ptrBook->GetOperator(&pBookOp);

	VarObj blocks = param.get_s("blocks");
	if (blocks.type() == binary_wo::typeInvalid)
		return E_FAIL;
	UINT64 cntCellsEverOld = 0;
	UINT64 cntCalcEverOld = 0;
	UINT64 cellsCalcOverThresholdEverOld = 0;
	ptrBook->GetWoStake()->GetConditionFormatStat(cntCellsEverOld, cntCalcEverOld, cellsCalcOverThresholdEverOld);

	acpt->addKey("blocks");
	acpt->beginArray();
	int blkCellsCnt = 0;
	for (int i = 0; i < blocks.arrayLength(); i++)
	{
		VarObj rg = blocks.at(i);
		IDX sheetIdx = GetSheetIdx(rg, ctx);
		if (sheetIdx == INVALIDIDX)
			continue;
		ROW rowFrom = rg.field_int32("rowFrom");
		ROW rowTo = rg.field_int32("rowTo");
		COL colFrom = rg.field_int32("colFrom");
		COL colTo = rg.field_int32("colTo");

		RANGE range(m_wwb->GetBMP());
		range.SetSheetFromTo(sheetIdx);
		range.SetRowFromTo(rowFrom, rowTo);
		range.SetColFromTo(colFrom, colTo);
		if (!range.IsValid() 
			|| !m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx))
		{
			continue;
		}
		blkCellsCnt += range.CellCount();

		acpt->beginStruct();
		{
			ConditionFormatCache *pCfCache = m_wwb->getCfCache();
			if (pCfCache->IsEnabled())
			{
				pCfCache->PrepareCfData(m_wwb->GetCoreWorkbook(), range);
				ApplyConditionalFormatByCache(acpt, range, m_wwb->GetCoreWorkbook(), pCfCache);
				pCfCache->OptmiseCache();
			}
			else
			{
				ApplyConditionalFormat(acpt, range, ptrBook);
			}
			acpt->addInt32("sheetIdx", sheetIdx);
			acpt->addInt32("rowFrom", rowFrom);
			acpt->addInt32("rowTo", rowTo);
			acpt->addInt32("colFrom", colFrom);
			acpt->addInt32("colTo", colTo);
		}
		acpt->endStruct();
	}
	acpt->endArray();
	
	UINT64 cntCellsEver = 0;
	UINT64 cntCalcEver = 0;
	UINT64 cellsCalcOverThresholdEver = 0;
	ptrBook->GetWoStake()->GetConditionFormatStat(cntCellsEver, cntCalcEver, cellsCalcOverThresholdEver);
	WOLOG_DEBUG << "[CF_Applier] cellsCnt: " << cntCellsEver - cntCellsEverOld << " calcCnt: " << cntCalcEver - cntCalcEverOld
				<< " calcOverThresholdCnt: " << cellsCalcOverThresholdEver - cellsCalcOverThresholdEverOld;
	cfTimeStat.setStatCount(blkCellsCnt, cntCellsEver - cntCellsEverOld, cntCalcEver - cntCalcEverOld, 
		cellsCalcOverThresholdEver - cellsCalcOverThresholdEverOld);
	return S_OK;
}

// 单元格数据是否为空的检查
class KCellValueAcpt : public ICellValueAcpt
{
	STDIMP_(INT) Do(ROW, COL, const_token_ptr pToken)
	{
		if (pToken)
			return 1;//停止枚举
		return 0;//继续枚举
	};
};

void colListToVariant(std::vector<long>& checkColumns, VARIANT& colList)
{
	//1 初始化多维数组;
	SAFEARRAYBOUND rgsabound[1];
	rgsabound[0].cElements = checkColumns.size(); //元素个数;
	rgsabound[0].lLbound = 0;//下标;
	SAFEARRAY *pSa = SafeArrayCreate(VT_VARIANT, 1, rgsabound);

	//2 放置元素;
	for (SIZE32 i = 0; i < (int)checkColumns.size(); i++)
	{
		KComVariant val(checkColumns.at(i), VT_I4);
		SafeArrayPutElement(pSa, &i, &val);
	}

	//3 将数组封装成 VARIANT;
	V_VT(&colList) = VT_ARRAY | VT_VARIANT;
	V_ARRAY(&colList) = pSa;
}

/////////////////////////////////////////////////////////////////////////
QueryGetRemoveDuplicate::QueryGetRemoveDuplicate(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("get_remove_duplicate"))
{
}

HRESULT QueryGetRemoveDuplicate::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	HRESULT hr = S_OK;
	ks_stdptr<etoldapi::Range> ptrSelRange = CreateRangeObj(param);
	if (!ptrSelRange || !param.has("activeCellRow") || !param.has("activeCellCol") || !param.has("isHeader"))
		return E_FAIL;

	CELL activeCell = { 0 };
	activeCell.row = param.field_int32("activeCellRow");
	activeCell.col = param.field_int32("activeCellCol");
	IDX idxSheet = param.field_int32("sheetIdx");

	ks_stdptr<_Worksheet> ptrWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(idxSheet);
	ks_stdptr<ISheet> ptrSheet = ptrWorksheet->GetSheet();

	RANGE rgOriginal(ptrSheet->GetBMP()), rgExpandRange(ptrSheet->GetBMP());
	{
		ks_stdptr<IKRanges> ptrRgsSelectionOld;
		// 保留Selection的Ranges，删除重复项完成之后重新选中
		app_helper::GetIRanges(ptrSelRange, &ptrRgsSelectionOld);

		UINT nCount = 0;
		ptrRgsSelectionOld->GetCount(&nCount);
		ASSERT(nCount == 1);

		const RANGE* pRange = NULL;
		INT iBook = -1;
		ptrRgsSelectionOld->GetItem(0, &iBook, &pRange);
		rgExpandRange = *pRange;
		rgOriginal = *pRange;
	}

	bool bHeader = param.field_bool("isHeader");
	BOOL bShowListObjTitle = FALSE;
	BOOL bListObjRg = GetListObjRange(ptrWorksheet, rgExpandRange, rgExpandRange, &bShowListObjTitle);
	CELL cellSingle = { 0 };
	if (app_helper::IsSingleCell(ptrSelRange, &cellSingle, NULL))	//单个单元格
	{
		if (!bListObjRg)
		{//扩展区域
			appcore_helper::GetContinualRangeMaxEx(ptrSheet, cellSingle.row, cellSingle.col, TRUE, &rgExpandRange);
			ptrWorksheet->GetUsedInRange(&rgExpandRange);
		}
		GetRangeDuplicateInfo(ptrWorksheet, rgExpandRange, activeCell, FALSE, bListObjRg, bHeader, acpt);
	}
	else // 不是单个单元格
	{
		// 单行列需要扩展
		if (!bListObjRg && (rgOriginal.ColFrom() == rgOriginal.ColTo() || rgOriginal.RowFrom() == rgOriginal.RowTo()))
		{
			//得到扩展区域
			appcore_helper::GetContinualRangeMaxEx(ptrSheet, rgOriginal, activeCell, TRUE, &rgExpandRange);
			if (!rgExpandRange.Compare(rgOriginal))
				GetRangeDuplicateInfo(ptrWorksheet, rgExpandRange, activeCell, TRUE, bListObjRg, bHeader, acpt);
		}
		GetRangeDuplicateInfo(ptrWorksheet, rgOriginal, activeCell, FALSE, bListObjRg, bHeader, acpt);
	}

	acpt->addInt32("sheetIdx", idxSheet);
	return S_OK;
}

void QueryGetRemoveDuplicate::GetRangeDuplicateInfo(_Worksheet* pWorksheet, RANGE rg, CELL activeCell, BOOL bExpand, bool bListObjRg, bool bHeader, ISerialAcceptor* acpt)
{
	ks_stdptr<ISheet> ptrSheet = pWorksheet->GetSheet();

	if (ptrSheet->GetBottom() < 0)
		return;

	if (bExpand)
		acpt->addKey("expand");
	else
		acpt->addKey("original");

	acpt->beginStruct();

	if (!bListObjRg)
	{
		RECT rc = { 0 };
		ptrSheet->CalcUsedScale(&rc);
		RANGE usedRange(ptrSheet->GetBMP());		//用户操作必须在usedRange范围内
		usedRange.SetSheetFromTo(rg.SheetFrom());
		usedRange.SetRowFromTo(0, rc.bottom);
		usedRange.SetColFromTo(0, rc.right);
		rg = usedRange.Intersect(rg);
		// 区域不合法: TX_RepeatWord_Invalid_Range
		if (!rg.IsValid() || rg.CellCount() > 1048576 * 4)
		{
			acpt->addBool("is_invalid_range", true);
			acpt->endStruct();
			return;
		}
	}

	ks_stdptr<IKRanges> ptrRgsSelectionNew;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&ptrRgsSelectionNew);
	ptrRgsSelectionNew->Append(alg::STREF_THIS_BOOK, rg);
	ks_stdptr<Range> ptrRange;
	pWorksheet->GetRangeByData(ptrRgsSelectionNew, &ptrRange);

	RANGE rgUnHeader(rg);
	if (bHeader && rgUnHeader.Height() > 1)
		rgUnHeader.SetRowFrom(rgUnHeader.RowFrom() + 1);

	if (appcore_helper::IsRowGroup(rgUnHeader, ptrSheet))
	{
		// 包含分组: TX_RepeatWord_Group_Range
		acpt->addBool("is_invalid_range", true);
		acpt->endStruct();
		return;
	}
	if (rgUnHeader.RowTo() == rgUnHeader.RowFrom())
	{
		// 区域不合法: TX_RepeatWord_Invalid_Range
		acpt->addBool("is_invalid_range", true);
		acpt->endStruct();
		return;
	}

	et_sdptr<ISheetEnum> spSheetEnum;
	ptrSheet->CreateEnum(&spSheetEnum);
	KCellValueAcpt cellValue;

	RANGE rgCheckNull(rg);
	if (IsNeedTrimTitleRow(pWorksheet, rg))
		rgCheckNull.SetRowFrom(rg.RowFrom() + 1);

	if (!spSheetEnum->EnumCellValue(rgCheckNull, &cellValue))
	{
		// 区域不合法: TX_RepeatWord_Invalid_Range
		acpt->addBool("is_invalid_range", true);
		acpt->endStruct();
		return;
	}

	long count = 0;
	ks_stdptr<Range> columns;
	ptrRange->get_EntireColumn(&columns);
	columns->get_Count(&count);

	QStringList headerList;
	std::vector<long> checkColumns;
	std::vector<COL> vecColPos;

	for (int i = 1; i <= count; ++i)
	{
		VARIANT varRowIndex;
		VARIANT varColIndex;
		VARIANT varTextValue;

		V_VT(&varRowIndex) = VT_I4;
		V_VT(&varColIndex) = VT_I4;
		V_INT(&varRowIndex) = 1;
		V_INT(&varColIndex) = i;

		//获取选中区域的第一行的内容
		ptrRange->get_Item(varRowIndex, varColIndex, &varTextValue);
		ks_stdptr<Range> spRange = V_DISPATCH(&varTextValue);		//把IKCoreObject 类型 转变成Range， 然后就可以用get_Text获取到内容
		VariantClear(&varTextValue);
		ks_bstr bstr;
		if (spRange != NULL)
			spRange->get_Text(&bstr);
		QString strHeader = QString::fromUtf16(bstr);

		//获取表头
		long column = 0;
		ptrRange->get_Column(&column);
		vecColPos.push_back(column + i - 2);
		headerList.push_back(strHeader);
		checkColumns.push_back(i);
	}

	XlYesNoGuess isHeader = xlGuess;
	isHeader = bHeader ? xlYes : xlNo;
	KComVariant vtListArray;
	colListToVariant(checkColumns, vtListArray);

	long duplicateCnt = 0, uniqueCnt = 0;
	ptrRange->DuplicatesCount(vtListArray, isHeader, &duplicateCnt, &uniqueCnt);

	acpt->addKey("range");
	acpt->beginStruct();
	WriteRangeObj(acpt, ptrRange);
	acpt->endStruct();
	acpt->addInt32("duplicates_count", duplicateCnt);
	acpt->addInt32("unique_count", uniqueCnt);

	acpt->addKey("columns");
	acpt->beginArray();
	for (int i = 0; i < vecColPos.size(); i++)
	{
		acpt->beginStruct();
		acpt->addInt32("column_idx", i + 1);
		acpt->addInt32("col", vecColPos[i]);
		acpt->addString("header_name", krt::utf16(headerList[i]));
		acpt->endStruct();
	}
	acpt->endArray();

	acpt->endStruct();
}

QString QueryGetRemoveDuplicate::getColumnName(int nCol)
{
	ks_castptr<IKEtApplication> pEtApp = m_wwb->GetCoreApp();
	int nMaxCols = pEtApp->GetActiveWorksheet()->GetSheet()->GetBMP()->cntCols - 1;

	if (nCol < 0 || nCol > nMaxCols)
		return QString("");

	ETReferenceStyle etRs = etA1;
	ks_stdptr<_Application> spApp = pEtApp.get();
	if (spApp)
		spApp->get_ReferenceStyle(&etRs);
	if (etR1C1 == etRs)
		return QString::number(nCol + 1);

	WCHAR wszColName[MAX_LOCALE_COL_A1REF_STR_LEN]; wszColName[0] = 0;
	ColumnIndex_Num2Str(nCol, wszColName, countof(wszColName));
	return QString::fromUtf16(wszColName);
}

BOOL QueryGetRemoveDuplicate::GetListObjRange(_Worksheet* pSheet, const RANGE& rg, RANGE& rgNew, BOOL* pbShowTitle)
{
	ASSERT(pSheet);
	ks_stdptr<ListObject> spListObj;
	if (IsRgInListObj(pSheet, rg, &spListObj) && spListObj)
	{
		spListObj->get_DataBodyRange(&rgNew);
		VARIANT_BOOL bShowTitle = VARIANT_FALSE;
		spListObj->get_ShowHeaders(&bShowTitle);
		if (VARIANT_TRUE == bShowTitle)
			rgNew.SetRowFrom(rgNew.RowFrom() - 1);

		if (pbShowTitle)
			*pbShowTitle = (VARIANT_TRUE == bShowTitle) ? TRUE : FALSE;
		return TRUE;
	}
	return FALSE;
}

BOOL QueryGetRemoveDuplicate::IsNeedTrimTitleRow(_Worksheet* pSheet, const RANGE& rg)
{
	ks_stdptr<ListObject> spListObj;
	if (!IsRgInListObj(pSheet, rg, &spListObj) || !spListObj)
	 	return FALSE;
	VARIANT_BOOL bShowTitle = VARIANT_FALSE;
	spListObj->get_ShowHeaders(&bShowTitle);
	return (VARIANT_TRUE == bShowTitle) ? TRUE : FALSE;
}

BOOL QueryGetRemoveDuplicate::IsRgInListObj(etoldapi::_Worksheet* pSheet, const RANGE& rgWork, etoldapi::ListObject** ppListObj)
{
	ASSERT(pSheet && ppListObj);
	ks_stdptr<ListObjects> spListObjs;
	if (FAILED(pSheet->get_ListObjects(&spListObjs)) || !spListObjs)
		return FALSE;

	if (!spListObjs->FindFirstItem(rgWork, ppListObj) || !(*ppListObj))
		return FALSE;

	RANGE rg(rgWork.GetBMP());
	(*ppListObj)->get_Range(&rg);
	return rg.Contain(rgWork);
}

/////////////////////////////////////////////////////////////////////////
QueryDuplicatesCount::QueryDuplicatesCount(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("duplicates_count"))
{
}

HRESULT QueryDuplicatesCount::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	VarObj columns = param.get_s("columns");
	if (columns.type() == binary_wo::typeInvalid)
		return E_FAIL;

	ks_stdptr<etoldapi::Range> ptrRange = CreateRangeObj(param);
	if (!ptrRange)
		return E_FAIL;

	std::vector<long> checkColumns;
	for (int i = 0; i < columns.arrayLength(); i++)
	{
		checkColumns.push_back(columns.at(i).value_int32());
	}

	if (!param.has("isHeader"))
		return E_FAIL;
	XlYesNoGuess isHeader = param.field_bool("isHeader") ? xlYes : xlNo;

	KComVariant vtListArray;
	colListToVariant(checkColumns, vtListArray);
	long m_duplicatesCnt = 0;
	long m_uniqueCnt = 0;
	ptrRange->DuplicatesCount(vtListArray, isHeader, &m_duplicatesCnt, &m_uniqueCnt);

	acpt->addInt32("duplicates_count", m_duplicatesCnt);
	acpt->addInt32("unique_count", m_uniqueCnt);
	return S_OK;
}



namespace {
	const kfc::nf::NF_FORMAT_PARAM* sGetNF_FORMAT_PARAM()
	{
		static BOOL s_bHas = FALSE;
		static kfc::nf::NF_FORMAT_PARAM s_NFFormatParam;
		if (FALSE == s_bHas)
		{
			s_NFFormatParam.dwOpt |= kfc::nf::nffo_DecmplForArea;
			s_NFFormatParam.dwOpt |= kfc::nf::nffo_FormatForArea;
			s_NFFormatParam.lLanguage = (0x0ffff & alg::_get_locale_id());

			if (!_kso_QueryFeatureState(kaf_et_local_num_fmt))
				s_NFFormatParam.dwOpt |= kfc::nf::nffo_IgnoreAreaCode;
			s_bHas = TRUE;
		}
		return &s_NFFormatParam;
	}
}

QueryNumfmt::QueryNumfmt(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("getNumFmt"))
{

}

HRESULT QueryNumfmt::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	VarObj items = param.get("items");
	if (param.has("callBackId"))
		acpt->addUint32("callBackId", param.field_uint32("callBackId")); //原样返回query的id前端用于区分

	acpt->addKey("items");
	acpt->beginArray();
	for (int i = 0, cnt = items.arrayLength(); i < cnt; ++i)
	{
		PCWSTR strNumfmt = items.item_str(i);
		kfc::nf::NFHANDLE hNF = NULL;
		if (FAILED(kfc::nf::NFCompile(strNumfmt, &hNF, sGetNF_FORMAT_PARAM())))
			continue;

		acpt->beginStruct();
		acpt->addString("str", strNumfmt);
		nf::_XNFSerialNFHandle(hNF, acpt);
		acpt->endStruct();

		kfc::nf::NFRelease(hNF);
	}
	acpt->endArray();
	return S_OK;
}

//拿一些text2col的预览信息
QueryText2ColPreView::QueryText2ColPreView(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.text2col.preview")){ }

HRESULT QueryText2ColPreView::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) {
	return wo::text2col::getPreview(m_wwb, param, acpt);
}

//检查text2col的目标区域有没有导致不能执行的错误
QueryText2ColCheck::QueryText2ColCheck(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.text2col.check")){ }

HRESULT QueryText2ColCheck::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) {
	return wo::text2col::text2colProc(m_wwb, (VarObj&)param, acpt, true, ctx);
}

QueryRangeSliceInfo::QueryRangeSliceInfo(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("book.rangeSlice.query"))
{
	
}

HRESULT QueryRangeSliceInfo::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) 
{
	RangeSliceHelper::KRangeSliceHelper* pHelper = m_wwb->GetRangeSliceHelper();
	if (!pHelper)
		return E_FAIL;

	WebStr queryType = param.has("queryType") ? param.field_str("queryType") : nullptr;
	if (!queryType)
		return E_FAIL;

	bool bSucceeded = true;
	if (xstrcmp(queryType, __X("sliceInfo")) == 0)
	{
		UINT32 readIndex = param.has("index") ? param.field_uint32("index") : 0;
		UINT32 readCount = param.has("count") ? param.field_uint32("count") : 1;
		QMap<int, QString> sliceInfo;
		bSucceeded = pHelper->getSliceInfo(readIndex, sliceInfo, readCount);
		acpt->addKey("sliceInfo");
		acpt->beginArray();
		for (auto it = sliceInfo.cbegin(); it != sliceInfo.cend(); ++it)
		{
			acpt->beginStruct();
			acpt->addUint32("index", it.key());
			acpt->addString("text", krt::utf16(it.value()));
			acpt->endStruct();
		}
		acpt->endArray();
		acpt->addUint32("totalCount", pHelper->getTotalCount());
		
	}
	else if (xstrcmp(queryType, __X("errorItems")) == 0 && param.has("errorItems"))
	{
		QMultiMap<std::pair<int, int>, std::pair<int, int>> errorPos; //<切片id-错误id> <错误起始位置-错误终止位置>
		binary_wo::VarObj objErrorItems = param.get("errorItems");
		for (int i = 0; i < objErrorItems.arrayLength_s(); ++i)
		{
			binary_wo::VarObj objErrorItem = objErrorItems.at(i);
			UINT32 index = objErrorItem.has("index") ? objErrorItem.field_uint32("index") : 0;
			UINT32 errorId = objErrorItem.has("errorId") ? objErrorItem.field_uint32("errorId") : 0;
			if (objErrorItem.has("pos"))
			{
				binary_wo::VarObj pos = objErrorItem.get("pos");
				if (pos.arrayLength_s() == 2)
					errorPos.insert({index, errorId}, {pos.item_uint32(0), pos.item_uint32(1)});
			}	
		}

		QVector<std::pair<int, RangeSliceHelper::SliceCellInfo>> res;
		pHelper->getErrorItemCells(errorPos, res);
		acpt->addKey("errorCells");
		acpt->beginArray();
		for (auto it = res.cbegin(); it != res.cend(); ++it)
		{
			acpt->beginStruct();
			acpt->addUint32("row", it->second.row);
			acpt->addUint32("col", it->second.col);
			acpt->addUint32("sheetIdx", it->second.sheetIdx);
			acpt->addUint32("errorId", it->first);
			acpt->addUint32("start", it->second.begin);
			acpt->addUint32("end", it->second.end);
			acpt->endStruct();
		}
		acpt->endArray();
	}

	acpt->addBool("ok", bSucceeded);
	return bSucceeded ? S_OK : E_FAIL;
}

/////////////////////////////////////QueryRangeCanAIFill///////////////////////////////////////////
QueryRangeCanAIFill::QueryRangeCanAIFill(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.checkCanAIFill"))
{
	
}

HRESULT QueryRangeCanAIFill::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) 
{
	const RANGE rg = ReadRange(param);
	if (!rg.IsValid())
		return S_FALSE;

	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX)
		return E_FAIL;

	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	if (!spWorkbook)
		return E_FAIL;

	ks_stdptr<ISheet> spSheet;
	spWorkbook->GetBook()->GetSheet(sheetIdx, &spSheet);

	if(app_helper::hasMergedCellInRange(spSheet, rg))
	{
		acpt->addString("AIFillErrorType", __X("SHOW_COVER_CONFIRM"));
		return S_FALSE;
	}

	bool hasEmptyCell = false;
	bool hasFormula = false;
	app_helper::hasEmptyCellAndFormula(spSheet, rg, &hasEmptyCell, &hasFormula);
	if(hasFormula)
	{
		acpt->addString("AIFillErrorType", __X("SHOW_COVER_CONFIRM"));
		return S_FALSE;
	}
	if(!hasEmptyCell)
	{
		acpt->addString("AIFillErrorType", __X("NO_EMPTY"));
		return S_FALSE;
	}

	ks_castptr<etoldapi::_Worksheet> cpWorksheet = spWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!cpWorksheet)
		return E_FAIL;

	ks_stdptr<Range> spRange;
	cpWorksheet->get_UsedRange(&spRange);
	range_helper::ranges rgs;
	app_helper::GetIRanges(spRange, &rgs);
	const RANGE *pUsedRg = nullptr;
	if (rgs.size() > 0)
	{
		pUsedRg = rgs.at(0).second;
		if (!pUsedRg)
			return S_FALSE;
	}

	RANGE extendRg(rg);
	extendRg.SetRowFromTo(pUsedRg->RowFrom(), pUsedRg->RowTo());
	if (!app_helper::IsRangeNotEmpty(spWorkbook->GetWorksheets()->GetSheetItem(sheetIdx), extendRg))
	{
		acpt->addString("AIFillErrorType", __X("NO_EXAMPLE"));
		return S_FALSE;
	}

	acpt->addString("AIFillErrorType", __X("COULD_FILL"));
	return S_OK;
}

/////////////////////////////////////QueryDataRowsOutOfRange///////////////////////////////////////////
QueryDataRowsOutOfRange::QueryDataRowsOutOfRange(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.getDataRowsOutOfRange"))
{
	
}

HRESULT QueryDataRowsOutOfRange::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) 
{
	const RANGE rg = ReadRange(param);
	if (!rg.IsValid())
		return S_FALSE;

	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX)
		return E_FAIL;
	ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);

	std::etvector<RANGE> vecRanges;
	if (app_helper::getExampleRanges(spSheet, m_wwb->GetCoreWorkbook(), rg, param.field_uint32("maxExampleRows"), vecRanges))
	{
		acpt->addKey("ExampleRangeOfCol");
		acpt->beginArray();
		for (const auto& it : vecRanges)
		{
			acpt->beginStruct();
			acpt->addUint32("rowFrom", it.RowFrom());
			acpt->addUint32("rowTo", it.RowTo());
			acpt->addUint32("colFrom", it.ColFrom());
			acpt->addUint32("colTo", it.ColTo());
			acpt->endStruct();
		}
		acpt->endArray();
		
		return S_OK;
	}

	return S_FALSE;
}

/////////////////////////////////////QueryRangeContents///////////////////////////////////////////
QueryRangeContents::QueryRangeContents(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.getRangeContents"))
{
	
}

HRESULT QueryRangeContents::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) 
{
	const RANGE rg = ReadRange(param);
	if (!rg.IsValid())
		return S_FALSE;

	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX)
		return S_FALSE;
	ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);
	if (!spSheet)
		return S_FALSE;

	ks_stdptr<IETStringTools> spTools;
	_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void **)&spTools);
	if (!spTools)
		return S_FALSE;

	spTools->SetEnv(spSheet);

	const int MAXCOUNT = 1000000;
	int cellCount = 0;
	acpt->addKey("Values");
	acpt->beginArray();
	for(ROW r = rg.RowFrom(); r <= rg.RowTo(); ++r)
	{
		acpt->beginArray();
		for (COL c = rg.ColFrom(); c <= rg.ColTo(); ++c)
		{
			ks_bstr bstrText;
			spTools->GetCellText(spSheet, r, c, nullptr, &bstrText, 0, nullptr);

			if (!bstrText.empty())
				acpt->addString(nullptr, bstrText);
			else
				acpt->addString(nullptr, __X(""));

			if(++cellCount >= MAXCOUNT)
				break;
		}
		acpt->endArray();

		if(cellCount >= MAXCOUNT)
			break;
	}
	acpt->endArray();

	return S_OK;
}

/////////////////////////////////////QuerySameDvRange///////////////////////////////////////////

namespace data_validation
{
// define in et_task_class.cpp
bool MapDvErrorCode(const int32_t &val, ks_wstring *res);

static void WriteQuerySameDvRangeResult(IKRanges* pRgs, ISerialAcceptor* acpt)
{
	UINT nRgCnt = 0;
	if (pRgs == NULL || FAILED(pRgs->GetCount(&nRgCnt)) ||  0 == nRgCnt)
		return;
	
	acpt->addKey("rgs");
	acpt->beginArray();
	for (UINT i = 0; i < nRgCnt; ++i)
	{
		const RANGE *pRg = NULL;
		pRgs->GetItem(i, NULL, &pRg);
		if (pRg)
		{
			acpt->beginStruct();
			WriteRANGE(*pRg, acpt);
			acpt->endStruct();
		}
	}

	acpt->endArray();
	acpt->addBool("ok", true);
}

static void WriteQuerySameDvRangeResult(const RANGE &rg, ISerialAcceptor* acpt)
{
	range_helper::ranges rgs = range_helper::ranges::create_instance();
	rgs.add(etexec::STREF_THIS_BOOK, rg);
	WriteQuerySameDvRangeResult(rgs, acpt);
}

} // namespace data_validation

QuerySameDvRange::QuerySameDvRange(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.getSameDvRange"))
{
}

HRESULT QuerySameDvRange::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	const RANGE range = ReadRange(param);
	if (!range.IsValid())
		return S_FALSE;

	ks_stdptr<etoldapi::Range> range_api = m_wwb->CreateRangeObj(range);
	if (range_api == NULL)
		return S_FALSE;

	KCOMPTR(Validation) ptrValidation;
	range_api->get_Validation(&ptrValidation);
	KCOMPTR(IValidationInfo) ptrValidationInfo = ptrValidation;
	if (ptrValidationInfo == NULL) {
		data_validation::WriteQuerySameDvRangeResult(range, acpt);
		return S_OK;
	}

	if (isThisBookHasHiddenProp(ctx))
	{
		data_validation::WriteQuerySameDvRangeResult(range, acpt);
		return S_OK;
	}

	ks_stdptr<IKRanges> spRanges;
	UINT range_count = 0;
	ptrValidationInfo->GetTheSameDVRanges(&spRanges);
	if (spRanges == NULL || FAILED(spRanges->GetCount(&range_count)) ||  0 == range_count)
		data_validation::WriteQuerySameDvRangeResult(range, acpt);
	else
		data_validation::WriteQuerySameDvRangeResult(spRanges, acpt);
	return S_OK;
}

/////////////////////////////////////QueryIsSameDvInRange///////////////////////////////////////////

QueryIsSameDvInRange::QueryIsSameDvInRange(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.isSameDVRange"))
{
}

HRESULT QueryIsSameDvInRange::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	RANGE range = ReadRange(param);
	if (!range.IsValid() || FAILED(AdjustRange(range)))
		return S_FALSE;

	ks_stdptr<etoldapi::Range> range_api = m_wwb->CreateRangeObj(range);
	if (range_api == NULL)
		return S_FALSE;

	KCOMPTR(Validation) ptrValidation;
	range_api->get_Validation(&ptrValidation);
	if (ptrValidation == NULL) {
		acpt->addBool("ok", true);
		return S_OK;
	}

	if (isThisBookHasHiddenProp(ctx))
	{
		acpt->addBool("ok", true);
		return S_OK;
	}

	bool res = true;
	ETDVType type;
	if (E_ET_SUSPENSIVE == ptrValidation->get_Type(&type)) {
		res = false;
	}

	acpt->addBool("ok", res);
	return S_OK;
}

HRESULT QueryIsSameDvInRange::AdjustRange(RANGE &rg)
{
    if (rg.RangeType() == rtCols)
        rg.SetRowFrom(rg.RowFrom() + 1);

    return rg.IsValid() ? S_OK : E_FAIL;
}


/////////////////////////////////////QueryCheckValidation///////////////////////////////////////////

QueryCheckValidation::QueryCheckValidation(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.checkValidation"))
{
}

HRESULT QueryCheckValidation::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	EnableProtectCompileFmla enableClFlmGuard;
	const RANGE range = ReadRange(param);
	if (!range.IsValid())
		return S_FALSE;

	ks_stdptr<etoldapi::Range> range_api = m_wwb->CreateRangeObj(range);
	if (range_api == NULL)
		return S_FALSE;

	ks_bstr formula(param.field_str("inputFormula"));
	if (formula == NULL)
		return S_FALSE;

	KCOMPTR(Validation) ptrValidation;
	range_api->get_Validation(&ptrValidation);
	KCOMPTR(IValidationInfo) ptrValidationInfo = ptrValidation;
	if (ptrValidationInfo == NULL) {
		acpt->addBool("isValid", true);
		acpt->addKey("cell");
		acpt->beginStruct();
		acpt->addInt32("row", range.RowFrom());
		acpt->addInt32("col", range.ColFrom());
		acpt->endStruct();
		return S_OK;
	}

	if (param.has("supBooksCtx"))
	{
		ctx->getSupBookCtx()->reset(param.get_s("supBooksCtx"));
	}

	bool isValid = false;
		// formula calculation needs the content of the cell which set by user but not accept now to calculate the formula
	APP_BeginUndoTrans(m_wwb->GetCoreWorkbook(), FALSE, NULL);
	HRESULT hr = range_api->put_Formula(formula);
	hr = ptrValidationInfo->IsValidInput();
	isValid = (hr == S_OK);
	APP_EndUndoTRANS(E_FAIL, FALSE, FALSE); // just check, rollback it. see edit_save.cpp CheckValidation

	//printf("isValid: %d\n", isValid);
	acpt->addBool("isValid", isValid);
	acpt->addKey("cell");
	acpt->beginStruct();
	acpt->addInt32("row", range.RowFrom());
	acpt->addInt32("col", range.ColFrom());
	acpt->endStruct();
	return S_OK;
}

/////////////////////////////////////QueryDvCustomList///////////////////////////////////////////

struct EtVectorBSTRGuard
{
	explicit EtVectorBSTRGuard(const std::etvector<BSTR> &vecBstr) : m_vecBstr(vecBstr)
	{
	}

	~EtVectorBSTRGuard()
	{
		for (size_t i = 0; i < m_vecBstr.size(); ++i)
		{
			::SysFreeString(m_vecBstr.at(i));
		}
	}

private:
	EtVectorBSTRGuard(const EtVectorBSTRGuard &);
	EtVectorBSTRGuard &operator=(const EtVectorBSTRGuard &);

	const std::etvector<BSTR> &m_vecBstr;
};

QueryDvCustomList::QueryDvCustomList(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.getDVCustomList"))
{
}

HRESULT QueryDvCustomList::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	const RANGE range = ReadRange(param);
	if (!range.IsValid())
		return S_FALSE;

	ks_stdptr<etoldapi::Range> range_api = m_wwb->CreateRangeObj(range);
	if (range_api == NULL)
		return S_FALSE;

	acpt->addKey("customList");
	KCOMPTR(Validation) ptrValidation;
	range_api->get_Validation(&ptrValidation);
	KCOMPTR(IValidationInfo) ptrValidationInfo = ptrValidation;
	if (ptrValidationInfo == NULL) {
		acpt->beginArray();
		acpt->endArray();
		return S_OK;
	}

	CELL cell;
	cell.row = range.RowFrom();
	cell.col = range.ColFrom();
    std::etvector<BSTR> vecValue, vecName;
	EtVectorBSTRGuard vecValueAutoRelease(vecValue);
	EtVectorBSTRGuard vecNameAutoRelease(vecName);

    HRESULT hr1 = ptrValidationInfo->GetCustomListValues(ETStringToolsOpt_None, &vecValue, FALSE);
	if (FAILED(hr1))
	{
		acpt->beginArray();
		acpt->endArray();

		if (hr1 == E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE)
		{
			AddErrorStr(acpt, __X("E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE"));
		}
		return hr1;
	}

    HRESULT hr2 = ptrValidationInfo->GetCustomList(ETStringToolsOpt_None, &vecName, FALSE);
	if (FAILED(hr2))
	{
		acpt->beginArray();
		acpt->endArray();

		if (hr2 == E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE)
		{
			AddErrorStr(acpt, __X("E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE"));
		}
		return hr2;
	}

	struct BstrCmp {
		bool operator()(const BSTR& lhs, const BSTR& rhs) const {
			return xstrcmp(lhs, rhs) < 0;
		}
	};

	std::map<BSTR, BSTR, BstrCmp> uniqueMap;
    acpt->beginArray();
    if (hr1 == S_OK && hr2 == S_OK && vecValue.size() == vecName.size()) {
        for (size_t i = 0; i < vecValue.size(); ++i) {
            if (vecValue.at(i) != NULL && vecName.at(i) != NULL) {
				auto it = uniqueMap.find(vecName.at(i));
				if (it == uniqueMap.end() || 0 != xstrcmp(it->second, vecValue.at(i))) {
					uniqueMap[vecName.at(i)] = vecValue.at(i);

					acpt->beginStruct();
					acpt->addString("value", vecValue.at(i));
					acpt->addString("name", vecName.at(i));
					acpt->addInt32("index", static_cast<int32_t>(i));
					acpt->addKey("cell");
					acpt->beginStruct();
						acpt->addInt32("row", cell.row);
						acpt->addInt32("col", cell.col);
					acpt->endStruct();
					acpt->endStruct();
				}
            }
        }
    }
    acpt->endArray();

	return S_OK;
}

QueryDvCustomListByFormula::QueryDvCustomListByFormula(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.getDVCustomListByFormula"))
{
}

HRESULT QueryDvCustomListByFormula::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	EnableProtectCompileFmla enableClFlmGuard;
	RANGE rg = ReadRange(param);
	if (!rg.IsValid())
		return S_FALSE;

	PCWSTR formulaText = NULL;
	if (param.has("formulaText"))
		formulaText = param.field_str("formulaText"); 
	else
		return E_INVALIDARG;

	if (xstrlen(formulaText) > 255)
	{
		AddErrorStr(acpt, GetErrWideString(E_ET_COMPILE_STRING_IN_FORMULA_TOO_LONG));
		return E_ET_COMPILE_STRING_IN_FORMULA_TOO_LONG;
	}

	IDX iSht = rg.SheetFrom();
	ROW r = rg.RowFrom();
	COL c = rg.ColFrom();

	CS_COMPILE_PARAM csp(cpfReOffset | cpfSysDefault, iSht, r, c);
	COMPILE_RESULT cr;
	ks_stdptr<IFormula> spFormula;
	IBookOp* pBookOp = m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator();
	pBookOp->CreateFormula(&spFormula);
	HRESULT hr = spFormula->SetFormula(formulaText, csp, &cr);
	if (FAILED(hr)) 
		return hr;
	CS_CALCULATE_PARAM ccp(ccfAsInName | ccfNPRefer, iSht, r, c);
	alg::managed_token_assist token; 
	hr = spFormula->Calculate(&token, ccp);
	if (FAILED(hr))
		return hr;
	if (token.major_type() != alg::ETP_STREF)
	{
		AddErrorStr(acpt, GetErrWideString(E_DATA_VALIDATION_CUSTOM_LIST_MUST_SINGLE_LINE));
		return E_DATA_VALIDATION_CUSTOM_LIST_MUST_SINGLE_LINE;
	}

	alg::const_stref_token_assist stToken(token);
	RANGE refRg(m_wwb->GetBMP());
	if (stToken.is_cell() && stToken.is_valid_cell(false))
	{
		refRg.SetCell(stToken.get_sheet_id(), stToken.get_cell_row(), stToken.get_cell_col());
	}
	else if (stToken.is_region() && stToken.is_valid_region(false))
	{
		if (stToken.get_begin_row() != stToken.get_end_row() && stToken.get_begin_col() != stToken.get_end_col())
		{
			AddErrorStr(acpt, GetErrWideString(E_DATA_VALIDATION_CUSTOM_LIST_MUST_SINGLE_LINE));
			return E_DATA_VALIDATION_CUSTOM_LIST_MUST_SINGLE_LINE;
		}
		refRg.SetSheetFromTo(stToken.get_begin_sheet(), stToken.get_end_sheet());
		refRg.SetRowFromTo(stToken.get_begin_row(), stToken.get_end_row());
		refRg.SetColFromTo(stToken.get_begin_col(), stToken.get_end_col());
	}
	else
	{
		return E_FAIL;
	}

	ks_stdptr<ISheet> spSheet;
	hr = m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(refRg.SheetFrom(), &spSheet);
	if (FAILED(hr))
		return hr;

	// 禁止查看区域
	if (ctx->getProtectionCtx()->isRangeHasHidden(refRg))
	{
		AddErrorStr(acpt, GetErrWideString(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE));
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
	}

	// 取列表内容
	RECT rcUsed = Rect_CreateScaleNone();
	spSheet->GetUsedScale(&rcUsed);
	if (Rect_IsValid(rcUsed))
	{
		RANGE rgUsed = Rect2Range(rcUsed, refRg.SheetFrom(), spSheet->GetBMP());
		refRg = refRg.Intersect(rgUsed);
		if (!refRg.IsValid())
			return S_FALSE;
	}

	const int MAX_LIMIT_COUNT = 32767;
	INT64 nCellCnt = refRg.CellCount();
	INT nLimitCount = MAX_LIMIT_COUNT;
	if (nCellCnt < MAX_LIMIT_COUNT)
		nLimitCount = static_cast<INT>(nCellCnt);

	struct _BSTRHASH
	{
		size_t operator()(BSTR pwcsz) const
		{
			return alg::HashWString(pwcsz);
		}
	};

	struct _BSTREQUAL
	{
		bool operator()(BSTR lhs, BSTR rhs) const
		{
			return 0 == xstrcmp(lhs, rhs);
		}
	};

	std::unordered_set<BSTR, _BSTRHASH, _BSTREQUAL> uniqueBstrs;
	acpt->addKey("values");
	acpt->beginArray();
	for (int i = refRg.RowFrom(); i <= refRg.RowTo() && nLimitCount >= 0; ++i)
	{
		for (int j = refRg.ColFrom(); j <= refRg.ColTo() && nLimitCount >= 0; ++j)
		{
			ks_bstr cellText;
			hr = ctx->getStringTools()->GetCellText(spSheet, i, j, &cellText, NULL, 0, NULL);
			if (SUCCEEDED(hr) && uniqueBstrs.find(cellText) == uniqueBstrs.end() && !cellText.empty())
			{
				acpt->addString(NULL, cellText);
				uniqueBstrs.insert(cellText.detach());
				--nLimitCount;
			}
		}
	}
	acpt->endArray();

	for (auto it = uniqueBstrs.begin(); it != uniqueBstrs.end(); ++it)
	{
		::SysFreeString(*it);
	}

	return S_OK;
}

QueryDvCustomListCellResult::QueryDvCustomListCellResult(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.calcCustomListCellResult"))
{
}

HRESULT QueryDvCustomListCellResult::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX)
		return E_FAIL;
	VarObj cells = param.get("cells");
    if (cells.type() != binary_wo::typeArray)
        return E_INVALIDARG;
    
    KDvCustomListCache* pCache = m_wwb->getCustomListCalcCache();
    if (pCache == NULL)
        return E_FAIL;

	std::vector<CELL> vecCells;

    acpt->addKey("cellSliceIndexs");
    acpt->beginArray();
    auto sz = cells.arrayLength();
    for (int32 i = 0; i < sz; ++i) {
		auto data = cells.at_s(i);
		if (data.arrayLength() > 1) {
			auto r = data.at_s(0).value_int32();
			for (int32 j = 1; j < data.arrayLength(); ++j) {
				auto c = data.at_s(j).value_int32();
				vecCells.push_back({r, c});
            }
        }
    }
	WOLOG_INFO << "[range_calcCustomListCellResult] cell count: " << vecCells.size();

	pCache->CalculateAndSerializeCellResults(acpt, sheetIdx, vecCells);
    acpt->endArray();
	return S_OK;
}

///////////////////////////////////QueryCustomSortFields//////////////////////////////////

namespace range_sort
{
static bool GetSort(const ks_stdptr<etoldapi::Range> &spRangeSel, ks_stdptr<etoldapi::Sort> &sort, ks_stdptr<IKSortData> &sortdata)
{
	ks_stdptr<etoldapi::Sort> ptrSort;
	ks_stdptr<_Worksheet> worksheet;
	spRangeSel->get_Worksheet(&worksheet);
	worksheet->get_Sort(&ptrSort);
	if (!ptrSort)
		return false;

	ks_stdptr<IKSortData> ptrSortData;
	ks_stdptr<ISortInfo> ptrSortInfo = ptrSort;
	VS(ptrSortInfo->GetSortData(&ptrSortData));
	if (!ptrSortData)
		return false;

	sort = ptrSort;
	sortdata = ptrSortData;
	return true;
}

static inline void WriteResult(ISerialAcceptor *acpt, bool success)
{
	acpt->addBool("success", success);
}

static bool IsSortParamValid(ks_stdptr<IKSortData> &ptrSortData, const RANGE &selRange, ETSortOrientation sortOrientation, const range_sort::SortItems &items)
{
	// 判断上次排序参数中的关键字是否有效
	// 规则：本次排序区域与上次排序区域有交集，并且交集包含所有关键字在内
	RANGE rg = selRange;
	const RANGE* pLastRng = NULL;	// 上次排序区域
	HRESULT hr = ptrSortData->GetRange(&pLastRng);
	if(SUCCEEDED(hr) && pLastRng != NULL)
	{
		rg = rg.Intersect(*pLastRng);
	}

	if(!rg.IsValid())
		return false;

	for(UINT i = 0, n = items.size(); i < n; ++i)
	{
		const auto& item = items[i];
		if((::etSortColumns == sortOrientation)
			&& (item.keyRange.ColFrom() < rg.ColFrom()
			|| item.keyRange.ColFrom() > rg.ColTo()))
		{
			return false;
		}
		else if((::etSortRows == sortOrientation)
			&& (item.keyRange.RowFrom() < rg.RowFrom()
			|| item.keyRange.RowFrom() > rg.RowTo()))
		{
			return false;
		}
	}

	return true;
}

static bool sortRangHasHeader(ks_stdptr<IKSortData> &ptrSortData, const ks_stdptr<etoldapi::Range> &spRangeSel, const RANGE &selRange,
                              bool *bHasHeader, bool *bHasFilterHeader)
{
	switch (ptrSortData->GetParentType())
	{
	case spt_AutoFilter:
		*bHasHeader = true;
		*bHasFilterHeader = true;
		break;
	case spt_ListObject:
		*bHasHeader = (ptrSortData->GetHeader() == sht_Yes) ? true : false;
		break;
	case spt_Worksheet:
	{
		// 如果是第一次排序，则判断一次是否包含标题行;
		// 如果不是第一次排序，则直接应用上一次的结论;
		int nHeaderCnt = 0;
		if (ptrSortData->GetHeaderCount() < 0)
			nHeaderCnt = app_helper::GuessRangeSortHeader(spRangeSel, TRUE, ptrSortData->GetSortRows(), FALSE);
		else if (ptrSortData->GetHasHeader())
			nHeaderCnt = app_helper::GuessRangeSortHeader(spRangeSel, FALSE, ptrSortData->GetSortRows(), TRUE);

		if (nHeaderCnt == 0 || selRange.Height() <= nHeaderCnt)
			*bHasHeader = false;
		else
			*bHasHeader = true;
	}
	break;
	default:
		return false;
	}

	return true;
}

} // namespace range_sort

QueryCustomSortFields::QueryCustomSortFields(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("range.getCustomSortFields"))
{
}

bool QueryCustomSortFields::GetSort(const ks_stdptr<etoldapi::Range> &spRangeSel, ks_stdptr<etoldapi::Sort> &sort,
									ks_stdptr<IKSortData> &sortdata)
{
	ks_stdptr<etoldapi::Sort> ptrSort;
    ks_stdptr<_Worksheet> worksheet;
	spRangeSel->get_Worksheet(&worksheet);

    BOOL bFilterSort = FALSE;
    VARIANT_BOOL bFilterMode = VARIANT_FALSE;
    worksheet->get_AutoFilterMode(&bFilterMode);
    ks_stdptr<AutoFilter> ptrFilter;
    worksheet->get_AutoFilter(NULL, &ptrFilter);

    if (VARIANT_TRUE == bFilterMode && ptrFilter) {
        ks_stdptr<Range> ptrFilterRange;
        ptrFilter->get_Range(&ptrFilterRange);
        ks_stdptr<IKRanges> ptrFilterRgs;
        app_helper::GetIRanges(ptrFilterRange, &ptrFilterRgs);
        const RANGE* ptrFilterRg = NULL;
        ptrFilterRgs->GetItem(0, 0, &ptrFilterRg);

		ks_stdptr<IRangeInfo> spRangeInfo = spRangeSel;
		if (spRangeInfo) {
			ks_stdptr<IKRanges> spRanges;
			spRangeInfo->GetIRanges(&spRanges);
			UINT nRanges = 0;
			spRanges->GetCount(&nRanges);
			if (nRanges > 0) {
				const RANGE* ptrRg = NULL;
				spRanges->GetItem(0, 0, &ptrRg);

				if (ptrRg && ptrFilterRg->Compare(*ptrRg) && ptrFilterRg->Height() > 1) {
					bFilterSort = TRUE;
				}
			}
		}
    }

    if (bFilterSort)
        ptrFilter->get_Sort(&ptrSort);
    else
        worksheet->get_Sort(&ptrSort);

	if (!ptrSort)
		return false;

	ks_stdptr<IKSortData> ptrSortData;
	ks_stdptr<ISortInfo> ptrSortInfo = ptrSort;
	VS(ptrSortInfo->GetSortData(&ptrSortData));
	if (!ptrSortData)
		return false;

	sort = ptrSort;
	sortdata = ptrSortData;
	return true;
}

HRESULT QueryCustomSortFields::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	const RANGE rgSel = ReadRange(param);
	if (!rgSel.IsValid())
		return S_FALSE;

	ks_stdptr<etoldapi::Range> spRangeSel = m_wwb->CreateRangeObj(rgSel);
	if (!spRangeSel)
		return false;

	ks_stdptr<etoldapi::Sort> ptrSort;
	ks_stdptr<IKSortData> ptrSortData;
	if (!this->GetSort(spRangeSel, ptrSort, ptrSortData)) {
		return S_FALSE;
	}

	// 获取内核排序数据
	const bool bCaseSensitive = static_cast<bool>(ptrSortData->GetMatchCase());
	const ETSortOrientation sortOrientation = ptrSortData->GetSortRows() ? ::etSortRows : ::etSortColumns;
	const ETSortMethod sortMethod = ptrSortData->GetSortByStroke() ? ::etStroke : ::etPinYin;

	range_sort::SortItems sortItems;
	if (!range_sort::LoadSortItems(m_wwb->GetCoreApp(), ptrSortData, &sortItems)) {

		range_sort::WriteResult(acpt, false);
		return S_FALSE;
	}

	range_sort::WriteResult(acpt, true);

	bool bHasHeader = false;
	bool bHasFilterHeader = false;
	range_sort::sortRangHasHeader(ptrSortData, spRangeSel, rgSel, &bHasHeader, &bHasFilterHeader);
	if (!IsSortParamValid(ptrSortData, rgSel, sortOrientation, sortItems)) {
		sortItems.clear();
	}

	range_sort::WriteSortOption(acpt, bCaseSensitive, sortOrientation, sortMethod, bHasHeader, bHasFilterHeader);
	acpt->addKey("sortFieldList");
	acpt->beginArray();
	const size_t condCount = sortItems.size();
	for (size_t i = 0; i < condCount; ++i) {
		acpt->beginStruct();
		auto &item = sortItems[i];
		const auto order = item.bAscending ? ::etAscending : ::etDescending;
		range_sort::WriteSortField(acpt, item.sortOn, order, sortItems[i].customList);
		range_sort::WriteSortKeyRange(acpt, sortOrientation, item.keyRange);
		range_sort::WriteSortOnValue(m_wwb->GetCoreWorkbook()->GetBook(), acpt, item.sortOn, item.spCellColor, item.spFontColor, item.spCellIcon);
		acpt->endStruct();
	}
	acpt->endArray();
	return S_OK;
}

//////////////////////////////////////QueryCellColorsInRange/////////////////////////////////////
namespace query_colors
{
enum QueryType
{
	kQInvaild = 0,
	kQCellColor = 1,
	kQFontColor = 2
};

struct Request
{
	uint32_t type;
	int32_t max_count;
};

static inline bool ReadReuest(const VarObj &param, Request *req)
{
	req->type = param.field_uint32("type");
	req->max_count = param.field_int32("max_count");
	return true;
}

typedef std::vector<ks_stdptr<ICellColor>> CellColors;
typedef std::vector<ks_stdptr<IFontColor>> FontColors;

static bool EnumCellColors(IBookOp *bookop, RANGE &rg, CellColors *colors, int max_count)
{
	// 枚举区域中的所有颜色
	ks_stdptr<ICellFormatsEnum> spFormatsEnum;
	ks_stdptr<IFilterCellColorItems> spItems;
	HRESULT hr = _appcore_CreateObject(CLSID_KCellFormatsEnum, IID_ICellFormatsEnum, (void **)&spFormatsEnum);
	KS_CHECK(hr);
	hr = spFormatsEnum->EnumCellColorsInRange(bookop, rg, &spItems);
	KS_CHECK(hr);

	for (int i = 0; i < spItems->GetItemCount(); ++i) {
		ks_stdptr<ICellColor> spCellColor;
		spItems->GetItem(i, &spCellColor);
		if (spCellColor) {
			colors->push_back(spCellColor);
		}
	}

KS_EXIT:
	return true;
}

static bool EnumFontColors(IBookOp *bookop, RANGE &rg, FontColors *colors, int max_count)
{
	// 枚举区域中的所有颜色
	ks_stdptr<ICellFormatsEnum> spFormatsEnum;
	ks_stdptr<IFilterFontColorItems> spItems;
	HRESULT hr = _appcore_CreateObject(CLSID_KCellFormatsEnum, IID_ICellFormatsEnum, (void **)&spFormatsEnum);
	KS_CHECK(hr);
	hr = spFormatsEnum->EnumFontColorsInRange(bookop, rg, &spItems);
	KS_CHECK(hr);

	for (int i = 0; i < spItems->GetItemCount(); ++i) {
		ks_stdptr<IFontColor> spFontColor;
		spItems->GetItem(i, &spFontColor);
		if (spFontColor) {
			colors->push_back(spFontColor);
		}
	}

KS_EXIT:
	return true;
}

static bool WriteCellColors(IBook *book, IBookOp *bookop, RANGE &rg, int32_t max_count, ISerialAcceptor *acpt)
{
	CellColors colors;
	if (!query_colors::EnumCellColors(bookop, rg, &colors, max_count))
		return false;

	acpt->addKey("cellColors");
	acpt->beginArray();

	for (auto it = colors.begin(); it != colors.end(); ++it) {
		ks_stdptr<ICellColor> &cell_clr = *it;
		const EtFill *fill = NULL;
		if (!cell_clr || FAILED(cell_clr->GetFill(&fill)) || (fill == NULL)) //  || cell_clr->IsNoColor() || fill->getBack().getType() == ectNONE
			continue;

		range_sort::WriteFill(acpt, *fill, book);
	}

	acpt->endArray();
	return true;
}

static bool WriteFontColors(IBook *book, IBookOp *bookop, RANGE &rg, int32_t max_count, ISerialAcceptor *acpt)
{
	FontColors colors;
	if (!query_colors::EnumFontColors(bookop, rg, &colors, max_count))
		return false;

	acpt->addKey("fontColors");
	acpt->beginArray();

	for (auto it = colors.begin(); it != colors.end(); ++it) {
		ks_stdptr<IFontColor> &font_clr = *it;
		const EtColor *clr = NULL;
		if (!font_clr || FAILED(font_clr->GetColor(&clr)) || (clr == NULL) || clr->getType() == ectNONE)
			continue;

		range_sort::WriteColor(acpt, *clr, book);
	}

	acpt->endArray();
	return true;
}

static inline void WriteResult(ISerialAcceptor *acpt, bool success)
{
	acpt->addBool("success", success);
}

} // namespace query_colors

QueryCellColorsInRange::QueryCellColorsInRange(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("range.enumCellColors"))
{
}

HRESULT QueryCellColorsInRange::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	RANGE range = ReadRange(param);
	query_colors::Request req;
	if (!range.IsValid() || !query_colors::ReadReuest(param, &req))
		return S_FALSE;

	IBook *book = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IBookOp> bookop;
	book->GetOperator(&bookop);
	bool success = false;

	const int32_t count_limit = 64;
	req.max_count = req.max_count > count_limit ? count_limit : req.max_count;

	switch (req.type)
	{
	case query_colors::kQCellColor:
		success = query_colors::WriteCellColors(book, bookop, range, req.max_count, acpt);
		break;
	case query_colors::kQFontColor:
		success = query_colors::WriteFontColors(book, bookop, range, req.max_count, acpt);
		break;
	default:
		ASSERT(false);
	}

	(void)success; // unused
	query_colors::WriteResult(acpt, true);
	acpt->addUint32("type", req.type);
	return S_OK;
}

// ===================================================
QueryTableStyle::QueryTableStyle(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("queryTableStyle"))
{
}

HRESULT QueryTableStyle::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	IBook *bk = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<ICoreTableStyles> spCoreStys;
	bk->GetExtDataItem(edBookTableStyles, (IUnknown**)&spCoreStys);

	if (param.has("indices"))
	{
		VarObj indices = param.get("indices");
		acpt->addKey("styles");
		acpt->beginArray();
		for (int i = 0; i < indices.arrayLength(); i++)
		{
			int idx = indices.item_int32(i);
			ks_stdptr<ICoreTableStyle> spCoreSty;
			spCoreStys->GetItem(idx, &spCoreSty);
			acpt->beginStruct();
				acpt->addInt32("tableIdx", idx);
				acpt->addKey("style");
					spCoreSty->GetPlay()->serialContent(acpt);
			acpt->endStruct();
		}
		acpt->endArray();
	}
	else if (param.has("tableIdxFrom") && param.has("tableIdxTo"))
	{
	int idxFrom = param.field_int32("tableIdxFrom");
	int idxTo = param.field_int32("tableIdxTo");

	acpt->addKey("styles");
	acpt->beginArray();
	for (int i = idxFrom; i <= idxTo; i++)
	{
			ks_stdptr<ICoreTableStyle> spCoreSty;
		spCoreStys->GetItem(i, &spCoreSty);
		acpt->beginStruct();
			acpt->addInt32("tableIdx", i);
			acpt->addKey("style");
				spCoreSty->GetPlay()->serialContent(acpt);
		acpt->endStruct();
	}
	acpt->endArray();
	}

	// batch query table/pivot/slicer styles
	if (param.has("batch"))
		acpt->addString("batch", param.field_str("batch"));

	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
QueryProcessOnInfo::QueryProcessOnInfo(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.processOnInfo"))
{

}

HRESULT QueryProcessOnInfo::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	drawing::AbstractShape* pShape = GetShape(param, ctx);
	if (pShape == nullptr)
		return E_FAIL;

	KInsertProcessonData* pProcessOnData = getProcessOnData(pShape);
	if (!pProcessOnData)
		return WO_FAIL;

	QString category = pProcessOnData->Category();
	QString fileId = pProcessOnData->FileId();
	QString definition = pProcessOnData->Definition();
	acpt->addKey("processOn");
	acpt->beginStruct();
	acpt->addString("category", krt::utf16(category));
	acpt->addString("fileId", krt::utf16(fileId));
	acpt->addString("definition", krt::utf16(definition));
	acpt->endStruct();

	return S_OK;
}

KInsertProcessonData* QueryProcessOnInfo::getProcessOnData(IKShape* pShape)
{
	wo::KInsertProcessonData* pProcessOnData = nullptr;

	ks_stdptr<IKExtData> spData;
	HRESULT hr = pShape->GetExtData(&spData);
	if (SUCCEEDED(hr))
	{
		ks_bstr type;
		spData->GetExtDataType(&type);

		pProcessOnData = new wo::KInsertProcessonData(pShape);
		BSTR guid = pProcessOnData->GetGuid();
		if (type.isEqual(guid)) {
			ks_bstr d;
			spData->GetExtData(&d);
			pProcessOnData->Deserialize(QString::fromUtf16(d.c_str()));
			ASSERT(pProcessOnData->IsValid() && "扩展数据格式错误");
		}
	}
	return pProcessOnData;
}

//////////////////////////////////////////////////////////////////////////
QueryDataValidation::QueryDataValidation(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("query.error.validation"))
{
}
typedef std::map<int32, std::map<int32, bool>> dataValidType;
HRESULT queryDataValidationPackRslt(const dataValidType& rslt, ISerialAcceptor &acpt) {
	auto sz_r = rslt.size();
	std::vector<int32> all_r;
	all_r.reserve(rslt.size());
	for (auto it = rslt.begin(); it != rslt.end(); ++it) {
		all_r.push_back(it->first);
	}
	acpt.addKey("rslt"); {
		acpt.beginStruct();
		{
			auto rows_bg = wo::sa::enterArray(&acpt, "rows");
			for (auto it = rslt.begin(); it != rslt.end(); ++it) {
				acpt.addInt32(nullptr, it->first);
			}
		}
		{
			auto col_data_bg = wo::sa::enterArray(&acpt, "col_data");
			for (auto it = rslt.begin(); it != rslt.end(); ++it) {
				acpt.beginStruct(); {
					std::vector<int> one_row_cols;
					std::vector<bool> one_row_data;
					for (auto it_row = (it->second).begin(); it_row != (it->second).end(); ++it_row) {
						one_row_cols.push_back(it_row->first);
						one_row_data.push_back(it_row->second);
					}
					{
						auto cols_bg = wo::sa::enterArray(&acpt, "cols");
						std::for_each(one_row_cols.begin(), one_row_cols.end(), [&](int& c) {
							acpt.addInt32(nullptr, c);
						});
					}
					{
						auto cols_vld_bg = wo::sa::enterArray(&acpt, "vld");
						std::for_each(one_row_data.begin(), one_row_data.end(), [&](bool vld) {
							acpt.addBool(nullptr, vld);
						});
					}
				}
				acpt.endStruct();
			}
		}
		acpt.endStruct();
	}
	return S_OK;
}

HRESULT QueryDataValidation::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt) {
	IDX idxSheet = param.field_int32("sheetIdx");
	ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(idxSheet, &spSheet);

	dataValidType rslt;
	auto cells = param.get("cells");
	auto sz = cells.arrayLength();

	for (int32 i = 0; i < sz; ++i) {
		auto data = cells.at_s(i);
		if (data.arrayLength() > 1) {
			std::map<int32, bool> r_rslt;
			auto r = data.at_s(0).value_int32();
			for (int32 j = 1; j < data.arrayLength(); ++j) {
				auto c = data.at_s(j).value_int32();
				if (ctx->getProtectionCtx()->isCellHidden(spSheet, r, c))
				{
					r_rslt[c] = false;
				}
				else
				{
					r_rslt[c] = this->check(r, c, idxSheet) != TRUE;
				}

			}rslt[r] = std::move(r_rslt);
		}
	}
	queryDataValidationPackRslt(rslt, *acpt);
	acpt->addInt32("sheetIdx", idxSheet);
	return S_OK;
}

HRESULT QueryDataValidation::check(ROW row, COL col, const IDX idxSheet) {
	ks_stdptr<ICoreValidation> spCoreValidation;
	auto hr = GetValidation(row, col, idxSheet, &spCoreValidation);
	if (FAILED(hr)) {
		return hr;
	}
	VARIANT_BOOL bShowError = VARIANT_FALSE;
	if (E_INVALIDARG == spCoreValidation->getShowError(&bShowError)) {
		return FALSE;
	}
	if (S_FALSE == spCoreValidation->IsValidInput()) {
		return TRUE;
	} return FALSE;
}

HRESULT QueryDataValidation::GetValidation(ROW row, COL col, const IDX idxSheet, ICoreValidation **ppv) {
	ks_stdptr<ICoreValidation> spCoreValidation;
	//拿对象
	if (FAILED(_appcore_CreateObject(CLSID_KCoreValidation, IID_ICoreValidation, (void**)&spCoreValidation))) {
		return E_FAIL;
	}

	CELL cell;
	cell.row = row;
	cell.col = col;

	RANGE rg(m_wwb->GetBMP());
	rg.SetCell(idxSheet, cell.row, cell.col);

	range_helper::ranges rgs = rgs.create_instance();
	rgs.add(etexec::STREF_THIS_BOOK, rg);

	ks_stdptr<IBook> spBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IWorkspace> spWorkspace;
	spBook->GetWorkspace(&spWorkspace);
	if (!spWorkspace) {
		return E_FAIL;
	}

	ks_stdptr<IAppSettings> spAppSettings;
	spWorkspace->GetAppSettings(&spAppSettings);
	if (!spAppSettings) {
		return E_FAIL;
	}

	ks_stdptr<IBookOp> spBookOp;
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
	REF_STYLE refStyle = spAppSettings->GetReferenceStyle();
	auto _idxSheet = idxSheet;
	spCoreValidation->Init(spBookOp, cell, rgs, rg, _idxSheet, refStyle);

	(*ppv) = spCoreValidation.detach();
	return S_OK;
}

QueryMergeFileList::QueryMergeFileList(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("file.getMergeList"))
{
}

HRESULT QueryMergeFileList::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<IBook> ptrBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (!ptrBook) return E_FAIL;

	std::vector<SMergeFileInfo> listMergeFile;
	ptrBook->GetMergeFileList(listMergeFile);

	acpt->addKey("listMergeFile");
	acpt->beginArray();
	int iListSize = listMergeFile.size();
	for (int i = 0; i < iListSize; i++)
	{
		acpt->beginStruct();
		{
			acpt->addString("fileId", listMergeFile[i].strId.c_str());
			acpt->addString("docName", listMergeFile[i].strDocName.c_str());
			acpt->addString("fileAuthor", listMergeFile[i].strAuthor.c_str());
			acpt->addString("fileTime", listMergeFile[i].strTime.c_str());
		}
		acpt->endStruct();
	}
	acpt->endArray();

	return S_OK;
}

QueryTxt2ColSubRange::QueryTxt2ColSubRange(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("range.text2col.subrange"))
{}

HRESULT QueryTxt2ColSubRange::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	if (ctx->getProtectionCtx()->isBookHasHiddenProperty())
	{
		acpt->addString("errName", __X("E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE"));
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
	}

	RANGE rg = ReadRange(param);

	IDX iSheet = param.field_int32("sheetIdx");
	ks_stdptr<IKWorksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	if (!spWorksheets) {
		return S_FALSE;
	}
	ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(iSheet);
	auto isheet = spWorksheet->GetSheet();


	ROW first_row = isheet->SeekNextCellInColumn(rg.RowFrom(), rg.ColFrom());
	if (first_row < rg.RowFrom()) {
		first_row = rg.RowFrom();
	}
	ROW last_row = isheet->SeekPrevCellInColumn(rg.RowTo(), rg.ColFrom());
	if (last_row > rg.RowTo()) {
		last_row = rg.RowTo();
	}


	acpt->addInt32("sheetIdx", iSheet);
	acpt->addInt32("rowFrom", first_row);
	acpt->addInt32("rowTo", last_row);
	acpt->addInt32("col", rg.ColFrom());

	return wo::text2col::subRangeGetAllData(m_wwb, iSheet, first_row, last_row, rg.ColFrom(), acpt);
}

//////////////////////////////////////////////////////////////////////////
QueryAutoFilter::QueryAutoFilter(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("range.AutofilterInfo"))
{
}

PCWSTR QueryAutoFilter::DetectAutofilterType(const RANGE &rg, PCWSTR filterID)
{
	IKAutoFilter *pAutoFilter = AutoFilterHelper::GetUserFilter(m_wwb->GetCoreWorkbook(), filterID, rg);
	if (pAutoFilter != NULL)
	{
		RANGE filterRg(m_wwb->GetBMP());
		pAutoFilter->GetFilterRange(&filterRg);
		if (filterRg.Contain(rg))
		{
			int nField = rg.ColFrom() - filterRg.ColFrom();
			ETFilterOperator fOp = pAutoFilter->GetOperator(nField);
			switch (fOp)
			{
				case FOp_FilterCellColor:
				case FOp_FilterFontColor:
				case FOp_FilterIcon:
					return __X("formats");
				default:
					return __X("values");
			}
		}
	}
	return __X("error");
}

bool QueryAutoFilter::HasAutofilter(const RANGE &rg, PCWSTR filterID)
{
	IKAutoFilter *pAutoFilter = AutoFilterHelper::GetUserFilter(m_wwb->GetCoreWorkbook(), filterID, rg);
	if (pAutoFilter != NULL)
	{
		RANGE filterRg(m_wwb->GetBMP());
		pAutoFilter->GetFilterRange(&filterRg);
		if (filterRg.Contain(rg))
		{
			return true;
		}
	}

	return false;
}

class CollectQueryAutoFilter
{
public:
	CollectQueryAutoFilter(KEtRevisionContext* ctx, KEtWorkbook* wb, const VarObj& param, PCWSTR filterID, const RANGE &rg)
	: m_rows(0)
	, m_hasFilter(false)
	, m_type(nullptr)
	, m_begin(std::chrono::steady_clock::now())
	, m_pCtx(ctx)
	{
		m_pFilter = AutoFilterHelper::GetUserFilter(wb->GetCoreWorkbook(), filterID, rg);
		if (m_pFilter != NULL)
		{
			RANGE filterRg(wb->GetBMP());
			m_pFilter->GetFilterRange(&filterRg);
			m_rows = filterRg.Height();

			m_hasFilter = m_pFilter->IsExecuteFilter();
			m_type = param.field_str("type");
		}
	}
	~CollectQueryAutoFilter()
	{// 行数, 时间
		if (m_rows == 0) return;

		int ms = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
#ifndef _DEBUG
		if(ms > 500)
#endif
		{
			// name = behaviour_get_filter_type_rows, count = filterTime
			QString name("behaviour_get_filter_");
			if (m_type)
				name += QString::fromUtf16(m_type);
			else
				name += "none";
			
			name += QString("_%1").arg(m_rows);

			uint filterTime = 0;
			if (!m_hasFilter)
				filterTime = m_pFilter->GetExecuteTime();
			
			util::CollectCmdInfo(m_pCtx, krt::utf16(name), filterTime);
		}
	}
private:
	PCWSTR m_type;
	KEtRevisionContext * m_pCtx;
	IKAutoFilter * m_pFilter;
	std::chrono::steady_clock::time_point m_begin;
	int m_rows;
	bool m_hasFilter;
};

HRESULT QueryAutoFilter::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    ks_castptr<IEtRevisionContext> acptCtx = acpt->getContext();
    if (!acptCtx || !ctx)
       return E_FAIL; 

	RANGE rg(m_wwb->GetBMP());

	rg.SetSheetFromTo(param.field_int32("sheetIdx"));
	rg.SetRowFromTo(
		param.field_int32("rowFrom"), param.field_int32("rowTo"));
	rg.SetColFromTo(
		param.field_int32("colFrom"), param.field_int32("colTo"));

	if (rg.SheetFrom() >= m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount())
		return E_FAIL;

	auto pFilterCtx = ctx->getFilterContext();
	bool readOnlyCoop = false;
    if (param.has("readOnly"))
    {
        readOnlyCoop = param.field_bool("readOnly");
        pFilterCtx->SetIsFilterReadOnly(readOnlyCoop);
    }
	
	PCWSTR filterID = pFilterCtx->GetID();


    ks_stdptr<Range> spRange = m_wwb->CreateRangeObj(rg);
	if (PivotHelpers::HasPivotTableInRange(spRange))
	{
		QueryPivotFilter queryPivotFilter(m_wwb);
		HRESULT hr = queryPivotFilter.Exec(param, ctx, acpt);
		if (SUCCEEDED(hr))
		{
			return hr;
		}
		else if (E_PIVOTTABLE_QUERY_PIVOTFIlTER_FAIL == hr && !HasAutofilter(rg, filterID))
		{
			AddErrorStr(acpt, __X("E_PIVOTTABLE_QUERY_PIVOTFIlTER_FAIL"));
			if (param.has("type"))
			{
				acpt->addString("type", param.field_str("type"));
			}
			return hr;
		}
	}

	CollectQueryAutoFilter collect(ctx, m_wwb, param, filterID, rg);
	HRESULT hr = S_OK;
	PCWSTR type = param.field_str("type");
    if (xstrcmp(__X("auto"), type) == 0)
	{//点击弹起面板
		hr = m_wwb->QueryAutoFilterInfos(static_cast<IKETUserConn*>(ctx->getUser()), filterID, rg, param, acpt);
    }
    else if (xstrcmp(__X("values"), type) == 0)
	{//点击排序,搜索,分页
		hr = m_wwb->QueryAutoFilterValues(static_cast<IKETUserConn*>(ctx->getUser()), filterID, rg, param, acpt);
	}
    else if (xstrcmp(__X("customValues"), type) ==0)
	{
		hr = m_wwb->QueryAutoFilterCustomValues(static_cast<IKETUserConn*>(ctx->getUser()), filterID, rg, acpt);
	}
    else if(xstrcmp(__X("formats"), type) == 0)
	{
		hr = m_wwb->QueryAutoFilterFormats(static_cast<IKETUserConn*>(ctx->getUser()), filterID, rg, acpt);
	}
    else if (xstrcmp(__X("condition"), type) == 0)
	{
		hr = m_wwb->QueryAutoFilterCondition(filterID, rg, acpt);
	}
    else if (xstrcmp(__X("clearCache"), type) == 0)
	{
		static_cast<IKETUserConn*>(ctx->getUser())->clearFilterCacheData();
	}
    else if (xstrcmp(__X("inverse"), type) == 0)
    {
        hr = m_wwb->QueryAutoFilterInverse(static_cast<IKETUserConn*>(ctx->getUser()), filterID, rg, param, acpt);
    }
    else if (xstrcmp(__X("categoryCount"), type) == 0)
    {
        hr = m_wwb->QueryAutoFilterCategoryCount(static_cast<IKETUserConn*>(ctx->getUser()), filterID, rg, param, acpt);
    }
    else if (xstrcmp(__X("setTmpValues"), type) == 0)
    {
        hr = m_wwb->QueryAutoFilterSetTmpValues(static_cast<IKETUserConn*>(ctx->getUser()), filterID, rg, param, acpt);
    }
    acpt->addString("type", type);
	AddErrorStr(acpt, hr);
	return hr;
}

// ===================================================
QueryPivotFilter::QueryPivotFilter(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X(""))
{
}

WebStr QueryPivotFilter::pivotTypeToStr(pivot_core::AxisFieldType type)
{
	switch (type)
	{
		case pivot_core::AxisFieldNone:
			return __X("none");
		case pivot_core::AxisFieldPage:
			return __X("page");
		case pivot_core::AxisFieldRow:
			return __X("row");
		case pivot_core::AxisFieldCol:
			return __X("col");
		default:
			ASSERT(FALSE);
			return __X("error");
	}
}

void QueryPivotFilter::rect2Range(const RECT& rect, RANGE *out)
{
	out->SetRowFromTo(rect.top, rect.bottom);
	out->SetColFromTo(rect.left, rect.right);
}

HRESULT QueryPivotFilter::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	RANGE rg(m_wwb->GetBMP());

	rg.SetSheetFromTo(param.field_int32("sheetIdx"));
	rg.SetRowFromTo(
		param.field_int32("rowFrom"), param.field_int32("rowTo"));
	rg.SetColFromTo(
		param.field_int32("colFrom"), param.field_int32("colTo"));

	if (rg.SheetFrom() >= m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount())
		return E_FAIL;

	ks_stdptr<Range> spRange = m_wwb->CreateRangeObj(rg);
	ks_stdptr<pivot_core::IPivotTable> spPvtTable;
	if (spRange)
		pivot_helper::GetCorePivotTableByCell(spRange, &spPvtTable);
	if (spPvtTable == nullptr)
		return E_FAIL;

	WebStr fieldName = NULL;
	ks_stdptr<pivot_core::ITblFieldAxis> spFieldAxis;
	if (param.has("fieldName"))
	{
		fieldName = param.field_str("fieldName");
		spFieldAxis = PivotHelpers::GetFieldAxisByFieldName(spPvtTable, fieldName);
	}
	if(spFieldAxis == nullptr)
		spFieldAxis = PivotHelpers::GetFieldAxisByCell(spPvtTable, {rg.RowFrom(), rg.ColFrom()}, fieldName);

	if (spFieldAxis == nullptr || !spFieldAxis->QueryFieldSrc())
	{
		return E_PIVOTTABLE_QUERY_PIVOTFIlTER_FAIL;
	}

	if (ctx->getProtectionCtx()->isBookHasHidden())
	{
		IDX sheetIdx = param.field_int32("sheetIdx");
		ks_stdptr<ISheet> spSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx)->GetSheet();
		auto sectionRanges =  PivotHelpers::getPivotTableSectionRanges(spSheet, spPvtTable);
		for (auto it = sectionRanges.begin(); it != sectionRanges.end(); ++it)
		{
			if (ctx->getProtectionCtx()->isRangeHasHidden(*it))
			{
				AddErrorStr(acpt, __X("E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE"));
				return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
			}
		}
	}

	acpt->addString("pivotType", pivotTypeToStr(spFieldAxis->GetFieldType()));
	acpt->addString("axisName", spFieldAxis->QueryFieldSrc()->GetName());
	serialCompactFields(acpt, spFieldAxis->GetFieldType(), spPvtTable);

	PCWSTR type = param.field_str("type");
	if (xstrcmp(__X("auto"), type) == 0)
		type = __X("values");
	acpt->addString("type", type);

	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorkbook->GetWorksheets()->GetSheetItem(rg.SheetFrom());
	BOOL b1904 = spWorkbook->GetBook()->Is1904DateSystem();
	if (xstrcmp(__X("values"), type) == 0)
	{
		PCWSTR searchData = NULL;
		PCWSTR searchType = NULL;
		VarObj vSc = param.get_s("search");
		if (vSc.type() != binary_wo::typeInvalid)
		{
			searchData = vSc.field_str("value");
			searchType = vSc.field_str("type");
		}

		SerialisePivotFilterHelper hlp(spPvtTable, spFieldAxis, spWorksheet, searchData);
		HRESULT hr = hlp.Init();
		if (FAILED(hr))
			return hr;
		SerialisePivotFilterOperatorHelper op(spPvtTable, spFieldAxis, spWorksheet, b1904);
		hr = op.Init();
		if (FAILED(hr))
			return hr;

		acpt->addKey("fieldData");
		acpt->beginStruct();
			acpt->addKey("search");
			acpt->beginStruct();
				acpt->addString("value", searchData == NULL ? __X("") : searchData);
				acpt->addString("type", searchType == NULL ? __X("") : searchType);
			acpt->endStruct();

			hlp.Serialise(acpt);
			op.Serialise(acpt);
		acpt->endStruct();

		ET_SORT_ORDER sortOrder = sot_None;
		PCWSTR sortStr = __X("none");
		hlp.GetOrder(sortOrder);
		switch(sortOrder)
		{
			case sot_None:
				sortStr = __X("none");
				break;
			case sot_Ascending:
				sortStr = __X("ascending");
				break;
			case sot_Descending:
				sortStr = __X("descending");
				break;
		}
		acpt->addKey("sortInfo");
		acpt->beginStruct();
			acpt->addString("sortOrder", sortStr);
			acpt->addBool("sortColor", false);
		acpt->endStruct();
	}
	else if (xstrcmp(__X("condition"), type) == 0)
	{
		SerialisePivotFilterOperatorHelper op(spPvtTable, spFieldAxis, spWorksheet, b1904);
		HRESULT hr = op.Init();
		if (FAILED(hr))
			return hr;
		op.Serialise(acpt);
	}
	else if (xstrcmp(__X("customValues"), type) ==0)
	{
		acpt->addKey("CustomValues");
		acpt->beginArray();
		acpt->endArray();
	}

	return S_OK;
}

void QueryPivotFilter::serialCompactFields(ISerialAcceptor *acpt, pivot_core::AxisFieldType type, pivot_core::IPivotTable *pPivotTable)
{
	if (pPivotTable->HasCompactFields())
	{
		pivot_core::ITblFieldsAxis* pFieldsAxis = NULL;
		if (type == pivot_core::AxisFieldRow || type == pivot_core::AxisFieldCol)
			pFieldsAxis = pPivotTable->GetAxisFields(type);
		if (pFieldsAxis == NULL)
			return;
		acpt->addKey("compactFields");
		acpt->beginArray();
			for (int i = 0; i < pFieldsAxis->Count(); i++)
			{
				pivot_core::ITblFieldAxis *pAxis = pFieldsAxis->Item(pivot_core::IDX_AXIS(i));
				if (pAxis && pAxis->QueryFieldSrc() && pAxis->QueryFieldSrc()->GetName())
					acpt->addString(nullptr, pAxis->QueryFieldSrc()->GetName());
			}
		acpt->endArray();
	}
}

// ===================================================
QueryPivotRangeGroupType::QueryPivotRangeGroupType(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("query.pivotTableRangeGroupType"))
{
}

HRESULT QueryPivotRangeGroupType::CreateRangeByTwoRanges(_Worksheet* pSheet, Range* pFirst, Range* pSecond, Range** ppRange)
{
	HRESULT hr = S_OK;

	ks_stdptr<IKRanges> spNewRgs;
	hr = _etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spNewRgs);

	ks_stdptr<IKRanges> spFirstRanges;
	if (SUCCEEDED(app_helper::GetIRanges(pFirst, &spFirstRanges)) && spFirstRanges)
	{
		UINT nCount = 0;
		spFirstRanges->GetCount(&nCount);
		for (UINT i = 0; i < nCount; ++i)
		{
			const RANGE* rg = NULL;
			spFirstRanges->GetItem(i, NULL, &rg);

			spNewRgs->Append(alg::STREF_THIS_BOOK, *rg);
		}
	}

	ks_stdptr<IKRanges> spSecondRanges;
	if (SUCCEEDED(app_helper::GetIRanges(pSecond, &spSecondRanges)) && spSecondRanges)
	{
		UINT nCount = 0;
		spSecondRanges->GetCount(&nCount);
		for (UINT i = 0; i < nCount; ++i)
		{
			const RANGE* rg = NULL;
			spSecondRanges->GetItem(i, NULL, &rg);

			spNewRgs->Append(alg::STREF_THIS_BOOK, *rg);
		}
	}

	hr = pSheet->GetRangeByData(spNewRgs, ppRange);
	return hr;
}

HRESULT QueryPivotRangeGroupType::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	std::vector<RANGE> rgSelectionVec;
	IDX sheetIdx = param.field_web_id("sheetIdx");
	ReadRangesInl(m_wwb->GetBMP(), sheetIdx, param, rgSelectionVec, "selection");

	for (auto it = rgSelectionVec.begin(); it != rgSelectionVec.end(); ++it)
	{
		if (ctx->getProtectionCtx()->isRangeHasHidden(*it))
		{
			return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
		}
	}

	ks_stdptr<etoldapi::Range> spSelectionRg = m_wwb->CreateRangeObj(rgSelectionVec);

	RANGE cellRange = ReadRange(param.get_s("activeCell"));
	if (ctx->getProtectionCtx()->isRangeHasHidden(cellRange))
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;

	ks_stdptr<etoldapi::Range> spActiveCellRg = m_wwb->CreateRangeObj(cellRange);
	ks_stdptr<PivotField> ptrPivotField;
	HRESULT hr = spActiveCellRg->get_PivotField(&ptrPivotField);
	if (FAILED(hr)) return E_FAIL;
	pivot_core::ITblFieldSrc* pTbFieldSrc = ptrPivotField->GetCoreField();
	if (pTbFieldSrc == NULL)
		return E_FAIL;

	ks_stdptr<etoldapi::Range> spRg;

    ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);

	CreateRangeByTwoRanges(spWorksheet, spActiveCellRg, spSelectionRg, &spRg);
	if(!spRg) return S_FALSE;

	ks_stdptr<pivot_core::IPivotTable> spCorePvtTbl;

	pivot_helper::GetCorePivotTableByCell(spActiveCellRg, &spCorePvtTbl);
	if (!spCorePvtTbl)
		return S_FALSE;

	pivot_core::AxisFieldType t = pivot_core::AxisFieldNone;
	pivot_core::IDX_AXIS idxBaseCellAxis;
	std::vector<pivot_core::IDX_ITEM> vecTblItem;
	hr = pivot_helper::GetGpRangeItemIdx(spRg, spCorePvtTbl, t, idxBaseCellAxis, vecTblItem);
	if (FAILED(hr)) return hr;

	bool bMultiSelect = vecTblItem.size() > 1;
	acpt->addBool("multiSelect", bMultiSelect);

	std::vector<pivot_core::ConstantsGroupArgument> args;
	DWORD dw = pTbFieldSrc->GetConstGroupArgument(bMultiSelect, args);

	if (dw != pivot_core::CGBT_Invalid && !args.empty())
	{
		acpt->addKey("group");
		acpt->beginArray();
		for (auto& arg : args)
		{
			acpt->beginStruct();
				acpt->addString("type", arg.groupTypeToStr(arg.type));
				acpt->addBool("bStart", arg.bStart);
				acpt->addBool("bStop", arg.bStop);
				acpt->addFloat64("start", arg.dbStart);
				acpt->addFloat64("stop", arg.dbStop);
				acpt->addFloat64("interval", arg.dbInterval);
			acpt->endStruct();
		}
		acpt->endArray();
	}

	return S_OK;
}

// ===================================================
QueryCellHistory::QueryCellHistory(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("queryCellHistory"))
{
}

HRESULT QueryCellHistory::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	UINT nRecent = 1;

	if (param.has("nRecent"))
		nRecent = param.field_int32("nRecent");

	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX) return S_OK;
	ks_stdptr<ISheet> sht = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx)->GetSheet();
	if (sht == NULL || sht->IsDbSheet() || sht->IsAppSheet() || sht->IsWorkbenchSheet()) return S_OK;
	ISheetStake *stake = sht->GetWoStake();
	if (stake == NULL) return S_OK;
	IHistoryInfo *spHistory = sht->GetWoStake()->getCellHistory();
	if (spHistory == NULL) return S_OK;

	if (param.has("blocks"))
	{
		VarObj vBlocks = param.get_s("blocks");
		acpt->addFloat64("objSheet", ctx->getSheetMain(sheetIdx)->objId());
		acpt->addKey("res");
		acpt->beginArray();
			for (int i = 0; i < vBlocks.arrayLength(); i++)
			{
				VarObj vItem = vBlocks.at(i);

				ROW rowFrom = vItem.field_int32("rowFrom");
				ROW rowTo = vItem.field_int32("rowTo");
				COL colFrom = vItem.field_int32("colFrom");
				COL colTo = vItem.field_int32("colTo");

				// could be optimised
				for (ROW r = rowFrom; r <= rowTo; r++)
					for (COL c = colFrom; c <= colTo; c++)
					{
						if (!ctx->getProtectionCtx()->isCellHidden(sht, rowFrom, rowTo))
							spHistory->serialHistory(r, c, nRecent, acpt, false);
					}
			}
		acpt->endArray();
	}

	return S_OK;
}

// ===================================================
QuerySingleCellHistory::QuerySingleCellHistory(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("querySingleCellHistory"))
{
}

HRESULT QuerySingleCellHistory::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	IDX sheetIdx = param.field_int32("sheetIdx");
	if (sheetIdx < 0) return E_FAIL;
	BMP_PTR bmp = m_wwb->GetBMP();
	ROW row = param.field_int32("row");
	if (row < 0 || row >= bmp->cntRows) return E_FAIL;
	ROW col = param.field_int32("col");
	if (col < 0 || col >= bmp->cntCols) return E_FAIL;
	UINT nRecent = 0; // unlimited

	IKWorksheet *pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (pWorksheet == NULL) return E_FAIL;
	ISheet *sht = pWorksheet->GetSheet();
	if (sht == NULL) return E_FAIL;

	if (ctx->getProtectionCtx()->isCellHidden(sht, row, col))
	{
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
	}

	WOLOG_INFO << "[area_transform] QuerySingleCellHistory sheetIdx: " << sheetIdx << ", row: " << row << ", col: " << col;

	ISheetStake *stake = sht->GetWoStake();
	if (stake == NULL) return E_FAIL;
	IHistoryInfo *pHistoryInfo = sht->GetWoStake()->getCellHistory();
	if (pHistoryInfo == NULL) return E_FAIL;

	acpt->addKey("history");
	pHistoryInfo->serialHistory(row, col, nRecent, acpt, true);

	return S_OK;
}

// ===================================================
QueryTotalHistoryCount::QueryTotalHistoryCount(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("querytotalHistoryCount"))
{
}

HRESULT QueryTotalHistoryCount::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	UINT64 totalCount = 0;
    ks_stdptr<Worksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();

    long sheetCount = 0;
    if (spWorksheets)
        spWorksheets->get_Count(&sheetCount);

    bool bFoundModified = false;
	for (IDX idx = 0; idx < sheetCount; idx++)
    {
        CELL firstCell = {-1, -1};
        ks_stdptr<ISheet> sht = spWorksheets->GetSheetItem(idx)->GetSheet();
		if (sht->IsDbSheet() || sht->IsAppSheet() || sht->IsWorkbenchSheet())
			continue;
		BOOL isVisible = TRUE;
		sht->GetVisible(&isVisible);
		if (isVisible)
		{
			UINT64 curCount = sht->GetWoStake()->getTotalHistoryCount(bFoundModified ? NULL : &firstCell, sht);
			const bool isCellHidden = ctx->getProtectionCtx()->isCellHidden(sht, firstCell.row, firstCell.col);
			if (!isCellHidden)
			{
				totalCount += curCount;
			}
			if (firstCell.row >= 0 && firstCell.col >= 0 && !isCellHidden)
			{
				if (!bFoundModified)
				{
					bFoundModified = true;
					IHistoryInfo *pHistoryInfo = sht->GetWoStake()->getCellHistory();
					if (pHistoryInfo)
						acpt->addInt32("commitID", pHistoryInfo->getCommitID(firstCell.row, firstCell.col));
					AbsObject *pSheetMain = ctx->getSheetMain(idx);
					if (pSheetMain)
						acpt->addFloat64("objSheet", pSheetMain->objId());
					acpt->addInt32("row", firstCell.row);
					acpt->addInt32("col", firstCell.col);
				}
			}
		}
    }

    acpt->addFloat64("cellCount", totalCount);

	return S_OK;
}

// ===================================================
EnumHistoryCell::EnumHistoryCell(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("enumHistoryCell"))
{
}

HRESULT EnumHistoryCell::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	IDX sheetIdx = GetSheetIdx(param, ctx);
    ROW row = param.field_int32("row");
    COL col = param.field_int32("col");
    bool forward = param.field_bool("forward");

    if (sheetIdx != INVALIDIDX)
    {
		ks_stdptr<ISheet> shtCheck = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx)->GetSheet();
		if (shtCheck && ctx->getProtectionCtx()->isCellHidden(shtCheck, row, col))
		{
			return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
		}

        ks_stdptr<_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
        ks_stdptr<Worksheets> spWorksheets = spWorkbook->GetWorksheets();
        ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(sheetIdx);
        IHistoryInfo *pHistoryInfo = NULL;
        if (spWorksheet)
        {
			CELL cell = {-1, -1};
            ks_stdptr<ISheet> sht = spWorksheet->GetSheet();
            BOOL isVisible = TRUE;
			sht->GetVisible(&isVisible);
			if (isVisible)
			{
				cell = sht->GetWoStake()->enumHistoryCell(row, col + (forward ? 1 : -1), forward, sht);
			}

            if (cell.row < 0 || cell.col < 0)
            {
                int offset = forward ? 1 : -1;
                sheetIdx += offset;

                long sheetCount = 0;
                spWorksheets->get_Count(&sheetCount);

                BMP_PTR pBMP = m_wwb->GetBMP();
                const CELL firstCell = {0, 0};
                const CELL lastCell = {pBMP->cntRows - 1, pBMP->cntCols - 1};
                const CELL startPos = forward ? firstCell : lastCell;

                for(; forward ? (sheetIdx < sheetCount) : (sheetIdx >= 0); sheetIdx += offset)
                {
                    ks_stdptr<IKWorksheet> _spWorksheet = spWorksheets->GetSheetItem(sheetIdx);
                    ks_stdptr<ISheet> _sht = _spWorksheet->GetSheet();
					if (_sht->IsDbSheet() || _sht->IsAppSheet() || _sht->IsWorkbenchSheet())
						continue;
					isVisible = TRUE;
					_sht->GetVisible(&isVisible);
					if (isVisible)
					{
						cell = _sht->GetWoStake()->enumHistoryCell(startPos.row, startPos.col, forward, _sht);
						if (cell.row >= 0 && cell.col >= 0) // FOUND IN OTHER SHEET
						{
							pHistoryInfo = _sht->GetWoStake()->getCellHistory();

							AbsObject *pSheetMain = ctx->getSheetMain(sheetIdx);
							if (pSheetMain)
								acpt->addFloat64("objSheet", pSheetMain->objId());
							acpt->addInt32("row", cell.row);
							acpt->addInt32("col", cell.col);
							acpt->addInt32("commitID", pHistoryInfo->getCommitID(cell.row, cell.col));
							break;
						}
					}
                }
            }
            else // FOUND IN CURRENT SHEET
            {
                pHistoryInfo = sht->GetWoStake()->getCellHistory();

                acpt->addFloat64("objSheet", ctx->getSheetMain(sheetIdx)->objId());
                acpt->addInt32("row", cell.row);
                acpt->addInt32("col", cell.col);
                acpt->addInt32("commitID", pHistoryInfo->getCommitID(cell.row, cell.col));
            }
        }
    }

    acpt->addBool("forward", forward);

	return S_OK;
}

QueryCanUnlockArea::QueryCanUnlockArea(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.canUnlockArea"))
{

}

HRESULT QueryCanUnlockArea::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX iSheet = param.field_int32("sheetIdx");
	IKWorksheet* pWs = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWs)
		return S_FALSE;

	WebStr password = param.field_str("password");

	bool bRes = false;
	WebStr strCorrectId = __X("");
	VarObj arrId = param.get_s("arrId");
	for (int i = 0; i < arrId.arrayLength_s(); i++)
	{
		WebStr id = arrId.item_str(i);
		if (SUCCEEDED(pWs->GetProtection()->ValidateAreaPassword(id, password)))
		{
			bRes = true;
			strCorrectId = id;
			break;
		}
	}

	if (bRes)
	{
		ks_castptr<IKETUserConn> user = ctx->getUser();
		user->markProtectionAreaUnlock(strCorrectId);
	}
	acpt->addBool("canUnlock", bRes);
	acpt->addString("correctId", strCorrectId);
	return S_OK;
}

QueryNextUnlock::QueryNextUnlock(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("sheet.getNextUnlock"))
{

}

HRESULT QueryNextUnlock::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX sheetIdx = GetSheetIdx(param, ctx);
	IKWorksheet* pWs = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWs)
		return S_FALSE;
	ks_stdptr<ISheet> pSheet = pWs->GetSheet();

	CELL cell;
	cell.row = param.field_int32("row");
	cell.col = param.field_int32("col");

	DIR dir = dirNone;
	ks_wstring strDir = param.field_str("dir");
	if (strDir == __X("dirUp"))
		dir = dirUp;
	else if (strDir == __X("dirDown"))
		dir = dirDown;
	else if (strDir == __X("dirLeft"))
		dir = dirLeft;
	else if (strDir == __X("dirRight"))
		dir = dirRight;

	et_sdptr<ISheetEnum> spEnum;
	VS(pSheet->CreateEnum(&spEnum));
	CELL NextCell = cell;
	spEnum->NextUnlock(cell, dir, &NextCell);

	acpt->addInt32("row", NextCell.row);
	acpt->addInt32("col", NextCell.col);
	return S_OK;
}

QueryValidateProtectSheetPassword::QueryValidateProtectSheetPassword(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("sheet.validateProtectSheetPassword"))
{

}

HRESULT QueryValidateProtectSheetPassword::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX iSheet = param.field_int32("sheetIdx");
	IKWorksheet* pWs = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWs)
		return S_FALSE;

	BOOL isOk = FALSE;
	BOOL isAutoPwdWrong = FALSE;

	ISheetProtection* pSheetProtection = pWs->GetProtection();
	if (param.has("password"))
	{
		WebStr password = param.field_str("password");
		isOk = pSheetProtection->IsPasswordMatched(password);
	}
	else
	{
		ks_wstring outPwd;
		if (!ctx->getProtectionCtx()->getAutoPassword(pSheetProtection->GetPasswordUUID(), &outPwd))
		{
			return E_FAIL;
		}

		isOk = pSheetProtection->IsPasswordMatched(outPwd.c_str());
		if (!isOk)
		{
			isAutoPwdWrong = TRUE;
		}
	}

	acpt->addBool("ok", isOk);
	ks_wstring skey;
	if (isOk)
	{
		skey = pWs->GetProtection()->GetSecretKey();
	}

	if (isAutoPwdWrong)
	{
		acpt->addBool("isAutoPwdWrong", true);
	}

	acpt->addString("secretKey", skey.c_str());
	return S_OK;
}

QueryValidateProtectSheetSecretKey::QueryValidateProtectSheetSecretKey(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("sheet.validateProtectSheetSecretKey"))
{

}

HRESULT QueryValidateProtectSheetSecretKey::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX iSheet = param.field_int32("sheetIdx");
	IKWorksheet* pWs = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWs)
		return S_FALSE;

	WebStr secretKey = param.field_str("secretKey");
	ks_wstring skey = pWs->GetProtection()->GetSecretKey();
	acpt->addBool("ok", skey == secretKey);
	return S_OK;
}

QueryGuessFormTitles::QueryGuessFormTitles(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("sheet.guessFormTitles"))
{

}

HRESULT QueryGuessFormTitles::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<ISheet> spSheet;
	IDX iSheet = GetSheetIdx(param, ctx);
	if (iSheet >= 0)
		m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(iSheet, &spSheet);

	VecStr vecTiltes;
	ROW row = param.field_int32("row");
	ROW titleRow = row;
	if (spSheet == NULL)
	{
		acpt->addString("err", __X("sheetNotExit"));
	}
	else if (row < 0)
	{
		titleRow = CollectTitles(ctx, vecTiltes, spSheet);
	}
	else
	{
		RANGE rg(spSheet->GetBMP());
		IDX iSht = 0;
		spSheet->GetIndex(&iSht);
		rg.SetRows(iSht, iSht, row, row);
		std::vector<CELL> mergeLeftTop;
		getRowMergeTops(spSheet, rg, mergeLeftTop, true);
		CollectRowCellsText(ctx, vecTiltes, spSheet, rg, &mergeLeftTop);
	}

	if (row < 0)
		acpt->addInt32("titlesRow", titleRow);
	else
		acpt->addInt32("row", row);

	acpt->addKey("titles");
	acpt->beginArray();
	for (size_t i = 0; i < vecTiltes.size(); ++i)
		acpt->addString(NULL, vecTiltes[i].c_str());
	acpt->endArray();
	return S_OK;
}

ROW QueryGuessFormTitles::CollectTitles(KEtRevisionContext* ctx, VecStr& vecTiltes, ISheet *pSheet)
{
	ROW fond = GetFilter(ctx, vecTiltes, pSheet);
	if (vecTiltes.size() > 0)
		return fond;

	fond = -1;
	RANGE rg(pSheet->GetBMP());
	IDX iSht = 0;
	pSheet->GetIndex(&iSht);
	rg.SetSheetFromTo(iSht);
	RECT rc = {0};
	pSheet->CalcUsedScale(&rc);
	rg.SetColFromTo(rc.left, rc.right);


	for (ROW row = 0; row < 10; ++row)
	{
		vecTiltes.clear();
		rg.SetRowFromTo(row);


		std::vector<CELL> mergeLeftTop;
		int invalidRowsCnt = getRowMergeTops(pSheet, rg, mergeLeftTop, false);
		if(invalidRowsCnt > 0)
		{
			row += invalidRowsCnt - 1;
			continue;
		}

		CollectRowCellsText(ctx, vecTiltes, pSheet, rg, &mergeLeftTop);

		if (vecTiltes.size() < 3)
			continue;

		fond = row;
		break;
	}

	if (fond < 0)
	{
		for (ROW row = 0; row < 10; ++row)
		{
			vecTiltes.clear();
			rg.SetRowFromTo(row);

			std::vector<CELL> mergeLeftTop;
			getRowMergeTops(pSheet, rg, mergeLeftTop, true);
			CollectRowCellsText(ctx, vecTiltes, pSheet, rg, &mergeLeftTop);

			if (vecTiltes.size() > 0)
			{
				fond = row;
				break;
			}
		}
	}

	return fond;
}

// 如果rg可以作为标题返回0 并收集合并单元格和左上角，不能作为标题时回返不能做为标题的行数。
int QueryGuessFormTitles::getRowMergeTops(ISheet *pSheet, const RANGE& rg, std::vector<CELL>& mergeLeftTop, bool isForce)
{
	ks_stdptr<IKRanges> spRes;
	pSheet->FindEffectMergeCell(rg, FALSE, &spRes);
	UINT cnt = 0;
	spRes->GetCount(&cnt);
	ROW rowCanUse = rg.RowFrom();
	for (UINT i = 0; i < cnt; ++i)
	{
		const RANGE* pRg = NULL;
		spRes->GetItem(i, NULL, &pRg);
		if(pRg)
		{
			if (pRg->Width() > 1 && pRg->RowTo() + 1 > rowCanUse)
				rowCanUse = pRg->RowTo() + 1;

			if (pRg->Height() > 1)
			{
				if (rowCanUse == rg.RowFrom() || isForce)
				{
					CELL c = {0};
					c.col = pRg->ColFrom();
					c.row = pRg->RowFrom();
					mergeLeftTop.push_back(c);
				}

				if (pRg->RowTo() > rowCanUse)
					rowCanUse = pRg->RowTo();
			}

		}
	}
	if (rowCanUse != rg.RowFrom() && !isForce)
		mergeLeftTop.clear();

	return rowCanUse - rg.RowFrom();
}

void QueryGuessFormTitles::CollectRowCellsText(KEtRevisionContext* ctx, VecStr& vecTiltes, ISheet *pSheet, const RANGE& rg, std::vector<CELL>* mergeLeftTop)
{
	constexpr int maxTiltes = 100;

	int curTop = 0;

	for (COL c = pSheet->SeekNextCellInRow(rg.RowFrom(), rg.ColFrom(), FALSE);
		c <= rg.ColTo() && c >= 0 && vecTiltes.size() < maxTiltes;
		c = pSheet->SeekNextCellInRow(rg.RowFrom(), c + 1, FALSE))
	{
		ROW r =  rg.RowFrom();

		// 处理合并单元
		if (mergeLeftTop != nullptr)
		{
			for (; curTop < mergeLeftTop->size(); ++curTop)
			{
				ROW mergeCellCol = mergeLeftTop->at(curTop).col;
				if (mergeCellCol >= c)
					break;

				if (!ctx->getProtectionCtx()->isCellHidden(pSheet, mergeLeftTop->at(curTop).row, mergeCellCol))
				{
					ks_bstr str;
					ctx->getStringTools()->GetCellText(pSheet, mergeLeftTop->at(curTop).row, mergeCellCol, &str, NULL, -1, NULL);
					if (!str.empty())
						vecTiltes.push_back(str.c_str());
				}
			}

			if (mergeLeftTop->size() > curTop && mergeLeftTop->at(curTop).col == c)
			{
				r = mergeLeftTop->at(curTop).row;
				curTop++;
			}
		}

		if (!ctx->getProtectionCtx()->isCellHidden(pSheet, r, c))
		{
			ks_bstr str;
			ctx->getStringTools()->GetCellText(pSheet, r, c, &str, NULL, -1, NULL);
			if (!str.empty())
				vecTiltes.push_back(str.c_str());
		}
	}
}

ROW QueryGuessFormTitles::GetFilter(KEtRevisionContext* ctx, VecStr& vecTiltes, ISheet *pSheet)
{
	IKAutoFilter* pFilter = pSheet->GetAutoFilters()->GetFilter(NULL);
	if (pFilter == NULL)
		return -1;

	RANGE rg(pSheet->GetBMP());
	pFilter->GetFilterRange(&rg);
	if (!rg.IsValid())
		return -1;

	rg.SetRowTo(rg.RowFrom());
	CollectRowCellsText(ctx, vecTiltes, pSheet, rg, nullptr);
	return rg.RowFrom();
}

QuerySupBooksList::QuerySupBooksList(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.supBooksList"))
{

}

HRESULT QuerySupBooksList::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	acpt->addKey("supBooks");
	acpt->beginArray();

	int sheetIdx = -1;
	ISheet* pSheet = util::getConnSharedSheet(m_wwb, ctx);
	if (pSheet)
	{
		uint sheetId = pSheet->GetStId();
		m_wwb->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(sheetId, &sheetIdx);
	}

	ks_stdptr<ISupEditLinks> spEditlinks;
	getSupEditLinks(m_wwb, &spEditlinks);
	if(spEditlinks->BeginGetLinkInfo(sheetIdx) == S_OK) 
	{
		while (true)
		{
			ks_wstring fullName, strItemName;
			BOOL bUpdate = FALSE;
			if (spEditlinks->GetNextLinkInfo(fullName, strItemName, bUpdate) != S_OK)
				break;

			SUP_LINKS_INFO_STATUS slis = SLIS_Unknown;
			IDX iBook = -1;
			ks_bstr bsFullName(fullName.c_str());
			if (S_OK != spEditlinks->GetStatus(bsFullName, &slis, &iBook))
				continue;
			acpt->beginStruct();
			acpt->addInt16("iBook", iBook);
			acpt->addString("status", SLIS2Str(slis));
			acpt->endStruct();
		}
	}

	acpt->endArray();
	return S_OK;
}



QueryChartData::QueryChartData(KEtWorkbook* wb) : EtQueryExecBase(wb, __X("chart.queryChartData"))
{
}

HRESULT QueryChartData::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
    EnableProtectCompileFmla enableClFlmGuard;
    uint32 sheetIndex = param.field_uint32("sheetIndex");
    uint32 shapeId = param.field_uint32("shapeId");
    ks_stdptr<IKWorksheets> ptrWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
    ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIndex);
    if (!ptrWorksheet)
        return E_FAIL;

	if (_kso_GetWoEtSettings()->IsEnableNewUpdateRender())
		m_wwb->updateRenderShape(ptrWorksheet);
	ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
	oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
	if (!spCanvas)
		return E_FAIL;
	ks_castptr<EtShapeTree> shapeTree = spCanvas;
    const drawing::AbstractShape *shape = shapeTree->find(shapeId);
    if (shape)
    {
        if (shape->hasChart())
        {
            QString strSha1;
            QString clientSha1 = QString::fromUtf16(param.field_str("chartSha1"));
            QByteArray sha1, picData;
            if (shape->getOriginalPictureSha1(sha1))
            {
                strSha1 = QString(sha1);
                if (!clientSha1.isEmpty() && strSha1 == QString(clientSha1))
                {
                    acpt->addKey("chartData");
                    acpt->beginStruct();
                    acpt->addBool("needUpdate", false);
                    acpt->addString("chartSha1", krt::utf16(strSha1));
                    acpt->endStruct();
                    return S_OK;
                }
            }

            ks_castptr<chart::KCTChartLayer> chartLayer = shape->getChild(0);
            if (chartLayer)
            {
                acpt->addKey("chartData");
                acpt->beginStruct();
                acpt->addBool("needUpdate", true);
                acpt->addString("chartSha1", krt::utf16(strSha1));

                QRectF rcfShape = shape->getShapeBounds();
                acpt->getContext()->ViewRect2CoreRect(const_cast<drawing::AbstractShape *>(shape),
                                                      rcfShape, rcfShape);
                acpt->addKey("rect");
                acpt->beginStruct();
                {
                    acpt->addFloat64("left", rcfShape.left());
                    acpt->addFloat64("right", rcfShape.right());
                    acpt->addFloat64("top", rcfShape.top());
                    acpt->addFloat64("bottom", rcfShape.bottom());
                }
                acpt->endStruct();

                chartLayer->serialChartData(acpt);
                acpt->endStruct();
                return S_OK;
            }
        }
    }

    return E_FAIL;
}

QuerySlicerData::QuerySlicerData(KEtWorkbook* wb) : EtQueryExecBase(wb, __X("pivot_table.querySlicerData"))
{
}

HRESULT QuerySlicerData::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
    EnableProtectCompileFmla enableClFlmGuard;
    uint32 sheetIndex = param.field_uint32("sheetIndex");
    uint32 shapeId = param.field_uint32("shapeId");
    ks_stdptr<IKWorksheets> ptrWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
    ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIndex);
    if (!ptrWorksheet)
        return E_FAIL;

	ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
	oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
	if (!spCanvas)
		return E_FAIL;
	ks_castptr<EtShapeTree> shapeTree = spCanvas;
    const drawing::AbstractShape *shape = shapeTree->find(shapeId);
    if (shape)
    {
        if (shape->isSlicerShape())
        {
            QString strSha1;
            QString clientSha1 = QString::fromUtf16(param.field_str("slicerSha1"));
            QByteArray sha1, picData;
            if (shape->getOriginalPictureSha1(sha1))
            {
                strSha1 = QString(sha1);
                if (!clientSha1.isEmpty() && strSha1 == QString(clientSha1))
                {
                    acpt->addKey("_slicerData");
                    acpt->beginStruct();
                    acpt->addBool("needUpdate", false);
                    acpt->addString("slicerSha1", krt::utf16(strSha1));
                    acpt->endStruct();
                    return S_OK;
                }
            }

            IKETSlicerLayer* sclicerLayer = _etopl_GetEtSlicerLayer(const_cast<drawing::AbstractShape *>(shape));
            if (sclicerLayer)
            {
                acpt->addKey("_slicerData");
                acpt->beginStruct();
                acpt->addBool("needUpdate", true);
                acpt->addString("slicerSha1", krt::utf16(strSha1));

                QRectF rcfShape = shape->getShapeBounds();
                acpt->getContext()->ViewRect2CoreRect(const_cast<drawing::AbstractShape *>(shape),
                                                      rcfShape, rcfShape);
                acpt->addKey("rect");
                acpt->beginStruct();
                {
                    acpt->addFloat64("left", rcfShape.left());
                    acpt->addFloat64("right", rcfShape.right());
                    acpt->addFloat64("top", rcfShape.top());
                    acpt->addFloat64("bottom", rcfShape.bottom());
                }
                acpt->endStruct();

                sclicerLayer->SerialSlicerData(acpt);

                acpt->endStruct();
                return S_OK;
            }
        }
    }

    return E_FAIL;
}


//////////////////////////////////////////////////////////////////////////
QuerySheetBackground::QuerySheetBackground(KEtWorkbook* wb)
: EtQueryExecBase(wb, __X("query.sheetBackground"))
{
}

HRESULT QuerySheetBackground::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX) return E_FAIL;
	ks_stdptr<ISheet> sht = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx)->GetSheet();

	ks_stdptr<IUnknown> spUnk;
	sht->GetExtDataItem(edSheetBackground, &spUnk);
	ks_stdptr<ISheetBGPicture> spBackground = spUnk;
	if (!spBackground)
		return E_FAIL;

	ks_stdptr<IKBlipAtom> spAtom;
	spBackground->GetBackgroundPicture(&spAtom);
	if (!spAtom)
		return E_FAIL;

	QString sha1 = util::UploadImage(spAtom);

	acpt->addString("sha1", krt::utf16(sha1));

	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
QueryEmpty::QueryEmpty(KEtWorkbook* wb) :
	EtQueryExecBase(wb, __X("query.empty"))
{
}

HRESULT QueryEmpty::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	//只为了能有query的回调函数。
	return S_OK;
}


QueryCustomNF::QueryCustomNF(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.customNF"))
{
}

HRESULT QueryCustomNF::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<INumberFmts> spNFs;
	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	spWorkbook->GetBook()->GetNumberFmts(&spNFs);
	if (spNFs == nullptr) return E_FAIL;

	spNFs->ResetEnum();

	acpt->addKey("customNF");
	acpt->beginArray();
		const NUMFMT *pNF;
		while(spNFs->Next(&pNF) == S_OK)
			if (pNF) acpt->addString(NULL, pNF->fmt);
	acpt->endArray();

	acpt->addInt32("fixedNumFmts", spWorkbook->GetFixedNumFmts());

	return S_OK;
}

QueryShapeCopy::QueryShapeCopy(KEtWorkbook* wb) : EtQueryExecBase(wb, __X("shape.copy"))
{
}

void QueryShapeCopy::findChartID(drawing::AbstractShape* pShape, drawing::ShapeTree* cpTree, std::vector<QString>& results)
{
	if (pShape->getModelType() == MODEL_CHART)
	{
		ks_castptr<chart::KCTChartLayer> kctChartLayer = pShape;
		QString uuid = cpTree->getChartId(kctChartLayer->ensureChart());
		if (!uuid.isEmpty())
			results.push_back(uuid);
	}
	else if (pShape->hasChart())
	{
		AbstractModel* child = pShape->getChild(0);
		ks_castptr<drawing::AbstractShape>absShape = child;
		findChartID(absShape, cpTree, results);
	}
}

void QueryShapeCopy::GetAbsShapeVect(const binary_wo::VarObj param, KEtRevisionContext* pCtx, std::vector<drawing::AbstractShape*>& results)
{
	IDX iSheet = GetSheetIdx(param, pCtx);
	VarObj shapes = param.get_s("shapes");
	const int count = shapes.arrayLength_s();
	for (int i = 0; i < count; i++) 
	{
		VarObj item = shapes.at_s(i);
		binary_wo::VarObj objIdxPath = item.get("shapeIdxPath");
		ShapeIdxPath idxPath;
		idxPath.reserve(objIdxPath.arrayLength());
		for (int i = 0, cnt = objIdxPath.arrayLength(); i < cnt; ++i)
			idxPath.push_back(objIdxPath.item_int32(i));

		drawing::AbstractShape* pShape = pCtx->getShapeByIdxPath(iSheet, idxPath);
		if(pShape == nullptr)
			return;
		results.push_back(pShape);
	}
}



HRESULT QueryShapeCopy::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    acpt->addInt32("copyId", param.field_int32("copyId"));
	if (gs_callback && gs_callback->setNeedDelayCopy) {
    	gs_callback->setNeedDelayCopy(1);
	}
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (CopyLink::isSuppportCopyLink(pBook))
	{
		std::vector<drawing::AbstractShape*> shapes;
		GetAbsShapeVect(param, ctx, shapes);
		if (!shapes.empty())
		{
			IDX iSheet = GetSheetIdx(param, ctx);
			if (iSheet == INVALIDIDX)
				return S_OK;
			UINT sheetStId = 0;
			HRESULT hr = pBook->RTSheetToSTSheet(iSheet, &sheetStId);
			if (FAILED(hr)) return S_OK;//不影响正常copy功能
			ks_stdptr<ISheet> spSheet;
			pBook->GetSheet(iSheet, &spSheet);
			ks_stdptr<IUnknown> ptrUnk;
			ks_stdptr<IKDrawingCanvas> ptrDrawingCanvas;
			spSheet->GetExtDataItem(edSheetDrawingCanvas, &ptrUnk);
			if (ptrUnk == NULL) return E_FAIL;
			ptrDrawingCanvas = ptrUnk;
			ks_castptr<drawing::ShapeTree> cpTree = ptrDrawingCanvas;
			if (nullptr != cpTree)
			{
				std::vector<QString> uuids;
				for (auto it = shapes.begin(); it != shapes.end(); it++) 
				{
					findChartID(*it, cpTree, uuids);
				}
				ASSERT(!uuids.empty());
				if (!uuids.empty())
				{	
					acpt->addKey("chartUUIDs");
					acpt->beginArray();
						for (auto it = uuids.begin(); it != uuids.end(); it++)
						{
							acpt->beginStruct();
							acpt->addString("uuid",krt::utf16(*it));
							
							acpt->addInt32("sheetStId", sheetStId);
							acpt->endStruct();
						}
					acpt->endArray();
				}
				acpt->addBool("hasHidden", m_wwb->isBookProtectedWithHiddenProperty());
			}
		}
	}
	CollectSecDocInfo(ctx, "copy", GetTag());
    return S_OK;
}

QueryDocSlimStat::QueryDocSlimStat(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.docSlimStat"))
{
}

HRESULT QueryDocSlimStat::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	const auto beginSlim {std::chrono::high_resolution_clock::now()};
	CheckDocSlimHelper hlp(m_wwb->GetDocSlim());
	hlp.Check(acpt, ctx->getUser()->connID());

	int version = 0;
	WebFileSize size = 0;
	GetFileSizeAndVersion(version, size);
	acpt->addFloat64("size", size);
	const auto endSlim {std::chrono::high_resolution_clock::now()};
	double timeConsumed = std::chrono::duration<double>(endSlim - beginSlim).count();

	QString info = QString("behaviour.checkslim_%1_%2").arg(timeConsumed).arg(false);
	ctx->collectInfo(info.toUtf8());

	return S_OK;
}

QueryNeedDocSlim::QueryNeedDocSlim(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.needDocSlim"))
{
}

void QueryNeedDocSlim::_CollectInfo(KEtRevisionContext* ctx, double timeConsumed)
{
	const bool hasProtection = ctx->getProtectionCtx()->isBookHasSheetProtected();
	if (hasProtection)
	{
		IEtProtectionCtx* protectionCtx = ctx->getProtectionCtx();
		const int hasHidden = protectionCtx->isBookHasHidden();
		const int hasReadonly = protectionCtx->isBookHasReadonly();
		const int hasEditable = protectionCtx->isThisBookHasEditable();

		QString info = QString("behaviour.sheetprotectarea_%1_%2_%3").arg(hasEditable).arg(hasReadonly).arg(hasHidden);
		ctx->collectInfo(info.toUtf8());
	}

	QString info = QString("behaviour.checkslim_%1_%2_%3_%4").arg(timeConsumed).arg(m_cache.bAutoSlim).arg(m_cache.hasInvisiblePicture).arg(m_cache.exceedShapesLimit);
	ctx->collectInfo(info.toUtf8());
}

HRESULT QueryNeedDocSlim::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	if (not ExecDocSlimHelper::isNeedSlim())
	{
		acpt->addBool("exceedShapesLimit", false);
		acpt->addBool("hasInvisiblePicture", false);
		acpt->addBool("bAutoSlim", false);
		return S_OK;
	}

	const auto beginSlim {std::chrono::high_resolution_clock::now()};
	
	updateCache(ctx->getBaseTaskVersion());
	

	// 对象超过10000个
	// 或文件大于30MiB且对象超过50个
	acpt->addBool("exceedShapesLimit", m_cache.exceedShapesLimit);

	// 文件大于30MiB且包含不可见对象
	acpt->addBool("hasInvisiblePicture", m_cache.hasInvisiblePicture);

	// 自动瘦身
	acpt->addBool("bAutoSlim", m_cache.bAutoSlim);

	const auto endSlim {std::chrono::high_resolution_clock::now()};
	double timeConsumed = std::chrono::duration<double>(endSlim - beginSlim).count();

	// 放这里，以后可以统一为一个命令
	_CollectInfo(ctx, timeConsumed);

	return S_OK;
}

HRESULT QueryNeedDocSlim::updateCache(WebInt currentVersion)
{
	// 版本没变化就不需要更新缓存了
	if (m_cache.isValid && currentVersion != m_cache.version)
		m_cache.isValid = false;
	if (m_cache.isValid)
		return S_OK;

	int version = 0;
	WebFileSize size = 0;
	GetFileSizeAndVersion(version, size);
	bool exceedFileSizeLimit = size > ExecDocSlimHelper::fileSizeLimit;

	IET_DocSlim *pDocSlim = m_wwb->GetDocSlim();
	if (pDocSlim == nullptr)
		return E_FAIL;

	size_t nShapeCount = 0; //对象数量
	size_t nShapeCountValide = 0;//不包含云附件对象数量
	size_t nShapeInvisible = 0; //不可见对象数量

	pDocSlim->CheckAllShapes();
	nShapeCount = pDocSlim->GetAllShapesCount();

	pDocSlim->CheckUnusedShapes(nullptr, _kso_GetWoEtSettings()->GetDocSlimInvisibleObjectTriggerValue());
	nShapeInvisible = pDocSlim->GetUnusedShapesCount();
	if (exceedFileSizeLimit)
	{
		if (nShapeCount > shapesLimit && nShapeCount < shapesHardLimit)
		{
			nShapeCountValide= getValidShapesCnt(shapesLimit);
		}
	}
	// 对象超过10000个
	// 或文件大于30MiB且对象超过50个
	m_cache.exceedShapesLimit = (nShapeCount > shapesHardLimit) || (exceedFileSizeLimit && (nShapeCountValide > shapesLimit));

	// 文件大于30MiB且包含不可见对象
	m_cache.hasInvisiblePicture = exceedFileSizeLimit && (nShapeInvisible > 0);

	// 自动瘦身
	m_cache.bAutoSlim = false;

	// 更新缓存
	m_cache.version = currentVersion;
	m_cache.isValid = true;

	IKDocSlimHelper* pDocSlimHelper = m_wwb->GetCoreWorkbook()->GetDocSlimHelper();
	if (pDocSlimHelper && !pDocSlimHelper->IsCanWoAutoSlim())
		return S_OK;

	if (pDocSlimHelper && pDocSlimHelper->IsProtectedDoc())
		return S_OK;

	if (nShapeInvisible >= _kso_GetWoEtSettings()->GetDocSlimInvisibleObjectTriggerValue())
	{
		m_cache.bAutoSlim = true;
		return S_OK;
	}

	pDocSlim->CheckOverlapShapes(nullptr, _kso_GetWoEtSettings()->GetDocSlimOverlapShapesTriggerValue());
	if (pDocSlim->GetOverlapShapeCount() >= _kso_GetWoEtSettings()->GetDocSlimOverlapShapesTriggerValue())
	{
		m_cache.bAutoSlim = true;
		return S_OK;
	}

	int64 iNullCellsCount = getNullCellsCount();
	if (_kso_GetWoEtSettings()->IsEnableAutoSlim() && iNullCellsCount >= _kso_GetWoEtSettings()->GetDocSlimNullCellsTriggerValue())
	{
		// 使用新瘦身算法
		m_cache.bAutoSlim = true;
		return S_OK;
	}

	pDocSlim->CheckUnusedDuplicateStyle();
	pDocSlim->CheckSameSheetProtectionUserRange();
	if (pDocSlim->GetUnusedDuplicateStylesCount() >= _kso_GetWoEtSettings()->GetDocSlimUnusedDuplicateStyleTriggerValue())
	{
		m_cache.bAutoSlim = true;
		return S_OK;
	}

	pDocSlim->CheckUnusedCellPictures();
	if (pDocSlim->GetUnusedCellPicturesCount() >= _kso_GetWoEtSettings()->GetDocSlimUnreferencedPictureTriggerValue())
	{
		m_cache.bAutoSlim = true;
		return S_OK;
	}

	pDocSlim->CheckDuplicateFormatCondition();
	if (pDocSlim->GetDuplicateFormatConditionCount() >= _kso_GetWoEtSettings()->GetDocSlimDuplicateFormatConditionTriggerValue())
	{
		m_cache.bAutoSlim = true;
		return S_OK;
	}

	return S_OK;
}

int QueryNeedDocSlim::getValidShapesCnt(int nLimit)
{
	ks_stdptr<_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	ks_stdptr<IBook> spBook = spWorkbook->GetBook();
	int nCountValide = 0;

	ks_stdptr<IKWorksheets> spWorksheets = spWorkbook->GetWorksheets();
	if (!spWorksheets)
		return nCountValide;

	int nSheets = spWorksheets->GetSheetCount();
	for (int idx = 0; idx < nSheets; ++idx)
	{
		ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(idx);

		BOOL bProtected = (spWorksheet->GetProtection()->IsAllowed(et_appcore::atProtect, NULL, NULL) == S_OK) ? FALSE : TRUE;
		if (bProtected)
			continue;

		ks_castptr<EtShapeTree> etShapeTree = spWorksheet->GetDrawingCanvas();
		if (!etShapeTree)
			continue;

		int nShapeCount = etShapeTree->childCount();
		nCountValide += nShapeCount - getCloudShapeCnt(etShapeTree);
		if (nLimit > 0 && nCountValide > nLimit)
			break;
	}

	return nCountValide;
}

int QueryNeedDocSlim::getCloudShapeCnt(EtShapeTree* shapeTree)
{
	if (!shapeTree) return 0;

	int ret = 0;
	int nCount = shapeTree->childCount();
	for(int i = 0; i < nCount; i++)
	{
		drawing::AbstractShape* pShape = shapeTree->childAt(i);

		if (!pShape->isPicture())
			continue;

		ks_bstr path;
		HGBL hgbl = NULL;
		IKBlipAtom* pBlipAtom = pShape->picID();
		if (pShape->isPicture() && pBlipAtom 
			&& pBlipAtom->GetLinkPath(&path) == S_OK && !path.empty())
		{
			ret++;
		}
	}
	return ret;
}

int64 QueryNeedDocSlim::getNullCellsCount()
{
	ks_stdptr<_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	ks_stdptr<IBook> spBook = spWorkbook->GetBook();
	ks_stdptr<IEtDocSlimData> spDocSlimData;
	spBook->GetExtDataItem(edBookDocSlimData, (IUnknown **)&spDocSlimData);
	if (!spDocSlimData)
		return 0;
	BookSlimData* pBookSlimData = spDocSlimData->GetBookDocSlimData();
	if (!pBookSlimData)
		return 0;
	return pBookSlimData->emptyCellCount;
}

CollectSecDocInfoCmd::CollectSecDocInfoCmd(KEtWorkbook *wb)
	: EtQueryExecBase(wb, __X("book.collectSecDocInfo"))
{
}

HRESULT CollectSecDocInfoCmd::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	if (m_wwb->isSecDoc())
	{
		QString actionName = QString::fromUtf16(param.field_str("actionName"));
		PCWSTR cmdType = param.field_str("cmdType");
		this->CollectSecDocInfo(ctx, actionName.toUtf8().constData(), cmdType);
	}

	return S_OK;
}

QueryHyperlink::QueryHyperlink(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.hyperlink"))
{
}

HRESULT QueryHyperlink::CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx)
{
	if(!isThisBookHasHiddenProp(ctx))
		return S_OK;

	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX)
		return E_FAIL;
	CELL cell;
	cell.row = param.field_int32("row");
	cell.col = param.field_int32("col");

	ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);
	return CheckProtectionCell(spSheet, cell.row, cell.col, ctx);
}

HRESULT QueryHyperlink::getRangesFromSubAddres(LPCWSTR lpwszSubAddress, IDX iSheet, CELL activeCell, range_helper::ranges& rgs)
{
	struct _hlp
	{
		static HRESULT GetSubAddressRanges(IBookOp* pBookOp, int iSheet, const CELL& activeCell, PCWSTR pwsz, IKRanges** ppRgs)
		{
			CS_COMPILE_PARAM prm(cpfAllowName, iSheet, activeCell.row, activeCell.col);
			HRESULT hr = pBookOp->CompileRange(pwsz, prm, ppRgs, croNeedCalc);
			if (FAILED(hr))
			{
				prm.of = cpfRCStyle | cpfAllowName;
				hr = pBookOp->CompileRange(pwsz, prm, ppRgs, croNeedCalc);
			}
			return hr;
		}
	};
	HRESULT hr = S_OK;

	ks_stdptr<IBookOp> spBookOp;
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
	
	hr = _hlp::GetSubAddressRanges(spBookOp, iSheet, activeCell, lpwszSubAddress, &rgs);
	if (FAILED(hr) && *lpwszSubAddress == __Xc('#'))
		hr = _hlp::GetSubAddressRanges(spBookOp, iSheet, activeCell, lpwszSubAddress + 1, &rgs);
	return hr;
}

HRESULT QueryHyperlink::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	HRESULT hr = S_OK;
	IDX sheetIdx = GetSheetIdx(param, ctx);
	ks_stdptr<IKWorksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(sheetIdx);
	if (spWorksheet == nullptr)
		return E_FAIL;
	CELL cell;
	cell.row = param.field_int32("row");
	cell.col = param.field_int32("col");
	ks_stdptr<etoldapi::Hyperlink> spHL;
	spWorksheet->HasHyperlink(cell, &spHL, TRUE);
	if (spHL)
	{
		ks_stdptr<IHyperlinkInfo> spHLInfo = spHL;
		ks_stdptr<IKHyperlink> spKHL;
		spHLInfo->GetETHyperlink(&spKHL);
		if (spKHL)
		{
			ks_bstr bstrAddress;
			ks_bstr bstrSubAddress;
			ks_bstr bstrSubAddressRgs;
			spKHL->GetAddress(&bstrAddress);
			spKHL->GetSubAddress(&bstrSubAddress);

			if (bstrAddress.empty() && !bstrSubAddress.empty())
			{
				range_helper::ranges rgs;
				hr = getRangesFromSubAddres(bstrSubAddress.c_str(), sheetIdx, cell, rgs);
				if (SUCCEEDED(hr) && rgs.size() > 0)
				{
					CS_COMPILE_FLAGS cf = cpfNormal | cpfRgForceSheet;
					CS_COMPILE_PARAM ccp(cf, 0, 0, 0);
					ks_stdptr<IBookOp> spBookOp;
					m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
					spBookOp->DecompileRange(rgs, ccp, &bstrSubAddressRgs);
				}
			}

			acpt->addKey("hyperlink");
			acpt->beginStruct();
				HYPERLINKTYPE type = HLinkNone;
				spKHL->GetHyperlinkType(&type);
				acpt->addUint32("type", type);

				if (!bstrAddress.empty())
					acpt->addString("address", bstrAddress);
				if (!bstrSubAddress.empty() || !bstrSubAddressRgs.empty())
					acpt->addString("subAddress", bstrSubAddressRgs.empty() ? bstrSubAddress : bstrSubAddressRgs);
			acpt->endStruct();
		}
	}

	return S_OK;
}

QuerySubscriptionInfo::QuerySubscriptionInfo(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.subscription"))
{
}

HRESULT QuerySubscriptionInfo::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	IBookStake *pBookStake = pBook->GetWoStake();
	ICbSubscribeOp *pSubOp = pBookStake->GetCbSubscribeOp();

	pSubOp->SerialiseCbRefers(acpt, "Refers");
	pSubOp->SerialiseCbSubscriptions(acpt, "Subscriptions");

	return S_OK;
}

QueryDocumentCustomProperty::QueryDocumentCustomProperty(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.documentCustomProperty"))
{
}

inline QDate QueryDocumentCustomProperty::fromSysDate(DATE date)
{
	return QDate(1900, 1, 1).addDays(date - 2);
}

HRESULT QueryDocumentCustomProperty::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	ks_stdptr<IKCoreObject> spCoreObj;
	spWorkbook->get_CustomDocumentProperties(&spCoreObj);
	if (!spCoreObj)
		return E_FAIL;
	ks_stdptr<DocumentProperties> spDocProps = spCoreObj;

	long nCount;
	if (S_OK != spDocProps->get_Count(&nCount))
		return E_FAIL;
	acpt->addKey("DocumentProperties");
	acpt->beginArray();
	for (int i = 0; i < nCount; ++i)
	{
		ks_stdptr<DocumentProperty> ptrDocProp;
		KComVariant varIndex(i + 1);
		if (S_OK != spDocProps->get_Item(varIndex, &ptrDocProp) && !ptrDocProp)
			continue;
		KsoDocProperties dataType;
		if (S_OK != ptrDocProp->get_Type(&dataType))
			continue;
		ks_bstr bstrName;
		if (S_OK != ptrDocProp->get_Name(&bstrName))
			continue;
		acpt->beginStruct();
		acpt->addString("name", bstrName.c_str());

		KComVariant varValue;
		if (S_OK == ptrDocProp->get_Value(&varValue))
		{
			QString strValue;
			if (ksoPropertyTypeString == dataType)
			{
				strValue = QString::fromUtf16((const ushort*)V_BSTR(&varValue));
			}
			else if (ksoPropertyTypeFloat == dataType)
			{
				strValue = QString::number(V_R8(&varValue));
			}
			else if (ksoPropertyTypeNumber == dataType)
			{
				strValue = QString::number(V_I4(&varValue));
			}
			else if (ksoPropertyTypeDate == dataType)
			{
				QDate date = fromSysDate(V_DATE(&varValue));
				strValue = date.toStringEx("yyyy-MM-dd");
			}
			else if (ksoPropertyTypeBoolean == dataType)
			{
				strValue = (V_BOOL(&varValue) ? "Yes" : "No");
			}
			acpt->addString("value", krt::utf16(strValue));
		}
		acpt->endStruct();
	}
	acpt->endArray();

	return S_OK;
}


QueryRangeValues::QueryRangeValues(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.rangeValues"))
{
}

class QueryRangeValues::KCellValueAcpt : public ICellValueAcpt
{
public:
    KCellValueAcpt(ISheet *sheet, ISheetEnum * stEnum, int *maxItems, RANGETYPE rgType, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
		: m_sheet(sheet), m_sheetEnum(stEnum), m_maxItems(maxItems), m_rgType(rgType), m_ctx(ctx), m_acpt(acpt)
    {
    }

    STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
    {
		if (m_sheetEnum->GetOneRowHidden(row))
		{
			return 0;  //继续枚举
		}

		BOOL hidden = FALSE;
		m_sheetEnum->GetColHidden(col, &hidden, 1);
		if (hidden)
		{
			return 0;  //继续枚举
		}

		if (m_ctx->getProtectionCtx()->isCellHidden(m_sheet, row, col))
		{
			return 0;  //继续枚举
		}

        if (pToken)
		{
            ks_bstr str;
            m_ctx->getStringTools()->GetCellText(pToken, NULL, &str);
            if (!str.empty())
			{
				QString text = QString::fromUtf16(str.c_str()).trimmed();
				if (text.size() > 0)
				{
					if (*m_maxItems == 0)
					{
						--*m_maxItems;
						return 1; //停止枚举
					}
					sa::Leave itemLeave = sa::enterStruct(m_acpt, nullptr);
					if (m_rgType != rtRows)
					{
						m_acpt->addInt32("row", row);
					}
					if (m_rgType != rtCols)
					{
						m_acpt->addInt32("col", col);
					}

					m_acpt->addString("text", krt::utf16(text));
					--*m_maxItems;
				}
            }
        }

        return 0;  //继续枚举
    };

private:
	ISheet *m_sheet;
	ISheetEnum *m_sheetEnum;
	RANGETYPE m_rgType;
    KEtRevisionContext* m_ctx;
    ISerialAcceptor* m_acpt;
	int *m_maxItems;
};

HRESULT QueryRangeValues::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	int maxItems = 3000;
	if (param.has("maxItems"))
	{
		maxItems = param.field_int32("maxItems");
	}

	{
		sa::Leave valuesLeave = sa::enterArray(acpt, "values");
		RANGE rg = ReadRange(param);
		if (!rg.IsValid())
			return S_OK;

		ks_stdptr<ISheet> spSheet;
		m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(rg.SheetFrom(), &spSheet);

		et_sdptr<ISheetEnum> spSheetEnum;
		spSheet->CreateEnum(&spSheetEnum);
		spSheetEnum->SetFilterContext(ctx->getFilterContext());


		ks_stdptr<IRangeInfo> host = m_wwb->CreateRangeObj(rg);
		ks_stdptr<IAppCoreRange> spCoreRange;
		host->GetAppCoreRange(&spCoreRange);
		if (!spCoreRange)
			return S_OK;

		range_helper::ranges rgs;
		if (spCoreRange->IsRangeInFilterMode())
			spCoreRange->GetFilteredIRanges(FALSE, &rgs);
		else
			host->GetIRanges(&rgs, TRUE);

		for (int i = 0; i < rgs.size(); ++i)
		{
			const RANGE *curRG = rgs[i].second;
			KCellValueAcpt cellValueAcpt(spSheet, spSheetEnum, &maxItems, curRG->RangeType(), ctx, acpt);
			spSheetEnum->EnumCellValueRowbyRow(*curRG, &cellValueAcpt);
		}
	}

	if (maxItems < 0)
	{
		acpt->addBool("hasMoreItems", true);
	}
	return S_OK;
}

QueryCommentInfoById::QueryCommentInfoById(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.commentInfoById"))
{
}

HRESULT QueryCommentInfoById::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	// 读取评论ID
	PCWSTR commentId = param.field_str("commentId");

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	CELL cmtPos;
	bool isFind = false;
	INT32 sheetCnt = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount();
	// 遍历每一个sheet
	for (INT32 shtIdx = 0; shtIdx < sheetCnt; shtIdx++)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(shtIdx, &spSheet);

		ks_stdptr<IKDrawingCanvas> spCommentCanvas;
		ks_stdptr<IUnknown> spUnk;
		if (SUCCEEDED(spSheet->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk)))
			spUnk->QueryInterface(IID_IKDrawingCanvas, (void**)&spCommentCanvas);
		if (!spCommentCanvas)
			continue;

		ks_stdptr<ICellComments> spCMTs = spCommentCanvas;
		std::set<ks_wstring> userIds;
		bool bIsResolved = false;
		ks_wstring chainId;
		isFind = FindCmtById(spCMTs, commentId, cmtPos, userIds, bIsResolved, chainId);
		if (isFind)
		{
			acpt->addKey("commentInfo");
			acpt->beginStruct();
				acpt->addBool("resolved", bIsResolved);
				acpt->addString("chainId", chainId.c_str());
				acpt->addKey("cellPos");
				acpt->beginStruct();
					acpt->addUint32("row", cmtPos.row);
					acpt->addUint32("col", cmtPos.col);
				acpt->endStruct();

				acpt->addKey("userIds");
				acpt->beginArray();
				for (const ks_wstring& userId : userIds)
				{
					acpt->beginStruct();
						acpt->addString("userId", userId.c_str());
					acpt->endStruct();
				}
				acpt->endArray();

				WebID objSheet = ctx->getSheetMain(shtIdx)->objId();
				acpt->addFloat64("objSheet", objSheet);
			acpt->endStruct();
			break;
		}
	}
	if(!isFind)
	{
		return  E_FAIL;
	}
	return S_OK;
}

bool QueryCommentInfoById::FindCmtById(ICellComments* pCellCmts, PCWSTR commentId, CELL& cmtPos, std::set<ks_wstring>& userIds, bool& bIsResolved, ks_wstring& chainId)
{
	INT cmtCnt;
	pCellCmts->GetCount(&cmtCnt);
	for (INT cmtIdx = 0; cmtIdx < cmtCnt; cmtIdx++)
	{
		ks_stdptr<ICellComment> spCellCmt;
		pCellCmts->GetItem(cmtIdx, &spCellCmt);
		if(!spCellCmt) continue;

		ks_stdptr<IWoComment> spWoCmt;
		spCellCmt->GetWoComment(&spWoCmt);
		if(!spWoCmt) continue;

		auto findCommentFromChains = [&](bool bIsResolved)
		{
			size_t chainCount = spWoCmt->GetChainCount(bIsResolved);
			for (size_t i = 0; i < chainCount; ++i)
			{
				IWoCommentChain* pWoCommentChain = spWoCmt->GetChainByIndex(bIsResolved, i);
				size_t itemCount = pWoCommentChain->Count();
				for (size_t j = 0; j < itemCount; ++j)
				{
					IWoCommentItem* pItem = pWoCommentChain->GetItem(j);
					PCWSTR itemId = pItem->GetId();
					if (xstrcmp(itemId, commentId) == 0)
					{
						userIds.clear();
						for(size_t k = 0; k < j; ++k)
							userIds.insert(pWoCommentChain->GetItem(k)->GetUserID());
						spCellCmt->GetBindCell(&cmtPos);
						chainId = pWoCommentChain->GetChainId();
						return true;
					}
				}
			}
			return false;
		};
		
		bool bFind = findCommentFromChains(false);
		if (bFind)
			return true;
		bFind = findCommentFromChains(true);
		if (bFind)
		{
			bIsResolved = true;
			return true;
		}
	}
	return false;
}

QueryPivotTableDefaultRange::QueryPivotTableDefaultRange(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.pivotTableDefaultRange"))
{
}

HRESULT QueryPivotTableDefaultRange::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	HRESULT hr = S_OK;
	ks_stdptr<etoldapi::Range> range = CreateRangeObj(param);
	if (!range)
	{
		return E_FAIL;
	}

	IDX iSheet = param.field_int32("sheetIdx");
	ks_stdptr<IKWorksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	if (!spWorksheets)
	{
		return E_FAIL;
	}
	ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(iSheet);
	if (!spWorksheet)
	{
		return E_FAIL;
	}
	ks_castptr<_Worksheet> spWorksheetApi = spWorksheet;

	if (param.has("defaultSrcRange") && param.field_bool("defaultSrcRange"))
	{
		ETReferenceStyle refStyle = etDefaultRefStyle;
		m_wwb->GetCoreApp()->get_ReferenceStyle(&refStyle);

		ks_bstr strRange;
		PivotHelpers::GetDefaultSourceRange(refStyle, range, spWorksheetApi, &strRange);
		if (strRange != NULL)
		{
			acpt->addString("defaultSrcRangeText", strRange.c_str());
		}
		else
		{
			acpt->addString("defaultSrcRangeText", __X(""));
		}
	}

	if (param.has("srcRange") && param.field_bool("srcRange"))
	{
		ks_stdptr<pivot_core::IPivotTable> spCorePivotTable;
    	ks_stdptr<etoldapi::PivotTable> spPivotTable;

		pivot_helper::GetCorePivotTableByCell(range, &spCorePivotTable);
		if (spCorePivotTable)
		{
			auto cache = spCorePivotTable->GetPivotCache();
			if (cache && cache->IsValid())
			{
				ks_castptr<pivot_core::ISrcDescription> pSrcDesc = cache->GetSrcDes();
				if (pSrcDesc)
				{
					RANGE rg(m_wwb->GetBMP());
					pSrcDesc->GetSourceData(&rg);
					acpt->addKey("sourceDataRange");
					acpt->beginStruct();
					WriteRANGE(rg, acpt);
					acpt->endStruct();
				}
			}
		}
	}

	return S_OK;
}

QueryPivotTableExtendRange::QueryPivotTableExtendRange(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.extendPivotTableSrcDataRange"))
{
}

HRESULT QueryPivotTableExtendRange::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	HRESULT hr = S_OK;
	RANGE rg = ReadRange(param);
	if (!rg.IsValid())
		return E_FAIL;

	IDX iSheet = param.field_int32("sheetIdx");
	ks_stdptr<IKWorksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	if (!spWorksheets)
		return E_FAIL;

	ks_stdptr<IKWorksheet> spWorksheet = spWorksheets->GetSheetItem(iSheet);
	if (!spWorksheet)
		return E_FAIL;

	ks_castptr<_Worksheet> spWorksheetApi = spWorksheet;
	ks_stdptr<IBook> spBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISheet> spSheet;
	spBook->GetSheet(iSheet, &spSheet);

	wo::util::EnumRangeVisibleCells(spSheet, rg, ctx, [iSheet, &rg] (ROW r, COL c, const_token_ptr pToken) -> bool {
		if (GetExecTokenMajorType(pToken) != alg::ETP_NONE && GetExecTokenMajorType(pToken) != alg::ETP_ERROR)
		{
			rg.SetCell(iSheet, r, c);
			return true;
		}

		return false;
	});

	ks_stdptr<etoldapi::Range> range = m_wwb->CreateRangeObj(rg);
	if (!range)
		return E_FAIL;

	ETReferenceStyle refStyle = etDefaultRefStyle;
	m_wwb->GetCoreApp()->get_ReferenceStyle(&refStyle);

	ks_bstr strRange;
	PivotHelpers::GetDefaultSourceRange(refStyle, range, spWorksheetApi, &strRange);
	ks_wstring extRgText;
	if (strRange != NULL)
	{
		if (ctx->getProtectionCtx()->isBookHasHidden())
		{
			ks_stdptr<IBookOp> spBookOp;
			m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);

			range_helper::ranges rgs;
			CS_COMPILE_FLAGS ccf = cpfSysDefault | cpfAllowName | cpfRgForceSheet | cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo;
			CS_COMPILE_PARAM ccp(ccf, iSheet, 0, 0);
			spBookOp->CompileRange(strRange.c_str(), ccp, &rgs, croNeedCalc);
			if (rgs.size() == 1)
			{
				if (!ctx->getProtectionCtx()->isRangeHasHidden(*rgs[0].second))
				{
					ks_bstr text;
					wo::util::Range2FormulaText(spBookOp, *rgs[0].second, ccp, &text);
					if (text)
					{
						extRgText = text.c_str();
					}
				}
			}
		}
		else
		{
			extRgText = strRange.c_str();
		}
	}

	acpt->addString("extendSrcRangeText", extRgText.c_str());
	return S_OK;
}

QueryNewCommentCount::QueryNewCommentCount(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.newCommentCount"))
{
}

HRESULT QueryNewCommentCount::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	double timeStamp = param.field_double("timeStamp");

	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	wo::IBookStake *pBookStake = pBook->GetWoStake();
	ICommentContainerBL* pCmtContanerBL = pBookStake->GetCommentContainerBL();
	ICommentContainerBL::cmtItemsInfo newItems;
	INT newCommentCnt = pCmtContanerBL->getNewCommentCount(timeStamp, newItems);
	{
		acpt->addUint32("newCommentCnt", newCommentCnt);
		acpt->addFloat64("timeStamp", timeStamp);
		acpt->addKey("newCommentsInfo");
		acpt->beginArray();
		for (auto itor = newItems.begin(); itor != newItems.end(); itor++)
		{
			acpt->beginStruct();
				acpt->addString("uuid", itor->first);
				acpt->addFloat64("dateTime", itor->second);
			acpt->endStruct();
		}
    	acpt->endArray();
	}

	return S_OK;
}

QueryIsMergeTaskValid::QueryIsMergeTaskValid(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.isMergeTaskValid"))
{
}

HRESULT QueryIsMergeTaskValid::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	if (!param.has("listSrcFile"))
	{
		acpt->addBool("taskValid", false);
		acpt->addString("failReason", __X("emptyListSrcFile"));
		return S_OK;
	}
	VarObj listSrcFile = param.get("listSrcFile");

	ks_stdptr<Workbooks> ptrWorkbooks = NULL;
	m_wwb->GetCoreApp()->get_Workbooks(&ptrWorkbooks);

	ks_stdptr<_Workbook> ptrWorkbookDst;
	int nDstBookIdx = 1;
	ptrWorkbooks->get_Item(KComVariant(nDstBookIdx, VT_I4), &ptrWorkbookDst);
	if (!ptrWorkbookDst)
	{
		acpt->addBool("taskValid", false);
		acpt->addString("failReason", __X("destBookNotExist"));
		return S_OK;
	}

	// 检查是否含有不可见区域
	const bool isWorkbookDstHidden = ctx->getProtectionCtx()->isBookHasHiddenProperty(ptrWorkbookDst->GetBook());
	if (isWorkbookDstHidden)
	{
		acpt->addBool("taskValid", false);
		acpt->addString("failReason", __X("destHiddenRange"));
		return S_OK;
	}

	// 检查源工作簿是否含有不可见
	int nListLength = listSrcFile.arrayLength();
	WebMimeHelper wmh;
	wmh.ctx = ctx;
	wmh.strField = "uploadId";
	for(int i = 0; i < nListLength; i++)
	{
		VarObj srcFile = listSrcFile.at(i);
		wmh.objParam = srcFile;

		QString strFilePath = wmh.resolvePath();
		ks_stdptr<_Workbook> ptrWorkbookSrc = util::OpenFile(m_wwb, ptrWorkbooks, krt::utf16(strFilePath));
		if (ptrWorkbookSrc == NULL)
		{
			acpt->addBool("taskValid", false);
			acpt->addString("failReason", __X("openSrcFileFailed"));
			acpt->addString("srcFileId", listSrcFile.field_str("fileId"));
			acpt->addString("srcDocName", listSrcFile.field_str("docName"));
			util::CloseFile(ptrWorkbookSrc);
			return S_OK;
		}

		VarObj mergeSrcRgInfo;
		if(srcFile.has("srcRangeInfo"))
		{
			ks_stdptr<etoldapi::Worksheets> spSheetsSrc = NULL;
			ptrWorkbookSrc->get_Worksheets(&spSheetsSrc);
			if (!spSheetsSrc)
			{
				acpt->addBool("taskValid", false);
				acpt->addString("failReason", __X("openSrcFileFailed"));
				acpt->addString("srcFileId", listSrcFile.field_str("fileId"));
				acpt->addString("srcDocName", listSrcFile.field_str("docName"));
				util::CloseFile(ptrWorkbookSrc);
				return S_OK;
			}

			long cntSheetSrc = 0;
			spSheetsSrc->get_Count(&cntSheetSrc);
			using _hasher = kfc::tools::str_hash_ic<ks_wstring>;
			using _comp = kfc::tools::str_equal_ic<ks_wstring>;
			std::unordered_set<ks_wstring, _hasher, _comp> sheetNames;
			for(int j = 0; j < cntSheetSrc; j++)
			{
				ks_bstr bstrSheetName;
				ks_stdptr<IKCoreObject> spCoreObjSrc = NULL;
				spSheetsSrc->get_Item(KComVariant(j + 1, VT_I4), &spCoreObjSrc);
				ks_stdptr<etoldapi::_Worksheet> spSheetSrc = spCoreObjSrc;
				spSheetSrc->get_Name(&bstrSheetName);
				sheetNames.insert(ks_wstring(bstrSheetName));
			}

			mergeSrcRgInfo = srcFile.get_s("srcRangeInfo");
			for(int j = 0; j < mergeSrcRgInfo.arrayLength_s(); j++)
			{
				ks_wstring shtName = mergeSrcRgInfo.item_str(j);
				if(!sheetNames.count(shtName))
				{
					acpt->addBool("taskValid", false);
					acpt->addString("failReason", __X("sheetNameNotExist"));
					acpt->addString("srcFileId", listSrcFile.field_str("fileId"));
					acpt->addString("srcDocName", listSrcFile.field_str("docName"));
					acpt->addString("sheetName", shtName.c_str());
					util::CloseFile(ptrWorkbookSrc);
					return S_OK;
				}
			}
		}

		util::CloseFile(ptrWorkbookSrc);
	}

	acpt->addBool("taskValid", true);
	return S_OK;

}

QueryMergeTasksInfo::QueryMergeTasksInfo(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.getMergeFileInfos"))
{
}

HRESULT QueryMergeTasksInfo::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX shtCnt = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount();
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	sa::Leave leaveStruct = sa::enterArray(acpt, "mergeFileInfos");
	for(IDX shtIdx = 0; shtIdx < shtCnt; shtIdx++)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(shtIdx, &spSheet);

		std::vector<SMergeFileInfo> listMergeFile;
		ks_stdptr<IMergeFile> spMergeFile;
		spSheet->GetExtDataItem(edSheetMergeFile, (IUnknown**)&spMergeFile);
		ASSERT(spMergeFile != nullptr);

		spMergeFile->GetMergeFileInfo(listMergeFile);
		if(listMergeFile.empty()) continue;

		sa::Leave leaveStruct = sa::enterStruct(acpt, nullptr);
		acpt->addFloat64("objSheet", ctx->getSheetMain(shtIdx)->objId());
		acpt->addFloat64("mergeTime", (double)spMergeFile->GetMergeTime());
		acpt->addBool("isSubscribe", spMergeFile->GetIsAutoRefresh());
		acpt->addInt32("titleRowCnt", spMergeFile->GetTitleRowCnt());
		acpt->addInt32("taskVersion", spMergeFile->GetTaskVersion());
		acpt->addBool("isMarkDataSrc", spMergeFile->GetIsMarkDataSrc());
		acpt->addBool("isRemoveDuplicates", spMergeFile->GetIsRemoveDuplicates());
		acpt->addBool("isTaskToBeUpdated", spMergeFile->GetTaskState());

		ks_wstring strHr;
		ks_wstring fileId;
		ks_wstring bkName;
		NetFileRes res;
		spMergeFile->GetMFError(strHr, fileId, bkName, res);
		acpt->addString("mergeResult", strHr.c_str());
		if (xstricmp(strHr.c_str(), __X("S_OK")) != 0)
		{
			acpt->addString("errFileId", fileId.c_str());
			acpt->addString("errBookName", bkName.c_str());
			acpt->addInt32("errFileNetState", res);
		}

		sa::Leave leaveArray = sa::enterArray(acpt, "listMergeFile");
		int iListSize = listMergeFile.size();
		for (int i = 0; i < iListSize; i++)
		{
			sa::Leave leaveStruct = sa::enterStruct(acpt, nullptr);

			acpt->addString("fileId", listMergeFile[i].strId.c_str());
			acpt->addString("docName", listMergeFile[i].strDocName.c_str());
			acpt->addString("fileTime", listMergeFile[i].strTime.c_str());
			acpt->addUint32("fileVersion", listMergeFile[i].fileVersion);

			sa::Leave leveArrayIn = sa::enterArray(acpt, "srcRangeInfo");
			for(int j = 0; j < listMergeFile[i].vecSrcInfo.size(); j++)
			{
				sa::Leave leaveStructIn = sa::enterStruct(acpt, nullptr);
				acpt->addString("sheetName", listMergeFile[i].vecSrcInfo[j].sheetName.c_str());
			}
		}
	}
	return S_OK;
}


QuerySheetNameList::QuerySheetNameList(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.sheetNameList"))
{
}

HRESULT QuerySheetNameList::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX shtCnt = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount();
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	sa::Leave leaveStruct = sa::enterArray(acpt, "sheetNames");
	for(IDX shtIdx = 0; shtIdx < shtCnt; shtIdx++)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(shtIdx, &spSheet);

		PCWSTR str = NULL;
		spSheet->GetName(&str);
		SHEETSTATE state = ssVisible;
		spSheet->GetVisible(&state);
		if(state == ssVisible)
			acpt->addString(nullptr, str);
	}
	return S_OK;
}

QueryDiagnosisResult::QueryDiagnosisResult(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.diagnosisResult"))
{
}

HRESULT QueryDiagnosisResult::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IBookStake *pBookStake = m_wwb->GetCoreWorkbook()->GetBook()->GetWoStake();
	pBookStake->ExportDiagnosisResult(acpt);
	return S_OK;
}

QueryDiagnosisStart::QueryDiagnosisStart(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.diagnosisStart"))
{
}

HRESULT QueryDiagnosisStart::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IBookStake *pBookStake = m_wwb->GetCoreWorkbook()->GetBook()->GetWoStake();

	{
		const DiagnosisInfo info = pBookStake->GetCalcDiagnosisInfo();
		if (info.status == CALC_DIAGNOSIS_STATUS::Diagnosing)
		{
			acpt->addBool("succeeded", false);
			acpt->addString("status", __X("diagnosing"));
			if (info.initiatorIsHuman && info.initiatorUserId)
				acpt->addString("userId", info.initiatorUserId);
			acpt->addFloat64("beginMoment", static_cast<double>(info.beginMoment));
			return S_OK;
		}
	}

	// 发起诊断
	VS(pBookStake->StartCalcDiagnosis(ctx->getUser()->userID()));

	// 解析附加参数
	if (param.has("diagnoseMode") && param.get_s("diagnoseMode").type() == typeString)
	{
		WebStr mode = param.field_str("diagnoseMode");

		if (xstrcmp(__X("auto"), mode) == 0) // 如果 diagnoseMode 为 auto, 则发起重算
		{
			VarObj recalculateArgs = param.get_s("recalculateArgs");
			VS(util::BookRecalculateBehaviour(m_wwb, recalculateArgs));
		}
	}

	m_wwb->updateCalcDiagnosisStatus();
	m_wwb->updateCalcStatus(false, ctx);

	acpt->addBool("succeeded", true);

	{
		const DiagnosisInfo info = pBookStake->GetCalcDiagnosisInfo();
		if (info.status == CALC_DIAGNOSIS_STATUS::HaveDiagnosed)
		{
			// 如果发起的诊断很快完成, 那么一并诊断返回结果
			sa::Leave wrapStruct = sa::enterStruct(acpt, "diagnosisResult");
			pBookStake->ExportDiagnosisResult(acpt);
		}
		else if (info.status == CALC_DIAGNOSIS_STATUS::Diagnosing)
		{
			// 如果在诊断中, 则一并返回开始时刻
			acpt->addFloat64("beginMoment", static_cast<double>(info.beginMoment));
		}
	}

	return S_OK;
}


QueryDiagnosisStop::QueryDiagnosisStop(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.diagnosisStop"))
{
}

HRESULT QueryDiagnosisStop::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IBookStake *pBookStake = m_wwb->GetCoreWorkbook()->GetBook()->GetWoStake();
	const DiagnosisInfo info = pBookStake->GetCalcDiagnosisInfo();
	
	if (info.status != CALC_DIAGNOSIS_STATUS::Diagnosing)
	{
		acpt->addBool("succeeded", false);
		acpt->addString("status", __X("diagnosing"));
		return S_OK;
	}

	if (xstrcmp(ctx->getUser()->userID(), info.initiatorUserId) != 0)
	{
		acpt->addBool("succeeded", false);
		if (info.initiatorIsHuman && info.initiatorUserId)
			acpt->addString("userId", info.initiatorUserId);
		acpt->addFloat64("beginMoment", static_cast<double>(info.beginMoment));
		return S_OK;
	}

	VS(pBookStake->StopCalcDiagnosis(ctx->getUser()->userID()));
	m_wwb->updateCalcDiagnosisStatus();
	m_wwb->updateCalcStatus(false, ctx);

	acpt->addBool("succeeded", true);

	// 一并返回诊断结果
	ASSERT(pBookStake->GetCalcDiagnosisInfo().status == CALC_DIAGNOSIS_STATUS::HaveDiagnosed);
	{
		sa::Leave wrapStruct = sa::enterStruct(acpt, "diagnosisResult");
		pBookStake->ExportDiagnosisResult(acpt);
	}
	return S_OK;
}

QueryTextLinkPosition::QueryTextLinkPosition(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.textLinkPosition"))
{
}

HRESULT QueryTextLinkPosition::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	ks_stdptr<IBook> spBook = spWorkbook->GetBook();

	ks_stdptr<IKWorksheets> spWorksheets = spWorkbook->GetWorksheets();
	if (!spWorksheets)
		return E_FAIL;

	bool isURL = true;
	QString address;
	if (param.has("textLinkURL"))
	{
		address = QString::fromUtf16(param.field_str("textLinkURL"));
	}
	else
	{
		isURL = false;
		address = QString::fromUtf16(param.field_str("textLinkUUID"));
	}

	int sheetCount = spWorksheets->GetSheetCount();
	for (IDX idx = 0; idx < sheetCount; idx++)
	{
		ks_stdptr<ISheet> spSheet;
        spBook->GetSheet(idx, &spSheet);
        if (!spSheet)
			continue;

		if (QueryOnCellLinks(address, isURL, spSheet, param, ctx, acpt))
		{
			return S_OK;
		}

		if (QueryOnCommentLinks(address, isURL, spSheet, param, ctx, acpt))
		{
			return S_OK;
		}
	}

	return S_OK;
}

bool QueryTextLinkPosition::QueryOnCommentLinks(
	const QString &address,
	bool isURL,
	ISheet *spSheet,
	const VarObj& param,
	KEtRevisionContext* ctx,
	ISerialAcceptor* acpt)
{
	ks_stdptr<IKDrawingCanvas> spCommentCanvas;
	ks_stdptr<IUnknown> spUnk;
	if (SUCCEEDED(spSheet->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk)))
		spUnk->QueryInterface(IID_IKDrawingCanvas, (void**)&spCommentCanvas);
	if (!spCommentCanvas)
		return false;

	ks_stdptr<ICellComments> spCMTs = spCommentCanvas;
	INT cmtCnt = -1;
	spCMTs->GetCount(&cmtCnt);
	for (INT cmtIdx = 0; cmtIdx < cmtCnt; cmtIdx++)
	{
		ks_stdptr<ICellComment> spCellCmt;
		spCMTs->GetItem(cmtIdx, &spCellCmt);
		if(!spCellCmt) continue;

		ks_stdptr<IWoComment> spWoCmt;
		spCellCmt->GetWoComment(&spWoCmt);
		if(!spWoCmt) continue;

		auto findLinkFromChains = [&](bool bIsResolved)
		{
			size_t chainCount = spWoCmt->GetChainCount(bIsResolved);
			for (size_t i = 0; i < chainCount; ++i)
			{
				IWoCommentChain* pWoCommentChain = spWoCmt->GetChainByIndex(bIsResolved, i);
				size_t itemCount = pWoCommentChain->Count();
				for (size_t j = 0; j < itemCount; ++j)
				{
					IWoCommentItem* pItem = pWoCommentChain->GetItem(j);
					IHyperlinkRuns *pRuns = pItem->GetLinkRuns();
					if (!pRuns) continue;
					UINT count = pRuns->GetRunsCount();
					for (UINT k = 0; k < count; ++k)
					{
						IHyperlinkRun* run = pRuns->GetRun(k);
						if (!run) continue;
						
						ks_stdptr<IHyperlinkRunAddress> spAddress(run->GetProperty());
						if (spAddress)
						{
							QString url(QString::fromUtf16(spAddress->GetAddress()));
							bool isMatch = CheckSameUUID(url, address, isURL);
							if (isMatch)
							{
								CELL cmtPos = {0};
								IDX sheetIdx = INVALIDIDX;
								spCellCmt->GetBindCell(&cmtPos);
								spSheet->GetIndex(&sheetIdx);
								RANGE rg(m_wwb->GetBMP());
								rg.SetCell(sheetIdx, cmtPos.row, cmtPos.col);
								return WriteResult(sheetIdx, rg, ctx, acpt, true, bIsResolved, pWoCommentChain->GetChainId());
							}
						}
						
					}
				}
			}
			return false;
		};

		bool bFind = findLinkFromChains(false);
		if (bFind)
			return true;
		bFind = findLinkFromChains(true);
		if (bFind)
			return true;
	}
	return false;	
}

bool QueryTextLinkPosition::CheckSameUUID(const QString &url, const QString &matchStr, bool isURL)
{
	bool isMatch = false;
	if (isURL)
	{
		if (matchStr == url)
			isMatch = true;
	}
	else if (url.contains(m_rx))
	{
		QString uuid = GetKWProtocolUUID(url);
		if (uuid == matchStr)
			isMatch = true;
	}
	return isMatch;
}

bool QueryTextLinkPosition::QueryOnCellLinks(
	const QString &address,
	bool isURL,
	ISheet *spSheet,
	const VarObj& param,
	KEtRevisionContext* ctx,
	ISerialAcceptor* acpt)
{
	ks_stdptr<IUnknown> spUnk;
	spSheet->GetExtDataItem(edSheetHyperlinks, &spUnk);
	ASSERT(spUnk != NULL);
	if (!spUnk)
		return false;

	IDX shtIdx = alg::STREF_INV_SHEET;
	spSheet->GetIndex(&shtIdx);
	ks_stdptr<IKHyperlinks> spKHLs;
	VS(spUnk->QueryInterface(IID_IKHyperlinks, (void**)&spKHLs));
	spKHLs->ResetToFirst();
	ks_stdptr<IKHyperlink> spKHL;
	while (spKHLs->Next(&spKHL) == S_OK)
	{
		HYPERLINKTYPE hlType = HLinkNone;
		spKHL->GetHyperlinkType(&hlType);
		if (IsRunsLink(hlType))
		{
			ks_stdptr<IHyperlinkRuns> spRuns;
			spKHL->GetRuns(&spRuns);
			if (QueryOnRuns(shtIdx, address, isURL, spKHL, spRuns, ctx, acpt))
			{
				return true;
			}
		}
		spKHL.clear();
	}

	return false;
}

bool QueryTextLinkPosition::QueryOnRuns(
	IDX shtIdx,
	const QString& matchStr,
	bool isURL,
	IKHyperlink* spKHL,
	IHyperlinkRuns* spRuns,
	KEtRevisionContext* ctx,
	ISerialAcceptor* acpt)
{
	if (!spRuns)
		return false;

	UINT count = spRuns->GetRunsCount();
	for (UINT i = 0; i < count; ++i)
	{
		IHyperlinkRun* run = spRuns->GetRun(i);
		if (run)
		{
			ks_stdptr<IHyperlinkRunAddress> spAddress(run->GetProperty());
			if (spAddress)
			{
				QString url(QString::fromUtf16(spAddress->GetAddress()));
				bool isMatch = CheckSameUUID(url, matchStr, isURL);
				if (isMatch)
				{
					RANGE rg(m_wwb->GetBMP());
					HRESULT hr = spKHL->GetEffectRange(&rg);
					if (SUCCEEDED(hr))
						return WriteResult(shtIdx, rg, ctx, acpt, false);
					else
						return false;
				}
			}
		}
	}

	return false;
}

QString QueryTextLinkPosition::GetKWProtocolUUID(const QString& url)
{
	int index = url.indexOf('?');
	if (index >= 0)
	{
		QString argsPart = url.right(url.size() - index - 1);
		QStringList argsList = argsPart.split('&');
		QStringList uuidPart = argsList.filter("uuid");
		for (int i = 0; i < uuidPart.size(); i++)
		{
			const QString &part = uuidPart[i];
			if (part.startsWith("extraAttrs"))
				continue;

			index = part.indexOf('=');
			if (index >= 0)
			{
				return part.right(part.size() - index - 1).trimmed();
			}
		}
	}

	return QString();
}

bool QueryTextLinkPosition::WriteResult(IDX shtIdx, const RANGE& rg, KEtRevisionContext* ctx, ISerialAcceptor* acpt, bool bComment, bool bIsResolved, PCWSTR chainId)
{
	if (rg.IsValid())
	{
		acpt->addKey("textLinkInfo");
		acpt->beginStruct();
		if (!ctx->getProtectionCtx()->isRangeHasHidden(rg))
		{
			acpt->addKey("cellPos");
			acpt->beginStruct();
			acpt->addUint32("row", rg.RowFrom());
			acpt->addUint32("col", rg.ColFrom());
			acpt->endStruct();
		}
		else
		{
			acpt->addBool("isHidden", true);
		}

		acpt->addBool("comment", bComment);
		if (bComment && bIsResolved)
		{
			acpt->addBool("resolved", bIsResolved);
			acpt->addString("chainId", chainId);
		}
		WebID objSheet = ctx->getSheetMain(shtIdx)->objId();
		acpt->addFloat64("objSheet", objSheet);
		acpt->endStruct();
		return true;
	}

	return false;
}

QueryImportrangeRefCellUrls::QueryImportrangeRefCellUrls(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.cellCalcRefUrls"))
{
}

HRESULT QueryImportrangeRefCellUrls::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	const RANGE range = ReadRange(param);
	IBookStake *pBookStake = m_wwb->GetCoreWorkbook()->GetBook()->GetWoStake();
    pBookStake->SerialImportrangeCellRefUrls(range.SheetFrom(), range.RowFrom(), range.ColFrom(), acpt);
	return S_OK;
}

QueryFormDataRow::QueryFormDataRow(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.formDataRow"))
{
}

HRESULT QueryFormDataRow::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	RANGE rg(pBook->GetBMP());
	HRESULT hr = TaskExecFormHelp::GetFormRg(pBook, ctx, rg,TaskExecFormBase::getAreaName(param));
	if (FAILED(hr))
		return hr;
	PCWSTR  strKey = param.field_str("key");
	int col = param.field_int32("fieldFind");
	if (col < 0 || col > rg.ColTo() )
	{
		WO_LOG_X(ctx->getLogger(), WO_LOG_ERROR, "[query.formDataRow] param is not right");
		return E_FAIL;
	}
	

	RANGE rgFind(rg);
	rgFind.SetColFromTo(rg.ColFrom() + col);
	ks_stdptr<IRangeInfo> spRgFind = m_wwb->CreateRangeObj(rgFind);
	ks_stdptr<IAppCoreRange> spCoreRgFind;
	spRgFind->GetAppCoreRange(&spCoreRgFind);

	FINDPARAM findparam ;
	findparam.sWhat.reserve(20);
	for (PCWSTR pCh = strKey; *pCh != __Xc('\0'); ++pCh)
	{
		switch(*pCh) 
		{
		case __Xc('?'):
		case __Xc('~'):
		case __Xc('*'):
			findparam.sWhat.push_back(__Xc('~'));
			break;
		}
		findparam.sWhat.push_back(*pCh);
	}

	findparam.cellAfter.row = rg.RowFrom();
	findparam.cellAfter.col = rg.ColFrom();
	findparam.nACinRANGE = 0;
	findparam.efcLookIn = etFind_Smart;
	findparam.efcLookAt = etFind_Whole;
	findparam.bMatchCase = TRUE;
	findparam.bMatchByte = TRUE;
	findparam.bfindAll = TRUE;
	CELL cellFound = {0};
	hr = spCoreRgFind->Find(findparam, cellFound, findparam.nACinRANGE);
	if (SUCCEEDED(hr) && cellFound.row != -1)
	{
	   acpt->addInt32("row", cellFound.row);
	}
	else
	   acpt->addInt32("row", -1);

	handleHttpResponse(S_OK,cellFound.row,ctx);


	return S_OK;
}

HRESULT QueryFormDataRow::PostExecute(HRESULT hr, const VarObj& param, KEtRevisionContext* ctx)
{
	if (FAILED(hr))
	{
		handleHttpResponse(hr,-1,ctx);
	}

	return EtQueryExecBase::PostExecute(hr, param, ctx);
}

void  QueryFormDataRow::handleHttpResponse(HRESULT hr, int row,KEtRevisionContext* pCtx)
{
	binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
	KSerialWrapBinWriter bw(*pResponse, pCtx);
	sa::Leave warpArray = sa::enterArray(&bw, "extendQueryResult");
	sa::Leave wrapStruct = sa::enterStruct(&bw, nullptr);
	{
		sa::Leave wrapResult = sa::enterStruct(&bw, "result");
		bw.addInt32("row", row);
	}
	bw.addInt32("error", hr);
	bw.addString("name", GetTag());
}


QueryRangeData::QueryRangeData(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.exportRangeData"))
{
}

HRESULT QueryRangeData::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	const RANGE rg = ReadRange(param);
	if(!rg.IsValid())
	{
		WOLOG_INFO << "[ExportRangeData] param struct error";
		return E_FAIL;
	}
	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();

	ASSERT(rg.SheetFrom() == rg.SheetTo());
	IDX sheetIdx = rg.SheetFrom();
	IKWorksheet* pWorksheet = spWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (pWorksheet == NULL) 
	{
		WOLOG_INFO << "[ExportRangeData] get worksheet failed, sheet idx=" << sheetIdx;	
		return E_FAIL;
	}
	ks_stdptr<ISheet> spSheet = pWorksheet->GetSheet();
	if (spSheet == NULL) 
	{
		WOLOG_INFO << "[ExportRangeData] get sheet failed, sheet idx=" << sheetIdx;	
		return E_FAIL;
	}

	et_sdptr<ISheetEnum> spSheetEnum;
	spSheet->CreateEnum(&spSheetEnum);
	class CellValueAcpt : public ICellValueAcpt
	{
	public:
		CellValueAcpt(etoldapi::_Workbook* wb,int sheetIdx,ISheet *pSheet,ISerialAcceptor* acpt,wo::KEtRevisionContext* ctx)
			:m_sheetIdx(sheetIdx)
			,m_pSheet(pSheet)
			,m_acpt(acpt)
			,m_ctx(ctx)
			,m_pCellImages(nullptr)
		{
			wb->GetBook()->GetOperator(&m_op);
			m_pCellImages = wb->GetCellImages();
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if(!pToken)
				return 0;//继续枚举
			//判断读取的区域是否在保护区域内，若在，禁止读取
			bool bCellInvisibleForCurUser = m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col);
			if(bCellInvisibleForCurUser)
				return 0;//继续枚举

			wo::sa::Leave itemLeave = wo::sa::enterStruct(m_acpt, nullptr);

			int rowFrom = row;
			int rowTo = row;
			int colFrom = col;
			int colTo = col;

			BOOL bMerge = FALSE;
			m_pSheet->IsMerged(row, col, &bMerge);
			if (bMerge)
			{
				//合并单元格
				RANGE rgCell(m_pSheet->GetBMP());
				rgCell.SetCell(m_sheetIdx, row, col);

				ks_stdptr<IKRanges> ptrRgs;
				m_pSheet->FindEffectMergeCell(rgCell, FALSE, &ptrRgs);
				const RANGE* pMergeRg = NULL;
				ptrRgs->GetItem(0, NULL, &pMergeRg);
				ASSERT(pMergeRg);
				rowFrom = pMergeRg->RowFrom();
				rowTo = pMergeRg->RowTo();
				colFrom = pMergeRg->ColFrom();
				colTo = pMergeRg->ColTo();
			}

			m_acpt->addInt32("rowFrom", rowFrom);
			m_acpt->addInt32("rowTo", rowTo);
			m_acpt->addInt32("colFrom", colFrom);
			m_acpt->addInt32("colTo", colTo);

			//获取数据
			ks_bstr text;
			m_ctx->getStringTools()->GetCellText(m_pSheet, row, col, &text, NULL, -1, NULL);
			m_acpt->addString("cellText", text.c_str()  == NULL ? __X("") : text.c_str());

			ks_bstr originalCellValue;
			m_ctx->getStringTools()->GetCellText(pToken, NULL, &originalCellValue);
			m_acpt->addString("originalCellValue", originalCellValue.c_str() == NULL ? __X("") : originalCellValue.c_str());

			//数值格式类型
			const XF* pXF = NULL;
			m_op->GetCellFormat(m_sheetIdx, row, col, &pXF, NULL);
			const NUMFMT* pNumFmt = pXF->pNumFmt;
			PCWSTR pcwNumfmt = pNumFmt->fmt;
			m_acpt->addString("numFormat", pcwNumfmt == NULL ? __X("") : pcwNumfmt);

			//判断是否为单元格图片
			if(!alg::const_vstr_token_assist::is_type(pToken) || !m_pCellImages)
			{
				m_acpt->addBool("isCellPic", FALSE);
				return 0;//继续枚举
			}
				
			CellImg_Param param;
			ks_wstring strCellValue = alg::const_vstr_token_assist(pToken).get_value();
			if (m_pCellImages->GetCellImgParamFromCellValue(strCellValue.c_str(), &param) == FALSE)
			{
				m_acpt->addBool("isCellPic", FALSE);
				return 0;//继续枚举
			}
			//单元格图片	
			m_acpt->addBool("isCellPic", TRUE);
			IKShape* pShape = m_pCellImages->GetImgByName(param.getNameStr());
			ks_castptr<drawing::AbstractShape> spShape = pShape;
			if (!spShape)
			{
				WOLOG_INFO << "[ExportRangeData] get shape failed!";	
				m_acpt->addBool("isCellPic", FALSE);
				return 0;//继续枚举
			}
				
			IKBlipAtom* spBlipAtom = spShape->picID();
			if (!spBlipAtom)
			{
				WOLOG_INFO << "[ExportRangeData] get IKBlipAtom failed!";	
				m_acpt->addBool("isCellPic", FALSE);
				return 0;//继续枚举
			}
				
			ks_bstr strUrl;
			spBlipAtom->GetLinkPath(&strUrl);
			if (strUrl.empty())
			{
				// 本地图片(非在线图片)
				QByteArray sha1;
				bool uploaded = util::UploadImg(
					m_ctx->getUser()->getDpi(), spShape, sha1);
				if (not uploaded)
				{
					WOLOG_INFO << "[ExportRangeData] shape get Picture failed!";
					return 0; //继续枚举
				}
				m_acpt->addString("tag", __X("local"));
				m_acpt->addString("sha1",krt::utf16(QString(sha1)));
				return 0;//继续枚举
			}
			else
			{
				//在线图片(1附件图片;2.云空间图片)
				//判断是否为附件
				bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(strUrl.c_str());
				if(isAttachment)
				{
					m_acpt->addString("tag", __X("attachment"));
					m_acpt->addString("picData", util::getAttachmentId(strUrl));
					return 0;//继续枚举
				}
				else
				{
					m_acpt->addString("tag", __X("url"));
					m_acpt->addString("picData", strUrl.c_str());
					return 0;//继续枚举
				}
			}
					
			return 0;//继续枚举
		};

		int m_sheetIdx;
		ks_stdptr<IBookOp> m_op;
		ICellImages *m_pCellImages;
		ISheet *m_pSheet;
		wo::KEtRevisionContext *m_ctx;
		ISerialAcceptor* m_acpt;
	};
	
	acpt->addKey("rangeData");
	acpt->beginArray();

	CellValueAcpt cellValue(spWorkbook, sheetIdx, spSheet, acpt, ctx);
	int ret = spSheetEnum->EnumCellValue(rg, &cellValue);
	acpt->endArray();

	return S_OK;
}

QueryMapCellPos::QueryMapCellPos(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.mapCellPos"))
{
}

HRESULT QueryMapCellPos::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	const RANGE rg = ReadRange(param);
	if(!rg.IsValid())
	{
		WOLOG_INFO << "[QueryMapCellPos] param error";
		return E_FAIL;
	}

	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	IBookStake *pBookStake = pBook->GetWoStake();
	CELL orgcell {rg.RowFrom(), rg.ColFrom() };
	CELL cell = orgcell;
	IDX orgIdxSheet = rg.SheetFrom();
	IDX idxSheet = orgIdxSheet;
	bool reachOriginal = false;
	bool res = pBookStake->GetAreaTransformContainter()->Invert(idxSheet, cell, reachOriginal);

	acpt->addKey("org");
	acpt->beginStruct();
		acpt->addInt32("sheetIdx", orgIdxSheet);
		acpt->addInt32("row", orgcell.row);
		acpt->addInt32("col", orgcell.col);
	acpt->endStruct();

	acpt->addKey("trans");
	acpt->beginStruct();
		acpt->addInt32("sheetIdx", idxSheet);
		acpt->addInt32("row", cell.row);
		acpt->addInt32("col", cell.col);
	acpt->endStruct();

	WOLOG_INFO << "[QueryMapCellPos] res:" << res << ", orgsheet: " << orgIdxSheet << ", org:" << orgcell << ", trans:" << cell << ", idxSheet:" << idxSheet;
	return S_OK;
}

QueryControlARange::QueryControlARange(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.controlARange"))
{
}

HRESULT QueryControlARange::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	std::vector<RANGE> rgVec;
	ReadRangesInl(m_wwb->GetBMP(), GetSheetIdx(param, ctx), param, rgVec);
	if (rgVec.empty())
		return E_FAIL;
	
	RANGE range = rgVec[0];
	IDX sheetIdx = range.SheetFrom();
	ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);
	if (!spSheet)
		return E_FAIL;

	BMP_PTR ptrBmp = m_wwb->GetBMP();
	RANGE controlARange(ptrBmp);
	BOOL bSheets = TRUE;
	if (1 == rgVec.size())
	{
		appcore_helper::GetContinualRangeMax(spSheet, range, &controlARange);

		// 不能比 rg0 还小
		controlARange = controlARange.Union(range);
		if (!range.Compare(controlARange))
			bSheets = FALSE;

		LPCWSTR userId = ctx->getUser()->userID();
		ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(sheetIdx);
		ProtectionAccessPerms pap = pSheetProtection->GetAccessPermWidthRange(userId, controlARange);
		if (PTAAP_Invisible == pap)
			bSheets = TRUE;
	}

	if (bSheets)
	{
		controlARange.SetSheetFromTo(sheetIdx);
		controlARange.SetRowFromTo(0, ptrBmp->cntRows - 1);
		controlARange.SetColFromTo(0, ptrBmp->cntCols - 1);
	}

	acpt->addKey("controlARange");
	acpt->beginStruct();
		WriteRange(acpt, controlARange);
	acpt->endStruct();
	acpt->addFloat64("sheetStId", spSheet->GetStId());
	acpt->addFloat64("objSheet", ctx->getSheetMain(sheetIdx)->objId());
	return S_OK;
}

QueryRangeFirstRow::QueryRangeFirstRow(KEtWorkbook* wb)
        : EtQueryExecBase(wb, __X("query.rangeFirstRow"))
{
}

class QueryRangeFirstRow::KCellValueAcpt : public ICellValueAcpt
{
public:
    KCellValueAcpt(ISheet* sheet, KEtRevisionContext* ctx, std::map<COL, QString>& values)
            : m_sheet(sheet), m_ctx(ctx), m_values(values)
    {
    }

    STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
    {
        if (pToken && !m_ctx->getProtectionCtx()->isCellHidden(m_sheet, row, col))
        {
            ks_bstr str;
            m_ctx->getStringTools()->GetCellText(pToken, nullptr, &str);
            if (!str.empty())
                m_values[col] = QString::fromUtf16(str.c_str());
        }
        return 0;
    };
private:
    ISheet* m_sheet;
    KEtRevisionContext* m_ctx;
    std::map<COL, QString>& m_values;
};

HRESULT QueryRangeFirstRow::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    if (!param.has("range"))
        return E_FAIL;

    IDX sheetIdx = GetSheetIdx(param, ctx);
    if (sheetIdx == INVALIDIDX)
        return E_FAIL;

    int maxColumns = 50;
    if (param.has("maxColumns"))
        maxColumns = param.field_int32("maxColumns");

    ks_stdptr<IBookOp> spBookOp;
    IBook* spBook = m_wwb->GetCoreWorkbook()->GetBook();
    spBook->GetOperator(&spBookOp);
    if (spBookOp == nullptr)
        return E_FAIL;

    // 解析区域
    range_helper::ranges rgs;
    CS_COMPILE_FLAGS ccf = cpfSysDefault | cpfAllowName | cpfForbidCrossBook;
    CS_COMPILE_PARAM ccp(ccf, sheetIdx, 0, 0);
    spBookOp->CompileRange(param.field_str("range"), ccp, &rgs, croAllowNamedRefer);
    if (rgs.size() != 1)
        return E_FAIL;

    RANGE range = *rgs[0].second;
    if (!range.IsValid())
        return E_FAIL;

    // 裁减区域
    ROW firstRow = range.RowFrom();
    range.SetRowFromTo(firstRow, firstRow);
    COL firstColumn = range.ColFrom();
    COL lastColumn = range.ColTo();
    if (maxColumns > 0 && lastColumn - firstColumn >= maxColumns)
    {
        lastColumn = firstColumn + maxColumns - 1;
        range.SetColFromTo(firstColumn, lastColumn);
    }

    sheetIdx = range.SheetFrom();
    if (!util::IsValidSheetIdx(spBook, sheetIdx))
        return E_FAIL;

    ks_stdptr<ISheet> spSheet;
    spBook->GetSheet(sheetIdx, &spSheet);
    if (!spSheet)
        return E_FAIL;

    // 枚举区域数据
    std::map<COL, QString> values;
    et_sdptr<ISheetEnum> spSheetEnum;
    spSheet->CreateEnum(&spSheetEnum);
    spSheetEnum->SetFilterContext(ctx->getFilterContext());
    KCellValueAcpt cellValueAcpt(spSheet, ctx, values);
    spSheetEnum->EnumCellValueRowbyRow(range, &cellValueAcpt);
    {
        sa::Leave valuesLeave = sa::enterArray(acpt, "values");
        for (COL col = firstColumn; col <= lastColumn; col++)
        {
            sa::Leave itemLeave = sa::enterStruct(acpt, nullptr);
            acpt->addInt32("column", col);
            bool hidden = ctx->getProtectionCtx()->isCellHidden(spSheet, firstRow, col);
            acpt->addBool("hidden", hidden);
            auto iter = values.find(col);
            if (!hidden && iter != values.end())
                acpt->addString("text", krt::utf16(iter->second));
        }
    }
    acpt->addInt32("sheetIdx", sheetIdx);
    acpt->addInt32("firstRow", firstRow);
    return S_OK;
}

QueryRangeFirstCol::QueryRangeFirstCol(KEtWorkbook* wb)
        : EtQueryExecBase(wb, __X("query.rangeFirstCol"))
{
}

class QueryRangeFirstCol::KCellValueAcpt : public ICellValueAcpt
{
public:
    KCellValueAcpt(ISheet* pSheet, KEtRevisionContext* pCtx, std::map<ROW, QString>& values)
            : m_pSheet(pSheet), m_pCtx(pCtx), m_values(values)
    {
    }

    STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
    {
        if (pToken && !m_pCtx->getProtectionCtx()->isCellHidden(m_pSheet, row, col))
        {
            ks_bstr str;
            m_pCtx->getStringTools()->GetCellText(pToken, nullptr, &str);
            if (!str.empty())
                m_values[row] = krt::fromUtf16(str.c_str());
        }
        return 0;
    };
private:
    ISheet* m_pSheet;
    KEtRevisionContext* m_pCtx;
    std::map<ROW, QString>& m_values;
};

HRESULT QueryRangeFirstCol::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    if (!param.has("range"))
        return E_FAIL;

    IDX sheetIdx = GetSheetIdx(param, ctx);
    if (sheetIdx == INVALIDIDX)
        return E_FAIL;

    int maxRows = 50;
    if (param.has("maxRows"))
        maxRows = param.field_int32("maxRows");

    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    IBookOp* pBookOp = pBook->LeakOperator();
    if (pBookOp == nullptr)
        return E_FAIL;

    // 解析区域
    range_helper::ranges rgs;
    CS_COMPILE_FLAGS ccf = cpfSysDefault | cpfAllowName | cpfForbidCrossBook;
    CS_COMPILE_PARAM ccp(ccf, sheetIdx, 0, 0);
    pBookOp->CompileRange(param.field_str("range"), ccp, &rgs, croAllowNamedRefer);
    if (rgs.size() != 1)
        return E_FAIL;

    RANGE range = *rgs[0].second;
    if (!range.IsValid())
        return E_FAIL;

    // 裁减区域
    COL firstCol = range.ColFrom();
    range.SetColFromTo(firstCol, firstCol);
    ROW firstRow = range.RowFrom();
    ROW lastRow = range.RowTo();
    if (maxRows > 0 && lastRow - firstRow >= maxRows)
    {
        lastRow = firstRow + maxRows - 1;
        range.SetRowFromTo(firstRow, lastRow);
    }

    sheetIdx = range.SheetFrom();
    if (!util::IsValidSheetIdx(pBook, sheetIdx))
        return E_FAIL;

    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(sheetIdx, &spSheet);
    if (!spSheet)
        return E_FAIL;

    // 枚举区域数据
    std::map<ROW, QString> values;
    et_sdptr<ISheetEnum> spSheetEnum;
    spSheet->CreateEnum(&spSheetEnum);
    spSheetEnum->SetFilterContext(ctx->getFilterContext());
    KCellValueAcpt cellValueAcpt(spSheet, ctx, values);
    spSheetEnum->EnumCellValueRowbyRow(range, &cellValueAcpt);
    {
        sa::Leave valuesLeave = sa::enterArray(acpt, "values");
        for (ROW row = firstRow; row <= lastRow; row++)
        {
            sa::Leave itemLeave = sa::enterStruct(acpt, nullptr);
            acpt->addInt32("row", row);
            bool hidden = ctx->getProtectionCtx()->isCellHidden(spSheet, row, firstCol);
            acpt->addBool("hidden", hidden);
            auto iter = values.find(row);
            if (!hidden && iter != values.end())
                acpt->addString("text", krt::utf16(iter->second));
        }
    }
    acpt->addInt32("sheetIdx", sheetIdx);
    acpt->addInt32("firstCol", firstCol);
    return S_OK;
}

QueryRangeQRLabelCount::QueryRangeQRLabelCount(KEtWorkbook* wb)
        : EtQueryExecBase(wb, __X("query.rangeQRLabelCount"))
{
}

HRESULT QueryRangeQRLabelCount::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    const RANGE range = ReadRange(param);
    if (!range.IsValid())
        return E_FAIL;

    ks_stdptr<ISheet> spSheet;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->GetSheet(range.SheetFrom(), &spSheet);
    if (!spSheet)
        return E_FAIL;

    std::tuple<UINT, UINT, bool> queryResult = qrlabel_helper::QueryQRLabelCount(ctx, range, spSheet);
	acpt->addUint32("totalQRLabelCount", std::get<0>(queryResult));
	acpt->addUint32("visibleQRLabelCount", std::get<1>(queryResult));
	return S_OK;
}

QueryCollectInquireInfo::QueryCollectInquireInfo(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.collectInquireInfo"))
{
	
}

HRESULT QueryCollectInquireInfo::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX)
		return E_FAIL;
	ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);
	if (!spSheet)
		return E_FAIL;

	//Todo:这里接收用户调整校对表头的参数。
	IDX tblHeadRowFrom = -1;
	IDX tblHeadRowTo = -1;
	RANGE userSelectTblHeadRg(spSheet->GetBMP());
	if(param.has("tblHeadRowFrom") && param.has("tblHeadRowTo"))
	{
		tblHeadRowFrom = param.field_uint32("tblHeadRowFrom");
		tblHeadRowTo = param.field_uint32("tblHeadRowTo");
		if(tblHeadRowFrom > tblHeadRowTo)
			return E_FAIL;
	}
	
	InquirerHelper helper(m_wwb, spSheet, sheetIdx);
	CollectInquirerInfoResult res = helper.collectInquirerInfo(tblHeadRowFrom, tblHeadRowTo);
	if(res.m_status == CollectInquirerInfoStatus_Fail_Has_Hidden)
	{
		AddErrorStr(acpt, __X("E_ET_INQUIRER_FAILED_REC_HAS_HIDDEN"));
		return E_ET_INQUIRER_FAILED_REC_HAS_HIDDEN;
	}
	else if(res.m_status == CollectInquirerInfoStatus_Fail_Rec_TableHead)
	{
		AddErrorStr(acpt, __X("E_ET_INQUIRER_FAILED_REC_TBL_HEAD"));
		return E_ET_INQUIRER_FAILED_REC_TBL_HEAD;
	}
		
	else if(res.m_status == CollectInquirerInfoStatus_Fail_Rec_PhoneCols)
	{
		AddErrorStr(acpt, __X("E_ET_INQUIRER_FAILED_REC_PHONE_COL"));
		return E_ET_INQUIRER_FAILED_REC_PHONE_COL;
	}
	else if(res.m_status == CollectInquirerInfoStatus_Fail_TblTitle_HorMerge)
	{
		AddErrorStr(acpt, __X("E_ET_INQUIRER_FAILED_REC_TBLTITLE_HOR_MERGE"));
		return E_ET_INQUIRER_FAILED_REC_TBLTITLE_HOR_MERGE;
	}
	else if(res.m_status == CollectInquirerInfoStatus_Fail_Tbl_Cols_Exceed_Threshold)
	{
		AddErrorStr(acpt, __X("E_ET_INQUIRER_FAILED_REC_TBL_COLS_EXCEEDS_THRESHOLD"));
		return E_ET_INQUIRER_FAILED_REC_TBL_COLS_EXCEEDS_THRESHOLD;
	}
	else if(res.m_status == CollectInquirerInfoStatus_Fail_Other_Reason)
		return E_FAIL;
	
	//走到这里说明识别表头 电话号码列等信息都成功了
	ks_stdptr<IUnknown> spUnk;
	spSheet->GetExtDataItem(edSheetInquirer, &spUnk);
	ks_stdptr<IInquirer> spSheetInquirer = spUnk;
	if(!spSheetInquirer)
		return E_FAIL;
	
	//因为后面ResetCreatingPhaseDirty()这里要对表头区域进行监控，所以这里得先把表头区域给设置上去!
	spSheetInquirer->SetTableHeadArea(res.m_tableHeadRg.RowFrom(), res.m_tableHeadRg.RowTo(), res.m_tableHeadRg.ColFrom(), res.m_tableHeadRg.ColTo());
	spSheetInquirer->ResetCreatingPhaseDirty();//开启监控创建到发布中间过程的标脏变化
	acpt->addKey("tblHeadRg");
	acpt->beginStruct();
		WriteRange(acpt, res.m_tableHeadRg);
	acpt->endStruct();

	acpt->addKey("phoneColsInfo");
	acpt->beginArray();
	int phoneColCnt = res.m_phoneColsInfo.size();
	for (int i = 0; i < phoneColCnt; i++)
	{
		acpt->beginStruct();
		acpt->addInt32("colIdx", res.m_phoneColsInfo[i].m_colIdx);
		acpt->addString("name", krt::utf16(res.m_phoneColsInfo[i].m_colName));
		acpt->endStruct();
	}
	acpt->endArray();
	
	return S_OK;
}

QueryGetAllInquirer::QueryGetAllInquirer(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.getAllInquirer"))
{

}

HRESULT QueryGetAllInquirer::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	ks_stdptr<Worksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	if(!spWorksheets)
		return E_FAIL;
    long sheetCount = 0;
    spWorksheets->get_Count(&sheetCount);

	VarObj sheetStIdVecVar = param.get("sheetStIdVec");

	acpt->addKey("inquirerVec");
	acpt->beginArray();
	for (int i = 0, cnt = sheetStIdVecVar.arrayLength(); i < cnt; ++i)
	{
		UINT32 stId = sheetStIdVecVar.item_uint32(i);
		IDX sheetIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(stId, &sheetIdx);

		acpt->beginStruct();
		acpt->addUint32("sheetStId", stId);
		if(sheetIdx < 0 || sheetIdx >= sheetCount)
		{
			acpt->addBool("bSheetExist", false);
			acpt->endStruct();
			continue;
		}

		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(sheetIdx, &spSheet);
		if(!spSheet)
		{
			acpt->addBool("bSheetExist", false);
			acpt->endStruct();
			continue;
		}
			
		acpt->addBool("bSheetExist", true);
		ks_stdptr<IUnknown> spUnk;
		spSheet->GetExtDataItem(edSheetInquirer, &spUnk);
		ks_stdptr<IInquirer> spSheetInquirer = spUnk;
		if(!spSheetInquirer || !spSheetInquirer->GetEnable())//GetEnable()为false 说明该inquirer已删除
		{
			acpt->addBool("bInquirerExist", false);
			acpt->endStruct();
			continue;
		}
		//走到这里说明查询器是存在的。
		acpt->addBool("bInquirerExist", true);

		bool bTblHeadDirty = false, bTblContentDirty = false;
		spSheetInquirer->GetFinishedPhaseDirty(bTblHeadDirty, bTblContentDirty);

		acpt->addBool("bTblHeadDirty", bTblHeadDirty);
		acpt->addBool("bTblContentDirty", bTblContentDirty);

		acpt->addInt32("colIdxPrimaryKey", spSheetInquirer->GetColIdxPrimaryKey());
		{
			IDX iTblHeadRowFrom = -1, iTblHeadRowTo = -1, iTblHeadColFrom = -1,  iTblHeadColTo = -1;
			spSheetInquirer->GetTableHeadArea(iTblHeadRowFrom, iTblHeadRowTo, iTblHeadColFrom, iTblHeadColTo);
			if(iTblHeadRowFrom > iTblHeadRowTo || iTblHeadColFrom > iTblHeadColTo)
			{
				WOLOG_ERROR << "[QueryGetAllInquirer] GetTableHeadArea result invalid!!!";
				return E_FAIL;
			}
			
			RANGE tableHeadRg(spSheet->GetBMP());
			tableHeadRg.SetSheetFromTo(sheetIdx, sheetIdx);
			tableHeadRg.SetRowFromTo(iTblHeadRowFrom, iTblHeadRowTo);
			tableHeadRg.SetColFromTo(iTblHeadColFrom, iTblHeadColTo);
			if(!tableHeadRg.IsValid())
			{
				WOLOG_ERROR << "[QueryGetAllInquirer] tableHeadRg invalid!!!";
				return E_FAIL;
			}
			
			acpt->addKey("tblHeadRg");
			acpt->beginStruct();
			WriteRANGE(tableHeadRg, acpt);
			acpt->endStruct();
		}

		acpt->endStruct();
	}
	acpt->endArray();
	return S_OK;
}

QuerySingleCellHistoryList::QuerySingleCellHistoryList(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.getCellHistoryList"))
{
}

HRESULT QuerySingleCellHistoryList::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	const RANGE rg = ReadRange(param);
	if(!rg.IsValid())
	{
		WOLOG_INFO << "[QuerySingleCellHistoryList] param rg error";
		return S_FALSE;
	}
	
	CommitID commitBegin = WO_INVALID_COMMIT_ID;
	if (param.has("commitBegin"))
	{
		commitBegin = param.field_int32("commitBegin");
	}

	int maxCommitCount = param.field_int32("maxCommitCount");

	bool isForward = false;
	if (param.has("isForward"))
	{
		isForward = param.field_bool("isForward");
	}

	WOLOG_INFO << "[cell_history] QuerySingleCellHistoryList: " 
	           << "commitBegin: " << commitBegin
			   << ", maxCommitCount: " << maxCommitCount
			   << ", isForward: " << isForward;

	ks_stdptr<ISheet> sht = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(rg.SheetFrom())->GetSheet();
	if (!sht || sht->IsDbSheet() || sht->IsAppSheet() || sht->IsWorkbenchSheet())
		return S_FALSE;

	if (ctx->getProtectionCtx()->isCellHidden(sht, rg.RowFrom(), rg.ColFrom()))
	{
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
	}

	ISheetStake *stake = sht->GetWoStake();
	if (!stake) 
		return S_FALSE;
	
	IHistoryInfo *pHistoryInfo = sht->GetWoStake()->getCellHistory();
	if (!pHistoryInfo) 
		return S_FALSE;

	acpt->addKey("history");
	pHistoryInfo->serialHistoryPart(rg.RowFrom(), rg.ColFrom(), commitBegin, maxCommitCount, isForward, acpt);
	return S_OK;
}

QueryRefRangeByCommitVer::QueryRefRangeByCommitVer(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.getRefRangeByCommitVer"))
{
}

HRESULT QueryRefRangeByCommitVer::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	CommitID commitBegin = WO_INVALID_COMMIT_ID;
	CommitID commitEnd = WO_INVALID_COMMIT_ID;
	if (param.has("commitVerRange"))
	{
		VarObj commitRnage = param.get("commitVerRange");
		commitBegin = commitRnage.field_int32("commitBegin");
		commitEnd = commitRnage.field_int32("commitEnd");
	}
	else
	{
		WOLOG_INFO << "[cell_history] invalid arg, miss commitRnage";
		return S_FALSE;
	}

	int maxCommitPerCellRef = -1; // unlimit
	if (param.has("maxCommitPerCellRef"))
	{
		maxCommitPerCellRef = param.field_int32("maxCommitPerCellRef");
	}

	bool skipExpired = false;
	if (param.has("skipExpired"))
	{
		skipExpired = param.field_bool("skipExpired");
	}

	WOLOG_INFO << "[cell_history] QueryRangeByCommit: " 
	           << "commitBegin: " << commitBegin
			   << ", commitEnd: " << commitEnd
			   << ", skipExpired: " << skipExpired
			   << ", maxCommitPerCellRef: " << maxCommitPerCellRef;

	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX) 
		return S_FALSE;

	ks_stdptr<ISheet> sht = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx)->GetSheet();
	if (!sht || sht->IsDbSheet() || sht->IsAppSheet() || sht->IsWorkbenchSheet())
		return S_FALSE;
	
	ISheetStake *stake = sht->GetWoStake();
	if (stake == NULL) 
		return S_FALSE;

	IHistoryInfo *spHistory = sht->GetWoStake()->getCellHistory();
	if (!spHistory) 
		return S_FALSE;

	spHistory->serialRangeByCommit(sht, commitBegin, commitEnd, maxCommitPerCellRef, skipExpired, acpt);
	return S_OK;
}

// 内部命令，测试使用
QueryCellHistoryPurgeOptions::QueryCellHistoryPurgeOptions(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.getCellHistoryPurgeOptions"))
{
}

HRESULT QueryCellHistoryPurgeOptions::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	// 内部命令，测试使用
	WoEtSetting *pWoSettings = static_cast<WoEtSetting*>(_kso_GetWoEtSettings());
	acpt->addInt32("isEnabling", pWoSettings->IsEnablingCellHistoryStore());
	acpt->addInt32("maxPurgeTag", pWoSettings->GetCellHistoryMaxPurgeTag());
	acpt->addInt32("reserveCount", pWoSettings->GetCellHistoryReserveCount());
	acpt->addInt32("expireSecs", pWoSettings->GetCellHistoryExpireSecs());

	int cc = 0;
	util::ForEachSheet(m_wwb->GetCoreWorkbook()->GetBook(), [&cc] (ISheet *pSheet, IDX) -> bool {
		ISheetStake *stake = pSheet->GetWoStake();
		if (!stake) 
			return false;

		IHistoryInfo *spHistory = pSheet->GetWoStake()->getCellHistory();
		if (!spHistory) 
			return false;

		cc += spHistory->getCommitCount();
		return false;
	});

	acpt->addInt32("commitVerCount", cc);
	return S_OK;
}

QueryAiRecognizedInfo::QueryAiRecognizedInfo(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.aiRecognizedInfo"))
{
}

HRESULT QueryAiRecognizedInfo::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	JsonDataType jsonDataType = JsonDataArrangement;
	if (param.has("jsonDataType"))
		jsonDataType = static_cast<JsonDataType>(param.field_uint32("jsonDataType"));

	// 数据问答工作簿数据按需上传
	if (JsonDataQABookSource == jsonDataType)
	{
		IKWorksheets* pWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
		if (!pWorksheets)
			return E_FAIL;
		
		IDX sheetIdx = GetSheetIdx(param, ctx);
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
		if (!pWorksheet)
			return E_FAIL;

		ISheet* pSheet = pWorksheet->GetSheet();
		if (!pSheet)
			return E_FAIL;

		if (pSheet->IsDbSheet())
		{
			if (!param.has("viewId"))
				return E_FAIL;

			EtDbId viewId = INVALIDIDX;
			HRESULT hr = _appcore_GainDbSheetContext()->DecodeEtDbId(param.field_str("viewId"), &viewId);
			if (FAILED(hr))
			{
				return E_FAIL;
			}

			ks_stdptr<IDBSheetViews> spDbSheetViews;
			pSheet->GetExtDataItem(edSheetDbViews, (IUnknown**)&spDbSheetViews);
			if (!spDbSheetViews)
				return E_FAIL;

			ks_stdptr<IDBSheetView> spView;
			spDbSheetViews->GetItemById(viewId, &spView);
			if (!spView)
				return E_FAIL;

			ks_stdptr<IDBSheetOp> spDbSheetOp;
			pSheet->GetExtDataItem(edSheetDbOp, (IUnknown**)&spDbSheetOp);
			if(!spDbSheetOp)
				return E_FAIL;
			
			QJsonObject sheetDataObj;
			wo::KIdentifyDBView identify(pWorksheet, spDbSheetOp);
			identify.identify(spView, sheetDataObj);

			if (identify.hasSheetOverflow())
			{
				acpt->addString("aiRecognizedInput", __X("over"));
				return S_OK;
			}
			
			if (param.has("selectionRangeList"))
			{
				QJsonArray selectionRangeList;
				QJsonArray selectionRange;
				VarObj selectionRangeObj = param.get("selectionRangeList");
				if (typeArray == selectionRangeObj.type())
				{
					for (int i = 0; i < selectionRangeObj.arrayLength_s(); i++)
					{
						selectionRange.append(selectionRangeObj.item_int32(i));
					}
				}
				selectionRangeList.append(selectionRange);
				sheetDataObj.insert("selectionRangeList", selectionRangeList);
			}

			QJsonArray sheetData;
			sheetData.append(sheetDataObj);
			
			QJsonObject obj;
			obj.insert("sheets_data", sheetData);

			const WCHAR* pViewName = nullptr;
			pViewName = spView->GetName();
			obj.insert("active_sheet", krt::fromUtf16(pViewName));
			
			QByteArray dataArray;
			QByteArray byteArray = QJsonDocument(obj).toJson(QJsonDocument::Compact);
			bool bGzip = KDocUtils::doCompress(byteArray, dataArray);

			// 降内存
			obj = QJsonObject();
			byteArray.clear();
			
			acpt->addString("aiRecognizedInput", (bGzip ? krt::utf16(QString(dataArray.toBase64())) : __X("zip fail")));
		}
		else
		{
			QString activeSheetName;
			if (param.has("activeName"))
				activeSheetName = krt::fromUtf16(param.field_str("activeName"));

			int maxSheetCell = 0, maxBookCell = 0;
			if (param.has("maxSheetCell"))
				maxSheetCell = param.field_int32("maxSheetCell");
			if (param.has("maxBookCell"))
				maxBookCell = param.field_int32("maxBookCell");
				
			QSet<QString> incrementSheet;
			if (param.has("incrementSheet"))
			{
				VarObj incrementSheetObj = param.get("incrementSheet");
				if (typeArray == incrementSheetObj.type())
				{
					for (int i = 0; i < incrementSheetObj.arrayLength_s(); i++)
					{
						incrementSheet.insert(krt::fromUtf16(incrementSheetObj.item_str(i)));
					}
				}
			}

			QJsonArray sheetDataArray;
			etai::KIdentifyQASource identify(m_wwb->GetCoreWorkbook());
			identify.SetMaxCellLimit(maxSheetCell, maxBookCell);
			identify.Identify(sheetDataArray, incrementSheet);

			QVariantMap countParams = std::move(identify.GetCountCollect());
			if (identify.HasOverflow())
			{
				acpt->addString("errorMsg", __X("over"));
				sa::Leave itemCount = sa::enterStruct(acpt, "countCollect");
				WriteCellCollect(countParams, acpt);
				return S_OK;
			}

			QJsonObject obj;
			obj.insert("sheets_data", sheetDataArray);
			obj.insert("active_sheet", activeSheetName);

			QByteArray dataArray;
			QByteArray byteArray = QJsonDocument(obj).toJson(QJsonDocument::Compact);
			if (!KDocUtils::doCompress(byteArray, dataArray))
			{
				acpt->addString("errorMsg", __X("gzip fail"));
				sa::Leave itemCount = sa::enterStruct(acpt, "countCollect");
				WriteCellCollect(countParams, acpt);
				return S_OK;
			}
			acpt->addString("aiRecognizedInput", krt::utf16(QString(dataArray.toBase64())));
			sa::Leave itemCount = sa::enterStruct(acpt, "countCollect");
			WriteCellCollect(countParams, acpt);
		}
	}
	else if (JsonDataAIFill == jsonDataType)
	{
		IDX sheetIdx = GetSheetIdx(param, ctx);
		IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		if (!pWorksheet)
			return E_FAIL;
		std::vector<RANGE> rgVec;
		ReadRangesInl(m_wwb->GetBMP(), sheetIdx, param, rgVec);
		if (rgVec.size() != 1)
			return S_FALSE;

		QJsonObject obj;
		KIdentifyTable identify(pWorksheet, ctx->getProtectionCtx(), jsonDataType);
		HRESULT hr = identify.Init();
		if (FAILED(hr))
			return hr;

		ks_stdptr<IKWorksheetView> spView = pWorksheet->GetActiveWorksheetView();
		if (!spView)
			return S_FALSE;
		range_helper::ranges rgs;
		spView->GetSelectionRange(&rgs);
		if (!rgs || rgs.size() == 0)
			return S_FALSE;
		UINT nCount = 0;
		rgs->GetCount(&nCount);
		if (nCount <= 0)
			return S_FALSE;
		
		std::vector<RANGE> selRgVec;
		const RANGE *pRg = nullptr;
		for(int i = 0;i < nCount;++i)
		{
			pRg = rgs.at(i).second;
			if (!pRg)
				return S_FALSE;
			selRgVec.push_back(*pRg);
		}
		
		identify.SetSelectionRange(selRgVec);
		identify.SetCustomRange(rgVec[0]);
		identify.Identify(obj);
		acpt->addString("aiRecognizedInput", krt::utf16(QString(QJsonDocument(obj).toJson(QJsonDocument::Compact))));
	}
	else
	{
		IDX sheetIdx = GetSheetIdx(param, ctx);
		IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		if (!pWorksheet)
			return E_FAIL;
		std::vector<RANGE> rgVec;
		ReadRangesInl(m_wwb->GetBMP(), sheetIdx, param, rgVec);

		QJsonObject obj;
		KIdentifyTable identify(pWorksheet, ctx->getProtectionCtx(), jsonDataType);
		HRESULT hr = identify.Init();
		if (FAILED(hr))
			return hr;
		identify.SetSelectionRange(rgVec);
		identify.Identify(obj);
		acpt->addString("aiRecognizedInput", krt::utf16(QString(QJsonDocument(obj).toJson(QJsonDocument::Compact))));
	}

	return S_OK;
}

void QueryAiRecognizedInfo::WriteCellCollect(QVariantMap counts, ISerialAcceptor* acpt)
{
	for (auto it = counts.begin(); it != counts.end(); it++)
	{
		QString key = it.key();
		int value = it.value().toInt();
		acpt->addInt32(key.toStdString().c_str(), value);
	}
}

QuerySmartSheetRecognition::QuerySmartSheetRecognition(KEtWorkbook *wb)
    : EtQueryExecBase(wb, __X("query.smartSheetRecognition"))
{
    m_jmTaskMap["teval"] = static_cast<int>(etai::JsonDataArrangement);
    m_jmTaskMap["zone"] = static_cast<int>(etai::JsonDataArrangement);
    m_jmTaskMap["proof"] = static_cast<int>(etai::JsonDataProofreading);
    m_jmTaskMap["datamining"] = static_cast<int>(etai::JsonDataAnalysis);
    m_jmTaskMap["dataunderstand"] = static_cast<int>(etai::JsonDataPivotTableSource);
}

HRESULT QuerySmartSheetRecognition::Exec(const VarObj &param, KEtRevisionContext *ctx, ISerialAcceptor *acpt)
{
	IDX sheetIdx = GetSheetIdx(param, ctx);
	IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWorksheet)
		return E_FAIL;

    if (!param.has("jmTask"))
        return E_FAIL;

    QString jmTask = QString::fromUtf16(param.field_str("jmTask"));
    if (m_jmTaskMap.count(jmTask) == 0)
        return E_FAIL;

	QJsonObject obj;
	int jmCellsCount = 0;
    etai::JsonDataType dataType = static_cast<etai::JsonDataType>(m_jmTaskMap[jmTask]);
    switch (dataType)
    {
    case etai::JsonDataArrangement:
    case etai::JsonDataProofreading:
    case etai::JsonDataAnalysis:{
        etai::KIdentifyTable identify(pWorksheet, dataType);
        identify.Identify(obj, nullptr);
		break;
    }
    case etai::JsonDataPivotTableSource: 
    default:
        break;
    }

	etai::CalcCellCount counter(obj);

    QString data = QString(QJsonDocument(obj).toJson(QJsonDocument::Compact));
    acpt->addKey("data");
    acpt->beginStruct();
	{
        acpt->addString("jm_task", krt::utf16(jmTask));
        acpt->addInt32("jm_cells", counter.count());
        acpt->addInt32("jm_rangeCells", counter.rangeCount());
		if(jmTask != QString("teval"))
        	acpt->addString("table_data", krt::utf16(data));
	}
	acpt->endStruct();
	acpt->addString("tableInfo", krt::utf16(data));
	return S_OK;
}

HRESULT QuerySmartSheetRecognition::CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx)
{
	IDX iSheet = GetSheetIdx(param, ctx);
	IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWorksheet) 
		return E_FAIL;
	if (!CheckExclusivePermission(pWorksheet, ctx))
		return E_OPERATION_NOT_SUPPORTED_ON_EXCLUSIVE;

	if (param.field_bool("needCheckPermission"))
	{
		if (SUCCEEDED(CheckProtectionIsMaster(iSheet, ctx)))
			return S_OK;
		ISheet* pSheet = pWorksheet->GetSheet();
		if (ctx->getProtectionCtx()->isSheetHasHidden(pSheet) ||
			ctx->getProtectionCtx()->isSheetHasReadonly(pSheet))
			return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;
	}
	return S_OK;
}

bool QuerySmartSheetRecognition::CheckExclusivePermission(IKWorksheet* pWorksheet, KEtRevisionContext* ctx)
{
	IBookStake* pBookStake = m_wwb->GetCoreWorkbook()->GetBook()->GetWoStake();
	if (!pBookStake)
		return true;

	if (ctx->getUser() && pBookStake->IsCreator(ctx->getUser()->userID()))
		return true;

	ISheetProtection* pSheetProtection =  pWorksheet->GetProtection();
	if (!pSheetProtection || pSheetProtection->GetExclusiveRangeMode() == ermNone)
		return true;
	
	// 开启独占后协作者不能使用表格整理
	return false;
}

QueryGetTableTitleCnt::QueryGetTableTitleCnt(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.getTableTitleCnt"))
{
}

HRESULT QueryGetTableTitleCnt::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX sheetIdx = GetSheetIdx(param, ctx);

	TableStructRecHelper::TableTitleInfo info;
	HRESULT hr = TableStructRecHelper::GetTableTitleInfo(m_wwb, ctx, sheetIdx, info);
	if (S_OK != hr)
		return hr;

	acpt->addString("errCode", ConvertErrCodeToString(info.errorCode));
	acpt->addInt32("titleCnt", info.row );
	acpt->addInt32("colFrom", info.colFrom);
	acpt->addInt32("colTo", info.colTo);
	acpt->addBool("hasMergedCell", info.bMerged);

	return S_OK;
}

QueryGetAppRelatedSheetDirty::QueryGetAppRelatedSheetDirty(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.getAllAppRelatedSheetDirty"))
{

}

HRESULT QueryGetAppRelatedSheetDirty::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<IKWorksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	if (!spWorksheets)
		return E_FAIL;
	int sheetCount = spWorksheets->GetSheetCount();
	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if(!pBook)
		return E_FAIL;

	struct KAppRelatedSheetDirtyEnum : IAppEtDbRelationItemEnum
	{
   		KAppRelatedSheetDirtyEnum(ISerialAcceptor* acpt)
			: m_acpt(acpt)
		{

		}
		STDIMP_(BOOL) Do(IAppEtDbRelationItem* pItem) override
		{
			if(!pItem)
				return TRUE;
 
			BOOL tblHeadDirty = FALSE, tblContentDirty = FALSE;
			pItem->GetDirty(tblHeadDirty, tblContentDirty);
			UINT dbSheetStId = pItem->GetRelatedDbSheetStId();
			wo::sa::Leave itemLeave = wo::sa::enterStruct(m_acpt, nullptr);

			m_acpt->addUint32("relatedDbSheetStId", dbSheetStId);
			m_acpt->addBool("isTblHeadDitry", tblHeadDirty);
			m_acpt->addBool("isTblContentDitry", tblContentDirty);
			return TRUE;
		}
   		private:
			ISerialAcceptor* m_acpt;
    };
	
	sa::Leave leaveArray = sa::enterArray(acpt, "dirtyRes");
	for (IDX idx = 0; idx < sheetCount; idx++)
	{
		ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(idx, &spSheet);
        if (!spSheet)
			continue;
		if (spSheet->IsDbSheet() || spSheet->IsAppSheet() || spSheet->IsWorkbenchSheet())
			continue;
		ks_stdptr<IUnknown> spUnk;
		spSheet->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
		ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
		if(!spSheetAppEtDbRelations)
			continue;
		
		KAppRelatedSheetDirtyEnum appEtDbRelationItemEnum(acpt);
    	spSheetAppEtDbRelations->Enum(&appEtDbRelationItemEnum);
	}

	return S_OK;
}

QueryNeedUpdateAppVersion::QueryNeedUpdateAppVersion(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.needUpdateAppVersion"))
{

}

HRESULT QueryNeedUpdateAppVersion::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	bool bNeedUpdateAppVersion = UpdateAppVersionHelper::IsNeedUpdateAppVersion(m_wwb);
	WOLOG_INFO << "[QueryNeedUpdateAppVersion] is need updateAppVersion: " << bNeedUpdateAppVersion;
	acpt->addBool("bNeedUpdateAppVersion", bNeedUpdateAppVersion);
	return S_OK;
}

HRESULT QuerySpecialCells::CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx)
{
	//不需要检查权限，因为没有返回单元格内容
	return S_OK;
}

QuerySpecialCells::QuerySpecialCells(KEtWorkbook *wwb) : EtQueryExecBase(wwb,  __X("query.specialCells"))
{
}

HRESULT QuerySpecialCells::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX) return E_INVALIDARG;

	if (!param.has("activeCell"))
		return E_INVALIDARG;
	if (!param.has("queryType"))
		return E_INVALIDARG;
	if (!param.has("cellValueType"))
		return E_INVALIDARG;
	bool isRowColDiff = false;
	if (param.has("isRowDiff"))
		isRowColDiff = true;
	bool isBatchSearch = false;
	if (param.has("locationRange"))
		isBatchSearch = true;

	ETCellType queryType = (ETCellType)param.field_int32("queryType");
	ETSpecialCellsValue	cellValueType = (ETSpecialCellsValue)param.field_int32("cellValueType");
	const VarObj varCell = param.get_s("activeCell");
	RANGE activeRg = ReadRange(varCell);
	if (!activeRg.IsSingleCell())
		return E_INVALIDARG;
	ks_castptr<_Worksheet> cpWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!cpWorksheet)
		return E_INVALIDARG;
	HRESULT hr = cpWorksheet->Activate();
	if (FAILED(hr))
		return E_INVALIDARG;

	VarObj vRanges = isBatchSearch ? param.get_s("locationRange") : param.get_s("rgs");
	ks_stdptr<IKRanges> spRanges;
	ReadRanges(vRanges, &spRanges);
	if (!spRanges)
		return E_INVALIDARG;

	ks_stdptr<etoldapi::Range> spRange;
	cpWorksheet->GetRangeByData(spRanges, &spRange);
	if (!spRange)
		return E_INVALIDARG;
	spRange->Select();
	ks_stdptr<etoldapi::Range> spRgActiveCell = m_wwb->CreateRangeObj(activeRg);
	if (!spRgActiveCell)
		return E_INVALIDARG;
	hr = spRgActiveCell->Activate();
	if (FAILED(hr))
		return E_INVALIDARG;
	ks_stdptr<etoldapi::Range> spResultRange;
	if (isRowColDiff)
	{
		bool isRowDiff = param.field_bool("isRowDiff");
		if (isRowDiff)
			spRange->RowDifferences(KComVariant(spRgActiveCell), &spResultRange);
		else
			spRange->ColumnDifferences(KComVariant(spRgActiveCell), &spResultRange);
	}
	else
	{
		hr = spRange->SpecialCells(queryType, KComVariant(cellValueType), &spResultRange);
		if (FAILED(hr) && API_E_CUSTOM_ERROR != hr)
			return hr;
	}
	if (spResultRange)
		WriteRangesObj(acpt, spResultRange);
	return S_OK;
}


QueryWorksheetFunction::QueryWorksheetFunction(KEtWorkbook *wwb) : EtQueryExecBase(wwb,  __X("query.worksheetFunction"))
{
}
 
HRESULT QueryWorksheetFunction::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<WorksheetFunction> spSheetFunction;
	m_wwb->GetCoreApp()->get_WorksheetFunction(&spSheetFunction);
 
	ks_stdptr<IBookOp> spBookOp;
	m_wwb->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
 
	const VarObj varFmla = param.get("fmla");
	const VarObj varTokens = varFmla.get("tokens");

	ks_stdptr<ITokenVectorInstant> spFmlaTokVec;
	HRESULT hr = createTokenVectorInstant(varTokens, &spFmlaTokVec);
	if (FAILED(hr))
		return hr;
 
	CELL activeCell = {0, 0};
	IDX activeSheet = 0;
	if (varFmla.has("activeCell"))
	{
		/**
	 	* 1. 部分函数需要活跃单元格。例如Indirect
		* 2. RTD函数的话，前端发送来的activeSheet, activeCell.row, activeCell.col都是-1.
		* 3. 前端WPSOpenApi通过ActiveSheet Model和ActiveCell Model获取 
	 	* */
	 	const VarObj varCell = varFmla.get("activeCell");
		RANGE rg = ReadRange(varCell);
		if (rg.SheetFrom() != rg.SheetTo() || 
				rg.RowFrom() != rg.RowTo() ||
				rg.ColFrom() != rg.ColTo())
			return E_INVALIDARG;

		activeSheet = rg.SheetFrom();
		activeCell.row = rg.RowFrom();
		activeCell.col = rg.ColFrom();
	}
	
	alg::managed_token_assist resToken;
	hr = spSheetFunction->WoCalculate(spBookOp, activeSheet, activeCell, spFmlaTokVec, &resToken);
	sa::addToken(acpt, "res", resToken);
	return hr;
}

 
HRESULT QueryWorksheetFunction::createTokenVectorInstant(const VarObj& varTokenVec, ITokenVectorInstant** ppVec)
{
	WebName ty = varTokenVec.structType();
	if (NULL == ty || strcmp("TokenVectorInstant", ty) != 0)
		return E_INVALIDARG;

	const bool bOffset = varTokenVec.field_bool("isOffset");
	if (bOffset)
		return E_INVALIDARG;  // 该查询命令，传送来的引用都是Result的. 参考calc_service/relativemodel.h

	ks_stdptr<ITokenVectorInstant> spFmlaTokVec;
	HRESULT hr = CreateInstantTokenVector(bOffset, &spFmlaTokVec);
	if (FAILED(hr))
		return hr;

	VarObj varItems = varTokenVec.get("items");
	for (int i = 0; i < varItems.arrayLength_s(); ++i)
	{
		alg::managed_token_assist mta;
		mta.set(createExecToken(varItems.at_s(i)));
		if (mta.is_valid())
		{
			spFmlaTokVec->AddItem(mta.detach());
		}
		else
		{
			hr = E_INVALIDARG;
			break;
		}
	}	
	
	if (SUCCEEDED(hr))
		*ppVec = spFmlaTokVec.detach();

	return hr;
}

managed_token_ptr QueryWorksheetFunction::createExecToken(const VarObj& varToken)
{
	WebName tokenTy = varToken.structType();
	if (tokenTy == NULL)
		return NULL;
 
	alg::managed_token_assist ret;
	if (strcmp("ExecTokenDBL", tokenTy) == 0)
	{
		ret.set(alg::managed_vdbl_token_assist().create(varToken.field_double("val")).detach());
	}
	else if (strcmp("ExecTokenBOOL", tokenTy) == 0)
	{
		ret.set(alg::managed_vbool_token_assist().create(varToken.field_bool("val")).detach());
	}
	else if (strcmp("ExecTokenSTR", tokenTy) == 0)
	{
		ret.set(alg::managed_vstr_token_assist().create(varToken.field_str("str")).detach());
	}
	else if (strcmp("ExecTokenSTREF_Region", tokenTy) == 0)
	{
		int bookid = varToken.field_uint32("bookId");
		int sheetFrom = varToken.field_uint32("sheetFrom");
		int sheetTo = varToken.field_uint32("sheetTo");
		int colFrom = varToken.field_uint32("colFrom");
		int colTo = varToken.field_uint32("colTo");
		int rowFrom = varToken.field_uint32("rowFrom");
		int rowTo = varToken.field_uint32("rowTo");
		bool isAbsColFrom = varToken.field_bool("isAbsColFrom");
		bool isAbsColTo = varToken.field_bool("isAbsColTo");
		bool isAbsRowFrom = varToken.field_bool("isAbsRowFrom");
		bool isCarePosOnly = varToken.field_bool("isCarePosOnly");
		bool isCell3D = varToken.field_bool("isCell3D");
		bool isCrossSheet = varToken.field_bool("isCrossSheet");
		bool isFullCol = varToken.field_bool("isFullCol");
		bool isFullRow = varToken.field_bool("isFullRow");

		if (sheetFrom == sheetTo)
		{
			int sheetIdx = sheetFrom;
			ks_stdptr<ISheet> spSheet;
			m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);
			if (spSheet && spSheet->IsGridSheet())
			{
				alg::managed_stref_token_assist	rgs;
				rgs.create(0, alg::ET_RVA_A);
				rgs.make_region(bookid, sheetIdx, sheetIdx, rowFrom, rowTo, colFrom, colTo, false, false, false, false, false);
				rgs.standardize_rc(false);
				ret.set(rgs.detach());
			}
		}
	}
	else if (strcmp("ExecTokenFunction", tokenTy) == 0)
	{
		DWORD fnid = varToken.field_uint32("id");
		int nparam = varToken.field_uint32("nparam");
		ret.set(alg::managed_function_token_assist().create(fnid, nparam).detach());
	}
	else if (strcmp("ExecTokenMatrix", tokenTy) == 0)
	{
		VarObj varArr = varToken.get("arr");
		int width = varToken.field_uint32("width");
		int height = varToken.field_uint32("height");

		alg::managed_matrix_token_assist rma;
		rma.create(width, height);
		for (int i = 0; i < varArr.arrayLength_s(); ++i)
		{
			alg::managed_token_assist tmp;
			tmp.set(createExecToken(varArr.at_s(i)));

			if (!tmp.is_valid())
			{
				rma.clear();
				break;
			}

			int x = i % width;
			int y = i / width;
			rma.set_item(x, y, tmp.detach());
		}

		ret.set(rma.detach());
	}
	else if (strcmp("ExecTokenVector", tokenTy) == 0)
	{
		/**
		 * 公式函数Large无法通过UI设置多个离散的的选区。
		 * 和从UI输入不同。OpenApi使用过程中，通过Application.Selection作为Large的参数，
		 * 可能存在多个离散选区的情况。参考kwsfunc_pre.cpp的AddRefer2Token方法，通过创建ExecTokenVector去解决。
		 * 
		 * 使用方法参考前端WorksheetFunction.TestSuite()
		*/
		const VarObj varArr = varToken.get("arr");
		const int len = varArr.arrayLength_s();

		alg::managed_vector_token_assist mvta;
		mvta.create(len, alg::ET_RVA_NONE);

		for (int i = 0; i < len; ++i)
		{
			alg::managed_token_assist tmp;
			tmp.set(createExecToken(varArr.at_s(i)));
			mvta.set_item(i, tmp.detach());
		}

		ret.set(mvta.detach());
	}
	else
	{
		ret.clear();
	}

	return ret.detach();
}

QueryRangeValue2::QueryRangeValue2(KEtWorkbook *wwb) : EtQueryExecBase(wwb,  __X("query.rangeValue2"))
{
}

HRESULT QueryRangeValue2::CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx)
{
	RANGE rg = ReadRange(param);
	if (!rg.IsValid()) return S_OK;
	CHECK_PROTECTION_ALLOW_READ(rg, ctx);
	return S_OK;
}

HRESULT QueryRangeValue2::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	RANGE rg = ReadRange(param);
	if (!rg.IsValid()) return S_OK;
	uint64 rowCounts = rg.RowTo() - rg.RowFrom(), colCounts = rg.ColTo() - rg.ColFrom();
	const int blockSize = 16384;
	if (rowCounts * colCounts > blockSize)
	{
		WOLOG_INFO << "cells count large than block size";
		return E_FAIL;
	}
	ks_stdptr<etoldapi::Range> spRange = m_wwb->CreateRangeObj(rg);

	KComVariant var;
	spRange->get_Value2V9(&var);

	bool bDiscrete = SerializeVariantHelper::NeedSerializeDiscrete(var);
    bool bForceString = true;
    if (param.has("isForceString"))
    {
        bForceString = param.field_bool("isForceString");
    }

	SerializeVariantHelper::Serialize(acpt, var, "res", bForceString, bDiscrete);
	acpt->addBool("isDiscrete", bDiscrete);

	return S_OK;
}

QueryUsersInfo::QueryUsersInfo(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("debug.usersInfo"))
{
}

HRESULT QueryUsersInfo::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	class KEnumUserConn : public IKEnumUserConn
	{
	public:
		KEnumUserConn(ISerialAcceptor *acpt) : m_acpt(acpt) {}
		virtual int Do(IKUserConn *pUserConn) override
		{
			m_acpt->beginStruct();
				m_acpt->addString("connId", pUserConn->connID());
				m_acpt->addString("userId", pUserConn->userID());
			m_acpt->endStruct();
			return S_OK;
		}
	private:
		ISerialAcceptor *m_acpt;
	};

	IKUserConns *pUserConns = ctx->getUsers();
	acpt->addKey("userConns");
	acpt->beginArray();
		KEnumUserConn euc(acpt);
		pUserConns->enumUserConn(&euc);
	acpt->endArray();

	return S_OK;
}

QueryRangeShowConditionRules::QueryRangeShowConditionRules(KEtWorkbook* wb)
	: QueryRangeShowConditionRules(wb, __X("range.showConditionFormats"))
{
}

QueryRangeShowConditionRules::QueryRangeShowConditionRules(KEtWorkbook* wb, PCWSTR tag)
	: EtQueryExecBase(wb, tag)
{
}

HRESULT QueryRangeShowConditionRules::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX idxSheet = -1;
	if (!param.has("sheetIdx"))
		return E_INVALIDARG;

	idxSheet = param.field_int32("sheetIdx");

	VarObj vRanges = param.get_s("rgs");
	ks_stdptr<IKRanges> spRanges;
	ReadRanges(vRanges, &spRanges);
	if(!spRanges)
		return E_INVALIDARG;

	return ExecBy(idxSheet, spRanges, acpt);
}

HRESULT QueryRangeShowConditionRules::ExecBy(IDX idxSheet, IKRanges* prgs, ISerialAcceptor* acpt)
{
	ks_stdptr<IKWorksheet> spWs = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(idxSheet);

	ks_stdptr<etoldapi::Range> spRange;
	spWs->GetRangeByData(prgs, &spRange);
	if (!spRange)
		return E_INVALIDARG;

	ks_stdptr<FormatConditions> spConds;
	spRange->get_FormatConditions(&spConds);
	if (!spConds)
		return E_FAIL;

	long lCount = 0;
	spConds->get_Count(&lCount);
	std::vector<CondFormatItem> vecItems;
	vecItems.resize(lCount);

	if (lCount > 1000)
		WOLOG_INFO << "Condition count in 'showConditionFormats': " << lCount;
	for (long i = 0; i < lCount; ++i)
	{
		long nIndex = i + 1;

		ks_stdptr<IKCoreObject> spCore;
		ETFormatConditionType type = etCellValue;
		spConds->Item(nIndex, &spCore, &type);
		CF_ApiWrapper* pWrapper = CF_ApiWrapperFactory::getWrapper(type);
		pWrapper->ItemFromCore(&vecItems[i], spCore);
		pWrapper->GetAppliesTo(spCore, &vecItems[i].m_spRange);

		AbsObject* pObject = NULL;
		spConds->GetCoreItem(i, &pObject);
		ASSERT(pObject);
		vecItems[i].pObject = pObject;
	}

	ks_stdptr<IBook> spBook = m_wwb->GetCoreWorkbook()->GetBook();
	exportCFRules(vecItems, acpt, spBook);

	return S_OK;
}

QuerySheetHasCellTextOverflow::QuerySheetHasCellTextOverflow(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.hasCellTextOverflow"))
{
}

HRESULT QuerySheetHasCellTextOverflow::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	class CellValueAcpt : public ICellValueAcpt
	{
	public:
		CellValueAcpt(etoldapi::_Workbook* wb, int sheetIdx,
			ISheet *pSheet, wo::KEtRevisionContext* ctx, bool& bHasCellTextOverflow, int maxCount)
			: m_sheetIdx(sheetIdx)
			, m_maxCount(maxCount)
			, m_fontMerics(m_font)
			, m_pCellImages(nullptr)
			, m_pSheet(pSheet)
			, m_ctx(ctx)
			, m_bHasCellTextOverflow(bHasCellTextOverflow)
		{
			wb->GetBook()->GetOperator(&m_op);
			m_pCellImages = wb->GetCellImages();
		}

		void updateFont(const FONT* pFont)
		{
			bool bUpdate = false;
			if (m_fontFamily != pFont->name)
			{
				m_font.setFamily(krt::fromUtf16(pFont->name));
				m_fontFamily = pFont->name;
				bUpdate = true;
			}
			if (m_dyheight != pFont->dyHeight)
			{
				m_font.setPixelSize(pFont->dyHeight);
				m_dyheight = pFont->dyHeight;
				bUpdate = true;
			}
			if (bUpdate)
				m_fontMerics = QFontMetrics(m_font);
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if (m_setOverFlow.count(col))
				return 0;

			if (!pToken || m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col))
				return 0;

			BOOL bMerge = FALSE;
			m_pSheet->IsMerged(row, col, &bMerge);
			if (bMerge)
				return 0;

			if (alg::const_vstr_token_assist::is_type(pToken) && m_pCellImages)
			{
				CellImg_Param param;
				if (m_pCellImages->GetCellImgParamFromCellValue(alg::const_vstr_token_assist(pToken).get_value(), &param))
					return 0;
			}

			if (m_maxCount-- == 0)
				return 1;

			const XF* pXF = nullptr;
			m_op->GetCellFormat(m_sheetIdx, row, col, &pXF, nullptr);
			if (pXF->fWrap)
				return 0;

			const FONT* pFont = pXF->pFont;
			updateFont(pFont);

			ks_bstr text;
			m_ctx->getStringTools()->GetCellText(m_pSheet, row, col, &text, nullptr, -1, nullptr);

			QString txt(krt::fromUtf16(text.c_str()));
			int width = m_fontMerics.width(txt);
			int colWidth = m_pSheet->GetColWidth(col, true);
			if (width > colWidth)
			{
				m_setOverFlow.insert(col);
				if (m_setOverFlow.size() >= 3)
				{
					m_bHasCellTextOverflow = true;
					return 1;
				}
			}
			return 0;//继续枚举
		};

		int m_sheetIdx;
		int m_maxCount;
		int m_dyheight;
		QFont m_font;
		QFontMetrics m_fontMerics;
		ks_wstring m_fontFamily;
		ks_stdptr<IBookOp> m_op;
		ICellImages *m_pCellImages;
		ISheet *m_pSheet;
		wo::KEtRevisionContext *m_ctx;
		std::set<COL> m_setOverFlow;
		bool& m_bHasCellTextOverflow;
	};

	uint sheetId = param.field_uint32("sheetStId");
	int sheetIdx = INVALIDIDX;
	m_wwb->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(sheetId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_FAIL;
	
	int maxCount = param.field_int32("maxCount");
	bool bHasCellTextOverflow = false;
	ISheet* pSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx)->GetSheet();
	CellValueAcpt accept(m_wwb->GetCoreWorkbook(), sheetIdx, pSheet, ctx, bHasCellTextOverflow, maxCount);
	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
	int right = pSheet->GetRight();
	int bottom = pSheet->GetBottom();
	if (right < 0 || right >= pSheet->GetBMP()->cntCols ||
		bottom < 0 || bottom >= pSheet->GetBMP()->cntRows)
		return E_FAIL;

	RANGE rg(pSheet->GetBMP());
	rg.SetSheetFromTo(sheetIdx);
	rg.SetRowFromTo(0, bottom);
	rg.SetColFromTo(0, right);
	spSheetEnum->EnumCellValueRowbyRow(rg, &accept);

	acpt->addBool("hasCellTextOverflow", bHasCellTextOverflow);

	return S_OK;
}

QueryIsEmptyCell::QueryIsEmptyCell(KEtWorkbook *wwb) : EtQueryExecBase(wwb,  __X("query.rgIsEmptyCell"))
{
}

HRESULT QueryIsEmptyCell::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{

	class CellValueAcpt: public ICellValueAcpt
	{
	private:
		bool* m_isHasValue;
		COL* m_col;
		ROW* m_row;
		KEtRevisionContext* m_ctx;
		ISheet* m_pSheet;

	public:
		CellValueAcpt(bool* isHasValue, ROW* row, COL* col, KEtRevisionContext* ctx, ISheet *pSheet)
			: m_isHasValue(isHasValue), m_col(col), m_row(row), m_ctx(ctx), m_pSheet(pSheet)
		{
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{

			if (pToken == NULL)
			{
				// 继续枚举
				return 0;
			}

			//判断读取的区域是否在保护区域内，若在，禁止读取
            bool bCellInvisibleForCurUser = m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col);
            if(bCellInvisibleForCurUser)
                return 0;//继续枚举

			DWORD dwType = alg::GetExecTokenMajorType(pToken);
			if (dwType == etexec::ETP_NONE)
			{
				// 继续枚举
				return 0;
			}

			// 走到这里  说明不是空单元格
			*m_col = col;
			*m_row = row;
			*m_isHasValue = true;
			return 1;
		}
	};

	bool isHasValue = false;
	ROW row = -1;
	COL col = -1;

	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	IDX sheetIdx = GetSheetIdx(param, ctx);

	if (!wo::util::IsValidSheetIdx(pBook, sheetIdx))
	{
		WOLOG_INFO << "[QueryRangeCopyBase::QueryIsEmptyCell] invalid sheetidx " << sheetIdx;
		return E_FAIL;
	}

	ISheet* pSheet;
    pBook->GetSheet(sheetIdx, &pSheet);

    et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);

    RANGE rg = ReadRange(param);

	CellValueAcpt cellValue(&isHasValue, &row, &col, ctx, pSheet);
	int ret = spSheetEnum->EnumCellValue(rg, &cellValue);

	acpt->addBool("result", !isHasValue);
	acpt->addInt32("col", col);
	acpt->addInt32("row", row);

	return S_OK;
}

QueryCalculate::QueryCalculate(KEtWorkbook *wwb) : EtQueryExecBase(wwb,  __X("query.calculate"))
{
}

HRESULT QueryCalculate::CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx)
{
	return S_OK;
}
 
HRESULT QueryCalculate::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	// 自动识别数字格式逻辑，参考office/et/appcore/range_methods.cpp的KAppCoreRange::InnerSetCellFormula

	PCWSTR formulaText = NULL;
	if (param.has("formulaText"))
	{
		formulaText = param.field_str("formulaText"); 
	}
	else
	{
		return E_INVALIDARG;
	}

	ROW row = 0;
	COL col = 0;
	IDX activeSheet = 0;
	if (param.has("activeCell"))
	{
	 	const VarObj varCell = param.get("activeCell");
		RANGE rg = ReadRange(varCell);

		CHECK_PROTECTION_ALLOW_READ(rg, ctx);
		CHECK_PROTECTION_FORMULA(rg, formulaText, ctx);

		activeSheet = rg.SheetFrom();
		row = rg.RowFrom();
		col = rg.ColFrom();
	}
	else
	{
		return E_INVALIDARG;
	}

	KXF oxf;
	CS_COMPILE_PARAM ccp(CombineCVORefStyle(cvoNormal, RS_A1),
						 activeSheet, row, col, nfcGeneral);
	BOOL bSingleStringSect = MakeCompileParam(ccp, oxf);
	
	COMPILE_RESULT crx = { GENERAL_ERROR, 0, NULL };
	ks_stdptr<IFormula> spFormula;
	IBookOp* pBookOp = m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator();
	pBookOp->CreateFormula(&spFormula);

	HRESULT hr = spFormula->SetFormula(formulaText, ccp, &crx);
	if (FAILED(hr)) 
	{
		return hr;
	}
	
	if (spFormula->CheckArrayAndRepair(ccp, AFRT_NoRepaire))
		return S_FALSE;

	hr = DealCompileResult(crx);
	if (SUCCEEDED(hr))
	{
		spFormula->ResolveName(ccp.nSheet);
		etexec::managed_token_assist resToken;
		CS_CALCULATE_PARAM ccprm(ccfNormal, ccp.nSheet, ccp.nRow, ccp.nCol);
		hr = spFormula->Calculate(&resToken, ccprm);
		sa::addToken(acpt, "res", resToken);

		WCHAR fmt[MAX_NUMBERFMT_CCH + 1];
		if (!bSingleStringSect && SUCCEEDED(ResolveNumFmt(formulaText, crx, oxf, fmt)))
			acpt->addString("numfmt", fmt);
	}

	return hr;
}

BOOL QueryCalculate::MakeCompileParam(OUT CS_COMPILE_PARAM& ccp, OUT KXF& oxf)
{
	BOOL bIgnoreNFCategory = FALSE;
	IBookOp* pBookOp = m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator();

	BOOL bSingleStringSect = FALSE;
	{
		oxf.mask._cats = XFMASK::_cat_pNumFmt;
		const XF *pxf;
		VS(pBookOp->GetCellFormat(ccp.nSheet, ccp.nRow, ccp.nCol, &pxf, &oxf.mask));
		oxf.setXF(pxf);

		kfc::nf::_XNF_ET_IsSingleStringSect(oxf.pNumFmt->hfmt, &bSingleStringSect);

		if (bSingleStringSect)
			ccp.of = cpfForceText;

		VS(_cpl_NFAGetCategory(oxf.pNumFmt->hfmt, (NumberFormatCategory *)&ccp.nfc));
		if (bIgnoreNFCategory && nfcPercentage == ccp.nfc)
			ccp.nfc = nfcGeneral;

		if (ccp.nfc == nfcDate || ccp.nfc == nfcTime)
		{
			kfc::nf::LanguageInfo langInfo = 0;
			kfc::nf::_XNF_GetLanguageInfo(oxf.pNumFmt->hfmt, &langInfo);
			ccp.calendar = kfc::nf::GetCalendarType(langInfo);
		}
	}

	if (0 == (ccp.of & cpfForceText))
		ccp.ofEx |= cpfCreateOleText;

	return bSingleStringSect;
}

HRESULT QueryCalculate::ResolveNumFmt(PCWSTR pcw, const COMPILE_RESULT& crx, const KXF& oxf,
									  OUT WCHAR(&fmt)[MAX_NUMBERFMT_CCH + 1])
{
	FinallyFormatType nft = (FinallyFormatType)crx.nFmtType;
	KComBSTR bstrNumFmt, bstrCellNF;
	BOOL fForExcel = TRUE;
	HRESULT hr = S_OK;

	if (crx.hNumFmt != NULL)
	{
		NumberFormatCategory cat = NumberFormatCategory::nfcGeneral;
		if (SUCCEEDED(_cpl_NFAGetCategory(oxf.numfmt.hfmt, &cat)) && cat == NumberFormatCategory::nfcGeneral)
		{
			hr = kfc::nf::_XNFUnCompile(crx.hNumFmt, &bstrCellNF, sGetNF_FORMAT_PARAM());
			if (SUCCEEDED(hr) && bstrCellNF != NULL)
			{
				ks_wcsncpy_s(fmt, bstrCellNF, countof(fmt) - 1);
				return hr;
			}
		}
	}

	hr = kfc::nf::_XNFUnCompileForExcel(
		oxf.pNumFmt->hfmt, &bstrCellNF, sGetNF_FORMAT_PARAM());
	if (SUCCEEDED(hr))
		_cpl_NFAResolveFormat(nft, bstrCellNF, &bstrNumFmt);
	
	if (nft == fftNumSysGeneral && bstrNumFmt != NULL && bstrNumFmt.Length() == 0)
	{
		ks_bstr bstr;
		LCID lcid = 0;
		if (kfc::nf::_XNFGetLocalNumberInfo(pcw, &lcid, &bstr))
		{
			bstrNumFmt.Attach(bstr.detach());
			fForExcel = FALSE;
		}
	}

	if (bstrNumFmt == NULL)
	{
		_cpl_NFAResolveFormat(nft, oxf.pNumFmt->fmt, &bstrNumFmt);
		fForExcel = FALSE;
	}
	VS(hr);

	if (bstrNumFmt == NULL && hr == S_OK && nft != fftGeneral)
	{
		bstrNumFmt.Attach(::SysAllocString(oxf.pNumFmt->fmt));
		fForExcel = FALSE;
	}

	BOOL fSucceed = TRUE;
	if (fForExcel)		// 如果直接取的内核数据不需要把数字格式toET
		fSucceed = gValidate2ETNumFmt(&bstrNumFmt);

	if (bstrNumFmt != NULL && fSucceed)
	{
		ks_wcsncpy_s(fmt, bstrNumFmt, countof(fmt) - 1);
		return hr;
	}

	return E_FAIL;
}

HRESULT QueryCalculate::DealCompileResult(COMPILE_RESULT& crx)
{
	HRESULT hr = S_OK;
	if (COMPILE_SUCCESS != crx.nErrCode)
	{ 
		switch (crx.nErrCode)
		{
		case BRACKET_NO_PAIR:
			hr = E_ET_COMPILE_BRACKET_NO_PAIR;
			break;

		case OPND_NEED_RIGHTBRACKET:
			hr = E_ET_COMPILE_OPND_NEED_RIGHTBRACKET;
			break;

		case BOPTR_NEED_OPND:
			hr = E_ET_COMPILE_BOPTR_NEED_OPND;
			break;

		case FUNC_PARAM_ERROR:
			hr = E_ET_COMPILE_FUNC_PARAM_ERROR;
			break;

		case FUNC_NEST_ERROR:
			hr = E_ET_COMPILE_FUNC_NEST_ERROR;
			break;

		case CONST_ARRAY:
			hr = E_ET_COMPILE_CONST_ARRAY;
			break;

		case INVALID_ROWCOLNO:
			hr = E_ET_COMPILE_INVALID_ROWCOLNO;
			break;

		case CELLTEXT_TOO_LONG:
			hr = E_ET_COMPILE_CELLTEXT_TOO_LONG;
			break;

		case STRING_IN_FORMULA_TOO_LONG:
			hr = E_ET_COMPILE_STRING_IN_FORMULA_TOO_LONG;
			break;

		case GENERAL_ERROR:
			hr = E_ET_COMPILE_GENERAL_ERROR;
			break;

		case OLD_HIDDEN_FUNC:
			hr = E_ET_FUNCTION_INVALID;
			break;

		default:
			break;
		};
	} 
	return hr;
}

QueryCustomCalendar::QueryCustomCalendar(KEtWorkbook* wb)
	: EtQueryExecBase(wb, __X("query.customCalendar"))
{
}

HRESULT QueryCustomCalendar::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ICustomCalendars* pCalendars = pBook->GetCustomCalendars(GET_TYPE::gain);
	if (pCalendars == nullptr)
		return S_FALSE;
	pCalendars->SerialCalendars(acpt);
	return S_OK;
}

QueryEndEmptyRowInRg::QueryEndEmptyRowInRg(KEtWorkbook *wwb) : EtQueryExecBase(wwb,  __X("query.getEndEmptyRowInRg"))
{
}

HRESULT QueryEndEmptyRowInRg::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	ks_stdptr<IBook> spBook = m_wwb->GetCoreWorkbook()->GetBook();
	if(!spBook)
	{
		WOLOG_INFO << "[QueryEndEmptyRowInRg::exec] spBook empty";
		return E_FAIL;
	}
	if (!param.has("sheetFrom") || !param.has("sheetTo"))
	{
		WOLOG_INFO << "[QueryEndEmptyRowInRg::exec] sheetIdx empty";
		return E_FAIL;
	}

	INT32 resRow = -1;
	IDX resSheetIdx = -1;
	for (IDX sheetIdx = param.field_int32("sheetFrom"); sheetIdx <= param.field_int32("sheetTo"); ++sheetIdx) 
	{
		if (!wo::util::IsValidSheetIdx(spBook, sheetIdx))
		{
			WOLOG_INFO << "[QueryEndEmptyRowInRg::exec] invalid sheetIdx" << sheetIdx;
			return E_FAIL;
		}

		INT32 row = RangeOperatorHelper::GetEndEmptyRowInRg(m_wwb, param, sheetIdx);
		if (row > resRow)
		{
			resRow = row;
			resSheetIdx = sheetIdx;
		}
	}

	if (resSheetIdx == -1 || resRow == -1)
	{
		WOLOG_INFO << "[QueryEndEmptyRowInRg::exec] resRow not found";
		return E_FAIL;
	}

	acpt->addInt32("sheetIdx", resSheetIdx);
	acpt->addInt32("row", resRow);
	return S_OK;
}

QuerySplitSheet::QuerySplitSheet(KEtWorkbook *wwb) : EtQueryExecBase(wwb,  __X("query.splitSheet"))
{

}

HRESULT QuerySplitSheet::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{

	RANGE rg = ReadRange(param);
	if (!rg.IsValid())
	{
		return WO_FAIL;
	}

	// 选择第几个
	int selectIndex = 0; // 从前端数据来

	// 时间的选择下标
	int dataTypeIndex = -1; // 数据从前端来
	if (param.has("dataTypeIndex"))
	{
		dataTypeIndex = param.field_int32("dataTypeIndex");
	}

	// 是否手动勾选了包含标题
	bool bManualCheckIncludeTitle = false; // 这个参数从前端数据读来 先忽略
	if (param.has("bManualCheckIncludeTitle"))
	{
		bManualCheckIncludeTitle = param.field_bool("bManualCheckIncludeTitle");
	}

	// 包含标题是否选中
	bool sp_cbIncludeTitleCheck = false;// 这个的初始值，也需要从前端读取而来
	if (param.has("checkTitle"))
	{
		sp_cbIncludeTitleCheck = param.field_bool("checkTitle");
	}

	HRESULT hr = S_OK;
	int sheetIdx = rg.SheetFrom();

	wo::SplitBookOpt splitBookOpt(m_wwb, ctx, &const_cast<VarObj&>(param));

	hr = splitBookOpt.InitData(sheetIdx);
	if (FAILED(hr))
	{
		return hr;
	}

	bool isA1Ref = splitBookOpt.isA1Ref();

	ks_stdptr<Range> spEXpRange;
	bool bHasSelectRange = false;

	// 约定 第一次全部为0
	if (rg.Height() <= 1 && rg.Width() <= 1)
	{
		// 获取当前内容选区
		splitBookOpt.initDefaultRange(&spEXpRange);

		RANGE currentRANG(m_wwb->GetBMP());
		GetRANGE(spEXpRange, currentRANG);

		selectIndex = rg.ColFrom() - currentRANG.ColFrom();
		
		if (selectIndex < 0 || selectIndex >= currentRANG.Width())
		{
			selectIndex = 0;
		}
	}
	else 
	{	
		bHasSelectRange = true;
		spEXpRange = m_wwb->CreateRangeObj(rg);
	}

	if (param.has("selectIndex"))
	{
		int tempSelectIndex = param.field_int32("selectIndex");
		if (tempSelectIndex >= 0)
		{
			selectIndex = tempSelectIndex;
		}
	}

	int maxCol = 50;
	if (param.has("colLimit"))
	{
		int tempSelectIndex = param.field_int32("colLimit");
		if (tempSelectIndex > maxCol)
		{
			maxCol = tempSelectIndex;
		}
	}

	int maxRow = m_wwb->GetBMP()->cntRows - 1;
	if (param.has("rowLimit")) 
	{
		int tempSelectIndex = param.field_int32("rowLimit");
		if (tempSelectIndex > 50 && tempSelectIndex < maxRow)
		{
			maxRow = tempSelectIndex;
		}
	}

	if (IsExceedMaxWidthHeight(spEXpRange, maxCol, maxRow))
	{
		acpt->addBool("startEnabled", false);
		acpt->addString("errMsg", __X("SplitBook_colRowMax"));
		return E_FAIL;
	}

	ks_bstr bstrTblRg;
	range_helper::ranges rgsTbl;
	if (FAILED(hr = splitBookOpt.GetRangeAddress(&bstrTblRg, spEXpRange, &rgsTbl)))
	{
		return hr;
	}

	bool sp_bStartEnabled = false;
	bool sp_bIsSameBook = false;
	bool sp_cbIncludeTitleEnabled = false;

	ks_wstring errMsg = __X("");

	if (!splitBookOpt.initRect(rgsTbl, bManualCheckIncludeTitle, bHasSelectRange, selectIndex,
		&sp_bStartEnabled, &sp_bIsSameBook, &sp_cbIncludeTitleEnabled, &sp_cbIncludeTitleCheck, errMsg))
	{
		acpt->addString("errMsg", errMsg.c_str());
		sp_bStartEnabled = false;
	}
	
	acpt->addString("rangeStr", bstrTblRg);
	WriteRangeObj(acpt, spEXpRange);

	acpt->addBool("startEnabled", sp_bStartEnabled);
	acpt->addBool("titleEnabled", sp_cbIncludeTitleEnabled);
	acpt->addBool("titleCheck", sp_cbIncludeTitleCheck);

	WOLOG_INFO << "m_bHasHeader " << splitBookOpt.isHasHeader();

	bool bDateType = false;
	// 获取依据的数据
	if (splitBookOpt.GetIKAutoFilter() && !bHasSelectRange)
	{
		WriteFilterAccording(acpt, isA1Ref, bHasSelectRange, &splitBookOpt, bDateType, selectIndex);
	}
	else 
	{
		WriteAccording(acpt, isA1Ref, bHasSelectRange, &splitBookOpt, bDateType, selectIndex);
	}


	// 全部的总数
	int preViewCount = 0;

	if (sp_bStartEnabled)
	{
		splitBookOpt.SetDateType(bDateType);
		preViewCount = splitBookOpt.GetPreViewCount(dataTypeIndex);
	}

	acpt->addInt32("preViewCount", preViewCount);
	acpt->addInt32("selectIndex", selectIndex);
	WOLOG_INFO << "preViewCount:" << preViewCount;
	return hr;
}

bool QuerySplitSheet::IsExceedMaxWidthHeight(etoldapi::Range* pRange, int maxCol, int maxHeight)
{
	if (!pRange)
		return false;

	ks_stdptr<IRangeInfo> spRgInfo = pRange;
	ks_stdptr<IKRanges> spIRanges;
	spRgInfo->GetIRanges(&spIRanges);
	UINT count = 0;
	spIRanges->GetCount(&count);
	if (count > 0)
	{
		const RANGE* pRG = NULL;
		spIRanges->GetItem(0, NULL, &pRG);
		if (pRG->Width() > maxCol || pRG->Height() > maxHeight)
		{
			return true;
		}
	}
	return false;
}

void QuerySplitSheet::GetRANGE(etoldapi::Range* pRange, RANGE& range)
{
	if (!pRange)
		return;

	ks_stdptr<IRangeInfo> spRgInfo = pRange;
	ks_stdptr<IKRanges> spIRanges;
	spRgInfo->GetIRanges(&spIRanges);
	UINT count = 0;
	spIRanges->GetCount(&count);
	if (count > 0)
	{
		const RANGE* pRG = NULL;
		spIRanges->GetItem(0, NULL, &pRG);
		range = *pRG;
	}
	return;
}

void QuerySplitSheet::WriteFilterAccording(ISerialAcceptor* acpt, bool isA1Ref, bool bHasSelectRange, wo::SplitBookOpt* splitBookOpt, bool& bDateType, int selectIndex)
{

	WOLOG_INFO << "WriteFilterAccording";

	acpt->addKey("according");
	acpt->beginArray();

	ISheet* pSheet = splitBookOpt->GetISheet();

	RANGE  rg(pSheet->GetBMP());
	splitBookOpt->GetIKAutoFilter()->GetFilterRange(&rg);
	IETStringTools* spStringTools = splitBookOpt->GetIETStringTools();
	for (int i = 0; i < rg.ColTo() - rg.ColFrom() + 1; ++i)
	{
		acpt->beginStruct();
		ks_wstring wsColName = splitBookOpt->get_column_name(pSheet, rg.ColFrom() + i, isA1Ref);

		acpt->addString("colName", wsColName.c_str());

		ks_bstr text;
		spStringTools->GetCellText(NULL, rg.RowFrom(), rg.ColFrom() + i, &text, NULL, -1, NULL);

		ks_wstring tempText = text.c_str();

		acpt->addString("text", tempText.c_str());
		bool isDate = splitBookOpt->isDateTypeField(i, bHasSelectRange);
		if (i == selectIndex)
		{
			bDateType = isDate;
		}

		acpt->addBool("isShowDate", isDate);

		acpt->endStruct();
	}
	acpt->endArray();
}

void QuerySplitSheet::WriteAccording(ISerialAcceptor* acpt, bool isA1Ref, bool bHasSelectRange, wo::SplitBookOpt* splitBookOpt, bool& bDateType, int selectIndex)
{
	WOLOG_INFO << "WriteAccording";
	RECT rx = splitBookOpt->GetRECT();
	BOOL bMerged = FALSE;
	ks_bstr mergeText;
	ISheet* pSheet = splitBookOpt->GetISheet();
	IETStringTools* spStringTools = splitBookOpt->GetIETStringTools();

	RANGE rg(pSheet->GetBMP());
	if (rx.top > 1)
		pSheet->IsMerged(rx.top - 1, rx.left, &bMerged);

	if (bMerged)
	{
		pSheet->RetieveMerge(rx.top - 1, rx.left, &rg);
		spStringTools->GetCellText(NULL, rg.RowFrom(), rg.ColFrom(), &mergeText, NULL, -1, NULL);
	}
	
	acpt->addKey("according");
	acpt->beginArray();
	bool bHasHeader = splitBookOpt->isHasHeader();
	for (int i = 0; i < rx.right - rx.left + 1; ++i)
	{
		acpt->beginStruct();
		ks_wstring wsColName = splitBookOpt->get_column_name(pSheet, rx.left + i, isA1Ref);
		acpt->addString("colName", wsColName.c_str());
		if (bHasHeader)
		{
			if (rx.top < 1)  //不应该出现这种情况，有标题头数据区域应该1开始
			{
				ASSERT(false);
				acpt->endStruct();
				break;
			}
			ks_bstr text;
			ks_wstring tempText;
			if (rg.Contain(rx.top - 1, rx.left + i))
				tempText = mergeText;
			else
			{
				spStringTools->GetCellText(NULL, rx.top - 1, rx.left + i, &text, NULL, -1, NULL);
				tempText = text;
			}
			acpt->addString("text", tempText.c_str());
		}
		else
		{
			acpt->addString("text", __X(""));
		}

		bool isDate = splitBookOpt->isDateTypeField(i, bHasSelectRange);
		if (i == selectIndex)
		{
			bDateType = isDate;
		}

		acpt->addBool("isShowDate", isDate);
		acpt->endStruct();
	}
	acpt->endArray();
}
QueryChartPos::QueryChartPos(KEtWorkbook* wwb): EtQueryExecBase(wwb,  __X("query.chartPos"))
{

}

HRESULT QueryChartPos::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	if (!param.has("linkuuid")) return E_FAIL;
	if (!param.has("sheetStId")) return E_FAIL;

	IDX sheetIdx = GetSheetIdx(param, ctx);
	if (sheetIdx == INVALIDIDX) return E_INVALIDARG;

	ks_stdptr<ISheet> spSheet;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->GetSheet(sheetIdx, &spSheet);
	ks_stdptr<IUnknown> ptrUnk;
	ks_stdptr<IKDrawingCanvas> ptrDrawingCanvas;
	spSheet->GetExtDataItem(edSheetDrawingCanvas, &ptrUnk);
	if (ptrUnk == NULL) 
	{
		WOLOG_ERROR << "[_ExecShapeImgCopy] ptrUnk NULL";
		return WO_FAIL;
	}
	
	ptrDrawingCanvas = ptrUnk;
	
	ks_castptr<drawing::ShapeTree> cpTree = ptrDrawingCanvas;
	if (nullptr == cpTree)
	{
		WOLOG_ERROR << "[_ExecShapeImgCopy]  cpTree null error";
		return WO_FAIL;
	}
	QString uuid = krt::fromUtf16(param.field_str("linkuuid"));
	const drawing::AbstractShape* shape = cpTree->getAbsShape(uuid);
	if (nullptr == shape)
	{
		WOLOG_ERROR << "[_ExecShapeImgCopy]  shape null error";
		return WO_FAIL;
	}
	AbstractLayer* layer = shape->getLayer();
	if (nullptr == layer)
	{
		WOLOG_ERROR << "[_ExecShapeImgCopy]  EtChartShapeTree null error";
		return WO_FAIL;
	}

	layer = layer->getSupLayer();
	if (nullptr == layer)
	{
		WOLOG_ERROR << "[_ExecShapeImgCopy]  EtChartLayer null error";
		return WO_FAIL;
	}

	AbstractModel* etShape = layer->getModel()->getParent();
	if (nullptr == etShape)
	{
		WOLOG_ERROR << "[_ExecShapeImgCopy]  etShape null error";
		return WO_FAIL;
	}

	ks_castptr<drawing::AbstractShape> spEtShape = etShape;
	if (nullptr == spEtShape)
	{
		WOLOG_ERROR << "[_ExecShapeImgCopy]  spEtShape null error";
		return WO_FAIL;
	}

	ks_stdptr<IKClientAnchor> ptrAnchor;
	spEtShape->GetClientAnchor(&ptrAnchor);
	ks_stdptr<IETShapeAnchor> ptrShapeAnchor;
	ptrShapeAnchor = ptrAnchor;
	if (!ptrShapeAnchor) return E_FAIL;
	CELL lt;
	INT ltxOff;
	INT ltyOff;
	CELL rb;
	INT rbxOff;
	INT rbyOff;
	HRESULT hr = ptrShapeAnchor->GetAnchor(lt, ltxOff, ltyOff, rb, rbxOff, rbyOff);
	if (FAILED(hr)) return E_FAIL;


	RANGE rg(spSheet->GetBMP());
	rg.SetSheetFromTo(sheetIdx, sheetIdx);
	rg.SetRowFromTo(lt.row, rb.row);
	rg.SetColFromTo(lt.col, rb.col);
	if(!rg.IsValid())
	{
		WOLOG_ERROR << "[QueryChartPos] rg invalid!!!";
		return E_FAIL;
	}
	acpt->addKey("pos");
	acpt->beginStruct();
		acpt->addKey("rg");
		acpt->beginStruct();
		WriteRANGE(rg, acpt);
		//stid
		acpt->addInt32("sheetStId", param.field_int32("sheetStId"));
		acpt->endStruct();
		LONG shapeId;
		spEtShape->GetShapeID(&shapeId);
		acpt->addInt32("shapeId", shapeId);
	acpt->endStruct();

	return S_OK;
}

QueryBookHasAiFormula::QueryBookHasAiFormula(KEtWorkbook *wwb) : EtQueryExecBase(wwb,  __X("query.bookHasAiFormula"))
{

}

HRESULT QueryBookHasAiFormula::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	// 只有 ksheet and et 才有ai公式
	if (m_wwb->GetCoreWorkbook()->GetBook()->GetBMP()->bDbSheet)
	{
		acpt->addBool("hasAiFormula", false);
		return S_OK;
	}


	acpt->addBool("hasAiFormula", alg::BOOL2bool(m_wwb->GetCoreWorkbook()->GetBook()->HasAiFormula()));
	return S_OK;
}

QueryDataAnalyzeMergeStatus::QueryDataAnalyzeMergeStatus(KEtWorkbook* wwb): EtQueryExecBase(wwb,  __X("query.dataAnalyzeMergeStatus"))
{

}

HRESULT QueryDataAnalyzeMergeStatus::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	if (!param.has("sheetStId"))
		return E_FAIL;
	int sheetStId = param.field_int32("sheetStId");
	bool isUpdating = getDAClientInstance(0)->IsUpdating(sheetStId);
	acpt->addBool("isUpdating", isUpdating);
	IDX sheetIdx = -1;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
	IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWorksheet) 
	{
		WOLOG_INFO << "[QueryDataAnalyzeMergeStatus] not find sheet";
		return E_KSHEET_SHEET_NOT_FOUND;
	}
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!pSheet) 
	{
		WOLOG_INFO << "[QueryDataAnalyzeMergeStatus] not find sheet";
		return E_KSHEET_SHEET_NOT_FOUND;
	}
	ks_stdptr<IEtDataAnalyzeData> spData;
    pSheet->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
    if (!spData)
    {
        return E_FAIL;
    }
	MergeLogList logList = spData->GetDataAnalyseLogList();
	if (logList.empty())
	{
		WOLOG_INFO << "[QueryDataAnalyzeMergeStatus] log is empty";
		return E_FAIL;
	}
	etda::MergeLog* log = logList.back();
	if (log)
	{
		acpt->addString("data", krt::utf16(log->date));
		acpt->addString("time", krt::utf16(log->time));
		acpt->addString("status", krt::utf16(log->status));
	}
	return S_OK;
}

QueryEt2DBConvertable::QueryEt2DBConvertable(KEtWorkbook* wwb)
	: EtQueryExecBase(wwb, __X("query.exportable"))
{
}

HRESULT QueryEt2DBConvertable::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	// 获取参数
	if (!param.has("sheetLimit") || !param.has("enableDocNameCheck") || !param.has("enableHeaderCheck"))
	{
		return E_INVALIDARG;
	}

	int sheetLimit = param.field_int32("sheetLimit");
	bool enableDocNameCheck = param.field_bool("enableDocNameCheck");
	bool enableHeaderCheck = param.field_bool("enableHeaderCheck");

	// 根据启用状态获取相应的关键词数组
	QStringList docKeywords;
	QStringList headerKeywords;
	int minHeaderHits = 0;

	// 只有启用文档名检查时才获取文档关键词
	if (enableDocNameCheck)
	{
		if (param.has("docKeywords"))
		{
			VarObj docKeywordsArray = param.get("docKeywords");
			for (int i = 0; i < docKeywordsArray.arrayLength(); ++i)
			{
				docKeywords.append(krt::fromUtf16(docKeywordsArray.item_str(i)));
			}
		}
	}

	// 只有启用表头检查时才获取表头关键词和最小命中数
	if (enableHeaderCheck)
	{
		if (param.has("headerKeywords"))
		{
			VarObj headerKeywordsArray = param.get("headerKeywords");
			for (int i = 0; i < headerKeywordsArray.arrayLength(); ++i)
			{
				headerKeywords.append(krt::fromUtf16(headerKeywordsArray.item_str(i)));
			}
		}

		if (param.has("minHeaderHits"))
		{
			minHeaderHits = param.field_int32("minHeaderHits");
		}
	}

	// 检查工作表数量限制（包含隐藏工作表，但排除数据库工作表）
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	IDX totalSheetCount = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount();
	IDX validSheetCount = 0;

	// 计算有效工作表数量（排除数据库工作表）
	for (IDX i = 0; i < totalSheetCount; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		HRESULT hr = pBook->GetSheet(i, &spSheet);
		if (FAILED(hr) || !spSheet)
			continue;

		SHEETTYPE sheetType = stUnknown;
		spSheet->GetFullType(&sheetType);

		// 排除数据库工作表，其他类型都计算在内（包括隐藏的）
		if (sheetType != stGrid_DB)
		{
			validSheetCount++;
		}
	}

	if (validSheetCount > sheetLimit)
	{
		acpt->addBool("shouldGuide", false);
		acpt->addKey("matchedDocKeywords");
		acpt->beginArray();
		acpt->endArray();
		acpt->addKey("matchedHeaderKeywords");
		acpt->beginArray();
		acpt->endArray();
		acpt->addInt32("tableNumber", 0);  // 工作表数量超限时，表格数量为0
		acpt->addString("tableRowCol", "");  // 工作表数量超限时，行列信息为空
		return S_OK;
	}

	// 检查是否包含不支持导出的工作表类型（仪表盘、说明页、工作台等）
	for (IDX sheetIdx = 0; sheetIdx < totalSheetCount; ++sheetIdx)
	{
		ks_stdptr<ISheet> spSheet;
		HRESULT hr = pBook->GetSheet(sheetIdx, &spSheet);
		if (FAILED(hr) || !spSheet)
			continue;

		// 检查工作表类型
		SHEETTYPE sheetType = stUnknown;
		spSheet->GetFullType(&sheetType);

		// 检查是否是不支持导出的工作表类型
		if (sheetType == stOldDashBoard ||    // 仪表盘
			sheetType == stDashBoard ||       // 数据库仪表盘
			sheetType == stFlexPaper ||       // 说明页
			sheetType == stWorkbench ||       // 工作台
			sheetType == stApp)               // 应用
		{
			acpt->addBool("shouldGuide", false);
			acpt->addKey("matchedDocKeywords");
			acpt->beginArray();
			acpt->endArray();
			acpt->addKey("matchedHeaderKeywords");
			acpt->beginArray();
			acpt->endArray();
			acpt->addInt32("tableNumber", 0);  // 包含特殊工作表类型时，表格数量为0
			acpt->addString("tableRowCol", "");  // 包含特殊工作表类型时，行列信息为空
			return S_OK;
		}
	}

	// 检查文档名关键词匹配
	QStringList matchedDocKeywords;
	if (enableDocNameCheck && !docKeywords.empty())
	{
		// 获取文档名
		PCWSTR docName = nullptr;
		pBook->GetName(&docName);
		if (docName)
		{
			QString docNameStr = krt::fromUtf16(docName);

			// 转换为小写进行匹配
			docNameStr = docNameStr.toLower();

			for (const auto& keyword : docKeywords)
			{
				QString lowerKeyword = keyword.toLower();

				if (docNameStr.contains(lowerKeyword))
				{
					matchedDocKeywords.append(keyword);
				}
			}
		}

		// 如果启用文档名检查但没有匹配到任何关键词，直接返回失败
		if (matchedDocKeywords.empty())
		{
			acpt->addBool("shouldGuide", false);
			acpt->addKey("matchedDocKeywords");
			acpt->beginArray();
			acpt->endArray();
			acpt->addKey("matchedHeaderKeywords");
			acpt->beginArray();
			acpt->endArray();
			acpt->addInt32("tableNumber", 0);  // 文档名不匹配时，表格数量为0
			acpt->addString("tableRowCol", "");  // 文档名不匹配时，行列信息为空
			return S_OK;
		}
	}

	// 检查超级表数量（如果任何工作表内超级表数量大于1个就不支持导出）
	// 同时统计各种表格数量用于前端打点
	UINT totalSuperTableCount = 0;
	UINT totalPivotTableCount = 0;
	UINT totalRegularTableCount = 0;
	QString tableRowColInfo;     // 存储表格行列信息
	UINT sheetSequence = 1;      // 工作表序号计数器

	for (IDX sheetIdx = 0; sheetIdx < totalSheetCount; ++sheetIdx)
	{
		ks_stdptr<ISheet> spSheet;
		HRESULT hr = pBook->GetSheet(sheetIdx, &spSheet);
		if (FAILED(hr) || !spSheet)
			continue;

		// 跳过数据库工作表
		SHEETTYPE sheetType = stUnknown;
		spSheet->GetFullType(&sheetType);
		if (sheetType == stGrid_DB)
			continue;

		// 获取当前工作表中的超级表数量
		ks_stdptr<ICoreListObjects> spListObjects;
		spSheet->GetExtDataItem(edSheetListObjects, (IUnknown**)&spListObjects);
		UINT sheetSuperTableCount = 0;
		if (spListObjects)
		{
			sheetSuperTableCount = spListObjects->GetCount();
			totalSuperTableCount += sheetSuperTableCount;

			// 如果当前工作表的超级表数量大于1，直接返回不支持
			if (sheetSuperTableCount > 1)
			{
				acpt->addBool("shouldGuide", false);
				acpt->addKey("matchedDocKeywords");
				acpt->beginArray();
				acpt->endArray();
				acpt->addKey("matchedHeaderKeywords");
				acpt->beginArray();
				acpt->endArray();
				acpt->addInt32("tableNumber", totalSuperTableCount + totalPivotTableCount + totalRegularTableCount);
				acpt->addString("tableRowCol", krt::utf16(tableRowColInfo));
				return S_OK;
			}
		}

		// 检查透视表数量
		ks_stdptr<pivot_core::IPivotTableHost> spPivotTableHost;
		spSheet->GetExtDataItem(edSheetPivotTablesHost, (IUnknown**)&spPivotTableHost);
		UINT sheetPivotTableCount = 0;
		if (spPivotTableHost)
		{
			ks_stdptr<pivot_core::IPivotTables> spPivotTables = spPivotTableHost->GetPivotTables();
			if (spPivotTables)
			{
				sheetPivotTableCount = spPivotTables->Count();
				totalPivotTableCount += sheetPivotTableCount;

				// 如果当前工作表的透视表数量大于1，直接返回不支持
				if (sheetPivotTableCount > 1)
				{
					acpt->addBool("shouldGuide", false);
					acpt->addKey("matchedDocKeywords");
					acpt->beginArray();
					acpt->endArray();
					acpt->addKey("matchedHeaderKeywords");
					acpt->beginArray();
					acpt->endArray();
					acpt->addInt32("tableNumber", totalSuperTableCount + totalPivotTableCount + totalRegularTableCount);
					acpt->addString("tableRowCol", krt::utf16(tableRowColInfo));
					return S_OK;
				}
			}
		}

		// 检查合并单元格 - 使用 FindEffectMergeCell 获取合并单元格集合
		IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		if (pWorksheet)
		{
			RANGE usedRange(spSheet->GetBMP());
			pWorksheet->GetUsedRange(&usedRange);

			// 获取 usedRange 中的合并单元格集合
			ks_stdptr<IKRanges> spMergedRanges;
			spSheet->FindEffectMergeCell(usedRange, FALSE, &spMergedRanges);

			UINT mergedCellCount = 0;
			if (spMergedRanges)
			{
				spMergedRanges->GetCount(&mergedCellCount);
			}

			// 如果当前工作表有合并单元格，直接返回不支持
			if (mergedCellCount > 0)
			{
				acpt->addBool("shouldGuide", false);
				acpt->addKey("matchedDocKeywords");
				acpt->beginArray();
				acpt->endArray();
				acpt->addKey("matchedHeaderKeywords");
				acpt->beginArray();
				acpt->endArray();
				acpt->addInt32("tableNumber", totalSuperTableCount + totalPivotTableCount + totalRegularTableCount);
				acpt->addString("tableRowCol", krt::utf16(tableRowColInfo));
				return S_OK;
			}

			// 统计规整表数量（既不是超级表也不是透视表，且没有合并单元格的有数据工作表）
			if (sheetSuperTableCount == 0 && sheetPivotTableCount == 0 && usedRange.IsValid())
			{
				totalRegularTableCount++;
			}

			// 收集工作表行列信息（只统计有数据的工作表）
			if (usedRange.IsValid())
			{
				ROW rowCount = usedRange.RowTo() - usedRange.RowFrom() + 1;
				COL colCount = usedRange.ColTo() - usedRange.ColFrom() + 1;

				// 构建格式：sheet序号_行数_列数
				if (!tableRowColInfo.isEmpty())
				{
					tableRowColInfo += "_";
				}

				// 使用 QString 直接拼接
				tableRowColInfo += QString("sheet%1_%2_%3")
								  .arg(sheetSequence)
								  .arg(rowCount)
								  .arg(colCount);
				sheetSequence++;
			}
		}
	}

	// 检查表头关键词匹配
	QStringList matchedHeaderKeywords;
	if (enableHeaderCheck && !headerKeywords.empty())
	{
		// 遍历所有工作表检查表头
		for (IDX sheetIdx = 0; sheetIdx < totalSheetCount; ++sheetIdx)
		{
			ks_stdptr<ISheet> spSheet;
			HRESULT hr = pBook->GetSheet(sheetIdx, &spSheet);
			if (FAILED(hr) || !spSheet || spSheet->IsDbSheet())
				continue;

			// 获取工作表的 usedRange
			RANGE usedRange(spSheet->GetBMP());
			IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
			if (!pWorksheet)
				continue;

			hr = pWorksheet->GetUsedRange(&usedRange);
			if (FAILED(hr) || !usedRange.IsValid())
				continue;

			// 检查第一行作为表头
			ROW headerRow = usedRange.RowFrom();
			for (COL col = usedRange.ColFrom(); col <= usedRange.ColTo(); ++col)
			{
				// 检查单元格是否被保护
				if (ctx->getProtectionCtx()->isCellHidden(spSheet, headerRow, col))
					continue;

				// 获取单元格文本
				ks_bstr cellText;
				ctx->getStringTools()->GetCellText(spSheet, headerRow, col, &cellText, NULL, -1, NULL);

				if (!cellText.empty())
				{
					QString cellStr = krt::fromUtf16(cellText.c_str());

					// 转换为小写进行匹配
					cellStr = cellStr.toLower();

					for (const auto& keyword : headerKeywords)
					{
						QString lowerKeyword = keyword.toLower();

						if (cellStr.contains(lowerKeyword))
						{
							// 避免重复添加
							if (!matchedHeaderKeywords.contains(keyword))
							{
								matchedHeaderKeywords.append(keyword);
							}
						}
					}
				}
			}
		}
	}

	// 判断是否满足引导条件
	bool shouldGuide = true;

	// 如果启用表头检查且有关键词，但匹配的关键词数量不足
	if (enableHeaderCheck && !headerKeywords.empty() && (int)matchedHeaderKeywords.size() < minHeaderHits)
	{
		shouldGuide = false;
	}

	// 返回结果
	acpt->addBool("shouldGuide", shouldGuide);

	// 返回匹配的文档名关键词
	acpt->addKey("matchedDocKeywords");
	acpt->beginArray();
	for (const auto& keyword : matchedDocKeywords)
	{
		acpt->addString(nullptr, krt::utf16(keyword));
	}
	acpt->endArray();

	// 返回匹配的表头关键词
	acpt->addKey("matchedHeaderKeywords");
	acpt->beginArray();
	for (const auto& keyword : matchedHeaderKeywords)
	{
		acpt->addString(nullptr, krt::utf16(keyword));
	}
	acpt->endArray();

	// 返回识别到的表格总数量（用于前端打点上报）
	acpt->addInt32("tableNumber", totalSuperTableCount + totalPivotTableCount + totalRegularTableCount);

	// 返回表格行列信息（用于前端打点上报）
	acpt->addString("tableRowCol", krt::utf16(tableRowColInfo));

	return S_OK;
}

} // namespace wo
