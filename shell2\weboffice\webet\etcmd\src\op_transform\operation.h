#ifndef __OPERATION_H__
#define __OPERATION_H__
#include <public_header/revision/src/kwcommand.h>
#include "rect_tf.h"
#include "condfmt_tf.h"
#include <public_header/webcommon/src/shapeoperation.h>

namespace wo
{
class KEtRevisionContext;
struct EtShapeAtomOp;
class OpShapeAtomBase;
struct EtChartShapeAtomOp;
class OpChartShapeAtomBase;
class VecOp;

enum OpDir
{
	dir_vert,
	dir_hori,
};

enum OpType
{
	ot_range_SetFormula, // 需要变换公式
	ot_range_Insert, // 特殊的操作
	ot_range_Delete, // 特殊的操作
	ot_range, // 普通的range
	ot_sheets_Add, // 添加sheet
	ot_sheets_Move, // 移动sheet
	ot_range_CutInsertVert,//垂直移动range
	ot_range_CutInsertHorz,//水平移动range
	ot_condfmt_New,
	ot_condfmt_Edit,
	ot_condfmt_Delete,
	ot_condfmt_Swap,
	ot_shape_Modify, // 修改shape属性，被变换目标
	ot_shape_Insert,
	ot_shape_Delete,
	ot_shape_Move, // 修改 z-order
	ot_pivot_table_field_name, // 需要变换透视表字段名
	ot_trans_formulas, // 需要变换公式
	ot_chart_trans_source, // 图表数据源 需要变换公式
	ot_chart_trans_series_source, // 图表系列数据源 需要变换公式
	ot_chart_Delete_series,//删除系列数据源
	ot_chart_Move_series,//移动系列数据源
	ot_chart_Add_series,//增加系列数据源
};

struct OpEnv
{
	IBook* m_bk;
	ks_stdptr<IBookOp> m_bookOp;
	BMP_PTR m_bmp;
	KEtRevisionContext* m_ctx;
};

void resetOpMap();

class OpBase
{
public:
	OpBase(OpEnv* env, const KwCommand* src, OpType tp)
		: m_env(env)
		, m_cmd(nullptr)
		, m_tp(tp)
	{
		if (src)
			m_cmd = KwCommand::copyFrom(src->cast());
	}

	OpBase(const OpBase& r)
		: m_tp(r.m_tp)
		, m_env(r.m_env)
		, m_cmd(nullptr)
	{
		if (r.m_cmd)
			m_cmd = KwCommand::copyFrom(r.m_cmd->cast());
	}

	virtual ~OpBase()
	{
		delete m_cmd;
	}

	virtual KwCommand* detachCmd()
	{
		KwCommand* cmd = m_cmd;
		m_cmd = nullptr;
		return cmd;
	}
	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) = 0;
	virtual bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) { return false; }

	virtual bool tfBySvCrossSht(const OpBase* opSv, VecOp& vecOpCl) { return false; }
	virtual bool tfByClCrossSht(const OpBase* opCl, VecOp& vecOpSv) { return false; }

	virtual bool updateSheetIdx() = 0;
	virtual bool updateShapeIdxPath() { return false; }

	OpType tp() const { return m_tp; }
	const KwCommand* cmd() const { return m_cmd; }
	KwCommand* cmd() { return m_cmd; }
protected:
	bool updateSheetIdxHelper(binary_wo::VarObj& param, WebName objSheet, WebName sheetIdx);
	bool updateTableColumnIdxHelper(binary_wo::VarObj& param, ICoreListObject *, WebName colObjId, WebName colIdx);
	bool updateShapeIdxPathHelper(binary_wo::VarObj& param, WebName objShapeName, WebName idxPathName);

private:
	OpBase& operator=(const OpBase& r);

protected:
	OpType m_tp;
	OpEnv* m_env;
	KwCommand* m_cmd;
};

class OpBaseHasOneObjSheet : public OpBase
{
public:
	OpBaseHasOneObjSheet(OpEnv* env, const KwCommand* src, OpType tp)
		: OpBase(env, src, tp)
	{

	}

	bool updateSheetIdx() override;
};

class OpOneObjSheet : public OpBaseHasOneObjSheet
{
public:
	OpOneObjSheet(OpEnv* env, const KwCommand* src)
		: OpBaseHasOneObjSheet(env, src, ot_range)
	{

	}

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; }
};

class VecOp
{
public:
	~VecOp()
	{
		for (size_t i = 0; i < m_v.size(); ++i)
		{
			delete m_v[i];
		}
	}

	std::vector<OpBase*> m_v;
};

class OpRangeInsert : public OpBaseHasOneObjSheet
{
public:
	OpRangeInsert(OpEnv* env, const KwCommand* src);
	OpRangeInsert(OpEnv* env, const KwCommand* src, const RECT& rc, OpDir dir);

	KwCommand* detachCmd() override;
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) override;

	bool MakeRes(std::vector<RECT>& vec,  VecOp& vecOp);

	RECT m_rc;
	OpDir m_dir;
};

class OpRangeDelete : public OpBaseHasOneObjSheet
{
public:
	OpRangeDelete(OpEnv* env, const KwCommand* src);
	OpRangeDelete(OpEnv* env, const KwCommand* src, const RECT& rc, OpDir dir);

	KwCommand* detachCmd() override;
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) override;

	bool MakeRes(std::vector<RECT>& vec,  VecOp& vecOp);
	bool makeShapesRes(OpShapesPaths& delShapes, VecOp& vecOp);

	RECT m_rc;
	OpDir m_dir;
	OpShapesPaths m_delShapes;
	bool m_bShapeIdxChange;
};

class OpRangeBase : public OpBaseHasOneObjSheet
{
public:
	OpRangeBase(OpEnv* env, const KwCommand* src, OpType tp, RectTrans::Param tsp);
	OpRangeBase(OpEnv* env, const KwCommand* src, const RECT& rc, OpType tp, RectTrans::Param tsp);

	KwCommand* detachCmd() override;
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;

	bool MakeRes(std::vector<RECT>& vec, VecOp& vecOp);
	virtual OpBase* clone(const RECT& rc) = 0;

	RECT m_rc;
	RectTrans::Param m_tsp;
};

class OpRangeExpandAbridge : public OpRangeBase
{
public:
	OpRangeExpandAbridge(OpEnv* env, const KwCommand* src) 
		: OpRangeBase(env, src, ot_range, RectTrans::ts_move_expand_abridge)
	{
	}

	OpRangeExpandAbridge(OpEnv* env, const KwCommand* src, const RECT& rc)
		: OpRangeBase(env, src, rc, ot_range, RectTrans::ts_move_expand_abridge)
	{
	}

	OpBase* clone(const RECT& rc) override
	{
		return new OpRangeExpandAbridge(m_env, m_cmd, rc);
	}
};

class OpRangeMoveAbridgeSplit: public OpRangeBase
{
public:
	OpRangeMoveAbridgeSplit(OpEnv* env, const KwCommand* src) 
		: OpRangeBase(env, src, ot_range, RectTrans::ts_move_abridge_split)
	{
	}

	OpRangeMoveAbridgeSplit(OpEnv* env, const KwCommand* src, const RECT& rc)
		: OpRangeBase(env, src, rc, ot_range, RectTrans::ts_move_abridge_split)
	{
	}

	OpBase* clone(const RECT& rc) override
	{
		return new OpRangeMoveAbridgeSplit(m_env, m_cmd, rc);
	}
};

class OpRangeMove: public OpRangeBase
{
public:
	OpRangeMove(OpEnv* env, const KwCommand* src) 
		: OpRangeBase(env, src, ot_range, RectTrans::ts_move)
	{
	}

	OpRangeMove(OpEnv* env, const KwCommand* src, const RECT& rc)
		: OpRangeBase(env, src, rc, ot_range, RectTrans::ts_move)
	{
	}

	OpBase* clone(const RECT& rc) override
	{
		return new OpRangeMove(m_env, m_cmd, rc);
	}
};

class OpRangeMoveAbridgeSplitNoSheetIdx : public OpRangeMoveAbridgeSplit
{
public:
	OpRangeMoveAbridgeSplitNoSheetIdx(OpEnv* env, const KwCommand* src) 
		: OpRangeMoveAbridgeSplit(env, src)
	{
	}

	OpRangeMoveAbridgeSplitNoSheetIdx(OpEnv* env, const KwCommand* src, const RECT& rc)
		: OpRangeMoveAbridgeSplit(env, src, rc)
	{
	}

	OpBase* clone(const RECT& rc) override
	{
		return new OpRangeMoveAbridgeSplitNoSheetIdx(m_env, m_cmd, rc);
	}

	bool updateSheetIdx() override { return false; }
};

class OpRangeExpandAbridgeNoSheetIdx: public OpRangeExpandAbridge
{
public:
	OpRangeExpandAbridgeNoSheetIdx(OpEnv* env, const KwCommand* src) 
		: OpRangeExpandAbridge(env, src)
	{
	}

	OpRangeExpandAbridgeNoSheetIdx(OpEnv* env, const KwCommand* src, const RECT& rc)
		: OpRangeExpandAbridge(env, src, rc)
	{
	}

	OpBase* clone(const RECT& rc) override
	{
		return new OpRangeExpandAbridgeNoSheetIdx(m_env, m_cmd, rc);
	}

	bool updateSheetIdx() override { return false; }
};

class OpFormulaBase : public OpRangeBase
{
public:
	OpFormulaBase(OpEnv* env, const KwCommand* src, const OpType tp);
	OpFormulaBase(OpEnv* env, const KwCommand* src, OpType tp, RectTrans::Param tsp);
	OpFormulaBase(const OpFormulaBase& ref, const RECT& rc);

	bool tfFormularBySv(IDX iSheet, const OpBase* opSv);

	void setFmla(PCWSTR, const binary_wo::VarObj& booksCtx);
	void getFmla(BSTR*);
	void setSheetIdx(binary_wo::VarObj param);

	int m_idxSht;
	exec_token_vector m_tokVec;

	bool isChangedByTfFml() { return m_isChangedByTfFml; }
private:
	bool m_isChangedByTfFml;
};

class OpRangeSetFormula : public OpFormulaBase
{
public:
	OpRangeSetFormula(OpEnv* env, const KwCommand* src);
	OpRangeSetFormula(const OpRangeSetFormula& ref, const RECT& rc)
		: OpFormulaBase (ref, rc)
	{
	}

	KwCommand* detachCmd() override;

	OpBase* clone(const RECT& rc) override
	{
		return new OpRangeSetFormula(*this, rc);
	}

	bool updateSheetIdx();
};

class OpSetCellFormula : public OpFormulaBase
{
public:
	OpSetCellFormula(OpEnv* env, const KwCommand* src);
	OpSetCellFormula(const OpSetCellFormula& ref, const RECT& rc)
		: OpFormulaBase (ref, rc)
	{
	}
	KwCommand* detachCmd() override;

	OpBase* clone(const RECT& rc) override
	{
		return new OpSetCellFormula(*this, rc);
	}
};

class OpAutoFill : public OpBase
{
public:
	OpAutoFill(OpEnv* env, const KwCommand* cmd);
	OpAutoFill(OpEnv* env, const KwCommand* cmd, 
		const RECT& src, const RECT& dst, OpDir dir, bool isFlashFill);

	KwCommand* detachCmd() override;

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	virtual bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) override { return false; }
	bool updateSheetIdx() override;

private:
	bool MakeRes(std::vector<RECT>& resSrc, std::vector<RECT>& resDst, VecOp& vecOp);

private:
	RECT m_src;
	RECT m_dst;
	OpDir m_dir;
	bool m_isFlashFill;
};

class OpSheetsAdd : public OpBase
{
public:
	OpSheetsAdd(OpEnv* env, const KwCommand* cmd);

	KwCommand* detachCmd() override;

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; };
	virtual bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) override { return false; }
	bool updateSheetIdx() override;
};

class OpSheetsMove : public OpBase
{
public:
	OpSheetsMove(OpEnv* env, const KwCommand* cmd);

	KwCommand* detachCmd() override;

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; };
	virtual bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) override { return false; }
	bool updateSheetIdx() override;
};

class OpSheetsCopy : public OpBase
{
public:
	OpSheetsCopy(OpEnv* env, const KwCommand* cmd);

	KwCommand* detachCmd() override;

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; };
	virtual bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) override { return false; }
	bool updateSheetIdx() override;
};

class EtOpShapeHelper : public OpShapeHelper
{
public:
	EtOpShapeHelper(binary_wo::VarObj param);
	EtOpShapeHelper(const OpShapePath& path);
	EtOpShapeHelper(const OpShapeAtomBase& atomOp);

	bool tfBySv(const OpBase* opSv);
	bool tfByCl(CmdShapeOpType opType, const OpShapePath& shape);

	static bool tfShapesBySv(const OpBase* opSv, const OpShapesPaths& paths, OpShapesPaths& result);
};

class OpShapeObj : public OpBaseHasOneObjSheet
{
public:
	OpShapeObj(OpEnv* env, const KwCommand* src, OpType tp = ot_shape_Modify);
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	bool updateShapeIdxPath() override;
	KwCommand* detachCmd() override;
	virtual OpShapeObj* clone();

	const OpShapePath& shapePath() const { return m_shapePath; }

protected:
	bool makeResult(OpShapeHelper& helper, VecOp& vecOpCl);

protected:
	bool m_bShapeIdxChanged;
	OpShapePath m_shapePath;
};

class OpShapeAtomBase : public OpBase
{
public:
	OpShapeAtomBase(OpEnv* env, const EtShapeAtomOp& op, OpType tp);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; }
 	bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) override;
	bool tfByClCrossSht(const OpBase* opCl, VecOp& vecOpSv) override;
	bool updateSheetIdx() override { return false; }
	virtual OpShapeAtomBase* clone() const = 0;

	const binary_wo::VarInt32Array shapeIdxPath() const;
	const binary_wo::VarInt32Array shapeCount() const;
	const std::vector<int32>& shapeIdxPathV() const;
	const std::vector<int32>& shapeCountV() const;
	int32 moveToPos() const { return m_extPos; }

protected:
	bool tfByShapesDelSeq(const OpShapesPaths& shapesSeq, VecOp& vecOpSv);
	bool tfByShapesDelSet(const OpShapesPaths& shapesSet, VecOp& vecOpSv);
	bool tfByDelShape(const OpShapePath& shape, VecOp& vecOpSv);

protected:
	OpShapePath m_shapePath;
	int32 m_extPos;
};

class OpChartShapeAtomBase : public OpShapeAtomBase
{
public:
	OpChartShapeAtomBase(OpEnv* env, const EtChartShapeAtomOp& op, OpType tp);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; }
 	bool tfByCl(const OpBase* opCl, VecOp& vecOpSv) override;
	bool tfByClCrossSht(const OpBase* opCl, VecOp& vecOpSv) override;
	bool updateSheetIdx() override { return false; }
	virtual OpChartShapeAtomBase* clone() const = 0;

	const binary_wo::VarInt32Array shapeIdxPath() const;
	const binary_wo::VarInt32Array shapeCount() const;
	const std::vector<int32>& shapeIdxPathV() const;
	const std::vector<int32>& shapeCountV() const;
	const WebID getShapeObjId() const;
	int32 moveToPos() const { return m_extPos; }

protected:
	bool tfByShapesDelSeq(const OpShapesPaths& shapesSeq, VecOp& vecOpSv);
	bool tfByShapesDelSet(const OpShapesPaths& shapesSet, VecOp& vecOpSv);
	bool tfByDelShape(const OpShapePath& shape, VecOp& vecOpSv);

protected:
	WebID shapeObjId;
};

class OpShapeAtomInsert : public OpShapeAtomBase
{
public:
	OpShapeAtomInsert(OpEnv* env, const EtShapeAtomOp& op, bool bReverse)
		: OpShapeAtomBase(env, op, ot_shape_Insert)
		, m_bReverseDel(bReverse)
	{
	}
	OpShapeAtomBase* clone() const override { return new OpShapeAtomInsert(*this); }
	bool isReverseDel() const { return m_bReverseDel; }
private:
	bool m_bReverseDel;
};

class OpShapeAtomDelete : public OpShapeAtomBase
{
public:
	OpShapeAtomDelete(OpEnv* env, const EtShapeAtomOp& op)
		: OpShapeAtomBase(env, op, ot_shape_Delete)
	{
	}
	OpShapeAtomBase* clone() const override { return new OpShapeAtomDelete(*this); }
};

class OpShapeAtomMove : public OpShapeAtomBase
{
public:
	OpShapeAtomMove(OpEnv* env, const EtShapeAtomOp& op, bool bReverse)
		: OpShapeAtomBase(env, op, ot_shape_Move)
		, m_bReverse(bReverse)
	{
	}
	OpShapeAtomBase* clone() const override { return new OpShapeAtomMove(*this); }
	bool isReverse() const { return m_bReverse; }
private:
	bool m_bReverse;
};

class OpChartShapeAtomDelete : public OpChartShapeAtomBase
{
public:
	OpChartShapeAtomDelete(OpEnv* env, const EtChartShapeAtomOp& op, bool bReverse)
		: OpChartShapeAtomBase(env, op, ot_chart_Delete_series)
		, m_bReverse(bReverse)
	{
	}
	OpChartShapeAtomBase* clone() const override { return new OpChartShapeAtomDelete(*this); }
	bool isReverse() const { return m_bReverse; }
private:
	bool m_bReverse;
};

class OpChartShapeAtomAdd : public OpChartShapeAtomBase
{
public:
	OpChartShapeAtomAdd(OpEnv* env, const EtChartShapeAtomOp& op, bool bReverse)
		: OpChartShapeAtomBase(env, op, ot_chart_Add_series)
		, m_bReverse(bReverse)
	{
	}
	OpChartShapeAtomBase* clone() const override { return new OpChartShapeAtomAdd(*this); }
	bool isReverse() const { return m_bReverse; }
private:
	bool m_bReverse;
};

class OpChartShapeAtomMove : public OpChartShapeAtomBase
{
public:
	OpChartShapeAtomMove(OpEnv* env, const EtChartShapeAtomOp& op, bool bReverse)
		: OpChartShapeAtomBase(env, op, ot_chart_Move_series)
		, m_bReverse(bReverse)
	{
	}
	OpChartShapeAtomBase* clone() const override { return new OpChartShapeAtomMove(*this); }
	bool isReverse() const { return m_bReverse; }
private:
	bool m_bReverse;
};

class OpDeleteShape : public OpShapeObj
{
public:
	OpDeleteShape(OpEnv* env, const KwCommand* src)
		: OpShapeObj(env, src)
	{
		m_tp = ot_shape_Delete;
	}
	OpShapeObj* clone() override;
};

class OpHyperlink : public OpBaseHasOneObjSheet
{
public:
	OpHyperlink(OpEnv* env, const KwCommand* src);
	OpHyperlink(OpEnv* env, const KwCommand* src, RECT& rc);

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	bool updateSheetIdx() override;
	KwCommand* detachCmd() override;

private:
	bool MakeRes(std::vector<RECT>& vec, VecOp& vecOp);
	bool isValidRect(const RECT& rc);

private:
	RECT m_rc;
};

class OpRangePaste : public OpBase
{
public:
	OpRangePaste(OpEnv* env, const KwCommand* src);
protected:
	OpRangePaste(OpEnv* env, const KwCommand* cmd, const RECT &src, const RECT &dest, OpDir dir);//单选区
	OpRangePaste(OpEnv* env, const KwCommand* src, const std::vector<RECT>& rcVec, const RECT &dest, OpDir dir);//多选区
	
	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	bool updateSheetIdx() override;
	KwCommand* detachCmd();
private:
	//单选区的判断是否发生了操作变换的方法。
	bool oneSrcRangeTfBySv(const OpBase* opSv, VecOp& vecOpCl);
	//多选区的判断是否发生了操作变换的方法。
	bool multiSrcRangeTfBySv(const OpBase* opSv, VecOp& vecOpCl);

	//检查多选区的每一块的复制区域是否发生了操作变换
	bool tfSingleRcBySv(const OpBase* opSv, const RECT &rc, std::vector<RECT> &vec);
	//检查粘贴区域(目前只支持单块粘贴区域)是否发生了操作变换。
	bool tfDestRangeBySv(const OpBase* opSv, VecOp& vecOpCl, RECT& newDest);

	bool MakeDestRes(std::vector<RECT>& resDst, VecOp& vecOp, RECT& newDest);//多复制区域用
	bool MakeRes(std::vector<RECT>& resSrc, std::vector<RECT>& resDst, VecOp& vecOp);//单复制区域用

	bool isValidRect(const RECT& rc);
	bool isRectTransformed(const std::vector<RECT>& vec, const RECT &rc);

private:
	bool m_multiRangeMode;//是否是多重选区状态
	std::vector<RECT> m_rcVec;//多选区用这个
	RECT m_src;//单选区用这个
	RECT m_dest;
	OpDir m_dir;
};

class OpFreezePanes : public OpBaseHasOneObjSheet
{
public:
	explicit OpFreezePanes(OpEnv* env, const KwCommand* src);
	
	bool tfBySv(const OpBase* srv_op, VecOp&) override;
	KwCommand* detachCmd() override;

private:
	RECT range_rect_;
	RECT active_rect_;
};

// Pseudo-command
class OpRangeCutInsertVert : public OpBase
{
public:
	OpRangeCutInsertVert(OpEnv* env, const RECT& rcTop, const RECT& rcBottom);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; };
	bool updateSheetIdx() override { return false; };

	RECT m_rcTop, m_rcBottom;
};

class OpRangeCutInsertHorz : public OpBase
{
public:
	OpRangeCutInsertHorz(OpEnv* env, const RECT& rcLeft, const RECT& rcRight);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; };
	bool updateSheetIdx() override { return false; };

	RECT m_rcLeft, m_rcRight;
};

class OpConditionFormatBase : public OpBase
{
public:
	OpConditionFormatBase(OpEnv* env, const KwCommand* src, OpType tp);

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	virtual bool updateSheetIdx() override;
	virtual KwCommand* detachCmd() override;

	virtual bool MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp);
	int priority() const;
protected:
	void initRgsFromCmd();
	void initPriorityFromCmd();
	
protected:
	std::vector<RECT> m_vecSrc;
	int m_iPriority;
};

class OpNewConditionFormat : public OpConditionFormatBase
{
public:
	OpNewConditionFormat(OpEnv* env, const KwCommand* cmd);
	OpNewConditionFormat(OpEnv* env, const KwCommand* cmd, const std::vector<RECT>& vecSrc);

	virtual bool MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp) override;
};

class OpDelConditionFormat : public OpConditionFormatBase
{
public:
	OpDelConditionFormat(OpEnv* env, const KwCommand* cmd);
	OpDelConditionFormat(OpEnv* env, const KwCommand* cmd, const std::vector<RECT>& vecSrc);
	OpDelConditionFormat(OpEnv* env, const KwCommand* cmd, int iPriority);

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	virtual bool MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp) override;
	bool MakeRes(int iPriority, VecOp& vecOp);
};

class OpEditConditionFormat : public OpConditionFormatBase
{
public:
	OpEditConditionFormat(OpEnv* env, const KwCommand* cmd);
	OpEditConditionFormat(OpEnv* env, const KwCommand* cmd, const std::vector<RECT>& vecSrc);
	OpEditConditionFormat(OpEnv* env, const KwCommand* cmd, int iPriority);

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	virtual bool MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp) override;
	bool MakeRes(int iPriority, VecOp& vecOp);
};

class OpSwapConditionFormat : public OpBase
{
public:
	OpSwapConditionFormat(OpEnv* env, const KwCommand* cmd);
	OpSwapConditionFormat(OpEnv* env, const KwCommand* cmd, const CfSwapPara& para);

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	virtual bool updateSheetIdx() override;
	virtual KwCommand* detachCmd() override;

	bool MakeRes(const CfSwapPara& para, VecOp& vecOp);
	const CfSwapPara& swapPara() const;
private:
	CfSwapPara m_swapPara;
};

class OpRangeActiveCell : public OpBaseHasOneObjSheet
{
public:
	OpRangeActiveCell(OpEnv *env, const KwCommand *src);
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	bool MakeRes(std::vector<RECT>& res, VecOp& vecOp, bool bChangeAC);
	virtual KwCommand* detachCmd() PURE;
	virtual OpBase* clone(const RECT& rc, const RECT& ac) PURE;	

	RECT m_rc;
	RECT m_ac;
	RectTrans::Param m_tsp;
};

class OpRangeAutoSum : public OpRangeActiveCell
{
public:
	OpRangeAutoSum(OpEnv *env, const KwCommand *src);
	OpRangeAutoSum(OpEnv *env, const KwCommand *src, const RECT& rc, const RECT& ac, RectTrans::Param tsp);
	KwCommand* detachCmd() override;
	OpBase* clone(const RECT& rc, const RECT& ac) override;
};

class OpAllowEditRange : public OpBase
{
public:
	OpAllowEditRange(OpEnv* env, const KwCommand* src);
	OpAllowEditRange(OpEnv* env, const KwCommand* src, const std::vector<RECT>& vecSrc);

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	bool updateSheetIdx() override;
	KwCommand* detachCmd();

private:
	bool MakeRes(std::vector<RECT>& resSrc, VecOp& vecOp);

	std::vector<RECT> m_vecSrc;
};

class OpShapePaste : public OpBase
{
public:
	OpShapePaste(OpEnv* env, const KwCommand* src);
	OpShapePaste(OpEnv* env, const KwCommand* cmd, const RECT &dest, OpDir dir);

	virtual bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	virtual bool tfBySvCrossSht(const OpBase* opSv, VecOp& vecOpCl) override;
	bool updateSheetIdx() override;
	bool updateShapeIdxPath() override;
	KwCommand* detachCmd() override;
	const OpShapesPaths& cutShapes() const { return m_cutShapes; }

private:
	bool makeShapesRes(OpShapesPaths& shapes, bool bSelShapes, VecOp& vecOp);
	bool MakeRes(std::vector<RECT>& resDst, VecOp& vecOp);
	bool isValidRect(const RECT& rc);

	RECT m_dest;
	OpDir m_dir;
	OpShapesPaths m_cutShapes;
	OpShapesPaths m_selShapes;
	bool m_bCutShapesChanged;
	bool m_bSelShapesChanged;
};

class OpChartShape : public OpShapeObj
{
public:
	OpChartShape(OpEnv* env, const KwCommand* src, OpType tp = ot_shape_Modify)
		: OpShapeObj(env, src, tp)
	{
	}

	bool updateShapeIdxPath() override;
private:
	bool updateChartShapeIdxPath(binary_wo::VarObj &param);
	OpShapeObj* clone() override;
};

class OpChartDeleteSeries : public OpChartShape
{
public:
	OpChartDeleteSeries(OpEnv* env, const KwCommand* src)
		: OpChartShape(env, src, ot_chart_Delete_series)
	{
	}
private:
	OpShapeObj* clone(){return new OpChartDeleteSeries(*this);}
};

class OpChartAddSeries : public OpChartShape
{
public:
	OpChartAddSeries(OpEnv* env, const KwCommand* src)
		: OpChartShape(env, src, ot_chart_Add_series)
	{
	}
private:
	OpShapeObj* clone(){return new OpChartAddSeries(*this);}
};

class OpChartMoveSeries : public OpChartShape
{
public:
	OpChartMoveSeries(OpEnv* env, const KwCommand* src)
		: OpChartShape(env, src, ot_chart_Move_series)
	{
	}
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	int fromOrder() const;
	int toOrder() const;
	bool tfAddSv(int pos, int& fOrder, int& tOrder);
	bool tfDelSv(int pos, int& fOrder, int& tOrder);
private:
	OpShapeObj* clone(){return new OpChartMoveSeries(*this);}
};

class OpMultiRangeBase : public OpBaseHasOneObjSheet
{
public:
	OpMultiRangeBase(OpEnv* env, const KwCommand* src, OpType tp, RectTrans::Param tsp);
	OpMultiRangeBase(OpEnv* env, const KwCommand* src, const std::vector<RECT>& rcVec, OpType tp, RectTrans::Param tsp);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	bool tfSingleRcBySv(const OpBase* opSv, const RECT &rc, std::vector<RECT> &vec);

	bool isRectTransformed(const std::vector<RECT>& vec, const RECT &rc);
	virtual OpBase* clone(const std::vector<RECT>& rc) = 0;

	std::vector<RECT> m_rcVec;
	RectTrans::Param m_tsp;
};

class OpMultiRangeSimple : public OpMultiRangeBase
{
public:
	OpMultiRangeSimple(OpEnv* env, const KwCommand* src, OpType tp, RectTrans::Param tsp, WebName name = "rgs");
	OpMultiRangeSimple(OpEnv* env, const KwCommand* src, const std::vector<RECT>& rcVec, OpType tp, RectTrans::Param tsp);

	KwCommand* detachCmd() override;
};

class OpMultiRangeExpandAbridge : public OpMultiRangeSimple
{
public:
	OpMultiRangeExpandAbridge(OpEnv* env, const KwCommand* src) 
		: OpMultiRangeSimple(env, src, ot_range, RectTrans::ts_move_expand_abridge)
	{}

	OpMultiRangeExpandAbridge(OpEnv* env, const KwCommand* src, const std::vector<RECT>& rcVec)
		: OpMultiRangeSimple(env, src, rcVec, ot_range, RectTrans::ts_move_expand_abridge)
	{}

	OpBase* clone(const std::vector<RECT>& rcVec) override
	{
		return new OpMultiRangeExpandAbridge(m_env, m_cmd, rcVec);
	}
};

class OpMultiRangeMoveAbridgeSplit: public OpMultiRangeSimple
{
public:
	OpMultiRangeMoveAbridgeSplit(OpEnv* env, const KwCommand* src) 
		: OpMultiRangeSimple(env, src, ot_range, RectTrans::ts_move_abridge_split)
	{
	}

	OpMultiRangeMoveAbridgeSplit(OpEnv* env, const KwCommand* src, const std::vector<RECT>& rcVec)
		: OpMultiRangeSimple(env, src, rcVec, ot_range, RectTrans::ts_move_abridge_split)
	{
	}

	OpBase* clone(const std::vector<RECT>& rcVec) override
	{
		return new OpMultiRangeMoveAbridgeSplit(m_env, m_cmd, rcVec);
	}
};

class OpPivotGroupRangeExpandAbridge : public OpBaseHasOneObjSheet
{
public:
	OpPivotGroupRangeExpandAbridge(OpEnv* env, const KwCommand* src);

	OpPivotGroupRangeExpandAbridge(OpEnv* env, const KwCommand* src, const RECT& rcCell, std::vector<RECT>& rcSelect);

	KwCommand* detachCmd() override;
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;

	bool MakeRes(VecOp& vecOpCell, VecOp& vecOpSelect, VecOp& vecOp);
	//不应该调到这里
	OpBase* clone(const RECT& rcCell, std::vector<RECT>& rcSelect)
	{
		return new OpPivotGroupRangeExpandAbridge(m_env, m_cmd, rcCell, rcSelect);
	}

	RECT m_rcCell;
	std::vector<RECT> m_rcSelect;
	std::unique_ptr<OpRangeExpandAbridge> m_spCellOp;
	std::unique_ptr<OpMultiRangeExpandAbridge> m_spSelectOp;
	RectTrans::Param m_tsp;
};

class OpSetDataSourceFml : public OpFormulaBase
{
public:
	OpSetDataSourceFml(OpEnv* env, const KwCommand* src, WebStr fml);
	virtual OpBase* clone(const RECT& rc) override;
	ks_wstring dataSourceFml();

private:
	bool m_hasEqualSign;
};

class IDataSourceFetcher
{
public:
	virtual ~IDataSourceFetcher() {}
	virtual bool hasDataSource(const binary_wo::VarObj & param) = 0;
	virtual LPCWSTR dataSource(const binary_wo::VarObj & param) = 0;
	virtual void setDataSource(binary_wo::VarObj & param, LPCWSTR fml) = 0;
};

class DataSouceKeyFetcher : public IDataSourceFetcher
{
public:
	DataSouceKeyFetcher(WebName key) : m_key(key) {}

	virtual bool hasDataSource(const binary_wo::VarObj & param) override { return param.has(m_key); }
	virtual LPCWSTR dataSource(const binary_wo::VarObj & param) override { return param.field_str(m_key); }
	virtual void setDataSource(binary_wo::VarObj & param, LPCWSTR fml) override { param.add_field_str(m_key, fml); }
private:
	WebName m_key;
};

class DataSouceObjKeyFetcher : public IDataSourceFetcher
{
public:
	DataSouceObjKeyFetcher(WebName objName, WebName key);

	virtual bool hasDataSource(const binary_wo::VarObj & param) override;
	virtual LPCWSTR dataSource(const binary_wo::VarObj & param) override;
	virtual void setDataSource(binary_wo::VarObj & param, LPCWSTR fml) override;

private:
	WebName m_objName;
	WebName m_key;
};

class OpSetDataSource : public OpChartShape
{
public:
	OpSetDataSource(OpEnv* env, const KwCommand* src);
	OpSetDataSource(OpEnv* env, const KwCommand* src, IDataSourceFetcher * fetcher);
	KwCommand* detachCmd() override;
	bool updateSheetIdx();

	OpShapeObj* clone() override { return new OpSetDataSource(m_env, m_cmd); }
	OpFormulaBase * opFormulaBase() { return m_spSourceFml ? m_spSourceFml.get() : nullptr; }

private:
	void initSourceFml(IDataSourceFetcher * fetcher);

private:
	std::unique_ptr<OpSetDataSourceFml> m_spSourceFml;
	std::unique_ptr<IDataSourceFetcher> m_spFetcher;
};


class OpSetCategoryDataSource : public OpSetDataSource
{
public:
	OpSetCategoryDataSource(OpEnv* env, const KwCommand* src);
	OpShapeObj* clone() override { return new OpSetCategoryDataSource(m_env, m_cmd); }
};


class OpDataLabelOption : public OpSetDataSource
{
public:
	OpDataLabelOption(OpEnv* env, const KwCommand* src);
	OpShapeObj* clone() override { return new OpDataLabelOption(m_env, m_cmd); }
};

OpBase* CreateOp(OpEnv* env, const KwCommand* src);

class OpSetPageSetting: public OpMultiRangeBase
{
public:
	OpSetPageSetting(OpEnv* env, const KwCommand* src);
	OpSetPageSetting(OpEnv* env, const KwCommand* src, const std::vector<RECT>& rcVec)
		: OpMultiRangeBase(env, src, rcVec, ot_range, RectTrans::ts_move_expand_abridge)
	{}

	OpBase* clone(const std::vector<RECT>& rcVec) override
	{
		return new OpSetPageSetting(m_env, m_cmd, rcVec);
	}

	void initRcVecfromSrcCmd(const KwCommand* src);
	void writeBackNewCmd();

	KwCommand* detachCmd() override;
};

class OpDbTableColumn : public OpBaseHasOneObjSheet
{
public:
	OpDbTableColumn(OpEnv* env, const KwCommand* src);
	OpDbTableColumn(OpEnv* env, const KwCommand* src, const RECT& rc);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	KwCommand* detachCmd() override;
	bool MakeRes(std::vector<RECT>& vec, VecOp& vecOp);
private:
	RECT m_rc;
};

struct IOpMultiFormulasPack
{
	virtual ~IOpMultiFormulasPack() {}
	virtual std::vector<exec_token_vector> unwrapTokenVecs(binary_wo::VarObj &param) = 0;
	virtual void wrapTokenVecs(std::vector<exec_token_vector> &tokenVecs, binary_wo::VarObj &param) = 0;
	virtual CS_COMPILE_PARAM getCCP() = 0;
};

class OpMultiFormulasBase : public OpBase
{
public:
	OpMultiFormulasBase(OpEnv* env, const KwCommand* src, IOpMultiFormulasPack *pack);
	bool tfFormularsBySv(IDX iSheet, const OpBase* opSv);
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override
	{
		return false;
	}

	void getFmlaToken(PCWSTR fmla, const binary_wo::VarObj& booksCtx, exec_token_vector &outTokVec);
	void getFmla(exec_token_vector &tokVec, BSTR* bstrFmla);
	KwCommand* detachCmd() override;
	bool isChangedByTfFml() { return m_isChangedByTfFml; }
protected:
	IOpMultiFormulasPack *m_pack;

public:
	std::vector<exec_token_vector> m_tokVecs;
private:
	bool m_isChangedByTfFml;
};


class OpSetSeriesDataSourceFml : public OpMultiFormulasBase, private IOpMultiFormulasPack
{
public:
	OpSetSeriesDataSourceFml(OpEnv* env, const KwCommand* src);

	virtual bool updateSheetIdx()
	{
		binary_wo::VarObj param = m_cmd->cast().get("param");
		return updateSheetIdxHelper(param , "objSheet", "sheetIdx");
	}

public:
	std::vector<exec_token_vector> unwrapTokenVecs(binary_wo::VarObj &param) override;
	void wrapTokenVecs(std::vector<exec_token_vector> &tokenVecs, binary_wo::VarObj &param) override;
	CS_COMPILE_PARAM getCCP() override
	{
		CS_COMPILE_FLAGS ccf = cpfSysDefault | cpfRgForceSheet | cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo;
		CS_COMPILE_PARAM ccp(ccf, m_idxSht, 0, 0);
		return ccp;
	}
private:
	int m_idxSht;
};


class OpSetSeriesDataSource : public OpChartShape
{
public:
	OpSetSeriesDataSource(OpEnv* env, const KwCommand* src);

	KwCommand* detachCmd() override;

	bool updateSheetIdx();
	
	OpShapeObj* clone() override { return new OpSetSeriesDataSource(m_env, m_cmd); }
	OpMultiFormulasBase * opFormulaBase() { return m_spSourceFml ? m_spSourceFml.get() : nullptr; }

private:
	std::unique_ptr<OpSetSeriesDataSourceFml> m_spSourceFml;
};


class OpCreatePivotTable : public OpMultiFormulasBase, private IOpMultiFormulasPack
{
public:
	OpCreatePivotTable(OpEnv* env, const KwCommand* src);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override
	{
		return m_rangeOp.tfBySv(opSv, vecOpCl);
	}

	bool updateSheetIdx() override;
	KwCommand* detachCmd() override;

private:
	std::vector<exec_token_vector> unwrapTokenVecs(binary_wo::VarObj &param) override;
	void wrapTokenVecs(std::vector<exec_token_vector> &tokenVecs, binary_wo::VarObj &param) override;

	CS_COMPILE_PARAM getCCP() override
	{
		CS_COMPILE_FLAGS ccf = cpfSysDefault | cpfRgForceSheet | cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo;
		CS_COMPILE_PARAM ccp(ccf, 0, 0, 0);
		return ccp;
	}

	OpRangeExpandAbridge m_rangeOp;
};

class OpPivotTableSetSourceData : public OpMultiFormulasBase, private IOpMultiFormulasPack
{
public:
	OpPivotTableSetSourceData(OpEnv* env, const KwCommand* src);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override
	{
		return m_rangeOp.tfBySv(opSv, vecOpCl);
	}

	virtual bool updateSheetIdx()
	{
		return m_rangeOp.updateSheetIdx();
	}

	KwCommand* detachCmd() override;

private:
	std::vector<exec_token_vector> unwrapTokenVecs(binary_wo::VarObj &param) override;
	void wrapTokenVecs(std::vector<exec_token_vector> &tokenVecs, binary_wo::VarObj &param) override;
	CS_COMPILE_PARAM getCCP() override
	{
		CS_COMPILE_FLAGS ccf = cpfSysDefault | cpfRgForceSheet | cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo;
		CS_COMPILE_PARAM ccp(ccf, 0, 0, 0);
		return ccp;
	}

	OpRangeExpandAbridge m_rangeOp;
};

class OpPivotTableRangeFieldName : public OpRangeBase
{
public:
	OpPivotTableRangeFieldName(OpEnv* env, const KwCommand* src);
	OpPivotTableRangeFieldName(OpEnv* env, WebID objPivotTable, WebStr fieldName, WebStr newFieldName);
	OpPivotTableRangeFieldName(OpEnv* env, const KwCommand* src, const RECT& rc, WebID objPivotTable, WebStr fieldName, WebStr newFieldName);

	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
	KwCommand* detachCmd() override;

	const ks_wstring& getNewFieldName() const
	{
		return m_newFieldName;
	}

	const ks_wstring& getFieldName() const
	{
		return m_fieldName;
	}

	OpBase* clone(const RECT& rc) override
	{
		return new OpPivotTableRangeFieldName(m_env, m_cmd, rc, m_objPivotTable, m_fieldName.c_str(), m_newFieldName.c_str());
	}

private:
	WebID m_objPivotTable;
	ks_wstring m_newFieldName;
	ks_wstring m_fieldName;
};

class OpsetSubscriptionOption : public OpBase
{
public:
	OpsetSubscriptionOption(OpEnv* env, const KwCommand* src)
		: OpBase(env, src, ot_range)
	{

	}

	bool updateSheetIdx() override;
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; }
};

class OpMultiSheetsOp : public OpBase
{
public:
	OpMultiSheetsOp(OpEnv* env, const KwCommand* src)
		: OpBase(env, src, ot_range)
	{

	}

	bool updateSheetIdx() override;
	bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override { return false; }
};

class OpRangeInsertNumberCol: public OpRangeInsert
{
public:
    OpRangeInsertNumberCol(OpEnv* env, const KwCommand* src);
    OpRangeInsertNumberCol(OpEnv* env, const KwCommand* src, const RECT& rc, OpDir dir, const RECT& rcCell);
    KwCommand* detachCmd() override;
    bool tfBySv(const OpBase* opSv, VecOp& vecOpCl) override;
    bool MakeRes(VecOp& vecOpCol, VecOp& vecOpCell, VecOp& vecOp);
    RECT m_rcCell;
    std::unique_ptr<OpRangeExpandAbridge> m_pCellOp;
};


} // namespace wo
#endif // __OPERATION_H__
