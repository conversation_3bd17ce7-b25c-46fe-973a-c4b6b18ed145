﻿#include "etstdafx.h"

#include "csv_adapter.h"
#include "database/database_utils.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/db_field_recognize_helper.h"
#include "util.h"
#include "utils/et_gridsheet_utils.h"
#include "sort_helper.h"

namespace wo
{

CsvAdapter::CsvAdapter(PCWSTR filePath) : m_filePath(filePath)
{
	VS(_appcore_CreateObject(CLSID_KETTextImport, IID_ITextImport, (void**)&m_spTextImport));
}

HRESULT CsvAdapter::Init(CsvParam param)
{
	HRESULT hr = m_spTextImport->Init(param.ttcType, m_filePath, 0);
	if (FAILED(hr))
		return hr;
	VS(m_spTextImport->SetSplitType(param.splitType));
	VS(m_spTextImport->SetParseSingleQuoteAsPrefix(TRUE));
	m_spTextImport->SetParseFormulaAsText(param.bParseFormulaAsText);
	VS(m_spTextImport->SetParseNumAsText(param.bParseNumAsText));
	VS(m_spTextImport->SetParseSpecialNumAsText(param.bParseSpecialNumAsText));
	m_spTextImport->SetIgnoreAllLeadingBlankLines(param.bIgnoreAllLeadingBlankLines);
	// 仅逗号作为分隔符
	VS(m_spTextImport->SetParam(et_TextQualifierDoubleQuote, FALSE, FALSE, FALSE, TRUE, FALSE, FALSE, 0));
	VS(m_spTextImport->SetMarkStringInDoubleQuoteAsTheSameLine(TRUE));

	hr = m_spTextImport->CheckOverFlowedState(m_rowCnt, m_colCnt);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT CsvAdapter::Exec(ISheet* pSheet, const RANGE* pRg)
{
	// 同步数据场景, 不需要产生协作记录 (不存在合理的"操作者")
	DbSheet::DisableDbTrackHistoryScope dbHistoryDisabler;

	if (!pSheet || !pRg)
		return E_FAIL;
	if (!pRg->IsValid())
		return E_FAIL;
	HRESULT hr = m_spTextImport->SetSheet(pSheet);
	if (FAILED(hr))
		return hr;
	hr = m_spTextImport->SetDestRange(pRg);
	if (FAILED(hr))
		return hr;
	hr = m_spTextImport->SetValue();
	if (FAILED(hr))
		return hr;
	return S_OK;
}

void CsvAdapter::GetRowColInfo(ROW& rowCnt, COL& colCnt) const
{
	rowCnt = m_rowCnt;
	colCnt = m_colCnt;
}

Csv2DbAdapter::Csv2DbAdapter(PCWSTR filePath, _Workbook* pTarWorkbook, BMP_PTR pBMP, IDBProtectionJudgement* pProtectionJudgement, const VarObj& param)
	: CsvAdapter(filePath)
	, m_pTarWorkbook(pTarWorkbook)
	, m_pBMP(pBMP)
	, m_pProtectionJudgement(pProtectionJudgement)
	, m_param(param)
{
	VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
}

HRESULT Csv2DbAdapter::Init()
{
	if (!m_pTarWorkbook || !m_pBMP || !m_pProtectionJudgement)
		return E_INVALIDARG;
	CsvParam csvParam;
	csvParam.bIgnoreAllLeadingBlankLines = TRUE;
	csvParam.bParseFormulaAsText = TRUE;
	HRESULT hr = CsvAdapter::Init(csvParam);
	if (FAILED(hr))
		return hr;
	VarObj defaultName = m_param.get_s("defaultName");
	m_defaultFieldName.resize(ET_DbSheet_FieldType_Count);
	for (int i = 0; i < ET_DbSheet_FieldType_Count; ++i)
	{
		PCWSTR typeStr = nullptr;
		VS(_appcore_GainEncodeDecoder()->EncodeFieldType(static_cast<ET_DbSheet_FieldType>(i), &typeStr));
		m_defaultFieldName[i] = typeStr;
		QString qStr(QString::fromUtf16(typeStr));
		const char* key = qStr.toUtf8().data(); // 生命周期
		if (defaultName.has(key))
			m_defaultFieldName[i] = defaultName.field_str(key);
	}
	return S_OK;
}

HRESULT Csv2DbAdapter::Exec(UINT& activeStId)
{
	// 同步数据场景, 不需要产生协作记录 (不存在合理的"操作者")
	DbSheet::DisableDbTrackHistoryScope dbHistoryDisabler;

	ks_stdptr<etoldapi::Worksheets> spSheets;
	HRESULT hr = m_pTarWorkbook->get_Worksheets(&spSheets);
	if (FAILED(hr))
		return hr;
	ROW rowCnt = 0;
	COL colCnt = 0;
	CsvAdapter::GetRowColInfo(rowCnt, colCnt);
	// 应产品要求， db 导入 csv 默认第一行为表头，其余作为数据导入
	// 当 csv 只有一行数据时，默认创建一个空行
	if (rowCnt > 1)
		--rowCnt;
	if (rowCnt > m_pBMP->cntRows)
	{
		m_lostFlag = true;
		rowCnt = m_pBMP->cntRows;
	}
	if (colCnt > m_pBMP->cntCols)
	{
		m_lostFlag = true;
		colCnt = m_pBMP->cntCols;
	}
	ITextImport* pTextImport = GetTextImport();
	m_fldNames.reserve(colCnt);
	// 提前备份列名信息，避免因数据部分加载导致列名信息丢失
	for (int col = 0; col < colCnt; ++col)
	{
		ks_bstr fldName;
		VS(pTextImport->GetValue(0, col, &fldName));
		m_fldNames.emplace_back(fldName.c_str());
	}

	hr = pTextImport->SetImportStartRow(2);
	if (FAILED(hr))
		return hr;
	// 添加导入数据的 dbsheet
	KComVariant vBefore;
	int idx = util::getLastSheetIdx(m_pTarWorkbook->GetBook()) + 1;
	KComVariant vAfter(idx);
	KComVariant vCount(1);
	KComVariant vType(xlWorksheet);
	ks_stdptr<IKCoreObject> spObj;
	hr = spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid_DB);
	if (FAILED(hr))
		return hr;
	m_spNewWorksheet = spObj;
	ISheet* pSheet = m_spNewWorksheet->GetSheet();
	activeStId = pSheet->GetStId();
	VS(m_spStringTools->SetEnv(pSheet));
	if (m_param.has("name"))
	{
		PCWSTR name = m_param.field_str("name");
		ks_bstr newName;
		hr = GetValidSheetName(spSheets, m_spNewWorksheet, name, &newName);
		if (FAILED(hr))
			return hr;
		hr = m_spNewWorksheet->put_Name(newName);
		if (FAILED(hr))
			return hr;
	}
	Database::SheetInitConfig config = Database::Utils::ParseDbSheetInitConfig(m_param);
	hr = DbSheet::ConfigureNewSheet(m_spNewWorksheet, m_pProtectionJudgement, rowCnt, colCnt, config);
	if (FAILED(hr))
		return hr;
	// 添加视图
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	hr = DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews);
	if (FAILED(hr))
		return hr;

	DbSheet::DisableDbSheetProtectScope disPtScope(m_pProtectionJudgement, pSheet->GetStId());
	VarObj defaultName = m_param.get_s("defaultName");
	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = spDbSheetViews->CreateView(et_DBSheetView_Grid, defaultName.field_str("grid_name"), false, Et_DBSheetViewUse_ForDb, &spDbSheetView);
	if (FAILED(hr))
		return hr;

	RANGE dest(m_pBMP);
	dest.SetSheetFromTo(idx);
	dest.SetRowFromTo(0);
	dest.SetColFromTo(0);
	hr = CsvAdapter::Exec(pSheet, &dest);
	if (FAILED(hr))
		return hr;
	hr = adjustFieldType(idx, spDbSheetView);
	if (FAILED(hr))
		return hr;
	hr = adjustFieldSettings(spDbSheetView);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

void Csv2DbAdapter::rollback()
{
	if (m_spNewWorksheet)
		m_spNewWorksheet->DeleteDirectly();
}

HRESULT Csv2DbAdapter::adjustFieldType(IDX idx, IDBSheetView* pDbSheetView) const
{
	HRESULT hr = S_OK;
	IBookOp* pBookOp = m_pTarWorkbook->GetBook()->LeakOperator();
	IDbFieldsManager* pFieldsMgr = pDbSheetView->GetFieldsManager();
	const IDBIds* pRecords = pDbSheetView->GetAllRecords();
	const IDBIds* pFields = pDbSheetView->GetAllFields();
	int rowCnt = static_cast<int>(pRecords->Count());
	int colCnt = static_cast<int>(pFields->Count());
	DBFieldTypeRecHelper fieldRecHelper;
	for (int col = 0; col < colCnt; ++col)
	{
		fieldRecHelper.begin();
		for (int row = 0; row < rowCnt; ++row)
		{
			const_token_ptr pToken = nullptr;
			pBookOp->GetCellValue(idx, row, col, &pToken);
			if (!pToken)
				continue;
			const XF* pXF = nullptr;
			pBookOp->GetCellFormat(idx, row, col, &pXF, nullptr);
			if (!pXF || !pXF->pNumFmt)
				continue;
			PCWSTR format = pXF->pNumFmt->fmt;
			ET_DbSheet_FieldType type = ET_DbSheet_FieldType_Invalid;
			hr = fieldRecHelper.addToken(pToken, format, type);
			if (FAILED(hr))
				return hr;
			if (hr == S_OK)
				break;
		}
		fieldRecHelper.end();
		ET_DbSheet_FieldType fldType = ET_DbSheet_FieldType_Invalid;
		PCWSTR numFmt = nullptr;
		fieldRecHelper.GetTypeInfo(fldType, numFmt);
		ks_stdptr<IDbField> spField;
		VS(pFieldsMgr->GetField(pFields->IdAt(col), &spField));
		VS(spField->SetTypeForIO(fldType));
		if (numFmt)
		{
			hr = spField->SetNumberFormat(numFmt);
			if (FAILED(hr))
				return hr;
		}
		if (fldType == Et_DbSheetField_SingleSelect)
		{
			ks_stdptr<IDbField_Select> spFieldSelect = spField;
			for (const auto& it : fieldRecHelper.GetSelectItem())
			{
				static constexpr int maxColorCnt = 10;
				static constexpr std::array<ARGB, maxColorCnt> colors = {
					0xFFE0EBF9, 0xFFE1F2FC, 0xFFDCF7F7, 0xFFE2F5E3, 0xFFFDF3CF,
					0xFFFFF0D7, 0xFFFDE3DC, 0xFFF9E3EF, 0xFFEDE7FC, 0xFFF1F1F1
				};
				hr = spFieldSelect->AppendItem(it.c_str(), colors.at(spFieldSelect->Count() % maxColorCnt));
				if (hr == E_DBSHEET_SELECT_ITEM_CONFLICT)
					continue;
				if (FAILED(hr))
					return hr;
			}
		}
	}
	return S_OK;
}

HRESULT Csv2DbAdapter::adjustFieldSettings(IDBSheetView* pDbSheetView) const
{
	HRESULT hr = S_OK;
	IDBSheetOp* pDataOp = pDbSheetView->GetSheetOp();
	IDbFieldsManager* pFieldsMgr = pDataOp->GetFieldsManager();
	const IDBIds* pRecords = pDataOp->GetAllRecords();
	const IDBIds* pFields = pDataOp->GetAllFields();
	int rowCnt = static_cast<int>(pRecords->Count());
	int colCnt = static_cast<int>(pFields->Count());
	std::vector<EtDbId> fldIds(colCnt);
	std::vector<PCWSTR> nameVec(colCnt);
	for (int col = 0; col < colCnt; ++col)
	{
		ks_stdptr<IDbField> spField;
		EtDbId fldId = pFields->IdAt(col);
		VS(pFieldsMgr->GetField(fldId, &spField));
		fldIds[col] = fldId;
		ET_DbSheet_FieldType fldType = spField->GetType();
		PCWSTR fldName = m_fldNames[col].c_str();
		if (fldName && *fldName)
			nameVec[col] = fldName;
		else
			nameVec[col] = m_defaultFieldName[fldType];
		switch (fldType)
		{
			case Et_DbSheetField_Email:
				for (int row = 0; row < rowCnt; ++row)
				{
					ks_bstr strVal;
					EtDbId recId = pRecords->IdAt(row);
					VS(m_spStringTools->GetCellText(nullptr, row, col, nullptr, &strVal, -1, nullptr));
					pDataOp->SetValue(recId, fldId, strVal.c_str());
					if (strVal.empty())
						continue;
					pDataOp->SetHyperlinkAddress(recId, fldId, strVal.c_str());
				}
				break;
			case Et_DbSheetField_Url:
				for (int row = 0; row < rowCnt; ++row)
				{
					ks_bstr strVal;
					EtDbId recId = pRecords->IdAt(row);
					VS(m_spStringTools->GetCellText(nullptr, row, col, nullptr, &strVal, -1, nullptr));
					if (strVal.empty())
						continue;
					alg::managed_token_assist mta;
					IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
					if (FAILED(pCtx->Text2DbHyperlinkToken(strVal.c_str(), strVal.c_str(), &mta)))
						continue;
					pDataOp->SetTokenValue(recId, fldId, mta);
				}
				break;
			case Et_DbSheetField_MultiLineText:
			case Et_DbSheetField_Phone:
			case Et_DbSheetField_SingleSelect:
				for (int row = 0; row < rowCnt; ++row)
				{
					ks_bstr strVal;
					VS(m_spStringTools->GetCellText(nullptr, row, col, nullptr, &strVal, -1, nullptr));
					pDataOp->SetValue(pRecords->IdAt(row), fldId, strVal.c_str());
				}
				break;
			default:
				break;
		}
	}
	pFieldsMgr->BatchSetFieldsName(fldIds.data(), nameVec.data(), colCnt);
	if (m_param.has("fieldWidth"))
	{
		INT width = m_param.field_int32("fieldWidth");
		ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = pDbSheetView;
		for (int i = 0; i < colCnt; ++i)
		{
			hr = spDbSheetViewGrid->SetFieldWidth(pFields->IdAt(i), width, TRUE);
			if (FAILED(hr))
				return hr;
		}
	}
	return S_OK;
}

////////////////////////////////////////////////////////////////////
SyncSqlCsv2DbAdapter::SyncSqlCsv2DbAdapter(KEtWorkbook* wb, PCWSTR filePath, _Workbook* pTarWorkbook, BMP_PTR pBMP, IDBProtectionJudgement* pProtectionJudgement, const VarObj& param)
	: CsvAdapter(filePath)
	, m_wwb(wb)
	, m_pTarWorkbook(pTarWorkbook)
	, m_pBMP(pBMP)
	, m_pProtectionJudgement(pProtectionJudgement)
	, m_param(param)
{
	VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
}

SyncSqlCsv2DbAdapter::~SyncSqlCsv2DbAdapter()
{
	if (m_spCsvWorksheet)
		m_spCsvWorksheet->DeleteDirectly();
}

HRESULT SyncSqlCsv2DbAdapter::Init()
{
	if (!m_pTarWorkbook || !m_pBMP || !m_pProtectionJudgement)
		return E_INVALIDARG;

	std::chrono::steady_clock::time_point  tp = std::chrono::steady_clock::now();
	CsvParam csvParam;
	csvParam.bIgnoreAllLeadingBlankLines = TRUE;
	csvParam.bParseFormulaAsText = TRUE;
	csvParam.bParseNumAsText = TRUE;
	HRESULT hr = CsvAdapter::Init(csvParam);
	if (FAILED(hr))
		return hr;
	
	unsigned int ms = (std::chrono::steady_clock::now() - tp) / std::chrono::milliseconds(1);
	util::CollectInfo(m_wwb, __X("behaviour.sql2db_read_csv_file"), ms);

	hr = ParseParams();
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT SyncSqlCsv2DbAdapter::Exec()
{
	ks_stdptr<ISheet> spSheet;
	HRESULT hr = DbSheet::GetDbSheet(m_pTarWorkbook->GetBook(), m_targetSheetId, &spSheet);
	if(FAILED(hr))
		return hr;
	
	ISheet* pSheet = spSheet.get();
	VS(DbSheet::GetDBSheetOp(pSheet, &m_spDbSheetOp));
	DbSheet::DbtBatchSubmitOptimizer batchSubmitter(m_spDbSheetOp.get());
	// 同步数据场景, 不需要产生协作记录 (不存在合理的"操作者")
	DbSheet::DisableDbTrackHistoryScope dbHistoryDisabler;

	if(m_spDbSheetOp->GetSheetSyncType() != DbSheet_St_Sql)
	{
		//创建sheet时设置SyncType
		WOLOG_ERROR << "Syc sheet type error";
		return E_FAIL;
	}

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	hr = DbSheet::GetDBSheetViews(spSheet, &spDbSheetViews);
	spDbSheetViews->GetItemAt(0, Et_DBSheetViewUse_ForDb, &m_spDbSheetView);
	if(!m_spDbSheetView)
		return E_FAIL;

	ClearOldNumberFormat();
	CheckPrimaryKeyIsValid();

	//CSV数据导入
	ROW rowCnt = 0;
	COL colCnt = 0;
	GetCsvSheetRowColInfo(rowCnt, colCnt);
	if(rowCnt <= 0 || colCnt <= 0)
		return E_FAIL;

	ITextImport* pTextImport = GetTextImport();

	//将csv表头数据导入到sheet内容中，读取第一行表头数据作为字段名，Csv2DbAdapter中获取字段名方法有问题
	hr = pTextImport->SetImportStartRow(1);
	if (FAILED(hr))
		return hr;

	hr = ImportSheetFromCsv(rowCnt + 1, colCnt);
	if(FAILED(hr))
		return hr;

	hr = BuildCsvSheetIdMap();
	if(FAILED(hr))
		return hr;

	DbSheet::DisableDbSheetProtectScope disPtScope(m_pProtectionJudgement, pSheet->GetStId());
	hr = SyncFieldData(pSheet);
	if(FAILED(hr))
		return hr;

	hr = SyncRecordData(pSheet);
	if(FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT SyncSqlCsv2DbAdapter::ImportSheetFromCsv(ROW rowCnt, COL colCnt)
{
	ks_stdptr<etoldapi::Worksheets> spSheets;
	m_pTarWorkbook->get_Worksheets(&spSheets);
	// 添加一张新表，存放csv导入数据
	KComVariant vBefore;
	int idx = util::getLastSheetIdx(m_pTarWorkbook->GetBook()) + 1;
	KComVariant vAfter(idx);
	KComVariant vCount(1);
	KComVariant vType(xlWorksheet);
	ks_stdptr<IKCoreObject> spObj;
	HRESULT hr = spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid_DB);
	if (FAILED(hr))
		return hr;

	m_spCsvWorksheet = spObj;
	ISheet* pSheet = m_spCsvWorksheet->GetSheet();
	VS(m_spStringTools->SetEnv(pSheet));
	DbSheet::DisableDbSheetProtectScope disPtScope(m_pProtectionJudgement, pSheet->GetStId());

	Database::SheetInitConfig config = Database::Utils::ParseDbSheetInitConfig(m_param);
	hr = DbSheet::ConfigureNewSheet(m_spCsvWorksheet, m_pProtectionJudgement, rowCnt, colCnt, config);
	if (FAILED(hr))
		return hr;

	RANGE dest(m_pBMP);
	dest.SetSheetFromTo(idx);
	dest.SetRowFromTo(0);
	dest.SetColFromTo(0);
	hr = CsvAdapter::Exec(pSheet, &dest);
	if (FAILED(hr))
		return hr;

	VS(DbSheet::GetDBSheetOp(pSheet, &m_spCsvDbSheetOp));
	DbSheet::DbtBatchSubmitOptimizer batchSubmitter(m_spCsvDbSheetOp.get());
	IDbFieldsManager* pFieldManager = m_spCsvDbSheetOp->GetFieldsManager();
	const IDBIds* pFieldIds = m_spCsvDbSheetOp->GetAllFields();

	//第一行数据是csv数据表头，作为字段名
	m_fldNames.reserve(colCnt);
	for (int col = 0; col < colCnt; ++col)
	{
		ks_bstr fldName;
		VS(m_spStringTools->GetCellText(nullptr, 0, col, nullptr, &fldName, -1, nullptr));
		m_fldNames.emplace_back(fldName.c_str());
	}

	if(pFieldIds->Count() != m_fldNames.size())
		return E_FAIL;

	for (EtDbIdx i = 0; i < pFieldIds->Count(); i++)
	{
		EtDbId fldId = pFieldIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		HRESULT hr;
		DbSheet::DbFieldUpdateScope updateScope(spField, m_pTarWorkbook->GetBook(), hr);
		spField->SetName(m_fldNames[i].c_str(), false, false);
		spField->SetType(GetSyncDbType(m_fldNames[i]), nullptr);
	}

	return S_OK;
}

HRESULT SyncSqlCsv2DbAdapter::BuildCsvSheetIdMap()
{
	ISheet* pSheet = GetCsvSheet();
	if(!pSheet)
		return E_FAIL;
	
	IDbFieldsManager *pFieldManager = m_spCsvDbSheetOp->GetFieldsManager();
	const IDBIds* pFieldIds = m_spCsvDbSheetOp->GetAllFields();

	for (EtDbIdx i = 0; i < pFieldIds->Count(); i++)
	{
		EtDbId fldId = pFieldIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		m_csvSheetFieldMap.insert(std::make_pair(spField->GetName(), fldId));
	}

	//主键判断
	bool isPrimaryKeyValid = IsPrimaryKeyValid();
	if(isPrimaryKeyValid)
	{
		IDbRecordsManager *pRecordsManager = m_spCsvDbSheetOp->GetRecordsManager();
		const IDBIds *pRecordIds = m_spCsvDbSheetOp->GetAllRecords();
		UINT rowCnt = pRecordIds->Count();
		for (int i = 1; i < rowCnt; ++i)
		{
			EtDbId rowId = pRecordIds->IdAt(i);
			PrimaryKey pk;
			if(!MakePrimaryKey(m_spCsvDbSheetOp, rowId, m_csvSheetFieldMap, pk))
				return E_FAIL;

			RecordInfo recordInfo = {rowId, false};
			m_csvSheetRecordMap.insert(std::make_pair(pk, recordInfo));
		}	
	}

	return S_OK;
}

void SyncSqlCsv2DbAdapter::CheckPrimaryKeyIsValid()
{
	m_primaryKeyParam.m_isValid = false;
	if(m_primaryKeyParam.m_primaryFieldNameVec.size() == 0)
	{
		return;
	}

	std::map<ks_wstring, ET_DbSheet_FieldType> syncFldInfoMap;
	IDbFieldsManager *pFieldManager = m_spDbSheetOp->GetFieldsManager();
	const IDBIds* pFieldIds = m_spDbSheetOp->GetAllFields();
	for (EtDbIdx i = 0; i < pFieldIds->Count(); i++)
	{
		EtDbId fldId = pFieldIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		if (spField && spField->IsSyncField())
		{
			PCWSTR syncSourceFieldId = spField->GetSyncSourceFieldId();
			if (!syncSourceFieldId)
				syncSourceFieldId = spField->GetName();
			syncFldInfoMap.insert(std::make_pair(syncSourceFieldId, spField->GetType()));
		}
	}

	for(auto& primaryFieldName : m_primaryKeyParam.m_primaryFieldNameVec)
	{
		if(syncFldInfoMap.find(primaryFieldName) == syncFldInfoMap.end())
			return;

		//校验类型是否一致
		if(!IsSameType(primaryFieldName, syncFldInfoMap[primaryFieldName]))
			return;
	}

	m_primaryKeyParam.m_isValid = true;
}

bool SyncSqlCsv2DbAdapter::IsSameType(const ks_wstring& fieldName, ET_DbSheet_FieldType fieldType)
{
	if(m_sqlFieldTypeMap.find(fieldName) == m_sqlFieldTypeMap.end())
		return false;
	
	ET_DbSheet_FieldType sqlOriginFieldType;
	if(!SqlDataType2DbType(m_sqlFieldTypeMap[fieldName], sqlOriginFieldType))
	{
		return false;
	}

	return sqlOriginFieldType == fieldType;
}

bool SyncSqlCsv2DbAdapter::IsPrimaryKeyValid()
{
	return m_primaryKeyParam.m_isValid;
}

bool SyncSqlCsv2DbAdapter::MakePrimaryKey(IDBSheetOp* pOp, EtDbId rowId, const std::map<ks_wstring, EtDbId>& fieldMap, PrimaryKey& pk)
{
	for(auto& fieldName : m_primaryKeyParam.m_primaryFieldNameVec)
	{
		if(fieldMap.find(fieldName) == fieldMap.end())
			return false;

		EtDbId fldId = 	fieldMap.at(fieldName);
		ks_bstr str;
		pOp->GetValueString(rowId, fldId, &str);
		pk.m_fieldValueVec.push_back(str.c_str());
	}
	return true;
}

void SyncSqlCsv2DbAdapter::UpdateTarSheetRecordData(EtDbId tarSheetRowId, EtDbId csvSheetRowId, bool exceptPrimaryField)
{
	IDbFieldsManager *pFieldManager = m_spDbSheetOp->GetFieldsManager();
	const IDBIds* pFieldIds = m_spDbSheetOp->GetAllFields();

	for (EtDbIdx i = 0; i < pFieldIds->Count(); i++)
	{
		EtDbId fldId = pFieldIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		ks_wstring fldName = spField->GetSyncSourceFieldId();
		if(m_csvSheetFieldMap.find(fldName) == m_csvSheetFieldMap.end())
			continue;
		if(exceptPrimaryField)	//带有主键列的更新，主键列已经判断过相等了，就不用再更新
		{
			const auto& primaryFieldNameVec = m_primaryKeyParam.m_primaryFieldNameVec;
			if(std::count(primaryFieldNameVec.begin(), primaryFieldNameVec.end(), fldName) > 0)
				continue;	
		}

		ks_wstring csvSheetVal = GetCsvSheetValue(csvSheetRowId, fldName.c_str());
		m_spDbSheetOp->SetValue(tarSheetRowId, fldId, csvSheetVal.c_str(), false);
	}
}

bool SyncSqlCsv2DbAdapter::SqlDataType2DbType(const ks_wstring& sqlType, ET_DbSheet_FieldType& fieldType)
{
	static const std::map<ks_wstring, ET_DbSheet_FieldType> sql2DbTypeMap = {
		{__X("text"), ET_DbSheet_FieldType::Et_DbSheetField_MultiLineText},
		{__X("number"), ET_DbSheet_FieldType::Et_DbSheetField_Number},
		{__X("date"), ET_DbSheet_FieldType::Et_DbSheetField_Date},
	};
	
	if(sql2DbTypeMap.find(sqlType) == sql2DbTypeMap.end())
		return false;
	
	fieldType = sql2DbTypeMap.at(sqlType);
	return true;
}

void SyncSqlCsv2DbAdapter::ModifyDbTypeFormat(binary_wo::VarObj& obj, ET_DbSheet_FieldType type)
{
	switch (type)
	{
	case ET_DbSheet_FieldType::Et_DbSheetField_Date:
	{
		obj.add_field_str("numberFormat", __X("yyyy/mm/dd hh:mm;@"));
		break;
	}
	default:
		break;
	}
}

ET_DbSheet_FieldType SyncSqlCsv2DbAdapter::GetSyncDbType(const ks_wstring& fieldName)
{
	if(m_sqlFieldTypeMap.find(fieldName) == m_sqlFieldTypeMap.end())
		return ET_DbSheet_FieldType::Et_DbSheetField_MultiLineText;

	ET_DbSheet_FieldType dbFieldType;
	if(!SqlDataType2DbType(m_sqlFieldTypeMap[fieldName], dbFieldType))
		return ET_DbSheet_FieldType::Et_DbSheetField_MultiLineText;

	return dbFieldType;
}

HRESULT SyncSqlCsv2DbAdapter::ClearEmptyRow(ISheet* pSheet)
{
	IDbRecordsManager *pRecordsManager = m_spDbSheetOp->GetRecordsManager();
	const IDBIds *pRecordIds = m_spDbSheetOp->GetAllRecords();
	UINT rowCnt = pRecordIds->Count();

	ks_stdptr<IDBSheetRange> spRg;
	m_spDbSheetView->CreateDBSheetRange(&spRg);
	spRg->SetFieldIds(m_spDbSheetView->GetAllFields());
	for (int i = 0; i < rowCnt; ++i)
	{
		EtDbId rowId = pRecordIds->IdAt(i);
		if(pRecordsManager->IsSyncRecord(rowId))
			continue;

		IDX rowIdx = pRecordIds->Id2Idx(rowId);
		IDX sheetIdx;
		pSheet->GetIndex(&sheetIdx);
		RANGE rowRange(m_pBMP);
		rowRange.SetSheetFromTo(sheetIdx, sheetIdx);
		rowRange.SetRowFromTo(rowIdx, rowIdx);
		rowRange.SetColFromTo(0, m_pBMP->cntCols - 1);
		IKWorksheet* pWorkSheet = m_pTarWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
		
		if(IsRangeEmpty(pWorkSheet, rowRange))
			spRg->AddRecordId(rowId);
	}

	if(spRg->GetRecordCnt() > 0)
	{
		HRESULT hr = m_spDbSheetView->RemoveRecords(spRg);
		if(FAILED(hr))
			return hr;
	}

	return S_OK;
}

HRESULT SyncSqlCsv2DbAdapter::ParseParams()
{
	if(!m_param.has("targetSheetId"))
		return E_FAIL;
	m_targetSheetId = m_param.field_int32("targetSheetId");

	if(!m_param.has("fieldTypeMap"))
		return E_FAIL;
	
	VarObj fieldTypeMapObj = m_param.get_s("fieldTypeMap");
	std::vector<WebName> fieldNames = fieldTypeMapObj.keys();
	for(auto& fieldName : fieldNames)
	{
		m_sqlFieldTypeMap.insert(std::make_pair(krt::utf16(QString(fieldName)), fieldTypeMapObj.field_str(fieldName)));
	}

	//可选参数
	if(m_param.has("primaryField"))
	{
		VarObj primaryFieldObj = m_param.get_s("primaryField");
		for (int i = 0, cnt = primaryFieldObj.arrayLength_s(); i < cnt; i++)
		{
			ks_wstring primaryField = primaryFieldObj.at(i).value_str();
			m_primaryKeyParam.m_primaryFieldNameVec.push_back(primaryField);
		}
	}

	if(m_param.has("first"))
	{
		m_firstSync = m_param.field_bool("first");
	}

	return S_OK;
}

HRESULT SyncSqlCsv2DbAdapter::SyncFieldData(ISheet* pSheet)
{
	HRESULT hr;
	IDbFieldsManager *pFieldManager = m_spDbSheetOp->GetFieldsManager();
	const IDBIds* pFieldIds = m_spDbSheetOp->GetAllFields();

	std::vector<EtDbId> toDeleteFieldVec, userAddFieldVec;
	std::vector<IDbField*> toUpdateFieldTypeVec;
	for (EtDbIdx i = 0; i < pFieldIds->Count(); i++)
	{
		EtDbId fldId = pFieldIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		if (spField && spField->IsSyncField())
		{
			PCWSTR syncSourceFieldId = spField->GetSyncSourceFieldId();
			bool hasSaveSyncSourceFieldId = (syncSourceFieldId != nullptr);
			if (!syncSourceFieldId)	//没有保存数据库同步字段名，默认取同步字段名
				syncSourceFieldId = spField->GetName();
			bool existField = std::find(m_fldNames.begin(), m_fldNames.end(), ks_wstring(syncSourceFieldId)) != m_fldNames.end();
			if(existField)
			{
				m_tarSheetSyncFieldNameMap.insert(std::make_pair(ks_wstring(syncSourceFieldId), fldId));
				if(!IsSameType(syncSourceFieldId, spField->GetType()))
				{
					toUpdateFieldTypeVec.push_back(spField);
				}
				if (!hasSaveSyncSourceFieldId)
					spField->SetSyncSourceFieldId(syncSourceFieldId);
			}
			else 
			{
				//字段不存在且不是用户自己创建的(可能用户重命名的)，删除字段，重新同步
				toDeleteFieldVec.push_back(fldId);
			}
		}
		else 
		{
			//用户创建的新字段，需要移动到字段最后
			userAddFieldVec.push_back(fldId);
		}
	}

	//更新字段类型
	for(IDbField* pField : toUpdateFieldTypeVec)
	{
		DbSheet::DbFieldUpdateScope updateScope(pField, m_pTarWorkbook->GetBook(), hr);
		if (FAILED(hr))
			return hr;

		ET_DbSheet_FieldType newType = GetSyncDbType(pField->GetSyncSourceFieldId());
		binary_wo::VarObjRoot root(new binary_wo::BinVarRoot());
		VarObj args = root.cast();
		ModifyDbTypeFormat(args, newType);
		hr = DbSheet::ConfigureDbField(pField, args, newType, false, m_spDbSheetView, false);
		if (FAILED(hr))
			return hr;
	}

	//添加新字段
	for(ks_wstring& fldName : m_fldNames)
	{
		if(m_tarSheetSyncFieldNameMap.find(fldName) != m_tarSheetSyncFieldNameMap.end())
			continue;

		//防止数据过大
		if (pFieldIds->Count() >= m_pBMP->cntCols)
		{
			m_lostFlag = true;
			break;
		}

		ET_DbSheet_FieldType fldType = GetSyncDbType(fldName);
		binary_wo::VarObjRoot root(new binary_wo::BinVarRoot());
		VarObj args = root.cast();
		args.add_field_str("fieldName", fldName.c_str());
		ModifyDbTypeFormat(args, fldType);
		ks_stdptr<IDBSheetRange> spDbRange;
		hr = DbSheet::AddField(m_spDbSheetView, fldType, args, &spDbRange);
		if(FAILED(hr))
			return hr;	
		
		EtDbId fldId = spDbRange->GetFieldId(0);
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		spField->SetSyncField(true);
		spField->SetSyncSourceFieldId(fldName.c_str());
		if(FAILED(hr))
			return hr;

		m_tarSheetSyncFieldNameMap.insert(std::make_pair(ks_wstring(spField->GetSyncSourceFieldId()), fldId));
	}

	//将用户创建字段移动到最后
	if(userAddFieldVec.size() > 0 || toDeleteFieldVec.size() > 0)
	{
		ks_stdptr<IDBSheetRange> spRg;
		m_spDbSheetView->CreateDBSheetRange(&spRg);
		spRg->SetRecordIds(m_spDbSheetView->GetAllRecords());
		for(EtDbId id : userAddFieldVec)
		{
			spRg->AddFieldId(id);
		}

		//将需要删除的字段也提前移动到后面，防止删除的时候作为主键列删除失败
		for(EtDbId id : toDeleteFieldVec)
		{
			spRg->AddFieldId(id);
		}

		EtDbId lastSyncFieldId = INVALID_ID;
		const IDBIds* pOrderFieldsIdIdxMap = m_spDbSheetView->GetOrderFields();
		for(int i = pOrderFieldsIdIdxMap->Count() - 1; i >= 0; --i)
		{
			EtDbId fldId = pOrderFieldsIdIdxMap->IdAt(i);
			ks_stdptr<IDbField> spField;
			pFieldManager->GetField(fldId, &spField);
			if(m_tarSheetSyncFieldNameMap.count(spField->GetSyncSourceFieldId()) > 0)
			{
				lastSyncFieldId = fldId;
				break;
			}
		}
		ASSERT(lastSyncFieldId != INVALID_ID);

		DbIdPostion pos;
		pos.id = lastSyncFieldId;
		pos.isBack = true;
		hr = m_spDbSheetView->MoveFields(spRg, pos);
		if(FAILED(hr))
			return hr;
	}

	//第一次同步删除创建表默认字段
	if(m_firstSync && userAddFieldVec.size() > 0)
	{
		toDeleteFieldVec.insert(toDeleteFieldVec.end(), userAddFieldVec.begin(), userAddFieldVec.end());
		userAddFieldVec.clear();
	}

	//删除多余字段
	if(toDeleteFieldVec.size() > 0)
	{
		ks_stdptr<IDBSheetRange> spRg;
		m_spDbSheetView->CreateDBSheetRange(&spRg);
		spRg->SetRecordIds(m_spDbSheetView->GetAllRecords());
		for(EtDbId id : toDeleteFieldVec)
		{
			spRg->AddFieldId(id);
		}
		hr = m_spDbSheetView->RemoveFields(spRg, nullptr);
		if(FAILED(hr))
			return hr;
	}

	return S_OK;
}

HRESULT SyncSqlCsv2DbAdapter::ClearOldNumberFormat()
{
	// 以前的数字字段数字格式为""，数字原样显示，但是数字太长后显示科学计数法，
	// 后面修改成使用默认"0.00_"格式，旧样张需要清理下，否则主键按照字符串拼接比较的时候会有问题
	IDbFieldsManager *pFieldManager = m_spDbSheetOp->GetFieldsManager();
	const IDBIds* pFieldIds = m_spDbSheetOp->GetAllFields();

	for (EtDbIdx i = 0; i < pFieldIds->Count(); i++)
	{
		EtDbId fldId = pFieldIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		if (!spField)
			continue;
		
		if (!spField->IsSyncField())
			continue;

		if (spField->GetType() != Et_DbSheetField_Number)
			continue;
		
		PCWSTR numberFormat = __X("0.00_ ");
		if (xstrcmp(numberFormat, spField->GetNumberFormat()) != 0)
		{
			spField->SetNumberFormat(numberFormat);
		}
	}

	return S_OK;
}

ks_wstring SyncSqlCsv2DbAdapter::GetCsvSheetValue(EtDbId rowId, PCWSTR fieldName)
{
	if(m_csvSheetFieldMap.find(fieldName) == m_csvSheetFieldMap.end())
		return ks_wstring();
	
	EtDbId fldId = m_csvSheetFieldMap.at(fieldName);

	const IDBIds *pRecordIds = m_spCsvDbSheetOp->GetAllRecords();
	const IDBIds* pFieldIds = m_spCsvDbSheetOp->GetAllFields();
	EtDbIdx row = pRecordIds->Id2Idx(rowId);
	EtDbIdx col = pFieldIds->Id2Idx(fldId);

	if(row == INV_EtDbIdx || col == INV_EtDbIdx)
		return ks_wstring();

	ks_bstr str;
	VS(m_spStringTools->GetCellText(nullptr, row, col, nullptr, &str, -1, nullptr));
	return str.c_str();
}

ISheet* SyncSqlCsv2DbAdapter::GetCsvSheet()
{
	if(!m_spCsvWorksheet)
		return nullptr;
	return m_spCsvWorksheet->GetSheet();
}

void SyncSqlCsv2DbAdapter::GetCsvSheetRowColInfo(ROW& rowCnt, COL& colCnt)
{
	CsvAdapter::GetRowColInfo(rowCnt, colCnt);
	// 应产品要求， db 导入 csv 默认第一行为表头，其余作为数据导入
	// 当 csv 只有一行数据时，默认创建一个空行
	if (rowCnt > 1)
		--rowCnt;
	if (rowCnt > m_pBMP->cntRows)
	{
		m_lostFlag = true;
		rowCnt = m_pBMP->cntRows;
	}
	if (colCnt > m_pBMP->cntCols)
	{
		m_lostFlag = true;
		colCnt = m_pBMP->cntCols;
	}
}

HRESULT SyncSqlCsv2DbAdapter::SyncRecordData(ISheet* pSheet)
{
	HRESULT hr;
	IDbRecordsManager *pRecordsManager = m_spDbSheetOp->GetRecordsManager();
	const IDBIds *pRecordIds = m_spDbSheetOp->GetAllRecords();
	bool isPrimaryKeyValid = IsPrimaryKeyValid();
	std::vector<EtDbId> userAddRecordVec, toDeleteRecordVec;
	std::set<EtDbId> toAddRecordSet; //为了保证顺序用Set
	std::map<ks_wstring, IDbField*> existFieldMap;
	UINT rowCnt = pRecordIds->Count();

	int csvSheetImportedRowIdx = 1; //第一行数据是字段名
	int csvSheetImportedRowMaxIdx = m_spCsvDbSheetOp->GetAllRecords()->Count() -1;
	for (int i = 0; i < rowCnt; ++i)
	{
		EtDbId rowId = pRecordIds->IdAt(i);
		if(!pRecordsManager->IsSyncRecord(rowId))
		{
			userAddRecordVec.push_back(rowId);
			continue;
		}

		if(isPrimaryKeyValid) //有主键列增量更新
		{
			//构建主键
			PrimaryKey pk;
			if(!MakePrimaryKey(m_spDbSheetOp, rowId, m_tarSheetSyncFieldNameMap, pk))
				return E_FAIL;

			if(m_csvSheetRecordMap.find(pk) != m_csvSheetRecordMap.end()) //更新行值
			{
				UpdateTarSheetRecordData(rowId, m_csvSheetRecordMap[pk].m_rowId, true);
				m_csvSheetRecordMap[pk].m_isRecordMatched = true;
			}
			else	// 删除行
			{
				toDeleteRecordVec.push_back(rowId);
			}
		}
		else	//无主键全量更新 
		{
			if(csvSheetImportedRowIdx > csvSheetImportedRowMaxIdx)	//csv表数据已经全部导入
			{
				toDeleteRecordVec.push_back(rowId);
			}
			else
			{
				EtDbId csvSheetRowId = m_spCsvDbSheetOp->GetAllRecords()->IdAt(csvSheetImportedRowIdx);
				UpdateTarSheetRecordData(rowId, csvSheetRowId);
				++csvSheetImportedRowIdx;
			}
		}
	}

	if(isPrimaryKeyValid) //判断主键行数据是否更新完成
	{
		for(auto iter = m_csvSheetRecordMap.begin(); iter != m_csvSheetRecordMap.end(); ++iter)
		{
			if(iter->second.m_isRecordMatched == false)
			{
				toAddRecordSet.insert(iter->second.m_rowId);
			}
		}
	}
	else	//导入表中行数>csv表中行数，删除多余行， 导入表中行数<csv表中行数，增加行
	{
		const IDBIds *pRecordIds = m_spCsvDbSheetOp->GetAllRecords();
		for(ROW row = csvSheetImportedRowIdx; row <= csvSheetImportedRowMaxIdx; ++row)
		{
			EtDbId rowId = pRecordIds->IdAt(row);
			toAddRecordSet.insert(rowId);
		}
	}

	//添加行
	if(toAddRecordSet.size() > 0)
	{
		//防止数据过大
		int insertRecordsCnt = toAddRecordSet.size();
		if(insertRecordsCnt > m_pBMP->cntRows - pRecordIds->Count())
		{
			m_lostFlag = true;
			insertRecordsCnt = m_pBMP->cntRows - pRecordIds->Count();
		}
		ks_stdptr<IDBSheetRange> spDbRange;
		hr = m_spDbSheetView->InsertRecords(insertRecordsCnt, &spDbRange);
		if(FAILED(hr))
			return hr;	
		
		auto toAddRecordIdIter = toAddRecordSet.begin();
		for(ROW row = 0; row < spDbRange->GetRecordCnt(); ++row)
		{
			EtDbId rowId = spDbRange->GetRecordId(row);
			pRecordsManager->SetSyncRecord(rowId, true);
			UpdateTarSheetRecordData(rowId, *toAddRecordIdIter);
			++toAddRecordIdIter;
		}
	}
	
	//第一次同步删除创建表默认字段
	if(m_firstSync && userAddRecordVec.size() > 0)
	{
		toDeleteRecordVec.insert(toDeleteRecordVec.end(), userAddRecordVec.begin(), userAddRecordVec.end());
		userAddRecordVec.clear();
	}

	//删除行
	if(toDeleteRecordVec.size() > 0)
	{
		ks_stdptr<IDBSheetRange> spRg;
		m_spDbSheetView->CreateDBSheetRange(&spRg);
		spRg->SetFieldIds(m_spDbSheetView->GetAllFields());
		for(auto rowId : toDeleteRecordVec)
			spRg->AddRecordId(rowId);
		hr = m_spDbSheetView->RemoveRecords(spRg);
		if(FAILED(hr))
			return hr;
	}

	//将用户创建行移动到最后
	if(userAddRecordVec.size() > 0)
	{
		int lastRecordIdx = pRecordIds->Count() - 1;
		const IDBIds* pOrderRowIdIdxMap = m_spDbSheetView->GetOrderRecords();
		EtDbId moveAfterRecordId = pOrderRowIdIdxMap->IdAt(lastRecordIdx);
		for(EtDbId id : userAddRecordVec)
		{
			EtDbId toMoveRecordId = id;
			if(toMoveRecordId != moveAfterRecordId)
			{
				ks_stdptr<IDBSheetRange> spRg;
				m_spDbSheetView->CreateDBSheetRange(&spRg);
				spRg->SetFieldIds(m_spDbSheetView->GetAllFields());
				spRg->AddRecordId(toMoveRecordId);

				DbIdPostion pos;
				pos.id = moveAfterRecordId;
				pos.isBack = true;
				hr = m_spDbSheetView->MoveRecords(spRg, pos);
				if(FAILED(hr))
					return hr;
			}
			moveAfterRecordId = toMoveRecordId;
		}
		QString userAddRecInfo = QString("behaviour_sql2db_userAddRecordCnt_sheetId_%1").arg(m_spDbSheetOp->GetSheetId());
		util::CollectInfo(m_wwb, krt::utf16(userAddRecInfo), userAddRecordVec.size());
	}
	return S_OK;
}


} // namespace wo
