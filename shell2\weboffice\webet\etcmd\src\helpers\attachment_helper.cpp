#include "etstdafx.h"
#include "webbase/webdeclare.h"
#include "webetlink.h"
#include "wo/wo_msgType_helper.h"
#include "et_revision_context_impl.h"
#include "attachment_helper.h"

extern Callback* gs_callback;
using binary_wo::BinWriter;
namespace wo
{

void CloudImgAttachmentCollector::begin(const ks_wstring& srcFileId)
{
    m_curSrcFileId = srcFileId;
    m_pCtx->clearAttachmentIds();
}

void CloudImgAttachmentCollector::end()
{
    if (m_curSrcFileId.empty()) return;
   
    AttachmentItem attachmentItem;
    m_pCtx->getAllAttachmentIds(attachmentItem.vecIds, attachmentItem.vecIdsVideo);
    m_IdContainer.insert(std::make_pair(m_curSrcFileId, attachmentItem));
    m_pCtx->clearAttachmentIds();
}
    

void CloudImgAttachmentCollector::setFileTagByPicAsAttachments(bool isAttachments)
{
	if (gs_callback && gs_callback->setFileTag)
	{
		binary_wo::BinWriter binWriter;
		binWriter.addKey("tag");
		binWriter.addString(__X("isPicAttachments"));
		binWriter.addKey("value");
		binWriter.addBool(isAttachments);
		binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};
		gs_callback->setFileTag(&slice);
	}
}

void CloudImgAttachmentCollector::filloutAttachmentIds(bool bSucceed)
{
    if (!bSucceed) return;

    using itemPair = std::pair<ks_wstring, AttachmentItem>;
    if (gs_callback && gs_callback->broadcast &&
        gs_callback->getAttachmentFromOtherFile)
    {	 
        for (const itemPair& item : m_IdContainer)
        {
            QString srcFileId = QString::fromUtf16(item.first.c_str());
            if (!item.second.empty())
            {
                BinWriter bw;
                bw.addKey("srcFileId");
                bw.addString(krt::utf16(srcFileId));
                bw.beginArray("attachmentIds");
                for (const ks_wstring& id : item.second.vecIds)
                {
                    bw.addString(id.c_str());
                }
                bw.endArray();

                bw.beginArray("videoAttachmentIds");
                for (const ks_wstring& id : item.second.vecIdsVideo)
                {
                    bw.addString(id.c_str());
                }
                bw.endArray();

                bw.addKey("isReady");
                bw.addBool(false);

                BinWriter::StreamHolder sh = bw.buildStream();
                WebSlice slice = { sh.get(), bw.writeLength() };
                gs_callback->getAttachmentFromOtherFile(&slice);
                gs_callback->broadcast(getMsgTypeName(msgType_Attachments), &slice, nullptr);

                // 这里还需要给图片打标
	            wo::IBookSetting *pSetting = m_pCtx->getBook()->GetWoStake()->getSetting();
                if (!pSetting)
                {
                   continue;
                }

                BOOK_INFO_COLLECT *info = m_pCtx->getBook()->GetBookInfoCollect();
                bool isOpenAttachments = pSetting->getIsInsertPicAsAttachments();
    
                // 如果已经打开了开关 则直接返回 不需要打标
                if (isOpenAttachments)
                {
                    continue;
                }

                setFileTagByPicAsAttachments(true);
            }
        }
    }
}

}// end namespace wo