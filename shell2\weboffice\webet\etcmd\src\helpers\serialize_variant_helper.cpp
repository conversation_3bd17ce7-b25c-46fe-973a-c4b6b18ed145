﻿#include "etstdafx.h"
#include "serialize_variant_helper.h"
#include <iomanip>
namespace wo
{
namespace SerializeVariantHelper
{
template <typename T>
inline
static WebInt AddStringFieldInt(ISerialAcceptor* acpt, WebName name, bool bForceString, T val)
{
    if (!bForceString)
        return WO_FAIL;
    return acpt->addString(name, krt::utf16(QString::number(val)));
}

inline
void TruncateTailZeros(std::string& str)
{
    if (str.find('.') == std::string::npos)
        return;
    
    size_t idxPoint = str.rfind('.');
    size_t idxZero = str.rfind('0');
    size_t idxTail = str.size();
    while (idxPoint <= idxZero && idxZero == idxTail - 1)
    {
        --idxTail;
        idxZero = str.rfind('0', idxZero - 1);
    }
    if (idxPoint == idxTail - 1)
        --idxTail;

    str = str.erase(idxTail);
}

template <typename T>
inline
static WebInt AddStringFieldFloat(ISerialAcceptor* acpt, WebName name, bool bForceString, T val)
{
    if (!bForceString)
        return WO_FAIL;
    std::ostringstream oss;
    oss << std::setprecision(std::numeric_limits<T>::digits10) << val;
    std::string str = oss.str();
    TruncateTailZeros(str);
    return acpt->addString(name, krt::utf16(QString::fromStdString(str)));
}
// 遍历数组，计算空单元格数量
static void TravelSafeArray(SAFEARRAY *pArray, UINT nDims, SIZE32* indexes, uint64& totalCells, uint64& emptyCells)
{
    if (nDims >= pArray->cDims || nDims >= 2)
    {
        ASSERT(FALSE);
        return;
    }
    for (ULONG i = 0; i < pArray->rgsabound[pArray->cDims - 1 - nDims].cElements; ++i)
    {
        indexes[nDims] = pArray->rgsabound[pArray->cDims - 1 - nDims].lLbound + i;
        if (nDims == pArray->cDims - 1)
        {
            KComVariant var = { 0 };
            SafeArrayGetElement(pArray, indexes, (void*)&var);
            VARTYPE vt = V_VT(&var);
            ++totalCells;
            if (vt == VT_EMPTY)
                ++emptyCells;
        }
        else
        {
            TravelSafeArray(pArray, nDims + 1, indexes, totalCells, emptyCells);
        }
    }
}

bool NeedSerializeDiscrete(const KComVariant& var)
{
    VARTYPE vt = V_VT(&var);
    if (vt != (VT_ARRAY | VT_VARIANT) && vt != VT_SAFEARRAY)
        return false;
    SAFEARRAY* pArray = V_ARRAY(&var);
    std::unique_ptr<SIZE32[]> indexes(new SIZE32[pArray->cDims]);
    uint64 totalCells = 0, emptyCells = 0;
    TravelSafeArray(pArray, 0, indexes.get(), totalCells, emptyCells);
    return (double)emptyCells / totalCells > 2.0 / 3;
}

WebInt Serialize(ISerialAcceptor* acpt, const KComVariant& var, WebName name, bool bForceString, bool bDiscrete)
{
    VARTYPE vt = V_VT(&var);
    switch (vt)
    {
        case VT_EMPTY:
            return acpt->addString(name, __X(""));
            break;
        case VT_I1:
            if (AddStringFieldInt(acpt, name, bForceString, V_I1(&var)) != WO_OK)
                return acpt->addInt8(name, V_I1(&var));
            break;
        case VT_I2:
            if (AddStringFieldInt(acpt, name, bForceString, V_I2(&var)) != WO_OK)
                return acpt->addInt16(name, V_I2(&var));
            break;
        case VT_INT:
        case VT_I4:
            if (AddStringFieldInt(acpt, name, bForceString, V_I4(&var)) != WO_OK)
                return acpt->addInt32(name, V_I4(&var));
            break;
        case VT_UI1:
            if (AddStringFieldInt(acpt, name, bForceString, V_UI1(&var)) != WO_OK)
                return acpt->addUint8(name, V_UI1(&var));
            break;
        case VT_UI2:
            if (AddStringFieldInt(acpt, name, bForceString, V_UI2(&var)) != WO_OK)
                return acpt->addUint16(name, V_UI2(&var));
            break;
        case VT_UINT:
        case VT_UI4:
            if (AddStringFieldInt(acpt, name, bForceString, V_UI4(&var)) != WO_OK)
                return acpt->addUint32(name, V_UI4(&var));
            break;
        case VT_R4:
            if (AddStringFieldFloat(acpt, name, bForceString, V_R4(&var)) != WO_OK)
                return acpt->addFloat32(name, V_R4(&var));
            break;
        case VT_R8:
            if (AddStringFieldFloat(acpt, name, bForceString, V_R8(&var)) != WO_OK)
                return acpt->addFloat64(name, V_R8(&var));
            break;
        case VT_BOOL:
            if (AddStringFieldInt(acpt, name, bForceString, V_BOOL(&var) == VARIANT_TRUE) != WO_OK)
                return acpt->addBool(name, V_BOOL(&var) == VARIANT_TRUE);
            break;
        case VT_ERROR:
            if (AddStringFieldInt(acpt, name, bForceString, V_ERROR(&var)) != WO_OK)
                return acpt->addInt32(name, V_ERROR(&var));
            break;
        case VT_LPWSTR:
        case VT_BSTR:
            return acpt->addString(name, V_BSTR(&var));
        case VT_ARRAY | VT_VARIANT:
		case VT_SAFEARRAY:
			return SerializeSafeArray(V_ARRAY(&var), acpt, name, bForceString, bDiscrete);
        default:
            // 不支持其他类型的转换
            return WO_FAIL;
    }
    return WO_OK;
}

WebInt SerializeSafeArray(SAFEARRAY *pArray, ISerialAcceptor* acpt, WebName name, bool bForceString, bool bDiscrete)
{
    if (name)
        acpt->addKey(name);
    std::unique_ptr<SIZE32[]> indexes(new SIZE32[pArray->cDims]);
    WebInt ret = WO_OK;
    acpt->beginArray();
    ret = SerializeSafeArrayInner(pArray, 0, indexes.get(), acpt, bForceString, bDiscrete);
    acpt->endArray();
    return ret;
}

// 序列化数组
WebInt SerializeSafeArrayInner(SAFEARRAY *pArray, UINT nDims, SIZE32* indexes, ISerialAcceptor* acpt, bool bForceString, bool bDiscrete)
{
    if (nDims >= pArray->cDims || nDims >= 2)
    {
        ASSERT(FALSE);
        return WO_FAIL;
    }
    WebInt ret = WO_OK;
    for (ULONG i = 0; i < pArray->rgsabound[pArray->cDims - 1 - nDims].cElements; ++i)
    {
        indexes[nDims] = pArray->rgsabound[pArray->cDims - 1 - nDims].lLbound + i;
        // 最后一层，这时候要添加值了
        if (nDims == pArray->cDims - 1)
        {
            KComVariant var = { 0 };
            SafeArrayGetElement(pArray, indexes, (void*)&var);
            if (bDiscrete)
            {
                VARTYPE vt = V_VT(&var);
                if (vt == VT_EMPTY)
                    continue;
                acpt->beginStruct();
                acpt->addInt32("row", indexes[0]);
                acpt->addInt32("col", indexes[1]);
                ret = Serialize(acpt, var, "value", bForceString);
                acpt->endStruct();
            }
            else
            {
                if (bForceString) // 为true时，走原来的逻辑，所有结果全部以str返回
                {
                    ret = Serialize(acpt, var, nullptr, true);
                }
                else // 用结构体包一下，按类型返回
                {
                    acpt->beginStruct();
                    ret = Serialize(acpt, var, "value", false);
                    acpt->endStruct();
                }
            }
        }
        else
        {
            if (bDiscrete)
            {
                ret = SerializeSafeArrayInner(pArray, nDims + 1, indexes, acpt, bForceString, bDiscrete);
            }
            else
            {
                acpt->beginArray();
                ret = SerializeSafeArrayInner(pArray, nDims + 1, indexes, acpt, bForceString, bDiscrete);
                acpt->endArray();
            }
        }
        if (ret != WO_OK)
            return ret;
    }
    return WO_OK;
}

}  // namespace SerializeVariantHelper
}  // namespace wo