﻿#include "etstdafx.h"
#include "proofreaderdata.h"
namespace etai
{
namespace
{

const QString colIndexToName(int col)
{
    QString r;
    while (col >= 0)
    {
        if (col < 26)
        {
            char c = 'A' + col;
            return c + r;
        }
        else
        {
            int b = col % 26;
            col = (col / 26) - 1;
            char c = 'A' + b;
            r = c + r;
        }
    }

    return r;
}

bool isNullTitle(const QString &title)
{
    if (title.isEmpty())
        return true;
    if (title.trimmed().isEmpty())
        return true;
    if (title.compare("-", Qt::CaseInsensitive) == 0)
        return true;
    if (title.compare("NULL", Qt::CaseInsensitive) == 0)
        return true;

    return false;
}

} // namespace

CorrecteData::CorrecteData(const QJsonObject &obj)
    : m_refCount(0), m_contentErrorList(NULL), m_tableErrorList(NULL)
{
    parseListObject<ContentErrorList>(obj, "contentErrorList", &m_contentErrorList);
    parseListObject<TableErrorList>(obj, "tableErrorList", &m_tableErrorList);

    if (m_tableErrorList != NULL)
    {
        const TableErrorList &tableErrors = *m_tableErrorList;
        for (size_t i = 0; i < tableErrors.size(); i++)
        {
            const TableError &error = *tableErrors.at(i);
            if (error.columnErrorList() != NULL)
            {
                for (size_t j = 0; j < error.columnErrorList()->size(); j++)
                    m_correcteList.push_back(new Correcte(error.columnErrorList()->at(j)));
            }
        }
    }

    if (m_contentErrorList != NULL)
    {
        const ContentErrorList &contentErrors = *m_contentErrorList;
        for (size_t i = 0; i < contentErrors.size(); i++)
        {
            m_correcteList.push_back(new Correcte(NULL, contentErrors.at(i)));
        }
    }
}

void CorrecteData::adjustSelectedRange(range_helper::ranges& ranges)
{
    if (m_spWorksheet == NULL)
        return;

    if (m_correcteList.size() <= 1)
        return;

    if (!ranges || ranges.size() == 0)
        return;

    UINT nCount = 0;
    HRESULT hr = ranges->GetCount(&nCount);
    if (FAILED(hr))
        return;

    for (UINT i = 0; i < nCount; ++i)
    {
        const RANGE *pRg = ranges.at(i).second;
        if (!pRg)
            continue;

        std::vector<Correcte *>::const_iterator it = m_correcteList.begin();
        while (it != m_correcteList.end())
        {
            Correcte *correcte = *it;
            if (correcte->columnError() != NULL)
            {
                const ES_CUBE cube = correcte->columnError()->cube();

                COL minCol = MAX(pRg->ColFrom(), cube.colFrom);
                ROW minRow = MAX(pRg->RowFrom(), cube.rowFrom);
                COL maxCol = MIN(pRg->ColTo(), cube.colTo);
                ROW maxRow = MIN(pRg->RowTo(), cube.rowTo);
                if (minCol <= maxCol && minRow <= maxRow)
                {
                    m_correcteList.erase(it);
                    m_correcteList.insert(m_correcteList.begin(), correcte);
                    break;
                }
            }

            ++it;
        }
    }
}

void CorrecteData::setWorksheet(IKWorksheet *worksheet)
{
    m_spWorksheet = worksheet;
    if (worksheet)
    {
        m_spWorkbook = worksheet->GetWorkbook();
        if (m_contentErrorList != NULL)
            m_contentErrorList->setWorksheet(worksheet);
        if (m_tableErrorList != NULL)
            m_tableErrorList->setWorksheet(worksheet);
    }
}

CorrecteData::~CorrecteData()
{
    delete m_contentErrorList;
    delete m_tableErrorList;

    for (size_t i = 0; i < m_correcteList.size(); i++)
        delete m_correcteList.at(i);
}

void CorrecteData::AddRef()
{
    ++m_refCount;
}

void CorrecteData::Release()
{
    --m_refCount;
    if (m_refCount == 0)
        delete this;
}

TableErrorList::TableErrorList(const QJsonArray &array) : Container<TableError>(array)
{
}

TableErrorList::~TableErrorList()
{
}

void TableErrorList::setWorksheet(IKWorksheet *worksheet)
{
    for (size_t i = 0; i < size(); i++)
    {
        const_cast<TableError *>(at(i))->setWorksheet(worksheet);
    }
}

TableErrorList::TableErrorList(const TableErrorList &o) : Container<TableError>(o)
{
}

TableError::TableError(const QJsonValue &val) : m_columnErrorList(NULL)
{
    const QJsonObject obj = val.toObject();
    parseRange(obj, "tableRg", &m_tableRange);
    parseRange(obj, "rowTitleRg", &m_rowTitleRange);
    parseRange(obj, "contentRg", &m_contentRange);
    parseListObject<ColumnErrorList>(obj, "columnErrorList", &m_columnErrorList);

    if (m_columnErrorList != NULL)
    {
        m_columnErrorList->setTableRange(&m_tableRange);
    }
}

TableError::~TableError()
{
    delete m_columnErrorList;
}

void TableError::setWorksheet(IKWorksheet *worksheet)
{
    if (m_columnErrorList != NULL)
        m_columnErrorList->setWorksheet(worksheet);
}

bool TableError::parseRange(ES_CUBE *range, const QJsonValue &value)
{
    if (value.isArray())
    {
        const QJsonArray array = value.toArray();
        ASSERT(array.size() == 4);

        if (array.size() == 4)
        {
            range->rowFrom = array.at(0).toInt();
            range->rowTo = array.at(1).toInt();
            range->colFrom = array.at(2).toInt();
            range->colTo = array.at(3).toInt();

            return true;
        }
    }

    return false;
}

bool TableError::parseRange(const QJsonObject &obj, const QString &key, ES_CUBE *range)
{
    QJsonObject::const_iterator it = obj.constFind(key);
    if (it != obj.constEnd())
    {
        return parseRange(range, it.value());
    }

    return false;
}

ErrorWordInfo::ErrorWordInfo(const QJsonValue &val)
{
    if (!val.isObject())
        return;

    const QJsonObject obj = val.toObject();

    readInteger(obj, "count", &m_count, -1);
    ASSERT(m_count != -1);

    bool b = readString(obj, "type", &m_type);
    ASSERT(b);

    QJsonObject::const_iterator it = obj.constFind("errorTypeList");
    if (it != obj.constEnd() && it.value().isArray())
    {
        const QJsonArray array = it.value().toArray();
        for (int i = 0; i < array.size(); i++)
        {
            ErrorType errorType = CellError::toErrorType(array.at(i).toString());
            if (errorType != ErrorType_Undefined)
                m_errorTypeList.push_back(errorType);
        }
    }
}

bool ErrorWordInfo::hasErrorType(const ErrorType errorType) const
{
    for (size_t i = 0; i < m_errorTypeList.size(); i++)
    {
        if (errorType == m_errorTypeList.at(i))
            return true;
    }

    return false;
}

ErrorWordInfoList::ErrorWordInfoList(const QJsonArray &array) : Container<ErrorWordInfo>(array)
{
}

ErrorWordInfoList::~ErrorWordInfoList()
{
}

ErrorWordInfoList::ErrorWordInfoList(const ErrorWordInfoList &o) : Container<ErrorWordInfo>(o)
{
}

ColumnErrorList::ColumnErrorList(const QJsonArray &array) : Container<ColumnError>(array)
{
}

ColumnErrorList::~ColumnErrorList()
{
}

void ColumnErrorList::setTableRange(const ES_CUBE *tableRange)
{
    for (size_t i = 0; i < size(); i++)
    {
        const_cast<ColumnError *>(at(i))->setTableRange(tableRange);
    }
}

void ColumnErrorList::setWorksheet(IKWorksheet *worksheet)
{
    for (size_t i = 0; i < size(); i++)
    {
        const_cast<ColumnError *>(at(i))->setWorksheet(worksheet);
    }
}

ColumnErrorList::ColumnErrorList(const ColumnErrorList &o) : Container<ColumnError>(o)
{
}

ColumnError::ColumnError(const QJsonValue &val)
    : m_columnIndex(-1), m_unifiedNormalFormatLocalList(NULL), m_unifiedCellErrorList(NULL), m_normCellErrorList(NULL),
      m_tableRange(NULL), m_errorWordInfoList(NULL), m_correcteUnifiedCellCount(0), m_correcteNormCellCount(0)
{
    const QJsonObject obj = val.toObject();

    readInteger(obj, "columnIndex", &m_columnIndex, -1);
    ASSERT(m_columnIndex != -1);

    readStringList(obj, "rowTitleContentList", &m_rowTitleContentList);

    parseListObject<UnifiedNormalFormatLocalList>(obj, "unifiedNormalFormatLocalList", &m_unifiedNormalFormatLocalList);
    parseListObject<CellErrorList>(obj, "unifiedCellErrorList", &m_unifiedCellErrorList);
    parseListObject<CellErrorList>(obj, "normCellErrorList", &m_normCellErrorList);
    parseListObject<ErrorWordInfoList>(obj, "errorWordInfoList", &m_errorWordInfoList);

    if (m_unifiedCellErrorList != NULL)
        for (size_t i = 0; i < m_unifiedCellErrorList->size(); i++)
            m_cellErrorList.push_back(m_unifiedCellErrorList->at(i));
    if (m_normCellErrorList != NULL)
        for (size_t i = 0; i < m_normCellErrorList->size(); i++)
            m_cellErrorList.push_back(m_normCellErrorList->at(i));
    std::sort(m_cellErrorList.begin(), m_cellErrorList.end(), CellError::compare);

    groupCellError();
}

ColumnError::~ColumnError()
{
    delete m_unifiedNormalFormatLocalList;
    delete m_unifiedCellErrorList;
    delete m_normCellErrorList;
    delete m_errorWordInfoList;

    for (size_t i = 0; i < m_cellErrorGroup.size(); i++)
        delete m_cellErrorGroup.at(i);
    m_cellErrorGroup.clear();
}

const QString ColumnError::columnName() const
{
    return colIndexToName(m_columnIndex);
}

const ES_CUBE ColumnError::cube() const
{
    ES_CUBE cube;
    bool has = false;
    if (m_unifiedCellErrorList != NULL && m_unifiedCellErrorList->size() > 0)
    {
        cube = m_unifiedCellErrorList->at(0)->cube();
        for (size_t i = 1; i < m_unifiedCellErrorList->size(); i++)
        {
            const ES_CUBE &o = m_unifiedCellErrorList->at(i)->cube();

            if (cube.colTo < o.colTo)
                cube.colTo = o.colTo;
            if (cube.colFrom > o.colFrom)
                cube.colFrom = o.colFrom;
            if (cube.rowTo < o.rowTo)
                cube.rowTo = o.rowTo;
            if (cube.rowFrom > o.rowFrom)
                cube.rowFrom = o.rowFrom;
        }
        has = true;
    }

    if (m_normCellErrorList != NULL && m_normCellErrorList->size() > 0)
    {
        if (!has)
        {
            cube = m_normCellErrorList->at(0)->cube();
        }
        for (size_t i = 0; i < m_normCellErrorList->size(); i++)
        {
            const ES_CUBE &o = m_normCellErrorList->at(i)->cube();

            if (cube.colTo < o.colTo)
                cube.colTo = o.colTo;
            if (cube.colFrom > o.colFrom)
                cube.colFrom = o.colFrom;
            if (cube.rowTo < o.rowTo)
                cube.rowTo = o.rowTo;
            if (cube.rowFrom > o.rowFrom)
                cube.rowFrom = o.rowFrom;
        }
    }

    return cube;
}

const ES_CUBE ColumnError::columnCube() const
{
    if (m_tableRange == NULL)
        return cube();

    ES_CUBE cube = *m_tableRange;
    cube.colTo = m_columnIndex;
    cube.colFrom = cube.colTo;

    // bug #487216
    //如果所有的识别结果的 colTo 和 colFrom 相同，则把cube.colTo 和 cube.colFrom 调整为识别的相应的值
    //否则cube.colTo 和 cube.colFrom 指定为列的 columnIndex;
    COL colFrom = -1;
    COL colTo = -1;

    if (m_normCellErrorList != NULL)
    {
        for (size_t i = 0; i < m_normCellErrorList->size(); i++)
        {
            const CellError &error = *m_normCellErrorList->at(i);

            if (!error.isMerge())
                return cube;

            if (colTo == -1)
            {
                colTo = error.mergeCube().colTo;
                colFrom = error.mergeCube().colFrom;
            }
            else if (error.mergeCube().colTo != colTo || error.mergeCube().colFrom != colFrom)
            {
                return cube;
            }
        }
    }

    if (m_unifiedCellErrorList != NULL)
    {
        for (size_t i = 0; i < m_unifiedCellErrorList->size(); i++)
        {
            const CellError &error = *m_unifiedCellErrorList->at(i);

            if (!error.isMerge())
                return cube;

            if (colTo == -1)
            {
                colTo = error.mergeCube().colTo;
                colFrom = error.mergeCube().colFrom;
            }
            else if (error.mergeCube().colTo != colTo || error.mergeCube().colFrom != colFrom)
            {
                return cube;
            }
        }
    }

    cube.colTo = colTo;
    cube.colFrom = colFrom;

    return cube;
}

CorrectType ColumnError::correctType() const
{
    if (m_unifiedCellErrorList != NULL && m_unifiedCellErrorList->size() > 0)
        return CorrectType_Uniform_Format;
    else
        return CorrectType_Specification;
}

const QString ColumnError::rowTitle() const
{
    int i = m_rowTitleContentList.size() - 1;
    while (i >= 0 && isNullTitle(m_rowTitleContentList.at(i)))
        i--;

    QString col;
    col.append(QObject::tr("%1column:"));
    col = col.arg(colIndexToName(m_columnIndex));

    if (i >= 0)
        return col.append(m_rowTitleContentList.at(i));
    else
        return col;
}

void ColumnError::setTableRange(const ES_CUBE *tableRange)
{
    m_tableRange = tableRange;
}

void ColumnError::setWorksheet(IKWorksheet *worksheet)
{
    if (m_unifiedCellErrorList != NULL)
        m_unifiedCellErrorList->setWorksheet(worksheet);
    if (m_normCellErrorList != NULL)
        m_normCellErrorList->setWorksheet(worksheet);

    ASSERT(worksheet != NULL);

    ISheet *pSheet = worksheet->GetSheet();
    if (!pSheet)
        return;

    RANGE range(cube(), pSheet->GetBMP());
    worksheet->GetRangeByData(&range, &m_spRange);

    RANGE columnRange(columnCube(), pSheet->GetBMP());
    worksheet->GetRangeByData(&columnRange, &m_spColumnRange);
}

void ColumnError::groupCellError()
{
    typedef std::map<ErrorType, std::vector<const CellError *> *> CellErrorMap;
    CellErrorMap cellErrorGroup;

    for (size_t i = 0; i < m_cellErrorList.size(); i++)
    {
        const CellError *error = m_cellErrorList.at(i);

        if (error->errorType() != ErrorType_Undefined)
        {
            const CellErrorMap::iterator it = cellErrorGroup.find(error->errorType());
            if (it == cellErrorGroup.end())
            {
                std::vector<const CellError *> *list = new std::vector<const CellError *>();
                list->push_back(error);
                cellErrorGroup.insert(std::make_pair(error->errorType(), list));
            }
            else
                it->second->push_back(error);
        }
    }

    CellErrorMap::const_iterator it = cellErrorGroup.begin();
    while (it != cellErrorGroup.end())
    {
        m_cellErrorGroup.push_back(it->second);
        ++it;
    }

    std::sort(m_cellErrorGroup.begin(), m_cellErrorGroup.end(), &compareCellErrorList);
}

UnifiedNormalFormatLocalList::UnifiedNormalFormatLocalList(const QJsonArray &array)
    : Container<UnifiedNormalFormatLocal>(array)
{
}

UnifiedNormalFormatLocalList::~UnifiedNormalFormatLocalList()
{
}

UnifiedNormalFormatLocalList::UnifiedNormalFormatLocalList(const UnifiedNormalFormatLocalList &o)
    : Container<UnifiedNormalFormatLocal>(o)
{
}

UnifiedNormalFormatLocal::UnifiedNormalFormatLocal(const QJsonValue &val) : m_frequency(0)
{
    if (!val.isArray())
        return;

    const QJsonArray array = val.toArray();
    if (array.size() != 2)
        return;

    m_formatLocal = array.at(0).toString();
    m_frequency = array.at(1).toInt();
}

UnifiedNormalFormatLocal::~UnifiedNormalFormatLocal()
{
}

CellError::CellError(const QJsonValue &val)
    : CellErrorBase(val), m_identifyType(IdentifyType_Number), m_errorType(ErrorType_Undefined)
{
    if (!val.isObject())
        return;

    const QJsonObject obj = val.toObject();

    QString identifyType;
    if (readString(obj, "identifyType", &identifyType))
        m_identifyType = ContentError::toIdentifyType(identifyType);
    QString errorType;
    if (readString(obj, "errorType", &errorType))
        m_errorType = toErrorType(errorType);
    readString(obj, "value2", &m_value2);
    readString(obj, "numberFormatLocal", &m_numberFormatLocal);
    if (m_numberFormatLocal.isEmpty())
        m_numberFormatLocal = "General";
}

CellError::~CellError()
{
}

ErrorType CellError::toErrorType(const QString &name)
{
    const char *ErrorTypes[] = {"UMultipleDate", "UMultipleTime",  "UTextDate",    "UTextTime", "NTextDate",
                                "NTextTime",     "NTextNumber",    "NMoneyNumber", "NThousand", "NNormDate",
                                "NNormTime",     "NPercentNumber", "NNumber"};
    int i = 0;
    while (i < ErrorType_End && name.compare(ErrorTypes[i], Qt::CaseInsensitive) != 0)
        i++;
    if (i < ErrorType_End)
        return (ErrorType)i;
    else
        return ErrorType_Undefined;
}

CellErrorList::CellErrorList(const QJsonArray &array) : Container<CellError>(array)
{
}

CellErrorList::~CellErrorList()
{
}

void CellErrorList::setWorksheet(IKWorksheet *worksheet)
{
    for (size_t i = 0; i < size(); i++)
    {
        const_cast<CellError *>(at(i))->setWorksheet(worksheet);
    }
}

CellErrorList::CellErrorList(const CellErrorList &o) : Container<CellError>(o)
{
}

ContentError::ContentError(const QJsonValue &val) : CellErrorBase(val), m_errorType(IdentifyType_Number)
{
    if (!val.isObject())
        return;

    const QJsonObject obj = val.toObject();

    QString errorType;
    if (readString(obj, "errorType", &errorType))
        m_errorType = toIdentifyType(errorType);
}

ContentError::~ContentError()
{
}

IdentifyType ContentError::toIdentifyType(const QString &name)
{
    const QString IdentifyTypes[] = {"Date", "Time", "DateTime", "MoneyNumber"};
    int i = 0;
    while (i < IdentifyType_Number && name.compare(IdentifyTypes[i], Qt::CaseInsensitive) != 0)
        i++;
    return (IdentifyType)i;
}

ContentErrorList::ContentErrorList(const QJsonArray &array) : Container<ContentError>(array)
{
}

ContentErrorList::ContentErrorList(const ContentErrorList &o) : Container<ContentError>(o)
{
}

ContentErrorList::~ContentErrorList()
{
}

void ContentErrorList::setWorksheet(IKWorksheet *worksheet)
{
    for (size_t i = 0; i < size(); i++)
    {
        const_cast<ContentError *>(at(i))->setWorksheet(worksheet);
    }
}

CellErrorBase::CellErrorBase(const QJsonValue &val)
    : m_rowIndex(-1), m_isMerge(false), m_hasOldValue2(false), m_hasOldText(false), m_hasOldNumberFormatLocal(false)
{
    if (!val.isObject())
        return;

    const QJsonObject obj = val.toObject();

    readInteger(obj, "rowIndex", &m_rowIndex, -1);
    ASSERT(m_rowIndex != -1);

    readString(obj, "content", &m_content);
    readCube(obj, "range", &m_cube);
    m_isMerge = readCube(obj, "mergeColumnRange", &m_mergeCube);
}

CellErrorBase::~CellErrorBase()
{
}

const QString CellErrorBase::location() const
{
    QString r = colIndexToName(cube().colFrom);
    return r.append(QString::number(rowIndex() + 1));
}

void CellErrorBase::setWorksheet(IKWorksheet *worksheet)
{
    ASSERT(worksheet != NULL);

    ISheet *pSheet = worksheet->GetSheet();
    if (!pSheet)
        return;

    RANGE range(m_cube, pSheet->GetBMP());
    HRESULT hr = worksheet->GetRangeByData(&range, &m_spRange);
    if (FAILED(hr))
        return;

    ks_bstr cellText;
    m_spRange->get_Text(&cellText);
    m_text = krt::fromUtf16(cellText.c_str()).trimmed();
}

bool CellErrorBase::readCube(const QJsonObject &obj, const QString &key, ES_CUBE *cube)
{
    QJsonObject::const_iterator it = obj.constFind(key);
    if (it != obj.constEnd() && it.value().isArray())
    {
        QJsonArray array = it.value().toArray();
        if (array.size() == 2)
        {
            cube->colFrom = array.at(0).toInt();
            cube->colTo = array.at(1).toInt();
            cube->rowFrom = m_rowIndex;
            cube->rowTo = cube->rowFrom;

            return true;
        }
    }

    return false;
}

Correcte::Correcte(const ColumnError *columnError, const ContentError *contentError)
    : m_columnError(columnError), m_contentError(contentError), m_status(Status_Untreated)
{
}

Correcte::Correcte(const ColumnError *columnError)
    : m_columnError(columnError), m_contentError(NULL), m_status(Status_Untreated)
{
}

Correcte::~Correcte()
{
}

void Correcte::deal()
{
    m_status = Status_Already_Deal;
}

void Correcte::ignore()
{
    m_status = Status_Ignore;
}

void Correcte::untreated()
{
    m_status = Status_Untreated;
}

void Correcte::userEdited()
{
    m_status = Status_User_Edited;
}
} // namespace etai