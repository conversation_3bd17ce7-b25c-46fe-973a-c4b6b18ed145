﻿/* -------------------------------------------------------------------------
//	文件名		：	smart_tips.cpp
//	创建者		：	徐建栋
//	创建时间	：	2004-11-8 14:18:00
//	功能描述	：	
//
// -----------------------------------------------------------------------*/

#include "etstdafx.h"
#include "smart_tips.h"
#include "et_hard_define_strings.h"
#include "funclib/et_funclib_ids.h"
#include "kso/l10n/et/et_app.h"
#include "kso/appfeature/kappfeature.h"
#include "kfc/et_numfmt_str.h"
#include "alg/alg.h"
#include "funclib/et_funclib_forstatus.h"
#include "tools/et_combstr.h"
#include "krt/krt.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#define SMARTTIPS_MAXDOUBLE	(10000.0)
const INT64 C_MAX_IDLE_COUNT = 1048576;

class KIdleUpdateHelper : public IEventSink
{
public:
	KIdleUpdateHelper()
		: m_pBookOp(NULL)
		, m_curSheetId(-1)
	{
		m_valVec.resize(MAX_OF_FSI, 0);
		m_errVec.resize(MAX_OF_FSI, false);
/*		UilHelper::GetApp()->GetEventManager()->RegisterEventSink(EVT_Document | EVT_View, this);*/
	}
	virtual ~KIdleUpdateHelper()
	{
		Clear();
// 		if (UilHelper::GetApp() && UilHelper::GetApp()->GetEventManager())
// 			UilHelper::GetApp()->GetEventManager()->RemoveEventSink(this);
	}

	void SetUpdateRangs(IBookOp *pBookOp, IDX iSheet, const range_helper::ranges & rgs)
	{
		if (m_pBookOp == pBookOp && iSheet == m_curSheetId && HasRangs())
			return;

		Clear();
		m_pBookOp = pBookOp;
		m_curSheetId = iSheet;
		Append(rgs);
	}

	void PopRangs(IKRanges **ppRgs)
	{
		*ppRgs = m_updateRgs.front();
		m_updateRgs.pop_front();
	}

	BOOL HasRangs()
	{
		return !m_updateRgs.empty();
	}

	KSmartTips::DOUBLE_VEC & GetValVec()
	{
		return m_valVec;
	}

	KSmartTips::BOOL_VEC & GetErrVec()
	{
		return m_errVec;
	}
private:
	HRESULT OnEvent(IN ET_Event_ID ID, IN KSO_WParam wParam, IN KSO_LParam lParam)
	{
		switch (ID)
		{
		case EVN_RangeSelectionChanged:
		case EVN_BeforeCloseBook:
		case EVN_ActiveSheet:
			Clear();
			break;
		}
		return S_OK;
	}
	void Clear()
	{
		m_pBookOp = NULL;
		m_curSheetId = -1;
		for (int i = 0; i < MAX_OF_FSI; ++i)
		{
			m_valVec.at(i) = 0;
			m_errVec.at(i) = false;
		}
		for (auto itr = m_updateRgs.begin(); itr != m_updateRgs.end(); ++itr)
			(*itr)->Release();
		m_updateRgs.clear();
	}
	void Append(const range_helper::ranges & rgs)
	{
		UINT nCount = 0;
		rgs->GetCount(&nCount);
		INT64 rgSumSize = 0;
		INT64 rgSize = 0;
		for (UINT i = 0; i < nCount; ++i)
		{
			const RANGE *pRg = NULL;
			rgs->GetItem(i, NULL, &pRg);
			rgSize = pRg->Width();
			rgSize *= pRg->Height();
			if (rgSumSize + rgSize > C_MAX_IDLE_COUNT)
			{
				INT64 diff = C_MAX_IDLE_COUNT - rgSumSize;
				int horz = diff / pRg->Width();
				int vert = diff / pRg->Height();
				if (horz >= vert && horz != 0)
				{
					RANGE rg(*pRg);
					rg.SetRowTo(rg.RowFrom() + horz - 1);
					AddBack(&rg);
					rg.SetRowFromTo(rg.RowFrom() + horz, pRg->RowTo());
					rgs->SetItem(i, 0, &rg);
				}
				else if (vert > horz && vert != 0)
				{
					RANGE rg(*pRg);
					rg.SetColTo(rg.ColFrom() + vert - 1);
					AddBack(&rg);
					rg.SetColFromTo(rg.ColFrom() + vert, pRg->ColTo());
					rgs->SetItem(i, 0, &rg);
				}
				AddNew();
				--i;
				rgSumSize = 0;
				continue;
			}
			else
			{
				rgSumSize += rgSize;
				AddBack(pRg);
			}
		}
	}

	void AddNew()
	{
		ks_stdptr<IKRanges> ptrRanges;
		_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&ptrRanges);
		m_updateRgs.push_back(ptrRanges.detach());
	}

	void AddBack(const RANGE *pRg)
	{
		if (m_updateRgs.empty())
			AddNew();

		IKRanges *pKRgs = m_updateRgs.back();
		pKRgs->Append(0, *pRg);
	}
private:
	KSmartTips::DOUBLE_VEC m_valVec;
	KSmartTips::BOOL_VEC m_errVec;
	std::list<IKRanges*> m_updateRgs;
	IBookOp *m_pBookOp;
	IDX m_curSheetId;
	int m_maxItemSize;
};

// -------------------------------------------------------------------------
KSmartTips::KSmartTips()
{
	m_spEtApp		= NULL;
	m_nfhComma		= NULL;
	m_nfhComma2		= NULL;
	m_nfhNormal		= NULL;
	m_nfhJPRead1	= NULL;
	m_nfhJPRead2	= NULL;
	m_bIsSpecificVersion	= FALSE;
}

KSmartTips::~KSmartTips()
{
	Term();
}

void KSmartTips::Init(IKEtApplication* pEtApp)
{
	m_spEtApp = pEtApp;
	ASSERT(m_spEtApp);
	m_spEtApp->QI(_Application, &m_spApiApp);
	ASSERT(m_spApiApp);
	m_spIdleUpdateHelper.clear();
	m_spIdleUpdateHelper = new KIdleUpdateHelper();
}

void KSmartTips::Term()
{
	if (m_nfhComma)
	{
		kfc::nf::_XNFRelease(m_nfhComma);
		m_nfhComma = NULL;
	}
	if (m_nfhComma2)
	{
		kfc::nf::_XNFRelease(m_nfhComma2);
		m_nfhComma2 = NULL;
	}
	if (m_nfhNormal)
	{
		kfc::nf::_XNFRelease(m_nfhNormal);
		m_nfhNormal = NULL;
	}
	if (m_nfhJPRead1)
	{
		kfc::nf::_XNFRelease(m_nfhJPRead1);
		m_nfhJPRead1 = NULL;
	}
	if (m_nfhJPRead2)
	{
		kfc::nf::_XNFRelease(m_nfhJPRead2);
		m_nfhJPRead2 = NULL;
	}
	m_spIdleUpdateHelper.clear();
}

BOOL KSmartTips::NeedMoreUpdate()
{
	return m_spIdleUpdateHelper->HasRangs();
}

HRESULT KSmartTips::UpdateSmartTip()
{
	//m_bResultStr = FALSE;
	if (!m_strResultString.empty())
		m_strResultString.clear();
	if (!m_strTips.empty())
		m_strTips.clear();
	UINT nShowMember = _GetVisibleTypes();

	if (nShowMember == et_applogic::stNone) 
		return S_OK;

	ks_stdptr<IKEtView> spETView = m_spEtApp->GetActiveView();
	if (!spETView || spETView->IsPrintPreviewMode())
		return S_FALSE;

	ks_stdptr<IBook> spBook = _GetActiveBook();
	if (!spBook)
		return S_FALSE;

	ks_stdptr<IBookOp> spBookOp;
	spBook->GetOperator(&spBookOp);

	KCOMPTR(Range) spRange;
	KCOMPTR(IKCoreObject) spDisp;
	m_spApiApp->get_Selection(0, &spDisp);
	if (spDisp != NULL)
		spDisp->QI(Range, &spRange);
	if (spRange == NULL)
		return S_FALSE;

	KCOMPTR(IKRanges) spRanges;
	app_helper::GetIRanges(spRange, &spRanges);
	if (spRanges == NULL)
		return S_FALSE;

	if (_kso_QueryFeatureState(kaf_et_statubar_auto_calc))  //zouyf, 2008-7-2,
	{
		GetTips(spRanges, spBookOp, nShowMember, m_strTips);
	}
	else
	{
		GetTips2(spRanges, spBookOp, nShowMember, m_strResultString, &m_strTips);
	}

	return S_OK;
}

HRESULT KSmartTips::GetResultString(ks_bstr& resultString)
{
	HRESULT hr = UpdateSmartTip();
	resultString.assign(m_strResultString.c_str());
	if (m_bIsSpecificVersion)
		m_bIsSpecificVersion = FALSE;

	return hr;
}

HRESULT KSmartTips::GetSmartTip(ks_bstr& smartTip)
{
	HRESULT hr = UpdateSmartTip();
	smartTip.assign(m_strTips.c_str());
	return hr;
}

void KSmartTips::GetStatusBarText(ks_bstr& sTip)
{
	UINT visibleTypes = _GetVisibleTypes();

	if(m_bIsSpecificVersion || visibleTypes == et_applogic::stNone)
		GetResultString(sTip);
	else
		GetSmartTip(sTip);
}

void KSmartTips::SetIsSpecialSmartTips(BOOL bv)
{
	m_bIsSpecificVersion = bv;
}
HRESULT KSmartTips::GetTips(IKRanges* pranges, IBookOp* pBookOp, UINT smarttipsoptions, ks_wstring& result)
{
	return GetTips2(pranges, pBookOp, smarttipsoptions, result, NULL);
}

//zouyf, 2008-7-2, 让状态栏能显示多种计算结果
HRESULT KSmartTips::GetTips2(IKRanges* pranges, IBookOp* pBookOp, UINT smarttipsoptions, ks_wstring& result, ks_wstring* pResult2)
{
	IBook* pBook = _GetActiveBook();
	bool b1904 = pBook ? pBook->Is1904DateSystem() : false;

	range_helper::ranges ranges = pranges;
	using namespace et_applogic;
	result.clear();
	
	if (pResult2)
		pResult2->clear();

	ranges.SubIntersect();
	if (ranges.size() == 0)
		return S_OK;
	
	const RANGE rg = *ranges[0].second;
	if (!rg.IsValid())
		return S_OK;

	IDX iSheet = ranges[0].second->SheetFrom();
	if (ranges.size() == 1 && rg.IsSingleCell()) // 察看单个单元格
	{
		const alg::ExecToken* pToken;
		pBookOp->GetCellValue(iSheet, rg.RowFrom(), rg.ColFrom(), &pToken);
		if(pToken)
		{
			//选中的单元格中的不是数字时，状态栏不显示
			double dvalue = 0.0;
			if (FAILED(GetDoubleValue(pToken, dvalue)))
				return S_OK;

			//1904
			switch (krt::l10n::getInt("AUTO_CALCULATE_READ_MODE"))
			{
			case 1:	// 中文
				FormatToken(dvalue, _GetTipsMode(), b1904, result);
				break;
			case 2: // 日文
				FormatToken(dvalue, rm_jpRead, b1904, result);
				break;
			}
		}
		return S_OK;
	}

	ASSERT(smarttipsoptions != stNone);

	m_spIdleUpdateHelper->SetUpdateRangs(pBookOp, iSheet, ranges);
	DOUBLE_VEC &valVec = m_spIdleUpdateHelper->GetValVec();
	BOOL_VEC &errVec = m_spIdleUpdateHelper->GetErrVec();
	ks_stdptr<IKRanges> ptrRgs;
	m_spIdleUpdateHelper->PopRangs(&ptrRgs);
	ranges = ptrRgs;
	GetTipValue(pBookOp, iSheet, ranges, valVec, errVec);
	if (m_spIdleUpdateHelper->HasRangs())
		return S_OK;

	if (smarttipsoptions & stSum) 
	{
		ks_wstring strResult;
		FormatDouble(errVec[FSI_SUM], b1904, valVec[FSI_SUM], strResult);
		//如果求和超过范围，则不显示
		if (!strResult.empty())
		{
			result.append(__X("  "));
			result.append(TX_SMART_TIPS_SUM);
			result.append(strResult);

			if (pResult2)
			{
				pResult2->append(__X("  "));
				pResult2->append(TX_SMART_TIPS_SUM);
			}
		}
	}

	if (smarttipsoptions & stAverage)
	{
		result.append(__X("  "));
		result.append(TX_SMART_TIPS_AVERAGE);
		FormatDouble(errVec[FSI_AVERAGE], b1904, valVec[FSI_AVERAGE], result);
		if (pResult2)
		{
			pResult2->append(__X("  "));
			pResult2->append(TX_SMART_TIPS_AVERAGE);
		}		
	}

	if(smarttipsoptions & stCount)	//	千万别认为stCount与COUNT公式同义，实际上这是COUNTA
	{
		result.append(__X("  "));
		result.append(TX_SMART_TIPS_COUNT);

		//如果是以当前活动单元格格式模式显示，计数和计数值用normal方式
		if (rm_ActiveCellBased == _GetTipsMode())
		{
			//1904
			Format_Normal(valVec[FSI_COUNT], b1904, result);
		}
		else
		{
			FormatDouble(errVec[FSI_COUNT], b1904, valVec[FSI_COUNT], result);
		}

		if(pResult2)
		{
			pResult2->append(__X("  "));
			pResult2->append(TX_SMART_TIPS_COUNT);
		}		
	}

	if(smarttipsoptions & stNumbersCount)
	{
		result.append(__X("  "));
		result.append(TX_SMART_TIPS_NUMBERCOUNT);

		if (rm_ActiveCellBased == _GetTipsMode())
		{
			//1904
			Format_Normal(valVec[FSI_NUMCOUNT], b1904, result);
		}
		else
		{
			FormatDouble(errVec[FSI_NUMCOUNT], b1904, valVec[FSI_NUMCOUNT], result);
		}

		if (pResult2)
		{
			pResult2->append(__X("  "));
			pResult2->append(TX_SMART_TIPS_NUMBERCOUNT);
		}	
	}

	if(smarttipsoptions & stMin)
	{
		result.append(__X("  "));
		result.append(TX_SMART_TIPS_MIN);
		FormatDouble(errVec[FSI_MIN], b1904, valVec[FSI_MIN], result);

		if (pResult2)
		{
			pResult2->append(__X("  "));
			pResult2->append(TX_SMART_TIPS_MIN);
		}		
	}

	if(smarttipsoptions & stMax)
	{
		result.append(__X("  "));
		result.append(TX_SMART_TIPS_MAX);
		FormatDouble(errVec[FSI_MAX], b1904, valVec[FSI_MAX], result);

		if (pResult2)
		{
			pResult2->append(__X("  "));
			pResult2->append(TX_SMART_TIPS_MAX);
		}		
	}

	if(pResult2)
	{
		pResult2->append(TX_SMART_TIPS_CONFIRM);
	}
	return S_OK;
}

HRESULT KSmartTips::WO_GetTips(IKRanges* pranges, WO_RESULT_VEC& result, DOUBLE_VEC& dbResult)
{
	range_helper::ranges ranges = pranges;
	ks_stdptr<IBook> spBook = _GetActiveBook();
	if (!spBook)
		return S_FALSE;

	bool b1904 = spBook ? spBook->Is1904DateSystem() : false;

	ks_stdptr<IBookOp> spBookOp;
	spBook->GetOperator(&spBookOp);

	ranges.SubIntersect();
	if (ranges.size() == 0)
		return S_OK;

	const RANGE rg = *ranges[0].second;
	if (!rg.IsValid())
		return S_OK;

	IDX iSheet = ranges[0].second->SheetFrom();
	if (ranges.size() == 1 && rg.IsSingleCell()) // 察看单个单元格
		return S_OK;

	DOUBLE_VEC valVec = m_spIdleUpdateHelper->GetValVec();
	BOOL_VEC errVec = m_spIdleUpdateHelper->GetErrVec();
	valVec.resize(MAX_OF_FSI, 0);
	errVec.resize(MAX_OF_FSI, false);
	HRESULT hr = GetTipValue(spBookOp, iSheet, ranges, valVec, errVec);
	dbResult.assign(valVec.begin(), valVec.end());

	// sum
	{
		ks_wstring strResult;
		FormatDouble(errVec[FSI_SUM], b1904, valVec[FSI_SUM], strResult);
		result.push_back(strResult);
	}

	// average
	{
		ks_wstring strResult;
		FormatDouble(errVec[FSI_AVERAGE], b1904, valVec[FSI_AVERAGE], strResult);
		result.push_back(strResult);
	}

	// count	//	千万别认为stCount与COUNT公式同义，实际上这是COUNTA
	{
		ks_wstring strResult;
		//如果是以当前活动单元格格式模式显示，计数和计数值用normal方式
		if (rm_ActiveCellBased == _GetTipsMode())
		{
			//1904
			Format_Normal(valVec[FSI_COUNT], b1904, strResult);
		}
		else
		{
			FormatDouble(errVec[FSI_COUNT], b1904, valVec[FSI_COUNT], strResult);
		}
		result.push_back(strResult);
	}

	// numbercount
	{
		ks_wstring strResult;

		if (rm_ActiveCellBased == _GetTipsMode())
		{
			//1904
			Format_Normal(valVec[FSI_NUMCOUNT], b1904, strResult);
		}
		else
		{
			FormatDouble(errVec[FSI_NUMCOUNT], b1904, valVec[FSI_NUMCOUNT], strResult);
		}
		result.push_back(strResult);
	}

	// min
	{
		ks_wstring strResult;
		FormatDouble(errVec[FSI_MIN], b1904, valVec[FSI_MIN], strResult);
		result.push_back(strResult);
	}

	// max
	{
		ks_wstring strResult;
		FormatDouble(errVec[FSI_MAX], b1904, valVec[FSI_MAX], strResult);
		result.push_back(strResult);
	}

	return hr;
}

HRESULT KSmartTips::FormatDouble(bool bError, bool b1904, double dbValue, ks_wstring& result)
{
	if (!bError)
	{
		ks_wstring strRead;
		//1904
		switch (krt::l10n::getInt("AUTO_CALCULATE_READ_MODE"))
		{
		case 1:	// 中文
			FormatToken(dbValue, _GetTipsMode(), b1904, strRead);
			break;
		case 2: // 日文
			FormatToken(dbValue, rm_jpRead, b1904, strRead);
			break;
		default:
			FormatToken(dbValue, rm_Normal, b1904, strRead);
			break;
		}
		if(strRead.length() > 0)
		{
			result.append(strRead);
		}
		return S_OK;
	}
	else //zouyf, 2008-10-22, 平均值要特殊处理，因为选中的一片区域全部是文本，average函数返回错误值
	{
		result.append(__X("0"));
		return S_FALSE;
	}
}

//通过公式的toke得到double值const alg::ExecToken* pToken, pToken只能是
//alg::ETP_VINT 或 alg::ETP_VDBL
HRESULT KSmartTips::GetDoubleValue( const alg::ExecToken* pToken, double& dValue )
{
	alg::const_token_assist ta(pToken);
	if(ta.major_type() == alg::ETP_VINT)
	{
		dValue = alg::const_vint_token_assist(pToken).get_value();
	}
	else if(ta.major_type() == alg::ETP_VDBL)
	{
		dValue = alg::const_vdbl_token_assist(pToken).get_value();
	}
	else
	{
		return E_FAIL;
	}

	return S_OK;
}

void KSmartTips::GetTokenValue(const alg::ExecToken* pToken, ForStatusIndex FSI,
							   DOUBLE_IT itVal, BOOL_IT itErr, bool bFirstGp)
{
	if (*itErr)
	{
		return;
	}

	double dbtmp = 0.;
	*itErr = (S_OK != GetDoubleValue(pToken, dbtmp)) ? true : false;
	if (!*itErr)
	{
		switch(FSI)
		{
		case FSI_COUNT:
		case FSI_NUMCOUNT:
		case FSI_SUM:
			*itVal += dbtmp;
			break;
		case FSI_MAX:
			if(bFirstGp)
			{
				*itVal = dbtmp;
			}
			else
			{
				*itVal = MAX(*itVal, dbtmp);
			}
			break;
		case FSI_MIN:
			if (bFirstGp)
			{
				*itVal = dbtmp;
			}
			else
			{
				*itVal = MIN(*itVal, dbtmp);
			}
			break;
		case FSI_AVERAGE:
		default:
			ASSERT(FALSE);	//	这是个悲剧
			break;
		}
	}
}

void KSmartTips::AddToResult(const alg::ExecToken* pToken, DOUBLE_VEC& valVec, 
							 BOOL_VEC& errVec, bool bFirstGp)
{
	
	if (!alg::const_matrix_token_assist::is_type(pToken))
	{
		return;
	}	   

	// 一遇到ErrorToken errVec就被置为true // average现在不计算
	alg::const_matrix_token_assist cmta(pToken);
	ASSERT(MAX_OF_FSI == cmta.get_width());

	DOUBLE_IT valIt = valVec.begin();
	BOOL_IT errIt = errVec.begin();

	GetTokenValue(cmta.get_item(FSI_COUNT, 0), FSI_COUNT, valIt + FSI_COUNT, errIt + FSI_COUNT);
	GetTokenValue(cmta.get_item(FSI_NUMCOUNT, 0), FSI_NUMCOUNT,
		valIt + FSI_NUMCOUNT, errIt + FSI_NUMCOUNT);
	GetTokenValue(cmta.get_item(FSI_MAX, 0), FSI_MAX, valIt + FSI_MAX, errIt + FSI_MAX, bFirstGp);
	GetTokenValue(cmta.get_item(FSI_MIN, 0), FSI_MIN, valIt + FSI_MIN, errIt + FSI_MIN, bFirstGp);
	GetTokenValue(cmta.get_item(FSI_SUM, 0), FSI_SUM, valIt + FSI_SUM, errIt + FSI_SUM);
}

HRESULT KSmartTips::GetTipValue(IBookOp* pBookOp, IDX iSheet, range_helper::ranges& ranges,
								DOUBLE_VEC& valVec, BOOL_VEC& errVec)
{
	ks_stdptr<IBook> spBook;
	pBookOp->GetBook(&spBook);
	INT sheetCount = 0;
	spBook->GetSheetCount(&sheetCount);
	if (iSheet >= sheetCount)
		return S_OK;

	KCOMPTR(ISheet) ptrSheet;
	pBookOp->GetSheet(iSheet, &ptrSheet);
	if (ptrSheet == NULL)
	{
		return S_OK;
	}
	
	RANGE rgUsed = *ranges[0].second;
	rgUsed.SetRowFromTo(
		RCB_UNION_LT(ptrSheet->GetUsedTopHdr(), ptrSheet->GetTop()), 
		RCB_UNION_RB(ptrSheet->GetUsedBottomHdr(), ptrSheet->GetBottom())
		);

	rgUsed.SetColFromTo(
		RCB_UNION_LT(ptrSheet->GetUsedLeftHdr(), ptrSheet->GetLeft()),
		RCB_UNION_RB(ptrSheet->GetUsedRightHdr(), ptrSheet->GetRight())
		);

	if (rgUsed.RowFrom() < 0)
	{
		return S_OK;
	}
	
	using namespace alg;
	DWORD dwFlag =	(ETREF_REGION | ETRA_LEFT | ETRA_TOP | ETRA_RIGHT | ETRA_BOTTOM);
	HRESULT hr;
	int nSize = ranges.size();
	bool bFirstGroup = true;
	int maxFuncNum = rgUsed.GetBMP()->cntFuncParam;
	size_t idx = 0;
	while(nSize > 0)
	{
		KCOMPTR(IFormula)	ptrFormula;
		hr = pBookOp->CreateFormula(&ptrFormula);
		KS_CHECK(hr);
		hr = ptrFormula->BeginBuildForStatus(FALSE);
		KS_CHECK(hr);

		managed_token_assist tokenResult;
		int nGroup = (nSize > maxFuncNum) ? maxFuncNum : nSize;
		nSize -= maxFuncNum;
		int nCount = 0;
		for (int i = 0; i < nGroup; ++i)
		{
			RANGE rg = *ranges[idx++].second;
			rg = rg.Intersect(rgUsed);
			if (rtUnknown == rg.RangeType())
				continue;
			managed_stref_token_assist tokenRef;
			tokenRef.create(dwFlag, ET_RVA_NONE);
			tokenRef.make_region(0, rg.SheetFrom(), rg.SheetTo(), rg.RowFrom(),rg.RowTo(), 
				rg.ColFrom(), rg.ColTo(), TRUE, TRUE, TRUE, TRUE, TRUE);
			hr = ptrFormula->AddFuncCallArgument(tokenRef);
			KS_CHECK(hr);
			++nCount;
		}
		if (0 == nCount)
			goto KS_EXIT;

		hr = ptrFormula->EndBuildForStatus(&tokenResult);
		KS_CHECK(hr);
		// 保存中间结果
		AddToResult(tokenResult.tag(), valVec, errVec, bFirstGroup);
		bFirstGroup = false;
	}

	// 求平均值
	errVec.at(FSI_AVERAGE) = errVec.at(FSI_SUM);
	if (!errVec.at(FSI_AVERAGE) && valVec.at(FSI_NUMCOUNT) > 0)
	{
		valVec.at(FSI_AVERAGE) = valVec.at(FSI_SUM) /  valVec.at(FSI_NUMCOUNT);
	}

KS_EXIT:
	return hr;
}

HRESULT KSmartTips::FormatToken( double dValue, ResultMode rm, 
								 bool b1904,
								ks_wstring& result,	BOOL bCheck /*= TRUE*/ )
{
	result.clear();
	
	// 检查是否需要格式化为读数
	switch (rm)
	{
	case rm_cnRead:
		return Format_cnRead(dValue, result);

	case rm_Comma:
		return Format_Comma(dValue, b1904, result);

	case rm_cnComma:
		return Format_cnComma(dValue, result);

	case rm_Normal:
		return Format_Normal(dValue, b1904, result);

	case rm_jpRead:
		return Format_jpRead(dValue, b1904, result);

	case rm_ActiveCellBased:		//用当前活动单元格的格式
		return Format_ActiveCellBased(dValue, b1904, result);

	default:
		ASSERT(FALSE);
	}

	return S_OK;
}

HRESULT KSmartTips::Format_cnRead(double dValue, ks_wstring& result)
{
	if (!(fabs(dValue) > 0))
	{
		result.append(__X("0"));
		return S_FALSE;
	}

	long lExponent = (long)(14 - log10(fabs(dValue)));
	if (lExponent > 308 || lExponent < -295)
	{
		return S_FALSE;
	}
	if (lExponent > 0)
		dValue = alg::ETDOUBLE(dValue).Round(lExponent).Get();
	// 常规转换
	WCHAR wcsText[512] = {0};
#ifdef X_CPU_MIPS
	kfc::nf::_ftot(dValue, (lExponent > 0 ? lExponent : 15), wcsText, 500, false, true, 0x05, NULL, true);
#else
	kfc::nf::_ftot(dValue, 15, wcsText, 500, false, true, 0x05, NULL, true);
#endif
	long lLen = xstrlen(wcsText);
	if (lLen <= 0)
		return S_FALSE;

	// 现在简体中文、繁体中文和日文版都走这个流程，其它版本暂无此功能  by fangyukuan 2010.04.12
	const WCHAR cswchSimbel_1 = TX_SmartTip_dian[0];	// 点(0x70B9)
	const WCHAR cswchSimbel_2 = TX_SmartTip_wan[0];	// 万(0x4E07)
	const WCHAR cswchSimbel_3 = TX_SmartTip_yi[0];	// 亿
	constexpr WCHAR cswchSimbel_4 = 0x5146; // 兆
	// 转换开始
	// 规则:
	//   四分位，小数点右起，单位：点万亿兆 （万亿兆循环）
	//   万亿左边四位都是零的时候省略，兆不省。
	//   四分位中千分位起连续的0省略。

	// 0 先处理负号
	const WCHAR* pcwcsText = wcsText;
	if (L'-' == wcsText[0])
	{
		result.append(1, L'-');
		pcwcsText = wcsText + 1;
		--lLen;
	}

	// 1 先找小数点
	const WCHAR* pcwcsPoint = xstrrchr(pcwcsText, (WCHAR)'.');	// 小数点的位置
	long lDigit = lLen;
	if (NULL != pcwcsPoint)		// 有小数点
		lDigit = pcwcsPoint - pcwcsText;

	result.reserve(lLen);
	const WCHAR* pcwcs = pcwcsText;
	long lSectLen = lDigit % 12;
	long lSectCount = lDigit / 12;
	while (lSectCount >= 0)
	{
		BOOL bSect = lSectLen != 0;
		int nZeroCount = 0;

		if (lSectLen > 8)
		{
			if (IsAppDecimals(pcwcs, lSectLen, 8))
			{
				result.append(pcwcs, lSectLen - 8);
				result.append(1, cswchSimbel_3);
			}
			pcwcs += lSectLen - 8;
			lSectLen = 8;
		}
		
		if (lSectLen > 4)
		{
			if (IsAppDecimals(pcwcs, lSectLen, 4))
			{
				result.append(pcwcs, lSectLen - 4);
				result.append(1, cswchSimbel_2);

			}
			pcwcs += lSectLen - 4;
			lSectLen = 4;
		}
		
		if (lSectLen > 0)
		{
			if (IsAppDecimals(pcwcs, lSectLen, 0))
			{
				result.append(pcwcs - nZeroCount, lSectLen);
				pcwcs += lSectLen;
				lSectLen = 0;
			}
		}
		
		if (lSectCount > 0 && bSect)// && (lDigit % 12 != 0))
			result.append(1, cswchSimbel_4);
		--lSectCount;
		lSectLen = 12;
	}

	if (NULL != pcwcsPoint)
	{
		if (fabs(dValue) < 1)
			result.append(__X("0"));

		result.append(1, cswchSimbel_1);
		result.append(pcwcsPoint + 1);
	}
	return S_OK;
}

//是否要加位数szDigitTest 数字字符串， nLen 数字的长度，nSaveLen 判断的保留长度
//如果在 [nSaveLen，nLen）范围有不为0的，就返回true，否则返回false,
bool KSmartTips::IsAppDecimals(LPCWSTR szDigitTest, int nLen, int nSaveLen)
{
	while (*szDigitTest == L'0' && nLen > nSaveLen)
	{
		++szDigitTest;
		--nLen;
	}
	return nLen != nSaveLen;
}

HRESULT KSmartTips::Format_Comma(double dValue, bool b1904, ks_wstring& result)
{
	if (!(fabs(dValue) > 0))
	{
		result.append(__X("0"));
		return S_FALSE;
	}
	long lExponent = (long)(14 - log10(fabs(dValue)));
	if (lExponent > 308 || lExponent < -295)
	{
		return S_FALSE;
	}
	if (lExponent > 0)
		dValue = alg::ETDOUBLE(dValue).Round(lExponent).Get();

	kfc::nf::NFHANDLE nfh = NULL;
	alg::ETDOUBLE etdValue(dValue);
	if (etdValue.Floor() == dValue)		// 整数
	{
		if (NULL == m_nfhComma)
			kfc::nf::_XNFCompile(__X("#,##0"), &m_nfhComma, NULL);
		nfh = m_nfhComma;
	}
	else
	{
		if (NULL == m_nfhComma2)
			kfc::nf::_XNFCompile(__X("#,##0.0##############"), &m_nfhComma2, NULL);
		nfh = m_nfhComma2;
	}
	if (NULL == nfh)
		return S_OK;

	FormatNumber(dValue, nfh, b1904, result);

	return S_OK;
}

HRESULT KSmartTips::Format_cnComma(double dValue, ks_wstring& result)
{
	if (!(fabs(dValue) > 0))
	{
		result.append(__X("0"));
		return S_FALSE;
	}
	long lExponent = (long)(14 - log10(fabs(dValue)));
	if (lExponent > 308 || lExponent < -295)
	{
		return S_FALSE;
	}
	if (lExponent > 0)
		dValue = alg::ETDOUBLE(dValue).Round(lExponent).Get();
	// 常规转换
	WCHAR wcsText[512] = {0};
	kfc::nf::_ftot(dValue, 15, wcsText, 500, false, true, 0x05, NULL, true);
	long lLen = xstrlen(wcsText);
	if (lLen <= 0)
		return S_FALSE;

	// 转换开始
	// 规则: 从右向左每4位中插入空格

	// 0 先处理负号
	const WCHAR* pcwcsText = wcsText;
	if (L'-' == wcsText[0])
	{
		result.append(1, L'-');
		pcwcsText = wcsText + 1;
		--lLen;
	}
	
	// 1 先找小数点
	const WCHAR* pcwcsPoint = xstrrchr(pcwcsText, (WCHAR)'.');	// 小数点的位置
	long lDigit = lLen;
	if (NULL != pcwcsPoint)		// 有小数点
		lDigit = pcwcsPoint - pcwcsText;
	ASSERT(lDigit > 0);
	if (0 == lDigit)
		return S_FALSE;

	long lSectLen = lDigit % 4;
	if (0 == lSectLen)
		lSectLen = 4;
	const WCHAR* pcwcs = pcwcsText;
	result.append(pcwcs, lSectLen);
	pcwcs += lSectLen;
	lDigit -= lSectLen;

	ASSERT((lDigit % 4) == 0);
	while (lDigit > 0)
	{
		result.append(__X(" "));
		result.append(pcwcs, 4);
		pcwcs += 4;
		lDigit -= 4;
	}
	
	if (NULL != pcwcsPoint)
	{
		result.append(pcwcsPoint);
	}
	return S_OK;
}

HRESULT KSmartTips::Format_Normal(double dValue, bool b1904, ks_wstring& result)
{
	if (NULL == m_nfhNormal)
		kfc::nf::_XNFCompile(__X("General"), &m_nfhNormal, NULL);
	if (NULL == m_nfhNormal)
		return S_OK;
	FormatNumber(dValue, m_nfhNormal, b1904, result);

	return S_OK;
}

//以当前活动单元格格式格式化数据。
HRESULT KSmartTips::Format_ActiveCellBased(double dValue, bool b1904, ks_wstring& result)
{
	ks_stdptr<IBook> spBook= _GetActiveBook();
	if (!spBook)
		return S_FALSE;

	KCOMPTR(IBookOp) spBookOp;
	spBook->GetOperator(&spBookOp);

	ks_stdptr<IKCoreObject> spDispatch;
	m_spApiApp->get_ActiveSheet(&spDispatch);
	ASSERT(spDispatch);

	ks_stdptr<_Worksheet> spWorksheet;
	spDispatch->QI(_Worksheet, &spWorksheet);
	ASSERT(spWorksheet);

	long apiSheetIdx = 0;
	spWorksheet->get_Index(0, &apiSheetIdx);
 	IDX iActiveWorksheet = --apiSheetIdx;
	ks_stdptr<IKWorksheet> spWorksheetInfo;
	spWorksheet->QI(IKWorksheet, &spWorksheetInfo);
	ASSERT(spWorksheetInfo);

	ks_stdptr<ISheetWndInfos> spSheetWndInfos = spWorksheetInfo->GetWndInfos();
	ASSERT(spSheetWndInfos);

	CELL cell = {0};
	spSheetWndInfos->GetActiveCell(0, cell);
	const XF* pXF = NULL;
	spBookOp->GetCellFormat(iActiveWorksheet, cell.row, cell.col, &pXF, NULL);

	kfc::nf::NF_INFO nfInfo;
	ZeroMemory(&nfInfo, sizeof(nfInfo));
	kfc::nf::_XNF_GetSectsInfo(pXF->pNumFmt->hfmt, &nfInfo);

	if (kfc::nf::FMT_TYPE_TEXT == nfInfo.SectInfos[0].FmtType)
	{
		Format_Normal(dValue, b1904, result);
	}
	else
	{
		FormatNumber(dValue, pXF->pNumFmt->hfmt, b1904, result);
	}

	return S_OK;
}

//通过格式句柄来格式化double数据，并转换为字符串添加在result中。
HRESULT KSmartTips::FormatNumber(double dValue, kfc::nf::NFHANDLE hFormat, bool b1904, ks_wstring& result)
{
	VARIANT var = {0};
	V_VT(&var) = VT_R8;
	V_R8(&var) = dValue;
	KComBSTR bs;
	
	// ET 纯通用格式
	const long GEN_ET_NORMAL_LENTH	= 12;
	kfc::nf::NF_FORMAT_PARAM param;
	param.lMaxcch = GEN_ET_NORMAL_LENTH;

	kfc::nf::_XNFFormatEx2(var, b1904, hFormat, &bs, NULL, &param);
	if (bs.Length() > 0)
		result.append(bs);

	return S_OK;
}

HRESULT KSmartTips::Format_jpRead(double dValue, bool b1904, ks_wstring& result)
{
	if (!(fabs(dValue) > 0))
	{
		result.append(__X("0"));
		return S_FALSE;
	}
	long lExponent = (long)(14 - log10(fabs(dValue)));
	if (lExponent > 308 || lExponent < -295)
	{
		return S_FALSE;
	}
	if (lExponent > 0)
		dValue = alg::ETDOUBLE(dValue).Round(lExponent).Get();
	
	kfc::nf::NFHANDLE nfh = NULL;
	alg::ETDOUBLE etdValue(dValue);
	if (etdValue.Floor() == dValue)		// 整数
	{
		if (NULL == m_nfhJPRead1)
			kfc::nf::_XNFCompile(kfc::nf::_XNFGetEtStr(Et_NF_AUTOCONVERSION_WITHOUTPOINT), &m_nfhJPRead1, NULL);
		nfh = m_nfhJPRead1;
	}
	else
	{
		if (NULL == m_nfhJPRead2)
			kfc::nf::_XNFCompile(kfc::nf::_XNFGetEtStr(Et_NF_AUTOCONVERSION_WITHPOINT), &m_nfhJPRead2, NULL);
		nfh = m_nfhJPRead2;
	}
	if (NULL == nfh)
		return S_OK;

	FormatNumber(dValue, nfh, b1904, result);
	return S_OK;

}

UINT KSmartTips::_GetVisibleTypes()
{
	ASSERT(m_spEtApp);
	UINT nType = et_applogic::stsNone;

	ks_stdptr<IAppSettings> spAppSetting = m_spEtApp->GetAppSettings();
	if (spAppSetting)
		nType = spAppSetting->GetSmartTipsVisibleTypes();

	return nType;
}

UINT KSmartTips::_GetTipsStyle()
{
	ASSERT(m_spEtApp);
	UINT nStyle = et_applogic::stsNone;

	ks_stdptr<IAppSettings> spAppSetting = m_spEtApp->GetAppSettings();
	if (spAppSetting)
		nStyle = spAppSetting->GetSmartTipsStyleShow();

	return nStyle;
}

KSmartTips::ResultMode KSmartTips::_GetTipsMode()
{
	switch(_GetTipsStyle())
	{
	case et_applogic::stsChinese:
		return rm_cnRead;
	case et_applogic::stsThousand:
		return rm_Comma;
	case et_applogic::stsTenThousand:
		return rm_cnComma;
	case et_applogic::stsActiveCellBase:
		return rm_ActiveCellBased;
	default:
		return rm_Normal;
	}
}

IBook* KSmartTips::_GetActiveBook()
{
	ks_stdptr<_Workbook> spWorkbook;
	m_spApiApp->get_ActiveWorkbook(&spWorkbook);
	if (!spWorkbook)
		return NULL;

	ks_stdptr<IKWorkbook> spWrokBookInfo;
	spWorkbook->QI(IKWorkbook, &spWrokBookInfo);
	if (!spWrokBookInfo)
		return NULL;

	return spWrokBookInfo->GetBook();
}
// -------------------------------------------------------------------------
