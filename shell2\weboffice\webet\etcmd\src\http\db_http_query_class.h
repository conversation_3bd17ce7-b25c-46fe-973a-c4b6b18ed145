﻿#ifndef __WEBET_DB_HTTP_QUERY_CLASS_H__
#define __WEBET_DB_HTTP_QUERY_CLASS_H__

#include "et_query_class.h"
#include "appcore/et_appcore_dbsheet.h"
#include "dbsheet/et_dbsheet_common_helper.h"

namespace wo
{

class DbHttpQueryClassBase : public EtQueryExecBase
{
public:
    DbHttpQueryClassBase(wo::KEtWorkbook *wwb, PCWSTR tag);

    virtual HRESULT PreExecute(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
    virtual HRESULT PostExecute(HRESULT hr, const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
    HRESULT listRecordsExec(UINT sheetStId, const binary_wo::VarObj& param, KEtRevisionContext* ctx, DBSheetCommonHelper& commonHelper,
                            wo::KEtWorkbook* wwb, IDBSheetCtx* pDbCtx, ks_wstring& errMsg,bool byOffset = true, const IDBIds *pRecords = nullptr);

    IDBSheetCtx* m_pDbCtx;
    IEncodeDecoder* m_pEncodeDecoder;
    DBSheetCommonHelper m_commonHelper;
	ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
	ks_wstring m_errMsg;
};

class DbHttpInquireParentRecordTaskClass : public DbHttpQueryClassBase
{
public:
    DbHttpInquireParentRecordTaskClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpIsEnableParentRecordTaskClass : public DbHttpQueryClassBase
{
public:
    DbHttpIsEnableParentRecordTaskClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpListRecordsQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpListRecordsQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpListRecordsByPageQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpListRecordsByPageQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpRetrieveRecordQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpRetrieveRecordQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpRetrieveRecordsQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpRetrieveRecordsQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpPermissionQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpPermissionQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class DbHttpPermissionListSchemaQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpPermissionListSchemaQueryClass(wo::KEtWorkbook *);
public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class DbHttpAttachmentPermissionQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpAttachmentPermissionQueryClass(wo::KEtWorkbook *);
public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class DbHttpBaseSchemaQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpBaseSchemaQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpRetrieveStatisticsResultQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpRetrieveStatisticsResultQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
private:
    void SerialiseStatisticResult(int, ISerialAcceptor *, IDbStatisticsResult *, IDBSheetView *, bool, IDbFieldsManager *);
};

// 开发者接口. 不对外暴露
class DbHttpDevRetrieveFormQueryClass: public DbHttpQueryClassBase
{
public:
    DbHttpDevRetrieveFormQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override { return S_OK; } // 开发者接口. 不进行权限检查
};

// 开发者接口. 不对外暴露
class DbHttpDevListRecordsQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpDevListRecordsQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override { return S_OK; } // 开发者接口. 不进行权限检查
};

class DbHttpListSheetsQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpListSheetsQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpListWebExtensionsQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpListWebExtensionsQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpGetDashboardChartResultQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpGetDashboardChartResultQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

class DbHttpGetFilterValuesListQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpGetFilterValuesListQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
private:
	WebInt m_version = 0;
};

class DbHttpGetFilterValuesQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpGetFilterValuesQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

// 仅供内部使用，不对外
class DbHttpGetServerRenderDataQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpGetServerRenderDataQueryClass(wo::KEtWorkbook *);
public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override;
};

class DbHttpFieldBaseSchemaQueryClass : public DbHttpQueryClassBase
{
public:
    DbHttpFieldBaseSchemaQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
};

} // wo

#endif // __WEBET_DB_HTTP_QUERY_CLASS_H__