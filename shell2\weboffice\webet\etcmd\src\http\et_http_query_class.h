#ifndef __WEBET_ET_HTTP_QUERY_CLASS_H__
#define __WEBET_ET_HTTP_QUERY_CLASS_H__

#include "et_query_class.h"
#include "appcore/et_appcore_et_filter.h"

class KSerialWrapBinWriter;

namespace wo
{

bool ExportShape(drawing::AbstractShape* spShape, ISerialAcceptor* pAcpt, int16 dpi, bool isForCellPic);

struct OptionColContextItem
{
    std::map<ks_wstring, int> map;
    std::vector<ks_wstring> vec;
    std::vector<ks_wstring> originTextVec;
};

class OptionColContext 
{

public:
	OptionColContext(std::vector<int>* optionCols, RANGE* rgFilter, int start, int end) 
        : m_optionCols(optionCols), m_rgFilter(rgFilter)
        , m_start(start), m_end(end)
	{}

private:
    RANGE* m_rgFilter;
	std::vector<int>* m_optionCols;
    int m_start;
    int m_end;

    std::map<int, std::unique_ptr<OptionColContextItem>> m_colMap;
    
public:
    void PutColData(int col, const ks_wstring& text, const ks_wstring& originText);
    
    void Serialize(KSerialWrapBinWriter *acpt, int count);

};

class EtHttpQueryClassBase : public EtQueryExecBase
{
public:
    EtHttpQueryClassBase(wo::KEtWorkbook *wwb, PCWSTR tag)
        : EtQueryExecBase(wwb, tag)
    {}

    virtual HRESULT PreExecute(const VarObj& param, KEtRevisionContext* ctx);
    virtual HRESULT PostExecute(HRESULT hr, const binary_wo::VarObj& param, KEtRevisionContext* ctx);
protected:
    HRESULT CheckDBSheet(IDX sheetIdx);
    ks_wstring m_errMsg;
};

class EtHttpSheetsInfoQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpSheetsInfoQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class EtHttpRangeDataQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpRangeDataQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }

private:
    void ExportShapeData(IKWorksheet* pWorksheet, ISerialAcceptor* pAcpt, const RANGE& rg, KEtRevisionContext* pCtx);

};

class EtHttpDefinedNameQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpDefinedNameQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};


class EtHttpRetrieveRecordQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpRetrieveRecordQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    // 在Exec输出单元格时已经有权限判断
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }

private:
    std::set<int> removeDuplicates(std::set<int>& rows, ISheet* pSheet, std::vector<long>& cols, const RANGE& rgFilter);

};

class EtHttpGetColTypeQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpGetColTypeQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    // 在Exec已经有权限判断
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class KEtExtraResult : public IKEtExtraResult
{

public:
    KEtExtraResult(std::set<int>* result) : m_result(result) {}
    STDPROC_(void) result(int i) override;

private:
    std::set<int>* m_result;

};


class EtHttpTitleContentQueryClass : public EtHttpQueryClassBase
{
public:
    explicit EtHttpTitleContentQueryClass(wo::KEtWorkbook *);
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
    void WriteTitleContent(IN KEtRevisionContext*, IN ISerialAcceptor*, IN IDX, IN int);
};

class EtHttpDocumentInfoQueryClass : public EtHttpQueryClassBase
{
public:
    explicit EtHttpDocumentInfoQueryClass(wo::KEtWorkbook *);
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
    void WriteCommentChains(IWoComment* pWoComment, bool bIsResolved, ISerialAcceptor* acpt, int& totalCnt);
    void WriteCommentInfo(ISheet* pSheet, ISerialAcceptor* acpt, int& totalCnt);
};

// 仅内部使用，不对用户开放
class EtHttpAiRecognizedInfoQueryClass : public EtHttpQueryClassBase
{
public:
	explicit EtHttpAiRecognizedInfoQueryClass(wo::KEtWorkbook*);

	HRESULT Exec(const binary_wo::VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

// 仅内部使用，不对用户开放
class EtHttpGetTitleContentByAIQueryClass : public EtHttpQueryClassBase
{
public:
	explicit EtHttpGetTitleContentByAIQueryClass(wo::KEtWorkbook*);

	HRESULT Exec(const binary_wo::VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};


// 目前仅内部使用，不对用户开放
class EtHttpIdentifyFieldTypeQueryClass : public EtHttpQueryClassBase
{
public:
	explicit EtHttpIdentifyFieldTypeQueryClass(wo::KEtWorkbook*);

	HRESULT Exec(const binary_wo::VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

// 判断区域是否是单元格图片
class EtHttpJudgeRangeIsPicQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpJudgeRangeIsPicQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    // 在Exec已经有权限判断
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class EtHttpGetQRColQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpGetQRColQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
private:
    ROW GetFirstEmptyRow(KEtRevisionContext* ctx, ISheet* pSheet, COL keyCol, ROW headerRow);
};

class EtHttpFindFirstEmptyColQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpFindFirstEmptyColQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class EtHttpFindQRKeyColQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpFindQRKeyColQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
private:
    COL FindKeyCol(ISheet* pSheet, const RANGE& range, KEtRevisionContext* pCtx, UINT uniquePercent, const std::vector<ks_wstring>* pMatchingHeaderNames);
    bool ShouldExcludeCol(KEtRevisionContext* pCtx, ISheet* pSheet, const RANGE& colRange);
};

class EtHttpColsInfoQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpColsInfoQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
private:
    static PCWSTR GetValueTypeStr(ICellImages* pCellImages, const_token_ptr pToken);
};

class EtHttpCopyRangeDataClass : public EtHttpQueryClassBase
{
public:
    EtHttpCopyRangeDataClass(wo::KEtWorkbook*);
public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*) override;
    // 在Exec已经有权限判断
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
private:
    enum Format
    {
        html, text
    };
};

class EtHttpGetRangeQRLabelCountQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpGetRangeQRLabelCountQueryClass(wo::KEtWorkbook *);
public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class EtHttpExecuteQueryApiClass : public EtHttpQueryClassBase
{
public:
    EtHttpExecuteQueryApiClass(wo::KEtWorkbook *);
public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class EtHttpGetLocalImagesSha1Class : public EtHttpQueryClassBase
{
public:
    EtHttpGetLocalImagesSha1Class(wo::KEtWorkbook*);
public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
private:
    QString GetImageSha1(ICellImages* pCellImages, PCWSTR imageName, qreal dpi, KEtRevisionContext* ctx);
};

class EtHttpGetFieldInfoQueryClass : public EtHttpQueryClassBase
{
public:
    explicit EtHttpGetFieldInfoQueryClass(wo::KEtWorkbook *);
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT WriteFieldInfo(IN KEtRevisionContext*, IN ISerialAcceptor*, IN IDX, IN int&, PCWSTR pcwDefaultName);
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
private:
    HRESULT genFieldInfo(ISerialAcceptor* acpt, PCWSTR pcwDefaultName);
};

class EtHttpGetHeaderInfoQueryClass : public EtHttpQueryClassBase
{
public:
    explicit EtHttpGetHeaderInfoQueryClass(wo::KEtWorkbook *);
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT WriteHeaderInfo(IN KEtRevisionContext*, IN ISerialAcceptor*, IN IDX);
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};
class EtHttpRangeMatrixQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpRangeMatrixQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }};

class EtHttpDataAnalyzeMergeInfoQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpDataAnalyzeMergeInfoQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class EtHttpDataAnalyzeMergeLogQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpDataAnalyzeMergeLogQueryClass(wo::KEtWorkbook *);

public:
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class EtHttpCheckHasHiddenQueryClass : public EtHttpQueryClassBase
{
public:
    EtHttpCheckHasHiddenQueryClass(wo::KEtWorkbook *);
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};

class EtHttpQueryProtectionInfoClass : public EtHttpQueryClassBase
{
public:
    EtHttpQueryProtectionInfoClass(wo::KEtWorkbook *);
    virtual HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; //　拷贝代码时请确认当前命令是否不需要前置权限校验，或已经在执行逻辑内进行了权限校验
    }
};
} // namespace wo

#endif // __WEBET_ET_HTTP_QUERY_CLASS_H__
