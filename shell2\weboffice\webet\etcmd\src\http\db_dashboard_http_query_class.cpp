﻿#include "etstdafx.h"
#include "db_dashboard_http_query_class.h"
#include "helpers/varobject_helper.h"
#include "src/workbook.h"
#include "src/et_revision_context_impl.h"
#include "webbase/serialize_impl.h"
#include "webbase/wo_sa_helper.h"
#include "appcore/et_appcore_shared_link_sheet.h"
#include "dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h"
#include "dbsheet/dashboard/db_chart_wrapper.h"
#include "appcore/et_appcore_enum.h"

namespace wo
{

// ================== DbDashboardHttpListChartsQueryClass ==================
DbDashboardHttpListChartsQueryClass::DbDashboardHttpListChartsQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.dashboard.listCharts"))
{
}

HRESULT DbDashboardHttpListChartsQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, pCtx);
    sa::Leave chartsLeave(sa::enterArray(&acpt, "charts"));
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    hr = dashboardModuleMgrWrapper.EnumChart([&](KDbChartWrapper* pChartWrapper) {
        sa::Leave leave(sa::enterStruct(&acpt, nullptr));
        pChartWrapper->SerializeBaseInfo(&acpt);
        return S_OK;
    });
    if (FAILED(hr))
        return hr;
    return S_OK;
}

HRESULT DbDashboardHttpListChartsQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
}

// ================== DbDashboardHttpGetChartsDataQueryClass ==================
DbDashboardHttpGetChartsDataQueryClass::DbDashboardHttpGetChartsDataQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.dashboard.getChartsData"))
{
}

HRESULT DbDashboardHttpGetChartsDataQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, pCtx);
    sa::Leave chartsLeave(sa::enterArray(&acpt, "charts"));
    VAR_OBJ_EXPECT_STRUCT(param, "lang")
    binary_wo::VarObj lang = param.get_s("lang");
    VAR_OBJ_EXPECT_ARRAY(param, "charts")
    binary_wo::VarObj charts = param.get_s("charts");
    int32 chartCount = charts.arrayLength_s();
    for (int32 i = 0; i < chartCount; ++i)
    {
        sa::Leave leave(sa::enterStruct(&acpt, nullptr));
        binary_wo::VarObj item = charts.at(i);
        PCWSTR chartId = item.field_str("id");
        std::unique_ptr<KDbChartWrapper> pChartWrapper = dashboardModuleMgrWrapper.GetChart(chartId);
        if (!pChartWrapper)
        {
            acpt.addString("id", chartId);
            acpt.addBool("valid", false);
            continue;
        }
        if (pChartWrapper->SerializeBaseInfo(&acpt))
        {
            {
                sa::Leave configLeave(sa::enterStruct(&acpt, "config"));
                pChartWrapper->SerializeConfig(&acpt);
            }
            {
                sa::Leave resultLeave(sa::enterStruct(&acpt, "result"));
                hr = pChartWrapper->SerializeContent(item, lang, pCtx, &acpt);
                if (FAILED(hr))
                {
                    PCWSTR resultTypeStr = __X("");
                    VS(_appcore_GainEncodeDecoder()->EncodeDbChartResultType(DbSheet_Chart_RT_Invalid, &resultTypeStr));
                    acpt.addString("type", resultTypeStr);
                }
            }
        }
    }
    return S_OK;
}

HRESULT DbDashboardHttpGetChartsDataQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
}

// ================== DbChartHttpListRecordsQueryClass ==================
DbChartHttpListRecordsQueryClass::DbChartHttpListRecordsQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.chart.listRecords"))
{
}

HRESULT DbChartHttpListRecordsQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    if (!param.has("chartId"))
        return E_INVALIDARG;

    PCWSTR chartId = param.field_str("chartId");
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    std::unique_ptr<KDbChartWrapper> pChartWrapper = dashboardModuleMgrWrapper.GetChart(chartId);
    if (!pChartWrapper)
        return E_FAIL;

    UINT dataSourceSheetId = pChartWrapper->GetDataSourceSheetId();
    if (dataSourceSheetId == 0)
        return E_FAIL;

    ks_stdptr<IDBIds> spRecords;
    if (param.has("categoryGroupKey"))
    {
        binary_wo::VarObj categoryGroupKey = param.get_s("categoryGroupKey");
        binary_wo::VarObj seriesGroupKey = param.get_s("seriesGroupKey");
        hr = pChartWrapper->GetGroupRecords(categoryGroupKey, seriesGroupKey, &spRecords);
    }
    else
    {
        hr = pChartWrapper->GetVisibleRecords(&spRecords);
    }
    if (FAILED(hr))
        return hr;
    return listRecordsExec(dataSourceSheetId, param, pCtx, m_commonHelper, m_wwb, m_pDbCtx, m_errMsg, true, spRecords);
}

HRESULT DbChartHttpListRecordsQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
}

// ================== DbDashboardHttpListFiltersQueryClass ==================
DbDashboardHttpListFiltersQueryClass::DbDashboardHttpListFiltersQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.dashboard.listFilters"))
{
}

HRESULT DbDashboardHttpListFiltersQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALID_REQUEST;

    binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, pCtx);
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    dashboardModuleMgrWrapper.SerializeFilters("filters", &acpt);
    return S_OK;
}

HRESULT DbDashboardHttpListFiltersQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
}

}