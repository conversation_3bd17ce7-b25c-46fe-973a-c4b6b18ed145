﻿#include "etstdafx.h"
#include "et_task_detail_xf.h"
#include "et_binvar_spec.h"
#include "workbook.h"

namespace wo
{
void DecodeColor(const binary_wo::VarObj& vClr, EtColor& clr)
{
	ETCOLORTYPE type = (ETCOLORTYPE)vClr.field_uint32("type");

	binary_wo::VarObj vTint = vClr.get_s("tint");
	double tnt = 0.0;
	if (vTint.type() != binary_wo::typeInvalid)
		tnt = vTint.value_double();

	switch (type)
	{
	case ectICV:
		clr.setICV(vClr.field_int32("value"), tnt);
		break;
	case ectTHEME:
		clr.setTheme((ETTHEMETYPE)vClr.field_int32("value"), tnt);
		break;
	case ectARGB:
		clr.setARGB(vClr.field_uint32("value"), tnt);
		break;
	case ectEMPTY:
		clr.setEMPTY();
		break;
	case ectNONE:
		clr.setNONE(tnt);
		break;
	case ectAUTO:
		clr.setAUTO(tnt);
		break;
	}
}

void EncodeColor(const EtColor& clr, WebName name, binary_wo::VarObj& resObj)
{
	binary_wo::VarObj vClr = resObj.add_field_struct(name);
	vClr.add_field_uint32("type", (DWORD)clr.getType());
	DWORD value = 0;
	switch (clr.getType())
	{
	case ectICV:
		value = clr.getICV();
		break;
	case ectTHEME:
		value = clr.getTheme();
		break;
	case ectARGB:
		value = clr.getARGB();
		break;
	case ectAUTO:
	case ectNONE:
		break;
	}
	vClr.add_field_double("tint", clr.getTint());
	vClr.add_field_uint32("value", value);
}

void DecodeFill(const binary_wo::VarObj& vFill, EtFill& fill, KXFMASK& mask)
{
	ETFILLTYPE type = (ETFILLTYPE)vFill.field_uint32("type");
	fill.setType(type);
	mask.inc_eft = 1;
	if (vFill.has("back"))
	{
		binary_wo::VarObj vClrBack = vFill.get_s("back");
		EtColor clr;
		DecodeColor(vClrBack, clr);
		fill.setBack(clr);
		mask.inc_clrBack = 1;
	}

	if (vFill.has("fore"))
	{
		binary_wo::VarObj vClrFore = vFill.get_s("fore");
		EtColor clr;
		DecodeColor(vClrFore, clr);
		fill.setFore(clr);
		mask.inc_clrFore = 1;
	}

	if (vFill.has("degree"))
	{
		double degree = vFill.field_double("degree");
		fill.setDegree(degree);
	}

	if (vFill.has("top"))
	{
		double top = vFill.field_double("top");
		fill.setTop(top);
	}

	if (vFill.has("left"))
	{
		double left = vFill.field_double("left");
		fill.setLeft(left);
	}

	if (vFill.has("right"))
	{
		double right = vFill.field_double("right");
		fill.setRight(right);
	}

	if (vFill.has("bottom"))
	{
		double bottom = vFill.field_double("bottom");
		fill.setBottom(bottom);
	}

	if (vFill.has("stopItems"))
	{
		mask.inc_eft = 1;
		binary_wo::VarObj stopItems = vFill.get_s("stopItems");
		if (stopItems.type() == binary_wo::typeArray)
		{
			for (int i = 0; i < stopItems.arrayLength(); i++)
			{
				binary_wo::VarObj item = stopItems.at(i);
				double pos = item.field_double("pos");
				EtColor clr;
				DecodeColor(item.get("color"), clr);
				fill.addStop(pos, clr);
			}
		}
	}
}

void DecodeIconSet(const binary_wo::VarObj& vIconSet, EtIconSet& iconSet)
{
	if (vIconSet.has("icoSet"))
	{
		WebStr set = vIconSet.field_str("icoSet");
		if (xstrcmp(set, __X("null")) == 0)
		{
			iconSet.icoSet = CFIS_None;
		}
		else if (xstrcmp(set, __X("3Arrows")) == 0)
		{
			iconSet.icoSet = CFIS_3Arrows;
		}
		else if (xstrcmp(set, __X("3ArrowsGray")) == 0)
		{
			iconSet.icoSet = CFIS_3ArrowsGray;
		}
		else if (xstrcmp(set, __X("3Flags")) == 0)
		{
			iconSet.icoSet = CFIS_3Flags;
		}
		else if (xstrcmp(set, __X("3TrafficLights1")) == 0)
		{
			iconSet.icoSet = CFIS_3TrafficLights1;
		}
		else if (xstrcmp(set, __X("3TrafficLights2")) == 0)
		{
			iconSet.icoSet = CFIS_3TrafficLights2;
		}
		else if (xstrcmp(set, __X("3Signs")) == 0)
		{
			iconSet.icoSet = CFIS_3Signs;
		}
		else if (xstrcmp(set, __X("3Symbols")) == 0)
		{
			iconSet.icoSet = CFIS_3Symbols;
		}
		else if (xstrcmp(set, __X("3Symbols2")) == 0)
		{
			iconSet.icoSet = CFIS_3Symbols2;
		}
		else if (xstrcmp(set, __X("3Stars")) == 0)
		{
			iconSet.icoSet = CFIS_3Stars;
		}
		else if (xstrcmp(set, __X("3Triangles")) == 0)
		{
			iconSet.icoSet = CFIS_3Triangles;
		}
		else if (xstrcmp(set, __X("4Arrows")) == 0)
		{
			iconSet.icoSet = CFIS_4Arrows;
		}
		else if (xstrcmp(set, __X("4ArrowsGray")) == 0)
		{
			iconSet.icoSet = CFIS_4ArrowsGray;
		}
		else if (xstrcmp(set, __X("4RedToBlack")) == 0)
		{
			iconSet.icoSet = CFIS_4RedToBlack;
		}
		else if (xstrcmp(set, __X("4CRV")) == 0)
		{
			iconSet.icoSet = CFIS_4CRV;
		}
		else if (xstrcmp(set, __X("4TrafficLights")) == 0)
		{
			iconSet.icoSet = CFIS_4TrafficLights;
		}
		else if (xstrcmp(set, __X("5Arrows")) == 0)
		{
			iconSet.icoSet = CFIS_5Arrows;
		}
		else if (xstrcmp(set, __X("5ArrowsGray")) == 0)
		{
			iconSet.icoSet = CFIS_5ArrowsGray;
		}
		else if (xstrcmp(set, __X("5CRV")) == 0)
		{
			iconSet.icoSet = CFIS_5CRV;
		}
		else if (xstrcmp(set, __X("5Quarters")) == 0)
		{
			iconSet.icoSet = CFIS_5Quarters;
		}
		else if (xstrcmp(set, __X("5Boxes")) == 0)
		{
			iconSet.icoSet = CFIS_5Boxes;
		}
	}
	if (vIconSet.has("icoIdx"))
	{
		iconSet.icoIdx = vIconSet.field_uint32("icoIdx");
	}
}

void EncodeFill(const EtFill& fill, WebName name, binary_wo::VarObj& resObj)
{
	binary_wo::VarObj vFill = resObj.add_field_struct(name);
	vFill.add_field_uint32("type", fill.getType());
	EncodeColor(fill.getBack(), "back", vFill);
	EncodeColor(fill.getFore(), "fore", vFill);
}

void DecodeFont(const binary_wo::VarObj& vFont, FONT& font, KXFMASK& mask)
{
	binary_wo::VarObj vthemeFont = vFont.get_s("themeFont");
	if (vthemeFont.type() != binary_wo::typeInvalid)
	{
		mask.inc_theme_name = 1;
		font.themeFontType = vthemeFont.value_uint32();
		font.name[0] = __Xc('\0');
	}

	binary_wo::VarObj vName = vFont.get_s("name");
	if (vName.type() != binary_wo::typeInvalid)
	{
		mask.inc_theme_name = 1;
		WebStr str = vName.value_str();
		xstrncpy_s(font.name, str, MAX_FONTNAME_CCH);
		font.name[MAX_FONTNAME_CCH] = __Xc('\0');
	}

	binary_wo::VarObj vdyHeight = vFont.get_s("dyHeight");
	if (vdyHeight.type() != binary_wo::typeInvalid)
	{
		mask.inc_dyHeight = 1;
		font.dyHeight = vdyHeight.value_uint32();
	}

	binary_wo::VarObj vcharSet = vFont.get_s("charSet");
	if (vcharSet.type() != binary_wo::typeInvalid)
	{
		mask.inc_bCharSet = 1;
		font.bCharSet = vcharSet.value_uint32();
	}

	binary_wo::VarObj vbls = vFont.get_s("bls");
	if (vbls.type() != binary_wo::typeInvalid)
	{
		mask.inc_bls = 1;
		font.bls = vbls.value_bool();
	}

	binary_wo::VarObj vitalic = vFont.get_s("italic");
	if (vitalic.type() != binary_wo::typeInvalid)
	{
		mask.inc_fItalic = 1;
		font.fItalic = vitalic.value_bool();
	}

	binary_wo::VarObj vstrikeout = vFont.get_s("strikeout");
	if (vstrikeout.type() != binary_wo::typeInvalid)
	{
		mask.inc_fStrikeout = 1;
		font.fStrikeout = vstrikeout.value_bool();
	}

	binary_wo::VarObj vuls = vFont.get_s("uls");
	if (vuls.type() != binary_wo::typeInvalid)
	{
		mask.inc_uls = 1;
		font.uls = vuls.value_uint32();
	}

	binary_wo::VarObj vsss = vFont.get_s("sss");
	if (vsss.type() != binary_wo::typeInvalid)
	{
		mask.inc_sss = 1;
		font.sss = vsss.value_uint32();
	}

	binary_wo::VarObj vClr = vFont.get_s("color");
	if (vClr.type() != binary_wo::typeInvalid)
	{
		DecodeColor(vClr, font.clr);
		mask.inc_clr = 1;
	}
}

void EncodeFont(const FONT& font, WebName name, binary_wo::VarObj& resObj)
{
	binary_wo::VarObj vFont = resObj.add_field_struct(name);
	vFont.add_field_str("name", font.name);
	vFont.add_field_int16("dyHeight", font.dyHeight);
	vFont.add_field_int8("charSet", font.bCharSet);
	vFont.add_field_bool("bls", font.bls);
	vFont.add_field_bool("italic", font.fItalic);
	vFont.add_field_bool("strikeout", font.fStrikeout);
	vFont.add_field_int8("uls", font.uls);
	vFont.add_field_int8("sss", font.sss);
	vFont.add_field_int16("themeFont", font.themeFontType);
	EncodeColor(font.clr, "color", vFont);
}

void DecodeXf(const binary_wo::VarObj& varXf, KXF& xf, KXFMASK& mask, bool decodeBdr)
{
	//fLocked
	binary_wo::VarObj vLocked = varXf.get_s("locked");
	if (vLocked.type() != binary_wo::typeInvalid)
	{
		mask.inc_fLocked = 1;
		xf.fLocked = vLocked.value_bool() ? 1 : 0;
	}

	//fHidden
	binary_wo::VarObj vHidden = varXf.get_s("hidden");
	if (vHidden.type() != binary_wo::typeInvalid)
	{
		mask.inc_fHidden = 1;
		xf.fHidden = vHidden.value_bool() ? 1 : 0;
	}

	//alcH
	binary_wo::VarObj vAlcH = varXf.get_s("alcH");
	if (vAlcH.type() != binary_wo::typeInvalid)
	{
		mask.inc_alcH = 1;
		xf.alcH = vAlcH.value_uint32();
	}
	//alcV
	binary_wo::VarObj vAlcV = varXf.get_s("alcV");
	if (vAlcV.type() != binary_wo::typeInvalid)
	{
		mask.inc_alcV = 1;
		xf.alcV = vAlcV.value_uint32();
	}

	//fWrap
	binary_wo::VarObj vWrap = varXf.get_s("wrap");
	if (vWrap.type() != binary_wo::typeInvalid)
	{
		mask.inc_fWrap = 1;
		xf.fWrap = vWrap.value_bool() ? 1 : 0;
	}

	//display：溢出与截断 [AS新增] [2:溢出 | 3:截断]
	binary_wo::VarObj vDisplay = varXf.get_s("display");
	if (vDisplay.type() != binary_wo::typeInvalid)
	{
		mask.inc_fWrap = 1;
		xf.display = vDisplay.value_uint32();
	}

	//fShrinkToFit
	binary_wo::VarObj vShrinkToFit = varXf.get_s("shrinkToFit");
	if (vShrinkToFit.type() != binary_wo::typeInvalid)
	{
		mask.inc_fShrinkToFit = 1;
		xf.fShrinkToFit = vShrinkToFit.value_bool() ? 1 : 0;
	}

	//cIndent
	binary_wo::VarObj vIndent = varXf.get_s("indent");
	if (vIndent.type() != binary_wo::typeInvalid)
	{
		mask.inc_cIndent = 1;
		xf.cIndent = vIndent.value_uint32();
	}

	//iReadingOrder
	binary_wo::VarObj vReadingOrder = varXf.get_s("readingOrder");
	if (vReadingOrder.type() != binary_wo::typeInvalid)
	{
		mask.inc_iReadingOrder = 1;
		xf.iReadingOrder = vReadingOrder.value_uint32();
	}

	//trot
	binary_wo::VarObj vTrot = varXf.get_s("trot");
	if (vTrot.type() != binary_wo::typeInvalid)
	{
		mask.inc_trot = 1;
		xf.trot = vTrot.value_uint32();
	}

	//fill
	binary_wo::VarObj vFill = varXf.get_s("fill");
	if (vFill.type() != binary_wo::typeInvalid)
		DecodeFill(vFill, xf.fill, mask);

	//pNumFmt;			//数字格式
	binary_wo::VarObj vNumFmt = varXf.get_s("numfmt");
	if (vNumFmt.type() != binary_wo::typeInvalid)
	{
		mask.inc_pNumFmt = 1;
		WebStr str = vNumFmt.value_str();
		xstrncpy_s(xf.numfmt.fmt, str, MAX_NUMBERFMT_CCH);
		xf.numfmt.fmt[MAX_NUMBERFMT_CCH] = __Xc('\0');
	}

	//font
	binary_wo::VarObj vFont = varXf.get_s("font");
	if (vFont.type() != binary_wo::typeInvalid)
		DecodeFont(vFont, xf.font, mask);

	if (decodeBdr)
	{
		mask.inc_clrTop = DecodeColor_s(varXf, "clrTop", xf.clrTop);
		mask.inc_clrBottom = DecodeColor_s(varXf, "clrBottom", xf.clrBottom);
		mask.inc_clrLeft = DecodeColor_s(varXf, "clrLeft", xf.clrLeft);
		mask.inc_clrRight = DecodeColor_s(varXf, "clrRight", xf.clrRight);
		mask.inc_clrDiagUp = DecodeColor_s(varXf, "clrDiagUp", xf.clrDiagUp);
		mask.inc_clrDiagDown = DecodeColor_s(varXf, "clrDiagDown", xf.clrDiagDown);

		binary_wo::VarObj x = varXf.get_s("dgTop");
		if (x.type() != binary_wo::typeInvalid)
		{
			xf.dgTop = x.value_int32();
			mask.inc_dgTop = 1;
		}

		x = varXf.get_s("dgBottom");
		if (x.type() != binary_wo::typeInvalid)
		{
			xf.dgBottom = x.value_int32();
			mask.inc_dgBottom = 1;
		}

		x = varXf.get_s("dgLeft");
		if (x.type() != binary_wo::typeInvalid)
		{
			xf.dgLeft = x.value_int32();
			mask.inc_dgLeft = 1;
		}

		x = varXf.get_s("dgRight");
		if (x.type() != binary_wo::typeInvalid)
		{
			xf.dgRight = x.value_int32();
			mask.inc_dgRight = 1;
		}

		x = varXf.get_s("dgDiagUp");
		if (x.type() != binary_wo::typeInvalid)
		{
			xf.dgDiagUp = x.value_int32();
			mask.inc_dgDiagUp = 1;
		}

		x = varXf.get_s("dgDiagDown");
		if (x.type() != binary_wo::typeInvalid)
		{
			xf.dgDiagDown = x.value_int32();
			mask.inc_dgDiagDown = 1;
		}
	}
}

void EncodeXf(const KXF& xf, WebName name, binary_wo::VarObj& resObj)
{
	binary_wo::VarObj vXf = resObj.add_field_struct(name);
	EncodeFont(xf.font, "font", vXf);

	vXf.add_field_bool("wrap", xf.fWrap);
	vXf.add_field_bool("display", xf.display);
	vXf.add_field_bool("shrinkToFit", xf.fShrinkToFit);
	vXf.add_field_int8("alcH", xf.alcH);
	vXf.add_field_int8("alcV", xf.alcV);
	vXf.add_field_int8("indent", xf.cIndent);
	vXf.add_field_int8("readingOrder", xf.iReadingOrder);
	vXf.add_field_int8("trot", xf.trot);
	vXf.add_field_int8("dgLeft", xf.dgLeft);
	vXf.add_field_int8("dgRight", xf.dgRight);
	vXf.add_field_int8("dgTop", xf.dgTop);
	vXf.add_field_int8("dgBottom", xf.dgBottom);
	vXf.add_field_int8("dgDiagUp", xf.dgDiagUp);
	vXf.add_field_int8("dgDiagDown", xf.dgDiagDown);
	vXf.add_field_str("numfmt", xf.numfmt.fmt);

	EncodeColor(xf.clrLeft, "clrLeft", vXf);
	EncodeColor(xf.clrRight, "clrRight", vXf);
	EncodeColor(xf.clrTop, "clrTop", vXf);
	EncodeColor(xf.clrBottom, "clrBottom", vXf);
	EncodeColor(xf.clrDiagUp, "clrDiagUp", vXf);
	EncodeColor(xf.clrDiagDown, "clrDiagDown", vXf);

	EncodeFill(xf.fill, "fill", vXf);
}

bool DecodeBorderXf(const binary_wo::VarObj& varXf, BorderXF& borderXf)
{
	DecodeColor_s(varXf, "clrTop", borderXf.spClrTop);
	DecodeColor_s(varXf, "clrBottom", borderXf.spClrBottom);
	DecodeColor_s(varXf, "clrLeft", borderXf.spClrLeft);
	DecodeColor_s(varXf, "clrRight", borderXf.spClrRight);
	DecodeColor_s(varXf, "clrDiagUp", borderXf.spClrDiagUp);
	DecodeColor_s(varXf, "clrDiagDown", borderXf.spClrDiagDown);
	DecodeColor_s(varXf, "clrInsideVert", borderXf.spClrInsideVert);
	DecodeColor_s(varXf, "clrInsideHorz", borderXf.spClrInsideHorz);

	DecodeInt_s(varXf, "dgTop", borderXf.dgTop);
	DecodeInt_s(varXf, "dgBottom", borderXf.dgBottom);
	DecodeInt_s(varXf, "dgLeft", borderXf.dgLeft);
	DecodeInt_s(varXf, "dgRight", borderXf.dgRight);
	DecodeInt_s(varXf, "dgDiagUp", borderXf.dgDiagUp);
	DecodeInt_s(varXf, "dgDiagDown", borderXf.dgDiagDown);
	DecodeInt_s(varXf, "dgInsideVert", borderXf.dgInsideVert);
	DecodeInt_s(varXf, "dgInsideHorz", borderXf.dgInsideHorz);

	return borderXf.IsValid();
}

HRESULT _SetRangeBorderXf(ks_stdptr<etoldapi::Range> spRange, const BorderXF& borderXf)
{
	HRESULT hr = S_CONTINUE;
	{
		if (!spRange) return S_OK;

		KCOMPTR(Borders) ptrBorders;
		spRange->get_Borders(&ptrBorders);
		if (ptrBorders == NULL) return S_OK;

		KCOMPTR(Border) ptrDiagonalDownBorder;
		ptrBorders->get_Item(etDiagonalDown, &ptrDiagonalDownBorder);

		KCOMPTR(Border) ptrDiagonalUpBorder;
		ptrBorders->get_Item(etDiagonalUp, &ptrDiagonalUpBorder);

		KCOMPTR(Border) ptrEdgeLeftBorder;
		ptrBorders->get_Item(etEdgeLeft, &ptrEdgeLeftBorder);

		KCOMPTR(Border) ptrEdgeTopBorder;
		ptrBorders->get_Item(etEdgeTop, &ptrEdgeTopBorder);

		KCOMPTR(Border) ptrEdgeBottomBorder;
		ptrBorders->get_Item(etEdgeBottom, &ptrEdgeBottomBorder);

		KCOMPTR(Border) ptrEdgeRightBorder;
		ptrBorders->get_Item(etEdgeRight, &ptrEdgeRightBorder);

		KCOMPTR(Border) ptrInsideVerticalBorder;
		ptrBorders->get_Item(etInsideVertical, &ptrInsideVerticalBorder);

		KCOMPTR(Border) ptrInsideHorizontalBorder;
		ptrBorders->get_Item(etInsideHorizontal, &ptrInsideHorizontalBorder);

		//具体的处理
		{{
			{{{
				app_helper::KSmartBatchSetRangeXF sbsXF(spRange);
				hr = sbsXF.GetHR();
				if (FAILED(hr))
					return hr;
				{	//设置LineStyle与Color时batch要分开，此处为LineStyle的
					// app_helper::KSmartBatchSetRangeXF sbsXF(spRange);
					// hr = sbsXF.GetHR();
					// if (FAILED(hr))
					// 	return S_OK;

					_SetBorderLineStyle(ptrEdgeLeftBorder, borderXf.dgLeft);
					_SetBorderLineStyle(ptrEdgeRightBorder, borderXf.dgRight);
					_SetBorderLineStyle(ptrEdgeTopBorder, borderXf.dgTop);
					_SetBorderLineStyle(ptrEdgeBottomBorder, borderXf.dgBottom);
					_SetBorderLineStyle(ptrDiagonalUpBorder, borderXf.dgDiagUp);
					_SetBorderLineStyle(ptrDiagonalDownBorder, borderXf.dgDiagDown);
					_SetBorderLineStyle(ptrInsideHorizontalBorder, borderXf.dgInsideHorz);
					_SetBorderLineStyle(ptrInsideVerticalBorder, borderXf.dgInsideVert);
				// }

				// {	//设置LineStyle与Color时batch要分开，此处为Color的
				// 	app_helper::KSmartBatchSetRangeXF sbsXF(spRange);
				// 	hr = sbsXF.GetHR();
				// 	if (FAILED(hr))
				// 		return S_OK;

					_SetBorderColor(ptrEdgeLeftBorder, borderXf.spClrLeft.get());
					_SetBorderColor(ptrEdgeRightBorder, borderXf.spClrRight.get());
					_SetBorderColor(ptrEdgeTopBorder, borderXf.spClrTop.get());
					_SetBorderColor(ptrEdgeBottomBorder, borderXf.spClrBottom.get());
					_SetBorderColor(ptrDiagonalUpBorder, borderXf.spClrDiagUp.get());
					_SetBorderColor(ptrDiagonalDownBorder, borderXf.spClrDiagDown.get());
					_SetBorderColor(ptrInsideHorizontalBorder, borderXf.spClrInsideHorz.get());
					_SetBorderColor(ptrInsideVerticalBorder, borderXf.spClrInsideVert.get());
				}
			}}}
		}}
	}
	return hr;
}

bool DecodeColor_s(const binary_wo::VarObj& vClrRoot, WebName clrFieldName, std::unique_ptr<EtColor>& spClr)
{
	binary_wo::VarObj vClr = vClrRoot.get_s(clrFieldName);
	if (vClr.type() != binary_wo::typeInvalid)
	{
		spClr.reset(new EtColor());
		DecodeColor(vClr, *spClr);
		return true;
	}
	else
	{
		return false;
	}
}

bool DecodeColor_s(const binary_wo::VarObj& vClrRoot, WebName clrFieldName, EtColor& spClr)
{
	binary_wo::VarObj vClr = vClrRoot.get_s(clrFieldName);
	if (vClr.type() != binary_wo::typeInvalid)
	{
		DecodeColor(vClr, spClr);
		return true;
	}
	else
	{
		return false;
	}
}

HRESULT _BORDERLINESTYLE_ETLineStyle(BORDERLINESTYLE bls, ETLineStyle& ls, ETBorderWeight& w)
{
	switch (bls)
	{
	case blsNone: ls = etLineStyleNone;		w = etThin;			break;
	case blsHair: ls = etContinuous;		w = etHairline;		break;
	case blsDotted: ls = etDot;				w = etThin;			break;
	case blsDashDotDot: ls = etDashDotDot;		w = etThin;			break;
	case blsDashDot: ls = etDashDot;			w = etThin;			break;
	case blsDashed: ls = etDash;			    w = etThin;			break;
	case blsThin: ls = etContinuous;		w = etThin;			break;
	case blsMediumDashDotDot: ls = etDashDotDot;		w = etMedium;		break;
	case blsSlantedDashDot: ls = etSlantDashDot;		w = etMedium;		break;
	case blsMediumDashDot: ls = etDashDot;			w = etMedium;		break;
	case blsMediumDashed: ls = etDash;				w = etMedium;		break;
	case blsMedium: ls = etContinuous;		w = etMedium;		break;
	case blsThick: ls = etContinuous;		w = etThick;		break;
	case blsDouble: ls = etDouble;			w = etThick;		break;
	default: return E_INVALIDARG;
	}

	return S_OK;
}

void _SetBorderLineStyle(ks_stdptr<etoldapi::Border> spBorder, int bls)
{
	if (BLS_INVALID == bls)
		return;

	oldapi::ETLineStyle ls;
	oldapi::ETBorderWeight w;
	HRESULT hr = _BORDERLINESTYLE_ETLineStyle((BORDERLINESTYLE)bls, ls, w);
	if (FAILED(hr))
		return;

	if (etLineStyleNone == ls)
		spBorder->put_LineStyle(ls);
	else
		spBorder->put_WeightStyle(w, ls);
}

void _SetBorderColor(ks_stdptr<etoldapi::Border> spBorder, const EtColor* pColor)
{
	if (!pColor)
		return;

	switch (pColor->getType())
	{
	case ectARGB:
		spBorder->put_Color(EtColor::ARGB2ABGR(pColor->getARGB()));
		break;
	case ectTHEME:
		{
			KComVariant varColor;
			V_VT(&varColor) = VT_I4;
			V_INT(&varColor) = static_cast<INT>(static_cast<UINT>(pColor->getTheme()) + 1);
			spBorder->put_ThemeColor(varColor);
		}
		break;
	case ectICV:
	default:
		ASSERT(FALSE);
		break;
	}

	KComVariant varTint = pColor->getTint();
	spBorder->put_TintAndShade(varTint);
}

}
