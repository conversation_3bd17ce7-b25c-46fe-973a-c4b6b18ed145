﻿#ifndef __WEBET_DB_DASHBOARD_TASK_CLASS_H__
#define __WEBET_DB_DASHBOARD_TASK_CLASS_H__

#include "et_task_class.h"

namespace wo
{

class TaskExecDbChartSetStyle : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbChartSetStyle(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbChartRemoveStyle : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbChartRemoveStyle(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbChartSetOrder : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbChartSetOrder(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbModifyPluginConfig : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbModifyPluginConfig(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbDashboardCreateFilter : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbDashboardCreateFilter(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbDashboardUpdateFilter : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbDashboardUpdateFilter(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbDashboardDeleteFilter : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbDashboardDeleteFilter(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbDashboardMoveFilter : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbDashboardMoveFilter(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbDashboardSetFilterCriteria : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbDashboardSetFilterCriteria(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbDashboardClearFilterCriteria : public ETDbSheetTaskClassBase
{
public:
    explicit TaskExecDbDashboardClearFilterCriteria(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbDashboardAddView : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbDashboardAddView(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbDashboardModifyView : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbDashboardModifyView(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
    bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
    {
        return false; // 允许输入公式
    }
};

};
#endif // __WEBET_DB_DASHBOARD_TASK_CLASS_H__