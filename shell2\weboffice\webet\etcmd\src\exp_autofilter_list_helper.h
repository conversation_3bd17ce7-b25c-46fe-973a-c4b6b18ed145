﻿#ifndef __WEBOFFICE_EXP_AUTOFILTER_LIST_HELPER_H__
#define __WEBOFFICE_EXP_AUTOFILTER_LIST_HELPER_H__

class ExportAfListHelper
{
public:
    ExportAfListHelper(const binary_wo::VarObj& param, const RANGE& fieldRg, IKWorksheet* pWs, _Workbook* pWb, wo::KEtRevisionContext* pCtx);
    ExportAfListHelper(const binary_wo::VarObj& param, const RANGE& fieldRg, IKWorksheet* pWs, _Workbook* pSrcWb, _Workbook* pDstWb, wo::KEtRevisionContext* pCtx);
    HRESULT Export();
    HRESULT ExportResult();
    HRESULT ExportCategoryFilterResultToBook(wo::KEtRevisionContext& ctx, wo::KEtWorkbook* pEtWorkBook);
    HRESULT ExportCategoryFilterResultToSheet();

protected:
    void GetFilterFieldName(ISheet* pSheet, ks_wstring& strFieldName);
	void SetNewSheetName(ISheet* pSheet, IBook* pBook, const ks_wstring& strFieldName);
	HRESULT ExportChart(etoldapi::Range* pRange);
private:
    const binary_wo::VarObj& m_param;
    RANGE m_fieldRg;
    IKWorksheet* m_pWorksheet;
    _Workbook* m_pSrcWb;
    _Workbook* m_pDstWb;
    wo::KEtRevisionContext* m_pCtx;
};

#endif // __WEBOFFICE_EXP_AUTOFILTER_LIST_HELPER_H__