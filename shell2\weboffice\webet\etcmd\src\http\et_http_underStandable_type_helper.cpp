﻿#include "et_http_underStandable_type_helper.h"
#include <kso/appfeature/kappfeature.h>


namespace wonf{
	const kfc::nf::NF_FORMAT_PARAM* sGetNF_FORMAT_PARAM()
	{
		static BOOL s_bHas = FALSE;
		static kfc::nf::NF_FORMAT_PARAM s_NFFormatParam;
		if (FALSE == s_bHas)
		{
			s_NFFormatParam.dwOpt |= kfc::nf::nffo_DecmplForArea;
			s_NFFormatParam.dwOpt |= kfc::nf::nffo_FormatForArea;
			s_NFFormatParam.lLanguage = (0x0ffff & alg::_get_locale_id());

			if (!_kso_QueryFeatureState(kaf_et_local_num_fmt))
				s_NFFormatParam.dwOpt |= kfc::nf::nffo_IgnoreAreaCode;
			s_bHas = TRUE;
		}
		return &s_NFFormatParam;
	}
}
namespace wo
{

    void serialHttpUnderstandableType(wo::KEtRevisionContext *ctx, ISheet *pSheet, IBookOp* pBookOp, IDX iSheet, ROW row, COL col,
            const_token_ptr pToken,  LPCWSTR text, ISerialAcceptor* pAcpt) 
    {
        pAcpt->addKey("understandableType");
        pAcpt->beginStruct();
        bool datatime = false;
        if (alg::const_vint_token_assist::is_type(pToken) || alg::const_vdbl_token_assist::is_type(pToken))
        {
            //数值类型，先判断是不是datetime
            kfc::nf::NF_FORMAT_TYPE formatType = kfc::nf::FMT_TYPE_NULL;
            DWORD nOpt = 0;
            DWORD valueType = 0;
            VARIANT var;
            app_helper::GetCellNumFmtType(pBookOp, iSheet, row, col, &nOpt, &formatType, &valueType, var);
        
            if (kfc::nf::FMT_TYPE_DATA_TIME == formatType)
            {
                switch (nOpt & kfc::nf::NFTYPE_DATE_DT)
                {
                    case kfc::nf::NFTYPE_DATE_DT:
                    {
                        pAcpt->addString("type", __X("datetime"));
                        
                        kfc::nf::NFHANDLE hNF = NULL;
                        kfc::nf::NFCompile(__X("yyyy-m-d h:mm"), &hNF, wonf::sGetNF_FORMAT_PARAM());
                        ks_bstr textFix;
                        ctx->getStringTools()->GetCellText(pSheet, row, col, &textFix, hNF, -1, NULL);

                        pAcpt->addString("value", textFix.c_str());
                        datatime = true;
                    }
                    break;
                    case kfc::nf::NFTYPE_DATE_D:
                    {
                        pAcpt->addString("type", __X("date"));
                        kfc::nf::NFHANDLE hNF = NULL;
                        kfc::nf::NFCompile(__X("yyyy-m-d"), &hNF, wonf::sGetNF_FORMAT_PARAM());
                        ks_bstr textFix;
                        ctx->getStringTools()->GetCellText(pSheet, row, col, &textFix, hNF, -1, NULL);

                        pAcpt->addString("value", textFix.c_str());
                        datatime = true;
                    }
                    break;
                    case kfc::nf::NFTYPE_DATE_T:
                    {
                        pAcpt->addString("type", __X("time"));
                        kfc::nf::NFHANDLE hNF = NULL;
                        kfc::nf::NFCompile(__X("h:mm:ss"), &hNF, wonf::sGetNF_FORMAT_PARAM());
                        ks_bstr textFix;
                        ctx->getStringTools()->GetCellText(pSheet, row, col, &textFix, hNF, -1, NULL);

                        pAcpt->addString("value", textFix.c_str());
                        datatime = true;
                    }
                    break;
                }
            }
        }

        if (!datatime)
        {
            if (alg::const_vint_token_assist::is_type(pToken))
            {//整数
                alg::const_vbool_token_assist vta(pToken);
                pAcpt->addString("type", __X("int"));
                pAcpt->addInt32("value", vta.get_value());
            }
            else if (alg::const_vdbl_token_assist::is_type(pToken))
            {//浮点数
                alg::const_vdbl_token_assist vta(pToken);
                pAcpt->addString("type", __X("double"));
                pAcpt->addFloat64("value", vta.get_value());
            }
            else if (alg::const_vbool_token_assist::is_type(pToken))
            {//布尔值
                alg::const_vbool_token_assist vta(pToken);
                pAcpt->addString("type", __X("bool"));
                pAcpt->addBool("value", vta.get_value());
            }
            else if (alg::const_vstr_token_assist::is_type(pToken))
            {//字符串
                alg::const_vstr_token_assist vta(pToken);
                pAcpt->addString("type", __X("string"));
                pAcpt->addString("value", vta.get_value());
            }
            else if (alg::const_opt_token_assist::is_type(pToken))
            {//操作符

            }
            else if (alg::const_error_token_assist::is_type(pToken))
            {//错误码
                alg::const_error_token_assist vta(pToken);
                pAcpt->addString("type", __X("error"));
                DWORD code = vta.get_code();
                if (code == alg::EXV_E_NULL)
                {
                    pAcpt->addString("value", __X("#EXV_E_NULL"));
                }
                else if(code == alg::EXV_E_DIV)
                {
                    pAcpt->addString("value", __X("#DIV/0!"));
                }
                else if(code == alg::EXV_E_VALUE)
                {
                    pAcpt->addString("value", __X("#VALUE!"));
                }
                else if(code == alg::EXV_E_REF)
                {
                    pAcpt->addString("value", __X("#REF!"));
                }
                else if(code == alg::EXV_E_NAME)
                {
                    pAcpt->addString("value", __X("#NAME?"));
                }
                else if(code == alg::EXV_E_NUM)
                {
                    pAcpt->addString("value", __X("#NUM!"));
                }
                else if(code == alg::EXV_E_NA)
                {
                    pAcpt->addString("value", __X("#N/A"));
                }
                else if(code == alg::EXV_E_GETTING_DATA)
                {
                    pAcpt->addString("value", __X("#GETTING_DATA"));
                }
                else if(code == alg::EXV_E_SPILL)
                {
                    pAcpt->addString("value", __X("#SPILL!"));
                }
                else if(code == alg::EXV_E_CONNECT)
                {
                    pAcpt->addString("value", __X("#CONNECT!"));
                }
                else if(code == alg::EXV_E_BLOCKED)
                {
                    pAcpt->addString("value", __X("#BLOCKED!"));
                }
                else if(code == alg::EXV_E_UNKNOWN)
                {
                    pAcpt->addString("value", __X("#UNKNOWN!"));
                }
                else if(code == alg::EXV_E_FIELD)
                {
                    pAcpt->addString("value", __X("#FIELD!"));
                }
                else if(code == alg::EXV_E_CALC)
                {
                    pAcpt->addString("value", __X("#CALC!"));
                }
                else if(code == alg::EXV_E_CIRCULAR)
                {
                    pAcpt->addString("value", __X("#CIRCULAR!"));
                }
                else if(code == alg::EXV_E_SUSPEND)
                {
                    pAcpt->addString("value", __X("#SUSPEND!"));
                }
                else if(code == alg::EXV_E_EXTERNAL_LOADING)
                {
                    pAcpt->addString("value", __X("#EXTERNAL_LOADING"));
                }
                else 
                {
                    pAcpt->addString("value", __X("#NOERROR"));
                }
                
            }
            else if (alg::const_function_token_assist::is_type(pToken))
            {//函数

            }
            else if (alg::const_prop_token_assist::is_type(pToken))
            {//PROP 公式编译器用

            }
            else if (alg::const_reserved_token_assist::is_type(pToken))
            {//特殊用途

            }
            else if (alg::const_cellint_token_assist::is_type(pToken))
            {//

            }
            else if (alg::const_attr_token_assist::is_type(pToken))
            {//ptg Attr

            }
            else if (alg::const_handle_token_assist::is_type(pToken))
            {//

            }
            else if (alg::const_vector_token_assist::is_type(pToken))
            {//离散区域使用

            }
            else if (alg::const_matrix_token_assist::is_type(pToken))
            {//矩阵常数使用

            }
            else if (alg::const_grid_token_assist::is_type(pToken))
            {//一个重量级的Matrix

            }
            else if (alg::const_stref_token_assist::is_type(pToken))
            {//STatic reference

            }
            else if (alg::const_perref_token_assist::is_type(pToken))
            {//PerstTokenVec中保存的ReferToken

            }
            else if (alg::const_fxref_token_assist::is_type(pToken))
            {//Formula eXtensive reference 公式编译器用

            }
            else if (alg::const_tabref_token_assist::is_type(pToken))
            {//

            }
            else if (alg::const_dbfieldref_token_assist::is_type(pToken))
            {//
                
            }
            else if (alg::const_fxtabref_token_assist::is_type(pToken))
            {//

            }
        }
        pAcpt->endStruct();
    }

}