﻿#ifndef __WEBET_DBSHEET_PROVIDER_H__
#define __WEBET_DBSHEET_PROVIDER_H__
#include "appcore/et_appcore_dbsheet_filter.h"

class KDbDataProvider : public IDbDataProvider
{
public:
    KDbDataProvider(IBook* pBook) : m_pBook(pBook)
    {}

    // IDbDataProvider
    STDPROC GetUserManager(IDbUsersManager** ppMgr) override;
    STDPROC GetFunctionContext(IFunctionContext** ppFuncCtx) override;

private:
    IBook* m_pBook = nullptr;
};

#endif // __WEBET_DBSHEET_PROVIDER_H__