﻿#ifndef __WEBET_COMMON_LOG_VALUE_SERIALISER_H__
#define __WEBET_COMMON_LOG_VALUE_SERIALISER_H__

#include "appcore/et_appcore_dbsheet.h"
#include "common_log/common_data.h"

namespace wo
{
class KEtWorkbook;
class CommonLogValueSerialiser
{
public:
    CommonLogValueSerialiser(IKWorkbook *pWorkbook, IKWorksheet *pWorksheet);
    HRESULT SerialiseRecord(Log_Record* pLogRecord, EtDbId recordId);
    HRESULT SerialiseRecords(Log_sheet* pSheet, const EtDbId* pRecIds, UINT cnt);
    HRESULT SerialiseField(Log_Field* pField, EtDbId fieldId);
    HRESULT SerialiseFields(Log_sheet* pLogSheet);
    std::unordered_set<EtDbId> GetNoPermissionRecord() const;
    std::unordered_set<EtDbId> GetNoPermissionField() const;
    HRESULT SerialiseCell(Log_Col*, EtDbId recordId, EtDbId fieldId);
private:
    HRESULT serialiseCell(LogCellValue* pCellValue, EtDbId recordId, EtDbId fieldId);
    HRESULT serialiseRecord(Log_Record* pLogRecord, EtDbId recordId, const std::vector<EtDbId>& fieldIds);
    HRESULT getSerialiseFieldIds(std::vector<EtDbId>& filedIds);
private:
    IBook *m_pBook;
    ISheet *m_pSheet;
    ks_stdptr<IDBSheetOp> m_spDbSheetOp;
    ks_stdptr<IDBSheetViews> m_spDbSheetViews;
    IDbFieldsManager *m_pFieldManager;
    IEncodeDecoder* m_pEncodeDecoder;
    ks_stdptr<IETStringTools> m_spStringTools;
	ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
};
} // namespace wo

#endif // __WEBET_COMMON_LOG_VALUE_SERIALISER_H__
