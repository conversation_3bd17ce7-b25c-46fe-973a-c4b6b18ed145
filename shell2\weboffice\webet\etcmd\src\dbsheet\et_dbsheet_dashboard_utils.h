#ifndef __WEBET_DATABASE_SHEET_DASHBOARD_UTILS_H__
#define __WEBET_DATABASE_SHEET_DASHBOARD_UTILS_H__

#include "appcore/et_appcore_dbsheet.h"
#include "webbase/binvariant/binvarobj.h"
#include "wo/et_shared_str.h"
#include "et_revision_context_impl.h"

namespace wo
{
using binary_wo::VarObj;
class KEtWorkbook;
namespace DbDashboard
{
bool IsEtDataSourceType(WebExtensionDataSourceType dataSourceType);
HRESULT SetStatsModuleDataSource(const VarObj& param, IDBChartStatisticMgr* pModuleMgr, IKWebExtension* pWebExtension,
                                 EtDbId* id, PCWSTR dataRangeKey = nullptr);
HRESULT SetStatsModuleSetting(const VarObj& param, _Workbook* pWorkbook, IDBChartStatisticModule* pModule, KEtRevisionContext* pCtx, ET_DBSheet_ChartType chartType);
IKWebExtension* CreateWebExtension(etoldapi::Shapes* pShapes, PCWSTR WebExtKey, WebExtType webExtType);
HRESULT DeleteWebExtensionHostShape(_Worksheet* pWorkSheet, IKWebExtension* pWebExtension);
HRESULT GetDbChartStatisticModuleId(IKWebExtension* pWebExtension, EtDbId& moduleId);
HRESULT CreateWebExtensionDataSource(IKWebExtension* pWebExtension, WebExtensionDataSourceType dataSourceType);
HRESULT SetEtDataSource(KEtWorkbook* pEtWorkbook, IDbtBookCtx* pDbtBookCtx, IKWebExtension* pWebExtension, IDBDashBoardDataOp* pDashboardData, RANGE& rg);
HRESULT CheckAndChangeDataSourceType(IBook* pBook, UINT dbSheetId, const RANGE& workSheetRange,
                                     IDbtBookCtx* pDbtBookCtx, IKWebExtension* pWebExtension,
                                     IDBDashBoardDataOp* pDashboardData, bool forceNormalRange, EtDbId* pNewModuleId = nullptr);
HRESULT SetWorksheetChartSetting(const VarObj& param, IKWebExtension* pWebExtension, IDBDashBoardDataOp* pDashboardData);
HRESULT DeleteWebExtension(IKWebExtension* pWebExtension, _Worksheet* pWorkSheet, IDBDashBoardDataOp* pDashboardData);
ET_DBSheet_ChartType GetChartType(IKWebExtension* pWebExtension);
INT GetChartMaxStatisticsCount(ET_DBSheet_ChartType chartType);
} // namespace DbDashboard

} // namespace wo

#endif // __WEBET_DATABASE_SHEET_DASHBOARD_UTILS_H__
