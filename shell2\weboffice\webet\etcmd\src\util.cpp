﻿
#include "etstdafx.h"
#include "util.h"
#include "etcore/little_alg.h"
#include "et_hard_define_strings.h"
#include "webbase/logger.h"
#include "webetlink.h"
#include "webbase/binvariant/binreader.h"
#include "webbase/binvariant/binwriter.h"
#include "workbooks.h"
#include "et_revision_context_impl.h"
#include "utils/attachment_utils.h"
#include <public_header/drawing/model/abstract_shape.h>
#include "mvc/et_mvc.h"
#include <public_header/opl/mvc/cellimage/et_cellimage_shape.h>
#include "form/et_form_task_class.h"
#include "fileInfoCollect.h"
#include <public_header/opl/mvc/et_shape_tree.h>
#include <public_header/chart/src/mvc/kctshapetreecontrol.h>
#include <public_header/chart/src/mvc/kctshapetreeapifactory.h>
#include "appcore/et_appcore_shared_link_sheet.h"
#include <public_header/chart/src/datasource/kctchartdatasource.h>
#include <public_header/chart/src/mvc/kctchartlayer.h>
#include <public_header/chart/src/model/kctchart.h>
#include "woetsetting.h"
#include "collect.h"
#include <kfc/et_numfmt_str.h>
#include "database/database_utils.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h"
#include "dbsheet/dashboard/db_chart_wrapper.h"
#include <kpaint/graphics_system.h>
#include "etapplication.h"
#include "woeventdispatcher.h"

#include "webbase/webdeclare.h"
#include "dashboard/webchart_config.h"
#include "insertpicture/insertpicture_helper.h"
#include "helpers/db_value_serialize_helper.h"
#include "db/db_basic_itf.h"

extern Callback* gs_callback;
extern WebProcType gs_procType;
#define CHECK_RETURN_VOID(pObj) if (!(pObj)) return;
#define CHECK_CONTINUE(pObj) if (!(pObj)) continue;

namespace wo
{
namespace util
{

bool IsEnableRelease(PCWSTR connId)
{
    if (xstrncmp(__X("webhook_core"), connId, 12) == 0)
        return false;
    if (xstrncmp(connId, __X("fake"), 4) == 0)
        return false;
    if (xstrncmp(connId, __X("sapi_"), 5) == 0)
        return false;
    return true;
}

CalcBatchUpdate::CalcBatchUpdate(IBookOp* pBookOp, bool specialForImportRange)
{
    if (pBookOp)
    {
        m_spBookOp = pBookOp;
        m_spBookOp->BeginBatchUpdate();
        if (specialForImportRange && not BanImportrangeCalcMixedGuard())
        {
            m_upImportrangeCalcGUard.reset(new GuardImportrangeCalc(pBookOp));
        }
    }
}

bool ForEachSheet(IBook *pBook, IDX from, IDX to, const std::function<bool(ISheet *, IDX)> &func)
{
    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    if (from < 0 || to < 0 || sheetCount <= from || sheetCount <= to || to < from)
    {
        WOLOG_INFO << "[util] forEachSheet sheetidx invalid";
        return false;
    }

    for (INT idx = from; idx <= to; ++idx)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(idx, &spSheet);
        if (spSheet)
        {
            if (func(spSheet, idx))
            {
                return true;
            }
        }
    }

    return false;
}

bool ForEachSheet(IBook *pBook, const std::function<bool(ISheet *, IDX)> &func)
{
    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    if (sheetCount <= 0)
        return false;

    return ForEachSheet(pBook, 0, sheetCount - 1, func);
}

bool ForEachSheetRange(IBook *pBook,
                       const RANGE &rg,
                       const std::function<bool(ISheet *, IDX, const RANGE &)> &func)
{
    return ForEachSheet(pBook, rg.SheetFrom(), rg.SheetTo(), [&](ISheet *pSheet, IDX sheetIdx) -> bool {
        RANGE newRg(rg);
        newRg.SetSheetFromTo(sheetIdx);
        return func(pSheet, sheetIdx, newRg);
    });
}

void Range2FormulaText(IBookOp *spBookOp, const RANGE &rg, CS_COMPILE_PARAM ccp,  BSTR *outBstr)
{
    etexec::managed_stref_token_assist msta;
    msta.create(0, etexec::ET_RVA_NONE);
    Range2Token(rg, alg::STREF_THIS_BOOK, 0, msta);

    range_helper::ranges rgs = range_helper::ranges::create_instance();
    rgs.add(alg::STREF_THIS_BOOK, rg);

    spBookOp->DecompileRange(rgs, ccp, outBstr);
}

ks_stdptr<IFormula> CreateFormula(IBook* pBook, PCWSTR str, CS_COMPILE_PARAM ccp)
{
    ks_stdptr<IBookOp> spBookOp;
    pBook->GetOperator(&spBookOp);
    ks_stdptr<IFormula> spFmla;
    spBookOp->CreateFormula(&spFmla);
    
    COMPILE_RESULT cr;
    spFmla->SetFormula(str, ccp, &cr);
    if (cr.nErrCode != COMPILE_SUCCESS)
    {
        return ks_stdptr<IFormula>();
    }

    return spFmla;
}

void prepareCoverShapes(IDX sheetIdx, IKShapeRange* ptrShapeRange, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	LONG count = 0;
	ptrShapeRange->GetShapeCount(&count);
	if (count == 0) return;

	LONG pictureShapeCnt = 0; // 收集埋点
	LONG sameSha1Cnt = 0;
	BOOL sameAdjacentSha1 = FALSE;

	acpt->addKey("floatImgInfo");
	acpt->beginArray();
	for (LONG i = 0; i < count; ++i)
	{
		ks_stdptr<drawing::AbstractShape> ptrShape;
		ptrShapeRange->GetShapeByIndex(i, &ptrShape);
		if (ptrShape && ptrShape->isPicture())
		{
			++pictureShapeCnt;
			sameAdjacentSha1 = FALSE;
			ctx->AddCopyShapeInfo(ptrShape, &sameAdjacentSha1);
			if (sameAdjacentSha1)
				++sameSha1Cnt;

			acpt->beginStruct();
			acpt->addInt32("shapeId", ptrShape->id());
			acpt->endStruct();
		}
	}
	acpt->endArray();
	WebID objSheet = ctx->getSheetMain(sheetIdx)->objId();
	acpt->addFloat64("objSheet", objSheet);

	if (pictureShapeCnt > 1000)
	{
		QString info = QStringLiteral("behaviour_copy_shape_cnt_%1_adjacent_same_sha1_%2").arg(count).arg(sameSha1Cnt);
		ctx->collectInfo(info.toUtf8());
	}
}

class CellValueSkipHiddenAcptUtil : public ICellValueAcpt 
{
public:
    CellValueSkipHiddenAcptUtil(ISheet* sheet, ISheetEnum* stEnum, IEtRevisionContext* ctx, const std::function<bool(ROW row, COL col, const_token_ptr pToken)>& cb)
        : m_sheet(sheet), m_sheetEnum(stEnum), m_ctx(ctx), m_callback(cb)
    {
    }

    STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
    {
		if (m_sheetEnum->GetOneRowHidden(row))
		{
			return 0;  //继续枚举
		}

		BOOL hidden = FALSE;
		m_sheetEnum->GetColHidden(col, &hidden, 1);
		if (hidden)
		{
			return 0;  //继续枚举
		}

		if (m_ctx->getProtectionCtx()->isCellHidden(m_sheet, row, col))
		{
			return 0;  //继续枚举
		}

        if (pToken && m_callback(row, col, pToken)) 
		{

            return 1;
        }

        return 0;  //继续枚举
    };

private:
	ISheet *m_sheet;
	ISheetEnum *m_sheetEnum;
    IEtRevisionContext* m_ctx;
    const std::function<bool (ROW row, COL col, const_token_ptr pToken)>& m_callback;
};

void EnumRangeVisibleCells(ISheet *spSheet, const RANGE &rg, IEtRevisionContext* ctx, const std::function<bool (ROW row, COL col, const_token_ptr pToken)> &func)
{
    et_sdptr<ISheetEnum> spSheetEnum;
	spSheet->CreateEnum(&spSheetEnum);
    spSheetEnum->SetFilterContext(ctx->getFilterContext());

    CellValueSkipHiddenAcptUtil cellValAcpt(spSheet, spSheetEnum, ctx, func);
    spSheetEnum->EnumCellValueRowbyRow(rg, &cellValAcpt);
}

// 本方法意在 CreateRangeObj 和 KEtWorkbook 解耦
ks_stdptr<etoldapi::Range> CreateRangeObj(etoldapi::_Workbook* pWorkbook, const RANGE& rg)
{
    ASSERT(rg.SheetFrom() == rg.SheetTo());

    ks_castptr<IKWorkbook> wb(pWorkbook);
    IKWorksheet* ws = wb->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    if (ws == nullptr) {
        ASSERT(!"KEtWorkbook::CreateRangeOb failed!");
        WOLOG_WARN << "KEtWorkbook::CreateRangeObj failed: sheetFrom:" << rg.SheetFrom();

        return ks_stdptr<etoldapi::Range>();
    }

    ks_stdptr<etoldapi::Range> host;
    VS(ws->GetRangeByData(&rg, &host));
    return host;
}

bool isCellImgListSheet(IBook* pBook, IDX iSheet)
{
	INT nCnt = 0;
	pBook->GetSheetCount(&nCnt);
	if (iSheet < 0 || iSheet >= nCnt)
		return false;
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(iSheet, &spSheet);

	const WCHAR* pName = NULL;
	spSheet->GetName(&pName);
	return 0 == xstrcmp(pName, STR_CELL_IMAGE_SHEET_NAME);
}

bool hasCellImgListSheet(IBook* pBook)
{
	INT nCnt = 0;
	pBook->GetSheetCount(&nCnt);
    for (int i = 0; i < nCnt; i++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);

        const WCHAR* pName = NULL;
        spSheet->GetName(&pName);
        if(0 == xstrcmp(pName, STR_CELL_IMAGE_SHEET_NAME))
            return true;
    }
    return false;
}

IDX getLastSheetIdx(IBook* pBook)
{
	INT nCount = 0;
	pBook->GetSheetCount(&nCount);

	if (nCount <= 1)
		return 0;

	return isCellImgListSheet(pBook, nCount - 1) ? nCount - 2 : nCount - 1;
}
///////////////////////////////////////////////////////////////////
SlowCollectCallTimeStat::SlowCollectCallTimeStat(
        const char* tag, 
        unsigned int threshold, 
        KEtWorkbook * wb,
        const WCHAR * collectName)
    : m_tag(tag)
    , m_threshold(threshold)
    , m_wb(wb)
    , m_collectName(collectName)
    , m_begin(std::chrono::steady_clock::now())
{
    ASSERT(m_tag != nullptr && m_wb != nullptr && m_collectName != nullptr);
}

SlowCollectCallTimeStat::~SlowCollectCallTimeStat()
{
    unsigned int ms = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
    if (ms == 0) return;
    
#ifndef _DEBUG
	if (ms > m_threshold)
#endif
    {
        WOLOG_INFO << '[' << m_tag << ']' << " elapsed: " << ms << " ms";
        CollectInfo(m_wb, m_collectName, ms);
    }
}

SlowCallTimeMetrics::SlowCallTimeMetrics(
    const char* tag,  
    KEtWorkbook * wb,
    KU32MetricItem mcItem,
    unsigned int threshold/* = util::kDefSlowCallThreshold*/)
: SlowCallTimeStat(tag, threshold, [wb, mcItem](unsigned int ms) {
    wb->coreMetric().addU32Metrics(mcItem, ms);
})
{
    
}

void SlowCallTimeMetrics::restart(const char * tag, KEtWorkbook * wb, KU32MetricItem mcItem)
{
    SlowCallTimeStat::restart(tag, [wb, mcItem](unsigned int ms) {
        wb->coreMetric().addU32Metrics(mcItem, ms);
    });
}

///////////////////////////////////////////////////////////////////
RandDist::RandDist(unsigned int seed, double probability)
: m_gen(seed)
, m_dist(probability)
{

}

bool RandDist::is()
{
    return m_dist(m_gen);
}

double RandDist::GetProbability()
{
    return m_dist.p();
}

///////////////////////////////////////////////////////////////////
CmdTimeMemStat::CmdTimeMemStat(KEtWorkbook * wb, KEtRevisionContext* pCtx, CmdCallback callback)
    : m_pCtx(pCtx)
    , m_begin(std::chrono::steady_clock::now())
    , m_wb(wb)
    , m_isExec(true)
    , m_memBegin(wo::MemoStatProcMemory())
    , m_callback(callback)
{
}

void CmdTimeMemStat::start(PCWSTR cmdName, bool isExec)
{
    m_isExec = isExec;
    if (cmdName)
        m_cmdName = QString::fromUtf16(cmdName);

    if (m_cmdName.size() > 0)
    {
        m_cmdName.replace(".", "_");
        WOLOG_INFO <<"[" << m_cmdName << "] begin";
    }

}

CmdTimeMemStat::~CmdTimeMemStat()
{
    // 统计执行超过 0.1 秒的命令
    int ms = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
    INT64 memUsage = (wo::MemoStatProcMemory() - m_memBegin) / 1024;
    if (m_cmdName.size() > 0)
    {
        WOLOG_INFO << "[" << m_cmdName << "] elapsed: " << ms << " ms" << ", mem_usage: " << memUsage << 'k';
    }

    wo::WoEtSetting * settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
    int sampleRate = 0;
    if (m_pCtx && settings->isCollect(ms, &sampleRate, settings->GetCmdTimeRand()))
    {
        _Application* pApp = m_wb->GetCoreApp();
	    IKUserConns* pUserConns = pApp->getUserConns();
        QString cmdName = makeCmdName("behaviour_");
        WoFileIncludeCollector collector(m_wb, krt::utf16(cmdName), ms);
        collector.addCmdTraceId(m_pCtx->getCmdTraceId().c_str())
                 .addSampleRate(sampleRate)
                 .addUserConnCnt(pUserConns->count())
                 .collect();
    }
    if (memUsage >= settings->GetCollectMemUsageThreshold() / 1024)
    {
        QString cmdName = makeCmdName("memstat_cmd_");
        WoFileIncludeCollector collector(m_wb, krt::utf16(cmdName), memUsage);
        if (m_pCtx)
            collector.addCmdTraceId(m_pCtx->getCmdTraceId().c_str()).collect();
        else
            collector.collect();
    }

    if (m_callback)
        m_callback(ms, memUsage, (m_memBegin >> 10));
}

QString CmdTimeMemStat::makeCmdName(const char* prefixName)
{
    QString name(prefixName);
    if (!m_pCtx)
        return name + m_cmdName;

    if(m_isExec)
    {
        name += "exec_";
    }
    else
    {
        name += (gs_procType == WebProcTypeMaster ? "query_master_": "query_slave_");
    }
    if (m_pCtx->isExecDirect())
    {
        name += "log_";
    }
    IWoCallBack* pCb = _kso_GetWoCallBack();
    if (pCb && pCb->isBreak())
        name += "break_";

    name += m_cmdName;
    return name;
}
///////////////////////////////////////////////////////////////////
CmdTimeStat::CmdTimeStat(KEtWorkbook * wb, KEtRevisionContext* pCtx, CmdCallback callback)
: m_memStat(wb, pCtx, [callback](unsigned int ms, unsigned int memUsedKb, unsigned int memKb) {
    if (callback)
        callback(ms);
})
{
    
}

///////////////////////////////////////////////////////////////////
CFTimeStat::CFTimeStat(KEtWorkbook * wb, PCWSTR name)
    : m_wb(wb)
    , m_name(krt::fromUtf16(name))
    , m_begin(std::chrono::steady_clock::now())
    , m_cellCnt(0)
    , m_travelCnt(0)
    , m_calcCnt(0)
    , m_calcOverThresholdCnt(0)

{
}

CFTimeStat::~CFTimeStat()
{
    int ms = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
#ifdef DEBUG
    WOLOG_DEBUG << "[" << m_name << "] elapsed: " << ms << "ms" << ", cellCnt: " << m_cellCnt
                << ", travelCnt: " << m_travelCnt << ", calcCnt: " << m_calcCnt
                << ", calcOverThresholdCnt: " << m_calcOverThresholdCnt;
#endif

    wo::WoEtSetting * settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
    int sampleRate = 0;
    if (settings && settings->isCollect(ms, &sampleRate, settings->GetCFQueryTimeRand()))
    {
        QString collectName = makeName();
        WoFileIncludeCollector collector(m_wb, krt::utf16(collectName), ms);

        if (m_cellCnt || m_travelCnt || m_calcCnt || m_calcOverThresholdCnt) {
            collector.addCount2(m_cellCnt).addFileSize1(m_travelCnt).addFileSize2(m_calcCnt).addThreadCount(m_calcOverThresholdCnt);
        }

        collector.addSampleRate(sampleRate).collect();
    }
}

QString CFTimeStat::makeName()
{
    QString name("behaviour_");
    name += (gs_procType == WebProcTypeMaster ? "query_master_": "query_slave_");
    IWoCallBack* pCb = _kso_GetWoCallBack();
    if (pCb && pCb->isBreak())
        name += "break_";

    name += m_name;
    return name;
}

void CFTimeStat::setStatCount(int cellCnt, int travelCnt, int calcCnt, int calcOverThresholdCnt)
{
    m_cellCnt = cellCnt;
    m_travelCnt = travelCnt;
    m_calcCnt = calcCnt;
    m_calcOverThresholdCnt = calcOverThresholdCnt;
}

///////////////////////////////////////////////////////////////////
BreakTimeStat::BreakTimeStat()
: m_isStart(false)
, m_isBreak(false)
{
}

void BreakTimeStat::restart()
{
    m_isStart = true;
    onSetBreak();
}

void BreakTimeStat::onSetBreak()
{
    if (!m_isStart) return;

    IWoCallBack* pCb = _kso_GetWoCallBack();
    if (!pCb) return;

    { // setBreak是异步线程。防止restart和setBreak多线程问题。
        ThreadLiteLib::SpinLockHelper locker(&m_lock);
        bool newVal = pCb->isBreak();
        if (m_isBreak == newVal) return;

        if (newVal)
            m_begin = std::chrono::steady_clock::now();
        m_isBreak = newVal;
        WOLOG_INFO << "BreakTimeStat: " << m_isBreak;
    }
}

unsigned int BreakTimeStat::takeElapseMS()
{
    if (!m_isStart) return 0;
    m_isStart = false;

    std::chrono::steady_clock::time_point begin;
    bool isBreak;
    {
        ThreadLiteLib::SpinLockHelper locker(&m_lock);
        isBreak = m_isBreak;
        begin = m_begin;
    }

    if (isBreak)
    {
        unsigned int ms = (std::chrono::steady_clock::now() - m_begin) / std::chrono::milliseconds(1);
        WOLOG_INFO << "BreakTimeStat: " << m_isBreak << ", ms: " << ms;
        return ms;
    }
    else
     {
        return 0;
     }
}
///////////////////////////////////////////////////////////////////
static void AddComponentInfo(BinWriter& binWriter, IBook* pBook)
{
    if (pBook)
    {
        BMP_PTR bmpPtr = pBook->GetBMP();
        binWriter.addKey("project");
        if (bmpPtr->bDbSheet)
            binWriter.addString(__X("dbt"));
        else if (bmpPtr->bKsheet)
            binWriter.addString(__X("ksheet"));
        else
            binWriter.addString(__X("et"));
    }
}

void CollectInfo(wo::KEtWorkbook* wb, const WCHAR *szName, int count)
{
    WoFileIncludeCollector collector(wb, szName, count);
    collector.collect();
}

void CollectInfo(wo::KEtWorkbook* wb, const WCHAR *szName, int count, int count2)
{
    WoFileIncludeCollector collector(wb, szName, count);
    collector.addCount2(count2).collect();
}

void CollectInfoRand(wo::KEtWorkbook* wb, const WCHAR *szName, int count, wo::util::RandDist * rand)
{
    wo::WoEtSetting * settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
    int sampleRate = 0;
    if (settings->isCollect(count, &sampleRate, rand))
    {
        WoFileIncludeCollector collector(wb, szName, count);
        collector.addSampleRate(sampleRate);
        collector.collect();
    }
}

void CollectInfo(wo::KEtWorkbook* wb, const WCHAR *szName, int count, const WCHAR *szTraceId)
{
    WoFileIncludeCollector collector(wb, szName, count);
    collector.addCmdTraceId(szTraceId).collect();
}

void CollectCmdInfo(KEtRevisionContext * ctx, const WCHAR *szName, int count)
{
    WoFileIncludeCollector collector(ctx->woWorkbook(), szName, count);
    collector.addCmdTraceId(ctx->getCmdTraceId().c_str()).collect();
}

void CollectAiColInfo(INT32 count, IBook* pBook)
{
    binary_wo::BinWriter binWriter;
    binWriter.addKey("event");
    binWriter.addString(__X("ai_column"));
    binWriter.addKey("value");
    binWriter.addInt32(count);
    AddComponentInfo(binWriter, pBook);

    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), binWriter.writeLength()};

    WOLOG_INFO << "index_aievaluate: " << "ai_column";
    gs_callback->collectInfo("index_aievaluate", &slice);
}

void CollectAiEafInfo(const QString &fileId, const EafResultInfo& info, INT32 tokenInput, INT32 tokenOutput, INT32 tokenTotal
    , PCWSTR value, IBook* pBook /*= nullptr*/)
{
    if (nullptr == info.funcName || __Xc('0') == *info.funcName)
    {
        ASSERT(FALSE);
        return;
    }

    binary_wo::BinWriter binWriter;
    binWriter.addKey("event");
    binWriter.addString(__X("ai_formula"));
    binWriter.addKey("fileid");
    binWriter.addString(krt::utf16(fileId));
    if (xstrlen(info.funcName) > 6) // "WPSAI_"
    {
        binWriter.addKey("type");
        binWriter.addString(info.funcName + 6);
    }
    else
    {
        ASSERT(FALSE);
    }
    binWriter.addKey("tokeninput");
    binWriter.addInt32(tokenInput);
    binWriter.addKey("tokenoutput");
    binWriter.addInt32(tokenOutput);
    binWriter.addKey("tokentotal");
    binWriter.addInt32(tokenTotal);
    binWriter.addKey("value");
    binWriter.addString(value);
    binWriter.addKey("duration");
    binWriter.addInt32(info.duration);
    binWriter.addKey("result");
    binWriter.addString(alg::const_error_token_assist::is_type(info.token) ? __X("fail") : __X("success"));
    AddComponentInfo(binWriter, pBook);

    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), binWriter.writeLength()};

    WOLOG_INFO << "index_aievaluate: " << "ai_formula";
    gs_callback->collectInfo("index_aievaluate", &slice);
}

void CollectEtXfs(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectEtXfs]";
        return;
    }
    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectEtXfs]";
        return;
    }
    BMP_PTR pBmp = pKEtWorkbook->GetBMP();
    if (nullptr == pBmp)
    {
        WOLOG_ERROR << "nullptr BMP_PTR in [CollectEtXfs]";
        return;
    }
    if (FALSE == pBmp->bDbSheet)
    {
        IBookStake* pBookStake = pKEtWorkbook->GetCoreWorkbook()->GetBook()->GetWoStake();
        if (nullptr == pBookStake)
        {
            WOLOG_ERROR << "nullptr IBookStake in [CollectEtXfs]";
            return;
        }
        
        int sz = pBookStake->getXFsCnt();
        if (sz < 50)
        {
            WOLOG_INFO << "[CollectEtXfs] xfs count is " << sz;
            return;
        }
        binWriter.beginStruct();
        {
            binWriter.addStringField(__X("fileinclude"), "name");
            binWriter.beginStruct("params");
            {
                binWriter.addKey("name");
                binWriter.addString(__X("XFsCount"));
                binWriter.addKey("count");
                binWriter.addInt32(pBookStake->getXFsCnt());
                binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");
            }
            binWriter.endStruct();
        }
        binWriter.endStruct();
    }
}

void CollectEtStyles(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
        return;

    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
        return;

    BMP_PTR pBmp = pKEtWorkbook->GetBMP();
    if (nullptr == pBmp)
        return;

    if (FALSE == pBmp->bDbSheet)
    {
        IBookStake* pBookStake = pKEtWorkbook->GetCoreWorkbook()->GetBook()->GetWoStake();
        if (nullptr == pBookStake)
            return;
        
        binWriter.beginStruct();
        {
            binWriter.addStringField(__X("fileinclude"), "name");
            binWriter.beginStruct("params");
            {
                binWriter.addKey("name");
                binWriter.addString(__X("styleCnt"));
                binWriter.addKey("count");
                binWriter.addInt32(pBookStake->getStyleCnt());
                binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");
            }
            binWriter.endStruct();
        }
        binWriter.endStruct();
    }
}

void CollectImagepoolInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
        return;

    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
        return;

    BMP_PTR pBmp = pKEtWorkbook->GetBMP();
    if (nullptr == pBmp)
        return;

    if (FALSE == pBmp->bDbSheet)
    {
        IBook *pBook = pKEtWorkbook->GetCoreWorkbook()->GetBook();
        IBookStake* pBookStake = pBook->GetWoStake();
        if (nullptr == pBookStake)
            return;
        
        BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
        if (!info || info->imageSize <= 0)
            return;

        quint64 memusage = kpt::chooseSystem(kpt::RasterGraphics).imagePoolMemUsage();
        binWriter.beginStruct();
        {
            binWriter.addStringField(__X("fileinclude"), "name");
            binWriter.beginStruct("params");
            {
                binWriter.addKey("name");
                binWriter.addString(__X("imagepool_k"));
                binWriter.addKey("count");
                binWriter.addInt32(static_cast<int>(memusage / 1024)); // k
                binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");
            }
            binWriter.endStruct();
        }
        binWriter.endStruct();
    }
}

void CollectProcPSS(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
        return;

    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
        return;

    BMP_PTR pBmp = pKEtWorkbook->GetBMP();
    if (nullptr == pBmp)
        return;

    int64_t pss = MemoStatProcPSS() / 1024;
    if (pss <=0)
        return;

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        {
            binWriter.addKey("name");
            binWriter.addString(__X("pss_mem_k"));
            binWriter.addKey("count");
            binWriter.addInt32(static_cast<int>(pss)); // k
            binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

#define EVENT_DISPATCHER_INFO_COLLECT(value)                                 \
binWriter.beginStruct();                                                     \
{                                                                            \
    binWriter.addStringField(__X("fileinclude"), "name");                    \
    binWriter.beginStruct("params");                                         \
    {                                                                        \
        binWriter.addKey("name");                                            \
        binWriter.addString(__X("##value"));                                 \
        binWriter.addKey("count");                                           \
        binWriter.addInt32(pInfo->value);                                    \
        binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid"); \
    }                                                                        \
    binWriter.endStruct();                                                   \
}                                                                            \
binWriter.endStruct();

void CollectEventDispatcherInfo(wo::KEtApplication* app, binary_wo::BinWriter& binWriter)
{
    if (nullptr == app)
    {
        WOLOG_ERROR << "nullptr KEtApplication in [CollectEventDispatcherInfo]";
        return;
    }

    wo::KEtWorkbooks* wbs = app->GetWorkbooks();
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectEventDispatcherInfo]";
        return;
    }

    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectEventDispatcherInfo]";
        return;
    }

    BMP_PTR pBmp = pKEtWorkbook->GetBMP();
    if (nullptr == pBmp)
    {
        WOLOG_ERROR << "nullptr BMP_PTR in [CollectEventDispatcherInfo]";
        return;
    }

    const EventDispatcherCallInfo* pInfo = app->getEventDispatcherCallInfo();
    if (nullptr == pInfo)
    {
        WOLOG_ERROR << "nullptr EventDispatcherCallInfo in [CollectEventDispatcherInfo]";
        return;
    }

    EVENT_DISPATCHER_INFO_COLLECT(processEventsCnt)
    EVENT_DISPATCHER_INFO_COLLECT(hasPendingEventsCnt)
    EVENT_DISPATCHER_INFO_COLLECT(registerSocketNotifierCnt)
    EVENT_DISPATCHER_INFO_COLLECT(unregisterSocketNotifierCnt)
    EVENT_DISPATCHER_INFO_COLLECT(registerTimerCnt)
    EVENT_DISPATCHER_INFO_COLLECT(unregisterTimerCnt)
    EVENT_DISPATCHER_INFO_COLLECT(unregisterTimersCnt)
    EVENT_DISPATCHER_INFO_COLLECT(registeredTimersCnt)
    EVENT_DISPATCHER_INFO_COLLECT(remainingTimeCnt)
    EVENT_DISPATCHER_INFO_COLLECT(wakeUpCnt)
    EVENT_DISPATCHER_INFO_COLLECT(interruptCnt)
    EVENT_DISPATCHER_INFO_COLLECT(flushCnt)
}


namespace
{
struct StrHasher
{
    size_t operator()(const ks_wstring& userId) const
    {
        return alg::HashWString(userId.c_str());
    }
};

struct StrEqual
{
    bool operator()(const ks_wstring& lhs, const ks_wstring& rhs) const
    {
        return xstrcmp(lhs.c_str(), rhs.c_str()) == 0;
    }
};

using WstrSet = std::unordered_set<ks_wstring, StrHasher, StrEqual>;
using Wstr2WStrSetMap = std::unordered_map<ks_wstring, WstrSet, StrHasher, StrEqual>;
class ImportrangeParamCollector : public IImportrangeParamAcpt
{
public:
    explicit ImportrangeParamCollector(Wstr2WStrSetMap& map) : m_map(map) {}
	STDPROC_(INT) Do(PCWSTR url, PCWSTR range) override
    {
        m_map[url].insert(range);
        return 0;
    }
private:
    Wstr2WStrSetMap& m_map;
};
class StatImportrangeParam : public ICellAcpt
{
public:
    explicit StatImportrangeParam(IBookOp* pBookOp, IDX sheetIdx, ImportrangeParamCollector& collector)
        : m_spBookOp(pBookOp)
        , m_sheetIdx(sheetIdx)
        , m_collector(collector)
    {}
    STDIMP_(INT) Do(ROW row, COL col)
    {
        return m_spBookOp->EnumCellImportrangeUrls(m_sheetIdx, row, col, &m_collector);
    }
private:
    ks_stdptr<IBookOp> m_spBookOp;
    IDX m_sheetIdx;
    ImportrangeParamCollector& m_collector;
};

namespace
{
QString desensitizedRange(PCWSTR range)
{
	QStringList list = krt::fromUtf16(range).split('!');
	if (list.size() < 2)
		return QString{};
	return list[1];
}
}

void collectImportrangeParamInfo(binary_wo::BinWriter& binWriter, wo::KEtWorkbooks* wbs, IBook* pBook, const Wstr2WStrSetMap& importrangeParams)
{
    if (importrangeParams.empty())
        return;

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        {
            FileInfoCollector::AddComponentInfo(binWriter, pBook);
            {
                QString value(QStringLiteral("behaviour_importrange"));
                for (auto it = importrangeParams.cbegin(); it != importrangeParams.cend(); ++it)
                {
                    if (it->second.empty())
                        continue;

                    PCWSTR url = it->first.c_str();
                    value.append(QStringLiteral("_ranges_%1").arg(it->second.size()));
                    for (const auto& rgStr : it->second)
                    {
                        value.append(QStringLiteral("_%1").arg(desensitizedRange(rgStr.c_str())));
                    }
                }
                binWriter.addStringField(krt::utf16(value), "name");
                WOLOG_INFO << "fileinclude: " << value;
            }
            binWriter.addStringField(pBook->GetWoStake()->GetCreatorUserID(), "fmla");
            binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}
}

void CollectEtFormula(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectEtFormula]";
        return;
    }
    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectEtFormula]";
        return;
    }
    BMP_PTR pBmp = pKEtWorkbook->GetBMP();
    if (nullptr == pBmp)
    {
        WOLOG_ERROR << "nullptr BMP_PTR in [CollectEtFormula]";
        return;
    }
    if (FALSE == pBmp->bDbSheet)
    {
        class KEtFmlCellValueAcpt : public ICellValueAcpt
        {
        public:
            explicit KEtFmlCellValueAcpt(IBookOp* pBookOp, IDX sheetIdx, std::unordered_map<int, int>& stat)
                : m_spBookOp(pBookOp)
                , m_sheetIdx(sheetIdx)
                , m_stat(stat)
            {}
            STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
            {
                if (nullptr == pToken)
                    return 0;
                ks_stdptr<IFormula> spFormula;
                VS(m_spBookOp->GetCellFormula(m_sheetIdx, row, col, &spFormula, NULL));
                if (nullptr == spFormula)
                    return 0;
                ks_stdptr<ITokenVectorInstant> tvi;
                spFormula->GetContent(nullptr, &tvi, nullptr);
                if (nullptr == tvi)
                    return 0;
                exec_token_vector etv(tvi.get());
                for (int i = 0, c = etv.size(); i < c; ++i)
                {
                    const_token_ptr item = etv.get(i);
                    if (false == alg::const_function_token_assist::is_type(item))
                        continue;
                    alg::const_function_token_assist cfta(item);
                    ++m_stat[cfta.get_id()];
                }
                return 0;
            }
        private:
            ks_stdptr<IBookOp> m_spBookOp;
            IDX m_sheetIdx;
            std::unordered_map<int, int>& m_stat;
        };
        class StatAiFmlColumn : public ICellValueAcpt
        {
        public:
            StatAiFmlColumn(IBookOp* pBookOp, IDX sheetIdx, Database::FieldContext& ctx, std::vector<int>& stat)
                : m_spBookOp(pBookOp), m_sheetIdx(sheetIdx), m_fldCtx(ctx), m_stat(stat) {}
            STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
            {
                if (nullptr == pToken)
                    return 0;
                ks_stdptr<IFormula> spFormula;
                VS(m_spBookOp->GetCellFormula(m_sheetIdx, row, col, &spFormula, NULL));
                if (nullptr == spFormula)
                    return 0;

                RANGE rg(m_spBookOp->LeakBook()->GetBMP());
                rg.SetCell(m_sheetIdx, row, col);
                wo::util::VALIDATION_Wrap dv;
                m_spBookOp->GetDataValidation(rg, m_sheetIdx, row, col, &dv, NULL, &rg, NULL);
                Database::IDbField *pField = nullptr;
                Database::FieldsManager *pDbMgr = Database::FieldsManager::Instance();
                bool isAiCol = false;
                if (pField = pDbMgr->IdentifyAll(&m_fldCtx, rg, dv))
                {
                    const Database::FieldType type = pField->GetType();
                    if (type == Database::dftAI)
                        isAiCol = true;
                }
                if (not isAiCol)
                    return 0;

                ks_stdptr<ITokenVectorInstant> tvi;
                spFormula->GetContent(nullptr, &tvi, nullptr);
                if (nullptr == tvi)
                    return 0;
                exec_token_vector etv(tvi.get());
                for (int i = etv.size() - 1; i >= 0; --i)
                {
                    const_token_ptr item = etv.get(i);
                    if (not alg::const_function_token_assist::is_type(item))
                        continue;
                    alg::const_function_token_assist fta(item);
                    DWORD funcId = fta.get_id();
                    if (IsWpsAiFormula(funcId))
                    {
                        ++m_stat[funcId - FNID_WPSAI_EXTRACT];
                        return 0;
                    }
                }
                return 0;
            }
        private:
            ks_stdptr<IBookOp> m_spBookOp;
            IDX m_sheetIdx;
            Database::FieldContext& m_fldCtx;
            std::vector<int>& m_stat;
        };

        _Workbook* pWorkbook = pKEtWorkbook->GetCoreWorkbook();
        if (nullptr == pWorkbook)
        {
            WOLOG_ERROR << "nullptr _Workbook in [CollectEtFormula]";
            return;
        }
        IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
        if (nullptr == pWorksheets)
        {
            WOLOG_ERROR << "nullptr IKWorksheets in [CollectEtFormula]";
            return;
        }
        IBook* pBook = pWorkbook->GetBook();
        if (nullptr == pBook)
        {
            WOLOG_ERROR << "nullptr IBook in [CollectEtFormula]";
            return;
        }
        int cnt = pWorksheets->GetSheetCount();
        std::unordered_map<int, int> stat;
        constexpr size_t aiFmlTypes = (FNID_WPSAI_ASSOCTRANS - FNID_WPSAI_EXTRACT + 1);
        std::vector<int> statCol(aiFmlTypes, 0);
        Database::FieldContext fieldContext(pKEtWorkbook, nullptr);
        Wstr2WStrSetMap importrangeParams;
        for (int shtIdx = 0; shtIdx < cnt; shtIdx++)
        {
            ks_castptr<etoldapi::_Worksheet> spWorksheet = pWorksheets->GetSheetItem(shtIdx);
            if (nullptr == spWorksheet)
            {
                WOLOG_ERROR << "nullptr _Worksheet in [CollectEtFormula]";
                return;
            }
            ISheet* pSheet = spWorksheet->GetSheet();
            if (nullptr == pSheet)
            {
                WOLOG_ERROR << "ISheet _Worksheet in [CollectEtFormula]";
                return;
            }
            IDX sheetIdx = INVALIDIDX;
            pSheet->GetIndex(&sheetIdx);
            if (INVALIDIDX == sheetIdx)
                continue;
            RECT rcUsed = {0};
            pSheet->CalcUsedScale(&rcUsed);
            RANGE rgUsed = Rect2Range(rcUsed, sheetIdx, pSheet->GetBMP());
            if (FALSE == rgUsed.IsValid())
                continue;
            et_sdptr<ISheetEnum> spSheetEnum;
            pSheet->CreateEnum(&spSheetEnum);
            if (nullptr == spSheetEnum)
                continue;
            KEtFmlCellValueAcpt fmlCntCollector(pBook->LeakOperator(), sheetIdx, stat);
            spSheetEnum->EnumCellValue(rgUsed, &fmlCntCollector);

            RANGE rg4JudgeCol(rgUsed);
            rg4JudgeCol.SetRowFromTo(1, 1);
            StatAiFmlColumn statAiFmlColumn(pBook->LeakOperator(), sheetIdx, fieldContext, statCol);
            spSheetEnum->EnumCellValue(rg4JudgeCol, &statAiFmlColumn);

            ImportrangeParamCollector irpc(importrangeParams);
            StatImportrangeParam sirp(pBook->LeakOperator(), sheetIdx, irpc);
            spSheetEnum->EnumCell(rgUsed, &sirp);
        }

        collectImportrangeParamInfo(binWriter, wbs, pBook, importrangeParams);

        ks_stdptr<IFunction> spFunction;
        VS(_funclib_CreateObject(CLSID_KFunction, IID_IFunction, (void**)&spFunction));
        if (spFunction)
        {
            binWriter.beginStruct();
            {
                binWriter.addStringField(__X("fileinclude"), "name");
                binWriter.beginStruct("params");
                {
                    FileInfoCollector::AddComponentInfo(binWriter, pBook);
                    if (stat.empty())
                    {
                        binWriter.addStringField(__X("behaviour_formula_0"), "name");
                        WOLOG_INFO << "fileinclude: " << __X("behaviour_formula_0");
                    }
                    else
                    {
                        QString value(QStringLiteral("behaviour_formula"));
                        for (auto it = stat.cbegin(); it != stat.cend(); ++it)
                        {
                            const FUNCPROTOTYPE* pFPT = NULL;
                            HRESULT hr = spFunction->GetFuncInfo(it->first, &pFPT);
                            if (SUCCEEDED(hr) && pFPT != NULL)
                                value.append(QStringLiteral("_%1_%2").arg(pFPT->FuncName).arg(it->second));
                        }
                        binWriter.addStringField(krt::utf16(value), "name");
                        WOLOG_INFO << "fileinclude: " << value;
                    }
                    binWriter.addStringField(pBook->GetWoStake()->GetCreatorUserID(), "fmla");
                    binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");
                }
                binWriter.endStruct();
            }
            binWriter.endStruct();

            // 上报 AI 公式使用情况
            std::vector<PCWSTR> funcNames(aiFmlTypes, nullptr);
            for (int i = 0; i < aiFmlTypes; ++i)
            {
                const FUNCPROTOTYPE* pFPT = NULL;
                HRESULT hr = spFunction->GetFuncInfo(FNID_WPSAI_EXTRACT + i, &pFPT);
                if (SUCCEEDED(hr) && pFPT != NULL)
                    funcNames[i] = pFPT->FuncStdName;
            }

            for (int i = 0; i < aiFmlTypes; ++i)
            {
                if (nullptr == funcNames[i])
                    continue;
                if (xstrlen(funcNames[i]) <= 6)
                    continue;
                int aiFmlCnt = stat[i + aiFmlTypes];
                if (aiFmlCnt > 0)
                {
                    binWriter.beginStruct();
                    {
                        binWriter.addStringField(__X("index_aievaluate"), "name");
                        binWriter.beginStruct("params");
                        {
                            binWriter.addKey("event");
                            binWriter.addString(__X("ai_formula_count"));
                            binWriter.addKey("type");
                            binWriter.addString(funcNames[i] + 6);
                            binWriter.addKey("value");
                            binWriter.addInt32(aiFmlCnt);
                        }
                        binWriter.endStruct();
                    }
                    binWriter.endStruct();
                }
                if (statCol[i] > 0)
                {
                    binWriter.beginStruct();
                    {
                        binWriter.addStringField(__X("index_aievaluate"), "name");
                        binWriter.beginStruct("params");
                        {
                            binWriter.addKey("event");
                            binWriter.addString(__X("ai_column_count"));
                            binWriter.addKey("type");
                            binWriter.addString(funcNames[i] + 6);
                            binWriter.addKey("value");
                            binWriter.addInt32(statCol[i]);
                        }
                        binWriter.endStruct();
                    }
                    binWriter.endStruct();
                }
            }
        }
    }
}

void CollectQRColCount(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectQRColCount]";
        return;
    }
    wo::KEtWorkbook* pEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectQRColCount]";
        return;
    }

    IBook *pBook = pEtWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook->GetBMP()->bKsheet)
        return;

    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    if (sheetCount == 0)
        return;

    int qrColCount = 0;
    Database::FieldContext fieldContext(pEtWorkbook, nullptr);
    for (INT i = 0; i < sheetCount; i++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        if (!spSheet)
            continue;

        if (spSheet->IsGridSheet())
            qrColCount += Database::Utils::GetQRColCount(&fieldContext, i);
    }

    if (qrColCount > 0)
    {
        binWriter.beginStruct();
        {
            binWriter.addStringField(__X("fileinclude"), "name");
            binWriter.beginStruct("params");
            {
                binWriter.addStringField(__X("application_qrlabel"), "name");
                binWriter.addInt32Field(qrColCount, "count");
                binWriter.addStringField(pEtWorkbook->getFileId(), "fileid");
                FileInfoCollector::AddComponentInfo(binWriter, pBook);
            }
            binWriter.endStruct();
        }
        binWriter.endStruct();
    }
}

void WriteSvrFileinclude(binary_wo::BinWriter& binWriter, PCWSTR name, wo::KEtWorkbook* pEtWorkbook, int cnt/* = 0*/, int cnt2 /* = -1 */) 
{
    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        {
            binWriter.addStringField(name, "name");
            binWriter.addInt32Field(cnt, "count");
            if (cnt2 >= 0)
                binWriter.addInt32Field(cnt2, "count2");
            binWriter.addStringField(pEtWorkbook->getFileId(), "fileid");
            WOLOG_INFO << name << ": " << cnt;
            FileInfoCollector::AddComponentInfo(binWriter, pEtWorkbook->GetCoreWorkbook()->GetBook());
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

void CollectStaleInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectStaleInfo]";
        return;
    }
    wo::KEtWorkbook* pEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectStaleInfo]";
        return;
    }
    int cnt = pEtWorkbook->getStaleOnNotExecCount();
    if (cnt > 0)
    {
        WriteSvrFileinclude(binWriter, __X("behaviour_non_exec_stale"), pEtWorkbook, cnt);
    }
}

void CollectVersionInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectVersionInfo]";
        return;
    }
    wo::KEtWorkbook* pEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectVersionInfo]";
        return;
    }
    int cnt = pEtWorkbook->getReinitCount();
    if (cnt > 0)
        WriteSvrFileinclude(binWriter, __X("behaviour_reinit_count"), pEtWorkbook, cnt);
    
    cnt = pEtWorkbook->getInspectFailCount();
    if (cnt > 0)
        WriteSvrFileinclude(binWriter, __X("behaviour_inspect_fail_count"), pEtWorkbook, cnt);
        
    cnt = pEtWorkbook->getOptInitCount();
    if (cnt > 0)
        WriteSvrFileinclude(binWriter, __X("behaviour_opt_init_count"), pEtWorkbook, cnt);
        
    cnt = pEtWorkbook->coreMetric().get64Metrics(K64MetricItem::kAllOutOfLimitSerVerCnt);
    if (cnt > 0)
        WriteSvrFileinclude(binWriter, __X("behaviour_serial_outoflimit_cnt"), pEtWorkbook, cnt);
        
    cnt = pEtWorkbook->getMaxSerialVerCount();
    WebInt bwLen = pEtWorkbook->getMaxSerialVerBwLen();
    if (cnt > 0 || bwLen > 0)
        WriteSvrFileinclude(binWriter, __X("behaviour_max_serial_ver_count"), pEtWorkbook, cnt, bwLen);
    
    WebInt verCnt = pEtWorkbook->getSerialVerCount();
    WebInt hitCnt = pEtWorkbook->getHitVerCount();
    if (verCnt > 0)
        WriteSvrFileinclude(binWriter, __X("behaviour_serial_ver_count"), pEtWorkbook, verCnt, hitCnt);
}

void CollectDbPrepareUser(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectDbPrepareUser]";
        return;
    }
    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectDbPrepareUser]";
        return;
    }
    IDBSheetCtx* pDbCtx = _appcore_GainDbSheetContext();
    if (nullptr == pDbCtx)
    {
        WOLOG_ERROR << "nullptr IDBSheetCtx in [CollectDbPrepareUser]";
        return;
    }
    int callCnt = pDbCtx->GetPrepareCurUserCalled();
    if (0 == callCnt)
        return;
    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        {
            QString value(QStringLiteral("behaviour_db_prepare_user_call_time_%1_updated_view_%2_new_visible_records_%3")
                .arg(callCnt).arg(pDbCtx->GetPrepareCurUserUpdatedViewCnt()).arg(pDbCtx->GetPrepareCurUserNewVisibleObj()));
            binWriter.addStringField(krt::utf16(value), "name");
            WOLOG_INFO << value;

            binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

void CollectLinkFormInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{ 
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectLinkFormInfo]";
        return;
    }
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    if (nullptr == wb)
    {
        WOLOG_ERROR << "nullptr wb in [CollectLinkFormInfo]";
        return;
    }

	IBook* pBook = wb->GetCoreWorkbook()->GetBook();
	if(!wo::isFormBook(pBook))
		return;

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("index_experience"), "name");
        binWriter.beginStruct("params");
        {
            binWriter.addStringField(__X("link_form"), "event");
            binWriter.addStringField(wb->getFileId(), "fileid");
            FileInfoCollector::AddComponentInfo(binWriter, pBook);
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

void CollectCondFmtCacheInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectCondFmtCacheInfo]";
        return;
    }
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    if (nullptr == wb)
    {
        WOLOG_ERROR << "nullptr wb in [CollectCondFmtCacheInfo]";
        return;
    }

    ConditionFormatCache *pCfCache = wb->getCfCache();
    if (!pCfCache->IsEnabled())
        return;

    double hitRate = pCfCache->GetCacheHitRate();
    if (hitRate == 0.0)
        return;

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        {
            WOLOG_INFO << "CondFmtCache hit rate: " << hitRate;
            QString name = QString("condition_format_cache_hit_rate_%1").arg(hitRate);
            binWriter.addStringField(krt::utf16(name), "name");
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}
void CollectUndoRedoFailInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectUndoRedoFailInfo]";
        return;
    }
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    if (nullptr == wb)
    {
        WOLOG_ERROR << "nullptr wb in [CollectUndoRedoFailInfo]";
        return;
    }

	IBook* pBook = wb->GetCoreWorkbook()->GetBook();
    if (not pBook->GetBMP()->bKsheet && not pBook->GetBMP()->bDbSheet)
        return;

    wo::KEtWorkbook::UndoRedoStat stat = wb->GetUndoRedoStat();

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        {
            FileInfoCollector::AddComponentInfo(binWriter, pBook);

            QString value(QStringLiteral("behaviour_undo_fail_%1_total_%2_redo_fail_%3_total_%4")
                .arg(stat.m_undoFailTime)
                .arg(stat.m_undoTotalCnt)
                .arg(stat.m_redoFailTime)
                .arg(stat.m_redoTotalCnt))
            ;
            binWriter.addStringField(krt::utf16(value), "name");
            WOLOG_INFO << "fileinclude: " << value;

            binWriter.addStringField(pBook->GetWoStake()->GetCreatorUserID(), "fmla");
            binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");

            
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

static void WriteName(PCWSTR name, binary_wo::BinWriter& binWriter, wo::KEtWorkbook* pWb, int count = 0)
{

    if (!pWb)
        return;

    if (!pWb->GetCoreWorkbook())
        return;

    IBook* pBook = pWb->GetCoreWorkbook()->GetBook();
    if (!pBook)
        return;

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("svr_fileinclude"), "name");
        binWriter.beginStruct("params");
        {
            binWriter.addStringField(name, "name");
            if (count)
                binWriter.addInt32Field(count, "count");

            binWriter.addStringField(pWb->getFileId(), "fileid");
            FileInfoCollector::AddComponentInfo(binWriter, pBook);
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

void CollectCrooBookInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        return;
    }
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    if (nullptr == wb)
    {
        return;
    }

    IBook* pBook = wb->GetCoreWorkbook()->GetBook();

    int sheetCrossBookCount = 0;
    int tableCrossBookCount = 0;
    pBook->GetCrossBookCountInfo(sheetCrossBookCount, tableCrossBookCount);

    WOLOG_INFO << "sheetCrossBookCount: " << sheetCrossBookCount << "   tableCrossBookCount:" << tableCrossBookCount;

    int allCrossBookCount = sheetCrossBookCount + tableCrossBookCount;

    if (allCrossBookCount <= 0)
    {
        return;
    }

    QString value(QStringLiteral("quote_otherfile_%1").arg(allCrossBookCount));
    WriteName(krt::utf16(value), binWriter, wb);
    WriteName(__X("behavior_crossbook_count"), binWriter, wb, allCrossBookCount);

    if (sheetCrossBookCount > 0)
    {
        QString value1(QStringLiteral("quote_otherfile_xlssheet_%1").arg(sheetCrossBookCount));
        WriteName(krt::utf16(value1), binWriter, wb);
    }

    if (tableCrossBookCount > 0)
    {
        QString value1(QStringLiteral("quote_otherfile_dbsheet_%1").arg(tableCrossBookCount));        
        WriteName(krt::utf16(value1), binWriter, wb);
    }



}

void GetApiShape(IKShape* pShape, KsoShape** ppShape)
{
	drawing::ShapeTreeControl* pCtl = getShapeTreeControl(pShape);
	ks_stdptr<IKCoreObject> spParent;
	pCtl->getSupLayerControl()->getCoreObject(pCtl->getLayer()->getSupLayer()->getModel(), &spParent);
	if (spParent != nullptr)
		getShapeTreeApiFactory(pShape)->CreateShape(spParent, pShape, ppShape);
}

using CollectShapeCallback = std::function<bool (drawing::AbstractShape *)>;

oldapi::KsoShapeType _getShapeType(drawing::AbstractShape* pShapeTree)
{
    IKShape* pShape = static_cast<IKShape*>(pShapeTree);
    if (!pShape) return ksoShapeTypeMixed;

    ks_stdptr<KsoShape> spKsoShape;
    GetApiShape(pShape, &spKsoShape);

    ks_stdptr<Shape> spShape = spKsoShape;
    oldapi::KsoShapeType shapeType = ksoShapeTypeMixed;
    spShape->get__Type(&shapeType);
    return shapeType;
}

bool HasNormalShape(drawing::AbstractShape* pShapeTree)
{
    return _getShapeType(pShapeTree) == ksoAutoShape;
}

bool HasTextBoxOrEffect(drawing::AbstractShape* pShapeTree)
{
    return _getShapeType(pShapeTree) == ksoTextBox;
}

bool HasPivotTableSlicer(drawing::AbstractShape* pShapeTree)
{
    return _getShapeType(pShapeTree) == ksoSlicer;
}

bool HasShape(drawing::AbstractShape* pShapeTree)
{
    return true;
}
bool HasPicture(drawing::AbstractShape* pShapeTree)
{
    return pShapeTree->isPicture();
}

bool HasPivotChart(drawing::AbstractShape* pShapeTree)
{
    if (!pShapeTree->hasChart()) return false;
    ks_castptr<chart::KCTChartLayer> chartLayer = pShapeTree->getChild(0);
	if (!chartLayer) return false;

	chart::KCTChart* pChart = chartLayer->ensureChart();
	if (!pChart) return false;

    return pChart->isPivotChart();
}

bool _walkThroughShapeTree(drawing::AbstractShape* pShapeTree, bool bNeedCount, int& count, const CollectShapeCallback& callback)
{
	if (pShapeTree->isGroupShape())
	{
        bool ret = false;
		drawing::GroupShape* pShapeGroup = static_cast<drawing::GroupShape*>(pShapeTree);
		int childCnt = pShapeGroup->childCount();
		for (int childIdx = 0; childIdx < childCnt; childIdx++)
		{
			drawing::AbstractShape* childShape = pShapeGroup->childAt(childIdx);
		    ret |= _walkThroughShapeTree(childShape, bNeedCount, count, callback);
            if (count && !bNeedCount) return true;
		}
        return ret;
	}
    if (callback(pShapeTree))
    {
        ++count;
        return true;
    }
    return false;
}

bool _collectShapesInfo(KEtWorkbook* pWb, bool bNeedCount, int& count, CollectShapeCallback callback)
{
    count = 0;
	ks_stdptr<IKWorksheets> ptrWorksheets = pWb->GetCoreWorkbook()->GetWorksheets();
	int sheetCnt = ptrWorksheets->GetSheetCount();
    bool ret = false;
	for (int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
	{
		ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
		if (!ptrWorksheet)
			continue;
        const WCHAR* pSheetName = nullptr;
        ptrWorksheet->GetSheet()->GetName(&pSheetName);
        if (xstrcmp(pSheetName, STR_CELL_IMAGE_SHEET_NAME) == 0)
            continue;
        ks_stdptr<IKDrawingCanvas> spCanvas;
        oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
        ks_castptr<drawing::ShapeTree> pShapeTree = spCanvas;
		if (!pShapeTree) continue;

		ret |= _walkThroughShapeTree(pShapeTree, bNeedCount, count, callback);
        if (count && !bNeedCount) return true;
	}
    return ret;
}

void CollectShapesInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectShapesInfo]";
        return;
    }
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    if (nullptr == wb)
    {
        WOLOG_ERROR << "nullptr wb in [CollectShapesInfo]";
        return;
    }

    int count = 0;
    bool bHasNormalShape = _collectShapesInfo(wb, false, count, HasNormalShape);
    if (bHasNormalShape)
        WriteSvrFileinclude(binWriter, __X("behaviour_shape1"), wb);

    bool bHasTextBoxOrEffect = _collectShapesInfo(wb, false, count, HasTextBoxOrEffect);
    if (bHasTextBoxOrEffect)
        WriteSvrFileinclude(binWriter, __X("behaviour_shape2"), wb);

    bool bHasPivotTableSlicer = _collectShapesInfo(wb, true, count, HasPivotTableSlicer);
    if (bHasPivotTableSlicer)
        WriteSvrFileinclude(binWriter, krt::utf16(QString("behaviour_pivottable_slicer_%1").arg(QString::number(count))), wb);

    bool bHasPivotChart = _collectShapesInfo(wb, true, count, HasPivotChart);
    if (bHasPivotChart)
        WriteSvrFileinclude(binWriter, krt::utf16(QString("behaviour_pivottable_view_%1").arg(QString::number(count))), wb);

    bool bHasShape = _collectShapesInfo(wb, true, count, HasShape);
    if (bHasShape)
        WriteSvrFileinclude(binWriter, __X("behaviour_shape_count"), wb, count);

    bool bHasPicture = _collectShapesInfo(wb, true, count, HasPicture);
    if (bHasPicture)
        WriteSvrFileinclude(binWriter, __X("behaviour_pic_count"), wb, count);
}

static void WriteNameWithBool(ks_wstring& name, bool b, binary_wo::BinWriter& binWriter)
{
    if (b)
        name += __X("_true");
    else
        name += __X("_false");
    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        {
            binWriter.addStringField(name.c_str(), "name");
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

void CollectUsedRangeAccuracy(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectUsedRangeAccuracy]";
        return;
    }
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    if (nullptr == wb)
    {
        WOLOG_ERROR << "nullptr wb in [CollectUsedRangeAccuracy]";
        return;
    }

    IKWorksheets* pWorksheets = wb->GetCoreWorkbook()->GetWorksheets();
    if (nullptr == pWorksheets)
        return;

    IBook* pBook = wb->GetCoreWorkbook()->GetBook();

    for (int i = 0, count = pWorksheets->GetSheetCount(); i < count; ++i)
    {
        if (isCellImgListSheet(pBook, i))
            continue;
        IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(i);
        if (!pWorksheet)
            continue;
        ISheet* pSheet = pWorksheet->GetSheet();
        if (!pSheet || !pSheet->IsGridSheet())
            continue;

        int right  = pSheet->GetRight();
        int bottom = pSheet->GetBottom();
        if (right < 0 || right >= pSheet->GetBMP()->cntCols ||
            bottom < 0 || bottom >= pSheet->GetBMP()->cntRows)
            continue;

        ISheetStake* pSheetStake = pSheet->GetWoStake();
        if (!pSheetStake)
            continue;
        bool bHasValue = true, bHasValueOrXf = true;
        pSheetStake->IsUsedRangeBorderHasValueOrXf(bHasValue, bHasValueOrXf);

        ks_wstring hasValueName = __X("used_range_border_has_value");
        WriteNameWithBool(hasValueName, bHasValue, binWriter);

        ks_wstring hasValueOrXfName = __X("used_range_border_has_value_or_xf");
        WriteNameWithBool(hasValueOrXfName, bHasValueOrXf, binWriter);
    }
}

class CellValueAcptUtil : public ICellValueAcpt 
{
public:
    CellValueAcptUtil(const std::function<bool(ROW row, COL col, const_token_ptr pToken)>& cb): m_callback(cb)
    {
    }

    STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
    {
        if (pToken && m_callback(row, col, pToken)) 
		{
            return 1;
        }

        return 0;  //继续枚举
    };

private:
    const std::function<bool (ROW row, COL col, const_token_ptr pToken)>& m_callback;
};

void EnumRangeCells(ISheet *spSheet, const RANGE &rg, const std::function<bool (ROW row, COL col, const_token_ptr pToken)> &func)
{
    et_sdptr<ISheetEnum> spSheetEnum;
	spSheet->CreateEnum(&spSheetEnum);

    CellValueAcptUtil cellValAcpt(func);
    spSheetEnum->EnumCellValueRowbyRow(rg, &cellValAcpt);
}

bool IsValidSheetIdx(IBook* pBook, IDX idxSheet)
{
    if (idxSheet < 0)
        return false;

    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    if (sheetCount <= idxSheet)
        return false;

    return true;
}

bool IsValidCell(IBook* pBook, const CELL &cell)
{
    if (cell.row < 0 || cell.col < 0)
        return false;
    
    BMP_PTR pBmp = pBook->GetBMP();
    if (pBmp->cntRows <= cell.row || pBmp->cntCols <= cell.col)
        return false;

    return true;
}

IgnoreHistoryGuard::IgnoreHistoryGuard(KEtRevisionContext* ctx, bool enable) : m_ctx(ctx), m_enable(enable)
{
    if (m_enable)
        m_ctx->beginIgnoreHistory();
}

IgnoreHistoryGuard::~IgnoreHistoryGuard()
{
    if (m_enable)
        m_ctx->endIgnoreHistory();
}

void IgnoreHistoryGuard::enable()
{
    if (m_enable)
        return;

    m_ctx->beginIgnoreHistory();
    m_enable = true;
}

WebInt DownloadedDashBoardSnapshot(KEtRevisionContext* pCtx, KEtWorkbook* pETWorkbook)
{
    static const UINT width = _kso_GetWoEtSettings()->GetDashBoardSnapshotWidth();
    static const UINT minHeight = _kso_GetWoEtSettings()->GetDashBoardSnapshotMinHeight();
    WOLOG_INFO << "DownloadedDashBoardSnapshot";

    //下载
    if (!pETWorkbook)
        return WO_OK;
    IKWebExtensionMgr* pWebExtensionMgr = pETWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return WO_OK;

    IBook* pBook = pETWorkbook->GetCoreWorkbook()->GetBook();
    std::vector<int> vecDashBoardStId;
    int nSheetCount = 0;
    pBook->GetSheetCount(&nSheetCount);
    for (int i = 0; i < nSheetCount; i++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        if (spSheet && spSheet->IsDbDashBoardSheet())
        {
            UINT webExtensionCount = 0;
            pWebExtensionMgr->GetWebExtensionCount(spSheet, &webExtensionCount);
            if (webExtensionCount <= 0 )    // 空仪表盘sheet不截图。
                continue;
            vecDashBoardStId.push_back(spSheet->GetStId());
        }
    }
    if (vecDashBoardStId.empty())
        return WO_OK;

    using resObj = std::tuple<int, WebInt, ks_wstring>;
    std::vector<resObj> vecResObj;
    WebSlice downloadList = {NULL, 0};
    do
    {
        if (width <= 0)    // 宽度为0时作为开关，不走服务端node server截图。
            break;
        binary_wo::BinWriter binWriter;
        binWriter.addStringField(pCtx->getUser()->connID(), "connId");
        binWriter.addInt32Field(WebDownloadKindLocal, "kind");

        binWriter.addKey("list");
        binWriter.beginArray();
        for (const auto& sheetId : vecDashBoardStId)
        {
            binWriter.beginStruct();
            binWriter.addKey("objectKey");
            binWriter.addString(krt::utf16(QString::number(sheetId))); // 仪表盘截图用对应sheetId获取
            binWriter.beginStruct("object");
            {
                binWriter.addInt32Field(WebDownloadByDashBoard, "tag");
                binWriter.beginStruct("dashboardArg");
                {
                    binWriter.addStringField(__X("png"), "format");
                    binWriter.addInt32Field(width, "width");
                    binWriter.addInt32Field(1080, "height");    // 高度实际是计算的没用到，给服务端接口传个值。
                    binWriter.addInt32Field(minHeight, "minHeight"); // node的db设死了默认最小高度，webet加个配置。
                    binWriter.addInt32Field(sheetId, "sheetId");
                }
                binWriter.endStruct();
            }
            binWriter.endStruct();
            binWriter.endStruct();
        }
        binWriter.endArray();

        binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
        WebSlice downloadArgList = {shbt.get(), binWriter.writeLength()};
        gs_callback->downloadImageSync(&downloadArgList, &downloadList);

        // 加载, 取出服务端返回的截图路径，失败时采取兼容提示图片兜底。
        if (downloadList.size == 0 || downloadList.data == nullptr)
            break;
        binary_wo::BinReader rd(downloadList.data, downloadList.size);
        binary_wo::VarObjRoot infoObj = rd.buildRoot();
        binary_wo::VarObj info = infoObj.cast();

        DownloadImgSyncRes downLoadRes = info.field_int32("status");
        if (DownloadImgSync_OK != downLoadRes)
        {
            WOLOG_INFO << "[DownloadedDashBoardSnapshot] DashBoardSheetImage export server error, resCode:" << downLoadRes;
            break;
        }
        binary_wo::VarObj objList = info.get_s("list");
        for (int i = 0; i < objList.arrayLength_s(); i++)
        {
            binary_wo::VarObj item = objList.at_s(i);
            WebStr objectKey = item.field_str("objectKey");
            WebInt code = item.field_int32("code");
            WebStr path = item.field_str("path");
            int stId = krt::fromUtf16(objectKey).toInt();
            vecResObj.emplace_back(stId, code, path);
        }
    } while (false);

    static const ks_wstring wstrPathCompatible = krt::utf16( krt::dirs::resources() + QDir::separator() + "mui" + QDir::separator() 
        + krt::i18n::language() + QDir::separator() 
        + "resource" + QDir::separator() + "wo" + QDir::separator() + "etDashBoardCompatible.png");
    for (const auto& stId : vecDashBoardStId)
    {
        IDX sheetIdx = INVALIDIDX;
        pBook->STSheetToRTSheet(stId, &sheetIdx);
        ks_stdptr<_Worksheet> spWorksheet = pETWorkbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
        if (!spWorksheet)
            continue;
        ks_stdptr<Shapes> spShapes;
        spWorksheet->get_Shapes(FALSE, &spShapes);
        if (!spShapes)
            continue;

        // 尝试删除仪表盘sheet中多余的浮动图片，避免旧截图存量。
        int nCount = 0;
        spShapes->get_Count(&nCount);
        ks_stdptr<IKShapeContainer>	spRootShape;
        spWorksheet->GetDrawingCanvas()->GetRootShape(&spRootShape);
        if (!spRootShape)
            continue;
        std::vector<drawing::AbstractShape* > vecShapes;
        for (int i = 0; i < nCount; ++i)
        {
            ks_stdptr<IKShape> spIKShape;
            spRootShape->GetShapeByIndex(i, &spIKShape);
            ks_castptr<drawing::AbstractShape> cpAbsShape = spIKShape;
            if(cpAbsShape && !cpAbsShape->getWebExtension() && cpAbsShape->isPicture())
                vecShapes.push_back(cpAbsShape);
        }
        for (auto& absShape : vecShapes)
            spRootShape->RemoveShape(absShape);

        InsertPictureHelper helper(spWorksheet, spShapes);
        ks_stdptr<Shape> spShape;
        ks_wstring wstrPath;
        auto it = std::find_if(vecResObj.begin(), vecResObj.end(),
                               [stId](const resObj& obj) { return std::get<0>(obj) == stId; }
                               );
        if (it != vecResObj.end() && std::get<1>(*it) == WO_OK)
            wstrPath = std::get<2>(*it);
        
        // 从path取dashbaordSnapshot创建shape插入原仪表盘sheet中，失败时插入兜底兼容图片。
        HRESULT hr = E_FAIL;
        if (!wstrPath.empty())
            hr = helper.Insert(wstrPath.c_str(), 0, 0, 0, 0, &spShape);
        if (FAILED(hr) || !spShape)
        {
            WOLOG_INFO << "[DownloadedDashBoardSnapshot] stId:" << " _XCreateStreamOnFile error, insert compatible dashboard snapshot";
            hr = helper.Insert(wstrPathCompatible.c_str(), 0, 0, 0, 0, &spShape);
            if (FAILED(hr) || !spShape)
                continue;
        }

        ks_stdptr<IKsoShapeEx> spShapeEx = spShape;
        float oriWidth = 0, oriHeight = 0;
        spShapeEx->get_OriginalWidth(&oriWidth);
        spShapeEx->get_OriginalHeight(&oriHeight);
        spShape->put_Width(oriWidth);
        spShape->put_Height(oriHeight);
        spShape->put_Top(0);
        ks_stdptr<IKShape> spCoreShape;
        spShapeEx->GetInterface(IID_IKShape, (void**)&spCoreShape);
        ks_castptr<drawing::AbstractShape> pAbsShape = spCoreShape;
        pAbsShape->mutablePicture()->setStretched(true);
    }

    gs_callback->clearDownloadImage(&downloadList);
    return WO_OK;
}

WebInt DownloadedAndLoadImg(std::map<ks_wstring, std::vector<ks_stdptr<IKBlipAtom>> *> &mapShapeUrl)
{
    WOLOG_INFO << "DownloadedAndLoadImg";

    //下载
    if (mapShapeUrl.empty())
        return WO_OK;

    binary_wo::BinWriter binWriter;
    binWriter.addInt32Field(WebDownloadKindLocal, "kind");

    binWriter.addKey("list");
    binWriter.beginArray();

    for (auto it = mapShapeUrl.begin(); it != mapShapeUrl.end(); it++)
    {
        binWriter.beginStruct();
        binWriter.addKey("objectKey");
        binWriter.addString(it->first.c_str());

        bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(it->first.c_str());
        if(isAttachment)
        {
            binWriter.beginStruct("object");
            {
                binWriter.addInt32Field(WebDownloadByAttachmentId, "tag");
                binWriter.addStringField(util::getAttachmentId(it->first.c_str()), "objectLocator");
            }
            binWriter.endStruct();
        }
        else
        {
            binWriter.beginStruct("object");
            {
                binWriter.addInt32Field(WebDownloadByUrl, "tag");
                binWriter.addStringField(it->first.c_str(), "objectLocator");
            }
            binWriter.endStruct();
        }

        binWriter.endStruct();
    }

    binWriter.endArray();

    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice downloadArgList = {shbt.get(), binWriter.writeLength()};
    WebSlice downloadList = {NULL, 0};
    gs_callback->downloadImageSync(&downloadArgList, &downloadList);

    //加载
    if (downloadList.size == 0 || downloadList.data == nullptr)
        return WO_FAIL;
    binary_wo::BinReader rd(downloadList.data, downloadList.size);
    binary_wo::VarObjRoot infoObj = rd.buildRoot();
    binary_wo::VarObj info = infoObj.cast();

    DownloadImgSyncRes downLoadRes = info.field_int32("status");
    if (DownloadImgSync_OK != downLoadRes)
    {
        WOLOG_INFO << "[DownloadedAndLoadImg] source images maybe exceed size limit, resCode:" << downLoadRes;
        return downLoadRes;
    }

    binary_wo::VarObj objList = info.get_s("list");
    //首先给没有其他问题的图片打上违规标志位，当判断到图片没有违规时，再取消它的标志位
    for(auto & element : mapShapeUrl)
    {
        std::vector<ks_stdptr<IKBlipAtom>> *pShapeList = element.second;
        for (auto it = pShapeList->begin(); it != pShapeList->end(); it++)
        {
            IKBlipAtom* pBlipAtom = (*it);

            KSOBLIPUSAGE usage;
            pBlipAtom->GetUsage(&usage);
            if(usage == ksoblipUsageDefault)
                pBlipAtom->SetUsage(ksoblipUsageUrlDownloadFail);
        }
    }

    for (int i = 0; i < objList.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = objList.at_s(i);
        WebStr objectKey = item.field_str("objectKey");
        WebInt code = item.field_int32("code");
        WebStr path = item.field_str("path");

        WOLOG_INFO << "[DownloadedAndLoadImg]" << " code: " << code << "  objectKey: " << objectKey;
        auto it = mapShapeUrl.find(objectKey);
        if (it == mapShapeUrl.end())
            continue;
        std::vector<ks_stdptr<IKBlipAtom>> *pShapeList = it->second;

        if (code == WO_OK)
        {
            ks_stdptr<IStream> spStream = NULL;
            HRESULT hr = _XCreateStreamOnFile(
                path,
                STGM_READ | STGM_SHARE_DENY_NONE,
                &spStream);
            ASSERT(SUCCEEDED(hr));
            if (FAILED(hr) || !spStream) {
                WOLOG_INFO << "[DownloadedAndLoadImg]" << " _XCreateStreamOnFile error ";
                continue;
            }

            STATSTG stg;
            ULISet32(stg.cbSize, 0);
            spStream->Stat(&stg, STATFLAG_NONAME);
            ASSERT(stg.cbSize.HighPart == 0);
            ULONG cbSize = stg.cbSize.LowPart;

            for (auto it = pShapeList->begin(); it != pShapeList->end(); it++)
            {
                HGBL hImg = NULL;
                ULARGE_INTEGER offset;
                ULISet32(offset, 0);
                hr = CreateHGblFromStream(&hImg, spStream, offset, cbSize);
                ASSERT(SUCCEEDED(hr));

                IKBlipAtom* pBlipAtom = *it;
                kso_md4 md4 = { 0 };
                hr = pBlipAtom->SetHGlobal(md4, hImg);
                ASSERT(SUCCEEDED(hr));

                //跑到这里证明图片不是违规的，但是有可能有其他问题，按原有逻辑不改动usage
                KSOBLIPUSAGE usage;
                pBlipAtom->GetUsage(&usage);
                if(usage == ksoblipUsageUrlDownloadFail || usage == ksoblipUsageUrlDownloading) {
                    WOLOG_INFO << "[DownloadedAndLoadImg]" << " all SUCCEEDED ";
                    pBlipAtom->SetUsage(ksoblipUsageDefault);
                }    
                pBlipAtom->SetIsCloudBlip(true);
                pBlipAtom->SetWebofficeCloudObj(true);
            }
        }
        else
        {
            for (auto it = pShapeList->begin(); it != pShapeList->end(); it++)
            {
                IKBlipAtom* pBlipAtom = (*it);
                pBlipAtom->SetUsage(ksoblipUsageUrlDownloadFail);
            }
        }
    }

    gs_callback->clearDownloadImage(&downloadList);
    return WO_OK;
}

WebInt DownloadSheetAllUrlImg(ISheet *spSheet, bool bForceAttachmentReDownload/* = true*/)
{
    std::map<ks_wstring, std::vector<ks_stdptr<IKBlipAtom>> *> mapShapeUrl;

    ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
    oplGetSheetOplData(spSheet, &spCanvas);
    if (!spCanvas) return WO_FAIL;

    ks_stdptr<IKShapeContainer> spShapeContainer = NULL;
    spCanvas->GetRootShape(&spShapeContainer);
    if (!spShapeContainer) return WO_FAIL;

    LONG nShapeCount = 0;
    spShapeContainer->GetShapeCount(&nShapeCount);
    for (INT i = 0; i < nShapeCount; ++i)
    {
        ks_stdptr<drawing::AbstractShape> shape;
        if (spShapeContainer->GetShapeByIndex(i, &shape) != S_OK)
            continue;

        ks_bstr path;
        HGBL hgbl = NULL;
        IKBlipAtom* pBlipAtom = shape->picID();
        if (shape->isPicture() && pBlipAtom
            && pBlipAtom->GetLinkPath(&path) == S_OK && !path.empty())
        {
            bool bNeedDownload = false;
            //判断是否为附件
            bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(path.c_str());
            if(isAttachment && bForceAttachmentReDownload)//如果是附件格式，并且强制重新下载
                bNeedDownload = true;
            else if(pBlipAtom->GetHGlobal(&hgbl) != S_OK)
                bNeedDownload = true;
            if(!bNeedDownload)
                continue;

            std::vector<ks_stdptr<IKBlipAtom>> *pShapeList = NULL;
            auto it = mapShapeUrl.find(path.c_str());
            if (it == mapShapeUrl.end())
            {
                pShapeList = new std::vector<ks_stdptr<IKBlipAtom>>;
                mapShapeUrl.insert(std::make_pair(path.c_str(), pShapeList));
            }
            else
            {
                pShapeList = it->second;
            }

            pShapeList->push_back(pBlipAtom);
        }
    }

    WebInt retCode = util::DownloadedAndLoadImg(mapShapeUrl);

    for (auto it = mapShapeUrl.begin(); it != mapShapeUrl.end(); it++)
        delete it->second;

    return retCode;
}

WebInt DownloadCellImgForSheet(IKWorkbook* pBook, ISheet *pSheet)
{
    //枚举所有图片
    class KCellValueAcpt : public ICellValueAcpt
    {
        STDIMP_(INT) Do(ROW, COL, const_token_ptr pToken)
        {
            if(!alg::const_vstr_token_assist::is_type(pToken))
                return 0;

            if (!m_pCellImages)
                return 0;
            CellImg_Param param;
            ks_wstring strCellValue;
            strCellValue = alg::const_vstr_token_assist(pToken).get_value();
            if (m_pCellImages->GetCellImgParamFromCellValue(strCellValue.c_str(), &param) == FALSE)
                return 0;
            
            IKShape* pShape = m_pCellImages->GetImgByName(param.getNameStr());
            ks_castptr<drawing::AbstractShape> spShape = pShape;
            if (!spShape)
                return 0;
            IKBlipAtom* spBlipAtom = spShape->picID();
            if (!spBlipAtom)
                return 0;

            ks_bstr strUrl;
            spBlipAtom->GetLinkPath(&strUrl);
            if (strUrl.empty())
                return 0;

            HGBL hgbl = NULL;
            if (spBlipAtom->GetHGlobal(&hgbl) == S_OK && hgbl)
            {
                //判断是否为附件
                bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(strUrl.c_str());
                if(!isAttachment)//1附件强制走下载;2.非附件看本地是否已经存在，若已存在，则直接返回
                    return 0;
            }
            
            ks_castptr<EtCellImageIndividualShape> pImgShape = spShape;
            if (pImgShape && ET_MODEL_CELLIMAGESHAPE == spShape->getModelType())
            {
                //无引用的单元格图片，直接跳过
                if (pImgShape->ref() <= 0)
			        return 0;
            }

                
            std::vector<ks_stdptr<IKBlipAtom>> *pShapeList = NULL;
            auto it = m_mapShapeUrl.find(strUrl.c_str());
            if (it == m_mapShapeUrl.end())
            {
                pShapeList = new std::vector<ks_stdptr<IKBlipAtom>>;
                m_mapShapeUrl.insert(std::make_pair(strUrl.c_str(), pShapeList));
            }
            else
            {
                pShapeList = it->second;
            }

            pShapeList->push_back(spBlipAtom);
            return 0;
        };

    public:
        KCellValueAcpt(IKWorkbook* pBook)
        {
            m_pCellImages = pBook->GetCellImages();
        }

        ~KCellValueAcpt()
        {
            for (auto it = m_mapShapeUrl.begin(); it != m_mapShapeUrl.end(); it++)
                delete it->second;
        }

        std::map<ks_wstring, std::vector<ks_stdptr<IKBlipAtom>> *> m_mapShapeUrl;
        ICellImages *m_pCellImages;
    };

    RANGE rgCheck(pSheet->GetBMP());
    IDX idxSheet = INVALIDIDX;
    pSheet->GetIndex(&idxSheet);
    rgCheck.SetSheetFromTo(idxSheet);
    rgCheck.SetRowFromTo(0, pSheet->GetBMP()->cntRows - 1);
    rgCheck.SetColFromTo(0, pSheet->GetBMP()->cntCols - 1);

    et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);

    KCellValueAcpt cellValue(pBook);
    spSheetEnum->EnumCellValue(rgCheck, &cellValue);
    return util::DownloadedAndLoadImg(cellValue.m_mapShapeUrl);
}

WebInt DownloadCellImgForRange(IKWorkbook* pBook, ISheet *pSheet, RANGE& range)
{
    //枚举所有图片
    class KCellValueAcpt : public ICellValueAcpt
    {
        STDIMP_(INT) Do(ROW, COL, const_token_ptr pToken)
        {
            if(!alg::const_vstr_token_assist::is_type(pToken))
                return 0;

            if (!m_pCellImages)
                return 0;
            CellImg_Param param;
            ks_wstring strCellValue;
            strCellValue = alg::const_vstr_token_assist(pToken).get_value();
            if (m_pCellImages->GetCellImgParamFromCellValue(strCellValue.c_str(), &param) == FALSE)
                return 0;
            
            IKShape* pShape = m_pCellImages->GetImgByName(param.getNameStr());
            ks_castptr<drawing::AbstractShape> spShape = pShape;
            if (!spShape)
                return 0;
            IKBlipAtom* spBlipAtom = spShape->picID();
            if (!spBlipAtom)
                return 0;

            ks_bstr strUrl;
            spBlipAtom->GetLinkPath(&strUrl);
            if (strUrl.empty())
                return 0;

            HGBL hgbl = NULL;
            if (spBlipAtom->GetHGlobal(&hgbl) == S_OK && hgbl)
            {
                //判断是否为附件
                bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(strUrl.c_str());
                if(!isAttachment)//1附件强制走下载;2.非附件看本地是否已经存在，若已存在，则直接返回
                    return 0;
            }
            
            ks_castptr<EtCellImageIndividualShape> pImgShape = spShape;
            if (pImgShape && ET_MODEL_CELLIMAGESHAPE == spShape->getModelType())
            {
                //无引用的单元格图片，直接跳过
                if (pImgShape->ref() <= 0)
			        return 0;
            }

                
            std::vector<ks_stdptr<IKBlipAtom>> *pShapeList = NULL;
            auto it = m_mapShapeUrl.find(strUrl.c_str());
            if (it == m_mapShapeUrl.end())
            {
                pShapeList = new std::vector<ks_stdptr<IKBlipAtom>>;
                m_mapShapeUrl.insert(std::make_pair(strUrl.c_str(), pShapeList));
            }
            else
            {
                pShapeList = it->second;
            }

            pShapeList->push_back(spBlipAtom);
            return 0;
        };

    public:
        KCellValueAcpt(IKWorkbook* pBook)
        {
            m_pCellImages = pBook->GetCellImages();
        }

        ~KCellValueAcpt()
        {
            for (auto it = m_mapShapeUrl.begin(); it != m_mapShapeUrl.end(); it++)
                delete it->second;
        }

        std::map<ks_wstring, std::vector<ks_stdptr<IKBlipAtom>> *> m_mapShapeUrl;
        ICellImages *m_pCellImages;
    };

    et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);

    KCellValueAcpt cellValue(pBook);
    spSheetEnum->EnumCellValue(range, &cellValue);
    return util::DownloadedAndLoadImg(cellValue.m_mapShapeUrl);
}

WebInt DownloadCommentImage(ISheet *pSheet)
{
	if (!pSheet)
		return WO_FAIL;

	ks_stdptr<IKDrawingCanvas> spCanvas;
	oplGetSheetCommentOplData(pSheet, &spCanvas);
	if (!spCanvas)
		return WO_FAIL;

	ks_stdptr<IKMediaManage> spMediaMgr;
	ks_stdptr<IBook> spBook = nullptr;
	if (SUCCEEDED(pSheet->GetBook(&spBook)) && spBook)
		oplGetBookMediaMgr(spBook, &spMediaMgr);
	if (!spMediaMgr)
		return WO_FAIL;

	std::map<ks_wstring, std::vector<ks_stdptr<IKBlipAtom>> *> mapShapeUrl;

	ks_stdptr<ICellComments> spCellComments = spCanvas;
	INT cmtCnt = 0;
	spCellComments->GetCount(&cmtCnt);
	for (INT cmtIdx = 0; cmtIdx < cmtCnt; ++cmtIdx)
	{
		ks_stdptr<ICellComment> spCellCmt;
		spCellComments->GetItem(cmtIdx, &spCellCmt);
		if (!spCellCmt)
			continue;
		ks_stdptr<IWoComment> spWoComment;
		spCellCmt->GetWoComment(&spWoComment);
		if (!spWoComment)
			continue;

		auto collectChainsData = [&](bool bIsResolved)
		{
			for (size_t i = 0; i < spWoComment->GetChainCount(bIsResolved); ++i)
			{
				IWoCommentChain* pCommentChain = spWoComment->GetChainByIndex(bIsResolved, i);
				if (pCommentChain == nullptr)
					continue;

				for (size_t j = 0; j < pCommentChain->Count(); ++j)
				{
					IWoCommentItem* pItem = pCommentChain->GetItem(j);
					if (pItem == nullptr)
						continue;

					for (INT k = 0; k < pItem->GetImageCount(); ++k)
					{
						if (IWoCommentImage* pImage = pItem->GetImage(k))
						{
							ks_stdptr<IKBlipAtom> spBlipAtom = pImage->GetBlip();
							ks_wstring fakeUrl = generateFakeAttachmentUrl(pImage->GetInfo().id.c_str());

							if (!spBlipAtom)
							{
								spMediaMgr->AddAtomExternal(koplBlipUNKNOWN, __X(""), nullptr, &spBlipAtom);
								if (!spBlipAtom)
									continue;
								spBlipAtom->SetLinkPath(fakeUrl.c_str());
								spBlipAtom->SetBlipName(fakeUrl.c_str());
								pImage->SetBlip(spBlipAtom);
							}

							std::vector<ks_stdptr<IKBlipAtom>> *pShapeList = NULL;
							auto it = mapShapeUrl.find(fakeUrl.c_str());
							if (it == mapShapeUrl.end())
							{
								pShapeList = new std::vector<ks_stdptr<IKBlipAtom>>;
								mapShapeUrl.insert(std::make_pair(fakeUrl.c_str(), pShapeList));
							}
							else
							{
								pShapeList = it->second;
							}

							pShapeList->push_back(spBlipAtom);
						}
					}
				}
			}
		};

		collectChainsData(false);
		collectChainsData(true);
    }

    WebInt retCode = util::DownloadedAndLoadImg(mapShapeUrl);

    for (auto it = mapShapeUrl.begin(); it != mapShapeUrl.end(); it++)
        delete it->second;

    return retCode;
}

bool UploadImg(int16 dpi, drawing::AbstractShape* pShape, OUT QByteArray& sha1)
{
    QByteArray picData;
    if (pShape->getPicture(sha1, picData, dpi / 96.0) != WO_OK)
        return false;

    // 防止图片没有上传
    // 内核无需批量处理uploadImage的参数. 服务端会在一个命令周期里等待多张图片的回调
    if (!picData.isEmpty())
    {
        WebSlice slice = { (WebByte*)(picData.data()), picData.size() };
        gs_callback->uploadImage(sha1.data(), NULL, &slice, nullptr);
    }
    return true;
}

void ReleaseValidation(VALIDATION* pValidation)
{
	if (pValidation)
	{
		if (pValidation->bsFormula1)
		{
			::SysFreeString(pValidation->bsFormula1);
			pValidation->bsFormula1 = NULL;
		}
		if(pValidation->bsFormula2)
		{
			::SysFreeString(pValidation->bsFormula2);
			pValidation->bsFormula2 = NULL;
		}
	}
}


double decodeColorAlpha(DWORD argb)
{
	DWORD AA = argb & 0xFF000000;
	AA = AA >> 24;

	if (AA == 0)
		return (double)(0xFF / 255); 
	else
		return (double)(AA / 255);
}

drawing::Color decodeColorARGB(DWORD argb, BOOL isAutoColor/* = FALSE*/) 
{
	DWORD rgb = 0xFF000000 | (argb & 0x00FFFFFF);
	drawing::Color color = drawing::Color::fromRgb(rgb, isAutoColor);
	color.addTransform(drawing::Color::Alpha, decodeColorAlpha(argb));
	return color;
}

void PrintLog_ranges(PCWSTR tag, range_helper::ranges& rgs)
{
	const UINT rangeCount = static_cast<UINT>(rgs.size());

	for (UINT i = 0; i < rangeCount; ++i)
	{
		const RANGE* pRG = rgs.at(i).second;
		WOLOG_INFO << "[" << tag << "] " << *(pRG);
	}
}

ISheet* getConnSharedSheet(KEtWorkbook* pWb, KEtRevisionContext* pCtx)
{
	IBook *pBook = pWb->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	if (!spSharedLinkMgr)
		return nullptr;
	PCWSTR shareId = spSharedLinkMgr->GetConnSharedLink(pCtx->getUser());
	if (!shareId)
		return nullptr;
	ISharedLink* pLink = spSharedLinkMgr->GetItem(shareId);
	if (pLink && pLink->Type() == SharedLinkType_Sheet)
		return static_cast<ISharedLinkSheet*>(pLink)->GetSheet();

	return nullptr;
}

_Workbook* OpenFile(KEtWorkbook* pWwb, Workbooks* ptrWorkbooks, const ks_wstring& fileName)
{
    ks_stdptr<IETPersist> ptrPersist = pWwb->GetCoreApp()->GetAppPersist()->GetPersist();
    if (!ptrPersist->IsSupportedFormat(fileName.c_str()))
        return nullptr;

	util::TemporaryDisableRevisionCtx disableRevisionCtx;
	ks_bstr SingleFileName(fileName.c_str());
	VARIANT varfn = {0};
	VARIANT vpas = {0};
	VARIANT vModifyPas = { 0 };

	V_VT(&varfn) = VT_BSTR;
	V_VT(&vpas) = VT_BSTR;
	V_BSTR(&varfn) = SingleFileName;
	VARIANT varAddToMru;
	V_VT(&varAddToMru) = VT_BOOL;
	V_BOOL(&varAddToMru) = VARIANT_TRUE;
	VARIANT var;
	V_VT(&var) = VT_BOOL;
	V_BOOL(&var) = VARIANT_TRUE;

	VARIANT varUpdateLinks = {0};
	V_VT(&varUpdateLinks) = VT_BOOL;
	V_BOOL(&varUpdateLinks) = VARIANT_FALSE;

	HRESULT hr = E_FAIL;
	ks_stdptr<_Workbook> ptrWorkbook = NULL;
	hr = ptrWorkbooks->Open(varfn, varUpdateLinks,
		var,	VARIANT(),
		vpas,	vModifyPas,
		VARIANT(),	VARIANT(),
		VARIANT(),	VARIANT(),
		VARIANT(),	VARIANT(),
		varAddToMru,  &ptrWorkbook, FALSE, FALSE);

	return ptrWorkbook;
}

void CloseFile(_Workbook* ptrWorkbook)
{
	VARIANT SaveChanges;
	V_VT(&SaveChanges) = VT_BOOL;
	V_BOOL(&SaveChanges) = VARIANT_FALSE;

	VARIANT Filename = {0};
	VARIANT RouteWorkbook = {0};

	util::TemporaryDisableRevisionCtx disableRevisionCtx;
	ptrWorkbook->Close(SaveChanges, Filename, RouteWorkbook);
}

AutoCmdTraceId::AutoCmdTraceId(KEtRevisionContext * ctx, const binary_wo::VarObj& cmd)
: m_ctx(ctx)
{
    WebStr id = getTraceIdInCmd(cmd, ctx);
    ctx->setCmdTraceId(id);
}

WebStr AutoCmdTraceId::getTraceIdInCmd(const binary_wo::VarObj & cmd, KEtRevisionContext*pCtx)
{
    if (pCtx->isExecDirect()) return nullptr;
    if (!cmd.has("param")) return nullptr;
    binary_wo::VarObj param = cmd.get("param");
    if (!param.has("cmdTraceId")) return nullptr;
    return param.field_str("cmdTraceId");
}

AutoCmdTraceId::~AutoCmdTraceId()
{
    m_ctx->setCmdTraceId(nullptr);
}

bool IsRangeEmpty(KEtRevisionContext* pCtx, ISheet *pSheet, const RANGE& range)
{
    class CheckEmptyAcpt : public ICellValueAcpt
    {
    public:
        CheckEmptyAcpt(KEtRevisionContext* pCtx, ISheet* pSheet) : m_pCtx(pCtx), m_pSheet(pSheet)
        {

        }

        bool IsEmpty() const
        {
            return m_empty;
        };

        STDPROC_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
        {
            if (!pToken)
                return 0;

            if(m_pCtx->getProtectionCtx()->isCellHidden(m_pSheet, row, col))
                return 0;//继续枚举

            DWORD tokenType = alg::GetExecTokenMajorType(pToken);
            if (tokenType == etexec::ETP_NONE)
                return 0;



            m_empty = false;
            return 1;
        };
    private:
        bool m_empty = true;
        KEtRevisionContext* m_pCtx;
        ISheet* m_pSheet;
    };
    et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);
    CheckEmptyAcpt checkEmptyAcpt(pCtx, pSheet);
    spSheetEnum->EnumCellValue(range, &checkEmptyAcpt);
    return checkEmptyAcpt.IsEmpty();
}

HRESULT SetNumberFormat(KEtWorkbook* pEtWorkbook, const RANGE &rg, int nfIndex)
{
    ks_bstr nfStr(kfc::nf::_XNFGetEtStr(nfIndex));
    return SetNumberFormat(pEtWorkbook, rg, nfStr);
}

HRESULT SetNumberFormat(KEtWorkbook* pEtWorkbook, const RANGE &rg, BSTR nfStr)
{
    ks_stdptr<etoldapi::Range> spRange = pEtWorkbook->CreateRangeObj(rg);
    if (!spRange)
        return E_FAIL;

    HRESULT hr = spRange->put_NumberFormat(nfStr);
    return hr;
}

bool HasMergedCell(ISheet* pSheet, const RANGE &rg)
{
    std::vector_s<RANGE> ranges;
    VS(pSheet->FindEffectMergeCell(rg, FALSE, ranges));
    return !ranges.empty();
}

PCWSTR getNetDiskName()
{
    static ks_wstring name;
    if (name.empty())
    {
        name = krt::utf16(QString::fromUtf8(gs_callback->getNetDiskName()));
    }

    return name.c_str();
}

SUPBOOK_STAT getNetDiskFile(PCWSTR netDiskName, PCWSTR fileId,
	ks_wstring& path, ks_wstring& version, ks_wstring& fileName, const QString& userId)
{
    if (xstrcmp(netDiskName, getNetDiskName()) != 0)
        return sbsNotExist;

    QString strFileId = QString::fromUtf16(fileId);

    const char* pPath = NULL;
    const char* pVersion = NULL;
    const char* pFileName = NULL;
    NetFileRes res = gs_callback->getNetFile(strFileId.toUtf8(), userId.toUtf8(), &pPath, &pVersion, &pFileName);
	int64_t fileSize = 0;
	if (NetFileRes_OK == res && pPath && pPath[0] != '\0')
	{
		QFile netFile(QString::fromUtf8(pPath));
		if (netFile.open(QIODevice::ReadOnly))
		{
			// 先把文件大小也打印出来, 怀疑有时文件不存在导致更新不了. 后续定位到问题再删除
			fileSize = static_cast<int64_t>(netFile.size());
		}
	}

	WOLOG_INFO << "[getNetDiskFile] getNetFile res: " << res  << ",fileid: " << (fileId ? fileId : __X(""))
               << ",usreid: " << userId << ",path: " << (pPath ? pPath : "") << ",fileSize: " << fileSize;

    SUPBOOK_STAT stat = errorCode2SupbookStat(res);
    if (sbsValidFile != stat)
        return stat;
    path = krt::utf16(QString::fromUtf8(pPath));
    version = krt::utf16(QString::fromUtf8(pVersion));
    fileName = krt::utf16(QString::fromUtf8(pFileName));
    for (size_t i = 0; i < fileName.length(); ++i)
    {
        switch(fileName[i])
        {
        case __Xc('['):
            fileName[i] = __Xc('(');
            break;
        case __Xc(']'):
            fileName[i] = __Xc(')');
            break;
        }
    }
    return sbsValidFile;
}

SUPBOOK_STAT errorCode2SupbookStat(int errorCode)
{
    switch (errorCode)
    {
        case NetFileRes_OK:
            return sbsValidFile;
        case NetFileRes_NotFound:
            return sbsNotExist;
        case NetFileRes_AccessDenied:
            return sbsAccessDenied;
        case NetFileRes_FileProtected:
            return sbsFileProtected;
        case NetFileRes_NotInLocal:
            return sbsFileNotReady;
        case NetFileRes_RelationNotExists:
            return sbsRelationNotExists;
        case NetFileRes_FileTooLarge:
            return sbsFileTooLarge;
        case NetFileRes_CircularReference:
            return sbsCircularReference;
        case NetFileRes_InvalidRelation:
            return sbsInvalidRelation;
        case NetFileRes_InvalidURL:
            return sbsInvalidURL;
        case NetFileRes_Internal:
            return sbsInternal;
        case NetFileRes_InvalidOfficeType:
            return sbsInvalidOfficeType;
        case NetFileRes_SecureDoc:
            return sbsSecureDoc;
        case NetFileRes_RelateConflict:
            return sbsRelateConflict;
        case NetFileRes_NeePassword:
            return sbsNeePassword;
        case NetFileRes_NotExists:
            return sbsNotExist;
        case NetFileRes_ContentPermission:
			return sbsContentPermission;
        default:
            ASSERT(FALSE);
            return sbsUnknownErr;
    }
}

ks_stdptr<ISheetProtection> getSheetProtection(ISheet *pSheet)
{
	ASSERT(pSheet);
	ks_stdptr<IUnknown> ptrUnknown;
	pSheet->GetExtDataItem(edSheetProtection, &ptrUnknown);
	if (ptrUnknown == NULL) // 如果sheet没有edSheetProtection, 是没开启保护的
	{
		SHEETTYPE sheetType = stUnknown;
		pSheet->GetFullType(&sheetType);

		WOLOG_INFO << "[getSheetProtection] "
				   << "getSheetProtection(edSheetProtection) failed: ptrUnknown null, sheettype: " 
                   << sheetType;
		return ks_stdptr<ISheetProtection>();
	}

	ks_stdptr<ISheetProtection> ptrSheetProtection;
	ptrUnknown->QueryInterface(IID_ISheetProtection, (void**)&ptrSheetProtection);
	return ptrSheetProtection;
}

HRESULT ClearRangeFormatsAndValidation(KEtWorkbook* pEtWb, const RANGE& range)
{
    ks_stdptr<etoldapi::Range> spRange = pEtWb->CreateRangeObj(range);
    if (!spRange)
        return E_FAIL;
    
    HRESULT hr = spRange->ClearFormats();
    if (FAILED(hr))
        return hr;
    
    ks_stdptr<Validation> spValidation;
    spRange->get_Validation(&spValidation);
    if (spValidation)
        spValidation->Delete();
    return S_OK;
}

ROW FindLastNonEmptyRow(KEtRevisionContext* pCtx, ROW headerRow, IKWorksheet* pWorkSheet)
{
    ISheet* pSheet = pWorkSheet->GetSheet();
    RANGE usedRange(pSheet->GetBMP());
    pWorkSheet->GetUsedRange(&usedRange);
    ROW rowTo = usedRange.RowTo();
    RANGE range(pSheet->GetBMP());
    range.SetRows(usedRange.SheetFrom(), usedRange.SheetFrom(), 0, 0);
    for (ROW row = rowTo; row > headerRow; --row)
    {
        range.SetRowFromTo(row);
        if (!util::IsRangeEmpty(pCtx, pSheet, range))
            return row;
    }
    return INVALID_ROW;
}

VALIDATION_Wrap::VALIDATION_Wrap()
{
    *static_cast<VALIDATION*>(this) = VALIDATION{}; // donot use ks_memset_s
}

VALIDATION_Wrap::~VALIDATION_Wrap()
{
    ReleaseValidation(this);
}

void CollectMaxUserAndConn(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectMaxUserAndConn]";
        return;
    }
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    if (nullptr == wb)
    {
        WOLOG_ERROR << "nullptr wb in [CollectMaxUserAndConn]";
        return;
    }
    _Application* pApp = wb->GetCoreApp();
	IKUserConns* pUserConns = pApp->getUserConns();
	if (!pUserConns)
        return;

    WriteSvrFileinclude(binWriter, __X("behaviour_max_conn_count"), wb, pUserConns->getMaxConnCount());
    WriteSvrFileinclude(binWriter, __X("behaviour_max_user_count"), wb, pUserConns->getMaxUserCount());
}

void CollectXLookupCache(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectEtFormula]";
        return;
    }
    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectEtFormula]";
        return;
    }
    BMP_PTR pBmp = pKEtWorkbook->GetBMP();
    if (nullptr == pBmp)
    {
        WOLOG_ERROR << "nullptr BMP_PTR in [CollectEtFormula]";
        return;
    }
    _Workbook* pWorkbook = pKEtWorkbook->GetCoreWorkbook();
    if (nullptr == pWorkbook)
    {
        WOLOG_ERROR << "nullptr _Workbook in [CollectEtFormula]";
        return;
    }

    UINT32 searchCacheCnt = 0, cacheFailCnt = 0;
    IBookStake* pBookStake = pKEtWorkbook->GetCoreWorkbook()->GetBook()->GetWoStake();
    pBookStake->GetXLookupCacheInfo(searchCacheCnt, cacheFailCnt);

    if (0 == cacheFailCnt)
        return;
    IBook* pBook = pWorkbook->GetBook();
    {
        binWriter.beginStruct();
        {
            binWriter.addStringField(__X("fileinclude"), "name");
            binWriter.beginStruct("params");
            {
                FileInfoCollector::AddComponentInfo(binWriter, pBook);

                binWriter.addStringField(__X("behaviour_xlookup_cache_info"), "name");
                binWriter.addStringField(wbs->GetWorkbook()->getFileId(), "fileid");

                binWriter.addUint32Field(searchCacheCnt, "count");
                binWriter.addUint32Field(cacheFailCnt, "count2");
            }
            binWriter.endStruct();
        }
        binWriter.endStruct();
    }
}

void CollectAndSendFormulaResChangeByWebhook(IBook* pBook, IKWorksheets* pSheets)
{
    ks_stdptr<IWebhookManager> spWebhookMgr;
    pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    if (spWebhookMgr == nullptr)
        return;
    
    SlowCallTimeStat callTime("WebHook in Exec", 100);
    ks_stdptr<IETStringTools> spStringTool; 
    IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
    if (pCtx)
        spStringTool = pCtx->getStringTools();
    else
        _etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&spStringTool);

    bool bIsEnableCommonLog = false;
    IWoETSettings* pSettings = _kso_GetWoEtSettings();
    if (pSettings)
        bIsEnableCommonLog = pSettings->IsEnableCommonLog();
    ks_stdptr<IDbCollector> spDbCollector;
	pBook->GetExtDataItem(edBookDbCollector, (IUnknown **)&spDbCollector);
    for (int i = 0; i < pSheets->GetSheetCount(); ++i)
    {
        ISheet* pSheet = pSheets->GetSheetItem(i)->GetSheet();
        if (pCtx == nullptr && i == 0)
            spStringTool->SetEnv(pSheet);
        ICellCollector* spCollector = pSheet->GetWoStake()->getCellCollector();
        if (pSheet->IsGridSheet())
        {
            IEtWebhookChangedRange* pRange = spWebhookMgr->GetEtWebhookChangedRange();
            class SnapshotCellEnum : public IEnumSnapshotCell
            {
            public:
                SnapshotCellEnum(IEtWebhookChangedRange* pEtRange, IBook* pBook, UINT sheetId, IETStringTools* pStringTools)
                    : m_pRg(pEtRange), m_pBook(pBook), m_sheetId(sheetId), m_pStringTool(pStringTools) {}

                STDPROC Do(UINT rowIdx, UINT colIdx, const_token_ptr pToken) override
                {
                    const_token_ptr pNewToken = nullptr;
                    IDX sheetIdx = INVALIDIDX;
                    m_pBook->STSheetToRTSheet(m_sheetId, &sheetIdx);
                    ASSERT(sheetIdx != INVALIDIDX);
                    m_pBook->LeakOperator()->GetCellValue(sheetIdx, rowIdx, colIdx, &pNewToken);
                    ks_bstr oldStr;
                    m_pStringTool->GetCellText(pToken, NULL, &oldStr);
                    ks_bstr newStr;
                    m_pStringTool->GetCellText(pNewToken, NULL, &newStr);
                    m_pRg->CollectUpdateCells(m_sheetId, rowIdx, colIdx, oldStr.c_str(), newStr.c_str());
                    return S_OK;
                }

            private:
                IEtWebhookChangedRange* m_pRg = nullptr;
                IBook* m_pBook = nullptr;
                UINT m_sheetId = 0;
                IETStringTools* m_pStringTool = nullptr;
            };
            if (spWebhookMgr->HaveEtHook(Et_WebhookType_etUpdateSheet) || spWebhookMgr->HaveEtHook(Et_WebhookType_updateRanges) || spWebhookMgr->HaveEtHook(Et_WebhookType_etUpdateRange))
            {
                SnapshotCellEnum cellEnum(pRange, pBook, pSheet->GetStId(), spStringTool);
                spCollector->EnumDirtyCells(&cellEnum);
            }
        }
        else if (pSheet->IsDbSheet())
        {
            IDbWebhookChangedRange* pDbRange = spWebhookMgr->GetDbWebhookChangedRange();
            ks_stdptr<IDBSheetOp> spOp;
            DbSheet::GetDBSheetOp(pSheet, &spOp);
            class SnapshotCellEnum : public IEnumSnapshotCell
            {
            public:
                SnapshotCellEnum(IDbWebhookChangedRange* pDbRange, IDBSheetOp* pOp, bool bIsEnableCommonLog,
                                 bool bIsNeedFormulaResChange, IDbCollector* pDbCollector)
                    : m_pRg(pDbRange), m_pOp(pOp), m_bIsEnableCommonLog(bIsEnableCommonLog),
                    m_bIsNeedFormulaResChange(bIsNeedFormulaResChange), m_pDbCollector(pDbCollector)
                {
                    m_pAllRecords = m_pOp->GetAllRecords();
                    m_pAllFields = m_pOp->GetAllFields();
                }

                STDPROC Do(UINT rowIdx, UINT colIdx, const_token_ptr pToken) override
                {
                    ASSERT(m_pRg);
                    EtDbId recId = m_pAllRecords->IdAt(rowIdx);
                    EtDbId fldId = m_pAllFields->IdAt(colIdx);
                    const_token_ptr pNewToken = nullptr;
                    m_pOp->GetValueToken(recId, fldId, &pNewToken);
                    if (alg::IsExecTokenEqual(pToken, pNewToken) != S_OK)
                    {
                        ks_bstr value;
                        m_pOp->GetValueString(recId, fldId, &value);
                        ks_bstr oldValue;
                        m_pOp->GetValueString(pToken, fldId, &oldValue);
                        if (m_bIsNeedFormulaResChange)
                            m_pRg->CollectFormulaResChange(m_pOp->GetSheetId(), recId, fldId, oldValue.c_str(), value.c_str());
                        if (m_bIsEnableCommonLog && m_pDbCollector)
                            m_pDbCollector->CollectFormulaResChange(m_pOp->GetSheetId(), recId, fldId, oldValue.c_str(), value.c_str());
                    }
                    return S_OK;
                }
            private:
                IDbWebhookChangedRange* m_pRg = nullptr;
                IDBSheetOp* m_pOp = nullptr;
                const IDBIds* m_pAllRecords = nullptr;
                const IDBIds* m_pAllFields = nullptr;
                bool m_bIsEnableCommonLog = false;
                bool m_bIsNeedFormulaResChange = false;
                IDbCollector* m_pDbCollector = nullptr;
            };
            bool bIsNeedFormulaResChange = spWebhookMgr->IsNeedCollectFormulaResChange(pSheet->GetStId());
            if (bIsEnableCommonLog || bIsNeedFormulaResChange)
            {
                SnapshotCellEnum cellEnum(pDbRange, spOp, bIsEnableCommonLog, bIsNeedFormulaResChange, spDbCollector);
                spCollector->EnumDirtyCells(&cellEnum);
            }
        }
        spCollector->Clear();
    }
    spWebhookMgr->SendEtCallToServer();
}

void ClearCollectedWebhookChange(IBook* pBook)
{
    if (!pBook)
        return;

    ks_stdptr<IWebhookManager> spWebhookMgr;
    pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    if (spWebhookMgr == nullptr)
        return;
    
    IEtWebhookChangedRange* pEtRange = spWebhookMgr->GetEtWebhookChangedRange();
    if (pEtRange)
        pEtRange->Clear();

    IDbWebhookChangedRange* pDbRange = spWebhookMgr->GetDbWebhookChangedRange();
    if (pDbRange)
        pDbRange->Clear();
}

void CollectOneWayLinkHiddenSheetInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectOneWayLinkHiddenSheetInfo]";
        return;
    }
    wo::KEtWorkbook* pKEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pKEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectOneWayLinkHiddenSheetInfo]";
        return;
    }

    BMP_PTR pBmp = pKEtWorkbook->GetBMP();
    if (nullptr == pBmp)
    {
        WOLOG_ERROR << "nullptr BMP_PTR in [CollectOneWayLinkHiddenSheetInfo]";
        return;
    }
    if (FALSE == pBmp->bDbSheet)
        return;

    _Workbook* pWorkbook = pKEtWorkbook->GetCoreWorkbook();
    if (nullptr == pWorkbook)
    {
        WOLOG_ERROR << "nullptr _Workbook in [CollectOneWayLinkHiddenSheetInfo]";
        return;
    }
    IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
    if (nullptr == pWorksheets)
    {
        WOLOG_ERROR << "nullptr IKWorksheets in [CollectOneWayLinkHiddenSheetInfo]";
        return;
    }

    class LinkSheetEnum : public IDbLinkSheetEnum
    {
    public:
        STDPROC Do(IDBSheetOp *pSrcSheet, IDBSheetOp *pLinkSheet) override
        {
            IDbFieldsManager* pFieldsMgr = pLinkSheet->GetFieldsManager();
            const IDBIds* pFields = pLinkSheet->GetAllFields();
            for (EtDbIdx i = 0, fldCnt = pFields->Count(); i < fldCnt; ++i)
            {
                EtDbId fldId = pFields->IdAt(i);
                ks_stdptr<IDbField> spField;
                HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
                if (FAILED(hr))
                    return hr;

                ks_stdptr<IDbField_Link> spLinkFld = spField;
                if (spLinkFld && spLinkFld->IsSyncLink() && spLinkFld->GetLinkSheet() == pSrcSheet->GetSheetId())
                    linkerFieldCnt ++;
            }
            return S_OK;
        }
        int GetLinkerFieldCnt(){return linkerFieldCnt;}
    private:
        int linkerFieldCnt = 0;
    };

    QString valueFieldCnt(QStringLiteral("hidden_sheet_field_count"));
    QString valueRecordCnt(QStringLiteral("hidden_sheet_record_count"));
    QString valueLinkerFieldCnt(QStringLiteral("hidden_sheet_linker_field")); // 关联隐藏底表的字段个数

    ks_stdptr<IDbLinkManager> spLinkMgr;
    pWorkbook->GetBook()->GetExtDataItem(edBookDbLinkManager, (IUnknown**)&spLinkMgr);
    if (!spLinkMgr)
    {
        WOLOG_ERROR << "nullptr IDbLinkManager in [CollectOneWayLinkHiddenSheetInfo]";
        return;
    }

    bool bNeedCollect = false;
    for (int i = 0, count = pWorksheets->GetSheetCount(); i < count; ++i)
    {
        ISheet* pSheet = pWorksheets->GetSheetItem(i)->GetSheet();
        if (!pSheet->IsDbSheet())
            continue;

        ks_stdptr<IDBSheetOp> spOp;
        DbSheet::GetDBSheetOp(pSheet, &spOp);
        if(spOp && spOp->GetSheetSyncType() == DbSheet_St_Cross_DB)
        {
            bNeedCollect = true;
            EtDbIdx fieldCnt = spOp->GetAllFields()->Count();
            valueFieldCnt.append(QStringLiteral("_%1").arg(fieldCnt));

            EtDbIdx recordCnt = spOp->GetAllRecords()->Count();
            valueRecordCnt.append(QStringLiteral("_%1").arg(recordCnt));

            LinkSheetEnum enumer;
            UINT sheetId = spOp->GetSheetId();
            spLinkMgr->EnumLinkSheets(sheetId, &enumer);
            valueLinkerFieldCnt.append(QStringLiteral("_%1").arg(enumer.GetLinkerFieldCnt()));
        }
    }
    if (!bNeedCollect)
        return;

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        binWriter.addStringField(krt::utf16(valueFieldCnt), "name");
        binWriter.endStruct();
    }
    binWriter.endStruct();

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        binWriter.addStringField(krt::utf16(valueRecordCnt), "name");
        binWriter.endStruct();
    }
    binWriter.endStruct();

    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("fileinclude"), "name");
        binWriter.beginStruct("params");
        binWriter.addStringField(krt::utf16(valueLinkerFieldCnt), "name");
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

void CollectDbFunnelInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    if (nullptr == wbs)
    {
        WOLOG_ERROR << "nullptr KEtWorkbooks in [CollectDbFunnelInfo]";
        return;
    }
    wo::KEtWorkbook* pEtWorkbook = wbs->GetWorkbook();
    if (nullptr == pEtWorkbook)
    {
        WOLOG_ERROR << "nullptr KEtWorkbook in [CollectDbFunnelInfo]";
        return;
    }
    _Workbook* pWorkbook = pEtWorkbook->GetCoreWorkbook();
    if (nullptr == pWorkbook)
    {
        WOLOG_ERROR << "nullptr _Workbook in [CollectDbFunnelInfo]";
        return;
    }
    IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
    if (nullptr == pWorksheets)
    {
        WOLOG_ERROR << "nullptr IKWorksheets in [CollectDbFunnelInfo]";
        return;
    }

    for (int i = 0, cnt = pWorksheets->GetSheetCount(); i < cnt; ++i)
    {
        ks_castptr<etoldapi::_Worksheet> pWorksheet = pWorksheets->GetSheetItem(i);
        if (!pWorksheet)
            continue;

        ISheet* pSheet = pWorksheet->GetSheet();
        if (!pSheet || !pSheet->IsDbDashBoardSheet())
            continue;

		if (!pSheet->GetBMP()->bDbSheet && !pSheet->GetBMP()->bKsheet)
			continue;

        HRESULT hr = S_OK;
        KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(pEtWorkbook, pWorksheet);
        dashboardModuleMgrWrapper.EnumChart([&](KDbChartWrapper* pChartWrapper) {
            UINT count = 0;
            PCWSTR typeStr = nullptr;
            hr = pChartWrapper->GetFunnelInfo(count, typeStr);
            if (FAILED(hr) || !typeStr)
                return S_OK; // 枚举下一个图表

            binWriter.beginStruct();
            {
                binWriter.addStringField(__X("svr_fileinclude"), "name");
                binWriter.beginStruct("params");
                {
                    // application_dashboard_funnel_5_singleselect，表示5层漏斗，漏斗层字段类型是单选项
                    QString value(QStringLiteral("application_dashboard_funnel_%1_%2")
                        .arg(QString::number(count))
                        .arg(QString::fromUtf16(typeStr)));
                    binWriter.addStringField(krt::utf16(value), "name");
                    binWriter.addStringField(pEtWorkbook->getFileId(), "fileid");
                    FileInfoCollector::AddComponentInfo(binWriter, pWorkbook->GetBook());
                }
                binWriter.endStruct();
            }
            binWriter.endStruct();
            return S_OK;
        });
    }
}

void CollectCommentCount(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    CHECK_RETURN_VOID(wbs)
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    CHECK_RETURN_VOID(wb)
    IBook* pBook = wb->GetCoreWorkbook()->GetBook();
    CHECK_RETURN_VOID(pBook)

    INT nSheetCount = 0;
	pBook->GetSheetCount(&nSheetCount);
	int64_t total = 0, normalOnly = 0, webOnly = 0, mix = 0;
	for (INT i = 0; i < nSheetCount; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(i, &spSheet);
        CHECK_CONTINUE(spSheet)

		PCWSTR sheetName = nullptr;
		spSheet->GetName(&sheetName);

		ks_stdptr<IKDrawingCanvas> spCommentCanvas;
		ks_stdptr<IUnknown> spUnk;
		if (SUCCEEDED(spSheet->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk)))
			spUnk->QueryInterface(IID_IKDrawingCanvas, (void**)&spCommentCanvas);
        CHECK_CONTINUE(spCommentCanvas)
		ks_stdptr<ICellComments> spCMTs = spCommentCanvas;
		INT nCount = 0;
		spCMTs->GetCount(&nCount);
		if (nCount <= 0)
			continue;

		for (int i = 0; i < nCount; ++i)
		{
			ks_stdptr<ICellComment> spComment;
			spCMTs->GetItem(i, &spComment);
            CHECK_CONTINUE(spComment)
			ks_stdptr<IWoComment> spWoCmt;
			spComment->GetWoComment(&spWoCmt);
            CHECK_CONTINUE(spWoCmt)
            total++;

            bool hasNormal = false, hasWeb = false;
            auto collectCommentCount = [&spWoCmt, &hasNormal, &hasWeb](bool bIsResolved)
            {
    			for (size_t i = 0, chainCount = spWoCmt->GetChainCount(bIsResolved); i < chainCount && !(hasNormal && hasWeb); ++i)
                {
    				IWoCommentChain* pChain = spWoCmt->GetChainByIndex(bIsResolved, i);
    				CHECK_CONTINUE(pChain)
                    for (size_t j = 0, itemCount = pChain->Count(); j < itemCount && !(hasNormal && hasWeb); ++j)
                    {
                        IWoCommentItem* pItem = pChain->GetItem(j);
                        CHECK_CONTINUE(pItem)
                        if (pItem->IsNormal()) hasNormal = true;
                        else hasWeb = true;
                    }
    			}
    		};
			collectCommentCount(true);
			collectCommentCount(false);
            if (hasNormal && hasWeb)
                mix++;
            else if (hasNormal)
                normalOnly++;
            else if (hasWeb)
                webOnly++;
            else
                total--;
		}
	}

	QString value = QStringLiteral("behaviour_webcmt_%1_%2_%3_%4").arg(total).arg(normalOnly).arg(webOnly).arg(mix);

    WriteSvrFileinclude(binWriter, krt::utf16(value), wb);
}

void CollectDbAttachmentInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    CHECK_RETURN_VOID(wbs)
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    CHECK_RETURN_VOID(wb)
    IBook* pBook = wb->GetCoreWorkbook()->GetBook();
    CHECK_RETURN_VOID(pBook);

    enum AttachmentType {
        NONE,
        ONLY_PICTURE,
        ONLY_OTHER,
        PICTURE_AND_OTHER
    };
    INT nDbAttachmentFieldCnt = 0;
    INT nSheetCount = 0;
    pBook->GetSheetCount(&nSheetCount);
    std::vector<INT> vecAttachmentType;
    std::vector<UINT> vecMaxAttachmentCnt;
    std::vector<INT> vecAttachmentSinglePicture;
    std::vector<UINT> vecAttachmentPictureSize;
    static const std::unordered_set<PCWSTR, util::PcwstrIHash, util::PcwstrICmp> pictureTypes = {
        __X("png"), __X("jpeg"), __X("jpg"), __X("bmp"), __X("gif"), __X("heic"),
        __X("image/png"), __X("image/jpeg"), __X("image/jpg"), __X("image/bmp"), __X("image/gif"), __X("image/heic")
    };

    for (int i = 0; i < nSheetCount; ++i)
    {
        UINT attachmentPictureSize = 0;
        ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(i, &spSheet);
        CHECK_CONTINUE(spSheet);

        ks_stdptr<IDBSheetOp> spDbSheetOp;
        DbSheet::GetDBSheetOp(spSheet, &spDbSheetOp);
        if (spDbSheetOp == nullptr)
            continue;

        IDbFieldsManager *pFieldsManager = spDbSheetOp->GetFieldsManager();
        if (!pFieldsManager->FieldCnt(Et_DbSheetField_Attachment))
        {
            vecAttachmentPictureSize.push_back(attachmentPictureSize);
            continue;
        }
        const IDBIds *pFields = spDbSheetOp->GetAllFields();
        for (EtDbIdx fld = 0; fld < pFields->Count(); fld++)
        {
            EtDbId fieldId = pFields->IdAt(fld);
            ks_stdptr<IDbField> spField;
            HRESULT hr = pFieldsManager->GetField(fieldId, &spField);
            if (FAILED(hr))
                continue;
            if (spField->IsSyncLookupField())
			    continue;
            ET_DbSheet_FieldType fieldType = spField->GetType();
            if (fieldType != Et_DbSheetField_Attachment)
                continue;
            nDbAttachmentFieldCnt++;
            INT attachmentSinglePicture = 0;
            UINT maxAttachmentCnt = 0;
            AttachmentType attachmentType = NONE;
            const IDBIds* pRecords = spDbSheetOp->GetAllRecords();
            for (EtDbIdx j = 0; j < pRecords->Count(); ++j)
            {
                EtDbId recordId = pRecords->IdAt(j);
                const_token_ptr pToken = nullptr;
                hr  = spDbSheetOp->GetValueToken(recordId,fieldId,&pToken);
                if (FAILED(hr) || !pToken || !alg::const_handle_token_assist::is_type(pToken))
                    continue;
                alg::const_handle_token_assist chta(pToken);
                if (alg::ET_HANDLE_TOKENARRAY != chta.get_handleType())
                    continue;
                alg::TOKEN_HANDLE pHandle = chta.get_handle();
                if (!pHandle)
                    continue;
                ks_stdptr<IDbTokenArrayHandle> spTokenArray = pHandle->CastUnknown();
                UINT cnt = spTokenArray->GetCount();
                maxAttachmentCnt = std::max(maxAttachmentCnt, cnt);
                if (!cnt)
                    continue;
                for (UINT k = 0; k < cnt; ++k)
                {
                    const_token_ptr pItem = nullptr;
                    spTokenArray->Item(k, &pItem);
                    alg::const_handle_token_assist hta(pItem);
                    if (!hta)
                        continue;
                    const IDbAttachmentHandle* const pDbAttachmentHandle = hta.get_handle()->CastAttachment();
                    if (!pDbAttachmentHandle)
                        continue;
                    PCWSTR type = pDbAttachmentHandle->GetContentType();
                    auto it = pictureTypes.find(type); 
                    if (it != pictureTypes.end())
                    {
                        attachmentPictureSize += pDbAttachmentHandle->GetSize() >> 10;
                    }
                    if (attachmentType == NONE)
                    {
                        attachmentType = it == pictureTypes.end() ? ONLY_OTHER : ONLY_PICTURE;
                        continue;
                    }

                    if (attachmentType == ONLY_PICTURE && it == pictureTypes.end() ||
                        attachmentType == ONLY_OTHER && it != pictureTypes.end())
                    {
                        attachmentType = PICTURE_AND_OTHER;
                        continue;
                    }
                }
            }
            if (maxAttachmentCnt == 1 && attachmentType == ONLY_PICTURE) //单图
                attachmentSinglePicture = 1;

            vecAttachmentType.push_back(static_cast<INT>(attachmentType));
            vecMaxAttachmentCnt.push_back(maxAttachmentCnt);
            vecAttachmentSinglePicture.push_back(attachmentSinglePicture);
        }
        vecAttachmentPictureSize.push_back(attachmentPictureSize);
    }
    
    if (!nDbAttachmentFieldCnt)
        return;
    QString dbAttachmentType("table_attachment_only_pic");
    for (auto it : vecAttachmentType)
    {
        dbAttachmentType += QString("_%1").arg(it);
    }

    QString attachmentCnt("table_attachment_cnt_max");
    for (auto it : vecMaxAttachmentCnt)
    {
        attachmentCnt += QString("_%1").arg(it);
    }

    QString attachmentSinglePicture("table_attachment_only_one_pic");
    for (auto it : vecAttachmentSinglePicture)
    {
        attachmentSinglePicture += QString("_%1").arg(it);
    }

    QString sheetPictureSize("table_attachment_pic_size");
    for (auto it : vecAttachmentPictureSize)
    {
        sheetPictureSize += QString("_%1").arg(it);
    }
    WriteSvrFileinclude(binWriter, krt::utf16(dbAttachmentType), wb,nDbAttachmentFieldCnt);
    WriteSvrFileinclude(binWriter, krt::utf16(attachmentCnt), wb,nDbAttachmentFieldCnt);
    WriteSvrFileinclude(binWriter, krt::utf16(attachmentSinglePicture), wb,nDbAttachmentFieldCnt);
    WriteSvrFileinclude(binWriter, krt::utf16(sheetPictureSize), wb,nSheetCount);
}

HRESULT CollectDbFmlaInfo(ITokenVectorBase* pExecSeq, int currentTblId, ks_wstring &fmlaStr)
{
    if (nullptr == pExecSeq)
        return E_FAIL;
    int paramSize = 0;
    pExecSeq->GetSize(&paramSize);
    for (int idx = 0; idx < paramSize; ++idx)
    {
        const_token_ptr pToken = nullptr;
        HRESULT hr = pExecSeq->GetItem(idx, &pToken);
        if (FAILED(hr))
            return hr;

        switch (GetExecTokenMajorType(pToken))
        {
            case etexec::ETP_VDBL:
                fmlaStr += __X("D_");
            break;
            case etexec::ETP_VBOOL:
                fmlaStr += __X("B_");
            break;
            case etexec::ETP_VINT:
                fmlaStr += __X("I_");
            break;
            case etexec::ETP_VSTR:
                fmlaStr += __X("S_");
            break;
            case etexec::ETP_MATRIX:
                fmlaStr += __X("M_");
            break;
            case etexec::ETP_ERROR:
                fmlaStr += __X("E_");
            break;
            case etexec::ETP_GRID:
                fmlaStr += __X("G_");
            break;
            case etexec::ETP_HANDLE:
                fmlaStr += __X("H_");
            break;
            case etexec::ETP_VECTOR:
                fmlaStr += __X("V_");
            break;
            case etexec::ETP_PERREF:
                fmlaStr += __X("P_");
            break;
            case etexec::ETP_STREF:
                fmlaStr += __X("R_");
            break;
            case etexec::ETP_TABREF:
            {
                alg::const_tabref_token_assist tabRef(pToken);
                int rangeType = tabRef.get_range_type();
                int nameId = tabRef.get_name_id();
                if (nameId != currentTblId)
                    fmlaStr += __X("TS_");
                else if (rangeType == alg::ET_TABREF_RANGE_THISROW)
                    fmlaStr += __X("TR_");
                else if (rangeType == alg::ET_TABREF_RANGE_COLUMN)
                    fmlaStr += __X("TC_");
                break;
            }
            case etexec::ETP_OPT:
                fmlaStr += __X("O_");
            break;
            case etexec::ETP_FUNCTION:
            {
                etexec::const_function_token_assist	rcf(pToken);
                fmlaStr.AppendFormat(__X("%d_"),rcf.get_id());
            }
            break;
            case etexec::ETP_PROP:
                fmlaStr += __X("PR_");
            break;
            case etexec::ETP_ATTR:
                fmlaStr += __X("A_");
            break;
            case etexec::ETP_RESERVED:
                fmlaStr += __X("RE_");
            break;
            case etexec::ETP_CELLINT:
                fmlaStr += __X("C_");
            break;
            default:
                break;
        }
    }
    return S_OK;
}


void CollectDbFmlaFieldWillBeTransformed(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    CHECK_RETURN_VOID(wbs)
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    CHECK_RETURN_VOID(wb)
    IBook* pBook = wb->GetCoreWorkbook()->GetBook();
    CHECK_RETURN_VOID(pBook);
    IBookOp* pBookOp = pBook->LeakOperator();
    CHECK_RETURN_VOID(pBookOp);

    INT nFmlaFieldWillBeTransformed = 0;
    INT nFmlaFieldCnt = 0;
    INT nSheetCount = 0;
    INT currentNameId = -1;
    pBook->GetSheetCount(&nSheetCount);
    for (int i = 0; i < nSheetCount; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        CHECK_CONTINUE(spSheet);

        ks_stdptr<ICoreListObjects> spCoreListObjs;
        HRESULT hr = spSheet->GetExtDataItem(edSheetListObjects, (IUnknown**)&spCoreListObjs);
        if (FAILED(hr))
            continue;
        ks_stdptr<ICoreListObject> spCoreListObj;
        hr = spCoreListObjs->GetItem(0, &spCoreListObj);
        if (FAILED(hr))
            continue;
        currentNameId = spCoreListObj->GetNameId();
        
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        VS(DbSheet::GetDBSheetOp(spSheet, &spDbSheetOp));
        if (spDbSheetOp == nullptr)
            continue;
        IDbFieldsManager* pFieldsManager = spDbSheetOp->GetFieldsManager();
        if (!pFieldsManager->FieldCnt(Et_DbSheetField_Formula))
            continue;
        const IDBIds* pFields = spDbSheetOp->GetAllFields();
        for (EtDbIdx fld = 0, c = pFields->Count(); fld < c; fld++)
        {
            EtDbId fieldId = pFields->IdAt(fld);
            ks_stdptr<IDbField> spField;
            HRESULT hr = pFieldsManager->GetField(fieldId, &spField);
            if (FAILED(hr))
                continue;
            if (spField->IsSyncLookupField())
                continue;
            ET_DbSheet_FieldType fieldType = spField->GetType();
            if (fieldType != Et_DbSheetField_Formula)
                continue;
            ks_stdptr<IFormula> spFormula;
            hr = pBookOp->CreateFormula(&spFormula);
            if (FAILED(hr))
                continue;
            ks_stdptr<IDbField_Formula> spFormulaField;
            spFormulaField = spField;
            if (spFormulaField == nullptr)
                continue;

            ks_stdptr<ITokenVectorInstant> vec;
            hr = spFormulaField->GetFormula(&vec);
            if (FAILED(hr))
                continue;
            nFmlaFieldCnt++;
            CS_COMPILE_PARAM compileParam = spField->GetCompileParam(FML_CPL_THISROW);
            hr = spFormula->SetFormulaContent(vec);
            if (FAILED(hr))
                continue;
            BOOL bDynamic = spFormula->CheckArrayAndRepair(compileParam, AFRT_UserChoose);
            if (bDynamic)
            {
                nFmlaFieldWillBeTransformed++;
                ks_wstring fmlaTokenString(__X("behaviour_dbfmlafield_transform_"));
                hr = CollectDbFmlaInfo(vec, currentNameId, fmlaTokenString);
                if (FAILED(hr))
                    continue;
                if (!fmlaTokenString.empty())
                    fmlaTokenString.pop_back();
                WriteSvrFileinclude(binWriter,fmlaTokenString.data(), wb);
            }
        }
    }

    if (0 == nFmlaFieldCnt)
        return;
    WriteSvrFileinclude(binWriter,__X("behaviour_dbfmlafield_transform_arraytype"), wb,
        nFmlaFieldWillBeTransformed, nFmlaFieldCnt);
}

void CollectDashboardThemeInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    CHECK_RETURN_VOID(wbs)
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    CHECK_RETURN_VOID(wb)
    IBook* pBook = wb->GetCoreWorkbook()->GetBook();
    CHECK_RETURN_VOID(pBook);
    ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    pBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    CHECK_RETURN_VOID(spCustomStorMgr);

    UINT dashbordCount = 0;
    UINT applyThemeCount = 0;
    QStringList webExtCountStrList;
    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    for (UINT i = 0; i < sheetCount; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        CHECK_CONTINUE(spSheet);
        if (!spSheet->IsDbDashBoardSheet())
            continue;

        UINT sheetStId = spSheet->GetStId();
        QString applyThemeInfo = QString("behaviour_dashboard_apply_theme_%1").arg(QString::number(sheetStId));
        PCWSTR pValue = spCustomStorMgr->GetValue(sheetStId, __X("dashboardThemeId"));
        if (pValue)
        {
            if (xstrcmp(pValue, __X("null")) != 0 && xstrcmp(pValue, __X("\"default\"")) != 0)
                ++applyThemeCount;

            applyThemeInfo = applyThemeInfo % QString("_%1").arg(QString::fromUtf16(pValue));
        }
        else
        {
            applyThemeInfo = applyThemeInfo % QString("_null");
        }

        PCWSTR pBackgroundImgId = spCustomStorMgr->GetValue(sheetStId, __X("dashboardBackgroundImageId"));
        if (pBackgroundImgId)
        {
            applyThemeInfo = applyThemeInfo % QString("_%1").arg(QString::fromUtf16(pBackgroundImgId));
        }
        else
        {
            applyThemeInfo = applyThemeInfo % QString("_null");
        }
        WriteSvrFileinclude(binWriter, krt::utf16(applyThemeInfo), wb);

        UINT webExtensionCount = 0;
        IKWebExtensionMgr* pWebExtensionMgr = wb->GetCoreWorkbook()->GetWebExtensionMgr();
        if (pWebExtensionMgr)
        {
            pWebExtensionMgr->GetWebExtensionCount(spSheet, &webExtensionCount);
        }
        webExtCountStrList.append(QString::number(webExtensionCount));
        ++dashbordCount;
    }

    if (dashbordCount == 0)
        return;

    QString value(QStringLiteral("application_dashboard_theme_%1_%2_%3")
                                .arg(QString::number(dashbordCount))
                                .arg(QString::number(applyThemeCount))
                                .arg(webExtCountStrList.join("_")));
    WriteSvrFileinclude(binWriter, krt::utf16(value), wb);
}

namespace
{

INT getDbDashboardChartCount(KEtWorkbook* pWorkbook, ISheet* pSheet)
{
    IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return 0;

    INT chartCount = 0;
    UINT webExtensionCount = 0;
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    for (UINT i = 0; i < webExtensionCount; ++i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        HRESULT hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            continue;

        if (spWebExtension->GetWebShapeType() == WET_DbDataSource)
            ++chartCount;
    }
    return chartCount;
}

INT getDbDashboardWebExtInfo(KEtWorkbook* pWorkbook, ISheet* pSheet, std::array<int, DbSheet_Chart_Count>& webextInfo, INT& nDbDashboardRichTextCnt, INT& nDbDashboardPluginCnt, INT& nDbDashboardViewCnt)
{
    IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return 0;

    UINT webExtensionCount = 0;
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    for (UINT i = 0; i < webExtensionCount; ++i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        HRESULT hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            continue;

        auto WebExtType = spWebExtension->GetWebShapeType();
        switch (WebExtType)
        {
        case WET_DbPlugin:
        {
            ++nDbDashboardPluginCnt;
            break;
        }
        case WET_DbRichText:
        {
            ++nDbDashboardRichTextCnt;
            break;
        }
        case WET_DbDataSource:
        {
            PCWSTR pValue = nullptr;
            spWebExtension->GetProperty(__X("chart-info"), &pValue);
            KWebChartConfig config(pValue);
            ET_DBSheet_ChartType chartType = config.GetChartType();
            if (chartType == DbSheet_Chart_Unknown)
                continue;
            ++webextInfo[chartType];
            break;
        }
        case WET_DbView:
        {
            ++nDbDashboardViewCnt;
            break;
        }
        default:
            break;
        }
    }
    return webExtensionCount;
}

}

void CollectDashboardInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    CHECK_RETURN_VOID(wbs)
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    CHECK_RETURN_VOID(wb)
    IBook* pBook = wb->GetCoreWorkbook()->GetBook();
    CHECK_RETURN_VOID(pBook);
    ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    pBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    CHECK_RETURN_VOID(spCustomStorMgr);

    INT nDbDashboardCnt = 0;
    INT nDbDashboardWebExtCnt = 0;
    INT nDbDashboardChartCnt = 0;
    INT nDbDashboardPluginCnt = 0;
    INT nDbDashboardRichTextCnt = 0;
    INT nDbDashboardViewCnt = 0;
    std::array<int, DbSheet_Chart_Count> dashboardWebExtTypeCount{}; 
    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    for (UINT i = 0; i < sheetCount; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        CHECK_CONTINUE(spSheet);
        if (!spSheet->IsDbDashBoardSheet())
            continue;

        ++nDbDashboardCnt;
        nDbDashboardChartCnt += getDbDashboardChartCount(wb, spSheet);
        nDbDashboardWebExtCnt += getDbDashboardWebExtInfo(wb, spSheet, dashboardWebExtTypeCount, nDbDashboardRichTextCnt, nDbDashboardPluginCnt, nDbDashboardViewCnt);
    }

    if (nDbDashboardCnt == 0)
        return;

    // 柱状图数_折线图数_条形图数_饼状图数_统计数字数_柱线图数_漏斗图数_文本数_插件数
    QString value = QString(QStringLiteral("application_dashboard_%1_%2_%3_%4_%5_%6_%7_%8_%9_%10_%11_%12")).arg(nDbDashboardCnt).arg(nDbDashboardChartCnt).arg(nDbDashboardWebExtCnt)
                    .arg(dashboardWebExtTypeCount[DbSheet_Chart_BasicBar]).arg(dashboardWebExtTypeCount[DbSheet_Chart_BasicLine])
                    .arg(dashboardWebExtTypeCount[DbSheet_Chart_HorizBar]).arg(dashboardWebExtTypeCount[DbSheet_Chart_BasicPie])
                    .arg(dashboardWebExtTypeCount[DbSheet_Chart_Value]).arg(dashboardWebExtTypeCount[DbSheet_Chart_BarLine])
                    .arg(dashboardWebExtTypeCount[DbSheet_Chart_Funnel]).arg(nDbDashboardRichTextCnt)
                    .arg(nDbDashboardPluginCnt);
    // 倒计时数_进度图数_表格部件_矩阵气泡图数_面积图数    todo 添加词云图数_散点图数
    QString newValue = QString(QStringLiteral("application_dashboard_newadd_%1_%2_%3")).arg(dashboardWebExtTypeCount[DbSheet_Chart_CountDown])
                       .arg(dashboardWebExtTypeCount[DbSheet_Chart_Progress]).arg(nDbDashboardViewCnt).arg(dashboardWebExtTypeCount[DbSheet_Chart_MatrixBubble])
                       .arg(dashboardWebExtTypeCount[DbSheet_Chart_Area]);

    WriteSvrFileinclude(binWriter, krt::utf16(value), wb);
    WriteSvrFileinclude(binWriter, krt::utf16(newValue), wb);
}

constexpr int ET_DbSheet_FieldType_Collect_Count = ET_DbSheet_FieldType_Count + 2;
constexpr int Et_DbSheetField_Collect_SearchLookup = ET_DbSheet_FieldType_Count;
constexpr int Et_DbSheetField_Collect_Statistic = ET_DbSheet_FieldType_Count + 1;

int GetFieldType(IDbField* pField)
{
    ET_DbSheet_FieldType type = pField->GetType();
    if (type == Et_DbSheetField_Lookup)
    {
        ks_stdptr<IDbField_Lookup> spFieldLookup = pField;
        switch (spFieldLookup->GetLookupType()) {
        case DBLookupType::LT_LOOKUP:
            return Et_DbSheetField_Collect_SearchLookup;
        case DBLookupType::LT_STATISTIC:
            return Et_DbSheetField_Collect_Statistic;
        default:
            break;
        }
    }
    return type;
}

void GetFieldTypeName(int fieldType, PCWSTR* pTypeStr)
{
    switch (fieldType)
    {
    case Et_DbSheetField_Collect_SearchLookup:
        *pTypeStr = __X("SearchLookup");
        return;
    case Et_DbSheetField_Collect_Statistic:
        *pTypeStr = __X("Statistic");
        return;
    default:
        break;
    }
     _appcore_GainEncodeDecoder()->EncodeFieldType(static_cast<ET_DbSheet_FieldType>(fieldType), pTypeStr);
}

void CollectDashboardViewInfo(wo::KEtWorkbooks* wbs, binary_wo::BinWriter& binWriter)
{
    CHECK_RETURN_VOID(wbs)
    wo::KEtWorkbook* wb = wbs->GetWorkbook();
    CHECK_RETURN_VOID(wb)
    IBook* pBook = wb->GetCoreWorkbook()->GetBook();
    CHECK_RETURN_VOID(pBook);
    IKWebExtensionMgr* pWebExtensionMgr = wb->GetCoreWorkbook()->GetWebExtensionMgr();
    CHECK_RETURN_VOID(pWebExtensionMgr);

    std::vector<INT> vecSheetRecCnt;
    std::vector<INT> vecSheetFldCnt;
    std::array<std::vector<int>, ET_DbSheet_FieldType_Collect_Count> sheetsFieldTypeCount{}; // 可见字段使用情况

    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    for (UINT i = 0; i < sheetCount; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        CHECK_CONTINUE(spSheet);
        if (!spSheet->IsDbDashBoardSheet())
            continue;

        UINT webExtensionCount = 0;
        pWebExtensionMgr->GetWebExtensionCount(spSheet, &webExtensionCount);
        for (UINT j = 0; j < webExtensionCount; ++j)
        {
            ks_stdptr<IKWebExtension> spWebExtension;
            HRESULT hr = pWebExtensionMgr->GetWebExtension(spSheet, j, &spWebExtension);
            if (FAILED(hr))
                continue;

            if (spWebExtension->GetWebShapeType() != WET_DbView)
                continue;

            ks_stdptr<IEtWebExtension_View> spWebExtView = spWebExtension;
            IDX dbSheetIdx = INVALIDIDX;
            pBook->STSheetToRTSheet(spWebExtView->GetSheetId(), &dbSheetIdx);
            if (dbSheetIdx == INVALIDIDX)
                continue;
            ks_stdptr<ISheet> spDbSheet;
            pBook->GetSheet(dbSheetIdx, &spDbSheet);
            ks_stdptr<IDBSheetOp> spDbSheetOp;
            VS(DbSheet::GetDBSheetOp(spDbSheet.get(), &spDbSheetOp));

            vecSheetRecCnt.push_back(spDbSheetOp->GetAllRecords()->Count());
            vecSheetFldCnt.push_back(spDbSheetOp->GetAllFields()->Count());

            ks_stdptr<IDBSheetViews> spDbSheetViews;
            spDbSheet->GetExtDataItem(edSheetDbViews, (IUnknown**)&spDbSheetViews);
            if (!spDbSheetViews)
                continue;
            ks_stdptr<IDBSheetView> spDbSheetView;
            spDbSheetViews->GetItemById(spWebExtView->GetViewId(), &spDbSheetView);
            if (!spDbSheetView)
                continue;

            const IDBIds* pVisibleFields = spDbSheetView->GetVisibleFields();
            std::array<int, ET_DbSheet_FieldType_Collect_Count> fieldTypeCount{};
            IDbFieldsManager *pFieldsManager = spDbSheetOp->GetFieldsManager();
            for (EtDbIdx fld = 0; fld < pVisibleFields->Count(); fld++)
            {
                EtDbId fldId = pVisibleFields->IdAt(fld);
                ks_stdptr<IDbField> spField;
                HRESULT hr = pFieldsManager->GetField(fldId, &spField);
                if (FAILED(hr))
                    continue;
                fieldTypeCount[GetFieldType(spField)]++;
            }

            for (int fieldType = 0; fieldType < fieldTypeCount.size(); fieldType++)
                sheetsFieldTypeCount[fieldType].push_back(fieldTypeCount[fieldType]);
        }
    }

    if (vecSheetRecCnt.size() > 0)
    {
        QString name = QString("dashboard_view_rec_cnt");
        for (auto it = vecSheetRecCnt.begin(); it != vecSheetRecCnt.end(); ++it)
            name += QString("_%1").arg(*it);

        WriteSvrFileinclude(binWriter, krt::utf16(name), wb);
    }
    if (vecSheetFldCnt.size() > 0)
    {
        QString name = QString("dashboard_view_fld_cnt");
        for (auto it = vecSheetFldCnt.begin(); it != vecSheetFldCnt.end(); ++it)
            name += QString("_%1").arg(*it);

        WriteSvrFileinclude(binWriter, krt::utf16(name), wb);
    }

    for (int fieldType = 0; fieldType < sheetsFieldTypeCount.size(); fieldType++)
    {
        PCWSTR typeStr = nullptr;
        GetFieldTypeName(fieldType, &typeStr);
        QString name = QString("dashboard_view_fld_%1").arg(QString::fromUtf16(typeStr).toLower());
        const std::vector<int>& vec = sheetsFieldTypeCount[fieldType];
        int typeTotalCnt = 0;
        for (auto it = vec.begin(); it != vec.end(); ++it)
        {
            name += QString("_%1").arg(*it);
            typeTotalCnt += *it;
        }
        if (typeTotalCnt > 0)
            WriteSvrFileinclude(binWriter, krt::utf16(name), wb, typeTotalCnt);
    }
}

// =================================
struct WoConnEnumStrPair
{
	int m_enum;
	const char * m_str;
};
constexpr WoConnEnumStrPair gSvrConnFromStr[] = 
{
	{WoConnFromUnknown, "unknown"},
	{WoConnFromSession, "session"},
	{WoConnFromPreOpenApi, "preOpenApi"},
	{WoConnFromServerApi, "serverApi"},
	{WoConnFromServer, "server"},
	{WoConnFromAirscript2, "Airscript2"},
	{WoConnFromAirscript1Session, "Airscript1Session"},
	{WoConnFromAirscript1PreOpenApi, "Airscript1PreOpenApi"},
	{WoConnFromCore, "core"},
	{WoConnFromSharedViewSession, "sharedViewSession"},
};
constexpr WoConnEnumStrPair gSvrConnSceneStr[] = 
{
	{WoConnSceneUnknown, "unknown"},
	{WoConnSceneExport, "export"},
	{WoConnSceneAsyncJob, "asyncJob"},
	{WoConnSceneAttachmentSaveAs, "attachmentSaveAs"},
	{WoConnSceneNewAttachment, "newAttachment"},
	{WoConnSceneUpdateRange, "updateRange"},
	{WoConnScenePaste, "paste"},
	{WoConnSceneFileMerge, "fileMerge"},
	{WoConnSceneForm, "form"},
	{WoConnSceneAirSheetApp, "airSheetApp"},
	{WoConnSceneAiRecognize, "aiRecognize"},
};
constexpr WoConnEnumStrPair gSvrConnLifeStr[] = 
{
	{WoConnLifeUnknown, "unknown"},
	{WoConnLifeSession, "session"},
	{WoConnLifeApi, "api"},
	{WoConnLifeCommand, "command"},
	{WoConnLifeProcess, "server"},
};

template<std::size_t N>
inline constexpr const char * getSvrConnEnumStr(const WoConnEnumStrPair (&strPair)[N], int v)
{
	if (v < 0 || v >= N)
		return strPair[0].m_str;
	return strPair[v].m_str;
}

template<std::size_t N>
constexpr bool checkWoSvrConnEnumStr(const WoConnEnumStrPair (&strPair) [N], std::size_t exptSize)
{
	if (N != exptSize)
		return false;
	int i = 0;
	for (const auto & item : strPair)
	{
		if (i != item.m_enum)
			return false;
		++i;
	}
	return true;
}

static_assert(checkWoSvrConnEnumStr(gSvrConnFromStr, _WoConnFromCount), "WoConnFrom和gSvrConnFromStr不一致");
static_assert(checkWoSvrConnEnumStr(gSvrConnSceneStr, _WoConnSceneCount), "WoConnScene和gSvrConnSceneStr不一致");
static_assert(checkWoSvrConnEnumStr(gSvrConnLifeStr, _WoConnLifeCount), "WoConnLife和gSvrConnLifeStr不一致");

const char * toSvrConnFromStr(WoConnFrom from)
{
    return getSvrConnEnumStr(gSvrConnFromStr, from);
}

const char * toSvrConnSceneStr(WoConnScene scene)
{
    return getSvrConnEnumStr(gSvrConnSceneStr, scene);
}

const char * toSvrConnLifeStr(WoConnLife life)
{
    return getSvrConnEnumStr(gSvrConnLifeStr, life);
}
PCWSTR GetBlipTypeStr(IKBlipAtom* pBlipAtom)
{
	if (pBlipAtom == NULL)
		return __X("png");

	long blipType = koplBlipERROR;
	pBlipAtom->GetBlipType(&blipType);

	switch (blipType)
	{
	case koplBlipJPEG:
		return __X("jpeg");
	case koplBlipDIB:
		return __X("bmp");
	case koplBlipTIF:
		return __X("tiff");
	case koplBlipGIF:
		return __X("gif");
	case koplBlipEMF:
		return __X("emf");
	case koplBlipWMF:
		return __X("wmf");
	case koplBlipPICT:
		return __X("pict");
	case koplBlipPNG:
		return __X("png");
	case koplBlipWDP:
		return __X("wdp");
	default:
		ASSERT(FALSE);
		return __X("png");
	}
}

void _getPicData(IKBlipAtom* pBlipAtom, QByteArray& picData)
{
	if (!pBlipAtom)
		return;

	HGBL hgbl = NULL;
	pBlipAtom->GetHGlobal(&hgbl);
	if (!hgbl)
		return;

	int dataSize = XGlobalSize(hgbl);
	if (dataSize > 0)
	{
		char* data = (char*)XGlobalLock(hgbl);
		if (data)
		{
			picData.setRawData(data, dataSize);
			XGlobalUnlock(hgbl);
		}
	}
}

QString UploadImage(const drawing::AbstractShape* shape)
{
	const drawing::Blip& blip = shape->picture()->blip();
	IKBlipAtom* pBlipAtom = blip.blipSvg().isValid() ? blip.blipSvg().getCachePngBlipAtom() : blip.blipAtom();
	return UploadImage(pBlipAtom);
}

QString UploadImage(IKBlipAtom* pBlipAtom)
{
	if (!pBlipAtom)
		return QString();

	QByteArray picData;
	_getPicData(pBlipAtom, picData);

	QByteArray sha1;
	QCryptographicHash hasher(QCryptographicHash::Sha1);
	hasher.addData(picData);
	sha1 = hasher.result().toHex();

	if (!picData.isEmpty())
	{
		QString qBlipType(krt::fromUtf16(GetBlipTypeStr(pBlipAtom)));
		QByteArray imageType = qBlipType.toUtf8();
		WebSlice slice = { (WebByte*)(picData.data()), picData.size() };
		gs_callback->uploadImage(sha1.data(), imageType.data(), &slice, nullptr);
	}
	return QString(sha1);
}

void hanldeEtDashBoardSheet(IKWorksheets* pWorksheets)
{
    std::vector<IKWorksheet*> dashboardSheetIdxVec;
    for (int i = 0, sheetCnt = pWorksheets->GetSheetCount(); i < sheetCnt; ++i)
    {
        IKWorksheet* pWorkSheet = pWorksheets->GetSheetItem(i);
        if (pWorkSheet && pWorkSheet->GetSheet()->IsDbDashBoardSheet())
        {
            dashboardSheetIdxVec.push_back(pWorkSheet);
        }
    }

    for (auto it = dashboardSheetIdxVec.rbegin(); it != dashboardSheetIdxVec.rend(); ++it)
    {
        (*it)->DeleteDirectly();
    }
}

bool IsNeedAdjustStatSheet(KEtWorkbook* pWorkbook)
{
	IKWorksheets* pWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
	int sheetCount = pWorksheets->GetSheetCount();
	for (int i = 0; i < sheetCount; ++i)
	{
		ISheet* pSheet = pWorksheets->GetSheetItem(i)->GetSheet();
		if (pSheet->IsDbSheet())
		{
			ks_stdptr<IDBSheetOp> spDbOp;
			VS(pSheet->GetExtDataItem(edSheetDbOp, (IUnknown**)&spDbOp));
			if (spDbOp->GetSheetSubType() == DbSheet_Sub_Type_StatSheet)
			{
				ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
				VS(pSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData));
				if (spDBStatisticSheetData->NeedStatSheetAdjust())
				{
					return true;
				}
			}
		}
	}
	return false;
}

void SendOpenProgress(const char* connID, PCWSTR stage, PCWSTR subStage, 
        UINT nProcess, UINT64 nCurrent, UINT64 nTotal)
{
    IWoCallBack *pWoCb = _kso_GetWoCallBack();
	if (!pWoCb)
		return;
	::CollectInfo collectFunc = pWoCb->getCollectProgressFunc();

    binary_wo::BinWriter binWriter;
    QString str = krt::fromUtf8(connID);
    binWriter.addKey("data");
    binWriter.beginStruct();
        binWriter.addStringField(krt::utf16(str),"connID");
        binWriter.addStringField(__X("et_open_progress"),"action");
        binWriter.addStringField(stage,"stage");
        binWriter.addInt32Field(nProcess,"process");
        binWriter.addKey("substage");
        binWriter.beginStruct();
            binWriter.addStringField(subStage,"type");
            binWriter.addInt32Field(nCurrent,"current");
            binWriter.addInt32Field(nTotal,"total");
        binWriter.endStruct();
    binWriter.endStruct();
    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), (WebSize)binWriter.writeLength()};

    if (collectFunc)
        collectFunc("et_open_progress", &slice);

}

int64_t MemoStatProcPSS() 
{
#ifdef X_OS_LINUX
    constexpr char const pss_field[] = "Pss:"; 
    int64_t result = 0;
    FILE* smaps_file = NULL;
    char line[1024] = {0};
    if ((smaps_file = fopen("/proc/self/smaps_rollup", "r")) == NULL)
        return 0;

    while (fgets(line, sizeof(line) - 1, smaps_file) != NULL) 
    {
        char *field_ptr = strstr(line, pss_field);
        if (field_ptr == NULL) continue;

        char *value_ptr = field_ptr + ks_strlen(pss_field);
        unsigned long value = 0;
        char unit[8] = {0};

        //PPS: 12345 kB
        if (ks_sscanf_s(value_ptr, "%lu %3s", &value, unit) >= 1) 
        {
            if (strcmp(unit, "kB") == 0)
                result = value * 1024;
            return result;
        }
        break;
    }
#endif

    return 0;
}

void CollectMemInfo(PCWSTR name, unsigned int memUsageK, CollectInfoFunc collectFunc)
{
    if (memUsageK <= 1024) // 1M
        return;

    if (!collectFunc)
        return;

    if (!_kso_GetWoEtSettings())
        return;

    wo::WoEtSetting* settings = static_cast<wo::WoEtSetting*>(_kso_GetWoEtSettings());
    if (memUsageK < settings->GetCollectMemUsageThreshold() / 1024)
        return;

    binary_wo::BinWriter binWriter;
    binWriter.addStringField(name, "name");
    binWriter.addInt32Field(memUsageK, "count");

    LPCWSTR fileId = settings->GetFileId();
    if (fileId)
        binWriter.addStringField(fileId, "fileid");

    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), binWriter.writeLength()};
    collectFunc("fileinclude", &slice);
}

HRESULT BookRecalculateBehaviour(KEtWorkbook* wwb, const binary_wo::VarObj& param)
{
	IBook* pBook = wwb->GetCoreWorkbook()->GetBook();
	_Application* pApp = wwb->GetCoreApp();

	IBookStake* bs = pBook->GetWoStake();
	bs->ClearImportrangeCache();

	DWORD flag = RCAF_NONE;
	if (param.has("skipAiFormula") && param.field_bool("skipAiFormula"))
		flag = RCAF_SkipAiFmla;

	std::unique_ptr<DisableCalcBreak> upDcb;
	if (param.has("cannotBreakCalculate") && param.field_bool("cannotBreakCalculate"))
		upDcb.reset(new DisableCalcBreak);
	HRESULT hr = pBook->RecalculateAll(flag);
	upDcb.reset();

    return hr;
}

DbCdcScope::DbCdcScope(KEtWorkbook* pWorkbook, bool bIgnoreCache)
{
    m_bIgnoreCache = bIgnoreCache;
    IBook *pBook = pWorkbook->GetCoreWorkbook()->GetBook();
    if (pBook)
        pBook->GetExtDataItem(edBookDbAutomations, (IUnknown**)&m_spAutomations);
    if (m_spAutomations)
        m_spAutomations->Begin();

    IWoETSettings* pSettings = _kso_GetWoEtSettings();
    if (pSettings)
        m_bEnableCommonLog = pSettings->IsEnableCommonLog();
    if (m_bEnableCommonLog)
    {
        if (pBook)
            pBook->GetExtDataItem(edBookDbCommonLog, (IUnknown**)&m_spDbCommonLog);
        m_upSerializeHelper = std::make_unique<KDbValueSerializeHelper>();
        m_upSerializeHelper->Init(pWorkbook);
        if (m_spDbCommonLog)
            m_spDbCommonLog->Begin();
    }
}

DbCdcScope::~DbCdcScope()
{
    if (m_spDbCommonLog)
        m_spDbCommonLog->End(m_upSerializeHelper.get());
    if (m_spAutomations)
        m_spAutomations->End(m_bIgnoreCache);
}

} // namespace util
} // namespace wo
