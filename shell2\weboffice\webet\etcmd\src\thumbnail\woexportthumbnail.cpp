﻿#include "etstdafx.h"
#include "woexportthumbnail.h"
#include <QImage>
#include <QFile>

namespace wo
{

static
int pixelToTwip(int pixel, int dpi)
{
    return pixel * 1440 / dpi;
}

HRESULT WoExportThumbnail(IKWorksheetView *pSheetView, SIZE size, WebStr fileName)
{
    const int defaultDpi = 96;
    IRenderView* rdView = pSheetView->GetActiveRenderView();
    QImage thumbnail(size.cx, size.cy, QImage::Format_ARGB32_Premultiplied);

    kpt::PainterExt painter(&thumbnail);
    painter.setRenderHints(QPainter::SmoothPixmapTransform);

    SIZE drawSize = {pixelToTwip(size.cx, defaultDpi), pixelToTwip(size.cy, defaultDpi)};
    painter.fill(Qt::white);

    HRESULT hr = rdView->DrawThumbnail(&painter, &drawSize, NULL, FALSE);
    painter.end();

    QBuffer buffer;
    bool res = thumbnail.save(&buffer, "PNG");

    if (SUCCEEDED(hr) && res)
    {
        QFile file(QString::fromUtf16(fileName));
        if (file.open(QIODevice::WriteOnly))
        {
            file.write(buffer.buffer().data(), buffer.buffer().size());
            file.close();
            return S_OK;
        }
    }

    return E_FAIL;
}

} //wo