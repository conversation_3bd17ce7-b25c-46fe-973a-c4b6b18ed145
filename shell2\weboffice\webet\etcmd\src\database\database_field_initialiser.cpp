﻿#include "etstdafx.h"
#include "database_field_initialiser.h"
#include "database_field_context.h"
#include "helpers/protection_helper.h"

namespace wo
{
namespace Database
{

// ================== FieldFormulaInitialiser ==================
FieldFormulaInitialiser::FieldFormulaInitialiser()
{}

FieldFormulaInitialiser::FieldFormulaInitialiser(PCWSTR formula)
    : m_formula(formula)
{}

HRESULT FieldFormulaInitialiser::Initialise(FieldContext *pContext, const RANGE &rg)
{
    return SetFormula(pContext, rg, m_formula.c_str());
}

HRESULT FieldFormulaInitialiser::SetFormula(FieldContext *pContext, const RANGE &rg, PCWSTR formula)
{
    IEtRevisionContext *ctx = pContext->GetRevisionContext();
    if (ctx)
    {
        CHECK_PROTECTION_FORMULA(rg, formula, ctx);
    }

    ks_stdptr<IRangeInfo> host = pContext->CreateRangeObj(rg);
    ks_stdptr<IAppCoreRange> spCoreRgReplace;
    host->GetAppCoreRange(&spCoreRgReplace);

    FINDPARAM fp;
    fp.sWhat = __X("");
    fp.cellAfter.row = rg.RowTo();
    fp.cellAfter.col = rg.ColTo();
    fp.nACinRANGE = 0;
    fp.next = dirUp;
    fp.efcLookIn = etFind_Formulas;
    fp.efcLookAt = etFind_Whole;
    fp.bAutoExpand = FALSE;
    fp.bMatchCase = FALSE;
    fp.bMatchByte = FALSE;
    fp.bfindAll = TRUE;

    ks_stdptr<IBookOp> spBookOp;
    pContext->GetBook()->GetOperator(&spBookOp);
    app_helper::KBatchUpdateCal buc(spBookOp);

    CELL cellError = { 0 };
    cellError.row = -1;
    long lCount = 0;
    long lReadOnlyRichDataCount = 0;
    range_helper::ranges rgs;
    return spCoreRgReplace->Replace(fp, formula, cellError, &lCount, &lReadOnlyRichDataCount, fp.nACinRANGE, TRUE, &rgs);
}

HRESULT FieldFormulaInitialiser::CheckPermission(FieldContext *pContext, const RANGE &rg)
{
    IEtRevisionContext *ctx = pContext->GetRevisionContext();
    if (ctx == nullptr)
        return E_FAIL;

    CHECK_PROTECTION_ALLOW_EDIT(rg, ctx);
    return S_OK;
}
// ================== FieldFormulaInitialiser ==================

} // Database
} // wo
