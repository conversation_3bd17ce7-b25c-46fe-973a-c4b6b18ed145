#ifndef __WEBET_DB_FIELD_COPY_HELPER_H__
#define __WEBET_DB_FIELD_COPY_HELPER_H__

#include "wo/et_shared_str.h"
#include "etcore/et_core_dbsheet_enum.h"
#include "appcore/et_appcore_dbsheet.h"
#include "database/database_field_context.h"
#include "etcore/et_core_basic.h"
#include "database/database_def.h"
#include "helpers/db_field_recognize_helper.h"
#include "utils/qrlabel_utils.h"
#include <vector>
#include <array>

namespace wo
{

namespace DBSelectField {
	constexpr int ColorsCnt = 10;
	constexpr std::array<ARGB, ColorsCnt> Colors = {
		0xFFE0EBF9, 0xFFE1F2FC, 0xFFDCF7F7, 0xFFE2F5E3, 0xFFFDF3CF,
		0xFFFFF0D7, 0xFFFDE3DC, 0xFFF9E3EF, 0xFFEDE7FC, 0xFFF1F1F1
	};
}

class RowIterator
{
public:
	virtual ~RowIterator() {}
	virtual bool Valid() = 0;
	virtual void Next() = 0;
	virtual int GetCurRow() = 0;
	virtual void Reset() = 0;
	virtual int GetCurIdx() = 0;
};

class RowIteratorByRange : public RowIterator
{
public:
	RowIteratorByRange(int begin, int end) : m_begin(begin), m_cur(begin), m_end(end) 
	{}
	virtual ~RowIteratorByRange() {}
	virtual bool Valid() override
	{
		return m_begin >= 0 && m_begin <= m_cur && m_cur <= m_end;
	}
	virtual void Next() override { m_cur++; }
	virtual int GetCurRow() override { return m_cur; }
	virtual void Reset() override { m_cur = m_begin; }
	virtual int GetCurIdx() override { return m_cur; }
private:
	int m_begin = -1;
	int m_cur = -1;
	int m_end = -1;
};

class RowIteratorByIdx : public RowIterator
{
public:
	RowIteratorByIdx(std::vector<int>* pRowsVec) : m_pRowsVec(pRowsVec)
	{}
	virtual ~RowIteratorByIdx() {}
	virtual bool Valid() override
	{
		ASSERT(m_pRowsVec);
		return m_curIdx >= 0 && m_curIdx < static_cast<int>(m_pRowsVec->size());
	}
	virtual void Next() override { m_curIdx++; }
	virtual int GetCurRow() override { return m_pRowsVec->at(m_curIdx); }
	virtual void Reset() override { m_curIdx = 0; }
	virtual int GetCurIdx() override { return m_curIdx; }
private:
	int m_curIdx = 0;
	std::vector<int>* m_pRowsVec = nullptr;
};

struct ImgInfo
{
	PCWSTR fileId = nullptr;
	PCWSTR source = nullptr;
	PCWSTR type = nullptr;
	PCWSTR name = nullptr;
	UINT width = 0;
	UINT height = 0;
	UINT size = 0;
};
HRESULT GetAttachmentHandle(ImgInfo info, IDbTokenArrayHandle** ppHandle);
class KEtRevisionContext;
class Et2DbFieldCopyHelper
{
public:
	Et2DbFieldCopyHelper(_Workbook* pEtWorkbook, etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet, KEtRevisionContext* ctx, const RANGE& rg);
	void Init(PCWSTR pcwFilePath, BOOL bRequestFromApp, VarObj* pParam);

	void SetIterRowsByRange(int begin, int end) 
	{
		m_upRowIter = std::move(std::make_unique<RowIteratorByRange>(begin, end));
	}
	void SetIterRowsByIdx(std::vector<int>* pRowsVec)
	{
		m_upRowIter = std::move(std::make_unique<RowIteratorByIdx>(pRowsVec));
	}
	void ResetIter()
	{
		if (m_upRowIter)
			m_upRowIter->Reset();
	}

	bool Valid();
	void Next();
	int GetCurRow();

	void BeginDeduceFieldType();
	HRESULT DeduceFieldType(int col);
	void EndDeduceFieldType();
	void GetTypeInfo(ET_DbSheet_FieldType& fldType, PCWSTR& numFmt)
	{
		m_stFieldTypeRecHelper.GetTypeInfo(fldType, numFmt);
	}
	HRESULT CopyFieldExDetail(IDbField* pField, int col, bool bIgnoreChangeFieldType, bool bNeedOptimizeSelectField, bool bNeedAddItem = false);
	void CorrectionForPrimaryField(ET_DbSheet_FieldType& fldType, PCWSTR& numFmt);
	
	void BeginCopyData(bool bIsAttachmentType, int col);
	void CopyCellData(IDbField* pField, EtDbId recId, EtDbId fldId, int col, bool bIsAttachmentType);
	void EndCopyData(EtDbId fldId);
	void BatchProcessLeftImg(EtDbId fldId);

	HRESULT SetFieldName(IDbField* pField, int row, int col, PCWSTR pcwDefaultName, bool bIsSkipCell = false);

public:
	static HRESULT CheckDataValidation(IBookOp* pEtBookOp, wo::Database::FieldContext* pContext, RANGE& rg, ET_DbSheet_FieldType& type, wo::Database::FieldType& etType);
private:
	HRESULT _GetTypeByFieldType(RANGE& rg, etoldapi::Range* pEtApiRange, ET_DbSheet_FieldType& dbType, Database::FieldType& etType);
	HRESULT _OptimizeSingleSelectField(etoldapi::_Worksheet* pEtWorksheet, int col);
	HRESULT _AddItems2StrSet(etoldapi::Range* pEtApiRange);
	void attachmentImgHandler(WebSlice* output);
	void localImgHandler(WebSlice* output);
	void batchProcessImg(WebSlice* output, EtDbId fldId);
	bool needSkip(const GlobalSharedString& fileId, EtDbId recId, EtDbId fldId);
	void batchProcessAttachmentImg(EtDbId fldId);
	void batchProcessLocalImg(EtDbId fldId);
	HRESULT BatchCopyAttachmentFieldData(int row, int col, EtDbId recId, EtDbId fldId);
	HRESULT BeforeCopyQRLabelImg(int col);
	void ClearAttachmentCache();

private:
	std::unique_ptr<RowIterator> m_upRowIter;
	DBFieldTypeRecHelper m_stFieldTypeRecHelper;
private:
	ks_stdptr<_Workbook> m_spEtApiWb = nullptr;
	ks_stdptr<IDBSheetOp> m_spDbSheetOp = nullptr;
	IBookOp* m_pEtBookOp = nullptr;
	int m_etSheetIdx;
	KEtRevisionContext* m_pCtx = nullptr;
	ks_stdptr<IKHyperlinks> m_spHyperlinks = nullptr;
	ICellImages* m_pImages = nullptr;
	ks_stdptr<ISheet> m_spEtSheet = nullptr;
	etoldapi::_Worksheet* m_pSrcWorkSheet = nullptr;
	RANGE m_etRange;
	Database::FieldContext m_fieldContext;
	ks_stdptr<IETStringTools> m_spStringTools;
private:
	PCWSTR m_pcwConnId;
	PCWSTR m_pcwFilePath;
	ks_wstring m_wstrDirPathStr;
	BOOL m_bRequestFromApp;
private:
	bool m_bIsQRLabel = false;
	bool m_bHasList = false;
	// IDbField_Rating 
	int m_iMaxRating = 0;
	// IDbField_Select
	GlobalSharedStringUnorderedSet m_strSet;

private:
	// 图片导入相关
	GlobalSharedStringHashMap<const_token_ptr> m_imgInfoCache;
	GlobalSharedStringHashMap<std::vector<EtDbId>> m_stPendingImgPos;
	std::vector<std::pair<GlobalSharedString, PCWSTR>> m_stAttachmentMap;
	std::vector<std::pair<GlobalSharedString, PCWSTR>> m_stLocalImgMap;
	static constexpr size_t m_queryBatchSize = 20;
	static constexpr size_t m_uploadBatchSize = 10;
	std::shared_ptr<util::QRLabelHelper> m_spQrLabelHelperPtr;
};
} // namespace wo

#endif