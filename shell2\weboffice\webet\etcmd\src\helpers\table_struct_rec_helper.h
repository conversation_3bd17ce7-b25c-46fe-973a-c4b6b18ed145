#ifndef __WEBET_STRUCT_HEAD_REC__
#define __WEBET_STRUCT_HEAD_REC__

class KEtWorkbook;
class KEtRevisionContext;
namespace wo
{
	namespace TableStructRecHelper
	{
		enum TableStructRecErrorCode
		{
			TableStructRecNull,
			TableStructRecOverLimit,
			TableStructRecFailed,
			TableStructRecSuccessed
		};
		struct TableTitleInfo
		{
			TableStructRecErrorCode errorCode = TableStructRecNull;
			ROW row = INVALID_ROW;
			COL colFrom = INVALID_ROW;
			COL colTo = INVALID_ROW;
			BOOL bMerged = FALSE;
		};
		PCWSTR ConvertErrCodeToString(TableStructRecErrorCode);
		//Todo:后面再支持用户可以依据用户选择的表头去调整，作为表头识别的算法因子。
		TableStructRecErrorCode calcTitleCnt(ISheet*, const RANGE&, bool, COL&, COL&, int&);
		void getContentBorders(ISheet*, const RANGE&, COL&, COL&);
		HRESULT recPhoneNumCol(ISheet*, const RANGE&, OUT std::unordered_set<int>& phoneColIdxs);
		HRESULT GetTableTitleInfo(KEtWorkbook*, KEtRevisionContext*, IDX, TableTitleInfo&);
		HRESULT GetResult(TableStructRecErrorCode);
	} // namespace TableStructRecHelper
} // namespace wo

#endif // __WEBET_STRUCT_HEAD_REC__
