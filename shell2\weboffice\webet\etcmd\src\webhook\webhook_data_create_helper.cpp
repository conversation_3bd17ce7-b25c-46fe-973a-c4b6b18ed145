#include "etstdafx.h"
#include "webhook_data_create_helper.h"
#include "etcore/et_core_dbsheet.h"
#include "appcore/et_appcore_dbsheet.h"
#include "etcore/et_core_basic.h"
#include "etx_result.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "webhook_param_validator.h"
#include <vector>
#include "webhook/webhook_common.h"
#include "webhook/webhook_enum.h"
#include "webhook/webhook_basic_itf.h"
#include "webhook/webhook_guid.c"
#include "webhook_data_validator.h"
#include "ettools/ettools_encode_decoder.h"
#include "dbsheet/et_dbsheet_provider.h"

HRESULT WebhookDataCreateHelper::writeHookResult(INT code, PCWSTR message)
{
    m_pRes->beginStruct();
    m_pRes->addStringField(m_hookId.c_str(), "hook_id");
    m_pRes->addInt16Field(code, "code");
    m_pRes->addStringField(message, "msg");
    m_pRes->endStruct();
    return S_OK;
}

namespace
{
#define VALIDATE_PARAMS(OPERATION) \
OPERATION##ParamsValidator stValidator(m_pBook); \
*m_pValid = stValidator.ValidateParams(m_hook, m_res, m_pData);

using namespace webhook_data;
struct rflx_validator_visitor
{
private:
    IBook* m_pBook = nullptr;
    IWebhookData* m_pData = nullptr;
    binary_wo::VarObj& m_hook;
    binary_wo::BinWriter& m_res;
    bool* m_pValid = nullptr;

public:
    rflx_validator_visitor(IBook* pBook, IWebhookData* pData, binary_wo::VarObj& hook, binary_wo::BinWriter& res, bool* pValid)
     : m_pBook(pBook), m_pData(pData), m_hook(hook), m_res(res), m_pValid(pValid) {}
    ~rflx_validator_visitor() = default;
    void operator()(const CreateSheet &data) {}
    void operator()(const RenameSheet &data)
    {
        VALIDATE_PARAMS(RenameSheet);
    }
    void operator()(const UpdateSheetDescription &data)
    {
        VALIDATE_PARAMS(UpdateSheetDesc);
    }
    void operator()(const UpdateSheetIcon &data)
    {
        VALIDATE_PARAMS(UpdateSheetIcon);
    }
    void operator()(const RemoveSheet &data)
    {
        VALIDATE_PARAMS(RemoveSheet);
    }
    void operator()(const CreateView &data)
    {
        VALIDATE_PARAMS(CreateView);
    }
    void operator()(const RenameView &data)
    {
        VALIDATE_PARAMS(RenameView);
    }
    void operator()(const RemoveView &data)
    {
        VALIDATE_PARAMS(RemoveView);
    }
    void operator()(const CreateField &data)
    {
        VALIDATE_PARAMS(CreateField);
    }
    void operator()(const UpdateField &data)
    {
        VALIDATE_PARAMS(UpdateField);
    }
    void operator()(const UpdatePrimaryField &data)
    {
        VALIDATE_PARAMS(UpdatePrimaryField);
    }
    void operator()(const RemoveField &data)
    {
        VALIDATE_PARAMS(RemoveField);
    }
    void operator()(const CreateRecord &data)
    {
        VALIDATE_PARAMS(CreateRecord);
    }
    void operator()(const RemoveRecord &data)
    {
        VALIDATE_PARAMS(RemoveRecord);
    }
    void operator()(const UpdateCells &data)
    {
        VALIDATE_PARAMS(UpdateCells);
    }
    void operator()(const UpdateSheet &data)
    {
        VALIDATE_PARAMS(UpdateSheet);
    }
    void operator()(const UpdateFieldCells &data)
    {
        VALIDATE_PARAMS(UpdateFieldCells);
    }
    void operator()(const UpdateRecordCells &data)
    {
        VALIDATE_PARAMS(UpdateRecordCells);
    }
    void operator()(const UpdateRecords &data)
    {
        VALIDATE_PARAMS(UpdateRecords);
    }
    void operator()(const UpdateRecordsParent &data)
    {
        VALIDATE_PARAMS(UpdateRecordsParent);
    }
    void operator()(const UpdateSheetsAllChange &data)
    {
        VALIDATE_PARAMS(UpdateSheetsAllChange);
    }
    void operator()(const CreateAndFillInRecord &data)
    {
        VALIDATE_PARAMS(CreateAndFillInRecord);
    }
    void operator()(const EtUpdateSheet &data)
    {
        VALIDATE_PARAMS(EtUpdateSheet);
    }
    void operator()(const EtUpdateRanges &data)
    {
        VALIDATE_PARAMS(EtUpdateRanges);
    }
    void operator()(const EtUpdateCharts &data)
    {
        VALIDATE_PARAMS(EtUpdateCharts);
    }
    void operator()(const EtRemoveSheet &data)
    {
        VALIDATE_PARAMS(EtRemoveSheet);
    }
    void operator()(const EtInsertRows &data)
    {
        VALIDATE_PARAMS(EtInsertRows);
    }
    void operator()(const EtRemoveRows &data)
    {
        VALIDATE_PARAMS(EtRemoveRows);
    }
    void operator()(const EtInsertColumns &data)
    {
        VALIDATE_PARAMS(EtInsertCols);
    }
    void operator()(const EtRemoveColumns &data)
    {
        VALIDATE_PARAMS(EtRemoveCols);
    }
    void operator()(const EtUpdateRange &data)
    {
        VALIDATE_PARAMS(EtUpdateRange);
    }
    void operator()(const EtUpdateSheetsAllChange &data)
    {
        VALIDATE_PARAMS(EtUpdateSheetsAllChange);
    }
};
}  // namespace anonymous

HRESULT WebhookDataCreateHelper::CreateWebhookData(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData** ppData, bool update, bool add)
{
    HRESULT hr = S_OK;
    m_hookId = __X("");
    m_pRes = &res;
    WebhookParamsValidator webhookParamsValidator(m_pBook);
    bool b = webhookParamsValidator.ValidateParams(hook, res, nullptr);
    if (!b)
        return E_FAIL;
    m_hookId = hook.field_str("hook_id");
    ks_stdptr<IWebhookData> spData;
    ks_stdptr<IWebhookDataCreator> spCreator;
    _webhook_CreateObject(CLSID_KWebhookDataCreator, IID_IWebhookDataCreator, reinterpret_cast<void **>(&spCreator));
    WebhookDataCreatorProvider provider(m_pBook);
    hr = spCreator->Set(hook, m_pRes, &provider);
    if (FAILED(hr))
        return hr;
    hr = spCreator->Create(&spData);
    if (FAILED(hr))
        return hr;
    webhook_data::CreateData* pCreateData = spCreator->GetCreateData();
    bool bIsValid = true;
    try
    {
        std::visit(rflx_validator_visitor {m_pBook, spData.get(), hook, res, &bIsValid}, pCreateData->rule);
    }
    catch(std::exception &e)
    {
        ks_wstring wstrErrorMsg = krt::utf16(e.what());
        writeHookResult(Webhook_Invalid_Argument, wstrErrorMsg.c_str());
        bIsValid = false;
    }
    if (!bIsValid)
        return E_FAIL;
    *ppData = spData.detach();
    return S_OK;
}

bool WebhookDataCreateHelper::hasEtUpdateHookPermission(bool update, bool add)
{
    if (!update) return true;
    if (!add) return true;
    ks_stdptr<IWebhookManager> spWebhookMgr;
    m_pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    return !spWebhookMgr->isBookHasHidden();
}

namespace webhook_data
{
struct rflx_type_visitor
{
private:
    ks_stdptr<IDbFcValueBase> spVal;
    IDbFcValueBase** ppValue;

public:
    rflx_type_visitor(IDbFcValueBase** ppValue) : ppValue(ppValue) {}
    ~rflx_type_visitor()
    {
        *ppValue = spVal.detach();
    }

    void operator()(const ks_wstring& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueText, IID_IDbFcValueText, (void**)&spVal));
        ks_stdptr<IDbFcValueText> spValue = spVal;
        spValue->SetValue(value.c_str());
    }

    void operator()(const filter_common_value& value)
    {
        std::visit(rflx_type_visitor(ppValue), value);
    }

    void operator()(const FilterNullValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueNull, IID_IDbFcValueNull, (void**)&spVal));
    }

    void operator()(const FilterAnyValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueAny, IID_IDbFcValueAny, (void**)&spVal));
    }

    void operator()(const FilterTextValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueText, IID_IDbFcValueText, (void**)&spVal));
        ks_stdptr<IDbFcValueText> spValue = spVal;
        spValue->SetValue(value.value.c_str());
    }

    void operator()(const FilterNumValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueNum, IID_IDbFcValueNum, (void**)&spVal));
        ks_stdptr<IDbFcValueNum> spValue = spVal;
        spValue->SetValue(value.value);
    }

    void operator()(const FilterDateValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueDate, IID_IDbFcValueDate, (void**)&spVal));
        ks_stdptr<IDbFcValueDate> spValue = spVal;
        KDbFcValueDateInfo date {};
        if (value.year)
            date.year = value.year.value();
        if (value.mon)
            date.mon = value.mon.value();
        if (value.mday)
            date.mday = value.mday.value();
        if (value.hour)
            date.hour = value.hour.value();
        if (value.min)
            date.min = value.min.value();
        if (value.sec)
            date.sec = value.sec.value();
        spValue->SetValue(date);
    }

    void operator()(const FilterTimeValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueTime, IID_IDbFcValueTime, (void**)&spVal));
        ks_stdptr<IDbFcValueTime> spValue = spVal;
        KDbFcValueTimeInfo time {};
        if (value.hour)
            time.hour = value.hour.value();
        if (value.min)
            time.min = value.min.value();
        if (value.sec)
            time.sec = value.sec.value();
        spValue->SetValue(time);
    }

    void operator()(const FilterSelectItemValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueSelectItem, IID_IDbFcValueSelectItem, (void**)&spVal));
        ks_stdptr<IDbFcValueSelectItem> spValue = spVal;
        spValue->SetSelectItemId(nullptr, value.value);
    }

    void operator()(const FilterContactValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueContact, IID_IDbFcValueContact, (void**)&spVal));
        ks_stdptr<IDbFcValueContact> spValue = spVal;
        spValue->SetValue(value.value.c_str());
    }

    void operator()(const FilterLinkValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueLink, IID_IDbFcValueLink, (void**)&spVal));
        ks_stdptr<IDbFcValueLink> spValue = spVal;
        if (std::holds_alternative<ks_wstring>(value.value))
        {
            spValue->SetValue(INV_EtDbId);
            spValue->SetContent(__X(""));
        }
        else
        {
            spValue->SetValue(std::get<EtDbId>(value.value));
            spValue->SetContent(value.content.c_str());
        }
    }

    void operator()(const FilterCheckboxValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueCheckbox, IID_IDbFcValueCheckbox, (void**)&spVal));
        ks_stdptr<IDbFcValueCheckbox> spValue = spVal;
        spValue->SetValue(value.value);
    }

    void operator()(const FilterDynamicSimpleValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueDynamicSimple, IID_IDbFcValueDynamicSimple, (void**)&spVal));
        ks_stdptr<IDbFcValueDynamicSimple> spValue = spVal;
        KDbFcDynamicType tp = DBFCDT_Today;
        HRESULT hr = _ettools_GainEncodeDecoder()->DecodeKDbFcDynamicType(value.dynamicType.c_str(), &tp);
        if (FAILED(hr))
            return;
        spValue->SetDynamicType(tp);
    }

    void operator()(const FilterDynamicThresholdValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueDynamicThreshold, IID_IDbFcValueDynamicThreshold, (void**)&spVal));
        ks_stdptr<IDbFcValueDynamicThreshold> spValue = spVal;
        KDbFcDynamicType tp = DBFCDT_Today;
        HRESULT hr = _ettools_GainEncodeDecoder()->DecodeKDbFcDynamicType(value.dynamicType.c_str(), &tp);
        if (FAILED(hr))
            return;
        spValue->SetDynamicType(tp);
        spValue->SetThreshold(value.threshold);
    }

    void operator()(const FilterDepartmentValue& value)
    {
        VS(_appcore_CreateObject(CLSID_KDbFcValueDepartment, IID_IDbFcValueDepartment, (void**)&spVal));
        ks_stdptr<IDbFcValueDepartment> spValue = spVal;
        spValue->SetValue(value.value.c_str());
        spValue->SetId(value.deptId.c_str());
    }
};
}

WebhookDataCreatorProvider::WebhookDataCreatorProvider(IBook* pBook)
{
    m_pBook = pBook;
}

STDIMP_(void) WebhookDataCreatorProvider::InstallFilter(IWebhookData* pData, webhook_data::Filter filter, UINT sheetId)
{
    wo::DBSheetCommonHelper commonHelper(m_pBook);
    commonHelper.GetDBSheetOp(sheetId, &m_spDbSheetOp);
    ks_stdptr<IDbExtraFilter> spExtraFilter;
    HRESULT hr = getFilter(filter, &spExtraFilter);
    if (FAILED(hr))
        return;
    pData->SetFilter(spExtraFilter);

}

STDIMP_(void) WebhookDataCreatorProvider::InstallFilterResult(IWebhookData* pData, webhook_data::Filter filter, UINT sheetId)
{
    wo::DBSheetCommonHelper commonHelper(m_pBook);
    commonHelper.GetDBSheetOp(sheetId, &m_spDbSheetOp);

    ks_stdptr<IDbExtraFilter> spExtraFilterResult;
    HRESULT hr = getFilter(filter, &spExtraFilterResult);
    if (FAILED(hr))
        return;
    pData->SetFilterResult(spExtraFilterResult);
}

HRESULT WebhookDataCreatorProvider::getFilter(webhook_data::Filter filter, IDbExtraFilter** ppFilter)
{
    ks_stdptr<IDbExtraFilter> spExtraFilter;
    VS(_appcore_CreateObject(CLSID_KDbExtraFilter, IID_IDbExtraFilter, (void**)& spExtraFilter));
    spExtraFilter->Init(m_spDbSheetOp);
	std::stack<IDbExtraFilter*> filterStack;
	filterStack.push(spExtraFilter);
	std::stack<webhook_data::Filter> jsonStack;
	jsonStack.push(filter);
	HRESULT hr = S_OK;
	bool validFilter = false;

	while (not filterStack.empty())
	{
		// 分析参数
		IDbExtraFilter* pTopFilter = filterStack.top();
		webhook_data::Filter param = jsonStack.top();
		filterStack.pop();
		jsonStack.pop();

		KDbFilterOpType op = DBFOT_And;
        hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterOpType(param.mode.c_str(), &op);
        if (FAILED(hr))
            return E_DBSHEET_FILTER_INVALID;
		if (op != DBFOT_And && op != DBFOT_Or)
			return E_DBSHEET_FILTER_INVALID;
		hr = pTopFilter->SetOperator(op);

		// 叶子节点
		if (param.criteria)
		{
			validFilter = true;
			if (param.filters)
				return E_DBSHEET_FILTER_INVALID;
			const auto& criteria = param.criteria;
			for (int32 i = 0, cnt = criteria->size(); i < cnt; ++i)
			{
				const auto& criterion = criteria->at(i);
				EtDbId fldId = INV_EtDbId;
				if (criterion.fieldId) // 兼容一下方便其他场景复用
                    fldId = criterion.fieldId.value();
				ks_stdptr<IDbFieldFilter> spFieldFilter;
				hr = pTopFilter->AddFieldFilter(fldId, &spFieldFilter);
				if (FAILED(hr))
					return E_DBSHEET_FILTER_INVALID;

				ks_stdptr<IDbFilterCriteria> spCriteria;
				hr = createDbFilterCriteria(criterion, &spCriteria);
				if (FAILED(hr))
					return E_DBSHEET_FILTER_INVALID;

				hr = spFieldFilter->SetCriteria(spCriteria);
				if (FAILED(hr))
					return E_DBSHEET_FILTER_INVALID;
			}
		}
		// 非叶子节点
		if (param.filters)
		{
			validFilter = true;
			if (param.criteria)
				return E_DBSHEET_FILTER_INVALID;
			const auto& filters = param.filters;
			for (int32 i = 0, cnt = filters->size(); i < cnt; ++i)
			{
				ks_stdptr<IDbExtraFilter> itemFilter;
				VS(pTopFilter->AddExtraFilter(&itemFilter));
				filterStack.push(itemFilter.get());
				jsonStack.push(filters->at(i));
			}
		}
	}
	return validFilter ? S_OK : S_FALSE;

    *ppFilter = spExtraFilter.detach();
    return S_OK;

}

HRESULT WebhookDataCreatorProvider::createDbFilterCriteria(const webhook_data::Criteria criteria, IDbFilterCriteria** ppVal)
{
	ks_stdptr<IDbFilterCriteria> spCriteria;
	VS(_appcore_CreateObject(CLSID_KDbFilterCriteria, IID_IDbFilterCriteria, (void**)&spCriteria));
	std::unique_ptr<KDbDataProvider> upProvider = std::make_unique<KDbDataProvider>(m_pBook);
	spCriteria->Init(upProvider.release());
	KDbFilterCriteriaOpType op = DBFCOT_Null;
	HRESULT hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterCriteriaOpType(criteria.op.c_str(), &op);
	if(FAILED(hr))
		return hr;

	spCriteria->SetCriteriaOp(op);
    const auto& values = criteria.values;
	for (int32 i = 0, cnt = values.size(); i < cnt; ++i)
	{
		ks_stdptr<IDbFcValueBase> spVal;
		hr = createDbFilterVal(values.at(i), &spVal);
		if(FAILED(hr))
			return hr;
		
		hr = spCriteria->AddValue(spVal);
		if(FAILED(hr))
			return hr;
	}

	*ppVal = spCriteria.detach();
	return S_OK;
}

HRESULT WebhookDataCreatorProvider::createDbFilterVal(const webhook_data::filter_value value, IDbFcValueBase** ppVal)
{
    std::visit(webhook_data::rflx_type_visitor(ppVal), value);
	return S_OK;
}