#include "db_query_server.h"

#include "dbsheet/et_dbsheet_utils.h"
#include "webetlink.h"
#include "webbase/binvariant/binwriter.h"
#include "webbase/serialize_impl.h"
#include "wo/et_revision_context.h"

extern Callback* gs_callback;

namespace wo
{

bool EnableQueryUserDepartmentInfo()
{
    auto func = []() {
        char* str = ::getenv("DB_ENABLE_QUERY_DEPARTMENT");
		if (str != nullptr && 0 == strncmp(str, "false", 5))
        {
            WOLOG_INFO << "[GetWoEtSettings] DB_ENABLE_QUERY_DEPARTMENT: " << str;
            return false;
        }
        return true;
    };
    static bool b = func();
    return b;
}

void DbUserInfoQuery::QueryUserInfo()
{
    // 每次最多请求200个
    int maxRequestCnt = 200;
    int i = 0;
    int startIdx = 0;
    int queryIdCount = m_spUsersMgr->GetQueryIdCount();
    for (i = 0; i < queryIdCount / maxRequestCnt; ++i)
        SendUserIds(maxRequestCnt);

    if (queryIdCount % maxRequestCnt != 0)
        SendUserIds(queryIdCount % maxRequestCnt);
}

void DbUserInfoQuery::SendUserIds(int count)
{
    binary_wo::BinWriter binWriter;
    m_spUsersMgr->WriteQueryId(binWriter, count);
    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice useridList = {shbt.get(), binWriter.writeLength()};
    QString connID;
    wo::IEtRevisionContext *pCtx = _etcore_GetEtRevisionContext();
    if (pCtx)
        connID = QString::fromUtf16(pCtx->getUser()->connID());
    gs_callback->fetchUserInfo(connID.toUtf8(), m_userId.toUtf8().data(), &useridList);
}


void DbUserInfoQuery::QueryUserCompanyInfo()
{
    class UserIdsEnum : public IDBUserIdsEnum
    {
    public:
        UserIdsEnum(std::vector<ks_wstring>& userIds) : m_userIds(userIds) {}
        STDPROC Do(PCWSTR id) override
        {
            m_userIds.push_back(id);
            return S_OK;
        }
    private:
        std::vector<ks_wstring>& m_userIds;
    };
    std::vector<ks_wstring> queryUserIds;
    UserIdsEnum  enumer(queryUserIds);
    m_spUsersMgr->EnumNeedQueryCompanyInfo(&enumer);
    if (!queryUserIds.empty())
    {
        m_spUsersMgr->ClearNeedQueryCompanyInfo();
        QueryUserCompanyInfo(queryUserIds);
    }
}

void DbUserInfoQuery::QueryUserCompanyInfo(const std::vector<ks_wstring>& userIds)
{
    if (!EnableQueryUserDepartmentInfo())
        return;
    int maxRequestCnt = 100; // 每次最多请求100个
    int queryIdCount = userIds.size();
    int count = 0;
    for (int index = 0; index < userIds.size();)
    {
        if (queryIdCount >= maxRequestCnt)
        {
            queryIdCount = queryIdCount - maxRequestCnt;
            count = maxRequestCnt;
        }
        else
        {
            count = queryIdCount;
        }

        binary_wo::BinWriter binWriter;
        binWriter.addKey("userid");
        binWriter.beginArray();
        for (int j = 0; j < count; ++j)
        {
            binWriter.addString(userIds[index + j].c_str());
        }
        binWriter.endArray();
        index += count;

        binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
        WebSlice useridList = {shbt.get(), binWriter.writeLength()};

        QString connID;
        wo::IEtRevisionContext *pCtx = _etcore_GetEtRevisionContext();
        if (pCtx)
            connID = QString::fromUtf16(pCtx->getUser()->connID());
        if (gs_callback->getUserOrganizeInfo)
            gs_callback->getUserOrganizeInfo(connID.toUtf8(), &useridList);
    }
}

} // namespace wo
