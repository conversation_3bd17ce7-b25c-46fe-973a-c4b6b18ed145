﻿#ifndef __PIVOT_TABLES_HELPER_H__
#define __PIVOT_TABLES_HELPER_H__

#include "etstdafx.h"
#include "pivot_core/pivot_core_x.h"
#include "webbase/binvariant/binvarobj.h"
namespace wo
{

namespace PivotHelpers
{
    bool HasPivotTableInRange(etoldapi::Range *);
    CELL GetFirstResultCell(IKWorksheet* pWorksheet, pivot_core::IPivotTable *pPT, bool *bCrossFrozen = NULL);
    RECT GetFilterButtonRect(IKWorksheet* pWorksheet, pivot_core::IPivotTable *pPT);
    pivot_core::ITblFieldAxis* GetFieldAxisByCell(pivot_core::IPivotTable *pPT, CELL cell, WebStr fieldName = NULL);
    pivot_core::ITblFieldAxis* GetFieldAxisByFieldName(pivot_core::IPivotTable* pPT, WebStr fieldName);
    pivot_core::ITblFieldAxis* GetFieldAxisByFieldId(pivot_core::IPivotTable* pPT, int nFieldId);
    HRESULT GetApiPivotTable(etoldapi::_Worksheet *pWorksheet, pivot_core::IPivotTable *pPivotTable, etoldapi::PivotTable **ppApiPivotTable);
    HRESULT GetPivotTableTool(etoldapi::_Workbook *pWorkbook, IET_PivotTableTool** tool);
    HRESULT GetApiPivotField(pivot_core::ITblFieldAxis* pFieldAxis, etoldapi::PivotTable *pApiPivotTable, etoldapi::PivotField **ppApiPivotField);
    BOOL GetCanSort(pivot_core::ITblFieldAxis* pFieldAxis, pivot_core::IPivotTable *pPivotTable);
    std::vector<RANGE> getPivotTableSectionRanges(ISheet *isheet, pivot_core::IPivotTable *spPvtTable);
    void GetDefaultSourceRange(ETReferenceStyle RefStyle, const ks_stdptr<Range>& ptrSelection, _Worksheet* pWorksheet, OUT BSTR* pbstrRange);
}

enum PTTreeItemType  // Item类型
{
    eRoot,
    eSelectedAall,
    eAddToFilter,
    eSubItem,
};

enum PTTreeItemHideType
{
    eVisible =0,
    eHide = 1,
};

class SlicerCachesBatchHelper
{
public:
	SlicerCachesBatchHelper(pivot_core::IPivotTable* tbl)
		: m_tbl(tbl)
		, m_hr(S_OK)
	{
		m_tbl->BeginSlicerCacheBatch();
	}
	~SlicerCachesBatchHelper()
	{
		m_tbl->EndSlicerCacheBatch(S_OK == m_hr);
	}
	void ProcResult(HRESULT hr)
	{
		m_hr = hr;
	}

protected:
	pivot_core::IPivotTable* m_tbl;
	HRESULT m_hr;
};

class PivotFilterHelperData
{
public:
    virtual HRESULT Init();
protected:
    PivotFilterHelperData(pivot_core::IPivotTable *pPT, pivot_core::ITblFieldAxis *pFA, etoldapi::_Worksheet *pWS);

    ks_stdptr<etoldapi::PivotTable> m_spApiPivotTable;
    ks_stdptr<etoldapi::PivotField> m_spApiPivotField;
    ks_stdptr<etoldapi::PivotFilters> m_spApiPivotFilters;

    ks_stdptr<pivot_core::IPivotTable> m_spPivotTable;
    ks_stdptr<pivot_core::ITblFieldAxis> m_spFieldAxis;

    ks_stdptr<etoldapi::_Worksheet> m_spWorksheet;
};

class ExecPivotFilterHelper : public PivotFilterHelperData
{
public:
    ExecPivotFilterHelper(pivot_core::IPivotTable *pPT, pivot_core::ITblFieldAxis *pFA, etoldapi::_Workbook *pWB, etoldapi::_Worksheet *pWS);

    virtual HRESULT Init() override;

    HRESULT FilterNone();
    HRESULT FilterValues(binary_wo::VarObj);
    HRESULT FilterCustom(binary_wo::VarObj);
    HRESULT FilterTopN(binary_wo::VarObj);

private:
    HRESULT resetFilter(bool bForceClear = false);
    HRESULT getApiPivotDataField(WebStr);
    HRESULT getApiPivotFilters();
    HRESULT _filterValues(const std::set<UINT> &, PivotTblBatchHelper &, SlicerCachesBatchHelper &);
    HRESULT _filterPageValues(const std::set<UINT> &, PivotTblBatchHelper &, SlicerCachesBatchHelper &);
    HRESULT _filterNoneValues(const std::set<UINT>&, PivotTblBatchHelper&, SlicerCachesBatchHelper&);
    oldapi::XlPivotFilterType covertToAPIType(pivot_core::FilterOperatorType type);
    oldapi::XlPivotFilterType covertTopType(pivot_core::FilterOperatorType type, bool isTop);
    pivot_core::FilterOperatorType getFOTFromStr(WebStr filterType, WebStr str);
    pivot_core::FilterOperatorType getTopFOTFromStr(WebStr str, bool &isTop);

    ks_stdptr<etoldapi::PivotField> m_spApiPivotDataField;
    ks_stdptr<etoldapi::_Workbook> m_spWorkbook;
};

class PivotFilterNode
{
public:
    enum CheckType
    {
        CheckType_unInit,
        CheckType_unchecked,
	    CheckType_partiallyChecked,
		CheckType_checked,
    };
    enum NodeType
    {
        eRoot,     
        eNonDate, //非日期节点
        eYear,
        eMonth,
        eDay,
        eHour,
        eMinute,
        eSecond
    };
    PivotFilterNode(NodeType type, int nItemID, PCWSTR bstrData, CheckType checkType, UINT val);
    ~PivotFilterNode();
    CheckType GainCheckState();
    void appendChild(int nItemID, PCWSTR bstrData, bool bVisible);
    void appendDateChild(pivot_core::DATETIME_T dateTime, int itemID, bool bVisible);
    void serialiseValues(ISerialAcceptor *acpt);
    PivotFilterNode* findChild(UINT key);
private:
    void addChild(PivotFilterNode* node, UINT key);
    int getTotal();

private:
    NodeType m_type;
    int    m_nItemID;
    // 节点是否选中
    CheckType   m_checkType = CheckType_unInit;
    // 内容
    ks_wstring m_bstrData;
    // 值
    int     m_val;

    // 维护一个set，可快速找到时间找子类node
    std::unordered_map<UINT, PivotFilterNode*> m_dateMap;
    std::vector<PivotFilterNode*> m_children;
};

inline bool findWithWildChar(PCWSTR matchText, PCWSTR nodeText)
{
    int nMatchLen = 0, nStartPos = 0;
    BOOL bRet = alg::FindWithWildChar(matchText, nodeText, 0, nStartPos, nMatchLen);
    if (bRet && nMatchLen == 0)
        bRet = FALSE;
    return bRet;
}

class SerialisePivotFilterHelper : public PivotFilterHelperData
{
public:
    SerialisePivotFilterHelper(pivot_core::IPivotTable *pPT, pivot_core::ITblFieldAxis *pFA, etoldapi::_Worksheet *pWS, PCWSTR searchData);

    void Serialise(ISerialAcceptor *);
    HRESULT GetOrder(ET_SORT_ORDER& sortOrder);

private:
    bool fieldIsOnlyDateType();
    bool fieldIsOnlyNumberType();

    PCWSTR customTypeToStr(ET_CUSTOM_FILTER_TYPE ty);
    void serialiseValues(ISerialAcceptor *, pivot_core::ITblFieldAxisItems *pItems);

    void GenerateFilterTree(PivotFilterNode& root);
    void MaintenanceCheckState(PivotFilterNode& root);

    PCWSTR m_searchData;
    PCWSTR m_searchType;
    ET_CUSTOM_FILTER_TYPE m_filterType;
};

class SerialisePivotFilterOperatorHelper : public PivotFilterHelperData
{
public:
    SerialisePivotFilterOperatorHelper(pivot_core::IPivotTable *pPT, pivot_core::ITblFieldAxis *pFA, etoldapi::_Worksheet *pWS, BOOL);

    void Serialise(ISerialAcceptor *);
private:
    void serialiseValues(ISerialAcceptor *);
    void serialiseCaption(ISerialAcceptor *, pivot_core::FilterParam);
    void serialiseDate(ISerialAcceptor *, pivot_core::FilterParam);
    void serialiseNumber(ISerialAcceptor *, pivot_core::FilterParam, pivot_core::IDX_ITEM idx);
    void serialiseDynamic(ISerialAcceptor *, pivot_core::FilterParam);
    void serialiseTopN(ISerialAcceptor *, pivot_core::FilterParam, pivot_core::IDX_ITEM idx);
    BOOL GetCanSort();
    static PCWSTR FOT2TypeStr(pivot_core::FilterOperatorType, bool bTop = false);

    BOOL m_b1904;
};

class SetPivotTableTextHelper
{
public:
    SetPivotTableTextHelper(etoldapi::Range *pCellRange, etoldapi::Range *pRange);

    HRESULT SetText(PCWSTR text);
private:
    RECT RANGE2RECT(const RANGE& rg);
    BOOL IsContained(const RECT& rhs, const RECT& lhs);
    void GetRanges(Range* pRange, IKRanges** ppv);
    HRESULT SetPivotTableFmlaText(pivot_core::IPivotTable* pvtTbl, CELL& cell, LPCWSTR pwszText);
    HRESULT CheckPivotTableFormula(pivot_core::IPivotTable* pCorePvtTbl, IN OUT ks_wstring& str, OUT pivot_core::PIVOT_ITEMS& items);
    BOOL GetActiveInfo(IBookOp** ppBookOp, IDX& iSheet, LONG& row, LONG& col);
    HRESULT PutPivotTableFormula(pivot_core::IPivotTable* pCorePvtTbl, ks_wstring& wstr);

    ks_stdptr<etoldapi::Range> m_spCellRange;
    ks_stdptr<etoldapi::Range> m_spRange;
    ks_stdptr<IRangeInfo> m_spRangeInfo;
};

struct PivotConsolidateData
{
    std::vector<std::vector<ks_wstring>> vvData;
    std::vector<std::vector<ks_wstring>> vvPageCache;
    BOOL bAutoPage;
};

struct CreatePivotTableOptions
{
    bool isNewSheet = false;
    bool pivotChart = false;
    bool autoFitColumnWidth = true;
    PCWSTR sourceDataText = nullptr;
    PCWSTR destRangeText = nullptr;
    IDX activeSheetIdx = INVALIDIDX;
    int styleId = -1;
};

class CreatePivotTableHelper
{
public:
    CreatePivotTableHelper(ETReferenceStyle refStyle, etoldapi::_Workbook* ppWorkbook, etoldapi::_Worksheet* pWorkSheet);
    HRESULT createPivotTable(
        const CreatePivotTableOptions& options,
        etoldapi::_Worksheet **pDestWorkSheet, 
        pivot_core::IPivotTable **pCorePivotTable, 
        etoldapi::PivotTable **pPivotTable = nullptr);

    HRESULT updateSourceByRange(pivot_core::IPivotTable* corePivotTable, etoldapi::PivotTable* pivotTable, PCWSTR strSrcRgText);

private:
    HRESULT getPlaceInfo(etoldapi::_Workbook** ppWorkbook, BOOL bRCStyle, BSTR* pbstr);
    HRESULT createPivotCache(etoldapi::_Workbook** ppWorkbook, BSTR* pbstr, etoldapi::PivotCache** ppCache, BSTR* pFailInfo);
    HRESULT createPivotTableInner(etoldapi::_Workbook* pWorkbook,
        BSTR bstrDest,
        etoldapi::PivotCache* pCache,
        etoldapi::PivotTable** ppTable,
        etoldapi::_Worksheet **spNewWorksheet = nullptr);

    HRESULT getPivotCache(etoldapi::_Workbook* pDestBook,
        BSTR bstrSrc,
        ETPivotTableSourceType type,
        XlPivotTableVersionList ver,
        etoldapi::PivotCache** ppCache,
        etoldapi::PivotTable** ppTable,
        BSTR* pFailInfo = nullptr);

    HRESULT getPivotCacheByConsolidate(etoldapi::_Workbook* pWorkbook, etoldapi::PivotCache** ppCache, BSTR* pFailInfo);

    BOOL checkSamePivotCache(etoldapi::_Workbook* pDestBook,
        const_token_ptr token,
        ETPivotTableSourceType type,
        etoldapi::PivotCache* pCache);

private:
    etoldapi::_Workbook* m_pWorkbook = nullptr;
    ks_stdptr<IET_PivotTableTool> m_spPTTool;
    bool m_isNewSheet = false;
    bool m_pivotChart = false;
    bool m_externalData = false;
    bool m_sourceData = true;
    bool m_consolidate = false;
    QString m_sourceDataText;
    QString m_destRangeText;

    bool m_bRCStyle = false;
    bool m_autoFitColumnWidth = true;
    ks_stdptr<etoldapi::_Worksheet> m_spWorksheet;
    ks_stdptr<etoldapi::Worksheets> m_spWorksheets;
    ks_stdptr<IETConnection> m_spConn;
    PivotConsolidateData m_dataConsolidate;
    IDX m_activeSheetIdx = INVALIDIDX;
};

class PivotTableHelper
{
public:
    PivotTableHelper(etoldapi::_Workbook* ppWorkbook, etoldapi::_Worksheet* pWorkSheet, pivot_core::IPivotTable* corePivotTable, etoldapi::PivotTable* pivotTable);

    HRESULT addField(PCWSTR fieldName, PCWSTR fieldOrientation, int newPos, bool autoFieldOrientation = false);
    HRESULT removeField(PCWSTR fieldName, PCWSTR fieldOrientation);
    HRESULT moveFiled(PCWSTR fieldName,
        PCWSTR srcOrientation,
        PCWSTR destOrientation,
        int newPos);
    BOOL checkArrayHasTrueValue(VARIANT varArray);
    HRESULT showPivotSubtotals(etoldapi::_Workbook* pWorkbook, etoldapi::PivotTable* pPivotTable, BOOL bShow);
    VARIANT getSubtotalsSafeArray(BOOL bShow);

    HRESULT getPivotField(PCWSTR fieldName, PCWSTR fieldDir, etoldapi::PivotField** pFld);
    HRESULT setFieldName(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR newName);
    HRESULT setFieldFunction(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR func);
    HRESULT setFieldSubtotals(PCWSTR fieldName, PCWSTR fieldDir, const std::vector<PCWSTR>& subtotals);
    HRESULT setFieldCalculation(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR calculation);
    HRESULT setFieldBaseField(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR baseField);
    HRESULT setFieldBaseItem(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR baseItem);
    HRESULT setFieldRepeatLabels(PCWSTR fieldName, PCWSTR fieldDir, bool repeatLabels);
    HRESULT refresh();
    HRESULT sortByLabel(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR order, bool bManual);
    HRESULT autoSort(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR oreder, PCWSTR field);

private:
    HRESULT getPivotFieldByName(PCWSTR fieldName, oldapi::ETPivotFieldOrientation dir, etoldapi::PivotField** outField);
    INT convertFieldPosition(etoldapi::PivotTable* tbl, INT nPos, oldapi::ETPivotFieldOrientation dir);
    
    HRESULT moveFieldProc(PCWSTR fieldName,
        oldapi::ETPivotFieldOrientation srcOrientation,
        oldapi::ETPivotFieldOrientation destOrientation,
        int newPos);

    HRESULT dataAreaMovetoOthers(IET_PivotTableTool* tool,
        etoldapi::PivotTable* tbl,
        etoldapi::PivotField* pFld,
        oldapi::ETPivotFieldOrientation desDir,
        int nPos);

    etoldapi::_Workbook* m_pWorkbook = nullptr;
    ks_stdptr<etoldapi::_Worksheet> m_spWorksheet;
    ks_stdptr<etoldapi::Worksheets> m_spWorksheets;
    ks_stdptr<etoldapi::PivotTable> m_spPivotTable;
    ks_stdptr<pivot_core::IPivotTable> m_spCorePivotTable;
    ks_stdptr<IET_PivotTableTool> m_spPTTool;
};

} // namespace wo

#endif // __PIVOT_TABLES_HELPER_H__
