#ifndef _LITEFILE_HELPER_H_
#define _LITEFILE_HELPER_H_
#include "etstdafx.h"
#include "wo/core_stake.h"

namespace wo
{
    class KEtWorkbook;
    struct LiteFileUpdateParams
    {
        BookExportLiteVersionMode mode;
        std::vector<RANGE> updateRanges;
        explicit LiteFileUpdateParams();
    };
    class LiteFileParamsHelper
    {
    public:
        LiteFileParamsHelper(wo::KEtWorkbook* wb, binary_wo::VarObj &obj);
        void Do(LiteFileUpdateParams& res);
        
    private:
        void checkSheetNames();
        bool loadRanges(const binary_wo::VarObj &obj, RANGE& rg, const std::function<bool(int, int)> &isValid) const;
        bool IsSingleSheet() const { return 1 == m_localSheetCount; }

    private:
        void adjustRangeForDB(LiteFileUpdateParams &res) const;
        wo::KEtWorkbook* m_pWb = nullptr;
        std::vector<PCWSTR> m_localSheetNames;
        std::vector<IDX> m_passed2Local;
        IBook* m_pBook;
        binary_wo::VarObj& m_obj;
        INT m_localSheetCount;
        bool m_bNeedConvertSheetFromTo;       // 是否需要依据传入的sheetNames变换另存区域的sheetIdx
        
    };
    
}

#endif //_LITEFILE_HELPER_H
