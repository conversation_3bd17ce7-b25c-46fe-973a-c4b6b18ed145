﻿#ifndef __WEBET_SLIM_HELPER_H__
#define __WEBET_SLIM_HELPER_H__

#include "etstdafx.h"

class EtAutoSlimParam;
class EtAutoSlimResult;
namespace wo
{

interface IEtRevisionContext;
class KEtWorkbook;
class ExecDocSlimHelper
{
public:
    ExecDocSlimHelper(IET_DocSlim *pDocSlim) : m_pDocSlim(pDocSlim){};
    HRESULT Slim(IEtRevisionContext*,UINT, WebStr, KEtWorkbook*);
    HRESULT AutoSlim(IEtRevisionContext*, binary_wo::VarObj&, KEtWorkbook* pWb);
    static bool isNeedSlim();
    static void recordSlimTime();
public:
    static constexpr int fileSizeLimit = 30 * 1024 * 1024; // 30 MiB
    static constexpr int autoSlimFrequency = 3; // 每日执行频率
private:
    double estimateImprovement(INT64 nullCells, size_t unusedShapes, size_t unusedCellPictures);
    void fillDeletedRectInfo(const binary_wo::VarObj&, EtAutoSlimParam&);
    void addDeletedRectInfo(binary_wo::VarObj&, const EtAutoSlimParam&);
    void fillAutoSlimParam(const binary_wo::VarObj&, EtAutoSlimParam&);
    void addAutoSlimParam(binary_wo::VarObj&, const EtAutoSlimParam&);
private:
    IET_DocSlim *m_pDocSlim;
    static std::chrono::time_point<std::chrono::high_resolution_clock> s_autoSlimTime;
    static int s_autoSlimCount;
};

class CheckDocSlimHelper
{
public:
    CheckDocSlimHelper(IET_DocSlim *pDocSlim) : m_pDocSlim(pDocSlim) {};
    HRESULT Check(ISerialAcceptor *, WebStr);
private:
    IET_DocSlim *m_pDocSlim;
};

} // wo

#endif // __WEBET_SLIM_HELPER_H__