﻿#include "etstdafx.h"
#include "custom_storage_helper.h"

namespace wo
{
namespace CustomStorageHelper
{
    HRESULT CopyCustomStorage(IBook* pSrcBook, IBook* pTarBook, std::unordered_map<UINT, UINT>* pSheetIdMap)
    {
        class KCustomStorageEnum : public ICustomStorageEnum
        {
        public:
            KCustomStorageEnum(ICustomStorageManager* pCustomStorage, std::unordered_map<UINT, UINT>* pSheetIdMap)
                : m_pSheetIdMap(pSheetIdMap), m_pCustomStorage(pCustomStorage) {}

            STDPROC Do(IDX sheetId, PC<PERSON>TR storInfoMapKey, PCWSTR group, PCWSTR value, BOOL isFallback2Book) override
            {
                auto iter = m_pSheetIdMap->find(sheetId);
                if (iter == m_pSheetIdMap->end() || xstrcmp(group, __X("dbDashboardRule")) != 0)
                    return S_OK;

                m_pCustomStorage->UpdateStorage(iter->second, storInfoMapKey, group, value, isFallback2Book);
                return S_OK;
            }
        private:
            const std::unordered_map<UINT, UINT>* m_pSheetIdMap;
            ICustomStorageManager* m_pCustomStorage = nullptr;
        };

        if (pSheetIdMap->empty())
            return S_OK;

        ks_stdptr<ICustomStorageManager> spCustomStorMgr;
        pSrcBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
        if (!spCustomStorMgr)
            return E_FAIL;
        ks_stdptr<ICustomStorageManager> spTarCustomStorMgr;
        pTarBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spTarCustomStorMgr);
        if (!spTarCustomStorMgr)
            return E_FAIL;

        KCustomStorageEnum customStorage(spTarCustomStorMgr, pSheetIdMap);
        return spCustomStorMgr->EnumCustomStorage(&customStorage);
    }

}  // namespace ColOperatorHelper
}  // namespace wo
