#ifndef __WO_MESSAGE_PRODUCER_H__
#define __WO_MESSAGE_PRODUCER_H__

#include <QMap>
#include <public_header/webcommon/src/chart/wo_transport/chart_msg_proxy.h>

using namespace chart::wo_transport;

namespace wo {

class SeriesRangeSubscription;

//远程数据传输通信逻辑
class ChartMessgeProducer: public IChartBinaryProducer
{
public:
    ChartMessgeProducer();
    virtual ~ChartMessgeProducer() override;

public:
    void AttachSubscriber(SeriesRangeSubscription* pSubsriber);

    void BeginMarkDirty(const int index, int diryMask);
    void EndMarkDirty();

    int GainRangeMask(const int index);
    void ResetMask();

public:
    void UpdateUUID(const QString& uid);
public:
    virtual BinaryMessage create() override;

protected:
    virtual BinaryMessage createInfo() override;
    virtual BinaryMessage createSeries() override;
    virtual BinaryMessage createContext() override;
    virtual BinaryMessage createError() override;
    virtual BinaryMessage createData() override;

private:
    QString m_UUID;
    QMap<int, int> m_dirtyMap;

    SeriesRangeSubscription* m_pSubsriber;
};

} //weboffice

#endif
