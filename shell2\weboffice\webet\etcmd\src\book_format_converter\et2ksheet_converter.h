﻿#ifndef __WEBET_ET2KSHEET_CONVERTER_H__
#define __WEBET_ET2KSHEET_CONVERTER_H__
#include "webbase/binvariant/binvarobj.h"

namespace wo
{
	class Et2KsBookConverter
	{
    private:
        _Workbook* m_pEtWb = nullptr;
        IKWorksheets* m_pEtSheets = nullptr;
        PCWSTR m_invalidImgUrl = nullptr;
        ks_stdptr<IKBlipAtom> m_spBrokenImg;
		bool m_masterProcess;
		binary_wo::VarObj m_passwordKeys;
    private:
		HRESULT _clearProtection() const;
		HRESULT _handleImgInShapeTree(drawing::AbstractShape*) const;
		HRESULT _imgHandler() const;
		HRESULT _processDbSheetInEt() const;
	public:
		explicit Et2KsBookConverter(_Workbook*, binary_wo::VarObj passwordKeys = binary_wo::VarObj(), bool masterProcess = false);
		HRESULT Init();
		HRESULT Exec();
	};
} // namespace wo

#endif
