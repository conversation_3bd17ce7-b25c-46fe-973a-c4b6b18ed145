﻿#ifndef __WEBET_ET_HTTP_TASK_CLASS_H__
#define __WEBET_ET_HTTP_TASK_CLASS_H__

#include "et_task_class.h"
#include <api/jsapi/ipc/kjsipc.h>
namespace etda
{
    struct MergeInfo;
}
namespace binary_wo
{
    class BinWriter;
}
class IKWorksheet;

namespace wo 
{

class IBatchTask;
class EtHttpTaskClassBase : public EtTaskExecBase
{
public:
    using EtTaskExecBase::EtTaskExecBase;
	virtual HRESULT PreExecute(KwCommand*, KEtRevisionContext*) override;
    virtual HRESULT PostExecute(HRESULT, KwCommand*, KEtRevisionContext*) override;
    HRESULT CheckDBSheet(IDX sheetIdx);
protected:
    ks_wstring m_errMsg;
};

class EtHttpUpdateRangeDataTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpUpdateRangeDataTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
    {
        return S_OK; //　内部限制
    }
    PCWSTR GetTag() override;

protected:
    HRESULT handleFormula(const RANGE & rg,etoldapi::Range* rangeApi,binary_wo::VarObj& item,KEtRevisionContext* ctx);
    HRESULT handlePicture(const RANGE & rg,binary_wo::VarObj& item,KwCommand*cmd, KEtRevisionContext* pCtx);
private:
    void exportShapeImage(KEtWorkbook* wwb, KEtRevisionContext* ctx, binary_wo::VarObj& param, drawing::AbstractShape* pShape);
};

class EtHttpAddRowTaskClass : public EtHttpUpdateRangeDataTaskClass
{
public:
    EtHttpAddRowTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
    {
        return S_OK; //　内部限制
    }
    PCWSTR GetTag() override;
};


class EtHttpSetRangeWidthHeightTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpSetRangeWidthHeightTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
    {
        return S_OK; //命令内部处理
    }
    PCWSTR GetTag() override;
};


class EtHttpPivotTableRefreshTaskClass: public EtHttpTaskClassBase
{
public:
    EtHttpPivotTableRefreshTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
    {
        return S_OK; //命令内部处理
    }
    PCWSTR GetTag() override;
};

class EtHttpSheetAddHyperlinkTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpSheetAddHyperlinkTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
    {
        return S_OK; //命令内部处理
    }
    PCWSTR GetTag() override;
};

class EtHttpSheetSetNameTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpSheetSetNameTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override
    {
        return CheckBookHasHiddenPropRange(ctx);
    }
    PCWSTR GetTag() override;
};

class EtHttpSheetsAddTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpSheetsAddTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override;
    PCWSTR GetTag() override;
};

class EtHttpSheetsCopyTaskClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpSheetsCopyTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override;
    PCWSTR GetTag() override;
};

class EtHttpCopySheetsFromBookTaskClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpCopySheetsFromBookTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
    {
        return CheckBookHasHiddenPropRange(ctx);
    }
    PCWSTR GetTag() override;
};

class EtHttpDeleteSheetsTaskClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpDeleteSheetsTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override;
    PCWSTR GetTag() override;
};

class EtHttpSetQRColTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpSetQRColTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
private:
    void AdjustQRCol(Database::GenQRLabelCol& qrCol, COL originalCol);
};

class EtHttpFillQRColTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpFillQRColTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override
    {
        return S_OK;
    }
    bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
    {
        return false;
    }
    PCWSTR GetTag() override;
};

class EtHttpClearColTypeTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpClearColTypeTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
};

class EtHttpRecalculateRangeDataTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpRecalculateRangeDataTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
};

class EtHttpUpdateWorkbenchSheetTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpUpdateWorkbenchSheetTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
};

// 删除range
class EtHttpDeleteRangeTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpDeleteRangeTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override
    {
        return S_OK;// 在执行中判断
    }
    PCWSTR GetTag() override;
};

class EtHttpV8JsEvaluateTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpV8JsEvaluateTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
};

class EtHttpInsertNumberColTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpInsertNumberColTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
    {
        return S_OK; //命令内部处理
    }
    PCWSTR GetTag() override;
};

class EtHttpSetCustomStorageTaskClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpSetCustomStorageTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;

protected:
    HRESULT HandleBookInfo(const binary_wo::VarObj& param, ks_stdptr<ICustomStorageManager>& spCustomStorMgr);
    HRESULT HandleSheetInfo(const binary_wo::VarObj& param, ks_stdptr<ICustomStorageManager>& spCustomStorMgr, 
                            KEtRevisionContext*& ctx);
};

// 执行API
class HttpExecuteApiBatchTask;
class EtHttpExecuteApiTaskClass : public EtHttpTaskClassBase
{
public:
    EtHttpExecuteApiTaskClass(KEtWorkbook* wb);
    ~EtHttpExecuteApiTaskClass();
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*ctx) override
    {
        return S_OK;// 在执行中判断
    }
    PCWSTR GetTag() override;
    IBatchTask * GetBatchTask() override;
    
private:
    std::unique_ptr<HttpExecuteApiBatchTask> m_spBatchTask;
};

class EtHttpPlaybackApiTaskClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpPlaybackApiTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
protected:
    void SwitchUser(PCWSTR connId, PCWSTR userId, PCWSTR sessionId, PCWSTR permissionId, KEtRevisionContext*);
    void ExecuteSingleApi(char* pData, KEtRevisionContext* ctx);
    bool TransformHeader(JsApiIpcRpcCall& rpcCall);
    bool TransformArgument(int nCount, JsApiIpcArgument* pArgs);
    bool TransformValueList(KJSVariantList& args);
    void GainObjMap(const JsApiIpcRpcCall& rpcCall, KJSVariantList& oldRes, KJSVariantList& retList);
private:
    std::unordered_map<JSFunction, JSFunction> m_funcMap;
    std::unordered_map<ApiObjectInterface, ks_stdptr<IDispatch>> m_objMap;
    int m_taskCnt = 0;
};

class EtHttpFullUpdateSourceSheetTaskClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpFullUpdateSourceSheetTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
private:
    HRESULT writeEtSyncData(IN KEtRevisionContext* ctx, IN ISerialAcceptor* acpt, IN IDX sheetIdx, INT iDefaultColRowCount);
    HRESULT recognizeTableStruct(ISheet* pSheet, RANGE& rg, INT iDefaultColRowCount);
    bool checkNeedRecognizeTitle(ISheet* pSheet, IEtIdContainer* pEtIdContainer, const RECT& rc);
};

class EtHttpImportWorkBookClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpImportWorkBookClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
private:
    HRESULT CheckRemoveDuplicates(const binary_wo::VarObj& param, etoldapi::_Worksheet* pWorksheet);
    HRESULT CheckNewFileEmptySheet(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISheet* pSheet);
    HRESULT DealWithImportInfo(binary_wo::VarObj& param, ISheet* pSheet, bool bSuccess);
};

class EtHttpDeleteDataAnalyzeMergeTaskClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpDeleteDataAnalyzeMergeTaskClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
};

class EtHttpAddDataAnalyzeLogClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpAddDataAnalyzeLogClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
};

class EtHttpUpdateDataAnalyzeDataSourceNameClass : public EtHttpTaskClassBase
{
public:
    explicit EtHttpUpdateDataAnalyzeDataSourceNameClass(KEtWorkbook* wb);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
    PCWSTR GetTag() override;
};
} // wo

#endif // __WEBET_ET_HTTP_TASK_CLASS_H__
