#ifndef __WO_FORMULA_IDX_GENERATOR_H__
#define __WO_FORMULA_IDX_GENERATOR_H__

#include "etstdafx.h"

namespace wo
{

class TokenVectorPersistHasher
{
public:
	size_t operator()(ITokenVectorPersist* fmla) const;
};

class TokenVectorPersistEuqer
{
public:
	bool operator()(ITokenVectorPersist* fmlaA, ITokenVectorPersist* fmlaB) const;
};

class FormulaIdxGenerator
{
public:
	size_t gainIdx(ITokenVectorPersist* fmla);
	size_t size();
	ITokenVectorPersist* at(size_t idx);

private:


	std::vector<ITokenVectorPersist*> m_vecFmla;
	std::unordered_map<ITokenVectorPersist*, size_t, TokenVectorPersistHasher, TokenVectorPersistEuqer> m_mapFmla;
};

} //namespace wo

#endif // __LIST_AUTOFILTER_HELPER_H__