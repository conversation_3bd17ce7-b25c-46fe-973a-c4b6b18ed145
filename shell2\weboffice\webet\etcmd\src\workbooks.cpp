#include "etstdafx.h"
#include "workbooks.h"
#include "et_task_executor.h"
#include "revision_ext.h"
#include "op_transform/et_op_tf.h"
#include "et_revision_context_impl.h"
#include "sys/stat.h"
#include <public_header/drawing/wo/serialdrawing.h>

#include "webbase/context_init_helper.h"
#include <public_header/webcommon/src/wodocmemostat.h>
#include "db_query_server.h"
#include "et/applogic/include/global.h"
#include "et/applogic/include/kapplication.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/jsapi_context.h"
#include <applogic/etapi_old_param.h>
#include "etcore/et_core_event_tracking.h"
#include "etcore/et_core_timestat.h"

namespace wo
{

KEtWorkbooks::KEtWorkbooks(et::KConApplication* consoleApp, WebLogFunc logFunc)
	: m_consoleApp(consoleApp), m_logFunc(logFunc), m_pStaleNotify(nullptr)
{
}

KEtWorkbooks::~KEtWorkbooks()
{
}

int KEtWorkbooks::Close(bool SaveChanges)
{
	m_Workbook.reset();
	VARIANT var;
	V_VT(&var) = VT_BOOL;
	V_BOOL(&var) = SaveChanges;
	if (!m_ptrWorkbooks)
		return E_FAIL;

	HRESULT hr = m_ptrWorkbooks->Close(var);
	m_ptrWorkbooks.clear();
	m_ptrWorkbook.clear();
    wo::KDrawingSerializer::clearFontTableForce();
    wo::KJsApiContextManager::Instance()->CloseAll();
	return hr;
}

void KEtWorkbooks::ConnectCloudVer(int32 cloudVer, int32 taskVer)
{
	if (m_Workbook.get() == nullptr) return;

	m_Workbook->getVersionMgr()->connectCloudVer(cloudVer, taskVer);
}

void KEtWorkbooks::New()
{
	ASSERT(m_ptrWorkbooks.get() == nullptr);
	m_ptrApp->get_Workbooks(&m_ptrWorkbooks);
	m_ptrWorkbooks->Add(KComVariant(), &m_ptrWorkbook);

	m_Workbook.reset(new KEtWorkbook(m_ptrWorkbook, m_consoleApp, m_logFunc,""));
}

void KEtWorkbooks::SetApp(_Application *app)
{
	m_ptrApp = app;
}

int KEtWorkbooks::Open(
	QString filePath, QString fileName,
	bool ReadOnly, QString passwd, QString modifyPasswd,
	QString fileId, int32 cloudVer, QString userID, bool bWoAutoslim)
{
	ks_bstr SingleFileName(krt::utf16(filePath));
	m_ptrWorkbooks.clear();
	m_ptrApp->get_Workbooks(&m_ptrWorkbooks);
	ks_bstr pas(krt::utf16(passwd));
	ks_bstr modifyPas(krt::utf16(modifyPasswd));
	VARIANT varfn = {0};
	VARIANT vpas = {0};
	VARIANT vModifyPas = { 0 };

	struct stat xstat = {0};
	VERIFY(::stat(filePath.toLocal8Bit().data(), &xstat) == 0);

	V_VT(&varfn) = VT_BSTR;
	V_VT(&vpas) = VT_BSTR;
	V_VT(&vModifyPas) = VT_BSTR;
	V_BSTR(&varfn) = SingleFileName;
	V_BSTR(&vpas) = pas;
	V_BSTR(&vModifyPas) = modifyPas;
	VARIANT varAddToMru;
	V_VT(&varAddToMru) = VT_BOOL;
	V_BOOL(&varAddToMru) = VARIANT_TRUE;
	VARIANT var;
	V_VT(&var) = VT_BOOL;
	V_BOOL(&var) = ReadOnly;
	
	VARIANT varUpdateLinks = {0};
	V_VT(&varUpdateLinks) = VT_BOOL;
	V_BOOL(&varUpdateLinks) = VARIANT_FALSE;

	HRESULT hr = E_FAIL;
	m_ptrWorkbook.clear();

	QTime openTimer; openTimer.start();
	EtWorkbookOpenParam paramDefault;
	paramDefault.bWoAutoslim = bWoAutoslim;
	hr = m_ptrWorkbooks->Open(varfn, varUpdateLinks,
							var,	VARIANT(),
							vpas,	vModifyPas,
							VARIANT(),	VARIANT(),
							VARIANT(),	VARIANT(),
							VARIANT(),	VARIANT(),
							varAddToMru,  &m_ptrWorkbook, &paramDefault, FALSE);
	if (hr != S_OK || m_ptrWorkbook == NULL){
		if (SUCCEEDED(hr)) hr = E_FAIL;
		fprintf(stderr, "open file error: %s\n", fileName.toLocal8Bit().data());
	} else {
		m_Workbook.reset(new KEtWorkbook(m_ptrWorkbook, m_consoleApp, m_logFunc, fileId));
		m_Workbook->GetMemoStat()->UpdateAfterOpen(openTimer.elapsed());
		m_Workbook->getVersionMgr()->addInitCloudVer(cloudVer);
		if (!fileName.isEmpty())
		{
			m_Workbook->SetValidateName(krt::utf16(fileName), fileName.length());
		}

		IBook* pBook = m_Workbook->GetCoreWorkbook()->GetBook();
		ks_wstring fileId(m_Workbook->getFileId());
		pBook->SetFileId(fileId.c_str(), fileId.length());

			IWorkspace* pWs = pBook->LeakWorkspace();
			IEtEventTracking* pEventTracking = pWs->GetEventTracking();
			IEtCollectInfo* pCollectInfo = pEventTracking->GetCollectInfo();
			etcore::TimeStat timeStat([&](unsigned int time) {
				KComVariant varVal(time);
				pCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::DB_QUERY_USER_TIME_CONSUMING, varVal);
			});

		if (m_Workbook->GetBMP()->bDbSheet || m_Workbook->GetBMP()->bKsheet)
		{
			ks_stdptr<IDbUsersManager> spUsersMgr;
			pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);
			DbUserInfoQuery userInfo(spUsersMgr, userID);
			// 主进程打开文件时, 需向服务端请求联系人信息(历史样张可能没有联系人管理器, 将联系人信息全部存储于单元格)
			// 非主进程的可编辑文件, 打开环节不做此操作(例如导入样张的场景)
			userInfo.QueryUserInfo();
		}
		m_Workbook->setStaleNotify(m_pStaleNotify);
	}
	return hr;
}

KEtWorkbook* KEtWorkbooks::GetWorkbook()
{
	return m_Workbook.get();
}

void KEtWorkbooks::notifyStale(const WebID objId)
{
	if (m_Workbook.get()) {
		m_Workbook->notifyStale(objId);
	}
}

void KEtWorkbooks::setStaleNotify(IStaleNotify* pNotify)
{
	m_pStaleNotify = pNotify;
}
}
