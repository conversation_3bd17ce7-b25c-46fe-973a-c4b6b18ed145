#ifndef __WEBET_DATABASE_FIELD_CONTEXT_H__
#define __WEBET_DATABASE_FIELD_CONTEXT_H__

#include "webbase/binvariant/binvarobj.h"
namespace wo
{
using binary_wo::VarObj;
class KEtWorkbook;
interface IEtRevisionContext;
namespace Database
{

class FieldContext
{
public:
    FieldContext(KEtWorkbook *wwb, IEtRevisionContext *pRevisionContext);
    FieldContext(KEtWorkbook *wwb, IEtRevisionContext *pRevisionContext, VarObj extObj);
    FieldContext(etoldapi::_Workbook* pApiWb, IEtRevisionContext *pRevisionContext);

    ks_stdptr<etoldapi::Range> CreateRangeObj(const RANGE& rg);
    IEtRevisionContext *GetRevisionContext();
    BMP_PTR GetBMP();
    IBook *GetBook();
    IKWorksheets *GetWorksheets();
    IET_NumberFormatter *GetNumberFormatter();
    VarObj GetExtObj();
    VarObj GetOutExtObj() const;
    SetDVReturnCode GetDVCode();
    void SetDVCode(SetDVReturnCode code);
    bool IsEnumAny();
    void SetEnumAny();
    void SetClear(bool bClear);
    bool IsClear();
private:
    KEtWorkbook *m_wwb;
    IEtRevisionContext *m_pRevisionContext;
    etoldapi::_Workbook* m_pApiWb;
    IBook *m_pBook;
    VarObj m_extObj;
    binary_wo::VarObjRoot m_outExtObj;
    bool m_bEnumAny;
    bool m_bClear;
    SetDVReturnCode m_dvReturnCode;
    static ks_stdptr<IET_NumberFormatter> m_spNumberFormatter;
};

} // Database
} // wo

#endif // __WEBET_DATABASE_FIELD_CONTEXT_H__