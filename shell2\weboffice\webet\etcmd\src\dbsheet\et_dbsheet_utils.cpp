﻿#include "etstdafx.h"
#include "et_dbsheet_utils.h"
#include "et_dbsheet_filter_helper.h"
#include "helpers/varobject_helper.h"
#include "database/database_utils.h"
#include "webetlink.h"
#include "ettools/ettools_encode_decoder.h"
#include "etcore/et_core_dbsheet_autolink.h"
#include "wo/et_revision_context.h"
#include "src/et_revision_context_impl.h"
#include "hresult_to_string.h"
#include "webbase/wo_sa_helper.h"
#include "webbase/binvariant/binreader.h"
#include "et_task_class.h"
#include "dbsheet/et_dbsheet_copysheet_helper.h"
#include "helpers/statsheet_helper.h"
#include "kfc/et_numfmt_str.h"
#include "webhook/webhook_helper.h"
#include "common_log/common_log_helper.h"
#include "db/db_basic_itf.h"

extern Callback* gs_callback;
namespace wo
{
namespace DbSheet
{

static inline bool getField_bool(const VarObj& obj, WebName name, bool def = true)
{
    return obj.has(name) ? obj.field_bool(name) : def;
}

// 是否需要获取列值作为选项
bool needGenerateSelectItemsFromContents(IDbField *pOldField, IDbField *pNewField)
{
    ET_DbSheet_FieldType oldType = pOldField->GetType();
    ET_DbSheet_FieldType newType = pNewField->GetType();
    if (newType != Et_DbSheetField_SingleSelect && newType != Et_DbSheetField_MultipleSelect)
        return false;
    switch (oldType)
    {
        case Et_DbSheetField_Url:
        {
            ks_stdptr<IDbField_Hyperlink> spOldFieldHyperlink = pOldField;
            return spOldFieldHyperlink->GetHyperlinkMode() == DbSheet_Hyperlink_Url_Mode;
        }
        case Et_DbSheetField_SingleLineText: // 兼容单行文本，后期可以考虑去除
        case Et_DbSheetField_Contact:
        case Et_DbSheetField_MultiLineText:
        case Et_DbSheetField_Rating:
        case Et_DbSheetField_BarCode:
            return true;
        default:
            return false;
    }
    return false;
}

static
HRESULT ConfigureAutomations(IDbField_Automations* pField, IBook* pBook, const VarObj& args)
{
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    VAR_OBJ_EXPECT_ARRAY(args, "automations")
    VarObj vAutomations = args.get_s("automations");

    std::vector<EtDbId> idVec;
    for (int i = 0; i < vAutomations.arrayLength_s(); i++)
    {
        VarObj vItem = vAutomations.at_s(i);
        EtDbId id = INV_EtDbId;
        if (vItem.has("id"))
            VS(pCtx->DecodeEtDbId(vItem.field_str("id"), &id));

        idVec.push_back(id);
    }

    HRESULT hr = pField->BatchEditAutomations(&idVec[0], idVec.size());
    if (FAILED(hr))
        return hr;

    for (int i = 0; i < vAutomations.arrayLength_s(); i++)
    {
        VarObj vItem = vAutomations.at_s(i);
        ks_stdptr<IDbAutomation> spAutomation;
        hr = pField->GetAutomation(i, &spAutomation);
        if (FAILED(hr))
            return hr;

        {
            VAR_OBJ_EXPECT_STRUCT(vItem, "trigger")
            VarObj vTrigger = vItem.get_s("trigger");

            VAR_OBJ_EXPECT_STRING(vTrigger, "type")
            DbAutomationTriggerType type = AutomationTrigger_UpdateRecord;
            VS(_appcore_GainEncodeDecoder()->DecodeDbAutomationTriggerType(vTrigger.field_str("type"), &type));
            ks_stdptr<IDbAutomationTrigger> spTrigger;
            hr = spAutomation->SetTrigger(type, &spTrigger);
            if (FAILED(hr))
                return hr;
            switch (type)
            {
                case AutomationTrigger_UpdateRecord:
                {
                    ks_stdptr<IDbAutomationTrigger_UpdateRecord> spTrigger_UpdateRecord = spTrigger;
                    VAR_OBJ_EXPECT_BOOL(vTrigger, "watchAll")
                    bool bWatchAll = vTrigger.field_bool("watchAll");
                    if (bWatchAll)
                    {
                        hr = spTrigger_UpdateRecord->WatchAll();
                        if (FAILED(hr))
                            return hr;
                    }
                    else
                    {
                        VAR_OBJ_EXPECT_ARRAY(vTrigger, "watchFields")
                        VarObj vWatchFields = vTrigger.get_s("watchFields");
                        std::vector<EtDbId> idVec;
                        for (int i = 0; i < vWatchFields.arrayLength_s(); i++)
                        {
                            EtDbId id = INV_EtDbId;
                            pCtx->DecodeEtDbId(vWatchFields.item_str(i), &id);
                            idVec.push_back(id);
                        }
                        hr = spTrigger_UpdateRecord->Watch(&idVec[0], idVec.size());
                        if (FAILED(hr))
                            return hr;
                    }
                    VAR_OBJ_EXPECT_STRING(vTrigger, "op")
                    KDbFilterOpType op = DBFOT_And;
                    VS(_appcore_GainEncodeDecoder()->DecodeKDbFilterOpType(vTrigger.field_str("op"), &op));
                    hr = spTrigger_UpdateRecord->SetOperator(op);
                    if (FAILED(hr))
                        return hr;
                    hr = spTrigger_UpdateRecord->ClearCondition();
                    if (FAILED(hr))
                        return hr;
                    VAR_OBJ_EXPECT_ARRAY(vTrigger, "conditions")
                    VarObj vConditions = vTrigger.get_s("conditions");
                    for (int i = 0; i < vConditions.arrayLength_s(); i++)
                    {
                        VarObj vCondition = vConditions.at_s(i);
                        EtDbId fieldId = INV_EtDbId;
                        VAR_OBJ_EXPECT_STRING(vCondition, "fieldId")
                        VS(pCtx->DecodeEtDbId(vCondition.field_str("fieldId"), &fieldId));
                        ks_stdptr<IDbFieldFilter> spCondition;
                        hr = spTrigger_UpdateRecord->AddCondition(fieldId, &spCondition);
                        if (FAILED(hr))
                            return hr;
                        VAR_OBJ_EXPECT_STRUCT(vCondition, "criteria")
                        ks_stdptr<IDbFilterCriteria> spCriteria;
                        hr = CreateDbFilterCriteria(vCondition.get_s("criteria"), pBook, &spCriteria);
                        if (FAILED(hr))
                            return hr;
                        hr = spCondition->SetCriteria(spCriteria);
                        if (FAILED(hr))
                            return hr;
                    }
                    break;
                }
                case AutomationTrigger_DueDate:
                {
                    ks_stdptr<IDbAutomationTrigger_DueDate> spTrigger_DueDate = spTrigger;
                    EtDbId dateField = INV_EtDbId;
                    VAR_OBJ_EXPECT_STRING(vTrigger, "dateField")
                    VS(pCtx->DecodeEtDbId(vTrigger.field_str("dateField"), &dateField));
                    hr = spTrigger_DueDate->SetDateField(dateField);
                    if (FAILED(hr))
                        return hr;

                    VAR_OBJ_EXPECT_NUMERIC(vTrigger, "executeTime");
                    hr = spTrigger_DueDate->SetExecuteTime(vTrigger.field_uint32("executeTime"));
                    if (FAILED(hr))
                        return hr;

                    if (vTrigger.has("executeTimeType"))
                    {
                        DbAutomationExecuteTimeType executeTimeType = AutomationExecuteTime_Unified;
                        VAR_OBJ_EXPECT_STRING(vTrigger, "executeTimeType");
                        VS(_appcore_GainEncodeDecoder()->DecodeDbAutomationExecuteTimeType(
                                vTrigger.field_str("executeTimeType"), &executeTimeType));
                        hr = spTrigger_DueDate->SetExecuteTimeType(executeTimeType);
                        if (FAILED(hr))
                            return hr;
                    }
                    break;
                }
                default:
                {
                    ASSERT(FALSE);
                    return E_INVALID_REQUEST;
                }
            }
        }

        {
            VAR_OBJ_EXPECT_ARRAY(vItem, "actions")
            VarObj vActions = vItem.get_s("actions");

            std::vector<EtDbId> actionIdVec;
            std::vector<DbAutomationActionType> actionTypeVec;
            for (int i = 0; i < vActions.arrayLength_s(); i++)
            {
                VarObj vAction = vActions.at_s(i);
                EtDbId id = INV_EtDbId;
                if (vAction.has("id"))
                    VS(pCtx->DecodeEtDbId(vAction.field_str("id"), &id));
                VAR_OBJ_EXPECT_STRING(vAction, "type")
                DbAutomationActionType type = AutomationAction_SimpleMessage;
                VS(_appcore_GainEncodeDecoder()->DecodeDbAutomationActionType(vAction.field_str("type"), &type));

                actionIdVec.push_back(id);
                actionTypeVec.push_back(type);
            }

            HRESULT hr = spAutomation->BatchEditActions(&actionIdVec[0], &actionTypeVec[0], actionIdVec.size());
            if (FAILED(hr))
                return hr;

            for (int i = 0; i < vActions.arrayLength_s(); i++)
            {
                VarObj vAction = vActions.at_s(i);
                VAR_OBJ_EXPECT_STRING(vAction, "type")
                DbAutomationActionType type = AutomationAction_SimpleMessage;
                VS(_appcore_GainEncodeDecoder()->DecodeDbAutomationActionType(vAction.field_str("type"), &type));

                ks_stdptr<IDbAutomationAction> spAction;
                hr = spAutomation->GetAction(i, &spAction);
                if (FAILED(hr))
                    return hr;

                switch (type)
                {
                    case AutomationAction_SimpleMessage:
                    {
                        ks_stdptr<IDbAutomationAction_SimpleMessage> spAction_SimpleMessage = spAction;
                        VAR_OBJ_EXPECT_STRING(vAction, "messageTemplateId")
                        hr = spAction_SimpleMessage->SetMessageTemplateId(vAction.field_str("messageTemplateId"));
                        if (FAILED(hr))
                            return hr;
                        EtDbId id = INV_EtDbId;
                        VAR_OBJ_EXPECT_STRING(vAction, "actionField")
                        pCtx->DecodeEtDbId(vAction.field_str("actionField"), &id);
                        hr = spAction_SimpleMessage->SetActionField(id);
                        if (FAILED(hr))
                            return hr;

                        id = INV_EtDbId;
                        VAR_OBJ_EXPECT_STRING(vAction, "notifyField")
                        pCtx->DecodeEtDbId(vAction.field_str("notifyField"), &id);
                        hr = spAction_SimpleMessage->SetNotifyField(id);
                        if (FAILED(hr))
                            return hr;

                        ks_stdptr<IDbAutomationTrigger> spTrigger;
                        hr = spAutomation->GetTrigger(&spTrigger);
                        if (FAILED(hr))
                            return hr;
                        break;
                    }
                    default:
                    {
                        ASSERT(FALSE);
                        return E_INVALID_REQUEST;
                    }
                }
            }
        }
    }
    return S_OK;
}

HRESULT ConfigureDbContactField(IDbField* pField, const VarObj& args, bool bNewField, const char* multipleContacts, const char* noticeContact)
{
    HRESULT hr = S_OK;
    ks_stdptr<IDbField_Contact> spField = pField;

    if (bNewField)
        VAR_OBJ_EXPECT_BOOL(args, multipleContacts);
    if (args.has(multipleContacts))
    {
        hr = spField->SetSupportMulti(args.field_bool(multipleContacts));
        if (FAILED(hr))
            return hr;
    }

    if (bNewField)
        VAR_OBJ_EXPECT_BOOL(args, noticeContact);
    if (args.has(noticeContact))
    {
        hr = spField->SetSupportNotice(args.field_bool(noticeContact));
        if (FAILED(hr))
            return hr;
    }

    return hr;
}

HRESULT ConfigureDbContactExtendField(IDbField* pField, const VarObj& args, IDBSheetRange** ppIstRg)
{
    HRESULT hr = S_OK;
    ks_stdptr<IDbField_Contact> spContactField = pField;
    ET_DbSheet_FieldType  type = pField->GetType();
    bool isCreator = false;
    IBook* pBook = pField->GetDbSheetData()->GetBook();
    PCWSTR creator = pBook->GetWoStake()->GetCreatorUserID();
    IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
    if (xstrcmp(creator, pCtx->getUser()->userID()) == 0) 
        isCreator = true;
    bool createDepartment = false;
    bool createLeader = false;
    bool createEmail = false;
    bool createEmployeeId = false;
    if (args.has("extendFieldInfo"))
    {
        //创建者才能配置扩展字段
        if (!isCreator)
            return E_DBSHEET_AD_FAIL;

        VarObj extendFieldInfo = args.get_s("extendFieldInfo");
        if (extendFieldInfo.has("department")) createDepartment = true;
        if (extendFieldInfo.has("leader")) createLeader = true;
        if (extendFieldInfo.has("email")) createEmail = true;
        if (extendFieldInfo.has("employeeId")) createEmployeeId = true;
    }
    else
    {
        if (!isCreator)
        {
            if (type == Et_DbSheetField_Contact && spContactField->GetSupportMulti() && spContactField->ExistExtendField())
                return E_DBSHEET_UNSUPPORTED_MUTIPLE;
            return S_OK;
        }
    }

    IDBSheetOp* pDBSheetOp = pField->GetDbSheetData();
    if (!pDBSheetOp)
        return E_FAIL;
    IDbFieldsManager* pFieldsMgr = pDBSheetOp->GetFieldsManager();
    std::vector<std::pair<ET_DbSheet_FieldType, VarObj>> extendFieldVec;
    EtDbId extendField = spContactField->GetDepartmentField();
    if (createDepartment)
    {   
        if (extendField == INV_EtDbId)
        {
            extendFieldVec.push_back({Et_DbSheetField_Department, args.get_s("extendFieldInfo").get_s("department")});
        }
    }
    else 
    {
        if (extendField != INV_EtDbId)
        {
            ClearDbContactExtendField(pFieldsMgr, extendField);
        }
    }

    extendField = spContactField->GetLeaderField();
    if (createLeader)
    {
        if (extendField == INV_EtDbId)
        {
            extendFieldVec.push_back({Et_DbSheetField_Contact, args.get_s("extendFieldInfo").get_s("leader")});
        }
    }
    else 
    {
        if (extendField != INV_EtDbId)
        {
            ClearDbContactExtendField(pFieldsMgr, extendField);
        }
    }

    extendField = spContactField->GetEmailField();
    if (createEmail)
    {
        if (extendField == INV_EtDbId)
        {
            extendFieldVec.push_back({Et_DbSheetField_Email, args.get_s("extendFieldInfo").get_s("email")});
        }
    }
    else 
    {
        if (extendField != INV_EtDbId)
        {
           ClearDbContactExtendField(pFieldsMgr, extendField);
        }
    }

    extendField = spContactField->GetEmployeeIdField();
    if (createEmployeeId)
    {
        if (extendField == INV_EtDbId)
        {
            extendFieldVec.push_back({Et_DbSheetField_MultiLineText, args.get_s("extendFieldInfo").get_s("employeeId")});
        }
    }
    else 
    { 
        if (extendField != INV_EtDbId)
        {
           ClearDbContactExtendField(pFieldsMgr, extendField);
        }
    }
    
    UINT insertFieldCnt = extendFieldVec.size();
    if (insertFieldCnt > 0)
    {
        ks_stdptr<IDBSheetRange> spDbRange;
        hr = pDBSheetOp->InsertFields(insertFieldCnt, &spDbRange);
        if (FAILED(hr))
            return hr;
        
        for (UINT i = 0; i < insertFieldCnt; i++)
        {
            EtDbId fldId = spDbRange->GetFieldId(i);
            ks_stdptr<IDbField> spExtendField;
            hr = pFieldsMgr->GetField(fldId, &spExtendField);
            if (FAILED(hr))
                return hr;

            const std::pair<ET_DbSheet_FieldType, VarObj>& extendFieldInfo = extendFieldVec.at(i);
            spExtendField->SetType(extendFieldInfo.first, nullptr);
            ks_wstring fmtName = pField->GetName();
            fmtName.AppendFormat(__X("·%s"), extendFieldInfo.second.field_str("name"));
            spExtendField->SetName(fmtName.c_str(), TRUE, FALSE);
            hr = spExtendField->SetAutoFillSourceField(pField->GetID());
            if (FAILED(hr))
                return hr;
        }

        if (ppIstRg)
            *ppIstRg = spDbRange.detach();
    }
    if (type == Et_DbSheetField_Contact && spContactField->GetSupportMulti() && spContactField->ExistExtendField())
        return E_DBSHEET_UNSUPPORTED_MUTIPLE;
    return hr;
}

HRESULT ClearDbContactExtendField(IDbFieldsManager* pFieldsMgr, EtDbId extendFldId)
{
    return SetDbContactExtendField(pFieldsMgr, extendFldId, INV_EtDbId);
}

HRESULT SetDbContactExtendField(IDbFieldsManager* pFieldsMgr, EtDbId extendFldId, EtDbId contactFldId)
{
    if (!pFieldsMgr)
        return E_FAIL;

    ks_stdptr<IDbField> spExtendField;
    pFieldsMgr->GetField(extendFldId, &spExtendField);
    if (spExtendField)
    {
        HRESULT hr = spExtendField->SetAutoFillSourceField(contactFldId);
        if (FAILED(hr))
            return hr;
    }

    return S_OK;
}

HRESULT GetDbContactExtendFields(IDbField* pField, std::vector<EtDbId>& ids)
{
    if (!pField)
        return E_FAIL;

    ET_DbSheet_FieldType type = pField->GetType();
    if (type != Et_DbSheetField_Contact && type != Et_DbSheetField_CreatedBy)
        return E_FAIL;

    ks_stdptr<IDbField_Contact> spContactField = pField;
    EtDbId extendField = spContactField->GetDepartmentField();
    if (extendField != INV_EtDbId) ids.push_back(extendField);

    extendField = spContactField->GetLeaderField();
    if (extendField != INV_EtDbId) ids.push_back(extendField);

    extendField = spContactField->GetEmailField();
    if (extendField != INV_EtDbId) ids.push_back(extendField);

    extendField = spContactField->GetEmployeeIdField();
    if (extendField != INV_EtDbId) ids.push_back(extendField);

    return S_OK;
}

HRESULT ConfigureDbFormulaResultField(IDbField* pField, const VarObj& args, bool bIsSyncSheet)
{
    if (!bIsSyncSheet)
        return E_FAIL;
    VAR_OBJ_EXPECT_STRING(args, "valueType");
    Et_DbSheet_Field_Value_Type type = DbSheet_Fvt_Text;
    VS(_appcore_GainEncodeDecoder()->DecodeDbFieldValueType(args.field_str("valueType"), &type));
    ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pField;
    spField_FormulaResult->SetFormulaResultType(type);
    if (args.has("numberFormat"))
    {
        VAR_OBJ_EXPECT_STRING(args, "numberFormat");
        WebStr numberFormat = args.field_str("numberFormat");
        if (xstrcmp(__X(""), numberFormat) != 0)
        {
            HRESULT hr = pField->SetNumberFormat(numberFormat);
            if(FAILED(hr))
                return hr;
        }
    }

    return S_OK;
}

namespace array_field_fall_back
{

void convertToText(IDbField* pField)
{
    IDBSheetOp* const pSheetOp = pField->GetDbSheetData();
    const IDBIds* pRecordIDs = pSheetOp->GetAllRecords();
    const EtDbIdx count = pRecordIDs->Count();
    const EtDbId fieldID = pField->GetID();
    std::unordered_map<EtDbId, ks_wstring> recCellStringMap;
    for (EtDbIdx i = 0; i < count; ++i)
    {
        const EtDbId ID = pRecordIDs->IdAt(i);
        KDBGroupUnit unit = DBGU_Text;
        ks_bstr cellString;
        pSheetOp->GetCellDisplayString(ID, fieldID, unit, &cellString, true);
        recCellStringMap.insert(std::make_pair(ID, cellString.c_str()));
    }

    pField->SetType(Et_DbSheetField_MultiLineText, nullptr);
    TypeChangeInfo info;
    pField->OnChangeFieldType(pField->GetType(), Et_DbSheetField_MultiLineText, &info);
    // 设置pField部分属性为默认
    pField->SetNumberFormatForIOAndDiffuse(kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_TEXT1));
    pField->SetValueUniqueForIO(FALSE);
    pField->SetDefaultValForIO(__X(""));
    pField->SetDefaultValTypeForIO(DbSheet_Field_Dvt_Normal);

    for (EtDbIdx i = 0; i < count; ++i)
    {
        const EtDbId ID = pRecordIDs->IdAt(i);
        ASSERT(recCellStringMap.find(ID) != recCellStringMap.end());
        auto&& cellString = recCellStringMap[ID];
        if (cellString.empty())
        {
            pSheetOp->SetTokenValue(ID, fieldID, nullptr);
            continue;
        }
        if (FAILED(pSheetOp->SetValue(ID, fieldID, cellString.c_str())))
            pSheetOp->SetTokenValue(ID, fieldID, nullptr);
    }
}

bool unwrap(const_token_ptr pToken, IDBSheetOp* pSheetOp, EtDbId recordID, EtDbId fieldID, IDbField* pField)
{
    if (!pToken)
    {
        pSheetOp->SetTokenValue(recordID, fieldID, nullptr);
        return true;
    }
    const auto setTokenSelf {[pToken, pSheetOp, recordID, fieldID]() -> void {
        const_token_ptr token {};
        pSheetOp->GetValueToken(recordID, fieldID, &token);
        if (token == pToken)
            return;
        pSheetOp->SetTokenValue(recordID, fieldID, pToken);
    }};
    if (!alg::const_handle_token_assist::is_type(pToken))
    {
        setTokenSelf();
        return true;
    }
    alg::const_handle_token_assist assist {pToken};
    if (assist.get_handleType() != alg::ET_HANDLE_TOKENARRAY)
    {
        setTokenSelf();
        return true;
    }
    const auto pArrayHandle {assist.get_handle()->CastArray()};
    const auto count {pArrayHandle->GetCount()};
    if (count == 0)
    {
        pSheetOp->SetTokenValue(recordID, fieldID, nullptr);
        return true;
    }
    else
    {
        switch (pField->GetType())
        {
        case Et_DbSheetField_CreatedBy:
        case Et_DbSheetField_LastModifiedBy:
        case Et_DbSheetField_Contact:
        case Et_DbSheetField_Attachment:
        case Et_DbSheetField_Department:
        {
            const_token_ptr item {};
            pArrayHandle->Item(0, &item);
            if (alg::const_handle_token_assist {item}.get_handleType() != alg::ET_HANDLE_TOKENARRAY)
            {
                setTokenSelf();
                return true;
            }
            break;
        }
        default:
            break;
        }
        if (count == 1)
        {
            const_token_ptr item {};
            pArrayHandle->Item(0, &item);
            return unwrap(item, pSheetOp, recordID, fieldID, pField);
        }
    }
    return false;
}

bool checkErrorToken(IDbField* pField)
{
    IDBSheetOp* const pSheetOp = pField->GetDbSheetData();
    const IDBIds* pRecordIDs = pSheetOp->GetAllRecords();
    const EtDbIdx count = pRecordIDs->Count();
    const EtDbId fieldID = pField->GetID();
    for (EtDbIdx i = 0; i < count; ++i)
    {
        const EtDbId recordID = pRecordIDs->IdAt(i);
        const_token_ptr token {};
        pSheetOp->GetValueToken(recordID, fieldID, &token);
        if (token)
        {
            const auto type {GetExecTokenMajorType(token)};
            switch (type)
            {
            case alg::ETP_ERROR:
                return true;
            case alg::ETP_HANDLE:
            {
                constexpr struct {
                    bool operator()(const_token_ptr token) const
                    {
                        if (alg::const_handle_token_assist::is_type(token))
                        {
                            alg::const_handle_token_assist assist {token};
                            const auto handle {assist.get_handle()->CastArray()};
                            if (handle)
                            {
                                const auto count {handle->GetCount()};
                                for (auto i {0}; i < count; ++i)
                                {
                                    const_token_ptr subtoken {};
                                    handle->Item(i, &subtoken);
                                    if ((*this)(subtoken))
                                        return true;
                                }
                            }
                            else
                            {
                                return false;
                            }
                        }
                        return alg::const_error_token_assist::is_type(token);
                    }
                } check {};
                if (check(token))
                    return true;
                break;
            }
            default:
                break;
            }
        }
    }
    return false;
}

void fallback(IDbField* pField, OUT bool* pIsFallBack2Txt)
{
    DisableDbUpdateLastModifiedInfoScope scope; //引用字段回落时不需要更新最后修改人修改时间
    IDBSheetOp* const pSheetOp = pField->GetDbSheetData();
    const auto oldNumberFormat {pField->GetNumberFormat()};
    app_helper::KBatchUpdateCal buc(pSheetOp->GetBook()->LeakOperator());
    if (pField->GetType() == Et_DbSheetField_FormulaResult)
    {
        if (checkErrorToken(pField))
        {
            convertToText(pField);
            if (pIsFallBack2Txt != nullptr)
                *pIsFallBack2Txt = true;
            return;
        }
        ks_stdptr<IDbField_FormulaResult> spField_FormulaResult = pField;
        switch (spField_FormulaResult->GetFormulaResultType())
        {
        case DbSheet_Fvt_Number:
        case DbSheet_Fvt_Logic:
            pField->SetTypeForIO(Et_DbSheetField_Number);
            {
                TypeChangeInfo info;
                pField->OnChangeFieldType(pField->GetType(), Et_DbSheetField_Number, &info);
            }
            pField->SetNumberFormatForIOAndDiffuse(oldNumberFormat);
            break;
        case DbSheet_Fvt_Text:
        case DbSheet_Fvt_Contact:
            convertToText(pField);
            if (pIsFallBack2Txt != nullptr)
                *pIsFallBack2Txt = true;
            return;
        case DbSheet_Fvt_Date:
            pField->SetTypeForIO(Et_DbSheetField_Date, TRUE);
            {
                TypeChangeInfo info;
                pField->OnChangeFieldType(pField->GetType(), Et_DbSheetField_MultiLineText, &info);
            }
            break;
        case DbSheet_Fvt_Time:
            pField->SetTypeForIO(Et_DbSheetField_Time, TRUE);
            {
                TypeChangeInfo info;
                pField->OnChangeFieldType(pField->GetType(), Et_DbSheetField_MultiLineText, &info);
            }
            break;
        default:
            ASSERT("不走新逻辑避免出现其它问题" == nullptr);
            return;
        }
    }
    const EtDbId fieldID = pField->GetID();
    const IDBIds* const pAllRecords = pSheetOp->GetAllRecords();
    const EtDbIdx recordCount = pAllRecords->Count();
    for (EtDbIdx i = 0; i < recordCount; ++i)
    {
        EtDbId recordID = pAllRecords->IdAt(i);
        const_token_ptr token {};
        pSheetOp->GetValueToken(recordID, fieldID, &token);
        if (token && !unwrap(token, pSheetOp, recordID, fieldID, pField))
        {
            convertToText(pField);
            if (pIsFallBack2Txt != nullptr)
                *pIsFallBack2Txt = true;
            return;
        }
    }

    if (pField->GetType() == Et_DbSheetField_Lookup)
    {
        Et_DbSheet_Field_Value_Type valueType = pField->GetValueType();
        switch (valueType)
        {
            case DbSheet_Fvt_Number:
                pField->SetTypeForIO(Et_DbSheetField_Number);
                pField->SetNumberFormatForIOAndDiffuse(oldNumberFormat);
                break;
            case DbSheet_Fvt_Text:
                pField->SetTypeForIO(Et_DbSheetField_MultiLineText, TRUE);
                break;
        }
    }

    // 由于例如编号字段在同步时虽会回落为文本，但其numFmt会带上000000。而引用编号原始单值/去重单值在另存为fallback时，并不会进入convertToText
    // 中清除所有格式，故在此特殊处理。另需注意此处仅清除numFmt，而非清除全部格式，否则会影响到引用（带默认值/禁重复）文本fallback的情形
    if (pField->GetType() == Et_DbSheetField_MultiLineText)
        pField->SetNumberFormatForIOAndDiffuse(kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_TEXT1));
}

}       // namespace DbSheet::array_field_fall_back

namespace statistic_sheet_helper {

bool needDiscardZeroDoubleToken(IDbField *pField, const_token_ptr pToken) noexcept
{
    switch (pField->GetType())
    {
    case Et_DbSheetField_SingleSelect:
    case Et_DbSheetField_MultipleSelect:
    case Et_DbSheetField_Contact:
    case Et_DbSheetField_Address:
    case Et_DbSheetField_Cascade:
    case Et_DbSheetField_Department:
        if (alg::GetExecTokenMajorType(pToken) == alg::ETP_VDBL && alg::const_vdbl_token_assist {pToken}.get_value() == 0.)
            return true;
        break;
    default:
        break;
    }
    return false;
}

}       // namespace wo::DbSheet::statistic_sheet_helper

namespace
{
HRESULT createAutoLinkCondProps(const VarObj& args, IDbAutolinkCondProps** ppAutoLinkCondProps)
{
    if (not args.has("filter"))
        return S_FALSE;

    VarObj filter = args.get_s("filter");
    if (binary_wo::typeStruct != filter.type())
        return E_DBSHEET_FILTER_INVALID;

    HRESULT hr = S_OK;
    // 用groups区分条件组, conditions 是 group 下的关键字. 如果要开放"或"选项, 则直接用条件组来描述
    ks_stdptr<IDbAutolinkCondProps> spAutoLinkCondProps;
    _appcore_CreateObject(CLSID_KDbAutolinkCondProps, IID_IDbAutolinkCondProps, (void **)&spAutoLinkCondProps);

        // 自动关联2期. 正式版本发布后, 只需保留这个逻辑分支
        // PS:下面的格式未对齐，是因为不想产生代码差异
        if (not filter.has("groups"))
            return E_INVALIDARG;
        VarObj groups = filter.get_s("groups");
        if (binary_wo::typeArray != groups.type())
            return E_DBSHEET_FILTER_INVALID;

        int32 condGroupCnt = groups.arrayLength();
        if (0 == condGroupCnt) // 不接受0个条件组
        {
            if (args.has("lookupType"))
            {
                UINT iType = args.field_uint32("lookupType");
                if (iType > DBLookupType::LT_NORMAL)
                    return E_DBSHEET_LOOKUP_CONDS_EMPTY;
            }
            return E_INVALIDARG;
        }
        for (int32 groupIdx = 0; groupIdx < condGroupCnt; ++groupIdx)
        {
            VarObj group = groups.at(groupIdx);
            if (binary_wo::typeStruct != group.type())
                return E_DBSHEET_FILTER_INVALID;
            if (not group.has("conds"))
                return E_DBSHEET_FILTER_INVALID;

            VarObj conditions = group.get_s("conds");
            if (binary_wo::typeArray != conditions.type())
                return E_DBSHEET_FILTER_INVALID;

            int32 condGroupSize = conditions.arrayLength();
            if (0 == condGroupSize) // 不接受0个条件
                return E_INVALIDARG;

            spAutoLinkCondProps->AddConditionGroup();
            for (int32 condIdx = 0; condIdx < condGroupSize; ++condIdx)
            {
                VarObj criterion = conditions.at(condIdx);
                if (binary_wo::typeStruct != criterion.type())
                    return E_DBSHEET_FILTER_INVALID;
                if (not criterion.has("linkSheetFieldId") || not criterion.has("curSheetCondContents") || 
                    not criterion.has("curSheetCondType") || not criterion.has("condOpType"))
                    return E_DBSHEET_FILTER_INVALID;

                IDbAutolinkCondProp* pProp = spAutoLinkCondProps->InsertInTheEnd();
                EtDbId linkSheetFieldId = INV_EtDbId;
                pProp->SetLinkedSheetFieldId(GetEtDbId(criterion, "linkSheetFieldId"));

                if (criterion.has("dateIntersectedValues"))
                {
                    VAR_OBJ_EXPECT_ARRAY(criterion, "dateIntersectedValues");
                    VarObj dateIntersectedValues = criterion.get("dateIntersectedValues");
                    int32 c = dateIntersectedValues.arrayLength_s();
                    std::vector<DbAutoLinkDateInfo> dateInfos(c, {-1, -1, -1, -1, -1, -1});
                    for (int32 i = 0, c = dateIntersectedValues.arrayLength_s(); i < c; ++i)
                    {
                        VarObj val = dateIntersectedValues.at_s(i);
                        if (val.type() != binary_wo::typeStruct)
                            return E_DBSHEET_FILTER_INVALID;
                        VAR_OBJ_EXPECT_STRING(val, "type");
                        if (xstrcmp(__X("Date"), val.field_str("type")) != 0)
                            return E_DBSHEET_FILTER_INVALID;

                        DbAutoLinkDateInfo& dateInfo = dateInfos[i];

                        // 如果没有任何一个具体的日期时间信息, 则它表示的是"匹配空单元格"而不是匹配任意值
                        // 自动关联在日期时间区间的"指定值"条件, 有单独表示"匹配任意值"的方式, 因此用这种方式表示匹配空单元格
                        if (not val.has("year"))
                            continue;
                        VAR_OBJ_EXPECT_NUMERIC(val, "year");
                        dateInfo.year = val.field_uint32("year");

                        if (not val.has("mon"))
                            continue;
                        VAR_OBJ_EXPECT_NUMERIC(val, "mon");
                        dateInfo.mon = val.field_uint32("mon");

                        if (not val.has("mday"))
                            continue;
                        VAR_OBJ_EXPECT_NUMERIC(val, "mday");
                        dateInfo.mday = val.field_uint32("mday");

                        if (not val.has("hour"))
                            continue;
                        VAR_OBJ_EXPECT_NUMERIC(val, "hour");
                        dateInfo.hour = val.field_uint32("hour");

                        if (not val.has("min"))
                            continue;
                        VAR_OBJ_EXPECT_NUMERIC(val, "min");
                        dateInfo.min = val.field_uint32("min");

                        if (not val.has("sec"))
                            continue;
                        VAR_OBJ_EXPECT_NUMERIC(val, "sec");
                        dateInfo.sec = val.field_uint32("sec");
                    }
                    pProp->SetIntersectedDateTime(dateInfos.data(), dateInfos.size());
                }
                else
                {
                    VarObj contents = criterion.get("curSheetCondContents");
                    const size_t contentCnt = contents.arrayLength_s();
                    std::vector<PCWSTR> contentVec;
                    for (size_t i = 0; i < contentCnt; ++i)
                    {
                        contentVec.push_back(contents.item_str(i));
                    }
                    pProp->SetIntersectedStrLiterals(contentVec.data(), contentVec.size());
                }

                ET_DBSheet_Autolink_CondType condType = ET_DBSheet_Autolink_CondType::Field;
                hr = _appcore_GainEncodeDecoder()->DecodeDbAutolinkCondType(criterion.field_str("curSheetCondType"), &condType);
                if (FAILED(hr))
                    return hr;
                pProp->SetCurSheetCondType(condType);

                KDbFilterCriteriaOpType opt = DBFCOT_Equals;
                hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterCriteriaOpType(criterion.field_str("condOpType"), &opt);
                if (FAILED(hr))
                    return hr;
                pProp->SetCriterionOpt(opt);
            }
        }
    *ppAutoLinkCondProps = spAutoLinkCondProps.detach();
    return S_OK;
}

HRESULT configureDbAutolinkField(IDbField_Link* pLinkField, const VarObj& args)
{
    ks_stdptr<IDbAutolinkCondProps> spAutoLinkCondProps;
    HRESULT hr = createAutoLinkCondProps(args, &spAutoLinkCondProps);
    if (FAILED(hr))
        return hr;
    pLinkField->SetAutoLink(args.field_uint32("linkSheet"), TRUE, spAutoLinkCondProps.get());
    return S_OK;
}

HRESULT customConfigDbLinkField(IDbField_Link* pLinkField, const VarObj& args)
{
    if (not args.has("customConfig"))
        return S_FALSE;

    HRESULT hr = S_OK;
    VAR_OBJ_EXPECT_STRUCT(args, "customConfig");
    VarObj vConfig = args.get_s("customConfig");
    ks_stdptr<IDbLinkCustomConfig> spLinkCustomConfig;
    _appcore_CreateObject(CLSID_KDbLinkCustomConfig, IID_IDbLinkCustomConfig, (void **)&spLinkCustomConfig);
    BOOL bAllRecords = FALSE;
    if (vConfig.has("isAllRecords"))
    {
        VAR_OBJ_EXPECT_BOOL(vConfig, "isAllRecords");
        bAllRecords = alg::bool2BOOL(vConfig.field_bool("isAllRecords"));
        spLinkCustomConfig->SetIsAllRecords(bAllRecords);
    }
    if (vConfig.has("filter") && !bAllRecords)
    {
        ks_stdptr<IDbAutolinkCondProps> spCondProps;
        hr = createAutoLinkCondProps(vConfig, &spCondProps);
        if (FAILED(hr))
            return hr;

        spLinkCustomConfig->SetCondGroups(spCondProps.get());
    }
    BOOL bAllFields = FALSE;
    if (vConfig.has("isAllFields"))
    {
        VAR_OBJ_EXPECT_BOOL(vConfig, "isAllFields");
        bAllFields = alg::bool2BOOL(vConfig.field_bool("isAllFields"));
        spLinkCustomConfig->SetIsAllFields(bAllFields);
    }
    if (vConfig.has("visibleFields") && !bAllFields)
    {
        std::vector<EtDbId> visibleFields;
        VAR_OBJ_EXPECT_ARRAY(vConfig, "visibleFields");
        VarObj vVisibleFields = vConfig.get_s("visibleFields");
        for (int i = 0; i < vVisibleFields.arrayLength_s(); ++i)
        {
            EtDbId fieldId = GetEtDbId(vVisibleFields.at(i));
            if (fieldId == INV_EtDbId)
            {
                WOLOG_WARN << "[customConfigDbLinkField] visibleFields id invalid, continue.";
                continue;
            }
            visibleFields.push_back(fieldId);
        }
        spLinkCustomConfig->SetVisibleFields(visibleFields.data(), visibleFields.size());
    }
    return pLinkField->SetLinkCustomConfig(args.field_uint32("linkSheet"), spLinkCustomConfig.get());
}
} // (anonymous)

HRESULT ConfigureLookupCondProps(IDbField* pField, IDBSheetOp* pDbOp, const VarObj& args)
{
    ks_stdptr<IDbAutolinkCondProps> spAutoLinkCondProps;
    // 普通引用字段跳过条件配置
    ks_stdptr<IDbField_Lookup> spFieldLookup = pField;
    HRESULT hr = S_OK;
    bool bIsSearchStatisticLookupField = false;
    if (args.has("lookupType"))
    {
        UINT iType = args.field_uint32("lookupType");
        hr = spFieldLookup->SetLookupType(static_cast<DBLookupType>(iType));
        if (FAILED(hr))
            return hr;
        if (spFieldLookup->GetLookupType() > LT_NORMAL)
            bIsSearchStatisticLookupField = true;
    }
    if (bIsSearchStatisticLookupField)
    {
        hr = createAutoLinkCondProps(args, &spAutoLinkCondProps);
        if (hr == E_DBSHEET_LOOKUP_CONDS_EMPTY)
            _appcore_CreateObject(CLSID_KDbAutolinkCondProps, IID_IDbAutolinkCondProps, (void **)&spAutoLinkCondProps);
        else if (FAILED(hr))
            return hr;
    }
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    if (args.has("linkField"))
    {
        EtDbId id = INV_EtDbId;
        pCtx->DecodeEtDbId(args.field_str("linkField"), &id);
        hr = spFieldLookup->SetLinkFieldId(id);
        if (FAILED(hr))
            return hr;
    }
    if (bIsSearchStatisticLookupField && args.has("lookupSheetId"))
    {
        UINT lookupSheetId = args.field_uint32("lookupSheetId");
        // 就是这里，把当前的查找引用字段与外部sheet关联起来
        spFieldLookup->SetAutoLink(lookupSheetId, TRUE, spAutoLinkCondProps.get());
        hr = spFieldLookup->SetLookupSheetId(lookupSheetId);
        if (FAILED(hr))
            return hr;
    }
    if (args.has("lookupField"))
    {
        EtDbId id = INV_EtDbId;
        pCtx->DecodeEtDbId(args.field_str("lookupField"), &id);
        if (args.has("linkField"))
        {
            ks_stdptr<IDbField_Link> spLinkField;
            hr = spFieldLookup->GetLinkField(&spLinkField);
            if (SUCCEEDED(hr))
            {
                UINT sheetId = spLinkField->GetLinkSheet();
                IDBSheetOp* pDbSheetOp = pField->GetDbSheetData();
                if (pDbSheetOp)
                {
                    IBook* pBook = pDbSheetOp->GetBook();
                    if (pBook)
                    {
                        hr = ValidateBaseLookupField(pBook, sheetId, id);
                        if (FAILED(hr))
                            return hr;
                    }
                }
            }
        }

        hr = spFieldLookup->SetLookupFieldId(id);
        if (FAILED(hr))
            return hr;
    }
    if (args.has("lookupFunction"))
    {
        ET_DbSheet_Lookup_Function func = DbSheet_Lookup_Function_Origin;
        _appcore_GainEncodeDecoder()->DecodeDbLookupFunction(args.field_str("lookupFunction"), &func);
        hr = spFieldLookup->SetLookupFunction(func);
        if (FAILED(hr))
            return hr;
    }
    return hr;
}

HRESULT ConfigureDbLinkField(IDbField* pField, const VarObj& args, bool bNewField, const char* multipleLinks)
{
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    HRESULT hr = S_OK;
    ks_stdptr<IDbField_Link> spFieldLink = pField;

    if (bNewField)
    {
        VAR_OBJ_EXPECT_NUMERIC(args, "linkSheet");
        VAR_OBJ_EXPECT_BOOL(args, multipleLinks);
    }
    bool isAuto = false;
    bool isCustomConfig = false;
    if (args.has("isAuto"))
        isAuto = args.field_bool("isAuto");

    if(pField->GetType() == Et_DbSheetField_OneWayLink)
    {
        //统计表的单向关联字段必须得是自动关联
        if(IsStatSheet(pField->GetDbSheetData()->GetBook(), pField->GetDbSheetData()->GetSheetId()))
        {
            if(!isAuto)
            {
                WOLOG_ERROR << "[ConfigureDbLinkField] For statSheet, when ConfigureDbLinkField it's not auto mode failed!";
                return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
            }
        }
    }

    if (args.has("linkSheet"))
    {
        if (isAuto)
        {
            hr = configureDbAutolinkField(spFieldLink.get(), args);
            if (FAILED(hr))
                return hr;

            isCustomConfig = false;
        }
        else
        {
            UINT linkSheetId = args.field_uint32("linkSheet");
            PCWSTR bidirectionalLinkPrefix = __X("");
            EtDbId viewId = INV_EtDbId;
            if (args.has("linkPrefix"))
                bidirectionalLinkPrefix = args.field_str("linkPrefix");
            if (args.has("linkView"))
                pCtx->DecodeEtDbId(args.field_str("linkView"), &viewId);
            if (args.has("customConfig"))
            {
                if (viewId != INV_EtDbId)
                    return E_INVALIDARG;

                hr = customConfigDbLinkField(spFieldLink.get(), args);
                if (FAILED(hr))
                    return hr;

                isCustomConfig = true;
            }
            IDBSheetOp* pDbOp = pField->GetDbSheetData();
            ks_stdptr<IDBSheetViews> spDbSheetViews;
            ks_stdptr<IDBSheetView> spView;
            GetDBSheetView(pDbOp->GetBook(), linkSheetId, viewId, &spView, &spDbSheetViews);
            if (spView == nullptr && viewId != INV_EtDbId)
                return E_FAIL;

            hr = spFieldLink->SetLinkSheet(linkSheetId, bidirectionalLinkPrefix, viewId);
            if (FAILED(hr))
                return hr;
        }
    }

    if (args.has(multipleLinks))
    {
        hr = spFieldLink->SetSupportMultiLinks(args.field_bool(multipleLinks));
        if (FAILED(hr))
            return hr;
    }

    if (not isAuto)
    {
        // 只有字段是自动关联时, 才显式设置它为手动关联
        if (spFieldLink->IsAutoLink())
            spFieldLink->SetAutoLinkProp(FALSE, nullptr);
    }
    if (!isCustomConfig && spFieldLink->IsLinkCustomConfig())
        spFieldLink->SetLinkCustomConfig(spFieldLink->GetLinkSheet(), nullptr);
    // 新建字段时, 根据字段所关联的表修改字段名称. 这步需在字段名及字段的关联表已设置之后执行
    // 仅当传入的名称是自动生成的(i.e. isDefaultName), 才应用传入的前缀修改名称
    if (bNewField && binary_wo::typeString == args.get_s("linkPrefix").type()
        && binary_wo::typeBool == args.get_s("isDefaultName").type() && args.field_bool("isDefaultName"))
    {
        PCWSTR bidirectionalLinkPrefix = args.field_str("linkPrefix");
        ks_stdptr<IDbField_Link> spFieldLink = pField;

        ks_stdptr<ISheet> spSheet;
        HRESULT hr = GetDbSheet(pField->GetDbSheetData()->GetBook()
            , spFieldLink->GetLinkSheet(), &spSheet);
        if (SUCCEEDED(hr) && spSheet)
        {
            PCWSTR pcwsLinkedSheetName = NULL;
            spSheet->GetName(&pcwsLinkedSheetName);
            ks_wstring fieldName(bidirectionalLinkPrefix);
            fieldName.append(pcwsLinkedSheetName);
            VS(pField->SetName(fieldName.c_str(), TRUE, FALSE));
        }
    }
    return hr;
}

static HRESULT preConfigureBeforeTypeCast(IDBSheetView* pView, IDbField* pField, const VarObj& args, ET_DbSheet_FieldType type)
{
	ASSERT(pField);
	ET_DbSheet_FieldType oldType = pField->GetType();
	HRESULT hr = S_OK;
	IDBSheetCtx* pCtx = _appcore_GainDbSheetContext();
	switch (oldType)
	{
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
			// 选项互转时需要先处理选项内容变更及对应内容的更新，故统一把选项互转和选项内容的变换放在类型处理前
			if (type == Et_DbSheetField_SingleSelect || type == Et_DbSheetField_MultipleSelect)
			{
				VAR_OBJ_EXPECT_ARRAY(args, "listItems")
				VarObj vListItems = args.get("listItems");
				int itemsCnt = vListItems.arrayLength_s();
				if (itemsCnt == 0)
					return E_DBSHEET_SELECT_ITEM_EMPTY;

				std::vector<SelectFieldItem> itemVec;
				for (int i = 0; i < itemsCnt; ++i)
				{
					VarObj vItem = vListItems.at_s(i);
					VAR_OBJ_EXPECT_STRING(vItem, "value")
					EtDbId id = INV_EtDbId;
					if (vItem.has("id"))
						pCtx->DecodeEtDbId(vItem.field_str("id"), &id);
					ARGB color = DEFAULT_COLOR;
					if (vItem.has("color"))
						color = vItem.field_uint32("color");
					itemVec.emplace_back(id, vItem.field_str("value"), color);
				}
				ks_stdptr<IDbField_Select> spFieldSelect = pField;
				hr = spFieldSelect->BatchEditItems(itemVec.data(), itemVec.size());
				if (FAILED(hr))
					return hr;

                //单/多选项互转时 对选项类型字段的"允许填写时新增选项"属性进行修改。
                if(args.has("allowAddItemWhenInputting"))
                {
                    BOOL bAllowAddItemWhenInputting = alg::bool2BOOL(args.field_bool("allowAddItemWhenInputting"));
                    spFieldSelect->SetAllowAddItemWhenInputting(bAllowAddItemWhenInputting);
                }
            break;
			}
        case Et_DbSheetField_CreatedBy:
        case Et_DbSheetField_Contact:
        {
            ks_stdptr<IDbField_Contact> spFieldContact = pField;
            IDbFieldsManager* pFieldManager =  pView->GetFieldsManager();
            if (oldType != type && spFieldContact->ExistExtendField())
            {
                EtDbId fieldId =  spFieldContact->GetDepartmentField();
                if (fieldId != INV_EtDbId)
                {
                    ClearDbContactExtendField(pFieldManager, fieldId);
                }
                fieldId =  spFieldContact->GetLeaderField();
                if (fieldId != INV_EtDbId)
                {
                    ClearDbContactExtendField(pFieldManager, fieldId);
                }
                fieldId =  spFieldContact->GetEmailField();
                if (fieldId != INV_EtDbId)
                {
                    ClearDbContactExtendField(pFieldManager, fieldId);
                }
                fieldId =  spFieldContact->GetEmployeeIdField();
                if (fieldId != INV_EtDbId)
                {
                    ClearDbContactExtendField(pFieldManager, fieldId);
                }
            }   
            else if (Et_DbSheetField_Contact == oldType && oldType != type && pField->GetAutoFillSourceField() != INV_EtDbId)
            {   // 扩展 leader 字段类型是Contact, 改变字段类型时也需要清除SourceField
                pField->SetAutoFillSourceField(INV_EtDbId);
            }
            break;
        }
        case Et_DbSheetField_Department:
        case Et_DbSheetField_Email:
        case Et_DbSheetField_MultiLineText:
             {
                //扩展字段修改或转为其他类型
                EtDbId oldAutoFillSourceField = pField->GetAutoFillSourceField();
                if (oldType != type  && oldAutoFillSourceField != INV_EtDbId)
                {   
                    pField->SetAutoFillSourceField(INV_EtDbId);
                }
             }
			break;
		default:
			break;
	}
	return S_OK;
}

namespace
{
}

HRESULT correctFieldType(IDbField* pField, const VarObj& args, ET_DbSheet_FieldType& type)
{
	ASSERT(pField);
	ET_DbSheet_FieldType oldType = pField->GetType();

    IDBSheetOp* pDbOp = pField->GetDbSheetData();
    if(IsStatSheetSettingField(pDbOp->GetBook(), pDbOp->GetSheetId(), pField->GetID()))
	{
		//统计表自动生成的字段，不能更改类型，但是可以更改字段名、格式
		if(oldType != type)
		{
            WOLOG_ERROR << "[correctFieldType] For statSheet, when correctFieldType the new field type is unvalid!";
			return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
		}
	}

    switch (type)
    {
    case Et_DbSheetField_Link: // 目标类型为"双向关联"
    {
        UINT linkSheetId = 0;
        if (args.has("linkSheet"))
        {
            VAR_OBJ_EXPECT_NUMERIC(args, "linkSheet");
            linkSheetId = args.field_uint32("linkSheet");
        }
        else
        {
            // 参数未指定linkSheet, 此时要求原字段是关联字段以提供sheet信息
            if (oldType != Et_DbSheetField_Link && oldType != Et_DbSheetField_OneWayLink)
                return E_INVALIDARG;
            ks_stdptr<IDbField_Link> spField_Link = pField;
            linkSheetId = spField_Link->GetLinkSheet();
        }
        if (linkSheetId == pField->GetDbSheetData()->GetSheetId()) // sheetId表示目标字段是一个到本表的关联
            type = Et_DbSheetField_OneWayLink;
        break;
    }
    default:
        break;
    }
    return S_OK;
}

HRESULT ConfigureDbField(IDbField* pField, const VarObj& args, ET_DbSheet_FieldType type, bool bNewField, IDBSheetView* pView, bool autoFillingSelectItems,
                         BOOL bNeedValidateName)
{
	// type 是字段转换情况下时要转换的类型，非转换时传入原类型即可
	// autoFillingSelectItems表示非选项字段转换为选项字段时是否将单元格内容填充选项
    // bNewField 表示是否为新增的字段. 新增字段时, 一些参数会被强制要求
	ASSERT(pField != nullptr);
	ET_DbSheet_FieldType oldType = pField->GetType();
	HRESULT hr = preConfigureBeforeTypeCast(pView, pField, args, type);
	if (FAILED(hr))
		return hr;
    hr = correctFieldType(pField, args, type);
    if (FAILED(hr))
        return hr;
	hr = pField->SetType(type, pView);
	if (FAILED(hr))
		return hr;

    if (args.has("description"))
    {
        hr = pField->SetDescription(args.field_str("description"));
        if (FAILED(hr))
            return hr;
    }
    // fieldName
    if (bNewField)
        VAR_OBJ_EXPECT_STRING(args, "fieldName");
    if (args.has("fieldName"))
    {
        hr = pField->SetName(args.field_str("fieldName"), TRUE, bNeedValidateName);
        if (FAILED(hr))
            return hr;
    }
    // numberFormat
    if (args.has("numberFormat"))
    {
        hr = pField->SetNumberFormatForIOAndDiffuse(args.field_str("numberFormat"));
        if (FAILED(hr))
            return hr;
    }

    if (args.has("promptId") &&
        (Et_DbSheetField_MultiLineText == type ||
         Et_DbSheetField_SingleLineText == type))
    {
        VAR_OBJ_EXPECT_STRING(args, "promptId");
        hr = pField->SetPromptId(args.field_str("promptId"));
        if (FAILED(hr))
            return hr;
    }

    //关联字段有单独的customConfig 对象配置
    if (args.has("customConfig") && args.get_s("customConfig").type() == binary_wo::typeString)
    {
        hr = pField->SetCustomConfig(args.field_str("customConfig"));
        if (FAILED(hr))
            return hr;
    }

    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
	IDBSheetOp* pDbOp = pField->GetDbSheetData();

    if(args.has("autoFillSourceField") && !args.has("createFromOtherField")) //复制扩展字段创建的时候不设置AutoFillSourceField，当作普通字段
    {
        EtDbId id = INV_EtDbId;
        pCtx->DecodeEtDbId(args.field_str("autoFillSourceField"), &id);
        //创建者才能配置字段的自动更新数据源
        IBook* pBook = pDbOp->GetBook();
        PCWSTR creator = pBook->GetWoStake()->GetCreatorUserID();
        wo::IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
        if (pCtx && xstrcmp(creator, pCtx->getUser()->userID()) != 0) 
            return E_DBSHEET_AD_FAIL;
        hr = pField->SetAutoFillSourceField(id);
        if (FAILED(hr))
            return hr;
    }

	// 字段转换新类型或是新增字段时的相关配置
    switch (type)
    {
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
		{
            if (oldType == type)
                break;
			ks_stdptr<IDbField_Select> spFieldSelect = pField;

            //更改配置属性: 对单/多选项 "允许填写时新增选项"属性进行重新修改。
            if(args.has("allowAddItemWhenInputting"))
            {
                BOOL bAllowAddItemWhenInputting = alg::bool2BOOL(args.field_bool("allowAddItemWhenInputting"));
                spFieldSelect->SetAllowAddItemWhenInputting(bAllowAddItemWhenInputting);
            }

            // TODO: 这块整合到appcore内处理
			if (autoFillingSelectItems)
			{
				GlobalSharedStringUnorderedSet selectItems;
				GetFieldValues(pDbOp, pField->GetID(), oldType, type, selectItems);
				for (auto it = selectItems.begin(); it != selectItems.end(); ++it)
				{
					PCWSTR str = it->c_str();
					if (!str || *str == __Xc('\0'))
						continue;
					hr = spFieldSelect->AppendItemWithAutoColor(str);
					if (FAILED(hr))
						return hr;
				}
				if (!selectItems.empty())
					break;
			}
			VAR_OBJ_EXPECT_ARRAY(args, "listItems")
			VarObj vListItems = args.get("listItems");
			int itemsCnt = vListItems.arrayLength_s();
			if (itemsCnt == 0)
				return E_DBSHEET_SELECT_ITEM_EMPTY;

			for (int i = 0; i < itemsCnt; ++i)
			{
				VarObj vItem = vListItems.at_s(i);
				VAR_OBJ_EXPECT_STRING(vItem, "value")
				if (vItem.has("color"))
					hr = spFieldSelect->AppendItem(vItem.field_str("value"), vItem.field_uint32("color"));
				else
					hr = spFieldSelect->AppendItemWithAutoColor(vItem.field_str("value"));
				if (FAILED(hr))
					return hr;
			}
			break;
		}
        case Et_DbSheetField_Rating:
        {
            if (args.has("maxRating"))
            {
                ks_stdptr<IDbField_Rating> spFieldRating = pField;
                hr = spFieldRating->SetMaxRating(args.field_uint32("maxRating"));
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Date:
        {
            if (args.has("loadLegalHoliday"))
            {
                ks_stdptr<IDbField_Date> spFieldDate = pField;
                hr = spFieldDate->SetLoadLegalHoliday(args.field_bool("loadLegalHoliday"));
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
		case Et_DbSheetField_Url:
		{
			if (args.has("displayText"))
			{
				ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = pField;
				hr = spFieldHyperlink->SetDisplayText(args.field_str("displayText"));
				if (FAILED(hr))
					return hr;
			}
			break;
		}
        case Et_DbSheetField_Contact:
        case Et_DbSheetField_CreatedBy:
        {
            if (type == Et_DbSheetField_Contact)
            {
                hr = ConfigureDbContactField(pField, args, bNewField, "supportMulti", "supportNotice");
                if (FAILED(hr))
                    return hr;
            }
            ks_stdptr<IDBSheetRange> spDbRange;
            hr = ConfigureDbContactExtendField(pField, args, &spDbRange);
            if (FAILED(hr))
                return hr;
            if (spDbRange && spDbRange->GetFieldCnt() > 0)
            {
                const INT width = 2220; //创建的扩展字段提供一个初始合适的字段宽度显示完整名称
                ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid;
                if (pView->GetType() == et_DBSheetView_Grid)
                    spDbSheetViewGrid = pView;
                for (UINT i = 0; i < spDbRange->GetFieldCnt(); i++)
                {
                    EtDbId fldId = spDbRange->GetFieldId(i);
                    pView->SetFieldsHidden(fldId, false);

                    if (spDbSheetViewGrid != nullptr)
                    {
                        hr = spDbSheetViewGrid->SetFieldWidth(fldId, width, TRUE);
                        if (FAILED(hr))
                            return hr;
                    }
                }
               
                // 将新建字段放在联系人字段之后
                DbIdPostion tar;
                tar.id = pField->GetID();
                tar.isBack = TRUE;
                hr = pView->MoveFields(spDbRange, tar);
                if(FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Complete:
        {
            if (args.has("color"))
            {
                ks_stdptr<IDbField_Complete> spFieldComplete = pField;
                hr = spFieldComplete->SetColor(args.field_uint32("color"));
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Automations:
        {
            ks_stdptr<IDbField_Automations> spFieldAutomations = pField;
			return ConfigureAutomations(spFieldAutomations, pDbOp->GetBook(), args);
        }
        case Et_DbSheetField_Formula:
        {
            ks_stdptr<IDbField_Formula> spFieldFormula = pField;
            if (args.has("formula"))
            {
                hr = spFieldFormula->SetFormula(args.field_str("formula"), pDbOp->IsRefColMode() ? FML_CPL_ET_COMPATIBLE : FML_CPL_THISROW);
                if (FAILED(hr))
                    return hr;
            }
            if (args.has("showPercentAsProgress"))
            {
                hr = spFieldFormula->SetShowPercentAsProgress(args.field_bool("showPercentAsProgress"));
                if (FAILED(hr))
                    return hr;
            }
            if (bNewField && args.has("createFromOtherField"))
                spFieldFormula->OnAfterCopy();
            break;
        }
        case Et_DbSheetField_Link:
        case Et_DbSheetField_OneWayLink:
            hr = ConfigureDbLinkField(pField, args, bNewField, "supportMultiLinks");
            if (FAILED(hr))
                return hr;
            break;
        case Et_DbSheetField_Attachment:
        {
            ks_stdptr<IDbField_Attachment> spFieldAttachment= pField;
            if (args.has("attachmentType"))
            {
                hr = spFieldAttachment->SetAttachmentType(args.field_str("attachmentType"));
                if (FAILED(hr))
                    return hr;
            }
            if (args.has("displayStyle"))
            {
                ET_DbSheet_Attachment_Display_Style displayStyle = DbSheet_Attachment_Display_Style_Pic;
                _appcore_GainEncodeDecoder()->DecodeDbAttachmentDisplayStyle(args.field_str("displayStyle"), &displayStyle);
                hr = spFieldAttachment->SetAttachmentDisplayStyle(displayStyle);
                if (FAILED(hr))
                    return hr;
            }
            if(args.has("onlyUploadByCamera"))
            {
                BOOL bOnlyUploadByCamera = alg::bool2BOOL(args.field_bool("onlyUploadByCamera"));
                spFieldAttachment->SetOnlyUploadByCamera(bOnlyUploadByCamera);
            }
            
            break;
        }
        case Et_DbSheetField_Lookup:
        {
            hr = ConfigureLookupCondProps(pField, pDbOp, args);
            if (FAILED(hr))
                return hr;
            break;
        }
        case Et_DbSheetField_Address:
        {
            ks_stdptr<IDbField_Cascade> spFieldAddress = pField;
            if (args.has("addressLevel"))
            {
                UINT addressLevel = args.field_uint32("addressLevel");
                hr = spFieldAddress->SetCascadeLevel(addressLevel);
                if (FAILED(hr))
                    return hr;
            }
            if (args.has("withDetailedAddress"))
            {
                hr = spFieldAddress->SetWithDetailedInfo(args.field_bool("withDetailedAddress"));
                if (FAILED(hr))
                    return hr;
            }
            if(args.has("presetAddress"))
            {
                binary_wo::VarObj vPresetAddress = args.get_s("presetAddress");
                ASSERT(vPresetAddress.type() == binary_wo::typeStruct);
                binary_wo::VarObj vDistrictArray = vPresetAddress.get_s("districts");
                int presetAddressItemCount = vDistrictArray.arrayLength_s();
                std::vector<PCWSTR> districtPointers;
                districtPointers.reserve(presetAddressItemCount);
                for (UINT i = 0; i < presetAddressItemCount; ++i)
                    districtPointers.push_back(vDistrictArray.item_str(i));
                hr = spFieldAddress->SetDefValueItemList(districtPointers.data(), districtPointers.size());
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Cascade:
        {
            ks_stdptr<IDbField_Cascade> spCascadeAddress = pField;
            if (args.has("displayAllLevel"))
            {
                spCascadeAddress->SetDisplayAllLevel(args.field_bool("displayAllLevel"));
            }
            if (args.has("allCascadeOption"))
            {
                hr = spCascadeAddress->SetAllCascadeOption(args.get_s("allCascadeOption"));
                if (FAILED(hr))
                    return hr;
            }
            if (args.has("cascadeTitle"))
            {
                binary_wo::VarObj titleArray = args.get_s("cascadeTitle");
                int titleCount = titleArray.arrayLength_s();
                std::vector<PCWSTR> titleList;
                titleList.reserve(titleCount);
                for (UINT i = 0; i < titleCount; ++i)
                    titleList.push_back(titleArray.item_str(i));
                hr = spCascadeAddress->SetCascadeTitle(titleList.data(), titleList.size());
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_Department:
        {
            ks_stdptr<IDbField_Cascade> spFieldDepartment = pField;
            if (args.has("supportMulti"))
            {
                spFieldDepartment->SetMultiValue(args.field_bool("supportMulti"));
            }
            if (args.has("displayAllLevel"))
            {
                spFieldDepartment->SetDisplayAllLevel(args.field_bool("displayAllLevel"));
            }
            break;
        }
        case  Et_DbSheetField_LastModifiedTime:
        case  Et_DbSheetField_LastModifiedBy:
        {
            ks_stdptr<IDbField_WatchedField> spField = pField;
            if (args.has("watchedAll"))
            {
                spField->SetWatchedAll(args.field_bool("watchedAll"));
            }
            if (args.has("watchedField") && !spField->IsWatchedAll())
            {
               binary_wo::VarObj objVec = args.get("watchedField");
               std::vector<EtDbId> dbIds;
               for(int32 i = 0, count = objVec.arrayLength_s(); i < count; ++i)
               {
                    EtDbId id = INV_EtDbId;
                    pCtx->DecodeEtDbId(objVec.at(i).value_str(), &id);
                    dbIds.push_back(id);
               }
               spField->SetWatchedFieldId(dbIds.data(), dbIds.size());
            }
            break;
        }
        case Et_DbSheetField_Button:
        {
            ks_stdptr<IDbField_Button> spField = pField;
            if (args.has("backgroundColor"))
            {
                hr = spField->SetBackgroundColor(args.field_uint32("backgroundColor"));
                if (FAILED(hr))
                    return hr;
            }
            if (args.has("icon"))
            {
                hr = spField->SetButtonIcon(args.field_str("icon"));
                if (FAILED(hr))
                    return hr;
            }
            if (args.has("text"))
            {
                hr = spField->SetButtonText(args.field_str("text"));
                if (FAILED(hr))
                    return hr;
            }
            if (args.has("successText"))
            {
                hr = spField->SetSuccessText(args.field_str("successText"));
                if (FAILED(hr))
                    return hr;
            }
            if (args.has("textColor"))
            {
                hr = spField->SetTextColor(args.field_uint32("textColor"));
                if (FAILED(hr))
                    return hr;
            }
            break;
        }
        case Et_DbSheetField_BarCode:
        {
            ks_stdptr<IDbField_BarCode> spFieldBarCode= pField;
            if(args.has("onlyScanByCamera"))
            {
                BOOL bOnlyScanByCamera = alg::bool2BOOL(args.field_bool("onlyScanByCamera"));
                spFieldBarCode->SetOnlyScanByCamera(bOnlyScanByCamera);
            }
            break;
        }
        default:
            break;
    }

    // 字段默认值放在最后进行修改，避免出现数据不一致情况
    ET_DbSheet_Field_Default_Value_Type defaultValueType = DbSheet_Field_Dvt_Normal;
    if (args.has("defaultValType"))
        VS(_appcore_GainEncodeDecoder()->DecodeDbFieldDefaultValueType(args.field_str("defaultValType"), &defaultValueType));

    if (args.has("defaultVal"))
    {
        hr = pField->SetDefaultVal(defaultValueType, args.field_str("defaultVal"), bNewField);
        if (FAILED(hr))
            return hr;
    }
    else
    {
        IEtRevisionContext* pEtCtx = _etcore_GetEtRevisionContext();
        // 回放时不校验默认值
        if (pEtCtx && !pEtCtx->isExecDirect())
        {
            hr = pField->CheckDefaultValValid();
            if (FAILED(hr))
                return hr;
        }
    }

    if (bNewField)
    {
        //只有新建字段才在这里处理,而普通的字段修改场景，特别是修改字段类型，此时还没把字段非法的数据给清掉
        //若此时设置SetValueUnique里面会去判断此时字段是否含有重复值，而数据是清除非法之前的状态，不太准确。
        if(args.has("valueUnique"))
        {
            bool bValUniqueProp = args.field_bool("valueUnique");
            hr = pField->SetValueUnique(bValUniqueProp,FALSE);
            if(FAILED(hr))
            {
                return hr;
            }
        }

    }

    return S_OK;
}

HRESULT GetFieldValues(IDBSheetOp* pDbSheetOp, EtDbId fldId, ET_DbSheet_FieldType oldType, ET_DbSheet_FieldType newType, 
    GlobalSharedStringUnorderedSet& values)
{
    const IDBIds* rids = pDbSheetOp->GetAllRecords();
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    for (EtDbIdx i = 0, cnt = rids->Count(); i < cnt; ++i)
    {
        if (Et_DbSheetField_Contact == oldType)
        {
            const_token_ptr pToken = nullptr;
            HRESULT hr = pDbSheetOp->GetValueToken(rids->IdAt(i), fldId, &pToken);
            if (FAILED(hr) || nullptr == pToken)
                continue;
            ks_stdptr<IDbTokenArrayHandle> spHandleArray;
            hr = pCtx->GetContactTokenArray(pToken, &spHandleArray);
            if (FAILED(hr))
            {
                ASSERT(FALSE);
                continue;
            }
            UINT cnt = spHandleArray->GetCount();
            if (cnt == 0)
                continue;
            for (int i = 0; i < cnt; ++i)
            {
                ks_stdptr<IDbContactHandle> spContactHandle;
                hr = pCtx->GetContactTokenItem(spHandleArray.get(), i, &spContactHandle);
                if (FAILED(hr))
                    continue; 

                values.emplace(spContactHandle->GetNickname());
            }
            continue;
        }

        ks_bstr cellStr;
        HRESULT hr = pDbSheetOp->GetValueString(rids->IdAt(i), fldId, &cellStr);
        if (FAILED(hr))
            return hr;
        bool isEmpty = cellStr.empty();
        if (isEmpty && oldType != Et_DbSheetField_Rating)
            continue;

        PCWSTR str = cellStr.c_str();
        if (newType == Et_DbSheetField_MultipleSelect)
        {
            ks_stdptr<IDbSelectItemHandle> spTokenSelect;
            VS(_db_CreateObject(CLSID_KDbSelectItemHandle, IID_IDbSelectItemHandle, (void **)&spTokenSelect));

            spTokenSelect->CreateFromCellStr(str);
            for (UINT i = 0, spSelectItemsCnt = spTokenSelect->Count(); i < spSelectItemsCnt; ++i)
                values.emplace(spTokenSelect->ItemText(i));
            if (oldType == Et_DbSheetField_Rating && isEmpty && spTokenSelect->Count() == 0)
                values.emplace(__X("0"));
            values.erase(GlobalSharedString(__X("")));
        }
        else if (oldType == Et_DbSheetField_Rating && isEmpty)
            values.emplace(__X("0"));
        else
            values.emplace(str);
    }
    return S_OK;
}

static
HRESULT InsertDbSheetListObject(etoldapi::_Worksheet* pWorksheet, UINT nRow, UINT nCol)
{
	if (nRow == 0 || nCol == 0)
		return E_INVALIDARG;

	ISheet *pSheet = pWorksheet->GetSheet();
	IDX sheetIdx = alg::STREF_INV_SHEET;
	pSheet->GetIndex(&sheetIdx);
	BMP_PTR pBmp = pSheet->GetBMP();
	bool needAddRange = false;
	if (nRow == pBmp->cntRows)
	{
		--nRow;
		needAddRange = true;
	}
	RANGE rg(pBmp);
	rg.SetSheetFromTo(sheetIdx, sheetIdx);
	rg.SetRowFromTo(0, nRow);
	rg.SetColFromTo(0, nCol - 1);

	ks_stdptr<etoldapi::Range> spRange;
	HRESULT hr = pWorksheet->GetRangeByData(&rg, &spRange);
	if (FAILED(hr))
		return hr;

	ks_stdptr<ListObjects> spListObjects;
	hr = pWorksheet->get_ListObjects(&spListObjects);
	if (FAILED(hr))
		return hr;

	KComVariant varSource(spRange);
	ks_stdptr<ListObject> spListObject;
	hr = spListObjects->Add(xlSrcRange, varSource, KComVariant(), xlYes, KComVariant(), KComVariant(), &spListObject);
	if(FAILED(hr))
		return hr;

	// 设置listObject的sheetName
    ks_bstr bstrName;
    pWorksheet->get_Name(&bstrName);
    ks_stdptr<ICoreListObjects> spLists;
    pSheet->GetExtDataItem(edSheetListObjects, (IUnknown **)&spLists);
    ks_stdptr<ICoreListObject> spList;
    if (spLists && spLists->GetCount() > 0)
        spLists->GetItem(0, &spList);
    if (spList)
    {
        hr = spList->SetDisplayName(bstrName.c_str(), TRUE);
        if (FAILED(hr))
            return hr;
        PCWSTR pwszDisplayName = NULL;
        spList->GetDisplayName(&pwszDisplayName);
        hr = spList->SetName(pwszDisplayName, FALSE);
        if (FAILED(hr))
            return hr;
    }

	// 设置表格样式为空
	hr = spListObject->SetTableStyle(BTSI_NONE);
	if (FAILED(hr))
		return hr;

	hr = spListObject->put_ShowHeaders(VARIANT_FALSE);
	if (FAILED(hr))
		return hr;

	// 删除第一行，因为默认新建dbsheet的时候第一行默认是表名
	IBook *pBook = pSheet->LeakBook();
	IBookOp *pBookOp = pBook->LeakOperator();

	RANGE dltRg(rg);
	dltRg.SetRowFromTo(0);
	hr = pBookOp->RemoveRange(dltRg, dirUp);
	if (FAILED(hr))
		return hr;
	if (needAddRange)
	{
		hr = pBookOp->InsertRange(dltRg, dirDown);
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

static
HRESULT SetRowColPadding(ISheet *pSheet, INT nTopPadding, INT nBottomPadding, INT nLeftPadding, INT nRightPadding)
{
	pSheet->SetRowTopPadding(nTopPadding);
	pSheet->SetRowBottomPadding(nBottomPadding);
	pSheet->SetColLeftPadding(nLeftPadding);
	pSheet->SetColRightPadding(nRightPadding);

	IDX sheetIdx = alg::STREF_INV_SHEET;
	pSheet->GetIndex(&sheetIdx);
	RANGE sheetRg(pSheet->GetBMP());
	sheetRg.SetSheets(sheetIdx, sheetIdx);

	IBook* pBook = pSheet->LeakBook();
	IBookOp *pBookOp = pBook->LeakOperator();
	HRESULT hr = pBookOp->FitRowCol(&sheetRg, FALSE, FALSE, FALSE, FALSE, FALSE);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT ConfigureNewSheet(etoldapi::_Worksheet* pWorksheet, IDBProtectionJudgement* pProtectionJudgement, UINT nRow, UINT nCol, Database::SheetInitConfig config, const SyncSheetInitParam* syncParam)
{
	ISheet *pSheet = pWorksheet->GetSheet();
	// 设置sheet类型为dbSheet
	HRESULT hr = pSheet->UpdateSheetType(stGrid_DB);
	if (FAILED(hr))
		return hr;

	// 插入list object
    UINT nListObjectRowCnt = (nRow >= 1 ? nRow: 1);//确保listObject row数目至少为1
	hr = InsertDbSheetListObject(pWorksheet, nListObjectRowCnt, nCol);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	VS(DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp));
	if (syncParam && syncParam->syncType != DbSheet_St_None)
	{
		VS(spDbSheetOp->SetSheetSyncType(syncParam->syncType));
		if (syncParam->syncType == DbSheet_St_Cross_DB)
		{
			pSheet->SetVisible(ssVeryhidden);
			ks_stdptr<IUnknown> spUnknownSyncReleationMgr;
			pSheet->LeakBook()->GetExtDataItem(edBookDbSyncReleationManager, &spUnknownSyncReleationMgr);
			ks_stdptr<IDbSyncReleationManager> spDbSyncReleationMgr = spUnknownSyncReleationMgr;
			hr = spDbSyncReleationMgr->InitReleation(spDbSheetOp->GetSheetId(), &syncParam->info);
			if (FAILED(hr))
				return hr;
		}
	}
	DisableDbSheetProtectScope disPtScope(pProtectionJudgement, pSheet->GetStId());

	hr = spDbSheetOp->InitRecords(nRow);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDBSheetRange> spDbRange;
	hr = spDbSheetOp->InitFields(nCol, &spDbRange);
	if (FAILED(hr))
		return hr;

	// 默认第一个字段为关键字段
	IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();
	hr = pFieldsMgr->SetPrimaryField(spDbRange->GetFieldId(0));
	if (FAILED(hr))
		return hr;
	ET_DbSheet_Sync_Type syncType = spDbSheetOp->GetSheetSyncType();
    if (syncType == DbSheet_St_DB || syncType == DbSheet_St_GridSheet) 
	{
	    ks_stdptr<IDbField> spField;
		hr = pFieldsMgr->GetField(pFieldsMgr->GetPrimaryField(), &spField);
		if (FAILED(hr))
			return hr;
		spField->SetSyncField(true);
	}
	// 设置padding
	hr = SetRowColPadding(pSheet, config.nTopPadding, config.nBottomPadding, config.nLeftPadding, config.nRightPadding);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

bool ExceededMaxDueDateAutomationLimits(IDBSheetOp *pDbSheetOp, UINT limits)
{
    const IDBIds *pFields = pDbSheetOp->GetAllFields();
    IDbFieldsManager *pFieldsMgr = pDbSheetOp->GetFieldsManager();
    UINT dueDateCnt = 0;
    for (EtDbIdx fld = 0; fld < pFields->Count(); fld++)
    {
        EtDbId fldId = pFields->IdAt(fld);
        ks_stdptr<IDbField> spField;
        HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
        if (FAILED(hr) || spField->GetType() != Et_DbSheetField_Automations)
            continue;

        ks_stdptr<IDbField_Automations> spField_Automations = spField;
        UINT automationsCnt = spField_Automations->GetAutomationsCount();

        for (EtDbIdx i = 0; i < automationsCnt; i++)
        {
            ks_stdptr<IDbAutomation> spAutomation;
            hr = spField_Automations->GetAutomation(i, &spAutomation);
            if (FAILED(hr))
                continue;

            ks_stdptr<IDbAutomationTrigger> spAutomationTrigger;
            hr = spAutomation->GetTrigger(&spAutomationTrigger);
            if (FAILED(hr) || spAutomationTrigger->GetType() != AutomationTrigger_DueDate)
                continue;

            dueDateCnt++;
        }
    }

    return dueDateCnt > limits;
}

bool HasDueDateAutomation(IDBSheetOp *pDbSheetOp, IDBSheetRange *pRange)
{
	std::set<EtDbId> fldIdSet;
	for (int i = 0, cnt = pRange->GetFieldCnt(); i < cnt; i++)
		fldIdSet.insert(pRange->GetFieldId(i));
	const IDBIds *pFields = pDbSheetOp->GetAllFields();
	IDbFieldsManager *pFieldsMgr = pDbSheetOp->GetFieldsManager();
	for (EtDbIdx fld = 0; fld < pFields->Count(); fld++)
	{
		EtDbId fldId = pFields->IdAt(fld);
		ks_stdptr<IDbField> spField;
		HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr) || spField->GetType() != Et_DbSheetField_Automations)
			continue;

		ks_stdptr<IDbField_Automations> spField_Automations = spField;
		UINT automationsCnt = spField_Automations->GetAutomationsCount();

		for (EtDbIdx i = 0; i < automationsCnt; i++)
		{
			ks_stdptr<IDbAutomation> spAutomation;
			hr = spField_Automations->GetAutomation(i, &spAutomation);
			if (FAILED(hr))
				continue;

			ks_stdptr<IDbAutomationTrigger> spAutomationTrigger;
			hr = spAutomation->GetTrigger(&spAutomationTrigger);
			if (FAILED(hr) || spAutomationTrigger->GetType() != AutomationTrigger_DueDate)
				continue;

			ks_stdptr<IDbAutomationTrigger_DueDate> spAutomationTrigger_DueDate = spAutomationTrigger;
			if (fldIdSet.find(spAutomationTrigger_DueDate->GetDateField()) != fldIdSet.end())
				return true;

			UINT actionCnt = spAutomation->GetActionCount();
			for (EtDbIdx actionIdx = 0; actionIdx < actionCnt; actionIdx++)
			{
				ks_stdptr<IDbAutomationAction> spAutomationAction;
				hr = spAutomation->GetAction(actionIdx, &spAutomationAction);
				if (FAILED(hr) || spAutomationAction->GetType() != AutomationAction_SimpleMessage)
					continue;

				ks_stdptr<IDbAutomationAction_SimpleMessage> spAutomationAction_SimpleMessage = spAutomationAction;
				if (fldIdSet.find(spAutomationAction_SimpleMessage->GetActionField()) != fldIdSet.end())
					return true;
			}
		}
	}

	return false;
}

bool IsRelatedToDueDate(IDBSheetOp *pDbSheetOp, EtDbId fieldId)
{
    const IDBIds *pFields = pDbSheetOp->GetAllFields();
    IDbFieldsManager *pFieldsMgr = pDbSheetOp->GetFieldsManager();
    for (EtDbIdx fld = 0; fld < pFields->Count(); fld++)
    {
        EtDbId id = pFields->IdAt(fld);
        ks_stdptr<IDbField> spField;
        HRESULT hr = pFieldsMgr->GetField(id, &spField);
        if (FAILED(hr) || spField->GetType() != Et_DbSheetField_Automations)
            continue;

        ks_stdptr<IDbField_Automations> spField_Automations = spField;
        UINT automationsCnt = spField_Automations->GetAutomationsCount();
        for (EtDbIdx i = 0; i < automationsCnt; i++)
        {
            ks_stdptr<IDbAutomation> spAutomation;
            hr = spField_Automations->GetAutomation(i, &spAutomation);
            if (FAILED(hr))
                continue;

            ks_stdptr<IDbAutomationTrigger> spAutomationTrigger;
            hr = spAutomation->GetTrigger(&spAutomationTrigger);
            if (FAILED(hr) || spAutomationTrigger->GetType() != AutomationTrigger_DueDate)
                continue;

            ks_stdptr<IDbAutomationTrigger_DueDate> spAutomationTrigger_DueDate = spAutomationTrigger;
            if (spAutomationTrigger_DueDate->GetDateField() == fieldId)
                return true;

            UINT actionCnt = spAutomation->GetActionCount();
            for (EtDbIdx actionIdx = 0; actionIdx < actionCnt; actionIdx++)
            {
                ks_stdptr<IDbAutomationAction> spAutomationAction;
                hr = spAutomation->GetAction(actionIdx, &spAutomationAction);
                if (FAILED(hr) || spAutomationAction->GetType() != AutomationAction_SimpleMessage)
                    continue;

                ks_stdptr<IDbAutomationAction_SimpleMessage> spAutomationAction_SimpleMessage = spAutomationAction;
                if (spAutomationAction_SimpleMessage->GetActionField() == fieldId)
                    return true;
            }
        }
    }
    return false;
}

bool ExceededFieldCountLimits(IDBSheetView* pView, ET_DbSheet_FieldType type, UINT limits)
{
    IDbFieldsManager *pFieldsMgr = pView->GetFieldsManager();

    return pFieldsMgr->FieldCnt(type) > limits;
}

namespace {

HRESULT _configureField(IDBSheetView* pView, EtDbId fldId, ET_DbSheet_FieldType type, const VarObj& args)
{
    HRESULT hr = S_OK;

    ks_stdptr<IDbField> spField;
    IDbFieldsManager *pFieldsMgr = pView->GetSheetOp()->GetFieldsManager();
    hr = pFieldsMgr->GetField(fldId, &spField);
    if (FAILED(hr))
        return hr;

    //对于统计表sheet，不支持增加普通字段，仅支持增加 公式、单向关联(仅限自动关联)、引用 字段
    if(!CheckFieldTypeValidForStatDbSheet(pView->GetSheetOp()->GetBook(), pView->GetSheetOp()->GetSheetId(), type))
    {
        WOLOG_ERROR << "[_configureField] For statSheet, when _configureField the field type is unvalid!";
        return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
    }

	hr = ConfigureDbField(spField, args, type, true, pView);
    if (FAILED(hr))
        return hr;

    if (args.has("fieldWidth"))
    {
        INT width = args.field_int32("fieldWidth");
        if (pView->GetType() == et_DBSheetView_Grid)
        {
            ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = pView;
            if (spDbSheetViewGrid != nullptr)
            {
                hr = spDbSheetViewGrid->SetFieldWidth(fldId, width, TRUE);
                if (FAILED(hr))
                    return hr;
            }
        }
    }

    if (args.has("hidden"))
    {
        bool hidden = args.field_bool("hidden");
		hr = pView->SetFieldsHidden(fldId, hidden);
		if (FAILED(hr))
			return hr;
    }

    if (args.has("format"))
    {
        if (pView->GetType() != et_DBSheetView_Grid)
            return E_FAIL;
        binary_wo::VarObj format = args.get_s("format");
        ARGB bgColor = format.field_uint32("bgColor");
        ks_stdptr<IDBSheetView_Grid> spGridView = pView;
        hr = spGridView->SetFieldTitleFormat(fldId, bgColor);
        if (FAILED(hr))
			return hr;
    }
    return hr;
}

}

HRESULT AddField(IDBSheetView* pView, ET_DbSheet_FieldType type, const VarObj& args, IDBSheetRange **ppRange)
{
    HRESULT hr = S_OK;
    ks_stdptr<IWebhookManager> spWebhookMgr;
    IBook* pBook = pView->GetSheetOp()->GetBook();
    pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
    KDisableDbWebhook disableDbWebhook(pRange, DbWebhookMask_updateField);
    ks_stdptr<IDbCollector> spDbCollector;
    pBook->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
    KDisableDbCollector disableDbCollector(spDbCollector, DCM_Webhook, CDOM_UpdateField);
    ks_stdptr<IDBSheetRange> spDbRange;
    hr = pView->InsertFields(1, &spDbRange);
    if(FAILED(hr))
        return hr;
    EtDbId fldId = spDbRange->GetFieldId(0);
    hr = _configureField(pView, fldId, type, args);
    if (FAILED(hr))
        return hr;
    hr = OnAddField(pView, fldId, type, spDbRange);
    if (FAILED(hr))
        return hr;

    ks_stdptr<ISheet> spSheet;
    GetDbSheet(pBook, pView->GetSheetOp()->GetSheetId(), &spSheet);
    ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
    spSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
    if (spDBStatisticSheetData)
        spDBStatisticSheetData->AdjustInsertFields(spDbRange);

    if (ppRange)
    {
        *ppRange = spDbRange.detach();
    }
    
    return hr;
}

HRESULT AddFields(IDBSheetView* pView,  const VarObj& args, IDBSheetRange **ppRange)
{
    HRESULT hr = E_FAIL;
    if(args.type() != binary_wo::typeArray)
        return hr;
    if (args.arrayLength_s() == 0)
        return S_FALSE;

    ks_stdptr<IWebhookManager> spWebhookMgr;
    IBook* pBook = pView->GetSheetOp()->GetBook();
    pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    pView->GetSheetOp()->GetBook()->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
    IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
    KDisableDbWebhook disableDbWebhook(pRange, DbWebhookMask_updateField);
    ks_stdptr<IDbCollector> spDbCollector;
    pBook->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
    KDisableDbCollector disableDbCollector(spDbCollector, DCM_Webhook, CDOM_UpdateField);
    ks_stdptr<IDBSheetRange> spDbRange;
    int fieldCnt = args.arrayLength_s();
    hr = pView->InsertFields(fieldCnt, &spDbRange);
    if(FAILED(hr))
        return hr;

    ks_stdptr<ISheet> spSheet;
    GetDbSheet(pBook, pView->GetSheetOp()->GetSheetId(), &spSheet);
    ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
    spSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
    if (spDBStatisticSheetData)
        spDBStatisticSheetData->AdjustInsertFields(spDbRange);


    for (int idx = 0; idx < fieldCnt; ++idx)
    {
        EtDbId fldId = spDbRange->GetFieldId(idx);
	    WebStr fldTypeStr = nullptr;
        const VarObj& fldItem = args.at_s(idx);
        if (fldItem.has("type"))
        {
            fldTypeStr = fldItem.field_str("type");
        }
        ET_DbSheet_FieldType fldType = Et_DbSheetField_MultiLineText;
        if (fldTypeStr)
            _appcore_GainEncodeDecoder()->DecodeFieldType(fldTypeStr, &fldType);

        hr = _configureField(pView, fldId, fldType, fldItem);
        if (FAILED(hr))
            return hr;
        hr = OnAddField(pView, fldId, fldType, spDbRange);
        if (FAILED(hr))
            return hr;
    }
    
    if (ppRange)
        *ppRange = spDbRange.detach();

    return hr;
}

HRESULT OnAddField(IDBSheetView* pView, EtDbId fldId, ET_DbSheet_FieldType type, IDBSheetRange *pRange)
{
    HRESULT hr = S_OK;
    // 联系人、创建者创建时，会更加字段配置信息自动创建扩展字段，需要将创建的扩展字段一起加入range中, 在指定位置插入联系人字段时扩展字段要紧邻联系人
    if (type == Et_DbSheetField_CreatedBy || type == Et_DbSheetField_Contact)
    {
        if (!pView)
            return E_FAIL;
        ks_stdptr<IDbField> spField;
        IDbFieldsManager *pFieldsMgr = pView->GetSheetOp()->GetFieldsManager();
        hr = pFieldsMgr->GetField(fldId, &spField);
        if (FAILED(hr))
            return hr;
        std::vector<EtDbId> fldIds;
        DbSheet::GetDbContactExtendFields(spField, fldIds);
        if (pRange)
            std::for_each(fldIds.begin(), fldIds.end(), [pRange](EtDbId fldId){ pRange->AddFieldId(fldId); });
    }
    return hr;
}

bool CheckFieldTypeValidForStatDbSheet(IBook* pBook, UINT sheetStId, ET_DbSheet_FieldType type)
{
    if(!IsStatSheet(pBook, sheetStId))
    {
        return true;
    }

    //统计表不支持增加普通字段，仅支持增加 公式、单向关联(仅限自动关联 )、引用字段
    if(type == Et_DbSheetField_Formula || type == Et_DbSheetField_Lookup || type == Et_DbSheetField_OneWayLink)
    {
        return true;
    }
    
    return false;
}

bool hasSelectField(IDBSheetView* pView)
{
    IDbFieldsManager *pFieldsMgr = pView->GetFieldsManager();
    const IDBIds* pAllField = pView->GetAllFields();
    for (int i = 0; i < pAllField->Count(); ++i)
    {
        EtDbId fldId = pAllField->IdAt(i);
        ks_stdptr<IDbField> spField;
        pFieldsMgr->GetField(fldId, &spField);
        if (spField == nullptr)
            continue;
        ET_DbSheet_FieldType type = spField->GetType();
        if (type == Et_DbSheetField_SingleSelect) // 后面有需要再补上多选项
            return true;
    }
    return false;
}

void AddNecessarySelectField(IDBSheetViews* pViews, IDBSheetRange** ppRange, bool& isAddField, const VarObj& param, bool bUsedForApp)
{
    ET_DBSheet_ViewUseType useType = bUsedForApp ? Et_DBSheetViewUse_ForApp : Et_DBSheetViewUse_ForDb;
    ks_stdptr<IDBSheetView> spView;
    if (pViews->GetSize(useType) != 0)
        pViews->GetItemAt(0, useType, &spView);
    else
        spView = pViews->GetDefaultView();

    if (!hasSelectField(spView) && param.has("field"))
    {
        VarObj field = param.get_s("field");
        WebStr fldTypeStr = field.field_str("type");
        ET_DbSheet_FieldType fldType = Et_DbSheetField_MultiLineText;
        if (fldTypeStr)
            _appcore_GainEncodeDecoder()->DecodeFieldType(fldTypeStr, &fldType);
        ASSERT(fldType == Et_DbSheetField_SingleSelect);  // 后面有需要再补上多选项
        VarObj args = field.get_s("args");
        AddField(spView, fldType, args, ppRange);
        isAddField = true;
    }
}

HRESULT initDbSheet(const VarObj& param, _Worksheet* pWorksheet, IDBProtectionJudgement* pProtectionJudgement)
{
	Database::SheetInitConfig config = Database::Utils::ParseDbSheetInitConfig(param);
	ISheet *pSheet = pWorksheet->GetSheet();
    app_helper::KBatchUpdateCal buc(pSheet->LeakBook()->LeakOperator());

	if (param.has("syncType"))
	{
		ET_DbSheet_Sync_Type syncType = DbSheet_St_None;
		_appcore_GainEncodeDecoder()->DecodeDbSheetSyncType(param.field_str("syncType"), &syncType);
        ks_stdptr<IDBSheetOp> spDbSheetOp;
	    VS(DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp));
		HRESULT hr = spDbSheetOp->SetSheetSyncType(syncType);
		if (FAILED(hr))
			return hr;
	}

	// 设置sheet类型为dbSheet
	VarObj fields = param.get("fields");
    int nFields = fields.arrayLength_s();
	if (nFields == 0)
		return E_FAIL;

	HRESULT hr = DbSheet::ConfigureNewSheet(pWorksheet, pProtectionJudgement, config.nEmptyRows, nFields, config);
	if (FAILED(hr))
		return hr;

	// 注意：ConfigureNewSheet 会更新 sheetStId
	DbSheet::DisableDbSheetProtectScope disPtScope(pProtectionJudgement, pSheet->GetStId());

	// 添加视图
	ks_stdptr<IDBSheetViews> spDbSheetViews;
    VS(GetDBSheetViews(pSheet, &spDbSheetViews));
	ks_stdptr<IDBSheetView> spDbSheetView;
    KSheet_Book_Version bookVersion = KSheet_Book_Version_Base;
	ks_stdptr<IUnknown> spUnk;
	pSheet->LeakBook()->GetExtDataItem(edFileVersion, &spUnk);
	ks_stdptr<IKEtFileVersion> spEtFileVersion = spUnk;
	if (spEtFileVersion)
		bookVersion = spEtFileVersion->GetKSheetBookVersion();
    if (pSheet->GetBMP()->bKsheet && bookVersion >= KSheet_Book_Version_AppSheet)
    {
        spDbSheetView = spDbSheetViews->GetDefaultView();
        hr = configureDbSheet(spDbSheetView, param, config.rowHeight);
        if (FAILED(hr))
            return E_FAIL;
    }
    else
    {
        // 支持传入视图配置，创建数据表时生成多个视图
        if(config.bHasViewConfigs)
        {
            VarObj initConfigObj = param.get_s("initConfig");
            VarObj viewConfigsObj = initConfigObj.get_s("viewConfigs");
            bool fieldInitialized = false;
            for(int i = 0; i < viewConfigsObj.arrayLength_s(); i++)
            {
                VarObj viewConfig = viewConfigsObj.at(i);
                WebStr name = viewConfig.field_str("name");
                IEncodeDecoder* pEncodeDecoder = _appcore_GainEncodeDecoder();
                ET_DBSheet_ViewType viewType = DbSheet::ConvertViewType(pEncodeDecoder, viewConfig.field_str("type"));
                
                // 收集表功能需求 先支持传入表格视图、表单视图
                if(viewType != et_DBSheetView_Grid && viewType != et_DBSheetView_Form)
                    continue;

                ks_stdptr<IDBSheetView> spDbSheetView;
                hr = spDbSheetViews->CreateView(viewType, name, false, Et_DBSheetViewUse_ForDb, &spDbSheetView);
                if (FAILED(hr))
                    return E_FAIL;

                INT rowHeight = config.rowHeight;
                if(viewConfig.has("rowHeight"))
                    rowHeight = viewConfig.field_int32("rowHeight");

                hr = configureDbSheet(spDbSheetView, param, rowHeight, fieldInitialized);
                if (FAILED(hr))
                    return E_FAIL;
                
                fieldInitialized = true;
            }
        }
        else    // 默认创建表格视图，旧代码逻辑
        {
            hr = spDbSheetViews->CreateView(et_DBSheetView_Grid, config.strViewName, false, Et_DBSheetViewUse_ForDb, &spDbSheetView);
            if (FAILED(hr))
                return E_FAIL;
            
            hr = configureDbSheet(spDbSheetView, param, config.rowHeight);
            if (FAILED(hr))
                return E_FAIL;
         }
    }

	return S_OK;
}

HRESULT configureDbSheet(IDBSheetView* pDbSheetView, const VarObj& param, INT rowHeight, bool fieldInitialized)
{
    HRESULT hr = initDbSheetViewRowHeight(pDbSheetView, rowHeight);
    if (FAILED(hr))
        return E_FAIL;

    hr = initDbSheetFields(pDbSheetView, param, fieldInitialized);
    if (FAILED(hr))
        return E_FAIL;

    hr = initDbSheetValues(pDbSheetView, param);
    if (FAILED(hr))
        return E_FAIL;
    
    return S_OK;
}

HRESULT initDbSheetFields(IDBSheetView* pDbSheetView, const VarObj& param, bool fieldInitialized)
{
    bool dispSumForNumberAndCurrencyInStatusBar = false;
    if (param.has("dispSumForNumberAndCurrencyInStatusBar"))
        dispSumForNumberAndCurrencyInStatusBar = true;

    VarObj fields = param.get("fields");
    IDbFieldsManager *pFieldsMgr = pDbSheetView->GetFieldsManager();
	const IDBIds *pFields = pDbSheetView->GetAllFields();
	for (int i = 0; i < fields.arrayLength_s(); i++)
	{
		VarObj item = fields.at(i);
		VarObj args = item.get_s("args");
		ks_stdptr<IDbField> spField;
		EtDbId fldId = pFields->IdAt(i);
		HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return E_FAIL;

		ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
		if (item.has("fieldType"))
			_appcore_GainEncodeDecoder()->DecodeFieldType(item.field_str("fieldType"), &type);

        if(!fieldInitialized)   // 创建收集表时同时创建多个视图,字段配置一次就行,视图公用
        {
            hr = DbSheet::ConfigureDbField(spField, args, type, false, pDbSheetView);
            if (FAILED(hr))
                return E_FAIL;
        }

        if (dispSumForNumberAndCurrencyInStatusBar &&
            (type == Et_DbSheetField_Number || type == Et_DbSheetField_Currency))
        {
            hr = pDbSheetView->GetMutableStatisticOptions()->SetStatistic(fldId, DBSSO_sum);
			if(FAILED(hr))
				return hr;
        }

		if (args.has("fieldWidth"))
		{
			INT width = args.field_int32("fieldWidth");
			if(pDbSheetView->GetType() == et_DBSheetView_Grid)
			{
				ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = pDbSheetView;
				if (spDbSheetViewGrid != nullptr)
				{
					hr = spDbSheetViewGrid->SetFieldWidth(fldId, width, TRUE);
					if (FAILED(hr))
						return E_FAIL;
				}
			}
		}
	}

    return S_OK;
}

HRESULT initDbSheetViewRowHeight(IDBSheetView* pDbSheetView, INT rowHeight)
{
    ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = pDbSheetView;
    if (spDbSheetViewGrid)
    {
        HRESULT hr = spDbSheetViewGrid->SetRecordsHeight(rowHeight);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT initDbSheetValues(IDBSheetView* pDbSheetView, const VarObj& param)
{
    if (param.has("values"))
	{
		const IDBIds *pAllRec = pDbSheetView->GetAllRecords();
		const IDBIds *pAllFld = pDbSheetView->GetAllFields();
		VarObj values = param.get_s("values");
		for(int r = 0; r < values.arrayLength_s(); r++)
		{
			VarObj row = values.at(r);
			for(int c = 0; c < row.arrayLength_s(); c++)
			{
				PCWSTR value = row.item_str(c);
				EtDbId recId = pAllRec->IdAt(r);
				if (recId == INV_EtDbId)
					return E_FAIL;
				EtDbId fldId = pAllFld->IdAt(c);
				if (fldId == INV_EtDbId)
					return E_FAIL;

				pDbSheetView->SetValue(recId, fldId, value);
			}
		}
	}
    return S_OK;
}

HRESULT initDbSyncSheet(_Worksheet* pWorksheet, IDBProtectionJudgement* pProtectionJudgement, const Database::SheetInitConfig& config, const SyncSheetInitParam* param, IDBSheetView** ppView)
{
    if (param->syncType != DbSheet_St_DB && param->syncType != DbSheet_St_Cross_DB
        && param->syncType != DbSheet_St_Merge_DB && param->syncType != DbSheet_St_GridSheet)
		return E_INVALIDARG;

	ISheet* pSheet = pWorksheet->GetSheet();
	app_helper::KBatchUpdateCal buc(pSheet->LeakBook()->LeakOperator());
	HRESULT hr = DbSheet::ConfigureNewSheet(pWorksheet, pProtectionJudgement, config.nEmptyRows, 1, config, param);
	if (FAILED(hr))
		return hr;
	DbSheet::DisableDbSheetProtectScope disPtScope(pProtectionJudgement, pSheet->GetStId());
	// 添加视图
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	ks_stdptr<IDBSheetView> spDbSheetView;
	VS(GetDBSheetViews(pSheet, &spDbSheetViews));
	hr = spDbSheetViews->CreateView(et_DBSheetView_Grid, config.strViewName, false, Et_DBSheetViewUse_ForDb, &spDbSheetView);
	if (FAILED(hr))
		return hr;
	if (ppView)
		*ppView = spDbSheetView.detach();

	return S_OK;
}

HRESULT setDbSyncSheet(const VarObj& param, _Worksheet* pWorksheet, IDBProtectionJudgement* pProtectionJudgement,
	IDBSheetView* pView, std::unordered_map<EtDbId, EtDbId>* pFieldMap, bool bNewLookupConvert)
{
	if (!pFieldMap)
		return E_FAIL;
	ISheet* pSheet = pWorksheet->GetSheet();
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	app_helper::KBatchUpdateCal buc(pSheet->LeakBook()->LeakOperator());
    DbSheet::DisableDbSheetProtectScope disPtScope(pProtectionJudgement, pSheet->GetStId());
	VarObj fields = param.get_s("fields");
	int nFields = fields.arrayLength_s();
	if (nFields == 0)
		return E_INVALIDARG;
	EtDbId syncFieldSourceId = INV_EtDbId;
	if (param.has("syncFieldSourceId"))
		dbctx->DecodeEtDbId(param.field_str("syncFieldSourceId"), &syncFieldSourceId);

	EtDbId primaryFieldId = INV_EtDbId;
	if (param.has("primaryFieldId"))
		dbctx->DecodeEtDbId(param.field_str("primaryFieldId"), &primaryFieldId);

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	VS(DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp));
	const IDBIds* pFields = spDbSheetOp->GetAllFields();
	EtDbIdx fldCnt = pFields->Count();
	HRESULT hr = S_OK;
	if (nFields > fldCnt)
	{
		hr = spDbSheetOp->InsertFields(nFields - fldCnt);
		if (FAILED(hr))
			return hr;
	}
	ks_stdptr<IDBSheetRange> spRemoveRange;
	spDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetRecordIds(spDbSheetOp->GetAllRecords());
	IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();
	IEncodeDecoder* pEncodeDecoder = _appcore_GainEncodeDecoder();
	pFieldMap->clear();

    // 必须先设置主键列，再配置字段，否则传入第一个字段是不支持作为主键列的字段会创建失败
    for (int i = 0; i < nFields; ++i)
    {
		VarObj item = fields.at(i);

		EtDbId srcFldId = INV_EtDbId;
        bool bHasSrcFldId = item.has("id");
        if (bHasSrcFldId)
        {
            VAR_OBJ_EXPECT_STRING(item, "id")
            dbctx->DecodeEtDbId(item.field_str("id"), &srcFldId);
            if (srcFldId == INV_EtDbId)
                return E_INVALIDARG;
        }
		ks_stdptr<IDbField> spField;
		EtDbId fldId = pFields->IdAt(i);
		hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;
		ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
		if (item.has("fieldType"))
		{
			VAR_OBJ_EXPECT_STRING(item, "fieldType")
			pEncodeDecoder->DecodeFieldType(item.field_str("fieldType"), &type);
		}
		if ((bHasSrcFldId && srcFldId == syncFieldSourceId) || !DBSyncHelper::isValidType(type))
		{
			spRemoveRange->AddFieldId(fldId);
			continue;
		}
        if (bHasSrcFldId)
		    pFieldMap->emplace(srcFldId, fldId);
    }

	if (primaryFieldId != INV_EtDbId)
	{
		auto it = pFieldMap->find(primaryFieldId);
		if(it != pFieldMap->end())
		{
			hr = pFieldsMgr->SetPrimaryField(it->second);
			if (FAILED(hr))
				return hr;
		}
	}

	for (int i = 0; i < nFields; ++i)
	{
		VarObj item = fields.at(i);
        EtDbId srcFldId = INV_EtDbId;
        bool bHasSrcFldId = item.has("id");
        if (bHasSrcFldId)
        {
            VAR_OBJ_EXPECT_STRING(item, "id")
            dbctx->DecodeEtDbId(item.field_str("id"), &srcFldId);
            if (srcFldId == INV_EtDbId)
                return E_INVALIDARG;
        }

		VarObj args = item.get_s("args");
		ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
		if (item.has("fieldType"))
		{
			VAR_OBJ_EXPECT_STRING(item, "fieldType")
			pEncodeDecoder->DecodeFieldType(item.field_str("fieldType"), &type);
		}
		if ((bHasSrcFldId && srcFldId == syncFieldSourceId) || !DBSyncHelper::isValidType(type))
			continue;
        EtDbId fldId = INV_EtDbId;
		if (bHasSrcFldId)
        {
            auto iter = pFieldMap->find(srcFldId);
            if (iter == pFieldMap->end())
                return E_INVALIDARG;
		    fldId = iter->second;
        }
		else
            fldId = pFields->IdAt(i);
        if (INV_EtDbId == fldId)
            return E_INVALIDARG;
        ks_stdptr<IDbField> spField;
		hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;
        ET_DbSheet_Sync_Type syncType = spDbSheetOp->GetSheetSyncType();
		if(syncType == DbSheet_St_DB || syncType == DbSheet_St_GridSheet)
			spField->SetSyncField(true);

		if(args.has("fieldWidth"))
		{
			if (pView && pView->GetType() == et_DBSheetView_Grid)
			{
				ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = pView;
				if (spDbSheetViewGrid)
				{
					hr = spDbSheetViewGrid->SetFieldWidth(fldId, args.field_int32("fieldWidth"), TRUE);
					if (FAILED(hr))
						return hr;
				}
			}
		}

		// 设置字段的arraySupport属性。保证前端绘制图标正确
		if (bNewLookupConvert && item.has("arraySupport"))
			if (type == Et_DbSheetField_Lookup)
				// 如果是引用字段，就直接设置为arraySupport属性为TRUE
				spField->SetArray(TRUE);
			else
				spField->SetArray(alg::bool2BOOL(item.field_bool("arraySupport")));

		spField->SetSyncField(TRUE);

		switch (type)
		{
			case Et_DbSheetField_Lookup:
			case Et_DbSheetField_AutoNumber:
			case Et_DbSheetField_Link:
			case Et_DbSheetField_OneWayLink:
				hr = spField->SetType(Et_DbSheetField_MultiLineText, nullptr);
				if (FAILED(hr))
					return hr;
				break;
			case Et_DbSheetField_CreatedBy:
			case Et_DbSheetField_LastModifiedBy:
				hr = spField->SetType(Et_DbSheetField_Contact, nullptr);
				if (FAILED(hr))
					return hr;
				break;
			case Et_DbSheetField_CreatedTime:
			case Et_DbSheetField_LastModifiedTime:
				hr = spField->SetType(Et_DbSheetField_Date, nullptr);
				if (FAILED(hr))
					return hr;
				if (args.has("numberFormat"))
				{
					VAR_OBJ_EXPECT_STRING(args, "numberFormat")
					hr = spField->SetNumberFormatForIOAndDiffuse(args.field_str("numberFormat"));
					if (FAILED(hr))
						return hr;
				}
				break;
			default:
				hr = DbSheet::ConfigureDbField(spField, args, type, false, pView, false, TRUE);
				if (FAILED(hr))
					return hr;
				continue;
		}
		VAR_OBJ_EXPECT_STRING(args, "fieldName")
		hr = spField->SetName(args.field_str("fieldName"), TRUE, TRUE);
		if (FAILED(hr))
			return hr;
		if (args.has("description"))
		{
			VAR_OBJ_EXPECT_STRING(args, "description")
			hr = spField->SetDescription(args.field_str("description"));
			if (FAILED(hr))
				return hr;
		}
	}

	for (int i = nFields; i < fldCnt; ++i)
		spRemoveRange->AddFieldId(pFields->IdAt(i));
	if (spRemoveRange->GetFieldCnt() > 0)
	{
		hr = spDbSheetOp->RemoveFields(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

int GetDBDashBoardSheetLimit()
{
	constexpr int maxDashBoardSheet = 30;
	return maxDashBoardSheet;
}

int GetDBFpsheetLimit()
{
	constexpr int maxFlexPaperSheetCnt = 20;
	return maxFlexPaperSheetCnt;
}

HRESULT copyDbUsersManager(IBook* pSrcBook, IBook* pTarBook)
{
	if (!pSrcBook || !pTarBook)
		return E_FAIL;
	BMP_PTR pSrcBmp = pSrcBook->GetBMP();
	if (!pSrcBmp->bDbSheet && !pSrcBmp->bKsheet)
		return S_OK;

	ks_stdptr<IUnknown> spSrcUnknown;
	pSrcBook->GetExtDataItem(edBookDbUserManager, &spSrcUnknown);
	ks_stdptr<IDbUsersManager> spSrcUsersMgr = spSrcUnknown;
	if (!spSrcUsersMgr)
		return E_FAIL;
	ks_stdptr<IUnknown> spTarUnknown;
	pTarBook->GetExtDataItem(edBookDbUserManager, &spTarUnknown);
	ks_stdptr<IDbUsersManager> spTarUsersMgr = spTarUnknown;
	if (!spTarUsersMgr)
		return E_FAIL;

	return spTarUsersMgr->Append(spSrcUsersMgr);
}

HRESULT GetDbSheet(IBook* pBook, UINT sheetStId, ISheet** ppSheet)
{
    IDX sheetIdx = INVALIDIDX;
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_DBSHEET_SHEET_NOT_FOUND;

    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(sheetIdx, &spSheet);
    if (nullptr == spSheet)
        return E_DBSHEET_SHEET_NOT_FOUND;
	if (!spSheet->IsDbSheet())
		return E_KSHEET_NOT_DBSHEET;

    *ppSheet = spSheet.detach();
    return S_OK;
}

HRESULT GetDbDashBoardSheet(IBook* pBook, UINT sheetStId, ISheet** ppSheet)
{
	IDX sheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_DBSHEET_SHEET_NOT_FOUND;

	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet || !spSheet->IsDbDashBoardSheet())
		return E_DBSHEET_SHEET_NOT_FOUND;

	*ppSheet = spSheet.detach();
	return S_OK;
}

HRESULT GetDBSheetViews(IBook* pBook, UINT sheetStId, IDBSheetViews** ppIDbSheetViews)
{
    ks_stdptr<ISheet> spSheet;
    HRESULT hr = GetDbSheet(pBook, sheetStId, &spSheet);
    if (FAILED(hr))
        return hr;

    return GetDBSheetViews(spSheet.get(), ppIDbSheetViews);
}

HRESULT GetDBSheetViews(ISheet* pSheet, IDBSheetViews** ppIDbSheetViews)
{
    ASSERT(pSheet);
    if (nullptr == pSheet)
        return E_DBSHEET_SHEET_NOT_FOUND;
    if (FALSE == pSheet->IsDbSheet())
        return E_KSHEET_NOT_DBSHEET;
    ASSERT(ppIDbSheetViews);

    ks_stdptr<IDBSheetViews> spDbSheetViews;
    pSheet->GetExtDataItem(edSheetDbViews, (IUnknown**)&spDbSheetViews);
    if (spDbSheetViews == nullptr)
        return E_DBSHEET_SHEET_NOT_FOUND;

    if (ppIDbSheetViews)
        *ppIDbSheetViews = spDbSheetViews.detach();
    return S_OK;
}

HRESULT GetDBSheetView(IBook* pBook, UINT sheetStId, EtDbId viewId
    , IDBSheetView** ppIDbSheetView, IDBSheetViews** ppIDbSheetViews)
{
    ASSERT(ppIDbSheetView);

    ks_stdptr<IDBSheetViews> spDbSheetViews;
    HRESULT hr = GetDBSheetViews(pBook, sheetStId, &spDbSheetViews);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDBSheetView> spDbSheetView;
    spDbSheetViews->GetItemById(viewId, &spDbSheetView);
    if (spDbSheetView == nullptr)
        return E_DBSHEET_VIEW_NOT_FOUND;

    *ppIDbSheetView = spDbSheetView.detach();
    if (ppIDbSheetViews)
        *ppIDbSheetViews = spDbSheetViews.detach();
    return S_OK;
}

HRESULT GetDBChartStatisticMgr(ISheet* pSheet, IDBChartStatisticMgr** ppChartStaticMgr)
{
	if (!pSheet || !pSheet->IsDbDashBoardSheet())
		return E_DBSHEET_SHEET_NOT_FOUND;

	ks_stdptr<IUnknown> spUnknown;
	pSheet->GetExtDataItem(edDbSheetChartDataStatisticMgr, &spUnknown);
	ks_stdptr<IDBChartStatisticMgr> spDbChartStaticMgr = spUnknown;
	if (!spDbChartStaticMgr)
		return E_DBSHEET_SHEET_NOT_FOUND;

	if (ppChartStaticMgr)
		*ppChartStaticMgr = spDbChartStaticMgr.detach();
	return S_OK;
}

HRESULT GetDBSheetOp(IBook* pBook, UINT sheetStId, IDBSheetOp** ppDbSheetOp)
{
    ASSERT(ppDbSheetOp);

    ks_stdptr<ISheet> spSheet;
    HRESULT hr = GetDbSheet(pBook, sheetStId, &spSheet);
    if (FAILED(hr))
        return hr;

    return GetDBSheetOp(spSheet.get(), ppDbSheetOp);
}

HRESULT GetDBSheetOp(ISheet* pSheet, IDBSheetOp** ppDbSheetOp)
{
    ASSERT(pSheet);
    if (nullptr == pSheet)
        return E_DBSHEET_SHEET_NOT_FOUND;
    if (FALSE == pSheet->IsDbSheet())
        return E_KSHEET_NOT_DBSHEET;
    ASSERT(ppDbSheetOp);

    ks_stdptr<IDBSheetOp> spDbSheetOp;
    pSheet->GetExtDataItem(edSheetDbOp, (IUnknown**)&spDbSheetOp);
    if (nullptr == spDbSheetOp)
        return E_KSHEET_NOT_DBSHEET;

    if (ppDbSheetOp)
        *ppDbSheetOp = spDbSheetOp.detach();
    return S_OK;
}

HRESULT GetDBDashBoardOp(ISheet* pSheet, IDBDashBoardDataOp** ppDbDashBoardOp)
{
	if (!ppDbDashBoardOp)
		return E_INVALIDARG;
	if (!pSheet || !pSheet->IsDbDashBoardSheet())
		return E_FAIL;

	ks_stdptr<IUnknown> spUnknown;
	pSheet->GetExtDataItem(edSheetDbDashBoardOp, &spUnknown);
	if (!spUnknown)
		return E_FAIL;
	ks_stdptr<IDBDashBoardDataOp> spDbSheetOp = spUnknown;
	if (!spDbSheetOp)
		return E_FAIL;

	*ppDbDashBoardOp = spDbSheetOp.detach();
	return S_OK;
}

HRESULT GetDBFpSheetOp(ISheet* pSheet, IFPSheetData** ppFPSheetOp)
{
	if (!ppFPSheetOp)
		return E_INVALIDARG;
	if (!pSheet || !pSheet->IsFpSheet())
		return E_FAIL;

	ks_stdptr<IUnknown> spUnknown;
	pSheet->GetExtDataItem(edSheetFpData, &spUnknown);
	if (!spUnknown)
		return E_FAIL;
	ks_stdptr<IFPSheetData> spFpSheetOp = spUnknown;

	*ppFPSheetOp = spFpSheetOp.detach();
	return S_OK;
}

// todo: et_dbsheet_task_class.cpp 及其他地方, 日后于bugfix用此函数改写并测试(重名, 视图名很长等case)
// 函数不适合批处理的情形(n^2复杂度)
HRESULT GenerateValidDbsheetViewName(IDBSheetViews* pViews, WebStr& baseName, ks_wstring& nameStr)
{
    int postfix = 2, nameLength = nameStr.length();
    do
    {
        if (0 == postfix)
            return E_DBSHEET_VIEW_INVALID_NAME;
        ks_wstring strPostFix;
        strPostFix.Format(__X("%d"), postfix);
        if (strPostFix.length() >= MAX_SHEET_NAME_CCH)
        {
            return E_FAIL;
        }
        if (strPostFix.length() + nameLength > MAX_SHEET_NAME_CCH)
        {
            nameStr.replace(MAX_SHEET_NAME_CCH - strPostFix.length(),
                nameLength - (MAX_SHEET_NAME_CCH - strPostFix.length()),
                strPostFix);
            ASSERT(nameStr.length() == MAX_SHEET_NAME_CCH);
        }
        else
        {
            nameStr.replace(nameLength, strPostFix.length(), strPostFix);
            ASSERT(nameStr.length() <= MAX_SHEET_NAME_CCH);
        }
        postfix++;
    } while (E_DBSHEET_VIEW_NAME_CONFLICT == pViews->IsValidViewName(nameStr.c_str()));
    baseName = nameStr.c_str();
    return S_OK;
}

ET_DBSheet_ViewType ConvertViewType(IEncodeDecoder* pEncodeDecoder, WebStr typeStr)
{
    // 兼容老代码
	if (xstrcmp(__X("vtGrid"), typeStr) == 0)
		return et_DBSheetView_Grid;
	if (xstrcmp(__X("vtKanban"), typeStr) == 0)
		return et_DBSheetView_Kanban;

	ET_DBSheet_ViewType type = et_DBSheetView_Grid;
	VS(pEncodeDecoder->DecodeViewType(typeStr, &type));
	return type;
}

EtDbId GetEtDbId(const binary_wo::VarObj& obj, WebName name) 
{
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	PCWSTR strId = obj.field_str(name);
	EtDbId id = INV_EtDbId;
	HRESULT hr = dbctx->DecodeEtDbId(strId, &id);
	if(FAILED(hr))
		ks_throw_s(E_INVALIDARG);

	return id;
}

EtDbId GetEtDbId(binary_wo::VarObj obj)
{
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	PCWSTR strId = obj.value_str();
	EtDbId id = INV_EtDbId;
	HRESULT hr = dbctx->DecodeEtDbId(strId, &id);
	if(FAILED(hr))
		ks_throw_s(E_INVALIDARG);

	return id;
}

void ParseUnsupportedAttachmentFileSuffixTypes(const binary_wo::VarObj& param,
                                                          std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes)
{
	binary_wo::VarObj unsupportedAttachmentFileSuffixTypesObj = param.get_s("unsupportedAttachmentFileSuffixTypes");
	int cnt = unsupportedAttachmentFileSuffixTypesObj.arrayLength_s();
    for (int i = 0; i < cnt; ++i)
    {
        PCWSTR unsupportedAttachmentFileSuffixType = unsupportedAttachmentFileSuffixTypesObj.at_s(i).value_str();
		unsupportedAttachmentFileSuffixTypes.insert(unsupportedAttachmentFileSuffixType);
    }
}

HRESULT filtrateByExtraFilter(IDbExtraFilter* pFilter, const binary_wo::VarObj& filter,
	DBSheetCommonHelper& commonHelper, IDBSheetOp* pData, bool bPreferId, OUT const IDbExtraFilter** ppTopFilter)
{
	using binary_wo::VarObj;
	// 遍历json并创建相应的filter
	// 使用栈模拟递归. 完成分析的节点不入栈, 未完成分析的节点入栈, 留待完成分析后出栈
	// 非叶子节点, 创造并添加相应的子filter, 将子filter的指针及对应的参数入栈, 完成分析;
	// 叶子节点, 完成criteria的分析与创建
	std::stack<IDbExtraFilter*> filterStack;
	filterStack.push(pFilter);
	std::stack<VarObj> jsonStack;
	jsonStack.push(filter);
	HRESULT hr = S_OK;
	bool validFilter = false;

	while (not filterStack.empty())
	{
		// 分析参数
		IDbExtraFilter* pTopFilter = filterStack.top();
		VarObj param = jsonStack.top();
		filterStack.pop();
		jsonStack.pop();
		*ppTopFilter = pTopFilter;

		KDbFilterOpType op = DBFOT_And;
		if (param.has("mode"))
		{
			hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterOpType(param.field_str("mode"), &op);
			if (FAILED(hr))
				return E_DBSHEET_FILTER_INVALID;
		}
		if (op != DBFOT_And && op != DBFOT_Or)
			return E_DBSHEET_FILTER_INVALID;
		hr = pTopFilter->SetOperator(op);

		// 叶子节点
		if (param.has("criteria"))
		{
			validFilter = true;
			if (param.has("filters"))
				return E_DBSHEET_FILTER_INVALID;
			VarObj criteria = param.get_s("criteria");
			if (binary_wo::typeArray != criteria.type())
				return E_DBSHEET_FILTER_INVALID;
			IDbFieldsManager* pFieldsMgr = pData->GetFieldsManager();
			for (int32 i = 0, cnt = criteria.arrayLength(); i < cnt; ++i)
			{
				VarObj criterion = criteria.at(i);
				if (binary_wo::typeStruct != criterion.type())
					return E_DBSHEET_FILTER_INVALID;
				EtDbId fldId = INV_EtDbId;
				if (criterion.has("fieldId")) // 兼容一下方便其他场景复用
				    fldId = commonHelper.GetEtDbId(criterion, "fieldId");
				else if (false == criterion.has("field"))
					return E_DBSHEET_FILTER_INVALID;
				else if (bPreferId)
				{
					fldId = commonHelper.GetEtDbId(criterion, "field");
				}
				else
				{
					ks_stdptr<IDbField> spField;
					hr = pFieldsMgr->FindField(criterion.field_str("field"), &spField);
					if (FAILED(hr))
						return E_DBSHEET_FILTER_INVALID;
					fldId = spField->GetID();
				}
				ks_stdptr<IDbFieldFilter> spFieldFilter;
				hr = pTopFilter->AddFieldFilter(fldId, &spFieldFilter);
				if (FAILED(hr))
					return E_DBSHEET_FILTER_INVALID;

				ks_stdptr<IDbFilterCriteria> spCriteria;
				hr = CreateDbFilterCriteria(criterion, pData->GetBook(), &spCriteria, true);
				if (FAILED(hr))
					return E_DBSHEET_FILTER_INVALID;

				hr = spFieldFilter->SetCriteria(spCriteria);
				if (FAILED(hr))
					return E_DBSHEET_FILTER_INVALID;
			}
		}
		// 非叶子节点
		if (param.has("filters"))
		{
			validFilter = true;
			if (param.has("criteria"))
				return E_DBSHEET_FILTER_INVALID;
			VarObj filters = param.get_s("filters");
			if (binary_wo::typeArray != filters.type())
				return E_DBSHEET_FILTER_INVALID;
			for (int32 i = 0, cnt = filters.arrayLength(); i < cnt; ++i)
			{
				ks_stdptr<IDbExtraFilter> itemFilter;
				VS(pTopFilter->AddExtraFilter(&itemFilter));
				filterStack.push(itemFilter.get());
				jsonStack.push(filters.at_s(i));
			}
		}
	}
	*ppTopFilter = nullptr;
	return validFilter ? S_OK : S_FALSE;
}

HRESULT RemoveFilterByFieldId(IDbFilter* pFilter, EtDbId fieldId, IDbFieldFilter* pExceptFieldFilter)
{
    if(!pFilter)
        return E_FAIL;

    for (UINT i = 0; i < pFilter->GetFiltersCount(); i++)
	{
		ks_stdptr<IDbFieldFilter> spFieldFilter;
		pFilter->GetFilter(i, &spFieldFilter);
		if(spFieldFilter && spFieldFilter->GetFieldId() == fieldId)
		{
            if(pExceptFieldFilter == spFieldFilter.get())
                continue;

			HRESULT hr = spFieldFilter->Remove();
            if (FAILED(hr))
                return hr;

			i--;
		}
	}
    return S_OK;
}

HRESULT GenerateExtraSort(IDBSheetOp* pData, DBSheetCommonHelper& commonHelper,
    const binary_wo::VarObj& sort, OUT IDbExtraSort** ppSort)
{
    ks_stdptr<IDbExtraSort> spExtraSort;
    VS(_appcore_CreateObject(CLSID_KDbExtraSort, IID_IDbExtraSort, (void**)&spExtraSort));
    spExtraSort->Init(pData);
    if (sort.has("conditions"))
    {
        VAR_OBJ_EXPECT_ARRAY(sort, "conditions");
        binary_wo::VarObj vConditions = sort.get_s("conditions");
        for (int i = 0, cnt = vConditions.arrayLength_s(); i < cnt; i++)
        {
            binary_wo::VarObj condition = vConditions.at_s(i);
            VAR_OBJ_EXPECT_STRING(condition, "fieldId");
            EtDbId fldId = commonHelper.GetEtDbId(condition, "fieldId");
            ks_stdptr<IDBRecordsOrderCondition> spCondition;
            VS(spExtraSort->AddCondition(fldId, &spCondition));
            VAR_OBJ_EXPECT_BOOL(condition, "isAscending")
            spCondition->SetAscending(alg::bool2BOOL(condition.field_bool("isAscending")));
        }
    }
    *ppSort = spExtraSort.detach();
    return S_OK;
}

HRESULT GetValidViewName(IDBSheetViews* pDbSheetViews, PCWSTR name, OUT ks_wstring& viewName)
{
    HRESULT hr = pDbSheetViews->IsValidViewName(name);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
		return hr;

    viewName = name;
	if (hr == E_DBSHEET_VIEW_NAME_CONFLICT)
	{
		int postfix = 1;
		do
		{
			QString newName = QString::fromUtf16(name) + QString("(") + QString::number(postfix, 10) + QString(")");
			viewName = krt::utf16(newName);
			postfix++;
		} while (E_DBSHEET_VIEW_NAME_CONFLICT == pDbSheetViews->IsValidViewName(viewName.c_str()));
	}
    return S_OK;
}

bool ContainTimeFormat(PCWSTR numberFormat)
{
    return xstrstr(numberFormat, __X("h:mm")) != nullptr;
}

void WalkThroughHandleTokenArray(const_token_ptr pToken, const std::function<void(alg::TOKEN_HANDLE, DWORD)>& tokenHandler)
{
    // 引用字段可以引用引用字段，TokenArray的item也可能是TokenArray，用递归有栈溢出的风险
    std::stack<const_token_ptr> tokenStack{{pToken}};
    while (!tokenStack.empty())
    {
        pToken = tokenStack.top();
        tokenStack.pop();
        if (!alg::const_handle_token_assist::is_type(pToken))
            continue;

        alg::const_handle_token_assist chta(pToken);
        alg::TOKEN_HANDLE handle = chta.get_handle();
        if (!handle)
            continue;

        DWORD handleType = chta.get_handleType();
        if (handleType != alg::ET_HANDLE_TOKENARRAY)
        {
            tokenHandler(handle, handleType);
            continue;
        }

        ks_stdptr<IDbTokenArrayHandle> spTokenArray = chta.get_handle()->CastUnknown();
        if (!spTokenArray)
            continue;

        for (UINT i = 0, c = spTokenArray->GetCount(); i < c; ++i)
        {
            const_token_ptr item = nullptr;
            VS(spTokenArray->Item(i, &item));
            tokenStack.push(item);
        }
    }
}

// KDbCstrUserInfo 结构体持有的是wo::DbSheet::UserInfo对象内字符串的指针
// 当对象发生拷贝/移动时, 之前持有的指针会失效. 因此要么预先对 wo::DbSheet::UserInfo 的vector
// 进行 reserve 以避免扩容中的拷贝/移动, 要么显式地在 userInfoVec 构造完毕后创建 cstrUserInfoVec
HRESULT GetCstrUserInfoVec(
    IN const std::vector<wo::DbSheet::UserInfo>& userInfoVec, 
    OUT std::vector<KDbCstrUserInfo>& cstrUserInfoVec)
{
    if (not cstrUserInfoVec.empty())
    {
        ASSERT(FALSE);
        return E_FAIL;
    }
    size_t c = userInfoVec.size();
    cstrUserInfoVec.reserve(c);
    for (int i = 0; i < c; ++i)
        cstrUserInfoVec.emplace_back(userInfoVec[i].ToCstr());
    return S_OK;
}
// 同上
HRESULT GetCstrFailedIds(
    IN const std::vector<ks_wstring>& failedIds, 
    OUT std::vector<PCWSTR>& cstrFailedIds)
{
    if (not cstrFailedIds.empty())
    {
        ASSERT(FALSE);
        return E_FAIL;
    }
    size_t c = failedIds.size();
    cstrFailedIds.reserve(c);
    for (int i = 0; i < c; ++i)
        cstrFailedIds.emplace_back(failedIds[i].c_str());
    return S_OK;
}

HRESULT AddWildChar(PCWSTR srcStr, ks_wstring& resStr)
{
    // COUNTIF/SUMIF 函数带通配符字符串匹配(在等于或不等于条件中)比较特殊，
    // 基本规则如下：
    // (1) 若 Criteria 字符串中含 '*' 和 '?' 字符，则 '~' 作为转义字符处理，即
    //     "~*" 转义后为 "*"，"~?" 转义后为 "?"，"~~" 转义后为 "~"
    // (2) 若 Criteria 字符串中不含 '*' 和 '? 字符，则 ~ 不作转义，"~~" 仍作 "~~"，而不转义为 "~"
    bool hasQuestionMarkOrAsterisk = false;
    for (PCWSTR str = srcStr; *str != __Xc('\0'); ++str)
    {
        switch (*str)
        {
            case __Xc('?'):
            case __Xc('*'):
                hasQuestionMarkOrAsterisk = true;
                break;
        }
    }
    if (hasQuestionMarkOrAsterisk)
    {
        for (PCWSTR str = srcStr; *str != __Xc('\0'); ++str)
        {
            switch (*str)
            {
                case __Xc('?'):
                case __Xc('*'):
                case __Xc('~'):
                    resStr.push_back(__Xc('~'));
                    break;
            }
            resStr.push_back(*str);
        }
    }
    return S_OK;
}

HRESULT DoShareView(binary_wo::VarObj& param, IEtRevisionContext* pCtx, IEncodeDecoder* pEncodeDecoder)
{
	if(!param.has("sheetStId") || !param.has("viewId") || !param.has("sharedId") )
	{
		WOLOG_ERROR << "[DoShareView] param error!";
		return E_FAIL;
	}

    IBook* pBook = pCtx->getBook();
	UINT sheetStId = param.field_uint32("sheetStId");
    EtDbId viewId = GetEtDbId(param, "viewId");
	PCWSTR sharedId = param.field_str("sharedId");
	ks_stdptr<IDBSheetView> spDbSheetView;
    GetDBSheetView(pBook, sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = S_OK;
	KDbSharedCriteriaType visibleRecTp = DBSCT_All;
	KDbSharedCriteriaType editableRecTp = DBSCT_All;
	KDbSharedCriteriaType removeableRecTp = DBSCT_All;
	if(param.has("visibleRecTp"))
	{
		hr = pEncodeDecoder->DecodeDbSharedCriteriaType(param.field_str("visibleRecTp"), &visibleRecTp);
		if (FAILED(hr))
			return hr;
	}
	
	if(param.has("editableRecTp"))
	{
		hr = pEncodeDecoder->DecodeDbSharedCriteriaType(param.field_str("editableRecTp"), &editableRecTp);
		if (FAILED(hr))
			return hr;
	}
	
	if(param.has("removeableRecTp"))
	{
		hr = pEncodeDecoder->DecodeDbSharedCriteriaType(param.field_str("removeableRecTp"), &removeableRecTp);
		if (FAILED(hr))
			return hr;
	}

	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	IDBSharedLinkView* pSharedLinkOld = spDbSheetView->GetSharedLink();
	if(pSharedLinkOld)
	{
		HRESULT hr = spSharedLinkMgr->Remove(pSharedLinkOld->Id());
		if(FAILED(hr))
			return hr;
		//重置过期的分享链接时，清除内核相关的ShareSetting数据
		hr = spDbSheetView->ClearAllShareSettings();
	}

	bool bUsedForApp = false;
	if (param.has("bUsedForApp"))
		bUsedForApp = param.field_bool("bUsedForApp");

	{
		//日志收集，方便排查问题
		WOLOG_INFO << "[DoShareView::CreateSharedLink] sharedId:"
			<< sharedId << ",userId:" << pCtx->getUser()->userID()
			<< ",bUsedForApp:" <<  bUsedForApp;
	}

	ks_stdptr<ISharedLink> spSharedLink;
	hr = spSharedLinkMgr->CreateSharedLink(sharedId, pCtx->getUser()->userID(), bUsedForApp, 
						SharedLinkType_DbView, &spSharedLink, sheetStId, viewId);
	if(FAILED(hr))
		return hr;				

	ks_stdptr<IDBSharedLinkView> spSharedView = spSharedLink;
	spSharedView->SetVisibleRecType(visibleRecTp);
	spSharedView->SetEditableRecType(editableRecTp);
	spSharedView->SetRemoveableRecType(removeableRecTp);
	
	if (param.has("canAddRec"))
		spSharedView->SetCanAddRecords(param.field_bool("canAddRec"));

    if (param.has("editableFieldIds"))
    {
        VAR_OBJ_EXPECT_STRUCT(param, "editableFieldIds");
        binary_wo::VarObj editableFieldIdsObj = param.get_s("editableFieldIds");
        bool defaultVal = editableFieldIdsObj.field_bool("defaultVal");
        std::vector<EtDbId> diffIds;
        if (editableFieldIdsObj.has("diffIds"))
        {
            VAR_OBJ_EXPECT_ARRAY(editableFieldIdsObj, "diffIds");
            binary_wo::VarObj diffIdsObj = editableFieldIdsObj.get_s("diffIds");
            int cnt = diffIdsObj.arrayLength_s();
            diffIds.reserve(cnt);
            for (int i = 0; i < cnt; ++i)
            {
                EtDbId fieldId = GetEtDbId(diffIdsObj.at_s(i));
                diffIds.push_back(fieldId);
            }
        }
        spSharedView->UpdateEditableFieldIds(alg::bool2BOOL(defaultVal), diffIds.data(), diffIds.size());
    }


	KDbDisplayFormType displayFormTp = DBDFT_Default;
	if(param.has("displayFormTp"))
	{
		pEncodeDecoder->DecodeDbDisplayForm(param.field_str("displayFormTp"), &displayFormTp);
	}
	spSharedView->SetDisplayFormType(displayFormTp);
	
    if (param.has("airAppSheetStId"))
		spSharedView->SetAppSheetStId(param.field_uint32("airAppSheetStId"));
 
	if (param.has("airAppId"))
	{
		EtDbId airAppId = GetEtDbId(param, "airAppId");
		spSharedView->SetAppId(airAppId);
	}

	VS(spSharedLinkMgr->Add(spSharedView));
	return hr;
}

// 非命令回放时调用，调用时要注意命令回放时的处理。
void FetchSharedIdForNotDirect(ET_DBSheet_ViewType viewType, ks_wstring* sharedId)
{
    IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
    ASSERT(!pCtx->isExecDirect());
    PCWSTR typeStr = nullptr;
    VS(_appcore_GainEncodeDecoder()->EncodeViewType(static_cast<ET_DBSheet_ViewType>(viewType), &typeStr));
    QString viewTypeStr = QString::fromUtf16(typeStr);

    constexpr int sharedIdBufMaxLen = 32;//目前服务端那边生成的sharedId只有20多个字节，预留数组为32，后续如果不够再加。
    char sharedIdBuf[sharedIdBufMaxLen] = {0};
    
    const char *pViewType = viewTypeStr.toUtf8();
    gs_callback->fetchSharedId(pViewType, sharedIdBuf, sharedIdBufMaxLen);
    QString qSharedId = QString::fromUtf8(sharedIdBuf);
    *sharedId = krt::utf16(qSharedId);
}

void SetSharedIdParam(std::vector<etoldapi::_Worksheet*>& newWorkSheets, binary_wo::VarObj &param)
{
    // 将新生成的分享id写入参数，在命令回放时就不需要重新生成分享id了
    wo::IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
    if (!pCtx->isExecDirect())
    {
        binary_wo::VarObj varSharedIds = param.add_field_array("sharedIds", binary_wo::typeStruct);
        for (int pos = 0; pos < newWorkSheets.size(); ++pos)
        {
            etoldapi::_Worksheet* pNewWorkSheet = newWorkSheets[pos];
            ISheet* pSheet = pNewWorkSheet->GetSheet();
            ASSERT(pNewWorkSheet && pSheet->IsAppSheet());
            binary_wo::VarObj varSheetSharedIds = varSharedIds.add_item_struct();
            varSheetSharedIds.add_field_uint32("sheetStId", pSheet->GetStId());
            binary_wo::VarObj varSharedIdsInfo = varSheetSharedIds.add_field_array("sharedIdsInfo", binary_wo::typeStruct);
            ks_stdptr<IAppSheetData> spAppSheetData;
            pSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spAppSheetData);
            for (int i = 0; i < spAppSheetData->GetAppCount(); ++i)
            {
                binary_wo::VarObj varViewSharedId = varSharedIdsInfo.add_item_struct();
                IAirApp* pApp = spAppSheetData->GetApp(i);
                varViewSharedId.add_field_str("sharedId", pApp->GetSharedId());
                EtDbIdStr buf;
                IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
                pCtx->EncodeEtDbId(pApp->GetId(), &buf);
                varViewSharedId.add_field_str("appId", buf);
            }
        }
        pCtx->setIsRealTransform(true);
    }
}

HRESULT SetViewProp(IDBSheetViews *pViews, IDBSheetView *pView, binary_wo::VarObj &param, KEtRevisionContext* pCtx)
{
    if (param.has("useDefaultViewFieldOrder") and param.field_bool("useDefaultViewFieldOrder"))
    {
        const IDBSheetView* const pDefaultView = pViews->GetDefaultView();
        const IDBIds* const pFields = pDefaultView->GetOrderFields();
        std::vector<EtDbId> IDs;
        const UINT fieldCount = pFields->Count();
        IDs.reserve(fieldCount);
        for (UINT i = 0; i < fieldCount; ++i)
        {
            IDs.emplace_back(pFields->IdAt(i));
        }
        pView->ResetOrderFields(IDs.data(), fieldCount, TRUE);
    }
    
    ET_DBSheet_ViewType type = pView->GetType();
    HRESULT hr = S_OK;
    switch (type)
    {
    case et_DBSheetView_Grid:
    {
        if (param.has("rowHeight"))
        {
            INT nHeight = param.field_int32("rowHeight");
            ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = pView;
            if (spDbSheetViewGrid)
            {
                hr = spDbSheetViewGrid->SetRecordsHeight(nHeight);
                if (FAILED(hr))
                    return hr;
            }
        }
        bool dispSumForNumberAndCurrencyInStatusBar = false;
        if (param.has("dispSumForNumberAndCurrencyInStatusBar"))
            dispSumForNumberAndCurrencyInStatusBar = true;

        if (dispSumForNumberAndCurrencyInStatusBar)
        {
            IDbFieldsManager *spFieldsMgr = pView->GetFieldsManager();
            const IDBIds* pFields = pView->GetOrderFields();
            for (EtDbIdx i = 0; i < pFields->Count(); ++i) 
            {
                EtDbId fldId = pFields->IdAt(i);
                ks_stdptr<IDbField> spField;
                hr = spFieldsMgr->GetField(fldId, &spField);
                if (FAILED(hr))
                    return hr;
                ET_DbSheet_FieldType fldType = spField->GetType();
                if(fldType == Et_DbSheetField_Number || fldType == Et_DbSheetField_Currency)
                {
                    hr = pView->GetMutableStatisticOptions()->SetStatistic(fldId, DBSSO_sum);
                    if(FAILED(hr))
                        return hr;
                }
            }
        }
        break;
    }
    // 理论上除了看板视图, 新建视图不会包含排序条件的, 而看板视图在 add_condition 底层标记dirty了. 增加视图类型, 则可能需要增加视图更新检查
    case et_DBSheetView_Kanban:
        if (param.has("ungroupedPositionOption"))
        {
            ks_stdptr<IDBSheetView_Kanban> spDbSheetView_Kanban = pView;
            ET_DBSheet_PositionOption option = DBSP_last;
            VS(_appcore_GainEncodeDecoder()->DecodePositionOption(param.field_str("ungroupedPositionOption"), &option));
            hr = spDbSheetView_Kanban->SetUngroupedPositionOption(option);
        }
        SetMaxFieldsCnt(param, pView);
        break;
    case et_DBSheetView_Gallery:
        SetMaxFieldsCnt(param, pView);
        break;
    case et_DBSheetView_Form:
    {
        hr = DealViewFormParam(param, pView, pViews);
        if (FAILED(hr))
            return hr;
        break;
    }
    case et_DBSheetView_Gantt:
    {
        EtDbId viewId = INV_EtDbId;
        if (param.has("fromViewId"))
            viewId = GetEtDbId(param, "fromViewId");
        ks_stdptr<IDBSheetView_Gantt> spView_Gantt = pView;
        // 新建甘特图时强制要求前端传颜色参数.
        // hs_todo: 如果能基于甘特图新建甘特图, 则可以放开这个要求. 不过应该没有这种场景, 因为此时可走复制视图
        VAR_OBJ_EXPECT_NUMERIC(param, "timelineColor");
        spView_Gantt->CreateFromView(viewId);
        spView_Gantt->SetTimeLineColor(param.field_uint32("timelineColor"));
        break;
    }
    case et_DBSheetView_Query:
    {
        ks_stdptr<IDBSheetView_Query> spView_Query = pView;
        if (param.has("conditionCanBlank"))
        {
            ASSERT(pCtx->isExecDirect());//期望后续前端传参数，都不能在视图级别带上"conditionCanBlank"此参数！！！
            BOOL canBlank = param.field_bool("conditionCanBlank");
            spView_Query->SetConditionCanBlankForOldIO(canBlank);
            if(pCtx->isExecDirect())
            {
                //回放时，把已有的查询条件的op都给遍历清洗成canBlank
                spView_Query->ResetExistedFieldConditionCanBlank(canBlank);
            }

        }
        if (param.has("needCheck"))
        {
            ASSERT(pCtx->isExecDirect());//期望后续前端传参数，都不能在视图级别带上"needCheck"此参数！！！
            BOOL needCheck = param.field_bool("needCheck");
            if(pCtx->isExecDirect())
            {
                //回放时，把已有的查询条件的op都给遍历清洗成canBlank
                spView_Query->ResetExistedFieldNeedSecondCheck(needCheck);
            }
        }
        break;
    }
    case et_DBSheetView_Calendar:
    {
        EtDbId viewId = INV_EtDbId;
        if (param.has("fromViewId"))
            viewId = GetEtDbId(param, "fromViewId");
        ks_stdptr<IDBSheetView_Calendar> spView_Calendar = pView;
        VAR_OBJ_EXPECT_NUMERIC(param, "timelineColor");
        spView_Calendar->CreateFromView(viewId);
        spView_Calendar->SetTimeLineColor(param.field_uint32("timelineColor"));
        break;
    }
    default:
        break;
    }
    return S_OK;
}

void SetMaxFieldsCnt(VarObj param, IDBSheetView* pView)
{
	if (param.has("maxFieldsCount"))
	{
		int maxFieldsCount = param.field_int32("maxFieldsCount");
		const IDBIds *pIds = pView->GetVisibleFields();
		EtDbIdx cnt = pIds->Count();
		std::vector<EtDbId> idVec;
		for (EtDbIdx i = maxFieldsCount; i < cnt; i++)
			idVec.push_back(pIds->IdAt(i));
		for (auto it = idVec.begin(); it != idVec.end(); ++it)
			pView->SetFieldsHidden(*it, TRUE);
	}
}

HRESULT DealViewFormParam(VarObj param, IDBSheetView* pDbSheetView,IDBSheetViews* pDbSheetViews)
{
	if (!param.has("visibleFields"))
		return S_OK;

	std::unordered_set<EtDbId> visibleFields;
	VarObj fields = param.get("visibleFields");
	for (UINT32 i = 0, cnt = fields.arrayLength(); i < cnt; ++i)
	{
		EtDbId fieldId = GetEtDbId(fields.at(i));
		visibleFields.insert(fieldId);
	}

	const IDBIds* pFields = pDbSheetView->GetOrderFields();
	for (EtDbIdx i = 0; i < pFields->Count(); ++i) 
	{
		EtDbId id = pFields->IdAt(i);
		pDbSheetView->SetFieldsHidden(id, alg::bool2BOOL(visibleFields.find(id) == visibleFields.end()));
	}
	return S_OK;
}


void GetAttachmentIdMap(const VarObj& param, std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher>& attachmentIdMap)
{
	const VarObj attachmentData = param.get_s("copyAttachmentData");
	for (int i = 0, dataCnt = attachmentData.arrayLength_s(); i < dataCnt; ++i)
	{
		const VarObj data = attachmentData.at_s(i);
		PCWSTR oldId = data.field_str("srcAttachmentId");
		attachmentIdMap[GlobalSharedString(oldId)] = GlobalSharedString((PCWSTR)nullptr);
		// status: 状态 0-成功 1-失败
		if (data.field_int32("status") == 0)
			attachmentIdMap[GlobalSharedString(oldId)] = GlobalSharedString(data.field_str("attachmentId"));
	}
}

DbFieldUpdateScope::DbFieldUpdateScope(IDbField *pField, IBook *pBook, HRESULT &hr)
{
    _appcore_CreateObject(CLSID_KDBFieldUpdateScope, IID_IDBFieldUpdateScope, (void**)&m_spDbFieldUpdateScope); 
    ASSERT(m_spDbFieldUpdateScope != nullptr);
    m_spDbFieldUpdateScope->Init(pField, pBook, &hr);
}

DbFieldUpdateScope::~DbFieldUpdateScope()
{
    
}

IDbField *DbFieldUpdateScope::GetOldField()
{
    ASSERT(m_spDbFieldUpdateScope != nullptr);
    return m_spDbFieldUpdateScope->GetOldField();
}

HRESULT SetFilterConditions(IDbFilter* pFilter, const binary_wo::VarObj& conditions, IDBSheetOp* pData, const DBSheetCommonHelper& commonHelper)
{

    HRESULT hr = E_FAIL;
    KDbFilterOpType op = DBFOT_And;
    bool bPreferId = true;
    if (conditions.has("mode"))
    {
        hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterOpType(conditions.field_str("mode"), &op);
        if (FAILED(hr))
            return E_DBSHEET_FILTER_INVALID;
    }
    if (op != DBFOT_And && op != DBFOT_Or)
        return E_DBSHEET_FILTER_INVALID;
    hr = pFilter->SetOperator(op);

    if (conditions.has("criteria"))
    {
        VarObj criteria = conditions.get_s("criteria");
        if (binary_wo::typeArray != criteria.type())
            return E_DBSHEET_FILTER_INVALID;
        IDbFieldsManager* pFieldsMgr = pData->GetFieldsManager();
        for (int32 i = 0, cnt = criteria.arrayLength(); i < cnt; ++i)
        {
            VarObj criterion = criteria.at(i);
            if (binary_wo::typeStruct != criterion.type())
                return E_DBSHEET_FILTER_INVALID;
            if (false == criterion.has("field"))
                return E_DBSHEET_FILTER_INVALID;
            EtDbId fldId = INV_EtDbId;
            if (bPreferId)
            {
                fldId = commonHelper.GetEtDbId(criterion, "field");
            }
            else
            {
                ks_stdptr<IDbField> spField;
                hr = pFieldsMgr->FindField(criterion.field_str("field"), &spField);
                if (FAILED(hr))
                    return E_DBSHEET_FILTER_INVALID;
                fldId = spField->GetID();
            }
            ks_stdptr<IDbFieldFilter> spFieldFilter;
            hr = pFilter->AddFilter(fldId, &spFieldFilter);
            if (FAILED(hr))
                return E_DBSHEET_FILTER_INVALID;

            ks_stdptr<IDbFilterCriteria> spCriteria;
            hr = CreateDbFilterCriteria(criterion, pData->GetBook(), &spCriteria);
            if (FAILED(hr))
                return E_DBSHEET_FILTER_INVALID;

            hr = spFieldFilter->SetCriteria(spCriteria);
            if (FAILED(hr))
                return E_DBSHEET_FILTER_INVALID;
        }
	}
    return hr;
}

HRESULT GenerateDBProtection(IBook* pBook, IDBProtectionJudgement* pDBProtectObj, const binary_wo::VarObj& permissionParam, OUT IDBProtection** pProtection)
{
    if (!pDBProtectObj || !pProtection || permissionParam.type() != binary_wo::typeArray)
        return E_FAIL;
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	DBSheetCommonHelper commonHelper(pBook);
    ks_stdptr<IDBProtection> spProtection;
    spProtection.attach(pDBProtectObj->CreateDBProtectionObj());
    for (int32 i = 0, length = permissionParam.arrayLength_s(); i < length; ++i)
    {
        binary_wo::VarObj  sheetPermissionConfig = permissionParam.at_s(i);
        WebStr strPermissionType = sheetPermissionConfig.field_str("sheetPermissionType");
        uint32 sheetStId = sheetPermissionConfig.field_uint32("sheetStId");
        IDX sheetIdx = INVALIDIDX;
        pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
        if (sheetIdx == INVALIDIDX)
            continue;
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
		// 跨 book 关联字段的同步底表的权限不支持手动修改，仅支持自动修改
		if (spDbSheetOp && spDbSheetOp->GetSheetSyncType() == DbSheet_St_Cross_DB)
			continue;
        KDBSheetPermissionType permissionType = DBSheet_Permission_NoPermission;
        VS(_appcore_GainEncodeDecoder()->DecodeKDBSheetPermissionType(strPermissionType, &permissionType));
        IDBSheetProtection* pSheetPermission  = spProtection->AddSheetPermission(permissionType, sheetStId);

        KDbSharedCriteriaType recordType = DBSCT_All;
        if(sheetPermissionConfig.has("recordConfigType")) //可编辑有此参数
        {
            HRESULT hr = _appcore_GainEncodeDecoder()->DecodeDbSharedCriteriaType(sheetPermissionConfig.field_str("recordConfigType"), &recordType);
            if (FAILED(hr))
                return hr;
            pSheetPermission->SetRecordProtectType(recordType);
        }
        if (DBSheet_Permission_View == permissionType || DBSheet_Permission_Edit == permissionType)
        {
            if (!sheetPermissionConfig.has("viewedSheetConfig"))
                continue;
            binary_wo::VarObj viewedSheetConfig = sheetPermissionConfig.get("viewedSheetConfig");
            if (!spDbSheetOp)
                continue;
            if (recordType == DBSCT_Custom || DBSheet_Permission_View == permissionType)
            {
                bool  allRecord = getField_bool(viewedSheetConfig, "isAllRecord");
                if (!allRecord)
                {
                    IDbFilter* recordFilter =  pSheetPermission->GetRecordProtect()->GetVisibleFilter();
                    HRESULT hr = SetFilterConditions(recordFilter, viewedSheetConfig.get_s("recordFilter"), spDbSheetOp, commonHelper);
                    if (FAILED(hr))
                        return hr;
                }
            }
            bool  allField = getField_bool(viewedSheetConfig, "isAllField");
            if (!allField)
            {
                IDBIdBoolMap* fieldMap =  pSheetPermission->GetFieldProtect()->GetVisible();
                fieldMap->SetAll(FALSE);
                binary_wo::VarObj ids = viewedSheetConfig.get_s("fieldFilter");
                int32 count = ids.arrayLength_s();
                EtDbId id = INV_EtDbId;
                IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();
		        EtDbId primaryFieldId = pFieldsMgr->GetPrimaryField();
                bool  primaryFieldVisible = false;
                for (int32 i = 0; i < count; ++i)
                {
                    dbctx->DecodeEtDbId(ids.item_str(i), &id);
                    fieldMap->Set(id, TRUE);
                    if (!primaryFieldVisible && primaryFieldId == id)
                         primaryFieldVisible = true;                  
                }
                if (!primaryFieldVisible)
                    return  E_DBSHEET_ALTER_PRIMARY_FIELD;
            }
        }
        if (DBSheet_Permission_Edit == permissionType)
        {
            if (!sheetPermissionConfig.has("editableSheetConfig"))
                continue;

            binary_wo::VarObj editableSheetConfig = sheetPermissionConfig.get("editableSheetConfig");

            pSheetPermission->SetAddRecord(getField_bool(editableSheetConfig, "isAddRecord"));
            bool  removeRecord = getField_bool(editableSheetConfig, "isRemoveRecord");
            IDbFilter* removeRecordFilter = pSheetPermission->GetRecordProtect()->GetRemoveableFilter();
            removeRecordFilter->SetOperator(removeRecord ? DBFOT_True : DBFOT_False);

            if (recordType == DBSCT_Custom)
            {
                bool  allRecord = getField_bool(editableSheetConfig, "isAllRecord");
                if (!allRecord)
                {
                    IDbFilter* editRecordFilter = pSheetPermission->GetRecordProtect()->GetEditableFilter();
                    if (!spDbSheetOp)
                        continue;
                    HRESULT hr = SetFilterConditions(editRecordFilter, editableSheetConfig.get_s("recordFilter"), spDbSheetOp, commonHelper);
                    if (FAILED(hr))
                        return hr;
                }
            }

            bool  allField = getField_bool(editableSheetConfig, "isAllField");
            if (!allField)
            {
                IDBIdBoolMap* editFieldMap = pSheetPermission->GetFieldProtect()->GetEditable();
                editFieldMap->SetAll(FALSE);
                binary_wo::VarObj ids = editableSheetConfig.get_s("fieldFilter");
                int32 count = ids.arrayLength_s();
                EtDbId id = INV_EtDbId;
                for (int32 i = 0; i < count; ++i)
                {
                    dbctx->DecodeEtDbId(ids.item_str(i), &id);
                    editFieldMap->Set(id, TRUE);
                }
            }
        }
    }
	// 最后处理跨 book 关联的同步底表的相关权限
	struct KDBIdBoolMapEnum : IDBIdBoolMapEnum
	{
		void Default(BOOL) override {}
		BOOL Diff(EtDbId id) override
		{
			fieldIds.emplace_back(id);
			return TRUE;
		}
		std::vector<EtDbId> fieldIds;
	};
	struct CrossFieldEnum : IDbFieldEnum
	{
		HRESULT Do(IDbField* pField) override
		{
			if (!pField || pField->GetType() != Et_DbSheetField_OneWayLink)
				return S_OK;
			ks_stdptr<IDbField_Link> spLinkField = pField;
			if (spLinkField->IsSyncLink())
				m_sheetIds.emplace(spLinkField->GetLinkSheet());
			return S_OK;
		}
		std::unordered_set<UINT> m_sheetIds;
	};
	struct KDBSheetProtectionEnum : IDBSheetProtectionEnum
	{
		explicit KDBSheetProtectionEnum(IBook* pBook) : m_commonHelper(pBook) {}
		HRESULT Do(UINT sheetStId, IDBSheetProtection* pSheetProtection) override
		{
			KDBSheetPermissionType permissionType = pSheetProtection->GetSheetPermissionType();
			if (permissionType == DBSheet_Permission_Custom || permissionType == DBSheet_Permission_NoPermission)
				return S_OK;
			ks_stdptr<IDBSheetOp> spDbSheetOp;
			m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
			if (!spDbSheetOp)
				return S_OK;
			IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();
			auto getSheetIdByMap = [pFieldsMgr](IDBIdBoolMap* fieldMap, std::unordered_set<UINT>& sheetIds) {
				KDBIdBoolMapEnum enumer;
				fieldMap->Enum(&enumer);
				for (EtDbId id : enumer.fieldIds)
				{
					ks_stdptr<IDbField> spField;
					pFieldsMgr->GetField(id, &spField);
					if (!spField || spField->GetType() != Et_DbSheetField_OneWayLink)
						continue;
					ks_stdptr<IDbField_Link> spLinkField = spField;
					if (spLinkField->IsSyncLink())
						sheetIds.emplace(spLinkField->GetLinkSheet());
				}
			};
			auto getSheetIdByTraversal = [pFieldsMgr](std::unordered_set<UINT>& sheetIds) {
				CrossFieldEnum crossFieldEnum;
				pFieldsMgr->EnumFields(&crossFieldEnum);
				for (auto id : crossFieldEnum.m_sheetIds)
					sheetIds.emplace(id);
			};
			switch (permissionType)
			{
				case DBSheet_Permission_View:
				{
					IDBIdBoolMap* fieldMap = pSheetProtection->GetFieldProtect()->GetVisible();
					if (fieldMap->GetDefault())
						getSheetIdByTraversal(m_viewSheetIds);
					else
						getSheetIdByMap(fieldMap, m_viewSheetIds);
					break;
				}
				case DBSheet_Permission_Edit:
				{
					IDBIdBoolMap* editFieldMap = pSheetProtection->GetFieldProtect()->GetEditable();
					if (editFieldMap->GetDefault())
					{
						getSheetIdByTraversal(m_editSheetIds);
						break;
					}
					getSheetIdByMap(editFieldMap, m_editSheetIds);
					IDBIdBoolMap* fieldMap = pSheetProtection->GetFieldProtect()->GetVisible();
					if (fieldMap->GetDefault())
						getSheetIdByTraversal(m_viewSheetIds);
					else
						getSheetIdByMap(fieldMap, m_viewSheetIds);
					break;
				}
				case DBSheet_Permission_Manage:
					getSheetIdByTraversal(m_manageSheetIds);
					break;
				default:
					ASSERT(false);
					break;
			}
			return S_OK;
		}
		DBSheetCommonHelper m_commonHelper;
		std::unordered_set<UINT> m_viewSheetIds;
		std::unordered_set<UINT> m_editSheetIds;
        std::unordered_set<UINT> m_manageSheetIds;
	};
	KDBSheetProtectionEnum crossSheetEnumer(pBook);
	spProtection->EnumeDBSheetProtection(&crossSheetEnumer);
    for (UINT sheetStId : crossSheetEnumer.m_manageSheetIds)
	{
		IDBSheetProtection* pSheetPermission = spProtection->AddSheetPermission(DBSheet_Permission_Edit, sheetStId);
		pSheetPermission->SetAllowEditSheetProperty(TRUE);
		pSheetPermission->SetManageFields(TRUE);
		crossSheetEnumer.m_editSheetIds.erase(sheetStId);
        crossSheetEnumer.m_viewSheetIds.erase(sheetStId);
	}
	for (UINT sheetStId : crossSheetEnumer.m_editSheetIds)
	{
		spProtection->AddSheetPermission(DBSheet_Permission_View, sheetStId);
		crossSheetEnumer.m_viewSheetIds.erase(sheetStId);
	}
	for (UINT sheetStId : crossSheetEnumer.m_viewSheetIds)
		spProtection->AddSheetPermission(DBSheet_Permission_NoPermission, sheetStId);
    *pProtection = spProtection.detach();

    return S_OK;
}

HRESULT ReadDBProtectionConfig(IBook* pBook, const WebSlice& permissionSlice)
{
    ks_stdptr<IDBUserGroups> spDBUserGroups;
    VS(pBook->GetExtDataItem(edDBUserGroups, (IUnknown**)&spDBUserGroups));
    ks_stdptr<IDBProtectionJudgement> spProtectJudgement;
    spDBUserGroups->GetJudgement(&spProtectJudgement);

    binary_wo::BinReader rd(permissionSlice.data, permissionSlice.size);
    binary_wo::VarObjRoot permissionRoot = rd.buildRoot();
    binary_wo::VarObj permission = permissionRoot.cast();
    binary_wo::VarObj permissionList = permission.get_s("permissions");
    if (permissionList.type() != binary_wo::typeArray)
        return E_INVALIDARG;

    int32 length = permissionList.arrayLength_s();
    for (int32 i = 0; i < length; i++)
    {
        binary_wo::VarObj permission = permissionList.at_s(i);
        ks_stdptr<IDBProtection> spProtection;
        HRESULT hr = GenerateDBProtection(pBook, spProtectJudgement, permission.get_s("permissionContent"), &spProtection);
        if(FAILED(hr))
            WOLOG_INFO << "[ReadDBProtectionConfig]  GenerateDBProtection failed hr:" << hr;
        if (spProtection)
        {
            spProtection->SetDBPermissonId(permission.field_str("corePermissionId"));
            spProtection->SetDBPermissonName(permission.field_str("permissionName"));
            spProtectJudgement->AddDBProtection(spProtection);
        }
         
    }

    if (length >= 0 && spDBUserGroups->IsProtected())
        spDBUserGroups->SetProtectOn(false);
    spDBUserGroups->MarkPermissionConfiged(TRUE);

    WOLOG_INFO << "[ReadDBProtectionConfig]  permission config  array size: " << length;

    return S_OK;
}

HRESULT ResetAppDBViewSharedId(IAirApp_DBView* pAirAppView, PCWSTR newSharedId)
{
    IDBSheetView* pView = pAirAppView->GetDBSheetView();
	if (pView)
	{
		IDBSharedLinkView* pLink = pView->GetSharedLink();
		if (pLink)
		{
			if(0 == xstrcmp(pLink->Id(),newSharedId))
			{
				WOLOG_INFO << "[ResetAppDBViewSharedId] new sharedId is the same as existing sharedId, skip to reset it!";
				return S_OK;
			}
			
			ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
			pView->GetSheetOp()->GetBook()->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);            
			HRESULT hr = spSharedLinkMgr->Remove(pLink->Id());
			if(FAILED(hr))
				return hr;
			
			hr = pLink->ResetId(newSharedId);
			if(FAILED(hr))
				return hr;

			hr =  spSharedLinkMgr->Add(pLink);
			if(FAILED(hr))
				return hr;
		}
	}
	return S_OK;
}

HRESULT SubmitCollectedFmlCells2DbSheet(IBook* pBook)
{
    if (not pBook->GetBMP()->bDbSheet && not pBook->GetBMP()->bKsheet)
        return S_FALSE;

    INT sheetCnt = 0;
    HRESULT hr = pBook->GetSheetCount(&sheetCnt);
    if (FAILED(hr))
        return hr;
    for (INT i = 0; i < sheetCnt; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        hr = pBook->GetSheet(i, &spSheet);
        if (FAILED(hr))
            return hr;
        if (not spSheet->IsDbSheet())
            continue;
        ks_stdptr<IDBSheetFmlCollector> spDbShtFmlCollector;
        VS(spSheet->GetExtDataItem(edSheetDbFmlCollector, (IUnknown**)&spDbShtFmlCollector));
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        hr = DbSheet::GetDBSheetOp(spSheet.get(), &spDbSheetOp);
        if (FAILED(hr))
            return hr;
        spDbShtFmlCollector->SubmitCells(spDbSheetOp.get());
    }
    return S_OK;
}

size_t CountCollectedFmlCells2DbSheet(IBook* pBook)
{
    if (not pBook->GetBMP()->bDbSheet && not pBook->GetBMP()->bKsheet)
        return 0;
    
    size_t stat = 0;

    INT sheetCnt = 0;
    HRESULT hr = pBook->GetSheetCount(&sheetCnt);
    if (FAILED(hr))
        return hr;
    for (INT i = 0; i < sheetCnt; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        hr = pBook->GetSheet(i, &spSheet);
        if (FAILED(hr))
            return hr;
        if (not spSheet->IsDbSheet())
            continue;
        ks_stdptr<IDBSheetFmlCollector> spDbShtFmlCollector;
        VS(spSheet->GetExtDataItem(edSheetDbFmlCollector, (IUnknown**)&spDbShtFmlCollector));
        stat += spDbShtFmlCollector->Count();
    }
    return stat;
}


static ks_wstring validDbSheetNameInKsheet(PCWSTR name)
{
    if (!name)
        return {};
    if (name[0] >= __Xc('0') && name[0] <= __Xc('9'))
    {
        ks_wstring ret;
        ret.reserve(1 + xstrlen(name));
        ret.push_back(__Xc('_'));
        ret.append(name);
        return ret;
    }
    return ks_wstring{name};
}

HRESULT ValidateBaseLookupField(IBook* pBook, UINT sheetId, EtDbId fldId)
{
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = GetDBSheetOp(pBook, sheetId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;
    IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();
    ks_stdptr<IDbField> spCheckField;
    hr = pFieldsMgr->GetField(fldId, &spCheckField);
    if (FAILED(hr))
        return hr;
    if (!spCheckField || spCheckField->GetType() == Et_DbSheetField_Button)
        return E_FAIL;
    return S_OK;
}

HRESULT SerializeFilterList(const binary_wo::VarObj& param, KEtRevisionContext* ctx, DBSheetCommonHelper* pCommonHelper, ISerialAcceptor* acpt, WebInt& version)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId");
    IDX sheetStId = param.field_uint32("sheetStId");
    VAR_OBJ_EXPECT_STRING(param, "viewId");
    EtDbId viewId = GetEtDbId(param, "viewId");

    ks_stdptr<IDBSheetView> spDbSheetView;
    if (viewId == INV_EtDbId)
    {
        ks_stdptr<IDBSheetViews> spDbSheetViews;
        HRESULT hr = pCommonHelper->GetDBSheetViews(sheetStId, &spDbSheetViews);
        if (FAILED(hr))
            return hr;
        if (spDbSheetViews->GetSize(Et_DBSheetViewUse_ForDb) != 0)
            spDbSheetViews->GetItemAt(0, Et_DBSheetViewUse_ForDb, &spDbSheetView);
        else
            spDbSheetView = spDbSheetViews->GetDefaultView();
    }
    else
    {
        pCommonHelper->GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
    }
    if(spDbSheetView == NULL)
        return E_DBSHEET_VIEW_NOT_FOUND;

    HRESULT hr = pCommonHelper->setGetViewCurModifyTar(param);
    if (FAILED(hr))
        return hr;

    const IDbFilter* pFilter = spDbSheetView->GetConstFilter();
    KDBFilterValuesSearchType searchTy = DBFVST_None;
    PCWSTR searchStr = NULL;
    binary_wo::VarObj vSc = param.get_s("search");
    if (vSc.type() != binary_wo::typeInvalid)
    {
        hr = _appcore_GainEncodeDecoder()->DecodeKDBFilterValuesSearchType(vSc.field_str("type"), &searchTy);
        if (FAILED(hr))
            return hr;
        VAR_OBJ_EXPECT_STRING(vSc, "value");
        searchStr = vSc.field_str("value");
    }

    BOOL bQueryByField = FALSE;
    BOOL bDateClassify = FALSE;
    if (param.has("isDateClassify"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "isDateClassify");
        bDateClassify = param.field_bool("isDateClassify");
    }
    VAR_OBJ_EXPECT_BOOL(param, "getAll");
    BOOL bGetAll = param.field_bool("getAll");
    VAR_OBJ_EXPECT_STRING(param, "fieldId");
    EtDbId fldId = GetEtDbId(param, "fieldId");
    const IDbFieldFilter* pFieldFilter = nullptr;
    if(param.has("filterId"))
        pFilter->GetFieldFilter(GetEtDbId(param, "filterId"), &pFieldFilter);
    else
    {
        pFilter->GetFieldFilterByFieldId(fldId, &pFieldFilter);
        bQueryByField = TRUE;
    }
    IDbFieldsManager* pFieldsMgr = spDbSheetView->GetFieldsManager();
    ks_stdptr<IDbField> spField;
    hr = pFieldsMgr->GetField(fldId, &spField);
    if (FAILED(hr))
        return hr;
    
    DBQueryPageInfo pageInfo;
    if (param.has("pageInfo"))
    {
        binary_wo::VarObj page = param.get_s("pageInfo");
        pageInfo.pageSize = page.field_uint32("pageSize");
        pageInfo.pageNum = page.field_uint32("pageNum");
        pageInfo.pageOffset = page.field_uint32("pageOffset");
    }

    DBQueryFieldValuesInfo queryParam;
    queryParam.pFieldFilter = pFieldFilter;
    queryParam.fieldId = fldId;
    queryParam.bGetAll = bGetAll;
    queryParam.searchTy = searchTy;
    queryParam.searchStr = searchStr;
    queryParam.bDateClassify = bDateClassify;
    queryParam.bQueryByField = bQueryByField;
    queryParam.pageInfo  = param.has("pageInfo") ? &pageInfo : nullptr;

    // TODO:优化为当单元格值有改动时才清空筛选的缓存
    WebInt curVersion = ctx->getBaseDataVersion();
    if (curVersion != version)
    {
        pFilter->ClearFieldsValues();
        version = curVersion;
    }
    return pFilter->GetFieldValues(spField->GetDbSheetData(), &queryParam, acpt);
}

HRESULT ConvertStringToDateTime(IBook* pBook, PCWSTR str,  double& dt)
{
    ks_stdptr<IFormula> spFormula;
    pBook->LeakOperator()->CreateFormula(&spFormula);
    CS_COMPILE_PARAM ccp(cpfSysDefault, 0, 0, 0, 0);
    COMPILE_RESULT compileResult;
    HRESULT hr = spFormula->SetFormula(str, ccp, &compileResult);
    if(FAILED(hr))
        return hr;

    const_token_ptr pToken = nullptr;
    BOOL bIsFormula;
    spFormula->GetContent(&bIsFormula, nullptr, &pToken);
    if (!bIsFormula && etexec::const_vdbl_token_assist::is_type(pToken))
    {
        dt = etexec::const_vdbl_token_assist(pToken).get_value();
        return S_OK;
    }
    return E_FAIL;
}

BOOL GetDbLinkCustomConfigRecords(IDBSheetOp* pDbSheetOp, const binary_wo::VarObj& param, std::vector<EtDbId>& records)
{
    BOOL bLinkCustomConfig = FALSE;
    alg::managed_token_assist pResult;
    HRESULT hr = GenDbLinkCustomConfigResult(pDbSheetOp, param, &pResult, bLinkCustomConfig);
    if (FAILED(hr))
    {
        WOLOG_ERROR << "[GetDbLinkCustomConfigRecords] GenDbLinkCustomConfigResult failed hr:" << hr;
        return bLinkCustomConfig;
    }

    if (pResult == nullptr)
        return bLinkCustomConfig;

    alg::const_handle_token_assist chta(pResult);
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (handle && chta.get_handleType() == alg::ET_HANDLE_DBLINK)
    {
        const IDbLinkHandle* pLink = handle->CastDbLink();
        UINT count = pLink->GetCount();
        for (UINT i = 0; i < count ; ++i)
            records.push_back(pLink->GetItemId(i));
    }
    return bLinkCustomConfig;
}

HRESULT GenDbLinkCustomConfigResult(IDBSheetOp* pDbSheetOp, const binary_wo::VarObj& param, managed_token_ptr* ppRes, BOOL& bLinkCustomConfig)
{
    ks_stdptr<IDbAutolinkCondProps> spConds;
    EtDbId recordId = param.has("baseRecId") ? GetEtDbId(param, "baseRecId") : INV_EtDbId;
    EtDbId fieldId = param.has("baseFldId") ? GetEtDbId(param, "baseFldId") : INV_EtDbId;

    HRESULT hr = S_OK;
    BOOL bIgnoreRecord = FALSE;
    if (param.has("filter"))
    {
        hr = createAutoLinkCondProps(param, &spConds);
        if (FAILED(hr))
            return hr;

        bIgnoreRecord = TRUE;
    }
    if ((!bIgnoreRecord && recordId == INV_EtDbId) || fieldId == INV_EtDbId)
        return E_INVALIDARG;

    IDX sheetIdx = INVALIDIDX;
    pDbSheetOp->GetIndex(&sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_DBSHEET_SHEET_NOT_FOUND;

    IDbFieldsManager* pFieldManager = pDbSheetOp->GetFieldsManager();
    ks_stdptr<IDbField> spField;
    pFieldManager->GetField(fieldId, &spField);
    if (!spField)
        return E_DBSHEET_FIELD_NOT_FOUND;

    if (spField->GetType() != Et_DbSheetField_Link && spField->GetType() != Et_DbSheetField_OneWayLink)
        return E_INVALIDARG;

    ks_stdptr<IDbField_Link> spLinkField = spField;
    bLinkCustomConfig = spLinkField->IsLinkCustomConfig();
    if (!bLinkCustomConfig)
        return S_FALSE;

    ks_stdptr<IFormula> spFormula;
    hr = spLinkField->GenerateCustomConfigLinkFml(&spFormula, spConds.get());
    if (FAILED(hr))
        return hr;

    // 跨表关联字段，在当前表空记录情况下，针对表单视图以关联表进行兼容
    BOOL bIgnoreField = FALSE;
    if (pDbSheetOp->GetAllRecords()->Count() == 0 && pDbSheetOp->GetSheetId() != spLinkField->GetLinkSheet())
    {
        pDbSheetOp->GetBook()->STSheetToRTSheet(spLinkField->GetLinkSheet(), &sheetIdx);
        if (sheetIdx == INVALIDIDX)
            return E_DBSHEET_SHEET_NOT_FOUND;

        bIgnoreField = TRUE;
    }

    EtDbIdx row = bIgnoreRecord ? 0 : pDbSheetOp->GetAllRecords()->Id2Idx(recordId);
    EtDbIdx col = bIgnoreField ? 0 : pDbSheetOp->GetAllFields()->Id2Idx(fieldId);
    alg::managed_token_assist token;
    CS_CALCULATE_PARAM ccpc(ccfNormal, sheetIdx, row, col);
    hr = spFormula->Calculate(&token, ccpc);
    if (FAILED(hr))
        return hr;

    if (token && !alg::const_handle_token_assist::is_type(token))
        return E_FAIL;

    *ppRes = token.detach();
    return hr;
}

} // namespace DbSheet

OpenWorkbookScope::OpenWorkbookScope(Workbooks* ptrWorkbooks, IETPersist *ptrPersist, PCWSTR fileName)
{
    if (!ptrPersist->IsSupportedFormat(fileName))
        return;

    ks_bstr SingleFileName(fileName);
    VARIANT varfn = {0};
    VARIANT vpas = {0};
    VARIANT vModifyPas = { 0 };

    V_VT(&varfn) = VT_BSTR;
    V_VT(&vpas) = VT_BSTR;
    V_BSTR(&varfn) = SingleFileName;
    VARIANT varAddToMru;
    V_VT(&varAddToMru) = VT_BOOL;
    V_BOOL(&varAddToMru) = VARIANT_TRUE;
    VARIANT var;
    V_VT(&var) = VT_BOOL;
    V_BOOL(&var) = VARIANT_TRUE;

    VARIANT varUpdateLinks = {0};
    V_VT(&varUpdateLinks) = VT_BOOL;
    V_BOOL(&varUpdateLinks) = VARIANT_FALSE;

	SetBreak(true); // 打开文件中的SetBreak行为如要修改, 记得考察webetlink的Open是否也要修改SetBreak
    IRevisionContext* pCtx = _kso_GetRevisionContext();
    _kso_SetRevisionContext(NULL);
    m_hr = ptrWorkbooks->Open(varfn, varUpdateLinks,
        var,	VARIANT(),
        vpas,	vModifyPas,
        VARIANT(),	VARIANT(),
        VARIANT(),	VARIANT(),
        VARIANT(),	VARIANT(),
        varAddToMru,  &m_spWorkbook, FALSE, FALSE);
    _kso_SetRevisionContext(pCtx);
}

OpenWorkbookScope::~OpenWorkbookScope()
{
    if (nullptr == m_spWorkbook)
        return;

    VARIANT SaveChanges;
    V_VT(&SaveChanges) = VT_BOOL;
    V_BOOL(&SaveChanges) = VARIANT_FALSE;

    VARIANT Filename = {0};
    VARIANT RouteWorkbook = {0};

    m_spWorkbook->Close(SaveChanges, Filename, RouteWorkbook);
}

_Workbook* OpenWorkbookScope::GetWorkbook()
{
    if (m_spWorkbook)
        return m_spWorkbook.get();
    else
        return nullptr;
}

HRESULT KDbIdIdxMap_Find::AppendId(EtDbId id)
{
    auto it = m_setIds.insert(id);
    if(!it.second)
        return S_FALSE;

    m_vecIds.push_back(id);
    return S_OK;
}

STDIMP_(EtDbIdx) KDbIdIdxMap_Find::Id2Idx(EtDbId id) const
{
    for (EtDbIdx i = 0; i < m_vecIds.size(); ++i)
    {
        if (m_vecIds[i] == id)
            return i;
    }
    return INV_EtDbIdx;
}

STDIMP_(EtDbId) KDbIdIdxMap_Find::IdAt(EtDbIdx idx) const
{
    return m_vecIds[idx];
}

STDIMP_(EtDbIdx) KDbIdIdxMap_Find::Count() const
{
    return m_vecIds.size();
}

STDIMP_(BOOL) KDbIdIdxMap_Find::IsEntire() const
{
    return FALSE;
}

KDbNormalCellValInvalidInfo::KDbNormalCellValInvalidInfo(EtDbId recordId, EtDbId fieldId, ks_wstring expectedVal, HRESULT errName)
    :m_recordId(recordId),
    m_fieldId(fieldId),
    m_expectedVal(expectedVal),
    m_errName(errName)
{

}

KDbNormalCellValInvalidInfo::~KDbNormalCellValInvalidInfo()
{

}

void KDbNormalCellValInvalidInfo::SerialContent(binary_wo::VarObj& resObj)
{
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    EtDbIdStr buf;
    binary_wo::VarObj item = resObj.add_item_struct();
    {
        pCtx->EncodeEtDbId(m_recordId, &buf);
        item.add_field_str("recordId", buf);
    }

    {
        pCtx->EncodeEtDbId(m_fieldId, &buf);
        item.add_field_str("fieldId", buf);
    }

    item.add_field_str("expectedVal", m_expectedVal.c_str());
    item.add_field_str("errName", GetErrWideString(m_errName));
}

void KDbNormalCellValInvalidInfo::SerialContent(ISerialAcceptor *acpt)
{
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    EtDbIdStr buf;
    wo::sa::Leave invalidCellsInfo(wo::sa::enterStruct(acpt, nullptr));
    {
        pCtx->EncodeEtDbId(m_recordId, &buf);
        acpt->addString("recordId", buf);
    }

    {
        pCtx->EncodeEtDbId(m_fieldId, &buf);
        acpt->addString("fieldId", buf);
    }

    acpt->addString("expectedVal", m_expectedVal.c_str());
    acpt->addString("errName", GetErrWideString(m_errName));
}

/////////////////////////////////////////////////////////////////////////////
KDbRecordCellValInvalidInfo::KDbRecordCellValInvalidInfo(EtDbId recordId, ks_wstring fieldKey, bool bPreferId, ks_wstring expectedVal, HRESULT errName)
    :m_recordId(recordId),
    m_fieldKey(fieldKey),
    m_bPreferId(bPreferId),
    m_expectedVal(expectedVal),
    m_errName(errName)
{

}

KDbRecordCellValInvalidInfo::~KDbRecordCellValInvalidInfo()
{

}

void KDbRecordCellValInvalidInfo::SerialContent(binary_wo::VarObj& resObj)
{
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    binary_wo::VarObj item = resObj.add_item_struct();
    {
        EtDbIdStr buf;
        pCtx->EncodeEtDbId(m_recordId, &buf);
        item.add_field_str("recordId", buf);
    }

    item.add_field_str("fieldKey", m_fieldKey.c_str());
    item.add_field_bool("preferId",m_bPreferId);
    item.add_field_str("expectedVal", m_expectedVal.c_str());
    item.add_field_str("errName", GetErrWideString(m_errName));
}

void KDbRecordCellValInvalidInfo::SerialContent(ISerialAcceptor *acpt)
{
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    wo::sa::Leave invalidCellsInfo(wo::sa::enterStruct(acpt, nullptr));
    {
        EtDbIdStr buf;
        pCtx->EncodeEtDbId(m_recordId, &buf);
        acpt->addString("recordId", buf);
    }

    acpt->addString("fieldKey", m_fieldKey.c_str());
    acpt->addBool("preferId",m_bPreferId);
    acpt->addString("expectedVal", m_expectedVal.c_str());
    acpt->addString("errName", GetErrWideString(m_errName));
}

/////////////////////////////////////////////////////////////////////////////
KDbSetCellValInvalidInfoCollector::KDbSetCellValInvalidInfoCollector(UINT sheetStId)
    :m_sheetStId(sheetStId)
{

}

KDbSetCellValInvalidInfoCollector::~KDbSetCellValInvalidInfoCollector()
{
    for(int i = 0; i < m_cellInfos.size(); ++i)
    {
        if(m_cellInfos[i])
        {
            delete m_cellInfos[i];
            m_cellInfos[i] = nullptr;
        }
    }
    m_cellInfos.clear();
}

void KDbSetCellValInvalidInfoCollector::InsertNormalCellItem(EtDbId recordId, EtDbId fieldId, ks_wstring expectedVal, HRESULT errName)
{
    m_cellInfos.push_back(new KDbNormalCellValInvalidInfo(recordId, fieldId, expectedVal, errName));
}

void KDbSetCellValInvalidInfoCollector::InsertRecordCellItem(EtDbId recordId, ks_wstring fieldKey, bool bPreferId, ks_wstring expectedVal, HRESULT errName)
{
    m_cellInfos.push_back(new KDbRecordCellValInvalidInfo(recordId, fieldKey, bPreferId, expectedVal, errName));
}

bool KDbSetCellValInvalidInfoCollector::IsEmpty()
{
    return m_cellInfos.empty();
}

int KDbSetCellValInvalidInfoCollector::GetSize()
{
    return m_cellInfos.size();
}

void KDbSetCellValInvalidInfoCollector::SerialContent(binary_wo::VarObj& invalidCellsObj)
{
    invalidCellsObj.add_field_uint32("sheetStId", m_sheetStId);
    binary_wo::VarObj invalidCellsObjArr = invalidCellsObj.add_field_array("invalidCells", binary_wo::typeStruct);
    for (size_t i = 0, count = m_cellInfos.size(); i < count; i++)
    {
        if(m_cellInfos[i])
        {
            m_cellInfos[i]->SerialContent(invalidCellsObjArr);
        }
    }
}

void KDbSetCellValInvalidInfoCollector::SerialContent(ISerialAcceptor *acpt)
{
    acpt->beginStruct();
    {
        acpt->addUint32("sheetStId", m_sheetStId);
        {
            wo::sa::Leave invalidCells(wo::sa::enterArray(acpt, "invalidCells"));
            for (size_t i = 0, count = m_cellInfos.size(); i < count; i++)
            {
                if(m_cellInfos[i])
                {
                    m_cellInfos[i]->SerialContent(acpt);
                }
            }
        }
    }
    acpt->endStruct();
}

KDbFieldValCacheGuard::KDbFieldValCacheGuard(EtDbId fldId, IDbFieldsManager *pFieldsMgr)
    :m_pFieldsMgr(pFieldsMgr)
    ,m_fldId(fldId)
{
    ks_stdptr<IDbField> spField;
    m_pFieldsMgr->GetField(m_fldId, &spField);
    if (spField)
    {
        spField->PrepareValToken();
    }
}

KDbFieldValCacheGuard::~KDbFieldValCacheGuard()
{
    ks_stdptr<IDbField> spField;
    m_pFieldsMgr->GetField(m_fldId, &spField);
    if (spField)
    {
        spField->DestroryValToken();
    }
}


} // namespace wo
