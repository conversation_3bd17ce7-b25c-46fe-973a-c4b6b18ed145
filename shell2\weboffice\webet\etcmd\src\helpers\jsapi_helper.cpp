﻿#include "etstdafx.h"
#include "et_revision_context_impl.h"
#include "jsapi_helper.h"
#include "workbook.h"
#include "varobject_helper.h"
#include "api/jsapi/jde/jside.h"
#include "api/jsapi/jde/jde_errorcode.h"
#include "api/jsapi/pub/jsapiutility.h"
#include <api/jsapi/ipc/kjsipc.h>
#include <api/jsapi/nativex/jspropertylist.h>
#include "jsapi_context.h"
#include "jsapi_record.h"
#include "jsapi/KjEtRangeEx.h"
#include "drawing/utility/shapesave.h"


class KJCRTContext {
public:
class Scope {
public:
	explicit Scope(const JSContextEnv *context) {}
	~Scope() {}
};
};
#include <api/jsapi/ksojscore/kjcexecute_mainthread_help.h>


namespace
{
    struct JSEnv
    {
        IKJSIDE* pJsIde = nullptr;
        KJSPropertyList rootPropList;
        std::unordered_map<int, QString> rootInfos;
        QString curTaskId;
        wo::KEtWorkbook* pWorkbook = nullptr;
        wo::KEtRevisionContext* pCtx = nullptr;
        std::unordered_map<ks_wstring, ks_wstring, wo::WSTR_HASH> picMap;
    };

    JSEnv* g_pJSEnv = nullptr;

	enum WpsReturnDataDes
	{
		WRDD_exception,
		WRDD_data,
		WRDD_classId,
		WRDD_fnGetProperties,
		WRDD_className,
		WRDDCount, // 新增项必须添加在此项前面
	};

	template<typename RawFn>
	HRESULT ExecuteWraper(
		RawFn fn)
	{
		HRESULT hr = E_FAIL;
#if !defined(X_OS_WINDOWS) && !defined(X_CPU_LOONGARCH64)
		SigEnvHelp envHelp;
		sighandler_t oldSigHandler = signal(SIGSEGV, JsapiSigHandler);
		int r = sigsetjmp(envHelp.SigEnv(), 1);
		if (r == 0)
		{
#endif
			__try
			{
				hr = fn();
			}
			__except (EXCEPTION_EXECUTE_HANDLER)
			{
				hr = JDE_E_RUNTIME_ERROR;
			}
#if !defined(X_OS_WINDOWS) && !defined(X_CPU_LOONGARCH64)
		}
		else
		{
			hr = JDE_E_RUNTIME_ERROR;
		}
		signal(SIGSEGV, oldSigHandler);
#endif
		return hr;
	}

	bool ResolveResult(HRESULT hr, const WCHAR* name, KJSVariant &exception)
	{
		if (SUCCEEDED(hr))
			return true;
		g_pJSEnv->pJsIde->GetExceptionMsg(hr, name, exception);
		return !exception.IsStringType();
	}

	struct JsApiIpcRpcCallHeader
	{
		int32 index = 0;
		int32 nSize = 0;
		int32 nArgs = 0;
		bool bReturnObj = false;
	};

	void Core2Disp(IKCoreObject* core, IDispatch** prop)
	{
		if (core == NULL)
			return;

		IKCoreNotify* notify = core->GetCoreNotify(kso_Shell_Api);
		if (notify)
			notify->QueryInterface(IID_IDispatch, (void**)prop);
	}
}

struct JsApiIpcRpcCallArg
{
	int32 nArgIndex = -1;
	int32 nObjIndex = -1;
};

namespace wo
{
JSTaskScope::JSTaskScope(QString& taskId)
{
	m_last = g_pJSEnv->curTaskId;
	g_pJSEnv->curTaskId = taskId;
}
JSTaskScope::~JSTaskScope()
{
    g_pJSEnv->curTaskId = m_last;
}

void GetClassName(const KJSVariant& classId, KJSVariant &varClassName)
{
	if (VT_I4 != V_VT(&classId))
		return;
	const int nClassId = V_I4(&classId);
	if (nClassId == INVALID_CLASSIDENTIFIER)
		return;
	JSApiHint hint;
	if (!g_pJSEnv->pJsIde->GetClassHint(nClassId, &hint))
		return;
	varClassName.AssignString(hint.strName);
}

JSRevisionContextScope::JSRevisionContextScope(wo::KEtRevisionContext* pCtx)
{
    m_pOld = g_pJSEnv->pCtx;
    g_pJSEnv->pCtx = pCtx;
}
JSRevisionContextScope::~JSRevisionContextScope()
{
    g_pJSEnv->pCtx = m_pOld;
}

HRESULT GetInMainThread(bool bCefEngine,
	const JSContextEnv& jsEnv,
	int nAttributeIdx,
	JSInputData inputData,
	KJSVariant& retval,
	JSApiObject *pJSApiObj,
	KJSVariant& exception)
{
    auto iter = g_pJSEnv->rootInfos.find(nAttributeIdx);
    if (iter == g_pJSEnv->rootInfos.end())
    {
        exception = "inner error: attribute idx";
        return S_OK;
    }
    LPCWCHAR name = krt::utf16(iter->second);
    KJSVariantList retList;
    g_pJSEnv->pJsIde->GetGlobalProperty(name, &retList);
    exception = retList[0];
    retval = retList[1];
    pJSApiObj->nCreate = retList[2].GetInt();
    pJSApiObj->fnGetProperties = (FnJSGetProperties)retList[3].GetByRef();
	return S_OK;
}

HRESULT SetInMainThread(const JSContextEnv& jsEnv,
	int nAttributeIdx,
	JSInputData inputData,
	KJSVariant& value,
	KJSVariant& exception)
{
    auto iter = g_pJSEnv->rootInfos.find(nAttributeIdx);
    if (iter == g_pJSEnv->rootInfos.end())
    {
        exception = "inner error: attribute idx";
        return S_OK;
    }
    LPCWCHAR name = krt::utf16(iter->second);
    JSApiArguments args;
    args.nCount = 1;
    args.pItems = &value;
    KJSVariantList retList;
    g_pJSEnv->pJsIde->SetGlobalProperty(name, &args, &retList);
    exception = retList[0];
	return S_OK;
}

HRESULT ExecuteInMainThread(bool bCefEngine,
	const JSContextEnv& jsEnv,
	int nAttributeIdx,
	JSInputData inputData,
	JSApiArguments& arguments,
	KJSVariant& retval,
	JSApiObject *pJSApiObj,
	KJSVariant& exception)
{
    auto iter = g_pJSEnv->rootInfos.find(nAttributeIdx);
    if (iter == g_pJSEnv->rootInfos.end())
    {
        exception = "inner error: attribute idx";
        return S_OK;
    }
    LPCWCHAR name = krt::utf16(iter->second);
    KJSVariantList retList;
    g_pJSEnv->pJsIde->ExecuteGlobalFunction(name, &arguments, &retList);
    exception = retList[0];
    retval = retList[1];
    pJSApiObj->nCreate = retList[2].GetInt();
    pJSApiObj->fnGetProperties = (FnJSGetProperties)retList[3].GetByRef();
	return S_OK;
}

void CallDefaultMemberExcept(const JsApiIpcRpcCall &rpcCall,
	KJSVariantList &valueList,
	KJSVariant &varException,
	KJSVariant &valRet,
	KJSVariant &varClassIdentifier,
	KJSVariant &varGetProperties)
{
	ks_stdptr<IDispatch> spDisp = (IUnknown *)rpcCall.pObj;
	if (!spDisp)
		return;
	int nSize = valueList.size();

	std::vector<KFakeCopyVaraint> varParamsVec(nSize);
	for (int i = 0; i < nSize; ++i)
		varParamsVec[i] = valueList[i];

	DISPID nameArgs[10] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
	DISPPARAMS dispParams = {varParamsVec.data(), nameArgs, UINT(nSize), UINT(nSize)};
	auto lambda = [&]()->HRESULT {
		HRESULT hr = spDisp->Invoke(DISPID_VALUE, IID_NULL, LOCALE_SYSTEM_DEFAULT,
									DISPATCH_METHOD | DISPATCH_PROPERTYGET | DISPATCH_PROPERTYPUT,
									&dispParams, &valRet, NULL, NULL);
		if (DISP_E_BADPARAMCOUNT == hr)
		{
			std::vector<KFakeCopyVaraint> varParamsVecR(nSize);
			for (int i = 0; i < nSize; ++i)
				varParamsVecR[nSize - i - 1] = valueList[i];
			DISPID nameArgsR[1] = {-3};
			DISPPARAMS dispParamsR = {varParamsVecR.data(), nameArgsR, UINT(nSize), UINT(1)};
			spDisp->Invoke(DISPID_VALUE, IID_NULL, LOCALE_SYSTEM_DEFAULT,
						DISPATCH_METHOD | DISPATCH_PROPERTYPUT, &dispParamsR, &valRet, NULL, NULL);
		}
		return hr;
	};

	HRESULT hr = ExecuteWraper(lambda);
	if (!ResolveResult(hr, __X("CallAsFunction"), varException))
		return;

	if (valRet.IsInterfaceType())
	{
		JSApiObject apiObj;
		g_pJSEnv->pJsIde->GetIDispatchApiObjFuncs(valRet, &apiObj);
		varClassIdentifier = apiObj.nCreate;
		varGetProperties = apiObj.fnGetProperties;
	}
}

HRESULT ValueOfToString(const JsApiIpcRpcCall &rpcCall,
	bool bVauleOf,
	KJSVariant &exception,
	KJSVariant &retval,
	KJSVariant &varClassIdentifier,
	KJSVariant &varGetProperties)
{
	ks_stdptr<IDispatch> spDisp = (IUnknown *)rpcCall.pObj;
	if (!spDisp)
		return E_FAIL;
	DISPPARAMS dispParams = {NULL, NULL, 0, 0};
	auto lambda = [&]() -> HRESULT
	{
		return spDisp->Invoke(DISPID_VALUE, IID_NULL, LOCALE_SYSTEM_DEFAULT,
							  DISPATCH_METHOD | DISPATCH_PROPERTYGET, &dispParams, &retval, NULL, NULL);
	};
	HRESULT hr = ExecuteWraper(lambda);
#ifdef X_OS_WINDOWS
	if (DISP_E_MEMBERNOTFOUND == hr)
		return E_FAIL;
#else
	if (E_INVALIDARG == hr)
		return E_FAIL;
#endif
	const WCHAR *name = bVauleOf ? __X("valueOf") : __X("toString");
	if (!ResolveResult(hr, name, exception))
		return E_FAIL;
	if (!bVauleOf)
	{
		BOOL bChangeRes = retval.TryChangeType(VT_BSTR);
#ifdef X_OS_UNIX
		if (!bChangeRes && retval.IsInterfaceType())
		{
			ks_stdptr<IDispatch> spDispRet = retval.GetInterface();
			if (spDispRet)
			{
				DISPPARAMS dispParamsRet = {NULL, NULL, 0, 0};
				auto reLambda = [&]() -> HRESULT
				{
					return spDispRet->Invoke(DISPID_VALUE, IID_NULL, LOCALE_SYSTEM_DEFAULT,
											 DISPATCH_METHOD | DISPATCH_PROPERTYGET, &dispParamsRet, &retval, NULL, NULL);
				};
				hr = ExecuteWraper(reLambda);
				if (E_INVALIDARG == hr)
					return E_FAIL;
				if (!ResolveResult(hr, name, exception))
					return E_FAIL;
			}
		}
		retval.TryChangeType(VT_BSTR);
#endif
	}
	if (retval.IsInterfaceType())
	{
		JSApiObject apiObj;
		g_pJSEnv->pJsIde->GetIDispatchApiObjFuncs(retval, &apiObj);
		varClassIdentifier = apiObj.nCreate;
		varGetProperties = apiObj.fnGetProperties;
	}
	return S_OK;
}

HRESULT GetIEnum(IUnknown* pData, IEnumVARIANT** ppRet)
{
	ks_stdptr<IDispatch> spDispatch = pData;
	if (!spDispatch)
		return E_FAIL;

	DISPPARAMS dispparamsNoArgs = { 0 };
	KJSVariant result;
	HRESULT hr = spDispatch->Invoke(DISPID_NEWENUM, IID_NULL, LOCALE_USER_DEFAULT,
		DISPATCH_METHOD | DISPATCH_PROPERTYGET, &dispparamsNoArgs, &result, NULL, NULL);
	if (result.IsInterfaceType())
	{
		ks_stdptr<IEnumVARIANT> spEnum = result.GetInterface();
		if (spEnum)
		{
			*ppRet = spEnum.detach();
			return S_OK;
		}
	}

	return hr;
}

HRESULT CreateIEnum(const JsApiIpcRpcCall &rpcCall,
	KJSVariant &exception,
	KJSVariant &retval,
	KJSVariant &varClassIdentifier,
	KJSVariant &varGetProperties)
{
	ks_stdptr<IEnumVARIANT> spEnum;
	HRESULT hr = GetIEnum((IUnknown *)rpcCall.pObj, &spEnum);
	if (!spEnum)
		return hr;
	retval.Attach(spEnum.detach());
	JSApiObject apiObj;
	g_pJSEnv->pJsIde->GetIDispatchApiObjFuncs(retval, &apiObj);
	varClassIdentifier = apiObj.nCreate;
	varGetProperties = apiObj.fnGetProperties;
	return hr;
}

KJsApiHelper::KJsApiHelper(KEtRevisionContext* pCtx, KEtWorkbook* pWb, bool isQuery, IJsApiBatchResult * pBatchRes)
    : m_pCtx(pCtx), m_pWb(pWb), m_isQuery(isQuery)
	, m_pRes(pBatchRes)
{
    if (g_pJSEnv)
        g_pJSEnv->pWorkbook = pWb;
}

WebInt KJsApiHelper::Execute(const binary_wo::VarObj& param)
{
    if (!g_pJSEnv)
    {
        WOLOG_ERROR << "[JsApi] JSEnv not init";
        return WO_FAIL;
    }
	m_pWb->getApiCallTime().SetCollect(true);
    QString taskId = krt::fromUtf16(param.field_str("taskId"));
    if (param.has("release"))
    {
        binary_wo::VarObj obj = param.get("release");
        std::pair<const byte*, int32> buff = obj.as_array_buffer_s();
        if (sizeof(IUnknown*) != buff.second)
            return WO_OK;
        IUnknown* data = *reinterpret_cast<IUnknown**>(const_cast<byte*>(buff.first));
        if (g_pJSEnv->pJsIde)
            g_pJSEnv->pJsIde->ReleaseAPIObject(taskId, data);
        return WO_OK;
    }
    else if (param.has("end"))
    {
        VAR_OBJ_EXPECT_BOOL(param, "end");
        bool bEnd = param.field_bool("end");
        if (bEnd)
        {
            KJsApiContextManager::Instance()->Close(taskId);
            m_pWb->getApiCallTime().ApiEnd();
            if (g_pJSEnv->pJsIde)
                g_pJSEnv->pJsIde->ReleaseByTask(taskId);
            JsApiExecRecord record;
		    IKUserConn* pUser = m_pCtx->getUser();
    		record.userContext = JsApiExecRecord::UserContext(pUser->connID(), pUser->userID(), krt::utf16(pUser->getSessionId()), pUser->getCorePermissionId());
            record.type = JSRecordType::End;
        	VAR_OBJ_EXPECT_STRING(param, "scriptName");
			record.scriptName = param.field_str("scriptName");
			if (param.has("language"))
			    record.language = param.field_str("language");
			
			if (m_pRes)
			{
				m_pRes->setNeedCommitApi(true);
				m_pRes->addApiRecord(record);
				m_pRes->addCurApiTaskCnt(-1);
				m_pRes->MarkJSContextChange(true);
			}
			else
				m_pWb->getApiRecords().push_back(record);
            return WO_OK;
        }
    }
	m_bThrowExceptionOnError = param.has("ThrowExceptionOnError");
	JSTaskScope scope(taskId);
	bool bEnter = m_pRes && m_pRes->MarkJSContextChange(false);
	KJsApiContextScope ctxScope(m_pWb, m_pCtx, taskId, param, bEnter);
    JSRevisionContextScope rcScope(m_pCtx);

	KJSVariantList retList;
	bool bShouldSyncContext = false;
	if (param.has("rpcCalls"))
	{
		binary_wo::VarObj varBase64 = param.get("rpcCalls");
		std::pair<const byte*, int32> buff = varBase64.as_array_buffer_s();
		const byte* pBUffer = buff.first;
		if (pBUffer - buff.first > buff.second - sizeof(int32))
		{
			WOLOG_ERROR << "[JsApi] invalid rpcCalls count";
			return WO_FAIL;
		}
		const int32 nCmds = *reinterpret_cast<const int32*>(pBUffer);
		pBUffer += sizeof(int32);
		std::vector<ApiObjectInterface> apis;
		for (int32 i = 0; i < nCmds; ++i)
		{
			const JsApiIpcRpcCallHeader* pHeader = reinterpret_cast<const JsApiIpcRpcCallHeader*>(pBUffer);
			if (pBUffer - buff.first > buff.second - sizeof(JsApiIpcRpcCallHeader))
			{
				WOLOG_ERROR << "[JsApi] invalid rpcCalls header";
				return WO_FAIL;
			}
			pBUffer += sizeof(JsApiIpcRpcCallHeader);
			if (pBUffer - buff.first > buff.second - pHeader->nSize)
			{
				WOLOG_ERROR << "[JsApi] invalid rpcCalls data";
				return WO_FAIL;
			}
			std::vector<const JsApiIpcRpcCallArg*> args;
			for (int32 iArgs = 0; iArgs < pHeader->nArgs; ++iArgs)
			{
				const JsApiIpcRpcCallArg* arg = reinterpret_cast<const JsApiIpcRpcCallArg*>(pBUffer);
				pBUffer += sizeof(JsApiIpcRpcCallArg);
				args.push_back(arg);
			}
			char* pRpcCall = reinterpret_cast<char*>(const_cast<byte*>(pBUffer));
			JsApiIpcRpcCall& rpcCall = *reinterpret_cast<JsApiIpcRpcCall*>(pRpcCall);
			if (pHeader->index >= 0)
			{
				if (pHeader->index >= apis.size())
				{
					WOLOG_ERROR << "[JsApi] api index invalid" << pHeader->index;
					return WO_FAIL;
				}
				rpcCall.pObj = apis[pHeader->index];
			}
			JsApiIpcArgument* pArgs = reinterpret_cast<JsApiIpcArgument*>(pRpcCall + sizeof(JsApiIpcRpcCall));
			KJSVariantList retListItem;
			const WebInt ret = ExecuteApi(param, taskId, pRpcCall, pHeader->nSize, rpcCall, pArgs, args, apis, retListItem);
			if (ret != WO_OK)
				return ret;
			bShouldSyncContext |= (rpcCall.type == JAT_Execute || rpcCall.type == JAT_CallDefaultMember || rpcCall.type == JAT_Set);
			retList.AddItem(retListItem.size());
			retList.append(retListItem);
			if (retListItem.size() > 0 && retListItem[0].IsStringType())
			{
				WOLOG_ERROR << "[JsApi] batchCmds failed beacuse exception.";
				break;
			}
			if (pHeader->bReturnObj && (retListItem.size() < 3 || !V_BYREF(&retListItem[3])))
			{
				WOLOG_ERROR << "[JsApi] batchCmds failed of create obj";
				break;
			}
			ApiObjectInterface api = nullptr;
			if (retListItem.size() > 1)
				api = V_BYREF(&retListItem[1]);
			apis.push_back(api);
			pBUffer += pHeader->nSize;
		}
	}
	else
	{
		VAR_OBJ_EXPECT_ARRAYBUFFER(param, "rpcCall");
		binary_wo::VarObj varBase64 = param.get("rpcCall");
		std::pair<const byte*, int32> buff = varBase64.as_array_buffer_s();
		size_t nHeader = sizeof(JsApiIpcRpcCall);
		const int32 nBlob = buff.second;
		if (nBlob < nHeader)
		{
			WOLOG_ERROR << "[JsApi] JsApi parse failed";
			m_pCtx->collectInfo("behaviour_jsapi_error_request");
			return WO_FAIL;
		}
		const char* pBUffer = reinterpret_cast<const char*>(buff.first);
		char* pRpcCall = const_cast<char*>(pBUffer);
		JsApiIpcRpcCall& rpcCall = *reinterpret_cast<JsApiIpcRpcCall*>(pRpcCall);
		pBUffer += nHeader;
		JsApiIpcArgument* pArgs = reinterpret_cast<JsApiIpcArgument*>(const_cast<char*>(pBUffer));
		std::vector<const JsApiIpcRpcCallArg*> args;
		std::vector<ApiObjectInterface> apis;
		const WebInt ret = ExecuteApi(param, taskId, pRpcCall, buff.second, rpcCall, pArgs, args, apis, retList);
		if (ret != WO_OK)
			return ret;
		bShouldSyncContext |= (rpcCall.type == JAT_Execute || rpcCall.type == JAT_CallDefaultMember || rpcCall.type == JAT_Set);
	}
	if (bShouldSyncContext)
	{
		UpdateTaskContext(retList);
	}

	const int nheader = sizeof(JsApiIpcRpcReturn);
	std::unordered_map<IJSONValue*, size_t> cnestJson;
    const int retLen = nheader + CalculateSize(retList, cnestJson);
	cnestJson.clear();
    binary_wo::BinWriter* bw = m_pCtx->getHttpResponse();
    bw->addKey("response");
    byte* pBuffer = bw->reserveArrayBuffer(retLen);
    if (!pBuffer)
    {
        WOLOG_ERROR << "[JsApi] reserveArrayBuffer failed" << QString::number(retLen);
		m_pCtx->collectInfo("behaviour_jsapi_oom");
        return WO_FAIL;
    }
    JsApiIpcRpcReturn rpcReturn;
    rpcReturn.nCount = retList.size();
    memcpy(pBuffer, &rpcReturn, nheader);
    pBuffer += nheader;
    ValueListToArguments(retList, pBuffer, cnestJson);
    return WO_OK;
}

WebInt KJsApiHelper::ExecuteApi(const binary_wo::VarObj& param, const QString& taskId,
	const char* pBuffer, int nSize, const JsApiIpcRpcCall& rpcCall, JsApiIpcArgument* pArgs,
	const std::vector<const JsApiIpcRpcCallArg*>& args, const std::vector<ApiObjectInterface> &apis, KJSVariantList& retList)
{
    if (rpcCall.type == JAT_GetRootProperties)
    {
        KJsApiContextManager::Instance()->SetRunning(taskId);
    }
    else if (rpcCall.type != JAT_GetProperties && !KJsApiContextManager::Instance()->IsRunning(taskId))
    {
        SetException(__X("执行被意外中断，请检查是否正在执行其它操作"), "[JsApi]stop running", rpcCall.type, retList);
        return WO_OK;
    }
	
	if (_kso_GetWoEtSettings()->NeedCheckAllSheetsPermission())
	{
		if (rpcCall.type != JAT_GetProperties && rpcCall.type != JAT_GetRootProperties && !m_pCtx->isCurUserCanEditAllSheets())
		{
			SetException(__X("文档设置受区域权限保护，当前版本暂不支持"), "[JsApi] Not have permissions", rpcCall.type, retList);
			return WO_OK;
		}
	}

	KJSVariantList valueList;
	std::vector<IJSONValue*> nestJson;
	ArgumentsToValueList(rpcCall.nCount, pArgs, valueList, nestJson);
	nestJson.clear();
	for (auto iter = args.begin(); iter != args.end(); ++iter)
	{
		const JsApiIpcRpcCallArg* pArg = *iter;
		if (pArg->nArgIndex >= valueList.size() || pArg->nObjIndex >= apis.size())
			return WO_FAIL;
		IDispatch* pDisp = reinterpret_cast<IDispatch*>(apis[pArg->nObjIndex]);
		valueList[pArg->nArgIndex] = pDisp;
	}
	KJSVariantList retListItem;
	if (!CanExecute(rpcCall))
		return WO_FAIL;
	ExecuteApi(&rpcCall, &valueList, &retListItem);
	retList = retListItem;

	if (rpcCall.type == JAT_GetRootProperties && m_pRes)
	{
		if (m_pRes->getCurApiTaskCnt() == 0)
			m_pRes->setDependCommitId(m_pCtx->getCurCommitVersion());
		m_pRes->addCurApiTaskCnt(1);
	}

	QByteArray blob(pBuffer, nSize);
	RecordApi(param, taskId, blob, rpcCall, retListItem);
	return WO_OK;
}

void KJsApiHelper::SetException(PCWSTR exception, PCSTR log, int callType, KJSVariantList& retList)
{
    KJSVariant exp = exception;
    if (JAT_Set == callType)
    {
        retList.AddItem(exp);
    }
    else
    {
        retList.resize(5);
        retList[0] = exp;
    }
    WO_LOG_X(m_pCtx->getLogger(), WO_LOG_ERROR, log);
}

HRESULT KJsApiHelper::GetGlobalProperties(KJSVariantList *pRetList)
{
    //todo no need to init v8engine
    if (!g_pJSEnv->pJsIde)
    {
        IKEtApplication* ptrApp = m_pWb->GetCoreApp();
        g_pJSEnv->pJsIde = ptrApp->GetJsIDE(TRUE);
        if (!g_pJSEnv->pJsIde)
        {
            WOLOG_ERROR << "[JsApi] JsIDE is null";
			m_pCtx->collectInfo("behaviour_jsapi_idenull");
            return E_FAIL;
        }
		JSApiGetFunction fn = nullptr;
		g_pJSEnv->pJsIde->OverrideRangeEx(KjWOEtRangeEx::GetFunction, fn);
		if (!fn)
			return E_FAIL;
		JSApiObject obj;
		fn(false, &obj);
		KjWOEtRangeEx::s_nCreate = obj.nCreate;
		KjWOEtRangeEx::s_fnBaseGetProps = obj.fnGetProperties;
    }
    KJSVariantList &retList = *pRetList;
    retList.AddItem(GetInMainThread);
    retList.AddItem(SetInMainThread);
    retList.AddItem(ExecuteInMainThread);

	constexpr bool bBatchCmds = true;
	bool isPicAttachments = false;
	wo::IBookSetting *pSetting = m_pWb->GetCoreWorkbook()->GetBook()->GetWoStake()->getSetting();
	if (pSetting)
		isPicAttachments = pSetting->getIsInsertPicAsAttachments();

    if (g_pJSEnv->rootInfos.size() == 0)
    {
		g_pJSEnv->rootPropList.Clear();
		g_pJSEnv->pJsIde->GetGlobalProperties(m_pWb->GetCoreWorkbook(), &g_pJSEnv->rootPropList);
		g_pJSEnv->rootPropList.ToVariantList(retList);
		retList.AddItem(bBatchCmds);
		retList.AddItem(isPicAttachments);

		auto props = g_pJSEnv->rootPropList.GetProperties();
        for (auto iter = props.begin(); iter != props.end(); ++iter)
        {
            KJSVariant& var = std::get<0>(iter->second);
            QString name = krt::fromUtf16(V_BSTR(&var));
            g_pJSEnv->rootInfos[iter->first] = name;
        }
    }
	else
	{
		g_pJSEnv->rootPropList.ToVariantList(retList);
		retList.AddItem(bBatchCmds);
		retList.AddItem(isPicAttachments);
	}

	InitAllSheetsPermission();
	retList.AddItem(m_bHasAllSheetsPermission);
    return S_OK;
}

void KJsApiHelper::ExecuteApi(const JsApiIpcRpcCall* pRpcCall, KJSVariantList* pValueList, KJSVariantList *pRetList)
{
    if (!g_pJSEnv)
    {
        WOLOG_ERROR << "[JsApi] JSEnv not init";
        return;
    }
    const JsApiIpcRpcCall& rpcCall = *pRpcCall;
    KJSVariantList &valueList = *pValueList;
    KJSVariantList &retList = *pRetList;

	HRESULT hr = S_OK;
	switch (rpcCall.type)
	{
	case JAT_Execute:
	{
		retList.resize(WRDDCount);
		hr = ExecuteInMainThreadExcept(rpcCall, valueList,
			retList[WRDD_exception], retList[WRDD_data], retList[WRDD_classId], retList[WRDD_fnGetProperties]);
		GetClassName(retList[WRDD_classId], retList[WRDD_className]);
		break;
	}
	case JAT_Get:
	{
		ASSERT(valueList.size() == 0);

		retList.resize(WRDDCount);
		hr = GetInMainThreadExcept(rpcCall,
			retList[WRDD_exception], retList[WRDD_data], retList[WRDD_classId], retList[WRDD_fnGetProperties]);
		GetClassName(retList[WRDD_classId], retList[WRDD_className]);
		break;
	}
	case JAT_Set:
	{
		ASSERT(valueList.size() == 1);
		retList.AddItem(KJSVariant());
		hr = SetInMainThreadExcept(rpcCall, valueList[0], retList[WRDD_exception]);
		break;
	}
	case JAT_GetProperties:
	{
		retList.resize(3);
		KJSPropertyList propList;
		GetPropertiesInMainThreadExcept(rpcCall, retList[0], retList[1], retList[2], &propList);

		propList.ToVariantList(retList);
		break;
	}
	case JAT_GetRootProperties:
	{
		GetGlobalProperties(pRetList);
		break;
	}
	case JAT_CallDefaultMember:
	{
		retList.resize(WRDDCount);
		CallDefaultMemberExcept(rpcCall, valueList,
			retList[WRDD_exception], retList[WRDD_data], retList[WRDD_classId], retList[WRDD_fnGetProperties]);
		GetClassName(retList[WRDD_classId], retList[WRDD_className]);
		break;
	}
	case JAT_ToString:
	case JAT_ValueOf:
	{
		retList.resize(WRDDCount);
		ValueOfToString(rpcCall, rpcCall.type == JAT_ValueOf,
			retList[WRDD_exception], retList[WRDD_data], retList[WRDD_classId], retList[WRDD_fnGetProperties]);
		GetClassName(retList[WRDD_classId], retList[WRDD_className]);
		break;
	}
	case JAT_CreateEnum:
	{
		retList.resize(WRDDCount);
		hr = CreateIEnum(rpcCall,
			retList[WRDD_exception], retList[WRDD_data], retList[WRDD_classId], retList[WRDD_fnGetProperties]);
		GetClassName(retList[WRDD_classId], retList[WRDD_className]);
		break;
	}
	default:
		ASSERT(FALSE);
		break;
	}
	if (m_bThrowExceptionOnError)
		ResolveResult(hr, __X("API"), retList[WRDD_exception]);
}

void KJsApiHelper::RecordApi(const binary_wo::VarObj& param,
							 const QString& taskId,
                             QByteArray& blob,
                             const JsApiIpcRpcCall &rpcCall,
                             KJSVariantList &retList)
{

    switch (rpcCall.type)
    {
        case JAT_GetProperties:
        case JAT_GetRootProperties:
            // 保留三个函数地址
            retList.resize(3);
            break;
        case JAT_Get:
        case JAT_CallDefaultMember:
        case JAT_ToString:
        case JAT_ValueOf:
        {
            KJSVariant &exception = retList[0];
            if (exception.IsStringType())
                return;
            // 返回基础数据的不记录
            KJSVariant fnGetProp = retList.GetItem(3);
            KJSVariant obj = retList.GetItem(1);
            if (fnGetProp.IsEmpty() || !fnGetProp.GetByRef())
                return;
            retList.clear();
            // 保留pobj和fnGetProp
            retList.AddItem(obj);
            retList.AddItem(fnGetProp);
            break;
        }
        case JAT_Execute:
        {
            KJSVariant fnGetProp = retList.GetItem(3);
            KJSVariant obj = retList.GetItem(1);
            retList.clear();
            if (fnGetProp.IsEmpty() || !fnGetProp.GetByRef())
                break;
            retList.AddItem(obj);
            retList.AddItem(fnGetProp);
            break;
        }
        case JAT_Set:
        {
            retList.clear();
            break;
        }
    }

    int nHead = sizeof(JsApiIpcRpcReturn);
	std::unordered_map<IJSONValue*, size_t> cnestJson;
    int retLen = nHead + CalculateSize(retList, cnestJson);
	cnestJson.clear();
    QByteArray retBlob(retLen, 0);
    char* pBuffer = retBlob.data();
    JsApiIpcRpcReturn rpcReturn;
    rpcReturn.nCount = retList.size();
    memcpy(pBuffer, &rpcReturn, nHead);
    pBuffer += nHead;
    ValueListToArguments(retList, (BYTE*)pBuffer, cnestJson);

    IKUserConn* pUser = m_pCtx->getUser();
    JsApiExecRecord record;
	bool isNeedCommitApi = false;
	if (rpcCall.type == JAT_GetRootProperties)
	{
		record.scriptName = param.field_str("scriptName");
		record.type = JSRecordType::Begin;
        if (param.has("ContextInfo"))
        {
            binary_wo::VarObj ctxInfo = param.get_s("ContextInfo");
            std::pair<const byte*, int32> buff = ctxInfo.as_array_buffer_s();
            record.contextInfo = QByteArray(reinterpret_cast<const char*>(buff.first), buff.second);
        }
		if (param.has("language"))
			record.language = param.field_str("language");
		isNeedCommitApi = true;
	}
    record.userContext = JsApiExecRecord::UserContext(pUser->connID(), pUser->userID(), krt::utf16(pUser->getSessionId()), pUser->getCorePermissionId());
    record.taskId = krt::utf16(taskId);
    record.content = blob + retBlob;
	
	if (m_pRes)
	{
		m_pRes->addApiRecord(record);
		if (m_pRes->getApiRecordsSize() >= 1000)
			isNeedCommitApi = true;
		m_pRes->setNeedCommitApi(isNeedCommitApi);
	}
	else
	{
		m_pWb->getApiRecords().push_back(record);
	}
}

bool KJsApiHelper::CanExecute(const JsApiIpcRpcCall& rpcCall)
{
	if (!m_isQuery)
		return true;

	switch (rpcCall.type)
	{
		case JAT_GetProperties:
        case JAT_Get:
		case JAT_ToString:
		case JAT_ValueOf:
			return true;
	}
	return false;
}

void KJsApiHelper::GetCurrentContext(std::vector<ks_stdptr<IDispatch>>& apis)
{
	ks_stdptr<IKCoreObject> spDispatch;
	m_pWb->GetCoreApp()->get_ActiveSheet(&spDispatch);
	ks_stdptr<IDispatch> spActiveSheetApi;
	Core2Disp(spDispatch, &spActiveSheetApi);
	apis.push_back(spActiveSheetApi);

	ks_stdptr<_Worksheet> spWorksheet;
	if (spDispatch)
		spDispatch->QI(_Worksheet, &spWorksheet);

	ks_stdptr<Range> spSelectionRange;
	if (spWorksheet)
	{
		ks_stdptr<IKRanges> spRanges;
		spWorksheet->GetActiveWorksheetView()->GetSelectionRange(&spRanges);
		spWorksheet->GetRangeByData(spRanges, &spSelectionRange);
	}
	ks_stdptr<IDispatch> spSelectionRangeApi;
	Core2Disp(spSelectionRange, &spSelectionRangeApi);
	apis.push_back(spSelectionRangeApi);

	ks_stdptr<IKCoreObject> spSelection;
	m_pWb->GetCoreApp()->get_SelectionV9(0, (IKCoreObject**)(&spSelection));
	ks_stdptr<etoldapi::Range> spRange = spSelection;
	ks_stdptr<IDispatch> spSelectionApi;
	Core2Disp(spSelection, &spSelectionApi);
	apis.push_back(spSelectionApi);
}

void KJsApiHelper::UpdateTaskContext(KJSVariantList &retList)
{
    std::vector<ks_stdptr<IDispatch>> apis;
    GetCurrentContext(apis);
    std::vector<IDispatch*> objs;
    for (size_t i = 0; i < apis.size(); ++i)
    {
        AddObject(apis[i], retList);
        objs.push_back(apis[i]);
    }

    JsApiExecRecord record;
    record.type = JSRecordType::UpdateContext;
    record.content = QByteArray(reinterpret_cast<const char*>(objs.data()), objs.size() * sizeof(IDispatch*));
    m_pWb->getApiRecords().push_back(record);
}

void KJsApiHelper::GainObjMapContext(std::unordered_map<ApiObjectInterface, ks_stdptr<IDispatch>>& objMap, const void* pData, int nLen)
{
    if (!g_pJSEnv)
    {
        WOLOG_ERROR << "[JsApi] JSEnv not init";
        return;
    }
    const ApiObjectInterface* res = reinterpret_cast<const ApiObjectInterface*>(pData);
    std::vector<ks_stdptr<IDispatch>> apis;
    GetCurrentContext(apis);
    for(int i = 0, cnt = std::min((int)apis.size(), nLen); i < cnt; ++i)
    {
        ApiObjectInterface item = *(res + i);
        objMap[item] = apis[i];
    }
}

void KJsApiHelper::AddObject(IDispatch* pDisp, KJSVariantList &retList)
{
	KJSVariant valRet(pDisp);
	JSApiObject apiObj;
	g_pJSEnv->pJsIde->GetIDispatchApiObjFuncs(valRet, &apiObj);
	KJSVariant varClassIdentifier(apiObj.nCreate);
	KJSVariant varGetProperties(apiObj.fnGetProperties);
	KJSVariant className;
	GetClassName(varClassIdentifier, className);

	retList.AddItem(KJSVariant());
	retList.AddItem(valRet);
	retList.AddItem(varClassIdentifier);
	retList.AddItem(varGetProperties);
	retList.AddItem(className);
}

void KJsApiHelper::InitEnv()
{
    if (!g_pJSEnv)
        g_pJSEnv = new JSEnv();
}

KEtRevisionContext* KJsApiHelper::CurrentContext()
{
    if (!g_pJSEnv)
    {
        WOLOG_ERROR << "[JsApi] JSEnv not init";
        return NULL;
    }
    return g_pJSEnv->pCtx;
}

KEtWorkbook* KJsApiHelper::CurrentWorkbook()
{
    if (!g_pJSEnv)
    {
        WOLOG_ERROR << "[JsApi] JSEnv not init";
        return NULL;
    }
    return g_pJSEnv->pWorkbook;
}


void KJsApiHelper::Pixel2Twip(int nSrcWidth, int nSrcHeight, int &nDstWidth, int &nDesHeight)
{
	constexpr double twipPerInch = 1440.0;
	constexpr double MAX_SIZE = 11 * twipPerInch;
	double dpi = 96.0;
	if (g_pJSEnv && g_pJSEnv->pCtx && g_pJSEnv->pCtx->getUser())
		dpi = g_pJSEnv->pCtx->getUser()->getDpi();
	double w = nSrcWidth, h = nSrcHeight;
	w = w / dpi * twipPerInch;
	h = h / dpi * twipPerInch;

	if (w > MAX_SIZE || h > MAX_SIZE)
	{
		double shrinkRatio = std::min(MAX_SIZE / w, MAX_SIZE / h);
		w *= shrinkRatio;
		h *= shrinkRatio;
	}
	nDstWidth = w;
	nDesHeight = h;
}

void KJsApiHelper::AddPicUuid(PCWSTR picId, PCWSTR uuid)
{
	g_pJSEnv->picMap[picId] = ks_wstring(uuid);
}

void KJsApiHelper::GetPicUuid(PCWSTR picId, ks_wstring& uuid)
{
	if (!g_pJSEnv)
    {
        WOLOG_ERROR << "[JsApi] JSEnv not init";
        return;
    }
	auto iter = g_pJSEnv->picMap.find(picId);
    if (iter == g_pJSEnv->picMap.end())
        return;
    uuid = iter->second;
}

void KJsApiHelper::InitAllSheetsPermission()
{
	m_bHasAllSheetsPermission = m_pCtx->isCurUserCanEditAllSheets();
}

} // namespace wo

void IpcAddRefAPIObject(JSContextEnv const&, IUnknown* data)
{
    if (!g_pJSEnv)
        return;
	if (data)
		g_pJSEnv->pJsIde->AddRefAPIObject(g_pJSEnv->curTaskId, data);
	if (g_pJSEnv->pWorkbook)
		g_pJSEnv->pWorkbook->addObjectRef(static_cast<IDispatch*>(data));
}

void IpcCreateJsFunction(const JSContextEnv& jsEnv, FunctionIdentifier id, LPVOID cmp, KJSVariant& var)
{
	ASSERT(FALSE);
}

void IpcCreateCefPromise(const JSContextEnv& jsEnv, JSPromiseHandle id, KJSVariant& var)
{
	ASSERT(FALSE);
}

void IpcCreateCefFunction(const JSContextEnv& jsEnv, FunctionIdentifier id, KJSVariant& var)
{
	ASSERT(FALSE);
}