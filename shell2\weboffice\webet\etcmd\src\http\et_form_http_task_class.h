﻿#ifndef __WEBET_ET_FORM_HTTP_TASK_CLASS_H__
#define __WEBET_ET_FORM_HTTP_TASK_CLASS_H__

#include "et_http_task_class.h"

namespace wo
{

namespace FormHelp
{
void GetNameRg(IBookOp* pBookOp, IDX iName, RANGE& rg);
void SetNameRg(IBookOp* pBookOp, IDX iName, const RANGE& rg);
HRESULT GetFormRg(IBook* pBook, KEtRevisionContext*, RANGE& rg,const ks_wstring& areaName);
ks_wstring getAreaName(VarObj obj);
};

class EtFormHttpAddSheetTaskClass : public EtHttpTaskClassBase
{
public:
    explicit EtFormHttpAddSheetTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx) override;
    PCWSTR GetTag() override;
};

};
#endif // __WEBET_ET_FORM_HTTP_TASK_CLASS_H__