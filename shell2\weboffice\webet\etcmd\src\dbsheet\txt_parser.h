#ifndef __TXT_PARSER__H__
#define __TXT_PARSER__H__

#include <vector>

namespace wo
{

typedef std::vector<std::vector<ks_wstring> > txt_result_type;

class TxtParserResult
{
public:
	PCWSTR GetString(UINT row, UINT col);
	void Clear();
	void EmplaceBack(std::vector<ks_wstring>& vec);
	UINT GetColCount();
	UINT GetRowCount();
private:
	txt_result_type m_result;
	int m_colCnt = 0;
};

class TxtParser
{
	enum parse_state_type
	{
		state_str_start,
		state_field,
		state_quotation_field,
		state_escape,
		state_return
	};

public:
	TxtParser();
	~TxtParser();
	bool parse(PCWSTR, TxtParserResult&);

private:
	void beforeParse();
	void resetFlag();
	void pushFieldToLine();
	void pushCharToField(WCHAR, bool);

private:
	parse_state_type currentState;
	std::vector<ks_wstring> currentLine;
	ks_wstring currentField;
	ks_wstring currentFieldRaw;
	bool meetNewlineInQuoteMark;
};
}
#endif
