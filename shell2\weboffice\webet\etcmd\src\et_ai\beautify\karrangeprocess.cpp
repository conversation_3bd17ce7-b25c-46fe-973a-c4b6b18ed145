﻿#include "../smart_analysis/identifytable.h"
#include "applogic/et_api_convert.h"
#include "etcore/et_core_basic.h"
#include "etstdafx.h"
#include "karrangeprocess.h"
#include "kxarrangeprocessdata.h"
#include <smartparam.h>
#include "applogic/et_api_convert.h"

#define MAX_ROW_HEIGHT 8190 //内核设置行高的最大值,单位:缇
#define CHECK_PROCESSEVENTS_FREQ 200

namespace etai
{
static const QString NONE_FILL_COLOR = "#FFFFFF";
static const QString NONE_TEXT_COLOR = "#000000";
static const QString NONE_BORDER_COLOR = "#000000";

HRESULT makeEtColor(IN QString strColor, IN IKWorkbook *pWorkBook, OUT long &colorValue)
{
    if (!pWorkBook)
        return E_FAIL;

    // Todo:后续得考虑兼容值 = origin 或者 empty 的两种情况
    strColor = strColor.replace("#", "");
    QColor color(strColor.toUInt(NULL, 16));

    IBook *pBook = pWorkBook->GetBook();
    if (!pBook)
        return E_FAIL;

    EtColor etColor(ectARGB);
    etColor.setARGB(color.rgba());
    colorValue = ARGB2BGR(pBook->ToARGB(etColor, GdiAutoBkColor));
    return S_OK;
}

KArrangeProcess::KArrangeProcess(const TableRangeInfo &tableInfo, TableApplyParam *pTblApplyInfo,
                                 TableRangeStyle *pTableRangeStyle, wo::KEtRevisionContext* pCtx)
    : m_tableInfo(tableInfo), m_headRangeFontSize(11), 
      m_titleRangeFontSize(16), m_contentRangeFontSize(10),
      m_subTitleRangeFontSize(14), m_otherRangeFontSize(10), m_infoRangeFontSize(10), m_pTableRangeStyle(pTableRangeStyle),
      m_pTableApplyParam(pTblApplyInfo), m_pCtx(pCtx), m_fontName("Microsoft YaHei"), m_originTransNestLevel(0)
{
    init();
}

KArrangeProcess::~KArrangeProcess()
{
    auto loopReleaseRange = [](const std::vector<Range*>& vecRange)
    {
        for (auto iter = vecRange.begin(); iter != vecRange.end(); ++iter)
            (*iter)->Release();
    };
    loopReleaseRange(m_vecTitleRangeCell);
    loopReleaseRange(m_vecHeadRangeCell);
    loopReleaseRange(m_vecContentRangeCell);
    loopReleaseRange(m_vecSubTitleRangeCell);
    loopReleaseRange(m_vecOtherRangeCell);
    loopReleaseRange(m_vecInfoRangeCell);
}

void KArrangeProcess::initViewSize(int width, int height, double dpi)
{
    width = width / (dpi / 72);
    double wView = viewWidth2CharsWidth(width, m_spWorkbook, false);
    double hView = height / (dpi/ 72);

    wView -= 15;
    hView -= 25;
    m_viewSize.setW(wView);
    m_viewSize.setH(hView);
}

HRESULT KArrangeProcess::process()
{
    if (!m_spWorkbook || !m_pTableApplyParam)
        return E_FAIL;

    VARIANT_BOOL allowedEdit;
    m_spAllRangeCell->get_AllowEdit(&allowedEdit);
    if (!allowedEdit)
        return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;

    HRESULT hr = E_FAIL;
    hr = doPreProcess();
    if (FAILED(hr))
        return E_FAIL;

    hr = doMidProcess();
    if (FAILED(hr))
        return E_FAIL;

    doPostProcess();

    if (coreObjectIsDestroyed() || getTransNestLevel() > m_originTransNestLevel)
        return E_FAIL;

    hr = applyProcessResult();

    return hr;
}

void KArrangeProcess::resetParam(const TableRangeInfo &tableInfo, TableApplyParam *pTblApplyInfo,
                                 TableRangeStyle *pTableRangeStyle)
{
    m_tableInfo = tableInfo;
    m_pTableRangeStyle = pTableRangeStyle;
    m_pTableApplyParam = pTblApplyInfo;
    init();
}

HRESULT KArrangeProcess::resetColorOrStyle(TableRangeStyle *pTableRangeStyle)
{
    m_pTableRangeStyle = pTableRangeStyle;
    applyAtomicTableStyle();
    return coreObjectIsDestroyed() ? E_FAIL : S_OK;
}

BOOL KArrangeProcess::coreObjectIsDestroyed()
{
    return m_spWorksheet->IsDestroyed() || m_spWorkbook->IsDestroyed();
}

HRESULT KArrangeProcess::doPreProcess()
{
    initFontSizeInfo();
    return S_OK;
}

HRESULT KArrangeProcess::doMidProcess()
{
    HRESULT hr = E_FAIL;
    hr = standardProcess();

    hr = structProcess();

    return hr;
}

HRESULT KArrangeProcess::doPostProcess()
{
    if (m_pTableApplyParam->getEffectType() == AdaptScreenEffect)
        doAdaptProcess();

    return S_OK;
}

HRESULT KArrangeProcess::applyProcessResult()
{
    //真正设置单元格属性
    HRESULT hr = S_OK;
    hr = applyAtomicTableProp();
    if (FAILED(hr))
        return hr;

    // Todo 设置皮肤，本次暂不处理
    // if (coreObjectIsDestroyed() || getTransNestLevel() > m_originTransNestLevel)
    //     return;
    // applyAtomicTableStyle();

    //真正设置列宽
    if (coreObjectIsDestroyed() || getTransNestLevel() > m_originTransNestLevel)
        return E_FAIL;
    hr = syncColPresetWidth2RealWidth();
    if (FAILED(hr))
        return hr;

    //真正设置行高
    if (coreObjectIsDestroyed() || getTransNestLevel() > m_originTransNestLevel)
        return E_FAIL;
    hr = syncRowPresetHeight2RealHeight();
    return hr;
}

HRESULT KArrangeProcess::init()
{
    //初始化成员变量
    HRESULT hr = initRecData();
	m_originTransNestLevel = getTransNestLevel();
    return hr;
}

// Todo:这方法为给产品和UI同学使用来测试列宽估算误差以及打印整一列所有单元的字符列宽
void KArrangeProcess::printfColWidthTestInfo()
{
    if (m_spAllRangeCell)
    {
        ks_stdptr<Font> spRangeFont;
        m_spAllRangeCell->get_Font(&spRangeFont);
        if (spRangeFont)
        {
            QString qfontName = getFontName();
            ks_bstr fontName(krt::utf16(qfontName));
            spRangeFont->put_Name(fontName);
            KComVariant fontSize;
            fontSize.AssignDouble(m_contentRangeFontSize);
            spRangeFont->put_Size(fontSize);
        }
    }

    for (int col = m_tableInfo.allRangeInfo.colFrom; col <= m_tableInfo.allRangeInfo.colTo; col++)
    {

        double colMaxWidth = 0;
        double colSumWidth = 0;
        double colMeanWidth = 0;
        int rowCnt = m_tableInfo.allRangeInfo.rowTo - m_tableInfo.allRangeInfo.rowFrom + 1;
        std::vector<double> vecCellWidth;
        for (int row = m_tableInfo.allRangeInfo.rowFrom; row <= m_tableInfo.allRangeInfo.rowTo; row++)
        {
            // Todo:若为合并单元格 则跳过 单列的纵向合并单元格是否也要跳过呢？？？
            if (isMergeCell(row, col))
                continue;
            int cellTextWidth = 0;
            int cellCharCnt = 0;
            bool bLineBreak = false;
            std::vector<int> vecEachParaWidth;
            getCellInfo(row, col, ZoneType::Content, cellTextWidth, cellCharCnt, bLineBreak, vecEachParaWidth);
            float contentMaxCharCnt = viewWidth2CharsWidth(cellTextWidth, m_spWorkbook);
            colSumWidth += contentMaxCharCnt;
            vecCellWidth.push_back(contentMaxCharCnt);
            if (contentMaxCharCnt > colMaxWidth)
                colMaxWidth = contentMaxCharCnt;
        }
        if (rowCnt > 0)
            colMeanWidth = colSumWidth / rowCnt;
        double sqrDiv = 0;
        for (int i = 0; i < vecCellWidth.size(); ++i)
            sqrDiv += ((vecCellWidth[i] - colMeanWidth) * (vecCellWidth[i] - colMeanWidth));
        if (rowCnt > 0)
            sqrDiv = sqrDiv / rowCnt;

        ks_stdptr<Range> spColRange;
        HRESULT hr =
            getSpecifiedRange(&spColRange, m_tableInfo.allRangeInfo.rowFrom, m_tableInfo.allRangeInfo.rowTo, col, col);
        if (FAILED(hr) || !spColRange)
            return;
        KComVariant colWidth;
        spColRange->get_ColumnWidth(&colWidth);
        if (V_VT(&colWidth) == VT_R8)
        {
            double originWidth = V_R8(&colWidth);
            colWidth.AssignDouble(200);
            spColRange->put_ColumnWidth(colWidth);
            spColRange->AutoFitCol();
        }

        spColRange->get_ColumnWidth(&colWidth);
        if (V_VT(&colWidth) == VT_R8)
        {
            double autoFitWidth = V_R8(&colWidth);
        }
    }
}

void KArrangeProcess::processMergeCells()
{
    if (!m_spAtomicTable)
        return;
    int mergeCellsCnt = m_spAtomicTable->m_mergeCellsList.size();
    for (int i = 0; i < mergeCellsCnt; i++)
    {
        AtomicCells *pEachAtomicCells = m_spAtomicTable->m_mergeCellsList.at(i);
        if (!pEachAtomicCells)
            continue;
        processEachMergeCell(pEachAtomicCells);
    }
}

void KArrangeProcess::processEachMergeCell(AtomicCells *pEachAtomicCells)
{
    HRESULT hr = S_CONTINUE;

    int rowCnt = pEachAtomicCells->getRowCnt();
    int colCnt = pEachAtomicCells->getColCnt();
    bool bOneRow = pEachAtomicCells->IsOneRowCell();
    bool bOneCol = pEachAtomicCells->isOneColCell();
    MergeCellType mergeCellType = HorMergeCell;
    if (bOneRow && !bOneCol)
        mergeCellType = HorMergeCell;
    else if (!bOneRow && bOneCol)
        mergeCellType = VerMergeCell;
    else
        mergeCellType = HorAndVerMergeCell;

    //合并单元格的内容拉直所占的列宽
    int cellTextWidth = 0;
    int cellCharCnt = 0;
    bool bLineBreak = false;

    ZoneType zoneType = pEachAtomicCells->getZoneType();
    bool bContentType = (zoneType == Content);

    float contentMaxCharCnt = pEachAtomicCells->getEstWidthWithChar();

    //计算合并单元格所占的宽度
    double sumWidth = getCellWidth(pEachAtomicCells);
    double eachLineHeight = pEachAtomicCells->getCellCharHeight();

    if (HorMergeCell == mergeCellType) //横向合并
    {
        if (contentMaxCharCnt > sumWidth)
        {
            //分配列宽
            double dMoreColWidth = contentMaxCharCnt - sumWidth;
            expandMergeCellWidth(pEachAtomicCells, dMoreColWidth);
        }
    }
    else if (VerMergeCell == mergeCellType) //纵向合并
    {
        //先调列宽
        double dNewWidth = getVerMergeCellWidth(contentMaxCharCnt, rowCnt);
        if (sumWidth < dNewWidth) //现有的列宽比新列宽还大的情形 则不调整
        {
            //更新新的列宽
            presetColWidth(pEachAtomicCells->iLeft, dNewWidth);
        }
    }
    else if (HorAndVerMergeCell == mergeCellType)
    {
        //先调列宽
        double dNewWidth = getHorAndVerMergeCellWidth(contentMaxCharCnt, rowCnt, colCnt);
        if (dNewWidth > sumWidth)
        {
            double dMoreColWidth = dNewWidth - sumWidth;
            expandMergeCellWidth(pEachAtomicCells, dMoreColWidth);
        }
    }
    bool bEnableAlignLeft = (bContentType && (HorMergeCell == mergeCellType || HorAndVerMergeCell == mergeCellType));
    checkAndExpandMergeCellHeight(pEachAtomicCells, bEnableAlignLeft);
}

double KArrangeProcess::getCellWidth(AtomicRange *pEachAtomicRange)
{
    return m_spAtomicTable->getCellWidth(pEachAtomicRange);
}

double KArrangeProcess::getMergeCellHeight(AtomicRange *pEachAtomicRange)
{
    return m_spAtomicTable->getMergeCellHeight(pEachAtomicRange);
}

double KArrangeProcess::estimatedCellHeight(const QVector<int> &eachParaTextWidthVec, double dCurWidth,
                                            double eachLineHeight, bool bTextOverflow /*= false*/)
{
    int sumLineCnt = 0;
    if (dCurWidth < 0)
        return eachLineHeight;
    for (int i = 0; i < eachParaTextWidthVec.size(); i++)
    {
        // Todo:后续得考虑对这个viewWidth2CharsWidth方法的依赖去掉
        float eachParaTextWidthCharCnt = viewWidth2CharsWidth(eachParaTextWidthVec[i], m_spWorkbook);
        // Todo:目前这种算法估算高度会有误差(尤其是在英文放不下的情况)
        int lineCnt = 1;
        if (!bTextOverflow)
        {
            lineCnt = ceil(eachParaTextWidthCharCnt / dCurWidth);
            if (lineCnt < 1)
                lineCnt = 1;
        }
        sumLineCnt += lineCnt;
    }
    //最少也要为一行
    if (sumLineCnt < 1)
        sumLineCnt = 1;
    return eachLineHeight * sumLineCnt;
}

void KArrangeProcess::checkAndExpandMergeCellHeight(AtomicCells *pEachAtomicCells, bool bEnableAlignLeft)
{
    return m_spAtomicTable->checkAndExpandMergeCellHeight(pEachAtomicCells, bEnableAlignLeft);
}

double KArrangeProcess::getCustomCellEstimatedHeight(AtomicRange *pEachAtomicRange, double eachLineHeight,
                                                     const QVector<int> &eachParaTextWidthVec, bool bTextOverflow)
{
    double sumWidth = getCellWidth(pEachAtomicRange);
    double calcSumHeight = estimatedCellHeight(eachParaTextWidthVec, sumWidth, eachLineHeight, bTextOverflow);
    return calcSumHeight;
}

double KArrangeProcess::getVerMergeCellWidth(IN const double dWidth, IN const int iRow)
{
    if (iRow <= 10)
    {
        if (dWidth <= 10)
            return dWidth;
        else if (dWidth <= 32)
        {
            if (iRow <= 5)
                return dWidth / 2;
            else
                return dWidth / 3;
        }
        else if (dWidth <= 50)
        {
            if (iRow <= 5)
                return dWidth / 3;
            else
                return dWidth / 4;
        }
        else
        {
            // Todo:按照单个单元格的内容列宽规则调整
            return getColMaxWidthWithChar();
        }
    }
    else
    {
        if (dWidth <= 50)
            return dWidth / 3;
        else
        {
            // Todo:按照单个单元格的内容列宽规则调整
            return getColMaxWidthWithChar();
        }
    }
}

double KArrangeProcess::getHorAndVerMergeCellWidth(double dWidth, int iRow, int iCol)
{
    if (dWidth <= 10 * iCol)
        return dWidth;
    else if (dWidth <= 32 * iCol)
    {
        if (iRow <= 5)
            return dWidth / 2;
        else
            return dWidth / 3;
    }
    else if (dWidth <= 50 * iCol)
    {
        if (iRow <= 5)
            return dWidth / 3;
        else
            return dWidth / 4;
    }
    else
    {
        // Todo:按照单个单元格的内容列宽规则调整
        return getColMaxWidthWithChar();
    }
}

void KArrangeProcess::expandMergeCellWidth(AtomicRange *pEachAtomicRange, double dMoreColWidth)
{
    HRESULT hr = S_CONTINUE;
    int colCnt = pEachAtomicRange->getColCnt();
    if (colCnt <= 0)
        return;

    QMap<int, bool> bColFinishAllocateWidth; //记录对每一列是否已完成分配空间
    QMap<int, double> colNewWidthMap;        //列的最终宽度

    for (int colIdx = pEachAtomicRange->iLeft; colIdx <= pEachAtomicRange->iRight; colIdx++)
    {
        bColFinishAllocateWidth.insert(colIdx, false);
        double dWidth = getPresetColWidth(colIdx);
        colNewWidthMap.insert(colIdx, dWidth);
    }
    double dColMaxWidthWithChar = getColMaxWidthWithChar();
    while (colCnt > 0 && dMoreColWidth > 0.1)
    {
        double dEachMoreColWidth = dMoreColWidth / colCnt;
        for (int colIdx = pEachAtomicRange->iLeft; colIdx <= pEachAtomicRange->iRight; colIdx++)
        {
            if (bColFinishAllocateWidth[colIdx])
                continue;
            if (colNewWidthMap[colIdx] >= dColMaxWidthWithChar)
            {
                bColFinishAllocateWidth[colIdx] = true;
                colCnt--;
                continue;
            }

            double newWidth = colNewWidthMap[colIdx] + dEachMoreColWidth;
            if (newWidth >= dColMaxWidthWithChar)
            {
                double realAddWidth = dColMaxWidthWithChar - colNewWidthMap[colIdx];
                dMoreColWidth -= realAddWidth;
                colNewWidthMap[colIdx] = dColMaxWidthWithChar;
                bColFinishAllocateWidth[colIdx] = true;
                colCnt--;
            }
            else
            {
                dMoreColWidth -= dEachMoreColWidth;
                colNewWidthMap[colIdx] += dEachMoreColWidth;
            }
        }
    }

    for (int colIdx = pEachAtomicRange->iLeft; colIdx <= pEachAtomicRange->iRight; colIdx++)
    {
        double dWidth = colNewWidthMap[colIdx];
        presetColWidth(colIdx, dWidth);
    }
}

void KArrangeProcess::expandMergeCellHeight(AtomicRange *pEachAtomicRange, double dMoreRowHeight)
{
    return m_spAtomicTable->expandMergeCellHeight(pEachAtomicRange, dMoreRowHeight);
}

bool KArrangeProcess::isMergeCell(int row, int col)
{
    ks_stdptr<Range> spRangeCell;
    HRESULT hr = getSpecifiedCell(row, col, &spRangeCell);
    if (FAILED(hr) || !spRangeCell)
        return false;

    KCOMPTR(Range) spMerge;
    hr = spRangeCell->get_MergeArea(&spMerge);
    if (SUCCEEDED(hr) && spMerge)
    {
        long nCellCnt = 0;
        spMerge->get_Count(&nCellCnt);
        if (nCellCnt > 1)
        {
            return true;
        }
    }
    return false;
}

void KArrangeProcess::processCellImgFmla()
{
    HRESULT hr = S_CONTINUE;
    if (!m_spAtomicTable)
        return;
    size_t hasImgFmlaCnt = m_spAtomicTable->m_imgFmlaCellsList.size();
    for (size_t i = 0; i < hasImgFmlaCnt; i++)
    {
        AtomicRange *pAtomicRange = m_spAtomicTable->m_imgFmlaCellsList.at(i);
        if (!pAtomicRange)
            continue;
        double dWidth = getCellWidth(pAtomicRange);
        double dHeight = getMergeCellHeight(pAtomicRange);
        if (dWidth < getImgCellColMinWidth())
        {
            double dMoreColWidth = getImgCellColMinWidth() - dWidth;
            expandMergeCellWidth(pAtomicRange, dMoreColWidth);
        }
        if (dHeight < getImgCellRowMinHeight()) //判断会存在遮挡情况
        {
            double dMoreRowHeight = getImgCellRowMinHeight() - dHeight;
            expandMergeCellHeight(pAtomicRange, dMoreRowHeight);
        }
    }
}

double KArrangeProcess::getImgCellColMinWidth()
{
    WholeEffectBase *pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
    if (!pWholeEffect)
        return 0;
    return pWholeEffect->getImgCellColMinWidth();
}

double KArrangeProcess::getImgCellRowMinHeight()
{
    WholeEffectBase *pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
    if (!pWholeEffect)
        return 0;
    return pWholeEffect->getImgCellRowMinHeight();
}

void KArrangeProcess::presetColWidth(int iCol, double dWidth)
{
    if (m_spAtomicTable->m_colList.find(iCol) != m_spAtomicTable->m_colList.end())
    {
        AtomicCol *pEachAtomicCol = m_spAtomicTable->m_colList.at(iCol).get();
        pEachAtomicCol->setColResultWidth(dWidth);
    }
}

double KArrangeProcess::getPresetColWidth(int iCol)
{
    return m_spAtomicTable->getPresetColWidth(iCol);
}

HRESULT KArrangeProcess::syncColPresetWidth2RealWidth()
{
    auto addSetColWidthOperator = [&](int colFrom, int colTo, double dWidth)
    {
        QJsonObject params = templateRangeJsonObject(m_tableInfo.allRangeInfo.rowFrom, m_tableInfo.allRangeInfo.rowTo, colFrom, colTo);
        params["value"] = app_helper::GetColWidthWithChars(m_spWorkbook, dWidth);
        QJsonDocument doc(params);
        m_pCtx->addBeautifyOperator(__X("range.setColumnWidth"), krt::utf16(doc.toJson(QJsonDocument::Compact)));
    };
    HRESULT hr = S_CONTINUE;
    int cnt = 0;
    double currentWidth = 0;
    int currentColFrom = m_tableInfo.allRangeInfo.colFrom;
    std::list<AtomicCol*> hiddenColList;
    for (int col = m_tableInfo.allRangeInfo.colFrom; col <= m_tableInfo.allRangeInfo.colTo; col++)
    {
        if (m_spAtomicTable->m_colList.find(col) != m_spAtomicTable->m_colList.end())
        {
            AtomicCol *pEachAtomicCol = m_spAtomicTable->m_colList.at(col).get();
            double dWidth = pEachAtomicCol->getColResultWidth();
            if (pEachAtomicCol->isHidden())
                hiddenColList.push_back(pEachAtomicCol);
            if (0 == currentWidth)
                currentWidth = dWidth;
            if (currentWidth == dWidth)
                continue;

            ks_stdptr<Range> spColRange;
            hr = getSpecifiedRange(&spColRange, m_tableInfo.allRangeInfo.rowFrom, m_tableInfo.allRangeInfo.rowTo, currentColFrom,
                                   col - 1);
            if (FAILED(hr) || !spColRange)
                continue;

            KComVariant colWidth;
            colWidth.AssignDouble(currentWidth);
            hr = spColRange->put_ColumnWidth(colWidth);
            if (FAILED(hr))
                return hr;
            addSetColWidthOperator(currentColFrom, col - 1, currentWidth);

            currentColFrom = col;
            currentWidth = dWidth;
        }
    }
    ks_stdptr<Range> spColRange;
    hr = getSpecifiedRange(&spColRange, m_tableInfo.allRangeInfo.rowFrom, m_tableInfo.allRangeInfo.rowTo, currentColFrom,
                            m_tableInfo.allRangeInfo.colTo);
    if (SUCCEEDED(hr) && spColRange)
    {
        KComVariant colWidth;
        colWidth.AssignDouble(currentWidth);
        hr = spColRange->put_ColumnWidth(colWidth);
        if (FAILED(hr))
            return hr;
        addSetColWidthOperator(currentColFrom, m_tableInfo.allRangeInfo.colTo, currentWidth);
    }
    ks_stdptr<IRowColOp> spRowColOp;
    ISheet *pSheet = m_spWorksheet->GetSheet();
    if (pSheet)
        pSheet->GetOperator(&spRowColOp);

    if (spRowColOp)
    {
        spRowColOp->BeginRowColBatchUpdate();
        for (auto it = hiddenColList.cbegin(); it != hiddenColList.cend(); ++it)
        {
            COL col = (*it)->getColIdx();
            hr = spRowColOp->SetColHidden(col, col, TRUE);
            if (FAILED(hr))
                return hr;
        }
        spRowColOp->EndRowColBatchUpdate();
    }
    return hr;
}

void KArrangeProcess::presetRowHeight(int iRow, double dHeight)
{
    return m_spAtomicTable->presetRowHeight(iRow, dHeight);
}

double KArrangeProcess::getPresetRowHeight(int iRow)
{
    return m_spAtomicTable->getPresetRowHeight(iRow);
}

HRESULT KArrangeProcess::syncRowPresetHeight2RealHeight()
{
    HRESULT hr = S_CONTINUE;
    double dMaRowHeight = MAX_ROW_HEIGHT / 20;
    int cnt = 0;
    double currentHeight = 0;
    int currentRowFrom = m_tableInfo.allRangeInfo.rowFrom;
    QList<AtomicRow *> hiddenRowList;
    for (int iRow = m_tableInfo.allRangeInfo.rowFrom; iRow <= m_tableInfo.allRangeInfo.rowTo; iRow++)
    {
        if (m_spAtomicTable->m_rowList.find(iRow) != m_spAtomicTable->m_rowList.end())
        {
            AtomicRow *pEachAtomicRow = m_spAtomicTable->m_rowList.at(iRow).get();
            double dHeight = pEachAtomicRow->getRowResultHeight();
            if (pEachAtomicRow->isHidden())
                hiddenRowList.append(pEachAtomicRow);
            if (dHeight > dMaRowHeight)
                dHeight = dMaRowHeight;
            if (0 == currentHeight)
                currentHeight = dHeight;
            if (currentHeight == dHeight)
                continue;

            KComVariant rowHeight;
            rowHeight.AssignDouble(currentHeight);

            ks_stdptr<Range> spRowRange;
            hr = getSpecifiedRange(&spRowRange, currentRowFrom, iRow - 1, m_tableInfo.allRangeInfo.colFrom,
                                   m_tableInfo.allRangeInfo.colTo);
            if (FAILED(hr) || !spRowRange)
                continue;
            hr = spRowRange->put_RowHeight(rowHeight);
            if (FAILED(hr))
                return hr;

            currentRowFrom = iRow;
            currentHeight = dHeight;
        }
    }
    KComVariant rowHeight;
    rowHeight.AssignDouble(currentHeight);

    ks_stdptr<Range> spRowRange;
    hr = getSpecifiedRange(&spRowRange, currentRowFrom, m_tableInfo.allRangeInfo.rowTo,
                           m_tableInfo.allRangeInfo.colFrom, m_tableInfo.allRangeInfo.colTo);
    if (SUCCEEDED(hr) && spRowRange)
    {
        hr = spRowRange->put_RowHeight(rowHeight);
        if (FAILED(hr))
            return hr;
    }
    ks_stdptr<IRowColOp> spRowColOp;
    ISheet *pSheet = m_spWorksheet->GetSheet();
    if (pSheet)
        pSheet->GetOperator(&spRowColOp);

    if (spRowColOp)
    {
        spRowColOp->BeginRowColBatchUpdate();
        for (auto it = hiddenRowList.cbegin(); it != hiddenRowList.cend(); ++it)
        {
            ROW row = (*it)->getRowIdx();
            hr = spRowColOp->SetRowHidden(row, row, TRUE);
            if (FAILED(hr))
                return hr;
        }
        spRowColOp->EndRowColBatchUpdate();
    }
    return hr;
}

void KArrangeProcess::restoreColHidden(int iCol)
{
    VARIANT varEmpty;
    V_VT(&varEmpty) = VT_ERROR;
    V_ERROR(&varEmpty) = DISP_E_PARAMNOTFOUND;
    ks_stdptr<etoldapi::Range> spCols;
    HRESULT hr = m_spAllRangeCell->get_Columns(&spCols);

    if (SUCCEEDED(hr) && spCols)
    {
        long colIdx = iCol - m_tableInfo.allRangeInfo.colFrom + 1;
        KComVariant varCol(colIdx, VT_I4);
        KComVariant var;
        hr = spCols->get_Item(varCol, varEmpty, &var);
        if (FAILED(hr))
            return;
        ks_stdptr<Range> spCol = KSmartParam(var).GetInterfaceValue();
        if (!spCol)
            return;
        KComVariant varHidden;
        varHidden.AssignBOOL(TRUE);
        spCol->put_Hidden(varHidden);
    }
}

HRESULT KArrangeProcess::applyAtomicTableProp()
{
    HRESULT hr = S_CONTINUE;
    if (!m_spAtomicTable)
        return E_FAIL;
    if (m_pTableApplyParam->getApplyFont())
        hr = applyAtomicTableFontProp();
    if (FAILED(hr))
        return hr;

    hr = applyAtomicTableAlignProp();
    if (FAILED(hr))
        return hr;

    int cnt = 0;
    for (auto iter = m_spAtomicTable->m_colList.begin(); iter != m_spAtomicTable->m_colList.end(); ++iter)
    {
        AtomicCol *pEachAtomicCol = iter->second.get();
        if (!pEachAtomicCol)
            continue;
        const std::vector<AtomicCells *>& vecCells = pEachAtomicCol->vecCells;
        double rowMaxHeight = 0;
        ETHAlign colHAlign = pEachAtomicCol->getHAlign();
        if (colHAlign != etHAlignCenter)
        {
            ks_stdptr<Range> spColRange;
            ISheet* pSheet = m_spWorksheet->GetSheet();
            if (!pSheet)
                continue;
            getSpecifiedRange(&spColRange, 0, pSheet->GetBMP()->cntRows - 1, pEachAtomicCol->getColIdx(), pEachAtomicCol->getColIdx());
            if (spColRange)
            {
                hr = spColRange->put_HorizontalAlignment(colHAlign);
                if (FAILED(hr))
                    return hr;
                QJsonObject params = templateSetXfJsonObject(
                        0, pSheet->GetBMP()->cntRows - 1, pEachAtomicCol->getColIdx(), pEachAtomicCol->getColIdx());
                QJsonObject xf;
                HALIGNMENT alcH = haGeneral;
                ETHAlign_HALIGNMENT(colHAlign, alcH);
                xf["alcH"] = alcH;
                params["xf"] = xf;
                QJsonDocument doc(params);
                m_pCtx->addBeautifyOperator(__X("multiRange.setXf"), krt::utf16(doc.toJson(QJsonDocument::Compact)));
            }
        }
        for (size_t i = 0; i < vecCells.size(); i++)
        {
            AtomicCells *pEachAtomicCells = vecCells.at(i);
            if (!pEachAtomicCells || pEachAtomicCells->getZoneType() == Empty)
                continue;

            ks_stdptr<Range> spRange;
            hr = getSpecifiedRange(&spRange, pEachAtomicCells->iTop, pEachAtomicCells->iTop,
                                    pEachAtomicCells->iLeft, pEachAtomicCells->iLeft);
            if (spRange)
            {
                //设置特殊的对齐
                ETHAlign hAlign = pEachAtomicCells->getHAlign();
                ETVAlign vAlign = pEachAtomicCells->getVAlign();
                bool bWrapText = pEachAtomicCells->isWrapText();
                bool bNeedAddSetXfOperator = 
                    (colHAlign != hAlign && pEachAtomicCells->getZoneType() != Content && hAlign != etHAlignCenter) ||
                    etVAlignCenter != vAlign ||
                    !bWrapText;
                QJsonObject params;
                QJsonObject xf;
                if (bNeedAddSetXfOperator)
                {
                    params = templateSetXfJsonObject(
                        pEachAtomicCells->iTop, pEachAtomicCells->iTop, pEachAtomicCells->iLeft, pEachAtomicCells->iLeft);
                }

                if (colHAlign != hAlign && pEachAtomicCells->getZoneType() != Content)
                {
                    hr = spRange->put_HorizontalAlignment(hAlign);
                    if (FAILED(hr))
                        return hr;
                    HALIGNMENT alcH = haGeneral;
                    ETHAlign_HALIGNMENT(hAlign, alcH);
                    xf["alcH"] = alcH;
                }

                if (etVAlignCenter != vAlign)
                {
                    hr = spRange->put_VerticalAlignment(vAlign);
                    if (FAILED(hr))
                        return hr;
                    VALIGNMENT alcV = vaCenter;
                    ETVAlign_VALIGNMENT(vAlign, alcV);
                    xf["alcV"] = alcV;
                }

                if (!pEachAtomicCells->isWrapText())
                {
                    KComVariant vWrap;
                    vWrap.AssignBOOL(FALSE);
                    hr = spRange->put_WrapText(vWrap);
                    if (FAILED(hr))
                        return hr;
                    xf["wrap"] = false;
                }

                if (bNeedAddSetXfOperator)
                {
                    params["xf"] = xf;
                    QJsonDocument doc(params);
                    m_pCtx->addBeautifyOperator(__X("multiRange.setXf"), krt::utf16(doc.toJson(QJsonDocument::Compact)));
                }

                if (pEachAtomicCells->isModifiedText())
                {
                    ks_wstring modifiedText = pEachAtomicCells->getModifiedText();
                    ks_bstr newText(modifiedText.c_str());
                    KComVariant newVal;
                    newVal.AssignBSTR(newText);
                    hr = spRange->put_Value(etRangeValueDefault, newVal);
                    if (FAILED(hr))
                        return hr;
                    QJsonObject params = templateRangeJsonObject(
                        pEachAtomicCells->iTop, pEachAtomicCells->iTop, pEachAtomicCells->iLeft, pEachAtomicCells->iLeft);
                    params["formula"] = krt::fromUtf16(modifiedText.c_str());
                    QJsonDocument doc(params);
                    m_pCtx->addBeautifyOperator(__X("range.setFormula"), krt::utf16(doc.toJson(QJsonDocument::Compact)));
                }
            }
            if (cnt++ == CHECK_PROCESSEVENTS_FREQ)
            {
                cnt = 0;
                if (coreObjectIsDestroyed() || getTransNestLevel() > m_originTransNestLevel)
                    return E_FAIL;
            }
        }
    }
    return hr;
}

HRESULT KArrangeProcess::applyRangeFontProp(const std::vector<Range*>& vecRange, const std::vector<ES_CUBE>& vecCube, double fontSize, bool bBold)
{
    KComVariant vBold;
    vBold.AssignBOOL(TRUE);
    auto addSetFontPropOperator = [&](ES_CUBE tableInfo, QString fontName, double fontSize, bool bBold)
    {
        QJsonObject params = templateSetXfJsonObject(
            tableInfo.rowFrom, tableInfo.rowTo, tableInfo.colFrom, tableInfo.colTo);
        QJsonObject xf;
        QJsonObject font;
        if (bBold)
            font["bls"] = bBold;
        font["dyHeight"] = fontSize * 20;
        font["name"] = fontName;

        xf["font"] = font;
        params["xf"] = xf;
        QJsonDocument doc(params);
        m_pCtx->addBeautifyOperator(__X("multiRange.setXf"), krt::utf16(doc.toJson(QJsonDocument::Compact)));
    };
    auto iter = vecRange.begin();
    for (int i = 0; i < vecRange.size(); ++i)
    {
        Range* range = vecRange[i];
        if (!range)
            continue;
        ks_stdptr<Font> spFont;
        range->get_Font(&spFont);
        if (!spFont)
            continue;

        QString qFontName = getFontName();
        ks_bstr fontName(krt::utf16(qFontName));
        HRESULT hr = spFont->put_Name(fontName);
        if (FAILED(hr))
            return hr;
        KComVariant vFontSize;
        vFontSize.AssignDouble(fontSize);
        spFont->put_Size(vFontSize);
        if (bBold)
            spFont->put_Bold(vBold);

        ES_CUBE tableInfo = vecCube[i];
        addSetFontPropOperator(tableInfo, qFontName, fontSize, bBold);
    }
    return S_OK;
}

HRESULT KArrangeProcess::applyAtomicTableFontProp()
{
    HRESULT hr = S_OK;
    //标题
    hr = applyRangeFontProp(m_vecTitleRangeCell, m_tableInfo.vecTitleRangeInfo, m_titleRangeFontSize, true);
    if (FAILED(hr))
        return hr;
    //表头
    hr = applyRangeFontProp(m_vecHeadRangeCell, m_tableInfo.vecHeadRangeInfo, m_headRangeFontSize, true);
    if (FAILED(hr))
        return hr;
    //副标题
    hr = applyRangeFontProp(m_vecSubTitleRangeCell, m_tableInfo.vecSubTitleRangeInfo, m_subTitleRangeFontSize, true);
    if (FAILED(hr))
        return hr;
    //内容
    hr = applyRangeFontProp(m_vecContentRangeCell, m_tableInfo.vecContentRangeInfo, m_contentRangeFontSize, false);
    if (FAILED(hr))
        return hr;
    //信息类型
    hr = applyRangeFontProp(m_vecInfoRangeCell, m_tableInfo.vecInfoRangeInfo, m_infoRangeFontSize, false);
    if (FAILED(hr))
        return hr;

    return hr;
}

HRESULT KArrangeProcess::applyAtomicTableAlignProp()
{
    // 先做统一的格式化
    if (!m_spAllRangeCell)
        return E_FAIL;
    HRESULT hr = S_OK;
    // 居中对齐
    hr = m_spAllRangeCell->put_VerticalAlignment(etVAlignCenter);
    if (FAILED(hr))
        return hr;
    hr = m_spAllRangeCell->put_HorizontalAlignment(etHAlignCenter);
    if (FAILED(hr))
        return hr;
    // 清除缩进格式
    hr = m_spAllRangeCell->put_IndentLevel(0);
    if (FAILED(hr))
        return hr;
    // 自动换行
    KComVariant vWrap;
    vWrap.AssignBOOL(TRUE);
    hr = m_spAllRangeCell->put_WrapText(vWrap);
    if (FAILED(hr))
        return hr;

    TableRangeInfo tableInfo = m_spAtomicTable->getTaleRangeInfo();
    ES_CUBE cube = tableInfo.allRangeInfo;
    QJsonObject params = templateSetXfJsonObject(
        cube.rowFrom, cube.rowTo, cube.colFrom, cube.colTo);

    QJsonObject xf;
    HALIGNMENT alcH = haGeneral;
    VALIGNMENT alcV = vaCenter;
    ETVAlign_VALIGNMENT(etVAlignCenter, alcV);
    ETHAlign_HALIGNMENT(etHAlignCenter, alcH);
    xf["alcH"] = alcH;
    xf["alcV"] = alcV;
    xf["wrap"] = true;
    xf["indent"] = 0;
    params["xf"] = xf;
    QJsonDocument doc(params);
    m_pCtx->addBeautifyOperator(__X("multiRange.setXf"), krt::utf16(doc.toJson(QJsonDocument::Compact)));
    return hr;
}

void KArrangeProcess::applyAtomicTableStyle()
{
    if (!m_pTableRangeStyle)
        return;

    applyFillAlterPlan();
    applyFillFirstColArea();
    applyFillOtherArea();
    applyFillTblHeadArea();
}

void KArrangeProcess::applyFillAlterPlan()
{
    //判断交替方式
    FillAlterBase alterBase = m_pTableRangeStyle->getFillAlterBase();
    ES_CUBE fillAlterArea = m_tableInfo.getFillAlterArea();

    if (RowAlternation == alterBase)
    {
        TableZoneStyle *pOddRowAlterStyle = m_pTableRangeStyle->getTableRangeStyle(StyleOddRowAlter);
        if (!pOddRowAlterStyle)
            return;
        TableZoneStyle *pEvenRowAlterStyle = m_pTableRangeStyle->getTableRangeStyle(StyleEvenRowAlter);
        if (!pEvenRowAlterStyle)
            return;

        bool bOdd = true;
        int firstColIdx = m_spAtomicTable->getFirstMergeColIdx();
        int count = 0;
        for (int iRow = fillAlterArea.rowFrom; iRow <= fillAlterArea.rowTo; iRow++)
        {
            if (count == 127)
            {
                count = 0;
                if (coreObjectIsDestroyed() || getTransNestLevel() > m_originTransNestLevel)
                    return;
            }
            count++;
            // Todo:这样取合并单元格可能会比较慢，因为需要遍历，当这个表的合并单元格数目大的时候，后续看看能不能直接取
            AtomicCells *pCell = m_spAtomicTable->getMergeCell(iRow, firstColIdx);
            if (pCell) //合并单元格
            {
                ks_stdptr<Range> spRange;
                getSpecifiedRange(&spRange, pCell->iTop, pCell->iBottom, fillAlterArea.colFrom, fillAlterArea.colTo);
                if (spRange)
                {
                    if (bOdd)
                        applyRangeStyle(spRange, pOddRowAlterStyle);
                    else
                        applyRangeStyle(spRange, pEvenRowAlterStyle);
                }
                iRow = pCell->iBottom; //跳过合并单元格所占的行
            }
            else //普通单元格
            {
                ks_stdptr<Range> spRange;
                getSpecifiedRange(&spRange, iRow, iRow, fillAlterArea.colFrom, fillAlterArea.colTo);
                if (bOdd)
                    applyRangeStyle(spRange, pOddRowAlterStyle);
                else
                    applyRangeStyle(spRange, pEvenRowAlterStyle);
            }
            bOdd = !bOdd;
        }
    }
    else if (ColAlternation == alterBase)
    {
        TableZoneStyle *pOddColStyle = m_pTableRangeStyle->getTableRangeStyle(StyleOddColAlter);
        if (!pOddColStyle)
            return;
        TableZoneStyle *pEvenColStyle = m_pTableRangeStyle->getTableRangeStyle(StyleEvenColAlter);
        if (!pEvenColStyle)
            return;

        bool bOdd = true;
        int idxRow = m_spAtomicTable->getFirstHeaderRowIdx();
        for (int iCol = fillAlterArea.colFrom; iCol <= fillAlterArea.colTo; iCol++)
        {
            AtomicCells *pCell = nullptr;
            if (idxRow >= 0)
                pCell = m_spAtomicTable->getMergeCell(idxRow, iCol);
            if (pCell) //合并单元格
            {
                ks_stdptr<Range> spRange;
                getSpecifiedRange(&spRange, fillAlterArea.rowFrom, fillAlterArea.rowTo, pCell->iLeft, pCell->iRight);
                if (spRange)
                {
                    if (bOdd)
                        applyRangeStyle(spRange, pOddColStyle);
                    else
                        applyRangeStyle(spRange, pEvenColStyle);
                }
                iCol = pCell->iRight; //跳过合并单元格所占的列
            }
            else //普通单元格
            {
                ks_stdptr<Range> spRange;
                getSpecifiedRange(&spRange, fillAlterArea.rowFrom, fillAlterArea.rowTo, iCol, iCol);
                if (spRange)
                {
                    if (bOdd)
                        applyRangeStyle(spRange, pOddColStyle);
                    else
                        applyRangeStyle(spRange, pEvenColStyle);
                }
            }
            bOdd = !bOdd;
        }
    }
    else if (NoneAlternation == alterBase)
    {
        TableZoneStyle *pStyle = m_pTableRangeStyle->getTableRangeStyle(StyleNoneAlter);
        if (!pStyle)
            return;
        ks_stdptr<Range> spRange;
        getSpecifiedRange(&spRange, fillAlterArea.rowFrom, fillAlterArea.rowTo, fillAlterArea.colFrom,
                          fillAlterArea.colTo);
        if (spRange)
            applyRangeStyle(spRange, pStyle);
    }
}

void KArrangeProcess::applyFillTblHeadArea()
{
    if (coreObjectIsDestroyed() || !m_pTableRangeStyle->isHeader())
        return;

    HRESULT hr = E_FAIL;
    //表头
    ISheet *pSheet = m_spWorksheet->GetSheet();
    if (!pSheet)
        return;
    for (int i = 0; i < m_tableInfo.vecHeadRangeInfo.size(); i++)
    {
        RANGE headRangeInfo(m_tableInfo.vecHeadRangeInfo[i], pSheet->GetBMP());
        ks_stdptr<Range> spHeadRangeCell;
        m_spWorksheet->GetRangeByData(&headRangeInfo, &spHeadRangeCell);
        if (spHeadRangeCell)
        {
            TableZoneStyle *pStyle = m_pTableRangeStyle->getTableRangeStyle(StyleRowTitle);
            if (!pStyle)
                continue;
            applyRangeStyle(spHeadRangeCell, pStyle);
        }
    }
}

void KArrangeProcess::applyFillFirstColArea()
{
    if (coreObjectIsDestroyed())
        return;

    HRESULT hr = E_FAIL;
    TableZoneStyle *pStyle = m_pTableRangeStyle->getTableRangeStyle(StyleFirstCol);
    if (!pStyle)
        return;

    ES_CUBE fillAlterArea = m_tableInfo.getFillAlterArea();
    int firstColIdx = m_tableInfo.getFirstColIdx();

    ks_stdptr<Range> spRange;
    hr = getSpecifiedRange(&spRange, fillAlterArea.rowFrom, fillAlterArea.rowTo, firstColIdx, firstColIdx);
    if (spRange)
        applyRangeStyle(spRange, pStyle);
}

void KArrangeProcess::applyFillOtherArea()
{
    if (coreObjectIsDestroyed())
        return;

    //标题
    for (int i = 0; i < m_vecTitleRangeCell.size(); i++)
    {
        ks_stdptr<Range> spTitleRangeCell = m_vecTitleRangeCell[i];
        if (spTitleRangeCell)
        {
            TableZoneStyle *pStyle = m_pTableRangeStyle->getTableRangeStyle(StyleBigTitle);
            if (!pStyle)
                continue;

            applyRangeStyle(spTitleRangeCell, pStyle);
        }
    }

    //信息类型
    for (int i = 0; i < m_vecInfoRangeCell.size(); ++i)
    {
        ks_stdptr<Range> spInfoRangeCell = m_vecInfoRangeCell[i];
        if (spInfoRangeCell)
        {
            TableZoneStyle *pStyle = m_pTableRangeStyle->getTableRangeStyle(StyleInfo);
            if (!pStyle)
                continue;

            applyRangeStyle(spInfoRangeCell, pStyle);
        }
    }
}

void KArrangeProcess::applyRangeStyle(Range *pRange, TableZoneStyle *pStyle)
{
    if (!pRange || !pStyle)
        return;

    HRESULT hr = E_FAIL;

    ks_stdptr<Font> spFont;
    pRange->get_Font(&spFont);
    if (spFont)
    {
        QString strColor = pStyle->getTextColor();
        if (!strColor.isEmpty() && strColor != "origin")
        {
            if (strColor == "none")
                strColor = NONE_TEXT_COLOR;

            long newColor = 0;
            hr = makeEtColor(strColor, m_spWorkbook, newColor);
            if (SUCCEEDED(hr))
                spFont->put_Color(newColor);
        }
    }

    //填充颜色
    ks_stdptr<Interior> cellInterior;
    pRange->get_Interior(&cellInterior);
    if (cellInterior)
    {
        QString strColor = pStyle->getFillColor();
        if (!strColor.isEmpty() && strColor != "origin")
        {
            if (strColor == "none")
                strColor = NONE_FILL_COLOR;
            long newColor = 0;
            hr = makeEtColor(strColor, m_spWorkbook, newColor);
            if (SUCCEEDED(hr))
                cellInterior->put_Color(newColor);
        }
    }
    //外边框色
    ks_stdptr<etoldapi::Borders> spBorders;
    pRange->get_Borders(&spBorders);
    if (spBorders)
    {
        applyRangeBorderStyle(spBorders, etEdgeTop, pStyle);
        applyRangeBorderStyle(spBorders, etEdgeBottom, pStyle);
        applyRangeBorderStyle(spBorders, etEdgeLeft, pStyle);
        applyRangeBorderStyle(spBorders, etEdgeRight, pStyle);
        applyRangeBorderStyle(spBorders, etInsideHorizontal, pStyle);
        applyRangeBorderStyle(spBorders, etInsideVertical, pStyle);
    }
}

void KArrangeProcess::doApplyRangeBorderStyle(Border *pBorder, TableZoneStyle *pStyle, borderStyleInfo *pBorderStyle)
{
    if (!pBorder || !pStyle || !pBorderStyle)
        return;

    bool bNeedApplyColor = false;
    //线框样式
    QString strBorderStyle = pBorderStyle->getBorderStyle();
    if (strBorderStyle == "none")
        pBorder->put_LineStyle(etLineStyleNone);
    else if (strBorderStyle == "default")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etThin, etContinuous);
    }
    else if (strBorderStyle == "1")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etHairline, etContinuous);
    }
    else if (strBorderStyle == "2")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etThin, etDot);
    }
    else if (strBorderStyle == "3")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etThin, etDashDotDot);
    }
    else if (strBorderStyle == "4")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etThin, etDashDot);
    }
    else if (strBorderStyle == "5")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etThin, etDash);
    }
    else if (strBorderStyle == "6")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etThin, etContinuous);
    }
    else if (strBorderStyle == "7")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etMedium, etDashDotDot);
    }
    else if (strBorderStyle == "8")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etMedium, etSlantDashDot);
    }
    else if (strBorderStyle == "9")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etMedium, etDashDot);
    }
    else if (strBorderStyle == "10")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etMedium, etDash);
    }
    else if (strBorderStyle == "11")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etMedium, etContinuous);
    }
    else if (strBorderStyle == "12")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etThick, etContinuous);
    }
    else if (strBorderStyle == "13")
    {
        bNeedApplyColor = true;
        pBorder->put_WeightStyle(etThick, etDouble);
    }

    //线框颜色
    if (!bNeedApplyColor)
        return;

    QString strBorderColor = pBorderStyle->getBorderColor();
    if (!strBorderColor.isEmpty() && strBorderColor != "origin")
    {
        if (strBorderColor == "none")
            strBorderColor = NONE_BORDER_COLOR;
        long newColor = 0;
        HRESULT hr = makeEtColor(strBorderColor, m_spWorkbook, newColor);
        if (SUCCEEDED(hr))
            pBorder->put_Color(newColor);
    }
}

UINT KArrangeProcess::getTransNestLevel()
{
    IKTransactionTool *pTrans = m_spWorkbook->GetTransactionTool();
    if (pTrans)
        return pTrans->GetNestLevel();
    else
        return 0;
}

void KArrangeProcess::applyRangeBorderStyle(etoldapi::Borders *pBorders, ETBorderIndex borderIdx,
                                            TableZoneStyle *pStyle)
{
    if (!pBorders || !pStyle || pBorders->IsDestroyed())
        return;

    ks_stdptr<Border> border;
    pBorders->get__Default(borderIdx, &border);
    if (!border)
        return;

    borderStyleInfo *pBorderStyleInfo = pStyle->getOneBorderStyle(borderIdx);
    if (pBorderStyleInfo)
        doApplyRangeBorderStyle(border, pStyle, pBorderStyleInfo);
}

double KArrangeProcess::getColMaxWidthWithChar()
{
    WholeEffectBase *pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
    if (!pWholeEffect)
        return 0;
    return pWholeEffect->getColMaxWidthWithChar();
}

HRESULT KArrangeProcess::getSpecifiedRange(OUT Range **ppRange, IN int rowBegin, IN int rowEnd, IN int colBegin,
                                           IN int colEnd)
{
    if (!m_spWorksheet || rowBegin > rowEnd || colBegin > colEnd) //容错处理
        return E_FAIL;
    ISheet *pSheet = m_spWorksheet->GetSheet();

    if (!pSheet)
        return E_FAIL;

    RANGE rg(pSheet->GetBMP());
    rg.SetRowFromTo(rowBegin, rowEnd);
    rg.SetColFromTo(colBegin, colEnd);

    ks_stdptr<Range> spRange;
    m_spWorksheet->GetRangeByData(&rg, &spRange);

    ks_stdptr<Range> spRangeCell;
    spRange->get_Cells(&spRangeCell);
    if (!spRangeCell)
        return E_FAIL;

    *ppRange = spRangeCell.detach();

    return S_OK;
}

HRESULT KArrangeProcess::initRecData()
{
    HRESULT hr = E_FAIL;
    if (m_tableInfo.isEmptyTableInfo())
        return E_FAIL;

    m_spWorkbook = m_tableInfo.getWorkBook();
    if (!m_spWorkbook)
        return E_FAIL;

    m_spWorksheet = m_tableInfo.getWorkSheet();
    if (!m_spWorksheet)
        return E_FAIL;

    ISheet *pSheet = m_spWorksheet->GetSheet();
    if (!pSheet)
        return E_FAIL;

    //处理的整个范围
    RANGE allRangeInfo(m_tableInfo.allRangeInfo, pSheet->GetBMP());
    ks_stdptr<Range> spAllRange;
    m_spWorksheet->GetRangeByData(&allRangeInfo, &spAllRange);
    if (!spAllRange)
        return E_FAIL;
    hr = spAllRange->get_Cells(&m_spAllRangeCell);
    if (!m_spAllRangeCell)
        return E_FAIL;

    //标题范围
    for (int i = 0; i < m_tableInfo.vecTitleRangeInfo.size(); i++)
    {
        RANGE titleRangeInfo(m_tableInfo.vecTitleRangeInfo[i], pSheet->GetBMP());
        ks_stdptr<Range> spTitleRange;
        m_spWorksheet->GetRangeByData(&titleRangeInfo, &spTitleRange);
        if (!spTitleRange)
            return E_FAIL;
        ks_stdptr<Range> spRangeCell;
        hr = spTitleRange->get_Cells(&spRangeCell);
        if (!spRangeCell)
            return E_FAIL;
        m_vecTitleRangeCell.push_back(spRangeCell.detach());
    }

    //表头(又称行标题)范围
    for (int i = 0; i < m_tableInfo.vecHeadRangeInfo.size(); i++)
    {
        RANGE headRangeInfo(m_tableInfo.vecHeadRangeInfo[i], pSheet->GetBMP());
        ks_stdptr<Range> spHeadRange;
        m_spWorksheet->GetRangeByData(&headRangeInfo, &spHeadRange);
        if (!spHeadRange)
            return E_FAIL;
        ks_stdptr<Range> spRangeCell;
        hr = spHeadRange->get_Cells(&spRangeCell);
        if (!spRangeCell)
            return E_FAIL;
        m_vecHeadRangeCell.push_back(spRangeCell.detach());
    }

    //表内容范围
    for (int i = 0; i < m_tableInfo.vecContentRangeInfo.size(); i++)
    {
        RANGE contentRangeInfo(m_tableInfo.vecContentRangeInfo[i], pSheet->GetBMP());
        ks_stdptr<Range> spContentRange;
        m_spWorksheet->GetRangeByData(&contentRangeInfo, &spContentRange);
        if (!spContentRange)
            return E_FAIL;
        ks_stdptr<Range> spRangeCell;
        hr = spContentRange->get_Cells(&spRangeCell);
        if (!spRangeCell)
            return E_FAIL;
        m_vecContentRangeCell.push_back(spRangeCell.detach());
    }

    //副标题范围
    for (int i = 0; i < m_tableInfo.vecSubTitleRangeInfo.size(); i++)
    {
        RANGE subTitleRangeInfo(m_tableInfo.vecSubTitleRangeInfo[i], pSheet->GetBMP());
        ks_stdptr<Range> spSubTitleRange;
        m_spWorksheet->GetRangeByData(&subTitleRangeInfo, &spSubTitleRange);
        if (!spSubTitleRange)
            return E_FAIL;
        ks_stdptr<Range> spRangeCell;
        hr = spSubTitleRange->get_Cells(&spRangeCell);
        if (!spRangeCell)
            return E_FAIL;
        m_vecSubTitleRangeCell.push_back(spRangeCell.detach());
    }

    //其他内容 例如备注之类的
    for (int i = 0; i < m_tableInfo.vecOtherRangeInfo.size(); i++)
    {
        RANGE otherRangeInfo(m_tableInfo.vecOtherRangeInfo[i], pSheet->GetBMP());
        ks_stdptr<Range> spOtherRange;
        m_spWorksheet->GetRangeByData(&otherRangeInfo, &spOtherRange);
        if (!spOtherRange)
            return E_FAIL;
        ks_stdptr<Range> spRangeCell;
        hr = spOtherRange->get_Cells(&spRangeCell);
        if (!spRangeCell)
            return E_FAIL;
        m_vecOtherRangeCell.push_back(spRangeCell.detach());
    }
    //信息类型
    for (int i = 0; i < m_tableInfo.vecInfoRangeInfo.size(); i++)
    {
        RANGE infoRangeInfo(m_tableInfo.vecInfoRangeInfo[i], pSheet->GetBMP());
        ks_stdptr<Range> spInfoRange;
        m_spWorksheet->GetRangeByData(&infoRangeInfo, &spInfoRange);
        if (!spInfoRange)
            return E_FAIL;
        ks_stdptr<Range> spRangeCell;
        hr = spInfoRange->get_Cells(&spRangeCell);
        if (!spRangeCell)
            return E_FAIL;
        m_vecInfoRangeCell.push_back(spRangeCell.detach());
    }
    return S_OK;
}

HRESULT KArrangeProcess::initAtomicTable()
{
    if (!m_spAllRangeCell || !m_spWorksheet)
        return E_FAIL;
    m_spAtomicTable = std::make_unique<AtomicTable>(m_tableInfo.allRangeInfo, m_spWorksheet);
    if (!m_spAtomicTable)
        return E_FAIL;
    m_spAtomicTable->BuildTable(m_spAllRangeCell, m_tableInfo, m_pTableApplyParam); //暂时
    return S_OK;
}

HRESULT KArrangeProcess::standardProcess()
{
    HRESULT hr = structStandardProcess();
    return hr;
}

HRESULT KArrangeProcess::structStandardProcess()
{
    HRESULT hr = E_FAIL;

    // Todo:后续的合并也要转成描述信息，而不在这里直接设
    hr = mergeAreaProcess();
    //在处理完需要合并的区域之后再收集合并单元格的信息
    hr = initAtomicTable();

    return hr;
}

HRESULT KArrangeProcess::structProcess()
{
    HRESULT hr = E_FAIL;

    hr = processRowHAndColW();
    //处理合并单元格
    processMergeCells();

    //设置完最适合行高之后 上下进行留白处理
    hr = adjustRowHeightWithSpace();

    //处理内嵌的图片单元格所在的行高与列宽
    processCellImgFmla();

    return hr;
}

HRESULT KArrangeProcess::processRowHAndColW()
{
    HRESULT hr = E_FAIL;
    if (!m_spAllRangeCell)
        return E_FAIL;

    //调整列宽到合适的值
    adjustColumnsWidth();

    //调整行高到合适的值
    hr = adjustRowsHeight();
    return hr;
}

void KArrangeProcess::doAdaptProcess()
{
    if (!m_spAdaptScreenProxy)
        m_spAdaptScreenProxy = std::make_unique<adaptScreenProcessProxy>(m_spAtomicTable.get(), m_viewSize);
    else
        m_spAdaptScreenProxy->init(m_spAtomicTable.get(), m_viewSize);
    //运用“代理模式”，外层直接通过这个代理类去调用适应屏幕调整，而不用去区分要调用哪一种“策略”
    m_spAdaptScreenProxy->doAdaptScreen();
}

double KArrangeProcess::getColRealWidth(int col)
{
    double dWidth = 0;
    ks_stdptr<Range> spColRange;
    HRESULT hr =
        getSpecifiedRange(&spColRange, m_tableInfo.allRangeInfo.rowFrom, m_tableInfo.allRangeInfo.rowTo, col, col);
    if (FAILED(hr) || !spColRange)
        return dWidth;
    KComVariant colWidth;
    spColRange->get_ColumnWidth(&colWidth);
    if (V_VT(&colWidth) == VT_R8)
        dWidth = V_R8(&colWidth);
    return dWidth;
}

double KArrangeProcess::getRowRealHeight(int row)
{
    double dHeight = 0;
    ks_stdptr<Range> spRowRange;
    HRESULT hr =
        getSpecifiedRange(&spRowRange, row, row, m_tableInfo.allRangeInfo.colFrom, m_tableInfo.allRangeInfo.colTo);
    if (FAILED(hr) || !spRowRange)
        return dHeight;
    KComVariant rowHeight;
    spRowRange->get_RowHeight(&rowHeight);
    if (V_VT(&rowHeight) == VT_R8)
        dHeight = V_R8(&rowHeight);
    return dHeight;
}

void KArrangeProcess::adjustColumnsWidth()
{
    if (!m_spAtomicTable)
        return;

    for (int col = m_tableInfo.allRangeInfo.colFrom; col <= m_tableInfo.allRangeInfo.colTo; col++)
    {
        if (m_spAtomicTable->m_colList.find(col) != m_spAtomicTable->m_colList.end())
        {
            AtomicCol *pEachAtomicCol = m_spAtomicTable->m_colList.at(col).get();
            // Todo:获取初始列宽，这个设置后续放到初始化AtomicCol的位置
            pEachAtomicCol->setColOriginWidth(getColRealWidth(col));
            processEachAtomicCol(pEachAtomicCol);
        }
    }
}

void KArrangeProcess::processEachAtomicCol(AtomicCol *pEachAtomicCol)
{
    WholeEffectBase *pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
    if (pWholeEffect)
        pEachAtomicCol->calcColResultWidth(pWholeEffect);
}

HRESULT KArrangeProcess::adjustRowsHeight()
{
    HRESULT hr = S_CONTINUE;
    if (!m_spAtomicTable)
        return E_FAIL;

    for (auto iter = m_spAtomicTable->m_rowList.begin(); iter != m_spAtomicTable->m_rowList.end(); iter++)
    {
        AtomicRow *pEachAtomicRow = iter->second.get();
        if (!pEachAtomicRow)
            continue;
        processEachAtomicRow(pEachAtomicRow);
    }
    return hr;
}

void KArrangeProcess::processEachAtomicRow(AtomicRow *pEachAtomicRow)
{
    if (!pEachAtomicRow)
        return;

    const std::vector<AtomicCells *>& vecCells = pEachAtomicRow->vecCells;
    int iRow = pEachAtomicRow->getRowIdx();
    double rowMaxHeight = 0;
    for (size_t i = 0; i < vecCells.size(); i++)
    {
        AtomicCells *pEachAtomicCells = vecCells.at(i);
        if (!pEachAtomicCells->IsOneCell()) //合并单元格 跳过
            continue;

        double sumWidth = getCellWidth(pEachAtomicCells);
        double calcHeight = pEachAtomicCells->getCellEstimatedHeight(sumWidth);
        if (rowMaxHeight < calcHeight)
            rowMaxHeight = calcHeight;
    }

    //设置新的行高
    pEachAtomicRow->setRowResultHeight(rowMaxHeight);
}

double KArrangeProcess::getRowSpacingWithChar()
{
    WholeEffectBase *pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
    if (!pWholeEffect)
        return 0;
    return pWholeEffect->getRowSpacingWithChar();
}

bool isImgFmlaCell(Range* pRange)
{
	static const LPCWSTR CELLIMG_FMLA_PATTERN = __X("=DISPIMG(\"");

	if (!pRange)
		return false;
	VARIANT_BOOL hasFormula;
	pRange->get_HasFormula(&hasFormula);
	if (!hasFormula)
		return false;

	ks_bstr bsFormula;
	pRange->get_Formula(&bsFormula);
	ks_wstring strFormula = bsFormula.c_str();
	return strFormula.find(CELLIMG_FMLA_PATTERN) != ks_wstring::npos;
}

void KArrangeProcess::getCellInfo(IN int row, IN int col, IN ZoneType zoneType, OUT int &cellWidth,
                                          OUT int &cellCharCnt, OUT bool &bLineBreak, OUT std::vector<int>& vec)
{
    cellWidth = 0; //返回值单位:磅
    cellCharCnt = 0;
    bLineBreak = false;

    ks_stdptr<Range> spRangeCell;
    HRESULT hr = getSpecifiedCell(row, col, &spRangeCell);
    if (FAILED(hr) || !spRangeCell)
        return;

    VARIANT varCell;
    VariantInit(&varCell);
    hr = spRangeCell->get_Value(etRangeValueDefault, &varCell);
    if (FAILED(hr))
        return;

    if (VarIsEmpty(varCell) || isImgFmlaCell(spRangeCell))
        return;

    // Todo:普通公式类型在et的本质也是字符串来的，后续要不要也考虑跳过呢？
    ks_bstr txt;
    spRangeCell->get_Text(&txt);
    ks_wstring text = txt.c_str();

    if (text.empty())
        return;

    size_t length = text.length();
    int startPos = 0;
    size_t multiLinePos = 0;
    bool bFindManualLineBreak = false;
    int maxCharEachPara = 0; //若存在换行符用户换行的情形下，统计每一行的字符最大数
    do
    {
        multiLinePos = text.find('\n', startPos); //换行符所在的位置
        bFindManualLineBreak = (multiLinePos < length);
        if (bFindManualLineBreak)
            bLineBreak = true; //存在手动换行
        int endPos = bFindManualLineBreak ? multiLinePos : length - 1;

        //去获取子串
        int eachLineCharCnt = endPos - startPos; // int subStrEndPos = bFindManualLineBreak ? endPos : endPos + 1;
        eachLineCharCnt = eachLineCharCnt + (bFindManualLineBreak ? 0 : 1);

        if (eachLineCharCnt > maxCharEachPara) //更新该段的最大值，注意这个值是否准确
            maxCharEachPara = eachLineCharCnt;

        ks_wstring eachParaText = text.substr(startPos, eachLineCharCnt); //注意这里endPos是否要加一

        int eachParaTextWidth = getTextWidth(eachParaText, zoneType);
        if (eachParaTextWidth > 0)
            vec.push_back(eachParaTextWidth);

        if (cellWidth < eachParaTextWidth)
            cellWidth = eachParaTextWidth;

        startPos = endPos + 1; //更新起始值

    } while (bFindManualLineBreak);

    cellCharCnt = maxCharEachPara;
}

int KArrangeProcess::getTextWidth(ks_wstring text, ZoneType zoneType)
{
    QFont font;
    font.setFamily(getFontName());
    // Todo:目前只处理了内容
    int fontSize = m_contentRangeFontSize;
    switch (zoneType)
    {
    case ZoneType::RowTitle:
        fontSize = m_headRangeFontSize;
        font.setBold(true);
        break;
    case ZoneType::BigTitle:
        fontSize = m_titleRangeFontSize;
        font.setBold(true);
        break;
    case ZoneType::Content:
        fontSize = m_contentRangeFontSize;
        break;
    case ZoneType::SubTitle:
        fontSize = m_subTitleRangeFontSize;
        font.setBold(true);
        break;
    case ZoneType::Other:
        fontSize = m_otherRangeFontSize;
        break;
    case ZoneType::Info:
        fontSize = m_infoRangeFontSize;
        break;
    default:
        fontSize = m_contentRangeFontSize;
        break;
    }

    font.setPixelSize(fontSize);
    QFontMetrics fm(font);

    QString txt(krt::fromUtf16(text.c_str()));
    int width = fm.width(txt);
    return width;
}

HRESULT KArrangeProcess::adjustRowHeightWithSpace()
{
    double spacing = getRowSpacingWithChar();
    if (spacing <= 0)
        return S_OK;

    for (auto iter = m_spAtomicTable->m_rowList.begin(); iter != m_spAtomicTable->m_rowList.end(); ++iter)
    {
        AtomicRow *pEachAtomicRow = iter->second.get();
        if (!pEachAtomicRow)
            continue;
        double dHeight = pEachAtomicRow->getRowResultHeight();
        dHeight += spacing;
        pEachAtomicRow->setRowResultHeight(dHeight);
    }

    return S_OK;
}

QString KArrangeProcess::getFontName()
{
    return m_fontName;
}

HRESULT KArrangeProcess::mergeRange(Range *pRange, IETStringTools *pTools)
{
    if (!pRange || !pTools || !m_spWorksheet || !m_spWorksheet->GetSheet())
        return E_FAIL;
    ISheet* pSheet = m_spWorksheet->GetSheet();
    RANGE rg(pSheet->GetBMP());
    IdentifyTool::GetTableRange(pRange, &rg);
    if (!rg.IsValid())
        return E_FAIL;
    if (rg.IsSingleCell())
        return S_OK;

    BOOL bMerge = FALSE;
    pSheet->IsMerged(rg.RowFrom(), rg.ColFrom(), &bMerge);
    if (bMerge)
    {
        //合并单元格
        RANGE rgCell(pSheet->GetBMP());
        rgCell.SetCell(rg.SheetFrom(), rg.RowFrom(), rg.ColFrom());

        ks_stdptr<IKRanges> ptrRgs;
        pSheet->FindEffectMergeCell(rgCell, FALSE, &ptrRgs);
        const RANGE* pMergeRg = NULL;
        ptrRgs->GetItem(0, NULL, &pMergeRg);
        if (rg.Compare(*pMergeRg))
            return S_OK;
    }

    HRESULT hr = S_OK;
    QString strText;
    for (ROW iRow = rg.RowFrom(); iRow <= rg.RowTo(); ++iRow)
        for (COL iCol = rg.ColFrom(); iCol <= rg.ColTo(); ++iCol)
        {
            QString strCellText = IdentifyTool::GetCellText(iRow, iCol, pTools);
            if (!strCellText.isEmpty() && !strText.isEmpty() && strCellText != strText)
                return E_FAIL;
            else if (!strCellText.isEmpty() && strText.isEmpty())
                strText = strCellText;
        }
    QJsonObject params = templateRangeJsonObject(rg.RowFrom(), rg.RowTo(), rg.ColFrom(), rg.ColTo());
    params["type"] = "MergeCenter";
    QJsonDocument doc(params);
    m_pCtx->addBeautifyOperator(__X("range.merge"), krt::utf16(doc.toJson(QJsonDocument::Compact)));
    return pRange->Merge(TRUE);
}

HRESULT KArrangeProcess::mergeAreaProcess()
{
    m_pCtx->getStringTools()->SetEnv(m_spWorksheet->GetSheet());
    auto loopMergeRange = [&](const std::vector<Range*>& vecRange, IETStringTools* pTools)
    {
        for (const auto& rg : vecRange)
            mergeRange(rg, pTools);
    };
    //标题默认做合并处理
    loopMergeRange(m_vecTitleRangeCell, m_pCtx->getStringTools());
    //副标题也做合并处理
    loopMergeRange(m_vecSubTitleRangeCell, m_pCtx->getStringTools());
    //其他的暂时也做合并处理
    loopMergeRange(m_vecOtherRangeCell, m_pCtx->getStringTools());

    return S_OK;
}

bool KArrangeProcess::bContainManualLineBreak(ROW cellRow, COL cellCol)
{
    ks_wstring text;
    HRESULT hr = getCellText(cellRow, cellCol, text);
    if (FAILED(hr))
        return false;

    return text.find('\n', 0) != ks_wstring::npos;
}

HRESULT KArrangeProcess::getCellText(IN ROW cellRow, IN COL cellCol, OUT ks_wstring &text)
{
    ks_stdptr<Range> spRangeCell;
    HRESULT hr = getSpecifiedCell(cellRow, cellCol, &spRangeCell);
    if (FAILED(hr) || !spRangeCell)
        return E_FAIL;
    ks_bstr txt;
    spRangeCell->get_Text(&txt);
    text = txt.c_str();
    return S_OK;
}

HRESULT KArrangeProcess::getSpecifiedCell(IN ROW cellRow, IN COL cellCol, OUT Range **ppRange)
{
    if (!m_spWorksheet)
        return E_FAIL;
    ISheet *pSheet = m_spWorksheet->GetSheet();
    if (!pSheet)
        return E_FAIL;

    RANGE cellRange(pSheet->GetBMP());
    cellRange.SetRowFromTo(cellRow, cellRow);
    cellRange.SetColFromTo(cellCol, cellCol);
    ks_stdptr<Range> spRange;
    m_spWorksheet->GetRangeByData(&cellRange, &spRange);
    if (!spRange)
        return E_FAIL;

    ks_stdptr<Range> spRangeCell;
    spRange->get_Cells(&spRangeCell);
    if (!spRangeCell)
        return E_FAIL;

    *ppRange = spRangeCell.detach();
    return S_OK;
}

void KArrangeProcess::initFontSizeInfo()
{
    WholeEffectBase *pWholeEffect = m_pTableApplyParam->getWholeEffectInfo();
    if (!pWholeEffect)
    {
        //适应
        m_titleRangeFontSize = 16;
        m_subTitleRangeFontSize = 14;
        m_headRangeFontSize = 11;
        m_contentRangeFontSize = 10;
        m_otherRangeFontSize = 10;
        m_infoRangeFontSize = 10;
        return;
    }

    if (!m_pTableApplyParam->getApplyFont())
        return;
    m_titleRangeFontSize = pWholeEffect->getTitleRangeFontSize();
    m_subTitleRangeFontSize = pWholeEffect->getSubTitleRangeFontSize();
    m_headRangeFontSize = pWholeEffect->getHeadRangeFontSize();
    m_contentRangeFontSize = pWholeEffect->getContentRangeFontSize();
    m_otherRangeFontSize = pWholeEffect->getOtherRangeFontSize();
    m_infoRangeFontSize = pWholeEffect->getInfoRangeFontSize();
}

single KArrangeProcess::getCellFontSize(int row, int col)
{
    ZoneType zoneType = m_tableInfo.getCellZoneType(row, col);
    switch (zoneType)
    {
    case RowTitle:
        return m_headRangeFontSize;
    case BigTitle:
        return m_titleRangeFontSize;
    case Content:
        return m_contentRangeFontSize;
    case SubTitle:
        return m_subTitleRangeFontSize;
    case Other:
        return m_otherRangeFontSize;
    case Info:
        return m_infoRangeFontSize;
    }
    return m_contentRangeFontSize;
}

void KArrangeProcess::getRangeData()
{
    if (!m_spAllRangeCell)
        return;

    ks_stdptr<Range> spRangeCell;
    m_spAllRangeCell->get_Cells(&spRangeCell);
    if (!spRangeCell)
        return;

    VARIANT varCell;
    VariantInit(&varCell);
    HRESULT hr = spRangeCell->get_Value(etRangeValueDefault, &varCell);
    if (FAILED(hr))
        return;

    QJsonArray jsonArr;
    if ((V_VT(&varCell) & VT_ARRAY) != 0)
    {
        SAFEARRAY *psa = V_ARRAY(&varCell);
        /*对二维数组的元素进行逐个遍历*/
        INT32 index[2] = {0, 0};
        INT32 lFirstLBound = 0;
        INT32 lFirstUBound = 0;
        INT32 lSecondLBound = 0;
        INT32 lSecondUBound = 0;
        hr = SafeArrayGetLBound(psa, 1, &lFirstLBound);
        if (!SUCCEEDED(hr))
            return;
        hr = SafeArrayGetUBound(psa, 1, &lFirstUBound);
        if (!SUCCEEDED(hr))
            return;
        hr = SafeArrayGetLBound(psa, 2, &lSecondLBound);
        if (!SUCCEEDED(hr))
            return;
        hr = SafeArrayGetUBound(psa, 2, &lSecondUBound);
        if (!SUCCEEDED(hr))
            return;
        for (INT32 i = lFirstLBound; i <= lFirstUBound; i++)
        {
            QJsonArray jsonRow;
            index[0] = i;
            VARIANT var;
            for (INT32 j = lSecondLBound; j <= lSecondUBound; j++)
            {
                index[1] = j;
                VariantInit(&var);
                hr = SafeArrayGetElement(psa, index, &var);
                if (!SUCCEEDED(hr))
                    return;

                if (V_VT(&var) == VT_BSTR)
                {
                    jsonRow.append(QJsonValue(krt::fromUtf16(V_BSTR(&var))));
                }
                else if (V_VT(&var) == VT_R8)
                {
                    DOUBLE dbl = V_R8(&var);
                    jsonRow.append(QJsonValue(QString::number(dbl, 10, 4)));
                }
                else
                {
                    jsonRow.append(QJsonValue(QString("")));
                }
                VariantClear(&var);
            }
            jsonArr.append(QJsonValue(jsonRow));
        }
    }

    else if (V_VT(&varCell) == VT_BSTR)
    {
        QJsonArray jsonRow;
        jsonRow.append(QJsonValue(krt::fromUtf16(V_BSTR(&varCell))));
        jsonArr.append(QJsonValue(jsonRow));
    }
    else if (V_VT(&varCell) == VT_R8)
    {
        QJsonArray jsonRow;
        DOUBLE dbl = V_R8(&varCell);
        jsonRow.append(QJsonValue(QString::number(dbl, 10, 4)));
        jsonArr.append(QJsonValue(jsonRow));
    }
    else
    {
        QJsonArray jsonRow;
        jsonRow.append(QJsonValue(QString("")));
        jsonArr.append(QJsonValue(jsonRow));
    }

    VariantClear(&varCell);
}

QJsonObject KArrangeProcess::templateRangeJsonObject(int rowFrom, int rowTo, int colFrom, int colTo)
{
    QJsonObject rg;
    rg["rowFrom"] = rowFrom;
    rg["rowTo"] = rowTo;
    rg["colFrom"] = colFrom;
    rg["colTo"] = colTo;
    rg["sheetStId"] = static_cast<int>(m_spWorksheet->GetSheet()->GetStId());
    int sheetIdx = INVALIDIDX;
    m_spWorksheet->GetSheet()->GetIndex(&sheetIdx);
    rg["sheetIdx"] = sheetIdx;
    return rg;
}

QJsonObject KArrangeProcess::templateSetXfJsonObject(int rowFrom, int rowTo, int colFrom, int colTo)
{
    QJsonObject params;
    QJsonArray rgs;
    QJsonObject rg = templateRangeJsonObject(rowFrom, rowTo, colFrom, colTo);
    rgs.append(rg);
    QJsonObject xf;
    params["rgs"] = rgs;
    params["sheetStId"] = static_cast<int>(m_spWorksheet->GetSheet()->GetStId());
    int sheetIdx = INVALIDIDX;
    m_spWorksheet->GetSheet()->GetIndex(&sheetIdx);
    params["sheetIdx"] = sheetIdx;
    return params;
}
} // namespace etai
