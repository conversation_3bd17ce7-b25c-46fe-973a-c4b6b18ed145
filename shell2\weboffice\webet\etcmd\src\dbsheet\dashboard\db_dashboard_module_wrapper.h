﻿#ifndef __WEBET_DB_DASHBOARD_MODULE_WRAPPER_H__
#define __WEBET_DB_DASHBOARD_MODULE_WRAPPER_H__

interface IKWebExtension;
class KEtRevisionContext;
namespace wo
{
class KEtWorkbook;
class KDbDashboardModuleWrapper
{
public:
    enum class Status
    {
        Normal,
        NoDataSource,
        InvalidConfig
    };
public:
    KDbDashboardModuleWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension);
    KDbDashboardModuleWrapper(const KDbDashboardModuleWrapper&) = delete;
    KDbDashboardModuleWrapper& operator=(const KDbDashboardModuleWrapper&) = delete;
    virtual ~KDbDashboardModuleWrapper() = default;
    virtual Status GetStatus() const = 0;
    virtual HRESULT SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang,
                                     KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) const = 0;
    virtual bool SerializeBaseInfo(ISerialAcceptor* pAcpt);
    // Atom中的部分信息序列化给OLAP
    virtual void SerializeSettings(ISerialAcceptor* pAcpt) {};
    PCWSTR GetWebExtensionKey() const;
    WebExtType GetWebExtType() const;
    POINT GetPosition() const;
    void SerializeProperties(ISerialAcceptor* pAcpt) const;
    PCWSTR GetStatusStr() const;
    bool IsChart();
protected:
    PCWSTR GetConfigStr() const;
    PCWSTR GetProperty(LPCWSTR keyName) const;
    WebExtensionDataSourceType GetDataSourceType() const;
protected:
    KEtWorkbook* m_pEtWorkbook;
    ks_stdptr<IKWebExtension> m_spWebExtension;
};

};
#endif // __WEBET_DB_DASHBOARD_MODULE_WRAPPER_H__