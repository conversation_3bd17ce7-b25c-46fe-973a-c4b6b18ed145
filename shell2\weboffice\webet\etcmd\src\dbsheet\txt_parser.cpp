#include "etstdafx.h"
#include "txt_parser.h"

constexpr WCHAR TAB = __Xc('\t');
constexpr WCHAR RETURNC = __Xc('\r');
constexpr WCHAR NEWLINE = __Xc('\n');
constexpr WCHAR QUOTEMARK = __Xc('"');

namespace wo
{
PCWSTR TxtParserResult::GetString(UINT row, UINT col)
{
	std::vector<ks_wstring>& vec = m_result[row];
	if (col >= vec.size())
		return nullptr;
	return vec[col].c_str();
}

UINT TxtParserResult::GetColCount()
{
	return m_colCnt;
}

UINT TxtParserResult::GetRowCount()
{
	return m_result.size();
}

void TxtParserResult::Clear()
{
	m_result.clear();
}

void TxtParserResult::EmplaceBack(std::vector<ks_wstring>& vec)
{
	m_result.emplace_back(vec);
	int size = vec.size();
	if (size > m_colCnt)
		m_colCnt = size;
}

TxtParser::TxtParser()
{
	resetFlag();
}

TxtParser::~TxtParser() {}

bool TxtParser::parse(PCWSTR txt, TxtParserResult& result)
{
	result.Clear();
	beforeParse();

	int txtLen = xstrlen(txt);
	if (txtLen > 0 && txt[txtLen - 1] == __Xc('\n'))
		--txtLen;
	for (int i = 0; i < txtLen; ++i)
	{
		WCHAR c = txt[i];
		switch (currentState)
		{
			case state_str_start:
				switch (c)
				{
					case QUOTEMARK:
						pushCharToField(c, false);
						currentState = state_quotation_field;
						break;
					case TAB:
						pushFieldToLine();
						break;
					case NEWLINE:
						pushFieldToLine();
						result.EmplaceBack(currentLine);
						currentLine.clear();
						break;
					case RETURNC:
						currentState = state_return;
						break;
					default:
						pushCharToField(c, true);
						currentState = state_field;
						break;
				}
				break;
			case state_field:
				switch (c)
				{
					case TAB:
						pushFieldToLine();
						currentState = state_str_start;
						break;
					case NEWLINE:
						pushFieldToLine();
						result.EmplaceBack(currentLine);
						currentLine.clear();
						currentState = state_str_start;
						break;
					case RETURNC:
						currentState = state_return;
						break;
					default:
						pushCharToField(c, true);
						break;
				}
				break;
			case state_quotation_field:
				switch (c)
				{
					case TAB:
						break;
					case QUOTEMARK:
						pushCharToField(c, false);
						currentState = state_escape;
						break;
					case NEWLINE:
					case RETURNC:
						meetNewlineInQuoteMark = true;
					default:
						pushCharToField(c, true);
						break;
				}
				break;
			case state_escape:
				switch (c)
				{
					case QUOTEMARK:
						pushCharToField(c, true);
						currentState = state_quotation_field;
						break;
					case TAB:
						pushFieldToLine();
						currentState = state_str_start;
						break;
					case NEWLINE:
						pushFieldToLine();
						result.EmplaceBack(currentLine);
						currentLine.clear();
						currentState = state_str_start;
						break;
					case RETURNC:
						currentState = state_return;
						break;
					default:
						pushCharToField(c, true);
						currentState = state_field;
						break;
				}
				break;
			case state_return:
				if (c == RETURNC)
					break;
				pushFieldToLine();
				result.EmplaceBack(currentLine);
				currentLine.clear();
				if (c != NEWLINE)
					--i;
				currentState = state_str_start;
				break;
			default:
				return false;
		}
	}
	pushFieldToLine();
	result.EmplaceBack(currentLine);
	return true;
}

void TxtParser::beforeParse()
{
	resetFlag();
	currentState = state_str_start;
	currentLine.clear();
	currentField = __X("");
	currentFieldRaw = __X("");
}

void TxtParser::resetFlag()
{
	meetNewlineInQuoteMark = false;
}

void TxtParser::pushFieldToLine()
{
	currentLine.emplace_back(meetNewlineInQuoteMark ? currentField : currentFieldRaw);
	currentField = __X("");
	currentFieldRaw = __X("");
	resetFlag();
}

void TxtParser::pushCharToField(WCHAR c, bool both)
{
	if (both)
		currentField.insert(currentField.end(), c);
	currentFieldRaw.insert(currentFieldRaw.end(), c);
}
}