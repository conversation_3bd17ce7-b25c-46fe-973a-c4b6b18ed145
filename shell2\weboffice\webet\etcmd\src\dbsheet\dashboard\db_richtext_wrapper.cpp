﻿#include "etstdafx.h"
#include "workbook.h"
#include "db_richtext_wrapper.h"
#include "webbase/serialize.h"

namespace wo
{

KDbRichTextWrapper::KDbRichTextWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension)
    : KDbDashboardModuleWrapper(pEtWorkbook, pWebExtension)
{
}

KDbDashboardModuleWrapper::Status KDbRichTextWrapper::GetStatus() const
{
    return Status::Normal;
}

HRESULT KDbRichTextWrapper::SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang,
                                             KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) const
{
    LPCWSTR value = GetProperty(__X("richTextContent"));
    pAcpt->addString("richTextContent", value);
    return S_OK;
}

}