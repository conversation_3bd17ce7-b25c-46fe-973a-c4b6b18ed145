﻿#ifndef _H_WO_BATCH_TRANS_H_
#define _H_WO_BATCH_TRANS_H_
#include <public_header/revision/src/kwversion.h>

namespace wo 
{

class KwTask;
class KwTasks;
class KEtWorkbook;
class KEtRevisionContext;

enum class WoBeginBatchTransFlag
{
	no,
	restart,
	keep,
};

class IBatchTask
{
public:
	~IBatchTask() {}
	virtual KwTask * CreateBatchTask(KEtRevisionContext * pCtx) = 0;
	virtual WoBeginBatchTransFlag GetBeginBatchFlag(IBatchTask * pPreBatchTask) = 0;
	virtual bool needStopInEndBatch() = 0;
    virtual PCWSTR GetTag() = 0;
    virtual bool isAllowMultiUserConns() = 0;
    virtual KwVersion::BanUndoType getBanUndoType() = 0;
    virtual void SetJSContextChange(bool value) = 0;
};

class KEtBatchTransHelper
{
public:
	KEtBatchTransHelper(KEtWorkbook*);
	void beginTransact(WebInt);
	void endTransact(bool, IRevisionContext* pCtx);
	
	void appBeginUndoTrans();
	void appEndUndoTrans(KEtRevisionContext * pCtx, HRESULT hr);

	void tryBeginBatch(KEtRevisionContext * pCtx, IBatchTask * batchTask, KwTask* pTask);
	void tryEndBatch(KEtRevisionContext * pCtx, KwTask* task, HRESULT hr, bool * pIsRemoveCmd);

	void onUndoRedo(KEtRevisionContext * pCtx);
	
	KwVersion* confirmTask(UINT32 userInnerID, KwTask* task, KwVersion::BanUndoType banUndo, WebInt baseVersion, bool isRollback);
    void moveFillLogToTasks(KwTasks & tasks);
    
    bool isSkipSerialComplexity();

protected:
	void clearBatchStatue();
	void setBatchStatus(IBatchTask * batchTask, UINT32 innerId);
	void confirmVersion(KEtRevisionContext * pCtx, IBatchTask * pBatchTask);
	void endAndConfirm(KEtRevisionContext * pCtx);
	bool isInTrans() { return m_nTransLevel > 0; }

private:
	std::vector<KwTask*> m_fillLogTasks;
	std::unique_ptr<app_helper::KUndoTransaction> m_undoTrans;
	KEtWorkbook* m_pWb;
	IBatchTask * m_pBatchTask;
	PCWSTR m_preVersionTag = nullptr;
	UINT32 m_firstTaskInnerID;
	int m_nTransLevel;
	bool m_isBatch;
    bool m_skipNextConfirm;
};
}

#endif
