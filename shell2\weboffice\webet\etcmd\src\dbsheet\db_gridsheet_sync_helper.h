﻿#ifndef __DB_GRIDSHEET_SYNC_HELPER_H__
#define __DB_GRIDSHEET_SYNC_HELPER_H__
#include "et_dbsheet_copysheet_helper.h"
#include "webbase/binvariant/binvarobj.h"

namespace wo
{
class KEtRevisionContext;
class GridsheetSyncHelper
{
public:
	struct GridsheetSyncParam : DBSyncParam
	{
		binary_wo::VarObj* m_pParam = nullptr; // 用于命令回放相关
		KEtRevisionContext* m_pCtx = nullptr;
		PCWSTR m_pcwDefaultName = __X("Unknown");
		PCWSTR m_pcwFilePath = nullptr;
		bool m_bEnableCopyAttachment = true;
	};
	GridsheetSyncHelper(etoldapi::_Worksheet*, etoldapi::_Worksheet*);

	HRESULT Init(const GridsheetSyncParam&);
	HRESULT ExecCopy();
	EtDbId getFldSourceId() const { return m_fldSourceId; }
	bool GetLostNoteFlag() const { return m_lostNoteFlag; }
	std::unordered_map<EtDbId, EtDbId> getFldMap() const { return m_fldMap; }
private:
	HRESULT setInfoField();
	HRESULT setInfoFieldHidden();
	HRESULT adjustFields(const RANGE&);
	HRESULT adjustRecords(const RANGE&);
	HRESULT calcSourceRange(RANGE&);
	HRESULT reAdjustIds();
    HRESULT doExecCopy(const RANGE&);
    EtDbId decodeRecordId(PCWSTR str);
    void encodeToBase64(QString& str);
private:
	GridsheetSyncParam m_param;
	mutable EtDbId m_fldSourceId = INV_EtDbId;
	mutable std::unordered_map<EtDbId, EtDbId> m_recMap;
	mutable std::unordered_map<EtDbId, EtDbId> m_fldMap;
private:
	etoldapi::_Worksheet* m_pSrcWorkSheet = nullptr;
	etoldapi::_Worksheet* m_pTarWorkSheet = nullptr;
	ISheet* m_pSrcSheet = nullptr;
	ISheet* m_pTarSheet = nullptr;
	IBook* m_pSrcBook = nullptr;
	IBook* m_pTarBook = nullptr;
	ks_stdptr<IDBSheetOp> m_spTarDbSheetOp;
	bool m_lostNoteFlag = false;
	ks_stdptr<IEtIdContainer> m_spEtIdContainer;
	ks_stdptr<IEt2DbSyncData> m_spEt2DbSyncData;
	std::unordered_set<EtDbId> m_stValidSrcRecordIds;
	std::unordered_set<EtDbId> m_stValidSrcFieldIds;
};
} // namespace wo


#endif