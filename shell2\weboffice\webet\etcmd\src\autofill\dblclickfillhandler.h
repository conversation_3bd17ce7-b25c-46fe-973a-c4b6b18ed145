﻿#ifndef __DBL_CLICK_FILL_HANDLER_H__
#define __DBL_CLICK_FILL_HANDLER_H__


namespace wo
{

class KDblClkFillHandle
{
private:
	KDblClkFillHandle() { ASSERT(FALSE); };
public:
	KDblClkFillHandle(Range* pRange)
	{
		m_ptrRange = pRange;
		ks_stdptr<IRangeInfo> ptrRangeInfo = pRange;
		ptrRangeInfo->GetWorksheetInfo(&m_spIKWorksheet);
	}
	HRESULT AutoFill(ETAutoFillType, RANGE& rgSelect, KEtRevisionContext* ctx, RANGE& rgDst);

protected:
	enum	FORMULACOUNT
	{
		FC_EMPTY = 1,
		FC_SUSPENSIVE,
		FC_FULL,
	};

// 	BOOL	HasFormula(IDX iSheet, int nRow, int nCol,
// 		BOOL bLeft, int* pnColumn);
	FORMULACOUNT HasFormula(IDX iSheet, int nRowBegin, int nRowEnd,
		int nColBegin, int nColEnd, BOOL bCheckOnce);
	BOOL	IsCellEmpty(IDX iSheet, int row, int col);
	RANGE	GetFillRange(BOOL bUseRefer, RANGE rgSrc);

	ks_stdptr<IKWorksheet>	m_spIKWorksheet;		// no addref
	KCOMPTR(IBookOp)		m_ptrBookOp;
	KCOMPTR(Range)			m_ptrRange;
};

}

#endif //__DBL_CLICK_FILL_HANDLER_H__
