﻿#include "etstdafx.h"
#include "data_analyze.h"
#include "data_analyze_task.h"
#include "data_analyze_helper.h"
#include "webetlink.h"
#include "webbase/webdeclare.h"
#include "dataanalyze/et_dataanalyze_helper.h"
#include "kso/l10n/et/etshell.h"
#include "workbooks.h"
#include "persist/per_imp.h"
#include "helpers/protection_helper.h"
#include "helpers/sheet_operator_helper.h"
#include "helpers/varobject_helper.h"
#include "hresult_to_string.h"
extern Callback* gs_callback;

#define PREVIEW_ROWS_LIMIT  100  //预览最多100行数据

namespace wo
{
    QString KDASqlGenerator::createSubUnionSQL(const TableInfo& table, const QStringList& udfColNames, bool bTagSource)
    {
        QString subSql;
        QString selectCols;
        QStringList udfNames = udfColNames;
        const QString columnStr = krt::fromUtf16(et_sDataAnalyzeColumn);
        etda::preprocessColumnNameList(udfNames, columnStr);
        for (int i = 0; i < table.mergeCols.size(); i++)
        {
            if (table.mergeCols[i] == "null") //null不能被引号包裹
            {
                selectCols += QString("%1 AS \"%2\"").arg(table.mergeCols[i], udfNames[i]);
            }
            else
            {
                selectCols += QString("\"%1\" AS \"%2\"").arg(table.mergeCols[i], udfNames[i]);
                if (m_tableColNamesSourceMap[table.tableName].indexOf(table.mergeCols[i]) == -1)
                {
                    WOLOG_ERROR << "[ETDA][createSubUnionSQL] column name not exist: " << krt::utf16(table.mergeCols[i]);
                    return QString(); //如果列名不存在，返回空sql让执行失败，不然列名会被当字面量
                }
            }

            if (i < table.mergeCols.size() - 1)
                selectCols += ", ";
        }
        if (!table.mergeCols.empty() && bTagSource)
        {
            int bookNameSuffix = etda::countNameSuffix(table.mergeCols, krt::fromUtf8(etda::k_column_book_name));
            int sheetNameSuffix = etda::countNameSuffix(table.mergeCols, krt::fromUtf8(etda::k_column_sheet_name));
            QString bookName = bookNameSuffix == 0 ? krt::fromUtf8(etda::k_column_book_name) : QString("%1_%2").arg(krt::fromUtf8(etda::k_column_book_name)).arg(bookNameSuffix++);
            QString sheetName = sheetNameSuffix == 0 ? krt::fromUtf8(etda::k_column_sheet_name) : QString("%1_%2").arg(krt::fromUtf8(etda::k_column_sheet_name)).arg(sheetNameSuffix++);
            selectCols += QString(", %1 AS %1, %2 AS %2").arg(bookName).arg(sheetName);
        }
        return QString("select %1 from %2").arg(selectCols).arg(table.tableName);
    }

    KDASqlGenerator::KDASqlGenerator(bool bPreview)
        : m_bPreview(bPreview)
    {
    }

    QString KDASqlGenerator::createTableName(const QString& fileId, int sheetId, int sourceId)
    {
        QString tableName = QString("etda_%1_%2_%3").arg(fileId).arg(sheetId).arg(sourceId);
        return tableName;
    }

    QString KDASqlGenerator::generateeDeduplicationSQL(const SqlGeneratorParam& param)
    {
        if (param.tableInfo.empty())
        {
            WOLOG_INFO << "[ETDA][generateeDeduplicationSQL] param empty";
            return QString();
        }
        QStringList sqls;
        for (int i = 0; i < param.tableInfo.size(); i++)
        {
            const QString& tableName = param.tableInfo[i].tableName;
            QStringList colList = getTableUsedColNames(tableName);
            QString selectCols = colList.join(",");
            QString sql = QString("DELETE FROM %1   WHERE id NOT IN   (  SELECT MIN(id)   FROM %1   GROUP BY %2  )").arg(tableName).arg(selectCols);
            sqls << sql;
            WOLOG_INFO << "[ETDA][generateeDeduplicationSQL] sql: " << krt::utf16(sql);
        }
        return sqls.join(';');
    }
    QString KDASqlGenerator::generateJoinSql(const SqlGeneratorParam& param)
    {
        QString sql;
        QString mergeType = krt::fromUtf16(ParseJoinModeEnum(param.joinMode));
        
        QString selectColsLeft, selectColsRight;
        QString leftTableName = param.tableInfo[0].tableName;
        QString rightTableName = param.tableInfo[1].tableName;
        QStringList leftColList = getTableUsedColNames(leftTableName);
        QStringList rightColList = getTableUsedColNames(rightTableName);
        if (leftColList.isEmpty() && rightColList.isEmpty())
            return QString();
        for (auto& i : leftColList)
        {
            QString columnName = "\"" + i + "\"";
            selectColsLeft += QString("%1.%2,").arg(leftTableName).arg(columnName);
        }
        for (auto& i : rightColList)
        {
            QString columnName = "\"" + i + "\"";
            selectColsRight += QString("%1.%2,").arg(rightTableName).arg(columnName);
        }
        selectColsRight.chop(1);
        QString finalCols = QString("%1 %2").arg(selectColsLeft).arg(selectColsRight);
        if (param.joinMode == etda::JoinMode::OuterJoin)
        {
            if (selectColsRight.isEmpty())
            {
                selectColsLeft.chop(1);
                finalCols = QString("%1 %2").arg(selectColsLeft).arg(selectColsRight);
            }
            QString subSql1 = QString("select %1 from %2 %3 %4 on %5.\"%6\" = %7.\"%8\"")
            .arg(finalCols)
            .arg(leftTableName)
            .arg(QString("left join"))
            .arg(rightTableName)
            .arg(leftTableName)
            .arg(param.tableInfo[0].mergeCols[0])
            .arg(rightTableName)
            .arg(param.tableInfo[1].mergeCols[0]);

            QString subSql2 = QString("select %1 from %2 %3 %4 on %5.\"%6\" = %7.\"%8\" where %2.\"%9\" is null")
            .arg(finalCols)
            .arg(leftTableName)
            .arg(QString("right join"))
            .arg(rightTableName)
            .arg(leftTableName)
            .arg(param.tableInfo[0].mergeCols[0])
            .arg(rightTableName)
            .arg(param.tableInfo[1].mergeCols[0])
            .arg(param.tableInfo[0].mergeCols[0]);

            sql = QString("select * from (%1 union all %2)").arg(subSql1).arg(subSql2);
        }
        else 
        {
            if (!leftColList.isEmpty() && !rightColList.isEmpty())
            {
                sql = QString("select %1 from %2 %3 %4 on %5.\"%6\" = %7.\"%8\"")
                .arg(finalCols)
                .arg(leftTableName)
                .arg(mergeType)
                .arg(rightTableName)
                .arg(leftTableName)
                .arg(param.tableInfo[0].mergeCols[0])
                .arg(rightTableName)
                .arg(param.tableInfo[1].mergeCols[0]);
            }
            //退化为单表查询
            else if (leftColList.isEmpty())
            {
                sql = QString("select %1 from %2")
                .arg(selectColsRight)
                .arg(rightTableName);
            }
            else if (rightColList.isEmpty())
            {
                selectColsLeft.chop(1);
                sql = QString("select %1 from %2")
                .arg(selectColsLeft)
                .arg(leftTableName);
            }
        }
        return sql;
    }

    QString KDASqlGenerator::generateUnionSQL(const SqlGeneratorParam& param)
    {
        QStringList subSqls;
        for (const TableInfo& table : param.tableInfo)
        {
            QString subSql = createSubUnionSQL(table, param.finalColNames, param.bTagSource);
            subSqls.push_back(subSql);
        }
        return subSqls.join(" union all ");
    }

    QString KDASqlGenerator::generateMergeSQL(const SqlGeneratorParam& param)
    {
        if (param.tableInfo.size()<2)
            return QString();
        QString sql;
        if (param.type == etda::MergeType::Join)
        {
            sql = generateJoinSql(param);
        }
        else 
        {
            sql = generateUnionSQL(param);
        }
        if (m_bPreview)
        {
            const int rowsLimit = PREVIEW_ROWS_LIMIT;
            sql += QString(" limit %1").arg(rowsLimit);
        }
        WOLOG_INFO << "[ETDA][generateMergeSQL] sql: " << krt::utf16(sql);
        return sql;  
    }

    QString KDASqlGenerator::generateCountSQL(const QString& tableName)
    {
        return QString("select count(*) from %1").arg(tableName);
    }

    QString KDASqlGenerator::generateIndexSQL(const SqlGeneratorParam& param)
    {
        if (param.type != etda::MergeType::Join || param.tableInfo.size() != 2)
            return QString();
        QStringList sqls;
        for(int i = 0; i < param.tableInfo.size(); i++)
        {
            QString tableName = param.tableInfo[i].tableName;
            QString selectCol = param.tableInfo[i].mergeCols[0];
            QString indexName = QString("idx_%1").arg(tableName);
            QString sql = QString("CREATE INDEX %1 ON %2 (%3)").arg(indexName).arg(tableName).arg(selectCol);
            sqls << sql;
        }
        return sqls.join(";");
    }
    void KDASqlGenerator::setTableColName(const QString& tableName, const QString& colName)
    {
        m_tablseColNameMap[tableName] << colName;
        m_tableColNamesSourceMap[tableName] << colName;
    }
    QStringList KDASqlGenerator::getTableUsedColNames(const QString& tableName)
    {
        return m_tablseColNameMap[tableName];
    }
    void KDASqlGenerator::replaceColNames(const QString& tableName, const QStringList& colNames)
    {
        m_tablseColNameMap[tableName] = colNames;
    }

    HRESULT FileProcessor::processFileList(const binary_wo::VarObj& fileList, std::unordered_map<int, QString>& sourceIdMap, etda::MergeInfo* pInfo, ExecDAOutput* output) 
    {
        std::unordered_set<int> sourceIdSet;
        int dataSourceCount = pInfo->dataSourceConfig.size();
        for (int i = 0; i < fileList.arrayLength_s(); i++) 
        {
            binary_wo::VarObj item = fileList.at_s(i);
            VAR_OBJ_EXPECT_NUMERIC(item, "sourceId")
            int sourceId = item.field_int32("sourceId");

            VAR_OBJ_EXPECT_STRING(item, "fileId")
            WebStr fileId = item.field_str("fileId");

            VAR_OBJ_EXPECT_NUMERIC(item, "sheetStId")
            int sheetStId = item.field_int32("sheetStId");
            for (int j = 0; j < dataSourceCount; j++) 
            {
                if (sourceIdSet.find(j) != sourceIdSet.end())
                    continue;
                etda::DataSourceConfig& it = pInfo->dataSourceConfig[j];
                if (it.fileId == krt::fromUtf16(fileId) && it.sheetStId == sheetStId) 
                {
                    it.sourceId = sourceId;
                    sourceIdSet.insert(j);
                    break;
                }
            }

            VAR_OBJ_EXPECT_STRING(item, "filePath")
            QString path = krt::fromUtf16(item.field_str("filePath"));
            if (path.isEmpty() || !QFile(path).exists()) 
            {
                WOLOG_ERROR << "[ETDA][processFileList] file not exist:" << path.toUtf8().constData();
                output->AddError(etda::IMPORT_CSV_DATABASE_NOT_EXIST, etda::getErrorMsg<etda::IMPORT_CSV_DATABASE_NOT_EXIST>());
                return E_FAIL;
            }
            sourceIdMap[sourceId] = std::move(path);
        }
        if (sourceIdMap.empty())
        {
            WOLOG_ERROR << "[ETDA][processFileList] sourceIdMap empty";
            return E_FAIL;
        }
        return S_OK;
    }

    void returnMergeTask(const MergeTaskParam&  param, binary_wo::BinWriter& binWriter, etda::ErrorCode& errorCode, PCWSTR errorMsg
    , etda::WarningCode& warningCode, PCWSTR warningMsg)
    {
        IETDAClient* pEtdaClient = getDAClientInstance();
        if (!param.isNewTask && param.isAutoSync)
                pEtdaClient->SetUpdating(param.sheetStId, false);
        if (pEtdaClient->IsCancel())
        {
            errorCode = etda::TASK_CANCEL;
            errorMsg = etda::getErrorMsg<etda::TASK_CANCEL>();
        }

        int res = pEtdaClient->Quit(param.isPreview, false);
        if (res != 0)
            WOLOG_INFO << "[ETDA][returnMergeTask] process quit fail";
        else 
            WOLOG_INFO << "[ETDA][returnMergeTask] process quit success";

        binWriter.addBoolField(param.pInfo->execResultConfig.isTagSource, "isTagSource");
        binWriter.addBoolField(param.pInfo->execResultConfig.isDeduplication, "isDeduplication");
        binWriter.addBoolField(param.isPreview,"isPreview");
        binWriter.addBoolField(param.isNewFile,"isNewFile");
        binWriter.addInt16Field(param.taskId,"taskId");
        binWriter.addStringField(param.targetFileId.c_str(), "targetFileId");
        binWriter.addInt32Field(param.targetSheetStId, "targetSheetStId");
        binWriter.addStringField(param.connId.c_str(), "connId");
        binWriter.addStringField(param.userId.c_str(), "userId");
        binWriter.addStringField(param.token.c_str(), "token");
        binWriter.addStringField(param.cronDesc.c_str(), "cronDesc");
        binWriter.addStringField(param.asyncTaskId.c_str(), "asyncTaskId");
        binWriter.addStringField(krt::utf16(QString::number(errorCode)), "errorCode");
        binWriter.addStringField(errorMsg, "errorMsg");
        binWriter.addInt32Field(warningCode, "warningCode");
        binWriter.addStringField(warningMsg, "warningMsg");
        binWriter.addStringField(param.sheetName.c_str(), "sheetName");
        binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
        WebSlice slice = {shbt.get(), binWriter.writeLength()};
        WOLOG_INFO << "[ETDA][returnMergeTask] dataAnalyzeMergeTaskDone start";
        gs_callback->dataAnalyzeMergeTaskDone(&slice);
        WOLOG_INFO << "[ETDA][returnMergeTask] dataAnalyzeMergeTaskDone end";
    }
    QStringList TaskParamProcessor::getFinalList(const QStringList& colList, etda::MergeInfo* pInfo, etda::DataSourceConfig& it, int preColsCount)
    {
        QStringList finalList;
        for (int i = 0; i < colList.size(); i++)
        {
            bool filter = false;
            for (auto it : it.filterCols)
            {
                if (it == i)
                {
                    filter = true;
                    break;
                }
            }
            if (filter)
                continue;
            for (auto it : pInfo->execResultConfig.filterCols)
            {
                if (it - preColsCount == i)
                {
                    filter = true;
                    break;
                }
            }
            if (!filter)
                finalList << colList[i];
        }
        return finalList;
    }
    void DATaskExecutor::execPreview(const QString& sql, const int rightTableBeginCol, binary_wo::BinWriter& binWriter)
    {
        WOLOG_INFO << "[ETDA][execPreview] preview begin";
        binWriter.addInt16Field(rightTableBeginCol,"rightTableBeginCol");
        
        getDAClientInstance()->SyncExecQuerySql(sql, 
        [&binWriter](bool bFinished, int rowCount, QVector<QVector<QVariant>>& datas)
        {
            binWriter.addKey("rangeData");
            binWriter.beginArray();
            WOLOG_INFO << "[ETDA][execPreview] rangeData size: " << datas.size();
            for(auto& i : datas)
            {
                binWriter.beginArray();
                for (auto& j : i)
                {
                    binWriter.addString(krt::utf16(j.toString()));
                }
                binWriter.endArray();
            }
            binWriter.endArray();
            WOLOG_INFO << "[ETDA][execPreview] SyncExecQuerySql complete";
        });
        WOLOG_INFO << "[ETDA][execPreview] preview end";
    }
    void DATaskExecutor::afterExportTask(const MergeTaskParam& param, int exportRes, const std::vector<etda::ImportFileLog>& sourcesLog, binary_wo::BinWriter& binWriter)
    {
        std::unique_ptr<etda::MergeLog> spLog = std::make_unique<etda::MergeLog>();
        QDateTime currentDateTime = QDateTime::currentDateTime().toUTC();  
        spLog->date = currentDateTime.toString("yyyy-MM-dd");  
        spLog->time = currentDateTime.toString("hh:mm");
        spLog->status = exportRes == etda::OK ? QString("success") : QString("fail");
        spLog->updateType = param.isAutoTrigger ? 1 : 0;
        spLog->taskId = param.taskId;
        spLog->sources = sourcesLog;
        std::string res;
        IETDAClient* pEtdaClient = getDAClientInstance();
        if (!param.isNewTask)
        {
            param.pData->AddDataAnalyseLog(*spLog);
            res = pEtdaClient->SerializationImportData(param.pData->GetDataAnalyseInfoList(), param.pData->GetDataAnalyseLogList());
        }
        else
        {
            pEtdaClient->AddMergeLog(spLog.release());
            res = pEtdaClient->SerializationImportData(pEtdaClient->GetMergeInfoList(), pEtdaClient->GetMergeLogList());
        }
        binWriter.addKey("importData");
        WOLOG_INFO << "[ETDA][afterExportTask] write importData size: " << res.size();
        binWriter.addArrayBuffer(reinterpret_cast<const unsigned char*>(res.c_str()), res.size());
    }
    HRESULT TaskParamProcessor::checkTaskParam(const binary_wo::VarObj& taskParam, MergeTaskParam& mergeTaskParam) 
    {
        VAR_OBJ_EXPECT_BOOL(taskParam, "isAutoSync")
        mergeTaskParam.isAutoSync = taskParam.field_bool("isAutoSync");

        VAR_OBJ_EXPECT_BOOL(taskParam, "isAutoTrigger")
        mergeTaskParam.isAutoTrigger = taskParam.field_bool("isAutoTrigger");
        if (mergeTaskParam.isAutoTrigger)
            mergeTaskParam.isAutoSync = true;

        VAR_OBJ_EXPECT_BOOL(taskParam, "isPreview")
        mergeTaskParam.isPreview = taskParam.field_bool("isPreview");

        VAR_OBJ_EXPECT_NUMERIC(taskParam, "sheetStId")
        mergeTaskParam.sheetStId = taskParam.field_int32("sheetStId");

        VAR_OBJ_EXPECT_NUMERIC(taskParam, "taskId")
        mergeTaskParam.taskId = taskParam.field_int32("taskId");

        if (taskParam.has("isNewFile"))
        {
            VAR_OBJ_EXPECT_BOOL(taskParam, "isNewFile");
            mergeTaskParam.isNewFile = taskParam.field_bool("isNewFile");
        }
        if (taskParam.has("targetFileId"))
            mergeTaskParam.targetFileId = taskParam.field_str("targetFileId");
        if (taskParam.has("targetSheetStId"))
            mergeTaskParam.targetSheetStId = taskParam.field_int32("targetSheetStId");
        if (taskParam.has("connId"))
            mergeTaskParam.connId = taskParam.field_str("connId");
        if (taskParam.has("userId"))
            mergeTaskParam.userId = taskParam.field_str("userId");
        if (taskParam.has("token"))
            mergeTaskParam.token = taskParam.field_str("token");
        if (taskParam.has("resultFilePath"))
            mergeTaskParam.resultFilePath = taskParam.field_str("resultFilePath");
        if (taskParam.has("cronDesc"))
            mergeTaskParam.cronDesc = taskParam.field_str("cronDesc");
        if (taskParam.has("asyncTaskId"))
            mergeTaskParam.asyncTaskId = taskParam.field_str("asyncTaskId");
        if (taskParam.has("isNewTask"))
            mergeTaskParam.isNewTask = taskParam.field_bool("isNewTask");
        if (taskParam.has("sheetName"))
            mergeTaskParam.sheetName = taskParam.field_str("sheetName");
        if (taskParam.has("colNamePrefix"))
            mergeTaskParam.colNamePreFix = taskParam.field_str("colNamePrefix");
        if (taskParam.has("tableText"))
            mergeTaskParam.tableText = taskParam.field_str("tableText");
        return S_OK;
    }

    bool DATaskExecutor::execExport(const QString& sql, const MergeTaskParam& param
        , binary_wo::BinWriter& binWriter
        , etda::ErrorCode& errorCode, PCWSTR errorMsg
        , std::vector<etda::ImportFileLog>& sourcesLog
        , std::function<void()> endMergeTask
        , etda::WarningCode& warningCode
        , PCWSTR warningMsg)
    {
        WOLOG_INFO << "[ETDA][execExport] export begin";
        IETDAClient* pEtdaClient = getDAClientInstance();
        etda::ServerProcessErrorCode exportRes = pEtdaClient->SyncExportFile(sql, krt::fromUtf16(param.resultFilePath.c_str()));
        binWriter.addStringField(param.resultFilePath.c_str(), "filePath");
        if (param.isAutoSync)
        {
            afterExportTask(param, exportRes, sourcesLog, binWriter);
        }

        pEtdaClient->SetCurUserId(ks_wstring());
        if (exportRes == etda::OK || exportRes == etda::EXCUTE_WARNING)
        {
            if (exportRes == etda::EXCUTE_WARNING)
            {
                warningCode =  etda::OUT_OF_LIMIT;
                warningMsg = etda::getWarningMsg<etda::OUT_OF_LIMIT>();
            }
            WOLOG_INFO << "[ETDA][execExport] export complete: " << param.resultFilePath << " warningMsg:"<< warningMsg;
        }
        else
        {
            errorCode = etda::EXPORT_FILE_FAILED;
            errorMsg = etda::getErrorMsg<etda::EXPORT_FILE_FAILED>();
            endMergeTask();
            WOLOG_INFO << "[ETDA][execExport] export fail: " << param.resultFilePath;
            return false;
        }
        WOLOG_INFO << "[ETDA][execExport] export end";
        return true;
    }

    bool DATaskExecutor::importDataSource(const MergeTaskParam& param, KDASqlGenerator& gen, SqlGeneratorParam& genParam, etda::ErrorCode& errorCode, PCWSTR errorMsg, std::vector<etda::ImportFileLog>& sourcesLog, bool& bFirstTable, int& rightTableBeginCol, std::function<void()> endMergeTask)
    {
        WOLOG_INFO << "[ETDA][importDataSource] begin";
        etda::MergeInfo* pInfo = param.pInfo;
        IETDAClient* pEtdaClient = getDAClientInstance();
        bool bSuccess = true;
        int preColsCount = 0;
        const QString columnStr = krt::fromUtf16(et_sDataAnalyzeColumn);

        for(auto it : pInfo->dataSourceConfig)
        {
            auto iterator = param.sourceIdMap.find(it.sourceId);
            if (iterator == param.sourceIdMap.end())
                continue;

            TableInfo info;
            info.tableName = KDASqlGenerator::createTableName(it.fileId, it.sheetStId, it.sourceId);
            QString preFix = wo::GetColNamePrefix(it.bookName, it.sheetName);
            QString filePath = iterator->second;
            int headRow = it.headRow;
            etda::ImportFileParam importParam;
            importParam.srcPath = filePath;
            importParam.tableName = info.tableName;
            importParam.beginRow = headRow;
            ASSERT(!param.colNamePreFix.empty());
            importParam.colText = krt::fromUtf16(param.colNamePreFix.c_str());
            int sourceIndex = it.sourceIndex;
            preFix = QString("%1%2_").arg(krt::fromUtf16(param.tableText.c_str())).arg(QString::number(sourceIndex));
            importParam.colNamePreFix = preFix;
            auto curMergeCondition = it.mergeCondition;
            etda::preprocessColumnNameList(curMergeCondition, columnStr);
            if (pInfo->execResultConfig.isTagSource)
            {
                importParam.isTagSource = true;
                importParam.bookName = it.bookName;
                importParam.sheetName = it.sheetName;
                genParam.bTagSource = true;
            }

            {
                QFile file(filePath);
                if (!file.exists())
                {
                    WOLOG_INFO << "[ETDA][importDataSource] file does not exist: " << krt::utf16(filePath);
                }
                else if (file.size() == 0)
                {
                    WOLOG_INFO << "[ETDA][importDataSource] file is empty: " << krt::utf16(filePath);
                }
            }

            auto start = std::chrono::high_resolution_clock::now();  

            if (pEtdaClient->SyncImportFile(importParam) != etda::OK)
            {
                errorCode = etda::IMPORT_FAILED;
                errorMsg = etda::getErrorMsg<etda::IMPORT_FAILED>();
                bSuccess = false;
                endMergeTask();
                WOLOG_INFO << "[ETDA][importDataSource] import file fail: " << krt::utf16(filePath);
                return bSuccess;
            }
            else
            {
                WOLOG_INFO << "[ETDA][importDataSource] import file success: " << krt::utf16(filePath);
            }
            auto end = std::chrono::high_resolution_clock::now();
            std::chrono::duration<double> duration = std::chrono::duration_cast<std::chrono::duration<double>>(end - start);
            WOLOG_INFO << "[ETDA][importDataSource] import file time: " << duration.count();
            bool requestTableInfo = pEtdaClient->SyncQueryTableInfo(info.tableName, 
            [&gen, &info](bool bFinished, int rowCount, QVector<QVector<QVariant>>& datas)
            {
                QString id = etda::k_column_id;
                for (auto& i : datas)
                {
                    for (auto& j : i)
                    {
                        if (j == id)
                            continue;
                        gen.setTableColName(info.tableName, j.toString());
                    }
                }
            });
            QStringList colList = gen.getTableUsedColNames(info.tableName);
            if (requestTableInfo)
            {
                WOLOG_INFO << "[ETDA][importDataSource] request table info success: " << krt::utf16(filePath);
                WOLOG_INFO << "[ETDA][importDataSource]request table info: " << krt::utf16(colList.join(" "));
            }
            else 
            {
                WOLOG_INFO << "[ETDA][importDataSource] request table info fail: " << krt::utf16(filePath);
            }
            if (genParam.type == etda::MergeType::Union)
            {
                colList.clear();
                for (auto i: curMergeCondition)
                {
                    colList << i;
                }
            }
            if (bFirstTable)
            {
                rightTableBeginCol = colList.size() - it.filterCols.size();
                bFirstTable = false;
            }
            TaskParamProcessor::filterEmptyCol(colList, curMergeCondition);
            QStringList finalList = TaskParamProcessor::getFinalList(colList, pInfo, it, preColsCount);
            if (pInfo->mergeConfig.mergeDirection == etda::MergeType::Join)
            {
                preColsCount += colList.size();
            }
            gen.replaceColNames(info.tableName, finalList);
            if (pInfo->mergeConfig.mergeDirection == etda::MergeType::Union)
            {
                info.mergeCols = finalList;
            }
            else
            {
                if (!curMergeCondition.empty())
                    info.mergeCols << curMergeCondition[0];
            }
            if (info.mergeCols.empty())
            {
                errorCode = etda::IMPORT_FAILED;
                errorMsg = etda::getErrorMsg<etda::NO_MERGE_CONDITION>();
                bSuccess = false;
                endMergeTask();
                 WOLOG_INFO << "[ETDA][importDataSource] mergeCondition is null";
                return bSuccess;
            }
            genParam.tableInfo.push_back(info);

            etda::ImportFileLog fileLog;

            QString selectCountSql = gen.generateCountSQL(info.tableName);
            pEtdaClient->SyncExecQuerySql(selectCountSql, 
            [&fileLog](bool bFinished, int rowCount, QVector<QVector<QVariant>>& datas)
            {
                if (datas.size() == 2 && !datas[1].isEmpty())
                {
                    fileLog.cols = datas[1][0].toInt();
                }
            });
            fileLog.fileName = it.bookName;
            fileLog.sheetName = it.sheetName;
            fileLog.status = QString("success");
            sourcesLog.push_back(fileLog);
        }
        WOLOG_INFO << "[ETDA][importDataSource] end ret:" << bSuccess;
        return bSuccess;
    }

    void DATaskExecutor::removeFile(const MergeTaskParam& param)
    {
        for (auto& filePath : param.sourceIdMap)
        {
            WOLOG_INFO << "DATaskExecutor::remove file: " << filePath.second;
            QFile::remove(filePath.second);
        }
    }

    void DATaskExecutor::execMergeTask(MergeTaskParam param)
    {
        KDASqlGenerator gen(param.isPreview);
        SqlGeneratorParam genParam;
        binary_wo::BinWriter binWriter;
        etda::ErrorCode errorCode = etda::SUCCESS;
        etda::WarningCode warningCode = etda::NO_WARNING;
        PCWSTR warningMsg = __X("");
        PCWSTR errorMsg = __X("success");
        etda::MergeInfo* pInfo = param.pInfo;
        genParam.type =  pInfo->mergeConfig.mergeDirection;
        genParam.joinMode = pInfo->mergeConfig.joinMode;
        auto endMergeTask = std::bind(&returnMergeTask, std::ref(param), std::ref(binWriter), std::ref(errorCode), std::ref(errorMsg)
        , std::ref(warningCode), std::ref(warningMsg));  
        for (int i = 0; i < pInfo->execResultConfig.finalColNames.size(); i++)
        {
            bool filter = false;
            for (auto it : pInfo->execResultConfig.filterCols)
            {
                if (it == i)
                {
                    filter = true;
                    break;
                }
            }
            if (filter)
                continue;
            genParam.finalColNames << pInfo->execResultConfig.finalColNames[i];
        }
        int rightTableBeginCol = 0;
        
        bool bFirstTable = true;
        std::vector<etda::ImportFileLog> sourcesLog;
        QString sql;
        IETDAClient* pEtdaClient = getDAClientInstance();

        bool bSuccess = importDataSource(param, gen, genParam, errorCode, errorMsg, sourcesLog, bFirstTable, rightTableBeginCol, endMergeTask);
        if (!bSuccess)
        {
            pEtdaClient->SetCurUserId(ks_wstring());
            WOLOG_ERROR << "[ETDA][execMergeTask] importDataSource Failed";
            return;
        }
        if (pInfo->execResultConfig.isDeduplication)
        {
            genParam.bDeduplication = true;
            QString sqls = gen.generateeDeduplicationSQL(genParam);
            pEtdaClient->SyncExecDmlSql(sqls);
        }
        if (genParam.type == etda::MergeType::Join)
        {
            QString indexSql = gen.generateIndexSQL(genParam);

            if (!indexSql.isEmpty())
            {
                WOLOG_INFO << "[ETDA][execMergeTask] index sql: " << krt::utf16(indexSql);
                etda::ServerProcessErrorCode res = pEtdaClient->SyncExecDmlSql(indexSql);
                if (res != etda::OK)
                {
                    WOLOG_INFO << "[ETDA][execMergeTask] create index fail";
                }
                else 
                {
                    WOLOG_INFO << "[ETDA][execMergeTask] create index success";
                }
            }
        }
        {
            QStringList list = genParam.finalColNames;
            etda::preprocessColumnNameList(list, krt::fromUtf16(et_sDataAnalyzeColumn));
            QMap<QString, QString> colMap;
            for (int i = 0; i < list.size(); i++)
            {
                colMap[list[i]] = genParam.finalColNames[i];
            }
            WOLOG_INFO << "[ETDA][SyncInsertColName] before: " << krt::utf16(genParam.finalColNames.join(" "));
            WOLOG_INFO << "[ETDA][SyncInsertColName] after: " << krt::utf16(list.join(" "));
            pEtdaClient->SyncInsertColName(colMap);
        }
        sql = gen.generateMergeSQL(genParam);

        auto start = std::chrono::high_resolution_clock::now();  

        if (param.isPreview)
        {
            execPreview(sql, rightTableBeginCol, binWriter);
            removeFile(param);
        }
        else
        {
            bool bSuccess = execExport(sql, param, binWriter, errorCode, errorMsg, sourcesLog, endMergeTask, warningCode, warningMsg);
            removeFile(param);
            if (!bSuccess)
            {
                WOLOG_ERROR << "[ETDA][execMergeTask] execExport Failed";
                return;
            }
        }
        warningMsg = etda::getWarningMsg<etda::NO_WARNING>();
        auto end = std::chrono::high_resolution_clock::now(); 
        std::chrono::duration<double> duration = std::chrono::duration_cast<std::chrono::duration<double>>(end - start);
        if (param.isPreview)
        {
            WOLOG_INFO << "[ETDA][execMergeTask] preview time: " << duration.count();
        }
        else 
        {
            WOLOG_INFO << "[ETDA][execMergeTask] export time: " << duration.count();
    
        }
        binary_wo::BinWriter dcBinWriter;
        dcBinWriter.addKey(param.isPreview ? "preview_execution_time" : "export_execution_time");
        dcBinWriter.addInt32(duration.count());

        binary_wo::BinWriter::StreamHolder shbt = dcBinWriter.buildStream();
        WebSlice slice = {shbt.get(), dcBinWriter.writeLength()};
        gs_callback->collectInfo("data_analyze_collect", &slice);
        endMergeTask();
    }

    void TaskParamProcessor::ParseExecResultConfig(etda::MergeInfo* pInfo, binary_wo::VarObj& execResultConfig)
    {
        if (execResultConfig.has("targetFileId"))
            pInfo->execResultConfig.targetFileId = krt::fromUtf16(execResultConfig.field_str("targetFileId"));
        
        if (execResultConfig.has("targetSheetStId"))
            pInfo->execResultConfig.targetSheetStId = execResultConfig.field_uint32("targetSheetStId");
        
        if (execResultConfig.has("filterCols"))
        {
            pInfo->execResultConfig.filterCols.clear();
            binary_wo::VarObj filterCols = execResultConfig.get_s("filterCols");
            for (int i = 0; i < filterCols.arrayLength_s(); i++)
            {
                pInfo->execResultConfig.filterCols.push_back(filterCols.item_int32(i));
            }
        }
        
        if (execResultConfig.has("finalColNames"))
        {
            pInfo->execResultConfig.finalColNames.clear();
            binary_wo::VarObj finalColNames = execResultConfig.get_s("finalColNames");
            for (int i = 0; i < finalColNames.arrayLength_s(); i++)
            {
                pInfo->execResultConfig.finalColNames.push_back(krt::fromUtf16(finalColNames.item_str(i)));
            }
        }
        if (execResultConfig.has("isDeduplication"))
        {
            pInfo->execResultConfig.isDeduplication = execResultConfig.field_bool("isDeduplication");
        }
        if (execResultConfig.has("isTagSource"))
        {
            pInfo->execResultConfig.isTagSource = execResultConfig.field_bool("isTagSource");
        }
    }
    void TaskParamProcessor::filterEmptyCol(const QStringList& colList, std::vector<QString>& mergeCols)
    {
        //预览时存在空列，导出时会跳过空列，需要过滤空列对应的列名，避免自动生成的空列名导致合并失败
        QSet<QString> colSet;
        for (auto& i : colList)
        {
            colSet.insert(i);
        }

        for (int i = mergeCols.size() - 1; i >= 0; i--)
        {
            if (mergeCols[i] == QLatin1String("NULL"))
                continue;
            if (colSet.find(mergeCols[i]) == colSet.end())
            {
                mergeCols[i] = QLatin1String("NULL");
            }
        }
    }
    etda::DataSourceConfig TaskParamProcessor::ParseDataSourceConfig(binary_wo::VarObj& dataSourceConfigItem, KSerialWrapBinWriter& acpt)
    {
        etda::DataSourceConfig dataSourceConfig;
        IETDAClient* pEtdaClient = getDAClientInstance();
        dataSourceConfig.fileId = krt::fromUtf16(dataSourceConfigItem.field_str("fileId"));
        dataSourceConfig.bookName = krt::fromUtf16(dataSourceConfigItem.field_str("bookName"));
        dataSourceConfig.sheetName = krt::fromUtf16(dataSourceConfigItem.field_str("sheetName"));
        dataSourceConfig.sheetStId = dataSourceConfigItem.field_uint32("sheetStId");
        dataSourceConfig.headRow = dataSourceConfigItem.field_int32("headRow");
        dataSourceConfig.sourceId = pEtdaClient->GenerateSourceId();
        dataSourceConfig.sourceIndex = dataSourceConfigItem.field_int32("sourceIndex");
        acpt.beginStruct();
        acpt.addInt16("sourceId", dataSourceConfig.sourceId);
        acpt.addString("fileId", dataSourceConfigItem.field_str("fileId"));
        acpt.addInt16("sheetStId", dataSourceConfig.sheetStId);
        acpt.endStruct();

        binary_wo::VarObj filterCols = dataSourceConfigItem.get_s("filterCols");
        for (int i = 0; i < filterCols.arrayLength_s(); i++)
        {
            dataSourceConfig.filterCols.push_back(filterCols.item_int32(i));
        }

        binary_wo::VarObj mergeColCondition = dataSourceConfigItem.get_s("mergeColCondition");
        for (int i = 0; i < mergeColCondition.arrayLength_s(); i++)
        {
            QString mergeCol = krt::fromUtf16(mergeColCondition.item_str(i));
            if (mergeCol.isEmpty())
            {
                mergeCol = QString("NULL");
            }
            dataSourceConfig.mergeCondition.push_back(mergeCol);
        }

        return dataSourceConfig;
    };

DataAnalyseImportHelper::DataAnalyseImportHelper(wo::KEtWorkbook* wwb, int targetSheetId)
    : m_wwb(wwb)
    , m_targetSheetStId(targetSheetId)
{

}

DataAnalyseImportHelper::~DataAnalyseImportHelper()
{

}

HRESULT DataAnalyseImportHelper::operator()(EtTaskExecBase* pTask, KwCommand* cmd, KEtRevisionContext* pCtx, const ks_wstring& sheetName, const QString& filePath)
{
    binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, pCtx);

    // 当前文件有不可见区域禁止导入
    if (pCtx->getProtectionCtx()->isBookHasHiddenProperty())
        return E_DATAANALYZE_PERMISSION_FORBIDDEN;

    if (filePath.isEmpty())
        return E_DATAANALYZE_FAILED;
    ks_stdptr<ISheet> spTargetSheet;
    HRESULT hr = prepareTargetSheet(&spTargetSheet);
    if (FAILED(hr))
        return E_DATAANALYZE_FAILED;

    //检查区域权限
    IDX idx = INVALIDIDX;
    spTargetSheet->GetIndex(&idx);
    RANGE dest(spTargetSheet->GetBMP());
    dest.SetSheetFromTo(idx);
    dest.SetRowFromTo(0, spTargetSheet->GetBMP()->cntRows - 1);
    dest.SetColFromTo(0, spTargetSheet->GetBMP()->cntCols - 1);

    auto checkCanEdit = [&]()->HRESULT
    {
        CHECK_PROTECTION_ALLOW_EDIT(dest, pCtx);
        return S_OK;
    };
    if (FAILED(hr = checkCanEdit()))
        return E_DATAANALYZE_PERMISSION_FORBIDDEN;

    //创建导入上下文
    ks_stdptr<IRangeInfo> spPasteRgInfo = m_wwb->CreateRangeObj(dest);
    range_helper::ranges rgs;
    ks_stdptr<IAppCoreRange> spPasteCoreRange;
    VS(spPasteRgInfo->GetAppCoreRange(&spPasteCoreRange));
    ASSERT(spPasteCoreRange);
    VS(spPasteCoreRange->GetFilteredIRanges(FALSE, &rgs));

    per_imp::ImportFileParam importParam;
    importParam.delimiter = __Xc(',');    //csv 的分隔符
    importParam.guessEncode = true;

    PASTEINFO info = { 0 };
    info.grbits.fPaste = 1;
    info.grbits.fRemote = 1;
    info.grbits.fHasCells = 0;
    CELL cell = { 0 };

	ks_stdptr<IKRanges> spPasteRgs;
    IETPersist* spPersist = spPersist = m_wwb->GetCoreApp()->GetAppPersist()->GetPersist();
    hr = spPersist->CreatePasteRanges(m_wwb->GetCoreWorkbook()->GetBook(),
        rgs, &info, NULL, NULL, &spPasteRgs, &cell);
    if (FAILED(hr))
        return E_DATAANALYZE_FAILED;

    //开始导入
    bool bLoseInfo = false;
    hr = spPersist->ImportFile(nullptr, nullptr, filePath, &importParam, bLoseInfo);
    if (FAILED(hr))
    {
        if (m_bNewSheet)
        {
            std::vector<UINT> vecSheetStId;
            vecSheetStId.push_back(m_targetSheetStId);
            SheetOperatorHelper::DeleteSheets(m_wwb, pCtx, vecSheetStId);
        }
        return E_DATAANALYZE_FAILED;
    }
    //重命名
    QString newSheetName;
    if (sheetName.empty())
        newSheetName = krt::fromUtf16(et_sDataAnalyzeMergeResult);
    else
        newSheetName = krt::fromUtf16(sheetName.c_str());
    IDX index = INVALIDIDX; int count = 1;
    IBook* pBook = spTargetSheet->LeakBook();
    if (m_bNewSheet)
    {
        while (SUCCEEDED(pBook->GetSheetIdxByName(krt::utf16(newSheetName), &index)))
            newSheetName = krt::fromUtf16(et_sDataAnalyzeMergeResult) + QString("(%1)").arg(QString::number(count++));
        spTargetSheet->SetName(krt::utf16(newSheetName), newSheetName.length());
    }
    //成功了，最后赋值
    m_targetSheet = spTargetSheet;
    return S_OK;
}

ISheet* DataAnalyseImportHelper::getTargetSheet()
{
    return m_targetSheet;
}

HRESULT DataAnalyseImportHelper::prepareTargetSheet(OUT ISheet** ppSheet)
{
    ks_stdptr<ISheet> spTargetSheet;
    if (m_targetSheetStId == 0) //意味着要新建
    {
        ks_stdptr<etoldapi::Worksheets> spTarWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
        if (!spTarWorksheets)
            return E_FAIL;
        int tarSheetCount = spTarWorksheets->GetSheetCount();
        if (tarSheetCount <= 0)
            return E_FAIL;

        // 获取最后一个sheet
        ks_stdptr<etoldapi::_Worksheet> spSheetInsertAfter = spTarWorksheets->GetSheetItem(tarSheetCount - 1);
        if (!spSheetInsertAfter)
            return E_FAIL;

        ks_bstr bName;
        spSheetInsertAfter->get_Name(&bName);
        KComVariant varName(bName), varNull, varCount(1, VT_I4);
        ks_stdptr<IKCoreObject> spNewWorksheet;
        HRESULT hr = spTarWorksheets->Add(varNull, varName, varCount, varNull, &spNewWorksheet);
        if (SUCCEEDED(hr) && spNewWorksheet)
        {
            m_bNewSheet = true;
            spTargetSheet = ks_castptr<IKWorksheet>(spNewWorksheet)->GetSheet();
            m_targetSheetStId = spTargetSheet->GetStId(); //新的id
        }
        else
        {
            return hr;
        }
    }
    else
    {
        IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
        int targetSheetIdx = INVALIDIDX;
        pBook->STSheetToRTSheet(m_targetSheetStId, &targetSheetIdx);
        if (targetSheetIdx == INVALIDIDX)
            return E_KSHEET_SHEET_NOT_FOUND;
        pBook->GetSheet(targetSheetIdx, &spTargetSheet);

        //清理旧合并数据
        RANGE rg(m_wwb->GetBMP());
        rg.SetRowFromTo(0, spTargetSheet->GetBMP()->cntRows - 1);
        rg.SetColFromTo(0, spTargetSheet->GetBMP()->cntCols - 1);
        rg.SetSheetFromTo(targetSheetIdx);
        m_wwb->CreateRangeObj(rg)->ClearContents();
    }

    if (ppSheet)
        *ppSheet = spTargetSheet.detach();
    return S_OK;
}

WebInt ExecDataAnalyzeInl(KEtWorkbooks* wbs, const UserContext* userContext, const WebSlice* slice, WebSlice* pOutput)
{
    ExecDAOutput daOutput(pOutput);
    if (wbs && wbs->GetWorkbook())
    {
        KS_TRY {
            binary_wo::BinReader reader(slice->data, slice->size);
            binary_wo::VarObjRoot root = reader.buildRoot();
            binary_wo::VarObj rootData = root.cast();

            VAR_OBJ_EXPECT_ARRAY(rootData, "commands");
            VarObj cmds = rootData.get("commands");

            if ( 1 == cmds.arrayLength())
            {
                VarObj cmd = cmds.at(0);
                std::unique_ptr<DataAnalyzeTaskBase> spTask = DataAnalyzeTaskFactory::CreateDataAnalyzeCmd(cmd, wbs->GetWorkbook());
                if (!spTask)
                {
                    daOutput.AddError(WO_INVALID_ARG, __X("error param"));
                    return WO_INVALID_ARG;
                }
                VAR_OBJ_EXPECT_STRUCT(cmd, "param");
                VarObj param = cmd.get("param");
                HRESULT hr = (*spTask)(userContext, param, &daOutput);
                if (FAILED(hr))
                    daOutput.AddError(hr, __X("exec data analyze fail"));
            }
            else
            {
                daOutput.AddError(WO_INVALID_ARG, __X("too much cmd"));
                return WO_INVALID_ARG;
            }
        }
        KS_CATCH(const binary_wo::ks_exception_msg & e) {
            HRESULT hr = e.get_result();
            PCWSTR msg = GetErrWideString(hr);
            daOutput.AddError(hr, msg);
            WOLOG_ERROR << "[ExecDataAnalyze] occur exception : " << hr << msg;
        }
    }
    else
    {
        daOutput.AddError(WO_FAIL, __X("workbook not open yet"));
    }
    return WO_OK;
}
}