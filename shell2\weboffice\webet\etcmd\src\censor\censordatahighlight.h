#ifndef __WEBOFFICE_WEBET_CENSORDATA_HIGHLIGHT_H__
#define __WEBOFFICE_WEBET_CENSORDATA_HIGHLIGHT_H__

#include "kso/l10n/et/et_styles.h"

namespace wo
{

    class KEtWorkbook;

    class CensorKeyWordHighLightHandler
    {
        friend class KEnumETCellValueAcpt;
    public:
        CensorKeyWordHighLightHandler(KEtWorkbook* pWorkbook);
        ~CensorKeyWordHighLightHandler();
        
        void StartHandleETCell();

    private:
        void getETCellText(const_token_ptr pToken, BSTR*);
        void _HandleETCellContent(ROW row, COL col, IDX sheetIdx,PCWSTR str, BOOL bCellFormula);
        void _SetETCellKeyWordHighlight(PCWSTR str, const RANGE& rg, BOOL bCellFormula);
        void _SetETCellKeyWordHighlight(PC<PERSON><PERSON> str, const RANGE& rg, BOOL bCellFormula, HighlightWordData* keyWordData);
        void _GetETCellKeyWordData(PCWSTR str, HighlightWordData** data);
        HRESULT _CollectCellText(etoldapi::_Worksheet *pWorksheet);
        void setKeyWordFontColor(HighlightFont* fontInfo, etoldapi::Range* pRange, int32_t prefixLen = 0);

        void prepareCommentHyperLink(etoldapi::_Worksheet *pWorksheet);
        void buildExtraSheetCell(PCWSTR sheetName, const RANGE& rg, PCWSTR str, HighlightWordData* keyWordData);
        void setBackgroundFontColor(etoldapi::Range* pCellRg, HighlightWordData* keyWordData);
        const RANGE dstCellInExtraSheet();

        QString buildShapeAddStr(IDX shtIdx, PCWSTR sheetName, PCWSTR addStr, int32_t &prefixLen);
        QString buildCellAddStr(Hyperlink* hyperlink, IDX shtIdx, PCWSTR sheetName, PCWSTR addStr, int32_t &prefixLen);
        void setCellAndHighlight(const RANGE &dstCellRg, PCWSTR cellStr, int32_t prefixLen, HighlightWordData* keyWordData);
        void setHyperlink(const RANGE &rg, PCWSTR bstrAddress);

        void _handleShape(etoldapi::_Worksheet *pWorksheet);
        void _handleShape(drawing::AbstractShape* shape);

        void _handleQrCodeText(PCWSTR text);
        void _creatQrCodeSheet();
        void _handleExtData(drawing::AbstractShape* shape);

        void _handleChart(drawing::AbstractShape* shape);

        void _realHandleChart(AbstractObject* pAbsOject);

        void _handleShapeBase(KsoTextFrame2* pTextFrame2, KsoFillFormat* pKsoFillFormat, bool isMath = false);

        void _realHandleShape(drawing::AbstractShape* abstractShape);
        void _realHandleChartShape(drawing::AbstractShape* abstractShape);

        void GetApiShape(IKShape* pShape, KsoShape** ppShape);
        void modifyFontColor(KsoTextRange2* ksoTextRange2, int color);

        void GetTextFrame(drawing::AbstractShape* pAbShape, KsoTextFrame2** ppTextFrame, KsoChartFormat** ppKsoChartFormat);

    private:
        KEtWorkbook* m_wwb;
        ks_stdptr<IETStringTools> m_spStrTools;
        ks_stdptr<IBookOp> m_spBookOp;
        ks_stdptr<etoldapi::_Worksheet> m_spExtraCensorSheet;
        ROW m_sheetRow;
        ROW m_blkRow;
        COL m_sheetCol;
        COL m_blkCol;
        IDX m_sheetIdx;

        // 二维码一维码相关数据
        ks_stdptr<etoldapi::_Worksheet> m_spQrCodeSheet;
        COL m_QrCodeCurrentCol;
        ROW m_QrCodeCurrentRow;

    };

    ///////////////////////////////////////////////////////////////////////
    class KEnumETCellValueAcpt : public ICellValueAcpt
    {
    public:
        KEnumETCellValueAcpt(CensorKeyWordHighLightHandler* handler, IDX sheetIdx)
            : m_handler (handler)
            , m_sheetIdx(sheetIdx)
        {

        }

        STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
        {
            if (!pToken)
                return 0;

            ks_bstr bstrCellText;
            BOOL bCellFormula = FALSE;
            m_handler->m_spBookOp->GetCellFormulaFlags(m_sheetIdx, row, col, &bCellFormula, NULL);
            if (FALSE == bCellFormula)
            {
                m_handler->getETCellText(pToken, &bstrCellText);
            }
            else
            {
                m_handler->m_spBookOp->GetCellFormulaText(m_sheetIdx, row, col, &bstrCellText, NULL);
            }

            if (!bstrCellText.empty())
                m_handler->_HandleETCellContent(row, col, m_sheetIdx, bstrCellText, bCellFormula);
            return 0;
        }
    private:
        CensorKeyWordHighLightHandler* m_handler;
        IDX m_sheetIdx;
    };

} // namespace wo

#endif // __WEBOFFICE_WEBET_CENSORDATA_HIGHLIGHT_H__