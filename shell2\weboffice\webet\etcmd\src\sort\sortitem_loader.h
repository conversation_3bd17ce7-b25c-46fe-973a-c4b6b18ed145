﻿#ifndef __WEBOFFICE_WEBET_SORTITEM_LOADER_H__
#define __WEBOFFICE_WEBET_SORTITEM_LOADER_H__

#include <appcore/et_appcore_cellcolor_itf.h>

#include <vector>

namespace wo
{
namespace range_sort
{

struct SortItem
{
	SortItem() : keyRange(NULL) { reset(); }

	void reset()
	{
		sortOn = ::xlSortOnValues;
		bAscending = TRUE;
		spCellColor = NULL;
		spFontColor = NULL;
		spCellIcon = NULL;
		customList.clear();
	}

	int sortOn;
	BOOL bAscending;
	ks_stdptr<ICellColor> spCellColor;
	ks_stdptr<IFontColor> spFontColor;
	ks_stdptr<ICellIcon> spCellIcon;
	QStringList customList;
	RANGE keyRange; // just for read
};

typedef std::vector<SortItem> SortItems;

bool LoadSortItems(etoldapi::_Application *app, IKSortData *sort_data, SortItems *sort_items);

} // namespace range_sort
} // namespace wo

#endif /* __KXSORTDLG_H__ */
