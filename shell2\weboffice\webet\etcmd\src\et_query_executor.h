﻿#ifndef __WEBET_ET_QUERY_EXECUTOR_H__
#define __WEBET_ET_QUERY_EXECUTOR_H__
#include <public_header/revision/src/kwrevisionctrl.h>
#include "kfc/tools/smart_wrap.h"

#define BLS_INVALID -1

namespace wo
{
class KEtWorkbook;
class KEtRevisionContext;
class EtQueryExecBase;

class EtQueryExecutor
{
public:
	typedef std::unordered_map<						\
		ks_wstring, std::shared_ptr<EtQueryExecBase>,	\
		kfc::tools::str_hash_ic<ks_wstring>,		\
		kfc::tools::str_equal_ic<ks_wstring>> ExecMap;

public:
	EtQueryExecutor(wo::KEtWorkbook*);
	virtual ~EtQueryExecutor();

	void Init();
protected:
	void initBaseExecMap();
	void initExecMap();
	void initDbSheetExecMap();

public:
	virtual WebInt Query(binary_wo::VarObj cmds, IRevisionContext* ctx, binary_wo::BinWriter& bw);

protected:
	template <typename ExecClass_>
	void mapAddExecClass();

protected:
	wo::KEtWorkbook* m_wwb;
	KEtRevisionContext* m_ctx;
	ExecMap m_execMap;
};

}

#endif //__WEBET_ET_QUERY_EXECUTOR_H__
