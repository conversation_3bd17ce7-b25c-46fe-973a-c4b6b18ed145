﻿#include "userorganizes.h"
#include "webetlink.h"
#include "util.h"
#include "serialize_impl.h"
#include <regex>


extern Callback* gs_callback;
namespace wo
{

KUserOrganizesCache::KUserOrganizesCache(IBook* pBook, UserOrganizesInfo* pInfo)
{
    m_bk = pBook;
    m_userOrganizesInfo = pInfo;
}

bool KUserOrganizesCache::FetchAll()
{
    std::set<ks_wstring> organizeIds;
	int wsCount = 0;
	m_bk->GetSheetCount(&wsCount);
    bool bDirty = false;
    for (int i = 0; i < wsCount; i++)
    {
        if (!util::isCellImgListSheet(m_bk, i))
        {
            ks_stdptr<ISheet> spSheet;
            m_bk->GetSheet(i, &spSheet);
            if (spSheet)
            {
                ks_stdptr<ISheetProtection> spSheetProtection = util::getSheetProtection(spSheet);
                if (spSheetProtection)
                {
                    int uCount = spSheetProtection->GetUserRangeCount();
                    for (int j = 0; j < uCount; j++)
                    {
                        const std::vector<AllowEditRangeUserData>* arrUserData = spSheetProtection->GetUserRangeUser(j);
                        for (int k = 0; k < arrUserData->size(); k++)
                        {
                            AllowEditRangeUserData aeru = arrUserData->at(k);
                            if (checkOrganizeId(aeru.userId, organizeIds))
                                bDirty = true;
                        }
                    }

                    if(!bDirty)
                    {
                        std::vector<ks_wstring> vecColProtectAdmins;
                        spSheetProtection->GetProtectedColAdmins(vecColProtectAdmins);
                        for (size_t index = 0; index < vecColProtectAdmins.size(); ++index)
                        {
                            if (checkOrganizeId(vecColProtectAdmins.at(index), organizeIds))
                                bDirty = true;
                        }
                    }
                }
            }
        }
    }

    m_organizeIds = organizeIds;

    if (bDirty)
        m_idsCache.clear();

    return bDirty;
}


WebInt KUserOrganizesCache::userIsInOrganize(const PCWSTR userId, const PCWSTR connId, std::map<ks_wstring, bool>& mapOrganizeIds)
{
	if (userId == NULL)
		return WO_FAIL;
    if (gs_callback && gs_callback->userIsInOrganize)
    {
        binary_wo::BinWriter bw;
        bw.addStringField(connId, "connId");
        bw.beginArray("items");
        for (auto it = mapOrganizeIds.begin(); it != mapOrganizeIds.end(); it++)
        {
            bw.beginStruct();
            bw.addStringField((*it).first.c_str(), "organizeId");
            bw.addStringField(userId, "userId");
            bw.endStruct();     
        }
        bw.endArray();

        binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
        WebSlice slice = {shbt.get(), bw.writeLength()};
        WebSlice result = {nullptr, 0};
        WebInt resCode = gs_callback->userIsInOrganize(&slice, &result);
        if (resCode != WO_OK)
            return resCode;
        binary_wo::BinReader rd(result.data, result.size);
        binary_wo::VarObjRoot rdRoot = rd.buildRoot();
        binary_wo::VarObj res = rdRoot.cast();
        binary_wo::VarObj data = res.get_s("data");
        for (int i = 0; i < data.arrayLength_s(); i++)
        {
            binary_wo::VarObj item = data.at_s(i);
            bool isIn = item.field_bool("isIn");
            PCWSTR organizeId = item.field_str("organizeId");
            ks_wstring oId = organizeId;
            auto it = mapOrganizeIds.find(oId);
            if (it != mapOrganizeIds.end())
            {
                it->second = isIn;
            }
        }
    }
    return WO_OK;
}

bool KUserOrganizesCache::checkOrganizeId(const ks_wstring& userId, std::set<ks_wstring>& organizeIds)
{
    PCWSTR pattern = __X("\\w+(_team|_department|_group)\\w*");
    QRegExp regexp(krt::fromUtf16(pattern));
    if (regexp.exactMatch(krt::fromUtf16(userId.c_str())))
    {
        organizeIds.insert(userId);
        if (m_organizeIds.find(userId) == m_organizeIds.end())
            return true;
    }
    return false;
}

HRESULT KUserOrganizesCache::FetchCurrentUserOrganizeInfo()
{
    if (m_userOrganizesInfo == NULL)
        return E_FAIL;

    wo::IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
    if (pCtx == NULL)
        return E_FAIL;

    PCWSTR userId = pCtx->getUser()->userID();
    PCWSTR connId = pCtx->getUser()->connID();
    if (m_organizeIds.size() > 0 && m_idsCache.find(userId) == m_idsCache.end())
    {
        std::map<ks_wstring, bool> mapOrganizeIds;
        for (auto it = m_organizeIds.begin(); it != m_organizeIds.end(); it++)
        {
            mapOrganizeIds[*it] = false;
        }

        WebInt res = userIsInOrganize(userId, connId, mapOrganizeIds);
        if (res != WO_OK)
            return E_FAIL;
    
        for (auto it1 = mapOrganizeIds.begin(); it1 != mapOrganizeIds.end(); it1++)
        {
            const ks_wstring& oId = it1->first;
            bool isIn = it1->second;
            auto it2 = (*m_userOrganizesInfo).find(oId);
            if (it2 != (*m_userOrganizesInfo).end())
            {
                if (isIn)
                    it2->second.insert(userId);
                else
                {
                    auto it3 = it2->second.find(userId);
                    if (it3 != it2->second.end())
                    {
                        it2->second.erase(it3);
                    }
                }
            }
            else
            {
                if (isIn)
                {
                    std::set<ks_wstring> ids;
                    ids.insert(userId);
                    (*m_userOrganizesInfo)[oId] = ids;
                }
            }
        }
        m_idsCache.insert(userId);
    }
    return S_OK;
}

HRESULT KUserOrganizesCache::ResetCurrentUserOrganizeInfo()
{
    wo::IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
    if (pCtx == NULL)
        return E_FAIL;
    
    PCWSTR userId = pCtx->getUser()->userID();
    // 从用户组缓存中剔除
    for (auto it = (*m_userOrganizesInfo).begin(); it != (*m_userOrganizesInfo).end(); it++)
    {
        auto it2 = it->second.find(userId);
        if (it2 != it->second.end())
        {
            it->second.erase(it2);
        }
    }
    // 从已缓存用户列表中剔除
    auto it = m_idsCache.find(userId);
    if (it != m_idsCache.end())
    {
        m_idsCache.erase(it);
    }
        
    return S_OK;
}

WebInt KUserOrganizesCache::SerialContent(ISerialAcceptor* acpt)
{
    if (m_userOrganizesInfo == NULL)
        return WO_FAIL;

    wo::IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
    if (pCtx == NULL)
        return WO_FAIL;
    PCWSTR userId = pCtx->getUser()->userID();

    acpt->addKey("userOrganizes");
    acpt->beginArray();

    for (auto it = (*m_userOrganizesInfo).begin(); it != (*m_userOrganizesInfo).end(); it++)
    {
        ks_wstring oId = it->first;
        std::set<ks_wstring> ids = it->second;
        acpt->beginStruct();
        acpt->addString("organizeId", oId.c_str());
        if (ids.find(userId) != ids.end())
            acpt->addBool("isIn", true);
        else
            acpt->addBool("isIn", false);
        acpt->endStruct();
    }

    acpt->endArray();

    return WO_OK;
}

}