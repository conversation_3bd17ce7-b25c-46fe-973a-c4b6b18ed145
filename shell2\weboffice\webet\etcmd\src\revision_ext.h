﻿#ifndef __WEBET_REVISION_EXT_H__
#define __WEBET_REVISION_EXT_H__
#include "webdeclare.h"
#include <public_header/revision/src/kwrevisionctrl.h>
#include <public_header/revision/src/kwversion.h>
#include "wo/worksheet_obj.h"
#include "appcore/et_appcore_sparkline_itf.h"

struct UNDO_TAG;

class KEtCustomVerInfo: public IUnknown
{
public:
	DECLARE_COUNT(KEtCustomVerInfo)
	DECLARE_CLASS(KEtCustomVerInfo)
	DECLARE_NO_QUERYINTERFACE()

	void reset(bool hasRecalculate) 
	{
		m_hasRecalculate = hasRecalculate;
	}
	
	bool getHasRecalculate() { return m_hasRecalculate; }
private:
	bool m_hasRecalculate;
};

namespace wo
{
struct IEtRevisionContext;

/**
 * 计算引擎每次运算完毕，收集引用区域脏的迷你图和迷你图组合
*/
class KSparklineRefAreaVersion
{
public:
	class HostSet
	{
		public:
			SPARKLINE_SET m_SparklineSet;
			SPARKLINE_GROUP_SET m_SparklineGroupSet;
			
			bool empty()
			{
				if (m_SparklineSet.empty() && m_SparklineGroupSet.empty())
					return true;
				else
					return false;
			}
	};

	class DirtyRefAreas : public std::map<IWorksheetObj*, HostSet>
	{
		public:
			HostSet* GetHostSet(IWorksheetObj* pSheetObj, bool bCreate = false);
	};

	KSparklineRefAreaVersion(IKWorkbook*);
	DirtyRefAreas& GetDirtySparklineRefAreas();
	bool HasDirtyRefAreas();

	void AddDirtySparklines(IWorksheetObj*, SPARKLINE_SET&);
	void AddDirtySparklineGroups(IWorksheetObj*, SPARKLINE_GROUP_SET&);
	
private:
	/**
	 * key: sheet obj id.  
	 * value: 引用区域脏的迷你图和迷你图组合.
	 * */
	DirtyRefAreas m_DirtySparklineRefAreas; 
	IKWorkbook* m_wb;
}; 
class KEtVersion: public KwVersion
{
public:
	KEtVersion(UINT32, KwTask*, IKWorkbook*, KwVersion::BanUndoType);
	const UNDO_TAG* getUndoTag();
	bool getHasRecalculate() { return m_hasRecalculate; }

protected:
	virtual void onConfim(
		WebInt dataIndex, WebInt taskIndex, Transact* trans) override;
	void setCustomVerInfo(IUnknown* pCustomVerInfo) override;
protected:
	bool m_hasRecalculate;
	
	IBookStake* m_bks;
	const UNDO_TAG* m_undoTag;
};


class KEtVersionManager: public KwVersionManager
{
	typedef KwVersionManager base_type;

public:
	KEtVersionManager(RepertoryProxy* rep, IKWorkbook*, WebLogFunc);
	virtual ~KEtVersionManager();

	virtual KwVersion* createVersion(
		UINT32 userInnerID, KwTask* task, KwVersion::BanUndoType banUndo) override;

	void markClearVersionData(bool clear);
	void markClearCurVersionCmdData(bool clear);
	void clearUnusedVersionData();  

	void confirmSparklineRefAreaVersion(KSparklineRefAreaVersion*);
	KSparklineRefAreaVersion* createSparklineRefAreaVersion();
	KSparklineRefAreaVersion* getCurSparklineRefAreaVersion();
	KSparklineRefAreaVersion* getSparklineRefAreaVersion(WebInt);
	WebInt getCurSparklineRefAreaVersionID();
	WebInt getSparklineRefAreaVersionCount();
	int64_t GetMemoryComplexity();

protected:
	IKWorkbook* m_wb;
	int m_maxUndoStep;
	bool m_needClearVersions;
	bool m_needClearCurVersionCmd;
	WebInt m_unusedVersionIndex;

private:
    void clearAfterMaxUndoSteps();
	std::vector<KSparklineRefAreaVersion*> m_sparklineRefAreaVersions;
};

}

#endif //__WEBET_REVISION_EXT_H__
