﻿#ifndef __WEBET_DATABASE_TASK_CLASS_H__
#define __WEBET_DATABASE_TASK_CLASS_H__

#include "et_task_class.h"
#include "database_def.h"

namespace wo
{

class TaskExecDbBase : public EtTaskExecBase
{
protected:
    using EtTaskExecBase::EtTaskExecBase;
    HRESULT Respond(Database::FieldContext *,
                    KEtRevisionContext *,
                    Database::FieldType,
                    bool,
                    const RANGE &);

	HRESULT Respond(Database::FieldContext *pFieldContext,
                    KEtRevisionContext *pRevisionContext,
                    Database::FieldType fieldType,
                    bool bClear,
                    std::vector<RANGE> &rgVec);
};

class TaskExecDbAddCol : public TaskExecDbBase
{
public:
    TaskExecDbAddCol(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
    virtual HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbEditCol : public TaskExecDbBase
{
public:
    TaskExecDbEditCol(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
    virtual HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbAddRow : public TaskExecDbBase
{
public:
    TaskExecDbAddRow(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
    virtual HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbSetField : public TaskExecDbBase
{
public:
    TaskExecDbSetField(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
    virtual HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
private:
    HRESULT AdjustRange(RANGE &);
    void serialFieldExtData(VarObj, Database::IDbField*);
    bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
    {
        return false;
    }
};

class TaskExecDbFillValues : public TaskExecDbBase
{
public:
    TaskExecDbFillValues(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
    virtual HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

} // wo

#endif // __WEBET_DATABASE_TASK_CLASS_H__