﻿#ifndef __WEBET_FILTER_CONTEXT_IMP_H__
#define __WEBET_FILTER_CONTEXT_IMP_H__

#include "etstdafx.h"
#include "wo/et_revision_context.h"

namespace wo
{

interface IBookSetting;
class WoFilterContext : public IWoFilterContext
{
public:
    WoFilterContext(IBook *, IEtRevisionContext *);

    void SetType(WoFilterType) override;
    WoFilterType GetType() override;
    PCWSTR GetID() override;
    PCWSTR GetRawID() override;
    bool IsShared() override;
    bool GetIsFilterReadOnly() override;
    void SetIsFilterReadOnly(bool readOnly) override;

  private:

    IBookSetting *m_pBookSetting;
    IEtRevisionContext *m_pRevisionContext;
    bool m_filterContextReadOnly;
};

} // wo

#endif // __WEBET_FILTER_CONTEXT_IMP_H__