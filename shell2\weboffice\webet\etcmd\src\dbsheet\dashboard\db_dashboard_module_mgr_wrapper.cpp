﻿#include "etstdafx.h"
#include "workbook.h"
#include "util.h"
#include "db_dashboard_module_mgr_wrapper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include <public_header/drawing/model/abstract_shape.h>
#include "db_chart_wrapper.h"
#include "src/dbsheet/et_dbsheet_dashboard_utils.h"
#include "dbsheet/dashboard/db_richtext_wrapper.h"
#include "dbsheet/dashboard/db_plugin_wrapper.h"
#include "dbsheet/dashboard/db_chart_wrapper.h"
#include "dbsheet/dashboard/db_view_wrapper.h"
#include "webbase/wo_sa_helper.h"
#include "webextension/webextension_i.h"
#include "src/dbsheet/et_dbsheet_filter_helper.h"

namespace
{

std::unordered_set<size_t> GetFirstNModulesByLayout(const std::vector<std::unique_ptr<wo::KDbDashboardModuleWrapper>>& modules, size_t num)
{
    size_t moduleCount = modules.size();
    if (moduleCount <= num)
    {
        std::unordered_set<size_t> result;
        for (size_t i = 0 ; i < moduleCount; ++i)
            result.insert(i);
        return result;
    }
    std::vector<std::pair<POINT, size_t>> modulePositions;
    for (size_t i = 0; i < moduleCount; ++i)
    {
        const auto& spModule = modules[i];
        POINT position = spModule->GetPosition();
        modulePositions.emplace_back(position, i);
    }
    std::sort(modulePositions.begin(), modulePositions.end(), [](
            const std::pair<POINT, size_t>& pair1,
            const std::pair<POINT, size_t>& pair2) {
        const POINT& position1 = pair1.first;
        const POINT& position2 = pair2.first;
        // position异常的始终在最后
        if (position1.x < 0 || position1.y < 0)
            return false;
        if (position2.x < 0 || position2.y < 0)
            return true;
        return position1.y == position2.y ? position1.x < position2.x : position1.y < position2.y;
    });
    modulePositions.resize(num);
    std::unordered_set<size_t> result;
    for (const auto& pair : modulePositions)
        result.insert(pair.second);
    return result;
}

}

namespace wo
{

KDbDashboardModuleMgrWrapper::KDbDashboardModuleMgrWrapper(KEtWorkbook* pEtWorkbook, etoldapi::_Worksheet* pWorksheet) :
        m_pEtWorkbook(pEtWorkbook), m_spWorksheet(pWorksheet)
{
    ASSERT(m_spWorksheet != nullptr);
    ISheet* pSheet = m_spWorksheet->GetSheet();
    VS(DbSheet::GetDBDashBoardOp(pSheet, &m_spDbDashboard));
}

HRESULT KDbDashboardModuleMgrWrapper::EnumChart(const std::function<HRESULT(KDbChartWrapper*)>& func)
{
    IKWebExtensionMgr* pWebExtensionMgr = m_pEtWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    ASSERT(pWebExtensionMgr != nullptr);
    UINT webExtensionCount = 0;
    ISheet* pSheet = m_spWorksheet->GetSheet();
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    for (UINT i = 0; i < webExtensionCount; ++ i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        HRESULT hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            return hr;

        std::unique_ptr<KDbChartWrapper> pChartWrapper = GetChart(spWebExtension);
        if (!pChartWrapper)
            continue;

        hr = func(pChartWrapper.get());
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT KDbDashboardModuleMgrWrapper::EnumModule(const std::function<HRESULT(KDbDashboardModuleWrapper*)>& func)
{
    IKWebExtensionMgr* pWebExtensionMgr = m_pEtWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    ASSERT(pWebExtensionMgr != nullptr);
    UINT webExtensionCount = 0;
    ISheet* pSheet = m_spWorksheet->GetSheet();
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    for (UINT i = 0; i < webExtensionCount; ++ i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        HRESULT hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            return hr;

        std::unique_ptr<KDbDashboardModuleWrapper> pModuleWrapper = GetModule(spWebExtension);
        if (!pModuleWrapper)
            continue;

        hr = func(pModuleWrapper.get());
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

std::unique_ptr<KDbChartWrapper> KDbDashboardModuleMgrWrapper::GetChart(PCWSTR chartId)
{
    return GetChart(GetWebExtension(chartId));
}

std::unique_ptr<KDbDashboardPluginWrapper> KDbDashboardModuleMgrWrapper::GetPlugin(PCWSTR webExtensionKey)
{
	if(!m_spDbDashboard)
		return nullptr;

    IKWebExtension* pWebExtension = GetWebExtension(webExtensionKey);
    if (!pWebExtension || !pWebExtension->IsValid())
        return nullptr;

    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
    // todo 工作表暂不支持
	if (DbDashboard::IsEtDataSourceType(dataSourceType))
		return nullptr;

    if (dataSourceType != dst_dbTable)
        return nullptr;

    EtDbId moduleId = INV_EtDbId;
    HRESULT hr = DbDashboard::GetDbChartStatisticModuleId(pWebExtension, moduleId);
    if (FAILED(hr))
        return nullptr;
    ks_stdptr<IDBChartStatisticModule> spStatisticModule;
    hr = m_spDbDashboard->GetChartStatisticMgr()->GetItemById(moduleId, &spStatisticModule);
    if (FAILED(hr))
        return nullptr;
    return std::make_unique<KDbDashboardPluginWrapper>(m_pEtWorkbook, pWebExtension, spStatisticModule);
}

std::unique_ptr<KDbDashboardViewWrapper> KDbDashboardModuleMgrWrapper::GetView(PCWSTR webExtensionKey)
{
    IKWebExtension* pWebExtension = GetWebExtension(webExtensionKey);
    if (!pWebExtension || pWebExtension->GetWebShapeType() != WET_DbView)
        return nullptr;

    return std::make_unique<KDbDashboardViewWrapper>(m_pEtWorkbook, pWebExtension);
}

std::unique_ptr<KDbChartWrapper> KDbDashboardModuleMgrWrapper::GetChart(IKWebExtension* pWebExtension)
{
	if(!m_spDbDashboard)
		return nullptr;

    if (!pWebExtension || !pWebExtension->IsValid() || !pWebExtension->IsDatasourceSupportingDashboardModule())
        return nullptr;

    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
    if (DbDashboard::IsEtDataSourceType(dataSourceType))
    {
        EtDbId moduleId = INV_EtDbId;
        VS(_appcore_GainDbSheetContext()->DecodeEtDbId(pWebExtension->GetModuleId(), &moduleId));
        ks_stdptr<IDbWorksheetChart> spWorksheetChart;
        HRESULT hr = m_spDbDashboard->GetWorksheetChartMgr()->GetChart(moduleId, &spWorksheetChart);
        if (SUCCEEDED(hr))
            return std::make_unique<KDbChartWrapper>(m_pEtWorkbook, pWebExtension, spWorksheetChart);
    }
    else if (dataSourceType == dst_dbTable)
    {
        EtDbId moduleId = INV_EtDbId;
        HRESULT hr = DbDashboard::GetDbChartStatisticModuleId(pWebExtension, moduleId);
        if (FAILED(hr))
            return nullptr;

        ks_stdptr<IDBChartStatisticModule> spStatisticModule;
        hr = m_spDbDashboard->GetChartStatisticMgr()->GetItemById(moduleId, &spStatisticModule);
        if (SUCCEEDED(hr))
            return std::make_unique<KDbChartWrapper>(m_pEtWorkbook, pWebExtension, spStatisticModule);
    }
    return nullptr;
}

IKWebExtension* KDbDashboardModuleMgrWrapper::GetWebExtension(PCWSTR webExtensionKey)
{
    if (!webExtensionKey)
        return nullptr;

    IKWebExtensionMgr* pWebExtensionMgr = m_pEtWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    ASSERT(pWebExtensionMgr != nullptr);
    ISheet* pSheet = m_spWorksheet->GetSheet();
    IDX sheetIdx = INVALIDIDX;
    VS(pSheet->GetIndex(&sheetIdx));
    if (sheetIdx == INVALIDIDX)
        return nullptr;

    return pWebExtensionMgr->FindWebExtension(sheetIdx, webExtensionKey);
}

std::unique_ptr<KDbDashboardModuleWrapper> KDbDashboardModuleMgrWrapper::GetModule(PCWSTR webExtensionKey)
{
    return GetModule(GetWebExtension(webExtensionKey));
}

std::unique_ptr<KDbDashboardModuleWrapper> KDbDashboardModuleMgrWrapper::GetModule(IKWebExtension* pWebExtension)
{
    if (!pWebExtension)
        return nullptr;

    int webExtType = pWebExtension->GetWebShapeType();
    switch (webExtType)
    {
        case WET_DbDataSource:
        {
            return GetChart(pWebExtension);
        }
        case WET_DbRichText:
        {
            return std::make_unique<KDbRichTextWrapper>(m_pEtWorkbook, pWebExtension);
        }
        case WET_DbPlugin:
        {
            return GetPlugin(pWebExtension->GetWebExtensionKey());
        }
        case WET_DbView:
        {
            return GetView(pWebExtension->GetWebExtensionKey());
        }
        default:
            ASSERT(FALSE);
            break;
    }
    return {};
}

std::vector<std::unique_ptr<KDbDashboardModuleWrapper>> KDbDashboardModuleMgrWrapper::GetModules()
{
    std::vector<std::unique_ptr<KDbDashboardModuleWrapper>> result;
    IKWebExtensionMgr* pWebExtensionMgr = m_pEtWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    ASSERT(pWebExtensionMgr != nullptr);
    UINT webExtensionCount = 0;
    ISheet* pSheet = m_spWorksheet->GetSheet();
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    for (UINT i = 0; i < webExtensionCount; ++i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        HRESULT hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            continue;

        std::unique_ptr<KDbDashboardModuleWrapper> spModuleWrapper = GetModule(spWebExtension);
        if (spModuleWrapper)
            result.emplace_back(std::move(spModuleWrapper));
    }
    return result;
}

HRESULT KDbDashboardModuleMgrWrapper::SerializeServerRenderData(const binary_wo::VarObj& param, bool isMobile,
                                                                KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt)
{
    if (!param.has("lang"))
        return E_INVALID_REQUEST;

    binary_wo::VarObj lang = param.get_s("lang");
    std::vector<std::unique_ptr<KDbDashboardModuleWrapper>> modules = GetModules();
    std::unordered_set<size_t> firstNModules = GetFirstNModulesByLayout(modules, isMobile ? 5 : 30);
    sa::Leave modulesLeave(sa::enterArray(pAcpt, "modules"));
    for (size_t i = 0, count = modules.size(); i < count; ++i)
    {
        const auto& spModule = modules[i];
        sa::Leave leave(sa::enterStruct(pAcpt, nullptr));
        pAcpt->addString("webExtensionKey", spModule->GetWebExtensionKey());
        PCWSTR typeStr = __X("");
        VS(_webextension_GainEncodeDecoder()->EncodeWebExtType(spModule->GetWebExtType(), &typeStr));
        pAcpt->addString("webExtensionType", typeStr);
        {
            sa::Leave propertiesLeave(sa::enterStruct(pAcpt, "properties"));
            spModule->SerializeProperties(pAcpt);
        }
        pAcpt->addString("status", spModule->GetStatusStr());
        if (spModule->GetWebExtType() == WET_DbPlugin || spModule->GetWebExtType() == WET_DbRichText)
            continue;
        if (firstNModules.find(i) != firstNModules.cend())
        {
            sa::Leave propertiesLeave(sa::enterStruct(pAcpt, "content"));
            spModule->SerializeContent(param, lang, pCtx, pAcpt);
        }
    }
    return S_OK;
}

void KDbDashboardModuleMgrWrapper::SerializeFilter(IDbDashboardFilter* pFilter, ISerialAcceptor* pAcpt) const
{
    EtDbIdStr buf;
    VS(_appcore_GainDbSheetContext()->EncodeEtDbId(pFilter->GetId(), &buf));
    pAcpt->addString("id", buf);
    pAcpt->addString("name", pFilter->GetName());
    pAcpt->addBool("multiSheetFilter", alg::BOOL2bool(pFilter->GetMultiSheetFilter()));
    class KDbDashboardFilterFieldEnum : public IDbDashboardFilterFieldEnum
    {
    public:
        explicit KDbDashboardFilterFieldEnum(ISerialAcceptor* pAcpt) : m_pAcpt(pAcpt) {}
        STDPROC Do(UINT sheetId, EtDbId fieldId) override
        {
            sa::Leave leave(sa::enterStruct(m_pAcpt, nullptr));
            EtDbIdStr buf;
            VS(_appcore_GainDbSheetContext()->EncodeEtDbId(fieldId, &buf));
            m_pAcpt->addString("fieldId", buf);
            m_pAcpt->addUint32("sheetId", sheetId);
            return S_OK;
        }
    private:
        ISerialAcceptor* m_pAcpt;
    };

    {
        sa::Leave filterFieldsleave(sa::enterArray(pAcpt, "filterFields"));
        KDbDashboardFilterFieldEnum filterFieldEnum(pAcpt);
        pFilter->EnumFilterField(&filterFieldEnum);
    }
}

void KDbDashboardModuleMgrWrapper::SerializeFilters(const char* keyName, ISerialAcceptor* pAcpt) const
{
	if(!m_spDbDashboard)
		return;

    sa::Leave filtersLeave(sa::enterArray(pAcpt, keyName));
    IDbDashboardFilterMgr* pFilterMgr = m_spDbDashboard->GetFilterMgr();
    for (UINT i = 0, count = pFilterMgr->GetCount(); i < count ; ++i)
    {
        ks_stdptr<IDbDashboardFilter> spFilter;
        VS(pFilterMgr->GetFilterAt(i, &spFilter));
        sa::Leave leave(sa::enterStruct(pAcpt, nullptr));
        SerializeFilter(spFilter, pAcpt);
    }
}

bool KDbDashboardModuleMgrWrapper::IsValidFilterName(PCWSTR name)
{
    if (!name)
        return false;

    static constexpr int kMaxFilterNameLen = 31;
    size_t len = ks_wcsnlen_s(name, kMaxFilterNameLen + 1);
    return len <= kMaxFilterNameLen;
}

HRESULT KDbDashboardModuleMgrWrapper::CheckCanAddPlugin()
{
    UINT16 dbDashboardPluginMax = _kso_GetWoEtSettings()->GetDbDashboardPluginMax();
    HRESULT hr = S_OK;
    IKWebExtensionMgr* pWebExtensionMgr = m_pEtWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return E_FAIL;

    ISheet* pSheet = m_spWorksheet->GetSheet();
    UINT webExtensionCount = 0;
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    UINT pluginCnt = 0;
    for (UINT i = 0; i < webExtensionCount; ++i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            return hr;

        if (spWebExtension->GetWebShapeType() == WET_DbPlugin && ++pluginCnt >= dbDashboardPluginMax)
            return E_DBSHEET_DASHBOARD_PLUGIN_COUNT_LIMIT;
    }
    return S_OK;
}

HRESULT KDbDashboardModuleMgrWrapper::CheckCanAddView()
{
    UINT16 dbDashboardViewMax = _kso_GetWoEtSettings()->GetDbDashboardViewMax();
    HRESULT hr = S_OK;
    IKWebExtensionMgr* pWebExtensionMgr = m_pEtWorkbook->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return E_FAIL;

    ISheet* pSheet = m_spWorksheet->GetSheet();
    UINT webExtensionCount = 0;
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    UINT viewCnt = 0;
    for (UINT i = 0; i < webExtensionCount; ++i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            return hr;

        if (spWebExtension->GetWebShapeType() == WET_DbView && ++viewCnt >= dbDashboardViewMax)
            return E_DBSHEET_DASHBOARD_VIEW_COUNT_LIMIT;
    }
    return S_OK;
}

HRESULT KDbDashboardModuleMgrWrapper::DeleteAllDbPlugin()
{
    ks_stdptr<IDBChartStatisticMgr> spDbChartStatisticMgr;
    DbSheet::GetDBChartStatisticMgr(m_spWorksheet->GetSheet(), &spDbChartStatisticMgr);
    if (!spDbChartStatisticMgr)
        return E_FAIL;

    ks_stdptr<etoldapi::Shapes> spShapes;
    m_spWorksheet->get_Shapes(FALSE, &spShapes);
    if (!spShapes)
        return E_FAIL;

    int shapeCount = 0;
    HRESULT hr = spShapes->get_Count(&shapeCount);
    if (FAILED(hr))
        return hr;
    for (int i = shapeCount; i > 0 ; --i)
    {
        ks_stdptr<oldapi::KsoShape> spKsoShape;
        spShapes->_Item(i, &spKsoShape);
        if (!spKsoShape)
            continue;

        ks_stdptr<IKsoShapeEx> spShapeEx = spKsoShape;
        if (!spShapeEx)
            continue;

        ks_stdptr<IKShape> spCoreShape;
        spShapeEx->GetInterface(IID_IKShape, (void**)&spCoreShape);
        drawing::AbstractShape* pAbsShape = static_cast<drawing::AbstractShape*>(spCoreShape.get());
        if (!pAbsShape || !pAbsShape->isWebExtensionHostShape())
            continue;

        IKWebExtension* pWebExtension = pAbsShape->getWebExtension();
        if (!pWebExtension || pWebExtension->GetWebShapeType() != WET_DbPlugin)
            continue;

        hr = spKsoShape->Delete();
        if (FAILED(hr))
            return hr;

        EtDbId moduleId = INV_EtDbId;
        hr = DbDashboard::GetDbChartStatisticModuleId(pWebExtension, moduleId);
        if (FAILED(hr))
            return hr;
        hr = spDbChartStatisticMgr->DelItem(moduleId);
        if (FAILED(hr))
            return hr;

        hr = pWebExtension->Delete(TRUE, FALSE);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

}
