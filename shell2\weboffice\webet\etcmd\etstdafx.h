﻿#ifndef __ETSTDAFX_H__
#define __ETSTDAFX_H__

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

#include <QtCore>
#include <QtGui>

#include <algorithm>
#include <bitset>
#include <functional>
#include <memory>
#include <array>
#include <vector>
#include <sstream>
#include <string>
#include <cctype>
#include <cmath>
#include <unordered_map>
#include <unordered_set>

#ifndef Q_OS_WIN
#include <chrono>
#include <ratio>
#endif

#include <krt/krt.h>

#define KFC_AtlCompliant

#include "etx_kfc.h"
#include <kso/api/ksoapi_old.h>
#include <public_header/security/corex.h>
#include <kso/api/chartapi_old.h>
using namespace etoldapi;
using namespace oldapi;

#include "etx_numfmt.h"
#include "etx_exec.h"
#define CPL_IMPORT_ALL
#include "etx_compiler.h"
#include "etx_core.h"
#include <etbase/etbase.h>
#include "kso/io/filterplugin.h"
#include "etx_event.h"
#include "etx_appcore.h"
#include "etx_corenotify.h"
#include "etx_applogic.h"
#include "applogic/etapi_old_enum.h"
#include <applogic/etapi_old.h>
#include "etx_persist.h"
#include "etx_render.h"
#include "etx_funclib.h"
#include "etx_shell.h"
#include "etx_result.h"
#include "etx_text.h"
#include "etx_opl.h"
#include "etx_chart.h"
#include <webbase/serialize.h>
#include <kso/pdf/pdfinterface.h>
#endif
