﻿#ifndef __WEBET_DATA_ANALYZE_HELPER_H__
#define __WEBET_DATA_ANALYZE_HELPER_H__
namespace wo
{
    struct KNotEmptyColsSet
    {
    public:
        void SetNotEmptyCol(int col);
        bool IsSkipCol(int col);
        bool HasNotEmptyCol();
        void SetColFrom(int fromColIdx);
    private:
        std::vector<bool> m_notEmptyCols;
        int m_fromColIdx = 0;
        int m_lastNotEmptyCol = 0;
        int m_firstNotEmptyCol = INT_MAX;
    };

    class EmptyColAcceptor : public ICellValueAcpt
    {
    public:
        EmptyColAcceptor() = default;
        STDIMP_(BOOL) Do(ROW row, COL col, const_token_ptr pToken);
        KNotEmptyColsSet& getNotEmptyCols();
    private:
        KNotEmptyColsSet m_notEmptyColsSet;
    };

    QString GetColNamePrefix(const QString& bookName, const QString& sheetName);
    
    QString GetAutoCompletColNameForEmptyCol(int sourceIndex, int emptyColIdx, const QString& tableText);

    etda::MergeType ParseMergeDirection(PCWSTR direction);

    etda::JoinMode ParseJoinModeStr(PCWSTR joinMode);

    PCWSTR ParseJoinModeEnum(etda::JoinMode joinMode);

    void processMatrixColName(std::vector<ks_wstring>& headerNameWithAutoCompleteEmptyVec, const int sourceIndex, std::vector<std::vector<ks_wstring>>& matrix, KNotEmptyColsSet& notEmptyColsSet, PCWSTR tableText);
    
    void exportRangeData(KNotEmptyColsSet& notEmptyColsSet,const std::vector<std::vector<ks_wstring>>& matrix, const std::vector<ks_wstring>& headerNameWithAutoCompleteEmptyVec, QSet<int>& hideColsList,KSerialWrapBinWriter& acpt);
}


#endif
