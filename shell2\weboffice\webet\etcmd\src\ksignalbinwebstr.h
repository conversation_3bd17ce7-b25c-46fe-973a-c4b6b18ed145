﻿#ifndef __SHELL2_WEBET_KSIGNAL_BINWEBSTR_H_
#define __SHELL2_WEBET_KSIGNAL_BINWEBSTR_H_

namespace wo {
    
struct BinWebStr
{
    const WCHAR * m_wszArray = nullptr;
    uint32 m_len = 0;
    
    void reset() { m_wszArray = nullptr; m_len = 0;}
};

struct BinWebStrHash
{
    std::size_t operator()(const BinWebStr & s) const noexcept 
    {
        std::size_t hx = 0x123;
        for (int i = 0; i < s.m_len; ++i)
        {
            hx *= 16777619;
            hx ^= (std::size_t)((unsigned short)(s.m_wszArray[i]));
        }
        return hx;
    }
};

struct BinWebStrEqual
{
    bool operator()(const BinWebStr & ls, const BinWebStr & rs) const
    {
        if (ls.m_len != rs.m_len) return false;
        return xstrncmp(ls.m_wszArray, rs.m_wszArray, ls.m_len) == 0;
    }
};
}

#endif //__SHELL2_WEBET_KSIGNAL_BINWEBSTR_H_
