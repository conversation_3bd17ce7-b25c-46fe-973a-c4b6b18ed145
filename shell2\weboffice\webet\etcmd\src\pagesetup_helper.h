/**
 * <AUTHOR>
 * @description 修改页面设置。析构的时候，会还原页面设置。
*/

#ifndef __PAGE_SETUP_HELPER_H__
#define __PAGE_SETUP_HELPER_H__

#include "workbook.h"

void initPageSetup(PAGESETUP& pageSetup);
bool HasContentToPrint(IKWorkbook* pWb, IKWorksheet* pWs);
void MarkPaginateDirty(IKWorkbook* pWb, IKWorksheet* pWs);
int GetPageCount(IKWorkbook* pWb, IKWorksheet* pWs);
enm_print_order DecodeOrder(int32 order);
enm_page_orientation DecodeOrientation(int32 orientation);

class TmpPageSetupDataHelper
{
public:
	TmpPageSetupDataHelper(wo::KEtWorkbook* pWb);
	~TmpPageSetupDataHelper();

	/**
	 * @param cmdParam 收到请求的参数 
	 * @param pWs 指定页面设置的工作表。如果是NULL，则整个工作簿
	 * @param skipOrNot 自定义函数。用于指定哪些类型的工作表不修改页面设置
	 * @return 成功返回WO_OK
	*/
	WebInt RevisePageSetups(const binary_wo::VarObj& cmdParam, IKWorksheet* pWs, std::function<bool (ISheet*)> const &skipOrNot);
private:
	// 修改pSheet的页面设置，并备份旧的页面设置。
	void RevisePageSetup(ISheet* pSheet, const LPPAGESETUP pData);
	void GetPageSetupData(binary_wo::VarObj var, PAGESETUP& pageSetup);

private:
	std::vector<std::pair<IPageSetupData*, PAGESETUP>> m_oldPageSetups;
	wo::KEtWorkbook* 	m_pWorkbook;
};

#endif
