﻿#include "db_gridsheet_sync_helper.h"
#include "appcore/et_appcore_dbsheet.h"
#include "database/database_field_context.h"
#include "etcore/et_core_basic.h"
#include "wo/et_shared_str.h"
#include "database/database_def.h"
#include "helpers/table_struct_rec_helper.h"
#include "utils/et_gridsheet_utils.h"
#include "helpers/db_field_copy_helper.h"
#include "util.h"
#include "et_dbsheet_utils.h"

namespace wo
{
GridsheetSyncHelper::GridsheetSyncHelper(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet)
	: m_pSrcWorkSheet(pSrcWorkSheet)
	, m_pTarWorkSheet(pTarWorkSheet)
	, m_pSrcSheet(m_pSrcWorkSheet->GetSheet())
	, m_pTarSheet(m_pTarWorkSheet->GetSheet())
	, m_pSrcBook(m_pSrcSheet->LeakBook())
	, m_pTarBook(m_pTarSheet->LeakBook())
{
	VS(DbSheet::GetDBSheetOp(m_pTarSheet, &m_spTarDbSheetOp));
}
// TODO(zhangpeng27): 重复代码，待整理
void GridsheetSyncHelper::encodeToBase64(QString& str)
{
	const QRegularExpression reBase64("{base64:.*?}");
	constexpr int leftPos = 8;
	constexpr int holderLen = 9;
	QRegularExpressionMatchIterator i = reBase64.globalMatch(str);
	while (i.hasNext())
	{
		QRegularExpressionMatch match = i.next();
		QString originStr = match.captured();
		QString posStr = originStr.mid(leftPos, originStr.size() - holderLen);
		QByteArray byte = posStr.toUtf8().toBase64();
		str.replace(originStr, QString(byte.data()));
	}
}
// TODO(zhangpeng27): 重复代码，待整理
EtDbId GridsheetSyncHelper::decodeRecordId(PCWSTR str)
{
	QString urlStr = krt::fromUtf16(str);
	int idx = urlStr.indexOf("?R=");
	if (idx == -1)
		return INV_EtDbId;
	QString posInfo = urlStr.mid(idx + 3);
	QByteArray byte = QByteArray::fromBase64(posInfo.toUtf8());
	QString decodedStr(byte.data());
	idx = decodedStr.lastIndexOf("/");
	if (idx == -1)
		return INV_EtDbId;
	QString idStr = decodedStr.mid(idx + 1);
	EtDbId id = INV_EtDbId;
	_appcore_GainDbSheetContext()->DecodeEtDbId(krt::utf16(idStr), &id);
	return id;
}

HRESULT GridsheetSyncHelper::Init(const GridsheetSyncParam& stSyncParam)
{
	if (!stSyncParam.urlTemplate || !stSyncParam.fldSourceName || !stSyncParam.m_pCtx)
		return E_INVALIDARG;
	m_param = stSyncParam;
	return S_OK;
}

HRESULT GridsheetSyncHelper::ExecCopy()
{
    struct CustomCtxController
    {
        CustomCtxController(IBook* pBook1, IBook* pBook2)
        {
            ASSERT(pBook1 && pBook2);
            pBook1->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&m_spDbBookCtx1);
            if (pBook1 != pBook2)
                VS(pBook2->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&m_spDbBookCtx2));
            if (m_spDbBookCtx1)
                m_spDbBookCtx1->GetViewCustomCtx()->SetCurCommon();
            if (m_spDbBookCtx2)
                m_spDbBookCtx2->GetViewCustomCtx()->SetCurCommon();
        }
        ~CustomCtxController()
        {
            if (m_spDbBookCtx1)
                m_spDbBookCtx1->GetViewCustomCtx()->SetCurAuto();
            if (m_spDbBookCtx2)
                m_spDbBookCtx2->GetViewCustomCtx()->SetCurAuto();
        }
        ks_stdptr<IDbtBookCtx> m_spDbBookCtx1;
        ks_stdptr<IDbtBookCtx> m_spDbBookCtx2;
    };
	CustomCtxController customCtxController(m_pSrcBook, m_pTarBook);
	// Et的dbsheet结构已经创建好了，这里直接读取就行了
    m_pSrcSheet->GetExtDataItem(edSheetEt2DbSyncData, (IUnknown**)&m_spEt2DbSyncData);
	if (!m_spEt2DbSyncData)
		return E_DBSHEET_SYNC_GRIDSHEET_DATA_INVALID;
	wo::ISheetStake* pSheetStake = m_pSrcSheet->GetWoStake();
	m_spEtIdContainer = pSheetStake->GetEtIdContainer();
    if (!m_spEtIdContainer)
		return E_DBSHEET_SYNC_GRIDSHEET_ID_CONTAINER_INVALID;
	RANGE rg(m_pSrcSheet->GetBMP());
	HRESULT hr = calcSourceRange(rg);
	if (FAILED(hr))
		return hr;
    hr = doExecCopy(rg);
    if (FAILED(hr))
		return hr;
	hr = reAdjustIds();
    if (FAILED(hr))
		return hr;
	return setInfoField();
}

HRESULT GridsheetSyncHelper::doExecCopy(const RANGE& rg)
{
    ks_stdptr<IDBSheetRange> spDbRange;
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	const IDBIds* pTarFieldIds = m_spTarDbSheetOp->GetAllFields();
	EtDbIdx tarFieldCnt = pTarFieldIds->Count();
	const IDBIds* pTarRecIds = m_spTarDbSheetOp->GetAllRecords();
	EtDbIdx tarRecCnt = pTarRecIds->Count();
	int primaryCol = rg.ColFrom();
	// 先计算出有效的区域，再进行调整
	m_spEt2DbSyncData->CalcSkipRanges();
	HRESULT hr = adjustFields(rg);
	if (FAILED(hr))
		return hr;
	hr = adjustRecords(rg);
	if (FAILED(hr))
		return hr;
	const IDBIds* pNewFieldIds = m_spTarDbSheetOp->GetAllFields();
	m_stValidSrcFieldIds.clear();
	m_stValidSrcRecordIds.clear();
	etoldapi::_Workbook* pEtWorkbook = static_cast<etoldapi::_Workbook*>(m_pSrcWorkSheet->GetWorkbook());
	Et2DbFieldCopyHelper stFieldCopyHelper(pEtWorkbook, m_pSrcWorkSheet, m_pTarWorkSheet, m_param.m_pCtx, rg);
	stFieldCopyHelper.Init(m_param.m_pcwFilePath, false, m_param.m_pParam);
	// 推导类型的时候要去掉表头区域
    stFieldCopyHelper.SetIterRowsByRange(rg.RowFrom() + 1, rg.RowTo());
	wo::util::CallTimeStat callTime("GridsheetSyncHelper::CopySheetData");
	for (COL col = rg.ColFrom(); col <= rg.ColTo(); ++col)
    {
		if (m_spEt2DbSyncData->IsSkipCol(col))
			continue;
		EtDbId srcFldId = m_spEtIdContainer->ColumnIdAt(col);
		if (srcFldId == INV_EtDbId)
			continue;
        EtDbId fldId = m_fldMap[srcFldId];
        ks_stdptr<IDbField> spField;
        pTarFieldsMgr->GetField(fldId, &spField);
        if(!spField)
            continue;
		ks_stdptr<IEtFieldItem> spFieldItem;
        m_spEt2DbSyncData->GetFieldItem(srcFldId, &spFieldItem);
		if (!spFieldItem)
			continue;
		stFieldCopyHelper.BeginDeduceFieldType();
        for (;stFieldCopyHelper.Valid();stFieldCopyHelper.Next())
        {
            ROW row = stFieldCopyHelper.GetCurRow();
			if (m_spEt2DbSyncData->IsSkipCell(row, col))
				continue;
            EtDbId srcRecId = m_spEtIdContainer->RowIdAt(row);
			if (INV_EtDbId == srcRecId)
				continue;
            hr = stFieldCopyHelper.DeduceFieldType(col);
            if (hr == S_OK)
                break;
        }
        stFieldCopyHelper.EndDeduceFieldType();
		ET_DbSheet_FieldType fldType = ET_DbSheet_FieldType_Invalid;
		PCWSTR numFmt = nullptr;
		stFieldCopyHelper.GetTypeInfo(fldType, numFmt);
		if (col == primaryCol)
			stFieldCopyHelper.CorrectionForPrimaryField(fldType, numFmt);
		spFieldItem->SetFieldType(fldType);
		spField->SetTypeForIO(fldType);
		if (numFmt)
			spField->SetNumberFormat(numFmt);
		stFieldCopyHelper.CopyFieldExDetail(spField, col, true, true, true);
		spField->SetSyncField(TRUE);
		stFieldCopyHelper.SetFieldName(spField, rg.RowFrom(), col, m_param.m_pcwDefaultName, m_spEt2DbSyncData->IsSkipCell(rg.RowFrom(), col));
		m_stValidSrcFieldIds.insert(srcFldId);
		bool bIsAttachmentType = fldType == Et_DbSheetField_Attachment;
		if (bIsAttachmentType && !m_param.m_bEnableCopyAttachment)
			continue;
		stFieldCopyHelper.BeginCopyData(bIsAttachmentType, col);
        // 设值
        for (; stFieldCopyHelper.Valid(); stFieldCopyHelper.Next())
        {
			ROW row = stFieldCopyHelper.GetCurRow();
			if (m_spEt2DbSyncData->IsSkipCell(row, col))
				continue;
			EtDbId srcRecId = m_spEtIdContainer->RowIdAt(row);
			if (INV_EtDbId == srcRecId)
				continue;
			auto recIt = m_recMap.find(srcRecId);
			if (recIt == m_recMap.cend())
				continue;
			EtDbId recId = recIt->second;
			m_stValidSrcRecordIds.insert(srcRecId);
			stFieldCopyHelper.CopyCellData(spField, recId, fldId, col, bIsAttachmentType);
        }
        stFieldCopyHelper.EndCopyData(fldId);
	}
    return S_OK;
}

HRESULT GridsheetSyncHelper::reAdjustIds()
{
	wo::util::CallTimeStat callTime("GridsheetSyncHelper::reAdjustIds");
	ks_stdptr<IDBSheetRange> spRemoveFieldRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveFieldRange);
	spRemoveFieldRange->SetRecordIds(m_spTarDbSheetOp->GetAllRecords());
	std::unordered_set<EtDbId> invalidSrcFieldId;
	HRESULT hr = S_OK;
	for (const auto& idPair : m_fldMap)
	{
		auto iter = m_stValidSrcFieldIds.find(idPair.first);
		if (iter == m_stValidSrcFieldIds.end())
		{
			invalidSrcFieldId.insert(idPair.first);
			spRemoveFieldRange->AddFieldId(idPair.second);
		}
	}
	if (spRemoveFieldRange->GetFieldCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveFields(spRemoveFieldRange);
		if (FAILED(hr))
			return hr;
		for (const auto& id : invalidSrcFieldId)
			m_fldMap.erase(id);
	}
	std::unordered_set<EtDbId> invalidSrcRecordId;
	ks_stdptr<IDBSheetRange> spRemoveRecordRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRecordRange);
	spRemoveRecordRange->SetFieldIds(m_spTarDbSheetOp->GetAllFields());
	for (const auto& idPair : m_recMap)
	{
		auto iter = m_stValidSrcRecordIds.find(idPair.first);
		if (iter == m_stValidSrcRecordIds.end())
		{
			invalidSrcRecordId.insert(idPair.first);
			spRemoveRecordRange->AddRecordId(idPair.second);
		}
	}
	if (spRemoveRecordRange->GetRecordCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveRecords(spRemoveRecordRange);
		if (FAILED(hr))
			return hr;
		for (const auto& id : invalidSrcRecordId)
			m_recMap.erase(id);
	}
	return S_OK;
}

HRESULT GridsheetSyncHelper::calcSourceRange(RANGE& rg)
{
	wo::util::CallTimeStat callTime("GridsheetSyncHelper::calcSourceRange");
	int iHeaderRow = m_spEtIdContainer->GetHeaderIndex();
	if (iHeaderRow < 0)
		return E_DBSHEET_SYNC_GRIDSHEET_HEADER_ROW_INVALID;
	RECT usedRect{0};
	HRESULT hr = m_pSrcSheet->CalcUsedScale(&usedRect);
    if (FAILED(hr))
        return hr;
	IDX sheetIdx = 0;
	m_pSrcSheet->GetIndex(&sheetIdx);
	rg.SetSheetFromTo(sheetIdx);
	if (usedRect.top != iHeaderRow)
		usedRect.top = iHeaderRow;
	int iRowCnt = m_spEtIdContainer->GetRowCount();
    int iColumnCnt = m_spEtIdContainer->GetColumnCount();
	if (0 == iColumnCnt || 0 == iRowCnt)
		return E_DBSHEET_SYNC_GRIDSHEET_ID_CONTAINER_EMPTY;
	usedRect.bottom = iRowCnt - 1;
	usedRect.right = iColumnCnt - 1;
	ASSERT(usedRect.top <= usedRect.bottom);
	ASSERT(usedRect.left <= usedRect.right);
	rg.SetRowFromTo(usedRect.top, usedRect.bottom);
	rg.SetColFromTo(usedRect.left, usedRect.right);
	et_sdptr<ISheetEnum> spSheetEnum;
    m_pSrcSheet->CreateEnum(&spSheetEnum);
	ROW bottomRow = spSheetEnum->GetLastNonEmptyRow(usedRect);
    if (INVALIDIDX != bottomRow && bottomRow < usedRect.bottom)
	{
		usedRect.bottom = bottomRow;
		rg.SetRowTo(bottomRow);
	}
	COL leftCol = spSheetEnum->GetFirstNonEmptyCol(usedRect);
    if (INVALIDIDX != leftCol && leftCol > usedRect.left)
	{
		usedRect.left = leftCol;
		rg.SetColFrom(leftCol);
	}

    COL rightCol = spSheetEnum->GetLastNonEmptyCol(usedRect);
    if (INVALIDIDX != rightCol && rightCol < rg.ColTo())
	{
		usedRect.right = rightCol;
        rg.SetColTo(rightCol);
	}
	return S_OK;
}

HRESULT GridsheetSyncHelper::setInfoField()
{
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	m_fldSourceId = pTarFieldsMgr->GetSyncFieldSourceId();
    HRESULT hr = S_OK;
	if (m_fldSourceId == INV_EtDbId)
	{
		ks_stdptr<IDBSheetRange> spDbSourceRange;
		hr = m_spTarDbSheetOp->InsertFields(1, &spDbSourceRange);
        if (FAILED(hr))
		    return hr;
		m_fldSourceId = spDbSourceRange->GetFieldId(0);
		pTarFieldsMgr->SetSyncFieldSourceIdForIO(m_fldSourceId);
	}
	IDBSheetCtx* pCtx = _appcore_GainDbSheetContext();
	ks_stdptr<IDbField> spInfoField;
	hr = pTarFieldsMgr->GetField(m_fldSourceId, &spInfoField);
	if (FAILED(hr))
		return hr;
	VS(spInfoField->SetTypeForIO(Et_DbSheetField_Url));
	VS(spInfoField->SetSyncFieldForIO(TRUE));
	hr = spInfoField->SetName(m_param.fldSourceName, TRUE, TRUE);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField_Hyperlink> spInfoUrl = spInfoField;
	spInfoUrl->SetDisplayText(m_param.fldSourceName);

	QString templateStr = krt::fromUtf16(m_param.urlTemplate);
	templateStr.replace(QRegularExpression(":sheetStId"), QString::number(m_pSrcSheet->GetStId()));
	const QRegularExpression reRecId(":recordId");
	for (const auto& itRec : m_recMap)
	{
        EtDbIdStr buf;
		VS(pCtx->EncodeEtDbId(itRec.first, &buf));
		QString info = templateStr;
		info.replace(reRecId, krt::fromUtf16(buf));
		encodeToBase64(info);
		PCWSTR valueStr = krt::utf16(info);
		alg::managed_token_assist mta;
		hr = pCtx->Text2DbHyperlinkToken(valueStr, valueStr, &mta);
		if (FAILED(hr))
			return hr;
		hr = m_spTarDbSheetOp->SetTokenValue(itRec.second, m_fldSourceId, mta);
		if (FAILED(hr))
			return hr;
	}
	return setInfoFieldHidden();
}

HRESULT GridsheetSyncHelper::adjustFields(const RANGE& rg)
{
	wo::util::CallTimeStat callTime("GridsheetSyncHelper::adjustFields");
	HRESULT hr = S_OK;
	std::unordered_map<EtDbId, EtDbId> oldFldMap;
	if (m_param.pOldFieldMap)
		oldFldMap = std::move(*m_param.pOldFieldMap);
	auto itEnd = oldFldMap.end();
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	const IDBIds* pTarIds = m_spTarDbSheetOp->GetAllFields();
	EtDbIdx fldCnt = rg.Width();
	std::unordered_set<EtDbId> holdFldId;
	holdFldId.reserve(fldCnt + 1);
	std::vector<EtDbId> insertFldId;
	insertFldId.reserve(fldCnt);
	EtDbId oldPrimaryFldId = pTarFieldsMgr->GetPrimaryField();
	EtDbId newPrimaryFldId = m_spEtIdContainer->ColumnIdAt(rg.ColFrom());
	m_fldSourceId = pTarFieldsMgr->GetSyncFieldSourceId();
	for (COL col = rg.ColFrom(); col <= rg.ColTo(); ++col)
	{
		EtDbId id = m_spEtIdContainer->ColumnIdAt(col);
		auto it = oldFldMap.find(id);
		if (it == itEnd || pTarIds->Id2Idx(it->second) == INV_EtDbIdx)
			insertFldId.emplace_back(id);
		else
		{
			m_fldMap.emplace(id, it->second);
			holdFldId.emplace(it->second);
		}
	}
	size_t insertFldCnt = insertFldId.size();
	size_t insertRangeCnt = insertFldCnt;
	if (m_fldSourceId == INV_EtDbId)
		++insertRangeCnt;
	ks_stdptr<IDBSheetRange> spDbRange;
	if (insertRangeCnt > 0)
	{
		hr = m_spTarDbSheetOp->InsertFields(insertRangeCnt, &spDbRange);
		if (FAILED(hr))
			return hr;
	}
	ks_stdptr<IDBSheetViews> spDBSheetViews;
	hr = m_spTarDbSheetOp->GetDbSheetViews(&spDBSheetViews);
	if (FAILED(hr))
		return hr;
	for (int i = 0; i < insertFldCnt; ++i)
	{
		EtDbId newFldId = spDbRange->GetFieldId(i);
		ks_stdptr<IDbField> spField;
		hr = pTarFieldsMgr->GetField(newFldId, &spField);
		if (FAILED(hr))
			return hr;
		spField->SetSyncField(true);

		m_fldMap.emplace(insertFldId[i], newFldId);
		holdFldId.emplace(newFldId);
		class AllDbViewsEnum : public IDbViewEnum
		{
			using CallBack = std::function<HRESULT(IDBSheetView*)>;
		public:
			explicit AllDbViewsEnum(CallBack cb) : m_cb(std::move(cb)) {}
			HRESULT Do(IDBSheetView* pView) override
			{
				return m_cb(pView);
			}
		private:
			CallBack m_cb;
		};
		AllDbViewsEnum viewsEnum([newFldId](IDBSheetView* pView) -> HRESULT {
			ET_DBSheet_ViewType type = pView->GetType();
			if (type == et_DBSheetView_Grid)
				pView->SetFieldsHidden(newFldId, FALSE);
			return S_OK;
		});
		spDBSheetViews->EnumViews(&viewsEnum);
	}
	if (m_fldSourceId == INV_EtDbId)
		m_fldSourceId = spDbRange->GetFieldId(insertFldCnt);
	holdFldId.emplace(m_fldSourceId);
	hr = pTarFieldsMgr->SetSyncFieldSourceIdForIO(m_fldSourceId);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField> spField;
	hr = pTarFieldsMgr->GetField(m_fldSourceId, &spField);
	if (FAILED(hr))
		return hr;
	spField->SetSyncField(true);
	auto itHoldFldIdEnd = holdFldId.end();
	if (oldPrimaryFldId != m_fldMap[newPrimaryFldId])
	{
		hr = pTarFieldsMgr->SetPrimaryField(m_fldMap[newPrimaryFldId]);
		if (FAILED(hr))
			return hr;
	}
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetRecordIds(m_spTarDbSheetOp->GetAllRecords());
	for (EtDbIdx i = 0, tarFldCnt = pTarIds->Count(); i < tarFldCnt; ++i)
	{
		EtDbId id = pTarIds->IdAt(i);
		if (holdFldId.find(id) == itHoldFldIdEnd)
		{
			ks_stdptr<IDbField> spField;
			hr = pTarFieldsMgr->GetField(id, &spField);
			if (FAILED(hr))
				return hr;
			if (spField->IsSyncField())
				spRemoveRange->AddFieldId(id);
		}		
	}
	if (spRemoveRange->GetFieldCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveFields(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

HRESULT GridsheetSyncHelper::adjustRecords(const RANGE& rg)
{
	wo::util::CallTimeStat callTime("GridsheetSyncHelper::adjustRecords");
	HRESULT hr = S_OK;
	const IDBIds* pTarIds = m_spTarDbSheetOp->GetAllRecords();
	EtDbIdx srcRecCnt = rg.Height() - 1;
	EtDbIdx tarRecCnt = pTarIds->Count();
	std::unordered_map<EtDbId, EtDbId> oldRecMap;
	for (EtDbIdx i = 0; i < tarRecCnt; ++i)
	{
		EtDbId recId = pTarIds->IdAt(i);
		ks_bstr source;
		m_spTarDbSheetOp->GetValueString(recId, m_fldSourceId, &source);
		if (source.empty())
			continue;
		EtDbId srcRecId = decodeRecordId(source.c_str());
		if (srcRecId != INV_EtDbId)
			oldRecMap.emplace(srcRecId, recId);
	}
	auto itEnd = oldRecMap.end();
	std::unordered_set<EtDbId> holdRecId;
	holdRecId.reserve(srcRecCnt);
	std::vector<EtDbId> insertRecId;
	insertRecId.reserve(srcRecCnt);
	holdRecId.emplace(m_spEtIdContainer->RowIdAt(rg.RowFrom()));
	for (ROW row = rg.RowFrom() + 1; row <= rg.RowTo(); ++row)
	{
		if (m_spEt2DbSyncData->IsSkipRow(row))
			continue;

		EtDbId id = m_spEtIdContainer->RowIdAt(row);
		auto it = oldRecMap.find(id);
		if (it == itEnd)
			insertRecId.emplace_back(id);
		else
		{
			m_recMap.emplace(id, it->second);
			holdRecId.emplace(it->second);
		}
	}
	auto itHoldRecIdEnd = holdRecId.end();
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetFieldIds(m_spTarDbSheetOp->GetAllFields());
	for (EtDbIdx i = 0; i < tarRecCnt; ++i)
	{
		EtDbId id = pTarIds->IdAt(i);
		if (holdRecId.find(id) == itHoldRecIdEnd)
			spRemoveRange->AddRecordId(id);
	}
	if (spRemoveRange->GetRecordCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveRecords(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	size_t insertRecCnt = insertRecId.size();
	if (insertRecCnt > 0)
	{
		ks_stdptr<IDBSheetRange> spDbRange;
		hr = m_spTarDbSheetOp->InsertRecords(insertRecCnt, &spDbRange);
		if (FAILED(hr))
			return hr;
		for (int i = 0; i < insertRecCnt; ++i)
			m_recMap.emplace(insertRecId[i], spDbRange->GetRecordId(i));
	}
	ks_stdptr<IDBSheetRange> spClearRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spClearRange);
	spClearRange->SetRecordIds(pTarIds);
    const IDBIds* pIds = m_spTarDbSheetOp->GetAllFields();
    EtDbIdx cnt = pIds->Count();
    for (EtDbIdx i = 0; i < cnt; ++i)
    {
        EtDbId id = pIds->IdAt(i);
        ks_stdptr<IDbField> spField;
        hr = m_spTarDbSheetOp->GetFieldsManager()->GetField(id, &spField);
        if (FAILED(hr))
            return hr;
		if (spField->IsSyncField())
			spClearRange->AddFieldId(id);
    }
	
	return m_spTarDbSheetOp->Clear(spClearRange);
}

HRESULT GridsheetSyncHelper::setInfoFieldHidden()
{
	ks_stdptr<IDBSheetViews> spDBSheetViews;
	HRESULT hr = m_spTarDbSheetOp->GetDbSheetViews(&spDBSheetViews);
	if (FAILED(hr))
		return hr;
	EtDbId fldSourceId = m_fldSourceId;
	class AllDbViewsEnum : public IDbViewEnum
	{
		using CallBack = std::function<HRESULT(IDBSheetView*)>;
	public:
		explicit AllDbViewsEnum(CallBack cb) : m_cb(std::move(cb)) {}
		HRESULT Do(IDBSheetView* pView) override
		{
			return m_cb(pView);
		}
	private:
		CallBack m_cb;
	};
	AllDbViewsEnum viewsEnum([fldSourceId](IDBSheetView* pView) -> HRESULT {
		pView->SetFieldsHidden(fldSourceId, TRUE);
		return S_OK;
	});
	spDBSheetViews->EnumViews(&viewsEnum);
	return S_OK;
}
} // namespace wo