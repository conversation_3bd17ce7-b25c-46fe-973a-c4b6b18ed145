#ifndef __WEBET_ET_QUERY_CLASS_H__
#define __WEBET_ET_QUERY_CLASS_H__

#include <public_header/revision/src/kwrevisionctrl.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include "applogic/et_applogic_find.h"
#include "et_task_executor.h"

class KSmartTips;
namespace wo
{
using binary_wo::VarObj;
class KEtWorkbook;
class KEtRevisionContext;
class SplitBookOpt;

class EtQueryExecBase
{
protected:
	EtQueryExecBase(wo::KEtWorkbook*, PCWSTR tag);
public:
	virtual ~EtQueryExecBase();

public:
	virtual HRESULT PreExecute(const VarObj& param, KEtRevisionContext* ctx);
	virtual HRESULT PostExecute(HRESULT hr, const VarObj& param, KEtRevisionContext* ctx);
	virtual HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) = 0;
	PCWSTR GetTag();
	virtual int GetTagNumber() const;

protected:
	RANGE ReadRange(VarObj var);
	IDX GetSheetIdx(VarObj var, KEtRevisionContext* ctx);
	ks_stdptr<etoldapi::Range> CreateRangeObj(VarObj var);
	bool ReadRanges(const VarObj& var, IKRanges** ppRanges);
	void WriteRange(ISerialAcceptor* acpt, const RANGE& rg);
	bool WriteRangeObj(ISerialAcceptor* acpt, etoldapi::Range* pRange);
	bool WriteRangesObj(ISerialAcceptor* acpt, etoldapi::Range* pRange);
	drawing::AbstractShape* GetShape(VarObj param, KEtRevisionContext*);
	void CollectSecDocInfo(KEtRevisionContext *ctx, const char *action, WebStr cmd);
	void AddErrorStr(ISerialAcceptor* acpt, PCWSTR err);
	void AddErrorStr(ISerialAcceptor* acpt, HRESULT hr);
	bool GetFileSizeAndVersion(int& ver, WebFileSize& size);

	// 在命令执行前调用，用于检测是否有权限执行(如判断保护工作表）
	// 命令执行时需要验证该命令执行的所有区域是否有权限
	// note: 调用内核命令时最好使用最外层的接口（api 级别），最好调用 Range 的接口（大部分权限检查都是这里检查）
	virtual HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext*) = 0;
	virtual bool IsCompileFormulaAsText(KEtRevisionContext* ctx);

	HRESULT CheckProtectionCell(ISheet *isheet, INT32 row, INT32 col, KEtRevisionContext* ctx);
	HRESULT CheckProtectionRange(const RANGE &rg, KEtRevisionContext* ctx);
	HRESULT CheckProtectionRanges(IKRanges* rgs, KEtRevisionContext* ctx);
	HRESULT CheckProtectionRange(const VarObj& param, KEtRevisionContext* ctx);
	HRESULT CheckBookHasHiddenPropRange(KEtRevisionContext* ctx);
	HRESULT CheckProtectionIsMaster(IDX idxSheet,  KEtRevisionContext* ctx);
	bool isBookHasHidden(KEtRevisionContext* ctx);
	bool isThisBookHasHiddenProp(KEtRevisionContext* ctx);
	ks_stdptr<ISheetProtection> getSheetProtection(IDX idxSheet);
	HRESULT CheckSheetPermission(UINT sheetId, KEtRevisionContext* ctx);

protected:
	wo::KEtWorkbook* m_wwb;
	static EtTaskExecutor::CmdNameSet  m_cmdPermissionCheckWhiteList;//命令不检查权限，服务端内部调用确保不越权
private:
	PCWSTR m_tag;
};

class QuerySmartTipsValue : public EtQueryExecBase
{
public:
	QuerySmartTipsValue(KEtWorkbook*);
	~QuerySmartTipsValue();

public:
	virtual HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;

protected:
	HRESULT transformVisibleRanges(IKRanges *spInOutRanges, KEtRevisionContext* ctx);

	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 转为可见区域
	}

private:
	KSmartTips* m_pSmartTips;
};

class QueryRangeEnd : public EtQueryExecBase
{
public:
	QueryRangeEnd(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryRangeFindBase : public EtQueryExecBase
{
public:
	QueryRangeFindBase(wo::KEtWorkbook* wwb, PCWSTR tag);
	HRESULT GetFindParam(const VarObj& param
		, ks_stdptr<etoldapi::Range>& spRange
		, KComVariant& varWhat
		, KComVariant& varAfter
		, KComVariant& varLookIn
		, KComVariant& varLookAt
		, KComVariant& varSearchOrder
		, ETSearchDirection& searchDir
		, KComVariant& varMatchCase
		, KComVariant& varMatchByte
		, KComVariant& varSearchFormat
		, KComVariant& varSearchRange
		, CELL& cellActive);
};

class QueryRangeFind : public QueryRangeFindBase
{
public:
	QueryRangeFind(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KETFind 里面判断
	}
};

class QueryRangeFindAll : public QueryRangeFindBase
	, public KFakeUnknown<IInterfaceSearchAllListReference>
	, public KFakeUnknown<IFindAllCellIsContinue>
{
public:
	QueryRangeFindAll(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

private:
	STDPROC_(BOOL) IsContinue();
	STDPROC_(BOOL) QueryInterrupts();

	STDMETHODIMP addItem(FindALLResulteWo );
	STDMETHODIMP_ (int) size();

	HRESULT addItem(FindALLResulte record){return S_OK;}
	BOOL isWo(){return TRUE;}

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KETFind 里面判断
	}
private:
	std::vector<FindALLResulteWo> m_vctRecord;
};

class QueryRangeCopyBase : public EtQueryExecBase
{
public:
	QueryRangeCopyBase(wo::KEtWorkbook* wwb, PCWSTR tag);
	HRESULT execCopy(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt, bool bMultiCopy);
	void PrepareCopyContext(IDX sheetIdx, IKRanges* spRanges, KEtRevisionContext* ctx, ISerialAcceptor* acpt);

private:
    HRESULT NormalCopy(IETPersist* pPersist, IDX sheetid, IUnknown* pRanges, COPYINFO* pCopyInfo, const VarObj& param, KEtRevisionContext* ctx, bool bMultiCopy, bool bGenerateHtml, ISerialAcceptor* acpt);
    HRESULT BatchCopy(IETPersist* pPersist, IDX sheetid, IUnknown* pRanges, COPYINFO* pCopyInfo, const VarObj& param, KEtRevisionContext* ctx, bool bMultiCopy, bool bGenerateHtml, ISerialAcceptor* acpt);
	
    HRESULT Copy(IETPersist* pPersist, IDX sheetid, IUnknown* pRanges, const COPYINFO* pCopyInfo,
		const char* format, QString& text, bool &hasBreak);
    
    void CollectCopyInfos(const VarObj& param, KEtRevisionContext* ctx, bool bMultiCopy, const CopyTypeInfo& txtCopyInfo, const CopyTypeInfo& htmlCopyInfo, int totalTime);
private:
	const int m_oneMillonSize;
	bool m_coverShape = false;
};

class QueryMultiRangCopy : public QueryRangeCopyBase
{
public:
	QueryMultiRangCopy(KEtWorkbook*);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 里面判断
	}
};

class QueryRangCopy : public QueryRangeCopyBase
{
public:
	QueryRangCopy(KEtWorkbook*);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 里面判断
	}

};

class QueryFuncInfo : public EtQueryExecBase
{
public:
	QueryFuncInfo(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 和文档内容无关，不需要判断权限
	}
};

class QueryPicUploadProgress : public EtQueryExecBase
{
public:
	QueryPicUploadProgress(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 和文档内容无关，不需要判断权限
	}
};

class QueryConvertStatus : public EtQueryExecBase
{
  public:
    QueryConvertStatus(KEtWorkbook* wb);

  public:
    virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx,
                         ISerialAcceptor* acpt) override;

  protected:
    HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK; // 和文档内容无关，不需要判断权限
    }
};

class QueryWebExtension : public EtQueryExecBase
{
public:
	explicit QueryWebExtension(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override
	{
		return S_OK; // 和文档内容无关，不需要判断权限
	}
private:
    HRESULT HasHiddenContent(IKDataRange* pDataRange, KEtRevisionContext* pCtx);
	HRESULT SerializeWebChart(const VarObj& param,IKWebExtension* pWebExtension, KEtRevisionContext* pCtx, ISerialAcceptor* acpt);
	HRESULT SerializeRichText(IKWebExtension* pWebExtension, ISerialAcceptor* acpt);
};

class QuerySortExpand : public EtQueryExecBase
{
public:
	QuerySortExpand(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //Todo 行为需要产品确定
	}
};

class QueryCustomSortFields : public EtQueryExecBase
{
public:
	QueryCustomSortFields(KEtWorkbook* wb);

public:
	bool GetSort(const ks_stdptr<etoldapi::Range> &spRangeSel, ks_stdptr<etoldapi::Sort> &sort, ks_stdptr<IKSortData> &sortdata);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryCellColorsInRange : public EtQueryExecBase
{
public:
	QueryCellColorsInRange(KEtWorkbook* wb);

public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryNumfmt : public EtQueryExecBase
{
public:
	QueryNumfmt(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 编译数字格式，和文件内容无关，不需要判断
	}
};

class QueryRangeShowConditionRules : public EtQueryExecBase
{
public:
	QueryRangeShowConditionRules(KEtWorkbook* wb);
	QueryRangeShowConditionRules(KEtWorkbook* wb, PCWSTR tag);

public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT ExecBy(IDX idxSheet, IKRanges* prgs, ISerialAcceptor* acpt);

	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryShowConditionRules : public QueryRangeShowConditionRules
{
public:
	QueryShowConditionRules(KEtWorkbook* wb);

public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryApplyConditionFormat : public EtQueryExecBase
{
public:
	QueryApplyConditionFormat(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryText2ColPreView : public EtQueryExecBase
{
public:
	QueryText2ColPreView(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class QueryText2ColCheck : public EtQueryExecBase
{
public:
	QueryText2ColCheck(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		// 在 text2col_helper.cpp text2colProc  中判断单元格权限
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class QueryRangeSliceInfo : public EtQueryExecBase
{
public:
	QueryRangeSliceInfo(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryRangeCanAIFill : public EtQueryExecBase
{
public:
	QueryRangeCanAIFill(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryDataRowsOutOfRange : public EtQueryExecBase
{
public:
	QueryDataRowsOutOfRange(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryRangeContents : public EtQueryExecBase
{
public:
	QueryRangeContents(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QuerySameDvRange : public EtQueryExecBase
{
public:
	QuerySameDvRange(KEtWorkbook* wb);

public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 里面判断
	}
};

class QueryIsSameDvInRange : public EtQueryExecBase
{
public:
	QueryIsSameDvInRange(KEtWorkbook* wb);

public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

private:
	HRESULT AdjustRange(RANGE &rg);

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 里面判断
	}
};

class QueryCheckValidation : public EtQueryExecBase
{
public:
	QueryCheckValidation(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(param, ctx);
	}
};

class QueryDvCustomList : public EtQueryExecBase
{
public:
	QueryDvCustomList(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(param, ctx);
	}
};

class QueryDvCustomListByFormula : public EtQueryExecBase
{
public:
	QueryDvCustomListByFormula(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryDvCustomListCellResult : public EtQueryExecBase
{
public:
	QueryDvCustomListCellResult(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryTableStyle : public EtQueryExecBase
{
public:
	QueryTableStyle(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QueryGetRemoveDuplicate : public EtQueryExecBase
{
public:
	QueryGetRemoveDuplicate(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(param, ctx);
	}

private:
	void GetRangeDuplicateInfo(_Worksheet* pWorksheet, RANGE rg, CELL activeCell, BOOL bExpand, bool bListObjRg, bool bHeader, ISerialAcceptor* acpt);
	BOOL GetListObjRange(_Worksheet* pSheet, const RANGE& rg, RANGE& rgNew, BOOL* pbShowTitle);
	BOOL IsNeedTrimTitleRow(_Worksheet* pSheet, const RANGE& rg);
	BOOL IsRgInListObj(etoldapi::_Worksheet* pSheet, const RANGE& rgWork, etoldapi::ListObject** ppListObj);
	QString getColumnName(int nCol);		//获取某列的列名
};

class QueryDuplicatesCount : public EtQueryExecBase
{
public:
	QueryDuplicatesCount(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(param, ctx);
	}
};

class QueryTxt2ColSubRange : public EtQueryExecBase
{
public:
	QueryTxt2ColSubRange(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // Exec 中做了判断
	}
};

class KInsertProcessonData;
class QueryProcessOnInfo : public EtQueryExecBase
{
public:
	QueryProcessOnInfo(KEtWorkbook* wb);

public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

private:
	KInsertProcessonData* getProcessOnData(IKShape* pShape);
};

class QueryDataValidation : public EtQueryExecBase
{
public:
	QueryDataValidation(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}

private:
	HRESULT check(ROW row, COL col, const IDX );
	HRESULT GetValidation(ROW row, COL col, const IDX, ICoreValidation **ppv);
};

class QueryMergeFileList : public EtQueryExecBase
{
public:
	QueryMergeFileList(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class QueryAutoFilter : public EtQueryExecBase
{
public:
	QueryAutoFilter(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在内核和命令执行时判断权限
	}

private:
	PCWSTR DetectAutofilterType(const RANGE &, PCWSTR);
	bool HasAutofilter(const RANGE &, PCWSTR);
};

class QueryPivotFilter : public EtQueryExecBase
{
public:
	QueryPivotFilter(KEtWorkbook*);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}

private:
	void serialCompactFields(ISerialAcceptor *acpt, pivot_core::AxisFieldType, pivot_core::IPivotTable *);
	static WebStr pivotTypeToStr(pivot_core::AxisFieldType);
	void rect2Range(const RECT& rect, RANGE *out);
};

class QueryPivotRangeGroupType : public EtQueryExecBase
{
public:
	QueryPivotRangeGroupType(KEtWorkbook*);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
	HRESULT CreateRangeByTwoRanges(_Worksheet* pSheet, Range* pFirst, Range* pSecond, Range** ppRange);
};


class QueryCellHistory : public EtQueryExecBase
{
public:
	QueryCellHistory(KEtWorkbook*);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
};

class QuerySingleCellHistory : public EtQueryExecBase
{
public:
	QuerySingleCellHistory(KEtWorkbook*);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
};

class QueryTotalHistoryCount : public EtQueryExecBase
{
public:
	QueryTotalHistoryCount(KEtWorkbook*);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
};

class EnumHistoryCell : public EtQueryExecBase
{
public:
	EnumHistoryCell(KEtWorkbook*);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;


protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在命令执行时判断权限
	}
};

class QueryCanUnlockArea : public EtQueryExecBase
{
public:
	QueryCanUnlockArea(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};

class QueryNextUnlock : public EtQueryExecBase
{
public:
	QueryNextUnlock(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};

class QueryValidateProtectSheetPassword : public EtQueryExecBase
{
public:
	QueryValidateProtectSheetPassword(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};

class QueryValidateProtectSheetSecretKey : public EtQueryExecBase
{
public:
	QueryValidateProtectSheetSecretKey(KEtWorkbook* wb);
public:
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};

class QueryGuessFormTitles : public EtQueryExecBase
{
	 typedef std::vector<ks_wstring> VecStr;
public:
	QueryGuessFormTitles(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在exec中做判断
	}

private:
	ROW CollectTitles(KEtRevisionContext* ctx, VecStr& vecTiltes, ISheet *pSheet);
	int getRowMergeTops(ISheet *pSheet, const RANGE& rg, std::vector<CELL>& tops, bool isForce);
	void CollectRowCellsText(KEtRevisionContext* ctx, VecStr& vecTiltes, ISheet *pSheet, const RANGE& rg, std::vector<CELL>* tops);
	ROW GetFilter(KEtRevisionContext* ctx, VecStr& vecTiltes, ISheet *pSheet);
};

class QuerySupBooksList : public EtQueryExecBase
{
public:
	QuerySupBooksList(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryChartData : public EtQueryExecBase
{
public:
	QueryChartData(KEtWorkbook* wb);
public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 EtChartLayer 判断
	}
};


class QuerySlicerData : public EtQueryExecBase
{
public:
	QuerySlicerData(KEtWorkbook* wb);
public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class QuerySheetBackground : public EtQueryExecBase
{
public:
	QuerySheetBackground(KEtWorkbook* wb);
public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 现在还不需要判断
	}
};

class QueryEmpty : public EtQueryExecBase
{
public:
	QueryEmpty(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryCustomNF : public EtQueryExecBase
{
public:
	QueryCustomNF(KEtWorkbook* wb);
public:
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};

class QueryShapeCopy : public EtQueryExecBase
{
public:
	QueryShapeCopy(KEtWorkbook*);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
private:
void GetAbsShapeVect(const VarObj param, KEtRevisionContext* pCtx, std::vector<drawing::AbstractShape*>& shapes);
void findChartID(drawing::AbstractShape* pShape, drawing::ShapeTree* cpTree, std::vector<QString>& results);
};

class QueryDocSlimStat : public EtQueryExecBase
{
public:
	QueryDocSlimStat(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryNeedDocSlim : public EtQueryExecBase
{
public:
	QueryNeedDocSlim(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}

	void _CollectInfo(KEtRevisionContext* ctx, double timeConsumed);

private:
	//检查到大于nLimit数量时不继续检查，-1为全部检查
	int getValidShapesCnt(int nLimit = -1);
	int getCloudShapeCnt(EtShapeTree*);
	int64 getNullCellsCount();

	struct InfoCache
	{
		InfoCache() : isValid(false) {}

		WebInt version;
		bool bAutoSlim;
		bool exceedShapesLimit;
		bool hasInvisiblePicture;
		bool isValid;
	};
	HRESULT updateCache(WebInt);
	void getFileVersionSize(int& version, WebFileSize& size);
	InfoCache m_cache;
	static constexpr size_t shapesLimit = 50; // 50 shapes
	static constexpr size_t shapesHardLimit = 10000; // 10000 shapes
};

class CollectSecDocInfoCmd : public EtQueryExecBase
{
public:
	CollectSecDocInfoCmd(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryHyperlink : public EtQueryExecBase
{
public:
	QueryHyperlink(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
	HRESULT getRangesFromSubAddres(LPCWSTR lpwszSubAddress, IDX iSheet, CELL activeCell, range_helper::ranges& rgs);

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override;
};

class QuerySubscriptionInfo : public EtQueryExecBase
{
public:
	QuerySubscriptionInfo(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};

class QueryDocumentCustomProperty : public EtQueryExecBase
{
public:
	QueryDocumentCustomProperty(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
private:
	static QDate fromSysDate(DATE date);
};

class QueryRangeValues : public EtQueryExecBase
{
public:
	QueryRangeValues(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在exec中做判断
	}
private:
	class KCellValueAcpt;
};

class QueryCommentInfoById : public EtQueryExecBase
{
public:
	QueryCommentInfoById(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在exec中做判断
	}
private:
	bool FindCmtById(ICellComments* pCellCmts, PCWSTR commentId, CELL& cmtPos, std::set<ks_wstring>& userIds, bool& bIsResolved, ks_wstring& chainId);
};

class QueryPivotTableDefaultRange : public EtQueryExecBase
{
public:
	QueryPivotTableDefaultRange(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在exec中做判断
	}
};

class QueryNewCommentCount : public EtQueryExecBase
{
public:
	QueryNewCommentCount(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryPivotTableExtendRange : public EtQueryExecBase
{
public:
	QueryPivotTableExtendRange(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryIsMergeTaskValid : public EtQueryExecBase
{
public:
	QueryIsMergeTaskValid(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryMergeTasksInfo : public EtQueryExecBase
{
public:
	QueryMergeTasksInfo(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QuerySheetNameList : public EtQueryExecBase
{
public:
	QuerySheetNameList(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryDiagnosisResult : public EtQueryExecBase
{
public:
	QueryDiagnosisResult(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在exec中做判断
	}
};

class QueryDiagnosisStart : public EtQueryExecBase
{
public:
	QueryDiagnosisStart(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在exec中做判断
	}
};

class QueryDiagnosisStop : public EtQueryExecBase
{
public:
	QueryDiagnosisStop(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在exec中做判断
	}
};

class QueryTextLinkPosition : public EtQueryExecBase
{
public:
	QueryTextLinkPosition(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在exec中做判断
	}

	bool QueryOnCellLinks(const QString &address, bool isURL, ISheet *spSheet, const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt);
	bool QueryOnCommentLinks(const QString &address, bool isURL, ISheet *spSheet, const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt);
	bool QueryOnRuns(IDX shtIdx, const QString &address, bool isURL, IKHyperlink *spKHL, IHyperlinkRuns *runs, KEtRevisionContext* ctx, ISerialAcceptor* acpt);
	QString GetKWProtocolUUID(const QString &address);
	bool CheckSameUUID(const QString &url, const QString &matchStr, bool isURL);
	bool WriteResult(IDX shtIdx, const RANGE& rg, KEtRevisionContext* ctx, ISerialAcceptor* acpt, bool bComment, bool bIsResolved = false, PCWSTR chainId = nullptr);
private:
	QRegExp m_rx{"kw:(mention|link)\\S+", Qt::CaseInsensitive};
};

class QueryImportrangeRefCellUrls : public EtQueryExecBase
{
public:
	QueryImportrangeRefCellUrls(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(param, ctx);
	}
};

class QueryFormDataRow : public EtQueryExecBase
{
public:
	QueryFormDataRow(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
	virtual HRESULT PostExecute(HRESULT hr, const VarObj& param, KEtRevisionContext* ctx) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	void handleHttpResponse(HRESULT hr, int row,KEtRevisionContext* pCtx);
};


class QueryRangeData : public EtQueryExecBase
{
public:
	QueryRangeData(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryMapCellPos : public EtQueryExecBase
{
public:
	QueryMapCellPos(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryControlARange : public EtQueryExecBase
{
public:
	QueryControlARange(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryRangeFirstRow : public EtQueryExecBase
{
public:
	QueryRangeFirstRow(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
private:
	class KCellValueAcpt;
};

class QueryRangeFirstCol : public EtQueryExecBase
{
public:
	explicit QueryRangeFirstCol(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
private:
	class KCellValueAcpt;
};

class QueryRangeQRLabelCount : public EtQueryExecBase
{
public:
    QueryRangeQRLabelCount(KEtWorkbook* wb);
    virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
protected:
    HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
};

class QueryCollectInquireInfo : public EtQueryExecBase
{
public:
	QueryCollectInquireInfo(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
};

class QueryGetAllInquirer : public EtQueryExecBase
{
public:
	QueryGetAllInquirer(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QuerySingleCellHistoryList : public EtQueryExecBase
{
public:
	QuerySingleCellHistoryList(KEtWorkbook*);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx)
	{
		return S_OK;  // 在 Exec 判断权限
	}
};

class QueryRefRangeByCommitVer : public EtQueryExecBase
{
public:
	QueryRefRangeByCommitVer(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx)
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

// 内部命令，测试使用
class QueryCellHistoryPurgeOptions : public EtQueryExecBase
{
public:
	QueryCellHistoryPurgeOptions(KEtWorkbook* wb);
	HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx)
	{
		return S_OK; // 不需要判断
	}
};

class QueryAiRecognizedInfo : public EtQueryExecBase
{
public:
	explicit QueryAiRecognizedInfo(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override
	{
		return S_OK; // 不需要判断,仅导出用户可查看的内容
	}
	void WriteCellCollect(QVariantMap, ISerialAcceptor* acpt);
};

class QuerySmartSheetRecognition : public EtQueryExecBase
{
public:
  explicit QuerySmartSheetRecognition(KEtWorkbook *);
  HRESULT Exec(const VarObj &, KEtRevisionContext *, ISerialAcceptor *) override;
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override;
	bool CheckExclusivePermission(IKWorksheet* pWorksheet, KEtRevisionContext*);

private:
	std::map<QString, int> m_jmTaskMap;
};

class QueryGetTableTitleCnt : public EtQueryExecBase
{
public:
	explicit QueryGetTableTitleCnt(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override
	{
		return S_OK; // 在Exec中做判断
	}
private:
	const int m_maxTitleCount = 30000;
	int m_leftTitleCount = 0;
};

class QueryGetAppRelatedSheetDirty : public EtQueryExecBase
{
public:
	explicit QueryGetAppRelatedSheetDirty(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryNeedUpdateAppVersion : public EtQueryExecBase
{
public:
	explicit QueryNeedUpdateAppVersion(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override
	{
		return S_OK; // 不需要判断
	}
};

class QuerySpecialCells : public EtQueryExecBase
{
public:
	QuerySpecialCells(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;

protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override;
};

class QueryCalculate : public EtQueryExecBase
{
public:
	QueryCalculate(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
 
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override;

private:
	HRESULT ResolveNumFmt(PCWSTR pcw, const COMPILE_RESULT& crx, const KXF& oxf, OUT WCHAR(&fmt)[MAX_NUMBERFMT_CCH + 1]);
	BOOL MakeCompileParam(OUT CS_COMPILE_PARAM& ccp, OUT KXF& oxf);
	HRESULT DealCompileResult(COMPILE_RESULT& crx);
};

class QueryWorksheetFunction : public EtQueryExecBase
{
public:
	QueryWorksheetFunction(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
 
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override
	{
		return S_OK; // 不需要判断
	}
private:
	HRESULT createTokenVectorInstant(const VarObj& varTokenVec, ITokenVectorInstant** ppVec);
	managed_token_ptr createExecToken(const VarObj& varToken);
};

class QueryRangeValue2 : public EtQueryExecBase
{
public:
	QueryRangeValue2(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
 
protected:
	HRESULT CheckCmdPermission(const VarObj&, KEtRevisionContext*) override;
};

// 内部调试用
class QueryUsersInfo : public EtQueryExecBase
{
public:
	QueryUsersInfo(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};

class QuerySheetHasCellTextOverflow : public EtQueryExecBase
{
public:
	QuerySheetHasCellTextOverflow(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};

class QueryIsEmptyCell : public EtQueryExecBase
{
public:
 	QueryIsEmptyCell(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class QueryCustomCalendar : public EtQueryExecBase
{
public:
	QueryCustomCalendar(KEtWorkbook* wb);
	virtual HRESULT Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt) override;

protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; //不需要判断
	}
};
// 查找范围内的EndRow。若有多个sheet，则返回所有sheet中的最末行，
// 例如sheet1中查到的endRow是第5行，sheet2查到的是第10行，则返回值为10
class QueryEndEmptyRowInRg : public EtQueryExecBase
{
public:
 	QueryEndEmptyRowInRg(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

// 拆分表格查询相关
class QuerySplitSheet : public EtQueryExecBase
{
public:
	QuerySplitSheet(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在执行中判断
	}

private:
	void WriteFilterAccording(ISerialAcceptor* acpt, bool isA1Ref, bool bHasSelectRange, SplitBookOpt*, bool& bDateType, int selectIndex);
	void WriteAccording(ISerialAcceptor* acpt, bool isA1Ref, bool bHasSelectRange, SplitBookOpt*, bool& bDateType, int selectIndex);
	bool IsExceedMaxWidthHeight(etoldapi::Range* pRange, int maxCol, int maxRow);
	void GetRANGE(etoldapi::Range* pRange, RANGE& range);
};	


// 查询表格是否使用了AI公式
class QueryBookHasAiFormula : public EtQueryExecBase
{
public:
	QueryBookHasAiFormula(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在执行中判断
	}
};

class QueryChartPos : public EtQueryExecBase
{
public:
 	QueryChartPos(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}

};

class QueryDataAnalyzeMergeStatus : public EtQueryExecBase
{
public:
 	QueryDataAnalyzeMergeStatus(KEtWorkbook*);
	HRESULT Exec(const VarObj&, KEtRevisionContext*, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(const VarObj& param, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}

};

} // namespace wo



#endif //__WEBET_ET_QUERY_CLASS_H__
