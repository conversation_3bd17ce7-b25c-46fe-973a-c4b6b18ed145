﻿#include "et_xva_cmd.h"
#include "helpers/slim_helper.h"
#include "wo/workbook_obj.h"
#include "kso/api/apiex_old.h"
#include <kso/api/ksoapi_old.h>
#include "wo_convert_common.h"
#include "workbooks.h"
#include <public_header/weblog/weblog.h>
#include "api/jsapi/jde/jside.h"
#include <package/openXmlServer.h>
#include "persist/et_persist_basic_itf.h"

namespace wo
{
struct PAPERSIZE
{
	qreal fWidth;
	qreal fHeight;
};

XvaCommand::XvaCommand(KEtWorkbooks* wbs, KEtWorkbook* wb) : m_wbs(wbs), m_workbook(wb)
{
    // Register functions
    funcMap["docPageCount"] = &XvaCommand::ExecQueryDocPageCount;
    funcMap["queryFonts"] = &XvaCommand::ExecQueryFonts;

    funcMap["ExecDocSlim"] = &XvaCommand::ExecDocSlim;
    funcMap["ExecQuerySheets"] = &XvaCommand::ExecQuerySheets;

    funcMap["mergeDocument"] = &XvaCommand::ExecMergeDocument;
    funcMap["splitDocument"] = &XvaCommand::ExecSplitDocument;
    funcMap["updatePassword"] = &XvaCommand::ExecUpdatePassword;

    funcMap["addWatermark"] = &XvaCommand::ExecAddWatermark;
	funcMap["execEtapi"] = &XvaCommand::ExecJsApi;
	funcMap["queryDocProperties"] = &XvaCommand::ExecQueryDocProperties;

	funcMap["extractDocContent"] = &XvaCommand::ExecExtractContent;
    funcMap["execCellImg2FloatImg"] = &XvaCommand::ExecCellImg2FloatImg;
}

XvaCommand::~XvaCommand()
{

}

WebInt XvaCommand::ExecExtraCmd(WebStr command, const VarObj& root, const VarObj& param, BinWriter &writer)
{
	std::string cmdName = QString::fromUtf16(command).toStdString();

	// 有些命令不需要这个参数了
	// if (!root.has("xva")) return XVA_INVALID_ARG;

    auto iter = funcMap.find(cmdName);
    if (iter == funcMap.end())
	{
		WEBLOG_ERROR_STRING("Command " + cmdName + " not supported");
        return XVA_COMMAND_NOT_SUPPORT;
	}

    CmdFuncPtr func = iter->second;
    if (func == NULL)
        return XVA_COMMAND_NOT_SUPPORT;
    
	// Open() 的时候SetBreak(true)会影响存盘时的计算过程，这里需要重置一下，以防文件保存失败
	SetBreak(false);

    return (this->*func)(root, param, writer);
}

_Workbook* XvaCommand::GetCoreWorkbook()
{
	return m_workbook->GetCoreWorkbook();
}

_Application* XvaCommand::GetCoreApplication()
{
	// 可能没有当前文档，所以使用 m_wbs获取app
	// return m_workbook->GetCoreApp();
	return m_wbs->GetCoreApp();
}

/*
 * @brief 获取表格某个sheet 在指定纸张，打印方向下 分页数量
 */
WebInt XvaCommand::ExecQueryDocPageCount(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	if (!root.has("xva"))
	{
		WEBLOG_ERROR("ExecQueryDocPageCount: param xva is missing");
		return XVA_INVALID_ARG;
	}

	if (!root.has("sheetIndex"))
	{
		WEBLOG_ERROR("ExecQueryDocPageCount: param sheetIndex is missing");
		return XVA_INVALID_ARG;
	}

	//sheetIndex 从1开始
	IDX sheetIndex = root.field_int32("sheetIndex");
	if (sheetIndex < 1)
	{
		WEBLOG_ERROR("ExecQueryDocPageCount: param sheetIndex should be >= 1");
		return XVA_INVALID_ARG;
	}

	ks_stdptr<_Worksheet> spSheet;
	if (!getExportSheet(sheetIndex - 1, spSheet))
	{
		WEBLOG_ERROR_FMT("Failed to get sheet at index: %d", sheetIndex - 1);
		return WO_FAIL;
	}

	ks_stdptr<IKWorksheetView> spSheetView = spSheet->GetActiveWorksheetView();
	if (!spSheetView)
	{
		WEBLOG_ERROR("GetActiveWorksheetView failed");
		return WO_FAIL;
	}

	IRenderView* pRdView = spSheetView->GetActiveRenderView();
	ASSERT(pRdView);
	if (!pRdView)
		return WO_FAIL;

	IRenderExportPic *pRdExportPic = pRdView->GetRenderExportPic();
	ASSERT(pRdExportPic);
	if (!pRdExportPic)
	{
		WEBLOG_ERROR("GetRenderExportPic() failed");
		return WO_FAIL;
	}

	//定义3种尺寸的大小，分别为A4, A2, A0; 没有设置导出所有行/列到单页的情况下，默认A4，否则默认A2
	const int PAPER_SIZE_NUM = 3;
	PAPERSIZE paperSizeTable[PAPER_SIZE_NUM] = {
		{ 8.27f, 11.69f }, //英寸
		{ 16.54f, 23.39f },
		{ 33.11f, 46.81f },
	};

	PAGESETUP *pPageSetup = NULL;
	spSheet->GetPageInfo()->GetPageSetup(&pPageSetup);
	ASSERT(pPageSetup);
	if (pPageSetup)
	{
		int fitToHeight = root.has("fitToHeight") ? root.field_int32("fitToHeight") : -1;
		int fitToWidth = root.has("fitToWidth") ? root.field_int32("fitToWidth") : -1;
		int orientation = root.has("orientation") ? root.field_int32("orientation") : -1; //
		int order = root.has("order") ? root.field_int32("order") : -1; //先列后行 还是 先行后列
		int paperSize = root.has("paperSize") ? root.field_int32("paperSize") : -1;
		if (paperSize < 0 || paperSize > PAPER_SIZE_NUM)
			paperSize = 0;

		if (fitToHeight != -1 || fitToWidth != -1)
			pPageSetup->bIsFit = (fitToHeight != 0 || fitToWidth != 0);
		if (fitToHeight != -1)
			pPageSetup->nFitToPagesTall = fitToHeight;
		if (fitToWidth != -1)
			pPageSetup->nFitToPagesWide = fitToWidth;
		if (orientation != -1)
			pPageSetup->orientation = orientation == 1 ? PGSTP_Portrait : PGSTP_Landscape;
		if (order != -1)
			pPageSetup->order = order;

		ks_stdptr<PageSetup> spPageSetup;
		spSheet->get_PageSetup(&spPageSetup);
		if (spPageSetup)
		{
			//以磅为单位
			float paperWidth = paperSizeTable[paperSize].fWidth * 72;
			float paperHeight = paperSizeTable[paperSize].fHeight * 72;
			if (pPageSetup->orientation == PGSTP_Landscape)
				std::swap(paperWidth, paperHeight);

			VARIANT width, height;
			V_VT(&width) = VT_R4;
			V_VT(&height) = VT_R4;
			V_R4(&width) = paperWidth;
			V_R4(&height) = paperHeight;
			spPageSetup->SetPaperWidthHeight(width, height);
		}
	}

	writer.addInt32Field(pRdExportPic->GetPageCount(), "pageCount");
	return WO_OK;
}

WebInt XvaCommand::ExecQueryFonts(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	queryFonts(GetCoreWorkbook(), writer);
	return WO_OK;
}

WebInt XvaCommand::ExecDocSlim(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	if (!root.has("xva"))
	{
		WEBLOG_ERROR("ExecDocSlim: param xva is missing");
		return XVA_INVALID_ARG;
	}

	if (!root.has("param"))
	{
		WEBLOG_ERROR("ExecDocSlim: param param is missing");
		return XVA_INVALID_ARG;
	}

	ASSERT(m_workbook);
	if (!m_workbook)
	{
		WEBLOG_ERROR("No active workbook found");
		return WO_FAIL;
	}

	IET_DocSlim *pDocSlim = m_workbook->GetDocSlim();
	if (!pDocSlim)
	{
		WEBLOG_ERROR("GetDocSlim() failed");
		return WO_FAIL;
	}

#define NULL_CELLS_NAME "nullCells"
#define UNUSED_STYLES_NAME "unusedDuplicateStyles"
#define UNUSED_SHAPES_NAME "unusedShapes"
#define UNUSED_CELL_PICS_NAME "unusedCellPictures"

	bool bAutoSlim = param.has("bAutoSlim") ? param.field_bool("bAutoSlim") : false;
	if (bAutoSlim)
	{
		// ================== checking ==================
		pDocSlim->CheckNullCellsOnlyHasXf();
		//INT64 nullCells = pDocSlim->GetNullCellsCount();
		pDocSlim->CheckUnusedShapes();
		//size_t unusedShapes = pDocSlim->GetUnusedShapesCount();
		pDocSlim->CheckUnusedCellPictures();
		//size_t unusedCellPictures = pDocSlim->GetUnusedCellPicturesCount();

		// ================== slimming ==================
		{
			pDocSlim->DeleteNullCellsOnlyHasXf(NULL);
			pDocSlim->DeleteUnsedShapes(NULL);
			pDocSlim->DeleteUnusedCellPictures(NULL);
		}
	}
	else
	{
		// ================== slimming ==================
		bool bDeleteNullCellsOnlyHasXf = param.has("bDeleteNullCellsOnlyHasXf") && param.field_bool("bDeleteNullCellsOnlyHasXf");
		bool bDeleteUnusedShapes = param.has("bDeleteUnusedShapes") && param.field_bool("bDeleteUnusedShapes");
		bool bDeleteUnusedCellPictures = param.has("bDeleteUnusedCellPictures") && param.field_bool("bDeleteUnusedCellPictures");
		bool bDeleteUnusedDuplicateStyle = param.has("bDeleteUnusedDuplicateStyle") && param.field_bool("bDeleteUnusedDuplicateStyle");

		if (bDeleteNullCellsOnlyHasXf)
		{
			pDocSlim->CheckNullCellsOnlyHasXf();
			pDocSlim->DeleteNullCellsOnlyHasXf(NULL);
		}

		if (bDeleteUnusedShapes)
		{
			pDocSlim->CheckUnusedShapes();
			pDocSlim->DeleteUnsedShapes(NULL);
		}

		if (bDeleteUnusedCellPictures)
		{
			pDocSlim->CheckUnusedCellPictures();
			pDocSlim->DeleteUnusedCellPictures(NULL);
		}

		if (bDeleteUnusedDuplicateStyle)
		{
			pDocSlim->CheckUnusedDuplicateStyle();
			pDocSlim->DeleteUnusedDuplicateStyle(NULL);
		}
	}

	writer.addBoolField(true, "finishSlimming");
	writer.addFloat64Field(pDocSlim->GetNullCellsCount(), NULL_CELLS_NAME);
	writer.addInt32Field(pDocSlim->GetUnusedDuplicateStylesCount(), UNUSED_STYLES_NAME);
	writer.addInt32Field(pDocSlim->GetUnusedShapesCount(), UNUSED_SHAPES_NAME);
//	// 完成瘦身之后发送的是已压缩的图片, 复用了potentialPicturesToBeCompressed这个字段
//	writer.addInt32Field(pDocSlim->GetCompressedPicturesCount(), PICS_TO_COMPRESSED_NAME);
	writer.addInt32Field(pDocSlim->GetUnusedCellPicturesCount(), UNUSED_CELL_PICS_NAME);
	return WO_OK;
}

/*
@brief 统计表格所有sheet，以及属性等数据
 */
WebInt XvaCommand::ExecQuerySheets(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	if (!root.has("xva"))
	{
		WEBLOG_ERROR("ExecQuerySheets: param xva is missing");
		return XVA_INVALID_ARG;
	}

	_Workbook* wb = m_workbook->GetCoreWorkbook();
	if (!wb)
	{
		WEBLOG_ERROR("ExecQuerySheets: no active workbook found");
		return WO_FAIL;
	}

	IKWorksheets* pWorksheets = wb->GetWorksheets();
	if (!pWorksheets)
		return WO_FAIL;

	IWorkbookObj* wbObj = wb->GetWoObject();
	if (!wbObj)
		return WO_FAIL;

	IDX iActiveSheet = INVALIDIDX;
	IKWorksheet* pActiveWs = wb->GetActiveWorksheet();
	if (pActiveWs)
		pActiveWs->GetSheet()->GetIndex(&iActiveSheet);

	writer.addKey("sheets");
	writer.beginArray();
	for (INT32 cnt = pWorksheets->GetSheetCount(), i = 0; i < cnt; ++i)
	{
		ks_stdptr<_Worksheet> spSheet = pWorksheets->GetSheetItem(i);

		VARIANT vtVisible;
		spSheet->get_Visible(&vtVisible);

		writer.beginStruct();
		writer.addInt32Field(V_I4(&vtVisible), "visible");
		writer.addInt32Field(i, "sheetIndex");

		ks_bstr bstrSheetName;
		spSheet->get_Name(&bstrSheetName);
		writer.addStringField(bstrSheetName, "sheetName");

		AbsObject* shtObj = wbObj->getSheetItem(i);
		if (shtObj)
		{
			writer.addKey("id");
			writer.addAbsObjID(shtObj->objId(), true);
		}

		writer.addBoolField(iActiveSheet == i, "active");
		writer.endStruct();

		qDebug() << "[" << __FUNCTION__ << "]" << " sheetIndex: " << i << " visible: " << V_I4(&vtVisible) << " id: " << shtObj->objId();
	}
	writer.endArray();
	return WO_OK;
}

bool XvaCommand::getExportSheet(int iSheet, ks_stdptr<_Worksheet> &spSheet)
{
	if (iSheet < 0)
		return false;

	ASSERT(m_workbook);
	if (!m_workbook)
		return false;

	if (!m_workbook->GetCoreWorkbook())
		return false;

	if (!m_workbook->GetCoreWorkbook()->GetWorksheets())
		return false;

	IKWorksheet* pWs = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWs)
		return false;

	spSheet = pWs;
	return true;
}


//
//	文档合并、拆分
//	代码改自 converttool/et/etsplitmerge.cpp
//

#define XLNOCHANGE	3
#define PDFFORMAT	103

static bool openEt(_Application* pApp, const QString& filePath, _Workbook** pWorkbook)
{
	pApp->put_DisplayAlerts(VARIANT_FALSE);
	ks_stdptr<Workbooks> spWorkbooks;
	pApp->get_Workbooks(&spWorkbooks);
	if (!spWorkbooks)
		return false;
	
	ks_stdptr<_Workbook> spWorkbook;
	VARIANT readOnly;
	V_VT(&readOnly) = VT_BOOL;
	V_BOOL(&readOnly) = VARIANT_TRUE;
	VARIANT var = {0};

	ks_bstr strSourceFileName(krt::utf16(filePath));
	VARIANT OpenFileName;
	V_VT(&OpenFileName) = VT_BSTR;
	V_BSTR(&OpenFileName) = strSourceFileName;

	HRESULT hr = spWorkbooks->Open(OpenFileName, var, readOnly, var, var, var, var, var, var, var, var, var, var, &spWorkbook);
	if (SUCCEEDED(hr) && spWorkbook)
	{
		*pWorkbook = spWorkbook.detach();
		return true;
	}
	else
	{
		return false;
	}
}

class WorkbookGuard
{
public:
	WorkbookGuard(_Workbook* pWorkbook)
	{
		m_pWorkbook = pWorkbook;
	}

	~WorkbookGuard()
	{
		if (m_pWorkbook)
		{
			VARIANT var;
			V_VT(&var) = VT_EMPTY;
			m_pWorkbook->Close(var, var ,var);
		}
	}

	_Workbook* m_pWorkbook = 0;
};

static int GetWorkbookSheetCount(_Workbook* workbook)
{
	ks_stdptr<Sheets> sheets;
	HRESULT hr = workbook->get_Sheets(&sheets);
	if (FAILED(hr))
		return 0;

	long sheetCount = 0;
	hr = sheets->get_Count(&sheetCount);
	if (FAILED(hr))
		return 0;

	return sheetCount;
}

static bool ExtractEtDocumentImpl(_Application* pApp, _Workbook* spWorkbook, int start, int end, int* pOriCount = NULL)
{
	ks_stdptr<Sheets> sheets;
	HRESULT hr = spWorkbook->get_Sheets(&sheets);
	if (FAILED(hr))
		return false;

	long sheetCount = 0;
	hr = sheets->get_Count(&sheetCount);
	if (FAILED(hr))
		return false;

	if (pOriCount)
		*pOriCount = sheetCount;

	if (start > sheetCount) {
		return false;
	}

	if (end == -1 || end > sheetCount)
		end = sheetCount;

	QVector<int> indexes;
	for (int i = 1; i <= sheetCount; ++i)
	{
		if (i < start || i > end)
			indexes.append(i);
	}

	if (indexes.count () == 0)
		return true;
	
	VARIANT_BOOL varBool;
	pApp->get_DisplayAlerts(&varBool);
	pApp->put_DisplayAlerts(VARIANT_FALSE);

	kfc::ks_stdptr<Sheets> sheetsToRemove;
	SAFEARRAYBOUND arrayBound;
	arrayBound.lLbound = 0;
	arrayBound.cElements = indexes.count();
	SAFEARRAY* safeArray = SafeArrayCreate(VT_VARIANT, 1, &arrayBound);
	for (int i = 0; i < indexes.count(); ++i)
	{
		kfc::KComVariant varIndex;
		V_VT(&varIndex) = VT_INT;
		V_I4(&varIndex) = indexes[i];
		SafeArrayPutElement(safeArray, &i, &varIndex);
	}

	kfc::KComVariant varIndexs;
	V_VT(&varIndexs) = VT_ARRAY | VT_VARIANT;
	V_ARRAY(&varIndexs) = safeArray;
	ks_stdptr<IKCoreObject> dispatch;
	sheets->get_Item(varIndexs, &dispatch);
	if (!dispatch)
		return false;

	dispatch->QueryInterface(IID_Sheets, (void**)&sheetsToRemove);
	if (!sheetsToRemove)
		return false;

	hr = sheetsToRemove->Delete();
	if (FAILED(hr))
		return false;
	
	// 不知为何，普通book里面会有不可见的sheet
	// ksheets.cpp -> KSheets::Delete() -> if (nSelVisable >= nVisibleSheet)
	long newCount = 0;
	ks_stdptr<Sheets> newSheets;
	hr = spWorkbook->get_Sheets(&newSheets);
	if (FAILED(hr))
		return false;
	hr = newSheets->get_Count(&newCount);
	if (FAILED(hr))
		return false;
	if (newCount == sheetCount)		// 如果删除后，sheet数量不变，则判定删除失败
	{
		WEBLOG_WARN("Failed to delete sheets");
		return false;
	}

	pApp->put_DisplayAlerts(varBool);

	return true;
}

static bool ExtractEtDocument(_Application* pApp, const QString& filePath, int start, int end, _Workbook** pWorkbookOut, int* pOriCount = NULL)
{
	ks_stdptr<_Workbook> spWorkbook;
	if (!openEt(pApp, filePath, &spWorkbook) || !spWorkbook)
		return false;

	WorkbookGuard etGuard(spWorkbook);
	
	bool ret = ExtractEtDocumentImpl(pApp, spWorkbook, start, end, pOriCount);

	if (pWorkbookOut)
	{
		*pWorkbookOut = spWorkbook.detach();
		etGuard.m_pWorkbook = NULL;
	}

	return ret;
}

static bool GetLastWorksheet(_Workbook* pWorkbook, _Worksheet** pWorkbookOut)
{
	ks_stdptr<Sheets> sheets;
	HRESULT hr = pWorkbook->get_Sheets(&sheets);
	if (FAILED(hr))
	{
		// fprintf(stderr, "[CONVERTTOOL]Get Sheets failed\n");
		return false;
	}

	long sheetCount = 0;
	hr = sheets->get_Count(&sheetCount);
	if (FAILED(hr))
	{
		// fprintf(stderr, "[CONVERTTOOL]Get Sheets failed\n");
		return false;
	}

	ks_stdptr<_Worksheet> lastWorkSheet;
	VARIANT index;
	V_VT(&index) = VT_INT;
	V_INT(&index) = sheetCount;
	ks_stdptr<IKCoreObject> dispatch;
	sheets->get_Item(index, &dispatch);
	if (!dispatch)
	{
		// fprintf(stderr, "[CONVERTTOOL]Get Worksheet failed\n");
		return false;
	}

	dispatch->QueryInterface(IID__Worksheet, (void**)&lastWorkSheet);
	if (!lastWorkSheet)
	{
		// fprintf(stderr, "[CONVERTTOOL]Get last Worksheet failed\n");
		return false;
	}

	*pWorkbookOut = lastWorkSheet.detach();
	return true;
}

static WebInt MergeEtDocument(_Application* pApp, const MergeInfo& mergeInfo)
{
	if (mergeInfo.files.count() < 1)
	{
		WEBLOG_ERROR("No valid et documents to merge");
		return XVA_INVALID_ARG;
	}

	QFileInfo destFileInfo(mergeInfo.destFile);
	QString destFileSuffixNoDot = destFileInfo.suffix();
	if (destFileSuffixNoDot.isEmpty())		// 目标文件必须要有后缀
	{
		WEBLOG_ERROR("Result filename should have a suffix");
		return XVA_INVALID_ARG;
	}

	HRESULT hr = E_FAIL;
	VARIANT nullVar;
	V_VT(&nullVar) = VT_EMPTY;

	ks_stdptr<_Workbook> spFirstWorkbook;
	int firstDocIndex = -1;
	QList<MergeFile> filesToJoin;
	DeleteGuard deleteGuard;

	for (int i = 0; i < mergeInfo.files.count(); ++i)
	{
		MergeFile file = mergeInfo.files[i];
		file.filePath = CopyToTempFile(file.filePath);
		deleteGuard.addFile(file.filePath);

		ks_stdptr<_Workbook> spWorkbook;
		if (!ExtractEtDocument(pApp, file.filePath, file.start, file.end, &spWorkbook) || !spWorkbook)
		{
			WEBLOG_ERROR_QSTRING("Failed to extract document: " + file.filePath);
			continue;
		}

		spWorkbook->Save();
		if (firstDocIndex == -1)
		{
			firstDocIndex = i;
			spFirstWorkbook = spWorkbook;
			// if (mergeInfo.destFile.right(4).toLower() == "xlsx")
			// liushengjie: 1.xls + 2.xlsx -> merged.xlsx 会失败，所以这里总是先把第一个文档另存为xlsx，最后再另存
			{
				// 因为是将后面的文档合并到第一个文档，如果后面的文档存在第一个文档不支持的特性，合并就会失败，
				// 所以如果目标文件的格式是xlsx，则第一个文档的格式需要先处理为xlsx，以确保合并过程中不会产生兼容性问题
				file.filePath = file.filePath + ".xlsx";
				deleteGuard.addFile(file.filePath);

				ks_bstr strFileName(krt::utf16(file.filePath));
				VARIANT varFileName;
				V_VT(&varFileName) = VT_BSTR;
				V_BSTR(&varFileName) = strFileName;

				// 显式指定文件格式，否则SaveAs会使用原文件的格式
				VARIANT varFileFormat;
				V_VT(&varFileFormat) = VT_INT;
				V_INT(&varFileFormat) = xlOpenXMLWorkbook;

				hr = spFirstWorkbook->SaveAs(varFileName, varFileFormat, nullVar, nullVar, nullVar, nullVar,
					oldapi::etNoChange, nullVar, nullVar, nullVar, nullVar, FALSE);
				if (FAILED(hr)) {
					WEBLOG_ERROR_QSTRING("Failed to save to " + file.filePath);
					return WO_FAIL;
				}

				// Workbook另存为xlsx后并不能直接使用xlsx的特性，需要重新打开
				hr = spFirstWorkbook->Close(nullVar, nullVar, nullVar);
				if (FAILED(hr))
					return WO_FAIL;

				spFirstWorkbook = NULL;
				if (!openEt(pApp, file.filePath, &spFirstWorkbook))
				{
					WEBLOG_ERROR_QSTRING("Failed to open workbook: " + file.filePath);
					return WO_FAIL;
				}
			}
		}
		else
		{
			spWorkbook->Close(nullVar, nullVar, nullVar);
		}

		filesToJoin += file;
	}

	if (!spFirstWorkbook || firstDocIndex == -1)
	{
		WEBLOG_ERROR("Cannot find a valid document to merge");
		return WO_FAIL;
	}

	for (int i = 0; i < filesToJoin.count(); ++i)
	{
		if (i == firstDocIndex)
			continue;
		
		const MergeFile& file = filesToJoin[i];

		ks_stdptr<_Worksheet> spLastWorksheet;
		if (!GetLastWorksheet(spFirstWorkbook, &spLastWorksheet)) {
			WEBLOG_ERROR("Failed to get last worksheet");
			return WO_FAIL;
		}

		ks_stdptr<_Workbook> spWorkbook;
		if (!openEt(pApp, file.filePath, &spWorkbook))
		{
			WEBLOG_ERROR_QSTRING("Failed to open workbook: " + file.filePath);
			return WO_FAIL;
		}

		ks_stdptr<Sheets> allSheets;
		hr = spWorkbook->get_Sheets(&allSheets);
		if (FAILED(hr))
		{
			WEBLOG_ERROR_QSTRING("Failed to get worksheets: " + file.filePath);
			return WO_FAIL;
		}

		VARIANT before;
		VARIANT after;
		V_VT(&before) = VT_EMPTY;
		V_VT(&after) = VT_UNKNOWN;
		V_UNKNOWN(&after) = spLastWorksheet;
		hr = allSheets->Copy(before, after);
		if (FAILED(hr))
		{
			WEBLOG_ERROR_QSTRING("Copy Sheets failed: " + file.filePath);
			return WO_FAIL;
		}

		spWorkbook->Close(nullVar, nullVar, nullVar);
		QFile::remove(file.filePath);
	}

	// 保存到临时文件，且临时文件与目标文件相同后缀
	QString mergedSaveFile = mergeInfo.destFile + "_merge_save." + destFileSuffixNoDot;
	{
		ks_bstr bstrSaveFile(krt::utf16(mergedSaveFile));
		KComVariant var;
		var.AssignString(bstrSaveFile);
		hr = spFirstWorkbook->SaveCopyAs(var, 0);
		if (FAILED(hr))
		{
			WEBLOG_ERROR_QSTRING("Failed to save to " + mergedSaveFile);
			return WO_FAIL;
		}
		spFirstWorkbook->Close(nullVar, nullVar, nullVar);
	}

	// QFile::copy(filesToJoin[firstDocIndex].filePath, mergeInfo.destFile);
	// QFile::remove(filesToJoin[firstDocIndex].filePath);
	QFile::copy(mergedSaveFile, mergeInfo.destFile);
	QFile::remove(mergedSaveFile);

	WEBLOG_ERROR_QSTRING("Merge file OK: " + mergeInfo.destFile);
	return WO_OK;
}

static HRESULT save_doc(_Workbook* spWorkbook, BSTR path)
{
	LONG nOriginFormat = spWorkbook->GetFileFormat();
	VARIANT varFormat;
	VariantInit(&varFormat);
	V_VT(&varFormat) = VT_I4;
	V_I4(&varFormat) = nOriginFormat;
	
	VARIANT varFileName;
	V_VT(&varFileName) = VT_BSTR;
	V_BSTR(&varFileName) = path;

	VARIANT nullVar = {0};
	return spWorkbook->SaveAs(varFileName, varFormat,
		nullVar, nullVar, nullVar, nullVar, (ETSaveAsAccessMode)xlNoChange, 
		nullVar, nullVar, nullVar, nullVar);
}

static WebInt SplitEtDocument(_Application* pApp, const SplitInfo& splitInfo)
{
	HRESULT hr = E_FAIL;

	// 保存成文件
	// 如果先保存，再打开，则会崩溃，这里直接复制原文件
	QFileInfo fi(splitInfo.srcFile);
	QString dataPath = CopyToTempFile(splitInfo.srcFile);
	// QString dataPath = fi.absolutePath() + "/" + fi.baseName() + "_temp." + fi.suffix();
	// save_doc(doc, ks_bstr(dataPath.utf16()));

	DeleteGuard deleteGuard(dataPath);

	QString destDir = splitInfo.destDir.isEmpty() ? "." : splitInfo.destDir;
	if (splitInfo.type == SplitInfo::SplitAverage)
	{
		int start = splitInfo.splitRangeStart;
		int index = 1;
		int splitRangeEnd = splitInfo.splitRangeEnd;

		do
		{
			ks_stdptr<_Workbook> spWorkbook;
			int sheetCount = 0;
			if (!ExtractEtDocument(pApp, dataPath, start, start + splitInfo.pagePerDoc - 1, &spWorkbook, &sheetCount) || !spWorkbook) {
				WEBLOG_ERROR_QSTRING("Failed to extract document: " + dataPath);
				break;
			}

			if (splitRangeEnd == -1) {
				splitRangeEnd = sheetCount;
			}

			WorkbookGuard workbookGuard(spWorkbook);
			QString destFilePath = destDir + "/" + fi.baseName() + "(" + QString::number(index++) + ")." + fi.suffix();
			QFile::remove(destFilePath);
			
			ks_bstr fileName(krt::utf16(destFilePath));
			hr = save_doc(spWorkbook, fileName);
			if (FAILED(hr))
			{
				WEBLOG_ERROR_QSTRING("Failed to save to: " + destFilePath);
				break;
			}

			start += splitInfo.pagePerDoc;
		} while (start <= splitRangeEnd);
	}
	else
	{
		// log.println("Split to %d ranges", splitInfo.ranges.count());
		int index = 1;
		for (int i = 0; i < splitInfo.ranges.count(); ++i)
		{
			SplitRange sr = splitInfo.ranges[i];
			ks_stdptr<_Workbook> spWorkbook;
			if (!ExtractEtDocument(pApp, dataPath, sr.start, sr.end, &spWorkbook) || !spWorkbook) {
				WEBLOG_ERROR_QSTRING("Failed to extract sheet: " + dataPath + " [" + sr.start + ", " + sr.end + "]");
				continue;
			}

			WorkbookGuard workbookGuard(spWorkbook);
			QString destFilePath = destDir + "/" + fi.baseName() + "(" + QString::number(index++) + ")." + fi.suffix();
			QFile::remove(destFilePath);
			// log.println("Split to %s", destFilePath.toStdString().c_str());
			ks_bstr fileName(krt::utf16(destFilePath));
			hr = save_doc(spWorkbook, fileName);
			if (FAILED(hr))
			{
				WEBLOG_ERROR_QSTRING("Failed to save to: " + destFilePath);
				break;
			}
		}
	}

	WEBLOG_ERROR_QSTRING("Split file OK: " + destDir);

	return WO_OK;
}

WebInt XvaCommand::ExecMergeDocument(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	if (param.type() == typeInvalid)
	{
		WEBLOG_ERROR("Param param is missing");
		return XVA_INVALID_ARG;
	}

	MergeInfo mergeInfo;
	if (!parseMergeDocumentParam(param, mergeInfo))
	{
		WEBLOG_ERROR("Failed to parse merge document param");
		return WO_INVALID_ARG;
	}

	return MergeEtDocument(this->GetCoreApplication(), mergeInfo);
}

WebInt XvaCommand::ExecSplitDocument(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	if (param.type() == typeInvalid)
	{
		WEBLOG_ERROR("Param param is missing");
		return XVA_INVALID_ARG;
	}
	
	SplitInfo splitInfo;
	if (!parseSplitDocumentParam(param, splitInfo))
	{
		WEBLOG_ERROR("Failed to parse split document param");
		return WO_INVALID_ARG;
	}
	
	return SplitEtDocument(this->GetCoreApplication(), splitInfo);
}

WebInt XvaCommand::ExecUpdatePassword(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{ 
    if (!param.has("dest_file"))
	{
		WEBLOG_ERROR("Param dest_file is missing");
        return WO_INVALID_ARG;
	}

	// 如果目标文件已存在，后面的保存会失败，所以这里删除
	QString qstrDestFile = QString::fromUtf16(param.field_str("dest_file"));
	QFile file(qstrDestFile);
	if (file.exists())
		file.remove();

	ks_bstr bstrtargetFileName(param.field_str("dest_file"));
    ks_bstr bstrOpenPassword, bstrEditPassword;
    if (param.has("new_open_password"))
        bstrOpenPassword.assign(param.field_str("new_open_password")); 
    if (param.has("new_edit_password"))
        bstrEditPassword.assign(param.field_str("new_edit_password")); 
    if (param.has("remove_open_password") && param.field_bool("remove_open_password"))
        bstrOpenPassword.assign(__X("")); 
    if (param.has("remove_edit_password") && param.field_bool("remove_edit_password"))
        bstrEditPassword.assign(__X("")); 

    VARIANT vPath;
	V_VT(&vPath) = VT_BSTR;
	V_BSTR(&vPath) = bstrtargetFileName;
    VARIANT var;
    V_VT(&var) = VT_EMPTY;
 
	ks_stdptr<_Workbook> spWorkbook = GetCoreWorkbook();
    spWorkbook->put_Password(bstrOpenPassword);
    spWorkbook->put_WritePassword(bstrEditPassword);

	// 指定为INT，后面ET才会根据后缀来保存对应的格式
	// 如果是empty，ET不会根据后缀来保存格式
	VARIANT vFormat = { 0 };
	V_VT(&vFormat) = VT_I4;
	V_I4(&vFormat) = 0;

    HRESULT hr =  spWorkbook->SaveAs(vPath, vFormat, var,
            var, var, var, etNoChange, var, var, var, var);
	
	if (hr != S_OK)
		WEBLOG_ERROR_QSTRING("Failed to save to: " + QString::fromUtf16(bstrtargetFileName.c_str()));
	
    return hr == 0 ? WO_OK : WO_FAIL;
}

// 表格添加水印，目前使用背景图片来实现，有如下限制：
// * 每个sheet都要添加，新sheet不会自动有水印
// * 仅支持平铺
// * 仅支持图片水印，如果要支持文字水印，需要调用方把文字渲染成图片
// * 不支持图片缩放、旋转，如果需要支持，需要调用方预处理图片
// * 不支持透明度，如果需要支持，需要调用方保证图片有Alpha通道（备注因为是背景图片模拟的，所以水印在文字下方）
WebInt XvaCommand::ExecAddWatermark(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	_Workbook* wb = m_workbook->GetCoreWorkbook();
	if (!wb)
	{
		WEBLOG_ERROR("No active workbook found");
		return WO_FAIL;
	}

	int sheetIdx = -1;
	if (param.has("sheetIdx"))
		sheetIdx = param.field_int32("sheetIdx");
	
	if (!param.has("image"))
	{
		WEBLOG_ERROR("No image specified");
		return WO_INVALID_ARG;
	}

	const WCHAR* szImageFile = param.field_str("image");
	if (!szImageFile || !*szImageFile)
	{
		WEBLOG_ERROR("Invalid image name");
		return WO_INVALID_ARG;
	}

	QString qstrImageFile = QString::fromUtf16(szImageFile);
	if (!QFile(qstrImageFile).exists())
	{
		WEBLOG_ERROR_QSTRING("Image does not exist: " + qstrImageFile);
		return WO_INVALID_ARG;
	}

	IKWorksheets* pWorksheets = wb->GetWorksheets();
	if (!pWorksheets)
		return WO_FAIL;

	ks_bstr bstrImageFile(szImageFile);
	int successCount = 0;
	for (int cnt = pWorksheets->GetSheetCount(), i = 0; i < cnt; ++i)
	{
		if (sheetIdx == -1 || sheetIdx - 1 == i)
		{
			ks_stdptr<_Worksheet> spSheet = pWorksheets->GetSheetItem(i);

			HRESULT hr = spSheet->SetBackgroundPicture(bstrImageFile);
			if (SUCCEEDED(hr))
				successCount++;
		}
	}

	return successCount > 0 ? WO_OK : WO_FAIL;
}

WebInt XvaCommand::ExecJsApi(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{

	if (!param.has("jsStr")) {
		WOLOG_INFO << "[JsApi] Not has jsStr";
		return E_FAIL;
	}

	ks_stdptr<IKEtApplication> spApp = m_workbook->GetCoreApp();
	IKJSIDE* jsIde = spApp->GetJsIDE(TRUE);
	if (!jsIde) {
		WOLOG_ERROR << "[JsApi] JsIDE is null";
		return E_FAIL;
	}

	INT sheetCount = 0;
	m_workbook->GetCoreWorkbook()->GetBook()->GetSheetCount(&sheetCount);
	if (sheetCount <= 0)
		return E_FAIL;

	V8InspectorEvaluateResult evalRes;
	BOOL bRes = jsIde->V8InspectorEvaluate(param.field_str("jsStr"), evalRes);
	HRESULT hr = S_OK;
	if (bRes) {
		WOLOG_INFO << "[JsApi] JsIDE V8 evaluate succeed";
	} else {
		hr = E_FAIL;
		WOLOG_ERROR << "[JsApi] JsIDE V8 evaluate failed";
	}

	writer.beginStruct();
	writer.addInt32Field(evalRes.hr, "status_code");
	if (evalRes.response)
		writer.addStringField(evalRes.response, "response");
	if (evalRes.responseResult)
		writer.addStringField(evalRes.responseResult, "response_result");
	writer.endStruct();
	m_workbook->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_workbook->GetCoreWorkbook());
	return hr;
}
namespace
{
static QString FormatDateTime(double date)
{
	KDateTime dt;
	dt.SetFromDouble(date);
	return QString("%1-%2-%3T%4:%5:%6.%7")
		.arg(dt.GetYear(), 4, 10, QChar('0'))
		.arg(dt.GetMonth(), 2, 10, QChar('0'))
		.arg(dt.GetDay(), 2, 10, QChar('0'))
		.arg(dt.GetHour(), 2, 10, QChar('0'))
		.arg(dt.GetMinute(), 2, 10, QChar('0'))
		.arg(dt.GetSecond(), 2, 10, QChar('0'))
		.arg(0, 3, 10, QChar('0'));
}

static std::string convert_string(const std::string& input)
{
	std::string result;
	for (char c : input)
	{
		if (std::isspace(c))
		{
			result += '_';
			continue;
		}
		result += std::tolower(c);
	}
	return result;
}
}

WebInt XvaCommand::ExecQueryDocProperties(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	ks_stdptr<_Workbook> spWorkbook = m_workbook->GetCoreWorkbook();
	ks_stdptr<IKCoreObject> spCoreObj;
	spWorkbook->get_CustomDocumentProperties(&spCoreObj);
	if (!spCoreObj)
		return E_FAIL;
	ks_stdptr<DocumentProperties> spDocProps = spCoreObj;

	writeDocumentProperties(spDocProps, writer);

	spCoreObj.clear();
	spWorkbook->get_BuiltinDocumentProperties(&spCoreObj);
	if (!spCoreObj)
		return E_FAIL;
	spDocProps.clear();
	spDocProps = spCoreObj;
	writeDocumentProperties(spDocProps, writer);

	return WO_OK;
}

WebInt XvaCommand::writeDocumentProperties(DocumentProperties* props, BinWriter& writer)
{
	long count = 0;
	props->get_Count(&count);
	writer.beginStruct("properties");
	for (long i = 0; i < count; i++)
	{
		KComVariant varIndex(i + 1);
		ks_stdptr<DocumentProperty> spProp;
		if (FAILED(props->get_Item(varIndex, &spProp))) {
			continue;
		}
		writeDocumentProperty(spProp, writer);
	}
	writer.endStruct();
	return WO_OK;
}

WebInt XvaCommand::writeDocumentProperty(DocumentProperty* prop, BinWriter& writer)
{
	ks_bstr bstrName;
	prop->get_Name(&bstrName);
	QString qstrName = QString::fromUtf16(bstrName.c_str());
	QByteArray nameUtf8 = qstrName.toUtf8();
	const char* name = nameUtf8.constData();
	std::string nameStr = convert_string(name);

	KComVariant varValue;
	prop->get_Value(&varValue);

	KsoDocProperties type;
	prop->get_Type(&type);

	switch (type)
	{
		case ksoPropertyTypeNumber:
			writer.addInt32Field(V_INT(&varValue), nameStr.c_str(), -1, true);
			break;
		case ksoPropertyTypeBoolean:
			writer.addBoolField(V_BOOL(&varValue) == VARIANT_TRUE, nameStr.c_str(), -1, true);
			break;
		case ksoPropertyTypeDate:
			writer.addStringField((const WCHAR *)FormatDateTime(V_DATE(&varValue)).c_str16(), nameStr.c_str(), -1, true);
			break;
		case ksoPropertyTypeString:
			if (V_BSTR(&varValue))
				writer.addStringField(V_BSTR(&varValue), nameStr.c_str(), -1, true);
			break;
		case ksoPropertyTypeFloat:
			writer.addFloat32Field(V_R4(&varValue), nameStr.c_str(), -1, true);
			break;
		case ksoPropertyTypeMetaFile:
		default:
			break;
	}
	return WO_OK;
}

WebInt XvaCommand::ExecExtractContent(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
	if (m_workbook == nullptr)
	{
		WEBLOG_ERROR("document is null, please check if the file exists");
		return WO_FAIL;
	}

	ks_stdptr<IETPersist> spEtPersist;
	if (m_workbook->GetCoreApp()->GetAppPersist())
		spEtPersist = m_workbook->GetCoreApp()->GetAppPersist()->GetPersist();
	if (!spEtPersist)
		return WO_FAIL;
	IContentExporter* pExporter = spEtPersist->GetContentExporter();
	if (!pExporter)
		return WO_FAIL;

	if (!param.has("out_file_path"))
	{
		WEBLOG_ERROR("miss parameter \"out_file_path\" ");
		return WO_INVALID_ARG;
	}

	QString outPath = krt::fromUtf16(param.field_str("out_file_path"));
	QFile file(outPath);
	if (!file.open(QFile::WriteOnly | QFile::Truncate))
	{
		WEBLOG_ERROR_QSTRING("open output file failed! " + outPath);
		return WO_INVALID_ARG;
	}

	WEBLOG_ERROR_QSTRING("open output file successed! " + outPath)
	kdc::ExtractOption extractOption;
	if (param.has("element_select_policy"))
	{
		VarObj selectPolicy = param.get("element_select_policy");
		if (selectPolicy.has("selected_element"))
			extractOption.selElement = static_cast<kdc::SelectedElement>(selectPolicy.field_int32("selected_element"));
	}

	if (param.has("workbook_range"))
	{
		VarObj range = param.get("workbook_range");
		if (range.has("sheetFrom"))
			extractOption.sheetFrom = range.field_int32("sheetFrom");
		if (range.has("sheetTo"))
			extractOption.sheetTo = range.field_int32("sheetTo");
		if (range.has("rowFrom"))
			extractOption.rowFrom = range.field_int32("rowFrom");
		if (range.has("rowTo"))
			extractOption.rowTo = range.field_int32("rowTo");
		if (range.has("colFrom"))
			extractOption.colFrom = range.field_int32("colFrom");
		if (range.has("colTo"))
			extractOption.colTo = range.field_int32("colTo");
	}

	if (param.has("workbook_range"))
	{
		VarObj range = param.get("workbook_range");
		if (range.has("sheetFrom"))
			extractOption.sheetFrom = range.field_int32("sheetFrom");
		if (range.has("sheetTo"))
			extractOption.sheetTo = range.field_int32("sheetTo");
		if (range.has("rowFrom"))
			extractOption.rowFrom = range.field_int32("rowFrom");
		if (range.has("rowTo"))
			extractOption.rowTo = range.field_int32("rowTo");
		if (range.has("colFrom"))
			extractOption.colFrom = range.field_int32("colFrom");
		if (range.has("colTo"))
			extractOption.colTo = range.field_int32("colTo");
	}

	QTextStream outStream(&file);
#ifdef _DEBUG
	std::chrono::steady_clock::time_point startTime = std::chrono::steady_clock::now();
	std::chrono::milliseconds extractCost;
#endif
	pExporter->ExportKdcContent(m_workbook->GetCoreWorkbook(), extractOption, outStream);
	file.close();
#ifdef _DEBUG
	extractCost = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - startTime);
	std::cerr << outPath.toStdString() << "elapsed time, write to json:" << extractCost.count() << "ms" << std::endl;

	QFile ckFile(outPath);
	if (!ckFile.open(QFile::ReadOnly | QIODevice::Text))
	{
		std::cerr << "open output file failed!(Check) " << outPath.toStdString() << std::endl;
		return WO_INVALID_ARG;
	}
    QByteArray jsonData = ckFile.readAll();
    ckFile.close();
    QJsonParseError parseError;
    QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonData, &parseError);

    if (parseError.error != QJsonParseError::NoError)
	{
        std::cerr << "Failed to parse JSON:" <<  parseError.errorString().toStdString() << std::endl;
        return WO_FAIL;
    }
    if (!jsonDoc.isObject())
	{
        std::cerr << "JSON is not an object" << std::endl;
        return WO_FAIL;
    }

	QJsonObject jsonObj = jsonDoc.object();

	if (param.has("json_size_path"))
	{
		QString sizePath = krt::fromUtf16(param.field_str("json_size_path"));
		QFile fileSize(sizePath);
		if (fileSize.open(QIODevice::Append | QIODevice::Text)) 
		{
			QTextStream stream(&fileSize);
			stream << outPath << ":\t" << jsonObj.size() << "\n";
			fileSize.close();
		}
	}

	return pExporter->ExportKdcContentCheck(jsonObj);
#endif
	return WO_OK;
}

WebInt XvaCommand::ExecCellImg2FloatImg(const binary_wo::VarObj &root, const VarObj& param, binary_wo::BinWriter &writer)
{
    struct XmlServerWrapper
    {
        XmlServerWrapper()
        {
            m_server.Startup();
        }
        ~XmlServerWrapper()
        {
            m_server.Shutdown();
        }

        OpenXmlServer m_server;
    }_xmlServer;

	_Workbook* wb = m_workbook->GetCoreWorkbook();
	if (!wb)
	{
        WOLOG_ERROR << "No active workbook found";
		return WO_FAIL;
	}

    FILEFORMAT format = wb->GetFileFormat();
    if (format != ffXLSX)
    {
		WOLOG_ERROR << "only xlsx format is supported";
		return WO_FAIL;
    }

    ks_stdptr<IKWorksheets> spSheets = wb->GetWorksheets();
    RANGE rg(wb->GetBook()->GetBMP());
    rg.SetRowFromTo(0, wb->GetBook()->GetBMP()->cntRows-1);
    rg.SetColFromTo(0, wb->GetBook()->GetBMP()->cntCols-1);

    ks_stdptr<IKRanges> spRgs;
    VS(_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRgs));

    for (IDX i = 0; i < spSheets->GetSheetCount(); i++)
    {
        ks_stdptr<IKWorksheet> spSheet = spSheets->GetSheetItem(i);
        BOOL bSheetVisible = TRUE;
        spSheet->GetSheet()->GetVisible(&bSheetVisible);
        if (!bSheetVisible)
            continue;

        rg.SetSheetFromTo(i);
        spRgs->Append(alg::STREF_THIS_BOOK, rg); 

        ks_stdptr<IKShapeRange> spPics;
        if (FAILED(spSheet->CellPic2FloatPic(spRgs, &spPics)))
        {
            WOLOG_ERROR << "ExecCellImg2FloatImg failed";
            return WO_FAIL;
        }
        spRgs->Clear();
    }

    WOLOG_INFO << "ExecCellImg2FloatImg succeed";
    return WO_OK;
}

}
