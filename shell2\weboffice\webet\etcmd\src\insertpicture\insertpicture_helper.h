#ifndef __WEBET_INSERT_PICTURE_HELPER_H__
#define __WEBET_INSERT_PICTURE_HELPER_H__

#include "et_revision_context_impl.h"

class InsertPictureHelper
{
public:
	InsertPictureHelper(_Worksheet*, Shapes*);
	HRESULT Insert(LPCWSTR path, single Left, single Top,
		single width, single height, Shape **Picture);
	HRESULT InsertFloatPictureAsAttachment(PCWSTR filePath, ks_wstring &name, PCWSTR url,single Left, single Top,
		float& width, float& height, BOOL bCompress, IKShape** Picture);
		
private:
	HRESULT _CreatePictureByUrl(IN PCWSTR filePath, IN ks_wstring &name,IN PCWSTR url, OUT INT* pnWidth,
		OUT INT* pnHeight, OUT IKShape** Picture);
	void GetActiveCellLtPoint(int& nL, int& nT);

private:
	_Worksheet * m_pWorksheet;
	Shapes* m_pShapes;
};

namespace PicTfHelper
{
	enum Check_Result
	{
		res_ok,
		res_hasSameLtCell,
		res_ltCellHasContent,
	};

	void CreateShapeRange(IKWorksheet* pWs, IKShapeRange** ppShapeRange);
	Check_Result CheckFloatPictures(IKShapeRange* pShapeRanges, IDX iSheet, wo::KEtRevisionContext* ctx);
	drawing::ElementId PickOneShapeInRange(const RANGE& rg, IKShapeRange* pShapeRanges);
}
#endif //__WEBET_INSERT_PICTURE_HELPER_H__
