﻿#ifndef __WEBET_WEB_MIME_HELPER_H__
#define __WEBET_WEB_MIME_HELPER_H__

#include "webbase/webdeclare.h"
#include "webbase/binvariant/binvarobj.h"
namespace wo
{
class KEtRevisionContext;
class KwCommand;

struct WebMimeHelper
{
	binary_wo::VarObj objParam;
	WebName strField;
	KEtRevisionContext* ctx;
	KwCommand* cmd;

	WebMimeHelper();
	~WebMimeHelper();
	QString resolvePath(WebInt* = nullptr);
	WebMimeData* resolveMimeData();
	QString getId();
	bool addMimeStream(binary_wo::VarObj, WebName);
	binary_wo::VarObj commandConvertMimeStream(KwCommand*, ISerialAcceptor*, WebName, binary_wo::VarObjRoot&);
	void setHasExtra();

private:
	std::vector<std::string> m_tempFileList;
};

} // wo

#endif // __WEBET_WEB_MIME_HELPER_H__
