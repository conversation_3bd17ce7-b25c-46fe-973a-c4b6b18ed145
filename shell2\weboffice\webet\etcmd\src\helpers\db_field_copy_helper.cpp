#include "etstdafx.h"
#include "db_field_copy_helper.h"
#include "uilogic/et_uilogic_basic_ift.h"
#include "uilogic/et_uilogic_global.h"
#include "kfc/et_numfmt_str.h"
#include "persist/persist_helper.h"

#include "binvariant/binreader.h"
#include "database/database.h"
#include "ettools/ettools_encode_decoder.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/db_field_recognize_helper.h"
#include "helpers/picture_upload_helper.h"
#include "utils/attachment_utils.h"
#include "utils/et_gridsheet_utils.h"
#include "utils/sheet_rename_utils.h"
#include "webetlink.h"
#include "util.h"
#include "db/db_basic_itf.h"

extern Callback* gs_callback;


static PCWSTR getCellFormat(IBookOp* pEtBookOp, int sheet, int row, int col)
{
	const XF* pXF = nullptr;
	VS(pEtBookOp->GetCellFormat(sheet, row, col, &pXF, nullptr));
	const NUMFMT* pNumFmt = pXF->pNumFmt;
	return static_cast<PCWSTR>(pNumFmt->fmt);
}

static bool checkHyperLinkWithRuns(IKHyperlinks* pHyperlinks, int row, int col)
{
	if (!pHyperlinks)
		return false;
	ks_stdptr<IKHyperlink> spHyperlink;
	VS(pHyperlinks->HasHyperLinkWithRuns(row, col, &spHyperlink));
	return spHyperlink != nullptr;
}

static HRESULT getTypeByFieldType(IBookOp* pEtBookOp, wo::Database::FieldContext* pContext, RANGE& rg, ET_DbSheet_FieldType& type, wo::Database::FieldType& etType)
{
	wo::util::VALIDATION_Wrap dv;
	int sheetIdx = rg.SheetTo(), row = rg.RowFrom(), col = rg.ColFrom();
	HRESULT hr = pEtBookOp->GetDataValidation(rg, sheetIdx, row, col, &dv, nullptr, nullptr, nullptr);
	if (FAILED(hr))
		return hr;
	wo::Database::FieldsManager* pDbMgr = wo::Database::FieldsManager::Instance();
	wo::Database::IDbField* pField = pDbMgr->IdentifyAll(pContext, rg, dv);
	if (!pField)
		return S_OK;
	etType = pField->GetType();
	switch (pField->GetType())
	{
		case wo::Database::dftCellPicture:
		case wo::Database::dftGenQRLabel:
			type = Et_DbSheetField_Attachment;
			break;
		case wo::Database::dftCheckbox:
			type = Et_DbSheetField_Checkbox;
			break;
		case wo::Database::dftEmail:
			type = Et_DbSheetField_Email;
			break;
		case wo::Database::dftHyperlink:
			type = Et_DbSheetField_Url;
			break;
		case wo::Database::dftList:
			{
				if (dv.pcwcsExtId != 0 && *(dv.pcwcsExtId) != 0)
				{
					ks_stdptr<IValidationExts> spValidationExts;
					pEtBookOp->LeakBook()->GetExtDataItem(edBookDataValidtionExts, (IUnknown **)&spValidationExts);
					ks_stdptr<IValidationExt> spValidationExt = spValidationExts->GetItem(dv.pcwcsExtId);
					if (spValidationExt && spValidationExt->GetType() == ValidationExtType::List)
					{
						ks_stdptr<IValidationListExt> spValidationExtList;
						spValidationExt->QueryInterface(IID_IValidationListExt, (void **)&spValidationExtList);
						if (spValidationExtList != NULL && spValidationExtList->GetMultiSupport())
						{
							type = Et_DbSheetField_MultipleSelect;
							break;
						}
					}
				}
				type = Et_DbSheetField_SingleSelect;
			}
			break;
		case wo::Database::dftPhone:
			type = Et_DbSheetField_Phone;
			break;
		case wo::Database::dftRating:
			type = Et_DbSheetField_Rating;
			break;
		case wo::Database::dftSchedule:
			type = Et_DbSheetField_Complete;
			break;
		case wo::Database::dftID:
			type = Et_DbSheetField_ID;
			break; 
		default:
			break;
	}
	return S_OK;
}

static void correctionForPrimaryField(ET_DbSheet_FieldType& fldType, PCWSTR& numFmt)
{
	switch (fldType)
	{
		case Et_DbSheetField_Complete:
		case Et_DbSheetField_Currency:
		case Et_DbSheetField_Date:
		case Et_DbSheetField_Email:
		case Et_DbSheetField_MultiLineText:
		case Et_DbSheetField_Number:
		case Et_DbSheetField_Percentage:
		case Et_DbSheetField_Phone:
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
		case Et_DbSheetField_Time:
		case Et_DbSheetField_Url:
		case Et_DbSheetField_BarCode:
			break;
		case Et_DbSheetField_Checkbox:
			numFmt = kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_NUM1);
			break;
		case Et_DbSheetField_Rating:
			fldType = Et_DbSheetField_Number;
			// 数字字段格式与默认的 ET_NF_COMBOBOX_NUMBER 不一致
			numFmt = __X("0_ ");
			break;
		default:
			fldType = Et_DbSheetField_MultiLineText;
			numFmt = nullptr;
			break;
	}
}

namespace wo
{
Et2DbFieldCopyHelper::Et2DbFieldCopyHelper(_Workbook* pEtWorkbook, etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet, KEtRevisionContext* ctx, const RANGE& rg)
	: m_spEtApiWb(pEtWorkbook)
	, m_pSrcWorkSheet(pSrcWorkSheet)
	, m_spEtSheet(pSrcWorkSheet->GetSheet())
	, m_pEtBookOp(pEtWorkbook->GetBook()->LeakOperator())
	, m_pCtx(ctx)
	, m_fieldContext(m_spEtApiWb.get(), m_pCtx)
	, m_etRange(rg)

{
	if (pTarWorkSheet)
		VS(DbSheet::GetDBSheetOp(pTarWorkSheet->GetSheet(), &m_spDbSheetOp));
	m_pImages = m_spEtApiWb->GetCellImages();
	m_spEtSheet->GetIndex(&m_etSheetIdx);
	ks_stdptr<IUnknown> spUnk;
	m_spEtSheet->GetExtDataItem(edSheetHyperlinks, &spUnk);
	m_spHyperlinks = spUnk;
	VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
	m_spStringTools->SetEnv(m_spEtSheet);
	m_pcwConnId = m_pCtx->getUser()->connID();
}

void Et2DbFieldCopyHelper::BeginDeduceFieldType()
{
	m_strSet.clear();
	m_iMaxRating = 0;
	m_bHasList = false;
	m_bIsQRLabel = false;
	m_stFieldTypeRecHelper.begin();
	if (m_upRowIter)
		m_upRowIter->Reset();
}

bool Et2DbFieldCopyHelper::Valid()
{
	return m_upRowIter && m_upRowIter->Valid();
}

void Et2DbFieldCopyHelper::Next()
{
	m_upRowIter->Next();
}

int Et2DbFieldCopyHelper::GetCurRow()
{
	return m_upRowIter->GetCurRow();
}

HRESULT Et2DbFieldCopyHelper::DeduceFieldType(int col)
{
	if (!m_upRowIter)
		return E_FAIL;
	int row = m_upRowIter->GetCurRow();
	// 拿单元格的格式
	PCWSTR format = getCellFormat(m_pEtBookOp, m_etSheetIdx, row, col);

	// 拿单元格的token
	const_token_ptr pToken = nullptr;
	HRESULT hr = GridSheet::GetCellToken(m_pEtBookOp, m_etSheetIdx, row, col, GridSheet::Fill, &pToken);
	if (FAILED(hr))
		return hr;
	// TODO（zhangpeng27） PrededuceFieldTypeByDatabaseField
	ET_DbSheet_FieldType curType = ET_DbSheet_FieldType_Invalid;
	Database::FieldType etType = Database::dftInvalid;
	bool isContact = checkHyperLinkWithRuns(m_spHyperlinks, row, col);
	// 只要有@人，就识别为单选项
	if (isContact)
		m_bHasList = true;

	ks_stdptr<etoldapi::Range> spEtApiRange;
	BMP_PTR pBMP = m_pEtBookOp->LeakBook()->GetBMP();
	RANGE etRANGE(pBMP);
	etRANGE.SetCell(m_etSheetIdx, row, col);
	VS(m_pSrcWorkSheet->GetRangeByData(&etRANGE, &spEtApiRange));

	// 优先按database的fieldType进行判断
	hr = _GetTypeByFieldType(etRANGE, spEtApiRange, curType, etType);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IKHyperlink> spHyperlink;
	m_spHyperlinks->HasHyperLink(row, col, &spHyperlink);
	BOOL isImage = m_pImages->IsImgCell(m_etSheetIdx, row, col);
	m_bIsQRLabel = (etType == Database::dftGenQRLabel);
	if (curType != Et_DbSheetField_Attachment && isImage)
		curType = Et_DbSheetField_Attachment;
	else if (curType == Et_DbSheetField_Attachment && (!isImage && !m_bIsQRLabel) && pToken)
		curType = ET_DbSheet_FieldType_Invalid;
	else if (spHyperlink && curType != Et_DbSheetField_Email)
		curType = Et_DbSheetField_Url;
	return m_stFieldTypeRecHelper.addToken(pToken, format, curType, isContact);
}

void Et2DbFieldCopyHelper::EndDeduceFieldType()
{
	m_stFieldTypeRecHelper.SetHasList(m_bHasList);
	m_stFieldTypeRecHelper.end();
}

void Et2DbFieldCopyHelper::CorrectionForPrimaryField(ET_DbSheet_FieldType& fldType, PCWSTR& numFmt)
{
	correctionForPrimaryField(fldType, numFmt);
}

HRESULT Et2DbFieldCopyHelper::CopyFieldExDetail(IDbField* pField, int col, bool bIgnoreChangeFieldType, bool bNeedOptimizeSelectField, bool bNeedAddItem)
{
	switch (pField->GetType())
	{
		case Et_DbSheetField_Rating:
			{
				ks_stdptr<IDbField_Rating> spFieldRating = pField;
            	int maxRating = m_iMaxRating == 0 ? 5 : m_iMaxRating;
            	spFieldRating->SetMaxRating(maxRating);
			}
			break;
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
			{
				if (bNeedOptimizeSelectField)
				{
					// 推断为单选项的字段, 将标题单元格(如果有的话)的下拉列表(如果有的话)选项也纳入 m_strSet
					HRESULT hr = _OptimizeSingleSelectField(m_pSrcWorkSheet, col);
					if (FAILED(hr))
					{
						WOLOG_ERROR << "[Et2DbFieldCopyHelper] Fail in _OptimizeSingleSelectField! col: " << col;
						return hr;
					}
				}
				auto strSet = m_stFieldTypeRecHelper.GetSelectItem();
				m_strSet.insert(strSet.begin(), strSet.end());

				ks_stdptr<IDbField_Select> spFieldSelect = pField;
				int j = 0;
				for (auto it = m_strSet.cbegin(); it != m_strSet.cend(); ++it, ++j)
				{
					spFieldSelect->AppendItem(it->c_str(), DBSelectField::Colors.at(j % DBSelectField::ColorsCnt));
				}
				// 当下拉选项全为空，直接转换为文本类型
				if (spFieldSelect->Count() == 0)
				{
					pField->SetTypeForIO(Et_DbSheetField_MultiLineText);
					if (bIgnoreChangeFieldType == false)
						pField->OnChangeFieldType(Et_DbSheetField_SingleSelect, Et_DbSheetField_MultiLineText, nullptr);
				}
				else if (bNeedAddItem)
				{
					spFieldSelect->SetAllowAddItemWhenInputting(TRUE);
					spFieldSelect->SetAutoAddItem(TRUE);
				} 
			}
			break;
		default:
			break;
	}
	return S_OK;
}

HRESULT Et2DbFieldCopyHelper::_OptimizeSingleSelectField(etoldapi::_Worksheet* pEtWorksheet, int col)
{
    HRESULT hr = S_OK;
    ks_stdptr<etoldapi::Range> spEtApiRange;
    RANGE etRANGE(m_spEtSheet->GetBMP());
    etRANGE.SetCell(m_etSheetIdx, m_etRange.RowFrom(), col);
    VS(pEtWorksheet->GetRangeByData(&etRANGE, &spEtApiRange));

    hr = _AddItems2StrSet(spEtApiRange.get());
    if (FAILED(hr))
        return hr;

    return hr;
}

HRESULT Et2DbFieldCopyHelper::_GetTypeByFieldType(RANGE& rg, etoldapi::Range* pEtApiRange, ET_DbSheet_FieldType& dbType, Database::FieldType& etType)
{
	ET_DbSheet_FieldType fieldType = ET_DbSheet_FieldType_Invalid;
	HRESULT hr = getTypeByFieldType(m_pEtBookOp, &m_fieldContext, rg, fieldType, etType);
	if (FAILED(hr))
		return hr;
	dbType = fieldType;
	switch (fieldType)
	{
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
        {
            hr = _AddItems2StrSet(pEtApiRange);
            if (FAILED(hr))
                return hr;
            m_bHasList = true;
            break;
        }
		case Et_DbSheetField_Rating:
        {
            VALIDATION_INFO dvi;
            dvi.Init();
            m_pEtBookOp->GetDataValidationInfo(rg, rg.SheetTo(), rg.RowFrom(), rg.ColFrom(), &dvi);
            exec_token_vector etv;
            dvi.spFormula1->GetTokenVecForce(&etv);
            const_token_ptr pToken = etv.get(6); // 这里第7个token是最大星星数
            switch (etexec::const_token_assist(pToken).major_type())
            {
                case etexec::ETP_VINT:
                {
                    int val = etexec::const_vint_token_assist(pToken).get_value();
                    if (m_iMaxRating != 0 && m_iMaxRating != val)
                        m_iMaxRating = 5;
                    else
                        m_iMaxRating = val;
                    break;
                }
            }
            break;
        }
		default:
			break;
    }
    return S_OK;
}

HRESULT Et2DbFieldCopyHelper::_AddItems2StrSet(etoldapi::Range* pEtApiRange)
{
    HRESULT hr = S_OK;
    KCOMPTR(Validation) ptrValidation;
    pEtApiRange->get_Validation(&ptrValidation);
    KCOMPTR(IValidationInfo) ptrValidationInfo = ptrValidation;
    std::etvector<BSTR> vecValue;
    hr = ptrValidationInfo->GetCustomListValues(ETStringToolsOpt_None, &vecValue, FALSE);
    if (FAILED(hr))
    {
        WOLOG_ERROR << "[Et2DbFieldCopyHelper] Fail in IValidationInfo::GetCustomListValues";
        return hr;
    }
    for (int i = 0, size = vecValue.size(); i < size; ++i)
    {
		BSTR bstrValue = vecValue.at(i);
        if (bstrValue && __Xc('\0') != *bstrValue)
        {
            m_strSet.insert(GlobalSharedString(bstrValue));
        }
        ::SysFreeString(bstrValue);
    }
    return hr;
}


HRESULT GetAttachmentHandle(ImgInfo info, IDbTokenArrayHandle** ppHandle)
{
	if (!ppHandle)
		return E_FAIL;
	if (!info.fileId || !info.type || info.size == 0)
		return E_INVALIDARG;
	DbSheet_AttachmentSource source = AttachmentSource_upload_ks3;
	if (xstrcmp(info.source, __X("cloud")) == 0)
		source = AttachmentSource_cloud;
	ks_wstring type = __X("image/");
	if (source == AttachmentSource_upload_ks3)
		type += info.type;
	else
		type = info.type;
	QString imgSizeStr;
	if (info.width * info.height != 0)
		imgSizeStr = QString("%1*%2").arg(info.width).arg(info.height);
	ks_stdptr<IDbAttachmentHandle> spAttachmentHandle;
	_db_CreateObject(CLSID_KDbAttachmentHandle, IID_IDbAttachmentHandle, (void**)&spAttachmentHandle);
	HRESULT hr = spAttachmentHandle->Init(info.fileId, source, type.c_str(), info.name, info.size, nullptr, krt::utf16(imgSizeStr));
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbTokenArrayHandle> spTokenArray;
	_db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle, (void**)&spTokenArray);
	alg::managed_handle_token_assist mhta;
	mhta.create(alg::ET_HANDLE_DBATTACHMENT, spAttachmentHandle);
	spTokenArray->Add(mhta.detach());
	*ppHandle = spTokenArray.detach();
	return S_OK;
}

bool ExportImg2Disk(IKBlipAtom* pBlipAtom, PCWSTR dirPath, const std::function<bool(const QString&)>& needSkip, QString& fileName)
{
	if (!pBlipAtom || !dirPath)
		return false;
	QByteArray sha1;
	QByteArray picData;
	if (!PictureUploadHelper::GetPicture(pBlipAtom, sha1, picData))
		return false;
	if (picData.isEmpty())
		return false;
	fileName = sha1;
	if (needSkip(fileName))
		return true;
	QString path = krt::fromUtf16(dirPath);
	// 文件夹不存在的话需要先创建，否则 QFile 无法成功创建文件
	QDir dir(path);
	if (!dir.exists() && !dir.mkpath(path))
		return false;
	QString imagePath = QString("%1/%2").arg(path).arg(fileName);
	QFile file(imagePath);
	if (!file.open(QIODevice::WriteOnly))
		return false;
	// 将 QByteArray 写入文件
	auto res = file.write(picData);
	return res != -1;
}

void Et2DbFieldCopyHelper::Init(PCWSTR pcwFilePath, BOOL bRequestFromApp, VarObj* pParam)
{
	m_pcwFilePath = pcwFilePath;
	// dirPath 增加后缀，否则与导入文件同名无法创建文件夹
	m_wstrDirPathStr = pcwFilePath;
	m_wstrDirPathStr += __X("_img");
	m_bRequestFromApp = bRequestFromApp;
	m_spQrLabelHelperPtr = std::make_shared<util::QRLabelHelper>(m_spEtApiWb, m_pCtx, util::QRLabelHelper::UseType_Import_KSheet2DB, pParam);
}

void Et2DbFieldCopyHelper::attachmentImgHandler(WebSlice* output) {
	binary_wo::BinWriter bw;
	if (m_bRequestFromApp)
		bw.addStringField(m_pCtx->getUser()->userID(), "userId");
	else
		bw.addStringField(m_pcwConnId, "connId");
	bw.beginArray("data");
	for (const auto& attachment : m_stAttachmentMap)
	{
		bw.beginStruct();
		bw.addStringField(attachment.first.c_str(), "key");
		bw.addStringField(attachment.second, "source");
		bw.endStruct();
	}
	bw.endArray();
	m_stAttachmentMap.clear();
	binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice input = {shbt.get(), bw.writeLength()};
	gs_callback->queryShapes(&input, output);
}

void Et2DbFieldCopyHelper::localImgHandler(WebSlice* output) {
	if(!m_pcwFilePath)
		return;
	binary_wo::BinWriter bw;
	bw.addStringField(m_pcwConnId, "connId");
	bw.addStringField(m_wstrDirPathStr.c_str(), "dirPath");
	bw.beginArray("data");
	for (const auto& localImg : m_stLocalImgMap)
	{
		bw.beginStruct();
		bw.addStringField(localImg.first.c_str(), "sha1");
		bw.addStringField(localImg.second, "type");
		bw.endStruct();
	}
	bw.endArray();
	m_stLocalImgMap.clear();
	binary_wo::BinWriter::StreamHolder shbt = bw.buildStream();
	WebSlice input = {shbt.get(), bw.writeLength()};
	gs_callback->convertShapes(&input, output);
}

void Et2DbFieldCopyHelper::batchProcessImg(WebSlice* output, EtDbId fldId) {
	binary_wo::BinReader br(output->data, output->size);
	binary_wo::VarObjRoot root = br.buildRoot();
	binary_wo::VarObj obj = root.cast();
	VarObj data = obj.get_s("data");
	HRESULT hr = S_OK;
	for (int idx = 0, len = data.arrayLength_s(); idx < len; ++idx)
	{
		binary_wo::VarObj itemObj = data.at(idx);
		// status: 状态 0-成功 1-失败
		if (itemObj.field_int32("status") != 0)
			continue;
		PCWSTR key = itemObj.field_str("key");
		if (!key)
			continue;
		ImgInfo imgInfo = {
			.fileId = itemObj.field_str("mapKey"),
			.source = itemObj.field_str("source"),
			.type = itemObj.field_str("type"),
			.name = itemObj.field_str("name"),
			.width = itemObj.field_uint32("width"),
			.height = itemObj.field_uint32("height"),
			.size = itemObj.field_uint32("size")
		};
		ks_stdptr<IDbTokenArrayHandle> spTokenArray;
		hr = GetAttachmentHandle(imgInfo, &spTokenArray);
		if (FAILED(hr))
			continue;
		GlobalSharedString fileId(key);
		alg::managed_handle_token_assist tokenAssist;
		tokenAssist.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
		const auto& recIds = m_stPendingImgPos[fileId];
		for (auto recId : recIds)
			VS(m_spDbSheetOp->SetTokenValue(recId, fldId, tokenAssist));
		if (!recIds.empty())
		{
			const_token_ptr pCacheToken = nullptr;
			VS(m_spDbSheetOp->GetValueToken(recIds.front(), fldId, &pCacheToken));
			m_imgInfoCache[fileId] = pCacheToken;
		}
		m_stPendingImgPos.erase(fileId);
	}
	gs_callback->onFreeWebSlice(output);
}

void Et2DbFieldCopyHelper::batchProcessAttachmentImg(EtDbId fldId)
{
	WebSlice output = {nullptr, 0};
	attachmentImgHandler(&output);
	batchProcessImg(&output, fldId);
}

void Et2DbFieldCopyHelper::batchProcessLocalImg(EtDbId fldId)
{
	WebSlice output = {nullptr, 0};
	localImgHandler(&output);
	batchProcessImg(&output, fldId);
}

bool Et2DbFieldCopyHelper::needSkip(const GlobalSharedString& fileId, EtDbId recId, EtDbId fldId) 
{
	auto itImgCache = m_imgInfoCache.find(fileId);
	// 已处理的图片，直接使用对应 token 赋值即可
	if (itImgCache != m_imgInfoCache.end())
	{
		const_token_ptr pToken = itImgCache->second;
		if (pToken)
			VS(m_spDbSheetOp->SetTokenValue(recId, fldId, pToken));
		return true;
	}
	// 同一附件只需请求一次
	auto itPendingImg = m_stPendingImgPos.find(fileId);
	if (itPendingImg != m_stPendingImgPos.cend())
	{
		itPendingImg->second.emplace_back(recId);
		return true;
	}
	return false;
}

HRESULT Et2DbFieldCopyHelper::BatchCopyAttachmentFieldData(int row, int col, EtDbId recId, EtDbId fldId)
{
	if(m_bIsQRLabel)
	{
		ks_wstring attachmentId;
		if(m_spQrLabelHelperPtr && m_spQrLabelHelperPtr->GetUploadQRLabelImgAttachmentId(m_spEtSheet->GetStId(), row, col, attachmentId))
		{
			PCWSTR source = __X("upload_ks3");
			GlobalSharedString fileId(attachmentId.c_str());
			m_stPendingImgPos[fileId].emplace_back(recId);
			m_stAttachmentMap.emplace_back(fileId, source);
		}
	}
	else
	{
		drawing::AbstractShape* pShape = m_pImages->GetImgCellAtom(m_etSheetIdx, row, col);
		IKBlipAtom* pBlipAtom = PictureUploadHelper::GetBlipAtom(pShape);
		if (!pBlipAtom)
			return S_FALSE;
		if (PictureUploadHelper::IsAttachment(pBlipAtom, pShape->picture().blip().linkMode()))
		{
			ks_bstr imgUrl;
			pBlipAtom->GetLinkPath(&imgUrl);
			PCWSTR source = nullptr;
			PCWSTR id = util::getFileId(imgUrl.c_str(), &source);
			if (!id || !source)
				return S_FALSE;
			GlobalSharedString fileId(id);
			if (needSkip(fileId, recId, fldId))
				return S_FALSE;
			m_stPendingImgPos[fileId].emplace_back(recId);
			m_stAttachmentMap.emplace_back(fileId, source);
		}
		else
		{
			QString fileName;
			PCWSTR type = PictureUploadHelper::GetUploadFormat(pBlipAtom);
			if (!type)
				return S_FALSE;
			bool bSkip = false;
			bool saveImg = m_pcwFilePath && ExportImg2Disk(pBlipAtom, m_wstrDirPathStr.c_str(), [&](const QString& sha) {
				GlobalSharedString id(krt::utf16(sha));
				bSkip = needSkip(id, recId, fldId);
				return bSkip;
			}, fileName);
			if (!saveImg || bSkip)
				return S_FALSE;
			GlobalSharedString fileId(krt::utf16(fileName));
			m_stLocalImgMap.emplace_back(fileId, type);
			m_stPendingImgPos[fileId].emplace_back(recId);
		}
	}
	if (m_stAttachmentMap.size() == m_queryBatchSize)
		batchProcessAttachmentImg(fldId);
	if (m_stLocalImgMap.size() == m_uploadBatchSize)
		batchProcessLocalImg(fldId);
	return S_OK;
}

HRESULT Et2DbFieldCopyHelper::BeforeCopyQRLabelImg(int col)
{
	BMP_PTR pBMP = m_pEtBookOp->LeakBook()->GetBMP();
	RANGE range(pBMP);
	range.SetSheetFromTo(m_etSheetIdx);
	range.SetRowFromTo(0, pBMP->cntRows -1);
	range.SetColFromTo(col, col);
	m_spQrLabelHelperPtr->ConvertQRLabel2CellImg(range);
	return S_OK;
}

void Et2DbFieldCopyHelper::ClearAttachmentCache()
{
	m_stAttachmentMap.clear();
	m_stLocalImgMap.clear();
}

void Et2DbFieldCopyHelper::BeginCopyData(bool bIsAttachmentType, int col)
{
	ASSERT(m_spDbSheetOp);
	ClearAttachmentCache();
	if(bIsAttachmentType && m_bIsQRLabel)
		BeforeCopyQRLabelImg(col);
	if (m_upRowIter)
		m_upRowIter->Reset();
}

void Et2DbFieldCopyHelper::CopyCellData(IDbField* pField, EtDbId recId, EtDbId fldId, int col, bool bIsAttachmentType)
{
	const int row = m_upRowIter->GetCurRow();
	bool isImage = alg::BOOL2bool(m_pImages->IsImgCell(m_etSheetIdx, row, col));
	HRESULT hr = S_OK;
	// 单元格图片或二维码数据只能拷贝到附件类型字段上
	if ((isImage || m_bIsQRLabel) ^ bIsAttachmentType)
		return;

	switch (pField->GetType())
	{
		case Et_DbSheetField_Attachment:
			hr = BatchCopyAttachmentFieldData(row, col, recId, fldId);
			break;
		case Et_DbSheetField_MultiLineText:
		case Et_DbSheetField_BarCode:
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
			{
				ks_bstr bstrVal;
				GridSheet::GetCellText(m_spStringTools, m_pSrcWorkSheet, row, col, GridSheet::Fill, &bstrVal);
				PCWSTR strVal = __X("");
				if(!bstrVal.empty())
					strVal = bstrVal.c_str();
				// 识别@人，将@人前的@去掉
				bool isContact = checkHyperLinkWithRuns(m_spHyperlinks, row, col);
				if (isContact && strVal[0] == __Xc('@') && bstrVal.size() > 0)
					strVal++;
				m_spDbSheetOp->SetValue(recId, fldId, strVal);
			}
			break;
		case Et_DbSheetField_Url:
			{
				const_token_ptr pToken = nullptr;
				VS(GridSheet::GetCellToken(m_pEtBookOp, m_etSheetIdx, row, col, GridSheet::Fill, &pToken));
				ks_bstr displayText;
				GridSheet::GetCellText(m_spStringTools, m_pSrcWorkSheet, row, col, GridSheet::Fill, &displayText);
				if (displayText.empty())
					break;
				ks_bstr address;
				ks_stdptr<IKHyperlink> spHyperlink;
				m_spHyperlinks->HasHyperLink(row, col, &spHyperlink);
				if (spHyperlink)
					spHyperlink->GetAddress(&address);
				etexec::managed_token_assist hyperlinkToken;
				IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
				hr = pCtx->Text2DbHyperlinkToken(displayText.c_str(), (!address || address.empty()) ? displayText.c_str() : address.c_str(), &hyperlinkToken);
				if (FAILED(hr) || !hyperlinkToken)
					break;
				VS(m_spDbSheetOp->SetTokenValue(recId, fldId, hyperlinkToken));
			}
			break;
		case Et_DbSheetField_Phone:
			{
				const_token_ptr pToken = nullptr;
				VS(GridSheet::GetCellToken(m_pEtBookOp, m_etSheetIdx, row, col, GridSheet::Fill, &pToken));
				etexec::managed_token_assist stringTokenAssist;
				if (GetExecTokenMajorType(pToken) != alg::ETP_VSTR)
				{
					VS(etexec::V_ToText(pToken, &stringTokenAssist));
					pToken = stringTokenAssist;
				}

				VS(m_spDbSheetOp->SetTokenValue(recId, fldId, pToken));
			}
			break;
		case Et_DbSheetField_Email:
			{
				PCWSTR val = __X("");
				ks_bstr address;
				ks_bstr displayText;
				ks_stdptr<IKHyperlink> spHyperlink;
				m_spHyperlinks->HasHyperLink(row, col, &spHyperlink);
				if (spHyperlink)
					spHyperlink->GetAddress(&address);
				if (!address.empty())
					val = address.c_str();
				else
				{
					GridSheet::GetCellText(m_spStringTools, m_pSrcWorkSheet, row, col, GridSheet::Fill, &displayText);
					if (displayText)
						val = displayText.c_str();
				}
				hr = m_spDbSheetOp->SetHyperlinkAddress(recId, fldId, val);
				if (FAILED(hr))
					break;
			}
			// fallthrough
		default:
			{
				const_token_ptr pToken = nullptr;
				VS(GridSheet::GetCellToken(m_pEtBookOp, m_etSheetIdx, row, col, GridSheet::Fill, &pToken));
				VS(m_spDbSheetOp->SetTokenValue(recId, fldId, pToken));
			}
			break;
	}
}

void Et2DbFieldCopyHelper::BatchProcessLeftImg(EtDbId fldId)
{
	if (!m_stAttachmentMap.empty())
		batchProcessAttachmentImg(fldId);
	if (!m_stLocalImgMap.empty())
		batchProcessLocalImg(fldId);
}

void Et2DbFieldCopyHelper::EndCopyData(EtDbId fldId)
{
	BatchProcessLeftImg(fldId);
}

HRESULT Et2DbFieldCopyHelper::SetFieldName(IDbField* pField, int row, int col, PCWSTR pcwDefaultName, bool bIsSkipCell)
{
	ks_bstr bstrVal;
	PCWSTR pcwName = pcwDefaultName;
	if (!bIsSkipCell)
	{
		GridSheet::GetCellText(m_spStringTools, m_pSrcWorkSheet, row, col, GridSheet::Fill, &bstrVal);
		if(!bstrVal.empty())
			pcwName = bstrVal.c_str();
	}
	return pField->SetName(pcwName, TRUE, TRUE);
}

HRESULT Et2DbFieldCopyHelper::CheckDataValidation(IBookOp* pEtBookOp, wo::Database::FieldContext* pContext, RANGE& rg, ET_DbSheet_FieldType& type, wo::Database::FieldType& etType)
{
	return getTypeByFieldType(pEtBookOp, pContext, rg, type, etType);
}
}  // namespace wo