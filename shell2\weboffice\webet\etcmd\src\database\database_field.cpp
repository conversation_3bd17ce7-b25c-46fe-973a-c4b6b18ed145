﻿#include "etstdafx.h"
#include "database_field.h"
#include "database_field_context.h"
#include "database_field_modifier.h"
#include "database_field_identifier.h"
#include "database_field_initialiser.h"
#include "kfc/et_numfmt_str.h"

namespace wo
{
namespace Database
{

// ================== FieldDefault ==================
HRESULT FieldDefault::Set(FieldContext *pContext, const RANGE &rg)
{
    for (auto it = m_modifiers.begin(); it != m_modifiers.end(); ++it)
    {
        IFieldModifier *pModifier = it->get();
        if (pModifier == nullptr)
        {
            ASSERT(FALSE);
            continue;
        }
        HRESULT hr = pModifier->CheckPermission(pContext, rg);
        if (FAILED(hr))
            return hr;
        hr = pModifier->Set(pContext, rg);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT FieldDefault::Clear(FieldContext *pContext, const RANGE &rg)
{
    pContext->SetClear(true);
    for (auto it = m_modifiers.begin(); it != m_modifiers.end(); ++it)
    {
        IFieldModifier *pModifier = it->get();
        if (pModifier == nullptr)
        {
            ASSERT(FALSE);
            continue;
        }
        HRESULT hr = pModifier->CheckPermission(pContext, rg);
        if (FAILED(hr))
            return hr;
        hr = pModifier->Clear(pContext, rg);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT FieldDefault::Initialise(FieldContext *pContext, const RANGE &rg)
{
    for (auto it = m_initialisers.begin(); it != m_initialisers.end(); ++it)
    {
        IFieldInitialiser *pInitialiser = it->get();
        if (pInitialiser == nullptr)
        {
            ASSERT(FALSE);
            continue;
        }
        HRESULT hr = pInitialiser->CheckPermission(pContext, rg);
        if (FAILED(hr))
            return hr;
        hr = pInitialiser->Initialise(pContext, rg);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

BOOL FieldDefault::Identify(FieldContext *pContext, const RANGE &rg, VALIDATION dv)
{
    if (m_identifiers.empty())
        return FALSE;

    for (auto it = m_identifiers.begin(); it != m_identifiers.end(); ++it)
    {
        IFieldIdentifier *pIdentifier = it->get();
        if (pIdentifier == nullptr)
        {
            ASSERT(FALSE);
            continue;
        }
        if (!pIdentifier->Identify(pContext, rg, dv))
            return FALSE;
    }
    return TRUE;
}

void FieldDefault::addModifier(IFieldModifier *modifier)
{
    m_modifiers.emplace_back(modifier);
}

void FieldDefault::addIdentifier(IFieldIdentifier *identifier)
{
    m_identifiers.emplace_back(identifier);
}

void FieldDefault::addInitialiser(IFieldInitialiser *initialiser)
{
    m_initialisers.emplace_back(initialiser);
}
// ================== FieldDefault ==================

DateField::DateField()
{
    // MODIFIER
    addModifier(new FieldUserDefinedNumberFormatModifier(NFCat_Date));
    addModifier(new FieldValidationModifier(dvvtDate, __X("1"), dvoGreaterEqual, TRUE, dvesStop));
    addModifier(new FieldCentreAlignedXFModifier());
    // IDENTIFIER
    addIdentifier(new FieldValidationTypeIdentifier(dvvtDate));
}

TimeField::TimeField()
{
    // MODIFIER
    addModifier(new FieldUserDefinedNumberFormatModifier(NFCat_Time));
    addModifier(new FieldValidationModifier(dvvtTime, __X("0"), dvoGreaterEqual, TRUE, dvesStop));
    addModifier(new FieldCentreAlignedXFModifier());
    // IDENTIFIER
    addIdentifier(new FieldValidationTypeIdentifier(dvvtTime));
}

NumberField::NumberField()
{
    // MODIFIER
    addModifier(new FieldUserDefinedNumberFormatModifier(NFCat_Number));
    addModifier(new FieldValidationModifier(dvvtDecimal, __X("-9999999999"), dvoGreaterEqual, TRUE, dvesStop));
    // IDENTIFIER
    addIdentifier(new FieldValidationTypeIdentifier(dvvtDecimal));
    addIdentifier(new FieldNumberFormatIdentifier(NFCat_Number, TRUE));
}

CurrencyField::CurrencyField()
{
    // MODIFIER
    addModifier(new FieldUserDefinedNumberFormatModifier(NFCat_Currency));
    addModifier(new FieldValidationModifier(dvvtDecimal, __X("-9999999999"), dvoGreaterEqual, TRUE, dvesStop));
    // IDENTIFIER
    addIdentifier(new FieldValidationTypeIdentifier(dvvtDecimal));
    addIdentifier(new FieldNumberFormatIdentifier(NFCat_Currency, TRUE));
}

PercentageField::PercentageField()
{
    // MODIFIER
    addModifier(new FieldUserDefinedNumberFormatModifier(NFCat_Percentate));
    addModifier(new FieldValidationModifier(dvvtDecimal, __X("-9999999999"), dvoGreaterEqual, TRUE, dvesStop));
    // IDENTIFIER
    addIdentifier(new FieldValidationTypeIdentifier(dvvtDecimal));
    addIdentifier(new FieldNumberFormatIdentifier(NFCat_Percentate, TRUE));
}

TextField::TextField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_TEXT1));
    addModifier(new FieldValidationModifier(dvvtFormula, __X("=IF(TRUE,TRUE,\"Text\")")));
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationIdentifier(__X("=IF(TRUE,TRUE,\"Text\")")));
    addIdentifier(new FieldNumberFormatIdentifier(NFCat_Text, TRUE));
}

IDField::IDField()
{
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_TEXT1));
    addModifier(new FieldValidationModifier(dvvtFormula, __X("=IF(TRUE,TRUE,\"ID\")"), dvoBetween, TRUE, dvesStop));
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationIdentifier(__X("=IF(TRUE,TRUE,\"ID\")")));
}

PhoneField::PhoneField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_TEXT1));
    addModifier(new FieldValidationModifier(dvvtFormula, __X("=IF(TRUE,TRUE,\"Tel\")"), dvoBetween, TRUE, dvesStop));
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationIdentifier(__X("=IF(TRUE,TRUE,\"Tel\")")));
}

EmailField::EmailField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_TEXT1));
    addModifier(new FieldValidationModifier(dvvtFormula, __X("=IF(TRUE,TRUE,\"Email\")"), dvoBetween, TRUE, dvesInfo));
    addModifier(new FieldHyperlinkModifier());
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationIdentifier(__X("=IF(TRUE,TRUE,\"Email\")")));
}

HyperlinkField::HyperlinkField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_TEXT1));
    addModifier(new FieldValidationModifier(dvvtFormula, __X("=IF(TRUE,TRUE,\"Web\")"), dvoBetween, TRUE, dvesInfo));
    addModifier(new FieldHyperlinkModifier());
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationIdentifier(__X("=IF(TRUE,TRUE,\"Web\")")));
}

CheckboxField::CheckboxField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(__X("[=1]\"\u2611\";[=0]\"\u2610\";0;@")));
    addModifier(new FieldRefFormulaValidationModifier(__X("=IF(TRUE,OR(%1=0,%1=1),\"Checkbox\")"), TRUE, dvesStop));
    addModifier(new FieldCheckboxModifier());
    addModifier(new FieldCentreAlignedXFModifier());
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationRegexIdentifier(__X("^=IF\\(TRUE,OR\\([A-Z0-9]+=[01],[A-Z0-9]+=[01]\\),\"Checkbox\"\\)$")));
    // INITIALISER
    addInitialiser(new FieldFormulaInitialiser(__X("0")));
}

ListField::ListField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_G));
	addModifier(new FieldListValidationSyncUpdateFrontModifier());
    addModifier(new FieldListValidationModifier(TRUE, dvesStop));
	addModifier(new FieldListValidationSyncUpdateBackModifier());
    // IDENTIFIER
    addIdentifier(new FieldValidationTypeIdentifier(dvvtCustomList));
}

RatingField::RatingField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_G));
    addModifier(new FieldRatingValidationModifier(__X("=IF(TRUE,AND(%1>=0,%1<=%2,INT(%1)=%1),\"Rating\")"), TRUE, dvesStop));
    addModifier(new FieldRatingConditionFormatModifier());
    addModifier(new FieldFontColourXFModifier(EtColor::MakeARGB(0xFF, 0xC0, 0x13)));
    addModifier(new FieldCentreAlignedXFModifier());
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationRegexIdentifier(__X("^=IF\\(TRUE,AND\\([A-Z0-9]+>=0,[A-Z0-9]+<=[0-9]+,INT\\([A-Z0-9]+\\)=[A-Z0-9]+\\),\"Rating\"\\)$")));
    // INITIALISER
    addInitialiser(new FieldFormulaInitialiser(__X("0")));
}

ScheduleField::ScheduleField()
{
    addModifier(new FieldNumberFormatModifier(__X("0%")));
    addModifier(new FieldRefFormulaValidationModifier(__X("=IF(TRUE,AND(%1>=0,%1<=1),\"Complete\")"), TRUE, dvesStop));
    addModifier(new FieldDatabarConditionFormatModifier(EtColor::MakeARGB(0xE2, 0xE6, 0xED)));
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationRegexIdentifier(__X("^=IF\\(TRUE,AND\\([A-Z0-9]+>=0,[A-Z0-9]+<=1\\),\"(Schedule|Complete)\"\\)$")));
}

CellPictureField::CellPictureField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_G));
    addModifier(new FieldRefFormulaValidationModifier(__X("=IF(TRUE,LEFT(%1,9)=\"=DISPIMG(\",\"DispImg\")"), TRUE, dvesStop));
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationRegexIdentifier(__X("^=IF\\(TRUE,LEFT\\([A-Z0-9]+,9\\)=\"=DISPIMG\\(\",\"DispImg\"\\)$")));
}

ScanField::ScanField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_G));
    addModifier(new FieldValidationModifier(dvvtFormula, __X("=IF(TRUE,TRUE,\"QRCodeInput\")")));
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationIdentifier(__X("=IF(TRUE,TRUE,\"QRCodeInput\")")));
}

AIField::AIField()
{
    // MODIFIER
    addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_G));
    //这里就是用来填充的
    addModifier(new FieldAIFormulaFillModifier(__X("=%1(%2,\"%3\")"), FALSE, dvesStop));
    addModifier(new FieldAIFormulaValidationModifier(__X("=IF(TRUE,IF(ISFORMULA(%1),LEFT(FORMULATEXT(%1),6)=\"=WPSAI\",FALSE),%2)"), TRUE, dvesStop));
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationRegexIdentifier(__X("^=IF\\(TRUE,IF\\(ISFORMULA\\([A-Z0-9]+\\),LEFT\\(FORMULATEXT\\([A-Z0-9]+\\),6\\)=\"=WPSAI\",([\\s\\S]*)+\\)$")));
}

GenQRLabelField::GenQRLabelField()
{
    // 设置一整列的数字格式很慢，单元格数字格式也不影响二维码的显示，所以这里就不修改数字格式了
    //addModifier(new FieldNumberFormatModifier(Et_BUILDIN_NF_G));
    // 表单在创建二维码列时很多数据还没准备好，这个时候填充的二维码公式获取不到二维码图片
    //addModifier(new FieldGenQRLabelFormulaFillModifier());
    addModifier(new FieldGenQRLabelFormulaValidationModifier());
    // IDENTIFIER
    addIdentifier(new FieldFormulaValidationRegexIdentifier(__X("^=IF\\(TRUE,IF\\(ISFORMULA\\([A-Z0-9]+\\),LEFT\\(FORMULATEXT\\([A-Z0-9]+\\),11\\)=\"=GENQRLABEL\",([\\s\\S]*)+\\)$")));
}
} // Database
} // wo
