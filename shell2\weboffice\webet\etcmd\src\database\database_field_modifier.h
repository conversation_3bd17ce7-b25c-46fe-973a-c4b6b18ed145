﻿#ifndef __WEBET_DATABSE_FIELD_MODIFIER_H__
#define __WEBET_DATABSE_FIELD_MODIFIER_H__

#include "database_def.h"

namespace wo
{
namespace Database
{

class FieldValidationModifier : public IFieldModifier
{
public:
    FieldValidationModifier(DVValueType type,
                            PCWSTR formula,
                            DV_OPERATOR op = dvoBetween,
                            BOOL fShowErrorMsg = FALSE,
                            DVErrorStyle errStyle = dvesInfo);

    virtual ~FieldValidationModifier() {}
    virtual HRESULT Set(FieldContext *, const RANGE &) override;
    virtual HRESULT Clear(FieldContext *, const RANGE &) override;
    virtual HRESULT CheckPermission(FieldContext *pContext, const RANGE &) override;
protected:
    void InitDv();
    HRESULT SetInner(FieldContext *, const RANGE &, BSTR);
    HRESULT getCellRefStr(ROW row, COL col, ks_bstr &bstr);
    HRESULT getColRefStr(COL col, ks_bstr &bstr);
    VALIDATION m_dv; 
private:
    ks_bstr m_formula;
};

class FieldTemplateFormulaValidationModifier : public FieldValidationModifier
{
public:
    FieldTemplateFormulaValidationModifier(PCWSTR formulaTemplate,
                                            BOOL fShowErrorMsg = FALSE,
                                            DVErrorStyle errStyle = dvesInfo);
protected:
    QString m_formulaTemplate;
};

class FieldRefFormulaValidationModifier : public FieldTemplateFormulaValidationModifier
{
public:
    FieldRefFormulaValidationModifier(PCWSTR formulaTemplate,
                                    BOOL fShowErrorMsg = FALSE,
                                    DVErrorStyle errStyle = dvesInfo);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
};

class FieldAIFormulaValidationModifier : public FieldRefFormulaValidationModifier
{
public:
    FieldAIFormulaValidationModifier(PCWSTR formulaTemplate,
                                    BOOL fShowErrorMsg = FALSE,
                                    DVErrorStyle errStyle = dvesInfo);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
};

class FieldAIFormulaFillModifier : public FieldRefFormulaValidationModifier
{
public:
    FieldAIFormulaFillModifier(PCWSTR formulaTemplate,
                                    BOOL fShowErrorMsg = FALSE,
                                    DVErrorStyle errStyle = dvesInfo);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
};

class FieldGenQRLabelFormulaValidationModifier : public FieldRefFormulaValidationModifier
{
public:
    FieldGenQRLabelFormulaValidationModifier();
    virtual HRESULT Set(FieldContext *, const RANGE &) override;
};

class FieldGenQRLabelFormulaFillModifier : public FieldRefFormulaValidationModifier
{
public:
    FieldGenQRLabelFormulaFillModifier();
    virtual HRESULT Set(FieldContext *, const RANGE &) override;
private:
    QString GenerateQRFormula(ROW row, const GenQRLabelCol& qrCol);
};

class FieldRatingValidationModifier : public FieldRefFormulaValidationModifier
{
public:
    FieldRatingValidationModifier(PCWSTR formulaTemplate,
                                    BOOL fShowErrorMsg = FALSE,
                                    DVErrorStyle errStyle = dvesInfo);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
};

class FieldMultipleListValidationModifier : public FieldTemplateFormulaValidationModifier
{
public:
    FieldMultipleListValidationModifier(BOOL fShowErrorMsg = FALSE,
                                        DVErrorStyle errStyle = dvesInfo);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
};

class FieldListValidationModifier : public FieldValidationModifier
{
public:
    FieldListValidationModifier(BOOL fShowErrorMsg = FALSE,
                                DVErrorStyle errStyle = dvesInfo);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
protected:
    HRESULT setInner(FieldContext *env, const RANGE &, BSTR);
    void InitValidationExt(binary_wo::VarObj vObj, IValidationExt** ppExt);
    HRESULT isValidRange(FieldContext* pContext, RANGE& rg);
};

class FieldListValidationSyncUpdateBase : public IFieldModifier
{
protected:
	enum SyncUpdateActionType
	{
		CHECK, 
		REPLACE
	};
	HRESULT setInner(FieldContext *, const RANGE &, SyncUpdateActionType);
};

class FieldListValidationSyncUpdateFrontModifier : public FieldListValidationSyncUpdateBase
{
public:
	FieldListValidationSyncUpdateFrontModifier() {}
	virtual HRESULT Set(FieldContext *, const RANGE &) override;
	virtual HRESULT Clear(FieldContext *, const RANGE &) override { return S_OK; }
	virtual HRESULT CheckPermission(FieldContext *pContext, const RANGE &) override;
};

class FieldListValidationSyncUpdateBackModifier : public FieldListValidationSyncUpdateBase
{
public:
	FieldListValidationSyncUpdateBackModifier() {}
	virtual HRESULT Set(FieldContext *, const RANGE &) override;
	virtual HRESULT Clear(FieldContext *, const RANGE &) override { return S_OK; }
	virtual HRESULT CheckPermission(FieldContext *pContext, const RANGE &) { return S_OK; };
};

class FieldNumberFormatModifier : public IFieldModifier
{
public:
    FieldNumberFormatModifier(PCWSTR format);
    FieldNumberFormatModifier(int nfIndex);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
    virtual HRESULT Clear(FieldContext *, const RANGE &) override;
    virtual HRESULT CheckPermission(FieldContext *, const RANGE &) override
    {
        // KAppCoreRange::SetXF内判断
        return S_OK;
    }
protected:
    FieldNumberFormatModifier();
    HRESULT setInner(FieldContext *env, const RANGE &, BSTR);
private:
    ks_bstr m_format;
};

class FieldUserDefinedNumberFormatModifier : public FieldNumberFormatModifier
{
public:
    FieldUserDefinedNumberFormatModifier(NumFmtCat nfc);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
private:
    NumFmtCat m_nfc;
};

class FieldXFModifier : public IFieldModifier
{
public:
    FieldXFModifier();

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
    virtual HRESULT Clear(FieldContext *, const RANGE &) override;
    virtual HRESULT CheckPermission(FieldContext *, const RANGE &) override
    {
        // KAppCoreRange::SetXF内判断
        return S_OK;
    }
protected:
    KXF m_XF;
};

class FieldCentreAlignedXFModifier : public FieldXFModifier
{
public:
    FieldCentreAlignedXFModifier();
};

class FieldFontColourXFModifier : public FieldXFModifier
{
public:
    FieldFontColourXFModifier(DWORD argb);
};

class FieldConditionFormatModifier : public IFieldModifier
{
public:
    virtual HRESULT Clear(FieldContext *, const RANGE &) override;
    virtual HRESULT CheckPermission(FieldContext *pContext, const RANGE &) override;
};

class FieldDatabarConditionFormatModifier : public FieldConditionFormatModifier
{
public:
    FieldDatabarConditionFormatModifier(DWORD argb);

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
private:
    EtColor m_clrFill;
};

class FieldRatingConditionFormatModifier : public FieldConditionFormatModifier
{
public:
    FieldRatingConditionFormatModifier();

    virtual HRESULT Set(FieldContext *, const RANGE &) override;
private:
    HRESULT SetConditionFormatInner(etoldapi::FormatConditions *, int, int, int);
    QString ConstructRatingString(int nBlack, int nWhite);
};

class FieldCellValuesModifier : public IFieldModifier
{
protected:
    HRESULT GetClippedRANGE(FieldContext *, RANGE &);
};

class FieldCheckboxModifier : public FieldCellValuesModifier
{
public:
    virtual HRESULT Set(FieldContext *, const RANGE &) override;
    virtual HRESULT Clear(FieldContext *, const RANGE &) override;
    virtual HRESULT CheckPermission(FieldContext *pContext, const RANGE &rg) override;
private:
    HRESULT SetInner(FieldContext *, const RANGE &, bool);
};

class FieldFormulaModifier : public FieldCellValuesModifier
{
public:
    virtual HRESULT Set(FieldContext *, const RANGE &) override;
    virtual HRESULT Clear(FieldContext *, const RANGE &) override;
    virtual HRESULT CheckPermission(FieldContext *pContext, const RANGE &rg) override;
};

class FieldHyperlinkModifier : public IFieldModifier
{
public:
    virtual HRESULT Set(FieldContext *, const RANGE &) override;
    virtual HRESULT Clear(FieldContext *, const RANGE &) override;
    virtual HRESULT CheckPermission(FieldContext *pContext, const RANGE &rg) override;
};

} // Database
} // wo

#endif // __WEBET_DATABSE_FIELD_MODIFIER_H__