﻿#ifndef __WEBET_DATABASE_SHEET_TASK_CLASS_H__
#define __WEBET_DATABASE_SHEET_TASK_CLASS_H__

#include "et_task_class.h"

namespace wo
{
// =======================dbsheet task class======================= //
class TaskExecDbtBookRecalculateLegacy : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbtBookRecalculateLegacy(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	bool SerialCommand(KwCommand* cmd, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecKSheetShareSheet: public ETDbSheetTaskClassBase
{
public:
	TaskExecKSheetShareSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecKSheetCancelShareSheet: public ETDbSheetTaskClassBase
{
public:
	TaskExecKSheetCancelShareSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecImportWorkbookLegacy : public ETDbSheetTaskClassBase
{
public:
    TaskExecImportWorkbookLegacy(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;

protected:
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
};

class TaskExecSetCustomStorage : public ETDbSheetTaskClassBase
{
public:
    TaskExecSetCustomStorage(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;

protected:
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
private:
	HRESULT HandleBookInfo(const binary_wo::VarObj& param, ks_stdptr<ICustomStorageManager>& spCustomStorMgr);
    HRESULT HandleSheetInfo(const binary_wo::VarObj& param, ks_stdptr<ICustomStorageManager>& spCustomStorMgr);
};

class TaskExecDbSheetsAddLegacy : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbSheetsAddLegacy(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbSheetsAdd : public TaskExecDbSheetsAddLegacy
{
public:
	explicit TaskExecDbSheetsAdd(KEtWorkbook* pBook) : TaskExecDbSheetsAddLegacy(pBook) {};
	PCWSTR GetTag() override { return __X("dbsheets.add"); };
};

class TaskExecDbSheetsMoveLegacy : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSheetsMoveLegacy(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecDbSheetsMove : public TaskExecDbSheetsMoveLegacy
{
public:
	TaskExecDbSheetsMove(KEtWorkbook* pBook) : TaskExecDbSheetsMoveLegacy(pBook) {};
	PCWSTR GetTag() override { return __X("dbsheets.move"); };
};

class TaskExecDbSheetsCopyLegacy : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSheetsCopyLegacy(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecDbSheetsCopy : public TaskExecDbSheetsCopyLegacy
{
public:
	TaskExecDbSheetsCopy(KEtWorkbook* pBook) : TaskExecDbSheetsCopyLegacy(pBook) {};
	PCWSTR GetTag() override { return __X("dbsheets.copy"); };
};

class TaskExecCopyAppSheet: public ETDbSheetTaskClassBase
{
public:
	TaskExecCopyAppSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecCopyDashBoardSheet: public ETDbSheetTaskClassBase
{
public:
    TaskExecCopyDashBoardSheet(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecDbSheetSetNameLegacy : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSheetSetNameLegacy(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecDbSheetSetName : public TaskExecDbSheetSetNameLegacy
{
public:
	TaskExecDbSheetSetName(KEtWorkbook* pBook) : TaskExecDbSheetSetNameLegacy(pBook) {};
	PCWSTR GetTag() override { return __X("dbsheet.setName"); };
};

class TaskExecDbSheetDelLegacy : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSheetDelLegacy(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecDbSheetDel : public TaskExecDbSheetDelLegacy
{
public:
	TaskExecDbSheetDel(KEtWorkbook* pBook) : TaskExecDbSheetDelLegacy(pBook) {};
	PCWSTR GetTag() override { return __X("dbsheet.delSheet"); };
};

class TaskExecDbSheetsDel : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbSheetsDel(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecDbPrepareCurUser : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbPrepareCurUser(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　不用限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbEmpty : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbEmpty(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　不用限制
	}
	PCWSTR GetTag() override;
private:
	void OnDateChange();
	void OnStatSheetChange();
};

class TaskExecDbConvertCurUserFilterToAnyUserFilter : public ETDbSheetTaskClassBase
{
public:
    TaskExecDbConvertCurUserFilterToAnyUserFilter(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
};

class TaskExecDbInsertRecords : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbInsertRecords(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
private:
	HRESULT FillGroupedValuesWrap(IDBSheetView* pSheetView, IDBSheetRange* pDBRange, UINT recCnt, EtDbId srcRecId);
};


class TaskExecDbRemoveRecords : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRemoveRecords(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbSetParentRecord : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetParentRecord(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbSetViewOption : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetViewOption(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbSetCellHyperlink : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetCellHyperlink(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbSetCellValue : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetCellValue(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	bool NeedSetHyperlink(IDbField*, IDBSheetOp *, EtDbId, EtDbId, PCWSTR, bool);
	bool NeedSetHyperlinkTokenAddress(IDbField*, IDBSheetOp *, EtDbId, EtDbId, PCWSTR, bool, OUT BSTR*);
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbSetRangeValues : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetRangeValues(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbSetCellPic : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetCellPic(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbModifyWebExtension : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbModifyWebExtension(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
    bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
    {
        return false; // 允许输入公式
    }
private:
	HRESULT ModifyDBChartSetting(const binary_wo::VarObj&, IKWebExtension*, KEtRevisionContext*, EtDbId);
};

class TaskExecDbAddWebExtension : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbAddWebExtension(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT InitDBChartSetting(const binary_wo::VarObj&, IKWebExtension*, IDBChartStatisticMgr*, KEtRevisionContext*);
    HRESULT InitWorksheetChartSetting(const binary_wo::VarObj& param, IKWebExtension* pWebExtension, IDBDashBoardDataOp* pDashBoardData);
};

class TaskExecDbDeleteWebExtension : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbDeleteWebExtension(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	int GetShapeOpChangeType() const override;
};

class TaskExecDbBatchModifyWebExtension : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbBatchModifyWebExtension(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbAddField : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbAddField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT handleField(IDBSheetView* spDbSheetView, IDBSheetViews* spDbSheetViews, const VarObj& param, IDBSheetRange *spDbRange);
};

class TaskExecDbRemoveFields : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRemoveFields(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbClearRange : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbClearRange(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbMoveRange : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbMoveRange(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbMoveGroup : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbMoveGroup(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbRemoveGroup : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRemoveGroup(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
};

class TaskExecDbModifyField : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbModifyField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetFieldHidden : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetFieldHidden(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetFieldWidth : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetFieldWidth(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetRecordsHeight : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetRecordsHeight(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbCreateView: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbCreateView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbCopyView: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbCopyView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbDeleteView: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbDeleteView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbRenameView: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRenameView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetViewDescription: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetViewDescription(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetViewNotice: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetViewNotice(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetPersonalView : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetPersonalView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetSheetDescription: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetSheetDescription(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetSheetIcon: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetSheetIcon(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetStatistic: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetStatistic(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbRemoveStatistic: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRemoveStatistic(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbMoveView: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbMoveView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetUngroupedPositionOption : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetUngroupedPositionOption(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbAddGroupCondition : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbAddGroupCondition(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbRemoveGroupCondition : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRemoveGroupCondition(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetGroupPriority : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetGroupPriority(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbChangeGroupField : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbChangeGroupField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetGroupAscending : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetGroupAscending(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbAddSortCondition : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbAddSortCondition(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetAutoSort : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetAutoSort(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbExecSort : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbExecSort(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
};

class TaskExecDbRemoveSortCondition : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRemoveSortCondition(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetSortPriority : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetSortPriority(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbChangeSortField : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbChangeSortField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetSortAscending : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetSortAscending(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetFiltersOp : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetFiltersOp(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetFilterCriteria : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetFilterCriteria(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbRemoveFilter : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRemoveFilter(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbClearFilters : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbClearFilters(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetGridFrozenCount : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetGridFrozenCount(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbRangePaste : public ETDbSheetTaskClassBase
{
	enum ExpandType
	{
		ExpandType_Skip,	// 粘贴到可见，只改写已有的记录/字段，超出的不粘贴。
		ExpandType_Expand,	// 粘贴到可见，超出的时插入新的记录/字段来扩展。
		ExpandType_Modify,	// 粘贴到可见，只做修改，超出时，整体执行失败。
		ExpandType_Specific,// 粘贴到指定位置，无论可见与否，超出部分不粘贴。
	};

public:
	TaskExecDbRangePaste(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
private:
    // 粘贴db内部格式
    HRESULT PasteJsonData(KwCommand* cmd, KEtRevisionContext* ctx, ks_wstring& mimeData);
    // 粘贴txt
    HRESULT PasteText(KwCommand* cmd, KEtRevisionContext* ctx);
public:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; //　内部限制
	}
	PCWSTR GetTag() override;
private:
	HRESULT CheckAndGetGroupRange(ks_stdptr<IDBSheetView>& spDbSheetView, 
								EtDbIdx tarBegRow, size_t srcRowCnt, 
								ks_stdptr<IDBSheetRange>& spTarRg, 
								ks_stdptr<IDBSheetRange>& groupAddRange);
	HRESULT CheckAndSavePasteInfo(ks_stdptr<IDBSheetView>& spDbSheetView, 
					ks_stdptr<IDBSheetRange>& spTarRg, 
					binary_wo::VarObj& param, KEtRevisionContext* ctx, 
					size_t srcRowCnt, size_t srcColCnt,
					BOOL bCurNoRecords, std::unordered_set<EtDbId>& newRecordIds);
	HRESULT CheckAndExpandRange(
		binary_wo::VarObj binOpt,
		ks_stdptr<IDBSheetView>& spDbSheetView, 
		ks_stdptr<IDBSheetRange>& spTarRg,
		size_t& srcRowCnt, size_t& srcColCnt,
        std::unordered_set<EtDbId>& newRecordIds);
	HRESULT getExpandType(PCWSTR, ExpandType& type);
    
    using GetStringFunc = std::function<void(UINT, UINT, ks_wstring&)>;
	bool CheckDatasourceMatchUniqueToPasteSpecField(IDBSheetView* pDbSheetView, bool autoFillingRange,
		size_t srcRowCnt, size_t srcColCnt,
		size_t fillRowCnt, size_t fillColCnt,
		IDBSheetRange* pTarRg, GetStringFunc func, OUT EtDbId& dupFldId);
	HRESULT ClearTarRgWithUniqueProp(IDBSheetView* pDbSheetView,
		size_t srcRowCnt, size_t srcColCnt,
		IDBSheetRange* pTarRg);
};

class TaskExecDbUserGroupAddUsers : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbUserGroupAddUsers(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbUserGroupRemoveUsers : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbUserGroupRemoveUsers(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbUserGroupSetProtection : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbUserGroupSetProtection(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbProtectionSwitch : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbProtectionSwitch(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetupProtection : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetupProtection(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbShareView : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbShareView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbCancelShareView : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbCancelShareView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbShareSheet : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbShareSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbCancelShareSheet: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbCancelShareSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecDbModifyShareView : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbModifyShareView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbAddTemplateSheets : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbAddTemplateSheets(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbImportXlsxSheets : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbImportXlsxSheets(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbImportCsv : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbImportCsv(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbImportDbSheets : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbImportDbSheets(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbAppendData : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbAppendData(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbAddSyncSheets : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbAddSyncSheets(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) final;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) final;
	PCWSTR GetTag() final;
};

class TaskExecDbUnsyncSheets : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbUnsyncSheets(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) final;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) final;
	PCWSTR GetTag() final;
};

class TaskExecCleanUnusedSyncSheets : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecCleanUnusedSyncSheets(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) final;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) final;
	PCWSTR GetTag() final;
};

class TaskExecUpdateSyncInfoSheets : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecUpdateSyncInfoSheets(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) final;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) final;
	PCWSTR GetTag() final;
};

class AddMergeSyncDbSheetsAdapter;
class TaskExecDbAddSyncMergeSheet : public ETDbSheetTaskClassBase
{
public:
	explicit TaskExecDbAddSyncMergeSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	void fillResObj(const AddMergeSyncDbSheetsAdapter& addSheetsHelper);
};

class TaskExecDbClearTransactions : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbClearTransactions(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetViewCoverField : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetViewCoverField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
protected:
	HRESULT setCoverField(binary_wo::VarObj& param, IDBSheetView* pView);
};

class TaskExecDbSetViewCoverDispType : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetViewCoverDispType(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
protected:
	HRESULT setCoverDispType(binary_wo::VarObj& param, IDBSheetView* pView);
};

class TaskExecDbSetKanbanCoverField : public TaskExecDbSetViewCoverField
{
public:
	TaskExecDbSetKanbanCoverField(KEtWorkbook*);
	PCWSTR GetTag() override;
};

class TaskExecDbSetKanbanCoverDispType : public TaskExecDbSetViewCoverDispType
{
public:
	TaskExecDbSetKanbanCoverDispType(KEtWorkbook*);
	PCWSTR GetTag() override;
};

class TaskExecDbSetGalleryCoverField : public TaskExecDbSetViewCoverField
{
public:
	TaskExecDbSetGalleryCoverField(KEtWorkbook*);
	PCWSTR GetTag() override;
};

class TaskExecDbSetGalleryCoverDispType : public TaskExecDbSetViewCoverDispType
{
public:
	TaskExecDbSetGalleryCoverDispType(KEtWorkbook*);
	PCWSTR GetTag() override;
};

class TaskExecDbSetFieldNameVisible : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetFieldNameVisible(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetDbLayout : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetDbLayout(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetTimerTask : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetTimerTask(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbFetchUserInfoDone : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbFetchUserInfoDone(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbUpdateUserOrganizeInfo : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbUpdateUserOrganizeInfo(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetFormFieldOption : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetFormFieldOption(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbassignTimeLine : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbassignTimeLine(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbConfigureGanttView : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbConfigureGanttView(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetViewDisplayFromType : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetViewDisplayFromType(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

//这个接口已经废弃，后续前端最好不要调用此命令，转向调用下面的TaskExecDbSetQueryFieldIds，但由于为了保证旧版本的命令回放正常，故此保留之。
class TaskExecDbSetQueryFieldIds : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetQueryFieldIds(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetQueryFields : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetQueryFields(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetQueryConditionCanBlank : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetQueryConditionCanBlank(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetQueryNeedSecondCheck : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetQueryNeedSecondCheck(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetQueryCriteria : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetQueryCriteria(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecUpdateAppSheetData : public ETDbSheetTaskClassBase
{
public:
	TaskExecUpdateAppSheetData(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbOnAfterDeleteApp : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbOnAfterDeleteApp(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbConvertToEtSheet : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbConvertToEtSheet(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT _doConvertViewDataToEtSheet(binary_wo::VarObj& param);
};

class TaskExecDbConvertFromEtSheet: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbConvertFromEtSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecUpdateAppVersion: public ETDbSheetTaskClassBase
{
public:
	TaskExecUpdateAppVersion(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecResetAppSheetSharedIds: public ETDbSheetTaskClassBase
{
public:
	TaskExecResetAppSheetSharedIds(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbRangeFill: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRangeFill(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetRangeXf: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetRangeXf(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbClearRangeXf: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbClearRangeXf(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetFieldTitleFormat: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetFieldTitleFormat(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbRemoveFieldTitleFormat: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbRemoveFieldTitleFormat(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetViewBackgroundImage: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetViewBackgroundImage(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbClearTempSettings: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbClearTempSettings(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSyncTempSettings: public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSyncTempSettings(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSaveAsNewViewByTempSettings: public TaskExecDbCopyView
{
public:
	TaskExecDbSaveAsNewViewByTempSettings(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetComposeFilter : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetComposeFilter(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT checkCompose(const binary_wo::VarObj&);
};

class TaskExecDbPermissionsUpgrade : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbPermissionsUpgrade(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT SetPermissionContent(binary_wo::VarObj& param, IDBProtection*, IDBProtection*);
	HRESULT AddCreatorCol(binary_wo::VarObj& param, ISheet*, EtDbId*);
	HRESULT SetCriterionProtection(IDBSheetProtection*,ISheet*, EtDbId);
	HRESULT SerializeUserPermission(KDBProtectionType& param, PCWSTR, ISerialAcceptor&);
};

class TaskExecDbSetCalendarCriteria : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetCalendarCriteria(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbConfigureCalendarView : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbConfigureCalendarView(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbAssignCalendarTimeLine : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbAssignCalendarTimeLine(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecModidyStatSheetSetting: public ETDbSheetTaskClassBase
{
public:
	TaskExecModidyStatSheetSetting(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecActivateStatSheet: public ETDbSheetTaskClassBase
{
public:
	TaskExecActivateStatSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecRecoverStatSheet2NormalDbSheet: public ETDbSheetTaskClassBase
{
public:
	TaskExecRecoverStatSheet2NormalDbSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecDbAddSidebarFolder : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbAddSidebarFolder(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbDeleteSidebarFolder : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbDeleteSidebarFolder(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

private:
	HRESULT EnumDbSidebarFolderSheets(IDbSidebarFolderTreeManager* pMgr, EtDbId targetId, 
									std::vector<UINT>& folderStIdVec);
	HRESULT EnumDbSidebarFolderFolders(IDbSidebarFolderTreeManager* pMgr, EtDbId targetId,
									std::vector<EtDbId>& folderIdVec);
	HRESULT DeleteSheetsInFolder(KEtRevisionContext* pCtx, const std::vector<UINT>& folderStIdVec);
};

class TaskExecDbMoveSidebarTree : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbMoveSidebarTree(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbSetNameSidebarFolder : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbSetNameSidebarFolder(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecDbCommonLogInit : public ETDbSheetTaskClassBase
{
public:
	TaskExecDbCommonLogInit(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};
} // namespace wo

#endif
