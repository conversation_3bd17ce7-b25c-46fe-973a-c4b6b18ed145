#include "etstdafx.h"
#include "wo/bw_helper.h"
#include "wo/sa_helper.h"
#include "wo/core_stake.h"
#include "condition_format_wrapper.h"

#include "applogic/et_apihost.h"
#include "kso/api/smartparam.h"
#include "kso/l10n/et/et_appcore.h"


int XlConditionValueTypes_To_ColorStyleMinMaxIdx(XlConditionValueTypes type)
{
	switch (type)
	{
	case xlConditionValueLowestValue:
	case xlConditionValueHighestValue       : return 0;
	case xlConditionValueNumber             : return 1;
	case xlConditionValuePercent            : return 2;
	case xlConditionValueFormula            : return 3;
	case xlConditionValuePercentile         : return 4;
	default: ASSERT (FALSE); break;
	}
	return 0;
}

int XlConditionValueTypes_To_ColorStyleMidIdx(XlConditionValueTypes type)
{
	switch (type)
	{
	case xlConditionValueNumber       : return 0;
	case xlConditionValuePercent      : return 1;
	case xlConditionValueFormula      : return 2;
	case xlConditionValuePercentile   : return 3;
	default: ASSERT (FALSE); break;
	}
	return 0;
}

int XlConditionValueTypes_To_DataBarMinMaxIdx(XlConditionValueTypes type)
{
	switch (type)
	{
	case xlConditionValueHighestValue:
	case xlConditionValueLowestValue       : return 0;
	case xlConditionValueNumber            : return 1;
	case xlConditionValuePercent           : return 2;
	case xlConditionValueFormula           : return 3;
	case xlConditionValuePercentile        : return 4;
	case xlConditionValueAutomaticMin:
	case xlConditionValueAutomaticMax      : return 5;
	default: ASSERT(FALSE); break;
	}
	return 0;
}

int XlIconSet_To_IconSetsIdx(XlIconSet type)
{
	switch (type)
	{
	case xl3Arrows        : return 0;
	case xl3ArrowsGray    : return 1;
	case xl3Flags         : return 2;
	case xl3TrafficLights1: return 3;
	case xl3TrafficLights2: return 4;
	case xl3Signs         : return 5;
	case xl3Symbols       : return 6;
	case xl3Symbols2      : return 7;
	case xl3Stars         : return 8;
	case xl3Triangles     : return 9;
	case xl4Arrows        : return 10;
	case xl4ArrowsGray    : return 11;
	case xl4RedToBlack    : return 12;
	case xl4CRV           : return 13;
	case xl4TrafficLights : return 14;
	case xl5Arrows        : return 15;
	case xl5ArrowsGray    : return 16;
	case xl5CRV           : return 17;
	case xl5Quarters      : return 18;
	case xl5Boxes         : return 19;
	default: ASSERT (FALSE); break;
	}
	return 0;
}


XlIconSet String_To_XlIconSet(const WebStr str)
{
	if (xstrcmp(str, __X("xl3Arrows")) == 0) return xl3Arrows;
	else if (xstrcmp(str, __X("xl3ArrowsGray")) == 0) return xl3ArrowsGray;
	else if (xstrcmp(str, __X("xl3Flags")) == 0) return xl3Flags;
	else if (xstrcmp(str, __X("xl3TrafficLights1")) == 0) return xl3TrafficLights1;
	else if (xstrcmp(str, __X("xl3TrafficLights2")) == 0) return xl3TrafficLights2;
	else if (xstrcmp(str, __X("xl3Signs")) == 0) return xl3Signs;
	else if (xstrcmp(str, __X("xl3Symbols")) == 0) return xl3Symbols;
	else if (xstrcmp(str, __X("xl3Symbols2")) == 0) return xl3Symbols2;
	else if (xstrcmp(str, __X("xl3Stars")) == 0) return xl3Stars;
	else if (xstrcmp(str, __X("xl3Triangles")) == 0) return xl3Triangles;
	else if (xstrcmp(str, __X("xl4Arrows")) == 0) return xl4Arrows;
	else if (xstrcmp(str, __X("xl4ArrowsGray")) == 0) return xl4ArrowsGray;
	else if (xstrcmp(str, __X("xl4RedToBlack")) == 0) return xl4RedToBlack;
	else if (xstrcmp(str, __X("xl4CRV")) == 0) return xl4CRV;
	else if (xstrcmp(str, __X("xl4TrafficLights")) == 0) return xl4TrafficLights;
	else if (xstrcmp(str, __X("xl5Arrows")) == 0) return xl5Arrows;
	else if (xstrcmp(str, __X("xl5ArrowsGray")) == 0) return xl5ArrowsGray;
	else if (xstrcmp(str, __X("xl5CRV")) == 0) return xl5CRV;
	else if (xstrcmp(str, __X("xl5Quarters")) == 0) return xl5Quarters;
	else if (xstrcmp(str, __X("xl5Boxes")) == 0) return xl5Boxes;
	else return xl3Arrows;
}

WebStr XlIconSet_To_String(XlIconSet is)
{
	switch (is)
	{
		case xl3Arrows: return __X("xl3Arrows");
		case xl3ArrowsGray: return __X("xl3ArrowsGray");
		case xl3Flags: return __X("xl3Flags");
		case xl3TrafficLights1: return __X("xl3TrafficLights1");
		case xl3TrafficLights2: return __X("xl3TrafficLights2");
		case xl3Signs: return __X("xl3Signs");
		case xl3Symbols: return __X("xl3Symbols");
		case xl3Symbols2: return __X("xl3Symbols2");
		case xl3Stars: return __X("xl3Stars");
		case xl3Triangles: return __X("xl3Triangles");
		case xl4Arrows: return __X("xl4Arrows");
		case xl4ArrowsGray: return __X("xl4ArrowsGray");
		case xl4RedToBlack: return __X("xl4RedToBlack");
		case xl4CRV: return __X("xl4CRV");
		case xl4TrafficLights: return __X("xl4TrafficLights");
		case xl5Arrows: return __X("xl5Arrows");
		case xl5ArrowsGray: return __X("xl5ArrowsGray");
		case xl5CRV: return __X("xl5CRV");
		case xl5Quarters: return __X("xl5Quarters");
		case xl5Boxes: return __X("xl5Boxes");
		default: return __X("xl3Arrows");
	}
}

int XlAboveBelow_To_AverageIdx(XlAboveBelow op, int nDev)
{
	switch (op)
	{
	case xlAboveAverage     : return 0;
	case xlBelowAverage     : return 1;
	case xlEqualAboveAverage: return 2;
	case xlEqualBelowAverage: return 3;
	case xlAboveStdDev      : return 2 + nDev * 2;
	case xlBelowStdDev      : return 3 + nDev * 2;
	default: ASSERT (FALSE); break;
	}
	return 0;
}

ETFormatConditionType Idx_To_ETFormatConditionType(int listIdx, int subIdx)
{
	if (0 == listIdx)
	{
		switch (subIdx)
		{
		case 0:
		case 1: return etColorScale;
		case 2: return etDatabar;
		case 3: return etIconSets;
		default: ASSERT(FALSE); break;
		}
	}
	else if (1 == listIdx)
	{
		switch (subIdx)
		{
		case 0: return etCellValue;
		case 1: return etTextString;
		case 2: return etTimePeriod;
		case 3: return etBlanksCondition;
		case 4: return etNoBlanksCondition;
		case 5: return etErrorsCondition;
		case 6: return etNoErrorsCondition;
		default: ASSERT(FALSE); break;
		}
	}
	else if (2 == listIdx)
	{
		return etTop10;
	}
	else if (3 == listIdx)
	{
		return etAboveAverageCondition;
	}
	else if (4 == listIdx)
	{
		return etUniqueValues;
	}
	else if (5 == listIdx)
	{
		return etExpression;
	}

	ASSERT (FALSE);
	return etCellValue;
}

XlIconSet IconSetsIdx_To_XlIconSet(int idx)
{
	switch (idx)
	{
	case 0 : return xl3Arrows;
	case 1 : return xl3ArrowsGray;
	case 2 : return xl3Flags;
	case 3 : return xl3TrafficLights1;
	case 4 : return xl3TrafficLights2;
	case 5 : return xl3Signs;
	case 6 : return xl3Symbols;
	case 7 : return xl3Symbols2;
	case 8 : return xl3Stars;
	case 9 : return xl3Triangles;
	case 10: return xl4Arrows;
	case 11: return xl4ArrowsGray;
	case 12: return xl4RedToBlack;
	case 13: return xl4CRV;
	case 14: return xl4TrafficLights;
	case 15: return xl5Arrows;
	case 16: return xl5ArrowsGray;
	case 17: return xl5CRV;
	case 18: return xl5Quarters;
	case 19: return xl5Boxes;
	default: ASSERT(FALSE); break;
	}
	return xl3Arrows;
}

XlConditionValueTypes ColorStyleMinMaxIdx_To_XlConditionValueTypes(int idx, bool bMin)
{
	switch (idx)
	{
	case 0:
		if (bMin)
			return xlConditionValueLowestValue;
		else
			return xlConditionValueHighestValue;
	case 1: return xlConditionValueNumber;
	case 2: return xlConditionValuePercent;
	case 3: return xlConditionValueFormula;
	case 4: return xlConditionValuePercentile;
	default: ASSERT(FALSE); break;
	}
	return xlConditionValueNone;
}

XlConditionValueTypes ColorStyleMidIdx_To_XlConditionValueTypes(int idx)
{
	switch (idx)
	{
	case 0: return xlConditionValueNumber;
	case 1: return xlConditionValuePercent;
	case 2: return xlConditionValueFormula;
	case 3: return xlConditionValuePercentile;
	default: ASSERT(FALSE); break;
	}
	return xlConditionValueNone;
}

XlConditionValueTypes DataBarMinMaxIdx_To_XlConditionValueTypes(int idx, bool bMin)
{
	switch (idx)
	{
	case 0:
		if (bMin)
			return xlConditionValueLowestValue;
		else
			return xlConditionValueHighestValue;
	case 1: return xlConditionValueNumber;
	case 2: return xlConditionValuePercent;
	case 3: return xlConditionValueFormula;
	case 4: return xlConditionValuePercentile;
	case 5:
		if (bMin)
			return xlConditionValueAutomaticMin;
		else
			return xlConditionValueAutomaticMax;
	default: ASSERT(FALSE); break;
	}
	return xlConditionValueNone;
}

int XlConditionValueTypes_To_IconSetIdx(XlConditionValueTypes type)
{
	switch (type)
	{
	case xlConditionValueNumber       : return 0;
	case xlConditionValuePercent      : return 1;
	case xlConditionValueFormula      : return 2;
	case xlConditionValuePercentile   : return 3;
	default: ASSERT(FALSE); break;
	}
	return 0;
}

XlConditionValueTypes IconSetIdx_To_XlConditionValueTypes(int idx)
{
	switch (idx)
	{
	case 0: return xlConditionValueNumber;
	case 1: return xlConditionValuePercent;
	case 2: return xlConditionValueFormula;
	case 3: return xlConditionValuePercentile;
	default: ASSERT(FALSE); break;
	}
	return xlConditionValueNone;
}

XlAboveBelow AverageIdx_To_XlAboveBelow(int idx, int &nDev)
{
	nDev = (idx - 2) / 2;
	switch (idx)
	{
	case 0: return xlAboveAverage;
	case 1: return xlBelowAverage;
	case 2: return xlEqualAboveAverage;
	case 3: return xlEqualBelowAverage;
	case 4:
	case 6:
	case 8: return xlAboveStdDev;
	case 5:
	case 7:
	case 9: return xlBelowStdDev;
	default: ASSERT(FALSE); break;
	}
	return xlAboveAverage;
}

XlTimePeriods Idx_To_XlTimePeriods(int idx)
{
	switch (idx)
	{
	case 1: return xlToday;
	case 0: return xlYesterday;
	case 3: return xlLast7Days;
	case 5: return xlThisWeek;
	case 4: return xlLastWeek;
	case 7: return xlLastMonth;
	case 2: return xlTomorrow;
	case 6: return xlNextWeek;
	case 9: return xlNextMonth;
	case 8: return xlThisMonth;
	default: ASSERT (FALSE); break;
	}
	return xlToday;
}

int XlTimePeriods_To_Idx(XlTimePeriods per)
{
	switch (per)
	{
	case xlToday    :return 1;
	case xlYesterday:return 0;
	case xlLast7Days:return 3;
	case xlThisWeek :return 5;
	case xlLastWeek :return 4;
	case xlLastMonth:return 7;
	case xlTomorrow :return 2;
	case xlNextWeek :return 6;
	case xlNextMonth:return 9;
	case xlThisMonth:return 8;
	default: ASSERT (FALSE);
	}
	return 0;
}

XlIcon Idx_To_XlIcon(int idx)
{
	XlIcon icon = xlIconNoCellIcon;
	if (idx > 0)
		icon = (XlIcon)idx;
	return icon;
}

int XlIcon_To_Idx(XlIcon icon)
{
	if (xlIconNoCellIcon == icon)
		return 0;
	else
		return icon;
}

void GetKXFFromItem(Style *pStyle, CondFormatItem *pItem)
{
	if (pStyle == nullptr)
		return;

	pItem->ensureKXF();
	pItem->pKXF->reset();
	ks_stdptr<IFormatHost> spFormat = pStyle;
	const XF* pXF = nullptr;
	spFormat->GetXF(&pItem->pKXF->mask, &pXF);
	pItem->pKXF->setXF(pXF);
}

QString TrimCellText(QString str)
{
	if (str.mid(0, 2) == QString("=\"") && str.endsWith(QChar('\"')))
		return str.mid(2, str.size() - 3);
	else
		return str;
}

UiDBDirection DatabarDirection_To_Idx(int dir)
{
	switch (dir)
	{
	case xlContext:	return UiContext;
	case xlLTR:		return UiL2R;
	case  xlRTL:	return UiR2L;
	default: ASSERT (FALSE); break;
	}
	return UiContext;
}

int Idx_To_DatabaDirection(int idx)
{
	switch (idx)
	{
	case UiContext:	return xlContext;
	case UiL2R:		return xlLTR;
	case  UiR2L:	return xlRTL;
	default: ASSERT(FALSE); break;
	}
	return xlContext;
}

// EtColor EtColorFromThemeColorItem(const KThemeColorItem &item)
// {
// 	EtColor clr;
// 	if (item.isThemeItem())
// 		clr.setTheme(EtThemeUiHelp::KsoTheme2EtThemeType(item.getKsoSchemeIndex()), item.getBrightness());
// 	else
// 		clr.setARGB(item.toQColor().rgba());
// 	return clr;
// }

QString GetStringFromVariant(VARIANT var)
{
	KSmartParam paraVar(var);
	if (paraVar.IsIntegerType())
		return QString::number(paraVar.GetNumberValue());
	else if (paraVar.IsFloatType())
		return QString::number(paraVar.GetFloatValue(), 'G', 11);

	return QString::fromUtf16(KSmartParam(var).GetStringValue());
}

double toDouble(QString str, bool *bOk)
{
	str = str.trimmed();
	bool bPercent = str.endsWith('%');
	if (bPercent)
		str.remove(str.size() - 1, 1);

	double dVal = str.toDouble(bOk);
	if (*bOk && bPercent)
		return dVal / 100.0;
	return dVal;
}

/////////////////////////////////////////////////////////////////////////////////////

HRESULT FormatConditionWrapper::ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject** obj, bool lastone)
{
	KComVariant varOperator;
	KComVariant varFormula1;
	KComVariant varFormula2;
	KComVariant varString;
	KComVariant varTextOperator;
	KComVariant varDateOperator;
	KComVariant varScopeType;

	if (etCellValue == pItem->type)
	{
		varOperator.Assign(pItem->opt);
		ks_bstr bstr1(krt::utf16(pItem->formula1));
		varFormula1.AssignBSTR(bstr1);
		if (pItem->opt == etBetween || pItem->opt == etNotBetween)
		{
			ks_bstr bstr2(krt::utf16(pItem->formula2));
			varFormula2.AssignBSTR(bstr2);
		}
	}
	else if (etTextString == pItem->type)
	{
		ks_bstr bstr(krt::utf16(pItem->formula1));
		varString.AssignBSTR(bstr);
		varTextOperator.Assign(pItem->opt);
	}
	else if (etTimePeriod == pItem->type)
	{
		varDateOperator.Assign(pItem->opt);
	}
	else if (etExpression == pItem->type)
	{
		ks_bstr bstr(krt::utf16(pItem->formula1));
		varFormula1.AssignBSTR(bstr);
	}


	HRESULT hr = S_OK;
	ks_stdptr<IKCoreObject> spCond;

	hr = pConds->Add(pItem->type, varOperator, varFormula1, varFormula2,
		varString, varTextOperator, varDateOperator, varScopeType, pItem->pKXF.get(), &spCond, obj);

	if (SUCCEEDED(hr))
	{
		ks_stdptr<FormatCondition> spFC = spCond;
		if (!lastone)
			spFC->SetFirstPriority();
		spFC->put_StopIfTrue(pItem->bStopIfTrue ? VARIANT_TRUE : VARIANT_FALSE);
		ks_bstr bstr(krt::utf16(pItem->strCfRuleNotes));
		spFC->put_CfRuleNotes(bstr);
	}
	return hr;
}

HRESULT FormatConditionWrapper::ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds)
{
	ks_stdptr<IKCoreObject> spCore;
	HRESULT hr = pConds->ModifyItem(pItem->type, lOldIdx, &spCore);
	if (FAILED(hr))
	{
		return hr;
	}

	KComVariant varOperator;
	KComVariant varFormula1;
	KComVariant varFormula2;
	KComVariant varString;
	KComVariant varTextOperator;
	KComVariant varDateOperator;
	KComVariant varScopeType;
	KComVariant varEmpty;

	ks_stdptr<FormatCondition> spCond = spCore;
	if (etCellValue == pItem->type)
	{
		varOperator.Assign(pItem->opt);
		ks_bstr bstr1(krt::utf16(pItem->formula1));
		varFormula1.AssignBSTR(bstr1);
		if (pItem->opt == etBetween || pItem->opt == etNotBetween)
		{
			ks_bstr bstr2(krt::utf16(pItem->formula2));
			varFormula2.AssignBSTR(bstr2);
		}
		hr = spCond->Modify(pItem->type, varOperator, varFormula1, varFormula2, varEmpty, varEmpty);
	}
	else if (etTextString == pItem->type)
	{
		ks_bstr bstr(krt::utf16(pItem->formula1));
		varString.AssignBSTR(bstr);
		varTextOperator.Assign(pItem->opt);
		hr = spCond->Modify(pItem->type, varEmpty, varEmpty, varEmpty, varString, varTextOperator);
	}
	else if (etTimePeriod == pItem->type)
	{
		varDateOperator.Assign(pItem->opt);
		hr = spCond->Modify(pItem->type, varEmpty, varEmpty, varEmpty, varEmpty, varDateOperator);
	}
	else if (etExpression == pItem->type)
	{
		ks_bstr bstr(krt::utf16(pItem->formula1));
		varFormula1.AssignBSTR(bstr);
		hr = spCond->Modify(pItem->type, varEmpty, varFormula1, varEmpty, varEmpty, varEmpty);
	}

	if (FAILED(hr))
	{
		return hr;
	}
	
	ks_stdptr<Style> spStyle;
	spCond->get_Style(&spStyle);
	ks_stdptr<IFormatHost> spFormat = spStyle;
	spFormat->SetXF(nullptr, nullptr);
	spFormat->SetXF(&pItem->pKXF->mask, pItem->pKXF.get());
	ks_bstr cfRuleNotes(krt::utf16(pItem->strCfRuleNotes));
	spCond->put_CfRuleNotes(cfRuleNotes);
	return hr;
}

void FormatConditionWrapper::ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond)
{
	ks_stdptr<FormatCondition> spCS = pCond;
	ks_bstr bstrFormula1;
	ks_bstr bstrFormula2;
	ks_bstr bstrText;
	ks_stdptr<Style> spStyle;
	long lType = -1;
	long lOperator = -1;
	XlContainsOperator containsOp;
	XlTimePeriods timePer;
	long lPriority = -1;
	VARIANT_BOOL bStopIfTrue = false;

	spCS->get_Formula1(&bstrFormula1);
	spCS->get_Formula2(&bstrFormula2);
	spCS->get_Text(&bstrText);
	spCS->get_Style(&spStyle);
	spCS->get_Type(&lType);
	spCS->get_Operator(&lOperator);
	spCS->get_Priority(&lPriority);
	spCS->get_TextOperator(&containsOp);
	spCS->get_DateOperator(&timePer);
	spCS->get_StopIfTrue(&bStopIfTrue);
	PCWSTR strCfRuleNotes = spCS->get_CfRuleNotes();

	pItem->strCfRuleNotes = QString::fromUtf16(strCfRuleNotes);
	pItem->bStopIfTrue = bStopIfTrue;
	pItem->nPriority   = lPriority;
	GetKXFFromItem(spStyle, pItem);
	pItem->type        = (oldapi::ETFormatConditionType)lType;
	switch (pItem->type)
	{
	case etCellValue:
	{
		pItem->formula1 = QString::fromUtf16(bstrFormula1.c_str());
		pItem->opt = lOperator;
		if (lOperator == etBetween || lOperator == etNotBetween)
		{
			pItem->formula2 = QString::fromUtf16(bstrFormula2.c_str());
		}
	}
	break;
	case etExpression:
		pItem->formula1 = QString::fromUtf16(bstrFormula1.c_str());
		break;
	case etTimePeriod:
		pItem->opt = timePer;
	break;
	case etTextString:
	{
		pItem->opt = containsOp;
		pItem->formula1 = QString::fromUtf16(bstrText.c_str());
	}
	break;
	case etBlanksCondition:
	case etNoBlanksCondition:
	case etErrorsCondition:
	case etNoErrorsCondition:
		break;
	default:
		ASSERT (FALSE); break;
	}
}

void FormatConditionWrapper::SwapPriority(FormatConditions *pConds, long lUp, long lDown)
{
	VS(pConds->SwapPriority(lUp, lDown));
}

HRESULT FormatConditionWrapper::Delete(IKCoreObject *pCore)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	return spFC->Delete();
}

VARIANT_BOOL FormatConditionWrapper::GetStopIfTrue(IKCoreObject *pCore)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	VARIANT_BOOL b = VARIANT_FALSE;
	VS(spFC->get_StopIfTrue(&b));
	return b;
}

void FormatConditionWrapper::SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL b)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	VS(spFC->put_StopIfTrue(b));
}

void FormatConditionWrapper::GetAppliesTo(IKCoreObject *pCore, Range **ppRg)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	VS(spFC->get_AppliesTo(ppRg));
}

HRESULT FormatConditionWrapper::SetAppliesTo(IKCoreObject *pCore, Range *pRg)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	return spFC->ModifyAppliesToRange(pRg);
}

long FormatConditionWrapper::GetPriority(IKCoreObject* pCore)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	long nPriority = -1;
	VS(spFC->get_Priority(&nPriority));
	return nPriority;
}

void FormatConditionWrapper::SetPriority(IKCoreObject* pCore, long nPriority)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	VS(spFC->put_Priority(nPriority));
}

void FormatConditionWrapper::SetFirstPriority(IKCoreObject* pCore)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	VS(spFC->SetFirstPriority());
}

void FormatConditionWrapper::SetLastPriority(IKCoreObject* pCore)
{
	ks_stdptr<FormatCondition> spFC = pCore;
	VS(spFC->SetLastPriority());
}

/////////////////////////////////////////////////////////////////////////////////////
HRESULT Top10Wrapper::ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject** obj, bool lastone)
{
	ks_stdptr<IKCoreObject> spCond;
	VS(pConds->AddTop10(&spCond, obj));

	HRESULT hr = ImportInner(pItem, spCond);
	if (FAILED(hr))
	{
		return hr;
	}

	ks_stdptr<ITop10> spTop10 = spCond;
	if(!lastone)
		spTop10->SetFirstPriority();
	spTop10->put_StopIfTrue(pItem->bStopIfTrue ? VARIANT_TRUE : VARIANT_FALSE);

	ks_bstr bstr(krt::utf16(pItem->strCfRuleNotes));
	spTop10->put_CfRuleNotes(bstr);

	return S_OK;
}

HRESULT Top10Wrapper::ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds)
{
	ks_stdptr<IKCoreObject> spCore;
	HRESULT hr = pConds->ModifyItem(pItem->type, lOldIdx, &spCore);
	if (FAILED(hr))
	{
		return hr;
	}

	return ImportInner(pItem, spCore);
}

void Top10Wrapper::ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond)
{
	ks_stdptr<ITop10> spTop10 = pCond;
	ks_stdptr<Style> spStyle;
	oldapi::XlTopBottom topBottom;
	long lRank = -1;
	VARIANT_BOOL bPercent = VARIANT_FALSE;
	long lPriority = -1;
	VARIANT_BOOL bStopIfTrue = false;

	spTop10->get_Style(&spStyle);
	spTop10->get_TopBottom(&topBottom);
	spTop10->get_Rank(&lRank);
	spTop10->get_Percent(&bPercent);
	spTop10->get_StopIfTrue(&bStopIfTrue);
	spTop10->get_Priority(&lPriority);
	PCWSTR strCfRuleNotes = spTop10->get_CfRuleNotes();

	pItem->type        = etTop10;
	GetKXFFromItem(spStyle, pItem);
	pItem->bBottom     = topBottom == xlTop10Bottom;
	pItem->strRank       = QString::number(lRank);
	pItem->bPercent    = bPercent;
	pItem->bStopIfTrue = bStopIfTrue;
	pItem->nPriority   = lPriority;
	pItem->strCfRuleNotes = QString::fromUtf16(strCfRuleNotes);
}

void Top10Wrapper::SwapPriority(FormatConditions *pConds, long lUp, long lDown)
{
	VS(pConds->SwapPriority(lUp, lDown));
}

HRESULT Top10Wrapper::Delete(IKCoreObject *pCore)
{
	ks_stdptr<ITop10> spTop10 = pCore;
	return spTop10->Delete();
}

VARIANT_BOOL Top10Wrapper::GetStopIfTrue(IKCoreObject *pCore)
{
	ks_stdptr<ITop10> spTop10 = pCore;
	VARIANT_BOOL b = VARIANT_FALSE;
	VS(spTop10->get_StopIfTrue(&b));
	return b;
}

void Top10Wrapper::SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL b)
{
	ks_stdptr<ITop10> spTop10 = pCore;
	VS(spTop10->put_StopIfTrue(b));
}

void Top10Wrapper::GetAppliesTo(IKCoreObject *pCore, Range **ppRg)
{
	ks_stdptr<ITop10> spTop10 = pCore;
	VS(spTop10->get_AppliesTo(ppRg));
}

HRESULT Top10Wrapper::SetAppliesTo(IKCoreObject *pCore, Range *pRg)
{
	ks_stdptr<ITop10> spTop10 = pCore;
	return (spTop10->ModifyAppliesToRange(pRg));
}

HRESULT Top10Wrapper::ImportInner(CondFormatItem *pItem, IKCoreObject *pCore)
{
	ks_stdptr<ITop10> spTop10 = pCore;
	spTop10->put_Percent(pItem->bPercent ? VARIANT_TRUE : VARIANT_FALSE);
	spTop10->put_Rank(pItem->strRank.toInt());
	spTop10->put_TopBottom(pItem->bBottom ? xlTop10Bottom : xlTop10Top);
	ks_bstr cfRuleNotes(krt::utf16(pItem->strCfRuleNotes));
	spTop10->put_CfRuleNotes(cfRuleNotes);

	ks_stdptr<Style> spStyle;
	spTop10->get_Style(&spStyle);
	ks_stdptr<IFormatHost> spFormat = spStyle;
	spFormat->SetXF(nullptr, nullptr);
	spFormat->SetXF(&pItem->pKXF->mask, pItem->pKXF.get());
	return S_OK;
}

long Top10Wrapper::GetPriority(IKCoreObject* pCore)
{
	ks_stdptr<ITop10> spFC = pCore;
	long nPriority = -1;
	VS(spFC->get_Priority(&nPriority));
	return nPriority;
}

void Top10Wrapper::SetPriority(IKCoreObject* pCore, long nPriority)
{
	ks_stdptr<ITop10> spFC = pCore;
	VS(spFC->put_Priority(nPriority));
}

void Top10Wrapper::SetFirstPriority(IKCoreObject* pCore)
{
	ks_stdptr<ITop10> spFC = pCore;
	VS(spFC->SetFirstPriority());
}

void Top10Wrapper::SetLastPriority(IKCoreObject* pCore)
{
	ks_stdptr<ITop10> spFC = pCore;
	VS(spFC->SetLastPriority());
}

/////////////////////////////////////////////////////////////////////////////////////

HRESULT AboveAverageWrapper::ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject** obj, bool lastone)
{
	ks_stdptr<IKCoreObject> spCond;
	VS(pConds->AddAboveAverage(&spCond, obj));

	HRESULT hr = ImportInner(pItem, spCond);
	if (FAILED(hr))
	{
		return hr;
	}

	ks_stdptr<IAboveAverage> spAA = spCond;
	if(!lastone)
		spAA->SetFirstPriority();
	spAA->put_StopIfTrue(pItem->bStopIfTrue ? VARIANT_TRUE : VARIANT_FALSE);

	ks_bstr bstr(krt::utf16(pItem->strCfRuleNotes));
	spAA->put_CfRuleNotes(bstr);

	return S_OK;
}

HRESULT AboveAverageWrapper::ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds)
{
	ks_stdptr<IKCoreObject> spCore;
	HRESULT hr = pConds->ModifyItem(pItem->type, lOldIdx, &spCore);
	if (FAILED(hr))
	{
		return hr;
	}

	return ImportInner(pItem, spCore);
}

void AboveAverageWrapper::ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond)
{
	ks_stdptr<IAboveAverage> spAA = pCond;
	ks_stdptr<Style> spStyle;
	oldapi::XlAboveBelow abvBelow;
	long lDev = -1;
	long lPriority = -1;
	VARIANT_BOOL bStopIfTrue = false;

	spAA->get_Style(&spStyle);
	spAA->get_AboveBelow(&abvBelow);
	spAA->get_NumStdDev(&lDev);
	spAA->get_Priority(&lPriority);
	spAA->get_StopIfTrue(&bStopIfTrue);
	PCWSTR strCfRuleNotes = spAA->get_CfRuleNotes();

	pItem->type        = etAboveAverageCondition;
	GetKXFFromItem(spStyle, pItem);
	pItem->opt         = abvBelow;
	pItem->nStdDev     = lDev;
	pItem->nPriority   = lPriority;
	pItem->bStopIfTrue = bStopIfTrue;
	pItem->strCfRuleNotes = QString::fromUtf16(strCfRuleNotes);
}


void AboveAverageWrapper::SwapPriority(FormatConditions *pConds, long lUp, long lDown)
{
	VS(pConds->SwapPriority(lUp, lDown));
}

HRESULT AboveAverageWrapper::Delete(IKCoreObject *pCore)
{
	ks_stdptr<IAboveAverage> spAA = pCore;
	return spAA->Delete();
}

VARIANT_BOOL AboveAverageWrapper::GetStopIfTrue(IKCoreObject *pCore)
{
	ks_stdptr<IAboveAverage> spAA = pCore;
	VARIANT_BOOL b = VARIANT_FALSE;
	VS(spAA->get_StopIfTrue(&b));
	return b;
}

void AboveAverageWrapper::SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL b)
{
	ks_stdptr<IAboveAverage> spAA = pCore;
	VS(spAA->put_StopIfTrue(b));
}

void AboveAverageWrapper::GetAppliesTo(IKCoreObject *pCore, Range **ppRg)
{
	ks_stdptr<IAboveAverage> spAA = pCore;
	VS(spAA->get_AppliesTo(ppRg));
}

HRESULT AboveAverageWrapper::SetAppliesTo(IKCoreObject *pCore, Range *pRg)
{
	ks_stdptr<IAboveAverage> spAA = pCore;
	return (spAA->ModifyAppliesToRange(pRg));
}

HRESULT AboveAverageWrapper::ImportInner(CondFormatItem *pItem, IKCoreObject *pCore)
{
	ks_stdptr<IAboveAverage> spAA = pCore;
	spAA->put_AboveBelow((XlAboveBelow)pItem->opt);
	if (pItem->opt == xlAboveStdDev || pItem->opt == xlBelowStdDev)
		spAA->put_NumStdDev(pItem->nStdDev);

	ks_bstr cfRuleNotes(krt::utf16(pItem->strCfRuleNotes));
	spAA->put_CfRuleNotes(cfRuleNotes);

	ks_stdptr<Style> spStyle;
	spAA->get_Style(&spStyle);
	ks_stdptr<IFormatHost> spFormat = spStyle;
	spFormat->SetXF(nullptr, nullptr);
	spFormat->SetXF(&pItem->pKXF->mask, pItem->pKXF.get());

	return S_OK;
}

long AboveAverageWrapper::GetPriority(IKCoreObject* pCore)
{
	ks_stdptr<IAboveAverage> spFC = pCore;
	long nPriority = -1;
	VS(spFC->get_Priority(&nPriority));
	return nPriority;
}

void AboveAverageWrapper::SetPriority(IKCoreObject* pCore, long nPriority)
{
	ks_stdptr<IAboveAverage> spFC = pCore;
	VS(spFC->put_Priority(nPriority));
}

void AboveAverageWrapper::SetFirstPriority(IKCoreObject* pCore)
{
	ks_stdptr<IAboveAverage> spFC = pCore;
	VS(spFC->SetFirstPriority());
}

void AboveAverageWrapper::SetLastPriority(IKCoreObject* pCore)
{
	ks_stdptr<IAboveAverage> spFC = pCore;
	VS(spFC->SetLastPriority());
}

/////////////////////////////////////////////////////////////////////////////////////

HRESULT UniqueValuesWrapper::ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject** obj, bool lastone)
{
	ks_stdptr<IKCoreObject> spCond;
	VS(pConds->AddUniqueValues(&spCond, obj));

	HRESULT hr = ImportInner(pItem, spCond);
	if (FAILED(hr))
	{
		return hr;
	}
	ks_stdptr<IUniqueValues> spUV = spCond;
	if(!lastone)
		spUV->SetFirstPriority();
	spUV->put_StopIfTrue(pItem->bStopIfTrue ? VARIANT_TRUE : VARIANT_FALSE);

	ks_bstr bstr(krt::utf16(pItem->strCfRuleNotes));
	spUV->put_CfRuleNotes(bstr);
	return S_OK;
}

HRESULT UniqueValuesWrapper::ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds)
{
	ks_stdptr<IKCoreObject> spCore;
	HRESULT hr = pConds->ModifyItem(pItem->type, lOldIdx, &spCore);
	if (FAILED(hr))
	{
		return hr;
	}

	return ImportInner(pItem, spCore);
}

void UniqueValuesWrapper::ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond)
{
	ks_stdptr<IUniqueValues> spUV = pCond;
	ks_stdptr<Style> spStyle;
	oldapi::XlDupeUnique dupUnique;
	long lPriority = -1;
	VARIANT_BOOL bStopIfTrue = false;

	spUV->get_Style(&spStyle);
	spUV->get_DupeUnique(&dupUnique);
	spUV->get_Priority(&lPriority);
	spUV->get_StopIfTrue(&bStopIfTrue);
	PCWSTR strCfRuleNotes = spUV->get_CfRuleNotes();

	pItem->type        = etUniqueValues;
	GetKXFFromItem(spStyle, pItem);
	pItem->bDuplicate  = dupUnique == xlDuplicate;
	pItem->nPriority   = lPriority;
	pItem->bStopIfTrue = bStopIfTrue;
	pItem->strCfRuleNotes = QString::fromUtf16(strCfRuleNotes);
}


void UniqueValuesWrapper::SwapPriority(FormatConditions *pConds, long lUp, long lDown)
{
	VS(pConds->SwapPriority(lUp, lDown));
}

HRESULT UniqueValuesWrapper::Delete(IKCoreObject *pCore)
{
	ks_stdptr<IUniqueValues> spUV = pCore;
	return spUV->Delete();
}

VARIANT_BOOL UniqueValuesWrapper::GetStopIfTrue(IKCoreObject *pCore)
{
	ks_stdptr<IUniqueValues> spUV = pCore;
	VARIANT_BOOL b = VARIANT_FALSE;
	VS(spUV->get_StopIfTrue(&b));
	return b;
}

void UniqueValuesWrapper::SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL b)
{
	ks_stdptr<IUniqueValues> spUV = pCore;
	VS(spUV->put_StopIfTrue(b));
}

void UniqueValuesWrapper::GetAppliesTo(IKCoreObject *pCore, Range **ppRg)
{
	ks_stdptr<IUniqueValues> spUV = pCore;
	VS(spUV->get_AppliesTo(ppRg));
}

HRESULT UniqueValuesWrapper::SetAppliesTo(IKCoreObject *pCore, Range *pRg)
{
	ks_stdptr<IUniqueValues> spUV = pCore;
	return (spUV->ModifyAppliesToRange(pRg));
}

HRESULT UniqueValuesWrapper::ImportInner(CondFormatItem *pItem, IKCoreObject *pCore)
{
	ks_stdptr<IUniqueValues> spUV = pCore;
	spUV->put_DupeUnique(pItem->bDuplicate ? xlDuplicate : xlUnique);
	ks_bstr cfRuleNotes(krt::utf16(pItem->strCfRuleNotes));
	spUV->put_CfRuleNotes(cfRuleNotes);

	ks_stdptr<Style> spStyle;
	spUV->get_Style(&spStyle);
	ks_stdptr<IFormatHost> spFormat = spStyle;
	spFormat->SetXF(nullptr, nullptr);
	return spFormat->SetXF(&pItem->pKXF->mask, pItem->pKXF.get());
}

long UniqueValuesWrapper::GetPriority(IKCoreObject* pCore)
{
	ks_stdptr<IUniqueValues> spFC = pCore;
	long nPriority = -1;
	VS(spFC->get_Priority(&nPriority));
	return nPriority;
}

void UniqueValuesWrapper::SetPriority(IKCoreObject* pCore, long nPriority)
{
	ks_stdptr<IUniqueValues> spFC = pCore;
	VS(spFC->put_Priority(nPriority));
}

void UniqueValuesWrapper::SetFirstPriority(IKCoreObject* pCore)
{
	ks_stdptr<IUniqueValues> spFC = pCore;
	VS(spFC->SetFirstPriority());
}

void UniqueValuesWrapper::SetLastPriority(IKCoreObject* pCore)
{
	ks_stdptr<IUniqueValues> spFC = pCore;
	VS(spFC->SetLastPriority());
}

/////////////////////////////////////////////////////////////////////////////////////

HRESULT ColorScaleWrapper::ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject** obj, bool lastone)
{
	ks_stdptr<IKCoreObject> spCond;
	VS(pConds->AddColorScale(pItem->cs.b3ColorScale ? 3 : 2, &spCond, obj));

	HRESULT hr = ImportInner(pItem, spCond);
	if (FAILED(hr))
	{
		return hr;
	}
	ks_stdptr<IColorScale> spCS = spCond;
	if(!lastone)
		spCS->SetFirstPriority();

	ks_bstr bstr(krt::utf16(pItem->strCfRuleNotes));
	spCS->put_CfRuleNotes(bstr);
	return S_OK;
}

HRESULT ColorScaleWrapper::ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds)
{
	ks_stdptr<IKCoreObject> spCore;
	HRESULT hr = pConds->ModifyItem(pItem->type, lOldIdx, &spCore);
	if (FAILED(hr))
	{
		return hr;
	}

	ks_stdptr<IColorScale> spCS = spCore;
	spCS->setThreeColorScale(alg::bool2BOOL(pItem->cs.b3ColorScale));
	return ImportInner(pItem, spCore);
}

void ColorScaleWrapper::ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond)
{
	long lPriority = -1;
	VARIANT_BOOL bStopIfTrue = false;
	ks_stdptr<IColorScale> spCS = pCond;
	ks_stdptr<ColorScaleCriteria> spCriteria;
	spCS->get_ColorScaleCriteria(&spCriteria);
	long lCount = -1;
	spCriteria->get_Count(&lCount);
	for (int i = 0; i < lCount; ++i)
	{
		ASSERT(lCount <= 3);
		ks_stdptr<ColorScaleCriterion> spCriterion;
		KComVariant varIndex(i + 1);
		spCriteria->get_Item(varIndex, &spCriterion);

		XlConditionValueTypes valType;
		EtColor clr;
		KComVariant val;

		spCriterion->get_EtColor(&clr);
		spCriterion->get_Type(&valType);
		spCriterion->get_Value(&val);

		pItem->cs.b3ColorScale  = lCount == 3;
		pItem->cs.arrClr[i]     = clr;
		pItem->cs.arrCv[i].cvTy = valType;
		pItem->cs.arrCv[i].val  = GetStringFromVariant(val);
	}
	spCS->get_StopIfTrue(&bStopIfTrue);
	spCS->get_Priority(&lPriority);
	PCWSTR strCfRuleNotes = spCS->get_CfRuleNotes();

	pItem->type        = etColorScale;
	pItem->nPriority   = lPriority;
	pItem->bStopIfTrue = bStopIfTrue;
	pItem->strCfRuleNotes = QString::fromUtf16(strCfRuleNotes);
}


void ColorScaleWrapper::SwapPriority(FormatConditions *pConds, long lUp, long lDown)
{
	VS(pConds->SwapPriority(lUp, lDown));
}

HRESULT ColorScaleWrapper::Delete(IKCoreObject *pCore)
{
	ks_stdptr<IColorScale> spCS = pCore;
	return spCS->Delete();
}

VARIANT_BOOL ColorScaleWrapper::GetStopIfTrue(IKCoreObject *pCore)
{
	ks_stdptr<IColorScale> spCS = pCore;
	VARIANT_BOOL b = VARIANT_FALSE;
	VS(spCS->get_StopIfTrue(&b));
	return b;
}

void ColorScaleWrapper::SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL b)
{
	ASSERT (FALSE);
	//ks_stdptr<IColorScale> spCS = pCore;
	//VS(spCS->put_StopIfTrue(b));
}

void ColorScaleWrapper::GetAppliesTo(IKCoreObject *pCore, Range **ppRg)
{
	ks_stdptr<IColorScale> spCS = pCore;
	VS(spCS->get_AppliesTo(ppRg));
}

HRESULT ColorScaleWrapper::SetAppliesTo(IKCoreObject *pCore, Range *pRg)
{
	ks_stdptr<IColorScale> spCS = pCore;
	return (spCS->ModifyAppliesToRange(pRg));
}

HRESULT ColorScaleWrapper::ImportInner(CondFormatItem *pItem, IKCoreObject *pCore)
{
	ks_stdptr<IColorScale> spCS = pCore;
	ks_stdptr<ColorScaleCriteria> spCriteria;
	spCS->get_ColorScaleCriteria(&spCriteria);
	long lCount = -1;
	spCriteria->get_Count(&lCount);
	for (int i = 0; i < lCount; ++i)
	{
		ks_stdptr<ColorScaleCriterion> spCriterion;
		KComVariant varIndex(i + 1);
		spCriteria->get_Item(varIndex, &spCriterion);
		XlConditionValueTypes type = pItem->cs.arrCv[i].cvTy;
		spCriterion->put_Type(type);
		if (type != xlConditionValueLowestValue && type != xlConditionValueHighestValue)
		{
			KComVariant val(krt::utf16(pItem->cs.arrCv[i].val));
			spCriterion->put_Value(val);
		}
		spCriterion->put_EtColor(pItem->cs.arrClr[i]);
	}
	ks_bstr cfRuleNotes(krt::utf16(pItem->strCfRuleNotes));
	spCS->put_CfRuleNotes(cfRuleNotes);

	return S_OK;
}

long ColorScaleWrapper::GetPriority(IKCoreObject* pCore)
{
	ks_stdptr<IColorScale> spFC = pCore;
	long nPriority = -1;
	VS(spFC->get_Priority(&nPriority));
	return nPriority;
}

void ColorScaleWrapper::SetPriority(IKCoreObject* pCore, long nPriority)
{
	ks_stdptr<IColorScale> spFC = pCore;
	VS(spFC->put_Priority(nPriority));
}

void ColorScaleWrapper::SetFirstPriority(IKCoreObject* pCore)
{
	ks_stdptr<IColorScale> spFC = pCore;
	VS(spFC->SetFirstPriority());
}

void ColorScaleWrapper::SetLastPriority(IKCoreObject* pCore)
{
	ks_stdptr<IColorScale> spFC = pCore;
	VS(spFC->SetLastPriority());
}

/////////////////////////////////////////////////////////////////////////////////////

HRESULT DatabarWrapper::ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject** obj, bool lastone)
{
	ks_stdptr<IKCoreObject> spCond;
	VS(pConds->AddDatabar(&spCond, obj));

	HRESULT hr = ImportInner(pItem, spCond);
	if (FAILED(hr))
	{
		return hr;
	}

	ks_stdptr<IDatabar> spDB = spCond;
	if(!lastone)
		spDB->SetFirstPriority();

	ks_bstr bstr(krt::utf16(pItem->strCfRuleNotes));
	spDB->put_CfRuleNotes(bstr);
	return S_OK;
}

HRESULT DatabarWrapper::ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds)
{
	ks_stdptr<IKCoreObject> spCore;
	HRESULT hr = pConds->ModifyItem(pItem->type, lOldIdx, &spCore);
	if (FAILED(hr))
	{
		return hr;
	}

	return ImportInner(pItem, spCore);
}

void DatabarWrapper::ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond)
{
	long lPriority = -1;
	VARIANT_BOOL bStopIfTrue = false;
	ks_stdptr<IDatabar> spDB = pCond;
	pItem->ensureDB();
	VARIANT_BOOL bShowValue = VARIANT_FALSE;
	spDB->get_ShowValue(&bShowValue);
	ks_stdptr<ConditionValue> spMin;
	ks_stdptr<ConditionValue> spMax;
	spDB->get_MinPoint(&spMin);
	spDB->get_MaxPoint(&spMax);
	KComVariant valMin;
	KComVariant valMax;
	spMin->get_Value(&valMin);
	spMax->get_Value(&valMax);
	XlConditionValueTypes minType;
	XlConditionValueTypes maxType;
	spMin->get_Type(&minType);
	spMax->get_Type(&maxType);
	XlDataBarFillType fillType;
	spDB->get_BarFillType(&fillType);
	spDB->getFillColor(&pItem->pDB->clrFill);
	ks_stdptr<DataBarBorder> spBorder;
	spDB->get_BarBorder(&spBorder);
	XlDataBarBorderType borderType;
	spBorder->get_Type(&borderType);
	spBorder->getBorderColor(&pItem->pDB->clrBorder);
	long lReadingOrder = 0;
	spDB->get_Direction(&lReadingOrder);
	ks_stdptr<NegativeBarFormat> spNeg;
	spDB->get_NegativeBarFormat(&spNeg);
	XlDataBarNegativeColorType negFillType;
	spNeg->get_ColorType(&negFillType);
	spNeg->getNegFillColor(&pItem->pDB->axisSetting.clrNegativeFill);
	XlDataBarNegativeColorType negBorderType;
	spNeg->get_BorderColorType(&negBorderType);
	spNeg->getNegBorderColor(&pItem->pDB->axisSetting.clrNegativeBorder);
	XlDataBarAxisPosition ap;
	spDB->get_AxisPosition(&ap);
	spDB->getAxisColor(&pItem->pDB->axisSetting.clrAxis);

	spDB->get_Priority(&lPriority);
	spDB->get_StopIfTrue(&bStopIfTrue);

	PCWSTR strCfRuleNotes = spDB->get_CfRuleNotes();

	pItem->type                           = etDatabar;
	pItem->pDB->bShowValue                = bShowValue;
	pItem->pDB->bBorder                   = borderType == xlDataBarBorderSolid;
	pItem->pDB->bGradient                 = fillType == xlDataBarFillGradient;
	pItem->pDB->dir                       = DatabarDirection_To_Idx(lReadingOrder);
	pItem->pDB->arrCv[0].cvTy             = minType;
	pItem->pDB->arrCv[0].val              = GetStringFromVariant(valMin);
	pItem->pDB->arrCv[1].cvTy             = maxType;
	pItem->pDB->arrCv[1].val              = GetStringFromVariant(valMax);
	pItem->pDB->axisSetting.ap            = ap;
	pItem->pDB->axisSetting.negBorderType = negBorderType;
	pItem->pDB->axisSetting.negFillType   = negFillType;
	pItem->nPriority                      = lPriority;
	pItem->bStopIfTrue                    = bStopIfTrue;
	pItem->strCfRuleNotes				  = QString::fromUtf16(strCfRuleNotes);
}

void DatabarWrapper::SwapPriority(FormatConditions *pConds, long lUp, long lDown)
{
	VS(pConds->SwapPriority(lUp, lDown));
}

HRESULT DatabarWrapper::Delete(IKCoreObject *pCore)
{
	ks_stdptr<IDatabar> spDB = pCore;
	return spDB->Delete();
}

VARIANT_BOOL DatabarWrapper::GetStopIfTrue(IKCoreObject *pCore)
{
	ks_stdptr<IDatabar> spDB = pCore;
	VARIANT_BOOL b = VARIANT_FALSE;
	VS(spDB->get_StopIfTrue(&b));
	return b;
}

void DatabarWrapper::SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL b)
{
	ASSERT (FALSE);
	//ks_stdptr<IDatabar> spDB = pCore;
	//VS(spDB->put_StopIfTrue(b));
}

void DatabarWrapper::GetAppliesTo(IKCoreObject *pCore, Range **ppRg)
{
	ks_stdptr<IDatabar> spDB = pCore;
	VS(spDB->get_AppliesTo(ppRg));
}

HRESULT DatabarWrapper::SetAppliesTo(IKCoreObject *pCore, Range *pRg)
{
	ks_stdptr<IDatabar> spDB = pCore;
	return (spDB->ModifyAppliesToRange(pRg));
}

HRESULT DatabarWrapper::ImportInner(CondFormatItem *pItem, IKCoreObject *pCore)
{
	ks_stdptr<IDatabar> spDB = pCore;

	spDB->put_ShowValue(pItem->pDB->bShowValue ? VARIANT_TRUE : VARIANT_FALSE);
	ks_stdptr<ConditionValue> spMin;
	ks_stdptr<ConditionValue> spMax;
	spDB->get_MinPoint(&spMin);
	spDB->get_MaxPoint(&spMax);
	KComVariant varMin(krt::utf16(pItem->pDB->arrCv[0].val));
	KComVariant varMax(krt::utf16(pItem->pDB->arrCv[1].val));
	spMin->Modify(pItem->pDB->arrCv[0].cvTy, varMin);
	spMax->Modify(pItem->pDB->arrCv[1].cvTy, varMax);
	spDB->put_BarFillType(pItem->pDB->bGradient ? xlDataBarFillGradient : xlDataBarFillSolid);
	spDB->setFillColor(pItem->pDB->clrFill);
	ks_stdptr<DataBarBorder> spBorder;
	spDB->get_BarBorder(&spBorder);
	spBorder->put_Type(pItem->pDB->bBorder ? xlDataBarBorderSolid : xlDataBarBorderNone);
	if (pItem->pDB->bBorder)
		spBorder->setBorderColor(pItem->pDB->clrBorder);
	spDB->put_Direction(Idx_To_DatabaDirection(pItem->pDB->dir));
	ks_stdptr<NegativeBarFormat> spNeg;
	spDB->get_NegativeBarFormat(&spNeg);
	spNeg->put_ColorType(pItem->pDB->axisSetting.negFillType);
	if (pItem->pDB->axisSetting.negFillType == xlDataBarColor)
	{
		spNeg->setNegFillColor(pItem->pDB->axisSetting.clrNegativeFill);
	}
	spNeg->put_BorderColorType(pItem->pDB->axisSetting.negBorderType);
	if (pItem->pDB->axisSetting.negBorderType == xlDataBarColor)
	{
		spNeg->setNegBorderColor(pItem->pDB->axisSetting.clrNegativeBorder);
	}
	spDB->put_AxisPosition(pItem->pDB->axisSetting.ap);
	if (pItem->pDB->axisSetting.ap != xlDataBarAxisNone)
	{
		spDB->setAxisColor(pItem->pDB->axisSetting.clrAxis);
	}
	ks_bstr cfRuleNotes(krt::utf16(pItem->strCfRuleNotes));
	spDB->put_CfRuleNotes(cfRuleNotes);

	return S_OK;
}

long DatabarWrapper::GetPriority(IKCoreObject* pCore)
{
	ks_stdptr<IDatabar> spFC = pCore;
	long nPriority = -1;
	VS(spFC->get_Priority(&nPriority));
	return nPriority;
}

void DatabarWrapper::SetPriority(IKCoreObject* pCore, long nPriority)
{
	ks_stdptr<IDatabar> spFC = pCore;
	VS(spFC->put_Priority(nPriority));
}

void DatabarWrapper::SetFirstPriority(IKCoreObject* pCore)
{
	ks_stdptr<IDatabar> spFC = pCore;
	VS(spFC->SetFirstPriority());
}

void DatabarWrapper::SetLastPriority(IKCoreObject* pCore)
{
	ks_stdptr<IDatabar> spFC = pCore;
	VS(spFC->SetLastPriority());
}


/////////////////////////////////////////////////////////////////////////////////////

HRESULT IconSetWrapper::ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject** obj, bool lastone)
{
	ks_stdptr<IKCoreObject> spCond;
	VS(pConds->AddIconSetCondition(&spCond, obj));

	HRESULT hr = ImportInner(pItem, spCond);
	if (FAILED(hr))
	{
		return hr;
	}

	ks_stdptr<IIconSetCondition> spIS = spCond;
	if(!lastone)
		spIS->SetFirstPriority();

	ks_bstr bstr(krt::utf16(pItem->strCfRuleNotes));
	spIS->put_CfRuleNotes(bstr);
	return S_OK;
}

HRESULT IconSetWrapper::ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds)
{
	ks_stdptr<IKCoreObject> spCore;
	HRESULT hr = pConds->ModifyItem(pItem->type, lOldIdx, &spCore);
	if (FAILED(hr))
	{
		return hr;
	}

	return ImportInner(pItem, spCore);
}

void IconSetWrapper::ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond)
{
	long lPriority = -1;
	VARIANT_BOOL bStopIfTrue = false;
	ks_stdptr<IIconSetCondition> spIS = pCond;
	pItem->ensureIS();
	ks_stdptr<IconCriteria> spCriteria;
	spIS->get_IconCriteria(&spCriteria);
	long lCount = -1;
	spCriteria->get_Count(&lCount);
	for (int i = 0; i < lCount; ++i)
	{
		ASSERT(lCount <= 5);
		ks_stdptr<IconCriterion> spCriterion;
		KComVariant varIndex(lCount - i);
		spCriteria->get_Item(varIndex, &spCriterion);

		XlConditionValueTypes valType;
		XlIcon iconId;
		long lOperator = -1;
		KComVariant val;

		if (i != lCount - 1)
		{
			spCriterion->get_Type(&valType);
			spCriterion->get_Value(&val);
			spCriterion->get_Operator(&lOperator);
			pItem->pIS->arrCv[i].cvTy  = valType;
			pItem->pIS->arrCv[i].bGte  = lOperator == 7;
			pItem->pIS->arrCv[i].val   = GetStringFromVariant(val);
		}
		spCriterion->get_Icon(&iconId);
		pItem->pIS->arrIcon[i]     = iconId;
	}

	VARIANT_BOOL bReverse = VARIANT_FALSE;
	VARIANT_BOOL bShowOnly = VARIANT_FALSE;
	XlIconSet varIS;
	BOOL bCustom = FALSE;
	ks_stdptr<IIconSet> spIconSet;

	spIS->get_ReverseOrder(&bReverse);
	spIS->get_ShowIconOnly(&bShowOnly);
	spIS->get_Priority(&lPriority);
	spIS->get_StopIfTrue(&bStopIfTrue);
	spIS->get_IconSet(&spIconSet);
	spIconSet->get_ID(&varIS, &bCustom);
	PCWSTR strCfRuleNotes = spIS->get_CfRuleNotes();

	pItem->type           = etIconSets;
	pItem->pIS->is        = varIS;
	pItem->pIS->bCustom   = bCustom;
	pItem->pIS->bShowOnly = bShowOnly;
	pItem->pIS->bReverse  = bReverse;
	pItem->nPriority      = lPriority;
	pItem->bStopIfTrue    = bStopIfTrue;
	pItem->strCfRuleNotes = QString::fromUtf16(strCfRuleNotes);
}

void IconSetWrapper::SwapPriority(FormatConditions *pConds, long lUp, long lDown)
{
	VS(pConds->SwapPriority(lUp, lDown));
}

HRESULT IconSetWrapper::Delete(IKCoreObject *pCore)
{
	ks_stdptr<IIconSetCondition> spIS = pCore;
	return spIS->Delete();
}

VARIANT_BOOL IconSetWrapper::GetStopIfTrue(IKCoreObject *pCore)
{
	ks_stdptr<IIconSetCondition> spIS = pCore;
	VARIANT_BOOL b = VARIANT_FALSE;
	VS(spIS->get_StopIfTrue(&b));
	return b;
}

void IconSetWrapper::SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL b)
{
	ASSERT (FALSE);
	//ks_stdptr<IIconSetCondition> spIS = pCore;
	//VS(spIS->put_StopIfTrue(b));
}

void IconSetWrapper::GetAppliesTo(IKCoreObject *pCore, Range **ppRg)
{
	ks_stdptr<IIconSetCondition> spIS = pCore;
	VS(spIS->get_AppliesTo(ppRg));
}

HRESULT IconSetWrapper::SetAppliesTo(IKCoreObject *pCore, Range *pRg)
{
	ks_stdptr<IIconSetCondition> spIS = pCore;
	return spIS->ModifyAppliesToRange(pRg);
}

HRESULT IconSetWrapper::ImportInner(CondFormatItem *pItem, IKCoreObject *pCore)
{
	ks_stdptr<IIconSetCondition> spIS = pCore;

	spIS->setIconSet(pItem->pIS->is);
	spIS->put_ShowIconOnly(pItem->pIS->bShowOnly ? VARIANT_TRUE : VARIANT_FALSE);
	bool bReverse = pItem->pIS->bCustom ? false : pItem->pIS->bReverse;
	spIS->put_ReverseOrder(bReverse ? VARIANT_TRUE : VARIANT_FALSE);
	ks_stdptr<IconCriteria> spCriteria;
	spIS->get_IconCriteria(&spCriteria);
	long lCount = -1;
	spCriteria->get_Count(&lCount);
	for (int i = 0; i < lCount; ++i)
	{
		ks_stdptr<IconCriterion> spCriterion;
		KComVariant varIndex(lCount - i);
		spCriteria->get_Item(varIndex, &spCriterion);
		if (pItem->pIS->bCustom)
			spCriterion->put_Icon(pItem->pIS->arrIcon[i]);
		if (i != lCount - 1)
		{
			spCriterion->put_Operator(pItem->pIS->arrCv[i].bGte ? 7 : 5);
			KComVariant val(krt::utf16(pItem->pIS->arrCv[i].val));
			spCriterion->put_Type(pItem->pIS->arrCv[i].cvTy);
			spCriterion->put_Value(val);
		}
	}
	ks_bstr cfRuleNotes(krt::utf16(pItem->strCfRuleNotes));
	spIS->put_CfRuleNotes(cfRuleNotes);

	return S_OK;
}

long IconSetWrapper::GetPriority(IKCoreObject* pCore)
{
	ks_stdptr<IIconSetCondition> spFC = pCore;
	long nPriority = -1;
	VS(spFC->get_Priority(&nPriority));
	return nPriority;
}

void IconSetWrapper::SetPriority(IKCoreObject* pCore, long nPriority)
{
	ks_stdptr<IIconSetCondition> spFC = pCore;
	VS(spFC->put_Priority(nPriority));
}

void IconSetWrapper::SetFirstPriority(IKCoreObject* pCore)
{
	ks_stdptr<IIconSetCondition> spFC = pCore;
	VS(spFC->SetFirstPriority());
}

void IconSetWrapper::SetLastPriority(IKCoreObject* pCore)
{
	ks_stdptr<IIconSetCondition> spFC = pCore;
	VS(spFC->SetLastPriority());
}

CF_ApiWrapper* CF_ApiWrapperFactory::getWrapper(ETFormatConditionType type)
{
	static FormatConditionWrapper fcWrapper;
	static Top10Wrapper top10Wrapper;
	static AboveAverageWrapper aaWrapper;
	static UniqueValuesWrapper uvWrapper;
	static ColorScaleWrapper csWrapper;
	static DatabarWrapper dbWrapper;
	static IconSetWrapper isWrapper;

	switch (type)
	{
	case etCellValue:
	case etExpression:
	case etNoBlanksCondition:
	case etErrorsCondition:
	case etNoErrorsCondition:
	case etBlanksCondition:
	case etTimePeriod:
	case etTextString:
		return &fcWrapper;
	case etColorScale:
		return &csWrapper;
	case etDatabar:
		return &dbWrapper;
	case etTop10:
		return &top10Wrapper;
	case etIconSets:
		return &isWrapper;
	case etUniqueValues:
		return &uvWrapper;
	case etAboveAverageCondition:
		return &aaWrapper;
	default: ASSERT (FALSE); break;
	}
	return nullptr;
}