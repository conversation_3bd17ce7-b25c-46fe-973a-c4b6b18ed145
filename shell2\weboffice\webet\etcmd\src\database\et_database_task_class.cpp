﻿#include "etstdafx.h"
#include "etcore/little_alg.h"
#include "et_database_task_class.h"
#include "database.h"
#include "database_utils.h"
#include "database_field_context.h"
#include "et_task_peripheral.h"
#include "workbook.h"
#include "helpers/list_objects_helper.h"
#include "helpers/protection_helper.h"
#include "et_revision_context_impl.h"

namespace wo
{

using namespace Database;

namespace data_validation
{
    bool MapDvErrorCode(const int32_t &val, ks_wstring *res);
}

// ================== TaskExecDbBase ==================
HRESULT TaskExecDbBase::Respond(FieldContext *pFieldContext,
                                    KEtRevisionContext *pRevisionContext,
                                    FieldType fieldType,
                                    bool bClear,
                                    const RANGE &rg)
{
    HRESULT hr = S_OK;

    EtTaskPeripheral* peripheral = GetPeripheral();
    if(peripheral)
    {
        SetDVReturnCode code = pFieldContext->GetDVCode();
        if (code != errNone)
        {
            hr = E_FAIL;
            ks_wstring str;
            data_validation::MapDvErrorCode(static_cast<int32_t>(code), &str);

            binary_wo::VarObj resObj = peripheral->resSelf();
            resObj.add_field_str("cmdName", GetTag());
            resObj.add_field_uint32("dvResultCode", static_cast<uint32_t>(code));
            resObj.add_field_str("dvResultValue", static_cast<WebStr>(str.c_str()));

            return hr;
        }

        RECT rc = Range2Rect(rg);
        IDX sheetIdx = rg.SheetFrom();
        ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
        if (spWorksheet)
        {
            WebID objSheet = pRevisionContext->getSheetMain(sheetIdx)->objId();
            ks_bstr sheetName;
            spWorksheet->get_Name(&sheetName);
            {
                binary_wo::VarObj resObj = peripheral->resSelf();
                resObj.add_field_str("cmdName", GetTag());
                resObj.add_field_str("type", Utils::FieldTypeToStr(fieldType));
                resObj.add_field_bool("bClear", bClear);
                resObj.add_field_bool("bEnumAny", pFieldContext->IsEnumAny());

                resObj.add_field_double("objSheet", objSheet);
                resObj.add_field_int32("sheetIdx", sheetIdx);
                resObj.add_field_str("sheetName", sheetName);
                WriteRect(resObj, rc);
            }

            {
                binary_wo::VarObj resObj = peripheral->resOthers();
                resObj.add_field_str("cmdName", GetTag());
                resObj.add_field_str("type", Utils::FieldTypeToStr(fieldType));
                resObj.add_field_bool("bClear", bClear);
                resObj.add_field_bool("bEnumAny", pFieldContext->IsEnumAny());

                resObj.add_field_str("affectedBy", pRevisionContext->getUser()->userID());

                resObj.add_field_double("objSheet", objSheet);
                resObj.add_field_int32("sheetIdx", sheetIdx);
                resObj.add_field_str("sheetName", sheetName);
                WriteRect(resObj, rc);
            }
        }
    }

    return hr;
}

HRESULT TaskExecDbBase::Respond(FieldContext *pFieldContext,
                                    KEtRevisionContext *pRevisionContext,
                                    FieldType fieldType,
                                    bool bClear,
                                    std::vector<RANGE> &rgVec)
{
    HRESULT hr = S_OK;

    EtTaskPeripheral* peripheral = GetPeripheral();
    if(peripheral)
    {
        SetDVReturnCode code = pFieldContext->GetDVCode();
        if (code != errNone)
        {
            hr = E_FAIL;
            ks_wstring str;
            data_validation::MapDvErrorCode(static_cast<int32_t>(code), &str);

            binary_wo::VarObj resObj = peripheral->resSelf();
            resObj.add_field_str("cmdName", GetTag());
            resObj.add_field_uint32("dvResultCode", static_cast<uint32_t>(code));
            resObj.add_field_str("dvResultValue", static_cast<WebStr>(str.c_str()));

            return hr;
        }

		if (rgVec.empty())
			return E_FAIL;
        
        IDX sheetIdx = rgVec[0].SheetFrom();
        ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
        if (spWorksheet)
        {
            WebID objSheet = pRevisionContext->getSheetMain(sheetIdx)->objId();
            ks_bstr sheetName;
            spWorksheet->get_Name(&sheetName);
            {
                binary_wo::VarObj resObj = peripheral->resSelf();
                resObj.add_field_str("cmdName", GetTag());
                resObj.add_field_str("type", Utils::FieldTypeToStr(fieldType));
                resObj.add_field_bool("bClear", bClear);
                resObj.add_field_bool("bEnumAny", pFieldContext->IsEnumAny());

                resObj.add_field_double("objSheet", objSheet);
                resObj.add_field_int32("sheetIdx", sheetIdx);
                resObj.add_field_str("sheetName", sheetName);
                WriteRanges(resObj, rgVec);
            }

            {
                binary_wo::VarObj resObj = peripheral->resOthers();
                resObj.add_field_str("cmdName", GetTag());
                resObj.add_field_str("type", Utils::FieldTypeToStr(fieldType));
                resObj.add_field_bool("bClear", bClear);
                resObj.add_field_bool("bEnumAny", pFieldContext->IsEnumAny());

                resObj.add_field_str("affectedBy", pRevisionContext->getUser()->userID());

                resObj.add_field_double("objSheet", objSheet);
                resObj.add_field_int32("sheetIdx", sheetIdx);
                resObj.add_field_str("sheetName", sheetName);
                WriteRanges(resObj, rgVec);
            }
        }
    }

    return hr;
}
// ================== TaskExecDbBase ==================

// ================== TaskExecDbAddCol ==================
TaskExecDbAddCol::TaskExecDbAddCol(KEtWorkbook* wwb)
    : TaskExecDbBase(wwb)
{}

HRESULT TaskExecDbAddCol::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    IDX sheetIdx = param.field_int32("sheetIdx");

    ks_stdptr<ListObject> spListObject;
    HRESULT hr = Utils::GetDbTable(m_wwb->GetCoreWorkbook(), sheetIdx, &spListObject);
    if (FAILED(hr))
        return hr;

    FieldContext fieldContext(m_wwb, pCtx, param.get_s("args"));

    Database::FieldType fieldType = Database::Utils::StrToFieldType(param.field_str("fieldType"));
    RANGE colRg(m_wwb->GetBMP());
    hr = Utils::DbAddCol(spListObject, &fieldContext, fieldType, colRg);

    EtTaskPeripheral* peripheral = GetPeripheral();
    if(peripheral && SUCCEEDED(hr))
    {
        binary_wo::VarObj resObj = peripheral->resSelf();
        resObj.add_field_str("cmdName", GetTag());
        binary_wo::VarObj rgObj = resObj.add_field_struct("selectRange");
        RECT rc = Range2Rect(colRg);
        WriteRect(rgObj, rc);
        if (pCtx->isInRevisionMode())
            m_wwb->setLastTaskSelection(rgObj);
        WebID objSheet = pCtx->getSheetMain(sheetIdx)->objId();
        rgObj.add_field_double("objSheet", objSheet);

        hr = Respond(&fieldContext, pCtx, fieldType, false, colRg);
    }
    
    return hr;
}

HRESULT TaskExecDbAddCol::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    // IDbField内部判断
    return S_OK;
}

PCWSTR TaskExecDbAddCol::GetTag()
{
    return __X("db.addCol");
}
// ================== TaskExecDbAddCol ==================

// ================== TaskExecDbEditCol ==================
TaskExecDbEditCol::TaskExecDbEditCol(KEtWorkbook* wwb)
    : TaskExecDbBase(wwb)
{}

HRESULT TaskExecDbEditCol::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    IDX sheetIdx = param.field_int32("sheetIdx");
    IDX colIndex = param.field_int32("colIndex");

    ks_stdptr<ListObject> spListObject;
    HRESULT hr = Utils::GetDbTable(m_wwb->GetCoreWorkbook(), sheetIdx, &spListObject);
    if (FAILED(hr))
        return hr;

    FieldContext fieldContext(m_wwb, pCtx, param.get_s("args"));

    Database::FieldType fieldType = Database::Utils::StrToFieldType(param.field_str("fieldType"));
    RANGE colRg(m_wwb->GetBMP());
    hr = Utils::DbEditCol(spListObject, colIndex + 1, &fieldContext, fieldType, colRg);
    CHECK_PROTECTION_ALLOW_EDIT(colRg, pCtx)

    EtTaskPeripheral* peripheral = GetPeripheral();
    if(peripheral && SUCCEEDED(hr))
    {
        binary_wo::VarObj resObj = peripheral->resSelf();
        resObj.add_field_str("cmdName", GetTag());
        binary_wo::VarObj rgObj = resObj.add_field_struct("selectRange");
        RECT rc = Range2Rect(colRg);
        WriteRect(rgObj, rc);
        if (pCtx->isInRevisionMode())
            m_wwb->setLastTaskSelection(rgObj);
        WebID objSheet = pCtx->getSheetMain(sheetIdx)->objId();
        rgObj.add_field_double("objSheet", objSheet);

        pCtx->setIsRealTransform(true);
        binary_wo::VarObj dst = param.add_field_struct("destination"); // 协作记录用
        WriteRect(dst, rc);
        IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(sheetIdx, &spSheet);
        PCWSTR str = NULL;
        spSheet->GetName(&str);
        dst.add_field_str("sheetName", str);

        hr = Respond(&fieldContext, pCtx, fieldType, false, colRg);
    }

    return hr;
}

HRESULT TaskExecDbEditCol::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    // IDbField内部判断
    return S_OK;
}

PCWSTR TaskExecDbEditCol::GetTag()
{
    return __X("db.editCol");
}
// ================== TaskExecDbEditCol ==================

// ================== TaskExecDbAddRow ==================
TaskExecDbAddRow::TaskExecDbAddRow(KEtWorkbook* wwb)
    : TaskExecDbBase(wwb)
{}

HRESULT TaskExecDbAddRow::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    IDX sheetIdx = param.field_int32("sheetIdx");

    ks_stdptr<ListObject> spListObject;
    HRESULT hr = Utils::GetDbTable(m_wwb->GetCoreWorkbook(), sheetIdx, &spListObject);
    if (FAILED(hr))
        return hr;
    
    FieldContext fieldContext(m_wwb, pCtx, param.get_s("args"));

    return Utils::DbAddRow(spListObject, &fieldContext);
}

HRESULT TaskExecDbAddRow::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    // IDbField内部判断
    return S_OK;
}

PCWSTR TaskExecDbAddRow::GetTag()
{
    return __X("db.addRow");
}
// ================== TaskExecDbAddRow ==================

// ================== TaskExecDbSetField ==================
TaskExecDbSetField::TaskExecDbSetField(KEtWorkbook* wwb)
    : TaskExecDbBase(wwb)
{}

HRESULT TaskExecDbSetField::AdjustRange(RANGE &rg)
{
    if (rg.RangeType() == rtCols)
	{
		// 修复bug#990484 整列设置数据有效性时，如果首行已经存在有效性，则带首行一起修改；否则首行不设置有效性
		RANGE colHead(rg);
		colHead.SetRowFromTo(rg.RowFrom(), rg.RowFrom());
		VALIDATION_INFO dvInfo;
		dvInfo.Init();
		m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator()->GetDataValidationInfo(colHead, colHead.SheetFrom(), colHead.RowFrom(), colHead.ColFrom(), &dvInfo);
		if (dvInfo.DVType == DVValueType::dvvtAnyValue)
		{
			rg.SetRowFrom(rg.RowFrom() + 1);
		}
	}
    return rg.IsValid() ? S_OK : E_FAIL;
}

HRESULT TaskExecDbSetField::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    IDX sheetIdx = param.field_int32("sheetIdx");
	std::vector<RANGE> rgVec;
	if (param.has("rgs"))
	{
		ReadRanges(param, rgVec, "rgs");
	}
	else
	{
    	RANGE rg = ReadRange(param);
		if (!rg.IsValid())
        	return E_INVALIDARG;
		rgVec.push_back(rg);
	}

	if (rgVec.empty())
		return E_INVALIDARG;
    

    bool bClear = false;
	FieldType fieldType = dftInvalid;
    if (param.has("bClear"))
        bClear = param.field_bool("bClear");
	if (param.has("fieldType"))
        fieldType = Utils::StrToFieldType(param.field_str("fieldType"));

	FieldsManager *pDbMgr = FieldsManager::Instance();
	FieldContext fieldContext(m_wwb, pCtx, param.get_s("args"));
	app_helper::KBatchUpdateCal batchUpdate(m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator());
	
	HRESULT hr = S_OK;
	for (int i = 0; i < rgVec.size(); ++i)
	{
		RANGE& rg = rgVec.at(i);
		// 整列的话, 不设置第一行
		if (!bClear && FAILED(AdjustRange(rg)))
			return E_FAIL;
		
		if (fieldType == dftInvalid)
		{
			if (!bClear)
				return E_FAIL;

			hr = Utils::ClearAll(&fieldContext, rg);
		}
		else
		{
			if (bClear)
			{
				hr = Utils::ClearType(&fieldContext, fieldType, rg);
			}
			else
			{
				hr = Utils::ClearExcludeType(&fieldContext, fieldType, rg);
				if (FAILED(hr))
					return hr;
				IDbField *pField = pDbMgr->GetField(fieldType);
				if (pField == nullptr)
					return E_FAIL;
				hr = pField->Set(&fieldContext, rg);
			}
		}
	}
    

    if (SUCCEEDED(hr))
    {
        hr = Respond(&fieldContext, pCtx, fieldType, bClear, rgVec);
        IDbField* pField = pDbMgr->GetField(fieldType);
        if (pField == nullptr)
        {
            if (param.has("fieldType"))
                WOLOG_INFO << "[db.setField] Field is null, fieldType is " << param.field_str("fieldType");
            else
                WOLOG_INFO << "[db.setField] Field is null, fieldType is not Exsit)";
        }
        else
        {
            serialFieldExtData(fieldContext.GetOutExtObj(), pField);
        }
    }
        

    return hr;
}

void TaskExecDbSetField::serialFieldExtData(VarObj outExtObj, IDbField* pField)
{
    if (pField == nullptr)
        return;
    // TODO(kingsoft): 这里应该在IDbField里新加一个接口：serialFieldExtData，让每个Field各个序列化自己的扩展数据
    switch(pField->GetType())
    {
    case FieldType::dftAI:
    {
        if (!outExtObj.has("aiLimit"))
            return;
        EtTaskPeripheral* peripheral = GetPeripheral();
        if(!peripheral)
            return;
        VarObj resObj = peripheral->resSelf();    
        resObj.add_field_int32("aiLimit", outExtObj.field_int32("aiLimit"));
        break;
    }
    default:
        break;
    }
}

HRESULT TaskExecDbSetField::CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx)
{
    // IDbField内部判断
    return S_OK;
}


PCWSTR TaskExecDbSetField::GetTag()
{
    return __X("db.setField");
}
// ================== TaskExecDbSetField ==================

// ================== TaskExecDbFillValues ==================
TaskExecDbFillValues::TaskExecDbFillValues(KEtWorkbook* wwb)
    : TaskExecDbBase(wwb)
{}

HRESULT TaskExecDbFillValues::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    class FillEnum : public IFieldEnum
    {
    public:
        FillEnum(PCWSTR formula) : m_formula(formula) {}
        virtual ~FillEnum() {}

        virtual HRESULT Do(FieldContext *pContext, IDbField *pField, const RANGE &rg, const VALIDATION&) override
        {
            ks_stdptr<IRangeInfo> host = pContext->CreateRangeObj(rg);
            RANGE ref(pContext->GetBook()->GetBMP());
            ref.SetCell(rg.SheetFrom(), rg.RowFrom(), rg.ColFrom());
            IEtRevisionContext *ctx = pContext->GetRevisionContext();
            if (ctx == nullptr)
                return E_FAIL;

            CHECK_PROTECTION_FORMULA(rg, m_formula, ctx);
            return host->SetFormula(m_formula, &ref);
        }
    private:
        PCWSTR m_formula;
    };

    binary_wo::VarObj param = cmd->cast().get("param");
    RANGE rg = ReadRange(param);
    if (!rg.IsValid())
        return E_FAIL;

    HRESULT hr = addExclusiveUserRange(pCtx, rg);
    if (FAILED((hr)))
        return  hr;

    FieldsManager *pDbMgr = FieldsManager::Instance();
    FieldType fieldType = Utils::StrToFieldType(param.field_str("fieldType"));

    binary_wo::VarObj args = param.get_s("args");
    FieldContext fieldContext(m_wwb, pCtx, args);

    PCWSTR formula = nullptr;
    if (args.has("formula"))
        formula = args.field_str("formula");
    FillEnum fillEnum(formula);
    return pDbMgr->EnumFieldsInRANGE(&fieldContext, rg, fieldType, dbEnumType, &fillEnum);
}

HRESULT TaskExecDbFillValues::CheckCmdPermission(KwCommand*, KEtRevisionContext* cmd)
{
    // KAppCoreRange::SetFormula内判断
    return S_OK;
}

PCWSTR TaskExecDbFillValues::GetTag()
{
    return __X("db.fillValues");
}
// ================== TaskExecDbFillValues ==================

} // wo
