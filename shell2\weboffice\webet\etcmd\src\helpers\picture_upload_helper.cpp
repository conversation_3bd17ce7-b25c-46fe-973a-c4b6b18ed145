﻿#include "etstdafx.h"
#include "picture_upload_helper.h"
#include "workbook.h"
#include "wo/wo_msgType_helper.h"
#include "webetlink.h"
#include <public_header/drawing/model/abstract_shape.h>
#include <public_header/drawing/wo/serialimage.h>
#include <public_header/etcore/mvc/et_workbook_layer.h>
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include <public_header/opl/mvc/cellimage/et_cellimage_shape.h>
#include <public_header/opl/mvc/cellimage/et_cellimage_shape_tree.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include "utils/attachment_utils.h"
#include "fileInfoCollect.h"
#include "webbase/binvariant/binreader.h"

extern Callback* gs_callback;

namespace wo
{

using namespace binary_wo;
class PerformanceMonitor
{
  public:
    using Key = std::string;
    typedef struct
    {
        std::chrono::steady_clock::time_point begin;
        std::chrono::steady_clock::time_point end;
    } Interval;

    void beginRecord(const char* key)
    {
        auto now = std::chrono::steady_clock::now();
        if (m_record.count(key) == 1)
        {
            m_record.at(key).emplace_back(Interval{ now, now });
        }
        else
        {
            m_record[key] = std::list<Interval>{ Interval{ now, now } };
        }
    }

    void endRecord(const char* key)
    {
        ASSERT(m_record.count(key) == 1);
        auto now     = std::chrono::steady_clock::now();
        auto& record = m_record[key].back();
        record.end   = now;
        WOLOG_INFO
          << "[upload picture record] " << key << " spend "
          << std::chrono::duration<double, std::milli>(record.end - record.begin).count()
          << "ms";
        m_record.erase(key);
    }

    void clear() { m_record.clear(); }

  private:
    std::map<Key, std::list<Interval>> m_record;
};


class ShapePicture : public Picture
{
public:
	ShapePicture(drawing::AbstractShape* pShape) : m_spShape(pShape) {}
	void update(IKBlipAtom* pBlipAtom)
	{
		m_spShape->setPicID(pBlipAtom);
	}
	IKBlipAtom* GetBlipAtom()
	{
		if (!m_spShape->isPicture())
			return nullptr;

		const drawing::Blip& blip = m_spShape->picture()->blip();
		// svg图片直接使用其png缓存图片来上传
		return blip.blipSvg().isValid() ? blip.blipSvg().getCachePngBlipAtom() : blip.blipAtom();
	}
	drawing::Blip::LinkMode GetLinkMode()
	{
		return m_spShape->picture().blip().linkMode();
	}

private:
	ks_stdptr<drawing::AbstractShape> m_spShape;
};

class CommentPicture : public Picture
{
public:
	CommentPicture(IWoCommentImage* pImage) : m_spCommentImage(pImage) {}
	void update(IKBlipAtom* pBlipAtom)
	{
		ks_bstr path;
		pBlipAtom->GetLinkPath(&path);
		WoCommentImageInfo imageInfo = m_spCommentImage->GetInfo();
		imageInfo.id = util::getAttachmentId(path.c_str());
		m_spCommentImage->SetBlip(pBlipAtom);
		m_spCommentImage->SetInfo(imageInfo);
	}
	IKBlipAtom* GetBlipAtom()
	{
		return m_spCommentImage->GetBlip();
	}
	drawing::Blip::LinkMode GetLinkMode()
	{
		if (IKBlipAtom* pBlip = m_spCommentImage->GetBlip())
		{
			ks_bstr linkPath;
			pBlip->GetLinkPath(&linkPath);
			if (linkPath.empty())
				return drawing::Blip::InternalEmbed;
		}
		return drawing::Blip::ExternalLink;
	}
private:
	ks_stdptr<IWoCommentImage> m_spCommentImage;
};

static PerformanceMonitor monitor;
bool PictureUploadHelper::m_uploaded = false;

PictureUploadHelper::PictureUploadHelper(KEtWorkbook* book)
  : m_workbook(book)
  , m_childProcessUploading(false)
{
}

IKBlipAtom* PictureUploadHelper::GetBlipAtom(drawing::AbstractShape* shape)
{
	if (!shape || !shape->isPicture())
		return nullptr;

	const drawing::Blip& blip = shape->picture()->blip();
	// svg图片直接使用其png缓存图片来上传
	return blip.blipSvg().isValid() ? blip.blipSvg().getCachePngBlipAtom() : blip.blipAtom();
}

bool PictureUploadHelper::IsAttachment(IKBlipAtom* pBlipAtom, drawing::Blip::LinkMode linkMode)
{
	if (!pBlipAtom)
		return false;
	if (linkMode != drawing::Blip::ExternalLink)
		return false;
	ks_bstr linkPath;
	pBlipAtom->GetLinkPath(&linkPath);
	return !linkPath.empty();
}

bool PictureUploadHelper::IsSupportUploadFormat(IKBlipAtom* pBlipAtom)
{
	if (!pBlipAtom)
		return false;
	LONG blipType = koplBlipERROR;
	pBlipAtom->GetBlipType(&blipType);
	// "png", "jpg", "jpeg", "bmp", "gif"
	switch (blipType)
	{
		case koplBlipPNG:
		case koplBlipJPEG:
		case koplBlipDIB:
		case koplBlipGIF:
			return true;
		default:
			break;
	}
	return false;
}

PCWSTR PictureUploadHelper::GetUploadFormat(IKBlipAtom* pBlipAtom)
{
	if (!pBlipAtom)
		return nullptr;
	LONG blipType = koplBlipERROR;
	pBlipAtom->GetBlipType(&blipType);
	// "png", "jpg", "jpeg", "bmp", "gif"
	switch (blipType)
	{
		case koplBlipPNG:
			return __X("png");
		case koplBlipJPEG:
			return __X("jpeg");
		case koplBlipDIB:
			return __X("bmp");
		case koplBlipGIF:
			return __X("gif");
		default:
			break;
	}
	return nullptr;
}

bool PictureUploadHelper::GetPicture(IKBlipAtom* pBlipAtom, QByteArray& sha1, QByteArray& picData)
{
	if (!pBlipAtom)
		return false;
	HGBL hgbl = nullptr;
	pBlipAtom->GetHGlobal(&hgbl);
	if (!hgbl)
		return false;
	UINT dataSize = XGlobalSize(hgbl);
	if (dataSize == 0)
		return false;
	char* data = static_cast<char*>(XGlobalLock(hgbl));
	if (data)
		picData.setRawData(data, dataSize);
	XGlobalUnlock(hgbl);

	QCryptographicHash hasher(QCryptographicHash::Sha1);
	hasher.addData(picData);
	sha1 = hasher.result().toHex();
	return true;
}

bool PictureUploadHelper::hasUploadablePicture()
{
    auto shapes = getAllShapes();
	auto iter = std::find_if(shapes.begin(), shapes.end(), [](std::unique_ptr<Picture>& shape) {
		IKBlipAtom* pBlipAtom = shape->GetBlipAtom();
		if (!pBlipAtom)
			return false;
		if (IsAttachment(pBlipAtom, shape->GetLinkMode()) || !IsSupportUploadFormat(pBlipAtom))
			return false;
		HGBL hgbl = nullptr;
		pBlipAtom->GetHGlobal(&hgbl);
		if (!hgbl)
			return false;
		return XGlobalSize(hgbl) > 0;
	});
    m_childProcessUploading = (iter != shapes.end());
    m_beginUploadTimepoint  = std::chrono::steady_clock::now();
    return m_childProcessUploading;
}

enum ExportTag
{
	Normal,
	Et2Ksheet
};

void PictureUploadHelper::parentProcessApplyUploadResult(const WebSlice* recv)
{
    if (!recv)
        return;

    auto shapes = getAllShapes();
    trimShapes(shapes);
    size_t totalShapeNum = shapes.size();
    auto duration        = std::chrono::duration<double, std::milli>(
                      std::chrono::steady_clock::now() - m_beginUploadTimepoint)
                      .count();

    BinReader reader(recv->data, recv->size);
    VarObjRoot root = reader.buildRoot();
    VarObj info     = root.cast();
    VarObj res      = info.get_s("result");
    VarObj fail     = res.get_s("fail");
    VarObj success  = res.get_s("success");
    QString status  = QString::fromUtf16(info.field_str("status"));
	int tag = info.field_int32("export_attachment_tag");
	if (tag == Et2Ksheet)
		m_needBroadcast = false;

    bool spaceFull{ status == "SpaceFull" };
    bool serverError{ status != "OK" };
    if (spaceFull || serverError)
        broadcast(spaceFull ? BroadcastType::spaceFull : BroadcastType::serverError);
    WOLOG_INFO << "[picture upload]upload status=" << status;

    BroadcastType broadcastType = BroadcastType::success;
    for (auto&& key : fail.keys())
    {
        broadcastType   = BroadcastType::partialSuccess;
        QString failMsg = QString::fromUtf16(fail.field_str(key));
        WOLOG_INFO << "[upload picture]fail : " << key << ":" << failMsg;
        if (failMsg == "IllegalPicture" || failMsg == "TimeOut")
            m_illegalOrTimeoutCache.insert(QString(key));
    }

    std::map<SHA1, AttachmentId> successMap;
    for (auto&& key : success.keys())
    {
        m_illegalOrTimeoutCache.erase(QString(key));
        successMap[key] = QString::fromUtf16(success.field_str(key));
        WOLOG_INFO << "[upload picture]success : " << key << ":" << successMap[key];
    }

    std::map<PicId, std::pair<std::list<std::unique_ptr<Picture>>, AttachmentId>>
      result;
    size_t successShapeNum = 0;
    monitor.beginRecord("applyUploadResult");
    for (auto& shape : shapes)
    {
        if (successMap.empty())
            break;

		PicId id = shape->GetBlipAtom();
        if (result.count(id) == 1)
        {
            result.at(id).first.emplace_back(std::move(shape));
            successShapeNum++;
            continue;
        }

        QByteArray sha1;
        QByteArray picData;
		GetPicture(id, sha1, picData);
        if (successMap.count(sha1) == 0)
            continue;
        successShapeNum++;
        result[id] = std::make_pair(std::list<std::unique_ptr<Picture>>(),
                                    successMap.at(sha1));
		result[id].first.emplace_back(std::move(shape));
    }
    monitor.endRecord("applyUploadResult");

    // 埋点
    {
        std::ostringstream oss;
        oss << "behaviour_picture_switch_" << successShapeNum << "_" << totalShapeNum
            << "_" << duration;
        WOLOG_INFO << "[picture upload]埋点=" << oss.str();
        BinWriter binWriter;
        binWriter.addKey("name");
		binWriter.addString(krt::utf16(QString::fromStdString(oss.str())));

        if (m_workbook)
        {
            FileInfoCollector::AddComponentInfo(binWriter, m_workbook->GetCoreWorkbook()->GetBook());
            binWriter.addKey("fileid");
            binWriter.addString(m_workbook->getFileId());
        }

        BinWriter::StreamHolder shbt = binWriter.buildStream();
        WebSlice slice               = { shbt.get(), binWriter.writeLength() };
        gs_callback->collectInfo("fileinclude", &slice);
    }

    if (successMap.empty())
    {
        broadcast(broadcastType);
        return;
    }

    if (!saveResult(result))
    {
        broadcast(BroadcastType::serverError);
        WOLOG_ERROR << "[picture upload] kernel apply upload result error";
        return;
    }

    broadcast(broadcastType);
    updateShapes();
    setUploaded(true);
}

void PictureUploadHelper::childProcessExportUploadResult()
{
    auto shapes = getAllShapes();
    trimShapes(shapes);
    WOLOG_INFO << "[upload pictures] detect " << shapes.size() << " local pictures";
    if (shapes.empty())
        return;

    std::set<SHA1> skipSha1;
    std::set<PicId> skipPicId;
    for (auto&& shape : shapes)
    {
		PicId id = shape->GetBlipAtom();
        if (skipPicId.count(id) == 1)
            continue;
        skipPicId.insert(id);

        QByteArray sha1;
        QByteArray picData;
		GetPicture(id, sha1, picData);
        if (skipSha1.count(sha1) == 1)
            continue;
        skipSha1.insert(sha1);
		QByteArray typeStr = krt::fromUtf16(GetUploadFormat(id)).toUtf8();
		const char* type = typeStr.data();
        WebSlice slice = { (WebByte*)(picData.data()), picData.size() };
        gs_callback->uploadImage(sha1.data(), type, &slice, nullptr);
    }
}

// 仅用于埋点
bool PictureUploadHelper::hasUploaded()
{
    return m_uploaded;
}

void PictureUploadHelper::setUploaded(bool b)
{
    m_uploaded = b;
}

void PictureUploadHelper::updateShapes()
{
    if (m_updateShapes)
        m_updateShapes(m_execCmdConnectId.toLatin1().data(),
                       m_execCmdUserId.toLatin1().data());
}

void PictureUploadHelper::setUpdateShapes(
  std::function<void(const char*, const char*)> func)
{
    if (!m_updateShapes)
        m_updateShapes = func;
}

void PictureUploadHelper::trimShapes(
  std::list<std::unique_ptr<Picture>>& shapesToBeTrim) const
{
    size_t unused1, unused2, unused3;
    trimShapes(shapesToBeTrim, unused1, unused2, unused3);
}

void PictureUploadHelper::trimShapes(
  std::list<std::unique_ptr<Picture>>& shapesToBeTrim,
  size_t& attachmentShapeNum, size_t& emptyShapeNum,
  size_t& unsupportFormatShapeNum) const
{
    monitor.beginRecord("trimShapes");
    size_t total            = shapesToBeTrim.size();
    attachmentShapeNum      = 0;
    emptyShapeNum           = 0;
    unsupportFormatShapeNum = 0;

	shapesToBeTrim.remove_if([&attachmentShapeNum, &emptyShapeNum, &unsupportFormatShapeNum](std::unique_ptr<Picture>& shape) {
		IKBlipAtom* pBlipAtom = shape->GetBlipAtom();
		if (!pBlipAtom)
			return true;
		if (IsAttachment(pBlipAtom, shape->GetLinkMode()))
		{
			++attachmentShapeNum;
			return true;
		}
		if (!IsSupportUploadFormat(pBlipAtom))
		{
			++unsupportFormatShapeNum;
			return true;
		}
		HGBL hgbl = nullptr;
		pBlipAtom->GetHGlobal(&hgbl);
		if (!hgbl || XGlobalSize(hgbl) == 0)
		{
			++emptyShapeNum;
			return true;
		}
		return false;
	});
	if (!m_illegalOrTimeoutCache.empty())
	{
		monitor.beginRecord("trimIllegalOrTimeoutPicture");
		shapesToBeTrim.remove_if([this](std::unique_ptr<Picture>& shape) {
			IKBlipAtom* pBlipAtom = shape->GetBlipAtom();
			QByteArray sha1;
			QByteArray picData;
			GetPicture(pBlipAtom, sha1, picData);
			return m_illegalOrTimeoutCache.count(sha1) == 1;
		});
		monitor.endRecord("trimIllegalOrTimeoutPicture");
	}

    monitor.endRecord("trimShapes");
    WOLOG_INFO << "total=" << total << ", attachment=" << attachmentShapeNum
               << ", unsupport=" << unsupportFormatShapeNum << ", empty=" << emptyShapeNum
               << ", raw=" << shapesToBeTrim.size();
}

void PictureUploadHelper::collectShapeInShapeTree(
  drawing::AbstractShape* shape,
  std::list<std::unique_ptr<Picture>>& container) const
{
    if (!shape)
        return;

    if (shape->isGroupShape())
    {
        drawing::GroupShape* shapeGp = static_cast<drawing::GroupShape*>(shape);
        int childCnt                 = shapeGp->childCount();
        for (int childIdx = 0; childIdx < childCnt; childIdx++)
        {
            drawing::AbstractShape* childShape = shapeGp->childAt(childIdx);
            collectShapeInShapeTree(childShape, container);
        }
    }
    else
    {
        container.emplace_back(std::make_unique<ShapePicture>(shape));
    }
}

void PictureUploadHelper::collectImageInCommentTree(
      ICellComments* pCellComments,
      std::list<std::unique_ptr<Picture>>& container) const
{
	if (!pCellComments)
		return;
	INT cmtCnt = 0;
	pCellComments->GetCount(&cmtCnt);
	for (INT cmtIdx = 0; cmtIdx < cmtCnt; cmtIdx++)
	{
		ks_stdptr<ICellComment> spCellCmt;
		pCellComments->GetItem(cmtIdx, &spCellCmt);
		if (!spCellCmt)
			continue;
		ks_stdptr<IWoComment> spWoCmt;
		spCellCmt->GetWoComment(&spWoCmt);
		if (!spWoCmt)
			continue;
		auto collectCommentInfo = [&](bool bIsResolved)
		{
			for (size_t i = 0; i < spWoCmt->GetChainCount(bIsResolved); ++i)
			{
				IWoCommentChain* pWoCommentChain = spWoCmt->GetChainByIndex(bIsResolved, i);
				if (!pWoCommentChain)
					continue;

				for (size_t j = 0; j < pWoCommentChain->Count(); ++j)
				{
					IWoCommentItem* pItem = pWoCommentChain->GetItem(j);
					if (!pItem)
						continue;

					for (INT k = 0; k < pItem->GetImageCount(); ++k)
					{
						IWoCommentImage* pImage = pItem->GetImage(k);
						if (pImage->GetBlip() != nullptr)
							container.emplace_back(std::make_unique<CommentPicture>(pImage));
					}
				}
			}
		};

		collectCommentInfo(false);
		collectCommentInfo(true);
	}
}

std::list<std::unique_ptr<Picture>> PictureUploadHelper::getAllShapes() const
{
    std::list<std::unique_ptr<Picture>> res;

    // cell picture
    ks_castptr<KEtWorkbookLayer> pLayer = m_workbook->GetCoreWorkbook()->GetRootModel();
	ks_stdptr<IKDrawingCanvas> spCanvas;
	oplGetCellImgOplData(m_workbook->GetCoreWorkbook()->GetBook(), &spCanvas);
	ks_castptr<drawing::ShapeTree> pShapeTree = spCanvas;
    collectShapeInShapeTree(pShapeTree, res);
    size_t cellImageNum = res.size();
    WOLOG_INFO << "[picture upload]detect " << cellImageNum << " cell images";

	size_t floatImageNum = 0, commentImageNum = 0;
    ks_stdptr<IKWorksheets> ptrWorksheets =
      m_workbook->GetCoreWorkbook()->GetWorksheets();
    int sheetCnt = ptrWorksheets->GetSheetCount();
    for (int sheetIdx = 0; sheetIdx < sheetCnt; sheetIdx++)
    {
        ks_stdptr<IKWorksheet> ptrWorksheet = ptrWorksheets->GetSheetItem(sheetIdx);
        if (!ptrWorksheet)
            continue;
        ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
        oplGetSheetOplData(ptrWorksheet->GetSheet(), &spCanvas);
        if (!spCanvas)
            continue;
        ks_castptr<EtShapeTree> pshapeTree = spCanvas;
        if (!pshapeTree)
            continue;

		size_t currentSize = res.size();
        collectShapeInShapeTree(pshapeTree, res);
		floatImageNum += res.size() - currentSize;
		currentSize = res.size();

        ks_stdptr<IKDrawingCanvas> spCommentCanvas;
        oplGetSheetCommentOplData(ptrWorksheet->GetSheet(), &spCommentCanvas);
        if (!spCommentCanvas)
            continue;
        ks_stdptr<ICellComments> spCellComments = spCommentCanvas;
		collectImageInCommentTree(spCellComments, res);
		commentImageNum += res.size() - currentSize;
    }
    WOLOG_INFO << "[picture upload]detect " << floatImageNum
               << " float images";

    WOLOG_INFO << "[picture upload]detect " << commentImageNum
               << " comment images";

    return res;
}

bool PictureUploadHelper::saveResult(
  std::map<PicId, std::pair<std::list<std::unique_ptr<Picture>>, AttachmentId>>&
    result)
{
    HRESULT hr              = S_OK;
    ks_stdptr<_Workbook> wb = m_workbook->GetCoreWorkbook();
    if (!wb)
        return false;

    ks_stdptr<IKMediaManage> spMediaMgr;
    oplGetBookMediaMgr(wb->GetBook(), &spMediaMgr);
    if (!spMediaMgr)
        return false;

    APP_BeginUndoTrans(wb, FALSE, nullptr);
    for (auto& element : result)
    {
        QString attachmentId = element.second.second;
        ASSERT(!attachmentId.isEmpty());

		ks_wstring fakeUrl = util::generateFakeAttachmentUrl(krt::utf16(attachmentId));
		ks_stdptr<IKBlipAtom> spBlipAtom;
		hr = spMediaMgr->AddAtomExternal(koplBlipUNKNOWN, __X(""), nullptr, &spBlipAtom);
		if (FAILED(hr))
			break;
		VS(spBlipAtom->SetLinkPath(fakeUrl.c_str()));
		VS(spBlipAtom->SetBlipName(fakeUrl.c_str()));
		for (auto& shape : element.second.first)
			shape->update(spBlipAtom);
    }
    APP_EndUndoTRANS(hr, TRUE, FALSE);
    m_workbook->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(
      m_workbook->GetCoreWorkbook());

    if (hr != S_OK)
        return false;
    return true;
}

void PictureUploadHelper::queryResponse(ISerialAcceptor* acpt)
{
    if (m_childProcessUploading)
    {
        // 子进程负责上传 不用告诉前端图片数量
        acpt->addBool("isConverting", true);
        return;
    }

    size_t attachmentShapeNumBeforeUpload = 0;
    size_t emptyShapeNum                  = 0;
    size_t unsupportUploadShapeNum        = 0;
    size_t localShapeNum                  = 0;
    // query只要拿到正确的本地图片数量就可以
    auto shapes = getAllShapes();
    trimShapes(shapes, attachmentShapeNumBeforeUpload, emptyShapeNum,
               unsupportUploadShapeNum);
    localShapeNum = shapes.size();
    acpt->addInt32("total", localShapeNum + attachmentShapeNumBeforeUpload);
    acpt->addInt32("raw", localShapeNum);
    acpt->addInt32("success", attachmentShapeNumBeforeUpload);
    acpt->addInt32("unsupport", unsupportUploadShapeNum + emptyShapeNum);
    acpt->addBool("isConverting", false);
}

void PictureUploadHelper::broadcast(BroadcastType type)
{
	if (!m_needBroadcast)
		return;
    BinWriter writer;
    writer.addKey("event");
    switch (type)
    {
        case BroadcastType::partialSuccess:
            writer.addString(__X("partialSuccess"));
            break;
        case BroadcastType::success:
            writer.addString(__X("success"));
            break;
        case BroadcastType::abort:
            writer.addString(__X("abort"));
            break;
        case BroadcastType::converting:
            writer.addString(__X("converting"));
            break;
        case BroadcastType::spaceFull:
            writer.addString(__X("spaceFull"));
            break;
        case BroadcastType::serverError:
        default:
            writer.addString(__X("serverError"));
            break;
    }

    auto shapes = getAllShapes();
    trimShapes(shapes);
    writer.addUint32Field(shapes.size(), "raw");

    BinWriter::StreamHolder sh = writer.buildStream();
    WebSlice slice             = { sh.get(), writer.writeLength() };
    if (gs_callback->broadcast)
        gs_callback->broadcast(getMsgTypeName(msgType_uploadPictureProgress), &slice,
                               nullptr);
}

} // namespace wo
