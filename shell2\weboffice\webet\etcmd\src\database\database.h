﻿#ifndef __WEBET_DATABASE_FIELDS_H__
#define __WEBET_DATABASE_FIELDS_H__

#include "database_def.h"

namespace wo
{
namespace Database
{

class FieldsManager
{
public:
    static FieldsManager *Instance()
    {
        static FieldsManager dbMgr;
        return &dbMgr;
    }

    IDbField *GetField(FieldType);
    HRESULT EnumFieldsInRANGE(FieldContext *, const RANGE &, FieldType, FieldEnumOption, IFieldEnum *);
    IDbField *IdentifyAll(FieldContext *pContext, const RANGE &, VALIDATION);
private:
    FieldsManager();

    std::vector<std::unique_ptr<IDbField>> m_fieldsVec;
    std::map<FieldType, size_t> m_fieldsMap;
};

} // Database
} // wo

#endif // __WEBET_DATABASE_FIELDS_H__