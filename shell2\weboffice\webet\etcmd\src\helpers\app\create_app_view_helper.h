#ifndef __WEBET_CREATE_APP_VIEW_HELPER__
#define __WEBET_CREATE_APP_VIEW_HELPER__

#include "etstdafx.h"
#include "workbook.h"

class KEtWorkbook;

namespace CreateAppViewHelper
{
	HRESULT CreateAppView(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, binary_wo::VarObj &param,
		OUT UINT& appSheetStId, OUT UINT& srcSheetStId, OUT EtDbId& appViewId, OUT ks_wstring& appName,
		OUT UINT& airAppSheetStId, OUT EtDbId& airAppId, OUT ks_wstring& errName);
	HRESULT createAirApp(wo::KEtWorkbook* pWorkbook, IDBSheetView* pView, IN const ks_wstring& setAppName, IN IDX specAfterSheetIdx, OUT ks_wstring& appName, OUT UINT& airAppSheetStId, OUT EtDbId& airAppId);
	HRESULT resetAirApp(wo::KEtWorkbook* pWorkbook, IDBSheetView* pDbSheetView, UINT airAppSheetStId, EtDbId airAppId, PCWSTR sharedId, OUT ks_wstring& airAppName);
	HRESULT BuildEtDbRelation(ISheet* pEtSheet, UINT relatedDbSheetStId, ROW titleBeginIdx, ROW titleEndIdx, COL colBegin, COL colEnd);
}//end namespace CreateAppViewHelper

#endif // __WEBET_CREATE_APP_VIEW_HELPER__
