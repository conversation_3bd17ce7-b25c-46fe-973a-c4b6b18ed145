
#ifndef __WEBET_PROTECTION_CONTEXT_IMP_H__
#define __WEBET_PROTECTION_CONTEXT_IMP_H__

#include "wo/et_revision_context.h"

namespace wo
{

class KEtWorkbook;
class IWorkbookObj;
class KEtRevisionContext;

class KProtectionContext : public IEtProtectionCtx
{
  public:
    KProtectionContext(etoldapi::_Workbook* pApiWb, IWorkbookObj *workbookObj, KEtRevisionContext *ctx);
    virtual ~KProtectionContext();

    bool isAllHidden() override;
    bool isCellHidden(ISheet *isheet, INT32 row, INT32 col) override;
    bool isRangeHasHidden(const RANGE &rg, bool ignoreShared = false) override;
    bool isRangeHasHidden(IKRanges *rgs, bool ignoreShared = false) override;
    bool isRangeHasHidden(ISheet *isheet, const RANGE &rg, bool ignoreShared = false) override;
    void splitVisibleRange(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg) override;
    void splitHiddenRange(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges) override;
    void splitVisibleRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg) override;
    void splitHiddenRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges) override;

    bool isRangeAllHidden(const RANGE &rg) override;

    bool isBookHasHidden(IKWorkbook *workbook) override;
    bool isBookHasHidden(IBook *ibook) override;
    bool isBookHasHidden() override;

    bool isBookHasHiddenProperty(IBook *ibook) override;
    bool isBookHasHiddenProperty() override;
    bool isRangeHasHiddenProperty(const RANGE &rg, bool checkUnprotected = false) override;

    bool isBookHasReadonly() override;
    bool isBookHasReadonly(IBook *ibook) override;

    bool isBookHasReadonlyProperty() override;
    bool isBookHasReadonlyProperty(IBook *ibook) override;

    bool isSheetHidden(UINT stId) override;
    bool isSheetHasHidden(ISheet *isheet) override;
    bool isSheetHasHiddenProperty(ISheet *isheet) override;
    bool isSheetHasReadonly(ISheet *isheet) override;
    bool isSheetHasReadonlyProperty(ISheet *isheet) override;

    bool isSheetShared(UINT stId) override;

    bool isSheetHasEditable(ISheet *isheet) override;
    bool isThisBookHasEditable() override;
    bool isBookHasSheetProtected() override;

    bool isCellImageFormula(const_token_vector vecToken) override;
    bool isCellImageFormula(PCWSTR) override;

    HRESULT checkAllowEdit(const RANGE &rg) override;
    HRESULT checkAllowEditWidthLockedCell(const RANGE &rg) override;
    HRESULT isOperationAllowedProtected(const RANGE &rg, et_appcore::ActionType acType) override;
    
    // only allow edit for [allow edit range], not checking for locked cell
    bool isAllowEdit(const RANGE &rg, ProtectionAccessPerms *right) override;
    bool isAllowEdit(IKRanges *rgs, ProtectionAccessPerms *right) override;
    bool isAllowEdit(const std::vector<RANGE> &rg, ProtectionAccessPerms *right) override;

	  bool isAllowedFormula(PCWSTR formula) override;
    HRESULT checkFormula(IDX iSheet, ROW row, COL col, const_token_vector vecToken) override;
    HRESULT checkFormula(const RANGE &rg, const_token_vector vecToken) override;
    HRESULT checkFormula(IDX iSheet, ROW row, COL col, PCWSTR) override;
    HRESULT checkFormula(const RANGE &rg, PCWSTR) override;
    HRESULT checkFormula(IDX iSheet, ROW row, COL col, const_token_ptr) override;

    HRESULT checkDVFormula(const RANGE &rg, const VALIDATION *validation) override;
    HRESULT checkDVFormula(const RANGE &rg, DVValueType dvType, PCWSTR pFormula1, PCWSTR pFormula2) override;
    HRESULT checkDVFormula(const RANGE &rg, DVValueType dvType, ITokenVectorInstant *pFormula1, ITokenVectorInstant *pFormula2) override;
    HRESULT checkDVFormulaInRange(const RANGE &rg) override;

    HRESULT checkCFFormula(const RANGE &appliedRg, ICondFmt *condFmt) override;
    HRESULT checkCFFormulaInRange(const RANGE &rg) override;

    HRESULT checkProtectionContent(const RANGE &rg, uint32_t *pckKind = nullptr) override;
    HRESULT checkProtectionCopyContent(const RANGE &rg) override;
    uint32_t getProtectionContentKind(const RANGE &rg, bool isAll = false) override;

    bool isProtectionAreaUnlock(LPCWSTR id) override;
    void clearProtectionCache() override;

    void setValidRefKind(uint32_t k) override;

    bool isCompileFormula() const override;
    void setCompileFormula(bool) override;

    uint32_t getPasteContentKind() const override;
    void setPasteContentKind(uint32_t kind) override;

    bool allowDeleteLockedRowCol() const override;
    void setAllowDeleteLockedRowCol(bool isAllowed) override;

    int currentCommand() const override;
    void setCurrentCommand(int cmd) override;
    bool getAutoPassword(PCWSTR srcUUID, ks_wstring *outPwd) override;

    void setHasProtectionCache(bool bl) override;
    void setupProtectionCache(ISheet *spSheet) override;

    void clearEtContext() override;
    void setupEtContext() override;
    bool needCheckInsertDeleteHiddenRange(const RANGE &rg) override;
    bool canRefColProtect(const RANGE& rg) override;
    bool hasHiddenColProtect(ISheet *spSheet, const RANGE& rg) override;
    bool isBookHasColProtect() override;
    void onUserColPermsChanged(LPCWSTR userId) override;
    void updateUserColPermsStatus(LPCWSTR userId) override;

    void markAllUsersColPermsChanged(LPCWSTR excludeId) override;
    bool isAllUsersColPermsChanged() override;
    
    bool isConnSharedLink() override;

    ProtectionType getPasteProtectionType() const override;
    void setPasteProtectionType(ProtectionType type) override;

    bool checkExclusive(const RANGE &rg, ProtectionAccessPerms *right);
	bool isUserRangeInvisible(const RANGE &rg) override;	// 是否保护区域不可见
	bool isCanReadWholeBook() override;
	bool isCanEditWholeBook() override;

  public:
    bool hasProtectionCache() const;

    bool ensureProtectionCacheSheets(ISheet *isheet);
    void ensureProtectionAreaCache(ISheet *isheet, ISheetProtection *pSheetProtection);

    bool isBookHasHiddenCache() const;
    bool isBookHasHiddenPropCache() const;
    bool isBookHasReadonlyCache() const;
    bool isBookHasReadonlyPropCache() const;
    bool isBookHasColProtectCache() const;

    ks_stdptr<ISheetProtection> getSheetProtection(ISheet *isheet);
    IBook *getBook();

  private:
    HRESULT isValidFormula(IDX iSheet, ROW row, COL col, const_token_vector vecToken);
    HRESULT isValidFormula(const RANGE &rg, const_token_vector vecToken);

    KProtectionContext(const KProtectionContext &);
    KProtectionContext &operator=(const KProtectionContext &);
    ProtectionAccessPerms getAccessPermWidthRange(const RANGE &rg);
    bool checkProtectionOnSheet(IDX sheetIndex);
    bool isSheetHasProtectedCols(ISheet* pSheet);
    PCWSTR getSharedLink();
    bool isAppSheetCanVisit(ISheet* pSheet);
    bool needCheckFormula();

    IBook *m_ibook;
    KEtRevisionContext *m_ctx;
    uint32_t m_validRefKind;
    etoldapi::_Workbook* m_pApiWb;
    IWorkbookObj *m_workbookObj;
    bool m_hasProtectionCache;

    bool m_hasSheetProtectedCache;
    bool m_isBookHasEditableCache;

    bool m_isBookHasHiddenCache;
    bool m_isBookHasHiddenPropCache;
    bool m_isBookHasReadonlyCache;
    bool m_isBookHasReadonlyPropCache;
    bool m_isBookHasColProtectCache;
    std::unordered_set<WebID> m_protectionCacheSheets;

    bool m_isCompileFormula;
    bool m_isAllowDelLockedRowCol;
    uint32_t m_pasteContent;
    int m_woCommand; // WoCommandTag
    std::set<ks_wstring> m_userColpermsChanged;
    bool m_isAllUserColPermsChanged;

    bool m_isConnSharedLinkCache;
    bool m_hasConnSharedLinkCache;
    ks_stdptr<ISharedLink> m_spSharedLink;

    ProtectionType m_protectionType;
};

} // namespace wo

#endif // __WEBET_PROTECTION_CONTEXT_IMP_H__