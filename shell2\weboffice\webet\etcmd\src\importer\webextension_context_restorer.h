#ifndef __WEBET_WEBEXTENSION_CONTEXT_RESTORER_H
#define __WEBET_WEBEXTENSION_CONTEXT_RESTORER_H

namespace wo
{
class WebExtensionContextRestorer
{
public:
    WebExtensionContextRestorer(etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook,
                                std::unordered_map<UINT, UINT>& sheetIdMap);
    void RestoreSheet(ISheet* pSheet);
private:
    void RestoreWebExtension(IKWebExtension* pWebExtension);
    HRESULT GetNewContext(PCWSTR context, BSTR* pNewContext);
private:
    etoldapi::_Workbook* m_pSrcWorkbook;
    etoldapi::_Workbook* m_pTarWorkbook;
    std::unordered_map<UINT, UINT>& m_sheetIdMap;
};
}

#endif //__WEBET_WEBEXTENSION_RESTORER_H
