﻿#include "etstdafx.h"
#include "workbook.h"
#include "db_dashboard_task_class.h"
#include "db_chart_wrapper.h"
#include "db_dashboard_module_mgr_wrapper.h"
#include "appcore/et_appcore_enum.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "src/util.h"
#include "et_task_peripheral.h"
#include "et_revision_context_impl.h"
#include "db_plugin_wrapper.h"
#include "db_view_wrapper.h"
#include "dbsheet/et_dbsheet_filter_helper.h"
#include "dashboard/et_dashboard_utils.h"

namespace wo
{

// ================== TaskExecDbChartSetStyle ==================
TaskExecDbChartSetStyle::TaskExecDbChartSetStyle(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbChartSetStyle::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(sheetStId)))
        return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    if (!param.has("chartId") || !param.has("chartElementType") || !param.has("identifier") || !param.has("style"))
        return E_INVALIDARG;

    PCWSTR chartId = param.field_str("chartId");
    std::unique_ptr<KDbChartWrapper> pChartWrapper = dashboardModuleMgrWrapper.GetChart(chartId);
    if (!pChartWrapper)
        return E_FAIL;

    ET_DBSheet_ChartElementType chartElementType = DbSheet_Chart_ET_Category;
    hr = m_pEncodeDecoder->DecodeDbChartElementType(param.field_str("chartElementType"), &chartElementType);
    if (FAILED(hr))
        return hr;

    PCWSTR identifier = param.field_str("identifier");
    PCWSTR style = param.field_str("style");
    hr = pChartWrapper->SetStyle(chartElementType, identifier, style);
    if (FAILED(hr))
        return hr;
    return S_OK;
}

HRESULT TaskExecDbChartSetStyle::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbChartSetStyle::GetTag()
{
    return __X("db.chart.setStyle");
}

// ================== TaskExecDbChartRemoveStyle ==================
TaskExecDbChartRemoveStyle::TaskExecDbChartRemoveStyle(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbChartRemoveStyle::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(sheetStId)))
        return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    if (!param.has("chartId") || !param.has("chartElementType") || !param.has("identifier"))
        return E_INVALIDARG;

    PCWSTR chartId = param.field_str("chartId");
    std::unique_ptr<KDbChartWrapper> pChartWrapper = dashboardModuleMgrWrapper.GetChart(chartId);
    if (!pChartWrapper)
        return E_FAIL;

    ET_DBSheet_ChartElementType chartElementType = DbSheet_Chart_ET_Category;
    hr = m_pEncodeDecoder->DecodeDbChartElementType(param.field_str("chartElementType"), &chartElementType);
    if (FAILED(hr))
        return hr;

    PCWSTR identifier = param.field_str("identifier");
    hr = pChartWrapper->RemoveStyle(chartElementType, identifier);
    if (FAILED(hr))
        return hr;
    return S_OK;
}

HRESULT TaskExecDbChartRemoveStyle::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbChartRemoveStyle::GetTag()
{
    return __X("db.chart.removeStyle");
}

// ================== TaskExecDbChartSetOrder ==================
TaskExecDbChartSetOrder::TaskExecDbChartSetOrder(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbChartSetOrder::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(sheetStId)))
        return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    if (!param.has("chartId") || (!param.has("sortType") && !param.has("seriesSortType") && !param.has("customOrder") && !param.has("ascending") && !param.has("customSeriesOrder")))
        return E_INVALIDARG;

    PCWSTR chartId = param.field_str("chartId");
    std::unique_ptr<KDbChartWrapper> pChartWrapper = dashboardModuleMgrWrapper.GetChart(chartId);
    if (!pChartWrapper)
        return E_FAIL;

    if (param.has("sortType"))
    {
        ET_DBSheet_ChartSortType sortType = DbSheet_Chart_ST_NoSort;
        hr = m_pEncodeDecoder->DecodeDbChartSortType(param.field_str("sortType"), &sortType);
        if (FAILED(hr))
            return hr;

        hr = pChartWrapper->SetSortType(sortType);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("seriesSortType"))
    {
        ET_DBSheet_ChartSortType seriesSortType = DbSheet_Chart_ST_NoSort;
        hr = m_pEncodeDecoder->DecodeDbChartSortType(param.field_str("seriesSortType"), &seriesSortType);
        if (FAILED(hr))
            return hr;

        hr = pChartWrapper->SetSeriesSortType(seriesSortType);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("customOrder"))
    {
        binary_wo::VarObj customOrder = param.get_s("customOrder");
        hr = pChartWrapper->SetCustomCategoryOrder(customOrder);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("customSeriesOrder"))
    {
        binary_wo::VarObj customOrder = param.get_s("customSeriesOrder");
        hr = pChartWrapper->SetCustomSeriesOrder(customOrder);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("ascending"))
    {
        hr = pChartWrapper->SetSortAscending(param.field_bool("ascending"));
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT TaskExecDbChartSetOrder::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbChartSetOrder::GetTag()
{
    return __X("db.chart.setOrder");
}

// ================== TaskExecDbModifyPluginConfig ==================
TaskExecDbModifyPluginConfig::TaskExecDbModifyPluginConfig(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbModifyPluginConfig::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
    IDX sheetIdx = GetSheetIdx(param);
	ks_stdptr<_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!spWorksheet)
		return E_FAIL;
	ISheet* pSheet = spWorksheet->GetSheet();
    if (!(pSheet->IsDbDashBoardSheet()))
        return E_FAIL;

    if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(pSheet->GetStId())))
		return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

    if (!param.has("webExtensionKey"))
        return E_INVALIDARG;
    PCWSTR webExtensionKey = param.field_str("webExtensionKey");
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    std::unique_ptr<KDbDashboardPluginWrapper> pPluginWrapper = dashboardModuleMgrWrapper.GetPlugin(webExtensionKey);
    if (!pPluginWrapper)
        return E_INVALIDARG;
    if (!param.has("pluginConfig"))
        return E_INVALIDARG;
    HRESULT hr = pPluginWrapper->SetPluginConfig(param.get("pluginConfig"), pCtx);
    if (FAILED(hr))
        return hr;
	return S_OK;
}

HRESULT TaskExecDbModifyPluginConfig::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return  S_OK;
}

PCWSTR TaskExecDbModifyPluginConfig::GetTag()
{
	return __X("db.dashboard.modifyPluginConfig");
}

// ================== TaskExecDbDashboardCreateFilter ==================
TaskExecDbDashboardCreateFilter::TaskExecDbDashboardCreateFilter(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDashboardCreateFilter::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
    if (FAILED(hr))
        return hr;

    if (!param.has("name"))
        return E_INVALIDARG;

    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    IDbDashboardFilterMgr* pFilterMgr = spDbDashboard->GetFilterMgr();
    UINT16 maxFilterCount = _kso_GetWoEtSettings()->GetDbDashboardMaxFilterCount();
    if (!pCtx->isExecDirect() && pFilterMgr->GetCount() >= maxFilterCount)
        return E_DBSHEET_DASHBOARD_FILTER_COUNT_LIMIT;

    ks_stdptr<IDbDashboardFilter> spFilter;
    PCWSTR name = param.field_str("name");
    if (!pCtx->isExecDirect() && !KDbDashboardModuleMgrWrapper::IsValidFilterName(name))
        return E_DBSHEET_DASHBOARD_INVALID_FILTER_NAME;

    hr = pFilterMgr->CreateFilter(name, &spFilter);
    if (FAILED(hr))
        return hr;

    if (param.has("multiSheetFilter"))
    {
        bool multiSheetFilter = param.field_bool("multiSheetFilter");
        hr = spFilter->SetMultiSheetFilter(multiSheetFilter);
        if (FAILED(hr))
            return hr;
    }

    if (param.has("filterFields"))
    {
        binary_wo::VarObj filterFields = param.get_s("filterFields");
        hr = spFilter->SetFilterFields(filterFields);
        if (FAILED(hr))
            return hr;
    }

    EtTaskPeripheral* peripheral = GetPeripheral();
    if(peripheral)
    {
        binary_wo::VarObj resObj = peripheral->resSelf();
        AddEtDbId(resObj, "filterId", spFilter->GetId());
    }
    return S_OK;
}

HRESULT TaskExecDbDashboardCreateFilter::CheckCmdPermission(KwCommand* pCmd, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbDashboardCreateFilter::GetTag()
{
    return __X("db.dashboard.createFilter");
}

// ================== TaskExecDbDashboardUpdateFilter ==================
TaskExecDbDashboardUpdateFilter::TaskExecDbDashboardUpdateFilter(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDashboardUpdateFilter::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
    if (FAILED(hr))
        return hr;

    if (!param.has("filterId"))
        return E_INVALIDARG;

    if (!param.has("name") && !param.has("filterFields") && !param.has("multiSheetFilter"))
        return E_INVALIDARG;

    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    EtDbId filterId = GetEtDbId(param, "filterId");
    ks_stdptr<IDbDashboardFilter> spFilter;
    hr = spDbDashboard->GetFilterMgr()->GetFilter(filterId, &spFilter);
    if (FAILED(hr))
        return hr;

    if (param.has("name"))
    {
        PCWSTR name = param.field_str("name");
        if (!pCtx->isExecDirect() && !KDbDashboardModuleMgrWrapper::IsValidFilterName(name))
            return E_DBSHEET_DASHBOARD_INVALID_FILTER_NAME;

        hr = spFilter->SetName(name);
        if (FAILED(hr))
            return hr;
    }

    if (param.has("multiSheetFilter"))
    {
        bool multiSheetFilter = param.field_bool("multiSheetFilter");
        hr = spFilter->SetMultiSheetFilter(multiSheetFilter);
        if (FAILED(hr))
            return hr;
    }

    if (param.has("filterFields"))
    {
        binary_wo::VarObj filterFields = param.get_s("filterFields");
        hr = spFilter->SetFilterFields(filterFields);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT TaskExecDbDashboardUpdateFilter::CheckCmdPermission(KwCommand* pCmd, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbDashboardUpdateFilter::GetTag()
{
    return __X("db.dashboard.updateFilter");
}

// ================== TaskExecDbDashboardDeleteFilter ==================
TaskExecDbDashboardDeleteFilter::TaskExecDbDashboardDeleteFilter(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDashboardDeleteFilter::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    EtDbId filterId = GetEtDbId(param, "filterId");
    return spDbDashboard->GetFilterMgr()->RemoveFilter(filterId);
}

HRESULT TaskExecDbDashboardDeleteFilter::CheckCmdPermission(KwCommand* pCmd, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbDashboardDeleteFilter::GetTag()
{
    return __X("db.dashboard.deleteFilter");
}

// ================== TaskExecDbDashboardMoveFilter ==================
TaskExecDbDashboardMoveFilter::TaskExecDbDashboardMoveFilter(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDashboardMoveFilter::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
    if (FAILED(hr))
        return hr;

    EtDbId filterId = GetEtDbId(param, "filterId");
    DbIdPostion tar;
    GetDBPos(param.get_s("pos"), tar);
    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    return spDbDashboard->GetFilterMgr()->MoveFilter(filterId, tar);
}

HRESULT TaskExecDbDashboardMoveFilter::CheckCmdPermission(KwCommand* pCmd, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbDashboardMoveFilter::GetTag()
{
    return __X("db.dashboard.moveFilter");
}

// ================== TaskExecDbDashboardSetFilterCriteria ==================
TaskExecDbDashboardSetFilterCriteria::TaskExecDbDashboardSetFilterCriteria(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDashboardSetFilterCriteria::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    if (pCtx->isExecDirect())
        return S_OK;

    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDbFilterCriteria> spCriteria;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    hr = CreateDbFilterCriteria(param.get("criteria"), pBook, &spCriteria);
    if (FAILED(hr))
        return hr;

    EtDbId filterId = GetEtDbId(param, "filterId");
    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    ks_stdptr<IDbDashboardFilter> spFilter;
    hr = spDbDashboard->GetFilterMgr()->GetFilter(filterId, &spFilter);
    if (FAILED(hr))
        return hr;

    pCmd->disableWriteCooperationRecord();
    return spFilter->SetConnCriteria(spCriteria);
}

HRESULT TaskExecDbDashboardSetFilterCriteria::CheckCmdPermission(KwCommand* pCmd, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbDashboardSetFilterCriteria::GetTag()
{
    return __X("db.dashboard.setFilterCriteria");
}

// ================== TaskExecDbDashboardClearFilterCriteria ==================
TaskExecDbDashboardClearFilterCriteria::TaskExecDbDashboardClearFilterCriteria(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDashboardClearFilterCriteria::operator()(KwCommand* pCmd, KEtRevisionContext* pCtx)
{
    if (pCtx->isExecDirect())
        return S_OK;

    binary_wo::VarObj param = pCmd->cast().get("param");
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
    if (FAILED(hr))
        return hr;

    EtDbId filterId = GetEtDbId(param, "filterId");
    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    ks_stdptr<IDbDashboardFilter> spFilter;
    hr = spDbDashboard->GetFilterMgr()->GetFilter(filterId, &spFilter);
    if (FAILED(hr))
        return hr;

    pCmd->disableWriteCooperationRecord();
    return spFilter->SetConnCriteria(nullptr);
}

HRESULT TaskExecDbDashboardClearFilterCriteria::CheckCmdPermission(KwCommand* pCmd, KEtRevisionContext*)
{
    return S_OK; // 内部限制
}

PCWSTR TaskExecDbDashboardClearFilterCriteria::GetTag()
{
    return __X("db.dashboard.clearFilterCriteria");
}

// ================== TaskExecDbDashboardAddView ==================
TaskExecDbDashboardAddView::TaskExecDbDashboardAddView(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDashboardAddView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT stId = param.field_uint32("sheetStId");
	IDX sheetIdx = INVALIDIDX;
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	pWorkbook->GetBook()->STSheetToRTSheet(stId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_INVALIDARG;
	ks_stdptr<_Worksheet> spWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!spWorksheet)
		return E_FAIL;
	ISheet* pSheet = spWorksheet->GetSheet();
	if (!(pSheet->IsDbDashBoardSheet()))
		return E_FAIL;
	
	if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(stId)))
		return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

	ks_stdptr<etoldapi::Shapes> spShapes;
	HRESULT hr = spWorksheet->get_Shapes(FALSE, &spShapes);
	if (!spShapes)
		return E_FAIL;

    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
	if (!pCtx->isExecDirect())
	{
        int shapesCnt = 0;
        spShapes->get_Count(&shapesCnt);
        if (shapesCnt >= DashBoard::GetDashBoardModuleLimit())
            return E_DBSHEET_DASHBOARD_CHART_COUNT_LIMIT;

		hr = dashboardModuleMgrWrapper.CheckCanAddView();
		if (FAILED(hr))
			return hr;
	}

	ks_bstr webExtensionKey(param.field_str("webExtensionKey"));
	if (webExtensionKey.empty())
		return E_FAIL;

	if (!param.has("webExtType"))
		return E_INVALIDARG;
	WebExtType webExtType = WET_Normal;
	hr = _webextension_GainEncodeDecoder()->DecodeWebExtType(param.field_str("webExtType"), &webExtType);
	if (FAILED(hr))
		return hr;
	if (webExtType != WET_DbView)
		return E_INVALIDARG;

	ks_stdptr<KsoShape> spKsoShape;
	hr = spShapes->AddWebExtension(webExtensionKey, nullptr, webExtType, &spKsoShape);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IKsoShapeEx> spShapeEx = spKsoShape;
	if (!spShapeEx)
		return E_FAIL;
	ks_stdptr<IKShape> spCoreShape;
	spShapeEx->GetInterface(IID_IKShape, (void**)&spCoreShape);
	drawing::AbstractShape* pAbsShape = static_cast<drawing::AbstractShape*>(spCoreShape.get());
	IKWebExtension* pWebExtension = pAbsShape->getWebExtension();

	bool duplicate = param.has("duplicate") && param.field_bool("duplicate");
	UINT sheetId = 0;
	EtDbId viewId = INV_EtDbId;
	if (duplicate)
	{
		IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetWebExtensionMgr();
		if (!pWebExtensionMgr)
			return E_FAIL;
		IKWebExtension* pOldWebExtension = pWebExtensionMgr->FindWebExtension(sheetIdx, param.field_str("oldWebExtensionKey"));
		if (!pOldWebExtension)
			return E_INVALIDARG;
		hr = pOldWebExtension->CopyTo(pWebExtension);
		if (FAILED(hr))
			return hr;
        ks_stdptr<IEtWebExtension_View> spOldWebExtView = pOldWebExtension;
		sheetId = spOldWebExtView->GetSheetId();
		viewId = spOldWebExtView->GetViewId();
	}
	else
	{
		hr = pWebExtension->SetProperty(GetWebofficeUniquePropertyKey(), __X("true"));
		if (FAILED(hr))
			return hr;
	}
	hr = DashBoard::SetWebextentionProperty(param, pWebExtension);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	std::unique_ptr<KDbDashboardViewWrapper> viewWrapper = dashboardModuleMgrWrapper.GetView(webExtensionKey.c_str());
	if (duplicate)
	{
		hr = viewWrapper->CopyFrom(sheetId, viewId, &spDbSheetView);
	}
	else
	{
        if (!param.has("viewSetting"))
            return E_INVALIDARG;
		sheetId = param.get("viewSetting").field_uint32("sheetId");
		hr = viewWrapper->CreateView(param.get("viewSetting"), pCtx, &spDbSheetView);
	}
	if (FAILED(hr))
		return hr;
	WebExtInfo info;
	info.sheetStId = stId;
	info.webExtKey = webExtensionKey.c_str();
	spDbSheetView->SetWebExtInfo(info);

    ks_stdptr<IEtWebExtension_View> spWebExtView = pWebExtension;
	spWebExtView->SetSheetId(sheetId);
	spWebExtView->SetViewId(spDbSheetView->GetId());

	hr = viewWrapper->SetViewShared(spWorksheet, sheetId, spDbSheetView->GetId());
	if (FAILED(hr))
		return hr;

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		if (param.has("callBackId"))
			resObj.add_field_uint32("callBackId", param.field_uint32("callBackId"));
		resObj.add_field_uint32("shapeId", pAbsShape->id());
		resObj.add_field_int32("hr", hr);
	}
	return S_OK;
}

HRESULT TaskExecDbDashboardAddView::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return  S_OK ;
}

PCWSTR TaskExecDbDashboardAddView::GetTag()
{
	return __X("db.dashboard.addView");
}

// ================== TaskExecDbDashboardModifyView ==================
TaskExecDbDashboardModifyView::TaskExecDbDashboardModifyView(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDashboardModifyView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	ks_stdptr<KsoShape> spKsoShape;
	GetApiShape(param, pCtx, &spKsoShape);
	ks_stdptr<IKsoShapeEx> spShapeEx = spKsoShape;
	if (!spShapeEx)
		return E_FAIL;
	ks_stdptr<IKShape> spCoreShape;
	spShapeEx->GetInterface(IID_IKShape, (void**)&spCoreShape);
	drawing::AbstractShape* pAbsShape = static_cast<drawing::AbstractShape*>(spCoreShape.get());
	if (!pAbsShape || !pAbsShape->isWebExtensionHostShape())
		return E_FAIL;

    IKWebExtension* pWebExtension = pAbsShape->getWebExtension();
    if (!pWebExtension)
        return E_FAIL;

    IDX sheetIdx = GetSheetIdx(param);
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheet* pWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWorksheet)
		return E_FAIL;
	ISheet* pSheet = pWorksheet->GetSheet();
    if (!(pSheet->IsDbDashBoardSheet()))
        return E_FAIL;

    if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(pSheet->GetStId())))
		return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

	ks_stdptr<IDBDashBoardDataOp> spDbDashBoardOp;
	VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashBoardOp));
	if (pWebExtension->GetWebShapeType() != WET_DbView)
		return E_FAIL;

	HRESULT hr = S_OK;
	if (param.has("viewSetting"))
	{
        PCWSTR webextensionKey = pWebExtension->GetWebExtensionKey();
        ks_stdptr<IEtWebExtension_View> spWebExtView = pWebExtension;
        UINT oldSheetId = spWebExtView->GetSheetId();
		ks_stdptr<_Worksheet> spWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
		KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
		std::unique_ptr<KDbDashboardViewWrapper> viewWrapper = dashboardModuleMgrWrapper.GetView(webextensionKey);
		ks_stdptr<IDBSheetView> spDbSheetView;
		hr = viewWrapper->ModifyView(param.get("viewSetting"), pCtx, &spDbSheetView);
		if (FAILED(hr))
			return hr;
		if (spDbSheetView)
		{
			WebExtInfo info;
			info.sheetStId = pSheet->GetStId();
			info.webExtKey = webextensionKey;
			spDbSheetView->SetWebExtInfo(info);
			spDbDashBoardOp->GetFilterMgr()->OnViewDataSourceChange(oldSheetId);

			hr = viewWrapper->SetViewShared(spWorksheet, spWebExtView->GetSheetId(), spDbSheetView->GetId());
			if (FAILED(hr))
				return hr;
		}
	}

	hr = DashBoard::SetWebextentionProperty(param, pWebExtension);
	if (FAILED(hr))
		return hr;

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_uint32("shapeId", pAbsShape->id());
	}
	return S_OK;
}

HRESULT TaskExecDbDashboardModifyView::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return  S_OK;
}

PCWSTR TaskExecDbDashboardModifyView::GetTag()
{
	return __X("db.dashboard.modifyView");
}

}