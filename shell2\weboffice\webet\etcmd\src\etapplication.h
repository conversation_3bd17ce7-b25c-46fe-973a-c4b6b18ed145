﻿#ifndef __ETAPPLICATION_H__
#define __ETAPPLICATION_H__

#include "etstdafx.h"
#include "etshared/etshareapplication.h"

#include "etentrys.h"
#include "workbooks.h"
#include "krt/katexit.h"
#include "webbase/stale_notify.h"

namespace wo
{

struct EventDispatcherCallInfo;

class IntervalTaskManager
{
public:
	IntervalTaskManager()
	{
		m_lastTime = std::chrono::steady_clock::now();
	}

	void OnIdle(wo::KEtWorkbook* wb, bool isMasterProc);

	template<typename T>
	void expiredAt(int secs, bool repeat, T&& func)
	{
		m_tasks.push_back(IntervalTask{secs, secs, repeat, func});
	}

private:
	struct IntervalTask
	{
		int secs = 0;
		int expired_secs = 0;
		bool repeat = false;
		std::function<void(KEtWorkbook* wb, bool)> task;
	};

	std::chrono::steady_clock::time_point m_lastTime;
	std::vector<IntervalTask> m_tasks;
};

class KEtApplication: public IStaleNotify
{
public:
	KEtApplication(WebLogFunc);
	~KEtApplication();
public:
	KEtWorkbooks* GetWorkbooks();
	IKEtApplication* getCoreApp();
	etoldapi::_Application* GetApiApp() { return m_ptrApp; };

	void onStale(const WebID objId) override;
	IntervalTaskManager& getIntervalTaskManager() { return m_intervalTaskMgr; }
	void runTidyMemUsageTask(int tidyImagePoolThreshold);

	static void setEventDispatcherCallInfo(const EventDispatcherCallInfo* pInfo) { m_pEventDispatcherCallInfo = pInfo; }
	static const EventDispatcherCallInfo* getEventDispatcherCallInfo() { return m_pEventDispatcherCallInfo; }

private:
	std::unique_ptr<KEtWorkbooks>	m_Workbooks;
	std::unique_ptr<KAtExit>		m_AtExit;

	ks_castptr<IKEtApplication>		m_ptrCoreApp;
	ks_stdptr<etoldapi::_Application>			m_ptrApp;

	std::unique_ptr<et::KConApplication> m_consoleApp;
	std::unique_ptr<KEntrys>	m_Entrys;
	WebLogFunc m_logFunc;
	IntervalTaskManager m_intervalTaskMgr;
	inline static const EventDispatcherCallInfo* m_pEventDispatcherCallInfo = nullptr;
};
}
#endif //__ETAPPLICATION_H__
