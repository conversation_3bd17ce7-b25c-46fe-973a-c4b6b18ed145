﻿#include "etstdafx.h"
#include "workbook.h"

#include "et_dbsheet_query_class.h"
#include "et_dbsheet_utils.h"
#include "database/database_field_context.h"
#include "et_revision_context_impl.h"
#include <public_header/drawing/model/abstract_shape.h>
#include "kso/io/clipboard/ksomimetype.h"
#include "ettools/ettools_encode_decoder.h"
#include "helpers/app/find_app_helper.h"
#include <public_header/drawing/api/dghost_i.h>
#include "appcore/et_appcore_shared_link_sheet.h"
#include "dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h"
#include "dbsheet/dashboard/db_chart_wrapper.h"
#include "db/db_basic_itf.h"

extern Callback* gs_callback;

namespace wo
{

// ================== ETDbSheetTaskClassBase ==================

ETDbSheetQueryClassBase::ETDbSheetQueryClassBase(wo::KEtWorkbook* wwb, PCWSTR tag)
    :EtQueryExecBase(wwb, tag)
    , m_pDbCtx(_appcore_GainDbSheetContext())
    , m_commonHelper(wwb)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    ks_stdptr<IUnknown> spUnknown;
    VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
    ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
    spDBUserGroups->GetJudgement(&m_spProtectionJudgement);
}

HRESULT ETDbSheetQueryClassBase::PreExecute(const VarObj& param, KEtRevisionContext* ctx)
{
    HRESULT hr = EtQueryExecBase::PreExecute(param, ctx);
    if (FAILED(hr))
        return hr;

    hr = m_pDbCtx->BeginQuery(param, ctx);
    return hr;
}

IDX ETDbSheetQueryClassBase::GetIdxById(UINT sheetStId)
{
    return m_commonHelper.GetIdxById(sheetStId);
}

void ETDbSheetQueryClassBase::GetDBSheetViews(UINT sheetStId, IDBSheetViews** ppIDbSheetViews)
{
	m_commonHelper.GetDBSheetViews(sheetStId, ppIDbSheetViews);
}

void ETDbSheetQueryClassBase::GetDBSheetView(UINT sheetStId, EtDbId viewId, IDBSheetView** ppIDbSheetView, IDBSheetViews** ppIDbSheetViews)
{
	m_commonHelper.GetDBSheetView(sheetStId, viewId, ppIDbSheetView, ppIDbSheetViews);
}

void ETDbSheetQueryClassBase::GetDBPos(binary_wo::VarObj pos, DbIdPostion& tar)
{
	m_commonHelper.GetDBPos(pos, tar);
}

HRESULT ETDbSheetQueryClassBase::GetDBRange(IDBSheetView* pView, binary_wo::VarObj vRg,
    bool extendRecordsViewAllToGridAll, bool extendFieldsViewAllToGridAll, IDBSheetRange** ppRg)
{
    return m_commonHelper.GetDBRange(pView, vRg, extendRecordsViewAllToGridAll, extendFieldsViewAllToGridAll, ppRg);
}

EtDbId ETDbSheetQueryClassBase::GetEtDbId(const binary_wo::VarObj& obj, WebName name)
{
    return m_commonHelper.GetEtDbId(obj, name);
}

EtDbId ETDbSheetQueryClassBase::GetEtDbId(binary_wo::VarObj obj)
{
    return m_commonHelper.GetEtDbId(obj);
}

// ================== QueryExecDbRangeCopy ==================

QueryExecDbRangeCopy::QueryExecDbRangeCopy(wo::KEtWorkbook* wwb)
    :ETDbSheetQueryClassBase(wwb, __X("dbSheet.rangeCopy"))
{

}

HRESULT QueryExecDbRangeCopy::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    acpt->addInt32("copyId", param.field_int32("copyId"));

    bool hasBreak = false;
    bool bAlterAttachmentField = false;
    bool alterNoteField = false;
    ks_wstring txt;
	HRESULT text_hr = m_commonHelper.GetAllDataText(param, txt, hasBreak, bAlterAttachmentField, alterNoteField);

    ks_wstring jsonFormatStr;
    HRESULT json_hr = m_commonHelper.RangeCopy(param, jsonFormatStr, hasBreak);

    if(FAILED(text_hr) && FAILED(json_hr))
		return E_FAIL;

    bool bNeedDelay = (hasBreak ? true : false);
    if (gs_callback && gs_callback->setNeedDelayCopy)
        gs_callback->setNeedDelayCopy(bNeedDelay? 1: 0);

	acpt->addKey("mineData");
	acpt->beginStruct();
    if (S_OK == text_hr)
		acpt->addString(kso_cb_text_format, txt.c_str());
	if (S_OK == json_hr)
		acpt->addString("db_cp_json_format", jsonFormatStr.c_str());
	acpt->endStruct();
    acpt->addBool("hasBreak", hasBreak);
	return S_OK;
}

HRESULT QueryExecDbRangeCopy::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if(FAILED(hr))
        return hr;

    return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}

QueryExecDbFilterList::QueryExecDbFilterList(wo::KEtWorkbook* wwb)
    : ETDbSheetQueryClassBase(wwb, __X("dbSheet.getFilterValuesList"))
{

}

HRESULT QueryExecDbFilterList::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    return DbSheet::SerializeFilterList(param, ctx, &m_commonHelper, acpt, m_version);
}

HRESULT QueryExecDbFilterList::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if(FAILED(hr))
        return hr;

    return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}


QueryExecDbPermissionFilterList::QueryExecDbPermissionFilterList(wo::KEtWorkbook* wwb)
    : ETDbSheetQueryClassBase(wwb, __X("dbSheet.getPermissionFilterValuesList"))
{

}

HRESULT QueryExecDbPermissionFilterList::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
	IDX sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

    WebStr permissionId = param.field_str("permissionId");
    WebStr filterType = param.field_str("filterType");
    bool isEditableFilter = false;
    if (xstrcmp(filterType, __X("VisibleFilter")) == 0)
    {
        isEditableFilter = false;
    }
    else if (xstrcmp(filterType, __X("EditableFilter")) == 0)
    {   
        isEditableFilter = true;
    } 
    else 
    {
        return E_INVALIDARG;
    }

    KDBFilterValuesSearchType searchTy = DBFVST_None;
    PCWSTR searchStr = NULL;
    binary_wo::VarObj vSc = param.get_s("search");
    if (vSc.type() != binary_wo::typeInvalid)
    {
        hr = _appcore_GainEncodeDecoder()->DecodeKDBFilterValuesSearchType(vSc.field_str("type"), &searchTy);
        if (FAILED(hr))
		    return hr;
        searchStr = vSc.field_str("value");
    }

    BOOL bDateClassify = FALSE;
    if (param.has("isDateClassify"))
        bDateClassify = param.field_bool("isDateClassify");
    BOOL bGetAll = param.field_bool("getAll");
    EtDbId fldId = GetEtDbId(param, "fieldId");
    IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();
    ks_stdptr<IDbField> spField;
    hr = pFieldsMgr->GetField(fldId, &spField);
    if (FAILED(hr))
        return hr;

    DBQueryPageInfo pageInfo;
    if (param.has("pageInfo"))
    {
        binary_wo::VarObj page = param.get_s("pageInfo");
        pageInfo.pageSize = page.field_uint32("pageSize");
        pageInfo.pageNum = page.field_uint32("pageNum");
        pageInfo.pageOffset = page.field_uint32("pageOffset");
    }

    DBQueryFieldValuesInfo queryParam;
    queryParam.fieldId = fldId;
    queryParam.bGetAll = bGetAll;
    queryParam.searchTy = searchTy;
    queryParam.searchStr = searchStr;
    queryParam.bDateClassify = bDateClassify;
    queryParam.pageInfo  = param.has("pageInfo") ? &pageInfo : nullptr;

    if (xstrcmp(permissionId, __X("")) == 0)
    {
        return spDbSheetOp->GetFieldValues(&queryParam, acpt);
    }
    else
    {
        const IDbFilter* pFilter = m_spProtectionJudgement->GetPermissionFilter(permissionId, sheetStId, isEditableFilter);
        if(!pFilter)
            return E_FAIL;

        const IDbFieldFilter* pFieldFilter = nullptr;
        if(param.has("filterId"))
            pFilter->GetFieldFilter(GetEtDbId(param, "filterId"), &pFieldFilter);
        else
            pFilter->GetFieldFilterByFieldId(fldId, &pFieldFilter);

        queryParam.pFieldFilter = pFieldFilter;
        return pFilter->GetFieldValues(spDbSheetOp, &queryParam, acpt);
    }

    return S_OK;
}

HRESULT QueryExecDbPermissionFilterList::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    return m_spProtectionJudgement->CheckCanManage();    
}


QueryExecDbChartFilterList::QueryExecDbChartFilterList(wo::KEtWorkbook* wwb)
	: ETDbSheetQueryClassBase(wwb, __X("dbSheet.getChartFilterValuesList"))
{
}

HRESULT QueryExecDbChartFilterList::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* acpt)
{
	UINT stId = param.field_uint32("sheetStId");
	IDX sheetIdx = INVALIDIDX;
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	pWorkbook->GetBook()->STSheetToRTSheet(stId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_INVALIDARG;
	_Worksheet* pWorksheet = static_cast<_Worksheet*>(pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx));
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!(pSheet->IsDbDashBoardSheet()))
		return E_FAIL;

    BOOL bDateClassify = FALSE;
    if (param.has("isDateClassify"))
        bDateClassify = param.field_bool("isDateClassify");

    HRESULT hr = S_OK;
    KDBFilterValuesSearchType searchTy = DBFVST_None;
	PCWSTR searchStr = nullptr;
	binary_wo::VarObj vSc = param.get_s("search");
	if (vSc.type() != binary_wo::typeInvalid)
	{
		hr = _appcore_GainEncodeDecoder()->DecodeKDBFilterValuesSearchType(vSc.field_str("type"), &searchTy);
		if (FAILED(hr))
			return hr;
		searchStr = vSc.field_str("value");
	}

    DBQueryPageInfo pageInfo;
    if (param.has("pageInfo"))
    {
        binary_wo::VarObj page = param.get_s("pageInfo");
        pageInfo.pageSize = page.field_uint32("pageSize");
        pageInfo.pageNum = page.field_uint32("pageNum");
        pageInfo.pageOffset = page.field_uint32("pageOffset");
    }

    EtDbId fieldId = GetEtDbId(param, "fieldId");
	BOOL bGetAll = param.field_bool("getAll");
    DBQueryFieldValuesInfo queryParam;
    queryParam.fieldId = fieldId;
    queryParam.bGetAll = bGetAll;
    queryParam.searchTy = searchTy;
    queryParam.searchStr = searchStr;
    queryParam.bDateClassify = bDateClassify;
    queryParam.pageInfo  = param.has("pageInfo") ? &pageInfo : nullptr;

    if (param.has("dataSourceId"))
    {
        UINT dataSourceId = param.field_uint32("dataSourceId");
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        hr = m_commonHelper.GetDBSheetOp(dataSourceId, &spDbSheetOp);
        if (FAILED(hr))
			return hr;
        return spDbSheetOp->GetFieldValues(&queryParam, acpt);
    }

    ks_stdptr<IUnknown> spUnknown;
    pSheet->GetExtDataItem(edDbSheetChartDataStatisticMgr, &spUnknown);
    if (!spUnknown)
        return E_FAIL;
    ks_stdptr<IDBChartStatisticMgr> spStatisticModuleMgr = spUnknown;
    ks_stdptr<IDBChartStatisticModule> spModule;
    spStatisticModuleMgr->GetItemById(GetEtDbId(param, "moduleId"), &spModule);
    if (!spModule)
        return E_FAIL;

	const IDbFilter* pFilter = spModule->GetConstFilter();
    if (!pFilter)
        return E_FAIL;
    const IDbFieldFilter* pFieldFilter = nullptr;
    if(param.has("filterId"))
        pFilter->GetFieldFilter(GetEtDbId(param, "filterId"), &pFieldFilter);
    else
        pFilter->GetFieldFilterByFieldId(fieldId, &pFieldFilter);

	IDbFieldsManager* pFieldsMgr = spModule->GetFieldsManager();
	if (!pFieldsMgr)
		return E_FAIL;
	ks_stdptr<IDbField> spField;
	hr = pFieldsMgr->GetField(fieldId, &spField);
	if (FAILED(hr))
		return hr;

	// TODO:优化为当单元格值有改动时才清空筛选的缓存
	WebInt curVersion = pCtx->getBaseDataVersion();
	if (curVersion != m_version)
	{
		pFilter->ClearFieldsValues();
		m_version = curVersion;
	}

    queryParam.pFieldFilter = pFieldFilter;
	return pFilter->GetFieldValues(spField->GetDbSheetData(), &queryParam, acpt);
}

HRESULT QueryExecDbChartFilterList::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext*)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;
    return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;
}
// ==================================================================================================================
QueryDbAllAccessibleFields::QueryDbAllAccessibleFields(wo::KEtWorkbook* wwb)
    : ETDbSheetQueryClassBase(wwb, __X("dbSheet.queryAllAccessibleFields"))
{

}

HRESULT QueryDbAllAccessibleFields::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    struct KDBIdBoolMapEnum : IDBIdBoolMapEnum
    {
    public:
        KDBIdBoolMapEnum(IDBSheetCtx* ctx, ISerialAcceptor* acpt)
            : m_pCtx(ctx), m_acpt(acpt)
        {
        }

        STDIMP_(void) Default(BOOL b) override
        {
        }

        STDIMP_(BOOL) Diff(EtDbId id) override
        {
            EtDbIdStr buf;
            m_pCtx->EncodeEtDbId(id, &buf);
            m_acpt->addString(nullptr, buf);
            return TRUE;
        }

    private:
        IDBSheetCtx* m_pCtx;
        ISerialAcceptor* m_acpt;
    } boolMapEnum(m_pDbCtx, acpt);

    IBook* pBook = ctx->getBook();
    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    acpt->addKey("ret");
    acpt->beginArray();
    for (int i = 0; i < sheetCount; i++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        if (!spSheet)
            continue;
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        spSheet->GetExtDataItem(edSheetDbOp, (IUnknown**)&spDbSheetOp);
        if (!spDbSheetOp)
            continue;
        const IDBIdBoolMap* pAccessField = spDbSheetOp->GetAccessibleFields();
        if (!pAccessField)
            continue;
        acpt->beginStruct();
            acpt->addInt32("sheetStId", spSheet->GetStId());
            bool isAllAccessible = pAccessField->GetDefault();
            acpt->addBool("isAllFieldsAccessible", isAllAccessible);
            if (!isAllAccessible)
            {
                acpt->addKey("accessibleFields");
                acpt->beginArray();
                    pAccessField->Enum(&boolMapEnum);
                acpt->endArray();
            }
        acpt->endStruct();
    }
    acpt->endArray();
    return S_OK;
}

HRESULT QueryDbAllAccessibleFields::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    return S_OK;
}
// ==================================================================================================================

QueryExecDbFieldItems::QueryExecDbFieldItems(wo::KEtWorkbook* wwb)
    : ETDbSheetQueryClassBase(wwb, __X("dbSheet.getFieldItems"))
{

}

HRESULT QueryExecDbFieldItems::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    IDX sheetStId = param.field_uint32("sheetStId");
    EtDbId viewId = GetEtDbId(param, "viewId");

    ks_stdptr<IDBSheetView> spDbSheetView;
    GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
    if(spDbSheetView == NULL)
        return E_DBSHEET_VIEW_NOT_FOUND;

    EtDbId fldId = GetEtDbId(param, "fieldId");

    ks_stdptr<IDbField> spField;
    IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
    HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
    if (FAILED(hr))
        return hr;

    GlobalSharedStringUnorderedSet selectItems;
    ET_DbSheet_FieldType newType = Et_DbSheetField_MultiLineText;
    if (param.has("type"))
        _appcore_GainEncodeDecoder()->DecodeFieldType(param.field_str("type"), &newType);
    hr = DbSheet::GetFieldValues(spDbSheetView->GetSheetOp(), fldId, spField->GetType(), newType, selectItems);
    if (FAILED(hr))
        return hr;

    acpt->addKey("valuesList");
    acpt->beginArray();
    for (auto it = selectItems.begin(); it != selectItems.end(); ++it)
        acpt->addString(nullptr, it->c_str());
    acpt->endArray();

    return S_OK;
}

HRESULT QueryExecDbFieldItems::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

QueryExecDbFieldTypeModifyCheck::QueryExecDbFieldTypeModifyCheck(wo::KEtWorkbook* wwb)
	: ETDbSheetQueryClassBase(wwb, __X("dbSheet.typeModifyCheck"))
{}

HRESULT QueryExecDbFieldTypeModifyCheck::Exec(const VarObj& param, KEtRevisionContext*, ISerialAcceptor* acpt)
{
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId fldId = GetEtDbId(param, "fieldId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDbField> spField;
	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
	HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
	if (FAILED(hr))
		return hr;

	ET_DbSheet_FieldType oldType = spField->GetType();
	ET_DbSheet_FieldType newType = Et_DbSheetField_MultiLineText;

	VarObj args = param.get_s("args");
	if (args.has("type"))
		_appcore_GainEncodeDecoder()->DecodeFieldType(args.field_str("type"), &newType);

	TypeChangeInfo info;
	if (oldType == Et_DbSheetField_Contact && args.has("supportMulti") && !args.field_bool("supportMulti"))
	{
		ks_stdptr<IDbField_Contact> spFieldContact = spField;
		info.resetContact = spFieldContact->GetSupportMulti();
	}
    else if (oldType == Et_DbSheetField_Department && args.has("supportMulti") && !args.field_bool("supportMulti"))
	{
		ks_stdptr<IDbField_Cascade> spFieldDepartment = spField;
		info.resetDepartment = spFieldDepartment->GetMultiValue();
	}
    else if(oldType == Et_DbSheetField_Address)
    {
        ks_stdptr<IDbField_Cascade> spFieldAddress = spField;
        if(args.has("addressLevel"))
            info.resetAddress = args.field_uint32("addressLevel") != spFieldAddress->GetCascadeLevel();
        if(args.has("withDetailedAddress") && !info.resetAddress)
            info.resetAddress = spFieldAddress->GetWithDetailedInfo() != args.field_bool("withDetailedAddress");
    }
    if(args.has("enableValueUniqueProp"))
    {
        info.enableValueUniquePorp = args.field_bool("enableValueUniqueProp");
    }
	// 0-failed, 1-success, 2-partial success, 3 will loss some data, 4 eable valUniqueProp failed
	acpt->addInt32("code", spField->CheckChangeFieldType(oldType, newType, &info));

	return S_OK;
}

HRESULT QueryExecDbFieldTypeModifyCheck::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

// ================== QueryExecDbSearchNext ==================
QueryExecDbSearchNext::QueryExecDbSearchNext(wo::KEtWorkbook* wwb)
    : ETDbSheetQueryClassBase(wwb, __X("dbSheet.searchNext"))
{}

namespace
{
long GetFindCellCountLimit()
{
    static long cnt = -2;
    if (cnt == -2)
    {
        if (char* str = ::getenv("AS_DB_FIND_CELL_COUNT_LIMIT"))
            cnt = std::strtol(str, nullptr, 10);
        else
            cnt = -1;
        WOLOG_INFO << "[GetWoEtSettings] AS_DB_FIND_CELL_COUNT_LIMIT: " << cnt;
    }
    return cnt;
}
}

HRESULT QueryExecDbSearchNext::Exec(const VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    IDX sheetStId = param.field_uint32("sheetStId");
    EtDbId viewId = GetEtDbId(param, "viewId");

    BOOL isFindAll = param.field_bool("isFindAll");
    BOOL isDirNext = param.field_bool("isDirNext");
    BOOL isMultiCondition = param.has("isMultiCondition") ? param.field_bool("isMultiCondition") : FALSE;
    VarObj searchRange = param.get("searchRange");
    DBRowColSearchType recordSearchType = DBSR_VISIBLE;
    if (searchRange.has("recordSearchType"))
        recordSearchType = GetSearchType(searchRange.field_str("recordSearchType"));
    DBRowColSearchType fieldSearchType = DBSR_VISIBLE;
    if (searchRange.has("fieldSearchType"))
        fieldSearchType = GetSearchType(searchRange.field_str("fieldSearchType"));
    ks_stdptr<KDbIdIdxMap_Find> spSearchRecords;
    spSearchRecords.attach(KS_NEW(KDbIdIdxMap_Find));
    if (recordSearchType == DBSR_SELECT && searchRange.has("recordsList"))
    {
        VarObj records = searchRange.get("recordsList");
        for (int i = 0; i < records.arrayLength_s(); ++i)
            spSearchRecords->AppendId(GetEtDbId(records.at(i)));
    }
    ks_stdptr<KDbIdIdxMap_Find> spSearchFields;
    spSearchFields.attach(KS_NEW(KDbIdIdxMap_Find));
    if (fieldSearchType == DBSR_SELECT && searchRange.has("fieldsList"))
    {
        VarObj fields =  searchRange.get("fieldsList");
        for (int i = 0; i < fields.arrayLength_s(); ++i)
            spSearchFields->AppendId(GetEtDbId(fields.at(i)));
    }
    DBReturnType returnType = DBRT_CELL;
    if (param.has("returnType"))
    {
        WebStr typeStr = param.field_str("returnType");
        if (xstrcmp(__X("cell"), typeStr) == 0)
            returnType = DBRT_CELL;
        else if (xstrcmp(__X("row"), typeStr) == 0)
            returnType = DBRT_ROW;
    }
    PCWSTR searchStr = param.field_str("value");
    if (searchStr == nullptr || xstrlen(searchStr) == 0)
        return S_OK;

    ks_stdptr<IDBSheetView> spDbSheetView;
    GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
    if(spDbSheetView == NULL)
        return E_DBSHEET_VIEW_NOT_FOUND;

    EtDbIdx recordsCnt = 0, fieldsCnt = 0;
    switch (recordSearchType)
    {
    case DBSR_All:
        recordsCnt = spDbSheetView->GetAllRecords()->Count();
        break;
    case DBSR_VISIBLE:
        recordsCnt = spDbSheetView->GetVisibleRecords()->Count();
        break;
    default:
        recordsCnt = spSearchRecords->Count();
    }

    switch (fieldSearchType)
    {
    case DBSR_All:
        fieldsCnt = spDbSheetView->GetAllFields()->Count();
        break;
    case DBSR_VISIBLE:
        fieldsCnt = spDbSheetView->GetVisibleFields()->Count();
        break;
    default:
        fieldsCnt = spSearchFields->Count();
    }

    if (fieldsCnt == 0 || recordsCnt == 0)
        return S_OK;
    
    long findCellCntLimit = GetFindCellCountLimit();
    if (returnType == DBRT_CELL && findCellCntLimit != -1 && fieldsCnt * recordsCnt > findCellCntLimit)
        isFindAll = false;
    
    BOOL needFieldInfo = param.has("needFieldInfo") ? param.field_bool("needFieldInfo") : TRUE;
    // includeActCell 用于标识搜索时是否要搜索当前单元格，主要用于没有选区时的搜索
    BOOL includeActCell = param.has("includeActCell") ? param.field_bool("includeActCell") : TRUE;
    EtDbId activeRecordId = param.has("recId") ? GetEtDbId(param, "recId") : INV_EtDbId;
    EtDbId activeFieldId = param.has("fldId") ? GetEtDbId(param, "fldId") : INV_EtDbId;

    DBFINDPARAM findParam;
    findParam.sWhat = searchStr;
    findParam.bFindAll = isFindAll;
    findParam.bDirNext = isDirNext;
    findParam.returnType = returnType;
    findParam.activeCellRecordId = activeRecordId;
    findParam.activeCellFieldId = activeFieldId;
    findParam.bIncludeActiveCell = includeActCell;
    findParam.bMultiCondition = isMultiCondition;
    findParam.pSearchRecords = spSearchRecords;
    findParam.bNeedFieldInfo = needFieldInfo;
    findParam.pSearchFields = spSearchFields;
    findParam.recordSearchType = recordSearchType;
    findParam.fieldSearchType = fieldSearchType;
    class FindResultEnum : public IDBFindResultEnum
    {
    public:
        FindResultEnum(ISerialAcceptor* acpt, DBSheetCommonHelper* pCommonHelper)
            : m_acpt(acpt), m_pCommonHelper(pCommonHelper) {}

        STDPROC Do(EtDbId recId, EtDbId fldId, EtDbIdx row, EtDbIdx col) override
        {
            m_acpt->beginStruct();
            m_pCommonHelper->AcceptorAddEtDbId(m_acpt, "rid", recId);
            m_pCommonHelper->AcceptorAddEtDbId(m_acpt, "fid", fldId);
            m_acpt->addInt32("ridx", row);
            m_acpt->addInt32("fidx", col);
            m_acpt->endStruct();
            return S_OK;
        }

    private:
        ISerialAcceptor* m_acpt;
        DBSheetCommonHelper* m_pCommonHelper;
    };
    FindResultEnum fEnum(acpt, &m_commonHelper);
    EtDbIdx resultOffset = -1;  // 第一个结果在所有结果中的偏移
    acpt->addKey("pos");
    acpt->beginArray();
    spDbSheetView->Find(findParam, resultOffset, &fEnum);
    acpt->endArray();
    acpt->addInt32("resultOffset", resultOffset);
    acpt->addBool("isFindAll", findParam.bFindAll);
    return S_OK;
}

HRESULT QueryExecDbSearchNext::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if(FAILED(hr))
        return hr;

    return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}

DBRowColSearchType QueryExecDbSearchNext::GetSearchType(WebStr typeStr)
{
    DBRowColSearchType searchType;
    if (xstrcmp(__X("all"), typeStr) == 0)
    {
        searchType = DBSR_All;
    }
    else if (xstrcmp(__X("visible"), typeStr) == 0)
    {
        searchType = DBSR_VISIBLE;
    }
    else if (xstrcmp(__X("select"), typeStr) == 0)
    {
        searchType = DBSR_SELECT;
    }
    return searchType;
}

// ================== QueryDbTimerTask ==================
QueryDbTimerTask::QueryDbTimerTask(wo::KEtWorkbook* wwb)
    :ETDbSheetQueryClassBase(wwb, __X("dbSheet.queryTimerTasks"))
{

}

HRESULT QueryDbTimerTask::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    IDX sheetStId = param.field_uint32("sheetStId");

    ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

    const IDBIds *pFields = spDbSheetOp->GetAllFields();
    const IDBIds *pRecords = spDbSheetOp->GetAllRecords();
    IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();
    binary_wo::VarObj vRows = param.get_s("rows");
    acpt->addUint32("sheetId", sheetStId);
    acpt->addKey("automations");
    acpt->beginArray();
    for (EtDbIdx fld = 0; fld < pFields->Count(); fld++)
    {
        EtDbId fldId = pFields->IdAt(fld);
        ks_stdptr<IDbField> spField;
        hr = pFieldsMgr->GetField(fldId, &spField);
        if (FAILED(hr) || spField->GetType() != Et_DbSheetField_Automations)
            continue;

        ks_stdptr<IDbField_Automations> spField_Automations = spField;
        UINT automationsCnt = spField_Automations->GetAutomationsCount();

        for (EtDbIdx i = 0; i < automationsCnt; i++)
        {
            ks_stdptr<IDbAutomation> spAutomation;
            hr = spField_Automations->GetAutomation(i, &spAutomation);
            if (FAILED(hr))
                continue;

            ks_stdptr<IDbAutomationTrigger> spAutomationTrigger;
            hr = spAutomation->GetTrigger(&spAutomationTrigger);
            if (FAILED(hr) || spAutomationTrigger->GetType() != AutomationTrigger_DueDate)
                continue;

            ks_stdptr<IDbAutomationTrigger_DueDate> spAutomationTrigger_DueDate = spAutomationTrigger;

            acpt->beginStruct();
            acpt->addUint32("total", spAutomationTrigger_DueDate->GetTimerTasksCount());
            m_commonHelper.AcceptorAddEtDbId(acpt, "id", spAutomation->GetId());
            acpt->addKey("status");
            acpt->beginArray();
            for (int i = 0, cnt = vRows.arrayLength_s(); i < cnt; i++)
            {
                ROW row = vRows.item_int32(i);
                acpt->beginStruct();
                acpt->addInt32("row", row);
                EtDbId recId = pRecords->IdAt(row);
                acpt->addBool("valid", spAutomationTrigger_DueDate->HasTimerTask(recId));
                acpt->endStruct();
            }
            acpt->endArray();
            acpt->endStruct();
        }
    }
    acpt->endArray();

    return S_OK;
}

HRESULT QueryDbTimerTask::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

// ================== QueryDbSheetSummary ==================
QueryDbSheetSummary::QueryDbSheetSummary(wo::KEtWorkbook* wwb)
    :ETDbSheetQueryClassBase(wwb, __X("dbSheet.querySheetSummary"))
{

}

HRESULT QueryDbSheetSummary::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    IDX sheetStId = param.field_uint32("sheetStId");

    ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

    const IDBIds *pFields = spDbSheetOp->GetAllFields();
    const IDBIds *pRecords = spDbSheetOp->GetAllRecords();
    EtDbId recId = pRecords->IdAt(0);
    ASSERT(recId != INV_EtDbId);
    IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();
    acpt->addUint32("sheetId", sheetStId);
    acpt->addKey("record");
    acpt->beginArray();
    EtDbIdStr fldIdBuf;
    for (EtDbIdx fld = 0; fld < pFields->Count(); fld++)
    {
        EtDbId fldId = pFields->IdAt(fld);
        m_pDbCtx->EncodeEtDbId(fldId, &fldIdBuf);
        ks_stdptr<IDbField> spField;
        hr = pFieldsMgr->GetField(fldId, &spField);
        if (FAILED(hr))
            continue;
        PCWSTR fieldTypeStr = __X("");
        VS(_appcore_GainEncodeDecoder()->EncodeFieldType(spField->GetType(), &fieldTypeStr));

        ks_bstr dispStr;
        hr = spDbSheetOp->GetDisplayString(recId, fldId, &dispStr);
        if (FAILED(hr) || dispStr.empty())
            continue;

        acpt->beginStruct();
            acpt->addString("fieldId", fldIdBuf);
            acpt->addString("fieldName", spField->GetName());
            acpt->addString("fieldType", fieldTypeStr);
            acpt->addString("value", dispStr.c_str());
        acpt->endStruct();
    }
    acpt->endArray();

    return S_OK;
}

HRESULT QueryDbSheetSummary::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

QueryDbSheetShareLinksInfo::QueryDbSheetShareLinksInfo(wo::KEtWorkbook* wwb)
    :ETDbSheetQueryClassBase(wwb, __X("dbsheet.queryShareLinksInfo"))
{

}

QueryDbSheetShareLinksInfo::KSharedLinkEnum::KSharedLinkEnum(IBook* pBook, KEtWorkbook* pWorkbook, ISerialAcceptor* acpt, IDBSheetCtx* pDbCtx)
		:m_acpt(acpt)
		,m_pBook(pBook)
		,m_pWorkbook(pWorkbook)
		,m_pDbCtx(pDbCtx)
{

}

STDIMP_(BOOL) QueryDbSheetShareLinksInfo::KSharedLinkEnum::Do(ISharedLink* pSharedLink)
{
    PCWSTR sharedId = pSharedLink->Id();
    m_acpt->beginStruct();
    m_acpt->addString("sharedId", sharedId);

    bool bValid = SerialSharedLinksInfo(pSharedLink, m_pBook, m_pWorkbook, m_acpt, m_pDbCtx);

    m_acpt->addBool("isValid", bValid);
    m_acpt->endStruct();

    return TRUE;
}

bool QueryDbSheetShareLinksInfo::SerialSharedLinksInfo(ISharedLink* pSharedLink, IBook* pBook, KEtWorkbook* pWorkbook, ISerialAcceptor* acpt, IDBSheetCtx* pDbCtx)
{
    if (!pSharedLink)
        return false;

    bool bValid = false;
    KSharedLinkType sharedLinkType = pSharedLink->Type();
    if (sharedLinkType == SharedLinkType_Sheet)
    {
        ks_stdptr<ISharedLinkSheet> spSharedLinkSheet = pSharedLink;
        if (spSharedLinkSheet)
        {
            ISheet* pSheet = spSharedLinkSheet->GetSheet();
            if (pSheet)
            {
                UINT sheetId = pSheet->GetStId();
                acpt->addUint32("sheetStId", sheetId);
                acpt->addString("type", __X("sheet"));
                bValid = true;
            }
        }
        return bValid;
    }
    ks_stdptr<IDBSharedLinkView> spSharedLinkView = pSharedLink;
    if (spSharedLinkView)
    {
        IDBSheetView* pView = spSharedLinkView->GetView();
        if(pView)
        {
            IDBSheetOp* pSheet = pView->GetSheetOp();
            if(pSheet)
            {
                UINT sheetStId = pSheet->GetSheetId();
                acpt->addUint32("sheetStId", sheetStId);
                EtDbId viewId = pView->GetId();
                EtDbIdStr buf;
                pDbCtx->EncodeEtDbId(viewId, &buf);
                acpt->addString("viewId", buf);
                acpt->addString("viewName", pView->GetName());
                UINT appSheetStId = spSharedLinkView->GetAppSheetStId();
                acpt->addUint32("airAppSheetStId", appSheetStId);
                EtDbId appId = spSharedLinkView->GetAppId();
                pDbCtx->EncodeEtDbId(appId, &buf);
                acpt->addString("airAppId", buf);
                PCWSTR appSheetName = __X("");
                if (appSheetStId != 0)
                {
                    IDX appSheetIdx = INVALIDIDX;
                    pBook->STSheetToRTSheet(appSheetStId, &appSheetIdx);
                    if (appSheetIdx != INVALIDIDX)
                    {
                        ks_stdptr<ISheet> spAppSheet;
                        pBook->GetSheet(appSheetIdx, &spAppSheet);
                        spAppSheet->GetName(&appSheetName);
                    }
                }
                acpt->addString("airAppSheetName", appSheetName);
                UINT relatedEtSheetStId = 0;
                bool bHasRelatedEtSheet = FindAppHelper::FindAppRelatedEtSheetStIdByDbSheetStId(pWorkbook, sheetStId, relatedEtSheetStId);
                if (bHasRelatedEtSheet)
                    acpt->addUint32("relatedEtSheetStId", relatedEtSheetStId);
                acpt->addString("type", __X("dbView"));
                bValid = true;
            }
        }
    }
    return bValid;
}

HRESULT QueryDbSheetShareLinksInfo::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	IBook* pBook = ctx->getBook();
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    if(!spSharedLinkMgr)
        return E_FAIL;

    acpt->addKey("sharedIdInfoVec");
    acpt->beginArray();

    if(!param.has("sharedIdVec"))
    {
        KSharedLinkEnum linkEnum(pBook, m_wwb, acpt, m_pDbCtx);
        spSharedLinkMgr->Enum(&linkEnum);
    }
    else
    {
        VarObj sharedIdVec = param.get("sharedIdVec");
        for (int i = 0; i < sharedIdVec.arrayLength_s(); ++i)
        {
            PCWSTR sharedId = sharedIdVec.item_str(i);
            
            acpt->beginStruct();
            acpt->addString("sharedId", sharedId);
            
            ISharedLink* pSharedLink = spSharedLinkMgr->GetItem(sharedId);
            bool bValid = SerialSharedLinksInfo(pSharedLink, pBook, m_wwb, acpt, m_pDbCtx);

            acpt->addBool("isValid", bValid);
            acpt->endStruct();
        }
    }
    
    acpt->endArray();
    return S_OK;   
}


QueryDbPermission::QueryDbPermission(wo::KEtWorkbook* wwb)
    :ETDbSheetQueryClassBase(wwb, __X("dbSheet.queryPermission"))
{

}

HRESULT QueryDbPermission::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    if (param.has("getAllPermissionList") && param.field_bool("getAllPermissionList"))
    {
        class DBProtectionEnum : public IDBProtectionEnum
        {
        public:
            DBProtectionEnum(ISerialAcceptor* acptor) : m_acpt(acptor) {}
            STDPROC_(BOOL)
            Do(IDBProtection* protection)
            {
                m_acpt->beginStruct();
                protection->SerialDBPermission(m_acpt, FALSE);
                m_acpt->endStruct();
                return TRUE;
            }

        private:
            ISerialAcceptor* m_acpt;
        };

        acpt->addKey("permissionList");
        acpt->beginArray();
        DBProtectionEnum enumer(acpt);
        m_spProtectionJudgement->EnumeDBProtection(&enumer);
        acpt->endArray();
    }
    else
    {
        if (param.has("corePermissionId"))
        {
            WebStr permissionId = param.field_str("corePermissionId");
            IDBProtection* protection = m_spProtectionJudgement->GetDBProtection(permissionId);
            if (protection)
            {
                protection->SerialDBPermission(acpt, TRUE);
                return S_OK;
            }
        }
        return E_DBSHEET_PERMISSION_ID_NOT_EXIST;
    }

    return S_OK;
}

HRESULT QueryDbPermission::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    return S_OK;
}

// ================== QueryDbSheetViews ==================
QueryDbSheetViews::QueryDbSheetViews(wo::KEtWorkbook* wwb)
    :ETDbSheetQueryClassBase(wwb, __X("dbSheet.querySheetViews"))
{

}

HRESULT QueryDbSheetViews::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    IDX sheetStId = param.field_uint32("sheetStId");
    bool personalVisible = false;
    if (param.has("personalVisible"))
        personalVisible = param.field_bool("personalVisible");

    ks_stdptr<IDBSheetViews> spDbSheetViews;
    GetDBSheetViews(sheetStId, &spDbSheetViews);
    if(!spDbSheetViews)
        return E_FAIL;

    acpt->addUint32("sheetId", sheetStId);
    acpt->addKey("views");
    acpt->beginArray();
    EtDbIdStr viewIdBuf;
    for (EtDbId viewIdx = 0; viewIdx < spDbSheetViews->GetSize(Et_DBSheetViewUse_ForDb); ++viewIdx)
    {
        ks_stdptr<IDBSheetView> spView;
        spDbSheetViews->GetItemAt(viewIdx, Et_DBSheetViewUse_ForDb, &spView);
        if (!spView)
            continue;

        if (personalVisible && !IsPersonalVisible(spView, ctx))
            continue;
        
        EtDbId viewId = spView->GetId();
        m_pDbCtx->EncodeEtDbId(viewId, &viewIdBuf);
        PCWSTR typeStr = nullptr;
        VS(_appcore_GainEncodeDecoder()->EncodeViewType(spView->GetType(), &typeStr));
        bool isPersonalView = spView->GetPersonalViewUserIdsCount() > 0;
        acpt->beginStruct();
            acpt->addString("viewId", viewIdBuf);
            acpt->addString("viewName", spView->GetName());
            acpt->addString("viewType", typeStr);
            acpt->addBool("isPersonal", isPersonalView);
        acpt->endStruct();
    }
    acpt->endArray();
    return S_OK;
}

bool QueryDbSheetViews::IsPersonalVisible(IDBSheetView *pView, KEtRevisionContext* ctx)
{
    UINT count = pView->GetPersonalViewUserIdsCount();
    if (0 == count)
        return true;

    for (UINT i = 0; i < count; ++i)
    {
       PCWSTR userid = pView->GetPersonalViewUserId(i);
       if (userid && xstrcmp(userid, ctx->getUser()->userID()) == 0)
          return true;
    }

    return false;
}

HRESULT QueryDbSheetViews::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

// ================== QueryDbLinkRecords ==================
QueryDbLinkRecords::QueryDbLinkRecords(wo::KEtWorkbook* wwb)
    :ETDbSheetQueryClassBase(wwb, __X("dbSheet.queryDbLinkRecords"))
{

}

HRESULT QueryDbLinkRecords::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    UINT sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

    EtDbId recId = GetEtDbId(param, "recId");
    EtDbId fldId = GetEtDbId(param, "fldId");
    bool bNeedPrimaryFieldStr = false;
    if (param.has("needPrimaryFieldStr"))
        bNeedPrimaryFieldStr = param.field_bool("needPrimaryFieldStr");
    IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();
    ks_stdptr<IDbField> spField;
    hr = pFieldsMgr->GetField(fldId, &spField);
    if (FAILED(hr))
        return hr;
    ET_DbSheet_FieldType fieldType = spField->GetType();
    switch (fieldType)
    {
        case Et_DbSheetField_Link:
        case Et_DbSheetField_OneWayLink:
        {
            const_token_ptr pToken = nullptr;
            hr = spDbSheetOp->GetValueToken(recId, fldId, &pToken);
            if (FAILED(hr))
                return hr;
            if (nullptr == pToken)
                return S_OK;
            alg::const_handle_token_assist chta(pToken);
            alg::TOKEN_HANDLE handle = chta.get_handle();
            if (handle == nullptr || chta.get_handleType() != alg::ET_HANDLE_DBLINK)
                return E_FAIL;
            const IDbLinkHandle* pLink = handle->CastDbLink();
            acpt->addKey("records");
            acpt->beginArray();
            SerialiseLinkHandle(pLink, acpt, bNeedPrimaryFieldStr);
            acpt->endArray();
            return S_OK;
        }
        case Et_DbSheetField_Lookup:
        {
            ks_stdptr<IDbField_Lookup> spLookupField = spField;
            // 只有引用字段的聚合类型是原始值或去重的情况下，前端需要展示胶囊并且会调用此方法
            ET_DbSheet_Lookup_Function lookupFunction = spLookupField->GetLookupFunction();
            if (lookupFunction != DbSheet_Lookup_Function_Origin && lookupFunction != DbSheet_Lookup_Function_Unique)
                return E_FAIL;
            ks_stdptr<IDbField> spLookupBaseField;
            hr = spLookupField->GetLookupBaseField(&spLookupBaseField);
            if (FAILED(hr) || !spLookupBaseField)
                return E_FAIL;
            ET_DbSheet_FieldType baseType = spLookupBaseField->GetType();
            if (baseType != Et_DbSheetField_OneWayLink && baseType != Et_DbSheetField_Link)
                return E_FAIL;            
            const_token_ptr pToken = nullptr;
            hr = spDbSheetOp->GetValueToken(recId, fldId, &pToken);
            if (FAILED(hr))
                return hr;
            if (nullptr == pToken)
                return S_OK;
            alg::const_handle_token_assist chta(pToken);
            alg::TOKEN_HANDLE handle = chta.get_handle();
            if (handle == nullptr || chta.get_handleType() != alg::ET_HANDLE_TOKENARRAY)
                return E_FAIL;
            const IDbTokenArrayHandle* pHandleAry = handle->CastArray();
            acpt->addKey("records");
            acpt->beginArray();
            SerialiseLinkTokenAry(pHandleAry, acpt, bNeedPrimaryFieldStr, IsTokenArrayCutOff());
            acpt->endArray();
            return S_OK; 
        }
        default:
            return E_FAIL;
    }
}

    
HRESULT QueryDbLinkRecords::SerialiseLinkHandle(const IDbLinkHandle* pLink, ISerialAcceptor* acpt, bool bNeedPrimaryFieldStr)
{
    UINT count = pLink->GetCount();
    EtDbIdStr buf;
    for (UINT i = 0; i < count ; i++)
    {
        EtDbId id = pLink->GetItemId(i);
        VS(m_pDbCtx->EncodeEtDbId(id, &buf));
        acpt->beginStruct();
        acpt->addString("id", buf);
        if (bNeedPrimaryFieldStr)
            acpt->addString("str", pLink->GetItemString(i));
        acpt->endStruct();
    }
    return S_OK;
}

bool QueryDbLinkRecords::IsTokenArrayCutOff()
{
    // 与tokenArray的截断相关的逻辑参考KDbTokenArrayHandle，这里的行为应当和那边保持一致
    static int b = 2;
    if (b == 2)
    {
        char* str = ::getenv("DB_BAN_TOKENARRAY_SERIAL_CUTOFF");
        if (str && strncmp(str, "true", 5) == 0)
            b = 1;
        else
            b = 0;
        WOLOG_INFO << "DB_BAN_TOKENARRAY_SERIAL_CUTOFF: " << str;
    }
    return b;
}

HRESULT QueryDbLinkRecords::SerialiseLinkTokenAry(const IDbTokenArrayHandle* pTokenAry, ISerialAcceptor *acpt, bool bNeedPrimaryFieldStr, bool enableCutOff)
{
    int32 lastBufferWriterLen = acpt->getBufferWriteLength();
    int32 memUsed = 0;
    constexpr int32 threshold = 128 * 1024; // 128 kb

    UINT count = pTokenAry->GetCount();
    HRESULT hr = S_OK;
    for (UINT i = 0; i < count; i++)
    {
        const_token_ptr pToken = nullptr;
        hr = pTokenAry->Item(i, &pToken);
        if (FAILED(hr) || nullptr == pToken)
            continue;
        alg::const_handle_token_assist chta(pToken);
        switch (chta.get_handleType())
        {
            case alg::ET_HANDLE_TOKENARRAY:
            {
                SerialiseLinkTokenAry(chta.get_handle()->CastArray(), acpt, bNeedPrimaryFieldStr, enableCutOff);
                break;
            }
            case alg::ET_HANDLE_DBLINK:
            {
                const IDbLinkHandle* linkHandle = chta.get_handle()->CastDbLink();
                SerialiseLinkHandle(linkHandle, acpt, bNeedPrimaryFieldStr);
                break;
            }
            default:
                ASSERT(false);
        }
        int32 curBufferWriterLen = acpt->getBufferWriteLength();
        memUsed += (curBufferWriterLen - lastBufferWriterLen);
        lastBufferWriterLen = curBufferWriterLen;
        if (memUsed >= threshold)
            break;
    }
    return S_OK;
}

HRESULT QueryDbLinkRecords::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;
    UINT sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<IDBSheetOp> spDbSheetOp;    
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;
    EtDbId recId = GetEtDbId(param, "recId");
    EtDbId fldId = GetEtDbId(param, "fldId");
    EtDbIdx row = spDbSheetOp->GetAllRecords()->Id2Idx(recId);
    EtDbIdx col = spDbSheetOp->GetAllFields()->Id2Idx(fldId);
    return m_spProtectionJudgement->CheckCellCanVisit(sheetStId, row, col);
}

// ================== QueryExecDbVirtualViewQueryRecords ==================
QueryExecDbVirtualViewQueryRecords::QueryExecDbVirtualViewQueryRecords(wo::KEtWorkbook* wwb)
    : ETDbSheetQueryClassBase(wwb, __X("dbSheet.getVirtualViewQueryRecords"))
{
}

HRESULT QueryExecDbVirtualViewQueryRecords::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    IDX sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

    const IDBIds *pRecords = nullptr;
    ks_stdptr<IDBSheetView> spDbSheetView;
    BOOL bLinkCustomConfig = FALSE;
    BOOL bChart = FALSE;
    std::vector<EtDbId> visibleIds;
    if (param.has("viewId")) // 关联视图
    {
        EtDbId viewId = GetEtDbId(param, "viewId");
        GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
        if (!spDbSheetView)
            return E_DBSHEET_VIEW_NOT_FOUND;

        pRecords = spDbSheetView->GetVisibleRecords();
        hr = m_commonHelper.setGetViewCurModifyTar(param);
        if (FAILED(hr))
            return hr;

        bLinkCustomConfig = FALSE;
    }
    else if (param.has("customConfigRecordsInfo"))
    {
        VarObj info = param.get("customConfigRecordsInfo");
        IDX baseSheetStId = info.field_uint32("baseSheetStId");
        ks_stdptr<IDBSheetOp> spBaseDbSheetOp;
        hr = m_commonHelper.GetDBSheetOp(baseSheetStId, &spBaseDbSheetOp);
        if (FAILED(hr))
            return hr;

        bLinkCustomConfig = DbSheet::GetDbLinkCustomConfigRecords(spBaseDbSheetOp.get(), info, visibleIds);
    }
    else if (param.has("chartParams"))
    {
        bChart = TRUE;
        VarObj chartParams = param.get("chartParams");
        UINT sheetId = chartParams.field_uint32("sheetId");
        ks_stdptr<_Worksheet> spWorksheet;
        hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
        if (FAILED(hr))
            return hr;
        if (!spWorksheet->GetSheet()->IsDbDashBoardSheet())
            return E_INVALIDARG;
        hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
        if (FAILED(hr))
            return hr;
        KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
        std::unique_ptr<KDbDashboardModuleWrapper> spModuleWrapper = dashboardModuleMgrWrapper.GetModule(chartParams.field_str("webExtensionKey"));
        if (!spModuleWrapper)
            return E_DBSHEET_DASHBOARD_MODULE_NOT_FOUND;

        if (!spModuleWrapper->IsChart())
            return E_FAIL;

        std::unique_ptr<KDbChartWrapper> spChartWrapper(static_cast<KDbChartWrapper*>(spModuleWrapper.release()));
        UINT dataSourceId = spChartWrapper->GetDataSourceSheetId();
        if (dataSourceId == 0)
            return E_DBSHEET_DASHBOARD_MODULE_NO_DBSHEET_DATASOURCE;
        if (dataSourceId != sheetStId)
            return E_DBSHEET_SHEET_MISMATCH;
        ks_stdptr<IDBIds> spChartRecords;
        if (chartParams.has("categoryGroupKey"))
        {
            VarObj categoryGroupKey = chartParams.get_s("categoryGroupKey");
            VarObj seriesGroupKey = chartParams.get_s("seriesGroupKey");
            hr = spChartWrapper->GetGroupRecords(categoryGroupKey, seriesGroupKey, &spChartRecords);
        }
        else
        {
            hr = spChartWrapper->GetVisibleRecords(&spChartRecords);
        }
        if (FAILED(hr))
            return hr;

        ks_stdptr<IDBSheetRange> spRange;
        m_spProtectionJudgement->GetNoPermissionRecords(sheetStId, &spRange);
        const EtDbId* pIds = nullptr;
        UINT32 cnt = 0;
        if (spRange)
            spRange->GetRecordIds(&pIds, &cnt);
        std::unordered_set<EtDbId> noPermissionRecords(pIds, pIds + cnt);
        for (EtDbIdx i = 0, count = spChartRecords->Count(); i < count; ++i)
        {
            EtDbId id = spChartRecords->IdAt(i);
            if (noPermissionRecords.find(id) == noPermissionRecords.end())
                visibleIds.push_back(id);
        }
    }

    if (!bLinkCustomConfig && !bChart)
    {
        pRecords = (pRecords == nullptr ? spDbSheetOp->GetAllRecords() : pRecords);
        visibleIds.reserve(pRecords->Count());
        for (EtDbIdx i = 0, c = pRecords->Count(); i < c; ++i)
            visibleIds.push_back(pRecords->IdAt(i));
    }

    if (param.has("filter"))
    {
        VarObj filter = param.get_s("filter");
        if (binary_wo::typeStruct != filter.type())
            return E_DBSHEET_FILTER_INVALID;

        ks_stdptr<IDbExtraFilter> spExtraFilter;
        VS(_appcore_CreateObject(CLSID_KDbExtraFilter, IID_IDbExtraFilter, (void**)& spExtraFilter));
        spExtraFilter->Init(spDbSheetOp.get());
        const IDbExtraFilter* pFailedFilter = nullptr;
        hr = DbSheet::filtrateByExtraFilter(spExtraFilter, filter, m_commonHelper, spDbSheetOp.get(), true, &pFailedFilter);
        if (FAILED(hr))
            return hr;

        IKUserConn* user = ctx->getUser();
        size_t newSize = visibleIds.size();
        hr = spExtraFilter->FilterVisibleRecords(visibleIds.data(), newSize, user);
        if (FAILED(hr))
            return hr;
        visibleIds.resize(newSize);
    }

    if (param.has("sort") && visibleIds.size() > 1)
    {
        VarObj sort = param.get_s("sort");
        if (binary_wo::typeStruct != sort.type())
            return E_DBSHEET_INVALID_INPUT;

        ks_stdptr<IDbExtraSort> spExtraSort;
        hr = DbSheet::GenerateExtraSort(spDbSheetOp.get(), m_commonHelper, sort, &spExtraSort);
        if (FAILED(hr))
            return hr;

        hr = spExtraSort->Exec(visibleIds.data(), visibleIds.size());
        if (FAILED(hr))
            return hr;
    }

    std::set<EtDbId> selectIds;
    if (param.has("selectRecords"))
    {
        VarObj selectRecords = param.get("selectRecords");
        for (int i = 0; i < selectRecords.arrayLength_s(); ++i)
            selectIds.insert(GetEtDbId(selectRecords.at(i)));
    }
    acpt->addKey("queryRecords");
    acpt->beginArray();
    for (size_t i = 0; i < visibleIds.size(); ++i)
    {
        if (selectIds.size() && selectIds.find(visibleIds[i]) != selectIds.end())
            continue;

        acpt->beginStruct();
        EtDbIdStr buf;
        VS(m_pDbCtx->EncodeEtDbId(visibleIds[i], &buf));
        acpt->addString("id", buf);
        acpt->endStruct();
    }
    acpt->endArray();
    return S_OK;
}

HRESULT QueryExecDbVirtualViewQueryRecords::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if (!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
    if (param.has("viewId")) // 关联视图
    {
        EtDbId viewId = GetEtDbId(param, "viewId");
        return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
    }
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

// ================== QueryExecDbVirtualViewFilterValuesList ==================
QueryExecDbVirtualViewFilterValuesList::QueryExecDbVirtualViewFilterValuesList(wo::KEtWorkbook* wwb)
    : ETDbSheetQueryClassBase(wwb, __X("dbSheet.getVirtualViewFilterValuesList"))
{
}

HRESULT QueryExecDbVirtualViewFilterValuesList::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor* acpt)
{
    IDX sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDBSheetViews> spDbSheetViews;
    GetDBSheetViews(sheetStId, &spDbSheetViews);
    if (!spDbSheetViews)
        return E_FAIL;

    const IDBIds *pRecords = nullptr;
    ks_stdptr<IDBIds> spChartRecords;
    ks_stdptr<IDBSheetView> spDbSheetView;
    if (param.has("viewId")) // 关联视图
    {
        EtDbId viewId = GetEtDbId(param, "viewId");
        GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
        if (!spDbSheetView)
            return E_DBSHEET_VIEW_NOT_FOUND;

        pRecords = spDbSheetView->GetVisibleRecords();
    }
    else if (param.has("chartParams"))
    {
        VarObj chartParams = param.get("chartParams");
        UINT sheetId = chartParams.field_uint32("sheetId");
        ks_stdptr<_Worksheet> spWorksheet;
        hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
        if (FAILED(hr))
            return hr;
        if (!spWorksheet->GetSheet()->IsDbDashBoardSheet())
            return E_INVALIDARG;
        hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
        if (FAILED(hr))
            return hr;
        KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
        std::unique_ptr<KDbDashboardModuleWrapper> spModuleWrapper = dashboardModuleMgrWrapper.GetModule(chartParams.field_str("webExtensionKey"));
        if (!spModuleWrapper)
            return E_DBSHEET_DASHBOARD_MODULE_NOT_FOUND;

        if (!spModuleWrapper->IsChart())
            return E_FAIL;

        std::unique_ptr<KDbChartWrapper> spChartWrapper(static_cast<KDbChartWrapper*>(spModuleWrapper.release()));
        UINT dataSourceId = spChartWrapper->GetDataSourceSheetId();
        if (dataSourceId == 0)
            return E_DBSHEET_DASHBOARD_MODULE_NO_DBSHEET_DATASOURCE;
        if (dataSourceId != sheetStId)
            return E_DBSHEET_SHEET_MISMATCH;
        if (chartParams.has("categoryGroupKey"))
        {
            VarObj categoryGroupKey = chartParams.get_s("categoryGroupKey");
            VarObj seriesGroupKey = chartParams.get_s("seriesGroupKey");
            hr = spChartWrapper->GetGroupRecords(categoryGroupKey, seriesGroupKey, &spChartRecords);
        }
        else
        {
            hr = spChartWrapper->GetVisibleRecords(&spChartRecords);
        }
        if (FAILED(hr))
            return hr;
    }
    if (!pRecords)
        pRecords = spDbSheetOp->GetAllRecords();

    KDBFilterValuesSearchType searchTy = DBFVST_None;
    PCWSTR searchStr = NULL;
    binary_wo::VarObj vSc = param.get_s("search");
    if (vSc.type() != binary_wo::typeInvalid)
    {
        hr = _appcore_GainEncodeDecoder()->DecodeKDBFilterValuesSearchType(vSc.field_str("type"), &searchTy);
        if (FAILED(hr))
            return hr;
        searchStr = vSc.field_str("value");
    }

    BOOL bDateClassify = FALSE;
    if (param.has("isDateClassify"))
        bDateClassify = param.field_bool("isDateClassify");
    BOOL bGetAll = param.field_bool("getAll");
    EtDbId fldId = GetEtDbId(param, "fieldId");

    ks_stdptr<IDbExtraFilter> spExtraFilter;
    VS(_appcore_CreateObject(CLSID_KDbExtraFilter, IID_IDbExtraFilter, (void**)&spExtraFilter));
    spExtraFilter->Init(spDbSheetOp.get());

    if (param.has("filter"))
    {
        VarObj filter = param.get_s("filter");
        if (binary_wo::typeStruct != filter.type())
            return E_DBSHEET_FILTER_INVALID;

        const IDbExtraFilter* pFailedFilter = nullptr;
        hr = DbSheet::filtrateByExtraFilter(spExtraFilter, filter, m_commonHelper, spDbSheetOp.get(), true, &pFailedFilter);
        if (FAILED(hr))
            return hr;
    }
    const IDbFieldFilter* pFieldFilter = nullptr;
    // 支持多筛选，需要知道当前是选的哪一个字段，index表示目标字段在传入的filter参数中筛选条件数组的下标
    if (param.has("index"))
        pFieldFilter = spExtraFilter->GetFieldFilter(param.field_uint32("index"));

    if (param.has("condition"))
    {
        VarObj condition = param.get("condition");
        if (condition.has("hiddenRecords"))
        {
            VarObj hiddenRecords = condition.get("hiddenRecords");
            for (int i = 0; i < hiddenRecords.arrayLength_s(); ++i)
                spExtraFilter->AddExtraHiddenRecord(GetEtDbId(hiddenRecords.at(i)));
        }
        if (condition.has("visibleRecordsInfo"))
        {
            VarObj info = condition.get("visibleRecordsInfo");
            IDX baseSheetStId = info.field_uint32("baseSheetStId");
            ks_stdptr<IDBSheetOp> spBaseDbSheetOp;
            hr = m_commonHelper.GetDBSheetOp(baseSheetStId, &spBaseDbSheetOp);
            if (FAILED(hr))
                return hr;

            BOOL bLinkCustomConfig = FALSE;
            alg::managed_token_assist pResult;
            hr = DbSheet::GenDbLinkCustomConfigResult(spBaseDbSheetOp.get(), info, &pResult, bLinkCustomConfig);
            if (FAILED(hr))
                return hr;

            if (bLinkCustomConfig)
            {
                spExtraFilter->EnableExtraVisibleRecords();
                if (pResult)
                {
                    alg::const_handle_token_assist chta(pResult);
                    alg::TOKEN_HANDLE handle = chta.get_handle();
                    if (handle && chta.get_handleType() == alg::ET_HANDLE_DBLINK)
                    {
                        const IDbLinkHandle* pLink = handle->CastDbLink();
                        UINT count = pLink->GetCount();
                        for (UINT i = 0; i < count ; ++i)
                            spExtraFilter->AddExtraVisibleRecord(pLink->GetItemId(i));
                    }
                }
            }
        }
    }
    DBQueryFieldValuesInfo queryParam;
    queryParam.fieldId = fldId;
    queryParam.bGetAll = bGetAll;
    queryParam.searchTy = searchTy;
    queryParam.searchStr = searchStr;
    queryParam.bDateClassify = bDateClassify;
    queryParam.pFieldFilter = pFieldFilter;
    const IDBIds* pValidRecords = spChartRecords ? spChartRecords.get() : pRecords;
    return spExtraFilter->GetFieldValues(spDbSheetView, pValidRecords, acpt, &queryParam);
}

HRESULT QueryExecDbVirtualViewFilterValuesList::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if (!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
    if (param.has("viewId")) // 关联视图
    {
        EtDbId viewId = GetEtDbId(param, "viewId");
        return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
    }
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

} // namespace wo
