#include "message_producer.h"
#include "krt/krtstring.h"
#include "range_subscription.h"
#include <public_header/webcommon/src/chart/wo_transport/channel/channel_types.h>

namespace wo {

inline ValInfo fromVariant(const chart::KCTCell* pX, BOOL b1904)
{
    ValInfo valInf;

    if (!pX || pX->IsEmpty()) {
        valInf.type = ValueType::valEmpty;
        return valInf;
    }

    if (pX->IsString()) {
        valInf.type = ValueType::valStr;
        QString strVal = pX->GetFormatedQString(b1904);
        valInf.strVal = strVal.toUtf8().data();
    }

    if (pX->IsBoolean()) {
        valInf.type = ValueType::valBool;
        valInf.doubleVal = pX->GetForceDouble();
    }

    if (pX->IsDouble()) {
        valInf.type = ValueType::valDouble;
        valInf.doubleVal = pX->GetForceDouble();
    }

    if (pX->IsError()) {
        valInf.type = (ValueType::type)(ValueType::errMain | pX->GetError());
    }

    valInf.numFormat = chart::CHART_W2UTF8(pX->GetNumberFormat());
    return valInf;
}

inline bool SpawnRawSeries(NotifierGroup* group, SeriesData* pData, BOOL b1904 = FALSE)
{
    if (!group || !pData) {
        return false;
    }

    RemoteCalcNameNotify* nameNotifier = group->nameNotifier;
    RemoteCalcCateNotify* cateNotifier = group->cateNotifier;
    RemoteCalcNotifyBase* valNotifier = group->valueNotifier;
    RemoteCalcNotifyBase* bubbleNotifier = group->bubbleNotifier;

    QStringList list;
    IKRanges* pRanges = valNotifier->GetRange();
    if (pRanges) {
        UINT count = 0;
        pRanges->GetCount(&count);
        pData->contexts.bFromRange = (count > 0);
    }

    pData->index = nameNotifier->GetIdx();
    pData->contexts.idx = pData->index;
    pData->dir = cateNotifier->GetDirection();
    pData->contexts.order = pData->index;

    std::vector<QString> names = nameNotifier->GetNames();
    for (auto it = names.begin(); it != names.end(); ++it) {
        pData->names.push_back((*it).toUtf8().data());
    }

    const chart::KCTSglCells* pSC = valNotifier->GetValues();
    if (pSC) {
        size_t count = pSC->GetCount();
        for (size_t idx = 0; idx < count; ++idx) {
            pData->values.push_back(fromVariant(pSC->GetItem(idx), b1904));
        }
    }

    pSC = bubbleNotifier->GetValues();
    if (pSC) {
        size_t count = pSC->GetCount();
        for (size_t idx = 0; idx < count; ++idx) {
            pData->bubbleSize.push_back(fromVariant(pSC->GetItem(idx), b1904));
        }
    }

    const chart::KCTMultiSglCells* pMSC = cateNotifier->GetCategory();
    if (pMSC)  {
        size_t count = pMSC->GetCount();
        for (size_t idx = 0; idx < count; ++idx) {
            std::vector<ValInfo> categories;
            pSC = pMSC->GetItem(idx);
            size_t nSC = (pSC != nullptr) ? pSC->GetCount() : 0;
            for (size_t iSC = 0; iSC < nSC; ++iSC) {
                categories.push_back(fromVariant(pSC->GetItem(iSC), b1904));
            }
            pData->cateNames.push_back(categories);
        }
    }

    pData->cateNameFromRange = true;
    pData->cateRefHidden = cateNotifier->IsHidden();
    pData->valRefHidden = valNotifier->IsHidden();

    pData->contexts.name = chart::CHART_W2UTF8(krt::utf16(nameNotifier->GetNFmla()));
    pData->contexts.fullRefName = chart::CHART_W2UTF8(krt::utf16(nameNotifier->GetBFmla()));

    pData->contexts.category = chart::CHART_W2UTF8(krt::utf16(cateNotifier->GetNFmla()));
    pData->contexts.fullRefCategory = chart::CHART_W2UTF8(krt::utf16(cateNotifier->GetBFmla()));

    pData->contexts.value = chart::CHART_W2UTF8(krt::utf16(valNotifier->GetNFmla()));
    pData->contexts.fullRefValue = chart::CHART_W2UTF8(krt::utf16(valNotifier->GetBFmla()));

    pData->contexts.bubleSize = chart::CHART_W2UTF8(krt::utf16(bubbleNotifier->GetNFmla()));

    return true;
}

ChartMessgeProducer::ChartMessgeProducer()
    : m_pSubsriber(nullptr)
{

}

ChartMessgeProducer::~ChartMessgeProducer()
{

}

void ChartMessgeProducer::AttachSubscriber(SeriesRangeSubscription* pSubsriber)
{
    m_pSubsriber = pSubsriber;
}

void ChartMessgeProducer::BeginMarkDirty(const int index, int dirtyMask)
{
    if (!m_dirtyMap.contains(index)) {
        int mask = 0;
        mask |= dirtyMask;
        m_dirtyMap.insert(index, mask);
        return;
    }

    int mask = m_dirtyMap[index];
    mask |= dirtyMask;
    m_dirtyMap[index] = mask;
}

void ChartMessgeProducer::EndMarkDirty()
{
    if (m_pSubsriber) {
        m_pSubsriber->CheckProducer();
    }
}

int ChartMessgeProducer::GainRangeMask(const int index)
{
    if (m_dirtyMap.contains(index)) {
        return m_dirtyMap[index];
    }

    return 0;
}

/*
* reset mask
*/
void ChartMessgeProducer::ResetMask()
{
    m_dirtyMap.clear();
}

void ChartMessgeProducer::UpdateUUID(const QString& uid)
{
    m_UUID = uid;
}

BinaryMessage ChartMessgeProducer::create()
{
    ChartData rawChart;
    BOOL b1904 = m_pSubsriber->b1904();
    int nGroups = m_pSubsriber->GetGroupSize();
    int countDirty = 0;

    for (int idx = 0; idx < nGroups; ++idx) {
        NotifierGroup* group = m_pSubsriber->GetNotifyGroup(idx, false);
        SeriesData data;

        if (SpawnRawSeries(group, &data, b1904)) {
            rawChart.seriesLst.push_back(data);
            countDirty++;
        }
    }

    BinaryMessage message;
    if (countDirty == nGroups) { //内核改为全量数据生成
        rawChart.uid = chart::CHART_W2UTF8(krt::utf16(m_UUID));
        rawChart.bAll = true;
        message.msgType = BinartyMessageType::MESSAGE_CHART_DATA;
        message.buffer = serialize(rawChart);
    }

    return message;
}

/**!
* @brief 推送chart元信息
*/
BinaryMessage ChartMessgeProducer::createInfo()
{
    BinaryMessage binary;

    return binary;
}

/*!
* @brief 推送chart的series(独立更新)
*/
BinaryMessage ChartMessgeProducer::createSeries()
{
    BinaryMessage binary;

    return binary;
}

/*!
* @brief 推送chart-series的内容
*/
BinaryMessage ChartMessgeProducer::createContext()
{
    BinaryMessage binary;

    return binary;
}

BinaryMessage ChartMessgeProducer::createError()
{
    BinaryMessage binary;

    return binary;
}

/*!
* @brief 推送chart的数据
*/
BinaryMessage ChartMessgeProducer::createData()
{
    BinaryMessage binary;

    return binary;
}

}
