﻿#ifndef __WEBET_SS_RECT_H__
#define __WEBET_SS_RECT_H__

#include "etcore/little_alg.h"
#include "webbase/binvariant/binvarobj.h"
#include "webbase/binvariant/binwriter.h"

namespace wo
{
struct SsRect
{
	SsRect(WebID x, const RECT& y)
		:objSheet(x), rc(y)
	{}

	WebID objSheet;
	RECT rc;
};

inline void CollectFragment(
	const RECT& rc, const RECT* arr, size_t len, std::vector<RECT>& out)
{
	typedef std::pair<size_t, RECT> ITEM_T;
	std::vector_s<ITEM_T> items;
	items.push_back(ITEM_T(0, rc));

	std::vector_s<RECT> buff;
	while (!items.empty())
	{
		ITEM_T item = items.back();
		items.pop_back();

		bool bx = false;
		for (size_t i = item.first; i < len && !bx; ++i)
		{
			RECT sub = {0};
			if (Rect_Contain(arr[i], item.second))
			{
				bx = true;
			}
			else if (Rect_Intersect(arr[i], item.second, sub))
			{
				Rect_Sub(item.second, sub, buff);
				std::for_each(buff.begin(), buff.end(),
					[&items, i](const RECT& rc){
						items.push_back(ITEM_T(i+1, rc));
				});
				buff.clear();
				bx = true;
			}
		}

		if (!bx) out.push_back(item.second);
	}
}

inline void MergeFragment(RECT rc, std::vector<RECT>& coll)
{
	typedef std::pair<INT32, INT32> SEG_T;
	auto ev = [](const RECT& x) ->SEG_T{
		return SEG_T(x.top, x.bottom);
	};
	auto eh = [](const RECT& x) ->SEG_T{
		return SEG_T(x.left, x.right);
	};
	auto fx = [](const SEG_T& lhs, const SEG_T& rhs){
		return (lhs.second+1) == rhs.first || (rhs.second+1) == lhs.first;
	};

	bool bx = true;
	while (bx)
	{
		bx = false;
		for (auto it = coll.begin(); it != coll.end(); ++it)
		{
			if (ev(rc) == ev(*it) && fx(eh(rc), eh(*it)) ||
				eh(rc) == eh(*it) && fx(ev(rc), ev(*it)))
			{
				RECT tmp = rc;
				Rect_Union(tmp, *it, rc);
				(*it) = coll.back();
				coll.pop_back();
				bx = true;
				break;
			}
		}
	}

	coll.push_back(rc);
}

inline RECT VarReadRect(const binary_wo::VarObj& var)
{
	RECT rc = {-1};
	rc.top = var.field_int32("rowFrom");
	rc.bottom = var.field_int32("rowTo");
	rc.left = var.field_int32("colFrom");
	rc.right = var.field_int32("colTo");
	return rc;
}

inline void VarWriteRect(const RECT& rc, binary_wo::VarObj& var)
{
	var.add_field_int32("rowFrom", rc.top);
	var.add_field_int32("rowTo", rc.bottom);
	var.add_field_int32("colFrom", rc.left);
	var.add_field_int32("colTo", rc.right);
}

inline void BinWriterAddRect(binary_wo::BinWriter& ww, const RECT& rc)
{
	ww.addInt32Field(rc.top, "rowFrom");
	ww.addInt32Field(rc.bottom, "rowTo");
	ww.addInt32Field(rc.left, "colFrom");
	ww.addInt32Field(rc.right, "colTo");
}

}//namespace wo

#endif //__WEBET_SS_RECT_H__
