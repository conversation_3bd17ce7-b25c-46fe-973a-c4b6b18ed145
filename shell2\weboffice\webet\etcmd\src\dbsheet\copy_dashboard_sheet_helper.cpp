﻿#include "etstdafx.h"
#include "copy_dashboard_sheet_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include <public_header/drawing/model/abstract_shape.h>

namespace wo
{
HRESULT CopyDashBoardSheetHelper::Init(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet,
                                       IKWebExtensionMgr* pWebExtensionMgr)
{
    m_pSrcWorkSheet = pSrcWorkSheet;
    m_pTarWorkSheet = pTarWorkSheet;
    m_pWebExtensionMgr = pWebExtensionMgr;
    VS(m_pTarWorkSheet->GetWorkbook()->GetBook()->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&m_spDbtBookCtx));
    return S_OK;
}

HRESULT CopyDashBoardSheetHelper::CopySheetInfo()
{
    ks_stdptr<IDBDashBoardDataOp> spSrcDashBoardDataOp;
    m_pSrcWorkSheet->GetSheet()->GetExtDataItem(edSheetDbDashBoardOp, (IUnknown**)&spSrcDashBoardDataOp);
    if (!spSrcDashBoardDataOp)
        return E_FAIL;

    ks_stdptr<IDBDashBoardDataOp> spTarDashBoardDataOp;
    m_pTarWorkSheet->GetSheet()->GetExtDataItem(edSheetDbDashBoardOp, (IUnknown**)&spTarDashBoardDataOp);
    if (!spTarDashBoardDataOp)
        return E_FAIL;

    spTarDashBoardDataOp->SetSheetIcon(spSrcDashBoardDataOp->GetSheetIcon());
    spTarDashBoardDataOp->SetSheetDescription(spSrcDashBoardDataOp->GetSheetDescription());
    return S_OK;
}

HRESULT CopyDashBoardSheetHelper::CopyCharts()
{
    ISheet* pSrcSheet = m_pSrcWorkSheet->GetSheet();
    IDX srcSheetIdx = INVALIDIDX;
    pSrcSheet->GetIndex(&srcSheetIdx);

    ks_stdptr<etoldapi::Shapes> spTarShapes;
    m_pTarWorkSheet->get_Shapes(FALSE, &spTarShapes);
    if (!spTarShapes)
        return E_FAIL;

    ks_stdptr<IDBChartStatisticMgr> spDbChartStatisticMgr;
    pSrcSheet->GetExtDataItem(edDbSheetChartDataStatisticMgr, (IUnknown**)&spDbChartStatisticMgr);
    if (!spDbChartStatisticMgr)
        return E_FAIL;

    for (EtDbIdx i = 0, srcChartCount = spDbChartStatisticMgr->GetSize(); i < srcChartCount; ++i)
    {
        ks_stdptr<IDBChartStatisticModule> spDbChartStatisticModule;
        spDbChartStatisticMgr->GetItemAt(i, &spDbChartStatisticModule);
        if (!spDbChartStatisticModule)
            continue;

        WebExtensionInfo srcWebextInfo = spDbChartStatisticModule->GetWebExtensionInfo();
        if (!srcWebextInfo.webExtensionKey)
            continue;

        IKWebExtension* pSrcWebExtension = m_pWebExtensionMgr->FindWebExtension(srcSheetIdx, srcWebextInfo.webExtensionKey);
        if (!pSrcWebExtension)
            continue;

        PCWSTR webExtensionKey = m_spDbtBookCtx->GainUuidStr();
        if (!webExtensionKey)
            return E_FAIL;

        ks_bstr tarWebExtensionKey(webExtensionKey);
        ks_stdptr<KsoShape> spTarKsoShape;
        HRESULT hr = spTarShapes->AddWebExtension(tarWebExtensionKey, nullptr, WET_DbDataSource, &spTarKsoShape);
        if (FAILED(hr))
            return hr;

        ks_stdptr<IKsoShapeEx> spTarShapeEx = spTarKsoShape;
        if (!spTarShapeEx)
            return E_FAIL;

        ks_stdptr<IKShape> spTarCoreShape;
        spTarShapeEx->GetInterface(IID_IKShape, (void**)&spTarCoreShape);
        drawing::AbstractShape* pTarAbsShape = static_cast<drawing::AbstractShape*>(spTarCoreShape.get());
        IKWebExtension* pTarWebExtension = pTarAbsShape->getWebExtension();
        if (!pTarWebExtension)
            return E_FAIL;

        hr = pSrcWebExtension->CopyTo(pTarWebExtension);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT CopyDashBoardSheetHelper::ExecCopy()
{
    HRESULT hr = CopySheetInfo();
    if (FAILED(hr))
        return hr;

    hr = CopyCharts();
    if (FAILED(hr))
        return hr;
    return S_OK;
}





} // wo
