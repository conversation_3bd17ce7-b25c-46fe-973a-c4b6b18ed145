﻿#include "etstdafx.h"

#include "addTemplateSheets.h"
#include "workbook.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "dbsheet/et_dbsheet_copysheet_helper.h"
#include "dbsheet/db_export_helper.h"
#include "kfc/tools/variant_array.h"
#include "et_hard_define_strings.h"
#include "et_revision_context_impl.h"
#include "utils/sheet_rename_utils.h"
#include "helpers/custom_storage_helper.h"

namespace wo
{
AddTemplateSheetsHelper::AddTemplateSheetsHelper(wo::KEtWorkbook* wwb, wo::KEtRevisionContext* ctx, 
    PCWSTR filePath, bool copyContent, bool delAllSheet, IDBProtectionJudgement* pProtectionJudgement, VarObj& param)
	: m_wwb(wwb)
    , m_ctx(ctx)
	, m_filePath(filePath)
	, m_copyContent(copyContent)
	, m_delAllSheet(delAllSheet)
	, m_hasDbSheet(false)
	, m_spProtectionJudgement(pProtectionJudgement)
    , m_param(param)
    , m_lastDbSheetStId(0)
{
}

HRESULT AddTemplateSheetsHelper::Exec(UINT& activeSheetId)
{
	if (!m_spProtectionJudgement)
		return E_FAIL;
    
    if (!m_wwb->GetBMP()->bDbSheet)
    {
        //db不存在区域权限
        // 当前文件有不可见区域禁止导入
        if (m_ctx->getProtectionCtx()->isBookHasHiddenProperty())
            return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK;
    }
    
	m_newWorksheets.clear();
	_Application* pTarApp = m_wwb->GetCoreApp();
    ks_stdptr<Workbooks> spWorkbooks;
	pTarApp->get_Workbooks(&spWorkbooks);
    if (!spWorkbooks)
        return E_FAIL;

	// 获取目标book
	_Workbook* pTarWorkbook = m_wwb->GetCoreWorkbook();

	// 打开源文件
	OpenWorkbookScope openWorkbookScope(spWorkbooks, pTarApp->GetAppPersist()->GetPersist(), m_filePath);
	HRESULT hr = openWorkbookScope.GetHr();
	if (FAILED(hr))
		return hr;
	_Workbook* pTemplateWorkbook = openWorkbookScope.GetWorkbook();
	if (!pTemplateWorkbook)
		return E_FAIL;
	IBook* pSrcBook = pTemplateWorkbook->GetBook();
	pTarApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(pTarWorkbook);

    //获取目标sheets
    ks_stdptr<etoldapi::Worksheets> spWorksheets;
	pTarWorkbook->get_Worksheets(&spWorksheets);
    if (!spWorksheets)
        return E_FAIL;

    // 用"在...后插入"的方式. 
    IKWorksheet* pSheetInsertAfter = nullptr;

    // 总是插入数据表到末尾的话, sheet的id总是count
    int sheetsCntBeforeTask = spWorksheets->GetSheetCount();
    int insertIdx = sheetsCntBeforeTask - 1;
    pSheetInsertAfter = spWorksheets->GetSheetItem(insertIdx);
    // todo: 对新文件, 设置合适的起始插入位置
    if (!pSheetInsertAfter)
        return E_FAIL;
    const WCHAR* pSrcSheetName = nullptr;
    ISheet* pSheetInsert = pSheetInsertAfter->GetSheet();
    pSheetInsert->GetName(&pSrcSheetName);
    if (0 == xstrcmp(pSrcSheetName, STR_CELL_IMAGE_SHEET_NAME))
    {
        --insertIdx;
        pSheetInsertAfter = spWorksheets->GetSheetItem(insertIdx);
		pSheetInsert = pSheetInsertAfter->GetSheet();
    }
	int remainDashBoardSheetCnt = DbSheet::GetDBDashBoardSheetLimit();
	int remainFpSheetCnt = DbSheet::GetDBFpsheetLimit();
    // 记录要删除的sheetId
    if (m_delAllSheet)
    {
        for (int i = 0; i < insertIdx + 1; ++i)
            m_delSheetVec.push_back(spWorksheets->GetSheetItem(i)->GetSheet()->GetStId());
    }
	else
	{
		remainFpSheetCnt -= spWorksheets->GetSheetCount(stFlexPaper, TRUE);
		remainDashBoardSheetCnt -= spWorksheets->GetSheetCount(stDashBoard, TRUE);
	}
    // 获取源sheets
    ks_stdptr<etoldapi::Worksheets> spTemplateWorksheets;
	pTemplateWorkbook->get_Worksheets(&spTemplateWorksheets);
    if (!spTemplateWorksheets)
        return E_FAIL;
    if (m_wwb->GetBMP()->bDbSheet)
    {
        const std::unordered_set<UINT>& importSheetIds = GetImportSheets();
        DBSheetRenameHelper::RenameConflictDBSheetName(spWorksheets, spTemplateWorksheets, importSheetIds);
    }
	ProcessCrossBookDbData(pTemplateWorkbook);
	IKWorksheets* pTemplateWorksheets = pTemplateWorkbook->GetWorksheets();
	int templateSheetCnt = pTemplateWorksheets->GetSheetCount();
    std::vector<int> templateSheetIdx;
	std::vector<int> dashboardPos;
    std::vector<int> appSheetsDelayCopyPos;
    std::vector<etoldapi::_Worksheet*> appSheets;
    for (int i = 0; i < templateSheetCnt; ++i)
    {
		ISheet* pTemplateSheet = pTemplateWorksheets->GetSheetItem(i)->GetSheet();
        const WCHAR* pSrcSheetName {__X("")};
        pTemplateSheet->GetName(&pSrcSheetName);
        if (xstrcmp(pSrcSheetName, STR_CELL_IMAGE_SHEET_NAME) == 0)
            continue;

        if (!CheckSheetImport(pTemplateSheet->GetStId()))
            continue;

		if (pTemplateSheet->IsFpSheet())
		{
			--remainFpSheetCnt;
			if (remainFpSheetCnt < 0)
				continue;
		}
		else if (pTemplateSheet->IsDbDashBoardSheet())
		{
			--remainDashBoardSheetCnt;
			if (remainDashBoardSheetCnt < 0)
				continue;
			dashboardPos.emplace_back(templateSheetIdx.size());
		}
        else if(pTemplateSheet->IsAppSheet())
        {
			appSheetsDelayCopyPos.emplace_back(templateSheetIdx.size());
        }
        templateSheetIdx.push_back(i);
    }
	if (remainFpSheetCnt < 0)
		m_bReachFpSheetLimit = true;
	if (remainDashBoardSheetCnt < 0)
		m_bReachDashboardLimit = true;

	IBook* pTarBook = pTarWorkbook->GetBook();
	if (!pTarBook)
        return S_FALSE;
    ks_stdptr<IBookOp> ptrBookOp;
	pTarBook->GetOperator(&ptrBookOp);
    app_helper::KBatchUpdateCal buc(ptrBookOp);
	bool clearContact = true;
	bool clearNote = true;
	if (m_forImport)
	{
		clearContact = false;
		clearNote = false;
		hr = DbSheet::copyDbUsersManager(pSrcBook, pTarBook);
		if (FAILED(hr))
			return hr;
	}
	int srcSheetCnt = static_cast<int>(templateSheetIdx.size());
	if (srcSheetCnt > m_maxImportSheetCount && m_maxImportSheetCount > 0)
	{
		m_exceededMaxImportSheetCount = true;
		srcSheetCnt = m_maxImportSheetCount;
	}
	DbSheet::DisableDbProtectScope disPtScope(m_spProtectionJudgement);
	std::vector<IKWorksheet*> srcWorkSheets;
	srcWorkSheets.reserve(srcSheetCnt);
	m_newWorksheets.reserve(srcSheetCnt);
    if (m_copyContent)
    {
        KVariantArrayDim1 varCoppingSheets(srcSheetCnt);
        for (int i = 0; i < srcSheetCnt; ++i)
            varCoppingSheets.SetAt(i, KComVariant(templateSheetIdx[i] + 1));

        ks_stdptr<IKCoreObject> spCoreObjCopySheets;
		hr = pTemplateWorksheets->GetSheet(varCoppingSheets, &spCoreObjCopySheets, stUnknown);
        if (FAILED(hr))
            return hr;
        ks_stdptr<Sheets> spCopySheets = spCoreObjCopySheets;
        // 由于copy对隐藏sheet有特殊的规则，因此先将隐藏sheet设置为可见，等copy结束后再恢复
        SHEETSTATE visibleState;
        pSheetInsert->GetVisible(&visibleState);
        if (visibleState != ssVisible)
            pSheetInsert->SetVisible(ssVisible);
        KComVariant vAfter(pSheetInsertAfter);
        // Excel规则，如果目标位置的Sheet是隐藏的，
        // 1.它会先向后找，如果能找到一个非隐藏的Sheet, 则在这个找到的Sheet的前面作为新的插入点
        // 2.如果向后没有找到，它会向前找，找到一个非隐藏的Sheet, 然后在这个找到的Sheet的后面作为新的插入点
        hr = spCopySheets->Copy(KComVariant(), vAfter);
        if (visibleState != ssVisible)
            pSheetInsert->SetVisible(visibleState);
        if (FAILED(hr))
            return hr;
		for (int i = 0; i < srcSheetCnt; ++i)
		{
			srcWorkSheets.emplace_back(pTemplateWorksheets->GetSheetItem(templateSheetIdx[i]));
			m_newWorksheets.emplace_back(spWorksheets->GetSheetItem(i + insertIdx + 1));
		}
    }
    else
    {
        for (int i = 0; i < srcSheetCnt; ++i)
        {
            //获取源sheet
			srcWorkSheets.emplace_back(pTemplateWorksheets->GetSheetItem(templateSheetIdx[i]));
			ks_castptr<_Worksheet> spSrcWorksheet = srcWorkSheets.back();
			if (!spSrcWorksheet)
				return E_FAIL;

            VARIANT emptyVar;
            V_VT(&emptyVar) = VT_EMPTY;
			KComVariant vAfter(pSheetInsertAfter);
			if (spSrcWorksheet->GetSheet()->IsDbSheet())
				hr = spSrcWorksheet->CopyDbSheet(emptyVar, vAfter, m_copyContent, nullptr);
			else
				hr = spSrcWorksheet->Copy(emptyVar, vAfter, nullptr);
            if (FAILED(hr))
                return hr;
			m_newWorksheets.emplace_back(spWorksheets->GetSheetItem(i + insertIdx + 1));
            // update insert sheet
			pSheetInsertAfter = m_newWorksheets.back();
        }
    }
	std::unordered_map<UINT, UINT> sheetIdMap;
	for (int i = 0; i < srcSheetCnt; ++i)
	{
		ISheet* pSrcSheet = srcWorkSheets[i]->GetSheet();
		ISheet* pTarSheet = m_newWorksheets[i]->GetSheet();
		sheetIdMap[pSrcSheet->GetStId()] = pTarSheet->GetStId();
		if (activeSheetId == 0)
			activeSheetId = pTarSheet->GetStId();
        if(pTarSheet->IsDbSheet())
            m_lastDbSheetStId = pTarSheet->GetStId();

        if (pSrcSheet->IsDbSheet() && pTarSheet->IsDbSheet())
        {
            ks_stdptr<IDBSheetOp> spSrcDbSheetOp;
            ks_stdptr<IDBSheetOp> spTarDbSheetOp;
            VS(DbSheet::GetDBSheetOp(pSrcSheet, &spSrcDbSheetOp));
            VS(DbSheet::GetDBSheetOp(pTarSheet, &spTarDbSheetOp));
            if (spSrcDbSheetOp && spTarDbSheetOp)
            {
                const IDBIds* pIds = spSrcDbSheetOp->GetAllFields();
                spTarDbSheetOp->ResetAllFields(pIds);
                IDbFieldsManager* pTarFieldsMgr = spTarDbSheetOp->GetFieldsManager();
                EtDbIdx cnt = pIds->Count();
                for (EtDbIdx dbIdx = 0; dbIdx < cnt; ++dbIdx)
                {
                    pTarFieldsMgr->AddField(pIds->IdAt(dbIdx), nullptr);
                }
            }
        }
	}

    CopyDBSheetDealCrossSheetDependenceHelper DealCrossSheetDependenceHelper;
    // 先批量拷贝 sheet, 保证各 sheet 的副本已创建, 具备有效 id
    for (int i = 0; i < srcSheetCnt; ++i)
    {
        // 获取源sheet
		ks_castptr<_Worksheet> spSrcWorksheet = srcWorkSheets[i];
		ISheet* pSrcSheet = spSrcWorksheet->GetSheet();
		if (!m_spProtectionJudgement)
			continue;
        // 获取对应的目标sheet
		ks_castptr<_Worksheet> spTarWorksheet = m_newWorksheets[i];
        DbSheet::DisableDbProtectScope disPtScope(m_spProtectionJudgement);
        HRESULT hr = S_OK;
        if (pSrcSheet->IsDbSheet())
        {
            m_hasDbSheet = true;
			CopyDBSheetHelper copyHelper(spSrcWorksheet, spTarWorksheet, &DealCrossSheetDependenceHelper, this->m_fallBackLookUp);
			DbSheetCopyParam copyParam;
			copyParam.copyContent = m_copyContent;
			copyParam.resetModifiedInfoByCurUser = true;
			copyParam.pAttachmentIdMap = m_pAttachmentIdMap;
			copyParam.pStIdMap = &sheetIdMap;
			copyParam.clearType[Et_DbSheetField_Attachment] = m_clearAttachment;
			copyParam.clearType[Et_DbSheetField_Contact] = clearContact;
			copyParam.clearType[Et_DbSheetField_Note] = clearNote;
			copyParam.allowInvalidValueWhenSheetNotInStIdMap = IsEnableImportSheets();
			if (!m_copyContent)
			{
				ks_stdptr<IDBSheetOp> spTemplateSheetOp;
				pSrcSheet->GetExtDataItem(edSheetDbOp, (IUnknown**)&spTemplateSheetOp);
				const IDBIds* recIds = spTemplateSheetOp->GetAllRecords();
				copyParam.recordCnt = recIds->Count();
			}
			hr = copyHelper.Init(copyParam);
			if (FAILED(hr))
				return hr;

            hr = copyHelper.ExecCopy();
            if (FAILED(hr))
                return hr;
            if (copyHelper.GetLostNoteFlag())
                m_lostFlag = true;
        }
        else if (pSrcSheet->IsFpSheet())
        {
            CopyDBFpSheetHelper copyHelper;
            VS(copyHelper.Init(spSrcWorksheet, spTarWorksheet));
            copyHelper.SetAttachmentIdMap(m_pAttachmentIdMap);
            hr = copyHelper.ExecCopy();
            if (FAILED(hr))
                return hr;
        }
    }
	DealCrossSheetDependenceHelper.DealCrossSheetDependence(&sheetIdMap);
    // 仪表盘部分数据依赖于dbsheet，如标脏等；优先处理dbsheet内容的复制再处理仪表盘
	for (int pos : dashboardPos)
	{
		ASSERT(srcWorkSheets[pos]->GetSheet()->IsDbDashBoardSheet());
		CopyDBDashboardHelper dashboardHelper;
		VS(dashboardHelper.Init(srcWorkSheets[pos], m_newWorksheets[pos]));
		dashboardHelper.Prepare4Template(&sheetIdMap);
		hr = dashboardHelper.ExecCopy();
		if (FAILED(hr))
			return hr;
	}

    // appSheet数据依赖于dbsheet(appsheet会绑定dbsheet的view，所以得先拷贝dbsheet)，所以得先把dbsheet的数据给copy过来之后，再去copy appsheet
	for (int pos : appSheetsDelayCopyPos)
    {
        // 获取源sheet
        ks_castptr<_Worksheet> spSrcWorksheet = srcWorkSheets[pos];
        ASSERT(spSrcWorksheet->GetSheet()->IsAppSheet());

        // 获取对应的目标sheet
		ks_castptr<_Worksheet> spTarWorksheet = m_newWorksheets[pos];

        CopyAppSheetHelper appSheetHelper;
        appSheetHelper.Init(spSrcWorksheet, spTarWorksheet, &m_appSharedInfo, &m_param);
        appSheetHelper.InitForTemplate(&sheetIdMap);
        hr = appSheetHelper.ExecCopy();
        if (FAILED(hr))
            return hr;
        appSheets.push_back(spTarWorksheet);
    }

    DbSheet::SetSharedIdParam(appSheets, m_param);

    // 删除需要被替换的sheet
	hr = deleteSheet(spTemplateWorksheets);

    if (FAILED(hr))
        return hr;

    hr = SidebarFolderTreeExec(pSrcBook, pTarBook, sheetIdMap);
    if (FAILED(hr))
        return hr;

    hr = CustomStorageHelper::CopyCustomStorage(pSrcBook, pTarBook, &sheetIdMap);
    if (FAILED(hr))
        return hr;

    return S_OK;
}

void AddTemplateSheetsHelper::rollback()
{
	if (m_newWorksheets.empty())
		return;

	for (IKWorksheet* pWorksheet: m_newWorksheets)
		pWorksheet->DeleteDirectly();
	m_newWorksheets.clear();
}

HRESULT AddTemplateSheetsHelper::deleteSheet(etoldapi::Worksheets* pTemplateWorksheets)
{
    if (m_delSheetVec.size() == 0)
        return S_OK;

    IKWorksheets* pSheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
    if (pSheets == nullptr)
        return E_FAIL;

    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    ks_stdptr<etoldapi::Worksheets> spWorksheets;
    m_wwb->GetCoreWorkbook()->get_Worksheets(&spWorksheets);
    for (int i = 0; i < m_delSheetVec.size(); ++i)
    {
        ks_bstr strName;
        IDX sheetIdx = INVALIDIDX;
        pBook->STSheetToRTSheet(m_delSheetVec[i], &sheetIdx);
        if (sheetIdx == INVALIDIDX)
            return E_FAIL;

        IKWorksheet* pSheet = pSheets->GetSheetItem(sheetIdx);
        if (pSheet == nullptr)
            return E_FAIL;

        ks_stdptr<_Worksheet> pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
        pWorkSheet->get_Name(&strName);
        pSheet->DeleteDirectly();

        if (strName)
        {
            long sheetCnt = 0;
            pTemplateWorksheets->get_Count(&sheetCnt);
            for (int i = 0; i < sheetCnt; ++i)
            {
                ks_stdptr<IKCoreObject> spCoreObjSrc;
                pTemplateWorksheets->get_Item(KComVariant(i + 1, VT_I4), &spCoreObjSrc);
                ks_stdptr<etoldapi::_Worksheet> spTemplateWorksheet = spCoreObjSrc;
                ks_bstr templateSheetName;
                spTemplateWorksheet->get_Name(&templateSheetName);
                if (strName.isEqual(templateSheetName))
                {
                    spWorksheets->get_Item(KComVariant(i + 1 + sheetIdx, VT_I4), &spCoreObjSrc);
                    ks_stdptr<etoldapi::_Worksheet> spUpdateNameWorksheet = spCoreObjSrc;
                    spUpdateNameWorksheet->put_Name(strName);
                    break;
                }
            }
        }
    }
    return S_OK;
}

void AddTemplateSheetsHelper::SerialAppSharedInfo(binary_wo::VarObj* pObj)
{
    if (m_appSharedInfo.empty())
        return;

    binary_wo::VarObj arrObj = pObj->add_field_array("appSharedInfo", binary_wo::typeStruct);
    for (int i = 0; i < m_appSharedInfo.size(); ++i)
    {
        AppSharedInfo sharedInfo = m_appSharedInfo[i];
        binary_wo::VarObj item = arrObj.add_item_struct();
        sharedInfo.Serialize(&item);
    }
}

bool AddTemplateSheetsHelper::HasCopyAirAppSheet()
{
    return !m_appSharedInfo.empty();
}

AirAppInfo AddTemplateSheetsHelper::GetActiveAppInfo()
{
    AirAppInfo info;
    if (m_appSharedInfo.empty())
        return info;
    
    return m_appSharedInfo[0].GetAppInfo();
}

UINT AddTemplateSheetsHelper::GetLastDbSheetStId() const
{
    return m_lastDbSheetStId;
}

// db导入db后，导入侧边栏文件夹tree结构
HRESULT AddTemplateSheetsHelper::SidebarFolderTreeExec(IBook* pSrcBook, IBook* pTarBook, std::unordered_map<UINT, UINT>& sheetIdMap)
{
	ks_stdptr<IDbSidebarFolderTreeManager> spSrcMgr;
	pSrcBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spSrcMgr);
	if (!spSrcMgr) // 为空说明SrcBook从未添加过folder，直接import sheet即可，adj会自动添加sheet至TarBook的tree中
		return S_OK;

    // 若pTarBook的侧边栏tree中之前从未添加过文件夹，则需初始化SidebarFolderTreeManager
	ks_stdptr<IUnknown> spUnknown;
	if (FAILED(pTarBook->GetExtDataItem(edDbSidebarFolderTreeManager, &spUnknown)))
	{
		ks_stdptr<IDbSidebarFolderTreeManager> spTmpMgr;
		VS(_appcore_CreateObject(CLSID_KDbSidebarFolderTreeManager, IID_IDbSidebarFolderTreeManager, (void**)&spTmpMgr));
		spTmpMgr->Init(pTarBook);
		pTarBook->SetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown*)spTmpMgr);
		if (!spTmpMgr)
			return E_FAIL;

		spTmpMgr->FirstAddFolderInit(); // 将当前所有sheet插入到tree中
	}
    ks_stdptr<IDbSidebarFolderTreeManager> spTarMgr;
	pTarBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spTarMgr);
	if (!spTarMgr) 
		return E_FAIL;

    // 若两边folder数量相加大于最大数量限制，则直接import sheet
    if (spSrcMgr->GetAllFolderCount() + spTarMgr->GetAllFolderCount() > _kso_GetWoEtSettings()->GetMaxSidebarFolderTreeFolderCnt())
		return S_OK;

    std::vector<SidebarFolderTreeHelper> srcVec;
    EnumSrcSidebarFolderTreeVec(spSrcMgr, srcVec);

    std::unordered_map<EtDbId, EtDbId> folderNodeIdMap; // src中的folderId，和tar中新建的folderId
    HRESULT hr = E_FAIL;
    for (int i = 0; i < srcVec.size(); ++i)
    {
        if (srcVec[i].type == 0) // folder需要新建添加
        {
            EtDbId newFolderNodeId = INV_EtDbId;
            if (srcVec[i].parentId == 0) // 添加至root节点
            {
                hr = spTarMgr->AddByImport(0, DbSidebarFolderTreeType_Folder, srcVec[i].stId, srcVec[i].name, &newFolderNodeId);
            }
            else
            {
                auto it = folderNodeIdMap.find(srcVec[i].parentId);
                if (it != folderNodeIdMap.end())
                    hr = spTarMgr->AddByImport(it->second, DbSidebarFolderTreeType_Folder, srcVec[i].stId, srcVec[i].name, &newFolderNodeId);
                else
                    continue;
            }
            if (FAILED(hr))
                return hr;

            folderNodeIdMap[srcVec[i].id] = newFolderNodeId;
        }
        else if (srcVec[i].type == 1) // sheet已经添加，故需要移动到对应位置
        {
            UINT newStId = 0;
            auto it = sheetIdMap.find(srcVec[i].stId);
            if (it != sheetIdMap.end())
                newStId = it->second;
            else
                continue;

            EtDbId newSheetNodeId = INV_EtDbId;
            hr = spTarMgr->GetNodeIdByStId(newStId, &newSheetNodeId);
            if (FAILED(hr))
                return hr;

            if (srcVec[i].parentId == 0) // 移动至root节点
            {
                hr = spTarMgr->Move(0, newSheetNodeId, DbSidebarFolderTreeRelPos_PushBack, 0);
            }
            else
            {
                auto it = folderNodeIdMap.find(srcVec[i].parentId);
                if (it != folderNodeIdMap.end())
                    hr = spTarMgr->Move(it->second, newSheetNodeId, DbSidebarFolderTreeRelPos_PushBack, 0);
                else
                    continue;
            }
            if (FAILED(hr))
                return hr;
        }
    }

	return S_OK;
}

// 查询scr文件的侧边栏文件夹tree结构
HRESULT AddTemplateSheetsHelper::EnumSrcSidebarFolderTreeVec(IDbSidebarFolderTreeManager* pMgr,
															std::vector<SidebarFolderTreeHelper>& sidebarFolderTreeVec)
{
    class DbSidebarImportEnum : public IDbSidebarImportEnum
    {
        using CallBack = std::function<HRESULT(EtDbId, DbSidebarFolderTreeType, UINT, PCWSTR, EtDbId)>;
    public:
        DbSidebarImportEnum(const CallBack &cb)
            : m_cb(cb)
        {}

        STDPROC Do(EtDbId id, DbSidebarFolderTreeType type, UINT stId, PCWSTR name, EtDbId parentId) override
        {
            return m_cb(id, type, stId, name, parentId);
        }

    private:
        const CallBack m_cb;
    };

    DbSidebarImportEnum sidebarImportEnum([&sidebarFolderTreeVec](EtDbId id, DbSidebarFolderTreeType type, UINT stId, PCWSTR name, EtDbId parentId) -> HRESULT {
        SidebarFolderTreeHelper helper;
        helper.id = id;
        helper.type = type;
        helper.stId = stId;
        helper.name = name;
        helper.parentId = parentId;

        sidebarFolderTreeVec.emplace_back(helper);
        return S_OK;
    });

	return pMgr->EnumDbSidebarForImport(&sidebarImportEnum);
}

} // namespace wo
