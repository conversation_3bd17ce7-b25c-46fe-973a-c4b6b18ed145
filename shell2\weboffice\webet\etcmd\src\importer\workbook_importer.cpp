﻿#include "etstdafx.h"
#include "workbook_importer.h"

#include <et_hard_define.h>
#include "dbsheet/db_export_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "etcore/et_core_dbsheet.h"
#ifndef X_OS_WINDOWS
#include "kern/errno.h"
#endif
#include "dbsheet/et_dbsheet_utils.h"
#include "kfc/tools/variant_array.h"
#include "webbase/binvariant/binvarobj.h"
#include "helpers/protection_helper.h"
#include "util.h"
#include "db_query_server.h"
#include "appcore/et_appcore_basic_itf.h"
#include "utils/sheet_rename_utils.h"

namespace wo
{
WorkbookImporter::WorkbookImporter(etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook, PCWSTR userId, binary_wo::VarObj& param)
        : m_pSrcWorkbook(pSrcWorkbook), m_pTarWorkbook(pTarWorkbook), m_userId(userId), m_param(param)
{
}

HRESULT WorkbookImporter::exec()
{
    m_firstImportSheetId = 0;
    m_exceededMaxImportSheetCount = false;
    m_exceededMaxDbDashboardCount = false;
    m_srcWorkSheets.clear();
    m_newWorkSheets.clear();
    // 获取worksheets
    ks_stdptr<etoldapi::Worksheets> spTarWorksheets = m_pTarWorkbook->GetWorksheets();
    ks_stdptr<etoldapi::Worksheets> spSrcWorksheets = m_pSrcWorkbook->GetWorksheets();
    if (!spTarWorksheets || !spSrcWorksheets)
        return E_FAIL;
	ProcessCrossBookDbData(m_pSrcWorkbook);
    // 检测sheet数量是否为空
    int tarSheetCount = spTarWorksheets->GetSheetCount();
    int srcSheetCount = spSrcWorksheets->GetSheetCount();
    if (tarSheetCount <= 0 || srcSheetCount <= 0)
        return E_FAIL;

    // 获取最后一个sheet
    ks_stdptr<etoldapi::_Worksheet> spSheetInsertAfter = spTarWorksheets->GetSheetItem(tarSheetCount - 1);
    if (!spSheetInsertAfter)
        return E_FAIL;

    HRESULT hr = E_FAIL;

    std::unordered_set<UINT> needCopyVeryHiddenSheet;
    // 对于应用对应的DbSheet,即使处于深度隐藏状态也需要复制,因此首先遍历收集所有此类DbSheet,由于无法保证应用的源Et先于深度隐藏的DbSheet被遍历到，所以单独先遍历一遍
    class FetchAllNeedCopyVeryHiddenDbSheet : public IAppEtDbRelationItemEnum
    {
        public:
            FetchAllNeedCopyVeryHiddenDbSheet(std::unordered_set<UINT>& needCopyVeryHiddenSheet)
            :m_needCopyVHSheet(needCopyVeryHiddenSheet){}
            STDIMP_(BOOL) Do(IAppEtDbRelationItem* pItem) override
            {
                m_needCopyVHSheet.insert(pItem->GetRelatedDbSheetStId());
                return TRUE;
            }
        private:
            std::unordered_set<UINT>& m_needCopyVHSheet;
    };
    FetchAllNeedCopyVeryHiddenDbSheet fetchAllNeedCopyVeryHiddenDbSheet(needCopyVeryHiddenSheet);    
    bool bSrcHasDbsheet = false;
    for (int i = 0; i < srcSheetCount; ++i)
    {
        ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = spSrcWorksheets->GetSheetItem(i);
        if (!spSrcWorksheet)
            return E_FAIL;
        ISheet* pSrcSheet = spSrcWorksheet->GetSheet();
        if (pSrcSheet->IsDbSheet())
            bSrcHasDbsheet = true;
        ks_stdptr<IUnknown> spUnk;
        pSrcSheet->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
        ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
        if(!spSheetAppEtDbRelations)
            continue;
        spSheetAppEtDbRelations->Enum(&fetchAllNeedCopyVeryHiddenDbSheet);
    }
    
    hr = onBeforeImport();
    if (FAILED(hr))
        return hr;
    //屏蔽权限检查，防止因为权限还没准备好
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	IBook* pBook = m_pTarWorkbook->GetBook();
	ks_stdptr<IUnknown> spUnknown;
	pBook->GetExtDataItem(edDBUserGroups, &spUnknown);
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
    if (spDBUserGroups)
	    spDBUserGroups->GetJudgement(&spProtectionJudgement);
	wo::DbSheet::DisableDbProtectScope disPtScope(spProtectionJudgement);
    // 只有数据表才预处理重名问题
    if (bSrcHasDbsheet && spTarWorksheets && spSrcWorksheets)
        DBSheetRenameHelper::RenameConflictDBSheetName(spTarWorksheets, spSrcWorksheets);
    // 遍历源sheets并在目标book里创建副本
    std::vector<int> srcSheetIdx;
    int dbDashboardCountLimit = DbSheet::GetDBDashBoardSheetLimit();
    int curDbDashboardCount =  spTarWorksheets->GetSheetCount(stDashBoard, TRUE);
    for (int i = 0; i < srcSheetCount; ++i)
    {
        ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = spSrcWorksheets->GetSheetItem(i);
        if (!spSrcWorksheet)
            return E_FAIL;

        // 忽略单元格图片sheet
        ISheet* pSrcSheet = spSrcWorksheet->GetSheet();
        bool tarIsEtBook = !m_pTarWorkbook->GetBook()->GetBMP()->bKsheet && 
                           !m_pTarWorkbook->GetBook()->GetBMP()->bDbSheet;
		bool srcIsEtBook = !m_pSrcWorkbook->GetBook()->GetBMP()->bKsheet && 
                           !m_pSrcWorkbook->GetBook()->GetBMP()->bDbSheet;
		if ((!tarIsEtBook && pSrcSheet->IsOldDbSheet()) || (tarIsEtBook != srcIsEtBook && pSrcSheet->IsDbDashBoardSheet()))
			continue;

        const WCHAR* pSrcSheetName = nullptr;
        pSrcSheet->GetName(&pSrcSheetName);
        if (!xstrcmp(pSrcSheetName, STR_CELL_IMAGE_SHEET_NAME))
            continue;

        if (pSrcSheet->IsFpSheet())
            continue;

        if (pSrcSheet->IsWorkbenchSheet())
            continue;

        {
            bool isSkipAppSheet = false;
            // 兼容旧命令
            if (m_param.has("isSkipAppSheet"))
                isSkipAppSheet = m_param.field_bool("isSkipAppSheet");
            if (!m_pTarWorkbook->GetBook()->GetBMP()->bKsheet)
                isSkipAppSheet = true;
            if (isSkipAppSheet && pSrcSheet->IsAppSheet())
                continue;
        }

        SHEETSTATE ssVisible;
        pSrcSheet->GetVisible(&ssVisible);
        if (ssVisible == ssVeryhidden && needCopyVeryHiddenSheet.find(pSrcSheet->GetStId()) == needCopyVeryHiddenSheet.end())
            continue;

        if (pSrcSheet->IsDbDashBoardSheet())
        {
            if (curDbDashboardCount >= dbDashboardCountLimit)
            {
                m_exceededMaxDbDashboardCount = true;
                continue;
            }
            else
            {
                curDbDashboardCount++;
            }
        }

        srcSheetIdx.push_back(i);
        if (srcSheetIdx.size() == m_maxImportSheetCount)
        {
            m_exceededMaxImportSheetCount = true;
            break;
        }
    }
    srcSheetCount = srcSheetIdx.size();

    if (srcSheetCount > 0)
    {
        KVariantArrayDim1 varCoppingSheets(srcSheetCount);
        for (int i = 0; i < srcSheetCount; ++i)
        {
            varCoppingSheets.SetAt(i, KComVariant(srcSheetIdx[i] + 1));
        }
        ks_stdptr<IKCoreObject> spCoreObjCopySheets;
        hr = spSrcWorksheets->GetSheet(varCoppingSheets, &spCoreObjCopySheets, stUnknown);
        if (FAILED(hr))
            return hr;

        ks_stdptr<Sheets> spCopySheets = spCoreObjCopySheets;
        ks_stdptr<Sheets> spNewSheets;
        VARIANT varAfter = KComVariant(spSheetInsertAfter);
        hr = spCopySheets->Copy(KComVariant(), varAfter, &spNewSheets);
        if (FAILED(hr))
            return hr;

        ASSERT(spNewSheets->GetSheetCount() == srcSheetCount);
        for (int i = 0; i < srcSheetCount; ++i)
        {
            ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = spSrcWorksheets->GetSheetItem(srcSheetIdx[i]);
            ks_stdptr<etoldapi::_Worksheet> spNewWorksheet = spNewSheets->GetSheetItem(i);
            m_srcWorkSheets.push_back(spSrcWorksheet);
            m_newWorkSheets.push_back(spNewWorksheet);

            // 记录第一个导入的sheetid
            ISheet* pNewSheet = spNewWorksheet->GetSheet();
            if (m_firstImportSheetId == 0)
                m_firstImportSheetId = pNewSheet->GetStId();

            hr = onAfterImportOneSheet(spSrcWorksheet, spNewWorksheet);
            if (FAILED(hr))
                return hr;
        }
    }
    
    hr = onAfterImport(m_srcWorkSheets, m_newWorkSheets);
    // 只有数据表才强制触发公式依赖构建
    if (bSrcHasDbsheet && spTarWorksheets)
    {
        int count = spTarWorksheets->GetSheetCount();
        DBSheetRenameHelper::ForceRenameDBSheets(spTarWorksheets, tarSheetCount, count - 1);
    }
    return hr;
}

void WorkbookImporter::setMaxImportSheetCount(int maxImportSheetCount)
{
    m_maxImportSheetCount = maxImportSheetCount;
}

bool WorkbookImporter::isExceededMaxImportSheetCount() const
{
    return m_exceededMaxImportSheetCount;
}

bool WorkbookImporter::isExceededMaxDbDashboardCount() const
{
    return m_exceededMaxDbDashboardCount;
}

UINT WorkbookImporter::getFirstImportSheetId() const
{
    return m_firstImportSheetId;
}

void WorkbookImporter::rollback()
{
    if (m_newWorkSheets.empty())
        return;

    for (const auto& spWorkSheet: m_newWorkSheets)
    {
        spWorkSheet->DeleteDirectly();
    }
    m_newWorkSheets.clear();
}

} // namespace wo
