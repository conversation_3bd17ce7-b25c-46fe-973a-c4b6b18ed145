﻿#ifndef __SHELL2_WEBET_KSIGNALMETRICS_H__
#define __SHELL2_WEBET_KSIGNALMETRICS_H__

#include "kmetrics_i.h"

namespace wo {
class KEtWorkbook;
class KSignalMetricsData;
struct KSignalResult;
class KSignalMetrics: public IWoSignalMetrics
{
public:
    KSignalMetrics();
    KSignalMetrics(const KSignalMetrics &) = delete;
    KSignalMetrics& operator=(const KSignalMetrics &) = delete;
    
    ~KSignalMetrics();
    void setEnable(bool v) override;
    bool isEnable() override;
    void setMinCollectSize(int sz) override;
    bool isNeedMetrics(WebSize sliceSize, wo::MsgType msgType, WebProcType procType) override;
    bool collectMetricData(
        IWebSlice *pSlice, 
        wo::MsgType msgType,
        WebProcType procType,
        PCWSTR traceId = nullptr,
        std::shared_ptr<IWoSignalResultNotify> spNotify = {}) override;
    void interrupt() override;
    void interruptAndWait() override;
    void finishAndWait() override;
    void setNotify(std::shared_ptr<IWoSignalMcNotify> spNotify) override;

    void sendCollectInfo(KEtWorkbook *pWoWb);
private:
    void sendRes(const KSignalResult & res, KEtWorkbook *pWoWb);
    static void threadProc(const std::shared_ptr<KSignalMetricsData> &spData);
    
private:
    std::shared_ptr<KSignalMetricsData> m_spData;
};

}

#endif