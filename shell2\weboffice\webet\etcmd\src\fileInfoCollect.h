#ifndef __WEBET_FILE_INFO_COLLECT_H__
#define __WEBET_FILE_INFO_COLLECT_H__

#include  "../etstdafx.h"
#include "binvariant/binwriter.h"
#include "persist/et_persist_basic_itf.h"

namespace wo
{

struct MemStatInfo
{
    int64_t rss = 0;
    int64_t mfxAllocSize = 0;
    int64_t mfxAllocObjSize = 0;
    int64_t fileSize = 0;
    int64_t dvCount = 0;
    int64_t cfCount = 0;
    int64_t cachedFontCount = 0;
    int64_t sharedConsts = 0;
    int64_t xfsCount = 0;
    int64_t usedCellCount = 0;
    int64_t transactionMem = 0;
    int64_t coreFreeMem = 0; // 内核内存池空闲内存大小
    int64_t rtsMem = 0;
    int64_t msrMem = 0;
    int64_t versionCount = 0;
    int64_t vmSize = 0;
    int64_t sharedMem = 0;
    int64_t codeMem = 0;
    int64_t dataMem = 0;
    int64_t userConnsCount = 0;
};

class KEtWorkbook;
class WoCollectInfo;
class WoFileIncludeCollector;
using binary_wo::BinWriter;
#define CASE_FILEFORMAT_ENUM_TO_STRING(name)                    \
    case name: return __X(#name);
class FileInfoCollector
{
    using CollectCallBack = std::function<void(WoFileIncludeCollector & collector)>;
public:
    FileInfoCollector(KEtWorkbook *workbook, const char * traceId);
    void Collect();
    void CollectMemInfo(const WCHAR * wszName, bool isFileOpen = false, WoCollectInfo *pCot = nullptr);
    void CollectMemInfoDiff();
    void CollectDbSheetRecordInfo();

    static void AddComponentInfo(BinWriter& binWriter, IBook* pBook);
    void CollectMemInfoOnExit(binary_wo::BinWriter&, WoCollectInfo *pCot);

private:
    void collectInfo(const char *collectName, int count = 1, bool fileFormat = false);
    void collectInfo(const WCHAR *collectName, int count = 1, CollectCallBack cb = nullptr);
    bool CollectOpenUrlCnt();
    bool CollectOpenFormulaCnt();
    bool CollectFxbook();
    bool CollectProtectSheet();
    bool collectExclusiveRanges();
    bool CollectPic();
    bool CollectDispImg();
    bool CollectInnerPic();
    bool CollectChart();
    bool CollectComment();
    bool CollectDVCustomList();
    bool CollectSheetFilter();
    bool CollectDbSheet();
    bool CollectDbSheetCount();
    void CollectWorkbenchSheetCount();
    bool CollectPivotTable();
    bool CollectImportrange();
    bool CollectCondFormatSpecTextIOError();
    bool CollectCondFormat();
    bool CollectSubTotalFuncInfo();
    bool CollectCellFuncInfo();
    bool CollectMergeFile();
    bool checkBookInfoCollect(DWORD);
    bool CollectPicCount();
    bool CollectUsedCellCount();
    bool CollectUsedRangeAndProtectInfo();
    bool CollectCellHistoryInfo();
    bool CollectToposortInfo();
    bool CollectAutoFitInfo();
    bool CollectCellCompileFormulaInfo();
    bool CollectImageSize();
    bool CollectExternalCropPic();
    bool CollectExternalLinkFloatPic();
    bool CollectTableCount();
    void CommitMemInfo(WoCollectInfo *pCot, const WCHAR *name, const MemStatInfo& info, MemStatInfo *pDiff);
    bool CollectLinkFormCount();
    WebStr FileFormatToStr(FILEFORMAT type);
    bool GetMemInfo(MemStatInfo& info, bool collectTrans, bool collectFreeMem, int64_t minRss = 0);
    bool CollectDataValidationInfo();
    void CollectSheetProtectionInfo(ISheetProtection* pSheetProtection, int nSheetIdx);
    void CollectMergeCells();
    bool CollectDashBoardCount();

    static int GetFieldType(IDbField* pField);
    static void GetFieldTypeName(int fieldType, OUT PCWSTR* pTypeStr);

    KEtWorkbook *m_pWorkbook;
    static QString m_traceId;

    static MemStatInfo m_lastMemStatInfo;
};

} // wo

#endif // __WEBET_FILE_INFO_COLLECT_H__