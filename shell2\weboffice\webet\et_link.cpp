﻿// -----------------------------------------------------------------------------
// file:   et_link.cpp
// 
// Copyright (C) 2024 Kingsoft Corporation. All rights reserved.
// ----------------------------------------------------------------------------
#include <kfc/com/regserver.h>
#include <alg/alg_guid.c>
#include <kso/api/ksoapi_old.c>
#include <kso/api/chartapi_old.c>
#include <kso/api/smartartapi_old.c>
#include <kso/ole/ole_guid.c>
#include <kde/kde_intf.c>
#include <etcore/et_core_guid.c>
#include <appcore/et_appcore_guid.c>
#include <applogic/etapi_old.c>
#include <applogic/et_applogic_guid.c>
#include <applogic/et_oleworkbook.c>
#include <shell/et_shell_guid.c>
#include <render/et_render_guid.c>
#include <funclib/et_funclib_guid.c>
#include <uilogic/et_uilogic_guid.c>
#include <persist/et_persist_guid.c>
#include <compiler/guid.c>
#include <filefmt/filefmt_guid.c>
#include <io/et_xlsxrw_guid.c>
#include <io/et_xlsbrw_guid.c>
#include <io/et_ethtmrw_guid.c>
#include <io/et_etxmlrw_guid.c>
#include <excelrw/et_excelrw_guid.c>
#include <security/corex.c>
#include <kso/api/apiex_old.c>
#include <db/db_guid.c>

DiagModule g_DiagModule;

DECLARE_KCOMMODULE();

typedef void __stdcall _ATL_TERMFUNC(void* pThis);
typedef _ATL_TERMFUNC* _ATL_TERMFUNC_PTR;

STDMETHODIMP _ModuleAddTermFunc(_ATL_TERMFUNC_PTR pFunc, void* pThis)
{
	return E_NOTIMPL;
}

STDMETHODIMP _ModuleLoadTypeLib(REFGUID rguid,
	WORD wVerMajor,
	WORD wVerMinor,
	LCID lcid,
	ITypeLib** pptlib)
{
	return E_NOTIMPL;
}
