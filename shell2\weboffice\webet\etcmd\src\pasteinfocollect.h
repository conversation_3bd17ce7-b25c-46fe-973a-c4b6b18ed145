﻿#ifndef __WEBET_PASTE_INFO_COLLECT_H__
#define __WEBET_PASTE_INFO_COLLECT_H__

#include "../etstdafx.h"
#include "persist/et_persist_basic_itf.h"

interface IEtCollectInfo;
interface IEtEventTracking;
class KEtRevisionContext;
namespace wo {
class WoPasteCollector
{
public:
    explicit WoPasteCollector(ISheet* pSheet, wo::IEtRevisionContext* ctx, PASTE_SPECIAL pasteType);
    ~WoPasteCollector();
    void setResult(HRESULT hr);
private:
    INT GetCommentCount();
    INT GetNameCount();
private:
    ISheet* m_pSheet = nullptr;
    std::chrono::steady_clock::time_point m_begin;
    PASTE_SPECIAL m_pasteType = PASTE_SPECIAL::psNone;
    IEtEventTracking* m_pEventTracking = nullptr;
    IEtCollectInfo* m_pEtCollectInfo = nullptr;
    HRESULT m_hr = E_FAIL;
    INT m_nameCount = 0;
    INT m_commentCount = 0;
};

}
#endif // __WEBET_PASTE_INFO_COLLECT_H__