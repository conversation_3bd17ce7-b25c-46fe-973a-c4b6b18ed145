﻿#include "etstdafx.h"
#include "db_view_identify.h"
#include "et_dbsheet_utils.h"
#include "db/db_basic_itf.h"

#define MaxCellCountQA 600000

namespace wo
{

KIdentifyDBView::KIdentifyDBView(IKWorksheet* pWorksheet, IDBSheetOp* pDbSheetOp)
    : m_spWorksheet(pWorksheet), m_spDbSheetOp(pDbSheetOp)
{

}

KIdentifyDBView::~KIdentifyDBView()
{

}

void KIdentifyDBView::identify(IDBSheetView* pView, OUT QJsonObject& jsonObj)
{
    if(!pView)
        return;

    QJsonArray jsonTableList;
    QJsonObject jsonTable;
    
    jsonTable.insert("tableIndex", QString::number(0));
    if (!transformTable(pView, jsonTable))
        return;
    jsonTableList.append(jsonTable);
    jsonObj.insert("tableList", jsonTableList);

    const WCHAR* pViewName = nullptr;
    pViewName = pView->GetName();
    jsonObj.insert("sheetname", krt::fromUtf16(pViewName));

    if (m_spWorksheet->GetWorkbook() && m_spWorksheet->GetWorkbook()->GetBook())
    {
        const WCHAR* pBookName = nullptr;
        m_spWorksheet->GetWorkbook()->GetBook()->GetName(&pBookName);
        jsonObj.insert("bookname", krt::fromUtf16(pBookName));
    }
    jsonObj.insert("recommendChart", true);
}

// 将表格视图中的表数据转为json
bool KIdentifyDBView::transformTable(IDBSheetView* pView, QJsonObject& jsonTable)
{
    if (!pView)
        return false;
    
    // 打包可见字段的字段名，并记录字段类型
	QJsonObject jsonFieldCells;
    const IDBIds* pVisibleFieldsIds = pView->GetVisibleFields();
    std::vector<ET_DbSheet_FieldType> baseFieldTypeVec(pVisibleFieldsIds->Count());
    for (int j = 0, fieldsCnt = pVisibleFieldsIds->Count(); j < fieldsCnt; ++j)
    {
        EtDbId fieldId = pVisibleFieldsIds->IdAt(j);
        ks_stdptr<IDbField> spField;
        m_spDbSheetOp->GetFieldsManager()->GetField(fieldId, &spField);
        if (!spField)
            continue;

        ET_DbSheet_FieldType fieldType = spField->GetType();
        ET_DbSheet_FieldType baseFieldType = fieldType;
        if (fieldType == Et_DbSheetField_Lookup)
        {
            ks_stdptr<IDbField> spBaseField;
            ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
            spFieldLookup->GetLookupBaseField(&spBaseField);
            if (spBaseField)
                baseFieldType = spBaseField->GetType();
        }
        baseFieldTypeVec[j] = baseFieldType;
        
        QJsonObject jsonCell;
        jsonCell.insert("value", krt::fromUtf16(spField->GetName()));
        
        QString strCell = QString("(%1, %2)").arg(j).arg(j);
        jsonFieldCells.insert(strCell.toUtf8().constData(), jsonCell);
    }

    QJsonObject jsonTableInfo;
    jsonTableInfo.insert("0", jsonFieldCells);
    m_nCount = pVisibleFieldsIds->Count();

    // 打包所有可见记录
    const IDBIds* pVisibleRecordsIds = pView->GetVisibleRecords();
    for (int i = 0, recordsCnt = pVisibleRecordsIds->Count(); i < recordsCnt; ++i)
    {
        m_isEmptyRow = true;
        QJsonObject jsonRecordCells;
        EtDbId recId = pVisibleRecordsIds->IdAt(i);
        for (int j = 0, fieldsCnt = pVisibleFieldsIds->Count(); j < fieldsCnt; ++j)
        {
            EtDbId fieldId = pVisibleFieldsIds->IdAt(j);

            switch (baseFieldTypeVec[j])
			{
                case Et_DbSheetField_Url:
                {
                    urlToJson(recId, fieldId, jsonRecordCells, j);
					break;
                }
                case Et_DbSheetField_Checkbox:
                {
                    boolToJson(recId, fieldId, jsonRecordCells, j);
					break;
                }
                case Et_DbSheetField_Number:
                case Et_DbSheetField_Rating:
                {
                    numberToJson(recId, fieldId, jsonRecordCells, j);
					break;
                }
                case Et_DbSheetField_Formula:
                case Et_DbSheetField_FormulaResult:
                case Et_DbSheetField_Complete:
                case Et_DbSheetField_Percentage:
                {
                    formulaToJson(recId, fieldId, jsonRecordCells, j);
                    break;
                }
                case Et_DbSheetField_Date:
                case Et_DbSheetField_Time:
                {
                    dateToJson(recId, fieldId, jsonRecordCells, j);
                    break;
                }
                case Et_DbSheetField_Currency:
                {
                    currencyToJson(recId, fieldId, jsonRecordCells, j);
                    break;
                }
                default:
                {
                    ks_bstr displayVal;
                    m_spDbSheetOp->GetDisplayString(recId, fieldId, &displayVal);
                    
                    if (displayVal.empty())
                        break;

                    QJsonObject jsonCell;
                    jsonCell.insert("value", krt::fromUtf16(displayVal.c_str()));

                    QString strCell = QString("(%1, %2)").arg(j).arg(j);
                    jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

                    m_isEmptyRow = false;
                    m_nCount++;
                    break;
                }
            }
        }
        
        if(hasSheetOverflow())
        {
            return false;
        }

        if(!m_isEmptyRow)
        {
            jsonTableInfo.insert(QString::number(i+1), jsonRecordCells);
        }
    }

    jsonTable.insert("tableInfo", jsonTableInfo);
    jsonTable.insert("cellCount", m_nCount);

    QJsonArray jsonTableStart;
    jsonTableStart.append(0);
    jsonTableStart.append(0);
    jsonTable.insert("tableStart", jsonTableStart);

    QJsonArray jsonTableEnd;
    jsonTableEnd.append(int32(pVisibleRecordsIds->Count()));
    jsonTableEnd.append(int32(pVisibleFieldsIds->Count()-1));
    jsonTable.insert("tableEnd", jsonTableEnd);

    return true;
}

void KIdentifyDBView::currencyToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col)
{
    ks_bstr displayVal;
    m_spDbSheetOp->GetDisplayString(recId, fieldId, &displayVal);

    if(displayVal.empty())
        return;
    
    QString val = krt::fromUtf16(displayVal.c_str());
    val.remove(",");
    QJsonObject jsonCell;
    jsonCell.insert("value", val);
    
    QString strCell = QString("(%1, %2)").arg(col).arg(col);
    jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

    m_isEmptyRow = false;
    m_nCount++;
}

void KIdentifyDBView::dateToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col)
{
    const_token_ptr pToken = nullptr;
    m_spDbSheetOp->GetValueToken(recId, fieldId, &pToken);
    if (!pToken)
        return;
    
    if (!alg::const_vdbl_token_assist::is_type(pToken))
        return;

    int year = -1, month = -1, day = -1, hour = -1, minute = -1, second = -1, weekday = -1;
    double date = alg::const_vdbl_token_assist(pToken).get_value();

    ks_stdptr<IDbField> spField;
    m_spDbSheetOp->GetFieldsManager()->GetField(fieldId, &spField);
    if (!spField)
        return;
    bool b1904 = spField->GetDbSheetData()->GetBook()->Is1904DateSystem();
    alg::VDS_ParseTime(date, b1904, &year, &month, &day, &hour, &minute, &second, &weekday);

    if (year <= 0 || month <= 0 || day < 0 || hour < 0 || minute < 0 || second < 0)
        return;
    
    PCWSTR nf = spField->GetNumberFormat();
    QString dateValue;
    if (xstrstr(nf, __X("h:mm:s")))
    {
        if(day == 0)  // 时间
        {
            dateValue = QString("%1:%2:%3").arg(hour).arg(minute).arg(second);
        }
        else  // 日期
        {
            dateValue = QString("%1/%2/%3 %4:%5:%6").arg(year).arg(month).arg(day).arg(hour).arg(minute).arg(second);
        }
    }
    else if (xstrstr(nf, __X("h:m")))
    {
        if(day == 0)  // 时间
        {
            dateValue = QString("%1:%2").arg(hour).arg(minute);
        }
        else  // 日期
        {
            dateValue = QString("%1/%2/%3 %4:%5").arg(year).arg(month).arg(day).arg(hour).arg(minute);
        }
    }
    else if (xstrstr(nf, __X("d")))
        dateValue = QString("%1/%2/%3").arg(year).arg(month).arg(day);
    else if (xstrstr(nf, __X("m")) || xstrstr(nf, __X("M")))
        dateValue = QString("%1/%2").arg(year).arg(month);
    else if (xstrstr(nf, __X("y")) || xstrstr(nf, __X("Y")))
        dateValue = QString("%1").arg(year);

    QJsonObject jsonCell;
    jsonCell.insert("value", dateValue);
    QString strCell = QString("(%1, %2)").arg(col).arg(col);
    jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

    m_isEmptyRow = false;
    m_nCount++;
}

void KIdentifyDBView::numberToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col)
{
    ks_bstr displayVal;
    m_spDbSheetOp->GetDisplayString(recId, fieldId, &displayVal);

    if(displayVal.empty())
        return;
    
    QJsonObject jsonCell;
    QString val = krt::fromUtf16(displayVal.c_str());
    if(val.contains(","))
    {
        jsonCell.insert("value", val);
    }
    else
    {
        jsonCell.insert("value", val.toDouble());
    }
    
    QString strCell = QString("(%1, %2)").arg(col).arg(col);
    jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

    m_isEmptyRow = false;
    m_nCount++;
}

void KIdentifyDBView::boolToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col)
{
    ks_bstr displayVal;
    m_spDbSheetOp->GetDisplayString(recId, fieldId, &displayVal);

    if (displayVal.empty())
        return;
    
    QJsonObject jsonCell;
    if(krt::fromUtf16(displayVal.c_str()) == "1")
    {
        jsonCell.insert("value", QJsonValue(true));
    }
    else
    {
        jsonCell.insert("value", QJsonValue(false));
    }
    
    QString strCell = QString("(%1, %2)").arg(col).arg(col);
    jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

    m_isEmptyRow = false;
    m_nCount++;
}

void KIdentifyDBView::urlToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col)
{
    const_token_ptr pToken = nullptr;
    m_spDbSheetOp->GetValueToken(recId, fieldId, &pToken);
    if (!pToken)
        return;
        
    if (!alg::const_handle_token_assist::is_type(pToken))
        return;

    alg::const_handle_token_assist chta(pToken);
    alg::TOKEN_HANDLE handle = chta.get_handle();
    if (!handle)
        return;
    ks_stdptr<IDbHyperlinkHandle> spHyperlinkHandle = handle->CastUnknown();
    if(!spHyperlinkHandle)
        return;
    PCWSTR bAddress = spHyperlinkHandle->GetAddress();
    PCWSTR displayVal = spHyperlinkHandle->GetDisplayText();
    QString val = krt::fromUtf16(displayVal) + " " + krt::fromUtf16(bAddress);

    QJsonObject jsonCell;
    jsonCell.insert("value", val);

    QString strCell = QString("(%1, %2)").arg(col).arg(col);
    jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

    m_isEmptyRow = false;
    m_nCount++;
}

void KIdentifyDBView::formulaToJson(EtDbId recId, EtDbId fieldId, QJsonObject& jsonRecordCells, int col)
{
    const_token_ptr pToken = nullptr;
    m_spDbSheetOp->GetValueToken(recId, fieldId, &pToken);
    if (!pToken)
        return;

    switch (alg::const_token_assist(pToken).major_type())
    {
        case alg::ETP_VINT:
        {
            int val = alg::const_vint_token_assist(pToken).get_value();
            QJsonObject jsonCell;
            jsonCell.insert("value", val);
            QString strCell = QString("(%1, %2)").arg(col).arg(col);
            jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

            m_isEmptyRow = false;
            m_nCount++;
            break;
        }
        case alg::ETP_VDBL:
        {
            double val = alg::const_vdbl_token_assist(pToken).get_value();
            QJsonObject jsonCell;
            jsonCell.insert("value", val);
            QString strCell = QString("(%1, %2)").arg(col).arg(col);
            jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

            m_isEmptyRow = false;
            m_nCount++;
            break;
        }
        default:
        {
            ks_bstr displayVal;
            m_spDbSheetOp->GetDisplayString(recId, fieldId, &displayVal);
            
            if (displayVal.empty())
                break;

            QJsonObject jsonCell;
            jsonCell.insert("value", krt::fromUtf16(displayVal.c_str()));
            QString strCell = QString("(%1, %2)").arg(col).arg(col);
            jsonRecordCells.insert(strCell.toUtf8().constData(), jsonCell);

            m_isEmptyRow = false;
            m_nCount++;
            break;
        }
    }
}

bool KIdentifyDBView::hasSheetOverflow() const
{
	return m_nCount > MaxCellCountQA;
}

} // namespace wo