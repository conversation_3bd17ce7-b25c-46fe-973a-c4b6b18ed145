﻿#ifndef __WEBET_GRIDSHEET_UTILS_H__
#define __WEBET_GRIDSHEET_UTILS_H__

namespace wo
{
	HRESULT GetValidSheetName(IKWorksheets*, IKWorksheet*, PCWSTR name, BSTR*);
	namespace GridSheet
	{
		enum MergedCellMode
		{
			Default, // 直接取对应单元格的值
			Fill,    // 合并单元格里的每一个单元格都视为一致的，等效于取左上角第一个单元格的值
		};
		HRESULT GetCellToken(IBookOp*, IDX, ROW, COL, MergedCellMode, const_token_ptr*);
		HRESULT GetCellText(IETStringTools*, IKWorksheet*, ROW, COL, MergedCellMode, BSTR*);
	} // namespace GridSheet
} // namespace wo

#endif // __WEBET_GRIDSHEET_UTILS_H__
