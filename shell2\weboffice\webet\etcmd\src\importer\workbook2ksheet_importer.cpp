﻿#include "workbook2ksheet_importer.h"
#include "etcore/et_core_dbsheet.h"
#ifndef X_OS_WINDOWS
#include "kern/errno.h"
#endif
#include "dbsheet/et_dbsheet_utils.h"
#include "dbsheet/et_dbsheet_copysheet_helper.h"
#include "helpers/protection_helper.h"
#include "webextension_context_restorer.h"
#include "utils/qrlabel_utils.h"
#include "../helpers/app/create_app_view_helper.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"
#include "dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h"
#include "helpers/custom_storage_helper.h"
#include "db/db_basic_itf.h"

namespace wo
{
Workbook2KSheetImporter::Workbook2KSheetImporter(etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook,
                                                 PCWSTR userId, IDBProtectionJudgement* pProtectionJudgement,
                                                 KEtRevisionContext* ctx, VarObj& param)
        : WorkbookImporter(pSrcWorkbook, pTarWorkbook, userId, param), m_spProtectionJudgement(pProtectionJudgement),
          m_pCtx(ctx)
{
}

void Workbook2KSheetImporter::clearDefaultValue(IDBSheetOp* pSrcSheetOp)
{
    const IDBIds* pFields = pSrcSheetOp->GetAllFields();
    IDbFieldsManager* pFieldsManager = pSrcSheetOp->GetFieldsManager();
    EtDbIdx fieldCount = pFields->Count();
    for (EtDbIdx fieldIndex = 0; fieldIndex < fieldCount; ++fieldIndex)
    {
        EtDbId fieldId = pFields->IdAt(fieldIndex);
        ks_stdptr<IDbField> spField;
        pFieldsManager->GetField(fieldId, &spField);
        spField->SetDefaultValTypeForIO(DbSheet_Field_Dvt_Normal);
        spField->SetDefaultValForIO(__X(""));
    }
}

void Workbook2KSheetImporter::collectDbAttachmentId(IDBSheetOp* pSrcSheetOp)
{
    collectDbViewAttachmentId(pSrcSheetOp);
    
    const IDBIds* pFields = pSrcSheetOp->GetAllFields();
    const IDBIds* pRecords = pSrcSheetOp->GetAllRecords();
    IDbFieldsManager* pFieldsManager = pSrcSheetOp->GetFieldsManager();
    EtDbIdx fieldCount = pFields->Count();
    EtDbIdx recordCount = pRecords->Count();
    for (EtDbIdx fieldIndex = 0; fieldIndex < fieldCount; ++fieldIndex)
    {
        EtDbId fieldId = pFields->IdAt(fieldIndex);
        ks_stdptr<IDbField> spField;
        pFieldsManager->GetField(fieldId, &spField);
        if (spField->GetType() != Et_DbSheetField_Attachment)
            continue;
        if (spField->IsSyncLookupField())
            continue;

        for (EtDbIdx recordIndex = 0; recordIndex < recordCount; ++recordIndex)
        {
            EtDbId recordId = pRecords->IdAt(recordIndex);
            const_token_ptr pToken = nullptr;
            HRESULT hr = pSrcSheetOp->GetValueToken(recordId, fieldId, &pToken);
            if (FAILED(hr) || !pToken)
                continue;

            if (not alg::const_handle_token_assist::is_type(pToken))
                continue;

            alg::const_handle_token_assist chta(pToken);
            alg::TOKEN_HANDLE handle = chta.get_handle();
            ks_stdptr <IDbTokenArrayHandle> spTokenArrayHandleCopy;
            _db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle, (void**)&spTokenArrayHandleCopy);

            if (!handle || chta.get_handleType() != etexec::ET_HANDLE_TOKENARRAY)
                continue;

            ks_stdptr<IDbTokenArrayHandle> spHandleArray = handle->CastUnknown();
            UINT handleCount = spHandleArray->GetCount();
            for (int i = 0; i < handleCount; ++i)
            {
                const_token_ptr pItem = nullptr;
                spHandleArray->Item(i, &pItem);
                alg::const_handle_token_assist itemChta(pItem);
                alg::TOKEN_HANDLE itemHandle = itemChta.get_handle();
                if (!itemHandle || itemChta.get_handleType() != etexec::ET_HANDLE_DBATTACHMENT)
                    continue;

                ks_stdptr<IDbAttachmentHandle> spAttachmentHandle = itemHandle->CastUnknown();
                //ksheet暂时不支持mp4，先过滤
                if(!m_isSupportVideoImport 
                && m_unsupportedAttachmentFileSuffixTypes.find(spAttachmentHandle->GetContentType()) != m_unsupportedAttachmentFileSuffixTypes.end())
                    continue;
                
                ks_stdptr<IDbAttachmentHandle> spTokenAttachmentHandleCopy;
                _db_CreateObject(CLSID_KDbAttachmentHandle, IID_IDbAttachmentHandle, (void **)&spTokenAttachmentHandleCopy);
                spTokenAttachmentHandleCopy->Init(spAttachmentHandle->GetFileId(), spAttachmentHandle->GetSource(), spAttachmentHandle->GetContentType(), 
                spAttachmentHandle->GetName(), spAttachmentHandle->GetSize(), spAttachmentHandle->GetLinkUrl(), spAttachmentHandle->GetImgSize());
                alg::managed_handle_token_assist mhta;
                mhta.create(alg::ET_HANDLE_DBATTACHMENT, spTokenAttachmentHandleCopy);
                spTokenArrayHandleCopy->Add(mhta.detach());

                PCWSTR attachmentId = spAttachmentHandle->GetFileId();
                m_pCtx->addCloudImgAttachmentIdDirectly(attachmentId);
            }
            alg::managed_handle_token_assist mhtaNew;
            mhtaNew.create(alg::ET_HANDLE_TOKENARRAY, spTokenArrayHandleCopy);
            VS(pSrcSheetOp->SetTokenValue(recordId, fieldId, mhtaNew));
        }
    }
}

void Workbook2KSheetImporter::collectDbViewAttachmentId(IDBSheetOp* pSrcSheetOp)
{
    //收集所有view的背景图附件
    ks_stdptr<IDBSheetViews> spDbViews;
    HRESULT hr = pSrcSheetOp->GetDbSheetViews(&spDbViews);
    if(FAILED(hr) || !spDbViews)
        return;
    
    IDBSheetCtx *pDbCtx = _appcore_GainDbSheetContext();
    if (!pDbCtx)
        return;

    ks_stdptr<IDBSheetViewsEnum> spEnum;
    if (SUCCEEDED(spDbViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
    {
        do
        {
            ks_stdptr<IDBSheetView> spDbSheetView;
            spEnum->GetCurView(&spDbSheetView);
            if (!spDbSheetView)
                continue;

            PCWSTR attachmentId = spDbSheetView->GetBackgroundImageAttachment();
            if(xstrcmp(attachmentId, __X("")) != 0)
            {
                m_pCtx->addCloudImgAttachmentIdDirectly(attachmentId);
            }
        }while (SUCCEEDED(spEnum->Next()));
    }
}


void Workbook2KSheetImporter::convertUnsupportedDbFields(IDBSheetOp* pSrcSheetOp)
{
    const IDBIds* pFields = pSrcSheetOp->GetAllFields();
    const IDBIds* pRecords = pSrcSheetOp->GetAllRecords();
    IDbFieldsManager* pFieldsManager = pSrcSheetOp->GetFieldsManager();
    EtDbIdx fieldCount = pFields->Count();
    EtDbIdx recordCount = pRecords->Count();
    for (EtDbIdx fieldIndex = 0; fieldIndex < fieldCount; ++fieldIndex)
    {
        EtDbId fieldId = pFields->IdAt(fieldIndex);
        ks_stdptr<IDbField> spField;
        pFieldsManager->GetField(fieldId, &spField);
        ET_DbSheet_FieldType fieldType = spField->GetType();
        if (m_unsupportedDbFieldTypes.count(fieldType) == 0)
            continue;

        switch (fieldType)
        {
            case Et_DbSheetField_LastModifiedBy:
                spField->SetTypeForIO(Et_DbSheetField_Contact);
                break;
            case Et_DbSheetField_LastModifiedTime:
                spField->SetTypeForIO(Et_DbSheetField_Date);
                break;
            default:
            {
                spField->SetTypeForIO(Et_DbSheetField_MultiLineText);
                if (fieldType != Et_DbSheetField_Automations)
                {
                    for (EtDbIdx recordIndex = 0; recordIndex < recordCount; ++recordIndex)
                    {
                        EtDbId recordId = pRecords->IdAt(recordIndex);
                        ks_bstr bstrVal;
                        HRESULT hr = pSrcSheetOp->GetDisplayString(recordId, fieldId, &bstrVal);
                        if (FAILED(hr))
                            continue;
                        pSrcSheetOp->SetValue(recordId, fieldId, bstrVal.c_str());
                    }
                }
                break;
            }
        }
    }
}

HRESULT Workbook2KSheetImporter::handleUnsupportedDbViews(ISheet* pSrcSheet)
{
    if (!pSrcSheet)
        return E_FAIL;

    ks_stdptr<IDBSheetViews> spDbSheetViews;
    VS(DbSheet::GetDBSheetViews(pSrcSheet, &spDbSheetViews));
    if (!spDbSheetViews)
        return E_FAIL;

    std::vector<EtDbId> unSupportedDbViewIds;
    for (int i = 0, cnt = spDbSheetViews->GetSize(Et_DBSheetViewUse_ForDb); i < cnt; ++i)
    {
        ks_stdptr<IDBSheetView> spDbSheetView;
        spDbSheetViews->GetItemAt(i, Et_DBSheetViewUse_ForDb, &spDbSheetView);
        if (spDbSheetView && spDbSheetView->GetType() == et_DBSheetView_Calendar)
            unSupportedDbViewIds.push_back(spDbSheetView->GetId());
    }

    for (size_t i = 0; i < unSupportedDbViewIds.size(); i++)
    {
        HRESULT hr = spDbSheetViews->DelItem(unSupportedDbViewIds.at(i));
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT Workbook2KSheetImporter::onBeforeImport()
{
    m_sheetIdMap.clear();
    IBook* pSrcBook = m_pSrcWorkbook->GetBook();
    if (!pSrcBook)
        return E_FAIL;

    ks_stdptr<etoldapi::Worksheets> spSrcWorksheets = m_pSrcWorkbook->GetWorksheets();
    if (!spSrcWorksheets)
        return E_FAIL;

    HRESULT hr = S_OK;
    if (pSrcBook->GetBMP()->bDbSheet)
    {
        // 清除db权限
        ks_stdptr<IUnknown> spUnknown;
        pSrcBook->GetExtDataItem(edDBUserGroups, &spUnknown);
        ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
        ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
        if (spDBUserGroups)
        {
            spDBUserGroups->Clear();
            spDBUserGroups->GetJudgement(&spProtectionJudgement);
        }
        DbSheet::DisableDbProtectScope disPtScope(spProtectionJudgement);

        // 将ksheet不支持的db字段转化为其它字段
        hr = handleAllDbSheetFields(spSrcWorksheets, true);
        if (FAILED(hr))
            return hr;

        hr = handleAllDbSheetViews(spSrcWorksheets, true);
        if (FAILED(hr))
        return hr;

        hr = handleAllDbSheetWebExtensions();
    } 
    else if (pSrcBook->GetBMP()->bKsheet)
    {
        hr = handleAllDbSheetFields(spSrcWorksheets, false);
        if (FAILED(hr))
            return hr;

        hr = handleAllDbSheetViews(spSrcWorksheets, false);
    }
    return hr;
}

HRESULT Workbook2KSheetImporter::handleAllDbSheetFields(etoldapi::Worksheets * pWss, bool isCvtUnsupport)
{
// 将ksheet不支持的db字段转化为其它字段
    int srcSheetCount = pWss->GetSheetCount();
    for (int i = 0; i < srcSheetCount; i++)
    {
        ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = pWss->GetSheetItem(i);
        if (!spSrcWorksheet)
            return E_FAIL;

        ISheet* pSrcSheet = spSrcWorksheet->GetSheet();
        if (!pSrcSheet->IsDbSheet())
            continue;

        ks_stdptr<IDBSheetOp> spSrcSheetOp;
        VS(DbSheet::GetDBSheetOp(pSrcSheet, &spSrcSheetOp));
        if (spSrcSheetOp)
        {
            if (isCvtUnsupport)
                convertUnsupportedDbFields(spSrcSheetOp);
            // 采集轻维表附件id以便导入
            collectDbAttachmentId(spSrcSheetOp);
            if (m_clearDefaultValue)
                clearDefaultValue(spSrcSheetOp);
        }
    }
    return S_OK;
}

void Workbook2KSheetImporter::handleCustomStorageAttachments()
{
    IBook* pBook = m_pSrcWorkbook->GetBook();
    INT sheetCount = 0;
    pBook->GetSheetCount(&sheetCount);
    if (sheetCount == 0)
        return;

    ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    pBook->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    if (!spCustomStorMgr)
        return;

    for (int i = 0; i < sheetCount; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        if (!spSheet || !spSheet->IsDbDashBoardSheet())
            continue;

        UINT sheetId = spSheet->GetStId();
        if (m_sheetIdMap.find(sheetId) == m_sheetIdMap.end())
            continue;

        PCWSTR pValue = spCustomStorMgr->GetValue(sheetId, __X("dashboardTitleIconId"));
        if (!pValue)
            continue;
        m_pCtx->addCloudImgAttachmentIdDirectly(pValue);
    }
}

HRESULT Workbook2KSheetImporter::handleAllDbSheetViews(etoldapi::Worksheets * pWss, bool isCvtUnsupport)
{
    // 处理AS不支持的视图
    int srcSheetCount = pWss->GetSheetCount();
    for (int i = 0; i < srcSheetCount; i++)
    {
        ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = pWss->GetSheetItem(i);
        if (!spSrcWorksheet)
            return E_FAIL;

        ISheet* pSrcSheet = spSrcWorksheet->GetSheet();
        if (!pSrcSheet->IsDbSheet())
            continue;

        if (isCvtUnsupport)
        {
            HRESULT hr = handleUnsupportedDbViews(pSrcSheet);
            if (FAILED(hr))
                return hr;
        }
    }
    return S_OK;
}

HRESULT Workbook2KSheetImporter::handleAllDbSheetWebExtensions()
{
    ks_stdptr<etoldapi::Worksheets> spSrcWorksheets = m_pSrcWorkbook->GetWorksheets();
    if (!spSrcWorksheets)
        return E_FAIL;
    // 处理AS不支持的仪表盘组件
    HRESULT hr = S_OK;
    for (int i = 0, length = spSrcWorksheets->GetSheetCount(); i < length; i++)
    {
        ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = spSrcWorksheets->GetSheetItem(i);
        if (!spSrcWorksheet)
            return E_FAIL;

        ISheet* pSrcSheet = spSrcWorksheet->GetSheet();
        if (!pSrcSheet->IsDbDashBoardSheet())
            continue;

        KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(nullptr, spSrcWorksheet);
        hr = dashboardModuleMgrWrapper.DeleteAllDbPlugin();
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT Workbook2KSheetImporter::onAfterImportOneSheet(etoldapi::_Worksheet* pSrcWorkSheet,
                                                       etoldapi::_Worksheet* pNewWorkSheet)
{
    ISheet* pSrcSheet = pSrcWorkSheet->GetSheet();
    ISheet* pNewSheet = pNewWorkSheet->GetSheet();
    m_sheetIdMap[pSrcSheet->GetStId()] = pNewSheet->GetStId();
    if (!pSrcSheet->GetBMP()->bKsheet && !pSrcSheet->GetBMP()->bDbSheet)
        clearProtection(pNewWorkSheet);

    ks_stdptr<etoldapi::Worksheets> spTarWorkSheets = m_pTarWorkbook->GetWorksheets();
    if (!spTarWorkSheets)
        return E_FAIL;
    
    if(pNewSheet->IsGridSheet())
    {
        IDX sheetIdx;
        pNewSheet->GetIndex(&sheetIdx);
        util::QRLabelHelper qrLabelHelper(m_pTarWorkbook, m_pCtx, util::QRLabelHelper::UseType_Import_KSheet2KSheet, &m_param);
        qrLabelHelper.ConvertQRLabel2CellImg(sheetIdx);
    }

    // ksheet的数据表名称校验比之前没有对名称做限制的db严格
    if (pSrcSheet->IsDbSheet())
    {
        WCHAR wszSheetName[MAX_SHEET_NAME_CCH + 1] = {0};
        const WCHAR* pcwsName = NULL;
        pNewSheet->GetName(&pcwsName);
        if (pNewWorkSheet->IsValidSheetName(pcwsName) != S_OK)
        {
            HRESULT hr = spTarWorkSheets->ValidateSheetName(stGrid_DB, pcwsName, wszSheetName, countof(wszSheetName));
            if (FAILED(hr))
                return hr;
            ks_bstr strName(wszSheetName);
            hr = pNewWorkSheet->put_Name(strName);
            if (FAILED(hr))
                return hr;
        }
    }
    return S_OK;
}


HRESULT Workbook2KSheetImporter::onAfterImport(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                                               const std::vector<etoldapi::_Worksheet*>& newWorkSheets)
{
    if (srcWorkSheets.size() != newWorkSheets.size())
        return E_FAIL;

	HRESULT hr = S_OK;
    CopyDBSheetDealCrossSheetDependenceHelper DealCrossSheetDependenceHelper;
    for (int i = 0; i < srcWorkSheets.size(); i++)
    {
        etoldapi::_Worksheet* pSrcWorksheet = srcWorkSheets[i];
        if (!pSrcWorksheet || !pSrcWorksheet->GetSheet()->IsDbSheet() || !m_spProtectionJudgement)
            continue;

        etoldapi::_Worksheet* pNewWorkSheet = newWorkSheets[i];
        if (!pNewWorkSheet)
            continue;

        DbSheet::DisableDbSheetProtectScope disableDbSheetProtectScope(m_spProtectionJudgement,
                                                                       pNewWorkSheet->GetSheet()->GetStId());
		CopyDBSheetHelper copyHelper(pSrcWorksheet, pNewWorkSheet, &DealCrossSheetDependenceHelper, this->m_fallBackLookupField);
		DbSheetCopyParam copyParam;
		copyParam.copyFieldTitleFormat = m_bCopyFieldTitleFormat;
		copyParam.resetModifiedInfoByCurUser = true;
		copyParam.pStIdMap = &m_sheetIdMap;
		copyParam.clearType[Et_DbSheetField_Note] = true;
		hr = copyHelper.Init(copyParam);
		if (FAILED(hr))
			return hr;
		hr = copyHelper.ExecCopy();
        if (FAILED(hr))
            return hr;
        if (m_bCopyFieldTitleFormatToDefaultView)
        {
            hr = copyFieldTitleFormatToDefaultView(pSrcWorksheet, pNewWorkSheet);
            if (FAILED(hr))
                return hr;
        }
    }
    DealCrossSheetDependenceHelper.DealCrossSheetDependence(&m_sheetIdMap);
	hr = copyOldDbSheets();
    if (FAILED(hr))
        return hr;

    hr = copyAppSheets(srcWorkSheets, newWorkSheets);
    if (FAILED(hr))
        return hr;

    hr = copyDbDashBoardSheets(srcWorkSheets, newWorkSheets);
    if (FAILED(hr))
        return hr;

    hr = CustomStorageHelper::CopyCustomStorage(m_pSrcWorkbook->GetBook(), m_pTarWorkbook->GetBook(), &m_sheetIdMap);
    if (FAILED(hr))
        return hr;

    handleCustomStorageAttachments();
    return DbSheet::copyDbUsersManager(m_pSrcWorkbook->GetBook(), m_pTarWorkbook->GetBook());
}

HRESULT Workbook2KSheetImporter::copySheet(etoldapi::_Worksheet* pSrcWorkSheet, VARIANT before,
                                           VARIANT after, IKCoreObject** ppNewSheetObj)
{
    if (pSrcWorkSheet->GetSheet()->IsDbSheet())
        return pSrcWorkSheet->CopyDbSheet(before, after, true, ppNewSheetObj);
    else
        return pSrcWorkSheet->Copy(before, after, ppNewSheetObj);
}

HRESULT Workbook2KSheetImporter::copyOldDbSheets()
{
    // 之前的导入命令没有这两个参数
    if (!m_param.has("xlsxImport") || !m_param.has("defaultName"))
        return S_OK;

    VarObj xlsxImport = m_param.get("xlsxImport");
    VarObj defaultName = m_param.get("defaultName");

    ks_stdptr<etoldapi::Worksheets> spTarWorksheets = m_pTarWorkbook->GetWorksheets();
    ks_stdptr<etoldapi::Worksheets> spSrcWorksheets = m_pSrcWorkbook->GetWorksheets();
    int srcSheetCount = spSrcWorksheets->GetSheetCount();
    for (int i = 0; i < srcSheetCount; ++i)
    {
        ks_stdptr<etoldapi::_Worksheet> spSrcWorksheet = spSrcWorksheets->GetSheetItem(i);
        if (!spSrcWorksheet)
            return E_FAIL;

        ISheet* pSrcSheet = spSrcWorksheet->GetSheet();
        if (!pSrcSheet->IsOldDbSheet())
            continue;

        SHEETSTATE ssVisible;
        pSrcSheet->GetVisible(&ssVisible);
        if (ssVisible == ssVeryhidden)
            continue;

        IDX etSheetIdx;
        pSrcSheet->GetIndex(&etSheetIdx);
        wo::Et2DbImporterSameProcess importer(m_pSrcWorkbook, m_pTarWorkbook, etSheetIdx, INVALIDIDX, m_pCtx, false,
                                              defaultName, xlsxImport, m_spProtectionJudgement.get(), true, true);
        HRESULT hr = importer.Exec();
        etoldapi::_Worksheet* pDbWorksheet = importer.GetDbWorksheet();
        if (pDbWorksheet)
        {
            m_srcWorkSheets.push_back(spSrcWorksheet);
            m_newWorkSheets.push_back(pDbWorksheet);
        }
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT Workbook2KSheetImporter::copyDbDashBoardSheets(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                                                       const std::vector<etoldapi::_Worksheet*>& newWorkSheets)
{
    WebExtensionContextRestorer contextRestorer(m_pSrcWorkbook, m_pTarWorkbook, m_sheetIdMap);
    for (int i = 0; i < srcWorkSheets.size(); i++)
    {
        etoldapi::_Worksheet* pSrcWorksheet = srcWorkSheets[i];
        if (!pSrcWorksheet || !pSrcWorksheet->GetSheet()->IsDbDashBoardSheet() || !m_spProtectionJudgement)
            continue;

        etoldapi::_Worksheet* pNewWorkSheet = newWorkSheets[i];
        if (!pNewWorkSheet)
            continue;

        CopyDBDashboardHelper dashboardHelper;
        dashboardHelper.Init(pSrcWorksheet, pNewWorkSheet);
        dashboardHelper.Prepare4Template(&m_sheetIdMap);
        VS(dashboardHelper.ExecCopy());
        contextRestorer.RestoreSheet(pNewWorkSheet->GetSheet());
    }


    return S_OK;
}

HRESULT Workbook2KSheetImporter::copyAppSheets(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                                               const std::vector<etoldapi::_Worksheet*>& newWorkSheets)
{
    std::vector<etoldapi::_Worksheet*> appSheets;
    for (int i = 0; i < srcWorkSheets.size(); i++)
    {
        etoldapi::_Worksheet* pSrcWorksheet = srcWorkSheets[i];
        if (!pSrcWorksheet || !pSrcWorksheet->GetSheet()->IsAppSheet() || !m_spProtectionJudgement)
            continue;

        etoldapi::_Worksheet* pNewWorkSheet = newWorkSheets[i];
        if (!pNewWorkSheet)
            continue;

        CopyAppSheetHelper appSheetHelper;
        appSheetHelper.Init(pSrcWorksheet, pNewWorkSheet, &m_appSharedInfo, &m_param);
        appSheetHelper.InitForTemplate(&m_sheetIdMap);
        HRESULT hr = appSheetHelper.ExecCopy();
        if (FAILED(hr))
            return hr;

        appSheets.push_back(pNewWorkSheet);
    }

    // 重建查询应用db和et源表的映射关系
    int stIdOffset = !srcWorkSheets.empty() && !newWorkSheets.empty() 
                     ? newWorkSheets[0]->GetSheet()->GetStId() - srcWorkSheets[0]->GetSheet()->GetStId() : -1;

    class RebuildEtDbRelation : public IAppEtDbRelationItemEnum
    {
        public:
            RebuildEtDbRelation(ISheet* sheet, int offset)
                :m_sheet(sheet)
                ,m_offset(offset) {}
            STDPROC_(BOOL) Do(IAppEtDbRelationItem* p)
            {
                ROW iRowFrom = 0, iRowTo = 0;
                COL iColFrom = 0, iColTo = 0;
                p->GetTableHeadArea(iRowFrom, iRowTo, iColFrom, iColTo);
                if (m_sheet && m_offset != -1)
                    CreateAppViewHelper::BuildEtDbRelation(m_sheet, m_offset + p->GetRelatedDbSheetStId(), iRowFrom, iRowTo, iColFrom, iColTo);
                return TRUE;
            }
        private:
            ISheet* m_sheet = nullptr;
            int m_offset = 0;
    };
    for (int i = 0; i < srcWorkSheets.size(); i++)
    {
        etoldapi::_Worksheet* pSrcWorksheet = srcWorkSheets[i];
        ks_stdptr<IUnknown> spUnk; 
        pSrcWorksheet->GetSheet()->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
        ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
        if(!spSheetAppEtDbRelations)
            continue;
        RebuildEtDbRelation rbedr(newWorkSheets[i]->GetSheet(), stIdOffset);
        spSheetAppEtDbRelations->Enum(&rbedr);
    }

    DbSheet::SetSharedIdParam(appSheets, m_param);
    return S_OK;
}

HRESULT Workbook2KSheetImporter::copyFieldTitleFormatToDefaultView(etoldapi::_Worksheet* pWorkSheet, etoldapi::_Worksheet* pNewWorkSheet)
{
	ISheet *pSheet = pWorkSheet->GetSheet();
	ISheet *pNewSheet = pNewWorkSheet->GetSheet();
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	ks_stdptr<IDBSheetViews> spNewDbSheetViews;
	VS(DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews));
	VS(DbSheet::GetDBSheetViews(pNewSheet, &spNewDbSheetViews));
    if (!spDbSheetViews || !spNewDbSheetViews)
		return S_FALSE;
    IDBSheetView* pDefaultView = spNewDbSheetViews->GetDefaultView();
    if (pDefaultView == nullptr)
        return S_FALSE;
    class CopyFieldTitleFormatEnum : public IDbSheetFieldTitleFormatEnum
    {
        public:
            CopyFieldTitleFormatEnum(IDBSheetView_Grid *pView): m_pView(pView) {};
            HRESULT Do(EtDbId id, DWORD bgColor) override
            {
                m_pView->SetFieldTitleFormat(id, bgColor);
                return S_OK;
            };
        private:
            IDBSheetView_Grid *m_pView;
    };

    if (pSheet->GetBMP()->bKsheet)
    {
        ks_stdptr<IDBSheetView_Grid> spDefaultGridView = pDefaultView;
        ks_stdptr<IDBSheetView_Grid> spGridView = spDbSheetViews->GetDefaultView();
        CopyFieldTitleFormatEnum cftfe(spDefaultGridView);
        spGridView->EnumFieldTitleFormat(&cftfe);
    }
    else 
    {
        for (int i = 0, cnt = spDbSheetViews->GetSize(Et_DBSheetViewUse_ForDb); i < cnt; ++i)
        {
            ks_stdptr<IDBSheetView> spDbSheetView;
            spDbSheetViews->GetItemAt(i, Et_DBSheetViewUse_ForDb, &spDbSheetView);
            if (spDbSheetView && spDbSheetView->GetType() == et_DBSheetView_Grid && pDefaultView->GetType() == et_DBSheetView_Grid)
            {
                ks_stdptr<IDBSheetView_Grid> spDefaultGridView = pDefaultView;
                ks_stdptr<IDBSheetView_Grid> spGridView = spDbSheetView;
                CopyFieldTitleFormatEnum cftfe(spDefaultGridView);
                HRESULT hr = spGridView->EnumFieldTitleFormat(&cftfe);
                if (FAILED(hr))
                    return hr;
                break;
            }
        }
    }

	return S_OK;
}

void Workbook2KSheetImporter::setClearDefaultValue(bool clearDefaultValue)
{
    m_clearDefaultValue = clearDefaultValue;
}

void Workbook2KSheetImporter::setIsSupportVideoImport(bool isSupportVideoImport)
{
    m_isSupportVideoImport = isSupportVideoImport;
}

void Workbook2KSheetImporter::setCopyFieldTitleFormat(bool bCopyFieldTitleFormat)
{
    m_bCopyFieldTitleFormat = bCopyFieldTitleFormat;
}

void Workbook2KSheetImporter::setCopyFieldTitleFormatToDefaultView(bool bCopyFieldTitleFormatToDefaultView)
{
    m_bCopyFieldTitleFormatToDefaultView = bCopyFieldTitleFormatToDefaultView;
}

std::set<ET_DbSheet_FieldType> Workbook2KSheetImporter::getUnsupportedDbFieldTypes()
{
    return m_unsupportedDbFieldTypes;
}

void Workbook2KSheetImporter::setUnsupportedDbFieldTypes(const std::set<ET_DbSheet_FieldType>& unsupportedDbFieldTypes)
{
    m_unsupportedDbFieldTypes = unsupportedDbFieldTypes;
}

std::set<ET_DBSheet_ViewType> Workbook2KSheetImporter::getUnsupportedDbViews()
{
    return m_unsupportedDbViews;
}

void Workbook2KSheetImporter::setUnsupportedDbViews(const std::set<ET_DBSheet_ViewType>& unsupportedDbViews)
{
    m_unsupportedDbViews = unsupportedDbViews;
}

std::set<ks_wstring> Workbook2KSheetImporter::getUnsupportedAttachmentFileSuffixTypes()
{
    return m_unsupportedAttachmentFileSuffixTypes;
}

void Workbook2KSheetImporter::setUnsupportedAttachmentFileSuffixTypes(const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes)
{
    m_unsupportedAttachmentFileSuffixTypes = std::move(unsupportedAttachmentFileSuffixTypes);
}

void Workbook2KSheetImporter::SerialAppSharedInfo(binary_wo::VarObj* pObj)
{
    if (m_appSharedInfo.size() == 0)
        return;

    binary_wo::VarObj arrObj = pObj->add_field_array("appSharedInfo", binary_wo::typeStruct);
    for (int i = 0; i < m_appSharedInfo.size(); ++i)
    {
        AppSharedInfo sharedInfo = m_appSharedInfo[i];
        binary_wo::VarObj item = arrObj.add_item_struct();
        sharedInfo.Serialize(&item);
    }
}

} // namespace wo
