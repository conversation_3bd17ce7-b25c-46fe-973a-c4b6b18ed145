﻿#include "etstdafx.h"
#include "et_op_tf.h"
#include "et_revision_context_impl.h"
#include "alg/alg.h"
#include "wo/struct_op.h"
#include "util.h"
#include "workbook.h"

namespace wo
{

struct NormalTransOp
{
	static bool tfBySv(OpBase* opCl, const OpBase* opSv, VecOp& vecOpClRes)
	{
		return opCl->tfBySv(opSv, vecOpClRes);
	}
	static bool tfByCl(OpBase* opSv, const OpBase* opCl, VecOp& vecOpSvRes)
	{
		return opSv->tfByCl(opCl, vecOpSvRes);
	}
};

struct CrossDatTransOp
{
	static bool tfBySv(OpBase* opCl, const OpBase* opSv, VecOp& vecOpClRes)
	{
		return opCl->tfBySvCrossSht(opSv, vecOpClRes);
	}
	static bool tfByCl(OpBase* opSv, const OpBase* opCl, VecOp& vecOpSvRes)
	{
		return opSv->tfByClCrossSht(opCl, vecOpSvRes);
	}
};

KEtOpTrsfmt::KEtOpTrsfmt()
	: m_ctx(nullptr)
{
	m_opEnv.m_bk = nullptr;
	m_opEnv.m_bmp = nullptr;
	m_opEnv.m_ctx = nullptr;
}

bool KEtOpTrsfmt::TransformationInner(IRevisionContext* ctx, KwTasks& tasks, TaskContext* tskCtx, bool bReverser)
{
	KEtRevisionContext* pCtx = static_cast<KEtRevisionContext*>(ctx);
	m_opEnv.m_bk = pCtx->getBook();
	m_opEnv.m_bmp = m_opEnv.m_bk->GetBMP();
	m_opEnv.m_bookOp.clear();
	m_opEnv.m_bk->GetOperator(&m_opEnv.m_bookOp);
	m_opEnv.m_ctx = pCtx;
	wo::util::SlowCallTimeStat callTime("TransformationInner", 100, [pCtx](unsigned int ms) {
		pCtx->woWorkbook()->coreMetric().addTransformationTime(ms);
	});

	clear();
	m_ctx = tskCtx;
	bool bChange = false;
	for (auto itTsk = tasks.begin(); itTsk != tasks.end(); ++itTsk)
	{
		if (TransTask(*itTsk, bReverser))
			bChange = true;
	}
	if (bChange)
	{
		KCoreMetric & cm = pCtx->woWorkbook()->coreMetric();
		cm.onTransformChanged();
	}
	return bChange;
}

bool KEtOpTrsfmt::Transformation(IRevisionContext* ctx, KwTasks& tasks, TaskContext* tskCtx)
{
	return TransformationInner(ctx, tasks, tskCtx, false);
}

bool KEtOpTrsfmt::Transformation_Reverser(IRevisionContext* ctx, KwTasks& tasks, TaskContext* tskCtx)
{
	return TransformationInner(ctx, tasks, tskCtx, true);
}

bool KEtOpTrsfmt::TransTask(KwTask* tsk, bool bReverser)
{
	OpBase* op = CreateOp(&m_opEnv, tsk->getSrcCommand());
	if (op == nullptr)
	{
		tsk->push_back(KwCommand::copyFrom(tsk->getSrcCommand()->cast()));
		return false;
	}

	ASSERT(tsk->empty());
	KwCommand* pSrcCmd = tsk->getSrcCommand();
	AbsObject* shtObj = GetMainObj(pSrcCmd, "objSheet", "sheetIdx", "sheetStId");
	AbsObject* shapeObj = nullptr;
	if (pSrcCmd->cast().has("shapeObjSheet") || pSrcCmd->cast().has("shapeSheetStId"))
		shapeObj = GetMainObj(pSrcCmd, "shapeObjSheet", "shapeSheetIdx", "shapeSheetStId");

	if (!shtObj && !shapeObj)
	{
		return true;
	}

	bool bChange = false;
	VecOp vecOpCl;
	vecOpCl.m_v.push_back(op);

	bool bHasMainOps = false;
	if (shapeObj) // shape有可能被全部删掉
	{
		// 目前只有shape.paste有可能跨出现两个不同的
		VecOp* vecOpSv = GetMainOps(shapeObj, bReverser);
		if (vecOpSv && !vecOpSv->m_v.empty())
		{
			bHasMainOps = true;
			if (TransTaskInner<CrossDatTransOp>(vecOpCl, vecOpSv, bReverser))
				bChange = true;
		}
	}
	if (shtObj)
	{
		VecOp* vecOpSv = GetMainOps(shtObj, bReverser);
		if (vecOpSv && !vecOpSv->m_v.empty())
		{
			bHasMainOps = true;
			if (TransTaskInner<NormalTransOp>(vecOpCl, vecOpSv, bReverser))
				bChange = true;
		}
	}

	if (adjustIdx(vecOpCl))
		bChange = true;

	for (size_t i = 0; i < vecOpCl.m_v.size(); ++i)
	{
		OpBase* opCl = vecOpCl.m_v[i];
		if (!opCl)
			continue;

		if (TransFormular(opCl, bReverser))
			bChange = true;
	}

	if (bChange || bHasMainOps) // bHasMainOps是兼容以前逻辑
	{
		for (size_t i = 0; i < vecOpCl.m_v.size(); ++i)
		{
			OpBase* opCl = vecOpCl.m_v[i];
			if (!opCl)
				continue;
			tsk->push_back(opCl->detachCmd());
		}
	}
	else
		tsk->push_back(KwCommand::copyFrom(tsk->getSrcCommand()->cast()));
	return bChange;
}

template <typename TransformOp>
bool KEtOpTrsfmt::TransTaskInner(VecOp& vecOpCl, VecOp* vecOpSv, bool bReverser)
{
	bool bChange = false;
	std::vector<size_t> vecIdx(vecOpCl.m_v.size(), 0);

	VecOp vecOpClRes;
	VecOp vecOpSvRes;
	for (size_t iC = 0; iC < vecOpCl.m_v.size(); ++iC)
	{
		size_t isBegin = vecIdx[iC];
		for (size_t iS = isBegin; iS < vecOpSv->m_v.size(); ++iS)
		{
			OpBase* opCl = vecOpCl.m_v[iC];
			OpBase* opSv = vecOpSv->m_v[iS];
			if (!opCl || !opSv)
				continue;

			bool bClChange = TransformOp::tfBySv(opCl, opSv, vecOpClRes);
			bool bSvChange = TransformOp::tfByCl(opSv, opCl, vecOpSvRes);

			if (bClChange)
			{
				delete vecOpCl.m_v[iC];
				vecOpCl.m_v[iC] = vecOpClRes.m_v[0];
				if (vecOpClRes.m_v.size() > 1)
				{
					vecOpCl.m_v.insert(vecOpCl.m_v.begin() + (iC + 1), vecOpClRes.m_v.begin() + 1, vecOpClRes.m_v.end());
					size_t nextIdx = iS + (bSvChange ? vecOpSvRes.m_v.size() : 1);
					vecIdx.insert(vecIdx.begin() + (iC + 1), vecOpClRes.m_v.size(), nextIdx);
				}
				bChange = true;
			}

			if (bSvChange)
			{
				delete vecOpSv->m_v[iS];
				vecOpSv->m_v[iS] = vecOpSvRes.m_v[0];
				if (vecOpSvRes.m_v.size() > 1)
				{
					vecOpSv->m_v.insert(vecOpSv->m_v.begin() + (iS + 1), vecOpSvRes.m_v.begin() + 1, vecOpSvRes.m_v.end());
					iS += vecOpSvRes.m_v.size() - 1;
				}
			}

			vecOpSvRes.m_v.clear();
			vecOpClRes.m_v.clear();
		}
	}
	return bChange;
}

AbsObject* KEtOpTrsfmt::GetMainObj(KwCommand* cmd, WebName objidName, WebName idxName, WebName stIdName)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	INT idx = INVALIDIDX;

	if (stIdName && param.has(stIdName))
	{
		UINT sheetStId = param.field_uint32(stIdName);
		m_opEnv.m_bk->STSheetToRTSheet(sheetStId, &idx);
	} 
	else
	{
	WebID webID = param.field_web_id(objidName);
	if (!m_opEnv.m_ctx->isInRevisionMode() || !param.has(idxName))
        idx = m_opEnv.m_ctx->getSheetIndex(webID);
    else
        idx = param.field_int32(idxName);
	}

	return m_opEnv.m_ctx->getSheetMain(idx);
}

VecOp* KEtOpTrsfmt::GetMainOps(AbsObject* mainObj, bool bReverser)
{
	if (mainObj == nullptr) return nullptr;

	auto it = m_mainOpsMap.find(mainObj);
	if (it != m_mainOpsMap.end())
		return it->second;

	Operates vecOp;
	if (mainObj)
		m_ctx->getMainObjectOperates(mainObj, vecOp);

	et_sptr<VecOp> spOpDats;
	spOpDats = new VecOp;
	for (auto it = vecOp.begin(); it != vecOp.end(); ++it)
	{
		AbsEtOperate* pOp = static_cast<AbsEtOperate*>(*it);
		AbsEtOperate::EtOpType opType = pOp->opType();

		switch (opType)
		{
		case AbsEtOperate::etOpInsertRg:
			{
				OpInsertRange* pDat = static_cast<OpInsertRange*>(pOp);
				if (!bReverser)
					spOpDats->m_v.push_back(new OpRangeInsert(&m_opEnv, NULL, pDat->getRect(), pDat->isVert() ? dir_vert : dir_hori));
				else
					spOpDats->m_v.push_back(new OpRangeDelete(&m_opEnv, NULL, pDat->getRect(), pDat->isVert() ? dir_vert : dir_hori));
			}
			break;
		case AbsEtOperate::etOpDelRg:
			{
				OpDeleteRange* pDat = static_cast<OpDeleteRange*>(pOp);
				if (!bReverser)
					spOpDats->m_v.push_back(new OpRangeDelete(&m_opEnv, NULL, pDat->getRect(), pDat->isVert() ? dir_vert : dir_hori));
				else
					spOpDats->m_v.push_back(new OpRangeInsert(&m_opEnv, NULL, pDat->getRect(), pDat->isVert() ? dir_vert : dir_hori));
			}
			break;
		case AbsEtOperate::etOpCutInsertVert:
			{
				OpCutInsertVert* pDat = static_cast<OpCutInsertVert*>(pOp);
				if (!bReverser)
				{
					spOpDats->m_v.push_back(new OpRangeCutInsertVert(&m_opEnv, pDat->getRectTop(), pDat->getRectBottom()));
				}
				else
				{
					RECT revTop = pDat->getRectTop();
					RECT revBottom = pDat->getRectBottom();
					revTop.bottom = revTop.top + (revBottom.bottom - revBottom.top);
					revBottom.top = revTop.bottom + 1;
					spOpDats->m_v.push_back(new OpRangeCutInsertVert(&m_opEnv, revTop, revBottom));
				}
			}
			break;
		case AbsEtOperate::etOpCutInsertHorz:
			{
				OpCutInsertHorz* pDat = static_cast<OpCutInsertHorz*>(pOp);
				if (!bReverser)
				{
					spOpDats->m_v.push_back(new OpRangeCutInsertHorz(&m_opEnv, pDat->getRectLeft(), pDat->getRectRight()));
				}
				else
				{
					RECT revLeft = pDat->getRectLeft();
					RECT revRight = pDat->getRectRight();
					revLeft.right = revLeft.left + (revRight.right - revRight.left);
					revRight.left = revLeft.right + 1;
					spOpDats->m_v.push_back(new OpRangeCutInsertHorz(&m_opEnv, revLeft, revRight));
				}
			}
			break;
		case AbsEtOperate::etOpShapeInsert:
			{
				OpShapeInsert* pDat = static_cast<OpShapeInsert*>(pOp);
				if (!bReverser)
					spOpDats->m_v.push_back(new OpShapeAtomInsert(&m_opEnv, pDat->data(), false));
				else
					spOpDats->m_v.push_back(new OpShapeAtomDelete(&m_opEnv, pDat->data()));
			}
			break;
		case AbsEtOperate::etOpShapeDelete:
			{
				OpShapeDelete* pDat = static_cast<OpShapeDelete*>(pOp);
				if (!bReverser)
				{
					spOpDats->m_v.push_back(new OpShapeAtomDelete(&m_opEnv, pDat->data()));
				}
				else
				{
					spOpDats->m_v.push_back(new OpShapeAtomInsert(&m_opEnv, pDat->data(), true));
				}
			}
			break;
		case AbsEtOperate::etOpShapeMove:
			{
				OpShapeDelete* pDat = static_cast<OpShapeDelete*>(pOp);
				spOpDats->m_v.push_back(new OpShapeAtomMove(&m_opEnv, pDat->data(), bReverser));
			}
			break;
		case AbsEtOperate::etOpSetPivotFieldName:
			{
				OpSetPivotFieldName* pDat = static_cast<OpSetPivotFieldName*>(pOp);
				spOpDats->m_v.push_back(new OpPivotTableRangeFieldName(&m_opEnv, pDat->object()->objId(), pDat->getFieldName(), pDat->getNewFieldName()));
			}
			break;
		case AbsEtOperate::etOpDeleteSeries:
			{
				OpChartShapeDelete* pDat = static_cast<OpChartShapeDelete*>(pOp);
				if (!bReverser)
					spOpDats->m_v.push_back(new OpChartShapeAtomDelete(&m_opEnv, pDat->data(), bReverser));
				else
					spOpDats->m_v.push_back(new OpChartShapeAtomAdd(&m_opEnv, pDat->data(), bReverser));
			}
			break;
		case AbsEtOperate::etOpMoveSeries:
			{
				OpChartShapeMove* pDat = static_cast<OpChartShapeMove*>(pOp);
				spOpDats->m_v.push_back(new OpChartShapeAtomMove(&m_opEnv, pDat->data(), bReverser));
			}
			break;
		case AbsEtOperate::etOpAddSeries:
			{
				OpChartShapeAdd* pDat = static_cast<OpChartShapeAdd*>(pOp);
				if (!bReverser)
					spOpDats->m_v.push_back(new OpChartShapeAtomAdd(&m_opEnv, pDat->data(), bReverser));
				else 
					spOpDats->m_v.push_back(new OpChartShapeAtomDelete(&m_opEnv, pDat->data(), bReverser));
			}
			break;
		}
	}

	if (bReverser)
		std::reverse(spOpDats->m_v.begin(), spOpDats->m_v.end());

	auto res = m_mainOpsMap.insert(std::make_pair(mainObj, spOpDats.detach()));
	ASSERT(res.second);
	return res.first->second;
}

void KEtOpTrsfmt::clear()
{
	for (auto it = m_mainOpsMap.begin(); it != m_mainOpsMap.end(); ++it)
	{
		delete it->second;
	}
	m_mainOpsMap.clear();
}

void KEtOpTrsfmt::ColectAllMainOps(std::vector<exec_token_vector> & tokVecs,  SheetOps& res, bool bReverser)
{
	using namespace etexec;
	for (auto& tokVec : tokVecs)
	{
		if (!tokVec)
			continue;	
		
		int size = tokVec.size();
		if (size <= 0)
			continue;
		for (int i = 0, n = tokVec.size(); i < n; ++i)
			ColectMainOps(tokVec.get(i), res, bReverser);
	}
}

void KEtOpTrsfmt::ColectMainOps(const_token_ptr pTk,  SheetOps& res, bool bReverser)
{
	using namespace etexec;
	if (const_stref_token_assist::is_type(pTk))
	{
		const_stref_token_assist ass(pTk);
		if(!ass.is_valid_sheet())
			return;
		IDX iSht = ass.get_sheet_id();
		AbsObject* pObj = m_opEnv.m_ctx->getSheetMain(iSht);
		VecOp* vecop = GetMainOps(pObj, bReverser);
		if (vecop == nullptr)
			return;
		res.insert(std::make_pair(iSht, vecop));
	}
	else if (const_vector_token_assist::is_type(pTk))
	{
		const_vector_token_assist rgv(pTk);
		for (int ii = 0, nn = rgv.get_count(); ii < nn; ++ii)
		{
			ColectMainOps(rgv.get_item(ii), res, bReverser);
		}
	}
}

bool KEtOpTrsfmt::TransFormular(OpBase* opCl, bool bReverser)
{
	if (opCl->tp() == ot_trans_formulas || opCl->tp() == ot_chart_trans_series_source)
	{
		return TransFormulars(opCl, bReverser);
	}

	OpFormulaBase* pOpFmla = nullptr;
	if (opCl->tp() == ot_range_SetFormula)
		pOpFmla = static_cast<OpFormulaBase*>(opCl);
	if (opCl->tp() == ot_chart_trans_source)
		pOpFmla = (static_cast<OpSetDataSource*>(opCl))->opFormulaBase();

	if (pOpFmla == nullptr || pOpFmla->m_tokVec == nullptr) return false;

	int size = pOpFmla->m_tokVec.size();
	if (size <= 0)
		return false;

	SheetOps vecShtOps;
	using namespace etexec;
	for (int i = 0, n =  pOpFmla->m_tokVec.size(); i < n; ++i)
		ColectMainOps(pOpFmla->m_tokVec.get(i), vecShtOps, bReverser);
	
	bool bChange = false;
	for (auto it = vecShtOps.begin(); it != vecShtOps.end(); ++it)
	{
		if (it->second == nullptr) continue;
		for (size_t i = 0; i < it->second->m_v.size(); ++i)
		{
			OpBase* op = it->second->m_v[i];
			if (pOpFmla->tfFormularBySv(it->first, op))
				bChange = true;
		}
	}
	return bChange;
}

bool KEtOpTrsfmt::TransFormulars(OpBase* opCl, bool bReverser)
{
	OpMultiFormulasBase* pOpFmla = nullptr;
	if (opCl->tp() == ot_chart_trans_series_source)
		pOpFmla = (static_cast<OpSetSeriesDataSource*>(opCl))->opFormulaBase();
	else
		pOpFmla = static_cast<OpMultiFormulasBase*>(opCl);

	bool bChange = false;
	SheetOps vecShtOps;
	ColectAllMainOps(pOpFmla->m_tokVecs, vecShtOps, bReverser);
	for (auto it = vecShtOps.begin(); it != vecShtOps.end(); ++it)
	{
		if (it->second == nullptr) continue;
		for (size_t i = 0; i < it->second->m_v.size(); ++i)
		{
			OpBase* op = it->second->m_v[i];
			if (pOpFmla->tfFormularsBySv(it->first, op))
			{
				bChange = true;
			}
		}
	}

	return bChange;
}

bool KEtOpTrsfmt::adjustIdx(VecOp& vecOpCl)
{
	bool bChange = false;

	bool bNeedAdjSheetIdx = !m_opEnv.m_ctx->isInRevisionMode() && 
							!m_opEnv.m_bk->GetBMP()->bKsheet;
	if (bNeedAdjSheetIdx && adjustSheetIdx(vecOpCl))
		bChange = true;
	if (updateShapeIdxPath(vecOpCl))
		bChange = true;
	
	return bChange;
}

// 调整sheetIdx 使得每个命令在执行时sheetIdx是对应该的
bool KEtOpTrsfmt::adjustSheetIdx(VecOp& vecOpCl)
{
	bool bChange = false;
	for (size_t i = 0; i < vecOpCl.m_v.size(); ++i)
	{
		OpBase* opCl = vecOpCl.m_v[i];
		if (!opCl)
			continue;

		if (opCl->updateSheetIdx())
			bChange = true;
	}
	return bChange;
}

bool KEtOpTrsfmt::updateShapeIdxPath(VecOp& vecOpCl)
{
	bool bChange = false;
	for (size_t i = 0; i < vecOpCl.m_v.size(); ++i)
	{
		OpBase* opCl = vecOpCl.m_v[i];
		if (!opCl)
			continue;

		if (opCl->updateShapeIdxPath())
			bChange = true;
	}
	return bChange;
}

}
