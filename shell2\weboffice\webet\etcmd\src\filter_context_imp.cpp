﻿#include "filter_context_imp.h"
#include "wo/core_stake.h"

namespace wo
{

WoFilterContext::WoFilterContext(IBook *pBook, IEtRevisionContext *pRevisionContext)
    : m_pBookSetting(pBook->GetWoStake()->getSetting())
    , m_pRevisionContext(pRevisionContext)
    , m_filterContextReadOnly(false)
{}

void WoFilterContext::SetType(WoFilterType type)
{
    m_pBookSetting->setWoFilterType(type);
}

WoFilterType WoFilterContext::GetType()
{
    return m_pBookSetting->getWoFilterType();
}

// 判断只读的方式有问题，导致获取ID时可能会拿不到准确的数据。 
// 用户登录多个端访问只读文档，其中一个只读筛选关闭后，其它端在获取序列化时，因为内核中不存在该只读筛选导致 m_filterContextReadOnly变成false，获取ID发生变化。，拿到的数据不准确
PCWSTR WoFilterContext::GetID()
{
    if (IsShared() && !m_filterContextReadOnly)
        return nullptr;
    
    return GetRawID();
}

// 作为GetID的补救措施。  这里有锅：内核不应该通过查询是否存在只读筛选来判断用户是否为”只读“
PCWSTR WoFilterContext::GetRawID()
{
    IKUserConn*	pUser = m_pRevisionContext->getUser();
    if (pUser == nullptr)
        return nullptr;

    switch (GetType())
    {
        case wft_conn:
            return pUser->connID();
        case wft_user:
            return pUser->userID();
        default:
            return nullptr;
    }
}

bool WoFilterContext::IsShared()
{
    return m_pBookSetting->getIsFilterShared();
}

bool WoFilterContext::GetIsFilterReadOnly() { return m_filterContextReadOnly; }
void WoFilterContext::SetIsFilterReadOnly(bool readOnly)
{
  m_filterContextReadOnly = readOnly;
}

} // namespace wo
