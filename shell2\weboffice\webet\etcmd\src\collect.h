﻿#ifndef _H_WEBOFFICE_WEBET_COLLECT_H_
#define _H_WEBOFFICE_WEBET_COLLECT_H_
#include "binvariant/binwriter.h"

namespace wo {
    class KEtWorkbook;
    class KEtRevisionContext;

    class WoCollectInfo 
    {
    public:
        WoCollectInfo();
        WoCollectInfo(binary_wo::BinWriter &wr, bool ignoreCollect);
        WoCollectInfo & addString(const char * key, const ks_wstring & value, bool skipEmpty = true);
        WoCollectInfo & addString(const char * key, const QString & value, bool skipEmpty = true);
        WoCollectInfo & addString(const char * key, const WCHAR * value, bool skipEmpty = true);
        WoCollectInfo & addInt32(const char * key, int value);
        WoCollectInfo & addBool(const char * key, bool value);
        WoCollectInfo & addFloat64(const char * key, float64 d);
        WoCollectInfo & addUInt32(const char * key, unsigned int value);
        WoCollectInfo & addInt64(const char * key, int64_t value);
        WoCollectInfo & addComponentInfo(IBook *pBook);
        WoCollectInfo & addComponentInfo(BMP_PTR bmp);
        WoCollectInfo & addAppVersion();
        void collect(const char * actionName);

        WoCollectInfo(const WoCollectInfo& ) = delete;
        WoCollectInfo & operator = (const WoCollectInfo &) = delete;

    protected:
        bool m_ignoreCollect;
        binary_wo::BinWriter m_wr;
        binary_wo::BinWriter& m_writer;
    };

    class WoFileIncludeCollector
    {
    public:
        explicit WoFileIncludeCollector(KEtWorkbook * wb, const WCHAR * name, int count);
        WoFileIncludeCollector & addCmdTraceId(const WCHAR * v);
        WoFileIncludeCollector & addCmdTraceId(const QString & v);
        WoFileIncludeCollector & addTraceId(const ks_wstring & v);
        WoFileIncludeCollector & addCount2(int cnt2);
        WoFileIncludeCollector & addSampleRate(int v);
        WoFileIncludeCollector & addThreadCount(int v);
        WoFileIncludeCollector & addName1(const WCHAR * wsz);
        WoFileIncludeCollector & addName1(const QString & v);
        WoFileIncludeCollector & addName1(const ks_wstring & v);
        WoFileIncludeCollector & addCmdName(const WCHAR * wsz);
        WoFileIncludeCollector & addCmdName(const ks_wstring & v);
        WoFileIncludeCollector & addFileSize1(int v);
        WoFileIncludeCollector & addFileSize2(int v);
        WoFileIncludeCollector & addUserConnCnt(size_t count);
        WoFileIncludeCollector & addConnSvrFrom(const ks_wstring & v);
        WoFileIncludeCollector & addConnSvrScene(const ks_wstring & v);
        WoFileIncludeCollector & addConnSvrLife(const ks_wstring & v);
        void collect();

    protected:
         
    private:
        WoCollectInfo m_collect;
        const WCHAR * m_szName;
        int m_count;
        bool m_isValid;
    };

    class WoOpenSaveCollector
    {
    public:
        explicit WoOpenSaveCollector(const WCHAR * name, int time);
        explicit WoOpenSaveCollector(KEtWorkbook * wb, const WCHAR * name, int time);
        ~WoOpenSaveCollector();
        WoOpenSaveCollector & addFileid(const char* fileid);
        WoOpenSaveCollector & addFileTypeByFileName(const std::string& fileName);
        WoOpenSaveCollector & addResult(HRESULT hr);
        WoOpenSaveCollector & addUserId(const char *userID);
        WoOpenSaveCollector & addHasPassword(bool isHasPassword);
        WoOpenSaveCollector & addHasModifyPassword(bool isHasModifyPassword);
    private:
        WoCollectInfo m_collect;
        bool m_isValid = true;
    };

    class WoCoreMetricsCollector
    {
    public:
        explicit WoCoreMetricsCollector(KEtWorkbook * wb, const WCHAR * name, unsigned int ms);
        ~WoCoreMetricsCollector();
        
        WoCoreMetricsCollector & addString(const char * key, const ks_wstring & value, bool skipEmpty = true)
            { m_collect.addString(key, value, skipEmpty); return *this;}
        WoCoreMetricsCollector & addInt32(const char * key, int value)
            { m_collect.addInt32(key, value); return *this;}
        WoCoreMetricsCollector & addUInt32(const char * key, unsigned int value) 
            { m_collect.addUInt32(key, value); return *this;}
        WoCoreMetricsCollector & addInt64(const char * key, int64_t value)
            { m_collect.addInt64(key, value); return *this;}
        
    private:
        WoCollectInfo m_collect;
        bool m_isValid = true;
    };
    
    
    class WoExitCollector
    {
    public:
        explicit WoExitCollector(KEtWorkbook * wb, binary_wo::BinWriter &wr, const WCHAR * name);
        ~WoExitCollector();
        
        WoExitCollector & addString(const char * key, const ks_wstring & value, bool skipEmpty = true)
            { if (m_isValid) m_collect.addString(key, value, skipEmpty); return *this;}
        WoExitCollector & addInt32(const char * key, int value)
            { if (m_isValid) m_collect.addInt32(key, value); return *this;}
        WoExitCollector & addUInt32(const char * key, unsigned int value) 
            { if (m_isValid) m_collect.addUInt32(key, value); return *this;}
        WoExitCollector & addInt64(const char * key, int64_t value)
            { if (m_isValid) m_collect.addInt64(key, value); return *this;}
    private:
        WoCollectInfo m_collect;
        binary_wo::BinWriter &m_bw;
        bool m_isValid = true;
    };
}


#endif
