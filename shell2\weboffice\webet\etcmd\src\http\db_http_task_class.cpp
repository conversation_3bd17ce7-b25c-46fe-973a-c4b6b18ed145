﻿#include "etstdafx.h"
#include "http_macro.h"
#include "db_http_task_class.h"
#include "workbook.h"
#include "et_revision_context_impl.h"
#include "webbase/serialize_impl.h"
#include "webbase/wo_sa_helper.h"
#include "db_value_serialiser.h"
#include "http_error.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"
#include "helpers/varobject_helper.h"
#include "helpers/webmime_helper.h"
#include "kso/l10n/et/et_app.h"
#include "helpers/app/create_app_view_helper.h"
#include "ettools/ettools_encode_decoder.h"

#include "book_format_converter/csv_adapter.h"
#include "book_format_converter/db_sync_adapter.h"
#include "book_format_converter/form2db_converter.h"
#include "appcore/et_appcore_webhook.h"
#include <public_header/drawing/model/abstract_shape.h>
#include "helpers/protection_helper.h"
#include "utils/et_gridsheet_utils.h"
#include "helpers/statsheet_helper.h"
#include "util.h"
#include "dbsheet/et_dbsheet_copysheet_helper.h"
#include "dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h"
#include "dbsheet/dashboard/db_plugin_wrapper.h"
#include "dashboard/et_dashboard_utils.h"
#include "webhook/webhook_helper.h"
#include "common_log/common_log_helper.h"

namespace wo
{
	static WebName  permissionName = "permissionName";
	static WebName  permissionContent = "permissionContent";
	static WebName  corePermissionId = "corePermissionId";

HRESULT DbHttpTaskClassBase::PreExecute(KwCommand *pCmd, KEtRevisionContext *pCtx)
{
	binary_wo::VarObj param = pCmd->cast().get("param");
	PCWSTR traceId = __X("");
	if (param.has("traceId"))
	{
		traceId = param.field_str("traceId");
		param.remove_field("traceId");
		pCtx->setIsRealTransform(true);
	}
	ks_stdptr<IWebhookManager> spWebhookMgr;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
	pRange->SetTraceId(traceId);
	ks_stdptr<IDbCollector> spDbCollector;
	pBook->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
	if (spDbCollector)
		spDbCollector->SetTraceId(traceId);
	return ETDbSheetTaskClassBase::PreExecute(pCmd, pCtx);
}

HRESULT DbHttpTaskClassBase::PostExecute(HRESULT hr, KwCommand *pCmd, KEtRevisionContext *pCtx)
{
	//注意：这里postExecute可能会把writer给清空，注意信息丢失
	pCtx->postExecute(hr);
	if (FAILED(hr))
	{
		binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
		KSerialWrapBinWriter acpt(*pResponse, pCtx);
		AddHttpError(hr, &acpt, m_errMsg.empty() ? nullptr : m_errMsg.c_str(), std::move(m_spErrInfoCollector));
		m_errMsg.clear();

	}
	return ETDbSheetTaskClassBase::PostExecute(hr, pCmd, pCtx);
}

HRESULT DbHttpTaskClassBase::SetSheetName(_Worksheet* pWorkSheet, PCWSTR name)
{
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    IDX sheetIdx = m_commonHelper.GetIdxById(pWorkSheet->GetSheet()->GetStId());
    if (isCellImgListSheet(sheetIdx))
        return E_INVALID_REQUEST;

    if (!etexec::IsValidSheetName(name))
        return E_INVALID_REQUEST;

    IDX idx = INVALIDIDX;
    if (SUCCEEDED(pBook->GetSheetIdxByName(name, &idx)) && idx != INVALIDIDX && idx != sheetIdx)
        return E_INVALID_REQUEST;

    if (xstricmp(name, TX_History_Reserved_SheetName) == 0)
        return E_INVALID_REQUEST;

    ks_bstr bsName(name);
    HRESULT hr = pWorkSheet->put_Name(bsName);
    if (FAILED(hr))
        return hr;
    return S_OK;
}

BOOL DbHttpTaskClassBase::IsAppSheetBook()
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	BMP_PTR pBmp = pBook->GetBMP();
	if (pBmp->bKsheet)
	{
		wo::KSheet_Book_Version bookVersion = wo::KSheet_Book_Version_Base;
		ks_stdptr<IKEtFileVersion> spEtFileVersion;
		pBook->GetExtDataItem(edFileVersion, (IUnknown**)&spEtFileVersion);
		if (spEtFileVersion)
			bookVersion = spEtFileVersion->GetKSheetBookVersion();
		// 应用改版后的ksheet组件没有view
		if (bookVersion >= KSheet_Book_Version_AppSheet)
			return TRUE;
	}
	return FALSE;
}

HRESULT DbHttpTaskClassBase::GetViewId(binary_wo::VarObj& obj, EtDbId& viewId)
{
	if (IsAppSheetBook())
	{
		viewId = EtDbId_DefaultView;
		return S_OK;
	}
	VAR_OBJ_EXPECT_STRING(obj, "id")
	HRESULT hr = m_pDbCtx->DecodeEtDbId(obj.field_str("id"), &viewId);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT DbHttpTaskClassBase::GetAppSheetDBView(binary_wo::VarObj& obj, IDBSheetView** pDbSheetView)
{
	VAR_OBJ_EXPECT_STRING(obj, "shareId")

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	ISharedLink* shareLink = spSharedLinkMgr->GetItem(obj.field_str("shareId"));
	ks_stdptr<IDBSharedLinkView> spSharedView = shareLink;
	if (!spSharedView)
		return E_FAIL;

	if (pDbSheetView)
	{
		*pDbSheetView = spSharedView->GetView();
		(*pDbSheetView)->AddRef();
	}
	return S_OK;
}

// ================== DbHttpCreateRecordsTaskClass ==================
namespace
{
HRESULT VerifyInput(binary_wo::VarObj param, const std::vector<WebName>& keys, 
	IDBSheetOp* pData, IDBSheetView* pView, IDBSheetCtx* pDbCtx)
{
	ks_stdptr<IDBSheetView_Form> spFormView = pView;

	const IDBIds* pFields = pView->GetVisibleFields();
	std::unordered_map<EtDbId, bool> requiredFld;
	for (EtDbIdx i = 0, cnt = pFields->Count(); i < cnt; ++i)
	{
		EtDbId fldId = pFields->IdAt(i);
		const IDBFormFieldOptions* pOpt = spFormView->GetFieldOption(fldId);
		if (pOpt && pOpt->GetIsRequired())
			requiredFld.insert(std::make_pair(fldId, false));
	}
	if (requiredFld.empty())
		return S_OK;

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId")
		bPreferId = param.field_bool("preferId");
	}
	for (size_t i = 0, c = keys.size(); i < c; ++i)
	{
		WebName fieldIdStr = keys[i];
		EtDbId fldId = INV_EtDbId;
		if (bPreferId)
		{
			HRESULT hr = pDbCtx->DecodeEtDbId(krt::utf16(krt::fromUtf8(fieldIdStr)), &fldId);
			if (FAILED(hr))
				return hr;
		}
		else
		{
			ks_stdptr<IDbField> spField;
			IDbFieldsManager *pFieldsMgr = pData->GetFieldsManager();
			pFieldsMgr->FindField(krt::utf16(krt::fromUtf8(fieldIdStr)), &spField);
			fldId = spField->GetID();
		}

		auto it = requiredFld.find(fldId);
		if (it != requiredFld.end())
			it->second = true;
	}

	for (auto it = requiredFld.begin(); it != requiredFld.end(); ++it)
	{
		if (!it->second)
			return E_DBSHEET_FORM_REQUIRED_FIELD;
		it->second = false;
	}
	return S_OK;
}
} // (anonymous)

HRESULT DbHttpCreateRecordsTaskBaseClass::createRecordsExec(
	KwCommand* cmd, KEtRevisionContext* ctx, bool devSubmitFormRec)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	VAR_OBJ_EXPECT_ARRAY(param, "records")
	binary_wo::VarObj records = param.get_s("records");
	const int cnt = records.arrayLength_s();
	if (not devSubmitFormRec && cnt == 0)
		return S_OK;
	if (devSubmitFormRec && 1 != cnt)
		return E_INVALID_REQUEST; // 暂时只允许表单仅提交一条记录

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spView;
	std::unique_ptr<DbSheet::DisableDbProtectScope> upDbPtrDisabler;
	if (devSubmitFormRec)
	{
		VAR_OBJ_EXPECT_STRING(param, "viewId");
		EtDbId viewId = INV_EtDbId;
		hr = m_pDbCtx->DecodeEtDbId(param.field_str("viewId"), &viewId);
		if (FAILED(hr))
			return hr;
		hr = m_commonHelper.GetDBSheetView(sheetStId, viewId, &spView, nullptr);
		if (FAILED(hr))
			return hr;

		VAR_OBJ_EXPECT_NUMERIC(param, "formMajorVer");
		ks_stdptr<IDBSheetView_Form> spFormView = spView;
		if (nullptr == spFormView)
			return E_INVALID_REQUEST;
		if (spFormView->GetMajorVer() != param.field_uint32("formMajorVer"))
			return E_DBSHEET_FORM_VER_CONFLICT;

		// 开发者接口. 屏蔽全部权限检查
		upDbPtrDisabler.reset(new DbSheet::DisableDbProtectScope(m_spProtectionJudgement));
	}

	// 支持添加记录时添加选项
	if (param.has("bAddSelectItem") && param.field_bool("bAddSelectItem"))
	{
		AddSelectItems(param, spDbSheetOp);
	}


	DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TrySetValueAndPassState);
	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, param, &m_errMsg);
	dbSerialiser.AssignDbLinkParam(&dbLinkParam);
	bool bEnableValCacheOpt = (cnt >= 2 );
	dbSerialiser.SetEnableValCacheOpt(bEnableValCacheOpt);

	std::vector<EtDbId> recIds;
	recIds.reserve(cnt);
	{
		IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
		app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

		ks_stdptr<IWebhookManager> spWebhookMgr;
		pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
		IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
		KDisableDbWebhook disableDbWebhook(pRange, DbWebhookMask_changeCellData);
		ks_stdptr<IDbCollector> spDbCollector;
		pBook->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
		KDisableDbCollector disableDbCollector(spDbCollector, DCM_Webhook, CDOM_UpdateCells);

		ks_stdptr<IDBSheetRange> spRange;
		hr = spDbSheetOp->InsertRecords(cnt, &spRange);
		if (FAILED(hr))
			return hr;
		// 修改新记录可能会因为字段权限原因导致失败，这里放开新记录的权限
		const EtDbId* pIds = nullptr;
		UINT32 insertCnt = 0;
		spRange->GetRecordIds(&pIds, &insertCnt);
		ASSERT(insertCnt == cnt);
		DbSheet::DisableDbRecordProtectScope disableDbRecordProtectScope(m_spProtectionJudgement, pIds, cnt);
		std::unique_ptr<KDbSetCellValInvalidInfoCollector> spCurCollector(new KDbSetCellValInvalidInfoCollector(sheetStId));
		HRESULT firstErrorHr = S_OK;
		DbSheet::DbtBatchSubmitOptimizer batchSubmitter(spDbSheetOp.get());
		for (EtDbIdx i = 0 ; i < cnt; i++)
		{
			EtDbId recordId = spRange->GetRecordId(i);
			binary_wo::VarObj item = records.at_s(i);
			recIds.push_back(recordId);
			if (item.has("fields"))
			{
				VAR_OBJ_EXPECT_STRUCT(item, "fields")
				binary_wo::VarObj fields = item.get_s("fields");
				std::vector<WebName> keys = fields.keys();
				if (devSubmitFormRec)
				{
					hr = VerifyInput(param, keys, spDbSheetOp.get(), spView.get(), m_pDbCtx);
					if (FAILED(hr))
						return hr;
				}
				for (auto keyIt = keys.begin(); keyIt != keys.end(); ++keyIt)
				{
					WebName fieldName = *keyIt;
					HRESULT hr = dbSerialiser.Set(recordId, fieldName, fields);
					if (FAILED(hr))
					{
						//产品要求：失败了不立马返回，而是先收集错误。
						//保持和原本接口返回值一致，收集第一个失败的返回值。
						if(SUCCEEDED(firstErrorHr))
						{
							firstErrorHr = hr;
							m_errMsg = krt::utf16(
								QStringLiteral("Failed due to field: %1").arg(krt::fromUtf8(fieldName)));
						}
						spCurCollector->InsertRecordCellItem(recordId, krt::utf16(krt::fromUtf8(fieldName)), dbSerialiser.IsPreferId(), __X(""), hr);
					}
				}
			}
		}
		//产品要求：遍历完成所有的字段值，最后返回第一个失败的返回值，保持和原本接口返回值一致
		if(FAILED(firstErrorHr))
		{
			m_spErrInfoCollector = std::move(spCurCollector);
			return firstErrorHr;
		}

		dbSerialiser.ClearDbLinkParam();
	}
	if (FAILED(dbLinkParam.hr))
		return dbLinkParam.hr;

	wo::sa::Leave arrayRecords(wo::sa::enterArray(&acpt, "records")); // acpt.addKey("records"); acpt.beginArray();
	for (size_t i = 0, c = recIds.size(); i < c; ++i)
	{
		acpt.beginStruct();
		VS(dbSerialiser.SerialiseRecord(recIds[i]));
		acpt.endStruct();
	}

	return S_OK;
}


// ================== DbHttpCreateRecordsTaskClass ==================
DbHttpCreateRecordsTaskClass::DbHttpCreateRecordsTaskClass(KEtWorkbook* wwb)
	: DbHttpCreateRecordsTaskBaseClass(wwb)
{
}

HRESULT DbHttpCreateRecordsTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return createRecordsExec(cmd, ctx, false);
}

PCWSTR DbHttpCreateRecordsTaskClass::GetTag()
{
	return __X("http.db.createRecords");
}

// ================== DbHttpUpdateRecordsTaskClass ==================
DbHttpUpdateRecordsTaskClass::DbHttpUpdateRecordsTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpUpdateRecordsTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	VAR_OBJ_EXPECT_ARRAY(param, "records")
	binary_wo::VarObj records = param.get_s("records");
	int cnt = records.arrayLength_s();
	if (cnt == 0)
		return S_OK;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;
	const IDBIds *pRecords = spDbSheetOp->GetAllRecords();

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	if(IsStatSheet(pBook, sheetStId))
	{
		//统计表不允许编辑
		WOLOG_ERROR << "[DbHttpUpdateRecordsTaskClass] For statSheet, it is not allow to UpdateRecords!";
		return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
	}

	// 支持添加记录时添加选项
	if (param.has("bAddSelectItem") && param.field_bool("bAddSelectItem"))
	{
		AddSelectItems(param, spDbSheetOp);
	}

	DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TrySetValueAndPassState);
	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, param, &m_errMsg);
	dbSerialiser.AssignDbLinkParam(&dbLinkParam);
	bool bEnableValCacheOpt = (cnt >= 2);
	dbSerialiser.SetEnableValCacheOpt(bEnableValCacheOpt);

	std::vector<EtDbId> recIds;
	recIds.reserve(cnt);
	{
		app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
		for (EtDbIdx i = 0 ; i < cnt; i++)
		{
			binary_wo::VarObj item = records.at_s(i);
			VAR_OBJ_EXPECT_STRING(item, "id")
			EtDbId recordId = m_commonHelper.GetEtDbId_NoExcept(item, "id");
			if (pRecords->Id2Idx(recordId) == INV_EtDbIdx)
				return E_DBSHEET_RECORD_NOT_FOUND;

			if (item.has("fields"))
			{
				binary_wo::VarObj fields = item.get_s("fields");
				std::vector<WebName> keys = fields.keys();
				for (auto keyIt = keys.begin(); keyIt != keys.end(); ++keyIt)
				{
					WebName fieldName = *keyIt;
					HRESULT hr = dbSerialiser.ClearUniqueField(recordId, fieldName);
					if (FAILED(hr))
					{
						m_errMsg = krt::utf16(
							QStringLiteral("Failed due to field: %1").arg(krt::fromUtf8(fieldName)));
						return hr;
					}
				}
			}
		}
		for (EtDbIdx i = 0 ; i < cnt; i++)
		{
			binary_wo::VarObj item = records.at_s(i);
			VAR_OBJ_EXPECT_STRING(item, "id")
			EtDbId recordId = m_commonHelper.GetEtDbId_NoExcept(item, "id");
			if (pRecords->Id2Idx(recordId) == INV_EtDbIdx)
				return E_DBSHEET_RECORD_NOT_FOUND;
			recIds.push_back(recordId);

			if (item.has("fields"))
			{
				binary_wo::VarObj fields = item.get_s("fields");
				std::vector<WebName> keys = fields.keys();
				for (auto keyIt = keys.begin(); keyIt != keys.end(); ++keyIt)
				{
					WebName fieldName = *keyIt;
					HRESULT hr = dbSerialiser.Set(recordId, fieldName, fields);
					if (FAILED(hr))
					{
						m_errMsg = krt::utf16(
							QStringLiteral("Failed due to field: %1").arg(krt::fromUtf8(fieldName)));
						return hr;
					}
				}
			}
		}

		dbSerialiser.ClearDbLinkParam();
	}
	if (FAILED(dbLinkParam.hr))
		return dbLinkParam.hr;

	wo::sa::Leave arrayRecords(wo::sa::enterArray(&acpt, "records")); // acpt.addKey("records"); acpt.beginArray();
	for (size_t i = 0, c = recIds.size(); i < c; ++i)
	{
		acpt.beginStruct();
		VS(dbSerialiser.SerialiseRecord(recIds[i]));
		acpt.endStruct();
	}

	return S_OK;
}


PCWSTR DbHttpUpdateRecordsTaskClass::GetTag()
{
	return __X("http.db.updateRecords");
}

// ================== DbHttpSetParentRecordTaskClass ==================
DbHttpSetParentRecordTaskClass::DbHttpSetParentRecordTaskClass(KEtWorkbook* wwb)
	:DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpSetParentRecordTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");
    VAR_OBJ_EXPECT_STRING(param, "parentId")
    EtDbId parentRecId = GetEtDbId(param, "parentId");

    std::vector<EtDbId> recordIds;
    HRESULT hr = m_commonHelper.GetEtDbIds(param, "records", recordIds);
    if (FAILED(hr))
        return hr;

	//与移动记录权限保持一致
    hr = m_spProtectionJudgement->CheckViewCanMoveRecords(sheetStId, 0);
    if (FAILED(hr))
        return hr;

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	hr = m_commonHelper.GetDBSheetViews(sheetStId, &spDbSheetViews);
	if (FAILED(hr))
		return hr;
	
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

    hr = spDbSheetOp->GetRecordsManager()->SetRecordsRelation(parentRecId, recordIds.data(), recordIds.size());
    if (FAILED(hr))
        return hr;

    //触发开启了父子记录显示的视图记录排序， 显示正确的记录层级和顺序
    DbSheet::AllDbViewsEnum viewsEnum([](IDBSheetView* pView) -> HRESULT {
        if (pView->IsEnableRecordsRelation())
        {
            HRESULT hr = pView->GetConstOrderManager()->ToggleForceSort();
            if (FAILED(hr))
                return hr;
        }
        return S_OK;
    });

    hr = spDbSheetViews->EnumViews(&viewsEnum);

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	acpt.addInt32("sheetId", sheetStId);
	EtDbIdStr buf;
	dbctx->EncodeEtDbId(parentRecId, &buf);
	acpt.addString("parentId", buf);
	acpt.addKey("childIds");
	acpt.beginArray();
	if (parentRecId != INV_EtDbId)
	{
		for (UINT i = 0; i < recordIds.size(); ++i)
    	{
        	if (spDbSheetOp->GetRecordsManager()->IsParentRecord(parentRecId, recordIds[i]))
			{
				EtDbIdStr child;
				dbctx->EncodeEtDbId(recordIds[i], &child);
				acpt.addString("parentId", child);
			}
    	}
	}
	acpt.endArray();

    return hr;
}

PCWSTR DbHttpSetParentRecordTaskClass::GetTag()
{
	return __X("http.db.bindParentRecord");
}

// ================== DbHttpEnableParentRecordTaskClass ==================
DbHttpEnableParentRecordTaskClass::DbHttpEnableParentRecordTaskClass(KEtWorkbook* wwb)
	:DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpEnableParentRecordTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

	//可編輯權限
    HRESULT hr = m_spProtectionJudgement->CheckViewCanMoveRecords(sheetStId, 0);
    if (FAILED(hr))
        return hr;
	
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;
	
	bool isEnable = false;
	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	isEnable = spDbSheetOp->GetRecordsManager()->GetEnableRecordsRelation();
	if (isEnable) {
		acpt.addBool("enable", true);
		return S_OK;
	}

	hr = spDbSheetOp->GetRecordsManager()->SetEnableRecordsRelation(true);
	if (FAILED(hr))
        return hr;

	acpt.addBool("enable", true);
	return S_OK;
}

PCWSTR DbHttpEnableParentRecordTaskClass::GetTag()
{
	return __X("http.db.enableParentRecord");
}

// ================== DbHttpDisableParentRecordTaskClass ==================
DbHttpDisableParentRecordTaskClass::DbHttpDisableParentRecordTaskClass(KEtWorkbook* wwb)
	:DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpDisableParentRecordTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

	//可編輯權限
    HRESULT hr = m_spProtectionJudgement->CheckViewCanMoveRecords(sheetStId, 0);
    if (FAILED(hr))
        return hr;
	
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;
	
	bool isEnable = true;
	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	isEnable = spDbSheetOp->GetRecordsManager()->GetEnableRecordsRelation();
	if (!isEnable) {
		acpt.addBool("enable", false);
		return S_OK;
	}

	hr = spDbSheetOp->GetRecordsManager()->SetEnableRecordsRelation(false);
	if (FAILED(hr))
        return hr;

	acpt.addBool("enable", false);
	return S_OK;
}

PCWSTR DbHttpDisableParentRecordTaskClass::GetTag()
{
	return __X("http.db.disableParentRecord");
}

// ================== DbHttpDeleteRecordsTaskClass ==================
DbHttpDeleteRecordsTaskClass::DbHttpDeleteRecordsTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpDeleteRecordsTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	HRESULT hr = S_OK;
	bool isBatch = false;
	if (param.has("isBatch"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "isBatch");
		isBatch = param.field_bool("isBatch");
	}
	
	DeleteMode mode = DeleteMode::IncludeMode;
	if (param.has("mode"))
	{
		VAR_OBJ_EXPECT_STRING(param, "mode");
		mode = DecodeDeleteMode(param.field_str("mode"));
		if (mode == DeleteMode::Unknown)
			return E_INVALID_REQUEST;
	}

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	if (mode == DeleteMode::IncludeMode)
		hr = DeleteIncludeRecords(param, ctx, sheetStId, isBatch);
	else if (mode == DeleteMode::AllMode)
		hr = DeleteAllRecords(param, ctx, sheetStId, isBatch);
	if (FAILED(hr))
		return hr;
	return S_OK;
}

DbHttpDeleteRecordsTaskClass::DeleteMode DbHttpDeleteRecordsTaskClass::DecodeDeleteMode(WebStr modeStr)
{
    if (xstricmp(modeStr, __X("include")) == 0)
		return DeleteMode::IncludeMode;
	else if (xstricmp(modeStr, __X("all")) == 0)
		return DeleteMode::AllMode;
	else
		return DeleteMode::Unknown;
}

HRESULT DbHttpDeleteRecordsTaskClass::DeleteIncludeRecords(binary_wo::VarObj param, KEtRevisionContext* ctx, UINT sheetStId, bool isBatch)
{
	if (!param.has("records"))
		return E_INVALID_REQUEST;
	VAR_OBJ_EXPECT_ARRAY(param, "records");
	binary_wo::VarObj records = param.get_s("records");
	int cnt = records.arrayLength_s();
	if (cnt == 0)
		return S_OK;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;
	const IDBIds *pRecords = spDbSheetOp->GetAllRecords();
	const IDBIds *pFields = spDbSheetOp->GetAllFields();

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	if (isBatch)
	{
		ks_stdptr<IDBSheetRange> spRg;
		spDbSheetOp->CreateDBSheetRange(&spRg);
		for (EtDbIdx i = 0 ; i < cnt; ++i)
		{
			binary_wo::VarObj item = records.at_s(i);
			VAR_OBJ_EXPECT_STRING(item, "id")
			EtDbId recordId = m_commonHelper.GetEtDbId_NoExcept(item, "id");
			if (pRecords->Id2Idx(recordId) != INV_EtDbIdx)
				spRg->AddRecordId(recordId);
			else
				return E_DBSHEET_RECORD_NOT_FOUND;
		}
		spRg->SetFieldIds(pFields);
		hr = spDbSheetOp->RemoveRecords(spRg);
		if (FAILED(hr))
			return hr;
	}
	else
	{
		binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
		KSerialWrapBinWriter acpt(*pResponse, ctx);

		acpt.addKey("records");
		acpt.beginArray();
		for (EtDbIdx i = 0 ; i < cnt; ++i)
		{
			binary_wo::VarObj item = records.at_s(i);
			VAR_OBJ_EXPECT_STRING(item, "id")
			EtDbId recordId = m_commonHelper.GetEtDbId_NoExcept(item, "id");
			acpt.beginStruct();
			acpt.addString("id", item.field_str("id"));
			bool deleted = false;
			if (pRecords->Id2Idx(recordId) != INV_EtDbIdx)
			{
				ks_stdptr<IDBSheetRange> spRg;
				spDbSheetOp->CreateDBSheetRange(&spRg);
				spRg->AddRecordId(recordId);
				spRg->SetFieldIds(pFields);
				hr = spDbSheetOp->RemoveRecords(spRg);
				if (SUCCEEDED(hr))
					deleted = true;
			}
			else
			{
				hr = E_DBSHEET_RECORD_NOT_FOUND;
			}
			if (FAILED(hr))
				AddHttpError(hr, &acpt);
			acpt.addBool("deleted", deleted);
			acpt.endStruct();
		}
		acpt.endArray();
	}
	return S_OK;
}

HRESULT DbHttpDeleteRecordsTaskClass::DeleteAllRecords(binary_wo::VarObj param, KEtRevisionContext* ctx, UINT sheetStId, bool isBatch)
{
	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;
	const IDBIds *pRecords = spDbSheetOp->GetAllRecords();
	const IDBIds *pFields = spDbSheetOp->GetAllFields();
	int cnt = pRecords->Count();
	if (cnt == 0)
		return S_OK;

	if (isBatch)
	{
		ks_stdptr<IDBSheetRange> spRg;
		spDbSheetOp->CreateDBSheetRange(&spRg);
		spRg->SetRecordIds(pRecords);
		spRg->SetFieldIds(pFields);
		hr = spDbSheetOp->RemoveRecords(spRg);
		if (FAILED(hr))
			return hr;
	}
	else
	{
		binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
		KSerialWrapBinWriter acpt(*pResponse, ctx);

		acpt.addKey("records");
		acpt.beginArray();
		for (EtDbIdx i = cnt; i > 0; --i)
		{
			EtDbId recordId = pRecords->IdAt(i - 1);
			EtDbIdStr buf;
			m_pDbCtx->EncodeEtDbId(recordId, &buf);
			acpt.beginStruct();
			acpt.addString("id", buf);
			bool deleted = false;

			ks_stdptr<IDBSheetRange> spRg;
			spDbSheetOp->CreateDBSheetRange(&spRg);
			spRg->AddRecordId(recordId);
			spRg->SetFieldIds(pFields);
			hr = spDbSheetOp->RemoveRecords(spRg);
			if (SUCCEEDED(hr))
				deleted = true;

			if (FAILED(hr))
				AddHttpError(hr, &acpt);
			acpt.addBool("deleted", deleted);
			acpt.endStruct();
		}
		acpt.endArray();
	}
	return S_OK;
}

PCWSTR DbHttpDeleteRecordsTaskClass::GetTag()
{
	return __X("http.db.deleteRecords");
}

// ================== DbHttpCreateFieldsTaskClass ==================
DbHttpCreateFieldsTaskClass::DbHttpCreateFieldsTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpCreateFieldsTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	VAR_OBJ_EXPECT_ARRAY(param, "fields")
	binary_wo::VarObj fields = param.get_s("fields");

	int cnt = fields.arrayLength_s();
	if (cnt == 0)
		return S_OK;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
	IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId");
		bPreferId = param.field_bool("preferId");
	}

	{
		wo::sa::Leave leave(wo::sa::enterArray(&acpt, "fields"));
		for (EtDbIdx i = 0 ; i < cnt; i++)
		{
			VarObj field = fields.at_s(i);
			ks_stdptr<IDbField> spField;
			hr = CreateField(field, bPreferId, spDbSheetOp, &spField, &m_errMsg);
			if (FAILED(hr))
				return hr;

			acpt.beginStruct();
			VS(dbSerialiser.SerialiseField(spField->GetID()));
			acpt.endStruct();

			// 联系人/创建者 扩展字段信息也需要序列化
			ET_DbSheet_FieldType type = spField->GetType();
			if (type == Et_DbSheetField_CreatedBy || type == Et_DbSheetField_Contact)
			{
				std::vector<EtDbId> extendFldIds;
				DbSheet::GetDbContactExtendFields(spField, extendFldIds);
				for (EtDbId id : extendFldIds)
				{
					acpt.beginStruct();
					VS(dbSerialiser.SerialiseField(id));
					acpt.endStruct();
				}
			}
		}
	}

	return S_OK;
}

PCWSTR DbHttpCreateFieldsTaskClass::GetTag()
{
	return __X("http.db.createFields");
}

// ================== DbHttpDeleteFieldsTaskClass ==================
DbHttpDeleteFieldsTaskClass::DbHttpDeleteFieldsTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpDeleteFieldsTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	VAR_OBJ_EXPECT_ARRAY(param, "fields")
	binary_wo::VarObj fields = param.get_s("fields");

	int cnt = fields.arrayLength_s();
	if (cnt == 0)
		return S_OK;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;
	const IDBIds *pRecords = spDbSheetOp->GetAllRecords();
	const IDBIds *pFields = spDbSheetOp->GetAllFields();

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	acpt.addKey("fields");
	acpt.beginArray();
	for (EtDbIdx i = 0 ; i < cnt; i++)
	{
		binary_wo::VarObj item = fields.at_s(i);
		VAR_OBJ_EXPECT_STRING(item, "id")
		EtDbId fieldId = m_commonHelper.GetEtDbId_NoExcept(item, "id");
		acpt.beginStruct();
		acpt.addString("id", item.field_str("id"));
		bool deleted = false;
		if(IsStatSheetSettingField(pBook, sheetStId, fieldId))
		{
			//统计表不允许删除自动生成的字段
			WOLOG_ERROR << "[DbHttpDeleteFieldsTaskClass] For statSheet, it is not allow to DeleteRecords!";
			hr = E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
		}
		else if (pFields->Id2Idx(fieldId) != INV_EtDbIdx)
		{
			ks_stdptr<IDBSheetRange> spRg;
			spDbSheetOp->CreateDBSheetRange(&spRg);
			spRg->AddFieldId(fieldId);
			spRg->SetRecordIds(pRecords);
			HRESULT hr = spDbSheetOp->RemoveFields(spRg);
			if (SUCCEEDED(hr))
				deleted = true;
		}
		else
		{
			hr = E_DBSHEET_FIELD_NOT_FOUND;
		}
		if (FAILED(hr))
			AddHttpError(hr, &acpt);
		acpt.addBool("deleted", deleted);
		acpt.endStruct();
	}
	acpt.endArray();

	return S_OK;
}

PCWSTR DbHttpDeleteFieldsTaskClass::GetTag()
{
	return __X("http.db.deleteFields");
}

// ================== DbHttpUpdateFieldsTaskClass ==================
DbHttpUpdateFieldsTaskClass::DbHttpUpdateFieldsTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpUpdateFieldsTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	VAR_OBJ_EXPECT_ARRAY(param, "fields")
	binary_wo::VarObj fields = param.get_s("fields");

	int cnt = fields.arrayLength_s();
	if (cnt == 0)
		return S_OK;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
	IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId");
		bPreferId = param.field_bool("preferId");
	}

	bool bOmitFailure = false;
	if (param.has("omitFailure"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "omitFailure");
		bOmitFailure = param.field_bool("omitFailure");
	}

	{
		wo::sa::Leave leave(wo::sa::enterArray(&acpt, "fields"));
		for (EtDbIdx i = 0 ; i < cnt; i++)
		{
			VarObj field = fields.at_s(i);
			VAR_OBJ_EXPECT_STRING(field, "id")
			EtDbId fieldId = m_commonHelper.GetEtDbId_NoExcept(field, "id");
			ks_stdptr<IDbField> spField;
			pFieldsMgr->GetField(fieldId, &spField);
			if (spField == nullptr)
			{
				if (bOmitFailure)
					continue;
				return E_DBSHEET_FIELD_NOT_FOUND;
			}

			HRESULT hr = UpdateField(field, bPreferId, spDbSheetOp, spField, &m_errMsg);
			if (FAILED(hr))
			{
				if (bOmitFailure)
					continue;
				return hr;
			}

			acpt.beginStruct();
			VS(dbSerialiser.SerialiseField(fieldId));
			acpt.endStruct();
		}
	}

	return S_OK;
}

PCWSTR DbHttpUpdateFieldsTaskClass::GetTag()
{
	return __X("http.db.updateFields");
}

// ================== DbHttpCreateViewTaskClass ==================
DbHttpCreateViewTaskClass::DbHttpCreateViewTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpCreateViewTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (IsAppSheetBook())
		return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	HRESULT hr = m_commonHelper.GetDBSheetViews(sheetStId, &spDbSheetViews);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId");
		bPreferId = param.field_bool("preferId");
	}

	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = CreateView(param, bPreferId, spDbSheetOp, spDbSheetOp->GetFieldsManager(), spDbSheetViews, Et_DBSheetViewUse_ForDb, &spDbSheetView, &m_errMsg);
	if (FAILED(hr))
		return hr;
	
	//新建视图时， 可见记录默认使用底表的记录顺序， 存在父子记录时须排序更新可见记录顺序
	if (spDbSheetView->IsExistParentRecord())
	{
		spDbSheetView->GetConstOrderManager()->ToggleForceSort();
	}

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
	acpt.addKey("view");
	acpt.beginStruct();
	dbSerialiser.SerialiseView(spDbSheetView);
	acpt.endStruct();

	return S_OK;
}

HRESULT DbHttpCreateViewTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");
	return m_spProtectionJudgement->CheckViewCanAdd(sheetStId);
}

PCWSTR DbHttpCreateViewTaskClass::GetTag()
{
	return __X("http.db.createView");
}

// ================== DbHttpUpdateViewTaskClass ==================
DbHttpUpdateViewTaskClass::DbHttpUpdateViewTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpUpdateViewTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	HRESULT hr =  S_OK;
	binary_wo::VarObj param = cmd->cast().get("param");
	ks_stdptr<IDBSheetView> spDbSheetView;
	if (IsAppSheetBook())
	{
		hr = GetAppSheetDBView(param, &spDbSheetView);
		if (FAILED(hr))
			return E_INVALIDARG;
	}
	else
	{
		VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
		UINT sheetStId = param.field_uint32("sheetId");
		ks_stdptr<IDBSheetViews> spDbSheetViews;
		hr = m_commonHelper.GetDBSheetViews(sheetStId, &spDbSheetViews);
		if (FAILED(hr))
			return hr;
		EtDbId viewId = INV_EtDbId;
		hr = GetViewId(param, viewId);
		if (FAILED(hr))
			return hr;
		hr = spDbSheetViews->GetItemById(viewId, &spDbSheetView);
		if (FAILED(hr))
			return hr;
	}

	hr = m_spProtectionJudgement->CheckViewCanEditNameAndDes(spDbSheetView->GetSheetOp()->GetSheetId());
	if (FAILED(hr))
		return hr;

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId");
		bPreferId = param.field_bool("preferId");
	}

	hr = UpdateView(param, bPreferId, spDbSheetView);
	if (FAILED(hr))
		return hr;

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(spDbSheetView->GetSheetOp()->GetSheetId(), &spWorksheet);
	if (FAILED(hr))
		return hr;

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
	acpt.addKey("view");
	acpt.beginStruct();
	dbSerialiser.SerialiseView(spDbSheetView);
	acpt.endStruct();

	return S_OK;
}

HRESULT DbHttpUpdateViewTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return  S_OK;
}

PCWSTR DbHttpUpdateViewTaskClass::GetTag()
{
	return __X("http.db.updateView");
}

// ================== DbHttpDeleteViewTaskClass ==================
DbHttpDeleteViewTaskClass::DbHttpDeleteViewTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpDeleteViewTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (IsAppSheetBook())
		return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	HRESULT hr = m_commonHelper.GetDBSheetViews(sheetStId, &spDbSheetViews);
	if (FAILED(hr))
		return hr;

	VAR_OBJ_EXPECT_STRING(param, "id")
	EtDbId viewId = INV_EtDbId;
	hr = m_pDbCtx->DecodeEtDbId(param.field_str("id"), &viewId);
	if (FAILED(hr))
		return hr;

	hr = spDbSheetViews->DelItem(viewId);
	if (FAILED(hr))
		return hr;

	acpt.addKey("view");
	acpt.beginStruct();
	acpt.addString("id", param.field_str("id"));
	acpt.endStruct();

	return S_OK;
}

HRESULT DbHttpDeleteViewTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");
	VAR_OBJ_EXPECT_STRING(param, "id")
	EtDbId viewId = INV_EtDbId;
	HRESULT hr = m_pDbCtx->DecodeEtDbId(param.field_str("id"), &viewId);
	if (FAILED(hr))
		return hr;
	return m_spProtectionJudgement->CheckViewCanRemove(sheetStId, viewId);
}

PCWSTR DbHttpDeleteViewTaskClass::GetTag()
{
	return __X("http.db.deleteView");
}

// ================== DbHttpAddViewSettingTaskClass ==================
DbHttpAddViewSettingTaskClass::DbHttpAddViewSettingTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpAddViewSettingTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	HRESULT hr = m_commonHelper.GetDBSheetViews(sheetStId, &spDbSheetViews);
	if (FAILED(hr))
		return hr;

	EtDbId viewId = INV_EtDbId;
	hr = GetViewId(param, viewId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = spDbSheetViews->GetItemById(viewId, &spDbSheetView);
	if (FAILED(hr))
		return hr;

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId");
		bPreferId = param.field_bool("preferId");
	}

	hr = UpdateViewSetting(param, bPreferId, spDbSheetView, &m_errMsg);
	if (FAILED(hr))
		return hr;

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
	acpt.addKey("view");
	acpt.beginStruct();
	dbSerialiser.SerialiseView(spDbSheetView);
	acpt.endStruct();

	return S_OK;
}

HRESULT DbHttpAddViewSettingTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR DbHttpAddViewSettingTaskClass::GetTag()
{
	return __X("http.db.addViewSetting");
}

// ================== DbHttpRemoveViewSettingTaskClass ==================
DbHttpRemoveViewSettingTaskClass::DbHttpRemoveViewSettingTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpRemoveViewSettingTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	HRESULT hr = m_commonHelper.GetDBSheetViews(sheetStId, &spDbSheetViews);
	if (FAILED(hr))
		return hr;

	EtDbId viewId = INV_EtDbId;
	hr = GetViewId(param, viewId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = spDbSheetViews->GetItemById(viewId, &spDbSheetView);
	if (FAILED(hr))
		return hr;

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId");
		bPreferId = param.field_bool("preferId");
	}

	hr = RemoveViewSetting(param, bPreferId, spDbSheetView);
	if (FAILED(hr))
		return hr;

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
	acpt.addKey("view");
	acpt.beginStruct();
	dbSerialiser.SerialiseView(spDbSheetView);
	acpt.endStruct();

	return S_OK;
}

HRESULT DbHttpRemoveViewSettingTaskClass::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR DbHttpRemoveViewSettingTaskClass::GetTag()
{
	return __X("http.db.removeViewSetting");
}

// ================== DbHttpCreateSheetTaskClass ==================
DbHttpCreateSheetTaskClass::DbHttpCreateSheetTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpCreateSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	VAR_OBJ_EXPECT_ARRAY(param, "fields")
	binary_wo::VarObj fields = param.get_s("fields");

	if (fields.arrayLength_s() == 0)
		return E_INVALID_REQUEST;

	VAR_OBJ_EXPECT_ARRAY(param, "views")
	binary_wo::VarObj views = param.get_s("views");

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	BMP_PTR pBmp = pBook->GetBMP();
	if (pBmp->bDbSheet)
	{
		// db组件至少要有一个view
		if (views.arrayLength_s() == 0)
			return E_DBSHEET_VIEWS_EMPTY;
	}
	else if (pBmp->bKsheet)
	{
		wo::KSheet_Book_Version bookVersion = wo::KSheet_Book_Version_Base;
		ks_stdptr<IKEtFileVersion> spEtFileVersion;
		pBook->GetExtDataItem(edFileVersion, (IUnknown**)&spEtFileVersion);
		if (spEtFileVersion)
			bookVersion = spEtFileVersion->GetKSheetBookVersion();
		// 应用改版后的ksheet组件没有view
		if (bookVersion >= KSheet_Book_Version_AppSheet && views.arrayLength_s() != 0)
			param.add_field_array("views", typeStruct);
		else if (bookVersion == KSheet_Book_Version_Base && views.arrayLength_s() == 0)
			return E_DBSHEET_VIEWS_EMPTY;
	}

	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	KComVariant vBefore;
	KComVariant vAfter;
	KComVariant vCount;
	KComVariant vType;
	vType.Assign(xlWorksheet);
	SHEETTYPE sheetType = stGrid_DB;

    if (param.has("beforeSheetId"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "beforeSheetId");
        UINT beforeSheetId = param.field_uint32("beforeSheetId");
        IDX beforeSheetIdx = INVALIDIDX;
        pBook->STSheetToRTSheet(beforeSheetId, &beforeSheetIdx);
        vBefore = beforeSheetIdx == INVALIDIDX ? 1 : beforeSheetIdx + 1;
    }
    else if (param.has("afterSheetId"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "afterSheetId");
        UINT afterSheetId = param.field_uint32("afterSheetId");
        IDX afterSheetIdx = INVALIDIDX;
        pBook->STSheetToRTSheet(afterSheetId, &afterSheetIdx);
        if (afterSheetIdx == INVALIDIDX)
            vAfter = getLastSheetIdx() + 1;
        else
            vAfter = afterSheetIdx + 1;
    }
    else
    {
        vAfter = getLastSheetIdx() + 1;
    }

	ks_stdptr<etoldapi::Worksheets> spWorksheets;
	m_wwb->GetCoreWorkbook()->get_Worksheets(&spWorksheets);
	ks_stdptr<IKCoreObject> spCoreObj;
	HRESULT hr = spWorksheets->Add(vBefore, vAfter, vCount, vType, &spCoreObj, sheetType);
	if (FAILED(hr))
		return hr;

	ks_stdptr<etoldapi::_Worksheet> spWorksheet = spCoreObj;
	if (!spWorksheet)
		return E_FAIL;

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId");
		bPreferId = param.field_bool("preferId");
	}

	hr = InitDbSheet(spWorksheet, bPreferId, param);
	if (FAILED(hr))
	{
		spWorksheet->DeleteDirectly();
		return hr;
	}

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
	acpt.addKey("sheet");
	acpt.beginStruct();
	VS(dbSerialiser.SerialiseSheet());
	acpt.addKey("records");
	VS(dbSerialiser.SerialiseAllRecords());
	acpt.endStruct();

	return S_OK;
}

HRESULT DbHttpCreateSheetTaskClass::InitDbSheet(etoldapi::_Worksheet *pWorksheet, bool bPreferId, binary_wo::VarObj obj)
{
	ISheet* pSheet = pWorksheet->GetSheet();
	ks_stdptr<IDBSheetOp> spDbSheetOp;
	VS(DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp));

	binary_wo::VarObj fields = obj.get_s("fields");
	binary_wo::VarObj views = obj.get_s("views");

	UINT nEmptyRows = spDbSheetOp->IsNoRecordsMode() ? 0 : 1;
	Database::SheetInitConfig config;
	HRESULT hr = DbSheet::ConfigureNewSheet(pWorksheet, m_spProtectionJudgement, nEmptyRows, fields.arrayLength_s(), config);
	if (FAILED(hr))
		return hr;

	// 注意：ConfigureNewSheet 会更新 sheetStId
	DbSheet::DisableDbSheetProtectScope disPtScope(m_spProtectionJudgement, pSheet->GetStId());

	VAR_OBJ_EXPECT_STRING(obj, "name")
	WebStr name = obj.field_str("name");

	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
	ks_bstr sheetName;
	GetValidSheetName(pWorksheets, pWorksheet, name, &sheetName);
	pWorksheet->put_Name(sheetName);

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	VS(DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews));

	for (int i = 0, cnt = views.arrayLength_s(); i < cnt; i++)
	{
		binary_wo::VarObj view = views.at_s(i);
		ks_stdptr<IDBSheetView> spDbSheetView;
		hr = CreateView(view, bPreferId, spDbSheetOp, spDbSheetOp->GetFieldsManager(), spDbSheetViews, Et_DBSheetViewUse_ForDb, &spDbSheetView, &m_errMsg);
		if (FAILED(hr))
			return hr;
	}

	if (obj.has("syncType"))
	{
		ET_DbSheet_Sync_Type syncType = DbSheet_St_None;
		_appcore_GainEncodeDecoder()->DecodeDbSheetSyncType(obj.field_str("syncType"), &syncType);
		hr = spDbSheetOp->SetSheetSyncType(syncType);
		if (FAILED(hr))
			return hr;
	}

	IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();
	const IDBIds *pFields = spDbSheetOp->GetAllFields();
	std::vector<EtDbIdx> deferredList;
	for (EtDbIdx idx = 0, cnt = fields.arrayLength_s(); idx < cnt; idx++)
	{
		binary_wo::VarObj field = fields.at_s(idx);
		ks_stdptr<IDbField> spField;
		HRESULT hr = pFieldsMgr->GetField(pFields->IdAt(idx), &spField);

		// 先过滤引用和公式类型, 只设置字段名称, 设定值的过程延后处理
		if (field.has("type"))
		{
			VAR_OBJ_EXPECT_STRING(field, "type")
			ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
			DECODE_STR_OBJ(field, "type", FieldType, type, &m_errMsg);

			switch (type)
			{
			case Et_DbSheetField_Lookup:
				// [[fallthrough]];
			case Et_DbSheetField_Formula:
				if (field.has("name"))
				{
					VAR_OBJ_EXPECT_STRING(field, "name")
					const WebStr name = field.field_str("name");
					if (xstrcmp(spField->GetName(), name) not_eq 0)
					{
						hr = spField->SetName(name, TRUE, TRUE);
						if (FAILED(hr))
							return hr;
					}
					deferredList.emplace_back(idx);
				}
				continue;
			default:
				break;
			}
		}

		if (FAILED(hr))
			return hr;

		hr = UpdateField(field, bPreferId, spDbSheetOp, spField, &m_errMsg);
		if (FAILED(hr))
			return hr;
	}

	// 处理延后的公式/引用字段
	for (const EtDbIdx deferredIndex : deferredList)
	{
		const binary_wo::VarObj field = fields.at_s(deferredIndex);
		ks_stdptr<IDbField> deferredField;
		pFieldsMgr->GetField(pFields->IdAt(deferredIndex), &deferredField);
		hr = UpdateField(field, bPreferId, spDbSheetOp, deferredField, &m_errMsg);
		if (FAILED(hr))
			return hr;
	}

	return S_OK;
}

HRESULT DbHttpCreateSheetTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR DbHttpCreateSheetTaskClass::GetTag()
{
	return __X("http.db.createSheet");
}

// ================== DbHttpUpdateSheetTaskClass ==================
DbHttpUpdateSheetTaskClass::DbHttpUpdateSheetTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpUpdateSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	HRESULT hr =  m_spProtectionJudgement->CheckSheetCanEditProp(sheetStId);
	if (FAILED(hr))
		return hr;

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	if (param.has("name"))
	{
		VAR_OBJ_EXPECT_STRING(param, "name")
		WebStr name = param.field_str("name");
		hr = SetSheetName(spWorksheet, name);
		if (FAILED(hr))
			return hr;
	}

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId");
		bPreferId = param.field_bool("preferId");
	}

	if (param.has("primaryField"))
	{
		VAR_OBJ_EXPECT_STRING(param, "primaryField")
		PCWSTR field = param.field_str("primaryField");
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
		if (FAILED(hr))
			return hr;

		hr = SetPrimaryField(field, bPreferId, spDbSheetOp, &m_errMsg);
		if (FAILED(hr))
			return hr;
	}

	if (param.has("syncType"))
	{
		ET_DbSheet_Sync_Type syncType = DbSheet_St_None;
		_appcore_GainEncodeDecoder()->DecodeDbSheetSyncType(param.field_str("syncType"), &syncType);
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		VS(DbSheet::GetDBSheetOp(spWorksheet->GetSheet(), &spDbSheetOp));
		hr = spDbSheetOp->SetSheetSyncType(syncType);
		if (FAILED(hr))
			return hr;
	}

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
	acpt.addKey("sheet");
	acpt.beginStruct();
	VS(dbSerialiser.SerialiseSheet());
	acpt.endStruct();

	return S_OK;
}

HRESULT DbHttpUpdateSheetTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return  S_OK;
}

PCWSTR DbHttpUpdateSheetTaskClass::GetTag()
{
	return __X("http.db.updateSheet");
}

// ================== DbHttpDeleteSheetTaskClass ==================
DbHttpDeleteSheetTaskClass::DbHttpDeleteSheetTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpDeleteSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<_Worksheet> spWorksheet;
	HRESULT hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	INT sheetCnt = 0;
	pBook->GetSheetCount(&sheetCnt);
	int visibleCnt = 0;
	for (int i = 0; i < sheetCnt; i++)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(i, &spSheet);
		if (spSheet == nullptr || spSheet->GetStId() == sheetStId)
			continue;

		BOOL bVisible = FALSE;
		spSheet->GetVisible(&bVisible);
		if (bVisible)
			visibleCnt++;
	}
	if (visibleCnt == 0)
		return E_INVALID_REQUEST;

	hr = spWorksheet->DeleteDirectly();
	if (FAILED(hr))
		return hr;

	acpt.addKey("sheet");
	acpt.beginStruct();
	acpt.addUint32("id", param.field_uint32("sheetId"));
	acpt.endStruct();

	return S_OK;
}

HRESULT DbHttpDeleteSheetTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanRemove();
}

PCWSTR DbHttpDeleteSheetTaskClass::GetTag()
{
	return __X("http.db.deleteSheet");
}


// ================== DbHttpEditPermissonTaskClass ==================
DbHttpEditPermissonTaskClass::DbHttpEditPermissonTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpEditPermissonTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_STRING(param, "action");

	HRESULT hr = E_INVALID_REQUEST;
	WebStr op = param.field_str("action");
	
	WOLOG_INFO << "permission action: " << op;
	if (0 == xstricmp(op, __X("add")))
	{
		VAR_OBJ_EXPECT_STRING(param, permissionName);
		VAR_OBJ_EXPECT_ARRAY(param, permissionContent);
		hr = add(param, ctx);
	}
	else if (0 == xstricmp(op, __X("remove")))
	{
		VAR_OBJ_EXPECT_STRING(param, corePermissionId);
		hr = remove(param.field_str(corePermissionId));
	}
	else if (0 == xstricmp(op, __X("modify")))
	{
		VAR_OBJ_EXPECT_STRING(param, corePermissionId);
		hr = modify(param);
	}

	ks_stdptr<IWebhookManager> spWebhookMgr;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	IDbWebhookChangedRange* pDbRange = spWebhookMgr->GetDbWebhookChangedRange();
	pDbRange->CollectUpdateRegion();

	// 收集埋点
	if(ctx)
		ctx->collectInfo("behavior.dbwebhook.updateRegion.editPermisson");

	return hr;
}

HRESULT DbHttpEditPermissonTaskClass::add(VarObj& param, KEtRevisionContext* ctx)
{
	HRESULT hr = S_OK;
	ks_stdptr<IDBProtection> spProtection;
	hr = DbSheet::GenerateDBProtection(m_wwb->GetCoreWorkbook()->GetBook(), m_spProtectionJudgement, param.get_s(permissionContent), &spProtection);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[DbHttpEditPermissonTaskClass] GenerateDBProtection Failed!";
		return hr;
	}
		
	spProtection->SetDBPermissonName(param.field_str(permissionName));

	//确保回放corePermissionId保持一致
	ks_wstring permissionId;
	if (param.has(corePermissionId))
	{
		permissionId = param.field_str(corePermissionId);
	}

	if (!permissionId.empty())
	{
		spProtection->SetDBPermissonId(permissionId.c_str());
	}
	else
	{
		param.add_field_str(corePermissionId,spProtection->GetDBPermissonId());
		ctx->setIsRealTransform(true);
	}
	
	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	acpt.addString(corePermissionId, spProtection->GetDBPermissonId());
	m_spProtectionJudgement->AddDBProtection(spProtection); 
	return S_OK;
}

HRESULT DbHttpEditPermissonTaskClass::remove(WebStr permissionId)
{
	IDBProtection* pProtection =  m_spProtectionJudgement->GetDBProtection(permissionId);
    if (!pProtection)
        return E_DBSHEET_PERMISSION_ID_NOT_EXIST;

 	m_spProtectionJudgement->RemoveDBProtection(permissionId);
	return S_OK;
}

HRESULT DbHttpEditPermissonTaskClass::modify(const VarObj& param)
{
	IDBProtection* pProtection =  m_spProtectionJudgement->GetDBProtection(param.field_str(corePermissionId));
    if (!pProtection)
        return E_DBSHEET_PERMISSION_ID_NOT_EXIST;

	ks_wstring  newName;
	if (param.has(permissionName))
		newName = param.field_str(permissionName);
	
	ks_wstring  oldName = pProtection->GetDBPermissonName();
	if (!newName.empty() && oldName != newName)
	{
		pProtection->SetDBPermissonName(newName.c_str());
	}

	
	if (param.has(permissionContent))
	{
		HRESULT hr = S_OK;
		ks_stdptr<IDBProtection> spProtection;
		hr = DbSheet::GenerateDBProtection(m_wwb->GetCoreWorkbook()->GetBook(), m_spProtectionJudgement, param.get_s(permissionContent), &spProtection);
		if (FAILED(hr))
		{
			WOLOG_INFO << "[DbHttpEditPermissonTaskClass] GenerateDBProtection Failed!";
			return hr;
		}
		spProtection->SetDBPermissonId(pProtection->GetDBPermissonId());
		spProtection->SetDBPermissonName(pProtection->GetDBPermissonName());
		m_spProtectionJudgement->UpdateDBProtection(pProtection->GetDBPermissonId(), spProtection);
	}

	return S_OK;
}


HRESULT DbHttpEditPermissonTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK; //服务端判断权限编辑的权限
}

PCWSTR DbHttpEditPermissonTaskClass::GetTag()
{
	return __X("http.db.editpermission");
}

// ================== DbHttpSetQueryFieldsTaskClass ==================
DbHttpSetQueryFieldsTaskClass::DbHttpSetQueryFieldsTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpSetQueryFieldsTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	HRESULT hr =  S_OK;
	binary_wo::VarObj param = cmd->cast().get("param");
	ks_stdptr<IDBSheetView> spDbSheetView;
	if (IsAppSheetBook())
	{
		hr = GetAppSheetDBView(param, &spDbSheetView);
		if (FAILED(hr))
			return E_INVALIDARG;
	}
	else 
	{
		VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
		IDX sheetStId = param.field_uint32("sheetId");
		EtDbId viewId = INV_EtDbId;
		GetViewId(param, viewId);
		if (FAILED(hr))
			return hr;

		GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
		if(spDbSheetView == NULL)
			return E_DBSHEET_VIEW_NOT_FOUND;
	}

	ks_stdptr<IDBSheetView_Query> spDbSheetView_Query = spDbSheetView;
	if (spDbSheetView_Query == nullptr)
		return E_INVALIDARG;

	hr = m_spProtectionJudgement->CheckCanQueryOp(spDbSheetView->GetSheetOp()->GetSheetId(), spDbSheetView->GetId());
	if(FAILED(hr))
		return hr;
	
	IDbFieldsManager* pFieldManager = spDbSheetView->GetFieldsManager();
	int phoneFieldCnt = 0;
	std::vector<EtDbId> fieldIdVec;
	std::vector<KDbFilterCriteriaOpType> tpVec;
	std::vector<PCWSTR> customPromptVec;
	std::vector<BOOL> enableScanCodeToInputsVec;
	std::vector<BOOL> conditionCanBlanksVec;
	std::vector<BOOL> needSecondChecksVec;
	BOOL bHasSetNeedSecondCheck = FALSE;//只有一个电话号码字段才能设置二次校验

	VarObj queryFields = param.get("queryFields");
	for (int i = 0; i < queryFields.arrayLength_s(); ++i)
	{
		VarObj queryFieldItem = queryFields.at_s(i);
		EtDbId fldId = GetEtDbId(queryFieldItem, "fieldId");
		if (fldId == INV_EtDbId)
			continue;

		KDbFilterCriteriaOpType op = DBFCOT_Null;
		HRESULT hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterCriteriaOpType(queryFieldItem.field_str("op"), &op);
		if(FAILED(hr))
			continue;

		//检查设置的op是否合法
		BOOL bValidOp = spDbSheetView_Query->IsValidCriteriaOpTp(fldId, op);
		if(!bValidOp)
		{
			WOLOG_ERROR << "[TaskExecDbSetQueryFields] fldId:" << fldId << "op:" << (int)op << " is unValid!";
			continue;
		}

		PCWSTR customPrompt = __X("");
		if(queryFieldItem.has("customPrompt"))
		{
			customPrompt = queryFieldItem.field_str("customPrompt");
		}

		BOOL enableScanCodeToInput = FALSE;
		if(queryFieldItem.has("enableScanCodeToInput"))
		{
			enableScanCodeToInput = queryFieldItem.field_bool("enableScanCodeToInput");
		}

		BOOL conditionCanBlank = FALSE;//默认值
		if(queryFieldItem.has("conditionCanBlank"))
		{
			//新命令一定会有这个参数。
			conditionCanBlank = queryFieldItem.field_bool("conditionCanBlank");
		}

		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		if (!spField)
			continue;
		
		BOOL needSecondCheck = FALSE;//默认值
		if(queryFieldItem.has("needSecondCheck"))
		{
			//新命令一定会有这个参数。
			needSecondCheck = queryFieldItem.field_bool("needSecondCheck");
			if(needSecondCheck)
			{
				if(spField->GetType() != Et_DbSheetField_Phone || bHasSetNeedSecondCheck)
				{
					//字段本身不是电话号码类型，或者已经有字段设置过二次校验了，后面没必要开启二次校验。
					needSecondCheck = FALSE;	
				}
				
			}
		}

		fieldIdVec.push_back(fldId);
		tpVec.push_back(op);

		customPromptVec.push_back(customPrompt);
		enableScanCodeToInputsVec.push_back(enableScanCodeToInput);
		conditionCanBlanksVec.push_back(conditionCanBlank);
		if(needSecondCheck)
		{
			bHasSetNeedSecondCheck = TRUE;
		}
		needSecondChecksVec.push_back(needSecondCheck);
	}


	if (fieldIdVec.size() == 0 || 
		!(fieldIdVec.size() == tpVec.size() 
			&& fieldIdVec.size() == customPromptVec.size()
			&& fieldIdVec.size() == enableScanCodeToInputsVec.size()
			&& fieldIdVec.size() == conditionCanBlanksVec.size()
			&& fieldIdVec.size() == needSecondChecksVec.size()
		)
	)
	{
		return E_FAIL;
	}

	hr = spDbSheetView_Query->SetQueryFields(fieldIdVec.data(), tpVec.data(), 
		customPromptVec.data(), enableScanCodeToInputsVec.data(),
		conditionCanBlanksVec.data(), needSecondChecksVec.data(),
		fieldIdVec.size());
	return hr;
}

PCWSTR DbHttpSetQueryFieldsTaskClass::GetTag()
{
	return __X("http.db.setQueryFields");
}

// ================== DbHttpDevSubmitFormRecordTaskClass ==================
DbHttpDevSubmitFormRecordTaskClass::DbHttpDevSubmitFormRecordTaskClass(KEtWorkbook* wwb)
	: DbHttpCreateRecordsTaskBaseClass(wwb)
{
	m_cmdPermissionCheckWhiteList.insert(GetTag());
}

HRESULT DbHttpDevSubmitFormRecordTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return createRecordsExec(cmd, ctx, true);
}

PCWSTR DbHttpDevSubmitFormRecordTaskClass::GetTag()
{
	return __X("http.db.devSubmitFormRecord");
}

DbHttpDevCreateAppViewTaskClass::DbHttpDevCreateAppViewTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
	m_cmdPermissionCheckWhiteList.insert(GetTag());
}

HRESULT DbHttpDevCreateAppViewTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (!m_wwb->GetCoreWorkbook()->GetBook()->GetBMP()->bKsheet)
	{
		WOLOG_ERROR << "[DbHttpDevCreateAppViewTaskClass] not ksheet, not allow to do this!";
		return E_FAIL;
	}

	UINT appSheetStId = 0;
	UINT srcSheetStId = 0;//若是从本地et创建应用，返回et_sheet stid，除此之外从本地et创建和从模板创建，均则返回0 表示无效的值
	EtDbId appViewId = INV_EtDbId;
	VarObj param = cmd->cast().get("param");

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	ks_wstring errName;
	ks_wstring appName;
	UINT airAppSheetStId = 0;
	EtDbId airAppId = INV_EtDbId;
	HRESULT hr = CreateAppViewHelper::CreateAppView(m_wwb, ctx, param, appSheetStId, srcSheetStId, appViewId, appName, airAppSheetStId, airAppId, errName);

	acpt.addKey("res");
	if (SUCCEEDED(hr))
	{
		acpt.beginStruct();
		EtDbIdStr buf;
		m_pDbCtx->EncodeEtDbId(appViewId, &buf);
		acpt.addString("appViewId", buf);
		acpt.addUint32("appSheetStId", appSheetStId);
		acpt.addUint32("srcSheetStId", srcSheetStId);
		acpt.addString("appName", appName.c_str());
		acpt.addString("errName", errName.c_str());
		if (airAppSheetStId != 0)
			acpt.addUint32("airAppSheetStId", airAppSheetStId);
		if (airAppId != INV_EtDbId)
		{
			m_pDbCtx->EncodeEtDbId(airAppId, &buf);
			acpt.addString("airAppId", buf);
		}
		acpt.endStruct();
		WOLOG_INFO << "[DbHttpDevCreateAppViewTaskClass] call CreateAppView success!";
		return WO_OK;
	}
	else
	{
		acpt.beginStruct();
		acpt.addString("errName", errName.c_str());
		acpt.endStruct();
		WOLOG_INFO << "[DbHttpDevCreateAppViewTaskClass] call CreateAppView Failed!";
		return WO_FAIL;
	}
}
	
PCWSTR DbHttpDevCreateAppViewTaskClass::GetTag()
{
	return __X("http.db.devCreateAppView");
}

// ================== DbHttpSyncSheetTaskClass ==================
DbHttpSyncSheetTaskClass::DbHttpSyncSheetTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpSyncSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "sourceSheetId")
	VAR_OBJ_EXPECT_NUMERIC(param, "targetSheetId")
	VAR_OBJ_EXPECT_STRING(param, "fieldSourceName")
	VAR_OBJ_EXPECT_STRING(param, "urlTemplate")
	VAR_OBJ_EXPECT_STRUCT(param, "syncData")

	if (param.get_s("fieldMappingList").keys().empty())
		WOLOG_INFO << "[DbHttpFullUpdateSyncSheetTaskClass] The field mapping list empty!";

	UINT srcStId = param.field_uint32("sourceSheetId");
	UINT tarStId = param.field_uint32("targetSheetId");
	HRESULT hr = m_spProtectionJudgement->CheckSheetCanEditProp(tarStId);
	if (FAILED(hr))
		return hr;
	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(tarStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	std::unordered_map<EtDbId, EtDbId> fldMap;
	if (param.has("fieldMappingList"))
	{
		VarObj fieldMappingList = param.get("fieldMappingList");
		for (WebName key : fieldMappingList.keys())
		{
			VAR_OBJ_EXPECT_STRING(fieldMappingList, key)
			EtDbId srcFldId = INV_EtDbId;
			hr = dbctx->DecodeEtDbId(krt::utf16(krt::fromUtf8(key)), &srcFldId);
			if (FAILED(hr))
				return E_INVALID_REQUEST;
			EtDbId tarFldId = m_commonHelper.GetEtDbId_NoExcept(fieldMappingList, key);
			if (tarFldId == INV_EtDbId)
				return E_INVALID_REQUEST;
			fldMap.emplace(srcFldId, tarFldId);
		}
	}

	bool bNewLookupConvert = true;
		
	_Workbook* pTarWorkbook = m_wwb->GetCoreWorkbook();
	DbSyncSheetHelper::SyncDbSheetParam syncParam;
	syncParam.urlTemplate = param.field_str("urlTemplate");
	syncParam.fldSourceName = param.field_str("fieldSourceName");
	syncParam.pFieldMap = &fldMap;
	syncParam.srcSheetId = srcStId;
	syncParam.sheetData = param.get("syncData");

	binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, pCtx);
	if (!param.has("preferId"))
		param.add_field_bool("preferId", true);	//使用id解析字段值
	if (!param.has("valuePreferId"))
		param.add_field_bool("valuePreferId", false); //不使用id解析选项值
	if (!param.has("formulaCompound"))
		param.add_field_str("textValue", __X("formulaCompound")); // formulaCompound值格式
	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, param, &m_errMsg);
	DbSyncSheetHelper updateHelper(pTarWorkbook, tarStId, m_spProtectionJudgement, bNewLookupConvert);
	updateHelper.Init(syncParam, &dbSerialiser);
	if (FAILED(hr))
		return hr;

	hr = updateHelper.Exec();
	if (FAILED(hr))
		return hr;
		// 全量同步且同步记录数大于500，命令较大
	bool needClearCmd = syncParam.sheetData.has("baseSchema") 
						&& syncParam.sheetData.has("records")
						&& syncParam.sheetData.get("records").arrayLength() > 500;
	//全量同步时清理当前同步命令，同步命令带有数据size较大，每次同步内核会缓存命令导致内存膨胀
	if (needClearCmd && _kso_GetWoEtSettings()->IsEnableClearCurVersionCmd())
	{
		WOLOG_INFO << "DbHttpSyncSheetTaskClass::ENABLE_CLEAR_CUR_VERSION_CMD";
		// 先清理事物，否则清除当前版本命令undo有问题
		_Application* pTarApp = m_wwb->GetCoreApp();
		pTarApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(pTarWorkbook);
		pCtx->SetClearCurVersionCmd();
	}
	
	const SyncSheetInfo& sheetInfo = updateHelper.getSyncSheetInfo();
	EtDbIdStr buf;
	acpt.addUint32("targetSheetId", tarStId);
	acpt.addKey("sheetInfo");
	acpt.beginStruct();
		VS(dbctx->EncodeEtDbId(sheetInfo.fldSourceId, &buf));
		acpt.addString("fieldSourceId", buf);
		acpt.addKey("fieldMappingList");
		acpt.beginStruct();
			for (auto it : sheetInfo.fldMap)
			{
				VS(dbctx->EncodeEtDbId(it.first, &buf));
				auto srcFieldId = krt::fromUtf16(buf).toStdString();
				VS(dbctx->EncodeEtDbId(it.second, &buf));
				acpt.addKey(srcFieldId.data(), true);
				acpt.addString(nullptr, buf);
			}
		acpt.endStruct();
	acpt.endStruct();

	return S_OK;	
}

HRESULT DbHttpSyncSheetTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR DbHttpSyncSheetTaskClass::GetTag()
{
	return __X("http.db.syncSheet");
}

// ================== DbHttpFullUpdateSyncSheetBaseTaskClass ==================
DbHttpFullUpdateSyncSheetBaseTaskClass::DbHttpFullUpdateSyncSheetBaseTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}


HRESULT DbHttpFullUpdateSyncSheetBaseTaskClass::FullUpdateSyncSheet(KEtRevisionContext* pCtx, etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook, VarObj& param, ISerialAcceptor& acpt, PCWSTR filePath, bool isBatch)
{
	util::CallTimeStat stat("FullUpdateSyncSheet");
	UINT srcStId = param.field_uint32("sourceSheetId");
	UINT tarStId = param.field_uint32("targetSheetId");
	HRESULT hr = m_spProtectionJudgement->CheckSheetCanEditProp(tarStId);
	if (FAILED(hr))
		return hr;

	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	std::unordered_map<EtDbId, EtDbId> fldMap;
	VarObj fieldMappingList = param.get("fieldMappingList");

	for (WebName key : fieldMappingList.keys())
	{
		VAR_OBJ_EXPECT_STRING(fieldMappingList, key)
		EtDbId srcFldId = INV_EtDbId;
		hr = dbctx->DecodeEtDbId(krt::utf16(krt::fromUtf8(key)), &srcFldId);
		if (FAILED(hr))
			return E_INVALID_REQUEST;
		EtDbId tarFldId = m_commonHelper.GetEtDbId_NoExcept(fieldMappingList, key);
		if (tarFldId == INV_EtDbId)
			return E_INVALID_REQUEST;
		fldMap.emplace(srcFldId, tarFldId);
	}

	bool bNewLookupConvert {};
	if (param.has("new_lookup_converter"))
		bNewLookupConvert = param.field_bool("new_lookup_converter");
	bool bEnableCopyAttachment = true;
	if (param.has("enableCopyAttachment"))
		bEnableCopyAttachment = param.field_bool("enableCopyAttachment");
	FullUpdateSyncDbSheetsAdapter updateHelper(pSrcWorkbook, pTarWorkbook, srcStId, tarStId, m_spProtectionJudgement,
		bNewLookupConvert);
	FullUpdateSyncDbSheetsAdapter::FullUpdateSyncDbSheetParam syncParam;
	syncParam.urlTemplate = param.field_str("urlTemplate");
	syncParam.fldSourceName = param.field_str("fieldSourceName");
	syncParam.pFieldMap = &fldMap;
	syncParam.pTimeStatInfo = &m_timeStatInfo;
	syncParam.m_pParam = &param;
	syncParam.m_pCtx = pCtx;
	syncParam.m_pcwFilePath = filePath;
	syncParam.m_bEnableCopyAttachment = bEnableCopyAttachment;
	if (param.has("defaultName"))
		syncParam.m_pcwDefaultName = param.field_str("defaultName");
	hr = updateHelper.Init(syncParam);
	if (FAILED(hr))
		return hr;
	hr = updateHelper.Exec();
	if (FAILED(hr))
		return hr;
	const SyncSheetInfo& sheetInfo = updateHelper.getSyncSheetInfo();
	EtDbIdStr buf;
	if (isBatch)
		acpt.beginStruct();
	if (updateHelper.GetLostFlag())
		acpt.addBool("lostInfo", true);
	acpt.addUint32("targetSheetId", tarStId);
	acpt.addKey("sheetInfo");
	acpt.beginStruct();
		VS(dbctx->EncodeEtDbId(sheetInfo.fldSourceId, &buf));
		acpt.addString("fieldSourceId", buf);
		acpt.addKey("fieldMappingList");
		acpt.beginStruct();
			for (auto it : sheetInfo.fldMap)
			{
				VS(dbctx->EncodeEtDbId(it.first, &buf));
				auto srcFieldId = krt::fromUtf16(buf).toStdString();
				VS(dbctx->EncodeEtDbId(it.second, &buf));
				acpt.addKey(srcFieldId.data(), true);
				acpt.addString(nullptr, buf);
			}
		acpt.endStruct();
	acpt.endStruct();
	if (isBatch)
		acpt.endStruct();
	return S_OK;
}

// ================== DbHttpFullUpdateSyncSheetTaskClass ==================
DbHttpFullUpdateSyncSheetTaskClass::DbHttpFullUpdateSyncSheetTaskClass(KEtWorkbook* wwb)
	: DbHttpFullUpdateSyncSheetBaseTaskClass(wwb)
{
}

HRESULT DbHttpFullUpdateSyncSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	m_timeStatInfo.clear();
	std::chrono::steady_clock::time_point fullUpdateBegin = std::chrono::steady_clock::now();
	VarObj param = cmd->cast().get("param");
	// 用户输入
	VAR_OBJ_EXPECT_NUMERIC(param, "sourceSheetId")
	VAR_OBJ_EXPECT_NUMERIC(param, "targetSheetId")
	VAR_OBJ_EXPECT_STRING(param, "fieldSourceName")
	// 服务端输入
	VAR_OBJ_EXPECT_STRING(param, "urlTemplate")
	VAR_OBJ_EXPECT_STRUCT(param, "fieldMappingList")
	if (param.get_s("fieldMappingList").keys().empty())
	{
		WOLOG_ERROR << "[DbHttpFullUpdateSyncSheetTaskClass] The field mapping list of http.db.fullUpdateSyncSheet cannot be empty!";
		return E_INVALID_REQUEST;
	}

	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = param, wmh.strField = "uploadid";
	QString path = wmh.resolvePath();
	if (path.isEmpty())
	{
		WOLOG_ERROR << "[DbHttpFullUpdateSyncSheetTaskClass] No file info!";
		return E_FAIL;
	}

	PCWSTR filePath = krt::utf16(path);
	_Application* pTarApp = m_wwb->GetCoreApp();
	ks_stdptr<Workbooks> spWorkbooks;
	pTarApp->get_Workbooks(&spWorkbooks);
	if (!spWorkbooks)
	{
		ASSERT(FALSE);
		return E_FAIL;
	}

	std::chrono::steady_clock::time_point openBegin = std::chrono::steady_clock::now();
	// 打开源文件
	OpenWorkbookScope openWorkbookScope(spWorkbooks, pTarApp->GetAppPersist()->GetPersist(), filePath);
	HRESULT hr = openWorkbookScope.GetHr();
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[DbHttpFullUpdateSyncSheetTaskClass] Data source error!";
		return hr;
	}
	auto openElapsed = (std::chrono::steady_clock::now() - openBegin) / std::chrono::milliseconds(1);
	m_timeStatInfo.AppendFormat(__X("open_%d_"), openElapsed);

	_Workbook* pSrcWorkbook = openWorkbookScope.GetWorkbook();
	if (!pSrcWorkbook)
	{
		WOLOG_ERROR << "[DbHttpFullUpdateSyncSheetTaskClass] Data source error!";
		return E_FAIL;
	}
	_Workbook* pTarWorkbook = m_wwb->GetCoreWorkbook();
	binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, pCtx);
	hr = FullUpdateSyncSheet(pCtx, pSrcWorkbook, pTarWorkbook, param, acpt, filePath);
	if (FAILED(hr))
		return hr;

	auto fullUpdateElapsed = (std::chrono::steady_clock::now() - fullUpdateBegin) / std::chrono::milliseconds(1);
	ks_wstring fullUpdateStatInfo;
	fullUpdateStatInfo.Format(__X("fullUpdate_%d_%s"), fullUpdateElapsed, m_timeStatInfo.c_str());
	pCtx->collectInfo(krt::fromUtf16(fullUpdateStatInfo.c_str()).toUtf8());
	
	return S_OK;
}

HRESULT DbHttpFullUpdateSyncSheetTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR DbHttpFullUpdateSyncSheetTaskClass::GetTag()
{
	return __X("http.db.fullUpdateSyncSheet");
}

// ================== DbHttpBatchFullUpdateSyncSheetTaskClass ==================
DbHttpBatchFullUpdateSyncSheetTaskClass::DbHttpBatchFullUpdateSyncSheetTaskClass(KEtWorkbook* wwb)
	: DbHttpFullUpdateSyncSheetBaseTaskClass(wwb)
{
}

HRESULT DbHttpBatchFullUpdateSyncSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = param, wmh.strField = "uploadid";
	QString path = wmh.resolvePath();
	if (path.isEmpty())
	{
		WOLOG_ERROR << "[DbHttpBatchFullUpdateSyncSheetTaskClass] No file info!";
		return E_FAIL;
	}
	PCWSTR filePath = krt::utf16(path);
	_Application* pTarApp = m_wwb->GetCoreApp();
	ks_stdptr<Workbooks> spWorkbooks;
	pTarApp->get_Workbooks(&spWorkbooks);
	if (!spWorkbooks)
	{
		ASSERT(FALSE);
		return E_FAIL;
	}
	// 打开源文件
	OpenWorkbookScope openWorkbookScope(spWorkbooks, pTarApp->GetAppPersist()->GetPersist(), filePath);
	HRESULT hr = openWorkbookScope.GetHr();
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[DbHttpBatchFullUpdateSyncSheetTaskClass] Data source error!";
		return hr;
	}
	_Workbook* pSrcWorkbook = openWorkbookScope.GetWorkbook();
	if (!pSrcWorkbook)
	{
		WOLOG_ERROR << "[DbHttpBatchFullUpdateSyncSheetTaskClass] Data source error!";
		return E_FAIL;
	}
	_Workbook* pTarWorkbook = m_wwb->GetCoreWorkbook();
	// 清理事务,并不收集事务
	pTarApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(pTarWorkbook);
	ks_stdptr<IWorkspace> spWS;
	IBook* pBook = pTarWorkbook->GetBook();
	pBook->GetWorkspace(&spWS);
	Scope_SoftDisableNonCoopUndo disableUndoScope(spWS);
	binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, pCtx);
	{
		wo::sa::Leave leave = wo::sa::enterArray(&acpt, "syncSheets");
		VarObj syncSheets = param.get_s("syncSheets");
		for (int i = 0, cnt = syncSheets.arrayLength_s(); i < cnt; i++)
		{
			VarObj syncSheet = syncSheets.at_s(i);
			hr = FullUpdateSyncSheet(pCtx, pSrcWorkbook, pTarWorkbook, syncSheet, acpt, filePath, true);
			if(FAILED(hr))
			{
				WOLOG_ERROR << "[DbHttpBatchFullUpdateSyncSheetTaskClass] Fullupdate error!";
				return hr;
			}
		}
	}

	return S_OK;
}

HRESULT DbHttpBatchFullUpdateSyncSheetTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR DbHttpBatchFullUpdateSyncSheetTaskClass::GetTag()
{
	return __X("http.db.batchFullUpdateSyncSheet");
}

// ================== DbHttpFullUpdateSyncMergeSheetTaskClass ==================
DbHttpFullUpdateSyncMergeSheetTaskClass::DbHttpFullUpdateSyncMergeSheetTaskClass(KEtWorkbook* wwb)
	: DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpFullUpdateSyncMergeSheetTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	VAR_OBJ_EXPECT_NUMERIC(param, "targetSheetId")
	VAR_OBJ_EXPECT_STRING(param, "fieldSourceName")
	VAR_OBJ_EXPECT_STRING(param, "fldSourceFileName")
	// 服务端输入
	VAR_OBJ_EXPECT_STRING(param, "urlTemplate")
	VAR_OBJ_EXPECT_STRUCT(param, "fieldMappingList")
	WebMimeHelper wmh;
	UINT tarStId = param.field_uint32("targetSheetId");
	HRESULT hr = m_spProtectionJudgement->CheckSheetCanEditProp(tarStId);
	if (FAILED(hr))
		return hr;
	_Application* pTarApp = m_wwb->GetCoreApp();
	ks_stdptr<Workbooks> spWorkbooks;
	pTarApp->get_Workbooks(&spWorkbooks);
	if (!spWorkbooks)
	{
		ASSERT(FALSE);
		return E_FAIL;
	}
	_Workbook* pTarWorkbook = m_wwb->GetCoreWorkbook();
	VarObj fieldMappingList = param.get("fieldMappingList");
	bool bNewLookupConvert = true;
	if (param.has("new_lookup_converter"))
		bNewLookupConvert = param.field_bool("new_lookup_converter");
	DbSheet::DisableDbTrackHistoryScope noDbHistoryDisabler;
	DbSheet::DisableDbUpdateLastModifiedInfoScope noDbLastModifyScope;
	FullUpdateMergeSyncDbSheetsAdapter updateHelper(pTarWorkbook, tarStId, m_spProtectionJudgement, bNewLookupConvert);
	FullUpdateMergeSyncDbSheetsAdapter::FullUpdateMergeSyncDbSheetParam syncParam;
	syncParam.fldSourceName = param.field_str("fieldSourceName");
	syncParam.urlTemplate = param.field_str("urlTemplate");
	syncParam.fldSourceFileName = param.field_str("fldSourceFileName");
	hr = updateHelper.Init(syncParam);
	if (FAILED(hr))
		return hr;
	// TODO 让服务端提供一个BatchResolvePath的接口
	VarObj srcInfoVec = fieldMappingList.get_s("field_mapping_items");
	int srcInfoCnt = srcInfoVec.arrayLength();
	updateHelper.SetBookNum(srcInfoCnt);
	for (int i = 0; i < srcInfoCnt; i++)
	{
		VarObj srcSheetParam = srcInfoVec.at(i);
		PCWSTR pcwFileId = srcSheetParam.field_str("file_id");
		wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = srcSheetParam, wmh.strField = "uploadid";
		QString path = wmh.resolvePath();
		if (path.isEmpty())
		{
			WOLOG_ERROR << "[DbHttpFullUpdateSyncMergeSheetTaskClass] No file info!";
			return E_FAIL;
		}
		PCWSTR pcwFilePath = krt::utf16(path);
		// 打开源文件
		OpenWorkbookScope openWorkbookScope(spWorkbooks, pTarApp->GetAppPersist()->GetPersist(), pcwFilePath);
		HRESULT hr = openWorkbookScope.GetHr();
		if (FAILED(hr))
		{
			WOLOG_ERROR << "[DbHttpFullUpdateSyncMergeSheetTaskClass] Data source error!";
			return hr;
		}
		_Workbook* pSrcWorkbook = openWorkbookScope.GetWorkbook();
		if (!pSrcWorkbook)
		{
			WOLOG_ERROR << "[DbHttpFullUpdateSyncMergeSheetTaskClass] Data source error!";
			return E_FAIL;
		}
		VarObj srcSheetItems = srcSheetParam.get_s("sheet_items");
		updateHelper.SetSrcWorkbook(pSrcWorkbook, i);
		PCWSTR pcwFileName = pcwFilePath;
		if (srcSheetParam.has("filename"))
			pcwFileName = srcSheetParam.field_str("filename");
		hr = updateHelper.AddFieldMappingItem(pcwFileId, pcwFileName, srcSheetItems);
		if (FAILED(hr))
			return hr;
		hr = updateHelper.ExecMerge();
		if (FAILED(hr))
			return hr;
	}
	fillHttpResponse(pCtx, updateHelper);
	return S_OK;
}

void DbHttpFullUpdateSyncMergeSheetTaskClass::fillHttpResponse(KEtRevisionContext* pCtx, const FullUpdateMergeSyncDbSheetsAdapter& updateHelper)
{
	binary_wo::BinWriter* pResponse = pCtx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, pCtx);
	IDBSheetCtx* pDbctx = _appcore_GainDbSheetContext();
	const auto& stFileIdMap = updateHelper.getFileIdMap();
	EtDbIdStr buf;
	if (updateHelper.GetLostFlag())
		acpt.addBool("lostInfo", true);
	VS(pDbctx->EncodeEtDbId(updateHelper.GetFieldSourceId(), &buf));
	acpt.addString("fieldSourceId", buf);
	VS(pDbctx->EncodeEtDbId(updateHelper.GetFieldSourceNameId(), &buf));
	acpt.addString("fldSourceFileInfoId", buf);
	acpt.addKey("fieldMappingList");
	acpt.beginStruct();
		acpt.addKey("field_mapping_items");
		acpt.beginArray();
			for (auto it : stFileIdMap)
			{
				acpt.beginStruct();
				acpt.addString("file_id", it.first.c_str());
				acpt.addKey("sheet_items");
				acpt.beginArray();
				for (auto stFieldMappingItem : it.second)
				{
					acpt.beginStruct();
					acpt.addUint32("sheet_id", stFieldMappingItem.m_uSheetStId);
					acpt.addKey("field_id_map");
					acpt.beginStruct();
					for (auto stFieldIdItem : stFieldMappingItem.m_fieldIdMap)
					{
						VS(pDbctx->EncodeEtDbId(stFieldIdItem.first, &buf));
						auto srcFieldId = krt::fromUtf16(buf).toStdString();
						VS(pDbctx->EncodeEtDbId(stFieldIdItem.second, &buf));
						acpt.addKey(srcFieldId.data(), true);
						acpt.addString(nullptr, buf);
					}
					acpt.endStruct();
					acpt.endStruct();
				}
				acpt.endArray();
				acpt.endStruct();
			}
		acpt.endArray();
	acpt.endStruct();
}

HRESULT DbHttpFullUpdateSyncMergeSheetTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}
PCWSTR DbHttpFullUpdateSyncMergeSheetTaskClass::GetTag()
{
	return __X("http.db.fullUpdateSyncMergeSheet");
}
// ================== DbHttpSyncFromSqlTaskClass ==================
DbHttpSyncFromSqlTaskClass::DbHttpSyncFromSqlTaskClass(KEtWorkbook* wwb)
: DbHttpTaskClassBase(wwb)
{

}

HRESULT DbHttpSyncFromSqlTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");

	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = param, wmh.strField = "uploadid";
	QString path = wmh.resolvePath();
	if (path.isEmpty())
	{
		WOLOG_ERROR << "[DbHttpSyncFromSqlTaskClass] No file info!";
		return E_FAIL;
	}

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
	SyncSqlCsv2DbAdapter syncSqlCsv2DbAdapter(m_wwb, krt::utf16(path), m_wwb->GetCoreWorkbook(), m_wwb->GetBMP(), m_spProtectionJudgement, param);
	HRESULT hr = syncSqlCsv2DbAdapter.Init();
	if(FAILED(hr))
	{
		WOLOG_ERROR << "[DbHttpSyncFromSqlTaskClass] Init failed!";
		return hr;
	}

	hr = syncSqlCsv2DbAdapter.Exec();
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[DbHttpSyncFromSqlTaskClass] Exec failed!";
		return hr;
	}
	return S_OK;
}

HRESULT DbHttpSyncFromSqlTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR DbHttpSyncFromSqlTaskClass::GetTag()
{
	return __X("http.db.syncFromSql");
}

// ================== DbHttpSyncFormToDbTaskClass ==================
DbHttpSyncFormToDbTaskClass::DbHttpSyncFormToDbTaskClass(KEtWorkbook* wwb)
: DbHttpTaskClassBase(wwb)
{

}

HRESULT DbHttpSyncFormToDbTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr;
	binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, pCtx);
	VarObj param = cmd->cast().get("param");
	if(!param.has("syncSheet"))
	{
		WOLOG_ERROR << "[DbHttpSyncFormToDbTaskClass] No syncSheets info!";
		return E_FAIL;
	}

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
	Form2DbCrossSheetFieldUpdater form2DbCrossSheetFieldUpdater(pBook);
	VarObj syncSheets = param.get_s("syncSheet");
	for(int i = 0; i < syncSheets.arrayLength_s(); ++i)
	{
		VarObj syncSheet = syncSheets.at(i);
		UINT sheetId = syncSheet.field_uint32("sheetId");

		ks_stdptr<_Worksheet> spWorksheet;
		hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
		if (FAILED(hr))
			return hr;

		WebMimeHelper wmh;
		wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = syncSheet, wmh.strField = "uploadid";
		QString path = wmh.resolvePath();
		if (path.isEmpty())
		{
			WOLOG_ERROR << "[DbHttpSyncFormToDbTaskClass] No file info!";
			return E_FAIL;
		}

		QFile file(path);
		if (!file.open(QIODevice::ReadOnly))
		{
			WOLOG_ERROR << "[DbHttpSyncFormToDbTaskClass] No file info!";
			return E_FAIL;
		}
		QByteArray ba = file.readAll();
		file.close();
		binary_wo::BinReader br((const byte*)ba.data(), ba.size());
		binary_wo::VarObjRoot root = br.buildRoot();
		binary_wo::VarObj obj = root.cast();

		Form2DbSheetType st = (i == 0 ? Form2DbSheetType::Form2DbSheetType_MainSheet : Form2DbSheetType::Form2DbSheetType_SubSheet);
		Form2DbJsonParser jsonParser(st);
		hr = jsonParser.ParseJsonData(obj.get_s("sheetdata"));
		if(FAILED(hr))
			return hr;
		param.add_field_bool("preferId", true);	//使用id解析字段值
		param.add_field_bool("valuePreferId", false); //不使用id解析选项值
		DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, param, &m_errMsg);
		Form2DbSyncUpdater syncUpdater(m_wwb, spWorksheet->GetSheet(), &form2DbCrossSheetFieldUpdater, &dbSerialiser, jsonParser.GetValue());
		hr = syncUpdater.UpdateSyncSheet();
		if(FAILED(hr))
		{
			WOLOG_ERROR << "Form2DbSyncUpdater sheetId:" << spWorksheet->GetSheet();
			return hr;
		}
	}
	hr = form2DbCrossSheetFieldUpdater.UpdateCrossSheetFieldValue();
	if(FAILED(hr))
	{
		WOLOG_ERROR << "Form2DbCrossSheetFieldUpdater::UpdateCrossSheetFieldValue"; 
		return hr;
	}

	return S_OK;
}

HRESULT DbHttpSyncFormToDbTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR DbHttpSyncFormToDbTaskClass::GetTag()
{
	return __X("http.db.syncFormToDb");
}

// ================== DbHttpCreateDashboardTaskClass ==================
DbHttpCreateDashboardTaskClass::DbHttpCreateDashboardTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpCreateDashboardTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

    ks_stdptr<etoldapi::Worksheets> spWorkSheets;
    _Workbook* pWb = m_wwb->GetCoreWorkbook();
    pWb->get_Worksheets(&spWorkSheets);
    if (!spWorkSheets)
        return E_FAIL;

    if (!ctx->isExecDirect() && spWorkSheets->GetSheetCount(stDashBoard, TRUE) >= DbSheet::GetDBDashBoardSheetLimit())
        return E_DBSHEET_DASHBOARD_COUNT_LIMIT;

    VAR_OBJ_EXPECT_STRING(param, "name")
    WebStr name = param.field_str("name");

    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

    KComVariant vBefore;
    KComVariant vAfter = getLastSheetIdx() + 1;
    KComVariant vCount;
    KComVariant vType;
    vType.Assign(xlWorksheet);
    SHEETTYPE sheetType = stDashBoard;
    ks_stdptr<IKCoreObject> spCoreObj;
    HRESULT hr = spWorkSheets->Add(vBefore, vAfter, vCount, vType, &spCoreObj, sheetType);
    if (FAILED(hr))
        return hr;

    ks_stdptr<etoldapi::_Worksheet> spWorksheet = spCoreObj;
    if (!spWorksheet)
        return E_FAIL;

    ISheet* pSheet = spWorksheet->GetSheet();
	ks_bstr sheetName;
	GetValidSheetName(spWorkSheets, spWorksheet, name, &sheetName);
	spWorksheet->put_Name(sheetName);
    hr = DashBoard::initDashBoardSheet(static_cast<IKEtWindow*>(pWb->GetActiveWindow()), spWorksheet, stDashBoard);
    if (FAILED(hr))
    {
        spWorksheet->DeleteDirectly();
        return hr;
    }

    ks_stdptr<IDBDashBoardDataOp> spDbDashBoard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashBoard));
    if (param.has("description"))
    {
        PCWSTR description = param.field_str("description");
        hr = spDbDashBoard->SetSheetDescription(description);
        if (FAILED(hr))
        {
            spWorksheet->DeleteDirectly();
            return hr;
        }
    }
    if (param.has("icon"))
    {
        PCWSTR icon = param.field_str("icon");
        hr = spDbDashBoard->SetSheetIcon(icon);
        if (FAILED(hr))
        {
            spWorksheet->DeleteDirectly();
            return hr;
        }
    }
    acpt.addKey("sheet");
    acpt.beginStruct();
	SerialiseSheetInfo(pWb, spWorksheet, &acpt, &m_errMsg);
    acpt.endStruct();
    return S_OK;
}

HRESULT DbHttpCreateDashboardTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR DbHttpCreateDashboardTaskClass::GetTag()
{
    return __X("http.db.createDashboard");
}

// ================== DbHttpUpdateDashboardTaskClass ==================
DbHttpUpdateDashboardTaskClass::DbHttpUpdateDashboardTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpUpdateDashboardTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

	HRESULT hr = m_spProtectionJudgement->CheckSheetCanEditProp(sheetStId);
	if (FAILED(hr))
        return hr;
    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

    ks_stdptr<_Worksheet> spWorksheet;
    hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    if (!spWorksheet->GetSheet()->IsDbDashBoardSheet())
        return E_INVALID_REQUEST;

    // 至少要修改一个属性
    if (!param.has("name") && !param.has("description") && !param.has("icon"))
        return E_INVALID_REQUEST;

    if (param.has("name"))
    {
        WebStr name = param.field_str("name");
        hr = SetSheetName(spWorksheet, name);
        if (FAILED(hr))
            return hr;
    }

    ks_stdptr<IDBDashBoardDataOp> spDbDashBoard;
    ISheet* pSheet = spWorksheet->GetSheet();
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashBoard));
    if (param.has("description"))
    {
        PCWSTR description = param.field_str("description");
        hr = spDbDashBoard->SetSheetDescription(description);
        if (FAILED(hr))
            return hr;
    }
    if (param.has("icon"))
    {
        PCWSTR icon = param.field_str("icon");
        hr = spDbDashBoard->SetSheetIcon(icon);
        if (FAILED(hr))
            return hr;
    }
    acpt.addKey("sheet");
    acpt.beginStruct();
	SerialiseSheetInfo(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, &m_errMsg);
    acpt.endStruct();
    return S_OK;
}

HRESULT DbHttpUpdateDashboardTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK;
}

PCWSTR DbHttpUpdateDashboardTaskClass::GetTag()
{
    return __X("http.db.updateDashboard");
}

// ================== DbHttpClearDashboardTaskClass ==================
DbHttpClearDashboardTaskClass::DbHttpClearDashboardTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpClearDashboardTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    IKWebExtensionMgr* pWebExtensionMgr = m_wwb->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return E_FAIL;

    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

	hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
	if (FAILED(hr))
        return hr;

    ks_stdptr<IDBChartStatisticMgr> spDbChartStatisticMgr;
    hr = m_commonHelper.GetDBChartStatisticMgr(sheetId, &spDbChartStatisticMgr);
    if (FAILED(hr))
        return hr;

    UINT webExtensionCount = 0;
    ISheet* pSheet = spWorksheet->GetSheet();
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    std::vector<ks_stdptr<IKWebExtension>> webExtensions;
    webExtensions.reserve(webExtensionCount);
    for (UINT i = 0; i < webExtensionCount; ++i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            return hr;

        LPCWSTR propertyValue = nullptr;
        hr = spWebExtension->GetProperty(GetWebofficeUniquePropertyKey(), &propertyValue);
        if (FAILED(hr) || !propertyValue)
            continue;

        webExtensions.emplace_back(spWebExtension);
    }

    for (auto& spWebExtension: webExtensions)
    {
        EtDbId moduleId = INV_EtDbId;
        hr = DbDashboard::GetDbChartStatisticModuleId(spWebExtension, moduleId);
        if (FAILED(hr))
            continue;

        hr = DbDashboard::DeleteWebExtensionHostShape(spWorksheet, spWebExtension);
        if (FAILED(hr))
            return hr;

        hr = spDbChartStatisticMgr->DelItem(moduleId);
        if (FAILED(hr))
            return hr;

        hr = spWebExtension->Delete(TRUE, FALSE);
        if (FAILED(hr))
            return hr;
    }
    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("sheet");
    acpt.beginStruct();
    acpt.addUint32("id", sheetId);
    acpt.endStruct();
    return S_OK;
}

HRESULT DbHttpClearDashboardTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK;
}

PCWSTR DbHttpClearDashboardTaskClass::GetTag()
{
    return __X("http.db.clearDashboard");
}

// ================== DbHttpDeleteDashboardTaskClass ==================
DbHttpDeleteDashboardTaskClass::DbHttpDeleteDashboardTaskClass(KEtWorkbook* wwb)
        : DbHttpDeleteSheetTaskClass(wwb)
{
}

PCWSTR DbHttpDeleteDashboardTaskClass::GetTag()
{
    return __X("http.db.deleteDashboard");
}

// ================== DbHttpCreateWebExtensionTaskClass ==================
DbHttpCreateWebExtensionTaskClass::DbHttpCreateWebExtensionTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpCreateWebExtensionTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

	hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
	if (FAILED(hr))
        return hr;

	ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALID_REQUEST;

    VAR_OBJ_EXPECT_STRING(param, "webExtensionType")
	auto webExtType = WET_Normal;
	hr = _webextension_GainEncodeDecoder()->DecodeWebExtType(param.field_str("webExtensionType"), &webExtType);
	if (FAILED(hr) || webExtType == WET_Normal || webExtType == WET_DataSource)
		return E_INVALID_REQUEST;

	KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
	if (webExtType == WET_DbPlugin && !ctx->isExecDirect())
	{
		hr = dashboardModuleMgrWrapper.CheckCanAddPlugin();
		if (FAILED(hr))
			return hr;
	}

    VAR_OBJ_EXPECT_STRING(param, "dataSourceType")
	auto webExtDataSourceType = dst_Unknown;
	hr = _webextension_GainEncodeDecoder()->DecodeWebExtensionDataSourceType(param.field_str("dataSourceType"), &webExtDataSourceType);
	if (FAILED(hr) || webExtDataSourceType != dst_dbTable)
		return E_INVALID_REQUEST;

    // 创建WebExtension
    ks_stdptr<etoldapi::Shapes> spShapes;
    hr = spWorksheet->get_Shapes(FALSE, &spShapes);
    if (!spShapes)
        return E_FAIL;

    int shapesCnt = 0;
    spShapes->get_Count(&shapesCnt);
    if (shapesCnt >= DashBoard::GetDashBoardModuleLimit())
        return E_DBSHEET_DASHBOARD_CHART_COUNT_LIMIT;

    ks_stdptr<IDbtBookCtx> spDbtBookCtx;
    VS(m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbtBookCtx));
    PCWSTR webExtensionKey = spDbtBookCtx->GainUuidStr();
    PCWSTR dataRangeKey = spDbtBookCtx->GainUuidStr();
    if (!webExtensionKey || !dataRangeKey)
        return E_FAIL;

    IKWebExtension* pWebExtension = DbDashboard::CreateWebExtension(spShapes, webExtensionKey, webExtType);
    if (!pWebExtension)
        return E_FAIL;

    hr = DbDashboard::CreateWebExtensionDataSource(pWebExtension, webExtDataSourceType);
    if (FAILED(hr))
        return hr;

	hr = DashBoard::SetWebextentionProperty(param, pWebExtension);
    if (FAILED(hr))
        return hr;

	if (pWebExtension->IsDatasourceSupportingDashboardModule())
	{
		// 创建DbChart
		ks_stdptr<IDBChartStatisticMgr> spDbChartStatisticMgr;
		VS(DbSheet::GetDBChartStatisticMgr(pSheet, &spDbChartStatisticMgr));


		EtDbId moduleId = INV_EtDbId;
		hr = DbDashboard::SetStatsModuleDataSource(param, spDbChartStatisticMgr, pWebExtension, &moduleId, dataRangeKey);
		if (FAILED(hr))
			return hr;

		if (webExtType == WET_DbDataSource)
		{
			ks_stdptr<IDBChartStatisticModule> spDbChartStatisticModule;
			hr = spDbChartStatisticMgr->GetItemById(moduleId, &spDbChartStatisticModule);
			if (FAILED(hr))
				return hr;

			VAR_OBJ_EXPECT_STRUCT(param, "statisticsModuleSetting")
			binary_wo::VarObj statisticsModuleSetting = param.get("statisticsModuleSetting");
			ET_DBSheet_ChartType chartType = DbDashboard::GetChartType(pWebExtension);
			hr = DbDashboard::SetStatsModuleSetting(statisticsModuleSetting, m_wwb->GetCoreWorkbook(), spDbChartStatisticModule, ctx, chartType);
			if (FAILED(hr))
				return hr;
		}
	}

    hr = pWebExtension->SetProperty(GetWebofficeUniquePropertyKey(), __X("true"));
    if (FAILED(hr))
        return hr;

    ctx->setIsRealTransform(true);
    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("webExtension");
    acpt.beginStruct();
    SerialiseWebExtension(sheetId, pWebExtension, &acpt, &m_commonHelper);
    acpt.endStruct();
    return S_OK;
}

HRESULT DbHttpCreateWebExtensionTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK;
}

PCWSTR DbHttpCreateWebExtensionTaskClass::GetTag()
{
    return __X("http.db.createWebExtension");
}

// ================== DbHttpUpdateWebExtensionTaskClass ==================
DbHttpUpdateWebExtensionTaskClass::DbHttpUpdateWebExtensionTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpUpdateWebExtensionTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    binary_wo::VarObj param = cmd->cast().get("param");
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
    IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return E_FAIL;

    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    IDX sheetIdx = INVALIDIDX;
    pWorkbook->GetBook()->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_DBSHEET_SHEET_NOT_FOUND;

	HRESULT hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
	if (FAILED(hr))
        return hr;

    VAR_OBJ_EXPECT_STRING(param, "webExtensionKey");
    PCWSTR webExtensionKey = param.field_str("webExtensionKey");
    IKWebExtension* pWebExtension = pWebExtensionMgr->FindWebExtension(sheetIdx, webExtensionKey);
    if (!pWebExtension)
        return E_INVALID_REQUEST;

	ks_stdptr<_Worksheet> spWorksheet;
    hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
	if (FAILED(hr))
        return hr;

	hr = DashBoard::SetWebextentionProperty(param, pWebExtension);
	if (FAILED(hr))
		return hr;

	if (pWebExtension->IsDatasourceSupportingDashboardModule())
	{
		EtDbId moduleId = INV_EtDbId;
		hr = DbDashboard::GetDbChartStatisticModuleId(pWebExtension, moduleId);
		if (FAILED(hr))
			return hr;

		ks_stdptr<IDBChartStatisticMgr> spDbChartStatisticMgr;
		hr = m_commonHelper.GetDBChartStatisticMgr(sheetId, &spDbChartStatisticMgr);
		if (FAILED(hr))
			return hr;

		ks_stdptr<IDBChartStatisticModule> spDbChartStatisticModule;
		hr = spDbChartStatisticMgr->GetItemById(moduleId, &spDbChartStatisticModule);
		if (FAILED(hr))
			return hr;

		if (pWebExtension->GetWebShapeType() == WET_DbPlugin)
		{
			KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
			std::unique_ptr<KDbDashboardPluginWrapper> pPluginWrapper = dashboardModuleMgrWrapper.GetPlugin(webExtensionKey);
			if (!pPluginWrapper)
				return E_INVALID_REQUEST;
			VAR_OBJ_EXPECT_STRUCT(param, "pluginConfig")
			binary_wo::VarObj pluginConfig = param.get("pluginConfig");
			hr = pPluginWrapper->SetPluginConfig(pluginConfig, ctx);
		}
		else
		{
			VAR_OBJ_EXPECT_STRUCT(param, "statisticsModuleSetting")
			binary_wo::VarObj statisticsModuleSetting = param.get("statisticsModuleSetting");
			ET_DBSheet_ChartType chartType = DbDashboard::GetChartType(pWebExtension);
			hr = DbDashboard::SetStatsModuleSetting(statisticsModuleSetting, pWorkbook, spDbChartStatisticModule, ctx, chartType);
		}
		if (FAILED(hr))
			return hr;
	}

    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("webExtension");
    acpt.beginStruct();
    SerialiseWebExtension(sheetId, pWebExtension, &acpt, &m_commonHelper);
    acpt.endStruct();
    return S_OK;
}

HRESULT DbHttpUpdateWebExtensionTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK;
}

PCWSTR DbHttpUpdateWebExtensionTaskClass::GetTag()
{
    return __X("http.db.updateWebExtension");
}

// ================== DbHttpDeleteWebExtensionTaskClass ==================
DbHttpDeleteWebExtensionTaskClass::DbHttpDeleteWebExtensionTaskClass(KEtWorkbook* wwb)
        : DbHttpTaskClassBase(wwb)
{
}

HRESULT DbHttpDeleteWebExtensionTaskClass::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    EnableProtectCompileFmla enableClFlmGuard;
    binary_wo::VarObj param = cmd->cast().get("param");
    IKWebExtensionMgr* pWebExtensionMgr = m_wwb->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return E_FAIL;

    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return E_DBSHEET_SHEET_NOT_FOUND;

	HRESULT hr = m_spProtectionJudgement->CheckCanManageDBChart(sheetId);
	if (FAILED(hr))
        return hr;
	
    VAR_OBJ_EXPECT_STRING(param, "webExtensionKey");
    PCWSTR webExtensionKey = param.field_str("webExtensionKey");
    IKWebExtension* pWebExtension = pWebExtensionMgr->FindWebExtension(sheetIdx, webExtensionKey);
    if (!pWebExtension)
        return E_INVALID_REQUEST;

    ks_stdptr<_Worksheet> spWorksheet;
    hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDBDashBoardDataOp> spDbDashBoard;
    VS(DbSheet::GetDBDashBoardOp(spWorksheet->GetSheet(), &spDbDashBoard));
    hr = DbDashboard::DeleteWebExtension(pWebExtension, spWorksheet, spDbDashBoard);
    if (FAILED(hr))
        return hr;

    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("webExtension");
	acpt.beginStruct();
    acpt.addString("webExtensionKey", webExtensionKey);
	acpt.endStruct();
    return S_OK;
}

HRESULT DbHttpDeleteWebExtensionTaskClass::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
    return S_OK;
}

PCWSTR DbHttpDeleteWebExtensionTaskClass::GetTag()
{
    return __X("http.db.deleteWebExtension");
}

} // namespace wo
