﻿#include "etstdafx.h"
#include "merge_range_helper.h"

namespace wo
{

namespace
{
inline bool rect_is_contain(const RANGE& src, const RANGE& dst)
{
	return src.RowFrom() <= dst.RowFrom() && src.RowTo() >= dst.RowTo()
		&& src.ColFrom() <= dst.ColFrom() && src.ColTo() >= dst.ColTo();
}

inline bool rect_is_join_left(const RANGE& src, const RANGE& dst)
{
	return src.RowFrom() == dst.RowFrom() && src.RowTo() == dst.RowTo()
		&& src.ColFrom() - 1 <= dst.ColTo() && src.ColFrom() - 1 >= dst.ColFrom();
}

inline bool rect_is_join_right(const RANGE& src, const RANGE& dst)
{
	return src.RowFrom() == dst.RowFrom() && src.RowTo() == dst.RowTo()
		&& src.ColTo() + 1 >= dst.ColFrom() && src.ColTo() + 1 <= dst.ColTo();
}

inline bool rect_is_join_top(const RANGE& src, const RANGE& dst)
{
	return src.ColFrom() == dst.ColFrom() && src.ColTo() == dst.ColTo()
		&& src.RowFrom() - 1 <= dst.RowTo() && src.RowFrom() - 1 >= dst.RowFrom();
}

inline bool rect_is_join_bottom(const RANGE& src, const RANGE& dst)
{
	return src.ColFrom() == dst.ColFrom() && src.ColTo() == dst.ColTo()
		&& src.RowTo() + 1 >= dst.RowFrom() && src.RowTo() + 1 <= dst.RowTo();
}
}
void RectContainer::add(const RANGE& rg)
{
	if (!tryMerge(rg))
		m_rgs.push_back(rg);
}

std::vector<RANGE> RectContainer::get()
{
	return m_rgs;
}

bool RectContainer::tryMerge(const RANGE& srcRg)
{
	for (auto& rg : m_rgs)
	{
		if (rg.SheetFrom() != srcRg.SheetFrom() || rg.SheetTo() != srcRg.SheetTo())
			continue;
		if (rect_is_contain(rg, srcRg))
			return true;

		if (rect_is_join_left(rg, srcRg))
		{
			rg.SetColFrom(srcRg.ColFrom());
			rg.SetColTo(std::max<COL>(srcRg.ColTo(), rg.ColTo()));
			return true;
		}

		if (rect_is_join_right(rg, srcRg))
		{
			rg.SetColTo(srcRg.ColTo());
			rg.SetColFrom(std::min<COL>(srcRg.ColFrom(), rg.ColFrom()));
			return true;
		}

		if (rect_is_join_top(rg, srcRg))
		{
			rg.SetRowFrom(srcRg.RowFrom());
			rg.SetRowTo(std::max<ROW>(srcRg.RowTo(), rg.RowTo()));
			return true;
		}

		if (rect_is_join_bottom(rg, srcRg))
		{
			rg.SetRowTo(srcRg.RowTo());
			rg.SetRowFrom(std::min<ROW>(srcRg.RowFrom(), rg.RowFrom()));
			return true;
		}
	}
	return false;
}

void MergeRangeHelper::executeMerge()
{
	for (auto rg : m_rgs)
	{
		m_rc.add(rg);
	}
}

void MergeRangeHelper::serialize(const char* key, ISerialAcceptor* acpt)
{
	std::vector<RANGE> rgs = m_rc.get();
	if (rgs.empty()) return;

	acpt->addKey(key);
	acpt->beginArray();
	for (auto rg : rgs)
	{
		acpt->beginStruct();
			acpt->addInt32("sheetIdx", rg.SheetFrom());
			acpt->addInt32("RowFrom", rg.RowFrom());
			acpt->addInt32("RowTo", rg.RowTo());
			acpt->addInt32("ColFrom", rg.ColFrom());
			acpt->addInt32("ColTo", rg.ColTo());
		acpt->endStruct();
	}
	acpt->endArray();
}
}