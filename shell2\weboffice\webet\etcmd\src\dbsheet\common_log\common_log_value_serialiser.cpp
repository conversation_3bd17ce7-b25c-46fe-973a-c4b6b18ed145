﻿#include "etstdafx.h"
#include "common_log_value_serialiser.h"
#include <public_header/drawing/api/dghost_i.h>
#include "dbsheet/et_dbsheet_utils.h"
#include "Coding/core_bundle/framework/krt/krtstring.h"
#include "ettools/ettools_encode_decoder.h"
#include "helpers/statsheet_helper.h"
#include "db/db_basic_itf.h"
#include "wo/db_id_encode_decode.h"

namespace wo
{
// ================== CommonLogValueSerialiser ==================
CommonLogValueSerialiser::CommonLogValueSerialiser(IKWorkbook *pWorkbook, IKWorksheet *pWorksheet)
    : m_pEncodeDecoder(_appcore_GainEncodeDecoder())
    , m_pBook(pWorkbook->GetBook())
{
    m_pSheet = pWorksheet->GetSheet();
    VS(DbSheet::GetDBSheetOp(m_pSheet, &m_spDbSheetOp));
    VS(DbSheet::GetDBSheetViews(m_pSheet, &m_spDbSheetViews));
    m_pFieldManager = m_spDbSheetOp->GetFieldsManager();
    _etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools);
    m_spStringTools->SetEnv(m_pSheet);

    ks_stdptr<IUnknown> spUnknown;
    VS(m_pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
    ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
    spDBUserGroups->GetJudgement(&m_spProtectionJudgement);
}

namespace
{

//序列化单个联系人的结构单项
void serializeContactItem(ContactFieldItem* pLogItem, IBook* pBook, PCWSTR id)
{
    ks_stdptr<IDbUsersManager> spUsersMgr;
    pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);
    PCWSTR nickname = spUsersMgr->GetNickname(id);
    PCWSTR avatar = spUsersMgr->GetAvatar(id);
    PCWSTR companyId = spUsersMgr->GetCompanyId(id);
    pLogItem->id = id;
    if (spUsersMgr->IsExist(id))
    {
        pLogItem->nickName = nickname;
        pLogItem->avatar = avatar;
        if (xstrlen(companyId) > 0)
            pLogItem->companyId = companyId;
    }
}

template <typename T>
void serialiseFieldDefaultValue(T* pLogField, IDbField* pField, IBook* pBook)
{
    ET_DbSheet_Field_Default_Value_Type defaultValType = pField->GetDefaultValType();
    PCWSTR defaultValTypeStr = nullptr;
    VS(_appcore_GainEncodeDecoder()->EncodeDbFieldDefaultValueType(defaultValType, &defaultValTypeStr));
    pLogField->defaultValueType = (defaultValTypeStr);

    PCWSTR defaultVal = pField->GetDefaultVal();
    if (!defaultVal || xstrlen(defaultVal) == 0)
        return;
    ET_DbSheet_FieldType fieldType = pField->GetType();
    if (fieldType == Et_DbSheetField_Contact)
    {
        if (defaultValType != DbSheet_Field_Dvt_RecordCreator)
        {
            QString contacts = krt::fromUtf16(defaultVal);
            QStringList contactList = contacts.split(",");
            auto contactDefaultValVec = ContactDefaultVal();
            for (const auto& contact: contactList)
            {
                if (contact.isEmpty())
                    continue;
                ContactFieldItem logItem{};
                serializeContactItem(&logItem, pBook, krt::utf16(contact));
                contactDefaultValVec.emplace_back(std::move(logItem));
            }
            pLogField->defaultValue = std::move(contactDefaultValVec);
        }
    }
    else if (fieldType == Et_DbSheetField_MultipleSelect)
    {
        ks_stdptr<IDbSelectItemHandle> spSelectHandle;
        _db_CreateObject(CLSID_KDbSelectItemHandle, IID_IDbSelectItemHandle, (void**) &spSelectHandle);
        HRESULT hr = spSelectHandle->CreateFromCellStr(defaultVal);
        if (FAILED(hr))
            return;
        auto selectDefaultValVec = MultipleSelectVal();
        for (UINT i = 0, cnt = spSelectHandle->Count(); i < cnt; ++i)
            selectDefaultValVec.emplace_back(spSelectHandle->ItemText(i));
        pLogField->defaultValue = std::move(selectDefaultValVec);
    }
    else
    {
        pLogField->defaultValue = FieldDefaultVal(defaultVal);
    }
}

template <typename T>
void serialiseBasicFieldInfo(T* pLogField, IDbField* pField, const ET_DbSheet_FieldType& type, IBook* pBook)
{
    pLogField->id = pField->GetID();
    PCWSTR typeStr = nullptr;
    VS(_ettools_GainEncodeDecoder()->EncodeFieldType(type, &typeStr));
    ASSERT(typeStr);
    pLogField->type = (typeStr);
    // pLogField->name = (pField->GetName());
    // pLogField->numberFormat = (pField->GetNumberFormat());
    // pLogField->description = (pField->GetDescription());
    // serialiseFieldDefaultValue<T>(pLogField, pField, pBook);
    // pLogField->syncField = alg::BOOL2bool(pField->IsSyncField());
    // pLogField->arraySupport = alg::BOOL2bool(pField->IsArray());
    // pLogField->autoFillSourceField = pField->GetAutoFillSourceField();
    // pLogField->customConfig = (pField->GetCustomConfig());
    // if (pField->IsFieldTypeCanSwitchOnValUnique())
    //     pLogField->uniqueValue = alg::BOOL2bool(pField->IsValueUnique());
    // if (Et_DbSheetField_FormulaResult == type || type == Et_DbSheetField_Lookup)
    // {
    //     PCWSTR valueTypeLiteral = __X("");
    //     _appcore_GainEncodeDecoder()->EncodeDbFieldValueType(pField->GetValueType(), &valueTypeLiteral);
    //     pLogField->valueType = (valueTypeLiteral);
    // }
}

// void serialiseExtraFieldInfo(Log_Field* pLogField, IBook* pBook, IDbField* pField)
// {
//     switch (pField->GetType())
//     {
//         case Et_DbSheetField_SingleSelect:
//         case Et_DbSheetField_MultipleSelect:
//         {
//             ks_stdptr<IDbField_Select> spField_Select = pField;
//             SelectFieldExt ext{};
//             for (int i = 0, cnt = spField_Select->Count(); i < cnt; ++i)
//             {
//                 SelectItem selectItem {};
//                 EtDbId id = INV_EtDbId;
//                 PCWSTR value = __X("");
//                 ARGB color = 0;
//                 spField_Select->Item(i, &value, &color, &id);
//                 selectItem.id = id;
//                 selectItem.value = value;
//                 selectItem.color = color;
//                 ext.items.push_back(selectItem);
//             }
//             ext.allowAddItemWhenInputting = alg::BOOL2bool(spField_Select->GetAllowAddItemWhenInputting());
//             ext.autoAddItem = alg::BOOL2bool(spField_Select->GetAutoAddItem());
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Rating:
//         {
//             ks_stdptr<IDbField_Rating> spFieldRating = pField;
//             RatingFieldExt ext{};
//             ext.max = spFieldRating->GetMaxRating();
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Date:
//         {
//             ks_stdptr<IDbField_Date> spFieldDate = pField;
//             DateFieldExt ext{};
//             ext.loadLegalHoliday = spFieldDate->GetLoadLegalHoliday();
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Url:
//         {
//             ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = pField;
//             UrlFieldExt ext{};
//             ext.displayText = spFieldHyperlink->GetDisplayText();
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Contact:
//         {
//             ks_stdptr<IDbField_Contact> spFieldContact = pField;
//             ContactFieldExt ext{};
//             ext.supportMulti = alg::BOOL2bool(spFieldContact->GetSupportMulti());
//             ext.multipleContacts = ext.supportMulti;
//             ext.noticeNewContact = alg::BOOL2bool(spFieldContact->GetSupportNotice());
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Link:
//         case Et_DbSheetField_OneWayLink:
//         {
//             ks_stdptr<IDbField_Link> spFieldLink = pField;
//             LinkFieldExt ext{};
//             ext.linkSheet = spFieldLink->GetLinkSheet();
//             ext.isSyncLink = alg::BOOL2bool(spFieldLink->IsSyncLink());
//             ext.supportMulti = alg::BOOL2bool(spFieldLink->GetSupportMultiLinks());

//             if (pField->GetType() == Et_DbSheetField_Link)
//                 ext.linkField = spFieldLink->GetReversedLinkField();

//             ext.linkView = spFieldLink->GetLinkView();
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Lookup:
//         {
//             ks_stdptr<IDbField_Lookup> spLookupField = pField;
//             LookupFieldExt ext{};

//             // 处理关联字段
//             EtDbId fldId = spLookupField->GetLinkFieldId();
//             if (fldId != INV_EtDbId)
//                 ext.linkField = fldId;

//             // 处理查找字段
//             fldId = spLookupField->GetLookupFieldId();
//             ext.lookupField = fldId;

//             // 处理查找类型
//             DBLookupType lookupType = spLookupField->GetLookupType();
//             if (lookupType > LT_NORMAL)
//             {
//                 ext.lookupSheetId = spLookupField->GetLookupSheetId();
//                 ext.lookupType = lookupType;
//             }

//             // 处理基字段类型
//             ks_stdptr<IDbField> spBaseField;
//             if (SUCCEEDED(spLookupField->GetLookupBaseField(&spBaseField)))
//             {
//                 ET_DbSheet_FieldType baseType = spBaseField->GetType();
//                 PCWSTR baseTypeStr = nullptr;
//                 VS(_appcore_GainEncodeDecoder()->EncodeFieldType(baseType, &baseTypeStr));
//                 ext.baseType = baseTypeStr;
//                 PCWSTR valueTypeStr = nullptr;
//                 VS(_appcore_GainEncodeDecoder()->EncodeDbFieldValueType(pField->GetValueType(), &valueTypeStr));
//                 if (valueTypeStr)
//                     ext.valueType = valueTypeStr;
//             }

//             // 处理聚合函数
//             ET_DbSheet_Lookup_Function aggregation = spLookupField->GetLookupFunction();
//             PCWSTR strAggregation = nullptr;
//             VS(_appcore_GainEncodeDecoder()->EncodeDbLookupFunction(aggregation, &strAggregation));
//             ext.aggregation = strAggregation;
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Attachment: 
//         {
//             ks_stdptr<IDbField_Attachment> spFieldAttachment = pField;
//             AttachmentFieldExt ext{};

//             ext.attachmentType = spFieldAttachment->GetAttachmentType();
//             ext.onlyUploadByCamera = spFieldAttachment->GetOnlyUploadByCamera();
//             PCWSTR displayStyle = __X("");
//             _appcore_GainEncodeDecoder()->EncodeDbAttachmentDisplayStyle(
//                 spFieldAttachment->GetAttachmentDisplayStyle(), &displayStyle);
//             ext.displayStyle = displayStyle;
            
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Address:
//         {
//             ks_stdptr<IDbField_Cascade> spFieldAddress = pField;
//             AddressFieldExt ext{};
//             ext.addressLevel = spFieldAddress->GetCascadeLevel();
//             ext.detailedAddress = alg::BOOL2bool(spFieldAddress->GetWithDetailedInfo());
//             ext.withDetailedAddress = alg::BOOL2bool(spFieldAddress->GetWithDetailedInfo());

//             if (IDbCascadeHandle* pPresetAddressHandle = spFieldAddress->GetDefValueHandle())
//             {
//                 // todo: cbt
//                 // ext.presetAddress = serialiseCascadeHandle(pPresetAddressHandle);
//             }
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Department:
//         {
//             ks_stdptr<IDbField_Cascade> spFieldDepartment = pField;
//             DepartmentFieldExt ext{};
//             ext.supportMulti = spFieldDepartment->GetMultiValue();
//             ext.displayAllLevel = spFieldDepartment->GetDisplayAllLevel();
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Cascade:
//         {
//             ks_stdptr<IDbField_Cascade> spFieldCascade = pField;
//             CascadeFieldExt ext{};

//             ext.displayAllLevel = spFieldCascade->GetDisplayAllLevel();

//             // 处理级联标题
//             IDbCascadeHandle* pTitleHandle = spFieldCascade->GetCascadeTitleHandle();
//             if (pTitleHandle && pTitleHandle->GetCascadeLevel())
//             {
//                 for (UINT i = 0; i < pTitleHandle->GetCascadeLevel(); ++i)
//                     ext.cascadeTitles.push_back(pTitleHandle->GetCascadeItemValue(i));
//             }

//             // 处理级联选项（需要实现类似的枚举器）
//             class KDbCascadeLogger : public IDbCascadeOptionEnum
//             {
//             public:
//                 KDbCascadeLogger(CascadeFieldExt& extRef) 
//                     : m_ext(extRef) {}
                    
//                 STDPROC TravelNode(EtDbId id, PCWSTR value, BOOL isLeafNode) override
//                 {
//                     m_stack.push_back({});
//                     auto& current = m_stack.back();
//                     current.id = id;
//                     current.value = value;
                    
//                     if (!isLeafNode)
//                     {
//                         current.children = std::vector<CascadeNode>(); // 初始化子节点容器
//                     }
//                     else if (m_stack.size() < 2) 
//                     {
//                         m_ext.allCascadeOption.emplace_back(m_stack.back()); // 只有一个节点直接插入到allCascadeOption中
//                     }
//                     return S_OK;
//                 }

//                 STDPROC DoAfterLeafNodeTraveled() override
//                 {
//                     if (m_stack.size() < 2) 
//                     {
//                         m_ext.allCascadeOption.emplace_back(m_stack.back());
//                         return S_OK;
//                     }
                    
//                     auto& child = m_stack.back();
//                     m_stack.pop_back();
//                     m_stack.back().children.value().push_back(std::move(child));
//                     return S_OK;
//                 }
        
//             private:
//                 std::vector<CascadeNode> m_stack; // 使用栈管理层级关系
//                 CascadeFieldExt& m_ext;
//             } enumer(ext);
        
//             ext.displayAllLevel = alg::BOOL2bool(spFieldCascade->GetDisplayAllLevel());
//             spFieldCascade->EnumAllCascadeOption(&enumer);
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Formula:
//         {
//             ks_stdptr<IDbField_Formula> spFieldFormula = pField;
//             FormulaFieldExt ext{};
//             PCWSTR valueTypeStr = nullptr;
//             VS(_appcore_GainEncodeDecoder()->EncodeDbFieldValueType(pField->GetValueType(), &valueTypeStr));
//             ext.valueType = valueTypeStr;
//             ks_bstr formula;
//             VS(spFieldFormula->GetFormula(&formula, FML_CPL_ET_COMPATIBLE));
//             ext.formula = formula.c_str();
//             ext.showPercentAsProgress = spFieldFormula->GetShowPercentAsProgress();
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_LastModifiedTime:
//         case Et_DbSheetField_LastModifiedBy:
//         {
//             ks_stdptr<IDbField_WatchedField> spWatchedField = pField;
//             WatchedFieldExt ext{};
//             ext.watchedAll = spWatchedField->IsWatchedAll();
            
//             if (!ext.watchedAll)
//             {
//                 UINT count = spWatchedField->GetWatchedFieldCount();
//                 ext.watchedField.resize(count);
//                 std::vector<EtDbId> dbIds;
//                 dbIds.resize(count);
//                 spWatchedField->GetWatchedFieldId(dbIds.data(), count);
//                 for (UINT i = 0; i < count; i++)
//                     ext.watchedField.push_back(dbIds[i]);
//             }
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_Button:
//         {
//             ks_stdptr<IDbField_Button> spFieldButton = pField;
//             ButtonExt ext{};
//             ext.icon = spFieldButton->GetButtonIcon();
//             ext.text = spFieldButton->GetButtonText();
//             ext.successText = spFieldButton->GetSuccessText();
//             ext.textColor = spFieldButton->GetTextColor();
//             ext.backgroundColor = spFieldButton->GetBackgroundColor();
//             pLogField->extData = ext;
//             break;
//         }
//         case Et_DbSheetField_BarCode:
//         {
//             ks_stdptr<IDbField_BarCode> spFieldBarCode = pField;
//             BarCodeExt ext{};
//             ext.onlyScanByCamera = spFieldBarCode->GetOnlyScanByCamera();
//             pLogField->extData = ext;
//             break;
//         }
//         default:
//             break;
//     }
// }

void serializeFieldInfo(Log_Field* pLogField, IDbField* pField, IBook* pBook)
{
    // if (pField->IsSyncLookupField())
    // {
    //     serialiseBasicFieldInfo<Log_Field>(pLogField, pField, Et_DbSheetField_Lookup, pBook);
    //     pLogField->baseFieldInfo = std::make_optional<Log_BaseFieldInfo>();
    //     serialiseBasicFieldInfo<Log_BaseFieldInfo>(&pLogField->baseFieldInfo.value(), pField, pField->GetType(), pBook);
    //     serialiseExtraFieldInfo(pLogField, pBook, pField);
    // }
    // else
    // {
        serialiseBasicFieldInfo<Log_Field>(pLogField, pField, pField->GetType(), pBook);
        // serialiseExtraFieldInfo(pLogField, pBook, pField);
    // }
    // if (Et_DbSheetField_Lookup == pField->GetType())
    // {
    //     ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
    //     ks_stdptr<IDbField> spBaseField;
    //     spField_Lookup->GetLookupBaseField(&spBaseField);
    //     if (spBaseField)
    //     {
    //         pLogField->baseFieldInfo = std::make_optional<Log_BaseFieldInfo>();
    //         serialiseBasicFieldInfo<Log_BaseFieldInfo>(&pLogField->baseFieldInfo.value(), spBaseField.get(),
    //                                         spBaseField->GetType(), pBook);
    //     }
    // }
}
inline bool isNeedSerialiseForFakeFormula(IDbField* pField)
{
    return !pField->IsArray() && pField->GetType() == Et_DbSheetField_FormulaResult;
}

bool isNeedSerialiseForFormula(IDbField* pField)
{
    return pField->GetType() == Et_DbSheetField_Formula || isNeedSerialiseForFakeFormula(pField);
}

// TODO token
void _serialiseDataKey(Log_Col*, const_token_ptr pToken)
{
    // acpt->addKey("data", true);
    // switch (GetExecTokenMajorType(pToken))
    // {
    //     using namespace alg;
    //     using namespace db_token_helper;
    //     case ETP_HANDLE:
    //     {
    //         helper<ETP_HANDLE> {pToken}.serialize(acpt);
    //         break;
    //     }
    //     case ETP_VSTR:
    //     {
    //         helper<ETP_VSTR> {pToken}.serialize(acpt);
    //         break;
    //     }
    //     case ETP_VINT:
    //     {
    //         helper<ETP_VINT> {pToken}.serialize(acpt);
    //         break;
    //     }
    //     case ETP_VDBL:
    //     {
    //         helper<ETP_VDBL> {pToken}.serialize(acpt);
    //         break;
    //     }
    //     case ETP_ERROR:
    //     {
    //         helper<ETP_ERROR> {pToken}.serialize(acpt);
    //         break;
    //     }
    //     case ETP_VBOOL:
    //     {
    //         helper<ETP_VBOOL> {pToken}.serialize(acpt);
    //         break;
    //     }
    //     default:
    //         ASSERT("其它情况若涉及请自行添加" == nullptr);
    //         break;
    // }
}

HRESULT serialiseContent(LogCellValue*, EtDbId recordId, IDbField* pField, const_token_ptr pToken, bool serializeDirectly = false)
{
    const ET_DbSheet_FieldType type = pField->GetType();
    // if (!serializeDirectly && pField->IsSyncLookupField())
    // {
    //     switch(type)
    //     {
    //     case Et_DbSheetField_FormulaResult:
    //         AddKey(this->m_acpt, fieldName);
    //         switch (GetExecTokenMajorType(pToken))
    //         {
    //             using namespace alg;
    //             using namespace db_token_helper;
    //         case ETP_VINT:
    //             helper<ETP_VINT> {pToken}.serialize(this->m_acpt);
    //             break;
    //         case ETP_VDBL:
    //             helper<ETP_VDBL> {pToken}.serialize(this->m_acpt);
    //             break;
    //         case ETP_VBOOL:
    //             helper<ETP_VBOOL> {pToken}.serialize(this->m_acpt);
    //             break;
    //         case ETP_VSTR:
    //             helper<ETP_VSTR> {pToken}.serialize(this->m_acpt);
    //             break;
    //         case ETP_ERROR:
    //             helper<ETP_ERROR> {pToken}.serialize(this->m_acpt);
    //             break;
    //         case ETP_HANDLE:
    //             helper<ETP_HANDLE> {pToken}.serialize(this->m_acpt);
    //             break;
    //         default:
    //             ASSERT("其它类型还未支持, 若需要请在此实现" == nullptr);
    //             break;
    //         }
    //         return S_OK;
    //     case Et_DbSheetField_CreatedBy:
    //     case Et_DbSheetField_LastModifiedBy:
    //     case Et_DbSheetField_Contact:
    //     case Et_DbSheetField_MultipleSelect:
    //     case Et_DbSheetField_Attachment:
    //         // 序列化支持直接写在对应字段处理的里面, 这里不作处理
    //         break;
    //     default:
    //     {
    //         if (alg::const_handle_token_assist::is_type(pToken))
    //         {
    //             alg::const_handle_token_assist assist(pToken);
    //             if (assist.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
    //                 return this->serialiseLookup(fieldName, pToken, pField);
    //         }
    //         break;
    //     }
    //     }
    // }
    // switch (type)
    // {
    //     case Et_DbSheetField_MultiLineText:
    //     case Et_DbSheetField_SingleLineText:
    //     case Et_DbSheetField_ID:
    //     case Et_DbSheetField_Phone:
    //     case Et_DbSheetField_Email:
    //     case Et_DbSheetField_SingleSelect:
    //     case Et_DbSheetField_BarCode:
    //         return serialiseValueStr(fieldName, pToken, pField);
    //     case Et_DbSheetField_Attachment:
    //         return _SerialiseAttachment(fieldName, pToken);
    //     case Et_DbSheetField_Address:
    //     case Et_DbSheetField_Cascade:
    //         return _SerialiseAddress(fieldName, pToken);
    //     case Et_DbSheetField_Department:
    //         return _SerialiseDepartment(fieldName, pToken);
    //     case Et_DbSheetField_Note:
    //         return _SerialiseNote(fieldName, pToken);
    //     case Et_DbSheetField_Link:
    //     case Et_DbSheetField_OneWayLink:
    //         return serialiseLink(fieldName, pToken);
    //     case Et_DbSheetField_Lookup:
    //         return serialiseLookup(fieldName, pToken, pField);
    //     case Et_DbSheetField_ParentRecord:
    //         return serialiseParentRecord(fieldName, pToken);
    //     case Et_DbSheetField_Url:
    //         return serialiseUrl(fieldName, recordId, pField->GetID(), pToken);
    //     case Et_DbSheetField_Date:
    //     {
    //         if (DbSheet::ContainTimeFormat(pField->GetNumberFormat()))
    //             return _SerialiseDateTime(fieldName, pToken);
    //         else
    //             return _SerialiseDate(fieldName, pToken);
    //     }
    //     case Et_DbSheetField_Time:
    //         return _SerialiseTime(fieldName, pToken);
    //     case Et_DbSheetField_CreatedTime:
    //         return _SerialiseDateTime(fieldName, pToken);
    //     case Et_DbSheetField_LastModifiedTime:
    //         return _SerialiseDateTime(fieldName, pToken);
    //     case Et_DbSheetField_Number:
    //     case Et_DbSheetField_Currency:
    //     case Et_DbSheetField_Percentage:
    //     case Et_DbSheetField_Rating:
    //     case Et_DbSheetField_Complete:
    //     case Et_DbSheetField_AutoNumber:
    //         return _SerialiseNumber(fieldName, pToken);
    //     case Et_DbSheetField_Checkbox:
    //         return _SerialiseBool(fieldName, pToken);
    //     case Et_DbSheetField_MultipleSelect:
    //         return serialiseMultipleSelect(fieldName, pToken, pField);
    //     case Et_DbSheetField_CellPicture:
    //         return serialiseCellPicture(fieldName, pToken, pField);
    //     case Et_DbSheetField_CreatedBy:
    //         return serializeContactToken(fieldName, pToken, pField);
    //     case Et_DbSheetField_LastModifiedBy:
    //         return serializeContactToken(fieldName, pToken, pField);
    //     case Et_DbSheetField_Contact:
    //     {
    //         return serializeContactToken(fieldName, pToken, pField);
    //     }
    //     case Et_DbSheetField_Formula:
    //     {
    //         switch (pField->GetValueType())
    //         {
    //         case DbSheet_Fvt_Text:
    //             return serialiseValueStr(fieldName, pToken, pField);
    //         case DbSheet_Fvt_Number:
    //             return serialiseFormulaNumeric(fieldName, pToken, {alg::ETP_VDBL, alg::ETP_VINT}, 
    //                 std::bind(&CommonLogValueSerialiser::_SerialiseNumber, this, std::placeholders::_1, std::placeholders::_2));
    //         case DbSheet_Fvt_Contact:
    //             return serialiseContactTypeFormula(fieldName, pToken, pField);
    //         // datetime格式, 因为公式字段没有处理, 这里也不处理
    //         case DbSheet_Fvt_Date:
    //             if (DbSheet::ContainTimeFormat(pField->GetNumberFormat()))
    //                 return serialiseFormulaFormattedText(fieldName, pToken, __X("yyyy/mm/dd hh:mm:ss"),
    //                     std::bind(&CommonLogValueSerialiser::_SerialiseDateTime, this, std::placeholders::_1, std::placeholders::_2));
    //             else
    //                 return serialiseFormulaFormattedText(fieldName, pToken, __X("yyyy/mm/dd"),
    //                     std::bind(&CommonLogValueSerialiser::_SerialiseDate, this, std::placeholders::_1, std::placeholders::_2));
    //         case DbSheet_Fvt_Time:
    //             return serialiseFormulaFormattedText(fieldName, pToken, __X("hh:mm:ss"), 
    //                 std::bind(&CommonLogValueSerialiser::_SerialiseTime, this, std::placeholders::_1, std::placeholders::_2));
    //         case DbSheet_Fvt_Logic:
    //             return serialiseFormulaNumeric(fieldName, pToken, {alg::ETP_VDBL, alg::ETP_VINT, alg::ETP_VBOOL}, 
    //                 std::bind(&CommonLogValueSerialiser::_SerialiseBool, this, std::placeholders::_1, std::placeholders::_2));
    //         default:
    //             return serialiseText(fieldName, __X(""));
    //         }
    //     }
    //     default:
    //         ASSERT(FALSE);
    //         return E_FAIL;
    // }
    return S_OK;
}

bool isNeedSerialiseForLookup(IDbField* pField)
{
    if (pField->GetType() != Et_DbSheetField_Lookup)
        return false;
    constexpr struct {
        bool operator()(IDbField *pField) const {
            ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
            ks_stdptr<IDbField> spBaseField;
            spField_Lookup->GetLookupBaseField(&spBaseField);
            if(!spBaseField)//base field not exist(eg: base field deleted)
                return false;

            const auto type {spBaseField->GetType()};
            return type == Et_DbSheetField_Formula;
        }
    } isLookingupFormula {};
    ks_stdptr<IDbField_Lookup> spField_Lookup = pField;
    const ET_DbSheet_Lookup_Function lookupFunction = spField_Lookup->GetLookupFunction();
    ks_stdptr<IDbField> spNextLookupBaseField;
    spField_Lookup->GetNextLookupBaseField(&spNextLookupBaseField);
    bool isSerialiseForLookup = false;
    if (spNextLookupBaseField)
    {
        if (spNextLookupBaseField->GetType() == Et_DbSheetField_Lookup)
        {
            ks_stdptr<IDbField> spBaseField;
            spField_Lookup->GetLookupBaseField(&spBaseField);
            if (spBaseField)    
            {
                //引用引用类型，多层嵌套，但不能转换到基础类型，需要转公式结果字段，token单独序列化
                if (spBaseField->GetType() == Et_DbSheetField_Lookup)
                    isSerialiseForLookup = true;
                else if (lookupFunction != DbSheet_Lookup_Function_Origin && lookupFunction != DbSheet_Lookup_Function_Unique)
                    isSerialiseForLookup = true;
            }
        }
        else
        {
            if (lookupFunction != DbSheet_Lookup_Function_Origin && lookupFunction != DbSheet_Lookup_Function_Unique)
            {
                isSerialiseForLookup = true; //引用非引用类型，聚合函数为非原始值/唯一值， 需要转公式结果字段，token单独序列化
            }
        }
    }
    return isSerialiseForLookup || isLookingupFormula(pField);
}
}  // namespace anonymous

std::unordered_set<EtDbId> CommonLogValueSerialiser::GetNoPermissionRecord() const
{
    ks_stdptr<IDBSheetRange> spRange;
    m_spProtectionJudgement->GetNoPermissionRecords(m_pSheet->GetStId(), &spRange);
    const EtDbId* pIds = NULL;
    UINT32 cnt = 0;
    if (spRange)
    {
        spRange->GetRecordIds(&pIds, &cnt);
    }
    return std::unordered_set<EtDbId>(pIds, pIds+cnt);
}

std::unordered_set<EtDbId> CommonLogValueSerialiser::GetNoPermissionField() const
{
    ks_stdptr<IDBSheetRange> spRange;
    m_spProtectionJudgement->GetNoPermissionFields(m_pSheet->GetStId(), &spRange);
    const EtDbId* pIds = NULL;
    UINT32 cnt = 0;
    if (spRange)
    {
        spRange->GetFieldIds(&pIds, &cnt);
    }
    return std::unordered_set<EtDbId>(pIds, pIds+cnt);
}

HRESULT CommonLogValueSerialiser::getSerialiseFieldIds(std::vector<EtDbId>& filedIds)
{
    filedIds.clear();
    std::unordered_set<EtDbId>  noPermissionFieldId = GetNoPermissionField();
    const IDBIds *pFields = m_spDbSheetOp->GetAllFields();
    filedIds.reserve(pFields->Count());
    for (EtDbIdx i = 0, c = pFields->Count(); i < c; ++i)
    {
        EtDbId fieldId = pFields->IdAt(i);
        ASSERT(fieldId != INV_EtDbId);
        if (noPermissionFieldId.find(fieldId) == noPermissionFieldId.end())
            filedIds.emplace_back(fieldId);
    }

    return S_OK;
}

HRESULT CommonLogValueSerialiser::SerialiseFields(Log_sheet* pLogSheet)
{
    pLogSheet->fields = std::make_optional<std::vector<Log_Field>>();
    std::vector<EtDbId> fldIds;
    getSerialiseFieldIds(fldIds);

    for (EtDbIdx fld = 0, cnt = fldIds.size(); fld < cnt; fld++)
    {
        Log_Field logField{};
        EtDbId fieldId = fldIds.at(fld);
        ASSERT(fieldId != INV_EtDbId);
        VS(SerialiseField(&logField, fieldId));
        pLogSheet->fields->emplace_back(std::move(logField));
    }
    return S_OK;
}

HRESULT CommonLogValueSerialiser::serialiseCell(LogCellValue* pCellValue, EtDbId recordId, EtDbId fieldId)
{
    ks_stdptr<IDbField> spField;
    HRESULT hr = m_pFieldManager->GetField(fieldId, &spField);
    if (spField == nullptr)
        return E_DBSHEET_FIELD_NOT_FOUND;

    const_token_ptr pToken = nullptr;
    hr = m_spDbSheetOp->GetValueToken(recordId, fieldId, &pToken);
    if (FAILED(hr))
        return hr;
    if (nullptr == pToken)
        return S_FALSE;

    if (wo::DbSheet::statistic_sheet_helper::needDiscardZeroDoubleToken(spField, pToken))
        return S_FALSE;

    ks_bstr displayText;
    hr = m_spDbSheetOp->GetDisplayString(recordId, fieldId, &displayText);
    if (FAILED(hr))
        return hr;
    pCellValue->text = displayText.c_str();
    if (spField->GetType() == Et_DbSheetField_Url)
    {
        ks_bstr hyperlinkStr;
        m_spDbSheetOp->GetHyperlinkAddress(recordId, fieldId, &hyperlinkStr);
        pCellValue->valueStr = hyperlinkStr.c_str();
    }
    // TODO token
    return S_OK;
}

HRESULT CommonLogValueSerialiser::SerialiseField(Log_Field* pLogField, EtDbId fieldId)
{
    ks_stdptr<IDbField> spField;
    m_pFieldManager->GetField(fieldId, &spField);
    if (spField == nullptr)
        return E_DBSHEET_FIELD_NOT_FOUND;
    serializeFieldInfo(pLogField, spField, m_pBook);
    return S_OK;
}

HRESULT CommonLogValueSerialiser::SerialiseCell(Log_Col* pCol, EtDbId recordId, EtDbId fieldId)
{
    pCol->newValue = std::make_optional<LogCellValue>();
    return serialiseCell(&pCol->newValue.value(), recordId, fieldId);
}

HRESULT CommonLogValueSerialiser::SerialiseRecord(Log_Record* pLogRecord, EtDbId recordId)
{
    const IDBIds *pRecords = m_spDbSheetOp->GetAllRecords();
    if (pRecords->Id2Idx(recordId) == INV_EtDbIdx)
        return E_DBSHEET_RECORD_NOT_FOUND;

    std::vector<EtDbId> fldIds;
    getSerialiseFieldIds(fldIds);
    serialiseRecord(pLogRecord, recordId, fldIds);
    return S_OK;
}

HRESULT CommonLogValueSerialiser::SerialiseRecords(Log_sheet* pLogSheet, const EtDbId* pRecIds, UINT cnt)
{
    if (!pRecIds)
        return E_FAIL;
    pLogSheet->records = std::make_optional<std::vector<Log_Record>>();
    std::vector<EtDbId> fldIds;
    getSerialiseFieldIds(fldIds);

    const IDBIds* pRecords = m_spDbSheetOp->GetAllRecords();
    std::unordered_set<EtDbId>  noPermissionRecord = GetNoPermissionRecord();
    for (size_t i = 0; i < cnt; ++i)
    {
        const EtDbId& recordId = pRecIds[i];
		if (pRecords->Id2Idx(recordId) == INV_EtDbIdx)
            continue;
        if (noPermissionRecord.find(recordId) == noPermissionRecord.end())
        {
            Log_Record logRecord{};
            serialiseRecord(&logRecord, recordId, fldIds);
            pLogSheet->records->emplace_back(std::move(logRecord));
        }
    }
    return S_OK;
}

HRESULT CommonLogValueSerialiser::serialiseRecord(Log_Record* pLogRecord, EtDbId recordId, const std::vector<EtDbId>& fieldIds)
{
    pLogRecord->id = recordId;

    // if (fieldIds.size() > 0)
    // {
    //     pLogRecord->fields = std::make_optional<std::vector<LogCellValue>>();
    //     for (size_t i = 0, c = fieldIds.size(); i < c; ++i)
    //     {
    //         LogCellValue logCellValue{};
    //         VS(serialiseCell(&logCellValue, recordId, fieldIds.at(i)));
    //         pLogRecord->fields->emplace_back(std::move(logCellValue));
    //     }
    // }
    return S_OK;
}

} // namespace wo
