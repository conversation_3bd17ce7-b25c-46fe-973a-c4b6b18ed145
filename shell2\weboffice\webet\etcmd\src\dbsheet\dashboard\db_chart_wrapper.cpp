﻿#include "etstdafx.h"
#include "workbook.h"
#include "db_chart_wrapper.h"
#include "helpers/pivot_tables_helper.h"
#include "dashboard/webchart_config.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "src/et_revision_context_impl.h"
#include "wo/db_id_encode_decode.h"
#include "webbase/wo_sa_helper.h"

namespace wo
{

KDbChartWrapper::KDbChartWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension, IDBChartStatisticModule* pStatisticModule) :
        KDbDashboardModuleWrapper(pEtWorkbook, pWebExtension), m_spStatisticModule(pStatisticModule)
{

}

KDbChartWrapper::KDbChartWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension, IDbWorksheetChart* pWorksheetChart) :
        KDbDashboardModuleWrapper(pEtWorkbook, pWebExtension), m_spWorksheetChart(pWorksheetChart)
{

}

bool KDbChartWrapper::SerializeBaseInfo(ISerialAcceptor* pAcpt)
{
    PCWSTR webExtensionKey = m_spWebExtension->GetWebExtensionKey();
    pAcpt->addString("id", webExtensionKey);
    auto webExtensionType = GetWebExtType();
    PCWSTR typeStr = __X("");
    VS(_webextension_GainEncodeDecoder()->EncodeWebExtType(webExtensionType, &typeStr));
    pAcpt->addString("webExtType", typeStr);

    bool bUsedNewKey = false;
    PCWSTR nameStr = GetProperty(__X("ext-base-name"));
    if (nameStr && *nameStr != __Xc('\0'))
    {
        pAcpt->addString("name", nameStr);
        bUsedNewKey = true;
    }

    PCWSTR configStr = GetConfigStr();
    KWebChartConfig chartConfig(configStr);
    bool validConfig = chartConfig.SerializeBaseInfo(pAcpt, bUsedNewKey);

    PCWSTR dataSourceTypeStr = GetDataSourceTypeStr();
    pAcpt->addString("dataSourceType", dataSourceTypeStr ? dataSourceTypeStr : __X("unknown"));
    Status status = GetStatus();
    bool valid = validConfig && status == Status::Normal;
    pAcpt->addBool("valid", valid);
    return valid;
}

static void addEtDbIdStr(ISerialAcceptor* pAcpt, const char* name, EtDbId id)
{
    EtDbIdStr buf;
    VS(db_id::EncodeEtDbId(id, &buf));
    pAcpt->addString(name, buf);
}

void KDbChartWrapper::SerializeSettings(ISerialAcceptor* pAcpt)
{
    if (!m_spStatisticModule)
        return;
    WebExtensionDataSourceType dataSourceType = GetDataSourceType();
    if (dataSourceType != dst_dbTable)
        return;
    wo::sa::Leave statisticsModule(sa::enterStruct(pAcpt, "statisticsModule"));
    {
        pAcpt->addUint32("dataSourceSheetId", m_spStatisticModule->GetDataSourceId());
        addEtDbIdStr(pAcpt, "moduleId", m_spStatisticModule->GetId());
        addEtDbIdStr(pAcpt, "dimensionFieldId", m_spStatisticModule->GetDimensionId());
        addEtDbIdStr(pAcpt, "groupFieldId", m_spStatisticModule->GetGroupConditionId());
        ET_DBSheet_ChartSortType sortType = m_spStatisticModule->GetSortType();
        pAcpt->addBool("sortByRow", sortType == DbSheet_Chart_ST_SortByHorizontalAxis);
        PCWSTR sortTypeStr = __X("");
        VS(_appcore_GainEncodeDecoder()->EncodeDbChartSortType(sortType, &sortTypeStr));
        pAcpt->addString("sortType", sortTypeStr);
        pAcpt->addBool("ascending", m_spStatisticModule->GetSortAscending());
        pAcpt->addBool("onlyCountDimension", m_spStatisticModule->GetOnlyCountDimension());
    }
}

void KDbChartWrapper::SerializeConfig(ISerialAcceptor* pAcpt)
{
    SerializeConfigChartInfo(pAcpt);
    SerializeConfigXAxis(pAcpt);
    SerializeConfigYAxis(pAcpt);
    SerializeConfigLegend(pAcpt);
    SerializeConfigAllLabel(pAcpt);
    SerializeConfigValueFormat(pAcpt);
}

void KDbChartWrapper::SerializeConfigChartInfo(ISerialAcceptor* pAcpt)
{
    PCWSTR configStr = GetConfigStr();
    KWebChartConfig chartConfig(configStr);
    PCWSTR configLayout = GetProperty(__X("ext-base-layout"));
    chartConfig.SerializeConfig(pAcpt, configLayout);
}

void KDbChartWrapper::SerializeConfigXAxis(ISerialAcceptor* pAcpt)
{
    PCWSTR configStr = GetProperty(__X("xAxisStyle"));
    KWebChartConfig chartConfig(configStr);
    chartConfig.SerializeConfigXAxis(pAcpt);
}

void KDbChartWrapper::SerializeConfigYAxis(ISerialAcceptor* pAcpt)
{
    PCWSTR configStr = GetProperty(__X("yAxisStyle"));
    KWebChartConfig chartConfig(configStr);
    chartConfig.SerializeConfigYAxis(pAcpt);
}

void KDbChartWrapper::SerializeConfigLegend(ISerialAcceptor* pAcpt)
{
    PCWSTR configStr = GetProperty(__X("legendStyle"));
    KWebChartConfig chartConfig(configStr);
    chartConfig.SerializeConfigLegend(pAcpt);
}

void KDbChartWrapper::SerializeConfigAllLabel(ISerialAcceptor* pAcpt)
{
    PCWSTR configStr = GetProperty(__X("allLabelStyle"));
    KWebChartConfig chartConfig(configStr);
    chartConfig.SerializeConfigAllLabel(pAcpt);
}

void KDbChartWrapper::SerializeConfigValueFormat(ISerialAcceptor* pAcpt)
{
    PCWSTR configStr = GetProperty(__X("valueFormatStyle"));
    KWebChartConfig chartConfig(configStr);
    chartConfig.SerializeConfigValueFormat(pAcpt);
}

KDbDashboardModuleWrapper::Status KDbChartWrapper::GetStatus() const
{
    WebExtensionDataSourceType dataSourceType = GetDataSourceType();
    if (dataSourceType == dst_Unknown)
        return Status::NoDataSource;

    if (IsWorksheetChart())
    {
        if (!GetFirstDataRange())
            return Status::NoDataSource;
    }
    else
    {
        if (m_spStatisticModule->GetDataSourceId() == 0)
            return Status::NoDataSource;

        ET_DBSheet_ChartType chartType = GetChartType();
        switch (chartType)
        {
            case DbSheet_Chart_Value:
            case DbSheet_Chart_Progress:
                break;
            case DbSheet_Chart_CountDown:
                return GetCountDownStatus();
            default:
            {
                // 图表必须有维度字段和统计字段，统计数字除外
                EtDbId dimensionFieldId  = m_spStatisticModule->GetDimensionId();
                const IDbChartStatisticOptions* pStatisticOptions = m_spStatisticModule->GetConstStatisticOptions();
                if (dimensionFieldId == INV_EtDbId || !pStatisticOptions || pStatisticOptions->Count() == 0)
                    return Status::InvalidConfig;
            }

        }
    }
    return Status::Normal;
}

HRESULT KDbChartWrapper::SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang,
                                          KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) const
{
    PCWSTR configStr = GetConfigStr();
    if (IsWorksheetChart())
    {
        IKDataRange* pDataRange = GetFirstDataRange();
        if (!pDataRange)
            return E_FAIL;

        HRESULT hr = S_FALSE;
        if (pCtx)
        {
            hr = HasHiddenContent(pDataRange, pCtx);
            if (FAILED(hr))
                return hr;
        }

        if (hr == S_FALSE)
        {
            WebExtensionDataSourceType dataSourceType = GetDataSourceType();
            KWebChartConfig chartConfig(configStr);
            m_spWorksheetChart->SerializeResult(lang, pAcpt, pDataRange, dataSourceType, chartConfig.GetChartType());
        }
        else
        {
            PCWSTR resultTypeStr = __X("");
            VS(_appcore_GainEncodeDecoder()->EncodeDbChartResultType(DbSheet_Chart_RT_DataIsProtected, &resultTypeStr));
            pAcpt->addString("type", resultTypeStr);
        }
    }
    else
    {
        Status status = GetStatus();
        if (status != Status::Normal)
        {
            PCWSTR resultTypeStr = __X("");
            VS(_appcore_GainEncodeDecoder()->EncodeDbChartResultType(DbSheet_Chart_RT_Invalid, &resultTypeStr));
            pAcpt->addString("type", resultTypeStr);
            return S_OK;
        }

        if (param.has("filter"))
        {
            VarObj filter = param.get_s("filter");
            if (binary_wo::typeStruct != filter.type())
                return E_DBSHEET_FILTER_INVALID;

            DBSheetCommonHelper commonHelper(m_pEtWorkbook);
            UINT dataSourceSheetId = m_spStatisticModule->GetDataSourceId();
            ks_stdptr<IDBSheetOp> spDbSheetOp;
            HRESULT hr = commonHelper.GetDBSheetOp(dataSourceSheetId, &spDbSheetOp);
            if (FAILED(hr))
                return hr;

            ks_stdptr<IDBIds> spVisibleRecords;
            hr = m_spStatisticModule->GetVisibleRecords(&spVisibleRecords);
            if (FAILED(hr))
                return hr;

            UINT originalRecordCount = spVisibleRecords->Count();
            std::vector<EtDbId> newVisibleRecords;
            newVisibleRecords.reserve(spVisibleRecords->Count());
            for (EtDbIdx idx = 0, count = spVisibleRecords->Count(); idx < count; ++idx)
                newVisibleRecords.push_back(spVisibleRecords->IdAt(idx));

            ks_stdptr<IDbExtraFilter> spExtraFilter;
            VS(_appcore_CreateObject(CLSID_KDbExtraFilter, IID_IDbExtraFilter, (void**)&spExtraFilter));
            spExtraFilter->Init(spDbSheetOp);
            const IDbExtraFilter* pFailedFilter = nullptr;
            hr = DbSheet::filtrateByExtraFilter(spExtraFilter, filter, commonHelper, spDbSheetOp, true, &pFailedFilter);
            if (FAILED(hr))
                return hr;

            size_t newRecordCount = newVisibleRecords.size();
            if (pCtx)
            {
                IKUserConn* pUser = pCtx->getUser();
                hr = spExtraFilter->FilterVisibleRecords(newVisibleRecords.data(), newRecordCount, pUser);
                if (FAILED(hr))
                    return hr;
            }

            newVisibleRecords.resize(newRecordCount);
            if (newRecordCount == originalRecordCount)
            {
                m_spStatisticModule->SerializeResult(configStr, lang, pAcpt);
            }
            else
            {
                m_spStatisticModule->SerializeNoRtsResult(configStr, newVisibleRecords.data(), newRecordCount, lang, pAcpt);
            }
        }
        else
        {
            m_spStatisticModule->SerializeResult(configStr, lang, pAcpt);
        }
    }
    return S_OK;
}

PCWSTR KDbChartWrapper::GetDataSourceTypeStr()
{
    WebExtensionDataSourceType dataSourceType = GetDataSourceType();
    switch (dataSourceType)
    {
        case dst_dbTable:
            return __X("dbsheet");
        case dst_Workbook:
        case dst_PivotTable:
            return __X("worksheet");
        default:
            return nullptr;
    }
}

bool KDbChartWrapper::IsWorksheetChart() const
{
    return m_spWorksheetChart != nullptr;
}

HRESULT KDbChartWrapper::HasHiddenContent(IKDataRange* pDataRange, KEtRevisionContext* pCtx) const
{
    PCWSTR context = pDataRange->GetContext();
    ASSERT(context != nullptr);
    if (xstrlen(context) && context[0] == __Xc('='))
        ++context;
    range_helper::ranges rgs;
    CS_COMPILE_FLAGS ccf = cpfAllowName | cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo | cpfForbidCrossBook;
    CS_COMPILE_PARAM ccp(ccf, 0, 0, 0);
    IBookOp* pBookOp = m_pEtWorkbook->GetCoreWorkbook()->GetBook()->LeakOperator();
    HRESULT hr = pBookOp->CompileRange(context, ccp, &rgs, croNeedCalc);
    if (FAILED(hr))
        return hr;
    return pCtx->getProtectionCtx()->isRangeHasHidden(rgs, true) ? S_OK : S_FALSE;
}

IKDataRange* KDbChartWrapper::GetFirstDataRange() const
{
    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = m_spWebExtension;
    ks_castptr<IKWorkbookDataSource> spDataSource = spDataSourceHost->GetDataSource();
    if (!spDataSource || spDataSource->GetRangeCount() <= 0)
        return nullptr;

    ks_stdptr<IKDataRangeEnum> spEnum;
    HRESULT hr = spDataSource->GetDataRangeEnumerator(&spEnum);
    if (FAILED(hr) || !spEnum || spEnum->Reset() != S_OK)
        return nullptr;

    return spEnum->GetDataRange();
}

HRESULT KDbChartWrapper::SetStyle(ET_DBSheet_ChartElementType elementType, PCWSTR identifier, PCWSTR style)
{
    IDbChartStyleManager* pStyleManager = GetStyleManager();
    if (!pStyleManager)
        return E_FAIL;
    return pStyleManager->SetStyle(elementType, identifier, style);
}

HRESULT KDbChartWrapper::RemoveStyle(ET_DBSheet_ChartElementType elementType, PCWSTR identifier)
{
    IDbChartStyleManager* pStyleManager = GetStyleManager();
    if (!pStyleManager)
        return E_FAIL;
    return pStyleManager->RemoveStyle(elementType, identifier);
}

HRESULT KDbChartWrapper::GetGroupRecords(const binary_wo::VarObj& categoryGroupKey,
                                         const binary_wo::VarObj& seriesGroupKey, IDBIds** ppRecords)
{
    if (IsWorksheetChart())
        return E_FAIL;

    return m_spStatisticModule->GetGroupRecords(categoryGroupKey, seriesGroupKey, ppRecords);
}

HRESULT KDbChartWrapper::GetVisibleRecords(IDBIds** ppRecords)
{
    if (IsWorksheetChart())
        return E_FAIL;

    return m_spStatisticModule->GetVisibleRecords(ppRecords);
}

UINT KDbChartWrapper::GetDataSourceSheetId()
{
    return IsWorksheetChart() ? 0 : m_spStatisticModule->GetDataSourceId();
}

HRESULT KDbChartWrapper::GetFunnelInfo(UINT& count, PCWSTR& typeStr)
{
    if (IsWorksheetChart())
        return E_FAIL;

    // 判断类型是否是漏斗图
    PCWSTR configStr = GetConfigStr();
    KWebChartConfig chartConfig(configStr);
    if (chartConfig.GetChartType() != DbSheet_Chart_Funnel)
        return E_FAIL;

    // count
    count = m_spStatisticModule->GetCategoryCount();

    // typeStr
    IDbFieldsManager* pFieldsMgr = m_spStatisticModule->GetFieldsManager();
    if (!pFieldsMgr)
        return E_FAIL;

    EtDbId fieldId = m_spStatisticModule->GetDimensionId();
    ks_stdptr<IDbField> spField;
    pFieldsMgr->GetField(fieldId, &spField);
    if (!spField)
        return E_FAIL;

    ET_DbSheet_FieldType type = spField->GetType();
    VS(_appcore_GainEncodeDecoder()->EncodeFieldType(type, &typeStr));
    return S_OK;
}

IDashboardChart* KDbChartWrapper::GetChart()
{
    if (IsWorksheetChart())
        return m_spWorksheetChart;
    return m_spStatisticModule;
}

IDbChartStyleManager* KDbChartWrapper::GetStyleManager()
{
    return GetChart()->GetMutableStyleManager();
}

HRESULT KDbChartWrapper::SetSortType(ET_DBSheet_ChartSortType sortType)
{
    return GetChart()->SetSortType(sortType);
}

HRESULT KDbChartWrapper::SetCustomCategoryOrder(const binary_wo::VarObj& customOrder)
{
    int32 count = customOrder.arrayLength_s();
    if (count <= 0)
        return E_INVALIDARG;

    std::vector<PCWSTR> order = GetCustomOrderVector(customOrder);
    return GetChart()->SetCustomCategoryOrder(order.data(), order.size());
}

HRESULT KDbChartWrapper::SetCustomSeriesOrder(const binary_wo::VarObj& customOrder)
{
    int32 count = customOrder.arrayLength_s();
    if (count <= 0)
        return E_INVALIDARG;

    std::vector<PCWSTR> order = GetCustomOrderVector(customOrder);
    return m_spStatisticModule->SetCustomSeriesOrder(order.data(), order.size());
}

std::vector<PCWSTR> KDbChartWrapper::GetCustomOrderVector(const binary_wo::VarObj& customOrder)
{
    int32 count = customOrder.arrayLength_s();
    std::vector<PCWSTR> order;
    order.reserve(count);
    for (int32 i = 0; i < count; ++i)
    {
        PCWSTR item = customOrder.item_str(i);
        order.emplace_back(item);
    }
    return order;
}

HRESULT KDbChartWrapper::SetSeriesSortType(ET_DBSheet_ChartSortType seriesSortType)
{
    return m_spStatisticModule->SetSeriesSortType(seriesSortType);
}

HRESULT KDbChartWrapper::SetSortAscending(bool ascending)
{
    return GetChart()->SetSortAscending(ascending);
}

ET_DBSheet_ChartType KDbChartWrapper::GetChartType() const
{
    PCWSTR configStr = GetConfigStr();
    KWebChartConfig chartConfig(configStr);
    return chartConfig.GetChartType();
}

KDbDashboardModuleWrapper::Status KDbChartWrapper::GetCountDownStatus() const
{
    std::string configStr = krt::fromUtf16(GetConfigStr()).toStdString();

    if (!configStr.empty())
    {
        Json::Value root;
        if (Json::Reader().parse(configStr, root) && root.isObject())
        {
            const Json::Value& boolValue = root["isCustomCount"];
            if (boolValue.isBool() && boolValue.asBool())
                return Status::Normal;
        }
    }

    const IDbChartStatisticOptions* pStatisticOptions = m_spStatisticModule->GetConstStatisticOptions();
    if (!pStatisticOptions || pStatisticOptions->Count() == 0)
        return Status::InvalidConfig;
    return Status::Normal;
}

}