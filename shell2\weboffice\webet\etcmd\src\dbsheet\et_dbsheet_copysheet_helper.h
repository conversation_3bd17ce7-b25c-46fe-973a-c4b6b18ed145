﻿#ifndef __ET_DBSHEET_COPYSHEET_HELPER_H__
#define __ET_DBSHEET_COPYSHEET_HELPER_H__

#include "wo/et_shared_str.h"
#include "etcore/et_core_dbsheet_enum.h"
#include "helpers/varobject_helper.h"

namespace wo
{

namespace DBSyncUtil
{
	EtDbId decodeRecordId(PCWSTR str);
	void encodeToBase64(QString& str);
}

struct DbSheetCopyParam
{
	using AttachmentIdMap = std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher>;
	bool copyContent = true;
	bool copyFieldTitleFormat = true;
	bool copyFormulaCustom = false;
	bool copyDefaultView = false; // 是否拷贝默认表格视图
	UINT recordCnt = 0; // m_copyContent 为 false 时，有效
	bool needAutoAddItem = false;
	bool needResetAutoValue = true;
	bool needValidateName = false;
	bool resetCreationInfoByCurUser = true;
	bool resetModifiedInfoByCurUser = false;
	PCWSTR biLinkPrefix = __X("");
	std::vector<bool> clearType;
	const std::unordered_map<UINT, UINT>* pStIdMap = nullptr; // 源表及其副本的sheetStId映射表
	const AttachmentIdMap* pAttachmentIdMap = nullptr;
	// 当表不在pStIdMap集合中，允许非法值
	bool allowInvalidValueWhenSheetNotInStIdMap = false;

	DbSheetCopyParam()
		: clearType(ET_DbSheet_FieldType_Count, false)
	{
	}
};

HRESULT GetNewAttachmentToken(const wo::DbSheetCopyParam::AttachmentIdMap*, const_token_ptr, IDbTokenArrayHandle**);
HRESULT GetNewNoteToken(const wo::DbSheetCopyParam::AttachmentIdMap*, const_token_ptr, IDbNoteHandle**);
HRESULT UpdateAttachmentByMapping(EtDbId, IDBSheetOp*, const DbSheetCopyParam::AttachmentIdMap*, bool& bLostInfo);
HRESULT UpdateNoteByMapping(EtDbId, IDBSheetOp*, const DbSheetCopyParam::AttachmentIdMap*, bool& bLostInfo);

// CopyDBSheetHelper 只能处理sheet类部数据拷贝，无法处理跨表引用数据，例如单项关联/双向关联等
// 此类功能：在所有sheet拷贝完成后，重新建立跨表引用关联关系
class CopyDBSheetDealCrossSheetDependenceHelper
{
public:
	CopyDBSheetDealCrossSheetDependenceHelper();
	HRESULT AddCopyDBSheet(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet);
	HRESULT DealCrossSheetDependence(const std::unordered_map<UINT, UINT>* pStIdMap);
	
private:
	std::unordered_map<etoldapi::_Worksheet*, etoldapi::_Worksheet*> m_dbMap;
};

class CopyDBSheetHelper
{
public:
	CopyDBSheetHelper(etoldapi::_Worksheet*, etoldapi::_Worksheet*, CopyDBSheetDealCrossSheetDependenceHelper*, bool bNewLookupConvert = false);
	virtual ~CopyDBSheetHelper() = default;

	HRESULT Init(const DbSheetCopyParam&);
	virtual HRESULT ExecCopy();
	bool GetLostNoteFlag() const { return m_lostNoteFlag; }
protected:
	HRESULT ResetTableName() const;
	HRESULT CopyBookCtxId() const;
	virtual HRESULT CopyDbSheetData();
	HRESULT CopyDbSheetViews() const;
	virtual HRESULT CopySheetProperty();
	virtual HRESULT CopyDbSheetRecords();
	HRESULT CopyDbSheetRecordsRelation();
	HRESULT CopyDbSheetFields();
	HRESULT CopyDbSheetField(IDbField*, IDbField*);
	HRESULT CopyDbSheetContactExtendField(IDbField*, IDbField*);
    void copyOneWayLinkField(IDbField* pField, IDbField* pNewField) const;
	void copyLookupLinkField(IDbField* pField, IDbField* pNewField) const;
	HRESULT CopyDbSheetRecordModifiedInfo() const;
	HRESULT SetDbSheetRecordModifiedInfo() const;
	virtual HRESULT onCopyFieldBaseInfo(IDbField*, IDbField*);
	virtual HRESULT AdjustFieldData(IDbField*, IDbField*);
	HRESULT copyLinkCustomConfig(IDbField_Link*, IDbField_Link*) const;

	static void copyAutoLinkProp(IAutoLinkCond* pSrcField, IAutoLinkCond* pNewField);
	void AdjustSheetStId(IDbField* pField, IDbField* pNewField);
	HRESULT postProcess();
	void postProcess_statisticSheet();

	etoldapi::_Worksheet* m_pSrcWorkSheet = nullptr;
	etoldapi::_Worksheet* m_pTarWorkSheet = nullptr;
	ISheet* m_pSrcSheet = nullptr;
	ISheet* m_pTarSheet = nullptr;
	IBook* m_pSrcBook = nullptr;
	IBook* m_pTarBook = nullptr;
	ks_stdptr<IDBSheetOp> m_spSrcDbSheetOp;
	ks_stdptr<IDBSheetOp> m_spTarDbSheetOp;
	bool m_bNewLookupConvert = false;
private:
	bool m_lostNoteFlag = false;
	DbSheetCopyParam m_param;
};

struct DBSyncParam
{
	bool needCopySheetInfo = false;
	PCWSTR fldSourceName = nullptr;
	PCWSTR urlTemplate = nullptr;
	const std::unordered_map<EtDbId, EtDbId>* pOldFieldMap = nullptr; // srcFieldId: tarFieldId
	ks_wstring* pTimeStatInfo = nullptr;	//暂时放这里收集耗时信息，后面可以去掉
};

class DBSyncHelper : public CopyDBSheetHelper
{
public:
	DBSyncHelper(etoldapi::_Worksheet*, etoldapi::_Worksheet*, bool bNewLookupConvert = false);

	HRESULT Init(const DBSyncParam&);
	HRESULT ExecCopy() override;
	EtDbId getFldSourceId() const { return m_fldSourceId; }
	std::unordered_map<EtDbId, EtDbId> getFldMap() const { return m_fldMap; }
	static bool isValidType(ET_DbSheet_FieldType);
private:
	HRESULT CopySheetProperty() override;
	HRESULT CopyDbSheetData() override;
	HRESULT CopyDbSheetFieldsProperty();
	HRESULT onCopyFieldBaseInfo(IDbField*, IDbField*) override;
	HRESULT onCopyFieldBaseInfo_impl(IDbField*, IDbField*);
	HRESULT AdjustFieldData(IDbField*, IDbField*) override;
	HRESULT adjustFields();
	HRESULT adjustRecords();
	HRESULT setInfoField();

	DBSyncParam m_param;
	mutable EtDbId m_fldSourceId = INV_EtDbId;
	mutable std::unordered_map<EtDbId, EtDbId> m_recMap;
	mutable std::unordered_map<EtDbId, EtDbId> m_fldMap;
};

class CopyDBDashboardHelper
{
public:
	HRESULT Init(IKWorksheet*, IKWorksheet*);
	HRESULT Prepare4Template(std::unordered_map<UINT, UINT>*);
	HRESULT ExecCopy();
private:
    HRESULT CopyStatisticModules(ISheet* pSheet, ISheet* pNewSheet);
    HRESULT CopyWorksheetCharts(IDBDashBoardDataOp* pSrcDashboardData, IDBDashBoardDataOp* pDstDashboardData);
	HRESULT CopyViews();
    HRESULT CopyFilters(IDBDashBoardDataOp* pSrcDashboardData, IDBDashBoardDataOp* pDstDashboardData);
	HRESULT GetValidName(IDBSheetViews* pViews, PCWSTR pBaseName, ks_wstring& ppValidName);
private:
	IKWorksheet* m_pWorkSheet = nullptr;
	IKWorksheet* m_pNewWorkSheet = nullptr;
	const std::unordered_map<UINT, UINT>* m_pSheetIdMap = nullptr;
};

class CopyDBFpSheetHelper
{
	using AttachmentIdMap = std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher>;
public:
	HRESULT Init(IKWorksheet*, IKWorksheet*);
	void SetAttachmentIdMap(const AttachmentIdMap* pAttachmentIdMap) { m_pAttachmentIdMap = pAttachmentIdMap; }
	HRESULT ExecCopy();
private:
	IKWorksheet* m_pWorkSheet = nullptr;
	IKWorksheet* m_pNewWorkSheet = nullptr;
	const AttachmentIdMap* m_pAttachmentIdMap = nullptr;
};

struct FieldMappingItem
{
	UINT m_uSheetStId = 0;
	std::unordered_map<EtDbId, EtDbId> m_fieldIdMap;

	FieldMappingItem(UINT uSheetStId) : m_uSheetStId(uSheetStId) {}
};

struct FieldBaseInfo 
{
	ET_DbSheet_FieldType m_fieldType;
	ks_wstring m_strName;
	ks_wstring m_strCurrencySymbol;
	BOOL m_bIsArray;

	FieldBaseInfo(ET_DbSheet_FieldType fieldType, PCWSTR pcwName, PCWSTR pcwCurrencySymbol, BOOL bIsArray)
		: m_fieldType(fieldType)
		, m_strName(pcwName)
		, m_strCurrencySymbol(pcwCurrencySymbol)
		, m_bIsArray(bIsArray)
		{}

	bool operator<(const FieldBaseInfo& stFiledBaseInfo) const
	{
		if (m_fieldType != stFiledBaseInfo.m_fieldType)
			return m_fieldType < stFiledBaseInfo.m_fieldType;
		if (m_strName != stFiledBaseInfo.m_strName)
			return m_strName < stFiledBaseInfo.m_strName;
		if (m_bIsArray != stFiledBaseInfo.m_bIsArray)
			return m_bIsArray < stFiledBaseInfo.m_bIsArray;
		return m_strCurrencySymbol < stFiledBaseInfo.m_strCurrencySymbol;
	}
};
constexpr int MAX_ROW_COUNT = 100 * 10000;
class DBSyncMergeHelper : public CopyDBSheetHelper
{
public:
	struct DBSyncMergeParam
	{
		bool bNeedCopySheetInfo = false;
		PCWSTR pcwFldSourceName = nullptr;
		PCWSTR pcwUrlTemplate = nullptr;
		PCWSTR pcwSheetName = nullptr;
		int m_iMaxSheetRows = MAX_ROW_COUNT;
		bool m_bNeedCopySheetData = true;
		PCWSTR pcwFldSourceFileName = nullptr;
		PCWSTR m_pcwKSheetUrlFileType = __X("k");
		PCWSTR m_pcwDbSheetUrlFileType = __X("d");
	};

	DBSyncMergeHelper(etoldapi::_Worksheet*, etoldapi::_Worksheet*, bool);

	HRESULT Init(const DBSyncMergeParam&);
	HRESULT PrepareMerge();
	HRESULT ExecMerge();
	HRESULT AdjustMerge();
	EtDbId GetFldSourceId() const { return m_sourceFldId; }
	EtDbId GetFldSourceNameId() const { return m_sourceNameFldId; }
	void MoveFieldMappingItems(std::vector<FieldMappingItem>& stFieldMappingItemsVec);
	void SetSrcWorkSheetsVec(const std::vector<IKWorksheet*> &srcWorkSheetsVec)
	{
		m_srcWorksheetsVec = srcWorkSheetsVec;
	}
	void SetSrcWorkSheet(etoldapi::_Worksheet* pSrcWorkSheet)
	{
		m_pSrcWorkSheet = pSrcWorkSheet;
	}
	void SetCurFileInfo(PCWSTR pcwFileId, PCWSTR pcwFileName)
	{
		m_pcwCurSourceFileId = pcwFileId;
		m_pcwCurSourceFileName = pcwFileName;
	}
	void SetSrcWorkbook(IBook* pBook)
	{
		m_pSrcBook = pBook;
	}
	void SetFieldMappingItems(std::vector<FieldMappingItem>& stFieldMappingItemsVec)
	{
		m_stOldFieldMapping.clear();
		for (int i = 0, size = stFieldMappingItemsVec.size(); i < size; i++)
		{
			m_stOldFieldMapping[stFieldMappingItemsVec[i].m_uSheetStId] = &stFieldMappingItemsVec[i];
		}
	}
private:
	struct RecordInfo
	{
		EtDbId m_recId;
		UINT32 m_uStId;
		PCWSTR m_pcwFileId;
		PCWSTR m_pcwFileName;
		PCWSTR m_pcwSheetName;
		PCWSTR m_pcwFileType;

		bool operator<(const RecordInfo& stRecordInfo) const
		{
			if (m_recId != stRecordInfo.m_recId)
				return m_recId < stRecordInfo.m_recId;
			if (m_uStId != stRecordInfo.m_uStId)
				return m_uStId < stRecordInfo.m_uStId;
			return xstrcmp(m_pcwFileId, stRecordInfo.m_pcwFileId) < 0;
		}
	};
	HRESULT copyRecords(IDBSheetOp* pSrcDbSheetOp, IDbField* pSrcField, IDbField* pTarField, UINT32 uStId);
    // 根据匹配规则调整字段类型
	HRESULT adjustFieldBaseInfo(IDbField*, IDbField*, bool);
	HRESULT adjustFieldExInfo(IDbField*, IDbField*, bool);
	bool checkCanReuseCurField(IDbField* pField, EtDbId& tarFieldId);
	void addOriginFieldInfoMapping(EtDbId tarFieldId, IDbField* pField);
	HRESULT removeUnusedField();
	HRESULT addSourceInfoField();
	HRESULT addSourceFileNameField();
	PCWSTR addHoldName(PCWSTR);
private:
	struct CopyFieldDataInfo
	{
		EtDbId m_srcFldId = INV_EtDbId; 
		EtDbId m_srcRecId = INV_EtDbId; 
		EtDbId m_tarFldId = INV_EtDbId; 
		EtDbId m_tarRecId = INV_EtDbId;
		bool m_bIsArray = false;
	};
	struct FieldValueTypeInfo
	{
		Et_DbSheet_Field_Value_Type m_fieldType;
		ks_wstring m_strName;
		NumFmtCat m_enmNumFmtCat;
		ks_wstring m_strCurrencySymbol;

		FieldValueTypeInfo(Et_DbSheet_Field_Value_Type fieldType, PCWSTR pcwName, PCWSTR pcwCurrencySymbol, NumFmtCat enmNumFmtCat)
			: m_fieldType(fieldType)
			, m_strName(pcwName)
			, m_strCurrencySymbol(pcwCurrencySymbol)
			, m_enmNumFmtCat(enmNumFmtCat)
			{}

		bool operator<(const FieldValueTypeInfo& stFormulaFiledInfo) const
		{
			if (m_fieldType != stFormulaFiledInfo.m_fieldType)
				return m_fieldType < stFormulaFiledInfo.m_fieldType;
			if (m_strName != stFormulaFiledInfo.m_strName)
				return m_strName < stFormulaFiledInfo.m_strName;
			if (m_strCurrencySymbol != stFormulaFiledInfo.m_strCurrencySymbol)
				return m_strCurrencySymbol < stFormulaFiledInfo.m_strCurrencySymbol;
			return m_enmNumFmtCat < stFormulaFiledInfo.m_enmNumFmtCat;
		}
	};

	struct LookupFieldTypeInfo
	{
		Et_DbSheet_Field_Value_Type m_fieldValueType;
		ET_DbSheet_FieldType m_fieldType;

		LookupFieldTypeInfo(ET_DbSheet_FieldType fieldType, Et_DbSheet_Field_Value_Type fieldValueType)
			: m_fieldType(fieldType)
			, m_fieldValueType(fieldValueType)
			{}

		bool operator<(const LookupFieldTypeInfo& stLookupFieldTypeInfo) const
		{
			if (m_fieldType != stLookupFieldTypeInfo.m_fieldType)
				return m_fieldType < stLookupFieldTypeInfo.m_fieldType;
			return m_fieldValueType < stLookupFieldTypeInfo.m_fieldValueType;
		}
	};
	STDPROC copyRecordDetail(IDBSheetOp* pSrcDbSheetOp, ET_DbSheet_FieldType enmFieldType, const CopyFieldDataInfo& stInfo);
	HRESULT copyDefaultRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo);
	HRESULT copyLinkRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo);
	HRESULT copyUrlRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo);
	HRESULT copyEmailRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo);
	HRESULT copyAutoNumberRecordDetail(IDBSheetOp* pSrcDbSheetOp, const CopyFieldDataInfo& stInfo);

	HRESULT adjustSelectFieldExDetail(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust);
	HRESULT adjustRatingFieldExDetail(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust);
	HRESULT adjustContactFieldExDetail(IDbField* pSrcField, IDbField* pTarField);
	HRESULT adjustAddressFieldExDetail(IDbField* pSrcField, IDbField* pTarField);
	HRESULT adjustDateFieldExDetail(IDbField* pSrcField, IDbField* pTarField);
	HRESULT adjustFormulaResultFieldExDetail(IDbField* pSrcField, IDbField* pTarField);
	void adjustLookupNumFormat(IDbField* pSrcField, IDbField* pTarField);
	void adjustFieldTypeByLookupFunction(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust);
	HRESULT adjustLookupFieldExDetail(IDbField* pSrcField, IDbField* pTarField, bool bIsFirstAdjust);
	HRESULT adjustCascadeFieldExDetail(IDbField* pSrcField, IDbField* pTarField);
	HRESULT adjustDepartmentFieldExDetail(IDbField* pSrcField, IDbField* pTarField);
	bool decodeSrcRecordInfo(PCWSTR str, RecordInfo& stSrcRecordInfo);
	HRESULT ClearOldField();
	HRESULT fillOldRecordMap();
	HRESULT adjustRecords(IDBSheetOp* pSrcDbSheetOp, uint32 uStId);
	HRESULT adjustViews();

	bool findFromFieldBaseInfoMap(const std::map<FieldBaseInfo, EtDbId>& stFieldBaseInfoMap,
                                  const FieldBaseInfo& stFieldBaseInfo, EtDbId& tarFieldId);
	bool findFromFormulaFieldInfoMap(const std::map<FieldValueTypeInfo, EtDbId>& stFormulaFieldInfoMap,
                                     const FieldValueTypeInfo& stFieldBaseInfo, EtDbId& tarFieldId);
	bool findFromOldFieldMapping(UINT uSheetStId, EtDbId srcFldId, EtDbId& tarFldId);
	HRESULT createField(EtDbId& fldId);
	HRESULT adjustSourceField();
	HRESULT adjustSourceNameField();
	void fillOldFormulaInfo(IDbField* pField, EtDbId fldId);
	// 合并数字格式时，保留最大精度
	void adjustNumFormat(IDbField* pSrcField, IDbField* pTarField);
	bool compareNumformatPrecision(IDbField* pSrcField, IDbField* pTarField);
	void getCurrencySymbol(IDbField* pField, ks_wstring& strCurrencySymbol);
	// 公式字段要根据NumFmtCat类型来合并
	NumFmtCat getNumFmtCat(PCWSTR pcwNumberFormat);
	void adjustDateTimeNumFormat(IDbField* pSrcField, IDbField* pTarField);
	bool isNeedOldFormat(Et_DbSheet_Field_Value_Type enmValueType);
	HRESULT copyBaseDataTo(IDbField* pSrcField, IDbField* pTarField);
private:
	DBSyncMergeParam m_param;
	mutable EtDbId m_sourceFldId = INV_EtDbId;
	mutable EtDbId m_sourceNameFldId = INV_EtDbId;
	mutable std::vector<IKWorksheet*> m_srcWorksheetsVec;
	std::unordered_map<EtDbId, RecordInfo> m_stRecordMap;
	std::map<RecordInfo, EtDbId> m_stOldRecordMap;
	std::unordered_set<EtDbId> m_stHoldFieldIdSet;
	std::map<FieldBaseInfo, EtDbId> m_stOriginFieldBaseInfoMapping;
	typedef std::map<FieldBaseInfo, std::map<LookupFieldTypeInfo, EtDbId>> LookupFieldMapping;
	LookupFieldMapping m_stLookupFieldMapping;
	std::vector<FieldMappingItem> m_stFieldMappingItemsVec;
	EtDbIdx m_curRecCnt;
	PCWSTR m_pcwCurSourceFileId = nullptr;
	PCWSTR m_pcwCurSourceFileName = nullptr;
	PCWSTR m_pcwCurSourceSheetName = nullptr;
	std::vector<ks_wstring> m_stOldFileIdVec;
	std::map<ks_wstring, int> m_stOldFileIdxMap;
	std::set<ks_wstring> m_stHoldNameSet;
	std::map<FieldValueTypeInfo, EtDbId> m_stFormulaValueTypeMap;
	ks_stdptr<IET_NumberFormatter> m_spNumberFormatter;
	std::unordered_set<EtDbId> m_stNewFieldIdSet;
	std::map<UINT, FieldMappingItem*> m_stOldFieldMapping;
	const int MIN_FIELD_WIDTH = 1572;
};

class DBAddMergeSyncHelper
{
public:
	struct DBAddMergeSyncParam
	{
		PCWSTR pcwFldSourceName = nullptr;
		PCWSTR pcwSheetName = nullptr;
		std::map<ks_wstring, binary_wo::VarObj*>* m_pSrcFileIdMap;
		PCWSTR pcwFldSourceFileName = nullptr;
	};

	DBAddMergeSyncHelper(etoldapi::_Worksheet*);

	HRESULT Init(const DBAddMergeSyncParam&);
	HRESULT PrepareMerge();
	HRESULT ExecMerge();
	HRESULT AdjustMerge();
	EtDbId GetFldSourceId() const { return m_fldSourceId; }
	EtDbId GetFldSourceNameId() const { return m_fldSourceNameId; }
	void MoveFieldMappingItems(std::map<ks_wstring, std::vector<FieldMappingItem>>& stFieldMappingItemsVec);
private:
	bool checkCanReuseOldField(ET_DbSheet_FieldType type, PCWSTR pcwFieldName, EtDbId& tarFieldId);
	void addOriginFieldInfoMapping(EtDbId tarFieldId, ET_DbSheet_FieldType type, PCWSTR pcwFieldName);
	HRESULT removeUnusedField();
private:
	DBAddMergeSyncParam m_param;
	mutable EtDbId m_fldSourceId = INV_EtDbId;
	mutable EtDbId m_fldSourceNameId = INV_EtDbId;
	std::unordered_set<EtDbId> m_stHoldFieldIdSet;
	std::map<FieldBaseInfo, EtDbId> m_stOriginFieldBaseInfoMapping;
	std::map<ks_wstring, std::vector<FieldMappingItem>> m_stFieldMappingItemsMap;
	etoldapi::_Worksheet* m_pTarWorkSheet = nullptr;
	ks_stdptr<IDBSheetOp> m_spTarDbSheetOp;
};
} // namespace wo

#endif
