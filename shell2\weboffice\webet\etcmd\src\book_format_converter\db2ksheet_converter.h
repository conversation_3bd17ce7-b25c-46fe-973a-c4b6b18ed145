﻿#ifndef __WEBET_DB2KSHEET_CONVERTER_H__
#define __WEBET_DB2KSHEET_CONVERTER_H__

#include "et_revision_context_impl.h"

interface IDBProtectionJudgement;

namespace wo
{
	class Db2KsBookConverter
	{
		_Workbook* m_pDbWb = nullptr;
		PCWSTR m_userId;
		IKWorksheets* m_pDbSheets = nullptr;
		ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
		KEtRevisionContext* m_pCtx;
		bool m_clearDefaultValue{true};
		std::set<ks_wstring> m_unsupportedAttachmentFileSuffixTypes{};
		static void convertUnsupportedDbFields(IDBSheetOp* pSheetOp);
		static void clearDefaultValue(IDBSheetOp* pSheetOp);
		//这里提供一个的通用的方法，清除dbsheet上支持的字段属性，ksheet上却暂时不支持的字段属性
		static void clearFieldPropNotSupportInKsBook(IDBSheetOp* pSheetOp);
		HRESULT onBeforeExport();
		void collectDbAttachmentId(IDBSheetOp* pSheetOp);
		HRESULT resetListobjectName(ISheet* pSheet);
		HRESULT copyFieldTitleFormatToDefaultView(ISheet* pSheet);
		HRESULT clearFieldTitleFormat(ISheet* pSheet);
		HRESULT handleUnsupportedDbViews(ISheet* pSheet);
        HRESULT handleDbSheet(ISheet* pSheet);
        HRESULT handleDbDashboardSheet(_Worksheet* pWorksheet);
	public:
		explicit Db2KsBookConverter(_Workbook*, PCWSTR, IDBProtectionJudgement*, KEtRevisionContext*);
		~Db2KsBookConverter();
		void SetUnsupportedAttachmentFileSuffixTypes(const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes);
		void setClearDefaultValue(bool clearDefaultValue);
		HRESULT Init();
		HRESULT Exec();
	};
} // namespace wo

#endif
