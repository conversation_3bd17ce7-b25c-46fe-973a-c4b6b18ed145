﻿#include "db_auth_query_cohistories.h"
#include "webdeclare.h"
#include "webbase/binvariant/binreader.h"
#include "qstring.h"
#include "workbook.h"

using namespace DbAuthQueryCoHistories;

HandlerContext::HandlerContext(wo::KEtWorkbook* workbook)
    : m_pDbCtx(_appcore_GainDbSheetContext()), m_common<PERSON>elper(workbook)
{
    IBook* pBook = workbook->GetCoreWorkbook()->GetBook();
    ks_stdptr<IUnknown> spUnknown;
    VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
    ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
    spDBUserGroups->GetJudgement(&m_spProtectionJudgement);
}

// ######################### LineCohistoriesHandler ############################
WebInt LineCohistoriesHandler::<PERSON><PERSON>(const VarObj& param)
{
    ks_stdptr<IDBProtectionJudgement> spProtectionJudgement = getContext()->m_spProtectionJudgement;

    if (!spProtectionJudgement)
        return WebDbCoHistoriesPermission::WoCoHistoriesDeny;

    if(!spProtectionJudgement->HasHiddenData())
        return WebDbCoHistoriesPermission::WoCoHistoriesAllow;

    UINT sheetStId = param.field_uint32("sheetStId");
    EtDbId recordId = getContext()->GetEtDbId(param, "recordId");
    HRESULT hr = spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if (FAILED(hr))
        return WebDbCoHistoriesPermission::WoCoHistoriesDeny;

    if (S_OK != spProtectionJudgement->CheckRecordCanEdit(sheetStId, recordId))
        return WebDbCoHistoriesPermission::WoCoHistoriesDeny;
    
    return WebDbCoHistoriesPermission::WoCoHistoriesAllow;
}

// ############################# HandlerFactory ################################
HandlerFactory::~HandlerFactory()
{
    for (auto handler: m_handlers)
    {
        if (handler.second)
            delete handler.second;
    }
    m_handlers.clear();
}

void HandlerFactory::InitDefault(wo::KEtWorkbook* workbook)
{
    Register(new LineCohistoriesHandler(workbook));
}

void HandlerFactory::Register(HandlerBase* handler)
{
    std::string typeName = handler->TypeName();
    if (m_handlers.find(typeName) != m_handlers.end())
        delete m_handlers[typeName];
    m_handlers[typeName] = handler;
}

WebInt HandlerFactory::Handle(const VarObj& root)
{
    if (!root.has("type") || !root.has("data"))
        return WebDbCoHistoriesPermission::WoCoHistoriesDeny;

    WebStr u16Type = root.field_str("type");
    std::string type = QString::fromUtf16(u16Type).toStdString();
    VarObj data = root.get_s("data");

    auto handler = m_handlers.find(type);
    if (handler == m_handlers.end())
        return WebDbCoHistoriesPermission::WoCoHistoriesDeny;

    return handler->second->Handle(data);
}