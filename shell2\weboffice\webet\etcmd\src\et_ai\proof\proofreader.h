﻿#ifndef __ETAI_PROOF_PROOFREADER_H__
#define __ETAI_PROOF_PROOFREADER_H__
namespace etai
{
class Correcte;
class CorrecteData;
class CellErrorBase;
class CellErrorList;
class CorrecteData;
class CellError;
class ColumnError;

class Corrector
{
  public:
    Corrector(IKWorkbook *workbook, IKWorksheet *worksheet, const Correcte &correcte, const QString &fmt,
              const QVariant &val, int correctType);
    ~Corrector();

  public:
    bool dataHasBeenEdited() const;
    bool run();

  public:
    static bool checkRange(Range *range);
    static bool correctOneKeyFmt(const CorrecteData &recognizeData, const QVariantMap &args);
    static bool dataHasBeenEdited(const CellErrorBase &cell);
    static bool dataHasBeenEdited(const CellErrorList &cellList);
    static bool dataHasBeenEdited(const CorrecteData &recognizeData);
    static bool setCellFmt(const CellError &cell, const ks_bstr &fmt);
    static bool setCellFmt(const CellError &cell, const QString& wval, const ks_bstr &fmt);

  private:
    bool runFmt();
    bool runCont();
    void calcCellCount(ColumnError *columnError, const CellError &cellError);

    static bool dataHasBeenEdited(const Correcte &correcte);
    static void saveOldValue(CellErrorBase *cell);

  private:
    IKWorkbook *m_workbook;
    IKWorksheet *m_worksheet;
    const Correcte &m_correcte;
    const QString m_fmt;
    const QVariant m_val;
    const int m_correctType;
    QString m_content;
};

bool Correct(const Correcte& correct);

} // namespace etai
#endif