﻿#include "etstdafx.h"
#include "et_revision_context_impl.h"
#include "kfc/et_numfmt_str.h"
#include "col_operator_helper.h"
#include "binvariant/binvarobj.h"
#include "webdef.h"
#include "workbook.h"
#include "util.h"

namespace wo
{
namespace ColOperatorHelper
{
    HRESULT FillNumberColData(KEtWorkbook* pEtWb, const binary_wo::VarObj& param, KEtRevisionContext* pCtx, const RANGE& addRange)
    {
        IBook* pBook = pEtWb->GetCoreWorkbook()->GetBook();
        IKWorksheet* pWorkSheet = pEtWb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(addRange.SheetFrom());
        if (!pWorkSheet)
            return E_INVALIDARG;

        // 插入列有可能会复制前一列的格式和数据有效性，会影响编号列的使用
        HRESULT hr = util::ClearRangeFormatsAndValidation(pEtWb, addRange);
        if (FAILED(hr))
            return hr;

        ROW headerRow = param.field_int32("headerRow");
        if (headerRow < 0 || headerRow >= pBook->GetBMP()->cntRows - 1)
            return E_INVALIDARG;
        // 填充列名称和编号到插入的新列，行范围从表头所在行开始，最后一个非空行结束
        ROW lastNonEmptyRow = util::FindLastNonEmptyRow(pCtx, headerRow, pWorkSheet);
        ROW rowTo = lastNonEmptyRow == INVALID_ROW ? headerRow + 10 : lastNonEmptyRow;
        constexpr int maxFillRows = 50000;
        if (rowTo > headerRow + maxFillRows)
            rowTo = headerRow + maxFillRows;
        if (rowTo > pBook->GetBMP()->cntRows - 1)
            rowTo = pBook->GetBMP()->cntRows - 1;
        ASSERT(rowTo > headerRow);
        RANGE range(pBook->GetBMP());
        range.SetSheetFromTo(addRange.SheetFrom());
        range.SetColFromTo(addRange.ColFrom());
        range.SetRowFromTo(headerRow, rowTo);
        auto accessPerm = PTAAP_None;
        if (!pCtx->getProtectionCtx()->isAllowEdit(range, &accessPerm))
        {
            if (accessPerm == PTAAP_Invisible)
                return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
            else if (accessPerm == PTAAP_Exclusive)
				return E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE;

            ASSERT(accessPerm == PTAAP_Visible);
            return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;
        }
        range.SetRowFrom(headerRow + 1);
        bool hasNumberFormatParam = param.has("numberFormat");
        if (hasNumberFormatParam)
        {
            ks_bstr numberFormat(param.field_str("numberFormat"));
            hr = util::SetNumberFormat(pEtWb, range, numberFormat);
        }
        else
        {
            hr = util::SetNumberFormat(pEtWb, range, Et_BUILDIN_NF_TEXT1);
        }
        if (FAILED(hr))
            return hr;

        PCWSTR colName = param.has("colName") ? param.field_str("colName") : nullptr;
        if (colName)
        {
            range.SetRowFromTo(headerRow);

            hr = pCtx->getProtectionCtx()->checkFormula(range, colName);
            if (hr != S_OK)
                return hr;

            ks_bstr bsFmla(colName);
            ks_stdptr<IRangeInfo> spHeaderRange = pEtWb->CreateRangeObj(range);
            if (!spHeaderRange)
                return E_FAIL;

            hr = spHeaderRange->SetFormula(colName, &range);
            if (FAILED(hr))
                return hr;
        }

        for (ROW row = headerRow + 1; row <= rowTo; ++row)
        {
            range.SetRowFromTo(row);
            int number = row - headerRow;
            QString numberStr = hasNumberFormatParam ? QString::number(number) : QString("%1").arg(number, 6, 10, QChar('0'));
            ks_stdptr<IRangeInfo> spNumberRange = pEtWb->CreateRangeObj(range);
            if (!spNumberRange)
                return E_FAIL;
            
            hr = spNumberRange->SetFormula(krt::utf16(numberStr), &range);
            if (FAILED(hr))
                return hr;
        }
        return S_OK;
    }
}  // namespace ColOperatorHelper
}  // namespace wo
