﻿#ifndef __ET_COPY_DASHBOARD_SHEET_HELPER_H__
#define __ET_COPY_DASHBOARD_SHEET_HELPER_H__

#include "appcore/et_appcore_dbsheet.h"
#include "workbook.h"

namespace wo
{
class CopyDashBoardSheetHelper
{
public:
    HRESULT Init(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet,
                 IKWebExtensionMgr* pWebExtensionMgr);
	HRESULT ExecCopy();
private:
    HRESULT CopySheetInfo();
    HRESULT CopyCharts();
private:
	etoldapi::_Worksheet* m_pSrcWorkSheet = nullptr;
	etoldapi::_Worksheet* m_pTarWorkSheet = nullptr;
    IKWebExtensionMgr* m_pWebExtensionMgr = nullptr;
    ks_stdptr<IDbtBookCtx> m_spDbtBookCtx;
};

};
#endif // __ET_COPY_DASHBOARD_SHEET_HELPER_H__