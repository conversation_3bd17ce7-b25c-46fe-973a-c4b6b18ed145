﻿#ifndef __SHELL2_WEBET_KCOREMETRICS_H__
#define __SHELL2_WEBET_KCOREMETRICS_H__

#include "wo/wo_coremetrics_i.h"

namespace binary_wo {
	class BinWriter;
}

namespace wo
{
class KEtWorkbook;

enum class KCoreMetricSyncOp {
	kQuery
};

enum class KMetricsType
{
	kQuery,
	kExec,
	kQueryInit,
	
	_kCount
};

class KCoreMetric: public ICoreMetrics
{
	union Flags
	{
		struct {
			uint32_t enable:1;
			uint32_t isQuery:1;
			uint32_t isExec: 1;
			uint32_t hasLock: 1;
			uint32_t isLock: 1;
			uint32_t isHttpCalling: 1;
			uint32_t hasCalcCollect: 1;
			uint32_t isCollect: 1;
			uint32_t hasExecInit: 1;
			uint32_t isInExecTask: 1;
			uint32_t isReInit: 1;
			uint32_t isInSerVersions: 1;
			uint32_t isInCustomNewObjs: 1;
			uint32_t isInUndoRedo: 1;
		};

		uint32_t value;
	};
	
public:
	KCoreMetric();
	bool isEnable() override { return m_flags.enable; }
	bool isReinit() { return m_flags.isReInit; }
	void onBeginQuery();
	void onBeginExec();
	void setTasksTime(unsigned int ms);
	void setSerInitTime(unsigned int ms);
	void setSerVersionTime(unsigned int ms);
	void onLockBefore(bool isLock);
	void onLockAfter();
	void onBeginSyncChild();
	void onEndSyncChild();
	void onSerialVersions(WebInt serVers, WebInt hitVers);
	void addMetric(KEtWorkbook * wb, binary_wo::BinWriter & bw, WebProcType procType);
	
	void setU32Metrics(KU32MetricItem type, unsigned int val) override; // beginCollect才收集
	void addU32Metrics(KU32MetricItem type, unsigned int val) override; // beginCollect才收集
	void addU32MetricsGlb(KU32MetricItem type, unsigned int val); // 非beginCollect也会收集
	void set64Metrics(K64MetricItem type, int64_t val) override; // beginCollect才收集
	void add64Metrics(K64MetricItem type, int64_t val) override; // beginCollect才收集
	void add64MetricsGlb(K64MetricItem type, int64_t val); // 非beginCollect也会收集
	void setWstrMetrics(KWStrMetricItem type, const WCHAR * wsz) override; // beginCollect才收集
	
	void getSerNewObjMetricItem(KU32MetricItem & bytesItem, KU32MetricItem & timeItem, KU32MetricItem & cntItem);
	void setInCustomNewObjs(bool v) { m_flags.isInCustomNewObjs = v; }
	int64_t get64Metrics(K64MetricItem item) { return _get64Metrics(item); }
	int64_t getAllForkQueryCount() { return _get64Metrics(K64MetricItem::kAllForkQueryCnt); }
	void addForkQuery(int cnt);
	void addUndoRedoTime(unsigned int ms, bool isUndo);
	void setExecInitImplTime(unsigned int ms, bool isReinit);
	void addAppEndTransTime(unsigned int ms);
	void addTransformationTime(unsigned int ms);
	void addCmdTime(unsigned int ms, unsigned int memUsedKb, unsigned int beginMemKb);
	void setTraceId(const ks_wstring & traceId);
	const ks_wstring & traceId() const { return m_traceId; }
	void setIsHttpCaling(bool v);
	void beginCollect(KMetricsType type);
	void setAllCountAndTime(int64_t cnt, int64_t ms);
	void endCollect(KEtWorkbook * wb, const char * name, unsigned int ms, WebProcType procType);
	void onSyncCoreMetric(binary_wo::VarObj rootObj, bool fromChild);
	void onSerializeVersionsBegin();
	void onBeforeCmdExec(WebStr name, const ks_wstring & cmdId);
	void onBeforeExecTasks();
	
	void onBeginUndoRedo(bool isUndo);
	void onTransformChanged();
	void onSerialOnlyVersionStart();
	void onSerialOnlyVersionEnd(int cnt, int binSize);
	
	void onSyncChildMetric(binary_wo::VarObj rootObj, binary_wo::BinWriter & bw);
	void onSyncMasterMetric(binary_wo::VarObj rootObj);
	
	void onOpenEnd();
private:
	void _setU32Metrics(KU32MetricItem type, unsigned int val) { assert(m_u32Metrics[(int)type] == 0); m_u32Metrics[(int)type] = val; }
	void _addU32Metrics(KU32MetricItem type, unsigned int val) { m_u32Metrics[(int)type] += val; }
	unsigned int _getU32Metrics(KU32MetricItem type) { return m_u32Metrics[(int)type]; }

	void _set64Metrics(K64MetricItem type, int64_t val) { m_64Metrics[(int)type] = val; }
	void _add64Metrics(K64MetricItem type, int64_t val) { m_64Metrics[(int)type] += val; }
	int64_t _get64Metrics(K64MetricItem type) { return m_64Metrics[(int)type];}
	
	void _setWStrMetrics(KWStrMetricItem type, const WCHAR * wsz) {m_wstrFlags.set((int)type); m_wstrMetrics[(int)type] = wsz; }

	unsigned int _calcBeginTime(unsigned int ms) 
	{
		return (std::chrono::steady_clock::now() - m_beginCollect) / std::chrono::milliseconds(1) - ms;
	}
	
	bool calcCollect(unsigned int ms, int & rate);
	void resetData();
	QString makeCollectName(wo::KEtWorkbook* wb, const char *name, WebProcType procType);
	
private:
	Flags m_flags;
	unsigned int m_u32Metrics[(int)KU32MetricItem::_kCount];
	int64_t m_64Metrics[(int)K64MetricItem::_kCount];
	std::bitset<(int)KWStrMetricItem::_kCount> m_wstrFlags;
	ks_wstring m_wstrMetrics[(int)KWStrMetricItem::_kCount];

	unsigned int m_forkNonLockTime;
	unsigned int m_syncChildTime;
	int m_syncChildCount;
	int m_execIdx;
	int m_level;
	int m_sampleRate;
	WebInt m_preInitTaskVers;
	KMetricsType m_type;
	std::chrono::steady_clock::time_point m_begin;
	std::chrono::steady_clock::time_point m_beforeForkAfterLock;
	std::chrono::steady_clock::time_point m_beginSyncChild;
	std::chrono::steady_clock::time_point m_beginCollect;
	std::chrono::steady_clock::time_point m_beginOnlySerialVersion;
	std::chrono::steady_clock::time_point m_tpOpenEnd;
	
	int64_t m_allForkNonLockTime;
	ks_wstring m_traceId;
	ThreadLiteLib::SpinlockExp m_lock;
};

class KAutoCustomNewObjsFlag
{
public:
	KAutoCustomNewObjsFlag(KCoreMetric & mc): m_mc(mc) { m_mc.setInCustomNewObjs(true); }
	~KAutoCustomNewObjsFlag() { m_mc.setInCustomNewObjs(false); }
private:
	KCoreMetric & m_mc;
};

}

#endif // __SHELL2_WEBET_KCOREMETRICS_H__
