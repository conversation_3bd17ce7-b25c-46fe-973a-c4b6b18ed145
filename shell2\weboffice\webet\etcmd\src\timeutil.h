#ifndef __WO_WEBET_TIMEAUTIL_H__
#define __WO_WEBET_TIMEAUTIL_H__

#include <iostream>

namespace wo
{

class Timer
{

public:

#ifndef Q_OS_WIN
    typedef std::chrono::steady_clock::time_point TType;
    typedef std::chrono::milliseconds MSType;
#else
    typedef ULONGLONG TType;
    typedef ULONGLONG MSType;
#endif

    Timer()
        : m_start(currTime())
    {
    }

    Timer(int duration)
        : m_start(currTime())
        , m_duration(duration)
    {
    }

    MSType elapsed()
    {
        auto time = currTime();
    #ifndef Q_OS_WIN
        return std::chrono::duration_cast<MSType>(time - m_start);
    #else
        return time - m_start;
    #endif
    }

    void setDuration(int millsecond)
    {
        m_duration = MSType(millsecond);
    }

    bool isExpired()
    {
        return elapsed() > m_duration;
    }

    void printElapsed(std::string tag)
    {
    #ifndef Q_OS_WIN
        auto _elapsed = elapsed().count();
    #else
        auto _elapsed = elapsed();
    #endif
        std::cout << tag << _elapsed << std::endl;
    }

private:
    static TType currTime()
    {
    #ifndef Q_OS_WIN
        return std::chrono::steady_clock::now();
    #else
        return ::GetTickCount64();
    #endif
    }

    TType m_start;
    MSType m_duration;
};

} // namespace wo

#endif // __WO_WEBET_TIMEAUTIL_H__
