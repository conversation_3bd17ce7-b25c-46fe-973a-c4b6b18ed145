﻿#ifndef __SMARTANALYSIS_IDENTIFYTABLE_H__
#define __SMARTANALYSIS_IDENTIFYTABLE_H__

namespace etai
{
enum JsonDataType
{
    JsonDataArrangement = 0,
    JsonDataProofreading = 1,
    JsonDataAnalysis = 2,
    JsonDataPivotTableSource = 3,
    JsonDataQABookSource = 4
};

interface ISmartAnalysisListener;
struct IdentifyRange
{
    long iTop;
    long iBottom;
    long iLeft;
    long iRight;
    IdentifyRange() : iTop(-1), iLeft(-1), iBottom(-1), iRight(-1)
    {
    }
    IdentifyRange(long nT, long nB, long nL, long nR) : iTop(nT), iBottom(nB), iLeft(nL), iRight(nR)
    {
    }
    IdentifyRange(const IdentifyRange &rg) : iTop(rg.iTop), iBottom(rg.iBottom), iLeft(rg.iLeft), iRight(rg.iRight)
    {
    }
    IdentifyRange(Range *pRange);
    IdentifyRange(const RANGE &rg);
    virtual ~IdentifyRange()
    {
    }
    IdentifyRange &operator=(const IdentifyRange &rs)
    {
        iTop = rs.iTop;
        iBottom = rs.iBottom;
        iLeft = rs.iLeft;
        iRight = rs.iRight;
        return *this;
    }
    bool operator<(const IdentifyRange &rhs) const
    {
        return ((iTop < rhs.iTop) || (iTop == rhs.iTop && iLeft < rhs.iLeft) ||
                (iTop == rhs.iTop && iLeft == rhs.iLeft && iBottom < rhs.iBottom) ||
                (iTop == rhs.iTop && iLeft == rhs.iLeft && iBottom == rhs.iBottom && iRight < rhs.iRight));
    }
    bool operator!=(const IdentifyRange &rhs) const
    {
        return (iTop != rhs.iTop || iLeft != rhs.iLeft || iBottom != rhs.iBottom || iRight != rhs.iRight);
    }
    bool IsInRange(const IdentifyRange &rg) const
    {
        return (rg.iTop >= iTop && rg.iBottom <= iBottom && rg.iLeft >= iLeft && rg.iRight <= iRight);
    }
    bool IsInRange(long iRow, long iCol) const
    {
        return (iRow >= iTop && iRow <= iBottom && iCol >= iLeft && iCol <= iRight);
    }
    bool IsOneCell() const
    {
        return (iTop == iBottom && iLeft == iRight);
    }
    bool IsInvalid() const
    {
        return iTop < 0 || iBottom < 0 || iLeft < 0 || iRight < 0 || iTop > iBottom || iLeft > iRight;
    }
    void ResetRange(Range *pRange);
    void ResetRange(const RANGE *pRange);
    void ExpandRange(const IdentifyRange &rg)
    {
        if (IsInvalid() || rg.IsInvalid())
            return;
        iTop = MIN(iTop, rg.iTop);
        iLeft = MIN(iLeft, rg.iLeft);
        iRight = MAX(iRight, rg.iRight);
        iBottom = MAX(iBottom, rg.iBottom);
    }
};
class HiddenRowColTool;
struct IdentifyCells : public IdentifyRange
{
    QString strContent;
    QString strValue2;
    QString strNumberFormat;
    float fFontSize;
    int nAlignment;
    QVariant varValue;

    IdentifyCells(RANGE &rg) : IdentifyRange(rg), fFontSize(11.0f), nAlignment(etHAlignGeneral)
    {
    }
    IdentifyCells &operator=(const IdentifyCells &rs)
    {
        iTop = rs.iTop;
        iBottom = rs.iBottom;
        iLeft = rs.iLeft;
        iRight = rs.iRight;
        strContent = rs.strContent;
        strValue2 = rs.strValue2;
        strNumberFormat = rs.strNumberFormat;
        fFontSize = rs.fFontSize;
        nAlignment = rs.nAlignment;
        varValue = rs.varValue;
        return *this;
    }
    void ExportJson(QJsonObject &jsonRow, HiddenRowColTool *pTool) const;
};

struct IdentifyMergeCellsList : public std::vector<IdentifyCells>
{
    const IdentifyCells *FindCellInMergeCells(long iRow, long iCol);
};

class HiddenRowColTool
{
  public:
    HiddenRowColTool(ISheet *pSheet, const RANGE &rg);
    bool IsHiddenRow(long iRow);
    bool IsHiddenCol(long iCol);

    // 当当前行为隐藏行则返回下一个非隐藏行对应的idx
    long GetExportJsonRowIdx(long iRow);
    // 处理区域的情况
    IdentifyRange GetExportJsonRange(IdentifyRange rg);

    // 供服务器返回结果映射到sheet上使用
    IdentifyRange GetRealRange(IdentifyRange rg);

    long GetRealRowIdx(long iRow);
    long GetRealColIdx(long iCol);

  private:
    void Init(ISheet *pSheet, const RANGE &rg);

    long GetExportJsonColIdx(long iCol);
    // 当当前行为隐藏行则返回上一个非隐藏行对应的idx
    long GetExportJsonRowIdx1(long iRow);
    long GetExportJsonColIdx1(long iCol);

  private:
    IdentifyRange m_UserRange;
    std::set<long> m_setHiddenRow;
    std::set<long> m_setHiddenCol;
};

typedef bool(__stdcall *IdentifyTableContinueProc)();

class KIdentifyTable
{
  public:
    KIdentifyTable(IN IKWorksheet* pWorkSheet, IN JsonDataType nJsonDataType);
    ~KIdentifyTable();

    bool IsContinue() const;
    virtual void Identify(OUT QJsonObject &obj, ISmartAnalysisListener *pListener = nullptr);
    void SetIdentifyTableContinueProc(IdentifyTableContinueProc identifyTableContinueProc);
    int GetCellCount() const;
    void setMaxCellCount(int maxCellCount);

  protected:
    bool GetTables(std::vector<IdentifyRange> &vecTables, QJsonArray &jsonTableList, ISmartAnalysisListener *pListener);
    virtual bool TransformTable(const IdentifyRange &rg, QJsonObject &jsonTable, ISmartAnalysisListener *pListener);
    virtual void GetTableRanges(std::vector<IdentifyRange> &vecTables, ISmartAnalysisListener *pListener);
    void GetSelectionRange();
    void GetTableCols(std::vector<std::pair<long, long>> &vecTableCols, ISmartAnalysisListener *pListener);
    void GetSelectionCols(IdentifyRange rg, std::vector<std::pair<long, long>> &vecTableCols);
    virtual void ExportJson(const QJsonArray &jsonTableList, QJsonObject &jsonObj);

    bool IsNotEmptyCell(IDX iSheet, ROW iRow, COL iCol);

    void BuildCells(IDX iSheet, ROW iRow, COL iCol, IdentifyCells &cells);
    void ExportJsonTable(const IdentifyRange &rg, QJsonObject &jsonTable);

  protected:
    ks_stdptr<_Worksheet> m_spWorksheet;
    ks_stdptr<IBookOp> m_spBookOp;
    ks_stdptr<IETStringTools> m_spTools;
    JsonDataType m_nJsonDataType;
    IdentifyRange m_Range;
    ks_stdptr<Range> m_spUserRange;
    IdentifyRange m_UserRange;
    IdentifyRange m_SelectRange;
    std::vector<IdentifyRange> m_vecSelectRangeList;
    HiddenRowColTool *m_pTool;
    IdentifyTableContinueProc m_identifyTableContinueProc;
    int m_nCellCount = 0;
    int m_maxCellCount = 0;
};

class KIdentifyPivotTableSource : public KIdentifyTable
{
  public:
    KIdentifyPivotTableSource(IN IKWorksheet *pWorkSheet, IN etoldapi::Range *pApiRange);

  protected:
    virtual void GetTableRanges(std::vector<IdentifyRange> &vecTables);
    virtual void ExportJson(const QJsonArray &jsonTableList, QJsonObject &jsonObj);
};

class KIdentifyResult
{
  public:
    KIdentifyResult(IN IKSheet *pSheet);
    ~KIdentifyResult();

    void TransformResultJson(IN JsonDataType nJsonDataType, IN OUT QJsonObject &jsonObj);

  private:
    void TransformResultJsonArrangement(QJsonObject &jsonObj);
    void TransformResultJsonProofreading(QJsonObject &jsonObj);
    void TransformError(QJsonObject &jsonObj);
    void TransformRealRange(const QJsonArray &jsonArray, QJsonArray &jsonRange);

  private:
    ks_stdptr<_Worksheet> m_spWorksheet;
    HiddenRowColTool *m_pTool = nullptr;
};

class IdentifyTool
{
  public:
    static QString GetCellText(ROW iRow, COL iCol, IETStringTools *pTools);
	static KComVariant GetCellVariant(IDX iSheet, ROW iRow, COL iCol, IBookOp* pBookOp);
    static QString GetCellValue2(IDX iSheet, ROW iRow, COL iCol, IBookOp *pBookOp);
    static QString GetCellNumberFormatLocal(IDX iSheet, ROW iRow, COL iCol, IBookOp *pBookOp);
    static void GetTableRange(Range *pRange, RANGE *pTableRange);
};

class KIdentifyQATable : public KIdentifyTable
{
public:
  KIdentifyQATable(IN IKWorksheet *pWorksheet, IN JsonDataType nJsonDataType);
  void Identify(OUT QJsonObject &obj, ISmartAnalysisListener *pListener = nullptr) override;

protected:
  bool TransformTable(const IdentifyRange &rg, QJsonObject &jsonTable, ISmartAnalysisListener *pListener) override;
};

class KIdentifyQASource
{
public:
	KIdentifyQASource(IKWorkbook* pBook);
	~KIdentifyQASource();
	void Identify(QJsonArray& data, QSet<QString> incrementSheet);
	bool HasOverflow() const;
	void SetMaxCellLimit(int maxSheetCell, int maxBookCell);
	QVariantMap GetCountCollect() const;

private:
	ks_stdptr<_Workbook> m_spWorkbook;
	int m_nCount = 0;
  int m_maxSheetCell = 0;
	int m_maxBookCell = 0;
	bool m_bOverFlow = false;
	QVariantMap m_countCollect;
};

} // namespace etai
#endif /* __SMARTANALYSIS_IDENTIFYTABLE_H__ */
