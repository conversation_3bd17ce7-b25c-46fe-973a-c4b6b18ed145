﻿#include "autofilteritemshelper.h"
#include "appcore/et_appcore_basic_itf.h"
#include "framework/krt/krtstring.h"
#include "helpers/varobject_helper.h"
#include "webbase/binvariant/binwriter.h"
#include "kso/l10n/et/etshell.h"
#include "appcore/et_appcore_cellcolor_itf.h"
#include "condition_format_helper.h"
#include "serialize_impl.h"
#include "wo/serial_xf_helper.h"
#include "workbook.h"
#include "ettools/ettools_encode_decoder.h"

namespace wo
{


AutofilterOperatorSerializeHelper::AutofilterOperatorSerializeHelper(IKAutoFilter *pAutoFilter, int nField)
 :m_pAuto<PERSON>ilter(pAutoFilter), m_nField(nField)
{

}
void AutofilterOperatorSerializeHelper::Serialize(ISerialAcceptor* acpt)
{
    if (!m_pAutoFilter)
        return;
    ETFilterOperator op = m_pAutoFilter->GetOperator(m_nField);
    acpt->addKey("filterOperator");
	acpt->beginStruct();
    
    PCWSTR pOpStr = NULL;
    HRESULT hr = _appcore_GainEncodeDecoder()->EncodeFilterOperatorType(op, &pOpStr);
    if (SUCCEEDED(hr) && pOpStr)
    {    
        acpt->addString("filterOp", pOpStr);
    }
    if (op == FOp_FilterDynamic)
    {
        KCriteriaParam1 param1;
        m_pAutoFilter->GetCriteria1(m_nField, &param1);
        ET_DYNAMIC_FILTER_CRITERIA dfc = dfc_None;
        if (param1.GetParamType() == cptDfc)
            dfc = param1.GetDfc();
        PCWSTR pDfcStr = NULL;
        hr = _appcore_GainEncodeDecoder()->EncodeDynamicFilterCriteriaType(dfc, &pDfcStr);
        if (SUCCEEDED(hr) && pDfcStr)
        {
            acpt->addString("criteria1", pDfcStr);
        }
            
    }
    acpt->endStruct();
}


AutoFilterItemsHelper::AutoFilterItemsHelper(IKAutoFilter* pAutoFilter, IAutoFilterValues* pValues, 
        ValuesNode* pRoot, const binary_wo::VarObj& param, KEtWorkbook* workbook, int nFieldIdx)
	: m_pAutoFilter(pAutoFilter)
    , m_pValues(pValues)
	, m_pRoot(pRoot)
	, m_nInvalidNodeCount(pValues->GetInvalidNodeCount())
	, m_serializeNodeCnt(0)
    , m_nFieldIdx(nFieldIdx)
{
	m_wwb = workbook;
    m_bApplyMatch = false;
    m_bSort = false;
    PCWSTR searchData = NULL;
	PCWSTR searchType = NULL;
    PCWSTR searchMode = NULL;
	m_wildfilterType = et_FilterType_Wildcard;
	_appcore_CreateObject(CLSID_KEtAutoFilterStringMatch, IID_IKEtFilterStringMatch, (void**)&m_spEtFilterStringMatch);
    binary_wo::VarObj vSc = param.get_s("search");
	if (vSc.type() != binary_wo::typeInvalid)
	{
        m_bApplyMatch = true;
		if (vSc.has("pinyin"))
		{//拼音开关是否开启
			m_bEnablePinyin = vSc.field_bool("pinyin");
		}
		if (vSc.has("value")) 
		{
			searchData = vSc.field_str("value");
			m_oriSearchDatas = krt::fromUtf16(searchData);
		}
		if (vSc.has("type")) searchType = vSc.field_str("type");
        if (vSc.has("mode")) searchMode = vSc.field_str("mode");
		if (vSc.has("wildfilterType")) m_wildfilterType = (ET_FilterType)vSc.field_int32("wildfilterType");   
	}
    PCWSTR sortType = NULL;
    PCWSTR sortOrder = NULL;
    binary_wo::VarObj vSort = param.get_s("sort");
    if (vSort.type() != binary_wo::typeInvalid)
    {
        m_bSort = true;
        if (vSort.has("type")) sortType = vSort.field_str("type");
        if (vSort.has("order")) sortOrder = vSort.field_str("order");
    }

    m_nSearchType = StrToSearchType(searchType);
    m_nSearchMode = StrToSearchMode(searchMode);
    parseSearchData(m_nSearchMode, searchData, m_searchDatas);
    m_nSortType = StrToSortType(sortType);
    m_nSortOrder = StrToSortOrder(sortOrder);

    m_bUsePage = false;
    m_nPageSize = PAGE_SIZE;
    m_nPageOffset = 0;
    m_nPageType = NORMAL;
    binary_wo::VarObj vPage = param.get_s("page");
    if (vPage.type() != binary_wo::typeInvalid)
    {
        m_bUsePage = true;
        if (vPage.has("size")) 
            m_nPageSize = std::max(PAGE_SIZE, vPage.field_int32("size"));
        if (vPage.has("offset")) 
            m_nPageOffset = std::max(0, vPage.field_int32("offset"));
        if (vPage.has("type"))
            m_nPageType = StrToPageType(vPage.field_str("type"));
    }
    //只有切换的时候才有这个参数 "pined"为预留 "applySwitchPined" : { "pined": true }
    binary_wo::VarObj vSwitchPined = param.get_s("applySwitchPined");
    if (vSwitchPined.type() != binary_wo::typeInvalid)
    {
        m_bApplySwitchPined = true;
    }
     

    //不能设置搜索状态,因为设置node状态的时候,需要根据是否是搜索状态来设置.如果是搜索状态,设置的是searchState,非搜索状态设置的是checktype,具体看setNodeState函数
	// ValuesNode* pSearchAdd = static_cast<ValuesNode*>(m_pRoot->childs.at(1));
	// pSearchAdd->searchState |= ITEM_VISIBLE;
}

bool AutoFilterItemsHelper::hasWildChar(PCWSTR matchText)
{
	while (*matchText)
	{
		if (*matchText == '?' ||
			*matchText == '~' ||
			*matchText == '*')
			return true;
		++matchText;
	}
	return false;
}

WCHAR AutoFilterItemsHelper::toUpperFast(WCHAR ch)
{
	if (ch >= __Xc('A') && ch <= __Xc('Z'))
		return ch;
	else if (ch >= __Xc('a') && ch <= __Xc('z'))
		return ch - (__Xc('a') - __Xc('A'));
	else // 使用Qt的toUpper保证多语正常
		return QChar::toUpper(ch);
}

void AutoFilterItemsHelper::toUpperStr(LPCWSTR lpwsz, LPWSTR lpwszDest)
{
	while (*lpwsz != 0)
	{
		*lpwszDest = toUpperFast(*lpwsz);
		++lpwsz;
		++lpwszDest;
	}
	*lpwszDest = 0;
}

bool AutoFilterItemsHelper::matchStrI(LPCWSTR lpFirst, LPCWSTR lpSrch)
{
	if (lpFirst == NULL || lpSrch == NULL)
		return false;

	int lenFirst = xstrlen(lpFirst);
	int lenSrch = xstrlen(lpSrch);

	WCHAR* lpFirstUp = new WCHAR[lenFirst + 1];
	WCHAR* lpSrchUp = new WCHAR[lenSrch + 1];

	toUpperStr(lpFirst, lpFirstUp);
	toUpperStr(lpSrch, lpSrchUp);

	bool br = (::xstrstr(lpFirstUp, lpSrchUp) != NULL);

	delete[] lpFirstUp;
	delete[] lpSrchUp;
	return br;
}

bool AutoFilterItemsHelper::findWithWildChar(PCWSTR matchText, PCWSTR nodeText)
{
	if (matchText == NULL || nodeText == NULL)
		return false;

	int nMatchLen = 0, nStartPos = 0;
	BOOL bRet = alg::FindWithWildChar(matchText, nodeText, 0, nStartPos, nMatchLen);
	if (bRet && nMatchLen == 0)
		bRet = FALSE;
	return bRet;
}


int AutoFilterItemsHelper::matchYMDHMM(
	ValuesNode* pNode,
	bool bLastState,
	SearchType nType,
	int nFloor,
	int nNodeIdx,
	int nParentIdx,
	LPCWSTR szText,
	bool hasWild,
	bool bPinyinSearch,
	DateMatchParam* pDateMatchParam)
{

	bool bComResult = false;
	if (nFloor != 0 && (nType == nFloor || nType == all))
	{
		ET_FilterType mutiFilterType = et_MutiFilterType_ContainOne;
		if (m_nSearchMode == AND) 
		{
			mutiFilterType = et_MutiFilterType_ContainAll;
		}
		else if (m_nSearchMode == WHOLE)
		{
			mutiFilterType = et_MutiFilterType_ContainString;
		}
		//全日期匹配
		QStringList list;
		if (nType == all && pNode->nNodeType == avType_DATE 
			&&(pDateMatchParam && pDateMatchParam->bDate))
		{
			std::stack<ValuesNode*> trace;
			int nTmp = matchYMDHMM(pNode, trace, bLastState, nType, nFloor, nNodeIdx, 
				nParentIdx, szText,hasWild, bPinyinSearch, mutiFilterType, m_wildfilterType, 
				pDateMatchParam->time, pDateMatchParam->dateType);
			pNode->searchState |= (nTmp != 0 ? Qt::Checked : Qt::Unchecked);
			return nTmp;
		}
		//月
		if (nFloor == 2)
		{
			bComResult = m_spEtFilterStringMatch->matchString(szText, pNode, true, bPinyinSearch, mutiFilterType, m_wildfilterType);
		}
		//all年日时分秒少考虑00,:00这些状况
		else
		{
			bComResult = m_spEtFilterStringMatch->matchString(szText, pNode, false, bPinyinSearch, mutiFilterType, m_wildfilterType);

			if (!bComResult  &&
				(!pNode->bstrText || pNode->bstrText[0] == __Xc('\0')) &&
				m_pRoot->childs.size() > (size_t)m_nInvalidNodeCount &&
				pNode == m_pRoot->childs.at(m_pRoot->childs.size() - 1))
			{
				if (hasWild)
					bComResult = m_spEtFilterStringMatch->findWithWildChar(szText, et_sBlanks);
				else
					bComResult = m_spEtFilterStringMatch->matchStrI(et_sBlanks, szText);
			}
		}
	}

	int nCount = pNode->childs.size();
	int nTmp = -1;
	bool bChildCheck = false;
	pNode->searchState = ITEM_VISIBLE;
	for (int i = 0; i < nCount; ++i)
	{
		//考虑搜索出现的节点
		if (i < m_nInvalidNodeCount && pNode == m_pRoot)
			continue;

		nTmp = matchYMDHMM(pNode->childs.at(i),
												 bComResult | bLastState,
												 nType,
												 nFloor + 1,
												 i,
												 nNodeIdx,
												 szText,
												 hasWild,
												 bPinyinSearch,
												 pDateMatchParam);
		pNode->searchState |= (nTmp != 0 ? Qt::Checked : Qt::Unchecked);

		if (nTmp & SEARCH_COMPARE_MATCH || nTmp & SEARCH_CHILD_CHECK)
			bChildCheck = true;
	}



	if (bComResult || bLastState)
		pNode->searchState |= Qt::Checked;

	if (pNode->searchState == (ITEM_VISIBLE | Qt::Unchecked) && pNode->parent)
	{
		pNode->searchState = 0;
	} 

	int nCheckInfo = 0;
	if (bComResult)
		nCheckInfo |= SEARCH_COMPARE_MATCH;
	if (bLastState)
		nCheckInfo |= SEARCH_LAST_STATE;
	if (bChildCheck)
		nCheckInfo |= SEARCH_CHILD_CHECK;
	//if(bComResult)
	//	m_nMatchCount++;
	return nCheckInfo;
}

int AutoFilterItemsHelper::matchYMDHMM(
		ValuesNode* pNode,
		std::stack<ValuesNode*>& trace,
		bool bLastState,
		SearchType nType,
		int nFloor,
		int nNodeIdx,
		int nParentIdx,
		LPCWSTR szText,
		bool hasWild,
		bool bPinyinSearch,
		ET_FilterType mutiFilterType, ET_FilterType filterType, double dbval, int dateType)
{
	bool bComResult = false;
	trace.push(pNode);
	int nCount = pNode->childs.size();
	bool bChildCheck = false;
	pNode->searchState = ITEM_VISIBLE;

	int nCheckInfo = 0;
	for (int i = 0; i < nCount && nFloor < 3; ++i)
	{
		int nTmp;
		nTmp = matchYMDHMM(pNode->childs.at(i), trace,
			bComResult | bLastState,
			nType,
			nFloor + 1,
			i,
			nNodeIdx,
			szText,
			hasWild,
			bPinyinSearch, mutiFilterType, filterType, dbval, dateType);
		if (nTmp & SEARCH_COMPARE_MATCH || nTmp & SEARCH_CHILD_CHECK)
			bChildCheck = true;
	}
	if (nFloor == 3)
	{
		if (m_wwb && m_wwb->GetCoreWorkbook() && m_wwb->GetCoreWorkbook()->GetBook())
		{
			bComResult = m_spEtFilterStringMatch->matchString(szText, pNode, trace, false, bPinyinSearch,
				mutiFilterType, filterType, dbval, dateType, m_wwb->GetCoreWorkbook()->GetBook()->Is1904DateSystem());
		}
	}
	//ValuesNode* tmpNode = trace.top();
	//节点展开
	if (bComResult || bChildCheck)
	{
		pNode->searchState |= Qt::Checked;
	}
	else
	{
		pNode->searchState = 0;
	}
	trace.pop();
	if (bComResult)
	{
		nCheckInfo |= SEARCH_COMPARE_MATCH;
		nCheckInfo |= SEARCH_CHILD_CHECK;
	}
	if (bChildCheck)
		nCheckInfo |= SEARCH_CHILD_CHECK;
	return nCheckInfo;
}

bool AutoFilterItemsHelper::GetDateParam(const QString& qStr, DateMatchParam& dateParam)
{
	 ks_castptr<_Workbook>spWorkbook = m_wwb->GetCoreWorkbook();
	if (spWorkbook == NULL) return false;

	ks_stdptr<IBookOp> spBookop;
	VS(spWorkbook->GetBook()->GetOperator(&spBookop));
	ks_stdptr<IFormula> spFmla;
	VS(spBookop->CreateFormula(&spFmla));

	COMPILE_RESULT cr;
	CS_COMPILE_PARAM ccp(cpfReForceResult | cpfRCStyle, 0, 0, 0);
	HRESULT hr = spFmla->SetFormula(krt::utf16(qStr), ccp, &cr);
	if (FAILED(hr)) return false;

	exec_token_vector etv;
	VS(spFmla->GetTokenVecForce(&etv));

	if (etv.size() != 1)
		return false;

	const_token_ptr pt = etv.get(0);

	DWORD dw = alg::const_token_assist(pt).major_type();
	if (dw == alg::ETP_VDBL || dw == alg::ETP_VINT)
	{
		//判断一下类型
		if ((fftMD <= cr.nFmtType && cr.nFmtType <= fftYMD) 
			|| cr.nFmtType == fftDbYMD
			|| (fftLCYM <= cr.nFmtType && cr.nFmtType <= fftLCYMD)
			|| (fftB1DM <= cr.nFmtType && cr.nFmtType <= fftB1DMY)
			|| (fftNumSysDM <= cr.nFmtType && cr.nFmtType <= fftNumSysYMD)
			|| (fftNumSysB1DM <= cr.nFmtType && cr.nFmtType <= fftNumSysB1DMY)
			|| (fftNumSysDM_BO <= cr.nFmtType && cr.nFmtType <= fftNumSysYMD_BO))
			{
			dateParam.time = etexec::const_vdbl_token_assist(pt).get_value();
			dateParam.dateType = cr.nFmtType;
			dateParam.bDate = true;
			return true;
			}
	}

	return false;
}



int AutoFilterItemsHelper::matchYMDHMM(ValuesNode* pNode, bool bLastState, SearchType nType, SearchMode nMode,
	int nFloor, std::vector<ks_wstring> matchWords)
{
	bool bComResult = false;
	if (nFloor != 0 && (nType == nFloor || nType == all))
	{
        for (ks_wstring matchWord : matchWords)
        {
            PCWSTR strSearch = matchWord.c_str();
            bool hasWild = hasWildChar(strSearch);
            bool bMatch = false;
            //月
            if (nFloor == 2)
            {
                if (hasWild)
                {
                    bMatch = (
                        findWithWildChar(strSearch, pNode->bstrText) ||
                        findWithWildChar(strSearch, krt::utf16(QString::number(pNode->dblVal, 'g', 16))));
                }
                else
                {
                    bMatch = (
                        matchStrI(pNode->bstrText, strSearch) ||
                        matchStrI(krt::utf16(QString::number(pNode->dblVal, 'g', 16)), strSearch)
                        );
                }
            }
            //all年日时分秒少考虑00,:00这些状况
            else
            {
                if (hasWild)
                    bMatch = findWithWildChar(strSearch, pNode->bstrText);
                else
                    bMatch =matchStrI(pNode->bstrText, strSearch);

                if (!bMatch  &&
                    xstrlen(pNode->bstrText) == 0 &&
                    m_pRoot->childs.size() > (size_t)m_nInvalidNodeCount &&
                    pNode == m_pRoot->childs.at(m_pRoot->childs.size() - 1))
                {
                    if (hasWild)
                        bMatch = findWithWildChar(strSearch, et_sBlanks);
                    else
                        bMatch =matchStrI(et_sBlanks, strSearch);
                }
            }

            bComResult = bMatch;
            if (nMode == AND || nMode == WHOLE)
            {
                if (!bComResult) 
                    break;
            } 
            else // OR
            {
                if (bComResult)
                    break;
            }
        }
	}

    int nCount = pNode->childs.size();
	int nTmp = -1;
	bool bChildCheck = false;
	pNode->searchState = ITEM_VISIBLE;
	for (int i = 0; i < nCount; ++i)
	{
		//考虑搜索出现的节点
		if (i < m_nInvalidNodeCount && pNode == m_pRoot)
			continue;

		nTmp = matchYMDHMM(pNode->childs.at(i),
			bComResult | bLastState,
			nType,
            nMode,
			nFloor + 1,
			matchWords);
		pNode->searchState |= (nTmp != 0 ? Qt::Checked : Qt::Unchecked);

		if (nTmp & SEARCH_COMPARE_MATCH || nTmp & SEARCH_CHILD_CHECK)
			bChildCheck = true;
	}

	//节点展开规则,1,本身匹配；2,子节点有匹配的;(排除一种情况，字节点依赖父节点checked)
	//if (bComResult || (!bComResult && bChildCheck))
	//emit expandTreeviewItem(createIndex(nParentIdx, 0, pNode->parent));

	if (bComResult || bLastState)
		pNode->searchState |= Qt::Checked;

	if (pNode->searchState == (ITEM_VISIBLE | Qt::Unchecked) && pNode->parent)
	{
		//emit hideRowTreeView(nNodeIdx, createIndex(nParentIdx, 0, pNode->parent));
		pNode->searchState = 0;
	} 

	int nCheckInfo = 0;
	if (bComResult)
		nCheckInfo |= SEARCH_COMPARE_MATCH;
	if (bLastState)
		nCheckInfo |= SEARCH_LAST_STATE;
	if (bChildCheck)
		nCheckInfo |= SEARCH_CHILD_CHECK;

	return nCheckInfo;
}

void AutoFilterItemsHelper::matchItems()
{
	if (!m_pRoot)
		return;
	ValuesNode* pSearchAdd = static_cast<ValuesNode*>(m_pRoot->childs.at(m_nInvalidNodeCount - 1));
	pSearchAdd->searchState |= ITEM_VISIBLE;
	QString& szText = m_oriSearchDatas;
	if (szText.size() == 0)
	{
		pSearchAdd->searchState = 0;
		return;
	}
	{
		PinyinSearchScope scope(m_spEtFilterStringMatch, false);
		QString trimmedStr = szText.trimmed();
		int length = trimmedStr.length();
		m_spEtFilterStringMatch->checkPatternType(krt::utf16(trimmedStr), length);
		bool bPinyinSrch = m_bEnablePinyin && m_spEtFilterStringMatch->patternIncludeLetter()
			&& length <= m_spEtFilterStringMatch->maxPinyinLength();
        bool bDictPack = m_spEtFilterStringMatch->pinyinDictPack();
		if (bPinyinSrch)
		{
			bool enablePolyphonic = bDictPack && (length <= m_spEtFilterStringMatch->maxPinyinLength());
			m_spEtFilterStringMatch->initPinyinPattern(krt::utf16(trimmedStr), length, enablePolyphonic);
		}

		DateMatchParam dateParam;
		GetDateParam(szText, dateParam);

		matchYMDHMM(m_pRoot,
			false,
			m_nSearchType,
			0, 0, 0,
			krt::utf16(szText),
			hasWildChar(krt::utf16(szText)),
			bPinyinSrch,
			&dateParam);
	}
	static_cast<ValuesNode*>(m_pRoot->childs.at(0))->searchState = m_pRoot->searchState;
}

AutoFilterItemsHelper::SearchType AutoFilterItemsHelper::StrToSearchType(PCWSTR str)
{
	if (str == NULL)
		return all;

	if (xstrcmp(str, __X("all")) == 0)
	{
		return all;
	}
	else if (xstrcmp(str, __X("year")) == 0)
	{
		return year;
	}
	else if (xstrcmp(str, __X("month")) == 0)
	{
		return month;
	}
	else if (xstrcmp(str, __X("day")) == 0)
	{
		return day;
	}
	else if (xstrcmp(str, __X("hour")) == 0)
	{
		return hour;
	}
	else if (xstrcmp(str, __X("minute")) == 0)
	{
		return minute;
	}
	else if (xstrcmp(str, __X("second")) == 0)
	{
		return second;
	}
	return all;
}

AutoFilterItemsHelper::SearchMode AutoFilterItemsHelper::StrToSearchMode(PCWSTR str)
{
    if (str == NULL)
		return OR;

	if (xstrcmp(str, __X("and")) == 0)
	{
		return AND;
	}
    else if (xstrcmp(str, __X("or")) == 0)
	{
		return OR;
	}
    else if (xstrcmp(str, __X("whole")) == 0)
    {
        return WHOLE;
    }
    return OR;
}

void AutoFilterItemsHelper::parseSearchData(SearchMode mode, PCWSTR str, std::vector<ks_wstring>& results)
{
    if (str != NULL && str[0] != 0)
    {
        if (mode == AND || mode == OR)
        {
            QStringList strLst = QString::fromUtf16(str).split(" ", QString::SkipEmptyParts).toSet().toList();
            for (QString s : strLst)
            {
                results.push_back(krt::utf16(s));
            }
        }
        else if (mode == WHOLE)
        {
            results.push_back(ks_wstring(str));
        }
    }
}

SortType AutoFilterItemsHelper::StrToSortType(PCWSTR str)
{
    if (str == NULL)
        return BYNAME;
    if (xstrcmp(str, __X("default")) == 0)
	{
		return BYNAME;
	}
    else if (xstrcmp(str, __X("byCount")) == 0)
	{
		return BYCOUNT;
	}
    return BYNAME;
}

SortOrder AutoFilterItemsHelper::StrToSortOrder(PCWSTR str)
{
    if (str == NULL)
        return ASC;
    if (xstrcmp(str, __X("desc")) == 0)
	{
		return DESC;
	}
    else if (xstrcmp(str, __X("asc")) == 0)
	{
		return ASC;
	}
    return ASC;
}

AutoFilterItemsHelper::PageType AutoFilterItemsHelper::StrToPageType(PCWSTR str)
{
    if (str == NULL)
        return NORMAL;
    if (xstrcmp(str, __X("normal")) == 0)
	{
		return NORMAL;
	}
    else if (xstrcmp(str, __X("search")) == 0)
	{
		return SEARCH;
	}
    return NORMAL;
}


void AutoFilterItemsHelper::applyReSort()
{
    if (!m_bSort)
    {
        return;
    }
    //resort
    BOOL byCount = m_sortParam.getSortType() == BYCOUNT ? TRUE : FALSE;
    BOOL asc = m_sortParam.getSortOrder() == ASC ? TRUE : FALSE;
    bool bAuto = m_sortParam.getSortTrigger() == AUTO;
    bool pinedItem = m_sortParam.pinedItems();
    //如果是全选 (partiallyChecked = false),那么排序内部排序也退化为全选的排序
    bAuto = bAuto && pinedItem;
    m_pValues->ResortValues(byCount, asc, bAuto ? TRUE : FALSE);
}

void AutoFilterItemsHelper::applyMatchItems()
{
    if (!m_bApplyMatch)
    {
        return;
    }
	//搜索
    if (!emptySearch())
	{
		matchItems();
	}
    else 
    {
        if (!m_pRoot || (m_pRoot->childs.size() <= m_nInvalidNodeCount - 1))
        {
            return;
        }
        ValuesNode* pSearchAdd = static_cast<ValuesNode*>(m_pRoot->childs.at(m_nInvalidNodeCount - 1));
        if (!pSearchAdd)
        {
            return;
        }
        pSearchAdd->searchState = 0;

        //场景:清空搜索栏
        //reset一下状态
        if (!m_pAutoFilter || !m_pValues || m_nFieldIdx == -1)
            return;
        KCriteriaParam1 param1;
        KCriteriaParam2 param2;
        if (FAILED(m_pAutoFilter->GetCriteria1(m_nFieldIdx, &param1)))
            return;
        if (FAILED(m_pAutoFilter->GetCriteria2(m_nFieldIdx, &param2)))
            return;
        HRESULT hr = m_pValues->ResetValuesCheckedState(m_pAutoFilter->GetOperator(m_nFieldIdx), &param1, &param2);
        if (FAILED(hr))
            return;
        m_bApplyMatch = false;
        m_nPageType = NORMAL;
        if (m_bApplySwitchPined)
        {   //开关改变了,需要重新排序
            m_bSort = true;
            applyReSort();
        }
    }
}


void AutoFilterItemsHelper::SetInverse()
{
    m_bInverse = true;
}

void AutoFilterItemsHelper::Serialize(ISerialAcceptor* acpt)
{
	acpt->addKey("items");
	acpt->beginStruct();
	ET_CUSTOM_FILTER_TYPE filterType = m_pValues->GetValuesType();
	acpt->addString("type", CustomTypeToStr(filterType));
    applyReSort();
	applyMatchItems();
	SerializeRoot(m_pRoot, acpt);
    acpt->addBool("hasDate", alg::BOOL2bool(m_pValues->hasDate()));
	acpt->endStruct();
}

PCWSTR AutoFilterItemsHelper::getCheckType(BYTE type)
{
	switch(type)
	{
	case 0:
		return __X("unchecked");
	case 1:
		return __X("partiallyChecked");
	case 2:
		return __X("checked");
	default: 
		ASSERT(false);
		return __X("unchecked");
	}
}

PCWSTR AutoFilterItemsHelper::CustomTypeToStr(ET_CUSTOM_FILTER_TYPE ty)
{
	switch(ty)
	{
		case customFilterType_NULL:
			return __X("null");
		case customFilterType_Number:
			return __X("num");
		case customFilterType_Date:
			return __X("date");
		case customFilterType_Label:
			return __X("label");
		default:
			ASSERT(FALSE);
			return __X("null");
	}
}

void AutoFilterItemsHelper::setNodeChecked(ValuesNode* pSearchAdd, ValuesNode* pCurrNode, int nCheckType)
{
	if (pSearchAdd->searchState & ITEM_VISIBLE)
		pCurrNode->searchState = ITEM_VISIBLE | nCheckType;
	else
		pCurrNode->SetCheckType(nCheckType);
}

void AutoFilterItemsHelper::transSetChildNode(QList<ValuesNode*>& nodeQueue, ValuesNode* pSearchAdd, int nCheckType)
{
    ValuesNode* pBottomRight = NULL;
    while (nodeQueue.size() != 0)
	{
		pBottomRight = nodeQueue.front();
		if (!pBottomRight)
		{
			nodeQueue.pop_front();
			continue;
		}
		size_t nCount = pBottomRight->childs.size();
		for (size_t i = 0; i < nCount; ++i)
		{
			ValuesNode* pNode = pBottomRight->childs.at(i);
			if (!pNode)
				continue;
			if (pSearchAdd->searchState & ITEM_VISIBLE &&
				((pNode->searchState & ITEM_VISIBLE) == 0 ||
				pNode == pSearchAdd))
				continue;

            setNodeChecked(pSearchAdd, pNode, nCheckType);

			if (pNode->childs.size() != 0)
				nodeQueue.push_back(pNode);
		}
		nodeQueue.pop_front();
	}
}
void AutoFilterItemsHelper::transSetParentNode(ValuesNode* pRoot, ValuesNode* pCurrNode, ValuesNode* pSearchAdd)
{
    ValuesNode* pParent = pCurrNode->parent;
	while (pParent)
	{
		//考虑搜索节点
		int nChildBegin = pParent == pRoot ? 2 : 0;
		if (pSearchAdd->searchState & ITEM_VISIBLE)//parent一定显示
			pParent->searchState = ITEM_CHECK_UNKNOWN;
		else
			pParent->SetCheckType(pParent->childs.at(nChildBegin)->GetCheckType());

		size_t nCount = pParent->childs.size();
		for (size_t i = nChildBegin; i < nCount; ++i)
		{
			ValuesNode* pChild = static_cast<ValuesNode*>(pParent->childs.at(i));
			if (!pChild)
				continue;

			if (pSearchAdd->searchState & ITEM_VISIBLE)
			{
				if ((pChild->searchState & ITEM_VISIBLE) == 0)
					continue;

				if (pParent->searchState == ITEM_CHECK_UNKNOWN)
					pParent->searchState = pChild->searchState;
				else
				{
					if (pChild->searchState != pParent->searchState
						|| pChild->searchState & Qt::PartiallyChecked)
					{
						pParent->searchState = ITEM_VISIBLE | Qt::PartiallyChecked;
						break;
					}
				}
			}
			else
			{
				if (pChild->GetCheckType() != pParent->GetCheckType()
					|| pChild->GetCheckType() == Qt::PartiallyChecked)
				{
					pParent->SetCheckType(Qt::PartiallyChecked);
					break;
				}
			}
		}
		pParent = pParent->parent;
	}
}

bool AutoFilterItemsHelper::findHMSNode(const binary_wo::VarObj& item, ValuesNode* pCurrNode, ValuesNode** ppFind)
{
    if (!pCurrNode)
        return false;
    bool hasM = wo::VarObjFieldValidation::expectIntegral(item, "minute");
    bool hasS = wo::VarObjFieldValidation::expectIntegral(item, "second");
    
    size_t cntHour = pCurrNode->childs.size();
    for (size_t i = 0; i < cntHour; i++) 
    {
        ValuesNode* pNodeH = pCurrNode->childs[i];
        if (!pNodeH)
            continue;
        int hour = (int)pNodeH->dblVal;
        int fHour = item.field_int32("hour");
        if (fHour == hour) 
        {
            if (!hasM)
            {
                //如不需要匹配"分","时"已经匹配,那就是匹配了.
                *ppFind = pNodeH;
                return true;
            }
            size_t cntMin = pNodeH->childs.size();
            for (size_t j = 0; j < cntMin; j++) 
            {
                ValuesNode* pNodeM = pNodeH->childs[j];
                if (!pNodeM)
                    continue;
                int minu = (int)pNodeM->dblVal;
                int fMinu = item.field_int32("minute");
                if (minu == fMinu)
                {
                    if (!hasS) 
                    {
                        //如不需要匹配"秒","分"已经匹配,那就是匹配了.
                        *ppFind = pNodeM;
                        return true;
                    }
                    size_t cntS = pNodeM->childs.size();
                    for (size_t k = 0; k < cntS; k++)
                    {
                        ValuesNode* pNodeS = pNodeM->childs[k];
                        if (!pNodeS)
                            continue;
                        int sencond = (int)pNodeS->dblVal;
                        int fs = item.field_int32("second");
                        if (sencond == fs) 
                        {
                            *ppFind = pNodeS;
                            return true;
                        }
                    }
                }
            }
        }
    }
    return false;
}

bool AutoFilterItemsHelper::setNodeState(ValuesNode* pRoot, ValuesNode* pCurrNode, int nCheckType)
{
    if (!pCurrNode)
        return false;
		
    if (!pRoot)
        return false;

    if (pRoot->childs.size() <= m_nInvalidNodeCount - 1)
        return false;

    ValuesNode* pSearchAdd = static_cast<ValuesNode*>(pRoot->childs.at(m_nInvalidNodeCount - 1));
    if (!pSearchAdd)
        return false;

    QList<ValuesNode*> nodeQueue;
	nodeQueue.push_back(pCurrNode);
    setNodeChecked(pSearchAdd, pCurrNode, nCheckType);

    //设置子结点
    transSetChildNode(nodeQueue, pSearchAdd, nCheckType);

    //设置父结点
    transSetParentNode(pRoot, pCurrNode, pSearchAdd);
	
	if (pSearchAdd->searchState & ITEM_VISIBLE)
		pRoot->childs.at(0)->searchState = pRoot->searchState;
	else
		pRoot->childs.at(0)->SetCheckType(pRoot->GetCheckType());

    return true;
}


HRESULT AutoFilterItemsHelper::SetAutoFilterNodeStatus(const binary_wo::VarObj& values, ValuesNode* pValuesRoot)
{
    if (pValuesRoot)
    {
        for (int i = 0, cnt = values.arrayLength(); i < cnt; ++i)
        {
            binary_wo::VarObj item = values.at(i);
		    PCWSTR type = item.field_str("type");
            //全选或者全不选
            if (xstrcmp(type, __X("all")) == 0)
            {
                PCWSTR checkType = item.field_str("checkType");
                if (xstrcmp(checkType, __X("checked")) == 0)
                {
                    //全选
                    setNodeState(pValuesRoot, pValuesRoot, Qt::Checked);
                }
                else 
                {
                     //全不选
                    setNodeState(pValuesRoot, pValuesRoot, Qt::Unchecked);
                }
                return S_OK;
            }
            bool date = false;
            if (xstrcmp(type, __X("date")) == 0)
            {
                date = true;
            }
            int childCount = pValuesRoot->childs.size();
            bool found = false;
            for (int i = m_nInvalidNodeCount; i < childCount; ++i)
            {
                ValuesNode* pNode_1 = pValuesRoot->childs[i];
                int cntChild = pNode_1->childs.size();

                //类型不匹配
                if (date)
                {
                    if(cntChild == 0) 
                        continue;
                }
                else 
                {
                    if(cntChild > 0) 
                        continue;
                }

                if (date)
                {
                    //年
                    ValuesNode* pNodeY = pNode_1;
                    int year = (int)pNodeY->dblVal;
                    for (int j = 0; j < cntChild; ++j)
                    {
                        ValuesNode* pNodeM = pNodeY->childs[j];
                        //月
                        int mon = (int)pNodeM->dblVal;
                        int cntDay = pNodeM->childs.size();
                        for (int k = 0; k < cntDay; k++)
                        {
                            ValuesNode* pNodeD = pNodeM->childs[k];
                            //日
                            int day = (int)pNodeD->dblVal;
                            //判断是否命中
                            int fYear = item.field_int32("year");
                            int fMon = item.field_int32("month");
                            int fDay = item.field_int32("day");
                            //year mon day 一定有,hour,min sencond不一定有
                            if (fYear == year && fMon == mon && fDay == day)
                            {
                                ValuesNode* pMatchNode = NULL;
                                bool hasH = wo::VarObjFieldValidation::expectIntegral(item, "hour");
                                bool bMatch = false;
                                if (hasH)
                                {
                                    bMatch = findHMSNode(item, pNodeD, &pMatchNode);
                                }
                                else 
                                {
                                    //前端不给"时",说明不需要匹配 "时",年月日匹配了就是匹配了
                                    bMatch = true;
                                    pMatchNode = pNodeD;
                                }
                                if (!bMatch)
                                    return S_OK;
                                PCWSTR checkType = item.field_str("checkType");
                                if (xstrcmp(checkType, __X("checked")) == 0)
                                {
                                    setNodeState(pValuesRoot, pMatchNode, Qt::Checked);
                                    found = true;
                                }
                                else if (xstrcmp(checkType, __X("unchecked")) == 0)
                                {
                                    setNodeState(pValuesRoot, pMatchNode, Qt::Unchecked);
                                    found = true;
                                }
                                else
                                {
                                    continue;
                                }
                                found = true;
                                break;
                            }
                        }
                       
                    }
                }
                else 
                {
                    if (!item.has("text"))
                    {
                        continue;
                    }
                    if ((NULL != pNode_1->bstrText && xstrcmp(item.field_str("text"), pNode_1->bstrText) == 0)
                        || (NULL == pNode_1->bstrText && item.field_str("text")[0] == 0))
                    {
                        PCWSTR checkType = item.field_str("checkType");
                        if (xstrcmp(checkType, __X("checked")) == 0)
                        {
                            setNodeState(pValuesRoot, pNode_1, Qt::Checked);
                            found = true;
                        }
                        else if (xstrcmp(checkType, __X("unchecked")) == 0)
                        {
                            setNodeState(pValuesRoot, pNode_1, Qt::Unchecked);
                            found = true;
                        }
                        else
                        {
                            continue;
                        }
                    } 

                }
                if (found) break;
            }
        }    
    }
    return S_OK;
}


void AutoFilterItemsHelper::RecursiveSelect(ValuesNode* pNode, bool bSearch, bool bChecked)
{
	if (!pNode) return;

	BYTE nCheckType = bChecked ? Qt::Checked : Qt::Unchecked;
	if (bSearch)
	{
        const byte ITEM_VISIBLE = 0x4;
		if ((pNode->searchState & ITEM_VISIBLE) == 0)
			return;
		pNode->searchState = ITEM_VISIBLE | nCheckType;
	}
	else
	{
		pNode->SetCheckType(nCheckType);
	}

	for (size_t i = 0, n = pNode->childs.size(); i < n; ++i)
	{
		RecursiveSelect(pNode->childs[i], bSearch, bChecked);
	}
}

void AutoFilterItemsHelper::RecursiveSelectPart(ValuesNode* pNode, bool bSearch)
{
	if (!pNode) return;

	for (size_t i = 0, n = pNode->childs.size(); i < n; i++)
	{
		ValuesNode* p = pNode->childs[i];
		BYTE nCheckType = bSearch ? (p->searchState & QT_CHECK_STATE)
			: p->GetCheckType();

		if (Qt::Checked == nCheckType)
		{
			RecursiveSelect(p, bSearch, false);
		}
		else if (Qt::Unchecked == nCheckType)
		{
			RecursiveSelect(p, bSearch, true);
		}
		else
		{
			RecursiveSelectPart(p, bSearch);
		}
	}
}

//是否搜索空
bool AutoFilterItemsHelper::emptySearch()
{
    return m_bApplyMatch && m_searchDatas.empty() && m_oriSearchDatas.isEmpty();
}

void AutoFilterItemsHelper::setResort(bool sort)
{
    m_bSort = sort;
}

bool AutoFilterItemsHelper::getResort()
{
    return m_bSort;
}

void AutoFilterItemsHelper::setSortParam(const SortParam& sortParam)
{
    m_sortParam = sortParam;
}
SortParam AutoFilterItemsHelper::sortParam()
{
    return m_sortParam;
}

void AutoFilterItemsHelper::SelectInvertItems(ValuesNode* pRoot)
{
	if (!pRoot || pRoot->childs.empty()) return;

	ValuesNode* pSearchNode = pRoot->childs[1];
	ASSERT(pSearchNode->nNodeType == avType_SEARCH);
	bool bSearch = pSearchNode->searchState & ITEM_VISIBLE;;

	auto updateChecked = [](ValuesNode* pNode, bool bSearch)
	{
		if (!pNode) return;

		BYTE nCheckType = bSearch ? (pNode->searchState & QT_CHECK_STATE)
			: pNode->GetCheckType();
		if ((int)nCheckType == Qt::PartiallyChecked)
			return;

		nCheckType = (Qt::Checked == (int)nCheckType) ?
			Qt::Unchecked : Qt::Checked;
		if (bSearch)
			pNode->searchState = ITEM_VISIBLE | nCheckType;
		else
			pNode->SetCheckType(nCheckType);
	};

	updateChecked(pRoot, bSearch);
	updateChecked(pRoot->childs[0], bSearch);

	for (size_t i = m_nInvalidNodeCount, cnt = pRoot->childs.size(); i < cnt; ++i)
	{
		ValuesNode* pNode = pRoot->childs.at(i);
		if (!pNode) continue;

		BYTE nCheckType = bSearch ? (pNode->searchState & QT_CHECK_STATE)
			: pNode->GetCheckType();

		if (Qt::Checked == nCheckType)
		{
			RecursiveSelect(pNode, bSearch, false);
		}
		else if (Qt::Unchecked == nCheckType)
		{
			RecursiveSelect(pNode, bSearch, true);
		}
		else
		{
			RecursiveSelectPart(pNode, bSearch);
		}
	}
}


void AutoFilterItemsHelper::SerializeRoot(ValuesNode* pRootNode, ISerialAcceptor* acpt)
{
    acpt->addKey("root");
    acpt->beginStruct();
    acpt->addKey("childs");
	acpt->beginArray();

    m_serializeNodeCnt = 0;
    int cnt = pRootNode->childs.size();
    int begin = m_nInvalidNodeCount;
    int maxSerializeCount = MAX_UNIQUE_FILTER_FORMAT;
    if (m_bUsePage)
    {
        begin = std::min(cnt, m_nPageOffset + m_nInvalidNodeCount);
        maxSerializeCount = m_nPageSize;
    }
    int i = begin;
	for (;i < cnt && m_serializeNodeCnt < maxSerializeCount; ++i)
	{
		bool temp = SerializeNode(pRootNode->childs[i], acpt, 1);
        if (temp)
            ++m_serializeNodeCnt;
	}
	acpt->endArray();
    acpt->addString("checkType", getCheckType(pRootNode->GetCheckType()));
	acpt->addUint32("total", pRootNode->childs[0]->cntValues);

    acpt->addKey("range");
    acpt->beginStruct();
    acpt->addUint32("begin", begin - m_nInvalidNodeCount);
    acpt->addUint32("end", i - m_nInvalidNodeCount);
    acpt->addUint32("max", cnt - m_nInvalidNodeCount);
    acpt->endStruct();

    // 前端的“全选”是否勾选，取决于所有可见项的勾选状态。
    // 由于初始数据的超限部分的状态对于前端来说是未知的，导致“全选”状态显示可能不准确。
    // 所以当每一次返回分页后，继续找到下一个未选中的节点位置传给前端。用于判断是否还存在“未勾选子项”。
    // 如果前端 勾选或者取消过一次“全选”，那么超限部分的节点是否勾选对于“全选”来说不重要了
    // 如果是搜索内容，默认为全选中
    if (m_nPageType == NORMAL)
    {
        for (;i < cnt; ++i)
        {
            ValuesNode* pNode = pRootNode->childs[i];
            if (pNode->GetCheckType() != 2)
            {
                acpt->addBool("hasDirty", true);
                break;
            }
        }
    }

    acpt->endStruct();
}

bool AutoFilterItemsHelper::SerializeNode(ValuesNode* pNode, ISerialAcceptor* acpt, int level)
{
    if (!pNode || !m_pRoot || m_pRoot->childs.size() < m_nInvalidNodeCount)
        return false;

    //bClientSearchState表示前端是否处于搜索状态中,而m_bApplyMatch表示此次操作是不是搜索操作.
    //例:先搜索,再反选.这时  bClientSearchState = true  而  m_bApplyMatch = false
    bool bClientSearchState = (m_nPageType == SEARCH);

    //搜索命中,包括历史搜索的命中
    bool bSearchMatch = pNode->searchState & ITEM_VISIBLE;
    
    //1. 搜索(m_bApplyMatch = true)未命中(bSearchMatch = false)
    //2. 搜索后(bClientSearchState = true)且未命中(bSearchMatch = false)
    if ((m_bApplyMatch || bClientSearchState) && (!bSearchMatch))
        return false;

    //在搜索状态的时候,前端也可以过来取数据:例如分页数据的获取
    bool bSeverSearchState = false;
    ValuesNode* pSearchAdd = static_cast<ValuesNode*>(m_pRoot->childs.at(m_nInvalidNodeCount - 1));
    if (!pSearchAdd)
        return false;
    if (pSearchAdd->searchState & ITEM_VISIBLE)
        bSeverSearchState = true;

	acpt->beginStruct();
	if (pNode->childs.size() > 0)
	{
		acpt->addKey("childs");
		acpt->beginArray();
		size_t i = 0;
		size_t cnt = pNode->childs.size();
		for (;i < cnt; ++i)
		{
			SerializeNode( pNode->childs[i], acpt, level + 1);
		}
		acpt->endArray();
		acpt->addUint32("total", pNode->childs[0]->cntValues);
	}

    if (level > 1 || level == 1 && pNode->childs.size() > 0)
		acpt->addInt16("val", pNode->dblVal);
    acpt->addString("text", pNode->bstrText == NULL ? __X("") : pNode->bstrText);
    acpt->addUint32("count", pNode->cntValues);
    BYTE state = pNode->GetCheckType();
    if (bClientSearchState)
    {
        state = pNode->searchState & QT_CHECK_STATE;
    }
    acpt->addString("checkType", getCheckType(state));
    acpt->endStruct();

    return true;
}

//////////////////////////////////////////////////////////////////////////


AutoFilterFormatItemsHelper::AutoFilterFormatItemsHelper(IAutoFilterFormatItems* pFmtItems, 
	IBook* pBook, IKAutoFilter *pAutoFilter, int nField)
	: m_pFmtItems(pFmtItems)
	, m_pBook(pBook), m_pAutoFilter(pAutoFilter), 
    m_nField(nField), m_sortBy(SORT_NONE)
{

}

void AutoFilterFormatItemsHelper::Serialize(ISerialAcceptor* acpt)
{
	ks_stdptr<IFilterCellColorItems> spCellColorItems;
	m_pFmtItems->GetCellColorItems(&spCellColorItems);
	serializeCellColorItems(spCellColorItems, acpt);

	ks_stdptr<IFilterFontColorItems> spFontColorItems;
	m_pFmtItems->GetFontColorItems(&spFontColorItems);
	serializeFontColorItems(spFontColorItems, acpt);

	ks_stdptr<IFilterIconItems> spIconItems;
	m_pFmtItems->GetIconItems(&spIconItems);
	serializeIconItems(spIconItems, acpt);
}

void AutoFilterFormatItemsHelper::serializeCellColorItems(IFilterCellColorItems* pItems, ISerialAcceptor* acpt)
{
	ASSERT(pItems);

	acpt->addKey("cellColorItems");
	acpt->beginStruct();
	acpt->addBool("isAllResult", !pItems->IsOverflow());
	
	acpt->addKey("cellColors");
	acpt->beginArray();
	KXFMASK mask(XFMASK::_catFills);
	int nCnt = pItems->GetItemCount();
	ks_stdptr<ICellColor> spCellColor;

	for (int i = 0; i < nCnt; ++i)
	{
		spCellColor.clear();
		pItems->GetItem(i, &spCellColor);
		const EtFill* pFill = NULL;
		spCellColor->GetFill(&pFill);
		optFill(acpt, NULL, pFill, mask, m_pBook);
		bool bSortOn = m_pAutoFilter->IsSortOnColor(m_nField, spCellColor, NULL, NULL);
		if (bSortOn) 
		{
			m_sortFill = *pFill;
			m_sortBy = CELL_FILL;
		}
	}
	acpt->endArray();

	acpt->endStruct();
}

void AutoFilterFormatItemsHelper::serializeFontColorItems(IFilterFontColorItems* pItems, ISerialAcceptor* acpt)
{
	ASSERT(pItems);

	acpt->addKey("fontColorItems");
	acpt->beginStruct();
	acpt->addBool("isAllResult", !pItems->IsOverflow());
	
	acpt->addKey("fontColors");
	acpt->beginArray();
	int nCnt = pItems->GetItemCount();
	ks_stdptr<IFontColor> spFontColor;
	for (int i = 0; i < nCnt; ++i)
	{
		spFontColor.clear();
		pItems->GetItem(i, &spFontColor);
		const EtColor* pColor = NULL;
		spFontColor->GetColor(&pColor);
		wo::SerialColour(acpt, NULL, m_pBook, *pColor, GdiAutoTextColor, spFontColor->IsAuto());
		bool bSortOn = m_pAutoFilter->IsSortOnColor(m_nField, NULL, spFontColor, NULL);
		if (bSortOn) 
		{
			m_sortColor = *pColor;
			m_sortBy = CELL_FONT;
		}
	}
	acpt->endArray();

	acpt->endStruct();
}

void AutoFilterFormatItemsHelper::serializeIconItems(IFilterIconItems* pItems, ISerialAcceptor* acpt)
{
	ASSERT(pItems);

	acpt->addKey("iconItems");
	acpt->beginStruct();
	acpt->addBool("isAllResult", !pItems->IsOverflow());

	acpt->addKey("IconSets");
	acpt->beginArray();
	int nCnt = pItems->GetItemCount();
	ks_stdptr<ICellIcon> spCellIcon;

	for (int i = 0; i < nCnt; ++i)
	{
		spCellIcon.clear();
		pItems->GetItem(i, &spCellIcon);
		const EtIconSet* pIconSet = NULL;
		spCellIcon->GetIcon(&pIconSet);
		optIconSet(acpt, binary_wo::VarObj(), NULL, pIconSet);
		bool bSortOn = m_pAutoFilter->IsSortOnColor(m_nField, NULL, NULL, spCellIcon);
		if (bSortOn) 
		{
			m_sortIconSet = *pIconSet;
			m_sortBy = INCON_SET;
		}
	}
	acpt->endArray();

	acpt->endStruct();
}

}// namespace wo
