﻿#ifndef __WEBET_DATA_ANALYZE_TASK_H__
#define __WEBET_DATA_ANALYZE_TASK_H__
#include "webbase/binvariant/binwriter.h"
namespace wo
{
enum DataAnalyzeTaskType
{
	DATask_Base,
	DATask_CreateMerge,
	DATask_StartMerge,
	DATask_UpdateMerge,
	DATask_CancelMerge,
	DATask_AddMergeLog,
	DATask_UpdateMergeLog,
	DATask_UpdateDataSourceName,
};

class KEtRevisionContext;
class ExecDAOutput
{
public:
	ExecDAOutput(WebSlice* pOutput);
	~ExecDAOutput();
	void AddError(int errorCode, PCWSTR msg);
	void AddData(binary_wo::BinWriter* dataBw);
	binary_wo::BinWriter* getWriter() { return &m_bw; }

private:
bool m_bAddError = false;
WebSlice* m_pOutput = nullptr;
binary_wo::BinWriter m_bw;
};

class KEtWorkbook;
class DataAnalyzeTaskBase
{
public:
	DataAnalyzeTaskBase(KEtWorkbook* wwb) : m_wwb(wwb) {};
	virtual ~DataAnalyzeTaskBase() {};
	virtual HRESULT operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output) = 0;
	virtual HRESULT pushData(binary_wo::BinWriter& bw) { return S_OK; };

protected:
	KEtWorkbook* m_wwb = nullptr;
};

class DataAnalyzeTaskFactory
{
public:
	static std::unique_ptr<DataAnalyzeTaskBase> CreateDataAnalyzeCmd(const binary_wo::VarObj& cmd, KEtWorkbook* wwb);

};

class CreateDAMergeTask : public DataAnalyzeTaskBase
{
public:
	CreateDAMergeTask(KEtWorkbook* wwb) : DataAnalyzeTaskBase(wwb) {};
	virtual ~CreateDAMergeTask() {};

	static PCWSTR GetTag();
	static DataAnalyzeTaskType GetType();
	HRESULT operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output) override;

};

class StartDAMergeTask : public DataAnalyzeTaskBase
{
public:
	StartDAMergeTask(KEtWorkbook* wwb) : DataAnalyzeTaskBase(wwb) {};
	virtual ~StartDAMergeTask() {};

	static PCWSTR GetTag();
	static DataAnalyzeTaskType GetType();
	HRESULT operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output) override;
};

class UpdateDAMergeTask : public DataAnalyzeTaskBase
{
public:
	UpdateDAMergeTask(KEtWorkbook* wwb) : DataAnalyzeTaskBase(wwb) {};
	virtual ~UpdateDAMergeTask() {};

	static PCWSTR GetTag();
	static DataAnalyzeTaskType GetType();
	HRESULT operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output) override;
};

class CancelDAMergeTask : public DataAnalyzeTaskBase
{
public:
	CancelDAMergeTask(KEtWorkbook* wwb) : DataAnalyzeTaskBase(wwb) {};
	virtual ~CancelDAMergeTask() {};

	static PCWSTR GetTag();
	static DataAnalyzeTaskType GetType();
	HRESULT operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output) override;
};

class UpdateDAMergeLogTask : public DataAnalyzeTaskBase
{
public:
	UpdateDAMergeLogTask(KEtWorkbook* wwb) : DataAnalyzeTaskBase(wwb) {};
	virtual ~UpdateDAMergeLogTask() {};

	static PCWSTR GetTag();
	static DataAnalyzeTaskType GetType();
	HRESULT operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output) override;
};

}
#endif