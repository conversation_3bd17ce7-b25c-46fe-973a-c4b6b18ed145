﻿#include "etstdafx.h"
#include "etapplication.h"
#include "uilogic/et_uilogic_global.h"
#include <kpaint/graphics_system.h>

namespace wo
{

KEtApplication::KEtApplication(WebLogFunc logFunc)
{
    // 创建app时argv会被修改，如果argv使用全局变量会导致后面创建的app的argv不正确
    int argc = 3;
    char* argv[] = { "", "-platform", "offscreen" };
	m_consoleApp.reset(new et::KConApplication("et", argc, argv));

	m_Entrys.reset(new KEntrys());
	m_consoleApp->setCoreApplication(m_Entrys->coreEntry()->GetApplication());

	ks_stdptr<IKCoreObject> spCoreAppDisp;
	m_ptrCoreApp = m_consoleApp->coreApplication();
	m_ptrCoreApp->GetDispObj(&spCoreAppDisp);
	spCoreAppDisp->QueryInterface(__iid(IKApplication), (void**)&m_ptrApp);
	

	m_Workbooks.reset(new KEtWorkbooks(m_consoleApp.get(), logFunc));
	m_Workbooks->SetApp(m_ptrApp);
	m_AtExit.reset(new KAtExit());

	m_Workbooks->setStaleNotify(this);
}

KEtApplication::~KEtApplication()
{
	m_Workbooks.reset();
	m_ptrApp->Destroy();
	
	//有个地方多放了一次引用，不知道为啥
	m_ptrApp.detach();

	m_Entrys.reset();
	m_consoleApp.reset();
}

KEtWorkbooks* KEtApplication::GetWorkbooks()
{
	return m_Workbooks.get();
}

IKEtApplication* KEtApplication::getCoreApp()
{
	return m_ptrCoreApp;
}

void KEtApplication::onStale(const WebID objId)
{
	if (m_Workbooks.get()) {
		m_Workbooks->notifyStale(objId);
	}
}

void KEtApplication::runTidyMemUsageTask(const int tidyImgPoolThreshold)
{
	if (tidyImgPoolThreshold <= 0)
		return;

	constexpr int seconds = 60;
	constexpr bool repeat = true;
	m_intervalTaskMgr.expiredAt(seconds, repeat, [this, tidyImgPoolThreshold](KEtWorkbook* wb, bool isMasterProc) -> void {
		if (!isMasterProc)
			return;

		qint64 memusage = static_cast<qint64>(kpt::chooseSystem(kpt::RasterGraphics).imagePoolMemUsage());
		if (memusage < tidyImgPoolThreshold)
			return;

		{
			wo::util::CallTimeStat callTime("tidy_imagepool");
			kpt::TidyMemoryUsageInfo info;
			info.m_woTidyImagePoolThreshold = std::max(static_cast<qint64>(tidyImgPoolThreshold), memusage / 2);
			kpt::TidyMemoryUsageInfo::PERFMEMORYSTATUS perfMemoryStatus = kpt::TidyMemoryUsageInfo::PERFMEMORYSTATUS::Memory_Status_Others;
			unsigned long long nCurrentPrivateUsage = 0;
			kpt::tidyMemoryUsage(info, perfMemoryStatus, nCurrentPrivateUsage);
		}

		if (static_cast<qint64>(kpt::chooseSystem(kpt::RasterGraphics).imagePoolMemUsage()) < tidyImgPoolThreshold)
		{
			WOLOG_INFO << "[tidy_imagepool]: complete";
		}
	});
}

// IntervalTaskManager
void IntervalTaskManager::OnIdle(KEtWorkbook* wb, bool isMasterProc)
{
	if (m_tasks.empty())
		return;

	constexpr int tickUnitSecs = 60;
	std::chrono::steady_clock::time_point curTime = std::chrono::steady_clock::now();
	int elapsed_secs = static_cast<int>((curTime - m_lastTime) / std::chrono::seconds(1));
	if (elapsed_secs >= tickUnitSecs)
	{
		for (auto it = m_tasks.begin(); it != m_tasks.end();)
		{
			IntervalTask& t = *it;
			t.expired_secs -= elapsed_secs;
			if(t.expired_secs <= 0)
			{
				t.task(wb, isMasterProc);
				if (t.repeat)
				{
					t.expired_secs = t.secs;
					++it;
				}
				else
				{
					it = m_tasks.erase(it);
				}
			}
		}

		m_lastTime = curTime;
	}
}

} // namespace wo