﻿/* -------------------------------------------------------------------------
//	文件名		：	smart_tips.h
//	创建者		：	徐建栋
//	创建时间	：	2004-6-17 15:49:46
//	功能描述	：	
//
// -----------------------------------------------------------------------*/
#ifndef __WO_SMART_TIPS_H__
#define __WO_SMART_TIPS_H__

// -------------------------------------------------------------------------
#include "funclib/et_funclib_forstatus.h"

class KApplication;
enum ForStatusIndex;
class KIdleUpdateHelper;
class KSmartTips
{
public:
	typedef std::vector<double>				DOUBLE_VEC;
	typedef std::vector<bool>				BOOL_VEC;
	typedef std::vector<double>::iterator	DOUBLE_IT;
	typedef std::vector<bool>::iterator		BOOL_IT;
	typedef std::vector<ks_wstring> WO_RESULT_VEC;
	enum ResultMode
	{
		rm_Normal,
		rm_cnRead,	// 中文简读
		rm_Comma,	// 千分分隔
		rm_cnComma,	// 万分分隔
		rm_jpRead,	// 日文读法
		rm_ActiveCellBased,  //用当前活动单元格格式
	};
public:
	KSmartTips();
	~KSmartTips();
	void Init(IKEtApplication* pEtApp);

	HRESULT UpdateSmartTip();
	HRESULT GetResultString(ks_bstr& resultString);
	HRESULT GetSmartTip(ks_bstr& smartTip);
	void GetStatusBarText(ks_bstr& sTip);
	void SetIsSpecialSmartTips(BOOL bv);
	void Term();
	BOOL NeedMoreUpdate();
	// for weboffice
	HRESULT WO_GetTips(IKRanges* pranges, WO_RESULT_VEC& result, DOUBLE_VEC& dbResult);
protected:
	HRESULT GetTips(IKRanges*, IBookOp*, /*et_applogic::SmartTipsType*/UINT,
		ks_wstring&);
	HRESULT GetTips2(IKRanges*, IBookOp*, /*et_applogic::SmartTipsType*/UINT, 
		ks_wstring&, ks_wstring*);
	HRESULT GetTipValue(IBookOp*, IDX, range_helper::ranges&, DOUBLE_VEC&,
		BOOL_VEC&);
	HRESULT GetDoubleValue(const alg::ExecToken* pToken, double& dValue);

	HRESULT FormatDouble(bool bError, bool b1904, double dbValue, ks_wstring& result);
	void	AddToResult(const alg::ExecToken*, DOUBLE_VEC&,
		BOOL_VEC&, bool bFirstGp = false);
	void	GetTokenValue(const alg::ExecToken*, ForStatusIndex,
		DOUBLE_IT, BOOL_IT, bool bFirstGp = false);
	// hlt:增加数字格式转大写
	HRESULT FormatToken(double dvalue, ResultMode, bool b1904, ks_wstring&, BOOL bCheck = TRUE);
	HRESULT Format_cnRead(double, ks_wstring& result);
	HRESULT Format_Comma(double, bool b1904, ks_wstring& result);
	HRESULT Format_cnComma(double, ks_wstring& result);
	HRESULT Format_Normal(double, bool b1904, ks_wstring& result);
	HRESULT Format_jpRead(double, bool b1904, ks_wstring& result);
	HRESULT Format_ActiveCellBased(double, bool b1904, ks_wstring& result);
	HRESULT FormatNumber(double, kfc::nf::NFHANDLE, bool b1904, ks_wstring&);

	//是否要加位数szDigitTest 数字字符串， nLen 数字的长度，nSaveLen 判断的保留长度
	//如果在 [nSaveLen，nLen）范围有不为0的，就返回true，否则返回false,
	bool IsAppDecimals(LPCWSTR szDigitTest, int nLen, int nSaveLen);

protected:
	UINT _GetVisibleTypes();
	UINT _GetTipsStyle();
	ResultMode _GetTipsMode();

	interface IBook* _GetActiveBook();

protected:
	ks_stdptr<IKEtApplication>	m_spEtApp;
	ks_stdptr<etoldapi::_Application>		m_spApiApp;
	BOOL				m_bIsSpecificVersion;
	ks_wstring			m_strResultString; // 为特殊区域版本专用
	ks_wstring			m_strTips;

	kfc::nf::NFHANDLE	m_nfhComma;		// #,##0
	kfc::nf::NFHANDLE	m_nfhComma2;	// #,##0.0##############
	kfc::nf::NFHANDLE	m_nfhNormal;	// general
	kfc::nf::NFHANDLE	m_nfhJPRead1;	// general
	kfc::nf::NFHANDLE	m_nfhJPRead2;	// general
	et_sptr<KIdleUpdateHelper> m_spIdleUpdateHelper;
};


// -------------------------------------------------------------------------

#endif /* __SMART_TIPS_H__ */
