﻿#include "webhook_data_validator.h"
#include "webbase/binvariant/binwriter.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "appcore/et_appcore_dbsheet.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "webhook/webhook_common.h"

namespace webhook_data_validator
{

// erase_if since C++ 20
template <typename C, typename Pred>
inline typename std::decay_t<C>::size_type erase_if(C &&c, Pred &&pred) {
    const auto old_size {c.size()};
    for(auto first {c.cbegin()}, last {c.cend()}; first not_eq last;) {
        if(std::forward<Pred>(pred)(*first)) {
            first = c.erase(first);
        }else {
            ++first;
        }
    }
    return old_size - c.size();
}

template <typename Validator>
std::pair<bool, PCWSTR> validateAll(const IWebhookData *pData, IBook *pBook, settings &s, Validator v)
{
    static_assert(std::is_same_v<Validator, WebhookDataValidator>);
    return v(pData, pBook, s);
}
template <typename Validator, typename ...Validators>
std::pair<bool, PCWSTR> validateAll(const IWebhookData *pData, IBook *pBook, settings &s, Validator v, Validators ...vs)
{
    static_assert(std::is_same_v<Validator, WebhookDataValidator>);
    const auto result {v(pData, pBook, s)};
    if (!result.first)
        return result;
    return validateAll(pData, pBook, s, vs...);
}

std::pair<bool, PCWSTR> sheetId(const IWebhookData *pData, IBook *pBook, settings &s)
{
    DbWebhookRange range {};
    pData->GetRange(&range);
    if (range.sheetId == INVALIDIDX)
        return {false, __X("sheetId is invalid")};
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(range.sheetId, &spSheet);
    if (!spSheet)
        return {false, __X("sheetId is invalid")};
    if (!spSheet->IsDbSheet())
        return {false, __X("sheet is not dbSheet")};
    return {true, {}};
}

std::pair<bool, PCWSTR> sheetIds(const IWebhookData *pData, IBook *pBook, settings &s)
{
    if (pData->GetAllSheets())
        return {true, {}};
    // 从原来逻辑来看, 目前退化到了只支持单 sheet
    if (pData->GetSheetIdsSize() > 1)
        return {false, __X("sheetIds unsupport array")};
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> viewId(const IWebhookData *pData, IBook *pBook, settings &s)
{
    DbWebhookRange range {};
    pData->GetRange(&range);
    ks_stdptr<IDBSheetViews> spDbSheetViews;
    wo::DBSheetCommonHelper commonHelper(pBook);
    // 这里之前假设了 sheet 一定存在
    commonHelper.GetDBSheetViews(range.sheetId, &spDbSheetViews);
    if (spDbSheetViews)
    {
        ks_stdptr<IDBSheetView> spDbSheetView;
        spDbSheetViews->GetItemById(range.viewId, &spDbSheetView);
        if(spDbSheetView == nullptr) {
            return {false, __X("viewId is invalid")};
        }
    }
    return {true, {}};
}

std::pair<bool, PCWSTR> fieldId(const IWebhookData *pData, IBook *pBook, settings &s)
{
    DbWebhookRange range {};
    pData->GetRange(&range);
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(range.sheetId, &spSheet);
    ASSERT(spSheet);
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    wo::DbSheet::GetDBSheetOp(spSheet, &spDbSheetOp);
    ASSERT(spDbSheetOp);
    if (spDbSheetOp->GetAllFields()->Id2Idx(*range.fieldIds.cbegin()) == INV_EtDbIdx)
        return {false, __X("fieldId is invalid")};
    return {true, {}};
}

std::pair<bool, PCWSTR> fieldIds(const IWebhookData *pData, IBook *pBook, settings &s)
{
    const auto range {pData->GetDbRange()};
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(range->sheetId, &spSheet);
    ASSERT(spSheet);
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    wo::DbSheet::GetDBSheetOp(spSheet, &spDbSheetOp);
    ASSERT(spDbSheetOp);
    const auto pAllFields {spDbSheetOp->GetAllFields()};
    erase_if(range->fieldIds, [pAllFields](EtDbId id) {
        if (pAllFields->Id2Idx(id) == INV_EtDbIdx)
        {
            WOLOG_ERROR << "[FieldIdsValidator] fieldId invalid ";
            return true;
        }
        return false;
    });
    if (range->fieldIds.empty() && static_cast<const settings_fieldIds &>(s).empty)
        return {true, {}};
    return {false, __X("fieldIds is invalid")};
}

std::pair<bool, PCWSTR> actionFieldIds(const IWebhookData *pData, IBook *pBook, settings &s)
{
    const auto queryRange {pData->GetQueryRange()};
    for (auto &fields : *queryRange)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(fields.first, &spSheet);
        if(spSheet == nullptr) {
            WOLOG_ERROR << "queryRange sheetId is invalid";
            continue;
        }
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        wo::DbSheet::GetDBSheetOp(spSheet, &spDbSheetOp);
        if(spDbSheetOp == nullptr) {
            WOLOG_ERROR << "queryRange sheetId is invalid";
            continue;
        }
        const auto pAllFields {spDbSheetOp->GetAllFields()};
        erase_if(fields.second, [pAllFields](EtDbId id) {
            if (pAllFields->Id2Idx(id) == INV_EtDbIdx)
            {
                WOLOG_ERROR << "[ActionFieldIdsValidator] fieldId invalid ";
                return true;
            }
            return false;
        });
    }
    if(queryRange->empty() and not static_cast<settings_queryRanges &>(s).empty) {
        return {false, {__X("queryRange is empty")}};
    }
    return {true, {}};
}

std::pair<bool, PCWSTR> recordIds(const IWebhookData *pData, IBook *pBook, settings &s)
{
    const auto range {pData->GetDbRange()};
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(range->sheetId, &spSheet);
    ASSERT(spSheet);
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    // 原本的逻辑假设了此处的 op 不为空, 没有判断, 这里也不判断
    wo::DbSheet::GetDBSheetOp(spSheet, &spDbSheetOp);
    ASSERT(spDbSheetOp);
    const auto pAllFields {spDbSheetOp->GetAllRecords()};
    erase_if(range->fieldIds, [pAllFields](EtDbId id) {
        if (pAllFields->Id2Idx(id) == INV_EtDbIdx)
        {
            WOLOG_ERROR << "[RecordIdsValidator] record invalid ";
            return true;
        }
        return false;
    });
    if (range->fieldIds.empty() && not static_cast<const settings_fieldIds &>(s).empty)
        return {false, __X("recordIds is invalid")};
    return {true, {}};
}

std::pair<bool, PCWSTR> updateSheetDescription(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> updateSheetIcon(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> updateSheet(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, formulaChanged, skipAfterMatchCreateAndFill);
}

std::pair<bool, PCWSTR> renameSheet(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> removeSheet(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> createView(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> renameView(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, viewId);
}

std::pair<bool, PCWSTR> removeView(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, viewId);
}

std::pair<bool, PCWSTR> createField(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> updateField(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, fieldId);
}

std::pair<bool, PCWSTR> updatePrimaryField(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> removeField(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, fieldId);
}

std::pair<bool, PCWSTR> createRecord(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return sheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> updateRecords(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, fieldIds, actionFieldIds,
            formulaChanged, skipAfterMatchCreateAndFill);
}

std::pair<bool, PCWSTR> removeRecord(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, recordIds);
}

std::pair<bool, PCWSTR> updateCells(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, recordIds, fieldIds, actionFieldIds,
            formulaChanged, skipAfterMatchCreateAndFill);
}

std::pair<bool, PCWSTR> updateFieldCells(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, fieldIds, actionFieldIds,
            formulaChanged, skipAfterMatchCreateAndFill);
}

std::pair<bool, PCWSTR> updateRecordCells(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, recordIds,
            formulaChanged, skipAfterMatchCreateAndFill);
}

std::pair<bool, PCWSTR> updateRecordsParent(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetId, recordIds);
}

std::pair<bool, PCWSTR> updateSheetsAllChanged(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return validateAll(pData, pBook, s, sheetIds, formulaChanged, userId);
}

std::pair<bool, PCWSTR> createAndFillInRecord(const IWebhookData *pData, IBook *pBook, settings &s)
{
    auto result {validateAll(pData, pBook, s, sheetId, fieldIds)};
    if(!result.first && !result.second)
        return {true, {}};
    return actionFieldIds(pData, pBook, s);
}

std::pair<bool, PCWSTR> etSheetId(const IWebhookData *pData, IBook *pBook, settings &s)
{
    EtWebhookRange range {};
    pData->GetRange(&range);
    if (range.sheetId == INVALIDIDX)
        return {false, __X("sheetId is invalid")};
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(range.sheetId, &spSheet);
    if (!spSheet)
        return {false, __X("sheetId is invalid")};
    if (!spSheet->IsGridSheet())
        return {false, __X("sheet is not et")};
    return {true, nullptr};
}

std::pair<bool, PCWSTR> etSheetIds(const IWebhookData *pData, IBook *pBook, settings &s)
{
    // 从原来逻辑来看, 目前退化到了只支持单 sheet, 目前仍然保留该校验函数
    if (pData->GetSheetIdsSize() > 1)
        return {false, __X("sheetIds unsupport array")};
    return etSheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> etRowColumnIndexArray(const IWebhookData *pData, IBook *pBook, settings &s)
{
    if (pData->GetExtraReturnRowColsSize() == 0)
        return {false, __X("returnExtraCellValueCols is invalid")};
    return {true, {}};
}

std::pair<bool, PCWSTR> etUpdateSheet(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return etSheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> etUpdateRange(const IWebhookData *pData, IBook *pBook, settings &s)
{
    if (pData->GetExtraReturnRowColsSize() > 0)
        return validateAll(pData, pBook, s, etSheetId, etRange, etRowColumnIndexArray);
    return validateAll(pData, pBook, s, etSheetId, etRange);
}

std::pair<bool, PCWSTR> etUpdateRanges(const IWebhookData *pData, IBook *pBook, settings &s)
{
    const auto pRangesVector {pData->GetEtRanges()};
    if(pRangesVector->empty()) {
        return {false, {__X("no ranges!")}};
    }
    static_cast<settings_etUpdateRanges &>(s).errorNumber = erase_if(*pRangesVector, [pBook, &s](const auto &range) {
        IDX sheetIndex {INVALIDIDX};
        pBook->STSheetToRTSheet(range.range.SheetFrom(), &sheetIndex);
        if (sheetIndex == INVALIDIDX)
        {
            WOLOG_ERROR << "[etUpdateRanges] sheetIdx INVALIDIDX " << range.uuid;
            return true;
        }
        return false;
    });
    return {true, {}};
}

std::pair<bool, PCWSTR> etUpdateCharts(const IWebhookData *pData, IBook *pBook, settings &s)
{
    const auto pChartsVector {pData->GetEtCharts()};
    std::unordered_map<IDX, bool> cache;
    for (auto it {pChartsVector->cbegin()}; it != pChartsVector->cend(); ++it)
    {
        IDX sheetIndex {INVALIDIDX};
        pBook->STSheetToRTSheet(it->sheetId, &sheetIndex);
        if (sheetIndex == INVALIDIDX)
        {
            WOLOG_ERROR << "[etUpdateCharts] sheetIdx INVALIDIDX ";
            it = pChartsVector->erase(it);
        }
        const auto result {cache.find(sheetIndex)};
        if (result == cache.cend())
        {
            ks_stdptr<ISheet> spSheet;
            pBook->GetSheet(sheetIndex, &spSheet);
            ks_stdptr<IKDrawingCanvas> spCanvas;
            spSheet->GetExtDataItem(edSheetDrawingCanvas, reinterpret_cast<IUnknown **>(&spCanvas));
            if (spCanvas == nullptr)
            {
                cache.emplace(sheetIndex, false);
                it = pChartsVector->erase(it);
                ++static_cast<settings_etUpdateCharts &>(s).errorNumber;
                continue;
            }
            ks_castptr<drawing::ShapeTree> cpTree {spCanvas};
            if (cpTree == nullptr)
            {
                cache.emplace(sheetIndex, false);
                it = pChartsVector->erase(it);
                ++static_cast<settings_etUpdateCharts &>(s).errorNumber;
                continue;
            }
            const auto pShape {cpTree->getAbsShape(it->uuid)};
            if (pShape == nullptr)
            {
                cache.emplace(sheetIndex, false);
                it = pChartsVector->erase(it);
                ++static_cast<settings_etUpdateCharts &>(s).errorNumber;
                continue;
            }
            cache.emplace(sheetIndex, true);
        }
        else
        {
            if (!cache[sheetIndex])
            {
                it = pChartsVector->erase(it);
                ++static_cast<settings_etUpdateCharts &>(s).errorNumber;
            }
        }
    }
    if(pChartsVector->empty()) {
        return {false, __X("chartUUIDs is empty!")};
    }
    return {true, {}};
}

std::pair<bool, PCWSTR> etRemoveSheet(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return etSheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> etInsertRows(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return etSheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> etRemoveRows(const IWebhookData *pData, IBook *pBook, settings &s)
{
    if (pData->GetExtraReturnRowColsSize() > 0)
        return validateAll(pData, pBook, s, etSheetId, etRowColumnIndexArray);
    return etSheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> etInsertColumns(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return etSheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> etRemoveColumns(const IWebhookData *pData, IBook *pBook, settings &s)
{
    if (pData->GetExtraReturnRowColsSize() > 0)
        return validateAll(pData, pBook, s, etSheetId, etRowColumnIndexArray);
    return etSheetId(pData, pBook, s);
}

std::pair<bool, PCWSTR> etUpdateSheetsAllChanged(const IWebhookData *pData, IBook *pBook, settings &s)
{
    return etSheetIds(pData, pBook, s);
}

}