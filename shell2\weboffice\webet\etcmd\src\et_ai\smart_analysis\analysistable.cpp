﻿/* -------------------------------------------------------------------------
//	文件名		：	dataanalysis.cpp
//	创建者		：
//	创建时间	：
//	功能描述	：
//
// -----------------------------------------------------------------------*/
#include "etstdafx.h"
#include "analysistable.h"
#include "pivot_core/pivot_core_x.h"
#include "kso/api/smartparam.h"
#include "funclib/et_funclib_forstatus.h"
#include "identifytable.h"
#include "etbase/etbase.h"
// #include "kwebshape.h"
#include "opl/et_opl_itf_core.h"
#include <public_header/drawing/text/text_define.h>
#include "uilogic/et_uilogic_basic_ift.h"
// #include "kaiethelper.h"

#define MaxFieldItemCount 50	// 默认显示最多50项
#define IncludePredValueMin 0.1	// 包含关系的预值，小于该值则不是包含关系的字段
#define NotNumberFieldCount 6	// 字段列表排列顺序默认先放几个维度
#define NumberFieldCount 4		// 字段列表排列顺序默认再放几个度量
#define MaxPivotTableCells 30000	// 获取解读数据最多支持单元格限制
#define MaxInsertCol 10000		// 插入解读的最大列数，容错用的
#define ChartWidth 360.0
#define ChartHeight 216.0
static const QString staticStrDateUnitYear = krt::fromUtf16(__X("年"));
static const QString staticStrDateUnitMonth = krt::fromUtf16(__X("月"));
static const QString staticStrDateUnitDay = krt::fromUtf16(__X("日"));
static const QString staticStrDateUnitMaoHao = krt::fromUtf16(__X(":"));
static const QString staticStrNormalFontName = krt::fromUtf16(__X("微软雅黑"));
static const QString staticStrTableSheet = krt::fromUtf16(__X("（规范后）"));
static const QString staticStrAnalysisSheet = krt::fromUtf16(__X("分析"));
static const QString staticStrSum = krt::fromUtf16(__X("求和"));
static const QString staticStrCount = krt::fromUtf16(__X("计数"));
static const QString staticStrAverage = krt::fromUtf16(__X("平均值"));
static const QString staticStrMax = krt::fromUtf16(__X("最大值"));
static const QString staticStrMin = krt::fromUtf16(__X("最小值"));
static const QString staticStrUnderstand = krt::fromUtf16(__X("数据解读"));
static const QString staticStrEmpty = krt::fromUtf16(__X("(空白)"));
namespace etai
{
namespace kidentifytable
{
	// IMPLEMENT_CLSID(KRanges, "078D596F-B1D5-41e9-A9D5-E271ECCCCFAF",
	// 	0x78d596f, 0xb1d5, 0x41e9, 0xa9, 0xd5, 0xe2, 0x71, 0xec, 0xcc, 0xcf, 0xaf);

	// IMPLEMENT_IID(IKRanges, "E2BDB95D-ABC5-4cbc-B135-BAE474A0BCC2",
	// 	0xe2bdb95d, 0xabc5, 0x4cbc, 0xb1, 0x35, 0xba, 0xe4, 0x74, 0xa0, 0xbc, 0xc2);

	BOOL IsObjectSelected(IKApplication* pApp)
	{
		if (!pApp)
			return false;

		ks_castptr<IKEtView> pETView = pApp->GetActiveView();
		if (!pETView)
			return FALSE;

		ks_castptr<IKSelection> spSel = pETView->GetSelection();
		if (!spSel)
			return FALSE;

		KsoSelectionType st = 0;
		spSel->GetType(&st);
		st = KsoSelectionTypeMajor(st);

		switch (st)
		{
		case ksoselectiondrawing:
		case ksoselectionchart:
			return TRUE;
			break;
		default:
			return FALSE;
			break;
		}

		return FALSE;
	}
}

HRESULT CreateRange(IKWorksheet* pSheet, const RANGE& rg, etoldapi::Range** ppRg)
{
	if (!pSheet || !ppRg)
		return E_FAIL;
	HRESULT hr = S_OK;
	ks_stdptr<IKRanges> spRgs;
	hr = _etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRgs);
	if (FAILED(hr) || !spRgs)
		return E_FAIL;
	hr = spRgs->Append(alg::STREF_THIS_BOOK, rg);
	if (FAILED(hr))
		return E_FAIL;
	ks_stdptr<etoldapi::Range> spRange;
	hr = pSheet->GetRangeByData(spRgs, &spRange);
	if (FAILED(hr) || !spRange)
		return E_FAIL;
	*ppRg = spRange;
	spRange->AddRef();
	return S_OK;
}

KDataFieldItem::KDataFieldItem(QString name, long cnt)
	: strName(name), nRecordCount(cnt)
{
}

QString KDataFieldItem::GetName() const
{
	return strName;
}

long KDataFieldItem::GetRecordCount() const
{
	return nRecordCount;
}

void KDataFieldItem::AddCount(long cnt)
{
	nRecordCount += cnt;
}

KDataFieldItemList::KDataFieldItemList()
{
}

KDataFieldItemList::~KDataFieldItemList()
{
	ClearAll();
}

void KDataFieldItemList::ClearAll()
{
	for (auto it = this->begin(); it != this->end(); ++it)
	{
		if (*it)
		{
			delete *it;
			*it = NULL;
		}
	}
	std::vector<KDataFieldItem*>::clear();
}

KDataFieldItem* KDataFieldItemList::FindByName(QString strName)
{
	for (auto it = this->begin(); it != this->end(); ++it)
		if (*it && (*it)->GetName() == strName)
			return *it;
	return NULL;
}

const KDataFieldItem* KDataFieldItemList::FindByName(QString strName) const
{
	for (auto it = this->begin(); it != this->end(); ++it)
		if (*it && (*it)->GetName() == strName)
			return *it;
	return NULL;
}

KDataField::KDataField(int i, QJsonObject& jsonColumn, QString name)
	: m_nIdx(i), m_dPredValue(0.0), m_strName(name), m_nType(DataFieldTypeString)
{
	Init(jsonColumn);
}

KDataField::KDataField(int i, QString name)
	: m_nIdx(i), m_dPredValue(0.0), m_strName(name), m_nType(DataFieldTypeString)
{
}

KDataField::~KDataField()
{

}

static bool moreRelatedPredValue(const std::pair<QString, double>& lhs, const std::pair<QString, double>& rhs)
{
	if (lhs.second > rhs.second)
		return true;
	return false;
}

KDataFieldTypeEnum TransformType(QString strClassType)
{
	if (strClassType == krt::fromUtf16(__X("Date")) || strClassType == krt::fromUtf16(__X("DateTime")))
		return DataFieldTypeDate;
	if (strClassType == krt::fromUtf16(__X("Number")) || strClassType == krt::fromUtf16(__X("PercentNumber")))
		return DataFieldTypeNumber;
	return DataFieldTypeString;
}

QString GetTypeString(KDataFieldTypeEnum nType)
{
	switch (nType)
	{
	case DataFieldTypeDate:
		return krt::fromUtf16(__X("Date"));
	case DataFieldTypeNumber:
		return krt::fromUtf16(__X("Number"));
	}
	return krt::fromUtf16(__X("String"));
}

void KDataField::Init(QJsonObject& jsonColumn)
{
	if (!jsonColumn.contains("classType") || !jsonColumn.contains("indexName"))
		return;
	m_strClassType = jsonColumn["classType"].toString();
	m_strIdentifyType = jsonColumn["identifyType"].toString();
	m_strIdx = jsonColumn["indexName"].toString();
	if (jsonColumn.contains("predValue"))
		m_dPredValue = jsonColumn["predValue"].toDouble();
}

int KDataField::GetIndexInt() const
{
	return m_nIdx;
}

QString KDataField::GetIndex() const
{
	return m_strIdx;
}

QString KDataField::GetName() const
{
	return m_strName;
}

QString KDataField::GetClassType() const
{
	return m_strClassType;
}

QString KDataField::GetIdentifyType() const
{
	return m_strIdentifyType;
}

KDataFieldTypeEnum KDataField::GetType() const
{
	return m_nType;
}

QString KDataField::GetTypeStr() const
{
	return GetTypeString(m_nType);
}

double KDataField::GetPredValue() const
{
	return m_dPredValue;
}

BOOL KDataField::IsNumberField() const
{
	return FALSE;
}

void KDataField::ExportJson(QJsonObject& jsonObj, int nMaxItemCnt) const 
{
	jsonObj.insert("name", m_strName);
	jsonObj.insert("type", GetTypeString(m_nType));
	jsonObj.insert("index", m_nIdx + 1);
}

KNumberField::KNumberField(int i, QJsonObject& jsonColumn, QString name)
	: KDataField(i, jsonColumn, name), m_dSum(0.0), m_dAverage(0.0), m_dMax(0.0), m_dMin(0.0), m_bPercent(false), m_nNumType(oldapi::etSum), m_nUsedNumType(oldapi::etSum)
{
	if (jsonColumn.contains("method") && jsonColumn["method"].toString() == "ave")
	{
		m_nNumType = oldapi::etAverage;
		m_nUsedNumType = m_nNumType;
	}
	m_nType = DataFieldTypeNumber;
	if (m_strClassType == krt::fromUtf16(__X("PercentNumber")))
		m_bPercent = true;
}

KNumberField::KNumberField(int i, QString name, bool is, oldapi::ETConsolidationFunction type)
	: KDataField(i, name), m_dSum(0.0), m_dAverage(0.0), m_dMax(0.0), m_dMin(0.0), m_bPercent(is), m_nNumType(type), m_nUsedNumType(type)
{
	m_nType = DataFieldTypeNumber;
}

KNumberField::~KNumberField()
{

}

void KNumberField::SetSum(double sum)
{
	m_dSum = sum;
}

double KNumberField::GetSum() const
{
	return m_dSum;
}

void KNumberField::SetAverage(double avg)
{
	m_dAverage = avg;
}

double KNumberField::GetAverage() const
{
	return m_dAverage;
}

void KNumberField::SetMax(double max)
{
	m_dMax = max;
}

double KNumberField::GetMax() const
{
	return m_dMax;
}

void KNumberField::SetMin(double min)
{
	m_dMin = min;
}

double KNumberField::GetMin() const
{
	return m_dMin;
}

bool KNumberField::IsPercent() const
{
	return m_bPercent;
}

oldapi::ETConsolidationFunction KNumberField::GetNumberFieldType() const
{
	return m_nNumType;
}

oldapi::ETConsolidationFunction KNumberField::GetUsedNumberFieldType() const
{
	return m_nUsedNumType;
}

void KNumberField::SetUsedNumberFieldType(oldapi::ETConsolidationFunction numType)
{
	m_nUsedNumType = numType;
}

BOOL KNumberField::IsNumberField() const
{
	return TRUE;
}

QString TransformNumberFieldType(oldapi::ETConsolidationFunction type)
{
	switch (type)
	{
	case oldapi::etAverage:
		return krt::fromUtf16(__X("average"));
	case oldapi::etMax:
		return krt::fromUtf16(__X("max"));
	case oldapi::etMin:
		return krt::fromUtf16(__X("min"));
	}
	return krt::fromUtf16(__X("sum"));
}

void KNumberField::ExportJson(QJsonObject& jsonObj, int nMaxItemCnt) const
{
	KDataField::ExportJson(jsonObj, nMaxItemCnt);
	jsonObj.insert("sum", m_dSum);
	jsonObj.insert("average", m_dAverage);
	jsonObj.insert("max", m_dMax);
	jsonObj.insert("min", m_dMin);
	jsonObj.insert("numNormal", TransformNumberFieldType(m_nNumType));
	jsonObj.insert("per", m_bPercent);
}

void KNumberField::ResetState()
{
// 	m_nUsedNumType = m_nNumType;
}

KStringField::KStringField(int i, QJsonObject& jsonColumn, QString name)
	: KDataField(i, jsonColumn, name), m_bDescending(true)
{
	m_nType = DataFieldTypeString;
}

KStringField::KStringField(int i, QString name)
	: KDataField(i, name), m_bDescending(true)
{
	m_nType = DataFieldTypeString;
}

KStringField::~KStringField()
{

}

long KStringField::GetSumCount() const
{
	long nCount = 0;
	for (auto it = m_itemList.begin(); it != m_itemList.end(); ++it)
		if (*it)
			nCount += (*it)->GetRecordCount();
	return nCount;
}

static bool moreItemCount(KDataFieldItem*& lhs, KDataFieldItem*& rhs)
{
	if (lhs->GetRecordCount() > rhs->GetRecordCount())
		return true;
	return false;
}

void KStringField::AddDataFieldItem(QString name, long cnt)
{
	KDataFieldItem* pItem = m_itemList.FindByName(name);
	if (pItem == NULL)
	{
		pItem = new KDataFieldItem(name, cnt);
		if (!pItem)
			return;
		m_itemList.push_back(pItem);
	}
	else
	{
		pItem->AddCount(cnt);
	}
}

const KDataFieldItemList& KStringField::GetDataFieldItemList() const
{
	return m_itemList;
}

int KStringField::GetItemCount() const
{
	return (int)m_itemList.size();
}

void KStringField::AddIncludeField(QString strField, double dPredValue)
{
	m_includeFieldList.push_back(std::make_pair(strField, dPredValue));
}

BOOL KStringField::IsIncludeField(QString strField) const
{
	for (auto it = m_includeFieldList.cbegin(); it != m_includeFieldList.cend(); ++it)
		if (it->first == strField)
			return TRUE;
	return FALSE;
}

void KStringField::ExportJson(QJsonObject& jsonObj, int nMaxItemCnt) const
{
	KDataField::ExportJson(jsonObj, nMaxItemCnt);
	jsonObj.insert("allCount", (int)m_itemList.size());
	QJsonArray jsonCellList;
	int i = 0;
	for (auto it = m_itemList.cbegin(); it != m_itemList.cend() && (nMaxItemCnt < 0 || i < nMaxItemCnt); ++it, ++i)
	{
		if (!*it)
			continue;
		const KDataFieldItem* pItem = *it;
		QJsonObject jsonCell;
		jsonCell.insert("value", pItem->GetName());
		jsonCell.insert("count", (qint64)pItem->GetRecordCount());
		jsonCellList.append(jsonCell);
	}
	jsonObj.insert("cellList", jsonCellList);
}

static bool lessItemCount(KDataFieldItem*& lhs, KDataFieldItem*& rhs)
{
	if (lhs->GetRecordCount() < rhs->GetRecordCount())
		return true;
	return false;
}

void KStringField::SortItemList(bool bDes)
{
	m_bDescending = bDes;
	if (bDes)
		std::sort(m_itemList.begin(), m_itemList.end(), moreItemCount);
	else
		std::sort(m_itemList.begin(), m_itemList.end(), lessItemCount);
}

void KStringField::ResetState()
{
	m_bDescending = true;
	std::sort(m_itemList.begin(), m_itemList.end(), moreItemCount);
}

void GetTypeByNumberFormat(QString strNF, bool& bYear, bool& bMonth, bool& bDay, bool& bHour, bool& bMinute)
{
	if (strNF.contains("yy"))
		bYear = true;
	if (strNF.contains("m"))
		bMonth = true;
	if (strNF.contains("d"))
		bDay = true;
	if (bYear && bMonth && bDay)
	{
		QString strTimeNF = strNF.mid(strNF.indexOf("d") + 1);
		if (strTimeNF.contains("h"))
			bHour = true;
		if (strTimeNF.contains("m"))
			bMinute = true;
	}
}

KDateFieldTypeEnum TransformDateType(bool bYear, bool bMonth, bool bDay, bool bHour, bool bMinute)
{
	if (bHour && bMinute && bDay)
		return DateFieldTypeYMDHM;
	if (bHour && bDay)
		return DateFieldTypeYMDH;
	if (bYear && bMonth && bDay)
		return DateFieldTypeYMD;
	if (bYear && bMonth)
		return DateFieldTypeYM;
	if (bMonth && bDay)
		return DateFieldTypeMD;
	if (bYear)
		return DateFieldTypeY;
	if (bMonth)
		return DateFieldTypeM;
	if (bDay)
		return DateFieldTypeD;
	return DateFieldTypeYMD;
}

KDateFieldTypeEnum GetDateFieldType(etoldapi::Range* pRange, int idx)
{
	if (!pRange)
		return DateFieldTypeYMD;
	ks_stdptr<etoldapi::Range> spColumns;
	HRESULT hr = pRange->get_Columns(&spColumns);
	if (FAILED(hr) || !spColumns)
		return DateFieldTypeYMD;
	ks_stdptr<etoldapi::Range> spRows;
	hr = pRange->get_Rows(&spRows);
	if (FAILED(hr) || !spRows)
		return DateFieldTypeYMD;
	long nRow = 0, nCol = 0;
	hr = spColumns->get_Count(&nCol);
	hr = spRows->get_Count(&nRow);
	if (idx < 0 || idx >= nCol || nRow < 2)
		return DateFieldTypeYMD;
	KComVariant varColIdx(idx + 1, VT_I4);
	bool bYear = false, bMonth = false, bDay = false, bHour = false, bMinute = false;
	for (long i = 2; i <= nRow; ++i)	// 跳过标题行
	{
		KComVariant varRowIdx(i, VT_I4);
		KComVariant varCell;
		hr = pRange->get_Item(varRowIdx, varColIdx, &varCell);
		if (FAILED(hr))
			continue;
		ks_stdptr<Range> spCell = KSmartParam(varCell).GetInterfaceValue();
		if (!spCell)
			continue;
		ks_bstr bstrNumberFormat;
		hr = spCell->get_NumberFormatLocal(&bstrNumberFormat);
		GetTypeByNumberFormat(krt::fromUtf16(bstrNumberFormat.c_str()), bYear, bMonth, bDay, bHour, bMinute);
		if (bYear && bMonth && bDay && bHour && bMinute)
			return DateFieldTypeYMDHM;
	}
	return TransformDateType(bYear, bMonth, bDay, bHour, bMinute);
}

KDateField::KDateField(int i, QJsonObject& jsonColumn, QString name, etoldapi::Range* pRange)
	: KStringField(i, jsonColumn, name), m_nDateType(DateFieldTypeYMD)
{
	m_nType = DataFieldTypeDate;
	m_bDescending = false;
	m_nDateType = GetDateFieldType(pRange, i);
}

KDateField::KDateField(int i, QString name, KDateFieldTypeEnum dateType)
	: KStringField(i, name), m_nDateType(dateType)
{
	m_nType = DataFieldTypeDate;
	m_bDescending = false;
}

KDateField::~KDateField()
{

}

static bool GetDateYMD(QString str, int& nYear, int& nMonth, int& nDay, int& nHour, int& nMinute)
{
	int iYear = str.indexOf(staticStrDateUnitYear);
	int iMonth = str.indexOf(staticStrDateUnitMonth);
	int iDay = str.indexOf(staticStrDateUnitDay);
	int iHour = str.indexOf(staticStrDateUnitMaoHao);
	QString strYear, strMonth, strDay, strHour, strMinute;
	// 仅数字
	if (iYear < 0 && iMonth < 0 && iDay < 0 && iHour < 0)
	{
		bool bOK = false;
		if (!str.isEmpty())
		{
			int y = str.toInt(&bOK);
			if (bOK)
			{
				nYear = y;
				return true;
			}
		}
		return false;
	}
	// y年m月d日h:m
	if (iYear > 0 && iMonth > 0 && iDay > 0 && iHour > 0)
	{
		strYear = str.mid(0, iYear);
		strMonth = str.mid(iYear + 1, iMonth - iYear - 1);
		strDay = str.mid(iMonth + 1, iDay - iMonth - 1);
		strHour = str.mid(iDay + 1, iHour - iDay - 1);
		strMinute = str.mid(iHour + 1);
	}
	// y年m月d日h
	else if (iYear > 0 && iMonth > 0 && iDay > 0 && iDay != str.size() - 1)
	{
		strYear = str.mid(0, iYear);
		strMonth = str.mid(iYear + 1, iMonth - iYear - 1);
		strDay = str.mid(iMonth + 1, iDay - iMonth - 1);
		strHour = str.mid(iDay + 1);
	}
	// y年m月d日
	else if (iYear > 0 && iMonth > 0 && iDay > 0)
	{
		strYear = str.mid(0, iYear);
		strMonth = str.mid(iYear + 1, iMonth - iYear - 1);
		strDay = str.mid(iMonth + 1, iDay - iMonth - 1);
	}
	// y年m月
	else if (iYear > 0 && iMonth > 0 && iDay < 0)
	{
		strYear = str.mid(0, iYear);
		strMonth = str.mid(iYear + 1, iMonth - iYear - 1);
	}
	// m月d日
	else if (iYear < 0 && iMonth > 0 && iDay > 0)
	{
		strMonth = str.mid(0, iMonth);
		strDay = str.mid(iMonth + 1, iDay - iMonth - 1);
	}
	// y年
	else if (iYear > 0 && iMonth < 0 && iDay < 0)
	{
		strYear = str.mid(0, iYear);
	}
	// m月
	else if (iYear < 0 && iMonth > 0 && iDay < 0)
	{
		strMonth = str.mid(0, iMonth);
	}
	// d日
	else if (iYear < 0 && iMonth < 0 && iDay > 0)
	{
		strDay = str.mid(0, iDay);
	}
	bool bOK = false, bReturn = false;
	if (!strYear.isEmpty())
	{
		int y = strYear.toInt(&bOK);
		if (bOK)
			nYear = y;
		bReturn |= bOK;
	}
	if (!strMonth.isEmpty())
	{
		int m = strMonth.toInt(&bOK);
		if (bOK)
			nMonth = m;
		bReturn |= bOK;
	}
	if (!strDay.isEmpty())
	{
		int d = strDay.toInt(&bOK);
		if (bOK)
			nDay = d;
		bReturn |= bOK;
	}
	if (!strHour.isEmpty())
	{
		int h = strHour.toInt(&bOK);
		if (bOK)
			nHour = h;
		bReturn |= bOK;
	}
	if (!strMinute.isEmpty())
	{
		int m2 = strMinute.toInt(&bOK);
		if (bOK)
			nMinute = m2;
		bReturn |= bOK;
	}
	return bReturn;
}

static bool moreDate(KDataFieldItem*& lhs, KDataFieldItem*& rhs)
{
	QString strLeft = lhs->GetName();
	QString strRight = rhs->GetName();
	int ly = 0, lm = 0, ld = 0, lh = 0, lm2 = 0, ry = 0, rm = 0, rd = 0, rh = 0, rm2 = 0;
	bool bl = GetDateYMD(strLeft, ly, lm, ld, lh, lm2);
	bool br = GetDateYMD(strRight, ry, rm, rd, rh, rm2);
	if (!bl && !br)
		return strLeft > strRight;
	if (!br)
		return true;
	if (!bl)
		return false;
	if (ly > ry || (ly == ry && lm > rm) || (ly == ry && lm == rm && ld > rd)
		|| (ly == ry && lm == rm && ld == rd && lh > rh) || (ly == ry && lm == rm && ld == rd && lh == rh && lm2 > rm2))
		return true;
	return false;
}

static bool lessDate(KDataFieldItem*& lhs, KDataFieldItem*& rhs)
{
	QString strLeft = lhs->GetName();
	QString strRight = rhs->GetName();
	int ly = 0, lm = 0, ld = 0, lh = 0, lm2 = 0, ry = 0, rm = 0, rd = 0, rh = 0, rm2 = 0;
	bool bl = GetDateYMD(strLeft, ly, lm, ld, lh, lm2);
	bool br = GetDateYMD(strRight, ry, rm, rd, rh, rm2);
	if (!bl && !br)
		return strLeft < strRight;
	if (!br)
		return true;
	if (!bl)
		return false;
	if (ly < ry || (ly == ry && lm < rm) || (ly == ry && lm == rm && ld < rd)
		|| (ly == ry && lm == rm && ld == rd && lh < rh) || (ly == ry && lm == rm && ld == rd && lh == rh && lm2 < rm2))
		return true;
	return false;
}

QString KDateField::CutString(QString strName)
{
	int y = 0, m = 0, d = 0, h = 0, m2 = 0;
	bool bOk = GetDateYMD(strName, y, m, d, h, m2);
	if (!bOk || 0 == y && 0 == m && 0 == d && 0 == h && 0 == m2)
		return strName;
	switch (m_nDateType)
	{
	case DateFieldTypeY:
		return QString::number(y) + staticStrDateUnitYear;
	case DateFieldTypeM:
		return QString::number(m) + staticStrDateUnitMonth;
	case DateFieldTypeD:
		return QString::number(d) + staticStrDateUnitDay;
	case DateFieldTypeYM:
		return QString::number(y) + staticStrDateUnitYear + QString::number(m) + staticStrDateUnitMonth;
	case DateFieldTypeMD:
		return QString::number(m) + staticStrDateUnitMonth + QString::number(d) + staticStrDateUnitDay;
	case DateFieldTypeYMD:
		return QString::number(y) + staticStrDateUnitYear + QString::number(m) + staticStrDateUnitMonth + QString::number(d) + staticStrDateUnitDay;
	case DateFieldTypeYMDH:
		return QString::number(y) + staticStrDateUnitYear + QString::number(m) + staticStrDateUnitMonth + QString::number(d) + staticStrDateUnitDay + QString::number(h);
	case DateFieldTypeYMDHM:
		return QString::number(y) + staticStrDateUnitYear + QString::number(m) + staticStrDateUnitMonth + QString::number(d) + staticStrDateUnitDay + QString::number(h) + staticStrDateUnitMaoHao + QString::number(m2);
	}
	return strName;
}

void KDateField::AddDataFieldItem(QString name, long cnt)
{
	KDataFieldItem* pItem = m_itemList.FindByName(name);
	if (pItem == NULL)
	{
		pItem = new KDataFieldItem(name, cnt);
		if (!pItem)
			return;
		m_itemList.push_back(pItem);
	}
	else
	{
		pItem->AddCount(cnt);
	}

	if (m_nDateType != DateFieldTypeYMD && m_nDateType != DateFieldTypeYMDHM)
	{
		name = CutString(name);
		KDataFieldItem* pDateItem = m_dateTypeItemList.FindByName(name);
		if (pDateItem == NULL)
		{
			pDateItem = new KDataFieldItem(name, cnt);
			if (!pDateItem)
				return;
			m_dateTypeItemList.push_back(pDateItem);
		}
		else
		{
			pDateItem->AddCount(cnt);
		}
	}
}

void KDateField::SortItemList(bool bDes)
{
	if (bDes)
	{
		std::sort(m_itemList.begin(), m_itemList.end(), moreDate);
		std::sort(m_dateTypeItemList.begin(), m_dateTypeItemList.end(), moreDate);
	}
	else
	{
		std::sort(m_itemList.begin(), m_itemList.end(), lessDate);
		std::sort(m_dateTypeItemList.begin(), m_dateTypeItemList.end(), lessDate);
	}
	m_bDescending = bDes;
}

void ExportDateFieldJson(const KDataFieldItemList& itemList, QJsonObject& jsonObj, int nMaxItemCnt)
{
	jsonObj.insert("allCount", (int)itemList.size());
	QJsonArray jsonCellList;
	int i = 0;
	for (auto it = itemList.cbegin(); it != itemList.cend() && (nMaxItemCnt < 0 || i < nMaxItemCnt); ++it, ++i)
	{
		if (!*it)
			continue;
		const KDataFieldItem* pItem = *it;
		QJsonObject jsonCell;
		jsonCell.insert("value", pItem->GetName());
		jsonCell.insert("count", (qint64)pItem->GetRecordCount());
		jsonCellList.append(jsonCell);
	}
	jsonObj.insert("cellList", jsonCellList);
}

void KDateField::ExportJson(QJsonObject& jsonObj, int nMaxItemCnt) const
{
	KDataField::ExportJson(jsonObj, nMaxItemCnt);
	if (m_dateTypeItemList.empty())
		ExportDateFieldJson(m_itemList, jsonObj, nMaxItemCnt);
	else
		ExportDateFieldJson(m_dateTypeItemList, jsonObj, nMaxItemCnt);
}

KDateFieldTypeEnum KDateField::GetDateTypeEnum() const
{
	return m_nDateType;
}

void KDateField::ResetState()
{
	m_bDescending = false;
	std::sort(m_itemList.begin(), m_itemList.end(), lessDate);
	std::sort(m_dateTypeItemList.begin(), m_dateTypeItemList.end(), lessDate);
}

KDataFieldList::KDataFieldList()
{
}

KDataFieldList::~KDataFieldList()
{
	ClearAll();
}

void KDataFieldList::ClearAll()
{
	for (auto it = this->begin(); it != this->end(); ++it)
	{
		if (*it)
		{
			delete *it;
			*it = NULL;
		}
	}
	std::vector<KDataField*>::clear();
}

KDataField* KDataFieldList::FindByName(QString str)
{
	for (auto it = this->begin(); it != this->end(); ++it)
		if (str == (*it)->GetName())
			return *it;
	return NULL;
}

KDataField* KDataFieldList::FindByIndex(QString str)
{
	for (auto it = this->begin(); it != this->end(); ++it)
		if (str == (*it)->GetIndex())
			return *it;
	return NULL;
}

const KDataField* KDataFieldList::FindByName(QString str) const
{
	for (auto it = this->cbegin(); it != this->cend(); ++it)
		if (str == (*it)->GetName())
			return *it;
	return NULL;
}

const KDataField* KDataFieldList::FindByIndex(QString str) const
{
	for (auto it = this->cbegin(); it != this->cend(); ++it)
		if (str == (*it)->GetIndex())
			return *it;
	return NULL;
}

static bool morePredValue(KDataField*& lhs, KDataField*& rhs)
{
	if (lhs->GetPredValue() > rhs->GetPredValue())
		return true;
	return false;
}

void KDataFieldList::SortByPredValue()
{
	std::sort(this->begin(), this->end(), morePredValue);
}

KAnalysisPivotTable::KAnalysisPivotTable(ks_stdptr<etoldapi::_Worksheet> spSheet, ks_stdptr<etoldapi::PivotTable> spPivotTable, KAnalysisTable* pTable)
	: m_spSheet(spSheet), m_spPivotTable(spPivotTable), m_pAnalysisTable(pTable), m_bInit(FALSE), m_pUnderstand(NULL), m_isRecommend(FALSE), m_isOnlyUsed(FALSE)
{
	if (m_spPivotTable && !m_spSheet)
		m_spPivotTable->get_Worksheet(&m_spSheet);
	if (m_spSheet)
		m_spBook = m_spSheet->GetWorkbook();
}

KAnalysisPivotTable::~KAnalysisPivotTable()
{
	if (m_pUnderstand)
	{
		delete m_pUnderstand;
		m_pUnderstand = NULL;
	}
}

KDataFieldList& KAnalysisPivotTable::GetAllDataField()
{
	return m_pAnalysisTable->GetAllDataField();
}

etoldapi::PivotTable* KAnalysisPivotTable::GetApiPivotTable()
{
	return m_spPivotTable;
}

void KAnalysisPivotTable::SetApiPivotTable(etoldapi::PivotTable* pPivotTable)
{
	m_spPivotTable = pPivotTable;
	Reset();
	if (!pPivotTable && m_pUnderstand)
		m_pUnderstand->ClearAll();
}

HRESULT KAnalysisPivotTable::InitFields(oldapi::ETPivotFieldOrientation efo, std::vector<const KDataField*>& fieldList)
{
	if (fieldList.size() > 0)
		fieldList.clear();
	if (!m_spPivotTable)
		return S_OK;

	HRESULT hr = S_OK;
	KComVariant var;
	ks_stdptr<etoldapi::PivotFields> spFields;
	hr = m_spPivotTable->AxisFields(efo, var, (IKCoreObject**)(&spFields));
	if (FAILED(hr) || !spFields)
		return E_FAIL;

	long nCnt = 0;
	hr = spFields->get_Count(&nCnt);
	for (long i = 1; i <= nCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<etoldapi::PivotField> spField;
		hr = spFields->Item(varIdx, &spField);
		if (FAILED(hr) || !spField)
			continue;
		ks_bstr bstrSourceName;
		hr = spField->get_SourceName(&bstrSourceName);
		if (FAILED(hr) || !bstrSourceName)
			continue;
		QString strSourceName = krt::fromUtf16(bstrSourceName.c_str());
		KDataField* pField = m_pAnalysisTable->GetAllDataField().FindByName(strSourceName);
		if (pField)
		{
			if (oldapi::etDataField == efo && pField->IsNumberField())
			{
				oldapi::ETConsolidationFunction nFunc = oldapi::etSum;
				spField->get_Function(&nFunc);
				KNumberField* pNumField = (KNumberField*)pField;
				pNumField->SetUsedNumberFieldType(nFunc);
			}
			fieldList.push_back(pField);
	}
	}
	return S_OK;
}

void CheckFieldList(std::vector<const KStringField*>& fieldList)
{
	for (int i = fieldList.size() - 1; i >= 0; --i)
		if (!fieldList[i])
			fieldList.erase(fieldList.begin() + i);
}

HRESULT KAnalysisPivotTable::InitFields(oldapi::ETPivotFieldOrientation efo, std::vector<const KStringField*>& fieldList)
{
	if (fieldList.size() > 0)
		fieldList.clear();
	if (!m_spPivotTable)
		return S_OK;

	HRESULT hr = S_OK;
	KComVariant var;
	ks_stdptr<etoldapi::PivotFields> spFields;
	hr = m_spPivotTable->AxisFields(efo, var, (IKCoreObject**)(&spFields));
	if (FAILED(hr) || !spFields)
		return E_FAIL;

	long nCnt = 0;
	hr = spFields->get_Count(&nCnt);
	std::vector<const KStringField*> tempFieldList(nCnt, nullptr);
	for (long i = 1; i <= nCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<etoldapi::PivotField> spField;
		hr = spFields->Item(varIdx, &spField);
		if (FAILED(hr) || !spField)
			continue;
		ks_bstr bstrSourceName;
		hr = spField->get_SourceName(&bstrSourceName);
		if (FAILED(hr) || !bstrSourceName)
			continue;
		QString strSourceName = krt::fromUtf16(bstrSourceName.c_str());
		const KDataField* pField = m_pAnalysisTable->GetAllDataField().FindByName(strSourceName);
		if (!pField || pField->IsNumberField())
			continue;
		int nPos = 0;
		KComVariant varPos;
		spField->get_Position(&varPos);
		if (V_VT(&varPos) == VT_I4)
			nPos = V_I4(&varPos);
		if (nPos > 0 && nPos <= nCnt)
			tempFieldList[nPos - 1] = (const KStringField*)pField;
	}
	CheckFieldList(tempFieldList);
	fieldList = tempFieldList;
	return S_OK;
}

void KAnalysisPivotTable::Init()
{
	if (!m_spPivotTable)
		return;

	Reset();

	m_bInit = TRUE;
}

IKWorksheet* KAnalysisPivotTable::GetWorksheet()
{
	ks_stdptr<IKWorksheet> spSheet = m_spSheet;
	return spSheet;
}

bool IsEqual(const std::vector<const KStringField*>& vec1, const std::vector<const KStringField*>& vec2)
{
	if (vec1.size() != vec2.size())
		return false;
	for (size_t i = 0; i < vec1.size(); ++i)
		if (vec1[i] != vec2[i])
			return false;
	return true;
}

bool IsEqual(const std::vector<const KDataField*>& vec1, const std::vector<const KDataField*>& vec2)
{
	if (vec1.size() != vec2.size())
		return false;
	for (size_t i = 0; i < vec1.size(); ++i)
		if (vec1[i] != vec2[i])
			return false;
	return true;
}

void KAnalysisPivotTable::Reset(BOOL* pReload)
{
	if (!pReload)
	{
		Reset();
		return;
	}
	std::vector<const KStringField*> oldRowFieldList(rowFieldList);
	std::vector<const KStringField*> oldColFieldList(colFieldList);
	std::vector<const KDataField*> oldDataFieldList(dataFieldList);
	std::vector<const KDataField*> oldPageFieldList(pageFieldList);
	Reset();
	if (IsEqual(oldRowFieldList, rowFieldList) && IsEqual(oldColFieldList, colFieldList) && IsEqual(oldDataFieldList, dataFieldList) && IsEqual(oldPageFieldList, pageFieldList))
		*pReload = FALSE;
	else
		*pReload = TRUE;
}

void KAnalysisPivotTable::ResetInsertList()
{
	VecKDataField tempFieldList;
	for (auto it = rowFieldList.begin(); it != rowFieldList.end(); ++it)
		tempFieldList.push_back(*it);
	for (auto it = colFieldList.begin(); it != colFieldList.end(); ++it)
		tempFieldList.push_back(*it);
	for (auto it = dataFieldList.begin(); it != dataFieldList.end(); ++it)
		tempFieldList.push_back(*it);
	std::vector<size_t> vecDel;
	for (size_t i = 0; i < m_vecInsertFieldList.size(); ++i)
		if (std::find(tempFieldList.begin(), tempFieldList.end(), m_vecInsertFieldList[i]) == tempFieldList.end())
			vecDel.push_back(i);
	for (int i = vecDel.size() - 1; i >= 0; --i)
		m_vecInsertFieldList.erase(m_vecInsertFieldList.begin() + vecDel[i]);
	for (auto it = tempFieldList.begin(); it != tempFieldList.end(); ++it)
		if (std::find(m_vecInsertFieldList.begin(), m_vecInsertFieldList.end(), *it) == m_vecInsertFieldList.end())
			m_vecInsertFieldList.push_back(*it);
}

void KAnalysisPivotTable::Reset()
{
	InitFields(oldapi::etRowField, rowFieldList);
	InitFields(oldapi::etColumnField, colFieldList);
	InitFields(oldapi::etDataField, dataFieldList);
	InitFields(oldapi::etPageField, pageFieldList);
	ResetInsertList();
}

oldapi::ETPivotFieldOrientation KAnalysisPivotTable::GetPivotFieldOrientation(const KDataField* pField)
{
	if (!m_bInit)
		Init();

	for (auto it = rowFieldList.begin(); it != rowFieldList.end(); ++it)
		if (pField == (*it))
			return oldapi::etRowField;
	for (auto it = colFieldList.begin(); it != colFieldList.end(); ++it)
		if (pField == (*it))
			return oldapi::etColumnField;
	// 维度字段可同时存在行和值，这里应返回行，仅度量会返回值
	for (auto it = dataFieldList.begin(); it != dataFieldList.end(); ++it)
		if (pField == (*it))
			return oldapi::etDataField;
	for (auto it = pageFieldList.begin(); it != pageFieldList.end(); ++it)
		if (pField == (*it))
			return oldapi::etPageField;
	return oldapi::etHidden;
}

HRESULT SelectA1(IKWorksheet* pSheet)
{
	if (!pSheet || !pSheet->GetSheet())
		return E_FAIL;
	RANGE rg(pSheet->GetSheet()->GetBMP());
	IDX iSheet = 0;
	pSheet->GetSheet()->GetIndex(&iSheet);
	rg.SetSheetFromTo(iSheet);
	rg.SetColFromTo(0);
	rg.SetRowFromTo(0);
	ks_stdptr<etoldapi::Range> spR1C1;
	HRESULT hr = CreateRange(pSheet, rg, &spR1C1);
	if (FAILED(hr) || !spR1C1)
		return E_FAIL;
	hr = spR1C1->Select();
	return hr;
}

QString GetFuncString(oldapi::ETConsolidationFunction eFunc)
{
	QString str;
	switch (eFunc)
	{
	case oldapi::etSum:
		str = staticStrSum;
		break;
	case oldapi::etCount:
		str = staticStrCount;
		break;
	case oldapi::etAverage:
		str = staticStrAverage;
		break;
	case oldapi::etMax:
		str = staticStrMax;
		break;
	case oldapi::etMin:
		str = staticStrMin;
		break;
	default:
		return str;
	}
	return str + krt::fromUtf16(__X("项:"));
}

HRESULT KAnalysisPivotTable::DeletePageField(const KDataField* pField)
{
	HRESULT hr = S_OK;

	KComVariant var;
	ks_stdptr<etoldapi::PivotFields> spFields;
	hr = m_spPivotTable->AxisFields(oldapi::etPageField, var, (IKCoreObject**)(&spFields));
	if (FAILED(hr) || !spFields)
		return E_FAIL;
	long nCnt = 0;
	hr = spFields->get_Count(&nCnt);
	if (FAILED(hr) || nCnt <= 0)
		return E_FAIL;
	for (long i = 1; i <= nCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<etoldapi::PivotField> spField;
		hr = spFields->Item(varIdx, &spField);
		if (FAILED(hr) || !spField)
			continue;
		ks_bstr bstrName;
		hr = spField->get_SourceName(&bstrName);
		if (FAILED(hr) || bstrName.empty())
			continue;
		if (krt::fromUtf16(bstrName.c_str()) == pField->GetName())
			return spField->put_Orientation(oldapi::etHidden);
	}
	return E_FAIL;
}

// 按字段项数排序，从少到多，如果项数相同则按数据源字段顺序从左到右
static bool lessFieldItemCount(const KStringField*& lhs, const KStringField*& rhs)
{
	if (lhs->GetItemCount() < rhs->GetItemCount())
		return true;
	if (lhs->GetItemCount() == rhs->GetItemCount() && lhs->GetIndex() < rhs->GetIndex())
		return true;
	return false;
}

HRESULT KAnalysisPivotTable::AddPageField(const KDataField* pField)
{
	HRESULT hr = S_OK;

	ks_stdptr<PivotField> spPivotField;
	KComVariant varFieldName(krt::utf16(pField->GetName()));
	hr = m_spPivotTable->PivotFields(varFieldName, (IKCoreObject**)(&spPivotField));
	if (FAILED(hr) || !spPivotField)
		return E_FAIL;

	return spPivotField->put_Orientation(oldapi::etPageField);
}

BOOL CheckArrayHasTrueValue(VARIANT varArray)
{
	KSmartParam smtArray(varArray);
	if (!smtArray.IsArrayType()) return FALSE;

	const int SUBTTTYPE_NUM = 13;
	size32 lLBoundElement = -1;
	size32 lUBoundElement = -1;
	SafeArrayGetLBound(V_ARRAY(&varArray), 1, &lLBoundElement);
	SafeArrayGetUBound(V_ARRAY(&varArray), 1, &lUBoundElement);

	size32 nSize = lUBoundElement - lLBoundElement + 1;
	if (nSize != SUBTTTYPE_NUM)
		return FALSE;

	VARTYPE varElemType = VT_EMPTY;
	HRESULT hr = SafeArrayGetVartype(V_ARRAY(&varArray), &varElemType);
	if (FAILED(hr)) return FALSE;

	if (VT_BOOL == varElemType)
	{
		VARIANT_BOOL vbElem = VARIANT_FALSE;
		for (size32 idx = 0; idx < SUBTTTYPE_NUM; ++idx)
		{
			size32 lIndexTemp = lLBoundElement + idx;
			HRESULT hr = SafeArrayGetElement(V_ARRAY(&varArray), &lIndexTemp, &vbElem);
			if (SUCCEEDED(hr) && vbElem == VARIANT_TRUE)
				return TRUE;
		}
	}
	else if (VT_VARIANT == varElemType)
	{
		KSmartParam smtElem;
		for (INT idx = 0; idx < SUBTTTYPE_NUM; ++idx)
		{
			size32 lIndexTemp = lLBoundElement + idx;
			HRESULT hr = SafeArrayGetElement(V_ARRAY(&varArray), &lIndexTemp, &smtElem);
			if (SUCCEEDED(hr) && smtElem.IsBooleanType() && smtElem.GetBooleanValue())
				return TRUE;
		}
	}
	return FALSE;
}

KComVariant GetSubtotalsSafeArray(BOOL bShow)
{
	const int SUBTTTYPE_NUM = 13;

	KComVariant varArray;
	SAFEARRAYBOUND rgsabound;
	rgsabound.cElements = SUBTTTYPE_NUM;
	rgsabound.lLbound = 1;
	V_VT(&varArray) = VT_VARIANT | VT_ARRAY;
	V_ARRAY(&varArray) = SafeArrayCreate(VT_VARIANT, 1, &rgsabound);

	KComVariant varTemp; V_VT(&varTemp) = VT_BOOL;
	for (size32 i = 1; i <= SUBTTTYPE_NUM; ++i)
	{
		V_BOOL(&varTemp) = (bShow ? VARIANT_TRUE : VARIANT_FALSE);
		SafeArrayPutElement(V_ARRAY(&varArray), &i, &varTemp);
	}
	return varArray;
}

HRESULT SetSubtotals(etoldapi::PivotTable* pPivotTable)
{
	if (!pPivotTable)
		return E_FAIL;
	KComVariant var;
	ks_stdptr<etoldapi::PivotFields> spPivotFields;
	HRESULT hr = pPivotTable->PivotFields(var, (IKCoreObject**)(&spPivotFields));
	if (FAILED(hr) || !spPivotFields)
		return E_FAIL;
	long nCnt = 0;
	hr = spPivotFields->get_Count(&nCnt);
	if (FAILED(hr) || nCnt <= 0)
		return E_FAIL;
	for (long i = 1; i <= nCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<etoldapi::PivotField> spPivotField;
		hr = spPivotFields->Item(varIdx, &spPivotField);
		if (FAILED(hr) || !spPivotField)
			continue;
		KComVariant varSubtotals;
		hr = spPivotField->get_Subtotals(var, &varSubtotals);
		if (FAILED(hr))
			continue;
		BOOL bShowed = CheckArrayHasTrueValue(varSubtotals);
		if (bShowed)
			hr = spPivotField->put_Subtotals(var, GetSubtotalsSafeArray(FALSE));
	}
	return hr;
}

HRESULT SetPivotTable(etoldapi::PivotTable* pPivotTable)
{
	HRESULT hr = S_OK;
	// 分类汇总 - 不显示分类汇总
	hr = SetSubtotals(pPivotTable);
	// 报表布局 - 以表格形式显示
	hr = pPivotTable->RowAxisLayout(oldapi::xlTabularRow);
	// 报表布局 - 不重复项目标签
	hr = pPivotTable->RepeatAllLabels(oldapi::xlDoNotRepeatLabels);
	// 总计 - 对行和列禁用
	hr = pPivotTable->put_ColumnGrand(VARIANT_FALSE);
	hr = pPivotTable->put_RowGrand(VARIANT_FALSE);
	// 数据透视表选项 - 显示 - 经典数据透视表布局 - 取消
	hr = pPivotTable->put_InGridDropZones(VARIANT_FALSE);
	// 数据透视表选项 - 显示 - 显示值行 - 取消
	hr = pPivotTable->put_ShowValuesRow(VARIANT_FALSE);
	return hr;
}

HRESULT SetPivotTableFont(etoldapi::Range* pRange, float fSize = 11.0, const QString& strName = staticStrNormalFontName, bool bBold = false)
{
	if (!pRange)
		return E_FAIL;
	ks_stdptr<etoldapi::Font> spFont;
	HRESULT hr = pRange->get_Font(&spFont);
	KComVariant varSize(fSize, VT_R4);
	hr = spFont->put_Size(varSize);
	ks_bstr bstrName(krt::utf16(strName));
	hr = spFont->put_Name(bstrName);
	if (bBold)
	{
		KComVariant var(VARIANT_TRUE);
		hr = spFont->put_Bold(var);
	}
	return hr;
}

HRESULT SetPivotTableStyle(etoldapi::PivotTable* pPivotTable)
{
	if (!pPivotTable)
		return E_FAIL;
	// 设置字体字号
	ks_stdptr<etoldapi::Range> spTableRange;
	HRESULT hr = pPivotTable->get_TableRange1(&spTableRange);
	if (SUCCEEDED(hr) && spTableRange)
		SetPivotTableFont(spTableRange);

	// 设置数据透视表样式
	KComVariant varStyleName(__X("PivotStyleMedium2"));
	hr = pPivotTable->put_TableStyle2(varStyleName);

	// 设置整表行高
	ks_stdptr<etoldapi::_Worksheet> spSheet;
	hr = pPivotTable->get_Worksheet(&spSheet);
	if (SUCCEEDED(hr) && spSheet)
	{
		ks_stdptr<etoldapi::Range> spRows;
		hr = spSheet->get_Rows(&spRows);
		KComVariant varHeight(21.0, VT_R8);
		hr = spRows->put_RowHeight(varHeight);
	}
	return hr;
}

HRESULT CreateRange(IKWorksheet* pWorksheet, ROW iRow, COL iCol, etoldapi::Range** ppRange)
{
	IDX iSheet = 0;
	pWorksheet->GetSheet()->GetIndex(&iSheet);
	RANGE rg(pWorksheet->GetSheet()->GetBMP());
	rg.SetSheetFromTo(iSheet);
	rg.SetRowFromTo(iRow);
	rg.SetColFromTo(iCol);
	return CreateRange(pWorksheet, rg, ppRange);
}

HRESULT CreatePivotTable(etoldapi::_Worksheet* pWorksheet, etoldapi::Range* pSrcRange, etoldapi::PivotTable** ppPivotTable)
{
	if (!pWorksheet || !pSrcRange || !ppPivotTable)
		return E_FAIL;
	ks_stdptr<etoldapi::_Workbook> spBook = pWorksheet->GetWorkbook();
	// 插入数据透视表
	ks_stdptr<etoldapi::PivotCaches> spPivotCaches;
	HRESULT hr = spBook->PivotCaches(&spPivotCaches);
	if (FAILED(hr) || !spPivotCaches)
		return hr;
	ks_stdptr<etoldapi::PivotCache> spPivotCache;
	KComVariant varPTV15(oldapi::xlPivotTableVersion15, VT_I4);
	KComVariant varRange(pSrcRange);
	hr = spPivotCaches->Add(oldapi::etDatabase, varRange, varPTV15, FALSE, &spPivotCache);
	if (SUCCEEDED(hr) && spPivotCache)
	{
		KComVariant varEmpytStr(__X(""));
		ks_stdptr<etoldapi::Range> spInsertRange;
		CreateRange(pWorksheet, 2, 0, &spInsertRange);
		KComVariant varInsertRange(spInsertRange);
		KComVariant var;
		hr = spPivotCache->CreatePivotTable(varInsertRange, varEmpytStr, var, varPTV15, FALSE, ppPivotTable);
		if (SUCCEEDED(hr) && *ppPivotTable)
		{
			// 设置数据透视表属性
			hr = SetPivotTable(*ppPivotTable);
			// 设置数据透视表样式
			hr = SetPivotTableStyle(*ppPivotTable);
		}
	}
	return hr;
}

HRESULT KAnalysisPivotTable::ClearFields(oldapi::ETPivotFieldOrientation efo)
{
	if (!m_spPivotTable)
		return E_FAIL;
	KComVariant var;
	ks_stdptr<etoldapi::PivotFields> spPivotFields;
	HRESULT hr = m_spPivotTable->AxisFields(efo, var, (IKCoreObject**)(&spPivotFields));
	if (FAILED(hr) || !spPivotFields)
		return hr;
	long nCnt = 0;
	hr = spPivotFields->get_Count(&nCnt);
	if (FAILED(hr) || nCnt <= 0)
		return hr;
	for (long i = 1; i <= nCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<etoldapi::PivotField> spPivotField;
		hr = spPivotFields->Item(varIdx, &spPivotField);
		if (FAILED(hr) || !spPivotField)
			continue;
		if (efo == oldapi::etColumnField)
		{
			ks_bstr bstrSourceName;
			spPivotField->get_SourceName(&bstrSourceName);
			if (!bstrSourceName)
				continue;
			QString strSourceName = krt::fromUtf16(bstrSourceName.c_str());
			KDataField* pField = m_pAnalysisTable->GetAllDataField().FindByName(strSourceName);
			if (!pField)
				continue;
		}
		hr = spPivotField->put_Orientation(oldapi::etHidden);
		if (FAILED(hr))
			return hr;
	}
	return hr;
}

HRESULT KAnalysisPivotTable::ClearApiPivotTable()
{
	if (!m_spPivotTable)
		return E_FAIL;
	ClearFields(oldapi::etRowField);
	ClearFields(oldapi::etColumnField);
	ClearFields(oldapi::etDataField);
	return S_OK;
}

void KAnalysisPivotTable::GetAllFields(VecKDataField& vecFields)
{
	Reset();	// todo 是否不需要
	for (size_t i = 0; i < rowFieldList.size(); ++i)
		vecFields.push_back(rowFieldList[i]);
	for (size_t i = 0; i < colFieldList.size(); ++i)
		vecFields.push_back(colFieldList[i]);
	for (size_t i = 0; i < dataFieldList.size(); ++i)
	{
		const KDataField* pField = dataFieldList[i];
		if (pField && pField->IsNumberField())	// 过滤掉维度计数
			vecFields.push_back(pField);	// Reset()函数在收集值字段时，已经将数值类型的计算类型更新到了KDataField*中
	}
	// 排重
	std::set<const KDataField*> setFields(vecFields.begin(), vecFields.end());
	vecFields.assign(setFields.begin(), setFields.end());
}

// 按字段在数据源的顺序从小到大排序
static bool lessFieldIndex(const KNumberField*& lhs, const KNumberField*& rhs)
{
	if (lhs->GetIndexInt() < rhs->GetIndexInt())
		return true;
	return false;
}

bool IsAllInclude(const std::vector<const KStringField*>& vecStrFields, const KStringField*& pField)
{
	// 均为包含关系，则返回true，否则返回false
	// 找某字段与其他字段交叉关系最多的为列字段，
	// 如果存在多个则取其中项数最少的为列字段，
	// 若又存在多个字段则按字段在数据源的顺序取最靠左的为列字段
	if (vecStrFields.empty())
		return true;
	int nMaxCrossCnt = 0;
	std::vector<int> vecCrossCnt;
	for (size_t i = 0; i < vecStrFields.size(); ++i)
	{
		int nCross = 0;
		for (size_t j = 0; j < vecStrFields.size(); ++j)
		{
			if (j == i)
				continue;
			if (!vecStrFields[i]->IsIncludeField(vecStrFields[j]->GetName()))
				nCross++;
		}
		if (nCross > nMaxCrossCnt)
			nMaxCrossCnt = nCross;
		vecCrossCnt.push_back(nCross);
	}
	if (nMaxCrossCnt == 0)
		return true;
	std::vector<size_t> vecIdx;
	for (size_t i = 0; i < vecCrossCnt.size(); ++i)
	{
		if (vecCrossCnt[i] != nMaxCrossCnt)
			continue;
		vecIdx.push_back(i);
	}
	if (!vecIdx.empty())
		pField = vecStrFields[vecIdx[0]];
	return false;
}

void TransCrossTwo(const KStringField* pRow, const KStringField* pCol, VecKDataField& vecRowFields, VecKDataField& vecColFields)
{
	vecRowFields.push_back(pRow);
	vecColFields.push_back(pCol);
}

void KAnalysisPivotTable::TransPivotTableSruct(const VecKDataField& vecFields, VecKDataField& vecRowFields, VecKDataField& vecColFields, VecKDataField& vecDataFields)
{
	if (vecFields.empty())
		return;
	// 区分维度和度量
	std::vector<const KStringField*> vecStrFields;
	std::vector<const KNumberField*> vecNumFields;
	for (size_t i = 0; i < vecFields.size(); ++i)
	{
		const KDataField* pField = vecFields[i];
		if (!pField)
			continue;
		if (pField->IsNumberField())
			vecNumFields.push_back((const KNumberField*)pField);
		else
			vecStrFields.push_back((const KStringField*)pField);
	}
	// 度量按数据源顺序从左到右排序
	std::sort(vecNumFields.begin(), vecNumFields.end(), lessFieldIndex);
	// 维度按字段项数从少到多排序
	std::sort(vecStrFields.begin(), vecStrFields.end(), lessFieldItemCount);
	if (vecNumFields.size() > 1)
	{
		for (auto it = vecStrFields.begin(); it != vecStrFields.end(); ++it)
			vecRowFields.push_back(*it);
	}
	else
	{
		if (vecStrFields.size() == 1)
		{
			vecRowFields.push_back(vecStrFields[0]);
		}
		else if (vecStrFields.size() == 2)
		{
			if (vecStrFields[0]->IsIncludeField(vecStrFields[1]->GetName()))
			{	// 是包含关系，都设为行字段
				vecRowFields.push_back(vecStrFields[0]);
				vecRowFields.push_back(vecStrFields[1]);
			}
			else
			{	// 非包含关系，项数少（需大于1项）的为列字段
				if (vecStrFields[0]->GetItemCount() < vecStrFields[1]->GetItemCount())
				{
					if (vecStrFields[0]->GetItemCount() > 1)
						TransCrossTwo(vecStrFields[1], vecStrFields[0], vecRowFields, vecColFields);
					else
						TransCrossTwo(vecStrFields[0], vecStrFields[1], vecRowFields, vecColFields);
				}
				else if (vecStrFields[0]->GetItemCount() > vecStrFields[1]->GetItemCount())
				{
					if (vecStrFields[1]->GetItemCount() > 1)
						TransCrossTwo(vecStrFields[0], vecStrFields[1], vecRowFields, vecColFields);
					else
						TransCrossTwo(vecStrFields[1], vecStrFields[0], vecRowFields, vecColFields);
				}
				else
				{	// 若项数相等，将日期类型为行字段
					if (vecStrFields[0]->GetType() == DataFieldTypeDate && vecStrFields[1]->GetType() != DataFieldTypeDate)
						TransCrossTwo(vecStrFields[0], vecStrFields[1], vecRowFields, vecColFields);
					else if (vecStrFields[0]->GetType() != DataFieldTypeDate && vecStrFields[1]->GetType() == DataFieldTypeDate)
						TransCrossTwo(vecStrFields[1], vecStrFields[0], vecRowFields, vecColFields); 
					else
					{	// 若项数相同，同为日期或同非日期，将数据源靠左的为列字段
						if (vecStrFields[0]->GetIndex() < vecStrFields[1]->GetIndex())
							TransCrossTwo(vecStrFields[1], vecStrFields[0], vecRowFields, vecColFields); 
						else
							TransCrossTwo(vecStrFields[0], vecStrFields[1], vecRowFields, vecColFields);
					}
				}
			}
		}
		else if (vecStrFields.size() > 2)
		{
			// 均为包含关系
			const KStringField* pColField = nullptr;
			if (IsAllInclude(vecStrFields, pColField))
			{
				for (auto it = vecStrFields.begin(); it != vecStrFields.end(); ++it)
					vecRowFields.push_back(*it);
			}
			else
			{
				for (auto it = vecStrFields.begin(); it != vecStrFields.end(); ++it)
					if (*it != pColField)
						vecRowFields.push_back(*it);
				vecColFields.push_back(pColField);
			}
		}
	}
	for (auto it = vecNumFields.begin(); it != vecNumFields.end(); ++it)
		vecDataFields.push_back((const KDataField*)(*it));
	// 度量为空时需要加上计数
	if (vecDataFields.empty())
		vecDataFields.push_back(vecRowFields.back());
}

HRESULT KAnalysisPivotTable::SetPivotTableFields(const VecKDataField& vecFields)
{
	VecKDataField vecRowFields, vecColFields, vecDataFields;
	TransPivotTableSruct(vecFields, vecRowFields, vecColFields, vecDataFields);
	return ReplacePivotTableFields(vecRowFields, vecColFields, vecDataFields);
}

HRESULT _AddRowField(etoldapi::PivotTable* pPivotTable, const KDataField* pField, int pos)
{
	if (!pField || pField->IsNumberField())
		return E_FAIL;
	ks_stdptr<etoldapi::PivotField> spPivotField;
	KComVariant varFieldName(krt::utf16(pField->GetName()));
	HRESULT hr = pPivotTable->PivotFields(varFieldName, (IKCoreObject**)(&spPivotField));
	if (FAILED(hr) || !spPivotField)
		return E_FAIL;

	hr = spPivotField->put_Orientation(oldapi::etRowField);
	if (FAILED(hr))
		return hr;

	KComVariant varPos(pos, VT_I4);
	spPivotField->put_Position(varPos);

	return S_OK;
}

HRESULT _AddColField(etoldapi::PivotTable* pPivotTable, const KDataField* pField, int pos)
{
	if (!pField || pField->IsNumberField())
		return E_FAIL;
	ks_stdptr<etoldapi::PivotField> spPivotField;
	KComVariant varFieldName(krt::utf16(pField->GetName()));
	HRESULT hr = pPivotTable->PivotFields(varFieldName, (IKCoreObject**)(&spPivotField));
	if (FAILED(hr) || !spPivotField)
		return E_FAIL;

	hr = spPivotField->put_Orientation(oldapi::etColumnField);
	if (FAILED(hr))
		return hr;

	KComVariant varPos(pos, VT_I4);
	spPivotField->put_Position(varPos);

	return S_OK;
}

HRESULT _AddDataField(etoldapi::PivotTable* pPivotTable, const KDataField* pField, int pos)
{
	if (!pField)
		return E_FAIL;
	KComVariant varFieldName(krt::utf16(pField->GetName()));
	ks_stdptr<etoldapi::PivotField> spPivotField;
	HRESULT hr = pPivotTable->PivotFields(varFieldName, (IKCoreObject**)(&spPivotField));
	if (FAILED(hr) || !spPivotField)
		return E_FAIL;

	oldapi::ETConsolidationFunction eFunc = oldapi::etCount;
	if (pField->IsNumberField())
		eFunc = ((const KNumberField*)pField)->GetUsedNumberFieldType();
	KComVariant varName(krt::utf16(GetFuncString(eFunc) + pField->GetName()));
	KComVariant varFunc(eFunc, VT_I4);
	ks_stdptr<etoldapi::PivotField> spNewPivotField;
	hr = pPivotTable->AddDataField(spPivotField, varName, varFunc, FALSE, &spNewPivotField);
	if (FAILED(hr) || !spNewPivotField)
		return E_FAIL;

	if (pos > 0)
	{
		KComVariant varPos(pos, VT_I4);
		spNewPivotField->put_Position(varPos);
	}

	KComVariant varPos(pos > 0 ? pos : 1, VT_I4);
	ks_stdptr<etoldapi::PivotField> spDataPivotField;
	hr = pPivotTable->AxisFields(oldapi::etDataField, varPos, (IKCoreObject**)(&spDataPivotField));
	if (FAILED(hr) || !spDataPivotField)
		return S_OK;

	ks_stdptr<etoldapi::Range> spFieldRange;
	hr = spDataPivotField->get_DataRange(&spFieldRange);
	if (FAILED(hr) || !spFieldRange)
		return E_FAIL;

	long nCnt = 0;
	spFieldRange->get_Count(&nCnt);
	if (nCnt <= 0)
		return E_FAIL;

	if (pField->IsNumberField())
	{
		const KNumberField* pNumField = (const KNumberField*)pField;
		if (pNumField && pNumField->IsPercent())
		{
			ks_bstr bstr(__X("0.00%"));
			spDataPivotField->put_NumberFormat(bstr);
		}
		else
		{
			ks_bstr bstr(__X("General"));
			spDataPivotField->put_NumberFormat(bstr);
		}
	}
	else
	{
		ks_bstr bstr(__X("General"));
		spDataPivotField->put_NumberFormat(bstr);
	}
	return S_OK;
}

HRESULT KAnalysisPivotTable::ReplacePivotTableFields(const QStringList& qslRow, const QStringList& qslCol, const VecNameField& vecData)
{
	if (!m_pAnalysisTable)
		return E_FAIL;
	HRESULT hr = S_OK;
	// 如果数据透视表对象为空，则创建一个数据透视表
	if (!m_spPivotTable)
	{
		hr = CreatePivotTable(m_spSheet, m_pAnalysisTable->GetSourceRange(), &m_spPivotTable);
		if (FAILED(hr) || !m_spPivotTable)
		{
			m_spPivotTable.clear();
			return E_FAIL;
		}
	}

	VecKDataField vecRowFields, vecColFields, vecDataFields;
	for (int i = 0; i < qslRow.size(); ++i)
		vecRowFields.push_back(m_pAnalysisTable->GetDataField(qslRow[i]));
	for (int i = 0; i < qslCol.size(); ++i)
		vecColFields.push_back(m_pAnalysisTable->GetDataField(qslCol[i]));
	for (size_t i = 0; i < vecData.size(); ++i)
	{
		KDataField* pField = m_pAnalysisTable->GetDataField(vecData[i].first);
		if (!pField)
			continue;
		if (pField->IsNumberField())
		{
			KNumberField* pNumField = (KNumberField*)pField;
			if (pNumField->GetUsedNumberFieldType() != vecData[i].second)
				pNumField->SetUsedNumberFieldType(vecData[i].second);
		}
		vecDataFields.push_back(pField);
	}
	return ReplacePivotTableFields(vecRowFields, vecColFields, vecDataFields);
}

HRESULT KAnalysisPivotTable::ReplacePivotTableFields(const VecKDataField& vecRowFields, const VecKDataField& vecColFields, const VecKDataField& vecDataFields)
{
	if (!m_spPivotTable)
		return E_FAIL;

	HRESULT hr = S_OK;
	hr = ClearFields(oldapi::etRowField);
	if (FAILED(hr))
		return hr;
	hr = ClearFields(oldapi::etColumnField);
	if (FAILED(hr))
		return hr;

	for (size_t i = 0; i < vecRowFields.size(); ++i)
	{
		hr = _AddRowField(m_spPivotTable, vecRowFields[i], i + 1);
		if (FAILED(hr))
			return hr;
	}
	for (size_t i = 0; i < vecColFields.size(); ++i)
	{
		hr = _AddColField(m_spPivotTable, vecColFields[i], i + 1);
		if (FAILED(hr))
			return hr;
	}
	if (!vecRowFields.empty() || !vecColFields.empty() || vecDataFields.empty())
	{
		hr = ClearFields(oldapi::etDataField);
		if (FAILED(hr))
			return hr;

		for (size_t i = 0; i < vecDataFields.size(); ++i)
		{
			hr = _AddDataField(m_spPivotTable, vecDataFields[i], i + 1);
			if (FAILED(hr))
				return hr;
		}
		if (vecDataFields.empty())
		{
			const KDataField* pField = nullptr;
			if (!vecRowFields.empty())
				pField = vecRowFields.back();
			else if (!vecColFields.empty())
				pField = vecColFields.back();
			if (pField)
			{
				hr = _AddDataField(m_spPivotTable, pField, 1);
				if (FAILED(hr))
					return hr;
			}
		}
	}
	else
	{
		KComVariant var;
		ks_stdptr<etoldapi::PivotFields> spPivotFields;
		HRESULT hr = m_spPivotTable->AxisFields(oldapi::etDataField, var, (IKCoreObject**)(&spPivotFields));
		if (FAILED(hr) || !spPivotFields)
			return hr;
		long nCnt = 0;
		hr = spPivotFields->get_Count(&nCnt);
		if (FAILED(hr))
			return hr;
		int idxSameI = -1, idxSameJ = -1;
		for (long i = 1; i <= nCnt; ++i)
		{
			KComVariant varIdx(i, VT_I4);
			ks_stdptr<etoldapi::PivotField> spPivotField;
			hr = spPivotFields->Item(varIdx, &spPivotField);
			if (FAILED(hr) || !spPivotField)
				continue;
			ks_bstr bstrSourceName;
			hr = spPivotField->get_SourceName(&bstrSourceName);
			QString strSourceName = krt::fromUtf16(bstrSourceName.c_str());
			for (size_t j = 0; j < vecDataFields.size(); ++j)
				if (vecDataFields[j] && strSourceName == vecDataFields[j]->GetName())
				{
					idxSameI = i;
					idxSameJ = j;
					break;
				}
			if (idxSameI > 0)
				break;
		}

		int iContinueI = idxSameI > 0 ? idxSameI : 1;
		for (long i = 1; i <= nCnt; ++i)
		{
			if (i == iContinueI)
				continue;
			KComVariant varIdx(i, VT_I4);
			ks_stdptr<etoldapi::PivotField> spPivotField;
			hr = spPivotFields->Item(varIdx, &spPivotField);
			if (FAILED(hr) || !spPivotField)
				continue;
			hr = spPivotField->put_Orientation(oldapi::etHidden);
			if (FAILED(hr))
				return hr;
		}
		for (size_t j = 0; j < vecDataFields.size(); ++j)
		{
			if (j == idxSameJ)
				continue;
			hr = _AddDataField(m_spPivotTable, vecDataFields[j], j + 1);
			if (FAILED(hr))
				return hr;
		}
		if (idxSameJ < 0 && nCnt > 0)
		{
			KComVariant varIdx(vecDataFields.size() + 1, VT_I4);
			ks_stdptr<etoldapi::PivotField> spPivotField;
			hr = m_spPivotTable->AxisFields(oldapi::etDataField, varIdx, (IKCoreObject**)(&spPivotField));
			if (FAILED(hr) || !spPivotField)
				return hr;
			hr = spPivotField->put_Orientation(oldapi::etHidden);
			if (FAILED(hr))
				return hr;
		}
	}
	// 更新数据透视表信息
	Reset();
	return S_OK;
}

HRESULT KAnalysisPivotTable::InsertPivotTableField(const KDataField* pField, bool isPage, bool bSelect)
{
	if (!pField)
		return E_FAIL;
	HRESULT hr = S_OK;
	// 如果数据透视表对象为空，则创建一个数据透视表
	bool bInsertPivotTable = false;
	if (!m_spPivotTable)
	{
		hr = CreatePivotTable(m_spSheet, m_pAnalysisTable->GetSourceRange(), &m_spPivotTable);
		if (FAILED(hr) || !m_spPivotTable)
		{
			m_spPivotTable.clear();
			return E_FAIL;
		}
		bInsertPivotTable = true;
	}

	ks_stdptr<IKWorksheet> spSheet = GetWorksheet();
	ks_stdptr<etoldapi::Range> spSelection;
	if (bSelect && !bInsertPivotTable)
	{
		ks_stdptr<etoldapi::_Workbook> spWorkbook = spSheet->GetWorkbook();
		ks_stdptr<IKApplication> spApp = spWorkbook->GetApplication();
		if (!kidentifytable::IsObjectSelected(spApp))
		{
			ks_stdptr<etoldapi::_Application> spApplication = spApp;
			spApplication->get_Selection(0, (IKCoreObject**)(&spSelection));
		}
	}

	if (!m_bInit)
		Init();

	if (isPage)	// 添加到筛选器
	{
		hr = AddPageField(pField);
		if (SUCCEEDED(hr))
			Reset();
	}
	else
	{
		VecKDataField vecFields;
		GetAllFields(vecFields);
		auto it = std::find(vecFields.begin(), vecFields.end(), pField);
		if (it == vecFields.end())
		{
			// 计算方式属性不应该维护在字段信息，应该维护在数据透视表，这里临时处理，待后续优化
			if (pField->IsNumberField()
				&& ((const KNumberField*)pField)->GetUsedNumberFieldType() != ((const KNumberField*)pField)->GetNumberFieldType()
				&& m_pAnalysisTable)
			{
				KDataFieldList& allFieldList = m_pAnalysisTable->GetAllDataField();
				auto itFind = std::find(allFieldList.begin(), allFieldList.end(), pField);
				if (itFind != allFieldList.end() && *itFind)
				{
					KNumberField* pNumField = (KNumberField*)*itFind;
					pNumField->SetUsedNumberFieldType(pNumField->GetNumberFieldType());
				}
			}

			vecFields.push_back(pField);
		}
		hr = SetPivotTableFields(vecFields);
	}
	if (FAILED(hr))
		return hr;

	if (bInsertPivotTable)
		SelectA1(spSheet);
	if (bSelect && spSelection)
		spSelection->Select();

	return S_OK;
}

HRESULT KAnalysisPivotTable::DeletePivotTableField(const KDataField* pField, bool bSelect)
{
	if (!pField || !m_spPivotTable)
		return E_FAIL;

	ks_stdptr<IKWorksheet> spSheet = GetWorksheet();
	ks_stdptr<etoldapi::Range> spSelection;
	if (bSelect)
	{
		ks_stdptr<etoldapi::_Workbook> spWorkbook = spSheet->GetWorkbook();
		ks_stdptr<IKApplication> spApp = spWorkbook->GetApplication();
		if (!kidentifytable::IsObjectSelected(spApp))
		{
			ks_stdptr<etoldapi::_Application> spApplication = spApp;
			spApplication->get_Selection(0, (IKCoreObject**)(&spSelection));
		}
	}

	if (!m_bInit)
		Init();

	HRESULT hr = S_OK;
	VecKDataField vecFields;
	GetAllFields(vecFields);
	auto it = std::find(vecFields.begin(), vecFields.end(), pField);
	if (it != vecFields.end())
	{
		// 计算方式属性不应该维护在字段信息，应该维护在数据透视表，这里临时处理，待后续优化
		if (pField->IsNumberField() 
			&& ((const KNumberField*)pField)->GetUsedNumberFieldType() != ((const KNumberField*)pField)->GetNumberFieldType()
			&& m_pAnalysisTable)
		{
			KDataFieldList& allFieldList = m_pAnalysisTable->GetAllDataField();
			auto itFind = std::find(allFieldList.begin(), allFieldList.end(), pField);
			if (itFind != allFieldList.end() && *itFind)
			{
				KNumberField* pNumField = (KNumberField*)*itFind;
				pNumField->SetUsedNumberFieldType(pNumField->GetNumberFieldType());
			}
		}
		vecFields.erase(it);
		if (vecFields.empty() && pageFieldList.empty() && m_spPivotTable)
			hr = DeleteApiPivotTable();
		else
			hr = SetPivotTableFields(vecFields);
	}
	else
	{
		if (std::find(pageFieldList.begin(), pageFieldList.end(), pField) != pageFieldList.end())
		{
			hr = DeletePageField(pField);
			if (SUCCEEDED(hr))
				Reset();
		}
		else
			return S_OK;	// 没找到要删除的字段
	}
	if (FAILED(hr))
		return hr;

	if (bSelect && spSelection)
		spSelection->Select();

	// 删除字段后数据透视表为空，则删除数据透视表对象
	if (IsEmptyAnalysis() && m_spPivotTable)
		DeleteApiPivotTable();

	return S_OK;
}

HRESULT KAnalysisPivotTable::ReplacePivotTableFields(const VecKDataField& vecFields, bool bSelect)
{
	HRESULT hr = S_OK;
	// 如果数据透视表对象为空，则创建一个数据透视表
	bool bInsertPivotTable = false;
	if (!m_spPivotTable)
	{
		hr = CreatePivotTable(m_spSheet, m_pAnalysisTable->GetSourceRange(), &m_spPivotTable);
		if (FAILED(hr) || !m_spPivotTable)
		{
			m_spPivotTable.clear();
			return E_FAIL;
		}
		bInsertPivotTable = true;
	}

	ks_stdptr<IKWorksheet> spSheet = GetWorksheet();
	ks_stdptr<etoldapi::Range> spSelection;
	if (bSelect && !bInsertPivotTable)
	{
		ks_stdptr<etoldapi::_Workbook> spWorkbook = spSheet->GetWorkbook();
		ks_stdptr<IKApplication> spApp = spWorkbook->GetApplication();
		if (!kidentifytable::IsObjectSelected(spApp))
		{
			ks_stdptr<etoldapi::_Application> spApplication = spApp;
			spApplication->get_Selection(0, (IKCoreObject**)(&spSelection));
		}
	}

	if (!m_bInit)
		Init();

	hr = SetPivotTableFields(vecFields);
	if (FAILED(hr))
		return hr;

	if (bInsertPivotTable)
		SelectA1(spSheet);
	if (bSelect && spSelection)
		spSelection->Select();

	// 删除字段后数据透视表为空，则删除数据透视表对象
	if (IsEmptyAnalysis() && m_spPivotTable)
		DeleteApiPivotTable();

	return S_OK;
}

HRESULT KAnalysisPivotTable::DeleteApiPivotTable()
{
	if (!m_spPivotTable)
		return S_FALSE;
	ks_stdptr<etoldapi::Range> spTableRange2;
	HRESULT hr = m_spPivotTable->get_TableRange2(&spTableRange2);
	if (SUCCEEDED(hr) && spTableRange2)
		hr = spTableRange2->Clear();
	m_spPivotTable.clear();
	Reset();
	CleanUnderstand();
	return hr;
}

QString GetRangeString(etoldapi::Range* pRange)
{
	if (!pRange)
		return QString();
	HRESULT hr = S_OK;
	KComVariant var;
	ks_bstr bstrRange;
	hr = pRange->get_Address(VARIANT_TRUE, VARIANT_TRUE, etA1, VARIANT_TRUE, var, &bstrRange);
	if (FAILED(hr) || bstrRange.empty())
		return QString();
	return krt::fromUtf16(bstrRange.c_str());
}

QString GetCellContent(etoldapi::Range* pRange, long iRow, long iCol)
{
	if (!pRange)
		return QString();

	KComVariant varRow(iRow, VT_I4);
	KComVariant varCol(iCol, VT_I4);
	KComVariant varCell;
	HRESULT hr = pRange->get_Item(varRow, varCol, &varCell);
	if (SUCCEEDED(hr))
	{
		ks_stdptr<Range> spCell = KSmartParam(varCell).GetInterfaceValue();
		if (spCell)
		{
			ks_bstr bstrText;
			hr = spCell->get_Text(&bstrText);
			if (SUCCEEDED(hr) && !bstrText.empty())
			{
				return krt::fromUtf16(bstrText.c_str());
			}
		}
	}
	return QString();
}

HRESULT SetPivotTable2(etoldapi::PivotTable* pPivotTable)
{
	HRESULT hr = S_OK;
	// 分类汇总 - 不显示分类汇总
	hr = SetSubtotals(pPivotTable);
	// 报表布局 - 以表格形式显示
	hr = pPivotTable->RowAxisLayout(oldapi::xlTabularRow);
	// 报表布局 - 重复所有项目标签
	hr = pPivotTable->RepeatAllLabels(oldapi::xlRepeatLabels);
	// 总计 - 对行和列禁用
	hr = pPivotTable->put_ColumnGrand(VARIANT_FALSE);
	hr = pPivotTable->put_RowGrand(VARIANT_FALSE);
	// 数据透视表选项 - 显示 - 经典数据透视表布局 - 取消
	hr = pPivotTable->put_InGridDropZones(VARIANT_FALSE);
	// 数据透视表选项 - 显示 - 显示值行 - 取消
	hr = pPivotTable->put_ShowValuesRow(VARIANT_FALSE);
	return hr;
}

HRESULT KAnalysisPivotTable::GetAnalysisDataPivotTable(QJsonObject& jsonTable)
{
	HRESULT hr = S_OK;
	hr = SetPivotTable2(m_spPivotTable);
	ks_stdptr<etoldapi::Range> spDataBodyRange;
	hr = m_spPivotTable->get_DataBodyRange(&spDataBodyRange);

	RANGE rg(m_spBook->GetBook()->GetBMP());
	if (SUCCEEDED(hr) && spDataBodyRange)
		IdentifyTool::GetTableRange(spDataBodyRange, &rg);

	long nColFieldCnt = 0;
	KComVariant var;
	ks_stdptr<etoldapi::PivotFields> spFields;
	hr = m_spPivotTable->AxisFields(oldapi::etColumnField, var, (IKCoreObject**)(&spFields));
	if (SUCCEEDED(hr) && spFields)
		hr = spFields->get_Count(&nColFieldCnt);

	IDX iSheet = 0;
	m_spSheet->GetSheet()->GetIndex(&iSheet);
	IBookOp* pBookOp = m_spBook->GetBook()->LeakOperator();
	if (!pBookOp)
		return E_FAIL;
	ks_stdptr<IETStringTools> spTools;
	_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&spTools);
	if (!spTools)
		return E_FAIL;
	spTools->SetEnv(m_spSheet->GetSheet());

	long nCol = rg.ColTo() - rg.ColFrom() + 1;
	long nCells = nCol;
	long iRowStart = rg.RowFrom();
	if (!colFieldList.empty())
		iRowStart += nColFieldCnt;
	QJsonArray jsonColList;
	for (long iCol = rg.ColFrom(); iCol <= rg.ColTo(); ++iCol)
	{
		QJsonObject jsonCol;
		QJsonArray jsonCellList;
		QString strName = IdentifyTool::GetCellText(iRowStart, iCol, spTools);
		for (long iRow = iRowStart + 1; iRow <= rg.RowTo(); ++iRow)
		{
			if (iRow * iCol > MaxPivotTableCells)
				continue;
			QJsonObject jsonCell;
			QString strContent = IdentifyTool::GetCellText(iRow, iCol, spTools);
			if (!strContent.isEmpty())
			{
				jsonCell.insert("content", strContent);
				nCells++;
			}
			QString strValue2 = IdentifyTool::GetCellValue2(iSheet, iRow, iCol, pBookOp);
			if (!strValue2.isEmpty() && strValue2 != strContent)
				jsonCell.insert("value2", strValue2);
			jsonCellList.append(jsonCell);
		}
		jsonCol.insert("name", strName);
		jsonCol.insert("cellList", jsonCellList);
		jsonColList.append(jsonCol);
	}
	jsonTable.insert("columnList", jsonColList);
	jsonTable.insert("cellCount", (qint64)nCells);
	jsonTable.insert("rangeCellCount", (qint64)(rg.RowTo() - nColFieldCnt) * nCol);
	return S_OK;
}

HRESULT KAnalysisPivotTable::GetPivotFieldInfo(etoldapi::PivotField* pField, QJsonObject& jsonInfo)
{
	HRESULT hr = S_OK;

	ks_bstr bstrName;
	hr = pField->get_Name(&bstrName);
	if (FAILED(hr))
		return hr;
	QString strName = krt::fromUtf16(bstrName.c_str());
	ks_bstr bstrSourceName;
	hr = pField->get_SourceName(&bstrSourceName);
	if (FAILED(hr))
		return hr;
	QString strSourceName = krt::fromUtf16(bstrSourceName.c_str());
	double dPredValue = 0.0;
	if (m_pAnalysisTable)
	{
		const KDataField* pDataField = m_pAnalysisTable->GetAllDataField().FindByName(strSourceName);
		if (pDataField)
			dPredValue = pDataField->GetPredValue();
	}

	jsonInfo.insert("name", strName);
	jsonInfo.insert("sourceName", strSourceName);
	jsonInfo.insert("predValue", dPredValue);

	KComVariant varPos;
	hr = pField->get_Position(&varPos);
	if (SUCCEEDED(hr) && V_VT(&varPos) == VT_I4)
		jsonInfo.insert("position", V_I4(&varPos));

	oldapi::EtPivotFieldDataType nType = oldapi::etPFDT_Text;
	hr = pField->get_DataType(&nType);
	if (SUCCEEDED(hr))
	{
		switch (nType)
		{
		case oldapi::etPFDT_Date:
			jsonInfo.insert("dataType", "date");
			break;
		case oldapi::etPFDT_Number:
			jsonInfo.insert("dataType", "number");
			break;
		case oldapi::etPFDT_Text:
			jsonInfo.insert("dataType", "text");
			break;
		}
	}

	return hr;
}

HRESULT KAnalysisPivotTable::GetPivotFieldsInfo(oldapi::ETPivotFieldOrientation efo, QJsonArray& jsonList)
{
	HRESULT hr = S_OK;
	KComVariant var;
	ks_stdptr<etoldapi::PivotFields> spFields;
	hr = m_spPivotTable->AxisFields(efo, var, (IKCoreObject**)(&spFields));
	if (FAILED(hr) || !spFields)
		return E_FAIL;
	long nCnt = 0;
	hr = spFields->get_Count(&nCnt);
	if (FAILED(hr) || nCnt <= 0)
		return E_FAIL;
	for (long i = 1; i <= nCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<etoldapi::PivotField> spField;
		hr = spFields->Item(varIdx, &spField);
		if (FAILED(hr) || !spField)
			continue;
		QJsonObject jsonInfo;
		hr = GetPivotFieldInfo(spField, jsonInfo);
		if (SUCCEEDED(hr))
			jsonList.append(jsonInfo);
	}
	return S_OK;
}

HRESULT KAnalysisPivotTable::GetAnalysisDataPivotTableInfo(QJsonObject& jsonInfo)
{
	HRESULT hr = S_OK;

	QJsonArray jsonRowFields;
	hr = GetPivotFieldsInfo(oldapi::etRowField, jsonRowFields);
	jsonInfo.insert("rowFields", jsonRowFields);

	QJsonArray jsonColFields;
	hr = GetPivotFieldsInfo(oldapi::etColumnField, jsonColFields);
	jsonInfo.insert("columnFields", jsonColFields);

	QJsonArray jsonDataFields;
	hr = GetPivotFieldsInfo(oldapi::etDataField, jsonDataFields);
	jsonInfo.insert("dataFields", jsonDataFields);

	return S_OK;
}

HRESULT KAnalysisPivotTable::GetAnalysisData(QJsonObject& jsonObj)
{
	if (!m_spPivotTable)
		return E_FAIL;
	if (!m_bInit)
		Init();
	ks_stdptr<IKTransactionTool> spTransTool = m_spBook->GetTransactionTool();
	spTransTool->StartTrans();

	HRESULT hr = S_OK;
	// 数据透视表 表格数据
	QJsonObject jsonTable;
	hr = GetAnalysisDataPivotTable(jsonTable);
	jsonObj.insert("pivotTable", jsonTable);

	// 数据透视表 字段描述
	QJsonObject jsonPTableInfo;
	hr = GetAnalysisDataPivotTableInfo(jsonPTableInfo);
	jsonObj.insert("pivotTableInfo", jsonPTableInfo);

	// 传给服务端的标记，区分是否推荐2个包含关系的维度，以匹配新版本的支持
	jsonObj.insert("isRecommendIncludeDim", true);

	spTransTool->Rollback();
	return hr;
}

HRESULT KAnalysisPivotTable::Refresh()
{
	if (!m_spPivotTable)
		return E_FAIL;

	if (!m_bInit)
		Init();

	ks_stdptr<etoldapi::PivotCache> spPivotCache;
	HRESULT hr = m_spPivotTable->PivotCache(&spPivotCache);
	if (FAILED(hr) || !spPivotCache)
		return hr;
	hr = spPivotCache->Refresh();
	if (FAILED(hr))
		return hr;

	// 更新数据透视表信息
	Reset();

	return S_OK;
}

BOOL KAnalysisPivotTable::IsEmptyAnalysis()
{
	if (!m_bInit)
		Init();

	if (!dataFieldList.empty() || !rowFieldList.empty() || !colFieldList.empty() || !pageFieldList.empty())
		return FALSE;
	return TRUE;
}

BOOL KAnalysisPivotTable::HasDimension()
{
	if (!m_bInit)
		Init();

	if (!rowFieldList.empty() || !colFieldList.empty())
		return TRUE;
	return FALSE;
}

BOOL KAnalysisPivotTable::IsUsedField(QString strField)
{
	if (!m_bInit)
		Init();

	for (auto it = rowFieldList.begin(); it != rowFieldList.end(); ++it)
	{
		const KDataField* pField = *it;
		if (pField && pField->GetName() == strField)
			return TRUE;
	}
	for (auto it = colFieldList.begin(); it != colFieldList.end(); ++it)
	{
		const KDataField* pField = *it;
		if (pField && pField->GetName() == strField)
			return TRUE;
	}
	for (auto it = dataFieldList.begin(); it != dataFieldList.end(); ++it)
	{
		const KDataField* pField = *it;
		if (pField && pField->GetName() == strField)
			return TRUE;
	}
	return FALSE;
}

BOOL KAnalysisPivotTable::IsUsedPageField(QString strField)
{
	if (!m_bInit)
		Init();

	for (auto it = pageFieldList.begin(); it != pageFieldList.end(); ++it)
	{
		const KDataField* pField = *it;
		if (pField && pField->GetName() == strField)
			return TRUE;
	}
	return FALSE;
}

HRESULT KAnalysisPivotTable::SetDataFieldFunc(const KDataField* pField, oldapi::ETConsolidationFunction nFunc)
{
	if (!pField || pField->GetType() != DataFieldTypeNumber)
		return E_FAIL;
	const KNumberField* pNumField = (const KNumberField*)pField;
	oldapi::ETConsolidationFunction nOldFunc = pNumField->GetUsedNumberFieldType();
	KComVariant varName(krt::utf16(GetFuncString(nOldFunc) + pField->GetName()));
	ks_stdptr<etoldapi::PivotField> spPivotField;
	HRESULT hr = m_spPivotTable->PivotFields(varName, (IKCoreObject**)(&spPivotField));
	if (FAILED(hr) || !spPivotField)
		return E_FAIL;
	hr = spPivotField->put_Function(nFunc);
	return hr;
}

void KAnalysisPivotTable::GetPivotTableFields(QStringList& qslRowFields, QStringList& qslColFields, QStringList& qslDataFields)
{
	if (!m_bInit)
		Init();

	for (auto it = rowFieldList.cbegin(); it != rowFieldList.cend(); ++it)
	{
		if (!(*it))
			continue;
		qslRowFields.push_back((*it)->GetName());
	}
	for (auto it = colFieldList.cbegin(); it != colFieldList.cend(); ++it)
	{
		if (!(*it))
			continue;
		qslColFields.push_back((*it)->GetName());
	}
	for (auto it = dataFieldList.cbegin(); it != dataFieldList.cend(); ++it)
	{
		if (!(*it))
			continue;
		qslDataFields.push_back((*it)->GetName());
	}
}

ISmartUnderstand* KAnalysisPivotTable::GetUnderstand()
{
	if (!m_pUnderstand)
		m_pUnderstand = new KUnderstand(m_spSheet);
	return m_pUnderstand;
}

void KAnalysisPivotTable::CleanUnderstand()
{
	if (m_pUnderstand)
	{
		delete m_pUnderstand;
		m_pUnderstand = NULL;
	}
}

BOOL KAnalysisPivotTable::HasUnderstand()
{
	return m_pUnderstand ? TRUE : FALSE;
}

BOOL KAnalysisPivotTable::GetIsRecommend()
{
	return m_isRecommend;
}

void KAnalysisPivotTable::SetIsRecommend(BOOL isRecommend)
{
	m_isRecommend = isRecommend;
}

BOOL KAnalysisPivotTable::GetIsOnlyUsed()
{
	return m_isOnlyUsed;
}

void KAnalysisPivotTable::SetIsOnlyUsed(BOOL isOnlyUsed)
{
	m_isOnlyUsed = isOnlyUsed;
}

QString KAnalysisPivotTable::GetFindField()
{
	return m_strFindField;
}

void KAnalysisPivotTable::SetFindField(QString strName)
{
	m_strFindField = strName;
}

int KAnalysisPivotTable::GetUsedNumberFieldTypeList(std::vector<std::pair<QString, QString>>& vecNumFieldType)
{
	if (!vecNumFieldType.empty())
		vecNumFieldType.clear();

	for (auto it = dataFieldList.begin(); it != dataFieldList.end(); ++it)
	{
		const KNumberField* pNumField = (const KNumberField*)(*it);
		if (!pNumField)
			continue;
		vecNumFieldType.push_back(std::make_pair(pNumField->GetName(), TransformNumberFieldType(pNumField->GetUsedNumberFieldType())));
	}

	return (int)vecNumFieldType.size();
}

QStringList KAnalysisPivotTable::GetInsertFieldList()
{
	QStringList qslFieldList;
	for (auto it = m_vecInsertFieldList.begin(); it != m_vecInsertFieldList.end(); ++it)
		if (*it)
			qslFieldList.push_back((*it)->GetName());
	return qslFieldList;
}

KAnalysisTable::KAnalysisTable(etoldapi::_Workbook* pBook)
	: m_spBook(pBook), m_pFieldMap(NULL), m_nFieldRowCount(0), m_nNotEmptyCellCount(0), m_isRecommend(FALSE), m_pRecommendUnderstand(nullptr)
{
}

KAnalysisTable::~KAnalysisTable()
{
	for (auto it = m_vecPivotTable.begin(); it != m_vecPivotTable.end(); ++it)
	{
		if (*it)
		{
			delete *it;
			*it = NULL;
		}
	}
	if (m_pFieldMap)
	{
		delete m_pFieldMap;
		m_pFieldMap = NULL;
	}
	if (m_pRecommendUnderstand)
	{
		delete m_pRecommendUnderstand;
		m_pRecommendUnderstand = nullptr;
	}
}

HRESULT RenameSheet(etoldapi::_Worksheet* pSheet, etoldapi::_Workbook* pBook, QString& strSheet, bool bOne = true)
{
	HRESULT hr = S_OK;

	ks_stdptr<etoldapi::Worksheets> spSheets;
	hr = pBook->get_Worksheets(&spSheets);
	if (FAILED(hr) || !spSheets)
		return E_FAIL;

	long nCnt = 0;
	spSheets->get_Count(&nCnt);
	std::set<int> setIdx;
	for (long i = 1; i <= nCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<etoldapi::_Worksheet> spSheet;
		hr = spSheets->get_Item(varIdx, (IKCoreObject**)(&spSheet));
		if (FAILED(hr) || !spSheet)
			continue;
		ks_bstr bstrName;
		hr = spSheet->get_Name(&bstrName);
		if (FAILED(hr) || bstrName.empty())
			continue;
		QString strName = krt::fromUtf16(bstrName.c_str());
		if (!strName.startsWith(strSheet))
			continue;
		if (!bOne && strName == strSheet)
		{
			setIdx.insert(1);
			continue;
		}
		strName = strName.mid(strSheet.length());
		bool bInt = false;
		int nNum = strName.toInt(&bInt);
		if (bInt && nNum > 0)
			setIdx.insert(nNum);
	}

	int nIdx = 1;
	if (!setIdx.empty())
		for (auto it = setIdx.begin(); it != setIdx.end(); ++it, ++nIdx)
			if (*it != nIdx)
				break;
	
	if (bOne || nIdx > 1)
		strSheet += QString::number(nIdx);
	if (pSheet)
	{
		ks_bstr bstr(krt::utf16(strSheet));
		hr = pSheet->put_Name(bstr);
	}
	return hr;
}

long GetRangeRowCount(etoldapi::Range* pRange)
{
	if (!pRange)
		return 0;
	ks_stdptr<etoldapi::Range> spRows;
	HRESULT hr = pRange->get_Rows(&spRows);
	if (FAILED(hr) || !spRows)
		return 0;
	long nCnt = 0;
	spRows->get_Count(&nCnt);
	return nCnt > 0 ? nCnt - 1 : nCnt;	// 去掉行标题
}

HRESULT SetCellNumberFormat(IKETBaseFormat* areaSevFormat, ROW row, COL col, const QString& strNumberFormat)
{
	KXF _kxf;
	_kxf.getmask()._cats = XFMASK::_cat_fWrap;
	_kxf.getxf().fWrap = 0;

	NUMFMT numfmt;
	numfmt.reserved = 0;
	kcwcsncpy_s(numfmt.fmt, krt::utf16(strNumberFormat), MAX_NUMBERFMT_CCH);
	numfmt.fmt[MAX_NUMBERFMT_CCH] = __Xc('\0');
	_kxf.getmask()._cats = (XFMASK::_category)(_kxf.getmask()._cats | XFMASK::_catNum);
	_kxf.getxf().pNumFmt = &numfmt;
	return areaSevFormat->SetCellFormat(row, col, _kxf);
}

HRESULT KAnalysisTable::InsertTable(int idxSheet, QString strSourceSheetName, QJsonObject& jsonTable, ISmartAnalysisListener* pListener)
{
	if (!m_spBook || jsonTable.empty())
		return E_FAIL;

	HRESULT hr = S_OK;
	// 插入工作表
	ks_stdptr<IKWorkbook> spBook = m_spBook;
	QString strSheetName = strSourceSheetName + staticStrTableSheet;
	RenameSheet(NULL, m_spBook, strSheetName, false);
	hr = spBook->NewWorksheet(idxSheet, krt::utf16(strSheetName), &m_spSrcSheet);
	if (FAILED(hr) || !m_spSrcSheet)
		return E_FAIL;
	m_spSrcSheet->Activate(FALSE);
	ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_spSrcSheet;

	// 填入各字段数据
	RANGE rg(spWorksheet->GetSheet()->GetBMP());
	if (!jsonTable.contains("cleanTable") || !jsonTable["cleanTable"].isObject())
		return E_FAIL;
	QJsonObject jsonCleanTable = jsonTable["cleanTable"].toObject();
	if (!jsonCleanTable.contains("columnList") || !jsonCleanTable["columnList"].isArray())
		return E_FAIL;
	QJsonArray jsonColumnList = jsonCleanTable["columnList"].toArray();
	if (jsonColumnList.isEmpty())
		return E_FAIL;
	QJsonObject jsonColumn0 = jsonColumnList[0].toObject();
	if (!jsonColumn0.contains("name") || !jsonColumn0["name"].isString() ||
		!jsonColumn0.contains("cellList") || !jsonColumn0["cellList"].isArray())
		return E_FAIL;
	QJsonArray jsonCellList0 = jsonColumn0["cellList"].toArray();
	if (jsonCellList0.isEmpty())
		return E_FAIL;
	rg.SetSheetFromTo(idxSheet);
	rg.SetRowFromTo(0, jsonCellList0.size());
	rg.SetColFromTo(0, jsonColumnList.size() - 1);

	et_sdptr<IKETBaseGridAreaService> spAreaSev(_etbase_CreateGridAreaService(spBook->GetBook()->LeakOperator(), rg));

	if (!spAreaSev)
		return E_FAIL;
	m_nNotEmptyCellCount = 0;
	ks_stdptr<ISmartAnalysisListener> spListener = pListener;
	for (int j = 0; j < jsonColumnList.size(); ++j)
	{
		if (!jsonColumnList[j].isObject())
			continue;
		QJsonObject jsonColumn = jsonColumnList[j].toObject();
		if (!jsonColumn.contains("name") || !jsonColumn["name"].isString() ||
			!jsonColumn.contains("cellList") || !jsonColumn["cellList"].isArray())
			continue;

		// 设置行标题内容
		QString strName = jsonColumn["name"].toString();
		QString strClassType = jsonColumn["classType"].toString();

		spAreaSev->GetContent2()->SetCellValue(0, j, krt::utf16(strName), TRUE);

		// 设置数据内容
		QJsonArray jsonCellList = jsonColumn["cellList"].toArray();
		ASSERT_ONCE(jsonCellList0.size() == jsonCellList.size());
		for (int k = 0; k < jsonCellList.size() && k < jsonCellList0.size(); ++k)
		{
			if (!jsonCellList[k].isObject())
				continue;
			QJsonObject jsonCell = jsonCellList[k].toObject();
			QString strNumberFormat;
			if (jsonCell.contains("numberFormatLocal") && jsonCell["numberFormatLocal"].isString())
				strNumberFormat = jsonCell["numberFormatLocal"].toString();
			// 设置NumberFormat，如果是文本格式需要先设置，防止文本类型数字精度会被截断
			if (strNumberFormat == QString("@"))
				SetCellNumberFormat(spAreaSev->GetFormat(), k + 1, j, strNumberFormat);
			// 设置Text
			if (jsonCell.contains("value2") && jsonCell["value2"].isString())
			{
				QString strValue2 = jsonCell["value2"].toString();
				if (!strValue2.isEmpty())
				{
					spAreaSev->GetContent()->SetCellValue(k + 1, j, krt::utf16(strValue2), cvoNormal);
					m_nNotEmptyCellCount++;
				}
			}
			// 设置NumberFormat
			if (!strNumberFormat.isEmpty() && strNumberFormat != QString("@"))
				SetCellNumberFormat(spAreaSev->GetFormat(), k + 1, j, strNumberFormat);
		}
	}
	// 在A1插入表
	ks_stdptr<etoldapi::ListObjects> spListObjects;
	hr = spWorksheet->get_ListObjects(&spListObjects);
	if (FAILED(hr) || !spListObjects)
		return E_FAIL;
	ks_stdptr<etoldapi::Range> spTableRange;
	hr = spWorksheet->GetRangeByData(&rg, &spTableRange);
	if (FAILED(hr) || !spTableRange)
		return E_FAIL;
	KComVariant varRange(spTableRange);
	KComVariant var;
	ks_stdptr<etoldapi::ListObject> spListObject;
	hr = spListObjects->Add(oldapi::xlSrcRange, varRange, var, oldapi::xlYes, var, var, &spListObject);
	if (FAILED(hr) || !spListObject)
		return E_FAIL;
	hr = spListObject->get_Range(&m_spSrcRange);
	if (FAILED(hr) || !spTableRange)
		return E_FAIL;
	m_nFieldRowCount = GetRangeRowCount(m_spSrcRange);
	// 选区到表格右上角区域外
	rg.SetRowFromTo(0);
	rg.SetColFromTo(jsonColumnList.size());
	ks_stdptr<etoldapi::Range> spSelectRange;
	hr = CreateRange(spWorksheet, rg, &spSelectRange);
	if (SUCCEEDED(hr) && spSelectRange)
		hr = spSelectRange->Select();
	// 如果选区在屏幕外，窗口滑动到A1位置
	ks_stdptr<etoldapi::Window> spActiveWindow = spWorksheet->GetApplication()->GetActiveWindow();
	if (spActiveWindow)
		hr = spActiveWindow->put_ScrollColumn(1);
	return S_OK;
}

HRESULT KAnalysisTable::InsertPivotTable()
{
	if (!m_spBook || !m_spSrcSheet || !m_spSrcRange)
		return E_FAIL;

	HRESULT hr = S_OK;
	ks_stdptr<etoldapi::Worksheets> spSheets;
	hr = m_spBook->get_Worksheets(&spSheets);
	if (FAILED(hr) || !spSheets)
		return E_FAIL;

	// 插入工作表
	IDX iSheet = 0;
	if (m_spSrcSheet->GetSheet())
		m_spSrcSheet->GetSheet()->GetIndex(&iSheet);
	KComVariant varBefore(iSheet + 1, VT_I4);
	KComVariant var;
	ks_stdptr<etoldapi::_Worksheet> spWorksheet;
    // todo : fix insert compile problem
	// hr = spSheets->Add(varBefore, var, var, var, (IKCoreObject**)(&spWorksheet));
	if (FAILED(hr) || !spWorksheet)
		return E_FAIL;
	QString strSheetName = staticStrAnalysisSheet;
	RenameSheet(spWorksheet, m_spBook, strSheetName);
	// 设置网格线不显示
	ks_stdptr<IKWorksheetView> spView = spWorksheet->GetActiveWorksheetView();
	hr = spView->SetDisplayGridlines(FALSE);

	m_vecPivotTable.push_back(new KAnalysisPivotTable(spWorksheet, NULL, this));
	return S_OK;
}

//通过公式的toke得到double值const alg::ExecToken* pToken, pToken只能是
//alg::ETP_VINT 或 alg::ETP_VDBL
HRESULT GetDoubleValue(const alg::ExecToken* pToken, double& dValue)
{
	alg::const_token_assist ta(pToken);
	if (ta.major_type() == alg::ETP_VINT)
		dValue = alg::const_vint_token_assist(pToken).get_value();
	else if (ta.major_type() == alg::ETP_VDBL)
		dValue = alg::const_vdbl_token_assist(pToken).get_value();
	else
		return E_FAIL;
	return S_OK;
}

void GetTokenValue(const alg::ExecToken* pToken, ForStatusIndex FSI, std::vector<double>::iterator itVal, std::vector<bool>::iterator itErr)
{
	if (*itErr)
		return;
	double dbtmp = 0.;
	*itErr = (S_OK != GetDoubleValue(pToken, dbtmp)) ? true : false;
	if (!*itErr)
	{
		switch (FSI)
		{
		case FSI_COUNT:
		case FSI_NUMCOUNT:
		case FSI_SUM:
			*itVal += dbtmp;
			break;
		case FSI_MAX:
				*itVal = dbtmp;
			break;
		case FSI_MIN:
				*itVal = dbtmp;
			break;
		case FSI_AVERAGE:
		default:
			ASSERT(FALSE);	//	这是个悲剧
			break;
		}
	}
}

void AddToResult(const alg::ExecToken* pToken, std::vector<double>& valVec, std::vector<bool>& errVec)
{
	if (!alg::const_matrix_token_assist::is_type(pToken))
		return;
	// 一遇到ErrorToken errVec就被置为true // average现在不计算
	alg::const_matrix_token_assist cmta(pToken);
	ASSERT(MAX_OF_FSI == cmta.get_width());

	std::vector<double>::iterator valIt = valVec.begin();
	std::vector<bool>::iterator errIt = errVec.begin();

	GetTokenValue(cmta.get_item(FSI_COUNT, 0), FSI_COUNT, valIt + FSI_COUNT, errIt + FSI_COUNT);
	GetTokenValue(cmta.get_item(FSI_NUMCOUNT, 0), FSI_NUMCOUNT, valIt + FSI_NUMCOUNT, errIt + FSI_NUMCOUNT);
	GetTokenValue(cmta.get_item(FSI_MAX, 0), FSI_MAX, valIt + FSI_MAX, errIt + FSI_MAX);
	GetTokenValue(cmta.get_item(FSI_MIN, 0), FSI_MIN, valIt + FSI_MIN, errIt + FSI_MIN);
	GetTokenValue(cmta.get_item(FSI_SUM, 0), FSI_SUM, valIt + FSI_SUM, errIt + FSI_SUM);

	errVec.at(FSI_AVERAGE) = errVec.at(FSI_SUM);
	if (!errVec.at(FSI_AVERAGE) && valVec.at(FSI_NUMCOUNT) > 0)
		valVec.at(FSI_AVERAGE) = valVec.at(FSI_SUM) / valVec.at(FSI_NUMCOUNT);
}

HRESULT MakeTokenNoError(const RANGE& rg, IBookOp* pBookOp, IFormula* pFormula)
{
	HRESULT hr = S_OK;
	int iSheet = rg.SheetFrom();
	int iCol = rg.ColFrom();
	int iRow = rg.RowFrom();
	int iLast = -1;
	alg::managed_stref_token_assist tokenRef;
	DWORD dwFlag = (alg::ETREF_REGION | alg::ETRA_LEFT | alg::ETRA_TOP | alg::ETRA_RIGHT | alg::ETRA_BOTTOM);
	tokenRef.create(dwFlag, alg::ET_RVA_NONE);
	for (; iRow <= rg.RowTo(); ++iRow)
	{
		const_token_ptr pt = nullptr;
		hr = pBookOp->GetCellValue(iSheet, iRow, iCol, &pt);
		if (FAILED(hr) || !pt)
			continue;
		if (!etexec::_IsErrorToken(pt))
		{
			if (iLast < 0)
				iLast = iRow;
			continue;
		}
		if (iLast >= 0)
		{
			tokenRef.make_region(0, iSheet, iSheet, iLast, iRow - 1, iCol, iCol, TRUE, TRUE, TRUE, TRUE, TRUE);
			iLast = -1;
			hr = pFormula->AddFuncCallArgument(tokenRef);
			if (FAILED(hr))
				return hr;
		}
	}
	if (iLast >= 0)
	{
		tokenRef.make_region(0, iSheet, iSheet, iLast, iRow - 1, iCol, iCol, TRUE, TRUE, TRUE, TRUE, TRUE);
		iLast = -1;
		hr = pFormula->AddFuncCallArgument(tokenRef);
		if (FAILED(hr))
			return hr;
	}

	return hr;
}

HRESULT GetTipValue(ISheet* pSheet, RANGE rg, std::vector<double>& valVec, std::vector<bool>& errVec)
{
	ks_stdptr<IBook> spBook;
	HRESULT hr = pSheet->GetBook(&spBook);
	if (FAILED(hr) || !spBook)
		return hr;
	ks_stdptr<IBookOp> spBookOp;
	hr = spBook->GetOperator(&spBookOp);
	if (FAILED(hr) || !spBookOp)
		return hr;

	bool bFirstGroup = true;
	size_t idx = 0;
	ks_stdptr<IFormula>	spFormula;
	hr = spBookOp->CreateFormula(&spFormula);
	if (FAILED(hr) || !spFormula)
		return hr;
	hr = spFormula->BeginBuildForStatus(FALSE);
	if (FAILED(hr))
		return hr;
	hr = MakeTokenNoError(rg, spBookOp, spFormula);
	if (FAILED(hr))
		return hr;
	alg::managed_token_assist tokenResult;
	hr = spFormula->EndBuildForStatus(&tokenResult);
	if (FAILED(hr))
		return hr;
	AddToResult(tokenResult.tag(), valVec, errVec);
	return S_OK;
}

HRESULT InitFieldInfoByFormula(ISheet* pSheet, RANGE& rg, KDataFieldList& allFieldList)
{
	if (!pSheet)
		return E_FAIL;
	if ((int)allFieldList.size() > rg.ColTo() - rg.ColFrom() + 1)
		return E_FAIL;
	HRESULT hr = S_OK;
	int iColFrom = rg.ColFrom();
	for (int i = 0; i < (int)allFieldList.size(); ++i)
	{
		KDataField* pField = allFieldList[i];
		if (!pField->IsNumberField())
			continue;

		KNumberField* pNumField = (KNumberField*)pField;
		std::vector<double> valVec(MAX_OF_FSI, 0);
		std::vector<bool> errVec(MAX_OF_FSI, false);
		int iCol = iColFrom + pField->GetIndexInt();
		rg.SetColFromTo(iCol, iCol);
		hr = GetTipValue(pSheet, rg, valVec, errVec);

		pNumField->SetSum(valVec[FSI_SUM]);
		pNumField->SetAverage(valVec[FSI_AVERAGE]);
		pNumField->SetMax(valVec[FSI_MAX]);
		pNumField->SetMin(valVec[FSI_MIN]);
	}

	return hr;
}

HRESULT KAnalysisTable::InitFieldInfo()
{
	HRESULT hr = S_OK;
	RANGE rg(m_spSrcSheet->GetSheet()->GetBMP());
	IdentifyTool::GetTableRange(m_spSrcRange, &rg);
	hr = InitFieldInfoByFormula(m_spSrcSheet->GetSheet(), rg, m_allFieldList);
	return hr;
}

void InitDateFieldItem(KStringField* pField, ValuesNode* pValuesNode, QString& strText)
{
	if (pValuesNode->childs.empty())
	{
		QString strTextSet = strText;
		if (!strText.contains(__Xc(':')))
			strTextSet = strText + krt::fromUtf16(pValuesNode->bstrText);
		int nCnt = pValuesNode->cntValues;
		pField->AddDataFieldItem(strTextSet, nCnt);
		return;
	}

	for (auto it = pValuesNode->childs.begin(); it != pValuesNode->childs.end(); ++it)
	{
		ValuesNode* pNode = *it;
		BYTE nType = pNode->nNodeType;
		if (avType_DATE == nType)
		{
			QString strTextSet = strText + krt::fromUtf16(pValuesNode->bstrText);
			InitDateFieldItem(pField, pNode, strTextSet);
		}
	}
}

HRESULT InitFieldItemByAutoFilter(IKAutoFilter* pAutoFilter, KDataFieldList& allFieldList)
{
	if (!pAutoFilter)
		return E_FAIL;

	HRESULT hr = S_OK;
	for (int i = 0; i < (int)allFieldList.size(); ++i)
	{
		KDataField* pField = allFieldList[i];
		if (!pField || pField->IsNumberField())
			continue;
		KStringField* pStrField = (KStringField*)pField;
		ks_stdptr<IAutoFilterValues> spAutoFilterValues;
		ValuesNode* pNodeRoot = NULL;
		hr = pAutoFilter->GetFilterItems(pField->GetIndexInt(), ETStringToolsOpt_DisplayText, FALSE, EOp_None, &spAutoFilterValues, &pNodeRoot);
		if (SUCCEEDED(hr) && pNodeRoot)
		{
			for (auto it = pNodeRoot->childs.begin(); it != pNodeRoot->childs.end(); ++it)
			{
				ValuesNode* pValuesNode = *it;
				BYTE nType = pValuesNode->nNodeType;
				if (avType_DATE == nType)
				{
					QString strText;
					InitDateFieldItem(pStrField, pValuesNode, strText);
				}
				else if (avType_NUMBER == nType || avType_STRING == nType || avType_BLANK == nType || avType_BOOL == nType)
				{
					QString strText =krt::fromUtf16(pValuesNode->bstrText);
					if (strText.isEmpty())
						strText = staticStrEmpty;
					int nCnt = pValuesNode->cntValues;
					pStrField->AddDataFieldItem(strText, nCnt);
				}
			}
			pStrField->SortItemList();
		}
	}
	return S_OK;
}

HRESULT KAnalysisTable::InitFieldItem()
{
    // TODO : fix shared filter compile problem
	// ks_stdptr<IKTransactionTool> spTransTool = m_spBook->GetTransactionTool();
	// spTransTool->StartTrans();

	// KComVariant var;
	// ks_stdptr<etoldapi::ListObject> spListObject;
	// HRESULT hr = m_spSrcRange->get_ListObject(&spListObject);
	// if (SUCCEEDED(hr) && spListObject)
	// {
	// 	hr = spListObject->put_ShowAutoFilter(VARIANT_TRUE);
	// 	ks_stdptr<etoldapi::AutoFilter> spApiAutoFilter;
	// 	hr = spListObject->get_AutoFilter(&spApiAutoFilter);
	// 	if (SUCCEEDED(hr) && spApiAutoFilter)
	// 	{
	// 		ks_stdptr<IAutoFilterInfo> spAutoFilterInfo = spApiAutoFilter;
	// 		ks_stdptr<IKAutoFilter> spAutoFilter;
	// 		hr = spAutoFilterInfo->GetCoreAutoFilter(&spAutoFilter);
	// 		if (SUCCEEDED(hr) && spAutoFilter)
	// 		{
	// 			hr = spAutoFilter->ShowAll();
	// 			if (SUCCEEDED(hr))
	// 				InitFieldItemByAutoFilter(spAutoFilter, m_allFieldList);
	// 		}
	// 	}
	// }
	// else
	// {
	// 	ks_stdptr<IKAutoFilter> spAutoFilter = m_spSrcSheet->GetCoreAutoFilter();
	// 	if (spAutoFilter)
	// 	{
	// 		hr = spAutoFilter->ShowAll();
	// 		if (SUCCEEDED(hr))
	// 			InitFieldItemByAutoFilter(spAutoFilter, m_allFieldList);
	// 	}
	// 	else
	// 	{
	// 		KComVariant varAutoFilter;
	// 		hr = m_spSrcRange->AutoFilter(var, var, oldapi::etAnd, var, var, &varAutoFilter);
	// 		if (SUCCEEDED(hr))
	// 			spAutoFilter = m_spSrcSheet->GetCoreAutoFilter();
	// 		if (spAutoFilter)
	// 			InitFieldItemByAutoFilter(spAutoFilter, m_allFieldList);
	// 	}
	// }

	// spTransTool->Rollback();

	return S_OK;
}

QString GetFieldNameByRangeIdx(etoldapi::WorksheetFunction* pFunc, etoldapi::Range* pRange, int idx)
{
	if (!pRange)
		return QString();
	KComVariant varRow(1, VT_I4);
	KComVariant varCol(idx + 1, VT_I4);
	KComVariant varCell;
	HRESULT hr = pRange->get_Item(varRow, varCol, &varCell);
	if (FAILED(hr))
		return QString();
	ks_stdptr<Range> spCell = KSmartParam(varCell).GetInterfaceValue();
	if (!spCell)
		return QString();
	KComVariant varRg(varCell);
	VARIANT_BOOL isText = VARIANT_FALSE;
	if (pFunc)
		pFunc->IsText(varRg, &isText);
	if (isText == VARIANT_TRUE)
	{
		KComVariant varValue2;
		hr = spCell->get_Value2(&varValue2);
		if (SUCCEEDED(hr))
		{
			KComVariant varValue2Bstr;
			hr = ::VariantChangeType(&varValue2Bstr, &varValue2, 0, VT_BSTR);
			if (SUCCEEDED(hr) && V_VT(&varValue2Bstr) == VT_BSTR)
			{
				ks_wstring strVal = V_BSTR(&varValue2Bstr);
				QString strText = krt::fromUtf16(strVal.c_str());
				return strText;
			}
		}
	}
	ks_bstr bstrText;
	spCell->get_Text(&bstrText);
	QString strText = krt::fromUtf16(bstrText.c_str());
	return strText;
}

int GetFieldIdx(QJsonObject& jsonColumn)
{
	if (!jsonColumn.contains("indexName") || !jsonColumn["indexName"].isString() || jsonColumn["indexName"].toString().isEmpty())
		return -1;
	QString strIdxName = jsonColumn["indexName"].toString();
	if (strIdxName.isEmpty() || !strIdxName.startsWith("X"))
		return -1;
	bool bOK = false;
	int idx = strIdxName.mid(1).toInt(&bOK);
	if (!bOK || idx < 0)
		return -1;
	return idx;
}

KDataField* CreateKDataField(etoldapi::WorksheetFunction* pFunc, etoldapi::Range* pRange, std::map<QString, int>& mapFieldName, QJsonObject& jsonColumn)
{
	int idx = GetFieldIdx(jsonColumn);
	if (idx < 0)
		return NULL;
	QString strName = GetFieldNameByRangeIdx(pFunc, pRange, idx);
	if (strName.isEmpty())
		return NULL;
	if (mapFieldName.find(strName) != mapFieldName.end())
		strName = strName + QString::number(++mapFieldName[strName]);
	else
		mapFieldName[strName] = 1;

	QString strClassType = jsonColumn["classType"].toString();
	KDataFieldTypeEnum nType = TransformType(strClassType);
	KDataField* pField = NULL;
	switch (nType)
	{
	case DataFieldTypeNumber:
		pField = new KNumberField(idx, jsonColumn, strName);
		break;
	case DataFieldTypeDate:
		pField = new KDateField(idx, jsonColumn, strName, pRange);
		break;
	default:
		pField = new KStringField(idx, jsonColumn, strName);
		break;
	}
	return pField;
}

HRESULT KAnalysisTable::InitAllDataField(QJsonObject& jsonTable)
{
	if (!jsonTable.contains("columnInfoList") || !jsonTable["columnInfoList"].isArray())
		return E_FAIL;
	ks_stdptr<etoldapi::_Application> spApp = m_spBook->GetApplication();
	ks_stdptr<etoldapi::WorksheetFunction> spFunc;
	if (spApp)
		spApp->get_WorksheetFunction(&spFunc);
	std::map<QString, int> mapFieldName;
	QJsonArray jsonColumnList = jsonTable["columnInfoList"].toArray();
	for (int j = 0; j < jsonColumnList.size(); ++j)
	{
		if (!jsonColumnList[j].isObject())
			continue;
		QJsonObject jsonColumn = jsonColumnList[j].toObject();
		KDataField* pField = CreateKDataField(spFunc, m_spSrcRange, mapFieldName, jsonColumn);
		if (!pField)
			continue;

		m_allFieldList.push_back(pField);
	}

	InitFieldItem();
	InitFieldInfo();

	if (jsonTable.contains("relatedList") && jsonTable["relatedList"].isArray())
	{
		QJsonArray jsonRelatedList = jsonTable["relatedList"].toArray();
		for (int i = 0; i < jsonRelatedList.size(); ++i)
		{
			QJsonObject jsonRelated = jsonRelatedList[i].toObject();
			if (!jsonRelated.contains("items") || !jsonRelated["items"].isArray() || !jsonRelated.contains("include") || !jsonRelated.contains("cross"))
				continue;
			double dInclude = jsonRelated["include"].toDouble();
			double dCross = jsonRelated["cross"].toDouble();
			bool bInclude1 = dCross < IncludePredValueMin && (dInclude > IncludePredValueMin || qFuzzyCompare(dInclude, IncludePredValueMin));
			bool bInclude2 = (dCross < 0.0 || qFuzzyCompare(dCross, 0.0)) && (dInclude > 0.0 || qFuzzyCompare(dInclude, 0.0));
			if (bInclude1 || bInclude2)	// 预值
			{
				QJsonArray jsonRelatedItems = jsonRelated["items"].toArray();
				if (jsonRelatedItems.size() != 2)
					continue;
				QString strField1 = jsonRelatedItems[0].toString();
				QString strField2 = jsonRelatedItems[1].toString();
				KDataField* pField1 = m_allFieldList.FindByIndex(strField1);
				KDataField* pField2 = m_allFieldList.FindByIndex(strField2);
				if (!pField1 || !pField2 || pField1->IsNumberField() || pField2->IsNumberField())
					continue;
				if (!bInclude1 && pField1->GetClassType() != pField2->GetClassType())
					continue;
				((KStringField*)pField1)->AddIncludeField(pField2->GetName(), dInclude);
				((KStringField*)pField2)->AddIncludeField(pField1->GetName(), dInclude);
			}
		}
	}

	if (!m_pFieldMap && jsonTable.contains("fieldMap") && jsonTable["fieldMap"].isObject())
	{
        // todo : fix compile
		// m_pFieldMap = new KFieldMap(jsonTable["fieldMap"].toObject(), m_allFieldList);
	}

	if (!m_pRecommendUnderstand && jsonTable.contains("RecommendChartList") && jsonTable["RecommendChartList"].isArray())
	{
        // todo : fix compile
		// m_pRecommendUnderstand = new KRecommendUnderstand(m_spSrcSheet, jsonTable["RecommendChartList"].toArray());
	}

	if (jsonTable.contains("labelList") && jsonTable["labelList"].isArray())
	{
		QJsonArray jsonLableList = jsonTable["labelList"].toArray();
		if (!jsonLableList.isEmpty())
			m_strLabel = jsonLableList[0].toString();
	}

	return S_OK;
}

namespace
{
	class KxEtSmartSheetEnum : public ICellValueAcpt {
	public:
		KxEtSmartSheetEnum() : m_nCount(0) {}
		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if (pToken)
				m_nCount++;
			return 0;
		}
		long getNotEmptyCellCount()const { return m_nCount; }
	private:
		long m_nCount;
	};
}

long GetCellCount(IKWorksheet* pSheet, Range* pRange)
{
	ks_stdptr<ISheet> spISheet = pSheet->GetSheet();
	et_sdptr<ISheetEnum> spEnum;
	spISheet->CreateEnum(&spEnum);
	KxEtSmartSheetEnum CellEnum;
	RANGE rg(spISheet->GetBMP());
	IdentifyTool::GetTableRange(pRange, &rg);
	// 跳过行标题
	if (rg.RowTo() > rg.RowFrom())
		rg.SetRowFrom(rg.RowFrom() + 1);
	spEnum->EnumCellValue(rg, &CellEnum);
	return CellEnum.getNotEmptyCellCount();
}

void KAnalysisTable::SetSource(IKWorksheet* pSheet, etoldapi::Range* pRange)
{
	if (!pSheet || !pRange)
		return;
	m_spSrcSheet = pSheet;
	m_spSrcRange = pRange;
	m_nFieldRowCount = GetRangeRowCount(m_spSrcRange);
	m_nNotEmptyCellCount = GetCellCount(pSheet, m_spSrcRange);
}

IKWorksheet* KAnalysisTable::GetSourceSheet()
{
	return m_spSrcSheet;
}

etoldapi::Range* KAnalysisTable::GetSourceRange()
{
	return m_spSrcRange;
}

int KAnalysisTable::GetAnalysisPivotTableCount()
{
	return m_vecPivotTable.size();
}

KAnalysisPivotTable* KAnalysisTable::GetAnalysisPivotTable(int idx)
{
	if (idx < 0 && idx >= (int)m_vecPivotTable.size())
		return NULL;
	return m_vecPivotTable[idx];
}

KDataFieldList& KAnalysisTable::GetAllDataField()
{
	return m_allFieldList;
}

const KDataField* KAnalysisTable::GetDataField(QString strName) const
{
	const KDataField* pField = m_allFieldList.FindByName(strName);
	return pField;
}

KDataField* KAnalysisTable::GetDataField(QString strName)
{
	KDataField* pField = m_allFieldList.FindByName(strName);
	return pField;
}

HRESULT KAnalysisTable::AddUserPivotTable(etoldapi::PivotTable* pPivotTable)
{
	if (!pPivotTable)
		return E_INVALIDARG;
	KAnalysisPivotTable* pAnalysis = new KAnalysisPivotTable(NULL, pPivotTable, this);
	if (!pAnalysis)
		return E_FAIL;
	m_vecPivotTable.push_back(pAnalysis);
	return S_OK;
}

void GetItemStr(QStringList& strItemList, ValuesNode* pValuesNode, QString& strText)
{
	if (pValuesNode->childs.empty())
	{
		strItemList.push_back(strText + krt::fromUtf16(pValuesNode->bstrText));
		return;
	}

	for (auto it = pValuesNode->childs.begin(); it != pValuesNode->childs.end(); ++it)
	{
		ValuesNode* pNode = *it;
		BYTE nType = pNode->nNodeType;
		if (avType_DATE == nType)
		{
			QString strTextSet = strText + krt::fromUtf16(pValuesNode->bstrText);
			GetItemStr(strItemList, pNode, strTextSet);
		}
	}
}

QString TransformFilterString(const QString& str)
{
	int iYear = str.indexOf(staticStrDateUnitYear);
	int iMonth = str.indexOf(staticStrDateUnitMonth);
	int iDay = str.indexOf(staticStrDateUnitDay);
	int iHour = str.indexOf(staticStrDateUnitMaoHao);
	if (iYear < 0 || iMonth < 0 || iDay < 0)
		return QString();
	QString strYear = str.mid(0, iYear);
	QString strMonth = str.mid(iYear + 1, iMonth - iYear - 1);
	QString strDay = str.mid(iMonth + 1, iDay - iMonth - 1);
	QString strHour;
	QString strMinute;
	if (iHour >= 0)
	{
		strHour = str.mid(iDay + 1, iHour - iDay - 1);
		strMinute = str.mid(iHour + 1);
	}
	bool bOK = false;
	int nYear = strYear.toInt(&bOK);
	if (!bOK)
		return QString();
	int nMonth = strMonth.toInt(&bOK);
	if (!bOK)
		return QString();
	int nDay = strDay.toInt(&bOK);
	if (!bOK)
		return QString();
	if (!strHour.isEmpty() && !strMinute.isEmpty())
	{
		int nHour = strHour.toInt(&bOK);
		if (bOK)
		{
			int nMinute = strMinute.toInt(&bOK);
			if (bOK)
				return strMonth + "/" + strDay + "/" + strYear + " " + strHour + ":" + strMinute;
		}
	}
	return strMonth + "/" + strDay + "/" + strYear;
}

QString GetAutoFilterStr(QString strItem, const KDataFieldItemList& itemList)
{
	if (itemList.empty())
		return QString();
	QString strLastItem;
	for (auto it = itemList.crbegin(); it != itemList.crend(); ++it)
	{
		const KDataFieldItem* pItem = *it;
		if (!pItem)
			continue;
		if (!pItem->GetName().isEmpty() && pItem->GetName().contains(strItem))
			strLastItem = pItem->GetName();
	}
	if (strLastItem.isEmpty())
		return QString();
	return TransformFilterString(strLastItem);
}

KComVariant GetCriteria2Array(std::vector<std::pair<int, QString>>& vecDate)
{
	KComVariant varCriteria2;
	SIZE32 lCount = vecDate.size();
	SAFEARRAYBOUND bound = { static_cast<ULONG>(lCount) * 2, 0 };
	SAFEARRAY* psa = SafeArrayCreate(VT_VARIANT, 1, &bound);
	V_VT(&varCriteria2) = VT_ARRAY | VT_VARIANT;
	V_ARRAY(&varCriteria2) = psa;
	SIZE32 i = 0;
	for (auto it = vecDate.begin(); it != vecDate.end(); ++it)
	{
		std::pair<int, QString>& date = *it;
		KComVariant Element;
		V_VT(&Element) = VT_I4;
		V_I4(&Element) = date.first;
		::SafeArrayPutElement(psa, &i, &Element);
		i++;
		Element.AssignString(krt::utf16(date.second));
		::SafeArrayPutElement(psa, &i, &Element);
		i++;
	}
	return varCriteria2;
}

int TransformDateFilterIdx(KDateFieldTypeEnum dateType)
{
	switch (dateType)
	{
	case DateFieldTypeY:
		return 0;
	case DateFieldTypeYM:
	case DateFieldTypeM:
		return 1;
	case DateFieldTypeYMD:
	case DateFieldTypeMD:
	case DateFieldTypeD:
		return 2;
	case DateFieldTypeYMDH:
		return 3;
	case DateFieldTypeYMDHM:
		return 4;
	}
	return -1;
}

bool GetAutoFilterString(QString strItem, const KDateField* pField, KComVariant& var)
{
	if (!pField)
		return false;
	std::vector<std::pair<int, QString>> vecDate;
	QString strDate = GetAutoFilterStr(strItem, pField->GetDataFieldItemList());
	if (strDate.isEmpty())
		return false;
	int nDateFilterIdx = TransformDateFilterIdx(pField->GetDateTypeEnum());
	if (nDateFilterIdx >= 0)
		vecDate.push_back(std::make_pair(nDateFilterIdx, strDate));
	var = GetCriteria2Array(vecDate);
	return true;
}

HRESULT KAnalysisTable::GetFilterInfo(const KDataField* pField1, QString strItem, IKAutoFilter* pAutoFilter, QJsonArray& jsonObj)
{
	if (!m_spSrcSheet || !m_spSrcRange)
		return E_FAIL;
	HRESULT hr = S_OK;
	if (!pField1)
		return S_FALSE;
	if (strItem == staticStrEmpty)
		strItem.clear();
	int idx = pField1->GetIndexInt();
	ks_stdptr<IAutoFilterValues> spAutoFilterValues;
	ValuesNode* pNodeRoot = NULL;
	hr = pAutoFilter->GetFilterItems(idx, ETStringToolsOpt_DisplayText, FALSE, EOp_None, &spAutoFilterValues, &pNodeRoot);
	if (FAILED(hr) || !spAutoFilterValues)
		return E_FAIL;
	ET_CUSTOM_FILTER_TYPE filterItemType = spAutoFilterValues->GetValuesType();

	KComVariant varIdx(idx + 1, VT_I4);
	KComVariant varCriteria1;
	KComVariant varCriteria2;
	KComVariant var;
	KComVariant varFilter;
	bool bOk = false;
	if (!strItem.isEmpty() && filterItemType == customFilterType_Date && pField1->GetType() == DataFieldTypeDate)
	{
		const KDateField* pDateField = (const KDateField*)pField1;
		bOk = GetAutoFilterString(strItem, pDateField, varCriteria2);
        // TODO : fix shared filter compile problem
		// if (bOk)
		// 	hr = m_spSrcRange->AutoFilter(varIdx, varCriteria1, oldapi::etFilterValues, varCriteria2, var, &varFilter);
	}
	if (!bOk)
	{
		varCriteria1.AssignString(krt::utf16(strItem));
        // TODO : fix shared filter compile problem
		// hr = m_spSrcRange->AutoFilter(varIdx, varCriteria1, oldapi::etAnd, varCriteria2, var, &varFilter);
	}
	if (FAILED(hr))
		return E_FAIL;

	// todo suyihong 把当前字段选择的项加进去
	KDataFieldList autoFilterFieldList;
	for (int i = 0; i < (int)m_allFieldList.size(); ++i)
	{
		const KDataField* pField = m_allFieldList[i];
		if (!pField || pField == pField1)
			continue;

		KDataField* pAutoFilterField = NULL;
		switch (pField->GetType())
		{
		case DataFieldTypeNumber:
			pAutoFilterField = new KNumberField(pField->GetIndexInt(), pField->GetName(), ((KNumberField*)pField)->IsPercent(), ((KNumberField*)pField)->GetNumberFieldType());
			break;
		case DataFieldTypeDate:
			pAutoFilterField = new KDateField(pField->GetIndexInt(), pField->GetName(), ((KDateField*)pField)->GetDateTypeEnum());
			break;
		default:
			pAutoFilterField = new KStringField(pField->GetIndexInt(), pField->GetName());
			break;
		}
		if (!pAutoFilterField)
			continue;
		autoFilterFieldList.push_back(pAutoFilterField);
	}

	InitFieldItemByAutoFilter(pAutoFilter, autoFilterFieldList);
	RANGE rg(m_spSrcSheet->GetSheet()->GetBMP());
	IdentifyTool::GetTableRange(m_spSrcRange, &rg);
	InitFieldInfoByFormula(m_spSrcSheet->GetSheet(), rg, autoFilterFieldList);

	for (auto it = autoFilterFieldList.begin(); it != autoFilterFieldList.end(); ++it)
	{
		KDataField* pField = *it;
		if (!pField)
			continue;
		QJsonObject jsonField;
		pField->ExportJson(jsonField, MaxFieldItemCount);
		jsonObj.append(jsonField);
	}

	return S_OK;
}

HRESULT KAnalysisTable::GetFilterInfoNumber(QString strField, oldapi::ETConsolidationFunction type)
{
	KDataField* pField = m_allFieldList.FindByName(strField);
	if (!pField || pField->GetType() != DataFieldTypeNumber)
		return E_FAIL;
	((KNumberField*)pField)->SetUsedNumberFieldType(type);
	return S_OK;
}

HRESULT KAnalysisTable::GetFilterInfo(const KDataField* pField, QString strItem, QJsonArray& jsonObj)
{
    // TODO : fix shared filter compile problem
	// if (!m_spSrcSheet || !m_spSrcRange || !pField)
	// 	return E_FAIL;

	// KComVariant var;
	// ks_stdptr<etoldapi::ListObject> spListObject;
	// HRESULT hr = m_spSrcRange->get_ListObject(&spListObject);
	// if (SUCCEEDED(hr) && spListObject)
	// {
	// 	VARIANT_BOOL bShowAutoFilter = VARIANT_FALSE;

		// hr = spListObject->get_ShowAutoFilter(&bShowAutoFilter);
		// if (bShowAutoFilter == VARIANT_FALSE)
		// 	hr = spListObject->put_ShowAutoFilter(VARIANT_TRUE);

		// ks_stdptr<etoldapi::AutoFilter> spApiAutoFilter;
		// hr = spListObject->get_AutoFilter(&spApiAutoFilter);
	// 	if (SUCCEEDED(hr) && spApiAutoFilter)
	// 	{
	// 		ks_stdptr<IAutoFilterInfo> spAutoFilterInfo = spApiAutoFilter;
	// 		ks_stdptr<IKAutoFilter> spAutoFilter;
	// 		hr = spAutoFilterInfo->GetCoreAutoFilter(&spAutoFilter);
	// 		if (SUCCEEDED(hr) && spAutoFilter)
	// 		{
	// 			hr = spAutoFilter->ShowAll();
	// 			if (SUCCEEDED(hr))
	// 			{
	// 				hr = GetFilterInfo(pField, strItem, spAutoFilter, jsonObj);
	// 				if (SUCCEEDED(hr))
	// 					hr = spAutoFilter->ShowAll();
	// 			}
	// 		}
	// 	}
	// 	if (bShowAutoFilter == VARIANT_FALSE)
	// 		hr = spListObject->put_ShowAutoFilter(VARIANT_FALSE);
	// }
	// else
	// {
	// 	ks_stdptr<IKAutoFilter> spAutoFilter = m_spSrcSheet->GetCoreAutoFilter();
	// 	if (spAutoFilter)
	// 	{
	// 		hr = spAutoFilter->ShowAll();
	// 		if (SUCCEEDED(hr))
	// 		{
	// 			hr = GetFilterInfo(pField, strItem, spAutoFilter, jsonObj);
	// 			if (SUCCEEDED(hr))
	// 				hr = spAutoFilter->ShowAll();
	// 		}
	// 	}
	// 	else
	// 	{
	// 		KComVariant varAutoFilter;
	// 		hr = m_spSrcRange->AutoFilter(var, var, oldapi::etAnd, var, var, &varAutoFilter);
	// 		if (SUCCEEDED(hr))
	// 			spAutoFilter = m_spSrcSheet->GetCoreAutoFilter();
	// 		if (spAutoFilter)
	// 		{
	// 			hr = GetFilterInfo(pField, strItem, spAutoFilter, jsonObj);
	// 			if (SUCCEEDED(hr))
	// 				hr = spAutoFilter->ShowAll();
	// 		}
	// 	}
	// }

	return S_OK;
}

KFieldMap* KAnalysisTable::GetFieldMap()
{
	return m_pFieldMap;
}

HRESULT KAnalysisTable::UpdateOnDeleteSheet(IKWorksheet* pDelSheet)
{
	if (!pDelSheet)
		return E_FAIL;

	std::vector<size_t> vecDelIdx;
	for (size_t i = 0; i < m_vecPivotTable.size(); ++i)
	{
		KAnalysisPivotTable* pAPT = m_vecPivotTable[i];
		if (!pAPT)
			continue;
		ks_stdptr<IKWorksheet> spSheet = pAPT->GetWorksheet();
		if (!spSheet || spSheet != pDelSheet)
			continue;
		delete pAPT;
		m_vecPivotTable[i] = NULL;
		vecDelIdx.push_back(i);
	}
	for (int j = (int)vecDelIdx.size() - 1; j >= 0; --j)
	{
		m_vecPivotTable.erase(m_vecPivotTable.begin() + vecDelIdx[j]);
	}
	return S_OK;
}

long KAnalysisTable::GetFieldRowCount() const
{
	return m_nFieldRowCount;
}

long KAnalysisTable::GetNotEmptyCellCount() const
{
	return m_nNotEmptyCellCount;
}

BOOL KAnalysisTable::GetIsRecommend()
{
	return m_isRecommend;
}

void KAnalysisTable::SetIsRecommend(BOOL isRecommend)
{
	m_isRecommend = isRecommend;
}

ISmartUnderstand* KAnalysisTable::GetRecommendUnderstand()
{
	return m_pRecommendUnderstand;
}

QString KAnalysisTable::GetLabel() const
{
	return m_strLabel;
}

// ----------------------------------------------------------------------
KFieldMapField::KFieldMapField(const KDataField* pField, double predValue, QString strType)
	: m_pField(pField), m_dPredValue(predValue), m_strType(strType)
{}

const KDataField* KFieldMapField::GetField() const
{
	return m_pField;
}

double KFieldMapField::GetPredValue() const
{
	return m_dPredValue;
}

QString KFieldMapField::GetTyle() const
{
	return m_strType;
}

HRESULT KFieldMapField::GetJsonObj(QJsonObject& jsonObj) const
{
	if (!m_pField)
		return E_FAIL;

	jsonObj.insert("name", m_pField->GetName());
	jsonObj.insert("predValue", m_dPredValue);
	jsonObj.insert("type", m_pField->GetTypeStr());
	return S_OK;
}

KFieldMapNumField::KFieldMapNumField(const KNumberField* pField, double predValue, QString strType)
	: KFieldMapField(pField, predValue, strType)
{}

HRESULT KFieldMapNumField::GetJsonObj(QJsonObject& jsonObj) const
{
	if (!m_pField)
		return E_FAIL;
	KFieldMapField::GetJsonObj(jsonObj);
	const KNumberField* pNumberField = (const KNumberField*)m_pField;
	jsonObj.insert("sum", pNumberField->GetSum());
	jsonObj.insert("average", pNumberField->GetAverage());
	jsonObj.insert("max", pNumberField->GetMax());
	jsonObj.insert("min", pNumberField->GetMin());
	if (pNumberField->IsPercent())
		jsonObj.insert("per", true);
	return S_OK;
}

KFieldMapStrField::KFieldMapStrField(const KStringField* pField, double predValue, QString strType)
	: KFieldMapField(pField, predValue, strType)
{}

void KFieldMapStrField::AddRelationField(const KStringField* pField, double dPredValue)
{
	m_relationList.push_back(KFieldMapListItem(pField, dPredValue));
}

void KFieldMapStrField::AddIncludeField(const KStringField* pField, double dPredValue, int nDirection)
{
	m_includeList.push_back(KFieldMapListItem(pField, dPredValue, nDirection));
}

HRESULT KFieldMapStrField::GetJsonObj(QJsonObject& jsonObj) const
{
	if (!m_pField)
		return E_FAIL;
	KFieldMapField::GetJsonObj(jsonObj);
	const KStringField* pStrField = (const KStringField*)m_pField;
	QJsonArray jsonItemList;
	const KDataFieldItemList& itemList = pStrField->GetDataFieldItemList();
	for (auto it = itemList.cbegin(); it != itemList.cend(); ++it)
	{
		QJsonObject jsonItem;
		jsonItem.insert("name", (*it)->GetName());
		jsonItem.insert("count", (qint64)(*it)->GetRecordCount());
		jsonItemList.append(jsonItem);
	}
	jsonObj.insert("itemList", jsonItemList);

	QJsonArray jsonIncludeList;
	for (size_t i = 0; i < m_includeList.size(); ++i)
	{
		if (!m_includeList[i].pField)
			continue;
		QJsonObject jsonInclude;
		jsonInclude.insert("name", m_includeList[i].pField->GetName());
		jsonInclude.insert("predValue", m_includeList[i].dPredValue);
		jsonInclude.insert("direction", m_includeList[i].nDirection);
		jsonIncludeList.append(jsonInclude);
	}
	jsonObj.insert("includeList", jsonIncludeList);

	QJsonArray jsonRelationList;
	for (size_t i = 0; i < m_relationList.size(); ++i)
	{
		if (!m_relationList[i].pField)
			continue;
		QJsonObject jsonRelation;
		jsonRelation.insert("name", m_relationList[i].pField->GetName());
		jsonRelation.insert("predValue", m_relationList[i].dPredValue);
		jsonRelationList.append(jsonRelation);
	}
	jsonObj.insert("relationList", jsonRelationList);

	return S_OK;
}

KFieldMapDataFieldList::~KFieldMapDataFieldList()
{
	for (auto it = this->begin(); it != this->end(); ++it)
	{
		if (*it)
		{
			delete* it;
			*it = NULL;
		}
	}
	std::vector<KFieldMapField*>::clear();
}

KFieldMap::KFieldMap(QJsonObject& jsonObj, const KDataFieldList& allFieldList)
{
	Init(jsonObj, allFieldList);
}

void InitFieldListNum(QJsonArray& jsonArray, const KDataFieldList& allFieldList, std::map<QString, KFieldMapDataFieldList>& mapField, QString strType)
{
	mapField[strType];
	if (jsonArray.isEmpty())
		return;
	for (int i = 0; i < jsonArray.size(); ++i)
	{
		QJsonObject jsonObj = jsonArray[i].toObject();
		if (!jsonObj.contains("indexName") || !jsonObj.contains("predValue"))
			continue;
		QString strIndex = jsonObj["indexName"].toString();
		const KDataField* pField = allFieldList.FindByIndex(strIndex);
		if (!pField || !pField->IsNumberField())
			continue;
		const KNumberField* pNumField = (const KNumberField*)pField;
		mapField[strType].push_back(new KFieldMapNumField(pNumField, jsonObj["predValue"].toDouble(), strType));
	}
}

void InitFieldListStr(QJsonArray& jsonArray, const KDataFieldList& allFieldList, std::map<QString, KFieldMapDataFieldList>& mapField, QString strType)
{
	mapField[strType];
	if (jsonArray.isEmpty())
		return;
	for (int i = 0; i < jsonArray.size(); ++i)
	{
		QJsonObject jsonObj = jsonArray[i].toObject();
		if (!jsonObj.contains("indexName") || !jsonObj.contains("predValue"))
			continue;
		QString strIndex = jsonObj["indexName"].toString();
		const KDataField* pField = allFieldList.FindByIndex(strIndex);
		if (!pField || pField->IsNumberField())
			continue;
		const KStringField* pStrField = (const KStringField*)pField;
		KFieldMapStrField* pFieldMapField = new KFieldMapStrField(pStrField, jsonObj["predValue"].toDouble(), strType);
		if (!pFieldMapField)
			continue;
		mapField[strType].push_back((KFieldMapField*)pFieldMapField);

		if (jsonObj.contains("includeList") && jsonObj["includeList"].isArray() && !jsonObj["includeList"].toArray().isEmpty())
		{
			QJsonArray jsonList = jsonObj["includeList"].toArray();
			for (int j = 0; j < jsonList.size(); ++j)
			{
				QJsonObject jsonField = jsonList[j].toObject();
				if (!jsonField.contains("indexName") || !jsonField.contains("relationPredValue"))
					continue;
				QString strIdx = jsonField["indexName"].toString();
				double dPred = jsonField["relationPredValue"].toDouble();
				int nDirection = jsonField["direction"].toInt();
				const KDataField* pField2 = allFieldList.FindByIndex(strIdx);
				if (!pField2 || pField2->IsNumberField())
					continue;
				pFieldMapField->AddIncludeField((const KStringField*)pField2, dPred, nDirection);
			}
		}
		if (jsonObj.contains("relationList") && jsonObj["relationList"].isArray() && !jsonObj["relationList"].toArray().isEmpty())
		{
			QJsonArray jsonList = jsonObj["relationList"].toArray();
			for (int j = 0; j < jsonList.size(); ++j)
			{
				QJsonObject jsonField = jsonList[j].toObject();
				if (!jsonField.contains("indexName") || !jsonField.contains("relationPredValue"))
					continue;
				QString strIdx = jsonField["indexName"].toString();
				double dPred = jsonField["relationPredValue"].toDouble();
				const KDataField* pField2 = allFieldList.FindByIndex(strIdx);
				if (!pField2 || pField2->IsNumberField())
					continue;
				pFieldMapField->AddRelationField((const KStringField*)pField2, dPred);
			}
		}
	}
}

void KFieldMap::Init(QJsonObject& jsonObj, const KDataFieldList& allFieldList)
{
	m_strView = krt::fromUtf16(__X("circular"));
	if (jsonObj.contains("view"))
		m_strView = jsonObj["view"].toString();
	if (!jsonObj.contains("dateFieldList") || !jsonObj["dateFieldList"].isArray()
		|| !jsonObj.contains("nrFieldList") || !jsonObj["nrFieldList"].isArray()
		|| !jsonObj.contains("nsFieldList") || !jsonObj["nsFieldList"].isArray()
		|| !jsonObj.contains("otherFieldList") || !jsonObj["otherFieldList"].isArray()
		|| !jsonObj.contains("notRecommendFieldList") || !jsonObj["notRecommendFieldList"].isArray()
		|| !jsonObj.contains("numberFieldList") || !jsonObj["numberFieldList"].isArray())
		return;
    // todo : fix compile
	// InitFieldListStr(jsonObj["dateFieldList"].toArray(), allFieldList, m_mapField, "date");
	// InitFieldListStr(jsonObj["nrFieldList"].toArray(), allFieldList, m_mapField, "nr");
	// InitFieldListStr(jsonObj["nsFieldList"].toArray(), allFieldList, m_mapField, "ns");
	// InitFieldListStr(jsonObj["otherFieldList"].toArray(), allFieldList, m_mapField, "other");
	// InitFieldListStr(jsonObj["notRecommendFieldList"].toArray(), allFieldList, m_mapField, "notRecommend");
	// InitFieldListNum(jsonObj["numberFieldList"].toArray(), allFieldList, m_mapField, "number");
}

void GetFieldListJsonObj(QJsonObject& jsonObj, const KFieldMapDataFieldList& fieldList)
{
	QJsonArray jsonArray;
	for (auto it = fieldList.cbegin(); it != fieldList.cend(); ++it)
	{
		const KFieldMapField* pFieldMapField = *it;
		if (!pFieldMapField)
			continue;
		const KDataField* pField = pFieldMapField->GetField();
		if (!pField)
			continue;
		QJsonObject jsonField;
		jsonField.insert("name", pField->GetName());
		jsonField.insert("predValue", pFieldMapField->GetPredValue());
		jsonArray.append(jsonField);
	}
	jsonObj.insert("fieldList", jsonArray);
}

HRESULT KFieldMap::GetFieldMapJsonObj(QJsonObject& jsonObj) const
{
	QString strView = krt::fromUtf16(__X("circular"));
	jsonObj.insert("view", strView);
	QJsonArray jsonMap;
	for (auto it = m_mapField.cbegin(); it != m_mapField.cend(); ++it)
	{
		const KFieldMapDataFieldList& fieldList = it->second;
		QJsonObject jsonFieldList;
		jsonFieldList.insert("type",it->first);
		GetFieldListJsonObj(jsonFieldList, fieldList);
		jsonMap.append(jsonFieldList);
	}
	jsonObj.insert("fieldMap", jsonMap);
	return S_OK;
}

const KFieldMapField* FindFieldList(QString strField, const KFieldMapDataFieldList& fieldList)
{
	for (size_t i = 0; i < fieldList.size(); ++i)
	{
		const KDataField* pField = fieldList[i]->GetField();
		if (pField && pField->GetName() == strField)
			return fieldList[i];
	}
	return NULL;
}

const KFieldMapField* KFieldMap::FindField(QString strField, QString& strType) const
{
	const KFieldMapField* pField = NULL;
	for (auto it = m_mapField.cbegin(); it != m_mapField.cend(); ++it)
	{
		pField = FindFieldList(strField, it->second);
		if (!pField)
			continue;
		strType = it->first;
		return pField;
	}
	return NULL;
}

HRESULT KFieldMap::GetFieldMapInfoJsonObj(QString strField, QJsonObject& jsonObj) const
{
	QString strType;
	const KFieldMapField* pField = FindField(strField, strType);
	if (pField)
		return pField->GetJsonObj(jsonObj);
	return S_FALSE;
}

QString KFieldMap::GetFieldType(QString strField) const
{
	QString strType;
	const KFieldMapField* pField = FindField(strField, strType);
	if (pField && !strType.isEmpty())
		return strType;
	return QString(); // todo , origin code return S_FALSE :(
}

// ----------------------------------------------------------------------

KChartXValues::KChartXValues(QString strName) : m_strName(strName)
{

}

QString KChartXValues::GetName() const
{
	return m_strName;
}

const QStringList& KChartXValues::GetValues() const
{
	return m_qslValues;
}

void KChartXValues::AddValues(QString strValue)
{
	m_qslValues.push_back(strValue);
}

HRESULT KChartXValues::ExportJson(QJsonObject& jsonObj) const
{
	jsonObj["name"] = m_strName;
	QJsonArray jsonValues;
	for (auto it = m_qslValues.cbegin(); it != m_qslValues.cend(); ++it)
		jsonValues.append(*it);
	jsonObj["values"] = jsonValues;
	return S_OK;
}

KChartYValues::KChartYValues(QString strName) : m_strName(strName)
{}

QString KChartYValues::GetName() const
{
	return m_strName;
}

const std::vector<double>& KChartYValues::GetValues() const
{
	return m_vecValues;
}

void KChartYValues::AddValues(double dValue)
{
	m_vecValues.push_back(dValue);
}

HRESULT KChartYValues::ExportJson(QJsonObject& jsonObj) const
{
	jsonObj["name"] = m_strName;
	QJsonArray jsonValues;
	for (auto it = m_vecValues.cbegin(); it != m_vecValues.cend(); ++it)
		jsonValues.append(*it);
	jsonObj["values"] = jsonValues;
	return S_OK;
}

KUnderstandChart::KUnderstandChart(IKWorksheet* pSheet, QJsonObject& jsonObj)
	: m_spSheet(pSheet)
{
	Init(jsonObj);
}

KUnderstandChartTypeEnum TransformChartTypeEx(QString strType)
{
	if (strType == "bar")
		return UnderstandChartTypeBar;
	else if (strType == "rowBar")
		return UnderstandChartTypeRowBar;
	else if (strType == "line")
		return UnderstandChartTypeLine;
	else if (strType == "pie")
		return UnderstandChartTypePie;
	else if (strType == "radar")
		return UnderstandChartTypeRadar;
	else if (strType == "doughnut")
		return UnderstandChartTypeDoughnut;
	else if (strType == "point")
		return UnderstandChartTypePoint;
	return UnderstandChartWebExtension;
}

KUnderstandChartTypeEnum TransformChartType(QString strType)
{
	KUnderstandChartTypeEnum type = TransformChartTypeEx(strType);
	return UnderstandChartWebExtension == type ? UnderstandChartTypeBar : type;
}

void KUnderstandChart::Init(QJsonObject& jsonObj)
{
	if (jsonObj.contains("chartType"))
	{
		m_chartType = TransformChartType(jsonObj["chartType"].toString());
		m_chartTypeName = jsonObj["chartType"].toString();
		m_curChartType = m_chartTypeName;
	}
	if (jsonObj.contains("format"))
		m_strFormat = jsonObj["format"].toString();
	if (jsonObj.contains("xValuesList") && jsonObj["xValuesList"].isArray())
	{
		QJsonArray jsonValuesList = jsonObj["xValuesList"].toArray();
		for (int i = 0; i < jsonValuesList.size(); ++i)
		{
			QJsonObject jsonValues = jsonValuesList[i].toObject();
			if (!jsonValues.contains("name") || !jsonValues.contains("values") || !jsonValues["values"].isArray())
				continue;
			QString strName = jsonValues["name"].toString();
			m_XValuesList.push_back(KChartXValues(strName));
			QJsonArray jsonValueList = jsonValues["values"].toArray();
			for (int j = 0; j < jsonValueList.size(); ++j)
				m_XValuesList.back().AddValues(jsonValueList[j].toString());
		}
	}
	if (jsonObj.contains("yValuesList") && jsonObj["yValuesList"].isArray())
	{
		QJsonArray jsonValuesList = jsonObj["yValuesList"].toArray();
		for (int i = 0; i < jsonValuesList.size(); ++i)
		{
			QJsonObject jsonValues = jsonValuesList[i].toObject();
			if (!jsonValues.contains("name") || !jsonValues.contains("values") || !jsonValues["values"].isArray())
				continue;
			QString strName = jsonValues["name"].toString();
			m_YValuesList.push_back(KChartYValues(strName));
			QJsonArray jsonValueList = jsonValues["values"].toArray();
			for (int j = 0; j < jsonValueList.size(); ++j)
				m_YValuesList.back().AddValues(jsonValueList[j].toDouble());
		}
	}
}

QString TransformYValues(const std::vector<double>& vecValues, bool needReverse)
{
	if (vecValues.empty())
		return QString();
	QString strText;
	if (needReverse)
	{
		for (auto it = vecValues.rbegin(); it != vecValues.rend(); ++it)
		{
			if (strText.isEmpty())
				strText = "={";
			else
				strText += ",";
			strText += QString::number(*it);
		}
	}
	else
	{
		for (auto it = vecValues.begin(); it != vecValues.end(); ++it)
		{
			if (strText.isEmpty())
				strText = "={";
			else
				strText += ",";
			strText += QString::number(*it);
		}
	}
	strText += "}";
	return strText;
}

QString TransformXValues(const QStringList& qslValues, bool bReverse)
{
	if (qslValues.isEmpty())
		return QString();
	QString strText;
	if (bReverse)
	{
		for (auto it = qslValues.rbegin(); it != qslValues.rend(); ++it)
		{
			if (strText.isEmpty())
				strText = "={";
			else
				strText += ",";
			strText += "\"" + *it + "\"";
		}
	}
	else
	{
		for (auto it = qslValues.begin(); it != qslValues.end(); ++it)
		{
			if (strText.isEmpty())
				strText = "={";
			else
				strText += ",";
			strText += "\"" + *it + "\"";
		}
	}
	strText += "}";
	return strText;
}

namespace
{
	struct KUChartFontStyle
	{
	public:
		KUChartFontStyle()
			: fSize(0), bBold(false), color(0xFF595959)
		{}
		KUChartFontStyle(QString name, QString fareast, float size, bool bb, KsoRGBType c)
			: strName(name), strNameFarEast(fareast), fSize(size), bBold(bb), color(c)
		{}
		QString strName;
		QString strNameFarEast;
		float fSize;
		bool bBold;
		KsoRGBType color;
	};
	struct KUChartDataLabelsStyle
	{
	public:
		KUChartDataLabelsStyle() 
			: nElement(ksoElementDataLabelOutSideEnd), bHasLeaderLines(true), bShowValue(false),
			bShowPercentage(true), bShowCategoryName(true), bShowLegendKey(true),
			strSeparator(" "), font(KUChartFontStyle(staticStrNormalFontName, staticStrNormalFontName, 9, false, 0xFF595959))
		{}
		KsoChartElementType nElement;	// 数据标签位置
		bool bHasLeaderLines;
		bool bShowValue;
		bool bShowPercentage;
		bool bShowCategoryName;
		bool bShowLegendKey;
		QString strSeparator;
		KUChartFontStyle font;
	};
	struct KUChartGroupStyle
	{
	public:
		KUChartGroupStyle() : nGapWidth(200), nOverlap(0), nDoughnutHoleSize(70)
		{}
		long nGapWidth;	// 分类间距
		long nOverlap;	// 系列重叠
		long nDoughnutHoleSize;	// 圆环图内径大小
	};
	struct KUChartStyle
	{
	public:
		KUChartStyle()
			: titleFont(KUChartFontStyle(staticStrNormalFontName, staticStrNormalFontName, 12, true, 0xFF595959)),
			categoryAxisFont(KUChartFontStyle(staticStrNormalFontName, staticStrNormalFontName, 9, false, 0xFF7F7F7F)),
			valueAxisFont(KUChartFontStyle(staticStrNormalFontName, staticStrNormalFontName, 8, false, 0xFF7F7F7F)),
			legendFont(KUChartFontStyle(staticStrNormalFontName, staticStrNormalFontName, 8, false, 0xFF7F7F7F)),
			radarAxisFont(KUChartFontStyle(staticStrNormalFontName, staticStrNormalFontName, 8, false, 0xFF595959))
		{
			vecSerColor.resize(10, 0xFF595959);
			vecSerColor[0] = 0xFFDB7E38;
			vecSerColor[1] = 0xFF7DBB1F;
			vecSerColor[2] = 0xFF59CEF8;
			vecSerColor[3] = 0xFF6A6CEB;
			vecSerColor[4] = 0xFFC882AC;
			vecSerColor[5] = 0xFFC1B61A;
			vecSerColor[6] = 0xFFD0807E;
			vecSerColor[7] = 0xFF9075D2;
			vecSerColor[8] = 0xFF60A7F3;
			vecSerColor[9] = 0xFF6FC376;
		}
		KUChartFontStyle titleFont;			// 图表标题
		KUChartFontStyle categoryAxisFont;	// x轴字体
		KUChartFontStyle valueAxisFont;		// y轴字体
		KUChartFontStyle radarAxisFont;		// 雷达图外圈文字
		KUChartFontStyle legendFont;		// 图例字体
		KUChartGroupStyle groups;			// 系列选项
		KUChartDataLabelsStyle dataLabels;	// 数据标签
		std::vector<KsoRGBType> vecSerColor;// 系列颜色或单系列的数据点颜色
	};
	struct KUChartStyleMap : public std::map<KUnderstandChartTypeEnum, KUChartStyle>
	{
	public:
		KUChartStyleMap()
		{
			KUChartStyle chartStyle;
			(*this)[UnderstandChartTypeBar] = chartStyle;
			(*this)[UnderstandChartTypeLine] = chartStyle;
			(*this)[UnderstandChartTypePie] = chartStyle;
			chartStyle.categoryAxisFont.fSize = 8;
			chartStyle.valueAxisFont.fSize = 9;
			chartStyle.groups.nGapWidth = 120;
			(*this)[UnderstandChartTypeRowBar] = chartStyle;

			chartStyle.categoryAxisFont.fSize = 8;
			chartStyle.valueAxisFont.fSize = 8;
			(*this)[UnderstandChartTypePoint] = chartStyle;
			chartStyle.dataLabels.strSeparator = ",";
			chartStyle.dataLabels.bShowCategoryName = false;
			chartStyle.dataLabels.bShowLegendKey = false;
			chartStyle.dataLabels.font.color = 0xFFFFFFFF;
			(*this)[UnderstandChartTypeDoughnut] = chartStyle;
			chartStyle.valueAxisFont.color = 0xFFACACAC;
			(*this)[UnderstandChartTypeRadar] = chartStyle;
		}
	};
}

HRESULT SetColor(oldapi::KsoFillFormat* pFill, KsoRGBType rgb)
{
	if (!pFill)
		return E_FAIL;
	ks_stdptr<KsoColorFormat> spColor;
	HRESULT hr = pFill->get__ForeColor(&spColor);
	if (FAILED(hr) || !spColor)
		return E_FAIL;
	return spColor->put_RGB(rgb);
}

HRESULT SetColor(oldapi::KsoLineFormat* pLine, KsoRGBType rgb)
{
	if (!pLine)
		return E_FAIL;
	ks_stdptr<KsoColorFormat> spColor;
	HRESULT hr = pLine->get__ForeColor(&spColor);
	if (FAILED(hr) || !spColor)
		return E_FAIL;
	return spColor->put_RGB(rgb);
}

HRESULT SetTextFont(oldapi::KsoChartFormat* pFormat, const KUChartFontStyle& style)
{
	if (!pFormat)
		return E_FAIL;
	ks_stdptr<oldapi::KsoTextFrame2> spTextFrame2;
	HRESULT hr = pFormat->get_TextFrame2(&spTextFrame2);
	if (FAILED(hr) || !spTextFrame2)
		return E_FAIL;
	ks_stdptr<oldapi::KsoTextRange2> spTextRange;
	hr = spTextFrame2->get_TextRange(&spTextRange);
	if (FAILED(hr) || !spTextRange)
		return E_FAIL;
	ks_stdptr<oldapi::KsoFont2> spFont;
	hr = spTextRange->get_Font(&spFont);
	if (FAILED(hr) || !spFont)
		return E_FAIL;
	if (!style.strName.isEmpty())
	{
		ks_bstr bstrName(krt::utf16(style.strName));
		hr = spFont->put_Name(bstrName);
	}
	if (!style.strNameFarEast.isEmpty())
	{
		ks_bstr bstrName(krt::utf16(style.strNameFarEast));
		hr = spFont->put_NameFarEast(bstrName);
	}
	if (style.bBold)
		hr = spFont->put_Bold(ksoTrue);
	if (style.fSize > 0)
		hr = spFont->put_Size(style.fSize);
	ks_stdptr<oldapi::KsoFillFormat> spFill;
	hr = spFont->get_Fill(&spFill);
	if (FAILED(hr) || !spFill)
		return E_FAIL;
	return SetColor(spFill, style.color);
}

HRESULT SetChartTitle(oldapi::KsoChart* pChart, const KUChartFontStyle& titleFont)
{
	ks_stdptr<KsoChartTitle> spChartTitle;
	HRESULT hr = pChart->get_ChartTitle(&spChartTitle);
	if (FAILED(hr) || !spChartTitle)
		return E_FAIL;
	ks_stdptr<oldapi::KsoChartFormat> spFormat;
	hr = spChartTitle->get_Format(&spFormat);
	if (FAILED(hr) || !spFormat)
		return E_FAIL;
	return SetTextFont(spFormat, titleFont);
}

HRESULT SetFormatColor(oldapi::KsoChartFormat* pFormat, KsoRGBType rgb)
{
	if (!pFormat)
		return E_FAIL;
	ks_stdptr<oldapi::KsoFillFormat> spFill;
	HRESULT hr = pFormat->get_Fill(&spFill);
	if (FAILED(hr) || !spFill)
		return E_FAIL;
	return SetColor(spFill, rgb);
}

HRESULT SetFormatColorLine(oldapi::KsoChartFormat* pFormat, KsoRGBType rgb)
{
	if (!pFormat)
		return E_FAIL;
	ks_stdptr<oldapi::KsoLineFormat> spLine;
	HRESULT hr = pFormat->get_Line(&spLine);
	if (FAILED(hr) || !spLine)
		return E_FAIL;
	return SetColor(spLine, rgb);
}

HRESULT SetFormatLineUnvisible(oldapi::KsoChartFormat* pFormat)
{
	if (!pFormat)
		return E_FAIL;
	ks_stdptr<oldapi::KsoLineFormat> spLine;
	HRESULT hr = pFormat->get_Line(&spLine);
	if (FAILED(hr) || !spLine)
		return E_FAIL;
	return spLine->put_Visible(oldapi::ksoFalse);
}

HRESULT SetDataPoint(oldapi::KsoSeries* pSeries, const std::vector<KsoRGBType>& vecSerColor)
{
	if (!pSeries)
		return E_FAIL;
	ks_stdptr<oldapi::KsoDataPoints> spDataPoints;
	KComVariant var;
	HRESULT hr = pSeries->DataPoints(var, (IKCoreObject**)(&spDataPoints));
	if (FAILED(hr) || !spDataPoints)
		return E_FAIL;
	long nPointCnt = 0;
	hr = spDataPoints->get_Count(&nPointCnt);
	if (nPointCnt <= 0)
		return E_FAIL;
	for (long i = 0; i < nPointCnt; ++i)
	{
		ks_stdptr<oldapi::KsoDataPoint> spPoint;
		hr = spDataPoints->Item(i + 1, &spPoint);
		if (FAILED(hr) || !spPoint)
			continue;
		ks_stdptr<oldapi::KsoChartFormat> spFormat;
		hr = spPoint->get_Format(&spFormat);
		if (FAILED(hr) || !spFormat)
			continue;
		long idx = i % vecSerColor.size();
		SetFormatColor(spFormat, vecSerColor[idx]);
	}
	return S_OK;
}

HRESULT SetDataLabels(oldapi::KsoChart* pChart, oldapi::KsoSeries* pSeries, const KUChartDataLabelsStyle& dataLabels)
{
	if (!pChart || !pSeries)
		return E_FAIL;
	HRESULT hr = pChart->SetElement(dataLabels.nElement);
	hr = pSeries->put_HasDataLabels(VARIANT_TRUE);
	KComVariant var;
	ks_stdptr<oldapi::KsoDataLabels> spDataLabels;
	hr = pSeries->DataLabels(var, (IKCoreObject**)(&spDataLabels));
	if (FAILED(hr) || !spDataLabels)
		return E_FAIL;
	hr = spDataLabels->put_ShowValue(dataLabels.bShowValue ? VARIANT_TRUE : VARIANT_FALSE);
	hr = spDataLabels->put_ShowPercentage(dataLabels.bShowPercentage ? VARIANT_TRUE : VARIANT_FALSE);
	hr = spDataLabels->put_ShowCategoryName(dataLabels.bShowCategoryName ? VARIANT_TRUE : VARIANT_FALSE);
	hr = spDataLabels->put_ShowLegendKey(dataLabels.bShowLegendKey ? VARIANT_TRUE : VARIANT_FALSE);
	KComVariant varSep(krt::utf16(dataLabels.strSeparator));
	hr = spDataLabels->put_Separator(varSep);
	ks_stdptr<oldapi::KsoChartFormat> spFormat;
	hr = spDataLabels->get_Format(&spFormat);
	if (SUCCEEDED(hr) && spFormat)
		hr = SetTextFont(spFormat, dataLabels.font);
	return hr;
}

HRESULT SetChartLegend(oldapi::KsoChart* pChart, bool hasLegend, const KUChartFontStyle* pLegendFont = NULL)
{
	if (!pChart)
		return E_FAIL;
	HRESULT hr = pChart->put_HasLegend(hasLegend ? VARIANT_TRUE : VARIANT_FALSE);
	if (!hasLegend || !pLegendFont)
		return hr;
	ks_stdptr<oldapi::KsoLegend> spLegend;
	hr = pChart->get_Legend(&spLegend);
	if (FAILED(hr) || !spLegend)
		return E_FAIL;
	ks_stdptr<oldapi::KsoChartFormat> spFormat;
	hr = spLegend->get_Format(&spFormat);
	if (SUCCEEDED(hr) && spFormat)
		hr = SetTextFont(spFormat, *pLegendFont);
	return hr;
}

HRESULT SetPieChartStyle(oldapi::KsoChart* pChart, const KUChartStyle& chartStyle)
{
	if (!pChart)
		return E_FAIL;
	KComVariant var;
	ks_stdptr<etoldapi::FullSeriesCollection> spFullSeriesCollection;
	HRESULT hr = pChart->FullSeriesCollection(var, (IKCoreObject**)(&spFullSeriesCollection));
	if (FAILED(hr) || !spFullSeriesCollection)
		return E_FAIL;
	long nSerCnt = 0;
	hr = spFullSeriesCollection->get_Count(&nSerCnt);
	if (nSerCnt <= 0)
		return E_FAIL;
	KComVariant varIdx(1, VT_I4);
	ks_stdptr<oldapi::KsoSeries> spSeries;
	hr = spFullSeriesCollection->Item(varIdx, &spSeries);
	if (FAILED(hr) || !spSeries)
		return E_FAIL;
	// 设置数据点颜色
	hr = SetDataPoint(spSeries, chartStyle.vecSerColor);
	// 设置数据标签
	hr = SetDataLabels(pChart, spSeries, chartStyle.dataLabels);
	// 设置图例不显示
	hr = SetChartLegend(pChart, false);
	return hr;
}

HRESULT SetSeries(oldapi::KsoChart* pChart, const std::vector<KsoRGBType>& vecSerColor, bool bLine, bool bLineUnvisible, long& nSeriesCnt)
{
	if (!pChart)
		return E_FAIL;
	KComVariant var;
	ks_stdptr<etoldapi::FullSeriesCollection> spFullSeriesCollection;
	HRESULT hr = pChart->FullSeriesCollection(var, (IKCoreObject**)(&spFullSeriesCollection));
	if (FAILED(hr) || !spFullSeriesCollection)
		return E_FAIL;
	hr = spFullSeriesCollection->get_Count(&nSeriesCnt);
	if (nSeriesCnt <= 0)
		return E_FAIL;
	for (long i = 0; i < nSeriesCnt; ++i)
	{
		KComVariant varIdx(i + 1, VT_I4);
		ks_stdptr<oldapi::KsoSeries> spSeries;
		hr = spFullSeriesCollection->Item(varIdx, &spSeries);
		if (FAILED(hr) || !spSeries)
			return E_FAIL;
		ks_stdptr<oldapi::KsoChartFormat> spFormat;
		hr = spSeries->get_Format(&spFormat);
		if (FAILED(hr) || !spFormat)
			return E_FAIL;
		long idx = i % vecSerColor.size();
		hr = SetFormatColor(spFormat, vecSerColor[idx]);
		if (bLine)
			hr = SetFormatColorLine(spFormat, vecSerColor[idx]);
		if (bLineUnvisible)
			hr = SetFormatLineUnvisible(spFormat);
	}
	return hr;
}

HRESULT SetAxes(oldapi::KsoAxis* pAxis, const KUChartFontStyle& font)
{
	if (!pAxis)
		return E_FAIL;
	ks_stdptr<oldapi::KsoChartFormat> spFormat;
	HRESULT hr = pAxis->get_Format(&spFormat);
	if (FAILED(hr) || !spFormat)
		return E_FAIL;
	return SetTextFont(spFormat, font);
}

HRESULT SetChartGroups(oldapi::KsoChart* pChart, const KUChartGroupStyle& groups, bool bSetOverlap)
{
	KComVariant var;
	ks_stdptr<oldapi::KsoChartGroups> spGroups;
	HRESULT hr = pChart->get_ChartGroups(var, (IKCoreObject**)(&spGroups));
	if (FAILED(hr) || !spGroups)
		return E_FAIL;
	long nGroupCnt = 0;
	hr = spGroups->get_Count(&nGroupCnt);
	if (nGroupCnt <= 0)
		return E_FAIL;
	for (long i = 1; i <= nGroupCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<oldapi::KsoChartGroup> spGroup;
		hr = spGroups->Item(varIdx, &spGroup);
		if (FAILED(hr) || !spGroup)
			continue;
		hr = spGroup->put_GapWidth(groups.nGapWidth);
		if (bSetOverlap)
			hr = spGroup->put_Overlap(groups.nOverlap);
	}
	return hr;
}

HRESULT SetBarChartStyle(oldapi::KsoChart* pChart, const KUChartStyle& chartStyle)
{
	if (!pChart)
		return E_FAIL;
	// 设置系列颜色
	long nSeriesCnt = 0;
	HRESULT hr = SetSeries(pChart, chartStyle.vecSerColor, false, false, nSeriesCnt);
	// 设置X轴标签
	KComVariant varCat(1, VT_I4);	// xlCategory
	ks_stdptr<oldapi::KsoAxis> spCategoryAxis;
	hr = pChart->Axes(varCat, oldapi::ksoPrimary, (IKCoreObject**)(&spCategoryAxis));
	if (SUCCEEDED(hr) && spCategoryAxis)
		hr = SetAxes(spCategoryAxis, chartStyle.categoryAxisFont);
	// 设置Y轴标签
	KComVariant varVal(2, VT_I4);	// xlValue
	ks_stdptr<oldapi::KsoAxis> spValueAxis;
	hr = pChart->Axes(varVal, oldapi::ksoPrimary, (IKCoreObject**)(&spValueAxis));
	if (SUCCEEDED(hr) && spValueAxis)
		hr = SetAxes(spValueAxis, chartStyle.valueAxisFont);
	// 设置分类间距、系列重叠
	hr = SetChartGroups(pChart, chartStyle.groups, nSeriesCnt > 1);
	// 设置图例显示
	return SetChartLegend(pChart, nSeriesCnt > 1, &(chartStyle.legendFont));
}

HRESULT SetRowBarChartStyle(oldapi::KsoChart* pChart, const KUChartStyle& chartStyle)
{
	if (!pChart)
		return E_FAIL;
	// 设置系列颜色
	long nSeriesCnt = 0;
	HRESULT hr = SetSeries(pChart, chartStyle.vecSerColor, false, false, nSeriesCnt);
	// 设置X轴标签
	KComVariant varCat(1, VT_I4);	// xlCategory
	ks_stdptr<oldapi::KsoAxis> spCategoryAxis;
	hr = pChart->Axes(varCat, oldapi::ksoPrimary, (IKCoreObject**)(&spCategoryAxis));
	if (SUCCEEDED(hr) && spCategoryAxis)
	{
		hr = SetAxes(spCategoryAxis, chartStyle.categoryAxisFont);
		spCategoryAxis->put_TickLabelPosition(oldapi::ksoTickLabelPositionLow);
	}
	// 设置Y轴标签
	KComVariant varVal(2, VT_I4);	// xlValue
	ks_stdptr<oldapi::KsoAxis> spValueAxis;
	hr = pChart->Axes(varVal, oldapi::ksoPrimary, (IKCoreObject**)(&spValueAxis));
	if (SUCCEEDED(hr) && spValueAxis)
		hr = SetAxes(spValueAxis, chartStyle.valueAxisFont);
	// 设置分类间距、系列重叠
	hr = SetChartGroups(pChart, chartStyle.groups, nSeriesCnt > 1);
	// 设置图例显示
	return SetChartLegend(pChart, nSeriesCnt > 1, &(chartStyle.legendFont));
}

HRESULT SetLineChartStyle(oldapi::KsoChart* pChart, const KUChartStyle& chartStyle)
{
	if (!pChart)
		return E_FAIL;
	// 设置系列颜色
	long nSeriesCnt = 0;
	HRESULT hr = SetSeries(pChart, chartStyle.vecSerColor, true, false, nSeriesCnt);
	// 设置X轴标签
	KComVariant varCat(1, VT_I4);	// xlCategory
	ks_stdptr<oldapi::KsoAxis> spCategoryAxis;
	hr = pChart->Axes(varCat, oldapi::ksoPrimary, (IKCoreObject**)(&spCategoryAxis));
	if (SUCCEEDED(hr) && spCategoryAxis)
		hr = SetAxes(spCategoryAxis, chartStyle.categoryAxisFont);
	// 设置Y轴标签
	KComVariant varVal(2, VT_I4);	// xlValue
	ks_stdptr<oldapi::KsoAxis> spValueAxis;
	hr = pChart->Axes(varVal, oldapi::ksoPrimary, (IKCoreObject**)(&spValueAxis));
	if (SUCCEEDED(hr) && spValueAxis)
		hr = SetAxes(spValueAxis, chartStyle.valueAxisFont);
	// 设置图例显示
	return SetChartLegend(pChart, nSeriesCnt > 1, &(chartStyle.legendFont));
}

HRESULT SetRadarAxisLabels(oldapi::KsoChart* pChart, const KUChartFontStyle& font)
{
	KComVariant varIdx(1, VT_I4);
	ks_stdptr<oldapi::KsoChartGroup> spGroup;
	HRESULT hr = pChart->get_ChartGroups(varIdx, (IKCoreObject**)(&spGroup));
	if (FAILED(hr) || !spGroup)
		return E_FAIL;
	ks_stdptr<oldapi::KsoTickLabels> spRadarAxisLabels;
	hr = spGroup->get_RadarAxisLabels(&spRadarAxisLabels);
	if (FAILED(hr) || !spRadarAxisLabels)
		return E_FAIL;
	ks_stdptr<oldapi::KsoChartFormat> spFormat;
	hr = spRadarAxisLabels->get_Format(&spFormat);
	if (FAILED(hr) || !spFormat)
		return E_FAIL;
	return SetTextFont(spFormat, font);
}

HRESULT SetRadarChartStyle(oldapi::KsoChart* pChart, const KUChartStyle& chartStyle)
{
	if (!pChart)
		return E_FAIL;
	// 设置系列颜色
	long nSeriesCnt = 0;
	HRESULT hr = SetSeries(pChart, chartStyle.vecSerColor, true, false, nSeriesCnt);
	// 设置Y轴标签
	KComVariant varVal(2, VT_I4);	// xlValue
	ks_stdptr<oldapi::KsoAxis> spValueAxis;
	hr = pChart->Axes(varVal, oldapi::ksoPrimary, (IKCoreObject**)(&spValueAxis));
	if (SUCCEEDED(hr) && spValueAxis)
		hr = SetAxes(spValueAxis, chartStyle.valueAxisFont);
	// 设置雷达图分类标签格式
	hr = SetRadarAxisLabels(pChart, chartStyle.radarAxisFont);
	// 设置图例显示
	return SetChartLegend(pChart, nSeriesCnt > 1, &(chartStyle.legendFont));
}

HRESULT SetChartGroups2(oldapi::KsoChart* pChart, const KUChartGroupStyle& groups)
{
	KComVariant var;
	ks_stdptr<oldapi::KsoChartGroups> spGroups;
	HRESULT hr = pChart->get_ChartGroups(var, (IKCoreObject**)(&spGroups));
	if (FAILED(hr) || !spGroups)
		return E_FAIL;
	long nGroupCnt = 0;
	hr = spGroups->get_Count(&nGroupCnt);
	if (nGroupCnt <= 0)
		return E_FAIL;
	for (long i = 1; i <= nGroupCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<oldapi::KsoChartGroup> spGroup;
		hr = spGroups->Item(varIdx, &spGroup);
		if (FAILED(hr) || !spGroup)
			continue;
		hr = spGroup->put_DoughnutHoleSize(groups.nDoughnutHoleSize);
	}
	return hr;
}

HRESULT SetDoughnutChartStyle(oldapi::KsoChart* pChart, const KUChartStyle& chartStyle)
{
	if (!pChart)
		return E_FAIL;
	KComVariant var;
	ks_stdptr<FullSeriesCollection> spFullSeriesCollection;
	HRESULT hr = pChart->FullSeriesCollection(var, (IKCoreObject**)(&spFullSeriesCollection));
	if (FAILED(hr) || !spFullSeriesCollection)
		return E_FAIL;
	long nSerCnt = 0;
	hr = spFullSeriesCollection->get_Count(&nSerCnt);
	if (nSerCnt <= 0)
		return E_FAIL;
	KComVariant varIdx(1, VT_I4);
	ks_stdptr<oldapi::KsoSeries> spSeries;
	hr = spFullSeriesCollection->Item(varIdx, &spSeries);
	if (FAILED(hr) || !spSeries)
		return E_FAIL;
	// 设置数据点颜色
	hr = SetDataPoint(spSeries, chartStyle.vecSerColor);
	// 设置数据标签
	hr = SetDataLabels(pChart, spSeries, chartStyle.dataLabels);
	// 设置圆环图内径大小
	hr = SetChartGroups2(pChart, chartStyle.groups);
	// 设置图例
	return SetChartLegend(pChart, true, &(chartStyle.legendFont));
}

HRESULT SetPointChartStyle(oldapi::KsoChart* pChart, const KUChartStyle& chartStyle)
{
	if (!pChart)
		return E_FAIL;
	// 设置系列颜色
	long nSeriesCnt = 0;
	HRESULT hr = SetSeries(pChart, chartStyle.vecSerColor, true, true, nSeriesCnt);
	// 设置X轴标签
	KComVariant varCat(1, VT_I4);	// xlCategory
	ks_stdptr<oldapi::KsoAxis> spCategoryAxis;
	hr = pChart->Axes(varCat, oldapi::ksoPrimary, (IKCoreObject**)(&spCategoryAxis));
	if (SUCCEEDED(hr) && spCategoryAxis)
		hr = SetAxes(spCategoryAxis, chartStyle.categoryAxisFont);
	// 设置Y轴标签
	KComVariant varVal(2, VT_I4);	// xlValue
	ks_stdptr<oldapi::KsoAxis> spValueAxis;
	hr = pChart->Axes(varVal, oldapi::ksoPrimary, (IKCoreObject**)(&spValueAxis));
	if (SUCCEEDED(hr) && spValueAxis)
		hr = SetAxes(spValueAxis, chartStyle.valueAxisFont);
	// 设置图例显示
	return SetChartLegend(pChart, nSeriesCnt > 1, &(chartStyle.legendFont));
}

HRESULT SetNormalChartStyle(KUnderstandChartTypeEnum nChartType, oldapi::KsoChart* pChart)
{
	if (!pChart)
		return E_FAIL;
	static const KUChartStyleMap chartStyleMap;
	const KUChartStyle& chartStyle = chartStyleMap.at(nChartType);
	HRESULT hr = SetChartTitle(pChart, chartStyle.titleFont);
	switch (nChartType)
	{
	case UnderstandChartTypeBar:
		return SetBarChartStyle(pChart, chartStyle);
	case UnderstandChartTypeRowBar:
		return SetRowBarChartStyle(pChart, chartStyle);
	case UnderstandChartTypeLine:
		return SetLineChartStyle(pChart, chartStyle);
	case UnderstandChartTypePie:
		return SetPieChartStyle(pChart, chartStyle);
	case UnderstandChartTypeRadar:
		return SetRadarChartStyle(pChart, chartStyle);
	case UnderstandChartTypeDoughnut:
		return SetDoughnutChartStyle(pChart, chartStyle);
	case UnderstandChartTypePoint:
		return SetPointChartStyle(pChart, chartStyle);
	}
	return hr;
}

HRESULT SetNumberFormat(QString strFormat, oldapi::KsoChart* pChart)
{
	if (!pChart)
		return E_FAIL;
	if (strFormat != __X("%"))
		return S_FALSE;
	KComVariant varVal(2, VT_I4);	// xlValue
	ks_stdptr<oldapi::KsoAxis> spValueAxis;
	HRESULT hr = pChart->Axes(varVal, oldapi::ksoPrimary, (IKCoreObject**)(&spValueAxis));
	if (FAILED(hr) || !spValueAxis)
		return E_FAIL;
	ks_stdptr<oldapi::KsoTickLabels> spTickLabels;
	hr = spValueAxis->get_TickLabels(&spTickLabels);
	if (FAILED(hr) || !spTickLabels)
		return E_FAIL;
	KComVariant varFormat(__X("0.00%"));
	return spTickLabels->put_NumberFormatLocal(varFormat);
}

HRESULT KUnderstandChart::InsertChartShape(IKWorksheet* pSheet, QString strType, RANGE& rg, const QString& strTitle, bool bSelect)
{
	IKWorksheetView *pSheetView = pSheet->GetActiveWorksheetView();
	IRenderView* rdView = pSheetView->GetActiveRenderView();
	IRenderData* rdData = rdView->GetNormalView()->GetRenderData();
	if (rdData)
	{
		rdData->GetRowColMeassureData()->ClearRowHeightBuf();
		rdData->GetCellRenderData()->ClearCellRenderDataCache();
	}

	ks_stdptr<etoldapi::Range> spRange;
	HRESULT hr = CreateRange(pSheet, rg, &spRange);
	if (FAILED(hr) || !spRange)
		return E_FAIL;
	if (bSelect)
		hr = spRange->Select();
	KComVariant varTop1, varLeft1;
	spRange->get_Top(&varTop1);
	spRange->get_Left(&varLeft1);
	double nTop = V_R8(&varTop1);
	double nLeft = V_R8(&varLeft1);

	ks_stdptr<etoldapi::_Worksheet> spSheet = pSheet;
	ks_stdptr<etoldapi::Shapes> spShapes;
	hr = spSheet->get_Shapes(FALSE, &spShapes);
	if (FAILED(hr) || !spShapes)
		return E_FAIL;
	KUnderstandChartTypeEnum chartType = m_chartType;
	if (!strType.isEmpty())
		chartType = TransformChartType(strType);
	bool needReverse = false;
	if (chartType == UnderstandChartTypeRowBar && m_chartType != UnderstandChartTypeRowBar
		|| m_chartType == UnderstandChartTypeRowBar && chartType != UnderstandChartTypeRowBar)
		needReverse = true;
	int nApiChartType = chartType;
	if (nApiChartType == UnderstandChartTypeLine && !m_YValuesList.empty() && m_YValuesList[0].GetValues().size() > 10)
		nApiChartType = 4; // xlLine 不带数据标记的折线图
	if (nApiChartType == UnderstandChartTypePoint)
		nApiChartType = UnderstandChartTypeLine;

	KComVariant varStyle(201, VT_I4);
	KComVariant varType(nApiChartType, VT_I4);
	KComVariant varLeft(nLeft, VT_R8);
	KComVariant varTop(nTop, VT_R8);
	KComVariant varWidth(ChartWidth, VT_R8);
	KComVariant varHeight(ChartHeight, VT_R8);
	KComVariant varSource;
	KComVariant var;
	ks_stdptr<etoldapi::Shape> spShape;
	hr = spShapes->AddChart2(varStyle, varType, varLeft, varTop, varWidth, varHeight, var, varSource, &spShape);
	if (FAILED(hr) || !spShape)
		return E_FAIL;
	spShape->put_Placement(oldapi::etMove);
	ks_stdptr<etoldapi::Range> spBR;
	spShape->get_BottomRightCell(&spBR);
	if (spBR)
	{
		long iBottom = 0;
		spBR->get_Row(&iBottom);
		rg.SetRowFromTo(iBottom);
	}
	ks_stdptr<oldapi::KsoChart> spChart;
	hr = spShape->get_Chart(&spChart);
	if (FAILED(hr) || !spChart)
		return E_FAIL;
	ks_stdptr<etoldapi::FullSeriesCollection> spFullSeriesCollection;
	hr = spChart->FullSeriesCollection(var, (IKCoreObject**)(&spFullSeriesCollection));
	if (FAILED(hr) || !spFullSeriesCollection)
		return E_FAIL;
	long nOldSeries = 0;
	hr = spFullSeriesCollection->get_Count(&nOldSeries);
	if (SUCCEEDED(hr) && nOldSeries > 0)
	{	// todo suyihong 如果选区在数据透视表中，会插入数据透视图，这个需要考虑下怎么处理
		for (long i = nOldSeries; i >= 1; --i)
		{
			KComVariant varIdx(i, VT_I4);
			ks_stdptr<oldapi::KsoSeries> spSeries;
			hr = spFullSeriesCollection->Item(varIdx, &spSeries);
			if (FAILED(hr) || !spSeries)
				continue;
			KComVariant varOK;
			hr = spSeries->Delete(&varOK);
		}
	}
	if (!strTitle.isEmpty())
	{
		ks_stdptr<oldapi::KsoChartTitle> spTitle;
		hr = spChart->get_ChartTitle(&spTitle);
		if (SUCCEEDED(hr) && spTitle)
		{
			ks_bstr bstrTitle(krt::utf16(strTitle));
			hr = spTitle->put_Text(bstrTitle);
		}
	}
	else
	{
		spChart->put_HasTitle(VARIANT_FALSE);
	}
	ks_stdptr<etoldapi::SeriesCollection> spSeriesCollection;
	hr = spChart->SeriesCollection(var, (IKCoreObject**)(&spSeriesCollection));
	if (FAILED(hr) || !spSeriesCollection)
		return E_FAIL;
	for (size_t i = 0; i < m_YValuesList.size(); ++i)
	{
		ks_stdptr<oldapi::KsoSeries> spSeries;
		hr = spSeriesCollection->NewSeries(&spSeries);
		if (FAILED(hr) || !spSeries)
			continue;
		ks_bstr bstrName(krt::utf16("=\"" + m_YValuesList[i].GetName() + "\""));
		spSeries->put_Name(bstrName);
		if (!m_XValuesList.empty())
		{
			KComVariant varXValues(krt::utf16(TransformXValues(m_XValuesList[0].GetValues(), needReverse)));
			spSeries->put_XValues(varXValues);
		}
		KComVariant varYValues(krt::utf16(TransformYValues(m_YValuesList[i].GetValues(), needReverse)));
		spSeries->put_Values(varYValues);
	}
	KsoChartType ct = ksoColumnClustered;
	hr = spChart->get_ChartType(&ct);
	if (nApiChartType == 4 && ct != ksoLineStandard)
		hr = spChart->put_ChartType(ksoLineStandard);
	hr = SetNormalChartStyle(chartType, spChart);
	if (!m_strFormat.isEmpty() && nApiChartType != UnderstandChartTypePie && nApiChartType != UnderstandChartTypeDoughnut)
		hr = SetNumberFormat(m_strFormat, spChart);
	return hr;
}

HRESULT KUnderstandChart::InsertChart(IKWorksheet* pSheet, QString strType, RANGE& rg, const QString& strTitle, bool bSelect)
{
	HRESULT hr = S_OK;
	ks_stdptr<etoldapi::_Worksheet> spSheet = pSheet;
	if (!spSheet)
		return E_FAIL;
	ks_stdptr<IKApplication> spApp = pSheet->GetApplication();
	ks_stdptr<IKEtApplication> spEtApp = spApp;
	ks_stdptr<IKWorksheet> spActiveSheet = spEtApp->GetActiveWorksheet();
	if (spActiveSheet != spSheet)
		spSheet->Activate();
	hr = InsertChartShape(pSheet, strType, rg, strTitle, bSelect);
	return hr;
}

HRESULT KUnderstandChart::ExportJson(QJsonObject& jsonObj) const
{
	jsonObj["chartType"] = m_chartTypeName;
	jsonObj["curChartType"] = m_curChartType;
	jsonObj["format"] = m_strFormat;
	QJsonArray jsonXValuesList;
	for (auto it = m_XValuesList.cbegin(); it != m_XValuesList.cend(); ++it)
	{
		QJsonObject jsonValues;
		(*it).ExportJson(jsonValues);
		jsonXValuesList.append(jsonValues);
	}
	jsonObj["xValuesList"] = jsonXValuesList;
	QJsonArray jsonYValuesList;
	for (auto it = m_YValuesList.cbegin(); it != m_YValuesList.cend(); ++it)
	{
		QJsonObject jsonValues;
		(*it).ExportJson(jsonValues);
		jsonYValuesList.append(jsonValues);
	}
	jsonObj["yValuesList"] = jsonYValuesList;
	return S_OK;
}

QString KUnderstandChart::GetChartType() const
{
	return m_chartTypeName;
}

void KUnderstandChart::ChangeChartType(const QString& strChartType)
{
	m_curChartType = strChartType;
}

bool KUnderstandChart::IsMoreXValues()
{
	return m_XValuesList.size() > 1;
}

void KReportConclusion::Init(const QJsonObject& jsonObj)
{
	if (jsonObj.contains("chartTypes") && jsonObj["chartTypes"].isArray())
	{
		const QJsonArray& jsonList = jsonObj["chartTypes"].toArray();
		for (int i = 0; i < jsonList.size(); ++i)
			vecChartType.push_back(jsonList[i].toString());
	}
	if (jsonObj.contains("conclusion"))
		strConclusion = jsonObj["conclusion"].toString();
	if (jsonObj.contains("analysisType"))
		strAnalysisType = jsonObj["analysisType"].toString();
}

HRESULT KReportConclusion::ExportJson(QJsonObject& jsonObj) const
{
	QJsonArray jsonList;
	for (size_t i = 0; i < vecChartType.size(); ++i)
		jsonList.append(vecChartType[i]);
	jsonObj["chartTypes"] = jsonList;
	jsonObj["conclusion"] = strConclusion;
	jsonObj["analysisType"] = strAnalysisType;
	return S_OK;
}

KReportPart::KReportPart(int i, IKWorksheet* pSheet, QJsonObject& jsonObj)
	: m_pChart(NULL), m_spSheet(pSheet)
{
	m_strChartName = krt::fromUtf16(__X("mountNode")) + QString::number(i + 1);
	Init(jsonObj);
}

KReportPart::KReportPart(int i, IKWorksheet* pSheet)
	: m_pChart(NULL), m_spSheet(pSheet)
{
	m_strChartName = krt::fromUtf16(__X("mountNode")) + QString::number(i + 1);
}

KReportPart::~KReportPart()
{
	if (m_pChart)
		delete m_pChart;
}

QString KReportPart::GetChartName() const
{
	return m_strChartName;
}

QString KReportPart::GetChartType() const
{
	if (!m_pChart)
		return QString();
	return m_pChart->GetChartType();
}

HRESULT KReportPart::ChangeChartType(const QString& strChartType)
{
	if (!m_pChart)
		return E_FAIL;
	m_pChart->ChangeChartType(strChartType);
	return S_OK;
}

QString KReportPart::GetAnalysisType(const QString& strChartType) const
{
	if (strChartType.isEmpty() || (m_pChart && strChartType == m_pChart->GetChartType()))
		return m_strAnalysisType;
	for (size_t i = 0; i < m_vecConclusion.size(); ++i)
		for (size_t j = 0; j < m_vecConclusion[i].vecChartType.size(); ++j)
			if (m_vecConclusion[i].vecChartType[j] == strChartType)
			{
				if (m_vecConclusion[i].strAnalysisType.isEmpty())
					return m_strAnalysisType;
				else
					return m_vecConclusion[i].strAnalysisType;
			}
	return m_strAnalysisType;
}

HRESULT KReportPart::GetTableData(QJsonArray& jsonObj) const
{
	int nCol = 0;
	for (auto it = m_tableData.begin(); it != m_tableData.end(); ++it)
	{
		const QStringList& row = *it;
		if (row.isEmpty())
			return S_FALSE;
		if (nCol <= 0)
			nCol = row.size();
		else if (nCol != row.size())
			return S_FALSE;
		QJsonArray jsonRow;
		for (int i = 0; i < row.size(); ++i)
			jsonRow.append(row[i]);
		jsonObj.append(jsonRow);
	}
	return S_OK;
}

HRESULT KReportPart::GetShapeOutRange(IKWorksheet* pSheet, RANGE& rg)
{
	ks_stdptr<etoldapi::_Worksheet> spSheet = pSheet;
	ks_stdptr<etoldapi::Shapes> spShapes;
	HRESULT hr = spSheet->get_Shapes(FALSE, &spShapes);
	if (FAILED(hr) || !spShapes)
		return E_FAIL;

	int nShapeCnt = 0;
	spShapes->get_Count(&nShapeCnt);
	if (nShapeCnt <= 0)
		return E_FAIL;

	for (int i = 1; i <= nShapeCnt; ++i)
	{
		KComVariant varIdx(i, VT_I4);
		ks_stdptr<etoldapi::Shape> spShape;
		hr = spShapes->Item(varIdx, &spShape);
		if (FAILED(hr) || !spShape)
			continue;;
		ks_stdptr<etoldapi::Range> spTL;
		spShape->get_TopLeftCell(&spTL);
		ks_stdptr<etoldapi::Range> spBR;
		spShape->get_BottomRightCell(&spBR);
		long lTop = 0, lLeft = 0, lBottom = 0, lRight = 0;
		spTL->get_Row(&lTop);
		spTL->get_Column(&lLeft);
		spBR->get_Row(&lBottom);
		spBR->get_Column(&lRight);
		lTop--, lLeft--, lBottom--, lRight--;
		if (1 == i)
		{
			rg.SetRowFromTo(lTop, lBottom);
			rg.SetColFromTo(lLeft, lRight);
			continue;
		}
		if (rg.RowFrom() > lTop)
			rg.SetRowFrom(lTop);
		if (rg.RowTo() < lBottom)
			rg.SetRowTo(lBottom);
		if (rg.ColFrom() > lLeft)
			rg.SetColFrom(lLeft);
		if (rg.ColTo() < lRight)
			rg.SetColTo(lRight);
	}
	return S_OK;
}

HRESULT KReportPart::GetInsertRange(IKWorksheet* pSheet, RANGE& rg)
{
	ks_stdptr<etoldapi::_Worksheet> spWorksheet = pSheet;
	ks_stdptr<etoldapi::Range> spUsedRange;
	HRESULT hr = spWorksheet->get_UsedRange(&spUsedRange);
	if (FAILED(hr) || !spUsedRange)
		return E_FAIL;

	ROW nRow = 0;
	COL nCol = 0;
	KComVariant var;
	ks_stdptr<etoldapi::PivotTables> spPivotTables;
	hr = spWorksheet->PivotTables(var, 0, (IKCoreObject**)&spPivotTables);
	if (SUCCEEDED(hr) && spPivotTables)
	{
		long nPivotTableCnt = 0;
		spPivotTables->get_Count(&nPivotTableCnt);
		if (nPivotTableCnt > 0)
		{
			ks_stdptr<etoldapi::PivotTable> spPivotTable;
			KComVariant var1(1, VT_I4);
			hr = spPivotTables->Item(var1, &spPivotTable);
			if (SUCCEEDED(hr) && spPivotTable)
			{
				ks_stdptr<etoldapi::Range> spTableRange;
				hr = spPivotTable->get_TableRange2(&spTableRange);
				if (SUCCEEDED(hr) && spTableRange)
				{
					RANGE rgPivotTable(rg.GetBMP());
					IdentifyTool::GetTableRange(spTableRange, &rgPivotTable);
					nRow = rgPivotTable.RowFrom();
					nCol = rgPivotTable.ColTo();
				}
			}
		}
	}

	rg.SetRowFromTo(nRow);	// 如果在数据源工作表插入则在第一行
	RANGE rgUsed(rg.GetBMP());
	IdentifyTool::GetTableRange(spUsedRange, &rgUsed);
	RANGE rgShape(rg.GetBMP());
	hr = GetShapeOutRange(pSheet, rgShape);
	if (SUCCEEDED(hr) && !rgShape.IsValid() && rgShape.ColTo() > rgUsed.ColTo() && rgShape.ColTo() < MaxInsertCol)
		rg.SetColFromTo(rgShape.ColTo() + 2);
	else if (rgUsed.ColTo() < MaxInsertCol)
		rg.SetColFromTo(rgUsed.ColTo() + 2);
	else
		rg.SetColFromTo(nCol + 2);
	return S_OK;
}

HRESULT SetCellTextAndFont(etoldapi::Range* pCell, QString strText, float fFontSize, const QString& strName, bool bBold)
{
	KComVariant valText;
	ks_bstr bstrText(krt::utf16(strText));
	valText.AssignBSTR(bstrText);
	HRESULT hr = pCell->put_Value2(valText);
	if (FAILED(hr))
		return hr;

	SetPivotTableFont(pCell, fFontSize, strName, bBold);
	return S_OK;
}

HRESULT SetCellText(etoldapi::Range* pCell, QString strText)
{
	KComVariant valText;
	ks_bstr bstrText(krt::utf16(strText));
	valText.AssignBSTR(bstrText);
	HRESULT hr = pCell->put_Value2(valText);
	return hr;
}

HRESULT SetTableColor(etoldapi::Range* pRange, bool bBold, long fontColor, long fillColor, bool bTheme = false)
{
	if (!pRange)
		return E_FAIL;
	ks_stdptr<etoldapi::Font> spFont;
	HRESULT hr = pRange->get_Font(&spFont);
	if (SUCCEEDED(hr) && spFont)
	{
		if (bBold)
		{
			KComVariant var(VARIANT_TRUE);
			hr = spFont->put_Bold(var);
		}
		if (bTheme)
		{
			KComVariant varThemeColor(xlThemeColorDark1, VT_I4);
			hr = spFont->put_ThemeColor(varThemeColor);
		}
		else
			hr = spFont->put_Color(fontColor);
	}
	ks_stdptr<etoldapi::Interior> spInterior;
	hr = pRange->get_Interior(&spInterior);
	if (SUCCEEDED(hr) && spInterior)
		hr = spInterior->put_Color(fillColor);
	return S_OK;
}

HRESULT KReportPart::InsertTitle(IKWorksheet* pSheet, const QString& strType, RANGE& rg, bool bSelect)
{
	ks_stdptr<etoldapi::Range> spRange;
	HRESULT hr = CreateRange(pSheet, rg, &spRange);
	if (FAILED(hr) || !spRange)
		return hr;
	if (bSelect)
		spRange->Select();

	QString strAnalysisType = GetAnalysisType(strType);
	QString strTitle = strAnalysisType;
	if (!m_qslNameList.isEmpty())
	{
		strTitle += " ";
		for (int i = 0; i < m_qslNameList.size(); ++i)
			if (!m_qslNameList[i].isEmpty())
				strTitle += krt::fromUtf16(__X("“")) + m_qslNameList[i] + krt::fromUtf16(__X("”"));
	}
	hr = SetCellTextAndFont(spRange, strTitle, 13.0, staticStrNormalFontName, true);

	// 设置大标题部分字体颜色
	KComVariant varStart(1, VT_I4);
	KComVariant varEnd(strAnalysisType.size(), VT_I4);
	ks_stdptr<etoldapi::Characters> spCharacters;
	hr = spRange->get_Characters(varStart, varEnd, &spCharacters);
	if (SUCCEEDED(hr) && spCharacters)
	{
		ks_stdptr<etoldapi::Font> spFont;
		hr = spCharacters->get_Font(&spFont);
		if (SUCCEEDED(hr) && spFont)
			spFont->put_Color(0xFFB5752F);
	}

	rg.SetRowFromTo(rg.RowTo() + 1);
	return S_OK;
}

QString KReportPart::GetConclusion(const QString& strChartType)
{
	if (!m_pChart || strChartType == m_pChart->GetChartType())
		return m_strConclusion;
	for (size_t i = 0; i < m_vecConclusion.size(); ++i)
		for (size_t j = 0; j < m_vecConclusion[i].vecChartType.size(); ++j)
			if (m_vecConclusion[i].vecChartType[j] == strChartType)
			{
				if (m_vecConclusion[i].strConclusion.isEmpty())
					return m_strConclusion;
				else
					return m_vecConclusion[i].strConclusion;
			}
	return m_strConclusion;
}

HRESULT KReportPart::InsertConclusion(IKWorksheet* pSheet, const QString& strType, RANGE& rg, bool isColAutoFit, bool bSelect)
{
	ks_stdptr<etoldapi::Range> spRange;
	HRESULT hr = CreateRange(pSheet, rg, &spRange);
	if (FAILED(hr) || !spRange)
		return hr;
	if (bSelect)
		spRange->Select();

	hr = SetCellTextAndFont(spRange, GetConclusion(strType), 12.0, staticStrNormalFontName, false);

	// 自适应列宽
	if (isColAutoFit)
	{
		ks_stdptr<etoldapi::Range> spEntCol;
		hr = spRange->get_EntireColumn(&spEntCol);
		if (SUCCEEDED(hr) && spEntCol)
			spEntCol->AutoFit();
	}

	rg.SetRowFromTo(rg.RowTo() + 1);
	return S_OK;
}

RANGE KReportPart::GetRealInsertTableRange(const RANGE &range)
{
	RANGE r(range);

	if (m_tableData.empty() || m_tableData.front().empty())
	{
		r.SetSheetFromTo(INVALIDIDX);
		return r;
	}
	int nRow = m_tableData.size();
	int nCol = m_tableData.front().size();

	r.SetRowTo(r.RowFrom() + nCol - 1);
	r.SetColTo(r.ColFrom() + nRow - 1);
	return r;
}

HRESULT KReportPart::InsertTable(IKWorksheet* pSheet, RANGE& rg, bool isColAutoFit, bool isRowAutoFit, bool isWrapText, bool bSelect)
{
	int nRow = m_tableData.size();
	int nCol = m_tableData.front().size();
	ks_stdptr<etoldapi::Range> spRange;
	HRESULT hr = CreateRange(pSheet, rg, &spRange);
	if (FAILED(hr) || !spRange)
		return hr;
	if (bSelect)
		spRange->Select();

	for (int iRow = 0; iRow < nRow && iRow < (int)m_tableData.size(); ++iRow)
	{
		for (int iCol = 0; iCol < nCol && iCol < (int)m_tableData[iRow].size(); ++iCol)
		{
			KComVariant varRow(iRow + 1, VT_I4);
			KComVariant varCol(iCol + 1, VT_I4);
			KComVariant varCell;
			// 产品要求插入的表格横向排列, 客户端代码为纵向, 此处行列对换
			hr = spRange->get_Item(varCol, varRow, &varCell);
			if (FAILED(hr))
				continue;
			ks_stdptr<etoldapi::Range> spCell = KSmartParam(varCell).GetInterfaceValue();
			if (!spCell)
				continue;
			SetCellText(spCell, m_tableData[iRow][iCol]);
		}
	}

	// 设置整表字体字号
	hr = SetPivotTableFont(spRange, 11.0, staticStrNormalFontName, false);

	// 设置左对齐
	spRange->put_HorizontalAlignment(etHAlignLeft);
	// 设置垂直对齐-居中
	spRange->put_VerticalAlignment(etVAlignCenter);

	// 设置首行字体颜色和填充颜色
	RANGE rgFirstCol(rg);
	rgFirstCol.SetColTo(rg.ColFrom());
	ks_stdptr<etoldapi::Range> spFirstRow;
	hr = CreateRange(pSheet, rgFirstCol, &spFirstRow);
	if (SUCCEEDED(hr) && spFirstRow)
		hr = SetTableColor(spFirstRow, true, xlThemeColorDark1, 0xFFB5752F, true);

	// 设置表格边框
	ks_stdptr<etoldapi::Borders> spBorders;
	hr = spRange->get_Borders(&spBorders);
	if (SUCCEEDED(hr) && spBorders)
	{
		spBorders->put_LineStyle(oldapi::etContinuous);
		spBorders->put_Color(0xFFF4DDCE);
	}
	// 设置自动换行
	if (isWrapText)
	{
		KComVariant varTrue(VARIANT_TRUE);
		spRange->put_WrapText(varTrue);
	}
	// 自适应行高
	if (isRowAutoFit)
	{
		ks_stdptr<etoldapi::Range> spEntRow;
		hr = spRange->get_EntireRow(&spEntRow);
		if (SUCCEEDED(hr) && spEntRow)
			spEntRow->AutoFit();
	}
	// 自适应列宽
	if (isColAutoFit)
	{
		ks_stdptr<etoldapi::Range> spEntCol;
		hr = spRange->get_EntireColumn(&spEntCol);
		if (SUCCEEDED(hr) && spEntCol)
			spEntCol->AutoFit();

		IKWorksheetView *pSheetView = pSheet->GetActiveWorksheetView();
		IRenderView* rdView = pSheetView->GetActiveRenderView();
		IRenderData* rdData = rdView->GetNormalView()->GetRenderData();
		if (rdData)
		{
			rdData->GetRowColMeassureData()->ClearRowHeightBuf();
			rdData->GetCellRenderData()->ClearCellRenderDataCache();
		}
	}

	rg.SetColTo(rg.ColFrom());
	rg.SetRowFromTo(rg.RowTo() + 1);
	return S_OK;
}

HRESULT KReportPart::InsertChart(IKWorksheet* pSheet, QString strType, RANGE& rg, bool bTitle, bool bSelect)
{
	if (!m_pChart)
		return S_FALSE;

	if (TransformChartTypeEx(strType) != UnderstandChartWebExtension && !m_pChart->IsMoreXValues())
	{
		QString strTitle;
		if (bTitle)
			strTitle = GetConclusion(strType);
		return m_pChart->InsertChart(pSheet, strType, rg, strTitle, bSelect);
	}
	else
	{
		return InsertWebShapeChart(pSheet, strType, rg, bTitle, bSelect);
	}
}

HRESULT KReportPart::InsertWebShapeChart(IKWorksheet* pSheet, QString strType, RANGE& rg, bool bTitle, bool bSelect)
{
    // todo : check unused feature

	// QJsonObject understandResult;
	// ExportJson(understandResult, strType);
	// QJsonDocument doc(understandResult);
	// IKWebExtension* pWebExt = KSmartChartWebShape::getInstance()->createSmartChart(doc.toJson(QT_NAMESPACE::QJsonDocument::Compact), bTitle);
	// if (!pWebExt)
	// 	return E_FAIL;
	// ks_stdptr<IKShape> spShape;
	// pWebExt->GetHostShape(&spShape);
	// if (!spShape)
	// 	return E_FAIL;
	// ks_stdptr<IKClientAnchor> spAnchor;
	// spShape->GetClientAnchor(&spAnchor);
	// if (!spAnchor)
	// 	return E_FAIL;

	// // 设置对象位置大小
	// ks_stdptr<etoldapi::Range> spRange;
	// HRESULT hr = CreateRange(pSheet, rg, &spRange);
	// if (FAILED(hr) || !spRange)
	// 	return E_FAIL;
	// KComVariant varTop1, varLeft1;
	// spRange->get_Top(&varTop1);
	// spRange->get_Left(&varLeft1);
	// RECT rc;
	// rc.left = rc.right = rc.top = rc.bottom = 0;
	// rc.top = _TxPointToTwip((V_R8(&varTop1) + 5));
	// rc.left = _TxPointToTwip((V_R8(&varLeft1) + 5));
	// rc.right = rc.left + _TxPointToTwip(ChartWidth);
	// rc.bottom = rc.top + _TxPointToTwip(ChartHeight);
	// spAnchor->SetRect(spShape, &rc);

	// // 设置后续插入内容的单元格
	// CELL cellX, cellY;
	// cellX.row = cellX.col = cellY.row = cellY.col = 0;
	// INT leftX = 0, leftY = 0, rightX = 0, rightY = 0;
	// ks_stdptr<IETShapeAnchor> spShapeAnchor = spAnchor;
	// if (!spShapeAnchor)
	// 	return E_FAIL; 
	// spShapeAnchor->GetAnchor(cellX, leftX, leftY, cellY, rightX, rightY);
	// rg.SetRowFromTo(cellY.row + 1);

	return S_OK;
}

HRESULT KReportPart::InsertReport(QString strType, QString strInsertType, bool isNewSheet, QString& strSheetName, bool bSelect)
{
	HRESULT hr = S_OK;

	ks_stdptr<IKWorksheet> spSheet = m_spSheet;
	ks_stdptr<_Workbook> spWorkbook = spSheet->GetWorkbook();
	if (isNewSheet)
	{
		ks_stdptr<etoldapi::Worksheets> spSheets;
		hr = spWorkbook->get_Worksheets(&spSheets);
		if (SUCCEEDED(hr) && spSheets)
		{
			IDX iSheet = 0;
			m_spSheet->GetSheet()->GetIndex(&iSheet);
			// 插入工作表
			KComVariant varBefore(iSheet + 1, VT_I4);
			KComVariant var;
			ks_stdptr<etoldapi::_Worksheet> spWorksheet;
            // todo
			// hr = spSheets->Add(varBefore, var, var, var, (IKCoreObject**)(&spWorksheet));
			if (SUCCEEDED(hr) && spWorksheet)
			{
				strSheetName = GetAnalysisType(strType) + krt::fromUtf16(__X("_"));
				RenameSheet(spWorksheet, spWorkbook, strSheetName);
				spSheet = spWorksheet;

				// 设置网格线不显示
				ks_stdptr<IKWorksheetView> spView = spWorksheet->GetActiveWorksheetView();
				hr = spView->SetDisplayGridlines(FALSE);
			}
		}
	}

	ks_stdptr<IKTransactionTool> spTransTool = spWorkbook->GetTransactionTool();
	spTransTool->StartTrans();

	// 空白文档放在A2
	RANGE rg(spSheet->GetSheet()->GetBMP());
	IDX iSheet = 0;
	spSheet->GetSheet()->GetIndex(&iSheet);
	rg.SetCell(iSheet, 1, 0);
	if (!isNewSheet)
	{	// 非新建的空白文档，需要找插入的位置
		hr = GetInsertRange(spSheet, rg);
		if (FAILED(hr))
		{
			spTransTool->Rollback();
			return hr;
		}
	}

	if (strInsertType == "conclusion")
	{
		hr = InsertConclusion(spSheet, strType, rg, true, bSelect);
	}
	else if (strInsertType == "chart")
	{
		hr = InsertChart(spSheet, strType, rg, true, bSelect);
	}
	else if (strInsertType == "table")
	{
		hr = InsertTable(spSheet, rg, true, false, false, bSelect);
	}
	else
	{
		RANGE rgLT(rg);
		hr = InsertTitle(spSheet, strType, rg, bSelect);
		hr = InsertConclusion(spSheet, strType, rg, false, bSelect);
		hr = InsertChart(spSheet, strType, rg, false, bSelect);
		hr = InsertTable(spSheet, rg, false, true, true, bSelect);

		if (bSelect)
		{
			ks_stdptr<etoldapi::Range> spLTRange;
			hr = CreateRange(spSheet, rgLT, &spLTRange);
			if (SUCCEEDED(hr) && spLTRange)
			{
				hr = spLTRange->Select();
			}
		}
	}

	if (isNewSheet)
	{
		m_spSheet->Activate(FALSE);
	}

	if (SUCCEEDED(hr))
		spTransTool->CommitTrans(nullptr, ksoCommit, FALSE);
	else
		spTransTool->Rollback();
	return hr;
}

HRESULT KReportPart::ExportJson(QJsonObject& jsonObj, const QString& strType) const
{
	jsonObj["chartName"] = m_strChartName;
	jsonObj["conclusion"] = m_strConclusion;
	if (m_pChart)
	{
		QJsonObject jsonChart;
		m_pChart->ExportJson(jsonChart);
		if (!strType.isEmpty())
			jsonChart["chartType"] = strType;
		jsonObj["chartData"] = jsonChart;
		QJsonArray jsonTable;
		GetTableData(jsonTable);
		jsonObj["tableData"] = jsonTable;
	}
	jsonObj["analysisType"] = m_strAnalysisType;
	QJsonArray jsonNameList;
	for (int i = 0; i < m_qslNameList.size(); ++i)
		jsonNameList.append(m_qslNameList[i]);
	jsonObj["nameList"] = jsonNameList;
	QJsonArray jsonConclusionList;
	for (size_t i = 0; i < m_vecConclusion.size(); ++i)
	{
		QJsonObject jsonConclusion;
		m_vecConclusion[i].ExportJson(jsonConclusion);
		jsonConclusionList.append(jsonConclusion);
	}
	jsonObj["chartTypeConclusionList"] = jsonConclusionList;
	return S_OK;
}

QT_NAMESPACE::QStringList KReportPart::GetNameList()
{
	return m_qslNameList;
}

void KReportPart::Init(QJsonObject& jsonObj)
{
    // todo :fix compile 
	// if (jsonObj.contains("chartData") && jsonObj["chartData"].isObject())
	// 	m_pChart = new KUnderstandChart(m_spSheet, jsonObj["chartData"].toObject());
	if (jsonObj.contains("conclusion"))
		m_strConclusion = jsonObj["conclusion"].toString();
	if (jsonObj.contains("tableData") && jsonObj["tableData"].isArray())
	{
		QJsonArray jsonTable = jsonObj["tableData"].toArray();
		for (int i = 0; i < jsonTable.size(); ++i)
		{
			if (!jsonTable[i].isArray())
				continue;
			QStringList qslRow;
			QJsonArray jsonRow = jsonTable[i].toArray();
			for (int j = 0; j < jsonRow.size(); ++j)
				qslRow.push_back(jsonRow[j].toString());
			m_tableData.push_back(qslRow);
		}
	}
	if (jsonObj.contains("views") && jsonObj["views"].isArray())
	{
		QJsonArray jsonViews = jsonObj["views"].toArray();
		for (int i = 0; i < jsonViews.size(); ++i)
			m_qslViews.push_back(jsonViews[i].toString());
	}
	if (jsonObj.contains("analysisType") && jsonObj["analysisType"].isString())
		m_strAnalysisType = jsonObj["analysisType"].toString();
	if (jsonObj.contains("nameList") && jsonObj["nameList"].isArray())
	{
		QJsonArray jsonNameList = jsonObj["nameList"].toArray();
		for (int i = 0; i < jsonNameList.size(); ++i)
			m_qslNameList.push_back(jsonNameList[i].toString());
	}
	if (jsonObj.contains("chartTypeConclusionList") && jsonObj["chartTypeConclusionList"].isArray())
	{
		QJsonArray jsonConclusionList = jsonObj["chartTypeConclusionList"].toArray();
		m_vecConclusion.resize(jsonConclusionList.size());
		for (int i = 0; i < jsonConclusionList.size(); ++i)
			m_vecConclusion[i].Init(jsonConclusionList[i].toObject());
	}
}

KUnderstand::KUnderstand(IKWorksheet* pSheet, QJsonArray& jsonObj)
	: m_spSheet(pSheet), m_bIsFirstInsert(TRUE), m_bReady(FALSE)
{
	Init(jsonObj);
}

KUnderstand::KUnderstand(IKWorksheet* pSheet)
	: m_spSheet(pSheet), m_bIsFirstInsert(TRUE), m_bReady(FALSE)
{
}

KUnderstand::~KUnderstand()
{
	ClearAll();
}

void KUnderstand::ClearAll()
{
	for (auto it = begin(); it != end(); ++it)
	{
		if (*it)
		{
			delete (*it);
			*it = NULL;
		}
	}
	std::vector<KReportPart*>::clear();
}

void KUnderstand::Init(QJsonArray& jsonObj)
{
	m_bReady = TRUE;
	if (jsonObj.isEmpty())
		return;
	for (int i = 0; i < jsonObj.size(); ++i)
	{
		QJsonObject jsonReport = jsonObj[i].toObject();
		KReportPart* pPart = new KReportPart(i, m_spSheet, jsonReport);
		if (pPart)
			this->push_back(pPart);
	}
}

// 接受服务器返回的解读结果Json数据
HRESULT KUnderstand::ParseUnderstandJsonObj(IKWorksheet* pSheet, QJsonArray& jsonObj, QJsonArray& jsonReportList)
{
	if (!empty())
		ClearAll();
	Init(jsonObj);
	return ExportJsonObj(jsonReportList);
}

// 获取解读结果Json数据
HRESULT KUnderstand::ExportJsonObj(QJsonArray& jsonReportList)
{
	for (auto it = begin(); it != end(); ++it)
	{
		KReportPart* pPart = *it;
		if (!pPart)
			continue;
		QJsonObject jsonObj;
		pPart->ExportJson(jsonObj);
		jsonReportList.append(jsonObj);
	}
	return S_OK;
}

// 获取某图表的表格数据
HRESULT KUnderstand::GetChartTableJsonObj(QString strChart, QJsonArray& jsonObj)
{
	for (auto it = begin(); it != end(); ++it)
	{
		KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == strChart)
			return pPart->GetTableData(jsonObj);
	}
	return S_FALSE;
}

// 将某图表插入文档
HRESULT KUnderstand::InsertReport(QString strChart, QString strType, QString strInsertType, bool isNewSheet, QString& strSheetName, bool bSelect)
{
	if (m_bIsFirstInsert)
		m_bIsFirstInsert = FALSE;
	for (auto it = begin(); it != end(); ++it)
	{
		KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == strChart)
			return pPart->InsertReport(strType, strInsertType, isNewSheet, strSheetName, bSelect);
	}
	return S_FALSE;
}

// 当前是否有解读
BOOL KUnderstand::IsEmpty()
{
	if (!empty())
		return FALSE;
	return TRUE;
}

HRESULT KUnderstand::GetReport(const QString& strType, const QString& chartName, QJsonObject& object)
{
	for (auto it = begin(); it != end(); ++it)
	{
		KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == chartName)
			return pPart->ExportJson(object, strType);
	}
	return S_FALSE;

}

BOOL KUnderstand::IsFirstInsertReport() const
{
	return m_bIsFirstInsert;
}

// 获取解读个数
INT KUnderstand::GetCount() const
{
	return (INT)size();
}

// 获取解读名称
QString KUnderstand::GetReportName(int idx) const
{
	if (idx < 0 || idx >= (int)size())
		return QString();
	auto it = begin() + idx;
	if (*it)
		return (*it)->GetChartName();
	return QString();
}

// 获取解读信息
int KUnderstand::GetReportInfo(const QString& strChartName, QString& strChartType, QString& strAnalysisType) const
{
	int i = 0;
	for (auto it = begin(); it != end(); ++it, ++i)
	{
		const KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == strChartName)
		{
			strChartType = pPart->GetChartType();
			strAnalysisType = pPart->GetAnalysisType(strChartType);
			return i;
		}
	}
	return -1;
}

// 设置某解读片段点赞状态
HRESULT KUnderstand::SetReportLike(const QString& strChart, bool b)
{
	for (auto it = begin(); it != end(); ++it)
	{
		KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == strChart)
		{
			pPart->SetLike(b);
			break;
		}
	}
	return S_OK;
}

// 判断是否已经返回解读结果
BOOL KUnderstand::IsReady() const
{
	return m_bReady;
}

// 设置空白分析页的解读
HRESULT KUnderstand::SetIsReady()
{
	m_bReady = TRUE;
	return S_OK;
}

// 获取某个解读结果Json数据
HRESULT KUnderstand::ExportReportJsonObj(const QString& strChartName, QJsonObject& jsonReport) const
{
	for (auto it = begin(); it != end(); ++it)
	{
		const KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == strChartName)
		{
			pPart->ExportJson(jsonReport);
			break;
		}
	}
	return S_OK;
}

// 切换某个解读的图表类型
HRESULT KUnderstand::ChangeChartType(const QString& strChartName, const QString& strChartType)
{
	for (auto it = begin(); it != end(); ++it)
	{
		KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == strChartName)
		{
			pPart->ChangeChartType(strChartType);
			break;
		}
	}
	return S_OK;
}

// 获取当前分析的行字段、列字段、值字段名字列表
void KUnderstand::GetRowColDataFields(const QString& strChartName, QStringList& qslRowFields, QStringList& qslColFields, QStringList& qslDataFields) const
{

}

// 获取某解读片段的使用字段名称
QStringList KUnderstand::GetNameList(QString strChart) const
{
	for (auto it = begin(); it != end(); ++it)
	{
		KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == strChart)
			return pPart->GetNameList();
	}
	return QStringList();
}

// ----------------------------------------------------------------------

KRecommendChart::KRecommendChart(int i, IKWorksheet* pSheet, QJsonObject& jsonObj)
	: KReportPart(i, pSheet), m_bLike(false)
{
	Init(jsonObj);
}

KRecommendChart::~KRecommendChart()
{

}

void InitFields(const QJsonArray& jsonFields, QStringList& qslFields)
{
	for (int i = 0; i < jsonFields.size(); ++i)
	{
		if (!jsonFields[i].isObject())
			continue;
		QJsonObject jsonField = jsonFields[i].toObject();
		if (jsonField.contains("name") && jsonField["name"].isString())
			qslFields.append(jsonField["name"].toString());
	}
}

void InitFields(const QJsonArray& jsonFields, VecNameField& vecFields)
{
	for (int i = 0; i < jsonFields.size(); ++i)
	{
		if (!jsonFields[i].isObject())
			continue;
		QJsonObject jsonField = jsonFields[i].toObject();
		if (jsonField.contains("name") && jsonField["name"].isString())
		{
			QString strName = jsonField["name"].toString();
			oldapi::ETConsolidationFunction func = oldapi::etSum;
			if (strName.startsWith(krt::fromUtf16(__X("求和项:"))))
			{
				func = oldapi::etSum;
				strName = strName.mid(4);
			}
			else if (strName.startsWith(krt::fromUtf16(__X("计数项:"))))
			{
				func = oldapi::etCount;
				strName = strName.mid(4);
			}
			else if (strName.startsWith(krt::fromUtf16(__X("平均值:"))))
			{
				func = oldapi::etAverage;
				strName = strName.mid(4);
			}
			vecFields.push_back(std::make_pair(strName, func));
		}
	}
}

void KRecommendChart::Init(QJsonObject& jsonObj)
{
	KReportPart::Init(jsonObj);

	if (jsonObj.contains("pivotTableInfo") && jsonObj["pivotTableInfo"].isObject())
	{
		QJsonObject jsonTableInfo = jsonObj["pivotTableInfo"].toObject();
		if (jsonTableInfo.contains("rowFields") && jsonTableInfo["rowFields"].isArray())
			InitFields(jsonTableInfo["rowFields"].toArray(), m_qslRowFields);
		if (jsonTableInfo.contains("columnFields") && jsonTableInfo["columnFields"].isArray())
			InitFields(jsonTableInfo["columnFields"].toArray(), m_qslColFields);
		if (jsonTableInfo.contains("dataFields") && jsonTableInfo["dataFields"].isArray())
			InitFields(jsonTableInfo["dataFields"].toArray(), m_vecDataFields);
	}
}

HRESULT KRecommendChart::GetRowColDataFields(QStringList& qslRow, QStringList& qslCol, VecNameField& vecData) const
{
	qslRow = m_qslRowFields;
	qslCol = m_qslColFields;
	vecData = m_vecDataFields;
	return S_OK;
}

HRESULT KRecommendChart::ExportJson(QJsonObject& jsonObj, const QString& strType) const
{
	jsonObj["like"] = m_bLike;
	return KReportPart::ExportJson(jsonObj, strType);
}

KRecommendUnderstand::KRecommendUnderstand(IKWorksheet* pSheet, QJsonArray& jsonObj)
	: KUnderstand(pSheet)
{
	Init(jsonObj);
}

KRecommendUnderstand::~KRecommendUnderstand()
{

}

const KRecommendChart* KRecommendUnderstand::GetRecommend(QString strName) const
{
	for (auto it = begin(); it != end(); ++it)
	{
		const KReportPart* pPart = *it;
		if (!pPart)
			continue;
		if (pPart->GetChartName() == strName)
			return (const KRecommendChart*)pPart;
	}
	return nullptr;
}

// 获取当前分析的行字段、列字段、值字段名字列表
void KRecommendUnderstand::GetRowColDataFields(const QString& strChartName, QStringList& qslRowFields, QStringList& qslColFields, QStringList& qslDataFields) const
{
	const KRecommendChart* pChart = GetRecommend(strChartName);
	if (pChart == nullptr)
		return;
	VecNameField vecData;
	pChart->GetRowColDataFields(qslRowFields, qslColFields, vecData);
	for (size_t i = 0; i < vecData.size(); ++i)
		qslDataFields.push_back(vecData[i].first);
}

void KRecommendUnderstand::Init(QJsonArray& jsonObj)
{
	m_bReady = TRUE;
	if (jsonObj.isEmpty())
		return;
	for (int i = 0; i < jsonObj.size(); ++i)
	{
		QJsonObject jsonReport = jsonObj[i].toObject();
		KRecommendChart* pChart = new KRecommendChart(i, m_spSheet, jsonReport);
		if (pChart)
			this->push_back(pChart);
	}
}

// ----------------------------------------------------------------------

KSmartAnalysis::KSmartAnalysis(IKWorksheet* pSheet)
	: m_spActiveSheet(pSheet), m_pAnalysis(NULL), m_bCleanTable(FALSE), m_bSelectPivotTable(FALSE)
	, m_pActivePivotTable(NULL), m_bNewSheet(FALSE), m_bInsertChart(FALSE), m_bNewActive(FALSE)
	, m_nCleanTableCount(0), m_error(JsonDataReceive), m_bDisplayFieldMap(FALSE), m_bIsFirstSource(TRUE), m_bIsFirstDispalyFieldMap(TRUE)
	, m_bIsStop(FALSE), m_bFieldListOpen(FALSE), m_bIsNotRequestUnderstand(FALSE), m_pFieldMapPivotTable(nullptr)
{
	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_spActiveSheet->GetWorkbook();
	_applogic_CreateObject(CLSID_KPivotTableTool, IID_IET_PivotTableTool, (void**)&m_spPivotTableTool);
	if (m_spPivotTableTool)
		m_spPivotTableTool->Init(spWorkbook);

	Init();
}

KSmartAnalysis::~KSmartAnalysis()
{
	clearListenner();
	m_pActivePivotTable = NULL;
	if (m_spOldActiveSheet)
	{
		m_spOldActiveSheet->Activate(FALSE);
		m_spOldActiveSheet.clear();
	}
	if (m_pFieldMapPivotTable)
	{
		ks_stdptr<etoldapi::_Worksheet> spApiWorksheet = m_pFieldMapPivotTable->GetWorksheet();
		if (spApiWorksheet /*&& !KAietHelper::isEditingEt()*/)
			spApiWorksheet->Delete(FALSE);
		m_pFieldMapPivotTable = nullptr;
	}
	if (m_pAnalysis)
	{
		delete m_pAnalysis;
		m_pAnalysis = NULL;
	}
}

void KSmartAnalysis::InitPivotTable(etoldapi::PivotTable* pPivotTable)
{
	if (!pPivotTable || !m_spActiveSheet)
		return;
	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_spActiveSheet->GetWorkbook();
	if (!spWorkbook)
		return;
	ks_stdptr<pivot_core::IPivotTable> spCorePivotTable = m_spPivotTableTool->GetCoreTable(pPivotTable);
	if (!spCorePivotTable)
		return;
	ks_stdptr<pivot_core::IPivotCache> spPivotCache = spCorePivotTable->GetPivotCache();
	if (!spPivotCache)
		return;
	ks_stdptr<pivot_core::ISrcDescription> spSrcDescription = spPivotCache->GetSrcDes();
	if (!spSrcDescription)
		return;
	pivot_core::PivotCacheSourceType srcType = spSrcDescription->GetSourceType();
	if (pivot_core::PCST_Worksheet != srcType)
		return;
	ks_bstr bstrBookSheet, bstrCrossBookName;
	BOOL isCrossBook = false, isNameRef = false, isAbsPath = false, isMissing = false;
	spSrcDescription->GetBookSheetName(NULL, &bstrBookSheet, &bstrCrossBookName, &isCrossBook, &isNameRef, &isAbsPath, &isMissing);
	if (isCrossBook || isNameRef || isAbsPath || isMissing || !bstrCrossBookName.empty() || bstrBookSheet.empty())
		return;
	RANGE rg(m_spActiveSheet->GetSheet()->GetBMP());
	spSrcDescription->GetSourceData(&rg);
	if (rg.SheetFrom() != rg.SheetTo() || rg.RowFrom() == rg.RowTo())
		return;
	ks_stdptr<IKWorksheets> spSheets = spWorkbook->GetWorksheets();
	m_spSrcSheet = spSheets->GetSheetItem(rg.SheetFrom());

	ks_stdptr<IKRanges> spRgs;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRgs);
	spRgs->Append(alg::STREF_THIS_BOOK, rg);
	m_spSrcSheet->GetRangeByData(spRgs, &m_spSrcRange);

	m_spSelectPivotTable = pPivotTable;
	m_bSelectPivotTable = TRUE;
}

void GetSelectionRange(IKWorksheet* pSheet, RANGE& rg)
{
	ks_stdptr<IKWorksheetView> spView = pSheet->GetActiveWorksheetView();
	if (!spView)
		return;
	range_helper::ranges rgs;
	spView->GetSelectionRange(&rgs);
	if (!rgs || rgs.size() == 0)
		return;
	UINT nCount = 0;
	rgs->GetCount(&nCount);
	if (nCount <= 0)
		return;
	const RANGE* pRg = rgs.at(0).second;
	if (!pRg)
		return;
	rg = *pRg;
}

bool GetPivotTableBySelection(IKWorksheet* pSheet, etoldapi::PivotTable** ppPivotTable)
{
	if (!pSheet || !ppPivotTable)
		return false;
	ks_stdptr<etoldapi::_Worksheet> spWorksheet = pSheet;
	ks_stdptr<etoldapi::PivotTables> spPivotTables;
	KComVariant var;
	HRESULT hr = spWorksheet->PivotTables(var, 0, (IKCoreObject**)(&spPivotTables));
	if (FAILED(hr) || !spPivotTables)
		return false;
	long nCnt = 0;
	hr = spPivotTables->get_Count(&nCnt);
	if (FAILED(hr) || nCnt <= 0)
		return false;
	if (1 == nCnt)
	{
		ks_stdptr<etoldapi::PivotTable> spPivot;
		KComVariant var1(1, VT_I4);
		hr = spPivotTables->Item(var1, &spPivot);
		if (SUCCEEDED(hr) && spPivot)
		{
			*ppPivotTable = spPivot.detach();
			return true;
		}
	}
	else
	{
		// 获取选区
		RANGE rgSelection(pSheet->GetSheet()->GetBMP());
		GetSelectionRange(pSheet, rgSelection);
		for (int i = 1; i <= nCnt; ++i)
		{
			ks_stdptr<etoldapi::PivotTable> spPivot;
			KComVariant var1(i, VT_I4);
			hr = spPivotTables->Item(var1, &spPivot);
			if (SUCCEEDED(hr) && spPivot)
			{
				ks_stdptr<etoldapi::Range> spTable;
				spPivot->get_TableRange2(&spTable);
				RANGE rgTable(pSheet->GetSheet()->GetBMP());
				IdentifyTool::GetTableRange(spTable, &rgTable);
				if (rgTable.Contain(rgSelection))
				{
					*ppPivotTable = spPivot.detach();
					return true;
				}
			}
		}
	}
	return false;
}

void KSmartAnalysis::Init()
{
	ks_stdptr<etoldapi::PivotTable> spPivotTable;
	bool b = GetPivotTableBySelection(m_spActiveSheet, &spPivotTable);
	if (b && spPivotTable)
		InitPivotTable(spPivotTable);
}

BOOL KSmartAnalysis::GetIdentifyData(QJsonObject& jsonObj)
{
	if (m_bSelectPivotTable)
	{
		IKWorksheet* pWorksheet = m_spSrcSheet;
		KIdentifyPivotTableSource identify(pWorksheet, m_spSrcRange);
		identify.Identify(jsonObj, m_spListener);
		return TRUE;
	}
	IKWorksheet* pWorksheet = m_spActiveSheet;
	KIdentifyTable identify(pWorksheet, JsonDataAnalysis);
	identify.Identify(jsonObj, m_spListener);
	return FALSE;
}

BOOL IsTableHidden(IKWorksheet* pSheet, const RANGE& rg)
{
	ks_stdptr<etoldapi::Range> spRange;
	HRESULT hr = CreateRange(pSheet, rg, &spRange);
	if (FAILED(hr) || !spRange)
		return false;
	ks_stdptr<etoldapi::Range> spRows;
	hr = spRange->get_Rows(&spRows);
	if (SUCCEEDED(hr) && spRows)
	{
		KComVariant varHidden;
		spRows->get_Hidden(&varHidden);
		if (KSmartParam(varHidden).GetBooleanValue())
			return TRUE;
	}
	ks_stdptr<etoldapi::Range> spCols;
	hr = spRange->get_Columns(&spCols);
	if (SUCCEEDED(hr) && spCols)
	{
		KComVariant varHidden;
		spCols->get_Hidden(&varHidden);
		if (KSmartParam(varHidden).GetBooleanValue())
			return TRUE;
	}
	return FALSE;
}

int KSmartAnalysis::GetSelectTableJsonIdx(QJsonArray& jsonArray)
{
	if (jsonArray.empty())
		return -1;
	if (jsonArray.size() == 1 || m_bSelectPivotTable || !m_spActiveSheet || !m_spActiveSheet->GetSheet())
		return 0;

	ks_stdptr<IKWorksheetView> spView = m_spActiveSheet->GetActiveWorksheetView();
	if (!spView)
		return 0;
	range_helper::ranges rgs;
	spView->GetSelectionRange(&rgs);
	if (!rgs || rgs.size() == 0)
		return 0;
	UINT nCount = 0;
	rgs->GetCount(&nCount);
	if (nCount <= 0)
		return 0;
	const RANGE* pRg = rgs.at(0).second;
	if (!pRg)
		return 0;
	int iNotHidden = -1;
	RANGE rgTable(m_spActiveSheet->GetSheet()->GetBMP());
	IDX iSheet = 0;
	m_spActiveSheet->GetSheet()->GetIndex(&iSheet);
	rgTable.SetSheetFromTo(iSheet);
	for (int i = 0; i < jsonArray.size(); ++i)
	{
		if (!jsonArray[i].isObject())
			continue;
		QJsonObject jsonTable = jsonArray[i].toObject();
		if (!jsonTable.contains("cleanTable") || !jsonTable["cleanTable"].isObject() 
			|| !jsonTable.contains("columnInfoList") || !jsonTable["columnInfoList"].isArray())
			continue;
		QJsonArray jsonColInfoList = jsonTable["columnInfoList"].toArray();
		if (jsonColInfoList.isEmpty())
			continue;
		QJsonObject jsonCleanTable = jsonTable["cleanTable"].toObject();
		if (!jsonCleanTable.contains("tableRange") || !jsonCleanTable["tableRange"].isArray())
			continue;
		QJsonArray jsonRange = jsonCleanTable["tableRange"].toArray();
		if (jsonRange.size() != 4)
			continue;
		int r1 = jsonRange.at(0).toInt(), r2 = jsonRange.at(1).toInt(), c1 = jsonRange.at(2).toInt(), c2 = jsonRange.at(3).toInt();
		if (pRg->RowFrom() >= r1 && pRg->RowFrom() <= r2 && pRg->RowTo() >= r1 && pRg->RowTo() <= r2
			&& pRg->ColFrom() >= c1 && pRg->ColFrom() <= c2 && pRg->ColTo() >= c1 && pRg->ColTo() <= c2)
			return i;
		if (iNotHidden < 0)
		{
			rgTable.SetRowFromTo(r1, r2);
			rgTable.SetColFromTo(c1, c2);
			if (!IsTableHidden(m_spActiveSheet, rgTable))
				iNotHidden = i;
		}
	}
	return iNotHidden >= 0 ? iNotHidden : 0;
}

HRESULT KSmartAnalysis::GetPivotTableSourceRange(etoldapi::PivotTable* pPivotTable, RANGE& rg)
{
	if (!pPivotTable)
		return E_FAIL;
	ks_stdptr<etoldapi::PivotCache> spPivotCache;
	HRESULT hr = pPivotTable->PivotCache(&spPivotCache);
	if (FAILED(hr) || !spPivotCache)
		return E_FAIL;
	ks_stdptr<pivot_core::IPivotCache> spCache = m_spPivotTableTool->GetCoreCache(spPivotCache);
	if (!spCache)
		return E_FAIL;
	ks_stdptr<pivot_core::ISrcDescription> spSrcDes = spCache->GetSrcDes();
	if (!spSrcDes)
		return E_FAIL;
	spSrcDes->GetSourceData(&rg);
	return S_OK;
}

int KSmartAnalysis::PivotTableListByRange(const RANGE* pTableRange)
{
	// 根据文件中数据透视表收集
	HRESULT hr = S_OK;
	int nAllPTCnt = 0;
	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_spActiveSheet->GetWorkbook();
	ks_stdptr<etoldapi::Worksheets> spWorksheets = spWorkbook->GetWorksheets();
	long nSheet = 0;
	spWorksheets->get_Count(&nSheet);
	for (long i = 1; i <= nSheet; ++i)
	{
		ks_stdptr<etoldapi::_Worksheet> spSheet;
		KComVariant varIdx(i, VT_I4);
		hr = spWorksheets->get_Item(varIdx, (IKCoreObject**)(&spSheet));
		if (FAILED(hr) || !spSheet)
			continue;
		ks_stdptr<IKWorksheet> spWorksheet = spSheet;
		ks_stdptr<etoldapi::PivotTable> spPivotTable;
		bool b = GetPivotTableBySelection(spWorksheet, &spPivotTable);
		if (b && spPivotTable)
		{
			RANGE rg(spWorkbook->GetBook()->GetBMP());
			hr = GetPivotTableSourceRange(spPivotTable, rg);
			if (FAILED(hr))
				continue;
			if (rg != *pTableRange)
				continue;
			m_pAnalysis->AddUserPivotTable(spPivotTable);
			nAllPTCnt++;
		}
	}
	return nAllPTCnt;
}

// 返回是否因行列数不同，需要清洗
BOOL KSmartAnalysis::NeedCleanRealTableRange(QJsonObject jsonCleanTable, RANGE& rg)
{
	if (!jsonCleanTable.contains("columnList") || !jsonCleanTable["columnList"].isArray() || jsonCleanTable["columnList"].toArray().isEmpty())
		return TRUE;
	QJsonArray jsonColList = jsonCleanTable["columnList"].toArray();
	int nRowStart = 0, nRowEnd = 0, nColStart = 0, nColEnd = 0;
	QJsonObject jsonColFirst = jsonColList.first().toObject();
	QJsonObject jsonColLast = jsonColList.last().toObject();
	if (!jsonColFirst.contains("cellList") || !jsonColFirst["cellList"].isArray() || jsonColFirst["cellList"].toArray().isEmpty()
		|| !jsonColLast.contains("cellList") || !jsonColLast["cellList"].isArray() || jsonColLast["cellList"].toArray().isEmpty())
		return TRUE;
	QJsonObject jsonCellLT = jsonColFirst["cellList"].toArray().first().toObject();
	QJsonObject jsonCellRB = jsonColLast["cellList"].toArray().last().toObject();
	if (!jsonCellLT.contains("pos") || !jsonCellLT["pos"].isArray() || jsonCellLT["pos"].toArray().size() != 2
		|| !jsonCellRB.contains("pos") || !jsonCellRB["pos"].isArray() || jsonCellRB["pos"].toArray().size() != 2)
		return TRUE;
	QJsonArray jsonPosLT = jsonCellLT["pos"].toArray();
	nRowStart = jsonPosLT[0].toInt();
	nColStart = jsonPosLT[1].toInt();
	QJsonArray jsonPosRB = jsonCellRB["pos"].toArray();
	nRowEnd = jsonPosRB[0].toInt();
	nColEnd = jsonPosRB[1].toInt();

	if (jsonColList.size() != nColEnd - nColStart + 1
		|| jsonColFirst["cellList"].toArray().size() != nRowEnd - nRowStart + 1)
		return TRUE;

	// 无行标题
	if (nRowStart <= 0)
		return TRUE;
	if (!jsonCleanTable.contains("tableRange") || !jsonCleanTable["tableRange"].isArray())
		return TRUE;
	QJsonArray jsonRange = jsonCleanTable["tableRange"].toArray();
	if (jsonRange.size() != 4)
		return TRUE;
	if (nRowStart - 1 != jsonRange.at(0).toInt())
		return TRUE;

	IDX iSheet = 0;
	m_spActiveSheet->GetSheet()->GetIndex(&iSheet);
	rg.SetSheetFromTo(iSheet, iSheet);
	rg.SetRowFromTo(nRowStart - 1, nRowEnd);
	rg.SetColFromTo(nColStart, nColEnd);
	return FALSE;
}

BOOL IsSameAutoFilterRange(IKWorksheet* pSheet, etoldapi::Range* pRange)
{
	// if (!pSheet || !pRange)
	// 	return FALSE;

	// ks_stdptr<etoldapi::_Workbook> spWorkbook = pSheet->GetWorkbook();
	// ks_stdptr<IKTransactionTool> spTransTool = spWorkbook->GetTransactionTool();
	// spTransTool->StartTrans();

	BOOL isSame = FALSE;
	// ks_stdptr<IKAutoFilter> spAutoFilter;
	// KComVariant var;
	// ks_stdptr<etoldapi::ListObject> spListObject;
	// HRESULT hr = pRange->get_ListObject(&spListObject);
	// if (SUCCEEDED(hr) && spListObject)
	// {
	// 	hr = spListObject->put_ShowAutoFilter(VARIANT_TRUE);
	// 	ks_stdptr<etoldapi::AutoFilter> spApiAutoFilter;
	// 	hr = spListObject->get_AutoFilter(&spApiAutoFilter);
	// 	if (SUCCEEDED(hr) && spApiAutoFilter)
	// 	{
	// 		ks_stdptr<IAutoFilterInfo> spAutoFilterInfo = spApiAutoFilter;
	// 		hr = spAutoFilterInfo->GetCoreAutoFilter(&spAutoFilter);
	// 	}
	// }
	// else
	// {
	// 	spAutoFilter = pSheet->GetCoreAutoFilter();
	// 	if (!spAutoFilter)
	// 	{
	// 		KComVariant varAutoFilter;
	// 		hr = pRange->AutoFilter(var, var, oldapi::etAnd, var, var, &varAutoFilter);
	// 		if (SUCCEEDED(hr))
	// 			spAutoFilter = pSheet->GetCoreAutoFilter();
	// 	}
	// }
	// if (spAutoFilter)
	// {
	// 	RANGE rgFilter(pSheet->GetSheet()->GetBMP());
	// 	spAutoFilter->GetFilterRange(&rgFilter);
	// 	RANGE rg(pSheet->GetSheet()->GetBMP());
	// 	IdentifyTool::GetTableRange(pRange, &rg);
	// 	if (rg.Compare(rgFilter))
	// 		isSame = TRUE;
	// }

	// spTransTool->Rollback();

	return isSame;
}

BOOL KSmartAnalysis::NeedCleanTable(QJsonObject& jsonTable, RANGE& rg, etoldapi::Range** ppRange)
{
	if (!jsonTable.contains("cleanTable") || !jsonTable["cleanTable"].isObject())
		return FALSE;

	// 有校对错误，需要清洗
	m_nCleanEnum = 1;
	QJsonObject jsonCleanTable = jsonTable["cleanTable"].toObject();
	if (!jsonCleanTable.contains("proofErrorCount"))
		return TRUE;
	int nCnt = jsonCleanTable["proofErrorCount"].toInt();
	if (nCnt > 0)
		return TRUE;

	// 清洗后的行列数与原区域行列数不一致，需要清洗
	m_nCleanEnum = 2;
	BOOL needClean = NeedCleanRealTableRange(jsonCleanTable, rg);
	if (needClean)
		return TRUE;

	// 有合并单元格，需要清洗
	m_nCleanEnum = 3;
	if (!ppRange || !m_spActiveSheet)
		return TRUE;
	HRESULT hr = S_OK;
	ks_stdptr<IKRanges> spRgs;
	hr = _etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRgs);
	if (FAILED(hr) || !spRgs)
		return TRUE;
	hr = spRgs->Append(alg::STREF_THIS_BOOK, rg);
	if (FAILED(hr))
		return TRUE;
	ks_stdptr<etoldapi::Range> spRange;
	hr = m_spActiveSheet->GetRangeByData(spRgs, &spRange);
	if (FAILED(hr) || !spRange)
		return TRUE;
	KComVariant varMerge;
	hr = spRange->get_MergeCells(&varMerge);
	if (FAILED(hr) || V_VT(&varMerge) != VT_BOOL || V_BOOL(&varMerge) != VARIANT_FALSE)
		return TRUE;

	// 检查区域内是否有数据透视表，如果有可能是源数据被删除的数据透视表，需要插入清洗数据
	m_nCleanEnum = 4;
	ks_stdptr<etoldapi::PivotTable> spPivotTable;
	hr = spRange->get_PivotTable(&spPivotTable);
	if (SUCCEEDED(hr) && spPivotTable)
		return TRUE;

	// 行标题有空白，需要清洗
	m_nCleanEnum = 5;
	int nColCnt = rg.ColTo() - rg.ColFrom() + 1;
	for (int iCol = 1; iCol <= nColCnt; ++iCol)
	{
		QString strContent = GetCellContent(spRange, 1, iCol);
		if (strContent.isEmpty())
			return TRUE;
	}

	// 如果自动筛选区域与表格区域不同，需要插入清洗数据，否则字段信息通过筛选获取到的将会对不上
	m_nCleanEnum = 6;
	if (!IsSameAutoFilterRange(m_spActiveSheet, spRange))
		return TRUE;

	m_nCleanEnum = 0;
	*ppRange = spRange;
	spRange->AddRef();
	return FALSE;
}

// 接收服务端返回的Json数据
HRESULT KSmartAnalysis::ParseJsonObj(QJsonArray& jsonObj)
{
	if (m_pAnalysis)
		return E_FAIL;
	if (m_spActiveSheet == nullptr || m_spActiveSheet->IsDestroyed())
		return E_FAIL;
	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_spActiveSheet->GetWorkbook();
	if (spWorkbook == nullptr)
		return E_FAIL;
	m_pAnalysis = new KAnalysisTable(spWorkbook);
	if (!m_pAnalysis || jsonObj.isEmpty())
		return E_FAIL;

	m_nCleanTableCount = jsonObj.size();
	int idxTable = 0;
	if (jsonObj.size() > 1 && !m_bSelectPivotTable)
		idxTable = GetSelectTableJsonIdx(jsonObj);
	QJsonObject jsonTable = jsonObj[idxTable].toObject();

	RANGE rg(spWorkbook->GetBook()->GetBMP());
	if (m_bSelectPivotTable)
	{
		// 是数据透视表，则直接使用数据透视表源
		m_pAnalysis->SetSource(m_spSrcSheet, m_spSrcRange);
		m_spSrcSheet->GetUsedRange(&rg);
		IdentifyTool::GetTableRange(m_spSrcRange, &rg);
		// 查找同源的数据透视表
		PivotTableListByRange(&rg);
	}
	else
	{
		// 普通表格，需先判断是否需要清洗
		ks_stdptr<etoldapi::Range> spRange;
		BOOL bClean = NeedCleanTable(jsonTable, rg, &spRange);
		if (bClean || !spRange)
		{
			IKUilMainWindow* pUilMainWnd = nullptr;
			IKMainWindow* pActiveMainWindow = spWorkbook->GetApplication()->GetActiveMainWindow();
			if (pActiveMainWindow)
				pUilMainWnd = pActiveMainWindow->GetUilMainWindow();
			ks_castptr<IEditApplication> cpEditApp;
			if (pUilMainWnd)
				cpEditApp = pUilMainWnd->GetUilObject(UMW_EditApplication);
			if (cpEditApp && cpEditApp->IsEditing())
				cpEditApp->ExitEdit(TRUE);
			// 插入清洗表格
			IDX iSheet = 0;
			if (m_spActiveSheet->GetSheet())
				m_spActiveSheet->GetSheet()->GetIndex(&iSheet);
			ks_stdptr<etoldapi::_Worksheet> spSheet = m_spActiveSheet;
			ks_bstr bstr;
			spSheet->get_Name(&bstr);
			QString strName = krt::fromUtf16(bstr.c_str());
			HRESULT hr = E_FAIL;
			ks_stdptr<_Workbook> spWorkbook = spSheet->GetWorkbook();
			ks_stdptr<IKTransactionTool> spTrans = spWorkbook->GetTransactionTool();
			spTrans->StartTrans();
			{
				ks_stdptr<IKWorkbook> spBook = spWorkbook;
				m_bNewSheet = TRUE;
				app_helper::KCalculateBatch bacth(spBook->GetBook()->LeakOperator());
				hr = m_pAnalysis->InsertTable(iSheet, strName, jsonTable, m_spListener);
			}
			if (SUCCEEDED(hr))
				spTrans->CommitTrans(nullptr, ksoCommit, FALSE);
			else
			{
				spTrans->Rollback();
				return hr;
			}
			if (m_bIsStop)
				return E_FAIL;
			m_spSrcSheet = m_pAnalysis->GetSourceSheet();
			m_spSrcRange = m_pAnalysis->GetSourceRange();
			m_bCleanTable = TRUE;
		}
		else
		{
			m_spSrcSheet = m_spActiveSheet;
			m_spSrcRange = spRange;
			m_pAnalysis->SetSource(m_spSrcSheet, m_spSrcRange);
			// 查找同源的数据透视表
			PivotTableListByRange(&rg);
		}
	}

	if (m_bSelectPivotTable && m_pAnalysis->GetAnalysisPivotTableCount() > 0)
	{
		// 无需新建，将当前选择的数据透视表作为当前分析
		SelectAnalysis(m_spActiveSheet);
	}

	m_pAnalysis->InitAllDataField(jsonTable);

	ks_stdptr<IKApplication> spApp = spWorkbook->GetApplication();
	ks_stdptr<IKEtApplication> spEtApp = spApp;
	spEtApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(spWorkbook);

	return S_OK;
}

// 是否插入清洗数据
BOOL KSmartAnalysis::IsInsertCleanTable(int* pEnum)
{
	if (m_bCleanTable && pEnum)
		*pEnum = m_nCleanEnum;
	return m_bCleanTable;
}

// 是否选择数据透视表
BOOL KSmartAnalysis::IsSelectionPivotTable()
{
	return m_bSelectPivotTable;
}

// 是否有分析数据透视表
BOOL KSmartAnalysis::HaveAnalysisPivotTable()
{
	return m_pActivePivotTable ? TRUE : FALSE;
}

// 切换当前选择的分析
HRESULT KSmartAnalysis::SelectAnalysis(IKWorksheet* pWorksheet)
{
	if (!m_pAnalysis || !pWorksheet)
		return S_FALSE;
	
	IDX idxSel = 0, idxSrc = -1;
	if (pWorksheet->GetSheet())
		pWorksheet->GetSheet()->GetIndex(&idxSel);
	if (m_spSrcSheet && m_spSrcSheet->GetSheet())
		m_spSrcSheet->GetSheet()->GetIndex(&idxSrc);
	if (idxSel == idxSrc || idxSrc < 0)	// 切换到了数据源sheet
		return S_OK;
	int nCnt = m_pAnalysis->GetAnalysisPivotTableCount();
	for (int i = 0; i < nCnt; ++i)
	{
		KAnalysisPivotTable* pPivotTable = m_pAnalysis->GetAnalysisPivotTable(i);
		if (!pPivotTable)
			continue;
		if (pPivotTable->GetWorksheet() == pWorksheet)
		{
			m_pActivePivotTable = pPivotTable;
			m_bNewActive = TRUE;
			return S_OK;
		}
	}
	return E_FAIL;
}

// 添加空白的分析
HRESULT KSmartAnalysis::NewAnalysis()
{
	if (!m_pAnalysis)
		return S_FALSE;
	m_bNewSheet = TRUE;
	HRESULT hr = m_pAnalysis->InsertPivotTable();
	if (FAILED(hr))
		return hr;
	int nCount = m_pAnalysis->GetAnalysisPivotTableCount();
	if (nCount > 0)
	{
		m_pActivePivotTable = m_pAnalysis->GetAnalysisPivotTable(nCount - 1);
		m_bNewActive = TRUE;
	}

	if (m_spSrcSheet)
	{
		ks_stdptr<IKApplication> spApp = m_spSrcSheet->GetApplication();
		ks_stdptr<IKEtApplication> spEtApp = spApp;
		spEtApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_spSrcSheet->GetWorkbook());
	}
	return S_OK;
}

// 字段顺序：按推荐值从大到小，再按 维度5个、度量3个 间隔排序
void SortAllFieldList(std::vector<KDataField*>& allFieldList)
{
	std::sort(allFieldList.begin(), allFieldList.end(), morePredValue);

	std::vector<KDataField*> strFieldList;
	std::vector<KDataField*> numFieldList;
	for (size_t i = 0; i < allFieldList.size(); ++i)
	{
		KDataField* pField = allFieldList[i];
		if (pField && pField->IsNumberField())
			numFieldList.push_back(pField);
		else if (pField)
			strFieldList.push_back(pField);
	}

	allFieldList.clear();
	size_t iStr = 0, iNum = 0;
	while (iStr + NotNumberFieldCount < strFieldList.size() && iNum < numFieldList.size())
	{
		for (int i = 0; i < NotNumberFieldCount && iStr < strFieldList.size(); ++i, ++iStr)
			allFieldList.push_back(strFieldList[iStr]);
		for (int i = 0; i < NumberFieldCount && iNum < numFieldList.size(); ++i, ++iNum)
			allFieldList.push_back(numFieldList[iNum]);
	}
	for (; iStr < strFieldList.size(); ++iStr)
		allFieldList.push_back(strFieldList[iStr]);
	for (; iNum < numFieldList.size(); ++iNum)
		allFieldList.push_back(numFieldList[iNum]);
}

// 获取字段列表
HRESULT KSmartAnalysis::GetFieldList(QJsonArray& jsonObj)
{
	if (!m_pAnalysis)
		return S_FALSE;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	std::vector<KDataField*> tempAllFieldList;
	for (auto it = allFieldList.begin(); it != allFieldList.end(); ++it)
		tempAllFieldList.push_back(*it);

	if (tempAllFieldList.size() > NotNumberFieldCount + NumberFieldCount)
		SortAllFieldList(tempAllFieldList);

	for (auto it = tempAllFieldList.cbegin(); it != tempAllFieldList.cend(); ++it)
	{
		KDataField* pField = *it;
		if (!pField)
			continue;

		QJsonObject jsonField;
		pField->ExportJson(jsonField, MaxFieldItemCount);	// 限制返回项个数最多50个
		jsonObj.append(jsonField);
	}
	m_bNewActive = FALSE;
	return S_OK;
}

// 获取某个字段完整信息
HRESULT KSmartAnalysis::GetField(QString strField, QJsonObject& jsonObj)
{
	if (!m_pAnalysis)
		return S_FALSE;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	const KDataField* pField = allFieldList.FindByName(strField);
	if (pField)
	{
		pField->ExportJson(jsonObj, -1);
		return S_OK;
	}
	return E_FAIL;
}

// 获取某个字段的类型信息
HRESULT KSmartAnalysis::GetFieldTypeInfo(QString strField, QString& strType, QString& strFieldMapType)
{
	if (!m_pAnalysis)
		return S_FALSE;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	const KDataField* pField = allFieldList.FindByName(strField);
	if (!pField)
		return E_FAIL;
	KFieldMap* pFieldMap = m_pAnalysis->GetFieldMap();
	if (!pFieldMap)
		return E_FAIL;
	strType = pField->GetTypeStr();
	strFieldMapType = pFieldMap->GetFieldType(strField);
	return S_OK;
}

// 获取使用到的字段列表
QStringList KSmartAnalysis::GetUsedFieldList()
{
	if (!m_pAnalysis || !m_pActivePivotTable)
		return QStringList();
	QStringList qslUsedField;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	for (auto it = allFieldList.begin(); it != allFieldList.end(); ++it)
	{
		KDataField* pField = *it;
		if (!pField)
			continue;

		QString strName = pField->GetName();
		if (m_pActivePivotTable->IsUsedField(strName))
			qslUsedField.push_back(strName);
	}
	return qslUsedField;
}

// 获取使用到的筛选器字段列表
QStringList KSmartAnalysis::GetUsedPageFieldList()
{
	if (!m_pAnalysis || !m_pActivePivotTable)
		return QStringList();
	QStringList qslUsedField;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	for (auto it = allFieldList.begin(); it != allFieldList.end(); ++it)
	{
		KDataField* pField = *it;
		if (!pField)
			continue;

		QString strName = pField->GetName();
		if (m_pActivePivotTable->IsUsedPageField(strName))
			qslUsedField.push_back(strName);
	}
	return qslUsedField;
}

// 获取当前分析的行字段、列字段、值字段名字列表
void KSmartAnalysis::GetPivotTableFields(QStringList& qslRowFields, QStringList& qslColFields, QStringList& qslDataFields)
{
	if (!m_pActivePivotTable)
		return;
	m_pActivePivotTable->GetPivotTableFields(qslRowFields, qslColFields, qslDataFields);
}

// 获取所有字段列表名字
QStringList KSmartAnalysis::GetAllFieldList()
{
	if (!m_pAnalysis)
		return QStringList();
	QStringList qslField;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	for (auto it = allFieldList.begin(); it != allFieldList.end(); ++it)
	{
		KDataField* pField = *it;
		if (!pField)
			continue;
		QString strName = pField->GetName();
		qslField.push_back(strName);
	}
	return qslField;
}

// 获取按添加顺序的使用到的字段列表，目前仅图谱使用
QStringList KSmartAnalysis::GetInsertFieldList()
{
	if (!m_pAnalysis || !m_pActivePivotTable)
		return QStringList();
	return m_pActivePivotTable->GetInsertFieldList();
}

void KSmartAnalysis::CheckSheetIsActive()
{
	IKWorksheet* pSheet = GetActiveAnalysisWorksheet();
	if (!pSheet)
		return;
	ks_stdptr<IKWorkbook> spBook = pSheet->GetWorkbook();
	ks_stdptr<IKWorksheet> spActiveSheet = spBook->GetActiveWorksheet();
	if (spActiveSheet != pSheet)
		pSheet->Activate(FALSE);
}

void KSmartAnalysis::clearListenner()
{
	if (m_spListener)
		m_spListener->stop();
	m_spListener.clear();
}

// 添加字段
HRESULT KSmartAnalysis::InsertField(QString strField, bool isPage, bool bSelect/* = true*/, bool bCleanUR/* = false*/)
{
	if (!m_pAnalysis)
		return S_FALSE;
	CheckSheetIsActive();
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	const KDataField* pField = allFieldList.FindByName(strField);
	if (!pField)
		return E_FAIL;
	if (!m_pActivePivotTable)
		NewAnalysis();
	if (!m_pActivePivotTable)
		return S_FALSE;

	ks_stdptr<IKWorksheet> spWorksheet = m_pActivePivotTable->GetWorksheet();
	ks_stdptr<_Workbook> spWorkbook = spWorksheet->GetWorkbook();
	ks_stdptr<IKTransactionTool> spTransTool = spWorkbook->GetTransactionTool();
	spTransTool->StartTrans();

	if (!m_bIsNotRequestUnderstand)
		m_bIsNotRequestUnderstand = TRUE;
	if (m_bDisplayFieldMap)
		bSelect = false;
	HRESULT hr = m_pActivePivotTable->InsertPivotTableField(pField, isPage, bSelect);

	if (m_bIsNotRequestUnderstand)
		m_bIsNotRequestUnderstand = FALSE;
	if (FAILED(hr) || hr == S_CONTINUE)
		spTransTool->Rollback();
	else
		spTransTool->CommitTrans(nullptr, ksoCommit, FALSE);
	if (bCleanUR)
	{
		ks_stdptr<IKApplication> spApp = spWorkbook->GetApplication();
		ks_stdptr<IKEtApplication> spEtApp = spApp;
		spEtApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(spWorkbook);
	}
	return hr;
}

// 删除字段
HRESULT KSmartAnalysis::DeleteField(QString strField, bool bSelect/* = true*/, bool bCleanUR/* = false*/)
{
	if (!m_pAnalysis)
		return S_FALSE;
	CheckSheetIsActive();
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	const KDataField* pField = allFieldList.FindByName(strField);
	if (!pField)
		return E_FAIL;
	if (!m_pActivePivotTable)
		return S_FALSE;

	ks_stdptr<IKWorksheet> spWorksheet = m_pActivePivotTable->GetWorksheet();
	ks_stdptr<_Workbook> spWorkbook = spWorksheet->GetWorkbook();
	ks_stdptr<IKTransactionTool> spTransTool = spWorkbook->GetTransactionTool();
	spTransTool->StartTrans();

	if (!m_bIsNotRequestUnderstand)
		m_bIsNotRequestUnderstand = TRUE;
	if (m_bDisplayFieldMap)
		bSelect = false;
	HRESULT hr = m_pActivePivotTable->DeletePivotTableField(pField, bSelect);

	if (m_bIsNotRequestUnderstand)
		m_bIsNotRequestUnderstand = FALSE;
	if (FAILED(hr))
		spTransTool->Rollback();
	else
		spTransTool->CommitTrans(nullptr, ksoCommit, FALSE);
	if (bCleanUR)
	{
		ks_stdptr<IKApplication> spApp = spWorkbook->GetApplication();
		ks_stdptr<IKEtApplication> spEtApp = spApp;
		spEtApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(spWorkbook);
	}
	return hr;
}

// 将数据透视表已使用字段清空后，加入需要的字段列表
HRESULT KSmartAnalysis::ReplaceFields(QStringList& qslFields, bool bSelect /* = true */, bool bCleanUR /* = false */)
{
	if (!m_pAnalysis)
		return S_FALSE;
	CheckSheetIsActive();
	if (!m_pActivePivotTable)
		return S_FALSE;

	VecKDataField vecFields;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	for (int i = 0; i < qslFields.size(); ++i)
	{
		const KDataField* pField = allFieldList.FindByName(qslFields[i]);
		if (!pField)
			continue;
		vecFields.push_back(pField);
	}
	HRESULT hr = m_pActivePivotTable->ReplacePivotTableFields(vecFields, bSelect);
	if (FAILED(hr))
		return hr;
	if (bCleanUR)
	{
		ks_stdptr<IKWorksheet> spWorksheet = m_pActivePivotTable->GetWorksheet();
		ks_stdptr<_Workbook> spWorkbook = spWorksheet->GetWorkbook();
		ks_stdptr<IKApplication> spApp = spWorkbook->GetApplication();
		ks_stdptr<IKEtApplication> spEtApp = spApp;
		spEtApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(spWorkbook);
	}
	return hr;
}

// 刷新数据透视表
HRESULT KSmartAnalysis::Refresh()
{
	return m_pActivePivotTable ? m_pActivePivotTable->Refresh() : S_FALSE;
}

oldapi::ETConsolidationFunction TransformNumFilterType(QString strItem)
{
	if (strItem.startsWith(staticStrSum))
		return oldapi::etSum;
	if (strItem.startsWith(staticStrAverage))
		return oldapi::etAverage;
	if (strItem.startsWith(staticStrMax))
		return oldapi::etMax;
	if (strItem.startsWith(staticStrMin))
		return oldapi::etMin;
	return oldapi::etSum;
}

// 获取某字段项的筛选信息（联动）
HRESULT KSmartAnalysis::GetFilterInfo(QString strField, QString strItem, QJsonArray& jsonObj)
{
	if (!m_pAnalysis)
		return S_FALSE;
	const KDataField* pField = m_pAnalysis->GetDataField(strField);
	if (!pField)
		return E_FAIL;
	HRESULT hr = S_OK;
	if (pField->GetType() == DataFieldTypeNumber)
	{
		if (m_pActivePivotTable && m_pActivePivotTable->IsUsedField(strField))
		{
			oldapi::ETConsolidationFunction type = TransformNumFilterType(strItem);
			hr = m_pActivePivotTable->SetDataFieldFunc(pField, type);
			if (FAILED(hr))
				return hr;
			hr = m_pAnalysis->GetFilterInfoNumber(strField, type);
		}
	}
	else
	{
		if (m_pAnalysis->GetSourceSheet())
		{
			ks_stdptr<_Workbook> spWorkbook = m_pAnalysis->GetSourceSheet()->GetWorkbook();
			ks_stdptr<IKTransactionTool> spTransTool = spWorkbook->GetTransactionTool();
			spTransTool->StartTrans();
			hr = m_pAnalysis->GetFilterInfo(pField, strItem, jsonObj);
			spTransTool->Rollback();
		}
	}
	return hr;
}

//字符串类型字段按计数个数排序
HRESULT KSmartAnalysis::SortStringFieldCount(QString strField, bool bDes, QJsonObject& jsonObj)
{
	if (!m_pAnalysis)
		return S_FALSE;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	KDataField* pField = allFieldList.FindByName(strField);
	if (pField && pField->GetType() != DataFieldTypeNumber)
	{
		KStringField* pStrField = (KStringField*)pField;
		pStrField->SortItemList(bDes);
	}
	if (pField)
	pField->ExportJson(jsonObj, MaxFieldItemCount);
	return S_OK;
}

// 重置字段列表选择的状态，包括字符串字段改为降序，日期字段改为按日显示且为降序
HRESULT KSmartAnalysis::resetState()
{
	if (!m_pAnalysis)
		return S_FALSE;
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	for (auto it = allFieldList.begin(); it != allFieldList.end(); ++it)
	{
		KDataField* pField = *it;
		if (pField)
			pField->ResetState();
	}
	return S_OK;
}
// 重新读取数据透视表使用到的行、列、值字段，用于更新使用到的字段，pReload为TRUE表示有变化，需要刷新
HRESULT KSmartAnalysis::ResetPivotTable(BOOL* pReload)
{
	if (!m_pActivePivotTable)
	{
		if (pReload)
		*pReload = TRUE;
		return S_FALSE;
	}
	if (ResetApiPivotTable())
	{
		if (pReload)
			*pReload = TRUE;
		return S_OK;
	}
	m_pActivePivotTable->Reset(pReload);
	if (m_bNewActive && pReload)
		*pReload = TRUE;
	m_bNewActive = FALSE;
	return S_OK;
}

// 获取当前分析的数据，用于给服务端进行解读
HRESULT KSmartAnalysis::GetActiveAnalysisData(QJsonObject& jsonObj)
{
	return m_pActivePivotTable ? m_pActivePivotTable->GetAnalysisData(jsonObj) : S_FALSE;
}

// 当前分析是否为空白分析
BOOL KSmartAnalysis::IsEmptyAnalysis()
{
	return m_pActivePivotTable ? m_pActivePivotTable->IsEmptyAnalysis() : TRUE;
}

// 当前分析是否有维度，即行字段或者列字段
BOOL KSmartAnalysis::HasDimension()
{
	return m_pActivePivotTable ? m_pActivePivotTable->HasDimension() : FALSE;
}

// 数据是否已准备好
BOOL KSmartAnalysis::IsReady()
{
	if (m_pAnalysis && !m_pAnalysis->GetAllDataField().empty())
		return TRUE;
	return FALSE;
}

// 获取字段个数
INT KSmartAnalysis::GetFieldCount()
{
	return m_pAnalysis ? m_pAnalysis->GetAllDataField().size() : 0;
}

// 获取源数据行数
INT KSmartAnalysis::GetFieldRowCount()
{
	return m_pAnalysis ? m_pAnalysis->GetFieldRowCount() : 0;
}

// 获取源数据非空单元格数
INT KSmartAnalysis::GetNotEmptyCellCount() const
{
	return m_pAnalysis ? m_pAnalysis->GetNotEmptyCellCount() : 0;
}

// 获取源数据sheet服务端返回的表格个数
INT KSmartAnalysis::GetCleanTableCount() const
{
	return m_nCleanTableCount;
}

// 获取字段图谱数据
HRESULT KSmartAnalysis::GetFieldMapJsonObj(QJsonObject& jsonObj)
{
	if (!m_pAnalysis)
		return S_FALSE;
	KFieldMap* pFieldMap = m_pAnalysis->GetFieldMap();
	if (!pFieldMap)
		return S_FALSE;
	return pFieldMap->GetFieldMapJsonObj(jsonObj);
}

// 获取字段图谱某个字段详细信息
HRESULT KSmartAnalysis::GetFieldMapInfoJsonObj(QString strField, QJsonObject& jsonObj)
{
	if (!m_pAnalysis)
		return S_FALSE;
	KFieldMap* pFieldMap = m_pAnalysis->GetFieldMap();
	if (!pFieldMap)
		return S_FALSE;
	return pFieldMap->GetFieldMapInfoJsonObj(strField, jsonObj);
}

// 进入图谱新建图谱专用的分析工作表
HRESULT KSmartAnalysis::InsertFieldMapPivotTable()
{
	if (!m_spSrcSheet || !m_pAnalysis)
		return S_FALSE;
	HRESULT hr = S_OK;
	ks_stdptr<IKEtApplication> spEtApp = m_spSrcSheet->GetApplication();
	m_spOldActiveSheet = spEtApp->GetActiveWorksheet();
	if (!m_pFieldMapPivotTable)
	{
		hr = NewAnalysis();
		if (SUCCEEDED(hr))
			m_pFieldMapPivotTable = m_pActivePivotTable;
	}
	return hr;
}

// 退出图谱删除图谱专用的分析工作表
HRESULT KSmartAnalysis::DeleteFieldMapPivotTable()
{
	if (!m_pFieldMapPivotTable)
		return S_FALSE;
	if (m_spOldActiveSheet)
	{
		m_spOldActiveSheet->Activate(FALSE);
		m_spOldActiveSheet.clear();
	}
	HRESULT hr = S_OK;
	ks_stdptr<etoldapi::_Worksheet> spApiWorksheet = m_pFieldMapPivotTable->GetWorksheet();
	if (spApiWorksheet)
	{
		UpdateOnDeleteSheet(spApiWorksheet);
		hr = spApiWorksheet->Delete(FALSE);
	}
	m_pFieldMapPivotTable = nullptr;
	return hr;
}

// 获取图谱工作表
IKWorksheet* KSmartAnalysis::GetFieldMapWorksheet(bool bDel/* = false*/)
{
	if (!m_pFieldMapPivotTable)
		return nullptr;
	IKWorksheet* pWorksheet = m_pFieldMapPivotTable->GetWorksheet();
	if (bDel)
	{
		UpdateOnDeleteSheet(pWorksheet);
		m_pFieldMapPivotTable = nullptr;
	}
	return pWorksheet;
}

// 获取当前分析的Worksheet
IKWorksheet* KSmartAnalysis::GetActiveAnalysisWorksheet()
{
	if (!m_pActivePivotTable)
		return NULL;
	return m_pActivePivotTable->GetWorksheet();
}

// 获取源数据的Worksheet
IKWorksheet* KSmartAnalysis::GetSourceWorksheet()
{

	return m_pAnalysis ? m_pAnalysis->GetSourceSheet() : *&m_spSrcSheet;
}

// 获取创建智能分析的Worksheet
IKWorksheet* KSmartAnalysis::GetNewAnalysisWorksheet()
{
	return m_spActiveSheet;
}

// 当前分析是否标脏
BOOL KSmartAnalysis::IsActiveAnalysisDirty()
{
	if (!m_spPivotTableTool || !m_pActivePivotTable)
		return FALSE;
	ks_stdptr<etoldapi::PivotTable> spPivotTable = m_pActivePivotTable->GetApiPivotTable();
	if (!spPivotTable)
		return FALSE;
	ks_stdptr<pivot_core::IPivotTable> spCorePivotTable = m_spPivotTableTool->GetCoreTable(spPivotTable);
	if (!spCorePivotTable)
		return FALSE;
	return spCorePivotTable->IsDirty(pivot_core::TDM_End);
}

// 设置当前分析标脏
HRESULT KSmartAnalysis::SetActiveAnalysisDirty()
{
	if (!m_spPivotTableTool || !m_pActivePivotTable)
		return E_FAIL;
	ks_stdptr<etoldapi::PivotTable> spPivotTable = m_pActivePivotTable->GetApiPivotTable();
	if (!spPivotTable)
		return E_FAIL;
	ks_stdptr<pivot_core::IPivotTable> spCorePivotTable = m_spPivotTableTool->GetCoreTable(spPivotTable);
	if (!spCorePivotTable)
		return E_FAIL;
	spCorePivotTable->SetDirtyFalse(pivot_core::TDM_End);
	return S_OK;
}

// 当删除工作表时，检查更新我的分析列表和当前分析
HRESULT KSmartAnalysis::UpdateOnDeleteSheet(IKWorksheet* pDelSheet)
{
	if (m_pActivePivotTable)
	{
		ks_stdptr<IKWorksheet> spActiveAnalysis = GetActiveAnalysisWorksheet();
		if (spActiveAnalysis && spActiveAnalysis == pDelSheet)
			m_pActivePivotTable = NULL;
	}
	if (m_pFieldMapPivotTable)
	{
		ks_stdptr<IKWorksheet> spFieldMap = m_pFieldMapPivotTable->GetWorksheet();
		if (spFieldMap && spFieldMap == pDelSheet)
			m_pFieldMapPivotTable = nullptr;
	}
	if (m_pAnalysis)
	{
		m_pAnalysis->UpdateOnDeleteSheet(pDelSheet);
		if (!m_pActivePivotTable)
		{
			int n = m_pAnalysis->GetAnalysisPivotTableCount();
			if (n > 0)
			{
				m_pActivePivotTable = m_pAnalysis->GetAnalysisPivotTable(n - 1);
				m_bNewActive = TRUE;
			}
		}
	}
	if (m_spSrcSheet && m_spSrcSheet == pDelSheet)
	{
		m_spSrcSheet.clear();
		m_spSrcRange.clear();
	}
	return S_OK;
}

// 解读插入图表时调用记录状态
void KSmartAnalysis::SetInsertChart(BOOL b)
{
	m_bInsertChart = b;
}
BOOL KSmartAnalysis::GetInsertChart() const
{
	return m_bInsertChart;
}

BOOL KSmartAnalysis::GetIsNewSheet() const
{
	return m_bNewSheet;
}

void KSmartAnalysis::SetIsNewSheet(BOOL b)
{
	m_bNewSheet = b;
}

// 获取解读接口
ISmartUnderstand* KSmartAnalysis::GetUnderstand()
{
	return m_pActivePivotTable ? m_pActivePivotTable->GetUnderstand() : NULL;
}

// 清除当前分析的解读数据
void KSmartAnalysis::CleanUnderstand()
{
	if (m_pActivePivotTable)
		m_pActivePivotTable->CleanUnderstand();
}
// 是否已生成解读数据
BOOL KSmartAnalysis::HasUnderstand()
{
	return m_pActivePivotTable ? m_pActivePivotTable->HasUnderstand() : FALSE;
}

int KSmartAnalysis::GetPivotTableCount()
{
	return m_pAnalysis ? m_pAnalysis->GetAnalysisPivotTableCount() : 0;
}

IKWorksheet* KSmartAnalysis::GetPivotTableSheet(int i)
{
	KAnalysisPivotTable* pTable = m_pAnalysis->GetAnalysisPivotTable(i);
	if (pTable)
		return pTable->GetWorksheet();
	return NULL;
}

// 是否推荐排序
BOOL KSmartAnalysis::GetIsRecommend(bool isSource)
{
	if (isSource && m_pAnalysis)
		return m_pAnalysis->GetIsRecommend();
	if (!isSource && m_pActivePivotTable)
		return m_pActivePivotTable->GetIsRecommend();
	return TRUE;
}

void KSmartAnalysis::SetIsRecommend(BOOL isRecommend, bool isSource)
{
	if (isSource && m_pAnalysis)
		m_pAnalysis->SetIsRecommend(isRecommend);
	else if (!isSource && m_pActivePivotTable)
		m_pActivePivotTable->SetIsRecommend(isRecommend);
}

// 是否仅显示正在分析的字段
BOOL KSmartAnalysis::GetIsOnlyUsed()
{
	return m_pActivePivotTable ? m_pActivePivotTable->GetIsOnlyUsed() : FALSE;
}

void KSmartAnalysis::SetIsOnlyUsed(BOOL isOnlyUsed)
{
	if (m_pActivePivotTable)
		m_pActivePivotTable->SetIsOnlyUsed(isOnlyUsed);
}

// 设置查找的字段名称
QString KSmartAnalysis::GetFindField()
{
	return m_pActivePivotTable ? m_pActivePivotTable->GetFindField() : QString();
}

void KSmartAnalysis::SetFindField(QString strName)
{
	if (m_pActivePivotTable)
		m_pActivePivotTable->SetFindField(strName);
}

// 获取数据解析错误信息
KParseJsonErrorEnum KSmartAnalysis::GetParseJsonError()
{
	return m_error;
}

void KSmartAnalysis::SetParseJsonError(KParseJsonErrorEnum err)
{
	m_error = err;
}

// 获取使用的数值字段的数值计算类型
int KSmartAnalysis::GetUsedNumberFieldTypeList(std::vector<std::pair<QString, QString>>& vecNumFieldType)
{
	return m_pActivePivotTable ? m_pActivePivotTable->GetUsedNumberFieldTypeList(vecNumFieldType) : 0;
}

void KSmartAnalysis::addListener(ISmartAnalysisListener* listener)
{
	if (listener)
		m_spListener = listener;
	
}

void KSmartAnalysis::removeListener(ISmartAnalysisListener* listener)
{
	if (listener && listener == m_spListener)
		m_spListener.clear();
}

// 获取某字段项数
INT KSmartAnalysis::GetFieldItemCount(QString strName) const
{
	if (!m_pAnalysis)
		return 0;
	const KDataField* pField = m_pAnalysis->GetDataField(strName);
	if (!pField || pField->IsNumberField())
		return 0;
	const KStringField* pStrField = (const KStringField*)pField;
	return pStrField->GetItemCount();
}

BOOL KSmartAnalysis::GetFieldMapIsDisplay()
{
	return m_bDisplayFieldMap;
}

void KSmartAnalysis::SetFieldMapIsDisplay(BOOL b)
{
	m_bDisplayFieldMap = b;
}

// 是否首次打开数据源
void KSmartAnalysis::SetIsFirstSource(BOOL b)
{
	m_bIsFirstSource = b;
}

BOOL KSmartAnalysis::GetIsFirstSource() const
{
	return m_bIsFirstSource;
}

// 是否首次打开图谱
void KSmartAnalysis::SetIsFirstDispalyFieldMap(BOOL b)
{
	m_bIsFirstDispalyFieldMap = b;
}

BOOL KSmartAnalysis::GetIsFirstDispalyFieldMap() const
{
	return m_bIsFirstDispalyFieldMap;
}


void KSmartAnalysis::SetIsStop(BOOL b)
{
	m_bIsStop = b;
}

BOOL KSmartAnalysis::GetIsStop() const
{
	return m_bIsStop;
}

INT KSmartAnalysis::GetFieldPredValueIndex(QString strName) const
{
	if (!m_pAnalysis)
		return -1;
	
	KDataFieldList& allFieldList = m_pAnalysis->GetAllDataField();
	std::vector<KDataField*> vecFieldList(allFieldList);
	std::sort(vecFieldList.begin(), vecFieldList.end(), morePredValue);
	for (size_t i = 0; i < vecFieldList.size(); ++i)
		if (vecFieldList[i] && vecFieldList[i]->GetName() == strName)
			return (int)i;
	return -1;
}

BOOL KSmartAnalysis::GetFieldListIsOpen()
{
	return m_bFieldListOpen;
}

void KSmartAnalysis::SetFieldListIsOpen(BOOL b)
{
	m_bFieldListOpen = b;
}

bool KSmartAnalysis::FindPivotTable(etoldapi::PivotTable** ppPivotTable)
{
	if (!ppPivotTable)
		return false;
	ks_stdptr<IKWorksheet> spWorksheet = m_pActivePivotTable->GetWorksheet();
	if (!spWorksheet || spWorksheet->IsDestroyed())
		return false;
	ks_stdptr<etoldapi::_Worksheet> spSheet = spWorksheet;
	ISheet* pSheet = spSheet->GetSheet();
	if (!pSheet)
		return false;
	ks_stdptr<IKWorksheet> spSourceWorksheet = m_pAnalysis->GetSourceSheet();
	if (!spSourceWorksheet || spSourceWorksheet->IsDestroyed())
		return false;

	RANGE rgSrcRange(pSheet->GetBMP());
	ks_stdptr<etoldapi::Range> spSrcRange = m_pAnalysis->GetSourceRange();
	IdentifyTool::GetTableRange(spSrcRange, &rgSrcRange);
	// 先看看A3单元格是否有数据透视表，并且跟原表同源
	ks_stdptr<etoldapi::Range> spA3;
	HRESULT hr = CreateRange(spWorksheet, 2, 0, &spA3);
	if (SUCCEEDED(hr) && spA3)
	{
		ks_stdptr<etoldapi::PivotTable> spPivot;
		hr = spA3->get_PivotTable(&spPivot);
		if (SUCCEEDED(hr) && spPivot)
		{
			RANGE rgSrcRg(spSheet->GetSheet()->GetBMP());
			hr = GetPivotTableSourceRange(spPivot, rgSrcRg);
			if (SUCCEEDED(hr) && rgSrcRange == rgSrcRg)
			{
				*ppPivotTable = spPivot.detach();
				return true;
			}
		}
	}
	// 再看看工作表是否有数据透视表，并且跟原表同源
	KComVariant var;
	ks_stdptr<etoldapi::PivotTables> spPivotTables;
	hr = spSheet->PivotTables(var, 0, (IKCoreObject**)&spPivotTables);
	if (SUCCEEDED(hr) && spPivotTables)
	{
		long nCnt = 0;
		spPivotTables->get_Count(&nCnt);
		for (long i = 1; i <= nCnt; ++i)
		{
			ks_stdptr<etoldapi::PivotTable> spPivot;
			KComVariant varIdx(i, VT_I4);
			hr = spPivotTables->Item(varIdx, &spPivot);
			if (SUCCEEDED(hr) && spPivot)
			{
				RANGE rgSrcRg(spSheet->GetSheet()->GetBMP());
				hr = GetPivotTableSourceRange(spPivot, rgSrcRg);
				if (SUCCEEDED(hr) && rgSrcRange == rgSrcRg)
				{
					*ppPivotTable = spPivot.detach();
					return true;
				}
			}
		}
	}
	return false;
}

// 检查当前数据透视表对象是否有效，如无效则更新数据透视表对象
BOOL KSmartAnalysis::ResetApiPivotTable()
{
	if (!m_pAnalysis || !m_pActivePivotTable || !m_spPivotTableTool)
		return FALSE;
	ks_stdptr<etoldapi::PivotTable> spPivotTable = m_pActivePivotTable->GetApiPivotTable();
	if (spPivotTable)
	{
		ks_stdptr<pivot_core::IPivotTable> spCorePivotTable = m_spPivotTableTool->GetCoreTable(spPivotTable);
		if (spCorePivotTable && spCorePivotTable->IsValid())
		{
			ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_pActivePivotTable->GetWorksheet();
			ks_stdptr<pivot_core::IPivotTables> spPivotTables = m_spPivotTableTool->GetCoreTables(spWorksheet);
			for (size_t i = 0; i < spPivotTables->Count(); ++i)
				if (spPivotTables->Item(i) == spCorePivotTable)
					return FALSE;
		}
		m_pActivePivotTable->SetApiPivotTable(nullptr);
	}
	ks_stdptr<etoldapi::PivotTable> spPivot;
	if (FindPivotTable(&spPivot) && spPivot)
		m_pActivePivotTable->SetApiPivotTable(spPivot);
	return TRUE;
}

// 是否当前分析不发送解读
BOOL KSmartAnalysis::GetIsNotRequestUnderstand()
{
	return m_bIsNotRequestUnderstand;
}

// 获取推荐解读接口
ISmartUnderstand* KSmartAnalysis::GetRecommendUnderstand()
{
	return m_pAnalysis ? m_pAnalysis->GetRecommendUnderstand() : nullptr;
}

// 将推荐解读片段插入分析方案
HRESULT KSmartAnalysis::InsertRecommendAnalysis(QString strName)
{
	if (!m_pAnalysis)
		return E_FAIL;
	KRecommendUnderstand* pUnderstand = (KRecommendUnderstand*)m_pAnalysis->GetRecommendUnderstand();
	if (!pUnderstand)
		return E_FAIL;
	const KRecommendChart* pChart = pUnderstand->GetRecommend(strName);
	if (!pChart)
		return E_FAIL;
	
	HRESULT hr = NewAnalysis();
	if (FAILED(hr) || !m_pActivePivotTable)
		return E_FAIL;

	ks_stdptr<IKWorksheet> spWorksheet = m_pActivePivotTable->GetWorksheet();
	ks_stdptr<_Workbook> spWorkbook = spWorksheet->GetWorkbook();
	ks_stdptr<IKTransactionTool> spTransTool = spWorkbook->GetTransactionTool();
	spTransTool->StartTrans();

	if (!m_bIsNotRequestUnderstand)
		m_bIsNotRequestUnderstand = TRUE;

	QStringList qslRowField, qslColField;
	VecNameField vecDataField;
	pChart->GetRowColDataFields(qslRowField, qslColField, vecDataField);
	hr = m_pActivePivotTable->ReplacePivotTableFields(qslRowField, qslColField, vecDataField);


	if (m_bIsNotRequestUnderstand)
		m_bIsNotRequestUnderstand = FALSE;
	if (FAILED(hr) || hr == S_CONTINUE)
		spTransTool->Rollback();
	else
		spTransTool->CommitTrans(nullptr, ksoCommit, FALSE);

	ks_stdptr<IKApplication> spApp = spWorkbook->GetApplication();
	ks_stdptr<IKEtApplication> spEtApp = spApp;
	spEtApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(spWorkbook);

	return hr;
}

// 是否已发送识别请求
BOOL KSmartAnalysis::GetDoRecognize()
{
	return m_bDoRecognize;
}

// 设置已发送识别请求
void KSmartAnalysis::SetDoRecognize(BOOL b)
{
	m_bDoRecognize = b;
}

// 获取数据源场景分类结果
QString KSmartAnalysis::GetLabel() const
{
	return m_pAnalysis ? m_pAnalysis->GetLabel() : QString();
}

KSmartAnalysisManager::KSmartAnalysisManager(IKWorksheet* pSheet)
{
	AddAnalysis(pSheet);
}

KSmartAnalysisManager::~KSmartAnalysisManager()
{
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
		if (m_vecAnalysis[i])
			delete m_vecAnalysis[i];
}

// 获取当前数据源
ISmartAnalysis * KSmartAnalysisManager::GetActiveSmartAnalysis()
{
	return m_pActiveAnalysis;
}

void KSmartAnalysisManager::SetActiveSmartAnalysis(ISmartAnalysis* pAnalysis)
{
	m_pActiveAnalysis = pAnalysis;
}

ISmartAnalysis* KSmartAnalysisManager::FindAnalysisBySheet(IKWorksheet* pSheet)
{
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
	{
		ISmartAnalysis* pAnalysis = m_vecAnalysis[i];
		if (!pAnalysis)
			continue;
		ks_stdptr<IKWorksheet> spSheet = pAnalysis->GetSourceWorksheet();
		if (!spSheet)
			spSheet = pAnalysis->GetNewAnalysisWorksheet();
		if (spSheet == pSheet)
			return pAnalysis;

		if (!pAnalysis->IsReady() && pAnalysis->IsSelectionPivotTable() && pAnalysis->GetNewAnalysisWorksheet() == pSheet)
			return pAnalysis;

		KSmartAnalysis* pSmartAnalysis = (KSmartAnalysis*)pAnalysis;
		int nCnt = pSmartAnalysis->GetPivotTableCount();
		for (int j = 0; j < nCnt; ++j)
		{
			ks_stdptr<IKWorksheet> spWorksheet = pSmartAnalysis->GetPivotTableSheet(j);
			if (spWorksheet == pSheet)
			{
				return pAnalysis;
			}
		}
	}
	return NULL;
}

// 设置当前数据源
HRESULT KSmartAnalysisManager::SelectAnalysis(IKWorksheet * pSheet)
{
	if (!pSheet)
		return S_FALSE;
	if (m_pActiveAnalysis)
	{
		ks_stdptr<IKWorksheet> spSheet = m_pActiveAnalysis->GetSourceWorksheet();
		if (spSheet == pSheet)
			return S_OK;
		if (!spSheet)
		{
			ks_stdptr<IKWorksheet> spNewSheet = m_pActiveAnalysis->GetNewAnalysisWorksheet();
			if (spNewSheet == pSheet)
				return S_OK;
		}
		ks_stdptr<IKWorksheet> spWorksheet = m_pActiveAnalysis->GetActiveAnalysisWorksheet();
		if (spWorksheet == pSheet)
			return S_OK;
	}

	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
	{
		ISmartAnalysis* pAnalysis = m_vecAnalysis[i];
		if (!pAnalysis)
			continue;
		ks_stdptr<IKWorksheet> spSheet = pAnalysis->GetSourceWorksheet();
		if (spSheet == pSheet)
		{
			m_pActiveAnalysis = pAnalysis;
			return S_OK;
		}
		if (!spSheet)
		{
			ks_stdptr<IKWorksheet> spNewSheet = pAnalysis->GetNewAnalysisWorksheet();
			if (spNewSheet == pSheet)
			{
				m_pActiveAnalysis = pAnalysis;
				return S_OK;
			}
		}
		KSmartAnalysis* pSmartAnalysis = (KSmartAnalysis*)pAnalysis;
		int nCnt = pSmartAnalysis->GetPivotTableCount();
		for (int j = 0; j < nCnt; ++j)
		{
			ks_stdptr<IKWorksheet> spWorksheet = pSmartAnalysis->GetPivotTableSheet(j);
			if (spWorksheet == pSheet)
			{
				m_pActiveAnalysis = pAnalysis;
				pAnalysis->SelectAnalysis(pSheet);
				return S_OK;
			}
		}
	}
	m_pActiveAnalysis = nullptr;
	// 没找到
	return S_FALSE;
}

// 新增数据源
HRESULT KSmartAnalysisManager::AddAnalysis(IKWorksheet * pSheet)
{
	m_pActiveAnalysis = new KSmartAnalysis(pSheet);
	m_vecAnalysis.push_back(m_pActiveAnalysis);
	return S_OK;
}

// 删除数据源
HRESULT KSmartAnalysisManager::DeleteAnalysis(ISmartAnalysis* pAnalysis)
{
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
	{
		if (m_vecAnalysis[i] == pAnalysis)
		{
			if (m_pActiveAnalysis == pAnalysis)
				m_pActiveAnalysis = NULL;
			m_vecAnalysis.erase(m_vecAnalysis.begin() + i);
			delete pAnalysis;
			return S_OK;
		}
	}
	return S_FALSE;
}

HRESULT KSmartAnalysisManager::DeleteAnalysis(IKWorksheet * pSheet)
{
	std::vector<size_t> vecDelIdx;
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
	{
		ISmartAnalysis* pAnalysis = m_vecAnalysis[i];
		if (!pAnalysis)
			continue;
		ks_stdptr<IKWorksheet> spSheet;
		if (pAnalysis->IsReady())
			spSheet = pAnalysis->GetSourceWorksheet();
		else
			spSheet = pAnalysis->GetNewAnalysisWorksheet();
		if (spSheet == pSheet)
		{
			vecDelIdx.push_back(i);
			pAnalysis->UpdateOnDeleteSheet(pSheet);
		}
	}
	for (int j = vecDelIdx.size() - 1; j >= 0; --j)
	{
		if (m_pActiveAnalysis == m_vecAnalysis[vecDelIdx[j]])
			m_pActiveAnalysis = NULL;
		delete m_vecAnalysis[vecDelIdx[j]];
		m_vecAnalysis.erase(m_vecAnalysis.begin() + vecDelIdx[j]);
	}
	// 没找到
	return S_FALSE;
}

// 删除数据源
HRESULT KSmartAnalysisManager::DeleteAnalysis(IKWorkbook * pBook)
{
	std::vector<size_t> vecDelIdx;
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
	{
		ISmartAnalysis* pAnalysis = m_vecAnalysis[i];
		if (!pAnalysis)
			continue;
		ks_stdptr<IKWorksheet> spSheet = pAnalysis->GetSourceWorksheet();
		if (!spSheet)
			continue;
		ks_stdptr<IKWorkbook> spBook = spSheet->GetWorkbook();
		if (spBook == pBook)
		{
			vecDelIdx.push_back(i);
			pAnalysis->UpdateOnDeleteSheet(spSheet);
		}
	}
	for (int j = vecDelIdx.size() - 1; j >= 0; --j)
	{
		bool bClearActive = false;
		if (m_pActiveAnalysis == m_vecAnalysis[vecDelIdx[j]])
			bClearActive = true;
		if (bClearActive)
			m_pActiveAnalysis = NULL;
		ISmartAnalysis* pAnalysis = m_vecAnalysis[vecDelIdx[j]];
		m_vecAnalysis.erase(m_vecAnalysis.begin() + vecDelIdx[j]);
		delete pAnalysis;
	}
	// 没找到
	return S_FALSE;
}

// 查找数据源
ISmartAnalysis* KSmartAnalysisManager::GetSmartAnalysis(IKWorksheet* pSheet)
{
	return FindAnalysisBySheet(pSheet);
}

// 判断当前工作表是否是分析页
BOOL KSmartAnalysisManager::IsAnalysisPivotTable(IKWorksheet* pSheet)
{
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
	{
		ISmartAnalysis* pAnalysis = m_vecAnalysis[i];
		if (!pAnalysis)
			continue;
		KSmartAnalysis* pSmartAnalysis = (KSmartAnalysis*)pAnalysis;
		int nCnt = pSmartAnalysis->GetPivotTableCount();
		for (int j = 0; j < nCnt; ++j)
		{
			ks_stdptr<IKWorksheet> spWorksheet = pSmartAnalysis->GetPivotTableSheet(j);
			if (spWorksheet == pSheet)
			{
				return TRUE;
			}
		}
	}
	return FALSE;
}

// 判断当前工作表是否是数据源页
BOOL KSmartAnalysisManager::IsAnalysisPivotTableSource(IKWorksheet* pSheet)
{
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
	{
		ISmartAnalysis* pAnalysis = m_vecAnalysis[i];
		if (!pAnalysis)
			continue;
		ks_stdptr<IKWorksheet> spSheet = pAnalysis->GetSourceWorksheet();
		if (spSheet == pSheet)
		{
			return TRUE;
		}
	}
	return FALSE;
}

uint32 KSmartAnalysisManager::GetAnalysisCount(IKWorkbook* pBook)
{
	uint32 count = 0;
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
	{
		ISmartAnalysis* pAnalysis = m_vecAnalysis[i];
		if (!pAnalysis)
			continue;
		ks_stdptr<IKWorksheet> spSheet = pAnalysis->GetSourceWorksheet();
		if (!spSheet)
			continue;
		ks_stdptr<IKWorkbook> spBook = spSheet->GetWorkbook();
		if (spBook == pBook)
			count++;
	}
	return count;
}
// 判断该分析是否有效
BOOL KSmartAnalysisManager::IsValidAnalysis(ISmartAnalysis* pAnalysis)
{
	return std::find(m_vecAnalysis.begin(), m_vecAnalysis.end(), pAnalysis) != m_vecAnalysis.end() ? TRUE : FALSE;
}

// 清除所有数据
HRESULT KSmartAnalysisManager::ClearAll()
{
	for (size_t i = 0; i < m_vecAnalysis.size(); ++i)
		if (m_vecAnalysis[i])
		{
			delete m_vecAnalysis[i];
			m_vecAnalysis[i] = nullptr;
		}
	m_vecAnalysis.clear();
	m_pActiveAnalysis = nullptr;
	return S_OK;
}

HRESULT Create_SmartAnalysisManager(IKWorksheet* pWorksheet, ISmartAnalysisManager** ppSmartAnalysisManager)
{
	if (!ppSmartAnalysisManager)
		return E_INVALIDARG;

	*ppSmartAnalysisManager = new KSmartAnalysisManager(pWorksheet);
	if (*ppSmartAnalysisManager)
		return S_OK;
	return E_FAIL;
}
}
