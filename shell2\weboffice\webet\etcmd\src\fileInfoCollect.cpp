﻿#include "fileInfoCollect.h"
#include "workbook.h"
#include "wo/core_stake.h"
#include "appcore/et_appcore_dbsheet.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "util.h"
#include "et_hard_define_strings.h"
#include "ettools/ettools_encode_decoder.h"
#include "webbase/memo_stat_common.h"
#include "form/et_form_task_class.h"
#include "kfc/service/rts/alg_rts.h"
#include "collect.h"

extern WebProcType gs_procType;

extern Callback* gs_callback;

namespace wo
{
constexpr int ET_DbSheet_FieldType_Collect_Count = ET_DbSheet_FieldType_Count + 2;
constexpr int Et_DbSheetField_Collect_SearchLookup = ET_DbSheet_FieldType_Count;
constexpr int Et_DbSheetField_Collect_Statistic = ET_DbSheet_FieldType_Count + 1;

MemStatInfo FileInfoCollector::m_lastMemStatInfo;
QString FileInfoCollector::m_traceId;

FileInfoCollector::FileInfoCollector(KEtWorkbook *pWorkbook, const char *traceId)
    : m_pWorkbook(pWorkbook)
{
    if (traceId)
        m_traceId = QString::fromUtf8(traceId);
}

void FileInfoCollector::Collect()
{
    ASSERT(m_pWorkbook);
    if (m_pWorkbook)
    {
        // 收集超链接
        CollectOpenUrlCnt();
        // 收集公式
        CollectOpenFormulaCnt();
        // 收集打开文件autofit
        CollectAutoFitInfo();
        // 收集Cell中公式编译数量
        CollectCellCompileFormulaInfo();
        // 收集跨book引用
        CollectFxbook();
        // 收集保护工作表
        CollectProtectSheet();
        // 收集独占单元格
        collectExclusiveRanges();
        // 收集图片
        CollectPic();
        // 收集dispimg公式使用统计
        CollectDispImg();
        // 收集单元格图片
        CollectInnerPic();
        // 收集图表
        CollectChart();
        // 收集评论
        CollectComment();
        // collect validity list 
        CollectDVCustomList();
        // collect sheet filter 
        CollectSheetFilter();
        // 收集DbSheet
        CollectDbSheet();
        // 收集DbSheet数量
        CollectDbSheetCount();
        // 收集工作台Sheet数量
        CollectWorkbenchSheetCount();
        //收集工作表表单数量
        CollectLinkFormCount();
        // 收集透视表
        CollectPivotTable();
        // collect importrange formula
        CollectImportrange();
        // 收集条件格式制定文本有IO错误（重复写双引号）
        CollectCondFormatSpecTextIOError();
        // 收集条件格式
        CollectCondFormat();
        //当同步筛选状态开启时，收集是否使用了SubTotal()函数
        CollectSubTotalFuncInfo();
        // 收集是否使用了CELL函数
        CollectCellFuncInfo();
        // 收集合并表格是否开启自动更新
        CollectMergeFile();
        // 收集图片数量
        CollectPicCount();
        // 收集有数据的单元格数量
        CollectUsedCellCount();
        // 收集useedRage和保护开启者信息
        CollectUsedRangeAndProtectInfo();
        // 收集单元格历史记录信息
        CollectCellHistoryInfo();
        // 收集图片体积大小信息
        CollectImageSize();
        // 是外部链接图片且有裁剪属性
        CollectExternalCropPic();
        // 收集et/ksheet表数量
        CollectTableCount();
        // 收集内存占用
        CollectMemInfo(__X("mem_open"), true);
        // 收集数据有效性数量
        CollectDataValidationInfo();
        CollectMergeCells();
        // 收集仪表盘sheet数量(仅webet)
        CollectDashBoardCount();
    }
}


void FileInfoCollector::AddComponentInfo(BinWriter& binWriter, IBook* pBook)
{
    ASSERT(pBook);
    if (pBook)
    {
        BMP_PTR bmpPtr = pBook->GetBMP();
        binWriter.addKey("project");
        if (bmpPtr->bDbSheet)
            binWriter.addString(__X("dbt"));
        else if (bmpPtr->bKsheet)
            binWriter.addString(__X("ksheet"));
        else
            binWriter.addString(__X("et"));
    }
}


bool FileInfoCollector::CollectOpenUrlCnt()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskHyperlink))
        return false;

    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    collectInfo("behaviour.openurlcnt", info->hyperlinkCount);
    return true;
}

bool FileInfoCollector::CollectOpenFormulaCnt()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    wo::IBookStake* pBookStake = pBook->GetWoStake();
    if (!pBookStake)
        return false;

    bool includeCellImg = true;
    if (!pBookStake->HasFormula(includeCellImg))
        return false;

    collectInfo("behaviour.openformulacnt");

    includeCellImg = false;
    if (pBookStake->HasFormula(includeCellImg))
    {
        collectInfo("behaviour.openformulacnt_nodispimg");
    }

    return true;
}

bool FileInfoCollector::CollectFxbook()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOKERRORS* pBookErr = NULL;
    if (FAILED(pBook->GetBookErrors(&pBookErr)) || !pBookErr->fHasExternRef)
        return false;

    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    collectInfo("behaviour_fxbook", info->supBookCount);
    return true;
}

bool FileInfoCollector::CollectProtectSheet()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskProtectSheet))
        return false;

    collectInfo("behaviour_protectsheet");

    if (checkBookInfoCollect(BOOK_INFO_COLLECT::_maskProtectWidthInvisibleRange))
    {
        collectInfo("behaviour_protectsheetinvisible");
    }

    return true;
}

bool FileInfoCollector::collectExclusiveRanges()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (nullptr == pBook)
        return false;
    BOOK_INFO_COLLECT *pInfo = pBook->GetBookInfoCollect();
    if (nullptr == pInfo || !pInfo->cntExclusiveSheets)
        return false;
    collectInfo(QString("behavior_occupycell_%1_%2").arg(pInfo->cntExclusiveSheets).arg(pInfo->cntExclusiveUsers).toUtf8());
    return true;
}

bool FileInfoCollector::CollectPic()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskPic))
        return false;

    collectInfo("behaviour_pic");
    return true;
}

bool FileInfoCollector::CollectDispImg()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    DispImgStatistics type = pBook->GetWoStake()->getDispImgStatistics();
    if (type == DispImgStatistics_NO)
        return false;

    BinWriter binWriter;
    binWriter.addKey("name");
    binWriter.addString(__X("behaviour.dispimg"));
    binWriter.addKey("fileid");
    binWriter.addString(m_pWorkbook->getFileId());
    binWriter.addKey("isnormal");
    binWriter.addBool(type == DispImgStatistics_Normal);
    if (type != DispImgStatistics_Normal)
    {
        binWriter.addKey("fmla");
        binWriter.addString(pBook->GetWoStake()->getDispImgFmla());
    }
    if (!m_traceId.isEmpty())
    {
        binWriter.addKey("traceid");
        binWriter.addString(krt::utf16(m_traceId));
    }
    AddComponentInfo(binWriter, pBook);

    BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), binWriter.writeLength()};
    WO_LOG_X(m_pWorkbook->getLogger(), WO_LOG_INFO, "fileinclude: behaviour.dispimg");
    gs_callback->collectInfo("fileinclude", &slice);
    return true;
}

bool FileInfoCollector::CollectInnerPic()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskCellPic))
        return false;

    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    collectInfo("behaviour_innerpic", info->cellImageCount);
    return true;
}

bool FileInfoCollector::CollectChart()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskChart))
        return false;

    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    collectInfo("behaviour_chart", info->chartCount, true);
    return true;
}

bool FileInfoCollector::CollectComment()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskComment))
        return false;

    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    collectInfo("behaviour_comment", info->commentCount);
    
    return true;
}

bool FileInfoCollector::CollectDVCustomList()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskDVCustomList))
        return false;

    collectInfo("behaviour_validitylist");
    return true;
}

bool FileInfoCollector::CollectPivotTable()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskPivotTable))
        return false;

    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    collectInfo("behaviour_pivottable", info->pivotTableCount);
    return true;
}

bool FileInfoCollector::CollectImportrange()
{
    IBookStake *pBookStake = m_pWorkbook->GetCoreWorkbook()->GetBook()->GetWoStake();
    if (pBookStake->HasImportrangeFuncs())
    {
        collectInfo("behaviour_importrange");
    }

    return true;
}


bool FileInfoCollector::CollectSheetFilter()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskSheetFilter))
        return false;

    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    wo::IBookSetting *pSettings = pBook->GetWoStake()->getSetting();
    if (!pSettings)
    {
        return false;
    }
    
    if (pSettings->getIsFilterShared())
    {
        collectInfo("behaviour_filter_1");
    }
    else
    {
        collectInfo("behaviour_filter_0");
    }

    return true;
}

bool FileInfoCollector::CollectDbSheet()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskDbSheet))
        return false;

    collectInfo("behaviour_dbsheet");
    return true;
}

bool FileInfoCollector::CollectCondFormatSpecTextIOError()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskCondFormatSpecTextIOError))
        return false;

    collectInfo("behaviour_condformat_spectext_io_error");
    return true;
}

bool FileInfoCollector::CollectCondFormat()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskCondFormat))
        return false;

    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    collectInfo("behaviour_condformat", info->condFormatCount, true);
    return true;
}

bool FileInfoCollector::CollectSubTotalFuncInfo()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskSubTotalFunc))
        return false;

    //Todo:同步筛选状态开
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    wo::IBookSetting *pSettings = pBook->GetWoStake()->getSetting();
    if (!pSettings)
    {
        return false;
    }
    
    if (pSettings->getIsFilterShared())
    {
        collectInfo("behaviour.subtotal_withfilterinstep");
    }
    else
    {
        collectInfo("behaviour.subtotal_withfilteruninstep");
    }

    return true;
}

bool FileInfoCollector::CollectCellFuncInfo()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskCellFunc))
        return false;

    collectInfo("behaviour.fxcell");
    return true;
}

bool FileInfoCollector::CollectMergeFile()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook) return false;

    INT nSheetCnt = 0;
    pBook->GetSheetCount(&nSheetCnt);
    if (nSheetCnt == 0)
        return false;

    INT nMergeSheet = 0;
    INT nMergeSheetAuto = 0;
    for (INT i = 0; i < nSheetCnt; i++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);

        std::vector<SMergeFileInfo> listMergeFile;
        ks_stdptr<IMergeFile> spMergeFile;
        spSheet->GetExtDataItem(edSheetMergeFile, (IUnknown**)&spMergeFile);
        if (!spMergeFile) return false;

        spMergeFile->GetMergeFileInfo(listMergeFile);
        if(listMergeFile.empty()) continue;

        nMergeSheet++;
        if (spMergeFile->GetIsAutoRefresh())
        {
            nMergeSheetAuto++;
        }
    }

    if (nMergeSheet > 0)
    {
        QString mergeSheetInfo = QString("behaviour.mergeetauto_%1_%2").arg(nMergeSheet).arg(nMergeSheetAuto);
        BinWriter binWriter;
        binWriter.addKey("name");
        binWriter.addString(krt::utf16(mergeSheetInfo));
        binWriter.addKey("fileid");
        binWriter.addString(m_pWorkbook->getFileId());
        binWriter.addKey("count");
        binWriter.addInt32(1);
        if (!m_traceId.isEmpty())
        {
            binWriter.addKey("traceid");
            binWriter.addString(krt::utf16(m_traceId));
        }
        AddComponentInfo(binWriter, pBook);

        BinWriter::StreamHolder shbt = binWriter.buildStream();
        WebSlice slice = {shbt.get(), binWriter.writeLength()};
        gs_callback->collectInfo("fileinclude", &slice);
    }
    return true;
}

bool FileInfoCollector::CollectCellHistoryInfo()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook->GetBMP()->bKsheet)
    {
        return true;
    }

    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();

    if (info->maxHistoryOnBook > 0)
    {
        collectInfo("behaviour.history_count_cell", info->maxHistoryOnCell);
        collectInfo("behaviour.history_count_sheet", info->maxHistoryOnSheet);
        collectInfo("behaviour.history_count_book", info->maxHistoryOnBook);
    }
    
    return true;
}

bool FileInfoCollector::CollectToposortInfo()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    if (info->msTopoSort > 100)
    {
        collectInfo("behaviour_open_toposort", info->msTopoSort);
    }
    return true;
}

bool FileInfoCollector::CollectAutoFitInfo()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    if (info->msAutoFit > 100)
    {
        collectInfo("behaviour_open_autofit", info->msAutoFit);
    }
    return true;
}

bool FileInfoCollector::CollectCellCompileFormulaInfo()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    if (info->cellCompileFmlCount > 0)
    {
        collectInfo("behaviour_cell_compile_fml", info->cellCompileFmlCount);
    }
    if (info->cellCompileFmlTokenCount > 0)
    {
        collectInfo("behaviour_cell_compile_token", info->cellCompileFmlTokenCount);
    }
    return true;
}

 bool FileInfoCollector::CollectTableCount()
 {
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (pBook->GetBMP()->bDbSheet)
        return true;

    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    if (info->tableCount > 0)
        collectInfo(QStringLiteral("behaviour_et_table_%1").arg(info->tableCount).toUtf8(), info->tableCount);
    return true;
 }

namespace
{

void collectDbSharedInfo(IBook* pBook, const WCHAR * fileId)
{
    BMP_PTR bmpPtr = pBook->GetBMP();
    if (FALSE == bmpPtr->bDbSheet)
        return;

    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
    pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    bool hasSharedLink = spSharedLinkMgr && FALSE == spSharedLinkMgr->SharedIdEmpty();

    binary_wo::BinWriter binWriter;
    binWriter.addStringField(krt::utf16(
        QStringLiteral("dbfilecontrol_shareview_%1").arg(hasSharedLink ? "true" : "false")), "event");
    binWriter.addStringField(fileId, "fileid");
    FileInfoCollector::AddComponentInfo(binWriter, pBook);

    binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), binWriter.writeLength()};

    WOLOG_INFO << "index_experience: " 
        << QStringLiteral("dbfilecontrol_shareview_%1").arg(hasSharedLink ? "true" : "false");
    gs_callback->collectInfo("index_experience", &slice);
}

}

bool FileInfoCollector::CollectDbSheetCount()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    INT nSheetCnt = 0;
    pBook->GetSheetCount(&nSheetCnt);
    if (nSheetCnt == 0)
        return false;

    INT nDbSheetCnt = 0;
    INT nDbViewCnt = 0;
    INT nDbViewVisibleRecCnt = 0;
    INT nDbViewVisibleFldCnt = 0;
    std::vector<INT> vecSheetRecCnt;
    std::vector<INT> vecSheetFldCnt;
    std::vector<INT> vecSheetFldValUniquePropCnt;//每个sheet开启了“禁止重复项”开关的字段数目
    std::array<std::vector<int>, ET_DbSheet_FieldType_Collect_Count> sheetsFieldTypeCount{};
    std::array<std::vector<int>, et_DBSheetView_Type_Count> sheetsViewTypeCount{};
    std::array<int, DbSheet_St_Count> sheetSyncTypeCount{};
    std::array<int, ET_DbSheet_FieldType_Count> kanbanGroupbyFieldTypeCount{};//看板视图数_单选_多选_联系人_级联_双向关联_单向关联_复选框_评分
    bool bHasFilter = false;
    bool bHasSort = false;
    bool bHasGroup = false;
    bool hasParentRecord = false;
    QString parentRecordDbSheetViewEvent = "application_sub_items";
    QString parentRecordDbSheetEvent = "application_sub_items_splitroot";
    INT exsitChildrenRecordTopRecordCount = 0;
    for (INT i = 0; i < nSheetCnt; i++)
    {
        std::array<int, et_DBSheetView_Type_Count> viewTypeCount{};

        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        if (spSheet == nullptr)
            return false;

        if (!spSheet->IsDbSheet())
            continue;
        nDbSheetCnt++;

        ks_stdptr<IDBSheetViews> spDbSheetViews;
        VS(DbSheet::GetDBSheetViews(spSheet.get(), &spDbSheetViews));
        if (spDbSheetViews == nullptr)
            return false;

        for (int type = Et_DBSheetViewUse_ForDb; type < Et_DBSheetViewUse_Count; ++type)
        {
            nDbViewCnt += spDbSheetViews->GetSize(static_cast<ET_DBSheet_ViewUseType>(type)); 
        }

        INT nDbViewEnableParentRecordCnt = 0;
        ks_stdptr<IDBSheetViewsEnum> spEnum;
        if (SUCCEEDED(spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
        {
            do
            {
                ks_stdptr<IDBSheetView> spDbSheetView;
                spEnum->GetCurView(&spDbSheetView);
                if (!spDbSheetView)
                    continue;

                ET_DBSheet_ViewType type = spDbSheetView->GetType();
                ++viewTypeCount[type];

                nDbViewVisibleRecCnt += spDbSheetView->GetVisibleRecords()->Count();
                nDbViewVisibleFldCnt += spDbSheetView->GetVisibleFields()->Count();

                const IDbFilter* pFilter = spDbSheetView->GetConstFilter();
                if (pFilter && pFilter->GetFiltersCount() > 0)
                    bHasFilter = true;

                ks_stdptr<IDBSheetOp> spDbSheetOp;
                VS(DbSheet::GetDBSheetOp(spSheet.get(), &spDbSheetOp));
                if (spDbSheetOp == nullptr)
                    return false;
                const IDBRecordsOrderManager* pRecordOrderManager = spDbSheetView->GetConstOrderManager();
                if (pRecordOrderManager)
                {
                    if (pRecordOrderManager->GetSortConditionCount() > 0)
                        bHasSort = true;
                    if (pRecordOrderManager->GetGroupConditionCount() > 0)
                    {
                        bHasGroup = true;
                        if (type == et_DBSheetView_Kanban)
                        {
                            const IDBRecordsOrderManager* pRecordOrderManager = spDbSheetView->GetConstOrderManager();
                            EtDbId groupFieldId = pRecordOrderManager->GetGroupCondition(0)->GetKeyFieldId();
                            ks_stdptr<IDbField> spField;
                            IDbFieldsManager *pFieldsManager = spDbSheetOp->GetFieldsManager();
                            HRESULT hr = pFieldsManager->GetField(groupFieldId, &spField);
                            if (FAILED(hr))
                                continue;
                            ++kanbanGroupbyFieldTypeCount[spField->GetType()];
                        }
                    }
                }

                if (spDbSheetView->IsExistParentRecord())
                {
                    hasParentRecord = true;
                    ++nDbViewEnableParentRecordCnt;
                }
            }while (SUCCEEDED(spEnum->Next()));
        }

        parentRecordDbSheetViewEvent += QString("_%1").arg(nDbViewEnableParentRecordCnt);
        for (int viewType = 0; viewType < viewTypeCount.size(); viewType++)
            sheetsViewTypeCount[viewType].push_back(viewTypeCount[viewType]);

        ks_stdptr<IDBSheetOp> spDbSheetOp;
        VS(DbSheet::GetDBSheetOp(spSheet.get(), &spDbSheetOp));
        if (spDbSheetOp == nullptr)
            return false;

        ET_DbSheet_Sync_Type syncType = spDbSheetOp->GetSheetSyncType();
        if (syncType != DbSheet_St_None)
            ++sheetSyncTypeCount[syncType];

        vecSheetRecCnt.push_back(spDbSheetOp->GetAllRecords()->Count());
        vecSheetFldCnt.push_back(spDbSheetOp->GetAllFields()->Count());

        INT sheetFldValUniquePropCnt = 0;

        IDbFieldsManager *pFieldsManager = spDbSheetOp->GetFieldsManager();
        const IDBIds *pFields = spDbSheetOp->GetAllFields();
        std::array<int, ET_DbSheet_FieldType_Collect_Count> fieldTypeCount{};
        for (EtDbIdx fld = 0; fld < pFields->Count(); fld++)
        {
            EtDbId fldId = pFields->IdAt(fld);
            ks_stdptr<IDbField> spField;
            HRESULT hr = pFieldsManager->GetField(fldId, &spField);
            if (FAILED(hr))
                continue;

            QRegularExpression pattern(R"(\"cmdType\"\s*:\s*\"([^\"]*)\")");
            QRegularExpressionMatch match = pattern.match(QString::fromUtf16(spField->GetCustomConfig()));
            if (match.hasMatch()) {
                QString name = QString("table_ai_%1").arg(match.captured(1).toLower());
                collectInfo(name.toUtf8());
            }

            fieldTypeCount[GetFieldType(spField)]++;
            if(spField->IsValueUnique())
            {
                sheetFldValUniquePropCnt++;
            }
        }

        vecSheetFldValUniquePropCnt.push_back(sheetFldValUniquePropCnt);

        for (int fieldType = 0; fieldType < fieldTypeCount.size(); fieldType++)
            sheetsFieldTypeCount[fieldType].push_back(fieldTypeCount[fieldType]);

        IDbRecordsManager* pRecordManager = spDbSheetOp->GetRecordsManager();
        exsitChildrenRecordTopRecordCount = pRecordManager->GetExistChildrenRecordTopRecordCount();
        parentRecordDbSheetEvent += QString("_%1").arg(exsitChildrenRecordTopRecordCount);
    }
    for (int fieldType = 0; fieldType < sheetsFieldTypeCount.size(); fieldType++)
    {
        PCWSTR typeStr = nullptr;
        GetFieldTypeName(fieldType, &typeStr);
        QString name = QString("table_%1").arg(QString::fromUtf16(typeStr).toLower());
        const std::vector<int> &vec = sheetsFieldTypeCount[fieldType];
        int typeTotalCnt = 0;
        for (auto it = vec.begin(); it != vec.end(); ++it)
        {
            name += QString("_%1").arg(*it);
            typeTotalCnt += *it;
        }
        if (typeTotalCnt > 0)
            collectInfo(name.toUtf8(), typeTotalCnt);
    }
    for (int viewType = 0; viewType < sheetsViewTypeCount.size(); viewType++)
    {
        PCWSTR typeStr = nullptr;
        _appcore_GainEncodeDecoder()->EncodeViewType(static_cast<ET_DBSheet_ViewType>(viewType), &typeStr);
        QString name = QString("application_%1").arg(QString::fromUtf16(typeStr).toLower());
        const std::vector<int> &vec = sheetsViewTypeCount[viewType];
        int typeTotalCnt = 0;
        for (auto it = vec.begin(); it != vec.end(); ++it)
        {
            name += QString("_%1").arg(*it);
            typeTotalCnt += *it;
        }
        if (typeTotalCnt > 0)
            collectInfo(name.toUtf8(), typeTotalCnt);
    }
    for (int syncType = 0; syncType < sheetSyncTypeCount.size(); ++syncType)
    {
        if (sheetSyncTypeCount[syncType] > 0)
        {
            PCWSTR typeStr = nullptr;
            _appcore_GainEncodeDecoder()->EncodeDbSheetSyncType(static_cast<ET_DbSheet_Sync_Type>(syncType), &typeStr);
            // 产品要求et同步db的埋点名字必须叫et，不能叫gridsheet
            if (syncType == DbSheet_St_GridSheet)
                typeStr = __X("et");
            QString name = QString("dbsheet_link_%1_%2").arg(QString::fromUtf16(typeStr).toLower()).arg(sheetSyncTypeCount[syncType]);
            collectInfo(name.toUtf8());
        }
    }

    if (nDbSheetCnt > 0) 
    {
        collectInfo(__X("behaviour_dbsheet_count"), nDbSheetCnt, [nDbViewCnt](WoFileIncludeCollector & collector){
            collector.addCount2(nDbViewCnt);
        });
        collectInfo(__X("behaviour_dbsheetviews_visible_recfld_cnt"), nDbViewVisibleRecCnt, [nDbViewVisibleFldCnt](WoFileIncludeCollector & collector){
            collector.addCount2(nDbViewVisibleFldCnt);
        });
        if (hasParentRecord)
        {
            collectInfo(parentRecordDbSheetViewEvent.toUtf8());
            collectInfo(parentRecordDbSheetEvent.toUtf8());
        }

    }
    if (vecSheetRecCnt.size() > 0)
    {
        int totalCnt = 0;
        QString name = QString("behaviour_rec_cnt");
        for (auto it = vecSheetRecCnt.begin(); it != vecSheetRecCnt.end(); ++it)
        {
            int cnt = *it;
            totalCnt += cnt;
            name += QString("_%1").arg(cnt);
        }
        collectInfo(name.toUtf8(), totalCnt);
    }
    if (vecSheetFldCnt.size() > 0)
    {
        int totalCnt = 0;
        QString name = QString("behaviour_fld_cnt");
        for (auto it = vecSheetFldCnt.begin(); it != vecSheetFldCnt.end(); ++it)
        {
            int cnt = *it;
            totalCnt += cnt;
            name += QString("_%1").arg(cnt);
        }
        collectInfo(name.toUtf8(), totalCnt);
    }
    if (vecSheetFldValUniquePropCnt.size() > 0)
    {
        int totalCnt = 0;
        QString name = QString("table_fld_val_unique_prop");
        for (auto it = vecSheetFldValUniquePropCnt.begin(); it != vecSheetFldValUniquePropCnt.end(); ++it)
        {
            int cnt = *it;
            totalCnt += cnt;
            name += QString("_%1").arg(cnt);
        }
        collectInfo(name.toUtf8(), totalCnt);
    }
    if (pBook->GetBMP()->bKsheet)
    {
        QString name = QString("behaviour_ksheet");
        name += QString("_%1_%2").arg(nDbSheetCnt > 0 ? 1 : 0).arg(nDbViewCnt > 1 ? 1 : 0);
        collectInfo(name.toUtf8());
    }
    if (bHasFilter)
        collectInfo("table_get_filter");
    if (bHasSort)
        collectInfo("table_get_sort");
    if (bHasGroup)
        collectInfo("table_get_group");

    const std::vector<int> &vec = sheetsViewTypeCount[et_DBSheetView_Kanban];
    int kanbanViewTotalCnt =  std::accumulate(vec.begin(), vec.end(), 0);
    
    if (kanbanViewTotalCnt > 0)
    {
        QString name = QString("kanban_group_%1_%2_%3_%4_%5_%6_%7_%8_%9")
            .arg(kanbanViewTotalCnt).arg(kanbanGroupbyFieldTypeCount[Et_DbSheetField_SingleSelect]).arg(kanbanGroupbyFieldTypeCount[Et_DbSheetField_MultipleSelect])
            .arg(kanbanGroupbyFieldTypeCount[Et_DbSheetField_Contact]).arg(kanbanGroupbyFieldTypeCount[Et_DbSheetField_Cascade])
            .arg(kanbanGroupbyFieldTypeCount[Et_DbSheetField_Link]).arg(kanbanGroupbyFieldTypeCount[Et_DbSheetField_OneWayLink])
            .arg(kanbanGroupbyFieldTypeCount[Et_DbSheetField_Checkbox]).arg(kanbanGroupbyFieldTypeCount[Et_DbSheetField_Rating]);
        collectInfo(name.toUtf8());
    }

    collectDbSharedInfo(pBook, m_pWorkbook->getFileId());

    return true;
}

void FileInfoCollector::CollectDbSheetRecordInfo()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    INT nSheetCnt = 0;
    pBook->GetSheetCount(&nSheetCnt);
    if (nSheetCnt == 0)
        return;

    INT nDbSheetCnt = 0;
    bool hasParentRecord = false;
    QString childrenRecordDbSheetEvent= "application_sub_items_max_level";
    INT existChildrenRecordMaxLevel = 0;
    for (INT i = 0; i < nSheetCnt; i++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        if (spSheet == nullptr)
            return;

        if (!spSheet->IsDbSheet())
            continue;
        nDbSheetCnt++;

        ks_stdptr<IDBSheetViews> spDbSheetViews;
        VS(DbSheet::GetDBSheetViews(spSheet.get(), &spDbSheetViews));
        if (spDbSheetViews == nullptr)
            return;
        INT nDbViewEnableParentRecordCnt = 0;
        ks_stdptr<IDBSheetViewsEnum> spEnum;
        if (SUCCEEDED(spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
        {
            do
            {
                ks_stdptr<IDBSheetView> spDbSheetView;
                spEnum->GetCurView(&spDbSheetView);
                if (!spDbSheetView)
                    continue;

                if (spDbSheetView->IsExistParentRecord())
                {
                    hasParentRecord = true;
                    break;
                }
            }while (SUCCEEDED(spEnum->Next()));
        }

        ks_stdptr<IDBSheetOp> spDbSheetOp;
        VS(DbSheet::GetDBSheetOp(spSheet.get(), &spDbSheetOp));
        if (spDbSheetOp == nullptr)
            return;

        IDbRecordsManager* pRecordManager = spDbSheetOp->GetRecordsManager();
        existChildrenRecordMaxLevel = pRecordManager->GetExistChildrenRecordMaxLevel();
        childrenRecordDbSheetEvent += QString("_%1").arg(existChildrenRecordMaxLevel);
    }
    
    if (nDbSheetCnt > 0) 
    {
        if (hasParentRecord)
        {
            collectInfo(childrenRecordDbSheetEvent.toUtf8());
        }

    }
}

void FileInfoCollector::CollectWorkbenchSheetCount()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook->GetBMP()->bKsheet)
        return;

    ks_stdptr<etoldapi::Worksheets> spWorksheets;
    m_pWorkbook->GetCoreWorkbook()->get_Worksheets(&spWorksheets);
    if (!spWorksheets)
        return;

    int workbenchSheetCount = spWorksheets->GetSheetCount(stWorkbench, TRUE);
    if (workbenchSheetCount > 0)
        collectInfo("application_workbench", workbenchSheetCount);
}

void FileInfoCollector::CollectMemInfo(const WCHAR * wszName, bool isFileOpen, WoCollectInfo *pCot)
{
    MemStatInfo curInfo;
    bool collectTrans = !isFileOpen;
    bool collectFreeMem = !isFileOpen;
    if (!GetMemInfo(curInfo, collectTrans, collectFreeMem))
        return;

    CommitMemInfo(pCot, wszName, curInfo, nullptr);
    if (isFileOpen)
    {
        m_lastMemStatInfo = curInfo;
    }
}

void FileInfoCollector::CollectMemInfoOnExit(binary_wo::BinWriter &binWriter, WoCollectInfo *pCot)
{
    binWriter.beginStruct();
    {
        binWriter.addStringField(__X("svr_coremem"), "name");
        binWriter.beginStruct("params");
        {
            CollectMemInfo(__X("mem_close"), false, pCot);
        }
        binWriter.endStruct();
    }
    binWriter.endStruct();
}

void FileInfoCollector::CollectMemInfoDiff()
{
    constexpr int64_t minMemDiff = 1024 * 1024 * 10;
    MemStatInfo curInfo;
    constexpr bool collectTrans = true;
    constexpr bool collectFreeMem = true;
    int64_t minRss = m_lastMemStatInfo.rss + minMemDiff; // 只上报增涨超过10M的
    if (!GetMemInfo(curInfo, collectTrans, collectFreeMem, minRss))
        return;

    MemStatInfo diffInfo = curInfo;
    diffInfo.rss -= m_lastMemStatInfo.rss;
    diffInfo.fileSize -= m_lastMemStatInfo.fileSize;
    diffInfo.mfxAllocSize -= m_lastMemStatInfo.mfxAllocSize;
    diffInfo.mfxAllocObjSize -= m_lastMemStatInfo.mfxAllocObjSize;
    diffInfo.dvCount -= m_lastMemStatInfo.dvCount;
    diffInfo.sharedConsts -= m_lastMemStatInfo.sharedConsts;
    diffInfo.cachedFontCount -= m_lastMemStatInfo.cachedFontCount;
    diffInfo.usedCellCount -= m_lastMemStatInfo.usedCellCount;
    diffInfo.userConnsCount -= m_lastMemStatInfo.userConnsCount;
    diffInfo.transactionMem -= m_lastMemStatInfo.transactionMem;
    diffInfo.coreFreeMem -= m_lastMemStatInfo.coreFreeMem;
    diffInfo.rtsMem -= m_lastMemStatInfo.rtsMem;
    diffInfo.msrMem -= m_lastMemStatInfo.msrMem;
    diffInfo.versionCount -= m_lastMemStatInfo.versionCount;

    m_lastMemStatInfo = curInfo;
    CommitMemInfo(nullptr, __X("mem_diff"), curInfo, &diffInfo);
}

bool FileInfoCollector::GetMemInfo(MemStatInfo& info, bool collectTrans, bool collectFreeMem, int64_t minRss)
{
    // 快速统计，下面的统计必需在常量时间取到
    if (!m_pWorkbook)
        return false;

    // 不统计子进程
    if (gs_procType != WebProcTypeMaster)
        return false;

    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    info.rss = MemoStatProcMemory(
        &info.vmSize, 
        &info.sharedMem,
        &info.codeMem,
        &info.dataMem); // resident set size
    if (info.rss <= minRss)
        return false;

    wo::IBookStake* pBookStake = pBook->GetWoStake();
    if (!pBookStake)
        return false;

    info.mfxAllocSize = alg::mfxEvaluateAllocatedSize();
    info.mfxAllocObjSize = alg::mfxEvaluateAllocatedSizeObj();
    info.fileSize = m_pWorkbook->GetFileSaveInfo().size;
    info.dvCount = 0;
    info.sharedConsts = 0;
    info.cachedFontCount = 0;
    pBookStake->MemStat(&info.dvCount, &info.sharedConsts);

	ks_stdptr<IFontHelper> spFontHelper;
	_ettext_GetxtObject(IID_IFontHelper, (void**)&spFontHelper);
    if (spFontHelper)
        info.cachedFontCount = spFontHelper->GetWoCachedFontCount();

    if (m_lastMemStatInfo.usedCellCount > 0)
    {
        info.usedCellCount = m_lastMemStatInfo.usedCellCount;
    }
    else
    {
        BOOK_PERF_STAT_DATA* bookstatInfo = pBook->GetBookPerfStat();
        if (bookstatInfo)
            info.usedCellCount = bookstatInfo->usedCellCount;
    }

    KEtVersionManager* mgr = static_cast<KEtVersionManager*>(m_pWorkbook->getVersionMgr());
    if (collectTrans)
    {
        info.transactionMem = m_pWorkbook->GetCoreApp()->GetEtTranscationTool()->GetMemoryComplexity(pBook); 
        info.transactionMem *= 1024; // 单位是 1K
        info.transactionMem += mgr->GetMemoryComplexity();

        if (pBook->GetRtsRepository())
        {
            UINT64 szMemo = 0, cntUnk = 0, cntAtom = 0;
            UINT cntTransStep = pBook->GetRtsRepository()->ProcMemoStatInfo(&szMemo, &cntUnk, &cntAtom);
            info.rtsMem = szMemo + (cntUnk + cntAtom) * sizeof(void*);
        }
    }
    info.versionCount = mgr->getTaskVersionCount();

	// TODO_WebMergeToPC_ET [黄帅][暂时注释因端移动mem代码导致web对memfix的改动丢失, 找不到实现的调用方]
    // if (collectFreeMem)
    //     info.coreFreeMem = alg::mfxEvaluateFreeMemSize() + alg::mfxEvaluateFreeMemSizeObj();

    info.msrMem = alg::msrMemStat();
    
    int64_t userCount = 0;
    IKUserConns* userConns = m_pWorkbook->GetCoreApp()->getUserConns();
    if (userConns)
        info.userConnsCount = userConns->count();
    return true;
}

void FileInfoCollector::CommitMemInfo(WoCollectInfo *pCot, const WCHAR *name, const MemStatInfo& info, MemStatInfo *pDiff)
{
    ASSERT(name != nullptr);
    constexpr int memShiftUnit = 10; // KB

    QString infoStr;
    const MemStatInfo *pInfo = pDiff ? pDiff : &info;
    infoStr = QString(
        "rss:%1_fs:%2_mas:%3_maos:%4_dvc:%5_shc:%6_cf:%7_ucc:%8_userc:%9_tcm:%10_cfm:%11_rts:%12_msr:%13_vers:%14")
        .arg(pInfo->rss >> memShiftUnit)
        .arg(pInfo->fileSize >> memShiftUnit)
        .arg(pInfo->mfxAllocSize >> memShiftUnit)
        .arg(pInfo->mfxAllocObjSize >> memShiftUnit)
        .arg(pInfo->dvCount)
        .arg(pInfo->sharedConsts)
        .arg(pInfo->cachedFontCount)
        .arg(pInfo->usedCellCount)
        .arg(pInfo->userConnsCount)
        .arg(pInfo->transactionMem >> memShiftUnit)
        .arg(pInfo->coreFreeMem >> memShiftUnit)
        .arg(pInfo->rtsMem >> memShiftUnit)
        .arg(pInfo->msrMem)
        .arg(pInfo->versionCount);

    wo::WoCollectInfo cot;
    wo::WoCollectInfo& collect = pCot == nullptr ? cot : *pCot;
    
    
    collect.addString("fileid", m_pWorkbook->getFileId());
    collect.addString("traceid", m_traceId);
    collect.addComponentInfo(m_pWorkbook->GetBMP());
    collect.addAppVersion();
    collect.addString("name", name);
    
    if (pDiff)
        collect.addString("diff_mem", infoStr);
    
    collect.addInt32("virtual_mem", info.vmSize >> memShiftUnit);
    collect.addInt32("resident_mem", info.rss >> memShiftUnit);
    collect.addInt32("shared_mem", info.sharedMem >> memShiftUnit);
    collect.addInt32("code_mem", info.codeMem >> memShiftUnit);
    collect.addInt32("data_mem", info.dataMem >> memShiftUnit);
    collect.addInt32("file_size", info.fileSize >> memShiftUnit);
    collect.addInt32("mfx_alloc", info.mfxAllocSize >> memShiftUnit);
    collect.addInt32("mfx_alloc_obj", info.mfxAllocObjSize >> memShiftUnit);
    collect.addInt32("dv", info.dvCount);
    collect.addInt32("cache_font", info.cachedFontCount);
    collect.addInt32("shared_const", info.sharedConsts);
    collect.addInt32("user_conns", info.userConnsCount);
    collect.addInt32("used_cell", info.usedCellCount);
    collect.addInt32("trans_mem", info.transactionMem >> memShiftUnit);
    collect.addInt32("rts_mem", info.rtsMem >> memShiftUnit);
    collect.addInt32("msr_cnt", info.msrMem);
    collect.addInt32("version_cnt", info.versionCount);
    
    collect.collect("svr_coremem");
    if (pDiff)
    {
        WOLOG_INFO << "svr_coremem: virtual_mem: " << (info.vmSize >> memShiftUnit) 
                << ", resident_mem: " << (info.rss >> memShiftUnit) 
                << ", diff_mem: " << krt::utf16(infoStr);
    }
    else
    {
        WOLOG_INFO << "svr_coremem: virtual_mem: " << (info.vmSize >> memShiftUnit) 
                << ", resident_mem: " << (info.rss >> memShiftUnit)
                << ", mem_info: " << krt::utf16(infoStr);
    }
}

bool FileInfoCollector::checkBookInfoCollect(DWORD mask)
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();

    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();

    return alg::IsBitUsed(info->data, mask);
}

WebStr FileInfoCollector::FileFormatToStr(FILEFORMAT type)
{
    switch (type)
    {
        CASE_FILEFORMAT_ENUM_TO_STRING(ffUNKNOWN)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffET)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffETT)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLS)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLT)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffDBF)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXML)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffRTF)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffHTM)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffMHT)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLSX)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLSM)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLSB)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffUNI)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffASC)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffCSV)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffPRN)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffPIC)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffEMB)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffUOF)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffUOF2)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffUOF3)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffUOST3)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffETCLIP)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffETCLIPCOOP)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffTxtAsHtm)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffDIF)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLTX)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLTM)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffETX)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffCUSTOMDOC)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffSECDOC)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffODS)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffSLK)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLAM)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffXLA)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffEMBOBJ)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffOFD)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffPDF)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffDBT)
        CASE_FILEFORMAT_ENUM_TO_STRING(ffKSHEET)
        default:
				ASSERT(FALSE);
				return __X("ffUNKNOWN");
    }
}
void FileInfoCollector::collectInfo(const char *collectName, int count/* = 1 */, bool fileFormat /*=false*/)
{
    BinWriter binWriter;
    binWriter.addKey("name");
    binWriter.addString(krt::utf16(QString::fromUtf8(collectName)));
    binWriter.addKey("fileid");
    binWriter.addString(m_pWorkbook->getFileId());
    if (!m_traceId.isEmpty())
    {
        binWriter.addKey("traceid");
        binWriter.addString(krt::utf16(m_traceId));
    }
    binWriter.addKey("count");
    binWriter.addInt32(count);
    if (fileFormat)
    {
        FILEFORMAT ff = m_pWorkbook->GetFileFormat();
        binWriter.addKey("fileformat");
        WebStr strff = FileFormatToStr(ff);
        binWriter.addString(strff);
    }
    AddComponentInfo(binWriter, m_pWorkbook->GetCoreWorkbook()->GetBook());

    BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), binWriter.writeLength()};
    WO_LOG_X(m_pWorkbook->getLogger(), WO_LOG_INFO, "fileinclude: %s: %d", collectName, count);
    gs_callback->collectInfo("fileinclude", &slice);
}

void FileInfoCollector::collectInfo(const WCHAR *collectName, int count/*= 1*/, CollectCallBack cb /* = nullptr*/)
{
    wo::WoFileIncludeCollector collector(m_pWorkbook, collectName, count);
    collector.addCmdTraceId(m_traceId);
    if (cb)
        cb(collector);
    collector.collect();
}

bool FileInfoCollector::CollectPicCount()
{
    ks_stdptr<IKMediaManage> spMediaMgr;
	oplGetBookMediaMgr(m_pWorkbook->GetCoreWorkbook()->GetBook(), &spMediaMgr);
	if (spMediaMgr)
    {
        LONG count = 0;
        spMediaMgr->GetBlipAtomCount(&count);
        if (count > 0)
        {
            collectInfo("behaviour_pic_count", count);
        }
    }
    return true;
}

bool FileInfoCollector::CollectUsedCellCount()
{
	BOOK_PERF_STAT_DATA* info = m_pWorkbook->GetCoreWorkbook()->GetBook()->GetBookPerfStat();
	if (info)
	{
		 collectInfo("behaviour_usedcell_count", info->usedCellCount);
	}
    return true;
}

bool FileInfoCollector::CollectUsedRangeAndProtectInfo()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook) return false;
    if (pBook->GetBMP()->bDbSheet)
        return false;

    ks_stdptr<IBookOp> spBookOp;
    pBook->GetOperator(&spBookOp);
    if (!spBookOp)
        return false;

    int realSheetCount = 0;
    int protectedSheetCount = 0;
    bool isSameProtectUser = true;
    bool bUnprotectedHasUserRange = false;
    bool bHasPassword = false;
    int64_t usedCellCount = 0;
    std::unordered_set<PCWSTR, util::PcwstrIHash, util::PcwstrICmp> protectUsers;

    ks_stdptr<IKWorksheets> spWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    QString strUserdRgInfo("behaviour_usedrange");
    CS_COMPILE_PARAM ccp(cpfNormal, 0, 0, 0);
    RANGE rgUsed(pBook->GetBMP());
    ks_bstr	ws;
    range_helper::ranges rgs = range_helper::ranges::create_instance();
    for (int iSheet = 0; iSheet < spWorksheets->GetSheetCount(); iSheet++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(iSheet, &spSheet);

        if (spSheet->IsDbSheet())
            continue;

        const WCHAR* pName = NULL;
        spSheet->GetName(&pName);
        if (0 == xstrcmp(pName, STR_CELL_IMAGE_SHEET_NAME))
            continue;

        ws.clear();
        rgs.clear();
        IKWorksheet* pSheet = spWorksheets->GetSheetItem(iSheet);
        ks_castptr<_Worksheet> spWorksheet = pSheet;
		spWorksheet->GetUsedRange(&rgUsed);
        rgUsed.SetSheetFromTo(0);
        rgs.add(alg::STREF_THIS_BOOK, rgUsed);
        spBookOp->DecompileRange(rgs, ccp, &ws);
		strUserdRgInfo += QString("_%1").arg(QString::fromUtf16(ws.c_str()));
        ++realSheetCount;
        usedCellCount += rgUsed.CellCount();
	    ISheetProtection* pSheetProtection = spWorksheet->GetProtection();
        if (!pSheetProtection)
            continue;

        if (pSheetProtection->IsProtected())
        {
            ++protectedSheetCount;
            if (isSameProtectUser && pSheetProtection->GetMaster())
            {
                if (!protectUsers.empty() && protectUsers.find(pSheetProtection->GetMaster()) == protectUsers.end())
                    isSameProtectUser = false;
                else
                    protectUsers.insert(pSheetProtection->GetMaster());
            }

            if (pSheetProtection->GetPassword() != 0 || pSheetProtection->IsExtendProtection())
                bHasPassword = true;

            CollectSheetProtectionInfo(pSheetProtection, iSheet);
        }
        else
        {
            if (!bUnprotectedHasUserRange && pSheetProtection->GetUserRangeCount() > 0)
                bUnprotectedHasUserRange = true;
        }
    }
    collectInfo(strUserdRgInfo.toUtf8(), 1);
    collectInfo("behaviour_et_sheet_count", realSheetCount);
    collectInfo("behaviour_et_usedcell_count", std::min<int64_t>(usedCellCount, std::numeric_limits<int>::max()));

    if (protectedSheetCount == 0)
    {
        collectInfo("behaviour_protect_0");
    }
    else
    {
        if (isSameProtectUser)
            collectInfo("behaviour_protect_1");
        else
            collectInfo("behaviour_protect_2");
    }

    if (bUnprotectedHasUserRange)
        collectInfo("behaviour_sheetprotectclose");

    if (bHasPassword)
        collectInfo("behaviour_protectpwd");

    return true;
}

bool FileInfoCollector::CollectImageSize()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook) return false;
    if (pBook->GetBMP()->bDbSheet || pBook->GetBMP()->bKsheet)
        return false;

    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();

    // 下面图片大小放在 filesize2 里, 貌似经常有问题, 先单独埋个图片的
    if (info->imageSize > 0)
    {
        collectInfo("behaviour_et_imagesize", static_cast<int>(info->imageSize));
    }

    const FileSaveInfo& fileSizeInfo = m_pWorkbook->GetFileSaveInfo();
    if(!fileSizeInfo.bSaved) return false;

    int64_t localImageSize = info->imageSize;
    int64_t localFileSize = fileSizeInfo.size;
    int64_t filesize1 = localFileSize - localImageSize;

    BinWriter binWriter;
    binWriter.addKey("name");
    binWriter.addString(__X("behaviour_filesize"));
    binWriter.addKey("fileid");
    binWriter.addString(m_pWorkbook->getFileId());
    binWriter.addKey("count");
    binWriter.addInt32(1);
    if (!m_traceId.isEmpty())
    {
        binWriter.addKey("traceid");
        binWriter.addString(krt::utf16(m_traceId));
    }
    AddComponentInfo(binWriter, pBook);

    binWriter.addKey("filesize1");
    binWriter.addFloat64(filesize1);
    binWriter.addKey("filesize2");
    binWriter.addFloat64(localImageSize);

    BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), binWriter.writeLength()};
    WO_LOG_X(m_pWorkbook->getLogger(), WO_LOG_INFO, "fileinclude: behaviour_filesize,  filesize1: %lld, filesize2: %lld", filesize1, localImageSize);
    gs_callback->collectInfo("fileinclude", &slice);

    return true;
}

bool FileInfoCollector::CollectLinkFormCount()
{
	IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	int count = wo::getFormCount(pBook);
    if (count > 0)
        collectInfo("application_biaodan", count);

    return true;
}

bool FileInfoCollector::CollectExternalCropPic()
{
    if (!checkBookInfoCollect(BOOK_INFO_COLLECT::_maskExternalCropPic))
        return false;

    collectInfo("behaviour_external_pic_with_crop");
    return true;
}

bool FileInfoCollector::CollectDataValidationInfo()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook || pBook->GetBMP()->bDbSheet)
        return false;

    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();

    if (info && info->dataValidationCount)
        collectInfo("behaviour_data_validation", info->dataValidationCount);
    return true;
}

void FileInfoCollector::CollectSheetProtectionInfo(ISheetProtection* pSheetProtection, int nSheetIdx)
{
    BinWriter binWriter;
    binWriter.addKey("fileid");
    binWriter.addString(m_pWorkbook->getFileId());
    binWriter.addKey("sheetid");
    binWriter.addInt32(nSheetIdx);
    int nUserRangeCnt = pSheetProtection->GetUserRangeCount();
    binWriter.addKey("area");
    binWriter.addInt32(nUserRangeCnt);
    if (!m_traceId.isEmpty())
    {
        binWriter.addKey("traceid");
        binWriter.addString(krt::utf16(m_traceId));
    }

    bool bHasPassword = pSheetProtection->GetPassword() != 0 || pSheetProtection->IsExtendProtection();
    WebStr strPwd = bHasPassword ? pSheetProtection->HasPasswordUUID() ? __X("auto") : __X("custom") : __X("nopwd");
    binWriter.addKey("sheet_pwd");
    binWriter.addString(strPwd);

    bool bOtherAreaVisible = pSheetProtection->GetOtherUserPermission() == PTAAP_Visible;
    binWriter.addKey("remain_area");
    binWriter.addString(bOtherAreaVisible ? __X("view") : __X("edit"));

    int nInvisibleCnt = 0;
    std::set<ks_wstring> users;
    for (int i = 0; i < nUserRangeCnt; ++i)
    {
        const std::vector<AllowEditRangeUserData>* pArrUserData = pSheetProtection->GetUserRangeUser(i);
		for (auto it = pArrUserData->cbegin(); it != pArrUserData->cend(); ++it)
		{
            users.insert(it->userId);
		}

        if (pSheetProtection->GetOthersAccessPermission(i) == PTAAP_Invisible)
            nInvisibleCnt++;
    }
    binWriter.addKey("people");
    binWriter.addInt32(users.size());
    binWriter.addKey("invisible");
    binWriter.addInt32(nInvisibleCnt);

    AddComponentInfo(binWriter, m_pWorkbook->GetCoreWorkbook()->GetBook());

    BinWriter::StreamHolder shbt = binWriter.buildStream();
    WebSlice slice = {shbt.get(), binWriter.writeLength()};
    WO_LOG_X(m_pWorkbook->getLogger(), WO_LOG_INFO, "et_protect_openfile");
    gs_callback->collectInfo("et_protect_openfile", &slice);
}

void FileInfoCollector::CollectMergeCells()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook || pBook->GetBMP()->bDbSheet)
        return;

    BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
    if (info && info->mergeCellCount > 0)
        collectInfo("behaviour_mergecell_cnt", info->mergeCellCount);
}

bool FileInfoCollector::CollectDashBoardCount()
{
    IBook *pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
    if (!pBook || pBook->GetBMP()->bDbSheet || pBook->GetBMP()->bKsheet)
        return false;

    int nSheet = 0, nDashboardCnt = 0;
    pBook->GetSheetCount(&nSheet);
    for (int i = 0; i < nSheet; i++)
    {
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(i, &spSheet);
        if (spSheet && spSheet->IsDbDashBoardSheet())
            nDashboardCnt++;
    }

    if (nDashboardCnt > 0)
        collectInfo("behaviour_dashboard", nDashboardCnt, true);
    return true;
}

int FileInfoCollector::GetFieldType(IDbField* pField)
{
    ET_DbSheet_FieldType type = pField->GetType();
    if (type == Et_DbSheetField_Lookup)
    {
        ks_stdptr<IDbField_Lookup> spFieldLookup = pField;
        switch (spFieldLookup->GetLookupType()) {
        case DBLookupType::LT_LOOKUP:
            return Et_DbSheetField_Collect_SearchLookup;
        case DBLookupType::LT_STATISTIC:
            return Et_DbSheetField_Collect_Statistic;
        default:
            break;
        }
    }
    return type;
}

void FileInfoCollector::GetFieldTypeName(int fieldType, PCWSTR* pTypeStr)
{
    switch (fieldType) {
    case Et_DbSheetField_Collect_SearchLookup:
        *pTypeStr = __X("SearchLookup");
        return;
    case Et_DbSheetField_Collect_Statistic:
        *pTypeStr = __X("Statistic");
        return;
    default:
        break;
    }
     _appcore_GainEncodeDecoder()->EncodeFieldType(static_cast<ET_DbSheet_FieldType>(fieldType), pTypeStr);
}
} // wo
