﻿#ifndef __WEBET_DATABASE_UTILS_H__
#define __WEBET_DATABASE_UTILS_H__

#include "database_def.h"
#include "webbase/binvariant/binvarobj.h"
#include "appcore/et_appcore_dbsheet.h"

interface IFormatHost;
namespace wo
{
namespace Database
{
namespace Utils
{

WebStr FieldTypeToStr(FieldType type);
FieldType StrToFieldType(WebStr str);

HRESULT ClearType(FieldContext *pContext, FieldType type, const RANGE &rg);
HRESULT ClearExcludeType(FieldContext *pContext, FieldType type, const RANGE &rg);
HRESULT ClearAll(FieldContext *pContext, const RANGE &rg);

HRESULT DbAddCol(ListObject *, FieldContext *, FieldType fieldType, RANGE &);
HRESULT DbAddRow(ListObject *, FieldContext *);
HRESULT DbEditCol(ListObject *, IDX, FieldContext *, FieldType fieldType, RANGE &);

HRESULT DbInitEmptyRow(RANGE, FieldContext *);
HRESULT DbInitEmptyCol(RANGE, FieldContext *);

HRESULT DbInitDefFont(PCWSTR, WORD, IFormatHost *);

HRESULT GetDbTable(IBook *pBook, IDX sheetIdx, ICoreListObject **listObject);
HRESULT GetDbTable(IKWorkbook *workbook, IDX sheetIdx, ListObject **listObject);

SheetInitConfig ParseDbSheetInitConfig(const binary_wo::VarObj&);

bool  IsFieldTypeContainCf(FieldContext *pContext, const RANGE &rg);
HRESULT GetQRColByPageId(FieldContext* pFieldContext, IDX sheetIdx, PCWSTR pageId, GenQRLabelCol& qrCol);
HRESULT GetQRCol(FieldContext* pFieldContext, IDX sheetIdx, COL col, GenQRLabelCol& qrCol);
UINT GetQRColCount(FieldContext* pFieldContext, IDX sheetIdx);
bool ParseQRColFromVarObj(BMP_PTR pBmp, const binary_wo::VarObj& obj, GenQRLabelCol& qrCol);
void WriteQRColToVarObj(const GenQRLabelCol& qrCol, binary_wo::VarObj& obj);
HRESULT EnumQRColPageId(FieldContext *pContext, const RANGE& range, const std::function<void(PCWSTR pageId)>& func);
FieldType GetColType(FieldContext* pFieldContext, IDX sheetIdx, COL col);
bool IsCellTextHasSeparatorSymbol(ISheet* pSheet, const RANGE& rg, WCHAR symbol);
DWORD CSTR2ARGB(const WCHAR* pwszColor);

} // Utils
} // Database
} // wo

#endif // __WEBET_DATABASE_UTILS_H__
