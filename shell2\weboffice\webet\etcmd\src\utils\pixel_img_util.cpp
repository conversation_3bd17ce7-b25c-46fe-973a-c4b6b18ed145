﻿#include "pixel_img_util.h"
#include "logger.h"

static bool isRectValid(const QImage& img, const RECT& rect)
{
	if (rect.bottom - rect.top < 3)
		return false;
	if (rect.right - rect.left < 3)
		return false;
	QColor tmp = img.pixelColor(rect.top, rect.left);
	for (int i = rect.top; i <= rect.bottom; ++i)
		for (int j = rect.left; j <= rect.right; ++j)
			if (img.pixelColor(j, i) != tmp)
				return true;
	return false;
}

static void bfs(const QImage& image, std::vector<RECT>& rects)
{
	QImage img(image);
	QSize imgSize = img.size();
	auto addNode = [&img](int x, int y, std::queue<std::pair<int, int>>& q) {
		q.emplace(x, y);
		img.setPixelColor(x, y, Qt::white);
	};
	for (int i = 0, height = imgSize.height(); i < height; ++i)
		for (int j = 0, width = imgSize.width(); j < width; ++j)
		{
			if (img.pixelColor(j, i) == Qt::white)
				continue;
			RECT rect = { j, i, j, i };
			std::queue<std::pair<int, int>> q;
			addNode(j, i, q);
			while (!q.empty())
			{
				auto pos = q.front();
				q.pop();
				int x = pos.first;
				int y = pos.second;
				if (x > rect.right)
					rect.right = x;
				else if (x < rect.left)
					rect.left = x;
				if (y > rect.bottom)
					rect.bottom = y;
				else if (y < rect.top)
					rect.top = y;
				if (x - 1 >= 0 && img.pixelColor(x - 1, y) != Qt::white)
					addNode(x - 1, y, q);
				if (x + 1 < width && img.pixelColor(x + 1, y) != Qt::white)
					addNode(x + 1, y, q);
				if (y - 1 >= 0 && img.pixelColor(x, y - 1) != Qt::white)
					addNode(x, y - 1, q);
				if (y + 1 < height && img.pixelColor(x, y + 1) != Qt::white)
					addNode(x, y + 1, q);
			}
			if (isRectValid(image, rect))
				rects.emplace_back(rect);
		}
}

static QImage getImg(IRenderView* pRdView, double width, double height, int dpi, double scale, const RANGE& usedRg)
{
	ASSERT(pRdView);
	ASSERT(usedRg.IsValid());
	QImage img(qCeil(width * scale), qCeil(height * scale), QImage::Format_RGB32);
	img.fill(Qt::white);
	const int dpm = dpi * 10000 / 254;
	img.setDotsPerMeterX(dpm);
	img.setDotsPerMeterY(dpm);

	kpt::PainterExt painter(&img);
	painter.setRenderHints(QPainter::SmoothPixmapTransform);
	painter.setupPageCoordinate(kpt::UnitPoint, 1);
	painter.scale(scale, scale);
	VS(pRdView->DrawOnlyBack(&painter, &usedRg));

	painter.end();
	return img;
}

inline static void transToDP(IRenderNormalView* pRdNormalView, QRectF& rc)
{
	ASSERT(pRdNormalView);
	qreal xp1 = 0, yp1 = 0, xp2 = 0, yp2 = 0;
	rc.getCoords(&xp1, &yp1, &xp2, &yp2);
	QPointF point1(xp1, yp1);
	pRdNormalView->LPtoDP(&point1);
	QPointF point2(xp2, yp2);
	pRdNormalView->LPtoDP(&point2);
	rc.setCoords(point1.x(), point1.y(), point2.x(), point2.y());
}

namespace wo
{
	namespace util
	{
		HRESULT getPixelImg(IKWorksheetView* pSheetView, int dpi, double scale, int maxSize, ImageHandler imgHandler)
		{
			constexpr int maxDpi = 96 * 4;
			constexpr double minScale = 0.2;
			constexpr double maxScale = 2;
			if (dpi <= 0 || dpi > maxDpi)
				return E_INVALIDARG;
			if (scale < minScale || scale > maxScale)
				return E_INVALIDARG;
			if (maxSize <= 0)
				return E_INVALIDARG;
			IKWorksheet* pWorksheet = pSheetView->GetWorksheet();
			RANGE usedRg(pWorksheet->GetSheet()->GetBMP());
			pWorksheet->GetUsedRange(&usedRg);
			if (!usedRg.IsValid())
				return E_INVALIDARG;
			IRenderView* pRdView = pSheetView->GetActiveRenderView();
			IRenderNormalView* pRdNormalView = pRdView->GetNormalView();
			if (!pRdNormalView)
				return E_FAIL;
			QRectF rcfDv = pRdNormalView->GetRangeDocCoordinate(usedRg);
			transToDP(pRdNormalView, rcfDv);
			double width = rcfDv.width();
			double height = rcfDv.height();
			// 限制图片尺寸
			if (qCeil(width * scale) * qCeil(height * scale) > maxSize)
			{
				WOLOG_INFO << "[GetPixelImg] range is too large";
				return E_FAIL;
			}
			QImage img = getImg(pRdView, width, height, dpi, scale, usedRg);
			std::vector<RECT> rects;
			bfs(img, rects);
			std::for_each(rects.begin(), rects.end(), [&imgHandler, &img](const RECT& rect) {
				imgHandler(img.copy(rect.left, rect.top, rect.right - rect.left + 1, rect.bottom - rect.top + 1));
			});
			return S_OK;
		}
	} // namespace util
} // namespace wo
