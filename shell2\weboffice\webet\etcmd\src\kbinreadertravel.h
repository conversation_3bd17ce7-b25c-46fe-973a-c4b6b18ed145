﻿#ifndef __SHELL2_WEBET_KBINREADER_TRAVEL_H__
#define __SHELL2_WEBET_KBINREADER_TRAVEL_H__

namespace binary_wo 
{

class BuffName
{
public:
    BuffName() : m_nameArray(nullptr), m_type(binary_wo::typeInvalid), m_nameLen(0) {}
    BuffName(WebName nameArray, uint type, uint8 nameLen)
     : m_nameArray(nameArray), m_type(type), m_nameLen(nameLen) {}
     
     uint16 type() const { return m_type; }
     WebName nameArray() const { return m_nameArray; }
     int32 nameLen() const { return m_nameLen; }
     std::string makeNameStr() const { return std::string(m_nameArray, m_nameLen); }
private:
    WebName m_nameArray; // 非字符串 
    uint16 m_type;
    uint8 m_nameLen;
};

class IBinReaderTravel
{
public:
    virtual ~IBinReaderTravel() {}
    virtual void onBeginStruct(const BuffName & name) = 0;
    virtual void onEndStruct(int bytes) = 0;
    virtual void onBeginNameStruct(const BuffName & name, const BuffName & nameStruct) = 0;
    virtual void onEndNameStruct(int bytes) = 0;
    virtual void onBeginArray(const BuffName & name, int32 arrLen) = 0;
    virtual void onEndArray(int bytes) = 0;
    virtual void onValueInt8(const BuffName & name, int8 v) = 0;
    virtual void onValueUint8(const BuffName & name, uint8 v) = 0;
    virtual void onValueInt16(const BuffName & name, int16 v) = 0;
    virtual void onValueUint16(const BuffName & name, uint16 v) = 0;
    virtual void onValueInt32(const BuffName & name, int32 v) = 0;
    virtual void onValueUint32(const BuffName & name, uint32 v) = 0;
    virtual void onValueFloat32(const BuffName & name, float32 v) = 0;
    virtual void onValueFloat64(const BuffName & name, float64 v) = 0;
    virtual void onValueAbsObj32(const BuffName & name, uint32 v) = 0;
    virtual void onValueAbsObj64(const BuffName & name, int64 v) = 0;
    virtual void onValueBool(const BuffName & name, bool v) = 0;
    virtual void onValueString(const BuffName & name, const WCHAR * wszArray, uint32 len) = 0;
    virtual void onValueNameString(const BuffName & name, const BuffName & v) = 0;
    virtual void onValueUint8Array(const BuffName & name, uint8 *pArray, uint32 len) = 0;
    virtual void onArrayItemString(const WCHAR * wszArray, uint32 len) = 0;
    virtual void onArrayItemNameString(const BuffName & v) = 0;
    virtual void onArrayItemValue(uint8 *pArray, uint32 len, binary_wo::Types tp) = 0;
    
    virtual bool needBreak() = 0;
};

enum class ResultCode
{
    kSucc,
    kFail = -1,
    kInterrupt = -2,
    kInvalid = -3,
};

class KBinReaderEnum
{
    struct BinVarData
    {
        uint16 m_type;
        explicit BinVarData(uint16 type): m_type(type) {}
    };
    
public:
    KBinReaderEnum(const byte * buf, int32 len, IBinReaderTravel *pTravel)
    : m_buffer(buf, len)
    , m_travel(pTravel)
    {
        ASSERT(pTravel != nullptr);
    }
    
    ResultCode read();
private:
    bool readNameList();
    ResultCode readStruct(BinVarData & parent);
    ResultCode readElement(BinVarData & parent, binary_wo::Types & type);
    ResultCode readValue(BinVarData & parent, const BuffName & name);
    ResultCode readArray(BinVarData & parent);
    
    const BuffName & readName();
    
private:
	binary_wo::BinBuffer	m_buffer;
    std::unordered_map<int32, BuffName> m_nameMaps;
    IBinReaderTravel *m_travel;
};

}

#endif