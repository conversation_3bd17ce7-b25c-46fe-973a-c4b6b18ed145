﻿#include "update_app_version_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "create_app_view_helper.h"
#include "etcmd/src/et_revision_context_impl.h"
#include "webetlink.h"
#include "webbase/binvariant/binreader.h"
#include "util.h"

extern Callback* gs_callback;

namespace UpdateAppVersionHelper
{

struct STR_HASH
{
	size_t operator()(const ks_wstring& str) const
	{
		return alg::HashWString(str.c_str());
	}
};

//view级别 UpdateViewToAppItem
UpdateViewToAppItem::UpdateViewToAppItem(EtDbId viewId, ET_DBSheet_ViewType viewTp, bool bPersonalView, PCWSTR appUserId,
    bool bNeedCreateSharedId, PCWSTR sharedId)
    :m_viewId(viewId),
    m_viewTp(viewTp),
    m_bPersonalView(bPersonalView),
    m_appUserId(appUserId),
    m_bNeedCreateSharedId(bNeedCreateSharedId),
    m_sharedId(sharedId),
    m_airAppSheetStId(0),
    m_airAppId(INV_EtDbId)
{
}

bool UpdateViewToAppItem::SetSharedId(PCWSTR sharedId)
{
    if(!sharedId)
        return false;
    m_sharedId = sharedId;
    return true;
}

bool UpdateViewToAppItem::SetAirAppInfo(UINT airAppSheetStId, EtDbId airAppId, const ks_wstring& airAppName)
{
    if (airAppSheetStId == 0 || airAppId == INV_EtDbId)
        return false;

    m_airAppSheetStId = airAppSheetStId;
    m_airAppId = airAppId;
    m_airAppName = airAppName;
    return true;
}

void UpdateViewToAppItem::SerialResult(binary_wo::BinWriter& writer)
{
    //Todo:后续这里得完善把创建出来的应用sheet的stid给返。
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    EtDbIdStr buf;
    pCtx->EncodeEtDbId(m_viewId, &buf);
    writer.addStringField(buf, "appViewId");
 
    PCWSTR typeStr = nullptr;
    VS(_appcore_GainEncodeDecoder()->EncodeViewType(static_cast<ET_DBSheet_ViewType>(m_viewTp), &typeStr));
    writer.addStringField(typeStr, "appViewType");
 
    writer.addBoolField(m_bPersonalView, "isPersonalView");
    writer.addStringField(m_appUserId.c_str(), "appUserId");
    writer.addBoolField(m_bNeedCreateSharedId, "isCreateSharedId");
    writer.addStringField(m_sharedId.c_str(), "sharedId");
    writer.addUint32Field(m_airAppSheetStId, "airAppSheetStId");
 
    pCtx->EncodeEtDbId(m_airAppId, &buf);
    writer.addStringField(buf, "airAppId");
    writer.addStringField(m_airAppName.c_str(), "airAppName");
}

void UpdateViewToAppItem::AddToParam(binary_wo::VarObj varViewSharedId)
{
    varViewSharedId.add_field_str("sharedId", m_sharedId.c_str());
    EtDbIdStr buf;
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
    pCtx->EncodeEtDbId(m_viewId, &buf);
    varViewSharedId.add_field_str("viewId", buf);
}

///////////////////////////////////////////////////////
//sheet级别 UpdateDbSheetToAppItem
UpdateDbSheetToAppItem::UpdateDbSheetToAppItem(UINT sheetStId, bool bVeryHidden)
    :m_sheetStId(sheetStId)
    ,m_bVeryHidden(bVeryHidden)
{
}

void UpdateDbSheetToAppItem::PushViewToAppItem(const UpdateViewToAppItem& item)
{
    m_updateViewIdOrders.push_back(item.m_viewId);
    m_updateViews.emplace(item.m_viewId, item);
}

bool UpdateDbSheetToAppItem::ModifySharedId(EtDbId viewId, PCWSTR sharedId)
{
    auto it = m_updateViews.find(viewId);
    if(it == m_updateViews.end())
        return false;
    return it->second.SetSharedId(sharedId);
}

bool UpdateDbSheetToAppItem::CheckFetchAllSharedIds()
{
    for(auto it = m_updateViews.begin();it != m_updateViews.end(); it++)
    {
        if(it->second.m_bNeedCreateSharedId)
        {
            if(it->second.m_sharedId.empty())
                return false;
        }
    }
    return true;
}

void UpdateDbSheetToAppItem::SerialResult(binary_wo::BinWriter& writer)
{
    for(auto it = m_updateViews.begin();it != m_updateViews.end(); it++)
    {
        writer.beginStruct();
        writer.addUint32Field(m_sheetStId, "dbSheetStId");
        it->second.SerialResult(writer);
        writer.endStruct();
    }
}

void UpdateDbSheetToAppItem::AddToParam(binary_wo::VarObj& varSheetSharedIds)
{
    varSheetSharedIds.add_field_uint32("sheetStId", m_sheetStId);
    binary_wo::VarObj varSharedIdsInfo = varSheetSharedIds.add_field_array("sharedIdsInfo", binary_wo::typeStruct);
    for(auto it = m_updateViews.begin();it != m_updateViews.end(); it++)
    {
        binary_wo::VarObj varViewSharedId = varSharedIdsInfo.add_item_struct();
        it->second.AddToParam(varViewSharedId);
    }
}

bool UpdateDbSheetToAppItem::IsEmpty()
{
    return m_updateViews.empty();
}

///////////////////////////////////////////////////////
//管理所有需要升级应用的dbsheet UpdateDbSheetToAppMgr
void UpdateDbSheetToAppMgr::PushDbSheetToAppItem(const UpdateDbSheetToAppItem& item)
{
    m_updateSheetStIdOrders.push_back(item.m_sheetStId);
    m_updateSheets.emplace(item.m_sheetStId, item);
}

bool UpdateDbSheetToAppMgr::ModifySharedId(UINT sheetStId, EtDbId viewId, PCWSTR sharedId)
{
    auto it = m_updateSheets.find(sheetStId);
    if(it == m_updateSheets.end())
        return false;
    return it->second.ModifySharedId(viewId, sharedId);
}

bool UpdateDbSheetToAppMgr::CheckFetchAllSharedIds()
{
    for(auto it = m_updateSheets.begin(); it != m_updateSheets.end(); it++)
    {
        if(!(it->second.CheckFetchAllSharedIds()))
        {
            return false;
        }
    }
    return true;
}

void UpdateDbSheetToAppMgr::SerialResult(binary_wo::BinWriter& writer)
{
    writer.beginArray("newAppList");
    for(auto it = m_updateSheets.begin(); it != m_updateSheets.end(); it++)
    {
        it->second.SerialResult(writer);
    }
    writer.endArray();
}

void UpdateDbSheetToAppMgr::AddToParam(VarObj& param)
{
    binary_wo::VarObj varSharedIds = param.add_field_array("sharedIds", binary_wo::typeStruct);
    for(auto it = m_updateSheets.begin(); it != m_updateSheets.end(); it++)
    {
        binary_wo::VarObj varSheetSharedIds = varSharedIds.add_item_struct();
        it->second.AddToParam(varSheetSharedIds);
    }
}

bool UpdateDbSheetToAppMgr::IsEmpty()
{
    return m_updateSheets.empty();
}

void UpdateDbSheetToAppMgr::LogInfo()
{
    size_t viewsCnt = 0;
    for(auto it = m_updateSheets.begin(); it != m_updateSheets.end(); it++)
    {
        viewsCnt += it->second.m_updateViews.size();
    }

    WOLOG_INFO << "[UpdateAppVersion::LogInfo] sheets cnt: " << m_updateSheets.size() << ", views cnt: " << viewsCnt;
}

updateAppVersionResGuard::updateAppVersionResGuard(wo::KEtRevisionContext* ctx)
    :m_pUpdateDbSheetToAppMgr(nullptr)
	,m_ctx(ctx)
    ,m_bStatus(false)
{

}

updateAppVersionResGuard::~updateAppVersionResGuard()
{
    if(!m_ctx->isExecDirect())
    {
        //把创建的应用结果给返回给服务端
        binary_wo::BinWriter binWriter;
        ASSERT(m_ctx->getUser()->userID());
        binWriter.addStringField(m_ctx->getUser()->userID(), "userId");
        if(m_bStatus)
        {
            binWriter.addBoolField(true, "isOk");
            if(m_pUpdateDbSheetToAppMgr)
                m_pUpdateDbSheetToAppMgr->SerialResult(binWriter);
        }
        else 
        {
            binWriter.addBoolField(false, "isOk");
        }
        
		binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice resWebSlice = {shbt.get(), binWriter.writeLength()};
		gs_callback->updateAppVersionDone(&resWebSlice);
    }
}

void updateAppVersionResGuard::UpdateResult(bool bStatus, UpdateDbSheetToAppMgr* pUpdateDbSheetToAppMgr)
{
    m_bStatus = bStatus;
    m_pUpdateDbSheetToAppMgr = pUpdateDbSheetToAppMgr;
}
///////////////////////////////////////////////////////

//没开启过分享视图，则开启分享视图
HRESULT ExecShareView(wo::IEtRevisionContext* ctx, UINT sheetStId, EtDbId viewId, PCWSTR sharedId, UINT airAppSheetStId, EtDbId airAppId, KDbDisplayFormType displayFormTp)
{
    binary_wo::VarObjRoot root(new binary_wo::BinVarRoot());
    binary_wo::VarObj param(root.get(), root.get());
    param.add_field_uint32("sheetStId", sheetStId);

    EtDbIdStr buf;
	_appcore_GainDbSheetContext()->EncodeEtDbId(viewId, &buf);
    param.add_field_str("viewId", buf);
    param.add_field_str("sharedId", sharedId);
    param.add_field_bool("bUsedForApp",true);
    param.add_field_uint32("airAppSheetStId", airAppSheetStId);
    _appcore_GainDbSheetContext()->EncodeEtDbId(airAppId, &buf);
    param.add_field_str("airAppId", buf);
    
    PCWSTR strDisplayFormType = NULL;
    _appcore_GainEncodeDecoder()->EncodeDbDisplayForm(displayFormTp, &strDisplayFormType);
    param.add_field_str("displayFormTp", strDisplayFormType);

    return wo::DbSheet::DoShareView(param, ctx, _appcore_GainEncodeDecoder());
}

//收集所有需要升级成应用的“view”
static HRESULT InitUpdateDbSheetToAppMgr(wo::KEtWorkbook* pWorkbook, UpdateDbSheetToAppMgr& updateDbSheetToAppMgr, std::unordered_set<ks_wstring, STR_HASH>& resetSharedIdsSet)
{
    HRESULT hr = E_FAIL;
    IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
    ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
    long sheetCount = 0;
    spWorksheets->get_Count(&sheetCount);
    for (IDX idx = 0; idx < sheetCount; idx++)
	{
		ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(idx, &spSheet);
        if (!spSheet || !spSheet->IsDbSheet())
			continue;

        //得单独处理查询应用的深度隐藏sheet
        SHEETSTATE sheetState = ssVisible;
        spSheet->GetVisible(&sheetState);
        bool bSheetVeryHidden = (sheetState == ssVeryhidden);

        //遍历所有的view
        ks_stdptr<IDBSheetViews> spViews;
        hr = wo::DbSheet::GetDBSheetViews(spSheet, &spViews);
        if(FAILED(hr))
            continue;

        // 旧AS的视图是ForDb，需要清洗为App，读盘/CopyTo的时候已经改为ForApp了，判断是否关联了appId
        UpdateDbSheetToAppItem updateDbSheetToAppItem(spSheet->GetStId(), bSheetVeryHidden);
        for (int i = 0; i < spViews->GetSize(Et_DBSheetViewUse_ForApp); ++i)
        {
            ks_stdptr<IDBSheetView> spView;
            spViews->GetItemAt(i, Et_DBSheetViewUse_ForApp, &spView);
            if (spView == nullptr)
                continue;

            AirAppInfo info;
            spView->GetAirAppInfo(&info);
            if (info.appId != INV_EtDbId)
                continue;

            PCWSTR sharedId = __X("");
            bool bNeedCreateSharedId = !(spView->GetSharedLink());
            if(!bNeedCreateSharedId)
            {
                IDBSharedLinkView* pLink = spView->GetSharedLink();
                if (pLink)
                {
                    sharedId = pLink->Id();
                    //判断现有sharedId是否需要重置
                    if(resetSharedIdsSet.find(sharedId) != resetSharedIdsSet.end())
                    {
                        bNeedCreateSharedId = true;
                        sharedId = __X("");
                    }
                }
            }

            bool bPersonalView = (spView->GetPersonalViewUserIdsCount() >= 1);
            PCWSTR appUserId = __X("");
            //appUserId分几种情况:
            //1.若本身为个人视图，则此值等于个人视图所绑定的userId；
            //2.若视图已经预埋了视图创建者的userId，则此值等于视图创建者的userId
            //3.除此之外此值传空，服务端自行判断(优先看服务端数据库是否记录，没记录就用文件所有者)
            if(bPersonalView)
            {
                ASSERT(spView->GetPersonalViewUserIdsCount() == 1);
                appUserId = spView->GetPersonalViewUserId(0);
            }
            else
            {
                appUserId = spView->GetCreatorUserId();//若视图已经预埋了视图创建者的userId，则此值为空，否则此值为空，是正常的。
            }

            if(bSheetVeryHidden)
            {
                //深度隐藏的数据表(目前应该只有查询应用)，仅清洗给应用使用的那个视图(肯定开启了分享链接)，自带的底表（没开启分享链接）不清洗
                if(spView->GetSharedLink())
                {
                    updateDbSheetToAppItem.PushViewToAppItem(UpdateViewToAppItem(spView->GetId(), 
                        spView->GetType(), bPersonalView, appUserId, bNeedCreateSharedId, sharedId));
                    break;
                }
            }
            else
            {
                updateDbSheetToAppItem.PushViewToAppItem(UpdateViewToAppItem(spView->GetId(),
                    spView->GetType(), bPersonalView,  appUserId, bNeedCreateSharedId, sharedId));
            }
        }
        if(!updateDbSheetToAppItem.IsEmpty())
            updateDbSheetToAppMgr.PushDbSheetToAppItem(updateDbSheetToAppItem);

	}

    return S_OK;
}

//赋值sharedId列表
static HRESULT SetSharedIds(UpdateDbSheetToAppMgr& updateDbSheetToAppMgr, VarObj& response)
{
    HRESULT hr= E_FAIL;
    if(!response.has("sharedIds"))
    {
        WOLOG_ERROR << "[SetSharedIds] param error";
        return E_FAIL;
    }
    
    binary_wo::VarObj sharedIds = response.get_s("sharedIds");
    for (int i = 0; i < sharedIds.arrayLength_s(); i++)
    {
        binary_wo::VarObj item = sharedIds.at_s(i);
        if(!item.has("sheetStId") || !item.has("sharedIdsInfo"))
        {
            WOLOG_ERROR << "[SetSharedIds] param error";
            return E_FAIL;
        }

        UINT sheetStId = item.field_uint32("sheetStId");
        binary_wo::VarObj sharedIdsInfo = item.get_s("sharedIdsInfo");

        for (int j = 0; j < sharedIdsInfo.arrayLength_s(); j++)
        {
            binary_wo::VarObj sharedIdInfoItem = sharedIdsInfo.at_s(j);
            if(!sharedIdInfoItem.has("viewId") || !sharedIdInfoItem.has("sharedId"))
            {
                WOLOG_ERROR << "[SetSharedIds] param error";
                return E_FAIL;
            }

            PCWSTR viewIdStr = sharedIdInfoItem.field_str("viewId");
            EtDbId viewId = INV_EtDbId;
            hr =  _appcore_GainDbSheetContext()->DecodeEtDbId(viewIdStr, &viewId);
            if (FAILED(hr))
            {
                WOLOG_ERROR << "[SetSharedIds] param parse viewId failed";
                return hr;
            }
			PCWSTR sharedId = sharedIdInfoItem.field_str("sharedId");
            if(!updateDbSheetToAppMgr.ModifySharedId(sheetStId, viewId, sharedId))
            {
                WOLOG_ERROR << "[SetSharedIds] ModifySharedId failed";
                return E_FAIL;
            }
        }
    }
    return S_OK;
}

//收集所有需要清洗的sheet和view，向服务端获取准备sharedId
static HRESULT FetchSharedIdsFromServer(wo::KEtWorkbook* pWorkbook, UpdateDbSheetToAppMgr& updateDbSheetToAppMgr)
{
    for(auto it = updateDbSheetToAppMgr.m_updateSheets.begin(); it != updateDbSheetToAppMgr.m_updateSheets.end(); it++)
    {
        const UpdateDbSheetToAppItem& updateDbSheetToAppItem = it->second;
        for(auto viewIt = updateDbSheetToAppItem.m_updateViews.begin();
            viewIt != updateDbSheetToAppItem.m_updateViews.end(); viewIt++)
        {
            const UpdateViewToAppItem& updateViewToAppItem = viewIt->second;
            if(updateViewToAppItem.m_bNeedCreateSharedId)
            {
                kfc::ks_wstring sharedId = __X("");
                wo::DbSheet::FetchSharedIdForNotDirect(updateViewToAppItem.m_viewTp, &sharedId);
                if (sharedId.empty())
                {
                    WOLOG_ERROR << "[FetchSharedIdsFromServer] fetchSharedId from server is empty error!";
                    return E_FAIL;
                }
                
                if (!updateDbSheetToAppMgr.ModifySharedId(updateDbSheetToAppItem.m_sheetStId, updateViewToAppItem.m_viewId, sharedId.c_str()))
                {
                    WOLOG_ERROR << "[FetchSharedIdsFromServer] ModifySharedId failed";
                    return E_FAIL;
                }
            }
        }
    }
    return S_OK;
}

//收集所有需要清洗的sheet和view，准备sharedId
static HRESULT FetchAndSetSharedIds(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, UpdateDbSheetToAppMgr& updateDbSheetToAppMgr, VarObj& param)
{
    if(ctx->isExecDirect())
    {
        return SetSharedIds(updateDbSheetToAppMgr, param);
    }
    else
    {
        return FetchSharedIdsFromServer(pWorkbook, updateDbSheetToAppMgr);
    }
}

//把视图描述给清洗为视图公告
static HRESULT ConvertViewDescription2Notice(IDBSheetView* pView)
{
    //Todo:后面如果前端调整了公告的json结构，内核这里也得一起调整
    const QString textKey("text");
    QString strViewDescription = QString::fromUtf16(pView->GetDescription());
    QJsonObject noticeObj;
    
    QString strOldNotice = QString::fromUtf16(pView->GetNotice());
    if(!strOldNotice.isEmpty())
    {
        QJsonParseError jsonError;
        QJsonDocument jsonDocument = QJsonDocument::fromJson(strOldNotice.toUtf8(), &jsonError);
        if (jsonError.error != QJsonParseError::NoError)
        {
            WOLOG_ERROR << "[ConvertViewDescription2Notice] parse view old notice json fail!";
            return E_FAIL;
        }
        
        noticeObj = jsonDocument.object();
        if(noticeObj.contains(textKey))
        {
            //把视图描述给追加到原本的公告text字段最后
            QString noticeText = noticeObj.value(textKey).toString();
            if(!noticeText.isEmpty())
            {
                //追加一个换行到最后
                noticeText.push_back('\n');
            }
            noticeText += strViewDescription;
            noticeObj[textKey] = QJsonValue(noticeText);
        }
    }
    else
    {
        noticeObj.insert(textKey, QJsonValue(strViewDescription));
    }
    
    QString noticeStr(QJsonDocument(noticeObj).toJson());
    HRESULT hr = pView->SetNotice(krt::utf16(noticeStr));
    return hr;
}

//对视图进行升级为新应用
static HRESULT ExecUpdateAppVersionForView(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, UINT sheetStId, UpdateViewToAppItem& updateViewToAppItem)
{
    HRESULT hr = E_FAIL;
    IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
    IDX sheetIdx = INVALIDIDX;
    pBook->STSheetToRTSheet(sheetStId, &sheetIdx);

    long sheetCount = 0;
    ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
    spWorksheets->get_Count(&sheetCount);
    if(sheetIdx < 0 || sheetIdx >= sheetCount)
    {
        WOLOG_ERROR << "[UpdateAppVersionForView] sheetIdx < 0 || sheetIdx >= sheetCount error";
        return E_FAIL;
    }

    ISheet* pSheet = spWorksheets->GetSheetItem(sheetIdx)->GetSheet();
    if (!pSheet || !pSheet->IsDbSheet())
        return E_FAIL;

    //遍历所有的view
    ks_stdptr<IDBSheetViews> spViews;
    hr = wo::DbSheet::GetDBSheetViews(pSheet, &spViews);
    if(FAILED(hr) || !spViews)
        return E_FAIL;

    EtDbId viewId  = updateViewToAppItem.m_viewId;
    ks_stdptr<IDBSheetView> spView;
    spViews->GetItemById(viewId, &spView);
    if (!spView)
        return E_FAIL;

    
    //将view洗为应用sheet。
    UINT airAppSheetStId = 0;
	EtDbId airAppId = INV_EtDbId;
    ks_wstring airAppName;

    ks_wstring setAppName = spView->GetName();
    hr = CreateAppViewHelper::createAirApp(pWorkbook, spView, setAppName, sheetIdx, airAppName, airAppSheetStId, airAppId);
    if(FAILED(hr))
    {
        WOLOG_ERROR << "[UpdateAppVersionForView] createAirApp error";
        return E_FAIL;
    }
    if(!updateViewToAppItem.SetAirAppInfo(airAppSheetStId, airAppId, airAppName))
    {
        WOLOG_ERROR << "[UpdateAppVersionForView] airAppSheetStId or airAppId invalid";
        return E_FAIL;
    }
    
    //视图描述清洗为视图公告
    hr = ConvertViewDescription2Notice(spView);
    if(FAILED(hr))
    {
        WOLOG_ERROR << "[UpdateAppVersionForView] ConvertViewDescription2Notice error";
        return E_FAIL;
    }

    if(updateViewToAppItem.m_bPersonalView)
    {
        //若本身是个人视图，清洗后，移除个人视图属性。
        hr = spView->ClearPersonalView();
        if(FAILED(hr))
        {
            WOLOG_ERROR << "[UpdateAppVersionForView] SetPersonalView error";
            return E_FAIL;
        }
    }
    
    
    if(updateViewToAppItem.m_bNeedCreateSharedId)
    {
        //则开启/重置分享视图
        PCWSTR sharedId = updateViewToAppItem.m_sharedId.c_str();
        ASSERT(sharedId);
        hr = ExecShareView(ctx, pSheet->GetStId(), viewId, sharedId, airAppSheetStId, airAppId, DBDFT_Default);
        return hr;
    }
    else
    {
        IDBSharedLinkView* pSharedLink = spView->GetSharedLink();
        ASSERT(pSharedLink);
        //对原本已有的分享链接sharedLink设置应用有关的属性
        pSharedLink->SetIsUsedForApp(TRUE);
        pSharedLink->SetAppSheetStId(airAppSheetStId);
        pSharedLink->SetAppId(airAppId);
    }
    return S_OK;
}

//应用升级执行
static HRESULT ExecUpdateAppVersion(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, UpdateDbSheetToAppMgr& updateDbSheetToAppMgr)
{
    HRESULT hr = E_FAIL;
    for(auto sheetStId:updateDbSheetToAppMgr.m_updateSheetStIdOrders)
    {
        ASSERT(updateDbSheetToAppMgr.m_updateSheets.find(sheetStId) != updateDbSheetToAppMgr.m_updateSheets.end());
        UpdateDbSheetToAppItem& updateDbSheetToAppItem = updateDbSheetToAppMgr.m_updateSheets.find(sheetStId)->second;
        //m_updateViewIdOrders是按照视图本身的顺序
        //由于ExecUpdateAppVersionForView新建appsheet每次都是插入追加到当前dbsheet之后的下一个位置的，
        //这样就会导致m_updateViewIdOrders排在后面的view清洗出来的appsheet会在排在前面的view清洗出来的appsheet之前，不符合预期，因此我们需要倒序遍历。
        for(auto viewIt=updateDbSheetToAppItem.m_updateViewIdOrders.rbegin(); viewIt != updateDbSheetToAppItem.m_updateViewIdOrders.rend(); ++viewIt)
        {
            EtDbId viewId = *viewIt;
            ASSERT(updateDbSheetToAppItem.m_updateViews.find(viewId) != updateDbSheetToAppItem.m_updateViews.end());
            UpdateViewToAppItem& updateViewToAppItem = updateDbSheetToAppItem.m_updateViews.find(viewId)->second;
            hr = ExecUpdateAppVersionForView(pWorkbook, ctx, sheetStId, updateViewToAppItem);
            if(FAILED(hr))
            {
                WOLOG_ERROR << "[ExecUpdateAppVersion] UpdateAppVersionForView failed";
                return hr;
            }
        }
    }
    return S_OK;
}

//应用升级版本
HRESULT updateAppVersion(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, UpdateDbSheetToAppMgr& updateDbSheetToAppMgr, VarObj& param)
{
    updateAppVersionResGuard guard(ctx);
    // 仅ksheet才需要清洗
    if (!pWorkbook->GetBMP()->bKsheet)
    {
        guard.UpdateResult(true, nullptr);
        WOLOG_INFO << "[UpdateAppVersion] Not ksheet, skip to updateAppVersion!";
        return S_OK;
    }

    std::unordered_set<ks_wstring, STR_HASH> resetSharedIdsSet;
    if(param.has("resetSharedIds"))
    {
        VarObj resetSharedIdsArr = param.get("resetSharedIds");
        for (int i = 0; i < resetSharedIdsArr.arrayLength_s(); ++i)
        {
            PCWSTR resetSharedId = resetSharedIdsArr.item_str(i);
            resetSharedIdsSet.insert(resetSharedId);
        }
    }

    HRESULT hr = InitUpdateDbSheetToAppMgr(pWorkbook, updateDbSheetToAppMgr, resetSharedIdsSet);
    if(FAILED(hr))
    {
        WOLOG_ERROR << "[UpdateAppVersion] InitUpdateDbSheetToAppMgr fialed";
        return hr;
    }

    if(updateDbSheetToAppMgr.IsEmpty())
    {
        guard.UpdateResult(true, nullptr);
        WOLOG_INFO << "[UpdateAppVersion] Detect nothing need updateAppVersion, skip it!";
        return S_OK;
    }

    hr = FetchAndSetSharedIds(pWorkbook, ctx, updateDbSheetToAppMgr, param);
    if(FAILED(hr))
    {
        WOLOG_ERROR << "[UpdateAppVersion] FetchAndSetSharedIds fialed";
        return hr;
    }

    bool bFetchAllSharedIds = updateDbSheetToAppMgr.CheckFetchAllSharedIds();
    if(!bFetchAllSharedIds)
    {
        WOLOG_ERROR << "[UpdateAppVersion] CheckFetchAllSharedIds fialed";
        return E_FAIL;
    }

    //屏蔽权限检查，因为应用清洗过程中会创建分享链接，但此时还没机会到task的postExecute去对views执行update，此时权限没准备好。
    ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
    IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<IUnknown> spUnknown;
	pBook->GetExtDataItem(edDBUserGroups, &spUnknown);
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	spDBUserGroups->GetJudgement(&spProtectionJudgement);
    wo::DbSheet::DisableDbProtectScope disPtScope(spProtectionJudgement);

    //升级应用
    hr = ExecUpdateAppVersion(pWorkbook, ctx, updateDbSheetToAppMgr);
    if(FAILED(hr))
    {
        WOLOG_ERROR << "[UpdateAppVersion] ExecUpdateAppVersion fialed";
        return hr;
    }

    if(!ctx->isExecDirect())
    {
        //把回调的参数给保存起来，回放命令时可以复用。
        updateDbSheetToAppMgr.AddToParam(param);
        ctx->setIsRealTransform(true);
        //把创建的应用结果给返回给服务端

        guard.UpdateResult(true, &updateDbSheetToAppMgr);
    }
    updateDbSheetToAppMgr.LogInfo();
    return hr;
}

HRESULT UpdateAppVersion(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, UpdateDbSheetToAppMgr& updateDbSheetToAppMgr, VarObj& param)
{
    wo::util::CallTimeStat callTime("UpdateAppVersion", "DoExec");
    WOLOG_INFO << "[UpdateAppVersion] UpdateAppVersion start";
    HRESULT hr = updateAppVersion(pWorkbook, ctx, updateDbSheetToAppMgr, param);
    if (SUCCEEDED(hr))
    {
        if (pWorkbook->GetBMP()->bKsheet)
        {
            ks_stdptr<IUnknown> spUnk;
            pWorkbook->GetCoreWorkbook()->GetBook()->GetExtDataItem(edFileVersion, &spUnk);
            ks_stdptr<IKEtFileVersion> spEtFileVersion = spUnk;
            if (spEtFileVersion->GetKSheetBookVersion() < wo::KSheet_Book_Version_AppSheet)
                spEtFileVersion->SetKSheetBookVersion(wo::KSheet_Book_Version_AppSheet);
        }
        pWorkbook->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(pWorkbook->GetCoreWorkbook());
        WOLOG_INFO << "[UpdateAppVersion] UpdateAppVersion succeed";
    }
    else
    {
        WOLOG_ERROR << "[UpdateAppVersion] UpdateAppVersion fialed error!";
    }
    return hr;
}

bool IsNeedUpdateAppVersion(wo::KEtWorkbook* pWorkbook)
{
    // 仅ksheet才需要清洗
    if (!pWorkbook->GetBMP()->bKsheet)
        return false;

    ks_stdptr<IUnknown> spUnk;
    pWorkbook->GetCoreWorkbook()->GetBook()->GetExtDataItem(edFileVersion, &spUnk);
    ks_stdptr<IKEtFileVersion> spEtFileVersion = spUnk;
    return spEtFileVersion->GetKSheetBookVersion() == wo::KSheet_Book_Version_Base;
}

}