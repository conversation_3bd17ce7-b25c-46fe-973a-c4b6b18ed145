﻿#include "etstdafx.h"
#include "database_field_modifier.h"
#include "database_field_context.h"
#include "kfc/et_numfmt_str.h"
#include "applogic/et_apihost.h"
#include "appcore/et_appcore_basic_itf.h"
#include "helpers/protection_helper.h"
#include "persist/persist_helper.h"
#include "kso/l10n/et/et_styles.h"
#include "util.h"
#include "etcore/et_core_utility.h"
#include "database_utils.h"
#include "helpers/qrlabel_helper.h"

namespace wo
{
namespace Database
{

static inline DVErrorStyle MapErrorStyle(PCWSTR str)
{
  if (0 == xstrcmp(str, __X("dvesStop"))) {
    return dvesStop;
  } else if (0 == xstrcmp(str, __X("dvesWarning"))) {
    return dvesWarning;
  } else if (0 == xstrcmp(str, __X("dvesInfo"))) {
    return dvesInfo;
  }

  return dvesStop;
}

namespace
{

class ForceIgnoreFilterHelper
{
public:
    ForceIgnoreFilterHelper(etoldapi::Range *pRange)
        : m_spRange(pRange)
    {
        m_spRange->ForceIgnoreFilter(TRUE);
    }
    ~ForceIgnoreFilterHelper()
    {
        m_spRange->ForceIgnoreFilter(FALSE);
    }
private:
    ks_stdptr<etoldapi::Range> m_spRange;
};

}

// ================== FieldValidationModifier ==================
FieldValidationModifier::FieldValidationModifier(DVValueType type,
                                                PCWSTR formula,
                                                DV_OPERATOR op,
                                                BOOL fShowErrorMsg,
                                                DVErrorStyle errStyle)
    : m_formula(formula)
{
    InitDv();
    m_dv.DVType = type;
    m_dv.Operator = op;
    m_dv.fShowErrorMsg = fShowErrorMsg;
    m_dv.ErrStyle = errStyle;
}

void FieldValidationModifier::InitDv()
{
    m_dv.fStrLookup = FALSE;
    m_dv.fAllowBlank = TRUE;
    m_dv.fSuppressCombo = TRUE;
    m_dv.mdImeMode = FALSE;
    m_dv.fshowInputMsg = FALSE;
    m_dv.bsFormula1 = NULL;
    m_dv.bsFormula2 = NULL;
    m_dv.pcwcsInputTitle = __X("");
    m_dv.pcwcsErrorTitle = __X("");
    m_dv.pcwcsInputText = __X("");
    m_dv.pcwcsErrorText = __X("");

    m_dv.DVType = dvvtAnyValue;
    m_dv.Operator = dvoBetween;
    m_dv.fShowErrorMsg = FALSE;
    m_dv.ErrStyle = dvesInfo;
    m_dv.pcwcsExtId = NULL;
}

HRESULT FieldValidationModifier::SetInner(FieldContext *pContext, const RANGE &rg, BSTR formula)
{
    HRESULT hr = S_OK;
    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    if (spRange == nullptr)
        return E_FAIL;

    ks_stdptr<Validation> spValidation;
    spRange->get_Validation(&spValidation);
    if (spValidation == nullptr)
        return E_FAIL;

    ks_stdptr<IValidationInfo> spValidationInfo = spValidation;
    if (spValidationInfo == nullptr)
        return E_FAIL;

    SetDVReturnCode code = errNone;
    m_dv.bsFormula1 = formula;

    binary_wo::VarObj extObj = pContext->GetExtObj();
    if (m_dv.fShowErrorMsg)
    {
        if (extObj.has("validationErrorTitle"))
            m_dv.pcwcsErrorTitle = extObj.field_str("validationErrorTitle");
        if (extObj.has("validationErrorText"))
            m_dv.pcwcsErrorText = extObj.field_str("validationErrorText");
    }

    EnableProtectCompileFmla compileFmlaEnabler;
    hr = spValidationInfo->SetValidation(&m_dv, FALSE, FALSE, &code);
    pContext->SetDVCode(code);
    return hr;
}

HRESULT FieldValidationModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    return SetInner(pContext, rg, m_formula);
}

HRESULT FieldValidationModifier::Clear(FieldContext *pContext, const RANGE &rg)
{
    HRESULT hr = S_OK;
    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    if (spRange == nullptr)
        return E_FAIL;

    ks_stdptr<Validation> spValidation;
    spRange->get_Validation(&spValidation);
    if (spValidation == nullptr)
        return E_FAIL;

	return spValidation->Delete(); // 清除数据有效性
}

HRESULT FieldValidationModifier::getCellRefStr(ROW row, COL col, ks_bstr &bstr)
{
    WCHAR strBuf[20] = {0};
    size_t bufLen = countof(strBuf);
    ASSERT(bufLen >= 11);
    size_t len = ColumnIndex_Num2Str(col, strBuf, countof(strBuf));
    len += RowIndex_Num2Str(row, strBuf + len, bufLen - len);
    ASSERT(len < bufLen);
    bstr.assign(strBuf, len);
    return S_OK;
}

HRESULT FieldValidationModifier::getColRefStr(COL col, ks_bstr &bstr)
{
    WCHAR strBuf[20] = {0};
    size_t bufLen = countof(strBuf);
    ASSERT(bufLen >= 11);
    size_t len = ColumnIndex_Num2Str(col, strBuf, countof(strBuf));
    ASSERT(len < bufLen);
    bstr.assign(strBuf, len);
    return S_OK;
}

HRESULT FieldValidationModifier::CheckPermission(FieldContext *pContext, const RANGE &)
{
    return S_OK;
}
// ================== FieldValidationModifier ==================

// ================== FieldTemplateFormulaValidationModifier ==================
FieldTemplateFormulaValidationModifier::FieldTemplateFormulaValidationModifier(PCWSTR formulaTemplate,
                                                                                BOOL fShowErrorMsg,
                                                                                DVErrorStyle errStyle)
    : FieldValidationModifier(dvvtFormula, NULL, dvoBetween, fShowErrorMsg, errStyle)
    , m_formulaTemplate(QString::fromUtf16(formulaTemplate))
{}
// ================== FieldTemplateFormulaValidationModifier ==================

// ================== FieldRefFormulaValidationModifier ==================
FieldRefFormulaValidationModifier::FieldRefFormulaValidationModifier(PCWSTR formulaTemplate,
                                                                    BOOL fShowErrorMsg,
                                                                    DVErrorStyle errStyle)
    : FieldTemplateFormulaValidationModifier(formulaTemplate, fShowErrorMsg, errStyle)
{}

HRESULT FieldRefFormulaValidationModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    ks_bstr bsRefCell;
    getCellRefStr(rg.RowFrom(), rg.ColFrom(), bsRefCell);
    ks_bstr bsFormula(krt::utf16(m_formulaTemplate.arg(QString::fromUtf16(bsRefCell))));
    return SetInner(pContext, rg, bsFormula);
}

// ================== FieldAIFormulaFillModifier ==================
FieldAIFormulaFillModifier::FieldAIFormulaFillModifier(PCWSTR formulaTemplate,
                                                                    BOOL fShowErrorMsg,
                                                                    DVErrorStyle errStyle)
    : FieldRefFormulaValidationModifier(formulaTemplate, fShowErrorMsg, errStyle)
{}

HRESULT FieldAIFormulaFillModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    ASSERT(rg.SheetFrom() == rg.SheetTo());
    IKWorksheet* ws = pContext->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    if (ws == nullptr) {
       return E_FAIL;
    }
    IEtRevisionContext *ctx = pContext->GetRevisionContext();
    if (ctx == nullptr)
        return E_FAIL;
    ks_stdptr<IBookOp> ptrBookOp;
    pContext->GetBook()->GetOperator(&ptrBookOp);
    if (ptrBookOp == nullptr)
        return E_FAIL;    
    RANGE ur(pContext->GetBook()->GetBMP());
    ws->GetUsedRange(&ur);
    RANGE ref(pContext->GetBook()->GetBMP());
    // RANGE 有效性判断 
    ROW rowFrom = rg.RowFrom();
    ref.SetCell(rg.SheetFrom(), rowFrom, rg.ColFrom());
    ROW rowTo = std::min(ur.RowTo(), rg.RowTo());
    if (rowTo < rowFrom)
        return E_FAIL;
    ref.SetRowTo(rowTo);

    VarObj obj = pContext->GetExtObj();
    if (!obj.has("funcName"))
        return E_FAIL;
    if (!obj.has("refCol"))    
        return E_FAIL;
    if (!obj.has("cond"))    
        return E_FAIL;

    PCWSTR funcName = obj.field_str("funcName");
    if (funcName == nullptr)
        return E_FAIL;
    VarObj cols = obj.get_s("refCol");
    INT32 len = cols.arrayLength();
    INT32 count = 0;
    bool hasWarnLimit = false;
    INT32 limitCount = _kso_GetWoEtSettings()->GetUDFSingleRequestLimit();
    std::unordered_set<UINT32> ignRowSet;
    if (obj.has("ignoreRows"))
    {
        VarObj irObj = obj.get("ignoreRows");
        if (irObj.type() == binary_wo::typeArray) 
        {
            for (int i = 0; i < irObj.arrayLength(); i++) 
            {
                ignRowSet.insert(irObj.item_int32(i));
            }
        }
    }
    // 获取转换前后的常规格式,为了求稳，前后两种都获取一下
    // 通过Xls_BUILDIN_NF_00取是不会为null的
    LPCWSTR gnrFmt1 = kfc::nf::_XNFGetExcelStr(Xls_BUILDIN_NF_00);
    ks_bstr gnrFmt2;
    gExcel2ETNumFmt(gnrFmt1, &gnrFmt2);
    // 确认函数在一列中是相同的，所以在外面处理就行了
    QStringList qNameList = krt::fromUtf16(funcName).split("-");
    for (int r = ref.RowFrom(); r <= ref.RowTo(); r++)
    {
        if (!ignRowSet.empty() && ignRowSet.find(r) != ignRowSet.end()) 
        {
            ignRowSet.erase(r);
            continue;
        }
        CELL cell = {0};
        cell.row = r;
        cell.col = ref.ColFrom();
        RANGE mergedRect(ref);    
        app_helper::GetMergeCell(ws, cell, mergedRect);
        if (cell.row != mergedRect.RowFrom() || cell.col != mergedRect.ColFrom())
            continue;
        // 最大支持5000(可配置)个公式。如果之前引用列的有效区域不连续（极端情况引用列前5000行都是空白），
        // 用户填充引用列的所有单元格后再进行配置AI列，原公式都得变成新配置（突破5000的限制）
        if (count >= limitCount)
        {
            if (!hasWarnLimit)
            {
                VarObj outExtObj = pContext->GetOutExtObj();
                outExtObj.add_field_int32("aiLimit", limitCount);
                hasWarnLimit = true;
            }
            BOOL bFormula = FALSE;
		    ptrBookOp->GetCellFormulaFlags(rg.SheetFrom(), r, rg.ColFrom(), &bFormula, NULL);
		    if (!bFormula)
			    continue;
        }    
        QString colStrs = "TEXTJOIN(";
        colStrs.append("CHAR(10)");
        colStrs.append(",TRUE,");
        bool needInstAI = false;
        for (int i = 0; i < len; i++)
        {
            INT32 col = cols.item_int32(i);
            const_token_ptr pToken = nullptr;
            ptrBookOp->GetCellValue(rg.SheetFrom(), r, col, &pToken);
            if ((needInstAI = (pToken != nullptr)))
                break;
        }
        //AI列首个单元格(第二行)填充AI公式，AI列其它单元格依据引用列是否有内容来填充。
        if (!needInstAI)
            continue;
        for (int i = 0; i < len; i++)
        { 
            INT32 col = cols.item_int32(i);
            const XF* pxf = nullptr;
            ptrBookOp->GetCellFormat(rg.SheetFrom(), r, col, &pxf, NULL);
            ks_bstr tmpCell;
            getCellRefStr(r, col, tmpCell);
            if (i != 0)
                colStrs.append(",");
            QString fmt = krt::fromUtf16(pxf->pNumFmt->fmt);
            QString regStr = "\"";
            QString repStr = "\"\"";
            fmt = fmt.replace(QRegExp(regStr), repStr);
            ks_bstr kFmt(krt::utf16(fmt));
            // 如果是常规格式，用LEFT把字符读出来，TEXT有坑，会把字符转成数字
            // gnrFmt2是我们常用的格式说明，放前面判断就行
            if (kFmt.isEqual(gnrFmt2) || kFmt.isEqualString(gnrFmt1))
            {
                colStrs.append("LEFT(");    
                colStrs.append(QString::fromUtf16(tmpCell));
                colStrs.append(", 2^20)");
            }
            // 不是常规格式，就用TEXT了，非常规格式，TEXT转数字肯定失败，所以不影响给aigc
            else
            {
                colStrs.append("TEXT(");    
                colStrs.append(krt::fromUtf16(tmpCell));
                colStrs.append(",\"");
                colStrs.append(fmt);
                colStrs.append("\")");
            }
            if (i == len - 1)
                colStrs.append(")");
        }
        // 自定义函数格式：funcName-promptId
        PCWSTR cond = obj.field_str("cond");
        ks_bstr bsFormula(krt::utf16(m_formulaTemplate.
                            arg(qNameList.count() > 1 ? krt::utf16(qNameList.at(0)) : funcName).
                            arg(krt::utf16(colStrs)).
                            arg(krt::fromUtf16(cond))));
        RANGE curCell(pContext->GetBook()->GetBMP());
        curCell.SetCell(rg.SheetFrom(), r, rg.ColFrom());
        CHECK_PROTECTION_FORMULA(curCell, bsFormula, ctx);
        ks_stdptr<IRangeInfo> host = pContext->CreateRangeObj(curCell);
        host->SetFormula(bsFormula, &curCell);
        XFMASK xfmask = {};
        const XF* pxf = nullptr;
        ptrBookOp->GetCellFormat(curCell.SheetFrom(), curCell.RowFrom(), curCell.ColFrom(), &pxf, &xfmask);
        // 设置公式成功,如果发现水平默认不居中，就设置为居中。
        if (!xfmask.inc_alcH)
        {
            XF xf;
            pxf == nullptr ? xf = {0} : xf = *pxf;
            xfmask.inc_alcH = 1;
            xf.alcH = haCenter;
            ptrBookOp->SetCellFormat(curCell, xfmask, xf);
        }
        count++;
    }
    util::CollectAiColInfo(count, pContext->GetBook());
    return S_OK;
}
// ================== FieldAIFormulaValidationModifier ==================

FieldAIFormulaValidationModifier::FieldAIFormulaValidationModifier(PCWSTR formulaTemplate,
                                                                    BOOL fShowErrorMsg,
                                                                    DVErrorStyle errStyle)
    : FieldRefFormulaValidationModifier(formulaTemplate, fShowErrorMsg, errStyle)
{}

HRESULT FieldAIFormulaValidationModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    VarObj obj = pContext->GetExtObj();
    if (!obj.has("funcName"))
        return E_FAIL;
    if (!obj.has("refCol"))    
        return E_FAIL;
    if (!obj.has("cond"))    
        return E_FAIL;
    
    ks_bstr bsRefCell;
    getCellRefStr(rg.RowFrom(), rg.ColFrom(), bsRefCell);    
    PCWSTR funcName = obj.field_str("funcName");
    VarObj cols = obj.get_s("refCol");
    QString colStrs = "TEXTJOIN(";
    QString colStrs2 = "";
    QString fml2 = krt::fromUtf16(__X("\"%1\"&%2&\"%3\""));
    colStrs.append("CHAR(10)");
    colStrs.append(",TRUE, ");
    INT32 len = cols.arrayLength();
    for (int i = 0; i < len; i++)
    {
        ks_bstr tmpCell;
        getCellRefStr(rg.RowFrom(), cols.item_int32(i), tmpCell);
        if (i != 0)
            colStrs.append(",");
        colStrs.append(QString::fromUtf16(tmpCell));
        if (i == len - 1)
            colStrs.append(")");
        ks_bstr tmpCol;
        getColRefStr(cols.item_int32(i), tmpCol);    
        colStrs2.append("$").
            append(krt::fromUtf16(tmpCol)).
            append(":").
            append("$").
            append(krt::fromUtf16(tmpCol));
        if (i != len - 1)
            colStrs2.append("+");    
    }
    PCWSTR cond = obj.field_str("cond");
    if (obj.has("sampleId"))
        fml2 = fml2.append("&\"").append(QString::fromUtf16(obj.field_str("sampleId"))).append("\"");
    ks_bstr fml2Str(krt::utf16(fml2.arg(QString::fromUtf16(funcName)).arg(colStrs2).arg(QString::fromUtf16(cond))));
    ks_bstr bsFormula(krt::utf16(m_formulaTemplate.
                        arg(QString::fromUtf16(bsRefCell)).
                        arg(QString::fromUtf16(fml2Str))));
    IKWorksheet* ws = pContext->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    if (ws == nullptr) {
       return E_FAIL;
    }
    return SetInner(pContext, rg, bsFormula);    
}

// ================== FieldGenQRLabelFormulaFillModifier ==================
FieldGenQRLabelFormulaFillModifier::FieldGenQRLabelFormulaFillModifier()
        : FieldRefFormulaValidationModifier(__X("=GENQRLABEL(\"%1\",%2)"), FALSE, dvesStop)
{
}

QString FieldGenQRLabelFormulaFillModifier::GenerateQRFormula(ROW row, const GenQRLabelCol& qrCol)
{
    ks_bstr keyCellStr;
    getCellRefStr(row, qrCol.keyCol, keyCellStr);
    QString cellsStr("$");
    cellsStr.append(krt::fromUtf16(keyCellStr.c_str()));
    for (const COL refCol : qrCol.refCols)
    {
        cellsStr.append(",$");
        ks_bstr cellStr;
        getCellRefStr(row, refCol, cellStr);
        cellsStr.append(krt::fromUtf16(cellStr.c_str()));
    }
    return m_formulaTemplate.arg(qrCol.pageId.c_str()).arg(cellsStr);
}

HRESULT FieldGenQRLabelFormulaFillModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    ASSERT(rg.SheetFrom() == rg.SheetTo());
    IKWorksheet* pWorkSheet = pContext->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    if (!pWorkSheet)
        return E_FAIL;

    IEtRevisionContext *pCtx = pContext->GetRevisionContext();
    if (!pCtx)
        return E_FAIL;

    ks_stdptr<IBookOp> spBookOp;
    pContext->GetBook()->GetOperator(&spBookOp);
    if (!spBookOp)
        return E_FAIL;

    VarObj obj = pContext->GetExtObj();
    GenQRLabelCol qrCol;
    qrCol.col = rg.ColFrom();
    if (!Utils::ParseQRColFromVarObj(pContext->GetBook()->GetBMP(), obj, qrCol))
        return E_FAIL;

    RANGE usedRange(pContext->GetBook()->GetBMP());
    pWorkSheet->GetUsedRange(&usedRange);
    if (qrCol.headerRow >= usedRange.RowTo())
        return S_OK;

    std::unordered_set<ROW> fillRows;
    ISheet* pSheet = pWorkSheet->GetSheet();
    et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);
    // 在关键列中查找非空行
    class CollectNonEmptyRowAcpt : public ICellValueAcpt
    {
    public:
        explicit CollectNonEmptyRowAcpt(std::unordered_set<ROW>& rows) : m_rows(rows)
        {
        }

        STDPROC_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
        {
            DWORD tokenType = alg::GetExecTokenMajorType(pToken);
            if (tokenType == etexec::ETP_NONE)
                return 0;

            m_rows.insert(row);
            return 0;
        };
    private:
        std::unordered_set<ROW>& m_rows;
    };
    RANGE keyColRange(pContext->GetBook()->GetBMP());
    keyColRange.SetCell(rg.SheetFrom(), qrCol.headerRow + 1, qrCol.keyCol);
    keyColRange.SetRowTo(usedRange.RowTo());
    CollectNonEmptyRowAcpt collectNonEmptyRowAcpt(fillRows);
    spSheetEnum->EnumCellValue(keyColRange, &collectNonEmptyRowAcpt);

    // 在二维码列中查找公式为二维码的行
    class CollectQRRowAcpt : public ICellValueAcpt
    {
    public:
        CollectQRRowAcpt(std::unordered_set<ROW>& rows, IBookOp* pBookOp, IDX sheetIdx) : m_rows(rows), m_pBookOp(pBookOp), m_sheetIdx(sheetIdx)
        {
        }

        STDPROC_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
        {
            ks_stdptr<IFormula> spFormula;
            VS(m_pBookOp->GetCellFormula(m_sheetIdx, row, col, &spFormula, nullptr));
            if (!spFormula)
                return 0;
            if (qrlabel_helper::IsGenQRLabelFormula(spFormula))
                m_rows.insert(row);
            return 0;
        };
    private:
        std::unordered_set<ROW>& m_rows;
        IBookOp* m_pBookOp;
        IDX m_sheetIdx;
    };
    RANGE qrColRange(pContext->GetBook()->GetBMP());
    qrColRange.SetCell(rg.SheetFrom(), qrCol.headerRow + 1, qrCol.col);
    qrColRange.SetRowTo(usedRange.RowTo());
    CollectQRRowAcpt collectQRRowAcpt(fillRows, spBookOp, rg.SheetFrom());
    spSheetEnum->EnumCellValue(qrColRange, &collectQRRowAcpt);

    // 填充二维码公式
    for (ROW row: fillRows)
    {
        CELL cell = { row, qrCol.col};
        RANGE mergeRange(pContext->GetBook()->GetBMP());
        app_helper::GetMergeCell(pSheet, cell, mergeRange);
        if (cell.row != mergeRange.RowFrom() || cell.col != mergeRange.ColFrom())
            continue;

        QString formula = GenerateQRFormula(row, qrCol);
        RANGE cellRange(pContext->GetBook()->GetBMP());
        cellRange.SetCell(usedRange.SheetFrom(), row, qrCol.col);
        ks_stdptr<IRangeInfo> spRange = pContext->CreateRangeObj(cellRange);
        HRESULT hr = spRange->SetFormula(krt::utf16(formula), &cellRange);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

// ================== FieldGenQRLabelFormulaValidationModifier ==================
FieldGenQRLabelFormulaValidationModifier::FieldGenQRLabelFormulaValidationModifier()
        : FieldRefFormulaValidationModifier(
        __X("=IF(TRUE,IF(ISFORMULA(%1),LEFT(FORMULATEXT(%1),11)=\"=GENQRLABEL\",FALSE),%2)"), TRUE, dvesStop)
{
}

HRESULT FieldGenQRLabelFormulaValidationModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    VarObj obj = pContext->GetExtObj();
    Database::GenQRLabelCol qrCol;
    qrCol.col = rg.ColFrom();
    if (!Utils::ParseQRColFromVarObj(pContext->GetBook()->GetBMP(), obj, qrCol))
        return E_FAIL;

    QString headerRowStr = QString("$%1:$%1").arg(qrCol.headerRow + 1);
    ks_bstr keyColStr;
    getColRefStr(qrCol.keyCol, keyColStr);
    QString colsStr = QString("$%1:$%1").arg(krt::fromUtf16(keyColStr));
    for (const COL refCol : qrCol.refCols)
    {
        colsStr += "+";
        ks_bstr colStr;
        getColRefStr(refCol, colStr);
        colsStr += QString("$%1:$%1").arg(krt::fromUtf16(colStr));
    }
    PCWSTR pageId = obj.field_str("pageId");
    QString formula = QString("\"%1\"&%2+%3").arg(pageId).arg(headerRowStr).arg(colsStr);
    ks_bstr bsRefCell;
    getCellRefStr(rg.RowFrom(), rg.ColFrom(), bsRefCell);
    ks_bstr bsFormula(krt::utf16(m_formulaTemplate.arg(krt::fromUtf16(bsRefCell)).arg(formula)));
    IKWorksheet* ws = pContext->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    if (ws == nullptr) {
        return E_FAIL;
    }
    return SetInner(pContext, rg, bsFormula);
}

// ================== FieldRatingValidationModifier ==================
FieldRatingValidationModifier::FieldRatingValidationModifier(PCWSTR formulaTemplate,
                                                            BOOL fShowErrorMsg,
                                                            DVErrorStyle errStyle)
    : FieldRefFormulaValidationModifier(formulaTemplate, fShowErrorMsg, errStyle)
{}

HRESULT FieldRatingValidationModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    ks_bstr bsRefCell;
    getCellRefStr(rg.RowFrom(), rg.ColFrom(), bsRefCell);
    binary_wo::VarObj extObj = pContext->GetExtObj();
    int nRatings = 0;
    if (extObj.has("option"))
        nRatings = extObj.field_int32("option");
    else if (extObj.has("maxRating"))
        nRatings = extObj.field_int32("maxRating");
    if (nRatings == 0)
        return E_INVALIDARG;
    ks_bstr bsFormula(krt::utf16(m_formulaTemplate.arg(QString::fromUtf16(bsRefCell)).arg(nRatings)));
    return SetInner(pContext, rg, bsFormula);
}
// ================== FieldRatingValidationModifier ==================

// ================== FieldMultipleListValidationModifier ==================
FieldMultipleListValidationModifier::FieldMultipleListValidationModifier(BOOL fShowErrorMsg,
                                                                        DVErrorStyle errStyle)
    : FieldTemplateFormulaValidationModifier(__X("=IF(TRUE,\"%1\"<>0,\"MultipleList\")"), fShowErrorMsg, errStyle)
{}

HRESULT FieldMultipleListValidationModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    QStringList formula;
    binary_wo::VarObj extObj = pContext->GetExtObj();
    if (extObj.has("listItems"))
    {
        binary_wo::VarObj listItems = extObj.get_s("listItems");
        ASSERT(listItems.type() == binary_wo::typeArray);
        for (int i = 0; i < listItems.arrayLength_s(); i++)
        {
            binary_wo::VarObj item = listItems.at_s(i);
            WebStr value = item.field_str("value");
            formula << QString::fromUtf16(value);
        }
        if (formula.isEmpty())
            return E_INVALIDARG;

        ks_bstr bsFormula(krt::utf16(m_formulaTemplate.arg(formula.join(';'))));
        return SetInner(pContext, rg, bsFormula);
    }
    else
    {
        return E_INVALIDARG;
    }
}
// ================== FieldMultipleListValidationModifier ==================

// ================== FieldListValidationModifier ==================
FieldListValidationModifier::FieldListValidationModifier(BOOL fShowErrorMsg,
                                                        DVErrorStyle errStyle)
    : FieldValidationModifier(dvvtCustomList, NULL, dvoBetween, fShowErrorMsg, errStyle)
{}

HRESULT FieldListValidationModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    binary_wo::VarObj extObj = pContext->GetExtObj();
    if (extObj.has("listItems"))
    {
        QStringList formula;
        binary_wo::VarObj listItems = extObj.get_s("listItems");
        ASSERT(listItems.type() == binary_wo::typeArray);
        for (int i = 0; i < listItems.arrayLength_s(); i++)
        {
            binary_wo::VarObj item = listItems.at_s(i);
            WebStr value = item.field_str("value");
			if (i == 0 && value && __Xc('=') == *value)
				formula << QString("\'").append(krt::fromUtf16(value));
			else
            	formula << krt::fromUtf16(value);
        }
        if (formula.isEmpty())
            return E_DBSHEET_SELECT_ITEM_INVALID;
        ks_bstr bsFormula(krt::utf16(formula.join(',')));
        if (bsFormula.size() > 255)
            return E_ET_COMPILE_STRING_IN_FORMULA_TOO_LONG;
        return setInner(pContext, rg, bsFormula);
    }
    else if (extObj.has("formula"))
    {
		binary_wo::VarObj fmlaObj = extObj.get("formula");
		if (fmlaObj.type() != binary_wo::typeString)
			return E_INVALIDARG;
		ks_bstr bsFormula(fmlaObj.value_str());
		if (bsFormula.size() > 255)
            return E_ET_COMPILE_STRING_IN_FORMULA_TOO_LONG;
        return setInner(pContext, rg, bsFormula);
    }
    else
    {
        return E_INVALIDARG;
    }
    return S_OK;
}

HRESULT FieldListValidationModifier::setInner(FieldContext *pContext, const RANGE &rg, BSTR formula)
{
    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    if (spRange == nullptr)
        return E_FAIL;

    ks_stdptr<Validation> spValidation;
    spRange->get_Validation(&spValidation);
    if (spValidation == nullptr)
        return E_FAIL;

    ks_stdptr<IValidationInfo> spValidationInfo = spValidation;
    if (spValidationInfo == nullptr)
        return E_FAIL;

    SetDVReturnCode code = errNone;
    m_dv.bsFormula1 = formula;

    binary_wo::VarObj extObj = pContext->GetExtObj();
	if (extObj.has("allowBlank"))
		m_dv.fAllowBlank = extObj.field_bool("allowBlank") ? TRUE : FALSE;
	if (extObj.has("errorStyle"))
		m_dv.ErrStyle = MapErrorStyle(extObj.field_str("errorStyle"));
	if (extObj.has("showErrorMsg"))
		m_dv.fShowErrorMsg = extObj.field_bool("showErrorMsg") ? TRUE : FALSE;
	if (extObj.has("showInputMsg"))
		m_dv.fshowInputMsg = extObj.field_bool("showInputMsg") ? TRUE : FALSE;

    if (m_dv.fShowErrorMsg)
    {
        if (extObj.has("validationErrorTitle"))
            m_dv.pcwcsErrorTitle = extObj.field_str("validationErrorTitle");
        if (extObj.has("validationErrorText"))
            m_dv.pcwcsErrorText = extObj.field_str("validationErrorText");
    }

	if (m_dv.fshowInputMsg)
    {
        if (extObj.has("validationInputTitle"))
            m_dv.pcwcsInputTitle = extObj.field_str("validationInputTitle");
        if (extObj.has("validationInputText"))
            m_dv.pcwcsInputText = extObj.field_str("validationInputText");
    }

	m_dv.pcwcsExtId = NULL;
    if (extObj.has("ext"))
    {
        binary_wo::VarObj dvExtObj = extObj.get_s("ext");
        if (dvExtObj.type() == binary_wo::typeStruct)
        {
            ks_stdptr<IValidationExt> spValidationExt;
            InitValidationExt(dvExtObj, &spValidationExt);
            if (spValidationExt)
            {
                ks_stdptr<IBook> spBook = pContext->GetBook();
				ks_stdptr<IValidationExts> spValidationExts;
				spBook->GetExtDataItem(edBookDataValidtionExts, (IUnknown**)&spValidationExts);
                PCWSTR extid = spValidationExts->AddItem(spValidationExt);
                if (extid != NULL)
                    m_dv.pcwcsExtId = extid;
            }
        }
    }

    EnableProtectCompileFmla compileFmlaEnabler;
    HRESULT hr = spValidationInfo->SetValidation(&m_dv, FALSE, FALSE, &code);
    pContext->SetDVCode(code);
    return hr;
}

void FieldListValidationModifier::InitValidationExt(binary_wo::VarObj dvExtObj, IValidationExt** ppExt)
{
    if (dvExtObj.has("list"))
    {
        ks_stdptr<IValidationListExt> spList;
        _appcore_CreateObject(CLSID_KValidationListExt, IID_IValidationListExt, (void**)&spList);

        binary_wo::VarObj listObj = dvExtObj.get_s("list");
        if (listObj.has("multiSupport"))
        {
            spList->SetMultiSupport(listObj.field_bool("multiSupport"));
        }
        if (listObj.has("colorSupport"))
        {
            spList->SetColorSupport(listObj.field_bool("colorSupport"));
        }
        if (listObj.has("colors"))
        {
            binary_wo::VarObj colorsObj = listObj.get_s("colors");
            if (colorsObj.type() == binary_wo::typeArray)
            {
                for (int32 i = 0; i < colorsObj.arrayLength_s(); i++)
                {
                    binary_wo::VarObj colorItemObj = colorsObj.at(i);
                    if (colorItemObj.type() == binary_wo::typeStruct)
                    {
                        UINT32 idx = colorItemObj.field_uint32("index");
                        PCWSTR clr = colorItemObj.field_str("color");
						EtColor etColor;
						etColor.setARGB(Utils::CSTR2ARGB(clr));
                        spList->AddColor(idx, etColor);
                    }
                }
            }
        }
		ks_wstring id;
		QUuid uuid = QUuid::createUuid();
		id = krt::utf16(uuid.toString());
		spList->SetId(id.c_str());

		(*ppExt) = spList.detach();
    }
    else 
    {
        (*ppExt) = NULL;
    }
}

HRESULT FieldListValidationModifier::isValidRange(FieldContext* pContext, RANGE& rg)
{
    IBook* pBook = pContext->GetBook();
    IDX sheetIdx = rg.SheetFrom();
    IKWorksheets* pWorkSheets = pContext->GetWorksheets();
    IKWorksheet* pWorksheet = pWorkSheets->GetSheetItem(sheetIdx);
    ISheetProtection* pSheetProtection = pWorksheet->GetProtection();
    //区域权限判断
    if (pSheetProtection && pSheetProtection->IsRangeHasHiddenProperty(rg))
        return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
    // 下拉列表(多选)是否含有逗号
	binary_wo::VarObj extObj = pContext->GetExtObj();
	if (extObj.has("ext"))
	{
		binary_wo::VarObj dvExtObj = extObj.get_s("ext");
		if (dvExtObj.type() == binary_wo::typeStruct)
		{
			if (dvExtObj.has("list"))
			{
				binary_wo::VarObj listObj = dvExtObj.get_s("list");
				if (listObj.has("multiSupport") && listObj.field_bool("multiSupport"))
				{
					ks_stdptr<ISheet> spSheet;
					pBook->GetSheet(sheetIdx, &spSheet);
					if (Utils::IsCellTextHasSeparatorSymbol(spSheet, rg, __Xc(',')))
						return E_OPERATION_NOT_SUPPORTED_COMMA_IN_CUSTOM_LIST;
				}
			}
		}
	}
    
    return S_OK;
}

// 检查是否存在受影响的的单元格
class SyncUpdateCellCheckEnum : public ICellAcpt
{
public:
	SyncUpdateCellCheckEnum(IETStringTools *pStringTools, ISheet *pSheet, QStringList &strList, bool bMultiCompare)
		: m_pStringTools(pStringTools), m_pSheet(pSheet), m_strList(strList), m_bMultiCompare(bMultiCompare)
	{
	}
	STDPROC_(INT)
	Do(ROW row, COL col)
	{
		BOOL bFormula = FALSE;
		BOOL bCellUsed = m_pSheet->IsCellUsed(row, col, &bFormula, NULL);
		if (!bCellUsed || bFormula)
			return 0;

		ks_bstr bsText;
		m_pStringTools->GetCellText(m_pSheet, row, col, &bsText, NULL, -1, NULL);
		if (!bsText.empty())
		{
			PCWSTR text = bsText.c_str();
			for (int i = 0, cur = 0; text[i] != 0; ++i)
			{
				WCHAR next = text[i + 1];
				if (next == 0 || (m_bMultiCompare && next == __Xc(',')))
				{
					if (i >= cur)
					{
						for (QString str : m_strList)
						{
							int len = i + 1 - cur;
							if (str.length() == len && xstrncmp(krt::utf16(str), text + cur, len) == 0)
								return E_DATA_VALIDATION_CUSTOM_LIST_MODIFY_AFFECT_RANGE_VALUE;
						}
					}
					cur = i + 2;
				}
			}
		}
		return 0;
	}

protected:
	IETStringTools *m_pStringTools;
	ISheet *m_pSheet;
	QStringList m_strList;
	bool m_bMultiCompare;
};
// 同步更新单元格内容
class SyncUpdateCellReplaceEnum : public ICellAcpt
{
public:
	SyncUpdateCellReplaceEnum(IETStringTools *pStringTools, ISheet *pSheet, QStringList &oldStrList, QStringList &newStrList, bool bMultiCompare)
		: m_pStringTools(pStringTools), m_pSheet(pSheet), m_oldStrList(oldStrList), m_newStrList(newStrList), m_bMultiCompare(bMultiCompare)
	{
		ASSERT(oldStrList.size() == newStrList.size());
	}
	STDPROC_(INT)
	Do(ROW row, COL col)
	{
		BOOL bFormula = FALSE;
		BOOL bCellUsed = m_pSheet->IsCellUsed(row, col, &bFormula, NULL);
		if (!bCellUsed || bFormula)
			return 0;

		ks_bstr bsText;
		m_pStringTools->GetCellText(m_pSheet, row, col, &bsText, NULL, -1, NULL);
		if (!bsText.empty())
		{
			PCWSTR text = bsText.c_str();
			ks_wstring replaceText(__X(""));
			int n = 0; // 切片位置
			bool bHasChanged = false;	// 是否需要重设单元格内容
			for (int i = 0, cur = 0; text[i] != 0; ++i)
			{
				WCHAR next = text[i + 1];
				if (next == 0 || (m_bMultiCompare && next == __Xc(',')))
				{
					if (i >= cur)
					{
						bool bHitModify = false;	// 是否命中修改项
						int len = i + 1 - cur;
						for (int j = 0; j < m_oldStrList.size(); ++j)
						{
							QString oldStr = m_oldStrList[j];
							if (oldStr.length() == len)
							{
								if (xstrncmp(krt::utf16(oldStr), text + cur, len) == 0)
								{
									bHasChanged = true;
									bHitModify = true;
									if (n > 0)
										replaceText.append(__X(","));
									replaceText.append(krt::utf16(m_newStrList[j]));
									n++;
									break;
								}
							}
						}
						if (!bHitModify)
						{
							if (n > 0)
								replaceText.append(__X(","));
							replaceText.append(text+cur, len);
							n++;
						}
					}
					cur = i + 2;
				}
			}
			if (bHasChanged)
			{
				IDX iSht;
				m_pSheet->GetIndex(&iSht);
				RANGE rg(m_pSheet->GetBMP());
				rg.SetCell(iSht, row, col);
				return m_pSheet->LeakBook()->LeakOperator()->SetCellText(rg, replaceText.c_str(), m_bMultiCompare ? cvoForceText : cvoNormal);
			}
		}
		return 0;
	}

protected:
	IETStringTools *m_pStringTools;
	ISheet *m_pSheet;
	QStringList m_oldStrList;
	QStringList m_newStrList;
	bool m_bMultiCompare;
};

HRESULT FieldListValidationSyncUpdateBase::setInner(FieldContext *pContext, const RANGE &rg, SyncUpdateActionType actionType)
{
	binary_wo::VarObj extObj = pContext->GetExtObj();
	if (extObj.has("modifyItems"))
	{
		QStringList oldStrList;
		QStringList newStrList;
        binary_wo::VarObj modifyItems = extObj.get_s("modifyItems");
		ASSERT(modifyItems.type() == binary_wo::typeArray);
        for (int i = 0; i < modifyItems.arrayLength_s(); i++)
        {
            binary_wo::VarObj item = modifyItems.at_s(i);
			ASSERT(item.type() == binary_wo::typeStruct);
            WebStr oldStr = item.field_str("old");
            oldStrList << QString::fromUtf16(oldStr);
			 WebStr newStr = item.field_str("new");
            newStrList << QString::fromUtf16(newStr);
        }

		if (oldStrList.empty())
			return S_OK;

		IBook* pBook = pContext->GetBook();
    	IDX sheetIdx = rg.SheetFrom();
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(sheetIdx, &spSheet);
		ks_stdptr<IETStringTools> spStringTools;
		_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&spStringTools);
		if (!spStringTools)
			return E_FAIL;
		spStringTools->SetEnv(spSheet);
		et_sdptr<ISheetEnum> spSheetEnum;
		spSheet->CreateEnum(&spSheetEnum);

		bool bMultiSupport = false;	// 是否多选
		if (extObj.has("ext"))
		{
			binary_wo::VarObj dvExtObj = extObj.get_s("ext");
			if (dvExtObj.has("list"))
			{
				binary_wo::VarObj listObj = dvExtObj.get_s("list");
				if (listObj.has("multiSupport"))
				{
					bMultiSupport = listObj.field_bool("multiSupport");
				}
			}
		}

		if (actionType == CHECK)
		{
			SyncUpdateCellCheckEnum checkEnum(spStringTools, spSheet, oldStrList, bMultiSupport);
			return spSheetEnum->EnumCell(rg, &checkEnum);
		}
		if (actionType == REPLACE)
		{
			SyncUpdateCellReplaceEnum replaceEnum(spStringTools, spSheet, oldStrList, newStrList, bMultiSupport);
			return spSheetEnum->EnumCell(rg, &replaceEnum);
		}
	}
	return S_OK;
}

HRESULT FieldListValidationSyncUpdateFrontModifier::Set(FieldContext *pContext, const RANGE &rg)
{
	binary_wo::VarObj extObj = pContext->GetExtObj();
	bool bNeedCheckModify = extObj.has("listItems");
	bNeedCheckModify &= extObj.has("modifyItems");
	bNeedCheckModify &= !extObj.has("bNeedModify");

	// 同时满足时做前置校验：1.自定义序列 2.有修改项 3.未确认是否更新
	if (bNeedCheckModify)
		return setInner(pContext, rg, CHECK);
	return S_OK;
}

HRESULT FieldListValidationSyncUpdateFrontModifier::CheckPermission(FieldContext *pContext, const RANGE &rg)
{
	IEtRevisionContext *ctx = pContext->GetRevisionContext();
	if (ctx == nullptr)
		return E_FAIL;

	CHECK_PROTECTION_ALLOW_EDIT(rg, ctx);
    return S_OK;
}

HRESULT FieldListValidationSyncUpdateBackModifier::Set(FieldContext *pContext, const RANGE &rg)
{
	binary_wo::VarObj extObj = pContext->GetExtObj();
	bool bCanModify = extObj.has("listItems");
	bCanModify &= extObj.has("modifyItems");
	bCanModify &= extObj.has("bNeedModify") && extObj.field_bool("bNeedModify");

	// 同时满足时做后置同步更新：1.自定义序列 2.有修改项 3.已确认更新
	if (bCanModify)
		setInner(pContext, rg, REPLACE);
	return S_OK;
}

// ================== FieldListValidationModifier ==================

// ================== FieldNumberFormatModifier ==================
FieldNumberFormatModifier::FieldNumberFormatModifier()
    : m_format(nullptr)
{}

FieldNumberFormatModifier::FieldNumberFormatModifier(PCWSTR format)
    : m_format(format)
{}

FieldNumberFormatModifier::FieldNumberFormatModifier(int nfIndex)
    : m_format(kfc::nf::_XNFGetEtStr(nfIndex))
{}

HRESULT FieldNumberFormatModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    return setInner(pContext, rg, m_format);
}

HRESULT FieldNumberFormatModifier::Clear(FieldContext *pContext, const RANGE &rg)
{
    ks_bstr bsFormula(kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_G));
    return setInner(pContext, rg, bsFormula);
}

HRESULT FieldNumberFormatModifier::setInner(FieldContext *pContext, const RANGE &rg, BSTR format)
{
    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    if (spRange == nullptr)
        return E_FAIL;

    ForceIgnoreFilterHelper hlp(spRange);
    return spRange->put_NumberFormat(format);
}
// ================== FieldNumberFormatModifier ==================

// ================== FieldUserDefinedNumberFormatModifier ==================
FieldUserDefinedNumberFormatModifier::FieldUserDefinedNumberFormatModifier(NumFmtCat nfc)
    : m_nfc(nfc)
{}

HRESULT FieldUserDefinedNumberFormatModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    binary_wo::VarObj extObj = pContext->GetExtObj();
    if (extObj.has("numberFormat"))
    {
        ks_bstr format(extObj.field_str("numberFormat"));
        IET_NumberFormatter *pFormatter = pContext->GetNumberFormatter();
        if (pFormatter == nullptr)
            return E_FAIL;

        return setInner(pContext, rg, format);
    }
    return E_INVALIDARG;
}
// ================== FieldUserDefinedNumberFormatModifier ==================

// ================== FieldXFModifier ==================
FieldXFModifier::FieldXFModifier()
{
    m_XF.reset();
}

HRESULT FieldXFModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    if (spRange == nullptr)
        return E_FAIL;
    ks_stdptr<IFormatHost> spHost = spRange;

    ForceIgnoreFilterHelper hlp(spRange);
    return spHost->SetXF(&m_XF.mask, &m_XF);
}

HRESULT FieldXFModifier::Clear(FieldContext *pContext, const RANGE &rg)
{
    ks_stdptr<IBook> spBook = pContext->GetBook();
    ks_stdptr<IBookOp> spBookOp;
    if (spBook)
        spBook->GetOperator(&spBookOp);
    if (spBookOp == nullptr)
        return E_FAIL;

    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    if (spRange == nullptr)
        return E_FAIL;
    ks_stdptr<IFormatHost> spHost = spRange;
    const XF *pXf = nullptr;
    spHost->GetNormalXF(&pXf);
    if (pXf == nullptr)
        return E_FAIL;

    ForceIgnoreFilterHelper hlp(spRange);
    return spHost->SetXF(&m_XF.mask, pXf);
}
// ================== FieldXFModifier ==================

// ================== FieldCentreAlignedXFModifier ==================
FieldCentreAlignedXFModifier::FieldCentreAlignedXFModifier()
    : FieldXFModifier()
{
    m_XF.alcH = haCenter; m_XF.mask.inc_alcH = 1;
    m_XF.alcV = vaCenter; m_XF.mask.inc_alcV = 1;
}
// ================== FieldCentreAlignedXFModifier ==================

// ================== FieldFontColourXFModifier ==================
FieldFontColourXFModifier::FieldFontColourXFModifier(DWORD argb)
    : FieldXFModifier()
{
    EtColor clr;
    clr.setARGB(argb);
    m_XF.font.clr = clr; m_XF.mask.inc_clr = 1;
}
// ================== FieldFontColourXFModifier ==================

// ================== FieldConditionFormatModifier ==================
HRESULT FieldConditionFormatModifier::Clear(FieldContext *pContext, const RANGE &rg)
{
    ks_stdptr<IBook> spBook = pContext->GetBook();
    ks_stdptr<IBookOp> spBookOp;
    if (spBook)
        spBook->GetOperator(&spBookOp);
    if (!spBookOp)
        return E_FAIL;

    return spBookOp->ClearRangeCF(rg);
}

HRESULT FieldConditionFormatModifier::CheckPermission(FieldContext *pContext, const RANGE &)
{
    return S_OK;
}
// ================== FieldConditionFormatModifier ==================

// ================== FieldDatabarConditionFormatModifier ==================
FieldDatabarConditionFormatModifier::FieldDatabarConditionFormatModifier(DWORD argb)
{
    m_clrFill.setARGB(argb);
}

HRESULT FieldDatabarConditionFormatModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    binary_wo::VarObj extObj = pContext->GetExtObj();
    if (extObj.has("color"))
    {
        DWORD argb = extObj.field_uint32("color");
        m_clrFill.setARGB(argb);
    }

    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    ks_stdptr<etoldapi::FormatConditions> spCondFmt;
    spRange->get_FormatConditions(&spCondFmt);
    if (spCondFmt == nullptr)
        return E_FAIL;

    ks_stdptr<IKCoreObject> spCoreObj;
    if (FAILED(spCondFmt->AddDatabar(&spCoreObj)))
        return E_FAIL;
    ks_castptr<etoldapi::IDatabar> spDatabar = spCoreObj;

    spDatabar->put_ShowValue(VARIANT_TRUE);
    ks_stdptr<etoldapi::ConditionValue> spMin;
    spDatabar->get_MinPoint(&spMin);
    spMin->Modify(xlConditionValueNumber, KComVariant(__X("0")));
    ks_stdptr<etoldapi::ConditionValue> spMax;
    spDatabar->get_MaxPoint(&spMax);
    spMax->Modify(xlConditionValueNumber, KComVariant(__X("1")));
    spDatabar->put_BarFillType(xlDataBarFillSolid);
    spDatabar->setFillColor(m_clrFill);
    ks_stdptr<etoldapi::DataBarBorder> spBorder;
    spDatabar->get_BarBorder(&spBorder);
    spBorder->put_Type(xlDataBarBorderNone);
    spDatabar->put_Direction(CF_DBDIR_Context);

    return S_OK;
}
// ================== FieldDatabarConditionFormatModifier ==================

// ================== FieldRatingConditionFormatModifier ==================
FieldRatingConditionFormatModifier::FieldRatingConditionFormatModifier()
{}

HRESULT FieldRatingConditionFormatModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    ks_stdptr<etoldapi::FormatConditions> spCondFmts;
    spRange->get_FormatConditions(&spCondFmts);
    if (spCondFmts == nullptr)
        return E_FAIL;

    int nRatings = 0;
    binary_wo::VarObj extObj = pContext->GetExtObj();
    if (extObj.has("option"))
        nRatings = extObj.field_int32("option");
    else if (extObj.has("maxRating"))
        nRatings = extObj.field_int32("maxRating");
    if (nRatings == 0)
        return E_INVALIDARG;

    for (int from = 0; from <= nRatings; from += 3)
    {
        // 一个条件格式可以设一个数字格式
        // 一个数字格式可以设三个条件
        int to = std::min(from + 2, nRatings);
        HRESULT hr = SetConditionFormatInner(spCondFmts, from, to, nRatings);
        if (FAILED(hr))
            return hr;
    }

    return S_OK;
}

QString FieldRatingConditionFormatModifier::ConstructRatingString(int nBlack, int nWhite)
{
    constexpr QChar blackStar(0x2605);
    constexpr QChar whiteStar(0x2606);
    return QString(nBlack, blackStar) + QString(nWhite, whiteStar);
}

HRESULT FieldRatingConditionFormatModifier::SetConditionFormatInner(etoldapi::FormatConditions *pCondFmts, int from, int to, int nTotal)
{
    ASSERT(to - from <= 2);

    ETFormatConditionType type = etCellValue;
    KComVariant varOperator;
    KComVariant varFormula1;
    KComVariant varFormula2;
    KComVariant varString;
    KComVariant varTextOperator;
    KComVariant varDateOperator;
    KComVariant varScopeType;

    varOperator.Assign(etBetween);
    ks_bstr bsformula1(krt::utf16(QString::number(from, 10)));
    varFormula1.AssignBSTR(bsformula1);
    ks_bstr bsformula2(krt::utf16(QString::number(to, 10)));
    varFormula2.AssignBSTR(bsformula2);

    QString format;
    int nBlack = to;
    switch (to - from)
    {
        case 2:
            format += QString("[=%1]%2;").arg(nBlack).arg(ConstructRatingString(nBlack, nTotal - nBlack));
            nBlack--;
            // fall through
        case 1:
            format += QString("[=%1]%2;").arg(nBlack).arg(ConstructRatingString(nBlack, nTotal - nBlack));
            nBlack--;
            // fall through
        case 0:
            format += QString("%1").arg(ConstructRatingString(nBlack, nTotal - nBlack));
            break;
        default:
            ASSERT(FALSE);
    }

    KXF kxf;
    kxf.mask.inc_pNumFmt = 1;
    xstrncpy_s(kxf.numfmt.fmt, krt::utf16(format), countof(kxf.numfmt.fmt));
    kxf.mask.inc_theme_name = 1;
    kxf.font.themeFontType = etThemeFontNone;
    kxf.mask.inc_bFamily = 1;
    xstrncpy_s(kxf.font.name, __X("ET-HEADERS-FONT"), countof(kxf.font.name));


    ks_stdptr<IKCoreObject> spCoreObj;
    return pCondFmts->Add(type, varOperator, varFormula1, varFormula2,
        varString, varTextOperator, varDateOperator, varScopeType, &kxf, &spCoreObj);
}
// ================== FieldRatingConditionFormatModifier ==================

// ================== FieldCellValuesModifier ==================
HRESULT FieldCellValuesModifier::GetClippedRANGE(FieldContext *pContext, RANGE &clipRg)
{
    ks_stdptr<IKWorksheets> spWorksheets = pContext->GetWorksheets();
    ks_stdptr<IKWorksheet> spWorksheet;
    IDX sheetIdx = clipRg.SheetFrom();
    if (spWorksheets)
        spWorksheet = spWorksheets->GetSheetItem(sheetIdx);
    if (spWorksheet == nullptr)
        return E_FAIL;

    bool bNeedClip = false;
    switch (clipRg.RangeType())
    {
        case rtSheets:
        case rtCols:
            bNeedClip = true;
            break;
        case rtRows:
        case rtCell:
        case rtCells:
            // 这些类型不处理
        default:
            break;
    }

    // 加上首行是整列的情况也进行裁剪
    if (clipRg.RowFrom() == 1 && clipRg.RowTo() == clipRg.GetBMP()->cntRows - 1)
        bNeedClip = true;

    if (bNeedClip)
    {
        ROW usedBottom = spWorksheet->GetSheet()->GetBottom();
        if (usedBottom >= 0)
            clipRg.SetRowTo(MAX(MIN(usedBottom, clipRg.RowTo()), clipRg.RowFrom()));
        else
            clipRg.SetRowTo(clipRg.RowFrom());
    }

    // 操作单元格太多就失败吧
    if (clipRg.CellCount() > MAX_ROWS_COUNT_XTND * 4)
        return E_FAIL;
    
    return S_OK;
}
// ================== FieldCellValuesModifier ==================

// ================== FieldCheckboxModifier ==================
HRESULT FieldCheckboxModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    RANGE clipRg(rg);
    if (FAILED(GetClippedRANGE(pContext, clipRg)))
        return E_FAIL;

    ks_stdptr<IBook> spBook = pContext->GetBook();
    ks_stdptr<IBookOp> spBookOp;
    if (spBook)
        spBook->GetOperator(&spBookOp);
    if (!spBookOp)
        return E_FAIL;

    return spBookOp->SetCellCheckbox(clipRg, FALSE);
}

HRESULT FieldCheckboxModifier::Clear(FieldContext *pContext, const RANGE &rg)
{
    RANGE clipRg(rg);
    if (FAILED(GetClippedRANGE(pContext, clipRg)))
        return E_FAIL;

    ks_stdptr<IBook> spBook = pContext->GetBook();
    ks_stdptr<IBookOp> spBookOp;
    if (spBook)
        spBook->GetOperator(&spBookOp);
    if (!spBookOp)
        return E_FAIL;

    return spBookOp->SetCellCheckbox(clipRg, TRUE);
}

HRESULT FieldCheckboxModifier::CheckPermission(FieldContext *pContext, const RANGE &rg)
{
    IEtRevisionContext *ctx = pContext->GetRevisionContext();
    if (ctx == nullptr)
        return E_FAIL;

    CHECK_PROTECTION_ALLOW_EDIT(rg, ctx);
    return S_OK;
}
// ================== FieldCheckboxModifier ==================

// ================== FieldFormulaModifier ==================
HRESULT FieldFormulaModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    binary_wo::VarObj extObj = pContext->GetExtObj();
    PCWSTR formula = nullptr;
    if (extObj.has("formula"))
        formula = extObj.field_str("formula");
    if (formula == nullptr)
        return E_INVALIDARG;

    ks_stdptr<IRangeInfo> host = pContext->CreateRangeObj(rg);
    RANGE ref(pContext->GetBook()->GetBMP());
    ref.SetCell(rg.SheetFrom(), rg.RowFrom(), rg.ColFrom());
    IEtRevisionContext *ctx = pContext->GetRevisionContext();
    if (ctx == nullptr)
        return E_FAIL;

    CHECK_PROTECTION_FORMULA(rg, formula, ctx);
    return host->SetFormula(formula, &ref);
}

HRESULT FieldFormulaModifier::Clear(FieldContext *pContext, const RANGE &rg)
{
    ks_stdptr<Range> spRange = pContext->CreateRangeObj(rg);
    return spRange->ClearContents();
}

HRESULT FieldFormulaModifier::CheckPermission(FieldContext *pContext, const RANGE &rg)
{
    IEtRevisionContext *ctx = pContext->GetRevisionContext();
    if (ctx == nullptr)
        return E_FAIL;

    CHECK_PROTECTION_ALLOW_EDIT(rg, ctx);
    return S_OK;
}
// ================== FieldFormulaModifier ==================

// ================== FieldHyperlinkModifier ==================
HRESULT FieldHyperlinkModifier::Set(FieldContext *pContext, const RANGE &rg)
{
    class CreateHyperlinkAcceptor : public ICellValueAcpt
    {
    public:
        CreateHyperlinkAcceptor(
                HRESULT &hr,
                FieldContext *pContext,
                IKHyperlinks *pHyperlinks,
                RANGE &rg,
                IETStringTools *pStrTools,
                IBookOp *pBookOp)
            : m_hr(hr)
            , m_pContext(pContext)
            , m_pHyperlinks(pHyperlinks)
            , m_rg(rg)
            , m_pStrTools(pStrTools)
            , m_pBookOp(pBookOp)
        {}

        STDPROC_(INT) Do(ROW r, COL c, const_token_ptr token) override
        {
            if (!token)
                return 0;
            m_rg.SetRowFromTo(r);
            m_rg.SetColFromTo(c);
            ks_stdptr<Range> spRange = m_pContext->CreateRangeObj(m_rg);
            
            ks_bstr text;
            m_pStrTools->GetCellText(token, NULL, &text);
            if (text.empty())
                return 0;

            ks_bstr bstrAddress;
            CheckAutoFixHypeLink(text, &bstrAddress);
            if (bstrAddress.empty())
                bstrAddress.assign(text);

            ks_stdptr<IKHyperlink> spHyperlink;
            m_hr = m_pHyperlinks->New(&spHyperlink, &m_rg);
            if (FAILED(m_hr))
                return 1;
            m_hr = spHyperlink->SetAddress(bstrAddress.c_str());
            if (FAILED(m_hr))
                return 1;

            // 超链接样式
            m_hr = m_pBookOp->SetCellStyle(m_rg, STR_STYLE_HYPERLINK);
            if (FAILED(m_hr))
                return 1;
            return 0;
        }
    private:
        HRESULT &m_hr;
        FieldContext *m_pContext;
        IKHyperlinks *m_pHyperlinks;
        RANGE &m_rg;
        IETStringTools *m_pStrTools;
        IBookOp *m_pBookOp;
    };

    ks_castptr<_Worksheet> spWorksheet = pContext->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    if (!spWorksheet)
        return E_FAIL;

    ISheet *pSheet = spWorksheet->GetSheet();
    if (!pSheet)
        return E_FAIL;
    ks_stdptr<IKHyperlinks> spHyperlinks;
    pSheet->GetExtDataItem(edSheetHyperlinks, (IUnknown**)&spHyperlinks);
    if (!spHyperlinks)
        return E_FAIL;

    ks_stdptr<IETStringTools> spStringTools;
    _etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&spStringTools);
    if (!spStringTools)
        return E_FAIL;
    spStringTools->SetEnv(pSheet);

    ISheetEnum *pSheetEnum;
    pSheet->CreateEnum(&pSheetEnum);

    HRESULT hr = S_OK;
    RANGE cellRg(rg);
    CreateHyperlinkAcceptor acpt(hr, pContext, spHyperlinks, cellRg, spStringTools, pContext->GetBook()->LeakOperator());
    pSheetEnum->EnumCellValue(rg, &acpt);
    pSheetEnum->Destroy();
    return hr;
}

HRESULT FieldHyperlinkModifier::Clear(FieldContext *pContext, const RANGE &rg)
{
    ks_stdptr<Range> spRange = pContext->CreateRangeObj(rg);

    ks_stdptr<Hyperlinks> spHyperlinks;
    spRange->get_Hyperlinks(&spHyperlinks);
    if (!spHyperlinks)
        return S_OK;
    
    return spHyperlinks->Delete();
}

HRESULT FieldHyperlinkModifier::CheckPermission(FieldContext *pContext, const RANGE &rg)
{
    if (!pContext->IsClear())
        return S_OK;

    IEtRevisionContext *ctx = pContext->GetRevisionContext();
    if (ctx == nullptr)
        return E_FAIL;
    CHECK_PROTECTION_ALLOW_EDIT(rg, ctx);

    ks_castptr<_Worksheet> spWorksheet = pContext->GetWorksheets()->GetSheetItem(rg.SheetFrom());
    ISheetProtection *pSheetProtection = spWorksheet->GetProtection();
    if (!pSheetProtection || !pSheetProtection->IsProtected() || pSheetProtection->IsMaster())
        return S_OK;

    SHEETPROTECTION sp;
    pSheetProtection->GetProperty(&sp);
    if (sp.bProtect && !sp.bInsertHyperlinks)
        return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;

    return S_OK;
}
// ================== FieldHyperlinkModifier ==================

} // Database
} // wo
