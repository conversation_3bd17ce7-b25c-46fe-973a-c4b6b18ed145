﻿#ifndef __WEBET_DATABASE_SHEET_DB_EXPORT_HELPER_H__
#define __WEBET_DATABASE_SHEET_DB_EXPORT_HELPER_H__

#include "appcore/et_appcore_dbsheet.h"

namespace wo
{

class KEtWorkbook;
class KEtRevisionContext;


struct AddWorkbookScope
{
public:
	AddWorkbookScope(Workbooks*, KEtWorkbook*, KEtRevisionContext&
		, UINT defaultSheetCount, PCWSTR font = nullptr, int fontSize = 0, BOOL bDbSt = FALSE);
	~AddWorkbookScope();
	_Workbook* GetWorkbook();
private:
	ks_stdptr<_Workbook> m_spNewWb;
	KEtWorkbook* m_pKEtWb;
	KEtRevisionContext& m_ctx;
	bool m_fromDb;
};

struct ExportOptimizer
{
	ExportOptimizer(IBookOp* pBookOp) : m_spBookOp(pBookOp)
	{
		if (m_spBookOp)
		{
			m_spBookOp->BeginBatchAutoFitDataChange();
			m_spBookOp->BeginBatchUpdate();
		}
	}
	~ExportOptimizer()
	{
		if (m_spBookOp)
		{
			m_spBookOp->EndBatchUpdate();
			m_spBookOp->EndBatchAutoFitDataChange();
		}
	}
private:
	ks_stdptr<IBookOp> m_spBookOp;
};

// dbt导出过程中涉及记录修改时会有导出状态命令判断，防止异常，导出得标记导出命令状态
struct ExportDbtScope
{
	explicit ExportDbtScope(IBook* pBook) : m_pBook(pBook)
	{
		IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->BeginExportDbt(m_pBook);
	}

	~ExportDbtScope()
	{
		IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->EndExportDbt(m_pBook);
	}
	IBook* m_pBook = nullptr;
};


class DbAttachmentDataExporter
{
public:
    DbAttachmentDataExporter(KEtWorkbook* pWorkbook);
    ~DbAttachmentDataExporter() = default;
    HRESULT ExtractDataToServer(UINT sheetId, EtDbId viewId, binary_wo::BinWriter& writer);
	HRESULT ExtractAllSheetDataToServer(KEtWorkbook* pWorkbook, binary_wo::BinWriter& writer);
	void SetIsFilterVideo(bool isFilterVideo);
	void SetUnsupportedAttachmentFileSuffixTypes(const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes);
private:
    void ExtractItemToServer(const_token_ptr pItem, UINT &itemIndex, binary_wo::BinWriter& writer);
    void ExtractFieldToServer(EtDbId fieldId, const IDBIds* pRecords, IDBSheetOp* pDbSheetOp, binary_wo::BinWriter& writer);
private:
    IBook* m_pBook;
	bool   m_isFilterVideo;
	std::set<ks_wstring> m_unsupportedAttachmentFileSuffixTypes{};
};

HRESULT ProcessCrossBookDbData(IKWorkbook*);
HRESULT RemoveNoPermissionDbSheetData(_Workbook* pWorkbook);
HRESULT RemoveNoExportSheet(_Workbook* pWorkbook, std::set<UINT>& sheetStIds);
void GetFallBackFields(ISheet* pSheet, const std::set<UINT>& sheetStIds,
    std::set<EtDbIdx>& fallBackLinkFldIds, std::set<EtDbIdx>& fallBackLookupFldIds);
HRESULT DoFallBackFields(ISheet* pSheet, const std::set<EtDbIdx>& fallBackLinkFldIds, 
	const std::set<EtDbIdx>& fallBackLookupFldIds);
HRESULT DoFallBackFieldsInner(IDbFieldsManager* pFieldsMgr, EtDbIdx fldId);
} // namespace wo

#endif // __WEBET_DATABASE_SHEET_DB_EXPORT_HELPER_H__
