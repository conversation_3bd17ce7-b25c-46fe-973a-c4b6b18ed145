﻿#include "etstdafx.h"
#include "file_merge_utils.h"
#include "workbook.h"
#include "kso/api/smartparam.h"

namespace filemerge
{

HRESULT TrasformSafeArray2Vector(VARIANT arr, std::vector<long>& parray)
{
	parray.clear();
	KSmartParam Param(arr);

	if(Param.IsErrorType())		//api未传columns值
		return S_FALSE;

	long numValue = Param.GetNumberValue(-1);
	if (!Param.IsArrayType() && numValue != -1)
	{
		parray.push_back(numValue);
		return S_OK;
	}

	if (!Param.IsArrayType())
		return E_INVALIDARG;

	SIZE32 LBound = 0;
	SIZE32 UBound = 0;
	SafeArrayGetLBound(V_ARRAY(&arr), 1, &LBound);
	SafeArrayGetUBound(V_ARRAY(&arr), 1, &UBound);
	UINT nDim = SafeArrayGetDim(V_ARRAY(&arr));
	ASSERT(nDim == 1);
	KComVariant VarElement;
	for (SIZE32 i = LBound; i <= UBound; ++i)
	{
		SafeArrayGetElement(V_ARRAY(&arr), &i, &VarElement);
		long lvalue = KSmartParam(VarElement).GetNumberValue(-1);
		if (lvalue == -1)
			return E_INVALIDARG;
		parray.push_back(lvalue);
	}
	return S_OK;
}

HRESULT RemoveDuplicates(wo::KEtWorkbook* wwb, _Worksheet* pSheet, bool isMarkSrcData)
{
	RECT rcData = { 0 };
	pSheet->GetSheet()->CalcUsedScale(&rcData);

	RANGE rgData(pSheet->GetSheet()->GetBMP());
	IDX iSheet = -1;
	pSheet->GetSheet()->GetIndex(&iSheet);
	rgData.SetSheetFromTo(iSheet);
	rgData.SetRowFromTo(rcData.top, rcData.bottom);
	rgData.SetColFromTo(rcData.left, isMarkSrcData ? rcData.right - 2 : rcData.right);
	if (!rgData.IsValid()) return E_FAIL;

	ks_stdptr<Range> ptrRange = wwb->CreateRangeObj(rgData);
	if (!ptrRange)
	{
		return E_FAIL;
	}

	/*统计被勾选的列数,除全选外*/
	QList<int> checkColunms;
	for (COL col = 1; col <= rgData.Width(); col++)
	{
		checkColunms.append(col);
	}

	//1 初始化多维数组;
	SAFEARRAYBOUND rgsabound[1];
	rgsabound[0].cElements = checkColunms.count(); //元素个数;
	rgsabound[0].lLbound = 0;//下标;
	SAFEARRAY* pSa = SafeArrayCreate(VT_VARIANT, 1, rgsabound);

	//2 放置元素;
	for (SIZE32 i = 0; i < checkColunms.count(); i++)
	{
		KComVariant val(checkColunms.at(i), VT_I4);
		SafeArrayPutElement(pSa, &i, &val);
	}

	//3 将数组封装成 VARIANT;
	KComVariant vtListArray;
	V_VT(&vtListArray) = VT_ARRAY | VT_VARIANT;
	V_ARRAY(&vtListArray) = pSa;

	if (isMarkSrcData)
	{
		RANGE rgSrcFields(pSheet->GetSheet()->GetBMP());
		rgSrcFields.SetSheetFromTo(iSheet);
		rgSrcFields.SetRowFromTo(rcData.top, rcData.bottom);
		rgSrcFields.SetColFromTo(rcData.right - 1, rcData.right);
		std::vector<long> srcFieldCols = { rcData.right - 1, rcData.right };
		std::vector<long> dataCols;
		ks_stdptr<IRemoveDuplicateItems> ptrUniqueTool;
		HRESULT hr = _appcore_CreateObject(CLSID_KRemoveDuplicateItems, IID_IRemoveDuplicateItems, (void**)&ptrUniqueTool);
		if (FAILED(hr) || !ptrUniqueTool)
			return E_INVALIDARG;
		ptrUniqueTool->Initialize(pSheet->GetSheet());

		range_helper::ranges uniqueRgs = range_helper::ranges::create_instance();
		range_helper::ranges repeatRgs = range_helper::ranges::create_instance();
		TrasformSafeArray2Vector(vtListArray, dataCols);
		if ((long)dataCols.size() > rgData.Width())
			return E_INVALIDARG;
		//此刻的数组只是标记选中range的第几列，需要转换成列号
		for (int i = 0; i < (int)dataCols.size(); i++)
		{
			COL col = rgData.ColFrom() + dataCols[i] - 1;
			if (col > rgData.ColTo() || col < rgData.ColFrom())				//参数不能越界
				return E_INVALIDARG;
			dataCols[i] = col;
		}
		ptrUniqueTool->GetDuplicateUniqueRanges(rgData, &dataCols[0], dataCols.size(), repeatRgs, uniqueRgs);

		KComVariant var;
		var.AssignDouble(etShiftUp);
		for (int idx = repeatRgs.size() - 1; idx >= 0; idx--)
		{
			RANGE tempRg = *repeatRgs.at(idx).second;
			tempRg.SetColFromTo(rcData.right - 1, rcData.right);
			ks_stdptr<Range> range = wwb->CreateRangeObj(tempRg);
			hr = range->Delete(var);
			if (FAILED(hr)) return hr;
		}
	}

	HRESULT hr = ptrRange->RemoveDuplicates(vtListArray, xlNo);
	return hr;
}
};