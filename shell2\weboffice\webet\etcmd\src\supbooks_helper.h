#ifndef __WO_SUPBOOKS_HELPER_H__
#define __WO_SUPBOOKS_HELPER_H__
#include "webbase/binvariant/binvarobj.h"

interface ISupEditLinks;
enum SUP_LINKS_INFO_STATUS;
namespace wo
{
class KEtWorkbook;
class KEtRevisionContext;

void getSupEditLinks(KEtWorkbook* wwb, ISupEditLinks** ppEditLinks);
void getSupbookStatus(ISupEditLinks* pEditLinks, BSTR bookFullName, KEtRevisionContext* pCtx, binary_wo::VarObj& status);
PCWSTR SLIS2Str(SUP_LINKS_INFO_STATUS slis);


}//namespace wo

#endif // __WO_SUPBOOKS_HELPER_H__