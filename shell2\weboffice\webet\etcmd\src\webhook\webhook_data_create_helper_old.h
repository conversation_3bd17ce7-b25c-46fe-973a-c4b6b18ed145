﻿#ifndef __ET_APPCORE_WEBHOOK_DATA_CREATE_HELPER_OLD_H__
#define __ET_APPCORE_WEBHOOK_DATA_CREATE_HELPER_OLD_H__

#include "webbase/binvariant/binwriter.h"
#include "webbase/binvariant/binvarobj.h"
#include "dbsheet/et_dbsheet_common_helper.h"

interface IBook;
interface IWebhookData;
interface IDbExtraFilter;

class WebhookDataCreateHelperOld
{
public:
    WebhookDataCreateHelperOld(IBook* pBook) : m_pBook(pBook), m_hookId(__X("")), m_pRes(nullptr) 
    {
        m_pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&m_spWebhookMgr);
    };
    HRESULT CreateWebhookData(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData** ppData, bool update = false, bool add = true);
    HRESULT CheckWebhookData(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData** ppData, bool update = false, bool add = true);
private:
    HRESULT writeHookResult(INT code, PCWSTR message);
    HRESULT createDbData(Et_DbSheet_WebhookType type, binary_wo::VarObj &hook, IWebhookData** ppData);
    HRESULT checkDbData(Et_DbSheet_WebhookType type, binary_wo::VarObj &hook, IWebhookData** ppData);
    HRESULT createEtData(Et_WebhookType type, binary_wo::VarObj &hook, IWebhookData** ppData, bool update = false, bool add = true);
    HRESULT checkEtData(Et_WebhookType type, binary_wo::VarObj &hook, IWebhookData** ppData, bool update = false, bool add = true);
    void setDbWebhookDataParam(Et_DbSheet_WebhookType type, binary_wo::VarObj &hook, IWebhookData* pData);
    void setEtWebhookDataParam(Et_WebhookType type, binary_wo::VarObj &hook, IWebhookData* pData);
    bool hasEtUpdateHookPermission(bool update, bool add);
    bool isBookHasHidden();
private:
    IBook* m_pBook;
    ks_wstring m_hookId;
    binary_wo::BinWriter* m_pRes;
    ks_stdptr<IWebhookManager> m_spWebhookMgr;
};

#endif