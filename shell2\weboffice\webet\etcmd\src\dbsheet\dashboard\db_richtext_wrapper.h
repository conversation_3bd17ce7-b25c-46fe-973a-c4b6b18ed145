﻿#ifndef __WEBET_DB_RICHTEXT_WRAPPER_H__
#define __WEBET_DB_RICHTEXT_WRAPPER_H__

#include "appcore/et_appcore_dbsheet.h"
#include "db_dashboard_module_wrapper.h"

namespace wo
{

class KDbRichTextWrapper : public KDbDashboardModuleWrapper
{
public:
    explicit KDbRichTextWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension);
    KDbRichTextWrapper(const KDbRichTextWrapper&) = delete;
    KDbRichTextWrapper& operator=(const KDbRichTextWrapper&) = delete;
    Status GetStatus() const override;
    HRESULT SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang, KEtRevisionContext* pCtx,
                             ISerialAcceptor* pAcpt) const override;
};

};
#endif // __WEBET_DB_RICHTEXT_WRAPPER_H__