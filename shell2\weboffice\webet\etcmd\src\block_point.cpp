﻿#include "etstdafx.h"
#include "kfc/tools/rrid_wrap.h"
#include "etcore/little_alg.h"
#include "block_point.h"
#include "wo/et_revision_context.h"
#include "wo/bw_helper.h"

namespace wo
{
BlockPoint::BlockPoint()
{
}

BlockPoint::BlockPoint(WebID obj, INT32 rr, INT32 cc)
{
	objSheet = obj, row = rr, col = cc;
}

BlockPoint::BlockPoint(const ::WoBlockPoint& bp): ::WoBlockPoint(bp)
{
}

void BlockPoint::ResetByCell(INT32 rr, INT32 cc)
{
	row = rr / BLK_ROWS_COUNT * BLK_ROWS_COUNT;
	col = cc / BLK_COLS_COUNT * BLK_COLS_COUNT;
}

RECT BlockPoint::GetRect() const
{
	return Rect_Create(row, col,
		row+BLK_ROWS_COUNT-1, col+BLK_COLS_COUNT-1);
}

void BlockPoint::Write(binary_wo::BinWriter& ww) const
{
	bw::Leave leave = bw::enterStruct(&ww, nullptr);
	ww.addInt32Field(row, "row");
	ww.addInt32Field(col, "col");
	ww.addAbsObjIDField(objSheet, "objSheet");
}

size_t BlockPoint::Hasher::operator()(const BlockPoint& x) const
{
	UINT32 hh = (x.objSheet >> 32), ll = x.objSheet;
	UINT32 arr[] = {hh, ll, x.col, x.row};
	return alg::HashUInt32Array(arr, _countof(arr));
}

bool BlockPoint::KeyEq::operator()(
	const BlockPoint& lhs, const BlockPoint& rhs) const
{
	return	lhs.objSheet == rhs.objSheet &&
			lhs.row == rhs.row &&
			lhs.col == rhs.col;
}

bool BlockPoint::LessOp::operator()(
	const BlockPoint& lhs, const BlockPoint& rhs) const
{
	if (lhs.objSheet == rhs.objSheet)
	{
		return lhs.row < rhs.row || lhs.row == rhs.row && lhs.col < rhs.col;
	}
	else
	{
		return lhs.objSheet < rhs.objSheet;
	}
}

BlockPointEnum::BlockPointEnum(WebID objSheet, const RECT& rc)
	: m_blockRect(Rect_Create(
		rc.top / BLK_ROWS_COUNT, rc.left / BLK_COLS_COUNT,
		rc.bottom / BLK_ROWS_COUNT, rc.right / BLK_COLS_COUNT))
	, m_rr(-1), m_cc(-1), m_objSheet(objSheet)
{
	m_rr = m_blockRect.top;
	m_cc = m_blockRect.left;
}

bool BlockPointEnum::IsValid() const
{
	return m_rr <= m_blockRect.bottom && m_cc <= m_blockRect.right;
}

BlockPoint BlockPointEnum::Current() const
{
	return BlockPoint(m_objSheet, m_rr*BLK_ROWS_COUNT, m_cc*BLK_COLS_COUNT);
}

void BlockPointEnum::Next()
{
	if (!IsValid()) return;

	++m_cc;
	if (m_cc > m_blockRect.right)
	{
		m_cc = m_blockRect.left;
		++m_rr;
	}
}

}
