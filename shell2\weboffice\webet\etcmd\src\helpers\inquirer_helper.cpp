#include "inquirer_helper.h"
#include "autofilter_helper.h"
#include "helpers/table_struct_rec_helper.h"
#include "helpers/copy_helper.h"
#include "autofilterparamhelper.h"
#include "kso/io/clipboard/ksomimetype.h"
#include "etcore/little_alg.h"
#include "et_task_detail_xf.h"

#include <iostream>
#include <fstream>
using namespace std;

namespace wo
{
constexpr int INQUIRER_RESULT_MAX_ROW = 1000;
constexpr int INQUIRER_RESULT_MAX_COL = 100;

InquirerHelper::InquirerHelper(wo::KEtWorkbook* pWb, ISheet* pSheet, int sheetIdx)
	: m_pWb(pWb)
	, m_pSheet(pSheet)
	, m_sheetIdx(sheetIdx)
{

}

//Todo:后续这里得改造成传一个RANGE支持用户手动重新框选表头
CollectInquirerInfoResult InquirerHelper::collectInquirerInfo(IDX tblHeadRowFrom, IDX tblHeadRowTo)
{
	CollectInquirerInfoResult res(CollectInquirerInfoStatus_Fail_Other_Reason, RANGE(m_pSheet->GetBMP()));


	//表头识别规则
	RECT rcUsed = Rect_CreateScaleNone();
	m_pSheet->GetUsedScale(&rcUsed);
	if(!Rect_IsValid(rcUsed))
	{
		//空表按照没有识别出表头来返回。
		res.m_status = CollectInquirerInfoStatus_Fail_Rec_TableHead;
		return res;
	}
	bool bUserSelectTblHead = (tblHeadRowFrom >=0 && tblHeadRowTo >= 0);
	if(!bUserSelectTblHead)
	{
		//由于useRange是会把仅含xf但不含内容的单元格也算进来，而我们表头识别的规则上下边界范围是要有内容的行。
		ROW firstHasContentRowIdx = m_pSheet->SeekFirstRowInColumns(rcUsed.left, rcUsed.right, TRUE);
		ROW LastHasContentRowIdx = m_pSheet->SeekLastRowInColumns(rcUsed.left, rcUsed.right, TRUE);
		rcUsed.top = firstHasContentRowIdx;
		rcUsed.bottom = LastHasContentRowIdx;
		if(!Rect_IsValid(rcUsed))
		{
			res.m_status = CollectInquirerInfoStatus_Fail_Rec_TableHead;
			return res;
		}
	}
	
	int titleCnt = 0;
	RANGE usedRg = Rect2Range(rcUsed, m_sheetIdx, m_pSheet->GetBMP());

	//检查当前创建查询器的用户 在区域内是否有禁止查看的数据
	if(!_checkUserHasHiddenAccess(usedRg))
	{
		res.m_status = CollectInquirerInfoStatus_Fail_Has_Hidden;
		return res;
	}

	int iRowFrom = usedRg.RowFrom();
	int iRowTo = usedRg.RowTo();
	COL iColFrom = usedRg.ColFrom();
	COL iColTo = usedRg.ColTo();

	if(!bUserSelectTblHead)
	{
		auto errCode = TableStructRecHelper::calcTitleCnt(m_pSheet, usedRg, true, iColFrom, iColTo, titleCnt);
		if (errCode == TableStructRecHelper::TableStructRecFailed)
			titleCnt = iRowFrom + 1;
		else if (errCode != TableStructRecHelper::TableStructRecSuccessed)
		{
			//说明表头识别失败了，创建失败
			res.m_status = CollectInquirerInfoStatus_Fail_Rec_TableHead;
			return res;
		}
		iRowTo = titleCnt - 1;
	}
	else
	{
		//根据用户重新校对选择的表头区域，作为最终的表头区域
		iRowFrom = tblHeadRowFrom;
		iRowTo = tblHeadRowTo;
		titleCnt = iRowTo + 1;
		//计算标题行区域的左右边界
		RANGE rg(m_pSheet->GetBMP());
		rg.SetSheetFromTo(m_sheetIdx);
		rg.SetRowFromTo(iRowFrom, iRowTo);
		rg.SetColFromTo(iColFrom, iColTo);
		TableStructRecHelper::getContentBorders(m_pSheet, rg, iColFrom, iColTo);
	}
	if(iRowFrom > iRowTo || iColFrom > iColTo)
	{
		WOLOG_ERROR << "[InquirerHelper::collectInquirerInfo] iRowFrom > iRowTo or iColFrom > iColTo error!";
		//说明表头识别失败了，创建失败
		res.m_status = CollectInquirerInfoStatus_Fail_Rec_TableHead;
		return res;
	}
	
	//构造表头区域
	RANGE tableHeadRg(m_pSheet->GetBMP());
	tableHeadRg.SetSheetFromTo(m_sheetIdx);
	tableHeadRg.SetRowFromTo(iRowFrom, iRowTo);
	tableHeadRg.SetColFromTo(iColFrom, iColTo);

	if(!tableHeadRg.IsValid())
	{
		//说明表头识别失败了，创建失败
		res.m_status = CollectInquirerInfoStatus_Fail_Rec_TableHead;
		return res;
	}

	if(tableHeadRg.ColTo() - tableHeadRg.ColFrom() + 1 > INQUIRER_RESULT_MAX_COL)
	{
		res.m_status = CollectInquirerInfoStatus_Fail_Tbl_Cols_Exceed_Threshold;
		return res;
	}

	//有哪些列识别出了手机号
	std::unordered_set<int> phoneNumColIdxs;
	HRESULT hr =  TableStructRecHelper::recPhoneNumCol(m_pSheet, tableHeadRg, phoneNumColIdxs);
	if(FAILED(hr) || phoneNumColIdxs.empty())
	{
		res.m_status = CollectInquirerInfoStatus_Fail_Rec_PhoneCols;
		return res;
	}
	
	wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext(); 
	if(!ctx)
	{
		return res;
	}
	
	//判断标题行是否有横向合并单元格
	for(int colIdx = iColFrom; colIdx <= iColTo; colIdx++)
	{
		//如果遇到了合并单元格，得找到合并单元格的左上角
		int row = tableHeadRg.RowTo();
		int col = colIdx;
		{
			BOOL bMerge = FALSE;
			m_pSheet->IsMerged(row, col, &bMerge);
			if(bMerge)
			{
				//合并单元格
				RANGE rgCell(m_pSheet->GetBMP());
				rgCell.SetCell(m_sheetIdx, row, col);

				ks_stdptr<IKRanges> ptrRgs;
				m_pSheet->FindEffectMergeCell(rgCell, FALSE, &ptrRgs);
				const RANGE* pMergeRg = NULL;
				ptrRgs->GetItem(0, NULL, &pMergeRg);
				ASSERT(pMergeRg);
				int rowFrom = pMergeRg->RowFrom();
				int rowTo = pMergeRg->RowTo();
				int colFrom = pMergeRg->ColFrom();
				int colTo = pMergeRg->ColTo();
				//检查标题行(表头的最后一行)是否有横向合并单元格,若有，则不允许
				if(colFrom != colTo)
				{
					res.m_status = CollectInquirerInfoStatus_Fail_TblTitle_HorMerge;
					return res;
				}

				row = rowFrom;
				col = colFrom;
			}
		}

		//获取标题行数据
		if(phoneNumColIdxs.count(colIdx) >= 1)
		{
			ks_bstr cellText;
			ctx->getStringTools()->GetCellText(m_pSheet, row, col, &cellText, nullptr, -1, nullptr);

			PhoneColsInfo info(colIdx, QString::fromUtf16(cellText));
			res.m_phoneColsInfo.push_back(info);
		}
	}
	
	res.m_status = CollectInquirerInfoStatus_Success;
	res.m_tableHeadRg = tableHeadRg;
	res.m_titleCnt = titleCnt;
	return res;
}

HRESULT InquirerHelper::exportInquirData(const ks_wstring& condStr, OUT QString& html)
{
	WOLOG_INFO << "[InquirerHelper::exportInquirData] exportInquirData() call ";
	ks_stdptr<IUnknown> spUnk;
	m_pSheet->GetExtDataItem(edSheetInquirer, &spUnk);
	ks_stdptr<IInquirer> spSheetInquirer = spUnk;
	if(!spSheetInquirer || !spSheetInquirer->GetEnable())
	{
		WOLOG_ERROR << "[InquirerHelper::exportInquirData] spSheetInquirer empty or is unEnable!";
		return E_FAIL;
	}	
	
	IDX iTblHeadRowFrom = -1, iTblHeadRowTo = -1, iTblHeadColFrom = -1,  iTblHeadColTo = -1;
	spSheetInquirer->GetTableHeadArea(iTblHeadRowFrom, iTblHeadRowTo, iTblHeadColFrom, iTblHeadColTo);
	if(iTblHeadRowFrom > iTblHeadRowTo || iTblHeadColFrom > iTblHeadColTo)
	{
		WOLOG_ERROR << "[InquirerHelper::exportInquirData] GetTableHeadArea result invalid!!!";
		return E_FAIL;
	}

	int colIdx = spSheetInquirer->GetColIdxPrimaryKey();

	RANGE tableHeadRg(m_pSheet->GetBMP());
	tableHeadRg.SetSheetFromTo(m_sheetIdx, m_sheetIdx);
	tableHeadRg.SetRowFromTo(iTblHeadRowFrom, iTblHeadRowTo);
	tableHeadRg.SetColFromTo(iTblHeadColFrom, iTblHeadColTo);
	if(!tableHeadRg.IsValid())
	{
		WOLOG_ERROR << "[InquirerHelper::exportInquirData] tableHeadRg invalid!!!";
		return E_FAIL;
	}
		
	return _exportInquirData(tableHeadRg, colIdx, condStr, html);
}

HRESULT InquirerHelper::_exportInquirData(const RANGE& tableHeadRg, int colIdx, const ks_wstring& condStr, OUT QString& html)
{
	WOLOG_INFO << "[InquirerHelper::_exportInquirData] _exportInquirData() end ";
	HRESULT hr = E_FAIL;
	if(!tableHeadRg.IsValid())
	{
		WOLOG_ERROR  << "[InquirerHelper::_exportInquirData] tableHeadRg invalid!!!";
		return E_FAIL;
	}
	if(condStr.empty())
	{
		WOLOG_ERROR  << "[InquirerHelper::_exportInquirData] dont allow filter cond empty!!!";
		return E_FAIL;
	}

	hr = _cancleRangeColHidden();
	if(FAILED(hr))
	{
		WOLOG_ERROR  << "[InquirerHelper::_exportInquirData] _cancleRangeColHidden() failed!!! ";
		return E_FAIL;
	}

	WOLOG_INFO << "[InquirerHelper::_exportInquirData] _clearFilter() call!!! ";
	hr = _clearFilter();
	if(FAILED(hr))
	{
		WOLOG_ERROR  << "[InquirerHelper::_exportInquirData] _clearFilter() failed!!! ";
		return E_FAIL;
	}

	_setStyle();

	WOLOG_INFO << "[InquirerHelper::_exportInquirData] _filterData() call!!! ";
	ks_stdptr<etoldapi::Range> dataAreaRgHost;
	hr = _filterData(tableHeadRg, colIdx, condStr, &dataAreaRgHost);
	if(FAILED(hr))
	{
		WOLOG_ERROR  << "[InquirerHelper::_exportInquirData] _filterData() failed!!! ";
		return E_FAIL;
	}

	WOLOG_INFO << "[InquirerHelper::_exportInquirData] _copyHtml() call!!! ";
	hr = _copyHtml(tableHeadRg, html);
	WOLOG_INFO << "[InquirerHelper::_exportInquirData] _exportInquirData() end ";
	return hr;
}

HRESULT InquirerHelper::_cancleRangeColHidden()
{
	RECT rcUsed = Rect_CreateScaleNone();
	m_pSheet->GetUsedScale(&rcUsed);
	if(!Rect_IsValid(rcUsed))
		return E_FAIL;
	RANGE usedRg = Rect2Range(rcUsed, m_sheetIdx, m_pSheet->GetBMP());
	if(!usedRg.IsValid())
		return E_FAIL;
	ks_stdptr<etoldapi::Range> host = m_pWb->CreateRangeObj(usedRg);
	if(!host)
		return E_FAIL;

	ks_stdptr<Range> spRowColRange;
	host->get_EntireColumn(&spRowColRange);
	if(!spRowColRange)
		return E_FAIL;
	
	KComVariant var;
	var.AssignBOOL(false);
	return spRowColRange->put_Hidden(var);
}

HRESULT InquirerHelper::_filterData(const RANGE& tableHeadRg, int colIdx, const ks_wstring& condStr, OUT Range** ppDataAreaRgHost)
{
	HRESULT hr = E_FAIL;
	wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext();  
	if(!ctx)
		return hr;
	IWoFilterContext *pFilterCtx = ctx->getFilterContext();
	PCWSTR filterID = pFilterCtx->GetID();

	RECT rcUsed = Rect_CreateScaleNone();
	m_pSheet->GetUsedScale(&rcUsed);
	if(!Rect_IsValid(rcUsed))
		return E_FAIL;
	RANGE usedRg = Rect2Range(rcUsed, m_sheetIdx, m_pSheet->GetBMP());

	//重新设置筛选范围(数据筛选)
	RANGE dataAreaRg(m_pSheet->GetBMP());
	int dataAreaRowFrom = tableHeadRg.RowTo();//筛选范围是从表头的最后一行开始!
	int dataAreaRowTo = usedRg.RowTo();
	int dataAreaColFrom = tableHeadRg.ColFrom();
	int dataAreaColTo = tableHeadRg.ColTo();

	dataAreaRg.SetSheetFromTo(m_sheetIdx);
	dataAreaRg.SetRowFromTo(dataAreaRowFrom, dataAreaRowTo);
	dataAreaRg.SetColFromTo(dataAreaColFrom, dataAreaColTo);
	if(!dataAreaRg.IsValid())
	{
		WOLOG_INFO << "[InquirerHelper::exportInquirData] dataAreaRg invalid!!!";
		return E_FAIL; 
	}
	ks_stdptr<etoldapi::Range> dataAreaRgHost = m_pWb->CreateRangeObj(dataAreaRg);
	if(!dataAreaRgHost)
	{
		WOLOG_INFO << "[InquirerHelper::exportInquirData] create dataAreaRgHost failed!!!";
		return E_FAIL; 
	}

	ks_stdptr<etoldapi::Range> spfilterRg;
	KComVariant varEmpty;
	hr = dataAreaRgHost->AutoFilter(filterID,  varEmpty, varEmpty, etAnd,
		varEmpty, varEmpty, &varEmpty, &spfilterRg);
	if(FAILED(hr) || !spfilterRg)
	{
		WOLOG_INFO << "[InquirerHelper::exportInquirData] reset AutoFilter failed!!!";
		return E_FAIL;
	}
	
	ks_stdptr<ICriteriaTextSet> spCriteriaTextSet;
	ks_stdptr<ICriteriaDateList> spCriteriaDateList;
	_appcore_CreateObject(CLSID_KCriteriaDateList, IID_ICriteriaDateList, (void**)&spCriteriaDateList);
	_appcore_CreateObject(CLSID_KCriteriaTextSet, IID_ICriteriaTextSet, (void**)&spCriteriaTextSet);
	spCriteriaTextSet->Add(condStr.c_str());

	KCriteriaParam1 param1;
	KCriteriaParam2 param2;
	param1.SetTextSet(spCriteriaTextSet);
	param2.SetDateList(spCriteriaDateList);

	ETFilterOperator fop = FOp_FilterValues;

	IKAutoFilter* pAutoFilter = AutoFilterHelper::GetUserFilter(m_pWb->GetCoreWorkbook(), filterID, usedRg);
	if(!pAutoFilter)
	{
		WOLOG_INFO << "[InquirerHelper::exportInquirData] pAutoFilter empty!!!";
		return E_FAIL;
	}
	RANGE filterRg(m_pSheet->GetBMP());
	pAutoFilter->GetFilterRange(&filterRg);
	if (!filterRg.IsValid() || colIdx < filterRg.ColFrom() || colIdx > filterRg.ColTo())
	{
		WOLOG_INFO << "[InquirerHelper::exportInquirData] filterRg not correct!!!";
		return E_FAIL;
	}
	int nField = colIdx - filterRg.ColFrom();
	if(nField < 0)
	{
		WOLOG_INFO << "[InquirerHelper::exportInquirData] nField unValid!!!";
		return E_FAIL;
	}

	ks_stdptr<IRangeInfo> spFilterRgInfo = spfilterRg;
	hr = spFilterRgInfo->AutoFilter(filterID, nField + 1, &param1, fop, &param2);

	if(FAILED(hr))
	{
		WOLOG_INFO << "[InquirerHelper::exportInquirData] doFilter failed,filter cond = " << condStr;
		return E_FAIL;
	}

	*ppDataAreaRgHost = dataAreaRgHost.detach();
	return S_OK;
}

HRESULT InquirerHelper::_clearFilter()
{
	RECT rcUsed = Rect_CreateScaleNone();
	m_pSheet->GetUsedScale(&rcUsed);
	if(!Rect_IsValid(rcUsed))
		return E_FAIL;
	RANGE usedRg = Rect2Range(rcUsed, m_sheetIdx, m_pSheet->GetBMP());

	wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext();
	if(!ctx)
	{
		WOLOG_ERROR << "[InquirerHelper::_clearFilter] ctxt empty error!";
		return E_FAIL;
	}
		
	IWoFilterContext *pFilterCtx = ctx->getFilterContext();
	PCWSTR filterID = pFilterCtx->GetID();
	
	IKAutoFilter* pAutoFilter = AutoFilterHelper::GetUserFilter(m_pWb->GetCoreWorkbook(), filterID, usedRg);
	if (pAutoFilter != NULL)
	{
		//如果原本有筛选 清除原本的筛选。
		ks_stdptr<etoldapi::Range> host = m_pWb->CreateRangeObj(usedRg);
		KComVariant varEmpty;
		ks_stdptr<etoldapi::Range> filterRg;
		HRESULT hr = host->AutoFilter(filterID,  varEmpty, varEmpty, etAnd,
			varEmpty, varEmpty, &varEmpty, &filterRg);
		if(FAILED(hr))
		{
			WOLOG_INFO << "[InquirerHelper::exportInquirData] _clearFilter failed!!!";
			return E_FAIL; 
		}
	}

	return S_OK;
}

HRESULT InquirerHelper::_copyHtml(const RANGE& tableHeadRg, QString &html)
{
	ks_stdptr<IETPersist> spPersist;
	spPersist = m_pWb->GetCoreApp()->GetAppPersist()->GetPersist();
	if(spPersist == NULL)
	{
		WOLOG_INFO << "[InquirerHelper::_copyHtml] GetPersist() failed!!!";
		return E_FAIL;
	}

	/////重新构造导出的数据范围
	RECT rcUsed = Rect_CreateScaleNone();
	m_pSheet->GetUsedScale(&rcUsed);
	if(!Rect_IsValid(rcUsed))
		return E_FAIL;
	
	RANGE dataAreaRg = Rect2Range(rcUsed, m_sheetIdx, m_pSheet->GetBMP());
	dataAreaRg.SetRowFrom(tableHeadRg.RowFrom());
	dataAreaRg.SetColFromTo(tableHeadRg.ColFrom(), tableHeadRg.ColTo());
	if(!dataAreaRg.IsValid())
		return E_FAIL;

	_clipCopyRgs(dataAreaRg, INQUIRER_RESULT_MAX_ROW + tableHeadRg.Height());

	ks_stdptr<etoldapi::Range> dataAreaRgHost = m_pWb->CreateRangeObj(dataAreaRg);
	if(!dataAreaRgHost)
		return E_FAIL;
	
	range_helper::ranges rgs;
	ks_stdptr<IRangeInfo> ptrPasteRangeInfo = dataAreaRgHost;
	ks_stdptr<IAppCoreRange> spPasteCoreRange;
	ptrPasteRangeInfo->GetAppCoreRange(&spPasteCoreRange);
	if(!spPasteCoreRange)
	{
		WOLOG_INFO << "[InquirerHelper::_copyHtml] GetAppCoreRange() failed!!!";
		return E_FAIL;
	}

	if (spPasteCoreRange->IsRangeInFilterMode())
		spPasteCoreRange->GetFilteredIRanges(FALSE, &rgs);
	else
	{
		//不可能走到这里的
		ASSERT(FALSE);
		return E_FAIL;
	}


	//粘贴答案
	COPYINFO info = {0};
	IDX sheetid = INVALIDIDX;
	info.grbits.fCopy = 1;
	info.grbits.fCache  = 1;
	info.grbits.fWithObjs = 1;
	info.grbits.fNoEmptyShape = 1;
	bool bHasBreak = false;

	html.clear();
	HRESULT html_hr = E_FAIL;

	IBook* pBook = m_pWb->GetCoreWorkbook()->GetBook();
	if(!pBook)
	{
		WOLOG_INFO << "[InquirerHelper::_copyHtml] GetBook() failed!!!";
		return E_FAIL;
	}

	//Todo:后续再对Copy返回的行列加以限制吧。
	html_hr = Copy_Helper::Copy(spPersist, m_sheetIdx, rgs, &info, kso_cb_html_format, html, bHasBreak, pBook);
	return html_hr;
}

void InquirerHelper::_clipCopyRgs(RANGE& rg, INT32 limitRowCnt)
{
	et_sdptr<ISheetEnum> spEnum;
	VS(m_pSheet->CreateEnum(&spEnum));
	
	wo::IEtRevisionContext* ctx = _etcore_GetEtRevisionContext();
	if(ctx)
		spEnum->SetFilterContext(ctx->getFilterContext());

	INT32 visibleCnt = 0;
	for (ROW rx = rg.RowFrom(); rx <= rg.RowTo();)
	{
		BOOL b = FALSE;
		INT32 cnt = spEnum->GetRowHidden(rx, &b);
		rx += cnt;

		if (b)
			continue;

		visibleCnt += cnt;
		if (visibleCnt >= limitRowCnt)
		{
			ROW rowTo = rx - (visibleCnt - limitRowCnt) - 1;
			if (rowTo < rg.RowTo())
				rg.SetRowTo(rowTo);
			return;
		}
	}
}

bool InquirerHelper::_checkUserHasHiddenAccess(RANGE& rg)
{   
	IKWorksheet* pWorksheet = m_pWb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(m_sheetIdx);
	ks_castptr<_Worksheet> pApiWs = pWorksheet;
	if (!pApiWs)
	{
		WOLOG_ERROR << "[InquirerHelper::_checkUserHasHiddenAccess] _Worksheet empty error!";
		return false;
	}

	ks_stdptr<ISheetProtection> pSheetProtection = pApiWs->GetProtection();;
	if (!pSheetProtection || !pSheetProtection->IsProtected() || pSheetProtection->IsMaster())
		return true;

	ProtectionAccessPerms perms = PTAAP_Edit;
	perms = pSheetProtection->GetUserAccessPerms(rg);
	if(perms != PTAAP_Invisible)
		return true;
	return false;
}

HRESULT InquirerHelper::_setStyle()
{
	EtColor borderColor;
	DWORD dColor = EtColor::MakeARGB(59, 59, 59);
	borderColor.setARGB(dColor);

	RECT rcUsed = Rect_CreateScaleNone();
	m_pSheet->GetUsedScale(&rcUsed);
	if(!Rect_IsValid(rcUsed))
		return E_FAIL;

	RANGE usedRg = Rect2Range(rcUsed, m_sheetIdx, m_pSheet->GetBMP());
	ks_stdptr<etoldapi::Range> spRange = m_pWb->CreateRangeObj(usedRg);
	if(!spRange)
		return E_FAIL;
	KCOMPTR(Borders) ptrBorders;
	spRange->get_Borders(&ptrBorders);
	if (ptrBorders == NULL) 
		return S_OK;

	for(int idx = etEdgeLeft; idx <= etInsideHorizontal; idx++)
	{
		KCOMPTR(Border) ptrEdgeBorder;
		ptrBorders->get_Item((ETBorderIndex)idx, &ptrEdgeBorder);
		if(!ptrEdgeBorder)
			continue;

		_SetBorderLineStyle(ptrEdgeBorder, etHairline);
		_SetBorderColor(ptrEdgeBorder, &borderColor);
	}

	return S_OK;
}

}// end namespace wo
