﻿#include "etstdafx.h"
#include "et_revision_context_impl.h"
#include "exp_autofilter_list_helper.h"
#include "framework/krt/krtstring.h"
#include "helpers/protection_helper.h"
#include "kbase/string/ksstring.h"
#include "kern/objbase.h"
#include "util.h"
#include "dbsheet/db_export_helper.h"
#include "workbook.h"
#include "helpers/varobject_helper.h"

ExportAfListHelper::ExportAfListHelper(const binary_wo::VarObj& param, const RANGE& fieldRg, IKWorksheet* pWs, _Workbook* pWb, wo::KEtRevisionContext* pCtx) :
    m_param(param),
    m_fieldRg(fieldRg),
    m_pWorksheet(pWs),
    m_pSrcWb(pWb),
	m_pDstWb(pWb),
    m_pCtx(pCtx)
{}

ExportAfListHelper::ExportAfListHelper(const binary_wo::VarObj& param, const RANGE& fieldRg, IKWorksheet* pWs, _Workbook* pSrcWb, _Workbook* pDstWb, wo::KEtRevisionContext* pCtx) :
    m_param(param),
    m_fieldRg(fieldRg),
    m_pWorksheet(pWs),
    m_pSrcWb(pSrcWb),
	m_pDstWb(pDstWb),
    m_pCtx(pCtx)
{}

static IKAutoFilter* GetCoreAutoFilter(IKWorksheet* pWSInfo, int nRow, int nCol)
{
	ISheet* pSheet = pWSInfo->GetSheet();
	ks_stdptr<ICoreListObjects> spLists;
	ks_stdptr<ICoreListObject> spList;
	pSheet->GetExtDataItem(edSheetListObjects, (IUnknown**)&spLists);
	// ListObj
	if (spLists && S_OK == spLists->FindItem(nRow, nCol, &spList))
		return spList->GetAutoFilters()->GetFilter(nullptr);

	// Worksheet
	return pWSInfo->GetCoreAutoFilter(nullptr);
}

HRESULT ExportAfListHelper::Export()
{
	IKWorksheets* pWorksheets = m_pSrcWb->GetWorksheets();
    if (!pWorksheets)
        return E_FAIL;
	if (m_fieldRg.SheetFrom() >= pWorksheets->GetSheetCount())
		return E_FAIL;
	IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(m_fieldRg.SheetFrom());
	if (!pWorksheet)
		return E_FAIL;
	ISheet* pSheet = pWorksheet->GetSheet();

    IKAutoFilter* pFilter = GetCoreAutoFilter(pWorksheet, m_fieldRg.RowFrom(), m_fieldRg.ColFrom());
	if (!pFilter)
		return E_FAIL;
	// 获取筛选区域，作权限判断
	RANGE filterRg(m_fieldRg.GetBMP());
	pFilter->GetFilterRange(&filterRg);
	m_fieldRg.SetRowTo(filterRg.RowTo());
	CHECK_PROTECTION_ALLOW_READ(m_fieldRg, m_pCtx);

    int nField = m_fieldRg.ColFrom() - filterRg.ColFrom();
	if (nField < 0)
		return E_FAIL;
	ks_stdptr<IAutoFilterValues> spFilterValues;
	ValuesNode* pRoot = nullptr;
	pFilter->GetFilterItems(nField, ETStringToolsOpt_None, TRUE, EOp_None, &spFilterValues, &pRoot);
	if (!pRoot || !spFilterValues)
		return E_FAIL;

	ks_wstring strFieldName;
	GetFilterFieldName(pSheet, strFieldName);

	IBook* pDstBook = m_pDstWb->GetBook();
    // disable autofit&calculate
	IBookOp* spBookOp;
	pDstBook->GetOperator(&spBookOp);
	struct ScopeHelper
	{
		IBookOp* m_bp;
		ScopeHelper(IBookOp* bp) : m_bp(bp) {
			m_bp->BeginBatchUpdate();
			m_bp->DisableAutoFit(TRUE);
		}
		~ScopeHelper() {
			m_bp->DisableAutoFit(FALSE);
			m_bp->EndBatchUpdate();
		}
	} sh(spBookOp);


    ISheet* pNewSheet = m_pWorksheet->GetSheet();
	IDX idxNew = INVALIDIDX;
	pNewSheet->GetIndex(&idxNew);
	SetNewSheetName(pNewSheet, pDstBook, strFieldName);
	RANGE newRg(pNewSheet->GetBMP());
	newRg.SetCell(idxNew, 0, 0);

	kfc::nf::NFHANDLE nf = spFilterValues->GetDateNFHandle();

	WebStr countName = m_param.field_str("countName");
	WebStr blankString = m_param.field_str("blankString");

    HRESULT hr = S_OK;
	ks_stdptr<IKAfValuesExporter> spAfValuesExporter;
	_applogic_CreateObject(CLSID_IKAfValuesExporter, IID_IKAfValuesExporter, (void**)&spAfValuesExporter);
    if (!spAfValuesExporter)
        return E_FAIL;
	spAfValuesExporter->Init(pDstBook, newRg, nf);
	spAfValuesExporter->SetExportParam(true);
	spAfValuesExporter->SetTitle(strFieldName, countName);
	spAfValuesExporter->SetBlankString(blankString);
	hr = spAfValuesExporter->ExportList(pRoot);

	bool bExportChart = m_param.field_bool("bExportChart");
	if (SUCCEEDED(hr) && bExportChart)
	{
		RANGE valuesRg(m_fieldRg.GetBMP());
		spAfValuesExporter->GetValuesRange(valuesRg);
		valuesRg.SetRowFrom(0);
		valuesRg.SetColFrom(0);
		valuesRg.SetRowTo(valuesRg.RowTo() - 1);

		ks_stdptr<etoldapi::Range> spRange = wo::util::CreateRangeObj(m_pDstWb, valuesRg);
		hr = ExportChart(spRange);
	}
	return hr;
}

HRESULT ExportAfListHelper::ExportResult()
{
	IKWorksheets* pWorksheets = m_pSrcWb->GetWorksheets();
    if (!pWorksheets)
        return E_FAIL;
	if (m_fieldRg.SheetFrom() >= pWorksheets->GetSheetCount())
		return E_FAIL;
	IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(m_fieldRg.SheetFrom());
	if (!pWorksheet)
		return E_FAIL;
    IKAutoFilter* pFilter = GetCoreAutoFilter(pWorksheet, m_fieldRg.RowFrom(), m_fieldRg.ColFrom());
	if (!pFilter)
		return E_FAIL;

	// 获取筛选区域，作权限判断
	RANGE filterRg(m_fieldRg.GetBMP());
	pFilter->GetFilterRange(&filterRg);
	m_fieldRg.SetRowTo(filterRg.RowTo());
	CHECK_PROTECTION_ALLOW_READ(m_fieldRg, m_pCtx);

    int nField = m_fieldRg.ColFrom() - filterRg.ColFrom();
	if (nField < 0)
		return E_FAIL;

	ks_stdptr<IKFilterExport> spExporter;
	VS(_applogic_CreateObject(CLSID_KFilterExport, IID_IKFilterExport, (void**)&spExporter));
    if (!spExporter)
        return E_FAIL;
	spExporter->Init(pWorksheet, m_pWorksheet, nField, pFilter);
	return spExporter->ExportFilterResult();
}


HRESULT ExportAfListHelper::ExportCategoryFilterResultToBook(wo::KEtRevisionContext& ctx, wo::KEtWorkbook* pEtWorkBook)
{
	IKWorksheets* pWorksheets = m_pSrcWb->GetWorksheets();
    if (!pWorksheets)
        return E_FAIL;
	if (m_fieldRg.SheetFrom() >= pWorksheets->GetSheetCount())
		return E_FAIL;
	IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(m_fieldRg.SheetFrom());
	if (!pWorksheet)
		return E_FAIL;
    IKAutoFilter* pFilter = GetCoreAutoFilter(pWorksheet, m_fieldRg.RowFrom(), m_fieldRg.ColFrom());
	if (!pFilter)
		return E_FAIL;

	HRESULT hr = S_OK;
	// 获取筛选区域，作权限判断
	RANGE filterRg(m_fieldRg.GetBMP());
	pFilter->GetFilterRange(&filterRg);
	m_fieldRg.SetRowTo(filterRg.RowTo());
	CHECK_PROTECTION_ALLOW_READ(m_fieldRg, m_pCtx);

    int nField = m_fieldRg.ColFrom() - filterRg.ColFrom();
	if (nField < 0)
		return E_FAIL;

	ks_stdptr<IKFilterExport> spExporter;
	VS(_applogic_CreateObject(CLSID_KFilterExport, IID_IKFilterExport, (void**)&spExporter));
    if (!spExporter)
        return E_FAIL;
	spExporter->Init(pWorksheet, m_pWorksheet, nField, pFilter);
    bool hasFilePath = wo::VarObjFieldValidation::expectString(m_param, "filePath");
    if (hasFilePath)
	{
		spExporter->SetExportedPath(m_param.field_str("filePath"));
	}
    else
    {
        ASSERT(hasFilePath);
        return E_FAIL;
    }
	QStringList listData;
	spExporter->PrepareCategoryFilterData(listData);
	int categoryCount = listData.size();
	if (0 == categoryCount)
		return E_FAIL;

    //服务端参数会把int改为float64,所以不能用expectIntegral
    bool hasLimit = m_param.has("categoryLimit");
    if (!hasLimit)
    {
        ASSERT(hasLimit);
        return E_FAIL;
    }
    wo::VarObjFieldValidation::expectIntegral(m_param, "sheetIdx");
    int nCategoryLimit = m_param.field_int32("categoryLimit");
    WOLOG_INFO << "[filter][ExportAfListHelper::ExportCategoryFilterResultToBook] nCategoryLimit:" << nCategoryLimit;
	//每个分类一个sheet
	for (int i = 0; i < categoryCount; ++i)
	{
        if (i >= nCategoryLimit) 
        {
            return S_OK;
        }
		if (i == 0)
		{
			hr = spExporter->ExportCategoryFilterResult(krt::utf16(listData[i]), true);
			if (FAILED(hr)) return hr;
		}
		else
		{
			ks_stdptr<Workbooks> spWorkbooks;
			pEtWorkBook->GetCoreApp()->get_Workbooks(&spWorkbooks);	
			wo::AddWorkbookScope newWbScope(spWorkbooks.get(), pEtWorkBook, ctx, 1);

			_Workbook* pWorkbook = newWbScope.GetWorkbook();
			if (!pWorkbook)
			{
				WO_LOG_X(pEtWorkBook->getLogger(), WO_LOG_ERROR, "Fail in AddWorkbookScope!");
				return E_FAIL;
			}

			ks_stdptr<etoldapi::Worksheets> spWorksheets;
			pWorkbook->get_Worksheets(&spWorksheets);

			ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorksheets->GetSheetItem(0);

			hr = spExporter->ChangeDstWorkSheet(spWorksheet);
			if (FAILED(hr)) return hr;
			hr = spExporter->ExportCategoryFilterResult(krt::utf16(listData[i]), true);
			if (FAILED(hr)) return hr;
		}	
	}
	return hr;
}

HRESULT ExportAfListHelper::ExportCategoryFilterResultToSheet()
{
	IKWorksheets* pWorksheets = m_pSrcWb->GetWorksheets();
    if (!pWorksheets)
        return E_FAIL;
	if (m_fieldRg.SheetFrom() >= pWorksheets->GetSheetCount())
		return E_FAIL;
	IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(m_fieldRg.SheetFrom());
	if (!pWorksheet)
		return E_FAIL;
    IKAutoFilter* pFilter = GetCoreAutoFilter(pWorksheet, m_fieldRg.RowFrom(), m_fieldRg.ColFrom());
	if (!pFilter)
		return E_FAIL;

	HRESULT hr = S_OK;
	// 获取筛选区域，作权限判断
	RANGE filterRg(m_fieldRg.GetBMP());
	pFilter->GetFilterRange(&filterRg);
	m_fieldRg.SetRowTo(filterRg.RowTo());
	CHECK_PROTECTION_ALLOW_READ(m_fieldRg, m_pCtx);

    int nField = m_fieldRg.ColFrom() - filterRg.ColFrom();
	if (nField < 0)
		return E_FAIL;

	ks_stdptr<IKFilterExport> spExporter;
	VS(_applogic_CreateObject(CLSID_KFilterExport, IID_IKFilterExport, (void**)&spExporter));
    if (!spExporter)
        return E_FAIL;
	spExporter->Init(pWorksheet, m_pWorksheet, nField, pFilter);
	
	QStringList listData;
	spExporter->PrepareCategoryFilterData(listData);
	int categoryCount = listData.size();
	if (0 == categoryCount)
		return S_FALSE;
	//每个分类一个sheet
	if (!m_pWorksheet)
		return E_FAIL;
	ks_stdptr<IKWorksheet> tmpWorkSheet =  m_pWorksheet;
	ISheet* pCurrentSheet = pWorksheet->GetSheet();
	if (!pCurrentSheet)
		return E_FAIL;
	IDX curSheetIdx = INVALIDIDX;
	pCurrentSheet->GetIndex(&curSheetIdx);
	if (curSheetIdx == INVALIDIDX)
		return E_FAIL;

	for (int i = 0; i < categoryCount; ++i)
	{
		WCHAR wszSheetName[MAX_SHEET_NAME_CCH + 1] = {};
		hr = pWorksheets->ValidateSheetName(stGrid, krt::utf16(listData[i]), wszSheetName, MAX_SHEET_NAME_CCH);
		if (FAILED(hr))
			return S_FALSE;
		tmpWorkSheet->GetSheet()->SetName(wszSheetName, xstrnlen_s(wszSheetName, MAX_SHEET_NAME_CCH + 1));
		spExporter->ExportCategoryFilterResult(krt::utf16(listData[i]), false);
		if (i < categoryCount - 1)
		{
			//新增sheet
			ks_stdptr<IKWorksheet> spWorksheet;
			hr = m_pSrcWb->NewWorksheet(curSheetIdx + 1, NULL, &spWorksheet);
			if (FAILED(hr)) return hr;
			hr = spExporter->ChangeDstWorkSheet(spWorksheet);
			tmpWorkSheet = spWorksheet;
			if (FAILED(hr)) return hr;
		}
	}
	
	return hr;
}

void ExportAfListHelper::GetFilterFieldName(ISheet* pSheet, ks_wstring& strFieldName)
{
	ks_bstr cellText;
	m_pCtx->getStringTools()->GetCellText(pSheet, m_fieldRg.RowFrom(), m_fieldRg.ColFrom(), &cellText, nullptr, -1, nullptr);
	if (!cellText.empty())
	{
		strFieldName = cellText.c_str();
		if (strFieldName.find(__X("=DISPIMG(\"")) == ks_wstring::npos)
			return;
	}
	WebStr colLabel = m_param.field_str("colLabel");
	QString label = QString::fromUtf16(colLabel);

	WCHAR arr[MAX_LOCALE_COL_A1REF_STR_LEN] = {0};
	ColumnIndex_Num2Str(m_fieldRg.ColFrom(), arr, countof(arr));
	label.append(krt::fromUtf16(arr));

	strFieldName = krt::utf16(label);
}

void ExportAfListHelper::SetNewSheetName(ISheet* pSheet, IBook* pBook, const ks_wstring& strFieldName)
{
	WebStr sheetName = m_param.field_str("sheetName");
	QString shtNameBase = krt::fromUtf16(__X("%1_%2")).arg(krt::fromUtf16(sheetName)).arg(krt::fromUtf16(strFieldName.c_str()));
	shtNameBase.remove(QRegExp(krt::fromUtf16(__X("(\r|\n|\r\n)"))));
	shtNameBase.truncate(MAX_SHEET_NAME_CCH);
	if (!alg::IsValidSheetName(krt::utf16(shtNameBase)))
		shtNameBase = krt::fromUtf16(sheetName);
	QString shtName = shtNameBase;
	for (int i = 0; i < 255; ++i)
	{
		IDX idxTmp = INVALIDIDX;
		pBook->GetSheetIdxByName(krt::utf16(shtName), &idxTmp);
		if (INVALIDIDX == idxTmp) break;

		QString postfix = krt::fromUtf16(__X("_%1")).arg(i + 1);
		shtName = shtNameBase.leftRef(MAX_SHEET_NAME_CCH - postfix.length()).toString() + postfix;
	}

	IDX idxNew = INVALIDIDX; VS(pSheet->GetIndex(&idxNew));
	const WCHAR* pShtName = NULL; 
	UINT uSheetNameLen = 0;
	pSheet->GetName(&pShtName, &uSheetNameLen);
	ks_wstring wsOldName(pShtName);
	if (SUCCEEDED(pSheet->SetName(krt::utf16(shtName), uSheetNameLen)))
	{
		et_appcore::KChangeTrackerProxy tracker(pBook);
		if (tracker.IsValid())
			tracker->TrackRenameSheet(idxNew, wsOldName.c_str(), krt::utf16(shtName));
	}
}

HRESULT ExportAfListHelper::ExportChart(etoldapi::Range* pRange)
{
	ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_pWorksheet;
	ks_stdptr<etoldapi::Shapes> spShapes;
	HRESULT hr = spWorksheet->get_Shapes(FALSE, &spShapes);
	if (!spShapes)
		return E_FAIL;

    binary_wo::VarObj rect = m_param.get("rect");
	int left = rect.field_int32("left");
	int top = rect.field_int32("top");
	int width = rect.field_int32("width");
	int height = rect.field_int32("height");
	int chartStyle = m_param.field_int32("chartStyle");
	int chartType = m_param.field_int32("chartType");

	left = wo::util::CORE_TO_API_UNIT_ET(left);
	top = wo::util::CORE_TO_API_UNIT_ET(top);
	if (width != -1 && height != -1)
	{
		width = wo::util::CORE_TO_API_UNIT_ET(width);
		height = wo::util::CORE_TO_API_UNIT_ET(height);
	} else {
		width = height = 0;
	}

	ks_stdptr<etoldapi::Shape> spShape;
	KComVariant varChartType(chartType);
	KComVariant varStyle(chartStyle);
	KComVariant varLeft(left), varTop(top), varWidth(width), varHeight(height);
	KComVariant varRange(pRange);
	const VARIANT VAR_EMPTY = { VT_EMPTY };
	hr = spShapes->AddChart2(varStyle, varChartType, varLeft, varTop,
		varWidth, varHeight, VAR_EMPTY, varRange, &spShape);
	if (FAILED(hr)) return hr;

	hr = spShape->Select(ksoTrue);
	return hr;
}