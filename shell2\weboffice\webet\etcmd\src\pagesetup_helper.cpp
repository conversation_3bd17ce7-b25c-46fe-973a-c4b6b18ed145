#include "etstdafx.h"
#include "pagesetup_helper.h"

#define MM2TWIP(x) ((double)((x) * 14400) / 254.0)

int _getSheetPageCount(IKWorksheet* pWs)
{
	IPageInfo* pPageInfo = pWs->GetPageInfo();
	if (!pPageInfo)
		return 0;

	int nPageCount = 0;
	int nAreaCount = 0;
	pPageInfo->GetPrintAreaCount(nAreaCount);

	ks_stdptr<IPrintArea> spPrintArea;
	for (int i = 0; i < nAreaCount; ++i)
	{
		spPrintArea.clear();
		pPageInfo->GetPrintAreaItem(i, &spPrintArea);
		if (spPrintArea)
		{
			int nPCount = 0;
			spPrintArea->GetPageCount(nPCount);
			nPageCount += nPCount;
		}
	}
	return nPageCount;
}

bool HasContentToPrint(IKWorkbook* pWb, IKWorksheet* pWs)
{
	if (pWs)
		return _getSheetPageCount(pWs) > 0;

	IKWorksheets* pWorksheets = pWb->GetWorksheets();
	int nCount = pWorksheets->GetSheetCount();
	for (int i = 0; i < nCount; ++i)
	{
		if (_getSheetPageCount(pWorksheets->GetSheetItem(i)) > 0)
			return true;
	}
	return false;
}

static void _markPaginateDirty(IKWorksheet* pWs)
{
	IKWorksheetView* pWsView = pWs->GetActiveWorksheetView();
	if (pWsView)
		pWsView->GetRenderPaginate()->Invalidate();
}

void MarkPaginateDirty(IKWorkbook* pWb, IKWorksheet* pWs)
{
	ASSERT(pWb || pWs);
	if (pWs)
		return _markPaginateDirty(pWs);

	IKWorksheets* pWorksheets = pWb->GetWorksheets();
	int nCount = pWorksheets->GetSheetCount();
	for (int i = 0; i < nCount; ++i)
		_markPaginateDirty(pWorksheets->GetSheetItem(i));
}

int GetPageCount(IKWorkbook* pWb, IKWorksheet* pWs)
{
	if (pWs)
		return _getSheetPageCount(pWs);

	IKWorksheets* pWorksheets = pWb->GetWorksheets();
	int nCount = pWorksheets->GetSheetCount();
	int nPages = 0;
	for (int i = 0; i < nCount; ++i)
		nPages += _getSheetPageCount(pWorksheets->GetSheetItem(i));

	return nPages;
}


void initPageSetup(PAGESETUP& pageSetup)
{
	ks_memset_s(&pageSetup, 0, sizeof(PAGESETUP));
	pageSetup.bCenterHorizontally = FALSE;
	pageSetup.bCenterVertically = FALSE;
	pageSetup.bDraft = FALSE;
	pageSetup.bBlackAndWhite = FALSE;
	pageSetup.bPrintGridlines = FALSE;
	pageSetup.bPrintHeadings = FALSE;
	pageSetup.bIsFit = FALSE;
	pageSetup.order = PGSTP_DownThenOver;
	pageSetup.orientation = PGSTP_Portrait;
	pageSetup.printComments = PGSTP_PrintNoComments;
	pageSetup.printErrors = PGSTP_PrintErrorsDisplayed;
	pageSetup.bNoPls = TRUE;
	pageSetup.paperSize = et_PaperA4;
	pageSetup.nFirstPageNumber = PGSTP_Automatic;
	pageSetup.nPrintQuality = 0;
	pageSetup.nPrintQualityVert = 0;
	pageSetup.nHeaderMargin = XLS_DEF_HEADER_MARGIN * TWIPS_PER_INCH;	//13 * TWIPS_PER_MM;
	pageSetup.nFooterMargin = XLS_DEF_FOOTER_MARGIN * TWIPS_PER_INCH;	//13 * TWIPS_PER_MM;
	pageSetup.nLeftMargin = XLS_DEF_LEFT_MARGIN * TWIPS_PER_INCH;	//19 * TWIPS_PER_MM;
	pageSetup.nRightMargin = XLS_DEF_RIGHT_MARGIN * TWIPS_PER_INCH;	//19 * TWIPS_PER_MM;
	pageSetup.nTopMargin = XLS_DEF_TOP_MARGIN * TWIPS_PER_INCH;	//25 * TWIPS_PER_MM;
	pageSetup.nBottomMargin = XLS_DEF_BOTTOM_MARGIN * TWIPS_PER_INCH;	//25 * TWIPS_PER_MM;
	pageSetup.nZoom = 100;
	pageSetup.nFitToPagesTall = 1;
	pageSetup.nFitToPagesWide = 1;
	pageSetup.nCopies = 1;
	pageSetup.bHFDiffOddEven = FALSE;
	pageSetup.bHFDiffFirst = FALSE;
	pageSetup.bHFScaleWithDoc = TRUE;
	pageSetup.bHFAlignMargins = TRUE;
}

enm_print_order DecodeOrder(int32 order)
{
	switch (order)
	{
	case 1:
		return PGSTP_DownThenOver;
	case 2:
		return PGSTP_OverThenDown;
	default:
		return PGSTP_DownThenOver;
	}
}

enm_page_orientation DecodeOrientation(int32 orientation)
{
	switch (orientation)
	{
	case 1:
		return PGSTP_Portrait;
	case 2:
		return PGSTP_Landscape;
	default:
		return PGSTP_Portrait;
	}
}

static const WCHAR* PaperSizeName[] = {
	__X("Letter"), __X("Tabloid"), __X("Legal"), __X("Statement"), __X("Executive"), 
	__X("A3"), __X("A4"), __X("A5"), __X("B4"), __X("B5")
};
 
static const ET_PaperSize PaperSize[] = {
	et_PaperLetter, et_PaperTabloid, et_PaperLegal, et_PaperStatement, et_PaperExecutive, 
	et_PaperA3, et_PaperA4, et_PaperA5, et_PaperB4, et_PaperB5
};
 
static ET_PaperSize DecodePaperSize(WebStr paperSize)
{
	for (int i = 0; i < _countof(PaperSizeName); i++)
	{
		if (xstrcmp(PaperSizeName[i], paperSize) == 0)
			return PaperSize[i];
	}
	return et_PaperA4;
}

inline bool CheckMarginError(double leftMargin, double pageWidth, double rightMargin)
{
	return (leftMargin < 0 || ((rightMargin + leftMargin) > pageWidth));
}

TmpPageSetupDataHelper::TmpPageSetupDataHelper(wo::KEtWorkbook* pWb)
	: m_pWorkbook(pWb)
{
}

TmpPageSetupDataHelper::~TmpPageSetupDataHelper()
{
	// 还原页面设置
	for (int i = 0, cnt = m_oldPageSetups.size(); i < cnt; ++i)
		m_oldPageSetups[i].first->SetPageSetup(&m_oldPageSetups[i].second);
}

void TmpPageSetupDataHelper::RevisePageSetup(ISheet* pSheet, const LPPAGESETUP pData)
{
	ks_stdptr<IPageSetupData> spPageSetupData;
	ks_stdptr<IUnknown> ptrUnk;
	pSheet->GetExtDataItem(edSheetPageSetup, &ptrUnk);
	ptrUnk->QueryInterface(IID_IPageSetupData, reinterpret_cast<void**>(&spPageSetupData));

	if (spPageSetupData)
	{
		LPPAGESETUP oldSetup = NULL;
		spPageSetupData->GetPageSetup(&oldSetup);
		m_oldPageSetups.push_back(std::make_pair(spPageSetupData, *oldSetup));
		PAGESETUP newSetup = *oldSetup;
		newSetup.bCenterHorizontally = pData->bCenterHorizontally;
		newSetup.bCenterVertically = pData->bCenterVertically;
		newSetup.bPrintGridlines = pData->bPrintGridlines;
		newSetup.bPrintHeadings = pData->bPrintHeadings;
		newSetup.order = pData->order;
		newSetup.orientation = pData->orientation;
		newSetup.nZoom = pData->nZoom;
		newSetup.nFitToPagesTall = pData->nFitToPagesTall;
		newSetup.nFitToPagesWide = pData->nFitToPagesWide;
		if (newSetup.nFitToPagesTall > 0 || newSetup.nFitToPagesWide > 0)
			newSetup.bIsFit = TRUE;

		newSetup.nHeaderMargin= pData->nHeaderMargin;
		newSetup.nFooterMargin = pData->nFooterMargin;
		newSetup.nLeftMargin = pData->nLeftMargin;
		newSetup.nRightMargin = pData->nRightMargin;
		newSetup.nTopMargin = pData->nTopMargin;
		newSetup.nBottomMargin = pData->nBottomMargin;
		newSetup.paperSize = pData->paperSize;
		newSetup.nFirstPageNumber = pData->nFirstPageNumber;

		newSetup.orientation == PGSTP_Portrait ? 
			newSetup.nPrintQualityVert = pData->nPrintQualityVert : newSetup.nPrintQuality = pData->nPrintQuality;

		spPageSetupData->SetPageSetup(&newSetup);

		qreal nWidth = 0, nHeight = 0;
		spPageSetupData->GetExportPaperSize(nWidth, nHeight);
		if (newSetup.orientation == PGSTP_Portrait)
		{
			spPageSetupData->SetExportPaperSize(nWidth, nHeight);

			if (CheckMarginError(newSetup.nHeaderMargin, nHeight, newSetup.nFooterMargin))
				newSetup.nHeaderMargin = XLS_DEF_HEADER_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nFooterMargin, nHeight, newSetup.nHeaderMargin))
				newSetup.nFooterMargin = XLS_DEF_FOOTER_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nLeftMargin, nWidth, newSetup.nRightMargin))
				newSetup.nLeftMargin = XLS_DEF_LEFT_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nRightMargin, nWidth, newSetup.nLeftMargin))
				newSetup.nRightMargin = XLS_DEF_RIGHT_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nTopMargin, nHeight, newSetup.nBottomMargin))
				newSetup.nTopMargin = XLS_DEF_TOP_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nBottomMargin, nHeight, newSetup.nTopMargin))
				newSetup.nBottomMargin = XLS_DEF_BOTTOM_MARGIN * TWIPS_PER_INCH;
		}
		else
		{
			spPageSetupData->SetExportPaperSize(nHeight, nWidth);

			if (CheckMarginError(newSetup.nHeaderMargin, nWidth, newSetup.nFooterMargin))
				newSetup.nHeaderMargin = XLS_DEF_HEADER_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nFooterMargin, nWidth, newSetup.nHeaderMargin))
				newSetup.nFooterMargin = XLS_DEF_FOOTER_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nLeftMargin, nHeight, newSetup.nRightMargin))
				newSetup.nLeftMargin = XLS_DEF_LEFT_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nRightMargin, nHeight, newSetup.nLeftMargin))
				newSetup.nRightMargin = XLS_DEF_RIGHT_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nTopMargin, nWidth, newSetup.nBottomMargin))
				newSetup.nTopMargin = XLS_DEF_TOP_MARGIN * TWIPS_PER_INCH;

			if (CheckMarginError(newSetup.nBottomMargin, nWidth, newSetup.nTopMargin))
				newSetup.nBottomMargin = XLS_DEF_BOTTOM_MARGIN * TWIPS_PER_INCH;
		}
		spPageSetupData->SetPageSetup(&newSetup);
	}
}

void TmpPageSetupDataHelper::GetPageSetupData(binary_wo::VarObj var, PAGESETUP& pageSetup)
{
	initPageSetup(pageSetup);
	const int MAX_PAGETOFIT = 32767;

	if (var.has("centerHorizontally"))
		pageSetup.bCenterHorizontally = var.field_bool("centerHorizontally");

	if (var.has("centerVertically"))
		pageSetup.bCenterVertically = var.field_bool("centerVertically");

	if (var.has("printGridlines"))
		pageSetup.bPrintGridlines = var.field_bool("printGridlines");

	if (var.has("printHeadings"))
		pageSetup.bPrintHeadings = var.field_bool("printHeadings");

	if (var.has("order"))
		pageSetup.order = DecodeOrder(var.field_int32("order"));

	if (var.has("orientation"))
		pageSetup.orientation = DecodeOrientation(var.field_int32("orientation"));

	if (var.has("fitToHeight"))
	{
		pageSetup.nFitToPagesTall = var.field_int32("fitToHeight");
		if (pageSetup.nFitToPagesTall < 0 || pageSetup.nFitToPagesTall > MAX_PAGETOFIT)
			pageSetup.nFitToPagesTall = 1;
	}
	if (var.has("fitToWidth"))
	{
		pageSetup.nFitToPagesWide = var.field_int32("fitToWidth");
		if (pageSetup.nFitToPagesWide < 0 || pageSetup.nFitToPagesWide > MAX_PAGETOFIT)
			pageSetup.nFitToPagesWide = 1;
	}
	if (var.has("zoom"))
	{
		pageSetup.nZoom = var.field_int32("zoom");
		if (pageSetup.nZoom < 10 || pageSetup.nZoom > 400)
			pageSetup.nZoom = 100;
	}

	if (var.has("headerMargin"))
		pageSetup.nHeaderMargin = MM2TWIP(var.field_double("headerMargin"));

	if (var.has("footerMargin"))
		pageSetup.nFooterMargin = MM2TWIP(var.field_double("footerMargin"));

	if (var.has("leftMargin"))
		pageSetup.nLeftMargin = MM2TWIP(var.field_double("leftMargin"));

	if (var.has("rightMargin"))
		pageSetup.nRightMargin = MM2TWIP(var.field_double("rightMargin"));

	if (var.has("topMargin"))
		pageSetup.nTopMargin = MM2TWIP(var.field_double("topMargin"));

	if (var.has("bottomMargin"))
		pageSetup.nBottomMargin = MM2TWIP(var.field_double("bottomMargin"));

	if (var.has("paperSize"))
		pageSetup.paperSize = DecodePaperSize(var.field_str("paperSize"));

	if (var.has("firstPageNumber"))
		pageSetup.nFirstPageNumber = var.field_int32("firstPageNumber");

	if (var.has("printQuality"))
		pageSetup.nPrintQuality = var.field_int32("printQuality");

	if (var.has("printQualityVert"))
		pageSetup.nPrintQualityVert = var.field_int32("printQualityVert");
}

WebInt TmpPageSetupDataHelper::RevisePageSetups(const binary_wo::VarObj& cmdParam, IKWorksheet* pWs, 
											std::function<bool (ISheet*)> const &skipOrNot)
{
	ks_stdptr<IKWorksheet> spWs = pWs;
	const bool bPageSetup = cmdParam.has("pageSetup") ? cmdParam.field_bool("pageSetup") : false;
	if (bPageSetup)
	{
		WOLOG_INFO << "[ExportAs] " << "has PageSetup";
		if (!HasContentToPrint(m_pWorkbook->GetCoreWorkbook(), spWs))
		{
			WEBLOG_ERROR("Nothing to print");
			return WO_NOTHING_TO_PRINT;
		}
			
		PAGESETUP setup;
		GetPageSetupData(cmdParam, setup);

		if (spWs)
		{
			RevisePageSetup(spWs->GetSheet(), &setup);
			app_helper::KViewBatchUpdate _Update(ks_castptr<_Worksheet>(spWs), ERM_PageSetupChanged);
		}
		else
		{
			IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
			int cnt = pWorksheets->GetSheetCount();
			for (int i = 0; i < cnt; ++i)
			{
				ISheet* pCurSheet = pWorksheets->GetSheetItem(i)->GetSheet();
				if (skipOrNot(pCurSheet))
					continue;
				RevisePageSetup(pCurSheet, &setup);
			}
			app_helper::KViewBatchUpdate _Update(m_pWorkbook->GetCoreWorkbook(), ERM_PageSetupChanged);
		}

		MarkPaginateDirty(m_pWorkbook->GetCoreWorkbook(), spWs);
	}

	return WO_OK;
}
