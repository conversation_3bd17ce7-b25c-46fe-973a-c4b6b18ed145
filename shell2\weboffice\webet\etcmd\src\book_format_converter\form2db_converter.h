﻿#ifndef __WEBET_FORM2DB_CONVERTER_H__
#define __WEBET_FORM2DB_CONVERTER_H__

#include "webbase/binvariant/binvarobj.h"
#include "appcore/et_appcore_dbsheet.h"
#include "etcmd/src/et_revision_context_impl.h"

namespace wo
{
	class JsonParser
	{
	public:
		HRESULT ParseJsonData(const binary_wo::VarObj& jsonData);
	protected:
		virtual HRESULT ParseSheetData(const binary_wo::VarObj& sheetData) = 0;
		virtual HRESULT ParseRecordData(const binary_wo::VarObj& recordData) = 0;
	};

	enum Form2DbSheetType
	{
		Form2DbSheetType_Invalid = 0,
		Form2DbSheetType_MainSheet,	// 主表， 每个表单数据都有一张主表，主表commitid唯一
		Form2DbSheetType_SubSheet,	// 子表， 每个表单数据可能有一张或多张子表，子表commitid是关联主表的一条记录的commitid
	};

	struct Form2DbData
	{
		struct FormRecord
		{
			PCWSTR commitTime = nullptr;
			PCWSTR commitId = nullptr;
			binary_wo::VarObj recordValue;
		};

		// 表单自增表格，会出现多条记录commitId都相同的情况
		typedef std::multimap<ks_wstring, FormRecord> FormData;  // <commitid, record>
		FormData formData;
		EtDbId commitIdField = INV_EtDbId;
		Form2DbSheetType sheetType = Form2DbSheetType_Invalid;
	};

	class Form2DbJsonParser : public JsonParser
	{
	public:
		Form2DbJsonParser(Form2DbSheetType st);
		const Form2DbData& GetValue() const;
	protected:
		HRESULT ParseSheetData(const binary_wo::VarObj& sheetData) override;
		HRESULT ParseRecordData(const binary_wo::VarObj& recordData) override;

	private:
		Form2DbData m_value;
	};

	class DbSheetValueSerialiser;
	class KEtWorkbook;
	class Form2DbCrossSheetFieldUpdater;
	class Form2DbSyncUpdater
	{
	public:
		Form2DbSyncUpdater(KEtWorkbook *wwb, ISheet* pSheet, Form2DbCrossSheetFieldUpdater* pCrossSheetFieldUpdater, DbSheetValueSerialiser* pDbSerialiser, const Form2DbData& data);
		HRESULT UpdateSyncSheet();
	private:
		HRESULT UpdateMainSyncSheet();
		HRESULT UpdateSubSyncSheet();
	private:
		const Form2DbData& m_value;
		DbSheetValueSerialiser* m_pDbSerialiser;
		ISheet* m_pSheet;
		ks_stdptr<IDBSheetOp> m_spDbSheetOp;
		KEtWorkbook *m_wwb;
		Form2DbCrossSheetFieldUpdater* m_pCrossSheetFieldUpdater;
	};

	class Form2DbCrossSheetFieldUpdater
	{
	public:
		Form2DbCrossSheetFieldUpdater(IBook* pBook);
		HRESULT UpdateCrossSheetFieldValue();
		HRESULT AddLinkFieldValue(UINT sheetId, EtDbId recId, ks_wstring commitId, EtDbId fldId, ks_wstring linkCommitId);
		HRESULT UpdateSheetCommitIdRecIdMap(UINT sheetId, std::unordered_map<ks_wstring, EtDbId, WSTR_HASH>& commitIdRecIdMap);
	private:
		HRESULT UpdateCrossSheetLinkFieldValue();
		HRESULT UpdateCrossSheetLinkFieldValueImp(IDBSheetOp* pDBSheetOp, EtDbId recId, EtDbId fldId, ks_wstring linkCommitId);
	private:
		std::unordered_map<UINT, std::unordered_map<EtDbId, std::unordered_map<EtDbId, ks_wstring>>> m_linkFieldValuesMap;
		std::unordered_map<UINT, std::unordered_map<ks_wstring, EtDbId, WSTR_HASH>> m_commitIdRecIdMap;
		IBook* m_pBook;
	};
}
#endif
