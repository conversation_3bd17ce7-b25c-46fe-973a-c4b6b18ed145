﻿#ifndef __WEBET_DB_CHART_WRAPPER_H__
#define __WEBET_DB_CHART_WRAPPER_H__
#include "appcore/et_appcore_dbsheet.h"
#include "db_dashboard_module_wrapper.h"

namespace wo
{

class KDbChartWrapper : public KDbDashboardModuleWrapper
{
public:
    KDbChartWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension, IDBChartStatisticModule* pStatisticModule);
    KDbChartWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension, IDbWorksheetChart* pWorksheetChart);
public:
    bool SerializeBaseInfo(ISerialAcceptor* pAcpt) override;
    void SerializeConfig(ISerialAcceptor* pAcpt);
    void SerializeConfigChartInfo(ISerialAcceptor* pAcpt);
    void SerializeConfigXAxis(ISerialAcceptor* pAcpt);
    void SerializeConfigYAxis(ISerialAcceptor* pAcpt);
    void SerializeConfigLegend(ISerialAcceptor* pAcpt);
    void SerializeConfigAllLabel(ISerialAcceptor* pAcpt);
    void SerializeConfigValueFormat(ISerialAcceptor* pAcpt);
    Status GetStatus() const override;
    HRESULT SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang, KEtRevisionContext* pCtx,
                             ISerialAcceptor* pAcpt) const override;
    HRESULT SetStyle(ET_DBSheet_ChartElementType elementType, PCWSTR identifier, PCWSTR style);
    HRESULT RemoveStyle(ET_DBSheet_ChartElementType elementType, PCWSTR identifier);
    HRESULT SetSortType(ET_DBSheet_ChartSortType sortType);
    HRESULT SetCustomCategoryOrder(const binary_wo::VarObj& customOrder);
    HRESULT SetCustomSeriesOrder(const binary_wo::VarObj& customOrder);
    std::vector<PCWSTR> GetCustomOrderVector(const binary_wo::VarObj& customOrder);
    HRESULT SetSeriesSortType(ET_DBSheet_ChartSortType SeriesSortType);
    HRESULT SetSortAscending(bool ascending);
    HRESULT GetGroupRecords(const binary_wo::VarObj& categoryGroupKey, const binary_wo::VarObj& seriesGroupKey, IDBIds** ppRecords);
    HRESULT GetVisibleRecords(IDBIds** ppRecords);
    UINT GetDataSourceSheetId();
    HRESULT GetFunnelInfo(UINT& count, PCWSTR& typeStr);
    void SerializeSettings(ISerialAcceptor* pAcpt) override;
private:
    ET_DBSheet_ChartType GetChartType() const;
    PCWSTR GetDataSourceTypeStr();
    bool IsWorksheetChart() const;
    HRESULT HasHiddenContent(IKDataRange* pDataRange, KEtRevisionContext* pCtx) const;
    IKDataRange* GetFirstDataRange() const;
    IDbChartStyleManager* GetStyleManager();
    IDashboardChart* GetChart();
    Status GetCountDownStatus() const;
private:
    ks_stdptr<IDBChartStatisticModule> m_spStatisticModule;
    ks_stdptr<IDbWorksheetChart> m_spWorksheetChart;
};

};
#endif // __WEBET_DB_CHART_WRAPPER_H__