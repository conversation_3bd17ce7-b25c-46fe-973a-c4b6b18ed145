﻿#include "etstdafx.h"
#include "revision_ext.h"
#include "wo/et_revision_context.h"
#include "wo/core_stake.h"
#include "webbase/context_init_helper.h"

namespace wo
{
KEtVersion::KEtVersion(UINT32 userInnerID, KwTask* task, IKWorkbook* wb, KwVersion::BanUndoType banUndo)
	: KwVers<PERSON>(userInnerID, task, banUndo)
	, m_bks(wb->GetBook()->GetWoStake())
	, m_undoTag(nullptr)
	, m_hasRecalculate(false)
{
}

const UNDO_TAG* KEtVersion::getUndoTag()
{
	return m_undoTag;
}

void KEtVersion::onConfim(WebInt, WebInt, Transact*)
{
	m_undoTag = m_bks->getLastUndoTag();
}

void KEtVersion::setCustomVerInfo(IUnknown* pCustomVerInfo)
{
	if (pCustomVerInfo)
	{
		KEtCustomVerInfo* pInfo = static_cast<KEtCustomVerInfo*>(pCustomVerInfo);
		m_hasRecalculate = pInfo->getHasRecalculate();
	}
}

// KEtVersionManager
//---------------------------------------------------------------------------------
KEtVersionManager::KEtVersionManager(RepertoryProxy* rep, IKWorkbook* wb, WebLogFunc logFunc)
	: KwVersionManager(rep, logFunc), m_wb(wb)
{
	m_unusedVersionIndex = 0;
	m_needClearVersions = false;
	m_needClearCurVersionCmd = false;
	ks_stdptr<IWorkspace> spWorkspace;
	wb->GetBook()->GetWorkspace(&spWorkspace);
	m_maxUndoStep = spWorkspace->GetTransCapacity();
}

KwVersion* KEtVersionManager::createVersion(
	UINT32 userInnerID, KwTask* task, KwVersion::BanUndoType banUndo)
{
	return new KEtVersion(userInnerID, task, m_wb, banUndo);
}

void KEtVersionManager::clearUnusedVersionData()
{
	if (m_needClearCurVersionCmd)
	{
		KwVersion* pLastDataVersion = nullptr;
		if (m_dataVersions.size() > 0)
			pLastDataVersion = m_dataVersions[m_dataVersions.size() - 1];
		if (pLastDataVersion)
		{
			KwTask* pTask = pLastDataVersion->getTask();
			if (pTask)
			{
				pTask->clearCommand();
			}
		}
		m_needClearCurVersionCmd = false;
	}
	//清除事务后，清除所有版本数据,无法区分刚打开文件初始化状态和版本数据重置的状态query会有问题，暂时屏蔽，后续处理
	//清除所有版本数据时机:在不再使用task对象之后，返回版本序列化数据（或重新初始化）之前（清除事务后版本序列化会失败，执行重新初始化版本数据）
	// if (m_needClearVersions)
	// {
	// 	auto it = m_taskVersions.begin();
	// 	for (; it != m_taskVersions.end(); it++) {
	// 		delete (*it);
	// 	}
	// 	m_taskVersions.clear();
	// 	m_dataVersions.clear();
	// 	m_verCloud2Task.clear();
	// 	m_dataCurrentIndex = InitVersion;
	// 	m_taskCurrentIndex = InitVersion;
	// 	m_unusedVersionIndex = 0;
	// 	m_needClearVersions = false;

	// 	WOLOG_INFO << "[clearAllVersionData] after clearAllUndoRedoSteps " ;
	// }
	// else
	{
		//超过最大撤销步数后清除不会访问到的版本数据
		clearAfterMaxUndoSteps();
	}
}

void KEtVersionManager::markClearVersionData(bool clear)
{
   m_needClearVersions  = clear;
}

void KEtVersionManager::markClearCurVersionCmdData(bool clear)
{
   m_needClearCurVersionCmd  = clear;
}

void  KEtVersionManager::clearAfterMaxUndoSteps()
{
	//释放不再使用到的KwVersion；
	while (m_dataVersions.size()  > m_maxUndoStep + m_unusedVersionIndex +1)
	{
		KwVersion* unusedVersion = m_dataVersions[m_unusedVersionIndex];
		ASSERT(unusedVersion != nullptr);
		WebInt taskIndex = unusedVersion->getTaskIndex();
		m_dataVersions[m_unusedVersionIndex] = nullptr;
		delete  unusedVersion;
		WOLOG_INFO << "[delete KEtVersion] dataIndex:" << m_unusedVersionIndex << " taskIndex:" << taskIndex;
		std::for_each(m_taskVersions.begin(),m_taskVersions.end(),[unusedVersion, taskIndex](KwVersion* & v){
			if(unusedVersion == v)
			{
				v = nullptr;
			}	
			if(v) 
			{
				auto undoTask = v->getUndoTask();
				if( undoTask.first && undoTask.second == taskIndex)
				{
					WOLOG_INFO << "[delete KEtVersion] taskIndex:" << v->getTaskIndex();
					delete v;
					v = nullptr;
				}
			}
		} );

		++m_unusedVersionIndex;
	}
}

KEtVersionManager::~KEtVersionManager()
{
	auto it = m_sparklineRefAreaVersions.begin();
	for (; it != m_sparklineRefAreaVersions.end(); it++) {
		delete (*it);
	}
	m_sparklineRefAreaVersions.clear();
}

void KEtVersionManager::confirmSparklineRefAreaVersion(KSparklineRefAreaVersion* version)
{
	if (NULL == version)
		return;

	m_sparklineRefAreaVersions.push_back(version);
}


KSparklineRefAreaVersion* KEtVersionManager::createSparklineRefAreaVersion()
{
	return new KSparklineRefAreaVersion(m_wb);
}

KSparklineRefAreaVersion* KEtVersionManager::getCurSparklineRefAreaVersion()
{
	if (m_sparklineRefAreaVersions.empty())
		return NULL;

	return m_sparklineRefAreaVersions.back();
}

KSparklineRefAreaVersion* KEtVersionManager::getSparklineRefAreaVersion(WebInt id)
{
	if (m_sparklineRefAreaVersions.empty())
		return NULL;

	return m_sparklineRefAreaVersions.at(id);
}

WebInt KEtVersionManager::getCurSparklineRefAreaVersionID()
{
	return getSparklineRefAreaVersionCount() - 1;
}

WebInt KEtVersionManager::getSparklineRefAreaVersionCount()
{
	return m_sparklineRefAreaVersions.size();
}

int64_t KEtVersionManager::GetMemoryComplexity()
{
	if (m_dataVersions.empty())
		return 0;
		
	int64_t c = 0;
	const size_t versCount = m_dataVersions.size();
	constexpr size_t maxCount = 100;
	for (size_t i = 0; i < versCount && i < maxCount; ++i)
	{
		KwVersion* pVer = m_dataVersions[versCount - i - 1]; // 优先用后面的
		if (pVer)
		{
			c += pVer->getSerialComplexity() + 1;
		}
	}

	if (versCount > maxCount)
	{
		c += versCount - maxCount;
	}

	c += m_sparklineRefAreaVersions.size();
	return c;
}

// KSparklineRefAreaVersion
//---------------------------------------------------------------------------------
KSparklineRefAreaVersion::KSparklineRefAreaVersion(IKWorkbook* wb)
	: m_wb(wb)
{
}

bool KSparklineRefAreaVersion::HasDirtyRefAreas()
{
	bool ret = false;
	if (m_DirtySparklineRefAreas.empty())
		return ret;

	for (auto it = m_DirtySparklineRefAreas.begin() ; it != m_DirtySparklineRefAreas.end() ; ++it)
	{
		if (!it->second.empty())
		{
			ret = true;
			break;
		}		
	}

	return ret;
}

void KSparklineRefAreaVersion::AddDirtySparklineGroups(IWorksheetObj* pSheetObj, SPARKLINE_GROUP_SET& src)
{
	if (src.empty())
		return;

	HostSet* pHostSet = m_DirtySparklineRefAreas.GetHostSet(pSheetObj, true);
	if (NULL == pHostSet)
		return;

	SPARKLINE_GROUP_SET& grpSet = pHostSet->m_SparklineGroupSet;
	grpSet.insert(src.begin(), src.end());
}


void KSparklineRefAreaVersion::AddDirtySparklines(IWorksheetObj* pSheetObj, SPARKLINE_SET& src)
{
	if (src.empty())
		return;

	HostSet* pHostSet = m_DirtySparklineRefAreas.GetHostSet(pSheetObj, true);
	if (NULL == pHostSet)
		return;

	SPARKLINE_SET& spkSet = pHostSet->m_SparklineSet;
	spkSet.insert(src.begin(), src.end());
}


KSparklineRefAreaVersion::DirtyRefAreas& KSparklineRefAreaVersion::GetDirtySparklineRefAreas()
{
	return m_DirtySparklineRefAreas;
}




// KSparklineRefAreaVersion::DirtyRefAreas
//---------------------------------------------------------------------------------


KSparklineRefAreaVersion::HostSet* KSparklineRefAreaVersion::DirtyRefAreas::GetHostSet(IWorksheetObj* pSheetObj, bool bCreate)
{
	HostSet* pHostSet = NULL;

	if (NULL == pSheetObj)
		return pHostSet;

	auto it = find(pSheetObj);
	if (it != end())
	{
		pHostSet = &(it->second);
	}
	else if (bCreate)
	{
		HostSet tmp;
		std::pair<DirtyRefAreas::iterator, bool> eleInserted;
		eleInserted = insert(std::make_pair(pSheetObj, tmp));

		if (eleInserted.second)
			pHostSet = &(eleInserted.first->second);
	}
	
	return pHostSet;
}


//---------------------------------------------------------------------------------


}
