﻿// -------------------------------------------------------------------------- //
//	文件名		：	kxarrangeprocessdata.h
//	创建者		：	chenyongquan2
//	创建时间	：	2021-06-21 16:49:00
//	功能描述	：	智能整理jsapi
//
// -------------------------------------------------------------------------- //

#ifndef __KAIET_SMARTDATASERVICE_ARRANGE_KXARRANGEPROCESSDATA_H__
#define __KAIET_SMARTDATASERVICE_ARRANGE_KXARRANGEPROCESSDATA_H__

#include "../proof/recognizedata.h"

namespace etai
{
float viewWidth2CharsWidth(int viewWidth, IKWorkbook *pWorkbook, bool bNeedDeviation = true);

enum AdaptScreenType
{
    AdaptScreenNormal,   //自适应
    AdaptScreenRowFirst, //行适应
    AdaptScreenColFirst, //列适应
};

enum SpacingType
{
    RowSpacing = 0, //行留白间距
    ColSpacing,     //列留白间距
};

enum WholeEffectType
{
    DefaultEffect = -1,
    AdaptEffect = 0,
    CompactEffect,
    LooseEffect,
    AdaptScreenEffect,
};

enum MergeCellType
{
    HorMergeCell = 0,   //横向合并
    VerMergeCell,       //纵向合并
    HorAndVerMergeCell, //多维合并

};

class WholeEffectBase
{
  public:
    WholeEffectBase();
    virtual ~WholeEffectBase();
    virtual double getColSpacingWithChar(double colWidthWidthChar) const = 0;
    virtual double getColMaxWidthWithChar() const;
    virtual double getImgCellColMinWidth() const;
    virtual double getImgCellRowMinHeight() const;

    virtual double getRowSpacingWithChar() const;

    virtual single getFontSizeByZoneType(ZoneType zoneType) const;
    virtual single getTitleRangeFontSize() const;
    virtual single getHeadRangeFontSize() const;
    virtual single getContentRangeFontSize() const;
    virtual single getSubTitleRangeFontSize() const;
    virtual single getOtherRangeFontSize() const;
    virtual single getInfoRangeFontSize() const;

  public:
    //字号
    int m_TitleRangeFontSize;
    int m_SubTitleRangeFontSize;
    int m_HeadRangeFontSize;
    int m_ContentRangeFontSize;
    int m_OtherRangeFontSize;
    int m_InfoRangeFontSize;
    //间距
    int m_RowHeightSpacing;
    //列宽最大值
    double m_ColMaxWidth;
    //嵌入图片单元格的行高列宽最小值
    double m_ImgCellColMinWidth;
    double m_ImgCellRowMinHeight;
};

class AdaptWholeEffect : public WholeEffectBase
{
  public:
    AdaptWholeEffect();
    virtual ~AdaptWholeEffect() = default;
    double getColSpacingWithChar(double colWidthWidthChar) const override;
};

class CompactWholeEffect : public WholeEffectBase
{
  public:
    CompactWholeEffect();
    virtual ~CompactWholeEffect() = default;
    double getColSpacingWithChar(double colWidthWidthChar) const override;
};

class LooseWholeEffect : public WholeEffectBase
{
  public:
    LooseWholeEffect();
    virtual ~LooseWholeEffect() = default;
    double getColSpacingWithChar(double colWidthWidthChar) const override;
};

class AdaptScreenWholeEffect : public WholeEffectBase
{
  public:
    AdaptScreenWholeEffect();
    virtual ~AdaptScreenWholeEffect() = default;
    double getColSpacingWithChar(double colWidthWidthChar) const override;
};

class TableApplyParam
{
  public:
    explicit TableApplyParam(WholeEffectType effectType = AdaptEffect);
    ~TableApplyParam() = default;
    void setEffectType(WholeEffectType effectType);
    void setApplyFont(bool bApplyFont);
    bool getApplyFont() const;
    WholeEffectType getEffectType() const;
    WholeEffectBase *getWholeEffectInfo() const;
    bool isNeedProcessBlank() const;
    QString getEffectTypeName() const;

  public:
    WholeEffectType m_effectType;
    std::unique_ptr<WholeEffectBase> m_spWholeEffect;
    bool bProcessBlank; //是否处理空格
    bool m_bApplyFont;
};

struct AtomicRange
{
  public:
    AtomicRange();
    AtomicRange(const AtomicRange &rg);
    AtomicRange(Range *pRange);
    AtomicRange(const ES_CUBE &cube);
    AtomicRange(long nT, long nB, long nL, long nR);
    virtual ~AtomicRange() = default;
    bool IsOneCell() const;
    bool IsInvalid() const;
    bool IsInRange(long iRow, long iCol) const;
    bool IsEqualRange(AtomicRange *pAtomicRange) const;

    bool IsOneRowCell() const;
    bool isOneColCell() const;

    int getRowCnt() const;
    int getColCnt() const;

    void ResetRange(Range *pRange);

  public:
    long iTop;
    long iBottom;
    long iLeft;
    long iRight;
};

struct AtomicCells : public AtomicRange
{
  public:
    AtomicCells(Range *pRange, IKWorkbook *pWorkbook) :
      AtomicRange(pRange), m_pWorkbook(pWorkbook)
    {
        m_fontName = QString::fromUtf8("Microsoft YaHei");
    }
    AtomicCells(const AtomicRange &rg)
      : AtomicRange(rg)
    {
        m_fontName = QString::fromUtf8("Microsoft YaHei");
    }
    AtomicCells &operator=(const AtomicCells &rs) = default;
    ~AtomicCells() override = default;
    void BuildCells(Range *pCell, ZoneType zoneType);
    void InitCellsProp(float fontSize, bool bAllowProcessBlank);
    double estimatedCellHeight(double dCurWidth, bool bTextOverflow = false);

  public:
    int   getIndentLevel() const;
    bool  isNeedSpecialApplyProp() const;
    bool  isContainLineBreak() const;
    bool  isModifiedText() const;
    bool  isWrapText() const;
    bool  isFontBold() const;
    bool  isImgFmlaCell() const;
    void  getEachParaTextWidthVec(std::vector<int>& vec) const;
    float getEstWidthWithChar() const;
    float getFontSize() const;
    double getCellCharHeight() const;
    double getCellEstimatedHeight(double dCurWidth) const;
    PCWSTR getModifiedText() const;
    QString getFontName() const;
    ZoneType getZoneType() const;
    ETHAlign getHAlign() const;
    ETVAlign getVAlign() const;

    void setTextOverflow(bool bTextOverflow);
    void setWrapText(bool bWrapText);
    void setHAlign(ETHAlign hAlign);
    void setVAlign(ETVAlign vAlign);

  private:
    int  estTextWidth(const ks_wstring& text) const;
    void initCharHeight();
    void doEstWidth();
    void processCellBlank();

  private:
    ks_wstring m_strContent;
    std::vector<int> m_vecEachParaTextWidth; //单位：磅
    QString m_fontName;

    IKWorkbook *m_pWorkbook = nullptr;
    double m_dCharHeight = -1;
    float m_contentWidthWithChar = -1;        //单位：字符
    float m_fFontSize = 10.0f;
    int m_emptyLineCnt = 0;
    int m_indentLevel = 0;
    ZoneType m_zoneType = Content;
    ETHAlign m_hAlign = etHAlignCenter;
    ETVAlign m_VAlign = etVAlignCenter;

    bool m_bTextOverflow = false;
    bool m_bLineBreak = false;
    bool m_bHasFormula = false;
    bool m_bImgFmlaCell = false;
    bool m_bBold = false;
    bool m_bWrapText = true;
    bool m_bModifyText = false; //标记文本是否发生了改变
};

struct AtomicRow
{
  public:
    std::vector<AtomicCells *> vecCells;
    AtomicRow(long i, int colCount) : m_iRow(i), m_dOriginHeight(0), m_dResultHeight(0), m_bHidden(false)
    {
        vecCells.reserve(colCount);
    }
    void setHidden(bool bHidden)
    {
        m_bHidden = bHidden;
    }
    bool isHidden() const
    {
        return m_bHidden;
    }
    int getRowIdx() const;
    void setRowResultHeight(double dHeight);
    double getRowResultHeight() const;

  private:
    double m_dOriginHeight;
    double m_dResultHeight;
    int m_iRow;
    bool m_bHidden;
};

typedef std::map<int, std::unique_ptr<AtomicRow>> AtomicRowMap;

struct AtomicCol
{
  public:
    std::vector<AtomicCells *> vecCells;
    AtomicCol(long i, int rowCount)
        : m_iCol(i), m_bNeedAlignLeft(false), m_dOriginWidth(0), m_dResultWidth(0), m_dMaxWidth(50), m_bHidden(false), m_hAlign(etHAlignCenter)
    {
        vecCells.reserve(rowCount);
    }
    void setHidden(bool bHidden)
    {
        m_bHidden = bHidden;
    }
    bool isHidden() const
    {
        return m_bHidden;
    }
    ETHAlign getHAlign() const
    {
        return m_hAlign;
    }
    int getColIdx() const;
    void calcColResultWidth(WholeEffectBase *pWholeEffect);
    void setColResultWidth(double dWidth);
    double getColResultWidth() const;

    void setColOriginWidth(double dWidth);
    void setColContentAlignLeft();

  private:
    void calcColResultTextWidth();

  public:
    long m_iCol;
    double m_dOriginWidth;
    double m_dResultWidth;
    double m_dMaxWidth;
    ETHAlign m_hAlign;
    bool m_bNeedAlignLeft;
    bool m_bHidden;
};

typedef std::map<int, std::unique_ptr<AtomicCol>> AtomicColMap;

struct MergeCellsList : public std::vector<AtomicCells *>
{
    AtomicCells *FindCellInMergeCells(long iRow, long iCol) const;
};

struct ImgFmlaCellsList : public std::vector<AtomicCells *>
{
    AtomicCells *FindCellInImgFmlaCells(AtomicCells *pAtomicRange) const;
};

struct AtomicTable : public AtomicRange
{
  public:
    AtomicTable(const AtomicRange &rg, IKWorksheet *pWorkSheet) : AtomicRange(rg), m_pWorkSheet(pWorkSheet){};
    ~AtomicTable() override = default;
    void BuildTable(Range *pRange, TableRangeInfo &tblInfo, TableApplyParam *pTableApplyParam);
    AtomicCells *NewCells(Range *pCellRange);
    AtomicCells *getMergeCell(long iRow, long iCol) const;
    int getFirstMergeColIdx() const;
    int getFirstHeaderRowIdx() const;

    // Todo:从process类搬来的代码，后续考虑写一个子类把这些新增方法放进去，而不全部放基类这里
    void checkAndExpandMergeCellHeight(AtomicCells *pEachAtomicCells, bool bEnableAlignLeft);
    // 获取单元格的预设宽度
    double getCellWidth(AtomicRange *pEachAtomicRange) const;
    double getMergeCellHeight(AtomicRange *pEachAtomicRange) const;
    void expandMergeCellHeight(AtomicRange *pEachAtomicRange, double dMoreRowHeight);
    double getPresetColWidth(int iCol) const;
    double getPresetRowHeight(int iRow) const;
    // 收集所有行的行高
    void presetRowHeight(int iRow, double dHeight);

    //拿到当前表格的高度和宽度
    void getTableSizeAndAve(double &w, double &h, double &wAve, double &hAve);
    TableRangeInfo getTaleRangeInfo() { return m_tblInfo; };

  public:
    MergeCellsList m_mergeCellsList;
    ImgFmlaCellsList m_imgFmlaCellsList;
    AtomicRowMap m_rowList;
    AtomicColMap m_colList;

  protected:
    std::vector<std::unique_ptr<AtomicRange>> m_vecTableCells; // 用于管理内存
    IKWorksheet *m_pWorkSheet;
    TableRangeInfo m_tblInfo;
    TableApplyParam *m_pTableApplyParam = nullptr;
};

// Todo:一个描述表格的数据结构，包括它的标题、副标题、行标题、内容的结构
struct TableInfo
{
  public:
    TableInfo() = default;
    bool isEmptyTableInfo();

  public:
    ES_CUBE allRangeInfo;
    std::vector<ES_CUBE> vecTitleRangeInfo;
    std::vector<ES_CUBE> vecHeadRangeInfo;
    std::vector<ES_CUBE> vecContentRangeInfo;
    std::vector<ES_CUBE> vecSubTitleRangeInfo;
    std::vector<ES_CUBE> vecOtherRangeInfo;
    std::vector<ES_CUBE> vecInfoRangeInfo;
};
//适应屏幕

struct ViewSize
{
  public:
    ViewSize() : m_ViewWidth(0), m_ViewHeight(0)
    {
    }
    void setW(int w)
    {
        m_ViewWidth = w;
    }
    void setH(int h)
    {
        m_ViewHeight = h;
    }
    int m_ViewWidth;
    int m_ViewHeight;
};

class AdaptScreenInfo
{
  public:
    AdaptScreenInfo();
    virtual ~AdaptScreenInfo() = default;
    void setViewSize(double w, double h);
    void setTableW(double w);
    void setTableH(double h);
    void setTableWidthAve(double ave);
    void setTableHeightAve(double ave);

    double getTableW() const
    {
        return m_wTableTotal;
    }
    double getTableH() const
    {
        return m_hTableTotal;
    }
    double getViewW() const
    {
        return m_wViewWithChar;
    }
    double getViewH() const
    {
        return m_hViewWithPound;
    }
    double getWidthAve() const
    {
        return m_wTableAve;
    }
    double getHeightAve() const
    {
        return m_hTableAve;
    }

    AdaptScreenType calcAdaptScreenType();

  private:
    AdaptScreenType m_Type; //类型
    //视图大小
    double m_wViewWithChar;  //单位：列宽单位，字符数
    double m_hViewWithPound; //单位：行高单位，磅
    //表的大小
    double m_wTableTotal; //单位：列宽单位，字符数
    double m_hTableTotal; //单位：行高单位，磅
    //表的平均列宽行高
    double m_wTableAve;
    double m_hTableAve;
};

//使用策略模式
class adaptScreenStrategy
{
  public:
    adaptScreenStrategy(AtomicTable *pTable, AdaptScreenInfo *pAdaptInfo);
    virtual ~adaptScreenStrategy() = default;
    virtual void doAdaptScreen() = 0;
    void printfDebugInfo();

  protected:
    double adaptColWidth(double wTable, double W);
    double adaptRowHeight(double hTable, double H);

    void updateTableTotalWidth();
    void updateTableTotalHeight();

  protected:
    AtomicTable *m_pAtomicTable;
    AdaptScreenInfo *m_pAdaptInfo;
};

class adaptScreenNormalStrategy : public adaptScreenStrategy
{
  public:
    adaptScreenNormalStrategy(AtomicTable *pTable, AdaptScreenInfo *pAdaptInfo);
    virtual ~adaptScreenNormalStrategy() = default;
    virtual void doAdaptScreen() override;
};

class adaptScreenColFirstStrategy : public adaptScreenStrategy
{
  public:
    adaptScreenColFirstStrategy(AtomicTable *pTable, AdaptScreenInfo *pAdaptInfo);
    virtual ~adaptScreenColFirstStrategy() = default;
    virtual void doAdaptScreen() override;

  private:
    //折行处理
    void adaptCellsInCol(AtomicCol *pAtomicCol, double newWidth);
    //行高优化
    void adaptRowHeightToViewH();
};

class adaptScreenRowFirstStrategy : public adaptScreenStrategy
{
  public:
    adaptScreenRowFirstStrategy(AtomicTable *pTable, AdaptScreenInfo *pAdaptInfo);
    virtual ~adaptScreenRowFirstStrategy() = default;
    virtual void doAdaptScreen() override;
};

//运用“代理模式”，外层直接通过这个代理类去调用适应屏幕调整，而不用去区分要调用哪一种“策略”
class adaptScreenProcessProxy
{
  public:
    adaptScreenProcessProxy(AtomicTable *pTable, const ViewSize &viewSz);
    ~adaptScreenProcessProxy() = default;
    void doAdaptScreen();
    void init(AtomicTable *pTable, const ViewSize &viewSz);

  private:
    std::unique_ptr<adaptScreenStrategy> m_spStrategy;
    std::unique_ptr<AdaptScreenInfo> m_spAdaptInfo;
};
} // namespace etai
#endif //__KAIET_SMARTDATASERVICE_ARRANGE_KXARRANGEPROCESSDATA_H__