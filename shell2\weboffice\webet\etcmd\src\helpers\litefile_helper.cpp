#include "etstdafx.h"
#include "litefile_helper.h"
#include "binvariant/binvarobj.h"
#include "et_hard_define_strings.h"
#include "logger.h"
#include "src/util.h"
#include "workbooks.h"
using namespace binary_wo;

// 从RANGE的私有方法copy, 怀疑设置Range时违反数据完整性导致子进程抛异常崩溃, 增加预检和日志抓一下现场
namespace
{
inline BOOL IsRangeValid(IDX sheetFrom, IDX sheetTo, ROW rowFrom, ROW rowTo, COL colFrom, COL colTo, IBook* pBook)
{
    BOOL bSht =	sheetFrom == RCB_NONE_LT && sheetTo == RCB_NONE_RB ||
                0 <= sheetFrom && sheetFrom <= sheetTo && sheetTo < MAX_SHEET_COUNT;

    BOOL bRow =	rowFrom == RCB_NONE_LT && rowTo == RCB_NONE_RB ||
                0 <= rowFrom && rowFrom <= rowTo && rowTo < pBook->GetBMP()->cntRows;

    BOOL bCol =	colFrom == RCB_NONE_LT && colTo == RCB_NONE_RB ||
                0 <= colFrom && colFrom <= colTo && colTo < pBook->GetBMP()->cntCols;
    return (bSht && bRow && bCol);
}
}

namespace wo
{
    LiteFileUpdateParams::LiteFileUpdateParams()
        : mode(BookExportLiteVersionMode_Full)
    {
    }

    LiteFileParamsHelper::LiteFileParamsHelper(wo::KEtWorkbook* wb, binary_wo::VarObj& obj)
      : m_pWb(wb)
      , m_obj(obj)
      , m_localSheetCount(0)
      , m_bNeedConvertSheetFromTo(false)
    {
        m_pBook = wb->GetCoreWorkbook()->GetBook();
    }

    void LiteFileParamsHelper::Do(LiteFileUpdateParams &res)
    {
        // 1. 检查是否存在有效ranges, 不存在则走全量
        if (!m_obj.has("ranges"))
        {
            res.mode = BookExportLiteVersionMode_Full;
            return;
        }

        VarObj ranges = m_obj.get_s("ranges");
        int rangesCount = ranges.arrayLength_s();
        if (0 == rangesCount)
        {
            res.mode = BookExportLiteVersionMode_Full;
            return;
        }
        res.mode = BookExportLiteVersionMode_SpecifiedRanges;

        // 2. 收集自身的sheet信息
        m_pBook->GetSheetCount(&m_localSheetCount);
        m_localSheetNames.reserve(m_localSheetCount);
        for (IDX sheetIdx = 0; sheetIdx < m_localSheetCount; sheetIdx++)
        {
            ks_stdptr<ISheet> spSheet;
            m_pBook->GetSheet(sheetIdx, &spSheet);
            PCWSTR sheetName = nullptr;
            spSheet->GetName(&sheetName);
            if (0 == xstrcmp(sheetName, STR_CELL_IMAGE_SHEET_NAME))
                continue;
            m_localSheetNames.emplace_back(sheetName);
        }
        m_localSheetCount = static_cast<int>(m_localSheetNames.size());
        
        // 3. 处理传入的sheetNames参数
        checkSheetNames();

        // 4. 处理传入的ranges参数
        auto IsValidSheetIdx = [&](IDX sheetFrom, IDX sheetTo) -> bool
        {
            if (IsSingleSheet())
                return true;
            if (sheetFrom < 0 || sheetTo < 0)
            {
                WOLOG_ERROR << "[LiteFileParamsHelper]: Invalid sheetFrom/sheetTo " << sheetFrom << " " << sheetTo;
                wo::util::CollectInfo(m_pWb, __X("behaviour_saveaslite_invalidarg"), sheetFrom, sheetTo);
            }
            if (m_bNeedConvertSheetFromTo)
                return sheetFrom >= 0 && sheetTo >= 0 &&
                       sheetFrom < m_passed2Local.size() && m_passed2Local[sheetFrom] != -1 &&
                       sheetTo < m_passed2Local.size() && m_passed2Local[sheetTo] != -1;
            else
                return sheetFrom < m_localSheetCount && sheetTo < m_localSheetCount;
        };

        res.updateRanges.reserve(rangesCount);
        for (int i = 0; i < rangesCount; ++i)
        {
            VarObj rangeObj = ranges.at_s(i);
            RANGE rg(m_pBook->GetBMP());
            if (!loadRanges(rangeObj, rg, IsValidSheetIdx))
            {
                // 有任何一个range不合法就回退到全量模式,因为这种情况下,订阅者可以选择重新指定源,可能用到任何sheet的数据
                res.mode = BookExportLiteVersionMode_Full;
                return;
            }
            res.updateRanges.emplace_back(rg);
        }
        adjustRangeForDB(res);
    }

    // 如果当前sheet数量大于1, 则需要依据提供的Names进行匹配,然后处理传入sheet的时候对sheetFrom和sheetTo进行转换
    // 如果当前sheet数量等于1, 则将所有传入的range的sheetFrom和sheetTo转为0 (指向当前的唯一一个sheet)
    void LiteFileParamsHelper::checkSheetNames()
    {
        if (!m_obj.has("sheetNames") || IsSingleSheet())
            return;
        VarObj passedSheetNames = m_obj.get_s("sheetNames");
        int passedSheetNamesCount = passedSheetNames.arrayLength_s();
        if (0 == passedSheetNamesCount)
            return;
        m_bNeedConvertSheetFromTo = true;
        m_passed2Local.resize(passedSheetNamesCount, -1);
        for (int i = 0; i < passedSheetNamesCount; ++i)
        {
            VarObj sheetNameItem = passedSheetNames.at_s(i);
            PCWSTR sheetName = sheetNameItem.value_str();
            for (int j = 0; j < m_localSheetCount; ++j)
            {
                if (0 == xstrcmp(sheetName, m_localSheetNames[j]))
                {
                    m_passed2Local[i] = j;
                    break;
                }
            }
        }
    }

    bool LiteFileParamsHelper::loadRanges(const VarObj &obj, RANGE &rg, const std::function<bool(int, int)> &isValid) const
    {
        IDX sheetFrom = obj.field_int32("sheetFrom");
        IDX sheetTo = obj.field_int32("sheetTo");
        if (!isValid(sheetFrom, sheetTo))
            return false;
        if (IsSingleSheet())
        {
            sheetFrom = 0, sheetTo = 0;
        }
        else
        {
            sheetFrom = m_bNeedConvertSheetFromTo ? m_passed2Local[sheetFrom] : sheetFrom;
            sheetTo = m_bNeedConvertSheetFromTo ? m_passed2Local[sheetTo] : sheetTo;
        }
        ROW rowFrom = obj.field_int32("rowFrom");
        ROW rowTo = obj.field_int32("rowTo");
        COL colFrom = obj.field_int32("colFrom");
        COL colTo = obj.field_int32("colTo");

        bool bValidRange = IsRangeValid(sheetFrom, sheetTo, rowFrom, rowTo, colFrom, colTo, m_pBook);
        if (!bValidRange)
        {
            WOLOG_ERROR << "[LiteFileParamsHelper]: Invalid Range after transform: " << sheetFrom << " " << sheetTo << " "
            << rowFrom << " " << rowTo << " " << colFrom << " " << colTo;
            wo::util::CollectInfo(m_pWb, __X("behaviour_saveaslite_invalidarg"), sheetFrom, sheetTo);
            return false;
        }
        rg.SetSheetFromTo(sheetFrom, sheetTo);
        rg.SetRowFromTo(rowFrom, rowTo);
        rg.SetColFromTo(colFrom, colTo);
        return true;
    }

    void LiteFileParamsHelper::adjustRangeForDB(LiteFileUpdateParams &res) const
    {
        std::unordered_map<int, EtDbIdx> dbSheetRecordCountCache;
        int nSheetCount = 0;
        VS(m_pBook->GetSheetCount(&nSheetCount));
        HRESULT hr = S_OK;
        for (int i = 0; i < static_cast<int>(nSheetCount); ++i)
        {
            ks_stdptr<ISheet> spSheet;
            hr = m_pBook->GetSheet(i, &spSheet);
            if (FAILED(hr))
                continue;
            if (!spSheet->IsDbSheet())
                continue;
            ks_stdptr<IDBSheetOp> spDbSheetOp;
            hr = spSheet->GetExtDataItem(edSheetDbOp, (IUnknown **) &spDbSheetOp);
            if (FAILED(hr))
                dbSheetRecordCountCache.emplace(i, 0);
            dbSheetRecordCountCache.emplace(i, spDbSheetOp->GetAllRecords()->Count());
        }
        for (auto &range: res.updateRanges)
        {
            int sheetFrom = range.SheetFrom();
            int sheetTo = range.SheetTo();
            if (sheetFrom == sheetTo)
            {
                auto it = dbSheetRecordCountCache.find(sheetFrom);
                if (it == dbSheetRecordCountCache.end())
                    continue;
                // 目前DB只支持整列引用, 如果以后发生变更需要同步修改
                range.SetRowFromTo(0, static_cast<ROW>(it->second));
            }
        }
    }
}
