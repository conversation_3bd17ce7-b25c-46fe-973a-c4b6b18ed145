﻿#include "data_analyze_task.h"
#include "util.h"
#include "data_analyze.h"
#include "wo/sa_helper.h"
#include "workbook.h"
#include "data_analyze_helper.h"
#include "helpers/varobject_helper.h"
#include "dataanalyze/et_dataanalyze.h"
#include "dataanalyze/etdacoreexport.h"
#include <webbase/binvariant/binvarobj.h>
#include "helpers/sheet_operator_helper.h"
#include "hresult_to_string.h"
namespace wo
{
class OutputAddDataScope
{
public:
	OutputAddDataScope(ExecDAOutput* pOutput, binary_wo::BinWriter* pBw) : m_pOutput(pOutput), m_pBw(pBw) {}
	~OutputAddDataScope()
	{
		if (m_pOutput && m_pBw)
			m_pOutput->AddData(m_pBw);
	}
private:
ExecDAOutput* m_pOutput = nullptr;
binary_wo::BinWriter* m_pBw = nullptr;
};

ExecDAOutput::ExecDAOutput(WebSlice* pOutput) : m_pOutput(pOutput)
{

}

ExecDAOutput::~ExecDAOutput()
{
	if (!m_pOutput)
		return;
	if (!m_bAddError)
	{
		m_bw.addStringField(__X("0"), "errorCode");
		m_bw.addStringField(__X("SUCCESS"), "errorMsg");
	}
	BinWriter::StreamHolder shbt = m_bw.buildStream();
	m_pOutput->data = shbt.detach();//	服务端调用之后通过FreeSyncProcWebSlice 释放内存
	m_pOutput->size = m_bw.writeLength();
	ASSERT(m_pOutput->size != 0);
}

void ExecDAOutput::AddError(int errorCode, PCWSTR msg)
{
	if (m_bAddError)	//避免覆盖
		return;
	m_bAddError = true;
	m_bw.addStringField(krt::utf16(QString::number(errorCode)), "errorCode");
	m_bw.addStringField(msg, "errorMsg");
}

void ExecDAOutput::AddData(binary_wo::BinWriter* dataBw)
{
	binary_wo::BinWriter::StreamHolder shbt = dataBw->buildStream();

	m_bw.beginArray("data");
	m_bw.addUint8Array((const uint8*)shbt.get(), dataBw->writeLength());
	m_bw.endArray();
}

using binary_wo::VarObj;
using TaskNameMap = std::unordered_map<PCWSTR, DataAnalyzeTaskType, util::StrHasher, util::StrEqual>;

TaskNameMap initTaskCreator()
{
	TaskNameMap retMap;
	retMap.insert(std::make_pair(CreateDAMergeTask::GetTag(), CreateDAMergeTask::GetType()));
	retMap.insert(std::make_pair(StartDAMergeTask::GetTag(), StartDAMergeTask::GetType()));
	retMap.insert(std::make_pair(UpdateDAMergeTask::GetTag(), UpdateDAMergeTask::GetType()));
	retMap.insert(std::make_pair(CancelDAMergeTask::GetTag(), CancelDAMergeTask::GetType()));
	retMap.insert(std::make_pair(UpdateDAMergeLogTask::GetTag(), UpdateDAMergeLogTask::GetType()));

	return retMap;
};

std::unique_ptr<DataAnalyzeTaskBase> DataAnalyzeTaskFactory::CreateDataAnalyzeCmd(const VarObj& cmd, KEtWorkbook* wwb)
{
	static TaskNameMap s_tag2Type = initTaskCreator();
	if (!cmd.has("name"))
		return nullptr;
	ks_wstring cmdName = cmd.field_str("name");
	auto itr = s_tag2Type.find(cmdName.c_str());
	DataAnalyzeTaskType type = DATask_Base;
	if (itr != s_tag2Type.end())
		type =itr->second;
	switch (type)
	{
		case DATask_CreateMerge:
			return std::make_unique<CreateDAMergeTask>(wwb);
		case DATask_StartMerge:
			return std::make_unique<StartDAMergeTask>(wwb);
		case DATask_UpdateMerge:
			return std::make_unique<UpdateDAMergeTask>(wwb);
		case DATask_CancelMerge:
			return std::make_unique<CancelDAMergeTask>(wwb);
		case DATask_UpdateMergeLog:
			return std::make_unique<UpdateDAMergeLogTask>(wwb);
		default:
			return nullptr;
	}
}

PCWSTR CreateDAMergeTask::GetTag()
{
	return __X("et.createDataAnalyzeMerge");
}

DataAnalyzeTaskType CreateDAMergeTask::GetType()
{
	return DATask_CreateMerge;
}

HRESULT CreateDAMergeTask::operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output)
{
	WOLOG_INFO << "[ETDA][EtHttpCreateDataAnalyzeMergeTaskClass] begin";
	IETDAClient* pEtdaClient = getDAClientInstance();

	if (!userContext || !userContext->userID || !userContext->connID)
		return WO_INVALID_ARG;

	ks_wstring connId(ET_A2W(ks_string(userContext->connID), true));
	ks_wstring userId(ET_A2W(ks_string(userContext->userID), true));
	if (!pEtdaClient->CheckUserId(userId))
	{
		output->AddError(etda::INVAILD_CONNID, etda::getErrorMsg<etda::START_PROCESS_FAILED>());
		WOLOG_INFO << "[ETDA][EtHttpCreateDataAnalyzeMergeTaskClass] user id is invalid :" << userId;
		return WO_FAIL;
	}
	WOLOG_INFO << "[ETDA][EtHttpCreateDataAnalyzeMergeTaskClass] user id is:" << userId;

	pEtdaClient->SetCurUserId(userId);
	VAR_OBJ_EXPECT_STRUCT(param, "mergeInfo")
	binary_wo::VarObj mergeInfo = param.get("mergeInfo");
	VAR_OBJ_EXPECT_STRUCT(mergeInfo, "mergeConfig")
	binary_wo::VarObj mergeConfig = mergeInfo.get("mergeConfig");

	VAR_OBJ_EXPECT_STRING(mergeConfig, "mergeDirection")
	PCWSTR direction = mergeConfig.field_str("mergeDirection");
	PCWSTR joinMode = nullptr;
	if (mergeConfig.has("joinMode"))
		joinMode = mergeConfig.field_str("joinMode");
	
	std::unique_ptr<etda::MergeInfo>spInfo = std::make_unique<etda::MergeInfo>();
	spInfo->mergeConfig.mergeDirection = ParseMergeDirection(direction);
	if (joinMode)
	{
		spInfo->mergeConfig.joinMode = ParseJoinModeStr(joinMode);
	}
	
	if(mergeInfo.has("execResultConfig"))
	{
		binary_wo::VarObj execResultConfig = mergeInfo.get("execResultConfig");
		TaskParamProcessor::ParseExecResultConfig(spInfo.get(), execResultConfig);
	}

	VAR_OBJ_EXPECT_ARRAY(mergeInfo, "dataSourceConfig")
	binary_wo::VarObj dataSourceConfig = mergeInfo.get("dataSourceConfig");

	binary_wo::BinWriter bw;
	KSerialWrapBinWriter acpt(bw, nullptr);
	OutputAddDataScope addDataScope(output, &bw);
	acpt.addKey("fileList");
	acpt.beginArray();

	for (int i = 0; i < dataSourceConfig.arrayLength_s(); i++)
	{
		binary_wo::VarObj dataSourceConfigItem = dataSourceConfig.at_s(i);
		spInfo->dataSourceConfig.push_back(TaskParamProcessor::ParseDataSourceConfig(dataSourceConfigItem, acpt));
	}
	acpt.endArray();

	if (spInfo->dataSourceConfig.size() < 2)
		return WO_OK;

	pEtdaClient->ClearMergeInfo();
	int taskId = pEtdaClient->AddMergeInfo(spInfo.release());
	acpt.addInt16("taskId", taskId);
	WOLOG_INFO << "[ETDA][EtHttpCreateDataAnalyzeMergeTaskClass] add taskID:" << taskId;
	WOLOG_INFO << "[ETDA][EtHttpCreateDataAnalyzeMergeTaskClass] end";
	return WO_OK;
}

PCWSTR StartDAMergeTask::GetTag()
{
	return __X("et.startDataAnalyzeMerge");
}

DataAnalyzeTaskType StartDAMergeTask::GetType()
{
	return DATask_StartMerge;
}

HRESULT StartDAMergeTask::operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output)
{
	WOLOG_INFO << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] begin";
	IETDAClient* pEtdaClient = getDAClientInstance();
	ks_wstring userId(ET_A2W(ks_string(userContext->userID), true));
	if (!pEtdaClient->CheckUserId(userId))
	{
		output->AddError(etda::INVAILD_CONNID, etda::getErrorMsg<etda::START_PROCESS_FAILED>());
		WOLOG_ERROR << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] user id is invalid :" << userId;
		return WO_FAIL;
	}
	WOLOG_INFO << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] user id is:" << userId;

	pEtdaClient->SetCurUserId(userId);
	VAR_OBJ_EXPECT_ARRAY(param, "fileList")
	binary_wo::VarObj fileList = param.get_s("fileList");
	std::unordered_map<int, QString> sourceIdMap;
	VAR_OBJ_EXPECT_STRUCT(param, "taskParam")
	binary_wo::VarObj taskParam = param.get("taskParam");

	MergeTaskParam mergeTaskParam;
	mergeTaskParam.resultFilePath = __X("");

	HRESULT hr = TaskParamProcessor::checkTaskParam(taskParam, mergeTaskParam);
	if (hr != S_OK) 
	{
		WOLOG_ERROR << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] checkTaskParam failed";
		return hr;
	}
	ASSERT(pEtdaClient);
	WOLOG_INFO << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] isNewTask: " << mergeTaskParam.isNewTask;
	if (!mergeTaskParam.isNewTask) 
	{
		IDX sheetIdx = -1;
		IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
		pBook->STSheetToRTSheet(mergeTaskParam.sheetStId, &sheetIdx);
		mergeTaskParam.pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		if (mergeTaskParam.pWorksheet) 
		{
			ks_stdptr<IEtDataAnalyzeData> spData;
			mergeTaskParam.pWorksheet->GetSheet()->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
			if (!spData)
			{
				WOLOG_ERROR << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] not find data analyze data";
				return WO_FAIL;
			}
			mergeTaskParam.pData = spData.get();
			mergeTaskParam.pInfo = spData->GetDataAnalyseInfo(mergeTaskParam.taskId);
		}
	}
	else 
	{
		mergeTaskParam.pInfo = pEtdaClient->GetMergeInfo(mergeTaskParam.taskId);
	}

	if (!mergeTaskParam.pInfo) 
	{
		output->AddError(etda::NO_TASKID, etda::getErrorMsg<etda::NO_TASKID>());
		WOLOG_ERROR << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] not find merge info";
		return WO_FAIL;
	}

	hr = FileProcessor::processFileList(fileList, mergeTaskParam.sourceIdMap, mergeTaskParam.pInfo, output);
	if (hr != S_OK) 
	{
		WOLOG_ERROR << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] process file list fail ";
		return hr;
	}
	QString fileId = krt::fromUtf16(m_wwb->getFileId());
	pEtdaClient->Quit(true, false);
	int pid = pEtdaClient->StartDaProcess(fileId);
	if (pid == 0)
	{
		WOLOG_ERROR << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] start process fail ";
		output->AddError(etda::START_PROCESS_FAILED, etda::getErrorMsg<etda::START_PROCESS_FAILED>());
		return WO_FAIL;
	}

	binary_wo::BinWriter bw;
	KSerialWrapBinWriter acpt(bw, nullptr);
	OutputAddDataScope addDataScope(output, &bw);
	acpt.addInt32("subProcessId", pid);
	std::thread t(&DATaskExecutor::execMergeTask, mergeTaskParam);
	t.detach();
	WOLOG_INFO << "[ETDA][EtHttpStartDataAnalyzeMergeTaskClass] end";
	return WO_OK;
}

PCWSTR UpdateDAMergeTask::GetTag()
{
	return __X("et.updateDataAnalyzeMerge");
}

DataAnalyzeTaskType UpdateDAMergeTask::GetType()
{
	return DATask_UpdateMerge;
}

HRESULT UpdateDAMergeTask::operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output)
{
	WOLOG_INFO << "[ETDA][EtHttpUpdateDataAnalyzeMergeTaskClass] begin";
	VAR_OBJ_EXPECT_NUMERIC(param, "taskId")
	int taskId = param.field_int32("taskId");
	
	VAR_OBJ_EXPECT_STRUCT(param, "mergeInfo")
	binary_wo::VarObj mergeInfo = param.get("mergeInfo");
	VAR_OBJ_EXPECT_STRUCT(mergeInfo, "mergeConfig")
	binary_wo::VarObj mergeConfig = mergeInfo.get("mergeConfig");

	VAR_OBJ_EXPECT_STRING(mergeConfig, "mergeDirection")
	PCWSTR direction = mergeConfig.field_str("mergeDirection");
	PCWSTR joinMode = nullptr;
	if (mergeConfig.has("joinMode"))
		joinMode = mergeConfig.field_str("joinMode");
	VAR_OBJ_EXPECT_NUMERIC(param, "taskStatus")
	int taskStatus = param.field_int32("taskStatus");
	
	etda::MergeInfo* pInfo = nullptr;
	if (taskStatus == 0 && param.has("sheetStId"))
	{
		int sheetStId = param.field_int32("sheetStId");
		IDX sheetIdx = -1;
		IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
		pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
		IKWorksheet* pWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		if (pWorksheet) 
		{
			ks_stdptr<IEtDataAnalyzeData> spData;
			pWorksheet->GetSheet()->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
			if (!spData)
			{
				output->AddError(etda::NO_TASKID, etda::getErrorMsg<etda::NO_TASKID>());
				return WO_FAIL;
			}
			pInfo = spData->GetDataAnalyseInfo(taskId);
		}
	}
	else
	{
		pInfo = getDAClientInstance()->GetMergeInfo(taskId);
	}
	if (!pInfo)
	{
		WOLOG_ERROR << "[ETDA][EtHttpUpdateDataAnalyzeMergeTaskClass] No MergeInfo";
		output->AddError(etda::NO_TASKID, etda::getErrorMsg<etda::NO_TASKID>());
		return WO_FAIL;
	}
	pInfo->mergeConfig.mergeDirection = ParseMergeDirection(direction);
	if (pInfo->mergeConfig.mergeDirection == etda::MergeType::Join && joinMode)
	{
		pInfo->mergeConfig.joinMode = ParseJoinModeStr(joinMode);
	}

	if(mergeInfo.has("execResultConfig"))
	{
		binary_wo::VarObj execResultConfig = mergeInfo.get("execResultConfig");
		TaskParamProcessor::ParseExecResultConfig(pInfo, execResultConfig);
	}
	
	VAR_OBJ_EXPECT_ARRAY(mergeInfo, "dataSourceConfig")
	binary_wo::VarObj dataSourceConfig = mergeInfo.get("dataSourceConfig");

	binary_wo::BinWriter bw;
	KSerialWrapBinWriter acpt(bw, nullptr);
	OutputAddDataScope addDataScope(output, &bw);
	acpt.addKey("fileList");
	acpt.beginArray();
	pInfo->dataSourceConfig.clear();
	for (int i = 0; i < dataSourceConfig.arrayLength_s(); i++)
	{
		binary_wo::VarObj dataSourceConfigItem = dataSourceConfig.at_s(i);
		pInfo->dataSourceConfig.push_back(TaskParamProcessor::ParseDataSourceConfig(dataSourceConfigItem, acpt));
	}
	acpt.endArray();
	WOLOG_INFO << "[ETDA][EtHttpUpdateDataAnalyzeMergeTaskClass] end";
	return WO_OK;
}

PCWSTR CancelDAMergeTask::GetTag()
{
	return __X("et.cancelDataAnalyze");
}

DataAnalyzeTaskType CancelDAMergeTask::GetType()
{
	return DATask_CancelMerge;
}

HRESULT CancelDAMergeTask::operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output)
{
	WOLOG_INFO << "[ETDA][EtHttpCancelDataAnalyzeMergeTaskClass] begin";
	IETDAClient* pEtdaClient = getDAClientInstance();
	ks_wstring userId(ET_A2W(ks_string(userContext->userID), true));
	if (!pEtdaClient->CheckUserId(userId))
	{
		output->AddError(etda::INVAILD_CONNID, etda::getErrorMsg<etda::START_PROCESS_FAILED>());
		WOLOG_ERROR << "[ETDA][EtHttpCancelDataAnalyzeMergeTaskClass] user id is invalid :" << userId;
		return WO_FAIL;
	}
	VAR_OBJ_EXPECT_NUMERIC(param, "taskId")
	int taskId = param.field_int32("taskId");
	WOLOG_INFO << "[ETDA][EtHttpCancelDataAnalyzeMergeTaskClass] cancel task " << taskId;
	int res = getDAClientInstance()->Quit(false, true);
	if (res != 0)
	{
		WOLOG_ERROR << "[ETDA][EtHttpCancelDataAnalyzeMergeTaskClass] process quit fail";
	}
	else 
	{
		WOLOG_INFO << "[ETDA][EtHttpCancelDataAnalyzeMergeTaskClass] process quit success";
	}
	pEtdaClient->SetCurUserId(ks_wstring());
	WOLOG_INFO << "[ETDA][EtHttpCancelDataAnalyzeMergeTaskClass] end";
	return WO_OK;
}

PCWSTR UpdateDAMergeLogTask::GetTag()
{
	return __X("et.updateDataAnalyzeMergeLog");
}

DataAnalyzeTaskType UpdateDAMergeLogTask::GetType()
{
	return DATask_UpdateMergeLog;
}

HRESULT UpdateDAMergeLogTask::operator()(const UserContext* userContext, const binary_wo::VarObj& param, ExecDAOutput* output)
{
	WOLOG_INFO << "[ETDA][EtHttpUpdateDataAnalyzeMergeLogClass] begin";
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetStId")
	IDX sheetIdx = SheetOperatorHelper::GetSheetIdx(m_wwb, param);
	if (INVALIDIDX == sheetIdx)
	{
		WOLOG_ERROR << "[ETDA][EtHttpUpdateDataAnalyzeMergeLogClass] SHEET_NOT_FOUND";
		return WO_SHEET_NOT_FOUND;
	}
	ks_stdptr<ISheet> spSheet;
	m_wwb->GetCoreWorkbook()->GetBook()->GetSheet(sheetIdx, &spSheet);
	if (!spSheet)
	{
		WOLOG_ERROR << "[ETDA][EtHttpUpdateDataAnalyzeMergeLogClass] SHEET_NOT_FOUND";
		return WO_SHEET_NOT_FOUND;
	}
	VAR_OBJ_EXPECT_NUMERIC(param, "taskId")
	int taskId = param.field_int32("taskId");
	ks_stdptr<IEtDataAnalyzeData> spData;
	spSheet->GetExtDataItem(edDataAnalyzeData, (IUnknown **)&spData);
	if (!spData)
	{
		output->AddError(etda::NO_TASKID, etda::getErrorMsg<etda::NO_TASKID>());
		WOLOG_ERROR << "[ETDA][EtHttpUpdateDataAnalyzeMergeLogClass] GetExtDataItem failed";
		return WO_FAIL;
	}
	etda::MergeLog log;
	VAR_OBJ_EXPECT_NUMERIC(param, "logId")
	log.logId = param.field_int32("logId");
	VAR_OBJ_EXPECT_STRING(param, "date")
	log.date = krt::fromUtf16(param.field_str("date"));
	VAR_OBJ_EXPECT_STRING(param, "time")
	log.time = krt::fromUtf16(param.field_str("time"));
	VAR_OBJ_EXPECT_STRING(param, "status")
	log.status = krt::fromUtf16(param.field_str("status"));
	VAR_OBJ_EXPECT_NUMERIC(param, "updateType")
	log.updateType = param.field_int32("updateType");
	VAR_OBJ_EXPECT_ARRAY(param, "sources")
	binary_wo::VarObj sources = param.get_s("sources");
	std::vector<etda::ImportFileLog> sourcesLog;
	for (int i = 0; i < sources.arrayLength_s(); i++)
	{
		binary_wo::VarObj item = sources.at_s(i);
		etda::ImportFileLog fileLog;
		VAR_OBJ_EXPECT_STRING(item, "fileName")
		fileLog.fileName = krt::fromUtf16(item.field_str("fileName"));
		VAR_OBJ_EXPECT_STRING(item, "sheetName")
		fileLog.sheetName = krt::fromUtf16(item.field_str("sheetName"));
		VAR_OBJ_EXPECT_STRING(item, "status")
		if (item.has("errorCode"))
		{
			fileLog.errorCode = krt::fromUtf16(item.field_str("errorCode"));
		}
		sourcesLog.push_back(fileLog);
	}
	log.sources = sourcesLog;
	HRESULT hr = spData->UpdateDataAnalyseLog(log);
	WOLOG_INFO << "[ETDA][EtHttpUpdateDataAnalyzeMergeLogClass] end hr: " << hr;
	return hr;
}

}