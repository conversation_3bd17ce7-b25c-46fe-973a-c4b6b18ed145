﻿#ifndef __SERIALIZE_VARIANT_HELPER_H__
#define __SERIALIZE_VARIANT_HELPER_H__
#include "kfc/variant.h"

namespace wo
{
namespace SerializeVariantHelper
{
    /*!
    * @brief 序列化Variant
    * @param bForceString 将variant转为string进行序列化
    * @param bDiscrete 是否以离散的方式序列化数组
    */
    WebInt Serialize(ISerialAcceptor* acpt, const KComVariant& variant, WebName name = nullptr, bool bForceString = false, bool bDiscrete = false);
    /*!
    * @brief 判断是否需要以离散的方式序列化数组，暂定空cell占比超过2/3时以离散的方式
    */
    bool NeedSerializeDiscrete(const KComVariant& var);
    WebInt SerializeSafeArray(SAFEARRAY *pArray,ISerialAcceptor* acpt, WebName name, bool bForceString, bool bDiscrete);
    WebInt SerializeSafeArrayInner(SAFEARRAY *pArray, UINT nDims, SIZE32* indexes, ISerialAcceptor* acpt, bool bForceString, bool bDiscrete);

}  // namespace SerializeVariantHelper
}  // namespace wo

#endif // __SERIALIZE_VARIANT_HELPER_H__