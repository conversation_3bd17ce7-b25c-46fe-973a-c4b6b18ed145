﻿#include "share_link_content_visibility_checker.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "appcore/et_appcore_shared_link_sheet.h"
#include "webextension/etwebextension_i.h"

namespace wo {

std::unique_ptr<ShareLinkContentVisibilityChecker> ShareLinkContentVisibilityChecker::CreateInstance(IBook* pBook, PCWSTR shareId)
{
    if (!shareId || !shareId[0])
        return nullptr;

    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
    pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**) &spSharedLinkMgr);
    if (!spSharedLinkMgr)
        return nullptr;

    ISharedLink* pSharedLink = spSharedLinkMgr->GetItem(shareId);
    if (!pSharedLink)
        return nullptr;

    KSharedLinkType sharedLinkType = pSharedLink->Type();
    if (sharedLinkType == SharedLinkType_DbView)
    {
        ks_stdptr<IDBSharedLinkView> spSharedLinkView = pSharedLink;
        if (!spSharedLinkView)
            return nullptr;

        IDBSheetView* pView = spSharedLinkView->GetView();
        if (!pView)
            return nullptr;
        UINT appSheetId = spSharedLinkView->GetAppSheetStId();
        return std::make_unique<ShareViewContentVisibilityChecker>(pBook, pView, appSheetId);
    }
    else if (sharedLinkType == SharedLinkType_Sheet)
    {
        ks_stdptr<ISharedLinkSheet> spSharedLinkSheet = pSharedLink;
        if (!spSharedLinkSheet)
            return nullptr;

        ISheet* pSheet = spSharedLinkSheet->GetSheet();
        if (!pSheet)
            return nullptr;

        if (pSheet->IsDbDashBoardSheet() && (pSheet->GetBMP()->bKsheet || pSheet->GetBMP()->bDbSheet))
        {
            ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
            VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
            if (!spDbDashboard)
                return nullptr;

            return std::make_unique<ShareDashBoardContentVisibilityChecker>(spDbDashboard, pSheet);
        }
        else if (pSheet->IsGridSheet())
        {
            return std::make_unique<ShareGridSheetContentVisibilityChecker>(pSheet->GetStId());
        }
    }
    return nullptr;
}

std::unique_ptr<ShareLinkContentVisibilityChecker> ShareLinkContentVisibilityChecker::CreateInstance(IBook* pBook, UINT sheetId)
{
    IDX sheetIdx = INVALIDIDX;
    pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
        return nullptr;

    // 暂时只支持导出仪表盘sheet数据
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(sheetIdx, &spSheet);
    if (!spSheet->IsDbDashBoardSheet())
        return nullptr;

    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(spSheet, &spDbDashboard));
    if (!spDbDashboard)
        return nullptr;

    return std::make_unique<ShareDashBoardContentVisibilityChecker>(spDbDashboard, spSheet);
}

ShareViewContentVisibilityChecker::ShareViewContentVisibilityChecker(IBook* pBook, IDBSheetView* pView, UINT appSheetId)
    : m_pBook(pBook), m_appSheetId(appSheetId)
{
    m_sheetId = pView->GetSheetOp()->GetSheetId();
    const IDBRecordsOrderManager*  pRecordsOrderManager = pView->GetConstOrderManager();
    for (int i = 0; i < pRecordsOrderManager->GetGroupConditionCount(); ++i)
    {
        EtDbId fieldId = pRecordsOrderManager->GetGroupCondition(i)->GetKeyFieldId();
        m_visibleContentFields.insert(fieldId);
    }
    ET_DBSheet_ViewType viewType = pView->GetType();
    if (viewType == et_DBSheetView_Gallery || viewType == et_DBSheetView_Kanban)
    {
        ks_stdptr<IDBSheetView_CardFeatures> spCardView = pView;
        if (spCardView)
        {
            EtDbId fieldId  = spCardView->GetCoverField();
            if (fieldId != INV_EtDbId)
                m_visibleContentFields.insert(fieldId);
        }
    }
    InitVisibleViews(pView);
}

bool ShareViewContentVisibilityChecker::IsSheetVisible(UINT sheetId) const
{
    return m_visibleViews.count(sheetId) || (IsApp() && sheetId == m_appSheetId);
}

bool ShareViewContentVisibilityChecker::IsSheetPropertyVisible(UINT sheetId) const
{
    return IsApp() && sheetId == m_appSheetId;
}

bool ShareViewContentVisibilityChecker::IsSheetViewVisible(UINT sheetId, EtDbId viewId) const
{
    auto it = m_visibleViews.find(sheetId);
    if (it == m_visibleViews.end())
        return false;

    return it->second.count(viewId);
}

bool ShareViewContentVisibilityChecker::IsFieldVisible(UINT sheetId, EtDbId fieldId) const
{
    if (m_sheetId == sheetId)
    {
        if (m_visibleContentFields.count(fieldId))
            return true;
    }
    auto fieldIdsIter = m_visibleFields.find(sheetId);
    if (fieldIdsIter == m_visibleFields.end())
    {
        auto viewsIter = m_visibleViews.find(sheetId);
        if (viewsIter != m_visibleViews.end())
        {
            std::vector<const IDBIds*> fieldIds = GetVisibleFields(sheetId, viewsIter->second);
            fieldIdsIter = m_visibleFields.insert({sheetId, fieldIds}).first;
        }
    }
    if (fieldIdsIter != m_visibleFields.end())
    {
        const std::vector<const IDBIds*>& fieldIds = fieldIdsIter->second;
        auto isIdExists = [fieldId](const IDBIds* pIds) { return pIds->Id2Idx(fieldId) != INV_EtDbIdx; };
        return std::any_of(fieldIds.cbegin(), fieldIds.cend(), isIdExists);
    }
    return false;
}

bool ShareViewContentVisibilityChecker::IsRecordVisible(UINT sheetId, EtDbId recordId) const
{
    auto recordIdsIter = m_visibleRecords.find(sheetId);
    if (recordIdsIter == m_visibleRecords.end())
    {
        auto viewsIter = m_visibleViews.find(sheetId);
        if (viewsIter != m_visibleViews.end())
        {
            std::vector<const IDBIds*> recordIds = GetVisibleRecords(sheetId, viewsIter->second);
            recordIdsIter = m_visibleRecords.insert({sheetId, recordIds}).first;
        }
    }
    if (recordIdsIter != m_visibleRecords.end())
    {
        const std::vector<const IDBIds*>& recordIds = recordIdsIter->second;
        auto isIdExists = [recordId](const IDBIds* pIds) { return pIds->Id2Idx(recordId) != INV_EtDbIdx; };
        return std::any_of(recordIds.cbegin(), recordIds.cend(), isIdExists);
    }
    return false;
}

void ShareViewContentVisibilityChecker::InitVisibleViews(const IDBSheetView* pView)
{
    const IDBIds* pFields = pView->GetVisibleFields();
    IDbFieldsManager* pFieldsManager = pView->GetFieldsManager();
    for (EtDbIdx i = 0, fieldCount = pFields->Count(); i < fieldCount; ++i)
    {
        EtDbId fieldId = pFields->IdAt(i);
        ks_stdptr<IDbField> spField;
        HRESULT hr = pFieldsManager->GetField(fieldId, &spField);
        if (FAILED(hr) ||
            (spField->GetType() != Et_DbSheetField_Link && spField->GetType() != Et_DbSheetField_OneWayLink))
            continue;

        ks_stdptr<IDbField_Link> spFieldLink = spField;
        UINT linkSheetId = spFieldLink->GetLinkSheet();
        IDX linkSheetIdx = INVALIDIDX;
        m_pBook->STSheetToRTSheet(linkSheetId, &linkSheetIdx);
        if (linkSheetIdx != INVALIDIDX)
        {
            EtDbId linkViewId = spFieldLink->GetLinkView();
            InsertVisibleView(linkSheetId, linkViewId);
        }
    }
    InsertVisibleView(pView->GetSheetOp()->GetSheetId(), pView->GetId());
}

void ShareViewContentVisibilityChecker::InsertVisibleView(UINT sheetId, EtDbId viewId)
{
    if (!m_visibleViews.count(sheetId))
        m_visibleViews.insert({sheetId, {viewId}});
    else
        m_visibleViews[sheetId].insert(viewId);
}

std::vector<const IDBIds*> ShareViewContentVisibilityChecker::GetVisibleFields(UINT sheetId, const std::unordered_set<EtDbId>& viewIds) const
{
    std::vector<const IDBIds*> fieldIds;
    IDX sheetIdx = INVALIDIDX;
    m_pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
		return fieldIds;
    ks_stdptr<ISheet> spSheet;
    m_pBook->GetSheet(sheetIdx, &spSheet);
    if (!spSheet)
        return fieldIds;

    // INV_EtDbId代表全部数据
    if (viewIds.count(INV_EtDbId))
    {
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        VS(DbSheet::GetDBSheetOp(spSheet, &spDbSheetOp));
        fieldIds.push_back(spDbSheetOp->GetAllFields());
    }
    else
    {
        ks_stdptr<IDBSheetViews> spDbSheetViews;
        VS(DbSheet::GetDBSheetViews(spSheet, &spDbSheetViews));
        for (auto viewId: viewIds)
        {
            ks_stdptr<IDBSheetView> spDbSheetView;
            spDbSheetViews->GetItemById(viewId, &spDbSheetView);
            if (spDbSheetView)
                fieldIds.push_back(spDbSheetView->GetVisibleFields());
        }
    }
    return fieldIds;
}

std::vector<const IDBIds*> ShareViewContentVisibilityChecker::GetVisibleRecords(UINT sheetId, const std::unordered_set<EtDbId>& viewIds) const
{
    std::vector<const IDBIds*> recordIds;
    IDX sheetIdx = INVALIDIDX;
    m_pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    if (sheetIdx == INVALIDIDX)
		return recordIds;
    ks_stdptr<ISheet> spSheet;
    m_pBook->GetSheet(sheetIdx, &spSheet);
    if (!spSheet)
        return recordIds;

    // INV_EtDbId代表全部数据
    if (viewIds.count(INV_EtDbId))
    {
        ks_stdptr<IDBSheetOp> spDbSheetOp;
        VS(DbSheet::GetDBSheetOp(spSheet, &spDbSheetOp));
        recordIds.push_back(spDbSheetOp->GetAllRecords());
    }
    else
    {
        ks_stdptr<IDBSheetViews> spDbSheetViews;
        VS(DbSheet::GetDBSheetViews(spSheet, &spDbSheetViews));
        for (auto viewId: viewIds)
        {
            ks_stdptr<IDBSheetView> spDbSheetView;
            spDbSheetViews->GetItemById(viewId, &spDbSheetView);
            if (spDbSheetView)
            {
                if (spDbSheetView->GetType() == et_DBSheetView_Query)
                {
                    recordIds.push_back(spDbSheetView->GetAllRecords());
                }
                else
                {
                    recordIds.push_back(spDbSheetView->GetVisibleRecords());
                }
            }
        }
    }
    return recordIds;
}

ShareDashBoardContentVisibilityChecker::ShareDashBoardContentVisibilityChecker(IDBDashBoardDataOp* pDbDashboard, ISheet* pSheet)
{
    m_sheetId = pSheet->GetStId();
    InitVisibleFields(pDbDashboard);
    InitVisibleViews(pSheet);
}


bool ShareDashBoardContentVisibilityChecker::IsSheetVisible(UINT sheetId) const
{
    return m_visibleFields.count(sheetId) || m_visibleViews.count(sheetId) || m_sheetId == sheetId;
}

bool ShareDashBoardContentVisibilityChecker::IsSheetPropertyVisible(UINT sheetId) const
{
    return m_sheetId == sheetId;
}

bool ShareDashBoardContentVisibilityChecker::IsSheetViewVisible(UINT sheetId, EtDbId viewId) const
{
    auto it = m_visibleViews.find(sheetId);
    if (it == m_visibleViews.end())
        return false;

    return it->second.count(viewId);
}

bool ShareDashBoardContentVisibilityChecker::IsFieldVisible(UINT sheetId, EtDbId fieldId) const
{
    if (m_visibleViews.count(sheetId))
        return true;

    auto it = m_visibleFields.find(sheetId);
    if (it == m_visibleFields.end())
        return false;

    return it->second.count(fieldId);
}

bool ShareDashBoardContentVisibilityChecker::IsRecordVisible(UINT sheetId, EtDbId recordId) const
{
    return m_visibleFields.count(sheetId) || m_visibleViews.count(sheetId);
}

void ShareDashBoardContentVisibilityChecker::InitVisibleFields(IDBDashBoardDataOp* pDbDashboard)
{
    IDBChartStatisticMgr* pChartStatisticMgr = pDbDashboard->GetChartStatisticMgr();
    CollectVisibleFields(pChartStatisticMgr);
    IDbDashboardFilterMgr* pFilterMgr = pDbDashboard->GetFilterMgr();
    CollectVisibleFields(pFilterMgr);
}

void ShareDashBoardContentVisibilityChecker::InitVisibleViews(ISheet* pSheet)
{
    IBook* pBook = pSheet->LeakBook();
    ks_stdptr<IKWebExtensionMgr> spWebExtensionMgr;
	VS(pBook->GetExtDataItem(edBookWebExtensionMgr, (IUnknown**)&spWebExtensionMgr));
    UINT webextensionCount = 0;
	spWebExtensionMgr->GetWebExtensionCount(pSheet, &webextensionCount);
	for (int i = 0; i < webextensionCount; ++i)
	{
        ks_stdptr<IKWebExtension> spWebExtension;
		HRESULT hr = spWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
		if (FAILED(hr))
            continue;

		if (spWebExtension->GetWebShapeType() != WET_DbView)
			continue;
        ks_stdptr<IEtWebExtension_View> spWebExtView = spWebExtension;
        UINT srcSheetId = spWebExtView->GetSheetId();
        if (srcSheetId == 0)
            continue;

        if (!m_visibleViews.count(srcSheetId))
            m_visibleViews.insert({srcSheetId, {spWebExtView->GetViewId()}});
        else
            m_visibleViews[srcSheetId].insert(spWebExtView->GetViewId());
    }
}

void ShareDashBoardContentVisibilityChecker::CollectVisibleFields(IDBChartStatisticMgr* pChartStatisticMgr)
{
    for (EtDbIdx i = 0, count = pChartStatisticMgr->GetSize(); i < count; ++i)
    {
        ks_stdptr<IDBChartStatisticModule> spModule;
        pChartStatisticMgr->GetItemAt(i, &spModule);
        if (!spModule)
            continue;

        UINT sheetId = spModule->GetDataSourceId();
        if (sheetId == 0)
            continue;

        EtDbId dimensionId = spModule->GetDimensionId();
        if (dimensionId != INV_EtDbId)
            InsertVisibleField(sheetId, dimensionId);

        EtDbId groupConditionId = spModule->GetGroupConditionId();
        if (groupConditionId != INV_EtDbId)
            InsertVisibleField(sheetId, groupConditionId);
    }
}

void ShareDashBoardContentVisibilityChecker::CollectVisibleFields(IDbDashboardFilterMgr* pFilterMgr)
{
    class KDbDashboardFilterFieldEnum : public IDbDashboardFilterFieldEnum
    {
    public:
        explicit KDbDashboardFilterFieldEnum(ShareDashBoardContentVisibilityChecker* pChecker) : m_pChecker(pChecker) {}
        STDPROC Do(UINT sheetId, EtDbId fieldId) override
        {
            if (sheetId > 0 && fieldId != INV_EtDbId)
                m_pChecker->InsertVisibleField(sheetId, fieldId);
            return S_OK;
        }
    private:
        ShareDashBoardContentVisibilityChecker* m_pChecker = nullptr;
    };
    EtDbIdx count = pFilterMgr->GetCount();
    if (count > 0)
    {
        KDbDashboardFilterFieldEnum filterFieldEnum(this);
        for (EtDbIdx i = 0; i < count; ++i)
        {
            ks_stdptr<IDbDashboardFilter> spFilter;
            pFilterMgr->GetFilterAt(i, &spFilter);
            if (spFilter)
                VS(spFilter->EnumFilterField(&filterFieldEnum));
        }
    }
}

void ShareDashBoardContentVisibilityChecker::InsertVisibleField(UINT sheetId, EtDbId fieldId)
{
    if (!m_visibleFields.count(sheetId))
        m_visibleFields.insert({sheetId, {fieldId}});
    else
        m_visibleFields[sheetId].insert(fieldId);
}

ShareGridSheetContentVisibilityChecker::ShareGridSheetContentVisibilityChecker(UINT sheetId)
        : m_sheetId(sheetId)
{
}


bool ShareGridSheetContentVisibilityChecker::IsSheetVisible(UINT sheetId) const
{
    return m_sheetId == sheetId;
}

bool ShareGridSheetContentVisibilityChecker::IsSheetPropertyVisible(UINT sheetId) const
{
    return m_sheetId == sheetId;
}

bool ShareGridSheetContentVisibilityChecker::IsSheetViewVisible(UINT sheetId, EtDbId viewId) const
{
    return false;
}

bool ShareGridSheetContentVisibilityChecker::IsFieldVisible(UINT sheetId, EtDbId fieldId) const
{
    return false;
}

bool ShareGridSheetContentVisibilityChecker::IsRecordVisible(UINT sheetId, EtDbId recordId) const
{
    return false;
}

}// wo