﻿#ifndef __WEBET_DATA_ANALYZE_H__
#define __WEBET_DATA_ANALYZE_H__

#include "dataanalyze/et_dataanalyze.h"
#include "dataanalyze/et_dataanalyze_errorinfo.h"
#include "dataanalyze/etdacoreexport.h"
#include "Coding/core_bundle/include/webbase/serialize_impl.h"
#include "et_revision_context_impl.h"
class IEtDataAnalyzeData;
namespace wo
{
    struct MergeTaskParam
    {
        etda::MergeInfo* pInfo = nullptr;
        IKWorksheet* pWorksheet = nullptr;
        bool isNewTask = true;
        bool isNewFile = false;
        bool isAutoSync = false;
        bool isPreview = false;
        bool isAutoTrigger = false;
        int taskId = 0;
        int sheetStId = 0;
        int targetSheetStId = 0;
        ks_wstring targetFileId;
        ks_wstring connId;
        ks_wstring userId;
        ks_wstring token;
        ks_wstring cronDesc;
        ks_wstring asyncTaskId;
        ks_wstring sheetName;
        ks_wstring colNamePreFix;
        ks_wstring tableText;
        ks_wstring resultFilePath;
        std::unordered_map<int, QString> sourceIdMap;
        IEtDataAnalyzeData* pData = nullptr;
    };

    struct TableInfo
    {
        QString tableName;
        QStringList mergeCols;
    };
    struct SqlGeneratorParam
    {
        etda::MergeType type;
        etda::JoinMode joinMode;
        std::vector<TableInfo> tableInfo;
        QStringList finalColNames;
        bool bTagSource = false;
        bool bDeduplication = false;
    };

    class KDASqlGenerator
    {
    public:
        KDASqlGenerator(bool bPreview);
        ~KDASqlGenerator() = default;
        QString generateeDeduplicationSQL(const SqlGeneratorParam& param);
        QString generateJoinSql(const SqlGeneratorParam& param);
        QString generateUnionSQL(const SqlGeneratorParam& param);
        QString generateMergeSQL(const SqlGeneratorParam& param);
        QString generateCountSQL(const QString& tableName);
        QString generateIndexSQL(const SqlGeneratorParam& param);
        void setTableColName(const QString& tableName, const QString& colName);
        QStringList getTableUsedColNames(const QString& tableName);
        void replaceColNames(const QString& tableName, const QStringList& colNames);

        static QString createTableName(const QString& fileId, int sheetId, int source);
        QString createSubUnionSQL(const TableInfo& table, const QStringList& udfColNames, bool bTagSource);
    private:
        std::map<QString, QStringList> m_tablseColNameMap; //存储合并中使用的列名
        std::map<QString, QStringList> m_tableColNamesSourceMap; //存储源表的列名
        bool m_bPreview = false;
    };
    
    class FileProcessor 
    {
    public:
        static HRESULT processFileList(const binary_wo::VarObj& fileList, std::unordered_map<int, QString>& sourceIdMap, etda::MergeInfo* pInfo, class ExecDAOutput* output);
    };

    void returnMergeTask(const MergeTaskParam&  param, binary_wo::BinWriter& binWriter, etda::ErrorCode& errorCode, PCWSTR errorMsg
    , etda::WarningCode& warningCode, PCWSTR warningMsg);

    class DATaskExecutor 
    {
    public:
        static bool importDataSource(const MergeTaskParam& param, KDASqlGenerator& gen, SqlGeneratorParam& genParam, etda::ErrorCode& errorCode, PCWSTR errorMsg, std::vector<etda::ImportFileLog>& sourcesLog, bool& bFirstTable, int& rightTableBeginCol, std::function<void()> endMergeTask);
        static bool execExport(const QString& sql, const MergeTaskParam& param, binary_wo::BinWriter& binWriter
        , etda::ErrorCode& errorCode, PCWSTR errorMsg, std::vector<etda::ImportFileLog>& sourcesLog
        , std::function<void()> endMergeTask, etda::WarningCode& warningCode, PCWSTR warningMsg);
        static void execPreview(const QString& sql, const int rightTableBeginCol, binary_wo::BinWriter& binWriter);
        static void afterExportTask(const MergeTaskParam& param, int exportRes, const std::vector<etda::ImportFileLog>& sourcesLog, binary_wo::BinWriter& binWriter);
        static void execMergeTask(MergeTaskParam param);
        static void removeFile(const MergeTaskParam& param);
    };
    class TaskParamProcessor 
    {
    public:
        static QStringList getFinalList(const QStringList& colList, etda::MergeInfo* pInfo, etda::DataSourceConfig& it, int preColsCount);
        static HRESULT checkTaskParam(const binary_wo::VarObj& taskParam, MergeTaskParam& mergeTaskParam);
        static void ParseExecResultConfig(etda::MergeInfo* pInfo, binary_wo::VarObj& execResultConfig);
        static etda::DataSourceConfig ParseDataSourceConfig(binary_wo::VarObj& dataSourceConfigItem, KSerialWrapBinWriter& acpt);
        static void filterEmptyCol(const QStringList& colList, std::vector<QString>& mergeCols);
    };

class EtTaskExecBase;
class DataAnalyseImportHelper
{
public:
    DataAnalyseImportHelper(wo::KEtWorkbook* wwb, int targetSheetStId);
    ~DataAnalyseImportHelper();
    HRESULT operator() (EtTaskExecBase* pTask, KwCommand* cmd, KEtRevisionContext* pCtx, const ks_wstring& sheetName, const QString& filePath);
    ISheet* getTargetSheet();

private:
    HRESULT prepareTargetSheet(OUT ISheet** ppISheet);

private:
    bool m_bNewSheet = false;
    int m_targetSheetStId = -1;
    wo::KEtWorkbook* m_wwb = nullptr;
    ks_stdptr<ISheet> m_targetSheet;
};

class KEtWorkbooks;
WebInt ExecDataAnalyzeInl(KEtWorkbooks* wbs, const UserContext* userContext, const WebSlice* commands, WebSlice* pOutput);
}

#endif
