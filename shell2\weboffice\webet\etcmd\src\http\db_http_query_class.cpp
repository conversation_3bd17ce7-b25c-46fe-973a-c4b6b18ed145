﻿#include "etstdafx.h"
#include "db_http_query_class.h"

#include "http_error.h"
#include "db_value_serialiser.h"
#include "et_hard_define_strings.h"
#include "dbsheet/et_dbsheet_filter_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/varobject_helper.h"
#include "src/et_revision_context_impl.h"
#include "src/workbook.h"
#include "webbase/serialize_impl.h"
#include "webbase/wo_sa_helper.h"
#include "ettools/ettools_encode_decoder.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"
#include "appcore/et_appcore_shared_link_sheet.h"
#include "dbsheet/dashboard/db_dashboard_module_mgr_wrapper.h"
#include "db/db_basic_itf.h"

namespace wo
{


DbHttpQueryClassBase::DbHttpQueryClassBase(wo::KEtWorkbook *wwb, PCWSTR tag)
	: EtQueryExecBase(wwb, tag)
	, m_pDbCtx(_appcore_GainDbSheetContext())
	, m_pEncodeDecoder(_appcore_GainEncodeDecoder())
	, m_commonHelper(wwb)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<IUnknown> spUnknown;
	VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	spDBUserGroups->GetJudgement(&m_spProtectionJudgement);
}

HRESULT DbHttpQueryClassBase::PreExecute(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
	// ServerApi调用，需要在身份一致（同一个UserId）下，借conn调用，解决动态筛选临时态的一些问题
	// 解析connFrom，校验合法性（同一个UserId认为合法）
	// 非必要，不传connFrom参数
	if (param.has("connFrom") && (param.get_s("connFrom").type() == binary_wo::typeString))
	{
		// 获取connFrom的IKUserConn
		PCWSTR connFrom = param.field_str("connFrom");
		if (connFrom)
		{
			IKUserConn* pConnFrom = ctx->getUsers()->getUserConn(QString::fromUtf16(connFrom).toUtf8());

			// 获取当前http调用的IKUserConn
			IKUserConn* pCurConn = ctx->getUser();

			if (pConnFrom && 0 == xstrcmp(pConnFrom->userID(), pCurConn->userID()))
			{
				ctx->setUser(pConnFrom);
			}
		}
	}

	return EtQueryExecBase::PreExecute(param, ctx);
}

HRESULT DbHttpQueryClassBase::PostExecute(HRESULT hr, const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
	pCtx->postExecute(hr);

	if (FAILED(hr))
	{
		binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
		KSerialWrapBinWriter acpt(*pResponse, pCtx);
		AddHttpError(hr, &acpt, m_errMsg.empty() ? nullptr : m_errMsg.c_str());
		m_errMsg.clear();
	}
	return EtQueryExecBase::PostExecute(hr, param, pCtx);
}

HRESULT DbHttpQueryClassBase::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
	if (!m_spProtectionJudgement->HasHiddenData())
		return S_OK;
	// todo: 待细化
	return m_spProtectionJudgement->IsNoPermissions() ? E_DBSHEET_AD_FAIL : S_OK;
}

static void WriteFilterErrMsg(ks_wstring& errMsg, const IDbExtraFilter* pFailedFilter)
{
    ASSERT(pFailedFilter);
    UINT depth = pFailedFilter->GetDepth();
    if (0 == depth)
    {
        errMsg.append(__X("Invalid filter detected at root filter"));
    }
    else
    {
        std::vector<UINT> indexes;
        indexes.resize(depth);
        pFailedFilter->GetCurFilterIndexesInfo(indexes.data(), depth);
        errMsg.append(__X("Invalid filter detected at indexes: "));
        for (UINT index : indexes)
        {
            errMsg.append(krt::utf16(QString::number(index)));
            errMsg.push_back(__Xc(','));
        }
        errMsg.pop_back();
    }
}

HRESULT DbHttpQueryClassBase::listRecordsExec(UINT sheetStId, const binary_wo::VarObj& param, KEtRevisionContext* ctx, DBSheetCommonHelper& commonHelper,
												wo::KEtWorkbook* wwb, IDBSheetCtx* pDbCtx, ks_wstring& errMsg, bool byOffset, const IDBIds *pRecords)
{
	if (not byOffset)
		VAR_OBJ_EXPECT_NUMERIC(param, "pageNum");

    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if(FAILED(hr))
        return hr;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId")
		bPreferId = param.field_bool("preferId");
	}
	bool bShowFieldsInfo = false;
	if (param.has("showFieldsInfo"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "showFieldsInfo")
		bShowFieldsInfo = param.field_bool("showFieldsInfo");
	}
	bool bShowPrimary = false;
	if (param.has("showPrimary"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "showPrimary")
		bShowPrimary = param.field_bool("showPrimary");
	}

	// todo: 分享视图下关联字段的权限检查
	// 2022.08.23及之后, 前端不传的参数服务端可能会带上默认值. 内核要考虑到这种情况
	ks_stdptr<IDBSheetView> spDbSheetView;
	if (wo::varObjHasStringMember(param, "viewId", __X(""))) // todo: view参数使用bPreferId改造, 支持解析名称
	{
		HRESULT hr = commonHelper.GetDBSheetView(sheetStId, commonHelper.GetEtDbId_NoExcept(param, "viewId"), &spDbSheetView, nullptr);
		if (FAILED(hr))
			return hr;
		pRecords = spDbSheetView->GetVisibleRecords();
	}
	if (nullptr == pRecords)
		pRecords = spDbSheetOp->GetAllRecords();

	std::vector<EtDbId> visibleIds;
	visibleIds.reserve(pRecords->Count());
	for (EtDbIdx i = 0, c = pRecords->Count(); i < c; ++i)
		visibleIds.push_back(pRecords->IdAt(i));

	// 需要将这份可视记录再基于 API 指定的条件(如果有的话)执行一次排序/筛选
	if (param.has("filter"))
	{
		VarObj filter = param.get_s("filter");
		if (binary_wo::typeStruct != filter.type())
			return E_DBSHEET_FILTER_INVALID;

		ks_stdptr<IDbExtraFilter> spExtraFilter;
		VS(_appcore_CreateObject(CLSID_KDbExtraFilter, IID_IDbExtraFilter, (void**)& spExtraFilter));
		spExtraFilter->Init(spDbSheetOp.get());
		const IDbExtraFilter* pFailedFilter = nullptr;
		hr = DbSheet::filtrateByExtraFilter(spExtraFilter, filter, commonHelper, spDbSheetOp.get(), bPreferId, &pFailedFilter);
		if (FAILED(hr))
		{
            WriteFilterErrMsg(errMsg, pFailedFilter);
			return hr;
		}

		IKUserConn* user = ctx->getUser();
		// 双指针处理可视记录
		size_t newSize = visibleIds.size();
		hr = spExtraFilter->FilterVisibleRecords(visibleIds.data(), newSize, user);
		if (FAILED(hr))
			return hr;
		visibleIds.resize(newSize);
	}
	std::unordered_map<EtDbId, EtDbIdx> visibleRecordsMap;
	visibleRecordsMap.reserve(visibleIds.size());
	for (int i = 0, c = visibleIds.size(); i < c; ++i)
		visibleRecordsMap[visibleIds[i]] = static_cast<EtDbIdx>(i);

	// 获取第一条记录的idx
	EtDbIdx start = 0;
	if (byOffset && param.has("offset"))
	{
		VAR_OBJ_EXPECT_STRING(param, "offset")
		WebStr offset = param.field_str("offset");
		if (xstrcmp(offset, __X("")) != 0)
		{
			EtDbId recordId = commonHelper.GetEtDbId_NoExcept(param, "offset");

			auto itr = visibleRecordsMap.find(recordId);
			if (visibleRecordsMap.end() == itr)
				return E_DBSHEET_RECORD_NOT_FOUND;
			start = itr->second;
		}
	}
	// 分页
	int pageSize = 100;
	if (wo::varObjHasIntergerMember(param, "pageSize", 0))
	{
		constexpr int pageMaxSize = 1000;
		VAR_OBJ_EXPECT_NUMERIC(param, "pageSize")
		const int v = param.field_int32("pageSize");
		if (v < 0)
			return E_INVALID_REQUEST;
		else if (v == 0)
			;
		else
			pageSize = std::min(pageMaxSize, v);
	}
	if (not byOffset)
	{
		const int v = param.field_int32("pageNum");
		if (v < 0)
			return E_INVALID_REQUEST;
		else if (v == 0)
			;
		else
			start = (v - 1) * pageSize;
	}
	// 获取从0开始的前maxRecords条记录. 
	UINT maxRecords = visibleRecordsMap.size();
	if (wo::varObjHasIntergerMember(param, "maxRecords", 0))
	{
		VAR_OBJ_EXPECT_NUMERIC(param, "maxRecords")
		const int v = param.field_int32("maxRecords");
		if (v < 0)
			return E_INVALID_REQUEST;
		else if (v == 0)
			;
		else
			maxRecords = std::min(maxRecords, static_cast<UINT>(v));
	}

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<_Worksheet> spWorksheet;
	hr = commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	DbSheetValueSerialiser dbSerialiser(wwb->GetCoreWorkbook(), spWorksheet, &acpt, param, &errMsg);
	dbSerialiser.CustomDbSheetView(spDbSheetView.get());

	if (bShowPrimary)
	{
		IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();
		EtDbId primaryFieldId = pFieldsMgr->GetPrimaryField();
		ks_stdptr<IDbField> spPrimaryField;
		hr = pFieldsMgr->GetField(primaryFieldId, &spPrimaryField);
		if (FAILED(hr))
			return hr;
		PCWSTR typeStr = nullptr;
		VS(_appcore_GainEncodeDecoder()->EncodeFieldType(spPrimaryField->GetType(), &typeStr));
		ASSERT(typeStr);
		acpt.addString("primaryFieldType", typeStr);
		
		if (bPreferId)
		{
			EtDbIdStr buf;
			VS(pDbCtx->EncodeEtDbId(spPrimaryField->GetID(), &buf));
			acpt.addString("primaryFieldId", buf);
		}
		else
		{
			acpt.addString("primaryFieldName", spPrimaryField->GetName());
		}
	}

	{
		std::unordered_set<EtDbId>  noPermissionRecord = dbSerialiser.GetNoPermissionRecord();
		wo::sa::Leave recArr(wo::sa::enterArray(&acpt, "records"));
		for (int i = 0; i < pageSize; ++i)
		{
			EtDbIdx rec = start + i;
			if (rec >= maxRecords)
				break;

			EtDbId recordId = visibleIds.at(rec);
			ASSERT(recordId != INV_EtDbId);

			if (noPermissionRecord.find(recordId) == noPermissionRecord.end())
			{
				acpt.beginStruct();
				VS(dbSerialiser.SerialiseRecord(recordId));
				acpt.endStruct();
			}
		}
	}

	if (bShowFieldsInfo)
		VS(dbSerialiser.SerialiseFields("fieldsSchema"));

	if (start + pageSize < maxRecords && byOffset) // idx 最多2^20，因此无需考虑溢出
	{
		IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
		EtDbId recordId = visibleIds.at(start + pageSize);

		EtDbIdStr buf;
		dbctx->EncodeEtDbId(recordId, &buf);
		acpt.addString("offset", buf);
	}
	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpInquireParentRecordTaskClass::DbHttpInquireParentRecordTaskClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.listParentChildren"))
{
}

HRESULT DbHttpInquireParentRecordTaskClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");
    VAR_OBJ_EXPECT_STRING(param, "parentId")
    EtDbId parentRecId = DbSheet::GetEtDbId(param, "parentId");

	//与查看权限保持一致
    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if (FAILED(hr))
        return hr;
	
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

	std::unordered_set<EtDbId> result;
	spDbSheetOp->GetRecordsManager()->GetRecordAllChildren(parentRecId, result);
	std::vector<EtDbId> childRecords(result.begin(), result.end());
	std::sort(childRecords.begin(), childRecords.end());

	std::unordered_map<EtDbId, EtDbIdx> visibleRecordsMap;
	visibleRecordsMap.reserve(childRecords.size());
	for (int i = 0, c = childRecords.size(); i < c; ++i)
		visibleRecordsMap[childRecords[i]] = static_cast<EtDbIdx>(i);

	// 获取第一条记录的idx
	EtDbIdx start = 0;
	if (param.has("offset"))
	{
		VAR_OBJ_EXPECT_STRING(param, "offset")
		WebStr offset = param.field_str("offset");
		if (xstrcmp(offset, __X("")) != 0)
		{
			EtDbId recordId = m_commonHelper.GetEtDbId_NoExcept(param, "offset");

			auto itr = visibleRecordsMap.find(recordId);
			if (visibleRecordsMap.end() == itr)
				return E_DBSHEET_RECORD_NOT_FOUND;
			start = itr->second;
		}
	}

	// 分页
	int pageSize = 100;
	if (wo::varObjHasIntergerMember(param, "pageSize", 0))
	{
		constexpr int pageMaxSize = 1000;
		VAR_OBJ_EXPECT_NUMERIC(param, "pageSize")
		const int v = param.field_int32("pageSize");
		if (v < 0)
			return E_INVALID_REQUEST;
		else if (v == 0)
			;
		else
			pageSize = std::min(pageMaxSize, v);
	}

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	UINT maxRecords = visibleRecordsMap.size();
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	{
		wo::sa::Leave recArr(wo::sa::enterArray(&acpt, "children"));
		for (int i = 0; i < pageSize; ++i)
		{
			EtDbIdx rec = start + i;
			if (rec >= maxRecords)
				break;

			EtDbId recordId = childRecords.at(rec);
			ASSERT(recordId != INV_EtDbId);

			EtDbIdStr buf;
			dbctx->EncodeEtDbId(recordId, &buf);
			acpt.beginStruct();
			acpt.addString("id", buf);
			std::unordered_set<EtDbId> child;
			spDbSheetOp->GetRecordsManager()->GetRecordAllChildren(recordId, child);
			int count = child.size();
			acpt.addInt32("child_count", count);
			acpt.endStruct();
		}
	}

	if (start + pageSize < maxRecords) // idx 最多2^20，因此无需考虑溢出
	{
		EtDbId recordId = childRecords.at(start + pageSize);

		EtDbIdStr buf;
		dbctx->EncodeEtDbId(recordId, &buf);
		acpt.addString("offset", buf);
	}
	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpIsEnableParentRecordTaskClass::DbHttpIsEnableParentRecordTaskClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.getParentRecordStatus"))
{
}

HRESULT DbHttpIsEnableParentRecordTaskClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

	ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;
	
	bool isEnable = spDbSheetOp->GetRecordsManager()->GetEnableRecordsRelation();
	
	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	acpt.addBool("enable", isEnable);

	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpListRecordsQueryClass::DbHttpListRecordsQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.listRecords"))
{
}

HRESULT DbHttpListRecordsQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");
	return listRecordsExec(sheetStId, param, ctx, m_commonHelper, m_wwb, m_pDbCtx, m_errMsg);
}

//////////////////////////////////////////////////////////////////////////
DbHttpListRecordsByPageQueryClass::DbHttpListRecordsByPageQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.listRecordsByPage"))
{
}

HRESULT DbHttpListRecordsByPageQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");
	return listRecordsExec(sheetStId, param, ctx, m_commonHelper, m_wwb, m_pDbCtx, m_errMsg, false);
}

//////////////////////////////////////////////////////////////////////////
DbHttpRetrieveRecordQueryClass::DbHttpRetrieveRecordQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.retrieveRecord"))
{
}

HRESULT DbHttpRetrieveRecordQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if(FAILED(hr))
        return hr;

	VAR_OBJ_EXPECT_STRING(param, "recordId")
	EtDbId recordId = m_commonHelper.GetEtDbId_NoExcept(param, "recordId");

	bool bShowFieldsInfo = false;
	if (param.has("showFieldsInfo"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "showFieldsInfo")
		bShowFieldsInfo = param.field_bool("showFieldsInfo");
	}

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;
	const IDBIds *pRecords = spDbSheetOp->GetAllRecords();
	if (pRecords->Id2Idx(recordId) == INV_EtDbIdx)
		return E_DBSHEET_RECORD_NOT_FOUND;

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, param, &m_errMsg);

	std::unordered_set<EtDbId>  noPermissionRecord = dbSerialiser.GetNoPermissionRecord();
	if (noPermissionRecord.find(recordId) == noPermissionRecord.end())
		VS(dbSerialiser.SerialiseRecord(recordId));

	if (bShowFieldsInfo)
		VS(dbSerialiser.SerialiseFields("fieldsSchema"));
	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpRetrieveRecordsQueryClass::DbHttpRetrieveRecordsQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.retrieveRecords"))
{
}

HRESULT DbHttpRetrieveRecordsQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if(FAILED(hr))
        return hr;

	VAR_OBJ_EXPECT_ARRAY(param, "records")
	std::vector<EtDbId> recordIds;
	hr = m_commonHelper.GetEtDbIds(param, "records", recordIds);
	if (FAILED(hr))
		return hr;

	bool bShowFieldsInfo = false;
	if (param.has("showFieldsInfo"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "showFieldsInfo")
		bShowFieldsInfo = param.field_bool("showFieldsInfo");
	}

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
	if (FAILED(hr))
		return hr;

	DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, param, &m_errMsg);

	VS(dbSerialiser.SerialiseRecords(recordIds));

	if (bShowFieldsInfo)
		VS(dbSerialiser.SerialiseFields("fieldsSchema"));
	return S_OK;
}


//////////////////////////////////////////////////////////////////////////
DbHttpPermissionQueryClass::DbHttpPermissionQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.getPermission"))
{
}


HRESULT DbHttpPermissionQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_STRING(param, "corePermissionId")
	WebStr permissionId = param.field_str("corePermissionId");

	IDBProtection* protection = m_spProtectionJudgement->GetDBProtection(permissionId);
	if (protection)
	{
        binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
        KSerialWrapBinWriter acpt(*pResponse, ctx);
		protection->SerialDBPermission(&acpt, TRUE);
		return S_OK;
    }

	return E_DBSHEET_PERMISSION_ID_NOT_EXIST;
}

HRESULT DbHttpPermissionQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
	return  S_OK;
}


//////////////////////////////////////////////////////////////////////////
DbHttpPermissionListSchemaQueryClass::DbHttpPermissionListSchemaQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.permissionListSchema"))
{
}


HRESULT DbHttpPermissionListSchemaQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{

    class DBProtectionEnum : public IDBProtectionEnum
    {
    public:
		DBProtectionEnum(ISerialAcceptor* acpt): m_acpt(acpt){}
        STDPROC_(BOOL) Do(IDBProtection* protection)
		{
			m_acpt->beginStruct();
			protection->SerialDBPermission(m_acpt, FALSE);
			m_acpt->endStruct();
			return TRUE;
		}
	private:
		ISerialAcceptor* m_acpt;
    };


    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
	acpt.addKey("permissionList");
	acpt.beginArray();
	DBProtectionEnum enumer(&acpt);
	m_spProtectionJudgement->EnumeDBProtection(&enumer);
	acpt.endArray();

	return S_OK;
}

HRESULT DbHttpPermissionListSchemaQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
	return  S_OK;
}


//////////////////////////////////////////////////////////////////////////
DbHttpAttachmentPermissionQueryClass::DbHttpAttachmentPermissionQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.attachmentPermission"))
{
}


HRESULT DbHttpAttachmentPermissionQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_STRING(param, "attachmentId")
	WebStr attachmentId = param.field_str("attachmentId");

	IKWorkbook *pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets *pWorksheets = pWorkbook->GetWorksheets();
	ks_wstring permission;
	bool find = false;
	for (INT32 cnt = pWorksheets->GetSheetCount(), i = 0; i < cnt && !find; ++i)
	{
		ks_stdptr<ISheet> spSheet = pWorksheets->GetSheetItem(i)->GetSheet();
		UINT sheetId = spSheet->GetStId();
		if (spSheet->IsFpSheet())
		{
			ks_stdptr<IFPSheetData> spFpSheetData;
			spSheet->GetExtDataItem(edSheetFpData, (IUnknown**)&spFpSheetData);
			if (!spFpSheetData)
				return E_FAIL;
			
			if (xstrcmp(spFpSheetData->GetContentId(), attachmentId) == 0)
			{
				if (SUCCEEDED(m_spProtectionJudgement->CheckSheetCanEdit(sheetId)))
					permission = __X("Permission_Edit");
				else if (SUCCEEDED(m_spProtectionJudgement->CheckSheetCanVisit(sheetId)))
					permission = __X("Permission_View");
				else 
				 	permission = __X("Permission_NoPermission");
				find = true;
				break;
			}
		}
		else if (spSheet->IsDbSheet())
		{
            ks_stdptr<IDBSheetOp> spOp;
            HRESULT hr = DbSheet::GetDBSheetOp(spSheet, &spOp);
            if (FAILED(hr))
                return E_FAIL;

            const IDBIds* pAllRecIds = spOp->GetAllRecords();
            const IDBIds* pAllFldIds = spOp->GetAllFields();
            IDbFieldsManager* pFieldsMgr = spOp->GetFieldsManager();
            EtDbIdx recCnt = pAllRecIds->Count();
            EtDbIdx fldCnt = pAllFldIds->Count();
            for (EtDbIdx fldIdx = 0; fldIdx < fldCnt && !find; ++fldIdx)
            {
                EtDbId fieldId = pAllFldIds->IdAt(fldIdx);
                ks_stdptr<IDbField> spField;
                pFieldsMgr->GetField(fieldId, &spField);
                if (spField->GetType() != Et_DbSheetField_Note)
                    continue;
                for (int recIdx = 0; recIdx < recCnt; ++recIdx)
                {
                    EtDbId recordId = pAllRecIds->IdAt(recIdx);
                    const_token_ptr pToken = nullptr;
                    hr = spOp->GetValueToken(recordId, fieldId, &pToken);
                    if (FAILED(hr))
                        continue;

					if (!alg::const_handle_token_assist::is_type(pToken))
						continue;
                    alg::const_handle_token_assist chta(pToken);
					alg::TOKEN_HANDLE handle = chta.get_handle();
                    if (handle && chta.get_handleType() ==  alg::ET_HANDLE_DBNOTE)
                    {
                    	ks_stdptr<IDbNoteHandle> spNoteToken = handle->CastUnknown();
						if (xstrcmp(spNoteToken->GetFileId(), attachmentId) == 0)
						{
							if (SUCCEEDED(m_spProtectionJudgement->CheckCellCanEdit(sheetId, recordId, fieldId)))
								permission = __X("Permission_Edit");
							else if (SUCCEEDED(m_spProtectionJudgement->CheckCellCanVisit(sheetId, recIdx, fldIdx)))
								permission = __X("Permission_View");
							else 
								permission = __X("Permission_NoPermission");
							find = true;
							break;
						}
					} 
                }
            }
        }
    }

	if (!find)
		return E_DBSHEET_INVALID_INPUT;  //附件id不匹配

    binary_wo::BinWriter* pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
	acpt.addString("permission", permission.c_str());

	return S_OK;
}

HRESULT DbHttpAttachmentPermissionQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
	return  S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpBaseSchemaQueryClass::DbHttpBaseSchemaQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.baseSchema"))
{
}

HRESULT DbHttpBaseSchemaQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	IKWorkbook *pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets *pWorksheets = pWorkbook->GetWorksheets();
	INT iSheetCnt = pWorksheets->GetSheetCount();
	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	bool showVeryhidden = true;
	if (param.has("showVeryhidden"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "showVeryhidden")
		showVeryhidden = param.field_bool("showVeryhidden");
	}
	int leftIdx = 0, rightIdx = iSheetCnt - 1;
	if (param.has("sheetId"))
	{
		VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
		UINT sheetStId = param.field_uint32("sheetId");
		IDX sheetIdx = INVALIDIDX;
		pWorkbook->GetBook()->STSheetToRTSheet(sheetStId, &sheetIdx);
		if (INVALIDIDX == sheetIdx)
			return E_DBSHEET_SHEET_NOT_FOUND;
		leftIdx = rightIdx = sheetIdx;
		showVeryhidden = true;
	}
	bool reserveNoPermissionSheet = false;
	if (param.has("reserveNoPermissionSheet"))
	{
		reserveNoPermissionSheet = param.field_bool("reserveNoPermissionSheet");
	}
	// 服务端会进行参数拦截，针对false默认丢弃
	bool bJustDbSheet = !(param.has("notJustDbSheet") ? param.field_bool("notJustDbSheet") : false);
	acpt.addKey("sheets");
	acpt.beginArray();
	for (int i = leftIdx; i <= rightIdx; ++i)
	{
		IKWorksheet *pWorksheet = pWorksheets->GetSheetItem(i);
		ISheet* pSheet = pWorksheet->GetSheet();
		if (bJustDbSheet && !pSheet->IsDbSheet())
			continue;
		if (!showVeryhidden)
		{
			SHEETSTATE sheetState = ssVisible;
			pSheet->GetVisible(&sheetState);
			if (sheetState == ssVeryhidden)
				continue;
		}
		UINT sheetStId = pSheet->GetStId();
		HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
		if(FAILED(hr))
		{
			if (reserveNoPermissionSheet)
			{
				acpt.beginStruct();
					acpt.addUint32("id", sheetStId);
					acpt.addBool("noPermission", true);
				acpt.endStruct();
			}
			continue;
		}

		acpt.beginStruct();
		if (bJustDbSheet) // 前端未做类型限制，这里兼容一下
		{
			DbSheetValueSerialiser dbSerialiser(pWorkbook, pWorksheet, &acpt, param, &m_errMsg);
			VS(dbSerialiser.SerialiseSheet());
		}
		else
		{
			SerialiseSheetInfo(pWorkbook, pWorksheet, &acpt, &m_errMsg, &param);
		}
		acpt.endStruct();
	}
	acpt.endArray();
	if (m_wwb->GetBMP()->bDbSheet)
		acpt.addString("bookType", __X("db"));
	else if (m_wwb->GetBMP()->bKsheet)
		acpt.addString("bookType", __X("as"));

	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpRetrieveStatisticsResultQueryClass::DbHttpRetrieveStatisticsResultQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.retrieveStatisticsResult"))
{
}

HRESULT DbHttpRetrieveStatisticsResultQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");

    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if(FAILED(hr))
        return hr;

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	hr = m_commonHelper.GetDBSheetViews(sheetStId, &spDbSheetViews);
	if (FAILED(hr))
		return hr;

	VAR_OBJ_EXPECT_STRING(param, "viewId")
	EtDbId viewId = INV_EtDbId;
	hr = m_pDbCtx->DecodeEtDbId(param.field_str("viewId"), &viewId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = spDbSheetViews->GetItemById(viewId, &spDbSheetView);
	if (FAILED(hr))
		return hr;

	bool bPreferId = false;
	if (param.has("preferId"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "preferId")
		bPreferId = param.field_bool("preferId");
	}

	const IDBRecordsOrderManager *pOrderManager = spDbSheetView->GetConstOrderManager();

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	acpt.addKey("groupsResult");
	ks_stdptr<IDbStatisticsResult> spResult;
	spDbSheetView->GetStatisticsResult(&spResult);
	SerialiseStatisticResult(-1, &acpt, spResult, spDbSheetView, bPreferId, spDbSheetView->GetFieldsManager());

	return S_OK;
}

void DbHttpRetrieveStatisticsResultQueryClass::SerialiseStatisticResult(int level,
	ISerialAcceptor *acpt,
	IDbStatisticsResult *pResult,
	IDBSheetView *pView,
	bool bPreferId,
	IDbFieldsManager *pFieldsManager)
{
	class KDbStatisticsResultEnum : public IDbStatisticsResultEnum
	{
	public:
		KDbStatisticsResultEnum(ISerialAcceptor *acpt, bool bPreferId, IDbFieldsManager *pFieldsManager)
			: m_acpt(acpt)
			, m_bPreferId(bPreferId)
			, m_pFieldsManager(pFieldsManager)
		{}

		STDPROC Do(EtDbId fieldId, DOUBLE result) override
		{
			if (m_bPreferId)
			{
				VS(_appcore_GainDbSheetContext()->EncodeEtDbId(fieldId, &m_buf));
				m_acpt->addKey(krt::fromUtf16(m_buf).toUtf8(), true);
			}
			else
			{
				ks_stdptr<IDbField> spField;
				VS(m_pFieldsManager->GetField(fieldId, &spField));
				m_acpt->addKey(krt::fromUtf16(spField->GetName()).toUtf8(), true);
			}
			m_acpt->addFloat64(nullptr, result);
			return S_OK;
		}
	private:
		EtDbIdStr m_buf;
		ISerialAcceptor *m_acpt;
		bool m_bPreferId;
		IDbFieldsManager *m_pFieldsManager;
	} sre(acpt, bPreferId, pFieldsManager);

	acpt->beginStruct();
		if (level >= 0)
		{
			const IDBRecordsOrderManager *pOrderManager = pView->GetConstOrderManager();
			ASSERT(level < pOrderManager->GetGroupConditionCount());
			const IDBRecordsOrderCondition *pGroupCond = pOrderManager->GetGroupCondition(level);
			EtDbId groupField = pGroupCond->GetKeyFieldId();
			EtDbId firstRecord = pResult->GetFirstRecord();
			ks_bstr groupName;
			HRESULT hr = pView->GetDisplayString(firstRecord, groupField, &groupName);
			if (SUCCEEDED(hr))
				acpt->addString("name", groupName);
		}

		acpt->addKey("result");
		acpt->beginStruct();
			VS(pResult->EnumResults(&sre));
		acpt->endStruct();

		UINT childrenCnt = pResult->GetChildrenCount();
		if (childrenCnt > 0)
		{
			acpt->addKey("children");
			acpt->beginArray();
			for (int i = 0; i < childrenCnt; i++)
			{
				ks_stdptr<IDbStatisticsResult> spChild;
				VS(pResult->GetChild(i, &spChild));
				SerialiseStatisticResult(level + 1, acpt, spChild, pView, bPreferId, pFieldsManager);
			}
			acpt->endArray();
		}
	acpt->endStruct();
}

//////////////////////////////////////////////////////////////////////////
DbHttpDevRetrieveFormQueryClass::DbHttpDevRetrieveFormQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.devRetrieveForm"))
{
	m_cmdPermissionCheckWhiteList.insert(GetTag());
}

// todo: 基于MajorVer或更多信息, 对需要序列化的属性乃至整个JSON包进行缓存, 以提升性能
HRESULT DbHttpDevRetrieveFormQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");
	VAR_OBJ_EXPECT_STRING(param, "viewId")
	EtDbId viewId = m_commonHelper.GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spView;
	HRESULT hr = m_commonHelper.GetDBSheetView(sheetStId, viewId, &spView, nullptr);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDBSheetView_Form> spFormView = spView;
	if (nullptr == spFormView)
		return E_INVALID_REQUEST;

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	acpt.addUint32("version", spFormView->GetMajorVer());
	acpt.addUint32("sheetId", sheetStId);
	{
		EtDbIdStr buf;
		VS(m_pDbCtx->EncodeEtDbId(viewId, &buf));
		acpt.addString("viewId", buf);
	}
	acpt.addString("viewName", spView->GetName());
	acpt.addString("viewNotice", spView->GetNotice());

	const IDBIds *pFields = spView->GetVisibleFields();
	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

    ks_stdptr<_Worksheet> spWorksheet;
    hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
    if (FAILED(hr))
        return hr;

	IDbFieldsManager *pFieldManager = spDbSheetOp->GetFieldsManager();
	{
		wo::sa::Leave fieldsList(wo::sa::enterArray(&acpt, "list")); // acpt.addKey("list"); acpt.beginArray();
		for (EtDbIdx i = 0, c = pFields->Count(); i < c; i++)
		{
			acpt.beginStruct();

			EtDbId fieldId = pFields->IdAt(i);
			ks_stdptr<IDbField> spField;
			pFieldManager->GetField(fieldId, &spField);
			if (spField == nullptr)
				return E_DBSHEET_FIELD_NOT_FOUND;
			PCWSTR typeStr = nullptr;
			VS(m_pEncodeDecoder->EncodeFieldType(spField->GetType(), &typeStr));
			ASSERT(typeStr);
			acpt.addString("fieldType", typeStr);
			EtDbIdStr buf;
			VS(m_pDbCtx->EncodeEtDbId(fieldId, &buf));
			acpt.addString("fieldId", buf);
			acpt.addString("fieldName", spField->GetName());
			acpt.addString("fieldDescription", spField->GetDescription());
			// descriptor
			acpt.addKey("descriptor");
			acpt.beginStruct();
			// 通用的信息
			acpt.addString("numberFormat", spField->GetNumberFormat());
			SerialiseExtraFieldInfo(spField.get(), &acpt, m_pDbCtx, m_wwb->GetCoreWorkbook()->GetBook());
            DbSheetValueSerialiser dbSerialiser(m_wwb->GetCoreWorkbook(), spWorksheet, &acpt, VarObj{}, &m_errMsg);
            dbSerialiser.SerialiseFieldDefaultValue(spField.get());
			acpt.endStruct();

			acpt.addKey("config");
			acpt.beginStruct();
			acpt.addBool("isRequired", alg::BOOL2bool(spFormView->GetFieldOption(fieldId)->GetIsRequired()));
			acpt.endStruct();

			acpt.endStruct();
		}
	}
	return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpDevListRecordsQueryClass::DbHttpDevListRecordsQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.devListRecords"))
{
	m_cmdPermissionCheckWhiteList.insert(GetTag());
}

HRESULT DbHttpDevListRecordsQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	// 开发者接口. 屏蔽全部权限检查
	DbSheet::DisableDbProtectScope dbPrtDisabler(m_spProtectionJudgement);
	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");
	return listRecordsExec(sheetStId, param, ctx, m_commonHelper, m_wwb, m_pDbCtx, m_errMsg);
}

//////////////////////////////////////////////////////////////////////////
DbHttpListSheetsQueryClass::DbHttpListSheetsQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.listSheets"))
{
}

HRESULT DbHttpListSheetsQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    IKWorkbook *pWorkbook = m_wwb->GetCoreWorkbook();
    IKWorksheets *pWorksheets = pWorkbook->GetWorksheets();
    INT sheetCount = pWorksheets->GetSheetCount();
	bool showVeryhidden = true;
	if (param.has("showVeryhidden"))
	{
		VAR_OBJ_EXPECT_BOOL(param, "showVeryhidden")
		showVeryhidden = param.field_bool("showVeryhidden");
	}

	ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    pWorkbook->GetBook()->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    if (!spCustomStorMgr)
        return E_FAIL;

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
	{
		sa::Leave leaveArray(sa::enterArray(&acpt, "sheets"));
		for (INT i = 0; i < sheetCount; i++)
		{
			IKWorksheet *pWorksheet = pWorksheets->GetSheetItem(i);
			ISheet* pSheet = pWorksheet->GetSheet();
			UINT sheetId = pSheet->GetStId();
			if (!pSheet->IsDbSheet() && !pSheet->IsDbDashBoardSheet())
				continue;

			if(!showVeryhidden)
			{
				SHEETSTATE sheetState = ssVisible;
				pSheet->GetVisible(&sheetState);
				if (sheetState == ssVeryhidden)
					continue;
			}

			HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
			if(FAILED(hr))
				continue;

			sa::Leave leaveStruct(sa::enterStruct(&acpt, nullptr));
			SerialiseSheetInfo(pWorkbook, pWorksheet, &acpt, &m_errMsg);

			spCustomStorMgr->SerializeBySheetId(sheetId, &acpt);
		}
	}

	if (m_wwb->GetBMP()->bDbSheet)
		acpt.addString("bookType", __X("db"));
	else if (m_wwb->GetBMP()->bKsheet)
		acpt.addString("bookType", __X("as"));
    return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpListWebExtensionsQueryClass::DbHttpListWebExtensionsQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.listWebExtensions"))
{
}

HRESULT DbHttpListWebExtensionsQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    IKWebExtensionMgr* pWebExtensionMgr = m_wwb->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return E_FAIL;

    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
    if(FAILED(hr))
        return hr;

    ks_stdptr<_Worksheet> spWorksheet;
	hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
	if (FAILED(hr))
		return hr;

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("webExtensions");
    acpt.beginArray();

    UINT webExtensionCount = 0;
    ISheet* pSheet = spWorksheet->GetSheet();
	pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
	for (UINT i = 0; i < webExtensionCount; ++ i)
	{
		ks_stdptr<IKWebExtension> spWebExtension;
        hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            return hr;

        LPCWSTR propertyValue = nullptr;
        hr = spWebExtension->GetProperty(GetWebofficeUniquePropertyKey(), &propertyValue);
        if (FAILED(hr) || !propertyValue)
            continue;

       acpt.beginStruct();
       SerialiseWebExtension(sheetId, spWebExtension, &acpt, &m_commonHelper);
       acpt.endStruct();
    }
    acpt.endArray();
    return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpGetDashboardChartResultQueryClass::DbHttpGetDashboardChartResultQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.getDashboardChartResult"))
{
}

HRESULT DbHttpGetDashboardChartResultQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetId = param.field_uint32("sheetId");
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
    pBook->STSheetToRTSheet(sheetId, &sheetIdx);

    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
    if(FAILED(hr))
        return hr;

    IKWebExtensionMgr* pWebExtensionMgr = m_wwb->GetCoreWorkbook()->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return E_FAIL;

    ks_stdptr<IDBChartStatisticMgr> spDbChartStatisticMgr;
    hr = m_commonHelper.GetDBChartStatisticMgr(sheetId, &spDbChartStatisticMgr);
    if (FAILED(hr))
        return hr;

    binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);
    acpt.addKey("results");
    acpt.beginArray();

    VAR_OBJ_EXPECT_ARRAY(param, "webExtensions")
    binary_wo::VarObj webExtensions = param.get_s("webExtensions");
    int32 webExtensionCount = webExtensions.arrayLength_s();
    for (int32 i = 0; i < webExtensionCount; ++i)
    {
        binary_wo::VarObj item = webExtensions.at(i);
        PCWSTR webExtensionKey = item.field_str("webExtensionKey");
        IKWebExtension* pWebExtension = pWebExtensionMgr->FindWebExtension(sheetIdx, webExtensionKey);
        if (!pWebExtension)
            return E_FAIL;

        EtDbId moduleId = INV_EtDbId;
        hr = DbDashboard::GetDbChartStatisticModuleId(pWebExtension, moduleId);
        if (FAILED(hr))
            return hr;

        ks_stdptr<IDBChartStatisticModule> spDbChartStatisticModule;
        hr = spDbChartStatisticMgr->GetItemById(moduleId, &spDbChartStatisticModule);
        if (FAILED(hr))
            return hr;

        UINT srcSheetId = spDbChartStatisticModule->GetDataSourceId();
        if (srcSheetId == 0)
            return E_FAIL;

        ks_stdptr<IDBSheetOp> spDbSheetOp;
        hr = m_commonHelper.GetDBSheetOp(srcSheetId, &spDbSheetOp);
        if (FAILED(hr))
            return hr;

		ks_stdptr<IDBIds> spVisibleRecords;
		hr = spDbChartStatisticModule->GetVisibleRecords(&spVisibleRecords);
		if (FAILED(hr))
            return hr;
        std::vector<EtDbId> newVisibleRecords;
        newVisibleRecords.reserve(spVisibleRecords->Count());
        for (EtDbIdx idx = 0, c = spVisibleRecords->Count(); idx < c; ++idx)
            newVisibleRecords.push_back(spVisibleRecords->IdAt(idx));
        if (item.has("filter"))
        {
            VarObj filter = item.get_s("filter");
            if (binary_wo::typeStruct != filter.type())
                return E_DBSHEET_FILTER_INVALID;

            ks_stdptr<IDbExtraFilter> spExtraFilter;
            VS(_appcore_CreateObject(CLSID_KDbExtraFilter, IID_IDbExtraFilter, (void**)& spExtraFilter));
            spExtraFilter->Init(spDbSheetOp.get());
            const IDbExtraFilter* pFailedFilter = nullptr;
            hr = DbSheet::filtrateByExtraFilter(spExtraFilter, filter, m_commonHelper, spDbSheetOp.get(), true, &pFailedFilter);
            if (FAILED(hr))
            {
                WriteFilterErrMsg(m_errMsg, pFailedFilter);
                return hr;
            }

            IKUserConn* user = ctx->getUser();
            size_t newSize = newVisibleRecords.size();
            hr = spExtraFilter->FilterVisibleRecords(newVisibleRecords.data(), newSize, user);
            if (FAILED(hr))
                return hr;
            newVisibleRecords.resize(newSize);
        }
        ks_bstr statisticResult;
        if (newVisibleRecords.size() == spVisibleRecords->Count())
        {
            hr = spDbChartStatisticModule->GetStaticResult(&statisticResult);
            if (FAILED(hr))
                return hr;
        }
        else
        {
            hr = spDbChartStatisticModule->GetNoRtsStatisticResult(newVisibleRecords.data(), newVisibleRecords.size(), &statisticResult);
            if (FAILED(hr))
                return hr;
        }
        acpt.beginStruct();
        acpt.addString("webExtensionKey", webExtensionKey);
		acpt.addString("result", statisticResult.c_str());
        acpt.endStruct();
    }
    acpt.endArray();
    return S_OK;
}

//////////////////////////////////////////////////////////////////////////
DbHttpGetFilterValuesListQueryClass::DbHttpGetFilterValuesListQueryClass(wo::KEtWorkbook* wwb)
    : DbHttpQueryClassBase(wwb, __X("http.db.getFilterValuesList"))
{

}

HRESULT DbHttpGetFilterValuesListQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);
	return DbSheet::SerializeFilterList(param, ctx, &m_commonHelper, &acpt, m_version);
}

HRESULT DbHttpGetFilterValuesListQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    if(!m_spProtectionJudgement->HasHiddenData())
        return S_OK;

    UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = DbSheet::GetEtDbId(param, "viewId");
    HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
    if(FAILED(hr))
        return hr;

    return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}

//////////////////////////////////////////////////////////////////////////
DbHttpGetFilterValuesQueryClass::DbHttpGetFilterValuesQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.getFilterValues"))
{
}

HRESULT DbHttpGetFilterValuesQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	HRESULT hr = S_OK;
    VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
    UINT sheetStId = param.field_uint32("sheetId");

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
        return hr;
	
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	hr = spDbSheetOp->GetDbSheetViews(&spDbSheetViews);
	if (FAILED(hr))
        return hr;
    
	IDBSheetView* pDbSheetView = spDbSheetViews->GetDefaultView();
	if (!pDbSheetView)
		return E_FAIL;

	const IDbFilter* pFilter = pDbSheetView->GetConstFilter();
	if (!pFilter)
		return E_FAIL;

	VAR_OBJ_EXPECT_STRING(param, "fieldId")
	EtDbId fldId = m_commonHelper.GetEtDbId(param, "fieldId");

	IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();
	ks_stdptr<IDbField> spField;
    hr = pFieldsMgr->GetField(fldId, &spField);
    if (FAILED(hr))
        return hr;

	KDBFilterValuesSearchType searchTy = DBFVST_None;
    PCWSTR searchStr = nullptr;
    if (param.has("search"))
    {
		VAR_OBJ_EXPECT_STRUCT(param, "search");
		binary_wo::VarObj vSc = param.get("search");
        hr = _appcore_GainEncodeDecoder()->DecodeKDBFilterValuesSearchType(vSc.field_str("type"), &searchTy);
        if (FAILED(hr))
		    return hr;
        searchStr = vSc.field_str("value");
    }

	VAR_OBJ_EXPECT_NUMERIC(param, "maxRecordCount")
	UINT maxRecordCnt = param.field_int32("maxRecordCount");

	// todo 未来可能会拓展
	BOOL bDateClassify = FALSE;

	class KFilterValueEnum : public IFilterValueEnum
    {
    public:
        KFilterValueEnum(KSerialWrapBinWriter* pAcpt)
			: m_pAcpt(pAcpt)
		{}

        STDPROC Do(PCWSTR dispText) override
        {
			sa::Leave filterValue(sa::enterStruct(m_pAcpt, nullptr));
			m_pAcpt->addString("text", dispText);
            return S_OK;
        }
    private:
		KSerialWrapBinWriter* m_pAcpt = nullptr;
    };

	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, ctx);

	sa::Leave filterValues(sa::enterArray(&acpt, "filterValues"));
	KFilterValueEnum filterValueEnum(&acpt);
	hr = pFilter->GetFilterValues(fldId, bDateClassify, maxRecordCnt, searchStr, &filterValueEnum);
	if (FAILED(hr))
		return hr;

    return S_OK;
}

HRESULT DbHttpGetFilterValuesQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
	if (!m_spProtectionJudgement->HasHiddenData())
		return S_OK;

	VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
	UINT sheetStId = param.field_uint32("sheetId");
	return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}


//////////////////////////////////////////////////////////////////////////
DbHttpGetServerRenderDataQueryClass::DbHttpGetServerRenderDataQueryClass(KEtWorkbook* wb)
        : DbHttpQueryClassBase(wb, __X("http.db.getServerRenderData"))
{
}

HRESULT DbHttpGetServerRenderDataQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt)
{
    if (!param.has("sheetId") && !param.has("shareId"))
        return E_INVALID_REQUEST;

    UINT sheetId = 0;
    if (param.has("sheetId"))
    {
        VAR_OBJ_EXPECT_NUMERIC(param, "sheetId")
        sheetId = param.field_uint32("sheetId");
    }
    else if (param.has("shareId"))
    {
        VAR_OBJ_EXPECT_STRING(param, "shareId")
        ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
        IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
        VS(pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr));
        ISharedLink* pShareLink = spSharedLinkMgr->GetItem(param.field_str("shareId"));
        ks_stdptr<ISharedLinkSheet> spSharedSheet = pShareLink;
        if (spSharedSheet)
            sheetId = spSharedSheet->GetSheet()->GetStId();
    }

    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    if (!spWorksheet->GetSheet()->IsDbDashBoardSheet())
        return E_FAIL;

    hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetId);
    if(FAILED(hr))
        return hr;

    bool isMobile = false;
    if (param.has("deviceType"))
    {
        VAR_OBJ_EXPECT_STRING(param, "deviceType")
        WebStr deviceTypeStr = param.field_str("deviceType");
        if (xstrcmp(deviceTypeStr, __X("mobile")) == 0)
            isMobile = true;
    }
    binary_wo::BinWriter *pResponse = pCtx->getHttpResponse();
    KSerialWrapBinWriter acpt(*pResponse, pCtx);
    sa::Leave activeSheetLeave(sa::enterStruct(&acpt, "activeSheet"));
    bool editable = SUCCEEDED(m_spProtectionJudgement->CheckCanManageDBChart(sheetId));
    acpt.addBool("editable", editable);
    {
        sa::Leave propertiesLeave(sa::enterStruct(&acpt, "properties"));
        ISheet* pSheet = spWorksheet->GetSheet();
        acpt.addUint32("id", pSheet->GetStId());
        PCWSTR sheetName = nullptr;
        pSheet->GetName(&sheetName);
        acpt.addString("name", sheetName);
        ks_stdptr<IDBDashBoardDataOp> spDbDashboardOp;
        VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboardOp));
        acpt.addString("description", spDbDashboardOp->GetSheetDescription());
        acpt.addString("icon", spDbDashboardOp->GetSheetIcon());
        acpt.addString("type", __X("xlDbDashBoardSheet"));
    }
    VAR_OBJ_EXPECT_STRUCT(param, "dbDashboard");
    binary_wo::VarObj dbDashboardParam = param.get_s("dbDashboard");
    VAR_OBJ_EXPECT_STRUCT(dbDashboardParam, "lang");
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    hr = dashboardModuleMgrWrapper.SerializeServerRenderData(dbDashboardParam, isMobile, pCtx, &acpt);
    if (FAILED(hr))
        return hr;

	ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    if (!spCustomStorMgr)
        return E_FAIL;
	spCustomStorMgr->SerializeBySheetId(sheetId, &acpt);

    return S_OK;
}

HRESULT DbHttpGetServerRenderDataQueryClass::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* ctx)
{
    return S_OK; // 内部判断
}


//////////////////////////////////////////////////////////////////////////
DbHttpFieldBaseSchemaQueryClass::DbHttpFieldBaseSchemaQueryClass(KEtWorkbook* wb)
	: DbHttpQueryClassBase(wb, __X("http.db.fieldBaseSchema"))
{
}

HRESULT DbHttpFieldBaseSchemaQueryClass::Exec(const binary_wo::VarObj& param, KEtRevisionContext* ctx, ISerialAcceptor*)
{
	IKWorkbook *pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets *pWorksheets = pWorkbook->GetWorksheets();
	binary_wo::BinWriter *pResponse = ctx->getHttpResponse();
	KSerialWrapBinWriter acpt(*pResponse, ctx);

	if (!param.has("sheetId"))
		return E_DBSHEET_SHEET_NOT_FOUND;
	UINT sheetStId = param.field_uint32("sheetId");
	IDX sheetIdx = INVALIDIDX;
	pWorkbook->GetBook()->STSheetToRTSheet(sheetStId, &sheetIdx);
	if (INVALIDIDX == sheetIdx)
		return E_DBSHEET_SHEET_NOT_FOUND;

	IKWorksheet *pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
	ISheet* pSheet = pWorksheet->GetSheet();
	if (pSheet->IsDbSheet())
	{
		HRESULT hr = m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
		if(FAILED(hr))
			return hr;
		
		DbSheetValueSerialiser dbSerialiser(pWorkbook, pWorksheet, &acpt, param, &m_errMsg);
		VS(dbSerialiser.SerialiseAllFieldsBaseSchema());
	}
	
	return S_OK;
}

} // wo
