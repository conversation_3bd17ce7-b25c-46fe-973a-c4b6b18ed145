﻿#ifndef __WEBET_DATABASE_FIELD_SPEC_H__
#define __WEBET_DATABASE_FIELD_SPEC_H__

#include "database_def.h"

namespace wo
{
namespace Database
{

class FieldDefault : public IDbField
{
public:
    virtual ~FieldDefault() {}
    virtual HRESULT Set(FieldContext *pContext, const RANGE &rg) override;
    virtual HRESULT Clear(FieldContext *pContext, const RANGE &rg) override;
    virtual HRESULT Initialise(FieldContext *pContext, const RANGE &rg) override;
    virtual BOOL Identify(FieldContext *pContext, const RANGE &rg, VALIDATION dv) override;
protected:
    void addModifier(IFieldModifier *modifier);
    void addIdentifier(IFieldIdentifier *identifier);
    void addInitialiser(IFieldInitialiser *initialiser);
private:
    std::vector<std::unique_ptr<IFieldModifier>> m_modifiers;
    std::vector<std::unique_ptr<IFieldIdentifier>> m_identifiers;
    std::vector<std::unique_ptr<IFieldInitialiser>> m_initialisers;
};

#define DECLARE_DB_FIELD(name)                                                  \
class name##Field : public FieldDefault                                         \
{                                                                               \
public:                                                                         \
    name##Field();                                                              \
    virtual ~name##Field() {}                                                   \
    virtual FieldType GetType() override {return dft##name;}                    \
};

DECLARE_DB_FIELD(Date)
DECLARE_DB_FIELD(Time)
DECLARE_DB_FIELD(Number)
DECLARE_DB_FIELD(Currency)
DECLARE_DB_FIELD(Percentage)
DECLARE_DB_FIELD(Text)
DECLARE_DB_FIELD(ID)
DECLARE_DB_FIELD(Phone)
DECLARE_DB_FIELD(Email)
DECLARE_DB_FIELD(Hyperlink)
DECLARE_DB_FIELD(Checkbox)
DECLARE_DB_FIELD(List)
DECLARE_DB_FIELD(Rating)
DECLARE_DB_FIELD(Schedule)
DECLARE_DB_FIELD(CellPicture)
DECLARE_DB_FIELD(Scan)
DECLARE_DB_FIELD(AI)
DECLARE_DB_FIELD(GenQRLabel)
} // Database
} // wo

#endif // __WEBET_DATABASE_FIELD_SPEC_H__