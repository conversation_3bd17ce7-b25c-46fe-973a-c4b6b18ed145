﻿#include "etstdafx.h"
#include "persist/et_persist_basic_itf.h"
#include "split_book_opt.h"
#include "etcore/little_alg.h"
#include "kfc/string.h"
#include "workbook.h"
#include "et_revision_context_impl.h"
#include "et_binvar_spec.h"
#include "etcore/et_core_sheet.h"
#include "webbase/logger.h"
#include "util.h"
#include "utils/attachment_utils.h"
#include "woetsetting.h"
#include "etcmd/src/censor/exportcensordata.h"
#include "et_hard_define_strings.h"
#include "utils/qrlabel_utils.h"
namespace wo
{

namespace CellEmpty
{

class CellValueAcpt: public ICellValueAcpt
{
private:
	bool* m_isHasValue;
	COL* m_col;
	ROW* m_row;
	KEtRevisionContext* m_ctx;
	ISheet* m_pSheet;

public:    
	CellValueAcpt(bool* isHasValue, ROW* row, COL* col, KEtRevisionContext* ctx, ISheet *pSheet)
		: m_isHasValue(isHasValue), m_col(col), m_row(row), m_ctx(ctx), m_pSheet(pSheet)
	{
	}

	STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken) 
	{

		if (pToken == NULL)
		{	
			// 继续枚举
			return 0;
		}

		//判断读取的区域是否在保护区域内，若在，禁止读取
		bool bCellInvisibleForCurUser = m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col);
		if(bCellInvisibleForCurUser)
			return 0;//继续枚举

		DWORD dwType = alg::GetExecTokenMajorType(pToken);
		if (dwType == etexec::ETP_NONE)
		{
			// 继续枚举
			return 0;
		}

		// 走到这里  说明不是空单元格
		*m_col = col;
		*m_row = row;
		*m_isHasValue = true;
		return 1;
	}
};
}

void SplitBookOpt::initDefaultRange(Range** spEXpRange)
{
	if (m_spAutoFilter)
	{
		RANGE filterRg(m_pSheet->GetBMP());
		m_spAutoFilter->GetFilterRange(&filterRg);
		m_spWorksheet->GetRangeByData(&filterRg, spEXpRange);
	}
	else
	{
		m_spWorksheet->get_UsedRange(spEXpRange);
	}
}

HRESULT SplitBookOpt::GetRangeAddress(BSTR* pbsAddr, Range*spEXpRange,  IKRanges** rgsTbl)
{
	app_helper::GetIRanges(spEXpRange, rgsTbl);
	CELL cell = { 0 };
	return m_spWorksheet->GetRangeAddress(m_spWorksheet->GetWorkbook(),
		*rgsTbl,
		crfAbsRowFrom | crfAbsRowTo | crfAbsColFrom | crfAbsColTo | crfForceSheet,
		cell,
		pbsAddr);
}

HRESULT SplitBookOpt::GetAppSettings(ISheet* pSheet, IAppSettings** spAppSettings)
{
	HRESULT hr = WO_FAIL;
	ks_stdptr<IWorkspace> spWorkspace;
	if (FAILED(hr = pSheet->LeakBook()->GetWorkspace(&spWorkspace)))
	{
		return hr;	
	}
	
	return spWorkspace->GetAppSettings(spAppSettings);
}

bool SplitBookOpt::checkIsListObjectHeader(const RANGE* rg, bool& headIsHide)
{
	ks_stdptr<ICoreListObjects> spCoreLists;
	m_pSheet->GetExtDataItem(edSheetListObjects, (IUnknown**)&spCoreLists);
	if (!spCoreLists)
		return false;
	
	bool br = false;
	size_t iCount = spCoreLists->GetCount();
	for (size_t i = 0; i < iCount; ++i)
	{
		ks_stdptr<ICoreListObject>  spCoreList;
		spCoreLists->GetItem(i, &spCoreList);
		if (!spCoreList)
			continue;

		RANGE    listRg(m_pSheet->GetBMP());
		spCoreList->GetRange(&listRg);
		if (listRg.ColFrom() >= rg->ColFrom() && listRg.ColTo() <= rg->ColTo()
			&& listRg.RowFrom() == rg->RowFrom())
		{
			br = true;
			RECT  listRC; 
			RANGE    listDataRg(m_pSheet->GetBMP());
			spCoreList->GetDataRange(&listDataRg);

			RANGE    listFullRg(m_pSheet->GetBMP());
			spCoreList->GetRange(&listFullRg);
			if (listFullRg.RowFrom() == listDataRg.RowFrom())
				headIsHide = true;
			
			listRC.top = listDataRg.RowFrom();
			listRC.bottom = listDataRg.RowTo();
			listRC.left = listDataRg.ColFrom();
			listRC.right = listDataRg.ColTo();
			m_spSheetSpliter->SetListObjRect(listRC, headIsHide);
			break;
		}
	}
	if (!br)
	{
		RECT  listRC;
		listRC.top = listRC.bottom = listRC.left = 	listRC.right = -1;
		m_spSheetSpliter->SetListObjRect(listRC, false);
	}
	return br;
}

bool SplitBookOpt::checkRangeIsOneRow(const RANGE* rg)
{
	if (rg->RowFrom() == rg->RowTo())
	{
		m_bDisplayPreView = false;
		return false;
	}

	m_bDisplayPreView = true;
	return true;
}

// bManualCheckIncludeTitle 是否是点击
bool SplitBookOpt::initRect(IKRanges* spRg, bool bManualCheckIncludeTitle, bool bHasSelectRange, int selectIndex,
	bool* sp_bStartEnabled, bool* sp_bIsSameBook, bool* sp_cbIncludeTitleEnabled, bool* sp_cbIncludeTitleCheck, ks_wstring& errMsg)
{
	ks_castptr<IKEtApplication> pEtApp = m_wwb->GetCoreApp();
	ks_stdptr<_Application> spApp = pEtApp.get();
	IDX idx = INVALIDIDX;
	ks_stdptr<IBookOp> spBookOp;
	m_spWorksheet->GetWorkbook()->GetBook()->GetOperator(&spBookOp);
	m_pSheet->GetIndex(&idx);

	UINT count = 0;
	INT bookID = alg::STREF_INV_BOOK;
	
	spRg->GetCount(&count);
	if (count != 1)
	{
		*sp_bStartEnabled = false;
		errMsg = __X("no_range");
		return false;
	}
	else
	{
		*sp_bStartEnabled = true;
	}

	const RANGE* tempRg = nullptr;
	spRg->GetItem(0, &bookID, &tempRg);

	if (tempRg->SheetFrom() != tempRg->SheetTo() || tempRg->SheetTo() != idx || bookID != alg::STREF_THIS_BOOK)
	{
		*sp_bIsSameBook = false;
		return false;
	}
	else
	{
		*sp_bIsSameBook = true;
	}

	if (selectIndex >= tempRg->Width() || selectIndex < 0)
	{
		errMsg = __X("index_error");
		return false;
	}

	m_selectIndex = selectIndex;

	bool bHeaderIsHide = false;
	bool bRgIncludeTable = checkIsListObjectHeader(tempRg, bHeaderIsHide);
	bool bRgIncludeAutofilter = false;
	if (m_spAutoFilter)
	{
		RANGE autoFilterRg(m_pSheet->GetBMP());
		m_spAutoFilter->GetFilterRange(&autoFilterRg);
		if (autoFilterRg.RowFrom() == tempRg->RowFrom() &&
			autoFilterRg.ColFrom() >= tempRg->ColFrom() &&
			autoFilterRg.ColTo() <= tempRg->ColTo())
		{
			bRgIncludeAutofilter = true;
		}
	}
	//设置checkbox置灰主要是表格和筛选的情况，这时候要用处理合并单元格之前的情况判断 
	*sp_cbIncludeTitleEnabled = !(bRgIncludeTable || bRgIncludeAutofilter);

	BOOL bMerged = FALSE;
	m_pSheet->IsMerged(tempRg->RowFrom(), tempRg->ColFrom(), &bMerged);

	if (!bManualCheckIncludeTitle)
		*sp_cbIncludeTitleCheck = false;

	ks_stdptr<Range> ptrRangeNew;
	m_spWorksheet->GetRangeByData(tempRg, &ptrRangeNew);

	//引起InitRect主要有三个地方，初始化对话框、区选框区选数据、
	//是否隐藏标题头checkbox点.  初始化和区选后，相当于是否隐藏标题头是初始状态
	//在点击是事隐藏标题头的时候，会设置m_bManualCheckIncludeTitle为ture
	//下面的情况分别是：1、如果选中表格或者自动筛选  设置有标题头（上面会设置checkobx置灰）
	//2、如果非点击checkbox,相当于首次初始化数据，通过自动检测标题头的函数
	//3、如果第一个单元格是合并单元格，但是首次初始化数据，就默认设为有标题头
	//4、有一种表格隐藏了标题的情况，设置是不包含标题头
	bool ischeck =  *sp_cbIncludeTitleCheck;
	if (ischeck)
		;
	else if (bRgIncludeTable || bRgIncludeAutofilter)
		*sp_cbIncludeTitleCheck = true;
	else if (!bManualCheckIncludeTitle && app_helper::GuessRangeSortHeader(ptrRangeNew, TRUE, TRUE, FALSE) != 0)
		*sp_cbIncludeTitleCheck = true;

	if (bMerged && !bManualCheckIncludeTitle)  //标题头是合并单元格的情况 
		*sp_cbIncludeTitleCheck = true;

	if (bRgIncludeTable && bHeaderIsHide) //表格头隐藏的情况 
		*sp_cbIncludeTitleCheck = false;
	
	RANGE processMergedRg(*tempRg);
	//当合并单元格的时候，如果判断有标题头后，必须把数据区域往后推
	if (bMerged && *sp_cbIncludeTitleCheck)
	{
		RANGE rg(m_pSheet->GetBMP());
		m_pSheet->RetieveMerge(tempRg->RowFrom(), tempRg->ColFrom(), &rg);
		if (rg.RowTo() <= processMergedRg.RowTo() && rg.RowTo() >= processMergedRg.RowFrom())
			processMergedRg.SetRowFrom(rg.RowTo());
	}

	//只有一行不用拆
	if (processMergedRg.RowTo() == processMergedRg.RowFrom())
	{
		if (!bManualCheckIncludeTitle)
			*sp_cbIncludeTitleCheck = false;
		*sp_cbIncludeTitleEnabled = false;	
		*sp_bStartEnabled = false;
		m_Rect.left = -1;//制造一个无效区域
		m_Rect.right = -2;
		errMsg = __X("only_row");
		return false;
	}

	m_bHasHeader = *sp_cbIncludeTitleCheck;

	m_spRg = new RANGE(processMergedRg);

	if (m_bHasHeader)
	{
		m_spRg->SetRowFrom(processMergedRg.RowFrom() + 1);
	}

	RECT tempRx = { 0 };
	tempRx.left = m_spRg->ColFrom();
	tempRx.right = m_spRg->ColTo();
	tempRx.top = m_spRg->RowFrom();
	tempRx.bottom = m_spRg->RowTo();
	WOLOG_INFO << "tempRx:" << tempRx;
	m_spWorksheet->GetSheet()->CalcUsedScale(&m_Rect);
	WOLOG_INFO << "m_Rect:" << m_Rect;

	if (!Rect_Intersect(m_Rect, tempRx, m_Rect))
	{
		*sp_bStartEnabled = false;
		errMsg = __X("not_in_valid_range");
		return false;
	}
	else
		*sp_bStartEnabled = true;

	m_spRg->SetColFrom(m_Rect.left);
	m_spRg->SetColTo(m_Rect.right);
	m_spRg->SetRowFrom(m_Rect.top);
	m_spRg->SetRowTo(m_Rect.bottom);

	if (!checkRangeIsOneRow(m_spRg))
	{
		errMsg = __X("only_row");
		*sp_bStartEnabled = false;
		return false;
	}	
	else
	{
		*sp_bStartEnabled = true;
	}

	int row = -1;
	int col = -1;
	bool isHasValue = false;
    et_sdptr<ISheetEnum> spSheetEnum;
    m_pSheet->CreateEnum(&spSheetEnum);
	CellEmpty::CellValueAcpt cellValue(&isHasValue, &row, &col, m_ctx, m_pSheet);
	int ret = spSheetEnum->EnumCellValue(*m_spRg, &cellValue);

	if (!isHasValue)
	{
		errMsg = __X("range_no_data");
		*sp_bStartEnabled = false;
		return false;
	}

	//用户没选择并且有筛选的情况不用设置区域
	if (!m_spAutoFilter || bHasSelectRange || bMerged)
	{
		m_spSheetSpliter->SetSelectRange(m_spRg, true);
	}

	m_spSheetSpliter->SetHeaderBegin(tempRg->RowFrom());

	return true;
}

HRESULT SplitBookOpt::InitData(int sheetIdX)
{

	HRESULT hr = S_OK;

	// 打印一下sheetIDX
	WOLOG_INFO << "InitData:" << sheetIdX;

	m_spWorkbook = m_wwb->GetCoreWorkbook();
	if (sheetIdX >= 0)
	{
		m_spWorksheet = m_spWorkbook->GetWorksheets()->GetSheetItem(sheetIdX);
		m_pSheet = m_spWorksheet->GetSheet();

		hr = GetAppSettings(m_pSheet, &m_spAppSettings);
		if (FAILED(hr))
		{
			return hr;
		}

		// 筛选
		m_spAutoFilter = m_spWorksheet->GetCoreAutoFilter(NULL);

		WOLOG_INFO << "filter is " << m_spAutoFilter; 
		
		VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
		m_spStringTools->SetEnv(m_pSheet);
	}

	// 拆分表格的核心接口
	_applogic_CreateObject(CLSID_kAfSheetSpliter, IID_IKAfSheetSpliter, (void**)&m_spSheetSpliter);
	if (!m_spSheetSpliter)
	{
		return WO_FAIL;
	}

	if (m_spWorksheet)
	{
		ks_stdptr<IKWorksheet> spIKWorksheet = m_spWorksheet;
		hr = m_spSheetSpliter->Init(spIKWorksheet);
	}

	// 根据当前被拆的book 设置拆分之后的后缀
	if (m_wwb->GetBMP()->bKsheet)
	{
		m_spSheetSpliter->SetSaveFormat(".ksheet");
	} 

	return hr;
}

bool SplitBookOpt::isDateTypeField(int nfield, bool bHasSelectRange)
{
	if (!isRectValid(m_Rect))
			return false;
	ks_stdptr<IBookOp> spBookOp;
	m_spWorksheet->GetWorkbook()->GetBook()->GetOperator(&spBookOp);
	IDX nSheetId = 0;
	m_pSheet->GetIndex(&nSheetId);
	BOOL b1904 = m_spWorksheet->GetWorkbook()->GetBook()->Is1904DateSystem();

	COL col = 0;
	ROW rFrom = 0;
	ROW rTo = 0;
	RANGE filterRg(m_spWorksheet->GetSheet()->GetBMP());
	if (m_spAutoFilter && !bHasSelectRange)
	{
		m_spAutoFilter->GetFilterRange(&filterRg);
		col = filterRg.ColFrom() + nfield;
		rFrom = filterRg.RowFrom() + 1;
		rTo = filterRg.RowTo();
	}
	else
	{
		
		col = m_Rect.left + nfield;
		rFrom = m_Rect.top;
		rTo = m_Rect.bottom;
	}
	bool bHasData = false;
	for (ROW r = rFrom; r <= rTo; r++)
	{
		const_token_ptr pToken = NULL;
		const XF* pXf = NULL;
		spBookOp->GetCellValue(nSheetId, r, col, &pToken);
		spBookOp->GetCellFormat(nSheetId, r, col, &pXf, NULL);
		if (isDateType(pToken, pXf, b1904))
		{
			bHasData = true;
			break;
		}
	}
	return bHasData;
}

bool SplitBookOpt::isRectValid(const RECT & rect)
{
	return rect.left <= rect.right && rect.top <= rect.bottom;
}

bool SplitBookOpt::isDateType(const_token_ptr pToken, const XF* pXf, BOOL b1904)
{
	if (alg::GetExecTokenMajorType(pToken) != alg::ETP_VDBL)
		return false;
	if (!pXf || !pXf->pNumFmt)
		return false;

	kfc::nf::NFSTYLE style;
	style.FmtType = kfc::nf::FMT_TYPE_NULL;
	style.dwOpt = 0;

	VARIANT var;
	V_VT(&var) = VT_R8;
	V_R8(&var) = alg::const_vdbl_token_assist(pToken).get_value();
	kfc::nf::_XNFFormatEx2(var, b1904, pXf->pNumFmt->hfmt, NULL, &style);
	if (kfc::nf::FMT_TYPE_DATA_TIME == style.FmtType
		&& (style.dwOpt & kfc::nf::NFTYPE_DATE_D))
		return true;
	return false;
}

SplitSheetDateType SplitBookOpt::GetSplitDateType(int idx)
{
	if (!m_bDateType)
	{
		return ssdt_None;
	}

	SplitSheetDateType dType;
	if (idx == 0)
		dType = ssdt_Year;
	else if (idx == 1)
		dType = ssdt_Month;
	else if (idx == 2)
		dType = ssdt_Day;
	else
		dType = ssdt_None;

	return dType;
}

int SplitBookOpt::GetPreViewCount(int dataTypeIndex)
{
	if (m_selectIndex < 0)
	{
		return 0;
	}

	if (!m_spSheetSpliter)
		return 0;
	if (!isRectValid(m_Rect))
		return 0;
	QStringList lstData;
	m_spSheetSpliter->GetSplitItems(m_selectIndex, lstData, false, GetSplitDateType(dataTypeIndex));
	return lstData.size();
}

ks_wstring SplitBookOpt::get_column_name(ISheet* sht, COL col, bool isA1Ref)
{
	ASSERT(col >= 0 && col < sht->GetBMP()->cntCols);
	ks_wstring label = __X("");

	if (isA1Ref)
	{
		WCHAR arr[MAX_LOCALE_COL_A1REF_STR_LEN] = { 0 };
		ColumnIndex_Num2Str(col, arr, countof(arr));
		label += arr;
	}
	else
	{
		WCHAR arr[MAX_LOCALE_ROW_A1REF_STR_LEN] = { 0 };
		RowIndex_Num2Str(col, arr, countof(arr));
		label += arr;
	}

	return label;
}

bool SplitBookOpt::isA1Ref()
{
	if (!m_spAppSettings)
	{
		return false;
	}
	return m_spAppSettings->GetReferenceStyle() == RS_A1;
}

void SplitBookOpt::handleFormat(binary_wo::VarObj& param)
{
	if (!param.has("format"))
	{
		return;
	}
	
	ks_wstring sheetName = param.field_str("format");
	// 校验一下 前端只能传上来 .xlsx 和 .ksheet
	if (sheetName != __X(".xlsx") && sheetName != __X(".ksheet"))
	{
		return;
	}

	if (m_spSheetSpliter)
	{
		m_spSheetSpliter->SetSaveFormat(krt::fromUtf16(sheetName.c_str()));
	}
}

BOOL SplitBookOpt::isBookProtected()
{

	if (!m_spWorkbook) 
	{
		return TRUE;
	}

	ks_stdptr<IBookProtection> spBookProtection;
	spBookProtection = m_spWorkbook->GetProtection();
	ASSERT(spBookProtection);						
	BOOKPROTECTION protection = { 0 };					
	spBookProtection->GetProperty(&protection);
	if (protection.bProtectStruct)				
		return TRUE;
	return FALSE;
}

HRESULT SplitBookOpt::split(int dataTypeIndex, bool bToSheet, const ks_wstring path, SplitResultContext* resultContext, SplitBookCallBack* splitBookCallBack)
{

	if ((!bToSheet && splitBookCallBack == nullptr) || resultContext == nullptr)
	{
		return WO_FAIL;
	}

	ks_wstring& err = resultContext->GetErrStr();

	//检查是否可以拆分
	if (!m_spSheetSpliter->IsSplitable())
	{
		err = __X("SplitSheet_NotSupportSplit");
		return WO_FAIL;
	}

	if (_etcore_GetEtRevisionContext() == NULL)
	{
		err = __X("ETContext_Null");
		return WO_FAIL;
	}

	ks_stdptr<etoldapi::_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	INT nSheetCount = spWorkbook->GetWorksheets()->GetSheetCount();

	QStringList lstData;
	m_spSheetSpliter->GetSplitItems(m_selectIndex, lstData, m_bDateType, GetSplitDateType(dataTypeIndex));

	int nCount = lstData.size();
	if (nCount == 0)
	{
		err = __X("SplitSheet_HasNoFilterData");
		return WO_FAIL;
	}

	// if (bToSheet)
	// {
	// 	if (nCount > 50)
	// 	{
	// 		err = __X("SplitSheet_SplitCountExceedMax");
	// 		return WO_FAIL;
	// 	}

	// 	if (nSheetCount + nCount >= 256)
	// 	{
	// 		err = __X("SplitSheet_SplitAfterSheetCountExceedMax");
	// 		return WO_FAIL;
	// 	}

	// }

	// 禁止查看的区域 判断
	IEtProtectionCtx* iEtProtectionCtx = m_ctx->getProtectionCtx();
	if (iEtProtectionCtx->isRangeHasHidden(*m_spRg))
	{
		err = __X("SplitBook_NoAllVisiblesPermission");
		return WO_NO_ALL_VISIBLES_PERMISSION;
	}

	// 如果是拆分到当前sheet，还需要判断book是否时保护的
	if (bToSheet && isBookProtected())
	{
		err = __X("SplitBook_BookProtected");
		return WO_FAIL;
	}

	HRESULT hr = WO_FAIL;

	ks_stdptr<IKWorksheet> spFirstSplitSheet;
	ks_castptr<IKEtApplication> pEtApp = m_wwb->GetCoreApp();

	int idx = m_selectIndex;
	for (int i = 0; i < nCount; i++)
	{
		bool  bNeedMsgErrDlg = true;
		//拆分到多个表
		if (bToSheet)
		{
			ks_stdptr<IKWorksheet> spSplitSheet;
			if (m_bDateType)
				hr = m_spSheetSpliter->Split2Sheet(idx, lstData.at(i), &spSplitSheet, GetSplitDateType(dataTypeIndex), bNeedMsgErrDlg);
			else
				hr = m_spSheetSpliter->Split2Sheet(idx, lstData.at(i), &spSplitSheet, ssdt_None, bNeedMsgErrDlg);

			if(spSplitSheet)
			{
				if(!spFirstSplitSheet)
					spFirstSplitSheet = spSplitSheet;
				
				IDX sheetIdx = -1;
				spSplitSheet->GetSheet()->GetIndex(&sheetIdx);
				//拆分成新表后，将二维码转换成二维码单元格图片
				util::QRLabelHelper qrLabelHelper(m_wwb->GetCoreWorkbook(), m_ctx, util::QRLabelHelper::UseType_SplitToNewSheet, m_pParam);
				qrLabelHelper.ConvertQRLabel2CellImg(sheetIdx);
			}
		}
		else  //拆分到多个工作簿
		{
			if (m_bDateType)
				hr = m_spSheetSpliter->Split2Book(idx, lstData.at(i), path.c_str(), GetSplitDateType(dataTypeIndex), bNeedMsgErrDlg, splitBookCallBack);
			else
				hr = m_spSheetSpliter->Split2Book(idx, lstData.at(i), path.c_str(), ssdt_None, bNeedMsgErrDlg, splitBookCallBack);
		}

		if (FAILED(hr))
		{
			err = __X("SplitSheet_Other");
			if (E_ABORT == hr || !bNeedMsgErrDlg)
			{
				err = __X("SplitSheet_SplitAbort");
				break;
			}
			
			IKApplication* pApp = kxApp->coreApplication();
			if (!pApp)
				break;

			IKDocument* pDoc = pApp->GetActiveDocument();
			if (!pDoc)
				break;

			_DocumentEx* pDocumentEx = pDoc->GetDocumentEx();
			if (!pDocumentEx)
				break;

			if (pDocumentEx->IsSecurityDoc())
			{
				err = __X("SplitSheet_SecurityDoc");
				break;
			}
			return hr;

			// kxApp->messageBox(krt::fromUtf16(et_sSplitSheetError), MB_OK | MB_ICONWARNING);
		}
	}

	if (spFirstSplitSheet != NULL)
	{
		IDX index = -1;
		spFirstSplitSheet->GetSheet()->GetIndex(&index);
		resultContext->SetFirstSheetIndex(index);
	}

	return hr;

}

static ks_stdptr<etoldapi::Range> CreateRangeObj(IKWorkbook* pWorkbook, const std::vector<RANGE> &rgVec)
{
    ks_stdptr<etoldapi::Range> host;
	if (rgVec.empty())
		return host;

	IDX sheetIdx = rgVec[0].SheetFrom();
	ks_stdptr<IKRanges> spRanges;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRanges);
	for (auto it = rgVec.begin(); it != rgVec.end(); ++it)
	{
		ASSERT(it->SheetFrom() == it->SheetTo());
		ASSERT(it->SheetFrom() == sheetIdx);
		spRanges->Append(alg::STREF_THIS_BOOK, *it);
	}

	IKWorksheet* ws = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	VS(ws->GetRangeByData(spRanges, &host));
	return host;
}


STDIMP_(const QString&) SplitBookCallBack::GetEmptySheetDefaultName() const
{
	return m_emptySheetDefaultName;
}

STDIMP SplitBookCallBack::callBack(LPCWSTR fullPath, etoldapi::_Workbook* workbook)
{

	std::vector<ks_wstring> attachmentImageIds;
	std::vector<ks_wstring> attachmentVideoIds;
    std::vector<ks_wstring> cloudSpaceImageUrls;

	wo::ExportAllAttachment exporter(workbook, pCtx);
	exporter.CollectAllAttachment();
	attachmentImageIds = exporter.getAttachmentImageIds();
	attachmentVideoIds = exporter.getAttachmentVideoIds();
	cloudSpaceImageUrls = exporter.getCloudSpaceImageUrls();
    
	SplitBookItem splitBookItem(fullPath, attachmentImageIds, cloudSpaceImageUrls, attachmentVideoIds);

	m_SplitBookItems.push_back(splitBookItem);

	return S_OK;
}

STDIMP SplitBookCallBack::postProcessCopySheet(etoldapi::_Worksheet* pSrc, etoldapi::_Worksheet* pDst)
{
	ks_stdptr<_Worksheet> spWorksheet = pDst;
	
	if (isSelectionDeliver()) {
		IKWorksheet* pIKWorksheet = pSrc->GetWorkbook()->GetWorksheets()->GetSheetItem(m_pRange->SheetFrom());
		//粘贴
		IKAppPersist* pAppPersist = pCtx->woWorkbook()->GetCoreApp()->GetAppPersist();
		RANGETYPE rangeType = m_pRange->RangeType();
		
		//缩小一下范围
		if (rangeType == rtSheets || rangeType == rtCols || rangeType == rtRows) {
			RANGE usedRange(*m_pRange);
			pIKWorksheet->GetUsedRange(&usedRange);
			if (m_pRange->ColTo() > usedRange.ColTo() && m_pRange->ColFrom() <= usedRange.ColTo()) {
				m_pRange->SetColTo(usedRange.ColTo());
			}
			if (m_pRange->RowTo() > usedRange.RowTo() && m_pRange->RowFrom() <= usedRange.RowTo()) {
				m_pRange->SetRowTo(usedRange.RowTo());
			}
		}


		//源区域
		RANGE srcRg(*m_pRange);
		ks_stdptr<etoldapi::Range> spRg;
		spRg = pCtx->woWorkbook()->CreateRangeObj(srcRg);
		ks_stdptr<IRangeInfo> spRangeInfo(spRg);
		ks_stdptr<IAppCoreRange> spCoreRange;
		spRangeInfo->GetAppCoreRange(&spCoreRange);
		range_helper::ranges rgs;
		if (S_OK == spCoreRange->GetFilteredIRanges(TRUE, &rgs))
			pAppPersist->SetCutCopyRange(etCopy, pCtx->woWorkbook()->CreateRangeObj(rgs));
		else
			pAppPersist->SetCutCopyRange(etCopy, spRg);
		pCtx->setCFHandleStrategy(static_cast<CFHandleStrategy>(1));

		//目标区域
		//粘贴到左上角
		RANGE destRg(pDst->GetWorkbook()->GetBook()->GetBMP());
		destRg.SetSheetFromTo(0, 0);
		destRg.SetColFromTo(0, 0);
		destRg.SetRowFromTo(0, 0);

		std::vector<RANGE> vecRANGES;
		vecRANGES.push_back(destRg);
		ks_stdptr<etoldapi::Range> spDestRange= CreateRangeObj(pDst->GetWorkbook(), vecRANGES);
		KCOMPTR(IRangeInfo) ptrRangeInfo;
		ptrRangeInfo = spDestRange;

		//这个标志会保留列宽，不粘贴公式
		VARIANT varSelectionDeliver;
		V_VT(&varSelectionDeliver) = VT_BOOL;
		V_BOOL(&varSelectionDeliver) = VARIANT_TRUE;
		ptrRangeInfo->PasteSpecialSelectionDeliver(psFull, 0, FALSE, FALSE, TRUE, 0, NULL, &varSelectionDeliver);
		pAppPersist->SetCutCopyRange(etCopyCutNone, NULL);
	}

	return S_OK;
}

STDIMP SplitBookCallBack::init(bool distribute, RANGE* range)
{
	m_distribute = distribute;
	m_pRange = range;
	return S_OK;
}


STDIMP_(const bool) SplitBookCallBack::isDistribute () const 
{
	return m_distribute;
}

const bool SplitBookCallBack::isSelectionDeliver () const
{
	return m_pRange != nullptr;
}

RANGE* SplitBookCallBack::getRange() const
{
	return m_pRange;
}

HRESULT SplitBookOpt::splitOptSheet(binary_wo::VarObj& param, wo::KEtWorkbook* wwb, KEtRevisionContext* pCtx, SplitResultContext* resultContext)
{
	ks_wstring path;
	return splitOpt(param, true, path, nullptr, wwb, pCtx, resultContext);
}

HRESULT SplitBookOpt::splitOptBook(binary_wo::VarObj& param, const ks_wstring& path, SplitBookCallBack* splitBookCallBack, wo::KEtWorkbook* wwb, KEtRevisionContext* pCtx)
{

	WOLOG_INFO << "path:" << path;
	QDir dir(krt::fromUtf16(path.c_str()));
	if (!dir.exists() && !(param.has("distribute")))
	{
		WOLOG_INFO << "文件夹不存在";
		bool isCreate = dir.mkpath(krt::fromUtf16(path.c_str()));
		if (!isCreate)
		{
			WOLOG_INFO << "创建文件夹失败";
			return E_FAIL;
		}
	}

	if (!param.has("sheetIdxs"))
	{
		return splitOpt(param, false, path, splitBookCallBack, wwb, pCtx, splitBookCallBack);
	}

	IBook *pBook = wwb->GetCoreWorkbook()->GetBook();
	binary_wo::VarObj sheetIdxsVarObj = param.get("sheetIdxs");
	std::vector<SplitSheetToNewBookItem> sheetIdxsVec;
	int length = sheetIdxsVarObj.arrayLength();
	for (int i = 0; i < length; i++)
	{
		binary_wo::VarObj item = sheetIdxsVarObj.at(i);

		IDX sheetIdx = -1;
		if (item.has("objSheet"))
		{
			WebID sheetId = item.field_web_id("objSheet");
			sheetIdx = pCtx->getSheetIndex(sheetId);
		} 
		else 
		{
			sheetIdx = GetSheetIdx(pBook, item);
		}
		if (!wo::util::IsValidSheetIdx(pBook, sheetIdx))
		{
			ks_wstring& err = splitBookCallBack->GetErrStr();
			err = __X("SplitBook_SheetNotFoundError");
			WOLOG_INFO << "[SplitBookOpt::splitOptBook] invalid sheetidx " << sheetIdx;
			return E_FAIL;
		}

		// 当前sheet开启了保护 不允许拆分
		if (IsProtected(wwb, sheetIdx) && !splitBookCallBack->isSelectionDeliver())
		{
			ks_wstring& err = splitBookCallBack->GetErrStr();
			err = __X("SplitBook_SheetProtection");
			WOLOG_INFO << "[SplitBookOpt::splitOptBook] IsProtected ";
			return E_FAIL;
		} 
		else if (splitBookCallBack->isSelectionDeliver()) 
		{
			//检查一下复制的range是否隐藏
			RANGE range(*splitBookCallBack->getRange());
			if (pCtx->getProtectionCtx()->isRangeHasHidden(range))
			{
				ks_wstring& err = splitBookCallBack->GetErrStr();
				err = __X("SplitBook_NoAllVisiblesPermission");
				WOLOG_INFO << "[SplitBookOpt::splitOptBook] IsProtected ";
				return E_FAIL;
			}
		}


		ks_wstring sheetName = item.field_str("sheetName");

		SplitSheetToNewBookItem sheetItem(sheetIdx, sheetName);

		sheetIdxsVec.push_back(sheetItem);
	}
	SplitBookOpt splitBookOpt(wwb, pCtx, &param);
	HRESULT hr = S_OK;
	if (FAILED(hr = splitBookOpt.InitData(-1)))
	{
		WOLOG_INFO << "[SplitBookOpt::splitOptBook] splitBookOpt.InitData(-1) FAIL hr:" << hr;
		return hr;
	}

	splitBookOpt.handleFormat(param);

	return splitBookOpt.SplitNewBook(sheetIdxsVec, splitBookCallBack, path);


}

// 调用者自己保证sheetIDx的正确
bool SplitBookOpt::IsProtected(wo::KEtWorkbook* wwb ,IDX sheetIdx)
{
	IKWorksheet* pWorksheet = wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	ks_castptr<_Worksheet> pApiWs = pWorksheet;
	if (!pApiWs)
	{
		return false;
	}

	ks_stdptr<ISheetProtection> res = pApiWs->GetProtection();
	if (!res)
	{
		return false;
	}

	return res->IsProtected();
}


/**
 * param: 前端传过来的参数
 * bToSheet: 是否拆分到sheet
 * path: 拆分到book的路径 bToSheet为false时无用到
 * splitBookCallBack: 拆分到book的对应的数据返回
 */
HRESULT SplitBookOpt::splitOpt(binary_wo::VarObj& param, bool bToSheet, const ks_wstring& path, SplitBookCallBack* splitBookCallBack, wo::KEtWorkbook* wwb, KEtRevisionContext* pCtx, SplitResultContext* resultContext)
{

	IBook *pBook = wwb->GetCoreWorkbook()->GetBook();

	IDX sheetIdx = -1;
	// 注意  拆分到sheet这种操作，别能从objSheet拿，涉及到回放
	if (!bToSheet && param.has("objSheet"))
	{
		WebID sheetId = param.field_web_id("objSheet");
		sheetIdx = pCtx->getSheetIndex(sheetId);
	} 
	else 
	{
		sheetIdx = GetSheetIdx(pBook, param);
	}

	if (!wo::util::IsValidSheetIdx(pBook, sheetIdx))
	{
		ks_wstring& err = resultContext->GetErrStr();
		err = __X("SplitBook_SheetNotFoundError");
		WOLOG_INFO << "[SplitBookOpt::splitOpt] invalid sheetidx " << sheetIdx;
		return E_FAIL;
	}

	// 当前sheet开启了保护 不允许拆分
	if (IsProtected(wwb, sheetIdx))
	{
		ks_wstring& err = resultContext->GetErrStr();
		err = __X("SplitBook_SheetProtection");
		WOLOG_INFO << "[SplitBookOpt::splitOpt] IsProtected ";
		return E_FAIL;
	}

	RANGE rg = ReadRangeInl(wwb->GetBMP(), sheetIdx, param);
	if (!rg.IsValid())
	{
		WOLOG_INFO << "[SplitBookOpt::splitOpt] !rg.IsValid() ";
		return WO_FAIL;
	}

	WOLOG_INFO << "splitOpt rg:" << rg;

	if (!bToSheet && splitBookCallBack == nullptr)
	{
		WOLOG_INFO << "[SplitBookOpt::splitOpt] !bToSheet && splitBookCallBack == nullptr";
		return WO_FAIL;
	}

	// 选择第几个
	int selectIndex = 0; // 从前端数据来
	if (param.has("selectIndex"))
	{
		selectIndex = param.field_int32("selectIndex");
	}

	// 时间的选择下标
	int dataTypeIndex = -1; // 数据从前端来
	if (param.has("dataTypeIndex"))
	{
		dataTypeIndex = param.field_int32("dataTypeIndex");
	}

	// 包含标题是否选中
	bool bIncludeTitleCheck = false;// 这个的初始值，也需要从前端读取而来
	if (param.has("checkTitle"))
	{
		bIncludeTitleCheck = param.field_bool("checkTitle");
	}

	//拆分成新book前，将二维码转单元格图片
	if(!bToSheet)
	{
		util::QRLabelHelper qrLabelHelper(wwb->GetCoreWorkbook(), pCtx, util::QRLabelHelper::UseType_SplitToNewBook, nullptr);
		qrLabelHelper.ConvertQRLabel2CellImg(sheetIdx);
	}

	if (splitBookCallBack)
	{
		if (param.has("emptySheetDefaultName"))
		{
			ks_wstring emptySheetDefaultName = param.field_str("emptySheetDefaultName");
			splitBookCallBack->SetEmptySheetDefaultName(emptySheetDefaultName);
		}
		else 
		{
			splitBookCallBack->SetEmptySheetDefaultName(__X("empty"));
		}
	}

	HRESULT hr = S_OK;

	wo::SplitBookOpt splitBookOpt(wwb, pCtx, &param);

	hr = splitBookOpt.InitData(sheetIdx);
	if (FAILED(hr))
	{
		WOLOG_INFO << "[SplitBookOpt::splitOpt] splitBookOpt.InitData(sheetIdx) fail ";
		return hr;
	}

	// 处理文档后缀
	splitBookOpt.handleFormat(param);

	ks_stdptr<Range> splitRange = wwb->CreateRangeObj(rg);
	range_helper::ranges rgsTbl;
	app_helper::GetIRanges(splitRange, &rgsTbl);

	bool sp_bStartEnabled = false;
	bool sp_bIsSameBook = false;
	bool sp_cbIncludeTitleEnabled = false;

	bool bHasSelectRange = true;

	if (!splitBookOpt.initRect(rgsTbl, true, bHasSelectRange, selectIndex,
		&sp_bStartEnabled, &sp_bIsSameBook, &sp_cbIncludeTitleEnabled, &bIncludeTitleCheck, resultContext->GetErrStr()))
	{
		WOLOG_INFO << "[SplitBookOpt::splitOpt] splitBookOpt.initRect fail ";
		return WO_FAIL;
	}

	// 协同的场景 出现这种情况的概率挺大的
	if (!sp_bStartEnabled)
	{
		WOLOG_INFO << "[SplitBookOpt::splitOpt] !sp_bStartEnabled ";
		return WO_FAIL;
	}

	// 是否是年月日
	bool bDateType = splitBookOpt.isDateTypeField(selectIndex, false);
	splitBookOpt.SetDateType(bDateType);

	return splitBookOpt.split(dataTypeIndex, bToSheet, path, resultContext, splitBookCallBack);
}

bool SplitBookOpt::isCanSplit(IBook* pBook, IDX sheetIdX)
{
	SHEETTYPE st = stUnknown;
	
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdX, &spSheet);
	spSheet->GetType(&st);
	if (st == stChart || st == stDialog || st == stMacro)
		return false;

	if (!spSheet->IsGridSheet())
	{
		return false;
	}

	return true;
}

/**
 * 根据sheet去拆分
 */
HRESULT SplitBookOpt::SplitNewBook(const std::vector<SplitSheetToNewBookItem>& sheetVec, SplitBookCallBack* splitBookCallBack, const ks_wstring& path)
{

	if (splitBookCallBack == nullptr)
	{
		WOLOG_INFO << "SplitNewBook splitBookCallBack == null";
		return WO_FAIL;
	}

	HRESULT hr = S_OK;

	IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();

	ks_wstring& err = splitBookCallBack->GetErrStr();

	IEtProtectionCtx* iEtProtectionCtx = m_ctx->getProtectionCtx();

	// 检查一下权限
	for (int i = 0; i < sheetVec.size(); i++)
	{
		SplitSheetToNewBookItem item = sheetVec.at(i);
		IDX sheetIdx = item.m_sheetIdx;
		// 检查一下sheetId
		if (!wo::util::IsValidSheetIdx(pBook, sheetIdx))
		{
			err = __X("SplitBook_SheetNotFoundError");
			WOLOG_INFO << "[SplitBookOpt::SplitNewBook] invalid sheetidx " << sheetIdx;
			return E_FAIL;
		}

		// 当前sheet开启了保护 不允许拆分
		if (IsProtected(m_wwb, sheetIdx) && !splitBookCallBack->isSelectionDeliver())
		{
			ks_wstring& err = splitBookCallBack->GetErrStr();
			err = __X("SplitBook_SheetProtection");
			WOLOG_INFO << "[SplitBookOpt::splitOptBook] IsProtected ";
			return E_FAIL;
		}
		else if (splitBookCallBack->isSelectionDeliver()) 
		{
			//检查一下复制的range是否隐藏
			RANGE range(*splitBookCallBack->getRange());
			if (iEtProtectionCtx->isRangeHasHidden(range))
			{
				err = __X("SplitBook_NoAllVisiblesPermission");
				return WO_NO_ALL_VISIBLES_PERMISSION;
			}
		}

		// 判断当前sheet是否对于用户全部可见
		if (IsProtected(m_wwb, sheetIdx) && !splitBookCallBack->isSelectionDeliver()) 
		{
			RANGE range(pBook->GetBMP());
			range.SetSheets(sheetIdx, sheetIdx);
			if (iEtProtectionCtx->isRangeHasHidden(range))
			{
				err = __X("SplitBook_NoAllVisiblesPermission");
				return WO_NO_ALL_VISIBLES_PERMISSION;
			}
			if (!isCanSplit(pBook, sheetIdx))
			{
				err = __X("SplitBook_NotSupprtSheetType");
				return E_FAIL;
			}
		}

		//拆分成新表，需要将二维码转单元格图片
		util::QRLabelHelper qrLabelHelper(m_wwb->GetCoreWorkbook(), m_ctx, util::QRLabelHelper::UseType_SplitToNewBook, nullptr);
		qrLabelHelper.ConvertQRLabel2CellImg(sheetIdx);	
	}


	if (splitBookCallBack->isDistribute() && !splitBookCallBack->isSelectionDeliver() && sheetVec.size() > 1)
	{
		int sheetSize = sheetVec.size();
		int realSize = 0;
		IKWorksheet* worksheets[sheetSize] ;
		std::vector<QString> names;
		for (int i = 0; i < sheetVec.size(); i++)
		{
			SplitSheetToNewBookItem item = sheetVec.at(i);
			IDX sheetIdx = item.m_sheetIdx;

			// 单元格图片的sheet
			if (0 == xstrcmp(item.m_sheetName.c_str(), STR_CELL_IMAGE_SHEET_NAME))
			{
				continue;
			}
			realSize++;
			IKWorksheet* pIKWorksheet = m_spWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
			worksheets[i] = pIKWorksheet;
			names.push_back(krt::fromUtf16(item.m_sheetName.c_str()));
		}
		m_spSheetSpliter->Init(worksheets, realSize);
		bool bNeedMsgErrDlg = true;
		hr = m_spSheetSpliter->SplitNewBook(0, names, path.c_str(), bNeedMsgErrDlg, splitBookCallBack);

		if (FAILED(hr))
		{
			if (E_ABORT == hr || !bNeedMsgErrDlg)
			{
				err = __X("SplitBook_SplitAbort");
				return hr;
			}

			IKApplication* pApp = kxApp->coreApplication();
			if (!pApp && pApp->GetActiveDocument())
			{
				_DocumentEx* pDocumentEx = pApp->GetActiveDocument()->GetDocumentEx();
				if (!pDocumentEx || pDocumentEx->IsSecurityDoc())
				{
					err = __X("SplitBook_SecurityDoc");
					return hr;
				}
			}
			err = __X("SplitBook_Other");
		}	
	}
	else
	{
		for (int i = 0; i < sheetVec.size(); i++)
		{
			SplitSheetToNewBookItem item = sheetVec.at(i);
			IDX sheetIdx = item.m_sheetIdx;

			// 单元格图片的sheet
			if (0 == xstrcmp(item.m_sheetName.c_str(), STR_CELL_IMAGE_SHEET_NAME))
			{
				continue;
			}
			const bool bKsheet = m_wwb->GetCoreWorkbook()->GetBook()->GetBMP()->bKsheet;
			if (splitBookCallBack->isDistribute() && splitBookCallBack->isSelectionDeliver())
			{
				//添加一个sheet
				KComVariant vBefore;
				KComVariant vAfter;
				ks_stdptr<IKCoreObject> spObj;
				SHEETTYPE initSheetType = stGrid;
				KComVariant vType;
				KComVariant vCount = 1;
				vType.Assign(xlWorksheet);
				vAfter.Assign(util::getLastSheetIdx(pBook) + 1);

				ks_stdptr<etoldapi::Worksheets> spSheets;
        		m_wwb->GetCoreWorkbook()->get_Worksheets(&spSheets);
				hr = spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, initSheetType);
				if (FAILED(hr))
				{
					err = __X("SplitBook_NoAllVisiblesPermission");
					return WO_NO_ALL_VISIBLES_PERMISSION;
				}

				ks_stdptr<_Worksheet> spWorkSheet = spObj;
				if (bKsheet)
				{
					ISheet* pSheet = spWorkSheet->GetSheet();
					pSheet->SetRowTopPadding(30);
					pSheet->SetRowBottomPadding(30);
					pSheet->SetColLeftPadding(15);
					pSheet->SetColRightPadding(15);

					IDX sheetIdx = alg::STREF_INV_SHEET;
					pSheet->GetIndex(&sheetIdx);
					RANGE sheetRg(pSheet->GetBMP());
					sheetRg.SetSheets(sheetIdx, sheetIdx);

					IBook* pBook = pSheet->LeakBook();
					IBookOp *pBookOp = pBook->LeakOperator();
					HRESULT hr = pBookOp->FitRowCol(&sheetRg, FALSE, FALSE, FALSE, FALSE, FALSE);
					pSheet->SetDefColWidth(1530);
				}
				m_spSheetSpliter->Init(spWorkSheet);
			}
			else 
			{
				IKWorksheet* pIKWorksheet = m_spWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
				m_spSheetSpliter->Init(pIKWorksheet);
			}
			

			bool bNeedMsgErrDlg = true;
			hr = m_spSheetSpliter->SplitNewBook(0, krt::fromUtf16(item.m_sheetName.c_str()), path.c_str(), bNeedMsgErrDlg, splitBookCallBack);
			if (splitBookCallBack->isDistribute() && splitBookCallBack->isSelectionDeliver())
			{
				//delete sheet
				ks_stdptr<etoldapi::Worksheets> spSheets;
        		m_wwb->GetCoreWorkbook()->get_Worksheets(&spSheets);
				spSheets->GetSheetItem(util::getLastSheetIdx(pBook))->DeleteDirectly();
			}
			if (FAILED(hr))
			{
				if (E_ABORT == hr || !bNeedMsgErrDlg)
				{
					err = __X("SplitBook_SplitAbort");
					break;
				}

				IKApplication* pApp = kxApp->coreApplication();
				if (!pApp)
					break;

				IKDocument* pDoc = pApp->GetActiveDocument();
				if (!pDoc)
					break;

				_DocumentEx* pDocumentEx = pDoc->GetDocumentEx();
				if (!pDocumentEx || pDocumentEx->IsSecurityDoc())
				{
					err = __X("SplitBook_SecurityDoc");
					break;
				}

				err = __X("SplitBook_Other");
				break;
			}
		}
	}
	return hr;
}



}