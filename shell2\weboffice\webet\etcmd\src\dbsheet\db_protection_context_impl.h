
#ifndef __WEBET_DB_PROTECTION_CONTEXT_IMP_H__
#define __WEBET_DB_PROTECTION_CONTEXT_IMP_H__

#include "wo/et_revision_context.h"
#include "appcore/et_appcore_dbsheet.h"

namespace wo
{

class KEtWorkbook;
class IWorkbookObj;
class KEtRevisionContext;

class KDbProtectionContext : public IEtProtectionCtx
{
public:
    KDbProtectionContext(KEtWorkbook *workbook);
    virtual ~KDbProtectionContext();

    bool isAllHidden() override;
    bool isCellHidden(ISheet *pSheet, INT32 row, INT32 col) override;
    bool isRangeHasHidden(const RANGE &rg, bool ignoreShared = false) override;
    bool isRangeHasHidden(IKRanges *rgs, bool ignoreShared = false) override;
    bool isRangeHasHidden(ISheet *pSheet, const RANGE &rg, bool ignoreShared = false) override;
    void splitVisibleRange(ISheet *pSheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg) override;
    void splitHiddenRange(ISheet *pSheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges) override;
    bool isRangeAllHidden(const RANGE &rg) override;

    bool isBookHasHidden(IKWorkbook *workbook) override;
    bool isBookHasHidden(IBook *pBook) override;
    bool isBookHasHidden() override;

    bool isBookHasHiddenProperty(IBook *pBook) override;
    bool isBookHasHiddenProperty() override;
    bool isRangeHasHiddenProperty(const RANGE &rg, bool checkUnprotected = false) override;

    bool isBookHasReadonly() override;
    bool isBookHasReadonly(IBook *pBook) override;

    bool isBookHasReadonlyProperty() override;
    bool isBookHasReadonlyProperty(IBook *pBook) override;

    bool isSheetHidden(UINT stId) override;
    bool isSheetHasHidden(ISheet *pSheet) override;
    bool isSheetHasHiddenProperty(ISheet *pSheet) override;
    bool isSheetHasReadonly(ISheet *pSheet) override;
    bool isSheetHasReadonlyProperty(ISheet *pSheet) override;

    bool isSheetShared(UINT stId) override;

    bool isSheetHasEditable(ISheet *pSheet) override;
    bool isThisBookHasEditable() override;
    bool isBookHasSheetProtected() override;

    bool isCellImageFormula(const_token_vector vecToken) override;
    bool isCellImageFormula(PCWSTR) override;

    HRESULT checkAllowEdit(const RANGE &rg) override;
    HRESULT checkAllowEditWidthLockedCell(const RANGE &rg) override;

    // only allow edit for [allow edit range], not checking for locked cell
    bool isAllowEdit(const RANGE &rg, ProtectionAccessPerms *right) override;
    bool isAllowEdit(IKRanges *rgs, ProtectionAccessPerms *right) override;
    bool isAllowEdit(const std::vector<RANGE> &rg, ProtectionAccessPerms *right) override;

	  bool isAllowedFormula(PCWSTR formula) override;
    HRESULT checkFormula(IDX iSheet, ROW row, COL col, const_token_vector vecToken) override;
    HRESULT checkFormula(const RANGE &rg, const_token_vector vecToken) override;
    HRESULT checkFormula(IDX iSheet, ROW row, COL col, PCWSTR) override;
    HRESULT checkFormula(const RANGE &rg, PCWSTR) override;
    HRESULT checkFormula(IDX iSheet, ROW row, COL col, const_token_ptr) override;

    HRESULT checkDVFormula(const RANGE &rg, const VALIDATION *validation) override;
    HRESULT checkDVFormula(const RANGE &rg, DVValueType dvType, PCWSTR pFormula1, PCWSTR pFormula2) override;
    HRESULT checkDVFormula(const RANGE &rg, DVValueType dvType, ITokenVectorInstant *pFormula1, ITokenVectorInstant *pFormula2) override;
    HRESULT checkDVFormulaInRange(const RANGE &rg) override;

    HRESULT checkCFFormula(const RANGE &appliedRg, ICondFmt *condFmt) override;
    HRESULT checkCFFormulaInRange(const RANGE &rg) override;

    HRESULT checkProtectionContent(const RANGE &rg, uint32_t *pckKind = nullptr) override;
    HRESULT checkProtectionCopyContent(const RANGE &rg) override;
    HRESULT isOperationAllowedProtected(const RANGE &rg, et_appcore::ActionType acType) override;
    uint32_t getProtectionContentKind(const RANGE &rg, bool isAll = false) override;
    

    ProtectionAccessPerms getAccessPermWidthRange(const RANGE &rg);// override;
    bool isProtectionAreaUnlock(LPCWSTR id) override;
    void clearProtectionCache() override;

    void setValidRefKind(uint32_t k) override;

    bool isCompileFormula() const override;
    void setCompileFormula(bool) override;

    uint32_t getPasteContentKind() const override;
    void setPasteContentKind(uint32_t kind) override;

    bool allowDeleteLockedRowCol() const override;
    void setAllowDeleteLockedRowCol(bool isAllowed) override;

    int currentCommand() const override;
    void setCurrentCommand(int cmd) override;
    bool getAutoPassword(PCWSTR srcUUID, ks_wstring *outPwd) override;

    void setHasProtectionCache(bool bl) override;
    void setupProtectionCache(ISheet *spSheet) override;

    void clearEtContext() override;
    void setupEtContext() override;

    bool needCheckInsertDeleteHiddenRange(const RANGE& rg) override;
    bool canRefColProtect(const RANGE& rg) override;
    bool hasHiddenColProtect(ISheet* spSheet, const RANGE& rg) override;
    bool isBookHasColProtect() override;

    void splitVisibleRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg) override;
    void splitHiddenRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges) override;
    void onUserColPermsChanged(LPCWSTR userId) override;
	void updateUserColPermsStatus(LPCWSTR userId) override;
    void markAllUsersColPermsChanged(LPCWSTR excludeId) override;
    bool isAllUsersColPermsChanged() override;
    bool isConnSharedLink() override;

    ProtectionType getPasteProtectionType() const override;
    void setPasteProtectionType(ProtectionType type) override;
    ISharedLink* GetSharedLink();
    bool isSheetLinkShare(UINT stId);

	bool isUserRangeInvisible(const RANGE &rg) override { return false; }
	bool isCanReadWholeBook() override { return false; }
	bool isCanEditWholeBook() override { return false; }

public:
    ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
    IBook* m_pBook;
};

} // namespace wo

#endif // __WEBET_DB_PROTECTION_CONTEXT_IMP_H__