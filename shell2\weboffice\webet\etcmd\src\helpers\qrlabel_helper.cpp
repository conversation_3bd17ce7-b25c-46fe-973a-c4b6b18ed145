﻿#include "qrlabel_helper.h"
#include "et_revision_context_impl.h"

namespace wo {

bool qrlabel_helper::IsGenQRLabelFormula(IFormula* pFormula)
{
	BOOL isFormula = FALSE;
	ks_stdptr<ITokenVectorInstant> spTokenVector;
	pFormula->GetContent(&isFormula, &spTokenVector, nullptr);
	if (!spTokenVector || !isFormula)
		return false;

	exec_token_vector etv(spTokenVector.get());
	if (etv.size() < 3)
		return false;

	const_token_ptr func = etv.get(etv.size() - 1);
	if (!alg::const_function_token_assist::is_type(func))
		return false;

	return alg::const_function_token_assist(func).get_id() == FNID_GENQRLABEL;
}

std::tuple<UINT, UINT, bool> qrlabel_helper::QueryQRLabelCount(KEtRevisionContext* ctx, const RANGE& range, ISheet* pSheet)
{
	class QRLabelCountAcpt : public ICellValueAcpt
	{
	public:
		QRLabelCountAcpt(KEtRevisionContext* ctx, ISheet* pSheet)
			: m_ctx(ctx), m_pSheet(pSheet)
		{
			VS(m_pSheet->GetCurUserOperator(&m_spRowColOp));
			IBook* pBook = nullptr;
			m_pSheet->GetBook(&pBook);
			VS(pBook->GetOperator(&m_pBookOp));
			VS(m_pSheet->GetIndex(&m_sheetIdx));
		}

		bool IsValidQRLabel(const_token_ptr pToken)
		{
			alg::const_handle_token_assist chta(pToken);
			DWORD handleType = chta.get_handleType();
			if (handleType != alg::ET_HANDLE_QRLABEL)
				return false;

			ks_stdptr<IQRLabelHandle> spTokenQRLabel = chta.get_handle()->CastUnknown();
			ASSERT(spTokenQRLabel);

			PCWSTR key = spTokenQRLabel->GetKey();
			return key && *key;
		}

		UINT GetTotalQRLabelCount() const
		{
			return m_totalQRLabelCount;
		}
		UINT GetVisibleQRLabelCount() const
		{
			return m_visibleQRLabelCount;
		}
		bool HasUncalculatedCell() const
		{
			return m_hasUncalculatedCell;
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if (!m_hasUncalculatedCell)
			{
				DWORD majorType = alg::const_token_assist(pToken).major_type();
				if ((majorType == alg::ETP_VDBL && alg::const_vdbl_token_assist(pToken).get_value() == 0.0)
					|| majorType == alg::ETP_CELLINT)
				{
					ks_stdptr<IFormula> spFormula;
					VS(m_pBookOp->GetCellFormula(m_sheetIdx, row, col, &spFormula, nullptr));
					if (spFormula && IsGenQRLabelFormula(spFormula))
						m_hasUncalculatedCell = true;
				}
			}

			if (!alg::const_handle_token_assist::is_type(pToken))
				return 0;
			
			if (!IsValidQRLabel(pToken))
				return 0;

			if (m_ctx->getProtectionCtx()->isCellHidden(m_pSheet, row, col))
				return 0;

			if (!m_spRowColOp->GetRowHidden(row) && !m_spRowColOp->GetColHidden(col))
				m_visibleQRLabelCount++;
			m_totalQRLabelCount++;
			return 0;
		}

	private:
		UINT m_totalQRLabelCount = 0;
		UINT m_visibleQRLabelCount = 0;
		bool m_hasUncalculatedCell = false;
		KEtRevisionContext* m_ctx;
		ISheet* m_pSheet;
		IDX m_sheetIdx = INVALIDIDX;
		IBookOp* m_pBookOp;
		ks_stdptr<IRowColOp> m_spRowColOp;
	};

	et_sdptr<ISheetEnum> spSheetEnum;
    pSheet->CreateEnum(&spSheetEnum);
    QRLabelCountAcpt qrLabelCountAcpt(ctx, pSheet);
    spSheetEnum->EnumCellValue(range, &qrLabelCountAcpt);
    return std::tuple<UINT, UINT, bool>(qrLabelCountAcpt.GetTotalQRLabelCount(),
                                        qrLabelCountAcpt.GetVisibleQRLabelCount(),
                                        qrLabelCountAcpt.HasUncalculatedCell());
}

} // end namespace wo