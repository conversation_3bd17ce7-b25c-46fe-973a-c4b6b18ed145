﻿#include "etstdafx.h"
#include "workbook2et_importer.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/protection_helper.h"
#include "utils/et_gridsheet_utils.h"
#include "utils/qrlabel_utils.h"
#include "webextension_context_restorer.h"

namespace wo
{
Workbook2EtImporter::Workbook2EtImporter(KEtRevisionContext* pCtx, etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook,
                                         PCWSTR userId, binary_wo::VarObj& param)
        : WorkbookImporter(pSrcWorkbook, pTarWorkbook, userId, param), m_exporter(pSrcWorkbook, pTarWorkbook, pCtx)
        , m_pCtx(pCtx)
{
}

HRESULT Workbook2EtImporter::copySheet(etoldapi::_Worksheet* pSrcWorkSheet, VARIANT before,
                                       VARIANT after, IKCoreObject** ppNewSheetObj)
{
	ISheet* pSrcSheet = pSrcWorkSheet->GetSheet();
    if (pSrcSheet->IsDbSheet())
    {
        // 源sheet为dbsheet时不复制sheet而是新建sheet，数据在后面写入
        KComVariant vCount, vType;
        vType.Assign(xlWorksheet);
        ks_stdptr<etoldapi::Worksheets> spTarWorkSheets = m_pTarWorkbook->GetWorksheets();
        if (!spTarWorkSheets)
            return E_FAIL;

        HRESULT hr = spTarWorkSheets->Add(before, after, vCount, vType, ppNewSheetObj, stGrid);
        if (FAILED(hr))
            return hr;

        ks_stdptr<etoldapi::_Worksheet> spNewWorksheet = *ppNewSheetObj;
        if (!spNewWorksheet)
            return E_FAIL;
		PCWSTR name = nullptr;
		pSrcSheet->GetName(&name);
		ks_bstr sheetName;
		hr = GetValidSheetName(spTarWorkSheets, spNewWorksheet, name, &sheetName);
		if (FAILED(hr))
			return hr;
		return spNewWorksheet->put_Name(sheetName);
    }
	return pSrcWorkSheet->Copy(before, after, ppNewSheetObj);
}

HRESULT Workbook2EtImporter::onBeforeImport()
{
    m_sheetIdMap.clear();
    return m_exporter.Init();
}

HRESULT Workbook2EtImporter::onAfterImportOneSheet(etoldapi::_Worksheet* pSrcWorkSheet,
                                                   etoldapi::_Worksheet* pNewWorkSheet)
{
    // 非DB要收集附件
    if (!pNewWorkSheet->GetSheet()->IsDbSheet())
    {
        wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext();
        if (ctx)
        {
            ctx->addAttachmentAnnexIdBySheet(pNewWorkSheet->GetSheet());
        }
    }

    ISheet* pSrcSheet = pSrcWorkSheet->GetSheet();
    ISheet* pNewSheet = pNewWorkSheet->GetSheet();
    m_sheetIdMap[pSrcSheet->GetStId()] = pNewSheet->GetStId();
    if (pSrcSheet->GetBMP()->bKsheet)
    {
        clearProtection(pNewWorkSheet);
        IDX sheetIdx;
        pNewSheet->GetIndex(&sheetIdx);

        util::QRLabelHelper qrLabelHelper(m_pTarWorkbook, m_pCtx, util::QRLabelHelper::UseType_Import_KSheet2Et, &m_param);
        qrLabelHelper.ConvertQRLabel2CellImg(sheetIdx);
    }

    if (pSrcSheet->IsDbSheet())
    {
        const WCHAR* pSrcSheetName = nullptr;
        pSrcSheet->GetName(&pSrcSheetName);
        // dbsheet复制后的sheetType为stGrid_DB，需调整为stGrid，否则前端会显示旧版dbsheet页面
        pNewSheet->UpdateSheetType(stGrid);
        IDX newSheetIdx;
        VS(pNewSheet->GetIndex(&newSheetIdx));
        ks_stdptr<IDBSheetOp> spSrcSheetOp;
        VS(DbSheet::GetDBSheetOp(pSrcSheet, &spSrcSheetOp));
        return m_exporter.dbViewExportData(newSheetIdx, pSrcSheetName,
                                           spSrcSheetOp->GetAllRecords(),
                                           spSrcSheetOp->GetAllFields(), spSrcSheetOp, nullptr);
    }
    return S_OK;
}

HRESULT Workbook2EtImporter::onAfterImport(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                                               const std::vector<etoldapi::_Worksheet*>& newWorkSheets)
{
    WebExtensionContextRestorer contextRestorer(m_pSrcWorkbook, m_pTarWorkbook, m_sheetIdMap);
    for (int i = 0; i < srcWorkSheets.size(); i++)
    {
        etoldapi::_Worksheet* pSrcWorksheet = srcWorkSheets[i];
        if (!pSrcWorksheet || !pSrcWorksheet->GetSheet()->IsDbDashBoardSheet())
            continue;

        etoldapi::_Worksheet* pNewWorkSheet = newWorkSheets[i];
        if (!pNewWorkSheet)
            continue;

        contextRestorer.RestoreSheet(pNewWorkSheet->GetSheet());
    }
    return S_OK;
}

} // namespace wo
