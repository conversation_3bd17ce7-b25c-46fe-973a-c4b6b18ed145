﻿/* -------------------------------------------------------------------------
//	文件名		：	autofilteritemshelper.h
//	创建者		：	陈嘉丰
//	创建时间	：	2018-4-22
//	功能描述	：
//
// -----------------------------------------------------------------------*/

#ifndef __AUTO_FILTER_ITEMS_HELPER_H__
#define __AUTO_FILTER_ITEMS_HELPER_H__

#include "etstdafx.h"
#include "appcore/et_appcore_et_filter.h"

#define PAGE_SIZE 100

namespace binary_wo
{
    class BinWriter;
}

namespace wo
{
class KEtWorkbook;

class AutofilterOperatorSerializeHelper
{
public:
    AutofilterOperatorSerializeHelper(IKAutoFilter *pAutoFilter, int nField);
    void Serialize(ISerialAcceptor* acpt);
private:
    IKAutoFilter* m_pAutoFilter;
    int m_nField;
};

class AutoFilterItemsHelper
{
	enum SearchType
	{
		all,
		year,
		month,
		day,
		hour,
		minute,
		second
	};

    enum SearchMode
    {
        OR,         // 搜索 包含任意关键字的内容
        AND,        // 搜索 同时包含所有关键字的内容
        WHOLE,      // 搜索 包行整个关键字的内容
    };


    enum PageType
    {
        NORMAL,     // 正常查询
        SEARCH      // for search
    };

public:
	AutoFilterItemsHelper(IKAutoFilter*, IAutoFilterValues* pValues, ValuesNode* pValuesRoot, 
        const binary_wo::VarObj& param, KEtWorkbook* workbook, int nFieldIdx);

	void Serialize(ISerialAcceptor* acpt);
    void SetInverse();
    HRESULT SetAutoFilterNodeStatus(const binary_wo::VarObj& values, ValuesNode* pValuesRoot);
    void SelectInvertItems(ValuesNode* pRoot);
    void setResort(bool sort);
    bool getResort();
    void setSortParam(const SortParam& sortParam);
    SortParam sortParam();
    int pageOffset() { return m_nPageOffset;}
    bool emptySearch();

private:
	static bool hasWildChar(PCWSTR matchText);
	static WCHAR toUpperFast(WCHAR ch);
	void static toUpperStr(LPCWSTR lpwsz, LPWSTR lpwszDest);
	bool static matchStrI(LPCWSTR lpFirst, LPCWSTR lpSrch);
	bool static findWithWildChar(PCWSTR matchText, PCWSTR nodeText);
	bool GetDateParam(const QString& qStr, DateMatchParam& dateParam);
	int matchYMDHMM(
		ValuesNode* pNode,
		bool bLastState,
		SearchType nType,
        SearchMode nMode,
		int nFloor,
		std::vector<ks_wstring> matchWords);

	int matchYMDHMM(
	ValuesNode* pNode,
	bool bLastState,
	SearchType nType,
	int nFloor,
	int nNodeIdx,
	int nParentIdx,
	LPCWSTR szText,
	bool hasWild,
	bool bPinyinSearch,
	DateMatchParam* pDateMatchParam);

	int matchYMDHMM(ValuesNode* pNode, std::stack<ValuesNode*>& trace,
	bool lastState,
	SearchType nType,
	int nFloor,
	int nNodeIdx,
	int nParentIdx,
	LPCWSTR szText,
	bool hasWild,
	bool bPinyinSearch, ET_FilterType mutiFilterType, ET_FilterType filterType, double dbval, int dateType);
	void matchItems();

	SearchType StrToSearchType(PCWSTR str);
    SearchMode StrToSearchMode(PCWSTR str);
    SortType StrToSortType(PCWSTR str);
    SortOrder StrToSortOrder(PCWSTR str);
    PageType StrToPageType(PCWSTR str);
    void parseSearchData(SearchMode type, PCWSTR str, std::vector<ks_wstring>& results);
	static PCWSTR getCheckType(BYTE type);
	static PCWSTR CustomTypeToStr(ET_CUSTOM_FILTER_TYPE ty);
    void SerializeRoot(ValuesNode* pNode, ISerialAcceptor* acpt);
	bool SerializeNode(ValuesNode* pNode, ISerialAcceptor* acpt, int level);

    void applyMatchItems();
    void applyReSort();
    void RecursiveSelect(ValuesNode* pNode, bool bSearch, bool bChecked);
    void RecursiveSelectPart(ValuesNode* pNode, bool bSearch);
    bool setNodeState(ValuesNode* pRoot, ValuesNode* pCurrNode, int nCheckType);
    bool findHMSNode(const binary_wo::VarObj& item, ValuesNode* pCurrNode, ValuesNode** ppFind);
    void setNodeChecked(ValuesNode* pSearchAdd, ValuesNode* pCurrNode, int nCheckType);
    void transSetChildNode(QList<ValuesNode*>& nodeQueue, ValuesNode* pSearchAdd, int nCheckType);
    void transSetParentNode(ValuesNode* pRoot, ValuesNode* pCurrNode, ValuesNode* pSearchAdd);
private:
	static constexpr byte ITEM_VISIBLE = 0x4;
	static constexpr byte SEARCH_CHILD_CHECK = 0x1;
	static constexpr byte SEARCH_LAST_STATE	= 0x2;
	static constexpr byte SEARCH_COMPARE_MATCH = 0x4;
	static constexpr byte QT_CHECK_STATE = 0x3;
	static constexpr byte ITEM_CHECK_UNKNOWN = 0x7;

	ValuesNode* m_pRoot = nullptr;
	int m_nInvalidNodeCount = 0;
	int m_serializeNodeCnt = 0;
    std::vector<ks_wstring> m_searchDatas;
	QString m_oriSearchDatas;
	ET_FilterType m_wildfilterType;//通配符配置
	SearchType m_nSearchType;
    SearchMode m_nSearchMode;
    SortType m_nSortType;
    SortOrder m_nSortOrder;
	IAutoFilterValues* m_pValues = nullptr;

    bool m_bApplyMatch = false; // 是否需要应用匹配查找  search
    bool m_bApplySwitchPined = false; //切换 置顶勾选 开关
    bool m_bSort = false;

    // 分页
    bool m_bUsePage = false;    // 是否启用分页
    int m_nPageSize = PAGE_SIZE;    // 分页大小
    int m_nPageOffset = 0;  // 分页位置
    PageType m_nPageType = NORMAL;//搜索状态
	ks_stdptr<IKEtFilterStringMatch> m_spEtFilterStringMatch = nullptr;
	KEtWorkbook* m_wwb = nullptr;
	bool m_bEnablePinyin = true;
    bool m_bInverse = false;
    SortParam m_sortParam;
    IKAutoFilter* m_pAutoFilter = nullptr;
    int m_nFieldIdx = -1;
};

class AutoFilterFormatItemsHelper
{

public:
	AutoFilterFormatItemsHelper(IAutoFilterFormatItems* pFmtItems, IBook* pBook, IKAutoFilter *pAutoFilter, int nField);
	void Serialize(ISerialAcceptor* acpt);
	EtFill* getFill() {return &m_sortFill;};
	EtColor* getFontColor() {return &m_sortColor;};
	EtIconSet* getIconSet() {return &m_sortIconSet;}
	SortBy getSortBy() {return m_sortBy;}
	IBook* getBook(){return m_pBook;};

private:
	void serializeCellColorItems(IFilterCellColorItems*, ISerialAcceptor* acpt);
	void serializeFontColorItems(IFilterFontColorItems*, ISerialAcceptor* acpt);
	void serializeIconItems(IFilterIconItems*, ISerialAcceptor* acpt);

private:
	IAutoFilterFormatItems* m_pFmtItems;
	IBook* m_pBook;
	IKAutoFilter* m_pAutoFilter = nullptr;
	EtFill m_sortFill;
	EtColor m_sortColor;
	EtIconSet m_sortIconSet;
	int m_nField = -1;
	SortBy m_sortBy = SORT_NONE;
};

}

#endif //__AUTO_FILTER_ITEMS_HELPER_H__
