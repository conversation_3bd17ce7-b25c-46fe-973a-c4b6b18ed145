﻿#include "etstdafx.h"
#include "wobatchtrans.h"
#include "et_revision_context_impl.h"
#include "workbook.h"
#include "memo_stat_common.h"

namespace wo 
{
KEtBatchTransHelper::KEtBatchTransHelper(KEtWorkbook* pWb) 
: m_pWb(pWb) 
, m_pBatchTask(nullptr)
, m_firstTaskInnerID(USER_INNER_INV_ID)
, m_isBatch(false)
, m_nTransLevel(0)
, m_skipNextConfirm(false)
{
}

void KEtBatchTransHelper::beginTransact(WebInt version)
{
	if (m_isBatch)
		return;
	
	m_pWb->baseBeginTransact(version);
	ASSERT(m_nTransLevel >= 0);
	++m_nTransLevel;
    m_skipNextConfirm = false;
}

void KEtBatchTransHelper::endTransact(bool isRollBack, IRevisionContext* pCtx)
{
	if (m_isBatch || m_nTransLevel <= 0)
		return;

	m_pWb->baseEndTransact(isRollBack, pCtx);
	--m_nTransLevel;
	ASSERT(m_nTransLevel >= 0);
}

void KEtBatchTransHelper::appBeginUndoTrans()
{
	if (m_isBatch)
		return;
	
	m_undoTrans = std::make_unique<app_helper::KUndoTransaction>(m_pWb->GetCoreWorkbook(), nullptr, FALSE);
	ASSERT(m_nTransLevel >= 0);
	++m_nTransLevel;
}

void KEtBatchTransHelper::appEndUndoTrans(KEtRevisionContext * pCtx, HRESULT hr)
{
	if (m_isBatch || m_nTransLevel <= 0)
		return;
	
	wo::util::SlowCallTimeStat callTime("EndTrans", wo::util::kDefSlowCallThreshold, [this, pCtx](unsigned int ms) {
		this->m_pWb->coreMetric().addAppEndTransTime(ms);
		if (pCtx)
			WOLOG_INFO << "appEndTrans.serialComplexity: " << pCtx->getSerialComplexity();
	});
	
	m_undoTrans->EndTransWithHResult(hr, TRUE, FALSE);
	
	wo::util::SlowCallTimeStat viewUpdate("EndTransViewUpdate", wo::util::kDefSlowCallThreshold);
	app_helper::KViewBatchUpdate _Update(m_undoTrans->GetEntry(), ERM_SheetChanged);
	m_undoTrans.reset();
	--m_nTransLevel;
	ASSERT(m_nTransLevel >= 0);
}

/**
*  目前只有 http.et.ExecuteApi 在使用。
*  1. 回放不允许batch
*  2. user->innerID 和 batch开始时的innerID不一样, 结束batch
*  3. 当前IBatchTask 计算是否可以和前一个 IBatchTask 进行batch
*     3.1 no: 当前IBatchTask 不支持batch
*     3.2 keep: 可以和前一个 batch
*     3.3 restart: 不可以batch，得新起一个batch.
**/
void KEtBatchTransHelper::tryBeginBatch(KEtRevisionContext * pCtx, IBatchTask * pBatchTask, KwTask* pTask)
{
    if (pCtx->isExecDirect())
        return;
        
    WoBeginBatchTransFlag flag = WoBeginBatchTransFlag::no;
    if (pBatchTask)
        flag = pBatchTask->GetBeginBatchFlag(m_pBatchTask);

	PCWSTR versionTag = pTask->getVersionTag();
	if (flag == WoBeginBatchTransFlag::keep)
	{
		// 前后版本标记不一致： 1. 前空现有 restart；2. 前有现空 restart；3. 前空现空 keep；4. 前有现有 比较
		if ((!m_preVersionTag && versionTag) || (m_preVersionTag && !versionTag))
			flag = WoBeginBatchTransFlag::restart;
		else if (!m_preVersionTag && !versionTag)
			flag = WoBeginBatchTransFlag::keep;
		else
			flag = xstrcmp(m_preVersionTag, versionTag) == 0 ? WoBeginBatchTransFlag::keep : WoBeginBatchTransFlag::restart;
	}
	m_preVersionTag = versionTag;

    if (m_isBatch) 
    {
        IKUserConn* user = pCtx->getUser();
        if (user->innerID() != m_firstTaskInnerID && 
            (!pBatchTask || !pBatchTask->isAllowMultiUserConns())) // diff user
            endAndConfirm(pCtx);
        if (flag == WoBeginBatchTransFlag::no || flag == WoBeginBatchTransFlag::restart)
        { // stop and confirm
            endAndConfirm(pCtx);
        }
        
        if (!isInTrans())
        {
            beginTransact(m_pWb->getVersionMgr()->getCurTaskVersionID() + 1);
            appBeginUndoTrans();
        }
    }

    if (flag == WoBeginBatchTransFlag::keep || flag == WoBeginBatchTransFlag::restart)
    { // start
        ASSERT((flag == WoBeginBatchTransFlag::restart && !m_isBatch)
            || (flag == WoBeginBatchTransFlag::keep && m_isBatch));
        if (!m_isBatch)
        {
            if (!isInTrans())
            {
                beginTransact(m_pWb->getVersionMgr()->getCurTaskVersionID() + 1);
                appBeginUndoTrans();
            }
            IKUserConn* user = pCtx->getUser();
            setBatchStatus(pBatchTask, user->innerID());
        }
        m_pBatchTask = pBatchTask;
    }

    if (WoBeginBatchTransFlag::keep != flag && pBatchTask)
        pBatchTask->SetJSContextChange(true);
}

/**
* 场景：batchTask结束或者临时结束(命令数 > 1000)
* 1. 失败时，已有逻辑是：1. task 删除失败commond，保留成功的；2. 原事务回滚；3. 添加空事务； 4. version banUndo
*    batch来说comman都不留了。
* 2. 成功时
*    2.1 如果需要stop。则结束并确认版本。
*    2.2 删除command。
*/
void KEtBatchTransHelper::tryEndBatch(KEtRevisionContext * pCtx, KwTask* task, HRESULT hr, bool * pIsRemoveCmd)
{
	if (!m_isBatch)
	{
		if (pIsRemoveCmd)
			*pIsRemoveCmd = false;
		return;
	}
	
	if (FAILED(hr))
	{ // 失败了就中止batch
		clearBatchStatue();
	}
	else
	{ // succ
		bool isStop = m_pBatchTask->needStopInEndBatch();
		if (isStop)
		{
			endAndConfirm(pCtx);
		}
	}
    
    // delete command
    if (pIsRemoveCmd)
        *pIsRemoveCmd = true;
}

KwVersion* KEtBatchTransHelper::confirmTask(UINT32 userInnerID, KwTask* task, KwVersion::BanUndoType banUndo, WebInt baseVersion, bool isRollback)
{
	if (m_isBatch || m_skipNextConfirm)
		return nullptr;
	
	return m_pWb->baseConfirmTask(userInnerID, task, banUndo, baseVersion, isRollback);
}

void KEtBatchTransHelper::confirmVersion(KEtRevisionContext * pCtx, IBatchTask * pBatchTask)
{
    if (!pBatchTask)
    {
        ASSERT("no batch task!");
        return;
    }
    
	KwTask * pTask = pBatchTask->CreateBatchTask(pCtx);
	if (m_preVersionTag)
		pTask->setVersionTag(m_preVersionTag);
	ASSERT(pTask);
	
    pCtx->getBook()->GetWoStake()->ClearCalcSerialComplexity();
    
	pCtx->setIsRealTransform(true);
	CommitID curCommitId = pCtx->getCurCommitVersion();
	if (curCommitId >= 0)
		pCtx->setCurCommitVersion(curCommitId + 1);

	KwVersion * ver = m_pWb->baseConfirmTask(
			pTask->getUserInnerID(), 
			pTask, 
			pBatchTask->getBanUndoType(),
			pCtx->getBaseDataVersion(), 
			false);
	if (ver) 
	{
		pCtx->addVersionPtr((intptr_t)ver);
		ver->setSerialComplexity(pCtx->getSerialComplexity());
        m_skipNextConfirm = true;
	}
	// record for fillLog
	m_fillLogTasks.push_back(pTask);
}

void KEtBatchTransHelper::onUndoRedo(KEtRevisionContext * pCtx)
{
	// stop confirm fillLog
	endAndConfirm(pCtx);
}

void KEtBatchTransHelper::endAndConfirm(KEtRevisionContext * pCtx)
{
	if (!m_isBatch)
		return;
	
	// end trans
	IBatchTask * pBatchTask = m_pBatchTask;
	clearBatchStatue(); // 必须在appEndUndoTrans/endTransact前
	appEndUndoTrans(pCtx, S_OK);
	endTransact(false, pCtx);
	// confirm task
	confirmVersion(pCtx, pBatchTask);
	m_preVersionTag = nullptr;
}

void KEtBatchTransHelper::clearBatchStatue()
{
	m_isBatch = false;
	m_pBatchTask = nullptr;
	m_firstTaskInnerID = USER_INNER_INV_ID;
}

void KEtBatchTransHelper::setBatchStatus(IBatchTask * batchTask, UINT32 innerId)
{
	ASSERT(!m_isBatch);
	if (m_isBatch)
		return;
	
	m_isBatch = true;
	m_pBatchTask = batchTask;
	m_firstTaskInnerID = innerId;
}

void KEtBatchTransHelper::moveFillLogToTasks(KwTasks & tasks)
{
    if (m_fillLogTasks.empty())
        return;
    
    if (tasks.empty())
        tasks.swap(m_fillLogTasks);
    else
    {
        tasks.insert(tasks.begin(), m_fillLogTasks.begin(), m_fillLogTasks.end());
        m_fillLogTasks.clear();
    }
}

bool KEtBatchTransHelper::isSkipSerialComplexity()
{
    return (m_isBatch || m_skipNextConfirm);
}

}