﻿#include "etstdafx.h"
#include "database_field_identifier.h"
#include "database_field_context.h"

namespace wo
{
namespace Database
{

// ================== FieldValidationTypeIdentifier ==================
FieldValidationTypeIdentifier::FieldValidationTypeIdentifier(DVValueType type)
    : m_type(type)
{}

BOOL FieldValidationTypeIdentifier::Identify(FieldContext *, const RANGE &, VALIDATION dv)
{
    return dv.DVType == m_type;
}
// ================== FieldValidationTypeIdentifier ==================

// ================== FieldFormulaValidationIdentifier ==================
FieldFormulaValidationIdentifier::FieldFormulaValidationIdentifier(PCWSTR formula)
    : m_formula(QString::fromUtf16(formula))
{}

BOOL FieldFormulaValidationIdentifier::Identify(FieldContext *, const RANGE &, VALIDATION dv)
{
    if (dv.DVType != dvvtFormula)
        return FALSE;
    if (dv.bsFormula1 == NULL)
        return FALSE;
    QString formula = QString::fromUtf16(dv.bsFormula1);
    return m_formula == formula;
}
// ================== FieldFormulaValidationIdentifier ==================

// ================== FieldFormulaValidationRegexIdentifier ==================
FieldFormulaValidationRegexIdentifier::FieldFormulaValidationRegexIdentifier(PCWSTR formula)
    : m_formulaRegex(QString::fromUtf16(formula), Qt::CaseInsensitive)
{}

BOOL FieldFormulaValidationRegexIdentifier::Identify(FieldContext *, const RANGE &, VALIDATION dv)
{
    if (dv.DVType != dvvtFormula)
        return FALSE;
    if (dv.bsFormula1 == NULL)
        return FALSE;
    QString formula = QString::fromUtf16(dv.bsFormula1);
    return m_formulaRegex.exactMatch(formula);
}
// ================== FieldFormulaValidationRegexIdentifier ==================

// ================== FieldNumberFormatIdentifier ==================
FieldNumberFormatIdentifier::FieldNumberFormatIdentifier(NumFmtCat nfc, bool bAllowGeneral)
    : m_nfc(nfc)
    , m_bAllowGeneral(bAllowGeneral)
{}

BOOL FieldNumberFormatIdentifier::Identify(FieldContext *pContext, const RANGE &rg, VALIDATION dv)
{
    ks_stdptr<etoldapi::Range> spRange = pContext->CreateRangeObj(rg);
    if (spRange == nullptr)
        return FALSE;
    
    ks_bstr format;
    if (FAILED(spRange->get_NumberFormatLocal(&format)))
        return FALSE;

    IET_NumberFormatter *pFormatter = pContext->GetNumberFormatter();
    if (pFormatter == nullptr)
        return FALSE;

    NUMFMT_CAT_INFO info;
    pFormatter->GetCatInfo(format, &info);
    if (info.cat == m_nfc || m_bAllowGeneral && info.cat == NFCat_General)
        return TRUE;

    return FALSE;
}
// ================== FieldNumberFormatIdentifier ==================

} // Database
} // wo