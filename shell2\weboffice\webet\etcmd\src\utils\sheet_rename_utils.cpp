﻿#ifndef __WEBET_UTILS_RENAME_UTILS_CPP__
#define __WEBET_UTILS_RENAME_UTILS_CPP__

#include "etstdafx.h"
#include "sheet_rename_utils.h"
#include "et_hard_define.h"

void DBSheetRenameHelper::RenameConflictDBSheetName(etoldapi::Worksheets* pTgtWorksheets, etoldapi::Worksheets* pSrcWorksheets, const std::unordered_set<UINT>& importSheetIds)
{
    std::vector<ks_wstring> targeNames;
    std::vector<ks_wstring> sourceNames;
    CollectSheetNames(pTgtWorksheets, targeNames);
    CollectSheetNames(pSrcWorksheets, sourceNames, importSheetIds);

    for (int i = 0; i < pSrcWorksheets->GetSheetCount(); i++)
    {
        ks_bstr srcSheetName;
        ks_stdptr<_Worksheet> spSheet = pSrcWorksheets->GetSheetItem(i);
        spSheet->get_Name(&srcSheetName);
        
        if (0 == xstrcmp(srcSheetName, STR_CELL_IMAGE_SHEET_NAME))
            continue;

        if (!importSheetIds.empty())
        {
            ISheet* pSrcSheet = spSheet->GetSheet();
            UINT id = pSrcSheet->GetStId();
            if (importSheetIds.find(id) == importSheetIds.end())
                continue;
        }

        ks_wstring strName(srcSheetName);
        IDX sheetIdx = alg::STREF_INV_SHEET;
        // 解决源文件sheet名与导入目标sheet名集合的重名问题，如果重名就返回新命名(strName)和sheetIdx
        ValidateSheetName(targeNames, stGrid_DB, strName, &sheetIdx);
        // 如果触发了重命名，新名字还得和源文件的sheetName集合判重。
        if (sheetIdx != alg::STREF_INV_SHEET)
        {
            sheetIdx = alg::STREF_INV_SHEET;
            ValidateSheetName(sourceNames, stGrid_DB, strName, &sheetIdx);
        }
        // 源文件sheet触发了重命名后，要把它加入到源文件以及目标文件的sheet名集合，以供下一轮名字判重
        ks_bstr strNameBstr(strName.c_str());
        spSheet->put_Name(strNameBstr);
        if (sourceNames[i] != strName)
            sourceNames.push_back(strName);
        // 这个strName一定不会和目标文件里的sheet同名，直接添加就行
        targeNames.push_back(strName);
        strName.clear();
    }
}

void DBSheetRenameHelper::ForceRenameDBSheets(etoldapi::Worksheets* pWorksheets, int startIdx, int endIdx)
{
    int64 count = 0;
    pWorksheets->get_Count(&count);
    if (startIdx < 0 || startIdx >= count ||
        endIdx < 0 || endIdx >= count )
        return;
    std::vector<ks_wstring> vNames;
    CollectSheetNames(pWorksheets, vNames);
    for (int i = startIdx; i <= endIdx; i++)
    {
        ks_bstr orgName;
        ks_stdptr<_Worksheet> spSheet = pWorksheets->GetSheetItem(i); // ->GetISheet();
        if (!spSheet || !spSheet->GetSheet()->IsDbSheet())
            continue;
        spSheet->get_Name(&orgName);
        if (!orgName)
            continue;
        if (0 == xstrcmp(orgName, STR_CELL_IMAGE_SHEET_NAME))
            continue;
        // 强制重命名的逻辑比较挫，就是把文件名随便命名为另一个然后马上再设回来，还原
        ks_wstring newName(orgName);
        // 这里随便添加的字符后缀，目的是为了触发重命名后的引用调整，目的达到后名字又会再设回来
        newName.append(__X("(1)"));
        ValidateSheetName(vNames, stGrid_DB, newName);
        ks_bstr newNameBstr(newName.c_str());
        spSheet->put_Name(newNameBstr);
        spSheet->put_Name(orgName);
    }
}

HRESULT DBSheetRenameHelper::ValidateSheetName(std::vector<ks_wstring>& vSheetNames, SHEETTYPE st, ks_wstring& pcwszName, IDX* pSheetIdx)
{
    UINT maxCntNew = MAX_SHEET_NAME_CCH + 1;
    if (pcwszName.empty())
    {
        pcwszName = vSheetNames[vSheetNames.size() - 1];
    }

    HRESULT hr = S_OK;
    ks_wstring ret;
    // 查看是否已经存在这个名字的sheet
    IDX iSheetExists = 0;
    BOOL bNameExists = GetSheetNameIdx(vSheetNames, pcwszName, iSheetExists);
    IDX orgSheetExistsIdx = iSheetExists;
    BSTR bstrNampePreFix = NULL;
    if (bNameExists)
    {
        // 获取可以用作工作表明前缀的部分
        GetNamePreFix(st, pcwszName.c_str(), &bstrNampePreFix);
    }
    // 记下要求检查的名称（前缀）
    // zzy @ 2007-8-7 : bug 39502
    // wszTempName 明细有可能被冲跨，改用ks_wstring
    // WCHAR wszTempName[MAX_SHEET_NAME_CCH + 1] = {0};
    ks_wstring wstrTempName(bstrNampePreFix);
    int i = 2;
    while (bNameExists)
    {
        wstrTempName = bstrNampePreFix;
        // "(n)"存放处（后缀）
        ks_wstring strPostFix;
        if (stModule == st)
            strPostFix.Format(__X("_%d"), i);
        else
            strPostFix.Format(__X("(%d)"), i);

        if (strPostFix.length() >= MAX_SHEET_NAME_CCH)
        {
            // 如果前缀被压缩到极限，我们不能给出正确的名称，检查失败
            ASSERT(FALSE);
            hr = E_FAIL;
            break;
        }


        if (strPostFix.length() + wstrTempName.length() > MAX_SHEET_NAME_CCH)
        {
            wstrTempName.replace(MAX_SHEET_NAME_CCH - strPostFix.length(),
                wstrTempName.length() - (MAX_SHEET_NAME_CCH - strPostFix.length()),
                strPostFix);
            ASSERT(wstrTempName.length() == MAX_SHEET_NAME_CCH);
        }
        else
        {
            wstrTempName.append(strPostFix);
            ASSERT(wstrTempName.length() <= MAX_SHEET_NAME_CCH);
        }

        // 查看是否已经存在这个名字的sheet
        bNameExists = GetSheetNameIdx(vSheetNames, wstrTempName, iSheetExists);
        i++;
    }

    ::SysFreeString(bstrNampePreFix);

    if (!bNameExists)
    {
        // 当前名称的Sheet不存在，并不存在冲突
        if (!wstrTempName.empty())
            pcwszName = wstrTempName.c_str();
        if (pSheetIdx)
            *pSheetIdx = orgSheetExistsIdx;
        hr = S_OK;
    }
    else
    {
        ASSERT(FALSE);
        hr = E_FAIL;
    }
    return hr;
}

HRESULT DBSheetRenameHelper::GetNamePreFix(SHEETTYPE st, const WCHAR* pwszSheetName, BSTR* pbstrSheetNamePreFix)
{
    *pbstrSheetNamePreFix = ::SysAllocString(pwszSheetName);
    BSTR bstrSheetNamePreFix = *pbstrSheetNamePreFix;

    int cch = ks_wcslen(bstrSheetNamePreFix);
    int chPos = cch - 1;
    BOOL bValidPreFix = FALSE;

    if (')' == bstrSheetNamePreFix[chPos])
    {
        chPos--;
        for (; chPos > 0;)
        {
            if ('0' <= bstrSheetNamePreFix[chPos] && bstrSheetNamePreFix[chPos] <= '9')
            {
                chPos--;
            }
            else if ('(' == bstrSheetNamePreFix[chPos])
            {
                bValidPreFix = TRUE;
                break;
            }
            else
            {
                bValidPreFix = FALSE;
                break;
            }
        }

        if (bValidPreFix)
        {
            bstrSheetNamePreFix[chPos] = 0;
        }
    }
    if (!bValidPreFix && st != stGrid_DB)
    {
        ks_wstring strPreFix;
        strPreFix = bstrSheetNamePreFix;
        strPreFix += __X(" ");

        ::SysFreeString(bstrSheetNamePreFix);
        *pbstrSheetNamePreFix = strPreFix.AllocBSTR();
    }

    return S_OK;
}

BOOL DBSheetRenameHelper::GetSheetNameIdx(std::vector<ks_wstring>& vSheetNames, ks_wstring& pcwszName, IDX& index)
{
    for (int i = 0; i < vSheetNames.size(); i++)
    {
        if (vSheetNames[i] == pcwszName)
        {
            index = i;
            return TRUE;
        }
    }
    index = alg::STREF_INV_SHEET;
    return FALSE;
}

void DBSheetRenameHelper::CollectSheetNames(etoldapi::Worksheets* pWorksheets, std::vector<ks_wstring>& names, const std::unordered_set<UINT>& importSheetIds)
{
    int64 dbSheetCount = 0;
    pWorksheets->get_Count(&dbSheetCount);
    for (int i = 0; i < dbSheetCount; i++)
    {
        auto pSheet = pWorksheets->GetSheetItem(i)->GetSheet();
        UINT id = pSheet->GetStId();
        if (!importSheetIds.empty() && importSheetIds.find(id) == importSheetIds.end())
            continue;
        PCWSTR strName = nullptr;
        pSheet->GetName(&strName);
        names.emplace_back(strName);
    }
}

#endif