﻿#include "etstdafx.h"
#include "qrlabel_utils.h"
#include "attachment_utils.h"
#include "webetlink.h"
#include "workbook.h"
#include "database/database.h"
#include "database/database_utils.h"
#include "database/database_field_context.h"
#include "applogic/cell_image_helper.h"
#include "binvariant/binreader.h"
#include "webbase/webdeclare.h"
#include <public_header/drawing/model/abstract_shape.h>
#include "helpers/variable_restore_scope.h"
#include "util.h"

extern Callback* gs_callback;
extern bool g_isCanBreak;
extern WebProcType gs_procType;

using namespace wo::Database;

namespace wo
{

namespace util
{

QRLabelHelper::QRLabelHelper(_Workbook* wb, KEtRevisionContext* ctx, UseType useType, binary_wo::VarObj* pParam)
: m_spWb(wb), m_pCtx(ctx), m_useType(useType), m_pParam(pParam)
{
	Init();
}

QRLabelHelper::~QRLabelHelper()
{
	AddQRLableImgInfoParam();
}

HRESULT QRLabelHelper::Init()
{
	char* str = ::getenv("GEN_QRCODE_IMAGE_ENABLE");
	if(!str)
		m_ConvertQRLabelEnable = true;
	else if (strncmp(str, "true", 5) == 0)
		m_ConvertQRLabelEnable = true;
	else
		m_ConvertQRLabelEnable = false;
	
	WOLOG_INFO << "Convert QRLabel enable:" << m_ConvertQRLabelEnable;

	ParseQRLableImgInfoParam();
	return S_OK;
}

HRESULT QRLabelHelper::ConvertQRLabel2CellImg(IDX sheetIdx)
{
	if (!CanConvertQRLabel(m_useType))
		return E_FAIL;

	IBook* pBook = m_spWb->GetBook();
	RANGE usedRange(pBook->GetBMP());
	IKWorksheet* pWorkSheet = m_spWb->GetWorksheets()->GetSheetItem(sheetIdx);
	if(!pWorkSheet)
		return E_FAIL;

	if(!pWorkSheet->GetSheet()->IsGridSheet())
		return S_OK;

	pWorkSheet->GetUsedRange(&usedRange);
	return ConvertQRLabel2CellImg(usedRange); 
}

HRESULT QRLabelHelper::ConvertQRLabel2CellImg(const RANGE& range)
{
	if (!CanConvertQRLabel(m_useType))
		return E_FAIL;

	class FindAllQRColEnum : public IFieldEnum
	{
	public:
		FindAllQRColEnum(QRLabelHelper* pQRLabelHelper, IKWorksheet* pWorkSheet, std::vector<RANGE>& qrLabelColRangList) 
		: m_pQRLabelHelper(pQRLabelHelper), m_pWorkSheet(pWorkSheet), m_qrLabelColRangeList(qrLabelColRangList)
		{
		}
		virtual ~FindAllQRColEnum() = default;
		HRESULT Do(FieldContext *pContext, Database::IDbField *pField, const RANGE& rg, const VALIDATION& dv) override
		{
			if(pField->GetType() != dftGenQRLabel)
				return S_OK;

			if (!dv.bsFormula1)
				return S_OK;	

			m_qrLabelColRangeList.push_back(rg);
			m_pQRLabelHelper->DoOnQRField(pContext, pField, rg);	
			m_pQRLabelHelper->ConvertQRLabel2CellImgInl(m_pWorkSheet, pField, rg);
			return S_OK;
		}
	private:
		IKWorksheet* m_pWorkSheet;
		QRLabelHelper* m_pQRLabelHelper;
		std::vector<RANGE>& m_qrLabelColRangeList;
	};

	wo::util::CallTimeStat callTime("ConvertQRLabel2CellImg");

	FieldContext fieldContext(m_spWb, m_pCtx);
	IKWorksheet* pWorkSheet = m_spWb->GetWorksheets()->GetSheetItem(range.SheetFrom());
	if(!pWorkSheet)
		return E_FAIL;

	std::vector<RANGE> qrLabelColRangeList;
	FindAllQRColEnum findAllQRColEnum(this, pWorkSheet, qrLabelColRangeList);
	FieldsManager *pDbMgr = FieldsManager::Instance();
	pDbMgr->EnumFieldsInRANGE(&fieldContext, range, dftGenQRLabel, dbEnumAll, &findAllQRColEnum);

	//部分场景需要重算
	if(m_useType == UseType::UseType_SplitToNewBook)
	{
		wo::VariableRestoreScope<decltype(g_isCanBreak)> vrs(g_isCanBreak);
		g_isCanBreak = false; // 先不支持中断
		for(auto& range : qrLabelColRangeList)
		{
			m_spWb->GetBook()->Recalculate(range, true);
		}
		m_spWb->GetBook()->RecalculateAll();
	}
	return S_OK;
}

HRESULT QRLabelHelper::DoOnQRField(FieldContext *pContext, Database::IDbField *pField, const RANGE& rg)
{
	if(m_useType == UseType::UseType_Import_KSheet2Et
		|| m_useType == UseType_Import_KSheet2KSheet
		|| m_useType == UseType::UseType_Export_KSheet2Xlsx
		|| m_useType == UseType::UseType_Export_KSheet2IMG
		|| m_useType == UseType::UseType_Export_KSheet2PDF
		|| m_useType == UseType::UseType_MergeFile
		|| m_useType == UseType::UseType_SplitToNewBook
		|| m_useType == UseType::UseType_SplitToNewSheet)
	{
		//将列数据格式校验清除
		HRESULT hr = Utils::ClearAll(pContext, rg);
		if(FAILED(hr))
			return E_FAIL;
	}
	return S_OK;
}

HRESULT QRLabelHelper::ConvertQRLabel2CellImgInl(IKWorksheet* pWorkSheet, Database::IDbField *pField, const RANGE& rg)
{
	class QRCellValueAcpt : public ICellValueAcpt
	{
	public:
		QRCellValueAcpt(QRLabelHelper* pQRLabelHelper, IKWorksheet* pWorkSheet, QRLabelUploader* pQRLabelUploader, ICellImages* pCellImages)
		: m_pQRLabelHelper(pQRLabelHelper), m_pWorkSheet(pWorkSheet), m_pQRLabelUploader(pQRLabelUploader), m_pCellImages(pCellImages)
		{	
		}
		STDPROC_(INT) Do(ROW row, COL col, const_token_ptr pToken) override
		{
			if (!pToken)
				return 0;
			DWORD tokenType = alg::GetExecTokenMajorType(pToken);
			if (tokenType == etexec::ETP_NONE)
				return 0;
			if (alg::const_handle_token_assist::is_type(pToken))
			{
				alg::const_handle_token_assist chta(pToken);
				if (chta.get_handleType() == alg::ET_HANDLE_QRLABEL)
				{
					ks_stdptr<IQRLabelHandle> spTokenQRLabel = chta.get_handle()->CastUnknown();
					if(!spTokenQRLabel)
						return 0;
					
					//先从回放参数获取二维码图片信息
					QRLabelHelper::UploadQRLabelImgInfo imgInfo;
					if(m_pQRLabelHelper->GetQRLableImgInfoFromParam(m_pWorkSheet, row, col, imgInfo))
					{
						m_pQRLabelHelper->DoQRLabelAttachment(m_pWorkSheet, row, col, imgInfo);
						return 0;
					}
					//不是命令回放，请求服务端上传
					m_pQRLabelUploader->UploadQRLabel(row, col, spTokenQRLabel.get());
				}
			}
			return 0;
		}
	private:
		QRLabelUploader* m_pQRLabelUploader;
		ICellImages* m_pCellImages;
		QRLabelHelper* m_pQRLabelHelper;
		IKWorksheet* m_pWorkSheet;
	};

	et_sdptr<ISheetEnum> spSheetEnum;
	pWorkSheet->GetSheet()->CreateEnum(&spSheetEnum);

	// 在服务端处理的时候 userId 也会转成 connId, 所以优先用 connId
	PCWSTR connectionID = m_pCtx->getUser()->connID();
	if (xstrcmp(__X("fake_connection_id"), connectionID) == 0)
		connectionID = m_pCtx->getUser()->userID();

	QRLabelUploader qrLabelUploader(this, pWorkSheet, m_spWb->GetCellImages(), connectionID);
	QRCellValueAcpt qrCellValueAcpt(this, pWorkSheet, &qrLabelUploader, m_spWb->GetCellImages());
	spSheetEnum->EnumCellValue(rg, &qrCellValueAcpt);	
	qrLabelUploader.BatchUploadQRLabel();
	return S_OK;
}

bool QRLabelHelper::CanConvertQRLabel(UseType type)
{
	if(!m_ConvertQRLabelEnable)
		return false;

	switch(type)
	{
		case UseType_Import_KSheet2KSheet:
		case UseType_Import_KSheet2Et:
		case UseType_Import_KSheet2DB:
		case UseType_MergeFile:
		case UseType_SplitToNewSheet:
		return true;
	}

	//必须在子进程导出
	if (gs_procType != WebProcTypeChild)
	{
		WOLOG_INFO << "QRLabel to cell image not in child process!" << ", type:" << type;
		return false;
	}
	return true;
}

int __pixel2twip(float fdip, int npixel)
{
	return (int)((double)npixel / fdip * 72 * 20 + 0.5f);
}

void QRLabelHelper::AdjustCellPictureSize(const QSize& imgSize, const QSize& cellSize, UseType type, single&width, single& height)
{
	//产品要求，导出pdf和图片是按照单元格大小，其他自适应图片大小
	width = (float)(imgSize.width());
	height = (float)(imgSize.height());
	//若长或宽其一大于11 inch，则插入的图片要以锁定纵横比的方式调整缩放至最长边为11 inch
	static const int MAX_SIZE = 11 * 1440;
	switch (type)
	{
		case UseType_Export_KSheet2PDF:
		case UseType_Export_KSheet2IMG:
		{
			if(cellSize.height() <= 0 || cellSize.width() <= 0)
			{
				width = -1; 
				height = -1;
				return;
			}
			QSize sz = QSize(std::min(MAX_SIZE, cellSize.width()), std::min(MAX_SIZE, cellSize.height()));
			//按照单元格大小，尽量保证长宽比例
			if(width/height < sz.width() * 1.0 / sz.height())
			{
				width *= sz.height() / height;
				height = sz.height();
			}
			else
			{
				height *= sz.width()/ width;
				width = sz.width();
			}
			break;
		}
		default:
		{
			//自适应图片大小
			width = __pixel2twip(96, width);
			height = __pixel2twip(96, height);
			if (width > MAX_SIZE || height > MAX_SIZE)
			{
				double shrinkRatio = std::min(MAX_SIZE / width, MAX_SIZE / height);
				width *= shrinkRatio;
				height *= shrinkRatio;
			}
			break;	
		}
	}
}

HRESULT QRLabelHelper::DoQRLabelAttachment(IKWorksheet* pWorkSheet, ROW row, COL col, const UploadQRLabelImgInfo& imgInfo)
{
	PCWSTR attachmentId = imgInfo.attachmentId.c_str();
	ks_wstring uuid = imgInfo.uuid.c_str();
	switch(m_useType)
	{
		case UseType::UseType_Import_KSheet2Et:
		case UseType::UseType_Import_KSheet2KSheet:
		case UseType::UseType_MergeFile:
		case UseType::UseType_SplitToNewSheet:
		{
			//命令回放时，不用重新请求，记录二维码图片信息
			SaveQRLabelImgInfo(pWorkSheet, row, col, imgInfo);
			//不用break,继续执行下面代码
		}
		case UseType::UseType_Export_KSheet2Xlsx:
		case UseType::UseType_Export_KSheet2IMG:
		case UseType::UseType_Export_KSheet2PDF:
		case UseType::UseType_SplitToNewBook:
		{
			PCWSTR strPath = nullptr;
			ks_wstring fakeUrl = util::generateFakeAttachmentUrl(attachmentId, __X(""));

			QSize cellSize;
			ISheet* pSheet = pWorkSheet->GetSheet();
			cellSize.setWidth(pSheet->GetColWidth(col, FALSE));
			cellSize.setHeight(pSheet->GetRowHeight(row, FALSE));
	
			ks_stdptr<IRowColOp> spOp;
			pSheet->QueryInterface(IID_IRowColOp, (void**)&spOp);
			if(spOp)
			{
				spOp->SetRowUnsynced(row, row, TRUE);
				spOp->SetColUnsynced(col, col, TRUE);
			}
			
			single height, width;
			AdjustCellPictureSize(imgInfo.size, cellSize, m_useType, width, height);
			ks_stdptr<IKShape> spShape;
			HRESULT hr = pWorkSheet->InsertCellPicture(row, col, strPath, uuid, fakeUrl.c_str(), width, height, FALSE, &spShape);
			if(FAILED(hr))
			{
				return hr;
			}
			break;
		}
		// ksheet在生成数据表，创建应用表时，不能沿用上面逻辑，会修改ksheet中二维码列，所以单独处理
		case UseType::UseType_Import_KSheet2DB:
		{
			SaveQRLabelImgInfo(pWorkSheet, row, col, imgInfo);
			break;
		}
	}
	return S_OK;
}

void QRLabelHelper::SaveQRLabelImgInfo(IKWorksheet* pWorkSheet, ROW row, COL col, const UploadQRLabelImgInfo& imgInfo)
{
	UINT sheetId = pWorkSheet->GetSheet()->GetStId();
	if(m_qrlabelImgMap.find(sheetId) == m_qrlabelImgMap.end())
	{
		std::map<ROW, UploadQRLabelImgInfo> colMap;
		colMap.insert(std::make_pair(row, imgInfo));
		std::map<COL, std::map<ROW, UploadQRLabelImgInfo>> sheetMap;
		sheetMap.insert(std::make_pair(col, colMap));
		m_qrlabelImgMap.insert(std::make_pair(sheetId, sheetMap));
	}
	else
	{
		auto& sheetMap = m_qrlabelImgMap[sheetId];
		if(sheetMap.find(col) == sheetMap.end())
		{
			std::map<ROW, UploadQRLabelImgInfo> colMap;
			colMap.insert(std::make_pair(row, imgInfo));
			sheetMap.insert(std::make_pair(col, colMap));
		}
		else
		{
			auto& colMap = sheetMap[col];
			colMap.insert(std::make_pair(row, imgInfo));
		}
	}
}

//将生成的二维码加入命令参数，防止命令回放重复请求，防止重新生成uuid
void QRLabelHelper::AddQRLableImgInfoParam()
{
	if(!m_pParam || m_qrlabelImgMap.empty())
		return;

	//m_qrlabelImgMapInParam不为空，说明是命令回放，不用再重复添加
	if(!m_qrlabelImgMapInParam.empty())
		return;

	if(!m_pParam->has("qrLabels"))
		m_pParam->add_field_array("qrLabels", typeStruct);

	auto qrLabelsArray = m_pParam->get_s("qrLabels");
	for(auto sheetItr = m_qrlabelImgMap.begin(); sheetItr != m_qrlabelImgMap.end(); ++sheetItr)
	{
		UINT sheetId = sheetItr->first;
		auto& sheetMap = sheetItr->second;
		binary_wo::VarObj sheetObj = qrLabelsArray.add_item_struct();
		sheetObj.add_field_uint32("sheetId", sheetId);
		binary_wo::VarObj colArray = sheetObj.add_field_array("cols", typeStruct);
		for(auto colItr = sheetMap.begin(); colItr != sheetMap.end(); ++colItr)
		{
			COL col = colItr->first;
			auto& colMap = colItr->second;
			binary_wo::VarObj colObj = colArray.add_item_struct();
			colObj.add_field_uint32("col", col);
			binary_wo::VarObj rowArray = colObj.add_field_array("rows", typeStruct);
			for(auto rowItr = colMap.begin(); rowItr != colMap.end(); ++rowItr)
			{
				ROW row = rowItr->first;
				auto& imgInfo = rowItr->second;
				binary_wo::VarObj rowObj = rowArray.add_item_struct();
				rowObj.add_field_uint32("row", row);
				rowObj.add_field_str("attachmentId", imgInfo.attachmentId.c_str());
				rowObj.add_field_str("uuid", imgInfo.uuid.c_str());
				rowObj.add_field_uint32("width", imgInfo.size.width());
				rowObj.add_field_uint32("height", imgInfo.size.height());
			}
		}
	}
	m_pCtx->setIsRealTransform(true);
}

//解析命令回放记录的二维码信息
void QRLabelHelper::ParseQRLableImgInfoParam()
{
	if(!m_pParam)
		return;

	if(!m_pParam->has("qrLabels"))
		return;

	auto qrLabelsArray = m_pParam->get_s("qrLabels");
	for(int sheetIdx = 0; sheetIdx < qrLabelsArray.arrayLength_s(); ++sheetIdx)
	{
		binary_wo::VarObj sheetObj = qrLabelsArray.at(sheetIdx);
		UINT sheetId = sheetObj.field_uint32("sheetId");
		if(m_qrlabelImgMapInParam.find(sheetId) == m_qrlabelImgMapInParam.end())
		{
			std::map<COL, std::map<ROW, UploadQRLabelImgInfo>> sheetMap;
			m_qrlabelImgMapInParam.insert(std::make_pair(sheetId, sheetMap));
		}
		auto& sheetMap = m_qrlabelImgMapInParam[sheetId];

		binary_wo::VarObj colArray = sheetObj.get_s("cols");
		for(int colIdx = 0; colIdx < colArray.arrayLength_s(); ++colIdx)
		{
			binary_wo::VarObj colObj = colArray.at(colIdx);
			COL col = colObj.field_uint32("col");
			if(sheetMap.find(col) == sheetMap.end())
			{
				std::map<ROW, UploadQRLabelImgInfo> colMap;
				sheetMap.insert(std::make_pair(col, colMap));
			}
			auto& colMap = sheetMap[col];

			binary_wo::VarObj rowArray = colObj.get_s("rows");
			for(int rowIdx = 0; rowIdx < rowArray.arrayLength_s(); ++rowIdx)
			{
				binary_wo::VarObj rowObj = rowArray.at(rowIdx);
				ROW row = rowObj.field_uint32("row");
				UploadQRLabelImgInfo imgInfo;
				imgInfo.attachmentId = rowObj.field_str("attachmentId");
				imgInfo.uuid = rowObj.field_str("uuid");
				imgInfo.size.setWidth(rowObj.field_uint32("width"));
				imgInfo.size.setHeight(rowObj.field_uint32("height"));
				colMap.insert(std::make_pair(row, imgInfo));
			}
		}
	}
}

bool QRLabelHelper::GetQRLableImgInfoFromParam(IKWorksheet* pWorkSheet, ROW row, COL col, UploadQRLabelImgInfo& imgInfo)
{
	UINT sheetId = pWorkSheet->GetSheet()->GetStId();
	if(m_qrlabelImgMapInParam.find(sheetId) == m_qrlabelImgMapInParam.end())
		return false;
	
	auto& sheetMap = m_qrlabelImgMapInParam[sheetId];
	if(sheetMap.find(col) == sheetMap.end())
		return false;

	auto& colMap = sheetMap[col];
	if(colMap.find(row) == colMap.end())
		return false;

	imgInfo = colMap[row];
	return true;
}

bool QRLabelHelper::GetUploadQRLabelImgAttachmentId(UINT sheetId, ROW row, COL col, ks_wstring& attachmentId)
{
	if(m_useType != UseType_Import_KSheet2DB)
		return false;

	if(m_qrlabelImgMap.find(sheetId) == m_qrlabelImgMap.end())
		return false;
	
	auto& sheetMap = m_qrlabelImgMap[sheetId];
	if(sheetMap.find(col) == sheetMap.end())
		return false;
	
	auto& colMap = sheetMap[col];
	if(colMap.find(row) == colMap.end())
		return false;
	
	attachmentId = m_qrlabelImgMap[sheetId][col][row].attachmentId;
	return true;
}

////////////////////////////////////////////////////////////////////////////
QRLabelUploader::QRLabelUploader(QRLabelAttachmentHandler* pHandler, IKWorksheet* pWorkSheet, ICellImages* pCellImages, PCWSTR connId)
: m_pWorkSheet(pWorkSheet), m_pCellImages(pCellImages), m_connId(connId), m_pHandler(pHandler)
{
	Init();
}

HRESULT QRLabelUploader::Init()
{
	char* str = ::getenv("GEN_QRCODE_IMAGE_BATCH_SIZE");
	if (str != nullptr)
		m_uploadBatchSize = atoi(str);
	else
		m_uploadBatchSize = 100;
	
	WOLOG_INFO << "QRLabel upload batch size:" << m_uploadBatchSize;
	return S_OK;
}

HRESULT QRLabelUploader::UploadQRLabel(ROW row, COL col, IQRLabelHandle* pQRLabelHandle)
{
	if(!pQRLabelHandle)
		return S_OK;

	if(m_pageIdMap.find(col) == m_pageIdMap.end())
		m_pageIdMap.insert(std::make_pair(col, pQRLabelHandle->GetPageId()));

	if(m_qrLabelMap.find(col) != m_qrLabelMap.end())
	{
		m_qrLabelMap[col].insert(std::make_pair(row, QRLabelImgParam(pQRLabelHandle)));
	}
	else
	{
		std::unordered_map<ROW, QRLabelImgParam> map;
		map.insert(std::make_pair(row, QRLabelImgParam(pQRLabelHandle)));
		m_qrLabelMap.insert(std::make_pair(col, map));
	}
	
	//批量接口必须是同一个pageid，同一二维码列pageid相同, 所以按照列请求
	if(m_qrLabelMap[col].size() >= m_uploadBatchSize)
	{
		BatchUploadQRLabel(col); 
		m_qrLabelMap.erase(col);
	}
	return S_OK;
}

namespace
{
bool SerialCellPicture(ICellImages* pCellImages, binary_wo::BinWriter* binWriter, PCWSTR str)
{
    CellImg_Param cellImgParam;
    if (!pCellImages || !cellimg::GetCellImgParamFromCellValue(str, &cellImgParam) || !cellImgParam.isValid())
        return false;

    IKShape* pShape = pCellImages->GetImgByName(cellImgParam.getNameStr());
    ks_castptr<drawing::AbstractShape> spShape = pShape;
    if (!spShape)
        return false;

    IKBlipAtom* spBlipAtom = spShape->picID();
    if (!spBlipAtom)
        return false;

    ks_bstr bstrUrl;
    spBlipAtom->GetLinkPath(&bstrUrl);
    if (bstrUrl.empty())
        return false;

    PCWSTR url = bstrUrl.c_str();
    bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(url);
	binWriter->beginStruct();
	binWriter->addStringField(__X("cellPic"), "type");
	binWriter->beginStruct("value");
    if (isAttachment)
    {
        PCWSTR prefix = xstrstr(url, WO_ET_ATTACHMENT_LINK_PATH_PREFIX);
        ASSERT(prefix != nullptr);
        PCWSTR attachmentId = prefix + WO_ET_ATTACHMENT_LINK_PATH_PREFIX_LEN;
		binWriter->addStringField( __X("attachment"), "cellPicType");
		binWriter->addStringField(attachmentId, "attachmentId");
    }
    else
    {
		binWriter->addStringField(__X("url"), "cellPicType");
		binWriter->addStringField(bstrUrl.c_str(), "url");
    }
	binWriter->endStruct();
	binWriter->endStruct();
    return true;
}
}

HRESULT QRLabelUploader::SerialColQRLabelHandle(binary_wo::BinWriter* binWriter, COL col)
{
	binWriter->addStringField(m_connId.c_str(), "connId");
	binWriter->addStringField(m_pageIdMap[col].c_str(), "pageId");
	binWriter->beginStruct("data");
	binWriter->addBoolField(true, "skipCheck");
	binWriter->addKey("qrcodes");
	binWriter->beginArray();
	auto& colMap = m_qrLabelMap[col];
	for(auto colIter = colMap.begin(); colIter != colMap.end(); ++colIter)
	{
		IQRLabelHandle* pHandle = colIter->second.m_pQRLabelHandle;
		if(!pHandle)
			continue;

		ks_wstring uuid;
		cellimg::CreateUUIDName(uuid);
		colIter->second.m_imgUuid = uuid;
		binWriter->beginStruct();
		binWriter->addStringField(pHandle->GetKey(), "key");
		binWriter->addStringField(uuid.c_str(), "md5");
		binWriter->addKey("tmpItems");
		binWriter->beginArray();
		for(int i = 0; i < pHandle->GetCount(); ++i)
		{
			bool isCellPic = SerialCellPicture(m_pCellImages, binWriter, pHandle->GetItem(i));
			if (!isCellPic)
			{
				binWriter->beginStruct();
				binWriter->addStringField(__X("text"), "type");
				binWriter->beginStruct("value");
				binWriter->addStringField(pHandle->GetItem(i), "text");
				binWriter->endStruct();
				binWriter->endStruct();
			}
		}
		binWriter->endArray();
		binWriter->endStruct();
	}
	binWriter->endArray();
	binWriter->endStruct();
	return S_OK;
}

HRESULT QRLabelUploader::UploadColQRLabel(binary_wo::BinWriter* binWriter, COL col)
{
	BinWriter::StreamHolder shbt = binWriter->buildStream();
	WebSlice slice = {shbt.get(), binWriter->writeLength()};
	WebSlice output = {nullptr, 0};
	if(!gs_callback->generateAndUploadQRLabelImg)
		return E_FAIL;

	gs_callback->generateAndUploadQRLabelImg(&slice, &output);
	std::unordered_map<ks_wstring, QRLabelAttachmentHandler::UploadQRLabelImgInfo, WSTR_HASH> uploadAttachmentMap;
	binary_wo::BinReader br(output.data, output.size);
	binary_wo::VarObjRoot root = br.buildRoot();
	binary_wo::VarObj obj = root.cast();
	VarObj data = obj.get_s("data");
	for(auto& key : data.keys())
	{
		binary_wo::VarObj itemObj = data.get_s(key);
		// status: 状态 0-成功 1-失败
		if (itemObj.field_int32("status") != 0)
			continue;

		ks_wstring attachmentId = itemObj.field_str("attachmentId");
		if (attachmentId.empty())
			continue;
		
		QRLabelAttachmentHandler::UploadQRLabelImgInfo imgInfo;
		imgInfo.attachmentId = attachmentId;
		imgInfo.uuid = krt::utf16(QString(key));
		if(itemObj.has("width") && itemObj.has("height"))
		{
			imgInfo.size.setWidth(itemObj.field_uint32("width"));
			imgInfo.size.setHeight(itemObj.field_uint32("height"));
		}

		uploadAttachmentMap.insert(std::make_pair(imgInfo.uuid, imgInfo));
	}

	auto& colMap = m_qrLabelMap[col];
	int uploadSize = colMap.size();
	for(auto colIter = colMap.begin(); colIter != colMap.end(); ++colIter)
	{
		ROW row = colIter->first;
		ks_wstring& uuid = colIter->second.m_imgUuid;
		if(uploadAttachmentMap.find(uuid) == uploadAttachmentMap.end())
		{
			WOLOG_ERROR << "Upload qrlabel error, row:" << row << ", col:" << col;
			continue;
		}
		--uploadSize;
		auto& imgInfo = uploadAttachmentMap[uuid];
		m_pHandler->DoQRLabelAttachment(m_pWorkSheet, row, col, imgInfo);
	}
	ASSERT(uploadSize == 0);
	return S_OK;
}

HRESULT QRLabelUploader::BatchUploadQRLabel(COL col)
{
	if(m_qrLabelMap.empty())
		return S_OK;

	BinWriter binWriter;
	if(col != -1)
	{
		SerialColQRLabelHandle(&binWriter, col);
		UploadColQRLabel(&binWriter, col);
	}
	else
	{
		for(auto iter = m_qrLabelMap.begin(); iter != m_qrLabelMap.end(); ++iter)
		{
			col = iter->first;
			SerialColQRLabelHandle(&binWriter, col);
			UploadColQRLabel(&binWriter, col);
		}
	}
	return S_OK;
}

} // namespace util

} // namespace wo
