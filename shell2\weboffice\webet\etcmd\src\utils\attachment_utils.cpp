﻿#include "etstdafx.h"
#include "attachment_utils.h"

namespace wo
{

namespace util
{

PCWSTR getAttachmentId(PCWSTR path)
{
	PCWSTR str = xstrstr(path, WO_ET_ATTACHMENT_LINK_PATH_PREFIX);
	if (!str)
		return nullptr;
	return str + WO_ET_ATTACHMENT_LINK_PATH_PREFIX_LEN;
}

// 解析kw:annex 协议中的id
ks_wstring getAttachmentIdByAnnex(PCWSTR path, BOOL& isVideo)
{
	if (!path)
	{
		return __X("");
	}

	QString src = krt::fromUtf16(path);
	if (!src.startsWith("kw:annex?"))
	{
		return __X("");
	}

 	src.remove(0, 9);

	QUrlQuery query = QUrlQuery(src);
	const QString& oId = query.queryItemValue("oId");
	if (!oId.isEmpty())
	{
		const QString& type = query.queryItemValue("aType");
		if (!type.isEmpty())
		{
			isVideo = type == "video";
		}
		return krt::utf16(oId);
	}

	return __X("");
}

PCWSTR getFileId(PCWSTR linkPath, PCWSTR* source)
{
	if (!linkPath)
		return nullptr;
	PCWSTR str = xstrstr(linkPath, WO_ET_ATTACHMENT_LINK_PATH_PREFIX);
	if (str)
	{
		if (source)
			*source = __X("upload_ks3");
		return str + WO_ET_ATTACHMENT_LINK_PATH_PREFIX_LEN;
	}
	int len = xstrlen(linkPath);
	for (int i = len - 1; i >= 0; --i)
		if (*(linkPath + i) == __Xc('/'))
		{
			// 云文档 url 最后一节为云文档 id
			if (source)
				*source = __X("cloud");
			return linkPath + i + 1;
		}
	return nullptr;
}

ks_wstring generateFakeAttachmentUrl(PCWSTR id, PCWSTR prefix)
{
	static PCWSTR etAttachmentPrefix = __X("https://www.kdocs.cn/office/weboffice-static/js/et_attachment_img.png");
	if (!prefix)
		prefix = etAttachmentPrefix;
	ks_wstring fakeUrl;
	if (!id)
		return fakeUrl;
	fakeUrl += prefix;
	fakeUrl += fakeUrl.find(__Xc('?')) != ks_wstring::npos ? __Xc('&') : __Xc('?');
	fakeUrl += WO_ET_ATTACHMENT_LINK_PATH_PREFIX;
	fakeUrl += id;
	return fakeUrl;
}

} // namespace util

} // namespace wo
