#include "remote_chart_helper.h"

#include <public_header/chart/src/model/kctcells.h>
#include <etchart/etchart_types.h>
#include <etchart/kdatasourcerangesdivider.h>
#include <etchart/kdividerchooser.h>
#include <etchart/kdatasourcehelper.h>
#include <etcore/et_ranges_helper.h>
#include <public_header/chart/src/model/charttype.h>

#include "pivot_core/pivot_core_x.h"
#include "message_producer.h"
#include "range_subscription.h"

namespace wo {

SeriesDirection CaclDirection(IKRanges *pCateRanges, IKRanges* pNameRanges) {
    SeriesDirection dir = sdirAuto;

    if (!pCateRanges | !pNameRanges) {
        return dir;
    }

    bool bNameVert = chart::KDataSourceHelper::isWithinSingleColumn(pNameRanges);
    bool bNameHori = chart::KDataSourceHelper::isWithinSingleRow(pNameRanges);

    if (!bNameVert && !bNameHori) {
        return dir;
    }

    bool bCateVert = chart::KDataSourceHelper::isWithinSingleColumn(pCateRanges);
    bool bCateHori = chart::KDataSourceHelper::isWithinSingleRow(pCateRanges);
    if (!bCateHori && !bCateVert) {
        return dir;
    }

    chart::KDataSourceHelper::sortRanges(pCateRanges);
    chart::KDataSourceHelper::sortRanges(pNameRanges);

    if (bNameHori && bNameVert) {
        //single cell
        const RANGE* pCateRange = nullptr;
        pCateRanges->GetItem(0, nullptr, &pCateRange);

        const RANGE* pNameRange = nullptr;
        pNameRanges->GetItem(0, nullptr, &pNameRange);

        if (pNameRange->RowFrom() <= pCateRange->RowFrom()) {
            return sdirColumn;
        }

        return sdirRow;
    }

    if (bNameHori) {
        return sdirColumn;
    }

    if (bNameVert) {
        return sdirRow;
    }

    return dir;
}

CELL CalcSeriesBoundary(IKRanges* pValRanges)
{
    CELL boundCell = {-1, -1};
    if (!pValRanges) {
        return boundCell;
    }

    UINT nName = 0;
    pValRanges->GetCount(&nName);
    if (nName <= 0) {
        return boundCell;
    }

    const RANGE *valRg = nullptr;
    pValRanges->GetItem(0, nullptr, &valRg);
    boundCell.col = valRg->ColFrom();
    boundCell.row = valRg->RowFrom();

    return boundCell;
}

RemoteChartLinkManager::RemoteChartLinkManager()
    : m_pBook(nullptr)
    , m_sheetIdx(-1)
{
    m_sheetIdx = -1;
    m_pSubsriber = new SeriesRangeSubscription(this);
}

RemoteChartLinkManager::~RemoteChartLinkManager()
{
    if (m_pSubsriber) {
        delete m_pSubsriber;
        m_pSubsriber = nullptr;
    }
}

void RemoteChartLinkManager::DoBindSheet(IBook* pBook, int sheetIdx)
{
    m_pBook = pBook;
    m_sheetIdx = sheetIdx;
}

void RemoteChartLinkManager::LinkRemoteChart(int chartType, IKRanges* nameRgs, IKRanges* cateRgs, IKRanges* valRgs)
{
    m_pSubsriber->Setb1904(m_pBook->Is1904DateSystem());
    m_pSubsriber->SetChartType(chartType);
    FillRanges(nameRgs, cateRgs, valRgs);
}

void RemoteChartLinkManager::SetBubbleSize(IKRanges* bubbleRgs)
{
    if (bubbleRgs) {
        m_spBubbleRgs.attach(bubbleRgs);
    }
}

/*!
* @brief:填充三项range的范围
*/
void RemoteChartLinkManager::FillRanges(IKRanges* nameRgs, IKRanges* cateRgs, IKRanges* valRgs)
{
    if (nameRgs) {
       m_spNameRgs.attach(nameRgs);
    }

    if (cateRgs) {
        m_spCateRgs.attach(cateRgs);
    }

    if (valRgs) {
        m_spValueRgs.attach(valRgs);
    }

    if (m_spBubbleRgs) {
        m_spBubbleRgs.clear();
    }
}

bool RemoteChartLinkManager::CheckRange()
{
    if (!m_spNameRgs || !m_spValueRgs || !m_spCateRgs) {
        return false;
    }

    UINT nameCnt = 0;
    m_spNameRgs->GetCount(&nameCnt);
    if (nameCnt <= 0) {
        return false;
    }

    UINT cateCnt = 0;
    m_spCateRgs->GetCount(&cateCnt);
    if (cateCnt <= 0) {
        return false;
    }

    UINT valueCnt = 0;
    m_spValueRgs->GetCount(&valueCnt);
    if (valueCnt <= 0) {
        return false;
    }

    if (nameCnt != valueCnt) {
        return false;
    }

    return true;
}

/*!
* @brief: 通知range更新订阅
*/
void RemoteChartLinkManager::NotifySubsriber(const QString& connId, const QString& userId, const QString& cuid)
{
    if (!m_pSubsriber) {
        return;
    }

    if (!CheckRange()) {
        return;
    }

    UINT count = 0;
    m_spNameRgs->GetCount(&count);

    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    SeriesDirection dir = CaclDirection(m_spCateRgs, m_spNameRgs);
    for (int index = 0; index < count; ++index) {

        const RANGE *pNameRange = nullptr;
        if (FAILED(m_spNameRgs->GetItem(index, NULL, &pNameRange))) {
            continue;
        }

        const RANGE *pValRange = nullptr;
        if (FAILED(m_spValueRgs->GetItem(index, NULL, &pValRange))) {
            continue;
        }

        const RANGE *pCateRange = nullptr;
        if (FAILED(m_spCateRgs->GetItem(index, NULL, &pCateRange))) {
            continue;
        }

        const RANGE* pBubbleSize = nullptr;
        if (m_spBubbleRgs) {
            m_spBubbleRgs->GetItem(index, NULL, &pBubbleSize);
        }

        NotifierGroup* group = m_pSubsriber->GetNotifyGroup(index);
        group->InitContext(m_pBook, m_sheetIdx, index);

        QString cateContext = chart::KDataSourceHelper::rangeToQString(spBookOp, pCateRange);
        RemoteCalcCateNotify* cateNotifier = group->cateNotifier;

        cateNotifier->SetDirection(dir);
        cateNotifier->RegNotify(cateContext, true);

        //series name
        QString nameContext = chart::KDataSourceHelper::rangeToQString(spBookOp, pNameRange);
        RemoteCalcNameNotify* nameNotifier = group->nameNotifier;

        nameNotifier->SetDirection(dir);
        nameNotifier->RegNotify(nameContext, true);

        //sereis value
        QString valContext = chart::KDataSourceHelper::rangeToQString(spBookOp, pValRange);
        RemoteCalcNotifyBase* valNotifier = group->valueNotifier;
        valNotifier->SetDirection(dir);
        valNotifier->RegNotify(valContext, true);

        //series bubble
        if (pBubbleSize) {
            QString bubbleFmla = chart::KDataSourceHelper::rangeToQString(spBookOp, pBubbleSize);
            RemoteCalcNotifyBase* bubbleNotifier = group->bubbleNotifier;
            bubbleNotifier->SetDirection(dir);
            bubbleNotifier->RegNotify(bubbleFmla, true);
        }
    }

    m_pSubsriber->InitUser(connId, userId, cuid);
    m_pSubsriber->SetGroupSize(count);

    NotifierGroup* pRegion = m_pSubsriber->GetRegionGroup();
    pRegion->InitContext(m_pBook, m_sheetIdx, -1);

    RANGE nameRg(GetBoundary(m_spNameRgs));
    RANGE valRg(GetBoundary(m_spValueRgs));
    RANGE cateRg(GetBoundary(m_spCateRgs));

    QString nameFmla = chart::KDataSourceHelper::rangeToQString(spBookOp, &nameRg);
    QString valueFmla = chart::KDataSourceHelper::rangeToQString(spBookOp, &valRg);
    QString cateFmla = chart::KDataSourceHelper::rangeToQString(spBookOp, &cateRg);

    m_pSubsriber->UpdateRegion(dir, nameFmla, valueFmla, cateFmla);
}

//针对数据透视表作为图表数据源
bool RemoteChartLinkManager::CheckRangeInPivotTable(const RANGE& range)
{
    ks_stdptr<ISheet> spWorkSheet;
    m_pBook->GetSheet(m_sheetIdx, &spWorkSheet);
    if (!spWorkSheet) {
        return false;
    }

    ks_stdptr<pivot_core::IPivotTable> spPivotTable;
    pivot_helper::GetCorePivotTableByCell(m_pBook, range, &spPivotTable);
    if (spPivotTable.get()) {
        return true;
    }

    return false;
}

bool RemoteChartLinkManager::ProcessRangeDivide()
{
    ks_stdptr<IKRanges> spRanges;
    if(!CheckNeedDivide(&spRanges)) {
        return true;
    }

    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    SeriesDirection dir = CaclDirection(m_spCateRgs, m_spNameRgs);
    if (dir == sdirAuto) {
        return false;
    }

    NotifierGroup* pRegion = m_pSubsriber->GetRegionGroup();
    pRegion->InitContext(m_pBook, m_sheetIdx, -1);

    int bookId = 0;
    RANGE nameRg(GetBoundary(m_spNameRgs));
    RANGE valRg(GetBoundary(m_spValueRgs, &bookId));
    RANGE cateRg(GetBoundary(m_spCateRgs));

    QString nameFmla = chart::KDataSourceHelper::rangeToQString(spBookOp, &nameRg);
    QString valueFmla = chart::KDataSourceHelper::rangeToQString(spBookOp, &valRg);
    QString cateFmla = chart::KDataSourceHelper::rangeToQString(spBookOp, &cateRg);

    m_pSubsriber->UpdateRegion(dir, nameFmla, valueFmla, cateFmla);

    ks_stdptr<IKRanges> spDivideRanges;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spDivideRanges);
    if (!spDivideRanges) {
        return false;
    }

    valRg.SetRowFrom(valRg.RowFrom() -1);
    valRg.SetColFrom(valRg.ColFrom() -1);
    spDivideRanges->Append(bookId, valRg);

    // 根据选中范围切分分类轴和各系列的名字/值所引用的单元格
	chart::RangeDivideResult divideResult;
    chart::ChartType chartType = m_pSubsriber->GetChartType();
    et_sptr<chart::KDataSourceRangesDivider> rangeDivider(chart::KDividerChooser::createDivider(spBookOp, spDivideRanges, chartType));

    rangeDivider->SetWebwpp(true);
    CELL valueBoundary = {-1, -1};
    bool success = rangeDivider->divide(dir, valueBoundary, 1, 1, &divideResult);
    if (success) {
        CELL nameBound = CalcSeriesBoundary(m_spNameRgs);
        CompileDivideRanges(divideResult, nameBound);
    }

    return success;
}

void RemoteChartLinkManager::CompileDivideRanges(const chart::RangeDivideResult& divideResult, const CELL boundary)
{
    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    size_t count = divideResult.seriesRanges.size();
    m_pSubsriber->SetGroupSize(count);

    bool bMarkFinish = false;
    ChartMessgeProducer* producer = m_pSubsriber->GetProducer();

    SeriesDirection dir = divideResult.direction;
    IKRanges *cateRgs = m_spCateRgs;
    // 根据切分结果创建分类Source和系列Source
    for (size_t idx = 0; idx < count; ++idx) {
        const chart::RangeDivideResult::SeriesRanges& ranges = divideResult.seriesRanges[idx];

        IKRanges* nameRgs = ranges.spNameRange;
        IKRanges* valueRgs = ranges.spValueRanges;
        IKRanges* bubbleRgs = ranges.spBubbleRanges;

        NotifierGroup* group = m_pSubsriber->GetNotifyGroup(idx);
        group->InitContext(m_pBook, m_sheetIdx, idx);

        if (cateRgs) {
            ks_stdptr<ITokenVectorInstant> spCategoryFmla;
            QString cateContext = chart::KDataSourceHelper::rangesToQString(spBookOp, cateRgs, false, &spCategoryFmla);
            //register notify(远端云文档更新监听需要)

            RemoteCalcCateNotify* cateNotifier = group->cateNotifier;

            cateNotifier->SetDirection(divideResult.direction);
            cateNotifier->RegNotify(cateContext);
            if (producer) {
                producer->BeginMarkDirty(idx, CalcNotifyCategory);
            }
            bMarkFinish = true;
        }

        if (nameRgs) {
            INT bookID = 0;
            const RANGE* pRange = nullptr;
            valueRgs->GetItem(0, &bookID, &pRange);
            RANGE nameRg(*pRange);

            if (dir == sdirColumn) {
                nameRg.SetRowFromTo(boundary.row);
            } else if (dir == sdirRow) {
                nameRg.SetColFromTo(boundary.col);
            }

            nameRgs->Clear();
            nameRgs->Append(bookID, nameRg);

            ks_stdptr<ITokenVectorInstant> spNameFmla;
            QString nameContext = chart::KDataSourceHelper::rangesToQString(spBookOp, nameRgs, false, &spNameFmla);
            //register notify(远端云文档更新监听需要)

            RemoteCalcNameNotify* nameNotifier = group->nameNotifier;

            nameNotifier->SetDirection(divideResult.direction);
            nameNotifier->RegNotify(nameContext);
            if (producer) {
                producer->BeginMarkDirty(idx, CalcNotifyName);
            }
            bMarkFinish = true;
        }

        if (valueRgs) {
            ks_stdptr<ITokenVectorInstant> spValueFmla;
            QString valueContext = chart::KDataSourceHelper::rangesToQString(spBookOp, valueRgs, false, &spValueFmla);
            //register notify(远端云文档更新监听需要)
            RemoteCalcNotifyBase* valueNotifier = group->valueNotifier;

            valueNotifier->SetDirection(divideResult.direction);
            valueNotifier->RegNotify(valueContext);

            if (producer) {
                producer->BeginMarkDirty(idx, CalcNotifyValue);
            }
            bMarkFinish = true;
        }

        chart::ChartType chartType = m_pSubsriber->GetChartType();
        if (chart::ChartMainTypeFromChartType(chartType) == chart::ChartMainTypeBubble) {
            QString bubbleContext;
            ks_stdptr<ITokenVectorInstant> spBubbleFmla;

            if (bubbleRgs) {
                bubbleContext = chart::KDataSourceHelper::rangesToQString(spBookOp, bubbleRgs, false, &spBubbleFmla);
            } else {
                size_t dataPointCnt = 0;
                UINT valRgCount = 0;
                valueRgs->GetCount(&valRgCount);
                for (size_t rIdx = 0; rIdx < valRgCount; ++rIdx) {
                    INT bookID;
                    const RANGE* valRg;
                    valueRgs->GetItem(rIdx, &bookID, &valRg);
                    dataPointCnt += valRg->Height() * valRg->Width();
                }

                bubbleContext = QString("={1");
                for (size_t pIdx = 1; pIdx < dataPointCnt; ++pIdx) {
                    bubbleContext.append(QString(",1"));
                    bubbleContext.append(QString("}"));
                }
            }

            //register notify(远端云文档更新监听需要
            RemoteCalcNotifyBase* bubbleNotifier = group->bubbleNotifier;

            bubbleNotifier->SetDirection(divideResult.direction);
            bubbleNotifier->RegNotify(bubbleContext);

            if (producer) {
                producer->BeginMarkDirty(idx, CalcNotifyBubble);
            }
            bMarkFinish = true;
        }
    }

    if (bMarkFinish) {
        producer->EndMarkDirty();
    }
}

RANGE RemoteChartLinkManager::GetBoundary(IKRanges* pRanges, int* pBookId)
{
    INT bookID = 0;
    UINT rgCount = 0;
    pRanges->GetCount(&rgCount);

    const RANGE *range = nullptr;
    pRanges->GetItem(0, &bookID, &range);
    RANGE rg(*range);

    for (uint idx = 1; idx < rgCount; ++idx) {
        pRanges->GetItem(idx, &bookID, &range);
        if (range->IsValid()) {
            if (range->RowFrom() < rg.RowFrom()) {
                rg.SetRowFrom(range->RowFrom());
            }
            if (range->RowTo() > rg.RowTo()) {
                rg.SetRowTo(range->RowTo());
            }

            if (range->ColFrom() < rg.ColFrom()) {
                rg.SetColFrom(range->ColFrom());
            }
            if (range->ColTo() > rg.ColTo()) {
                rg.SetColTo(range->ColTo());
            }
        }
    }

    if (pBookId) {
        *pBookId = bookID;
    }

    return rg;
}

bool RemoteChartLinkManager::CheckNeedDivide(IKRanges** ppRanges)
{
    ks_stdptr<IKRanges> spRanges;
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&spRanges);
    if (!spRanges) {
        return false;
    }

    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    spRanges->Append(m_spNameRgs);
    spRanges->Append(m_spValueRgs);
    spRanges->Append(m_spCateRgs);

    UINT rgCount = 0;
    spRanges->GetCount(&rgCount);
    if (rgCount <= 0) {
        return false;
    }

    int bookID = 0;
    const RANGE *pRange = nullptr;
    spRanges->GetItem(0, &bookID, &pRange);

    bool bDivide = true;
    if (rgCount == 0 && pRange->IsSingleCell()) {
        bDivide = false;
    }

    if (chart::KDataSourceHelper::isSingleEmptyCellOrMergedCell(spBookOp, spRanges)) {
        if (chart::ChartMainTypeFromChartType(m_pSubsriber->GetChartType()) == chart::ChartMainTypeStock) {
            return false;
        }
    }

    *ppRanges = spRanges.detach();
    return bDivide;
}

void RemoteChartLinkManager::DivideByFmla(const QString& nameFmla, const QString& cateFmla, const QString& valFmla)
{
    ks_stdptr<IBookOp> spBookOp;
	m_pBook->GetOperator(&spBookOp);

    ks_stdptr<IKRanges> spNameRanges;
    bool bOk = chart::KDataSourceHelper::qstringToRanges(spBookOp, nameFmla, &spNameRanges);
    if (!bOk) {
        return;
    }

    ks_stdptr<IKRanges> spCateRanges;
    bOk = chart::KDataSourceHelper::qstringToRanges(spBookOp, cateFmla, &spCateRanges);
    if (!bOk) {
        return;
    }

    ks_stdptr<IKRanges> spValueRanges;
    bOk = chart::KDataSourceHelper::qstringToRanges(spBookOp, valFmla, &spValueRanges);
    if (!bOk) {
        return;
    }

    FillRanges(spNameRanges.detach(), spCateRanges.detach(), spValueRanges.detach());
    ProcessRangeDivide();
}

bool RemoteChartLinkManager::ExportChartData()
{
    if (!m_pSubsriber) {
        return false;
    }

    return m_pSubsriber->DoExportData();
}

}
