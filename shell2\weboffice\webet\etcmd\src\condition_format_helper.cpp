#include "etstdafx.h"
#include "condition_format_cache.h"
#include "wo/bw_helper.h"
#include "wo/sa_helper.h"
#include "wo/core_stake.h"
#include "et_task_detail_xf.h"
#include "condition_format_helper.h"
#include "condition_format_wrapper.h"
#include "applogic/et_apihost.h"
#include "webbase/binvariant/binvarobj.h"
#include "wo/serial_xf_helper.h"


void optColor(ISerialAcceptor* acpt, KeyName tag, const EtColor& ec, IBook* bk)
{
	wo::SerialColour(acpt, tag, bk, ec);
}

void optFill(ISerialAcceptor* acpt, KeyName tag, const EtFill* p, const KXFMASK& mask, IBook* bk)
{
	wo::SerialFill(acpt, tag, bk, p);
}

void optXf(ISerialAcceptor* acpt, KeyName tag, const XF* pXF, const KXFMASK& mask, IBook* ptrBook)
{
	wo::SerialXF(acpt, tag, ptrBook, pXF, &mask, true);
}

void optColor(binary_wo::VarObj& obj, KeyName tag, const EtColor& ec, IBook* bk)
{
	wo::SerialColour(obj, tag, bk, ec);
}

void optFill(binary_wo::VarObj& obj, KeyName tag, const EtFill* p, const KXFMASK& mask, IBook* bk)
{
	wo::SerialFill(obj, tag, bk, p);
}

void optXf(binary_wo::VarObj& obj, KeyName tag, const XF* pXF, const KXFMASK& mask, IBook* ptrBook)
{
	wo::SerialXF(obj, tag, ptrBook, pXF, &mask, true);
}

static LPCWSTR iconSetToStr(CF_ICONSET icon)
{
	switch (icon)
	{
	case CFIS_None:				return __X("null");

	case CFIS_3Arrows:			return __X("3Arrows");
	case CFIS_3ArrowsGray:		return __X("3ArrowsGray");
	case CFIS_3Flags:			return __X("3Flags");
	case CFIS_3TrafficLights1:	return __X("3TrafficLights1");
	case CFIS_3TrafficLights2:	return __X("3TrafficLights2");
	case CFIS_3Signs:			return __X("3Signs");
	case CFIS_3Symbols:			return __X("3Symbols");
	case CFIS_3Symbols2:		return __X("3Symbols2");
	case CFIS_3Stars:			return __X("3Stars");
	case CFIS_3Triangles:		return __X("3Triangles");

	case CFIS_4Arrows:			return __X("4Arrows");
	case CFIS_4ArrowsGray:		return __X("4ArrowsGray");
	case CFIS_4RedToBlack:		return __X("4RedToBlack");
	case CFIS_4CRV:				return __X("4CRV");
	case CFIS_4TrafficLights:	return __X("4TrafficLights");

	case CFIS_5Arrows:			return __X("5Arrows");
	case CFIS_5ArrowsGray:		return __X("5ArrowsGray");
	case CFIS_5CRV:				return __X("5CRV");
	case CFIS_5Quarters:		return __X("5Quarters");
	case CFIS_5Boxes:			return __X("5Boxes");

	default:
		ASSERT(FALSE);
		return __X("null");
	}
}

void optIconSet(ISerialAcceptor* acpt, binary_wo::VarObj obj, KeyName tag, const EtIconSet* p)
{
	if (obj.type() == binary_wo::typeInvalid)
	{
		if (tag != nullptr)
			acpt->addKey(tag);
		acpt->beginStruct();
		acpt->addString("icoSet", iconSetToStr(p->icoSet));
		acpt->addUint32("icoIdx", p->icoIdx);
		acpt->endStruct();
	}
	else
	{
		if (tag != nullptr)
			obj = obj.add_field_struct(tag);
		obj.add_field_str("icoSet", iconSetToStr(p->icoSet));
		obj.add_field_uint32("icoIdx", p->icoIdx);
	}
}

void optDataBar(ISerialAcceptor* acpt, KeyName tag, const EtDataBar* p, IBook* bk)
{
	if (tag)
		acpt->addKey(tag);
	acpt->beginStruct();
		optColor(acpt, "clrBar", p->clrBar, bk);
		optColor(acpt, "clrFrame", p->clrFrame, bk);
		optColor(acpt, "clrAxis", p->clrAxis, bk);
		acpt->addInt32("axisPos", p->axisPos);
		acpt->addInt32("barSize", p->barSize);
	acpt->endStruct();
}

HRESULT ApplyConditionalFormat(ISerialAcceptor* acpt, const RANGE& rgApply, IBook* ptrBook)
{
	ASSERT(acpt && ptrBook);

	ks_stdptr<IBookOp> pBookOp;
	ptrBook->GetOperator(&pBookOp);

	pBookOp->BeginRequestAppliedFormatX(rgApply, AFF_IgnoreHiddenRow);
	IDX sheetIdx = rgApply.SheetFrom();

	std::unordered_map<const XF*, size_t> mapXF;
	std::vector<std::pair<const XF*, KXFMASK>> vecXF;
	acpt->addKey("cfItems");
	acpt->beginArray();
	for (ROW row = rgApply.RowFrom(); row <= rgApply.RowTo(); row++)
	{
		for (COL col = rgApply.ColFrom(); col <= rgApply.ColTo(); col++)
		{
			CFExtFmt cfExtFmt;
			KXFMASK mask(XFMASK::_catAll, XFMASK::_catFontAll);
			const XF* pXF = NULL;
			ParamGetCellFormat param;
			param.pMask = &mask;
			HRESULT hr = pBookOp->GetCellAppliedFormatX(sheetIdx, row, col, &pXF, &cfExtFmt, &param);	//这里取的条件格式合成单元格样式的XF
			if (SUCCEEDED(hr) && param.bHasApplyCF && pXF)
			{
				acpt->beginStruct();
				{
					acpt->addInt32("row", row);
					acpt->addInt32("col", col);
					size_t id = 0;
					if (mapXF.count(pXF) == 0)
					{
						id = mapXF.size();
						mapXF[pXF] = id;
						vecXF.push_back(std::make_pair(pXF, mask));
					}
					else
					{
						id = mapXF[pXF];
					}
					acpt->addUint32("id", id);
					
					if (cfExtFmt.mask.fIconSet || cfExtFmt.mask.fDataBar)
					{
						if (cfExtFmt.mask.fIconSet)
							optIconSet(acpt, binary_wo::VarObj(), "iconSet", &cfExtFmt.iconSet);
						if (cfExtFmt.mask.fDataBar)
							optDataBar(acpt, "dataBar", &cfExtFmt.dataBar, ptrBook);
						acpt->addBool("fHideValue", cfExtFmt.mask.fHideValue);
						acpt->addBool("fFrame", cfExtFmt.mask.fFrame);
						acpt->addBool("fSolidFill", cfExtFmt.mask.fSolidFill);
					}
				}
				acpt->endStruct();
			}
		}
	}
	acpt->endArray();

	acpt->addKey("XFs");
	acpt->beginArray();
	for (auto it = vecXF.begin(); it != vecXF.end(); ++it)
		optXf(acpt, nullptr, it->first, it->second, ptrBook);
	acpt->endArray();
	pBookOp->EndRequestAppliedFormatX();
	return S_OK;
}

HRESULT ApplyConditionalFormatByCache(ISerialAcceptor* acpt, const RANGE& rgApply, IKWorkbook* pWorkbook, ConditionFormatCache *pCache)
{
	ASSERT(acpt);

	std::unordered_map<const XF*, size_t> mapXF;
	std::vector<std::pair<const XF*, const XFMASK *>> vecXF;
	IDX sheetIdx = rgApply.SheetFrom();
	IKWorksheet* pWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	ks_stdptr<IKXFGridCache> spXFGrid;
	pCache->getXFGridCache(pWorksheet, &spXFGrid);
	acpt->addKey("cfItems");
	acpt->beginArray();
	for (ROW row = rgApply.RowFrom(); row <= rgApply.RowTo(); row++)
	{
		for (COL col = rgApply.ColFrom(); col <= rgApply.ColTo(); col++)
		{
			const CFExtFmt* pExt;
			const XF* pXF = nullptr;
			const XFMASK *pMask = nullptr;
			if (SUCCEEDED(spXFGrid->getCFEx(row, col, &pXF, &pMask, &pExt)))
			{
				acpt->beginStruct();
				{
					acpt->addInt32("row", row);
					acpt->addInt32("col", col);
					size_t id = 0;
					if (mapXF.count(pXF) == 0)
					{
						id = mapXF.size();
						mapXF[pXF] = id;
						vecXF.push_back(std::make_pair(pXF, pMask));
					}
					else
					{
						id = mapXF[pXF];
					}
					acpt->addUint32("id", id);
					
					if (pExt && (pExt->mask.fIconSet || pExt->mask.fDataBar))
					{
						if (pExt->mask.fIconSet)
							optIconSet(acpt, binary_wo::VarObj(), "iconSet", &pExt->iconSet);
						if (pExt->mask.fDataBar)
							optDataBar(acpt, "dataBar", &pExt->dataBar, pWorkbook->GetBook());
						acpt->addBool("fHideValue", pExt->mask.fHideValue);
						acpt->addBool("fFrame", pExt->mask.fFrame);
						acpt->addBool("fSolidFill", pExt->mask.fSolidFill);
					}
				}
				acpt->endStruct();
			}
		}
	}
	acpt->endArray();

	acpt->addKey("XFs");
	acpt->beginArray();
	for (auto it = vecXF.begin(); it != vecXF.end(); ++it)
		optXf(acpt, nullptr, it->first, *it->second, pWorkbook->GetBook());
	acpt->endArray();
	return S_OK;
}

Wo_CFOperator GetConditionOperator(WebStr cvTy)
{
	if (xstrcmp(cvTy, __X("blanksCondition")) == 0)			return Wo_BlanksCondition;
	else if (xstrcmp(cvTy, __X("noBlanksCondition")) == 0)	return Wo_NoBlanksCondition;
	else if (xstrcmp(cvTy, __X("stringConclude")) == 0)		return Wo_StringConclude;
	else if (xstrcmp(cvTy, __X("stringExclude")) == 0)		return Wo_StringExclude;
	else if (xstrcmp(cvTy, __X("stringBeginsWith")) == 0)	return Wo_StringBeginsWith;
	else if (xstrcmp(cvTy, __X("stringEndsWith")) == 0)		return Wo_StringEndsWith;
	else if (xstrcmp(cvTy, __X("uniqueValues")) == 0)		return Wo_UniqueValues;
	else if (xstrcmp(cvTy, __X("duplicateValues")) == 0)	return Wo_DuplicateValues;
	else if (xstrcmp(cvTy, __X("errors")) == 0)				return Wo_Errors;
	else if (xstrcmp(cvTy, __X("noErrors")) == 0)			return Wo_NoErrors;
	
	else if (xstrcmp(cvTy, __X("between")) == 0)			return Wo_Between;
	else if (xstrcmp(cvTy, __X("notBetween")) == 0)			return Wo_NotBetween;
	else if (xstrcmp(cvTy, __X("equal")) == 0)				return Wo_Equal;
	else if (xstrcmp(cvTy, __X("notEqual")) == 0)			return Wo_NotEqual;
	else if (xstrcmp(cvTy, __X("greater")) == 0)			return Wo_Greater;
	else if (xstrcmp(cvTy, __X("less")) == 0)				return Wo_Less;
	else if (xstrcmp(cvTy, __X("greaterEqual")) == 0)		return Wo_GreaterEqual;
	else if (xstrcmp(cvTy, __X("lessEqual")) == 0)			return Wo_LessEqual;

	else if (xstrcmp(cvTy, __X("top10")) == 0)				return Wo_Top10;
	else if (xstrcmp(cvTy, __X("top10%")) == 0)			return Wo_Top10Per;
	else if (xstrcmp(cvTy, __X("last10")) == 0)			return Wo_Last10;
	else if (xstrcmp(cvTy, __X("last10%")) == 0)				return Wo_Last10Per;
	else if (xstrcmp(cvTy, __X("aboveAverage")) == 0)		return Wo_AboveAverage;
	else if (xstrcmp(cvTy, __X("belowAverage")) == 0)			return Wo_BelowAverage;

	else if (xstrcmp(cvTy, __X("today")) == 0)				return Wo_Today;
	else if (xstrcmp(cvTy, __X("yesterday")) == 0)			return Wo_Yesterday;
	else if (xstrcmp(cvTy, __X("last7Days")) == 0)			return Wo_Last7Days;
	else if (xstrcmp(cvTy, __X("thisWeek")) == 0)			return Wo_ThisWeek;
	else if (xstrcmp(cvTy, __X("lastWeek")) == 0)			return Wo_LastWeek;
	else if (xstrcmp(cvTy, __X("lastMonth")) == 0)			return Wo_LastMonth;
	else if (xstrcmp(cvTy, __X("tomorrow")) == 0)			return Wo_Tomorrow;
	else if (xstrcmp(cvTy, __X("nextWeek")) == 0)			return Wo_NextWeek;
	else if (xstrcmp(cvTy, __X("nextMonth")) == 0)			return Wo_NextMonth;
	else if (xstrcmp(cvTy, __X("thisMonth")) == 0)			return Wo_ThisMonth;
	
	ASSERT(FALSE);
	return Wo_BlanksCondition;
}

XlConditionValueTypes DecodeConditionValueTypes(WebStr strCvTy)
{
	if (xstrcmp(strCvTy, __X("xlConditionValueNone")) == 0) return xlConditionValueNone;
	else if (xstrcmp(strCvTy, __X("xlConditionValueNumber")) == 0) return xlConditionValueNumber;
	else if (xstrcmp(strCvTy, __X("xlConditionValueLowestValue")) == 0) return xlConditionValueLowestValue;
	else if (xstrcmp(strCvTy, __X("xlConditionValueHighestValue")) == 0) return xlConditionValueHighestValue;
	else if (xstrcmp(strCvTy, __X("xlConditionValuePercent")) == 0) return xlConditionValuePercent;
	else if (xstrcmp(strCvTy, __X("xlConditionValueFormula")) == 0) return xlConditionValueFormula;
	else if (xstrcmp(strCvTy, __X("xlConditionValuePercentile")) == 0) return xlConditionValuePercentile;
	else if (xstrcmp(strCvTy, __X("xlConditionValueAutomaticMin")) == 0) return xlConditionValueAutomaticMin;
	else if (xstrcmp(strCvTy, __X("xlConditionValueAutomaticMax")) == 0) return xlConditionValueAutomaticMax;
	else return xlConditionValueNone;
}

WebStr EncodeConditionValueTypes(XlConditionValueTypes cvTy)
{
	switch (cvTy)
	{
	case xlConditionValueNone:			return __X("xlConditionValueNone");
	case xlConditionValueNumber:		return __X("xlConditionValueNumber");
	case xlConditionValueLowestValue:	return __X("xlConditionValueLowestValue");
	case xlConditionValueHighestValue:	return __X("xlConditionValueHighestValue");
	case xlConditionValuePercent:		return __X("xlConditionValuePercent");
	case xlConditionValueFormula:		return __X("xlConditionValueFormula");
	case xlConditionValuePercentile:	return __X("xlConditionValuePercentile");
	case xlConditionValueAutomaticMin:	return __X("xlConditionValueAutomaticMin");
	case xlConditionValueAutomaticMax:	return __X("xlConditionValueAutomaticMax");
	}
	return __X("");
}

XlConditionValueTypes GetConditionValueTypes(WebStr cvTy)
{
	if (xstrcmp(cvTy, __X("xlConditionValueNone")) == 0)
	{
		return xlConditionValueNone;
	}
	else if (xstrcmp(cvTy, __X("xlConditionValueNumber")) == 0)
	{
		return xlConditionValueNumber;
	}
	else if (xstrcmp(cvTy, __X("xlConditionValueLowestValue")) == 0)
	{
		return xlConditionValueLowestValue;
	}
	else if (xstrcmp(cvTy, __X("xlConditionValueHighestValue")) == 0)
	{
		return xlConditionValueHighestValue;
	}
	else if (xstrcmp(cvTy, __X("xlConditionValuePercent")) == 0)
	{
		return xlConditionValuePercent;
	}
	else if (xstrcmp(cvTy, __X("xlConditionValueFormula")) == 0)
	{
		return xlConditionValueFormula;
	}
	else if (xstrcmp(cvTy, __X("xlConditionValuePercentile")) == 0)
	{
		return xlConditionValuePercentile;
	}
	else if (xstrcmp(cvTy, __X("xlConditionValueAutomaticMin")) == 0)
	{
		return xlConditionValueAutomaticMin;
	}
	else if (xstrcmp(cvTy, __X("xlConditionValueAutomaticMax")) == 0)
	{
		return xlConditionValueAutomaticMax;
	}

	ASSERT(FALSE);
	return xlConditionValueNone;
}

WebStr EncodeConditionType(ETFormatConditionType type)
{
	switch (type)
	{
	case etCellValue:	return __X("etCellValue");
	case etExpression:	return __X("etExpression");
	case etColorScale:	return __X("etColorScale");
	case etDatabar:		return __X("etDatabar");
	case etTop10:		return __X("etTop10");
	case etIconSets:	return __X("etIconSets");
	case etUniqueValues:return __X("etUniqueValues");
	case etTextString:	return __X("etTextString");
	case etBlanksCondition:	return __X("etBlanksCondition");
	case etTimePeriod:	return __X("etTimePeriod");
	case etAboveAverageCondition: return __X("etAboveAverageCondition");
	case etNoBlanksCondition: return __X("etNoBlanksCondition");
	case etErrorsCondition: return __X("etErrorsCondition");
	case etNoErrorsCondition: return __X("etNoErrorsCondition");
	}
	return __X("");
}

Wo_FormatConditionType GetConditionType(WebStr strType)
{
	if (xstrcmp(strType, __X("containsValue")) == 0)
	{
		return Wo_ContainsValue;
	}
	else if (xstrcmp(strType, __X("valueRange")) == 0)
	{
		return Wo_ValueRange;
	}
	else if (xstrcmp(strType, __X("rankAverage")) == 0)
	{
		return Wo_RankAverage;
	}
	else if (xstrcmp(strType, __X("timePeriod")) == 0)
	{
		return Wo_TimePeriod;
	}
	else if (xstrcmp(strType, __X("expression")) == 0)
	{
		return Wo_Expression;
	}
	
	ASSERT(FALSE);
	return Wo_ContainsValue;
}

void ReadColorScale(binary_wo::VarObj& itemObj, CondFormatItem& item)
{
	binary_wo::VarObj cs = itemObj.get_s("colorScale");
	item.cs.b3ColorScale = cs.field_bool("b3ColorScale");

	binary_wo::VarObj arrCv = cs.get_s("arrCv");
	binary_wo::VarObj arrClr = cs.get_s("arrClr");

	int nLen1 = arrCv.arrayLength();
	int nLen2 = arrClr.arrayLength();

	for (int i = 0; i < nLen1; i++)
	{
		binary_wo::VarObj uicfvo = arrCv.at(i);
		item.cs.arrCv[i].bGte = uicfvo.field_bool("bGte");
		item.cs.arrCv[i].val = QString::fromUtf16(uicfvo.field_str("val"));
		item.cs.arrCv[i].cvTy;// = GetConditionOperator(uicfvo.field_str("cvTy"));
	}

	for (int i = 0; i < nLen2; i++)
	{
		binary_wo::VarObj color = arrClr.at(i);
		wo::DecodeColor(color, item.cs.arrClr[i]);
	}
}

void EncodeColor(const EtColor& clr, binary_wo::VarObj& vClr)
{
	vClr.add_field_uint32("type", (DWORD)clr.getType());
	DWORD value = 0;
	switch (clr.getType())
	{
	case ectICV:
		value = clr.getICV();
		break;
	case ectTHEME:
		value = clr.getTheme();
		break;
	case ectARGB:
		value = clr.getARGB();
		break;
	case ectAUTO:
	case ectNONE:
		break;
	}
	vClr.add_field_double("tint", clr.getTint());
	vClr.add_field_uint32("value", value);
}

void EncodeColorScale(const UiCfColorScale& cs, binary_wo::VarObj& resObj)
{
	resObj.add_field_bool("b3ColorScale", cs.b3ColorScale);
	binary_wo::VarObj arrCv = resObj.add_field_array("arrCv", binary_wo::typeStruct);
	for (int i = 0; i < 3; i++)
	{
		binary_wo::VarObj uicfvo = arrCv.add_item_struct();
		uicfvo.add_field_bool("bGte", cs.arrCv[i].bGte);
		uicfvo.add_field_str("cvTy", EncodeConditionValueTypes(cs.arrCv[i].cvTy));
		uicfvo.add_field_str("val", krt::utf16(cs.arrCv[i].val));
	}
	binary_wo::VarObj arrClr = resObj.add_field_array("arrClr", binary_wo::typeStruct);
	for (int i = 0; i < 3; i++)
	{
		binary_wo::VarObj clr = arrClr.add_item_struct();
		clr.add_field_uint32("ect", cs.arrClr[i].getType());
		clr.add_field_double("tint", cs.arrClr[i].getTint());
		switch (cs.arrClr[i].getType())
		{
		case ectARGB:
			clr.add_field_uint32("value", cs.arrClr[i].getARGB());
			break;
		case ectICV:
			clr.add_field_uint32("value", cs.arrClr[i].getICV());
			break;
		case ectTHEME:
			clr.add_field_uint32("value", cs.arrClr[i].getTheme());
			break;
		default:
			clr.add_field_uint32("value", 0);
			break;
		}
	}
}

void exportColorScale(const UiCfColorScale& cs, ISerialAcceptor* acpt)
{
	if (!acpt)
		return;

	acpt->addKey("colorScale");
	acpt->beginStruct();
	{
		acpt->addBool("b3ColorScale", cs.b3ColorScale);

		acpt->addKey("arrCv");
		acpt->beginArray();
		for (int i = 0; i < 3; i++)
		{
			acpt->beginStruct();
			acpt->addBool("bGte", cs.arrCv[i].bGte);
			acpt->addString("cvTy", EncodeConditionValueTypes(cs.arrCv[i].cvTy));
			acpt->addString("val", krt::utf16(cs.arrCv[i].val));
			acpt->endStruct();
		}	
		acpt->endArray();

		acpt->addKey("arrClr");
		acpt->beginArray();
		for (int i = 0; i < 3; i++)
		{
			acpt->beginStruct();
			acpt->addUint32("ect", cs.arrClr[i].getType());
			acpt->addFloat64("tint", cs.arrClr[i].getTint());
			switch (cs.arrClr[i].getType())
			{
			case ectARGB:
				acpt->addUint32("value", cs.arrClr[i].getARGB());
				break;
			case ectICV:
				acpt->addUint32("value", cs.arrClr[i].getICV());
				break;
			case ectTHEME:
				acpt->addUint32("value", cs.arrClr[i].getTheme());
				break;
			default:
				acpt->addUint32("value", 0);
				break;
			}
			acpt->endStruct();
		}
		acpt->endArray();
	}
	acpt->endStruct();
}

XlIcon String_To_XlIcon(WebStr strType)
{
	if (xstrcmp(strType, __X("xlIconNoCellIcon")) == 0) return xlIconNoCellIcon;
	else if (xstrcmp(strType, __X("xlIconGreenUpArrow")) == 0) return xlIconGreenUpArrow;
	else if (xstrcmp(strType, __X("xlIconYellowSideArrow")) == 0) return xlIconYellowSideArrow;
	else if (xstrcmp(strType, __X("xlIconRedDownArrow")) == 0) return xlIconRedDownArrow;
	else if (xstrcmp(strType, __X("xlIconGrayUpArrow")) == 0) return xlIconGrayUpArrow;
	else if (xstrcmp(strType, __X("xlIconGraySideArrow")) == 0) return xlIconGraySideArrow;
	else if (xstrcmp(strType, __X("xlIconGrayDownArrow")) == 0) return xlIconGrayDownArrow;
	else if (xstrcmp(strType, __X("xlIconGreenFlag")) == 0) return xlIconGreenFlag;
	else if (xstrcmp(strType, __X("xlIconYellowFlag")) == 0) return xlIconYellowFlag;
	else if (xstrcmp(strType, __X("xlIconRedFlag")) == 0) return xlIconRedFlag;
	else if (xstrcmp(strType, __X("xlIconGreenCircle")) == 0) return xlIconGreenCircle;
	else if (xstrcmp(strType, __X("xlIconYellowCircle")) == 0) return xlIconYellowCircle;
	else if (xstrcmp(strType, __X("xlIconRedCircleWithBorder")) == 0) return xlIconRedCircleWithBorder;
	else if (xstrcmp(strType, __X("xlIconBlackCircleWithBorder")) == 0) return xlIconBlackCircleWithBorder;
	else if (xstrcmp(strType, __X("xlIconGreenTrafficLight")) == 0) return xlIconGreenTrafficLight;
	else if (xstrcmp(strType, __X("xlIconYellowTrafficLight")) == 0) return xlIconYellowTrafficLight;
	else if (xstrcmp(strType, __X("xlIconRedTrafficLight")) == 0) return xlIconRedTrafficLight;
	else if (xstrcmp(strType, __X("xlIconYellowTriangle")) == 0) return xlIconYellowTriangle;
	else if (xstrcmp(strType, __X("xlIconRedDiamond")) == 0) return xlIconRedDiamond;
	else if (xstrcmp(strType, __X("xlIconGreenCheckSymbol")) == 0) return xlIconGreenCheckSymbol;
	else if (xstrcmp(strType, __X("xlIconYellowExclamationSymbol")) == 0) return xlIconYellowExclamationSymbol;
	else if (xstrcmp(strType, __X("xlIconRedCrossSymbol")) == 0) return xlIconRedCrossSymbol;
	else if (xstrcmp(strType, __X("xlIconGreenCheck")) == 0) return xlIconGreenCheck;
	else if (xstrcmp(strType, __X("xlIconYellowExclamation")) == 0) return xlIconYellowExclamation;
	else if (xstrcmp(strType, __X("xlIconRedCross")) == 0) return xlIconRedCross;
	else if (xstrcmp(strType, __X("xlIconYellowUpInclineArrow")) == 0) return xlIconYellowUpInclineArrow;
	else if (xstrcmp(strType, __X("xlIconYellowDownInclineArrow")) == 0) return xlIconYellowDownInclineArrow;
	else if (xstrcmp(strType, __X("xlIconGrayUpInclineArrow")) == 0) return xlIconGrayUpInclineArrow;
	else if (xstrcmp(strType, __X("xlIconGrayDownInclineArrow")) == 0) return xlIconGrayDownInclineArrow;
	else if (xstrcmp(strType, __X("xlIconRedCircle")) == 0) return xlIconRedCircle;
	else if (xstrcmp(strType, __X("xlIconPinkCircle")) == 0) return xlIconPinkCircle;
	else if (xstrcmp(strType, __X("xlIconGrayCircle")) == 0) return xlIconGrayCircle;
	else if (xstrcmp(strType, __X("xlIconBlackCircle")) == 0) return xlIconBlackCircle;
	else if (xstrcmp(strType, __X("xlIconCircleWithOneWhiteQuarter")) == 0) return xlIconCircleWithOneWhiteQuarter;
	else if (xstrcmp(strType, __X("xlIconCircleWithTwoWhiteQuarters")) == 0) return xlIconCircleWithTwoWhiteQuarters;
	else if (xstrcmp(strType, __X("xlIconCircleWithThreeWhiteQuarters")) == 0) return xlIconCircleWithThreeWhiteQuarters;
	else if (xstrcmp(strType, __X("xlIconWhiteCircleAllWhiteQuarters")) == 0) return xlIconWhiteCircleAllWhiteQuarters;
	else if (xstrcmp(strType, __X("xlIcon0Bars")) == 0) return xlIcon0Bars;
	else if (xstrcmp(strType, __X("xlIcon1Bar")) == 0) return xlIcon1Bar;
	else if (xstrcmp(strType, __X("xlIcon2Bars")) == 0) return xlIcon2Bars;
	else if (xstrcmp(strType, __X("xlIcon3Bars")) == 0) return xlIcon3Bars;
	else if (xstrcmp(strType, __X("xlIcon4Bars")) == 0) return xlIcon4Bars;
	else if (xstrcmp(strType, __X("xlIconGoldStar")) == 0) return xlIconGoldStar;
	else if (xstrcmp(strType, __X("xlIconHalfGoldStar")) == 0) return xlIconHalfGoldStar;
	else if (xstrcmp(strType, __X("xlIconSilverStar")) == 0) return xlIconSilverStar;
	else if (xstrcmp(strType, __X("xlIconGreenUpTriangle")) == 0) return xlIconGreenUpTriangle;
	else if (xstrcmp(strType, __X("xlIconYellowDash")) == 0) return xlIconYellowDash;
	else if (xstrcmp(strType, __X("xlIconRedDownTriangle")) == 0) return xlIconRedDownTriangle;
	else if (xstrcmp(strType, __X("xlIcon4FilledBoxes")) == 0) return xlIcon4FilledBoxes;
	else if (xstrcmp(strType, __X("xlIcon3FilledBoxes")) == 0) return xlIcon3FilledBoxes;
	else if (xstrcmp(strType, __X("xlIcon2FilledBoxes")) == 0) return xlIcon2FilledBoxes;
	else if (xstrcmp(strType, __X("xlIcon1FilledBox")) == 0) return xlIcon1FilledBox;
	else if (xstrcmp(strType, __X("xlIcon0FilledBoxes")) == 0) return xlIcon0FilledBoxes;
	else return xlIconNoCellIcon;
}

WebStr XlIcon_To_String(XlIcon type)
{
	switch (type)
	{
		case xlIconNoCellIcon: return __X("xlIconNoCellIcon");
		case xlIconGreenUpArrow: return __X("xlIconGreenUpArrow");
		case xlIconYellowSideArrow: return __X("xlIconYellowSideArrow");
		case xlIconRedDownArrow: return __X("xlIconRedDownArrow");
		case xlIconGrayUpArrow: return __X("xlIconGrayUpArrow");
		case xlIconGraySideArrow: return __X("xlIconGraySideArrow");
		case xlIconGrayDownArrow: return __X("xlIconGrayDownArrow");
		case xlIconGreenFlag: return __X("xlIconGreenFlag");
		case xlIconYellowFlag: return __X("xlIconYellowFlag");
		case xlIconRedFlag: return __X("xlIconRedFlag");
		case xlIconGreenCircle: return __X("xlIconGreenCircle");
		case xlIconYellowCircle: return __X("xlIconYellowCircle");
		case xlIconRedCircleWithBorder: return __X("xlIconRedCircleWithBorder");
		case xlIconBlackCircleWithBorder: return __X("xlIconBlackCircleWithBorder");
		case xlIconGreenTrafficLight: return __X("xlIconGreenTrafficLight");
		case xlIconYellowTrafficLight: return __X("xlIconYellowTrafficLight");
		case xlIconRedTrafficLight: return __X("xlIconRedTrafficLight");
		case xlIconYellowTriangle: return __X("xlIconYellowTriangle");
		case xlIconRedDiamond: return __X("xlIconRedDiamond");
		case xlIconGreenCheckSymbol: return __X("xlIconGreenCheckSymbol");
		case xlIconYellowExclamationSymbol: return __X("xlIconYellowExclamationSymbol");
		case xlIconRedCrossSymbol: return __X("xlIconRedCrossSymbol");
		case xlIconGreenCheck: return __X("xlIconGreenCheck");
		case xlIconYellowExclamation: return __X("xlIconYellowExclamation");
		case xlIconRedCross: return __X("xlIconRedCross");
		case xlIconYellowUpInclineArrow: return __X("xlIconYellowUpInclineArrow");
		case xlIconYellowDownInclineArrow: return __X("xlIconYellowDownInclineArrow");
		case xlIconGrayUpInclineArrow: return __X("xlIconGrayUpInclineArrow");
		case xlIconGrayDownInclineArrow: return __X("xlIconGrayDownInclineArrow");
		case xlIconRedCircle: return __X("xlIconRedCircle");
		case xlIconPinkCircle: return __X("xlIconPinkCircle");
		case xlIconGrayCircle: return __X("xlIconGrayCircle");
		case xlIconBlackCircle: return __X("xlIconBlackCircle");
		case xlIconCircleWithOneWhiteQuarter: return __X("xlIconCircleWithOneWhiteQuarter");
		case xlIconCircleWithTwoWhiteQuarters: return __X("xlIconCircleWithTwoWhiteQuarters");
		case xlIconCircleWithThreeWhiteQuarters: return __X("xlIconCircleWithThreeWhiteQuarters");
		case xlIconWhiteCircleAllWhiteQuarters: return __X("xlIconWhiteCircleAllWhiteQuarters");
		case xlIcon0Bars: return __X("xlIcon0Bars");
		case xlIcon1Bar: return __X("xlIcon1Bar");
		case xlIcon2Bars: return __X("xlIcon2Bars");
		case xlIcon3Bars: return __X("xlIcon3Bars");
		case xlIcon4Bars: return __X("xlIcon4Bars");
		case xlIconGoldStar: return __X("xlIconGoldStar");
		case xlIconHalfGoldStar: return __X("xlIconHalfGoldStar");
		case xlIconSilverStar: return __X("xlIconSilverStar");
		case xlIconGreenUpTriangle: return __X("xlIconGreenUpTriangle");
		case xlIconYellowDash: return __X("xlIconYellowDash");
		case xlIconRedDownTriangle: return __X("xlIconRedDownTriangle");
		case xlIcon4FilledBoxes: return __X("xlIcon4FilledBoxes");
		case xlIcon3FilledBoxes: return __X("xlIcon3FilledBoxes");
		case xlIcon2FilledBoxes: return __X("xlIcon2FilledBoxes");
		case xlIcon1FilledBox: return __X("xlIcon1FilledBox");
		case xlIcon0FilledBoxes: return __X("xlIcon0FilledBoxes");
		default: return __X("xlIconNoCellIcon");
	}
}

XlDataBarNegativeColorType String_To_XlDataBarNegativeColorType(WebStr strType)
{
	if (xstrcmp(strType, __X("xlDataBarColor")) == 0) return xlDataBarColor;
	else if (xstrcmp(strType, __X("xlDataBarSameAsPositive")) == 0) return xlDataBarSameAsPositive;
	else return xlDataBarColor;
}

WebStr XlDataBarNegativeColorType_To_String(XlDataBarNegativeColorType type)
{
	switch (type)
	{
		case xlDataBarColor: return __X("xlDataBarColor");
		case xlDataBarSameAsPositive: return __X("xlDataBarSameAsPositive");
		default: return __X("xlDataBarColor");
	}
}

XlDataBarAxisPosition String_To_XlDataBarAxisPosition(WebStr strAp)
{
	if (xstrcmp(strAp, __X("xlDataBarAxisAutomatic")) == 0) return xlDataBarAxisAutomatic;
	else if (xstrcmp(strAp, __X("xlDataBarAxisMidpoint")) == 0) return xlDataBarAxisMidpoint;
	else if (xstrcmp(strAp, __X("xlDataBarAxisNone")) == 0) return xlDataBarAxisNone;
	else return xlDataBarAxisAutomatic;
}

WebStr XlDataBarAxisPosition_To_String(XlDataBarAxisPosition ap)
{
	switch (ap)
	{
		case xlDataBarAxisAutomatic: return __X("xlDataBarAxisAutomatic");
		case xlDataBarAxisMidpoint: return __X("xlDataBarAxisMidpoint");
		case xlDataBarAxisNone: return __X("xlDataBarAxisNone");
		default: return __X("xlDataBarAxisAutomatic");
	}
}

UiDBDirection String_To_UiDBDirection(WebStr strDir)
{
	if (xstrcmp(strDir, __X("UiContext")) == 0) return UiContext;
	else if (xstrcmp(strDir, __X("UiL2R")) == 0) return UiL2R;
	else if (xstrcmp(strDir, __X("UiR2L")) == 0) return UiR2L;
	else return UiContext;
}

WebStr UiDBDirection_To_String(UiDBDirection dir)
{
	switch(dir)
	{
		case UiContext: return __X("UiContext");
		case UiL2R: return __X("UiL2R");
		case UiR2L: return __X("UiR2L");
		default: return __X("UiContext");
	}
}

void optUiCfVo_reverse(OUT UiCfVo& vo, IN const binary_wo::VarObj& cond)
{
	if (cond.has("bGte"))
		vo.bGte = cond.field_bool("bGte");
	vo.cvTy = DecodeConditionValueTypes(cond.field_str("type"));
	vo.val = QString::fromUtf16(cond.field_str("val"));
}

void optUiCfVo(ISerialAcceptor* acpt, KeyName tag, UiCfVo vo, bool bNeedGte)
{
	if (tag)
		acpt->addKey(tag);
	acpt->beginStruct();
		if (bNeedGte) 
			acpt->addBool("bGte", vo.bGte);
		acpt->addString("type", EncodeConditionValueTypes(vo.cvTy));
		acpt->addString("val", krt::utf16(vo.val));
	acpt->endStruct();
}

void optUiDataBarNegAxisSetting_reverse(OUT UiDataBarNegAxisSetting& setting, IN const binary_wo::VarObj& varSetting)
{
	setting.negFillType = String_To_XlDataBarNegativeColorType(varSetting.field_str("negFillType"));
	wo::DecodeColor(varSetting.get("clrNegativeFill"), setting.clrNegativeFill);
	setting.negBorderType = String_To_XlDataBarNegativeColorType(varSetting.field_str("negBorderType"));
	wo::DecodeColor(varSetting.get("clrNegativeBorder"), setting.clrNegativeBorder);
	setting.ap = String_To_XlDataBarAxisPosition(varSetting.field_str("ap"));
	wo::DecodeColor(varSetting.get("clrAxis"), setting.clrAxis);
}

void optUiDataBarNegAxisSetting(ISerialAcceptor* acpt, KeyName tag, UiDataBarNegAxisSetting setting, IBook *bk)
{
	if (tag)
		acpt->addKey(tag);
	acpt->beginStruct();
		acpt->addString("negFillType", XlDataBarNegativeColorType_To_String(setting.negFillType));
		optColor(acpt, "clrNegativeFill", setting.clrNegativeFill, bk);
		acpt->addString("negBorderType", XlDataBarNegativeColorType_To_String(setting.negBorderType));
		optColor(acpt, "clrNegativeBorder", setting.clrNegativeBorder, bk);
		acpt->addString("ap", XlDataBarAxisPosition_To_String(setting.ap));
		optColor(acpt, "clrAxis", setting.clrAxis, bk);
	acpt->endStruct();
}

CondFormatItem importCFItem(binary_wo::VarObj& cfItem)
{
	auto copy2Item = [](KXF &kxf, CondFormatItem *pItem)
	{
		pItem->ensureKXF();
		*pItem->pKXF = kxf;
	};

	CondFormatItem item;

	KXFMASK mask;
	KXF xf;
	binary_wo::VarObj varXf = cfItem.get_s("xf");
	wo::DecodeXf(varXf, xf, mask, true);
	xf.mask = mask;

	bool bMultiColor = false;
	if (cfItem.has("bMultiColor"))
		bMultiColor = cfItem.field_bool("bMultiColor");

	if (cfItem.has("formula1"))
		item.formula1 = QString::fromUtf16(cfItem.field_str("formula1"));
	if (cfItem.has("formula2"))
		item.formula2 = QString::fromUtf16(cfItem.field_str("formula2"));
	if (cfItem.has("cfRuleNotes"))
		item.strCfRuleNotes = QString::fromUtf16(cfItem.field_str("cfRuleNotes"));

	WOLOG_INFO << "[importCFItem] " << "item.formula1: " << item.formula1
									<< ", item.formula2: " << item.formula2
									<< ", item.cfRuleNotes: " << item.strCfRuleNotes;

	if (!bMultiColor)
	{
		ASSERT(cfItem.has("type") && cfItem.has("operator"));
		Wo_FormatConditionType type = GetConditionType(cfItem.field_str("type"));
		Wo_CFOperator ope = GetConditionOperator(cfItem.field_str("operator"));
		WebStr topN = __X("10");
		if(cfItem.has("rank"))
			topN = cfItem.field_str("rank");
		copy2Item(xf, &item);
		switch (type)
		{
			case Wo_ContainsValue:
			{
				switch (ope)
				{
				case Wo_NoBlanksCondition:
					item.type = etNoBlanksCondition;
					break;
				case Wo_BlanksCondition:
					item.type = etBlanksCondition;
					break;
				case Wo_UniqueValues:
				case Wo_DuplicateValues:
					item.type = etUniqueValues;
					item.bDuplicate = ope == Wo_DuplicateValues;
					break;
				case Wo_Errors:
					item.type = etErrorsCondition;
					break;
				case Wo_NoErrors:
					item.type = etNoErrorsCondition;
					break;
				case Wo_StringConclude:
				case Wo_StringExclude:
				case Wo_StringBeginsWith:
				case Wo_StringEndsWith:
					item.type = etTextString;
					if (ope == Wo_StringConclude)	item.opt = xlContains;
					if (ope == Wo_StringExclude)	item.opt = xlDoesNotContain;
					if (ope == Wo_StringBeginsWith) item.opt = xlBeginsWith;
					if (ope == Wo_StringEndsWith)	item.opt = xlEndsWith;
					break;
				}
				break;
			}
			case Wo_ValueRange:
			{
				item.type = etCellValue;
				switch (ope)
				{
				case Wo_Between:	item.opt = etBetween;	break;
				case Wo_NotBetween:	item.opt = etNotBetween; break;
				case Wo_Equal:		item.opt = etEqual;		break;
				case Wo_NotEqual:	item.opt = etNotEqual;	break;
				case Wo_Greater:	item.opt = etGreater;	break;
				case Wo_Less:		item.opt = etLess;		break;
				case Wo_GreaterEqual:item.opt = etGreaterEqual;	break;
				case Wo_LessEqual:	item.opt = etLessEqual;	break;
				}
				break;
			}
			case Wo_RankAverage:
			{
				switch (ope)
				{
				case Wo_Top10:
				case Wo_Last10:
				case Wo_Top10Per:
				case Wo_Last10Per:
					item.type = etTop10;
					item.bBottom = ope == Wo_Last10 || ope == Wo_Last10Per;
					item.strRank = QString::fromUtf16(topN);
					item.bPercent = ope == Wo_Top10Per || ope == Wo_Last10Per;
					break;
				case Wo_AboveAverage:
				case Wo_BelowAverage:
					item.type = etAboveAverageCondition;
					item.opt = (ope == Wo_AboveAverage) ? xlAboveAverage : xlBelowAverage;
					break;
				}
				break;
			}
			case Wo_TimePeriod:
			{
				item.type = etTimePeriod;
				switch (ope)
				{
				case Wo_Today:		item.opt = xlToday;		break;
				case Wo_Yesterday:	item.opt = xlYesterday;	break;
				case Wo_Last7Days:	item.opt = xlLast7Days;	break;
				case Wo_ThisWeek:	item.opt = xlThisWeek;	break;
				case Wo_LastWeek:	item.opt = xlLastWeek;	break;
				case Wo_LastMonth:	item.opt = xlLastMonth;	break;
				case Wo_Tomorrow:	item.opt = xlTomorrow;	break;
				case Wo_NextWeek:	item.opt = xlNextWeek;	break;
				case Wo_NextMonth:	item.opt = xlNextMonth;	break;
				case Wo_ThisMonth:	item.opt = xlThisMonth;	break;
				}
				break;
			}
			case Wo_Expression:
			{
				item.type = etExpression;
				break;
			}
		}
	}
	else
	{
		if (cfItem.has("CfIconSet"))
			importIconSets(item, cfItem);
		else if (cfItem.has("CfDataBar"))
			importDataBar(item, cfItem);
		else
			importColorScale(item, cfItem);
	}
	return item;
}

void importIconSets(OUT CondFormatItem& item,
				    IN const binary_wo::VarObj& cfItem)
{
	item.type = etIconSets;
	const binary_wo::VarObj cfIconSet = cfItem.get("CfIconSet");

	item.ensureIS();
	item.pIS->bCustom = cfIconSet.field_bool("bCustom");
	item.pIS->bReverse = cfIconSet.field_bool("bReverse");
	item.pIS->bShowOnly = cfIconSet.field_bool("bShowOnly");
	item.pIS->is = String_To_XlIconSet(cfIconSet.field_str("iconSet"));
	
	const binary_wo::VarObj icon = cfIconSet.get("icon");
	const int lenIcon = icon.arrayLength_s();
	for (int i = 0; i < lenIcon; ++i)
	{
		item.pIS->arrIcon[i] = String_To_XlIcon(icon.at_s(i).value_str());
	}

	const binary_wo::VarObj cond = cfIconSet.get("cond");
	const int lenCond = cond.arrayLength_s();
	for (int i = 0; i < lenCond; ++i)
	{
		optUiCfVo_reverse(item.pIS->arrCv[i], cond.at_s(i));
	}
}

void importDataBar(OUT CondFormatItem& item,
				   IN const binary_wo::VarObj& cfItem)
{
	item.type = etDatabar;
	const binary_wo::VarObj cfDataBar = cfItem.get("CfDataBar");

	item.ensureDB();
	item.pDB->bShowValue = cfDataBar.field_bool("bShowValue");
	item.pDB->bBorder = cfDataBar.field_bool("bBorder");
	item.pDB->bGradient = cfDataBar.field_bool("bGradient");
	item.pDB->dir = String_To_UiDBDirection(cfDataBar.field_str("dir"));
	wo::DecodeColor(cfDataBar.get("clrFill"), item.pDB->clrFill);
	wo::DecodeColor(cfDataBar.get("clrBorder"), item.pDB->clrBorder);
	optUiCfVo_reverse(item.pDB->arrCv[0], cfDataBar.get("minCond"));
	optUiCfVo_reverse(item.pDB->arrCv[1], cfDataBar.get("maxCond"));
	optUiDataBarNegAxisSetting_reverse(item.pDB->axisSetting, cfDataBar.get("axisSetting"));
}

void importColorScale(OUT CondFormatItem& item,
					  IN const binary_wo::VarObj& cfItem)
{
	item.type = etColorScale;
	item.cs.b3ColorScale = false;
	if (cfItem.has("b3ColorScale"))
		item.cs.b3ColorScale = cfItem.field_bool("b3ColorScale");

	binary_wo::VarObj scale_values = cfItem.get_s("scale_values");
	binary_wo::VarObj scale_types = cfItem.get_s("scale_types");
	binary_wo::VarObj scale_colors = cfItem.get_s("scale_colors");
	ASSERT(scale_values.arrayLength() >= 2 && scale_types.arrayLength() >= 2 && scale_colors.arrayLength() >= 2);

	item.cs.arrCv[0].cvTy = GetConditionValueTypes(scale_types.at(0).value_str());
	item.cs.arrCv[0].val = QString::fromUtf16(scale_values.at(0).value_str());
	wo::DecodeColor(scale_colors.at(0), item.cs.arrClr[0]);

	item.cs.arrCv[1].cvTy = GetConditionValueTypes(scale_types.at(1).value_str());
	item.cs.arrCv[1].val = QString::fromUtf16(scale_values.at(1).value_str());
	wo::DecodeColor(scale_colors.at(1), item.cs.arrClr[1]);

	if (item.cs.b3ColorScale)
	{
		ASSERT(scale_values.arrayLength() == 3 && scale_types.arrayLength() == 3 && scale_colors.arrayLength() == 3);
		item.cs.arrCv[2].cvTy = GetConditionValueTypes(scale_types.at(2).value_str());
		item.cs.arrCv[2].val = QString::fromUtf16(scale_values.at(2).value_str());
		wo::DecodeColor(scale_colors.at(2), item.cs.arrClr[2]);
	}
}

void exportCFRules(const std::vector<CondFormatItem>& vecItems, ISerialAcceptor* acpt, IBook* ptrBook)
{
	if (!acpt)
		return;

	acpt->addKey("rules");
	acpt->beginArray();
	{
		for (size_t i = 0; i < vecItems.size(); i++)
		{
			const CondFormatItem& item = vecItems[i];

			acpt->beginStruct();
			{
				acpt->addObjectID("id", item.pObject);
				
				// 为了方便调试，带上priority
				acpt->addInt32("priority", item.nPriority);
				if (item.pKXF)
					optXf(acpt, "xf", item.pKXF.get(), item.pKXF->mask, ptrBook);

				if (!item.strCfRuleNotes.isEmpty())
					acpt->addString("cfRuleNotes", krt::utf16(item.strCfRuleNotes));

				bool bMultiColor = false;
				ks_wstring type;
				ks_wstring option;
				switch (item.type)
				{
				case etCellValue:
					type = __X("valueRange");
					switch (item.opt)
					{
					case etBetween:		option = __X("between");	break;
					case etNotBetween:	option = __X("notBetween"); break;
					case etEqual:		option = __X("equal");		break;
					case etNotEqual:	option = __X("notEqual");	break;
					case etGreater:		option = __X("greater");	break;
					case etLess:		option = __X("less");		break;
					case etGreaterEqual:option = __X("greaterEqual");	break;
					case etLessEqual:	option = __X("lessEqual");	break;
					}
					break;
				case etNoBlanksCondition:
					type = __X("containsValue");
					option = __X("noBlanksCondition");
					break;
				case etBlanksCondition:
					type = __X("containsValue");
					option = __X("blanksCondition");
					break;
				case etUniqueValues:
					type = __X("containsValue");
					option = item.bDuplicate ? __X("duplicateValues") : __X("uniqueValues");
					break;
				case etErrorsCondition:
				case etNoErrorsCondition:
					type = __X("containsValue");
					option = item.type == etErrorsCondition ? __X("errors") : __X("noErrors");
					break;
				case etTextString:
					type = __X("containsValue");
					switch (item.opt)
					{
					case xlContains: option = __X("stringConclude"); break;
					case xlDoesNotContain: option = __X("stringExclude"); break;
					case xlBeginsWith: option = __X("stringBeginsWith"); break;
					case xlEndsWith: option = __X("stringEndsWith"); break;
					}
					break;
				case etTop10:
					type = __X("rankAverage");
					acpt->addString("rank", krt::utf16(item.strRank));
					if (item.bBottom)
						option = item.bPercent ? __X("last10%") : __X("last10");
					else
						option = item.bPercent ? __X("top10%") : __X("top10");
					break;
				case etAboveAverageCondition:
					type = __X("rankAverage");
					option = item.opt == xlAboveAverage ? __X("aboveAverage") : __X("belowAverage");
					break;
				case etTimePeriod:
					type = __X("timePeriod");
					switch (item.opt)
					{
					case xlToday:	option = __X("today");	break;
					case xlYesterday: option = __X("yesterday"); break;
					case xlLast7Days: option = __X("last7Days"); break;
					case xlThisWeek: option = __X("thisWeek"); break;
					case xlLastWeek: option = __X("lastWeek"); break;
					case xlLastMonth:option = __X("lastMonth"); break;
					case xlTomorrow: option = __X("tomorrow"); break;
					case xlNextWeek: option = __X("nextWeek"); break;
					case xlNextMonth: option = __X("nextMonth"); break;
					case xlThisMonth: option = __X("thisMonth"); break;
					}
					break;
				case etColorScale:
					{
						bMultiColor = true;

						acpt->addBool("b3ColorScale", item.cs.b3ColorScale);

						acpt->addKey("scale_types");
						acpt->beginArray();
						{
							acpt->addString(NULL, EncodeConditionValueTypes(item.cs.arrCv[0].cvTy));
							acpt->addString(NULL, EncodeConditionValueTypes(item.cs.arrCv[1].cvTy));
							if (item.cs.b3ColorScale)
								acpt->addString(NULL, EncodeConditionValueTypes(item.cs.arrCv[2].cvTy));
						}
						acpt->endArray();

						acpt->addKey("scale_values");
						acpt->beginArray();
						{
							acpt->addString(NULL, krt::utf16(item.cs.arrCv[0].val));
							acpt->addString(NULL, krt::utf16(item.cs.arrCv[1].val));
							if (item.cs.b3ColorScale)
								acpt->addString(NULL, krt::utf16(item.cs.arrCv[2].val));
						}
						acpt->endArray();

						acpt->addKey("scale_colors");
						acpt->beginArray();
						{
							optColor(acpt, NULL, item.cs.arrClr[0], ptrBook);
							optColor(acpt, NULL, item.cs.arrClr[1], ptrBook);
							if (item.cs.b3ColorScale)
								optColor(acpt, NULL, item.cs.arrClr[2], ptrBook);
						}
						acpt->endArray();
						break;
					}
				case etExpression:
					type = __X("expression");
					break;
				case etIconSets:
					{
						bMultiColor = true;
						type = __X("iconSet");

						acpt->addKey("CfIconSet");
						acpt->beginStruct();
							acpt->addBool("bCustom", item.pIS->bCustom);
							acpt->addBool("bReverse", item.pIS->bReverse);
							acpt->addBool("bShowOnly", item.pIS->bShowOnly);

							int n = 0;
							XlIconSet iconSet = item.pIS->is;
							acpt->addString("iconSet", XlIconSet_To_String(iconSet));
							if (XlIconSet_To_IconSetsIdx(iconSet) < 10)
								n = 3;
							else if (XlIconSet_To_IconSetsIdx(iconSet) < 15)
								n = 4;
							else
								n = 5;

							acpt->addKey("icon");
							acpt->beginArray();
							for (int i = 0; i < n; ++i)
								acpt->addString(nullptr, XlIcon_To_String(item.pIS->arrIcon[i]));
							acpt->endArray();

							acpt->addKey("cond");
							acpt->beginArray();
							for (int i = 0; i < n - 1; ++i)
								optUiCfVo(acpt, nullptr, item.pIS->arrCv[i], true);
							acpt->endArray();
						acpt->endStruct();
						break;
					}
				case etDatabar:
					{
						bMultiColor = true;
						type = __X("dataBar");

						acpt->addKey("CfDataBar");
						acpt->beginStruct();
							acpt->addBool("bShowValue", item.pDB->bShowValue);
							acpt->addBool("bBorder", item.pDB->bBorder);
							acpt->addBool("bGradient", item.pDB->bGradient);
							acpt->addString("dir", UiDBDirection_To_String(item.pDB->dir));
							optColor(acpt, "clrFill", item.pDB->clrFill, ptrBook);
							optColor(acpt, "clrBorder", item.pDB->clrBorder, ptrBook);
							optUiCfVo(acpt, "minCond", item.pDB->arrCv[0]);
							optUiCfVo(acpt, "maxCond", item.pDB->arrCv[1]);
							optUiDataBarNegAxisSetting(acpt, "axisSetting", item.pDB->axisSetting, ptrBook);
						acpt->endStruct();
						break;
					}
				}

				// 多颜色的条件格式样式包括：色阶（双色or三色），图标集，数据条
				if (bMultiColor)
					acpt->addBool("bMultiColor", true);
				else
				{
					acpt->addString("formula1", krt::utf16(item.formula1));
					acpt->addString("formula2", krt::utf16(item.formula2));
					acpt->addString("operator", option.c_str());
					acpt->addString("type", type.c_str());
				}
				
				ASSERT(item.m_spRange);
				acpt->addKey("ranges");
				acpt->beginArray();
				{
					ks_stdptr<IRangeInfo> spIRangeInfo = item.m_spRange;
					ks_stdptr<IKRanges> spRgs;
					spIRangeInfo->GetIRanges(&spRgs);
					UINT count = 0;
					spRgs->GetCount(&count);
					for (UINT idx = 0; idx < count; idx++)
					{
						const RANGE* pRG = NULL;
						spRgs->GetItem(idx, NULL, &pRG);
						if (pRG)
						{
							acpt->beginStruct();
							acpt->addInt32("sheetIdx", pRG->SheetFrom());
							acpt->addInt32("rowFrom", pRG->RowFrom());
							acpt->addInt32("rowTo", pRG->RowTo());
							acpt->addInt32("colFrom", pRG->ColFrom());
							acpt->addInt32("colTo", pRG->ColTo());
							acpt->endStruct();
						}
					}
				}
				acpt->endArray();
			}
			acpt->endStruct();
		}
	}
	acpt->endArray();
}