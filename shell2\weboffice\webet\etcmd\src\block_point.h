﻿#ifndef __WEBET_BLOCK_POINT_H__
#define __WEBET_BLOCK_POINT_H__

#include "webdeclare.h"

namespace binary_wo
{
    class BinWriter;
}

namespace wo
{

struct BlockPoint: public ::WoBlockPoint
{
public:
	BlockPoint();
	BlockPoint(WebID, INT32 rr, INT32 cc);
	BlockPoint(const ::WoBlockPoint&);

	void ResetByCell(INT32 rr, INT32 cc);

	RECT GetRect() const;
	void Write(binary_wo::BinWriter&) const;

	struct Hasher{size_t operator() (const BlockPoint& ) const;};
	struct KeyEq{bool operator()(const BlockPoint&, const BlockPoint&) const;};
	struct LessOp{bool operator()(const BlockPoint&, const BlockPoint&) const;};
};

class BlockPointEnum
{
public:
	BlockPointEnum(WebID objSheet, const RECT& rc);
	bool IsValid() const;
	BlockPoint Current() const;
	void Next();

protected:
	INT m_rr;
	INT m_cc;
	RECT m_blockRect;
	const WebID m_objSheet;

private:
	BlockPointEnum(const BlockPointEnum&);
	BlockPointEnum operator=(const BlockPointEnum&);
};

}

#endif //__WEBET_BLOCK_POINT_H__
