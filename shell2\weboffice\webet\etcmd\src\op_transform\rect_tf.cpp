﻿#include "etstdafx.h"
#include "rect_tf.h"
namespace wo
{

bool RectTrans::isValidRect(const RECT& rc)
{
	return 0 <= rc.left && rc.left <= rc.right && rc.right < m_bmp->cntCols &&
		0 <= rc.top && rc.top <= rc.bottom && rc.bottom < m_bmp->cntRows;
}

bool RectTrans::MoveByDel(const Rect& dRc, Rect& op)
{
	Rect mv;
	mv.smFrom =dRc.smTo + 1;
	mv.smTo = m_maxSm;
	if (mv.smFrom <= mv.smTo)
	{
		mv.dfFrom = dRc.dfFrom;
		mv.dfTo = dRc.dfTo;
		if (mv.isContain(op))
		{
			if(m_param & ts_move)
			{
				int step = dRc.smTo - dRc.smFrom + 1;
				op.smFrom -= step;
				op.smTo -= step;
			}
			return true;
		}
	}
	return false;
}

bool RectTrans::Abridge(const Rect& dRc, Rect& op)
{
	if (dRc.isDfContain(op) && op.smFrom <= dRc.smTo && op.smTo >= dRc.smFrom)
	{
		if (op.smFrom < dRc.smFrom)
		{
			if (m_param & ts_abridge)
				op.smTo -= std::min(op.smTo, dRc.smTo) - dRc.smFrom + 1;
		}
		else
		{
			int sz =  op.smSize();
			if (m_param & ts_abridge)
				sz -= std::min(op.smTo, dRc.smTo) - op.smFrom + 1;
			if (m_param & ts_move)
				op.smFrom = dRc.smFrom;
			op.smTo = op.smFrom + sz - 1;
		}
		return true;
	}
	else if (dRc.isSmContain(op) && op.dfFrom <= dRc.dfTo && op.dfTo >= dRc.dfFrom)
	{
		if (m_param & ts_abridge)
		{
			if (op.dfTo <= dRc.dfTo)
			{
				ASSERT(op.dfFrom < dRc.dfFrom);
				op.dfTo = dRc.dfFrom - 1;
				return true;
			}
			if (op.dfFrom >= dRc.dfFrom)
			{
				ASSERT(op.dfTo > dRc.dfTo);
				op.dfFrom = dRc.dfTo + 1;
				return true;
			}
		}
		return !(m_param & ts_split);
	}
	return false;
}

void RectTrans::SplitByDel(const Rect& dRc, Rect& op)
{
	if (!(m_param & ts_split))
		return;

	if (!(m_param & ts_rowCol) &&
		op.dfFrom == 0 && op.dfTo == m_maxDf && (dRc.dfFrom != 0 || dRc.dfTo != m_maxDf))
		return;

	if (!(m_param & ts_abridge) && op.smFrom <= dRc.smFrom)
		return;

	if ((m_param & ts_exDf) && op.isSmContain(dRc))
		return;

	ASSERT(op.smTo >= dRc.smFrom && dRc.isDfCross(op));
	m_res.reserve(3);
	if (op.dfFrom < dRc.dfFrom)
	{
		Rect rc(op);
		if (op.dfTo > dRc.dfTo)
		{
			rc.dfFrom = dRc.dfTo + 1;
			m_res.push_back(rc);

			rc.dfFrom = dRc.dfFrom;
			rc.dfTo = dRc.dfTo;
			VERIFY(MoveByDel(dRc, rc) || Abridge(dRc, rc));
			if (rc.smSize() > 0)
				m_res.push_back(rc);
		}
		else
		{
			rc.dfFrom = dRc.dfFrom;
			VERIFY(MoveByDel(dRc, rc) || Abridge(dRc, rc));
			if (rc.smSize() > 0)
				m_res.push_back(rc);
		}
		rc = op;
		rc.dfTo = dRc.dfFrom - 1;
		m_res.push_back(rc);
	}
	else
	{
		ASSERT(op.dfTo > dRc.dfTo);
		Rect rc(op);
		rc.dfFrom = dRc.dfTo + 1;
		m_res.push_back(rc);

		rc = op;
		rc.dfTo = dRc.dfTo;
		VERIFY(MoveByDel(dRc, rc) || Abridge(dRc, rc));
		if (rc.smSize() > 0)
			m_res.push_back(rc);
	}
}


void RectTrans::Delete(const Rect& dRc, Rect& op)
{
	if (op.smTo < dRc.smFrom || op.dfTo < dRc.dfFrom || op.dfFrom > dRc.dfTo)
		return;

	if (op.smTo == m_maxSm && op.smFrom == 0)
		return;

	if (MoveByDel(dRc, op))
		return;

	if (Abridge(dRc, op))
		return;

	SplitByDel(dRc, op);
}

void RectTrans::DeleteUp(const RECT& rcDel, const RECT& rcTrs, std::vector<RECT>& vecRc)
{
	if (!isValidRect(rcDel) || !isValidRect(rcTrs))
		return;

	VertMod();
	Rect dRc, op;
	dRc.fromVert(rcDel);
	op.fromVert(rcTrs);

	m_res.clear();
	Delete(dRc, op);
	getVertRes(op, vecRc);
}

void RectTrans::DeleteLeft(const RECT& rcDel, const RECT& rcTrs, std::vector<RECT>& vecRc)
{
	if (!isValidRect(rcDel) || !isValidRect(rcTrs))
		return;

	HoriMod();
	Rect dRc, op;
	dRc.fromHori(rcDel);
	op.fromHori(rcTrs);

	m_res.clear();
	Delete(dRc, op);
	getHoriRes(op, vecRc);
}

bool RectTrans::MoveByInsert(const Rect& iRc, Rect& op)
{
	Rect mv;
	mv.smFrom =iRc.smFrom;
	mv.smTo = m_maxSm;
	if (mv.smFrom <= mv.smTo)
	{
		mv.dfFrom = iRc.dfFrom;
		mv.dfTo = iRc.dfTo;
		if (mv.isContain(op))
		{
			if(m_param & ts_move)
			{
				int step = iRc.smTo - iRc.smFrom + 1;
				op.smFrom += step;
				op.smTo += step;
			}
			return true;
		}
	}
	return false;
}

bool RectTrans::Expand(const Rect& iRc, Rect& op)
{
	if (iRc.isDfContain(op) && op.smFrom < iRc.smFrom && op.smTo >= iRc.smFrom)
	{
		if (!(m_param & ts_expand))
			return !(m_param & ts_split);
		op.smTo += iRc.smTo - iRc.smFrom + 1; 
		return true;
	}
	return false;
}

bool RectTrans::SplitByInsertSimple(const Rect& iRc, Rect& op)
{
	ASSERT(iRc.isDfContain(op));
	ASSERT(m_param & ts_split);
	ASSERT( op.smFrom != 0 || op.smTo != m_maxSm);
	ASSERT(op.smFrom < iRc.smFrom && op.smTo >= iRc.smFrom);
	ASSERT(!(m_param & ts_exDf));

	Rect rc = op;
	rc.smFrom = iRc.smTo + 1;
	rc.smTo += iRc.smSize();
	m_res.push_back(rc);

	op.smTo = iRc.smFrom - 1;
	return true;
}

void RectTrans::SplitByInsert(const Rect& iRc, Rect& op)
{
	if (!(m_param & ts_split))
		return;

	if (op.smFrom == 0 && op.smTo == m_maxSm)
		return;

	if (!(m_param & ts_rowCol) && 
		op.dfFrom == 0 && op.dfTo == m_maxDf && (iRc.dfFrom != 0 || iRc.dfTo != m_maxDf))
		return;

	if ((m_param & ts_exDf) && op.smFrom < iRc.smFrom)
		return;

	ASSERT(op.smTo >= iRc.smFrom && iRc.isDfCross(op));
	m_res.reserve(4);
	if (op.dfFrom < iRc.dfFrom)
	{
		Rect rc(op);
		if (op.dfTo > iRc.dfTo)
		{
			rc.dfFrom = iRc.dfTo + 1;
			m_res.push_back(rc);

			rc.dfFrom = iRc.dfFrom;
			rc.dfTo = iRc.dfTo;
			VERIFY(MoveByInsert(iRc, rc) || SplitByInsertSimple(iRc, rc));
			m_res.push_back(rc);
		}
		else
		{
			rc.dfFrom = iRc.dfFrom;
			VERIFY(MoveByInsert(iRc, rc) || SplitByInsertSimple(iRc, rc));
			m_res.push_back(rc);
		}
		rc = op;
		rc.dfTo = iRc.dfFrom - 1;
		m_res.push_back(rc);
	}
	else
	{
		if (op.dfTo > iRc.dfTo)
		{
			Rect rc(op);
			rc.dfFrom = iRc.dfTo + 1;
			m_res.push_back(rc);

			rc = op;
			rc.dfTo = iRc.dfTo;
			VERIFY(MoveByInsert(iRc, rc) || SplitByInsertSimple(iRc, rc));
			m_res.push_back(rc);
		}
		else
		{
			Rect rc(op);
			SplitByInsertSimple(iRc, rc);
			m_res.push_back(rc);
			return;
		}
	}
}

void RectTrans::Insert(const Rect& iRc, Rect& op)
{
	if (op.smTo < iRc.smFrom || op.dfTo < iRc.dfFrom || op.dfFrom > iRc.dfTo)
		return;

	if (op.smTo == m_maxSm && op.smFrom == 0)
		return;

	if (MoveByInsert(iRc, op))
		return;

	if (Expand(iRc, op))
		return;

	SplitByInsert(iRc, op);
}

void RectTrans::InsertDown(const RECT& rcIst, const RECT& rcTrs, std::vector<RECT>& vecRc)
{
	if (!isValidRect(rcIst) || !isValidRect(rcTrs))
		return;

	VertMod();
	Rect dRc, op;
	dRc.fromVert(rcIst);
	op.fromVert(rcTrs);

	m_res.clear();
	Insert(dRc, op);
	getVertRes(op, vecRc);
}

void RectTrans::InsertRight(const RECT& rcIst, const RECT& rcTrs, std::vector<RECT>& vecRc)
{
	if (!isValidRect(rcIst) || !isValidRect(rcTrs))
		return;

	HoriMod();
	Rect dRc, op;
	dRc.fromHori(rcIst);
	op.fromHori(rcTrs);

	m_res.clear();
	Insert(dRc, op);
	getHoriRes(op, vecRc);
}

void RectTrans::SwapVert(const RECT& rcTop, const RECT& rcBottom, const RECT& rcTrs, std::vector<RECT>& vecRc)
{
	if (!isValidRect(rcTop) || !isValidRect(rcBottom) || !isValidRect(rcTrs))
		return;

	VertMod();
	Rect tRc, bRc, op;
	tRc.fromVert(rcTop);
	bRc.fromVert(rcBottom);
	op.fromVert(rcTrs);

	m_res.clear();
	Swap(tRc, bRc, op);
	getVertRes(op, vecRc);
}

void RectTrans::SwapHorz(const RECT& rcLeft, const RECT& rcRight, const RECT& rcTrs, std::vector<RECT>& vecRc)
{
	if (!isValidRect(rcLeft) || !isValidRect(rcRight) || !isValidRect(rcTrs))
		return;

	HoriMod();
	Rect lRc, rRc, op;
	lRc.fromHori(rcLeft);
	rRc.fromHori(rcRight);
	op.fromHori(rcTrs);

	m_res.clear();
	Swap(lRc, rRc, op);
	getHoriRes(op, vecRc);
}

void RectTrans::Swap(const Rect& fstRc, const Rect& scdRc, Rect& op)
{
	if (!fstRc.isCross(op) && !scdRc.isCross(op))
		return;
	
	if (op.dfFrom < fstRc.dfFrom)
	{
		Rect rc(op);
		rc.dfTo = fstRc.dfFrom - 1;
		m_res.push_back(rc);
	}

	if (op.smFrom < fstRc.smFrom)
	{
		Rect rc(op);
		rc.smFrom = op.smFrom;
		rc.smTo = fstRc.smFrom - 1;
		m_res.push_back(rc);
	}
	if (fstRc.isCross(op))
	{
		Rect rc(fstRc);
		rc.dfFrom = std::max(fstRc.dfFrom, op.dfFrom);
		rc.dfTo = std::min(fstRc.dfTo, op.dfTo);
		rc.smFrom = std::max(fstRc.smFrom, op.smFrom) + scdRc.smSize();
		rc.smTo = std::min(fstRc.smTo, op.smTo) + scdRc.smSize();
		m_res.push_back(rc);
	}
	if (scdRc.isCross(op))
	{
		Rect rc(scdRc);
		rc.dfFrom = std::max(scdRc.dfFrom, op.dfFrom);
		rc.dfTo = std::min(scdRc.dfTo, op.dfTo);
		rc.smFrom = std::max(scdRc.smFrom, op.smFrom) - fstRc.smSize();
		rc.smTo = std::min(scdRc.smTo, op.smTo) - fstRc.smSize();
		m_res.push_back(rc);
	}
	if (op.smTo > scdRc.smTo)
	{
		Rect rc(op);
		rc.smFrom = scdRc.smTo + 1;
		rc.smTo = op.smTo;
		m_res.push_back(rc);
	}

	if (op.dfTo > fstRc.dfTo)
	{
		Rect rc(op);
		rc.dfFrom = fstRc.dfTo + 1;
		m_res.push_back(rc);
	}
}

void RectTrans::VertMod()
{
	m_maxSm = m_bmp->cntRows - 1;
	m_maxDf = m_bmp->cntCols - 1;
}

void RectTrans::HoriMod()
{
	m_maxSm = m_bmp->cntCols - 1;
	m_maxDf = m_bmp->cntRows - 1;
}

void RectTrans::getVertRes(const Rect& res, std::vector<RECT>& vecRes)
{
	if (m_res.empty())
	{
		vecRes.resize(1);
		res.toVert(vecRes[0]);
	}
	else
	{
		vecRes.resize(m_res.size());
		for (size_t i = 0; i < m_res.size(); ++i)
			m_res[i].toVert(vecRes[i]);
	}
}

void RectTrans::getHoriRes(const Rect& res, std::vector<RECT>& vecRes)
{
	if (m_res.empty())
	{
		vecRes.resize(1);
		res.toHori(vecRes[0]);
	}
	else
	{
		vecRes.resize(m_res.size());
		for (size_t i = 0; i < m_res.size(); ++i)
			m_res[i].toHori(vecRes[i]);
	}
}

}