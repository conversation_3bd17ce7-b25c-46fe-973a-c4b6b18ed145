﻿#ifndef __WO_WEBET_JSAPI_RECORD_H__
#define __WO_WEBET_JSAPI_RECORD_H__

namespace wo
{
    enum class JSRecordType
    {
        Invalid,
        Begin,
        End,
        UpdateContext,
		PicUuid,
    };

    struct JsApiExecRecord
    {
        struct UserContext
        {
            ks_wstring connId;
            ks_wstring userId;
            ks_wstring sessionId;
            ks_wstring permissionId;
            UserContext(PCWSTR connId, PCWSTR userId, PCWSTR sessionId, PCWSTR permissionId) :
                connId(connId), userId(userId), sessionId(sessionId), permissionId(permissionId) {}
            UserContext() = default;
        } userContext;
        ks_wstring taskId;
        ks_wstring scriptName;
        ks_wstring language;
        QByteArray content;
        QByteArray contextInfo;
        JSRecordType type = JSRecordType::Invalid;
    };
}

#endif //__WO_WEBET_JSAPI_RECORD_H__