﻿#ifndef __SHELL2_WEBET_KMETRICS_I_H__
#define __SHELL2_WEBET_KMETRICS_I_H__

#include "webbase/webdeclare.h"
#include "wo/wo_msgType_helper.h"
#include <vector>
#include <string>
#include <utility>
#include "ksignalbinwebstr.h"

interface IWebSlice;

namespace binary_wo
{
    enum class ResultCode;
}

namespace wo
{
    struct KCntSize
    {
        int m_cnt = 0;
        int m_size = 0;
    };

    using TopCollectInfos = std::vector<std::pair<std::string, KCntSize>>;
    using WStrCntSizeData = std::pair<ks_wstring, KCntSize>;
    using TopCollectWStrInfos = std::vector<WStrCntSizeData>;
    
    struct KSignalResult
    {
        TopCollectInfos m_selfSizeInfos;
        TopCollectInfos m_sizeInfos;
        TopCollectWStrInfos m_newObjClsNameInfos;
        ks_wstring m_traceId;
        wo::MsgType m_msgType = wo::msgType_OnExit;
        int m_signalSizeKb = 0;
        int m_noRefNewObjCnt = 0;
        int m_noRefNewObjBytes = 0;
        int m_nNewObjCnt = 0;
        int m_nNewObjClsNameCnt = 0;
        int m_nNewObjTopObjCnt = 0;
        
        KSignalResult() {}
        explicit KSignalResult(const ks_wstring & traceId, wo::MsgType msgType, int signalSz)
        : m_traceId(traceId)
        , m_msgType(msgType)
        , m_signalSizeKb(signalSz >> 10)
        {}
    };

}

class IWoSignalResultNotify
{
public:
    virtual ~IWoSignalResultNotify() {}
    virtual void onResultCode(binary_wo::ResultCode code) = 0; // 主线程/工作线程回调
    virtual void onSuccResult(const wo::KSignalResult & ret) = 0;
};

enum class WoSignalMcNotifyCode
{
    kBeforeRunTask,
    kAfterGetTask,
    kBeforeTaskExec,
    kAfterTaskExec,
    _kCount
};

class IWoSignalMcNotify
{
public:
    virtual ~IWoSignalMcNotify() {}
    virtual void onNotify(WoSignalMcNotifyCode code, long param) = 0;
};

class IWoSignalMetrics
{
public:
    virtual ~IWoSignalMetrics() {}
    virtual void setEnable(bool v) = 0;
    virtual bool isEnable() = 0;
    virtual void setMinCollectSize(int sz) = 0;
    virtual bool isNeedMetrics(WebSize sliceSize, wo::MsgType msgType, WebProcType) = 0;
    virtual bool collectMetricData(
        IWebSlice *pSlice, 
        wo::MsgType msgType, 
        WebProcType procType,
        PCWSTR traceId = nullptr,
        std::shared_ptr<IWoSignalResultNotify> sp = {}) = 0;
    virtual void interrupt() = 0;
    virtual void interruptAndWait() = 0;
    virtual void finishAndWait() = 0;
    
    virtual void setNotify(std::shared_ptr<IWoSignalMcNotify> spNotify) = 0;
};

#endif// __SHELL2_WEBET_KMETRICS_I_H__