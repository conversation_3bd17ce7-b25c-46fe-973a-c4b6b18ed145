#ifndef __WEBET_DATABASE_FILTER_HELPER_H__
#define __WEBET_DATABASE_FILTER_HELPER_H__

#include "webbase/binvariant/binvarobj.h"

interface IDbFilterCriteria;
namespace wo 
{
HRESULT CreateDbFilterCriteria(
    binary_wo::VarObj vCriteria, IBook* pBook, IDbFilterCriteria** ppVal, bool bSvrApi = false, bool bQueryView = false);

    HRESULT SetComposeFilter(IDBSheetView* pView, const binary_wo::VarObj& param);
}//namespace wo 
#endif // __WEBET_DATABASE_FILTER_HELPER_H__