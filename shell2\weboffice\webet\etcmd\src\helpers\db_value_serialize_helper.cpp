﻿#include "db_value_serialize_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "workbook.h"
#include "webbase/wo_sa_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "common_log/common_data.h"
#include "dbsheet/common_log/common_log_value_serialiser.h"
#include <public_header/drawing/model/shared_style_sheet.h>

namespace wo
{

STDIMP_(void) KDbValueSerializeHelper::SerializeAllSheets(Log_ChangeData* pData, IDbSerializeMultipartHelper* pHelper)
{
    bool bIsFirstPart = true;
    if (pHelper && !pHelper->IsFirstPart())
        bIsFirstPart = false;
    if (bIsFirstPart)
    {
        IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
	    INT sheetCount = pWorksheets->GetSheetCount();
        std::vector<IKWorksheet*> worksheetsVec;
        worksheetsVec.reserve(sheetCount);
        for (INT i = 0; i < sheetCount; i++)
        {
            IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(i);
            worksheetsVec.push_back(pWorksheet);
            ISheet* pSheet = pWorksheet->GetSheet();
            if (pSheet->IsDbSheet())
                pHelper->AddSheetIdx(i);
        }
        serializeFirstPart(pData, pHelper, worksheetsVec);
        // SerialiseDbPermission(m_pWorkbook, pData);
        serializeSidebarFolder(pData);
    }
    else
        serializeMiddlePart(pData, pHelper);
}

STDIMP_(void) KDbValueSerializeHelper::SerializeSheets(Log_ChangeData* pData, const UINT* pSheetIds, UINT uCnt, IDbSerializeMultipartHelper* pHelper)
{
    bool bIsFirstPart = true;
    if (pHelper && !pHelper->IsFirstPart())
        bIsFirstPart = false;
    if (bIsFirstPart)
    {
        std::vector<IKWorksheet*> worksheetsVec;
        worksheetsVec.reserve(uCnt);
        IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
        for (UINT i = 0; i < uCnt; i++)
        {
            IDX sheetIdx = INVALIDIDX;
            pBook->STSheetToRTSheet(pSheetIds[i], &sheetIdx);
            if (INVALIDIDX == sheetIdx)
                continue;
            IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
            IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
            worksheetsVec.push_back(pWorksheet);
            ISheet* pSheet = pWorksheet->GetSheet();
            if (pSheet->IsDbSheet())
                pHelper->AddSheetIdx(sheetIdx);
        }
        serializeFirstPart(pData, pHelper, worksheetsVec);
    }
    else
        serializeMiddlePart(pData, pHelper);
}

STDIMP_(void) KDbValueSerializeHelper::SerializeRecords(Log_sheet* pLogSheet, UINT uSheetId, const EtDbId* pRecIds, UINT cnt)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    CommonLogValueSerialiser logSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet);
    VS(logSerialiser.SerialiseRecords(pLogSheet, pRecIds, cnt));
}

STDIMP_(void) KDbValueSerializeHelper::SerializeView(Log_View* pLogView, UINT uSheetId, IDBSheetView* pView)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    // DbSheetValueSerialiser dbSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet, pAcpt, VarObj{}, nullptr);
    // dbSerialiser.SetPreferIdOpt(true);
    // VS(dbSerialiser.SerialiseView(pView, pLogView));
}

STDIMP_(void) KDbValueSerializeHelper::SerializeDashboardWebExtension(Log_WebExtension* pWebExtension, UINT uSheetId, PCWSTR pcwWebExtensionKey)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    // SerialiseSheetDashboardWebExtension(m_pWorkbook, pWorksheet, pWebExtension, pcwWebExtensionKey);
}

STDIMP_(void) KDbValueSerializeHelper::SerializeDashboardFilters(Log_sheet* pLogSheet, UINT uSheetId, const EtDbId* pModuleIds, UINT cnt)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    // SerialiseSheetDashboardFilters(m_pWorkbook, pWorksheet, pLogSheet, pModuleIds, cnt);
}

STDIMP_(void) KDbValueSerializeHelper::SerializeField(Log_Field* pField, UINT uSheetId, EtDbId fieldId)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    CommonLogValueSerialiser logSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet);
    VS(logSerialiser.SerialiseField(pField, fieldId));
}

namespace 
{
class KDbXfSerializeHelper : public IDbXfSerializeHelper
{
public:
    STDPROC_(void) AddXf(const XF* pXF, const XFMASK& mask, INT* pIdx)
    {
        auto iter = m_xfMap.find(pXF);
        if (iter != m_xfMap.end())
        {
            if (pIdx)
                *pIdx = iter->second;
            return;
        }
        m_xfVec.emplace_back(pXF, mask);
        if (pIdx)
            *pIdx = m_nextIdx;
        m_xfMap[pXF] = m_nextIdx++;
    }
    STDPROC_(void) EnumXfs(IDbXfSerializeEnum* pEnum)
    {
        if (!pEnum)
            return;
        for (int i = 0, size = m_xfVec.size(); i < size; i++)
        {
            BOOL bRes = pEnum->Do(i, m_xfVec[i].first, m_xfVec[i].second);
            if (!bRes)
                return;
        }
    }
private:
    std::unordered_map<const XF*, INT> m_xfMap;
    std::vector<std::pair<const XF*, XFMASK>> m_xfVec;
    INT m_nextIdx = 0;
};

class XfSerializeEnum : public IDbXfSerializeEnum
{
public:
    XfSerializeEnum(Log_Xf* pLogXf, IDbValueSerializeHelper* pHelper) :
        m_pLogXf(pLogXf), m_pValueSerializeHelper(pHelper) {}
    STDPROC_(BOOL) Do(INT idx, const XF* pXF, const XFMASK& mask) override
    {
        m_pValueSerializeHelper->SerialiseXfForDb(m_pLogXf, pXF, mask);
        return TRUE;
    }
private:
    Log_Xf* m_pLogXf = nullptr;
    IDbValueSerializeHelper* m_pValueSerializeHelper = nullptr;
};
}  // namespace anonymous

void KDbValueSerializeHelper::serializeFirstPart(Log_ChangeData* pData, IDbSerializeMultipartHelper* pHelper,
                                                 const std::vector<IKWorksheet*>& worksheetsVec)
{
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    KDbXfSerializeHelper stXfHelper;
    {
        // wo::sa::Leave leaveArray(wo::sa::enterArray(pAcpt, "sheets"));
        for (IKWorksheet* pWorksheet : worksheetsVec)
        {
            // wo::sa::Leave leaveStruct(wo::sa::enterStruct(pAcpt, nullptr));
            // SerialiseSheetInfo(m_pWorkbook, pWorksheet, pAcpt, nullptr, nullptr, true, pHelper, &stXfHelper);
        }
    }
    {
        // wo::sa::Leave leaveArray(wo::sa::enterArray(pAcpt, "xfs"));
        // XfSerializeEnum stXfEnum(pAcpt, this);
        // stXfHelper.EnumXfs(&stXfEnum);
    }
}

void KDbValueSerializeHelper::serializeMiddlePart(Log_ChangeData* pData, IDbSerializeMultipartHelper* pHelper)
{
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    ASSERT(pHelper);
    KDbXfSerializeHelper stXfHelper;
    {
        // wo::sa::Leave leaveArray(wo::sa::enterArray(pAcpt, "sheets"));
        // 基本数据已经序列化出去了，这里只序列化值
        // while (!pHelper->IsFinish() && !pHelper->IsNeedNextPart(pAcpt->getBufferWriteLength()))
        // {
        //     INT i = pHelper->GetCurSheetIdx();
        //     IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(i);
        //     ISheet* pSheet = pWorksheet->GetSheet();
            // wo::sa::Leave leaveStruct(wo::sa::enterStruct(pAcpt, nullptr));
            // DbSheetValueSerialiser dbSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet, pAcpt, VarObj{}, nullptr);
            // dbSerialiser.SetPreferIdOpt(true);
            // dbSerialiser.SetXfSerializeHelper(&stXfHelper);
            // dbSerialiser.SetTextValueOpt(TextValueType::TVT_FormulaCompound);
            // pAcpt->addUint32("id", pWorksheet->GetSheet()->GetStId());
            // VS(dbSerialiser.SerialiseAllRecords(pHelper));
        // }
    }
    {
        // wo::sa::Leave leaveArray(wo::sa::enterArray(pAcpt, "xfs"));
        // XfSerializeEnum stXfEnum(pAcpt, this);
        // stXfHelper.EnumXfs(&stXfEnum);
    }
}

STDIMP_(void) KDbValueSerializeHelper::SerializeViewProp(Log_View* pLogView, UINT uSheetId, IDBSheetView* pView)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    // DbSheetValueSerialiser dbSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet, pAcpt, VarObj{}, nullptr);
    // dbSerialiser.SetPreferIdOpt(true);
    // VS(dbSerialiser.SerialiseViewProp(pView));
}

STDIMP_(void) KDbValueSerializeHelper::SerialiseViewCondition(Log_GroupCondition* pLogCondition, UINT uSheetId, IDBRecordsOrderCondition* pCond)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    // DbSheetValueSerialiser dbSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet, pAcpt, VarObj{}, nullptr);
    // dbSerialiser.SetPreferIdOpt(true);
    // dbSerialiser.SerialiseViewCondition(pCond);
}

STDIMP_(void) KDbValueSerializeHelper::SerialiseViewSortSetting(Log_View* pLogView, UINT uSheetId, IDBSheetView* pView)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    // DbSheetValueSerialiser dbSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet, pAcpt, VarObj{}, nullptr);
    // dbSerialiser.SetPreferIdOpt(true);
    // VS(dbSerialiser.SerialiseViewSortSetting(pView));
}
STDIMP_(void) KDbValueSerializeHelper::SerialiseViewGroupSetting(Log_View* pLogView, UINT uSheetId, IDBSheetView* pView)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    // DbSheetValueSerialiser dbSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet, pAcpt, VarObj{}, nullptr);
    // dbSerialiser.SetPreferIdOpt(true);
    // VS(dbSerialiser.SerialiseViewGroupSetting(pView));
}

STDIMP_(void) KDbValueSerializeHelper::SerialiseViewFilterSetting(Log_FilterCondition* pLogFilter, UINT uSheetId, IDbFieldFilter* pFieldFilter)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    // DbSheetValueSerialiser dbSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet, pAcpt, VarObj{}, nullptr);
    // dbSerialiser.SetPreferIdOpt(true);
    // VS(dbSerialiser.SerialiseViewFilterSetting(pFieldFilter));
}

STDIMP_(void) KDbValueSerializeHelper::SerialisePermission(Log_permissionId* pLogPermission, PCWSTR pcwPermissionId)
{
    // SerialiseDbPermission(m_pWorkbook, pLogPermission, pcwPermissionId);
}

static void serialiseDbXfColour(Log_XfColor* pColor, IBook *bk, const EtColor& ec, GdiEnumColor autoType = GdiAutoTextColor)
{
    EtColor& cc = const_cast<EtColor&>(ec);
    pColor->tint = cc.getTint();
    pColor->type = cc.getType();
    switch (cc.getType())
    {
    case ectICV:
        pColor->value = cc.getICV();
        break;
    case ectTHEME:
        pColor->value = cc.getTheme();
        break;
    case ectARGB:
        pColor->value = cc.getARGB();
        break;
    }
    DWORD argb = bk->ToARGB(ec, autoType);
    if ((argb & 0xFF000000) != 0)
    {
        double alpha = ((argb >> 24) & 0xFF);
        auto conv = [alpha, argb](INT x)->DWORD {
            DWORD tmp = ((argb >> x) & 0xFF);
            return static_cast<DWORD>(alpha * tmp / 255) << x;
        };
        argb = (conv(16) | conv(8) | conv(0));
    }
    pColor->rgbValue.Format(__X("#%06X"), (argb & 0xFFFFFF));
    pColor->isAuto = false;
}

static void serialiseDbXf(Log_Xf* pLogXf, IBook* pBook, const XF* pXF, const XFMASK& mask)
{
    if (!pXF)
        return;
    if (pXF->pFont && mask._catsFont & XFMASK::_cat_clr)
    {
        auto& logXfFont = pLogXf->font.value();
        serialiseDbXfColour(&logXfFont.color, pBook, pXF->pFont->clr);
    }
    if (pXF->pFill && mask._cats & XFMASK::_cat_eft)
    {
        auto& logXfFill = pLogXf->fill.value();
        auto* pFill = pXF->pFill;
        ETFILLTYPE tt = pFill->getType();
        logXfFill.type = tt;
        if (tt == eftPatternNone)
            return;
        EtColor clrFore(pFill->getFore());
        EtColor clrBack(pFill->getBack());
        if (pFill->getType() >= eftPatternGray50 && pFill->getType() <= eftPatternGray08)
        {
            if (clrFore.getType() == ectNONE)
                clrFore.setAUTO();
            if (clrBack.getType() == ectAUTO)
                clrBack.setNONE();
        }
        serialiseDbXfColour(&logXfFill.back, pBook, clrBack, GdiAutoBkColor);
        serialiseDbXfColour(&logXfFill.fore, pBook, clrFore, GdiAutoTextColor);
    }
}

STDIMP_(void) KDbValueSerializeHelper::SerialiseXfForDb(Log_Xf* pLogXf, const XF* pXf, const XFMASK& mask)
{
    serialiseDbXf(pLogXf, m_pWorkbook->GetCoreWorkbook()->GetBook(), pXf, mask);
}

STDIMP_(void) KDbValueSerializeHelper::SerializeCell(Log_Col* pLogCol, UINT uSheetId, EtDbId recId, EtDbId fieldId)
{
    IDX sheetIdx = INVALIDIDX;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->STSheetToRTSheet(uSheetId, &sheetIdx);
    if (INVALIDIDX == sheetIdx)
        return;
    IKWorksheets* pWorksheets = m_pWorkbook->GetCoreWorkbook()->GetWorksheets();
    IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(sheetIdx);
    CommonLogValueSerialiser logSerialiser(m_pWorkbook->GetCoreWorkbook(), pWorksheet);
    VS(logSerialiser.SerialiseCell(pLogCol, recId, fieldId));
}

void KDbValueSerializeHelper::serializeSidebarFolder(Log_ChangeData* pData)
{
    ks_stdptr<IDbSidebarFolderTreeManager> spSidebarFolderTreeMgr;
    IBook* pBook = m_pWorkbook->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spSidebarFolderTreeMgr);
	if (!spSidebarFolderTreeMgr)
        return;
    // spSidebarFolderTreeMgr->SerialSidebarFolder(pAcpt);
}
}  // namespace wo