#include "etstdafx.h"
#include "et_task_peripheral.h"
#include "workbook.h"
#include "et_binvar_spec.h"
#include "wo/core_stake.h"
#include "webbase/memo_stat_common.h"

namespace wo
{
EtTaskPeripheral::EtTaskPeripheral(bool bPre, bool bPost, KEtWorkbook* wwb)
	: m_bPre(bPre), m_bPost(bPost), m_banUndo(KwVersion::banUndoNone), m_banUndoDef(KwVersion::banUndoNone), m_bCheckRange(true)
	, m_bRcHeaderRecord(false), m_wwb(wwb), m_rangeReader(nullptr)
	, m_bCareFilterHidden(false)
{
}

EtTaskPeripheral::~EtTaskPeripheral()
{

}

KwVersion::BanUndoType EtTaskPeripheral::getBanUndo()
{
	return m_banUndo;
}

void EtTaskPeripheral::PreProgress(KwCommand* cmd, bool* bDenyOp, binary_wo::VarObj resOthers, binary_wo::VarObj resSelf)
{
	m_resOthers = resOthers;
	m_resSelf = resSelf;
	(*bDenyOp) = false;
	m_banUndo = m_banUndoDef;

	if (!m_bPre) return;

	RANGE rg = ReadRange(cmd);
	if (rg.CellCount() > MAX_ROWS_COUNT_XTND * 4)
	{
		(*bDenyOp) = true;
	}
}

void EtTaskPeripheral::PostProgress(KwCommand* cmd)
{
}


RANGE EtTaskPeripheral::ReadRange(KwCommand* cmd)
{
	RANGE rg (m_wwb->GetBMP());
	if (m_rangeReader != nullptr)
	{
		rg = (*m_rangeReader)(m_wwb->GetCoreWorkbook()->GetBook(), cmd);
	}
	else
	{
		const binary_wo::VarObj& param =  cmd->cast().get("param");
		IDX sheetIdx = GetSheetIdx(m_wwb->GetCoreWorkbook()->GetBook(), param);
		rg = ReadRangeInl(m_wwb->GetBMP(), sheetIdx, param);
	}
	return rg;
}

void EtTaskPeripheral::setDefBanUndo(KwVersion::BanUndoType tp)
{
	m_banUndoDef = tp;
}

void EtTaskPeripheral::setBanUndo(KwVersion::BanUndoType tp)
{
	m_banUndo = tp;
}

void EtTaskPeripheral::setCheckRange(bool bCheckRange)
{
	m_bCheckRange = bCheckRange;
}

void EtTaskPeripheral::setRcHeaderRecord()
{
	m_bRcHeaderRecord = true;
}

void EtTaskPeripheral::setCareFilterHidden()
{
	m_bCareFilterHidden = true;
}

void EtTaskPeripheral::setEffectedRg(const std::vector<RANGE>& rgVec)
{
	m_rgEffected = rgVec;
}

void EtTaskPeripheral::installRangeReader(EtTaskPeripheral::RangeReader func)
{
	m_rangeReader = func;
}

ScopeRcHeader::ScopeRcHeader(EtTaskPeripheral* taskPer, KwCommand* cmd, IEtRevisionContext* ctx, const HRESULT& hr)
	: m_taskPer(taskPer), m_cmd(cmd), m_ctx(ctx), m_hr(hr)
{
	if (m_taskPer != nullptr)
	{
		ASSERT(m_cmd != nullptr);
		m_idxSheet = m_taskPer->rcHeaderBeginRecord(m_cmd);
	}
	else
	{
		m_idxSheet = INVALIDIDX;
	}
}

ScopeRcHeader::~ScopeRcHeader()
{
	if (m_idxSheet != INVALIDIDX)
	{
		ASSERT(m_taskPer != nullptr);
		m_taskPer->rcHeaderEndRecord(m_idxSheet, m_cmd, m_ctx, FAILED(m_hr));
	}
}

INT32 EtTaskPeripheral::rcHeaderBeginRecord(KwCommand* cmd)
{
	if (!m_bRcHeaderRecord) return INVALIDIDX;

	IBook* pb = m_wwb->GetCoreWorkbook()->GetBook();
	INT32 idxSheet = INVALIDIDX;
	if (m_rangeReader == nullptr)
	{
		//ReadRange and ReadRanges都可以用这里的
		idxSheet = cmd->cast().get("param").field_int32("sheetIdx");
	}
	else
	{
		idxSheet = (*m_rangeReader)(pb, cmd).SheetFrom();
	}
	idxSheet = (idxSheet == RCB_NONE_LT) ? INVALIDIDX : idxSheet;

	if (idxSheet != INVALIDIDX)
	{
		if (!pb->GetWoStake()->rcHeaderBeginRecord(idxSheet))
		{
			idxSheet = INVALIDIDX;
		}
	}
	return idxSheet;
}

void EtTaskPeripheral::rcHeaderEndRecord(INT32 idxSheet, KwCommand* cmd, IEtRevisionContext* ctx, bool bCancel)
{
	ASSERT(idxSheet != INVALIDIDX);
	IBook* pb = m_wwb->GetCoreWorkbook()->GetBook();
	INT32 id = pb->GetWoStake()->rcHeaderEndRecord(idxSheet, bCancel);
	if (!bCancel)
	{
		binary_wo::VarObj param = cmd->cast().get("param");
		param.add_field_int32("idRowColHeaderSerial", id);
		ctx->setIsRealTransform(true);
		cmd->setHasExtra();
	}
}

void EtTaskPeripheral::cacheCurFilterHiddenIfNeeded(KwCommand* cmd, IEtRevisionContext* ctx, bool bPreCache)
{
	if (!m_bCareFilterHidden && !bPreCache)
		return;

	IBook* pb = m_wwb->GetCoreWorkbook()->GetBook();
	if (pb->GetWoStake()->getSetting()->getIsFilterShared())
		return;

	class RgEffectedHelper
	{
	public:
		explicit RgEffectedHelper(std::vector<RANGE>* pRgVector)
			: m_pRgVector(pRgVector)
		{
		}

		~RgEffectedHelper()
		{
			if (NULL != m_pRgVector)
				m_pRgVector->clear();
		}
	private:
		std::vector<RANGE>* m_pRgVector;
	};

	RgEffectedHelper rgEffectedHelper(&m_rgEffected);
	try
	{
		binary_wo::VarObj param = cmd->cast().get("param");
		if (m_rgEffected.empty())
		{
			if (param.has("rgs"))
			{
				IDX sheetIdx = GetSheetIdx(m_wwb->GetCoreWorkbook()->GetBook(), param);
				ReadRangesInl(m_wwb->GetBMP(), sheetIdx, param, m_rgEffected);
			}
			else
			{
				RANGE rg = ReadRange(cmd);
				if (rg.IsValid())
					m_rgEffected.push_back(rg);
			}
		}
	}
	catch(...)
	{
	}

	if (m_rgEffected.empty())
		return;

	std::map<IDX, std::vector<RANGE>> mapSheetRgs;
	for (size_t i = 0, nSize = m_rgEffected.size(); i < nSize; i++)
		mapSheetRgs[m_rgEffected.at(i).SheetFrom()].push_back(m_rgEffected.at(i));

	IBookStake* pBS = pb->GetWoStake();
	std::map<IDX, std::vector<RANGE>>::iterator iter = mapSheetRgs.begin();
	for (; iter != mapSheetRgs.end(); iter++)
	{
		IKWorksheet* pWs = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iter->first);
		if (!pWs)
			return;

		ks_stdptr<IRangeInfo> spRgInfo = m_wwb->CreateRangeObj(iter->second);
		if (!spRgInfo)
			return;

		bool bAdd = pBS->addUserFilterHiddenData(pWs->GetSheet(), spRgInfo->GetIsInFilterMode(), iter->second);
		if (!bAdd)
			return;
	}

	INT32 idLastData = pBS->commitUserFilterHiddenData();
	if (idLastData < 0)
		return;
	
	binary_wo::VarObj param = cmd->cast().get("param");
	param.add_field_int32("idUserFilterHidden", idLastData);
	ctx->setIsRealTransform(true);
	cmd->setHasExtra();
}

KEtTaskPeripheralFullRC::KEtTaskPeripheralFullRC(KEtWorkbook* wwb)
	:EtTaskPeripheral(true, true, wwb)
{
}

void KEtTaskPeripheralFullRC::PreProgress(KwCommand* cmd, bool* bDenyOp, binary_wo::VarObj resOthers, binary_wo::VarObj resSelf)
{
	m_resOthers = resOthers;
	m_resSelf = resSelf;

	RANGE rg = ReadRange(cmd);
	(*bDenyOp) = (	(rg.Width() < rg.GetBMP()->cntCols) &&
					(rg.Height() < rg.GetBMP()->cntRows) &&
					rg.CellCount() > MAX_ROWS_COUNT_XTND * 4);
}

KEtMultiRangeTaskPeripheral::KEtMultiRangeTaskPeripheral(bool bPre, bool bPost, KEtWorkbook* wwb)
	: EtTaskPeripheral(bPre, bPost, wwb)
{}

void KEtMultiRangeTaskPeripheral::PreProgress(KwCommand* cmd, bool* bDenyOp, binary_wo::VarObj resOthers, binary_wo::VarObj resSelf)
{
	m_resOthers = resOthers;
	m_resSelf = resSelf;
	(*bDenyOp) = false;

	if (!m_bPre) return;

	std::vector<RANGE> rgVec;
	ReadRanges(cmd, rgVec);
	if (GetTotalCellCount(rgVec) > MAX_ROWS_COUNT_XTND * 4)
		(*bDenyOp) = true;
}

void KEtMultiRangeTaskPeripheral::PostProgress(KwCommand* cmd)
{
}

void KEtMultiRangeTaskPeripheral::ReadRanges(KwCommand* cmd, std::vector<RANGE> &rgVec)
{
	ASSERT(m_rangeReader == nullptr);
	const binary_wo::VarObj& param = cmd->cast().get("param");
	IDX sheetIdx = GetSheetIdx(m_wwb->GetCoreWorkbook()->GetBook(), param);
	ReadRangesInl(m_wwb->GetBMP(), sheetIdx, param, rgVec);
}

INT64 KEtMultiRangeTaskPeripheral::GetTotalCellCount(const std::vector<RANGE> &rgVec)
{
	INT64 totalCount = 0;
	for (auto it = rgVec.begin(); it != rgVec.end(); ++it)
		totalCount += it->CellCount();

	return totalCount;
}

KEtRangesTaskPeripheral::KEtRangesTaskPeripheral(bool bPre, bool bPost, KEtWorkbook* wwb)
	: EtTaskPeripheral(bPre, bPost, wwb)
{
}

void KEtRangesTaskPeripheral::PreProgress(KwCommand* cmd, bool* bDenyOp, binary_wo::VarObj resOthers, binary_wo::VarObj resSelf)
{
	m_resOthers = resOthers;
	m_resSelf = resSelf;

	if (!m_bPre) return;

	std::vector<RANGE> rgVec;
	ReadRanges(cmd, rgVec);
	CheckDenyOp(bDenyOp, rgVec);
}

void KEtRangesTaskPeripheral::ReadRanges(KwCommand* cmd, std::vector<RANGE> &rgVec)
{
	RANGE rg(m_wwb->GetBMP());
	if (m_rangeReader != nullptr)
	{
		rg = (*m_rangeReader)(m_wwb->GetCoreWorkbook()->GetBook(), cmd);
		if (rg.IsValid()) rgVec.push_back(rg);
	}
	else
	{
		const binary_wo::VarObj& param = cmd->cast().get("param");
		IDX sheetIdx = GetSheetIdx(m_wwb->GetCoreWorkbook()->GetBook(), param);
		if (param.has("rgs"))
			ReadRangesInl(m_wwb->GetBMP(), sheetIdx, param, rgVec);
		else
		{
			rg = ReadRangeInl(m_wwb->GetBMP(), sheetIdx, param);
			if (rg.IsValid()) rgVec.push_back(rg);
		}
	}
}

void KEtRangesTaskPeripheral::CheckDenyOp(bool* bDenyOp, const std::vector<RANGE> &rgVec)
{
	INT64 totalCount = 0;
	for (auto it = rgVec.begin(); it != rgVec.end(); ++it)
		totalCount += it->CellCount();

	*bDenyOp = (totalCount > MAX_ROWS_COUNT_XTND * 4);
}

KEtMultiRangeTaskPeripheralFullRC::KEtMultiRangeTaskPeripheralFullRC(KEtWorkbook* wwb)
	: KEtMultiRangeTaskPeripheral(true, true, wwb)
{}

INT64 KEtMultiRangeTaskPeripheralFullRC::GetTotalCellCount(const std::vector<RANGE> &rgVec)
{
	INT64 totalCount = 0;
	for (auto it = rgVec.begin(); it != rgVec.end(); ++it)
		if (it->Width() < it->GetBMP()->cntCols && it->Height() < it->GetBMP()->cntRows)
			totalCount += it->CellCount();

	return totalCount;
}

}