﻿#include "etstdafx.h"

#include "form2db_converter.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "http/db_value_serialiser.h"
#include "util.h"

namespace wo
{
HRESULT JsonParser::ParseJsonData(const binary_wo::VarObj& jsonData)
{
	HRESULT hr = ParseSheetData(jsonData);
	if(FAILED(hr))
		return hr;

	if(!jsonData.has("records"))
		return E_FAIL;

	binary_wo:: VarObj records = jsonData.get_s("records");
	if(records.type() != binary_wo::typeArray)
		return E_FAIL;

	for(int i = 0; i < records.arrayLength_s(); ++i)
	{
		hr = ParseRecordData(records.at(i));
		if(FAILED(hr))
			return hr;
	}
	return S_OK;
}

Form2DbJsonParser::Form2DbJsonParser(Form2DbSheetType st)
{
	m_value.sheetType = st;
}

HRESULT Form2DbJsonParser::ParseSheetData(const binary_wo::VarObj& sheetData)
{
	if(!sheetData.has("commitIdField"))
		return E_FAIL;

	EtDbId commitIdField = INV_EtDbId;
	_appcore_GainDbSheetContext()->DecodeEtDbId(sheetData.field_str("commitIdField"), &commitIdField);
	if(commitIdField == INV_EtDbId)
		return E_FAIL;

	m_value.commitIdField = commitIdField;
	return S_OK;
}

HRESULT Form2DbJsonParser::ParseRecordData(const binary_wo::VarObj& recordData)
{
	if(!recordData.has("commitTime") || !recordData.has("commitId"))
		return E_FAIL;
	
	Form2DbData::FormRecord formRecord;
	formRecord.commitTime = recordData.field_str("commitTime");
	formRecord.commitId = recordData.field_str("commitId");
	formRecord.recordValue = recordData.get_s("fields");

	ks_wstring commitId = formRecord.commitId;
	m_value.formData.emplace(std::make_pair(commitId, std::move(formRecord)));
	return S_OK;
}

const Form2DbData& Form2DbJsonParser::GetValue() const
{
	return m_value;
}

Form2DbSyncUpdater::Form2DbSyncUpdater(KEtWorkbook *wwb, ISheet* pSheet, Form2DbCrossSheetFieldUpdater* pCrossSheetFieldUpdater, DbSheetValueSerialiser* pDbSerialiser, const Form2DbData& data)
: m_wwb(wwb)
, m_pSheet(pSheet)
, m_pCrossSheetFieldUpdater(pCrossSheetFieldUpdater)
, m_pDbSerialiser(pDbSerialiser)
, m_value(data)
{
	DbSheet::GetDBSheetOp(pSheet, &m_spDbSheetOp);
}

HRESULT Form2DbSyncUpdater::UpdateSyncSheet()
{
	// 上报埋点
	UINT syncRowCnt = m_value.formData.size();
	UINT syncFldCnt = syncRowCnt > 0 ? m_value.formData.begin()->second.recordValue.keys().size() : 0;
	wo::util::CollectInfo(m_wwb, __X("form_sync_db_rows_cols_count"), syncRowCnt, syncFldCnt);

	switch(m_value.sheetType)
	{
	case Form2DbSheetType::Form2DbSheetType_MainSheet:
		return UpdateMainSyncSheet();
	case Form2DbSheetType::Form2DbSheetType_SubSheet:
		return UpdateSubSyncSheet();
	default:
		break;
	}
	
	return E_FAIL;
}

HRESULT Form2DbSyncUpdater::UpdateMainSyncSheet()
{
	HRESULT hr;
	IBook* pBook = m_pSheet->LeakBook();
	IDbRecordsManager *pRecordsManager = m_spDbSheetOp->GetRecordsManager();
	const IDBIds *pRecordIds = m_spDbSheetOp->GetAllRecords();
	UINT rowCnt = pRecordIds->Count();
	IDbFieldsManager *pFieldManager = m_spDbSheetOp->GetFieldsManager();
	const IDBIds* pFieldIds = m_spDbSheetOp->GetAllFields();

	std::set<ks_wstring> syncRecordCommitIds;
	for(const auto& record_pair : m_value.formData)
		syncRecordCommitIds.insert(record_pair.first);

	std::unordered_map<ks_wstring, EtDbId, WSTR_HASH> commitIdRecIdMap;
	for (int i = 0; i < rowCnt; ++i)
	{
		EtDbId recId = pRecordIds->IdAt(i);
		EtDbId commitId = m_value.commitIdField;
		ks_bstr commitIdStr;
		m_spDbSheetOp->GetValueString(recId, commitId, &commitIdStr);
		if(commitIdRecIdMap.find(commitIdStr.c_str()) == commitIdRecIdMap.end())
			commitIdRecIdMap.insert(std::make_pair(commitIdStr.c_str(), recId));
		// 从表单数据中找不到对应的commitId，跳过该记录更新
		auto formDataItr = m_value.formData.find(commitIdStr.c_str());
		if(formDataItr == m_value.formData.end())
			continue;

		syncRecordCommitIds.erase(commitIdStr.c_str());
		const Form2DbData::FormRecord& formRecord = formDataItr->second;
		double formRecCommitTime = 0;
		hr = DbSheet::ConvertStringToDateTime(pBook, formRecord.commitTime, formRecCommitTime);
		if(FAILED(hr))
			return hr;

		std::vector<WebName> formFldIds = formRecord.recordValue.keys();
		for(WebName fldIdStr : formFldIds)
		{
			EtDbId fldId = INV_EtDbId;
			_appcore_GainDbSheetContext()->DecodeEtDbId(krt::utf16(krt::fromUtf8(fldIdStr)), &fldId);
			if(pFieldIds->IdAt(fldId) == INV_EtDbIdx)
				continue;

			ks_stdptr<IDbField> spField;
			hr = pFieldManager->GetField(fldId, &spField);
			if(FAILED(hr))
				return E_FAIL;

			// 不更新关联字段
			if(spField->GetType() == ET_DbSheet_FieldType::Et_DbSheetField_Link)
				continue;

			double lastModifyTime = 0;
			PCWSTR lastModifyId;
			pRecordsManager->GetCellLastModifiedInfo(recId, fldId, lastModifyTime, lastModifyId);
			if(lastModifyTime > formRecCommitTime) // 判断同步表数据和表单数据，哪个最后修改用哪个
				continue;
			
			hr = m_pDbSerialiser->Set(recId, fldIdStr, formRecord.recordValue);
			if(FAILED(hr))
				return hr;
		}

		// 同步数据对比完了
		if(syncRecordCommitIds.size() == 0)
			break;
	}

	// 插入新的记录
	int insertRecordsCnt = syncRecordCommitIds.size();
	if(insertRecordsCnt > 0)
	{
		if(insertRecordsCnt > pBook->GetBMP()->cntRows - pRecordIds->Count())
		{
			WOLOG_WARN << "insertRecordsCnt: " << insertRecordsCnt 
						<< " over bmp limit, " << "current records: " << pRecordIds->Count();
			insertRecordsCnt = pBook->GetBMP()->cntRows - pRecordIds->Count();
		}

		ks_stdptr<IDBSheetRange> spDbRange;
		hr = m_spDbSheetOp->InsertRecords(insertRecordsCnt, &spDbRange);
		if(FAILED(hr))
			return hr;

		auto idIter = syncRecordCommitIds.begin();
		for(ROW i = 0; i < spDbRange->GetRecordCnt(); ++i)
		{
			EtDbId recId = spDbRange->GetRecordId(i);
			auto& commitId = *idIter++;
			commitIdRecIdMap.insert(std::make_pair(commitId, recId));
			const Form2DbData::FormRecord& formRecord = m_value.formData.find(commitId)->second;
			std::vector<WebName> formFldIds = formRecord.recordValue.keys();
			for(WebName fldIdStr : formFldIds)
			{
				EtDbId fldId = INV_EtDbId;
				_appcore_GainDbSheetContext()->DecodeEtDbId(krt::utf16(krt::fromUtf8(fldIdStr)), &fldId);
				if(pFieldIds->IdAt(fldId) == INV_EtDbIdx)
					continue;

				ks_stdptr<IDbField> spField;
				hr = pFieldManager->GetField(fldId, &spField);
				if(FAILED(hr))
					return E_FAIL;

				if(spField->GetType() == ET_DbSheet_FieldType::Et_DbSheetField_Link)
					continue;	// 子表处理关联字段

				hr = m_pDbSerialiser->Set(recId, fldIdStr, formRecord.recordValue);
				if(FAILED(hr))
					return hr;
			}
		}
	}
	m_pCrossSheetFieldUpdater->UpdateSheetCommitIdRecIdMap(m_pSheet->GetStId(), commitIdRecIdMap);
	return S_OK;
}

HRESULT Form2DbSyncUpdater::UpdateSubSyncSheet()
{
	HRESULT hr;
	IBook* pBook = m_pSheet->LeakBook();
	IDbRecordsManager *pRecordsManager = m_spDbSheetOp->GetRecordsManager();
	const IDBIds *pRecordIds = m_spDbSheetOp->GetAllRecords();
	IDbFieldsManager *pFieldManager = m_spDbSheetOp->GetFieldsManager();
	const IDBIds* pFieldIds = m_spDbSheetOp->GetAllFields();

	// 先删除字表记录，再插入新记录，因为子表commitid不唯一，无法对比更新
	ks_stdptr<IDBSheetRange> spRemoveRg;
	m_spDbSheetOp->CreateDBSheetRange(&spRemoveRg);
	spRemoveRg->SetFieldIds(m_spDbSheetOp->GetAllFields());
	for(int i = 0; i < pRecordIds->Count(); i++)
		spRemoveRg->AddRecordId(pRecordIds->IdAt(i));

	// 插入新的记录
	int insertRecordsCnt = m_value.formData.size();
	if(insertRecordsCnt > pBook->GetBMP()->cntRows - pRecordIds->Count())
	{
		WOLOG_WARN << "insertRecordsCnt: " << insertRecordsCnt 
					<< " over bmp limit, " << "current records: " << pRecordIds->Count();
		insertRecordsCnt = pBook->GetBMP()->cntRows - pRecordIds->Count();
	}

	ks_stdptr<IDBSheetRange> spInsertRange;
	if(insertRecordsCnt > 0)
	{
		hr = m_spDbSheetOp->InsertRecords(insertRecordsCnt, &spInsertRange);
		if(FAILED(hr))
			return hr;
	}

	// 填充插入新记录的值
	int recordIdx = 0;
	for(auto recItr = m_value.formData.begin(); recItr != m_value.formData.end() && recordIdx < insertRecordsCnt; ++recItr, ++recordIdx)
	{
		EtDbId recId = spInsertRange->GetRecordId(recordIdx);
		auto& commitId = recItr->first;
		const Form2DbData::FormRecord& formRecord = recItr->second;
		std::vector<WebName> formFldIds = formRecord.recordValue.keys();
		for(WebName fldIdStr : formFldIds)
		{
			EtDbId fldId = INV_EtDbId;
			_appcore_GainDbSheetContext()->DecodeEtDbId(krt::utf16(krt::fromUtf8(fldIdStr)), &fldId);
			if(pFieldIds->IdAt(fldId) == INV_EtDbIdx)
				continue;
			ks_stdptr<IDbField> spField;
			hr = pFieldManager->GetField(fldId, &spField);
			if(FAILED(hr))
				return E_FAIL;
			if(spField->GetType() == ET_DbSheet_FieldType::Et_DbSheetField_Link)
			{
				binary_wo::VarObj linkCellVal = formRecord.recordValue.get_s(fldIdStr);
				if(linkCellVal.arrayLength_s() > 0)
					m_pCrossSheetFieldUpdater->AddLinkFieldValue(m_pSheet->GetStId(), recId, commitId, fldId, linkCellVal.at(0).value_str());
				continue;
			}
			hr = m_pDbSerialiser->Set(recId, fldIdStr, formRecord.recordValue);
			if(FAILED(hr))
				return hr;
		}
	}

	// 删除旧的记录
	if(spRemoveRg->GetRecordCnt() > 0)
	{
		hr = m_spDbSheetOp->RemoveRecords(spRemoveRg);
		if(FAILED(hr))
			return hr;
	}

	return S_OK;
}

// Form2DbCrossSheetFieldUpdater
Form2DbCrossSheetFieldUpdater::Form2DbCrossSheetFieldUpdater(IBook* pBook)
: m_pBook(pBook)
{

}

HRESULT Form2DbCrossSheetFieldUpdater::UpdateCrossSheetFieldValue()
{
	HRESULT hr = UpdateCrossSheetLinkFieldValue();
	return hr;
}

HRESULT Form2DbCrossSheetFieldUpdater::AddLinkFieldValue(UINT sheetId, EtDbId recId, ks_wstring commitId, EtDbId fldId, ks_wstring linkCommitId)
{
	m_linkFieldValuesMap[sheetId][recId][fldId] = linkCommitId;
	return S_OK;
}

HRESULT Form2DbCrossSheetFieldUpdater::UpdateSheetCommitIdRecIdMap(UINT sheetId, std::unordered_map<ks_wstring, EtDbId, WSTR_HASH>& commitIdRecIdMap)
{
	m_commitIdRecIdMap[sheetId] = std::move(commitIdRecIdMap);
	return S_OK;
}

HRESULT Form2DbCrossSheetFieldUpdater::UpdateCrossSheetLinkFieldValue()
{
	DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TrySetValueAndPassState);
	DbSheet::DbLinkHlp dbLinkHlp(m_pBook, dbLinkParam);
	for(auto sheetMapItr = m_linkFieldValuesMap.begin(); sheetMapItr != m_linkFieldValuesMap.end(); ++sheetMapItr)
	{
		UINT sheetId = sheetMapItr->first;
		auto& sheetMap = sheetMapItr->second;
		ks_stdptr<IDBSheetOp> spDBSheetOp;
		DbSheet::GetDBSheetOp(m_pBook, sheetId, &spDBSheetOp);
		for(auto recItr = sheetMap.begin(); recItr != sheetMap.end(); ++recItr)
		{
			EtDbId recId = recItr->first;
			auto& recMap = recItr->second;
			for(auto fldItr = recMap.begin(); fldItr != recMap.end(); ++fldItr)
			{
				EtDbId fldId = fldItr->first;
				ks_wstring& linkCommitId = fldItr->second;
				HRESULT hr = UpdateCrossSheetLinkFieldValueImp(spDBSheetOp, recId, fldId, linkCommitId);
				if(FAILED(hr))
				{
					WOLOG_ERROR << "UpdateCrossSheetLinkFieldValue error!";
					return hr;
				}
			}
		}
	}
	return S_OK;
}

HRESULT Form2DbCrossSheetFieldUpdater::UpdateCrossSheetLinkFieldValueImp(IDBSheetOp* pDBSheetOp, EtDbId recId, EtDbId fldId, ks_wstring linkCommitId)
{
	if(!pDBSheetOp)
		return E_FAIL;
	
	IDbFieldsManager* pFieldManager = pDBSheetOp->GetFieldsManager();
	ks_stdptr<IDbField> spField;
	HRESULT hr = pFieldManager->GetField(fldId, &spField);
	if(FAILED(hr))
		return E_FAIL;

	if(spField->GetType() != ET_DbSheet_FieldType::Et_DbSheetField_Link)
		return E_FAIL;
	
	ks_stdptr<IDbField_Link> spFieldLink = spField;
	UINT linkSheetId = spFieldLink->GetLinkSheet();
	auto linkSheetItr = m_commitIdRecIdMap.find(linkSheetId);
	if(linkSheetItr == m_commitIdRecIdMap.end())
	{
		WOLOG_ERROR << "UpdateCrossSheetLinkFieldValueImp linkCommitid error!";
		return E_FAIL;
	}
	auto& commitIdRecMap = linkSheetItr->second;
	auto linkRecordItr = commitIdRecMap.find(linkCommitId);
	if(linkRecordItr == commitIdRecMap.end())
	{
		WOLOG_ERROR << "UpdateCrossSheetLinkFieldValueImp linkCommitid error!";
		return E_FAIL;
	}
	EtDbId linkRecId = linkRecordItr->second;
	ks_stdptr<IFormula> spFmla;
	VS(spFieldLink->GenerateLinkFormula(linkSheetId, &linkRecId, 1, &spFmla));
	return pDBSheetOp->SetFormula(recId, fldId, spFmla.get());
}

} // namespace wo
