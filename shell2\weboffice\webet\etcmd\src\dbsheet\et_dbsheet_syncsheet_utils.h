#ifndef __WEBET_DATABASE_SHEET_SYNCSHEET_UTILS_H__
#define __WEBET_DATABASE_SHEET_SYNCSHEET_UTILS_H__

#include "appcore/et_appcore_dbsheet.h"

namespace wo
{
namespace DbSyncSheet
{
HRESULT ClearSyncFieldFlag(IDBSheetOp* pDBSheetOp);
HRESULT ClearSyncRecordFlag(IDBSheetOp* pDBSheetOp);
HRESULT ClearSyncFieldSourceFieldId(IDBSheetOp* pDBSheetOp);

} // namespace DbSyncSheet

} // namespace wo

#endif // __WEBET_DATABASE_SHEET_SYNCSHEET_UTILS_H__
