﻿#ifndef __WEBET_DB_DASHBOARD_PLUGIN_WRAPPER_H__
#define __WEBET_DB_DASHBOARD_PLUGIN_WRAPPER_H__

#include "appcore/et_appcore_dbsheet.h"
#include "db_dashboard_module_wrapper.h"

namespace wo
{

class KDbDashboardPluginWrapper : public KDbDashboardModuleWrapper
{
public:
    explicit KDbDashboardPluginWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension, IDBChartStatisticModule* pStatisticModule);
    KDbDashboardPluginWrapper(const KDbDashboardPluginWrapper&) = delete;
    KDbDashboardPluginWrapper& operator=(const KDbDashboardPluginWrapper&) = delete;

public:
    Status GetStatus() const override;
    HRESULT SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang, KEtRevisionContext* pCtx,
                             ISerialAcceptor* pAcpt) const override;
    HRESULT SetPluginConfig(const binary_wo::VarObj& param, IEtRevisionContext* pCtx);
    void SerializeSettings(ISerialAcceptor* pAcpt) override;
private:
    HRESULT SetPluginConfigDataRange(const binary_wo::VarObj& dataRange);
    HRESULT SetPluginConfigGroups(const binary_wo::VarObj& groups);
    HRESULT SetPluginConfigSeries(const binary_wo::VarObj& series);
    HRESULT IsPluginConfigDataRangeChange(const binary_wo::VarObj& dataRange, bool& isChange);
    HRESULT IsPluginConfigGroupsChange(const binary_wo::VarObj& groups, bool& isChange);
    HRESULT IsPluginConfigSeriesChange(const binary_wo::VarObj& series, bool& isChange);
    ET_DBSheet_ChartSortType MappingSortType(const binary_wo::VarObj& param);
    HRESULT SetCustomConfig(const binary_wo::VarObj& param, IEtRevisionContext* pCtx);
private:
    ks_stdptr<IDBChartStatisticModule> m_spStatisticModule;
    static constexpr int s_pluginGroupLimitNum = 2;
    static constexpr int s_pluginStatsLimitNum = 10;
};

};
#endif // __WEBET_DB_DASHBOARD_PLUGIN_WRAPPER_H__