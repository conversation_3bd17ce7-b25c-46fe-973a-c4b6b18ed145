﻿#include "etstdafx.h"
#include "range_operator_helper.h"
#include "binvariant/binvarobj.h"
#include "workbook.h"
#include "util.h"
#include "etcore/little_alg.h"
#include "pivot_core/pivot_core_x.h"
#include "protection_helper.h"
#include "kso/textstd/texttool.h"

namespace wo
{
namespace RangeOperatorHelper
{
    INT32 GetEndEmptyRowInRg(KEtWorkbook* pWorkBook, const VarObj& param, const IDX sheetIdx, bool bSkipEmptyMergeCell)
    {
        if (!pWorkBook)
            return INVALID_ROW;

        IBook* pBook = pWorkBook->GetCoreWorkbook()->GetBook();
        const BMP_PTR bpm = pWorkBook->GetBMP();
        RANGE getEndRowRg(bpm);
        getEndRowRg.SetSheetFromTo(sheetIdx);
        bool hasRange = param.has("rowFrom") && param.has("rowTo") && param.has("colFrom") && param.has("colTo");
        if (!hasRange) // 若param中无row、col相关参数，则表示不限定范围，即在整表范围内进行查找
        {
            getEndRowRg.SetRowFromTo(0, bpm->cntRows - 1);
            getEndRowRg.SetColFromTo(0, bpm->cntCols - 1);
        }
        else
        {
            getEndRowRg.SetRowFromTo(param.field_int32("rowFrom"), param.field_int32("rowTo"));
            getEndRowRg.SetColFromTo(param.field_int32("colFrom"), param.field_int32("colTo"));
        }
        
        ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(sheetIdx, &spSheet);
        et_sdptr<ISheetEnum> spSheetEnum;
        spSheet->CreateEnum(&spSheetEnum);
        INT32 resRow = spSheetEnum->GetEndRow(getEndRowRg);

        INT32 maxRow = !hasRange ? bpm->cntRows - 1 : param.field_int32("rowTo");
        if(resRow > maxRow)
            return INVALID_ROW;

        // 处理存在merge的单元格的情况
        RANGE resRg(bpm);
        resRg.SetSheetFromTo(sheetIdx);
        if (!hasRange) 
        {
            if (bSkipEmptyMergeCell)
                resRg.SetRowFromTo(resRow, bpm->cntRows - 1);
            else
                resRg.SetRowFromTo(resRow, resRow);
            resRg.SetColFromTo(0, bpm->cntCols - 1);
        }
        else
        {
            resRg.SetRowFromTo(resRow, param.field_int32("rowTo"));
            resRg.SetColFromTo(param.field_int32("colFrom"), param.field_int32("colTo"));
        }

        ks_stdptr<IBookOp> spBookOp;
        pBook->GetOperator(&spBookOp);
        std::vector_s<RANGE> vecRgs;
        spSheet->FindEffectMergeCell(resRg, FALSE, vecRgs);
        for(auto&& rg : vecRgs)
        {
            if (bSkipEmptyMergeCell)
            {
                resRow = std::max(resRow, rg.RowTo() + 1);
            }
            else
            {
                const_token_ptr pToken = NULL;
                spBookOp->GetCellValue(rg.SheetFrom(), rg.RowFrom(), rg.ColFrom(), &pToken);
                if (pToken)
                    resRow = std::max(resRow, rg.RowTo() + 1);
            }
        }

        // 处理存在透视表的情况
        ks_stdptr<pivot_core::IPivotTables> spCorePvtTables;
        HRESULT hr = pivot_core::GetPivotTablesFromSheet(spSheet, &spCorePvtTables);
        if (FAILED(hr))
            return INVALID_ROW;
        for (UINT idx = 0, cnt = spCorePvtTables->Count(); idx < cnt; ++idx)
        {
            pivot_core::IPivotTable* pCorePvtTable = spCorePvtTables->Item(idx);
            if (!pCorePvtTable)
                continue;
            RECT pivotTableBody = {0};
            pCorePvtTable->GetBodyRect(&pivotTableBody);
            INT32 pivotTableBottom = pivotTableBody.bottom + 1;
            if (resRow < pivotTableBottom)
                resRow = pivotTableBottom;
        }

        return resRow > maxRow ? INVALID_ROW : resRow;
    }

    INT32 GetSheetDataRange(IKWorksheet* pWorksheetSrc, etoldapi::Range** ppRgSrc)
    {
        class HasCellValueAcpt : public ICellValueAcpt
        {
        public:
            HasCellValueAcpt()
            {
            }

            STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
            {
                if (pToken)
                    return 1;//停止枚举

                return 0;//继续枚举
            };
        };

        INT32 rowCnt = 0;
        ISheet* pSheetSrc = pWorksheetSrc->GetSheet();
        IDX srcSheetIdx = INVALIDIDX;
        pSheetSrc->GetIndex(&srcSheetIdx);
        RECT usedRect = {0};
        pSheetSrc->CalcUsedScale(&usedRect);
        RANGE usedRange = Rect2Range(usedRect, srcSheetIdx, pSheetSrc->GetBMP());
        if (usedRange.Width() <= 0)
            usedRange.SetColFromTo(0, pSheetSrc->GetBMP()->cntCols - 1); //整行复制
        else 
            usedRange.SetColFrom(0); // usedRange Col 可能不从0开始，这里强制设置为0

        et_sdptr<ISheetEnum> spSheetEnum;
        pSheetSrc->CreateEnum(&spSheetEnum);
        HasCellValueAcpt acpt;
        if (spSheetEnum->EnumCellValue(usedRange, &acpt))
        {
            rowCnt = usedRange.Height();
            pWorksheetSrc->GetRangeByData(&usedRange, ppRgSrc);
        }

        return rowCnt;
    }
}  // namespace RangeOperatorHelper

namespace RangeSliceHelper
{

constexpr const int g_maxSliceCount = 2048; // 切片最大字数
const WCHAR* g_sentence_separator[] = {__X("…"), __X("，"), __X(","), __X("。"), __X("："), __X("；"), __X("！"), __X("？"), __X(","), __X("."), __X(":"), __X(";"), __X("!"), __X("?"), __X("\r")}; //为了保证切片的语句连贯性

static bool isEmoji(IN UINT chHiSurg, IN UINT chLoSurg)
{
    TxUsrItem const* pUsrItem = __TxGetUsrItem(chLoSurg, chHiSurg);
    if (pUsrItem)
    {
        TxUsrType type = pUsrItem->nType;
        switch (type)
        {
        case usrMiscSymbolsAndPictographs:
            return true;
        case usrEmoticons:
        case usrTransportAndMapSymbols:
        case usrSupplementalSymbolsAndPictographs:
        {
            TxCharClass charClass = _TxGetCharClass(chLoSurg, chHiSurg);
            if (charClass == CC_NoFarEast)
                return true;
            break;
        }
        default:
            return false;
        }
    }
    return false;
}

static bool containsEmoji(const PCWSTR& str, int len)
{
    if (str == NULL)
        return false;
    for (int i = 0; i < len - 1; ++i)
    {
        WCHAR ch = str[i];
        if (isEmoji(ch, str[i + 1]))
            return true;
    }
    return false;
}

INT KRangeSliceHelper::Do(ROW row, COL col, const_token_ptr pToken)
{
    //非字符串token、公式跳过
	if (!(pToken && alg::const_vstr_token_assist::is_type(pToken)) || isCellFormula(row, col))
		return 0;

    //隐藏单元格也跳过
    IRowColOp* pRowColOp = m_pSheet->LeakOperator();
    if (pRowColOp && (pRowColOp->GetRowHidden(row) || pRowColOp->GetColHidden(col)))
        return 0;
    
    if (containsEmoji(alg::const_vstr_token_assist(pToken).get_value(), alg::const_vstr_token_assist(pToken).get_length()))
        return 0;

	QString contenet = krt::fromUtf16(alg::const_vstr_token_assist(pToken).get_value()) + "\n";

    int length = contenet.length();
    
    if (m_currentTextIndex + length >= g_maxSliceCount)
    {
        int start = 0;
        int tmpLen = length;
        while (m_currentTextIndex + tmpLen >= g_maxSliceCount)
        {
            m_currentTextIndex = 0;
            int separatorIndex = -1;
            for (int i = 0; i < sizeof(g_sentence_separator) / sizeof(WCHAR*); ++i)
            {
                separatorIndex = contenet.lastIndexOf(krt::fromUtf16(g_sentence_separator[i]), g_maxSliceCount + separatorIndex);
                if (separatorIndex != -1)
                    break;
            }

            m_sliceVec.push_back(QVector<SliceCellInfo>());
            int addLen = separatorIndex == -1 ? std::min(g_maxSliceCount, length) : separatorIndex + 1;
            addSliceCell(row ,col, start, start + addLen);
            start += addLen;
            tmpLen -= addLen;
            if (tmpLen > 0 && tmpLen < g_maxSliceCount)
            {
                m_sliceVec.push_back(QVector<SliceCellInfo>());
                addSliceCell(row ,col, start, length);
                break;
            }
        }
    }
    else
    {
        if (m_sliceVec.isEmpty())
            m_sliceVec.push_back(QVector<SliceCellInfo>());
        addSliceCell(row ,col, 0 , length);
    }
    return 0;
}

void KRangeSliceHelper::addSliceCell(int row, int col, int begin, int end)
{
    if (m_sliceVec.size() >= 1)
    {
        m_sliceVec[m_sliceVec.size() - 1].push_back({row ,col, m_sheetIdx, begin, end});
        m_currentTextIndex += end - begin;
        if (m_currentTextIndex >= g_maxSliceCount)
            m_currentTextIndex = 0;
    }
}

void KRangeSliceHelper::startCreateText(ISheet* pSheet, const RANGE& rg)
{
    if (!pSheet)
        return;
    pSheet->GetIndex(&m_sheetIdx);
    et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
	spSheetEnum->EnumCellValueRowbyRow(rg, this);
}

void KRangeSliceHelper::clearSliceCache()
{
    m_sliceVec.clear();
    m_currentTextIndex = 0;
}

KRangeSliceHelper::KRangeSliceHelper(KEtWorkbook* pWorkbook)
    : m_pWorkbook(pWorkbook)
{

}

KRangeSliceHelper::~KRangeSliceHelper()
{
    clearSliceCache();
}

HRESULT KRangeSliceHelper::beginSliceRange(etoldapi::_Worksheet* pWorksheet)
{
    if (!pWorksheet)
        return E_FAIL;

    ISheet* pSheet = pWorksheet->GetSheet();
    if (!pSheet)
        return E_FAIL;

    m_pSheet = pSheet;
    SHEETSTATE sheetState;
    pSheet->GetVisible(&sheetState);
    if (sheetState != ssVisible || !pSheet->IsGridSheet())
        return S_OK;

	ks_stdptr<etoldapi::Range> spRange;
	pWorksheet->get_UsedRange(&spRange);
	if (!spRange)
		return E_FAIL;

	ks_stdptr<IRangeInfo> spRangeInfo = spRange;
	if (!spRangeInfo)
		return E_FAIL;

	ks_stdptr<IKRanges> spRanges;
	spRangeInfo->GetIRanges(&spRanges);
	if (!spRanges)
		return E_FAIL;

	range_helper::ranges rgs(spRanges);

	if (rgs.size() != 1)
		return E_FAIL;

	const RANGE& range = *(rgs.at(0).second);
    wo::IEtRevisionContext *pCtx = _etcore_GetEtRevisionContext();
    if (pCtx)
    {
        auto accessPerm = PTAAP_None;
        if (!pCtx->getProtectionCtx()->isAllowEdit(range, &accessPerm))
        {
            if (accessPerm == PTAAP_Invisible)
            {
                return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
            }
        }
    }
	startCreateText(pSheet, range);
    return S_OK;
}

bool KRangeSliceHelper::getSliceInfo(int index, QMap<int, QString>& sliceInfo, int count)
{
    if (m_sliceVec.size() <= index)
        return false;

    for (int i = index; i < count + index && i < m_sliceVec.size(); ++i)
    {
        const QVector<SliceCellInfo>& cellInfos = m_sliceVec[i];
        if (cellInfos.isEmpty())
            continue;
        QString sliceText;
        for (int j = 0; j < cellInfos.size(); ++j)
        {
            const SliceCellInfo& cellInfo = cellInfos[j];
            QString cellText;
            getCellText({cellInfo.row, cellInfo.col}, cellInfo.sheetIdx, cellText);
            cellText += "\n";
            sliceText.append(cellText.mid(cellInfo.begin, cellInfo.end - cellInfo.begin));
        }
        sliceInfo.insert(i, sliceText);
    }
    return true;
}

//QMultiMap<std::pair<int, int>, std::pair<int, int>>: <切片id-错误id> <错误起始位置-错误终止位置>
void KRangeSliceHelper::getErrorItemCells(const QMultiMap<std::pair<int, int>, std::pair<int, int>>& errPos, QVector<std::pair<int, SliceCellInfo>>& cellVec)
{
    for (auto it = errPos.cbegin(); it != errPos.cend(); ++it)
    {
        int sliceIndex = it.key().first;
        if (m_sliceVec.size() <= sliceIndex || sliceIndex < 0)
            continue;
        const QVector<SliceCellInfo>& slice = m_sliceVec[sliceIndex];
        SliceCellInfo cellInfo = slice[0];
        int offset = cellInfo.begin;
        int totalLen = 0;
        for (int i = 0; i < slice.size(); ++i)
        {
            if (i != 0)
            {
                cellInfo.begin = cellInfo.end;
                cellInfo.end += slice[i].end;
            }
            if (cellInfo.begin <= it.value().first + offset && cellInfo.end >= it.value().second + offset)
            {
                SliceCellInfo info = slice[i];
                info.begin = slice[i].begin + it.value().first - totalLen;
                info.end = slice[i].begin + it.value().second - totalLen;
                cellVec.push_back({it.key().second, info});
                break;
            }
            totalLen += slice[i].end - slice[i].begin;
        }
    }
}

bool KRangeSliceHelper::getCellText(CELL cell, int sheetIdx, QString& cellText)
{
    IKWorksheet* pWorkSheet = m_pWorkbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWorkSheet)
		return false;

    ks_stdptr<IETStringTools> spStringTools;
    if (FAILED(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&spStringTools)))
        return false;

    if (!spStringTools)
        return false;

    spStringTools->SetEnv(pWorkSheet->GetSheet());

    ks_bstr bstr;
    spStringTools->GetCellText(NULL, cell.row, cell.col, &bstr, NULL, 0, NULL);
    cellText = bstr.empty() ? QString() : krt::fromUtf16(bstr);
    return true;
}

bool KRangeSliceHelper::modifyText(int sliceIndex, CELL cell, int sheetIdx, const QString& modifyText, int start, int end)
{
    QString cellText;
    if (!getCellText(cell, sheetIdx, cellText))
        return false;

    int len = end - start;
    cellText.replace(start, len, modifyText);
    RANGE rg(m_pWorkbook->GetBMP());
    rg.SetCell(sheetIdx, cell.row, cell.col);
    ks_stdptr<etoldapi::Range> spRange = m_pWorkbook->CreateRangeObj(rg);
    if (!spRange)
        return false;

    if (FAILED(spRange->put_Value2(KComVariant(krt::utf16(cellText)))))
        return false;
    
    return true;
}

bool KRangeSliceHelper::isCellFormula(ROW row, COL col)
{
    ks_stdptr<IBookOp> spBookOp;
    m_pWorkbook->GetCoreWorkbook()->GetBook()->GetOperator(&spBookOp);
    if (!spBookOp)
        return false;

    BOOL bFormula = FALSE;
	BOOL bArrayFormula = FALSE;
	spBookOp->GetCellFormulaFlags(m_sheetIdx, row, col, &bFormula, &bArrayFormula);
	return bFormula || bArrayFormula;
}

HRESULT KRangeSliceHelper::beginBatchSliceRange()
{
    etoldapi::_Workbook* pWorkbook = m_pWorkbook->GetCoreWorkbook();
    if (!pWorkbook)
        return E_FAIL;

    ks_stdptr<etoldapi::Worksheets> spWorksheets;
    pWorkbook->get_Worksheets(&spWorksheets);
    if (!spWorksheets)
        return E_FAIL;

    long sheetCount = 0;
    spWorksheets->get_Count(&sheetCount);
    for (long i = 1; i <= sheetCount; ++i)
    {
        KComVariant varIdx(static_cast<int64>(i), VT_I8);
		ks_stdptr<IKCoreObject> spCoreObj;
		spWorksheets->get_Item(varIdx, &spCoreObj);
		ks_stdptr<etoldapi::_Worksheet> spWorksheet = spCoreObj;
        if (!spWorksheet)
		    return E_FAIL;
        HRESULT hr = beginSliceRange(spWorksheet);
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

int KRangeSliceHelper::getTotalCount() const
{
    return m_sliceVec.size();
}
}
}  // namespace wo
