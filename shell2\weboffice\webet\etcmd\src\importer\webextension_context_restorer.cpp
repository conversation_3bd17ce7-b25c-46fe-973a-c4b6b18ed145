#include "webextension_context_restorer.h"
#include "dbsheet/et_dbsheet_dashboard_utils.h"

namespace wo
{

WebExtensionContextRestorer::WebExtensionContextRestorer(etoldapi::_Workbook* pSrcWorkbook,
                                                         etoldapi::_Workbook* pTarWorkbook,
                                                         std::unordered_map<UINT, UINT>& sheetIdMap)
        : m_pSrcWorkbook(pSrcWorkbook),
          m_pTarWorkbook(pTarWorkbook),
          m_sheetIdMap(sheetIdMap)
{

}

void WebExtensionContextRestorer::RestoreSheet(ISheet* pSheet)
{
    IKWebExtensionMgr* pWebExtensionMgr = m_pTarWorkbook->GetWebExtensionMgr();
    if (!pWebExtensionMgr)
        return;

    UINT webExtensionCount = 0;
    pWebExtensionMgr->GetWebExtensionCount(pSheet, &webExtensionCount);
    for (UINT i = 0; i < webExtensionCount; ++i)
    {
        ks_stdptr<IKWebExtension> spWebExtension;
        HRESULT hr = pWebExtensionMgr->GetWebExtension(pSheet, i, &spWebExtension);
        if (FAILED(hr))
            continue;

        RestoreWebExtension(spWebExtension);
    }
}

void WebExtensionContextRestorer::RestoreWebExtension(IKWebExtension* pWebExtension)
{
    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
    if (!DbDashboard::IsEtDataSourceType(dataSourceType))
        return;

    ks_castptr<IKWorkbookDataSource> spDataSource = spDataSourceHost->GetDataSource();
    ASSERT(spDataSource != nullptr);

    std::vector<PCWSTR> dataRangesToMove;
    std::vector<ks_wstring> dataRangesToUpdate;
    ks_stdptr<IKDataRangeEnum> spEnum;
    if (SUCCEEDED(spDataSource->GetDataRangeEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
    {
        do
        {
            IKDataRange* pDataRange = spEnum->GetDataRange();
            PCWSTR context = pDataRange->GetContext();
            ks_bstr newContext;
            HRESULT hr = GetNewContext(context, &newContext);
            if (FAILED(hr))
            {
                dataRangesToMove.push_back(pDataRange->GetKey());
                continue;
            }
            if (!newContext.isEqualString(context))
            {
                pDataRange->SetContext(newContext);
                dataRangesToUpdate.emplace_back(pDataRange->GetKey());
            }
        } while (SUCCEEDED(spEnum->Next()));
    }
    for (PCWSTR key: dataRangesToMove)
        spDataSource->UnRegisterRange(key);
    for (const auto& key: dataRangesToUpdate)
    {
        KComVariant varKey(key.c_str());
        ks_stdptr<IKDataRange> spDataRange;
        spDataSource->GetRange(varKey, &spDataRange);
        if (spDataRange)
            spDataSource->RegisterRange(key.c_str(), spDataRange);
    }
}

HRESULT WebExtensionContextRestorer::GetNewContext(PCWSTR context, BSTR* pNewContext)
{
    IBook* pSrcBook = m_pSrcWorkbook->GetBook();
    range_helper::ranges ranges;
    CS_COMPILE_FLAGS ccf =
            cpfAllowName | cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo | cpfForbidCrossBook;
    CS_COMPILE_PARAM compileParam(ccf, 0, 0, 0);
    HRESULT hr = pSrcBook->LeakOperator()->CompileRange(context, compileParam, &ranges, croNeedCalc);
    if (FAILED(hr))
        return hr;

    range_helper::ranges newRanges = range_helper::ranges::create_instance();
    IBook* pTarBook = m_pTarWorkbook->GetBook();
    UINT rangeCount = 0;
    ranges->GetCount(&rangeCount);
    for (UINT i = 0; i < rangeCount; ++i)
    {
        const RANGE* pRange = ranges.at(i).second;
        if (!pRange)
            continue;

        IDX sheetIdx = pRange->SheetFrom();
        ks_stdptr<ISheet> spSheet;
        pSrcBook->GetSheet(sheetIdx, &spSheet);
        if (!spSheet)
            return hr;

        UINT sheetId = spSheet->GetStId();
        auto iter = m_sheetIdMap.find(sheetId);
        if (iter == m_sheetIdMap.end())
            return E_FAIL;

        IDX newSheetIdx = INVALIDIDX;
        pTarBook->STSheetToRTSheet(iter->second, &newSheetIdx);
        if (newSheetIdx == INVALIDIDX)
            return E_FAIL;

        RANGE newRange(pTarBook->GetBMP());
        newRange.SetSheetFromTo(newSheetIdx, newSheetIdx);
        newRange.SetRowFromTo(pRange->RowFrom(), pRange->RowTo());
        newRange.SetColFromTo(pRange->ColFrom(), pRange->ColTo());
        newRanges.add(alg::STREF_THIS_BOOK, newRange);
    }
    if (newRanges.size() == 0)
        return E_FAIL;

    ccf = cpfCellA1 | cpfRgAbsRowFrom | cpfRgAbsRowTo | cpfRgAbsColFrom | cpfRgAbsColTo | cpfRgForceSheet;
    CS_COMPILE_PARAM decompileParam(ccf, 0, 0, 0);
    return pTarBook->LeakOperator()->DecompileRange(newRanges, decompileParam, pNewContext);
}

}