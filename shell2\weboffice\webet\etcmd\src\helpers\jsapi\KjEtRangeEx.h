﻿#ifndef _WEBET_KjEtRangeEx_H__
#define _WEBET_KjEtRangeEx_H__
#include "api/jsapi/pub/jsapiutility.h"

namespace wo
{

struct KjWOEtRangeEx
{
	static HRESULT GetInMainThread(bool bCefEngine,
		const JSContextEnv& jsEnv,
		int nAttributeIdx,
		JSInputData inputData,
		KJSVariant& retval,
		JSApiObject* pJSApiObj,
		KJSVariant& exception);

	static HRESULT SetInMainThread(const JSContextEnv& jsEnv,
		int nAttributeIdx,
		JSInputData inputData,
		KJSVariant& value,
		KJSVariant& exception);

	static HRESULT ExecuteInMainThread(bool bCefEngine,
		const JSContextEnv& jsEnv,
		int nAttributeIdx,
		JSInputData inputData,
		JSApiArguments& arguments,
		KJSVariant& retval,
		JSApiObject* pJSApiObj,
		KJSVariant& exception);

	static void GetProperties(JSInputData inputData,
		const JSContextEnv& jsEnv,
		FnJSAPIGet &fnGet,
		FnJSAPISet &fnSet,
		FnJSAPIExecute &fnExecute,
		IJSPropertyList *pProps);

	static void GetFunction(bool bCefEngine, JSApiObject* pJSApiObj);

	static FnJSAPIGet s_fnBaseGet;
	static FnJSAPISet s_fnBaseSet;
	static FnJSAPIExecute s_fnBaseExecute;
	static FnJSGetProperties s_fnBaseGetProps;
	static int s_nInsertCellPictureRaw;
	static int s_nCreate;
};

} // namespace wo

#endif //_WEBET_KjEtRangeEx_H__