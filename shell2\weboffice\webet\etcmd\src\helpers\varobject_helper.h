#ifndef __WEBET_VAROBJECT_HELPER_H__
#define __WEBET_VAROBJECT_HELPER_H__

#include "webbase/binvariant/binvarobj.h"

namespace wo
{
using binary_wo::VarObj;
namespace VarObjFieldValidation
{
    inline bool expectNumeric(const VarObj &obj, WebName name, bool printLog = true)
    {
        switch (obj.get_s(name).type())
        {
            case binary_wo::typeInt8: case binary_wo::typeUint8: case binary_wo::typeUint16:
            case binary_wo::typeInt16: case binary_wo::typeUint32: case binary_wo::typeInt32:
            case binary_wo::typeFloat32: case binary_wo::typeFloat64:
                return true;
            default:
                if (printLog)
                    WOLOG_ERROR << name << " has a VarObj unexpected type! Expected: numeric";
                return false;
        }
    }
    inline bool expectFloat(const VarObj &obj, WebName name, bool printLog = true)
    {
        switch (obj.get_s(name).type())
        {
            case binary_wo::typeFloat32: case binary_wo::typeFloat64:
                return true;
            default:
                if (printLog)
                    WOLOG_ERROR << name << " has a VarObj unexpected type! Expected: float";
                return false;
        }
    }
    inline bool expectIntegral(const VarObj &obj, WebName name, bool printLog = true)
    {
        switch (obj.get_s(name).type())
        {
            case binary_wo::typeInt8: case binary_wo::typeUint8: case binary_wo::typeUint16:
            case binary_wo::typeInt16: case binary_wo::typeUint32: case binary_wo::typeInt32:
                return true;
            default:
                if (printLog)
                    WOLOG_ERROR << name << " has a VarObj unexpected type! Expected: integral";
                return false;
        }
    }
    inline bool expectBool(const VarObj &obj, WebName name, bool printLog = true)
    {
        switch (obj.get_s(name).type())
        {
            case binary_wo::typeBool:
                return true;
            default:
                if (printLog)
                    WOLOG_ERROR << name << " has a VarObj unexpected type! Expected: bool";
                return false;
        }
    }
    inline bool expectString(const VarObj &obj, WebName name, bool printLog = true)
    {
        switch (obj.get_s(name).type())
        {
            case binary_wo::typeString:
                return true;
            default:
                if (printLog)
                    WOLOG_ERROR << name << " has a VarObj unexpected type! Expected: string";
                return false;
        }
    }
    inline bool expectArray(const VarObj &obj, WebName name, binary_wo::Types type = binary_wo::typeInvalid, bool printLog = true)
    {
        const VarObj array = obj.get_s(name);
        switch (array.type())
        {
            case binary_wo::typeArray:
            {
                if (type == binary_wo::typeInvalid)
                    return true;
                int cnt = array.arrayLength_s();
                if (cnt > 0)
                {
                    binary_wo::VarObj item = array.at_s(0);
                    if (item.type() != type)
                    {
                        WOLOG_ERROR << name << " item has a VarObj unexpected type!";
                        return E_INVALID_REQUEST;
                    }
                }
                return true;
            }
            default:
                if (printLog)
                    WOLOG_ERROR << name << " has a VarObj unexpected type! Expected: array";
                return false;
        }
    }
    inline bool expectStruct(const VarObj &obj, WebName name, bool printLog = true)
    {
        switch (obj.get_s(name).type())
        {
            case binary_wo::typeStruct:
                return true;
            default:
                if (printLog)
                    WOLOG_ERROR << name << " has a VarObj unexpected type! Expected: struct";
                return false;
        }
    }
    inline bool expectArrayBuffer(const VarObj &obj, WebName name, bool printLog = true)
    {
        switch (obj.get_s(name).type())
        {
            case binary_wo::typeArrayBuffer:
                return true;
            default:
                if (printLog)
                    WOLOG_ERROR << name << " has a VarObj unexpected type! Expected: ArrayBuffer";
                return false;
        }
    }
} // VarObjFieldValidation

inline bool varObjHasStringMember(const VarObj &obj, WebName name, PCWSTR defaultVal = __X(""))
{
    if (not VarObjFieldValidation::expectString(obj, name, false))
        return false;
    PCWSTR value = obj.field_str(name);
    if (0 == xstrncmp(value, defaultVal, xstrlen(defaultVal) + 1))
        return false;
    return true;
}

inline bool varObjHasIntergerMember(const VarObj &obj, WebName name, int defaultVal = 0)
{
    // 服务端不透传参数时, expectIntegral 校验可能失效, 因此用 expectNumeric
    if (not VarObjFieldValidation::expectNumeric(obj, name, false))
        return false;
    const int v = obj.field_int32(name);
    if (v == defaultVal)
        return false;
    return true;
}

} // wo
#define VAR_OBJ_EXPECT_NUMERIC(obj, name) if (!wo::VarObjFieldValidation::expectNumeric(obj, name)) return E_INVALID_REQUEST;
#define VAR_OBJ_EXPECT_FLOAT(obj, name) if (!wo::VarObjFieldValidation::expectFloat(obj, name)) return E_INVALID_REQUEST;
// 自2022.08 服务端修改了 server api 相关的架构之后, 暴露出了一个奇怪的现象
// 先前服务端透传的前端传参可以被检验为整型, 但现在由服务端发来的参数不会被视作整型
// 因此 VAR_OBJ_EXPECT_INTEGRAL 建议仅在websocket及其他确定由服务端透传参数的地方使用
#define VAR_OBJ_EXPECT_INTEGRAL(obj, name) if (!wo::VarObjFieldValidation::expectIntegral(obj, name)) return E_INVALID_REQUEST;
#define VAR_OBJ_EXPECT_BOOL(obj, name) if (!wo::VarObjFieldValidation::expectBool(obj, name)) return E_INVALID_REQUEST;
#define VAR_OBJ_EXPECT_STRING(obj, name) if (!wo::VarObjFieldValidation::expectString(obj, name)) return E_INVALID_REQUEST;
#define VAR_OBJ_EXPECT_ARRAY(obj, name) if (!wo::VarObjFieldValidation::expectArray(obj, name)) return E_INVALID_REQUEST;
#define VAR_OBJ_EXPECT_ARRAY_FOR_TYPE(obj, name, type) if (!wo::VarObjFieldValidation::expectArray(obj, name, type)) return E_INVALID_REQUEST;
#define VAR_OBJ_EXPECT_STRUCT(obj, name) if (!wo::VarObjFieldValidation::expectStruct(obj, name)) return E_INVALID_REQUEST;
#define VAR_OBJ_EXPECT_ARRAYBUFFER(obj, name) if (!wo::VarObjFieldValidation::expectArrayBuffer(obj, name)) return E_INVALID_REQUEST;

#endif // __WEBET_VAROBJECT_HELPER_H__