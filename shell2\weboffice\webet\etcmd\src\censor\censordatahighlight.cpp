#include "etstdafx.h"
#include "censordatahighlight.h"
#include "etcore/little_alg.h"
#include "workbook.h"
#include "etcore/et_core_sheet.h"
#include "webdeclare.h"
#include <persist/persist_helper.h>
#include <applogic/et_api_convert.h>
#include <applogic/et_apihost.h>
#include <kern/oleauto.h>
#include <webbase/binvariant/binreader.h>
#include "etcmd/src/util.h"
#include "dbsheet/db_export_helper.h"
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include <public_header/drawing/model/group_shape.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include <qbytearray.h>
#include <QJsonDocument>
#include <QJsonObject>
#include <public_header/webcommon/src/urlutils.h>
#include <public_header/drawing/view/shape_visual.h>
#include <public_header/drawing/text/visual/text_frame_visual.h>
#include <public_header/drawing/text/control/text_frame_control.h>
#include <public_header/drawing/text/control/text_frame_factory.h>
#include <public_header/drawing/text/data/text_frame.h>

#include <public_header/chart/src/datasource/kctchartdatasource.h>
#include <public_header/chart/src/mvc/kctchartlayer.h>
#include <public_header/chart/src/model/kctchart.h>
#include <public_header/chart/src/model/kcttitle.h>
#include <public_header/chart/src/model/kctaxes.h>
#include <public_header/chart/src/model/kctaxis.h>
#include <public_header/chart/src/mvc/kctshapetreeapifactory.h>

#include <public_header/drawing/api/dghost_i.h>

#define QRCODE_GUID __X("44B7C0F4-79DB-4F8B-9303-0E098D69D8BE")
#define BARCODE_GUID __X("11875FA4-B263-4FB5-9052-7E12DCA7BDF3")

extern Callback* gs_callback;
namespace wo {

inline
LONG DgKsoRGB2CoreRGB(KsoRGBType ksoRGB)
{
	return ((ksoRGB & 0x00ff0000) >> 16)
		| (ksoRGB & 0x0000ff00)
		| ((ksoRGB & 0x000000ff) << 16)
		| 0xff000000;
}

CensorKeyWordHighLightHandler::CensorKeyWordHighLightHandler(KEtWorkbook* pWorkbook)
	: m_wwb(pWorkbook)
{
	_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStrTools);
	m_spBookOp = m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator();
	m_sheetRow = INVALIDIDX;
	m_blkRow = INVALIDIDX;
	m_sheetCol = INVALIDIDX;
	m_blkCol = INVALIDIDX;
	m_sheetIdx = INVALIDIDX;
	m_QrCodeCurrentCol = 0;
	m_QrCodeCurrentRow = 0;
}

CensorKeyWordHighLightHandler::~CensorKeyWordHighLightHandler()
{
}

void CensorKeyWordHighLightHandler::StartHandleETCell()
{
	if (nullptr == m_spBookOp)
		return;
	
	ExportOptimizer optimizer(m_spBookOp);

	ks_stdptr<_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets* pWorksheets = spWorkbook->GetWorksheets();
	int cnt = pWorksheets->GetSheetCount();

	KComVariant vBefore;
	KComVariant vAfter = wo::util::getLastSheetIdx(spWorkbook->GetBook()) + 1; // 供 oldapi::Worksheets使用，其计数从1开始

	KComVariant vCount = 1;
	KComVariant vType(xlWorksheet);
	ks_stdptr<IKCoreObject> spObj;
	ks_stdptr<etoldapi::Worksheets> spSheets = pWorksheets;
	spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid);
	m_spExtraCensorSheet = spObj;
	VS(m_spExtraCensorSheet->GetSheet()->GetIndex(&m_sheetIdx));
	m_sheetRow = m_sheetCol = m_blkRow = m_blkCol = 0;
	
	for (size_t shtIdx = 0; shtIdx < cnt; shtIdx++)
	{
		if (shtIdx == m_sheetIdx)
		{
			continue;
		}

		ks_castptr<etoldapi::_Worksheet> spWorksheet = pWorksheets->GetSheetItem(shtIdx);
		_CollectCellText(spWorksheet);
		prepareCommentHyperLink(spWorksheet.get());

		_handleShape(spWorksheet.get());

	}

}

void CensorKeyWordHighLightHandler::_handleShape(etoldapi::_Worksheet *pWorksheet)
{

	ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
	oplGetSheetOplData(pWorksheet->GetSheet(), &spCanvas);
	if (!spCanvas)
		return;
	ks_castptr<EtShapeTree> pshapeTree = spCanvas;
	_handleShape(pshapeTree);
}	

void CensorKeyWordHighLightHandler::_handleShape(drawing::AbstractShape* shape)
{

	if (!shape)
	{
		return;
	}

	ks_stdptr<KsoShape> pShape;
	GetApiShape(shape, &pShape);

	if(shape->isGroupShape())
	{
		drawing::GroupShape* groupShape = static_cast<drawing::GroupShape*>(shape);
		for (int i = 0, n = groupShape->childCount(); i < n; i++)
		{
			drawing::AbstractShape* abstractShape = groupShape->childAt(i);
			_handleShape(abstractShape);
		}
		return;
	}

	_handleExtData(shape);

	_handleChart(shape);

	_realHandleShape(shape);

}

void CensorKeyWordHighLightHandler::_handleChart(drawing::AbstractShape* shape)
{
	if (!shape->hasChart())
	{
		return;
	}

	ks_castptr<chart::KCTChartLayer> chartLayer = shape->getChild(0);
	if (!chartLayer)
	{
		return;
	}

	chart::KCTChart* pChart = chartLayer->ensureChart();
	if (!pChart)
	{
		return;
	}

	_realHandleChart(pChart);

}

void CensorKeyWordHighLightHandler::_realHandleChart(AbstractObject* pAbsOject)
{
	int childCount = pAbsOject->getChildCount();
	for (int i = 0; i < childCount; i++)
	{
		AbstractObject* pObject = pAbsOject->getChildObject(i);
		drawing::AbstractShape* pShape = dynamic_cast<drawing::AbstractShape*>(pObject);

		if (pShape == nullptr)
		{
			continue;
		}

		_realHandleChartShape(pShape);

		_realHandleChart(pObject);
	}
}

void CensorKeyWordHighLightHandler::_handleShapeBase(KsoTextFrame2* pTextFrame2, KsoFillFormat* pKsoFillFormat, bool isMath)
{

	if (!pTextFrame2)
	{
		return;
	}

	ks_stdptr<KsoTextRange2> ksoTextRange2;
	if (FAILED(pTextFrame2->get_TextRange(&ksoTextRange2)))
	{
		return;
	}

	ks_bstr text;
	if (FAILED(ksoTextRange2->get_Text(&text)))
	{
		return;
	}

	HighlightWordData* keyWordData = nullptr;
	_GetETCellKeyWordData(text.c_str(), &keyWordData);
	if (!keyWordData)
	{
		WOLOG_ERROR << "_handleTextStream: keyword data is empty";
		return;
	}

	// 处理背景颜色
	if (keyWordData->backgroudColor && pKsoFillFormat)
	{
		ks_stdptr<KsoColorFormat> colorFormat;
		pKsoFillFormat->get__ForeColor(&colorFormat);
			// 这里先转一下 里面要转回去
		int color = DgKsoRGB2CoreRGB(*keyWordData->backgroudColor) & RGB_MASK;
		colorFormat->put_RGB(color);
	}

	// 字体颜色
	if (keyWordData->fontColor)
	{
		modifyFontColor(ksoTextRange2, (UINT32)*keyWordData->fontColor);
	}

	if (!keyWordData->fonts)
	{
		if (!isMath) 
		{
			// 对象中的文本因为无法查找 也提取带sheet显示
			_handleQrCodeText(text.c_str());
		}
		return;
	}

	// 敏感词的字体
	HighlightFont** keyWordFontsData = keyWordData->fonts->data;
	int length = keyWordData->fonts->size;
	for (int i = 0; i < length; i++)
	{

		HighlightFont* fontInfo = keyWordFontsData[i];
		if (!fontInfo->color || fontInfo->startOffset > fontInfo->endOffset)
		{
			continue;
		}
	
		ks_stdptr<KsoTextRange2> tempRange2;

		if (FAILED(ksoTextRange2->get_Characters(fontInfo->startOffset, fontInfo->endOffset - fontInfo->startOffset + 1, &tempRange2)))
		{
			continue;
		}

		modifyFontColor(tempRange2, *fontInfo->color);

	}

	if (!isMath) 
	{
		// 对象中的文本因为无法查找 也提取带sheet显示
		_handleQrCodeText(text.c_str());
	}
}

void CensorKeyWordHighLightHandler::_realHandleShape(drawing::AbstractShape* abstractShape)
{
	ks_stdptr<KsoShape> pShape;
	GetApiShape(abstractShape, &pShape);
	if (!pShape) 
	{
		return;
	}

	ks_stdptr<KsoTextFrame2> pTextFrame2;
	if (FAILED(pShape->get__TextFrame2(&pTextFrame2)))
	{
		return;
	}

	ks_stdptr<KsoFillFormat> fillFormat;
	pShape->get__Fill(&fillFormat);
	_handleShapeBase(pTextFrame2, fillFormat, abstractShape->isMath());
}


void CensorKeyWordHighLightHandler::_realHandleChartShape(drawing::AbstractShape* abstractShape)
{
	ks_stdptr<KsoTextFrame2> pTextFrame2;
	ks_stdptr<KsoChartFormat> pKsoChartFormat;
	GetTextFrame(abstractShape, &pTextFrame2, &pKsoChartFormat);
	if (!pTextFrame2 || !pKsoChartFormat)
	{
		return;
	}

	ks_stdptr<KsoFillFormat> fillFormat;
	pKsoChartFormat->get_Fill(&fillFormat);

	_handleShapeBase(pTextFrame2, fillFormat, abstractShape->isMath());

}

void CensorKeyWordHighLightHandler::modifyFontColor(KsoTextRange2* ksoTextRange2, int color)
{
	ks_stdptr<KsoFont2> spKsoFont;
	if (FAILED(ksoTextRange2->get_Font(&spKsoFont)))
	{
		return;
	}
	
	ks_stdptr<KsoFillFormat> ksoFillFormat;
	if (FAILED(spKsoFont->get_Fill(&ksoFillFormat)))
	{
		return;
	}

	ks_stdptr<KsoColorFormat> fontColorFormat;
	if (FAILED(ksoFillFormat->get__ForeColor(&fontColorFormat)))
	{
		return;
	}
	// 这里先转一下 里面要转回去
	color = DgKsoRGB2CoreRGB(color) & RGB_MASK;
	fontColorFormat->put_RGB(color);
}

void CensorKeyWordHighLightHandler::GetApiShape(IKShape* pShape, KsoShape** ppShape)
{
	drawing::ShapeTreeControl* pCtl = getShapeTreeControl(pShape);
	ks_stdptr<IKCoreObject> spParent;
	pCtl->getSupLayerControl()->getCoreObject(pCtl->getLayer()->getSupLayer()->getModel(), &spParent);
	if (spParent != nullptr)
		getShapeTreeApiFactory(pShape)->CreateShape(spParent, pShape, ppShape);
}

void CensorKeyWordHighLightHandler::GetTextFrame(drawing::AbstractShape* pAbShape, KsoTextFrame2** ppTextFrame, KsoChartFormat** ppKsoChartFormat)
{

	if (!pAbShape || !pAbShape->getActiveShapeVisual())
	{
		return;
	}

	if (pAbShape->getModelType() == ChartModelTypeSeriesDataLabelText)
	{
		return;
	}


	drawing::AbstractTextframeVisual* abTextFrameVisual = pAbShape->getActiveShapeVisual()->textFrameVisual();
	if (!abTextFrameVisual)
	{
		return;
	}

	ks_castptr<drawing::TextFrameControl> textFrameCtrl = abTextFrameVisual->getLayerControl();

	ks_castptr<drawing::AbstractTextFrame> pTextFrame = textFrameCtrl->getLayer();
	ks_castptr<chart::KCTShape> pctShape = pTextFrame->getParent();
	ks_castptr<drawing::ShapeTreeControl> shapeTreeControl = textFrameCtrl->getSupLayerControl();
	ks_stdptr<IKCoreObject> spShape;

	HRESULT hr = S_OK;
	if (pAbShape->getModelType() == ChartModelTypeLegendEntryText)
	{
		hr = shapeTreeControl->getCoreObject(pctShape->getParent(), &spShape);
	}
	else 
	{
		hr = shapeTreeControl->getCoreObject(pctShape, &spShape);
	}

	if (FAILED(hr))
	{
		return;
	}

	ks_stdptr<KsoChartShape> spChartShape = spShape;
	if (!spChartShape)
		return;

	ks_stdptr<KsoChartFormat> spChartFormat;
	spChartShape->get_Format(&spChartFormat);
	if (!spChartFormat)
		return;
	
	ks_castptr<chart::KCTShapeTreeApiFactory> spFactory = shapeTreeControl->getApiFactory();
	ks_stdptr<KsoTextFrame2> spTextFrame2;
	IKTextFrame* ptrTxtFrame = pTextFrame.get();
	hr = spFactory->CreateCTTextFrame2(spChartFormat, pctShape, (IKTextFrame**)&ptrTxtFrame, 1, &spTextFrame2);
	VERIFY(SUCCEEDED(hr));
	*ppTextFrame = spTextFrame2.detach();
	*ppKsoChartFormat = spChartFormat.detach();

}

void CensorKeyWordHighLightHandler::_handleExtData(drawing::AbstractShape* shape)
{
	ks_stdptr<IKExtData> spData;
	if (!SUCCEEDED(shape->GetExtData(&spData)))
	{
		return;
	}

	ks_bstr type;
	spData->GetExtDataType(&type);
	bool isQrCode = xstrcmp(type.c_str(), QRCODE_GUID) == 0;
	bool isBarCode = xstrcmp(type.c_str(), BARCODE_GUID) == 0;
	if (!isQrCode && !isBarCode)
	{
		return;
	}

	ks_bstr d;
	spData->GetExtData(&d);
	const QString& data = krt::fromUtf16(d.c_str());
	QByteArray json  = QByteArray::fromBase64(data.toStdString().c_str());

	QJsonParseError jsonError;
	QJsonDocument jsonDoc(QJsonDocument::fromJson(json, &jsonError));

	if(jsonError.error != QJsonParseError::NoError)
	{
		WOLOG_INFO << "json error!" << jsonError.errorString();
		return;
	}

	QJsonObject rootObj = jsonDoc.object();

	QString text;
	if (isQrCode)
	{
		auto it = rootObj.find("LastUrl");
		if (it == rootObj.end())
		{
			return;
		}

		std::unordered_map<std::string, std::string> parameters = GetUrlParameters(it.value().toString().toStdString());
		auto iter = parameters.find("text");
		if (iter == parameters.end())
			return;

		text = QString::fromStdString(iter->second);
	}
	else 
	{
		auto it = rootObj.find("Text");
		if (it == rootObj.end())
		{
			return;
		}
		text = it.value().toString();
	}

	_handleQrCodeText(krt::utf16(text));
}

void CensorKeyWordHighLightHandler::_creatQrCodeSheet()
{

	if (m_spQrCodeSheet)
	{
		return;	
	}

	ks_stdptr<_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets* pWorksheets = spWorkbook->GetWorksheets();
	KComVariant vBefore;
	KComVariant vAfter = wo::util::getLastSheetIdx(spWorkbook->GetBook()) + 1;
	KComVariant vCount = 1;
	KComVariant vType(xlWorksheet);
	ks_stdptr<IKCoreObject> spObj;
	ks_stdptr<etoldapi::Worksheets> spSheets = pWorksheets;
	spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid);
	m_spQrCodeSheet = spObj;

}

void CensorKeyWordHighLightHandler::_handleQrCodeText(PCWSTR text)
{

	_creatQrCodeSheet();

	int sheetIdx = 0;
	m_spQrCodeSheet->GetSheet()->GetIndex(&sheetIdx);
	
	int maxRow = m_wwb->GetBMP()->cntRows - 1;
	int maxCol = 10;

	if (m_QrCodeCurrentCol > maxCol)
	{
		m_QrCodeCurrentCol = 0;
		m_QrCodeCurrentRow++;
	}

	if (m_QrCodeCurrentRow > maxRow)
	{
		return;
	}

	RANGE rg(m_wwb->GetBMP());
	rg.SetCell(sheetIdx, m_QrCodeCurrentRow, m_QrCodeCurrentCol++);
	if (!rg.IsValid())
	{
		return;
	}

	HRESULT hr = m_spBookOp->SetCellText(rg, text, cvoForceText);
	if (FAILED(hr))
	{
		return;
	}

	_SetETCellKeyWordHighlight(text, rg, false);

}

void CensorKeyWordHighLightHandler::getETCellText(const_token_ptr pToken, BSTR* bstrCellText)
{
	if (!pToken)
		return;

	if (m_spStrTools)
	{
		m_spStrTools->GetCellText(pToken, NULL, bstrCellText);
	}
}

void CensorKeyWordHighLightHandler::_GetETCellKeyWordData(PCWSTR str, HighlightWordData** data)
{
	if (!gs_callback)
		return;

	QString qCellStr(krt::fromUtf16(str));
	QByteArray utf8 = qCellStr.toUtf8();
	WebSlice slice = {(WebByte*)utf8.data(), utf8.size()};
	gs_callback->highlightKeyword(&slice, data);
}

void CensorKeyWordHighLightHandler::_HandleETCellContent(ROW row,
	COL col,
	IDX sheetIdx,
	PCWSTR cellText,
	BOOL bCellFormula)
{
	RANGE rg(m_wwb->GetBMP());
	rg.SetCell(sheetIdx, row, col);
	if (!rg.IsValid())
		return;

	_SetETCellKeyWordHighlight(cellText, rg, bCellFormula);
}

void CensorKeyWordHighLightHandler::_SetETCellKeyWordHighlight(PCWSTR str, const RANGE& rg, BOOL bCellFormula)
{
	ks_stdptr<etoldapi::Range> pCellRg = m_wwb->CreateRangeObj(rg);
	if (!pCellRg)
		return;

	HighlightWordData* keyWordData = nullptr;
	_GetETCellKeyWordData(str, &keyWordData);
	if (!keyWordData)
	{
		WOLOG_ERROR << "SetETCellKeyWordHighlight: keyword data is empty";
		return;
	}

	if (keyWordData->backgroudColor || keyWordData->fontColor)
		setBackgroundFontColor(pCellRg.get(), keyWordData);

	if (!keyWordData->fonts)
		return;

	int length = keyWordData->fonts->size;
	if (bCellFormula && length)
	{
		// fonts数组里面有值，就有敏感词
		HRESULT hr = m_spBookOp->SetCellText(rg, str, cvoForceText);
		if (FAILED(hr))
		{
			WOLOG_ERROR << "SetETCellKeyWordHighlight: SetCellText is failed";
			return;
		}
	}

	HighlightFont** keyWordFontsData = keyWordData->fonts->data;
	if (!keyWordFontsData)
	{
		WOLOG_ERROR << "SetETCellKeyWordHighlight: fonts->data is empty";
		return;
	}
	for (int i = 0; i < length; ++i)
	{
		HighlightFont* font = keyWordFontsData[i];
		if (font)
		{
			setKeyWordFontColor(font, pCellRg, 0);
		}
	}
	
}

HRESULT CensorKeyWordHighLightHandler::_CollectCellText(etoldapi::_Worksheet *pWorksheet)
{
	ISheet* pSheet = pWorksheet->GetSheet();
	IDX shtIdx = INVALIDIDX;
	pSheet->GetIndex(&shtIdx);
	RECT rcUsed = {0};
	pSheet->CalcUsedScale(&rcUsed);
	RANGE rgUsed = Rect2Range(rcUsed, shtIdx, pSheet->GetBMP());
	if(!rgUsed.IsValid())
	{
		return S_OK;
	}

	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);

	m_spStrTools->SetEnv(pSheet);
	KEnumETCellValueAcpt strsCollector(this, shtIdx);
	spSheetEnum->EnumCellValueRowbyRow(rgUsed, &strsCollector);
	return S_OK;
}

void CensorKeyWordHighLightHandler::setKeyWordFontColor(HighlightFont* fontInfo, etoldapi::Range* pCellRg, int32_t prefixLen)
{
	if (!fontInfo->color)
	{
		WOLOG_ERROR << "SetKeyWordFontColor: font->color is empty";
		return;
	}
	if ((fontInfo->startOffset) > (fontInfo->endOffset))
	{
		WOLOG_ERROR << "SetKeyWordFontColor: startOffset is bigger than endOffset";
		return;
	}

	VARIANT varStart = { 0 };
	V_VT(&varStart) = VT_I4;
	V_I4(&varStart) = fontInfo->startOffset + 1 + prefixLen;
	VARIANT varCch = { 0 };
	V_VT(&varCch) = VT_I4;
	V_I4(&varCch) = (fontInfo->endOffset - fontInfo->startOffset + 1);
	KCOMPTR(Characters) ptrCharacters;
	HRESULT hr = pCellRg->get_Characters(varStart, varCch, &ptrCharacters);
	if (SUCCEEDED(hr) && ptrCharacters)
	{
		KCOMPTR(Font) ptrFont;
		hr = ptrCharacters->get_Font(&ptrFont);
		if (SUCCEEDED(hr) && ptrFont)
		{
			EtColor clr;
			clr.setARGB((UINT32)*fontInfo->color);
			putEtColorHelp::PutEtColor(ptrFont.get(), m_wwb->GetCoreWorkbook()->GetBook(), clr);
		}
	}
}

void CensorKeyWordHighLightHandler::prepareCommentHyperLink(etoldapi::_Worksheet *pWorksheet)
{
	ISheet* pSheet = pWorksheet->GetSheet();
	IDX shtIdx = INVALIDIDX;
	VS(pSheet->GetIndex(&shtIdx));
	ks_bstr sheetName;
	VS(pWorksheet->get_Name(&sheetName));

	ks_stdptr<IKDrawingCanvas> spCommentCanvas;

	ks_stdptr<IUnknown> spUnk;
	if (SUCCEEDED(pSheet->GetExtDataItem(edSheetCommentDrawingCanvas, &spUnk)))
		spUnk->QueryInterface(IID_IKDrawingCanvas, (void**)&spCommentCanvas);
	if (spCommentCanvas)
	{
		ks_stdptr<ICellComments> spCMTs = spCommentCanvas;
		INT numCellComment = 0;
		spCMTs->GetCount(&numCellComment);
		for (INT i = 0; i < numCellComment; i++)
		{
			ks_stdptr<ICellComment> spICellComment;
			spCMTs->GetItem(i, &spICellComment);
			ASSERT(spICellComment);

			ks_bstr bstrCellComment;
			spICellComment->GetContent(&bstrCellComment);
			if (bstrCellComment.empty())
				continue;

			HighlightWordData* keyWordData = nullptr;
			_GetETCellKeyWordData(bstrCellComment.c_str(), &keyWordData);
			if (!keyWordData)
			{
				WOLOG_ERROR << "SetETCellKeyWordHighlight: keyword data is empty";
				continue;
			}
			// 评论/超链接等附加信息, 不需要在原始单元格赋予默认色, 因此仅处理fonts反馈的"有关键词"信息
			if (!keyWordData->fonts)
				continue;
			
			CELL cell;
			VS(spICellComment->GetBindCell(&cell));
			RANGE rg(m_wwb->GetBMP());
			rg.SetCell(shtIdx, cell.row, cell.col);
			if (rg.IsValid())
				buildExtraSheetCell(sheetName.c_str(), rg, bstrCellComment.c_str(), keyWordData);
		}
	}

	ks_stdptr<Hyperlinks> spHyperlinks;
	pWorksheet->get_Hyperlinks(&spHyperlinks);
	if (spHyperlinks)
	{
		long cntLinks = 0;
		spHyperlinks->get_Count(&cntLinks);

		for (long i = 1; i <= cntLinks; i++)
		{
			ks_stdptr<Hyperlink> tempLink;
			spHyperlinks->get_Item(KComVariant(i, VT_I4), &tempLink);

			ks_bstr bstrCellAddress;
			tempLink->get_Address(&bstrCellAddress);
			if (bstrCellAddress.empty())
				continue;

			long RHS = 0;
			if (FAILED(tempLink->get_Type(&RHS)))
			{
				continue;
			}

			QString cellStr;
			int32_t prefixLen = 0;
			if (RHS != 0)
			{
				// 对象类型超链接	
				cellStr = buildShapeAddStr(shtIdx, sheetName.c_str(), bstrCellAddress.c_str(), prefixLen);
			}
			else 
			{
				// 单元格超链接
				cellStr = buildCellAddStr(tempLink, shtIdx, sheetName.c_str(), bstrCellAddress.c_str(), prefixLen);
			}
			if (cellStr.isEmpty())
			{
				continue;
			}

			HighlightWordData* keyWordData = nullptr;
			_GetETCellKeyWordData(bstrCellAddress.c_str(), &keyWordData);
			if (keyWordData && !keyWordData->fonts)
			{
				keyWordData = nullptr;
			}
			RANGE dstCellRg{dstCellInExtraSheet()};

			setHyperlink(dstCellRg, bstrCellAddress.c_str());

			setCellAndHighlight(dstCellRg, krt::utf16(cellStr), prefixLen, keyWordData);
		}
	}
}

void CensorKeyWordHighLightHandler::setHyperlink(const RANGE &rg, PCWSTR bstrAddress)
{

	ks_stdptr<Range> rangeApi = m_wwb->CreateRangeObj(rg);

	ks_stdptr<IKHyperlinks>	spHyplinks;
	VS(m_spExtraCensorSheet->GetSheet()->GetExtDataItem(edSheetHyperlinks, (IUnknown**)&spHyplinks));
	if (!spHyplinks)
    	return;

	ks_stdptr<Range> spAnchorRange;
	HRESULT hr = rangeApi->QueryInterface(IID_Range, (void**)&spAnchorRange);
	if (FAILED(hr))
	{
		return;
	}

	ks_stdptr<IKHyperlink> spHL;
	range_helper::ranges rgs;
	app_helper::GetIRanges(spAnchorRange, &rgs);
	if (rgs.size() != 1)
		return;
	const RANGE* pAnchorRg = rgs.at(0).second;
	hr = spHyplinks->New(&spHL, pAnchorRg);

	// 数据
	spHL->SetAddress(bstrAddress);
}

QString CensorKeyWordHighLightHandler::buildShapeAddStr(IDX shtIdx, PCWSTR sheetName, PCWSTR addStr, int32_t &prefixLen)
{
	QString cellStr = QStringLiteral("[Sheet: %1, shape ]: ")
		.arg(krt::fromUtf16(sheetName));
	prefixLen = cellStr.size();
	cellStr.append(krt::fromUtf16(addStr));
	return cellStr;
}

QString CensorKeyWordHighLightHandler::buildCellAddStr(Hyperlink* hyperlink, IDX shtIdx, PCWSTR sheetName, PCWSTR addStr, int32_t &prefixLen)
{
	ks_stdptr<etoldapi::Range> spApiRg;
	VS(hyperlink->get_Range(&spApiRg));
	long row = INVALIDIDX, col = INVALIDIDX;
	// 取左上单元格. 必要时可以取整个超链接区域
	VS(spApiRg->get_Row(&row));
	VS(spApiRg->get_Column(&col));
	// 1 base -> 0 base
	--row;
	--col;
	RANGE rg(m_wwb->GetBMP());
	rg.SetCell(shtIdx, row, col);
	QString cellStr;
	if (!rg.IsValid())
	{
		return cellStr;
	}

	range_helper::ranges rgs = range_helper::ranges::create_instance();
	rgs.add(alg::STREF_THIS_BOOK, rg);
	ks_bstr cellPos;
	CS_COMPILE_PARAM ccp;
	ccp.nSheet = rg.SheetFrom();
	VS(m_spBookOp->DecompileRange(rgs, ccp, &cellPos));
	
	cellStr = QStringLiteral("[Sheet: %1, Cell: %2]: ")
		.arg(krt::fromUtf16(sheetName)).arg(krt::fromUtf16(cellPos.c_str()));
	prefixLen = cellStr.size();
	cellStr.append(krt::fromUtf16(addStr));

	return cellStr;
}

void CensorKeyWordHighLightHandler::setCellAndHighlight(const RANGE &dstCellRg, PCWSTR cellStr, int32_t prefixLen, HighlightWordData* keyWordData)
{
	ASSERT(dstCellRg.IsValid());
	VS(m_spBookOp->SetCellText(dstCellRg, cellStr, cvoForceText));
	ks_stdptr<etoldapi::Range> pCellRg = m_wwb->CreateRangeObj(dstCellRg);
	if (nullptr == pCellRg || !keyWordData)
		return;
	if (keyWordData->backgroudColor || keyWordData->fontColor)
		setBackgroundFontColor(pCellRg.get(), keyWordData);

	if (!keyWordData->fonts)
		return;

	int length = keyWordData->fonts->size;

	HighlightFont** keyWordFontsData = keyWordData->fonts->data;
	if (nullptr == keyWordFontsData)
	{
		WOLOG_ERROR << "SetETCellKeyWordHighlight: fonts->data is empty";
		return;
	}
	for (int i = 0; i < length; ++i)
	{
		HighlightFont* font = keyWordFontsData[i];
		if (font)
			setKeyWordFontColor(font, pCellRg, prefixLen);
	}
}

void CensorKeyWordHighLightHandler::buildExtraSheetCell(
	PCWSTR sheetName, const RANGE& rg, PCWSTR str, HighlightWordData* keyWordData)
{
	range_helper::ranges rgs = range_helper::ranges::create_instance();
	rgs.add(alg::STREF_THIS_BOOK, rg);
	ks_bstr cellPos;
	CS_COMPILE_PARAM ccp;
	ccp.nSheet = rg.SheetFrom();
	VS(m_spBookOp->DecompileRange(rgs, ccp, &cellPos));
	
	QString cellStr = QStringLiteral("[Sheet: %1, Cell: %2]: ")
		.arg(krt::fromUtf16(sheetName)).arg(krt::fromUtf16(cellPos.c_str()));
	int32_t prefixLen = cellStr.size();
	cellStr.append(krt::fromUtf16(str));

	RANGE dstCellRg{dstCellInExtraSheet()};
	ASSERT(dstCellRg.IsValid());
	VS(m_spBookOp->SetCellText(dstCellRg, krt::utf16(cellStr), cvoForceText));
	ks_stdptr<etoldapi::Range> pCellRg = m_wwb->CreateRangeObj(dstCellRg);
	if (nullptr == pCellRg)
		return;
	if (keyWordData->backgroudColor || keyWordData->fontColor)
		setBackgroundFontColor(pCellRg.get(), keyWordData);

	if (!keyWordData->fonts)
		return;

	int length = keyWordData->fonts->size;

	HighlightFont** keyWordFontsData = keyWordData->fonts->data;
	if (nullptr == keyWordFontsData)
	{
		WOLOG_ERROR << "SetETCellKeyWordHighlight: fonts->data is empty";
		return;
	}
	for (int i = 0; i < length; ++i)
	{
		HighlightFont* font = keyWordFontsData[i];
		if (font)
			setKeyWordFontColor(font, pCellRg, prefixLen);
	}
}

void CensorKeyWordHighLightHandler::setBackgroundFontColor(etoldapi::Range* pCellRg, HighlightWordData* keyWordData)
{
	KXF xf;
	KXFMASK mask;
	DWORD argb = 0;
	// 填充背景色
	if (keyWordData->backgroudColor)
	{
		xf.fill.setType(eftPatternSolid);
		mask.inc_eft = 1;
		argb = *keyWordData->backgroudColor;
		EtColor color;
		color.setARGB(argb);
		xf.fill.setBack(color);
		mask.inc_clrBack = 1;

		color.setAUTO();
		xf.fill.setFore(color);
		mask.inc_clrFore = 1;
	}
	// 字体颜色
	if (keyWordData->fontColor)
	{
		argb = *keyWordData->fontColor;
		xf.font.clr.setARGB(argb);
		mask.inc_clr = 1;	
	}
	ks_stdptr<IFormatHost> host = pCellRg;
	host->SetXF(&mask, &xf);
}

namespace
{
// 手动分块处理. 常量与 etcore/data_engine/blockgrid_common.h 保持一致
// 如果不保持一致, 也仅仅是无法充分利用表格分块提供的性能优化, 逻辑上还是没问题的
enum
{
#if defined(X_BIT_32)
	BLKCELLCNTBIT = 9,
#elif defined(X_BIT_64)
	BLKCELLCNTBIT = 8,
#endif
	BLKROWBIT = 6,
	BLKROW = 1 << BLKROWBIT,
	BLKCOLBIT = BLKCELLCNTBIT - BLKROWBIT,
	BLKCOL = 1 << BLKCOLBIT,
};

}

const RANGE CensorKeyWordHighLightHandler::dstCellInExtraSheet()
{
	BMP_PTR pBmp = m_wwb->GetBMP();
	RANGE rg(pBmp);
	if (INVALIDIDX == m_sheetIdx)
		return rg;
	const int maxBlkRows = (pBmp->cntRows + 1) / BLKROW, maxBlkCols = (pBmp->cntCols + 1) / BLKCOL;
	rg.SetCell(m_sheetIdx, m_sheetRow + BLKROW * m_blkRow, m_sheetCol + BLKCOL * m_blkCol);

	++m_sheetRow;
	if (m_sheetRow >= BLKROW)
	{
		m_sheetRow = 0;
		++m_sheetCol;
	}
	if (m_sheetCol >= BLKCOL)
	{
		m_sheetCol = 0;
		++m_blkRow;
	}
	if (m_blkRow >= maxBlkRows)
	{
		m_blkRow = 0;
		++m_blkCol;
	}
	if (m_blkCol >= maxBlkCols)
	{
		m_blkCol = 0; // 回到左上单元格. 如有需求, 之后这种情况新建一个sheet
	}
	
	return rg;
}

} // namespace wo
