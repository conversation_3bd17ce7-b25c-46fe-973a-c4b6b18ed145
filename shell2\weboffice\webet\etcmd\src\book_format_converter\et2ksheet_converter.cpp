﻿#include "et2ksheet_converter.h"
#include <public_header/etcore/mvc/et_workbook_layer.h>
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include "helpers/protection_helper.h"
#include <public_header/opl/mvc/cellimage/et_cellimage_shape_tree.h>
#include "util.h"
#include <public_header/opl/mvc/et_shape_tree.h>

namespace wo
{

static inline bool isLocalImg(drawing::AbstractShape* pShape)
{
	if (!pShape || !pShape->isPicture())
		return false;
	const drawing::Blip& blip = pShape->picture()->blip();
	IKBlipAtom* pBlipAtom = blip.blipSvg().isValid() ? blip.blipSvg().getSvgBilpAtom() : blip.blipAtom();
	if (!pBlipAtom)
		return false;

	ks_bstr linkPath;
	pBlipAtom->GetLinkPath(&linkPath);
	if (!linkPath.empty())
		return false;

	HGBL hgbl = nullptr;
	pBlipAtom->GetHGlobal(&hgbl);
	return hgbl && XGlobalSize(hgbl) > 0;
}

Et2KsBookConverter::Et2KsBookConverter(_Workbook* pEtWb, binary_wo::VarObj passwordKeys, bool masterProcess) : m_pEtWb(pEtWb), m_passwordKeys(passwordKeys), m_masterProcess(masterProcess) {}

HRESULT Et2KsBookConverter::Init()
{
	if (!m_pEtWb)
		return E_INVALIDARG;
	m_pEtSheets = m_pEtWb->GetWorksheets();
	if (!m_pEtSheets)
		return E_FAIL;
	ks_stdptr<IKMediaManage> spMediaMgr;
	HRESULT hr = oplGetBookMediaMgr(m_pEtWb->GetBook(), &spMediaMgr);
	if (FAILED(hr))
		return hr;
	hr = spMediaMgr->AddAtomExternal(koplBlipUNKNOWN, __X(""), nullptr, &m_spBrokenImg);
	if (FAILED(hr))
		return hr;
	static const QString brokenImg = krt::dirs::resources() + QDir::separator() + "mui" + QDir::separator() + krt::i18n::language() + QDir::separator() + "resource" + QDir::separator() + "wo" + QDir::separator() + "brokenimg.png";
	hr = m_spBrokenImg->SetLinkPath(krt::utf16(brokenImg));
	if (FAILED(hr))
		return hr;
	return S_OK;
}

inline void InitSheetProtectInfo(SHEETPROTECTION& sheetProtection)
{
	ks_memset_s(&sheetProtection, 0, sizeof(SHEETPROTECTION));
	sheetProtection.bProtect = 0;
    sheetProtection.bSelLockedCells = 1;
    sheetProtection.bSelUnlockedCells = 1;
    sheetProtection.bEditObjects = 1;
    sheetProtection.bEditScenarios = 1;
	sheetProtection.wPassword = 0;
}

HRESULT Et2KsBookConverter::_clearProtection() const
{
	HRESULT hr = S_OK;
	for (int i = 0, sheetCnt = m_pEtSheets->GetSheetCount(); i < sheetCnt; ++i)
	{
		IKWorksheet* pEtWs = m_pEtSheets->GetSheetItem(i);
		if (!pEtWs)
			return E_FAIL;
		ISheetProtection* pSheetProtection = pEtWs->GetProtection();

        if (!m_masterProcess)
        {
            for (int j = pSheetProtection->GetUserRangeCount() - 1; j >= 0; --j)
                pSheetProtection->DeleteUserRange(j);
            hr = switchOffProtectedCols(static_cast<_Worksheet*>(pEtWs));
            if (FAILED(hr))
                return hr;

			SHEETPROTECTION resetSP = {0};
			InitSheetProtectInfo(resetSP);	
			pSheetProtection->SetProperty(&resetSP);
			pSheetProtection->SetMaster(__X(""));
			pSheetProtection->SetPasswordUUID(__X(""));
			hr = pEtWs->GetSheet()->SetProtected(FALSE);
			if (FAILED(hr))
				return hr;
        }
        else
        {
			bool bProtect = pSheetProtection->IsProtected();
			if (!bProtect)
			{
                for (int j = pSheetProtection->GetUserRangeCount() - 1; j >= 0; --j)
                    pSheetProtection->DeleteUserRange(j);
                hr = switchOffProtectedCols(static_cast<_Worksheet*>(pEtWs));
                if (FAILED(hr))
                    return hr;

                continue;
            }

            // master进程et升级，手动密码的情况下密码key不匹配不允许转为AS
            if (!pSheetProtection->HasPasswordUUID())
            {
                ks_wstring serverkey = pSheetProtection->GetSecretKey();
                if (!serverkey.empty())
                {
                    int32 arrCount = m_passwordKeys.arrayLength_s();
                    UINT sheetId = pEtWs->GetSheet()->GetStId();
                    bool passwordMatch = false;
                    for (int32 i = 0; i < arrCount; ++i)
                    {
                        binary_wo::VarObj obj = m_passwordKeys.at_s(i);
                        if (sheetId == obj.field_uint32("sheetId"))
                        {
                            WebStr clientKey = obj.field_str("secretKey");
                            if (serverkey == clientKey)
                            {
                                passwordMatch = true;
                            }
                            break;
                        }
                    }
                    if (!passwordMatch)
                        return E_PROTECTION_RANGE_WITH_CUSTOM_PASSEWORD;
                }
            }

            pSheetProtection->SetPassword(__X(""));
            pSheetProtection->SetPasswordUUID(__X(""));
        }
    }
	m_pEtWb->put_Password(nullptr);
	return S_OK;
}

HRESULT Et2KsBookConverter::_handleImgInShapeTree(drawing::AbstractShape* pShape) const
{
	if (!pShape)
		return S_OK;

	std::stack<drawing::AbstractShape*> shapeStack;
	shapeStack.emplace(pShape);
	while (!shapeStack.empty())
	{
		auto pShapeNode = shapeStack.top();
		shapeStack.pop();
		if (pShapeNode->isGroupShape())
		{
			drawing::GroupShape* pShapeGp = static_cast<drawing::GroupShape*>(pShapeNode);
			for (int i = 0, cnt = pShapeGp->childCount(); i < cnt; ++i)
				shapeStack.emplace(pShapeGp->childAt(i));
			continue;
		}
		if (isLocalImg(pShapeNode))
			pShapeNode->setPicID(m_spBrokenImg);
	}
	return S_OK;
}

HRESULT Et2KsBookConverter::_imgHandler() const
{
	// 单元格图片处理
	ks_stdptr<IKDrawingCanvas> spCanvas;
	oplGetCellImgOplData(m_pEtWb->GetBook(), &spCanvas);
	ks_castptr<drawing::ShapeTree> pShapeTree = spCanvas;
	HRESULT hr = _handleImgInShapeTree(pShapeTree);
	if (FAILED(hr))
		return hr;
	// 浮动图片处理
	for (int i = 0, cnt = m_pEtSheets->GetSheetCount(); i < cnt; ++i)
	{
		IKWorksheet* pEtWorksheet = m_pEtSheets->GetSheetItem(i);
		if (!pEtWorksheet)
			continue;
		ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
		oplGetSheetOplData(pEtWorksheet->GetSheet(), &spCanvas);
		if (!spCanvas)
			continue;
		ks_castptr<EtShapeTree> pshapeTree = spCanvas;
		hr = _handleImgInShapeTree(pshapeTree.get());
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

HRESULT Et2KsBookConverter::_processDbSheetInEt() const
{
	HRESULT hr = S_OK;
	for (int i = 0, cnt = m_pEtSheets->GetSheetCount(); i < cnt; ++i)
	{
		ISheet* pSheet = m_pEtSheets->GetSheetItem(i)->GetSheet();
		SHEETTYPE st = stUnknown;
		pSheet->GetFullType(&st);
		if (st == stGrid_DB)
		{
			hr = pSheet->UpdateSheetType(stGrid);
			if (FAILED(hr))
				return hr;
		}
	}
	return hr;
}

HRESULT Et2KsBookConverter::Exec()
{
	HRESULT hr = _clearProtection();
	if (FAILED(hr))
		return hr;

    if (!m_masterProcess)
    {
        hr = _imgHandler();
        if (FAILED(hr))
            return hr;
    }

	util::hanldeEtDashBoardSheet(m_pEtSheets);
	hr = _processDbSheetInEt();
	if (FAILED(hr))
		return hr;
	return S_OK;
}

} // namespace wo
