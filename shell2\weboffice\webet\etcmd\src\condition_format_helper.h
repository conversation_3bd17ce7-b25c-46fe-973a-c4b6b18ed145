﻿#ifndef __CF_APPLIER_HELPER_H__
#define __CF_APPLIER_HELPER_H__

#include "condition_format_wrapper.h"
struct CondFormatItem;
interface ConditionFormatCache;
typedef unsigned short IXF;
const IXF INV_IXF = -1;

namespace binary_wo
{
    class VarObj;
}

enum Wo_FormatConditionType {
	Wo_ContainsValue,
	Wo_ValueRange,
	Wo_RankAverage,
	Wo_TimePeriod,
	Wo_Expression,
};

enum Wo_CFOperator {
	Wo_BlanksCondition,    //单元格为空
    Wo_NoBlanksCondition,  //单元格有内容
    Wo_StringConclude,     //文本包含
    Wo_StringExclude,      //文本不包含
    Wo_StringBeginsWith,   //文本开头
    Wo_StringEndsWith,     //文本结尾
    Wo_UniqueValues,       //唯一值
    Wo_DuplicateValues,    //重复值
    Wo_Errors,             //错误
    Wo_NoErrors,           //无错误

    Wo_Between,            //介于
    Wo_NotBetween,         //未介于
    Wo_Equal,              //等于
    Wo_NotEqual,           //不等于
    Wo_Greater,            //大于
    Wo_Less,               //小于
    Wo_GreaterEqual,       //大于或等于
    Wo_LessEqual,          //小于或等于

    Wo_Top10,              //前10项
    Wo_Top10Per,           //前10%
    Wo_Last10,             //最后10项
    Wo_Last10Per,          //最后10%
    Wo_AboveAverage,       //高于平均值
    Wo_BelowAverage,        //低于平均值

    Wo_Today,              //今天
	Wo_Yesterday,          //昨天
	Wo_Last7Days,          //最近7天
	Wo_ThisWeek,           //本周
	Wo_LastWeek,           //上周
	Wo_LastMonth,          //上月
	Wo_Tomorrow,           //本月
	Wo_NextWeek,           //下周
	Wo_NextMonth,          //下月
	Wo_ThisMonth           //本月
};

CondFormatItem importCFItem(binary_wo::VarObj& obj);
void importColorScale(OUT CondFormatItem& item, IN const binary_wo::VarObj& cfItem);
void importIconSets(OUT CondFormatItem& item, IN const binary_wo::VarObj& cfItem);
void importDataBar(OUT CondFormatItem& item, IN const binary_wo::VarObj& cfItem);
void exportCFRules(const std::vector<CondFormatItem>& vecItems, ISerialAcceptor* acpt, IBook* ptrBook);
void optColor(ISerialAcceptor* acpt, KeyName tag, const EtColor& ec, IBook* bk);
void optFill(ISerialAcceptor* acpt, KeyName tag, const EtFill* p, const KXFMASK& mask, IBook* bk);
void optXf(ISerialAcceptor* acpt, KeyName tag, const XF* pXF, const KXFMASK& mask, IBook* ptrBook);
void optIconSet(ISerialAcceptor* acpt, binary_wo::VarObj, KeyName tag, const EtIconSet* p);
void optDataBar(ISerialAcceptor* acpt, KeyName tag, const EtDataBar* p, IBook* bk);
void optUiCfVo(ISerialAcceptor* acpt, KeyName tag, UiCfVo vo, bool bNeedGte = false);
void optUiCfVo_reverse(OUT UiCfVo& vo, IN const binary_wo::VarObj& cond);
void optUiDataBarNegAxisSetting(ISerialAcceptor* acpt, KeyName tag, UiDataBarNegAxisSetting setting, IBook *bk);
int XlConditionValueTypes_To_DataBarMinMaxIdx(XlConditionValueTypes);
int XlIconSet_To_IconSetsIdx(XlIconSet);
WebStr UiDBDirection_To_String(UiDBDirection);
UiDBDirection String_To_UiDBDirection(WebStr strDir);
WebStr XlIcon_To_String(XlIcon);
XlIcon String_To_XlIcon(WebStr);
WebStr XlIconSet_To_String(XlIconSet);
XlIconSet String_To_XlIconSet(WebStr);
WebStr XlDataBarNegativeColorType_To_String(XlDataBarNegativeColorType);
XlDataBarNegativeColorType String_To_XlDataBarNegativeColorType(WebStr strType);
WebStr XlDataBarAxisPosition_To_String(XlDataBarAxisPosition);
XlDataBarAxisPosition String_To_XlDataBarAxisPosition(WebStr strAp);
HRESULT ApplyConditionalFormat(ISerialAcceptor* acpt, const RANGE& rgApply, IBook* ptrBook);
HRESULT ApplyConditionalFormatByCache(ISerialAcceptor* acpt, const RANGE& rgApply, IKWorkbook* pWorkbook, ConditionFormatCache *pCache);

void optColor(binary_wo::VarObj& obj, KeyName tag, const EtColor& ec, IBook* bk);
void optFill(binary_wo::VarObj & obj, KeyName tag, const EtFill * p, const KXFMASK & mask, IBook * bk);
void optXf(binary_wo::VarObj & obj, KeyName tag, const XF * pXF, const KXFMASK & mask, IBook * ptrBook);

#endif