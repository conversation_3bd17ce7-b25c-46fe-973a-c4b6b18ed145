﻿#ifndef __WO_EVENT_DISPATCHER_H__
#define __WO_EVENT_DISPATCHER_H__

#include <QAbstractEventDispatcher>

namespace wo
{

struct EventDispatcherCallInfo
{
    int processEventsCnt = 0;
    int hasPendingEventsCnt = 0;
    int registerSocketNotifierCnt = 0;
    int unregisterSocketNotifierCnt = 0;
    int registerTimerCnt = 0;
    int unregisterTimerCnt = 0;
    int unregisterTimersCnt = 0;
    int registeredTimersCnt = 0;
    int remainingTimeCnt = 0;
    int wakeUpCnt = 0;
    int interruptCnt = 0;
    int flushCnt = 0;
};

class KWoEventDispatcher : public QAbstractEventDispatcher
{
public:
    KWoEventDispatcher() {};
    ~KWoEventDispatcher() {};

public:
    bool processEvents(QEventLoop::ProcessEventsFlags flags) override;
    bool hasPendingEvents() override;
    void registerSocketNotifier(QSocketNotifier* notifier) override;
    void unregisterSocketNotifier(QSocketNotifier* notifier) override;

    void registerTimer(int timerId, int interval, Qt::TimerType timerType, QObject* object) override;
    bool unregisterTimer(int timerId) override;
    bool unregisterTimers(QObject* object) override;
    QList<TimerInfo> registeredTimers(QObject* object) const override;

    int remainingTime(int timerId) override;

    void wakeUp() override;
    void interrupt() override;
    void flush() override;

    const EventDispatcherCallInfo* getCallInfo() const;

private:
    // QAbstractEventDispatcher* m_impl = nullptr;  // 如果需要，可以在子进程中封装真正的事件分发器
    mutable EventDispatcherCallInfo m_callInfo;  // 通过埋点查看是否有使用到事件分发器
};

}
#endif //__WO_EVENT_DISPATCHER_H__
