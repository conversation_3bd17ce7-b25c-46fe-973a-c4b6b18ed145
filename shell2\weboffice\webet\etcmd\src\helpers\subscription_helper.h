﻿#ifndef __WO_SUBSCRIPTION_HELPER_H__
#define __WO_SUBSCRIPTION_HELPER_H__

#include "wo/core_stake.h"

namespace wo
{

class KEtWorkbook;

namespace Subscription
{

class Utils
{
public:
    Utils(KEtWorkbook *);

    ICbSubscribeOp *GetSubscribeOp();

    WebInt ReSubscribe(bool unSubscribe = true);
    WebInt ReSubscribeWithAsyncGetFile(KEtWorkbook *workbook, const char* userId);
    WebInt ReSubscribeAuthorizedFile(const std::vector<ks_wstring>&);

    INT GetSupBookIndex(PCWSTR fileId);
    void SetAutoUpdatePaused(BOOL);
    BOOL IsAutoUpdatePaused();
    BOOL IsAutoUpdateForbidden();
    bool GetSupBookFullName(PCWSTR fileId, ks_wstring *out);
    PCWSTR GetSupBookFileId(INT nBook);
    INT GetSupBookIdxByFullName(PCWSTR fullName);
    void SetMergeTasksAutoUpdate(BOOL);
private:
    IBook *GetBook();
    IBookSetting *GetBookSetting();
    WebInt UnSubscribeAll();
    void CleanUpCacheWhenUnSubscribe(const std::unordered_set<INT> &books, KEtWorkbook *workbook);
    
    template<typename Iterator>
    void _Subscribe(Iterator, Iterator);
    template<typename Iterator>
    void _UnSubscribe(Iterator, Iterator);

    KEtWorkbook *m_workbook;
};


} // Subscription
} // wo

#endif // __WO_SUBSCRIPTION_HELPER_H__