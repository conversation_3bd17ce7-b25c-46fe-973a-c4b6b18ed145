﻿#include "etstdafx.h"
#include "webchart_config.h"
#include "webbase/wo_sa_helper.h"
#include "appcore/et_appcore_enum.h"
#include "ettools/ettools_encode_decoder.h"
#include "etcore/et_core_dbsheet_enum.h"

KWebChartConfig::KWebChartConfig(PCWSTR configStr)
{
    m_jsonString = krt::fromUtf16(configStr).toStdString();
}

bool KWebChartConfig::SerializeStringValue(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt)
{
    const Json::Value& value = object[keyName];
    if (!value.isString())
        return false;

    QString str = krt::fromUtf8(value.asString().c_str());
    pAcpt->addString(keyName, krt::utf16(str));
    return true;
}

bool KWebChartConfig::SerializeStringArray(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt)
{
    const Json::Value& value = object[keyName];
    if (!value.isArray())
        return false;

    wo::sa::Leave runsLeave = wo::sa::enterArray(pAcpt, keyName);
    for (Json::ArrayIndex i = 0; i < value.size(); ++i)
    {
        const Json::Value& jsonValue = value[i];
        if (jsonValue.isString())
        {
            QString str = krt::fromUtf8(jsonValue.asString().c_str());
            pAcpt->addString(nullptr, krt::utf16(str));
        }
    }
    return true;
}

bool KWebChartConfig::SerializeBoolValue(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt)
{
    const Json::Value& value = object[keyName];
    if (!value.isBool())
        return false;

    bool b = value.asBool();
    pAcpt->addBool(keyName, b);
    return true;
}

bool KWebChartConfig::SerializeUintValue(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt)
{
    const Json::Value& value = object[keyName];
    if (!value.isNumeric())
        return false;

    uint32 number = value.asUInt();
    pAcpt->addUint32(keyName, number);
    return true;
}

bool KWebChartConfig::SerializeDoubleValue(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt)
{
    const Json::Value& value = object[keyName];
    if (!value.isDouble())
        return false;

    double d = value.asDouble();
    pAcpt->addFloat64(keyName, d);
    return true;
}

void KWebChartConfig::SerializeFontStyleValue(const Json::Value& root, ISerialAcceptor* pAcpt)
{
    const Json::Value& fontObject = root["fontStyle"];
    if (fontObject.isObject())
    {
        wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "fontStyle"));
        SerializeUintValue(fontObject, "fontSize", pAcpt);
        SerializeStringValue(fontObject, "color", pAcpt);
        SerializeBoolValue(fontObject, "bold", pAcpt);
        SerializeBoolValue(fontObject, "italic", pAcpt);
    }
}

void KWebChartConfig::SerializeLayout(const Json::Value& object, ISerialAcceptor* pAcpt)
{
    if (object.isObject())
    {
        wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "layout"));
        SerializeDoubleValue(object, "x", pAcpt);
        SerializeDoubleValue(object, "y", pAcpt);
        SerializeDoubleValue(object, "w", pAcpt);
        SerializeDoubleValue(object, "h", pAcpt);
        SerializeDoubleValue(object, "minW", pAcpt);
        SerializeDoubleValue(object, "minH", pAcpt);
    }
}

bool KWebChartConfig::SerializeBaseInfo(ISerialAcceptor* pAcpt, bool bUsedNewKey)
{
    if (m_jsonString.empty())
        return false;

    Json::Value root;
    if (!Json::Reader().parse(m_jsonString, root) || !root.isObject())
        return false;

    bool validName = true;
    if (!bUsedNewKey)
        validName = SerializeStringValue(root, "name", pAcpt);
    bool validType = SerializeStringValue(root, "type", pAcpt);
    return validName && validType;
}

void KWebChartConfig::SerializeConfig(ISerialAcceptor* pAcpt, PCWSTR configLayout)
{
    if (m_jsonString.empty())
        return;

    Json::Value root;
    if (!Json::Reader().parse(m_jsonString, root) || !root.isObject())
        return;

    SerializeStringValue(root, "valueLabel", pAcpt);
    SerializeStringValue(root, "formatType", pAcpt);
    SerializeStringValue(root, "stackType", pAcpt);
    SerializeStringValue(root, "lineEffect", pAcpt);
    SerializeStringValue(root, "pieType", pAcpt);
    SerializeStringValue(root, "blankType", pAcpt);
    SerializeBoolValue(root, "showLabel", pAcpt);
    SerializeBoolValue(root, "showLegend", pAcpt);
    SerializeBoolValue(root, "isBlue", pAcpt);
    SerializeBoolValue(root, "splitLine", pAcpt);
    SerializeStringValue(root, "funnelType", pAcpt);
    SerializeBoolValue(root, "funnelDataSum", pAcpt);

    const Json::Value& colorsObject = root["colors"];
    if (colorsObject.isObject())
    {
        wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "colors"));
        SerializeStringValue(colorsObject, "name", pAcpt);
        SerializeStringArray(colorsObject, "value", pAcpt);
    }

    if ((configLayout && *configLayout != __Xc('\0')))
    {
        Json::Value object;
        std::string layoutStr = krt::fromUtf16(configLayout).toStdString();
        if (Json::Reader().parse(layoutStr, object))
            SerializeLayout(object, pAcpt);
    }
    else
    {
        SerializeLayout(root["layout"], pAcpt);
    }
}

void KWebChartConfig::SerializeConfigXAxis(ISerialAcceptor* pAcpt)
{
    if (m_jsonString.empty())
        return;

    Json::Value root;
    if (!Json::Reader().parse(m_jsonString, root) || !root.isObject())
        return;

    wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "xAxisStyle"));
    SerializeStringValue(root, "ange", pAcpt);
    SerializeFontStyleValue(root, pAcpt);
}

void KWebChartConfig::SerializeConfigYAxis(ISerialAcceptor* pAcpt)
{
    if (m_jsonString.empty())
        return;

    Json::Value root;
    if (!Json::Reader().parse(m_jsonString, root) || !root.isObject())
        return;

    wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "yAxisStyle"));
    const Json::Value& leftYAxisStyle = root["leftYAxis"];
    if (leftYAxisStyle.isObject())
    {
        wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "leftYAxis"));
        SerializeDoubleValue(leftYAxisStyle, "min", pAcpt);
        SerializeDoubleValue(leftYAxisStyle, "max", pAcpt);
        SerializeFontStyleValue(leftYAxisStyle, pAcpt);
    }

    const Json::Value& rightYAxisStyle = root["rightYAxis"];
    if (rightYAxisStyle.isObject())
    {
        wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "rightYAxis"));
        SerializeDoubleValue(rightYAxisStyle, "min", pAcpt);
        SerializeDoubleValue(rightYAxisStyle, "max", pAcpt);
        SerializeFontStyleValue(rightYAxisStyle, pAcpt);
    }
}

void KWebChartConfig::SerializeConfigLegend(ISerialAcceptor* pAcpt)
{
    if (m_jsonString.empty())
        return;

    Json::Value root;
    if (!Json::Reader().parse(m_jsonString, root) || !root.isObject())
        return;

    wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "legendStyle"));
    SerializeStringValue(root, "legendPosition", pAcpt);
    SerializeFontStyleValue(root, pAcpt);
}

void KWebChartConfig::SerializeConfigFunnelLayer(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt)
{
    const Json::Value& value = object[keyName];
    if (value.isObject())
    {
        wo::sa::Leave leave = wo::sa::enterStruct(pAcpt, keyName);
        SerializeBoolValue(value, "show", pAcpt);
        SerializeStringValue(value, "position", pAcpt);
        SerializeStringArray(value, "labelContent", pAcpt);
        SerializeFontStyleValue(value, pAcpt);
    }
}

void KWebChartConfig::SerializeConfigAllLabel(ISerialAcceptor* pAcpt)
{
    if (m_jsonString.empty())
        return;

    Json::Value root;
    if (!Json::Reader().parse(m_jsonString, root) || !root.isObject())
        return;

    wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "allLabelStyle"));
    SerializeStringArray(root, "labelContent", pAcpt);
    SerializeStringArray(root, "pieLabelContent", pAcpt);
    SerializeStringValue(root, "barLabelPosition", pAcpt);
    SerializeStringValue(root, "lineLabelPosition", pAcpt);
    SerializeStringValue(root, "pieLabelPosition", pAcpt);
    SerializeStringValue(root, "horizBarLabelPosition", pAcpt);
    SerializeFontStyleValue(root, pAcpt);
    SerializeConfigFunnelLayer(root, "funnelLayer", pAcpt);
    SerializeConfigFunnelLayer(root, "funnelTransLayer", pAcpt);
    SerializeConfigFunnelLayer(root, "funnelTotalTransRate", pAcpt);
}

void KWebChartConfig::SerializeConfigValueFormat(ISerialAcceptor* pAcpt)
{
    if (m_jsonString.empty())
        return;

    Json::Value root;
    if (!Json::Reader().parse(m_jsonString, root) || !root.isObject())
        return;

    wo::sa::Leave leave(wo::sa::enterStruct(pAcpt, "valueFormatStyle"));
    SerializeStringValue(root, "decimalPlaces", pAcpt);
    SerializeBoolValue(root, "showThousandSeparator", pAcpt);
}

ET_DBSheet_ChartType KWebChartConfig::GetChartType()
{
    ET_DBSheet_ChartType result = DbSheet_Chart_Unknown;
    if (m_jsonString.empty())
        return result;

    Json::Value root;
    if (!Json::Reader().parse(m_jsonString, root) || !root.isObject())
        return result;

    const Json::Value& jsonValue = root["type"];
    if (!jsonValue.isString())
        return result;
    
    QString chartTypeStr = krt::fromUtf8(jsonValue.asString().c_str());
    _appcore_GainEncodeDecoder()->DecodeDbChartType(krt::utf16(chartTypeStr), &result);
    return result;
}