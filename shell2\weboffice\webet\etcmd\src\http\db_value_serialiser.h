﻿#ifndef __WEBET_DB_VALUE_SERIALISER_H__
#define __WEBET_DB_VALUE_SERIALISER_H__

#include "appcore/et_appcore_dbsheet.h"
#include "webbase/binvariant/binvarobj.h"

using binary_wo::VarObj;
namespace wo
{

class KDbFieldValCacheGuard;
class DbSheetFieldRetriever
{
public:
    DbSheetFieldRetriever();

    void Reset(IDbFieldsManager *, bool);
    HRESULT GetField(PCWSTR, IDbField **);
    QString GetFieldName(EtDbId);
    HRESULT GainFieldValUniqueGuard(EtDbId fldId);
private:
    bool m_bPreferId;
    IDbFieldsManager *m_pFieldsManager;
    std::map<ks_wstring, EtDbId> m_fieldNameIdMap;
    std::unordered_map<EtDbId, std::unique_ptr<KDbFieldValCacheGuard>> m_fieldValCacheMap;
};

enum TextValueType
{
    TVT_Original,
    TVT_Text,
    TVT_NumberToText,
    TVT_Compound,
    TVT_FormulaCompound,
};

enum class LinkValueType
{
    ID,
    All,
};

namespace DbSheet {
    struct DbLinkHlp;
}
class DBSheetCommonHelper;
class DbSheetValueSerialiser
{
public:
    DbSheetValueSerialiser(IKWorkbook *pWorkbook, IKWorksheet *pWorksheet, ISerialAcceptor *acpt, const VarObj& param, ks_wstring* pErrorMsg);
    void AssignDbLinkParam(DbReversedLinkParam* pParam) { m_dbLinkParam = pParam; }
    void ClearDbLinkParam() { m_dbLinkParam = nullptr; m_upDbLinkHlp.reset(); }
    void CustomDbSheetView(IDBSheetView* pView) { m_pView = pView; }
    void SetEnableValCacheOpt(bool bEnable) {m_bEnableValCacheOpt = bEnable; };

    HRESULT SerialiseRecord(EtDbId recordId);
    HRESULT SerialiseRecords(const std::vector<EtDbId>& recordIds);
    HRESULT SerialiseAllRecords();
    HRESULT SerialiseField(EtDbId fieldId);
    HRESULT SerialiseFields(const char* keyName); // 细节不对外暴露. 内部控制fields具体是哪些字段
    HRESULT SerialiseView(IDBSheetView *);
    HRESULT SerialiseViewSettings(IDBSheetView *);
    HRESULT SerialiseSheet();
    HRESULT SerialiseAllFieldsBaseSchema();  // 序列化所有字段的基本信息，包括id， 名称，字段类型和权限信息

    HRESULT Set(EtDbId recId, WebName fieldKey, binary_wo::VarObj obj);
    // 同步表场景，根据fieldKey 取不到IDbField，需要用下面方法
    HRESULT Set(EtDbId recId, IDbField* pField, WebName fieldKey, binary_wo::VarObj obj);
    HRESULT SetDBSyncAutoValue(EtDbId recId, IDbField* pField, WebName fieldKey, const binary_wo::VarObj& obj);
    HRESULT ClearUniqueField(EtDbId recId, WebName fieldKey);
    void SerialiseFieldDefaultValue(IDbField*);
    void SerialiseFieldValueUnique(IDbField* pField);
    bool IsPreferId() const; 
    std::unordered_set<EtDbId> GetNoPermissionRecord() const;
    std::unordered_set<EtDbId> GetNoPermissionField() const;
private:
    HRESULT serializeField_impl(IDbField *pField, bool needBaseFieldInfo = true);
    HRESULT serialiseRecord(EtDbId recordId, const std::vector<EtDbId>& fieldIds);
    HRESULT _SetInner(EtDbId recId, IDbField* pField, WebName fieldKey, binary_wo::VarObj obj);
    HRESULT serialiseCell(EtDbId recordId, EtDbId fieldId);
    HRESULT serialiseCellForceText(EtDbId recordId, EtDbId fieldId);
    HRESULT serialiseContent(WebName fieldName, EtDbId, IDbField*, const_token_ptr, bool serializeDirectly = false);
    HRESULT serialiseText(WebName fieldName, PCWSTR text);
    HRESULT serialiseValueStr(WebName fieldName, const_token_ptr pToken, IDbField* pField);
    HRESULT serialiseUrl(WebName fieldName, EtDbId, EtDbId, const_token_ptr pToken);
    HRESULT _SerialiseDate(WebName fieldName, const_token_ptr pToken);
    HRESULT _SerialiseTime(WebName fieldName, const_token_ptr pToken);
    HRESULT _SerialiseDateTime(WebName fieldName, const_token_ptr pToken);
    HRESULT _SerialiseNumber(WebName fieldName, const_token_ptr pToken);
    HRESULT _SerialiseBool(WebName fieldName, const_token_ptr pToken);
    HRESULT serialiseMultipleSelect(WebName fieldName, const_token_ptr pToken, IDbField* pField);
    HRESULT serialiseCellPicture(WebName fieldName, const_token_ptr pToken, IDbField* pField);
    HRESULT _SerialiseAttachment(WebName fieldName, const_token_ptr pToken);
    HRESULT _SerialiseAddress(WebName fieldName, const_token_ptr pToken);
    HRESULT _SerialiseDepartment(WebName fieldName, const_token_ptr pToken);
    HRESULT _SerialiseNote(WebName fieldName, const_token_ptr pToken);
    HRESULT serialiseLink(WebName fieldName, const_token_ptr pToken);
    HRESULT serialiseLookup(WebName, const_token_ptr pToken, IDbField* pField);
    HRESULT serialiseParentRecord(WebName fieldName, const_token_ptr pToken);
    HRESULT serialiseContactTypeFormula(WebName fieldName, const_token_ptr pToken, IDbField* pField);
    HRESULT serialize_lookup(const IDbTokenArrayHandle *pTokenArrayHandle, IDbField *pField, WebName fieldName);
    HRESULT serializeContactItem(PCWSTR id);
    template <typename Getter>
    HRESULT serializeContactArray(const_token_ptr pToken, Getter getter);
    HRESULT serializeContactToken(WebName fieldName, const_token_ptr pToken, IDbField* pField);
    
    // 处理公式字段的序列化. 目前主要针对单元格中出现的dbsheet引用.
    HRESULT serialiseFormulaNumeric(WebName fieldName, const_token_ptr pToken, 
        const std::vector<DWORD>& tokenTypes, std::function<HRESULT(WebName, const_token_ptr)> tokenSerializer);
    HRESULT serialiseFormulaFormattedText(WebName fieldName, const_token_ptr pToken, 
        PCWSTR format, std::function<HRESULT(WebName, const_token_ptr)> tokenSerializer);
    HRESULT serialiseOriginLookup(WebName, const_token_ptr pToken, IDbField* pField);
    HRESULT serialiseUniqueLookup(WebName, const_token_ptr pToken, IDbField* pField);

    HRESULT SerialiseViewGroupSetting(IDBSheetView *);
    HRESULT SerialiseViewSortSetting(IDBSheetView *);
    HRESULT SerialiseViewFilterSetting(IDBSheetView *);
    HRESULT SerialiseViewFilterCriteria(const IDbFilterCriteria *);
    HRESULT SerialiseViewStatisticsSetting(IDBSheetView *);

    HRESULT parseFields(VarObj param);
    HRESULT parseBoolParams(VarObj param);
    HRESULT getSerialiseFieldIds(std::vector<EtDbId>& filedIds);
    bool isNeedSerialiseForLookup(IDbField* pField);
    bool isNeedSerialiseForFormula(IDbField* pField);
    void _serialiseDataKey(ISerialAcceptor* acpt, const_token_ptr pToken);
private:
    ICellImages *m_pCellImages;
    IBook *m_pBook;
    ISheet *m_pSheet;
    ks_stdptr<IDBSheetOp> m_spDbSheetOp;
    ks_stdptr<IDBSheetViews> m_spDbSheetViews;
    IDbFieldsManager *m_pFieldManager;
    std::vector<EtDbId> m_fieldIds;
    ISerialAcceptor *m_acpt;
    IDBSheetCtx *m_pDbCtx;
    IEncodeDecoder* m_pEncodeDecoder;
    ks_stdptr<IETStringTools> m_spStringTools;
    bool m_customFields;
    bool m_bPreferId;
    bool m_bValuePreferId;
    bool m_bIncludeAllRecIds;
    bool m_bServerApiSyncSheetMode;
    TextValueType m_textValue;
    LinkValueType m_linkValue;
    bool m_showPrimary;
    bool m_showRecordExtraInfo;
    bool m_omitFailure;
    DbSheetFieldRetriever m_fieldRetriever;
    DbReversedLinkParam* m_dbLinkParam = nullptr;
    std::unique_ptr<DbSheet::DbLinkHlp> m_upDbLinkHlp;
    IDBSheetView* m_pView = nullptr;
    bool m_bEnableValCacheOpt;
	ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
    wo::KSheet_Book_Version m_bookVersion = wo::KSheet_Book_Version_Base;
    ks_wstring* m_pErrorMsg;
};

HRESULT CreateView(binary_wo::VarObj, bool,  IDBSheetOp *, IDbFieldsManager *, IDBSheetViews *, ET_DBSheet_ViewUseType, IDBSheetView **, ks_wstring*);
HRESULT CreateField(binary_wo::VarObj, bool, IDBSheetOp *, IDbField **, ks_wstring*);
HRESULT UpdateField(binary_wo::VarObj, bool, IDBSheetOp *, IDbField *, ks_wstring*);
HRESULT SetPrimaryField(PCWSTR, bool, IDBSheetOp *, ks_wstring*);
HRESULT UpdateView(binary_wo::VarObj, bool, IDBSheetView *);
HRESULT UpdateViewSetting(binary_wo::VarObj, bool, IDBSheetView *, ks_wstring*);
HRESULT RemoveViewSetting(binary_wo::VarObj, bool, IDBSheetView *);
void SerialiseExtraFieldInfo(IDbField*, ISerialAcceptor*, IDBSheetCtx*, IBook*);
void SerialiseSheetInfo(IKWorkbook*, IKWorksheet*, ISerialAcceptor*, ks_wstring*, const VarObj* = nullptr);
HRESULT SerialiseWebExtension(UINT, IKWebExtension*, ISerialAcceptor*, DBSheetCommonHelper*);
void SerialiseChartStatisticModule(IDBChartStatisticModule*, ISerialAcceptor*, DBSheetCommonHelper*);
void SerialisePluginConfig(IDBChartStatisticModule*, IKWebExtension*, ISerialAcceptor*, DBSheetCommonHelper*);
void SerialiseFilterCriteriaValue(IDbFcValueBase*, ISerialAcceptor*);
void SerialiseDataConditionDataRange(IDBChartStatisticModule*, ISerialAcceptor*, DBSheetCommonHelper*);
void SerialiseDataConditionGroups(IDBChartStatisticModule*, ISerialAcceptor*, DBSheetCommonHelper*);
void SerialiseDataConditionSeries(IDBChartStatisticModule*, ISerialAcceptor*, DBSheetCommonHelper*);
void resetLookupFieldRetriever(IDBSheetOp*, IDbField*, bool, DbSheetFieldRetriever&);
void resetLookupFieldRetrieverBySid(IDBSheetOp*, UINT, bool, DbSheetFieldRetriever&);
} // namespace wo

#endif // __WEBET_DB_VALUE_SERIALISER_H__
