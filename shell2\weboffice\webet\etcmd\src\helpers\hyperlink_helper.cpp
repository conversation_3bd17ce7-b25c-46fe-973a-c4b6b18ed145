﻿#include "etstdafx.h"
#include "hyperlink_helper.h" 
#include <persist/persist_helper.h>
#include "et_revision_context_impl.h"
#include <applogic/etapi_old.h>
#include "database/database.h"
#include "database/database_field_context.h"
#include "mvc/et_mvc.h"
#include <public_header/drawing/model/abstract_shape.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include "tools/et_combstr.h"
#include "kso/api/smartparam.h"
#include "kso/l10n/et/et_styles.h"


namespace wo {



HRESULT AutoFixToHyperlinkHelper::AutoFix(binary_wo::Var<PERSON>bj param, BSTR bstrCellValue, Range *pCellRg, bool &bURL, IEtRevisionContext *ctx)
{
  ks_bstr bstrAddress;
  bURL = CheckAutoFixHypeLink(bstrCellValue, &bstrAddress);
  if (bURL) 
  {
    if (param.getVar() != nullptr) 
    {
      param.add_field_bool("isUrl", true);
      param.add_field_str("address", bstrAddress.c_str());
      ctx->setIsRealTransform(true);
    }

    BOOL bCover = FALSE;
    ks_stdptr<Hyperlink> ptrHyperlink;
    ks_stdptr<Range> ptrRg = pCellRg;
    ptrRg->get_Hyperlink(&ptrHyperlink);
    if (ptrHyperlink)
    {
        ks_stdptr<Range> ptrRange;
        ptrHyperlink->get_Range(&ptrRange);
        bCover = IsSameRange(ptrRange, ptrRg);
    }

    if (bCover && ptrHyperlink)
    {
        ptrHyperlink->put_Address(bstrAddress);
        ptrHyperlink->put_SubAddress(0);
        ptrHyperlink->put_ScreenTip(bstrAddress);
        ptrHyperlink->put_TextToDisplay(bstrCellValue);
    }
    else
    {
		ks_stdptr<IRangeInfo> ptrRangeInfo = pCellRg;
		ks_stdptr<IKWorksheet> ptrWorksheetInfo;
		ptrRangeInfo->GetWorksheetInfo(&ptrWorksheetInfo);
		KCOMPTR(_Worksheet) ptrWorkSheet = ptrWorksheetInfo;

        KComVariant       varDispText(bstrCellValue);
        AddHyperlink(ptrWorkSheet,ptrRg, bstrAddress, KComVariant(),
                      KComVariant(), varDispText);
    }
  }

  return S_OK;
}

HRESULT AutoFixToHyperlinkHelper::AddHyperlink(_Worksheet* pWorksheet,IKCoreObject*  pRgOrShape,
                                 BSTR           bstrAddress,
                                 VARIANT        varSubAddress,
                                 VARIANT        varScreenTip,
                                 VARIANT        varDispText)
{

	if (!pWorksheet || !pRgOrShape)
    return E_FAIL;

	ks_stdptr<IKHyperlinks>	spHyplinks;
	VS(pWorksheet->GetSheet()->GetExtDataItem(edSheetHyperlinks, (IUnknown**)&spHyplinks));
	if (!spHyplinks)
    return E_FAIL;

	ks_stdptr<Range> spAnchorRange;
	ks_stdptr<Shape> spShape;

	HRESULT hr = pRgOrShape->QueryInterface(IID_Range, (void**)&spAnchorRange);
	if (FAILED(hr))
	{
		hr = pRgOrShape->QueryInterface(IID_Shape, (void**)&spShape);
		if (FAILED(hr))
			return E_FAIL;
	}

	IKWorkbook* pWorkbook = pWorksheet->GetWorkbook();
	ISheet* pSheet = pWorksheet->GetSheet();
	ASSERT(pSheet);
	KCOMPTR(IUnknown) spUnk;
	pSheet->GetExtDataItem(edSheetProtection, &spUnk);
	ASSERT(spUnk);
	KCOMPTR(ISheetProtection) spSP;
	spUnk->QI(ISheetProtection, &spSP);
	ASSERT(spSP);

	SHEETPROTECTION sp;
	spSP->GetProperty(&sp);
	if (sp.bProtect && spAnchorRange)
	{
		VARIANT_BOOL varAllowEdit;
		spAnchorRange->get_AllowEdit(&varAllowEdit);
		if (!varAllowEdit)
			return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;
	}
	else if (sp.bProtect && spShape && !sp.bEditObjects && !spSP->IsMaster())
	{
		return API_E_CUSTOM_ERROR;
	}

	ks_stdptr<IKHyperlink> spHL;
	KComBSTR bsSubAddress = NULL;
	const RANGE* pAnchorRg = NULL;
	range_helper::ranges rgs;
	if (spAnchorRange != NULL)
	{
		app_helper::GetIRanges(spAnchorRange, &rgs);
		if (rgs.size() != 1)
			return E_FAIL;
		pAnchorRg = rgs.at(0).second;
		hr = spHyplinks->New(&spHL, pAnchorRg);
		KS_CHECK(hr);
	}
	else if (spShape != NULL)
	{
		INT nSpID = 0;
		spShape->get_Id(&nSpID);
		hr = spHyplinks->New(&spHL, nSpID);
	}

	{
		// 获得参数
		KComBSTR bsScreenTip = NULL;
		KSmartParam ParamSubAddress(varSubAddress);
		if (ParamSubAddress.IsStringType())
		{ ParamSubAddress.GetStringValue(&bsSubAddress); }

		KSmartParam ParamScreenTip(varScreenTip);
		if (ParamScreenTip.IsStringType())
		{ ParamScreenTip.GetStringValue(&bsScreenTip); }
		
		// 数据
		spHL->SetAddress(bstrAddress);
		if (bsSubAddress.Length() != 0)
			spHL->SetSubAddress(bsSubAddress);
		if (bsScreenTip.Length() != 0)
			spHL->SetScreenTip(bsScreenTip);
	}
	

	// Text To Display
	if (spAnchorRange != NULL)
	{
		KSmartParam Param(varDispText);
		if (V_VT(&varDispText) == VT_ERROR || Param.IsStringType())
		{
			//bug 59250 当TextToDisplay缺省时，可能需要显示Address的内容，
			//所以要加上V_VT(&TextToDisplay) == VT_ERROR这个条件
			//还没有考虑TextToDisplay传进来的是非String的情况，Excel考虑了
			CELL c;
			c.row = pAnchorRg->RowFrom();
			c.col = pAnchorRg->ColFrom();

			ks_stdptr<Range> spApiRange;
			IDX Index;
			pWorksheet->GetSheet()->GetIndex(&Index);
			RANGE rg(pWorksheet->GetSheet()->GetBMP());
			rg.SetCell(Index, c.row, c.col);
			VS(pWorksheet->GetRangeByData(&rg, &spApiRange));
			// 在显示文字可以编辑的时候，如果显示的文字为空的时候应该写链接的地址
			// 当Formual为Text和None时可编辑
			et_applogic::FormulaType ft = et_applogic::ft_Unknown;			
			KCOMPTR(IRangeInfo) ptrRangeInfo;
			ptrRangeInfo = spAnchorRange;
			hr = ptrRangeInfo->GetFormulaType(&ft);
			KS_CHECK(hr);
			
			// web端应产品需求, 不管单元格里面内容是什么, 只要传了DisplayTest都要设进去
#if !WEB_OFFICE_ENABLE
			if (et_applogic::ft_None == ft
				|| (et_applogic::ft_Text == ft && Param.IsStringType())
				|| pWorkbook->CoopCurStatus() == DocCoopNormal)
#endif
			{
				KComBSTR bsDisplayTest;
				if (Param.IsStringType())
					bsDisplayTest = Param.GetStringValue();
				if (0 == bsDisplayTest.Length())
					bsDisplayTest = bstrAddress;
				if (0 == bsDisplayTest.Length())
					bsDisplayTest = bsSubAddress;

				KComBSTR bstrVal;
				VS( spApiRange->get_Formula(&bstrVal) );
				if (bsDisplayTest.Length() != 0 
					&& (bstrVal == NULL || xstrcmp(bsDisplayTest, bstrVal) != 0))
				{
					ks_castptr<IKEtApplication> etApp = pWorksheet->GetApplication();
					switch(etApp->GetAppSettings()->GetReferenceStyle()) 
					{
					case RS_R1C1:
							spApiRange->put_FormulaR1C1(bsDisplayTest);
						break;
					case RS_A1:
							spApiRange->put_Formula(bsDisplayTest);
						break;
					default:
						ASSERT(!"KHyperlinks::Add, put_Formula!!");
						break;
					}						
				}
			}
		}
		else
			return E_INVALIDARG;
	}

	
	if (spAnchorRange != NULL)
	{
		// 超级链接样式
		// bug: 45040
		KComVariant VarStyleName = STR_STYLE_HYPERLINK;
		ISheetProtection *pProtection = pWorksheet->GetProtection();
		SHEETPROTECTION prot;
		pProtection->GetProperty(&prot);
		int bProtect = prot.bProtect;
		prot.bProtect = 0;
		pProtection->SetProperty(&prot);

		spAnchorRange->put_Style(VarStyleName);

		prot.bProtect = bProtect;
		pProtection->SetProperty(&prot);

		// 自动更新
		app_helper::KPaneUpdateParam paramUpdate;
		paramUpdate.UpdateFlag = ERM_CellStyleChanged;
		paramUpdate.bScrollBarHorz = paramUpdate.bScrollBarVert = TRUE;
		app_helper::KSmartBatchUpdate<_Worksheet> sbuWb(pWorksheet, paramUpdate);
	}

KS_EXIT:
	return hr;
}



HRESULT AutoFixToHyperlinkHelper::EnsureDbHyperlinkFields(Database::FieldContext* pContext,
                                            BSTR   bstrCellValue,
                                            Range* pRange)
{
    class EnsureHyperlink : public Database::IFieldEnum {
      public:
        EnsureHyperlink(BSTR formula,_Worksheet * pworkSheet)
            : m_formula(formula),m_pworkSheet(pworkSheet)
        {
        }

        virtual HRESULT Do(Database::FieldContext* pContext,
                           Database::IDbField*,
                           const RANGE& rg,
                           const VALIDATION&) override
        {
            ks_stdptr<Range>        spRange = pContext->CreateRangeObj(rg);
            KComVariant             varDispText(m_formula);
            return AddHyperlink(m_pworkSheet,spRange, m_formula,KComVariant(), KComVariant(), varDispText);
        }

      private:
        BSTR        m_formula;
		_Worksheet * m_pworkSheet;
    };

	range_helper::ranges rgs;
	ks_stdptr<IRangeInfo> spRangeInfo = pRange;
	spRangeInfo->GetIRanges(&rgs, FALSE);
	if (rgs.size() != 1)
		return E_INVALIDARG;
	const RANGE *pRg = rgs[0].second;

	ks_stdptr<IKWorksheets> spWorksheets = pContext->GetWorksheets();
	ks_stdptr<_Worksheet> spWorkSheet = spWorksheets->GetSheetItem(pRg->SheetFrom());
    EnsureHyperlink          ensureEnum(bstrCellValue,spWorkSheet);
    Database::FieldsManager* pDbMgr = Database::FieldsManager::Instance();

    return pDbMgr->EnumFieldsInRANGE(pContext, *pRg, Database::dftHyperlink, Database::dbEnumType, &ensureEnum);
}

BOOL AutoFixToHyperlinkHelper::IsSameRange(Range *pRg1, Range *pRg2)
{
    BOOL rt = FALSE;
    if (pRg1 && pRg2)
    {
        KComVariant Empty;
        ks_bstr     address1;
        ks_bstr     address2;

        pRg1->get_Address(VARIANT_TRUE, VARIANT_TRUE, etA1, VARIANT_TRUE, Empty,
                          &address1);
        pRg2->get_Address(VARIANT_TRUE, VARIANT_TRUE, etA1, VARIANT_TRUE, Empty,
                          &address2);

        if (address1.size() == address2.size() && !xstrcmp(address1, address2))
        {
            rt = TRUE;
        }
    }

    return rt;
}


}
