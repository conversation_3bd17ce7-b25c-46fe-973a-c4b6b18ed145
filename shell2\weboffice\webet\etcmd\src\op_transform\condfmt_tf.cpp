﻿//#include "etstdafx.h"
#include "condfmt_tf.h"
namespace wo
{
	
	bool CondFmtTrans::TransByAdd(int iTrs, int& iRes)
	{
		if(iTrs < 0)
			return false;
		iRes = iTrs + 1;
		return true;
	}

	bool CondFmtTrans::TransByDel(int iDel, int iTrs, int& iRes)
	{
		if((iDel < 0) || (iTrs < 0) || (iDel == iTrs))
			return false;

		iRes = (iDel > iTrs)? iTrs: iTrs - 1;
		return true;
	}

	bool CondFmtTrans::TransBySwap(const CfSwapPara& para, int iTrs, int& iRes)
	{
		if(!para.isValid() || (iTrs < 0))
			return false;

		if(para.m_iMove < para.m_iBelow)
		{
			//down
			if((iTrs > para.m_iMove) && (iTrs < para.m_iBelow))
				iRes = iTrs - 1;
			else if(iTrs == para.m_iMove)
				iRes = para.m_iBelow - 1;
			else
				iRes = iTrs;
		}
		else if(para.m_iMove > para.m_iBelow)
		{
			//up
			if((iTrs >= para.m_iBelow) && (iTrs < para.m_iMove))
				iRes = iTrs + 1;
			else if (iTrs == para.m_iMove)
				iRes = para.m_iBelow;
			else
				iRes = iTrs;
		}

		return true;
	}

	bool CondFmtTrans::SwapByAdd(const CfSwapPara& paraTrs, CfSwapPara& paraRes)
	{
		if(!paraTrs.isValid())
			return false;

		paraRes.m_iBelow = paraTrs.m_iBelow + 1;
		paraRes.m_iMove = paraTrs.m_iMove + 1;
		return true;
	}

	bool CondFmtTrans::SwapByDel(int iDel, const CfSwapPara& paraTrs, CfSwapPara& paraRes)
	{
		if((iDel < 0) || !paraTrs.isValid())
			return false;

		TransByDel(iDel, paraTrs.m_iBelow, paraRes.m_iBelow);
		TransByDel(iDel, paraTrs.m_iMove, paraRes.m_iMove);
		return true;
	}

	bool CondFmtTrans::SwapBySwap(const CfSwapPara& para, const CfSwapPara& paraTrs, CfSwapPara& paraRes)
	{
		if(!para.isValid() || !paraTrs.isValid())
			return false;

		TransBySwap(para, paraTrs.m_iBelow, paraRes.m_iBelow);
		TransBySwap(para, paraTrs.m_iMove, paraRes.m_iMove);
		return true;
	}

}