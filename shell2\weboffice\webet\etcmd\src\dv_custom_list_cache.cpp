﻿#include "dv_custom_list_cache.h"
#include "appcore/et_appcore_validation.h"
#include "woetsetting.h"
#include "util.h"

namespace wo 
{

struct EtValidationGuard
{
	explicit EtValidationGuard(VALIDATION* pDv) : m_pDv(pDv)
	{}

	~EtValidationGuard()
	{
		if (m_pDv)
		{
			if (m_pDv->bsFormula1)
			{
				::SysFreeString(m_pDv->bsFormula1);
				m_pDv->bsFormula1 = NULL;
			}
			if (m_pDv->bsFormula2)
			{
				::SysFreeString(m_pDv->bsFormula2);
				m_pDv->bsFormula2 = NULL;
			}
		}
	}
private:
	VALIDATION* m_pDv;
};

KDvCustomListCache::KDvCustomListCache(etoldapi::_Workbook* wb)
    : m_ptrWorkbook(wb)
{
    m_pBook = ks_castptr<IKWorkbook>(m_ptrWorkbook)->GetBook();
	_etcore_CreateObject(CLSID_KRanges, IID_IKRanges, (void**)&m_spDirtyRanges);
    _etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools);
	m_pBook->GetDvCustomListNotify()->AddScheme(this);
}

KDvCustomListCache::~KDvCustomListCache()
{
	removeAllCache();
	m_pBook->GetDvCustomListNotify()->RemoveScheme(this);
	m_spDirtyRanges.clear();
}

UINT KDvCustomListCache::getCacheMaxSizePerSheet()
{
	IWoETSettings *pWoSettings = _kso_GetWoEtSettings();
	if (pWoSettings)
		return pWoSettings->GetCustomListCacheMaxSizePerSheet();
	return DEFAULT_CUSTOM_LIST_CACHE_MAX_SIZE_PER_SHEET;
}

bool KDvCustomListCache::findCache(IDX sheetIdx, ROW row, COL col, CellResult& ret)
{
    wo::IWorksheetObj* pWsObj = getSheetMain(sheetIdx);
    if (pWsObj == NULL)
        return false;
    WebID objSheetId = pWsObj->objId();
    auto it = m_cache.find(objSheetId);
    if (it != m_cache.end())
    {
		ks_stdptr<ISheet> spSheet;
		m_pBook->GetSheet(sheetIdx, &spSheet);
		m_spStringTools->SetEnv(spSheet);
		m_spStringTools->SetOption(ETStringToolsOpt_None);

        CellResultVector& crv = it->second;
		for (auto it2 = crv.begin(); it2 != crv.end(); ++it2)
		{
			CellResult cellResult = *it2;
			if (cellResult.cell.row == row && cellResult.cell.col == col)
			{
				ks_bstr bstrCellText;
				getCellText(spSheet, sheetIdx, row, col, &bstrCellText, cellResult.bMulitiCompare);
				if (!bstrCellText.empty() && xstrcmp(bstrCellText, alg::msrGetStringResourceValue(cellResult.hd)) == 0)
				{
					ret = cellResult;
					return true;
				}
				// 内容发生变化，缓存失效
				alg::msrUnreferStringResource(cellResult.hd);
				crv.erase(it2);	
				return false;
			}
		}
    }
    return false;
}

void KDvCustomListCache::removeCache(const RANGE& rg)
{
	if (!rg.IsValid())
		return;

	class EnumAcpt : public ICellAcpt
	{
	public:
		EnumAcpt(KDvCustomListCache* pHost, IDX iSht) : m_pHost(pHost), m_iSht(iSht) {}
		STDPROC_(INT) Do(ROW row, COL col)
		{
			m_pHost->removeCache(m_iSht, row, col);
			return 0;
		}

	protected:
		KDvCustomListCache *m_pHost;
		IDX m_iSht;
	};

	for (IDX iSht = rg.SheetFrom(); iSht <= rg.SheetTo(); ++iSht)
	{
		ks_stdptr<ISheet> spSheet;
		m_pBook->GetSheet(iSht, &spSheet);
		et_sdptr<ISheetEnum> spSheetEnum;
		spSheet->CreateEnum(&spSheetEnum);
		EnumAcpt acpt(this, iSht);
		spSheetEnum->EnumCell(rg, &acpt);
	}
}

bool KDvCustomListCache::removeCache(IDX sheetIdx, ROW row, COL col)
{
    wo::IWorksheetObj* pWsObj = getSheetMain(sheetIdx);
    if (pWsObj == NULL)
        return false;
    WebID objSheetId = pWsObj->objId();

    auto it = m_cache.find(objSheetId);
    if (it != m_cache.end())
    {
        CellResultVector& crv = it->second;
		for (auto it2 = crv.begin(); it2 != crv.end(); ++it2)
		{
			CellResult ret = *it2;
			if (ret.cell.row == row && ret.cell.col == col)
			{
				alg::msrUnreferStringResource(ret.hd);
				crv.erase(it2);
				return true;
			}
		}
    }
    return false;
}

void KDvCustomListCache::removeAllCache()
{
	for (auto it = m_cache.begin(); it != m_cache.end(); ++it)
	{
		CellResultVector& crv = it->second;
		for (auto it2 = crv.begin(); it2 != crv.end(); ++it2)
		{
			CellResult cr = *it2;
			alg::msrUnreferStringResource(cr.hd);
		}
		crv.clear();
	}
	m_cache.clear();
}

void KDvCustomListCache::saveCache(IDX sheetIdx, CellResult& ret)
{
    wo::IWorksheetObj* pWsObj = getSheetMain(sheetIdx);
    if (pWsObj == NULL)
        return;
    WebID objSheetId = pWsObj->objId();

	UINT cacheMaxSize = getCacheMaxSizePerSheet();
    auto it = m_cache.find(objSheetId);
    if (it != m_cache.end())
    {
        CellResultVector& crv = it->second;
		for (auto it2 = crv.begin(); it2 != crv.end(); it2++)
		{
			CellResult cr = *it2;
			if (cr.cell.row == ret.cell.row && cr.cell.col == ret.cell.col)
			{
				alg::msrUnreferStringResource(cr.hd);
				crv.erase(it2);
				break;
			}
		}
		if (cacheMaxSize > 0)
		{
			if (crv.size() == cacheMaxSize)
			{
				auto it3 = crv.begin();
				CellResult cr = *it3;
				alg::msrUnreferStringResource(cr.hd);
				crv.erase(it3);
			}
				
			crv.push_back(ret);
		}
    }
    else if (cacheMaxSize > 0)
    {
        CellResultVector crv;
		crv.push_back(ret);
        m_cache[objSheetId] = std::move(crv);
    }
}

wo::IWorksheetObj* KDvCustomListCache::getSheetMain(IDX sheetIdx)
{
    wo::IWorkbookObj* wbo = m_ptrWorkbook->GetWoObject();
    if (0 <= sheetIdx && sheetIdx < wbo->getSheetCount())
		return wbo->getSheetItem(sheetIdx);
	else
		return nullptr;
}

HRESULT KDvCustomListCache::CalculateAndSerializeCellResults(ISerialAcceptor* acpt, IDX sheetIdx, std::vector<CELL>& vecCells)
{
	struct _CellCompare
	{
		bool operator()(const CELL& a, const CELL& b) const
      	{
			if (a.row < b.row)
				return true;
			if (a.row > b.row)
				return false;
			return a.col < b.col;
		}
	};

	struct _BSTRHASH
	{
		size_t operator()(BSTR pwcsz) const
		{
			return alg::HashWString(pwcsz);
		}
	};

	struct _BSTREQUAL
	{
		bool operator()(BSTR lhs, BSTR rhs) const
		{
			return 0 == xstrcmp(lhs, rhs);
		}
	};

	// 检查是否多选
	auto checkIsMultiSupport = [&](IBook* pBook, PCWSTR pcwExtId) -> bool
	{
		if (pBook == NULL || pcwExtId == NULL || *pcwExtId == NULL)
			return false;
		ks_stdptr<IValidationExts> spValidationExts;
		pBook->GetExtDataItem(edBookDataValidtionExts, (IUnknown **)&spValidationExts);
		// 只收集下拉列表有效性扩展
		ks_stdptr<IValidationExt> spValidationExt = spValidationExts->GetItem(pcwExtId);
		if (spValidationExt == NULL || spValidationExt->GetType() != ValidationExtType::List)
			return false;
		// 是否按逗号分割
		ks_stdptr<IValidationListExt> spValidationExtList;
		spValidationExt->QueryInterface(IID_IValidationListExt, (void **)&spValidationExtList);
		if (spValidationExtList != NULL)
			return spValidationExtList->GetMultiSupport();
		return false;
	};

	using CELLS = std::set<CELL, _CellCompare>;
	struct DVCells
	{
		CELLS 	singleSelCells;	// 单选单元格集合	用 token 比较
		CELLS 	multipleSelCells; // 多选单元格集合		用 celltext 比较
	};
	using CatDVCells = std::unordered_map<BSTR, DVCells, _BSTRHASH, _BSTREQUAL>;	// 相同引用数据分类 取数据列表
	CatDVCells catCells;

	{
		std::etvector<BSTR> tempVecBstr;
		util::EtVectorBSTRGuard tempVecBstrAutoRelease(tempVecBstr);
		for (int i = 0; i < vecCells.size(); ++i)
		{
			CELL &cell = vecCells.at(i);
			CellResult ret;
			if (findCache(sheetIdx, cell.row, cell.col, ret))
			{
				serialCellResult(acpt, sheetIdx, ret);
				continue;
			}

			RANGE rg(m_pBook->GetBMP());
			rg.SetCell(sheetIdx, cell.row, cell.col);
			VALIDATION dv;
			ks_memset_s(&dv, 0, sizeof(VALIDATION));
			m_pBook->LeakOperator()->GetDataValidation(rg, sheetIdx, cell.row, cell.col, &dv, NULL, NULL, NULL);
			EtValidationGuard dvRelease(&dv);
			BSTR bstr = dv.bsFormula1;
			dv.bsFormula1 = NULL;
			tempVecBstr.push_back(bstr);
			PCWSTR pcwExtId = dv.pcwcsExtId;
			bool bMulitSupport = checkIsMultiSupport(m_pBook, pcwExtId);

			if (bstr)
			{
				auto it = catCells.find(bstr);
				if (it != catCells.end())
				{
					DVCells &dvCells = it->second;
					if (bMulitSupport)
						dvCells.multipleSelCells.insert(cell);
					else
						dvCells.singleSelCells.insert(cell);
				}
				else
				{
					DVCells dvCells;
					if (bMulitSupport)
						dvCells.multipleSelCells.insert(cell);
					else
						dvCells.singleSelCells.insert(cell);
					catCells[bstr] = std::move(dvCells);
				}
			}
		}
	}

	if (catCells.empty())
		return S_OK;

	class EnumDvCellsOp
	{
	public:
		EnumDvCellsOp(KDvCustomListCache* pHost, ISheet* pSht, IDX iSht, CELLS cells, bool bMultiCompare, ISerialAcceptor* acpt)
			: m_pHost(pHost)
			, m_pSht(pSht)
			, m_iSht(iSht)
			, m_cells(cells)
			, m_bMultiCompare(bMultiCompare)
			, m_serialAcpt(acpt)
		{}

		void operator()() 
		{
			if (m_cells.empty())
				return;
			CELL firstCell = *(m_cells.begin());
			ks_stdptr<ICoreValidation> spCoreValidation;
			if (FAILED(m_pHost->getValidation(m_iSht, firstCell.row, firstCell.col, &spCoreValidation)))
				return;
			// 获取引用区域文本集合
			std::etvector<BSTR> vecValue;
			util::EtVectorBSTRGuard vecValueAutoRelease(vecValue);
			if (m_bMultiCompare)
				VS(spCoreValidation->GetCustomList(ETStringToolsOpt_None, &vecValue, FALSE));
			else
				VS(spCoreValidation->GetCustomListValues(ETStringToolsOpt_None, &vecValue, FALSE));

			ks_bstr bstrCellText;
			for (auto it3 = m_cells.begin(); it3 != m_cells.end(); ++it3)
			{
				bstrCellText.clear();
				CELL cell = *it3;
				m_pHost->getCellText(m_pSht, m_iSht, cell.row, cell.col, &bstrCellText, m_bMultiCompare);

				if (bstrCellText.empty())
					continue;

				// 比较+序列化+缓存
				KDvCustomListCache::CellResult cr;
				cr.cell = {cell.row, cell.col};
				cr.bMulitiCompare = m_bMultiCompare;
				cr.hd = alg::msrIdentifyStringResource(bstrCellText);
				m_pHost->calculateCellResult(bstrCellText, vecValue, m_bMultiCompare, cr.result);
				m_pHost->serialCellResult(m_serialAcpt, m_iSht, cr);
				m_pHost->saveCache(m_iSht, cr);
			}
		}
		
	private:
		KDvCustomListCache* m_pHost;
		ISheet* m_pSht;
		IETStringTools* m_pStrTools;
		IDX m_iSht;
		CELLS m_cells;
		bool m_bMultiCompare;
		ISerialAcceptor* m_serialAcpt;
	};

	ks_stdptr<ISheet> spSheet;
	m_pBook->GetSheet(sheetIdx, &spSheet);
	m_spStringTools->SetEnv(spSheet);
	m_spStringTools->SetOption(ETStringToolsOpt_None);

	for (auto it = catCells.begin(); it != catCells.end(); ++it)
	{
		DVCells& dvCells = it->second;
		EnumDvCellsOp(this, spSheet, sheetIdx, dvCells.singleSelCells, false, acpt)();
		EnumDvCellsOp(this, spSheet, sheetIdx, dvCells.multipleSelCells, true, acpt)();
	}
	return S_OK;
}

HRESULT KDvCustomListCache::getValidation(IDX sheetIdx, ROW row, COL col, ICoreValidation **ppv)
{
	ks_stdptr<ICoreValidation> spCoreValidation;
	if (FAILED(_appcore_CreateObject(CLSID_KCoreValidation, IID_ICoreValidation, (void**)&spCoreValidation)))
		return E_FAIL;

	CELL cell;
	cell.row = row;
	cell.col = col;

	RANGE rg(m_pBook->GetBMP());
	rg.SetCell(sheetIdx, cell.row, cell.col);

	range_helper::ranges rgs = rgs.create_instance();
	rgs.add(etexec::STREF_THIS_BOOK, rg);

	ks_stdptr<IWorkspace> spWorkspace;
	m_pBook->GetWorkspace(&spWorkspace);
	if (!spWorkspace)
		return E_FAIL;

	ks_stdptr<IAppSettings> spAppSettings;
	spWorkspace->GetAppSettings(&spAppSettings);
	if (!spAppSettings)
		return E_FAIL;

	REF_STYLE refStyle = spAppSettings->GetReferenceStyle();
	spCoreValidation->Init(m_pBook->LeakOperator(), cell, rgs, rg, sheetIdx, refStyle);

	(*ppv) = spCoreValidation.detach();
	return S_OK;
}

void KDvCustomListCache::calculateCellResult(BSTR text, std::etvector<BSTR>& vecValue, bool bMultiCompare, std::vector<INT>& ret)
{
	ret.clear();
	for (int i = 0, cur = 0; text[i] != 0; ++i)
	{
		WCHAR next = text[i + 1];
		if (next == 0 || (bMultiCompare && next == __Xc(',')))
		{
			int index = -1;
			if (i >= cur)
			{
				for (int j = 0; j < vecValue.size(); ++j)
				{
					BSTR &bstrText = vecValue.at(j);
					if (bstrText != NULL)
					{
						int len = i + 1 - cur;
						if (SysStringLen(bstrText) == len
							&& xstrncmp(bstrText, text + cur, len) == 0)
						{
							index = j;
							break;
						}
					}
				}
			}
			ret.push_back(index);
			cur = i + 2;
		}
	}
}

void KDvCustomListCache::serialCellResult(ISerialAcceptor* acpt, IDX sheetIdx, CellResult& cr)
{
    if (acpt == NULL)
        return;

    wo::IWorksheetObj* pWsObj = getSheetMain(sheetIdx);
    if (pWsObj == NULL)
        return;
    WebID objSheetId = pWsObj->objId();

    acpt->beginStruct();
    acpt->addUint32("objSheet", objSheetId);
    acpt->addUint32("sheetIdx", sheetIdx);
    acpt->addUint32("row", cr.cell.row);
    acpt->addUint32("col", cr.cell.col);
    acpt->addKey("indexs");
    acpt->beginArray();
    for (int i = 0; i < cr.result.size(); ++i)
    {
        acpt->addInt32(NULL, cr.result[i]);
    }
    acpt->endArray();
    acpt->endStruct();
}

void KDvCustomListCache::getCellText(ISheet* pSheet, IDX sheetIdx, ROW row, COL col, BSTR *pBstr, bool bMultiCompare)
{
	if (bMultiCompare)
	{
		m_spStringTools->GetCellText(pSheet, row, col, pBstr, NULL, 0, NULL);
	}
	else
	{
		const_token_ptr pCellValue = NULL;
		m_pBook->LeakOperator()->GetCellValue(sheetIdx, row, col, &pCellValue);
		etexec::TokenToText(pCellValue, pBstr);
	}
}

void KDvCustomListCache::OnFileOpened()
{
	m_bEnableCollectDirtys = TRUE;
}

STDIMP_(void) KDvCustomListCache::OnRangeDirty(const RANGE& rg)
{
	if (m_bEnableCollectDirtys == FALSE  && !rg.IsValid())
		return;
	removeCache(rg);
	m_spDirtyRanges->Append(alg::STREF_THIS_BOOK, rg);
}

STDIMP_(UINT) KDvCustomListCache::GetDirtyRangeCount()
{
	UINT uCnt = 0;
	m_spDirtyRanges->GetCount(&uCnt);
	return uCnt;
}

STDIMP_(void) KDvCustomListCache::ClearDirtys() 
{
	m_spDirtyRanges->Clear();
}

STDIMP_(void) KDvCustomListCache::SerialDirtyRanges(ISerialAcceptor *acpt)
{
	acpt->addKey("customListDirtyCells");
    acpt->beginArray();
    {
        UINT uCount = GetDirtyRangeCount();
        for (INT i = 0; i < static_cast<INT>(uCount); i++)
        {
            const RANGE *prg = NULL;
            m_spDirtyRanges->GetItem(i, NULL, &prg);
            if (prg != NULL)
            {
                acpt->beginStruct();
                acpt->addUint32("sheetFrom", prg->SheetFrom());
                acpt->addUint32("sheetTo", prg->SheetTo());
                acpt->addUint32("rowFrom", prg->RowFrom());
                acpt->addUint32("rowTo", prg->RowTo());
                acpt->addUint32("colFrom", prg->ColFrom());
                acpt->addUint32("colTo", prg->ColTo());
                acpt->endStruct();
            }
        }
    }
    acpt->endArray();
}

}