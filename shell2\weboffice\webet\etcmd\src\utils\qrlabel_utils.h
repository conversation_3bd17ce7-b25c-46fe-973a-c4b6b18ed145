﻿#ifndef __WEBET_UTILS_QRLABEL_H__
#define __WEBET_UTILS_QRLABEL_H__

#include "etcmd/src/et_revision_context_impl.h"
#include "database/database_def.h"

namespace binary_wo
{
	class BinWriter;
};

namespace wo
{
	namespace util
	{
		class QRLabelAttachmentHandler
		{	
		public:
			struct UploadQRLabelImgInfo
			{
				ks_wstring attachmentId;
				ks_wstring uuid;
				QSize size;
			};
		public:
			virtual HRESULT DoQRLabelAttachment(IKWorksheet* pWorkSheet, ROW row, COL col, const UploadQRLabelImgInfo& imgInfo) = 0;
		};
		class QRLabelUploader
		{
		public:
			QRLabelUploader(QRLabelAttachmentHandler* pHandler, IKWorksheet* pWorkSheet, ICellImages* pCellImages, PCWSTR connId);
			HRESULT UploadQRLabel(ROW row, COL col, IQRLabelHandle* pQRLabelHandle);
			HRESULT BatchUploadQRLabel(COL col = -1);
		private:
			HRESULT Init();
			HRESULT SerialColQRLabelHandle(binary_wo::BinWriter* binWriter, COL col);
			HRESULT UploadColQRLabel(binary_wo::BinWriter* binWriter, COL col);
		private:
			IKWorksheet* m_pWorkSheet;
			ICellImages* m_pCellImages;
			QRLabelAttachmentHandler* m_pHandler;
			struct QRLabelImgParam
			{
				QRLabelImgParam(IQRLabelHandle* pQRLabelHandle, ks_wstring imgUuid = ks_wstring())
				: m_pQRLabelHandle(pQRLabelHandle), m_imgUuid(imgUuid)
				{

				}
				
				IQRLabelHandle* m_pQRLabelHandle;
				ks_wstring m_imgUuid;
			};
			std::unordered_map<COL, std::unordered_map<ROW, QRLabelImgParam>> m_qrLabelMap;
			std::unordered_map<COL, ks_wstring> m_pageIdMap;
			ks_wstring m_connId;
			int m_uploadBatchSize = 100;
		};

		class QRLabelHelper : public QRLabelAttachmentHandler
		{
			public:
				enum UseType
				{
					UseType_None,
					UseType_Import_KSheet2KSheet,
					UseType_Import_KSheet2Et,
					UseType_Import_KSheet2DB,
					UseType_Export_KSheet2Xlsx,
					UseType_Export_KSheet2PDF,
					UseType_Export_KSheet2IMG,
					UseType_MergeFile,
					UseType_SplitToNewBook,		// 子进程
					UseType_SplitToNewSheet,	// 当前进程
				};

				QRLabelHelper(_Workbook* wb, wo::KEtRevisionContext* ctx, UseType useType, binary_wo::VarObj* pParam);
				virtual ~QRLabelHelper();
				HRESULT ConvertQRLabel2CellImg(IDX sheetIdx);
				HRESULT ConvertQRLabel2CellImg(const RANGE& range);
				bool GetUploadQRLabelImgAttachmentId(UINT, ROW, COL, ks_wstring&);

			private:
				HRESULT Init();
				HRESULT ConvertQRLabel2CellImgInl(IKWorksheet* pWorkSheet, Database::IDbField *pField, const RANGE& rg);
				HRESULT DoOnQRField(Database::FieldContext *pContext, Database::IDbField *pField, const RANGE& rg);
				HRESULT DoQRLabelAttachment(IKWorksheet* pWorkSheet, ROW row, COL col, const UploadQRLabelImgInfo& imgInfo) override;
				void SaveQRLabelImgInfo(IKWorksheet* pWorkSheet, ROW row, COL col, const UploadQRLabelImgInfo& imgInfo);
				bool CanConvertQRLabel(UseType type);
				void AdjustCellPictureSize(const QSize& imgSize, const QSize& cellSize, UseType type, single& w, single& h);
				void AddQRLableImgInfoParam();
				void ParseQRLableImgInfoParam();
				bool GetQRLableImgInfoFromParam(IKWorksheet* pWorkSheet, ROW row, COL col, UploadQRLabelImgInfo& imgInfo);
			private:
				ks_stdptr<_Workbook> m_spWb;
				QRLabelAttachmentHandler* m_pHandler;
				wo::KEtRevisionContext* m_pCtx;
				binary_wo::VarObj* m_pParam = nullptr;
				UseType m_useType = UseType_None;
				std::map<UINT, std::map<COL, std::map<ROW, UploadQRLabelImgInfo>>> m_qrlabelImgMap;
				std::map<UINT, std::map<COL, std::map<ROW, UploadQRLabelImgInfo>>> m_qrlabelImgMapInParam;
				bool m_ConvertQRLabelEnable = true;
		};
	} // namespace util
} // namespace wo

#endif
