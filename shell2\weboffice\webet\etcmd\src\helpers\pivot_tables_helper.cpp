﻿#include "etstdafx.h"
#include "pivot_tables_helper.h"
#include "etcore/little_alg.h"
#include "kfc/datetime.h"
#include "pivot_core/pivot_core_def.h"
#include "pivot_core/pivot_core_filter_operator.h"
#include "pivot_core/pivot_core_cpl.h"
#include "kso/l10n/et/et_app.h"
#include "kso/api/smartparam.h"
#include "kfc/et_numfmt_str.h"

#include "util.h"

namespace wo
{

namespace PivotHelpers
{
bool HasPivotTableInRange(etoldapi::Range *pRange)
{
    ks_stdptr<pivot_core::IPivotTable> spPvtTable;
    if (pRange && pivot_helper::GetCorePivotTableByCell(pRange, &spPvtTable))
        return true;
    return false;
}

static bool IsPercentCalculation(oldapi::XlPivotFieldCalculation pfc)
{
	return pfc == xlPercentDifferenceFrom || pfc == xlPercentOf ||
		pfc == xlPercentOfColumn || pfc == xlPercentOfRow ||
		pfc == xlPercentOfTotal || pfc == xlPercentOfParentRow ||
		pfc == xlPercentOfParentColumn || pfc == xlPercentOfParent ||
		pfc == xlPercentRunningTotal;
}

void Rect_Expand(RECT &src, const RECT &tag)
{
    // tag无效的话就不进行拓展了
    if (!Rect_IsValid(tag))
        return;

    // src无效tag有效的话直接取tag
    if (!Rect_IsValid(src))
    {
        src = tag;
        return;
    }

    // 都有效的话就正常进行拓展
    src.top = std::min(src.top, tag.top);
    src.left = std::min(src.left, tag.left);
    src.bottom = std::max(src.bottom, tag.bottom);
    src.right = std::max(src.right, tag.right);
    ASSERT(Rect_IsValid(src));
}

CELL GetFirstResultCell(IKWorksheet* pWorksheet, pivot_core::IPivotTable *pPT, bool *bCrossFrozen)
{
    RECT bodyRc = Rect_CreateScaleNone();
    pPT->GetBodyRect(&bodyRc);
    RECT dataRc = Rect_CreateScaleNone();
    pPT->GetDataArea(&dataRc);
    UINT nPage = pPT->GetPageRectCount();
    bool hasReport = nPage > 0;
    RECT reportRc = Rect_CreateScaleNone();
    if (hasReport) {
        std::vector<RECT> vecPage(nPage);
        pPT->GetPageRect(&vecPage[0], nPage);
        for (int i = 0; i < nPage; i++)
            Rect_Expand(reportRc, vecPage[i]);
    }

    CELL cell = {bodyRc.top, bodyRc.left};
    // 有report默认滚到report
    if (hasReport)
        cell = {reportRc.top, reportRc.left};

    if (bCrossFrozen) *bCrossFrozen = false;
    ISheetWndInfos *pWndInfos = pWorksheet->GetWndInfos();
    INT nCount = 0;
    pWndInfos->GetCount(&nCount);
    if (nCount > 0)
    {
        ISheetWndInfo *pWndInfo = nullptr;
        pWndInfos->GetItem(0, &pWndInfo);
        if (pWndInfo && pWndInfo->GetFrozen())
        {
            ROW top = pWndInfo->GetPanePositionTop();

            if (hasReport && IsSeria(reportRc.top, top, bodyRc.top))
            {
                // 冻结线在report和body之间, 滚动到body
                if (bCrossFrozen) *bCrossFrozen = true;
                cell = {bodyRc.top, bodyRc.left};
            }
            else if (IsSeria(bodyRc.top, top, dataRc.top))
            {
                // 冻结线在body和data之间, 滚动到data
                if (bCrossFrozen) *bCrossFrozen = true;
                cell = {dataRc.top, dataRc.left};
            }
        }
    }

    return cell;
}

RECT GetFilterButtonRect(IKWorksheet* pWorksheet, pivot_core::IPivotTable *pPT)
{
    ISheet *pSheet = pWorksheet->GetSheet();
    RECT rect = Rect_CreateScaleNone();
    RECT rowButton = Rect_CreateScaleNone();
    pPT->GetRowButtonArea(&rowButton);
    Rect_Expand(rect, rowButton);
    RECT colButton = Rect_CreateScaleNone();
    pPT->GetColButtonArea(&colButton);
    Rect_Expand(rect, colButton);

    UINT nPage = pPT->GetPageRectCount();
    std::vector<RECT> vecPage(nPage);
    pPT->GetPageRect(&vecPage[0], nPage);
    for (int i = 0; i < nPage; i++)
        Rect_Expand(rect, vecPage[i]);

    return rect;
}

pivot_core::ITblFieldAxis* GetFieldAxisByFieldId(pivot_core::IPivotTable* pPT, int nFieldId)
{
	pivot_core::IDX_FIELD fid(nFieldId);
	pivot_core::ITblFieldSrc* pFieldSrc = pPT->GetPivotFields()->Item(fid);
	if (NULL == pFieldSrc)
		return NULL;

	return pFieldSrc->QueryAxisField(false);
}

pivot_core::ITblFieldAxis* GetFieldAxisByFieldName(pivot_core::IPivotTable* pPT, WebStr fieldName)
{
    using namespace pivot_core;
	AxisFieldType afts[] = {AxisFieldPage, AxisFieldRow, AxisFieldCol };
	for (size_t i = 0, n = countof(afts); i < n; ++i)
	{
		ITblFieldsAxis* axes = pPT->GetAxisFields(afts[i]);
	    ASSERT(axes);
	    IDX_AXIS idx = axes->IdxByName(fieldName);
	    if (IDX_AXIS::inv() == idx || axes->Item(idx) == NULL)
		    continue;
	    return axes->Item(idx);
    }
    return NULL;
}

pivot_core::ITblFieldAxis* GetFieldAxisByCell(pivot_core::IPivotTable *pPT, CELL cell, WebStr fieldName)
{
    auto GetFieldAxisIdx = [](pivot_core::IPivotTable *pPT, CELL cell, WebStr fieldName, pivot_core::ITblFieldsAxis* pFieldsAxis, RECT rc) -> int
    {
        int idx = INVALIDIDX;
        // 有给fieldName先按fieldName找
        if (fieldName)
        {
            ks_bstr bstrFieldName(fieldName);
            for (int i = 0; i < pFieldsAxis->Count(); i++)
            {
                pivot_core::ITblFieldAxis *pAxis = pFieldsAxis->Item(pivot_core::IDX_AXIS(i));
                ks_bstr bstrName(pAxis->QueryFieldSrc()->GetName());

                if (bstrName.isEqual(bstrFieldName))
                {
                    idx = i;
                    break;
                }
            }
        }

        // 找不到 而且还是compact的话默认第一个
        if (idx == INVALIDIDX && pPT->HasCompactFields())
            idx = 0;

        // 还是找不到 按相对位置算
        if (idx == INVALIDIDX && Rect_IsValid(rc))
            idx = cell.col - rc.left;

        return idx;
    };

    switch (pPT->GetCellType(cell))
    {
        case pivot_core::CellRowLabel:
        case pivot_core::CellRowButton:
        {
            pivot_core::ITblFieldsAxis* pFieldsAxis = pPT->GetAxisFields(pivot_core::AxisFieldRow);
            RECT rc = Rect_CreateScaleNone();
            pPT->GetRowButtonArea(&rc);
            int idx = GetFieldAxisIdx(pPT, cell, fieldName, pFieldsAxis, rc);
            if (idx >= 0 && idx < pFieldsAxis->Count())
                return pFieldsAxis->Item(pivot_core::IDX_AXIS(idx));
            break;
        }
        case pivot_core::CellColLabel:
        case pivot_core::CellColButton:
        {
            pivot_core::ITblFieldsAxis* pFieldsAxis = pPT->GetAxisFields(pivot_core::AxisFieldCol);
            RECT rc = Rect_CreateScaleNone();
            pPT->GetColButtonArea(&rc);
            int idx = GetFieldAxisIdx(pPT, cell, fieldName, pFieldsAxis, rc);
            if (idx >= 0 && idx < pFieldsAxis->Count())
                return pFieldsAxis->Item(pivot_core::IDX_AXIS(idx));
            break;
        }
        case pivot_core::CellPageFieldItem:
        {
            pivot_core::ITblFieldsAxis* pFieldsAxis = pPT->GetAxisFields(pivot_core::AxisFieldPage);

            pivot_core::IDX_AXIS idx = pPT->GetAxisPageItem(cell);
            if (idx != pivot_core::IDX_AXIS::inv())
                return pFieldsAxis->Item(idx);
            break;
        }
        default:
            break;
    }

    return NULL;
}

static bool ExpendToListObject(ISheet* pSheet, const RANGE &rg, OUT BSTR* pbstrRange)
{
    if (pSheet->IsDbSheet() || pSheet->IsOldDbSheet())
    {
        return false;
    }

	ks_stdptr<ICoreListObjects> spLstObjs;
	pSheet->GetExtDataItem(edSheetListObjects, (IUnknown**)&spLstObjs);
	if(spLstObjs == NULL)
		return false;

	ks_stdptr<ICoreListObject> spLstObj;
	spLstObjs->FindFirstItem(rg, &spLstObj);
	if (spLstObj == NULL)
		return false;

	RANGE rgTbl(rg.GetBMP());
	spLstObj->GetRange(&rgTbl);

	if (!rgTbl.Contain(rg))
		return false;
	
	PCWSTR name = NULL; 
	spLstObj->GetDisplayName(&name);
	*pbstrRange = ks_bstr(name).detach();
	return true;
}

void GetDefaultSourceRange(ETReferenceStyle RefStyle, const ks_stdptr<Range>& ptrSelection, _Worksheet* pWorksheet, OUT BSTR* pbstrRange)
{
    ASSERT(NULL != pbstrRange);

    ks_stdptr<IKWorksheet> ptrWorksheetInfo = pWorksheet;
    ks_stdptr<ISheet> ptrSheet = ptrWorksheetInfo->GetSheet();

    range_helper::ranges rgs;
    app_helper::GetIRanges(ptrSelection, &rgs);
    UINT nCount = 0;
    rgs->GetCount(&nCount);
    if (nCount > 1)
    {
        range_helper::ranges rgsNew = range_helper::ranges::create_instance();
        RANGE rg = *rgs.at(0).second;
        rg.SetRowFromTo(0);
        rg.SetColFromTo(0);

        RANGE rgUsed(rg);
        ptrWorksheetInfo->GetUsedRange(&rgUsed);
        rg = rgUsed.Intersect(rg);
        if (!rg.IsValid())
        {
            *pbstrRange = NULL;
            return;
        }

        rgsNew->Append(alg::STREF_THIS_BOOK, rg);

        ks_stdptr<IKWorkbook> ptrWBI;
        ptrWBI = ptrWorksheetInfo->GetWorkbook();

        CELL	cell = { 0, 0 };
        ptrWorksheetInfo->GetRangeAddress(ptrWBI,
            rgsNew,
            (etR1C1 == RefStyle ? crfRCStyle : 0) |
            crfAbsRowFrom | crfAbsRowTo | crfAbsColFrom | crfAbsColTo | crfForceSheet,
            cell,
            pbstrRange);
        return;
    }

    if (nCount == 1 && ExpendToListObject(ptrSheet, *rgs.at(0).second, pbstrRange))
        return;

    if (app_helper::IsSingleCell(ptrSelection, NULL, FALSE))
    {
        RANGE rgData = *rgs.at(0).second;

        //取得连续的最大有数据的区域
        appcore_helper::GetContinualRangeMaxEx(ptrSheet, rgData.RowFrom(), rgData.ColFrom(), TRUE, &rgData);
        ptrWorksheetInfo->GetUsedInRange(&rgData);
        rgs.set(0, alg::STREF_THIS_BOOK, rgData);
    }

    {
        RANGE rgData = *rgs.at(0).second;
        ks_stdptr<Range> spRange;
        ptrWorksheetInfo->GetRangeByData(rgs, &spRange);
        // 猜行头，Excel也是用了和自动筛选相同的算法
        int iHeader = app_helper::GuessRangePivotrTableHeader(spRange);
        ROW realRow = rgData.RowFrom() + iHeader;
        if (realRow <= rgData.RowTo())
            rgData.SetRowFrom(realRow);
        rgs.set(0, alg::STREF_THIS_BOOK, rgData);
    }

    { {	// 去掉第一行的左右空格
            RANGE rgData = *rgs.at(0).second;

            CELL cell_LT = appcore_helper::GetNearstValuedCell(ptrSheet, rgData.RowFrom(), rgData.ColFrom() - 1, dirRight);
            CELL cell_RT = appcore_helper::GetNearstValuedCell(ptrSheet, rgData.RowFrom(), rgData.ColTo() + 1, dirLeft);

            if (cell_LT.col > cell_RT.col)
            {
                *pbstrRange = NULL;
                return;			//  注意，这种情况下提前退出了 !!!
            }
            rgData.SetColFromTo(cell_LT.col, cell_RT.col);
            rgs.set(0, alg::STREF_THIS_BOOK, rgData);
        }}

    ks_stdptr<IKWorkbook> ptrWBI = ptrWorksheetInfo->GetWorkbook();
    CELL	cell = { 0, 0 };

    ptrWorksheetInfo->GetRangeAddress(ptrWBI,
        rgs,
        (etR1C1 == RefStyle ? crfRCStyle : 0) |
        crfAbsRowFrom | crfAbsRowTo | crfAbsColFrom | crfAbsColTo | crfForceSheet,
        cell,
        pbstrRange);
}

static int GetFieldIndexByName(etoldapi::PivotTable* tbl, PCWSTR fieldName)
{
    KComVariant varEmpty;
    ks_stdptr<IKCoreObject> spFieldsTmp;
    if (FAILED(tbl->PivotFields(varEmpty, &spFieldsTmp)))
        return INVALIDIDX;

    ks_castptr<etoldapi::PivotFields> spFields = spFieldsTmp;
    long cnt = 0;
    spFields->get_Count(&cnt);

    for (int i = 0; i < cnt; ++i)
    {
        ks_stdptr<etoldapi::PivotField> spField;
        KComVariant varIdx;
        varIdx.Assign(i + 1);
        VS(spFields->Item(varIdx, &spField));
        ks_bstr bstrFieldName;
        spField->get_Name(&bstrFieldName);
        if (xstrcmp(bstrFieldName.c_str(), fieldName) == 0)
        {
            return i;
        }
    }

    return INVALIDIDX;
}

static int GetDataFieldPosByName(etoldapi::PivotTable* tbl, PCWSTR fieldName)
{
    KComVariant varEmpty;
    ks_stdptr<IKCoreObject> spFieldsTmp;
    ks_stdptr<IKCoreObject> spFldObj;
    if (SUCCEEDED(tbl->AxisFields(oldapi::etDataField, varEmpty, &spFldObj)))
    {
        ks_castptr<etoldapi::PivotFields> spFields = spFldObj;
        long cnt = 0;
        spFields->get_Count(&cnt);
        for (int i = 0; i < cnt; ++i)
        {
            ks_stdptr<etoldapi::PivotField> spField;
            KComVariant varIdx;
            varIdx.Assign(i + 1);
            VS(spFields->Item(varIdx, &spField));
            ks_bstr bstrFieldName;
            spField->get_Name(&bstrFieldName);
            if (xstrcmp(bstrFieldName.c_str(), fieldName) == 0)
            {
                KComVariant varPosTmp;
                VS(spField->get_Position(&varPosTmp));
                KSmartParam varPos = varPosTmp;
                ASSERT(varPos.IsIntegerType());
                return varPos.GetNumberValue() - 1;
            }
        }
    }

    return INVALIDIDX;
}

static int GetDataFieldIndexByName(IET_PivotTableTool* tool, etoldapi::PivotTable* tbl, PCWSTR fieldName)
{
    KComVariant varEmpty;
    ks_stdptr<IKCoreObject> spFieldsTmp;
    ks_stdptr<IKCoreObject> spFldObj;
    if (SUCCEEDED(tbl->AxisFields(oldapi::etDataField, varEmpty, &spFldObj)))
    {
        ks_castptr<etoldapi::PivotFields> spFields = spFldObj;
        long cnt = 0;
        spFields->get_Count(&cnt);
        for (int i = 0; i < cnt; ++i)
        {
            ks_stdptr<etoldapi::PivotField> spField;
            KComVariant varIdx;
            varIdx.Assign(i + 1);
            VS(spFields->Item(varIdx, &spField));
            ks_bstr bstrFieldName;
            spField->get_Name(&bstrFieldName);
            if (xstrcmp(bstrFieldName.c_str(), fieldName) == 0)
            {
                if (tool->GetCoreField(spField))
                {
                    PCWSTR pcwName = tool->GetCoreField(spField)->GetName();
                    return GetFieldIndexByName(tbl, pcwName);
                }
            }
        }
    }

    return INVALIDIDX;
}

static int GetFieldIndexByName(IET_PivotTableTool* tool, etoldapi::PivotTable* tbl, PCWSTR fieldName, oldapi::ETPivotFieldOrientation fieldDir)
{
    if (fieldDir == etDataField)
    {
        return GetDataFieldIndexByName(tool, tbl, fieldName);
    }
    else
    {
        return GetFieldIndexByName(tbl, fieldName);
    }
}

HRESULT GetApiPivotTable(etoldapi::_Worksheet *pWorksheet, pivot_core::IPivotTable *pPivotTable, etoldapi::PivotTable **ppApiPivotTable)
{
    if (pWorksheet == nullptr || pPivotTable == nullptr)
        return E_FAIL;

    return pWorksheet->GainPivotTable(pPivotTable, ppApiPivotTable);
}

HRESULT GetPivotTableTool(etoldapi::_Workbook *pWorkbook, IET_PivotTableTool** tool)
{
    if (pWorkbook == nullptr)
        return E_FAIL;

    ks_stdptr<IET_PivotTableTool> spPivotTool;
    _applogic_CreateObject(CLSID_KPivotTableTool, IID_IET_PivotTableTool, (void**)tool);
    return (*tool)->Init(pWorkbook);
}

HRESULT GetApiPivotField(pivot_core::ITblFieldAxis* pFieldAxis, etoldapi::PivotTable *pApiPivotTable, etoldapi::PivotField **ppApiPivotField)
{
    if (pFieldAxis == nullptr || pApiPivotTable == nullptr)
        return E_FAIL;

    VARIANT varName;
    ks_bstr bstrName(pFieldAxis->QueryFieldSrc()->GetName());
    V_VT(&varName) = VT_BSTR; V_BSTR(&varName) = (BSTR)bstrName;
    return pApiPivotTable->PivotFields(varName, (IKCoreObject **)ppApiPivotField);
}

BOOL GetCanSort(pivot_core::ITblFieldAxis* pFieldAxis, pivot_core::IPivotTable *pPivotTable)
{
    if (!pFieldAxis)
        return FALSE;

    pivot_core::AxisFieldType type = pFieldAxis->GetFieldType();
    if (pivot_core::AxisFieldPage == type || 
        pivot_core::AxisFieldNone == type)
        return FALSE;

    std::vector<pivot_core::IDX_ITEM> vec;
    if (!pFieldAxis->GetVisibledItems(vec) && vec.empty()) return FALSE;

    if (!pPivotTable->HasItemShowDetail(pFieldAxis)) return FALSE;

    pivot_core::ITblFieldAxisItems* fldAxisItems = pFieldAxis->GetFieldItems();
    for (int i = 0, n = fldAxisItems->Count(); i < n; ++i)
    {
        pivot_core::ITblFieldAxisItem * pItem = fldAxisItems->Item(pivot_core::IDX_ITEM(i));
        if (pItem == NULL) continue;

        if (!pItem->IsCalculated()) return TRUE;
    }

    return FALSE;
}

std::vector<RANGE> getPivotTableSectionRanges(ISheet *isheet, pivot_core::IPivotTable *spPvtTable)
{
    if (!isheet)
    {
        return std::vector<RANGE>();
    }
	int count = spPvtTable->GetSectionRangeCount();
	std::vector<RANGE> rgs(count, RANGE(isheet->GetBMP()));
	spPvtTable->GetSectionRanges(rgs.data());
	return rgs;
}

oldapi::ETPivotFieldOrientation ToFieldOrientation(WebStr str)
{
    if (xstrcmp(str, __X("etColumnField")) == 0)
    {
        return etColumnField;
    }
    else if (xstrcmp(str, __X("etDataField")) == 0)
    {
        return etDataField;
    }
    else if (xstrcmp(str, __X("etHidden")) == 0)
    {
        return etHidden;
    }
    else if (xstrcmp(str, __X("etPageField")) == 0)
    {
        return etPageField;
    }
    else if (xstrcmp(str, __X("etRowField")) == 0)
    {
        return etRowField;
    }
    else 
    {
        ASSERT(false);
    }

    return oldapi::ETPivotFieldOrientation(etDataField);
}

static ETSortOrder toSortOrder(PCWSTR order)
{
    if (0 == xstrcmp(order, __X("etAscending")))
		return etAscending;
        
    if (0 == xstrcmp(order, __X("etDescending")))
		return etDescending;

    if (0 == xstrcmp(order, __X("etSortManual")))
		return etDescending;

    return etAscending;
}

} // PivotHelpers

inline 
BOOL VarBool_BOOL(VARIANT_BOOL vb)
{
    return vb != VARIANT_FALSE;
}

inline
VARIANT_BOOL BOOL_VarBool(BOOL b)
{
    return b ? VARIANT_TRUE : VARIANT_FALSE;
}

PivotFilterHelperData::PivotFilterHelperData(pivot_core::IPivotTable *pPT, pivot_core::ITblFieldAxis *pFA, etoldapi::_Worksheet *pWS)
    : m_spPivotTable(pPT)
    , m_spFieldAxis(pFA)
    , m_spWorksheet(pWS)
{}

HRESULT PivotFilterHelperData::Init()
{
    HRESULT hr = PivotHelpers::GetApiPivotTable(m_spWorksheet, m_spPivotTable, &m_spApiPivotTable);
    if (FAILED(hr))
        return hr;
    hr = PivotHelpers::GetApiPivotField(m_spFieldAxis, m_spApiPivotTable, &m_spApiPivotField);
    if (FAILED(hr))
        return hr;
    return S_OK;
}

SerialisePivotFilterHelper::SerialisePivotFilterHelper(pivot_core::IPivotTable *pPT, pivot_core::ITblFieldAxis *pFA, etoldapi::_Worksheet *pWS, PCWSTR searchData)
    : PivotFilterHelperData(pPT, pFA, pWS)
{
    m_searchData = searchData;

    if (m_searchData && xstrlen(m_searchData) == 0)
        m_searchData = NULL;

    if (fieldIsOnlyNumberType())
        m_filterType = customFilterType_Number;
    else if(fieldIsOnlyDateType())
        m_filterType = customFilterType_Date;
    else
        m_filterType = customFilterType_Label;
}

PCWSTR SerialisePivotFilterHelper::customTypeToStr(ET_CUSTOM_FILTER_TYPE ty)
{
    switch(ty)
    {
        case customFilterType_NULL:
            return __X("null");
        case customFilterType_Number:
            return __X("num");
        case customFilterType_Date:
            return __X("date");
        case customFilterType_Label:
            return __X("label");
        default:
            ASSERT(FALSE);
            return __X("null");
    }
}

void SerialisePivotFilterHelper::Serialise(ISerialAcceptor *acpt)
{
    if (!m_spFieldAxis)
        return;
    pivot_core::ITblFieldAxisItems *pItems = m_spFieldAxis->GetFieldItems();
    if (!pItems)
        return;
    int nItems = pItems->Count();
    bool isAll = nItems > 10000 ? false : true;
    acpt->addKey("items");
    acpt->beginStruct();
        acpt->addString("type", customTypeToStr(m_filterType));
        acpt->addBool("isAllReslut", isAll);
        acpt->addKey("root");
        serialiseValues(acpt, pItems);
    acpt->endStruct();
}


void SerialisePivotFilterHelper::serialiseValues(ISerialAcceptor *acpt, pivot_core::ITblFieldAxisItems *pItems)
{

    PivotFilterNode root(PivotFilterNode::eRoot, -1, __X(""), m_spFieldAxis->IsAllItemsVisible() ? PivotFilterNode::CheckType_checked : PivotFilterNode::CheckType_unchecked, -1);
    GenerateFilterTree(root);
    MaintenanceCheckState(root);
    bool childAllMatch = true;
    int serialCount = 0;
    int totalCount = pItems->Count();
    acpt->beginStruct();
        root.serialiseValues(acpt);
    acpt->endStruct();
}

HRESULT SerialisePivotFilterHelper::GetOrder(ET_SORT_ORDER& sortOrder)
{
    if(m_spFieldAxis == NULL ||  m_spFieldAxis->QueryFieldSrc() == NULL)
        return E_FAIL;
    if (m_spFieldAxis->QueryFieldSrc()->GetAutoSortOrder() ==  pivot_core::soSortManual)
        sortOrder = sot_None;
    else if(m_spFieldAxis->QueryFieldSrc()->GetAutoSortOrder() ==  pivot_core::soSortAsc)
        sortOrder = sot_Ascending ;
    else
        sortOrder = sot_Descending;

    return S_OK;
}

bool SerialisePivotFilterHelper::fieldIsOnlyDateType()
{
    if (!m_spFieldAxis)
        return false;
    DWORD dflag = m_spFieldAxis->GetCfdSummaryFlags();
    return pivot_core::ClusterDataSummary::IsDateTypeOnly(dflag);
}

bool SerialisePivotFilterHelper::fieldIsOnlyNumberType()
{
    if (!m_spFieldAxis)
        return false;
    DWORD dflag = m_spFieldAxis->GetCfdSummaryFlags();
    return pivot_core::ClusterDataSummary::IsNumberTypeOnly(dflag);
}

// ===================================================
SerialisePivotFilterOperatorHelper::SerialisePivotFilterOperatorHelper(pivot_core::IPivotTable *pPT, pivot_core::ITblFieldAxis *pFA, etoldapi::_Worksheet *pWS, BOOL b1904)
    : PivotFilterHelperData(pPT, pFA, pWS)
    , m_b1904(b1904)
{}

void SerialisePivotFilterOperatorHelper::Serialise(ISerialAcceptor *acpt)
{
    acpt->addBool("canSort", PivotHelpers::GetCanSort(m_spFieldAxis, m_spPivotTable));
    acpt->addKey("condition");
    acpt->beginStruct();
        pivot_core::ITblFieldSrc* fldSrc = m_spFieldAxis->QueryFieldSrc();
        if (fldSrc == NULL) return;

        pivot_core::FilterParam filterCaption, filterValue;
        pivot_core::IDX_ITEM idx;
        fldSrc->GetFilterCaption(&filterCaption);
        fldSrc->GetFilterValue(&filterValue, &idx);
        
        if (filterCaption.fot >= pivot_core::FO_CaptionBetween && filterCaption.fot <= pivot_core::FO_CaptionLessThanOrEqual)
            serialiseCaption(acpt, filterCaption);
        else if (filterCaption.fot >= pivot_core::FO_DateBetween && filterCaption.fot <= pivot_core::FO_DateOlderThanOrEqual)
            serialiseDate(acpt, filterCaption);
        else if (filterCaption.fot >= pivot_core::FO_DateYesterday && filterCaption.fot <= pivot_core::FO_DateYearToDate)
            serialiseDynamic(acpt, filterCaption);
        else if (filterValue.fot >= pivot_core::FO_ValueBetween && filterValue.fot <= pivot_core::FO_ValueLessThanOrEqual)
            serialiseNumber(acpt, filterValue, idx);
        else if (filterValue.fot >= pivot_core::FO_TopNCount && filterValue.fot <= pivot_core::FO_TopNSum)
            serialiseTopN(acpt, filterValue, idx);
        else
        {
            bool bIsFilterValues = false;
            bool bIsAllVisible = m_spFieldAxis->IsAllItemsVisible();
            if (m_spFieldAxis->GetFieldType() == pivot_core::AxisFieldPage)
            {
                bool bIsMultipleSelect = m_spFieldAxis->QueryFieldSrc()->GetProperty(pivot_core::FPT_PageFieldIsMultipleSelect);
                if (bIsMultipleSelect)
                {
                    bIsFilterValues = !bIsAllVisible;
                }
                else
                {
                    KComVariant currentPage;
                    if (SUCCEEDED(m_spApiPivotField->get_CurrentPage(&currentPage)) && V_VT(&currentPage) == VT_BSTR)
                    {
                        ks_wstring currentPageName(V_BSTR(&currentPage));
                        bIsFilterValues = currentPageName != TX_PIVOT_PAGE_ITEM_ALL;
                    }
                }
            }
            else
            {
                bIsFilterValues = filterCaption.fot != pivot_core::FO_Unknown ? true : !bIsAllVisible;
            }

            if (bIsFilterValues)
                serialiseValues(acpt);
            else
            {
                acpt->addString("operator", __X("none"));
            }
        }
    acpt->endStruct();
}

void SerialisePivotFilterOperatorHelper::serialiseValues(ISerialAcceptor *acpt)
{
    acpt->addString("operator", __X("filterValues"));
    constexpr int MaxLen = 50;
    ks_wstring strList;

    pivot_core::ITblFieldAxisItems *pItems = m_spFieldAxis->GetFieldItems();
    if (!pItems)
        return;

    VARIANT_BOOL vb;
    m_spApiPivotField->get_EnableMultiplePageItems(&vb);
    bool bIsPage = m_spFieldAxis->GetFieldType() == pivot_core::AxisFieldPage;
    bool bIsMultipleSelect = VarBool_BOOL(vb);
    bool bSelectAll = false;
    ks_wstring currentPageName;
    if (!bIsMultipleSelect)
    {
        KComVariant currentPage;
        if (SUCCEEDED(m_spApiPivotField->get_CurrentPage(&currentPage)) && V_VT(&currentPage) == VT_BSTR)
        {
            currentPageName.assign(V_BSTR(&currentPage));
            if (currentPageName == TX_PIVOT_PAGE_ITEM_ALL)
                bSelectAll = true;
        }
    }

    int nItems = pItems->Count();
    for (int i = 0; i < nItems; i++)
    {
        ks_stdptr<pivot_core::ITblFieldAxisItem> spItem = pItems->Item(pivot_core::IDX_ITEM(i));

        if (spItem == NULL || !spItem->IsValid() || !spItem->IsVisible() || 
            (spItem->IsMissing() && m_spFieldAxis->GetFieldType() == pivot_core::AxisFieldPage
            && !m_spFieldAxis->QueryFieldSrc()->GetProperty(pivot_core::FPT_ShowNoDataItems)))
            continue;
        
        bool checked = bIsPage && !bIsMultipleSelect ? 
             (bSelectAll || spItem->GetName() == currentPageName) : 
             spItem->IsVisible();

        if (checked)
        {
            if (!strList.empty())
                strList.append(__X(","));
            strList.append(spItem->GetName());
            if (strList.length() > MaxLen)
                break;
        }
    }

    if (strList.length() > MaxLen)
        strList.resize(MaxLen);

    acpt->addString("valList", strList.c_str());
    acpt->addBool("cut", strList.length() > MaxLen);
}

void SerialisePivotFilterOperatorHelper::serialiseCaption(ISerialAcceptor *acpt, pivot_core::FilterParam fp)
{
    acpt->addString("operator", __X("and"));

    acpt->addKey("param1");
    acpt->beginStruct();
        acpt->addString("customType", FOT2TypeStr(fp.fot));
        acpt->addString("val", fp.strValue1.c_str());
    acpt->endStruct();

    if (pivot_core::FilterOperatorHelp::IsCaptionStrBinary(fp.fot))
    {
        acpt->addKey("param2");
        acpt->beginStruct();
            acpt->addString("customType", FOT2TypeStr(fp.fot));
            acpt->addString("val", fp.strValue2.c_str());
        acpt->endStruct();
    }
}

void SerialisePivotFilterOperatorHelper::serialiseDate(ISerialAcceptor *acpt, pivot_core::FilterParam fp)
{
    auto convertDateValueToStr = [](double dbl, BOOL b1904)-> ks_wstring
    {
        kfc::nf::NFHANDLE hNF = NULL;
        HRESULT hr = S_OK;
        if (0 <= dbl && dbl < 1)
        {
            ks_bstr bstrFormat;
            _cpl_NFAGetFormatByFFT(fftHMS, &bstrFormat);
            hr = kfc::nf::_XNFCompile(bstrFormat, &hNF, NULL);
        }
        else if (fmod(dbl, 1.) == 0.)
        {
            hr = kfc::nf::_XNFCompile(kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_DATE1), &hNF, NULL);
        }
        else
        {
            hr = kfc::nf::_XNFCompile(kfc::nf::_XNFGetEtStr(Et_NFUIL_09), &hNF, NULL);
        }
        ks_bstr bstr;
        if (S_OK == hr)
        {
            KComVariant var;
            var.AssignDouble(dbl);
            hr = kfc::nf::_XNFFormatEx2(var, b1904, hNF, &bstr, NULL, NULL);
        }
        
        kfc::nf::_XNFRelease(hNF);
        ks_wstring wstr = bstr.c_str();
        return wstr;
    };

    acpt->addString("operator", __X("and"));

    acpt->addKey("param1");
    acpt->beginStruct();
        acpt->addString("customType", FOT2TypeStr(fp.fot));
        acpt->addString("val", convertDateValueToStr(fp.dblValue1, m_b1904).c_str());
        acpt->addBool("bWholeDay", fp.bUseWholeDay);
    acpt->endStruct();

    if (pivot_core::FilterOperatorHelp::IsCaptionDateBinary(fp.fot))
    {
        acpt->addKey("param2");
        acpt->beginStruct();
            acpt->addString("customType", FOT2TypeStr(fp.fot));
            acpt->addString("val", convertDateValueToStr(fp.dblValue2, m_b1904).c_str());
        acpt->endStruct();
    }
}

void SerialisePivotFilterOperatorHelper::serialiseNumber(ISerialAcceptor *acpt, pivot_core::FilterParam fp, pivot_core::IDX_ITEM idx)
{
    acpt->addString("operator", __X("and"));

    pivot_core::IDX_VALUE  idVaule(idx.v());
    pivot_core::ITblFieldValue* pValueItem = m_spPivotTable->GetValueFields()->Item(idVaule);
    if (pValueItem)
        acpt->addString("dataField", pValueItem->GetName());

    acpt->addKey("param1");
    acpt->beginStruct();
        acpt->addString("customType", FOT2TypeStr(fp.fot));
        acpt->addString("val", krt::utf16(QString::number(fp.dblValue1, 'g', 16)));
    acpt->endStruct();

    if (pivot_core::FilterOperatorHelp::IsValueBinary(fp.fot))
    {
        acpt->addKey("param2");
        acpt->beginStruct();
            acpt->addString("customType", FOT2TypeStr(fp.fot));
            acpt->addString("val", krt::utf16(QString::number(fp.dblValue2, 'g', 16)));
        acpt->endStruct();
    }
}

void SerialisePivotFilterOperatorHelper::serialiseDynamic(ISerialAcceptor *acpt, pivot_core::FilterParam fp)
{
    acpt->addString("operator", __X("filterDynamic"));
    acpt->addString("dynamicCriteria", FOT2TypeStr(fp.fot));

    DATE date =  _XDateFromDouble(fp.dblValue1);
    TIMEINFO timeInf;
    _XTmFromDate2(date, m_b1904, FALSE, 0, timeInf, NULL, NULL);
    acpt->addKey("date1");
    acpt->beginStruct();
        acpt->addInt16("year", timeInf.tm_year);
        acpt->addInt8("month", timeInf.tm_mon);
        acpt->addInt8("day", timeInf.tm_mday);
    acpt->endStruct();

    date =  _XDateFromDouble(fp.dblValue1);
    _XTmFromDate2(date, m_b1904, FALSE, 0, timeInf, NULL, NULL);
    acpt->addKey("date2");
    acpt->beginStruct();
        acpt->addInt16("year", timeInf.tm_year);
        acpt->addInt8("month", timeInf.tm_mon);
        acpt->addInt8("day", timeInf.tm_mday);
    acpt->endStruct();
}

void SerialisePivotFilterOperatorHelper::serialiseTopN(ISerialAcceptor *acpt, pivot_core::FilterParam fp, pivot_core::IDX_ITEM idx)
{
    pivot_core::IDX_VALUE  idVaule(idx.v());
    pivot_core::ITblFieldValue* pValueItem = m_spPivotTable->GetValueFields()->Item(idVaule);
    if (pValueItem)
        acpt->addString("dataField", pValueItem->GetName());
    acpt->addString("operator", FOT2TypeStr(fp.fot, fp.bTop));
    acpt->addFloat64("threshold", fp.dblValue1);
}

PCWSTR SerialisePivotFilterOperatorHelper::FOT2TypeStr(pivot_core::FilterOperatorType fot, bool bTop /*false*/)
{
    switch (fot)
    {
        case pivot_core::FO_Unknown:
            return __X("");

        case pivot_core::FO_CaptionBetween:
        case pivot_core::FO_DateBetween:
        case pivot_core::FO_ValueBetween:
            return __X("between");
        case pivot_core::FO_CaptionNotBetween:
        case pivot_core::FO_DateNotBetween:
        case pivot_core::FO_ValueNotBetween:
            return __X("notBetween");
        case pivot_core::FO_CaptionBeginsWith:
            return __X("beginWith");
        case pivot_core::FO_CaptionNotBeginsWith:
            return __X("notBeginWith");
        case pivot_core::FO_CaptionContains:
            return __X("contains");
        case pivot_core::FO_CaptionNotContains:
            return __X("notContains");
        case pivot_core::FO_CaptionEndsWith:
            return __X("endWith");
        case pivot_core::FO_CaptionNotEndsWith:
            return __X("notEndWith");
        case pivot_core::FO_CaptionEqual:
        case pivot_core::FO_DateEqual:
        case pivot_core::FO_ValueEqual:
            return __X("equals");
        case pivot_core::FO_CaptionNotEqual:
        case pivot_core::FO_DateNotEqual:
        case pivot_core::FO_ValueNotEqual:
            return __X("notEqu");
        case pivot_core::FO_CaptionGreaterThan:
        case pivot_core::FO_DateNewerThan:
        case pivot_core::FO_ValueGreaterThan:
            return __X("greater");
        case pivot_core::FO_CaptionLessThan:
        case pivot_core::FO_DateOlderThan:
        case pivot_core::FO_ValueLessThan:
            return __X("less");
        case pivot_core::FO_CaptionGreaterThanOrEqual:
        case pivot_core::FO_DateNewerThanOrEqual:
        case pivot_core::FO_ValueGreaterThanOrEqual:
            return __X("greaterEqu");
        case pivot_core::FO_CaptionLessThanOrEqual:
        case pivot_core::FO_DateOlderThanOrEqual:
        case pivot_core::FO_ValueLessThanOrEqual:
            return __X("lessEqu");
        
        case pivot_core::FO_DateYesterday:
            return __X("yesterday");
        case pivot_core::FO_DateToday:
            return __X("today");
        case pivot_core::FO_DateTomorrow:
            return __X("tomorrow");
        case pivot_core::FO_DateMonthLast:
            return __X("lastMonth");
        case pivot_core::FO_DateMonthThis:
            return __X("thisMonth");
        case pivot_core::FO_DateMonthNext:
            return __X("nextMonth");
        case pivot_core::FO_DateM1:
            return __X("allDatesInPeriodJanuary");
        case pivot_core::FO_DateM2:
            return __X("allDatesInPeriodFebruray");
        case pivot_core::FO_DateM3:
            return __X("allDatesInPeriodMarch");
        case pivot_core::FO_DateM4:
            return __X("allDatesInPeriodApril");
        case pivot_core::FO_DateM5:
            return __X("allDatesInPeriodMay");
        case pivot_core::FO_DateM6:
            return __X("allDatesInPeriodJune");
        case pivot_core::FO_DateM7:
            return __X("allDatesInPeriodJuly");
        case pivot_core::FO_DateM8:
            return __X("allDatesInPeriodAugust");
        case pivot_core::FO_DateM9:
            return __X("allDatesInPeriodSeptember");
        case pivot_core::FO_DateM10:
            return __X("allDatesInPeriodOctober");
        case pivot_core::FO_DateM11:
            return __X("allDatesInPeriodNovember");
        case pivot_core::FO_DateM12:
            return __X("allDatesInPeriodDecember");
        case pivot_core::FO_DateQuarterLast:
            return __X("lastQuarter");
        case pivot_core::FO_DateQuarterThis:
            return __X("thisQuarter");
        case pivot_core::FO_DateQuarterNext:
            return __X("nextQuarter");
        case pivot_core::FO_DateQ1:
            return __X("allDatesInPeriodQuarter1");
        case pivot_core::FO_DateQ2:
            return __X("allDatesInPeriodQuarter2");
        case pivot_core::FO_DateQ3:
            return __X("allDatesInPeriodQuarter3");
        case pivot_core::FO_DateQ4:
            return __X("allDatesInPeriodQuarter4");
        case pivot_core::FO_DateWeekLast:
            return __X("lastWeek");
        case pivot_core::FO_DateWeekThis:
            return __X("thisWeek");
        case pivot_core::FO_DateWeekNext:
            return __X("nextWeek");
        case pivot_core::FO_DateYearLast:
            return __X("lastYear");
        case pivot_core::FO_DateYearThis:
            return __X("thisYear");
        case pivot_core::FO_DateYearNext:
            return __X("nextYear");
        case pivot_core::FO_DateYearToDate:
            return __X("yearToDate");
        
        case pivot_core::FO_TopNCount:
            return bTop ? __X("top10Items") : __X("bottom10Items");
        case pivot_core::FO_TopNPercent:
            return bTop ? __X("top10Percent") : __X("bottom10Percent");
        case pivot_core::FO_TopNSum:
            return bTop ? __X("top10Sum") : __X("bottom10Sum");
        default:
            return __X("");
    }
}

ExecPivotFilterHelper::ExecPivotFilterHelper(pivot_core::IPivotTable *pPT, pivot_core::ITblFieldAxis *pFA, etoldapi::_Workbook *pWB, etoldapi::_Worksheet *pWS)
    : PivotFilterHelperData(pPT, pFA, pWS)
    , m_spWorkbook(pWB)
{}

HRESULT ExecPivotFilterHelper::Init()
{
    HRESULT hr = PivotFilterHelperData::Init();
    if (FAILED(hr))
        return hr;
    hr = getApiPivotFilters();
    if (FAILED(hr))
        return hr;
    return S_OK;
}

HRESULT ExecPivotFilterHelper::getApiPivotDataField(WebStr fieldName)
{
    if (m_spApiPivotTable == nullptr)
        return E_FAIL;

    ks_bstr bstrName(fieldName);
    ks_stdptr<IKCoreObject> spFieldsCoreObj;
    HRESULT hr = m_spApiPivotTable->AxisFields(etDataField, VARIANT(), &spFieldsCoreObj);
    if(FAILED(hr))
        return hr;
    ks_stdptr<etoldapi::PivotFields> spValuesFields = spFieldsCoreObj;
    VARIANT varName;
    V_VT(&varName) = VT_BSTR; V_BSTR(&varName) = (BSTR)bstrName;
    return spValuesFields->Item(varName, &m_spApiPivotDataField);
}

HRESULT ExecPivotFilterHelper::getApiPivotFilters()
{
    if (m_spApiPivotField == nullptr)
        return E_FAIL;

    return m_spApiPivotField->get_PivotFilters(&m_spApiPivotFilters);
}

HRESULT ExecPivotFilterHelper::resetFilter(bool bForceClear)
{
    if (bForceClear || !m_spPivotTable->GetBoolProperty(pivot_core::TPT_AllowFieldMultiFilter))
        return m_spApiPivotField->ClearAllFilters();

    return S_OK;
}

HRESULT ExecPivotFilterHelper::FilterNone()
{
    ks_stdptr<IET_PivotTableTool> pivotTool;
    HRESULT hr = PivotHelpers::GetPivotTableTool(m_spWorkbook, &pivotTool);
    if (FAILED(hr))
        return hr;
    
    {
        hr = S_OK;
        PivotTblBatchHelper hlp(pivotTool, m_spApiPivotTable, &hr);

        hlp.ProcResult(resetFilter(true));
    }

    return hr;
}

HRESULT ExecPivotFilterHelper::FilterValues(binary_wo::VarObj condition)
{
    ks_stdptr<IET_PivotTableTool> pivotTool;
    HRESULT hr = PivotHelpers::GetPivotTableTool(m_spWorkbook, &pivotTool);
    if (FAILED(hr))
        return hr;
    
    {
        hr = S_OK;

        std::set<UINT> valuesSet;
        binary_wo::VarObj valsObj = condition.get("values");
        if (valsObj.arrayLength() == 0)
        {
            ASSERT(FALSE);
            return hr;
        }
        for (int i = 0, cnt = valsObj.arrayLength(); i < cnt; ++i)
        {
            binary_wo::VarObj item = valsObj.at(i);
            PCWSTR type = item.field_str("type");
            if (xstrcmp(type, __X("text")) == 0 || 
                (xstrcmp(type, __X("date")) == 0 && item.has("index")))
            {
                UINT index = item.field_uint32("index");
                valuesSet.insert(index);
            }
        }

        PivotTblBatchHelper hlp(pivotTool, m_spApiPivotTable, &hr);

        hr = resetFilter();
        if (FAILED(hr))
            return hr;

        SlicerCachesBatchHelper _batch(m_spPivotTable);
        switch (m_spFieldAxis->GetFieldType())
        {
            case pivot_core::AxisFieldRow:
            case pivot_core::AxisFieldCol:
                _filterValues(valuesSet, hlp, _batch);
                break;
            case pivot_core::AxisFieldPage:
                _filterPageValues(valuesSet, hlp, _batch);
                break;
			case pivot_core::AxisFieldNone:
				_filterNoneValues(valuesSet, hlp, _batch);
				break;
            default:
                hlp.ProcResult(E_FAIL);
                break;
        }
    }

    return hr;
}

HRESULT ExecPivotFilterHelper::_filterValues(const std::set<UINT> &valuesSet, PivotTblBatchHelper &hlp, SlicerCachesBatchHelper &_batch)
{
    pivot_core::ITblFieldAxisItems *pItems = m_spFieldAxis->GetFieldItems();
    if (!pItems)
        return E_FAIL;
    int nItems = pItems->Count();

    for (int i = 0; i < nItems; i++)
    {
        BOOL bValue = FALSE;
        if (valuesSet.count(i) != 0)
            bValue = TRUE;
        ks_stdptr<pivot_core::ITblFieldAxisItem> spItem = pItems->Item(pivot_core::IDX_ITEM(i));
        if (spItem == NULL || !spItem->IsValid() ||
            (spItem->IsMissing() && m_spFieldAxis->GetFieldType() == pivot_core::AxisFieldPage
            && !m_spFieldAxis->QueryFieldSrc()->GetProperty(pivot_core::FPT_ShowNoDataItems)))
            continue;
        if (spItem->IsVisible() == bValue)
            continue;
        hlp.ProcResult(spItem->SetVisible(bValue));
    }
    _batch.ProcResult(hlp.GetResult());

    return hlp.GetResult();
}

HRESULT ExecPivotFilterHelper::_filterNoneValues(const std::set<UINT>& valuesSet, PivotTblBatchHelper& hlp, SlicerCachesBatchHelper& _batch)
{
	pivot_core::ITblFieldAxisItems* pItems = m_spFieldAxis->GetFieldItems();
	if (!pItems)
		return E_FAIL;
	int nItems = pItems->Count();
	for (int i = 0; i < nItems; i++)
	{
		BOOL bValue = FALSE;
		if (valuesSet.count(i) != 0)
			bValue = TRUE;
		ks_stdptr<pivot_core::ITblFieldAxisItem> spItem = pItems->Item(pivot_core::IDX_ITEM(i));
		if (spItem == NULL || !spItem->IsValid())
			continue;
		if (spItem->IsVisible() == bValue)
			continue;
		hlp.ProcResult(spItem->SetVisible(bValue));
	}
	_batch.ProcResult(hlp.GetResult());

	return hlp.GetResult();
}

HRESULT ExecPivotFilterHelper::_filterPageValues(const std::set<UINT> &valuesSet, PivotTblBatchHelper &hlp, SlicerCachesBatchHelper &_batch)
{
    pivot_core::ITblFieldAxisItems *pItems = m_spFieldAxis->GetFieldItems();
    if (!pItems)
        return E_FAIL;
    int nItems = pItems->Count();

    bool bIsMultipleSelect = valuesSet.size() > 1;
    VARIANT_BOOL vb;
    m_spApiPivotField->get_EnableMultiplePageItems(&vb);
    bool b = VarBool_BOOL(vb);

    if (bIsMultipleSelect)
    {
        KComVariant allPage(TX_PIVOT_PAGE_ITEM_ALL);
        HRESULT hr = m_spApiPivotField->put_CurrentPage(allPage);
        hlp.ProcResult(hr);

        for (int i = 0; i < nItems; ++i)
        {
            BOOL bValue = FALSE;
            if (valuesSet.count(i) != 0)
                bValue = TRUE;
            ks_stdptr<pivot_core::ITblFieldAxisItem> spItem = pItems->Item(pivot_core::IDX_ITEM(i));
            if (spItem == NULL || spItem->IsVisible() == bValue)
                continue;
            hlp.ProcResult(spItem->SetVisible(bValue));
        }
        if (bIsMultipleSelect != b)
        {
            HRESULT hr = m_spApiPivotField->put_EnableMultiplePageItems(BOOL_VarBool(bIsMultipleSelect));
            hlp.ProcResult(hr);
        }
    }
    else
    {
        for (int i = 0; i < nItems; ++i)
        {
            BOOL bValue = TRUE;
            ks_stdptr<pivot_core::ITblFieldAxisItem> spItem = pItems->Item(pivot_core::IDX_ITEM(i));
            if (spItem == NULL || spItem->IsVisible() == bValue)
                continue;
            hlp.ProcResult(spItem->SetVisible(bValue));
        }
        ks_stdptr<pivot_core::ITblFieldAxisItem> spItem = pItems->Item(pivot_core::IDX_ITEM(*(valuesSet.begin())));
        ASSERT(spItem);
        KComVariant page(spItem->GetName().c_str());
        hlp.ProcResult(m_spApiPivotField->put_CurrentPage(page));
        if (bIsMultipleSelect != b)
        {
            HRESULT hr = m_spApiPivotField->put_EnableMultiplePageItems(BOOL_VarBool(bIsMultipleSelect));
            hlp.ProcResult(hr);
        }
    }
    _batch.ProcResult(hlp.GetResult());

    return hlp.GetResult();
}

HRESULT ExecPivotFilterHelper::FilterCustom(binary_wo::VarObj condition)
{
    ks_stdptr<IET_PivotTableTool> pivotTool;
    HRESULT hr = PivotHelpers::GetPivotTableTool(m_spWorkbook, &pivotTool);
    if (FAILED(hr))
        return hr;

    {
        hr = S_OK;
        PivotTblBatchHelper hlp(pivotTool, m_spApiPivotTable, &hr);

        hr = resetFilter();
        if (FAILED(hr))
            return hr;

        binary_wo::VarObj varParam1 = condition.get_s("param1");
        if (varParam1.type() == binary_wo::typeInvalid)
        {   // 日期--动态筛选
            if (!condition.has("dynamicCriteria"))
                return E_FAIL;

            WebStr filterType = __X("date");
            WebStr customType = condition.field_str("dynamicCriteria");
            pivot_core::FilterOperatorType fot = getFOTFromStr(filterType, customType);
            oldapi::XlPivotFilterType xltype = covertToAPIType(fot);
            hlp.ProcResult(m_spApiPivotFilters->Add2(xltype, VARIANT(), VARIANT(), VARIANT(), VARIANT(), VARIANT(), VARIANT(), VARIANT(), VARIANT(), NULL));
            return hr;
        }

        if (!varParam1.has("customType"))
            return E_FAIL;
        WebStr customType = varParam1.field_str("customType");
        WebStr filterType = __X("caption");
        if (varParam1.has("filterType"))
            filterType = varParam1.field_str("filterType");
        pivot_core::FilterOperatorType fot = getFOTFromStr(filterType, customType);
        bool bVauleFilter = pivot_core::FilterOperatorHelp::IsValue(fot);
        if (bVauleFilter)
        {   // 值筛选
            if (!condition.has("dataField"))
                return E_FAIL;

            WebStr dataField = condition.field_str("dataField");
            hr = getApiPivotDataField(dataField);
            if (FAILED(hr))
                return E_FAIL;
        }

        oldapi::XlPivotFilterType xltype = covertToAPIType(fot);
        WebStr val = varParam1.field_str("val");
        KComVariant v1, v2, vbWholeDay;
        double dbV1, dbV2;
        bool bOk = false;
        if (bVauleFilter)
        {
            dbV1 = krt::fromUtf16(val).toDouble(&bOk);
            if (!bOk)
                return E_PIVOTTABLE_FILTER_VALUE_DOUBLE_ERROR;
            v1.AssignDouble(dbV1);
        }
        else
        {
            v1.Assign(val, xstrlen(val));
        }
        if (pivot_core::FilterOperatorHelp::IsCaptionDateBinary(fot) || pivot_core::FilterOperatorHelp::IsCaptionDateUnary(fot))
            vbWholeDay.AssignBOOL(alg::bool2BOOL(varParam1.has("bWholeDay") ? varParam1.field_bool("bWholeDay") : false));
        if (pivot_core::FilterOperatorHelp::IsCaptionStrBinary(fot) ||
            pivot_core::FilterOperatorHelp::IsCaptionDateBinary(fot) || 
            pivot_core::FilterOperatorHelp::IsValueBinary(fot))
        {
            binary_wo::VarObj varParam2 = condition.get_s("param2");
            if (varParam2.type() == binary_wo::typeInvalid)
                return E_FAIL;
            WebStr val2 = varParam2.field_str("val");
            if (bVauleFilter)
            {
                dbV2 = krt::fromUtf16(val2).toDouble(&bOk);
                if (!bOk)
                    return E_PIVOTTABLE_FILTER_VALUE_DOUBLE_ERROR;
                else if (dbV2 < dbV1)
                    return E_PIVOTTABLE_FILTER_VALUE_ENDLESSTHANBEGIN;
                v2.AssignDouble(dbV2);
            }
            else
            {
                v2.Assign(val2, xstrlen(val2));
            }
        }
        hlp.ProcResult(m_spApiPivotFilters->Add2(xltype, KComVariant(m_spApiPivotDataField), v1, v2, VARIANT(), VARIANT(), VARIANT(), VARIANT(), vbWholeDay, NULL));
    }

    return hr;
}

HRESULT ExecPivotFilterHelper::FilterTopN(binary_wo::VarObj condition)
{
    if (!condition.has("dataField"))
        return E_INVALIDARG;

    WebStr dataField = condition.field_str("dataField");
    HRESULT hr = getApiPivotDataField(dataField);
    if (FAILED(hr))
        return E_FAIL;

    ks_stdptr<IET_PivotTableTool> pivotTool;
    hr = PivotHelpers::GetPivotTableTool(m_spWorkbook, &pivotTool);
    if (FAILED(hr))
        return hr;
    
    {
        hr = S_OK;
        PivotTblBatchHelper hlp(pivotTool, m_spApiPivotTable, &hr);

        hr = resetFilter();
        if (FAILED(hr))
            return hr;
        
        bool isTop = true;
        pivot_core::FilterOperatorType fot = getTopFOTFromStr(condition.field_str("operator"), isTop);

        WebStr th = condition.field_str("threshold");
        KComVariant v1;
        bool bOk = false;
        double dbThreshold = krt::fromUtf16(th).toDouble(&bOk);
        if (!bOk || dbThreshold < 0.0)
            return E_PIVOTTABLE_FILTER_VALUE_DOUBLE_ERROR;
        v1.AssignDouble(dbThreshold);
        oldapi::XlPivotFilterType xlType = covertTopType(fot, isTop);

        hlp.ProcResult(m_spApiPivotFilters->Add2(xlType, KComVariant(m_spApiPivotDataField), v1, VARIANT(), VARIANT(), VARIANT(), VARIANT(), VARIANT(), VARIANT(), NULL));
    }

    return hr;
}

oldapi::XlPivotFilterType ExecPivotFilterHelper::covertToAPIType(pivot_core::FilterOperatorType type)
{
    switch (type)
    {
        case pivot_core::FO_CaptionEqual:
            return xlCaptionEquals;
        case pivot_core::FO_CaptionNotEqual:
            return xlCaptionDoesNotEqual;
        case pivot_core::FO_CaptionGreaterThan:
            return xlCaptionIsGreaterThan;
        case pivot_core::FO_CaptionGreaterThanOrEqual:
            return xlCaptionIsGreaterThanOrEqualTo;
        case pivot_core::FO_CaptionLessThan:
            return xlCaptionIsLessThan;
        case pivot_core::FO_CaptionLessThanOrEqual:
            return xlCaptionIsLessThanOrEqualTo;
        case pivot_core::FO_CaptionBeginsWith:
            return xlCaptionBeginsWith;
        case pivot_core::FO_CaptionNotBeginsWith:
            return xlCaptionDoesNotBeginWith;
        case pivot_core::FO_CaptionEndsWith:
            return xlCaptionEndsWith;
        case pivot_core::FO_CaptionNotEndsWith:
            return xlCaptionDoesNotEndWith;
        case pivot_core::FO_CaptionContains:
            return xlCaptionContains;
        case pivot_core::FO_CaptionNotContains:
            return xlCaptionDoesNotContain;
        case pivot_core::FO_CaptionBetween:
            return xlCaptionIsBetween;
        case pivot_core::FO_CaptionNotBetween:
            return xlCaptionIsNotBetween;

        case pivot_core::FO_DateEqual:
            return xlSpecificDate;
        case pivot_core::FO_DateNotEqual:
            return xlNotSpecificDate;
        case pivot_core::FO_DateNewerThan:
            return xlAfter;
        case pivot_core::FO_DateNewerThanOrEqual:
            return xlAfterOrEqualTo;
        case pivot_core::FO_DateOlderThan:
            return xlBefore;
        case pivot_core::FO_DateOlderThanOrEqual:
            return xlBeforeOrEqualTo;
        case pivot_core::FO_DateBetween:
            return xlDateBetween;
        case pivot_core::FO_DateNotBetween:
            return xlDateNotBetween;
        case pivot_core::FO_DateTomorrow:
            return xlDateTomorrow;
        case pivot_core::FO_DateToday:
            return xlDateToday;
        case pivot_core::FO_DateYesterday:
            return xlDateYesterday;
        case pivot_core::FO_DateWeekNext:
            return xlDateNextWeek;
        case pivot_core::FO_DateWeekThis:
            return xlDateThisWeek;
        case pivot_core::FO_DateWeekLast:
            return xlDateLastWeek;
        case pivot_core::FO_DateMonthNext:
            return xlDateNextMonth;
        case pivot_core::FO_DateMonthThis:
            return xlDateThisMonth;
        case pivot_core::FO_DateMonthLast:
            return xlDateLastMonth;
        case pivot_core::FO_DateQuarterNext:
            return xlDateNextQuarter;
        case pivot_core::FO_DateQuarterThis:
            return xlDateThisQuarter;
        case pivot_core::FO_DateQuarterLast:
            return xlDateLastQuarter;
        case pivot_core::FO_DateYearNext:
            return xlDateNextYear;
        case pivot_core::FO_DateYearThis:
            return xlDateThisYear;
        case pivot_core::FO_DateYearLast:
            return xlDateLastYear;
        case pivot_core::FO_DateYearToDate:
            return xlYearToDate;
        case pivot_core::FO_DateQ1:
            return xlAllDatesInPeriodQuarter1;
        case pivot_core::FO_DateQ2:
            return xlAllDatesInPeriodQuarter2;
        case pivot_core::FO_DateQ3:
            return xlAllDatesInPeriodQuarter3;
        case pivot_core::FO_DateQ4:
            return xlAllDatesInPeriodQuarter4;
        case pivot_core::FO_DateM1:
            return xlAllDatesInPeriodJanuary;
        case pivot_core::FO_DateM2:
            return xlAllDatesInPeriodFebruary;
        case pivot_core::FO_DateM3:
            return xlAllDatesInPeriodMarch;
        case pivot_core::FO_DateM4:
            return xlAllDatesInPeriodApril;
        case pivot_core::FO_DateM5:
            return xlAllDatesInPeriodMay;
        case pivot_core::FO_DateM6:
            return xlAllDatesInPeriodJune;
        case pivot_core::FO_DateM7:
            return xlAllDatesInPeriodJuly;
        case pivot_core::FO_DateM8:
            return xlAllDatesInPeriodAugust;
        case pivot_core::FO_DateM9:
            return xlAllDatesInPeriodSeptember;
        case pivot_core::FO_DateM10:
            return xlAllDatesInPeriodOctober;
        case pivot_core::FO_DateM11:
            return xlAllDatesInPeriodNovember;
        case pivot_core::FO_DateM12:
            return xlAllDatesInPeriodDecember;

        case pivot_core::FO_ValueEqual:
            return oldapi::xlValueEquals;
        case pivot_core::FO_ValueNotEqual:
            return oldapi::xlValueDoesNotEqual;
        case pivot_core::FO_ValueGreaterThan:
            return oldapi::xlValueIsGreaterThan;
        case pivot_core::FO_ValueGreaterThanOrEqual:
            return oldapi::xlValueIsGreaterThanOrEqualTo;
        case pivot_core::FO_ValueLessThan:
            return oldapi::xlValueIsLessThan;
        case pivot_core::FO_ValueLessThanOrEqual:
            return oldapi::xlValueIsLessThanOrEqualTo;
        case pivot_core::FO_ValueBetween:
            return oldapi::xlValueIsBetween;
        case pivot_core::FO_ValueNotBetween:
            return oldapi::xlValueIsNotBetween;
        default:
            return xlInvalibItem;;
    }
}

oldapi::XlPivotFilterType ExecPivotFilterHelper::covertTopType(pivot_core::FilterOperatorType type, bool isTop)
{
    switch (type)
    {
    case pivot_core::FO_TopNCount:
        return isTop ? xlTopCount : xlBottomCount;
    case pivot_core::FO_TopNPercent:
        return isTop ? xlTopPercent : xlBottomPercent;
    case pivot_core::FO_TopNSum:
        return isTop ? xlTopSum : xlBottomSum;
    default:
        ASSERT(FALSE);
        return xlTopCount;
    }
}

pivot_core::FilterOperatorType ExecPivotFilterHelper::getFOTFromStr(WebStr filterType, WebStr str)
{
    enum FilterType
    {
        FilterCaption,
        FilterDate,
        FilterValue,
    };

    FilterType ft = FilterCaption;
    if (xstrcmp(filterType, __X("caption")) == 0)
        ft = FilterCaption;
    else if (xstrcmp(filterType, __X("date")) == 0)
        ft = FilterDate;
    else if (xstrcmp(filterType, __X("value")) == 0)
        ft = FilterValue;	

    if (xstrcmp(str, __X("null")) == 0)
        return pivot_core::FO_Unknown;
    switch (ft)
    {
        case FilterCaption:
            if (xstrcmp(str, __X("equals")) == 0)
                return pivot_core::FO_CaptionEqual;
            else if (xstrcmp(str, __X("notEqu")) == 0)
                return pivot_core::FO_CaptionNotEqual;
            else if (xstrcmp(str, __X("greater")) == 0)
                return pivot_core::FO_CaptionGreaterThan;
            else if (xstrcmp(str, __X("greaterEqu")) == 0)
                return pivot_core::FO_CaptionGreaterThanOrEqual;
            else if (xstrcmp(str, __X("less")) == 0)
                return pivot_core::FO_CaptionLessThan;
            else if (xstrcmp(str, __X("lessEqu")) == 0)
                return pivot_core::FO_CaptionLessThanOrEqual;
            else if (xstrcmp(str, __X("beginWith")) == 0)
                return pivot_core::FO_CaptionBeginsWith;
            else if (xstrcmp(str, __X("notBeginWith")) == 0)
                return pivot_core::FO_CaptionNotBeginsWith;
            else if (xstrcmp(str, __X("endWith")) == 0)
                return pivot_core::FO_CaptionEndsWith;
            else if (xstrcmp(str, __X("notEndWith")) == 0)
                return pivot_core::FO_CaptionNotEndsWith;
            else if (xstrcmp(str, __X("contains")) == 0)
                return pivot_core::FO_CaptionContains;
            else if (xstrcmp(str, __X("notContains")) == 0)
                return pivot_core::FO_CaptionNotContains;
            else if (xstrcmp(str, __X("between")) == 0)
                return pivot_core::FO_CaptionBetween;
            else if (xstrcmp(str, __X("notBetween")) == 0)
                return pivot_core::FO_CaptionNotBetween;
        case FilterDate:
            if (xstrcmp(str, __X("equals")) == 0)
                return pivot_core::FO_DateEqual;
            else if (xstrcmp(str, __X("notEqu")) == 0)
                return pivot_core::FO_DateNotEqual;
            else if (xstrcmp(str, __X("greater")) == 0)
                return pivot_core::FO_DateNewerThan;
            else if (xstrcmp(str, __X("greaterEqu")) == 0)
                return pivot_core::FO_DateNewerThanOrEqual;
            else if (xstrcmp(str, __X("less")) == 0)
                return pivot_core::FO_DateOlderThan;
            else if (xstrcmp(str, __X("lessEqu")) == 0)
                return pivot_core::FO_DateOlderThanOrEqual;
            else if (xstrcmp(str, __X("between")) == 0)
                return pivot_core::FO_DateBetween;
            else if (xstrcmp(str, __X("notBetween")) == 0)
                return pivot_core::FO_DateNotBetween;
            else if (xstrcmp(str, __X("yesterday")) == 0)
                return pivot_core::FO_DateYesterday;
            else if (xstrcmp(str, __X("today")) == 0)
                return pivot_core::FO_DateToday;
            else if (xstrcmp(str, __X("tomorrow")) == 0)
                return pivot_core::FO_DateTomorrow;
            else if (xstrcmp(str, __X("lastMonth")) == 0)
                return pivot_core::FO_DateMonthLast;
            else if (xstrcmp(str, __X("thisMonth")) == 0)
                return pivot_core::FO_DateMonthThis;
            else if (xstrcmp(str, __X("nextMonth")) == 0)
                return pivot_core::FO_DateMonthNext;
            else if (xstrcmp(str, __X("lastWeek")) == 0)
                return pivot_core::FO_DateWeekLast;
            else if (xstrcmp(str, __X("thisWeek")) == 0)
                return pivot_core::FO_DateWeekThis;
            else if (xstrcmp(str, __X("nextWeek")) == 0)
                return pivot_core::FO_DateWeekNext;
            else if (xstrcmp(str, __X("lastQuarter")) == 0)
                return pivot_core::FO_DateQuarterLast;
            else if (xstrcmp(str, __X("thisQuarter")) == 0)
                return pivot_core::FO_DateQuarterThis;
            else if (xstrcmp(str, __X("nextQuarter")) == 0)
                return pivot_core::FO_DateQuarterNext;
            else if (xstrcmp(str, __X("lastYear")) == 0)
                return pivot_core::FO_DateYearLast;
            else if (xstrcmp(str, __X("thisYear")) == 0)
                return pivot_core::FO_DateYearThis;
            else if (xstrcmp(str, __X("nextYear")) == 0)
                return pivot_core::FO_DateYearNext;
            else if (xstrcmp(str, __X("yearToDate")) == 0)
                return pivot_core::FO_DateYearToDate;
            else if (xstrcmp(str, __X("allDatesInPeriodJanuary")) == 0)
                return pivot_core::FO_DateM1;
            else if (xstrcmp(str, __X("allDatesInPeriodFebruray")) == 0)
                return pivot_core::FO_DateM2;
            else if (xstrcmp(str, __X("allDatesInPeriodMarch")) == 0)
                return pivot_core::FO_DateM3;
            else if (xstrcmp(str, __X("allDatesInPeriodApril")) == 0)
                return pivot_core::FO_DateM4;
            else if (xstrcmp(str, __X("allDatesInPeriodMay")) == 0)
                return pivot_core::FO_DateM5;
            else if (xstrcmp(str, __X("allDatesInPeriodJune")) == 0)
                return pivot_core::FO_DateM6;
            else if (xstrcmp(str, __X("allDatesInPeriodJuly")) == 0)
                return pivot_core::FO_DateM7;
            else if (xstrcmp(str, __X("allDatesInPeriodAugust")) == 0)
                return pivot_core::FO_DateM8;
            else if (xstrcmp(str, __X("allDatesInPeriodSeptember")) == 0)
                return pivot_core::FO_DateM9;
            else if (xstrcmp(str, __X("allDatesInPeriodOctober")) == 0)
                return pivot_core::FO_DateM10;
            else if (xstrcmp(str, __X("allDatesInPeriodNovember")) == 0)
                return pivot_core::FO_DateM11;
            else if (xstrcmp(str, __X("allDatesInPeriodDecember")) == 0)
                return pivot_core::FO_DateM12;
            else if (xstrcmp(str, __X("allDatesInPeriodQuarter1")) == 0)
                return pivot_core::FO_DateQ1;
            else if (xstrcmp(str, __X("allDatesInPeriodQuarter2")) == 0)
                return pivot_core::FO_DateQ2;
            else if (xstrcmp(str, __X("allDatesInPeriodQuarter3")) == 0)
                return pivot_core::FO_DateQ3;
            else if (xstrcmp(str, __X("allDatesInPeriodQuarter4")) == 0)
                return pivot_core::FO_DateQ4;
        case FilterValue:
            if (xstrcmp(str, __X("equals")) == 0)
                return pivot_core::FO_ValueEqual;
            else if (xstrcmp(str, __X("notEqu")) == 0)
                return pivot_core::FO_ValueNotEqual;
            else if (xstrcmp(str, __X("greater")) == 0)
                return pivot_core::FO_ValueGreaterThan;
            else if (xstrcmp(str, __X("greaterEqu")) == 0)
                return pivot_core::FO_ValueGreaterThanOrEqual;
            else if (xstrcmp(str, __X("less")) == 0)
                return pivot_core::FO_ValueLessThan;
            else if (xstrcmp(str, __X("lessEqu")) == 0)
                return pivot_core::FO_ValueLessThanOrEqual;
            else if (xstrcmp(str, __X("between")) == 0)
                return pivot_core::FO_ValueBetween;
            else if (xstrcmp(str, __X("notBetween")) == 0)
                return pivot_core::FO_ValueNotBetween;
        default:
            ASSERT(FALSE);
            return pivot_core::FO_Unknown;
    }
    
}

pivot_core::FilterOperatorType ExecPivotFilterHelper::getTopFOTFromStr(WebStr str, bool &isTop)
{
    if (xstrcmp(str, __X("top10Items")) == 0)
    {
        isTop = true;
        return pivot_core::FO_TopNCount;
    }
    else if (xstrcmp(str, __X("top10Percent")) == 0)
    {
        isTop = true;
        return pivot_core::FO_TopNPercent;
    }
    else if (xstrcmp(str, __X("top10Sum")) == 0)
    {
        isTop = true;
        return pivot_core::FO_TopNSum;
    }
    else if (xstrcmp(str, __X("bottom10Items")) == 0)
    {
        isTop = false;
        return pivot_core::FO_TopNCount;
    }
    else if (xstrcmp(str, __X("bottom10Percent")) == 0)
    {
        isTop = false;
        return pivot_core::FO_TopNPercent;
    }
    else if (xstrcmp(str, __X("bottom10Sum")) == 0)
    {
        isTop = false;
        return pivot_core::FO_TopNSum;
    }
    else
    {
        ASSERT(FALSE);
        isTop = true;
        return pivot_core::FO_TopNCount;
    }
}

SetPivotTableTextHelper::SetPivotTableTextHelper(etoldapi::Range *pCellRange, etoldapi::Range *pRange)
    : m_spCellRange(pCellRange)
    , m_spRange(pRange)
    , m_spRangeInfo(pRange)
{}

HRESULT SetPivotTableTextHelper::SetText(PCWSTR text)
{
    ks_stdptr<pivot_core::IPivotTable> spCorePvtTbl;
    BOOL bHasPvtTbl = pivot_helper::GetCorePivotTableByCell(m_spCellRange, &spCorePvtTbl);
    if (!bHasPvtTbl) return S_CONTINUE;

    HRESULT hr = pivot_helper::IsAllowedEditWithPivotTable(m_spCellRange);
    if (FAILED(hr))
        return hr;
    CELL cell = { 0 };
    app_helper::GetLTCell(m_spCellRange, cell);
    pivot_core::CellType ct = spCorePvtTbl->GetCellType(cell);
    if (pivot_core::CellValue == ct)
    {
        range_helper::ranges rgs;
        GetRanges(m_spRange, &rgs);
        RECT rcBody = { -1 };
        VS(spCorePvtTbl->GetBodyRect(&rcBody));
        RECT* arrPageRect = NULL;
        UINT nPageCnt = spCorePvtTbl->GetPageRectCount();
        for (int i = 0; i < rgs.size(); ++i)
        {
            RECT rc = RANGE2RECT(*rgs.at(i).second);
            if (IsContained(rcBody, rc)) continue;

            if (nPageCnt > 0)
            {
                if (NULL == arrPageRect)
                {
                    arrPageRect = new RECT[nPageCnt];
                    nPageCnt = spCorePvtTbl->GetPageRect(arrPageRect, nPageCnt);
                }
                ASSERT(nPageCnt > 0);
                BOOL bContained = FALSE;
                for (UINT j = 0; j < nPageCnt; ++j)
                {
                    if (IsContained(arrPageRect[j], rc))
                    {
                        bContained = TRUE;
                        break;
                    }
                }
                if (bContained) break;
            }

            if (arrPageRect) delete[] arrPageRect;
            return E_PIVOTTABLE_DENIED_EDIT_THIS_PART;
        }
        if (arrPageRect) { delete[] arrPageRect; arrPageRect = NULL; }

        hr = SetPivotTableFmlaText(spCorePvtTbl, cell, text);
    }
    else
    {
        ks_stdptr<_Worksheet> spWorksheet;
        m_spRange->get_Worksheet(&spWorksheet);
        hr = pivot_helper::SetPivotTableCellValue(spWorksheet, spCorePvtTbl, cell, text);
    }
    return hr;
}

HRESULT SetPivotTableTextHelper::SetPivotTableFmlaText(pivot_core::IPivotTable* pvtTbl, CELL& cell, LPCWSTR pwszText)
{
    ks_wstring wstr(pwszText);

    pivot_core::PIVOT_ITEMS items;
    HRESULT hr = CheckPivotTableFormula(pvtTbl, wstr, items);
    if (SUCCEEDED(hr))
    {
        ks_bstr bstrRef;
        hr = pivot_core::PivotDecompileItems(pvtTbl, items, &bstrRef);
        if (SUCCEEDED(hr) && !bstrRef.empty())
        {
            if (wstr.empty())
                wstr = __X(" =");
            else if (wstr.at(0) != __Xc('='))
                wstr.insert(0, __X(" = "));
            wstr.insert(0, bstrRef.c_str());
            hr = PutPivotTableFormula(pvtTbl, wstr);
        }
    }
    return hr;
}

HRESULT SetPivotTableTextHelper::PutPivotTableFormula(pivot_core::IPivotTable* pCorePvtTbl, ks_wstring& wstr)
{
    ks_stdptr<etoldapi::_Worksheet> spWorksheet;
    HRESULT hr = m_spRange->get_Worksheet(&spWorksheet);
    if (FAILED(hr))
        return hr;
    ks_stdptr<etoldapi::PivotTable> spPivotTable;
    hr = spWorksheet->GainPivotTable(pCorePvtTbl, &spPivotTable);
    if (spPivotTable)
    {
        ks_stdptr<etoldapi::PivotFormulas> spPivotFormulas;
        spPivotTable->get_PivotFormulas(&spPivotFormulas);
        ks_bstr bstrFmla(wstr.c_str());
        VARIANT var; V_VT(&var) = VT_BOOL; V_BOOL(&var) = VARIANT_TRUE;
        ks_stdptr<etoldapi::PivotFormula> spPivotFmla;
        hr = spPivotFormulas->Add(bstrFmla, var, &spPivotFmla);
    }
    return hr;
}

HRESULT SetPivotTableTextHelper::CheckPivotTableFormula(pivot_core::IPivotTable* pCorePvtTbl, IN OUT ks_wstring& str, OUT pivot_core::PIVOT_ITEMS& items)
{
    typedef std::vector<pivot_core::PivotDimension> PivotDimVec;
    ASSERT(pCorePvtTbl);

    ks_stdptr<IBookOp> spBookOp;
    LONG row, col;
    IDX iSheet = INVALIDIDX;
    GetActiveInfo(&spBookOp, iSheet, row, col);
    RECT rc;
    rc.top = rc.bottom = row;
    rc.left = rc.right = col;

    std::vector<pivot_core::DescAreaPtr> vecDesc;
    pCorePvtTbl->DescResolve(rc, vecDesc);
    if (vecDesc.size() != 1) return E_FAIL;

    const pivot_core::IDescNormal* pNormlDesc = vecDesc[0]->QueryNormal();
    if (!pNormlDesc || vecDesc[0]->GetAffectedType() != pivot_core::aa_data)
        return E_FAIL;

    const PivotDimVec& dims = pNormlDesc->GetDimRef();
    pivot_core::ConvertPivotDimension(pCorePvtTbl, dims, items);
    if (items.empty()) return E_FAIL;

    pivot_helper::UpdateDimItemsToResolveFmla(pCorePvtTbl, items);

    // 常量识别一下
    ks_stdptr<IFormula> spFormula;
    VS(spBookOp->CreateFormula(&spFormula));
    COMPILE_RESULT cr = { GENERAL_ERROR, 0 };
    VS(spFormula->SetFormula(str.c_str(),
        CS_COMPILE_PARAM(cpfNormal, iSheet, row, col), &cr));
    BOOL bIsFormula = TRUE;
    const_token_ptr pToken = nullptr;
    spFormula->GetContent(&bIsFormula, NULL, &pToken);
    if (pToken && !bIsFormula && COMPILE_SUCCESS == cr.nErrCode)
    {
        auto num2str = [](double dbl, ks_wstring& s)->bool {
            int len = 0; std::vector<WCHAR> vv(128, 0);
            kfc::nf::_ftot(dbl, 15, &vv[0],
                (int)vv.size(), false, true, 0, &len, false);
            if (len > 0) {
                s.assign(&vv[0], len);
                return true;
            }
            return false;
        };

        DWORD dw = GetExecTokenMajorType(pToken);
        if (alg::ETP_VINT == dw)
        {
            int v = alg::const_vint_token_assist(pToken).get_value();
            if (num2str(v, str)) return S_OK;
        }
        else if (alg::ETP_VDBL == dw)
        {
            double v = alg::const_vdbl_token_assist(pToken).get_value();
            if (num2str(v, str)) return S_OK;
        }
        else if (alg::ETP_ERROR == dw)
        {
            str = alg::TokenToTextI(pToken);
            return S_OK;
        }
        return E_PIVOTTABLE_DENIED_INPUT_TEXT_WITH_CALCITEM;
    }

    // 透视表公式
    pivot_core::PIVOT_COMPILE_PARAM param;
    param.flags = pivot_core::pcfCalcItem;
    param.table = pCorePvtTbl;
    param.items = &items;
    ks_stdptr<pivot_core::IPivotFmla> spFmla;
    pivot_core::PIVOT_COMPILE_RESULT pcr = pivot_core::PivotCompileFormula(
        str.c_str(), param, &spFmla);
    return pivot_core::PivotCompileResultToHResult(pcr);
}

BOOL SetPivotTableTextHelper::GetActiveInfo(IBookOp** ppBookOp, IDX& iSheet, LONG& row, LONG& col)
{
    ks_stdptr<IRangeInfo> ptrRangeInfo = m_spCellRange;

    if (ptrRangeInfo)
    {
        ks_stdptr<IKWorksheet> ptrWorksheetInfo;
        ks_stdptr<IKWorkbook>  ptrWorkbookInfo;
        ptrRangeInfo->GetWorkbookInfo(&ptrWorkbookInfo);
        if (ptrWorkbookInfo && ppBookOp)
        {
            ks_stdptr<IBook>   ptrBook = ptrWorkbookInfo->GetBook();
            ptrBook->GetOperator(ppBookOp);
        }

        ptrRangeInfo->GetWorksheetInfo(&ptrWorksheetInfo);
        if (ptrWorksheetInfo)
        {
            ks_stdptr<ISheet>  ptrSheet = ptrWorksheetInfo->GetSheet();
            ptrSheet->GetIndex(&iSheet);
        }
    }

    // 1 base  > 0 base
    m_spRange->get_Row(&row);
    --row;
    m_spRange->get_Column(&col);
    --col;

    return TRUE;
}

RECT SetPivotTableTextHelper::RANGE2RECT(const RANGE& rg)
{
    RECT rc = { -1 };
    rc.left = rg.ColFrom();
    rc.right = rg.ColTo();
    rc.top = rg.RowFrom();
    rc.bottom = rg.RowTo();
    return rc;
}

BOOL SetPivotTableTextHelper::IsContained(const RECT& rhs, const RECT& lhs)
{
    return rhs.left <= lhs.left
        && rhs.right >= lhs.right
        && rhs.top <= lhs.top
        && rhs.bottom >= lhs.bottom;
}

void SetPivotTableTextHelper::GetRanges(Range* pRange, IKRanges** ppv)
{
    if (pRange == NULL)
        return;
    ks_stdptr<IRangeInfo> ptrRangeInfo = pRange;
    ks_stdptr<IKRanges> ptrRanges;
    ptrRangeInfo->GetIRanges(&ptrRanges);
    if (ptrRanges)
    {
        *ppv = ptrRanges;
        (*ppv)->AddRef();
    }
}

class PivotTblBatchHelper
{
public:
    PivotTblBatchHelper(IET_PivotTableTool* tool,
        etoldapi::PivotTable* tbl,
        HRESULT* res = NULL,
        BOOL bReportError = TRUE)
        : m_spPTTool(tool),
        m_hr(res),
        m_handle(NULL),
        m_bReportError(bReportError)
    {
        ASSERT(m_spPTTool != NULL);
        m_handle = m_spPTTool->BatchPivotTableBegin(tbl);
    }

    ~PivotTblBatchHelper()
    {
        HRESULT hr = m_spPTTool->BatchPivotTableEnd(m_handle, m_bReportError);
        if (m_hr != NULL)
            (*m_hr) = hr;
    }

    void ProcResult(HRESULT hr)
    {
        return m_spPTTool->BatchPivotTableProcResult(m_handle, hr);
    }

    HRESULT GetResult()
    {
        return m_spPTTool->BatchPivotTableGetResult(m_handle);
    }

protected:
    ks_stdptr<IET_PivotTableTool> m_spPTTool;
    BOOL m_bReportError;
    HANDLE m_handle;
    HRESULT* m_hr;
};

CreatePivotTableHelper::CreatePivotTableHelper(ETReferenceStyle refStyle, etoldapi::_Workbook* pWorkbook, etoldapi::_Worksheet* pWorkSheet)
    : m_pWorkbook(pWorkbook),
    m_spWorksheet(pWorkSheet),
    m_spWorksheets(pWorkbook->GetWorksheets())
{
    m_bRCStyle = refStyle == RS_R1C1;
    PivotHelpers::GetPivotTableTool(pWorkbook, &m_spPTTool);
}

HRESULT CreatePivotTableHelper::createPivotTable(
    const CreatePivotTableOptions& options, 
    etoldapi::_Worksheet** pDestWorkSheet, 
    pivot_core::IPivotTable** pCorePivotTable, 
    etoldapi::PivotTable** pPivotTable)
{
    if (!m_spPTTool)
    {
        return E_FAIL;
    }

    m_isNewSheet = options.isNewSheet;
    m_pivotChart = options.pivotChart;
    m_activeSheetIdx = options.activeSheetIdx;
    if (m_spWorksheet->GetSheet()->IsDbSheet())
    {
        ks_bstr sheetName;
        m_spWorksheet->get_Name(&sheetName);
        m_sourceDataText = QString::fromUtf16(sheetName.c_str());
    }
    else
        m_sourceDataText = QString::fromUtf16(options.sourceDataText);
    m_destRangeText = QString::fromUtf16(options.destRangeText);

    HRESULT hr = S_OK;
    ks_bstr bstrDest;
    ks_bstr bstrFailInfo;
    ks_stdptr<_Workbook> spWorkbook;
    ks_stdptr<PivotCache> spPivotCache;

    PivotTblBatchHelper hlp(m_spPTTool, NULL);
    hr = createPivotCache(&spWorkbook, &bstrDest, &spPivotCache, &bstrFailInfo);
    if (hr == S_OK)
    {
        ks_stdptr<PivotTable> spPivotTable;
        ks_stdptr<pivot_core::IPivotTable> pCoreTbl;
        hr = createPivotTableInner(spWorkbook, bstrDest, spPivotCache, &spPivotTable, pDestWorkSheet);
        if (SUCCEEDED(hr) && spPivotTable)
        {
            m_spPTTool->TableStackPush(spPivotTable);
            ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_spPTTool->GetWorksheet(spPivotTable);
            pCoreTbl = m_spPTTool->GetCoreTable(spPivotTable);

            if (!options.autoFitColumnWidth) 
            {
                spPivotTable->put_HasAutoFormat(VARIANT_FALSE);
            }
            
            if (options.styleId != -1) 
            {
                pCoreTbl->SetTableStyle(options.styleId);
            }

            CELL cellActive = { 0 };
            VS(pCoreTbl->GetAnchor(&cellActive));
            ASSERT(0 <= cellActive.row && 0 <= cellActive.col);
            ISheet* pSheet = pCoreTbl->GetHost()->QuerySheet();
            if (!pSheet)
            {
                ASSERT(0);
                return E_FAIL;
            }

            RANGE rg(pSheet->GetBMP());

            IDX shIDX = INVALIDIDX;
            pSheet->GetIndex(&shIDX);
            rg.SetCell(shIDX, cellActive.row, cellActive.col);

            //设置ActiveCell在数据透视表内
            ks_stdptr<IKWorksheet> ptrSheetInfo = spWorksheet;
            ASSERT(ptrSheetInfo != NULL);

            ks_stdptr<Range> ptrAcitveCellRg;
            ptrSheetInfo->GetRangeByData(&rg, &ptrAcitveCellRg);
            if (m_pivotChart && ptrAcitveCellRg)
            {
                ks_stdptr<Shapes> spShapes;
                hr = spWorksheet->get_Shapes(FALSE, &spShapes);
                if (spShapes)
                {
                    ks_stdptr<Shape> spShape;
                    KComVariant varChartType(ksoColumnClustered);
                    KComVariant varStyle(201);
                    KComVariant VAR_EMPTY;
                    hr = spShapes->AddPivotChart(varStyle, varChartType, ptrAcitveCellRg, &spShape);
                }
            }

            if (FAILED(hr))
                hlp.ProcResult(E_FAIL);
        }

        if (SUCCEEDED(hr))
        {
            if (pPivotTable)
                *pPivotTable = spPivotTable.detach();

            if (pCorePivotTable)
                *pCorePivotTable = pCoreTbl.detach();
        }
    }

    return hr;
}

HRESULT CreatePivotTableHelper::updateSourceByRange(pivot_core::IPivotTable* corePivotTable, etoldapi::PivotTable* pivotTable, PCWSTR strSrcRgText)
{
    if (!m_spPTTool)
    {
        return E_FAIL;
    }

    QString strSrcRg = QString::fromUtf16(strSrcRgText);
    {
        ks_stdptr<PivotCache> spPivotCache;
        pivotTable->PivotCache(&spPivotCache);
        if (!spPivotCache) return E_FAIL;

        KComVariant varData;
        spPivotCache->get_SourceData(&varData);
        KSmartParam smtData(varData);
        if (!smtData.IsStringType()) return E_FAIL;

        ks_bstr bstrSrc;
        m_spPTTool->ResolveCacheSourceString(
            smtData.GetStringValue(), TRUE, FALSE, m_bRCStyle, &bstrSrc);
        PCWSTR text = (!bstrSrc.empty() && bstrSrc[0] == __Xc('=')) ?
            bstrSrc.c_str() + 1 : bstrSrc.c_str();

        if (strSrcRg.compare(QString::fromUtf16(text), Qt::CaseInsensitive) == 0) return S_FALSE;
    }

    ks_bstr bstrSrc;
    HRESULT hr = m_spPTTool->ResolveCacheSourceString(krt::utf16(strSrcRg), m_bRCStyle, FALSE, TRUE, &bstrSrc);
    if (FAILED(hr)) return hr;

    ks_stdptr<PivotCache> spNewPivotCache;
    ks_stdptr<PivotTable> spNewPivotTable;
    hr = getPivotCache(m_pWorkbook, bstrSrc, etDatabase, xlPivotTableVersionCurrent, &spNewPivotCache, &spNewPivotTable);
    if (FAILED(hr)) return hr;
    if (!spNewPivotCache) return E_FAIL;

    if (spNewPivotTable) {
        ks_wstring strName;
        pivot_core::IPivotTable* tbl =
            m_spPTTool->GetCoreTable(spNewPivotTable);
        ISheet* sht = tbl->GetHost()->QuerySheet();
        if (!sht) {
            ASSERT(0);
            return E_FAIL;
        }

        IDX idxNew = INVALIDIDX, idxOld = INVALIDIDX;
        VS(sht->GetIndex(&idxNew));
        VS(ks_castptr<IKWorksheet>(m_spWorksheet)
            ->GetSheet()
            ->GetIndex(&idxOld));
        if (idxNew != idxOld) {
            const WCHAR* pcwsName = NULL;
            VS(sht->GetName(&pcwsName));
            strName = pcwsName;
            strName.append(__X("!"));
        }
        strName.append(tbl->GetName());

        KComVariant varName(strName.c_str(), strName.size());
        hr = pivotTable->ChangePivotCache(varName);
    }
    else if (spNewPivotCache) {
        KComVariant varCache((IUnknown*)spNewPivotCache);
        hr = pivotTable->ChangePivotCache(varCache);
    }

    return hr;
}

HRESULT CreatePivotTableHelper::getPlaceInfo(etoldapi::_Workbook** ppWorkbook, BOOL bRCStyle, BSTR* pbstr)
{
    ASSERT(ppWorkbook && pbstr);

    if (!m_isNewSheet)
    {
        QString strDestRg = m_destRangeText;
        if (strDestRg.isEmpty())
            return E_FAIL;

        ks_bstr bstrDestRg;
        HRESULT hr =
            m_spPTTool->ResolveCacheSourceString(krt::utf16(strDestRg), m_bRCStyle, TRUE, bRCStyle, &bstrDestRg);
        if (FAILED(hr))
            return hr;

        ks_stdptr<_Workbook> spWorkbook;
        hr = m_spPTTool->ResolveWorkBook(krt::utf16(strDestRg), m_bRCStyle, &spWorkbook);
        if (FAILED(hr))
            return hr;
        IKWorkbook* ws = ks_castptr<IKWorkbook>(spWorkbook);
        if (ws->GetBook()->GetShared())
            return E_PIVOTTABLE_DENIED_EDIT_BECAUSE_SHARED_WORKBOOK;

        (*ppWorkbook) = spWorkbook.detach();
        (*pbstr) = bstrDestRg.detach();
        return S_OK;
    }
    else
    {
        ks_castptr<IKWorksheet> cpWorksheet = m_spWorksheet;
        (*ppWorkbook) = ks_castptr<_Workbook>(cpWorksheet->GetWorkbook());
        (*ppWorkbook)->AddRef();
        (*pbstr) = NULL;
        return S_OK;
    }

    return E_FAIL;
}

HRESULT CreatePivotTableHelper::createPivotCache(etoldapi::_Workbook** ppWorkbook,
    BSTR* pbstr,
    etoldapi::PivotCache** ppCache,
    BSTR* pFailInfo)
{
    HRESULT hr = E_FAIL;

    if (m_sourceData)
    {
        ks_bstr bstrSrc;
        hr = m_spPTTool->ResolveCacheSourceString(
            krt::utf16(m_sourceDataText),
            m_bRCStyle,
            FALSE,
            TRUE,
            &bstrSrc);

        if (FAILED(hr))
            return hr;

        ks_bstr bstrDest;
        hr = getPlaceInfo(ppWorkbook, TRUE, &bstrDest);
        if (FAILED(hr))
            return hr;

        if (*ppWorkbook != ks_castptr<IKWorksheet>(m_spWorksheet)->GetWorkbook())
        {
            ks_bstr bstr;
            hr = m_spPTTool->MakeThisBookCross(bstrSrc.c_str(), &bstr);
            if (FAILED(hr))
                return E_FAIL;
            bstrSrc.attach(bstr.detach());
        }

        hr = getPivotCache(*ppWorkbook,
            bstrSrc,
            etDatabase,
            xlPivotTableVersionCurrent,
            ppCache,
            NULL,
            pFailInfo);
        if (FAILED(hr) || !*ppCache)
            return hr;

        *pbstr = bstrDest.detach();
    }
    else if (m_externalData)
    {
        ks_stdptr<IKWorkbookConnection> spWorkbookConn;
        hr =
            m_spPTTool->GetKWorkbookConnection(m_spConn, m_spWorksheet->GetWorkbook(), &spWorkbookConn);
        if (FAILED(hr))
            return hr;

        ks_bstr bstrDest;
        hr = getPlaceInfo(ppWorkbook, TRUE, &bstrDest);
        if (FAILED(hr))
            return hr;

        ks_stdptr<PivotCaches> spPivotCaches;
        (*ppWorkbook)->PivotCaches(&spPivotCaches);
        KComVariant varSrcConn(spWorkbookConn);
        KComVariant varVer((int)xlPivotTableVersion15, VT_I4);
        hr = spPivotCaches->Create(etExternal, varSrcConn, varVer, TRUE, ppCache);
        if (FAILED(hr))
            return hr;

        *pbstr = bstrDest.detach();
    }
    else if (m_consolidate)
    {
        if (m_dataConsolidate.vvData.empty())
            return E_FAIL;

        ks_bstr bstrDest;
        hr = getPlaceInfo(ppWorkbook, TRUE, &bstrDest);
        if (FAILED(hr))
            return hr;

        hr = getPivotCacheByConsolidate(*ppWorkbook, ppCache, pFailInfo);
        if (FAILED(hr) || !*ppCache)
            return hr;

        *pbstr = bstrDest.detach();
    }
    else
    {
        return E_FAIL;
    }

    return hr;
}

HRESULT CreatePivotTableHelper::createPivotTableInner(etoldapi::_Workbook* pWorkbook,
    BSTR bstrDest,
    etoldapi::PivotCache* pCache,
    etoldapi::PivotTable** ppTable,
    etoldapi::_Worksheet** spNewWorksheet)
{
    ks_stdptr<IKCoreObject> spCoreObj;
    ks_stdptr<_Workbook> spActiveBook = m_pWorkbook;
    if (spActiveBook->GetBook() != pWorkbook->GetBook())
        pWorkbook->Activate();

    if (NULL == bstrDest || NULL == bstrDest[0])
    {
        spCoreObj.clear();
        KComVariant vBefore, vAfter;
        if (INVALIDIDX != m_activeSheetIdx)
        {
            if (m_activeSheetIdx > 0)
            {
                vAfter = m_activeSheetIdx;
            }
            else
            {
                ASSERT(m_activeSheetIdx == 0);
                vBefore = m_activeSheetIdx + 1;
            }
        }

        m_spWorksheets->Add(vBefore, vAfter, KComVariant(), KComVariant(), &spCoreObj, stGrid);
        ks_stdptr<_Worksheet> spWorksheet = spCoreObj;
        if (!spWorksheet)
            return E_FAIL;

        ks_bstr bstrSheetName;
        spWorksheet->get_Name(&bstrSheetName);
        ks_wstring strDest = bstrSheetName.c_str();
        strDest.append(__X("!R3C1"));

        IDX idxSheet = INVALIDIDX;
        spWorksheet->GetSheet()->GetIndex(&idxSheet);

        ks_bstr bstrAutoName;
        pivot_core::IPivotTables* pTables = m_spPTTool->GetCoreTables(spWorksheet);
        if (!pTables || FAILED(pTables->GenerateAutoName(&bstrAutoName)))
            return E_FAIL;

        KComVariant varDest(strDest.c_str(), strDest.size());
        KComVariant varTblName(bstrAutoName.c_str(), bstrAutoName.size());
        KComVariant varReadData;

        XlPivotTableVersionList ver = xlPivotTableVersion15;
        pCache->get_Version(&ver);
        KComVariant varVer((int)ver, VT_I4);

        HRESULT hr = pCache->CreatePivotTable(varDest, varTblName, varReadData, varVer, TRUE, ppTable);
        if (FAILED(hr))
            return E_FAIL;

        if (spNewWorksheet) 
        {
            *spNewWorksheet = spWorksheet.detach();
        }

        return S_OK;
    }
    else
    {
        ks_stdptr<IBookOp> spBookOp;
        pWorkbook->GetBook()->GetOperator(&spBookOp);
        IDX idxSheet = INVALIDIDX;
        if (ks_castptr<IKWorksheet>(m_spWorksheet)->GetWorkbook() == pWorkbook)
            ks_castptr<IKWorksheet>(m_spWorksheet)->GetSheet()->GetIndex(&idxSheet);
        else
            idxSheet = 0;

        CS_COMPILE_PARAM ccp(cpfCellRC, idxSheet, 0, 0);
        range_helper::ranges rgs;

        PCWSTR strFmla = bstrDest;
        if (__Xc('=') == (*strFmla) || __Xc('＝') == (*strFmla))
            ++strFmla;

        HRESULT hr = spBookOp->CompileRange(strFmla, ccp, &rgs);
        if (FAILED(hr) || rgs.size() == 0)
            return hr;

        ASSERT(alg::STREF_THIS_BOOK == rgs.at(0).first);
        ks_stdptr<_Worksheet> ws = ks_castptr<IKWorkbook>(pWorkbook)->GetWorksheets()->GetSheetItem(rgs.at(0).second->SheetFrom());
        if (!ws)
            return E_FAIL;

        ks_bstr bstrAutoName;
        pivot_core::IPivotTables* pTables = m_spPTTool->GetCoreTables(ks_castptr<etoldapi::_Worksheet>(ws));
        if (!pTables || FAILED(pTables->GenerateAutoName(&bstrAutoName)))
            return E_FAIL;

        KComVariant varDest(bstrDest);
        KComVariant varTblName(bstrAutoName.c_str(), bstrAutoName.size());
        KComVariant varReadData;

        XlPivotTableVersionList ver = xlPivotTableVersion15;
        pCache->get_Version(&ver);
        KComVariant varVer((int)ver, VT_I4);

        hr = pCache->CreatePivotTable(varDest, varTblName, varReadData, varVer, TRUE, ppTable);
            
        if (SUCCEEDED(hr) && spNewWorksheet) 
        {
            *spNewWorksheet = ws.detach();
        }
        return hr;
    }
}

HRESULT CreatePivotTableHelper::getPivotCache(etoldapi::_Workbook* pDestBook,
    BSTR bstrSrc,
    ETPivotTableSourceType type,
    XlPivotTableVersionList ver,
    etoldapi::PivotCache** ppCache,
    etoldapi::PivotTable** ppTable,
    BSTR* pFailInfo)
{
    if (NULL == bstrSrc || NULL == *bstrSrc)
        return E_FAIL;

    ks_stdptr<IBookOp> spBookOp;
    VS(pDestBook->GetBook()->GetOperator(&spBookOp));
    ks_stdptr<IFormula> spFormula;
    VS(spBookOp->CreateFormula(&spFormula));

    IDX idxSheet = INVALIDIDX;
    ks_castptr<IKWorksheet>(m_spWorksheet)->GetSheet()->GetIndex(&idxSheet);
    CS_COMPILE_PARAM ccp(cpfReForceResult | cpfAllowTableRef | cpfAllowName | cpfRCStyle, idxSheet, 0, 0);
    COMPILE_RESULT result;
    VS(spFormula->SetFormula(bstrSrc, ccp, &result));
    VS(spFormula->ConvertFmlaOffset2Target(0, 0));
    VS(spFormula->ResolveName(idxSheet));
    exec_token_vector etv;
    VS(spFormula->GetTokenVecForce(&etv));

    ks_stdptr<PivotCache> spPivotCache;
    if (etv.size() == 1)
    {
        ks_stdptr<Worksheets> spWorksheets;
        pDestBook->get_Worksheets(&spWorksheets);
        long nSheetCnt = 0;
        spWorksheets->get_Count(&nSheetCnt);
        for (int32 i = 0; i < (int32)nSheetCnt; ++i)
        {
            ks_stdptr<IKCoreObject> spCoreObj;
            spWorksheets->get_Item(KComVariant(i + 1), &spCoreObj);
            ks_stdptr<_Worksheet> spWorksheet = spCoreObj;
            if (!spWorksheet)
                continue;

            spCoreObj.clear();
            spWorksheet->PivotTables(KComVariant(), 0, &spCoreObj);
            ks_stdptr<PivotTables> spPivotTables = spCoreObj;
            if (!spPivotTables)
                continue;

            long nPivotCnt = 0;
            spPivotTables->get_Count(&nPivotCnt);
            for (int32 j = 0; j < (int32)nPivotCnt; ++j)
            {
                ks_stdptr<PivotTable> spPivotTable;
                spPivotTables->Item(KComVariant(j + 1), &spPivotTable);
                if (!spPivotTable)
                    continue;

                ks_stdptr<PivotCache> spCache;
                spPivotTable->PivotCache(&spCache);
                if (!spCache)
                    continue;

                if (checkSamePivotCache(pDestBook, etv[0], type, spCache))
                {
                    if (ppTable)
                        (*ppTable) = spPivotTable.detach();
                    spPivotCache = spCache;
                    break;
                }
            }
            if (spPivotCache)
                break;
        }

        if (!spPivotCache)
        {
            // 存在只有PivotCache没有Table的情况，比如Slicer的场景
            ks_stdptr<etoldapi::PivotCaches> spPivotCaches;
            pDestBook->PivotCaches(&spPivotCaches);
            LONG lCnt = 0;
            spPivotCaches->get_Count(&lCnt);
            for (int32 lIdx = 1; lIdx <= (int32)lCnt; ++lIdx)
            {
                ks_stdptr<etoldapi::PivotCache> spTempPivotCache;
                spPivotCaches->Item(KComVariant(lIdx), &spTempPivotCache);
                if (!spTempPivotCache)
                    continue;

                if (checkSamePivotCache(pDestBook, etv[0], type, spTempPivotCache))
                {
                    spPivotCache = spTempPivotCache;
                    break;
                }
            }
        }
    }
    if (!spPivotCache)
    {
        ks_stdptr<PivotCaches> spPivotCaches;
        VS(pDestBook->PivotCaches(&spPivotCaches));
        KComVariant varType((int)type, VT_I4);
        KComVariant varSrcData(bstrSrc);
        KComVariant varVer((int)ver, VT_I4);
        return spPivotCaches->Create(type, varSrcData, varVer, TRUE, ppCache, pFailInfo);
    }

    (*ppCache) = spPivotCache.detach();
    return S_OK;
}

HRESULT CreatePivotTableHelper::getPivotCacheByConsolidate(_Workbook* pWorkbook,
    etoldapi::PivotCache** ppCache,
    BSTR* pFailInfo)
{
    if (m_dataConsolidate.vvData.empty())
        return E_FAIL;

    size_t cnt = m_dataConsolidate.vvData[0].size();
    if (0 == cnt)
        return E_FAIL;

    return E_FAIL; // unsupport now
}

BOOL CreatePivotTableHelper::checkSamePivotCache(_Workbook* pDestBook,
    const_token_ptr token,
    ETPivotTableSourceType type,
    etoldapi::PivotCache* pCache)
{
    ASSERT(token);
    ETPivotTableSourceType srcType = etDatabase;
    pCache->get_SourceType(&srcType);
    if (type != srcType)
        return FALSE;

    KComVariant varSourceData;
    pCache->get_SourceData(&varSourceData);
    if (etDatabase == type)
    {
        KSmartParam smtSourceData(varSourceData);
        if (smtSourceData.IsStringType())
        {
            const BSTR bstr = smtSourceData.GetStringValue();
            if (NULL == bstr || NULL == *bstr)
                return FALSE;

            ks_stdptr<IBookOp> spBookOp;
            VS(pDestBook->GetBook()->GetOperator(&spBookOp));
            ks_stdptr<IFormula> spFormula;
            VS(spBookOp->CreateFormula(&spFormula));

            ks_wstring strFormula(bstr);
            strFormula.Trim(__X(" "));
            if (strFormula[0] != __Xc('='))
                strFormula.insert(0, __X("="));

            IDX idxSheet = INVALIDIDX;
            ks_castptr<IKWorksheet>(m_spWorksheet)->GetSheet()->GetIndex(&idxSheet);
            CS_COMPILE_PARAM ccp(cpfReForceResult | cpfAllowTableRef | cpfAllowName | cpfRCStyle,
                idxSheet,
                0,
                0);
            COMPILE_RESULT result;
            VS(spFormula->SetFormula(strFormula.c_str(), ccp, &result));
            VS(spFormula->ConvertFmlaOffset2Target(0, 0));
            VS(spFormula->ResolveName(idxSheet));

            exec_token_vector etv;
            VS(spFormula->GetTokenVecForce(&etv));
            if (etv.size() == 1 && alg::IsExecTokenEqual(etv[0], token) == S_OK)
                return TRUE;
        }
    }

    return FALSE;
}

PivotTableHelper::PivotTableHelper(etoldapi::_Workbook* pWorkbook,
    etoldapi::_Worksheet* pWorkSheet,
    pivot_core::IPivotTable* corePivotTable,
    etoldapi::PivotTable* pivotTable)
    : m_pWorkbook(pWorkbook),
    m_spWorksheet(pWorkSheet),
    m_spWorksheets(pWorkbook->GetWorksheets()),
    m_spCorePivotTable(corePivotTable),
    m_spPivotTable(pivotTable)
{
    PivotHelpers::GetPivotTableTool(pWorkbook, &m_spPTTool);
}

HRESULT PivotTableHelper::addField(PCWSTR fieldName, PCWSTR fieldOrientation, int newPos, bool autoFieldOrientation)
{
    if (!m_spPTTool)
    {
        return E_FAIL;
    }

    int fieldIndex = PivotHelpers::GetFieldIndexByName(m_spPivotTable, fieldName);
    if (INVALIDIDX == fieldIndex)
    {
        return E_FAIL;
    }

    ks_stdptr<IET_PivotTableTool> pivotTool = m_spPTTool;
    ks_stdptr<etoldapi::PivotTable> tbl = m_spPivotTable;
    int ID = fieldIndex;
    KComVariant var;
    var.Assign(ID + 1);
    ks_stdptr<IKCoreObject> spObj;
    VS(tbl->PivotFields(var, &spObj));
    HRESULT hr4Proc = S_OK;
    ks_castptr<etoldapi::PivotField> spFld = spObj;
    bool needResetOrientWhenFail = false;

    { // let PivotTblBatchHelper release hr4Proc
        oldapi::ETPivotFieldOrientation dir = PivotHelpers::ToFieldOrientation(fieldOrientation);
        if (autoFieldOrientation)
        {
            pivot_core::SubtotalType st = pivot_core::SubtotalCount;
		    m_spCorePivotTable->ValueFieldsDefaultParam(pivotTool->GetCoreField(spFld), &st);
            dir = (st == pivot_core::SubtotalSum? oldapi::etDataField : oldapi::etRowField);
        }
        PivotTblBatchHelper hlp(pivotTool, tbl, &hr4Proc, FALSE);
        if (dir == oldapi::etDataField)
        {
            ks_stdptr<etoldapi::PivotField> spFldAdd;
            KComVariant varEmpty;
            hlp.ProcResult(tbl->AddDataField(spFld, varEmpty, varEmpty, FALSE, &spFldAdd));
            if (spFldAdd != NULL && newPos != -1)
            {
                KComVariant varPos(newPos + 1);
                spFldAdd->put_Position(varPos);
            }
        }
        else
        {
            hlp.ProcResult(spFld->put_Orientation(dir));
            if (dir != etHidden && SUCCEEDED(hlp.GetResult()))
            {
                int nPos = convertFieldPosition(tbl, newPos, dir) + 1;
                KComVariant varNewPos;
                varNewPos.Assign(nPos);
                hlp.ProcResult(spFld->put_Position(varNewPos));
                needResetOrientWhenFail = true;
            }
        }
    }

    // 事务回滚时, put_Orientation 回滚不了，先容下错
    if (needResetOrientWhenFail && FAILED(hr4Proc))
    {
        spFld->put_Orientation(etHidden);
    }

    return hr4Proc;
}

HRESULT PivotTableHelper::removeField(PCWSTR fieldName, PCWSTR fieldOrientationStr)
{
    if (!m_spPTTool)
    {
        return E_FAIL;
    }

    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldOrientationStr);
    ks_stdptr<IET_PivotTableTool> tool = m_spPTTool;
    ks_stdptr<etoldapi::PivotTable> tbl = m_spPivotTable;
    HRESULT hr = S_OK;
    { // let PivotTblBatchHelper release hr4Proc
        PivotTblBatchHelper hlp(tool, tbl, &hr, FALSE);

        ks_stdptr<etoldapi::PivotField> spFld;
        if (fieldOrientation == oldapi::etDataField)
        {
            int fieldPos = PivotHelpers::GetDataFieldPosByName(m_spPivotTable, fieldName);
            if (INVALIDIDX == fieldPos)
            {
                return E_FAIL;
            }

            KComVariant varIdx;
            varIdx.Assign(fieldPos + 1);
            ks_stdptr<IKCoreObject> spFldObj;
            if (SUCCEEDED(tbl->AxisFields(oldapi::etDataField, varIdx, &spFldObj)))
            {
                ks_castptr<etoldapi::PivotField> spFldTmp = spFldObj;
                spFld = spFldTmp.get();
            }
        }
        else
        {
            int fieldIndex = PivotHelpers::GetFieldIndexByName(m_spPivotTable, fieldName);
            if (INVALIDIDX == fieldIndex)
            {
                return E_FAIL;
            }

            ks_stdptr<IKCoreObject> spFldObj;
            KComVariant varIdx;
            varIdx.Assign(fieldIndex + 1);
            if (SUCCEEDED(tbl->PivotFields(varIdx, &spFldObj)))
            {
                ks_castptr<etoldapi::PivotField> spFldTmp = spFldObj;
                spFld = spFldTmp.get();
            }
        }

        if (spFld != NULL)
            hlp.ProcResult(spFld->put_Orientation(oldapi::etHidden));
    }

    return hr;
}

INT PivotTableHelper::convertFieldPosition(etoldapi::PivotTable* tbl,
    INT nPos,
    oldapi::ETPivotFieldOrientation dir)
{
    KComVariant varEmpty;
    ks_stdptr<IKCoreObject> spObjAxis;
    VS(tbl->AxisFields(dir, varEmpty, &spObjAxis));
    ks_castptr<etoldapi::PivotFields> spAxisFlds = spObjAxis;
    long xPos = 0;
    VS(spAxisFlds->get_Count(&xPos));

    if (dir != oldapi::etPageField)
    {
        if (nPos >= 0)
            return nPos;
        else
            return xPos - 1;
    }
    else
    {
        if (nPos >= 0)
            return (xPos - nPos - 1);
        else
            return 0;
    }
}

HRESULT PivotTableHelper::moveFiled(PCWSTR fieldName,
    PCWSTR srcOrientation,
    PCWSTR destOrientation,
    int newPos)
{
    return moveFieldProc(fieldName, PivotHelpers::ToFieldOrientation(srcOrientation), PivotHelpers::ToFieldOrientation(destOrientation), newPos);
}

HRESULT PivotTableHelper::moveFieldProc(PCWSTR fieldName,
    oldapi::ETPivotFieldOrientation srcDir,
    oldapi::ETPivotFieldOrientation destDir,
    int newPos)
{
    if (!m_spPTTool)
    {
        return E_FAIL;
    }
    
    ks_stdptr<IET_PivotTableTool> tool = m_spPTTool;
    ks_stdptr<etoldapi::PivotTable> tbl = m_spPivotTable;
    if (tbl == NULL)
        return E_FAIL;

    int nNewPos = newPos;
    ks_stdptr<etoldapi::PivotField> spFld;
    int idxFld = PivotHelpers::GetFieldIndexByName(tool, tbl, fieldName, srcDir);
    if (INVALIDIDX == idxFld)
    {
        return E_FAIL;
    }

    if (srcDir == oldapi::etDataField)
    {
        int posFld = PivotHelpers::GetDataFieldPosByName(m_spPivotTable, fieldName);
        if (INVALIDIDX == posFld)
        {
            return E_FAIL;
        }

        KComVariant varIdx;
        varIdx.Assign(posFld + 1);
        ks_stdptr<IKCoreObject> spFldObj;
        if (SUCCEEDED(tbl->AxisFields(oldapi::etDataField, varIdx, &spFldObj)))
        {
            ks_castptr<etoldapi::PivotField> spFldTmp = spFldObj;
            spFld = spFldTmp.get();

            if (destDir != oldapi::etDataField && destDir != oldapi::etHidden)
            {
                pivot_core::AxisFieldType aft = pivot_core::AxisFieldNone;
                pivot_core::ITblFieldSrc* src = tool->GetCoreField(spFld);
                if (src != NULL && src->IsAxisFieldCreated())
                    aft = src->QueryAxisField(false)->GetFieldType();

                if (pivot_core::AxisFieldNone != aft)
                    return dataAreaMovetoOthers(tool, tbl, spFld, destDir, newPos);
            }
        }
    }
    else
    {
        ks_stdptr<IKCoreObject> spFldObj;
        KComVariant varIdx;
        varIdx.Assign(idxFld + 1);
        if (SUCCEEDED(tbl->PivotFields(varIdx, &spFldObj)))
        {
            ks_castptr<etoldapi::PivotField> spFldTmp = spFldObj;
            spFld = spFldTmp.get();
        }
    }

    BOOL bAxisHidden = FALSE;
    HRESULT hr = S_OK;
    APP_BeginUndoTrans(m_pWorkbook, FALSE, NULL);
    {
        PivotTblBatchHelper hlp(tool, tbl, &hr);
        if (spFld != NULL)
        {
            pivot_core::ITblFieldSrc* src = tool->GetCoreField(spFld);
            if (srcDir != oldapi::etDataField && oldapi::etDataField == destDir)
            {
                oldapi::ETPivotFieldOrientation srcDir = oldapi::etHidden;
                spFld->get_Orientation(&srcDir);
                BOOL bSetAxisNone = (etHidden == srcDir) ? FALSE : TRUE;
                ks_stdptr<PivotField> spPivotField;
                hr = tbl->AddDataField(spFld, KComVariant(), KComVariant(), bSetAxisNone, &spPivotField);
                if (spPivotField != NULL && nNewPos != -1)
                {
                    KComVariant varPos(nNewPos + 1);
                    spPivotField->put_Position(varPos);
                }
                bAxisHidden = TRUE;
            }
            else if (src != NULL && src->HasCalculatedItems() && destDir == oldapi::etPageField)
            {
                hr = E_PIVOTTABLE_DENIED_CALCULATEDITEM_MOVE_TO_PAGE;
            }
            else
            {
                hlp.ProcResult(spFld->put_Orientation(destDir));
                //这里是从dataArea移动page,row,col的位置处理
                if (srcDir == oldapi::etDataField && oldapi::etDataField != destDir &&
                    oldapi::etHidden != destDir && SUCCEEDED(hr))
                {
                    ks_stdptr<IKCoreObject> spFldObj;
                    KComVariant varIdx;
                    varIdx.Assign(idxFld + 1);
                    ks_stdptr<etoldapi::PivotField> spFldFromData;
                    if (SUCCEEDED(tbl->PivotFields(varIdx, &spFldObj)))
                    {
                        ks_castptr<etoldapi::PivotField> spFldTmp = spFldObj;
                        spFldFromData = spFldTmp.get();

                        nNewPos = convertFieldPosition(tbl, nNewPos, destDir) + 1;
                        KComVariant varPos;
                        varPos.Assign(nNewPos);
                        hlp.ProcResult(spFldFromData->put_Position(varPos));
                    }
                }
            }
            hlp.ProcResult(hr);
        }
    }

    if (SUCCEEDED(hr) && spFld != NULL && (srcDir != oldapi::etDataField || destDir == oldapi::etDataField) &&
        !bAxisHidden)
    {
        PivotTblBatchHelper hlp(tool, tbl, &hr);
        nNewPos = convertFieldPosition(tbl, nNewPos, destDir) + 1;
        KComVariant varPos;
        varPos.Assign(nNewPos);

        hlp.ProcResult(spFld->put_Position(varPos));
    }

    APP_EndUndoTRANS(hr, FALSE, FALSE);
    return hr;
}

HRESULT PivotTableHelper::dataAreaMovetoOthers(IET_PivotTableTool* tool,
    etoldapi::PivotTable* tbl,
    etoldapi::PivotField* pFld,
    oldapi::ETPivotFieldOrientation desDir,
    int nPos)
{
    pivot_core::ITblFieldSrc* src = tool->GetCoreField(pFld);
    if (src == NULL)
        return E_FAIL;

    HRESULT hr = S_OK;
    ks_stdptr<etoldapi::PivotField> spFld;
    ks_stdptr<IKCoreObject> spFldObj;
    KComVariant varName;
    varName.AssignString(src->GetName());
    if (SUCCEEDED(tbl->PivotFields(varName, &spFldObj)))
    {
        ks_castptr<etoldapi::PivotField> spFldTmp = spFldObj;
        spFld = spFldTmp.get();

        if (spFld == NULL)
            return E_FAIL;

        oldapi::ETPivotFieldOrientation srcDir = oldapi::etHidden;
        spFld->get_Orientation(&srcDir);

        PivotTblBatchHelper hlp(tool, tbl);
        if (srcDir != desDir)
        {
            if (spFld != NULL)
            {
                hr = spFld->put_Orientation(desDir);
                hlp.ProcResult(hr);
            }
        }

        {
            int nNewPos = nPos;
            KComVariant varPosTmp;

            spFld->get_Position(&varPosTmp);
            KSmartParam param(varPosTmp);
            int oldPos = param.GetNumberValue();
            nNewPos = convertFieldPosition(tbl, nNewPos, desDir) + 1;

            //从dataArea移到cow,rol的时候，如果这个字段本身以前在新的位置前面要减去一
            //移到page的时候，因为page在内核存的顺序是相反，所以处理不一样
            if (srcDir == oldapi::etPageField)
            {
                if (oldPos > nNewPos && nPos != -1)
                {
                    nNewPos = nNewPos + 1;
                }
            }
            else
            {
                if (oldPos < nPos && nPos != -1)
                {
                    nNewPos = nNewPos - 1;
                }
            }

            KComVariant varPos;
            varPos.Assign(nNewPos);
            if (SUCCEEDED(hr) && spFld != NULL)
                hlp.ProcResult(spFld->put_Position(varPos));
        }
    }

    return hr;
}

HRESULT PivotTableHelper::getPivotFieldByName(PCWSTR fieldName, oldapi::ETPivotFieldOrientation dir, etoldapi::PivotField** outField)
{
    if (dir == oldapi::etDataField)
    {
        int posFld = PivotHelpers::GetDataFieldPosByName(m_spPivotTable, fieldName);
        if (INVALIDIDX == posFld)
        {
            return E_FAIL;
        }

        KComVariant varIdx;
        varIdx.Assign(posFld + 1);
        ks_stdptr<IKCoreObject> spFldObj;
        if (SUCCEEDED(m_spPivotTable->AxisFields(oldapi::etDataField, varIdx, &spFldObj)))
        {
            ks_castptr<etoldapi::PivotField> spFldTmp = spFldObj;
            *outField = spFldTmp;
            spFldObj.detach();
            return S_OK;
        }
    }
    else
    {
        int idxFld = PivotHelpers::GetFieldIndexByName(m_spPTTool, m_spPivotTable, fieldName, dir);
        if (INVALIDIDX == idxFld)
        {
            return E_FAIL;
        }

        ks_stdptr<IKCoreObject> spFldObj;
        KComVariant varIdx;
        varIdx.Assign(idxFld + 1);
        if (SUCCEEDED(m_spPivotTable->PivotFields(varIdx, &spFldObj)))
        {
            ks_castptr<etoldapi::PivotField> spFldTmp = spFldObj;
            *outField = spFldTmp;
            spFldObj.detach();
            return S_OK;
        }
    }

    return E_FAIL;
}

HRESULT PivotTableHelper::setFieldName(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR newName)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);

    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    ks_bstr bsNewName(newName);
    return spFld->put_Name(bsNewName);
}

oldapi::ETConsolidationFunction strToETConsolidationFunction(PCWSTR str)
{
    if (xstrcmp(str, __X("etAverage")) == 0)
    {
        return etAverage;
    }
    else if (xstrcmp(str, __X("etCount")) == 0)
    {
        return etCount;
    }
    else if (xstrcmp(str, __X("etCountNums")) == 0)
    {
        return etCountNums;
    }
    else if (xstrcmp(str, __X("etMax")) == 0)
    {
        return etMax;
    }
    else if (xstrcmp(str, __X("etMin")) == 0)
    {
        return etMin;
    }
    else if (xstrcmp(str, __X("etProduct")) == 0)
    {
        return etProduct;
    }
    else if (xstrcmp(str, __X("etStDev")) == 0)
    {
        return etStDev;
    }
    else if (xstrcmp(str, __X("etStDevP")) == 0)
    {
        return etStDevP;
    }
    else if (xstrcmp(str, __X("etSum")) == 0)
    {
        return etSum;
    }
    else if (xstrcmp(str, __X("etVar")) == 0)
    {
        return etVar;
    }
    else if (xstrcmp(str, __X("etVarP")) == 0)
    {
        return etVarP;
    }
    else if (xstrcmp(str, __X("etDistinctCount")) == 0)
    {
        return etDistinctCount;
    }
    return etUnknown;
}

oldapi::XlPivotFieldCalculation strToXlPivotFieldCalculation(PCWSTR str)
{
    if (xstrcmp(str, __X("xlDifferenceFrom")) == 0)
    {
        return xlDifferenceFrom;
    }
    else if (xstrcmp(str, __X("xlIndex")) == 0)
    {
        return xlIndex;
    }
    else if (xstrcmp(str, __X("xlNoAdditionalCalculation")) == 0)
    {
        return xlNoAdditionalCalculation;
    }
    else if (xstrcmp(str, __X("xlPercentDifferenceFrom")) == 0)
    {
        return xlPercentDifferenceFrom;
    }
    else if (xstrcmp(str, __X("xlPercentOf")) == 0)
    {
        return xlPercentOf;
    }
    else if (xstrcmp(str, __X("xlPercentOfColumn")) == 0)
    {
        return xlPercentOfColumn;
    }
    else if (xstrcmp(str, __X("xlPercentOfRow")) == 0)
    {
        return xlPercentOfRow;
    }
    else if (xstrcmp(str, __X("xlPercentOfTotal")) == 0)
    {
        return xlPercentOfTotal;
    }
    else if (xstrcmp(str, __X("xlRunningTotal")) == 0)
    {
        return xlRunningTotal;
    }
    else if (xstrcmp(str, __X("xlPercentOfParentRow")) == 0)
    {
        return xlPercentOfParentRow;
    }
    else if (xstrcmp(str, __X("xlPercentOfParentColumn")) == 0)
    {
        return xlPercentOfParentColumn;
    }
    else if (xstrcmp(str, __X("xlPercentOfParent")) == 0)
    {
        return xlPercentOfParent;
    }
    else if (xstrcmp(str, __X("xlPercentRunningTotal")) == 0)
    {
        return xlPercentRunningTotal;
    }
    else if (xstrcmp(str, __X("xlRankAscending")) == 0)
    {
        return xlRankAscending;
    }
    else if (xstrcmp(str, __X("xlRankDecending")) == 0)
    {
        return xlRankDecending;
    }
    else
    {
        ASSERT(false);
    }
    return xlNoAdditionalCalculation;
}

HRESULT PivotTableHelper::setFieldFunction(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR func)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    return spFld->put_Function(strToETConsolidationFunction(func));
}

VARIANT PivotTableHelper::getSubtotalsSafeArray(BOOL bShow)
{
	const int SUBTTTYPE_NUM = 13;
	BOOL subttArray[SUBTTTYPE_NUM] = { FALSE };
	if (bShow) subttArray[0] = TRUE;

	VARIANT varArray;
	SAFEARRAYBOUND rgsabound;
	rgsabound.cElements = SUBTTTYPE_NUM;
	rgsabound.lLbound = 1;
	V_VT(&varArray) = VT_VARIANT | VT_ARRAY;
	V_ARRAY(&varArray) = SafeArrayCreate(VT_VARIANT, 1, &rgsabound);

	VARIANT varTemp; V_VT(&varTemp) = VT_BOOL;
	for (size32 i = 1; i <= SUBTTTYPE_NUM; ++i)
	{
		V_BOOL(&varTemp) = (subttArray[i - 1] ? VARIANT_TRUE : VARIANT_FALSE);
		SafeArrayPutElement(V_ARRAY(&varArray), &i, &varTemp);
	}
	return varArray;
}


BOOL PivotTableHelper::checkArrayHasTrueValue(VARIANT varArray)
{
	KSmartParam smtArray(varArray);
	if (!smtArray.IsArrayType()) return FALSE;

	const int SUBTTTYPE_NUM = 13;
	size32 lLBoundElement = -1;
	size32 lUBoundElement = -1;
	SafeArrayGetLBound(V_ARRAY(&varArray), 1, &lLBoundElement);
	SafeArrayGetUBound(V_ARRAY(&varArray), 1, &lUBoundElement);

	size32 nSize = lUBoundElement - lLBoundElement + 1;
	if (nSize != SUBTTTYPE_NUM)
		return FALSE;

	VARTYPE varElemType = VT_EMPTY;
	HRESULT hr = SafeArrayGetVartype(V_ARRAY(&varArray), &varElemType);
	if (FAILED(hr)) return FALSE;

	if (VT_BOOL == varElemType)
	{
		VARIANT_BOOL vbElem = VARIANT_FALSE;
		for (size32 idx = 0; idx < SUBTTTYPE_NUM; ++idx)
		{
			size32 lIndexTemp = lLBoundElement + idx;
			HRESULT hr = SafeArrayGetElement(V_ARRAY(&varArray), &lIndexTemp, &vbElem);
			if (SUCCEEDED(hr) && vbElem == VARIANT_TRUE)
				return TRUE;
		}
	}
	else if (VT_VARIANT == varElemType)
	{
		KSmartParam smtElem;
		for (INT idx = 0; idx < SUBTTTYPE_NUM; ++idx)
		{
			size32 lIndexTemp = lLBoundElement + idx;
			HRESULT hr = SafeArrayGetElement(V_ARRAY(&varArray), &lIndexTemp, &smtElem);
			if (SUCCEEDED(hr) && smtElem.IsBooleanType() && smtElem.GetBooleanValue())
				return TRUE;
		}
	}
	return FALSE;
}

HRESULT PivotTableHelper::showPivotSubtotals(
	etoldapi::_Workbook* pWorkbook, etoldapi::PivotTable* pPivotTable, BOOL bShow)
{
	ASSERT(pPivotTable);
	ks_stdptr<IKCoreObject> spCoreObj;
	KComVariant varEmpty;
	HRESULT hr = pPivotTable->PivotFields(varEmpty, &spCoreObj);
	if (FAILED(hr)) return hr;

	KComVariant varSubtotals;
	VARIANT var = getSubtotalsSafeArray(bShow);
	varSubtotals.Attach(&var);
	ks_stdptr<etoldapi::PivotFields> spPivotFields = spCoreObj;
	long nCount = 0;
	VS(spPivotFields->get_Count(&nCount));
	VARIANT varIndex; V_VT(&varIndex) = VT_INT;
	for (long i = 0; i < nCount; i++)
	{
		V_INT(&varIndex) = i + 1;
		ks_stdptr<etoldapi::PivotField> spPivotField;
		spPivotFields->Item(varIndex, &spPivotField);
		if (!spPivotField || spPivotField->IsSigmaField()) continue;

		KComVariant varArray;
		hr = spPivotField->get_Subtotals(varEmpty, &varArray);
		if (FAILED(hr)) break;

		BOOL b = checkArrayHasTrueValue(varArray);
		if (bShow != b)
			hr = spPivotField->put_Subtotals(varEmpty, varSubtotals);

		if (FAILED(hr)) break;
	}
	return hr;
}



HRESULT PivotTableHelper::getPivotField(PCWSTR fieldName, PCWSTR fieldDir, etoldapi::PivotField** pPFld)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    return getPivotFieldByName(fieldName, fieldOrientation, pPFld);
}

HRESULT PivotTableHelper::setFieldSubtotals(PCWSTR fieldName, PCWSTR fieldDir, const std::vector<PCWSTR>& subtotals)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    KComVariant varEmpty, varArray;
    const int SUBTTTYPE_NUM = 13;
    PCWSTR subtotalType[SUBTTTYPE_NUM] = {
        __X("SubtotalAuto"),
        __X("SubtotalSum"),
        __X("SubtotalCount"),
        __X("SubtotalAverage"),
        __X("SubtotalMax"),
        __X("SubtotalMin"),
        __X("SubtotalProduct"),
        __X("SubtotalCountN"),
        __X("SubtotalStdev"),
        __X("SubtotalStdevP"),
        __X("SubtotalVar"),
        __X("SubtotalVarP"),
        __X("SubtotalDistinct")
    };

    auto wstrEqual = [](PCWSTR x, PCWSTR y) -> bool {
        return xstrcmp(x, y) == 0;
    };

    SAFEARRAYBOUND rgsabound;
    rgsabound.cElements = SUBTTTYPE_NUM;
    rgsabound.lLbound = 1;
    V_VT(&varArray) = VT_VARIANT | VT_ARRAY;
    V_ARRAY(&varArray) = SafeArrayCreate(VT_VARIANT, 1, &rgsabound);

    KComVariant varBoolTemp;
    for (SIZE32 i = 1; i <= SUBTTTYPE_NUM; ++i)
    {
        auto fit = std::find_if(subtotals.begin(), subtotals.end(), [&](PCWSTR val) -> bool {
            return wstrEqual(subtotalType[i - 1], val);
            });

        if (fit != subtotals.end())
        {
            varBoolTemp.AssignBOOL(TRUE);
        }
        else
        {
            varBoolTemp.AssignBOOL(FALSE);
        }

        SafeArrayPutElement(V_ARRAY(&varArray), &i, &varBoolTemp);
    }

    return spFld->put_Subtotals(varEmpty, varArray);
}

HRESULT PivotTableHelper::refresh()
{
    ks_stdptr<etoldapi::PivotCache> spPivotCache;
    pivot_core::IPivotCache* tblCache = m_spCorePivotTable->GetPivotCache();
    if (tblCache)
    {
        m_spPTTool->GainPivotCache(m_pWorkbook, tblCache, &spPivotCache);
        if (spPivotCache)
        {
            HRESULT hr = spPivotCache->Refresh();
            m_spCorePivotTable->SetCycleRef(m_spCorePivotTable->IsSourceDataDirty());
            return hr;
        }
    }

    return E_FAIL;
}

HRESULT PivotTableHelper::setFieldCalculation(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR calculation)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    oldapi::XlPivotFieldCalculation cal = strToXlPivotFieldCalculation(calculation);
    hr = spFld->put_Calculation(cal);
    if (FAILED(hr))
    {
        return hr;
    }

    QString qstrNumberFmt;
    if (PivotHelpers::IsPercentCalculation(cal))
        qstrNumberFmt = QString::fromUtf16(kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_NUM19));
    else
        qstrNumberFmt = QString::fromUtf16(kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_G));

	if (FAILED(spFld->put_NumberFormat(ks_bstr(krt::utf16(qstrNumberFmt)))))
		return E_FAIL;
    
    return S_OK;
}

HRESULT PivotTableHelper::setFieldBaseField(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR baseField)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    KComVariant var;
    var.AssignString(baseField);
    return spFld->put_BaseField(var);
}

HRESULT PivotTableHelper::setFieldBaseItem(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR baseItem)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    KComVariant var;
    var.AssignString(baseItem);
    return spFld->put_BaseItem(var);
}

HRESULT PivotTableHelper::setFieldRepeatLabels(PCWSTR fieldName, PCWSTR fieldDir, bool repeatLabels)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    return spFld->put_RepeatLabels(wo::util::bool2VarBool(repeatLabels));
}

HRESULT PivotTableHelper::sortByLabel(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR order, bool bManual)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    return spFld->SortByLabel(PivotHelpers::toSortOrder(order), alg::bool2BOOL(bManual));
}

HRESULT PivotTableHelper::autoSort(PCWSTR fieldName, PCWSTR fieldDir, PCWSTR order, PCWSTR field)
{
    const oldapi::ETPivotFieldOrientation fieldOrientation = PivotHelpers::ToFieldOrientation(fieldDir);
    ks_stdptr<etoldapi::PivotField> spFld;
    HRESULT hr = getPivotFieldByName(fieldName, fieldOrientation, &spFld);
    if (FAILED(hr) || !spFld)
    {
        return hr;
    }

    if (field)
    {
        ks_bstr bsField(field);
        return spFld->AutoSort(PivotHelpers::toSortOrder(order), bsField, KComVariant(), KComVariant());
    }
    else
    {
        return spFld->AutoSort(PivotHelpers::toSortOrder(order), NULL, KComVariant(), KComVariant());
    }
}

////// PivotFilterNode 相关

PivotFilterNode::PivotFilterNode(NodeType type, int nItemID, PCWSTR bstrData, CheckType checkType, UINT val)
    : m_type(type)
    , m_nItemID(nItemID)
    , m_bstrData(bstrData)
    , m_checkType(checkType)
    , m_val(val)
{
}
PivotFilterNode::~PivotFilterNode()
{
    for(const auto& child : m_children)
        delete child;
}

PivotFilterNode::CheckType PivotFilterNode::GainCheckState()
{
    if(m_children.empty())
        return m_checkType;
    
    bool isSame = true;
    CheckType state = CheckType::CheckType_unInit;
    for(const auto& child : m_children)
    {
        if (state == CheckType::CheckType_unInit)
        {
            state = child->GainCheckState();
        }
        else
        {
            if (state != child->GainCheckState())
            {
                isSame = false;
                break;
            }
        }
    }
    m_checkType = isSame ? state : CheckType::CheckType_partiallyChecked;
    return m_checkType;
}

void PivotFilterNode::appendChild(int nItemID, PCWSTR bstrData, bool bVisible)
{
    m_children.push_back(new PivotFilterNode(eNonDate, nItemID, bstrData, bVisible ? CheckType_checked : CheckType_unchecked, -1));
}

void PivotFilterNode::appendDateChild(pivot_core::DATETIME_T dateTime, int itemID, bool bVisible)
{
    UINT year = dateTime.year;
    UINT month = dateTime.month;
    UINT day = dateTime.day;
    UINT hour = dateTime.hour;
    UINT minute = dateTime.minute;
    UINT second = dateTime.second;

    auto haveHMS = [&]() ->bool {
		return hour != 0 || minute != 0 || second != 0;
	};

    PivotFilterNode* pYearNode = this->findChild(year);
    if (pYearNode == nullptr)
    {
        QString data = QString("%1年").arg(year);
        pYearNode = new PivotFilterNode(eYear, -1, krt::utf16(data), CheckType_unInit, year);
        this->addChild(pYearNode, year);
    }

    PivotFilterNode* pMonthNode = pYearNode->findChild(month);
    if (pMonthNode == nullptr)
    {
        QString data = QString("%1月").arg(month);
        pMonthNode = new PivotFilterNode(eMonth, -1, krt::utf16(data), CheckType_unInit, month);
        pYearNode->addChild(pMonthNode, month);
    }

    PivotFilterNode* pDayNode = pMonthNode->findChild(day);
    if (pDayNode == nullptr)
    {
        QString data = QString("%1日").arg(day);
        if (haveHMS()) // dayNode 不是叶子节点
            pDayNode = new PivotFilterNode(eDay, -1, krt::utf16(data), CheckType_unInit, day);
        else
            pDayNode = new PivotFilterNode(eDay, itemID, krt::utf16(data), bVisible ? CheckType_checked : CheckType_unchecked, day);
        pMonthNode->addChild(pDayNode, day);
    }

    if (haveHMS())
    {
        PivotFilterNode* pHourNode = pDayNode->findChild(hour);
        if(pHourNode == nullptr)
        {
            QString data = QString::asprintf("%02d", hour);
            pHourNode = new PivotFilterNode(eHour, -1, krt::utf16(data), CheckType_unInit, hour);
            pDayNode->addChild(pHourNode, hour);
        }

        PivotFilterNode* pMinuteNode = pHourNode->findChild(minute);
        if (pMinuteNode == nullptr)
        {
            QString data = QString::asprintf(":%02d", minute);
            pMinuteNode = new PivotFilterNode(eMinute, -1, krt::utf16(data), CheckType_unInit, minute);
            pHourNode->addChild(pMinuteNode, minute);
        }

        PivotFilterNode* pSecondNode = pMinuteNode->findChild(second);
        if (pSecondNode == nullptr)
        {
            QString data = QString::asprintf(":%02d", second);
            pSecondNode = new PivotFilterNode(eSecond, itemID, krt::utf16(data), bVisible ? CheckType_checked : CheckType_unchecked, second);
            pMinuteNode->addChild(pSecondNode, second);
        }
    }
}

void PivotFilterNode::addChild(PivotFilterNode* node, UINT key)
{
    m_children.push_back(node);
    m_dateMap.insert(std::make_pair(key, node));
}

PivotFilterNode* PivotFilterNode::findChild(UINT key)
{
    auto it = m_dateMap.find(key);
    if (it != m_dateMap.end())
    {
        return it->second;
    }
    return nullptr;
}

int PivotFilterNode::getTotal()
{
    if (m_children.empty())
        return 1;
    int total = 0;
    for(const auto& child : m_children)
    {
        total += child->getTotal();
    }
    return total;
}

void PivotFilterNode::serialiseValues(ISerialAcceptor *acpt)
{
    if (m_children.size() > 0)
    {
        acpt->addKey("childs");
        acpt->beginArray();
            if (m_type == eRoot)
            {
                acpt->beginStruct();
                acpt->endStruct();
                acpt->beginStruct();
                acpt->endStruct();
            }
            for (const auto& child : m_children)
            {
                acpt->beginStruct();
                child->serialiseValues(acpt);
                acpt->endStruct();
            }
        acpt->endArray();
    }
    acpt->addString("text", m_bstrData.c_str());
    switch(m_checkType)
    {
        case CheckType::CheckType_unchecked:
            acpt->addString("checkType", __X("unchecked"));
            break;
	    case CheckType::CheckType_partiallyChecked:
            acpt->addString("checkType", __X("partiallyChecked"));
            break;
		case CheckType::CheckType_checked:
            acpt->addString("checkType", __X("checked"));
            break;
    }
    if(m_type == eRoot)
        acpt->addUint32("total", getTotal());
    else
        acpt->addUint32("count", getTotal());
    acpt->addUint32("val", m_val);
    acpt->addUint32("index", m_nItemID);
}


void SerialisePivotFilterHelper::GenerateFilterTree(PivotFilterNode& root)
{
    if (!m_spFieldAxis)
        return;

    VARIANT_BOOL vb;
    m_spApiPivotField->get_EnableMultiplePageItems(&vb);
    bool bIsPage = m_spFieldAxis->GetFieldType() == pivot_core::AxisFieldPage;
    bool bIsMultipleSelect = VarBool_BOOL(vb);
    bool bSelectAll = false;
    ks_wstring currentPageName;
    if (!bIsMultipleSelect)
    {
        KComVariant currentPage;
        if (SUCCEEDED(m_spApiPivotField->get_CurrentPage(&currentPage)) && V_VT(&currentPage) == VT_BSTR)
        {
            currentPageName.assign(V_BSTR(&currentPage));
            if (currentPageName == TX_PIVOT_PAGE_ITEM_ALL)
                bSelectAll = true;
        }
    }

    pivot_core::ITblFieldAxisItems *pItems = m_spFieldAxis->GetFieldItems();
    int nItems = pItems->Count();
    for (int i = 0; i < nItems && i < 10000; i++)
    {
        ks_stdptr<pivot_core::ITblFieldAxisItem> spItem = pItems->Item(pivot_core::IDX_ITEM(i));
        if (spItem == NULL || !spItem->IsValid() ||
            (spItem->IsMissing() && !m_spFieldAxis->QueryFieldSrc()->GetProperty(pivot_core::FPT_ShowNoDataItems)))
             continue;

        if (m_searchData && !findWithWildChar(m_searchData, spItem->GetName().c_str()))
            continue;

        pivot_core::DATETIME_T dateTime{ 0 };
		
        bool bIsVisible = bIsPage && !bIsMultipleSelect ? 
                        (bSelectAll || spItem->GetName() == currentPageName) : 
                        spItem->IsVisible();
		if (spItem->GetDateTime(&dateTime))
		{
			root.appendDateChild(dateTime, i, bIsVisible);
		}
        else 
        {
            root.appendChild(i, spItem->GetName().c_str() , bIsVisible);
        }
    }
};

void SerialisePivotFilterHelper::MaintenanceCheckState(PivotFilterNode& root)
{
    root.GainCheckState();
}


} // wo
