﻿#include "et_dbsheet_syncsheet_utils.h"

namespace wo
{
namespace DbSyncSheet
{

HRESULT ClearSyncFieldFlag(IDBSheetOp* pDBSheetOp)
{
    if (!pDBSheetOp)
        return E_FAIL;
    
    IDbFieldsManager* pFieldManager = pDBSheetOp->GetFieldsManager();
    const IDBIds* pFieldIds = pDBSheetOp->GetAllFields();
    UINT colCnt = pFieldIds->Count();

	for (EtDbIdx i = 0; i < colCnt; ++i)
	{
        EtDbId fldId = pFieldIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		HRESULT hr = pFieldManager->GetField(fldId, &spField);
        if (FAILED(hr))
            continue;
        if(spField->IsSyncField())
            spField->SetSyncField(false);
    }

    return S_OK;
}

HRESULT ClearSyncRecordFlag(IDBSheetOp* pDBSheetOp)
{
    if (!pDBSheetOp)
        return E_FAIL;

    IDbRecordsManager *pRecordsManager = pDBSheetOp->GetRecordsManager();
	const IDBIds *pRecordIds = pDBSheetOp->GetAllRecords();
	UINT rowCnt = pRecordIds->Count();

	for (EtDbIdx i = 0; i < rowCnt; ++i)
	{
		EtDbId rowId = pRecordIds->IdAt(i);
		if(pRecordsManager->IsSyncRecord(rowId))
		{
			pRecordsManager->SetSyncRecord(rowId, false);
		}
    }
    return S_OK;
}

HRESULT ClearSyncFieldSourceFieldId(IDBSheetOp* pDBSheetOp)
{
    if (!pDBSheetOp)
        return E_FAIL;
    
    IDbFieldsManager* pFieldManager = pDBSheetOp->GetFieldsManager();
    const IDBIds* pFieldIds = pDBSheetOp->GetAllFields();
    UINT colCnt = pFieldIds->Count();

	for (EtDbIdx i = 0; i < colCnt; ++i)
	{
        EtDbId fldId = pFieldIds->IdAt(i);
		ks_stdptr<IDbField> spField;
		HRESULT hr = pFieldManager->GetField(fldId, &spField);
        if (FAILED(hr))
            continue;
        if(spField->GetSyncSourceFieldId() != nullptr)
            spField->SetSyncSourceFieldId(nullptr);
    }

    return S_OK;
}

}

} // wo
