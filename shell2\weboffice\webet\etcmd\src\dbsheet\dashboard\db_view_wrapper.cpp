﻿#include "etstdafx.h"
#include "workbook.h"
#include "db_view_wrapper.h"
#include "webbase/serialize.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "appcore/et_appcore_shared_link_sheet.h"

namespace wo
{

KDbDashboardViewWrapper::KDbDashboardViewWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension)
    : KDbDashboardModuleWrapper(pEtWorkbook, pWebExtension)
{
}

KDbDashboardModuleWrapper::Status KDbDashboardViewWrapper::GetStatus() const
{
    return Status::Normal;
}

HRESULT KDbDashboardViewWrapper::SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang,
                                             KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) const
{
    return S_OK;
}

HRESULT KDbDashboardViewWrapper::CreateView(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, IDBSheetView** ppView)
{
    if (!param.has("name") || !param.has("type") || !param.has("sheetId"))
        return E_INVALID_REQUEST;

	UINT sheetId = param.field_uint32("sheetId");
    ks_stdptr<IDBSheetViews> spDbSheetViews;
	DbSheet::GetDBSheetViews(m_pEtWorkbook->GetCoreWorkbook()->GetBook(), sheetId, &spDbSheetViews);
	if(!spDbSheetViews)
		return E_FAIL;

    PCWSTR name = param.field_str("name");
	ks_wstring nameStr;
	HRESULT hr = GetValidName(spDbSheetViews, name, nameStr);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
		return hr;
	name = nameStr.c_str();
    IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
    ET_DBSheet_ViewType type = DbSheet::ConvertViewType(pDecoder, param.field_str("type"));
	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = spDbSheetViews->CreateView(type, name, true, Et_DBSheetViewUse_ForDashboard, &spDbSheetView);
	if (FAILED(hr))
		return hr;
    if (spDbSheetView->IsExistParentRecord())
	{
		spDbSheetView->GetConstOrderManager()->ToggleForceSort();
	}

	ks_stdptr<IEtWebExtension_View> spWebExtView = m_spWebExtension;
	spWebExtView->SetSheetId(sheetId);
	spWebExtView->SetViewId(spDbSheetView->GetId());

    binary_wo::VarObj obj = param;
	hr = DbSheet::SetViewProp(spDbSheetViews, spDbSheetView, obj, pCtx);
	if (FAILED(hr))
		return hr;
	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	hr = pMgr->SetAutoSort(TRUE);
	if (FAILED(hr))
		return hr;

	*ppView = spDbSheetView.detach();
    return S_OK;
}

HRESULT KDbDashboardViewWrapper::CopyFrom(UINT sheetId, EtDbId viewId, IDBSheetView** ppView)
{
	if (sheetId == 0 || viewId == INV_EtDbId || !ppView)
		return E_FAIL;

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	DbSheet::GetDBSheetViews(m_pEtWorkbook->GetCoreWorkbook()->GetBook(), sheetId, &spDbSheetViews);
	if(!spDbSheetViews)
		return E_FAIL;

	ks_stdptr<IDBSheetView> spSrcView;
	spDbSheetViews->GetItemById(viewId, &spSrcView);
	if (!spSrcView)
		return E_FAIL;

	ks_wstring viewName;
	HRESULT hr = GetValidName(spDbSheetViews, spSrcView->GetName(), viewName);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
		return hr;
	ks_stdptr<IDBSheetView> spNewView;
	hr = spDbSheetViews->CreateView(spSrcView->GetType(), viewName.c_str(), false, Et_DBSheetViewUse_ForDashboard, &spNewView);
	if (FAILED(hr))
		return hr;

	hr = spSrcView->CopyTo(spNewView, TRUE, TRUE);
	if (FAILED(hr))
		return hr;

	*ppView = spNewView.detach();
	return S_OK;
}

HRESULT KDbDashboardViewWrapper::DeleteView()
{
	ks_stdptr<IEtWebExtension_View> spWebExtView = m_spWebExtension;
	if (spWebExtView->GetViewId() == 0)
		return S_OK;

    UINT sheetId = spWebExtView->GetSheetId();
    ks_stdptr<IDBSheetViews> spDbSheetViews;
	DbSheet::GetDBSheetViews(m_pEtWorkbook->GetCoreWorkbook()->GetBook(), sheetId, &spDbSheetViews);
	if(!spDbSheetViews)
		return E_FAIL;

	UINT viewId = spWebExtView->GetViewId();
    ks_stdptr<IDBSheetView> spDbSheetView;
	HRESULT hr = spDbSheetViews->GetItemById(viewId, &spDbSheetView);
	if (FAILED(hr))
		return E_FAIL;

	return spDbSheetViews->DelItem(viewId);
}

HRESULT KDbDashboardViewWrapper::ModifyView(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, IDBSheetView** ppView)
{
	if (!param.has("sheetId"))
		return E_INVALIDARG;

	UINT sheetId = param.field_uint32("sheetId");
	ks_stdptr<IEtWebExtension_View> spWebExtView = m_spWebExtension;
	if (sheetId == spWebExtView->GetSheetId())
		return S_OK;

	HRESULT hr = S_OK;
	if (spWebExtView->GetSheetId() != 0)
	{
		UINT oldSheetId = spWebExtView->GetSheetId();
		_Workbook* pWorkbook = m_pEtWorkbook->GetCoreWorkbook();
		ks_stdptr<IDBSheetViews> spOldDbSheetViews;
		DbSheet::GetDBSheetViews(pWorkbook->GetBook(), oldSheetId, &spOldDbSheetViews);
		if(!spOldDbSheetViews)
			return E_FAIL;

		EtDbId oldViewId = spWebExtView->GetViewId();
		IDX oldSheetIdx = INV_EtDbIdx;
		pWorkbook->GetBook()->STSheetToRTSheet(oldSheetId, &oldSheetIdx);
		ks_stdptr<_Worksheet> spWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(oldSheetIdx);
		hr = RemoveViewShared(spWorksheet, oldSheetId, oldViewId);
		if (FAILED(hr))
			return hr;
		hr = spOldDbSheetViews->DelItem(oldViewId);
		if (FAILED(hr))
			return hr;
	}
	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = CreateView(param, pCtx, &spDbSheetView);
	if (FAILED(hr))
		return hr;
	*ppView = spDbSheetView.detach();
	return S_OK;
}

HRESULT KDbDashboardViewWrapper::GetValidName(IDBSheetViews* pViews, PCWSTR pBaseName, ks_wstring& pValidName)
{
	HRESULT hr = pViews->IsValidViewName(pBaseName);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
		return hr;

    pValidName = pBaseName;
	if (hr == E_DBSHEET_VIEW_NAME_CONFLICT)
	{
		int postfix = 2;
		do
		{
			QString newName = QString::fromUtf16(pBaseName) + QString::number(postfix, 10);
			postfix++;
			pValidName = krt::utf16(newName);
		} while (E_DBSHEET_VIEW_NAME_CONFLICT == pViews->IsValidViewName(pValidName.c_str()));
	}
    return S_OK;
}

HRESULT KDbDashboardViewWrapper::SetViewShared(IKWorksheet* pWorksheet, UINT sheetId, EtDbId viewId)
{
	ISheetProtection* pSheetProtection = pWorksheet->GetProtection();
	if (pSheetProtection)
	{
		ISharedLinkSheet* pSharedLink = pSheetProtection->GetSharedLink();
		if (pSharedLink)
		{
			HRESULT hr = pSharedLink->SetDashboardViewId(sheetId, viewId);
			if (FAILED(hr))
				return hr;
		}
	}
	return S_OK;
}

HRESULT KDbDashboardViewWrapper::RemoveViewShared(IKWorksheet* pWorksheet, UINT sheetId, EtDbId viewId)
{
	ISheetProtection* pSheetProtection = pWorksheet->GetProtection();
	if (pSheetProtection)
	{
		ISharedLinkSheet* pSharedLink = pSheetProtection->GetSharedLink();
		if (pSharedLink)
		{
			HRESULT hr = pSharedLink->RemoveDashboardViewId(sheetId, viewId);
			if (FAILED(hr))
				return hr;
		}
	}
	return S_OK;
}

}