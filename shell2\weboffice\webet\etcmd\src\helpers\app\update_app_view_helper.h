﻿#ifndef __WEBET_UPDATE_APP_VIEW_HELPER__
#define __WEBET_UPDATE_APP_VIEW_HELPER__

#include "etstdafx.h"
#include "workbook.h"

class KEtWorkbook;

namespace UpdateAppViewHelper
{
	HRESULT UpdateAppSheetData(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, binary_wo::VarObj &param, ISheet* pDbSheet, OUT bool& needResetApp);
}//end namespace UpdateAppViewHelper

#endif // __WEBET_UPDATE_APP_VIEW_HELPER__
