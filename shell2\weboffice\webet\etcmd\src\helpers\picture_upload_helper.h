﻿#ifndef __PICTURE_UPLOAD_HELPER_H__
#define __PICTURE_UPLOAD_HELPER_H__

namespace drawing
{
	class AbstractShape;
}

namespace wo
{
class KEtWorkbook;

class Picture
{
public:
	virtual ~Picture() = default;
	virtual void update(IKBlipAtom*) = 0;
	virtual IKBlipAtom* GetBlipAtom() = 0;
	virtual drawing::Blip::LinkMode GetLinkMode() = 0;
};

class PictureUploadHelper
{
public:
    using SHA1         = QString;
    using AttachmentId = QString;
    using PicId        = IKBlipAtom*;
    enum class BroadcastType
    {
        partialSuccess, // 部分成功部分失败
        success,
        serverError,
        abort,
        converting,
        spaceFull
    };

	explicit PictureUploadHelper(KEtWorkbook*);

    void queryResponse(ISerialAcceptor* acpt);

    std::list<std::unique_ptr<Picture>> getAllShapes() const;

    void setUpdateShapes(std::function<void(const char*, const char*)> func);

    void setUploadDone() { m_childProcessUploading = false; }
    // 第二版 fork一个子进程出来做上传
    bool hasUploadablePicture();
    void childProcessExportUploadResult();
    void parentProcessApplyUploadResult(const WebSlice* recv);

	static bool IsAttachment(IKBlipAtom*, drawing::Blip::LinkMode);
	static bool IsSupportUploadFormat(IKBlipAtom*);
	static IKBlipAtom* GetBlipAtom(drawing::AbstractShape*);
	static PCWSTR GetUploadFormat(IKBlipAtom*);
	static bool GetPicture(IKBlipAtom*, QByteArray& sha1, QByteArray& picData);
    static bool hasUploaded();
    static void setUploaded(bool b);
private:
	void trimShapes(std::list<std::unique_ptr<Picture>>&) const;
    void broadcast(BroadcastType);

    void collectShapeInShapeTree(
      drawing::AbstractShape* shape,
      std::list<std::unique_ptr<Picture>>& container) const;

    void collectImageInCommentTree(
      ICellComments* shape,
      std::list<std::unique_ptr<Picture>>& container) const;

    bool saveResult(
      std::map<PicId, std::pair<std::list<std::unique_ptr<Picture>>,
                                AttachmentId>>& result);

    void trimShapes(std::list<std::unique_ptr<Picture>>& shapesToBeTrim,
                    size_t& attachmentShapeNum, size_t& emptyShapeNum,
                    size_t& unsupportFormatShapeNum) const;

    void updateShapes();

    KEtWorkbook* m_workbook;
    QString m_execCmdConnectId;
    QString m_execCmdUserId;
    std::function<void(const char*, const char*)> m_updateShapes;
    bool m_childProcessUploading;
	bool m_needBroadcast = true;

    std::set<SHA1> m_illegalOrTimeoutCache;
    std::chrono::time_point<std::chrono::steady_clock> m_beginUploadTimepoint;
    static bool m_uploaded;
};

} // namespace wo

#endif
