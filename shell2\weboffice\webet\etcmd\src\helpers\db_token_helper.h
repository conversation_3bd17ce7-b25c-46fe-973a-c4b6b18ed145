﻿#ifndef __DB_TOKEN_HELPER_H__
#define __DB_TOKEN_HELPER_H__

#include "webbase/binvariant/binvarobj.h"
#include "etcore/et_core_dbsheet.h"
#include "helpers/varobject_helper.h"
#include "kfc/string.h"
#include "db/db_basic_itf.h"

template <typename = void>
struct TokenArrayGenerator
{
    // generator 内部没有处理数组的就调用这个
    template <typename Generator>
    alg::managed_token_assist operator()(const binary_wo::VarObj& object, Generator&& generator) const
    {
        ks_stdptr<IDbTokenArrayHandle> spTokenArray;
        _db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle,
                reinterpret_cast<void**>(&spTokenArray));
        const UINT count = object.arrayLength_s();
        for (UINT i = 0; i < count; ++i)
        {
            const binary_wo::VarObj& subobject = object.at_s(i);
            managed_token_ptr result {};
            if (subobject.type() == binary_wo::typeArray)
                result = (*this)(subobject, std::forward<Generator>(generator)).detach();
            else
                result = generator(subobject).detach();
            if (result)
                spTokenArray->Add(result);
        }
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
        return alg::managed_token_assist {assist.detach()};
    }
};
struct EmptyArrayGenerator;
template <>
struct TokenArrayGenerator<EmptyArrayGenerator>
{
    // 产生空的数组
    template <typename Generator = void (*)()>
    alg::managed_token_assist operator()(const binary_wo::VarObj& = {}, Generator&& = []() {}) const
    {
        ks_stdptr<IDbTokenArrayHandle> spTokenArray;
        _db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle,
                reinterpret_cast<void**>(&spTokenArray));
        alg::managed_handle_token_assist assist;
        assist.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
        return alg::managed_token_assist {assist.detach()};
    }
};

namespace db_token_helper
{
 
struct TokenHelperBase
{
    const_token_ptr m_pToken;
    TokenHelperBase() noexcept = default;
    constexpr explicit TokenHelperBase(const_token_ptr token) noexcept : m_pToken(token) {}
};

template <DWORD TokenLiteral>
struct helper : private TokenHelperBase
{
    static_assert(
        static_cast<long>(TokenLiteral) < 0, // TokenLiteral 是 unsigned, 所以实例化到这里一定会出现编译错误
        "This token is not specialized, please implement it. "
        "You can copy this class with removing static_assert statement and "
        "implementing the declared only member functions below. "
        "Esspecially attention that whether the new specialization should "
        "add special functions or not."
    );
    using TokenHelperBase::TokenHelperBase;
    static WebStr type() noexcept;
    auto value() const;
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const;
    void serialize(ISerialAcceptor* acpt) const;
};

template <>
struct helper<alg::ETP_VINT> : private TokenHelperBase
{
    using TokenHelperBase::TokenHelperBase;
    static WebStr type() noexcept
    {
        return __X("ETP_VINT");
    }
    auto value() const
    {
        return alg::const_vint_token_assist(m_pToken).get_value();
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        const binary_wo::Types type = object.type();
        alg::managed_vint_token_assist assist;
        switch (object.type())
        {
        case binary_wo::typeInt8:
        case binary_wo::typeInt16:
        case binary_wo::typeInt32:
            assist.create(object.value_int32());
            return alg::managed_token_assist {assist.detach()};
        case binary_wo::typeUint8:
        case binary_wo::typeUint16:
        case binary_wo::typeUint32:
            assist.create(static_cast<int>(object.value_uint32()));
            return alg::managed_token_assist {assist.detach()};
        case binary_wo::typeFloat32:
        case binary_wo::typeFloat64:
            assist.create(static_cast<int>(object.value_double()));
            return alg::managed_token_assist {assist.detach()};
        default:
            break;
        }
        return {};
    }
    void serialize(ISerialAcceptor* acpt) const
    {
        acpt->beginStruct();
            acpt->addString("type", this->type());
            acpt->addInt32("value", this->value());
        acpt->endStruct();
    }
};

template <>
struct helper<alg::ETP_VDBL> : private TokenHelperBase
{
    using TokenHelperBase::TokenHelperBase;
    static WebStr type() noexcept
    {
        return __X("ETP_VDBL");
    }
    auto value() const
    {
        return alg::const_vdbl_token_assist(m_pToken).get_value();
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        const binary_wo::Types type = object.type();
        alg::managed_vdbl_token_assist assist;
        switch (object.type())
        {
        case binary_wo::typeInt8:
        case binary_wo::typeInt16:
        case binary_wo::typeInt32:
            assist.create(static_cast<double>(object.value_int32()));
            return alg::managed_token_assist {assist.detach()};
        case binary_wo::typeUint8:
        case binary_wo::typeUint16:
        case binary_wo::typeUint32:
            assist.create(static_cast<double>(object.value_uint32()));
            return alg::managed_token_assist {assist.detach()};
        case binary_wo::typeFloat32:
        case binary_wo::typeFloat64:
            assist.create(object.value_double());
            return alg::managed_token_assist {assist.detach()};
        default:
            break;
        }
        return {};
    }
    void serialize(ISerialAcceptor* acpt) const
    {
        acpt->beginStruct();
            acpt->addString("type", this->type());
            acpt->addFloat64("value", this->value());
        acpt->endStruct();
    }
};

template <>
struct helper<alg::ETP_VBOOL> : private TokenHelperBase
{
    using TokenHelperBase::TokenHelperBase;
    static WebStr type() noexcept
    {
        return __X("ETP_VBOOL");
    }
    auto value() const
    {
        return alg::const_vbool_token_assist(m_pToken).get_value();
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        const binary_wo::Types type = object.type();
        if (type == binary_wo::typeBool)
        {
            alg::managed_vbool_token_assist assist;
            assist.create(object.value_bool());
            return alg::managed_token_assist {assist.detach()};
        }
        return {};
    }
    void serialize(ISerialAcceptor* acpt) const
    {
        acpt->beginStruct();
            acpt->addString("type", this->type());
            acpt->addBool("value", this->value());
        acpt->endStruct();
    }
};

template <>
struct helper<alg::ETP_VSTR> : private TokenHelperBase
{
    using TokenHelperBase::TokenHelperBase;
    static WebStr type() noexcept
    {
        return __X("ETP_VSTR");
    }
    auto value() const
    {
        return alg::const_vstr_token_assist(m_pToken).get_value();
    }
    int length() const
    {
        return alg::const_vstr_token_assist(m_pToken).get_length();
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        const binary_wo::Types type = object.type();
        if (type == binary_wo::typeString)
        {
            alg::managed_vstr_token_assist assist;
            assist.create(object.value_str());
            return alg::managed_token_assist {assist.detach()};
        }
        return {};
    }
    void serialize(ISerialAcceptor* acpt) const
    {
        acpt->beginStruct();
            acpt->addString("type", this->type());
            acpt->addString("value", this->value());
        acpt->endStruct();
    }
};

template <>
struct helper<alg::ETP_ERROR> : private TokenHelperBase
{
    using TokenHelperBase::TokenHelperBase;
    static WebStr type() noexcept
    {
        return __X("ETP_ERROR");
    }
    auto value() const
    {
        return alg::const_error_token_assist(m_pToken).get_code();
    }
    DWORD subcode() const
    {
        return alg::const_error_token_assist(m_pToken).get_subcode();
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        const binary_wo::Types type = object.type();
        if (type == binary_wo::typeStruct)
        {
            using namespace wo::VarObjFieldValidation;
            if (expectNumeric(object, "code", false) and expectNumeric(object, "subcode", false))
            {
                alg::managed_error_token_assist assist;
                assist.create(object.field_int32("code"), object.field_int32("subcode"));
                return alg::managed_token_assist {assist.detach()};
            }
            else
            {
                return (*this)(object.get_s("value"));
            }
        }
        return {};
    }
    void serialize(ISerialAcceptor* acpt) const
    {
        acpt->beginStruct();
            acpt->addString("type", this->type());
            acpt->addKey("value", true);
            acpt->beginStruct();
                alg::const_error_token_assist assist(m_pToken);
                acpt->addInt32("code", assist.get_code());
                acpt->addInt32("subcode", assist.get_subcode());
            acpt->endStruct();
        acpt->endStruct();
    }
};

template <>
struct helper<alg::ETP_HANDLE> : private TokenHelperBase
{
private:
    static WebStr handleType(const_token_ptr pToken)
    {
        switch (alg::const_handle_token_assist {pToken}.get_handleType())
        {
        case alg::ET_HANDLE_DBLINK:
            return __X("ET_HANDLE_DBLINK");
        case alg::ET_HANDLE_TOKENARRAY:
            return __X("ET_HANDLE_TOKENARRAY");
        case alg::ET_HANDLE_CONTACT:
            return __X("ET_HANDLE_CONTACT");
        case alg::ET_HANDLE_DBATTACHMENT:
            return __X("ET_HANDLE_DBATTACHMENT");
        case alg::ET_HANDLE_DBNOTE:
            return __X("ET_HANDLE_DBNOTE");
        case alg::ET_HANDLE_DBADDRESS:
            return __X("ET_HANDLE_DBADDRESS");
        case alg::ET_HANDLE_SELECTITEMS:
            return __X("ET_HANDLE_SELECTITEMS");
        case alg::ET_HANDLE_QRLABEL:
            return __X("ET_HANDLE_QRLABEL");
        case alg::ET_HANDLE_DBHYPERLINK:
            return __X("ET_HANDLE_DBHYPERLINK");
        default:
            ASSERT("新加入的 handle token type 注意修改这里" == nullptr);
            return __X("ET_HANDLE_UNKNOWN");
        }
    }
    static void serialize_impl(ISerialAcceptor *acpt, const_token_ptr pToken)
    {
        switch (GetExecTokenMajorType(pToken))
        {
            using namespace alg;
        case ETP_VINT:
            helper<ETP_VINT> {pToken}.serialize(acpt);
            break;
        case ETP_VDBL:
            helper<ETP_VDBL> {pToken}.serialize(acpt);
            break;
        case ETP_VBOOL:
            helper<ETP_VBOOL> {pToken}.serialize(acpt);
            break;
        case ETP_VSTR:
            helper<ETP_VSTR> {pToken}.serialize(acpt);
            break;
        case ETP_ERROR:
            helper<ETP_ERROR> {pToken}.serialize(acpt);
            break;
        case ETP_HANDLE:
        {
            const auto type = alg::const_handle_token_assist {pToken}.get_handleType();
            if (type == alg::ET_HANDLE_TOKENARRAY)
            {
                helper<ETP_HANDLE> {pToken}.serialize(acpt);
            }
            else
            {
                switch (type)
                {
                case ET_HANDLE_DATATYPE:
                    ASSERT("你给我干哪来了?" == nullptr);
                    break;
                case ET_HANDLE_DBLINK:
                case ET_HANDLE_CONTACT:
                case ET_HANDLE_DBATTACHMENT:
                case ET_HANDLE_DBNOTE:
                case ET_HANDLE_DBADDRESS:
                case ET_HANDLE_SELECTITEMS:
                case ET_HANDLE_QRLABEL:
                case ET_HANDLE_DBHYPERLINK:
                {
                    const auto handle {alg::const_handle_token_assist {pToken}.get_handle()};
                    ASSERT(handle->CanConvertToString() == TRUE);
                    acpt->beginStruct();
                        acpt->addString("type", helper<alg::ETP_VSTR>::type());
                        acpt->addString("value", handle->GetConvertedString());
                    acpt->endStruct();
                    break;
                }
                default:
                    ASSERT("新增的 token 类型请实现这里" == nullptr);
                    break;
                }
            }
            break;
        }
        default:
            ASSERT("其它类型还未支持, 若需要请在此实现" == nullptr);
            break;
        }
    }
public:
    using TokenHelperBase::TokenHelperBase;
    static WebStr type() noexcept
    {
        return __X("ETP_HANDLE");
    }
    auto value() const
    {
        return alg::const_handle_token_assist(m_pToken).get_handle();
    }
    WebStr handleType() const
    {
        if (this->m_pToken == nullptr)
        {
            ASSERT("只给锅不给米就让我煮饭是吧" == nullptr);
            return __X("ET_HANDLE_UNKNOWN");
        }
        return this->handleType(this->m_pToken);
    }
    alg::managed_token_assist operator()(const binary_wo::VarObj& object) const
    {
        if (!wo::VarObjFieldValidation::expectString(object, "handleType"))
            return TokenArrayGenerator<EmptyArrayGenerator> {}();
        const binary_wo::VarObj& valueObj = object.get_s("value");
        // 服务端会拦掉带 null 的 binvar, 所以 value 为空的 handle token 在序列化时转换成了空字符串
        const auto inner_generator {[](const binary_wo::VarObj& object) -> alg::managed_token_assist {
            const WebStr type = object.field_str("type");
            if (xstrcmp(type, __X("ETP_VINT")) == 0)
                return alg::managed_token_assist {helper<alg::ETP_VINT> {}(object.get_s("value"))};
            else if (xstrcmp(type, __X("ETP_VDBL")) == 0)
                return alg::managed_token_assist {helper<alg::ETP_VDBL> {}(object.get_s("value"))};
            else if (xstrcmp(type, __X("ETP_VBOOL")) == 0)
                return alg::managed_token_assist {helper<alg::ETP_VBOOL> {}(object.get_s("value"))};
            else if (xstrcmp(type, __X("ETP_VSTR")) == 0)
                return alg::managed_token_assist {helper<alg::ETP_VSTR> {}(object.get_s("value"))};
            else if (xstrcmp(type, __X("ETP_ERROR")) == 0)
                return alg::managed_token_assist {helper<alg::ETP_ERROR> {}(object)};
            else if (xstrcmp(type, __X("ETP_HANDLE")) == 0)
                return alg::managed_token_assist {helper<alg::ETP_HANDLE> {}(object)};
           ASSERT("其它类型还未支持, 若需要请在此实现" == nullptr);
           return TokenArrayGenerator<EmptyArrayGenerator> {}();
        }};
        if (valueObj.type() == binary_wo::typeString)
            return TokenArrayGenerator<EmptyArrayGenerator> {}();
        else if (valueObj.type() == binary_wo::typeStruct)
            return inner_generator(valueObj);
        return TokenArrayGenerator<decltype(inner_generator)> {}(valueObj, inner_generator);
    }
    void serialize(ISerialAcceptor* acpt) const
    {
        if (this->m_pToken == nullptr)
        {
            ASSERT("只给锅不给米就让我煮饭是吧" == nullptr);
            return;
        }
        acpt->beginStruct();
            acpt->addString("type", this->type());
            acpt->addString("handleType", this->handleType());
            acpt->addKey("value", true);
            // value scope start
                alg::const_handle_token_assist assist {this->m_pToken};
                switch (assist.get_handleType())
                {
                case alg::ET_HANDLE_TOKENARRAY:
                {
                    const IDbTokenArrayHandle *const pTokenArrayHandle = assist.get_handle()->CastArray();
                    const UINT count = pTokenArrayHandle->GetCount();
                    if (count == 0)
                    {
                        acpt->addString(nullptr, __X(""));
                    }
                    else
                    {
                        acpt->beginArray();
                        for (UINT i = 0; i < count; ++i)
                        {
                            const_token_ptr token {};
                            pTokenArrayHandle->Item(i, &token);
                            this->serialize_impl(acpt, token);
                        }
                        acpt->endArray();
                    }
                    break;
                }
                default:
                    serialize_impl(acpt, m_pToken);
                    break;
                }
            // value scope end
        acpt->endStruct();
    }
};

}       // namespace db_token_helper

#endif // __DB_TOKEN_HELPER_H__