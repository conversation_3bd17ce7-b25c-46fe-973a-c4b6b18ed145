﻿#ifndef __WEBET_DB_DASHBOARDVIEW_WRAPPER_H__
#define __WEBET_DB_DASHBOARDVIEW_WRAPPER_H__

#include "appcore/et_appcore_dbsheet.h"
#include "db_dashboard_module_wrapper.h"

namespace wo
{

class KDbDashboardViewWrapper : public KDbDashboardModuleWrapper
{
public:
    explicit KDbDashboardViewWrapper(KEtWorkbook* pEtWorkbook, IKWebExtension* pWebExtension);
    KDbDashboardViewWrapper(const KDbDashboardViewWrapper&) = delete;
    KDbDashboardViewWrapper& operator=(const KDbDashboardViewWrapper&) = delete;
    Status GetStatus() const override;
    HRESULT SerializeContent(const binary_wo::VarObj& param, const binary_wo::VarObj& lang, KEtRevisionContext* pCtx,
                             ISerialAcceptor* pAcpt) const override;

    HRESULT CreateView(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, IDBSheetView**);
    HRESULT CopyFrom(UINT sheetId, EtDbId viewId, IDBSheetView**);
    HRESULT DeleteView();
    HRESULT ModifyView(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, IDBSheetView**);
    HRESULT SetViewShared(IKWorksheet* pWorksheet, UINT sheetId, EtDbId viewId);
    HRESULT RemoveViewShared(IKWorksheet* pWorksheet, UINT sheetId, EtDbId viewId);
private:
    HRESULT GetValidName(IDBSheetViews* pViews, PCWSTR pBaseName, ks_wstring& ppValidName);
};

};
#endif // __WEBET_DB_DASHBOARDVIEW_WRAPPER_H__