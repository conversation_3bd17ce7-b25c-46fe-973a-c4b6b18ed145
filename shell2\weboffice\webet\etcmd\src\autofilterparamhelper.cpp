﻿#include "appcore/et_appcore_basic_itf.h"
#include "appcore/et_appcore_cellcolor_itf.h"
#include "etcore/et_core_attr.h"
#include "etstdafx.h"
#include "autofilterparamhelper.h"
#include "etx_result.h"
#ifndef X_OS_WINDOWS
#include "kern/errno.h"
#endif
#include "kfc/com/sptr.h"
#include "kfc/datetime.h"
#include "et_hard_define_numbers.h"
#include "kfc/et_numfmt_str.h"
#include "webbase/binvariant/binwriter.h"
#include "webbase/binvariant/binvarobj.h"
#include "et_hard_define_modules.h"
#include "condition_format_helper.h"
#include "serialize_impl.h"
#include "et_task_detail_xf.h"
#include "wo/serial_xf_helper.h"
#include "xll/xlcall.h"

namespace wo
{

AutofilterParamSerialHelper::AutofilterParamSerialHelper()
	: m_nField(-1)
	, m_pAutoFilter(NULL)
	, m_pBook(NULL)
	, m_opt(opt_widget)
	, m_spFilterValues(NULL)
{

}


void AutofilterParamSerialHelper::InitForWidget(int nField, IKAutoFilter* pAutoFilter,
	IAutoFilterValues* pFilterValues, IBook* pBook)
{
	m_nField = nField;
	m_pAutoFilter = pAutoFilter;
	m_spFilterValues = pFilterValues;
	m_pBook = pBook;
	m_opt = opt_widget;
}

void AutofilterParamSerialHelper::InitForTips(int nField, IKAutoFilter* pAutoFilter, IBook* pBook)
{
	m_nField = nField;
	m_pAutoFilter = pAutoFilter;
	m_spFilterValues = NULL;
	m_pBook = pBook;
	m_opt = opt_tips;
}

void AutofilterParamSerialHelper::SerializeEmptyTips(ISerialAcceptor* acpt)
{
	acpt->addKey("condition");
	acpt->beginStruct();
	acpt->addString("operator", ETFilterOperatorToStr(FOp_None));
	acpt->addString("valList", __X(""));
	acpt->endStruct();
}

void AutofilterParamSerialHelper::Serialize(ISerialAcceptor* acpt, binary_wo::VarObj obj)
{
	ETFilterOperator fOp = m_pAutoFilter->GetOperator(m_nField);
	binary_wo::VarObj condition;
	if (obj.type() == binary_wo::typeInvalid)
	{
		acpt->addKey("condition");
		acpt->beginStruct();
		acpt->addString("operator", ETFilterOperatorToStr(fOp));
	}
	else
	{
		condition = obj.add_field_struct("condition");
		condition.add_field_str("operator", ETFilterOperatorToStr(fOp));
	}

	if (FOp_And == fOp || FOp_Or == fOp)
	{
		serializeCustom(fOp, acpt, condition);
	}
	else if (FOp_Bottom10Items == fOp || FOp_Bottom10Percent == fOp ||
		FOp_Top10Items == fOp || FOp_Top10Percent == fOp)
	{
		KCriteriaParam1 param1;
		m_pAutoFilter->GetCriteria1(m_nField, &param1);
		int val = 10;
		if (!param1.IsEmpty() && param1.GetParamType() == cptWcs)
		{
			QString strVal = QString::fromUtf16(param1.GetWcs());
			val = strVal.toInt();
		}
		if (condition.type() == binary_wo::typeInvalid)
			acpt->addFloat64("val", val);
		else
			condition.add_field_double("val", val);
	}
	else if (FOp_FilterDynamic == fOp)
	{
		serializeDynamic(acpt, condition);
	}
	else if (FOp_FilterValues == fOp)
	{
		if (m_opt == opt_tips && condition.type() == binary_wo::typeInvalid)
			serializeValuesForTips(acpt);
		else if (m_opt == opt_tips && condition.type() != binary_wo::typeInvalid)
			serializeValuesForTips(condition);
		else if (m_opt == opt_widget)
			serializeValuesForWidgit(acpt, condition);
		
	}
	else if (FOp_FilterCellColor == fOp)
	{
		serializeCellColor(acpt, condition);
	}
	else if (FOp_FilterFontColor == fOp)
	{
		serializeFontColor(acpt, condition);
	}
	else if (FOp_FilterIcon == fOp)
	{
		SerializeIcon(acpt, condition);
	}

	if (obj.type() == binary_wo::typeInvalid)
		acpt->endStruct();
}

PCWSTR AutofilterParamSerialHelper::CustomConditionTypeToStr(CUSTOM_CONDITION_TYPE ty)
{
	switch(ty)
	{
	case cct_null:
		return __X("null");
	case cct_equals:
		return __X("equals");
	case cct_notEqu:
		return __X("notEqu");
	case cct_greater:
		return __X("greater");
	case cct_greaterEqu:
		return __X("greaterEqu");
	case cct_less:
		return __X("less");
	case cct_lessEqu:
		return __X("lessEqu");
	case cct_beginWith:
		return __X("beginWith");
	case cct_notBeginWith:
		return __X("notBeginWith");
	case cct_endWith:
		return __X("endWith");
	case cct_notEndWith:
		return __X("notEndWith");
	case cct_contains:
		return __X("contains");
	case cct_notContains:
		return __X("notContains");
	case cct_between:
		return __X("between");
	case cct_notBetween:
		return __X("notBetween");
	default:
		ASSERT(FALSE);
		return __X("null");
	}
}

PCWSTR AutofilterParamSerialHelper::ETFilterOperatorToStr(ETFilterOperator ty)
{
	switch(ty)
	{
	case FOp_None:
		return __X("none");
	case FOp_And:
		return __X("and");
	case FOp_Bottom10Items:
		return __X("bottom10Items");
	case FOp_Bottom10Percent:
		return __X("bottom10Percent");
	case FOp_Or:
		return __X("or");
	case FOp_Top10Items:
		return __X("top10Items");
	case FOp_Top10Percent:
		return __X("top10Percent");
	case FOp_FilterCellColor:
		return __X("filterCellColor");
	case FOp_FilterDynamic:
		return __X("filterDynamic");
	case FOp_FilterFontColor:
		return __X("filterFontColor");
	case FOp_FilterIcon:
		return __X("filterIcon");
	case FOp_FilterValues:
		return __X("filterValues");
	case FOp_FilterNoFill:
		return __X("filterNoFill");
	case FOp_FilterAutomaticFontColor:
		return __X("filterAutomaticFontColor");
	case FOp_FilterNoIcon:
		return __X("filterNoIcon");
	default:
		ASSERT(FALSE);
		return __X("none");
	};
}

PCWSTR AutofilterParamSerialHelper::DynamicFilterCriteriaToStr(ET_DYNAMIC_FILTER_CRITERIA ty)
{
	switch(ty)
	{		
	case dfc_None:
		return __X("none");
	case dfc_FilterToday:
		return __X("today");
	case dfc_FilterYesterday:
		return __X("yesterday");
	case dfc_FilterTomorrow:
		return __X("tomorrow");
	case dfc_FilterThisWeek:
		return __X("thisWeek");
	case dfc_FilterLastWeek:
		return __X("lastWeek");
	case dfc_FilterNextWeek:
		return __X("nextWeek");
	case dfc_FilterThisMonth:
		return __X("thisMonth");
	case dfc_FilterLastMonth:
		return __X("lastMonth");
	case dfc_FilterNextMonth:
		return __X("nextMonth");
	case dfc_FilterThisQuarter:
		return __X("thisQuarter");
	case dfc_FilterLastQuarter:
		return __X("lastQuarter");
	case dfc_FilterNextQuarter:
		return __X("nextQuarter");
	case dfc_FilterThisYear:
		return __X("thisYear");
	case dfc_FilterLastYear:
		return __X("lastYear");
	case dfc_FilterNextYear:
		return __X("nextYear");
	case dfc_FilterYearToDate:
		return __X("yearToDate");
	case dfc_FilterAllDatesInPeriodQuarter1:
		return __X("allDatesInPeriodQuarter1");
	case dfc_FilterAllDatesInPeriodQuarter2:
		return __X("allDatesInPeriodQuarter2");
	case dfc_FilterAllDatesInPeriodQuarter3:
		return __X("allDatesInPeriodQuarter3");
	case dfc_FilterAllDatesInPeriodQuarter4:
		return __X("allDatesInPeriodQuarter4");
	case dfc_FilterAllDatesInPeriodJanuary:
		return __X("allDatesInPeriodJanuary");
	case dfc_FilterAllDatesInPeriodFebruray:
		return __X("allDatesInPeriodFebruray");
	case dfc_FilterAllDatesInPeriodMarch:
		return __X("allDatesInPeriodMarch");
	case dfc_FilterAllDatesInPeriodApril:
		return __X("allDatesInPeriodApril");
	case dfc_FilterAllDatesInPeriodMay:
		return __X("allDatesInPeriodMay");
	case dfc_FilterAllDatesInPeriodJune:
		return __X("allDatesInPeriodJune");
	case dfc_FilterAllDatesInPeriodJuly:
		return __X("allDatesInPeriodJuly");
	case dfc_FilterAllDatesInPeriodAugust:
		return __X("allDatesInPeriodAugust");
	case dfc_FilterAllDatesInPeriodSeptember:
		return __X("allDatesInPeriodSeptember");
	case dfc_FilterAllDatesInPeriodOctober:
		return __X("allDatesInPeriodOctober");
	case dfc_FilterAllDatesInPeriodNovember:
		return __X("allDatesInPeriodNovember");
	case dfc_FilterAllDatesInPeriodDecember:
		return __X("allDatesInPeriodDecember");
	case dfc_FilterAboveAverage:
		return __X("aboveAverage");
	case dfc_FilterBelowAverage:
		return __X("belowAverage");
	default:
		ASSERT(FALSE);
		return __X("none");
	}
}

void AutofilterParamSerialHelper::serializeValuesForWidgit(ISerialAcceptor* acpt, binary_wo::VarObj obj)
{
	if (m_spFilterValues == NULL) return;
	ET_CUSTOM_FILTER_TYPE cfType = m_spFilterValues->GetValuesType();
	if (customFilterType_Number  == cfType || customFilterType_NULL == cfType)
	{
		int nCount = 0;
		ks_bstr bstr;
		KCriteriaParam1 param1;
		m_pAutoFilter->GetCriteria1(m_nField, &param1);
		ICriteriaTextSet *pTextSet = param1.GetTextSet();
		nCount = pTextSet->GetCount();
		//前端对话框使用，只有2个以下时的本字值筛选才有
		if (1 == nCount)
		{
			pTextSet->GetItem(0, &bstr);
			if (obj.type() == binary_wo::typeInvalid)
				acpt->addString("comboBox1", bstr);
			else
				obj.add_field_str("comboBox1", bstr.c_str());
		}
		else if (2 == nCount)
		{
			pTextSet->GetItem(0, &bstr);
			if (obj.type() == binary_wo::typeInvalid)
				acpt->addString("comboBox1", bstr);
			else
				obj.add_field_str("comboBox1", bstr.c_str());
			bstr.clear();
			pTextSet->GetItem(1, &bstr);
			if (obj.type() == binary_wo::typeInvalid)
				acpt->addString("comboBox2", bstr);
			else
				obj.add_field_str("comboBox2", bstr.c_str());
		}
	}
}

void AutofilterParamSerialHelper::serializeValuesForTips(ISerialAcceptor* acpt)
{
	constexpr int MaxLen = 50;
	KCriteriaParam1 param1;
	m_pAutoFilter->GetCriteria1(m_nField, &param1);
	ICriteriaTextSet *pTextSet = param1.GetTextSet();
	ks_bstr bstr;
	ks_wstring strList;
	if (pTextSet)
	{
		for (int i = 0, cnt = pTextSet->GetCount(); i < cnt; ++i)
		{
			bstr.clear();
			pTextSet->GetItem(i, &bstr);
			if (i != 0)
				strList.append(__X(","));
			strList.append(bstr.c_str());
			if (strList.length() > MaxLen)
				break;
		}
	}
	if (strList.length() < MaxLen)
	{
		KCriteriaParam2 param2;
		m_pAutoFilter->GetCriteria2(m_nField, &param2);
		ICriteriaDateList *pList = param2.GetDateList();
		if (pList)
		{
			ICriteriaDateList::Iterator* it = pList->Begin();

			for ( ; !pList->IsEnd(it); it = it->Next())
			{
				ETDateTimeGrouping dtGrouping;
				TIMEINFO t;
				it->GetValue(dtGrouping, t);
				if (!strList.empty())
					strList.append(__X(","));
				switch(dtGrouping)
				{
					case dtg_Second:
						strList.AppendFormat(__X("%d-%d-%d %d:%d:%d"),t.tm_year, t.tm_mon, t.tm_mday, t.tm_hour, t.tm_min, t.tm_sec);
						break;
					case dtg_Minute:
						strList.AppendFormat(__X("%d-%d-%d %d:%d"),t.tm_year, t.tm_mon, t.tm_mday, t.tm_hour, t.tm_min);
						break;
					case dtg_Hour:
						strList.AppendFormat(__X("%d-%d-%d %d"),t.tm_year, t.tm_mon, t.tm_mday, t.tm_hour);
						break;
					case dtg_Day:
						strList.AppendFormat(__X("%d-%d-%d"),t.tm_year, t.tm_mon, t.tm_mday);
						break;
					case dtg_Month:
						strList.AppendFormat(__X("%d-%d"),t.tm_year, t.tm_mon);
						break;
					case dtg_Year:
						strList.AppendFormat(__X("%d"),t.tm_year);
						break;
				}
				if (strList.length() > MaxLen)
					break;
			}

			pList->DestroyIterator(it);
		}
	}

	if (strList.length() > MaxLen)
		strList.resize(MaxLen);

	acpt->addString("valList", strList.c_str());
	acpt->addBool("cut", strList.length() > MaxLen);
}

void AutofilterParamSerialHelper::serializeValuesForTips(binary_wo::VarObj& resObj)
{
	binary_wo::VarObj objValues = resObj.add_field_array("values", binary_wo::typeStruct);
	KCriteriaParam1 param1;
	m_pAutoFilter->GetCriteria1(m_nField, &param1);
	serializeTextSet(param1.GetTextSet(), objValues);

	KCriteriaParam2 param2;
	m_pAutoFilter->GetCriteria2(m_nField, &param2);
	serializeDateList(param2.GetDateList(), objValues);
}

void AutofilterParamSerialHelper::serializeTextSet(ICriteriaTextSet* pTextSet, binary_wo::VarObj& resObj)
{
	if (NULL == pTextSet)
		return;
	ks_bstr bstr;
	for (int i = 0, cnt = pTextSet->GetCount(); i < cnt; ++i)
	{
		bstr.clear();
		pTextSet->GetItem(i, &bstr);
		binary_wo::VarObj objItem = resObj.add_item_struct();
		objItem.add_field_str("type", __X("text"));
		objItem.add_field_str("text", bstr.c_str());
	}
}

void AutofilterParamSerialHelper::serializeDateList(ICriteriaDateList* pList, binary_wo::VarObj& resObj)
{
	if (NULL == pList)
		return;
	ICriteriaDateList::Iterator* it = pList->Begin();
	for (; !pList->IsEnd(it); it = it->Next())
	{
		binary_wo::VarObj objItem = resObj.add_item_struct();
		ETDateTimeGrouping dtGrouping;
		TIMEINFO timeInfo = { -1, -1, -1, -1, -1, -1, -1, -1, -1 };
		it->GetValue(dtGrouping, timeInfo);
		objItem.add_field_str("type", __X("date"));
		objItem.add_field_str("group", ETDataTimeGroupingToStr(dtGrouping));
		switch (dtGrouping)
		{
		case dtg_Second:
			objItem.add_field_int32("second", timeInfo.tm_sec);
		case dtg_Minute:
			objItem.add_field_int32("minute", timeInfo.tm_min);
		case dtg_Hour:
			objItem.add_field_int32("hour", timeInfo.tm_hour);
		case dtg_Day:
			objItem.add_field_int32("day", timeInfo.tm_mday);
		case dtg_Month:
			objItem.add_field_int32("month", timeInfo.tm_mon);
		case dtg_Year:
			objItem.add_field_int32("year", timeInfo.tm_year);
			break;
		default:
			ASSERT(FALSE);
			break;
		}
	}
}

PCWSTR AutofilterParamSerialHelper::ETDataTimeGroupingToStr(ETDateTimeGrouping dtGrouping)
{
	switch (dtGrouping)
	{
	case dtg_Year:
		return __X("year");
	case dtg_Month:
		return __X("month");
	case dtg_Day:
		return __X("day");
	case dtg_Hour:
		return __X("hour");
	case dtg_Minute:
		return __X("minute");
	case dtg_Second:
		return __X("second");
	default:
		ASSERT(FALSE);
		return __X("year");
	}
}

void AutofilterParamSerialHelper::serializeDynamic(ISerialAcceptor* acpt, binary_wo::VarObj obj)
{
	KCriteriaParam1 param1;
	m_pAutoFilter->GetCriteria1(m_nField, &param1);
	ET_DYNAMIC_FILTER_CRITERIA dfc = dfc_None;
	if (param1.GetParamType() == cptDfc)
		dfc = param1.GetDfc();

	if (obj.type() == binary_wo::typeInvalid)
		acpt->addString("dynamicCriteria", DynamicFilterCriteriaToStr(dfc));
	else
		obj.add_field_str("dynamicCriteria", DynamicFilterCriteriaToStr(dfc));

	int condIdx1 = 0;
	int condIdx2 = 0;
	double defData1 = 0.0;
	double defData2 = 0.0;
	m_pAutoFilter->GetDynamicValue(m_nField, &defData1, &defData2);
	if (dfc_FilterAboveAverage == dfc || dfc_FilterBelowAverage == dfc)
	{
		if (obj.type() == binary_wo::typeInvalid)
			acpt->addFloat64("average", defData1);
		else
			obj.add_field_double("average", defData1);
	}
	else if(dfc_FilterToday	== dfc ||
		dfc_FilterYesterday	== dfc ||
		dfc_FilterTomorrow	== dfc ||
		dfc_FilterThisWeek	== dfc ||
		dfc_FilterLastWeek	== dfc ||
		dfc_FilterNextWeek	== dfc ||
		dfc_FilterThisMonth	== dfc ||
		dfc_FilterLastMonth	== dfc ||
		dfc_FilterNextMonth	== dfc ||
		dfc_FilterThisQuarter == dfc ||
		dfc_FilterLastQuarter == dfc ||
		dfc_FilterNextQuarter == dfc ||
		dfc_FilterThisYear == dfc ||
		dfc_FilterLastYear == dfc ||
		dfc_FilterNextYear == dfc)
	{

		BOOL b1904 = m_pBook->Is1904DateSystem();
		DATE date = _XDateFromDouble(defData1);
		TIMEINFO timeInf;
		_XTmFromDate2(date, b1904, FALSE, 0, timeInf, NULL, NULL);
		if (obj.type() == binary_wo::typeInvalid)
		{
			acpt->addKey("date1");
			acpt->beginStruct();
			acpt->addInt16("year", timeInf.tm_year);
			acpt->addInt8("month", timeInf.tm_mon);
			acpt->addInt8("day", timeInf.tm_mday);
			acpt->endStruct();

			date = _XDateFromDouble(defData2);
			_XTmFromDate2(date, b1904, FALSE, 0, timeInf, NULL, NULL);
			acpt->addKey("date2");
			acpt->beginStruct();
			acpt->addInt16("year", timeInf.tm_year);
			acpt->addInt8("month", timeInf.tm_mon);
			acpt->addInt8("day", timeInf.tm_mday);
			acpt->endStruct();
		}
		else
		{
			binary_wo::VarObj date1 = obj.add_field_struct("date1");
			date1.add_field_int16("year", timeInf.tm_year);
			date1.add_field_int8("month", timeInf.tm_mon);
			date1.add_field_int8("day", timeInf.tm_mday);

			date = _XDateFromDouble(defData2);
			_XTmFromDate2(date, b1904, FALSE, 0, timeInf, NULL, NULL);
			binary_wo::VarObj date2 = obj.add_field_struct("date2");
			date2.add_field_int16("year", timeInf.tm_year);
			date2.add_field_int8("month", timeInf.tm_mon);
			date2.add_field_int8("day", timeInf.tm_mday);
		}
	}
}

void AutofilterParamSerialHelper::serializeCellColor(ISerialAcceptor* acpt, binary_wo::VarObj obj)
{
	KCriteriaParam1 param1;
	m_pAutoFilter->GetCriteria1(m_nField, &param1);
	if (param1.GetParamType() != cptCellColor)
		return;
	IFilterCellColorItems* pCellColors = param1.GetCellColors();
	if (!pCellColors || pCellColors->IsEmpty())
		return;
	
	const auto getFill = [&](int index)
	{
		ks_stdptr<ICellColor> spCellColor;
		pCellColors->GetItem(index, &spCellColor);
		const EtFill *pFill = nullptr;
		spCellColor->GetFill(&pFill);
		return pFill;
	};
	KXFMASK mask(KXFMASK::_catFills);
	if (obj.type() == binary_wo::typeInvalid)
	{
		acpt->addKey("fills");
		acpt->beginArray();
		for (int i = 0, count = pCellColors->GetItemCount(); i < count; ++i)
		{
			optFill(acpt, nullptr, getFill(i), mask, m_pBook);
		}
		acpt->endArray();
	}
	else
	{
		binary_wo::VarObj fillsObj = obj.add_field_array("fills", binary_wo::typeStruct);
		for (int i = 0, count = pCellColors->GetItemCount(); i < count; ++i)
		{
			binary_wo::VarObj fillObj = fillsObj.add_item_struct();
			optFill(fillObj, nullptr, getFill(i), mask, m_pBook);
		}
	}
}

void AutofilterParamSerialHelper::serializeFontColor(ISerialAcceptor* acpt, binary_wo::VarObj obj)
{
	KCriteriaParam1 param1;
	m_pAutoFilter->GetCriteria1(m_nField, &param1);
	if (param1.GetParamType() != cptFontColor)
		return;
	IFilterFontColorItems* pFontColors = param1.GetFontColors();
	if (!pFontColors || pFontColors->IsEmpty())
		return;

	const auto getColor = [&](int index, BOOL& bIsAuto)
	{
		ks_stdptr<IFontColor> spFontColor;
		pFontColors->GetItem(index, &spFontColor);
		const EtColor* pColor = nullptr;
		spFontColor->GetColor(&pColor);
		bIsAuto = spFontColor->IsAuto();
		return pColor;
	};

	BOOL bIsAuto = FALSE;
	if (obj.type() == binary_wo::typeInvalid)
	{
		acpt->addKey("colors");
		acpt->beginArray();
		for (int i = 0, count = pFontColors->GetItemCount(); i < count; ++i)
		{
			const EtColor* pColor = getColor(i, bIsAuto);
			wo::SerialColour(acpt, nullptr, m_pBook, *pColor, GdiAutoTextColor, bIsAuto);
		}
		acpt->endArray();
	}
	else
	{
		binary_wo::VarObj colorsObj = obj.add_field_array("colors", binary_wo::typeStruct);
		for (int i = 0, count = pFontColors->GetItemCount(); i < count; ++i)
		{
			binary_wo::VarObj colorObj = colorsObj.add_item_struct();
			wo::SerialColour(colorObj, nullptr, m_pBook, *getColor(i, bIsAuto));
		}
	}
}

void AutofilterParamSerialHelper::SerializeIcon(ISerialAcceptor* acpt, binary_wo::VarObj obj)
{
	KCriteriaParam1 param1;
	m_pAutoFilter->GetCriteria1(m_nField, &param1);
	if (param1.GetParamType() != cptIcon)
		return;
	IFilterIconItems* pIcons = param1.GetIcons();
	if (!pIcons || pIcons->IsEmpty())
		return;

	const auto getIcon = [&](int index)
	{
		ks_stdptr<ICellIcon> spIcon;
		pIcons->GetItem(index, &spIcon);
		const EtIconSet* pIconSet = nullptr;
		spIcon->GetIcon(&pIconSet);
		return pIconSet;
	};
	
	if (obj.type() == binary_wo::typeInvalid)
	{
		acpt->addKey("iconSets");
		acpt->beginArray();
		for (int i = 0, count = pIcons->GetItemCount(); i < count; ++i)
		{
			optIconSet(acpt, obj, nullptr, getIcon(i));
		}
		acpt->endArray();
	}
	else
	{
		binary_wo::VarObj iconsObj = obj.add_field_array("iconSets", binary_wo::typeStruct);
		for (int i = 0, count = pIcons->GetItemCount(); i < count; ++i)
		{
			binary_wo::VarObj iconObj = iconsObj.add_item_struct();
			optIconSet(acpt, iconObj, nullptr, getIcon(i));
		}
	}
}

void AutofilterParamSerialHelper::serializeCustom(ETFilterOperator fOp, ISerialAcceptor* acpt, binary_wo::VarObj obj)
{
	KCriteriaParam1 param1;
	m_pAutoFilter->GetCriteria1(m_nField, &param1);
	KCriteriaParam2 param2;
	m_pAutoFilter->GetCriteria2(m_nField, &param2);

	QString val;
	CUSTOM_CONDITION_TYPE nIndex;
	bool bTop10Disabled = (FOp_And == fOp ||FOp_Or == fOp);
	
	if (m_spFilterValues == NULL)
	{
		ValuesNode* pRoot = NULL;
		HRESULT hr = m_pAutoFilter->GetFilterItems(m_nField, ETStringToolsOpt_None, TRUE, EOp_None, &m_spFilterValues, &pRoot);
		if (FAILED(hr))
			return;
	}
	ET_CUSTOM_FILTER_TYPE filterType = m_spFilterValues->GetValuesType();

	if (ParserCustomCriteria(QString::fromUtf16(param1.GetWcs()), filterType, nIndex, val)
		&& bTop10Disabled)
	{
		if (obj.type() == binary_wo::typeInvalid)
		{
			acpt->addKey("param1");
			acpt->beginStruct();
			acpt->addString("customType", CustomConditionTypeToStr(nIndex));
			acpt->addString("val", krt::utf16(val.remove((QChar)0x0a)));
			acpt->endStruct();
		}
		else
		{
			binary_wo::VarObj objParam1 = obj.add_field_struct("param1");
			objParam1.add_field_str("customType", CustomConditionTypeToStr(nIndex));
			objParam1.add_field_str("val", krt::utf16(val.remove((QChar)0x0a)));
		}
	}

	if (ParserCustomCriteria(QString::fromUtf16(param2.GetWcs()), filterType, nIndex, val)
		&& bTop10Disabled)
	{
		if (obj.type() == binary_wo::typeInvalid)
		{
			acpt->addKey("param2");
			acpt->beginStruct();
			acpt->addString("customType", CustomConditionTypeToStr(nIndex));
			acpt->addString("val", krt::utf16(val.remove((QChar)0x0a)));
			acpt->endStruct();
		}
		else
		{
			binary_wo::VarObj objParam2 = obj.add_field_struct("param2");
			objParam2.add_field_str("customType", CustomConditionTypeToStr(nIndex));
			objParam2.add_field_str("val", krt::utf16(val.remove((QChar)0x0a)));
		}
	}
}

BOOL AutofilterParamSerialHelper::ParserCustomCriteria(const QString& str, ET_CUSTOM_FILTER_TYPE filterType, 
	CUSTOM_CONDITION_TYPE& condType, QString& val)
{
	condType = cct_null;
	val = str;

	if (str.isEmpty())
		return false;

	//参见untransition
	int len = str.length();
	if ('=' == str[0])
	{
		if( 2 == len && '*' == str[1])
		{
			condType = cct_equals;
			val = "*";
		}
		else if (len >=3 && '*' == str[len - 1] && '*' == str[1])
		{//=*XXX*
			condType = cct_contains;
			val = str.mid(2, len - 3);
		}
		else if (len >= 2 && '*' == str[len -1])
		{//=XXX*
			condType = cct_beginWith;
			val = str.mid(1, len - 2);
		}
		else if (len >= 2 && '*' == str[1])
		{//=*XXXX
			condType = cct_endWith;
			val = str.mid(2, len - 2);
		}
		else 
		{//=XXXX
			condType = cct_equals;
			val = str.mid(1, len - 1);
		}
	}
	else if ('<' == str[0])
	{
		if (len >= 2 && '>' == str[1])
		{//<>
			if(3 == len && '*' == str[2])
			{
				condType = cct_notEqu;
				val = "*";
			}
			else if (len >= 4 && '*' == str[len - 1] && '*' == str[2])
			{//<>*XXXX*
				condType = cct_notContains;
				val = str.mid(3, len - 4);
			}
			else if (len >= 3 && '*' == str[len - 1])
			{//<>XXXX*
				condType = cct_notBeginWith;
				val = str.mid(2, len - 3);
			}
			else if (len >= 3 && '*' == str[2])
			{//<>*XXXX
				condType = cct_notEndWith;
				val = str.mid(3, len -3);
			}
			else
			{//<>XXXX
				condType = cct_notEqu;
				val = str.mid(2, len - 2);
			}
		}
		else if (len >= 2 && '=' == str[1])
		{//<=XXX
			condType = cct_lessEqu;
			val = str.mid(2, len - 2);
		}
		else
		{//<
			condType = cct_less;
			val = str.mid(1, len - 1);
		}
	}
	else if ('>' == str[0])
	{
		condType = cct_greater;
		val = str.mid(1, len - 1);
		if (len >= 2 && '=' == str[1])
		{//>=
			condType = cct_greaterEqu;
			val = str.mid(2, len - 2);
		}
	}

	if (filterType == customFilterType_Date)
	{
		doubleStrToDate(val);
	}
	return true;
}

bool AutofilterParamSerialHelper::doubleStrToDate(QString& val)
{
	bool ok = false;
	double dbl = val.toDouble(&ok);
	if (!ok)
		return false;

	BOOL b1904 = m_pBook->Is1904DateSystem();
	int iYear, iMonth, iday;
	int ihour, imin, isec, iDummy;
	HRESULT hr = etexec::VDS_ParseTime(dbl, b1904, &iYear, &iMonth, &iday, &ihour, &imin, &isec, &iDummy);
	if (!SUCCEEDED(hr))
		return false;

	KComVariant var;
	var.AssignDouble(dbl);
	ks_bstr bstr;
	if (ihour > 0 || imin > 0 || isec > 0)
	{
		switch (kfc::nf::_XNFGetLangVersion())
		{
		case ET_LANGUAGE_AREA_1041:
		case ET_LANGUAGE_AREA_2057:
		case ET_LANGUAGE_AREA_3076:
		case ET_LANGUAGE_AREA_2052:
			kfc::nf::_XNFFormat2(var, b1904, kfc::nf::_XNFGetEtStr(Et_NFUIL_09), &bstr, NULL, NULL);
			break;
		default:
			kfc::nf::_XNFFormat2(var, b1904, kfc::nf::_XNFGetEtStr(Et_NFUIL_03), &bstr, NULL, NULL);
			break;
		}
	}
	else
		kfc::nf::_XNFFormat2(var, b1904, kfc::nf::_XNFGetEtStr(Et_BUILDIN_NF_DATE1), &bstr, NULL, NULL);
	val = QString::fromUtf16(bstr.c_str());
	return true;
}

///////////////////////////////////////////////////////////
AutofilterParamGenerator::AutofilterParamGenerator(binary_wo::VarObj condition, IBook* pBook, ValuesNode* pRootNode)
	: m_fop(FOp_None)
	, m_pBook(pBook)
    , m_pRootNode(pRootNode)
{
	m_fop = strToETFilterOperator(condition.field_str("operator"));
	if (FOp_And == m_fop || FOp_Or == m_fop)
	{
		genCustom(condition);
	}
	else if (FOp_Bottom10Items == m_fop || FOp_Bottom10Percent == m_fop ||
		FOp_Top10Items == m_fop || FOp_Top10Percent == m_fop)
	{
		m_param1.SetWcs(condition.field_str("threshold"));
	}
	else if (FOp_FilterDynamic == m_fop)
	{
		ET_DYNAMIC_FILTER_CRITERIA dfc = strToDynamicFilterCriteria(condition.field_str("dynamicCriteria"));
		m_param1.SetDfc(dfc);
	}
	else if (FOp_FilterValues == m_fop)
	{
		genValues(condition);
	}
	else if (FOp_FilterCellColor == m_fop)
	{
		genCellColor(condition);
	}
	else if (FOp_FilterFontColor == m_fop)
	{
		genFontColor(condition);
	}
	else if (FOp_FilterIcon == m_fop)
	{
		genCellIcon(condition);
	}
}

void AutofilterParamGenerator::genValues(binary_wo::VarObj condition)
{
    enum AllCheckedType {
        unknow = 0,         // 全选状态不明确
        checkedAll,         // 点击过全选
        unCheckedAll,       // 取消过全选
    };
	
    AllCheckedType allCheckedType = unknow;       // 全选的状态
    bool bOnlyFilterThis = false;   // 仅筛选此项
    bool bForSearch = false;        // 是否正在搜索
    bool bSearchAdd = false;        // 添加到筛选器
    int lastNodeIndex = -1;
	if (condition.has("filterBy")) return;
    if (condition.has("allCheckedType"))
    {
        PCWSTR strAllCheckedType = condition.field_str("allCheckedType");
        if (xstrcmp(strAllCheckedType, __X("checkedAll")) == 0)
            allCheckedType = checkedAll;
        else if (xstrcmp(strAllCheckedType, __X("unCheckedAll")) == 0)
            allCheckedType = unCheckedAll;
    }
    if (condition.has("onlyFilterThis"))
        bOnlyFilterThis = condition.field_bool("onlyFilterThis");
    if (condition.has("forSearch"))
        bForSearch = condition.field_bool("forSearch");
    if (condition.has("searchAdd")) 
        bSearchAdd = condition.field_bool("searchAdd");
    if (condition.has("end"))
        lastNodeIndex = condition.field_int32("end") + 2;

    byte ITEM_VISIBLE = 0x4;

    _appcore_CreateObject(CLSID_KCriteriaDateList, IID_ICriteriaDateList, (void**)&m_spCriteriaDateList);
	_appcore_CreateObject(CLSID_KCriteriaTextSet, IID_ICriteriaTextSet, (void**)&m_spCriteriaTextSet);

    // 添加到筛选器：上一次筛选的结果，先踢出掉搜索匹配项，在加上搜索勾选项。  搜索勾选项分2部分：可见部分+超限部分
    if (m_pRootNode && bForSearch && bSearchAdd)
    {
        int count = m_pRootNode->childs.size();
        for (int i = 2; i < count; ++i)
        {
            ValuesNode* pNode_1 = m_pRootNode->childs[i];
            bool bValid = (pNode_1->GetCheckType() != 0);
            if (!bValid)
                continue;
            if (pNode_1->childs.size() > 0) 
            {
                // 年
                ETDateTimeGrouping group = dtg_Year;
                TIMEINFO timeInfo = {-1,-1,-1,-1,-1,-1};
                timeInfo.tm_year = pNode_1->dblVal;
                if (pNode_1->childs.size() > 0)
                {
                    // 月
                    for (int j = 0; j < pNode_1->childs.size(); ++j)
                    {
                        ValuesNode* pNode_2 = pNode_1->childs[j];
                        bValid = (pNode_2->GetCheckType() != 0);
                        if (!bValid)
                            continue;

                        group = dtg_Month;
                        timeInfo.tm_mon = pNode_2->dblVal;
                        if (pNode_2->childs.size() > 0)
                        {
                            // 日
                            for (int k = 0; k < pNode_2->childs.size(); ++k)
                            {
                                ValuesNode* pNode_3 = pNode_2->childs[k];
                                bValid = (pNode_3->GetCheckType() == 2) && !(pNode_3->searchState & ITEM_VISIBLE);
                                if (!bValid)
                                    continue;

                                group = dtg_Day;
                                timeInfo.tm_mday = pNode_3->dblVal;
                                m_spCriteriaDateList->Add(group, timeInfo);
                            }
                        }
                        else
                        {
                            if (!(pNode_2->searchState & ITEM_VISIBLE))
                                m_spCriteriaDateList->Add(group, timeInfo);
                        }
                    }
                }
                else
                {
                    m_spCriteriaDateList->Add(group, timeInfo);
                }
            }
            else 
            {
                if (!(pNode_1->searchState & ITEM_VISIBLE))
                    m_spCriteriaTextSet->Add(pNode_1->bstrText);
            }
        }
    }

    // 超限部分的内容
    if (m_pRootNode != NULL && lastNodeIndex > 0 && lastNodeIndex < m_pRootNode->childs.size())
    {
        // 符合条件的节点：
        //      如果 非搜索时，
        //          如果 allCheckedType == unknow && pNode->nCheckType != 0 && 末尾节点nchecktype == 2
        //          或者 allCheckedType == checkedAll
        //
        //      如果 搜索时 (allCheckedType 必然是 checkedAll 或者 unCheckedAll)
        //          如果 allCheckedType == checkedAll && (pNode->searchState & ITEM_VISIBLE)

        for (int i = lastNodeIndex; i < m_pRootNode->childs.size(); ++i)
        {
            ValuesNode* pNode_1 = m_pRootNode->childs[i];
            bool bValid = bForSearch ? (allCheckedType == checkedAll && (pNode_1->searchState & ITEM_VISIBLE)) : (allCheckedType == unknow && pNode_1->GetCheckType() != 0) || (allCheckedType == checkedAll);
            if ((!bValid))
                continue;
                    
            if (pNode_1->childs.size() > 0) 
            {
                // 年
                ETDateTimeGrouping group = dtg_Year;
                TIMEINFO timeInfo = {-1,-1,-1,-1,-1,-1};
                timeInfo.tm_year = pNode_1->dblVal;
                if (pNode_1->childs.size() > 0)
                {
                    // 月
                    for (int j = 0; j < pNode_1->childs.size(); ++j)
                    {
                        ValuesNode* pNode_2 = pNode_1->childs[j];
                        bValid = bForSearch ? (allCheckedType == checkedAll && (pNode_2->searchState & ITEM_VISIBLE)) : (allCheckedType == unknow && pNode_2->GetCheckType() != 0) || (allCheckedType == checkedAll);
                        if (!bValid)
                            continue;

                        group = dtg_Month;
                        timeInfo.tm_mon = pNode_2->dblVal;
                        if (pNode_2->childs.size() > 0)
                        {
                            // 日
                            for (int k = 0; k < pNode_2->childs.size(); ++k)
                            {
                                ValuesNode* pNode_3 = pNode_2->childs[k];
                                bValid = bForSearch ? (allCheckedType == checkedAll && (pNode_3->searchState & ITEM_VISIBLE)) : (allCheckedType == unknow && pNode_3->GetCheckType() != 0) || (allCheckedType == checkedAll);
                                if (!bValid)
                                    continue;

                                group = dtg_Day;
                                timeInfo.tm_mday = pNode_3->dblVal;
                                m_spCriteriaDateList->Add(group, timeInfo);
                            }
                        }
                        else
                        {
                            m_spCriteriaDateList->Add(group, timeInfo);
                        }
                    }
                }
                else
                {
                    m_spCriteriaDateList->Add(group, timeInfo);
                }
            }
            else 
            {
                m_spCriteriaTextSet->Add(pNode_1->bstrText);
            }
        }
    }
    
    // 可见部分
	binary_wo::VarObj valsObj = condition.get("values");
	for (int i = 0, cnt = valsObj.arrayLength(); i < cnt; ++i)
	{
		binary_wo::VarObj item = valsObj.at(i);
		PCWSTR type = item.field_str("type");
		if (xstrcmp(type, __X("text")) == 0)
		{
			m_spCriteriaTextSet->Add(item.field_str("text"));
		}
		else if (xstrcmp(type, __X("date")) == 0)
		{
			ETDateTimeGrouping group = strToETDateTimeGrouping(item.field_str("group"));
			TIMEINFO timeInfo = {-1,-1,-1,-1,-1,-1};
			switch(group)
			{
			case dtg_Second:
				timeInfo.tm_sec = item.field_int32("second");
			case dtg_Minute:
				timeInfo.tm_min = item.field_int32("minute");
			case dtg_Hour:
				timeInfo.tm_hour = item.field_int32("hour");
			case dtg_Day:
				timeInfo.tm_mday = item.field_int32("day");
			case dtg_Month:
				timeInfo.tm_mon = item.field_int32("month");
			case dtg_Year:
				timeInfo.tm_year = item.field_int32("year");
			}
			m_spCriteriaDateList->Add(group, timeInfo);
		}
	}
    
    if (m_spCriteriaTextSet->GetCount() > 0)
	    m_param1.SetTextSet(m_spCriteriaTextSet);
    if (m_spCriteriaDateList->GetCount() > 0)
	    m_param2.SetDateList(m_spCriteriaDateList);
}


void AutofilterParamGenerator::genCustom(binary_wo::VarObj condition)
{
	binary_wo::VarObj varParam1 = condition.get_s("param1");
	binary_wo::VarObj varParam2 = condition.get_s("param2");

	if (varParam1.type() != binary_wo::typeInvalid)
	{
		CUSTOM_CONDITION_TYPE type1 = strToCustomConditionType(varParam1.field_str("customType"));
		genCustomCriteria(type1, varParam1.field_str("val"), m_strParam1);
		m_param1.SetWcs(m_strParam1.c_str());
	}

	if (varParam2.type() != binary_wo::typeInvalid)
	{
		CUSTOM_CONDITION_TYPE type2 = strToCustomConditionType(varParam2.field_str("customType"));
		genCustomCriteria(type2, varParam2.field_str("val"), m_strParam2);
		m_param2.SetWcs(m_strParam2.c_str());
	}
}

void AutofilterParamGenerator::genCustomCriteria(CUSTOM_CONDITION_TYPE type, PCWSTR val, ks_wstring& strParam)
{
	switch (type)
	{
	case cct_equals:
		strParam.append(__X("="));
		strParam.append(val);
		break;
	case cct_notEqu:
		strParam.append(__X("<>"));
		strParam.append(val);
		break;
	case cct_greater:
		strParam.append(__X(">"));
		strParam.append(val);
		break;
	case cct_greaterEqu:
		strParam.append(__X(">="));
		strParam.append(val);
		break;
	case cct_less:
		strParam.append(__X("<"));
		strParam.append(val);
		break;
	case cct_lessEqu:
		strParam.append(__X("<="));
		strParam.append(val);
		break;
	case cct_beginWith:
		strParam.append(__X("="));
		strParam.append(val);
		strParam.append(__X("*"));
		break;
	case cct_notBeginWith:
		strParam.append(__X("<>"));
		strParam.append(val);
		strParam.append(__X("*"));
		break;
	case cct_endWith:
		strParam.append(__X("=*"));
		strParam.append(val);
		break;
	case cct_notEndWith:
		strParam.append(__X("<>*"));
		strParam.append(val);
		break;
	case cct_contains:
		strParam.append(__X("=*"));
		strParam.append(val);
		strParam.append(__X("*"));
		break;
	case cct_notContains:
		strParam.append(__X("<>*"));
		strParam.append(val);
		strParam.append(__X("*"));
	}

	if(strParam.compare(__X("=**")) == 0)
		strParam = __X("=*");
	else if(strParam.compare(__X("<>**")) == 0)
		strParam = __X("<>*");
	else if(strParam.compare(__X(">=**")) == 0)
		strParam = __X(">=*");
	else if(strParam.compare(__X("<=**")) == 0)
		strParam = __X("<=*");
	else if(strParam.compare(__X(">**")) == 0)
		strParam = __X(">*");
	else if(strParam.compare(__X("<**")) == 0)
		strParam = __X("<*");
}

void AutofilterParamGenerator::genCellColor(binary_wo::VarObj condition)
{
	_appcore_CreateObject(CLSID_KFilterCellColorItems, IID_IFilterCellColorItems, (void**)&m_spCellColors);
	EtFill fill;
	KXFMASK mask(KXFMASK::_catFills);
	auto addCellColor = [&](const binary_wo::VarObj& fillObj)
	{
		DecodeFill(fillObj, fill, mask);
		ks_stdptr<ICellColor> spCellColor;
		_appcore_CreateObject(CLSID_KCellColor, IID_ICellColor, (void**)&spCellColor);
		spCellColor->SetFill(fill);
		m_spCellColors->Add(spCellColor);
	};
	// 旧命令兼容
	if (condition.has("fill"))
	{
		addCellColor(condition.get("fill"));
	}
	else if (condition.has("fills"))
	{
		binary_wo::VarObj fillsObj = condition.get("fills");
		for (int i = 0, length = fillsObj.arrayLength(); i < length; ++i)
		{
			addCellColor(fillsObj.at(i));
		}
	}
	if (m_spCellColors->GetItemCount() > 0)
		m_param1.SetCellColors(m_spCellColors);
}

void AutofilterParamGenerator::genFontColor(binary_wo::VarObj condition)
{
	_appcore_CreateObject(CLSID_KFilterFontColorItems, IID_IFilterFontColorItems, (void**)&m_spFontColors);
	EtColor color;
	auto addFontColor = [&](const binary_wo::VarObj& colorObj)
	{
		DecodeColor(colorObj, color);
		ks_stdptr<IFontColor> spFontColor;
		_appcore_CreateObject(CLSID_KFontColor, IID_IFontColor, (void**)&spFontColor);
		spFontColor->SetBookForIO(m_pBook);
		spFontColor->SetColor(color);
		m_spFontColors->Add(spFontColor);
	};
	if (condition.has("color"))
	{
		addFontColor(condition.get("color"));
	}
	else if (condition.has("colors"))
	{
		binary_wo::VarObj colorObjs = condition.get("colors");
		for (int i = 0, length = colorObjs.arrayLength(); i < length; ++i)
		{
			addFontColor(colorObjs.at(i));
		}
	}
	if (m_spFontColors->GetItemCount() > 0)
		m_param1.SetFontColors(m_spFontColors);
}

void AutofilterParamGenerator::genCellIcon(binary_wo::VarObj condition)
{
	_appcore_CreateObject(CLSID_KFilterIconItems, IID_IFilterIconItems, (void**)&m_spCellIcons);
	EtIconSet icon;
	auto addCellIcon = [&](const binary_wo::VarObj& iconObj)
	{
		icon.icoSet = strToEtIconSet(iconObj.field_str("icoSet"));
		icon.icoIdx = iconObj.field_uint32("icoIdx");
		ks_stdptr<ICellIcon> spCellIcon;
		_appcore_CreateObject(CLSID_KCellIcon, IID_ICellIcon, (void**)&spCellIcon);
		spCellIcon->SetIcon(icon);
		m_spCellIcons->Add(spCellIcon);
	};
	if (condition.has("iconSet"))
	{
		addCellIcon(condition.get("iconSet"));
	}
	else if (condition.has("iconSets"))
	{
		binary_wo::VarObj iconsObj = condition.get("iconSets");
		for (int i = 0, length = iconsObj.arrayLength(); i < length; ++i)
		{
			addCellIcon(iconsObj.at(i));
		}
	}
	if (m_spCellIcons->GetItemCount() > 0)
		m_param1.SetIcons(m_spCellIcons);
}

wo::CUSTOM_CONDITION_TYPE AutofilterParamGenerator::strToCustomConditionType(PCWSTR str)
{
	if (xstrcmp(str, __X("null")) == 0) return cct_null;
	else if (xstrcmp(str, __X("equals")) == 0) return cct_equals;
	else if (xstrcmp(str, __X("notEqu")) == 0) return cct_notEqu;
	else if (xstrcmp(str, __X("greater")) == 0) return cct_greater;
	else if (xstrcmp(str, __X("greaterEqu")) == 0) return cct_greaterEqu;
	else if (xstrcmp(str, __X("less")) == 0) return cct_less;
	else if (xstrcmp(str, __X("lessEqu")) == 0) return cct_lessEqu;
	else if (xstrcmp(str, __X("beginWith")) == 0) return cct_beginWith;
	else if (xstrcmp(str, __X("notBeginWith")) == 0) return cct_notBeginWith;
	else if (xstrcmp(str, __X("endWith")) == 0) return cct_endWith;
	else if (xstrcmp(str, __X("notEndWith")) == 0) return cct_notEndWith;
	else if (xstrcmp(str, __X("contains")) == 0) return cct_contains;
	else if (xstrcmp(str, __X("notContains")) == 0) return cct_notContains;
	else if (xstrcmp(str, __X("between")) == 0) return cct_between;
	else if (xstrcmp(str, __X("notBetween")) == 0) return cct_notBetween;
	ASSERT(FALSE);
	return cct_null;
}

ETFilterOperator AutofilterParamGenerator::strToETFilterOperator(PCWSTR str)
{
	if (xstrcmp(str, __X("none")) == 0) return FOp_None;
	else if (xstrcmp(str, __X("and")) == 0) return FOp_And;
	else if (xstrcmp(str, __X("or")) == 0) return FOp_Or;
	else if (xstrcmp(str, __X("bottom10Items")) == 0) return FOp_Bottom10Items;
	else if (xstrcmp(str, __X("bottom10Percent")) == 0) return FOp_Bottom10Percent;
	else if (xstrcmp(str, __X("bottom10Sum")) == 0) return FOp_Bottom10Sum;
	else if (xstrcmp(str, __X("top10Items")) == 0) return FOp_Top10Items;
	else if (xstrcmp(str, __X("top10Percent")) == 0) return FOp_Top10Percent;
	else if (xstrcmp(str, __X("top10Sum")) == 0) return FOp_Top10Sum;
	else if (xstrcmp(str, __X("filterCellColor")) == 0) return FOp_FilterCellColor;
	else if (xstrcmp(str, __X("filterDynamic")) == 0) return FOp_FilterDynamic;
	else if (xstrcmp(str, __X("filterFontColor")) == 0) return FOp_FilterFontColor;
	else if (xstrcmp(str, __X("filterIcon")) == 0) return FOp_FilterIcon;
	else if (xstrcmp(str, __X("filterValues")) == 0) return FOp_FilterValues;
	else if (xstrcmp(str, __X("filterNoFill")) == 0) return FOp_FilterNoFill;
	else if (xstrcmp(str, __X("filterAutomaticFontColor")) == 0) return FOp_FilterAutomaticFontColor;
	else if (xstrcmp(str, __X("filterNoIcon")) == 0) return FOp_FilterNoIcon;
	ASSERT(FALSE);
	return FOp_None;
}

ET_DYNAMIC_FILTER_CRITERIA AutofilterParamGenerator::strToDynamicFilterCriteria(PCWSTR str)
{
	if (xstrcmp(str, __X("none")) == 0) return dfc_None;
	else if (xstrcmp(str, __X("today")) == 0) return dfc_FilterToday;
	else if (xstrcmp(str, __X("yesterday")) == 0) return dfc_FilterYesterday;
	else if (xstrcmp(str, __X("tomorrow")) == 0) return dfc_FilterTomorrow;
	else if (xstrcmp(str, __X("thisWeek")) == 0) return dfc_FilterThisWeek;
	else if (xstrcmp(str, __X("lastWeek")) == 0) return dfc_FilterLastWeek;
	else if (xstrcmp(str, __X("nextWeek")) == 0) return dfc_FilterNextWeek;
	else if (xstrcmp(str, __X("thisMonth")) == 0) return dfc_FilterThisMonth;
	else if (xstrcmp(str, __X("lastMonth")) == 0) return dfc_FilterLastMonth;
	else if (xstrcmp(str, __X("nextMonth")) == 0) return dfc_FilterNextMonth;
	else if (xstrcmp(str, __X("thisQuarter")) == 0) return dfc_FilterThisQuarter;
	else if (xstrcmp(str, __X("lastQuarter")) == 0) return dfc_FilterLastQuarter;
	else if (xstrcmp(str, __X("nextQuarter")) == 0) return dfc_FilterNextQuarter;
	else if (xstrcmp(str, __X("thisYear")) == 0) return dfc_FilterThisYear;
	else if (xstrcmp(str, __X("lastYear")) == 0) return dfc_FilterLastYear;
	else if (xstrcmp(str, __X("nextYear")) == 0) return dfc_FilterNextYear;
	else if (xstrcmp(str, __X("yearToDate")) == 0) return dfc_FilterYearToDate;
	else if (xstrcmp(str, __X("allDatesInPeriodQuarter1")) == 0) return dfc_FilterAllDatesInPeriodQuarter1;
	else if (xstrcmp(str, __X("allDatesInPeriodQuarter2")) == 0) return dfc_FilterAllDatesInPeriodQuarter2;
	else if (xstrcmp(str, __X("allDatesInPeriodQuarter3")) == 0) return dfc_FilterAllDatesInPeriodQuarter3;
	else if (xstrcmp(str, __X("allDatesInPeriodQuarter4")) == 0) return dfc_FilterAllDatesInPeriodQuarter4;
	else if (xstrcmp(str, __X("allDatesInPeriodJanuary")) == 0) return dfc_FilterAllDatesInPeriodJanuary;
	else if (xstrcmp(str, __X("allDatesInPeriodFebruray")) == 0) return dfc_FilterAllDatesInPeriodFebruray;
	else if (xstrcmp(str, __X("allDatesInPeriodMarch")) == 0) return dfc_FilterAllDatesInPeriodMarch;
	else if (xstrcmp(str, __X("allDatesInPeriodApril")) == 0) return dfc_FilterAllDatesInPeriodApril;
	else if (xstrcmp(str, __X("allDatesInPeriodMay")) == 0) return dfc_FilterAllDatesInPeriodMay;
	else if (xstrcmp(str, __X("allDatesInPeriodJune")) == 0) return dfc_FilterAllDatesInPeriodJune;
	else if (xstrcmp(str, __X("allDatesInPeriodJuly")) == 0) return dfc_FilterAllDatesInPeriodJuly;
	else if (xstrcmp(str, __X("allDatesInPeriodAugust")) == 0) return dfc_FilterAllDatesInPeriodAugust;
	else if (xstrcmp(str, __X("allDatesInPeriodSeptember")) == 0) return dfc_FilterAllDatesInPeriodSeptember;
	else if (xstrcmp(str, __X("allDatesInPeriodOctober")) == 0) return dfc_FilterAllDatesInPeriodOctober;
	else if (xstrcmp(str, __X("allDatesInPeriodNovember")) == 0) return dfc_FilterAllDatesInPeriodNovember;
	else if (xstrcmp(str, __X("allDatesInPeriodDecember")) == 0) return dfc_FilterAllDatesInPeriodDecember;
	else if (xstrcmp(str, __X("aboveAverage")) == 0) return dfc_FilterAboveAverage;
	else if (xstrcmp(str, __X("belowAverage")) == 0) return dfc_FilterBelowAverage;
	ASSERT(FALSE);
	return dfc_None;
}

ETDateTimeGrouping AutofilterParamGenerator::strToETDateTimeGrouping(PCWSTR str)
{
	if (xstrcmp(str, __X("year")) == 0) return dtg_Year;
	if (xstrcmp(str, __X("month")) == 0) return dtg_Month;
	if (xstrcmp(str, __X("day")) == 0) return dtg_Day;
	if (xstrcmp(str, __X("hour")) == 0) return dtg_Hour;
	if (xstrcmp(str, __X("minute")) == 0) return dtg_Minute;
	if (xstrcmp(str, __X("second")) == 0) return dtg_Second;

	ks_throw(E_INVALIDARG);
	return dtg_Year;
}

CF_ICONSET AutofilterParamGenerator::strToEtIconSet(PCWSTR str)
{
		if (xstrcmp(str, __X("3Arrows")) == 0) return CFIS_3Arrows;
		else if (xstrcmp(str, __X("3ArrowsGray")) == 0) return CFIS_3ArrowsGray;
		else if (xstrcmp(str, __X("3Flags")) == 0) return CFIS_3Flags;
		else if (xstrcmp(str, __X("3TrafficLights1")) == 0) return CFIS_3TrafficLights1;
		else if (xstrcmp(str, __X("3TrafficLights2")) == 0) return CFIS_3TrafficLights2;
		else if (xstrcmp(str, __X("3Signs")) == 0) return CFIS_3Signs;
		else if (xstrcmp(str, __X("3Symbols")) == 0) return CFIS_3Symbols;
		else if (xstrcmp(str, __X("3Symbols2")) == 0) return CFIS_3Symbols2;
		else if (xstrcmp(str, __X("3Stars")) == 0) return CFIS_3Stars;
		else if (xstrcmp(str, __X("3Triangles")) == 0) return CFIS_3Triangles;
		else if (xstrcmp(str, __X("4Arrows")) == 0) return CFIS_4Arrows;
		else if (xstrcmp(str, __X("4ArrowsGray")) == 0) return CFIS_4ArrowsGray;
		else if (xstrcmp(str, __X("4RedToBlack")) == 0) return CFIS_4RedToBlack;
		else if (xstrcmp(str, __X("4CRV")) == 0) return CFIS_4CRV;
		else if (xstrcmp(str, __X("4TrafficLights")) == 0) return CFIS_4TrafficLights;
		else if (xstrcmp(str, __X("5Arrows")) == 0) return CFIS_5Arrows;
		else if (xstrcmp(str, __X("5ArrowsGray")) == 0) return CFIS_5ArrowsGray;
		else if (xstrcmp(str, __X("5CRV")) == 0) return CFIS_5CRV;
		else if (xstrcmp(str, __X("5Quarters")) == 0) return CFIS_5Quarters;
		else if (xstrcmp(str, __X("5Boxes")) == 0) return CFIS_5Boxes;

		return CFIS_None;

}

RowRangeHelper::RowRangeHelper(IBook* pBook, binary_wo::VarObj obj)
	: m_pBook(pBook)
	, m_obj(obj)
{

}

RowRangeHelper::~RowRangeHelper()
{
	if (NULL != m_pBook && m_obj.type() != binary_wo::typeInvalid)
		serialize();
}

void RowRangeHelper::serialize()
{
	binary_wo::VarObj rowHiddenList = m_obj.add_field_array("sheetRowHiddenList", binary_wo::typeStruct);
	for (auto iter = m_mapRowRanges.cbegin(), endIter = m_mapRowRanges.cend(); iter != endIter; iter++)
	{
		binary_wo::VarObj objItem = rowHiddenList.add_item_struct();
		IDX idxSheet = iter->first;
		objItem.add_field_int32("sheetIdx", idxSheet);
		binary_wo::VarObj objRgs = objItem.add_field_array("ranges", binary_wo::typeStruct);
		ks_stdptr<ISheet> spSheet;
		m_pBook->GetSheet(idxSheet, &spSheet);
		if (NULL == spSheet)
			continue;
		auto& rowRanges = iter->second;
		for (auto iterItem = rowRanges.cbegin(), iterEnd = rowRanges.cend(); iterItem != iterEnd; iterItem++)
		{
			binary_wo::VarObj objRgItem = objRgs.add_item_struct();
			collectFilterResult(m_obj, objRgItem, spSheet, nullptr, *iterItem);
		}
	}
}

BOOL RowRangeHelper::checkWithCoreFilter(ISheet*pSht,const RowRangeItem& rgItem,PCWSTR currentId,PCWSTR coreUserId)
{
	if(ks_wstring(currentId) == ks_wstring(coreUserId))
	{
		return TRUE;
	}

	if(nullptr == pSht)
		return FALSE;
	et_sdptr<ISheetEnum> spSheetEnum;
	pSht->CreateEnum(&spSheetEnum);
	if(currentId == nullptr)
	{
		wo::IWoFilterContext *fx = nullptr;
		spSheetEnum->SetFilterContext(fx);	
	}
	else
	{
		spSheetEnum->SetFilterContext(currentId);
	}

	et_sdptr<ISheetEnum> spCoreEnum;
	pSht->CreateEnum(&spCoreEnum);
	spCoreEnum->SetFilterContext(coreUserId);

	const int maxRow = rgItem.rowTo + 1; 
	for(int rowIndex = rgItem.rowFrom; rowIndex < maxRow; )
	{
		BOOL bCurrHidden = FALSE;
		BOOL bCoreHidden = FALSE;
		const INT32 currCnt = spSheetEnum->GetRowHidden(rowIndex, &bCurrHidden);
		const INT32 coreCnt = spCoreEnum->GetRowHidden(rowIndex, &bCoreHidden);

		if(bCurrHidden != bCoreHidden || currCnt != coreCnt)
		{
			return FALSE;
		}
		 
		rowIndex += coreCnt	;
	}

	return TRUE;
}

HRESULT RowRangeHelper::collectFilterResult(VarObj& param, VarObj& target,
	ISheet* pSht, IFilterRowContainter* pFilterRowCol, const RowRangeItem& rg)
{
	if (NULL == pSht) return S_FALSE;

	et_sdptr<ISheetEnum> spSheetEnum;
	if (pFilterRowCol == nullptr)
		pSht->CreateEnum(&spSheetEnum);

	const size_t cntListLimit = 100000;

	BOOL bCurHidden = FALSE;
	std::vector<int32> vecCache;
	for (ROW row = rg.rowFrom, rowEnd = rg.rowTo + 1; row < rowEnd && vecCache.size() < cntListLimit;)
	{
		BOOL bIsHidden = FALSE;
		INT nCount = 0;
		if (pFilterRowCol == nullptr)
			nCount = spSheetEnum->GetRowHidden(row, &bIsHidden, rowEnd - row);
		else
			nCount = pFilterRowCol->GetSame(row, &bIsHidden, rowEnd - row);
		if (nCount < 0)
			break;
		
		if (vecCache.empty() || bCurHidden != bIsHidden)
		{
			vecCache.push_back(nCount);
			bCurHidden = bIsHidden;
		}
		else
		{
			vecCache.back() += nCount;
		}

		row += nCount;
	}

	if (vecCache.size() >= cntListLimit)
	{
		//客户端不添加rowHiddenListExceeded的checkParam逻辑，保证能够回落
		param.add_field_bool("rowHiddenListExceeded", true);
		return S_FALSE;
	}

	if (vecCache.size() % 2 == 0) bCurHidden = !bCurHidden;
	
	ASSERT(!vecCache.empty());
	binary_wo::VarObj rowHiddenList = target.add_field_struct("rowHiddenList");
	rowHiddenList.add_field_bool("firstHidden", alg::BOOL2bool(bCurHidden));
	rowHiddenList.add_field_int32("firstRow", rg.rowFrom);
	binary_wo::VarObj arrData = rowHiddenList.add_field_array("arrData", binary_wo::typeInt32);
	arrData.add_item_int32(&vecCache[0], vecCache.size());
	return S_OK;
}

void RowRangeHelper::Add(const RANGE& rg)
{
	if (!rg.IsValid())
		return;

	auto iter = m_mapRowRanges.find(rg.SheetFrom());
	if (iter == m_mapRowRanges.end())
	{
		RowRanges rowRgs;
		rowRgs.push_back(RowRangeItem(rg.SheetFrom(), rg.RowFrom(), rg.RowTo()));
		m_mapRowRanges.insert(RowRangesPair(rg.SheetFrom(), rowRgs));
		return;
	}

	auto& rowRgs = iter->second;

	auto rgRowFromIter = std::lower_bound(rowRgs.begin(), rowRgs.end(), RowRangeItem(rg.SheetFrom(), rg.RowFrom(), rg.RowTo()), CmpRowFrom());
	bool bRowFromEnd = rgRowFromIter == rowRgs.end();

	auto rgRowToIter = std::lower_bound(rowRgs.begin(), rowRgs.end(), RowRangeItem(rg.SheetFrom(), rg.RowFrom(), rg.RowTo()), CmpRowTo());
	bool bRowToEnd = rgRowToIter == rowRgs.end();

	if (bRowFromEnd)
	{
		auto& rgBack = rowRgs.back();
		if (rgBack.rowTo >= rg.RowFrom()) // 里面
			rgBack.rowTo = std::max(rg.RowTo(), rgBack.rowTo);
		else
			rowRgs.push_back(RowRangeItem(rg.SheetFrom(), rg.RowFrom(), rg.RowTo()));
	}
	else if (!bRowFromEnd && bRowToEnd)
	{
		add4RowToIsEnd(rg, rgRowFromIter, rgRowToIter, rowRgs);
	}
	else
	{
		if (isFirst(rowRgs, rgRowFromIter))
			add4First(rg, rgRowFromIter, rgRowToIter, rowRgs);
		else
			add4HasPre(rg, rgRowFromIter, rgRowToIter, rowRgs);
	}
}

bool RowRangeHelper::isFirst(RowRanges& rowRgs, RowRangeIter& rgRowIter)
{
	size_t nRowFrom = rgRowIter - rowRgs.begin();
	return nRowFrom == 0;
}

RowRangeHelper::RowRangeIter RowRangeHelper::getPre(RowRanges& rowRgs, RowRangeIter& rgRowIter)
{
	size_t nRowFrom = rgRowIter - rowRgs.begin();
	if (nRowFrom == 0)
		return rgRowIter;
	else
		return rowRgs.begin() + nRowFrom - 1;
}

void RowRangeHelper::add4HasPre(const RANGE& rg, RowRangeIter& rgRowFromIter, RowRangeIter& rgRowToIter, RowRanges& rowRgs)
{
	auto iterPreRowFrom = getPre(rowRgs, rgRowFromIter);
	if (iterPreRowFrom->rowTo >= rg.RowFrom()) // 里面
	{
		if (rgRowToIter->rowFrom > rg.RowTo()) // 外面
		{
			if (rgRowFromIter == rgRowToIter)
			{
				iterPreRowFrom->rowTo = rg.RowTo();
			}
			else if (rgRowFromIter < rgRowToIter)
			{
				iterPreRowFrom->rowTo = rg.RowTo();
				auto iterPreRowTo = getPre(rowRgs, rgRowToIter);
				if (rgRowFromIter == iterPreRowTo)
					rowRgs.erase(rgRowFromIter);
				else
					rowRgs.erase(rgRowFromIter, rgRowToIter);
			}
		}
		else
		{
			if (rgRowFromIter == rgRowToIter)
			{
				iterPreRowFrom->rowTo = rgRowToIter->rowTo;
				rowRgs.erase(rgRowToIter);
			}
			else if (rgRowFromIter < rgRowToIter)
			{
				iterPreRowFrom->rowTo = rgRowToIter->rowTo;
				rowRgs.erase(rgRowFromIter, ++rgRowToIter);
			}
		}
	}
	else
	{
		if (rgRowToIter->rowFrom > rg.RowTo())
		{
			if (rgRowFromIter == rgRowToIter)
			{
				rowRgs.insert(rgRowFromIter, RowRangeItem(rg.SheetFrom(), rg.RowFrom(), rg.RowTo()));
			}
			else if (rgRowFromIter < rgRowToIter)
			{
				rgRowFromIter->rowFrom = rg.RowFrom();
				rgRowFromIter->rowTo = rg.RowTo();
				rgRowFromIter++;
				auto iterPreRowTo = getPre(rowRgs, rgRowToIter);
				if (rgRowFromIter == iterPreRowTo)
					rowRgs.erase(rgRowFromIter);
				else if (rgRowFromIter < iterPreRowTo)
					rowRgs.erase(rgRowFromIter, rgRowToIter);
			}
		}
		else
		{
			if (rgRowFromIter == rgRowToIter)
			{
				rgRowToIter->rowFrom = rg.RowFrom();
			}
			else if (rgRowFromIter < rgRowToIter)
			{
				rgRowToIter->rowFrom = rg.RowFrom();
				auto iterPreRowTo = getPre(rowRgs, rgRowToIter);
				if (rgRowFromIter == iterPreRowTo)
				{
					rowRgs.erase(rgRowFromIter);
				}
				else if (rgRowFromIter < iterPreRowTo)
				{
					rowRgs.erase(rgRowFromIter, rgRowToIter);
				}
			}
		}
	}
}

void RowRangeHelper::add4First(const RANGE& rg, RowRangeIter& rgRowFromIter, RowRangeIter& rgRowToIter, RowRanges& rowRgs)
{
	if (rgRowToIter->rowFrom > rg.RowTo())
	{
		if (rgRowFromIter < rgRowToIter)
			rowRgs.erase(rgRowFromIter, ++rgRowToIter);
		rowRgs.insert(rowRgs.begin(), RowRangeItem(rg.SheetFrom(), rg.RowFrom(), rg.RowTo()));
	}
	else
	{
		if (rgRowFromIter == rgRowToIter)
		{
			rgRowFromIter->rowFrom = rg.RowFrom();
		}
		else
		{
			rgRowFromIter->rowFrom = rg.RowFrom();
			rgRowFromIter->rowTo = rgRowToIter->rowTo;
			++rgRowFromIter;
			if (rgRowFromIter == rgRowToIter)
				rowRgs.erase(rgRowFromIter);
			else
				rowRgs.erase(rgRowFromIter, ++rgRowToIter);
		}
	}
}

void RowRangeHelper::add4RowToIsEnd(const RANGE& rg, RowRangeIter& rgRowFromIter, RowRangeIter& rgRowToIter, RowRanges& rowRgs)
{
	if (isFirst(rowRgs, rgRowFromIter))
	{
		rowRgs.clear();
		rowRgs.push_back(RowRangeItem(rg.SheetFrom(), rg.RowFrom(), rg.RowTo()));
	}
	else
	{
		auto iterPre = getPre(rowRgs, rgRowFromIter);
		if (iterPre->rowTo >= rg.RowFrom()) // 里面
		{
			iterPre->rowTo = rg.RowTo();
			rowRgs.erase(rgRowFromIter, rowRgs.end());
		}
		else
		{
			rgRowFromIter->rowFrom = rg.RowFrom();
			rgRowFromIter->rowTo = rg.RowTo();
			++rgRowFromIter;
			if (rgRowFromIter != rowRgs.end())
				rowRgs.erase(rgRowFromIter, rowRgs.end());
		}
	}
}

}


