﻿#include "identifytable.h"
#include "kso/textstd/texttool.h"
#include "kso/numberformat/numberformatconvert.h"
#include "applogic/et_api_convert.h"
#include "wo/et_revision_context.h"

constexpr int maxColumn = 500;
constexpr int maxRow = 60000;
constexpr int maxCellCount = 1000000;
constexpr int maxMergeRow = 10000;

// 如果返回false则表示处理的区域在范围以外，则不处理
inline static bool AmendRange(IdentifyRange& rg)
{
	if (rg.iLeft >= maxColumn)
		return false;
	if (rg.iTop >= maxRow)
		return false;
	if (rg.iRight >= maxColumn)
		rg.iRight = maxColumn - 1;
	if (rg.iBottom >= maxRow)
		rg.iBottom = maxRow - 1;
	return true;
}

inline static bool AmendRange(const ROW row, const COL col)
{
	return row * col <= maxCellCount;
}

inline static bool IsRightCell(const IdentifyRange& rg, ROW iRow, COL iCol)
{
	if (rg.IsOneCell())
		return true;
	if (rg.iBottom - rg.iTop <= maxMergeRow)
		return true;
	return rg.iTop == iRow && rg.iLeft == iCol;
}

HRESULT GetJsonDataType(PCWSTR str, JsonDataType* type)
{
	if (!type)
		return E_FAIL;
	if (xstricmp(str, __X("teval")) == 0
		|| xstricmp(str, __X("zone")) == 0
		|| xstricmp(str, __X("form")) == 0)
		*type = JsonDataArrangement;
	else if (xstricmp(str, __X("proof")) == 0)
		*type = JsonDataProofreading;
	else if (xstricmp(str, __X("datamining")) == 0)
		*type = JsonDataAnalysis;
	else if (xstricmp(str, __X("dataunderstand")) == 0)
		*type = JsonDataPivotTableSource;
	else
		return E_INVALIDARG;
	return S_OK;
}

IdentifyRange::IdentifyRange(const RANGE& rg)
{
	ResetRange(rg);
}

void IdentifyRange::ResetRange(const RANGE& rg)
{
	iTop = rg.RowFrom();
	iLeft = rg.ColFrom();
	iBottom = rg.RowTo();
	iRight = rg.ColTo();
}

static bool removePattern(const QRegExp& rx, QString& showText)
{
	QString result;
	int prev = 0, curr = 0;
	bool bMatched = false;
	while ((curr = rx.indexIn(showText, curr)) != -1)
	{
		if (curr > prev)
			result += showText.mid(prev, curr - prev);
		prev = curr = curr + rx.matchedLength();
		bMatched = true;
	}
	if (bMatched)
	{
		result += showText.mid(prev);
		showText = result;
	}
	return bMatched;
}

static QString Full2Half(PCWSTR source)
{
	if (!source)
		return {};
	ks_wstring wsRes;
	while (*source != 0)
	{
		WCHAR wchFollow = 0;
		wsRes += _TxFull2HalfWidth(*source, &wchFollow);
		if (wchFollow != 0)
			wsRes += wchFollow;
		++source;
	}
	return krt::fromUtf16(wsRes.c_str());
}

static bool DeleteWhitespace(QString& content)
{
	static const QRegExp rx("[\\s]+");
	return removePattern(rx, content);
}

static bool DeleteUnit(QString& content)
{
	static const QRegExp exp("^(.+([(|（].*[)|）]).*)+$");
	if (content.indexOf(exp) == -1)
		return false;
	static const QRegExp rx("[(|（].*[(|（|)|）]*[)|）]");
	return removePattern(rx, content);
}

static bool isValidContent(PCWSTR content)
{
	QString cleanContent = Full2Half(content);
	DeleteWhitespace(cleanContent);
	DeleteUnit(cleanContent);
	return !cleanContent.isEmpty();
}

void IdentifyCells::ExportJson(QJsonObject& jsonRow, HiddenRowColTool* pTool) const
{
	QJsonObject jsonCells;
	jsonCells.insert("content", krt::fromUtf16(strContent.c_str()).toUtf8().constData());
	if (!strValue2.empty())
		jsonCells.insert("value2", krt::fromUtf16(strValue2.c_str()).toUtf8().constData());
	if (strNumberFormat && *strNumberFormat)
		jsonCells["numberFormatLocal"] = krt::fromUtf16(strNumberFormat).toUtf8().constData();
	if (!qFuzzyCompare(fFontSize, defaultFontSize))
		jsonCells.insert("fontsize", fFontSize);
	if (nAlignment != etHAlignGeneral)
		jsonCells.insert("alignment", nAlignment);
	// 取单元格去掉隐藏行列后是否为合并单元格
	IdentifyRange rgCell(this->iTop, this->iBottom, this->iLeft, this->iRight);
	if (pTool)
		rgCell = pTool->GetExportJsonRange(this);
	if (!rgCell.IsOneCell())
	{
		jsonCells.insert("merge", true);
		if (iTop != iBottom)
			jsonCells.insert("mergerows", QString("(%1, %2)").arg(rgCell.iTop).arg(rgCell.iBottom).toUtf8().constData());
	}
	jsonRow.insert(QString("(%1, %2)").arg(rgCell.iLeft).arg(rgCell.iRight).toUtf8().constData(), jsonCells);
}

const IdentifyCells* IdentifyMergeCellsList::FindCellInMergeCells(ROW iRow, COL iCol) const
{
	auto it = std::find_if(begin(), end(), [iRow, iCol](const IdentifyCells& cell) {
		return cell.IsInRange(iRow, iCol);
	});
	return it == end() ? nullptr : &(*it);
}

HiddenRowColTool::HiddenRowColTool(ISheet* pSheet, const RANGE& rg) : m_usedRange(rg)
{
	Init(pSheet, rg);
}

void HiddenRowColTool::Init(ISheet* pSheet, const RANGE& rg)
{
	if (!pSheet)
		return;
	IRowColOp* pRowColOp = pSheet->LeakOperator();
	if (!pRowColOp)
		return;
	for (ROW iRow = rg.RowFrom(); iRow <= rg.RowTo(); ++iRow)
	{
		if (!pRowColOp->GetRowHidden(iRow))
			continue;
		m_setHiddenRow.emplace(iRow);
	}
	for (COL iCol = rg.ColFrom(); iCol <= rg.ColTo(); ++iCol)
	{
		if (!pRowColOp->GetColHidden(iCol))
			continue;
		m_setHiddenCol.emplace(iCol);
	}
}

bool HiddenRowColTool::IsHiddenRow(ROW iRow) const
{
	return m_setHiddenRow.find(iRow) != m_setHiddenRow.end();
}

bool HiddenRowColTool::IsHiddenCol(COL iCol) const
{
	return m_setHiddenCol.find(iCol) != m_setHiddenCol.end();
}

ROW HiddenRowColTool::GetExportJsonRowIdx(ROW iRow) const
{
	int nHiddenRowCnt = 0;
	for (ROW it : m_setHiddenRow)
	{
		if (it >= iRow)
			return iRow - nHiddenRowCnt;
		++nHiddenRowCnt;
	}
	return iRow - nHiddenRowCnt;
}

COL HiddenRowColTool::GetExportJsonColIdx(COL iCol) const
{
	int nHiddenColCnt = 0;
	for (COL it : m_setHiddenCol)
	{
		if (it >= iCol)
			return iCol - nHiddenColCnt;
		++nHiddenColCnt;
	}
	return iCol - nHiddenColCnt;
}

ROW HiddenRowColTool::GetExportJsonRowIdx1(ROW iRow) const
{
	int nHiddenRowCnt = 0;
	for (ROW it : m_setHiddenRow)
	{
		if (it == iRow)
			return iRow - nHiddenRowCnt - 1;
		if (it > iRow)
			return iRow - nHiddenRowCnt;
		++nHiddenRowCnt;
	}
	return iRow - nHiddenRowCnt;
}

COL HiddenRowColTool::GetExportJsonColIdx1(COL iCol) const
{
	int nHiddenColCnt = 0;
	for (COL it : m_setHiddenCol)
	{
		if (it == iCol)
			return iCol - nHiddenColCnt - 1;
		if (it > iCol)
			return iCol - nHiddenColCnt;
		++nHiddenColCnt;
	}
	return iCol - nHiddenColCnt;
}

IdentifyRange HiddenRowColTool::GetExportJsonRange(const IdentifyRange* pRg) const
{
	IdentifyRange dest;
	dest.iTop = GetExportJsonRowIdx(pRg->iTop);
	dest.iBottom = GetExportJsonRowIdx1(pRg->iBottom);
	dest.iLeft = GetExportJsonColIdx(pRg->iLeft);
	dest.iRight = GetExportJsonColIdx1(pRg->iRight);
	return dest;
}

inline static void GetCellText(ROW iRow, COL iCol, IETStringTools* pTools, BSTR* pBstr)
{
	if (!pTools)
		return;
	pTools->GetCellText(nullptr, iRow, iCol, pBstr, nullptr, -1, nullptr);
}

KIdentifyTable::KIdentifyTable(IKWorksheet* pSheet, wo::IEtProtectionCtx* ctx, JsonDataType nJsonDataType)
	: m_pWorksheet(pSheet)
	, m_pCtx(ctx)
	, m_nJsonDataType(nJsonDataType)
{
}

HRESULT KIdentifyTable::Init()
{
	if (!m_pWorksheet || !m_pCtx)
		return E_INVALIDARG;
	m_pSheet = m_pWorksheet->GetSheet();
	if (!m_pSheet)
		return E_FAIL;
	m_pWorksheet->GetWorkbook()->GetBook()->GetOperator(&m_spBookOp);
	if (!m_spBookOp)
		return E_FAIL;
	_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spTools);
	if (!m_spTools)
		return E_FAIL;
	m_spTools->SetEnv(m_pSheet);
	m_pBmp = m_pSheet->GetBMP();
	if (!m_pBmp)
		return E_FAIL;
	RANGE rg(m_pBmp);
	m_pWorksheet->GetUsedRange(&rg);
	if (rg.IsValid())
		m_usedRange.ResetRange(rg);
	if (m_nJsonDataType == JsonDataArrangement || m_nJsonDataType == JsonDataProofreading)
		m_spTool = std::make_unique<HiddenRowColTool>(m_pSheet, rg);
	return S_OK;
}

void KIdentifyTable::SetRangeLimit(UINT rowLimit)
{
	if (rowLimit != 0 && m_usedRange.iBottom - m_usedRange.iTop > rowLimit)
		m_usedRange.iBottom = m_usedRange.iTop + rowLimit;
}

bool KIdentifyTable::IsContinue() const
{
	return !m_identifyTableContinueProc || m_identifyTableContinueProc();
}

bool KIdentifyTable::IsNotEmptyCell(IDX iSheet, ROW iRow, COL iCol) const
{
	if (m_pCtx->isCellHidden(m_pSheet, iRow, iCol))
		return true;
	ks_bstr strText;
	GetCellText(iRow, iCol, m_spTools, &strText);
	if (strText.empty())
	{
		strText.clear();
		RANGE rg(m_pBmp);
		rg.SetCell(iSheet, iRow, iCol);
		m_pSheet->MergeExpandOnce(rg);
		if (!rg.IsSingleCell())
			GetCellText(rg.RowFrom(), rg.ColFrom(), m_spTools, &strText);
	}
	return isValidContent(strText.c_str());
}

void KIdentifyTable::GetTableCols(std::vector<std::pair<COL, COL>>& vecTableCols) const
{
	std::set<COL> setNotEmptyCol;
	IDX iSheet = 0;
	m_pSheet->GetIndex(&iSheet);
	for (COL iCol = m_usedRange.iLeft; iCol <= m_usedRange.iRight; ++iCol)
	{
		if (m_spTool && m_spTool->IsHiddenCol(iCol))
		{
			// 隐藏列不切割表格
			setNotEmptyCol.emplace(iCol);
			continue;
		}
		for (ROW iRow = m_usedRange.iTop; iRow <= m_usedRange.iBottom; ++iRow)
			if (IsNotEmptyCell(iSheet, iRow, iCol))
			{
				setNotEmptyCol.emplace(iCol);
				break;
			}
	}

	if (setNotEmptyCol.empty())
		return;

	auto itCol = setNotEmptyCol.begin();
	COL iLeft = *itCol;
	COL iLastCol = iLeft;
	++itCol;
	for (; itCol != setNotEmptyCol.end(); ++itCol)
	{
		COL iCol = *itCol;
		if (iCol - iLastCol > 1)
		{
			// 有空列且非隐藏
			bool hasNotHiddenCol = false;
			for (COL i = iLeft; i <= iLastCol; ++i)
				if (!(m_spTool && m_spTool->IsHiddenCol(i)))
				{
					hasNotHiddenCol = true;
					break;
				}
			// 仅有非空白列但都是隐藏列的话不push
			if (hasNotHiddenCol)
				vecTableCols.emplace_back(iLeft, iLastCol);
			iLeft = iCol;
		}
		iLastCol = iCol;
	}
	vecTableCols.emplace_back(iLeft, iLastCol);
}

void KIdentifyTable::GetTableRanges(std::vector<IdentifyRange>& vecTables) const
{
	// 以空列切表
	std::vector<std::pair<COL, COL>> vecTableCols;
	GetTableCols(vecTableCols);

	if (vecTableCols.empty())
		return;

	std::for_each(vecTableCols.begin(), vecTableCols.end(), [&vecTables, this](const std::pair<COL, COL>& colRg) {
		vecTables.emplace_back(m_usedRange.iTop, m_usedRange.iBottom, colRg.first, colRg.second);
	});
}

void KIdentifyTable::BuildCells(IDX iSheet, ROW iRow, COL iCol, IdentifyCells& cells) const
{
	if (m_pCtx->isCellHidden(m_pSheet, iRow, iCol))
		return;
	// strContent
	GetCellText(iRow, iCol, m_spTools, &cells.strContent);

	RANGE cellRg(m_pBmp);
	cellRg.SetCell(iSheet, iRow, iCol);
	// strValue2
	ks_stdptr<Range> spRange;
	VS(m_pWorksheet->GetRangeByData(&cellRg, &spRange));
	KComVariant varValue2;
	HRESULT hr = spRange->get_Value2(&varValue2);
	if (SUCCEEDED(hr))
	{
		KComVariant varValue2Bstr;
		hr = ::VariantChangeType(&varValue2Bstr, &varValue2, 0, VT_BSTR);
		if (SUCCEEDED(hr) && V_VT(&varValue2Bstr) == VT_BSTR)
		{
			cells.strValue2.attach(V_BSTR(&varValue2Bstr));
			V_BSTR(&varValue2Bstr) = nullptr;
		}
		if (cells.strValue2.empty() || cells.strContent.isEqual(cells.strValue2))
			cells.strValue2.clear();
	}

	const XF* pXf = nullptr;
	m_spBookOp->GetCellFormat(iSheet, iRow, iCol, &pXf, nullptr);
	if (!pXf)
		return;
	// numberformat
	if (pXf->pNumFmt)
	{
		ks_bstr bsformat;
		_kso_NF_LocalToBuiltin(static_cast<PCWSTR>(pXf->pNumFmt->fmt), &bsformat);
		if (bsformat.empty() || xstricmp(bsformat.c_str(), __X("General")) != 0)
			cells.strNumberFormat = static_cast<PCWSTR>(pXf->pNumFmt->fmt);
	}
	// fontsize
	constexpr float scale = 20;
	if (pXf->pFont)
		cells.fFontSize = static_cast<float>(pXf->pFont->dyHeight) / scale;
	// alignment
	HALIGNMENT_ETHAlign(pXf->alcH, cells.nAlignment);

	// merge
	m_pSheet->MergeExpandOnce(cellRg);
	if (!cellRg.IsSingleCell())
		cells.ResetRange(cellRg);
}

void KIdentifyTable::ExportJsonTable(const IdentifyRange& rg, QJsonObject& jsonTable) const
{
	IdentifyRange rgTable = m_spTool ? m_spTool->GetExportJsonRange(&rg) : rg;
	QJsonArray jsonStart;
	jsonStart.append(rgTable.iTop);
	jsonStart.append(rgTable.iLeft);
	jsonTable.insert("tableStart", jsonStart);
	QJsonArray jsonEnd;
	jsonEnd.append(rgTable.iBottom);
	jsonEnd.append(rgTable.iRight);
	jsonTable.insert("tableEnd", jsonEnd);
}

bool KIdentifyTable::TransformTable(const IdentifyRange& rg, QJsonObject& jsonTable, int& jmCells) const
{
	int nRowCount = rg.iBottom - rg.iTop + 1;
	int nColCount = rg.iRight - rg.iLeft + 1;
	IdentifyMergeCellsList mergeCellsList;
	IDX iSheet = 0;
	m_pSheet->GetIndex(&iSheet);
	RANGE range(m_pBmp);
	range.SetSheetFromTo(iSheet);
	QJsonObject jsonTableInfo;
	for (int i = 0; i < nRowCount; ++i)
	{
		ROW iRow = rg.iTop + i;
		if (m_spTool && m_spTool->IsHiddenRow(iRow))
			continue;

		bool isEmptyRow = true;
		QJsonObject jsonRow;
		for (int j = 0; j < nColCount; ++j)
		{
			if (!IsContinue())
				return false;

			COL iCol = rg.iLeft + j;
			if (m_spTool && m_spTool->IsHiddenCol(iCol))
				continue;
			if (!AmendRange(iRow, iCol))
				continue;

			const IdentifyCells* pCells = mergeCellsList.FindCellInMergeCells(iRow, iCol);
			range.SetRowFromTo(iRow);
			range.SetColFromTo(iCol);
			IdentifyCells cells(range);
			if (!pCells)
			{
				BuildCells(iSheet, iRow, iCol, cells);
				pCells = &cells;
				if (!cells.IsOneCell())
				{
					mergeCellsList.emplace_back(std::move(cells));
					pCells = &(mergeCellsList.back());
				}
			}
			// 超过10000行的合并单元格，非左上角的单元格丢弃
			if (pCells && IsRightCell(*pCells, iRow, iCol))
			{
				// 将单元格写Json数据
				pCells->ExportJson(jsonRow, m_spTool.get());
				if (!pCells->strContent.empty())
				{
					++jmCells;
					if (isEmptyRow)
						isEmptyRow = false;
				}
			}
		}
		// 将整行写Json数据, 空行不写
		if (jsonRow.empty() || isEmptyRow)
			continue;
		jsonTableInfo.insert(QString::number(m_spTool ? m_spTool->GetExportJsonRowIdx(iRow) : iRow).toStdString().c_str(), jsonRow);
	}
	// 将整表写Json数据
	jsonTable.insert("tableInfo", jsonTableInfo);
	ExportJsonTable(rg, jsonTable);
	return true;
}

bool KIdentifyTable::GetTables(const std::vector<IdentifyRange>& vecTables, QJsonObject& jsonObj) const
{
	QJsonArray jsonTableList;
	int jmCells = 0;
	for (size_t i = 0, cnt = vecTables.size(); i < cnt; ++i)
	{
		if (!IsContinue())
			return false;
		QJsonObject jsonTable;
		jsonTable.insert("tableIndex", QString::number(i).toStdString().c_str());
		if (!TransformTable(vecTables[i], jsonTable, jmCells))
			return false;
		jsonTableList.append(jsonTable);
	}
	jsonObj.insert("jmCells", jmCells);
	jsonObj.insert("jmRangeCells", (m_usedRange.iBottom - m_usedRange.iTop + 1) * (m_usedRange.iRight - m_usedRange.iLeft + 1));
	jsonObj.insert("tableList", jsonTableList);
	jsonObj.insert("caller", "db_sheet");
	return true;
}

void KIdentifyTable::GetSelectionRangeList(QJsonObject& jsonObj) const
{
	QJsonArray jsonSelectRangeList;
	for (const auto& selectRg : m_selectRangeList)
	{
		if (!IsContinue())
			return;
		IdentifyRange rg = m_spTool ? m_spTool->GetExportJsonRange(&selectRg) : selectRg;
		QJsonArray jsonRange;	// 上左下右
		jsonRange.append(rg.iTop);
		jsonRange.append(rg.iLeft);
		jsonRange.append(rg.iBottom);
		jsonRange.append(rg.iRight);
		jsonSelectRangeList.append(jsonRange);
	}
	jsonObj.insert("selectionRangeList", jsonSelectRangeList);
}

void KIdentifyTable::SetSelectionRange(const std::vector<RANGE>& rgs)
{
	m_selectRangeList.clear();

	std::for_each(rgs.begin(), rgs.end(), [this](const RANGE& rg) {
		m_selectRangeList.emplace_back(rg);
	});
}

void KIdentifyTable::Identify(QJsonObject& obj)
{
	// 限制处理的行列数
	if (!AmendRange(m_usedRange))
		return;

	// 根据选区方案获取需要处理的表格区域
	std::vector<IdentifyRange> vecTables;
	GetTableRanges(vecTables);
	// 提取属性
	if (!GetTables(vecTables, obj))
		return;
	// 转换至Json结构
	GetSelectionRangeList(obj);
}

void KIdentifyTable::SetIdentifyTableContinueProc(IdentifyTableContinueProc identifyTableContinueProc)
{
	m_identifyTableContinueProc = identifyTableContinueProc;
}

void KIdentifyTable::SetCustomRange(const RANGE& customRg)
{
    RANGE rg(m_pBmp);
    rg.SetRowFromTo(m_usedRange.iTop, m_usedRange.iBottom);
    rg.SetColFromTo(m_usedRange.iLeft, m_usedRange.iRight);
    m_usedRange.ResetRange(rg.Intersect(customRg));
}