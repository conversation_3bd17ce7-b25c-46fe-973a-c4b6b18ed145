#pragma once

namespace wo
{

namespace et_config
{

} // namespace et_config

namespace ksheet_config
{
    
namespace cell_history
{
    constexpr int kMinute = 60;
    constexpr int kHour = 60 * kMinute;
    constexpr int kDay = 24 * kHour;
    constexpr int kTimeBias = 1 * kHour;

    constexpr int kCellHistoryMaxPurgeTag = 100000; // 超出 10 万条协作记录时需要清理过期记录
    constexpr int kCellHistoryExpireSeconds = 7 * kDay + kTimeBias; // 只保留 7 天内的记录
    constexpr int kCellHistoryReserve = 2; // 只保留 7 天内的 2 条记录

} // namespace cell_history

} // ksheet_config
} // namespace wo
