﻿#include "etstdafx.h"

#include "et_dbsheet_common_helper.h"

#include "et_dbsheet_utils.h"
#include "src/workbook.h"
#include "src/workbooks.h"
#include "webbase/binvariant/binreader.h"
#include "../et_task_class.h"
#include "db_query_server.h"
#include "etcore/et_core_event_tracking.h"
#include "etcore/et_core_timestat.h"
#include "hresult_to_string.h"
#include "db/db_basic_itf.h"
#include "etcore/et_core_event_tracking.h"

extern Callback* gs_callback;

namespace wo 
{

DBSheetCommonHelper::DBSheetCommonHelper(KEtWorkbooks* wbs)
    : m_wbs(wbs), m_workbook(wbs->GetWorkbook()), m_pBook(m_workbook->GetCoreWorkbook()->GetBook())
{
}

DBSheetCommonHelper::DBSheetCommonHelper(KEtWorkbook* wb)
    : m_wbs(NULL), m_workbook(wb), m_pBook(m_workbook->GetCoreWorkbook()->GetBook())
{
}

DBSheetCommonHelper::DBSheetCommonHelper(IBook* pBook)
    : m_wbs(NULL), m_workbook(NULL), m_pBook(pBook)
{
}

DBSheetCommonHelper::~DBSheetCommonHelper()
{
}

EtDbId DBSheetCommonHelper::GetEtDbId_NoExcept(const binary_wo::VarObj& obj, WebName name) const noexcept
{
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	PCWSTR strId = obj.field_str(name);
	EtDbId id = INV_EtDbId;
	HRESULT hr = dbctx->DecodeEtDbId(strId, &id);
	if(FAILED(hr))
		return INV_EtDbId;

	return id;
}

EtDbId DBSheetCommonHelper::GetEtDbId(const binary_wo::VarObj& obj, WebName name) const
{
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	PCWSTR strId = obj.field_str(name);
	EtDbId id = INV_EtDbId;
	HRESULT hr = dbctx->DecodeEtDbId(strId, &id);
	if(FAILED(hr))
		ks_throw_s(E_INVALIDARG);

	return id;
}

HRESULT DBSheetCommonHelper::GetEtDbIds(const binary_wo::VarObj& obj, WebName name, std::vector<EtDbId>& EtDbIds) const
{
    IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
    binary_wo::VarObj ids = obj.get_s(name);
    if (ids.type() != binary_wo::typeArray)
    {
        return E_INVALIDARG;
    }
    int32 count = ids.arrayLength_s();
    EtDbIds.resize(count);
    EtDbId id = INV_EtDbId;
    for (int32 i = 0; i < count; ++i)
    {
        HRESULT hr = dbctx->DecodeEtDbId(ids.item_str(i), &id);
        if (FAILED(hr))
            return E_INVALIDARG;

        EtDbIds[i] = id;
    }

    return S_OK;
}

EtDbId DBSheetCommonHelper::GetEtDbId(binary_wo::VarObj obj)
{
	IDBSheetCtx* dbctx = _appcore_GainDbSheetContext();
	PCWSTR strId = obj.value_str();
	EtDbId id = INV_EtDbId;
	HRESULT hr = dbctx->DecodeEtDbId(strId, &id);
	if(FAILED(hr))
		ks_throw_s(E_INVALIDARG);

	return id;
}

HRESULT DBSheetCommonHelper::GetSheet(UINT sheetStId, ISheet** ppSheet)
{
	IDX sheetIdx = INVALIDIDX;
	m_pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_DBSHEET_SHEET_NOT_FOUND;

	ks_stdptr<ISheet> spSheet;
	m_pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet)
		return E_DBSHEET_SHEET_NOT_FOUND;

	*ppSheet = spSheet.detach();
	return S_OK;
}

HRESULT DBSheetCommonHelper::GetDbSheet(UINT sheetStId, ISheet** ppSheet)
{
	return DbSheet::GetDbSheet(m_pBook, sheetStId, ppSheet);
}

HRESULT DBSheetCommonHelper::GetWorksheet(UINT sheetStId, _Worksheet** ppWorksheet)
{
	if (nullptr == m_workbook)
		return E_FAIL;

	IDX sheetIdx = GetIdxById(sheetStId);
	if(sheetIdx == INVALIDIDX)	
		return E_DBSHEET_SHEET_NOT_FOUND;

	ks_stdptr<_Worksheet> spWorksheet = m_workbook->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	*ppWorksheet = spWorksheet.detach();
	return S_OK;
}

HRESULT DBSheetCommonHelper::GetDBSheetViews(UINT sheetStId, IDBSheetViews** ppIDbSheetViews)
{
	return DbSheet::GetDBSheetViews(m_pBook, sheetStId, ppIDbSheetViews);
}

HRESULT DBSheetCommonHelper::GetDBSheetView(UINT sheetStId, EtDbId viewId, IDBSheetView** ppIDbSheetView
	, IDBSheetViews** ppIDbSheetViews) const
{
	return DbSheet::GetDBSheetView(m_pBook, sheetStId, viewId, ppIDbSheetView, ppIDbSheetViews);
}

HRESULT DBSheetCommonHelper::GetDBSheetOp(UINT sheetStId, IDBSheetOp** ppDbSheetOp) const
{
	return DbSheet::GetDBSheetOp(m_pBook, sheetStId, ppDbSheetOp);
}

HRESULT DBSheetCommonHelper::GetDBChartStatisticMgr(UINT sheetStId, IDBChartStatisticMgr** ppChartStaticMgr)
{
	ks_stdptr<ISheet> spSheet;
	HRESULT hr = DbSheet::GetDbDashBoardSheet(m_pBook, sheetStId, &spSheet);
	if (FAILED(hr))
		return hr;

	return DbSheet::GetDBChartStatisticMgr(spSheet, ppChartStaticMgr);
}

HRESULT DBSheetCommonHelper::GetDBRange(IDBSheetView* pView, binary_wo::VarObj vRg, 
	bool extendRecordsViewAllToGridAll, bool extendFieldsViewAllToGridAll, IDBSheetRange** ppRg)
{
	ks_stdptr<IDBSheetRange> spRg;
	pView->CreateDBSheetRange(&spRg);

	WebStr strRecMode = vRg.field_str("recordsMode");
	if (xstrcmp(__X("include"), strRecMode) == 0)
	{
		binary_wo::VarObj recIds = vRg.get("records");
		for(int i = 0, cnt = recIds.arrayLength_s(); i < cnt; ++i)
		{
			spRg->AddRecordId(GetEtDbId(recIds.at(i)));
		}
	}
	else if (xstrcmp(__X("allInView"), strRecMode) == 0) 
	{
		if(extendRecordsViewAllToGridAll)
			spRg->SetRecordIds(pView->GetAllRecords());
		else
			spRg->SetRecordIds(pView->GetVisibleRecords());
	}
	else if (xstrcmp(__X("excludeInView"), strRecMode) == 0) 
	{
		binary_wo::VarObj recIds = vRg.get("records");
		std::unordered_set<EtDbId> exSet;
		for(int i = 0, cnt = recIds.arrayLength_s(); i < cnt; ++i)
		{
			exSet.insert(GetEtDbId(recIds.at(i)));
		}

		const IDBIds* pIds = pView->GetVisibleRecords();
		for(size_t i = 0; i < pIds->Count(); ++i) 
		{
			EtDbId id = pIds->IdAt(i);
			if(exSet.find(id) == exSet.end())
				spRg->AddRecordId(id);
		}
	}
	else if (xstrcmp(__X("exclude"), strRecMode) == 0)
	{
		binary_wo::VarObj recIds = vRg.get("records");
		std::unordered_set<EtDbId> exSet;
		for (int i = 0, cnt = recIds.arrayLength_s(); i < cnt; ++i)
			exSet.insert(GetEtDbId(recIds.at(i)));
		auto itExSetEnd = exSet.end();
		const IDBIds* pIds = pView->GetAllRecords();
		for (size_t i = 0; i < pIds->Count(); ++i) 
		{
			EtDbId id = pIds->IdAt(i);
			if (exSet.find(id) == itExSetEnd)
				spRg->AddRecordId(id);
		}
	}
	else
	{
		return E_INVALIDARG;
	}

	WebStr strFldMode = vRg.field_str("fieldsMode");
	if (xstrcmp(__X("include"), strFldMode) == 0)
	{
		binary_wo::VarObj fldIds = vRg.get("fields");
		for(int i = 0, cnt = fldIds.arrayLength_s(); i < cnt; ++i)
		{
			spRg->AddFieldId(GetEtDbId(fldIds.at(i)));
		}
	}
	else if (xstrcmp(__X("allInView"), strFldMode) == 0) 
	{
		if(extendFieldsViewAllToGridAll)
			spRg->SetFieldIds(pView->GetAllFields());
		else
			spRg->SetFieldIds(pView->GetVisibleFields());
	}
	else if (xstrcmp(__X("excludeInView"), strFldMode) == 0) 
	{
		binary_wo::VarObj fldIds = vRg.get("fields");
		std::unordered_set<EtDbId> exSet;
		for(int i = 0, cnt = fldIds.arrayLength_s(); i < cnt; ++i)
		{
			exSet.insert(GetEtDbId(fldIds.at(i)));
		}

		const IDBIds* pIds = pView->GetVisibleFields();
		for(size_t i = 0; i < pIds->Count(); ++i) 
		{
			EtDbId id = pIds->IdAt(i);
			if(exSet.find(id) == exSet.end())
				spRg->AddFieldId(id);
		}
	}
	else
	{
		return E_INVALIDARG;
	}
	*ppRg = spRg.detach();
	return S_OK;
}

HRESULT DBSheetCommonHelper::RangeCopy(const binary_wo::VarObj &param, ks_wstring& txt, bool& hasBreak)
{
    hasBreak = false;
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
    HRESULT hr = S_OK;

    IEtEventTracking* pEtEventTracking = m_pBook->LeakWorkspace()->GetEventTracking();
	etcore::TimeStat timeStat([&pEtEventTracking, &hr](unsigned int time){
		KComVariant varTime(time);
        KComVariant varRes(hr);
		pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_TOTAL_TIME_CONSUMING, varTime);
        pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_STATUS, varRes);
        if (FAILED(hr))
        {
            KComVariant varErrorName(GetErrWideString(hr));
            pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::ERROR_NAME, varErrorName);
        }
		pEtEventTracking->SendInfoAfterCopy();
	});

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (!spDbSheetView)
    {
        hr = E_DBSHEET_VIEW_NOT_FOUND;
		return hr;
    }

	ks_stdptr<IDBSheetRange> sheetRange;
	hr = GetDBRange(spDbSheetView, param.get("tarRg"), false, false, &sheetRange);
	if (FAILED(hr))
		return hr;

    IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
    ks_stdptr<IDBSheetOp> spDBSheetOp = spDbSheetView->GetSheetOp();
    DBJsonDataHelper jsonDataHelper;
    jsonDataHelper.SetSrcFileId(m_workbook->getFileId());
    bool copySubRecord = false;
    bool containPrimaryField = sheetRange->GetFieldIdx(pFieldsMgr->GetPrimaryField()) != INV_EtDbIdx;
    if(containPrimaryField && spDbSheetView->IsExistParentRecord())
    {
        copySubRecord = true;
        jsonDataHelper.setHandleSubRecord(true);
    }

	UINT32 fldCnt = sheetRange->GetFieldCnt();
	UINT32 recCnt = sheetRange->GetRecordCnt();
    QString cntStr = QString::number(recCnt * fldCnt);
    KComVariant varCnt(krt::utf16(cntStr));
    pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_AND_CUT_CELL_COUNT, varCnt);

    ks_stdptr<IDbUsersManager> spUsersMgr;
	m_pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);
    IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();

    for (UINT32 r = 0; r < recCnt; ++r)
	{
        std::unique_ptr<DBJsonRecord> record(new DBJsonRecord());
		EtDbId recId = sheetRange->GetRecordId(r);
        if (copySubRecord)
        {
            EtDbId parentId = spDBSheetOp->GetRecordsManager()->GetParentRecord(recId);
            if (parentId != INV_EtDbId)
            {
                while(parentId != INV_EtDbId && INV_EtDbIdx == sheetRange->GetRecordIdx(parentId))
                {
                    parentId = spDBSheetOp->GetRecordsManager()->GetParentRecord(parentId);
                }
                record->setParentRecordIdx(sheetRange->GetRecordIdx(parentId));
            }
        }

		for (UINT32 f = 0; f < fldCnt; ++f)
		{
			EtDbId fldId = sheetRange->GetFieldId(f);
			ks_stdptr<IDbField> spField;
			hr = pFieldsMgr->GetField(fldId, &spField);
			if (FAILED(hr))
				continue;

            ET_DbSheet_FieldType type = spField->GetType();
            const auto defaultCopy {[&]() {
                DBJsonDataString* obj = new DBJsonDataString();
                const_token_ptr pToken = nullptr;
                hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                if (SUCCEEDED(hr) && pToken != nullptr)
                {
                    ks_bstr bstrVal;
                    hr = spDbSheetView->GetValueString(recId, fldId, &bstrVal);
                    if (SUCCEEDED(hr))
                        obj->SetStringValue(bstrVal.c_str());
                }
                else
                    obj->SetIsBlank(TRUE);
                record->addCellData(obj);
            }};

            if (type == Et_DbSheetField_Lookup)
                type = ConvertSupportCopyLookupField(spField.get());
            if (spField->IsSyncLookupField() && (m_supportCopyTypes.find(type) == m_supportCopyTypes.end()))
            {
                defaultCopy();
            }
            else
            {
                switch(type)
                {
                    case Et_DbSheetField_MultiLineText:                   // 多行文本
                    case Et_DbSheetField_SingleLineText:                  // 单行文本
                    case Et_DbSheetField_ID:                              // 身份证
                    case Et_DbSheetField_Phone:                           // 电话
                    case Et_DbSheetField_Email:                           // 电子邮箱
                    case Et_DbSheetField_Formula:                         // 公式
                    case Et_DbSheetField_Lookup:                          // 引用
                    case Et_DbSheetField_AutoNumber:                      // 编号
                    case Et_DbSheetField_ParentRecord:                    // 父记录
                    case Et_DbSheetField_Automations:                     // 触发器
                    case Et_DbSheetField_CellPicture:                     // 单元格图片
                    case Et_DbSheetField_Button:                          // 按钮
                    case Et_DbSheetField_Cascade:                         // 级联选项
                    case Et_DbSheetField_BarCode:                         // 条码字段
                    default:                                              // 默认按str定义。若新增字段，需要考虑是否要扩展数据类型
                        defaultCopy();
                        break;
                    case Et_DbSheetField_Date:                            // 日期
                    case Et_DbSheetField_Time:                            // 时间
                    case Et_DbSheetField_CreatedTime:                     // 创建时间
                    case Et_DbSheetField_LastModifiedTime:                // 最后修改时间
                        {
                            DBJsonDataTime* obj = new DBJsonDataTime();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && pToken && alg::const_vdbl_token_assist::is_type(pToken))
                            {
                                double val = alg::const_vdbl_token_assist(pToken).get_value();
                                obj->SetDoubleValue(val);
                                ks_bstr bstrVal;
                                hr = spDbSheetView->GetValueString(recId, fldId, &bstrVal);
                                if (SUCCEEDED(hr))
                                {
                                    obj->SetStringValue(bstrVal.c_str());
                                }
                            }
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_Number:                          // 数值
                    case Et_DbSheetField_Checkbox:                        // 复选框
                    case Et_DbSheetField_Rating:                          // 等级
                    case Et_DbSheetField_Complete:                        // 进度条
                    case Et_DbSheetField_Currency:                        // 货币
                    case Et_DbSheetField_Percentage:                      // 百分比
                        {
                            DBJsonDataNumber* obj = new DBJsonDataNumber();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && pToken && alg::const_vdbl_token_assist::is_type(pToken))
                            {
                                double val = alg::const_vdbl_token_assist(pToken).get_value();
                                obj->SetRealNumber(val);
                                ks_bstr bstrVal;
                                hr = spDbSheetView->GetValueString(recId, fldId, &bstrVal);
                                if (SUCCEEDED(hr))
                                {
                                    obj->SetStringValue(bstrVal.c_str());
                                }
                            }
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_SingleSelect:                    // 单选项
                        {
                            DBJsonDataSelect* obj = new DBJsonDataSelect();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && nullptr != pToken && alg::const_vstr_token_assist::is_type(pToken))
                            {
                                alg::const_vstr_token_assist cvta(pToken);
                                obj->AddSelectItem(cvta.get_value());
                            }
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_MultipleSelect:                  // 多选项
                        {
                            DBJsonDataSelect* obj = new DBJsonDataSelect();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && nullptr != pToken && alg::const_handle_token_assist::is_type(pToken))
                            {
                                alg::const_handle_token_assist chta(pToken);
                                alg::TOKEN_HANDLE handle = chta.get_handle();
                                if (nullptr != handle && chta.get_handleType() == alg::ET_HANDLE_SELECTITEMS)
                                {
                                    ks_stdptr<IDbSelectItemHandle> spSelectItemHandle = handle->CastUnknown();
                                    if (spSelectItemHandle)
                                    {
                                        int cnt = spSelectItemHandle->Count();   
                                        if (cnt > 0)
                                        {
                                            for (UINT i = 0; i < cnt; ++i)
                                            {
                                                obj->AddSelectItem(spSelectItemHandle->ItemText(i));
                                            }
                                        }
                                    }
                                }
                            }
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_Url:                             // 超链接
                        {
                            DBJsonDataUrl* obj = new DBJsonDataUrl();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && nullptr != pToken && alg::const_handle_token_assist::is_type(pToken))
                            {
                                alg::const_handle_token_assist chta(pToken);
                                alg::TOKEN_HANDLE handle = chta.get_handle();
                                if (nullptr != handle && alg::ET_HANDLE_DBHYPERLINK == chta.get_handleType())
                                {
                                    ks_stdptr<IDbHyperlinkHandle> spHyperlinkHandle = handle->CastUnknown();
                                    if (spHyperlinkHandle)
                                    {
                                        obj->SetDispalayText(spHyperlinkHandle->GetDisplayText());
                                        obj->SetAddress(spHyperlinkHandle->GetAddress());
                                    }
                                }
                            }
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_Contact:                         // 联系人
                    case Et_DbSheetField_CreatedBy:                       // 创建者
                    case Et_DbSheetField_LastModifiedBy:                  // 最后修改者
                        {
                            DBJsonDataUser* obj = new DBJsonDataUser();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && nullptr != pToken)
                                ParseContactToken(pToken,obj);
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_Address:                         // 地址
                        {
                            DBJsonDataAddress* obj = new DBJsonDataAddress();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && nullptr != pToken && alg::const_handle_token_assist::is_type(pToken))
                            {
                                alg::const_handle_token_assist chta(pToken);
                                alg::TOKEN_HANDLE handle = chta.get_handle();
                                if (nullptr != handle && alg::ET_HANDLE_DBADDRESS == chta.get_handleType())
                                {
                                    ks_stdptr<IDbCascadeHandle> spAddressHandle = handle->CastUnknown();
                                    if (spAddressHandle) 
                                    {
                                        obj->SetDetail(spAddressHandle->GetDetail());
                                        ks_wstring strAddr;
                                        UINT cnt = spAddressHandle->GetCascadeLevel();
                                        if (cnt > 0)
                                        {
                                            QJsonArray ja_list;
                                            for (UINT i = 0; i < cnt; i++)
                                            {
                                                obj->AddDistrict(spAddressHandle->GetCascadeItemValue(i));
                                            }  
                                        }
                                    }
                                }
                            }
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_Department:                         
                        {
                            DBJsonDataDepartment* obj = new DBJsonDataDepartment();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && nullptr != pToken && alg::const_handle_token_assist::is_type(pToken))
                            {
                                alg::const_handle_token_assist chta(pToken);
                                alg::TOKEN_HANDLE handle = chta.get_handle();
                                if (nullptr != handle && alg::ET_HANDLE_TOKENARRAY == chta.get_handleType())
                                {
                                    ks_stdptr<IDbTokenArrayHandle> spHandleArray = handle->CastUnknown();
                                    for (UINT i = 0; i < spHandleArray->GetCount(); ++i)
                                    {
                                        const_token_ptr item = nullptr;
                                        VS(spHandleArray->Item(i, &item));
                                        alg::const_handle_token_assist hta(item);
                                        if (hta.get_handleType() == alg::ET_HANDLE_DBADDRESS)
                                        {
                                            ks_stdptr<IDbCascadeHandle> spHandle = hta.get_handle()->CastUnknown();
                                            if (spHandle)
                                            {
                                                DBJsonDataAddress addrObj;
                                                addrObj.SetDetail(spHandle->GetDetail());
                                                for (UINT j = 0; j < spHandle->GetCascadeLevel(); j++)
                                                {
                                                    addrObj.AddDistrict(spHandle->GetCascadeItemValue(j));
                                                }  
                                                obj->AddDepartmentItem(addrObj);
                                            }
                                        }
                                    }
                                }
                            }
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_Attachment:                      // Attachment
                        {
                            DBJsonDataAttachment* obj = new DBJsonDataAttachment();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && nullptr != pToken && alg::const_handle_token_assist::is_type(pToken))
                                ParseAttachmentToken(pToken, obj);
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                        }
                        break;
                    case Et_DbSheetField_Note:                            // Embedded FlexPaper, RichEdit
                        {
                            DBJsonDataNote* obj = new DBJsonDataNote();
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken);
                            if (SUCCEEDED(hr) && nullptr != pToken && alg::const_handle_token_assist::is_type(pToken))
                                ParseNoteToken(pToken, obj);
                            else
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                            break;
                        }
                    case Et_DbSheetField_OneWayLink:
                    case Et_DbSheetField_Link:
                        {                        
                            ks_stdptr<IDbField_Link> spFieldLink= spField;
                            UINT linkSheetId = spFieldLink->GetLinkSheet();
                            EtDbId linkViewId = spFieldLink->GetLinkView();
                            DBJsonDataLink* obj = new DBJsonDataLink(linkSheetId, linkViewId);
                            const_token_ptr pToken = nullptr;
                            hr = spDbSheetView->GetValueToken(recId, fldId, &pToken); 
                            if (SUCCEEDED(hr) && alg::const_handle_token_assist::is_type(pToken))
                            {
                                alg::const_handle_token_assist chta(pToken);
                                alg::TOKEN_HANDLE handle = chta.get_handle();
                                if (chta.get_handleType() == alg::ET_HANDLE_DBLINK)
                                {
                                    const IDbLinkHandle* pLinkHandle = handle->CastDbLink();
                                    if (pLinkHandle)
                                    {
                                        auto count = pLinkHandle->GetCount();
                                        obj->AssignContainerSize(count);
                                        for (auto i = 0; i < count; ++i)
                                        {
                                            obj->AddRecordItem(pLinkHandle->GetItemId(i));
                                        }
                                        obj->SetStringValue(pLinkHandle->GetConvertedString());
                                    }
                                }
                            }
                            else 
                                obj->SetIsBlank(TRUE);
                            record->addCellData(obj);
                            break;
                        }
                }
            }
        }
        jsonDataHelper.AddRecord(record.release());
    }

    jsonDataHelper.toJson(txt);
    hr = S_OK;
    return hr;
}

ET_DbSheet_FieldType DBSheetCommonHelper::ConvertSupportCopyLookupField(IDbField* pField)
{
    ET_DbSheet_FieldType type = pField->GetType();
    ks_stdptr<IDbField> spBaseField;
    ks_stdptr<IDbField_Lookup> spFieldLookup = pField;
    spFieldLookup->GetLookupBaseField(&spBaseField);
    ET_DbSheet_FieldType baseFieldType = ET_DbSheet_FieldType_Invalid;
    if (spBaseField)
        baseFieldType = spBaseField->GetType();

    if (m_supportCopyTypes.find(baseFieldType) != m_supportCopyTypes.end())
        return baseFieldType;
    return type;
}

void DBSheetCommonHelper::ParseContactToken(const_token_ptr pToken, DBJsonDataUser* pObj)
{
    alg::const_handle_token_assist hta(pToken);
    if (!hta)
        return;
    if (hta.get_handleType() == alg::ET_HANDLE_CONTACT)
    {
        ks_stdptr<IDbContactHandle> spContactHandle = hta.get_handle()->CastUnknown();
        if(spContactHandle)
        {
            ks_stdptr<IDbUsersManager> spUsersMgr;
            m_pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);
            DBJsonDataUserItem user;
            user.name = spContactHandle->GetNickname();
            user.id = spContactHandle->GetId();
            user.avatar = spContactHandle->GetAvatar();
            user.companyId = spUsersMgr->GetCompanyId(user.id.c_str());
            pObj->AddUserItem(user);
        }
    }
    else if (hta.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
    {
        const IDbTokenArrayHandle* handle = hta.get_handle()->CastArray();
        const UINT count = handle->GetCount();
        for (UINT i = 0; i < count; ++i)
        {
            const_token_ptr token {};
            handle->Item(i, &token);
            this->ParseContactToken(token, pObj);
        }
    }
}

void DBSheetCommonHelper::ParseAttachmentToken(const_token_ptr pToken, DBJsonDataAttachment* pObj)
{
    alg::const_handle_token_assist hta(pToken);
    if (!hta)
        return;
    if (hta.get_handleType() == alg::ET_HANDLE_DBATTACHMENT)
    {
        ks_stdptr<IDbAttachmentHandle> spDbAttachmentHandle = hta.get_handle()->CastUnknown();
        if(spDbAttachmentHandle)
        {
            DBJsonDataAttachmentItem attachment;
            attachment.id = spDbAttachmentHandle->GetFileId();
            attachment.type = spDbAttachmentHandle->GetContentType();
            attachment.size = spDbAttachmentHandle->GetSize();
            attachment.imageSize = spDbAttachmentHandle->GetImgSize();
            attachment.link = spDbAttachmentHandle->GetLinkUrl();
            attachment.source = spDbAttachmentHandle->GetSource();
            attachment.name = spDbAttachmentHandle->GetName();
            pObj->AddAttachmentItem(attachment);
        }
    }
    else if (hta.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
    {
        const IDbTokenArrayHandle* handle = hta.get_handle()->CastArray();
        const UINT count = handle->GetCount();
        for (UINT i = 0; i < count; ++i)
        {
            const_token_ptr token {};
            handle->Item(i, &token);
            this->ParseAttachmentToken(token, pObj);
        }
    }
}

void DBSheetCommonHelper::ParseNoteToken(const_token_ptr pToken, DBJsonDataNote* pObj)
{
    alg::const_handle_token_assist hta(pToken);
    if (!hta)
        return;
    if (hta.get_handleType() == alg::ET_HANDLE_DBNOTE)
    {
        ks_stdptr<IDbNoteHandle> spNoteHandle = hta.get_handle()->CastUnknown();
        if (spNoteHandle)
        {
            DBJsonDataNoteItem note;
            note.attachmentId = spNoteHandle->GetFileId();
            note.shortSummary = spNoteHandle->GetSummary();
            note.modifyDate = spNoteHandle->GetModifyDate();
            pObj->AddNoteItem(note);
        }
    }
    else if (hta.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
    {
        const IDbTokenArrayHandle* handle = hta.get_handle()->CastArray();
        const UINT count = handle->GetCount();
        for (UINT i = 0; i < count; ++i)
        {
            const_token_ptr token {};
            handle->Item(i, &token);
            this->ParseNoteToken(token, pObj);
        }
    }
}

HRESULT DBSheetCommonHelper::RangePaste(binary_wo::VarObj &param, DBJsonDataHelper* copyDataParser, IDBSheetRange* pTarRg, bool autoFillingRange, int srcRowCnt, int srcColCnt, int fillRowCnt, int fillColCnt, int& pasteFailCnt, bool& bAlterAutoField, EtDbId& uniquekLimitFieldId, std::vector<CellValueItem>& notUniqueItems, std::vector<CellValueItem>& invalidPhotoItems, std::vector<CellValueItem>& invalidScanItems)
{
     UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
    
    // 选择range
	if (pTarRg == NULL || pTarRg->GetRecordCnt() < 1 || pTarRg->GetFieldCnt() < 1)
		return E_INVALIDARG;

    ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

    ks_stdptr<IDbFieldsManager> spFieldsMgr = spDbSheetView->GetFieldsManager();
    ks_stdptr<IDbUsersManager> spUsersMgr;
	m_pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);

    struct CELL
    {
        EtDbId fldId;
        EtDbId recId;
    };
    std::map<ks_wstring, int>                       willCopyAttachments;       // 待服务端拷贝附件
    std::map<DBJsonDataAttachment*, std::vector<CELL>>   delayWriteAttachments;     // 延迟写入“图片和附件”
    std::map<DBJsonDataNote*, std::vector<CELL>>   delayWriteNotes;           // 延迟写入“付文本”
    std::map<ks_wstring, std::vector<ks_wstring>>   attachmentMaps;         // 附件映射

    PCWSTR srcFileId = copyDataParser->GetSrcFileId();
    PCWSTR dstFileId = m_workbook->getFileId();
    bool pasteSubRecord = false;
    ET_DbSheet_Sync_Type syncType = spDbSheetOp->GetSheetSyncType();
    bool isDbSyncSheet = (syncType == DbSheet_St_DB || syncType == DbSheet_St_GridSheet);
	IEtEventTracking* pEtEventTracking = nullptr;
	if (m_pBook)
	{
		pEtEventTracking = m_pBook->LeakWorkspace()->GetEventTracking();
		ASSERT(pEtEventTracking);
	}
    IPasteProgressNotify* pProgressNotify = m_pBook->GetPasteProgressNotify();
    {
    PasteStageScope pasteStageScope(pProgressNotify, PASTE_STAGE_CELLS, true);
    pProgressNotify->SetSubTaskTotal(fillColCnt * fillRowCnt);
    etcore::TimeStat timeStat([&pEtEventTracking](unsigned int time){
		if (pEtEventTracking)
		{
			KComVariant varTime(time);
			pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::CELL_DATA_TIME, varTime);
		}
	});
	for (auto c = 0; c < fillColCnt; ++c)
	{
		EtDbId fldId = pTarRg->GetFieldId(c);
		if (spDbSheetView->GetAllFields()->Id2Idx(fldId) == INV_EtDbIdx)
			continue;

		ks_stdptr<IDbField> spField;
		hr = spFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;

		ET_DbSheet_FieldType fldType = spField->GetType();
		for (auto r = 0; r < fillRowCnt; ++r)
		{
            pProgressNotify->CheckBreakAndUpdateProgress();

			EtDbId recId = pTarRg->GetRecordId(r);
			if (spDbSheetView->GetAllRecords()->Id2Idx(recId) == INV_EtDbIdx)
				continue;
			
            hr = E_FAIL;
            ks_wstring val;
            if (spField->IsAuto())
                hr = E_DBSHEET_ALTER_AUTO_FIELD;
            else if (isDbSyncSheet && spField->IsSyncField())
                hr = E_FAIL;
            else if (spField->GetType() == Et_DbSheetField_Button)
                hr = E_FAIL;
            else
            {
                const DBJsonRecord* record = nullptr;
                DBJsonDataHelper::CellOBJ* cdObj = NULL;
                if (autoFillingRange)
                {
                    record = copyDataParser->GetRecord(r % srcRowCnt);
                    cdObj = record ? record->getCellData(c % srcColCnt) : nullptr;
                }
                else
                {
                    record = copyDataParser->GetRecord(r);
                    cdObj = record ? record->getCellData(c) : nullptr;
                }

                if (cdObj == NULL)
                    continue;

                //粘贴记录父子关系
                if (copyDataParser->getHandleSubRecord())
                {
                    EtDbId parentId = pTarRg->GetRecordId(record->getParentRecordIdx());
                    if (parentId != INV_EtDbId)
                    {
                        pasteSubRecord = true;
                        hr = spDbSheetOp->GetRecordsManager()->SetRecordsRelation(parentId, &recId, 1);
                        if (FAILED(hr))
                            return hr;
                    }
                }
                cdObj->GetStringValue(val);
                DBJsonDataType srcType = cdObj->GetType();
                DBJsonDataType dstType = copyDataParser->TranslateFieldTypeToCopyDataType(fldType);

                if (cdObj->IsBlank())
                {
                    // 特殊处理
                    if (fldType == Et_DbSheetField_Checkbox 
                    || fldType == Et_DbSheetField_Rating 
                    || fldType == Et_DbSheetField_Complete)
                    {
                        alg::managed_vdbl_token_assist tokenAssist;
                        tokenAssist.create(0.0);
                        hr = spDbSheetOp->SetTokenValue(recId, fldId, tokenAssist);
                    }
                    else if (fldType == Et_DbSheetField_BarCode)
                    {
                        ks_stdptr<IDbField_BarCode> spBarCodeField = spField;
                        if (spBarCodeField->GetOnlyScanByCamera())
                        {
                            invalidScanItems.push_back(CellValueItem(fldId, recId, val));
                            pasteFailCnt++;
                            continue;
                        }
                        else
                        {
                            hr = spDbSheetOp->SetTokenValue(recId, fldId, NULL);
                        }
                    }
                    else
                    {
                        hr = spDbSheetOp->SetTokenValue(recId, fldId, NULL);
                    }
                }
                else if (srcType == dstType)
                {
                    switch (srcType)
                    {
                        case DBJsonDataType_Str:
                        {
                            // 拦截向只允许移动端扫码字段写信息
                            if (fldType == Et_DbSheetField_BarCode)
                            {
                                ks_stdptr<IDbField_BarCode> spBarCodeField = spField;
                                if (spBarCodeField->GetOnlyScanByCamera())
                                {
                                    invalidScanItems.push_back(CellValueItem(fldId, recId, val));
                                    pasteFailCnt++;
                                    continue;
                                }
                            }
                            hr = spDbSheetOp->PasteCell(recId, fldId, val.c_str());
                            break;
                        }
                        case DBJsonDataType_Time:
                            {
                                DBJsonDataTime* timeObj = dynamic_cast<DBJsonDataTime*>(cdObj);
                                if (timeObj == nullptr) 
                                    break;

                                double value = timeObj->GetDoubleValue();
                                alg::managed_vdbl_token_assist tokenAssist;
                                tokenAssist.create(value);
                                hr = spDbSheetOp->SetTokenValue(recId, fldId, tokenAssist);
                            }
                            break;
                        case DBJsonDataType_Num:
                            {
                                DBJsonDataNumber* numObj = dynamic_cast<DBJsonDataNumber*>(cdObj);
                                if (numObj == nullptr) 
                                    break;

                                double value = numObj->GetRealNumber();
                                if (fldType == Et_DbSheetField_Checkbox)
                                {
                                    if (value != 0 && value != 1)
                                        break;
                                }
                                else if (fldType == Et_DbSheetField_Complete)
                                {
                                    if (value < 0 || value > 1)
                                        break;
                                }
                                else if (fldType == Et_DbSheetField_Rating)
                                {
                                    if (value < 0)
                                        break;

                                    ks_stdptr<IDbField_Rating> spFieldRating = spField;
                                    UINT maxRating = spFieldRating->GetMaxRating();
                                    if (value > maxRating) value = maxRating;
                                    value = round(value);
                                }
                                alg::managed_vdbl_token_assist tokenAssist;
                                tokenAssist.create(value);
                                hr = spDbSheetOp->SetTokenValue(recId, fldId, tokenAssist);
                            }
                            break;
                        case DBJsonDataType_Sel:
                            {
                                DBJsonDataSelect *selObj = dynamic_cast<DBJsonDataSelect*>(cdObj);
                                if (selObj == nullptr) 
                                    break;
 
                                UINT itemSize = selObj->GetSelectItemsCount();
                                if (itemSize == 0)
                                {
                                    hr = spDbSheetOp->SetTokenValue(recId, fldId, nullptr);
                                    break;
                                }
                                ks_stdptr<IDbField_Select> spFieldSelect = spField;
                                UINT itemCount = spFieldSelect->Count();
                                if ((fldType == Et_DbSheetField_SingleSelect && itemSize!= 1) || itemSize > itemCount)
                                    break;

                                // 不能写入不存在的选项
                                std::vector<PCWSTR> fieldItemStrs;
                                for (int i = 0; i < itemCount; ++i)
                                {
                                    PCWSTR fieldItemStr;
                                    spFieldSelect->Item(i, &fieldItemStr, NULL, NULL);
                                    fieldItemStrs.push_back(fieldItemStr);
                                }
                                
                                BOOL itemMatch = FALSE;
                                for (int i = 0; i < itemSize; ++i)
                                {
                                    PCWSTR itemValue = selObj->GetSelectItem(i);
                                    itemMatch = FALSE;
                                    for (int j = 0; j < fieldItemStrs.size(); ++j)
                                    {
                                        PCWSTR fieldItemStr = fieldItemStrs.at(j);
                                        if (alg::StringOp<WCHAR, alg::chplcCaseWidthIsc>::equal(fieldItemStr, itemValue))
                                        {
                                            itemMatch = TRUE;
                                            break;
                                        }
                                    }
                                    if (!itemMatch) 
                                        break;
                                }
                                if (!itemMatch)
                                    break;

                                ks_wstring wstr;
                                selObj->GetStringValue(wstr);
                                if (fldType == Et_DbSheetField_MultipleSelect)
                                {
                                    ks_stdptr<IDbSelectItemHandle> spSelectItemHandle;
                                    _db_CreateObject(CLSID_KDbSelectItemHandle, IID_IDbSelectItemHandle, (void **)&spSelectItemHandle);
                                    hr = spSelectItemHandle->CreateFromCellStr(wstr.c_str());
                                    if (0 == spSelectItemHandle->Count())
                                        hr = spDbSheetOp->SetTokenValue(recId, fldId, nullptr);
                                    else
                                    {
                                        alg::managed_handle_token_assist mhta;
                                        mhta.create(alg::ET_HANDLE_SELECTITEMS, spSelectItemHandle);
                                        hr = spDbSheetOp->SetTokenValue(recId, fldId, mhta);
                                    }
                                }
                                else
                                {
                                    alg::managed_vstr_token_assist mvta;
                                    mvta.create(wstr.c_str());
                                    hr = spDbSheetOp->SetTokenValue(recId, fldId, mvta);
                                }
                            }
                            break;
                        case DBJsonDataType_Url:
                            {
                                DBJsonDataUrl *urlObj = dynamic_cast<DBJsonDataUrl*>(cdObj);
                                if (urlObj == nullptr) 
                                    break;

                                ks_stdptr <IDbHyperlinkHandle> spTokenHyperlink;
                                _db_CreateObject(CLSID_KDbHyperlinkHandle, IID_IDbHyperlinkHandle, (void**) &spTokenHyperlink);
                                spTokenHyperlink->Init(urlObj->GetDisplayText(), urlObj->GetAddress());
                                alg::managed_handle_token_assist mhta;
                                mhta.create(alg::ET_HANDLE_DBHYPERLINK, spTokenHyperlink);
                                hr = spDbSheetOp->SetTokenValue(recId, fldId, mhta);
                            }
                            break;
                        case DBJsonDataType_Usr:
                            {
                                DBJsonDataUser *usrObj = dynamic_cast<DBJsonDataUser*>(cdObj);
                                if (usrObj == nullptr) 
                                    break;

                                int userCount = usrObj->GetUsersCount();
                                if (userCount == 0)
                                {
                                    hr = spDbSheetOp->SetTokenValue(recId, fldId, nullptr);
                                    break;
                                }
                                ks_wstring txt;
                                for (int i=0; i < userCount; ++i)
                                {
                                    DBJsonDataUserItem* pUser = usrObj->GetUser(i);
                                    if (!spUsersMgr->IsExist((pUser->id).c_str()))
                                    {
                                        spUsersMgr->UpdateDbsheetUserInfo((pUser->id).c_str(), (pUser->name).c_str(), (pUser->avatar).c_str(), (pUser->companyId).c_str());
                                    }

                                    txt.append(pUser->id);
                                    if (i < userCount - 1)
                                        txt.append(__X(","));
                                }
                                hr = spDbSheetOp->PasteCell(recId, fldId, txt.c_str());
                            }
                            break;
                        case DBJsonDataType_Addr:
                            {
                                DBJsonDataAddress *addrObj = dynamic_cast<DBJsonDataAddress*>(cdObj);
                                if (addrObj == nullptr) 
                                    break;

                                ks_stdptr<IDbField_Cascade> spAddressField = spField;
                                UINT level = spAddressField->GetCascadeLevel();
                                int districtsCount = addrObj->GetDistrictsCount();
                                if (districtsCount < level)
                                    break;

                                std::vector<PCWSTR> _districts;
                                _districts.reserve(districtsCount);
                                for (int i = 0; i < districtsCount; i++)
                                {
                                    PCWSTR district = addrObj->GetDistrict(i);
                                    if (i < level)
                                    {
                                        _districts.push_back(district);
                                    }
                                }
                                ks_wstring detail = addrObj->GetDetail();
                                BOOL dstWithDetail = spAddressField->GetWithDetailedInfo();
                                if (dstWithDetail && xstrcmp(detail.c_str(), __X("")) == 0)
                                    break;
                                
                                ks_stdptr <IDbCascadeHandle> spTokenAddress;
                                _db_CreateObject(CLSID_KDbCascadeHandle, IID_IDbCascadeHandle, (void**) &spTokenAddress);
                                spTokenAddress->Init(_districts.data(), _districts.size(), dstWithDetail ? detail.c_str() : NULL, DBCT_Address);
                                alg::managed_handle_token_assist mhta;
                                mhta.create(alg::ET_HANDLE_DBADDRESS, spTokenAddress);
                                hr = spDbSheetOp->SetTokenValue(recId, fldId, mhta);
                            }
                            break;
                        case DBJsonDataType_Dept:
                            {
                                if (xstrcmp(srcFileId, dstFileId) != 0)
                                {
                                    hr = E_FAIL;
                                }
                                else
                                {
                                    DBJsonDataDepartment *deptObj = dynamic_cast<DBJsonDataDepartment*>(cdObj);
                                    if (deptObj == nullptr) 
                                        break;

                                    int departmentCnt = deptObj->GetDepartmentCount();
                                    if (departmentCnt == 0)
                                         break;
                                    ks_stdptr<IDbField_Cascade> spDepartmentField = spField;
                                    bool  multi = spDepartmentField->GetMultiValue();
                                    ks_stdptr <IDbTokenArrayHandle> spTokenArray;
                                    _db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle, (void**)&spTokenArray);
                                    for (int i = 0; i < departmentCnt; ++i)
                                    {
                                        if (!multi && i ==1)
                                            break;
                                        ks_stdptr<IDbCascadeHandle> spTokenDepartment;
                                        _db_CreateObject(CLSID_KDbCascadeHandle, IID_IDbCascadeHandle, (void**)&spTokenDepartment);
                                        DBJsonDataAddress* pItem = deptObj->GetDepartmentItem(i);
                                        int districtCount = pItem->GetDistrictsCount();
                                        std::vector<PCWSTR> districts;
                                        districts.reserve(districtCount);
                                        for (int j = 0; j < districtCount; j++)
                                        {
                                            districts.push_back(pItem->GetDistrict(j));
                                        }
                                        
                                        spTokenDepartment->Init(districts.data(), districts.size(), pItem->GetDetail(), DBCT_Department);
                                        alg::managed_handle_token_assist mhta;
                                        mhta.create(alg::ET_HANDLE_DBADDRESS, spTokenDepartment);
                                        spTokenArray->Add(mhta.detach());
                                    }
                                    alg::managed_handle_token_assist mhta;
                                    mhta.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
                                    hr = spDbSheetOp->SetTokenValue(recId, fldId, mhta);
                                }
                            }
                            break;
                        case DBJsonDataType_Atta:
                            {
                                /**
                                 * 图片和附件
                                 * 如果源文件和当前文件是同一个文件，则直接粘贴
                                 * 如果源文件和当前文件不是同一个，判断附件的source类型（本地上传/cloud文档）
                                 *    如果附件是本地上传，则添加至“待拷贝”列表中 
                             *    如果附件是本地上传，则添加至“待拷贝”列表中 
                                 *    如果附件是本地上传，则添加至“待拷贝”列表中 
                                 *    如果附件是cloud文档，则直接写入
                                 */

                                 // 拦截向只允许移动端上传图片字段写信息
                                if (fldType == Et_DbSheetField_Attachment)
                                {
                                    ks_stdptr<IDbField_Attachment> spAttachmentField = spField;
                                    if (spAttachmentField->GetOnlyUploadByCamera())
                                    {
                                        invalidPhotoItems.push_back(CellValueItem(fldId, recId, val));
                                        pasteFailCnt++;
                                        continue;
                                    }
                                }
                                DBJsonDataAttachment *attaObj = dynamic_cast<DBJsonDataAttachment*>(cdObj);
                                if (attaObj == nullptr) 
                                    break;

                                int attachmentsCount = attaObj->GetAttachmentsCount();
                                if (attachmentsCount == 0) 
                                {
                                    hr = spDbSheetOp->SetTokenValue(recId, fldId, nullptr);
                                    break;
                                }
                                if (xstrcmp(srcFileId, dstFileId) == 0)
                                {
                                    ks_stdptr <IDbTokenArrayHandle> spTokenArray;
                                    _db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle, (void**)&spTokenArray);
                                    for (int i = 0; i < attachmentsCount; ++i)
                                    {
                                        DBJsonDataAttachmentItem* pAttachment = attaObj->GetAttachmentItem(i);
                                        ks_stdptr<IDbAttachmentHandle> spTokenHandle;
                                        _db_CreateObject(CLSID_KDbAttachmentHandle, IID_IDbAttachmentHandle, (void **)&spTokenHandle);
                                        spTokenHandle->Init(pAttachment->id.c_str(), (DbSheet_AttachmentSource)pAttachment->source, pAttachment->type.c_str(), pAttachment->name.c_str(), pAttachment->size, pAttachment->link.c_str(), pAttachment->imageSize.c_str());
                                        alg::managed_handle_token_assist mhta;
                                        mhta.create(alg::ET_HANDLE_DBATTACHMENT, spTokenHandle);
                                        spTokenArray->Add(mhta.detach());
                                    }
                                    alg::managed_handle_token_assist mhta;
                                    mhta.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
                                    hr = spDbSheetOp->SetTokenValue(recId, fldId, mhta);
                                }
                                else
                                {
                                    // 添加到"待拷贝"列表，延迟写入
                                    CELL cell = {fldId, recId};
                                    if (delayWriteAttachments.find(attaObj) == delayWriteAttachments.end())
                                    {
                                        std::vector<CELL> cellList;
                                        cellList.push_back(cell);
                                        delayWriteAttachments[attaObj] = cellList;
                                    }
                                    else
                                        delayWriteAttachments[attaObj].push_back(cell);

                                    for (int i = 0; i < attachmentsCount; ++i)
                                    {
                                        DBJsonDataAttachmentItem* pAttachment = attaObj->GetAttachmentItem(i);
                                        ks_wstring attaId = pAttachment->id.c_str();
                                        if (pAttachment->source == AttachmentSource_upload_ks3 && willCopyAttachments.find(attaId) == willCopyAttachments.end())
                                            willCopyAttachments[attaId] = 1;    // 跨文档复制粘贴时，附件和图片 只需要拷贝1份
                                    }
                                    hr = S_OK;
                                }
                            }
                            break;
                        case DBJsonDataType_Note:
                            {
                                DBJsonDataNote *noteObj = dynamic_cast<DBJsonDataNote*>(cdObj);
                                if (noteObj == nullptr) 
                                    break;

                                int notesCount = noteObj->GetNotesCount();
                                if (notesCount == 0) 
                                    break;
                                // 添加到"待拷贝"列表，延迟写入
                                CELL cell = {fldId, recId};
                                if (delayWriteNotes.find(noteObj) == delayWriteNotes.end())
                                {
                                    std::vector<CELL> cellList;
                                    cellList.push_back(cell);
                                    delayWriteNotes[noteObj] = cellList;
                                }
                                else
                                    delayWriteNotes[noteObj].push_back(cell);

                                DBJsonDataNoteItem* pNote = noteObj->GetNoteItem(0);
                                ks_wstring attaId = pNote->attachmentId.c_str();
                                if (willCopyAttachments.find(attaId) != willCopyAttachments.end())
                                    willCopyAttachments[attaId] += 1;
                                else
                                    willCopyAttachments[attaId] = 1;
                                hr = S_OK;
                            break;
                            }
                        case DBJsonDataType_Link:
                            {
                                // 对于来自同一个file,且关联的sheet相同, 且目标view相同或者粘贴处的目标view为全部视图的情况下，使用recId直接设置token，否则使用旧的逻辑匹配关键字段的文本.
                                DBJsonDataLink *linkObj = dynamic_cast<DBJsonDataLink*>(cdObj);
                                if (linkObj == nullptr) 
                                    break;                                
                                ks_stdptr<IDbField_Link> spLinkField= spField;
                                bool supportMulti = spLinkField->GetSupportMultiLinks();
                                UINT linkSheetStId = spLinkField->GetLinkSheet();
                                EtDbId linkViewId = spLinkField->GetLinkView();
                                bool bUseRecId = xstrcmp(srcFileId, dstFileId) == 0 && linkSheetStId == linkObj->GetLinkSheet() 
                                    && (linkViewId == INV_EtDbId || linkViewId == linkObj->GetLinkView());
                                if (bUseRecId)
                                {
                                    ks_stdptr<IDBSheetOp> spDbLinkSheetOp;
                                    hr = DbSheet::GetDBSheetOp(m_pBook, linkSheetStId, &spDbLinkSheetOp);
                                    if (FAILED(hr))
                                        return hr;
                                    const IDBIds* pRecords = nullptr;
                                    if (linkViewId == INV_EtDbId)
                                    {
                                        pRecords = spDbLinkSheetOp->GetAllRecords();
                                    }
                                    else
                                    {
                                        ks_stdptr<IDBSheetViews> spDbLinkViews;
                                        hr = spDbLinkSheetOp->GetDbSheetViews(&spDbLinkViews);
                                        if (FAILED(hr))
                                            return hr;
                                        ks_stdptr<IDBSheetView> spDbLinkView;
                                        hr = spDbLinkViews->GetItemById(linkViewId, &spDbLinkView);
                                        if (FAILED(hr))
                                            return hr;
                                        pRecords = spDbLinkView->GetVisibleRecords();
                                    }
                                    std::unordered_set<EtDbId> visableRecSet;
                                    for (int i = 0; i < pRecords->Count() ; i++)
                                    {
                                        visableRecSet.insert(pRecords->IdAt(i));
                                    }
                                    std::vector<EtDbId> insertRecVec;
                                    const std::vector<EtDbId>& pasteVec = linkObj->GetRecIdContainer();
                                    for (const auto& item: pasteVec)
                                    {
                                        if (visableRecSet.find(item) != visableRecSet.end())
                                        {
                                            insertRecVec.emplace_back(item);
                                            if (!supportMulti && insertRecVec.size()==1)
                                                break;
                                        }
                                    }
                                    if (!insertRecVec.empty())
                                    {
                                        ks_stdptr<IFormula> spNewFmla;
                                        hr = spLinkField->GenerateLinkFormula(linkSheetStId, &insertRecVec[0], insertRecVec.size(), &spNewFmla);
                                        if (FAILED(hr))
                                            return hr;
                                        hr = spDbSheetOp->SetFormula(recId, fldId, spNewFmla);
                                        if (FAILED(hr))
                                            return hr;
                                    }
                                    if (insertRecVec.size() != pasteVec.size())
                                    // 部分失败，可能复制/粘贴这段时间内有些记录不可见/不存在了
                                        hr = S_FALSE;
                                    else
                                        hr = S_OK;
                                }
                                else 
                                {
                                    hr = spDbSheetOp->PasteCell(recId, fldId, val.c_str());
                                }
                                break;
                            }
                    }
                }
                else if (dstType == DBJsonDataType_Usr || dstType == DBJsonDataType_Atta || dstType == DBJsonDataType_Note)
                    hr = E_FAIL;
                else if (dstType == DBJsonDataType_Str)
                {
                    // 拦截向只允许移动端扫码字段写信息
                    if (fldType == Et_DbSheetField_BarCode)
                    {
                        ks_stdptr<IDbField_BarCode> spBarCodeField = spField;
                        if (spBarCodeField->GetOnlyScanByCamera())
                        {
                            invalidScanItems.push_back(CellValueItem(fldId, recId, val));
                            pasteFailCnt++;
                            continue;
                        }
                        else
                        {
                            hr = spDbSheetOp->PasteCell(recId, fldId, val.c_str());
                        }
                    }
                    else
                    {
                        hr = spDbSheetOp->PasteCell(recId, fldId, val.c_str());
                    }
                }
                else
                    hr = spDbSheetOp->PasteCell(recId, fldId, val.c_str());
            }

            if (FAILED(hr))
			{
				if (hr == E_DBSHEET_ALTER_AUTO_FIELD)
					bAlterAutoField = true;
				else if(hr == E_DBSHEET_VALUE_NOT_UNIQUE_IN_FIELD)
				{
                    notUniqueItems.push_back(CellValueItem(fldId, recId, val));
					if(notUniqueItems.size() > 1000)
					{
                        uniquekLimitFieldId = fldId;
						return E_DBSHEET_PASTECELL_ERROR_EXCEEDED_LIMITS;
                    }	
                }

				// 改为继续粘贴其他cell
				pasteFailCnt += 1;
			}
		}
	}
    }

    // 回放时attachmentMaps保存附件拷贝的映射
    if (param.has("attachmentMaps"))
    {
        binary_wo::VarObj attachmentMapsList = param.get("attachmentMaps");
        if (attachmentMapsList.type() == binary_wo::typeArray)
        {
            for (int i = 0; i < attachmentMapsList.arrayLength_s(); i++)
            {
                binary_wo::VarObj item = attachmentMapsList.at_s(i);
                ks_wstring oldAttaId = item.field_str("id");
                binary_wo::VarObj newAttaIds = item.get_s("newIds");
                std::vector<ks_wstring> list;
                for (int j = 0; j < newAttaIds.arrayLength_s(); j++)
                {
                    list.push_back(ks_wstring(newAttaIds.at_s(j).value_str()));
                }
                attachmentMaps[oldAttaId] = list;
            }
        }
    }

    if (attachmentMaps.empty() && willCopyAttachments.size() > 0)
    {
        // 服务端拷贝附件
        constexpr size_t copyAttachmentsBatchSize = 10;
        WOLOG_INFO << "[CopyAttachmentSync] :" << "sourceId: " << srcFileId << ", destinationId: " << dstFileId;

        PasteStageScope pasteStageScope(pProgressNotify, PASTE_STAGE_DOWNLOAD_ATTACHMENTS, true);
        pProgressNotify->SetSubTaskTotal(willCopyAttachments.size());
        etcore::TimeStat timeStat([&pEtEventTracking](unsigned int time){
            if (pEtEventTracking)
            {
                KComVariant varTime(time);
                pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::DOWNLOAD_ATTACHMENTS_TIME, varTime);
            }
        });
        auto it = willCopyAttachments.begin();
        binary_wo::VarObj attachmentMapsList = param.add_field_array("attachmentMaps", binary_wo::typeStruct);
        while (it != willCopyAttachments.end())
        {
            std::map<ks_wstring, int> batchMap;
            size_t curCnt = 0;
            while (it != willCopyAttachments.end() && curCnt < copyAttachmentsBatchSize)
            {
                batchMap[it->first] = it->second;
                ++curCnt;
                ++it;
            }
            BatchCopyAttachments(batchMap, attachmentMaps, attachmentMapsList, srcFileId);

            pProgressNotify->CheckBreak();
            pProgressNotify->UpdateProgress(curCnt);
        }
    }

    {
    // “附片和附件”
    PasteStageScope pasteStageScope(pProgressNotify, PASTE_STAGE_ATTACHMENTS, true);
    pProgressNotify->SetSubTaskTotal(delayWriteAttachments.size());
    etcore::TimeStat timeStat([&pEtEventTracking](unsigned int time){
        if (pEtEventTracking)
        {
            KComVariant varTime(time);
            pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::CELL_ATTACHMENTS_TIME, varTime);
        }
    });
    for (auto it = delayWriteAttachments.begin(); it != delayWriteAttachments.end(); ++it)
    {
        pProgressNotify->CheckBreakAndUpdateProgress();

        DBJsonDataAttachment *attaObj = (*it).first;
        std::vector<CELL> cellList = (*it).second;
        for (CELL cell : cellList)
        {
            int attachmentsCount = attaObj->GetAttachmentsCount();
            ks_stdptr <IDbTokenArrayHandle> spTokenArray;
            _db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle, (void**)&spTokenArray);
            for (int i = 0; i < attachmentsCount; ++i)
            {
                DBJsonDataAttachmentItem* pAttachment = attaObj->GetAttachmentItem(i);
                ks_wstring oldAttachmentId = pAttachment->id.c_str();
                ks_wstring newAttachmentId = oldAttachmentId;
                if (pAttachment->source == AttachmentSource_upload_ks3)
                {
                    if (attachmentMaps.find(oldAttachmentId) == attachmentMaps.end())
                    {
                        pasteFailCnt += 1;
                        continue;
                    }
                    std::vector<ks_wstring> newAttachmentIds = attachmentMaps[oldAttachmentId];
                    if (newAttachmentIds.empty())
                    {
                        pasteFailCnt += 1;
                        continue;
                    }
                    newAttachmentId = newAttachmentIds[0];  // 只会有一个
                }
                ks_stdptr<IDbAttachmentHandle> spTokenHandle;
                _db_CreateObject(CLSID_KDbAttachmentHandle, IID_IDbAttachmentHandle, (void **)&spTokenHandle);
                spTokenHandle->Init(newAttachmentId.c_str(), (DbSheet_AttachmentSource)pAttachment->source, pAttachment->type.c_str(), pAttachment->name.c_str(), pAttachment->size, pAttachment->link.c_str(), pAttachment->imageSize.c_str());
                alg::managed_handle_token_assist mhta;
                mhta.create(alg::ET_HANDLE_DBATTACHMENT, spTokenHandle);
                spTokenArray->Add(mhta.detach());
            }
            if (spTokenArray->GetCount() == attachmentsCount)
            {
                alg::managed_handle_token_assist mhta;
                mhta.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
                hr = spDbSheetOp->SetTokenValue(cell.recId, cell.fldId, mhta);
                if (FAILED(hr))
                    pasteFailCnt += 1;
            }
            else
                pasteFailCnt += 1;
        }
    }
    }

    {
    // “富文本”
    PasteStageScope pasteStageScope(pProgressNotify, PASTE_STAGE_NOTES, true);
    pProgressNotify->SetSubTaskTotal(delayWriteNotes.size());
    etcore::TimeStat timeStat([&pEtEventTracking](unsigned int time){
        if (pEtEventTracking)
        {
            KComVariant varTime(time);
            pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::CELL_NOTES_TIME, varTime);
        }
    });
    for (auto it = delayWriteNotes.begin(); it != delayWriteNotes.end(); ++it)
    {
        pProgressNotify->CheckBreakAndUpdateProgress();

        DBJsonDataNote *noteObj = (*it).first;
        std::vector<CELL> cellList = (*it).second;
        for (int i = 0; i < cellList.size(); ++i)
        {
            CELL cell = cellList[i];
            int notesCount = noteObj->GetNotesCount();
            if (notesCount == 0)
            {
                pasteFailCnt += 1;
                continue;
            }
            DBJsonDataNoteItem* pNote = noteObj->GetNoteItem(0);
            ks_wstring oldAttachmentId = pNote->attachmentId.c_str();
            if (attachmentMaps.find(oldAttachmentId) == attachmentMaps.end())
            {
                pasteFailCnt += 1;
                continue;
            }
            std::vector<ks_wstring> newAttachmentIds = attachmentMaps[oldAttachmentId];
            if (i >= newAttachmentIds.size())
            {
                pasteFailCnt += 1;
                continue;
            }
            ks_wstring newAttachmentId = newAttachmentIds[i]; 
            ks_stdptr<IDbNoteHandle> spTokenRichnote;
            _db_CreateObject(CLSID_KDbNoteHandle, IID_IDbNoteHandle, (void **)&spTokenRichnote);
            hr = spTokenRichnote->Init4IO(newAttachmentId.c_str(), pNote->shortSummary.c_str(), pNote->modifyDate);
            if (FAILED(hr))
            {
                pasteFailCnt += 1;
                continue;
            }
            alg::managed_handle_token_assist mhta;
            mhta.create(alg::ET_HANDLE_DBNOTE, spTokenRichnote);
            hr = spDbSheetOp->SetTokenValue(cell.recId, cell.fldId, mhta);
            if (FAILED(hr))
                pasteFailCnt += 1;
        }
    }
    }
    //复制父子记录关系，可能导致记录顺序层级显示不对， 触发排序，重排记录顺序
    if (pasteSubRecord)
    {
        spDbSheetView->GetConstOrderManager()->ToggleForceSort();
    }
    
	if (srcRowCnt == 0 || srcColCnt == 0)
		pasteFailCnt += 1;

	return S_OK;
}

void DBSheetCommonHelper::BatchCopyAttachments(const std::map<ks_wstring, int>& willCopyAttachments,
                                               std::map<ks_wstring, std::vector<ks_wstring>>& attachmentMaps,
                                               binary_wo::VarObj& attachmentMapsList,
                                               PCWSTR srcFileId)
{
    if (!gs_callback || !gs_callback->copyAttachmentSync)
        return;

    binary_wo::BinWriter bw;
    bw.addStringField(srcFileId, "sourceId");
    bw.addStringField(m_workbook->getFileId(), "destinationId");
    bw.beginArray("attachments");
    for (auto it = willCopyAttachments.begin(); it != willCopyAttachments.end(); ++it)
    {
        bw.beginStruct();
        bw.addStringField(it->first.c_str(), "id");
        bw.addInt32Field(it->second, "amount");
        bw.endStruct();

        WOLOG_INFO << "[CopyAttachmentSync] attachment:" << it->first.c_str() << ", amount: " << it->second;
    }
    bw.endArray();
    
    binary_wo::BinWriter::StreamHolder sh = bw.buildStream();
    WebSlice attachmentData = {sh.get(), bw.writeLength()};
    WebSlice responseData = {nullptr, 0};
    gs_callback->copyAttachmentSync(&attachmentData, &responseData);
    binary_wo::BinReader rd(responseData.data, responseData.size);
    binary_wo::VarObjRoot responseRoot = rd.buildRoot();
    binary_wo::VarObj response = responseRoot.cast();

    // ks_string byteStr;
    // for (int i = 0; i < responseData.size; ++i)
    // {
    //     if (i == 0)
    //         byteStr.append("[");
    //     int v = (int)(*(char*)(responseData.data + i)); 
    //     byteStr.append(std::to_string(v).c_str());
    //     if (i == responseData.size - 1)
    //         byteStr.append("]");
    //     else
    //         byteStr.append(" ");
    // }
    // WOLOG_INFO << "[CopyAttachmentSync] binary rawdata: " << byteStr.c_str();

    if (response.has("internalFailure") && !response.field_bool("internalFailure"))
    {
        binary_wo::VarObj result = response.get_s("result");
        for (int i = 0; i < result.arrayLength_s(); i++)
        {
            binary_wo::VarObj item = result.at_s(i);
            PCWSTR oldAttaId = item.field_str("id");
            binary_wo::VarObj newAttaIds = item.get_s("newId");
            if (newAttaIds.arrayLength_s() > 0) 
            {
                binary_wo::VarObj attachmentMapItem = attachmentMapsList.add_item_struct();
                attachmentMapItem.add_field_str("id", oldAttaId);
                binary_wo::VarObj newIdList= attachmentMapItem.add_field_array("newIds", binary_wo::typeString);
                std::vector<ks_wstring> list;
                for (int j = 0; j < newAttaIds.arrayLength_s(); j++)
                {
                    ks_wstring newId = newAttaIds.at_s(j).value_str();
                    list.push_back(newId);
                    newIdList.add_item_str(newId.c_str());
                }
                attachmentMaps[oldAttaId] = list;
            }
        }
        WOLOG_INFO << "[CopyAttachmentSync] done. ";
    }
    else
    {
        WOLOG_INFO << "[CopyAttachmentSync] failed.";
    }

    // 回调通知释放变量
    if (gs_callback && gs_callback->onCopyAttachmentSyncDone)
        gs_callback->onCopyAttachmentSyncDone();
}

HRESULT DBSheetCommonHelper::GetAllDataText(const binary_wo::VarObj &param, ks_wstring& txt, bool& hasBreak, bool &bAlterAttachmentField, bool& bAlterNoteField)
{
	hasBreak = false;
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
    HRESULT hr = S_OK;

    IEtEventTracking* pEtEventTracking = m_pBook->LeakWorkspace()->GetEventTracking();
	etcore::TimeStat timeStat([&pEtEventTracking, &hr](unsigned int time){
		KComVariant varTime(time);
        KComVariant varRes(hr);
		pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_TOTAL_TIME_CONSUMING, varTime);
        pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_STATUS, varRes);
        if (FAILED(hr))
        {
            KComVariant varErrorName(GetErrWideString(hr));
            pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::ERROR_NAME, varErrorName);
        }
		pEtEventTracking->SendInfoAfterCopy();
	});

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (!spDbSheetView)
    {
        hr = E_DBSHEET_VIEW_NOT_FOUND;
		return hr;
    }

	ks_stdptr<IDBSheetRange> sheetRange;
	hr = GetDBRange(spDbSheetView, param.get("tarRg"), false, false, &sheetRange);
	if (FAILED(hr))
		return hr;

	UINT32 fldCnt = sheetRange->GetFieldCnt();
	UINT32 recCnt = sheetRange->GetRecordCnt();
    QString cntStr = QString::number(recCnt * fldCnt);
    KComVariant varCnt(krt::utf16(cntStr));
    pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_AND_CUT_CELL_COUNT, varCnt);
	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();

	for (UINT32 r = 0; r < recCnt; ++r)
	{
		EtDbId recId = sheetRange->GetRecordId(r);
		for (UINT32 f = 0; f < fldCnt; ++f)
		{
			EtDbId fldId = sheetRange->GetFieldId(f);
			ks_bstr bstrVal;
			ks_stdptr<IDbField> spField;
			hr = pFieldsMgr->GetField(fldId, &spField);
			if (FAILED(hr))
				continue;
			ET_DbSheet_FieldType type = spField->GetType();
			if (type == Et_DbSheetField_Lookup)
			{
				ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
				ks_stdptr<IDbField> spBaseField;
				hr = spFieldLookup->GetLookupBaseField(&spBaseField);
				if (FAILED(hr))
					continue;
				type = spBaseField->GetType();
			}
			if (type == Et_DbSheetField_Attachment || type == Et_DbSheetField_Note)
			{
				bAlterAttachmentField = type == Et_DbSheetField_Attachment;
				bAlterNoteField = type == Et_DbSheetField_Note;
			}
			else
				spDbSheetView->GetDisplayString(recId, fldId, &bstrVal);

			if (f != 0)
				txt += ks_wstring(__X("\t"));
			if(bstrVal.c_str())
			{
				ks_wstring cellValue(bstrVal.c_str());
				if (cellValue.find_first_of(__Xc('\n')) != ks_wstring::npos || cellValue.find_first_of(__Xc('\r')) != ks_wstring::npos)
				{
					size_t pos = 0;
					while (true)
					{
						auto idx = cellValue.find_first_of(__Xc('"'), pos);
						if (idx != ks_wstring::npos)
						{
							cellValue.replace(idx, 1, __X("\"\""));
							pos = idx + 2;
							continue;
						}
						break;
					}
					cellValue = __X("\"") + cellValue + __X("\"");
				}
				txt += cellValue;
				if (txt.size() > 2 * 1024 * 1024)
					hasBreak = true;
			}
		}
        if (txt.empty() || r != recCnt - 1)
		    txt += ks_wstring(__X("\n"));
	}
    hr = S_OK;
	return hr;
}

IDX DBSheetCommonHelper::GetIdxById(UINT sheetStId)
{
	IDX sheetIdx = INVALIDIDX;
	m_pBook->STSheetToRTSheet(sheetStId, &sheetIdx);
	return sheetIdx;
}

void DBSheetCommonHelper::GetDBPos(binary_wo::VarObj pos, DbIdPostion& tar)
{
	if (pos.empty())
	{
		tar.id = INV_EtDbId;
		tar.isBack = FALSE;
		return;
	}
	binary_wo::VarObj vId = pos.get("id");
	tar.id = GetEtDbId(vId);
	if (tar.id != INV_EtDbId)
	{
		tar.isBack = pos.field_bool("isBack");
	}
}

BOOL DBSheetCommonHelper::IsFieldEmpty(IDBSheetView* pView, EtDbId fldId)
{
	ks_stdptr<IDBSheetRange> spRg;
	pView->CreateDBSheetRange(&spRg);
	spRg->SetRecordIds(pView->GetAllRecords());
	spRg->AddFieldId(fldId);

	UINT32 recCnt = spRg->GetRecordCnt();
	for (UINT32 i = 0; i < recCnt; ++i)
	{
		EtDbId rid = spRg->GetRecordId(i);
		ks_bstr bstrVal;
		HRESULT hr = pView->GetValueString(rid, fldId, &bstrVal);
		if (FAILED(hr))
			return false;
		if (!bstrVal.empty())
			return false;
	}

	return true;
}

void DBSheetCommonHelper::AcceptorAddEtDbId(ISerialAcceptor* acpt, WebName name, EtDbId id)
{
    EtDbIdStr buf;
	_appcore_GainDbSheetContext()->EncodeEtDbId(id, &buf);
	acpt->addString(name, buf);
}

HRESULT DBSheetCommonHelper::setGetViewCurModifyTar(binary_wo::VarObj param)
{
	if (!param.has("modifyTar"))
		return S_OK; // 默认就是Auto 不需要设置

	if (xstrcmp(param.field_str("modifyTar"), __X("Common")) == 0)
	{
		ks_stdptr<IDbtBookCtx> spDbBookCtx;
		VS(m_pBook->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbBookCtx));
		spDbBookCtx->GetViewCustomCtx()->SetCurCommon();
		return S_OK;
	}
	else if (xstrcmp(param.field_str("modifyTar"), __X("Auto")) == 0) 
	{
		return S_OK; // 默认就是Auto 不需要设置
	}
    else if (xstrncmp(param.field_str("modifyTar"), __X("Conn"), 4) == 0)
    {
        wo::IEtRevisionContext* pRevCtx = _etcore_GetEtRevisionContext();
        IKUserConn* user = NULL;
        if (pRevCtx != NULL)
            user = pRevCtx->getUser();
        ks_stdptr<IDbtBookCtx> spDbBookCtx;
        VS(m_pBook->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbBookCtx));
        DBCDMConnType connType = DBCDMConnType::Origin;
        if (xstrcmp(param.field_str("modifyTar"), __X("Conn_Calendar")) == 0)
            connType = DBCDMConnType::Calendar;
        spDbBookCtx->GetViewCustomCtx()->SetCurConnId(user ? user->connID() : NULL, connType);
        return S_OK;
    }
	return E_INVALIDARG;
}

} // namespace wo
