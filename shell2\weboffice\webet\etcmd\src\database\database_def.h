﻿#ifndef __WEBET_DATABASE_DEF_H__
#define __WEBET_DATABASE_DEF_H__

#include "wo/wo_database_enum.h"
namespace wo
{
namespace Database
{

#define DB_DEFAULT_TABLE_STYLE          BTSI_NONE
#define DB_DEFAULT_COL_WIDTH            1995
#define DB_DEFAULT_FONT_FAMILY          __X("微软雅黑")
#define DB_DEFAULT_FONT_HEIGHT          200

struct SheetInitConfig
{
    // 这里的默认值是为了和老的命令兼容, 不要改
    SheetInitConfig()
        : nEmptyRows(1)
        , nZoom(120)
        , nTopPadding(60)
        , nBottomPadding(60)
        , nLeftPadding(60)
        , nRightPadding(60)
        , strViewName(__X("GridView"))
        , rowHeight(480)
        , bHasViewConfigs(false)
    {}

    INT nEmptyRows;
    INT nZoom;
    INT nTopPadding;
    INT nBottomPadding;
    INT nLeftPadding;
    INT nRightPadding;
    PCWSTR strViewName;
    INT rowHeight;
    BOOL bHasViewConfigs;
};

class FieldContext;


struct GenQRLabelCol
{
    COL col = INVALID_COL;
    ks_wstring pageId;
    ROW headerRow = INVALID_ROW;
    COL keyCol = INVALID_COL;
    std::vector<COL> refCols;
};

interface IPermissionChecker
{
    virtual ~IPermissionChecker() {}
    virtual HRESULT CheckPermission(FieldContext *, const RANGE &) PURE;
};

interface IFieldModifier : public IPermissionChecker
{
    virtual ~IFieldModifier() {}
    virtual HRESULT Set(FieldContext *, const RANGE &) PURE;
    virtual HRESULT Clear(FieldContext *, const RANGE &) PURE;
};

interface IFieldIdentifier
{
    virtual ~IFieldIdentifier() {}
    virtual BOOL Identify(FieldContext *, const RANGE &, VALIDATION) PURE;
};

interface IFieldInitialiser : public IPermissionChecker
{
    virtual ~IFieldInitialiser() {}
    virtual HRESULT Initialise(FieldContext *, const RANGE &) PURE;
};

interface IDbField
{
    virtual ~IDbField() {}
    virtual FieldType GetType() PURE;
    virtual HRESULT Set(FieldContext *pContext, const RANGE &rg) PURE;
    virtual HRESULT Clear(FieldContext *pContext, const RANGE &rg) PURE;
    virtual HRESULT Initialise(FieldContext *pContext, const RANGE &rg) PURE;
    virtual BOOL Identify(FieldContext *pContext, const RANGE &rg, VALIDATION dv) PURE;
};

enum FieldEnumOption
{
    dbEnumAll,                      // 枚举所有类型
    dbEnumType,                     // 枚举特定类型
    dbEnumExcludeType,              // 枚举除开特定类型以外的所有类型
};

interface IFieldEnum
{
    virtual ~IFieldEnum() = default;
    virtual HRESULT Do(FieldContext *, IDbField *, const RANGE &, const VALIDATION&) PURE;
    virtual bool ShouldStopEnum()  { return false; };
};

} // Database
} // wo

#endif // __WEBET_DATABASE_DEF_H__