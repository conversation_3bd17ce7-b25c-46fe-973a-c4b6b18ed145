﻿#include "etstdafx.h"
#include "et_dbsheet_task_class.h"
#include "et_task_detail_xf.h"
#include "workbook.h"
#include "et_task_peripheral.h"
#include "et_dbsheet_utils.h"
#include "et_dbsheet_dashboard_utils.h"
#include "db2et_exporter.h"
#include "src/util.h"
#include "database/database.h"
#include "database/database_utils.h"
#include "database/database_field_context.h"
#include "helpers/protection_helper.h"
#include "helpers/webmime_helper.h"
#include <public_header/drawing/model/abstract_shape.h>
#include "webbase/clipboard_tools.h"
#include "kso/io/clipboard/ksomimetype.h"
#include "kso/io/clipboard/ksoclipboard.h"
#include "kfc/io/seq_cache_reader.h"
#include "kfc/charencoding/codepage.h"
#include "kfc/charencoding/guessencoding.h"
#include "et_dbsheet_filter_helper.h"
#include "dbsheet/et_dbsheet_copysheet_helper.h"
#include "wo/workbook_obj.h"
#include "kso/l10n/et/et_app.h"
#include <persist/persist_helper.h>
#include "et2db_exporter.h"
#include "addTemplateSheets.h"
#include "kso/textstd/texttool.h"
#include "helpers/varobject_helper.h"
#include "ettools/ettools_encode_decoder.h"
#include "helpers/app/update_app_view_helper.h"
#include "helpers/app/delete_app_helper.h"
#include "helpers/app/find_app_helper.h"
#include "webbase/wo_sa_helper.h"
#include "helpers/app/update_app_version_helper.h"
#include "ksheet/appsheet_copysheet_helper.h"
#include "book_format_converter/csv_adapter.h"
#include "book_format_converter/db_sync_adapter.h"
#include "book_format_converter/db_append_data_adapter.h"
#include "copy_dashboard_sheet_helper.h"
#include "helpers/sheet_operator_helper.h"
#include "share_sheet_helper.h"
#include "txt_parser.h"
#include "db_json_data_helper.h"
#include "et_binvar_spec.h"
#include "utils/et_gridsheet_utils.h"
#include <kbase/string/cstring.h>
#include "helpers/statsheet_helper.h"
#include "serialize_impl.h"
#include "db_sidebar_folder_tree_helper.h"
#include "helpers/varobject_helper.h"
#include "dashboard/db_dashboard_module_mgr_wrapper.h"
#include "dbsheet/dashboard/db_view_wrapper.h"
#include "db_query_server.h"
#include "dbsheet/et_dbsheet_syncsheet_utils.h"
#include "etcore/et_core_event_tracking.h"
#include "etcore/et_core_timestat.h"
#include "hresult_to_string.h"
#include "dashboard/et_dashboard_utils.h"
#include "webhook/webhook_helper.h"
#include "helpers/db_value_serialize_helper.h"
#include "common_log/common_log_helper.h"
#include "db/db_basic_itf.h"
#include "dbsheet/et_dbsheet_provider.h"

extern Callback* gs_callback;

namespace wo
{
struct STR_HASH
{
	size_t operator()(const ks_wstring& str) const
	{
		return alg::HashWString(str.c_str());
	}
};

struct InfoHlp
{
	InfoHlp(HRESULT& hr, IEtCollectInfo* pEtCollectInfo) 
		: m_hr(hr), 
		m_pEtCollectInfo(pEtCollectInfo) {};
	~InfoHlp() 
	{
		if (m_pEtCollectInfo == nullptr)
			return;
		KComVariant varRes(m_hr);
		m_pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_STATUS, varRes);
		if (FAILED(m_hr))
		{
			KComVariant varErrName(GetErrWideString(m_hr));
			m_pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::ERROR_NAME, varErrName);
		}
	};
	HRESULT& m_hr;
	IEtCollectInfo* m_pEtCollectInfo = nullptr;
};

using DBVUF = DbSheetViewUpdateFlag;
// ================== ETDbSheetTaskClassBase ==================

ETDbSheetTaskClassBase::ETDbSheetTaskClassBase(KEtWorkbook* wwb)
	: EtTaskExecBase(wwb)
	, m_pDbCtx(_appcore_GainDbSheetContext())
	, m_pEncodeDecoder(_appcore_GainEncodeDecoder())
	, m_commonHelper(wwb)
	, m_dbViewUpdateFlags(static_cast<DbSheetViewUpdateFlag>(
		static_cast<uint32_t>(DBVUF::Default) | static_cast<uint32_t>(DbSheetViewUpdateFlag::CalcCellAddDirty)))
{
}

HRESULT ETDbSheetTaskClassBase::PreExecute(KwCommand* cmd, KEtRevisionContext* ctx)
{
	HRESULT hr = EtTaskExecBase::PreExecute(cmd, ctx);
	if(FAILED(hr))
		return hr;
	
	binary_wo::VarObj cmdVarObj = cmd->cast();
	m_pDbCtx->BeginExec(cmdVarObj, ctx);

	// 所有临时操作均不支持undo
	binary_wo::VarObj param = cmdVarObj.get("param");
	if (param.has("modifyTar") && xstrncmp(param.field_str("modifyTar"), __X("Conn"), 4) == 0)
	{
		m_peri->setBanUndo(KwVersion::banUndoCur);
	}
	return hr;
}

static HRESULT UpdateViews(DbSheetViewUpdateFlag flags, DBSheetCommonHelper& helper, UINT sheetStId)
{
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	HRESULT hr = helper.GetDBSheetViews(sheetStId, &spDbSheetViews);
	ASSERT(spDbSheetViews);
	if (FAILED(hr))
		return S_OK;
	return spDbSheetViews->Update(flags);
}

static HRESULT UpdateChartStatisticData(DbSheetViewUpdateFlag flags, DBSheetCommonHelper& helper, UINT sheetStId)
{
	wo::util::SlowCallTimeStat cts("UpdateChartStatisticData", 30);
	ks_stdptr<IDBChartStatisticMgr> spChartStatisticMgr;
	HRESULT hr = helper.GetDBChartStatisticMgr(sheetStId, &spChartStatisticMgr);
	if (!spChartStatisticMgr)
		return E_DBSHEET_SHEET_NOT_FOUND;
	if (FAILED(hr))
		return S_OK;
	return spChartStatisticMgr->Update(flags);
}

HRESULT ETDbSheetTaskClassBase::PostExecute(HRESULT hr, KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj cmdVarObj = cmd->cast();

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if(SUCCEEDED(hr))
		UpdateStatSheetFormula(pBook);

	// collect and do view update. For dbsheet only
	if (SUCCEEDED(hr))
	{
		// 63d62b7 的改动有误, 引入了非预期的行为变更. 此前的注释称"代码中的diffused变量实际上恒为 false", 这个说法是错误的
		// 目前 db 公式重算时, 都会注册公式到容器内, 供 db 逻辑自行取用. 此处的逻辑定义为:
		// 1. 编辑操作中，总是从容器中取出已重算的db公式，令它们参与db的视图、仪表盘更新
		// 		当前编辑操作引发的重算会序列化到前端，为用户所感知；它们也会注册到上述容器内；因此它们需要参与db视图、仪表盘更新
		// 2. DoIdle 引发的空事务，则保持行为和 63d62b7 改动前一致: 只有【恢复计算是否全部完成】为true的场景，才去更新db视图、仪表盘
		// 		这既是原先的设计意图，也是63d62b7的预期（63d62b7本就没打算改变这个行为）。公式算到一半就去更新视图、仪表盘没有太大的意义，因为其它公式还没算完，
		// 		视图、仪表盘之后还要继续更新。使用未彻底完成的计算结果更新视图、仪表盘反而增加了性能开销，占据了本该分配给公式计算的资源，对于性能表现较差的样张，会雪上加霜
		if (alg::IsBitUsed(static_cast<DWORD>(m_dbViewUpdateFlags), static_cast<DWORD>(DBVUF::CalcCellAddDirty)) || 
			m_wwb->DbtCalcCellsNeedAddDirty())
		{
			wo::util::CallTimeStat cts("DiffuseCalcCells");
			hr = DbSheet::SubmitCollectedFmlCells2DbSheet(pBook);
			if (FAILED(hr))
				return hr;
			m_wwb->SetDbtCalcCellsNeednotAddDirty();
		}
		// P.S. 63d62b7 的改动还存在一个问题: 对于"编辑操作", 它引发的计算会立刻序列化到前端, 为用户所感知, 而更早版本引发的未全部完成的计算, 它已完成的部分并不会序列化到前端
		// 换言之, "编辑操作"后需要参与视图/仪表盘更新的公式应当仅包含本次编辑操作会影响的, 而 63d62b7 改动后的逻辑则会一并带上更早版本中那些算了一部分的公式, 由于这些公式不会
		// 序列化到前端, 参与视图和仪表盘更新后, 说不定反而会给用户造成困扰 (内核中公式结果已更新, 但前端感知不到这一点, 除非刷新页面)

		ks_stdptr<IDbSheetDirtyManager> spSheetDirtyMananger;
		pBook->GetExtDataItem(edBookDbDirtyManager, (IUnknown**)&spSheetDirtyMananger);
		// obtain sheetStId. for dbsheet only
		if (m_pDbCtx->IsAuthoritySettingDirty() || ctx->isAuthorityDirty())
		{
			hr = updateAll();
			if(SUCCEEDED(hr))
			{
				m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
			}
			else
			{
				std::abort();
			}
		}
		else if (m_pDbCtx->IsPrepareCurUser() || m_pDbCtx->IsAuthorityDirty())
		{
			hr = updateAll();
			// 不需要清事务
		}
		else if (cmdVarObj.has("__skipDbtViewsUpdate__") && cmdVarObj.field_bool("__skipDbtViewsUpdate__"))
		{
			; // 参数设置 __skipDbtViewsUpdate__ 为true时, 跳过常规视图更新
		}
		else if (spSheetDirtyMananger != nullptr && spSheetDirtyMananger->HasDirtySheet())
		{
			if (SUCCEEDED(hr))
			{
				wo::util::CallTimeStat cts("UpdateViews");
				class SheetStIdEnum : public ISheetStIdEnum
				{
				public:
					SheetStIdEnum(wo::KEtWorkbook* wwb, DbSheetViewUpdateFlag flags) : m_helper(wwb), m_flags(flags) {}
					HRESULT Do(UINT sheetStId) override
					{
						ks_stdptr<ISheet> spSheet;
						HRESULT hr = m_helper.GetSheet(sheetStId, &spSheet);
						if (FAILED(hr))
							return hr;
						if (spSheet->IsDbDashBoardSheet())
							return UpdateChartStatisticData(m_flags, m_helper, sheetStId);
						if (spSheet->IsDbSheet())
							return UpdateViews(m_flags, m_helper, sheetStId);
						return E_FAIL;
					}
				private:
					DBSheetCommonHelper m_helper;
					DbSheetViewUpdateFlag m_flags;
				};
				SheetStIdEnum sheetIdEnum(m_wwb, m_dbViewUpdateFlags);
				hr = spSheetDirtyMananger->EnumDirtySheetId(&sheetIdEnum);
			}
		}
		ks_stdptr<IDbUsersManager> spUsersMgr;
		pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);
		if (spUsersMgr && ctx->getUser())
		{
			QString strUserID = QString::fromUtf16(ctx->getUser()->userID());
			DbUserInfoQuery userInfo(spUsersMgr, strUserID);
			userInfo.QueryUserInfo();
			userInfo.QueryUserCompanyInfo();
		}
	}
	m_pDbCtx->EndExec(cmdVarObj, ctx, hr);

	return EtTaskExecBase::PostExecute(hr, cmd, ctx);
}

void ETDbSheetTaskClassBase::GetDBSheetViews(UINT sheetStId, IDBSheetViews** ppIDbSheetViews)
{
	m_commonHelper.GetDBSheetViews(sheetStId, ppIDbSheetViews);
}

void ETDbSheetTaskClassBase::GetDBSheetView(UINT sheetStId, EtDbId viewId, IDBSheetView** ppIDbSheetView, IDBSheetViews** ppIDbSheetViews)
{
	m_commonHelper.GetDBSheetView(sheetStId, viewId, ppIDbSheetView, ppIDbSheetViews);
}

void ETDbSheetTaskClassBase::AddEtDbId(binary_wo::VarObj& obj, WebName name, EtDbId id)
{
	EtDbIdStr buf;
	m_pDbCtx->EncodeEtDbId(id, &buf);
	obj.add_field_str(name, buf);
}

void ETDbSheetTaskClassBase::AddEtDbIdItem(binary_wo::VarObj& objArr, EtDbId id)
{
	EtDbIdStr buf;
	m_pDbCtx->EncodeEtDbId(id, &buf);
	objArr.add_item_str(buf);
}

EtDbId ETDbSheetTaskClassBase::GetEtDbId(const binary_wo::VarObj& obj, WebName name)
{
	return m_commonHelper.GetEtDbId(obj, name);
}

EtDbId ETDbSheetTaskClassBase::GetEtDbId(binary_wo::VarObj obj)
{
	return m_commonHelper.GetEtDbId(obj);
}

void ETDbSheetTaskClassBase::GetDBPos(binary_wo::VarObj pos, DbIdPostion& tar)
{
	m_commonHelper.GetDBPos(pos, tar);
}

bool ETDbSheetTaskClassBase::GetDBParentId(binary_wo::VarObj pos, EtDbId& parentId)
{
	parentId = INV_EtDbId;
	if (pos.has("parentId"))
    {
        parentId = m_commonHelper.GetEtDbId(pos, "parentId");
		return true;
    }
	return false;
}

HRESULT ETDbSheetTaskClassBase::GetDBRange(IDBSheetView* pView, binary_wo::VarObj vRg, 
	bool extendRecordsViewAllToGridAll, bool extendFieldsViewAllToGridAll, IDBSheetRange** ppRg)
{
	return m_commonHelper.GetDBRange(pView, vRg, extendRecordsViewAllToGridAll, extendFieldsViewAllToGridAll, ppRg);
}

HRESULT ETDbSheetTaskClassBase::GetAllRecordByParentRecords(IDBSheetView* pView, const EtDbId* pIds, UINT32 recCnt, IDBSheetRange**recordRg, bool getVisibleChildren)
{
    if (pIds == nullptr || recCnt == 0 || pView == nullptr)
        return E_FAIL;

    class KDbRecordRelationEnum : public IDbRecordRelationEnum
    {
    public:
        KDbRecordRelationEnum(std::vector<EtDbId>& ids, const IDBIds* visibleId) : m_idVec(ids), m_visibleId(visibleId) {}
        STDIMP TravelRecord(EtDbId id, BOOL hasChildren) override
        {
            if (m_visibleId)
            {
                if (m_visibleId->Id2Idx(id) != INV_EtDbIdx)
                    m_idVec.emplace_back(id);
            }
            else
                 m_idVec.emplace_back(id);

            return S_OK;
        }
        STDIMP DoAfterRecordChildrenTraveled() override { return S_OK; }

    private:
        std::vector<EtDbId>& m_idVec;
		const IDBIds* m_visibleId;
    };

	IDBSheetOp* pSheetOp = pView->GetSheetOp();
	const IDBIds* visibleId = getVisibleChildren ? pView->GetVisibleRecords() : nullptr;
	const IDBIds* orderIds = pView->GetOrderRecords();
	std::vector<EtDbId> recordIds;
    KDbRecordRelationEnum eunmer(recordIds, visibleId);
    for (UINT32 i = 0; i < recCnt; ++i)
    {
        recordIds.emplace_back(*pIds);
        HRESULT hr = pSheetOp->GetRecordsManager()->EnumRecordAllChildren(*pIds++, &eunmer);
        if (FAILED(hr))
            return hr;
    }

    if (recordRg)
    {
        ks_stdptr<IDBSheetRange> spNewRg;
        pSheetOp->CreateDBSheetRange(&spNewRg);
        spNewRg->SetFieldIds(pSheetOp->GetAllFields());

        auto cmp = [orderIds](EtDbId a, EtDbId b) -> bool {
            return orderIds->Id2Idx(a) < orderIds->Id2Idx(b);
        };
        std::sort(recordIds.begin(), recordIds.end(), cmp);

		for (UINT32 i = 0, recCnt = recordIds.size(); i < recCnt; ++i)
		{
			spNewRg->AddRecordId(recordIds[i]);
		}
        *recordRg = spNewRg.detach();
    }

    return S_OK;
}

HRESULT ETDbSheetTaskClassBase::GetAllParentRecords(IDBSheetOp* pSheetOp, IDBSheetRange*recordRg, std::vector<EtDbId>& parentRecords)
{
	if (pSheetOp == nullptr || recordRg == nullptr)
        return E_FAIL;

	parentRecords.clear();
	const EtDbId* pIds = NULL;
	UINT32 recCnt = 0;
	recordRg->GetRecordIds(&pIds, &recCnt);
	parentRecords.reserve(recCnt);
	for (UINT32 i = 0; i < recCnt; ++i)
	{
        const EtDbId& id = *pIds++;
        if (parentRecords.cend() == std::find_if(parentRecords.cbegin(), parentRecords.cend(), [id, pSheetOp](const EtDbId& item) { return pSheetOp->GetRecordsManager()->IsParentRecord(item, id); }))
            parentRecords.emplace_back(id);
    }
	return S_OK;
}
HRESULT ETDbSheetTaskClassBase::CheckProtectionSheetRange(IDBSheetRange* pRange, UINT sheetStId)
{
	HRESULT hr = S_OK;
	UINT32 fieldCnt = pRange->GetFieldCnt();
	for (UINT32 i = 0; i < fieldCnt; ++i)
	{
		hr = m_spProtectionJudgement->CheckFieldCanSetValue(sheetStId, pRange->GetFieldId(i));
		if (FAILED(hr))
			return hr;
	}

	UINT32 recordCnt = pRange->GetRecordCnt();
	for (UINT32 i = 0; i < recordCnt; ++i)
	{
		hr = m_spProtectionJudgement->CheckRecordCanEdit(sheetStId, pRange->GetRecordId(i));
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}
namespace
{

HRESULT updateUserInfo(IBook* pBook, VarObj param)
{
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	VarObj vContacts = param.get("userInfo");
	ks_stdptr<IDbUsersManager> spUsersMgr;
	pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);
	for (int i = 0; i < vContacts.arrayLength(); ++i)
	{
		VarObj vContact = vContacts.at(i);
        PCWSTR companyId = nullptr;
        if (vContact.has("companyId"))
            companyId = vContact.field_str("companyId");
		spUsersMgr->UpdateDbsheetUserInfo(vContact.field_str("id")
			, vContact.field_str("nickname"), vContact.field_str("avatar"), companyId);
	}
	return S_OK;
}

} // anonymous

HRESULT ETDbSheetTaskClassBase::attemptModifyLink2Multiple(IDBSheetView* pView, VarObj param, KEtRevisionContext* pCtx)
{
	HRESULT hr = S_OK;

	VAR_OBJ_EXPECT_ARRAY(param, "linkFieldIds");
	VarObj linkFieldIds = param.get_s("linkFieldIds");
	std::unordered_set<EtDbId> fldIdSet; // 简单的去重
	IDbFieldsManager *pCurDataFieldsMgr = pView->GetFieldsManager();
	for (int i = 0, c = linkFieldIds.arrayLength_s(); i < c; ++i)
	{
		EtDbId fldId = GetEtDbId(linkFieldIds.at_s(i));
		if (fldIdSet.end() != fldIdSet.find(fldId))
			continue;
		fldIdSet.insert(fldId);
		ks_stdptr<IDbField> spField;
		hr = pCurDataFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return E_DBSHEET_FIELD_NOT_FOUND;

		ASSERT(Et_DbSheetField_Link == spField->GetType());
		pCtx->setMessageBoxAnswer(WOMB_DBSHEET_LINK_NOT_MULTIPLE, IDOK);
		// 尝试修改字段配置，并在失败时返回 E_DBSHEET_AD_FIELD_MANAGE
		ks_stdptr<IDbField_Link> spField_Link = spField;
		if (nullptr == spField_Link)
			return E_FAIL;
		ks_stdptr<IDBSheetOp> spLinkedData;
		hr = m_commonHelper.GetDBSheetOp(spField_Link->GetLinkSheet(), &spLinkedData);
		if (FAILED(hr))
			return hr;
		IDbFieldsManager *pFieldsMgr = spLinkedData->GetFieldsManager();
		ks_stdptr<IDbField> spLinkedField;
		hr = pFieldsMgr->GetField(spField_Link->GetReversedLinkField(), &spLinkedField);
		if (FAILED(hr))
			return E_DBSHEET_FIELD_NOT_FOUND;
		// 修改字段配置前的权限检查
		hr = m_spProtectionJudgement->CheckFieldCanEdit(spField_Link->GetLinkSheet(), spField_Link->GetReversedLinkField());
		if (FAILED(hr))
			return hr; // E_DBSHEET_AD_FIELD_MANAGE
		ks_stdptr<IDbField_Link> spLinkedField_Link = spLinkedField;
		if (nullptr == spField_Link)
			return E_FAIL;
		spLinkedField_Link->SetSupportMultiLinks(TRUE);
	}

	return S_OK;
}

HRESULT ETDbSheetTaskClassBase::prepareMessageBoxIfSingleLink(IDBSheetView* pView, EtDbId, EtDbId fldId, VarObj param,
	HRESULT hr, DbReversedLinkParam& dbLinkParam)
{
	if (E_DBSHEET_LINK_NOT_MULTIPLE != dbLinkParam.hr)
		return hr;
	if (m_wwb->GetCoreApp()->WoMessageBox(WOMB_DBSHEET_LINK_NOT_MULTIPLE) == IDOK)
		return E_FAIL;
	
	// 往 pData 的 recId, fldId 尝试写入 val 时, dbLinkParam.hr 提示这个行为是失败的
	IDBSheetOp* pData = pView->GetSheetOp();
	{
		if (false == param.has("linkFieldIds"))
			param.add_field_array("linkFieldIds", typeString);
		VarObj linkFieldIds = param.get_s("linkFieldIds");
		EtDbIdStr buf;
		m_pDbCtx->EncodeEtDbId(fldId, &buf);
		linkFieldIds.add_item_str(buf);
	}
	if (dbLinkParam.rejectedRecNum > 0)
	{
		if (param.has("totalRejectedRecords"))
		{
			int totalRejectedRecords = param.field_int32("totalRejectedRecords");
			param.add_field_int32("totalRejectedRecords", totalRejectedRecords + dbLinkParam.rejectedRecNum);
		}
		else
		{
			param.add_field_int32("totalRejectedRecords", dbLinkParam.rejectedRecNum);
		}
		if (false == param.has("firstRejectedRecId"))
		{
			EtDbIdStr buf;
			m_pDbCtx->EncodeEtDbId(dbLinkParam.firstRejectedRecId, &buf);
			param.add_field_str("dbLinkParam.firstRejectedRecId", buf);
			// 将 dbLinkParam.firstRejectedRecId 在 primary 字段的单元格值，及其当前关联记录的 primary 字段单元格值给出去
			IDbFieldsManager* pFieldsManager = pData->GetFieldsManager();
			ks_stdptr<IDbField> spEditingField;
			VS(pFieldsManager->GetField(fldId, &spEditingField));
			ks_stdptr<IDbField_Link> spEditingLinkField = spEditingField;
			UINT linkedSheetStId = spEditingLinkField->GetLinkSheet();
			ks_stdptr<IDBSheetOp> spLinkedData;
			m_commonHelper.GetDBSheetOp(linkedSheetStId, &spLinkedData);
			IDbFieldsManager* pLinkedSheetFieldsManager = spLinkedData->GetFieldsManager();
			ks_bstr linkedRecordPrimaryCellStr;
			spLinkedData->GetValueString(dbLinkParam.firstRejectedRecId, pLinkedSheetFieldsManager->GetPrimaryField(), &linkedRecordPrimaryCellStr);
			param.add_field_str("selectedRecordDispText", linkedRecordPrimaryCellStr.c_str());

			ks_stdptr<IDbLinkHandle> spLinkHandle;
			const_token_ptr pToken = nullptr;
			VS(spLinkedData->GetValueToken(dbLinkParam.firstRejectedRecId, spEditingLinkField->GetReversedLinkField(), &pToken));
			if (alg::const_handle_token_assist::is_type(pToken))
			{
				alg::const_handle_token_assist chta(pToken);
				alg::TOKEN_HANDLE handle = chta.get_handle();
				if (handle != nullptr && chta.get_handleType() == alg::ET_HANDLE_DBLINK)
					spLinkHandle = handle->CastUnknown();
			}
			ASSERT(spLinkHandle);
			ASSERT(1 == spLinkHandle->GetCount());
			UINT originLinkedSheetStId = spLinkHandle->GetSheetId();
			EtDbId originLinkedRecId = spLinkHandle->GetItemId(0);

			ks_stdptr<IDBSheetOp> spOriginLinkedData;
			m_commonHelper.GetDBSheetOp(originLinkedSheetStId, &spOriginLinkedData);
			IDbFieldsManager* pOriginLinkedSheetFieldsManager = spOriginLinkedData->GetFieldsManager();
			ks_bstr originLinkedRecordPrimaryCellStr;
			spOriginLinkedData->GetValueString(originLinkedRecId, pOriginLinkedSheetFieldsManager->GetPrimaryField(), &originLinkedRecordPrimaryCellStr);
			param.add_field_str("linkedRecordDispText", originLinkedRecordPrimaryCellStr.c_str());
		}
	}
	return dbLinkParam.hr; // 返回 dbLinkParam.hr 以令事务失败
}

namespace
{

// 函数声明. 为了让diff具有高可读性, 没有调整函数的代码位置. 之后的提交可考虑进一步整理这些函数的位置
HRESULT createCopyFromRecord(IDBSheetView* pView, IDBSheetRange* pDbRg, EtDbId srcRecId, VarObj param,
	std::unique_ptr<DbReversedLinkParam>& upDbLinkParam, KDbSetCellValInvalidInfoCollector& collctor);

/**
 *  objRecoresValues = [ 
 * 		[ {fieldId: "1", val: "cat"}, {fieldId: "2", val: "1kg"} ] ,
 * 		[ {fieldId: "1", val: "dog"}, {fieldId: "2", val: "3kg", extData} ] ,
 * ]
 *
 *  extData = { displayText } // hyperlink
 *  extData = { userInfo } // contact
 */
HRESULT setRecordsValue(IDBSheetView* pView, IDBSheetRange* pDbRg, VarObj objRecoresValues, bool ignoreFailed,
	std::unique_ptr<DbReversedLinkParam>& upDbLinkParam, KDbSetCellValInvalidInfoCollector& collctor)
{
	int32 recoresCnt = objRecoresValues.arrayLength_s();
	if (recoresCnt < 1)
		return S_OK;

	IBook* pBook = pView->GetSheetOp()->GetBook();
	bool hasDbBidirectionalLinkField = false;
	std::unique_ptr<DbSheet::DbLinkHlp> upDbLinkHlp;
	HRESULT firstErrorHr = S_OK;

	// 批量赋值
	DbSheet::DbtBatchSubmitOptimizer batchSubmitter(pView->GetSheetOp(), _kso_GetWoEtSettings()->IsEnableDBResizeRowOpt());

	for (UINT32 idx = 0, count = pDbRg->GetRecordCnt(); idx < count; idx++)
	{
		EtDbId recId = pDbRg->GetRecordId(idx);
		binary_wo::VarObj vRecordVals = objRecoresValues.at_s(idx % recoresCnt);

		HRESULT hr = S_OK;
		for (UINT32 i = 0, cnt = vRecordVals.arrayLength_s(); i < cnt; ++i) 
		{
			binary_wo::VarObj item = vRecordVals.at(i);
			EtDbId fldId = DbSheet::GetEtDbId(item, "fieldId");
			IDbFieldsManager *spFieldsMgr = pView->GetFieldsManager();
			ks_stdptr<IDbField> spField;
			hr = spFieldsMgr->GetField(fldId, &spField);
			if (FAILED(hr))
				return hr;
			ET_DbSheet_FieldType fldType = spField->GetType();
			WebStr strVal = nullptr;
			if (fldType != Et_DbSheetField_Address && fldType != Et_DbSheetField_Cascade && fldType != Et_DbSheetField_Department)
				strVal = item.field_str("val");

			// 预处理
			switch (fldType)
			{
			case Et_DbSheetField_Email:
			{
				hr = pView->GetSheetOp()->SetHyperlinkAddress(recId, fldId, strVal);
				VarObj extData = item.get_s("extData");
				if (extData.type() == binary_wo::typeStruct && extData.has("displayText"))
				{
					strVal = extData.field_str("displayText");
				}
				break;
			}
			case Et_DbSheetField_Contact:
			{
				VarObj extData = item.get_s("extData");
				if (extData.type() == binary_wo::typeStruct && extData.has("userInfo"))
				{
					updateUserInfo(pBook, extData);
				}
				break;
			}
			case Et_DbSheetField_Link:
 				if (not hasDbBidirectionalLinkField)
				{
					hasDbBidirectionalLinkField = true;
					upDbLinkParam.reset(new DbReversedLinkParam(
						DbReversedLinkParam::TryModifyField2Multiple));
					upDbLinkHlp.reset(new DbSheet::DbLinkHlp(pBook, *upDbLinkParam));
				}
				break;
			default:
				break;
			}

			if (spField->IsAuto())
			{
				hr = E_DBSHEET_ALTER_AUTO_FIELD;
				collctor.InsertNormalCellItem(recId, fldId, strVal, hr);
			}
			else if (fldType == Et_DbSheetField_Address || fldType == Et_DbSheetField_Cascade || fldType == Et_DbSheetField_Department)
			{
				binary_wo::VarObj val = item.get_s("val");
				ASSERT(val.type() == typeStruct);
				hr = pView->SetValue(recId, fldId, val);
				if (hr == E_DBSHEET_AD_FIELD_EDIT)
					hr = S_FALSE;
				if(FAILED(hr))
				{
					//Todo:地址字段由于value是一个结构体，因此这里暂时expectedVal返回__X(""),后续若产品有需求，则再定义。
					collctor.InsertNormalCellItem(recId, fldId, __X(""), hr);
				}
			}
			else if (fldType == Et_DbSheetField_Url)
			{
				WebStr displayText = nullptr;
				VarObj extData = item.get_s("extData");
				if (extData.type() == binary_wo::typeStruct && extData.has("displayText"))
					displayText = extData.field_str("displayText");
				alg::managed_token_assist mta;
				IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
				if (FAILED(pCtx->Text2DbHyperlinkToken(displayText, strVal, &mta)))
					continue;
				hr = pView->GetSheetOp()->SetTokenValue(recId, fldId, mta);
				if (hr == E_DBSHEET_AD_FIELD_EDIT)
					hr = S_FALSE;
				if (FAILED(hr))
					collctor.InsertNormalCellItem(recId, fldId, strVal, hr);
			}
			else
			{
				hr = pView->SetValue(recId, fldId, strVal);
				if (hr == E_DBSHEET_AD_FIELD_EDIT)
					hr = S_FALSE;
				if(FAILED(hr))
				{
					collctor.InsertNormalCellItem(recId, fldId, strVal, hr);
				}
				
			}
			//产品要求：失败了不立马返回，而是先收集错误。
			//保持和原本接口返回值一致，收集第一个失败的返回值。
			if(FAILED(hr) && SUCCEEDED(firstErrorHr))
			{
				firstErrorHr = hr;
			}
		}
	}

	//产品要求：遍历完成所有的字段值，最后返回第一个失败的返回值，保持和原本接口返回值一致
	if(!ignoreFailed && FAILED(firstErrorHr))
		return firstErrorHr;

	return S_OK;
}

} // anonymous

HRESULT ETDbSheetTaskClassBase::setRecsValWrap(IDBSheetView* pView, IDBSheetRange* dbRg, VarObj objRecoresValues, bool ignoreFailed)
{
	// 如果是统计表则不能设置
	if(pView->GetSheetOp()->GetSheetSubType() == DbSheet_Sub_Type_StatSheet)
	{
		WOLOG_ERROR << "[ETDbSheetTaskClassBase::setRecsValWrap] It's not allow in statSheet";
		return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
	}

	KDbSetCellValInvalidInfoCollector collector(pView->GetSheetOp()->GetSheetId());
	std::unique_ptr<DbReversedLinkParam> upDbLinkParam;
	HRESULT hr = setRecordsValue(pView, dbRg, objRecoresValues, ignoreFailed, upDbLinkParam, collector);
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if (peripheral)
		{
			VarObj resObj = peripheral->resSelf();
			if(!collector.IsEmpty())
			{
				binary_wo::VarObj invalidCellsObj = resObj.add_field_struct("invalidCellsInfo");
				collector.SerialContent(invalidCellsObj);
			}
		}
	}

	if (FAILED(hr))
		return hr;
	if (upDbLinkParam && upDbLinkParam->adFieldEdit)
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if (peripheral)
		{
			VarObj resObj = peripheral->resSelf();
			resObj.add_field_bool("unable2ModifyLinkField", true);
		}
	}
	return hr;
}

static bool isDbValueEmpty(binary_wo::VarObj& val, ET_DbSheet_FieldType fieldType)
{
    if (fieldType == Et_DbSheetField_Address)
    {
        binary_wo::VarObj districtArray = val.get_s("districts");
        if (districtArray.arrayLength_s() > 0)
            return false;

        if (!val.has("detail"))
            return true;

        PCWSTR detail = val.field_str("detail");
        return !detail || !(*detail);
    }
	else if (fieldType == Et_DbSheetField_Cascade || fieldType == Et_DbSheetField_Department)
    {
        binary_wo::VarObj districtArray = val.get_s("districts");
        return  districtArray.arrayLength_s() == 0;
    }
    WebStr str = val.value_str();
    return !str || !(*str);
}

HRESULT ETDbSheetTaskClassBase::checkFormRecord(IDBSheetView* pView, VarObj param)
{
	if (pView->GetType() != et_DBSheetView_Form)
		return S_OK;

	VarObj objRecoresValues = param.get_s("recordsValues");
	int32 recoresCnt = objRecoresValues.arrayLength_s();
	if(recoresCnt < 1)
		return E_INVALIDARG;

	ks_stdptr<IDBSheetView_Form> spView = pView;
	if (spView->GetMajorVer() != param.field_uint32("formMajorVer"))
		return E_DBSHEET_FORM_VER_CONFLICT;

	const IDBIds* pFields = pView->GetVisibleFields();
    IDbFieldsManager* pFieldsManager = pView->GetFieldsManager();
	std::unordered_map<EtDbId, bool> requiredFld;
    std::unordered_map<EtDbId, ET_DbSheet_FieldType> requiredFieldTypes;
	for (EtDbIdx i = 0, cnt = pFields->Count(); i < cnt; ++i)
	{
		EtDbId fldId = pFields->IdAt(i);
		const IDBFormFieldOptions* pOpt = spView->GetFieldOption(fldId);
        ks_stdptr<IDbField> spField;
        pFieldsManager->GetField(fldId, &spField);
		if (pOpt && pOpt->GetIsRequired())
        {
            requiredFld.insert(std::make_pair(fldId, false));
            requiredFieldTypes.insert(std::make_pair(fldId, spField->GetType()));
        }

	}

	if (requiredFld.empty())
		return S_OK;

	for (UINT32 iRec = 0; iRec < recoresCnt; iRec++)
	{
		VarObj vRecordVals = objRecoresValues.at_s(iRec);
		UINT32 cellCnt = vRecordVals.arrayLength_s();
		if(cellCnt < requiredFld.size())
			return E_DBSHEET_FORM_REQUIRED_FIELD;

		for (UINT32 i = 0; i < cellCnt; ++i) 
		{
			VarObj item = vRecordVals.at(i);
			EtDbId fldId = GetEtDbId(item, "fieldId");
			auto it = requiredFld.find(fldId);
			if (it != requiredFld.end())
			{
                binary_wo::VarObj val = item.get_s("val");
                ET_DbSheet_FieldType fieldType = requiredFieldTypes.at(fldId);
                if (isDbValueEmpty(val, fieldType))
                    return E_DBSHEET_FORM_REQUIRED_FIELD;
                it->second = true;
			}
		}

		for (auto it = requiredFld.begin(); it != requiredFld.end(); ++it)
		{
			if (!it->second)
				return E_DBSHEET_FORM_REQUIRED_FIELD;
			it->second = false;
		}
	}
	return S_OK;
}

HRESULT ETDbSheetTaskClassBase::createRecordCopyWrap(IDBSheetView* pView, IDBSheetRange* pDbRg, EtDbId srcRecId, VarObj param)
{
	KDbSetCellValInvalidInfoCollector collector(pView->GetSheetOp()->GetSheetId());
	std::unique_ptr<DbReversedLinkParam> upDbLinkParam;
	HRESULT hr = createCopyFromRecord(pView, pDbRg, srcRecId, param, upDbLinkParam, collector);
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if (peripheral)
		{
			VarObj resObj = peripheral->resSelf();
			if(!collector.IsEmpty())
			{
				binary_wo::VarObj invalidCellsObj = resObj.add_field_struct("invalidCellsInfo");
				collector.SerialContent(invalidCellsObj);
			}
		}
	}
	if (FAILED(hr))
		return hr;
	if (upDbLinkParam && upDbLinkParam->adFieldEdit)
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if (peripheral)
		{
			VarObj resObj = peripheral->resSelf();
			resObj.add_field_bool("unable2ModifyLinkField", true);
		}
	}
	return hr;
}

namespace
{

HRESULT createCopyFromRecord(IDBSheetView* pView, IDBSheetRange* pDbRg, EtDbId srcRecId, VarObj param,
	std::unique_ptr<DbReversedLinkParam>& upDbLinkParam, KDbSetCellValInvalidInfoCollector& collctor)
{
	// 遍历各field，取值并赋值（直接取token）。跳过某些字段
	HRESULT hr = S_OK;
	const IDBIds* pFields = pView->GetAllFields();

	std::unique_ptr<DbSheet::DbLinkHlp> upDbLinkHlp;
	IDbFieldsManager *spFieldsMgr = pView->GetFieldsManager();
	for (EtDbIdx i = 0, cnt = pFields->Count(); i < cnt; ++i)
	{
		ks_stdptr<IDbField> spField;
		HRESULT hr = spFieldsMgr->GetField(pFields->IdAt(i), &spField);
		if (FAILED(hr))
			return hr;
		if (spField->GetType() == Et_DbSheetField_Link)
		{
			upDbLinkParam.reset(new DbReversedLinkParam(
				DbReversedLinkParam::TryModifyField2Multiple));
			upDbLinkHlp.reset(new DbSheet::DbLinkHlp(pView->GetSheetOp()->GetBook(), *upDbLinkParam));
			break;
		}
	}

	UINT succeededCount = 0;
	for (EtDbIdx i = 0, cnt = pFields->Count(); i < cnt; ++i)
	{
		EtDbId fldId = pFields->IdAt(i);
		for (UINT32 idx = 0, count = pDbRg->GetRecordCnt(); idx < count; idx++)
		{
			EtDbId recId = pDbRg->GetRecordId(idx);
			hr = pView->GetSheetOp()->CopyCell(srcRecId, fldId, recId);
			if(hr == E_DBSHEET_VALUE_NOT_UNIQUE_IN_FIELD)
			{
				IDBSheetOp* pSheetOp = pView->GetSheetOp();
				ks_bstr str;
				HRESULT hr = pSheetOp->GetValueString(srcRecId, fldId, &str);
				collctor.InsertNormalCellItem(recId, fldId, str.c_str(),hr);
				continue;
			}
			if (SUCCEEDED(hr))
				++succeededCount;
		}
	}
	return succeededCount > 0 ? S_OK : E_FAIL;
}

}

HRESULT ETDbSheetTaskClassBase::writeDbRange(IDBSheetView* pView, IDBSheetRange* pDbRg, binary_wo::VarObj rgObj)
{
	AddEtDbId(rgObj, "viewId", pView->GetId());
	if(pDbRg->IsEntireField()) 
	{
		rgObj.add_field_str("recordsMode", __X("allInView"));
	}
	else 
	{
		rgObj.add_field_str("recordsMode", __X("include"));
		binary_wo::VarObj recIds = rgObj.add_field_array("records", binary_wo::typeString);
		for (size_t i = 0, count = pDbRg->GetRecordCnt(); i < count; i++)
		{
			AddEtDbIdItem(recIds, pDbRg->GetRecordId(i));
		}
	}

	if(pDbRg->IsEntireRecord())
	{
		rgObj.add_field_str("fieldsMode", __X("allInView"));
	}
	else
	{
		rgObj.add_field_str("fieldsMode", __X("include"));
		binary_wo::VarObj fields = rgObj.add_field_array("fields", binary_wo::typeString);
		for (size_t i = 0, count = pDbRg->GetFieldCnt(); i < count; i++)
		{
			AddEtDbIdItem(fields, pDbRg->GetFieldId(i));
		}
	}
	return S_OK;
}

HRESULT ETDbSheetTaskClassBase::updateAll()
{
	wo::util::CallTimeStat cts("UpdateAllViews");
	INT sheetCnt = 0;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->GetSheetCount(&sheetCnt);
	HRESULT hr = S_OK;
	for (int i = 0; i < sheetCnt; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(i, &spSheet);

		if (spSheet->IsDbDashBoardSheet())
		{
			hr = UpdateChartStatisticData(m_dbViewUpdateFlags, m_commonHelper, spSheet->GetStId());
			if (FAILED(hr))
				return hr;
			continue;
		}
		ks_stdptr<IDBSheetViews> spDbSheetViews;
		hr = DbSheet::GetDBSheetViews(spSheet.get(), &spDbSheetViews);
		if (FAILED(hr))
			continue;

		hr = spDbSheetViews->Update(m_dbViewUpdateFlags);
		if (FAILED(hr))
		{
			ASSERT(!__X("权限相关的代码有问题!!!!!!!!!"));
			WOLOG_ERROR << "DbSheetViews update failed!!!";
			return E_DBSHEET_AD_FAIL;
		}
	}
	if (m_pDbCtx->IsPrepareCurUser())
		m_pDbCtx->SetPrepareCurUserCalled();
	return S_OK;
}

HRESULT ETDbSheetTaskClassBase::getUserGroup(EtDbId userGroupId, IDBUserGroup** ppGroup)
{
	ks_stdptr<IUnknown> spUnknown;
	m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edDBUserGroups, &spUnknown);
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	return spDBUserGroups->GetGroupById(userGroupId, ppGroup);
}

HRESULT ETDbSheetTaskClassBase::setGetViewCurModifyTar(VarObj param)
{
	return m_commonHelper.setGetViewCurModifyTar(param);
}

HRESULT ETDbSheetTaskClassBase::AddSelectItems(const binary_wo::VarObj& param, IDBSheetOp* pSheetOp)
{
	IDbFieldsManager *pFieldsMgr = pSheetOp->GetFieldsManager();
	VarObj fieldList = param.get("fieldValues");
	int itemsCnt = fieldList.arrayLength_s();
	for (int i = 0; i < itemsCnt; ++i)
	{
		VarObj vItem = fieldList.at_s(i);
		EtDbId fldId = GetEtDbId(vItem, "fieldId");

		ks_stdptr<IDbField> spField;
		HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			continue;
		ET_DbSheet_FieldType type = spField->GetType();
		if (type != Et_DbSheetField_SingleSelect && type != Et_DbSheetField_MultipleSelect)
			continue;
		
		ks_stdptr<IDbField_Select> spFieldSelect = spField;
		if (!spFieldSelect->GetAllowAddItemWhenInputting())
			continue;
		VarObj vListItems = vItem.get_s("listItems");
		int  addItemsCnt = vListItems.arrayLength_s();
		for (int j = 0; j < addItemsCnt; ++j)
		{
			VarObj item = vListItems.at_s(j);
			WebStr val = item.field_str("value");
			if (spFieldSelect->HasItem(val))
				continue;
			if (item.has("color"))
				hr = spFieldSelect->AppendItem(val, item.field_uint32("color"));
			else
				hr = spFieldSelect->AppendItemWithAutoColor(val);
			if (FAILED(hr))
				return hr;
		}
	}
	return S_OK;
}

// ================== TaskExecKSheetShareSheet =======================
TaskExecKSheetShareSheet::TaskExecKSheetShareSheet(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

// ksheet分享工作表时数据表的权限没准备好，需要在db表内进行标脏和视图更新
// 后续可能需要针对et事务对db表的权限影响重新设计一下（引用自：重新计算 book.recalculate）
HRESULT TaskExecKSheetShareSheet::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	if (!param.has("sheetStId") || !param.has("sharedId"))
	{
		WOLOG_ERROR << "[DoExecShareSheet] param error!";
		return E_FAIL;
	}

	UINT sheetStId = param.field_uint32("sheetStId");
	PCWSTR sharedId = param.field_str("sharedId");
	
	return ShareSheetHelper::ShareSheet(m_wwb, pCtx, sharedId, sheetStId);
}

PCWSTR TaskExecKSheetShareSheet::GetTag()
{
	return __X("sheet.shareSheet");
}

HRESULT TaskExecKSheetShareSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isConnSharedLink())
		return E_OPERATION_NOT_SUPPORTED_ON_SHARED_SHEET;
	return S_OK;
}

// ==================== TaskExecKSheetCancelShareSheet ==================
TaskExecKSheetCancelShareSheet::TaskExecKSheetCancelShareSheet(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT TaskExecKSheetCancelShareSheet::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	return ShareSheetHelper::CancelShareSheet(m_wwb, sheetStId);
}

PCWSTR TaskExecKSheetCancelShareSheet::GetTag()
{
	return __X("sheet.cancelShareSheet");
}

HRESULT TaskExecKSheetCancelShareSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isConnSharedLink())
		return E_OPERATION_NOT_SUPPORTED_ON_SHARED_SHEET;
	return S_OK;
}

// ================== TaskExecDbPrepareCurUser ==================
TaskExecDbPrepareCurUser::TaskExecDbPrepareCurUser(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbPrepareCurUser::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	m_pDbCtx->SetPrepareCurUser();
	m_pDbCtx->ResetReserializeWhenAuthorityDirty();
	return S_OK;
}

PCWSTR TaskExecDbPrepareCurUser::GetTag()
{
	return __X("dbsheet.prepareCurUser");
}
// ================== TaskExecDbEmpty ==================
TaskExecDbEmpty::TaskExecDbEmpty(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
	m_dbViewUpdateFlags = static_cast<DbSheetViewUpdateFlag>(
		static_cast<uint32_t>(DBVUF::Default)
		| static_cast<uint32_t>(DBVUF::UpdateAll)
		| static_cast<uint32_t>(DBVUF::CanBreak)
	);
}

void TaskExecDbEmpty::OnDateChange()
{
	IKWorksheets* pWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	int sheetCount = pWorksheets->GetSheetCount();
	for (int i = 0; i < sheetCount; ++i)
	{
		ISheet* pSheet = pWorksheets->GetSheetItem(i)->GetSheet();
		if (pSheet->IsDbDashBoardSheet())
		{
			ks_stdptr<IDBChartStatisticMgr> spChartStatisticMgr;
			DbSheet::GetDBChartStatisticMgr(pSheet, &spChartStatisticMgr);
			if (spChartStatisticMgr)
				spChartStatisticMgr->MarkChartsDirtyOnDateChange();
		}
	}
}

void TaskExecDbEmpty::OnStatSheetChange()
{
	IKWorksheets* pWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	int sheetCount = pWorksheets->GetSheetCount();
	for (int i = 0; i < sheetCount; ++i)
	{
		ISheet* pSheet = pWorksheets->GetSheetItem(i)->GetSheet();
		if (pSheet->IsDbSheet())
		{
			ks_stdptr<IDBStatisticSheetData> spDBStatisticSheetData;
			pSheet->GetExtDataItem(edStatisticSheetData, (IUnknown**)&spDBStatisticSheetData);
			if(!spDBStatisticSheetData)
				continue;
			
			if(spDBStatisticSheetData->NeedStatSheetAdjust())
			{
				spDBStatisticSheetData->SyncResultFromSourceSheet();

				ks_stdptr<IDBSheetOp> spDbSheetOp;
				VS(DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp));
				const auto pFieldIDs {spDbSheetOp->GetAllFields()};
				const auto fieldCount {pFieldIDs->Count()};
				const auto pFieldManager {spDbSheetOp->GetFieldsManager()};
				for (UINT i {0}; i < fieldCount; ++i)
				{
					ks_stdptr<IDbField> spField {};
					const auto fieldID {pFieldIDs->IdAt(i)};
					pFieldManager->GetField(fieldID, &spField);
					if (spField->GetType() == Et_DbSheetField_Email)
					{
						const auto pRecordIDs {spDbSheetOp->GetAllRecords()};
						const auto recordCount {pRecordIDs->Count()};
						for (UINT j {0}; j < recordCount; ++j)
						{
							ks_bstr cellString {};
							const auto recordID {pRecordIDs->IdAt(j)};
							spDbSheetOp->GetDisplayString(recordID, fieldID, &cellString);
							spDbSheetOp->SetHyperlinkAddress(recordID, fieldID, cellString.c_str());
						}
					}
				}
			}
		}
	}
}

HRESULT TaskExecDbEmpty::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	if (param.has("dateChanged") && param.field_bool("dateChanged"))
	{
		OnDateChange();
	}
	if (param.has("afterDbGroupby") && param.field_bool("afterDbGroupby"))
	{
		OnStatSheetChange();
	}
	m_pDbCtx->ResetReserializeWhenAuthorityDirty();
	return S_OK;
}

PCWSTR TaskExecDbEmpty::GetTag()
{
	return __X("dbsheet.empty");
}

// ================== TaskExecDbConvertCurUserFilterToAnyUserFilter ==================
TaskExecDbConvertCurUserFilterToAnyUserFilter::TaskExecDbConvertCurUserFilterToAnyUserFilter(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbConvertCurUserFilterToAnyUserFilter::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
    IKWorksheets* pWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
    int sheetCount = pWorksheets->GetSheetCount();
    for(int sheetIdx = 0; sheetIdx < sheetCount; sheetIdx++)
    {
        ks_castptr<etoldapi::_Worksheet> spWorksheet = pWorksheets->GetSheetItem(sheetIdx);
        ISheet* pSheet = spWorksheet->GetSheet();
        if (!pSheet->IsDbSheet())
            continue;

        ks_stdptr<IDBSheetViews> spDbSheetViews;
        DbSheet::GetDBSheetViews(pSheet, &spDbSheetViews);
        HRESULT hr = spDbSheetViews->ConvertCurUserFilterToAnyUserFilter();
        if (FAILED(hr))
            return hr;
    }
    return S_OK;
}

HRESULT TaskExecDbConvertCurUserFilterToAnyUserFilter::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
    //　仅审核时使用
    if (m_pDbCtx->IsCensoring())
        return S_OK;
    return E_FAIL;
}

PCWSTR TaskExecDbConvertCurUserFilterToAnyUserFilter::GetTag()
{
    return __X("dbsheet.convertCurUserFilterToAnyUserFilter");
}

TaskExecDbtBookRecalculateLegacy::TaskExecDbtBookRecalculateLegacy(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

// 实际上除了recalculate, 还可能有其他et事务导致需要在db表内进行标脏和视图更新. 整体需要重新设计
HRESULT TaskExecDbtBookRecalculateLegacy::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	if (pCtx->getProtectionCtx()->isConnSharedLink())
		return S_FALSE;
	auto start = std::chrono::high_resolution_clock::now();
	HRESULT hr = TaskExecHelperBookRecalculate(m_wwb)(cmd, pCtx);
	auto end = std::chrono::high_resolution_clock::now();
	std::chrono::duration<double> elapsed = end - start;
	double count = elapsed.count();
	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral && SUCCEEDED(hr))
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_double("recalculateTime", count * 1000);
	}
	return hr;
}

PCWSTR TaskExecDbtBookRecalculateLegacy::GetTag()
{
	return __X("book.recalculate");
}

bool TaskExecDbtBookRecalculateLegacy::SerialCommand(KwCommand* cmd, ISerialAcceptor* acpt)
{
	return TaskExecHelperBookRecalculate(m_wwb).SerialCommand(cmd, acpt);
}

// ========================== TaskExecImportWorkbookLegacy ==================================
TaskExecImportWorkbookLegacy::TaskExecImportWorkbookLegacy(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecImportWorkbookLegacy::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	TaskExecHelperImportWorkbook helper(m_wwb, m_spProtectionJudgement.get());
	return helper(this, cmd, pCtx);
}

PCWSTR TaskExecImportWorkbookLegacy::GetTag()
{
	return __X("book.importWorkbook");
}

// ========================== TaskExecSetCustomStorage ==================================
TaskExecSetCustomStorage::TaskExecSetCustomStorage(KEtWorkbook* wwb)
        : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecSetCustomStorage::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    if (!spCustomStorMgr)
        return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");
	if (param.has("book"))
	{
		HRESULT hr = HandleBookInfo(param, spCustomStorMgr);
		if (FAILED(hr))
			return hr;
	}
    if (param.has("sheets"))
	{
		HRESULT hr = HandleSheetInfo(param, spCustomStorMgr);
		if (FAILED(hr))
			return hr;
	}

	return S_OK;
}

PCWSTR TaskExecSetCustomStorage::GetTag()
{
	return __X("book.setCustomStorage");
}

HRESULT TaskExecSetCustomStorage::HandleBookInfo(const binary_wo::VarObj& param, ks_stdptr<ICustomStorageManager>& spCustomStorMgr)
{
	binary_wo::VarObj bookObj = param.get("book");
    for (EtDbIdx i = 0 ; i < bookObj.arrayLength(); i++)
    {
        binary_wo::VarObj item = bookObj.at(i);
        if (!item.has("key") || !item.has("value") || !item.has("group"))
			return E_INVALIDARG;
        HRESULT hr = spCustomStorMgr->UpdateStorage(-1, item.field_str("key"), item.field_str("group"), 
                                    				item.field_str("value"), FALSE);
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

HRESULT TaskExecSetCustomStorage::HandleSheetInfo(const binary_wo::VarObj& param, ks_stdptr<ICustomStorageManager>& spCustomStorMgr)
{
	HRESULT hr = S_OK;
	binary_wo::VarObj sheetsObj = param.get("sheets");
    for (EtDbIdx i = 0 ; i < sheetsObj.arrayLength(); i++)
    {
        binary_wo::VarObj item = sheetsObj.at(i);
		if (!item.has("sheetId"))
			return E_INVALIDARG;
		UINT stId = item.field_uint32("sheetId");
		ks_stdptr<ISheet> spSheet;
		hr = m_commonHelper.GetSheet(stId, &spSheet);
		if (FAILED(hr))
			return hr;
		if (spSheet->IsDbDashBoardSheet())
			hr = m_spProtectionJudgement->CheckCanManageDBChart(stId);
		else
			hr = m_spProtectionJudgement->CheckSheetCanEdit(stId);
		if (FAILED(hr))
			return hr;

		if (!item.has("key") || !item.has("value") || !item.has("group") || !item.has("isFallback2Book"))
			return E_INVALIDARG;
        hr = spCustomStorMgr->UpdateStorage(stId, item.field_str("key"), item.field_str("group"), 
                                    		item.field_str("value"), item.field_bool("isFallback2Book"));
		if (FAILED(hr))
            return hr;
    }
	return S_OK;
}

// ========================== TaskExecDbSheetsAddLegacy ==================================
TaskExecDbSheetsAddLegacy::TaskExecDbSheetsAddLegacy(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSheetsAddLegacy::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	ks_stdptr<etoldapi::Worksheets> spSheets;
	_Workbook* pWb = m_wwb->GetCoreWorkbook();
	pWb->get_Worksheets(&spSheets);
	if (!spSheets)
		return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");
	KComVariant vType;
	SHEETTYPE sheetType = stGrid;
	HRESULT hr = SheetOperatorHelper::GetSheetType(param, vType, sheetType);
	if (FAILED(hr))
		return hr;
	ET_DbSheet_Sync_Type syncType = DbSheet_St_None;
	if (param.has("syncType"))
	{
		hr = m_pEncodeDecoder->DecodeDbSheetSyncType(param.field_str("syncType"), &syncType);
		if (FAILED(hr))
			return hr;
	}
	switch (sheetType)
	{
		case stGrid_DB:
		{
			if (ctx->isExecDirect())
				break;
			if (syncType == DbSheet_St_DB || syncType == DbSheet_St_Cross_DB)
			{
				int existSyncStCnt = 0;
				for (int i = 0, tarSheetsCnt = spSheets->GetSheetCount(); i < tarSheetsCnt; ++i)
				{
					ISheet* pTarShet = spSheets->GetSheetItem(i)->GetSheet();
					ks_stdptr<IDBSheetOp> spDBSheetOp;
					DbSheet::GetDBSheetOp(pTarShet, &spDBSheetOp);
					if (!spDBSheetOp)
						continue;
					if (spDBSheetOp->IsSyncSheet())
						++existSyncStCnt;
				}
				if (existSyncStCnt >= param.field_int32("maxSyncSheetLimit"))
					return E_DBSHEET_SYNCSHEET_EXCEEDED_LIMITS;
			}
			break;
		}
		case stDashBoard:
		{
			if (ctx->isExecDirect())
				break;
			if (spSheets->GetSheetCount(stDashBoard, TRUE) >= DbSheet::GetDBDashBoardSheetLimit())
				return E_DBSHEET_DASHBOARD_COUNT_LIMIT;
			break;
		}
		case stFlexPaper:
		{
			if (ctx->isExecDirect())
				break;
			if (spSheets->GetSheetCount(stFlexPaper, TRUE) >= DbSheet::GetDBFpsheetLimit())
				return E_FAIL;
			break;
		}
		default:
			break;
	}
	KComVariant vBefore;
	if (param.has("before"))
	{
		binary_wo::VarObj before = param.get_s("before");
		int sheetIdx = GetSheetIdx(before);
		vBefore = INVALIDIDX == sheetIdx ? 1 : sheetIdx + 1;
	}
	KComVariant vAfter;
	if (param.has("after"))
	{
		binary_wo::VarObj after = param.get_s("after");
		int sheetIdx = GetSheetIdx(after);
		if (INVALIDIDX == sheetIdx)
			vAfter = getLastSheetIdx() + 1;
		else
			vAfter = sheetIdx + 1;
	}
	if (param.has("end"))
	{
		if (param.field_bool("end"))
			vAfter = getLastSheetIdx() + 1;
	}
	KComVariant vCount;
	if (param.has("count"))
		vCount = param.field_int32("count");
	IBook* pBook = pWb->GetBook();
	// 供命令回放使用. 利用了id单调递增及新增sheet操作无法undo
	if (param.has("maxSheetStId"))
		pBook->SetMaxSheetStId(param.field_uint32("maxSheetStId") - 1);
	_Worksheet* pWorkSheet = nullptr;
	ISheet* pSheet = nullptr;
	{
		app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
		ks_stdptr<IKCoreObject> spObj;
		hr = spSheets->Add(vBefore, vAfter, vCount, vType, &spObj, sheetType);
		if (FAILED(hr))
			return hr;
		if (!spObj)
			return E_FAIL;
		pWorkSheet = static_cast<_Worksheet*>(spObj.get());
		pSheet = pWorkSheet->GetSheet();
		if (param.has("name"))
		{
			PCWSTR name = param.field_str("name");
			ks_wstring revisedName(name);
			if (param.has("sheetNameSuffix"))
			{
				revisedName.append(param.field_str("sheetNameSuffix"));
				name = revisedName.c_str();
			}
			ks_bstr strName;
			hr = GetValidSheetName(spSheets, pWorkSheet, name, &strName);
			if (FAILED(hr))
			{
				pWorkSheet->DeleteDirectly();
				return hr;
			}
			hr = pWorkSheet->put_Name(strName);
			if (FAILED(hr))
			{
				pWorkSheet->DeleteDirectly();
				return hr;
			}
		}
	}
	// 有宏就把宏关掉
	pBook->GetWoStake()->getSetting()->markMacrosLost();
	EtTaskPeripheral* peripheral = GetPeripheral();
	switch (sheetType)
	{
		case stGrid_DB:
		{
			if (syncType == DbSheet_St_DB || syncType == DbSheet_St_Cross_DB)
			{
				Database::SheetInitConfig config = Database::Utils::ParseDbSheetInitConfig(param);
				DbSheet::SyncSheetInitParam syncInitParam;
				syncInitParam.syncType = syncType;
				if (syncType == DbSheet_St_Cross_DB)
				{
					config.nEmptyRows = 0;
					VAR_OBJ_EXPECT_STRING(param, "sourceFileId")
					VAR_OBJ_EXPECT_STRING(param, "sourceFileName")
					VAR_OBJ_EXPECT_INTEGRAL(param, "sourceSheetId")
					VAR_OBJ_EXPECT_STRING(param, "sourceSheetName")
					syncInitParam.info.sourceFileId = param.field_str("sourceFileId");
					syncInitParam.info.userId = ctx->getUser()->userID();
					syncInitParam.info.sourceFileName = param.field_str("sourceFileName");
					syncInitParam.info.sourceSheetId = param.field_uint32("sourceSheetId");
					syncInitParam.info.sourceSheetName = param.field_str("sourceSheetName");
				}
				ks_stdptr<IDBSheetView> spDbSheetView;
				hr = DbSheet::initDbSyncSheet(pWorkSheet, m_spProtectionJudgement.get(), config, &syncInitParam, &spDbSheetView);
				if (FAILED(hr))
					break;
				std::unordered_map<EtDbId, EtDbId> fieldMap;
				bool bNewLookupConvert {};
				if (param.has("new_lookup_converter"))
					bNewLookupConvert = param.field_bool("new_lookup_converter");
				hr = DbSheet::setDbSyncSheet(param, pWorkSheet, m_spProtectionJudgement.get(), spDbSheetView, &fieldMap, bNewLookupConvert);
				if (FAILED(hr))
					break;
				if (peripheral)
				{
					VarObj resObj = peripheral->resSelf();
					resObj.add_field_str("cmdName", GetTag());
					VarObj sheetInfo = resObj.add_field_struct("sheetInfo");
					sheetInfo.add_field_uint32("srcSheetId", param.field_uint32("sourceSheetId"));
					sheetInfo.add_field_uint32("refSheetId", pSheet->GetStId());
					VarObj fieldMappingList = sheetInfo.add_field_struct("fieldMappingList");
					VarObj srcFieldId = fieldMappingList.add_field_array("srcFieldId", typeString);
					VarObj tarFieldId = fieldMappingList.add_field_array("tarFieldId", typeString);
					for (auto it : fieldMap)
					{
						AddEtDbIdItem(srcFieldId, it.first);
						AddEtDbIdItem(tarFieldId, it.second);
					}
				}
				break;
			}
			if(param.has("dbSheetSubType"))
			{
				//Todo：子类型后续这里再细化
				hr = initStatDbSheet(param, m_wwb, pWorkSheet, m_spProtectionJudgement.get());
			}
			else 
			{
				hr = DbSheet::initDbSheet(param, pWorkSheet, m_spProtectionJudgement.get()); // 独立dbsheet组件
			}
			
			break;
		}
		case stFlexPaper:
		{
			if (param.has("contentId"))
			{
				PCWSTR contentId = param.field_str("contentId");
				ks_stdptr<IUnknown> spUnknown;
				pSheet->GetExtDataItem(edSheetFpData, &spUnknown);
				ks_stdptr<IFPSheetData> spFPSheetData = spUnknown;
				if (spFPSheetData)
					spFPSheetData->SetContentId(contentId);
			}
			break;
		}
		case stDashBoard:
			hr = DashBoard::initDashBoardSheet(static_cast<IKEtWindow*>(pWb->GetActiveWindow()), pWorkSheet, stDashBoard);
			break;
		default:
			return E_INVALIDARG;
	}
	if (FAILED(hr))
	{
		pWorkSheet->DeleteDirectly();
		return hr;
	}

	// 在侧边栏文件夹tree中移动对应sheet(添加sheet至tree root已在adjuster中处理)
	wo::DbSidebarFolderTreeAddHelper sidebarFolderTreeAddHelper(m_wwb);
	sidebarFolderTreeAddHelper.MoveSheet(param, pSheet->GetStId());

	IDX sheetIdx = INVALIDIDX;
	pSheet->GetIndex(&sheetIdx);
	IWorksheetObj* obj = pWb->GetWoObject()->getSheetItem(sheetIdx);
	param.add_field_web_id("objId", obj->objId());
	if (!param.has("maxSheetStId"))
		param.add_field_uint32("maxSheetStId", pSheet->GetStId());
	if (param.has("name"))
		param.remove_field("name");
	ks_bstr sheetName;
	VS(pWorkSheet->get_Name(&sheetName));
	param.add_field_str("name", sheetName.c_str());
	ctx->setIsRealTransform(true);
	if (ctx->isInRevisionMode())
		m_wwb->setLastTaskSheet(sheetIdx);

	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		UINT sheetId = pSheet->GetStId();
		resObj.add_field_int32("sheetId", sheetId);
	}

	return S_OK;
}

PCWSTR TaskExecDbSheetsAddLegacy::GetTag()
{
	return __X("sheets.add");
}

HRESULT TaskExecDbSheetsAddLegacy::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}
// ========================== TaskExecDbSheetsMoveLegacy ==================================
TaskExecDbSheetsMoveLegacy::TaskExecDbSheetsMoveLegacy(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbSheetsMoveLegacy::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	ks_stdptr<etoldapi::Worksheets> spSheets;
	m_wwb->GetCoreWorkbook()->get_Worksheets(&spSheets);
	if (!spSheets)
		return E_FAIL;
	
	binary_wo::VarObj param = cmd->cast().get("param");

	if (!param.has("fromIdx"))
		return E_FAIL;
	int fromIdx = param.field_int32("fromIdx");
	if (INVALIDIDX == fromIdx)
		return E_FAIL;
	else
		fromIdx += 1;

	ks_stdptr<IKCoreObject> spCoreObj;
	spSheets->get_Item(KComVariant(fromIdx, VT_I4), &spCoreObj);
	if (!spCoreObj)
		return E_FAIL;
	ks_stdptr<etoldapi::_Worksheet> spSheet = spCoreObj;

	HRESULT hr =  m_spProtectionJudgement->CheckSheetCanEditProp(spSheet->GetSheet()->GetStId());
	if (FAILED(hr))
		return hr;
	
	VARIANT emptyVar;
	V_VT(&emptyVar) = VT_EMPTY;
	
	if (param.has("before"))
	{
		binary_wo::VarObj before = param.get_s("before");
		int sheetIdx = GetSheetIdx(before);
		if (INVALIDIDX == sheetIdx)
			return E_FAIL;
		else
			sheetIdx += 1;
		ks_stdptr<IKCoreObject> vBefore;
		spSheets->get_Item(KComVariant(sheetIdx, VT_I4), &vBefore);
		if (!vBefore)
			return E_FAIL;
		KComVariant worksheetVar(static_cast<IKCoreObject*>(vBefore));
		
		HRESULT hr = spSheet->Move(worksheetVar, emptyVar);

		if (SUCCEEDED(hr))
		{
			long sheetIdx;
			spSheet->get_Index(0, &sheetIdx); // based on 1
			if (ctx->isInRevisionMode())
				m_wwb->setLastTaskSheet(sheetIdx - 1);
		}

		return hr;
	}
	if (param.has("after"))
	{
		binary_wo::VarObj after = param.get_s("after");
		int sheetIdx = GetSheetIdx(after);
		if (INVALIDIDX == sheetIdx)
			return E_FAIL;
		else
			sheetIdx += 1;
		ks_stdptr<IKCoreObject> vAfter;
		spSheets->get_Item(KComVariant(sheetIdx, VT_I4), &vAfter);
		if (!vAfter)
			return E_FAIL;
		KComVariant worksheetVar(static_cast<IKCoreObject*>(vAfter));
		
		HRESULT hr = spSheet->Move(emptyVar, worksheetVar);

		if (SUCCEEDED(hr))
		{
			long sheetIdx;
			spSheet->get_Index(0, &sheetIdx); // based on 1
			if (ctx->isInRevisionMode())
				m_wwb->setLastTaskSheet(sheetIdx - 1);
		}

		return hr;
	}

	return E_FAIL;
}

HRESULT TaskExecDbSheetsMoveLegacy::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

PCWSTR TaskExecDbSheetsMoveLegacy::GetTag()
{
	return __X("sheets.move");
}

// ========================== TaskExecDbSheetsCopyLegacy ==================================
TaskExecDbSheetsCopyLegacy::TaskExecDbSheetsCopyLegacy(KEtWorkbook* wwb) 
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbSheetsCopyLegacy::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	EnableProtectCompileFmla enableClFlmGuard;
	ks_stdptr<etoldapi::Worksheets> spSheets;
	m_wwb->GetCoreWorkbook()->get_Worksheets(&spSheets);
	if (!spSheets)
		return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");

	if (!param.has("fromIdx"))
		return E_FAIL;
	int copySheetIdx = param.field_int32("fromIdx");

	if (INVALIDIDX == copySheetIdx)
		return E_FAIL;
	else
		copySheetIdx += 1;

	ks_stdptr<IKCoreObject> spCoreObj;
	spSheets->get_Item(KComVariant(copySheetIdx, VT_I4), &spCoreObj);
	if (!spCoreObj)
		return E_FAIL;
	ks_stdptr<etoldapi::_Worksheet> spSheet = spCoreObj;
	// copyContent用于判断是否要复制表的数据
	bool copyContent = true;
	HRESULT hr = S_OK;
	if (param.has("copyContent"))
		copyContent = param.field_bool("copyContent");

	VARIANT emptyVar;
	V_VT(&emptyVar) = VT_EMPTY;
	ks_stdptr<IKCoreObject> spNewSheetObj;
	
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	KComVariant vAfter(static_cast<IKCoreObject*>(spCoreObj));
	hr = spSheet->CopyDbSheet(emptyVar, vAfter, copyContent, &spNewSheetObj);
	if (FAILED(hr))
		return hr;
	ks_stdptr<etoldapi::_Worksheet> spNewSheet = spNewSheetObj;
	ISheet *pSheet = spNewSheet->GetSheet();
	{{
		DbSheet::DisableDbSheetProtectScope disPtScope(m_spProtectionJudgement, pSheet->GetStId());
		bool fallBackLookUp {};
		if (param.has("new_lookup_converter"))
			fallBackLookUp = param.field_bool("new_lookup_converter");
		CopyDBSheetHelper copyHelper(spSheet, spNewSheet, nullptr, fallBackLookUp);
		DbSheetCopyParam copyParam;
		copyParam.copyContent = copyContent;
		copyParam.needResetAutoValue = !copyContent;
		copyParam.resetCreationInfoByCurUser = false;
		copyParam.clearType[Et_DbSheetField_Note] = true;
		if (param.has("linkPrefix"))
			copyParam.biLinkPrefix = param.field_str("linkPrefix");
		if (!copyContent)
			copyParam.recordCnt = param.field_int32("recordCnt");
		hr = copyHelper.Init(copyParam);
		if (FAILED(hr))
			return hr;
		hr = copyHelper.ExecCopy();
	}}

	if (SUCCEEDED(hr))
	{
		// 在侧边栏文件夹tree中移动对应sheet(添加sheet至tree root已在adjuster中处理)
		wo::DbSidebarFolderTreeCopyHelper sidebarFolderTreeCopyHelper(m_wwb);
		sidebarFolderTreeCopyHelper.MoveSheet(pSheet->GetStId(), spSheet->GetSheet()->GetStId());

		long newSheetIdx;
		spNewSheet->get_Index(0, &newSheetIdx); // based on 1

		IWorkbookObj* wbObj = m_wwb->GetCoreWorkbook()->GetWoObject();
		IWorksheetObj* obj = wbObj->getSheetItem(newSheetIdx-1);
		param.add_field_web_id("objId", obj->objId());
		pCtx->setIsRealTransform(true);

		EtTaskPeripheral* peripheral = GetPeripheral();
		if (!peripheral)
			return hr;

		binary_wo::VarObj resObj = peripheral->resSelf();
		UINT newSheetId = 0;
		ISheet *pSheet = spNewSheet->GetSheet();
		IDX iSheet = alg::STREF_INV_SHEET;
		pSheet->GetIndex(&iSheet);
		pSheet->LeakBook()->RTSheetToSTSheet(iSheet, &newSheetId);
		resObj.add_field_int32("newSheetId", newSheetId);
	}
	return hr;
}

HRESULT TaskExecDbSheetsCopyLegacy::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR TaskExecDbSheetsCopyLegacy::GetTag()
{
	return __X("sheets.copy");
}

// ================== TaskExecCopyAppSheet ==================
TaskExecCopyAppSheet::TaskExecCopyAppSheet(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecCopyAppSheet::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	EnableProtectCompileFmla enableClFlmGuard;
	if(!m_wwb->GetBMP()->bKsheet)
		return E_FAIL;
	
	ks_stdptr<etoldapi::Worksheets> spSheets;
	m_wwb->GetCoreWorkbook()->get_Worksheets(&spSheets);
	if (!spSheets)
		return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");
	if (!param.has("fromIdx"))
		return E_FAIL;
	
	int copySheetIdx = param.field_int32("fromIdx");
	if (INVALIDIDX == copySheetIdx)
		return E_FAIL;
	else
		copySheetIdx += 1;

	ks_stdptr<IKCoreObject> spCoreObj;
	spSheets->get_Item(KComVariant(copySheetIdx, VT_I4), &spCoreObj);
	if (!spCoreObj)
		return E_FAIL;

	ks_stdptr<etoldapi::_Worksheet> spSheet = spCoreObj;
	if(!spSheet->GetSheet()->IsAppSheet())
		return E_FAIL;

	VARIANT emptyVar;
	V_VT(&emptyVar) = VT_EMPTY;
	KComVariant vAfter(static_cast<IKCoreObject*>(spCoreObj));

	ks_stdptr<IKCoreObject> spNewSheetObj;
	HRESULT hr = spSheet->Copy(emptyVar, vAfter, &spNewSheetObj);
	if(!spNewSheetObj)
		return E_FAIL;

	ks_stdptr<etoldapi::_Worksheet> spNewSheet = spNewSheetObj;
	ASSERT(spNewSheet->GetSheet()->IsAppSheet());

	//拷贝应用sheet
	CopyAppSheetForLocal appSheetHelper;
	appSheetHelper.Init(spSheet, spNewSheet, &param);
	hr = appSheetHelper.ExecCopy();
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[TaskExecCopyAppSheet] appSheetHelper.ExecCopy() Fail!";
		return hr;
	}
	std::vector<etoldapi::_Worksheet*> appSheets;
	appSheets.push_back(spNewSheet);
	DbSheet::SetSharedIdParam(appSheets, param);
	
	long newSheetIdx;
	spNewSheet->get_Index(0, &newSheetIdx); // based on 1

	IWorkbookObj* wbObj = m_wwb->GetCoreWorkbook()->GetWoObject();
	IWorksheetObj* obj = wbObj->getSheetItem(newSheetIdx-1);
	param.add_field_web_id("objId", obj->objId());
	ctx->setIsRealTransform(true);

	//返回结果
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (!peripheral)
		return hr;

	UINT newSheetId = 0;
	ISheet *pSheet = spNewSheet->GetSheet();
	IDX iSheet = alg::STREF_INV_SHEET;
	pSheet->GetIndex(&iSheet);
	pSheet->LeakBook()->RTSheetToSTSheet(iSheet, &newSheetId);

	binary_wo::VarObj resObj = peripheral->resSelf();
	resObj.add_field_int32("newSheetId", newSheetId);
	appSheetHelper.SerialAppSharedInfo(&resObj); 
	return hr;
}

HRESULT TaskExecCopyAppSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

PCWSTR TaskExecCopyAppSheet::GetTag()
{
	return __X("dbsheet.copyAppSheet");
}

// ================== TaskExecCopyDashBoardSheet ==================
TaskExecCopyDashBoardSheet::TaskExecCopyDashBoardSheet(KEtWorkbook* wwb)
        :ETDbSheetTaskClassBase(wwb)
{
    m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecCopyDashBoardSheet::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
    EnableProtectCompileFmla enableClFlmGuard;
    ks_stdptr<etoldapi::Worksheets> spWorkSheets;
    m_wwb->GetCoreWorkbook()->get_Worksheets(&spWorkSheets);
    if (!spWorkSheets)
        return E_FAIL;

    if (!ctx->isExecDirect() && spWorkSheets->GetSheetCount(stDashBoard, TRUE) >= DbSheet::GetDBDashBoardSheetLimit())
        return E_DBSHEET_DASHBOARD_COUNT_LIMIT;

    binary_wo::VarObj param = cmd->cast().get("param");
    if (!param.has("fromIdx"))
        return E_FAIL;

    int copySheetIdx = param.field_int32("fromIdx");
    if (INVALIDIDX == copySheetIdx)
        return E_FAIL;
    else
        copySheetIdx += 1;

    ks_stdptr<IKCoreObject> spCoreObj;
    spWorkSheets->get_Item(KComVariant(copySheetIdx, VT_I4), &spCoreObj);
    if (!spCoreObj)
        return E_FAIL;

    ks_stdptr<etoldapi::_Worksheet> spSheet = spCoreObj;
    if(!spSheet->GetSheet()->IsDbDashBoardSheet())
        return E_FAIL;

    HRESULT hr = S_OK;
    VARIANT emptyVar;
    V_VT(&emptyVar) = VT_EMPTY;
    KComVariant vAfter(static_cast<IKCoreObject*>(spCoreObj));
    ks_stdptr<IKCoreObject> spNewSheetObj;
    // sheet复制时会复制webextension，不用自己复制webextension
    // 先暂时添加参数兼容命令回放，后面再去掉
    bool newImpl = param.has("newImpl") && param.field_bool("newImpl");
    if (newImpl)
    {
        hr = spSheet->Copy(emptyVar, vAfter, &spNewSheetObj);
        if (FAILED(hr))
            return hr;
    }
    else
    {
        KComVariant vCount = 1;
        KComVariant vType = xlWorksheet;
        hr = spWorkSheets->Add(emptyVar, vAfter, vCount, vType, &spNewSheetObj, stDashBoard);
        if (FAILED(hr))
            return hr;
    }
    ks_stdptr<etoldapi::_Worksheet> spNewSheet = spNewSheetObj;
	ISheet* pNewSheet = spNewSheet->GetSheet();
    ASSERT(pNewSheet->IsDbDashBoardSheet());
    if (param.has("newName"))
    {
        PCWSTR sheetName = param.field_str("newName");
		ks_bstr strName;
		GetValidSheetName(spWorkSheets, spNewSheet, sheetName, &strName);
		spNewSheet->put_Name(strName);
    }
    if (newImpl)
    {
        CopyDBDashboardHelper dashboardHelper;
        dashboardHelper.Init(spSheet, spNewSheet);
        hr = dashboardHelper.ExecCopy();
    }
    else
    {
        IKWebExtensionMgr* pWebExtensionMgr = m_wwb->GetCoreWorkbook()->GetWebExtensionMgr();
        if (!pWebExtensionMgr)
            return E_FAIL;

        CopyDashBoardSheetHelper copySheetHelper;
        copySheetHelper.Init(spSheet, spNewSheet, pWebExtensionMgr);
        hr = copySheetHelper.ExecCopy();
    }
    if (FAILED(hr))
    {
        WOLOG_ERROR << "[TaskExecCopyDashBoardSheet] copySheetHelper.ExecCopy() Fail!";
        return hr;
    }

	ks_stdptr<ICustomStorageManager> spCustomStorMgr;
    m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edCustomStorageManager, (IUnknown**)&spCustomStorMgr);
    if (!spCustomStorMgr)
        return E_FAIL;
	hr = spCustomStorMgr->CopyTo(spSheet->GetSheet()->GetStId(), pNewSheet->GetStId());
	if (FAILED(hr))
		return hr;

	// 在侧边栏文件夹tree中移动对应dashboard(添加dashboard至tree root已在adjuster中处理)
	wo::DbSidebarFolderTreeCopyHelper sidebarFolderTreeCopyHelper(m_wwb);
	sidebarFolderTreeCopyHelper.MoveSheet(pNewSheet->GetStId(), spSheet->GetSheet()->GetStId());

    long newSheetIdx;
    spNewSheet->get_Index(0, &newSheetIdx); // based on 1
    IWorkbookObj* wbObj = m_wwb->GetCoreWorkbook()->GetWoObject();
    IWorksheetObj* obj = wbObj->getSheetItem(newSheetIdx-1);
    param.add_field_web_id("objId", obj->objId());
    ctx->setIsRealTransform(true);

    //返回结果
    EtTaskPeripheral* peripheral = GetPeripheral();
    if (!peripheral)
        return hr;

    binary_wo::VarObj resObj = peripheral->resSelf();
    UINT newSheetId = pNewSheet->GetStId();
    resObj.add_field_int32("newSheetId", newSheetId);
    return hr;
}

HRESULT TaskExecCopyDashBoardSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
    return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR TaskExecCopyDashBoardSheet::GetTag()
{
    return __X("dbsheet.copyDashBoardSheet");
}

// ========================== TaskExecDbSheetDelLegacy ==================================
TaskExecDbSheetDelLegacy::TaskExecDbSheetDelLegacy(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT TaskExecDbSheetDelLegacy::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	ks_stdptr<IBookProtection> ptrBookProtection = m_wwb->GetCoreWorkbook()->GetProtection();
	if (ptrBookProtection){
		BOOKPROTECTION bookProtection;
		ptrBookProtection->GetProperty(&bookProtection);
		if (bookProtection.bProtectStruct)
			return S_FALSE;
	}

	binary_wo::VarObj param = cmd->cast().get("param");
	IDX iSheet = GetSheetIdx(param);

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (pBook == NULL)
	{
		return S_FALSE;
	}

	if (isCellImgListSheet(iSheet))
		return S_FALSE;

	ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(iSheet);

	bool bDashBoard =  false;
	INT iListSize = 0;
	pBook->GetSheetCount(&iListSize);
	if (iSheet >= 0 && iSheet < iListSize) 
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(iSheet, &spSheet);
		bDashBoard = spSheet && spSheet->IsDbDashBoardSheet();
	}
	if (!bDashBoard && pSheetProtection && pSheetProtection->IsProtected())
	{
		return S_FALSE;
	}

	param.add_field_str("affectedBy", ctx->getUser()->userID());
	ctx->setIsRealTransform(true);

	BOOL bDbSheet = pBook->GetBMP()->bDbSheet;
	int iVisibleCnt = 0;
	for (int i = 0; i < iListSize; ++i)
	{
		if (i == iSheet)
			continue;
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(i, &spSheet);
		if (!spSheet)
			continue;
		// db删除表时, 数据表要保留一个
		if (bDbSheet && !spSheet->IsDbSheet())
			continue;
		BOOL bVisible = FALSE;
		spSheet->GetVisible(&bVisible);
		if (bVisible)
			++iVisibleCnt;
	}
	if (iVisibleCnt < 1)
		return S_FALSE;

	WebID sheetObjId = 0;
	if (const AbsObject *obj = ctx->getSheetMain(iSheet))
	{
		sheetObjId = obj->objId();
		AbsKeepRepertory* keepRepertory = ctx->getDocument()->getKeepRepertory();
		keepRepertory->onCloseMainObject(ctx->getSheetMain(iSheet));
	}
	IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!pWorkSheet)
		return S_FALSE;
	ISheet* pSheet = pWorkSheet->GetSheet();
	UINT sheetId = pSheet->GetStId();
	ks_stdptr<IUnknown> spUnknown;
	pBook->GetExtDataItem(edBookDbSyncReleationManager, &spUnknown);
	ks_stdptr<IDbSyncReleationManager> spDbSyncReleationMgr = spUnknown;
	if (pSheet->IsDbSheet())
	{
		ks_stdptr<IDBSheetOp> spDBSheetOp;
		DbSheet::GetDBSheetOp(pSheet, &spDBSheetOp);
		if (spDBSheetOp && spDBSheetOp->GetSheetSyncType() == DbSheet_St_Cross_DB)
			if (spDbSyncReleationMgr->CountRef(sheetId) != 0)
				return E_FAIL;
	}
	PCWSTR sheetName = nullptr;
	pSheet->GetName(&sheetName);
	ks_stdptr<IBookOp> ptrBookOp;
	pBook->GetOperator(&ptrBookOp);
	app_helper::KBatchUpdateCal buc(ptrBookOp);
	HRESULT hr = pWorkSheet->DeleteDirectly();
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (SUCCEEDED(hr) && peripheral && sheetObjId > 0)
	{
		ctx->onSheetDelete(sheetObjId);

		// 删除sheet
		binary_wo::VarObj resObj = peripheral->resOthers();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_double("objSheet", sheetObjId);
		resObj.add_field_str("affectedBy", ctx->getUser()->userID());
	}
	if (gs_callback && gs_callback->reportVersionTag)
	{
		binary_wo::BinWriter binWriter;
		binWriter.addKey("scene");
		binWriter.addInt32(11);
		binWriter.addKey("tagName");
		binWriter.addString(sheetName);

		binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};
		gs_callback->reportVersionTag(&slice);
		WOLOG_INFO << "reportVersionTag: del_dbsheet";
	}
	return hr;
}

HRESULT TaskExecDbSheetDelLegacy::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return m_spProtectionJudgement->CheckSheetCanRemove();
}

PCWSTR TaskExecDbSheetDelLegacy::GetTag()
{
	return __X("sheet.delSheet");
}
// ========================== TaskExecDbSheetsDelLegacy ==================================
TaskExecDbSheetsDel::TaskExecDbSheetsDel(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT TaskExecDbSheetsDel::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	_Workbook* pWb = m_wwb->GetCoreWorkbook();
	// 保护工作簿禁止操作
	IBookProtection* pBookProtection = pWb->GetProtection();
	if (pBookProtection)
	{
		BOOKPROTECTION bookProtection = {};
		pBookProtection->GetProperty(&bookProtection);
		if (bookProtection.bProtectStruct)
			return E_FAIL;
	}
	IBook* pBook = pWb->GetBook();
	if (!pBook)
		return E_FAIL;
	IKWorksheets* pWorkSheets = pWb->GetWorksheets();
	if (!pWorkSheets)
		return E_FAIL;
	int iSheetCount = pWorkSheets->GetSheetCount();

	binary_wo::VarObj param = cmd->cast().get("param");
	binary_wo::VarObj idsObj = param.get_s("sheets");
	INT32 selSheetCount = idsObj.arrayLength_s();
	if (selSheetCount == 0)
		return E_INVALIDARG;
	// 去重
	std::unordered_set<IDX> allSheetIds;
	for (IDX i = 0; i < selSheetCount; ++i) 
	{
		binary_wo::VarObj sheetObj = idsObj.at(i);
		IDX sheetIdx = sheetObj.field_int32("sheetIdx");
		allSheetIds.insert(sheetIdx);
	}
	int delVisibleSheetNum = 0;
	std::vector<IKWorksheet*> normalWorksheets;
	std::vector<IKWorksheet*> linkSyncWorksheets;
	// 选中 sheet 有效性检查
	for (int idx : allSheetIds)
	{
		if (idx < 0 || idx >= iSheetCount)
			continue; // 索引越界
		ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(idx);
		if (pSheetProtection && pSheetProtection->IsProtected())
			return E_FAIL; // 保护工作表 不允许删除
		if (isCellImgListSheet(idx))
			return E_FAIL; // 不应该是 cellimageSheet
		IKWorksheet* pWorksheet = pWorkSheets->GetSheetItem(idx);
		ISheet* pSheet = pWorksheet->GetSheet();
		if (!pSheet)
			continue;
		if (pSheet->IsDbSheet())
		{
			BOOL bVisible = FALSE;
			pSheet->GetVisible(&bVisible);
			if (bVisible)
				++delVisibleSheetNum;
			ks_stdptr<IDBSheetOp> spDBSheetOp;
			DbSheet::GetDBSheetOp(pSheet, &spDBSheetOp);
			if (spDBSheetOp && spDBSheetOp->GetSheetSyncType() == DbSheet_St_Cross_DB)
			{
				linkSyncWorksheets.emplace_back(pWorksheet);
				continue;
			}
		}
		normalWorksheets.emplace_back(pWorksheet);
	}
	int iVisibleCnt = 0;
	for (int idx = 0; idx < iSheetCount; ++idx)
	{
		ISheet* pSheet = pWorkSheets->GetSheetItem(idx)->GetSheet();
		if (!pSheet->IsDbSheet())
			continue;
		BOOL bVisible = FALSE;
		pSheet->GetVisible(&bVisible);
		if (bVisible)
			++iVisibleCnt;
	}
	// db删除表时, 数据表要保留一个
	if (iVisibleCnt - delVisibleSheetNum < 1)
		return E_FAIL;
	ks_stdptr<IUnknown> spUnknown;
	pBook->GetExtDataItem(edBookDbSyncReleationManager, &spUnknown);
	ks_stdptr<IDbSyncReleationManager> spDbSyncReleationMgr = spUnknown;
	size_t sheetsNum = allSheetIds.size();
	std::vector<WebID> objSheetVec;
	objSheetVec.reserve(sheetsNum);
	std::vector<UINT> failedVec;
	failedVec.reserve(sheetsNum);
	auto deleteSheet = [&](IKWorksheet* pWorksheet) {
		if (pWorksheet->IsDestroyed())
			return;
		ISheet* pSheet = pWorksheet->GetSheet();
		UINT sheetId = pSheet->GetStId();
		if (pSheet->IsDbSheet())
		{
			ks_stdptr<IDBSheetOp> spDBSheetOp;
			DbSheet::GetDBSheetOp(pSheet, &spDBSheetOp);
			if (spDBSheetOp && spDBSheetOp->GetSheetSyncType() == DbSheet_St_Cross_DB)
				if (spDbSyncReleationMgr->CountRef(sheetId) != 0)
					return;
		}
		IDX iSheet = INVALIDIDX;
		pSheet->GetIndex(&iSheet);
		WebID sheetObjId = 0;
		AbsObject* obj = ctx->getSheetMain(iSheet);
		if (obj)
		{
			sheetObjId = obj->objId();
			AbsKeepRepertory* keepRepertory = ctx->getDocument()->getKeepRepertory();
			keepRepertory->onCloseMainObject(obj);
		}
		ks_stdptr<IBookOp> spBookOp;
		pBook->GetOperator(&spBookOp);
		app_helper::KBatchUpdateCal buc(spBookOp);
		HRESULT hr = pWorksheet->DeleteDirectly();
		if (FAILED(hr))
			failedVec.emplace_back(sheetId);
		else if (sheetObjId > 0)
		{
			ctx->onSheetDelete(sheetObjId);
			objSheetVec.emplace_back(sheetObjId);
		}
	};
	for (IKWorksheet* pWorksheet : normalWorksheets)
		deleteSheet(pWorksheet);
	for (IKWorksheet* pWorksheet : linkSyncWorksheets)
		deleteSheet(pWorksheet);

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		// 删除sheet
		binary_wo::VarObj resObj = peripheral->resOthers();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_str("affectedBy", ctx->getUser()->userID());
		if (!objSheetVec.empty())
		{
			VarObj objIdVec = resObj.add_field_array("objSheetVec", binary_wo::typeFloat64);
			for (auto id : objSheetVec)
				objIdVec.add_item_double(static_cast<double>(id));
		}
		if (!failedVec.empty())
		{
			VarObj failedSheets = resObj.add_field_array("failedSheets", binary_wo::typeUint32);
			for (auto id : failedVec)
				failedSheets.add_item_uint32(id);
		}
	}

	return S_OK;
}

HRESULT TaskExecDbSheetsDel::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanRemove();
}

PCWSTR TaskExecDbSheetsDel::GetTag()
{
	return __X("dbsheet.delSheets");
}
// ========================== TaskExecDbSheetSetNameLegacy ==================================
TaskExecDbSheetSetNameLegacy::TaskExecDbSheetSetNameLegacy(KEtWorkbook* wb)
	: ETDbSheetTaskClassBase(wb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbSheetSetNameLegacy::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX iSheet = GetSheetIdx(param);

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	ks_stdptr<_Worksheet> spSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(iSheet);
	if (!spSheet)
		return S_FALSE;

	if (isCellImgListSheet(iSheet))
		return S_FALSE;

	HRESULT hr =  m_spProtectionJudgement->CheckSheetCanEditProp(spSheet->GetSheet()->GetStId());
	if (FAILED(hr))
		return hr;
	ks_bstr strName(param.field_str("name"));
	hr = spSheet->IsValidSheetName(strName.c_str());
	if (hr != S_OK)
	{
		if (hr == E_INVALID_CHAR)
			return E_DBSHEET_NAME_CHAR_INVALID;
		if (hr == S_FALSE)
			return E_NAME_CONFLICT;
		return hr;
	}

	IDX idx = INVALIDIDX;
	if (SUCCEEDED(pBook->GetSheetIdxByName(strName.c_str(), &idx)) && idx != INVALIDIDX)
	{
        IDX idxThis = 0;
		spSheet->GetSheet()->GetIndex(&idxThis);
		if (idx != idxThis)
		    return E_NAME_CONFLICT;
	}

	if (xstricmp(strName.c_str(), TX_History_Reserved_SheetName) == 0)
	{
		return E_FAIL;
	}

	hr = spSheet->put_Name(strName);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT TaskExecDbSheetSetNameLegacy::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

PCWSTR TaskExecDbSheetSetNameLegacy::GetTag()
{
	return __X("sheet.setName");
}

namespace
{

HRESULT ExtendDateConfigEndField(IDBSheetView* pView, IDBSheetView_DateConfig* pView_DateConfig, VarObj param)
{
	if (pView_DateConfig->GetBeginField() != pView_DateConfig->GetEndField() || INV_EtDbId == pView_DateConfig->GetEndField())
		return S_FALSE;

	ASSERT(param.has("beginTime") || param.has("endTime"));
	// 已与前端约定, 传入的参数必须是发生了改动的值 (拖拽时间线一端时, 另一端的值不传入). 只变更其一, 则有可能需要拓展字段
	// 若同时变更, 则需要分析是否为"单日期字段"下的"平移"
	bool needExtend = true;
	if ((param.has("beginTime") && param.has("endTime")))
	{
		PCWSTR beginTime = param.field_str("beginTime");
		PCWSTR endTime = param.field_str("endTime");
		needExtend = xstrcmp(beginTime, endTime) != 0;
	}

	// 根据视图当前的日期字段决定是否要新建日期字段
	if (needExtend)
	{
		HRESULT hr = S_OK;
		hr = pView_DateConfig->AutoSearchEndField();
		if (FAILED(hr))
			return hr;
		if (S_FALSE == hr)
		{
			VarObj args = param.get_s("args");
			PCWSTR endFieldName = args.field_str("endFieldName");
			ks_stdptr<IDBSheetRange> spDbRange;
			hr = DbSheet::AddField(pView, Et_DbSheetField_Date, args, &spDbRange);
			if (FAILED(hr))
				return hr;
			{
				EtDbId fldId = spDbRange->GetFieldId(0);
				ks_stdptr<IDbField> spField;
				IDbFieldsManager *pFieldsMgr = pView->GetSheetOp()->GetFieldsManager();
				hr = pFieldsMgr->GetField(fldId, &spField);
				if (FAILED(hr))
					return hr;
				hr = spField->SetName(endFieldName, TRUE, FALSE);
				if (FAILED(hr))
					return hr;
				hr = pView_DateConfig->SetEndField(fldId);
				if (FAILED(hr))
					return hr;
				// 甘特图自动创建的日期字段, 不作为可见字段
				hr = pView->SetFieldsHidden(spField->GetID(), TRUE);
				if (FAILED(hr))
					return hr;
			}
		}
	}
	// 函数成功的返回值也将用来表示是否确实拓展了字段. 需要修改代码时请留意这点
	return S_OK;
}

HRESULT assignDateConfigTimeLine(IDBSheetView* pView, VarObj param, EtDbId recId)
{
	HRESULT hr = S_OK;

	// 根据视图当前关联的日期字段状态决定新建及字段关联行为
	ks_stdptr<IDBSheetView_DateConfig> spView = pView;
	HRESULT extendRes = ExtendDateConfigEndField(pView, spView.get(), param);
	if (FAILED(extendRes))
		return extendRes;

	// 为单元格赋值
	bool skipSetBeginTime = not param.has("beginTime"), skipSetEndTime = not param.has("endTime");
	IDbFieldsManager* pFldsMgr = pView->GetFieldsManager();
	if (not skipSetBeginTime)
	{
		ks_stdptr<IDbField> spField;
		hr = pFldsMgr->GetField(spView->GetBeginField(), &spField);
		if (SUCCEEDED(hr) && spField->IsAuto())
			skipSetBeginTime = true;
	}
	if (not skipSetEndTime)
	{
		ks_stdptr<IDbField> spField;
		hr = pFldsMgr->GetField(spView->GetEndField(), &spField);
		if (SUCCEEDED(hr) && spField->IsAuto())
			skipSetEndTime = true;
	}

	if (S_OK == extendRes && skipSetEndTime)
	{
		// 自动拓展了结束日期字段, 且无法从前端参数设值, 则自动从开始日期字段取值并填入
		const_token_ptr pToken = nullptr;
		hr = pView->GetValueToken(recId, spView->GetBeginField(), &pToken);
		if (FAILED(hr))
			return hr;
		hr = pView->GetSheetOp()->SetTokenValue(recId, spView->GetEndField(), pToken, FALSE);
		if (FAILED(hr))
			return hr;
	}
	if (not skipSetBeginTime)
	{
		PCWSTR beginTime = param.field_str("beginTime");
		ASSERT(beginTime);
		hr = pView->SetValue(recId, spView->GetBeginField(), beginTime);
		if (FAILED(hr))
			return hr;
	}
	if (not skipSetEndTime)
	{
		PCWSTR endTime = param.field_str("endTime");
		ASSERT(endTime);
		hr = pView->SetValue(recId, spView->GetEndField(), endTime);
		if (FAILED(hr))
			return hr;
	}

	return hr;
}

}

// ================== TaskExecDbInsertRecord ==================

TaskExecDbInsertRecords::TaskExecDbInsertRecords(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbInsertRecords::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr = S_OK;
	binary_wo::VarObj param = cmd->cast().get("param");

	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	UINT recCnt = param.field_uint32("cnt");
	if (recCnt == 0)
		return E_INVALIDARG;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	// 查询视图不支持新增记录
	if (spDbSheetView->GetType() == et_DBSheetView_Query)
		return E_INVALIDARG;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	ks_stdptr<IWebhookManager> spWebhookMgr;
	pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
	IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
	KDisableDbWebhook disableDbWebhook(pRange, DbWebhookMask_changeCellData);
	ks_stdptr<IDbCollector> spDbCollector;
	pBook->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
	KDisableDbCollector disableDbCollector(spDbCollector, DCM_Webhook, CDOM_UpdateCells);

	// 插入记录自动填值的优先级为：权限筛选 < 视图筛选 < 默认值 < 分组
	ks_stdptr<IDBSheetRange> spDbRange;
	binary_wo::VarObj vPos = param.get_s("pos");
	DbIdPostion tar;
	EtDbId parentRecId = INV_EtDbId;
	if (GetDBParentId(param.get_s("pos"), parentRecId))
	{
        if (!spDbSheetView->IsEnableRecordsRelation() && !pCtx->isExecDirect())
            return E_DBSHEET_SET_PARENT_RECORD_NOT_SUPPORT;
	}
	
	DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TryModifyField2Multiple);
	if (!vPos.empty())
	{
		GetDBPos(vPos, tar);
		DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);
		hr = spDbSheetView->InsertRecords(recCnt, &spDbRange, &tar, parentRecId);
	}
	else
	{
		DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);
		hr = spDbSheetView->InsertRecords(recCnt, &spDbRange, nullptr, parentRecId);
	}
	if (dbLinkParam.adFieldEdit)
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if (peripheral) 
		{
			VarObj resObj = peripheral->resSelf();
			resObj.add_field_bool("unable2ModifyLinkField", true);
		}
	}
	if (FAILED(hr))
		return hr;
	if (FAILED(dbLinkParam.hr))
		return dbLinkParam.hr;

	// 在新增记录操作中, 部分文件的日志(2023 夸夸卡)出现了"add records"埋点到[Calculate] begin之间间隔了数百毫秒的现象
	wo::util::CallTimeStat cts("SetValueInDbtInsertRecords");

	hr = checkFormRecord(spDbSheetView, param);
	if (FAILED(hr))
		return hr;

	//添加记录时添加选项
	if (param.has("bAddSelectItem") && param.field_bool("bAddSelectItem"))
	{
		AddSelectItems(param, spDbSheetOp);
	}

    // 修改新记录可能会因为字段权限原因导致失败，这里放开新记录的权限
    const EtDbId* pIds = NULL;
    UINT32 cnt = 0;
    spDbRange->GetRecordIds(&pIds, &cnt);
    DbSheet::DisableDbRecordProtectScope disableDbRecordProtectScope(m_spProtectionJudgement, pIds, cnt);

	VarObj vRecordsValues = param.get_s("recordsValues");
	if (!vRecordsValues.empty())
	{
		hr = setRecsValWrap(spDbSheetView, spDbRange, vRecordsValues, false);
		if (FAILED(hr))
			return hr;
	}

	// 复制记录并插入
	if (param.has("recId"))
	{
		EtDbId recId = GetEtDbId(param, "recId");
		hr = createRecordCopyWrap(spDbSheetView.get(), spDbRange, recId, param);
		if (FAILED(hr))
			return hr;
	}

	if (!vPos.empty())
	{
		// todo: 起初前端于末尾插入记录时，将不会带上pos参数。04885c3cd7c7ebc0af24775a5ca85b7ff3b41237 修改了分组下插入的逻辑，分组下插入总是带上id
		// 以告知内核从何处复制分组条件。分组下插入也分成“组内末尾插入”和“组内中间插入”，后者是填报者不具有的权限（因为它改变了其他记录的相对顺序）。
		// 这部分逻辑条件允许时建议再做整理与优化。目前的优先级是“复制记录并插入” > “分组下插入” > “非复制/非分组下插入”。
		if (tar.id != INV_EtDbId)
		{
			DbSheet::DisableDbSheetProtectScope disPtScope(m_spProtectionJudgement, sheetStId);
			const IDBRecordsOrderManager* pMgr = spDbSheetView->GetConstOrderManager();
			if (pMgr && pMgr->GetGroupConditionCount() > 0)
			{
				// 如果不是"复制记录并插入", 则存在分组时, 从插入位置获取自动填充值
				// 看板视图需要应用前端传入的默认值，其并不等于记录基准单元格的值（多项，联系人，关联），所以不再走填充份分组值的逻辑
				if (false == param.has("recId") && parentRecId == INV_EtDbId && spDbSheetView->GetType() != et_DBSheetView_Kanban)
				{
					EtDbId recId = tar.id;
					if (spDbSheetView->IsExistParentRecord())
						recId = spDbSheetView->GetRecordsManager()->GetTopRecord(recId);
					hr = FillGroupedValuesWrap(spDbSheetView, spDbRange, recCnt, recId);
					if (FAILED(hr))
						return hr;
				}
			}

			//添加子记录
            if (parentRecId != INV_EtDbId)
            {
                hr = spDbSheetView->SyncAdjustRecordOrder(spDbRange, tar, parentRecId);
                if (FAILED(hr))
                    return hr;
            }

			// 存在分组且从分组末尾插入时，不需要显式地 move records，它会通过 update 环节移动到正确的位置。不显式调用 move records 也
			// 可以避开 dbsheet 权限对 move 行为的限制, 免得某些操作被权限意外拦截
			else if (nullptr == pMgr || pMgr->GetGroupConditionCount() == 0
				|| (false == vPos.has("isAtEnd") || false == vPos.field_bool("isAtEnd")))
			{
				if (spDbSheetView->GetType() == et_DBSheetView_Kanban)
				{
					PCWSTR groupId = nullptr;
					if (param.has("groupId"))
						groupId = param.field_str("groupId");
					ks_stdptr<IDBSheetView_Kanban> spDbKanbanSheetView = spDbSheetView;
					hr = spDbKanbanSheetView->MoveGroupRecords(spDbRange, tar, groupId, groupId);
				}
				else
				{
					hr = spDbSheetView->MoveRecords(spDbRange, tar);
				}
				if (FAILED(hr))
					return hr;
			}
		}
	}
    
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		binary_wo::VarObj rgObj = resObj.add_field_struct("insertedRange");
		writeDbRange(spDbSheetView, spDbRange, rgObj);
	}

	return hr;
}

namespace
{

HRESULT FillGroupedValues(IDBSheetView* pSheetView, IDBSheetRange* pDBRange, UINT recCnt, EtDbId srcRecId,
	std::unique_ptr<DbReversedLinkParam>& upDbLinkParam)
{
	HRESULT hr = S_OK;
	if (!pSheetView || !pDBRange)
		return hr;

	if (const IDBRecordsOrderManager* pMgr = pSheetView->GetConstOrderManager())
	{
		bool hasDbBidirectionalLinkField = false;
		std::unique_ptr<DbSheet::DbLinkHlp> upDbLinkHlp;
		IDbFieldsManager *spFieldsMgr = pSheetView->GetFieldsManager();

		for (UINT i = 0, count = pMgr->GetGroupConditionCount(); i < count; i++)
		{
			const IDBRecordsOrderCondition* cond = pMgr->GetGroupCondition(i);
			EtDbId fieldId = cond->GetKeyFieldId();
			if (not hasDbBidirectionalLinkField)
			{
				ks_stdptr<IDbField> spField;
				HRESULT hr = spFieldsMgr->GetField(fieldId, &spField);
				if (FAILED(hr))
					return hr;
				if (spField->GetType() == Et_DbSheetField_Link)
				{
					hasDbBidirectionalLinkField = true;
					upDbLinkParam.reset(new DbReversedLinkParam(
						DbReversedLinkParam::TryModifyField2Multiple));
					upDbLinkHlp.reset(new DbSheet::DbLinkHlp(pSheetView->GetSheetOp()->GetBook(), *upDbLinkParam));
				}
			}

			for (UINT recIdx = 0; recIdx < recCnt; recIdx++)
			{
				EtDbId recId = pDBRange->GetRecordId(recIdx);
                const_token_ptr pToken = nullptr;
                pSheetView->GetSheetOp()->GetValueToken(srcRecId, fieldId, &pToken);
                // 当分组值为空时不填值，走默认值
                if (pToken == nullptr)
                    continue;
				hr = pSheetView->GetSheetOp()->CopyCell(srcRecId, fieldId, recId);
				if(hr == E_DBSHEET_VALUE_NOT_UNIQUE_IN_FIELD)
				{
					//产品要求：设置筛选条件/分组条件后，添加记录时，「自动填充值」与「禁止录入重复值」的冲突
					//如果以某字段设置了筛选/分组条件，则在筛选/分组结果下，组内新增记录时，该字段不进行填充
					hr = S_OK;
					continue;
				}
				if (FAILED(hr))
					return hr;
			}
		}
	}

	return hr;
}

} // anonymous

HRESULT TaskExecDbInsertRecords::FillGroupedValuesWrap(IDBSheetView* pSheetView, IDBSheetRange* pDBRange, UINT recCnt, EtDbId srcRecId)
{
	std::unique_ptr<DbReversedLinkParam> upDbLinkParam;
	HRESULT hr = FillGroupedValues(pSheetView, pDBRange, recCnt, srcRecId, upDbLinkParam);
	if (FAILED(hr))
		return hr;
	if (upDbLinkParam && upDbLinkParam->adFieldEdit)
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if (peripheral)
		{
			VarObj resObj = peripheral->resSelf();
			resObj.add_field_bool("unable2ModifyLinkField", true);
		}
	}
	return hr;
}


PCWSTR TaskExecDbInsertRecords::GetTag()
{
	return __X("dbSheet.insertRecords");
}

// ================== TaskExecDbRemoveRecords ==================

TaskExecDbRemoveRecords::TaskExecDbRemoveRecords(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRemoveRecords::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetRange> spRg;
	HRESULT hr = GetDBRange(spDbSheetView, param.get("rg"), false, true, &spRg);
	if(FAILED(hr))
		return hr;

	if(!spRg->IsEntireRecord()) 
		return E_FAIL;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	bool bAlterDueDateAutomation = DbSheet::HasDueDateAutomation(spDbSheetOp, spRg);
	bool bExceededMaxBatchRecords = false;
	if (bAlterDueDateAutomation)
	{
		ks_stdptr<IDbAutomations> m_spAutomations;
		IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
		pBook->GetExtDataItem(edBookDbAutomations, (IUnknown**)&m_spAutomations);
		IDbAutomationConfig *pConfig = m_spAutomations->GetConfig();
		if (spRg->GetRecordCnt() > pConfig->GetMaxBatchRecords())
		{
			bExceededMaxBatchRecords = true;
			spRg->ShrinkRecords(pConfig->GetMaxBatchRecords());
		}
	}

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	hr = spDbSheetView->RemoveRecords(spRg);
	if(FAILED(hr))
		return hr;

	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral) 
	{
		if(SUCCEEDED(hr))
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			resObj.add_field_str("cmdName", GetTag());
			resObj.add_field_bool("alterDueDateAutomation", bAlterDueDateAutomation);
			resObj.add_field_bool("exceededMaxBatchRecords", bExceededMaxBatchRecords);
		}
	}

	return S_OK;
}

PCWSTR TaskExecDbRemoveRecords::GetTag()
{
	return __X("dbSheet.removeRecords");
}

// ================== TaskExecDbSetParentRecord ==================

TaskExecDbSetParentRecord::TaskExecDbSetParentRecord(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetParentRecord::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

    if (!spDbSheetView->IsEnableRecordsRelation() && !pCtx->isExecDirect())
        return E_DBSHEET_SET_PARENT_RECORD_NOT_SUPPORT;

    HRESULT hr = m_spProtectionJudgement->CheckViewCanMoveRecords(sheetStId, viewId);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDBSheetRange> spRg;
	hr = GetDBRange(spDbSheetView, param.get("rg"), false, true, &spRg);
	if(FAILED(hr))
		return hr;

	if(!spRg->IsEntireRecord()) 
		return E_INVALIDARG;

	EtDbId parentRecId = GetEtDbId(param, "parentId");
	DbIdPostion pos;
	GetDBPos(param.get_s("pos"), pos);

	ks_stdptr<IDBSheetOp> spDbSheetOp = spDbSheetView->GetSheetOp();
    std::vector<EtDbId> records;
    hr = GetAllParentRecords(spDbSheetOp, spRg, records);
    if (FAILED(hr))
        return hr;
	hr = spDbSheetOp->GetRecordsManager()->SetRecordsRelation(parentRecId, records.data(), records.size());
	if(FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetRange> spNewRg;
	hr = GetAllRecordByParentRecords(spDbSheetView, records.data(), records.size(), &spNewRg);
	if(FAILED(hr))
		return hr;

    hr = spDbSheetView->SyncAdjustRecordOrder(spNewRg, pos, parentRecId);
    if (FAILED(hr))
        return hr;

    return S_OK;
}

PCWSTR TaskExecDbSetParentRecord::GetTag()
{
	return __X("dbSheet.setParentRecord");
}



// ================== TaskExecDbSetViewOption ==================
TaskExecDbSetViewOption::TaskExecDbSetViewOption(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetViewOption::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

    ET_DBSheet_ViewType type = spDbSheetView->GetType();
    if (type != et_DBSheetView_Grid && type != et_DBSheetView_Gantt)
        return E_FAIL;

	HRESULT hr = m_spProtectionJudgement->CheckSheetCanManage(sheetStId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView_Grid> spView = spDbSheetView;
	if (param.has("enableRecordsRelation"))
	{
		hr = spView->SetEnableRecordsRelation(param.field_bool("enableRecordsRelation"));	
    }
	if (param.has("enablePerspectiveSubrecord"))
	{
		if (spView->GetEnableRecordsRelation())
		{
			spView->SetEnablePerspectiveSubrecord(param.field_bool("enablePerspectiveSubrecord"));
		}
		else
			return E_FAIL;
	}

	return hr;
}

PCWSTR TaskExecDbSetViewOption::GetTag()
{
	return __X("dbSheet.setViewOption");
}


// ================== TaskExecDbSetCellHyperlink ==================
TaskExecDbSetCellHyperlink::TaskExecDbSetCellHyperlink(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb) {}

HRESULT TaskExecDbSetCellHyperlink::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	binary_wo::VarObj vCell = param.get("cell");
	EtDbId recId = GetEtDbId(vCell, "recordId");
	EtDbId fldId = GetEtDbId(vCell, "fieldId");
	if (!param.has("address") || !param.has("displayText"))
		return E_INVALIDARG;

	return spDbSheetView->SetValue(recId, fldId, param);
}

PCWSTR TaskExecDbSetCellHyperlink::GetTag()
{
	return __X("dbSheet.setCellHyperlink");
}

// ================== TaskExecDbSetCellValue ==================

TaskExecDbSetCellValue::TaskExecDbSetCellValue(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetCellValue::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	if(IsStatSheet(m_wwb->GetCoreWorkbook()->GetBook(), sheetStId))
	{
		//统计表不允许编辑
		WOLOG_ERROR << "[TaskExecDbSetCellValue] For statSheet, not allow to set cellValue";
		return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
	}

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	binary_wo::VarObj vCell = param.get("cell");
	EtDbId recId = GetEtDbId(vCell, "recordId");
	EtDbId fldId = GetEtDbId(vCell, "fieldId");

	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
	ks_stdptr<IDbField> spField;
	hr = pFieldsMgr->GetField(fldId, &spField);
	if (FAILED(hr))
		return E_DBSHEET_FIELD_NOT_FOUND;

    PCWSTR strVal = nullptr;
    if (spField->GetType() != Et_DbSheetField_Address && spField->GetType() != Et_DbSheetField_Cascade && spField->GetType() != Et_DbSheetField_Department)
    {
        strVal = param.field_str("val");
        ASSERT(strVal);
    }
	app_helper::KBatchUpdateCal buc(m_wwb->GetCoreWorkbook()->GetBook()->LeakOperator());

	if (param.has("userInfo"))
	{
		updateUserInfo(m_wwb->GetCoreWorkbook()->GetBook(), param);
	}

	// 超链接为超链接模式下单元格有值只设置显示文本或清空
	IDBSheetOp *pDbSheetOp = spDbSheetView->GetSheetOp();
	if (NeedSetHyperlink(spField, pDbSheetOp, recId, fldId, strVal, param.has("setHyperlinkDisplayText")))
	{
		hr = pDbSheetOp->SetHyperlinkAddress(recId, fldId, strVal);
		if (FAILED(hr))
			return hr;
	}
	bool isFormula = false;
	if (param.has("isFormula"))
		isFormula = param.field_bool("isFormula");
	// 目前只有关联字段是"可手动输入的公式"
	if (isFormula && (spField->GetType() != Et_DbSheetField_Link && 
		spField->GetType() != Et_DbSheetField_OneWayLink))
		return E_FAIL;

	if (param.has("bModifyLinkToMultiple"))
	{
		VarObj bModifyLinkToMultiple = param.get_s("bModifyLinkToMultiple");
		if (typeBool == bModifyLinkToMultiple.type() && bModifyLinkToMultiple.value_bool())
		{
			hr = attemptModifyLink2Multiple(spDbSheetView.get(), param, pCtx);
			if (FAILED(hr))
				return hr;
		}
	}

	switch (spField->GetType())
	{
        // 双向关联需要特殊处理. 设值操作可能因为反向关联字段的限制导致操作需要提权才能成功
        // 单向关联不需要这种处理
        case Et_DbSheetField_Link:
        {
            DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TrySetValueAndPassState);
            if (param.has("bReplaceLinkedvalue"))
            {
                VarObj bReplaceLinkedvalue = param.get_s("bReplaceLinkedvalue");
                if (typeBool == bReplaceLinkedvalue.type() && bReplaceLinkedvalue.value_bool())
                {
                    pCtx->setMessageBoxAnswer(WOMB_DBSHEET_LINK_NOT_MULTIPLE, IDOK);
                    dbLinkParam.strategy = DbReversedLinkParam::OverwriteIfSingleLink;
                }
            }
            {
                DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);
                hr = spDbSheetView->SetValue(recId, fldId, strVal);
            }

            hr = prepareMessageBoxIfSingleLink(spDbSheetView.get(), recId, fldId, param, hr, dbLinkParam);
            if (E_DBSHEET_LINK_NOT_MULTIPLE == hr)
                makeMessageBoxResult(pCtx, param);
            break;
        }
        case Et_DbSheetField_Address:
		case Et_DbSheetField_Cascade:
		case Et_DbSheetField_Department:
        {
            binary_wo::VarObj val = param.get_s("val");
            ASSERT(val.type() == typeStruct);
            hr = spDbSheetView->SetValue(recId, fldId, val);
            break;
        }
		case Et_DbSheetField_Url:
		{
			binary_wo::VarObjRoot root(new binary_wo::BinVarRoot());
			binary_wo::VarObj hyperlink(root.cast());
			hyperlink.add_field_str("displayText", strVal);
			ks_bstr address;
			if (NeedSetHyperlinkTokenAddress(spField, pDbSheetOp, recId, fldId, strVal, param.has("setHyperlinkDisplayText"), &address))
				hyperlink.add_field_str("address", strVal);
			else
				hyperlink.add_field_str("address", address.c_str());
			hr = spDbSheetView->SetValue(recId, fldId, hyperlink);
			break;
		}
		case Et_DbSheetField_SingleSelect:
		case Et_DbSheetField_MultipleSelect:
		{
			ks_stdptr<IDbField_Select> spField_Select = spField;
			if (spField_Select->GetAllowAddItemWhenInputting() && param.has("bAddSelectItem") && param.field_bool("bAddSelectItem"))
			{
				if (param.has("fieldValues"))
				{
					AddSelectItems(param, spDbSheetOp);
				}
				else
				{
					ks_stdptr<IDbSelectItemHandle> spTokenSelect;
					_db_CreateObject(CLSID_KDbSelectItemHandle, IID_IDbSelectItemHandle, (void **)&spTokenSelect);
					spTokenSelect->CreateFromCellStr(strVal);
					for (UINT i = 0; i < spTokenSelect->Count(); ++i)
					{
						PCWSTR itemVal = spTokenSelect->ItemText(i);
						if (spField_Select->HasItem(itemVal))
							continue;
						spField_Select->AppendItemWithAutoColor(itemVal);
					}
				}
			}
			hr = spDbSheetView->SetValue(recId, fldId, strVal);
			break;
		}
	default:
		hr = spDbSheetView->SetValue(recId, fldId, strVal);
		break;
	}

	return hr;
}

bool TaskExecDbSetCellValue::NeedSetHyperlink(IDbField* pField, IDBSheetOp *pDbSheetOp, EtDbId recId, EtDbId fldId, PCWSTR val, bool check = false)
{
	ET_DbSheet_FieldType type = pField->GetType();
	if (type == Et_DbSheetField_Email)
		return true;
	return false;
}

bool TaskExecDbSetCellValue::NeedSetHyperlinkTokenAddress(IDbField* pField, IDBSheetOp *pDbSheetOp, EtDbId recId, EtDbId fldId, PCWSTR val, bool check, OUT BSTR* pStr)
{
	ET_DbSheet_FieldType type = pField->GetType();
	if (type == Et_DbSheetField_Url)
	{
		// 兼容旧逻辑，设值直接将链接地址和文本设为同一值
		if (!check)
			return true;
		// 新逻辑，有值时为超链接模式只编辑显示文本，或输入为超连接地址时，显示文本和超链接一起更新
		if (*val == __Xc('\0'))
			return true;

		ks_bstr bstrVal(val);
		if (CheckAutoFixHypeLink(bstrVal, nullptr))
			return true;

		ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = pField;
		if (spFieldHyperlink->GetHyperlinkMode() == DbSheet_Hyperlink_Button_Mode)
			return true;

		const_token_ptr pToken = nullptr;
		if (FAILED(pDbSheetOp->GetValueToken(recId, fldId, &pToken)))
		{
			*pStr = ks_bstr(__X("")).detach();
			return false;
		}

		if (!pToken || !alg::const_handle_token_assist::is_type(pToken))
			return true;

		alg::const_handle_token_assist chta(pToken);
		alg::TOKEN_HANDLE handle = chta.get_handle();
		ks_stdptr<IDbHyperlinkHandle> spHyperlinkHandle;
		if (handle)
			spHyperlinkHandle = handle->CastUnknown();
		ks_bstr address(spHyperlinkHandle ? spHyperlinkHandle->GetAddress() : __X(""));
		bool needSet = address.empty();
		*pStr = address.detach();
		return needSet;
	}
	return false;
}

PCWSTR TaskExecDbSetCellValue::GetTag()
{
	return __X("dbSheet.setCellValue");
}

// ================== TaskExecDbSetRangeValues ==================

TaskExecDbSetRangeValues::TaskExecDbSetRangeValues(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetRangeValues::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	bool isIgnoreError = false;
	if (param.has("ignoreError"))
        isIgnoreError = param.field_bool("ignoreError");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetRange> spDbRange;
	HRESULT hr = GetDBRange(spDbSheetView, param.get("rg"), false, false, &spDbRange);
	if (FAILED(hr))
		return hr;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	binary_wo::VarObj vRecordsValues = param.get_s("recordsValues");
	if (!vRecordsValues.empty())
	{
		hr = setRecsValWrap(spDbSheetView, spDbRange, vRecordsValues, isIgnoreError);
		if (FAILED(hr))
			return hr;
	}
	
	return hr;
}

PCWSTR TaskExecDbSetRangeValues::GetTag()
{
	return __X("dbSheet.setRangeValues");
}
// ================== TaskExecDbModifyWebExtension ==================
TaskExecDbModifyWebExtension::TaskExecDbModifyWebExtension(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbModifyWebExtension::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	ks_stdptr<KsoShape> spKsoShape;
	GetApiShape(param, pCtx, &spKsoShape);
	ks_stdptr<IKsoShapeEx> spShapeEx = spKsoShape;
	if (!spShapeEx)
		return E_FAIL;
	ks_stdptr<IKShape> spCoreShape;
	spShapeEx->GetInterface(IID_IKShape, (void**)&spCoreShape);
	drawing::AbstractShape* pAbsShape = static_cast<drawing::AbstractShape*>(spCoreShape.get());
	if (!pAbsShape || !pAbsShape->isWebExtensionHostShape())
		return E_FAIL;

    IKWebExtension* pWebExtension = pAbsShape->getWebExtension();
    if (!pWebExtension)
        return E_FAIL;

    IDX sheetIdx = GetSheetIdx(param);
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheet* pWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWorksheet)
		return E_FAIL;
	ISheet* pSheet = pWorksheet->GetSheet();
    if (!(pSheet->IsDbDashBoardSheet()))
        return E_FAIL;

	if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(pSheet->GetStId())))
		return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

	ks_stdptr<IDBDashBoardDataOp> spDbDashBoardOp;
	VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashBoardOp));

    ks_stdptr<IDbtBookCtx> spDbtBookCtx;
	IBook* pBook = pWorkbook->GetBook();
    VS(pBook->GetExtDataItem(edBookDbBookCtx, (IUnknown**) &spDbtBookCtx));
    EtDbId moduleId = INV_EtDbId;
    if (param.has("dataSourceRange") || param.has("dataSourceSheetId"))
    {
        UINT dbSheetId = 0;
        RANGE workSheetRange(pBook->GetBMP());
        if (param.has("dataSourceRange"))
        {
            binary_wo::VarObj dataSourceRange = param.get("dataSourceRange");
            workSheetRange = ReadRange(dataSourceRange);
            CHECK_PROTECTION_ALLOW_READ(workSheetRange, pCtx);
        }
        else if (param.has("dataSourceSheetId"))
        {
            dbSheetId = param.field_uint32("dataSourceSheetId");
        }
        bool forceNormalRange = param.has("forceNormalRange") && param.field_bool("forceNormalRange");
        HRESULT hr = DbDashboard::CheckAndChangeDataSourceType(pBook, dbSheetId, workSheetRange, spDbtBookCtx,
                                                               pWebExtension, spDbDashBoardOp, forceNormalRange, &moduleId);
        if (FAILED(hr))
            return hr;
    }

    if (param.has("moduleId") && moduleId == INV_EtDbId)
        moduleId = GetEtDbId(param, "moduleId");

    ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
    WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
    if (dataSourceType == dst_dbTable)
    {
        HRESULT hr = ModifyDBChartSetting(param, pWebExtension, pCtx, moduleId);
        if (FAILED(hr))
            return hr;
    }
    else if (DbDashboard::IsEtDataSourceType(dataSourceType))
    {
        HRESULT hr = DbDashboard::SetWorksheetChartSetting(param, pWebExtension, spDbDashBoardOp);
        if (FAILED(hr))
            return hr;

        if (param.has("dataSourceRange"))
        {
            binary_wo::VarObj dataSourceRange = param.get("dataSourceRange");
            RANGE rg = ReadRange(dataSourceRange);
            CHECK_PROTECTION_ALLOW_READ(rg, pCtx);
            hr = DbDashboard::SetEtDataSource(m_wwb, spDbtBookCtx, pWebExtension, spDbDashBoardOp, rg);
            if (FAILED(hr))
                return hr;
        }
    }
    else
    {
        HRESULT hr = DashBoard::SetWebextentionProperty(param, pWebExtension, spDbDashBoardOp);
        if (FAILED(hr))
            return hr;
    }

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_uint32("shapeId", pAbsShape->id());
	}
	return S_OK;
}

HRESULT TaskExecDbModifyWebExtension::ModifyDBChartSetting(const binary_wo::VarObj& param, IKWebExtension* pWebExtension, KEtRevisionContext* pCtx, EtDbId moduleId)
{
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IDX sheetIdx = GetSheetIdx(param);
	IKWorksheet* pWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWorksheet)
		return E_FAIL;
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!(pSheet->IsDbDashBoardSheet()))
		return E_FAIL;
	ks_stdptr<IUnknown> spUnknown;
	pSheet->GetExtDataItem(edDbSheetChartDataStatisticMgr, &spUnknown);
	if (!spUnknown)
		return E_FAIL;
	ks_stdptr<IDBChartStatisticMgr> spStatisticModuleMgr = spUnknown;
	ks_stdptr<IDBDashBoardDataOp> spDbDashBoardOp;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashBoardOp));
	HRESULT hr = DashBoard::SetWebextentionProperty(param, pWebExtension, spDbDashBoardOp);
	if (FAILED(hr))
		return hr;
	if (param.has("addNewDataRange") && param.field_bool("addNewDataRange"))
	{
		hr = DbDashboard::SetStatsModuleDataSource(param, spStatisticModuleMgr, pWebExtension, nullptr);
		if (FAILED(hr))
			return hr;
	}
	if (moduleId == INV_EtDbId)
		return S_OK;
	ks_stdptr<IDBChartStatisticModule> spModule;
	hr = spStatisticModuleMgr->GetItemById(moduleId, &spModule);
	if (FAILED(hr))
		return hr;
	ET_DBSheet_ChartType chartType = DbDashboard::GetChartType(pWebExtension);
	return DbDashboard::SetStatsModuleSetting(param, pWorkbook, spModule, pCtx, chartType);
}

HRESULT TaskExecDbModifyWebExtension::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return  S_OK;
}

PCWSTR TaskExecDbModifyWebExtension::GetTag()
{
	return __X("dbSheet.modifyWebExtension");
}
// ================== TaskExecDbAddWebExtension ==================
TaskExecDbAddWebExtension::TaskExecDbAddWebExtension(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbAddWebExtension::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT stId = param.field_uint32("sheetStId");
	IDX sheetIdx = INVALIDIDX;
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	pWorkbook->GetBook()->STSheetToRTSheet(stId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_INVALIDARG;
	ks_stdptr<_Worksheet> spWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!spWorksheet)
		return E_FAIL;
	ISheet* pSheet = spWorksheet->GetSheet();
	if (!(pSheet->IsDbDashBoardSheet()))
		return E_FAIL;
	
	if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(pSheet->GetStId())))
		return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

    ks_stdptr<IDBDashBoardDataOp> spDbDashBoardOp;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashBoardOp));
	ks_stdptr<IUnknown> spUnknown;
	pSheet->GetExtDataItem(edDbSheetChartDataStatisticMgr, &spUnknown);
	if (!spUnknown)
		return E_FAIL;
	ks_stdptr<IDBChartStatisticMgr> spStatisticModuleMgr = spUnknown;

	ks_stdptr<etoldapi::Shapes> spShapes;
	HRESULT hr = spWorksheet->get_Shapes(FALSE, &spShapes);
	if (!spShapes)
		return E_FAIL;

	WebExtType webExtType = WET_DbDataSource;
	if (param.has("webExtType"))
	{
        hr = _webextension_GainEncodeDecoder()->DecodeWebExtType(param.field_str("webExtType"), &webExtType);
        if (FAILED(hr))
            return hr;
    }
	if (webExtType == WET_Normal || webExtType == WET_DataSource)
		return E_INVALID_REQUEST;

	KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
	if (!pCtx->isExecDirect())
	{
		int shapesCnt = 0;
		spShapes->get_Count(&shapesCnt);
		if (shapesCnt >= DashBoard::GetDashBoardModuleLimit())
			return E_DBSHEET_DASHBOARD_CHART_COUNT_LIMIT;

		if (webExtType == WET_DbPlugin)
		{
			hr = dashboardModuleMgrWrapper.CheckCanAddPlugin();
			if (FAILED(hr))
				return hr;
		}
	}

	ks_stdptr<KsoShape> spKsoShape;
	ks_bstr webExtensionKey(param.field_str("webExtensionKey"));
	if (webExtensionKey.empty())
		return E_FAIL;

	hr = spShapes->AddWebExtension(webExtensionKey, nullptr, webExtType, &spKsoShape);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IKsoShapeEx> spShapeEx = spKsoShape;
	if (!spShapeEx)
		return E_FAIL;
	ks_stdptr<IKShape> spCoreShape;
	spShapeEx->GetInterface(IID_IKShape, (void**)&spCoreShape);
	drawing::AbstractShape* pAbsShape = static_cast<drawing::AbstractShape*>(spCoreShape.get());
	IKWebExtension* pWebExtension = pAbsShape->getWebExtension();
    // 有dataSourceSheetId参数说明数据源类型为数据表
	WebExtensionDataSourceType dataSourceType = param.has("dataSourceSheetId") ? dst_dbTable : dst_Unknown;
	switch (webExtType)
	{
	case WET_DbPlugin:
		dataSourceType = dst_dbTable;
		break;
	default:
		break;
	}
    if (dataSourceType != dst_Unknown)
    {
        hr = DbDashboard::CreateWebExtensionDataSource(pWebExtension, dataSourceType);
        if (FAILED(hr))
            return hr;
    }

	if (param.has("duplicate") && param.field_bool("duplicate"))
	{
		IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetWebExtensionMgr();
		if (!pWebExtensionMgr)
			return E_FAIL;
		IKWebExtension* pOldWebExtension = pWebExtensionMgr->FindWebExtension(sheetIdx, param.field_str("oldWebExtensionKey"));
		if (!pOldWebExtension)
			return E_INVALIDARG;
		hr = pOldWebExtension->CopyTo(pWebExtension);
		if (FAILED(hr))
			return hr;
		hr = DashBoard::SetWebextentionProperty(param, pWebExtension);
	}
	else
	{
        if (dataSourceType == dst_dbTable)
        {
            hr = InitDBChartSetting(param, pWebExtension, spStatisticModuleMgr, pCtx);
            if (FAILED(hr))
                return hr;
        }
        else
        {
            hr = DashBoard::SetWebextentionProperty(param, pWebExtension);
            if (FAILED(hr))
                return hr;
        }
		hr = pWebExtension->SetProperty(GetWebofficeUniquePropertyKey(), __X("true"));
	}
	if (FAILED(hr))
		return hr;

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		if (param.has("callBackId"))
			resObj.add_field_uint32("callBackId", param.field_uint32("callBackId"));
		resObj.add_field_uint32("shapeId", pAbsShape->id());
		resObj.add_field_int32("hr", hr);
	}
	return S_OK;
}

HRESULT TaskExecDbAddWebExtension::InitDBChartSetting(const binary_wo::VarObj& param, IKWebExtension* pWebExtension, IDBChartStatisticMgr* pModuleMgr, KEtRevisionContext* pCtx)
{
	HRESULT hr = DashBoard::SetWebextentionProperty(param, pWebExtension);
	if (FAILED(hr))
		return hr;

	EtDbId moduleId = INV_EtDbId;
	hr = DbDashboard::SetStatsModuleDataSource(param, pModuleMgr, pWebExtension, &moduleId);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDBChartStatisticModule> spModule;
	hr = pModuleMgr->GetItemById(moduleId, &spModule);
	if (FAILED(hr))
		return hr;

	if (param.has("dataSourceSheetId") && param.field_int32("dataSourceSheetId") == -1)
		return S_OK;

	ET_DBSheet_ChartType chartType = DbDashboard::GetChartType(pWebExtension);
	return DbDashboard::SetStatsModuleSetting(param, m_wwb->GetCoreWorkbook(), spModule, pCtx, chartType);
}

HRESULT TaskExecDbAddWebExtension::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return  S_OK ;
}

PCWSTR TaskExecDbAddWebExtension::GetTag()
{
	return __X("dbSheet.addWebExtension");
}

// ================== TaskExecDbDeleteWebExtension ==================
TaskExecDbDeleteWebExtension::TaskExecDbDeleteWebExtension(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDeleteWebExtension::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	EnableProtectCompileFmla enableClFlmGuard;
	binary_wo::VarObj param = cmd->cast().get("param");
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IDX sheetIdx = GetSheetIdx(param);
	IKWorksheet* pWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWorksheet)
		return E_FAIL;
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!(pSheet->IsDbDashBoardSheet()))
		return E_FAIL;

	if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(pSheet->GetStId())))
		return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

    ks_stdptr<IDBDashBoardDataOp> spDbDashBoardOp;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashBoardOp));

	ks_stdptr<KsoShape> spKsoShape;
	GetApiShape(param, pCtx, &spKsoShape);
	if (!spKsoShape)
		return E_FAIL;
	ks_stdptr<IKsoShapeEx> spShapeEx = spKsoShape;
	if (!spShapeEx)
		return E_FAIL;
	ks_stdptr<IKShape> spCoreShape;
	spShapeEx->GetInterface(IID_IKShape, (void**)&spCoreShape);
	drawing::AbstractShape* pAbsShape = static_cast<drawing::AbstractShape*>(spCoreShape.get());
	if (!pAbsShape || !pAbsShape->isWebExtensionHostShape())
		return E_FAIL;

	IKWebExtension* pWebExtension = pAbsShape->getWebExtension();
	if (!pWebExtension)
		return E_FAIL;

	HRESULT hr = spKsoShape->Delete();
	if (FAILED(hr))
		return hr;

    if (pWebExtension->IsDatasourceSupportingDashboardModule())
    {
        ks_stdptr<IKWebExtensionDataSourceHost> spDataSourceHost = pWebExtension;
        WebExtensionDataSourceType dataSourceType = spDataSourceHost->GetDataSourceType();
        if (dataSourceType == dst_dbTable)
        {
            hr = spDbDashBoardOp->GetChartStatisticMgr()->DelItem(GetEtDbId(param, "moduleId"));
            if (FAILED(hr))
                return hr;
        }
        else if (dataSourceType == dst_Workbook || dataSourceType == dst_PivotTable)
        {
            EtDbId moduleId = INV_EtDbId;
            VS(_appcore_GainDbSheetContext()->DecodeEtDbId(pWebExtension->GetModuleId(), &moduleId));
            hr = spDbDashBoardOp->GetWorksheetChartMgr()->RemoveChart(moduleId);
            if (FAILED(hr))
                return hr;

            ks_stdptr<IKWorkbookDataSource> spDataSource = spDataSourceHost->GetDataSource();
            hr = spDataSource->RemoveAllRange();
            if (FAILED(hr))
                return hr;
        }
    }
	if (pWebExtension->GetWebShapeType() == WET_DbView)
	{
		PCWSTR webextensionKey = pWebExtension->GetWebExtensionKey();
		ks_stdptr<IEtWebExtension_View> spWebExtView = pWebExtension;
		UINT oldSheetId = spWebExtView->GetSheetId();
		ks_stdptr<_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
		KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet); 
		std::unique_ptr<KDbDashboardViewWrapper> viewWrapper = dashboardModuleMgrWrapper.GetView(webextensionKey);
		hr = viewWrapper->RemoveViewShared(spWorksheet, oldSheetId, spWebExtView->GetViewId());
		if (FAILED(hr))
			return hr;
		hr = viewWrapper->DeleteView();
		if (FAILED(hr))
			return hr;

		spDbDashBoardOp->GetFilterMgr()->OnViewRemove(oldSheetId);
	}
	ks_wstring wstrWebExtensionKey = pWebExtension->GetWebExtensionKey();
	hr = pWebExtension->Delete(TRUE, FALSE);
	if (SUCCEEDED(hr))
		spDbDashBoardOp->OnRemoveWebExtension(wstrWebExtensionKey.c_str());
	return hr;
}

HRESULT TaskExecDbDeleteWebExtension::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbDeleteWebExtension::GetTag()
{
	return __X("dbSheet.deleteWebExtension");
}

int TaskExecDbDeleteWebExtension::GetShapeOpChangeType() const
{
	return ShapeOpChange_Delete;
}
// ================== TaskExecDbAddWebExtension ==================
TaskExecDbBatchModifyWebExtension::TaskExecDbBatchModifyWebExtension(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbBatchModifyWebExtension::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT stId = param.field_uint32("sheetStId");
	IDX sheetIdx = INVALIDIDX;
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	pWorkbook->GetBook()->STSheetToRTSheet(stId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_INVALIDARG;

	if (FAILED(m_spProtectionJudgement->CheckCanManageDBChart(stId)))
		return E_DBSHEET_PERMISSION_CHART_NOT_ALLOW;

	IKWebExtensionMgr* pWebExtensionMgr = pWorkbook->GetWebExtensionMgr();
	if (!pWebExtensionMgr)
		return E_FAIL;

	if (!param.has("batchModifyList"))
		return E_FAIL;
	HRESULT hr = E_FAIL;

	ks_stdptr<_Worksheet> spWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!spWorksheet)
		return E_FAIL;
	ISheet* pSheet = spWorksheet->GetSheet();
    ks_stdptr<IDBDashBoardDataOp> spDbDashBoardOp;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashBoardOp));
	binary_wo::VarObj batchModifyList = param.get("batchModifyList");
	for (int i = 0, length = batchModifyList.arrayLength(); i < length; ++i)
	{
		binary_wo::VarObj item = batchModifyList.at(i);
		if (!item.has("webExtensionKey"))
			return E_FAIL;

		IKWebExtension* pWebExtension = pWebExtensionMgr->FindWebExtension(sheetIdx, item.field_str("webExtensionKey"));
		if (pWebExtension)
		{
			hr = DashBoard::SetWebextentionProperty(item, pWebExtension, spDbDashBoardOp);
			if (FAILED(hr))
				return hr;
		}
	}
	return hr;
}

HRESULT TaskExecDbBatchModifyWebExtension::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return  S_OK;
}

PCWSTR TaskExecDbBatchModifyWebExtension::GetTag()
{
	return __X("dbSheet.batchModifyWebExtension");
}
// ================== TaskExecDbSetCellPic ==================
static void exportShapeImage(KEtWorkbook* wwb, KEtRevisionContext* ctx, binary_wo::VarObj& param, drawing::AbstractShape* pShape)
{
	QByteArray sha1, picData;
	int16 dpi = ctx->getUser()->getDpi();
	if (pShape->getPicture(sha1, picData, dpi / 96.0) != WO_OK)
		return;

	param.add_field_str("sha1", krt::utf16(QString(sha1)));
	ctx->setIsRealTransform(true);
	wwb->exportShapePicture(sha1.data(), picData.data(), picData.size());
}

TaskExecDbSetCellPic::TaskExecDbSetCellPic(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbSetCellPic::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if (!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	binary_wo::VarObj vCell = param.get("cell");

	EtDbId recId = GetEtDbId(vCell, "recordId");
	EtDbIdx row = spDbSheetView->GetAllRecords()->Id2Idx(recId);
	EtDbId fldId = GetEtDbId(vCell, "fieldId");
	EtDbIdx col = spDbSheetView->GetAllFields()->Id2Idx(fldId);
	if (row == INV_EtDbIdx || col == INV_EtDbIdx)
		return E_INVALIDARG;

	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
	ks_stdptr<IDbField> spField;
	HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
	if (FAILED(hr))
		return hr;
	if (spField->GetType() != Et_DbSheetField_CellPicture)
		return E_INVALIDARG;

	single width = -1;
	single height = -1;
	binary_wo::VarObj widthVar = param.get_s("width");
	if (widthVar.type() != binary_wo::typeInvalid)
		width = widthVar.value_double();
	binary_wo::VarObj heightVar = param.get_s("height");
	if (heightVar.type() != binary_wo::typeInvalid)
		height = heightVar.value_double();

	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx =pCtx, wmh.objParam = param, wmh.strField = "mimeDataID";

	ks_wstring uuid;
	if (param.has("uuid"))
		uuid = param.field_str("uuid");

	IDX sheetIdx = INVALIDIDX;
	m_wwb->GetCoreWorkbook()->GetBook()->STSheetToRTSheet(sheetStId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_INVALIDARG;

	// InsertCellPicture不经过dbsheet data, 手动set value一下引发修改检测，触发自动化任务及视图更新的脏字段收集
	hr = spDbSheetView->SetValue(recId, fldId, __X(""));
	if(FAILED(hr))
		return hr;

	ks_stdptr<_Worksheet> spWorksheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	ks_stdptr<IKShape> spShape;
	// 生成的uuid需要存起来, 不然回放的时候会不一致
	hr = spWorksheet->InsertCellPicture(
		row, col, krt::utf16(wmh.resolvePath()), uuid, NULL, width, height, FALSE, &spShape);

	if (SUCCEEDED(hr) && spShape)
	{
		ks_castptr<drawing::AbstractShape> cpShape = spShape;
		exportShapeImage(m_wwb, pCtx, param, cpShape);
		param.add_field_str("uuid", uuid.c_str());
		pCtx->setIsRealTransform(true);
	}

	return hr;
}

HRESULT TaskExecDbSetCellPic::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	binary_wo::VarObj vCell = param.get("cell");
	return m_spProtectionJudgement->CheckCellCanEdit(sheetStId, GetEtDbId(vCell, "recordId"), GetEtDbId(vCell, "fieldId"));
}

PCWSTR TaskExecDbSetCellPic::GetTag()
{
	return __X("dbSheet.setCellPic");
}
// ================== TaskExecDbAddField ==================

TaskExecDbAddField::TaskExecDbAddField(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

namespace
{
void assignDateConfigField(IDBSheetView_DateConfig* pView, EtDbId fldId)
{
	if (INV_EtDbId == pView->GetBeginField() && INV_EtDbId == pView->GetEndField())
	{
		VS(pView->SetBeginField(fldId));
		VS(pView->SetEndField(fldId));
	}
	else if (pView->GetBeginField() == pView->GetEndField())
	{
		VS(pView->SetEndField(fldId));
	}
}
}

HRESULT TaskExecDbAddField::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr = S_OK;
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT32 sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

    ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	ks_stdptr<IDBSheetRange> spDbRange;
	binary_wo::VarObj args = param.get_s("args");
	if(args.type() == binary_wo::typeArray)
	{
		hr = DbSheet::AddFields(spDbSheetView, args, &spDbRange);//插入多列
		if (FAILED(hr))
			return hr;
	}
	else
	{
		WebStr fldTypeStr = nullptr;
		if (param.has("type"))
		{
			fldTypeStr = param.field_str("type");
		}
		ET_DbSheet_FieldType fldType = Et_DbSheetField_MultiLineText;
		if (fldTypeStr)
			m_pEncodeDecoder->DecodeFieldType(fldTypeStr, &fldType);
		hr = DbSheet::AddField(spDbSheetView, fldType, args, &spDbRange);
		if (FAILED(hr))
			return hr;
	}

	hr = handleField(spDbSheetView, spDbSheetViews, param, spDbRange);
	if (FAILED(hr))
		return hr;

	binary_wo::VarObj vPos = param.get_s("pos");
	if(!vPos.empty())
	{
		DbIdPostion tar;
		GetDBPos(vPos, tar);
		if(tar.id != INV_EtDbId)
		{
			hr = spDbSheetView->MoveFields(spDbRange, tar);
			if(FAILED(hr))
				return hr;
		}
	}

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		binary_wo::VarObj rgObj = resObj.add_field_struct("insertedRange");
		writeDbRange(spDbSheetView, spDbRange, rgObj);
	}
	return hr;
}

HRESULT TaskExecDbAddField::handleField(IDBSheetView* spDbSheetView, IDBSheetViews* spDbSheetViews, const VarObj& param, IDBSheetRange *spDbRange)
{
	HRESULT hr = S_OK;
	bool dispSumForNumberAndCurrencyInStatusBar = false;
	if (param.has("dispSumForNumberAndCurrencyInStatusBar"))
		dispSumForNumberAndCurrencyInStatusBar = true;

	UINT32  fieldCnt = spDbRange->GetFieldCnt();
	IDbFieldsManager *spFieldsMgr = spDbSheetView->GetFieldsManager();
	for (UINT32 idx = 0; idx < fieldCnt; ++ idx)
	{
		EtDbId fldId = spDbRange->GetFieldId(idx);
		ks_stdptr<IDbField> spField;
		hr = spFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;
		ET_DbSheet_FieldType fldType = spField->GetType();
		switch (fldType)
		{
		case Et_DbSheetField_Date:
		{
			ks_stdptr<IDBSheetViewsEnum> spEnum;
			if (SUCCEEDED(spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
			{
				do
				{
					ks_stdptr<IDBSheetView> spDbSheetView;
					spEnum->GetCurView(&spDbSheetView);
					if (!spDbSheetView)
						continue;

					switch (spDbSheetView->GetType())
					{
						case et_DBSheetView_Gantt:
						case et_DBSheetView_Calendar:
						{
							ks_stdptr<IDBSheetView_DateConfig> spDCView = spDbSheetView;
							if (nullptr == spDCView)
								break; // 退出swtich
							assignDateConfigField(spDCView.get(), fldId);
						}
						default:
							break; // 退出swtich
					}
				}while (SUCCEEDED(spEnum->Next()));
			}
			break;
		}
		case Et_DbSheetField_Number:
		case Et_DbSheetField_Currency:
		{
			if (!dispSumForNumberAndCurrencyInStatusBar)
				break;
			ET_DBSheet_ViewType type = spDbSheetView->GetType();
			if (type != et_DBSheetView_Grid && type != et_DBSheetView_Query)
				break;
			hr = spDbSheetView->GetMutableStatisticOptions()->SetStatistic(fldId, DBSSO_sum);
			if(FAILED(hr))
				return hr;
			break;
		}
		case Et_DbSheetField_Automations:
		{
			ks_stdptr<IDBSheetOp> spDbSheetOp;
			HRESULT hr = m_commonHelper.GetDBSheetOp(spDbSheetView->GetSheetOp()->GetSheetId(), &spDbSheetOp);
			if (FAILED(hr))
				return hr;

			ks_stdptr<IDbAutomations> spAutomations;
			m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edBookDbAutomations, (IUnknown**)&spAutomations);
			if (spAutomations)
			{
				IDbAutomationConfig *pConfig = spAutomations->GetConfig();
				if (DbSheet::ExceededMaxDueDateAutomationLimits(spDbSheetOp, pConfig->GetMaxDueDateAutomations()))
					return E_DBSHEET_DUE_DATE_AUTOMATION_EXCEEDED_LIMITS;
			}
			m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
			break;
		}
		case Et_DbSheetField_Note:
			if (DbSheet::ExceededFieldCountLimits(spDbSheetView, Et_DbSheetField_Note, 1))
				return E_DBSHEET_RICHTEXT_EXCEEDED_LIMITS;
			break;
		default:
			break;
		}
	}

	return S_OK;
}

HRESULT TaskExecDbAddField::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	return m_spProtectionJudgement->CheckFieldCanAdd(sheetStId);
}

PCWSTR TaskExecDbAddField::GetTag()
{
	return __X("dbSheet.insertFields");
}

// ================== TaskExecDbRemoveFields ==================
TaskExecDbRemoveFields::TaskExecDbRemoveFields(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRemoveFields::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetRange> spRg;
	HRESULT hr = GetDBRange(spDbSheetView, param.get("rg"), true, false, &spRg);
	if(FAILED(hr))
		return hr;

	if(!spRg->IsEntireField()) 
		return E_FAIL;
	
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	bool needClearTranscation = false;
	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
	for (int i = 0, cnt = spRg->GetFieldCnt(); i < cnt; i++)
	{
		EtDbId fldId = spRg->GetFieldId(i);
		ks_stdptr<IDbField> spField;
		hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			continue;
		ET_DbSheet_FieldType fldType = spField->GetType();
		if (fldType == Et_DbSheetField_Automations)
		{
			needClearTranscation = true;
			break;
		}

		if(IsStatSheetSettingField(pBook, sheetStId, fldId))
		{
			//统计表不允许删除自动生成的字段
			WOLOG_ERROR << "[TaskExecDbRemoveFields] For statSheet, not allow to remove the statSettingFields";
			return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
		}
	}
	etoldapi::_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	std::unique_ptr<RemoveFldStrategy> upStrategy;
	if (param.has("removeReversedLink"))
	{
		upStrategy.reset(new RemoveFldStrategy);
		VAR_OBJ_EXPECT_BOOL(param, "removeReversedLink");
		upStrategy->removeReversedLinkField = alg::bool2BOOL(param.field_bool("removeReversedLink"));
	}
	
	hr = spDbSheetView->RemoveFields(spRg, upStrategy.get());
	if(FAILED(hr))
		return hr;

	if (needClearTranscation)
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(pWorkbook);
	return hr;
}

PCWSTR TaskExecDbRemoveFields::GetTag()
{
	return __X("dbSheet.removeFields");
}

// ================== TaskExecDbModifyField ==================
TaskExecDbModifyField::TaskExecDbModifyField(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb) {}

HRESULT TaskExecDbModifyField::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId fldId = GetEtDbId(param, "fieldId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	VarObj args = param.get_s("args");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if (!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDbField> spField;
	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
	HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;

	_Workbook* pWb = m_wwb->GetCoreWorkbook();
	IBook* pBook = pWb->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	if(args.has("clearField") && args.field_bool("clearField"))
	{
		ks_stdptr<IDBSheetRange> spRg;
		spDbSheetView->CreateDBSheetRange(&spRg);
		spRg->AddFieldId(fldId);
		spRg->SetRecordIds(spDbSheetView->GetAllRecords());
		hr = spDbSheetView->Clear(spRg, TRUE);
		if(FAILED(hr))
			return hr;
	}

	// 1. 信息收集
	ET_DbSheet_FieldType oldType = spField->GetType();
	ET_DbSheet_FieldType newType = Et_DbSheetField_MultiLineText;
	if (param.has("type"))
		m_pEncodeDecoder->DecodeFieldType(param.field_str("type"), &newType);

	if(IsStatSheetSettingField(pWb->GetBook(), sheetStId, fldId))
	{
		//统计表自动生成的字段，不能更改类型，但是可以更改字段名、格式
		if(oldType != newType)
		{
			WOLOG_ERROR << "[TaskExecDbModifyField] For statSheet, not allow to modify the statSettingFields type!";
			return E_DBSHEET_STAT_SHEET_NOT_SUPPORT_OPT;
		}
	}
	
	bool needClearTranscation = DbSheet::IsRelatedToDueDate(spDbSheetOp, fldId);
	// 字段转换时，需要先关闭重复值设置
	if (oldType != newType)
		VS(spField->SetValueUnique(FALSE, FALSE));
	// 2. 更新字段配置信息
	HRESULT updateHr = S_OK;
	{
		DbSheet::DbFieldUpdateScope updateScope(spField, pBook, updateHr);
		if (FAILED(updateHr))
			return updateHr;

		bool bGenerateSelectItemsFromContents = DbSheet::needGenerateSelectItemsFromContents(updateScope.GetOldField(), spField);
		hr = DbSheet::ConfigureDbField(spField, args, newType, false, spDbSheetView, bGenerateSelectItemsFromContents);
		if (FAILED(hr))
			return hr;
	} // DbFieldUpdateScope
	if (FAILED(updateHr))
		return updateHr;

	if (args.has("fieldWidth") && spDbSheetView->GetType() == et_DBSheetView_Grid)
	{
		INT width = args.field_int32("fieldWidth");
		switch (spDbSheetView->GetType())
		{
		case et_DBSheetView_Grid:
		case et_DBSheetView_Gantt:
		case et_DBSheetView_Query:
		{
			ks_stdptr<IDBSheetView_Grid> spGridView = spDbSheetView;
			if (spGridView != nullptr)
			{
				hr = spGridView->SetFieldWidth(fldId, width, FALSE);
				if (FAILED(hr))
					return hr;
			}
		}
		default:
			break;
		}
	}

	bool bOldValueUnique = spField->IsValueUnique();
	//更改字段“禁止录入重复值”属性
	if(args.has("valueUnique"))
	{
		bool bValUniqueProp = args.field_bool("valueUnique");
		hr = spField->SetValueUnique(bValUniqueProp, TRUE);
		if(FAILED(hr))
		{
			return hr;
		}
	}
	if (bOldValueUnique != spField->IsValueUnique())
		needClearTranscation = true;
	// 4. 处理触发器相关依赖
	if (needClearTranscation)
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(pWb);
	else if (newType == Et_DbSheetField_Automations)
	{
		ks_stdptr<IUnknown> spUnknown;
		pBook->GetExtDataItem(edBookDbAutomations, &spUnknown);
		ks_stdptr<IDbAutomations> spAutomations = spUnknown;
		if (spAutomations)
		{
			IDbAutomationConfig *pConfig = spAutomations->GetConfig();
			if (DbSheet::ExceededMaxDueDateAutomationLimits(spDbSheetOp, pConfig->GetMaxDueDateAutomations()))
				return E_DBSHEET_DUE_DATE_AUTOMATION_EXCEEDED_LIMITS;
		}
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(pWb);
	}
	return S_OK;
}

HRESULT TaskExecDbModifyField::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId fldId = GetEtDbId(param, "fieldId");
	return m_spProtectionJudgement->CheckFieldCanEdit(sheetStId, fldId);
}

PCWSTR TaskExecDbModifyField::GetTag()
{
	return __X("dbSheet.modifyField");
}


// ================== TaskExecDbClearRange ==================

TaskExecDbClearRange::TaskExecDbClearRange(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbClearRange::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetRange> spRg;
	HRESULT hr = GetDBRange(spDbSheetView, param.get("rg"), false, false, &spRg);
	if(FAILED(hr))
		return hr;

	bool bAlterAutoField = false;
	UINT32 fldCnt = spRg->GetFieldCnt();
	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
	for (EtDbIdx fld = 0; fld < fldCnt; fld++)
	{
		EtDbId fldId = spRg->GetFieldId(fld);
		ks_stdptr<IDbField> spField;
		pFieldsMgr->GetField(fldId, &spField);
		if (spField == nullptr)
			continue;
		if (spField->IsAuto())
		{
			bAlterAutoField = true;
			break;
		}
	}

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;
	bool bAlterDueDateAutomation = DbSheet::HasDueDateAutomation(spDbSheetOp, spRg);
	bool bExceededMaxBatchRecords = false;
	if (bAlterDueDateAutomation)
	{
		ks_stdptr<IDbAutomations> m_spAutomations;
		IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
		pBook->GetExtDataItem(edBookDbAutomations, (IUnknown**)&m_spAutomations);
		IDbAutomationConfig *pConfig = m_spAutomations->GetConfig();
		if (spRg->GetRecordCnt() > pConfig->GetMaxBatchRecords())
		{
			bExceededMaxBatchRecords = true;
			spRg->ShrinkRecords(pConfig->GetMaxBatchRecords());
		}
	}

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	{
		DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TrySetValueAndPassState);
		DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);
		hr = spDbSheetView->Clear(spRg);
		if (FAILED(hr))
			return hr;
		if (FAILED(dbLinkParam.hr))
			return dbLinkParam.hr;
	}

	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral) 
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_bool("alterAutoField", bAlterAutoField);
		resObj.add_field_bool("alterDueDateAutomation", bAlterDueDateAutomation);
		resObj.add_field_bool("exceededMaxBatchRecords", bExceededMaxBatchRecords);
	}
	return hr;
}

PCWSTR TaskExecDbClearRange::GetTag()
{
	return __X("dbSheet.clearRange");
}


// ================== TaskExecDbMoveRange ==================

TaskExecDbMoveRange::TaskExecDbMoveRange(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbMoveRange::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetRange> spRg;
	HRESULT hr = GetDBRange(spDbSheetView, param.get("rg"), true, true, &spRg);
	if(FAILED(hr))
		return hr;

	DbIdPostion pos;
	GetDBPos(param.get_s("pos"), pos);

	bool isRecords = spRg->IsEntireRecord();
	bool isFields = spRg->IsEntireField();

	if(isRecords && isFields)
		return S_FALSE;

	if(!isRecords && !isFields)
		return E_INVALIDARG;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	binary_wo::VarObj vRecordsValues = param.get_s("recordsValues");
	bool onlyGroup = param.has("onlyGroup") && param.field_bool("onlyGroup");	
	if(!onlyGroup)
	{
		if(isRecords)
		{
            //移动记录的时候重设记录父子关系
            EtDbId parentId = INV_EtDbId;
            ks_stdptr<IDBSheetRange> spNewRg;
            bool setParent = GetDBParentId(param.get_s("pos"), parentId);
            if (spDbSheetView->GetType() == et_DBSheetView_Kanban)
            {
				PCWSTR groupFrom = nullptr, groupTo = nullptr;
				bool isNewSortGroup = param.has("groupFrom") && param.has("groupTo");
				if(isNewSortGroup)
				{
					groupFrom = param.field_str("groupFrom");
					groupTo = param.field_str("groupTo");
				}
				ks_stdptr<IDBSheetView_Kanban> spDbKanbanSheetView = spDbSheetView;
            	hr = spDbKanbanSheetView->MoveGroupRecords(spRg, pos, groupFrom, groupTo);
			}
			else if (setParent)
            {
                if (!spDbSheetView->IsEnableRecordsRelation() && !pCtx->isExecDirect())
                    return E_DBSHEET_SET_PARENT_RECORD_NOT_SUPPORT;

                hr = m_spProtectionJudgement->CheckViewCanMoveRecords(sheetStId, viewId);
                if (FAILED(hr))
                    return hr;

                ks_stdptr<IDBSheetOp> spDbSheetOp = spDbSheetView->GetSheetOp();
                std::vector<EtDbId> records;
                hr = GetAllParentRecords(spDbSheetOp, spRg, records);
                if (FAILED(hr))
                    return hr;

                hr = spDbSheetOp->GetRecordsManager()->SetRecordsRelation(parentId, records.data(), records.size());
                if (FAILED(hr))
                    return hr;

                hr = GetAllRecordByParentRecords(spDbSheetView, records.data(), records.size(), &spNewRg);
                if (FAILED(hr))
                    return hr;

                hr = spDbSheetView->SyncAdjustRecordOrder(spNewRg, pos, parentId);
            }
			else
				hr = spDbSheetView->MoveRecords(spRg, pos);
		}
		else
			hr = spDbSheetView->MoveFields(spRg, pos);

		if(FAILED(hr))
			return hr;
	}
	else if (vRecordsValues.empty())
	{
		return E_INVALIDARG;
	}


	if (!vRecordsValues.empty())
	{
		hr = setRecsValWrap(spDbSheetView, spRg, vRecordsValues, false);
		if (FAILED(hr))
			return hr;
	}

	return hr;
}

PCWSTR TaskExecDbMoveRange::GetTag()
{
	return __X("dbSheet.moveRange");
}

// ================== TaskExecDbMoveGroup ==================

TaskExecDbMoveGroup::TaskExecDbMoveGroup(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbMoveGroup::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
    if (spDbSheetView->GetType() != et_DBSheetView_Kanban)
        return E_FAIL;
	
    PCWSTR groupFrom = param.field_str("groupFrom");
    PCWSTR groupTo = param.field_str("groupTo");
    bool isBack = param.field_bool("isBack");
    HRESULT hr;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	ks_stdptr<IDBSheetView_Kanban> spDbKanbanSheetView = spDbSheetView;
    hr = spDbKanbanSheetView->MoveGroup(groupFrom, groupTo, isBack);

	return hr;
}

PCWSTR TaskExecDbMoveGroup::GetTag()
{
	return __X("dbSheet.moveGroup");
}

// ================== TaskExecDbRemoveGroup ==================

TaskExecDbRemoveGroup::TaskExecDbRemoveGroup(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRemoveGroup::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
    if (spDbSheetView->GetType() != et_DBSheetView_Kanban)
        return E_FAIL;
	
    PCWSTR groupId = param.field_str("groupId");
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

	DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TrySetValueAndPassState);
	DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);
	ks_stdptr<IDBSheetView_Kanban> spDbKanbanSheetView = spDbSheetView;
    HRESULT hr = spDbKanbanSheetView->RemoveGroup(groupId);
	
	return hr;
}

PCWSTR TaskExecDbRemoveGroup::GetTag()
{
	return __X("dbSheet.removeGroup");
}

// ================== TaskExecDbSetFieldHidden ==================
TaskExecDbSetFieldHidden::TaskExecDbSetFieldHidden(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetFieldHidden::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	bool hidden = param.field_bool("hidden");

	// 兼容旧命令的代码，后面再删掉 begin
	if (param.has("fieldId"))
	{
		EtDbId fldId = GetEtDbId(param, "fieldId");
		return spDbSheetView->SetFieldsHidden(fldId, hidden);
	}
	// 兼容 end

	ks_stdptr<IDBSheetRange> spRg;
	HRESULT hr = GetDBRange(spDbSheetView, param.get("rg"), true, false, &spRg);
	if(FAILED(hr))
		return hr;

	if (!spRg->IsEntireField())
		return E_FAIL;

	for (EtDbIdx i = 0; i < spRg->GetFieldCnt(); i++)
	{
		EtDbId fldId = spRg->GetFieldId(i);
		hr = spDbSheetView->SetFieldsHidden(fldId, hidden);
		if (FAILED(hr))
			return hr;
	}
	return hr;
}

HRESULT TaskExecDbSetFieldHidden::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetFieldsHidden(sheetStId, viewId);
}

PCWSTR TaskExecDbSetFieldHidden::GetTag()
{
	return __X("dbSheet.setFieldHidden");
}

// ================== TaskExecDbSetFieldWidth ==================
TaskExecDbSetFieldWidth::TaskExecDbSetFieldWidth(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetFieldWidth::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	INT width = param.field_int32("width");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetRange> spRg;
	HRESULT hr = GetDBRange(spDbSheetView, param.get("rg"), true, false, &spRg);
	if (FAILED(hr))
		return hr;

	switch (spDbSheetView->GetType())
	{
	case et_DBSheetView_Grid:
	case et_DBSheetView_Gantt:
	case et_DBSheetView_Query:
	{
		ks_stdptr<IDBSheetView_Grid> spGridView = spDbSheetView;
		if (spGridView == nullptr)
			return E_FAIL;

		for (EtDbIdx i = 0; i < spRg->GetFieldCnt(); i++)
		{
			EtDbId fldId = spRg->GetFieldId(i);
			hr = spGridView->SetFieldWidth(fldId, width, FALSE);
			if (FAILED(hr))
				return hr;
		}
		break;
	}
	default:
		return E_FAIL;
	}

	return S_OK;
}

HRESULT TaskExecDbSetFieldWidth::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetFieldWidth(sheetStId, viewId);
}

PCWSTR TaskExecDbSetFieldWidth::GetTag()
{
	return __X("dbSheet.setFieldWidth");
}

// ================== TaskExecDbSetRecordsHeight ==================
TaskExecDbSetRecordsHeight::TaskExecDbSetRecordsHeight(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetRecordsHeight::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	INT height = param.field_int32("height");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	if(spDbSheetView->GetType() != et_DBSheetView_Grid)
		return E_FAIL;
	ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = spDbSheetView;
	if (spDbSheetViewGrid == nullptr)
		return E_FAIL;
	
	return spDbSheetViewGrid->SetRecordsHeight(height);
}

HRESULT TaskExecDbSetRecordsHeight::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetRecordsHeight(sheetStId, viewId);
}

PCWSTR TaskExecDbSetRecordsHeight::GetTag()
{
	return __X("dbSheet.setRecordsHeight");
}

// ================== TaskExecDbCreateView ==================
TaskExecDbCreateView::TaskExecDbCreateView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbCreateView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	WebStr name = param.field_str("name");
	
	ET_DBSheet_ViewType type = DbSheet::ConvertViewType(m_pEncodeDecoder, param.field_str("type"));

    ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetViews(sheetStId, &spDbSheetViews);
	if(!spDbSheetViews)
		return E_FAIL;

	HRESULT hr = spDbSheetViews->IsValidViewName(name);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
		return hr;

	ks_wstring nameStr(name);
	if (hr == E_DBSHEET_VIEW_NAME_CONFLICT)
	{
		int postfix = 2;
		do
		{
			QString newName = QString::fromUtf16(name) + QString::number(postfix, 10);
			postfix++;
			nameStr = krt::utf16(newName);
		} while (E_DBSHEET_VIEW_NAME_CONFLICT == spDbSheetViews->IsValidViewName(nameStr.c_str()));
		name = nameStr.c_str();
	}

	bool isAddField = false;
	ks_stdptr<IDBSheetRange> spRange;
	if (type == et_DBSheetView_Kanban)
		DbSheet::AddNecessarySelectField(spDbSheetViews.get(), &spRange, isAddField, param);
	bool autoChooseFieldForGroup = true; // 兼容旧命令，默认自动将第一个单选项字段设置为看板视图的分组条件
	if (param.has("autoChooseField"))
		autoChooseFieldForGroup = param.field_bool("autoChooseField");
	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = spDbSheetViews->CreateView(type, name, autoChooseFieldForGroup, Et_DBSheetViewUse_ForDb, &spDbSheetView);
	if (FAILED(hr))
		return hr;
	
	//新建视图时， 可见记录默认使用底表的记录顺序， 存在父子记录时须排序更新可见记录顺序
	if (spDbSheetView->IsExistParentRecord())
	{
		spDbSheetView->GetConstOrderManager()->ToggleForceSort();
	}

	if (wo::VarObjFieldValidation::expectString(param, "viewId"))
	{
		EtDbId viewId = GetEtDbId(param, "viewId");
		hr = spDbSheetViews->SortFieldsByActiveView(viewId, spDbSheetView);
		if (FAILED(hr))
			return hr;
	}

	hr = DbSheet::SetViewProp(spDbSheetViews, spDbSheetView, param, pCtx);
	if (FAILED(hr))
		return hr;
	
	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral) 
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		AddEtDbId(resObj, "viewId", spDbSheetView->GetId());
		resObj.add_field_bool("addField", isAddField);
		if (spRange)
		{
			AddEtDbId(resObj, "fieldId", spRange->GetFieldId(0));
		}
	}
	return hr;
}

HRESULT TaskExecDbCreateView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	return m_spProtectionJudgement->CheckViewCanAdd(sheetStId);
}

PCWSTR TaskExecDbCreateView::GetTag()
{
	return __X("dbSheet.createView");
}

// ================== TaskExecDbCopyView ==================
TaskExecDbCopyView::TaskExecDbCopyView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbCopyView::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	WebStr name = param.field_str("name");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetViews(sheetStId, &spDbSheetViews);
	if(!spDbSheetViews)
		return E_FAIL;

	HRESULT hr = spDbSheetViews->IsValidViewName(name);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
		return hr;

	ks_wstring nameStr(name);
	int nameLength = nameStr.length();
	if (hr == E_DBSHEET_VIEW_NAME_CONFLICT)
	{
		int postfix = 2;
		do
		{
			ks_wstring strPostFix;
			strPostFix.Format(__X("%d"), postfix);
			if (strPostFix.length() >= MAX_SHEET_NAME_CCH)
			{
				return E_FAIL;
			}
			if (strPostFix.length() + nameLength > MAX_SHEET_NAME_CCH)
			{
				nameStr.replace(MAX_SHEET_NAME_CCH - strPostFix.length(),
					nameLength - (MAX_SHEET_NAME_CCH - strPostFix.length()),
					strPostFix);
				ASSERT(nameStr.length() == MAX_SHEET_NAME_CCH);
			}
			else
			{
				nameStr.replace(nameLength, strPostFix.length(), strPostFix);
				ASSERT(nameStr.length() <= MAX_SHEET_NAME_CCH);
			}
			postfix++;
		} while (E_DBSHEET_VIEW_NAME_CONFLICT == spDbSheetViews->IsValidViewName(nameStr.c_str()));
		name = nameStr.c_str();
	}

	ks_stdptr<IDBSheetView> spDbSheetView;
	spDbSheetViews->GetItemById(viewId, &spDbSheetView);
	if (spDbSheetView == nullptr)
		return E_FAIL;
	ks_stdptr<IDBSheetView> spNewDbSheetView;
	hr = spDbSheetViews->CreateView(spDbSheetView->GetType(), name, false, Et_DBSheetViewUse_ForDb, &spNewDbSheetView);
	if (FAILED(hr))
		return hr;
	hr = spDbSheetView->CopyTo(spNewDbSheetView, TRUE, TRUE);
	if (FAILED(hr))
		return hr;
	EtDbId newViewId = spNewDbSheetView->GetId();
	if (param.has("pos"))
	{
		DbIdPostion tar;
		GetDBPos(param.get_s("pos"), tar);
		hr = spDbSheetViews->Move(newViewId, tar);
	}

	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral) 
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		AddEtDbId(resObj, "viewId", newViewId);
	}
	return hr;
}

HRESULT TaskExecDbCopyView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	return m_spProtectionJudgement->CheckViewCanAdd(sheetStId);
}

PCWSTR TaskExecDbCopyView::GetTag()
{
	return __X("dbSheet.copyView");
}

// ================== TaskExecDbDeleteView ==================
TaskExecDbDeleteView::TaskExecDbDeleteView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDeleteView::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetViews(sheetStId, &spDbSheetViews);
	if(!spDbSheetViews)
		return E_FAIL;

	ks_stdptr<IDBSheetView> spDbSheetView;
	HRESULT hr = spDbSheetViews->GetItemById(viewId, &spDbSheetView);
	if (FAILED(hr))
		return E_FAIL;
	
	EtDbIdx viewIdx = spDbSheetViews->GetViewIdx(viewId, spDbSheetView->GetUseType());
	hr = spDbSheetViews->DelItem(viewId);
	if (FAILED(hr))
		return hr;

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (SUCCEEDED(hr) && peripheral)
	{
		ks_wstring viewName = spDbSheetView->GetName();
		binary_wo::VarObj resObj = peripheral->resOthers();
		resObj.add_field_str("cmdName", GetTag());
		EtDbIdStr buf;
		m_pDbCtx->EncodeEtDbId(viewId, &buf);
		resObj.add_field_str("viewId", buf);
		resObj.add_field_str("viewName", viewName.c_str());
		resObj.add_field_str("affectedBy", ctx->getUser()->userID());
		resObj.add_field_uint32("sheetStId", sheetStId);
		binary_wo::VarObj resObjSelf = peripheral->resSelf();
		resObjSelf.add_field_str("cmdName", GetTag());
		resObjSelf.add_field_uint32("viewIdx", viewIdx);
		resObjSelf.add_field_str("viewId", buf);
		resObjSelf.add_field_uint32("sheetStId", sheetStId);
	}

	return S_OK;
}

HRESULT TaskExecDbDeleteView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckViewCanRemove(sheetStId, viewId);
}

PCWSTR TaskExecDbDeleteView::GetTag()
{
	return __X("dbSheet.deleteView");
}

// ================== TaskExecDbRenameView ==================
TaskExecDbRenameView::TaskExecDbRenameView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRenameView::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	
	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	WebStr name = param.field_str("name");
	ks_wstring viewName(name);
	HRESULT hr = DbSheet::GetValidViewName(spDbSheetViews, name, viewName);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
		return hr;

	return spDbSheetView->SetName(viewName.c_str());
}

HRESULT TaskExecDbRenameView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	return m_spProtectionJudgement->CheckViewCanEditNameAndDes(sheetStId);
}

PCWSTR TaskExecDbRenameView::GetTag()
{
	return __X("dbSheet.renameView");
}

// ================== TaskExecDbSetViewDescription ==================
TaskExecDbSetViewDescription::TaskExecDbSetViewDescription(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetViewDescription::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	return spDbSheetView->SetDescription(param.field_str("description"));
}

HRESULT TaskExecDbSetViewDescription::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	return m_spProtectionJudgement->CheckViewCanEditNameAndDes(sheetStId);
}

PCWSTR TaskExecDbSetViewDescription::GetTag()
{
	return __X("dbSheet.setViewDescription");	
}

// ================== TaskExecDbSetViewNotice ==================
TaskExecDbSetViewNotice::TaskExecDbSetViewNotice(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetViewNotice::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	return spDbSheetView->SetNotice(param.field_str("notice"));
}

HRESULT TaskExecDbSetViewNotice::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	return m_spProtectionJudgement->CheckViewCanEditNameAndDes(sheetStId);
}

PCWSTR TaskExecDbSetViewNotice::GetTag()
{
	return __X("dbSheet.setViewNotice");	
}

// ================== TaskExecDbSetPersonalView ==================
TaskExecDbSetPersonalView::TaskExecDbSetPersonalView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetPersonalView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	bool isPersonal = param.field_bool("isPersonal");
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = spDbSheetView->SetPersonalView(isPersonal, pCtx->getUser()->userID());
	if (SUCCEEDED(hr) && isPersonal)
	{
		if (EtTaskPeripheral* peripheral = GetPeripheral())
		{
			binary_wo::VarObj resObj = peripheral->resOthers();
			resObj.add_field_str("cmdName", GetTag());
			EtDbIdStr buf;
			m_pDbCtx->EncodeEtDbId(viewId, &buf);
			resObj.add_field_str("viewId", buf);
			resObj.add_field_str("affectedBy", pCtx->getUser()->userID());
			resObj.add_field_uint32("sheetStId", sheetStId);
		}
	}
	return hr;
}

HRESULT TaskExecDbSetPersonalView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetPersonalView(sheetStId, viewId);
}

PCWSTR TaskExecDbSetPersonalView::GetTag()
{
	return __X("dbSheet.setPersonalView");
}

// ================== TaskExecDbSetStatistic ==================
TaskExecDbSetStatistic::TaskExecDbSetStatistic(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetStatistic::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
	ET_DBSheet_StatisticOption option = DBSSO_null;
	VS(m_pEncodeDecoder->DecodeStatisticOption(param.field_str("statisticOptions"), &option));
	HRESULT hr = spDbSheetView->GetMutableStatisticOptions()->SetStatistic(fldId, option);
	return hr;
}

HRESULT TaskExecDbSetStatistic::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetStatisticOption(sheetStId, viewId);
}

PCWSTR TaskExecDbSetStatistic::GetTag()
{
	return __X("dbSheet.setStatistic");
}

// ================== TaskExecDbRemoveStatistic ==================
TaskExecDbRemoveStatistic::TaskExecDbRemoveStatistic(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRemoveStatistic::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
	HRESULT hr = spDbSheetView->GetMutableStatisticOptions()->RemoveStatistic(fldId);
	return hr;
}

HRESULT TaskExecDbRemoveStatistic::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanRemoveStatisticOption(sheetStId, viewId);
}

PCWSTR TaskExecDbRemoveStatistic::GetTag()
{
	return __X("dbSheet.removeStatistic");
}

// ================== TaskExecDbSetSheetDescription ==================
TaskExecDbSetSheetDescription::TaskExecDbSetSheetDescription(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetSheetDescription::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");

	HRESULT hr =  m_spProtectionJudgement->CheckSheetCanEditProp(sheetStId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<ISheet> spSheet;
	hr = m_commonHelper.GetSheet(sheetStId, &spSheet);
	if (FAILED(hr))
		return hr;

	PCWSTR desc = param.field_str("description");
	if (spSheet->IsDbSheet())
	{
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		VS(DbSheet::GetDBSheetOp(spSheet.get(), &spDbSheetOp));
		return spDbSheetOp->SetSheetDescription(desc);
	}
	if (spSheet->IsDbDashBoardSheet())
	{
		ks_stdptr<IDBDashBoardDataOp> spDbDashBoardOp;
		VS(DbSheet::GetDBDashBoardOp(spSheet.get(), &spDbDashBoardOp));
		return spDbDashBoardOp->SetSheetDescription(desc);
	}
	return E_FAIL;
}

HRESULT TaskExecDbSetSheetDescription::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbSetSheetDescription::GetTag()
{
	return __X("dbSheet.setSheetDescription");
}

// ================== TaskExecDbSetSheetIcon ==================
TaskExecDbSetSheetIcon::TaskExecDbSetSheetIcon(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetSheetIcon::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");

	HRESULT hr =  m_spProtectionJudgement->CheckSheetCanEditProp(sheetStId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<ISheet> spSheet;
	hr = m_commonHelper.GetSheet(sheetStId, &spSheet);
	if (FAILED(hr))
		return hr;

	PCWSTR icon = param.field_str("icon");

	if (spSheet->IsDbSheet())
	{
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		VS(DbSheet::GetDBSheetOp(spSheet.get(), &spDbSheetOp));
		hr = spDbSheetOp->SetSheetIcon(icon);
	}
	else if (spSheet->IsDbDashBoardSheet())
	{
		ks_stdptr<IDBDashBoardDataOp> spDbDashBoardOp;
		VS(DbSheet::GetDBDashBoardOp(spSheet.get(), &spDbDashBoardOp));
		hr = spDbDashBoardOp->SetSheetIcon(icon);
	}
	else if (spSheet->IsFpSheet())
	{
		ks_stdptr<IFPSheetData> spFpSheetData;
		VS(spSheet->GetExtDataItem(edSheetFpData, (IUnknown**)&spFpSheetData));
		hr = spFpSheetData->SetIcon(param.field_str("icon"));
	}
	return hr;
}

HRESULT TaskExecDbSetSheetIcon::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbSetSheetIcon::GetTag()
{
	return __X("dbSheet.setSheetIcon");
}

// ================== TaskExecDbMoveView ==================
TaskExecDbMoveView::TaskExecDbMoveView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbMoveView::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	HRESULT hr = S_OK;
	binary_wo::VarObj param = cmd->cast().get("param");

	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	
	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	DbIdPostion tar;
	GetDBPos(param.get_s("pos"), tar);
	return spDbSheetViews->Move(viewId, tar);
}

HRESULT TaskExecDbMoveView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanMoveView(sheetStId, viewId);
}

PCWSTR TaskExecDbMoveView::GetTag()
{
	return __X("dbSheet.moveView");
}

// ================== TaskExecDbSetUngroupedPositionOption ==================
TaskExecDbSetUngroupedPositionOption::TaskExecDbSetUngroupedPositionOption(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetUngroupedPositionOption::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetView_Kanban> spDbSheetView_Kanban = spDbSheetView;
	if (spDbSheetView_Kanban == nullptr)
		return E_INVALIDARG;
	ET_DBSheet_PositionOption option = DBSP_last;
	VS(m_pEncodeDecoder->DecodePositionOption(param.field_str("ungroupedPositionOption"), &option));
	HRESULT hr = spDbSheetView_Kanban->SetUngroupedPositionOption(option);
	if (FAILED(hr))
		return hr;

	hr = spDbSheetView_Kanban->MoveUngroupedPos(option);
	
	return hr;
}

HRESULT TaskExecDbSetUngroupedPositionOption::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetUngroupedPositionOption(sheetStId, viewId);
}

PCWSTR TaskExecDbSetUngroupedPositionOption::GetTag()
{
	return __X("dbSheet.setUngroupedPositionOption");
}


// ================== TaskExecDbAddGroupCondition ==================
TaskExecDbAddGroupCondition::TaskExecDbAddGroupCondition(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb) {}

HRESULT TaskExecDbAddGroupCondition::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if (spDbSheetView == nullptr)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	ks_stdptr<IDBRecordsOrderCondition> spCondition;
	HRESULT hr = pMgr->AddGroupCondition(fldId, &spCondition);
	if (FAILED(hr))
		return hr;

	KDBGroupUnit unit = DBGU_Text;
	if (param.has("unit"))
		m_pEncodeDecoder->DecodeGroupUnit(param.field_str("unit"), &unit);
	IDbFieldsManager* pFieldsMgr = spDbSheetView->GetFieldsManager();
	ks_stdptr<IDbField> spField;
	pFieldsMgr->GetField(fldId, &spField);
	spField->AdjustGroupUnit(unit);
	spCondition->SetUnit(unit);
	
	return S_OK;
}

HRESULT TaskExecDbAddGroupCondition::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanGroupRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbAddGroupCondition::GetTag()
{
	return __X("dbSheet.addGroupCondition");
}


// ================== TaskExecDbRemoveGroupCondition ==================
TaskExecDbRemoveGroupCondition::TaskExecDbRemoveGroupCondition(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRemoveGroupCondition::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	HRESULT hr = pMgr->RemoveGroupCondition(fldId);

	return hr;
}

HRESULT TaskExecDbRemoveGroupCondition::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanGroupRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbRemoveGroupCondition::GetTag()
{
	return __X("dbSheet.removeGroupCondition");
}

// ================== TaskExecDbSetGroupPriority ==================
TaskExecDbSetGroupPriority::TaskExecDbSetGroupPriority(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetGroupPriority::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();

	binary_wo::VarObj ids = param.get("priority");
	std::vector<EtDbId> vecId;
	for(int32 i = 0, cnt = ids.arrayLength(); i < cnt; ++i)
	{
		binary_wo::VarObj id = ids.at(i);
		vecId.push_back(GetEtDbId(id));
	}

	pMgr->ModifyGroupPriority(vecId.data(), vecId.size());
	return S_OK;
}

HRESULT TaskExecDbSetGroupPriority::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanGroupRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetGroupPriority::GetTag()
{
	return __X("dbSheet.setGroupPriority");
}

// ================== TaskExecDbChangeGroupField ==================
TaskExecDbChangeGroupField::TaskExecDbChangeGroupField(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb) {}

HRESULT TaskExecDbChangeGroupField::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fieldIdOld = GetEtDbId(param, "fieldIdOld");
	EtDbId fieldIdNew = GetEtDbId(param, "fieldIdNew");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if (spDbSheetView == nullptr)
		return E_DBSHEET_VIEW_NOT_FOUND;

	KDBGroupUnit unit = DBGU_Text;
	if (param.has("unit"))
		m_pEncodeDecoder->DecodeGroupUnit(param.field_str("unit"), &unit);
	IDbFieldsManager* pFieldsMgr = spDbSheetView->GetFieldsManager();
	ks_stdptr<IDbField> spField;
	pFieldsMgr->GetField(fieldIdNew, &spField);
	if (spField == nullptr)
		return E_DBSHEET_FIELD_NOT_FOUND;
	spField->AdjustGroupUnit(unit);
	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	HRESULT hr = E_FAIL;
	for (UINT i = 0, cnt = pMgr->GetGroupConditionCount(); i < cnt; ++i)
	{
		IDBRecordsOrderCondition* cond = pMgr->GetGroupCondition(i);
		if (cond->GetKeyFieldId() == fieldIdOld)
		{
			hr = cond->SetKeyFieldId(fieldIdNew);
			if(FAILED(hr))
				return E_FAIL;
			hr = cond->SetUnit(unit);
			break;
		}
	}
	if (spDbSheetView->GetType() == et_DBSheetView_Kanban)
		spDbSheetView->SetGroupFullyDirty();

	return hr;
}

HRESULT TaskExecDbChangeGroupField::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanGroupRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbChangeGroupField::GetTag()
{
	return __X("dbSheet.changeGroupField");
}

// ================== TaskExecDbSetGroupAscending ==================
TaskExecDbSetGroupAscending::TaskExecDbSetGroupAscending(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetGroupAscending::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	if (spDbSheetView->GetType() == et_DBSheetView_Kanban)
		return E_INVALIDARG;

	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	HRESULT hr = E_FAIL;
	for(UINT i = 0, cnt = pMgr->GetGroupConditionCount(); i < cnt; ++i)
	{
		IDBRecordsOrderCondition* p = pMgr->GetGroupCondition(i);
		if(p->GetKeyFieldId() == fldId)
		{
			hr = p->SetAscending(alg::bool2BOOL(param.field_bool("isAscending")));
			break;
		}
	}

	return hr;
}

HRESULT TaskExecDbSetGroupAscending::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanGroupRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetGroupAscending::GetTag()
{
	return __X("dbSheet.setGroupAscending");
}

// ================== TaskExecDbAddSortCondition ==================
TaskExecDbAddSortCondition::TaskExecDbAddSortCondition(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbAddSortCondition::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	ks_stdptr<IDBRecordsOrderCondition> spCondition;
	HRESULT hr = pMgr->AddSortCondition(fldId, &spCondition);
	if(FAILED(hr))
		return hr;

	if (param.has("isAscending"))
		hr = spCondition->SetAscending(alg::bool2BOOL( param.field_bool("isAscending")));
	
	return hr;
}

HRESULT TaskExecDbAddSortCondition::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSortRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbAddSortCondition::GetTag()
{
	return __X("dbSheet.addSortCondition");
}

// ================== TaskExecDbSetAutoSort ==================
TaskExecDbSetAutoSort::TaskExecDbSetAutoSort(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetAutoSort::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	bool isAuto = param.field_bool("isAuto");
	HRESULT hr = pMgr->SetAutoSort(alg::bool2BOOL(isAuto));

	return hr;
}

HRESULT TaskExecDbSetAutoSort::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSortRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetAutoSort::GetTag()
{
	return __X("dbSheet.setAutoSort");
}

// ================== TaskExecDbExecSort ==================
TaskExecDbExecSort::TaskExecDbExecSort(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbExecSort::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	const IDBRecordsOrderManager* pMgr = spDbSheetView->GetConstOrderManager();
	if(pMgr->GetSortConditionCount() == 0)
		return E_FAIL;
	// 这里的本质是调用view关于[更新]的底层实现. 只不过, 原先的设计中, view直接调用"更新"方法, 强制地执行全部更新. 从含义上说, view的SetDirty()方法很接近 ExecSort, 但更合适的是增设一个 OrderManager 的底层实现 ExecSort(), 来执行"Exec Sort".
	pMgr->ToggleForceSort();
	return S_OK;
}

HRESULT TaskExecDbExecSort::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSortRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbExecSort::GetTag()
{
	return __X("dbSheet.execSort");
}

// ================== TaskExecDbRemoveSortCondition ==================
TaskExecDbRemoveSortCondition::TaskExecDbRemoveSortCondition(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRemoveSortCondition::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = spDbSheetView->GetMutableOrderManager()->RemoveSortCondition(fldId);

	return S_OK;
}

HRESULT TaskExecDbRemoveSortCondition::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSortRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbRemoveSortCondition::GetTag()
{
	return __X("dbSheet.removeSortCondition");
}

// ================== TaskExecDbSetSortPriority ==================
TaskExecDbSetSortPriority::TaskExecDbSetSortPriority(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetSortPriority::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();

	binary_wo::VarObj ids = param.get("priority");
	std::vector<EtDbId> vecId;
	for(int32 i = 0, cnt = ids.arrayLength(); i < cnt; ++i)
	{
		binary_wo::VarObj id = ids.at(i);
		vecId.push_back(GetEtDbId(id));
	}

	pMgr->ModifySortPriority(vecId.data(), vecId.size());
	return S_OK;
}

HRESULT TaskExecDbSetSortPriority::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSortRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetSortPriority::GetTag()
{
	return __X("dbSheet.setSortPriority");
}

// ================== TaskExecDbChangeSortField ==================
TaskExecDbChangeSortField::TaskExecDbChangeSortField(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbChangeSortField::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fieldIdOld = GetEtDbId(param, "fieldIdOld");
	EtDbId fieldIdNew = GetEtDbId(param, "fieldIdNew");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	HRESULT hr = E_FAIL;
	for(UINT i = 0, cnt = pMgr->GetSortConditionCount(); i < cnt; ++i)
	{
		IDBRecordsOrderCondition* p = pMgr->GetSortCondition(i);
		if(p->GetKeyFieldId() == fieldIdOld)
		{
			hr = p->SetKeyFieldId(fieldIdNew);
			break;
		}
	}

	return S_OK;
}

HRESULT TaskExecDbChangeSortField::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSortRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbChangeSortField::GetTag()
{
	return __X("dbSheet.changeSortField");
}

// ================== TaskExecDbSetSortAscending ==================
TaskExecDbSetSortAscending::TaskExecDbSetSortAscending(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetSortAscending::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;


	IDBRecordsOrderManager* pMgr = spDbSheetView->GetMutableOrderManager();
	HRESULT hr = E_FAIL;
	for(UINT i = 0, cnt = pMgr->GetSortConditionCount(); i < cnt; ++i)
	{
		IDBRecordsOrderCondition* p = pMgr->GetSortCondition(i);
		if(p->GetKeyFieldId() == fldId)
		{
			hr = p->SetAscending(alg::bool2BOOL(param.field_bool("isAscending")));
			break;
		}
	}

	return hr;
}

HRESULT TaskExecDbSetSortAscending::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSortRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetSortAscending::GetTag()
{
	return __X("dbSheet.setSortAscending");
}

// ================== TaskExecDbSetFiltersOp ==================
TaskExecDbSetFiltersOp::TaskExecDbSetFiltersOp(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetFiltersOp::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	
	KDbFilterOpType op = DBFOT_And;
	hr = m_pEncodeDecoder->DecodeKDbFilterOpType(param.field_str("op"), &op);
	if(FAILED(hr))
		return hr;
	
	if (op != DBFOT_And && op != DBFOT_Or)
		return E_INVALIDARG;

	IDbFilter* pFilter = spDbSheetView->GetMutableFilter();
	hr = pFilter->SetOperator(op);

	return hr;
}

HRESULT TaskExecDbSetFiltersOp::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}

PCWSTR TaskExecDbSetFiltersOp::GetTag()
{
	return __X("dbSheet.setFiltersOp");
}

void PrintFilterLog(IDbFilter* pFilter, binary_wo::VarObj& param)
{
	if(!pFilter)
		return;

	if(param.has("filterId"))
		WOLOG_INFO << "param: " << "filterId:" <<  krt::fromUtf16(param.field_str("filterId")) << ",fieldId:" <<  krt::fromUtf16(param.field_str("fieldId"));
	else
		WOLOG_INFO << "param: " << "no filterId," << "fieldId:" << krt::fromUtf16(param.field_str("fieldId"));
	
	UINT filterCnt = pFilter->GetFiltersCount();
	QString filterInfo = QString("filter count:%1, multiFilter:%2 ").arg(filterCnt).arg(pFilter->GetEnableMultiFieldFilter());
	for(int i = 0; i < filterCnt; i++)
	{
		ks_stdptr<IDbFieldFilter> spFieldFilter;
		HRESULT hr = pFilter->GetFilter(i, &spFieldFilter);
		if(FAILED(hr))
			continue;
		filterInfo.append(QString("[idx:%1, filterId:%2, fieldId:%3], ").arg(i).arg(spFieldFilter->GetFilterId()).arg(spFieldFilter->GetFieldId()));
	}
	WOLOG_INFO << filterInfo;
}

// ================== TaskExecDbSetFilterCriteria ==================
TaskExecDbSetFilterCriteria::TaskExecDbSetFilterCriteria(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetFilterCriteria::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;
	
	IDbFilter* pFilter = spDbSheetView->GetMutableFilter();
	ks_stdptr<IDbFieldFilter> spFieldFilter;
	if (param.has("fieldId"))
	{
		EtDbId fieldId = GetEtDbId(param, "fieldId");
		if(param.has("filterId"))
			hr = pFilter->GetFieldFilter(GetEtDbId(param, "filterId"), &spFieldFilter);
		else
			hr = pFilter->GetFieldFilterByFieldId(fieldId, &spFieldFilter);

		PrintFilterLog(pFilter, param);
		
		if (FAILED(hr))
			return hr;
		
		if (param.has("fieldIdNew"))
			hr = spFieldFilter->SetFieldId(GetEtDbId(param, "fieldIdNew"));
		
		//在字段头上修改筛选条件需要将筛选面板上同一字段的多个筛选条件合并成一个筛选
		if(param.has("mergeFieldFilter") && param.field_bool("mergeFieldFilter"))
			DbSheet::RemoveFilterByFieldId(pFilter, spFieldFilter->GetFieldId(), spFieldFilter);
	}
	else
	{
		if(param.has("multiFieldFilter") && param.field_bool("multiFieldFilter"))
			pFilter->SetEnableMultiFieldFilter(true);

		hr = pFilter->AddFilter(GetEtDbId(param, "fieldIdNew"), &spFieldFilter);
	}
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDbFilterCriteria> spCriteria;
	hr = CreateDbFilterCriteria(param.get("criteria"), spDbSheetView->GetSheetOp()->GetBook(), &spCriteria);
	if (FAILED(hr))
		return hr;

	hr = spFieldFilter->SetCriteria(spCriteria);

	return hr;
}

HRESULT TaskExecDbSetFilterCriteria::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}

PCWSTR TaskExecDbSetFilterCriteria::GetTag()
{
	return __X("dbSheet.setFilterCriteria");
}

// ================== TaskExecDbRemoveFilter ==================
TaskExecDbRemoveFilter::TaskExecDbRemoveFilter(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRemoveFilter::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDbFilter* pFilter = spDbSheetView->GetMutableFilter();
	ks_stdptr<IDbFieldFilter> spFieldFilter;
	if(param.has("filterId"))
		hr = pFilter->GetFieldFilter(GetEtDbId(param, "filterId"), &spFieldFilter);
	else
		hr = pFilter->GetFieldFilter(GetEtDbId(param, "fieldId"), &spFieldFilter);

	PrintFilterLog(pFilter, param);
	
	if (FAILED(hr))
		return hr;
	hr = spFieldFilter->Remove();
	return hr;
}

HRESULT TaskExecDbRemoveFilter::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}

PCWSTR TaskExecDbRemoveFilter::GetTag()
{
	return __X("dbSheet.removeFilter");
}

// ================== TaskExecDbClearFilters ==================
TaskExecDbClearFilters::TaskExecDbClearFilters(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbClearFilters::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDbFilter* pFilter = spDbSheetView->GetMutableFilter();
	if(param.has("fieldId"))
	{
		EtDbId fieldId = GetEtDbId(param, "fieldId");
		return DbSheet::RemoveFilterByFieldId(pFilter, fieldId);
	}

	return pFilter->ClearFilter();
}

HRESULT TaskExecDbClearFilters::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}

PCWSTR TaskExecDbClearFilters::GetTag()
{
	return __X("dbSheet.clearFilters");
}

// ================== TaskExecDbSetGridFrozenCount ==================
TaskExecDbSetGridFrozenCount::TaskExecDbSetGridFrozenCount(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetGridFrozenCount::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	int count = param.field_uint32("count");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetView_Grid> spDbSheetView_grid = spDbSheetView;
	if (spDbSheetView_grid == nullptr)
		return E_INVALIDARG;

	return spDbSheetView_grid->SetFrozenCols(count);
}

HRESULT TaskExecDbSetGridFrozenCount::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetGridFrozenCount(sheetStId, viewId);
}

PCWSTR TaskExecDbSetGridFrozenCount::GetTag()
{
	return __X("dbSheet.setGridFrozenCols");
}


// ================== TaskExecDbRangePaste ==================

TaskExecDbRangePaste::TaskExecDbRangePaste(KEtWorkbook* wwb) : ETDbSheetTaskClassBase(wwb) {}

namespace
{
bool getStream(WebMimeHelper& wmh, const QString& priorityFormat, ks_stdptr<IStream>& pFileStream)
{
	WebMimeData* pMimeData = wmh.resolveMimeData();
	if (!pMimeData)
		return false;

	binary_wo::VarObj& param = wmh.objParam;
	QString mimeType = priorityFormat;
	if (mimeType.isEmpty())
		mimeType = QString::fromUtf8(pMimeData->mimeType);
	QString path = QString::fromUtf8(pMimeData->path);
	if (!path.isEmpty())
	{
		HRESULT res = _XCreateStreamOnFile(krt::utf16(path), STGM_G_READ, &pFileStream);
		if (res == S_OK && NULL != pFileStream)
		{
			return true;
		}
	}
	return false;
}

bool getMimeData(WebMimeHelper& wmh, ks_stdptr<IStream>& pFileStream)
{
	WebMimeHelper wmhDo = wmh;
	wmhDo.strField = "ksDocClipboardId";

	binary_wo::VarObj param = wmh.objParam;
	QString priorityFormat;
	if (param.has("priorityFormat"))
	{
		QString clipboardID = QString::fromUtf16(param.field_str(wmhDo.strField));

		//为了解决后面扩展多种延迟拷贝的格式后ID与文件1对多的问题，
		//将ID与Type拼接一下生成一个组合ID，确保mimeDataID-->file 关系为1对1。
		priorityFormat = QString::fromUtf16(param.field_str("priorityFormat"));
		clipboardID.append("-");
		clipboardID.append(priorityFormat);
		param.add_field_str("ksDocClipboardExId", krt::utf16(clipboardID));
		wmhDo.strField = "ksDocClipboardExId";
	}
	return getStream(wmhDo, priorityFormat, pFileStream);
}

UINT getCodePage(ks_stdptr<IStream>& pStream)
{
	unsigned char data[3] = {0};
	pStream->Read(&data, sizeof(data), NULL);

	// 有 bom 的以 bom 为准
	if (data[0] == 0xff && data[1] == 0xfe)
		return KFC_CP_Unicode;

	if (data[0] == 0xfe && data[1] == 0xff)
		return KFC_CP_UnicodeBE;
		
	if (data[0] == 0xef && data[1] == 0xbb && data[2] == 0xbf)
		return KFC_CP_UTF8;

	return KFC_CP_UTF8; // WPP, WPS 默认都是这个格式
}

HRESULT getWStringFromMime(WebMimeHelper& wmh, ks_wstring& str)
{
	ks_stdptr<IStream> pStream;
	if (!getMimeData(wmh, pStream))
		return E_FAIL;

	UINT codePage = getCodePage(pStream);

	LARGE_INTEGER pos = { 0 };
	pStream->Seek(pos, SEEK_SET, NULL);

	IUnicodeCacheReader *pReader = _XNewUnicoderCacheReader(pStream, codePage); // utf16:1200 utf8:65001
	WCHAR ch = 0;
	while(true)
	{
		ch = pReader->readChar();
		if (ch == __Xc('\0'))
			break;
		str.push_back(ch);
	}

	return S_OK;
}

}// namespace

HRESULT TaskExecDbRangePaste::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	IBook* pBook = ctx->getBook();
	IEtEventTracking* pEtEventTracking = nullptr;
	if (pBook)
		pEtEventTracking = pBook->LeakWorkspace()->GetEventTracking();
	etcore::TimeStat timeStat([&pEtEventTracking](unsigned int time){
		if (pEtEventTracking)
		{
			KComVariant varTime(time);
			pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_PASTE_TOTAL_TIME_CONSUMING, varTime);
			pEtEventTracking->SendInfoAfterPaste();
		}
	});

    binary_wo::VarObj param = cmd->cast().get("param");
    // 1.判断是否云粘贴，是则读取所有内容
    if (param.has("ksDocClipboardId") && param.has("priorityFormat"))
    {
        PCWSTR priorityFormat = param.field_str("priorityFormat");
        if (xstrcmp(priorityFormat, __X("db_cp_json_format")) == 0)
        {
            WebMimeHelper wmh;
            wmh.ctx = ctx, wmh.cmd = cmd;
            wmh.objParam = param;
            wmh.setHasExtra();
            ks_wstring mimeData;
            HRESULT hr = getWStringFromMime(wmh, mimeData);
            if (SUCCEEDED(hr) && mimeData.size() > 0)
            {
				try
				{
					hr = PasteJsonData(cmd, ctx, mimeData);
				}
				catch (const et_exception& ex)
				{
					hr = ex.get_result();
				}
                return hr;
            }
        }
    }
    
    // 2.如果不存在json格式的mimeData，则认为是外部数据粘贴
    // 由于前端界面按块请求数据显示，前端复制生成的txt可能不完整，粘贴时存在部分数据丢失问题的可能性
    if (!param.has("text/plain"))
        return E_INVALIDARG;

	HRESULT hr = S_OK;
	try
	{
		hr = PasteText(cmd, ctx);
	}
	catch (const et_exception& ex)
	{
		hr = ex.get_result();
	}
    return hr;
}

HRESULT TaskExecDbRangePaste::PasteJsonData(KwCommand* cmd, KEtRevisionContext* ctx, ks_wstring& mimeData)
{
	HRESULT hr = S_OK;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	IEtCollectInfo* pEtCollectInfo = nullptr;
	IEtEventTracking* pEtEventTracking = nullptr;
	if (pBook)
	{
		pEtEventTracking = pBook->LeakWorkspace()->GetEventTracking();
		ASSERT(pEtEventTracking);
		pEtCollectInfo = pEtEventTracking->GetCollectInfo();
	}
	InfoHlp info(hr, pEtCollectInfo);

	IPasteProgressNotify* pProgressNotify = pBook->GetPasteProgressNotify();
	PasteScopeManger pasteScopeManger(pProgressNotify);
	pProgressNotify->SetPasteType(DB_JSON_PASTE);

    binary_wo::VarObj param = cmd->cast().get("param");
    UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (!spDbSheetView)
	{
		hr = E_DBSHEET_VIEW_NOT_FOUND;
		return hr;
	}
    
    // 选择range
	ks_stdptr<IDBSheetRange> spTarRg;
	hr = GetDBRange(spDbSheetView, param.get("tarRg"), false, false, &spTarRg);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetOp> spDbSheetOp = spDbSheetView->GetSheetOp();
	
	//本身db的底表有可能是无记录
	BOOL bCurNoRecords = spDbSheetOp->IsNoRecordsMode() 
		&& spTarRg->GetRecordCnt() < 1 
		&& spDbSheetView->GetAllRecords()->Count() < 1 //若当前有记录，只是筛选导致把所有记录都隐藏掉了，跟产品同学孙怡确认过这种情况不属于真正意义上的无记录。
		&& spTarRg->GetFieldCnt() >= 1;
 
	if (!bCurNoRecords)
	{
		if (spTarRg->GetRecordCnt() < 1 || spTarRg->GetFieldCnt() < 1)
		{
			hr = E_INVALIDARG;
			return hr;
		}
	}

 	bool containPrimaryField = spTarRg->GetFieldIdx(spDbSheetView->GetFieldsManager()->GetPrimaryField()) != INV_EtDbIdx;
	DBJsonDataHelper copyDataParser;
    if(containPrimaryField && spDbSheetView->IsEnableRecordsRelation())
    {
		copyDataParser.setHandleSubRecord(true);
    }
    QByteArray data = krt::fromUtf16(mimeData.c_str()).toUtf8();
    QJsonDocument jd_data = QJsonDocument::fromJson(data);
    
    hr = copyDataParser.parse(jd_data);
    if (FAILED(hr))
        return hr;
   
    int srcRowCnt = copyDataParser.GetRowCount();
    int srcColCnt = copyDataParser.GetColCount();

    BMP_PTR bmp = spDbSheetView->GetSheetOp()->GetBook()->GetBMP();
	EtDbIdx tarBegRow = !bCurNoRecords ? spDbSheetView->GetVisibleRecords()->Id2Idx(spTarRg->GetRecordId(0)) : 0;
	srcRowCnt = MIN(srcRowCnt, bmp->cntRows - tarBegRow);

    EtDbIdx tarBegCol = spDbSheetView->GetVisibleFields()->Id2Idx(spTarRg->GetFieldId(0));
	srcColCnt = MIN(srcColCnt, bmp->cntCols - tarBegCol);

    // 复制单个单元格到多行多列时，可以自动填充
	binary_wo::VarObj varOption = param.get("option");
	bool autoFillingRange = false;
	if (varOption.has("autoFillingRange"))
		autoFillingRange = varOption.field_bool("autoFillingRange");
	
	// 兼容旧命令，旧命令没有autoFillingRange参数
	size_t fillColCnt = srcColCnt;
	size_t fillRowCnt = srcRowCnt;
	if(!bCurNoRecords)
	{
		//无记录不需要fill填充。
		if (autoFillingRange && spTarRg->GetRecordCnt() % srcRowCnt == 0 && spTarRg->GetFieldCnt() % srcColCnt == 0 ||
			!autoFillingRange && srcRowCnt == 1 && srcColCnt == 1)
		{
			autoFillingRange = true;
			fillRowCnt = spTarRg->GetRecordCnt();
			fillColCnt = spTarRg->GetFieldCnt();
		}
	}
	if(pEtCollectInfo)
	{
		QString Countsrc = QString::number(srcRowCnt * srcColCnt);
		QString Countdes = QString::number(fillRowCnt * fillColCnt);
		KComVariant varCopyCount(krt::utf16(Countsrc));
		KComVariant varPasteCount(krt::utf16(Countdes));
		pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_AND_CUT_CELL_COUNT, varCopyCount);
		pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::PASTE_CELL_COUNT, varPasteCount);
	}

	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
	std::unordered_set<EtDbId> newRecordIds;
	hr = CheckAndSavePasteInfo(spDbSheetView, spTarRg, param, ctx, fillRowCnt, fillColCnt, bCurNoRecords, newRecordIds);
	if (FAILED(hr))
		return hr;

	{
	// 准备数据
	PasteStageScope pasteStageScope(pProgressNotify, PASTE_STAGE_PREPARE_DATA, true);
	pProgressNotify->UpdateProgressUI();
	etcore::TimeStat timeStat([&pEtEventTracking](unsigned int time){
		if (pEtEventTracking)
		{
			KComVariant varTime(time);
			pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::PREPARE_DATA_TIME, varTime);
		}
	});
	hr = CheckAndExpandRange(varOption, spDbSheetView, spTarRg, fillRowCnt, fillColCnt, newRecordIds);
	if (FAILED(hr))
		return hr;
	}

	bool bAlterDueDateAutomation = false;
	ks_stdptr<IDbAutomations> m_spAutomations;
	pBook->GetExtDataItem(edBookDbAutomations, (IUnknown**)&m_spAutomations);
	IDbAutomationConfig *pConfig = m_spAutomations->GetConfig();
	bAlterDueDateAutomation = DbSheet::HasDueDateAutomation(spDbSheetOp, spTarRg);
	bool bExceededMaxBatchRecords = false;
	if (bAlterDueDateAutomation && fillRowCnt > pConfig->GetMaxBatchRecords())
	{
		bExceededMaxBatchRecords = true;
		fillRowCnt = pConfig->GetMaxBatchRecords();
	}

	//检查复制的数据源txtResult如果要粘贴落地后，是否满足ValueUnique的属性，若否，则拦截。
	EtDbId dupFldId = INV_EtDbId;
    auto op = [&copyDataParser](UINT r, UINT c, ks_wstring& res) -> void
    {
        copyDataParser.GetString(r, c, res);
    };
	bool bValidValueUnique = CheckDatasourceMatchUniqueToPasteSpecField(spDbSheetView, autoFillingRange, srcRowCnt, srcColCnt,
		fillRowCnt, fillColCnt, spTarRg, op, dupFldId);
	if(!bValidValueUnique)
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if(peripheral)
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			binary_wo::VarObj notMatchUniqueInfoObj = resObj.add_field_struct("notMatchUniqueInfo");
			notMatchUniqueInfoObj.add_field_int32("sheetStId", sheetStId);
			AddEtDbId(notMatchUniqueInfoObj, "fieldId", dupFldId);
		}
		return E_DBSHEET_DATASOURCE_NOT_MATCH_UNIQUE_TO_PASTE_SPEC_FIELD;
	}
	else
	{
		//粘贴过去先把带有ValueUnique的属性的目标区域给clear
		hr = ClearTarRgWithUniqueProp(spDbSheetView, srcRowCnt, srcColCnt, spTarRg);
		if (FAILED(hr))
		{
			return hr;
		}
	}
	
	DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TryModifyField2Multiple);
	DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);

    // 修改新记录可能会因为字段权限原因导致失败，这里放开新记录的权限
    DbSheet::DisableDbRecordProtectScope disableDbRecordProtectScope(m_spProtectionJudgement, &newRecordIds);
	// 批量 submitchange
	DbSheet::DbtBatchSubmitOptimizer batchSubmitter(spDbSheetOp.get());

	int pasteFailCnt = 0;
	bool bAlterAutoField = false;
    EtDbId uniquekLimitFieldId;
    std::vector<DBSheetCommonHelper::CellValueItem> vCellValueItems;
    std::vector<DBSheetCommonHelper::CellValueItem> vInvalidPhotoItems;
    std::vector<DBSheetCommonHelper::CellValueItem> vInvalidScanItems;
    hr = m_commonHelper.RangePaste(param, &copyDataParser, spTarRg, autoFillingRange, srcRowCnt, srcColCnt, fillRowCnt, fillColCnt, pasteFailCnt, bAlterAutoField, uniquekLimitFieldId, vCellValueItems, vInvalidPhotoItems, vInvalidScanItems);

	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral)
	{
		if(SUCCEEDED(hr))
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			resObj.add_field_str("cmdName", GetTag());
			resObj.add_field_int32("pasteFailCnt", pasteFailCnt);
			bool needSeralPasteFailContents = vCellValueItems.size() > 0 || vInvalidPhotoItems.size() > 0 || vInvalidScanItems.size() > 0;
			if (needSeralPasteFailContents)
			{
				KDbSetCellValInvalidInfoCollector collector(sheetStId);
				for (const DBSheetCommonHelper::CellValueItem& item : vCellValueItems)
				{
					collector.InsertNormalCellItem(item.recId, item.fldId, item.value, E_DBSHEET_VALUE_NOT_UNIQUE_IN_FIELD);
				}
                for (const DBSheetCommonHelper::CellValueItem& item : vInvalidPhotoItems)
                {
                    collector.InsertNormalCellItem(item.recId, item.fldId, item.value, E_DBSHEET_PASTECELL_DST_ONLY_ALLOW_UPLOAD_BY_PHONE);
                }
                for (const DBSheetCommonHelper::CellValueItem& item : vInvalidScanItems)
                {
                    collector.InsertNormalCellItem(item.recId, item.fldId, item.value, E_DBSHEET_PASTECELL_DST_ONLY_ALLOW_SCAN_BY_CAMERA);
                }
				binary_wo::VarObj invalidCellsObj = resObj.add_field_struct("invalidCellsInfo");
				collector.SerialContent(invalidCellsObj);
			}
			resObj.add_field_bool("alterAutoField", bAlterAutoField);
			resObj.add_field_bool("alterDueDateAutomation", bAlterDueDateAutomation);
			resObj.add_field_bool("exceededMaxBatchRecords", bExceededMaxBatchRecords);
		}
        else if(hr == E_DBSHEET_PASTECELL_ERROR_EXCEEDED_LIMITS)
        {
            binary_wo::VarObj resObj = peripheral->resSelf();
			binary_wo::VarObj notMatchUniqueInfoObj = resObj.add_field_struct("notMatchUniqueInfo");
			notMatchUniqueInfoObj.add_field_int32("sheetStId", sheetStId);
			notMatchUniqueInfoObj.add_field_int32("errCellLimitCnt", 1000);
			AddEtDbId(notMatchUniqueInfoObj, "fieldId", uniquekLimitFieldId);
        }
	}
	return hr;
}

HRESULT TaskExecDbRangePaste::PasteText(KwCommand* cmd, KEtRevisionContext* ctx)
{
	HRESULT hr = S_OK;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	IEtCollectInfo* pEtCollectInfo = nullptr;
	IEtEventTracking* pEtEventTracking = nullptr;
	if (pBook)
	{
		pEtEventTracking = pBook->LeakWorkspace()->GetEventTracking();
		ASSERT(pEtEventTracking);
		pEtCollectInfo = pEtEventTracking->GetCollectInfo();
	}
	InfoHlp info(hr, pEtCollectInfo);

	IPasteProgressNotify* pProgressNotify = pBook->GetPasteProgressNotify();
	PasteScopeManger pasteScopeManger(pProgressNotify);
	pProgressNotify->SetPasteType(DB_PLAIN_PASTE);

	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (!spDbSheetView)
	{
		hr = E_DBSHEET_VIEW_NOT_FOUND;
		return hr;
	}

	// 选择range
	ks_stdptr<IDBSheetRange> spTarRg;
	hr = GetDBRange(spDbSheetView, param.get("tarRg"), false, false, &spTarRg);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
	if (FAILED(hr))
		return hr;
	
	//本身db的底表有可能是无记录
	BOOL bCurNoRecords = spDbSheetOp->IsNoRecordsMode() 
		&& spTarRg->GetRecordCnt() < 1 
		&& spDbSheetView->GetAllRecords()->Count() < 1 //若当前有记录，只是筛选导致把所有记录都隐藏掉了，跟产品同学孙怡确认过这种情况不属于真正意义上的无记录。
		&& spTarRg->GetFieldCnt() >= 1;

	if (!bCurNoRecords)
	{
		if (spTarRg->GetRecordCnt() < 1 || spTarRg->GetFieldCnt() < 1)
		{
			hr = E_INVALIDARG;
			return hr;
		}
	}

	// 解释粘帖txt内容
    if (!param.has("text/plain"))
	{
		hr = E_INVALIDARG;
        return hr;
	}
	WebStr txt = param.field_str("text/plain");
	TxtParserResult txtResult;
	if (!TxtParser().parse(txt, txtResult))
	{
		hr = E_INVALIDARG;
		return hr;
	}

	// 解释出来的行列数量
	int srcRowCnt = txtResult.GetRowCount();

	BMP_PTR bmp = spDbSheetView->GetSheetOp()->GetBook()->GetBMP();
	EtDbIdx tarBegRow = !bCurNoRecords ? spDbSheetView->GetVisibleRecords()->Id2Idx(spTarRg->GetRecordId(0)) : 0;
	srcRowCnt = MIN(srcRowCnt, bmp->cntRows - tarBegRow);
	int srcColCnt = txtResult.GetColCount();

	EtDbIdx tarBegCol = spDbSheetView->GetVisibleFields()->Id2Idx(spTarRg->GetFieldId(0));
	srcColCnt = MIN(srcColCnt, bmp->cntCols - tarBegCol);

	// 复制单个单元格到多行多列时，可以自动填充
	binary_wo::VarObj varOption = param.get("option");
	bool autoFillingRange = false;
	if (varOption.has("autoFillingRange"))
		autoFillingRange = varOption.field_bool("autoFillingRange");
	
	// 兼容旧命令，旧命令没有autoFillingRange参数
	size_t fillColCnt = srcColCnt;
	size_t fillRowCnt = srcRowCnt;
	if(!bCurNoRecords)
	{
		//无记录不需要fill填充。
		if (autoFillingRange && spTarRg->GetRecordCnt() % srcRowCnt == 0 && spTarRg->GetFieldCnt() % srcColCnt == 0 ||
			!autoFillingRange && srcRowCnt == 1 && srcColCnt == 1)
		{
			autoFillingRange = true;
			fillRowCnt = spTarRg->GetRecordCnt();
			fillColCnt = spTarRg->GetFieldCnt();
		}
	}

	// 埋点收集
	if(pEtCollectInfo)
	{
		QString Countsrc = QString::number(srcRowCnt * srcColCnt);
		QString Countdes = QString::number(fillRowCnt * fillColCnt);
		KComVariant varCopyCount(krt::utf16(Countsrc));
		KComVariant varPasteCount(krt::utf16(Countdes));
		pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::COPY_AND_CUT_CELL_COUNT, varCopyCount);
		pEtCollectInfo->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::PASTE_CELL_COUNT, varPasteCount);
	}

	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
	std::unordered_set<EtDbId> newRecordIds;
	hr = CheckAndSavePasteInfo(spDbSheetView, spTarRg, param, ctx, fillRowCnt, fillColCnt, bCurNoRecords, newRecordIds);
	if (FAILED(hr))
		return hr;

	{
	// 准备数据
	PasteStageScope pasteStageScope(pProgressNotify, PASTE_STAGE_PREPARE_DATA, true);
	pProgressNotify->UpdateProgressUI();
	etcore::TimeStat timeStat([&pEtEventTracking](unsigned int time){
		if (pEtEventTracking)
		{
			KComVariant varTime(time);
			pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::PREPARE_DATA_TIME, varTime);
		}
	});
	hr = CheckAndExpandRange(varOption, spDbSheetView, spTarRg, fillRowCnt, fillColCnt, newRecordIds);
	if (FAILED(hr))
		return hr;
	}

	bool bAlterDueDateAutomation = false;
	IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
	ks_stdptr<IDbAutomations> m_spAutomations;
	pBook->GetExtDataItem(edBookDbAutomations, (IUnknown**)&m_spAutomations);
	IDbAutomationConfig *pConfig = m_spAutomations->GetConfig();
	
	bAlterDueDateAutomation = DbSheet::HasDueDateAutomation(spDbSheetOp, spTarRg);
	bool bExceededMaxBatchRecords = false;
	if (bAlterDueDateAutomation && fillRowCnt > pConfig->GetMaxBatchRecords())
	{
		bExceededMaxBatchRecords = true;
		fillRowCnt = pConfig->GetMaxBatchRecords();
	}

	//检查复制的数据源txtResult如果要粘贴落地后，是否满足ValueUnique的属性，若否，则拦截。
	EtDbId dupFldId = INV_EtDbId;
    auto op = [&txtResult](UINT r, UINT c, ks_wstring& res) -> void
    {
        res = txtResult.GetString(r, c);
    };
	bool bValidValueUnique = CheckDatasourceMatchUniqueToPasteSpecField(spDbSheetView, autoFillingRange, srcRowCnt, srcColCnt,
		fillRowCnt, fillColCnt, spTarRg, op, dupFldId);
	if(!bValidValueUnique)
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if(peripheral)
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			binary_wo::VarObj notMatchUniqueInfoObj = resObj.add_field_struct("notMatchUniqueInfo");
			notMatchUniqueInfoObj.add_field_int32("sheetStId", sheetStId);
			AddEtDbId(notMatchUniqueInfoObj, "fieldId", dupFldId);
		}
		hr = E_DBSHEET_DATASOURCE_NOT_MATCH_UNIQUE_TO_PASTE_SPEC_FIELD;
		return hr;
	}
	else
	{
		//粘贴过去先把带有ValueUnique的属性的目标区域给clear
		hr = ClearTarRgWithUniqueProp(spDbSheetView, srcRowCnt, srcColCnt, spTarRg);
		if (FAILED(hr))
		{
			return hr;
		}
			
	}

	// 设置值
	int pasteFailCnt = 0;
	bool bAlterAutoField = false;
	DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TryModifyField2Multiple);
	DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);

	// 批量 submitchange
	DbSheet::DbtBatchSubmitOptimizer batchSubmitter(spDbSheetOp.get());
	KDbSetCellValInvalidInfoCollector collector(sheetStId);
	{
	PasteStageScope pasteStageScope(pProgressNotify, PASTE_STAGE_CELLS, true);
	pProgressNotify->SetSubTaskTotal(fillColCnt * fillRowCnt);

	etcore::TimeStat timeStat([&pEtEventTracking](unsigned int time){
		if (pEtEventTracking)
		{
			KComVariant varTime(time);
			pEtEventTracking->GetCollectInfo()->CollectBookInfo(et_event_tracking::BOOK_FIELD_NAME::CELL_DATA_TIME, varTime);
		}
	});

	for (auto c = 0; c < fillColCnt; ++c)
	{
		EtDbId fldId = spTarRg->GetFieldId(c);
		if (spDbSheetView->GetAllFields()->Id2Idx(fldId) == INV_EtDbIdx)
			continue;

		ks_stdptr<IDbField> spField;
		hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;

        if (spField->IsSyncField())
		{
			pasteFailCnt += fillRowCnt;
			continue;
		}

		ET_DbSheet_FieldType fieldType = spField->GetType();

		//prepare field val token cache
		std::unique_ptr<KDbFieldValCacheGuard> spDbFieldValCacheGuard;
		if(srcRowCnt >= 2)
		{	
			WOLOG_INFO << "[TaskExecDbRangePaste] PrepareValToken to optimize!";
			spDbFieldValCacheGuard = std::move(std::make_unique<KDbFieldValCacheGuard>(fldId, pFieldsMgr));
		}

		IBook* pBook = ctx->getBook();
        // 修改新记录可能会因为字段权限原因导致失败，这里放开新记录的权限
        DbSheet::DisableDbRecordProtectScope disableDbRecordProtectScope(m_spProtectionJudgement, &newRecordIds);
		for (auto r = 0; r < fillRowCnt; ++r)
		{
			pProgressNotify->CheckBreakAndUpdateProgress();

			EtDbId recId = spTarRg->GetRecordId(r);
			if (spDbSheetView->GetAllRecords()->Id2Idx(recId) == INV_EtDbIdx)
				continue;

            // 文本粘贴时，目标字段是”图片和附件“、”富文本“时，不在提示”不支持”附件和富文本“字段粘贴“。因为内部格式已经可以支持，避免造成用户误解。
            if (fieldType == Et_DbSheetField_Attachment || fieldType == Et_DbSheetField_Note)
            {
                pasteFailCnt += 1;
                continue;
            }
            if (fieldType == Et_DbSheetField_BarCode)
            {
				ks_stdptr<IDbField_BarCode> spBarCodeField = spField;
				if (spBarCodeField->GetOnlyScanByCamera())
				{
					pasteFailCnt++;
					continue;
				}
            }
			// todo: 类似TokenPtrMc那样, 用union管理val/mta, 并使用一个外部标识来决定行为, 或许是更好的设计
			ks_wstring val = __X("");
			if (autoFillingRange)
				val = txtResult.GetString(r % srcRowCnt, c % srcColCnt);
			else
				val = txtResult.GetString(r, c);

			hr = spDbSheetOp->PasteCell(recId, fldId, val.c_str());
			if (hr != S_OK)
			{
				if (hr == E_DBSHEET_ALTER_AUTO_FIELD)
				{
					bAlterAutoField = true;
				}
				else if(hr == E_DBSHEET_VALUE_NOT_UNIQUE_IN_FIELD)
				{
					collector.InsertNormalCellItem(recId, fldId, val, hr);
					if(collector.GetSize() > 1000)
					{
						EtTaskPeripheral* peripheral = GetPeripheral();
						if(peripheral)
						{
							binary_wo::VarObj resObj = peripheral->resSelf();
							binary_wo::VarObj notMatchUniqueInfoObj = resObj.add_field_struct("notMatchUniqueInfo");
							notMatchUniqueInfoObj.add_field_int32("sheetStId", sheetStId);
							notMatchUniqueInfoObj.add_field_int32("errCellLimitCnt", 1000);
							AddEtDbId(notMatchUniqueInfoObj, "fieldId", fldId);
						}
						//超过1000条重复数据时，拒绝此次整体粘贴命令
						hr = E_DBSHEET_PASTECELL_ERROR_EXCEEDED_LIMITS;
						return hr;
					}
					
				}

				// 改为继续粘贴其他cell
				pasteFailCnt += 1;
				continue;
			}
		}
	}
	}

	if (srcRowCnt == 0 || srcColCnt == 0)
		pasteFailCnt += 1;

	hr = S_OK; // 重置单元格粘贴的结果为“成功”。

	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral)
	{
		if(SUCCEEDED(hr))
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			resObj.add_field_str("cmdName", GetTag());
			resObj.add_field_int32("pasteFailCnt", pasteFailCnt);
			if(!collector.IsEmpty())
			{
				binary_wo::VarObj invalidCellsObj = resObj.add_field_struct("invalidCellsInfo");
				collector.SerialContent(invalidCellsObj);
			}
			
			resObj.add_field_bool("alterAutoField", bAlterAutoField);
			resObj.add_field_bool("alterDueDateAutomation", bAlterDueDateAutomation);
			resObj.add_field_bool("exceededMaxBatchRecords", bExceededMaxBatchRecords);
		}
	}
	return hr;
}

HRESULT TaskExecDbRangePaste::CheckAndGetGroupRange(ks_stdptr<IDBSheetView> &spDbSheetView,
													EtDbIdx tarBegRow, size_t srcRowCnt,
													ks_stdptr<IDBSheetRange> &spTarRg,
													ks_stdptr<IDBSheetRange> &groupAddRange)
{
	// 是否有分组
	const IDBRecordsOrderManager* pMgr = spDbSheetView->GetConstOrderManager();
	if (pMgr->GetGroupConditionCount() > 0)
	{
		// 分组里插入多少行
		UINT groupNeedInsertCnt = 0;
		HRESULT hr = pMgr->GetInsertCountInGroup(tarBegRow, srcRowCnt, &groupNeedInsertCnt);
		if (FAILED(hr))
			return hr;

		if (groupNeedInsertCnt > 0)
		{
			ks_stdptr<IWebhookManager> spWebhookMgr;
			m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
			IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
			KDisableDbWebhook disableDbWebhook(pRange, DbWebhookMask_changeCellData);
			ks_stdptr<IDbCollector> spDbCollector;
    		m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
    		KDisableDbCollector disableDbCollector(spDbCollector, DCM_Webhook, CDOM_UpdateCells);

			hr = spDbSheetView->InsertRecords(groupNeedInsertCnt, &groupAddRange);
			if (FAILED(hr))
				return hr;

			// 获取所在分组所有条件列的值
			DbSheet::DisableDbRecordProtectScope disableDbSheetRangeRecordProtectScope(m_spProtectionJudgement, groupAddRange);
			int condCnt = pMgr->GetGroupConditionCount();
			EtDbId srcRecId = spTarRg->GetRecordId(0);//在具体某个分组下粘贴数据，说明此值一定分组字段下是有值的，也就是说不存在所谓的无记录。
			for (int i = 0; i < condCnt; ++i)
			{
				const IDBRecordsOrderCondition *cond = pMgr->GetGroupCondition(i);
				EtDbId fldId = cond->GetKeyFieldId();

				for (UINT32 idx = 0, count = groupAddRange->GetRecordCnt(); idx < count; idx++)
				{
					EtDbId recId = groupAddRange->GetRecordId(idx);
					// todo: 粘贴与反向关联也有关，以后需要补上
					hr = spDbSheetView->GetSheetOp()->CopyCell(srcRecId, fldId, recId);
					if (FAILED(hr))
						return hr;
				}
			}
		}
	}
	return S_OK;
}

HRESULT TaskExecDbRangePaste::CheckAndSavePasteInfo(ks_stdptr<IDBSheetView> &spDbSheetView,
													ks_stdptr<IDBSheetRange> &spTarRg,
											binary_wo::VarObj& param, KEtRevisionContext* ctx, 
											size_t srcRowCnt, size_t srcColCnt, 
											BOOL bCurNoRecords, std::unordered_set<EtDbId>& newRecordIds)
{
	WebName lastPasteInfoKey = "lastPasteInfo";
	WebName lastSrcRowCntKey = "lastSrcRowCnt";
	WebName lastSrcColCntKey = "lastSrcColCnt";

	bool lastPasteExist = param.has(lastPasteInfoKey);
	if (lastPasteExist)
	{
		auto info = param.get(lastPasteInfoKey);
		UINT lastSrcRowCnt = info.field_uint32(lastSrcRowCntKey);
		UINT lastSrcColCnt = info.field_uint32(lastSrcColCntKey);

		return (srcRowCnt != lastSrcRowCnt || srcColCnt != lastSrcColCnt) ? E_INVALIDARG : S_OK;
	} 

	binary_wo::VarObj objInfo = param.add_field_struct(lastPasteInfoKey);
	objInfo.add_field_uint32(lastSrcRowCntKey, srcRowCnt);
	objInfo.add_field_uint32(lastSrcColCntKey, srcColCnt);

	ExpandType recordsExpandMod = ExpandType_Modify;
	HRESULT hr = getExpandType(param.get("option").field_str("recExpMod"), recordsExpandMod);
	if (FAILED(hr))
		return hr;

	EtDbIdx tarBegRow = !bCurNoRecords ? spDbSheetView->GetVisibleRecords()->Id2Idx(spTarRg->GetRecordId(0)) : 0;
	EtDbIdx tarBegCol = spDbSheetView->GetVisibleFields()->Id2Idx(spTarRg->GetFieldId(0));

	if (recordsExpandMod != ExpandType_Specific && tarBegRow == INV_EtDbIdx || tarBegCol == INV_EtDbIdx)
	{
		return E_DBSHEET_RANGE_NOT_FOUND;
	}

	ks_stdptr<IDBSheetRange> groupAddRange;
	if (recordsExpandMod == ExpandType_Expand)
	{
		DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TryModifyField2Multiple);
		DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);
		hr = CheckAndGetGroupRange(spDbSheetView, tarBegRow, srcRowCnt, spTarRg, groupAddRange);
		if (dbLinkParam.adFieldEdit)
		{
			EtTaskPeripheral* peripheral = GetPeripheral();
			if (peripheral) 
			{
				VarObj resObj = peripheral->resSelf();
				resObj.add_field_bool("unable2ModifyLinkField", true);
			}
		}
		if (FAILED(hr))
			return hr;
		if (FAILED(dbLinkParam.hr))
			return dbLinkParam.hr;
	}

	ks_stdptr<IDBSheetRange> spTarRgBlk = spTarRg;
	spTarRg.clear();
	spDbSheetView->CreateDBSheetRange(&spTarRg);

	// 可见view最大行列数
	EtDbIdx dstMaxRowCount = spDbSheetView->GetVisibleRecords()->Count();
	EtDbIdx dstMaxColCount = spDbSheetView->GetVisibleFields()->Count();
	if (groupAddRange != nullptr)
	{
		UINT32 groupNewRecCount = groupAddRange->GetRecordCnt();
		UINT32 groupOrgRecCount = srcRowCnt - groupNewRecCount;
		UINT32 groupOrgRecEndIdx = tarBegRow + groupOrgRecCount;
		for (UINT32 idx = tarBegRow; idx < groupOrgRecEndIdx; ++idx)
		{
			spTarRg->AddRecordId(spDbSheetView->GetVisibleRecords()->IdAt(idx));
		}
		for (UINT32 idx = 0; idx < groupNewRecCount; ++idx)
		{
			EtDbId newRecordId = groupAddRange->GetRecordId(idx);
			spTarRg->AddRecordId(newRecordId);
			newRecordIds.insert(newRecordId);
		}
	}
	else if (recordsExpandMod == ExpandType_Specific)
	{
		for (EtDbIdx i = 0, cnt = MIN(srcRowCnt, spTarRgBlk->GetRecordCnt()); i < cnt; ++i)
		{
			spTarRg->AddRecordId(spTarRgBlk->GetRecordId(i));
		}
	}
	else
	{
		for (UINT32 idx = tarBegRow, cnt = MIN(tarBegRow + srcRowCnt, dstMaxRowCount); idx < cnt; ++idx) 
		{
			spTarRg->AddRecordId(spDbSheetView->GetVisibleRecords()->IdAt(idx));
		}
	}

	for (UINT32 idx = tarBegCol, cnt = MIN(tarBegCol + srcColCnt, dstMaxColCount); idx < cnt; ++idx)
	{
		spTarRg->AddFieldId(spDbSheetView->GetVisibleFields()->IdAt(idx));
	}

	VS(writeDbRange(spDbSheetView, spTarRg, param.get("tarRg")));
	ctx->setIsRealTransform(true);
	return S_OK;
}

HRESULT TaskExecDbRangePaste::CheckAndExpandRange(
	binary_wo::VarObj objOpt,
	ks_stdptr<IDBSheetView>& spDbSheetView, 
	ks_stdptr<IDBSheetRange>& spTarRg,
	size_t& srcRowCnt, 
	size_t& srcColCnt,
    std::unordered_set<EtDbId>& newRecordIds)
{
	HRESULT hr = S_OK;
	ExpandType fieldsExpandMod = ExpandType_Modify;
	hr = getExpandType(objOpt.field_str("fldExpMod"), fieldsExpandMod);
	if(FAILED(hr)) 
		return hr;
	if (fieldsExpandMod == ExpandType_Specific)
		return E_INVALIDARG;

	ExpandType recordsExpandMod = ExpandType_Modify;
	hr = getExpandType(objOpt.field_str("recExpMod"), recordsExpandMod);
	if(FAILED(hr)) 
		return hr;

	bool needExpRec = srcRowCnt > spTarRg->GetRecordCnt() && (spDbSheetView->GetSheetOp()->GetSheetSyncType() != DbSheet_St_DB);
	bool needExpFld = srcColCnt > spTarRg->GetFieldCnt();
	if (needExpRec && recordsExpandMod == ExpandType_Modify && needExpFld && fieldsExpandMod == ExpandType_Modify)
	{
		return E_DBSHEET_PASTE_NEED_EXPAND;
	}
	if(needExpRec && recordsExpandMod == ExpandType_Modify)
	{
		return E_DBSHEET_PASTE_NEED_EXPAND_RECORDS;
	} 
	if (needExpFld && fieldsExpandMod == ExpandType_Modify)
	{
		return E_DBSHEET_PASTE_NEED_EXPAND_FIELDS;
	}

	if (needExpRec && recordsExpandMod == ExpandType_Expand)
	{
		auto insertRowCount = srcRowCnt - spTarRg->GetRecordCnt();
		ks_stdptr<IDBSheetRange> insertRowRg;

		DbReversedLinkParam dbLinkParam(DbReversedLinkParam::TryModifyField2Multiple);
		DbSheet::DbLinkHlp dbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), dbLinkParam);
		ks_stdptr<IWebhookManager> spWebhookMgr;
		m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
		IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
		KDisableDbWebhook disableDbWebhook(pRange, DbWebhookMask_changeCellData);
		ks_stdptr<IDbCollector> spDbCollector;
    	m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
    	KDisableDbCollector disableDbCollector(spDbCollector, DCM_Webhook, CDOM_UpdateCells);
		hr = spDbSheetView->InsertRecords(insertRowCount, &insertRowRg);
		if (dbLinkParam.adFieldEdit)
		{
			EtTaskPeripheral* peripheral = GetPeripheral();
			if (peripheral) 
			{
				VarObj resObj = peripheral->resSelf();
				resObj.add_field_bool("unable2ModifyLinkField", true);
			}
		}
		if (FAILED(hr))
			return hr;
		if (FAILED(dbLinkParam.hr))
			return dbLinkParam.hr;

		for (UINT32 i = 0, cnt = insertRowRg->GetRecordCnt(); i < cnt; ++i)
		{
            EtDbId recordId = insertRowRg->GetRecordId(i);
			spTarRg->AddRecordId(recordId);
            newRecordIds.insert(recordId);
		}
	}
	if (needExpFld && fieldsExpandMod == ExpandType_Expand)
	{
		auto insertColCount = srcColCnt - spTarRg->GetFieldCnt();
		ks_stdptr<IWebhookManager> spWebhookMgr;
		m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edBookWebhookManager, (IUnknown**)&spWebhookMgr);
		IDbWebhookChangedRange* pRange = spWebhookMgr->GetDbWebhookChangedRange();
		KDisableDbWebhook disableDbWebhook(pRange, DbWebhookMask_updateField);
		ks_stdptr<IDbCollector> spDbCollector;
    	m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edBookDbCollector, (IUnknown**)&spDbCollector);
    	KDisableDbCollector disableDbCollector(spDbCollector, DCM_Webhook, CDOM_UpdateField);
		ks_stdptr<IDBSheetRange> insertColRg;
		hr = spDbSheetView->InsertFields(insertColCount, &insertColRg);
		if(FAILED(hr))
			return hr;
		IDbFieldsManager *pFieldsMgr = spDbSheetView->GetFieldsManager();
		const EtDbId* pIds = NULL;
		std::vector<PCWSTR> nameVec;
		PCWSTR defFldName = objOpt.field_str("defFldName");
		for (int i = 0; i < insertColRg->GetFieldCnt(); ++i)
		{
			nameVec.push_back(defFldName);
			EtDbId fieldId = insertColRg->GetFieldId(i);
			spTarRg->AddFieldId(fieldId);
		}
		UINT32 cnt = 0;
		insertColRg->GetFieldIds(&pIds, &cnt);
		pFieldsMgr->BatchSetFieldsName(pIds, nameVec.data(), cnt);
	}

	if (srcRowCnt > spTarRg->GetRecordCnt() && newRecordIds.empty())
		srcRowCnt = spTarRg->GetRecordCnt();
	return S_OK;
}

bool TaskExecDbRangePaste::CheckDatasourceMatchUniqueToPasteSpecField(IDBSheetView* pDbSheetView,  bool autoFillingRange,
	size_t srcRowCnt, size_t srcColCnt,
	size_t fillRowCnt, size_t fillColCnt,
	IDBSheetRange* pTarRg, GetStringFunc func, EtDbId& dupFldId)
{
	IDbFieldsManager *pFieldsMgr = pDbSheetView->GetFieldsManager();
	for (auto c = 0; c < fillColCnt; ++c)
	{
		EtDbId fldId = pTarRg->GetFieldId(c);
		if (pDbSheetView->GetAllFields()->Id2Idx(fldId) == INV_EtDbIdx)
			continue;
		
		ks_stdptr<IDbField> spField;
		HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;
		if(!spField->IsValueUnique())
			continue;
		if(fillRowCnt != srcRowCnt)
		{
			//说明有自重复
			dupFldId = fldId;
			return false;
		}
		GlobalSharedStringUnorderedSet colTxtSet;
		for (auto r = 0; r < fillRowCnt; ++r)
		{
			EtDbId recId = pTarRg->GetRecordId(r);
			if (pDbSheetView->GetAllRecords()->Id2Idx(recId) == INV_EtDbIdx)
				continue;
			ks_wstring val;
			if (autoFillingRange)
				func(r % srcRowCnt, c % srcColCnt, val);
			else
				func(r, c, val);
			ks_wstring checkVal = val;
			checkVal.Trim(__X(" "));//去除前后的空格
			if(checkVal.empty())
				continue;

			if(colTxtSet.find(GlobalSharedString(val.c_str())) != colTxtSet.end())
			{
				dupFldId = fldId;
				return false;
			}
			else
			{
				colTxtSet.insert(GlobalSharedString(val.c_str()));
			}
		}
	}
	return true;
}

HRESULT TaskExecDbRangePaste::ClearTarRgWithUniqueProp(IDBSheetView* pDbSheetView,
	size_t srcRowCnt, size_t srcColCnt,
	IDBSheetRange* pTarRg)
{
	//对粘贴范围(当前视图可见范围内)内带有ValueUnique属性的该列的单元格清空
	IDbFieldsManager *pFieldsMgr = pDbSheetView->GetFieldsManager();

	ks_stdptr<IDBSheetRange> spClearRg;
	pDbSheetView->CreateDBSheetRange(&spClearRg);

	for (auto c = 0; c < srcColCnt; ++c)
	{
		EtDbId fldId = pTarRg->GetFieldId(c);
		if (pDbSheetView->GetAllFields()->Id2Idx(fldId) == INV_EtDbIdx)
			continue;
		
		ks_stdptr<IDbField> spField;
		HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;
		if(!spField->IsValueUnique())
			continue;

		spClearRg->AddFieldId(fldId);
		for (auto r = 0; r < srcRowCnt; ++r)
		{
			EtDbId recId = pTarRg->GetRecordId(r);
			if (pDbSheetView->GetAllRecords()->Id2Idx(recId) == INV_EtDbIdx)
				continue;
			spClearRg->AddRecordId(recId);
		}

	}

	return pDbSheetView->Clear(spClearRg);
}

HRESULT TaskExecDbRangePaste::getExpandType(PCWSTR typeStr, ExpandType& type)
{
	if (xstrcmp(typeStr, __X("Skip")) == 0)
		type = ExpandType_Skip;
	else if (xstrcmp(typeStr, __X("Expand")) == 0)
		type = ExpandType_Expand;
	else if (xstrcmp(typeStr, __X("Modify")) == 0)
		type = ExpandType_Modify;
	else if (xstrcmp(typeStr, __X("Specific")) == 0)
		type = ExpandType_Specific;
	else
		return E_INVALIDARG;
	return S_OK;
}

PCWSTR TaskExecDbRangePaste::GetTag()
{
	return __X("dbSheet.rangePaste");
}

// ================== TaskExecDbUserGroupAddUsers ==================
TaskExecDbUserGroupAddUsers::TaskExecDbUserGroupAddUsers(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbUserGroupAddUsers::operator()(KwCommand* cmd, KEtRevisionContext* pAcpt)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	EtDbId groupId = GetEtDbId(param, "groupId");
	ks_stdptr<IDBUserGroup> spUserGroup;
	HRESULT hr = getUserGroup(groupId, &spUserGroup);
	if (FAILED(hr))
		return hr;

	binary_wo::VarObj usersIds = param.get("userIds");
	BOOL bMove = alg::bool2BOOL(param.field_bool("move"));
	for (int32 i = 0, cnt = usersIds.arrayLength_s(); i < cnt; ++i)
	{
		hr = spUserGroup->AddUser(usersIds.item_str(i), bMove);
		if (FAILED(hr))
			return hr;
	}
	return hr;
}

HRESULT TaskExecDbUserGroupAddUsers::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr = CheckUserCanChangeProtection(pCtx);
    if (S_OK == hr)
		return hr;

	return m_spProtectionJudgement->CheckCanManage();
}

PCWSTR TaskExecDbUserGroupAddUsers::GetTag()
{
	return __X("db.userGroupAddUsers");
}

// ================== TaskExecDbUserGroupRemoveUsers ==================

TaskExecDbUserGroupRemoveUsers::TaskExecDbUserGroupRemoveUsers(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbUserGroupRemoveUsers::operator()(KwCommand* cmd, KEtRevisionContext* pAcpt)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	EtDbId groupId = GetEtDbId(param, "groupId");
	ks_stdptr<IDBUserGroup> spUserGroup;
	HRESULT hr = getUserGroup(groupId, &spUserGroup);
	if (FAILED(hr))
		return hr;
	
	binary_wo::VarObj usersIds = param.get("userIds");
	for (int32 i = 0, cnt = usersIds.arrayLength_s(); i < cnt; ++i)
	{
		hr = spUserGroup->RemoveUser(usersIds.item_str(i));
		if (FAILED(hr))
			return hr;
	}
	return hr;
}

HRESULT TaskExecDbUserGroupRemoveUsers::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr = CheckUserCanChangeProtection(pCtx);
    if (S_OK == hr)
		return hr;

	return m_spProtectionJudgement->CheckCanManage();
}

PCWSTR TaskExecDbUserGroupRemoveUsers::GetTag()
{
	return __X("db.userGroupRemoveUsers");
}

// ================== TaskExecDbUserGroupSetProtection ==================
TaskExecDbUserGroupSetProtection::TaskExecDbUserGroupSetProtection(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbUserGroupSetProtection::operator()(KwCommand* cmd, KEtRevisionContext* pAcpt)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	EtDbId groupId = GetEtDbId(param, "groupId");
	ks_stdptr<IDBUserGroup> spUserGroup;
	HRESULT hr = getUserGroup(groupId, &spUserGroup);
	if (FAILED(hr))
		return hr;

	KDBProtectionType ptType = DBPT_Custom;
	PCWSTR strTp = param.field_str("protectionType");
	hr = m_pEncodeDecoder->DecodeKDBProtectionType(strTp, &ptType);
	if (FAILED(hr))
		return hr;
	
	ks_stdptr<IDBProtection> spPt;	
	spUserGroup->GetProtection(&spPt);
	hr = spPt->ResetByType(ptType);
	if (FAILED(hr))
		return hr;

	return hr;
}

HRESULT TaskExecDbUserGroupSetProtection::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr = CheckUserCanChangeProtection(pCtx);
    if (S_OK == hr)
		return hr;

	return m_spProtectionJudgement->CheckCanManage();
}

PCWSTR TaskExecDbUserGroupSetProtection::GetTag()
{
	return __X("db.userGroupSetProtection");
}


// ================== TaskExecDbProtectionSwitch ==================

TaskExecDbProtectionSwitch::TaskExecDbProtectionSwitch(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbProtectionSwitch::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	ks_stdptr<IUnknown> spUnknown;
	m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edDBUserGroups, &spUnknown);
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;

	binary_wo::VarObj param = cmd->cast().get("param");
	bool isOn = param.field_bool("isOn");
	HRESULT hr = spDBUserGroups->SetProtectOn(alg::bool2BOOL(isOn));
	if(FAILED(hr))
		return hr;

	QString connID = QString::fromUtf16(pCtx->getUser()->connID());
	bool isBookProtected = m_spProtectionJudgement->HasGroupProtection();
	updateProtectionInfoRetryAndAbort(pCtx, connID.toUtf8(), isBookProtected);
	return hr;
}

HRESULT TaskExecDbProtectionSwitch::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr = CheckUserCanChangeProtection(pCtx);
    if (S_OK != hr)
		hr = E_DBSHEET_AD_MANAGE;
	return hr;
}

PCWSTR TaskExecDbProtectionSwitch::GetTag()
{
	return __X("db.protectionSwitch");
}


// ================== TaskExecDbSetupProtection ==================

TaskExecDbSetupProtection::TaskExecDbSetupProtection(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbSetupProtection::operator()(KwCommand* cmd, KEtRevisionContext* pAcpt)
{
	ks_stdptr<IUnknown> spUnknown;
	m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edDBUserGroups, &spUnknown);
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	binary_wo::VarObj param = cmd->cast().get("param");
	binary_wo::VarObj groups = param.get("groups");

	HRESULT hr = spDBUserGroups->Clear();
	if(FAILED(hr))
		return hr;

	for (int32 i = 0, cnt = groups.arrayLength_s(); i < cnt; ++i)
	{
		binary_wo::VarObj group = groups.at(i);
		KDBProtectionType ptType = DBPT_Custom;
		PCWSTR strTp = group.field_str("protectionType");
		hr = m_pEncodeDecoder->DecodeKDBProtectionType(strTp, &ptType);
		if (FAILED(hr))
			return hr;

		ks_stdptr<IDBUserGroup> spGp;
		ks_stdptr<IDBProtection> spPt;
		spDBUserGroups->AddGroup(group.field_str("name"), &spGp);
		spGp->GetProtection(&spPt);
		hr = spPt->ResetByType(ptType);
		if (FAILED(hr))
			return hr;

		if (group.has("description"))
		{
			hr = spGp->SetDescription(group.field_str("description"));
			if (FAILED(hr))
				return hr;
		}

		if (group.field_bool("isDefault"))
		{
			hr = spGp->SetAsDefaultGroup();
			if (FAILED(hr))
				return hr;
		}

		binary_wo::VarObj usersIds = group.get("userIds");
		for (int32 i = 0, cnt = usersIds.arrayLength_s(); i < cnt; ++i)
		{
			hr = spGp->AddUser(usersIds.item_str(i), TRUE);
			if (FAILED(hr))
				return hr;
		}
	}

	return hr;
}

HRESULT TaskExecDbSetupProtection::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr = CheckUserCanChangeProtection(pCtx);
    if (S_OK != hr)
		hr = E_DBSHEET_AD_MANAGE;
	return hr;
}

PCWSTR TaskExecDbSetupProtection::GetTag()
{
	return __X("db.setupProtection");
}

TaskExecDbShareView::TaskExecDbShareView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT TaskExecDbShareView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");

	HRESULT hr = m_spProtectionJudgement->CheckCanShare(sheetStId);
	if (FAILED(hr))
		return hr;
 	
	return DbSheet::DoShareView(param, pCtx, m_pEncodeDecoder);
}

HRESULT TaskExecDbShareView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbShareView::GetTag()
{
	return __X("dbsheet.shareView");
}


TaskExecDbCancelShareView::TaskExecDbCancelShareView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT TaskExecDbCancelShareView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	HRESULT hr = m_spProtectionJudgement->CheckCanShare(sheetStId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBSharedLinkView* pSharedLink = spDbSheetView->GetSharedLink();
	if(pSharedLink == NULL)
		return E_DBSHEET_SHARED_NOT_EXIST;


	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	IBook* pBook = pCtx->getBook();
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	hr = spSharedLinkMgr->Remove(pSharedLink->Id());
	if(FAILED(hr))
		return hr;

	hr = spDbSheetView->ClearAllShareSettings();
	if(FAILED(hr))
		return hr;

	return hr;
}

HRESULT TaskExecDbCancelShareView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbCancelShareView::GetTag()
{
	return __X("dbsheet.cancelShareView");
}


TaskExecDbShareSheet::TaskExecDbShareSheet(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT TaskExecDbShareSheet::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	if (!param.has("sheetStId") || !param.has("sharedId"))
	{
		WOLOG_ERROR << "[TaskExecDbShareSheet] param error!";
		return E_FAIL;
	}

	UINT sheetStId = param.field_uint32("sheetStId");
	PCWSTR sharedId = param.field_str("sharedId");
	
	HRESULT hr = m_spProtectionJudgement->CheckCanShare(sheetStId);
	if (FAILED(hr))
		return hr;
	return ShareSheetHelper::ShareSheet(m_wwb, pCtx, sharedId, sheetStId);
}
HRESULT TaskExecDbShareSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbShareSheet::GetTag()
{
	return __X("dbsheet.shareSheet");
}

TaskExecDbCancelShareSheet::TaskExecDbCancelShareSheet(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT TaskExecDbCancelShareSheet::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");

	HRESULT hr = m_spProtectionJudgement->CheckCanShare(sheetStId);
	if (FAILED(hr))
		return hr;
	return ShareSheetHelper::CancelShareSheet(m_wwb, sheetStId);
}

PCWSTR TaskExecDbCancelShareSheet::GetTag()
{
	return __X("dbsheet.cancelShareSheet");
}

HRESULT TaskExecDbCancelShareSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

TaskExecDbModifyShareView::TaskExecDbModifyShareView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoOver);
}

HRESULT TaskExecDbModifyShareView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	HRESULT hr = m_spProtectionJudgement->CheckCanShare(sheetStId);
	if (FAILED(hr))
		return hr;
	
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	IDBSharedLinkView* pSharedLink = spDbSheetView->GetSharedLink();
	if(pSharedLink == NULL)
		return E_DBSHEET_SHARED_NOT_EXIST;

	if(param.has("visibleRecTp"))
	{
		KDbSharedCriteriaType visibleRecTp = DBSCT_All;
		hr = m_pEncodeDecoder->DecodeDbSharedCriteriaType(param.field_str("visibleRecTp"), &visibleRecTp);
		if (FAILED(hr))
			return hr;

		hr = pSharedLink->SetVisibleRecType(visibleRecTp);
		if (FAILED(hr))
			return hr;
	}
	
	if (param.has("editableRecTp"))
	{
		KDbSharedCriteriaType editableRecTp = DBSCT_All;
		hr = m_pEncodeDecoder->DecodeDbSharedCriteriaType(param.field_str("editableRecTp"), &editableRecTp);
		if (FAILED(hr))
			return hr;
		
		hr = pSharedLink->SetEditableRecType(editableRecTp);
		if (FAILED(hr))
			return hr;
	}
	
	if(param.has("removeableRecTp"))
	{
		KDbSharedCriteriaType removeableRecTp = DBSCT_All;
		hr = m_pEncodeDecoder->DecodeDbSharedCriteriaType(param.field_str("removeableRecTp"), &removeableRecTp);
		if (FAILED(hr))
			return hr;

		hr = pSharedLink->SetRemoveableRecType(removeableRecTp);
		if (FAILED(hr))
			return hr;
	}

	if (param.has("canAddRec"))
	{
        hr = pSharedLink->SetCanAddRecords(param.field_bool("canAddRec"));
		if (FAILED(hr))
			return hr;
	}

    if (param.has("editableFieldIds"))
    {
        VAR_OBJ_EXPECT_STRUCT(param, "editableFieldIds");
        binary_wo::VarObj editableFieldIdsObj = param.get_s("editableFieldIds");
        bool defaultVal = editableFieldIdsObj.field_bool("defaultVal");
        std::vector<EtDbId> diffIds;
        if (editableFieldIdsObj.has("diffIds"))
        {
            VAR_OBJ_EXPECT_ARRAY(editableFieldIdsObj, "diffIds");
            binary_wo::VarObj diffIdsObj = editableFieldIdsObj.get_s("diffIds");
            int cnt = diffIdsObj.arrayLength_s();
            diffIds.reserve(cnt);
            for (int i = 0; i < cnt; ++i)
            {
                EtDbId fieldId = GetEtDbId(diffIdsObj.at_s(i));
                diffIds.push_back(fieldId);
            }
        }
        pSharedLink->UpdateEditableFieldIds(alg::bool2BOOL(defaultVal), diffIds.data(), diffIds.size());
    }

	return hr;
}

HRESULT TaskExecDbModifyShareView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbModifyShareView::GetTag()
{
	return __X("dbsheet.modifyShareView");
}
// ================== TaskExecDbAddTemplateSheets ==================
TaskExecDbAddTemplateSheets::TaskExecDbAddTemplateSheets(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbAddTemplateSheets::operator()(KwCommand* cmd, KEtRevisionContext* pCtx) 
{
	// param可用来控制更多信息，例如模板的来源等
	binary_wo::VarObj param = cmd->cast().get("param");

	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = param, wmh.strField = "mimeDataID";
	QString path = wmh.resolvePath();
	PCWSTR fileName = krt::utf16(path);

	bool copyContent = true;
	if (param.has("isEmptyContent"))
		copyContent = param.field_bool("isEmptyContent");

	bool delAllSheet = false;
	if (param.has("deleteAllSheet"))
		delAllSheet = param.field_bool("deleteAllSheet");

	UINT sheetId = 0;
	if (param.has("deleteSheetStId"))
		sheetId = param.field_uint32("deleteSheetStId");

	if (sheetId != 0)
		delAllSheet = true;

	std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher> attachmentIdMap;
	attachmentIdMap[GlobalSharedString((PCWSTR)nullptr)] = GlobalSharedString((PCWSTR)nullptr);
	DbSheet::GetAttachmentIdMap(param, attachmentIdMap);

	// 保留新增sheets的第一个sheet的id供前端聚焦
	UINT activeSheetId = 0;
	AddTemplateSheetsHelper addTemplateHelper(m_wwb, pCtx, fileName, copyContent, delAllSheet, m_spProtectionJudgement, param);
	addTemplateHelper.SetClearAttachment(false);
	addTemplateHelper.SetAttachmentIdMap(&attachmentIdMap);
	HRESULT hr = addTemplateHelper.Exec(activeSheetId);
	if (FAILED(hr))
	{
		addTemplateHelper.rollback();
		return hr;
	}
	//导入完成，判断是否需要把view转成应用。
	UpdateAppVersionHelper::UpdateDbSheetToAppMgr updateDbSheetToAppMgr;
	hr = UpdateAppVersionHelper::UpdateAppVersion(m_wwb, pCtx, updateDbSheetToAppMgr, param);
	if (FAILED(hr))
		return hr;

	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral) 
	{
		binary_wo::VarObj resObj = peripheral->resSelf();

		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_uint32("activeSheetId", activeSheetId);
		if (addTemplateHelper.GetReachFpSheetLimit())
			resObj.add_field_bool("reachFpSheetLimit", true);
		if (addTemplateHelper.GetReachDashboardLimit())
			resObj.add_field_bool("reachDashboardLimit", true);
	}
	return S_OK;
}

HRESULT TaskExecDbAddTemplateSheets::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR TaskExecDbAddTemplateSheets::GetTag()
{
	return __X("dbSheet.addTemplateSheets");
}

// ================== TaskExecDbImportXlsxSheets ==================
TaskExecDbImportXlsxSheets::TaskExecDbImportXlsxSheets(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbImportXlsxSheets::operator()(KwCommand* cmd, KEtRevisionContext* pCtx) 
{
	VarObj param = cmd->cast().get("param");

	if (false == param.has("uploadid"))
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "No uploadid parameter!");
		return E_FAIL;
	}

	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = param, wmh.strField = "uploadid";
	QString path = wmh.resolvePath();

	if (path.isEmpty())
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "Can't parse uploadid!");
		return E_FAIL;
	}
	PCWSTR filePath = krt::utf16(path);

	if (false == param.has("xlsxImport"))
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "No xlsxImport parameter!");
		return E_FAIL;
	}
	VarObj xlsxImport = param.get("xlsxImport");
	
	// todo: 一期前端不预览xlsx，因此也不提供 sheetsCfg 参数，先由内核写死
	// if (false == xlsxImport.has("sheetsCfg"))
	// {
	// 	WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "No sheetsCfg parameter in xlsxImport!");
	// 	return E_FAIL;
	// }

	if (false == param.has("defaultName"))
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "No defaultName parameter!");
		return E_FAIL;
	}
	VarObj defaultName = param.get("defaultName");
	// todo: 导入时会创建看板视图, 并在不存在单选项字段时创建一个. 这个字段的配置信息需要前端传, 此处暂时使用自己构造的
	VarObj kanbanCfg = param.add_field_struct("kanbanCfg");

	ks_stdptr<Workbooks> spWorkbooks;
	m_wwb->GetCoreApp()->get_Workbooks(&spWorkbooks);
	if (nullptr == spWorkbooks)
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "Fail to get Workbooks!");
		return E_FAIL;
	}
	OpenWorkbookScope openWorkbookScope(spWorkbooks, m_wwb->GetCoreApp()->GetAppPersist()->GetPersist(), filePath);
	// 打开 xlsx 文件
	ks_stdptr<_Workbook> spEtApiWorkbook = openWorkbookScope.GetWorkbook();
	if (nullptr == spEtApiWorkbook || FAILED(openWorkbookScope.GetHr()))
	{
		// 将错误信息告知前端，并返回 E_FAIL 错误码
		EtTaskPeripheral* peripheral = GetPeripheral();
		if(peripheral) 
		{
			VarObj resObj = peripheral->resSelf();
			switch (openWorkbookScope.GetHr())
			{
			case IO_E_NEED_PERMISSION:
			case IO_E_SDSERVER_ERROR:
			case IO_E_SEC_NO_READ_PERMISSION:
			case IO_E_NOT_SUPPORT_SECURITY_DOC:
				resObj.add_field_bool("securityDoc", true);
				break;
			case IO_E_OPENCANCEL:
			case IO_E_NEED_MODIFY_PASSWORD:
			case IO_E_INVALID_MODIFY_PASSWORD:
				resObj.add_field_bool("encryptedDoc", true);
				break;
			default:
				break;
			}
		}
		
		return E_FAIL;
	}
	
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());

	//获取目标book
	ks_stdptr<_Workbook> spWorkbook = m_wwb->GetCoreWorkbook();
	// 构造一个空的RANGE参数. 不实际使用, 仅为兼容构造函数
	RANGE rg(m_wwb->GetBMP());
	// 记录当前最后一个sheet的idx。导入完成后需聚焦到导入的第一个sheet
	EtDbIdx lastSheetIdx = getLastSheetIdx();

	GlobalSharedStringHashMap<GlobalSharedString> attachmentIdMap;
	attachmentIdMap[GlobalSharedString((PCWSTR)nullptr)] = GlobalSharedString((PCWSTR)nullptr);
	VarObj attachmentData = param.get_s("copyAttachmentData");
	for (int i = 0, dataCnt = attachmentData.arrayLength_s(); i < dataCnt; ++i)
	{
		VarObj data = attachmentData.at_s(i);
		PCWSTR oldId = data.field_str("srcAttachmentId");
		attachmentIdMap[GlobalSharedString(oldId)] = GlobalSharedString((PCWSTR)nullptr);
		// status: 状态 0-成功 1-失败
		if (data.field_int32("status") == 0)
			attachmentIdMap[GlobalSharedString(oldId)] = GlobalSharedString(data.field_str("attachmentId"));
	}

	Et2DbImporter importer(spEtApiWorkbook, spWorkbook, pCtx, false, defaultName, xlsxImport, m_spProtectionJudgement.get(), true);
	importer.InitImportSheets(param);
	importer.SetAttachmentIdMap(&attachmentIdMap);
	if (param.has("needClearFormat"))
		importer.SetNeedClearFormat(param.field_bool("needClearFormat"));
	importer.SetFilePath(filePath);

	HRESULT hr = S_OK;
	
	hr = importer.Exec();
	if (FAILED(hr))
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "Fail in Exec()!");
		return E_FAIL;
	}

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		UINT activeSheetId = 0;
		m_wwb->GetCoreWorkbook()->GetBook()->RTSheetToSTSheet(lastSheetIdx + 1, &activeSheetId);
		if (INV_EtDbId != activeSheetId)
		{
			VarObj resObj = peripheral->resSelf();

			resObj.add_field_str("cmdName", GetTag());
			resObj.add_field_uint32("activeSheetId", activeSheetId);
		}
		const EtExportError& etExportError = importer.GetErrorStatus();
		if (etExportError.HasFailedMsg())
		{
			VarObj resObj = peripheral->resSelf();
			resObj.add_field_bool("completedSuccess", false);

			if (etExportError.RowCntLimit())
				resObj.add_field_bool("rowCntLimit", true);
			if (etExportError.InvalidSheet())
				resObj.add_field_bool("hasInvadSheet", true);
			if (etExportError.SheetCntLimit())
				resObj.add_field_bool("sheetCntLimit", true);
			if (etExportError.ProtectedTable())
				resObj.add_field_bool("hasProtectedTable", true);
				
			if (etExportError.ExceptionThrown())
				resObj.add_field_bool("ExceptionThrown", true);
		}
		else
		{
			VarObj resObj = peripheral->resSelf();
			resObj.add_field_bool("completedSuccess", true);
		}
	}
	return hr;
}

HRESULT TaskExecDbImportXlsxSheets::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR TaskExecDbImportXlsxSheets::GetTag()
{
	return __X("dbSheet.importXlsxSheets");
}
// ================== TaskExecDbImportCsv ==================
TaskExecDbImportCsv::TaskExecDbImportCsv(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbImportCsv::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	EtTaskPeripheral* peripheral = GetPeripheral();

	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = param, wmh.strField = "uploadid";
	QString path = wmh.resolvePath();
	if (path.isEmpty())
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "No file info!");
		return E_FAIL;
	}
	Csv2DbAdapter csv2DbAdapter(krt::utf16(path), m_wwb->GetCoreWorkbook(), m_wwb->GetBMP(), m_spProtectionJudgement, param);
	HRESULT hr = csv2DbAdapter.Init();
	if (FAILED(hr))
	{
		if (peripheral && hr == E_DATA_EMPTY)
		{
			VarObj resObj = peripheral->resSelf();
			resObj.add_field_str("cmdName", GetTag());
			resObj.add_field_bool("emptyCsv", true);
		}
		return hr;
	}
	UINT activeStId = 0;
	hr = csv2DbAdapter.Exec(activeStId);
	if (FAILED(hr))
	{
		csv2DbAdapter.rollback();
		return hr;
	}
	if (peripheral)
	{
		VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_uint32("activeSheetId", activeStId);
		if (csv2DbAdapter.GetLostFlag())
			resObj.add_field_bool("lostInfo", true);
		resObj.add_field_bool("completedSuccess", true);
	}
	return S_OK;
}

HRESULT TaskExecDbImportCsv::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR TaskExecDbImportCsv::GetTag()
{
	return __X("dbSheet.importCsv");
}
// ================== TaskExecDbImportDbSheets ==================
TaskExecDbImportDbSheets::TaskExecDbImportDbSheets(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbImportDbSheets::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = param, wmh.strField = "uploadid";
	QString path = wmh.resolvePath();
	if (path.isEmpty())
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "No file info!");
		return E_FAIL;
	}
	PCWSTR filePath = krt::utf16(path);
	std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher> attachmentIdMap;
	attachmentIdMap[GlobalSharedString((PCWSTR)nullptr)] = GlobalSharedString((PCWSTR)nullptr);
	DbSheet::GetAttachmentIdMap(param, attachmentIdMap);
	// 保留新增sheets的第一个sheet的id供前端聚焦
	UINT activeSheetId = 0;

	constexpr int maxImportSheetCount = 50;
	AddTemplateSheetsHelper addTemplateHelper(m_wwb, pCtx, filePath, true, false, m_spProtectionJudgement, param);
	addTemplateHelper.InitImportSheets(param);
	addTemplateHelper.setMaxImportSheetCount(maxImportSheetCount);
	addTemplateHelper.SetIsForImport(true);
	addTemplateHelper.SetClearAttachment(false);
	addTemplateHelper.SetAttachmentIdMap(&attachmentIdMap);
	if (param.has("new_lookup_converter"))
		param.field_bool("new_lookup_converter") ? addTemplateHelper.setFallBackLookUp(true) : static_cast<void>(0);
	HRESULT hr = addTemplateHelper.Exec(activeSheetId);
	if (FAILED(hr))
	{
		addTemplateHelper.rollback();
		return hr;
	}
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_uint32("activeSheetId", activeSheetId);
		resObj.add_field_bool("completedSuccess", true);
		if (addTemplateHelper.isExceededMaxImportSheetCount())
			resObj.add_field_bool("sheetCntLimit", true);
		if (addTemplateHelper.GetReachFpSheetLimit())
			resObj.add_field_bool("reachFpSheetLimit", true);
		if (addTemplateHelper.GetReachDashboardLimit())
			resObj.add_field_bool("reachDashboardLimit", true);
		if (addTemplateHelper.GetLostFlag())
			resObj.add_field_bool("lostInfo", true);
	}
	return S_OK;
}

HRESULT TaskExecDbImportDbSheets::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR TaskExecDbImportDbSheets::GetTag()
{
	return __X("dbSheet.importDbSheets");
}
// ================== TaskExecDbAddSyncSheets ==================
TaskExecDbAddSyncSheets::TaskExecDbAddSyncSheets(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbAddSyncSheets::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	if (!param.has("sheetsInfo"))
	{
		WOLOG_ERROR << "[TaskExecDbAddSyncSheets] No sync sheet info!";
		return E_FAIL;
	}

	VarObj sheetsInfo = param.get_s("sheetsInfo");
	int sheetsCnt = sheetsInfo.arrayLength();
	if (sheetsCnt <= 0)
		return E_INVALIDARG;

	AddSyncDbSheetsAdapter addSheetsHelper(m_wwb->GetCoreWorkbook(), m_spProtectionJudgement);
	AddSyncDbSheetsAdapter::SyncDbSheetsParam syncParam;
	syncParam.maxSyncSheetLimit = param.field_int32("maxSyncSheetLimit");
	syncParam.fldSourceName = param.field_str("fieldSourceName");
	syncParam.urlTemplate = param.field_str("urlTemplate");
	syncParam.sheetsInfo = &sheetsInfo;
	HRESULT hr = addSheetsHelper.Init(syncParam);
	if (FAILED(hr))
		return hr;
	hr = addSheetsHelper.Exec();
	if (FAILED(hr))
		return hr;
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_uint32("activeSheetId", addSheetsHelper.getActiveStId());
		if (addSheetsHelper.GetReachSyncSheetLimit())
			resObj.add_field_bool("reachSyncSheetLimit", true);
		if (addSheetsHelper.GetLostFlag())
			resObj.add_field_bool("lostInfo", true);
		const auto& sheetInfoVec = addSheetsHelper.getSyncSheetInfo();
		int stCnt = static_cast<int>(sheetInfoVec.size());
		VarObj sheetsInfo = resObj.add_field_structArray("sheetsInfo", stCnt);
		for (int i = 0; i < stCnt; ++i)
		{
			VarObj sheetInfo = sheetsInfo.at(i);
			auto& syncSheetInfo = sheetInfoVec[i];
			sheetInfo.add_field_uint32("srcSheetId", syncSheetInfo.srcSheetId);
			sheetInfo.add_field_uint32("refSheetId", syncSheetInfo.tarSheetId);
			PCWSTR syncTypeStr = nullptr;
			VS(_appcore_GainEncodeDecoder()->EncodeDbSheetSyncType(syncSheetInfo.syncType, &syncTypeStr));
			sheetInfo.add_field_str("syncType", syncTypeStr);
		}
	}
	return S_OK;
}

HRESULT TaskExecDbAddSyncSheets::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR TaskExecDbAddSyncSheets::GetTag()
{
	return __X("dbSheet.addSyncSheets");
}
// ================== TaskExecDbUnsyncSheets ==================
TaskExecDbUnsyncSheets::TaskExecDbUnsyncSheets(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbUnsyncSheets::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	VarObj param = cmd->cast().get("param");
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
	IBook* pBook = pWorkbook->GetBook();
	VarObj stIdVec = param.get_s("sheetIdVec");
	HRESULT hr = S_OK;
	std::vector<IKWorksheet*> worksheetVec;
	for (int i = 0, stCnt = stIdVec.arrayLength(); i < stCnt; ++i)
	{
		UINT stId = stIdVec.item_uint32(i);
		IDX idx = INVALIDIDX;
		hr = pBook->STSheetToRTSheet(stId, &idx);
		if (FAILED(hr))
			return hr;
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(idx);
		if (!pWorksheet)
			continue;
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		hr = DbSheet::GetDBSheetOp(pWorksheet->GetSheet(), &spDbSheetOp);
		if (FAILED(hr))
			return hr;
		if (spDbSheetOp->GetSheetSyncType() == DbSheet_St_Sql)
		{
			DbSyncSheet::ClearSyncRecordFlag(spDbSheetOp);
			DbSyncSheet::ClearSyncFieldFlag(spDbSheetOp);
			spDbSheetOp->SetSheetSyncType(DbSheet_St_None);
			continue;
		}
		if (!spDbSheetOp->IsSyncSheet())
			continue;
		worksheetVec.emplace_back(pWorksheet);
	}
	if(worksheetVec.empty()) //只有DB同步需要下面代码逻辑
		return S_OK;

	std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher> attachmentIdMap;
	GlobalSharedString nullStr(static_cast<PCWSTR>(nullptr));
	attachmentIdMap[nullStr] = nullStr;
	VarObj attachmentData = param.get_s("copyAttachmentData");
	for (int i = 0, dataCnt = attachmentData.arrayLength_s(); i < dataCnt; ++i)
	{
		VarObj data = attachmentData.at_s(i);
		GlobalSharedString oldId(data.field_str("srcAttachmentId"));
		attachmentIdMap[oldId] = nullStr;
		// status: 状态 0-成功 1-失败
		if (data.field_int32("status") == 0)
			attachmentIdMap[oldId] = GlobalSharedString(data.field_str("attachmentId"));
	}
	bool bLostNoteFlag = false;
	for (IKWorksheet* pWorksheet : worksheetVec)
	{
		UnsyncDbSheetHelper helper(pWorksheet);
		VS(helper.Init(&attachmentIdMap));
		VS(helper.Exec());
		if (!bLostNoteFlag && helper.GetLostNoteFlag())
			bLostNoteFlag = true;
	}
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(pWorkbook);
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		VarObj resObj = peripheral->resSelf();
		if (bLostNoteFlag)
			resObj.add_field_bool("lostInfo", true);
	}
	return S_OK;
}

HRESULT TaskExecDbUnsyncSheets::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckCanManage();
}

PCWSTR TaskExecDbUnsyncSheets::GetTag()
{
	return __X("dbSheet.unsyncSheets");
}
// ================== TaskExecCleanUnusedSyncSheets ==================
TaskExecCleanUnusedSyncSheets::TaskExecCleanUnusedSyncSheets(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecCleanUnusedSyncSheets::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
	IBook* pBook = pWorkbook->GetBook();
	if (!param.has("sheetIdVec"))
	{
		ks_stdptr<IUnknown> spUnknownDbSyncReleationMgr;
		pBook->GetExtDataItem(edBookDbSyncReleationManager, &spUnknownDbSyncReleationMgr);
		ks_stdptr<IDbSyncReleationManager> spDbSyncReleationMgr = spUnknownDbSyncReleationMgr;
		struct KDbUnusedSyncSheetEnum : IDbUnusedSyncSheetEnum
		{
			HRESULT Do(UINT sheetId) override
			{
				needRemoveSheetId.emplace_back(sheetId);
				return S_OK;
			}
			std::vector<UINT> needRemoveSheetId;
		};
		KDbUnusedSyncSheetEnum sheetEnum;
		spDbSyncReleationMgr->EnumUnusedSyncSheet(&sheetEnum);
		VarObj sheetIdVec = param.add_field_array("sheetIdVec", binary_wo::typeUint32);
		for (const auto& stId : sheetEnum.needRemoveSheetId)
			sheetIdVec.add_item_uint32(stId);
	}
	VarObj sheetIdVec = param.get_s("sheetIdVec");
	for (int i = 0, sheetIdLen = sheetIdVec.arrayLength_s(); i <sheetIdLen; ++i)
	{
		UINT stId = sheetIdVec.item_uint32(i);
		IDX idx = INVALIDIDX;
		pBook->STSheetToRTSheet(stId, &idx);
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(idx);
		if (!pWorksheet)
			continue;
		pWorksheet->DeleteDirectly();
	}
	return S_OK;
}

HRESULT TaskExecCleanUnusedSyncSheets::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanRemove();
}

PCWSTR TaskExecCleanUnusedSyncSheets::GetTag()
{
	return __X("dbSheet.cleanUnusedSyncSheets");
}

// ================== TaskExecDbAddSyncMergeSheet ==================
TaskExecDbAddSyncMergeSheet::TaskExecDbAddSyncMergeSheet(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}
HRESULT TaskExecDbAddSyncMergeSheet::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	WebMimeHelper wmh;
	_Application* pTarApp = m_wwb->GetCoreApp();
	ks_stdptr<Workbooks> spWorkbooks;
	pTarApp->get_Workbooks(&spWorkbooks);
	if (!spWorkbooks)
	{
		ASSERT(FALSE);
		return E_FAIL;
	}
	_Workbook* pTarWorkbook = m_wwb->GetCoreWorkbook();
	pTarApp->GetEtTranscationTool()->ClearAllUndoRedoSteps(pTarWorkbook);
	AddMergeSyncDbSheetsAdapter addSheetsHelper(pTarWorkbook, m_spProtectionJudgement);
	AddMergeSyncDbSheetsAdapter::SyncDbSheetsParam syncParam;
	syncParam.maxSyncSheetLimit = param.field_int32("maxSyncSheetLimit");
	syncParam.fldSourceName = param.field_str("fieldSourceName");
	syncParam.fldSourceFileName = param.field_str("fldSourceFileName");
	syncParam.viewName = param.field_str("viewName");
	syncParam.sheetName = param.field_str("sheetName");
	HRESULT hr = addSheetsHelper.Init(syncParam);
	if (FAILED(hr))
		return hr;
	VAR_OBJ_EXPECT_ARRAY(param, "srcSheetsInfoVec");
	VarObj srcInfoVec = param.get_s("srcSheetsInfoVec");
	int srcInfoCnt = srcInfoVec.arrayLength();
	for (int i = 0; i < srcInfoCnt; i++)
	{
		VarObj srcSheetsParam = srcInfoVec.at(i);
		PCWSTR fileId = srcSheetsParam.field_str("fileId");
		VAR_OBJ_EXPECT_ARRAY(srcSheetsParam, "srcSheets");
		VarObj srcSheetsInfo = srcSheetsParam.get_s("srcSheets"); // 包含fields和recordCount信息
		if (nullptr == fileId)
			continue;
		hr = addSheetsHelper.AddFieldMappingItem(fileId, &srcSheetsInfo);
		if (FAILED(hr))
			return hr;
	}
	hr = addSheetsHelper.ExecMerge();
	if (FAILED(hr))
		return hr;
	fillResObj(addSheetsHelper);
	return S_OK;
}

HRESULT TaskExecDbAddSyncMergeSheet::CheckCmdPermission(KwCommand*, KEtRevisionContext*)
{
	return m_spProtectionJudgement->CheckSheetCanAdd();
}

PCWSTR TaskExecDbAddSyncMergeSheet::GetTag()
{
	return __X("dbSheet.addSyncMergeSheet");
}

void TaskExecDbAddSyncMergeSheet::fillResObj(const AddMergeSyncDbSheetsAdapter& addSheetsHelper)
{
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_uint32("targetSheetId", addSheetsHelper.GetActiveStId());
		AddEtDbId(resObj, "fieldSourceId", addSheetsHelper.GetFieldSourceId());
		AddEtDbId(resObj, "fldSourceFileInfoId", addSheetsHelper.GetFieldSourceNameId());
		const auto& stFileIdMap = addSheetsHelper.getFileIdMap();
		int bookCnt = static_cast<int>(stFileIdMap.size());
		VarObj sheetsInfo = resObj.add_field_structArray("sheetsInfo", bookCnt);
		auto iter = stFileIdMap.begin();
		for (int i = 0; i < bookCnt && iter != stFileIdMap.end(); ++i, ++iter)
		{
			VarObj sheetInfo = sheetsInfo.at(i);
			sheetInfo.add_field_str("fileId", iter->first.c_str());
			const auto& stFieldItems = iter->second;
			int sheetCnt = stFieldItems.size();
			VarObj sheetItems = sheetInfo.add_field_structArray("sheetItems", sheetCnt);
			auto itemIter = stFieldItems.begin();
			for (int j = 0; j < sheetCnt && itemIter != stFieldItems.end(); ++j, ++itemIter)
			{
				VarObj sheetItem = sheetItems.at(j);
				sheetItem.add_field_uint32("srcSheetId", itemIter->m_uSheetStId);
				VarObj fieldMappingList = sheetItem.add_field_struct("fieldMappingList");
				VarObj srcFieldId = fieldMappingList.add_field_array("srcFieldId", typeString);
				VarObj tarFieldId = fieldMappingList.add_field_array("tarFieldId", typeString);
				for (auto it : itemIter->m_fieldIdMap)
				{
					AddEtDbIdItem(srcFieldId, it.first);
					AddEtDbIdItem(tarFieldId, it.second);
				}
			}
		}
	}
}

// ================== TaskExecUpdateSyncInfoSheets ==================
TaskExecUpdateSyncInfoSheets::TaskExecUpdateSyncInfoSheets(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecUpdateSyncInfoSheets::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetId = param.field_uint32("sheetStId");
	PCWSTR sourceFileName = nullptr;
	if (param.has("sourceFileName"))
		sourceFileName = param.field_str("sourceFileName");
	PCWSTR sourceSheetName = nullptr;
	if (param.has("sourceSheetName"))
		sourceSheetName = param.field_str("sourceSheetName");
	if (!sourceFileName && !sourceSheetName)
		return E_INVALIDARG;
	_Workbook* pWorkbook = m_wwb->GetCoreWorkbook();
	IBook* pBook = pWorkbook->GetBook();
	ks_stdptr<IUnknown> spUnknownDbSyncReleationMgr;
	pBook->GetExtDataItem(edBookDbSyncReleationManager, &spUnknownDbSyncReleationMgr);
	ks_stdptr<IDbSyncReleationManager> spDbSyncReleationMgr = spUnknownDbSyncReleationMgr;
	return spDbSyncReleationMgr->UpdateReleation(sheetId, sourceFileName, sourceSheetName);
}

HRESULT TaskExecUpdateSyncInfoSheets::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	VarObj param = cmd->cast().get("param");
	return m_spProtectionJudgement->CheckSheetCanEdit(param.field_uint32("sheetStId"));
}

PCWSTR TaskExecUpdateSyncInfoSheets::GetTag()
{
	return __X("dbSheet.updateSyncInfo");
}
// ================== TaskExecDbAppendData ==================
TaskExecDbAppendData::TaskExecDbAppendData(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbAppendData::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	WebMimeHelper wmh;
	wmh.cmd = cmd, wmh.ctx = pCtx, wmh.objParam = param, wmh.strField = "uploadid";
	QString path = wmh.resolvePath();
	if (path.isEmpty())
	{
		WO_LOG_X(m_wwb->getLogger(), WO_LOG_ERROR, "No flie info!");
		return E_FAIL;
	}
	PCWSTR filePath = krt::utf16(path);
	std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher> attachmentIdMap;
	GlobalSharedString nullStr(static_cast<PCWSTR>(nullptr));
	attachmentIdMap[nullStr] = nullStr;
	VarObj attachmentData = param.get_s("copyAttachmentData");
	for (int i = 0, dataCnt = attachmentData.arrayLength_s(); i < dataCnt; ++i)
	{
		VarObj data = attachmentData.at_s(i);
		GlobalSharedString oldId(data.field_str("srcAttachmentId"));
		attachmentIdMap[oldId] = nullStr;
		// status: 状态 0-成功 1-失败
		if (data.field_int32("status") == 0)
			attachmentIdMap[oldId] = GlobalSharedString(data.field_str("attachmentId"));
	}
	_Application* pTarApp = m_wwb->GetCoreApp();
	ks_stdptr<Workbooks> spWorkbooks;
	pTarApp->get_Workbooks(&spWorkbooks);
	if (!spWorkbooks)
		return E_FAIL;
	// 打开源文件
	OpenWorkbookScope openWorkbookScope(spWorkbooks, pTarApp->GetAppPersist()->GetPersist(), filePath);
	HRESULT hr = openWorkbookScope.GetHr();
	if (FAILED(hr))
		return hr;
	_Workbook* pSrcWorkbook = openWorkbookScope.GetWorkbook();
	if (!pSrcWorkbook)
		return E_FAIL;
	IKWorksheets* pSrcWorksheets = pSrcWorkbook->GetWorksheets();
	IDX srcStIdx = INVALIDIDX;
	// 产品需求，默认取第一张非深度隐藏的工作表或数据表作为输入
	for (int i = 0, srcSheetCnt = pSrcWorksheets->GetSheetCount(); i < srcSheetCnt; ++i)
	{
		IKWorksheet* pSrcWorksheet = pSrcWorksheets->GetSheetItem(i);
		if (!pSrcWorksheet)
			continue;
		ISheet* pSrcSheet = pSrcWorksheet->GetSheet();
		if (!pSrcSheet)
			continue;
		SHEETSTATE sheetState = ssVisible;
		pSrcSheet->GetVisible(&sheetState);
		if (sheetState == ssVeryhidden)
			continue;
		if (pSrcSheet->IsGridSheet() || pSrcSheet->IsOldDbSheet() || pSrcSheet->IsDbSheet())
		{
			srcStIdx = i;
			break;
		}
	}
	_Workbook* pTarWorkbook = m_wwb->GetCoreWorkbook();
	UINT tarStId = param.field_uint32("sheetStId");
	IDX tarStIdx = INVALIDIDX;
	pTarWorkbook->GetBook()->STSheetToRTSheet(tarStId, &tarStIdx);
	DbAppendDataAdapter::DbAppendDataParam appendDataParam;
	appendDataParam.pAttachmentIdMap = &attachmentIdMap;
	appendDataParam.srcStIdx = srcStIdx;
	appendDataParam.tarStIdx = tarStIdx;
	DbAppendDataAdapter appendDataHelper(pSrcWorkbook, pTarWorkbook, m_spProtectionJudgement);
	hr = appendDataHelper.Init(appendDataParam);
	if (FAILED(hr))
		return hr;
	hr = appendDataHelper.Exec();
	if (FAILED(hr))
		return hr;
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		VarObj resObj = peripheral->resSelf();
		const auto& newRecordIds = appendDataHelper.GetNewRecordIds();
		size_t newRecCnt = newRecordIds.size();
		resObj.add_field_uint32("appendRecordsNum", newRecCnt);
		if (appendDataHelper.GetRowLimitFlag())
			resObj.add_field_bool("reachRowLimit", true);
		VarObj rgObj = resObj.add_field_struct("rg");
		ks_stdptr<IDBSheetOp> spDbSheetOp;
		hr = m_commonHelper.GetDBSheetOp(tarStId, &spDbSheetOp);
		if (FAILED(hr))
			return hr;
		VarObj recIds = rgObj.add_field_array("records", binary_wo::typeString);
		const IDBIds* pRecIds = spDbSheetOp->GetAllRecords();
		EtDbIdx recCnt = pRecIds->Count();
		if (newRecCnt > recCnt / 2)
		{
			rgObj.add_field_str("recordsMode", __X("exclude"));
			std::unordered_set<EtDbId> exSet(newRecordIds.begin(), newRecordIds.end());
			auto itExSetEnd = exSet.end();
			for (int i = 0; i < recCnt; ++i) 
			{
				EtDbId id = pRecIds->IdAt(i);
				if (exSet.find(id) == itExSetEnd)
					AddEtDbIdItem(recIds, id);
			}
		}
		else
		{
			rgObj.add_field_str("recordsMode", __X("include"));
			for (auto id : newRecordIds)
				AddEtDbIdItem(recIds, id);
		}
		rgObj.add_field_str("fieldsMode", __X("allInView"));
	}
	return S_OK;
}

HRESULT TaskExecDbAppendData::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	VarObj param = cmd->cast().get("param");
	return m_spProtectionJudgement->CheckRecordCanAdd(param.field_uint32("sheetStId"));
}

PCWSTR TaskExecDbAppendData::GetTag()
{
	return __X("dbSheet.appendData");
}
// ================== TaskExecDbClearTransactions ==================

TaskExecDbClearTransactions::TaskExecDbClearTransactions(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbClearTransactions::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
	return S_OK;
}

HRESULT TaskExecDbClearTransactions::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecDbClearTransactions::GetTag()
{
	return __X("dbsheet.clearTransactions");
}

// ================== TaskExecDbSetViewCoverField ==================
TaskExecDbSetViewCoverField::TaskExecDbSetViewCoverField(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetViewCoverField::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ET_DBSheet_ViewType type = spDbSheetView->GetType();
	switch (type)
	{
		case et_DBSheetView_Gallery:
		case et_DBSheetView_Kanban:
			return setCoverField(param, spDbSheetView);
		default:
			ASSERT(FALSE);
			return E_INVALIDARG;
	}
}

HRESULT TaskExecDbSetViewCoverField::setCoverField(binary_wo::VarObj& param, IDBSheetView* pView)
{
	ks_stdptr<IDBSheetView_CardFeatures> spViewCard = pView;
	if (spViewCard == nullptr)
		return E_INVALIDARG;

	return spViewCard->SetCoverField(GetEtDbId(param, "coverField"));
}

HRESULT TaskExecDbSetViewCoverField::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetCoverField(sheetStId, viewId);
}

// ================== TaskExecDbSetViewCoverDispType ==================
TaskExecDbSetViewCoverDispType::TaskExecDbSetViewCoverDispType(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetViewCoverDispType::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
	
	ET_DBSheet_ViewType type = spDbSheetView->GetType();
	switch (type)
	{
		case et_DBSheetView_Gallery:
		case et_DBSheetView_Kanban:
			return setCoverDispType(param, spDbSheetView);
		default:
			ASSERT(FALSE);
			return E_INVALIDARG;
	}
}

HRESULT TaskExecDbSetViewCoverDispType::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetCoverDispType(sheetStId, viewId);
}

HRESULT TaskExecDbSetViewCoverDispType::setCoverDispType(binary_wo::VarObj& param, IDBSheetView* pView)
{
	ks_stdptr<IDBSheetView_CardFeatures> spViewCard = pView;
	if (spViewCard == nullptr)
		return E_INVALIDARG;

	ET_DBSheet_CoverDispType type = et_DBSheetCoverDisp_fit;
	HRESULT hr = m_pEncodeDecoder->DecodeKDbCoverDispType(param.field_str("dispType"), &type);
	if (FAILED(hr))
		return hr;

	return spViewCard->SetCoverDispType(type);
}

// ================== TaskExecDbSetKanbanCoverField ==================
TaskExecDbSetKanbanCoverField::TaskExecDbSetKanbanCoverField(KEtWorkbook* wwb)
	: TaskExecDbSetViewCoverField(wwb)
{
}

PCWSTR TaskExecDbSetKanbanCoverField::GetTag()
{
	return __X("dbsheet.setKanbanCoverField");
}

// ================== TaskExecDbSetGalleryCoverField ==================
TaskExecDbSetGalleryCoverField::TaskExecDbSetGalleryCoverField(KEtWorkbook* wwb)
	: TaskExecDbSetViewCoverField(wwb)
{
}

PCWSTR TaskExecDbSetGalleryCoverField::GetTag()
{
	return __X("dbsheet.setGalleryCoverField");
}

// ================== TaskExecDbSetKanbanCoverDispType ==================
TaskExecDbSetKanbanCoverDispType::TaskExecDbSetKanbanCoverDispType(KEtWorkbook* wwb)
	: TaskExecDbSetViewCoverDispType(wwb)
{
}

PCWSTR TaskExecDbSetKanbanCoverDispType::GetTag()
{
	return __X("dbsheet.setKanbanCoverDispType");
}

// ================== TaskExecDbSetGalleryCoverDispType ==================
TaskExecDbSetGalleryCoverDispType::TaskExecDbSetGalleryCoverDispType(KEtWorkbook* wwb)
	: TaskExecDbSetViewCoverDispType(wwb)
{
}

PCWSTR TaskExecDbSetGalleryCoverDispType::GetTag()
{
	return __X("dbsheet.setGalleryCoverDispType");
}

// ================== TaskExecDbSetFieldNameVisible ==================
TaskExecDbSetFieldNameVisible::TaskExecDbSetFieldNameVisible(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetFieldNameVisible::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
	
	ET_DBSheet_ViewType type = spDbSheetView->GetType();
	bool isVisible =  true;
	if (param.has("isVisible"))
		isVisible = param.field_bool("isVisible");
	switch (type)
	{
		case et_DBSheetView_Gallery:
		case et_DBSheetView_Kanban:
		{
			ks_stdptr<IDBSheetView_CardFeatures> spViewCard = spDbSheetView;
			if (!spViewCard)
				return E_INVALIDARG;
			return spViewCard->SetFieldNameVisible(isVisible);
		}
		default:
			ASSERT(FALSE);
			return E_INVALIDARG;
	}
}

HRESULT TaskExecDbSetFieldNameVisible::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanbSetFieldNameVisible(sheetStId, viewId);
}

PCWSTR TaskExecDbSetFieldNameVisible::GetTag()
{
	return __X("dbsheet.setFieldNameVisible");
}

// ================== TaskExecDbSetDbLayout ==================
TaskExecDbSetDbLayout::TaskExecDbSetDbLayout(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbSetDbLayout::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");

	ET_DBSheet_LayoutType layoutType = DB_Layout_Application;
	HRESULT hr = E_FAIL;
	if (param.has("dbLayout"))
		hr = m_pEncodeDecoder->DecodeDbLayoutType(param.field_str("dbLayout"), &layoutType);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDbLayout> spDbLayout;
	m_wwb->GetCoreWorkbook()->GetBook()->GetExtDataItem(edDbLayout, (IUnknown**)&spDbLayout);
	if (!spDbLayout)
		return E_FAIL;
	WebStr userID = pCtx->getUser()->userID();
	return spDbLayout->SetDbLayout(userID, layoutType);
}

HRESULT TaskExecDbSetDbLayout::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbSetDbLayout::GetTag()
{
	return __X("dbsheet.setDbLayout");
}

// ================== TaskExecDbSetTimerTask ==================
TaskExecDbSetTimerTask::TaskExecDbSetTimerTask(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbSetTimerTask::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");

	ks_stdptr<IDBSheetOp> spDbSheetOp;
    HRESULT hr = m_commonHelper.GetDBSheetOp(sheetStId, &spDbSheetOp);
    if (FAILED(hr))
        return hr;

	EtDbId automationId = GetEtDbId(param, "automationId");
	EtDbId recId = GetEtDbId(param, "recId");
	const IDBIds *pFields = spDbSheetOp->GetAllFields();
	IDbFieldsManager *pFieldsMgr = spDbSheetOp->GetFieldsManager();
	for (EtDbIdx fld = 0; fld < pFields->Count(); fld++)
	{
		EtDbId fldId = pFields->IdAt(fld);
		ks_stdptr<IDbField> spField;
		HRESULT hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr) || spField->GetType() != Et_DbSheetField_Automations)
			continue;

		ks_stdptr<IDbField_Automations> spField_Automations = spField;
		UINT automationsCnt = spField_Automations->GetAutomationsCount();

		for (EtDbIdx i = 0; i < automationsCnt; i++)
		{
			ks_stdptr<IDbAutomation> spAutomation;
			hr = spField_Automations->GetAutomation(i, &spAutomation);
			if (FAILED(hr) || spAutomation->GetId() != automationId)
				continue;

			ks_stdptr<IDbAutomationTrigger> spAutomationTrigger;
			hr = spAutomation->GetTrigger(&spAutomationTrigger);
			if (FAILED(hr) || spAutomationTrigger->GetType() != AutomationTrigger_DueDate)
				continue;

			ks_stdptr<IDbAutomationTrigger_DueDate> spAutomationTrigger_DueDate = spAutomationTrigger;
			hr = spAutomationTrigger_DueDate->SetTimerTask(recId);
			if (FAILED(hr))
				return hr;
			
			EtTaskPeripheral* peripheral = GetPeripheral();
			if(peripheral) 
			{
				binary_wo::VarObj resSelfObj = peripheral->resSelf();
				resSelfObj.add_field_str("cmdName", GetTag());
				resSelfObj.add_field_bool("timerChanged", true);

				binary_wo::VarObj resOthersObj = peripheral->resOthers();
				resOthersObj.add_field_str("cmdName", GetTag());
				resOthersObj.add_field_bool("timerChanged", true);
			}

			return S_OK;
		}
	}
	return E_FAIL;
}

HRESULT TaskExecDbSetTimerTask::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbSetTimerTask::GetTag()
{
	return __X("dbsheet.setTimerTask");
}

// ================== TaskExecDbFetchUserInfoDone ==================
TaskExecDbFetchUserInfoDone::TaskExecDbFetchUserInfoDone(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbFetchUserInfoDone::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	HRESULT hr = S_OK;
	
	std::vector<ks_wstring> failedIds;
	std::vector<wo::DbSheet::UserInfo> userInfoVec;
	binary_wo::VarObj args = cmd->cast().get("param");
	binary_wo::VarObj infoList = args.get_s("user_info_list");
	for (int i = 0; i < infoList.arrayLength_s(); ++i)
	{
		VarObj item = infoList.at_s(i);

		if (binary_wo::typeStruct != item.type())
			return E_INVALID_REQUEST;
		VAR_OBJ_EXPECT_STRING(item, "id");
		if (!item.has("remarked_name") || !item.has("avatar_url"))
			continue;
		VAR_OBJ_EXPECT_STRING(item, "remarked_name");
		VAR_OBJ_EXPECT_STRING(item, "avatar_url");

		userInfoVec.emplace_back(wo::DbSheet::UserInfo{});
		auto& userInfo = userInfoVec.back();
		WebStr id = item.field_str("id");
		userInfo.id = id;
		WebStr name = item.field_str("remarked_name");
        if (xstrcmp(__X(""), name) == 0 && item.has("name"))
            name = item.field_str("name");
		userInfo.nickname = name;
		userInfo.avatar = item.field_str("avatar_url");
        if (item.has("companyId"))
            userInfo.companyId = std::make_unique<ks_wstring>(item.field_str("companyId"));
	}
	if (args.has("failed_list"))
	{
		VarObj failedIdList = args.get_s("failed_list");
		for (int i = 0; i < failedIdList.arrayLength_s(); ++i)
		{
			VarObj item = failedIdList.at_s(i);
			WebStr id = item.value_str();
			failedIds.emplace_back(id);
		}
	}
	ks_stdptr<IDbUsersManager> spUsersMgr;
	IBook* pBook = pCtx->getBook();
	pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);

	std::vector<KDbCstrUserInfo> cstrUserInfoVec;
	hr = wo::DbSheet::GetCstrUserInfoVec(userInfoVec, cstrUserInfoVec);
	if (FAILED(hr))
		return hr;
	std::vector<PCWSTR> cstrFailedIds;
	hr = wo::DbSheet::GetCstrFailedIds(failedIds, cstrFailedIds);
	if (FAILED(hr))
		return hr;

	hr = spUsersMgr->UpdateUserInfoFromServer(cstrUserInfoVec.data(), cstrUserInfoVec.size()
		, cstrFailedIds.data(), cstrFailedIds.size());
	if (FAILED(hr))
		return hr;

	return hr;
}

HRESULT TaskExecDbFetchUserInfoDone::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}


PCWSTR TaskExecDbFetchUserInfoDone::GetTag()
{
	return __X("dbsheet.fetchUserInfoDone");
}


TaskExecDbUpdateUserOrganizeInfo::TaskExecDbUpdateUserOrganizeInfo(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbUpdateUserOrganizeInfo::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj args = cmd->cast().get("param");
	ks_stdptr<IDbUsersManager> spUsersMgr;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	pBook->GetExtDataItem(edBookDbUserManager, (IUnknown**)&spUsersMgr);
	if (!spUsersMgr)
		return E_FAIL;
	
	//更新所有, 请求所有用户id的企业信息, 否则应用传入的企业信息
	bool  updateAll = false;
	if (args.has("updateAll")) 
		updateAll = args.field_bool("updateAll");

	if (updateAll)
	{
		std::vector<ks_wstring> queryUserIds;
		class UserIdsEnum : public IDBUserIdsEnum
		{
		public:
			UserIdsEnum(std::vector<ks_wstring>& ids) : m_userIds(ids) {}
			STDPROC Do(PCWSTR id) override
			{
				m_userIds.push_back(id);
				return S_OK;
			}
		private:
			std::vector<ks_wstring>& m_userIds;
		};

		UserIdsEnum  enumer(queryUserIds);
		spUsersMgr->EnumCompanyUser(&enumer);
     	if (!queryUserIds.empty()) 
		{
			DbUserInfoQuery userInfo(spUsersMgr, QString());
			userInfo.QueryUserCompanyInfo(queryUserIds);
		}
		WOLOG_INFO << "[TaskExecDbUpdateUserOrganizeInfo] force update all user ";
	}
	else
	{
		if (!args.has("userInfos"))
			return E_FAIL;
		binary_wo::VarObj infoList = args.get_s("userInfos");
		spUsersMgr->UpdateUserCompanyInfo(infoList);
	}

	return S_OK;
}

HRESULT TaskExecDbUpdateUserOrganizeInfo::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}


PCWSTR TaskExecDbUpdateUserOrganizeInfo::GetTag()
{
	return __X("dbsheet.updateUserOrganizeInfo");
}

// ================== TaskExecDbassignTimeLine ==================
TaskExecDbassignTimeLine::TaskExecDbassignTimeLine(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	
}

HRESULT TaskExecDbassignTimeLine::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if (nullptr == spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
	if (et_DBSheetView_Gantt != spDbSheetView->GetType())
		return E_INVALID_REQUEST;

	EtDbId recId = GetEtDbId(param, "recordId");
	if (INV_EtDbId == recId)
		return E_DBSHEET_RECORD_NOT_FOUND;

	HRESULT hr = assignDateConfigTimeLine(spDbSheetView.get(), param, recId);
	if (FAILED(hr))
		return hr;

	return hr;
}

HRESULT TaskExecDbassignTimeLine::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if (!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
	ASSERT(et_DBSheetView_Gantt == spDbSheetView->GetType());

	EtDbId recId = GetEtDbId(param, "recordId");
	if (INV_EtDbId == recId)
		return E_DBSHEET_RECORD_NOT_FOUND;

	ks_stdptr<IDBSheetView_Gantt> spView_Gantt = spDbSheetView;
	HRESULT hr = S_OK;
	if (param.has("beginTime"))
	{
		hr = m_spProtectionJudgement->CheckCellCanEdit(sheetStId, recId, spView_Gantt->GetBeginField());
		if (FAILED(hr))
			return hr;
	}
	if (param.has("endTime"))
	{
		hr = m_spProtectionJudgement->CheckCellCanEdit(sheetStId, recId, spView_Gantt->GetEndField());
		if (FAILED(hr))
			return hr;
	}
	return hr;
}

PCWSTR TaskExecDbassignTimeLine::GetTag()
{
	return __X("dbsheet.assignTimeLine");
}

// ================== TaskExecDbConfigureGanttView ==================
TaskExecDbConfigureGanttView::TaskExecDbConfigureGanttView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	
}

namespace
{
// 当前给甘特和日历用
HRESULT addDateConfigField(IDBSheetView* pView, VarObj param, WebName paraName, IDBSheetViews* pViews)
{
	if (strncmp("beginFieldName", paraName, 15) != 0 && strncmp("endFieldName", paraName, 13) != 0)
		return S_OK;

	HRESULT hr = S_OK;
	VarObj fldName = param.get_s(paraName);
	if (typeString == fldName.type())
	{
		VarObj args = param.add_field_struct("args");
		args.add_field_str("fieldName", fldName.value_str());
		ks_stdptr<IDBSheetRange> spDbRange;
		hr = DbSheet::AddField(pView, Et_DbSheetField_Date, args, &spDbRange);
		if (FAILED(hr))
			return hr;

		EtDbId fldId = spDbRange->GetFieldId(0);
		ks_stdptr<IDbField> spField;
		IDbFieldsManager *pFieldsMgr = pView->GetSheetOp()->GetFieldsManager();
		hr = pFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;

		// 自动创建的日期字段, 不作为可见字段
		hr = pView->SetFieldsHidden(spField->GetID(), TRUE);
		if (FAILED(hr))
			return hr;

		ks_stdptr<IDBSheetView_DateConfig> spView = pView;
		if (strncmp("beginFieldName", paraName, 15) == 0)
			hr = spView->SetBeginField(fldId);
		else if (strncmp("endFieldName", paraName, 13) == 0)
			hr = spView->SetEndField(fldId);
		if (FAILED(hr))
			return hr;

		// 遍历其他视图, 将其他缺乏字段配置的视图也追加设置
		if (pViews)
		{
			ks_stdptr<IDBSheetViewsEnum> spEnum;
			if (SUCCEEDED(pViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
			{
				do
				{
					ks_stdptr<IDBSheetView> spView;
					spEnum->GetCurView(&spView);
					if (!spView)
						continue;

					switch (spView->GetType())
					{
						case et_DBSheetView_Gantt:
						case et_DBSheetView_Calendar:
						{
							ks_stdptr<IDBSheetView_DateConfig> spDCView = spView;
							if (nullptr == spDCView)
								break; // 退出swtich
							if (pView == spView.get()) // 与当前视图是同一个view
								break;
							assignDateConfigField(spDCView.get(), fldId);
						}
						default:
							break; // 退出swtich
					}
				}while (SUCCEEDED(spEnum->Next()));
			}
		}
	}
	return S_OK;
}
}

HRESULT TaskExecDbConfigureGanttView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (nullptr == spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;
	if (et_DBSheetView_Gantt != spDbSheetView->GetType())
		return E_INVALID_REQUEST;

	HRESULT hr = S_OK;
	ks_stdptr<IDBSheetView_Gantt> spView_Gantt = spDbSheetView;

	if (param.has("timeLineColorType"))
	{	
		KDBViewConfigColorType colorType = KDBViewConfigColorType_Custom;
		hr = m_pEncodeDecoder->DecodeDbViewConfigColorType(param.field_str("timeLineColorType"), &colorType);
		if (FAILED(hr))
			return hr;
		hr = spView_Gantt->SetTimeLineColorType(colorType);
		if (FAILED(hr))
			return hr;
	}

	if (param.has("followField"))
	{
		if (spView_Gantt->GetTimeLineColorType() != KDBViewConfigColorType_Follow)
			return E_INVALIDARG;
		EtDbId followFieldId = GetEtDbId(param, "followField");
		hr = spView_Gantt->SetFollowField(followFieldId);
		if (FAILED(hr))
			return hr;
	}

	if (wo::VarObjFieldValidation::expectNumeric(param, "timelineColor"))
	{
		ARGB timelineColor = param.field_uint32("timelineColor");
		VS(spView_Gantt->SetTimeLineColor(timelineColor));
	}

	if (wo::VarObjFieldValidation::expectBool(param, "isOnlyWorkDay"))
	{
		bool bIsOnlyWorkDay = param.field_bool("isOnlyWorkDay");
		VS(spView_Gantt->SetIsOnlyWorkDay(alg::bool2BOOL(bIsOnlyWorkDay)));
	}

	VarObj createFields = param.get_s("createFields");
	if (typeBool == createFields.type() && createFields.value_bool())
	{
		hr = addDateConfigField(spDbSheetView.get(), param, "beginFieldName", spDbSheetViews);
		if (FAILED(hr))
			return hr;
		hr = addDateConfigField(spDbSheetView.get(), param, "endFieldName", spDbSheetViews);
		if (FAILED(hr))
			return hr;
	}

	if (param.has("beginField"))
	{
		EtDbId beginFldId = GetEtDbId(param, "beginField");
		hr = spView_Gantt->SetBeginField(beginFldId); // 允许通过 INV_EtDbId 使甘特图取消与日期字段的关联
		if (FAILED(hr))
			return hr;
	}
	if (param.has("endField"))
	{
		EtDbId endFldId = GetEtDbId(param, "endField");
		hr = spView_Gantt->SetEndField(endFldId); // 允许通过 INV_EtDbId 使甘特图取消与日期字段的关联
		if (FAILED(hr))
			return hr;
	}

	if (SUCCEEDED(hr))
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if (peripheral) 
		{
			VarObj resObj = peripheral->resSelf();

			resObj.add_field_str("cmdName", GetTag()); // cmdrespone 需要
		}
	}

	return hr;
}

HRESULT TaskExecDbConfigureGanttView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanGanttOp(sheetStId, viewId);
}

PCWSTR TaskExecDbConfigureGanttView::GetTag()
{
	return __X("dbsheet.configureGanttView");
}

// ================== TaskExecDbSetFormFieldOption ==================
TaskExecDbSetFormFieldOption::TaskExecDbSetFormFieldOption(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetFormFieldOption::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	EtDbId fldId = GetEtDbId(param, "fieldId");
	bool isRequired = param.field_bool("isRequired");

	HRESULT hr = m_spProtectionJudgement->CheckCanSetFormFieldOption(sheetStId, viewId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	if(spDbSheetView->GetType() != et_DBSheetView_Form)
		return E_INVALIDARG;

	ks_stdptr<IDBSheetView_Form> spDbSheetViewForm = spDbSheetView;
	if (spDbSheetViewForm == nullptr)
		return E_FAIL;

	return spDbSheetViewForm->GainMutableFieldOption(fldId)->SetIsRequired(isRequired);
}

HRESULT TaskExecDbSetFormFieldOption::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK; //内部判断
}

PCWSTR TaskExecDbSetFormFieldOption::GetTag()
{
	return __X("dbsheet.setFormFieldOption");
}

TaskExecDbSetViewDisplayFromType::TaskExecDbSetViewDisplayFromType(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbSetViewDisplayFromType::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	if(!param.has("sheetStId") || !param.has("viewId") || !param.has("sharedId") || !param.has("displayFormTp"))
	{
		WOLOG_ERROR << "[TaskExecDbSetViewDisplayFromType] param error!";
		return E_FAIL;
	}

	UINT sheetStId = param.field_uint32("sheetStId");
    EtDbId viewId = GetEtDbId(param, "viewId");
	PCWSTR sharedId = param.field_str("sharedId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = m_spProtectionJudgement->CheckCanSetCoverDispType(sheetStId, viewId);
	if (FAILED(hr))
		return hr;

	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	IBook* pBook = pCtx->getBook();
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	IDBSharedLinkView* pSharedLink = spDbSheetView->GetSharedLink();
	if(pSharedLink == NULL)
		return E_DBSHEET_SHARED_NOT_EXIST;

	KDbDisplayFormType displayFormTp = DBDFT_Default;
	hr = m_pEncodeDecoder->DecodeDbDisplayForm(param.field_str("displayFormTp"), &displayFormTp);
	if (FAILED(hr))
		return hr;
	
	hr = pSharedLink->SetDisplayFormType(displayFormTp);
	return hr;
}

HRESULT TaskExecDbSetViewDisplayFromType::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbSetViewDisplayFromType::GetTag()
{
	return __X("dbsheet.setDisplayFormTp");
}

// ================== TaskExecDbSetQueryFieldIds ==================
TaskExecDbSetQueryFieldIds::TaskExecDbSetQueryFieldIds(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetQueryFieldIds::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetView_Query> spDbSheetView_Query = spDbSheetView;
	if (spDbSheetView_Query == nullptr)
		return E_INVALIDARG;
	
	IDbFieldsManager* pFieldManager = spDbSheetView->GetFieldsManager();
	int phoneFieldCnt = 0;
	std::vector<EtDbId> fieldIdVec;
	std::vector<KDbFilterCriteriaOpType> tpVec;
	std::vector<PCWSTR> customPromptVec;
	std::vector<BOOL> enableScanCodeToInputsVec;
	std::vector<BOOL> conditionCanBlanksVec;
	std::vector<BOOL> needSecondChecksVec;

	VarObj fieldIds = param.get("queryFieldIds");
	for (int i = 0; i < fieldIds.arrayLength_s(); ++i)
	{
		EtDbId fldId = GetEtDbId(fieldIds.at_s(i));
		if (fldId == INV_EtDbId)
			continue;
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		if (!spField)
			continue;
		ET_DbSheet_FieldType type = spField->GetType();
		if (type == Et_DbSheetField_Phone)
			++phoneFieldCnt;
		fieldIdVec.push_back(fldId);

		KDbFilterCriteriaOpType tp = spDbSheetView_Query->GetFieldDefaultCriteriaOpType(fldId);
		tpVec.push_back(tp);

		customPromptVec.push_back(__X(""));
		enableScanCodeToInputsVec.push_back(FALSE);
		BOOL bConditionCanBlank = FALSE;//默认值
		if(pCtx->isExecDirect())
		{
			//得清洗基于conditionCanBlank旧io的回放命令
			//根据GetConditionCanBlankForOldIO()的状态来清洗每个条件的bConditionCanBlank
			bConditionCanBlank = spDbSheetView_Query->GetConditionCanBlankForOldIO();
		}
		conditionCanBlanksVec.push_back(bConditionCanBlank);

		BOOL bneedSecondCheck = FALSE;
		needSecondChecksVec.push_back(bneedSecondCheck);
	}

	if (fieldIdVec.size() == 0 || 
		!(fieldIdVec.size() == tpVec.size() 
			&& fieldIdVec.size() == customPromptVec.size()
			&& fieldIdVec.size() == enableScanCodeToInputsVec.size()
			&& fieldIdVec.size() == conditionCanBlanksVec.size()
			&& fieldIdVec.size() == needSecondChecksVec.size()
		)
	)
	{
		return E_FAIL;
	}

	HRESULT hr = spDbSheetView_Query->SetQueryFields(fieldIdVec.data(), tpVec.data(), 
		customPromptVec.data(), enableScanCodeToInputsVec.data(),
		conditionCanBlanksVec.data(), needSecondChecksVec.data(),
		fieldIdVec.size());
	return hr;
}

HRESULT TaskExecDbSetQueryFieldIds::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanQueryOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetQueryFieldIds::GetTag()
{
	return __X("dbSheet.setQueryFieldIds");
}

// ================== TaskExecDbSetQueryFields ==================
TaskExecDbSetQueryFields::TaskExecDbSetQueryFields(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetQueryFields::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetView_Query> spDbSheetView_Query = spDbSheetView;
	if (spDbSheetView_Query == nullptr)
		return E_INVALIDARG;
	
	IDbFieldsManager* pFieldManager = spDbSheetView->GetFieldsManager();
	int phoneFieldCnt = 0;
	std::vector<EtDbId> fieldIdVec;
	std::vector<KDbFilterCriteriaOpType> tpVec;
	std::vector<PCWSTR> customPromptVec;
	std::vector<BOOL> enableScanCodeToInputsVec;
	std::vector<BOOL> conditionCanBlanksVec;
	std::vector<BOOL> needSecondChecksVec;
	BOOL bHasSetNeedSecondCheck = FALSE;//只有一个电话号码字段才能设置二次校验

	VarObj queryFields = param.get("queryFields");
	for (int i = 0; i < queryFields.arrayLength_s(); ++i)
	{
		VarObj queryFieldItem = queryFields.at_s(i);
		EtDbId fldId = GetEtDbId(queryFieldItem, "fieldId");
		if (fldId == INV_EtDbId)
			continue;

		KDbFilterCriteriaOpType op = DBFCOT_Null;
		HRESULT hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterCriteriaOpType(queryFieldItem.field_str("op"), &op);
		if(FAILED(hr))
			continue;

		//检查设置的op是否合法
		BOOL bValidOp = spDbSheetView_Query->IsValidCriteriaOpTp(fldId, op);
		if(!bValidOp)
		{
			WOLOG_ERROR << "[TaskExecDbSetQueryFields] fldId:" << fldId << "op:" << (int)op << " is unValid!";
			continue;
		}

		PCWSTR customPrompt = __X("");
		if(queryFieldItem.has("customPrompt"))
		{
			customPrompt = queryFieldItem.field_str("customPrompt");
		}

		BOOL enableScanCodeToInput = FALSE;
		if(queryFieldItem.has("enableScanCodeToInput"))
		{
			enableScanCodeToInput = queryFieldItem.field_bool("enableScanCodeToInput");
		}

		BOOL conditionCanBlank = FALSE;//默认值
		if(queryFieldItem.has("conditionCanBlank"))
		{
			//新命令一定会有这个参数。
			conditionCanBlank = queryFieldItem.field_bool("conditionCanBlank");
		}
		else if(pCtx->isExecDirect())
		{
			//得清洗基于conditionCanBlank旧io的回放命令
			conditionCanBlank = spDbSheetView_Query->GetConditionCanBlankForOldIO();
		}

		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(fldId, &spField);
		if (!spField)
			continue;
		
		BOOL needSecondCheck = FALSE;//默认值
		if(queryFieldItem.has("needSecondCheck"))
		{
			//新命令一定会有这个参数。
			needSecondCheck = queryFieldItem.field_bool("needSecondCheck");
			if(needSecondCheck)
			{
				if (spField->GetBaseFieldType() != Et_DbSheetField_Phone || bHasSetNeedSecondCheck)
				{
					//字段本身不是电话号码类型，或者已经有字段设置过二次校验了，后面没必要开启二次校验。
					needSecondCheck = FALSE;	
				}
				
			}
		}

		fieldIdVec.push_back(fldId);
		tpVec.push_back(op);

		customPromptVec.push_back(customPrompt);
		enableScanCodeToInputsVec.push_back(enableScanCodeToInput);
		conditionCanBlanksVec.push_back(conditionCanBlank);
		if(needSecondCheck)
		{
			bHasSetNeedSecondCheck = TRUE;
		}
		needSecondChecksVec.push_back(needSecondCheck);
	}


	if (fieldIdVec.size() == 0 || 
		!(fieldIdVec.size() == tpVec.size() 
			&& fieldIdVec.size() == customPromptVec.size()
			&& fieldIdVec.size() == enableScanCodeToInputsVec.size()
			&& fieldIdVec.size() == conditionCanBlanksVec.size()
			&& fieldIdVec.size() == needSecondChecksVec.size()
		)
	)
	{
		return E_FAIL;
	}

	HRESULT hr = spDbSheetView_Query->SetQueryFields(fieldIdVec.data(), tpVec.data(), 
		customPromptVec.data(), enableScanCodeToInputsVec.data(),
		conditionCanBlanksVec.data(), needSecondChecksVec.data(),
		fieldIdVec.size());
	return hr;
}

HRESULT TaskExecDbSetQueryFields::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanQueryOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetQueryFields::GetTag()
{
	return __X("dbSheet.setQueryFields");
}

// ================== TaskExecDbSetQueryConditionCanBlank ==================
TaskExecDbSetQueryConditionCanBlank::TaskExecDbSetQueryConditionCanBlank(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetQueryConditionCanBlank::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetView_Query> spDbSheetView_Query = spDbSheetView;
	if (spDbSheetView_Query == nullptr)
		return E_INVALIDARG;
	
	ASSERT(pCtx->isExecDirect());//期望后续前端都不要执行此命令！！！
	bool canBlank = param.field_bool("canBlank");
	HRESULT hr = spDbSheetView_Query->SetConditionCanBlankForOldIO(canBlank);

	if(pCtx->isExecDirect())
	{
		//回放时，把已有的查询条件的op都给遍历清洗成canBlank
		spDbSheetView_Query->ResetExistedFieldConditionCanBlank(canBlank);
	}

	return hr;
}

HRESULT TaskExecDbSetQueryConditionCanBlank::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanQueryOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetQueryConditionCanBlank::GetTag()
{
	return __X("dbSheet.setQueryConditionCanBlank");
}

// ================== TaskExecDbSetQueryNeedSecondCheck ==================
TaskExecDbSetQueryNeedSecondCheck::TaskExecDbSetQueryNeedSecondCheck(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetQueryNeedSecondCheck::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetView_Query> spDbSheetView_Query = spDbSheetView;
	if (spDbSheetView_Query == nullptr)
		return E_INVALIDARG;
	
	if(!pCtx->isExecDirect())
	{
		//期望后续前端都不要执行此命令！！
		return S_OK;
	}
	bool needCheck = param.field_bool("needCheck");
	//产品规则:如果是开启手机号码二次验证，则把用户的手机号码的查询条件的op给强制设置为“等于”
	if (needCheck)
	{
		spDbSheetView_Query->UpdatePhoneFieldCriteriaOpType(DBFCOT_Equals);
	}
	
	//这里要回落为给首个电话号码类型的字段设置开启二次校验
	HRESULT hr = spDbSheetView_Query->ResetExistedFieldNeedSecondCheck(needCheck);
	return hr;
}

HRESULT TaskExecDbSetQueryNeedSecondCheck::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanQueryOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetQueryNeedSecondCheck::GetTag()
{
	return __X("dbSheet.setQueryNeedSecondCheck");
}

// ================== TaskExecDbSetQueryCriteria ==================
TaskExecDbSetQueryCriteria::TaskExecDbSetQueryCriteria(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetQueryCriteria::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if (spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	ks_stdptr<IDBSheetView_Query> spDbSheetView_Query = spDbSheetView;
	if (spDbSheetView_Query == nullptr)
		return E_INVALIDARG;

	if (!param.has("querys"))
		return E_INVALIDARG;

	HRESULT hr = S_OK;
	IDbFilter* pFilter = spDbSheetView->GetMutableFilter();
	ks_stdptr<IDbFieldFilter> spFieldFilter;

	IDbFieldsManager* pFieldManager = spDbSheetView->GetFieldsManager();
	VarObj querys = param.get_s("querys");
	std::vector<int> validQueryIdx;
	for (int i = 0, c = querys.arrayLength_s(); i < c; ++i)
	{
		VarObj query = querys.at_s(i);
		EtDbId fldId = GetEtDbId(query, "fieldId");
		if (fldId == INV_EtDbId)
			continue;
		
		if (!spDbSheetView_Query->HasQueryFieldId(fldId))
			continue;

		validQueryIdx.push_back(i);
	}

	if (validQueryIdx.size() == 0)
		return S_OK;

	// 清除所有条件
	pFilter->ClearFilter();

	PCWSTR sharedId = pCtx->GetRedirectSharedId();
	ks_castptr<IKETUserConn> spUser = pCtx->getUser();
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (sharedId == nullptr)
	{
		ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
		pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
		if (spSharedLinkMgr)
			sharedId = spSharedLinkMgr->GetConnSharedLink(spUser);
	}

	for (int i = 0; i < validQueryIdx.size(); ++i)
	{
		VarObj query = querys.at_s(validQueryIdx[i]);
		EtDbId fldId = GetEtDbId(query, "fieldId");
		VarObj criteria = query.get("criteria");
		BOOL needCheck = spDbSheetView_Query->HasQueryFieldNeedSecondCheck(fldId);
		if (needCheck == TRUE && sharedId != nullptr)
		{
			ks_stdptr<IDbField> spField;
			pFieldManager->GetField(fldId, &spField);
			if (spField && spField->GetBaseFieldType() == Et_DbSheetField_Phone)
			{
				VarObj vValues = criteria.get("values");
				for (int32 i = 0, cnt = vValues.arrayLength(); i < cnt; ++i)
				{
					VarObj vValue = vValues.at(i);
					PCWSTR phone = vValue.field_str("value");
					if (!spUser->isVerifiedPhone(phone))
						return E_DBSHEET_QUERY_PHONE_INVALID;
				}
			}
		}
		hr = pFilter->AddFilter(fldId, &spFieldFilter);
		if (FAILED(hr))
			return hr;

		ks_stdptr<IDbFilterCriteria> spCriteria;
		hr = CreateDbFilterCriteria(criteria, spDbSheetView->GetSheetOp()->GetBook(), &spCriteria, false, true);
		if (FAILED(hr))
			return hr;

		hr = spFieldFilter->SetCriteria(spCriteria);
		if (FAILED(hr))
			return hr;
	}

	return hr;
}

HRESULT TaskExecDbSetQueryCriteria::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbSetQueryCriteria::GetTag()
{
	return __X("dbsheet.setQueryCriteria");
}

// ================== TaskExecUpdateAppSheetData ==================
TaskExecUpdateAppSheetData::TaskExecUpdateAppSheetData(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecUpdateAppSheetData::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	ks_stdptr<ISheet> spDbSheet;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	DbSheet::GetDbSheet(pBook, sheetStId, &spDbSheet);
	bool needResetApp = false;

	HRESULT hr = UpdateAppViewHelper::UpdateAppSheetData(m_wwb, pCtx, param, spDbSheet, needResetApp);

	if (SUCCEEDED(hr))
	{
		EtTaskPeripheral* peripheral = GetPeripheral();
		if (peripheral)
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			resObj.add_field_bool("needResetApp", needResetApp);
		}
	}

	return hr;
}

HRESULT TaskExecUpdateAppSheetData::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecUpdateAppSheetData::GetTag()
{
	return __X("dbsheet.updateAppSheetData");
}

// ================== TaskExecDbOnAfterDeleteApp ==================
// 接口目前没调用，只是作为兼容命令回放存在
TaskExecDbOnAfterDeleteApp::TaskExecDbOnAfterDeleteApp(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbOnAfterDeleteApp::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	if (!param.has("dbSheetStId"))
	{
		WOLOG_ERROR << "[TaskExecDbOnAfterDeleteApp] param error!";
		return E_INVALIDARG;
	}

	UINT dbSheetStId = param.field_uint32("dbSheetStId");
	UINT relatedEtSheetStId = 0;
	//判断删除应用时 是否能找到关联的"et_sheet"
	bool bHasRelatedEtSheet = FindAppHelper::FindAppRelatedEtSheetStIdByDbSheetStId(m_wwb, dbSheetStId, relatedEtSheetStId);
	if(!bHasRelatedEtSheet)
	{
		//若找不到关联的"et_sheet",说明是以下几种情况:
		//1.关联的是非查询应用
		//2.关联的是查询应用 且
			// 2.1 从模板创建应用
			// 2.2 创建查询应用的数据源为一个db_sheet
		if(!param.has("viewId") || !param.has("sharedId"))
		{
			WOLOG_ERROR << "[TaskExecDbOnAfterDeleteApp] param error!";
			return E_INVALIDARG;
		}
		EtDbId viewId = GetEtDbId(param, "viewId");
		PCWSTR sharedId = param.field_str("sharedId");
		ks_stdptr<IDBSheetView> spDbSheetView;
		GetDBSheetView(dbSheetStId, viewId, &spDbSheetView, NULL);
		if (!spDbSheetView)
			return E_DBSHEET_VIEW_NOT_FOUND;

		ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
		IBook* pBook = pCtx->getBook();
		pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
		IDBSharedLinkView* pSharedLink = spDbSheetView->GetSharedLink();
		if (pSharedLink == NULL)
			return E_DBSHEET_SHARED_NOT_EXIST;
		//对分享链接取消""和应用关联的属性"
		return pSharedLink->SetIsUsedForApp(FALSE);
	}
	else
	{
		//走到这里说明 关联的是查询应用 并且 此db_sheet是基于et_sheet为数据源创建出来的，删除应用需要把这个db_sheet给一并删除
		return DeleteAppHelper::DelOneAppRelatedDbSheet(m_wwb, dbSheetStId, INV_EtDbId);
	}
	
}

HRESULT TaskExecDbOnAfterDeleteApp::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	return S_OK;
}

PCWSTR TaskExecDbOnAfterDeleteApp::GetTag()
{
	return __X("dbsheet.onAfterDeleteApp");
}

// ================== TaskExecDbConvertToEtSheet ==================
TaskExecDbConvertToEtSheet::TaskExecDbConvertToEtSheet(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecDbConvertToEtSheet::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	bool bOnlyViewData = param.field_bool("bOnlyViewData");
	if(bOnlyViewData)
	{
		return _doConvertViewDataToEtSheet(param);
	}
	else
	{
		//Todo:目前暂时不支持bOnlyViewData为false的情况；只是为了后续拓展性，先留着这里
		//后续如果要支持导出该sheet的所有数据，则改造这里即可。
		return E_FAIL;
	}
}

HRESULT TaskExecDbConvertToEtSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckViewCanVisit(sheetStId, viewId);
}

PCWSTR TaskExecDbConvertToEtSheet::GetTag()
{
	return __X("dbsheet.convertToEtSheet");
}

HRESULT TaskExecDbConvertToEtSheet::_doConvertViewDataToEtSheet(binary_wo::VarObj& param)
{
	UINT dbSheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	DbSheetViewDataToEtSheetExporter exporter(m_wwb->GetCoreWorkbook(), dbSheetStId, viewId);
	HRESULT hr = exporter.Init();
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[TaskExecDbConvertToEtSheet] exporter init failed!";
		return E_FAIL;
	}

	if (param.has("needCopyDataFormat"))
		exporter.setNeedCopyDataFormat(param.field_bool("needCopyDataFormat"));

	hr = exporter.Exec();
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[TaskExecDbConvertToEtSheet] exporter Exec failed!";
		return E_FAIL;
	}
	EtTaskPeripheral* peripheral = GetPeripheral();
	if(peripheral) 
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag()); // cmdrespone 需要
		resObj.add_field_uint32("newSheetStId", exporter.GetResultSheetStId());
	}
	return hr;
}

TaskExecDbConvertFromEtSheet::TaskExecDbConvertFromEtSheet(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
	
}

HRESULT TaskExecDbConvertFromEtSheet::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();

	const VarObj param = cmd->cast().get("param");
	IDX iSheet = GetSheetIdx(param);
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(iSheet, &spSheet);
	if(!spSheet)
	{
		WOLOG_ERROR << "[TaskExecConvertEtSheet2Db] param sheetStId error!";
		return E_FAIL;
	}
	if(spSheet->IsDbSheet())
	{
		WOLOG_ERROR << "[TaskExecConvertEtSheet2Db] param sheetStId is dbsheet,not match!";
		return E_FAIL;
	}

	bool hasHiddenProp = ctx->getProtectionCtx()->isSheetHasHiddenProperty(spSheet);
	if(hasHiddenProp)
	{
		WOLOG_INFO << "[TaskExecConvertEtSheet2Db] sheetIdx:" << iSheet << ", hasHiddenProp! ban to exec!";
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
	}

	if (!param.has("xlsxImport") || !param.has("defaultName"))
	{
		WOLOG_ERROR << "[TaskExecConvertEtSheet2Db] param xlsxImport or defaultName not exist!";
		return E_FAIL;
	}
	VarObj xlsxImport = param.get("xlsxImport");
	VarObj defaultName = param.get("defaultName");

	ks_stdptr<IUnknown> spUnknown;
	VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	if(!spUnknown)
	{
		WOLOG_ERROR << "[TaskExecConvertEtSheet2Db] edDBUserGroups ExtDataItem not exist!";
		return E_FAIL;
	}
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	if (spDBUserGroups)
		spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if(!spProtectionJudgement)
	{
		WOLOG_ERROR << "[TaskExecConvertEtSheet2Db] spProtectionJudgement empty!";
		return E_FAIL;
	}

    Et2DbImporterSameProcess importer(m_wwb->GetCoreWorkbook(), m_wwb->GetCoreWorkbook(), iSheet, INVALIDIDX, ctx,
                                      false, defaultName, xlsxImport, spProtectionJudgement.get(), true, true);
    HRESULT hr = importer.Exec();
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[TaskExecConvertEtSheet2Db] importer do Exec() Fail!!";
		return hr;
	}
	
	//最后一个sheet作为应用的sheet
	IDX newDbSheetIdx = wo::util::getLastSheetIdx(pBook);
	ks_stdptr<Worksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	long sheetCount = 0;
	spWorksheets->get_Count(&sheetCount);
	if(newDbSheetIdx < 0 || newDbSheetIdx >= sheetCount)
	{
		WOLOG_ERROR << "[TaskExecConvertEtSheet2Db] appSheetIdx < 0 || appSheetIdx >= sheetCount error";
		return E_FAIL;
	}
	UINT newDbSheetStId = 0;
	pBook->RTSheetToSTSheet(newDbSheetIdx , &newDbSheetStId);

	//返回结果
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag()); // cmdrespone 需要
		resObj.add_field_uint32("activeSheetId", newDbSheetStId);
	}
	
	return S_OK;
}

PCWSTR TaskExecDbConvertFromEtSheet::GetTag()
{
	return __X("dbsheet.convertFromEtSheet");
}

HRESULT TaskExecDbConvertFromEtSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	if (ctx->getProtectionCtx()->isConnSharedLink())
		return E_OPERATION_NOT_SUPPORTED_ON_SHARED_SHEET;
	return S_OK;
}

TaskExecUpdateAppVersion::TaskExecUpdateAppVersion(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
	
}

HRESULT TaskExecUpdateAppVersion::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	VarObj param = cmd->cast().get("param");
	UpdateAppVersionHelper::UpdateDbSheetToAppMgr updateDbSheetToAppMgr;
	HRESULT hr = UpdateAppVersionHelper::UpdateAppVersion(m_wwb, ctx, updateDbSheetToAppMgr, param);


	return hr;

}

HRESULT TaskExecUpdateAppVersion::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

PCWSTR TaskExecUpdateAppVersion::GetTag()
{
	return __X("dbsheet.updateAppVersion");
}

// ================== TaskExecResetAppSheetSharedIds ==================
TaskExecResetAppSheetSharedIds::TaskExecResetAppSheetSharedIds(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecResetAppSheetSharedIds::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	HRESULT hr = E_FAIL;
	VarObj param = cmd->cast().get("param");
	if(!param.has("resetSharedIds"))
		return E_FAIL;
	
	std::unordered_map<ks_wstring, ks_wstring, STR_HASH> resetSharedIdsMap;
	VarObj resetSharedIdsArr = param.get_s("resetSharedIds");
	for (int i = 0; i < resetSharedIdsArr.arrayLength_s(); ++i)
	{
		VarObj resetSharedIdObj = resetSharedIdsArr.at_s(i);
		PCWSTR oldSharedId = resetSharedIdObj.field_str("oldSharedId");
		PCWSTR newSharedId = resetSharedIdObj.field_str("newSharedId");
		resetSharedIdsMap.insert(std::make_pair(oldSharedId, newSharedId));
	}
	if(resetSharedIdsMap.empty())
		return S_OK;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	ks_stdptr<Worksheets> spWorksheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	long sheetCount = 0;
	spWorksheets->get_Count(&sheetCount);
	for (IDX idx = 0; idx < sheetCount; idx++)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(idx, &spSheet);
		if (!spSheet || !spSheet->IsAppSheet())
			continue;
		ks_stdptr<IAppSheetData> spAppSheetData;
		spSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spAppSheetData);
		if (!spAppSheetData)
			return E_FAIL;
		for (int i = 0; i < spAppSheetData->GetAppCount(); ++i)
		{
			IAirApp* pAirApp = spAppSheetData->GetApp(i);
			PCWSTR oldSharedId = pAirApp->GetSharedId();
			if(resetSharedIdsMap.find(oldSharedId) == resetSharedIdsMap.end())
				continue;
			ks_wstring newSharedId = resetSharedIdsMap.at(oldSharedId);
			pAirApp->SetSharedId(newSharedId.c_str());
			switch (pAirApp->GetCategory())
			{
				case KSheet_AirAppCat_DbSheetView:
				{
					ks_stdptr<IAirApp_DBView> spAirAppView = pAirApp;
					hr = DbSheet::ResetAppDBViewSharedId(spAirAppView, newSharedId.c_str());
					if(FAILED(hr))
					{
						WOLOG_ERROR << "[TaskExecResetAppSheetSharedIds] reset sharedId fialed!";
						return hr;
					}
					
					break;
				}
			}
		}
	}
	return S_OK;
}

HRESULT TaskExecResetAppSheetSharedIds::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

PCWSTR TaskExecResetAppSheetSharedIds::GetTag()
{
	return __X("dbsheet.resetAppSheetSharedIds");
}

// ================== TaskExecDbRangeFill ==================
TaskExecDbRangeFill::TaskExecDbRangeFill(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRangeFill::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (!spDbSheetView)
		return E_DBSHEET_VIEW_NOT_FOUND;

	// 选择range
	ks_stdptr<IDBSheetRange> spSrcRg;
	HRESULT hr = GetDBRange(spDbSheetView, param.get("srcRg"), false, false, &spSrcRg);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetRange> spTarRg;
	hr = GetDBRange(spDbSheetView, param.get("tarRg"), false, false, &spTarRg);
	if (FAILED(hr))
		return hr;

	if (spSrcRg->GetRecordCnt() < 1 || spSrcRg->GetFieldCnt() < 1 || spTarRg->GetRecordCnt() < 1 || spTarRg->GetFieldCnt() < 1)
		return E_INVALIDARG;

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
	IDBSheetOp* pSheetOp = spDbSheetView->GetSheetOp();
	bool bAlterDueDateAutomation = DbSheet::HasDueDateAutomation(pSheetOp, spTarRg);
	bool bExceededMaxBatchRecords = false;
	if (bAlterDueDateAutomation)
	{
		ks_stdptr<IDbAutomations> m_spAutomations;
		pBook->GetExtDataItem(edBookDbAutomations, (IUnknown**)&m_spAutomations);
		IDbAutomationConfig *pConfig = m_spAutomations->GetConfig();
		if (spTarRg->GetRecordCnt() > pConfig->GetMaxBatchRecords())
		{
			bExceededMaxBatchRecords = true;
			spTarRg->ShrinkRecords(pConfig->GetMaxBatchRecords());
		}
	}

	std::unique_ptr<DbReversedLinkParam> upDbLinkParam;
	std::unique_ptr<DbSheet::DbLinkHlp> upDbLinkHlp;
	IDbFieldsManager* pFieldManager = pSheetOp->GetFieldsManager();
	for (int i = 0; i < spSrcRg->GetFieldCnt(); ++i)
	{
		ks_stdptr<IDbField> spField;
		pFieldManager->GetField(spSrcRg->GetFieldId(i), &spField);
		if (spField->GetType() == Et_DbSheetField_Link)
		{
			upDbLinkParam.reset(new DbReversedLinkParam(
				DbReversedLinkParam::TryModifyField2Multiple));
			upDbLinkHlp.reset(new DbSheet::DbLinkHlp(m_wwb->GetCoreWorkbook()->GetBook(), *upDbLinkParam));
			break;
		}
	}
	FillResultStatus fillResultStatus {INV_EtDbId, FALSE, FALSE};
	hr = pSheetOp->FillToRange(spSrcRg, spTarRg, fillResultStatus);
	bool bNotMatchUniqueOccur = fillResultStatus.notMatchUniqueFldId != INV_EtDbId;
	// 产品要求，单列填充时，拦截禁止重复属性的列和仅允许拍摄上传的列, 并且在前端提示
	// 多列填充时，拦截禁止重复属性的列和仅允许拍摄上传的列，对于前者需要在前端提示。其他列期望正常填充。
	if(bNotMatchUniqueOccur)
	{
		if(spSrcRg->GetFieldCnt() == 1)
		{
			hr = E_DBSHEET_VALUE_NOT_UNIQUE_IN_FIELD;
		}
	} 
	else if (alg::BOOL2bool(fillResultStatus.fillOnlyAllowUploadAttachmentField))
	{
		if(spSrcRg->GetFieldCnt() == 1)
		{
			// 目前如果多列填充的情况下，填充了仅允许拍摄上传的列是不需要在前端提示的，以下的错误玛只有在单列填充且为仅允许拍摄上传的列时会返回
			hr = E_DBSHEET_RANGEFILL_DST_ONLY_ALLOW_UPLOAD_BY_PHONE;
		}
	}
	else if (alg::BOOL2bool(fillResultStatus.fillOnlyAllowUploadBarCodeField))
	{
		if(spSrcRg->GetFieldCnt() == 1)
		{
			// 目前如果多列填充的情况下，填充了仅允许扫码录入的列是不需要在前端提示的，以下的错误玛只有在单列填充且为仅允许扫码录入的列时会返回
			hr = E_DBSHEET_RANGEFILL_DST_ONLY_ALLOW_SCAN_BY_CAMERA;
		}
	}

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		if(SUCCEEDED(hr))
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			resObj.add_field_str("cmdName", GetTag());
			resObj.add_field_bool("alterDueDateAutomation", bAlterDueDateAutomation);
			resObj.add_field_bool("exceededMaxBatchRecords", bExceededMaxBatchRecords);
			if(bNotMatchUniqueOccur)
			{
				binary_wo::VarObj notMatchUniqueInfoObj = resObj.add_field_struct("notMatchUniqueInfo");
				notMatchUniqueInfoObj.add_field_int32("sheetStId", sheetStId);
				AddEtDbId(notMatchUniqueInfoObj, "fieldId", fillResultStatus.notMatchUniqueFldId);
			}
		}
		else if(hr == E_DBSHEET_VALUE_NOT_UNIQUE_IN_FIELD)
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			resObj.add_field_str("cmdName", GetTag()); // cmdrespone 需要
			binary_wo::VarObj notMatchUniqueInfoObj = resObj.add_field_struct("notMatchUniqueInfo");
			notMatchUniqueInfoObj.add_field_int32("sheetStId", sheetStId);
			AddEtDbId(notMatchUniqueInfoObj, "fieldId", fillResultStatus.notMatchUniqueFldId);
		}
	}

	return hr;
}

PCWSTR TaskExecDbRangeFill::GetTag()
{
	return __X("dbSheet.rangeFill");
}

HRESULT TaskExecDbRangeFill::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

// ================== TaskExecDbSetRangeXf ==================
TaskExecDbSetRangeXf::TaskExecDbSetRangeXf(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetRangeXf::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	IDX sheetIdx = GetSheetIdx(param);
	IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (pWorkSheet == nullptr)
		return E_FAIL;

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (spDbSheetView == nullptr)
		return E_DBSHEET_VIEW_NOT_FOUND;

	// 选择range
	ks_stdptr<IDBSheetRange> spRange;
	binary_wo::VarObj rgs = param.get_s("rgs");
	HRESULT hr = GetDBRange(spDbSheetView, rgs, false, false, &spRange);
	if (FAILED(hr))
		return hr;

	hr = CheckProtectionSheetRange(spRange, sheetStId);
	if (FAILED(hr))
		return hr;

	binary_wo::VarObj varXf = param.get("xf");
	KXF xf;
	KXFMASK mask;
	DecodeXf(varXf, xf, mask);

	if (!mask.IsMaskNone())
	{
		IDBSheetOp *pDbSheetOp = spDbSheetView->GetSheetOp();
		hr = pDbSheetOp->SetXf(spRange, &mask, &xf);
		if (FAILED(hr))
			return hr;
	}

	return S_OK;
}

PCWSTR TaskExecDbSetRangeXf::GetTag()
{
	return __X("dbSheet.setRangeXf");
}

HRESULT TaskExecDbSetRangeXf::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

// ================== TaskExecDbClearRangeXf ==================
TaskExecDbClearRangeXf::TaskExecDbClearRangeXf(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbClearRangeXf::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	IDX sheetIdx = GetSheetIdx(param);
	IKWorksheet* pWorkSheet = m_wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (pWorkSheet == nullptr)
		return E_FAIL;

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (spDbSheetView == nullptr)
		return E_DBSHEET_VIEW_NOT_FOUND;

	// 选择range
	ks_stdptr<IDBSheetRange> spRange;
	binary_wo::VarObj rgs = param.get_s("rgs");
	HRESULT hr = GetDBRange(spDbSheetView, rgs, false, false, &spRange);
	if (FAILED(hr))
		return hr;

	hr = CheckProtectionSheetRange(spRange, sheetStId);
	if (FAILED(hr))
		return hr;

	IDBSheetOp *pDbSheetOp = spDbSheetView->GetSheetOp();
	hr = pDbSheetOp->ClearXf(spRange);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

PCWSTR TaskExecDbClearRangeXf::GetTag()
{
	return __X("dbSheet.clearRangeXf");
}

HRESULT TaskExecDbClearRangeXf::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return S_OK;
}

// ================== TaskExecDbSetFieldTitleFormat ==================
TaskExecDbSetFieldTitleFormat::TaskExecDbSetFieldTitleFormat(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetFieldTitleFormat::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	binary_wo::VarObj format = param.get("format");
	ARGB bgColor = format.field_uint32("bgColor");
	HRESULT hr = S_OK;

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (spDbSheetView == nullptr)
		return E_DBSHEET_VIEW_NOT_FOUND;
	if (spDbSheetView->GetType() != et_DBSheetView_Grid)
		return E_FAIL;
	ks_stdptr<IDBSheetView_Grid> spGridView = spDbSheetView;

	VarObj fieldsId = param.get_s("fieldsId");
	for (int i = 0, c = fieldsId.arrayLength_s(); i < c; ++i)
	{
		EtDbId fieldId = GetEtDbId(fieldsId.at_s(i));

		hr = spGridView->SetFieldTitleFormat(fieldId, bgColor);
		if (FAILED(hr))
			return hr;
	}

	return S_OK;
}

PCWSTR TaskExecDbSetFieldTitleFormat::GetTag()
{
	return __X("dbSheet.setFieldTitleFormat");
}

HRESULT TaskExecDbSetFieldTitleFormat::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetFieldTitleFormat(sheetStId, viewId);
}

// ================== TaskExecDbRemoveFieldTitleFormat ==================
TaskExecDbRemoveFieldTitleFormat::TaskExecDbRemoveFieldTitleFormat(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbRemoveFieldTitleFormat::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	HRESULT hr = S_OK;

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (spDbSheetView == nullptr)
		return E_DBSHEET_VIEW_NOT_FOUND;
	if (spDbSheetView->GetType() != et_DBSheetView_Grid)
		return E_FAIL;
	ks_stdptr<IDBSheetView_Grid> spGridView = spDbSheetView;

	VarObj fieldsId = param.get_s("fieldsId");
	for (int i = 0, c = fieldsId.arrayLength_s(); i < c; ++i)
	{
		EtDbId fieldId = GetEtDbId(fieldsId.at_s(i));

		hr = spGridView->RemoveFieldTitleFormat(fieldId);
		if (FAILED(hr))
			return hr;
	}

	return S_OK;
}

PCWSTR TaskExecDbRemoveFieldTitleFormat::GetTag()
{
	return __X("dbSheet.removeFieldTitleFormat");
}

HRESULT TaskExecDbRemoveFieldTitleFormat::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetFieldTitleFormat(sheetStId, viewId);
}

// ================== TaskExecDbSetViewBackgroundImage ==================
TaskExecDbSetViewBackgroundImage::TaskExecDbSetViewBackgroundImage(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetViewBackgroundImage::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	HRESULT hr = S_OK;

	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (spDbSheetView == nullptr)
		return E_DBSHEET_VIEW_NOT_FOUND;
	
	if(!param.has("attachmentId") || !param.has("source") || !param.has("position") || !param.has("blurredDegree"))
	{
		WOLOG_ERROR << "[TaskExecDbSetViewBackgroundImage] param error!";
		return E_FAIL;
	}
	
	PCWSTR attachmentId = param.field_str("attachmentId");
	PCWSTR source = param.field_str("source");
	UINT position = param.field_int32("position");
	UINT blurredDegree = param.field_int32("blurredDegree");

	hr = spDbSheetView->SetBackgroundImageAttachment(attachmentId);
	if (FAILED(hr))
		return hr;

	hr = spDbSheetView->SetBackgroundImageSource(source);
	if (FAILED(hr))
		return hr;
	
	hr = spDbSheetView->SetBackgroundImagePosition(position);
	if (FAILED(hr))
		return hr;

	hr = spDbSheetView->SetBackgroundImageBlurredDegree(blurredDegree);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT TaskExecDbSetViewBackgroundImage::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanSetBackgroundImage(sheetStId, viewId);
}

PCWSTR TaskExecDbSetViewBackgroundImage::GetTag() 
{
	return __X("dbSheet.setViewBackgroundImage");
}

// ================== TaskExecDbClearTempSettings ==================
TaskExecDbClearTempSettings::TaskExecDbClearTempSettings(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbClearTempSettings::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if (spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;

	return spDbSheetView->ClearTempSettings();
}

HRESULT TaskExecDbClearTempSettings::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecDbClearTempSettings::GetTag()
{
	return __X("dbSheet.clearTempSettings");
}

// ================== TaskExecDbSyncTempSettings ==================
TaskExecDbSyncTempSettings::TaskExecDbSyncTempSettings(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbSyncTempSettings::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if (spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;

	return spDbSheetView->SyncTempSettings(spDbSheetView);
}

HRESULT TaskExecDbSyncTempSettings::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecDbSyncTempSettings::GetTag()
{
	return __X("dbSheet.syncTempSettings");
}

// ================== TaskExecDbSaveAsNewViewByTempSettings ==================
TaskExecDbSaveAsNewViewByTempSettings::TaskExecDbSaveAsNewViewByTempSettings(KEtWorkbook* wwb)
	: TaskExecDbCopyView(wwb)
{
}

HRESULT TaskExecDbSaveAsNewViewByTempSettings::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId curViewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spCurDbSheetView;
	GetDBSheetView(sheetStId, curViewId, &spCurDbSheetView, NULL);
	if (spCurDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = TaskExecDbCopyView::operator()(cmd, pCtx);
	if (FAILED(hr))
		return hr;

	EtTaskPeripheral* parent = TaskExecDbCopyView::GetPeripheral();
	EtDbId newViewId = GetEtDbId(parent->resSelf(), "viewId");
	ks_stdptr<IDBSheetView> spNewDbSheetView;
	GetDBSheetView(sheetStId, newViewId, &spNewDbSheetView, NULL);
	if (spNewDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
		AddEtDbId(resObj, "viewId", newViewId);
	}
	return spNewDbSheetView->SyncTempSettings(spCurDbSheetView);
}

HRESULT TaskExecDbSaveAsNewViewByTempSettings::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return TaskExecDbCopyView::CheckCmdPermission(cmd, pCtx);
}

PCWSTR TaskExecDbSaveAsNewViewByTempSettings::GetTag()
{
	return __X("dbSheet.saveAsNewViewByTempSettings");
}

// ================== TaskExecDbSetComposeFilter =================
TaskExecDbSetComposeFilter::TaskExecDbSetComposeFilter(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetComposeFilter::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if(spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;

	hr = checkCompose(param);
	if (hr == S_OK)
		hr = SetComposeFilter(spDbSheetView, param.get("compose"));

	return hr;
}

HRESULT TaskExecDbSetComposeFilter::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*)
{
	binary_wo::VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanFilterRelatedOp(sheetStId, viewId);
}

PCWSTR TaskExecDbSetComposeFilter::GetTag()
{
	return __X("dbSheet.setComposeFilter");
}

HRESULT TaskExecDbSetComposeFilter::checkCompose(const binary_wo::VarObj& param)
{
	if (!param.has("compose"))
		return S_FALSE;

	if (!param.has("modifyTar") || xstrcmp(param.field_str("modifyTar"), __X("Conn")) != 0)
	{
		WOLOG_ERROR << "[TaskExecDbSetComposeFilter] compose filter param invalid.";
		return E_DBSHEET_FILTER_INVALID;
	}
	return S_OK;
}

// ================== TaskExecDbPermissionsUpgrade =================
TaskExecDbPermissionsUpgrade::TaskExecDbPermissionsUpgrade(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbPermissionsUpgrade::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	binary_wo::VarObj param = cmd->cast().get("param");

	if (!gs_callback || !gs_callback->convertUsersPermission)
		return E_FAIL;

	ks_stdptr<IUnknown> spUnknown;
	VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	ASSERT(spDBUserGroups);
	if (!spDBUserGroups)
		return E_FAIL;

	if (spDBUserGroups->GetGroupCnt() == 0)
		return E_FAIL;

	bool isClearUserGroups = param.has("isClearUserGroups") && param.field_bool("isClearUserGroups");

	binary_wo::BinWriter binWriter;
	KSerialWrapBinWriter acpt(binWriter, pCtx);
	// connId
	acpt.addString("connId", pCtx->getUser()->connID());
	acpt.addBool("isClearUserGroups", isClearUserGroups);

	// 清理旧权限
	if (isClearUserGroups)
	{
		binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};
		WebInt result = gs_callback->convertUsersPermission(&slice);
		if (result != WO_OK)
			return E_FAIL;

		spDBUserGroups->Clear();
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
		return S_OK; 
	}

	binary_wo::VarObj permissionNames = param.get("permissionNames");
	ks_stdptr<IDBProtection> spEditorProtection;
	spEditorProtection.attach(m_spProtectionJudgement->CreateDBProtectionObj());
	spEditorProtection->SetDBPermissonName(permissionNames.field_str("customEditor"));

	ks_stdptr<IDBProtection> spReporterProtection;
	spReporterProtection.attach(m_spProtectionJudgement->CreateDBProtectionObj());
	spReporterProtection->SetDBPermissonName(permissionNames.field_str("customReaderSubmitter"));

	ks_stdptr<IDBUserGroup> visitorGroup;
	spDBUserGroups->GetDefaultGroup(&visitorGroup);
	if (!visitorGroup)
		return E_FAIL;

	HRESULT hr = SetPermissionContent(param, spEditorProtection, spReporterProtection);
	if (FAILED(hr))
		return hr;

	// permissions
	{
		sa::Leave permissions(sa::enterArray(&acpt, "permissions"));

		auto SerialPermission = [&](IDBProtection* pProtection)
		{
			if (!pProtection)
				return;

			sa::Leave protection(sa::enterStruct(&acpt, nullptr));
			pProtection->SerialDBPermission(&acpt, TRUE);
		};

		SerialPermission(spEditorProtection);
		SerialPermission(spReporterProtection);
	}

	// usersPermission
	{
		sa::Leave usersPermissionStruct(sa::enterStruct(&acpt, "usersPermission"));
		for (int i = 0; i < spDBUserGroups->GetGroupCnt(); ++i)
		{
			ks_stdptr<IDBUserGroup> spDBUserGroup;
			spDBUserGroups->GetGroup(i, &spDBUserGroup);
			if (!spDBUserGroup)
				continue;

			PCWSTR corePermissionId = __X("");
			KDBProtectionType type = spDBUserGroup->GetProtectionType();
			if (type == DBPT_Editor)
				corePermissionId = spEditorProtection->GetDBPermissonId();
			else if (type == DBPT_ReaderSubmitter)
				corePermissionId = spReporterProtection->GetDBPermissonId();

			if (spDBUserGroup->GetId() == visitorGroup->GetId())
			{
				KDBProtectionType visitorProtectionType = visitorGroup->GetProtectionType();
				sa::Leave guestStruct(sa::enterStruct(&acpt, "guest"));
				SerializeUserPermission(visitorProtectionType, corePermissionId, acpt);
			}

			for (int j = 0; j < spDBUserGroup->GetUserIDCount(); ++j)
			{
				WebStr userId = nullptr;
				spDBUserGroup->GetUserIDByIdx(j, &userId);
				if (!userId)
					continue;

				std::string userIdStr = QString::fromUtf16(userId).toStdString();
				sa::Leave permission(sa::enterStruct(&acpt, userIdStr.c_str(), true));
				SerializeUserPermission(type, corePermissionId, acpt);
			}
		}
	}

	if (!pCtx->isExecDirect())
	{
		binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
		WebSlice slice = {shbt.get(), binWriter.writeLength()};
		WebInt result = gs_callback->convertUsersPermission(&slice);
		if (result != WO_OK)
		{
			if (result == WO_WAIT_TIMEOUT)
				return E_DBSHEET_WAIT_UPGRADE_PERMISSION_TIMEOUT;
			else if (result == WO_NO_PERMISSION_UPGRADE_ADVANCED_PERMISSION)
				return E_DBSHEET_NO_PERMISSION_UPGRADE_ADVANCED_PERMISSION;

			WOLOG_INFO << "convertUsersPermission: callback return failed";
			return E_FAIL;
		}
	}

	m_spProtectionJudgement->AddDBProtection(spEditorProtection);
	m_spProtectionJudgement->AddDBProtection(spReporterProtection);
	// 执行成功，清理旧权限，不允许再undo/redo
	spDBUserGroups->Clear();
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
	return S_OK;
}

HRESULT TaskExecDbPermissionsUpgrade::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecDbPermissionsUpgrade::GetTag()
{
	return __X("dbsheet.upgradePermissions");
}

HRESULT TaskExecDbPermissionsUpgrade::SetPermissionContent(binary_wo::VarObj& param, IDBProtection* pEditorProtection, IDBProtection* pReporterProtection)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
	INT sheetCnt = 0;
	pBook->GetSheetCount(&sheetCnt);
	for (INT i = 0; i < sheetCnt; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		pBook->GetSheet(i, &spSheet);
		if (spSheet == nullptr)
			continue;
		
		UINT sheetStId = spSheet->GetStId();

		SHEETTYPE sheetType = stUnknown;
		spSheet->GetFullType(&sheetType);
		switch(sheetType)
		{
		case stGrid_DB: // 数据表
		{
			// 查看范围和编辑范围为所有记录
			IDBSheetProtection* pEditorSheetPermission = pEditorProtection->AddSheetPermission(DBSheet_Permission_Edit, sheetStId);
			pEditorSheetPermission->SetRecordProtectType(DBSCT_All);

			// 1、查看范围为所有记录；2、编辑范围为创建者 = 我的记录；3、所有数据表默认帮用户添加一列“创建者”；
			// 查询数据表中是否存在“创建者”列
			EtDbId fldId = INV_EtDbId;
			HRESULT hr = AddCreatorCol(param, spSheet, &fldId);
			if (FAILED(hr))
				return hr;

			IDBSheetProtection* pSheetProtection = pReporterProtection->AddSheetPermission(DBSheet_Permission_Edit, sheetStId);
			pSheetProtection->SetRecordProtectType(DBSCT_Custom);
			hr = SetCriterionProtection(pSheetProtection, spSheet, fldId);
			if (FAILED(hr))
				return hr;

			break;
		}
		case stDashBoard: // DB的仪表盘
		{
			pEditorProtection->AddSheetPermission(DBSheet_Permission_View, sheetStId);
			pReporterProtection->AddSheetPermission(DBSheet_Permission_View, sheetStId);
			break;
		}
		case stFlexPaper: // 轻文档（说明文档）
		{
			pEditorProtection->AddSheetPermission(DBSheet_Permission_View, sheetStId);
			pReporterProtection->AddSheetPermission(DBSheet_Permission_View, sheetStId);
			break;
		}
		default: // 无权限
		{
			pEditorProtection->AddSheetPermission(DBSheet_Permission_NoPermission, sheetStId);
			pReporterProtection->AddSheetPermission(DBSheet_Permission_NoPermission, sheetStId);
			break;
		}
		}
	}
	return S_OK;
}

HRESULT TaskExecDbPermissionsUpgrade::AddCreatorCol(binary_wo::VarObj& param, ISheet* pSheet, EtDbId* pFldId)
{
	ks_stdptr<IDBSheetOp> spDbSheetOp;
	DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp);
	if (!spDbSheetOp)
		return E_FAIL;

	IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();
	if (pFieldsMgr->FieldCnt(Et_DbSheetField_CreatedBy) > 0)
	{
		const IDBIds* pFields = spDbSheetOp->GetAllFields();
		for (UINT32 i = 0; i < pFields->Count(); ++i)
		{
			ks_stdptr<IDbField> spField;
			EtDbId fldId = pFields->IdAt(i);
			pFieldsMgr->GetField(fldId, &spField);
			if (spField && spField->GetType() == Et_DbSheetField_CreatedBy)
			{
				*pFldId = fldId;
				return S_OK;
			}
		}
	}

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetViews(pSheet->GetStId(), &spDbSheetViews);
	if (!spDbSheetViews)
		return E_FAIL;

	ks_stdptr<IDBSheetView> spDbSheetView;
	HRESULT hr = spDbSheetViews->GetItemAt(0, Et_DBSheetViewUse_ForDb, &spDbSheetView);
	if (!spDbSheetView)
		return E_FAIL;

	ks_stdptr<IDBSheetRange> spDbRange;
	hr = DbSheet::AddField(spDbSheetView, Et_DbSheetField_CreatedBy, param.get("createField"), &spDbRange);
	if (FAILED(hr))
		return hr;

	*pFldId = spDbRange->GetFieldId(0);

	return S_OK;
}

HRESULT TaskExecDbPermissionsUpgrade::SetCriterionProtection(IDBSheetProtection* pSheetProtection, ISheet* pSheet, EtDbId fldId)
{
	IDBRecordProtection* pRecordProtection = pSheetProtection->GetRecordProtect();
	IDbFilter* pVisibleFilter = pRecordProtection->GetVisibleFilter();
	pVisibleFilter->SetOperator(DBFOT_True);
	
	IDbFilter* pEditableFilter = pRecordProtection->GetEditableFilter();
	pEditableFilter->SetOperator(DBFOT_And);

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp);
	if (!spDbSheetOp)
		return E_FAIL;

	ks_stdptr<IDbFieldFilter> spFieldFilter;
	HRESULT hr = pEditableFilter->AddFilter(fldId, &spFieldFilter);
	if (FAILED(hr))
		return E_DBSHEET_FILTER_INVALID;

	ks_stdptr<IDbFilterCriteria> spCriteria;
	VS(_appcore_CreateObject(CLSID_KDbFilterCriteria, IID_IDbFilterCriteria, (void**)&spCriteria));
	std::unique_ptr<KDbDataProvider> upProvider = std::make_unique<KDbDataProvider>(spDbSheetOp->GetBook());
	spCriteria->Init(upProvider.release());
	spCriteria->SetCriteriaOp(DBFCOT_Intersected);

	ks_stdptr<IDbFcValueDynamicSimple> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueDynamicSimple, IID_IDbFcValueDynamicSimple, (void**)&spVal));
	spVal->SetDynamicType(DBFCDT_CurUser);

	spCriteria->AddValue(spVal);
	hr = spFieldFilter->SetCriteria(spCriteria);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT TaskExecDbPermissionsUpgrade::SerializeUserPermission(KDBProtectionType& type, PCWSTR corePermissionId, ISerialAcceptor& acpt)
{
	PCWSTR permissionType = __X("preset");
	KDBProtectionType ptType = type;
	switch(type)
	{
	case DBPT_Admin: // 管理员——>可编辑
		ptType = DBPT_Editor;
		break;
	case DBPT_Reader: // 查看者——可查看
		break;
	case DBPT_NoPermissions: // 禁止查看
		break;
	case DBPT_Editor: // 编辑者——>自定义可编辑
		permissionType = __X("thirdapp");
		break;
	case DBPT_ReaderSubmitter: // 填报者——>自定义可填报
		permissionType = __X("thirdapp");
		break;
	default:
		ptType = DBPT_NoPermissions;
		break;
	}
	PCWSTR strTp = nullptr;
	HRESULT hr = m_pEncodeDecoder->EncodeKDBProtectionType(ptType, &strTp);
	if (FAILED(hr))
		return hr;
	acpt.addString("permissionType", permissionType);
	acpt.addString("corePermissionId", corePermissionId);
	acpt.addString("permissionContentType", strTp);
	return S_OK;
}

// ================== TaskExecDbSetCalendarCriteria ==================
// 日历视图内部区间筛选条件原本在TaskExecDbConfigureCalendarView共用，由于只读下需要服务端放开筛选条件，因此独立出来。
TaskExecDbSetCalendarCriteria::TaskExecDbSetCalendarCriteria(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbSetCalendarCriteria::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");

	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if (spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView_Calendar> spView_Calendar = spDbSheetView;
	if (param.has("beginDate") && param.has("endDate"))
	{
		PCWSTR beginDate = param.field_str("beginDate");
		hr = spView_Calendar->SetBeginDate(beginDate);
		if (FAILED(hr))
			return hr;

		PCWSTR endDate = param.field_str("endDate");
		hr = spView_Calendar->SetEndDate(endDate);
		if (FAILED(hr))
			return hr;
	}

	return S_OK;
}

HRESULT TaskExecDbSetCalendarCriteria::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecDbSetCalendarCriteria::GetTag()
{
	return __X("dbsheet.setCalendarCriteria");
}

// ================== TaskExecDbConfigureCalendarView ==================
TaskExecDbConfigureCalendarView::TaskExecDbConfigureCalendarView(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbConfigureCalendarView::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
	if (spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	HRESULT hr = setGetViewCurModifyTar(param);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IDBSheetView_Calendar> spView_Calendar = spDbSheetView;
	if (wo::VarObjFieldValidation::expectNumeric(param, "timelineColor", false))
	{
		ARGB timelineColor = param.field_uint32("timelineColor");
		spView_Calendar->SetTimeLineColor(timelineColor);
	}

	if (param.has("timeLineColorType"))
	{
		KDBViewConfigColorType colorType = KDBViewConfigColorType_Custom;
		hr = m_pEncodeDecoder->DecodeDbViewConfigColorType(param.field_str("timeLineColorType"), &colorType);
		if (FAILED(hr))
			return hr;

		hr = spView_Calendar->SetTimeLineColorType(colorType);
		if (FAILED(hr))
			return hr;
	}

	if (param.has("followField"))
	{
		if (spView_Calendar->GetTimeLineColorType() != KDBViewConfigColorType_Follow)
			return E_INVALIDARG;

		EtDbId followFieldId = GetEtDbId(param, "followField");
		hr = spView_Calendar->SetFollowField(followFieldId);
		if (FAILED(hr))
			return hr;
	}

	VarObj createFields = param.get_s("createFields");
	if (typeBool == createFields.type() && createFields.value_bool())
	{
		hr = addDateConfigField(spDbSheetView.get(), param, "beginFieldName", spDbSheetViews);
		if (FAILED(hr))
			return hr;

		hr = addDateConfigField(spDbSheetView.get(), param, "endFieldName", spDbSheetViews);
		if (FAILED(hr))
			return hr;
	}

	if (param.has("beginField"))
	{
		EtDbId beginFieldId = GetEtDbId(param, "beginField");
		hr = spDbSheetView->SetFieldsHidden(beginFieldId, FALSE);
		if (FAILED(hr))
			return hr;

		hr = spView_Calendar->SetBeginField(beginFieldId);
		if (FAILED(hr))
			return hr;
	}

	if (param.has("endField"))
	{
		EtDbId endFieldId = GetEtDbId(param, "endField");
		hr = spDbSheetView->SetFieldsHidden(endFieldId, FALSE);
		if (FAILED(hr))
			return hr;

		hr = spView_Calendar->SetEndField(endFieldId);
		if (FAILED(hr))
			return hr;
	}

	if (param.has("titleField"))
	{
		EtDbId titleFieldId = GetEtDbId(param, "titleField");
		hr = spDbSheetView->SetFieldsHidden(titleFieldId, FALSE);
		if (FAILED(hr))
			return hr;

		hr = spView_Calendar->SetTitleField(titleFieldId);
		if (FAILED(hr))
			return hr;
	}

	return S_OK;
}

HRESULT TaskExecDbConfigureCalendarView::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	IDX sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	return m_spProtectionJudgement->CheckCanCalendarOp(sheetStId, viewId);
}

PCWSTR TaskExecDbConfigureCalendarView::GetTag()
{
	return __X("dbsheet.configureCalendarView");
}

// ================== TaskExecDbAssignCalendarTimeLine ==================
TaskExecDbAssignCalendarTimeLine::TaskExecDbAssignCalendarTimeLine(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbAssignCalendarTimeLine::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, NULL);
	if (spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	EtDbId recId = GetEtDbId(param, "recordId");
	if (INV_EtDbId == recId)
		return E_DBSHEET_RECORD_NOT_FOUND;

	HRESULT hr = assignDateConfigTimeLine(spDbSheetView.get(), param, recId);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT TaskExecDbAssignCalendarTimeLine::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	VarObj param = cmd->cast().get("param");
	UINT sheetStId = param.field_uint32("sheetStId");
	EtDbId viewId = GetEtDbId(param, "viewId");
	ks_stdptr<IDBSheetView> spDbSheetView;
	GetDBSheetView(sheetStId, viewId, &spDbSheetView, nullptr);
	if (spDbSheetView == NULL)
		return E_DBSHEET_VIEW_NOT_FOUND;

	if (spDbSheetView->GetType() != et_DBSheetView_Calendar)
		return E_DBSHEET_INVALID_INPUT;

	EtDbId recId = GetEtDbId(param, "recordId");
	if (INV_EtDbId == recId)
		return E_DBSHEET_RECORD_NOT_FOUND;

	ks_stdptr<IDBSheetView_Calendar> spView_Calendar = spDbSheetView;
	HRESULT hr = S_OK;
	if (param.has("beginTime"))
	{
		hr = m_spProtectionJudgement->CheckCellCanEdit(sheetStId, recId, spView_Calendar->GetBeginField());
		if (FAILED(hr))
			return hr;
	}
	if (param.has("endTime"))
	{
		hr = m_spProtectionJudgement->CheckCellCanEdit(sheetStId, recId, spView_Calendar->GetEndField());
		if (FAILED(hr))
			return hr;
	}
	return hr;
}

PCWSTR TaskExecDbAssignCalendarTimeLine::GetTag()
{
	return __X("dbsheet.assignCalendarTimeLine");
}

TaskExecModidyStatSheetSetting::TaskExecModidyStatSheetSetting(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecModidyStatSheetSetting::operator()(KwCommand* cmd, KEtRevisionContext* ctx) 
{
	HRESULT hr = E_FAIL;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	binary_wo::VarObj param = cmd->cast().get("param");
	
	IDX iSheet = GetSheetIdx(param);
	ks_stdptr<ISheet> spStatSheet;
	pBook->GetSheet(iSheet, &spStatSheet);

	hr = SetStatSheetSetting(param, m_wwb,spStatSheet);
	if (FAILED(hr))
		return hr;

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
	}

	return S_OK;	
}

HRESULT TaskExecModidyStatSheetSetting::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) 
{
	return m_spProtectionJudgement->CheckCanManage();
}

PCWSTR TaskExecModidyStatSheetSetting::GetTag()
{
	return __X("dbSheet.modifyStatSheetSetting");
}

TaskExecActivateStatSheet::TaskExecActivateStatSheet(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecActivateStatSheet::operator()(KwCommand* cmd, KEtRevisionContext* ctx)
{
	HRESULT hr = E_FAIL;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	binary_wo::VarObj param = cmd->cast().get("param");
	
	IDX iSheet = GetSheetIdx(param);
	ks_stdptr<ISheet> spStatSheet;
	pBook->GetSheet(iSheet, &spStatSheet);
	if(!spStatSheet)
		return E_FAIL;

	hr = ActivateStatSheetByResetSettingField(param, m_wwb, spStatSheet);
	if (FAILED(hr))
		return hr;

	//与产品侧讨论后，统计表失效后重新配置激活/以及把统计表给恢复为普通表的操作 可以接受清事务
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
	}

	return S_OK;
}

HRESULT TaskExecActivateStatSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
{
	return m_spProtectionJudgement->CheckCanManage();
}

PCWSTR TaskExecActivateStatSheet::GetTag()
{
	return __X("dbSheet.activateStatSheet");
}

TaskExecRecoverStatSheet2NormalDbSheet::TaskExecRecoverStatSheet2NormalDbSheet(KEtWorkbook* wwb)
	:ETDbSheetTaskClassBase(wwb)
{

}

HRESULT TaskExecRecoverStatSheet2NormalDbSheet::operator()(KwCommand* cmd, KEtRevisionContext*)
{
	HRESULT hr = E_FAIL;
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	binary_wo::VarObj param = cmd->cast().get("param");
	
	IDX iSheet = GetSheetIdx(param);
	ks_stdptr<ISheet> spStatSheet;
	pBook->GetSheet(iSheet, &spStatSheet);
	if(!spStatSheet)
		return E_FAIL;

	hr = RecoverStatSheet2NormalDbSheet(spStatSheet);
	if (FAILED(hr))
		return hr;

	//因为统计表 sheet的拓展对象ExtDataItem edStatisticSheetData 不是原子，所以当执行此操作后，sheet的拓展对象ExtDataItem会被reset，因此无法支持事务undo，因此这里clear事务列表，防止undo
	//与产品侧讨论后，统计表失效后重新配置激活/以及把统计表给恢复为普通表的操作 可以接受清事务
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		binary_wo::VarObj resObj = peripheral->resSelf();
		resObj.add_field_str("cmdName", GetTag());
	}

	return S_OK;
}

HRESULT TaskExecRecoverStatSheet2NormalDbSheet::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) 
{
	return m_spProtectionJudgement->CheckCanManage();
}

PCWSTR TaskExecRecoverStatSheet2NormalDbSheet::GetTag()
{
	return __X("dbSheet.recoverStatSheet2NormalDbSheet");
}

// ================== TaskExecDbAddSidebarFolder ==================
TaskExecDbAddSidebarFolder::TaskExecDbAddSidebarFolder(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbAddSidebarFolder::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (!pBook)
		return E_FAIL;

	if (_kso_GetWoEtSettings()->GetMaxSidebarFolderTreeFolderCnt() == 0)
		return E_SIDEBARFOLDERTREE_MAX_FOLDER_CNT;

	binary_wo::VarObj param = cmd->cast().get("param");
	if (!param.has("source") || !param.has("target"))
		return E_FAIL;

	binary_wo::VarObj sourceObj = param.get_s("source");
	ASSERT(sourceObj.type() == typeStruct);
	DbSidebarFolderTreeType sourceType = DbSidebarFolderTreeType_Folder;
	VAR_OBJ_EXPECT_STRING(sourceObj, "name")
	PCWSTR sourceName = sourceObj.field_str("name");

	binary_wo::VarObj targetObj = param.get_s("target");
	ASSERT(targetObj.type() == typeStruct);
	VAR_OBJ_EXPECT_STRING(targetObj, "id")
	EtDbId targetId = GetEtDbId(targetObj, "id");

	// 若侧边栏tree中之前从未添加过文件夹，则需初始化SidebarFolderTreeManager
	ks_stdptr<IUnknown> spUnknown;
	if (FAILED(pBook->GetExtDataItem(edDbSidebarFolderTreeManager, &spUnknown)))
	{
		if (targetId != 0) { // 此次插入文件夹操作必然在root节点
			return E_FAIL;
		}

		ks_stdptr<IDbSidebarFolderTreeManager> spTmpMgr;
		VS(_appcore_CreateObject(CLSID_KDbSidebarFolderTreeManager, IID_IDbSidebarFolderTreeManager, (void**)&spTmpMgr));
		spTmpMgr->Init(pBook);
		pBook->SetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown*)spTmpMgr);
		if (!spTmpMgr)
			return E_FAIL;

		spTmpMgr->FirstAddFolderInit(); // 将当前所有sheet插入到tree中
	}

	ks_stdptr<IDbSidebarFolderTreeManager> spMgr;
	pBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spMgr);
	if (!spMgr)
		return E_FAIL;

	HRESULT hr = E_FAIL;
	EtDbId newFolderNodeId = INV_EtDbId;
	if (targetObj.has("before"))
	{
		binary_wo::VarObj beforeObj = targetObj.get_s("before");
		ASSERT(beforeObj.type() == typeStruct);
		VAR_OBJ_EXPECT_STRING(beforeObj, "id")
		EtDbId beforeId = GetEtDbId(beforeObj, "id");

		hr = spMgr->Add(targetId, sourceType, 0, sourceName, DbSidebarFolderTreeRelPos_Before, beforeId, &newFolderNodeId);
	}
	else if (targetObj.has("after"))
	{
		binary_wo::VarObj afterObj = targetObj.get_s("after");
		ASSERT(afterObj.type() == typeStruct);
		VAR_OBJ_EXPECT_STRING(afterObj, "id")
		EtDbId afterId = GetEtDbId(afterObj, "id");

		hr = spMgr->Add(targetId, sourceType, 0, sourceName, DbSidebarFolderTreeRelPos_After, afterId, &newFolderNodeId);
	}
	else
	{
		hr = spMgr->Add(targetId, sourceType, 0, sourceName, DbSidebarFolderTreeRelPos_PushBack, 0, &newFolderNodeId);
	}

	if (SUCCEEDED(hr))
	{
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());

		EtTaskPeripheral* peripheral = GetPeripheral();
		if(peripheral) 
		{
			binary_wo::VarObj resObj = peripheral->resSelf();
			resObj.add_field_str("cmdName", GetTag());
			AddEtDbId(resObj, "newFolderNodeId", newFolderNodeId);
		}
	}

	return hr;
}

HRESULT TaskExecDbAddSidebarFolder::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return m_spProtectionJudgement->CheckSheetCanAdd(); // 和添加sheet一致
}

PCWSTR TaskExecDbAddSidebarFolder::GetTag()
{
	return __X("dbsheet.addSidebarFolder");
}

// ================== TaskExecDbDeleteSidebarFolder ==================
TaskExecDbDeleteSidebarFolder::TaskExecDbDeleteSidebarFolder(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbDeleteSidebarFolder::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	// 保护工作簿禁止操作
	IBookProtection* pBookProtection = m_wwb->GetCoreWorkbook()->GetProtection();
	if (pBookProtection)
	{
		BOOKPROTECTION bookProtection = {};
		pBookProtection->GetProperty(&bookProtection);
		if (bookProtection.bProtectStruct)
			return E_FAIL;
	}

	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (!pBook)
		return E_FAIL;

	ks_stdptr<IDbSidebarFolderTreeManager> spMgr;
	pBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spMgr);
	if (!spMgr)
		return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");
	if (!param.has("target"))
		return E_FAIL;

	binary_wo::VarObj targetObj = param.get_s("target");
	ASSERT(targetObj.type() == typeStruct);
	VAR_OBJ_EXPECT_STRING(targetObj, "id")
	EtDbId targetId = GetEtDbId(targetObj, "id");
	if (targetId == 0) // 不允许删除root文件夹
		return E_SIDEBARFOLDERTREE_ROOT;

	std::vector<UINT> folderStIdVec;
	EnumDbSidebarFolderSheets(spMgr, targetId, folderStIdVec);
	std::vector<EtDbId> oriFolderIdVec; // 删除前目标文件夹下的所有文件夹（包括自己）
	EnumDbSidebarFolderFolders(spMgr, targetId, oriFolderIdVec);

	HRESULT hr = E_FAIL;
	if (folderStIdVec.size() == 0) {
		hr = spMgr->Delete(targetId);
	} else {
		hr = DeleteSheetsInFolder(pCtx, folderStIdVec);
		spMgr->DeleteEmptyFolders(targetId);
	}

	// hr即便为FAIL，也可能已经删除了部分sheet，故此处还是需要clear
	m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
	
	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		std::vector<EtDbId> remainFolderIdVec; // 删除后目标文件夹下剩余的所有文件夹（包括自己）。若为空则说明所有sheet及folder都删除成功
											   // 若非空则说明有部分sheet删除失败，从而导致上层的文件夹也不能被删除
		EnumDbSidebarFolderFolders(spMgr, targetId, remainFolderIdVec);

		binary_wo::VarObj resObj = peripheral->resOthers();
		if (remainFolderIdVec.empty())
		{
			VarObj obj = resObj.add_field_array("folders", binary_wo::typeString);
			for (auto id : oriFolderIdVec)
				AddEtDbIdItem(obj, id);
		}
		else
		{
			VarObj obj = resObj.add_field_array("failedFolders", binary_wo::typeString);
			for (auto id : remainFolderIdVec)
				AddEtDbIdItem(obj, id);
		}
	}

	return hr;
}

HRESULT TaskExecDbDeleteSidebarFolder::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return m_spProtectionJudgement->CheckSheetCanRemove(); // 和删除sheet一致
}

PCWSTR TaskExecDbDeleteSidebarFolder::GetTag()
{
	return __X("dbsheet.deleteSidebarFolder");
}

HRESULT TaskExecDbDeleteSidebarFolder::EnumDbSidebarFolderSheets(IDbSidebarFolderTreeManager* pMgr, EtDbId targetId,
																std::vector<UINT>& folderStIdVec)
{
	class DbSidebarFolderSheetsEnum : public IDbSidebarFolderSheetsEnum
    {
        using CallBack = std::function<HRESULT(UINT stId)>;
    public:
        DbSidebarFolderSheetsEnum(const CallBack &cb)
            : m_cb(cb)
        {}

        STDPROC Do(const UINT stId) override
        {
            return m_cb(stId);
        }
    private:
        const CallBack m_cb;
    };

    DbSidebarFolderSheetsEnum sidebarFolderSheetsEnum([&folderStIdVec](UINT stId) -> HRESULT {
        folderStIdVec.push_back(stId);
        return S_OK;
    });

	return pMgr->EnumDbSidebarFolderSheets(targetId, &sidebarFolderSheetsEnum);
}

HRESULT TaskExecDbDeleteSidebarFolder::EnumDbSidebarFolderFolders(IDbSidebarFolderTreeManager* pMgr, EtDbId targetId,
																std::vector<EtDbId>& folderIdVec)
{
	class DbSidebarFolderFoldersEnum : public IDbSidebarFolderFoldersEnum
    {
        using CallBack = std::function<HRESULT(EtDbId nodeId)>;
    public:
        DbSidebarFolderFoldersEnum(const CallBack &cb)
            : m_cb(cb)
        {}

        STDPROC Do(const EtDbId nodeId) override
        {
            return m_cb(nodeId);
        }
    private:
        const CallBack m_cb;
    };

    DbSidebarFolderFoldersEnum sidebarFolderFoldersEnum([&folderIdVec](EtDbId nodeId) -> HRESULT {
        folderIdVec.push_back(nodeId);
        return S_OK;
    });

	return pMgr->EnumDbSidebarFolderFolders(targetId, &sidebarFolderFoldersEnum);
}

HRESULT TaskExecDbDeleteSidebarFolder::DeleteSheetsInFolder(KEtRevisionContext* pCtx, const std::vector<UINT>& folderStIdVec)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (!pBook)
		return E_FAIL;
	IKWorksheets* pWorkSheets = m_wwb->GetCoreWorkbook()->GetWorksheets();
	if (!pWorkSheets)
		return E_FAIL;
	int iSheetCount = pWorkSheets->GetSheetCount();

	if (folderStIdVec.size() == 0)
		return E_INVALIDARG;

	// 去重排序
	std::set<IDX> aSheetIds;
	for (IDX i = 0; i < folderStIdVec.size(); ++i) 
	{
		IDX iSheet = INVALIDIDX;
		pBook->STSheetToRTSheet(folderStIdVec[i], &iSheet);
		if (iSheet == INVALIDIDX)
			continue;

		aSheetIds.insert(iSheet);
	}

	// 选中sheet有效性检查
	int delDbSheetNum = 0;
	for (int idx : aSheetIds)
	{
		if (idx < 0 || idx >= iSheetCount)
			return E_INVALIDARG; // 索引越界
		ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(idx);
		if (pSheetProtection && pSheetProtection->IsProtected())
			return E_FAIL; // 保护工作表 不允许删除
		if (isCellImgListSheet(idx))
			return E_FAIL; // 不应该是 cellimageSheet
		ISheet* pSheet = pWorkSheets->GetSheetItem(idx)->GetSheet();
		if (!pSheet)
			return E_FAIL;
		BOOL bVisible = FALSE;
		pSheet->GetVisible(&bVisible);
		if (!bVisible)
			return E_FAIL; // 不应该隐藏
		if (pSheet->IsDbSheet())
			++delDbSheetNum;
	}
	int iVisibleCnt = 0;
	for (int idx = 0; idx < iSheetCount; ++idx)
	{
		ISheet* pSheet = pWorkSheets->GetSheetItem(idx)->GetSheet();
		if (!pSheet->IsDbSheet())
			continue;
		BOOL bVisible = FALSE;
		pSheet->GetVisible(&bVisible);
		if (bVisible)
			++iVisibleCnt;
	}
	// db删除表时, 数据表要保留一个
	if (iVisibleCnt - delDbSheetNum < 1)
		return E_FAIL;

	size_t sheetsNum = aSheetIds.size();
	std::vector<WebID> objSheetVec;
	objSheetVec.reserve(sheetsNum);
	std::vector<UINT> failedVec;
	failedVec.reserve(sheetsNum);
	for (auto it = aSheetIds.rbegin(); it != aSheetIds.rend(); ++it)
	{
		IDX iSheet = *it;
		IKWorksheet* pWorksheet = pWorkSheets->GetSheetItem(iSheet);
		WebID sheetObjId = 0;
		AbsObject* obj = pCtx->getSheetMain(iSheet);
		if (obj)
		{
			sheetObjId = obj->objId();
			AbsKeepRepertory* keepRepertory = pCtx->getDocument()->getKeepRepertory();
			keepRepertory->onCloseMainObject(obj);
		}
		ks_stdptr<IBookOp> spBookOp;
		pBook->GetOperator(&spBookOp);
		app_helper::KBatchUpdateCal buc(spBookOp);
		HRESULT hr = pWorksheet->DeleteDirectly();

		if (FAILED(hr))
			failedVec.emplace_back(pWorksheet->GetSheet()->GetStId());
		else if (sheetObjId > 0)
		{
			pCtx->onSheetDelete(sheetObjId);
			objSheetVec.emplace_back(sheetObjId);
		}
	}

	EtTaskPeripheral* peripheral = GetPeripheral();
	if (peripheral)
	{
		// 删除sheet
		binary_wo::VarObj resObj = peripheral->resOthers();
		resObj.add_field_str("cmdName", GetTag());
		resObj.add_field_str("affectedBy", pCtx->getUser()->userID());
		if (!objSheetVec.empty())
		{
			VarObj objIdVec = resObj.add_field_array("objSheets", binary_wo::typeFloat64);
			for (auto id : objSheetVec)
				objIdVec.add_item_double(static_cast<double>(id));
		}
		if (!failedVec.empty())
		{
			VarObj failedSheets = resObj.add_field_array("failedSheets", binary_wo::typeUint32);
			for (auto id : failedVec)
				failedSheets.add_item_uint32(id);
		}
	}

	return S_OK;
}

// ================== TaskExecDbMoveSidebarTree ==================
TaskExecDbMoveSidebarTree::TaskExecDbMoveSidebarTree(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
}

HRESULT TaskExecDbMoveSidebarTree::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (!pBook)
		return E_FAIL;

	ks_stdptr<IDbSidebarFolderTreeManager> spMgr;
	pBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spMgr);
	if (!spMgr)
		return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");
	if (!param.has("source") || !param.has("target"))
		return E_FAIL;

	binary_wo::VarObj sourceObj = param.get_s("source");
	ASSERT(sourceObj.type() == typeStruct);
	VAR_OBJ_EXPECT_STRING(sourceObj, "id")
	EtDbId sourceId = GetEtDbId(sourceObj, "id");

	binary_wo::VarObj targetObj = param.get_s("target");
	ASSERT(targetObj.type() == typeStruct);
	VAR_OBJ_EXPECT_STRING(targetObj, "id")
	EtDbId targetId = GetEtDbId(targetObj, "id");

	HRESULT hr = E_FAIL;
	if (targetObj.has("before"))
	{
		binary_wo::VarObj beforeObj = targetObj.get_s("before");
		ASSERT(beforeObj.type() == typeStruct);
		VAR_OBJ_EXPECT_STRING(beforeObj, "id")
		EtDbId beforeId = GetEtDbId(beforeObj, "id");

		hr = spMgr->Move(targetId, sourceId, DbSidebarFolderTreeRelPos_Before, beforeId);
	}
	else if (targetObj.has("after"))
	{
		binary_wo::VarObj afterObj = targetObj.get_s("after");
		ASSERT(afterObj.type() == typeStruct);
		VAR_OBJ_EXPECT_STRING(afterObj, "id")
		EtDbId afterId = GetEtDbId(afterObj, "id");

		hr = spMgr->Move(targetId, sourceId, DbSidebarFolderTreeRelPos_After, afterId);
	} 
	else
	{
		hr = spMgr->Move(targetId, sourceId, DbSidebarFolderTreeRelPos_PushBack, 0);
	}
	
	if (SUCCEEDED(hr))
	{
		m_wwb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_wwb->GetCoreWorkbook());
	}

	return hr;
}

HRESULT TaskExecDbMoveSidebarTree::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecDbMoveSidebarTree::GetTag()
{
	return __X("dbsheet.moveSidebarTree");
}

// ================== TaskExecDbSetNameSidebarFolder ==================
TaskExecDbSetNameSidebarFolder::TaskExecDbSetNameSidebarFolder(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbSetNameSidebarFolder::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (!pBook)
		return E_FAIL;

	ks_stdptr<IDbSidebarFolderTreeManager> spMgr;
	pBook->GetExtDataItem(edDbSidebarFolderTreeManager, (IUnknown**)&spMgr);
	if (!spMgr)
		return E_FAIL;

	binary_wo::VarObj param = cmd->cast().get("param");
	if (!param.has("target"))
		return E_FAIL;

	binary_wo::VarObj targetObj = param.get_s("target");
	ASSERT(targetObj.type() == typeStruct);
	VAR_OBJ_EXPECT_STRING(targetObj, "id")
	EtDbId targetId = GetEtDbId(targetObj, "id");

	if (targetId == 0) // 不允许重命名root文件夹
		return E_SIDEBARFOLDERTREE_ROOT;
	VAR_OBJ_EXPECT_STRING(targetObj, "name")
	PCWSTR name = nullptr;
	name = targetObj.field_str("name");

	HRESULT hr = spMgr->SetName(targetId, name);

	return hr;
}

HRESULT TaskExecDbSetNameSidebarFolder::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecDbSetNameSidebarFolder::GetTag()
{
	return __X("dbsheet.setNameSidebarFolder");
}
// TODO(zhangpeng27) 调试用
// ================== TaskExecDbCommonLogInit ==================
TaskExecDbCommonLogInit::TaskExecDbCommonLogInit(KEtWorkbook* wwb)
	: ETDbSheetTaskClassBase(wwb)
{
	m_peri->setDefBanUndo(KwVersion::banUndoCur);
}

HRESULT TaskExecDbCommonLogInit::operator()(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	IBook* pBook = m_wwb->GetCoreWorkbook()->GetBook();
	if (!pBook)
		return E_FAIL;

	ks_stdptr<IDbCommonLog> spLog;
	pBook->GetExtDataItem(edBookDbCommonLog, (IUnknown**)&spLog);
	if (spLog)
	{
		wo::KDbValueSerializeHelper stHelper;
		stHelper.Init(m_wwb);
		spLog->SerializeInit(&stHelper);
	}
	return S_OK;
}

HRESULT TaskExecDbCommonLogInit::CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx)
{
	return S_OK;
}

PCWSTR TaskExecDbCommonLogInit::GetTag()
{
	return __X("dbsheet.commonLogInit");
}
}//namespace wo
