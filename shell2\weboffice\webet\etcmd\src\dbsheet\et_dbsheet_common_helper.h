#ifndef __ET_DBSHEET_COMMON_HELPER_H__
#define __ET_DBSHEET_COMMON_HELPER_H__

#include "db_json_data_helper.h"
#include "etcore/et_core_dbsheet_enum.h"

interface IDBSheetViews;
interface IDBChartStatisticMgr;

namespace wo
{

class KEtWorkbooks;
class KEtWorkbook;

class DBSheetCommonHelper
{
public:
    explicit DBSheetCommonHelper(KEtWorkbooks*);
    explicit DBSheetCommonHelper(KEtWorkbook*);
    explicit DBSheetCommonHelper(IBook*);
    ~DBSheetCommonHelper();

    struct CellValueItem
    {
        EtDbId fldId;
        EtDbId recId;
        ks_wstring value;
        CellValueItem(EtDbId fId, EtDbId rId, ks_wstring v)
        {
            fldId = fId;
            recId = rId;
            value = v;
        }
    };

    HRESULT RangeCopy(const binary_wo::VarObj &param, ks_wstring& txt, bool& hasBreak);
    HRESULT RangePaste(binary_wo::VarObj &param, DBJsonDataHelper* copyDataParser, IDBSheetRange* pTarRg, bool autoFillingRange, int srcRowCnt, int srcColCnt, int fillRowCnt, int fillColCnt, int& pasteFailCnt, bool& bAlterAutoField, EtDbId& uniquekLimitFieldId, std::vector<CellValueItem>& notUniqueItems, std::vector<CellValueItem>& invalidPhotoItems, std::vector<CellValueItem>& invalidScanItems);
    HRESULT GetAllDataText(const binary_wo::VarObj &param, ks_wstring& txt, bool& hasBreak, bool &bAlterAttachmentField, bool& bAlterNoteField);
    EtDbId GetEtDbId_NoExcept(const binary_wo::VarObj& obj, WebName name) const noexcept;
    EtDbId GetEtDbId(const binary_wo::VarObj& obj, WebName name) const;
    HRESULT GetEtDbIds(const binary_wo::VarObj& obj, WebName name, std::vector<EtDbId>& EtDbIds) const;
    EtDbId GetEtDbId(binary_wo::VarObj obj);
	HRESULT GetSheet(UINT, ISheet**);
    HRESULT GetDbSheet(UINT sheetStId, ISheet** ppSheet);
    HRESULT GetWorksheet(UINT sheetStId, _Worksheet** ppWorksheet);
    HRESULT GetDBSheetViews(UINT sheetStId, IDBSheetViews** ppIDbSheetViews);
    HRESULT GetDBSheetView(UINT sheetStId, EtDbId viewId, IDBSheetView** ppIDbSheetView
        , IDBSheetViews** ppIDbSheetViews) const;
    HRESULT GetDBSheetOp(UINT sheetStId, IDBSheetOp** ppDbSheetOp) const;
	HRESULT GetDBChartStatisticMgr(UINT, IDBChartStatisticMgr**);
    HRESULT GetDBRange(IDBSheetView* pView, binary_wo::VarObj vRg, 
        bool extendRecordsViewAllToGridAll, bool extendFieldsViewAllToGridAll, IDBSheetRange** ppRg);
    IDX GetIdxById(UINT sheetStId);
    void GetDBPos(binary_wo::VarObj pos, DbIdPostion& tar);
    BOOL IsFieldEmpty(IDBSheetView* pView, EtDbId fldId);
    void AcceptorAddEtDbId(ISerialAcceptor* acpt, WebName name, EtDbId id);
    HRESULT setGetViewCurModifyTar(binary_wo::VarObj param);

private:
    void ParseContactToken(const_token_ptr pToken, DBJsonDataUser* pObj);
    void ParseAttachmentToken(const_token_ptr pToken, DBJsonDataAttachment* pObj);
    void ParseNoteToken(const_token_ptr pToken, DBJsonDataNote* pObj);
    ET_DbSheet_FieldType ConvertSupportCopyLookupField(IDbField* pField);
    void BatchCopyAttachments(const std::map<ks_wstring, int>&, std::map<ks_wstring, std::vector<ks_wstring>>&, binary_wo::VarObj&, PCWSTR);

private:
    KEtWorkbooks* m_wbs;
    KEtWorkbook* m_workbook;
    IBook* m_pBook;
    const std::set<ET_DbSheet_FieldType> m_supportCopyTypes = {Et_DbSheetField_Contact, Et_DbSheetField_CreatedBy, Et_DbSheetField_LastModifiedBy,
                                                                      Et_DbSheetField_Note, Et_DbSheetField_Attachment};
};

} // namespace wo

#endif
