﻿#ifndef __WEBET_CONDITION_FORMAT_CACHE_H__
#define __WEBET_CONDITION_FORMAT_CACHE_H__

#include <unordered_map>
#include "etcore/et_core_attr.h"
#include "etcore/et_core_basic.h"
#include "etcore/et_core_condfmt.h"
#include "kso/et/core_define.h"
#include "alg/exec/alg_hash.h"
#include "etcore/et_core_condfmt.h"

enum ConditionFormatCacheMode
{
    CFCM_None,
    CFCM_Disabled,
    CFCM_Enabled,
};

interface IBookOp;
interface IKXFGridCache;
class ConditionFormatCache
{
public:
    ConditionFormatCache();

    bool IsEnabled();
    void Init(IBook* pBook);

    void PrepareCfData(IKWorkbook *pWorkbook, const RANGE &rg);
    void getXFGridCache(IKWorksheet* pWorksheet, IKXFGridCache** ppXFGridCache);
    void OptmiseCache();
    double GetCacheHitRate()
    {
        return 0.0;
    }
    void Reset(IBook* pBook);
private:
    IBook* m_pBook = nullptr;
};

#endif // __WEBET_CONDITION_FORMAT_CACHE_H__