#include "find_app_helper.h"
#include "etcore/little_alg.h"
#include "util.h"
#include "dbsheet/et_dbsheet_utils.h"


namespace FindAppHelper
{

bool FindAppRelatedEtSheetStIdByDbSheetStId(wo::KEtWorkbook* pWorkbook, UINT relatedDbSheetStId, UINT &etSheetStId)
{
	IBook *pBook = pWorkbook->GetCoreWorkbook()->GetBook();
	if(!pBook)
		return false;

	long sheetCount = 0;
	ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
	spWorksheets->get_Count(&sheetCount);
	for (IDX idx = 0; idx < sheetCount; idx++)
	{
		ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(idx, &spSheet);
        if (!spSheet)
			continue;
		if(spSheet->IsDbSheet())
			continue;
		ks_stdptr<IUnknown> spUnk;
		spSheet->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
		ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
		if(!spSheetAppEtDbRelations || !spSheetAppEtDbRelations->ContainRelatedDbSheetStId(relatedDbSheetStId))
			continue;
		
		//找到了
		etSheetStId = spSheet->GetStId();
		return true;
	}
	return false;
}


}//end namespace FindAppHelper


