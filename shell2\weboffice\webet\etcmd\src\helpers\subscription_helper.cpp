﻿#include "etstdafx.h"
#include "subscription_helper.h"
#include "workbook.h"
#include "wo/core_stake.h"
#include "webbase/serialize_impl.h"
#include "supbooks_helper.h"

extern Callback* gs_callback;

namespace wo
{

namespace Subscription
{


Utils::Utils(KEtWorkbook *workbook)
    : m_workbook(workbook)
{}

IBook *Utils::GetBook()
{
    if (m_workbook == nullptr)
        return nullptr;
    etoldapi::_Workbook *pWorkbook = m_workbook->GetCoreWorkbook();
    if (pWorkbook == nullptr)
        return nullptr;
    return pWorkbook->GetBook();
}

IBookSetting *Utils::GetBookSetting()
{
    IBook* pBook = GetBook();
    if (pBook == nullptr)
        return nullptr;
    IBookStake* pBookStake = pBook->GetWoStake();
    if (pBookStake == nullptr)
        return nullptr;
    return pBookStake->getSetting();
}

ICbSubscribeOp *Utils::GetSubscribeOp()
{
    IBook* pBook = GetBook();
    if (pBook == nullptr)
        return nullptr;
    IBookStake* pBookStake = pBook->GetWoStake();
    if (pBookStake == nullptr)
        return nullptr;
    return pBookStake->GetCbSubscribeOp();
}

void Utils::SetAutoUpdatePaused(BOOL b)
{
    IBookSetting *pBookSetting = GetBookSetting();
    if (pBookSetting == nullptr)
        return;
    pBookSetting->setSubscriptionAutoUpdatePaused(b);
}

BOOL Utils::IsAutoUpdatePaused()
{
    IBookSetting *pBookSetting = GetBookSetting();
    if (pBookSetting == nullptr)
        return FALSE;
    return pBookSetting->isSubscriptionAutoUpdatePaused();
}

BOOL Utils::IsAutoUpdateForbidden()
{
    ICbSubscribeOp *pSubOp = GetSubscribeOp();
    if (pSubOp == nullptr)
        return FALSE;
    BOOL b = FALSE;
    pSubOp->ExceededFilesThreshold(b);
    return b;
}

void Utils::SetMergeTasksAutoUpdate(BOOL autoUpdate)
{
    IBookSetting *pBookSetting = GetBookSetting();
    if (pBookSetting == nullptr)
        return;
    pBookSetting->setMergeTasksAutoUpdate(autoUpdate);
}

WebInt Utils::ReSubscribe(bool unSubscribe)
{
    if (!gs_callback->notifyFile)
        return WO_FAIL;

    ICbSubscribeOp *pSubOp = GetSubscribeOp();
    if (pSubOp == nullptr)
        return WO_OK;

    std::unordered_set<INT> setRm;
    std::unordered_set<INT> setDirty;
    pSubOp->CalcReferringBooks(setRm, setDirty, nullptr);

    if (unSubscribe)
    {
        _UnSubscribe(setRm.cbegin(), setRm.cend());
    }
    
    _Subscribe(setDirty.cbegin(), setDirty.cend());

    return WO_OK;
}

// 服务端在自动更新树上挂载节点
WebInt Utils::ReSubscribeWithAsyncGetFile(KEtWorkbook *workbook, const char* userId)
{
    if (!gs_callback->notifyFile || !gs_callback->asyncGetNetFile)
        return WO_FAIL;

    ICbSubscribeOp *pSubOp = GetSubscribeOp();
    if (pSubOp == nullptr)
        return WO_OK;

    std::unordered_set<INT> setRm;
    std::unordered_set<INT> setDirty;
    std::unordered_map<INT, std::vector<RANGE>> setDynamicRange;
    pSubOp->CalcReferringBooks(setRm, setDirty, &setDynamicRange);
    std::unordered_set<INT> needCleanUpSet;
    needCleanUpSet.insert(setRm.begin(), setRm.end());
    needCleanUpSet.insert(setDirty.begin(), setDirty.end());
    CleanUpCacheWhenUnSubscribe(needCleanUpSet, workbook);

    _UnSubscribe(setRm.cbegin(), setRm.cend());
    _Subscribe(setDirty.cbegin(), setDirty.cend());

    const char* cUserID = userId == NULL ? "" : userId;
	QString qUserId(cUserID);
    for (INT bookId : setDirty)
    {
        PCWSTR fileId = GetSupBookFileId(bookId);
        if (fileId)
        {
            QString qFileId = QString::fromUtf16(fileId);
            if (setDynamicRange.count(bookId) != 0 && !setDynamicRange[bookId].empty())
            {
                const std::vector<RANGE>& ranges = setDynamicRange[bookId];
                BinWriter bw;
                bw.addKey("ranges");
                bw.beginArray();
                for (auto it = ranges.cbegin(); it != ranges.cend(); ++it)
                {
                    if (!it->IsValid())
                        continue;
                    bw.beginStruct();
                    bw.addInt32Field(it->SheetFrom(), "sheetFrom");
                    bw.addInt32Field(it->SheetFrom(), "sheetTo");
                    bw.addInt32Field(it->RowFrom(), "rowFrom");
                    bw.addInt32Field(it->RowTo(), "rowTo");
                    bw.addInt32Field(it->ColFrom(), "colFrom");
                    bw.addInt32Field(it->ColTo(), "colTo");
                    bw.endStruct();
                    BinWriter::StreamHolder sh = bw.buildStream();
                    WebSlice slice = { sh.get(), bw.writeLength() };
                    gs_callback->asyncGetNetFile(qFileId.toUtf8(), qUserId.toUtf8(), DownloadFileTask_CrossBook, true, &slice);
                }
            }
            else
            {
                WebSlice slice = { nullptr, 0 };
                gs_callback->asyncGetNetFile(qFileId.toUtf8(), qUserId.toUtf8(), DownloadFileTask_CrossBook, false, &slice);
            }
        }
    }

    return WO_OK;
}

WebInt Utils::ReSubscribeAuthorizedFile(const std::vector<ks_wstring>& vecIds)
{
    if (!gs_callback->notifyFile)
        return WO_FAIL;

    ICbSubscribeOp *pSubOp = GetSubscribeOp();
    if (pSubOp == nullptr)
        return WO_OK;

    std::unordered_set<INT> setAll;
    for (const ks_wstring& item : vecIds)
    {
        INT nBook = GetSupBookIndex(item.c_str());
        if (nBook != alg::STREF_INV_BOOK)
            setAll.insert(nBook);
    }
    _Subscribe(setAll.cbegin(), setAll.cend());

    return WO_OK;
}

WebInt Utils::UnSubscribeAll()
{
    ICbSubscribeOp *pSubOp = GetSubscribeOp();
    if (pSubOp == nullptr)
        return WO_OK;
    std::vector<INT> vec;

    pSubOp->GetReferringBooks(vec);
    _UnSubscribe(vec.cbegin(), vec.cend());
    return WO_OK;
}

void Utils::CleanUpCacheWhenUnSubscribe(const std::unordered_set<INT> &books, KEtWorkbook *workbook)
{
    if (books.empty())
        return;
    ks_stdptr<ISupEditLinks> spSupLinks;
    getSupEditLinks(workbook, &spSupLinks);
    if (nullptr == spSupLinks)
        return;
    ICbSubscribeOp *pSubOp = GetSubscribeOp();
    if (nullptr == spSupLinks)
        return;
    std::unordered_map<INT, std::vector<RANGE>> needCleanUpRanges;
    pSubOp->CollectCrossBookRefersRanges(books, needCleanUpRanges);
    spSupLinks->ClearSupBookCache(needCleanUpRanges);
}

PCWSTR Utils::GetSupBookFileId(INT nBook)
{
    if (nBook == alg::STREF_INV_BOOK)
        return nullptr;

    IBook* pBook = GetBook();
    if (pBook == nullptr)
        return nullptr;
    ks_stdptr<ISupBooks> spSupBooks;
    pBook->GetSupBooks(&spSupBooks);
    if (spSupBooks == nullptr)
        return nullptr;
    ks_stdptr<ISupBook> spSupBook;
    spSupBooks->GetSupBook(nBook, &spSupBook);
    if (spSupBook == nullptr)
        return nullptr;
    return spSupBook->getNetFileId();
}

INT Utils::GetSupBookIdxByFullName(PCWSTR fullName)
{
    if (fullName == nullptr)
        return alg::STREF_INV_BOOK;

    IBook* pBook = GetBook();
    if (pBook == nullptr)
        return alg::STREF_INV_BOOK;

    ks_stdptr<ISupBooks> spSupBooks;
    pBook->GetSupBooks(&spSupBooks);
    if (spSupBooks == nullptr)
        return alg::STREF_INV_BOOK;

    int nCount = 0;
    spSupBooks->GetCount(&nCount);
    for (int i = 0; i < nCount; i++)
    {
        ks_stdptr<ISupBook> spSupBook;
        spSupBooks->GetSupBook(i, &spSupBook);
        if (spSupBook == nullptr)
            continue;

        const WCHAR* strName = nullptr;
        spSupBook->GetName(&strName);
        if (strName && xstrcmp(strName, fullName) == 0)
        {
            return i;
        }
    }

    return alg::STREF_INV_BOOK;
}

INT Utils::GetSupBookIndex(PCWSTR fileId)
{
    if (fileId == nullptr)
        return alg::STREF_INV_BOOK;
    IBook* pBook = GetBook();
    if (pBook == nullptr)
        return alg::STREF_INV_BOOK;
    ks_stdptr<ISupBooks> spSupBooks;
    pBook->GetSupBooks(&spSupBooks);
    if (spSupBooks == nullptr)
        return alg::STREF_INV_BOOK;
    int nCount = 0;
    spSupBooks->GetCount(&nCount);
    for (int i = 0; i < nCount; i++)
    {
        ks_stdptr<ISupBook> spSupBook;
        spSupBooks->GetSupBook(i, &spSupBook);
        if (spSupBook == nullptr)
            continue;

        PCWSTR _fileId = spSupBook->getNetFileId();
        if (_fileId && xstrcmp(fileId, _fileId) == 0)
            return i;
    }

    return alg::STREF_INV_BOOK;
}

bool Utils::GetSupBookFullName(PCWSTR fileId, ks_wstring *out)
{
    if (fileId == nullptr)
        return false;

    IBook* pBook = GetBook();
    if (pBook == nullptr)
        return false;

    ks_stdptr<ISupBooks> spSupBooks;
    pBook->GetSupBooks(&spSupBooks);
    if (spSupBooks == nullptr)
        return false;

    int nCount = 0;
    spSupBooks->GetCount(&nCount);
    for (int i = 0; i < nCount; i++)
    {
        ks_stdptr<ISupBook> spSupBook;
        spSupBooks->GetSupBook(i, &spSupBook);
        if (spSupBook == nullptr)
            continue;

        PCWSTR _fileId = spSupBook->getNetFileId();
        if (_fileId && xstrcmp(fileId, _fileId) == 0)
        {
            const WCHAR* strName = nullptr;
            spSupBook->GetName(&strName);
            if (strName)
            {
                *out = strName;
                return true;
            }
        }
    }

    return false;
}

template<typename Iterator>
void Utils::_Subscribe(Iterator first, Iterator last)
{
    ICbSubscribeOp *pSubOp = GetSubscribeOp();
    if (pSubOp == nullptr)
        return;
    if (first == last)
        return;
    ks_wstring file2Sub;
    bool bUseBatchNotify = _kso_GetWoEtSettings()->IsBatchNotifyCBRInfo();
    if (bUseBatchNotify)
    {
        binary_wo::BinWriter bw;
        KSerialWrapBinWriter acpt(bw, nullptr);
        acpt.addKey("data");
        acpt.beginArray();
        for (auto it = first; it != last; ++it)
        {
            INT nBook = *it;
            PCWSTR fileID = GetSupBookFileId(nBook);
            if (fileID == nullptr)
                continue;
            acpt.beginStruct();
            acpt.addString("fileId", fileID);
            pSubOp->SerialiseCbRefers(&acpt, nBook);
            acpt.endStruct();
            file2Sub.AppendFormat(__X("%s,"), fileID);
        }
        acpt.endArray();
        binary_wo::BinWriter::StreamHolder sh = bw.buildStream();
        WebSlice slice = { sh.get(), bw.writeLength() };
        gs_callback->crossBookNotify(WoNotifySubscribeCb, &slice);
    }
    else
    {
        for (auto it = first; it != last; ++it)
        {
            INT nBook = *it;
            PCWSTR fileID = GetSupBookFileId(nBook);
            if (fileID == nullptr)
                continue;

            binary_wo::BinWriter bw;
            KSerialWrapBinWriter acpt(bw, nullptr);
            pSubOp->SerialiseCbRefers(&acpt, nBook);

            binary_wo::BinWriter::StreamHolder sh = bw.buildStream();
            WebSlice slice = { sh.get(), bw.writeLength() };
            QString qsFileID = QString::fromUtf16(fileID);
            gs_callback->notifyFile(WoNotifySubscribeCb, qsFileID.toUtf8(), &slice);
            file2Sub.AppendFormat(__X("%s,"), fileID);
        }
    }
    if (!file2Sub.empty())
    {
        file2Sub.pop_back();
        WOLOG_INFO << "[subscription] " << "WoNotifySubscribeCb, subscribe file: " << file2Sub;
    }
}

template<typename Iterator>
void Utils::_UnSubscribe(Iterator first, Iterator last)
{
    if (first == last)
        return;
    ks_wstring file2UnSub;
    bool bUseBatchNotify = _kso_GetWoEtSettings()->IsBatchNotifyCBRInfo();
    if (bUseBatchNotify)
    {
        binary_wo::BinWriter bw;
        bw.beginArray("fileIds");
        for (auto it = first; it != last; ++it)
        {
            INT nBook = *it;
            PCWSTR fileID = GetSupBookFileId(nBook);
            if (fileID == nullptr)
                continue;
            bw.addString(fileID);
            file2UnSub.AppendFormat(__X("%s,"), fileID);
        }
        bw.endArray();
        binary_wo::BinWriter::StreamHolder sh = bw.buildStream();
        WebSlice slice = { sh.get(), bw.writeLength() };
        gs_callback->crossBookNotify(WoNotifyUnSubscribeCb, &slice);
    }
    else
    {
        for (auto it = first; it != last; ++it)
        {
            INT nBook = *it;
            PCWSTR fileID = GetSupBookFileId(nBook);
            if (fileID == nullptr)
                continue;

            WebSlice slice = { nullptr, 0 };
            QString qsFileID = QString::fromUtf16(fileID);
            gs_callback->notifyFile(WoNotifyUnSubscribeCb, qsFileID.toUtf8(), &slice);
            file2UnSub.AppendFormat(__X("%s,"), fileID);
        }
    }
    if (!file2UnSub.empty())
    {
        file2UnSub.pop_back();
        WOLOG_INFO << "[subscription] " << "WoNotifyUnSubscribeCb, unsubscribe file: " << file2UnSub;
    }
}

} // Subscription
} // wo