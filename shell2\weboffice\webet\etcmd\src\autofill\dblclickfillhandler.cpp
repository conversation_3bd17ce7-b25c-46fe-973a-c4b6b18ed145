﻿#include "etstdafx.h"
#include "et_revision_context_impl.h"
#include "dblclickfillhandler.h"
#include "helpers/protection_helper.h"

namespace wo
{

////////////////////////////////////////////////////////////////////////////
HRESULT KDblClkFillHandle::AutoFill(ETAutoFillType etAFT, RANGE& rgSelect, KEtRevisionContext* ctx, RANGE& rgDst)
{
	KCOMPTR(IKWorkbook) ptrWBI = m_spIKWorksheet->GetWorkbook();
	if (m_ptrBookOp == NULL)
	{
		KCOMPTR(IBook) ptrBook = ptrWBI->GetBook();
		ptrBook->GetOperator(&m_ptrBookOp);
	}

	range_helper::ranges rgs;
	app_helper::GetIRanges(m_ptrRange, &rgs);
	if (rgs.size() != 1)
		return S_FALSE;

	RANGE rg = *rgs[0].second;
	if (!rg.IsValid())
	{
		ASSERT(FALSE);
		return S_FALSE;
	}
	if (rg.RowTo() == rg.GetBMP()->cntRows - 1)
		return S_FALSE;

	// 判断选中区域是否有值
	KCOMPTR(ISheet) ptrSheet;
	m_ptrBookOp->GetSheet(rg.SheetFrom(), &ptrSheet);
	BMP_PTR pBmp = ptrSheet->GetBMP();

	ASSERT(ptrSheet != NULL);
	RANGE rgUsed = *rgs[0].second;
	rgUsed.SetRowFromTo(ptrSheet->GetTop(), ptrSheet->GetBottom());
	rgUsed.SetColFromTo(ptrSheet->GetLeft(), ptrSheet->GetRight());

	RANGE rgTemp = rg;
	rgTemp.Intersect(rgUsed);
	if (rtUnknown == rgTemp.RangeType())
		return S_FALSE;
	if (FC_FULL != HasFormula(rgTemp.SheetFrom(), rgTemp.RowFrom(), rgTemp.RowTo(), rgTemp.ColFrom(), rgTemp.ColTo(), TRUE))
		return S_FALSE;

	// 终于开始工作了
	BOOL bReferColumn = FALSE;	// 具有参考列
	// 判断第一个下一行是否有值
	RANGE rgFill = rg;
	FORMULACOUNT fc = HasFormula(rg.SheetFrom(), rg.RowTo() + 1, rg.RowTo() + 1, rg.ColFrom(), rg.ColTo(), FALSE);
	if (FC_EMPTY == fc)
		rgFill = GetFillRange(TRUE, rg);
	else if (FC_SUSPENSIVE == fc)
		return S_FALSE;
	else
		rgFill = GetFillRange(FALSE, rg);

	if (rgFill.RowTo() <= rg.RowTo())
		return S_FALSE;

	KCOMPTR(Range) ptrRgFill;
	m_spIKWorksheet->GetRangeByData(&rgFill, &ptrRgFill);
	if (!ptrRgFill)
		return S_FALSE;
	rgDst = rgFill;
// 	KCOMPTR(_Workbook) ptrWorkbook;
// 	ptrWBI->QueryInterface(IID__Workbook, (void**)&ptrWorkbook);
// 	UIL_BeginUndoTrans(ptrWorkbook, TRUE, NULL)


// 		m_ptrRange->Select();
	rgSelect = rgFill;
	{
		ISheetProtection* pSheetProtection = m_spIKWorksheet->GetProtection();
		appcore_helper::KDisabledCheckExclusive disableCheck(pSheetProtection);
		CHECK_PROTECTION_ALLOW_EDIT(rgSelect, ctx);
	}
	HRESULT hr = m_ptrRange->AutoFill(ptrRgFill, etAFT, NULL);
// 	if (S_FALSE == hr)
// 	{
// 		UIL_EndUndoTRANS(E_FAIL, FALSE, TRUE);
// 	}
// 	else
// 	{
// 		UIL_EndUndoTRANS(hr, FALSE, TRUE);
// 	}
	//FixBug#153131
// 	if (SUCCEEDED(hr))
// 	{
// 		IKSmartLabelAutoFill* pSmartLabel = UilHelper::GetApp()->GetSmartLabel();
// 		pSmartLabel->SetWorkSheetView(m_pEtView->GetWorksheetView());
// 		pSmartLabel->SetSrcRanges(rg);
// 		pSmartLabel->SetDstRanges(rgFill);
// 		if (pSmartLabel->GetFillMenuItem().size() > 1)
// 			pSmartLabel->SetEnable(TRUE);
// 		else
// 			pSmartLabel->SetEnable(FALSE);
// 	}

// 	if (SUCCEEDED(hr) && S_FALSE != hr)
// 		ptrRgFill->Select();
	return hr;
}
// BOOL KDblClkFillHandle::HasFormula(IDX iSheet, int r, int c,
// 	BOOL bLeft, int* pnColumn)
// {
// 	if (pnColumn != NULL)
// 	{
// 		KCOMPTR(ISheet) ptrSheet;
// 		m_ptrBookOp->GetSheet(iSheet, &ptrSheet);
// 		KCOMPTR(IRowColOp) ptrRowColOp;
// 		ptrSheet->GetOperator(&ptrRowColOp);
// 		BMP_PTR pBmp = ptrSheet->GetBMP();
// 		while (c >= 0 && c < pBmp->cntCols &&
// 			ptrRowColOp->GetColHidden(c))
// 		{
// 			if (bLeft)
// 				--c;
// 			else
// 				++c;
// 		}
// 		if (c < 0 || c >= pBmp->cntCols)
// 			return FALSE;
// 	}
// 
// 	etexec::const_token_assist token;
// 	HRESULT hr = m_ptrBookOp->GetCellValue(iSheet, r, c, &token);
// 	AS(hr);
// 	BOOL bNotEmpty = token != NULL;
// 	if (bNotEmpty && pnColumn != NULL)
// 		*pnColumn = c;
// 	return bNotEmpty;
// }

// 参数bCheckOnce为真时，找到一个有值的单元格就返回FC_FULL
KDblClkFillHandle::FORMULACOUNT KDblClkFillHandle::HasFormula(IDX iSheet, int nRowBegin, int nRowEnd,
	int nColBegin, int nColEnd, BOOL bCheckOnce)
{
	ASSERT(nRowBegin <= nRowEnd && nColBegin <= nColEnd);
	int nCells = 0;	// 遍历的单元格个数
	int nValues = 0;
	for (; nRowBegin <= nRowEnd; ++nRowBegin)
	{
		int nCol = nColBegin;
		for (; nCol <= nColEnd; ++nCol)
		{
			++nCells;
			if (!IsCellEmpty(iSheet, nRowBegin, nCol))
				++nValues;
			if (bCheckOnce && nValues > 0)
				return FC_FULL;
			if (nValues > 0 && nValues < nCells)
				break;
		}
		if (nValues > 0 && nValues < nCells)
			break;
	}
#ifdef _DEBUG
	if (nValues > 0 && nValues < nCells)
	{
		ASSERT(nValues + 1 == nCells);
	}
#endif
	if (0 == nValues)
		return FC_EMPTY;
	else if (nValues == nCells)
		return FC_FULL;
	return FC_SUSPENSIVE;
}

BOOL KDblClkFillHandle::IsCellEmpty(IDX iSheet, int row, int col)
{
	etexec::const_token_assist token;
	HRESULT hr = m_ptrBookOp->GetCellValue(iSheet, row, col, &token);
	AS(hr);
	return NULL == token;
}

RANGE KDblClkFillHandle::GetFillRange(BOOL bUseRefer, RANGE rgSrc)
{
	class KCellAcpt : public ICellValueAcpt
	{
	public:
		KCellAcpt()
		{}
		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if (pToken)
				return 1;

			return 0;
		}
	};

	if (!rgSrc.IsValid())
		return rgSrc;

	BMP_PTR pBmp = m_spIKWorksheet->GetSheet()->GetBMP();
	long row = rgSrc.RowTo() + 1;

	ks_stdptr<ISheet> ptrSheet;
	m_ptrBookOp->GetSheet(rgSrc.SheetFrom(), &ptrSheet);

	ROW rowLast = rgSrc.RowFrom();
	ROW r = 0;
	COL c = 0;
	RANGE rgMax(ptrSheet->GetBMP());
	CELL activeCell = {rgSrc.RowFrom(), rgSrc.ColFrom()};
	appcore_helper::GetContinualRangeMaxEx(ptrSheet, rgSrc, activeCell, TRUE, &rgMax);
	rowLast = rgMax.RowTo();
	if (bUseRefer)
	{
		et_sdptr<ISheetEnum> spStEnum;
		ptrSheet->CreateEnum(&spStEnum);
		KCellAcpt acp;

		RANGE rgCur(rgSrc);
		for (r = row; r <= rgMax.RowTo(); ++r)
		{
			rgCur.SetRowFromTo(r, r);
			if (spStEnum->EnumCellValue(rgCur, &acp))
			{
				rowLast = r - 1;
				goto KS_EXIT;
			}
		}
	}
	else
	{
		for (r = row; r <= rgMax.RowTo(); ++r)
		{
			for (c = rgSrc.ColFrom(); c <= rgSrc.ColTo(); ++c)
			{
				if (IsCellEmpty(rgSrc.SheetFrom(), r, c))
				{
					rowLast = r - 1;
					goto KS_EXIT;
				}
			}
		}
	}

KS_EXIT:
	rgSrc.SetRowTo(rowLast);
	return rgSrc;
}


}
