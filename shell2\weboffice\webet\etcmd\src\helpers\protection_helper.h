#ifndef __WEBOFFICE_WEBET_HELPERS_PROTECTION_HELPER__
#define __WEBOFFICE_WEBET_HELPERS_PROTECTION_HELPER__

#include "et_revision_context_impl.h"

namespace wo
{

struct EnableProtectCompileFmla
{
    EnableProtectCompileFmla()
    {
        wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext();
        ASSERT(ctx);
        m_oldIsCompileFormula = ctx->getProtectionCtx()->isCompileFormula();
        ctx->getProtectionCtx()->setCompileFormula(true);
    }

    ~EnableProtectCompileFmla()
    {
        wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext();
        ctx->getProtectionCtx()->setCompileFormula(m_oldIsCompileFormula);
    }

  private:
    EnableProtectCompileFmla(const EnableProtectCompileFmla &);
    EnableProtectCompileFmla &operator=(const EnableProtectCompileFmla &);

    bool m_oldIsCompileFormula;
};

IEtProtectionCtx::ProtectionContentKind getFormulaRefRangeKind(IBook *pBook, range_helper::ranges &rgs);

uint32_t getProtectionContentKind(etoldapi::_Workbook* pApiWb,
                                  const RANGE &rg,
                                  bool checkAll,
                                  KEtRevisionContext *ctx);
HRESULT checkProtectionContentKind(KEtRevisionContext *ctx,
                                   etoldapi::_Workbook* pApiWb,
                                   const RANGE &rg,
                                   bool checkAll,
                                   uint32_t *pckKind);

bool isPivotTableRefHiddenRange(ISheet *isheet, pivot_core::IPivotTable *spPvtTable, KEtRevisionContext *ctx);

bool IsProtectionError(HRESULT hr);

HRESULT switchOnProtection(KEtRevisionContext* ctx, _Worksheet* pWs, LPCWSTR lpszPassword, const SHEETPROTECTION& options);
HRESULT switchOffProtection(KEtRevisionContext* ctx,_Worksheet* pWs);
HRESULT switchOffProtectedCols(_Worksheet* pWs);

void getSheetProtection(ISheet *spSheet, ISheetProtection **ptrSheetProtection);
bool isSheetHasProtectedCols(ISheet* pSheet);

//因为旧et表格(有密码)与ksheet表格(空密码)开启保护所用的密码机制是不一样的，下面的方法只能用于ksheet新表格，不可用于旧et表格，用之前千万得注意。
//旧et如果支持区域保护也可以使用该方法
bool needSwitchOffProtection(KEtRevisionContext* ctx, IDX sheetIdx, _Worksheet* pWs);

// 清除权限相关数据
bool clearProtection(_Worksheet* pWs);

bool isRegionProtect(IKWorksheet* pWorksheet);
bool isEnableRegionProtect(IKWorksheet* pWorksheet);
ProtectionAccessPerms getAccessPermission(const binary_wo::VarObj &obj, const char* name, ProtectionAccessPerms defaultRight);

}; // namespace wo

#define CHECK_PROTECTION_ALLOW_EDIT(rg, ctx)                                                                 \
    do                                                                                                       \
    {                                                                                                        \
        auto accessPerm = PTAAP_None;                                                                        \
        if (!(ctx)->getProtectionCtx()->isAllowEdit((rg), &accessPerm))                                      \
        {                                                                                                    \
            if (accessPerm == PTAAP_Invisible)                                                               \
            {                                                                                                \
                return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;                                            \
            }                                                                                                \
            else if (accessPerm == PTAAP_Exclusive)                                                          \
            {                                                                                                \
                return E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE;                                          \
            }                                                                                                \
            else                                                                                             \
            {                                                                                                \
                ASSERT(accessPerm == PTAAP_Visible);                                                         \
                return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;                                         \
            }                                                                                                \
        }                                                                                                    \
    } while (false);

#define CHECK_PROTECTION_ALLOW_READ(rg, ctx)                                                                 \
    do                                                                                                       \
    {                                                                                                        \
        if ((ctx)->getProtectionCtx()->isRangeHasHidden(rg))                                                 \
        {                                                                                                    \
            return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;                                                \
        }                                                                                                    \
    } while (false);

#define CHECK_RANGES_HAS_HIDDEN(rgs, ctx)                                                                    \
    do                                                                                                       \
    {                                                                                                        \
        for (size_t i = 0; i < rgs.size(); ++i)                                                              \
        {                                                                                                    \
            if ((ctx)->getProtectionCtx()->isRangeHasHidden(rgs.at(i)))                                      \
            {                                                                                                \
                return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;                                            \
            }                                                                                                \
        }                                                                                                    \
    } while (false);

#define CHECK_PROTECTION_FORMULA(rg, flm, ctx)                                                               \
    do                                                                                                       \
    {                                                                                                        \
        HRESULT hr = (ctx)->getProtectionCtx()->checkFormula(rg, flm);                                       \
        if (hr != S_OK)                                                                                      \
        {                                                                                                    \
            return hr;                                                                                       \
        }                                                                                                    \
    } while (false);

#define CHECK_BOOK_HAS_HIDDEN_PROP_RANGE(ctx)                                                                \
    do                                                                                                       \
    {                                                                                                        \
        if ((ctx)->getProtectionCtx()->isBookHasHiddenProperty())                                            \
        {                                                                                                    \
            return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_BOOK;                                                 \
        }                                                                                                    \
    } while (false);

#define CHECK_BOOK_HAS_HIDDEN_PROP_RANGE_FLMA(ctx)                                                           \
    do                                                                                                       \
    {                                                                                                        \
        if ((ctx)->getProtectionCtx()->isBookHasHiddenProperty())                                            \
        {                                                                                                    \
            return E_FORMULA_NOT_SUPPORTED_ON_HIDDEN_BOOK;                                                   \
        }                                                                                                    \
    } while (false);

#define CHECK_PROTECTION_CONTENT(rg, ctx)                                                                    \
    do                                                                                                       \
    {                                                                                                        \
        HRESULT hr = ctx->getProtectionCtx()->checkProtectionContent(rg);                                    \
        if (S_OK != hr)                                                                                      \
        {                                                                                                    \
            return hr;                                                                                       \
        }                                                                                                    \
    } while (false);

#define CHECK_PROTECTION_COPYRANGE(rg, ctx)                                                                  \
    do                                                                                                       \
    {                                                                                                        \
        HRESULT hr = ctx->getProtectionCtx()->checkProtectionCopyContent(rg);                                \
        if (S_OK != hr)                                                                                      \
        {                                                                                                    \
            return hr;                                                                                       \
        }                                                                                                    \
    } while (false);

#define CHECK_PROTECTION_CUTRANGE(rg, ctx)                                                                   \
    do                                                                                                       \
    {                                                                                                        \
        if (ctx->getProtectionCtx()->isRangeHasHidden(rg))                                                   \
        {                                                                                                    \
            return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;                                                \
        }                                                                                                    \
        auto kind = ctx->getProtectionCtx()->getProtectionContentKind(rg, true);                             \
        if (kind != IEtProtectionCtx::pckNone)                                                               \
        {                                                                                                    \
            AddExtErrorValue(kind);                                                                          \
            return E_APPLY_CONTENT_NOT_SUPPORTED_ON_HIDDEN_BOOK;                                             \
        }                                                                                                    \
    } while (false);

#define CHECK_PROTECTION_FILL_SRCRANGE_CONTENT(rg, ctx)                                                      \
    do                                                                                                       \
    {                                                                                                        \
        uint32_t kind = IEtProtectionCtx::pckNone;                                                           \
        HRESULT hr = ctx->getProtectionCtx()->checkProtectionContent(rg, &kind);                             \
        if (kind != IEtProtectionCtx::pckNone)                                                               \
        {                                                                                                    \
            AddExtErrorValue(kind);                                                                          \
            return hr;                                                                                       \
        }                                                                                                    \
    } while (false);

#define CHECK_PROTECTION_CUTPASTE_RANGE(rg, ctx)                                                             \
    do                                                                                                       \
    {                                                                                                        \
        if (ctx->getProtectionCtx()->isRangeHasHidden(rg))                                                   \
        {                                                                                                    \
            return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;                                                \
        }                                                                                                    \
        CHECK_PROTECTION_ALLOW_EDIT((rg), (ctx));                                                            \
    } while (false);

#endif // __WEBOFFICE_WEBET_HELPERS_PROTECTION_HELPER__