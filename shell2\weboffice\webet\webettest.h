﻿#ifndef __WEB_ET_TEST_H__
#define __WEB_ET_TEST_H__

class IWoSignalMetrics;
interface IBook;
namespace etoldapi {
	interface _Workbook;
}
namespace wo
{
	interface OpTransformation;
	
	class ITestGetter
	{
	public:
		virtual ~ITestGetter() {}
		virtual etoldapi::_Workbook* GetWorkbook() = 0;
		virtual IBook* GetBook() = 0;
		virtual IWoSignalMetrics * CreateSignalMetrics() = 0;
	};
}

WEB_EXPORT wo::OpTransformation* CreateOpTransformation();
WEB_EXPORT wo::ITestGetter* TestGetter();

#endif //__WEB_ET_LINK_H__
