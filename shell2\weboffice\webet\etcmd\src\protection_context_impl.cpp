#include "etstdafx.h"

#include "et_revision_context_impl.h"
#include "helpers/protection_helper.h"
#include "webbase/logger.h"
#include "wo/workbook_obj.h"
#include "workbook.h"
#include "wo/wo_protection_helper.h"

#include "protection_context_impl.h"
#include "util.h"
#include "appcore/et_appcore_shared_link_sheet.h"
#include "appcore/et_appcore_enum.h"

extern Callback* gs_callback;

namespace wo
{

KProtectionContext::KProtectionContext(etoldapi::_Workbook* pApiWb, IWorkbookObj *workbookObj, KEtRevisionContext *ctx)
    : m_ibook(pApiWb->GetBook()),
      m_ctx(ctx),
      m_pApiWb(pApiWb),
      m_workbookObj(workbookObj),
      m_hasProtectionCache(false),
      m_hasSheetProtectedCache(false),
      m_isBookHasEditableCache(false),
      m_isBook<PERSON>asHiddenCache(false),
      m_isBookHasReadonlyCache(false),
      m_isBookHasHiddenPropCache(false),
      m_isBookHasReadonlyPropCache(false),
      m_isCompileFormula(true),
      m_isAllowDelLockedRowCol(false),
      m_pasteContent(wo::IEtProtectionCtx::pckNone),
      m_woCommand(IEtProtectionCtx::wocmdInvalid),
      m_validRefKind(IProtectionFormula::rrfSelfVisible),
      m_isBookHasColProtectCache(false),
      m_isAllUserColPermsChanged(false),
      m_isConnSharedLinkCache(false),
      m_hasConnSharedLinkCache(false),
      m_protectionType(ptNone)
{
}

KProtectionContext::~KProtectionContext()
{
}

IBook *KProtectionContext::getBook()
{
    return m_ibook;
}

void KProtectionContext::setupProtectionCache(ISheet *spSheet)
{
	if (!m_hasSheetProtectedCache)
	{
		ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(spSheet);
		if (pSheetProtection && pSheetProtection->IsContentsProtected())
		{
			m_hasSheetProtectedCache = true;
		}
	}

	if (!m_isBookHasEditableCache && isSheetHasEditable(spSheet))
	{
		m_isBookHasEditableCache = true;
	}

	if (!m_isBookHasHiddenCache && isSheetHasHidden(spSheet))
    {
        m_isBookHasHiddenCache = true;
        m_isBookHasHiddenPropCache = true;
    }

    if (!m_isBookHasHiddenPropCache && isSheetHasHiddenProperty(spSheet))
    {
        m_isBookHasHiddenPropCache = true;
    }

    if (!m_isBookHasReadonlyCache && isSheetHasReadonly(spSheet))
    {
        m_isBookHasReadonlyCache = true;
        m_isBookHasReadonlyPropCache = true;
    }

    if (!m_isBookHasReadonlyPropCache && isSheetHasReadonlyProperty(spSheet))
    {
        m_isBookHasReadonlyPropCache = true;
    }

    if (!m_isBookHasColProtectCache && isSheetHasProtectedCols(spSheet))
    {
        m_isBookHasColProtectCache = true;
    }

    if (!m_hasConnSharedLinkCache && isConnSharedLink())
    {
        m_hasConnSharedLinkCache = true;
    }
}

void KProtectionContext::setHasProtectionCache(bool bl)
{
    m_hasProtectionCache = true;
}

void KProtectionContext::setupEtContext()
{
    ASSERT(m_hasProtectionCache);
}

void KProtectionContext::clearEtContext()
{
}

ks_stdptr<ISheetProtection> KProtectionContext::getSheetProtection(ISheet *spSheet)
{
    return wo::util::getSheetProtection(spSheet);
}

bool KProtectionContext::ensureProtectionCacheSheets(ISheet *isheet)
{
    IDX idxSheet = 0;
    isheet->GetIndex(&idxSheet);
    AbsObject *obj = m_workbookObj->getSheetItem(idxSheet);
    if (obj)
    {
        m_protectionCacheSheets.insert(obj->objId());
        return true;
    }
    else
    {
        ASSERT(false);
        WOLOG_INFO << "[protection] ensureProtectionCache error, " << idxSheet;
    }

    return false;
}

void KProtectionContext::ensureProtectionAreaCache(ISheet *isheet, ISheetProtection *pSheetProtection)
{
    if (ensureProtectionCacheSheets(isheet))
    {
        pSheetProtection->EnsureAreaCache();
    }
}

bool KProtectionContext::isAllHidden()
{
    return false; // et 目前没有隐藏所有内容的设置
}

bool KProtectionContext::isSheetHidden(UINT stId)
{
    if (isConnSharedLink())
    {
        if (isSheetShared(stId))
            return false;
        IDX sheetIdx = INVALIDIDX;
        m_ibook->STSheetToRTSheet(stId, &sheetIdx);
        if (sheetIdx == INVALIDIDX)
			return true;
        ks_stdptr<ISheet> spSheet;
        m_ibook->GetSheet(sheetIdx, &spSheet);
        if (spSheet->IsAppSheet())
        {
            if (isAppSheetCanVisit(spSheet))
                return false;
        }
        return true;
    }

    return false; // et 目前没有隐藏所有内容的设置
}

PCWSTR KProtectionContext::getSharedLink()
{
    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
    m_ibook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    wo::IEtRevisionContext *pRvsCtx = _etcore_GetEtRevisionContext();
    if (!spSharedLinkMgr || !pRvsCtx || !pRvsCtx->getUser())
        return nullptr;

    PCWSTR curSharedId = spSharedLinkMgr->GetConnSharedLink(pRvsCtx->getUser());
    return curSharedId;
}

bool KProtectionContext::isAppSheetCanVisit(ISheet* pSheet)
{
    PCWSTR curSharedId = getSharedLink();
    if (curSharedId == nullptr)
        return true;

    ks_stdptr<IAppSheetData> spAppSheetData;
    pSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spAppSheetData);
    IAirApp* pApp = spAppSheetData->GetApp(0);
    if (pApp->GetCategory() == KSheet_AirAppCat_DbSheetView)
    {
        ks_stdptr<IAirApp_DBView> spApp_DBView = pApp;
        IDBSheetView* pView = spApp_DBView->GetDBSheetView();
        if (pView)
        {
            IDBSharedLinkView* pSharedLink = pView->GetSharedLink();
            if (pSharedLink == nullptr)
                return false;
            PCWSTR sharedId = pSharedLink->Id();
            if (sharedId == nullptr)
                return false;
            if (xstrcmp(sharedId, curSharedId) == 0)
                return true;
        }
    }
    return false;
}

bool KProtectionContext::isSheetShared(UINT stId)
{
    if (!isConnSharedLink() || !m_spSharedLink)
        return false;
    
    switch(m_spSharedLink->Type())
    {
        case SharedLinkType_DbView:
            break;
        case SharedLinkType_Sheet:
        {
            ks_stdptr<ISharedLinkSheet> spSharedLinkSheet = m_spSharedLink;
            if (spSharedLinkSheet && spSharedLinkSheet->GetSheet()->GetStId() == stId)
                return true;
            break;
        }
        default:
            ASSERT(FALSE);
    }
    return false;
}

bool KProtectionContext::canRefColProtect(const RANGE& rg)
{
    if (!rg.IsValid() || !isBookHasColProtect())
    {
        return true;
    }

    const bool res = wo::util::ForEachSheet(getBook(), rg.SheetFrom(), rg.SheetTo(), [this, &rg](ISheet *spSheet, IDX sheetIdx) -> bool {
        ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(spSheet);
        if (pSheetProtection && pSheetProtection->IsProtected())
        {
            if (!pSheetProtection->CanRefColProtect(rg))
            {
                return true;
            }
        }

        return false;
    });

    return !res;
}

bool KProtectionContext::hasHiddenColProtect(ISheet* spSheet, const RANGE& rg)
{
    if (!spSheet || !isBookHasColProtect())
    {
        return false;
    }

    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(spSheet);
    if (pSheetProtection && pSheetProtection->IsProtected())
    {
        if (!pSheetProtection->CanRefColProtect(rg))
        {
            return true;
        }
    }

    return false;
}

bool KProtectionContext::isCellHidden(ISheet *isheet, INT32 row, INT32 col)
{
    if (isheet && isSheetHidden(isheet->GetStId()))
    {
        return true; // 分享状态sheet隐藏的情况下cell应该也是隐藏的
    }

    if (!isheet || !isBookHasHidden())
    {
        return false;
    }

    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection->IsProtected() && m_ctx->getUser())
    {
        ensureProtectionAreaCache(isheet, pSheetProtection);
        return pSheetProtection->IsCellVeryHidden(m_ctx->getUser()->userID(), row, col);
    }

    return false;
}

bool KProtectionContext::isRangeHasHidden(const RANGE &rg, bool ignoreShared)
{
    if (!rg.IsValid() || (!isBookHasHidden() && !isConnSharedLink()))
    {
        return false;
    }

    return wo::util::ForEachSheetRange(getBook(), rg, [&](ISheet *spSheet, IDX sheetIdx, const RANGE &oneSheetRg) -> bool {
        if (isRangeHasHidden(spSheet, oneSheetRg, ignoreShared))
        {
            return true;
        }
        return false;
    });
}

bool KProtectionContext::isRangeHasHidden(IKRanges *rgs, bool ignoreShared)
{
    if (!rgs || (!isBookHasHidden() && !isConnSharedLink()))
    {
        return false;
    }

    UINT count = 0;
    rgs->GetCount(&count);
    for (UINT i = 0; i < count; ++i)
    {
        const RANGE *rg = nullptr;
        rgs->GetItem(i, NULL, &rg);
        if (isRangeHasHidden(*rg, ignoreShared))
        {
            return true;
        }
    }

    return false;
}

bool KProtectionContext::isRangeHasHidden(ISheet *isheet, const RANGE &rg, bool ignoreShared)
{
    if (isheet && !ignoreShared && isSheetHidden(isheet->GetStId()))
    {
        return true;
    }

    if (!isheet || !rg.IsValid() || !isBookHasHidden())
    {
        return false;
    }

    ASSERT(rg.SheetFrom() == rg.SheetTo());
    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection->IsProtected() && m_ctx->getUser())
    {
        ensureProtectionAreaCache(isheet, pSheetProtection);
        return pSheetProtection->IsRangeHasHidden(m_ctx->getUser()->userID(), rg);
    }

    return false;
}

void KProtectionContext::splitVisibleRange(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg)
{
    if (!isheet || !rg.IsValid() || !isBookHasHidden())
    {
        vctVisibleRg.push_back(rg);
        return;
    }

    ASSERT(rg.SheetFrom() == rg.SheetTo());
    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection->IsProtected() && m_ctx->getUser())
    {
        ensureProtectionAreaCache(isheet, pSheetProtection);
        pSheetProtection->SplitVisibleRange(m_ctx->getUser()->userID(), rg, vctVisibleRg);
    }
    else
    {
        vctVisibleRg.push_back(rg);
    }
}

void KProtectionContext::splitHiddenRange(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges)
{
    if (!isheet || !rg.IsValid() || !isBookHasHidden())
    {
        return;
    }

    ASSERT(rg.SheetFrom() == rg.SheetTo());
    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection->IsProtected() && m_ctx->getUser())
    {
        ensureProtectionAreaCache(isheet, pSheetProtection);
        pSheetProtection->SplitHiddenRange(m_ctx->getUser()->userID(), rg, hiddenRanges);
    }
}

void KProtectionContext::splitVisibleRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg)
{
    if (!isheet || !rg.IsValid() || !isBookHasHidden())
    {
        vctVisibleRg.push_back(rg);
        return;
    }

    ASSERT(rg.SheetFrom() == rg.SheetTo());
    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection->IsProtected() && m_ctx->getUser())
    {
        ensureProtectionAreaCache(isheet, pSheetProtection);
        pSheetProtection->SplitVisibleRangeForClear(m_ctx->getUser()->userID(), rg, vctVisibleRg);
    }
    else
    {
        vctVisibleRg.push_back(rg);
    }
}

void KProtectionContext::splitHiddenRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges)
{
    if (!isheet || !rg.IsValid() || !isBookHasHidden())
    {
        return;
    }

    ASSERT(rg.SheetFrom() == rg.SheetTo());
    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection->IsProtected() && m_ctx->getUser())
    {
        ensureProtectionAreaCache(isheet, pSheetProtection);
        pSheetProtection->SplitHiddenRangeForClear(m_ctx->getUser()->userID(), rg, hiddenRanges);
    }
}

ProtectionAccessPerms KProtectionContext::getAccessPermWidthRange(const RANGE &rg)
{
    if (!rg.IsValid() || (!isBookHasHidden() && !isBookHasReadonly()))
    {
        return PTAAP_Edit;
    }

    IBook *pBook = getBook();
    uint32_t perms = PTAAP_Edit;

    wo::util::ForEachSheetRange(pBook, rg, [&](ISheet *spSheet, IDX, const RANGE &oneSheetRg) -> bool {
        ks_stdptr<ISheetProtection> pProtection = getSheetProtection(spSheet);
        if (pProtection->IsProtected() && m_ctx->getUser())
        {
            ensureProtectionAreaCache(spSheet, pProtection);
            uint32_t curPerm = pProtection->GetAccessPermWidthRange(m_ctx->getUser()->userID(), oneSheetRg);
            perms &= curPerm;
        }

        return false;
    });

    return ProtectionAccessPerms(perms);
}

bool KProtectionContext::isSheetHasHidden(ISheet *isheet)
{
    if (!isheet)
    {
        return false;
    }

    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection && pSheetProtection->IsProtected() && m_ctx->getUser())
    {
        return pSheetProtection->IsInHiddenRanges(m_ctx->getUser()->userID());
    }

    return false;
}

bool KProtectionContext::isBookHasHidden(IBook *ibook)
{
    if (!ibook)
    {
        return false;
    }

    INT shtCnt = 0;
    ibook->GetSheetCount(&shtCnt);
    for (INT i = 0; i < shtCnt; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        ibook->GetSheet(i, &spSheet);
        if (isSheetHasHidden(spSheet))
        {
            return true;
        }
    }

    return false;
}

bool KProtectionContext::isBookHasHidden()
{
    if (hasProtectionCache())
    {
        return isBookHasHiddenCache();
    }

    return isBookHasHidden(getBook());
}

bool KProtectionContext::isBookHasHidden(IKWorkbook *workbook)
{
    return isBookHasHidden(workbook->GetBook());
}

bool KProtectionContext::isSheetHasHiddenProperty(ISheet *isheet)
{
    if (!isheet)
    {
        return false;
    }

    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection && pSheetProtection->IsProtected())
    {
        return pSheetProtection->HasHiddenPropety();
    }

    return false;
}

bool KProtectionContext::isBookHasHiddenProperty(IBook *ibook)
{
    if (!ibook)
    {
        return false;
    }

    INT shtCnt = 0;
    ibook->GetSheetCount(&shtCnt);
    for (INT i = 0; i < shtCnt; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        ibook->GetSheet(i, &spSheet);
        if (isSheetHasHiddenProperty(spSheet))
        {
            return true;
        }
    }

    return false;
}

bool KProtectionContext::isRangeHasHiddenProperty(const RANGE &rg, bool checkUnprotected /* = false */)
{
    if (!rg.IsValid())
    {
        return false;
    }

    IBook *pBook = getBook();
    return wo::util::ForEachSheetRange(pBook, rg, [&](ISheet *spSheet, IDX, const RANGE &oneSheetRg) -> bool {
        ks_stdptr<ISheetProtection> pProtection = getSheetProtection(spSheet);
        if (checkUnprotected || pProtection->IsProtected())
        {
            ensureProtectionAreaCache(spSheet, pProtection);
            if (pProtection->IsRangeHasHiddenProperty(oneSheetRg, checkUnprotected))
            {
                return true;
            }
            return false;
        }

        return false;
    });
}

bool KProtectionContext::isBookHasHiddenProperty()
{
    if (hasProtectionCache())
    {
        return isBookHasHiddenPropCache();
    }

    return isBookHasHiddenProperty(getBook());
}

bool KProtectionContext::isSheetHasReadonly(ISheet *isheet)
{
    if (!isheet)
    {
        return false;
    }

    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection && pSheetProtection->IsProtected() && m_ctx->getUser())
    {
        return pSheetProtection->IsInReadonlyRanges(m_ctx->getUser()->userID());
    }

    return false;
}

bool KProtectionContext::isBookHasReadonly(IBook *ibook)
{
    if (!ibook)
    {
        return false;
    }

    INT shtCnt = 0;
    ibook->GetSheetCount(&shtCnt);
    for (INT i = 0; i < shtCnt; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        ibook->GetSheet(i, &spSheet);
        if (isSheetHasReadonly(spSheet))
        {
            return true;
        }
    }

    return false;
}

bool KProtectionContext::isBookHasReadonly()
{
    if (hasProtectionCache())
    {
        return isBookHasReadonlyCache();
    }

    return isBookHasReadonly(getBook());
}

bool KProtectionContext::isSheetHasReadonlyProperty(ISheet *isheet)
{
    if (!isheet)
    {
        return false;
    }

    ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
    if (pSheetProtection && pSheetProtection->IsProtected())
    {
        return pSheetProtection->HasReadonlyPropety();
    }

    return false;
}

bool KProtectionContext::isBookHasReadonlyProperty(IBook *ibook)
{
    if (!ibook)
    {
        return false;
    }

    INT shtCnt = 0;
    ibook->GetSheetCount(&shtCnt);
    for (INT i = 0; i < shtCnt; ++i)
    {
        ks_stdptr<ISheet> spSheet;
        ibook->GetSheet(i, &spSheet);
        if (isSheetHasReadonlyProperty(spSheet))
        {
            return true;
        }
    }

    return false;
}

bool KProtectionContext::isSheetHasEditable(ISheet *isheet)
{
	if (!isheet)
	{
		return false;
	}

	ks_stdptr<ISheetProtection> pSheetProtection = getSheetProtection(isheet);
	if (pSheetProtection && pSheetProtection->IsProtected() && m_ctx->getUser())
	{
		return pSheetProtection->IsInEditableRanges(m_ctx->getUser()->userID());
	}

	return false;
}

bool KProtectionContext::isBookHasReadonlyProperty()
{
    if (hasProtectionCache())
    {
        return isBookHasReadonlyPropCache();
    }

    return isBookHasReadonlyProperty(getBook());
}

bool KProtectionContext::isBookHasSheetProtected()
{
	ASSERT(hasProtectionCache());
	return m_hasSheetProtectedCache;
}

bool KProtectionContext::isThisBookHasEditable()
{
	ASSERT(hasProtectionCache());
	return m_isBookHasEditableCache;
}

bool KProtectionContext::hasProtectionCache() const
{
    return m_hasProtectionCache;
}

void KProtectionContext::clearProtectionCache()
{
    m_hasProtectionCache = false;

    for (auto it = m_protectionCacheSheets.begin(); it != m_protectionCacheSheets.end(); ++it)
    {
        int sheetIndex = m_ctx->getSheetIndex(*it);
        ASSERT(sheetIndex >= 0);
        int sheetCount = m_pApiWb->GetWorksheets()->GetSheetCount();
        if (sheetIndex < sheetCount)
        {
            IKWorksheet *pWorksheet = m_pApiWb->GetWorksheets()->GetSheetItem(sheetIndex);
            if (pWorksheet)
            {
                ks_castptr<_Worksheet> pApiWs = pWorksheet;
                ISheetProtection *pSheetProtection = pApiWs->GetProtection();
                pSheetProtection->ClearAreaCache();
            }
        }
    }

    m_protectionCacheSheets.clear();
}

bool KProtectionContext::isBookHasHiddenCache() const
{
    ASSERT(m_hasProtectionCache);
    return m_isBookHasHiddenCache;
}

bool KProtectionContext::isBookHasHiddenPropCache() const
{
    ASSERT(m_hasProtectionCache);
    return m_isBookHasHiddenPropCache;
}

bool KProtectionContext::isBookHasReadonlyCache() const
{
    ASSERT(m_hasProtectionCache);
    return m_isBookHasReadonlyCache;
}

bool KProtectionContext::isBookHasReadonlyPropCache() const
{
    ASSERT(m_hasProtectionCache);
    return m_isBookHasReadonlyPropCache;
}

bool KProtectionContext::isBookHasColProtectCache() const
{
    ASSERT(m_hasProtectionCache);
    return m_isBookHasColProtectCache;
}

 bool KProtectionContext::isBookHasColProtect()
 {
    ASSERT(hasProtectionCache());
    return m_isBookHasColProtectCache;
 }

bool KProtectionContext::isCellImageFormula(const_token_vector vecToken)
{
    if (!vecToken)
    {
        return false;
    }

    int count = 0;
    vecToken->GetSize(&count);
    if (count > 0)
    {
        const_token_ptr funcToken = NULL;
        vecToken->GetItem(count - 1, &funcToken);
        if (funcToken && GetExecTokenMajorType(funcToken) == alg::ETP_FUNCTION)
        {
            alg::const_function_token_assist func(funcToken);
            return func.get_id() == FNID_DISPIMG;
        }
    }

    return false;
}

bool KProtectionContext::isCellImageFormula(PCWSTR formula)
{
    if (!formula)
    {
        return false;
    }

    return xstrstr(formula, __X("=DISPIMG")) == formula;
}

HRESULT KProtectionContext::checkAllowEdit(const RANGE &rg)
{
    auto accessPerm = PTAAP_None;
    if (!isAllowEdit(rg, &accessPerm))
    {
        if (accessPerm == PTAAP_Invisible)
        {
            return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
        }
        else if (accessPerm == PTAAP_Exclusive)
		{
			return E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE;
		}
        else
        {
            ASSERT(accessPerm == PTAAP_Visible);
            return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;
        }
    }

    return S_OK;
}

HRESULT KProtectionContext::checkAllowEditWidthLockedCell(const RANGE &rg)
{
    if (!rg.IsValid())
        return S_OK;
    
    IBook *pBook = getBook();
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(rg.SheetFrom(), &spSheet);
    if (!spSheet)
        return S_OK;

    ks_stdptr<ISheetProtection> protection = getSheetProtection(spSheet);
    if (!protection)
        return S_OK;

    if (!protection->IsCanExclusiveRange(m_ctx->getUser()->userID(), rg))
        return  E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE;;

    appcore_helper::KDisabledCheckExclusive disableCheck(protection);
    if (!protection->IsProtected())
        return S_OK;

	ks_stdptr<IAppCoreRange> spCoreRange;
	_appcore_CreateObject(CLSID_KAppCoreRange, IID_IAppCoreRange, (void**)&spCoreRange);
	if (!spCoreRange)
		return E_FAIL;

	if (FAILED(spCoreRange->CreateInSheet(spSheet, NULL, NULL)))
		return E_FAIL;

    spCoreRange->Append(rg);
    HRESULT res = S_OK; 
    spCoreRange->IsAllowedEdit(&res);
    return res;
}

HRESULT KProtectionContext::isOperationAllowedProtected(const RANGE &rg, et_appcore::ActionType acType)
{
    if (!rg.IsValid())
        return S_OK;
    
    IBook *pBook = getBook();
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(rg.SheetFrom(), &spSheet);
    if (!spSheet)
        return S_OK;

    ks_stdptr<ISheetProtection> protection = getSheetProtection(spSheet);
    if (!protection)
        return S_OK;

	PCWSTR userID = m_ctx->getUser()->userID();
	if (!protection->IsCanExclusiveRange(userID, rg))
        return E_OPERATION_NOT_SUPPORTED_EDIT_ON_EXCLUSIVE;

     if (!protection->IsProtected())
        return S_OK;

	ks_stdptr<IAppCoreRange> spCoreRange;
	_appcore_CreateObject(CLSID_KAppCoreRange, IID_IAppCoreRange, (void**)&spCoreRange);
	if (!spCoreRange)
		return E_FAIL;

	if (FAILED(spCoreRange->CreateInSheet(spSheet, NULL, NULL)))
		return E_FAIL;

    spCoreRange->Append(rg);
    return spCoreRange->isOperationAllowedProtected(acType);
}

bool KProtectionContext::isAllowEdit(const RANGE &rg, ProtectionAccessPerms *out)
{
    if (!rg.IsValid())
    {
        if (out)
            *out = PTAAP_Edit;
        return true;
    }
    
    if (!checkExclusive(rg, out))
        return false;

    auto right = getAccessPermWidthRange(rg);
    if (out)
    {
        *out = right;
    }
    return (right & PTAAP_Edit) == PTAAP_Edit;
}

bool KProtectionContext::isAllowEdit(IKRanges *rgs, ProtectionAccessPerms *right)
{
    if (!rgs)
    {
        return true;
    }

    UINT count = 0;
    rgs->GetCount(&count);
    for (UINT i = 0; i < count; ++i)
    {
        const RANGE *rg = nullptr;
        rgs->GetItem(i, NULL, &rg);
        if (!isAllowEdit(*rg, right))
        {
            return false;
        }
    }

    return true;
}

bool KProtectionContext::isAllowEdit(const std::vector<RANGE> &ranges, ProtectionAccessPerms *right)
{
    for (auto i = ranges.begin(); i != ranges.end(); ++i)
    {
        if (!isAllowEdit(*i, right))
        {
            return false;
        }
    }

    return true;
}

bool KProtectionContext::checkProtectionOnSheet(IDX sheetIndex)
{
    IBook *pBook = getBook();
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(sheetIndex, &spSheet);
    return !isSheetHasHidden(spSheet);
}

bool KProtectionContext::isRangeAllHidden(const RANGE &rg)
{
    if (!rg.IsValid())
    {
        return false;
    }

    if (!isBookHasHidden())
    {
        // 创建 ISheet 有点耗时~~
        return false;
    }

    IBook *pBook = getBook();
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(rg.SheetFrom(), &spSheet);

    std::vector<RANGE> vctVisibleRg;
    splitVisibleRange(spSheet, rg, vctVisibleRg);
    return vctVisibleRg.empty();
}

bool KProtectionContext::isProtectionAreaUnlock(LPCWSTR id)
{
    ks_castptr<IKETUserConn> user = m_ctx->getUser();
	return user->isProtectionAreaUnlock(id);
}

bool KProtectionContext::isAllowedFormula(PCWSTR formula)
{
	auto isFormulaStr = [this] (PCWSTR str) -> bool {
		EnableProtectCompileFmla guard;
		return wo::isFormulaStr(m_pApiWb->GetBook(), str);
	};

	if (formula && isBookHasHiddenProperty())
	{
		if (!isCellImageFormula(formula) && isFormulaStr(formula))
		{
			return false;
		}
	}

	return true;
}

HRESULT KProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, const_token_ptr token)
{
    if (token && needCheckFormula())
    {
        if (token && GetExecTokenMajorType(token) == alg::ETP_VSTR)
        {
            alg::const_vstr_token_assist str(token);
            return checkFormula(iSheet, row, col, str.get_value());
        }
    }

    return S_OK;
}

HRESULT KProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, const_token_vector vecToken)
{
    if (vecToken && needCheckFormula())
    {
        if (isCellImageFormula(vecToken))
        {
            return S_OK;
        }

        return isValidFormula(iSheet, row, col, vecToken);
    }

    return S_OK;
}

HRESULT KProtectionContext::checkFormula(const RANGE &rg, const_token_vector vecToken)
{
    if (vecToken && needCheckFormula())
    {
        if (isCellImageFormula(vecToken))
        {
            return S_OK;
        }

        return isValidFormula(rg, vecToken);
    }

    return S_OK;
}

HRESULT KProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, PCWSTR fmla)
{
    if (fmla && needCheckFormula())
    {
        EnableProtectCompileFmla guard;
        ks_stdptr<IBookOp> spBookOp;
        m_ibook->GetOperator(&spBookOp);

        ks_stdptr<IFormula> spFmla;
        spBookOp->CreateFormula(&spFmla);
        CS_COMPILE_PARAM ccp(cpfSysDefault, iSheet, row, col);

        COMPILE_RESULT cr;
        spFmla->SetFormula(fmla, ccp, &cr);
        if (cr.nErrCode != COMPILE_SUCCESS)
            return E_FORMULA_NOT_SUPPORTED_ON_HIDDEN_BOOK;

        BOOL bFmla = FALSE;
        ks_stdptr<ITokenVectorInstant> ptrStTokens;
        spFmla->GetContent(&bFmla, &ptrStTokens, NULL);
        if (bFmla)
        {
            return checkFormula(iSheet, row, col, ptrStTokens);
        }
    }

    return S_OK;
}

HRESULT KProtectionContext::checkFormula(const RANGE &rg, PCWSTR fmla)
{
    if (fmla && needCheckFormula())
    {
        EnableProtectCompileFmla guard;
        ks_stdptr<IBookOp> spBookOp;
        m_ibook->GetOperator(&spBookOp);

        ks_stdptr<IFormula> spFmla;
        spBookOp->CreateFormula(&spFmla);
        CS_COMPILE_PARAM ccp(cpfReOffset, rg.SheetFrom(), rg.RowFrom(), rg.ColFrom());

        COMPILE_RESULT cr;
        spFmla->SetFormula(fmla, ccp, &cr);
        if (cr.nErrCode != COMPILE_SUCCESS)
            return E_FORMULA_NOT_SUPPORTED_ON_HIDDEN_BOOK;

        BOOL bFmla = FALSE;
        ks_stdptr<ITokenVectorInstant> ptrStTokens;
        spFmla->GetContent(&bFmla, &ptrStTokens, NULL);
        if (bFmla)
        {
            return checkFormula(rg, ptrStTokens);
        }
    }

    return S_OK;
}

HRESULT KProtectionContext::checkDVFormula(const RANGE &rg, const VALIDATION *dv)
{
    if (dv && needCheckFormula())
    {
        ks_bstr str;
        IBookStake *pBookStake = m_ibook->GetWoStake();

        uint32_t refKind = 0;
        return pBookStake->getProtectionFormula()->checkDVFormula(rg, dv->DVType, dv->bsFormula1, dv->bsFormula2, m_validRefKind, &refKind);
    }

    return S_OK;
}

HRESULT KProtectionContext::checkDVFormula(const RANGE &rg, DVValueType dvType, PCWSTR pFormula1, PCWSTR pFormula2)
{
    if (needCheckFormula())
    {
        IBookStake *pBookStake = m_ibook->GetWoStake();
        uint32_t refKind = 0;
        return pBookStake->getProtectionFormula()->checkDVFormula(rg, dvType, pFormula1, pFormula2, m_validRefKind, &refKind);
    }

    return S_OK;
}

HRESULT KProtectionContext::checkDVFormula(const RANGE &rg,
                                           DVValueType dvType,
                                           ITokenVectorInstant *pFormula1,
                                           ITokenVectorInstant *pFormula2)
{
    if (needCheckFormula())
    {
        IBookStake *pBookStake = m_ibook->GetWoStake();
        uint32_t refKind = 0;
        return pBookStake->getProtectionFormula()->checkDVFormula(rg, dvType, pFormula1, pFormula2, m_validRefKind, &refKind);
    }

    return S_OK;
}

HRESULT KProtectionContext::checkDVFormulaInRange(const RANGE &rg)
{
    if (needCheckFormula())
    {
        IBookStake *pBookStake = m_ibook->GetWoStake();
        uint32_t refKind = 0;
        return pBookStake->getProtectionFormula()->checkDVFormulaInRange(rg, m_validRefKind, &refKind);
    }

    return S_OK;
}

HRESULT KProtectionContext::checkCFFormula(const RANGE &appliedRg, ICondFmt *condFmt)
{
    if (needCheckFormula())
    {
        IBookStake *pBookStake = m_ibook->GetWoStake();
        uint32_t refKind = 0;
        return pBookStake->getProtectionFormula()->checkCFFormula(appliedRg, condFmt, m_validRefKind, &refKind);
    }

    return S_OK;
}

HRESULT KProtectionContext::checkCFFormulaInRange(const RANGE &rg)
{
    if (needCheckFormula())
    {
        IBookStake *pBookStake = m_ibook->GetWoStake();
        uint32_t refKind = 0;
        return pBookStake->getProtectionFormula()->checkCFFormulaInRange(rg, m_validRefKind, &refKind);
    }

    return S_OK;
}


HRESULT KProtectionContext::checkProtectionCopyContent(const RANGE &rg)
{
    if (!rg.IsValid() || !needCheckFormula())
    {
        return S_OK;
    }

    if (isRangeHasHidden(rg))
    {
        return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
    }

    return S_OK;
}

uint32_t KProtectionContext::getProtectionContentKind(const RANGE &rg, bool isAll)
{
    if (!rg.IsValid() || !isBookHasHiddenProperty())
    {
        return pckNone;
    }

    return wo::getProtectionContentKind(m_pApiWb, rg, isAll, m_ctx);
}

HRESULT KProtectionContext::checkProtectionContent(const RANGE &rg, uint32_t *pckKind)
{
    if (!rg.IsValid() || !isBookHasHiddenProperty())
    {
        return S_OK;
    }

    if (isRangeHasHidden(rg))
    {
        return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;
    }

    uint32_t kind = pckNone;
    HRESULT hr = wo::checkProtectionContentKind(m_ctx, m_pApiWb, rg, true, &kind);
    if (kind != pckNone && pckKind)
    {
        *pckKind = kind;
    }

    return hr;
}

HRESULT KProtectionContext::isValidFormula(IDX iSheet, ROW row, COL col, const_token_vector vecToken)
{
    IBookStake *pBookStake = m_ibook->GetWoStake();
    uint32_t refKind = 0;
    return pBookStake->getProtectionFormula()->checkFormula(iSheet, row, col, vecToken, m_validRefKind, &refKind);
}

HRESULT KProtectionContext::isValidFormula(const RANGE &rg, const_token_vector vecToken)
{
    IBookStake *pBookStake = m_ibook->GetWoStake();
    uint32_t refKind = 0;
    return pBookStake->getProtectionFormula()->checkFormula(rg, vecToken, m_validRefKind, &refKind);
}

void KProtectionContext::setValidRefKind(uint32_t k)
{
    m_validRefKind = k;
}

bool KProtectionContext::isCompileFormula() const
{
    return m_isCompileFormula;
}

void KProtectionContext::setCompileFormula(bool set)
{
    m_isCompileFormula = set;
}

uint32_t KProtectionContext::getPasteContentKind() const
{
    return m_pasteContent;
}

void KProtectionContext::setPasteContentKind(uint32_t kind)
{
    m_pasteContent = kind;
}

bool KProtectionContext::allowDeleteLockedRowCol() const
{
    return m_isAllowDelLockedRowCol;
}

void KProtectionContext::setAllowDeleteLockedRowCol(bool isAllowed)
{
    m_isAllowDelLockedRowCol = isAllowed;
}

int KProtectionContext::currentCommand() const
{
    return m_woCommand;
}

void KProtectionContext::setCurrentCommand(int cmd)
{
    m_woCommand = cmd;
}

bool KProtectionContext::getAutoPassword(PCWSTR srcUUID, ks_wstring *outPwd)
{
	QString uuid = QString::fromUtf16(srcUUID);
	QString connid = QString::fromUtf16(m_ctx->getUser()->connID());
	const char *pwd = nullptr;

	WebInt hr = gs_callback->getAutoPasswd(connid.toUtf8().constData(), uuid.toUtf8().constData(), &pwd);
    if (WO_OK == hr && pwd)
	{
		*outPwd = krt::utf16(QString::fromUtf8(pwd));
		return true;
	}
    else
    {
        WOLOG_INFO << "[protection] getAutoPasswd error: " << hr;
    }

	return false;
}

bool KProtectionContext::isSheetHasProtectedCols(ISheet* pSheet)
{
	return wo::isSheetHasProtectedCols(pSheet);
}

bool KProtectionContext::needCheckInsertDeleteHiddenRange(const RANGE& rg)
{
    return true;
}

void KProtectionContext::onUserColPermsChanged(LPCWSTR userId)
{
    if (!userId)
        return;

    m_userColpermsChanged.insert(userId);
}

void KProtectionContext::updateUserColPermsStatus(LPCWSTR excludeId)
{
    if (m_userColpermsChanged.empty() || m_ctx->isExecDirect())
        return;

    if (excludeId)
        m_userColpermsChanged.erase(excludeId);

    class KEnumUserConn : public IKEnumUserConn
    {
    public:
        KEnumUserConn(std::set<ks_wstring> &userColpermsChanged) : m_userColpermsChanged(userColpermsChanged) {}
        virtual int Do(IKUserConn *pUserConn) override
        {
            ks_castptr<IKETUserConn> conn = pUserConn;
            if (m_userColpermsChanged.find(conn->userID()) != m_userColpermsChanged.end())
            {
                conn->setCorePermissionChanged(true);
            }
            return S_OK;
        }
    private:
        std::set<ks_wstring> &m_userColpermsChanged;
    };
    IKUserConns *conns = m_ctx->getUsers();
    KEnumUserConn euc(m_userColpermsChanged);
    VS(conns->enumUserConn(&euc));

    m_userColpermsChanged.clear();
    m_isAllUserColPermsChanged = false;
}

bool KProtectionContext::isAllUsersColPermsChanged()
{
    return m_isAllUserColPermsChanged;
}

void KProtectionContext::markAllUsersColPermsChanged(LPCWSTR excludeId)
{
    class KEnumUserConn : public IKEnumUserConn
    {
    public:
        KEnumUserConn(std::set<ks_wstring> &userColpermsChanged) : m_userColpermsChanged(userColpermsChanged) {}
        virtual int Do(IKUserConn *pUserConn) override
        {
            ks_castptr<IKETUserConn> conn = pUserConn;
            LPCWSTR userId = conn->userID();
            m_userColpermsChanged.insert(userId);
            return S_OK;
        }
    private:
        std::set<ks_wstring> &m_userColpermsChanged;
    };

	IKUserConns *conns = m_ctx->getUsers();
	KEnumUserConn euc(m_userColpermsChanged);
    VS(conns->enumUserConn(&euc));
    m_userColpermsChanged.erase(excludeId);
    m_isAllUserColPermsChanged = true;
}

bool KProtectionContext::isConnSharedLink()
{
    if (m_hasConnSharedLinkCache)
        return m_isConnSharedLinkCache;

    m_hasConnSharedLinkCache = true;
    IBook *pBook = getBook();
    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    wo::IEtRevisionContext *pRvsCtx = _etcore_GetEtRevisionContext();
    if (!spSharedLinkMgr || !pRvsCtx || !pRvsCtx->getUser())
        return false;

    PCWSTR sharedId = spSharedLinkMgr->GetConnSharedLink(pRvsCtx->getUser());
    if (!sharedId)
        return false;

    ISharedLink* pLink = spSharedLinkMgr->GetItem(sharedId);
    if (pLink)
    {
        m_spSharedLink = pLink;
        m_isConnSharedLinkCache = true;
    }

    return true;
}

bool KProtectionContext::needCheckFormula()
{
    return isBookHasHiddenProperty() || isConnSharedLink();
}

IEtProtectionCtx::ProtectionType KProtectionContext::getPasteProtectionType() const
{
    return m_protectionType;
}

void KProtectionContext::setPasteProtectionType(IEtProtectionCtx::ProtectionType type)
{
    m_protectionType = type;
}

bool KProtectionContext::checkExclusive(const RANGE &rg, ProtectionAccessPerms *right)
{
    IBook *pBook = getBook();
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(rg.SheetFrom(), &spSheet);
    if (!spSheet)
        return true;

    ks_stdptr<ISheetProtection> protection = getSheetProtection(spSheet);
    if  (!protection)
        return true;

    PCWSTR userID = m_ctx->getUser()->userID();
    if (!protection->IsCanExclusiveRange(userID, rg))
    {
        if (right)
            *right = PTAAP_Exclusive;
        return false;
    }

    return true;
}

bool KProtectionContext::isUserRangeInvisible(const RANGE &rg)
{
	IBook *pBook = getBook();
    ks_stdptr<ISheet> spSheet;
    pBook->GetSheet(rg.SheetFrom(), &spSheet);
    if (!spSheet)
        return true;

	ks_stdptr<ISheetProtection> spSheetProtection = getSheetProtection(spSheet);
	if (!spSheetProtection)
		return true;

    if (spSheetProtection->IsProtected() && m_ctx->getUser())
	{
		return spSheetProtection->IsUserRangeInvisible(m_ctx->getUser()->userID(), rg);
	}

    return true;
}

bool KProtectionContext::isCanReadWholeBook()
{
	INT sheetCount = 0;
	getBook()->GetSheetCount(&sheetCount);

	RANGE rg(getBook()->GetBMP());
	rg.SetSheets(0, sheetCount - 1);
	return !isRangeHasHidden(rg);
}

bool KProtectionContext::isCanEditWholeBook()
{
	INT sheetCount = 0;
	getBook()->GetSheetCount(&sheetCount);

	RANGE rg(getBook()->GetBMP());
	rg.SetSheets(0, sheetCount - 1);
	auto accessPerm = PTAAP_None;
	return isAllowEdit(rg, &accessPerm);
}

} // namespace wo