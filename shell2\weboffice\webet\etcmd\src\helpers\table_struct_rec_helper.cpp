#include "table_struct_rec_helper.h"
#include "workbook.h"
#include "et_revision_context_impl.h"

namespace wo
{
namespace TableStructRecHelper
{

static constexpr ROW s_tableHeadRecRowsCnt = 20-1;//只检查前面20行
static constexpr COL s_tableHeadRecColsCnt = 200-1;//只检查前面200列

TableStructRecErrorCode _getRgBorderAndTitleCnt(ISheet* pSheet, const RANGE& rg, OUT COL& contentRgLeftBorder, OUT COL& contentRgRightBorder, int& titleCnt);

PCWSTR ConvertErrCodeToString(TableStructRecErrorCode errCode)
{
	switch (errCode)
	{
		case TableStructRecNull:
			return __X("TableStructRecNull");
		case TableStructRecOverLimit:
			return __X("TableStructRecOverLimit");
		case TableStructRecFailed:
			return __X("TableStructRecFailed");
		case TableStructRecSuccessed:
			return __X("TableStructRecSuccessed");
		default:
			break;
	}
	return __X("");
}

HRESULT GetResult(TableStructRecErrorCode errCode)
{
	switch (errCode)
	{
		case TableStructRecNull:
			return E_KSHEET_TITLE_RECORD_NULL;
		case TableStructRecOverLimit:
			return E_KSHEET_TITLE_RECORD_OVERLIMIT;
		case TableStructRecFailed:
			return E_KSHEET_TITLE_RECORD_EMPTY_CELL;
		case TableStructRecSuccessed:
			return S_OK;
		default:
			break;
	}
	return E_KSHEET_TITLE_UNKNOWN;
}

TableStructRecErrorCode calcTitleCnt(ISheet* pSheet, const RANGE& rg, bool bOnlyContent, COL& contentRgLeftBorder, COL& contentRgRightBorder, int& titleCnt)
{
	int topIndex = rg.RowFrom();
	int bottomIndex = rg.RowTo();
	int leftIndex = rg.ColFrom();
	int rightIndex = rg.ColTo();
	// 情况1,空表;这里肯定是个空表，但是排除不了只设置了样式，而实际没有内容的情况，交由下面的情况3处理。
	if (topIndex < 0 || leftIndex < 0)
		return TableStructRecNull;
	// 情况2,有筛选
	if (!bOnlyContent)
	{
		IKAutoFilter* pAutoFilter = KEtWorkbook::getAutoFilterInRange(pSheet, rg, nullptr);
		if (pAutoFilter)
		{
			RANGE filterRg(pSheet->GetBMP());
			pAutoFilter->GetFilterRange(&filterRg);
			titleCnt = filterRg.RowFrom() + 1;
			contentRgLeftBorder = leftIndex;
			contentRgRightBorder = rightIndex;
			return TableStructRecSuccessed;
		}
	}
	// 情况3：尝试从上到下去拿到第一个满足填满内容的行
	if (topIndex > s_tableHeadRecRowsCnt || leftIndex > s_tableHeadRecColsCnt)
		return TableStructRecOverLimit;

	RANGE rgData(pSheet->GetBMP());
	int rowFrom = std::max(topIndex,0);
	int rowTo = std::min(s_tableHeadRecRowsCnt, bottomIndex);
	if(rowFrom > rowTo)
	{
		WOLOG_INFO << "[calcTitleCnt] rowFrom > rowTo occured!!!";
		return TableStructRecOverLimit;
	}

	int colFrom = std::max(leftIndex,0);
	int colTo = std::min(s_tableHeadRecColsCnt,rightIndex);
	if(colFrom > colTo)
	{
		WOLOG_INFO << "[calcTitleCnt] colFrom > colTo occured!!!";
		return TableStructRecOverLimit;
	}
	int sheetIdx = rg.SheetFrom();
	rgData.SetSheetFromTo(sheetIdx,sheetIdx);
	rgData.SetRowFromTo(rowFrom,rowTo);
	rgData.SetColFromTo(colFrom,colTo);

	return _getRgBorderAndTitleCnt(pSheet, rgData, contentRgLeftBorder, contentRgRightBorder, titleCnt);
}

TableStructRecErrorCode _getRgBorderAndTitleCnt(ISheet* pSheet, const RANGE& rg, OUT COL& contentRgLeftBorder, OUT COL& contentRgRightBorder, int& titleCnt)
{
	struct RowInfo
	{
		int m_validCellNum = 0;
		bool m_bHasEmptyCell = false;
	};
	struct RowInfoMap
	{
		void addRowNotEmptyCellNum(int iRow)
		{
			if(!m_rowInfoMap.count(iRow))
			{
				RowInfo rowInfo;
				rowInfo.m_validCellNum = 1;
				m_rowInfoMap[iRow] = rowInfo;
			}
			else
			{
				m_rowInfoMap[iRow].m_validCellNum++;
			}
		}

		void setRowHasEmptyCell(int iRow)
		{
			if(!m_rowInfoMap.count(iRow))
			{
				RowInfo rowInfo;
				rowInfo.m_bHasEmptyCell = true;
				m_rowInfoMap[iRow] = rowInfo;
			}
			else
			{
				m_rowInfoMap[iRow].m_bHasEmptyCell = true;
			}
		}

		TableStructRecErrorCode getTitleCnt(COL minNotEmptyCellLeftBorder, COL maxNotEmptyCellRightBorder, int& titleCnt) const
		{
			if(m_rowInfoMap.empty())
				return TableStructRecNull;

			int rowNotEmptyCellNum = maxNotEmptyCellRightBorder - minNotEmptyCellLeftBorder + 1;
			if(rowNotEmptyCellNum <= 0)
			{
				WOLOG_INFO << "[calcTitleCnt] rowNotEmptyCellNum <= 0 occured!!!";
				return TableStructRecNull;
			}
			int maxValidCellNum = 0;
			int maxValidCellNumRowCnt = 0;
			for (const auto& it : m_rowInfoMap)
			{
				if((it.second).m_bHasEmptyCell)
					continue;
				if((it.second).m_validCellNum == rowNotEmptyCellNum)
				{
					titleCnt = it.first + 1;
					return TableStructRecSuccessed;
				}
				if ((it.second).m_validCellNum > maxValidCellNum)
				{
					maxValidCellNumRowCnt = it.first + 1;
					maxValidCellNum = (it.second).m_validCellNum;
				}
			}
			if (maxValidCellNum == 0)
				return TableStructRecNull;
			titleCnt = maxValidCellNumRowCnt;
			return TableStructRecFailed;
		}

		std::map<int, RowInfo> m_rowInfoMap;
	};

	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);
	class KCellValueAcpt : public ICellValueAcpt
	{
	public:
		KCellValueAcpt(ISheet* pSheet, int sheetIdx)
			:m_pSheet(pSheet)
			,m_minNotEmptyCellLeftBorder(s_tableHeadRecColsCnt)
			,m_maxNotEmptyCellRightBorder(-1)
			,m_sheetIdx(sheetIdx)
		{
			VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
			VS(m_spStringTools->SetEnv(m_pSheet));
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if(!pToken)
				return 0;//继续枚举

			ks_bstr text;
			m_spStringTools->GetCellText(m_pSheet, row, col, &text, nullptr, -1, nullptr);
			bool bCellTextEmpty = text.empty();

			BOOL bMerge = FALSE;
			m_pSheet->IsMerged(row, col, &bMerge);
			if(bMerge)
			{
				//合并单元格
				RANGE rgCell(m_pSheet->GetBMP());
				rgCell.SetCell(m_sheetIdx, row, col);

				ks_stdptr<IKRanges> ptrRgs;
				m_pSheet->FindEffectMergeCell(rgCell, FALSE, &ptrRgs);
				const RANGE* pMergeRg = NULL;
				ptrRgs->GetItem(0, NULL, &pMergeRg);
				ASSERT(pMergeRg);
				int rowFrom = pMergeRg->RowFrom();
				int rowTo = pMergeRg->RowTo();
				int colFrom = pMergeRg->ColFrom();
				int colTo = pMergeRg->ColTo();

				if(bCellTextEmpty)
				{
					for(int i=rowFrom; i<=rowTo; i++)
						m_rowInfoMap.setRowHasEmptyCell(i);
					return 0;//继续枚举
				}

				if(colTo > m_maxNotEmptyCellRightBorder)
				{
					m_maxNotEmptyCellRightBorder = colTo;
				}
				if(colFrom < m_minNotEmptyCellLeftBorder)
				{
					m_minNotEmptyCellLeftBorder = colFrom;
				}

				bool bHorMerge = (colTo - colFrom >= 1);//合并了多列
				bool bVerMerge = (rowTo - rowFrom >= 1);//合并了多行
				if(bHorMerge && bVerMerge)
				{
					//合并区域,按一个单元格算
					m_rowInfoMap.addRowNotEmptyCellNum(row);
				}
				else if(bHorMerge && !bVerMerge)
				{
					//横向合并，按一个单元格算
					m_rowInfoMap.addRowNotEmptyCellNum(row);
				}
				else
				{
					//纵向合并,看作占了多行
					for(int i=rowFrom; i<=rowTo; i++)
						m_rowInfoMap.addRowNotEmptyCellNum(i);
				}

			}
			else
			{
				//非合并单元格
				if(!bCellTextEmpty)
				{
					if(col > m_maxNotEmptyCellRightBorder)
					{
						m_maxNotEmptyCellRightBorder = col;
					}
					if(col < m_minNotEmptyCellLeftBorder)
					{
						m_minNotEmptyCellLeftBorder = col;
					}

					m_rowInfoMap.addRowNotEmptyCellNum(row);
				}
				else
				{
					m_rowInfoMap.setRowHasEmptyCell(row);
				}
			}
			return 0;//继续枚举
		};

		TableStructRecErrorCode getTitleCnt(int& titleCnt) const
		{
			return m_rowInfoMap.getTitleCnt(m_minNotEmptyCellLeftBorder, m_maxNotEmptyCellRightBorder, titleCnt);
		}

		void getContentRgBorder(OUT COL& contentRgLeftBorder, OUT COL& contentRgRightBorder) const
		{
			contentRgLeftBorder = m_minNotEmptyCellLeftBorder;
			contentRgRightBorder = m_maxNotEmptyCellRightBorder;
		}
	private:
		int m_sheetIdx;
		COL m_minNotEmptyCellLeftBorder;
		COL m_maxNotEmptyCellRightBorder;
		ISheet* m_pSheet;
		RowInfoMap m_rowInfoMap;
		ks_stdptr<IETStringTools> m_spStringTools;
	};
	int sheetIdx = rg.SheetFrom();
	KCellValueAcpt cellValue(pSheet, sheetIdx);
	spSheetEnum->EnumCellValue(rg, &cellValue);

	cellValue.getContentRgBorder(contentRgLeftBorder,contentRgRightBorder);

	return cellValue.getTitleCnt(titleCnt);
}

static bool _checkIsPhone(PCWSTR val)
{
    QString phone = QString::fromUtf16(val);
    QRegExp rxPhone("^1[3-9][0-9]{9}$");

    if (phone.contains(rxPhone))
        return true;
    else
        return false;
}

void getContentBorders(ISheet* pSheet, const RANGE& rg, OUT COL& contentRgLeftBorder, OUT COL& contentRgRightBorder)
{
	int titleCnt = 0;
	_getRgBorderAndTitleCnt(pSheet, rg, contentRgLeftBorder, contentRgRightBorder, titleCnt);
}

HRESULT recPhoneNumCol(ISheet* pSheet, const RANGE& tableHeadRg, OUT std::unordered_set<int>& phoneColIdxs)
{
	static int s_maxCheckPhoneRowsCnt = 10;//检查列类型 最多只从标题行后往下找10行。
	class KCellValueAcpt : public ICellValueAcpt
	{
	public:
		KCellValueAcpt(ISheet* pSheet)
			:m_pSheet(pSheet)
		{
			VS(_etcore_CreateObject(CLSID_KETStringTools, IID_IETStringTools, (void**)&m_spStringTools));
			VS(m_spStringTools->SetEnv(m_pSheet));
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if(!pToken)
				return 0;//继续枚举
			if(m_colIdxs.find(col) == m_colIdxs.end() )
				m_colIdxs[col] = true;

			if(!m_colIdxs[col])
				return false;

			ks_bstr text;
			m_spStringTools->GetCellText(m_pSheet, row, col, &text, nullptr, -1, nullptr);
			bool bCellTextEmpty = text.empty();
			if(bCellTextEmpty)//Todo:为空要不要跳过
				return 0;
			bool bMatchPhoneFormmat = _checkIsPhone(text.c_str());
			if(!bMatchPhoneFormmat)
				m_colIdxs[col] = false;
			return 0;
		}

		ISheet* m_pSheet;
		ks_stdptr<IETStringTools> m_spStringTools;
		std::unordered_map<int,bool> m_colIdxs;//colIdx->bool是否为手机号码列
	};

	RANGE checkRg(pSheet->GetBMP());
	checkRg.SetSheetFromTo(tableHeadRg.SheetFrom(), tableHeadRg.SheetTo());
	int rowTo = std::min(pSheet->GetBottom(), tableHeadRg.RowTo() + s_maxCheckPhoneRowsCnt);
	if(tableHeadRg.RowTo() + 1 > rowTo)
	{
		//不含有数据部分,直接认为识别手机号码失败
		return  E_FAIL;
	}

	checkRg.SetRowFromTo(tableHeadRg.RowTo() + 1, rowTo);
	checkRg.SetColFromTo(tableHeadRg.ColFrom(), tableHeadRg.ColTo());
	if(!checkRg.IsValid())
		return  E_FAIL;

	et_sdptr<ISheetEnum> spSheetEnum;
	pSheet->CreateEnum(&spSheetEnum);

	KCellValueAcpt cellValue(pSheet);
	spSheetEnum->EnumCellValue(checkRg, &cellValue);

	if(cellValue.m_colIdxs.empty())
		return S_OK;
	for(auto it = cellValue.m_colIdxs.begin(); it!=cellValue.m_colIdxs.end(); it++)
	{
		int idx = it->first;
		bool bMatch = it->second;
		if(bMatch)
			phoneColIdxs.insert(idx);
	}

	return S_OK;
}

HRESULT GetTableTitleInfo(KEtWorkbook* wwb, KEtRevisionContext* ctx, IDX sheetIdx, TableTitleInfo& info)
{
	IKWorksheet* pWorksheet = wwb->GetCoreWorkbook()->GetWorksheets()->GetSheetItem(sheetIdx);
	if (!pWorksheet)
		return E_INVALIDARG;
	ISheet* pSheet = pWorksheet->GetSheet();
	if (!pSheet)
		return E_INVALIDARG;
	RANGE rg(pSheet->GetBMP());
	pWorksheet->GetUsedRange(&rg);

	if (ctx->getProtectionCtx()->isRangeHasHidden(rg))
		return E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE;

	COL colFrom = -1, colTo = -1;
	int titleCnt = 0;
	auto errCode = TableStructRecHelper::calcTitleCnt(pSheet, rg, false, colFrom, colTo, titleCnt);

	ROW row = titleCnt - 1;
	if (errCode != TableStructRecHelper::TableStructRecSuccessed && errCode != TableStructRecHelper::TableStructRecFailed)
	{
		row = rg.RowFrom();
		colFrom = rg.ColFrom();
		colTo = rg.ColTo();
	}

	BOOL bMerge = FALSE;
	for (COL col = colFrom; col < colTo; ++col)
	{
		pSheet->IsMerged(row, col, &bMerge);
		if (bMerge)
			break;
	}

	info.errorCode = errCode;
	info.row = row;
	info.colFrom = colFrom;
	info.colTo = colTo;
	info.bMerged = bMerge;

	return S_OK;
}

} // namespace TableStructRecHelper
} // namespace wo
