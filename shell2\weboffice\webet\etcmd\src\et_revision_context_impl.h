﻿#ifndef __ET_WEBET_REVISION_CONTEXT_IMPL_H__
#define __ET_WEBET_REVISION_CONTEXT_IMPL_H__

#include "wo/et_revision_context.h"
#include "unordered_map"
#include "wo/core_stake.h"
#include "vector"
#include "memory"
#include "revision_ext.h"
#include "kfc/service/alg_msr.h"
#include "filter_context_imp.h"

class CellNode;
struct WebMimeData; 

namespace wo
{
interface IBookStake;
interface IWorkbookObj;
struct BlockPoint;
typedef std::vector<int> ShapeIdxPath;
typedef std::vector<int> ShapeCountPath;
class KEtWorkbook;

struct WSTR_HASH
{
	size_t operator()(const ks_wstring& str) const
	{
		return alg::HashWString(str.c_str());
	}
};

struct KCopyShapeCtx
{
	KCopyShapeCtx()
		: bIsCopying(false)
	{
	}

	void ResetCtx()
	{
		bIsCopying = false;
		shapeSha1Maper.clear();
		lastSha1Value.clear();
		mapObjectType.clear();
	}
	bool bIsCopying;
	ks_wstring cloudPathPrefix;
	ks_wstring cloudPathPostfix;
	std::unordered_map<INT, ks_wstring> shapeSha1Maper;
	ks_wstring lastSha1Value;
	std::map<ks_wstring, WebDownloadBasicTag> mapObjectType;
};

struct KPasteTextCtx
{
	KPasteTextCtx()
		: bEnableCellMultiHyperlinks(false)
		, bAutoFixLinkRuns(false)
		, bEnableBom(false)
	{}

	bool bEnableCellMultiHyperlinks;
	bool bAutoFixLinkRuns;
	bool bEnableBom;
};

struct SupBookCacheUpdateInfo
{
	typedef std::vector<COL> ROWDATA;
	typedef std::unordered_map<ROW, ROWDATA> SHEETDATA;

	std::vector<IDX> names;
	std::unordered_map<IDX, SHEETDATA> sheetsData;
};
typedef std::unordered_map<PCWSTR, SupBookCacheUpdateInfo> SupBookCacheUpdateInfos;

// 编译不通过的也认为是公式，非法公式
bool isFormulaStr(IBook* pBook, PCWSTR str);

class KSupBooksCtx : public ISupBooksCtx
{
	struct Value {
		ks_wstring fullName;
		ks_wstring fileId;
	};

	struct sheetNameCmper
	{
		bool operator()(const ks_wstring& rl, const ks_wstring& rr) const 
		{
			return xstricmp(rl.c_str(), rr.c_str()) < 0; 
		}
	};

	struct SheetsInfo
	{
		std::map<ks_wstring, ks_wstring, sheetNameCmper> m_old2new;
		std::vector<ks_wstring>  m_unmatchOldName;
		std::vector<ks_wstring>  m_newName;
	};

public:
	virtual ~KSupBooksCtx();
	virtual void reset(const binary_wo::VarObj& booksCtx) override;
	virtual void getUnmatchSheetInfo(PCWSTR fullBookName, binary_wo::VarObj& infoObj) override;
	virtual PCWSTR findFullName(PCWSTR name) override;
	virtual PCWSTR findFileId(PCWSTR name) override;
	virtual void userAssignSheetsName(PCWSTR fullBookName, ISheetsNameMatcher*) override;

	void insertSupBook(PCWSTR url, PCWSTR fileId) override;

private:
	bool insert(PCWSTR name, PCWSTR fullName, PCWSTR fileId);
	SheetsInfo* getSheetsInfo(PCWSTR fullBookName, bool bGain);
	void collectUnmatchSheets(PCWSTR fullBookName, ISheetsNameMatcher* pMatcher);

private:
	std::unordered_map<alg::MSR_HANDLE, Value> m_booksInfo;
	std::unordered_map<alg::MSR_HANDLE, SheetsInfo> m_book2SheetInfo; 

	std::map<ks_wstring, int32_t> m_downloadedUrlFiles;
	std::map<ks_wstring, int32_t> m_downloadedFilesWithFileid;
};

class KEtPerfStatContext: public IEtPerfStatContext
{
public:
	explicit KEtPerfStatContext(KEtWorkbook* wb);
	virtual ~KEtPerfStatContext();

	void incWriteCellValueCount() override;
	void incWriteCellFormatCount() override;

private:
    KEtPerfStatContext(const KEtPerfStatContext &);
    KEtPerfStatContext &operator=(const KEtPerfStatContext &);

	KEtWorkbook* m_wb = nullptr;
};

class KCellHistoryCtx : public ICellHistoryCtx
{
public:
	KCellHistoryCtx();
	virtual ~KCellHistoryCtx();

	void onCommitCellHsitory() override;
	bool needCleanHistory() override;
	int getReserveCount() override;

	void addCommitCount(std::size_t s)
	{
		m_commitCount += s;
	}

private:
    KCellHistoryCtx(const KCellHistoryCtx &);
    KCellHistoryCtx &operator=(const KCellHistoryCtx &);

	std::size_t m_commitCount = 0;
};

class KProtectionContext;
class KEtRevisionContext: public IEtRevisionContext
{
public:
	KEtRevisionContext(KEtWorkbook* wb);
	virtual ~KEtRevisionContext();

public:
	virtual IKUserConns* getUsers() override;
	virtual IKUserConn*	getUser() override;
	virtual IKUserConn* setUser(IKUserConn* p) override;
	virtual IKUserConn* getUserFromInnerID(UINT32) override;
	virtual IKDocument* getDocument() override;
	virtual void submitModifyShape(drawing::AbstractShape* shape) override;

	virtual void ViewRect2CoreRect(const drawing::AbstractShape* shape, QRectF& rcfView, QRectF& rcfCore) override;

public:
	virtual void getSerializingRects(WebID objSheet, const RECT** ppRects, int* pCount) override;
	virtual IBook* getBook() override;
	virtual IETStringTools* getStringTools() override;
	virtual void addFmla(const CellNode*) override;

public:
	virtual IWorksheetObj* getSheetMain(IDX sheetId) override;
	virtual IDX getSheetIndex(WebID id) override;
	virtual IDX getSheetIndexByGridData(const AbsObject* obj) override;
	virtual IDX getSheetIndexByDvPlayer(const AbsObject* obj) override;

	virtual void resetCalcNodes(const CellNode**, size_t) override;
	virtual IWoLogger*  getLogger() override;
	virtual void setFilterExecRes(int nSourceCount, int nResultCount) override;
	virtual void getFilterExecRes(int& nSourceCount, int& nResultCount) override;

	virtual void setFlashFillRect(const RECT& rc) override;
	virtual void getFlashFillRect(const RECT** ppRects) override;

	virtual PCWSTR getNetDiskName() override;
	virtual SUPBOOK_STAT getNetDiskFile(PCWSTR netDiskName, PCWSTR fileId, 
		ks_wstring& path, ks_wstring& version, ks_wstring& fileName) override;

	virtual void beginIgnoreHistory() override;
	virtual bool isIgnoreHistory() override;
	virtual void endIgnoreHistory() override;

	//一些命令会导致supBookCache更新，需要收集更新的内容并同步到客户端
	virtual bool isSyncSupBookCache() override;
	virtual void addSupBookCache(PCWSTR fileId) override;
	virtual void addSupBookCacheName(PCWSTR fileId, IDX idxName) override;
	virtual void addSupBookCacheCell(PCWSTR fileId, IDX iSheet, ROW r, COL c) override;
	virtual void addSupBookCacheEmptyRow(PCWSTR fileId, IDX iSheet, ROW r) override;

	virtual void markDirtySupBook(PCWSTR fileId) override;
	virtual bool isDirtySupBook(PCWSTR fileId) override;
	virtual void clearDirtySupBook() override;

	virtual void InitCopyShapeCtx(const ks_wstring& prefixPath, const ks_wstring& postfixPath) override;
	virtual bool GenWoCloudImgPath(INT shapeId, ks_wstring& imgPath) override;
	virtual void AddCopyShapeInfo(drawing::AbstractShape* shape, BOOL* pSameAdjacentSha1 = NULL) override;
	virtual void ResetCopyShapeCtx(ISerialAcceptor* acpt) override;
	
	virtual BOOL GenWoImgHtmlUrl(IN IKShape*, OUT BSTR* pUrl) override;

	// 兼容历史命令，记录粘贴txt是否转为了linkruns
	virtual void SetEnableCellMultiLink(bool b);
	virtual bool GetEnableCellMultiLink();
	virtual void SetUTF_8_BOM(bool b);
	virtual bool GetUTF_8_BOM();
	virtual void SetAutoFixLinkRunsFlag(bool b);
	virtual bool GetAutoFixLinkRunsFlag();
	virtual void SetEnablePasteDynamicArray(bool b) override;
	virtual bool GetEnablePasteDynamicArray(bool bPostExecute) override;
	virtual void SetEnablePasteDynamicArrayValue(bool b) override;
	virtual bool GetEnablePasteDynamicArrayValue(bool bPostExecute) override;

	virtual ISupBooksCtx* getSupBookCtx() override;
	PCWSTR getFileId() override;

	virtual void onDbAutomationTriggered(
		WebTriggerAction action,
		long triggerTime,
		const WebSlice *msg,
		UINT sheetId,
		EtDbId recordId,
		EtDbId automationId,
		EtDbId actionId,
		HRESULT hr) override;
	virtual void serialiseDbAutomationTriggeredInfo(ISerialAcceptor *) override;

	IWoFilterContext* getFilterContext() override;
	IEtProtectionCtx* getProtectionCtx() override;

	void addCloudImgAttachmentId(drawing::AbstractShape* pShape) override;
	void addCloudImgAttachmentId(LPCWSTR path) override;
	void addCloudImgAttachmentIdByAnnex(LPCWSTR path) override;
	void getAttachmentAnnexIdBySheet(ISheet* pSheet, std::vector<ks_wstring>& vecIds, std::vector<ks_wstring>& vecIdsVideo) override;
	void addAttachmentAnnexIdBySheet(ISheet* pSheet) override;
    void addCloudImgAttachmentIdDirectly(LPCWSTR attachmentId) override;
	void getAllAttachmentIds(std::vector<ks_wstring>& vecIds, std::vector<ks_wstring>& vecIdsVideo) override;
	void clearAttachmentIds() override;
	
	bool isPivotTableLayoutOnCellContent() override;
	void setPivotTableLayoutOnCellContent(bool enable) override;

	virtual void markVisitingView(UINT sheetId, EtDbId viewId) override;
	virtual void getDbVisitingViews(UINT sheetId, std::unordered_set<EtDbId> *pSet) override;
	virtual EtDbId getUserVisitingView(UINT sheetId, IKUserConn *pUserConn) const override;

	virtual UINT elapsed(UINT mark = 0xfff) override;
	void setNeedCleanChildProc(BOOL b) { m_bNeedCleanChildProc = b; }
	virtual BOOL GetNeedCleanChildProc() override;
	virtual void OnClearAllUndoRedoSteps() override;
	virtual void SetClearCurVersionCmd() override;
	virtual WebProcType getProcType() override;

	virtual HRESULT SetRedirectSharedId(PCWSTR redirectSharedId) override;
	virtual PCWSTR GetRedirectSharedId() override;
	void notifyRenderMsg(
			IRenderView* rdView,
			int viewIdx, 
			int msg, 
			KSO_WParam wParam, 
			KSO_LParam lParam) override;
	virtual void addBeautifyOperator(PCWSTR cmdName, PCWSTR params) override;
	virtual std::vector<std::pair<ks_wstring, ks_wstring>>* getBeautifyOperators() override;
	virtual bool isAuthorityDirty() override { return m_bAuthorityDirty; };
    virtual void setAuthorityDirty() override { m_bAuthorityDirty = true; };

	virtual void recordEventTrack(const char* name, unsigned int ms) override { eventTrackingInfos.push_back({name, ms}); }

	// 记录一下操作 禁止查看序列化时导致数据泄漏
	virtual void markChangeOp(ChangeOpType type) override;
	virtual Database::FieldType getCellDbFieldType(RANGE&) override;
	WebInt initVersionCount() override { return m_initVersionCount; }
	ks_wstring GetDownloadFileUserid();
	IBookCollectInfo * collectInfo() override;
	void setUserRequestInfo(const char* requestInfo);
	const ks_wstring& getUserRequestInfo();
	void addGridExportInfo(int blockCnt, int emptyCellCnt, int solidCellCnt) override;

	void RecordErrorCell(const CELL& cell) override;
	CELL GetRecordErrorCell() override;

public:
	void collectInitBlockPoints(bool bInit, std::vector<BlockPoint>&);
	void collectFmla(std::vector<const CellNode*>&);
	void collectCachedCalc(std::vector<const CellNode*>&);

	void getShapeIdxPathById(IDX iSheet, WebID objId, ShapeIdxPath& idxPath, ShapeCountPath& idxInfo);
	drawing::AbstractShape* getShapeByIdxPath(IDX iSheet, const ShapeIdxPath& idxPath);

	bool getChartShapeIdxPath(drawing::AbstractShape* shapeGp, WebID objId, ShapeIdxPath& idxPath);
	drawing::AbstractShape* getChartRootByIdxPath(IDX iSheet, const ShapeIdxPath& idxPath);
	drawing::AbstractShape* getChartShapeByIdxPath(drawing::AbstractShape* root, const ShapeIdxPath& idxPath);
	void getChartShapeIdxPathById(drawing::AbstractShape* root, WebID objId, ShapeIdxPath& idxPath);

	void onSheetDelete(WebID objId);
	bool getHasFmlaChanged() { return m_bFmlaChange; }
	void setInitRect(WebID sheet, const CELL& leftTop, const CELL& frozenCellLeftTop);
	void setInitBlock(WebID sheet, const binary_wo::VarObj& blocks);
	void setInitBlock(WebID sheet, std::vector<RECT> & blocks);
	void setExecDirect() { m_state = CtxState::ExecDirect; }
	virtual bool isExecDirect() override { return m_state == CtxState::ExecDirect; }
	CtxState state() override { return m_state; }
	void setState(CtxState state) { m_state = state; }

	void resetSyncSupBookCacheCtx(bool bEnableSync);
	const SupBookCacheUpdateInfos& getSupBookCacheUpdateInfos() const;

	ks_stdptr<ISheetProtection> getSheetProtection(ISheet *isheet);
	virtual void collectInfo(const char *collectName, int count = 1) override;
	virtual void collectInfo(const WCHAR *collectName, int count, int count2) override;
	virtual void collectTasksDirectFailedInfo(HRESULT hr, const char *collectName, int count = 1) override;
	virtual WebInt fetchPermissionId(const char *userId, char *permissionIdBuf, int permissionIdBufLen) override;

	virtual void setMessageBoxAnswer(WoMbIdentifier, int) override;
	virtual int getMessageBoxAnswer(WoMbIdentifier) override;
	virtual WoMbIdentifier mostRecentMessageBoxId() override;

	void setAllMutableResult(bool);
	bool getAllMutableResult() const;
	
	binary_wo::BinWriter* getHttpResponse() { return m_upWriter.get(); };
	void postExecute(HRESULT hr);
	HRESULT getExecuteResult() { return m_hr; }
	bool asyncGetNetDiskFileByUrl(PCWSTR url, DownloadFileTask downloadFileTask) override;
	bool asyncGetNetDiskFileByFileid(PCWSTR fileId) override;
	PCWSTR GetTraceId() override { return m_cmdTraceId.c_str(); }

	void SetCmdName(PCWSTR);
	void setCmdTraceId(PCWSTR wsz) { m_cmdTraceId = wsz; }
	const ks_wstring & getCmdTraceId() { return m_cmdTraceId; }
	void setTraceId(const ks_wstring & wstr) { m_traceId = wstr; }
	const ks_wstring & getTraceId() override { return m_traceId; }
	wo::KEtWorkbook * woWorkbook() { return m_pWoWb; }

	class ProtectionCtxExchanger
	{
	public:
		static ProtectionCtxExchanger* makeExchanger(KEtRevisionContext* etRevisionCtx, et_sptr<KProtectionContext>& spProtectionCtx);
		~ProtectionCtxExchanger();
		ProtectionCtxExchanger(const ProtectionCtxExchanger&) = delete;
		ProtectionCtxExchanger& operator=(const ProtectionCtxExchanger&) = delete;
	private:
		ProtectionCtxExchanger(KEtRevisionContext* etRevisionCtx, KProtectionContext* newProtectionCtx);
	private:
		static std::size_t numObjects;
		KEtRevisionContext* m_etRevisionCtx;
		et_sptr<IEtProtectionCtx> m_spOldProtectionCtx;
	};
	std::unique_ptr<ProtectionCtxExchanger> generateExchanger(et_sptr<KProtectionContext>& spProtectionCtx);
	friend class ProtectionCtxExchanger;

	IEtPerfStatContext* getPerfStatCtx() override;
	ICellHistoryCtx* getCellHistoryCtx() override;
	uint64_t getCurCommitVersionTime() override;
	void setCurCommitVersionTime(uint64_t tm)  override;
	bool isUserEditMode() override;
	bool isInRevisionMode() override;

	void SubmitEventTrackingInfo();

	virtual bool isSearchCellImg() override { return m_bSearchCellImg;}
	virtual void SetSearchCellImg(bool bSearchCellImg) override { m_bSearchCellImg = bSearchCellImg;}
	virtual CFHandleStrategy getCFHandleStrategy() override {return m_cfHandleStrategy;}
	virtual void setCFHandleStrategy(CFHandleStrategy cfStrategy) override{ m_cfHandleStrategy = cfStrategy;}
	bool isNewCfStrategyOn() const { return m_cfHandleStrategy != cfHandleStrategyDef; }

	bool isReSerialize() { return m_isReSerialize; }

	bool IsNoCheckPerms() { return m_IsNoCheckPerms; }

	void SetIsNoCheckPerms(bool isNoCheckPerms) {m_IsNoCheckPerms = isNoCheckPerms;}

	virtual bool IsOwner(LPCWSTR userId) override;

	bool IsUserHasSerialObjs();

	void SetExclusiveRangeShtIdx(INT32 nShtIdx) override
	{
		m_nExclusiveRangeShtIdx = nShtIdx;
	}

	INT32 GetExclusiveRangeShtIdx() override
	{
		return m_nExclusiveRangeShtIdx;
	}

	bool SetJSRun(bool val)
    {
        bool old = m_bRunJs;
        m_bRunJs = val;
        return old;
    }

	bool isCurUserCanProtect() override;
	bool isCurUserCanReadAllSheets() override;
	bool isCurUserCanEditAllSheets() override;

protected:
	void validateInitRect();
	static bool getShapeIdxPath(drawing::GroupShape* shape, WebID objId, ShapeIdxPath& idxPath, ShapeCountPath& idxInfo);
	RECT getBlockRect(ROW posRow, COL posCol);

	
	void clearEtContext();
	void setupEtContext();
	void setupProtectionCache(ISheet *spSheet);
	void collectCellHistoryInfo(ISheet *spSheet);

	BOOL genUrlBySha1(drawing::AbstractShape*, OUT BSTR*);
	void genImgHtmlUrl(PCWSTR objectKey, WebDownloadBasicTag tag, ks_wstring& output);

	void collectFuncInfo(const QString& uuid, const std::vector<FunctionRecord>& funcInfos);
	void collectFmlaInfo(const QString& uuid, const std::vector<FormulaRecord>& fmlaInfos);
	void collectInfoPart(binary_wo::BinWriter& binWriter, const char *collectName, int count);

	struct DbAutomationTriggeredInfo
	{
		DbAutomationTriggeredInfo() : triggeredCount(0), hr(S_OK) {}
		int triggeredCount;
		UINT sheetId;
		EtDbId recordId;
		EtDbId automationId;
		HRESULT hr;
	};
	
	struct DynamicArrayCtx
	{
		bool m_bEnablePasteDynamicArray = false;
		bool m_bGotEnablePasteDynamicArray = false;
		bool m_bEnablePasteDynamicArrayValue = false;
		bool m_bGotEnablePasteDynamicArrayValue = false;
	};
protected:
	IBook* m_bk;
	IBookStake* m_bks;
	IKWorkbook* m_wb;
	IWorkbookObj* m_wbo;
	etoldapi::_Application* m_app;
	ks_stdptr<IETStringTools> m_tools;
	std::unordered_set<const CellNode*> m_nodesFmla;
	KEtWorkbook* m_pWoWb;

	WebInt 	m_dataVersion;
	WebInt 	m_taskVersion;
	WebInt	m_reachVersion;
	WebInt	m_initVersionCount;
	IKUserConn* m_user;
	std::unordered_map<WebID, AbsObject*> m_idWeb2Core;
	std::unordered_map<const AbsObject*, WebID> m_gridData2SheetId;
	std::unordered_map<const AbsObject*, WebID> m_rows2SheetId;
	std::unordered_map<const AbsObject*, WebID> m_cols2SheetId;
	std::unordered_map<const AbsObject*, WebID> m_dvs2SheetId;

	std::unordered_map<const AbsObject*, FormatConditions*> m_sheetObj2FConds;

	ks_wstring m_cmdTraceId;
	ks_wstring m_traceId;

	INT32 m_activeSheetIndex;
	WebID m_activeGridCells;
	std::vector<RECT> m_vecInitRects;
	INT32 m_idxWnd;

	int m_nSourceCount;
	int m_nResultCount;

	INT32 m_nExclusiveRangeShtIdx = INVALIDIDX;

	std::vector<const CellNode*> m_nodesCalcCache;
	IWoLogger* m_logger;

	bool m_bIgnoreHistory;
	DynamicArrayCtx m_dynamicArrayCtx;
	KSupBooksCtx m_supBooksCtx;
	bool m_bFmlaChange;

	CtxState m_state;
	RECT m_flashFillRect;

	SupBookCacheUpdateInfos m_supBookCacheUpdateInfos;
	bool m_bSyncSupBookCache;
	std::unordered_set<ks_wstring, WSTR_HASH> m_supBooksStateToSerial;
	KCopyShapeCtx m_copyShapeCtx;
	KPasteTextCtx m_pasteTextCtx;

	std::unordered_set<ks_wstring, WSTR_HASH> m_attachmentIdsToFetch;
	std::unordered_set<ks_wstring, WSTR_HASH> m_attachmentIdsToFetchVideo;

	WoFilterContext m_filterCtx;
	et_sptr<IEtProtectionCtx> m_spProtectionCtx;
	std::map<WoMbIdentifier, int> m_mbAnswer;
	WoMbIdentifier m_mostRecentMbId;
	bool m_isPivotTableLayoutOnCellContent;
	bool m_bAllMutableResult;
	bool m_bAuthorityDirty = false;

	std::unique_ptr<binary_wo::BinWriter> m_upWriter;
	HRESULT m_hr;
	ks_wstring m_cmdName;

	DbAutomationTriggeredInfo m_automationTriggerInfo;
	KEtPerfStatContext m_perfStatContext;
	uint64_t m_curCommitVersionTime = 0;
	KCellHistoryCtx m_cellHistoryCtx;
	bool m_bSearchCellImg;
	CFHandleStrategy  m_cfHandleStrategy;

	std::chrono::steady_clock::time_point m_beginClock;
	UINT m_elapsed;
	UINT m_getElapsedCnt;
	BOOL m_bNeedCleanChildProc;
	PCWSTR m_redirectSharedId;
	std::vector<std::pair<ks_wstring, ks_wstring>> m_vecBeautifyOperators;

	// 序列化的过程中，是否改了单元格内容
	bool m_serializeChangeContent;
	// 序列化的时候，是否需要重新查询
	bool m_isReSerialize;

	// 设置导出过程不校验某一些权限
	bool m_IsNoCheckPerms;

	struct EventTrackingInfo
	{
		const char* name;
		unsigned int ms;
	};
	std::vector<EventTrackingInfo> eventTrackingInfos;
    bool m_bRunJs = false;
	ks_wstring m_userRequestInfo;
	CELL m_errCell;

};

}//namespace wo

#endif //__ET_WEBET_REVISION_CONTEXT_IMPL_H__
