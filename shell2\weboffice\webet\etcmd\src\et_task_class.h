﻿#ifndef __WEBET_ET_TASK_CLASS_H__
#define __WEBET_ET_TASK_CLASS_H__
#include <public_header/revision/src/kwrevisionctrl.h>
#include <public_header/webcommon/src/processondata.h>
#include "appcore/et_appcore_dbsheet.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "database/database.h"
#include "database/database_field_context.h"
#include "et_task_executor.h"

namespace chart
{
	class KCTChart;
	class KCTAxis;
	class KCTTextProperty;
	class KCTDataLabel;
	class KCTDataLabels;
	class KCTSeries;
	class KCTSeriesCollection;
	class KCTTrendline;
	class KCTCoreChart;
	class KCTLegend;
}
interface IDBProtectionJudgement;
interface IFilterRowContainter;
interface IEncodeDecoder;
class CF_ApiWrapper;
namespace per_imp{ class ImportFileParam; }
namespace wo
{
using  binary_wo::VarObj;
class KEtWorkbook;
class KEtRevisionContext;
class EtTaskPeripheral;
struct WebMimeHelper;
struct EtSheetsShapeOps;
struct EtShapeAtomOps;
struct EtSheetsChartShapeOps;
struct EtChartShapeAtomOps;
class IBatchTask;

class EtTaskExecBase
{
protected:
	EtTaskExecBase(wo::KEtWorkbook*);
public:
	virtual ~EtTaskExecBase();

public:
	virtual HRESULT PreExecute(KwCommand*, KEtRevisionContext*);
	virtual HRESULT PostExecute(HRESULT, KwCommand*, KEtRevisionContext*);

	virtual HRESULT operator()(KwCommand*, KEtRevisionContext*) = 0;
	virtual PCWSTR GetTag() = 0;
	virtual void AddDErrorStrDefault(HRESULT hr);
	virtual void AddCallbackId(KwCommand* cmd);
	virtual bool SerialCommand(KwCommand* cmd, ISerialAcceptor*);
	EtTaskPeripheral* GetPeripheral();
	virtual IBatchTask * GetBatchTask() { return nullptr; }
	virtual int GetTagNumber() const;
	virtual int GetShapeOpChangeType() const;
	

protected:
	RANGE ReadRange(binary_wo::VarObj var);
	void ReadRange(binary_wo::VarObj var, etoldapi::Range** pprg);
	void ReadRanges(binary_wo::VarObj var, std::vector<RANGE>& vec, WebName name);
	IDX GetSheetIdx(const binary_wo::VarObj& var);
	IDX GetSheetIdx(const binary_wo::VarObj& var, WebName stIdName);
	void WriteRect(binary_wo::VarObj& var, const RECT& rc);
	void WriteRect(binary_wo::VarObj& var, const CELL& cell);
	void WriteCell(binary_wo::VarObj& var, const CELL& cell);
	void WriteRanges(binary_wo::VarObj& var, std::vector<RANGE>& rgVec);
	static bool DecodeInt_s(const binary_wo::VarObj& vNumRoot, WebName numFieldName, int& val);
	drawing::ShapeVisual* GetShapeVisual(binary_wo::VarObj param, KEtRevisionContext*);
	void GetApiShape(binary_wo::VarObj param, KEtRevisionContext* pCtx, KsoShape** ppShape);
	void GetApiShape(IKShape* pShape, KsoShape** ppShape);
	chart::KCTShape* GetChartShape(IKShape* pShape);
	HRESULT getCellComments(const RANGE& rg,bool bCreate, ICellComments** ppCmt);
	HRESULT getWoComment(const RANGE& rg, bool bCreate, IWoComment** ppCmt);
	void AddErrorStr(PCWSTR cmdName, HRESULT hr, PCWSTR forceErr = nullptr);
	HRESULT SetCellFormula(binary_wo::VarObj param, const RANGE& rg, PCWSTR str, KEtRevisionContext* pCtx);
	void AddExtErrorValue(uint32_t errorVal);
	void AddSheetIdxToRes(IDX idxSheet);
	bool GetAnyCell(ISheet* pSheet, const RANGE& rg, CELL* pPos);
	HRESULT InsertRange(const RANGE& rg, ETInsertShiftDirection opt);
	//插入区域前会尝试删除被挤出表外的区域中有只有格式的单元格，isReport 为true时将任一个非空单元格的位置返回给前端， 为 false 时不需要ctx
	HRESULT InsertRangeEx(const RANGE& rg, ETInsertShiftDirection opt, KEtRevisionContext* pCtx,bool formatFromRightOrBelow=false);
	bool isFormulaStr(PCWSTR str);
	void SetNameRg(IBookOp* pBookOp, IDX iName, const RANGE& rg);
	void addFilterExtraField(KEtRevisionContext*, binary_wo::VarObj& param);
	bool isCellImgListSheet(IDX iSheet);
	HRESULT paste(IETPersist* spPersist, IKDrawingCanvas* spDrawCanvas, IKDrawingCanvas* spCmtDrawCanvas, PASTEINFO info,
		QMimeData* pMineDate, const RANGE& rg, CELL& cell, const RANGE** ppSlect, KEtRevisionContext* ctx, bool& bPasteRealSkipHidden,
		const bool bEnablePasteSkipHidden);
	bool getMimeDataByClipboardId(WebMimeHelper& wmh, QMimeData& mineData, QString& srcFileId);
	bool getMimeData(WebMimeHelper& wmh, const QString& priorityFormat, QMimeData& mineData, QString& srcFileId);
	IDX getLastSheetIdx(); //最后一个是CellImgListSheet时要忽略
	void clipByEmptyRow(ISheet* pSheet, const RANGE& srcRg, RANGE& destRg, KEtRevisionContext* ctx, binary_wo::VarObj& param);
	void ViewRect2CoreRect(drawing::ShapeVisual* pShapeVisual, QRectF& view, QRectF& core);
	void CoreRect2ViewRect(drawing::ShapeVisual* pShapeVisual, QRectF& core, QRectF& view);
	void visualCalcDirtyRegion(IKWorksheet*);
	RANGE getActiveCellByOffset(const RANGE& rg, const binary_wo::VarObj& param);
	ks_stdptr<ISheet> getSheetFromParam(const binary_wo::VarObj& param, const char *key = nullptr);
	HRESULT TransConditionFormat(FormatConditions* pConds, binary_wo::VarObj& rule, CF_ApiWrapper** ppWrapper, IKCoreObject** ppCore, long& idxCFRule);

	// 在命令执行前调用，用于检测是否有权限执行(如判断保护工作表）
	// 命令执行时需要验证该命令执行的所有区域是否有权限
	// note: 调用内核命令时最好使用最外层的接口（api 级别），最好调用 Range 的接口（大部分权限检查都是这里检查）
	virtual HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) = 0;
	virtual bool IsCompileFormulaAsText(KEtRevisionContext* ctx);

	virtual void CommitShapeMainOp(KwCommand* cmd, EtSheetsShapeOps* pShapeAtoms);
	virtual void CommitChartShapeMainOp(KwCommand* cmd, EtSheetsChartShapeOps* pChartShapeAtoms);

	HRESULT CheckProtectionRange(const RANGE &rg, KEtRevisionContext* ctx);
	HRESULT CheckProtectionRanges(range_helper::ranges &rgs, KEtRevisionContext* ctx);
	HRESULT CheckProtectionRange(KwCommand* cmd, KEtRevisionContext* ctx);
	HRESULT CheckProtectionRanges(KwCommand* cmd, KEtRevisionContext* ctx);
	HRESULT CheckProtectionContent(const RANGE &rg, KEtRevisionContext* ctx);
	HRESULT CheckBookHasHiddenPropRange(KEtRevisionContext* ctx, bool asFlmaRes = false);
	HRESULT CheckObjectProtection(IDX idxSheet,  KEtRevisionContext* ctx);
	HRESULT CheckProtectionIsMaster(IDX idxSheet,  KEtRevisionContext* ctx);
	HRESULT CheckUserCanChangeProtection(KEtRevisionContext* ctx, bool bIgnoreOwner = true);
	HRESULT CheckSheetPermission(UINT sheetId, KEtRevisionContext* ctx);
	ks_stdptr<ISheetProtection> getSheetProtection(IDX idxSheet);
	PCWSTR convertWoMbIdentifier(WoMbIdentifier);
	bool makeMessageBoxResult(KEtRevisionContext*, binary_wo::VarObj);
	void updateProtectionInfoRetryAndAbort(KEtRevisionContext* ctx, const char* connID, bool isProtected);
	void setFileTagByPicAsAttachments(bool isAttachments);
	void setFileTagByHasDashBoard(IBook* pBook, bool hasDashboard);

	HRESULT CheckCmdPermissionWhenUseNewPlan(const RANGE &rg);
	HRESULT CheckCanMergeWhenUseNewPlan(const RANGE &rg);
	HRESULT CheckCanMergeWhenUseNewPlan(const std::vector<RANGE> &rgs);

	void unmergeRange(const RANGE&rg);
	void mergeRange(const RANGE&rg);	
	void moveContentCell(const RANGE& rg,
						const RANGE&adjustRg,
						ETInsertShiftDirection opt );
	
	void NotifyHistoryCellPosChange(KEtRevisionContext* pCtx);

	HRESULT removeExclusiveUserRange(KEtRevisionContext* pCtx, const RANGE& rg);
	HRESULT removeExclusiveUserRanges(KEtRevisionContext* pCtx, const std::vector<RANGE>& rgs);
	HRESULT addExclusiveUserRange(KEtRevisionContext* pCtx, const RANGE& rg);
	HRESULT addExclusiveUserRanges(KEtRevisionContext* pCtx, const std::vector<RANGE>& rgs);
	BOOL isCanExclusiveUserRange(KEtRevisionContext* pCtx, const RANGE& rg);
	//这个删除接口会删除区域里的所有独占权限
	HRESULT removeAllExclusiveUserRanges(KEtRevisionContext* pCtx, const std::vector<RANGE>& rgs);
	HRESULT checkExclusiveRangesVaild(ExclusiveRangeMode mode, const std::vector<RANGE>& rgs);
	HRESULT confirmExclusiveRanges(ISheetProtection* pSheetProtection, PCWSTR userId, const std::vector<RANGE>& rgs);
	//获取填充区目标区域比源区域新增的部分
	void getNewFillRange(const RANGE& srcRg, const RANGE& dstRg, RANGE& resRg);

	void preExecEtSyncData(KEtRevisionContext* pCtx, const binary_wo::VarObj& cmdVarObj);
	void postExecEtSyncData(KEtRevisionContext* pCtx, KwCommand* cmd, HRESULT hr);
	void adjustEtSyncData(KEtRevisionContext* pCtx);

private:
	void UnmergeEmpty(IBookOp* pBookOp, ISheet* pSheet, const RANGE& rgRm, CELL& cellWithContent, RANGE& rgSelection);
	void prepareCmdCtx(KwCommand*, KEtRevisionContext*);

protected:
	wo::KEtWorkbook* m_wwb;
	static EtTaskExecutor::CmdNameSet  m_cmdPermissionCheckWhiteList; //命令不检查权限，服务端内部调用确保不越权
	std::unique_ptr<EtTaskPeripheral> m_peri;
	ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
	bool m_bConfirmExceed = false;	// 独占区域超出限制二次确认
	bool m_bConfirmSeize = false;	// 创建者抢夺独占区域二次确认
};

class ETDbSheetTaskClassBase : public EtTaskExecBase
{
public:
	ETDbSheetTaskClassBase(KEtWorkbook*);
	virtual HRESULT PreExecute(KwCommand*, KEtRevisionContext*) override;
	virtual HRESULT PostExecute(HRESULT, KwCommand*, KEtRevisionContext*) override;

public:
	void GetDBSheetView(UINT sheetStId, EtDbId viewId, IDBSheetView** ppIDbSheetView, IDBSheetViews** ppIDbSheetViews);
	void GetDBSheetViews(UINT sheetStId, IDBSheetViews** ppIDbSheetViews);
	void GetDBPos(binary_wo::VarObj pos, DbIdPostion& tar);
	HRESULT GetDBRange(IDBSheetView* pView, binary_wo::VarObj vRg, 
		bool extendRecordsViewAllToGridAll, bool extendFieldsViewAllToGridAll, IDBSheetRange** ppRg);
	HRESULT writeDbRange(IDBSheetView* pView, IDBSheetRange* pDbRg, VarObj rgObj);

	HRESULT attemptModifyLink2Multiple(IDBSheetView* pView, VarObj param, KEtRevisionContext* pCtx);
	HRESULT prepareMessageBoxIfSingleLink(IDBSheetView* pView, EtDbId recId, EtDbId fldId, VarObj param, 
		HRESULT hr, DbReversedLinkParam& dbLinkParam);
	HRESULT setRecsValWrap(IDBSheetView* pView, IDBSheetRange* dbRg, VarObj objRecoresValues, bool ignoreFailed);
	HRESULT checkFormRecord(IDBSheetView* pView, VarObj objRecoresValues);
	HRESULT createRecordCopyWrap(IDBSheetView* pView, IDBSheetRange* pDbRg, EtDbId srcRecId, VarObj param);

	void AddEtDbId(binary_wo::VarObj& obj, WebName name, EtDbId id);
	void AddEtDbIdItem(binary_wo::VarObj& objArr, EtDbId id);

	EtDbId GetEtDbId(const binary_wo::VarObj& obj, WebName name);
	EtDbId GetEtDbId(binary_wo::VarObj obj);

	HRESULT CheckProtectionSheetRange(IDBSheetRange*, UINT sheetStId);
	static HRESULT GetAllParentRecords(IDBSheetOp* pSheetOp, IDBSheetRange*recordRg, std::vector<EtDbId>& parentRecords);
	static HRESULT GetAllRecordByParentRecords(IDBSheetView* pView, const EtDbId* pIds, UINT32 recCnt, IDBSheetRange**recordRg, bool getVisibleChildren = false);
protected:
	HRESULT updateAll();
	HRESULT getUserGroup(EtDbId userGroupId, IDBUserGroup** ppGroup);
	HRESULT setGetViewCurModifyTar(VarObj param);
	bool GetDBParentId(binary_wo::VarObj pos, EtDbId& parentId);
	HRESULT AddSelectItems(const binary_wo::VarObj& param, IDBSheetOp* pSheetOp);
	IDBSheetCtx* m_pDbCtx;
	IEncodeDecoder* m_pEncodeDecoder;
	DBSheetCommonHelper m_commonHelper;
	DbSheetViewUpdateFlag m_dbViewUpdateFlags;
};

class EtMultiRangeTaskExecBase : public EtTaskExecBase
{
public:
	EtMultiRangeTaskExecBase(wo::KEtWorkbook*);
protected:
	void ReadRanges(binary_wo::VarObj var, std::vector<RANGE> &);
};

// 兼容【range.xxx和multiRange.xxx】从param中读取单选区和多选区
class EtRangesTaskExecBase : public EtTaskExecBase
{
public:
	EtRangesTaskExecBase(wo::KEtWorkbook*);
protected:
	void ReadRanges(binary_wo::VarObj param, std::vector<RANGE> &);
	binary_wo::VarObj GenSelectRangesVarObj(binary_wo::VarObj parent, const std::vector<RANGE> &);
};

class TaskExecSetCellFormula: public EtTaskExecBase
{
public:
	TaskExecSetCellFormula(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::SetFormula 和 Exec 执行时判断权限
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecSetDuplicateRemind : public EtTaskExecBase
{
public:
	TaskExecSetDuplicateRemind(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // Exec 执行时判断权限, 前端应该没使用这个命令了
	}
};

class TaskExecRangeInsert: public EtTaskExecBase
{
public:
	TaskExecRangeInsert(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::InnerInsert 判断权限
	}
};

class TaskExecRangeInsertNumberCol: public EtTaskExecBase
{
public:
    TaskExecRangeInsertNumberCol(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
};

class TaskExecRangeMergeCellsInsert: public EtTaskExecBase
{
public:
	TaskExecRangeMergeCellsInsert(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd,KEtRevisionContext*)override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd,KEtRevisionContext* pCtx) override
	{
		return S_OK;
	}
private:
	bool _handleRowCellExpand(KEtRevisionContext* pCtx,bool isInsertBefore,const RANGE& insertRange);
	bool _handleColCellExpand(KEtRevisionContext* pCtx,bool isInsertBefore,const RANGE& insertRange);
};

class TaskExecRangeUnMergeInsert: public EtTaskExecBase
{
public:
	TaskExecRangeUnMergeInsert(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd,KEtRevisionContext*) override;
	PCWSTR GetTag()	override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd,KEtRevisionContext* pCtx) override
	{
		return S_OK;
	}
private:
	void _setCrossRangeFormat(const RANGE&rg,ETInsertShiftDirection opt,bool isInsertBefore);
	static RANGE _adjustColRange(const RANGE&insertRg,bool isInsertBefore,const RANGE& effectRg);
	static RANGE _adjustRowRange(const RANGE&insertRg,bool isInsertBefore,const RANGE& effectRg);
};


// 选区删除的基类
class TaskExecRangeBaseDelete: public EtTaskExecBase
{
public:
	TaskExecRangeBaseDelete(KEtWorkbook*);

protected:
	IDX sheetFrom;
	IDX sheetTo;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::Delete 判断权限
	}

	HRESULT setupProtectionParam(const binary_wo::VarObj &param, KEtRevisionContext* ctx);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;

	ks_stdptr<Range> createRange(const std::vector<RANGE> &rgVec);
	virtual std::vector<RANGE> initAndCheckData(const binary_wo::VarObj &param) = 0;
	bool isRtCells(const std::vector<RANGE> &rgVec);
	void initSheetFromTo(const RANGE &rg);
};

class TaskExecRangeDelete: public TaskExecRangeBaseDelete
{
public:
	TaskExecRangeDelete(KEtWorkbook*);
	PCWSTR GetTag() override;

protected:
	std::vector<RANGE> initAndCheckData(const binary_wo::VarObj &param) override;
};

class TaskExecMultiRangeDelete: public TaskExecRangeBaseDelete
{
public:
	TaskExecMultiRangeDelete(KEtWorkbook*);
	PCWSTR GetTag() override;

protected:
	std::vector<RANGE> initAndCheckData(const binary_wo::VarObj &param) override;
};

class TaskExecRangesMergeBase : public EtRangesTaskExecBase
{
public:
	TaskExecRangesMergeBase(wo::KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		std::vector<RANGE> rgVec;
		ReadRanges(param, rgVec);
		if (rgVec.empty()) return S_OK;
		if(CheckCanMergeWhenUseNewPlan(rgVec) != S_OK)
			return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;

		return S_OK;
	}
};

class TaskExecRangeMerge: public TaskExecRangesMergeBase
{
public:
	TaskExecRangeMerge(KEtWorkbook*);
	PCWSTR GetTag() override;
};

class TaskExecMultiRangeMerge: public TaskExecRangesMergeBase
{
public:
	TaskExecMultiRangeMerge(KEtWorkbook*);
	PCWSTR GetTag() override;
};

class TaskExecRangeMergeCells : public EtTaskExecBase
{
public:
	TaskExecRangeMergeCells(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		RANGE rg = ReadRange(param);
		if (!rg.IsValid()) return S_OK;
		if(CheckCanMergeWhenUseNewPlan(rg) != S_OK)
			return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;

		return S_OK; // 旧逻辑在 KRange::QueryCanMerge 判断权限
	}
};

class TaskExecRangeIntelliMerge : public EtTaskExecBase
{
public:
	TaskExecRangeIntelliMerge(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		RANGE rg = ReadRange(param);
		if (!rg.IsValid()) return S_OK;
		if(CheckCanMergeWhenUseNewPlan(rg) != S_OK)
			return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;

		return S_OK; // 旧逻辑在 KRange::QueryCanMerge 判断权限
	}
};

class TaskExecRangeUnMerge: public EtTaskExecBase
{
public:
	TaskExecRangeUnMerge(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		RANGE rg = ReadRange(param);
		if (!rg.IsValid()) return S_OK;
		if(CheckCanMergeWhenUseNewPlan(rg) != S_OK)
			return E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET;

		return S_OK; // 旧逻辑在 KRange::QueryCanMerge 判断权限
	}
};

class TaskExecRangeSetRowHeight: public EtTaskExecBase
{
public:
	TaskExecRangeSetRowHeight(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecRangeSetColumnWidth: public EtTaskExecBase
{
public:
	TaskExecRangeSetColumnWidth(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;  // 不需要判断
	}
};


class TaskExecRangeUpdate: public EtTaskExecBase
{
public:
	TaskExecRangeUpdate(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; 
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};


class TaskExecRangeSetFormula: public EtTaskExecBase
{
public:
	TaskExecRangeSetFormula(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::SetFormula 和 Exec 执行时判断权限
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
	struct CollectInfoParam
	{
		int cbCount = 0;
		int cbLength = 0;
		int stCount = 0;
		int stLength = 0;
		int total = 0;
		int layer = 0;
		std::unordered_map<int, int> funcIdMap;
	};
private:
	BSTR delFrontBackSpace(LPCWSTR lpwsz);
private:
	typedef std::vector<std::pair<UINT, FONT> > VecRuns;
	void _DecodeRuns(const binary_wo::VarObj& varRuns, VecRuns& vecRuns);
	HRESULT _SetRuns(const WCHAR* pcwcs, const VecRuns& vecRuns, Range* pRange);
	HRESULT _PutFont(UINT uBegin, UINT cch, Range* pRange, const FONT* pFont);
	PCWSTR _GetRealName(const FONT* pFont);
	HRESULT _UNDERLINESTYLE_ETUnderlineStyle(UNDERLINESTYLE ls, ETUnderlineStyle& etls);
	static REF_STYLE refStyleStringToEnum(PCWSTR);
	void collectFormulaInfo(IBook *pBook, const RANGE& rg, PCWSTR fmla, KEtRevisionContext* pCtx);
	void collectIfErrorFunInfo(exec_token_vector& etv, KEtRevisionContext* pCtx);
	bool _CheckIsCellImageInsertScript(IBook *, const RANGE&, const binary_wo::VarObj&);
	void getFuncInfo(exec_token_vector& etv, CollectInfoParam& param, IBookOp* pOp);
	HRESULT additionalAiFormula(const RANGE& rg, IBook* pBook, KEtRevisionContext* pCtx);
};

class TaskExecRangeSetXf: public EtTaskExecBase
{
public:
	TaskExecRangeSetXf(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::SetXF 判断权限
	}
};

class TaskExecMultiRangeRemoveExclusiveRange : public EtMultiRangeTaskExecBase
{
public:
	TaskExecMultiRangeRemoveExclusiveRange(KEtWorkbook* wwb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class TaskExecMultiRangeSetXf : public EtMultiRangeTaskExecBase
{
public:
	TaskExecMultiRangeSetXf(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::SetXF 判断权限
	}
};

class TaskExecRangeClearContents: public EtTaskExecBase
{
public:
	TaskExecRangeClearContents(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::ClearContents 判断权限
	}
};

class TaskExecRangeClearFormats: public EtTaskExecBase
{
public:
	TaskExecRangeClearFormats(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::ClearFormats 判断权限
	}
};

class TaskExecMultiRangeClearFormats : public EtMultiRangeTaskExecBase
{
public:
	TaskExecMultiRangeClearFormats(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::ClearFormats 判断权限
	}
};

class TaskExecRangeClear: public EtTaskExecBase
{
public:
	TaskExecRangeClear(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::Clear 判断权限
	}
};

class TaskExecRangeAutoFill: public EtTaskExecBase
{
public:
	TaskExecRangeAutoFill(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT flashFill(KwCommand* cmd, binary_wo::VarObj& param, KEtRevisionContext* pCtx);
	static RANGE ReadRange4Check(IBook*, KwCommand*);
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx); // KAppCoreRange::AutoFill KRange::FlashFill

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecRangeFill: public EtTaskExecBase
{
public:
	TaskExecRangeFill(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override; // KAppCoreRange::FillDirection

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecRangeDblClickFill : public EtTaskExecBase
{
public:
	TaskExecRangeDblClickFill(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override; // KDblClkFillHandle::AutoFill
};

class SameFilterConditionEnum :public IKAutoFilterEnum
{
public:
	SameFilterConditionEnum(ISheet*pSht,PCWSTR coreUserId):
		m_pSht(pSht),
		m_coreUserId(coreUserId){}	
	virtual BOOL Do(PCWSTR, IKAutoFilter* af) override;
	void notifySameFilterConditionChanged();
		
private:
		PCWSTR m_coreUserId;
		ISheet* m_pSht;
};
class TaskExecRangeAutoFilter: public EtTaskExecBase
{
public:
	TaskExecRangeAutoFilter(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}

private:
	HRESULT ExecPivotFilter(KwCommand* cmd, KEtRevisionContext* pCtx);
	HRESULT addAutoFilterResult4Coop(VarObj& param, ISheet* pSht, const RANGE& rg, IFilterRowContainter* pFilterRowCol, bool bIsShared);
	BOOL checkHasSameFilterWithCore(ISheet* pSht,const RANGE& rg,PCWSTR currentId ,PCWSTR coreUserId);
	void notifySameFilterConditionChanged(PCWSTR coreUserId,ISheet* pSht);
	bool HasAutofilter(const RANGE &, PCWSTR);
};

class TaskExecRangeSetAutoFilter: public EtTaskExecBase
{
	enum SetingType
	{
		SetingType_onoff,
		SetingType_showAll,
		SetingType_reApply,
	};
public:
	TaskExecRangeSetAutoFilter(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	SetingType GetSetingType(KwCommand* cmd);
	HRESULT ExecOnOff(VarObj& param, KEtRevisionContext* ctx);
	HRESULT _ExecShowAll(VarObj& param, KEtRevisionContext* ctx, AutoFilter **ppAutoFilter, pivot_core::IPivotTable **ppPivotTable);
	HRESULT ExecShowAll(VarObj& param,  KEtRevisionContext* ctx);
	HRESULT ExecReApply(VarObj& param, KEtRevisionContext* ctx);
    HRESULT ExecSwitchFilterBottomFollowUsedRange(VarObj& param, KEtRevisionContext* ctx);
	HRESULT addSheetFilterToParam(AutoFilter* pAutoFilter, VarObj& param, KEtRevisionContext* ctx);

	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KRange::CheckAutoFilterState 时判断权限
	}
};


class TaskExecRangePastText: public EtTaskExecBase
{
public:
	TaskExecRangePastText(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 exec 时判断权限
	}
};

class TaskExecRangeAutoFit : public EtTaskExecBase
{
public:
	TaskExecRangeAutoFit(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangeReplace : public EtTaskExecBase
{
public:
	TaskExecRangeReplace(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	void WriteResult(etoldapi::Range* pResult);
	void WriteRangeObj(etoldapi::Range* pResult);
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		binary_wo::VarObj param = cmd->cast().get("param");
		IDX idxSheet = GetSheetIdx(param);
		ks_stdptr<ISheetProtection> spSheetProtection = getSheetProtection(idxSheet);
		appcore_helper::KDisabledCheckExclusive disableCheck(spSheetProtection);
		return CheckProtectionRange(cmd, ctx);  // KAppCoreRange::Replace
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}

private:
  HRESULT InnerReplace(etoldapi::Range *pRange, const VarObj &param, CELL cellAfter, BOOL bAutoExpand,
                       int &nReplaceCnt, KEtRevisionContext *ctx);
  void getSingleCellInSheet(etoldapi::_Worksheet *pWorksheet, etoldapi::Range **ppRange);
};

class TaskExecRangesSetHiddenBase : public EtRangesTaskExecBase
{
public:
	TaskExecRangesSetHiddenBase(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class TaskExecRangeSetHidden : public TaskExecRangesSetHiddenBase
{
public:
	TaskExecRangeSetHidden(KEtWorkbook* wb);
	PCWSTR GetTag() override;
};

class TaskExecMultiRangeSetHidden : public TaskExecRangesSetHiddenBase
{
public:
	TaskExecMultiRangeSetHidden(KEtWorkbook* wb);
	PCWSTR GetTag() override;
};

class TaskExecSheetSetStandardWidth : public EtTaskExecBase
{
public:
	TaskExecSheetSetStandardWidth(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecSheetSetStandardHeight : public EtTaskExecBase
{
public:
	TaskExecSheetSetStandardHeight(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecSheetSetVisible : public EtTaskExecBase
{
public:
	TaskExecSheetSetVisible(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecSheetAddHyperlink : public EtTaskExecBase
{
public:
	TaskExecSheetAddHyperlink(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	static RANGE RangeReader(IBook*, KwCommand*);
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override; // KETHyperlinks::Add
};

class TaskExecSheetDeleteHyperlink : public EtTaskExecBase
{
public:
	TaskExecSheetDeleteHyperlink(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override; // KETHyperlink::Delete()
};

class TaskExecSheetReapplyFilter : public EtTaskExecBase
{
public:
	TaskExecSheetReapplyFilter(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 exec 判断权限
	}

private:
	HRESULT ReapplyAll(PCWSTR connID, ISheet* pSheet, KEtRevisionContext* ctx);
};

class TaskExecSheetEditHyperlink : public EtTaskExecBase
{
public:
	TaskExecSheetEditHyperlink(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecSheetsAdd : public EtTaskExecBase
{
public:
	explicit TaskExecSheetsAdd(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	int GetShapeOpChangeType() const override;
private:
	HRESULT initEtDbSheet(const VarObj&, etoldapi::_Worksheet*, KEtRevisionContext*);
	HRESULT setRowColPadding(ISheet*, INT, INT, INT, INT);
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecAddSmartAnalysisChart : public EtTaskExecBase
{
  public:
    explicit TaskExecAddSmartAnalysisChart(KEtWorkbook *);
    HRESULT operator()(KwCommand *, KEtRevisionContext *) override;
    PCWSTR GetTag() override;

  protected:
    HRESULT CheckCmdPermission(KwCommand *, KEtRevisionContext *) override;
};

class TaskExecSheetDataProof : public EtTaskExecBase
{
  public:
    explicit TaskExecSheetDataProof(KEtWorkbook *);
    HRESULT operator()(KwCommand *, KEtRevisionContext *) override;
    PCWSTR GetTag() override;

  protected:
    HRESULT CheckCmdPermission(KwCommand *, KEtRevisionContext *) override;
};

class TaskExecSheetBeautify : public EtTaskExecBase
{
  public:
    explicit TaskExecSheetBeautify(KEtWorkbook *);
    HRESULT operator()(KwCommand *, KEtRevisionContext *) override;
    PCWSTR GetTag() override;

  protected:
    HRESULT CheckCmdPermission(KwCommand *, KEtRevisionContext *) override;

  private:
	int GetEffectType(binary_wo::VarObj& param);
	QVariantMap GetFormatConfig(binary_wo::VarObj& param);

  private:
	std::map<QString, int> m_effectTypeMap;
};

class TaskExecSheetBeautifyAIFormat : public TaskExecSheetBeautify
{
  public:
    explicit TaskExecSheetBeautifyAIFormat(KEtWorkbook *);
    HRESULT operator()(KwCommand *, KEtRevisionContext *) override;
    PCWSTR GetTag() override;
};

class TaskExecImportWorkbookInEt : public EtTaskExecBase
{
public:
    TaskExecImportWorkbookInEt(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;

protected:
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
};

class TaskExecBookSetCustomStorage : public EtTaskExecBase
{
public:
    TaskExecBookSetCustomStorage(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;

protected:
    HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
    {
        return S_OK;
    }
private:
	HRESULT HandleBookInfo(const binary_wo::VarObj& param, ks_stdptr<ICustomStorageManager>& spCustomStorMgr);
	HRESULT HandleSheetInfo(const binary_wo::VarObj& param, ks_stdptr<ICustomStorageManager>& spCustomStorMgr);
};

class TaskExecSheetsMove : public EtTaskExecBase
{
public:
	TaskExecSheetsMove(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecSheetsCopy : public EtTaskExecBase
{
public:
	TaskExecSheetsCopy(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	int GetTagNumber() const override;
	int GetShapeOpChangeType() const override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecSheetsAddTemplate : public ETDbSheetTaskClassBase
{
public:
	TaskExecSheetsAddTemplate(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecBlockUploadAllPictureToAttachment : public EtTaskExecBase
{
  public:
    TaskExecBlockUploadAllPictureToAttachment(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
    PCWSTR GetTag() override;

  private:
  	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // Exec 执行时判断权限, 前端应该没使用这个命令了
	}
};

class TaskExecInsertPictureAttachment : public EtTaskExecBase
{
public:
	TaskExecInsertPictureAttachment(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
protected:
	HRESULT _doInsertPicture(KwCommand* cmd, KEtRevisionContext* ctx, IKShape** prop);
	HRESULT _realDoInsertPicture(KwCommand* cmd, KEtRevisionContext* ctx, IKShape** props, double xPos, double yPos, binary_wo::VarObj& param, IDX iSheetIDX);

};

class TaskExecInsertPictureAttachments: public TaskExecInsertPictureAttachment
{
public:
	TaskExecInsertPictureAttachments(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}

};

class TaskExecInsertPicture: public EtTaskExecBase
{
public:
	TaskExecInsertPicture(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
protected:
	HRESULT _doInsertPicture(KwCommand* cmd, KEtRevisionContext* ctx, Shape** prop);
	HRESULT _realDoInsertPicture(KwCommand* cmd, KEtRevisionContext* ctx, Shape** props, double xPos, double yPos, binary_wo::VarObj& param, IDX iSheetIDX);

};

class TaskExecInsertPictures: public TaskExecInsertPicture
{
public:
	TaskExecInsertPictures(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};


class TaskExecChangePicture : public EtTaskExecBase
{
public:
	TaskExecChangePicture(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChangePictureAttachment : public EtTaskExecBase
{
public:
	TaskExecChangePictureAttachment(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};


class TaskExecInsertProcessOn : public TaskExecInsertPicture
{
public:
	TaskExecInsertProcessOn(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecModifyProcessOn : public EtTaskExecBase
{
public:
	TaskExecModifyProcessOn(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}

private:
	HRESULT GetFill(drawing::AbstractShape* shape, VarObj picture, WebMimeHelper& wmh, drawing::Fill *outFill);
	HRESULT ModifyShapePicture(drawing::AbstractShape* shape, drawing::Fill &fill);
};

class TaskExecSetShapeAnchor : public EtTaskExecBase
{
public:
	TaskExecSetShapeAnchor(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecSetShapeSize : public EtTaskExecBase
{
public:
	TaskExecSetShapeSize(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecSetShapePos : public EtTaskExecBase
{
public:
	TaskExecSetShapePos(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecDeleteShape : public EtTaskExecBase
{
public:
	TaskExecDeleteShape(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	int GetShapeOpChangeType() const override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecRangeInsertCommentItem : public EtTaskExecBase
{
public:
	TaskExecRangeInsertCommentItem(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	static HRESULT getHyperlinkRuns(const VarObj& param, IHyperlinkRuns** runs);
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangDeleteCommentItem : public EtTaskExecBase
{
public:
	TaskExecRangDeleteCommentItem(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangEditCommentItem : public EtTaskExecBase
{
public:
	TaskExecRangEditCommentItem(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangSetCommentProp : public EtTaskExecBase
{
public:
	TaskExecRangSetCommentProp(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangDeleteComment : public EtTaskExecBase
{
public:
	TaskExecRangDeleteComment(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangeSetCommentChainResolved : public EtTaskExecBase
{
public:
	TaskExecRangeSetCommentChainResolved(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangeDeleteCommentChain : public EtTaskExecBase
{
public:
	TaskExecRangeDeleteCommentChain(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangPaste : public EtTaskExecBase
{
public:
	TaskExecRangPaste(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	bool SerialCommand(KwCommand* cmd, ISerialAcceptor* acpt) override;
	ListObjHelp::ExtendOppty::Value  extendLstObjBeforePaste(IKRanges* ptrRgsDst, CELL &activeCell);

protected:
	HRESULT CheckCopyPasteRange(bool isCut, const RANGE &src, const RANGE &dest, KEtRevisionContext* ctx);
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限和检查复制内容, coredataacceptor 判断粘贴内容
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
private:
	HRESULT _execCommand(KwCommand* cmd, KEtRevisionContext*);
	//普通的复制(包含单选区，从外部粘贴等情况)
	HRESULT _execNormalCommand(KwCommand* cmd, KEtRevisionContext*);
	//多复制选区源的操作
	HRESULT _execMultiCopyRgsCommand(KwCommand* cmd, KEtRevisionContext*);

};

class TaskExecImportCsvFile: public EtTaskExecBase
{
public:
	TaskExecImportCsvFile(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		if (param.has("insertType"))
		{
			QString insertType = QString::fromUtf16(param.field_str("insertType"));
			if (insertType == "insertToNewSheet") return S_OK;
		}
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}

private:
	TaskExecSheetsAdd m_sheetsAdd;

private:
	ROW nextRowOfBiggestCommentRow(ks_stdptr<IBook>& pBook, ks_stdptr<ISheet> pSheet) const;
	HRESULT relocateToAppendRange(RANGE&) const;
	HRESULT handleDifferentInsertType(KwCommand* cmd, KEtRevisionContext*, RANGE& destRg);

	HRESULT Import(
		IETPersist* spPersist,
		const QString& fileName,
		const RANGE& dest, 
		KEtRevisionContext* ctx,
		const per_imp::ImportFileParam* param,
		const RANGE** ppSlect,
		bool& loseInfoDuringImpCell);
    HRESULT ClearMerge(ISheet* pSheet, const RANGE& rg);
    HRESULT ClearPivotTable(ISheet* pSheet, const RANGE& rg);
    HRESULT AdjustColumnWidth(const RANGE& rg);
};

class TaskExecShapePaste : public EtTaskExecBase
{
public:
	TaskExecShapePaste(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	bool SerialCommand(KwCommand* cmd, ISerialAcceptor* acpt) override;
	int GetShapeOpChangeType() const override;

	HRESULT Paste(WebMimeHelper&, KEtRevisionContext* pCtx, EtTaskPeripheral* peripheral, QMimeData* pMimeData, QString& srcFileId);
protected:
	HRESULT tryGetMimeData(WebMimeHelper& wmh, KEtRevisionContext* ctx, IETPersist* pPersist, bool& bFromMimeDataID, KsoMimeData& mimeData);
	bool tryGetSelShapesRect(KEtRevisionContext* pCtx, const VarObj& param, RECT& rc);
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
private:
	void GetIKShape(IDX sheetIdx, VarObj objIdxPath, KEtRevisionContext* pCtx, IKShape** ppShape);
	void GetApiShapeRange(IKDrawingCanvas* pCanvas, IKShapeRange* pShapeRange, KEtRevisionContext* pCtx, KsoShapeRange** ppShapeRange);
	HRESULT getMimeDataFromLocal(VarObj& param, IETPersist* pPersist, KEtRevisionContext* ctx, KsoMimeData* pKsoMimeData);

};

class TaskExecRangFmtPaintPaste : public EtTaskExecBase
{
public:
	TaskExecRangFmtPaintPaste(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限和检查复制内容, coredataacceptor 判断粘贴内容
	}
};

class TaskExecRangPasteSpecail : public EtTaskExecBase
{
public:
	TaskExecRangPasteSpecail(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限和检查复制内容, coredataacceptor 判断粘贴内容
	}
};

class TaskExecRangPasteSpecailByHtml : public EtTaskExecBase
{
public:
	TaskExecRangPasteSpecailByHtml(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限和检查复制内容, coredataacceptor 判断粘贴内容
	}
};

class TaskExecRangPasteSmart : public EtTaskExecBase
{
public:
	TaskExecRangPasteSmart(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限和检查复制内容, coredataacceptor 判断粘贴内容
	}
};

class TaskExecRangPasteSpreadSheetSmart : public EtTaskExecBase
{
public:
	TaskExecRangPasteSpreadSheetSmart(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限和检查复制内容, coredataacceptor 判断粘贴内容
	}
};

class TaskExecSheetSetName : public EtTaskExecBase
{
public:
	TaskExecSheetSetName(KEtWorkbook* wb);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecSheetDel : public EtTaskExecBase
{
public:
	TaskExecSheetDel(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	int GetShapeOpChangeType() const override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangeSort : public EtTaskExecBase
{
public:
	TaskExecRangeSort(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 exec 和 KSort::IsAllowSort 判断权限
	}
private:
	bool GetEffectAutoFilters(ISheet* pSheet, const RANGE& rgSortArea);
	BOOL GetFilterSort(_Worksheet* pSheet, const RANGE& rgSortArea, etoldapi::Sort** ppSort);
	HRESULT ExecPivotSort(KwCommand* cmd, KEtRevisionContext* pCtx);
};

class TaskExecRangeGainLinkName : public EtTaskExecBase
{
public:
	TaskExecRangeGainLinkName(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}

private:
	HRESULT findExist(IBookOp *pBookOp, const RANGE& rgRef, int& maxId, ks_wstring& name);
};

class TaskExecPutFreezePanes : public EtTaskExecBase
{
public:
	explicit TaskExecPutFreezePanes(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecFreezeFirstVisibleRowOrCol : public EtTaskExecBase
{
public:
	explicit TaskExecFreezeFirstVisibleRowOrCol(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecToggleGroup : public EtTaskExecBase
{
public:
	TaskExecToggleGroup(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;

private:
	bool hasGroup(IRowColOp *pRowColOp);
};

class TaskExecSetCollapseByGroup : public EtTaskExecBase
{
public:
	TaskExecSetCollapseByGroup(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecSetCollapseByLevel : public EtTaskExecBase
{
public:
	TaskExecSetCollapseByLevel(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

namespace table_style
{
	struct Request
	{
		explicit Request(BMP_PTR const bmp)
			: style_index(0), num_tile_rows(0), style_option(0), range(bmp), style_type(TST_NONE_STYLE) {}

		UINT style_index;
		BYTE num_tile_rows;
		DWORD style_option;
		RANGE range;
		DWORD style_type;
	};
}

class TaskExecSetTableStyle : public EtTaskExecBase
{
public:
	explicit TaskExecSetTableStyle(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}

private:
	HRESULT setListObjectStyle(const table_style::Request &);
	HRESULT setPivotTableStyle(const table_style::Request &);
	bool checkPivotTableIntersection(pivot_core::IPivotTable*, const RANGE &);
};

class TaskExecBatchSetTableStyle : public TaskExecSetTableStyle
{
public:
	explicit TaskExecBatchSetTableStyle(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRanges(cmd, ctx);
	}
};

class TaskExecSetDataValidation : public EtTaskExecBase
{
public:
	TaskExecSetDataValidation(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
    PCWSTR CreateValidationExt(binary_wo::VarObj obj);
    HRESULT checkDV(const RANGE& rg, VALIDATION& dv);
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecNewCFRule : public EtTaskExecBase
{
public:
	TaskExecNewCFRule(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecDelCFRule : public EtTaskExecBase
{
public:
	TaskExecDelCFRule(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecRangeClearAllCFRules : public EtTaskExecBase
{
public:
	TaskExecRangeClearAllCFRules(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}

	HRESULT ExecBy(IDX idxSheet, etoldapi::Range* pRange);
};


class TaskExecClearAllCFRules : public TaskExecRangeClearAllCFRules
{
public:
	TaskExecClearAllCFRules(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecEditCFRule : public EtTaskExecBase
{
public:
	TaskExecEditCFRule(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};
class TaskExecRangeRepairError : public EtTaskExecBase
{
public:
	TaskExecRangeRepairError(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
	EC_RuleType getRuleType(PCWSTR);
};


class TaskExecSwapCFRule : public EtTaskExecBase
{
public:
	TaskExecSwapCFRule(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecSetDuplicateValueRemind : public EtTaskExecBase
{
public:
	TaskExecSetDuplicateValueRemind(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
	{
		// 改成和用户和区域相关，在exec中判断
		return S_OK;
	}
};

class TaskExecClearDuplicateValueRemind : public EtTaskExecBase
{
public:
	TaskExecClearDuplicateValueRemind(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
	{
		// 改成和用户和区域相关，在exec中判断
		return S_OK;
	}
};

class TaskExecRemoveDuplicates : public EtTaskExecBase
{
public:
	TaskExecRemoveDuplicates(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
	{
		// 改成和用户和区域相关，在exec中判断
		return S_OK;
	}

private:
	void colListToVarient(VarObj& obj, VARIANT& colList);
};

class TaskExecAddChart : public EtRangesTaskExecBase
{
public:
	TaskExecAddChart(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;

private:
	HRESULT _AddChart(KwCommand* cmd, int32* nShapeID, KEtRevisionContext*);
	HRESULT _SetDashboardChart(IKWorksheet*, etoldapi::Shape*);
	void expandRange(IKRanges*, etoldapi::_Worksheet*);
};

class TaskExecChartSetDataSource : public EtTaskExecBase
{
public:
	TaskExecChartSetDataSource(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecPivotTableAddField : public EtTaskExecBase
{
public:
	TaskExecPivotTableAddField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecPivotTableRemoveField : public EtTaskExecBase
{
public:
	TaskExecPivotTableRemoveField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecChartSetElement : public EtTaskExecBase
{
public:
	TaskExecChartSetElement(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartApplyLayout : public EtTaskExecBase
{
public:
	TaskExecChartApplyLayout(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetChartColor: public EtTaskExecBase
{
public:
	TaskExecChartSetChartColor(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetChartType : public EtTaskExecBase
{
public:
	TaskExecChartSetChartType(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetChartStyle : public EtTaskExecBase
{
public:
	TaskExecChartSetChartStyle(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetChartTitle : public EtTaskExecBase
{
public:
	TaskExecChartSetChartTitle(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetAxisTitle : public EtTaskExecBase
{
public:
	TaskExecChartSetAxisTitle(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetPlotBy : public EtTaskExecBase
{
public:
	TaskExecChartSetPlotBy(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecSetTheme : public EtTaskExecBase
{
public:
	TaskExecSetTheme(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecFileMerge : public EtTaskExecBase
{
	class HasCellValueAcpt : public ICellValueAcpt
	{
	public:
		HasCellValueAcpt()
			: m_row(-1)
			, m_col(-1)
		{
		}

		STDIMP_(INT) Do(ROW row, COL col, const_token_ptr pToken)
		{
			if (pToken)
			{
				DWORD typeToken = alg::GetExecTokenMajorType(pToken);
				if(typeToken == alg::ETP_VSTR)
				{
					alg::const_vstr_token_assist strToken(pToken);
					if(strToken.get_length() <= 0)
						return 0;
				}
				m_row = row;
				m_col = col;
				return 1;//停止枚举
			}
			return 0;//继续枚举
		};

		ROW m_row;
		COL m_col;
	};

	struct sourceDataInfo
	{
		ROW start;
		int cnt;
		ks_wstring srcSheetName;
		ks_wstring srcBookName;
 	};

	struct STR_HASH
	{
		size_t operator()(const ks_wstring& str) const
		{
			return alg::HashWString(str.c_str());
		}
	};

	using wstrMap = std::unordered_map<ks_wstring, ks_wstring, STR_HASH>;
	using wstrSet = std::unordered_set<ks_wstring, STR_HASH>;

public:
	TaskExecFileMerge(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
private:
	void responseMergeFileList(std::vector<SMergeFileInfo>& listMergeFile);
	HRESULT CopyBookData(KEtRevisionContext* ctx, _Workbook* ptrWorkbookDst, _Worksheet* spSheetDst, _Workbook* ptrWorkbookSrc, bool&, bool, VarObj&, ks_wstring, ks_wstring&);
	void CopyColWidth(ISheet* spSheetSrc, ISheet* spSheetDst);
	HRESULT ClearDstSheet(_Worksheet* spSheet);
	HRESULT CreateDstSheet(Worksheets* spSheetsDst, bool bNewFile, ks_stdptr<etoldapi::_Worksheet> &spSheetDst, const WCHAR* namePrefix);
	HRESULT RemoveDuplicates(_Worksheet* spSheet);
	HRESULT FreezeFirstRow(_Worksheet* spSheet);
	HRESULT SetFilter(VarObj& param, _Worksheet* spSheet, KEtRevisionContext* ctx);
	INT32 getCopyRange(etoldapi::_Worksheet* spSheetSrc, bool bSkipEmptyRow ,  etoldapi::Range** ppRgSrc);

	INT64 GetCurTime();
	HRESULT AddDataSrcFields(_Worksheet* spSheet);
	HRESULT SetDataSrcColTitle(_Worksheet* spSheet, RANGE rg);
	void InitVariabelState();
	HRESULT CheckSecurityDoc(_Workbook*);
	void DelUseLessSheet(_Workbook* ptrWorkbookDst);
	void AddResSelfInfo(ks_wstring bookName, ks_wstring sheetName);
	bool CheckVersion(_Worksheet* spSheet, VarObj& attr);

	bool _needBradCastStateInfo(const VarObj& param, WebID& objSheet);
	void BroadCastStartInfo(const VarObj& param);
	void BroadCastEndInfo(const VarObj& param, _Worksheet* pSheet);

private:
	int m_nCurRow;
	bool m_isMarkSrcData;
	bool m_autoRefresh;
	bool m_isRemoveDuplicates;
	bool m_hasCopyTitle;
	UINT32 m_titleRowCnt;
	std::vector<sourceDataInfo> m_srcDataInfo;
	int m_newSheetIdx;
	binary_wo::VarObj* m_pParam;
};

class TaskExecCopyFromBook : public EtTaskExecBase
{
public:
	TaskExecCopyFromBook(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecSetPixelator : public EtTaskExecBase
{
public:
	TaskExecSetPixelator(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class TaskExecText2Col : public EtTaskExecBase
{
public:
	TaskExecText2Col(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		// 在 text2col_helper.cpp text2colProc  中判断单元格权限
		HRESULT hr = CheckBookHasHiddenPropRange(ctx);
		if (hr != S_OK)
		{
			return hr;
		}

		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckProtectionIsMaster(iSheet, ctx);
	}
};

class TaskExecRangeSlice : public EtTaskExecBase
{
public:
	explicit TaskExecRangeSlice(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;//不需要判断
	}
};

class TaskExecProofreadModifyText : public EtTaskExecBase
{
public:
	explicit TaskExecProofreadModifyText(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;//在内部判断
	}

};

class TaskExecPixelate : public EtTaskExecBase
{
public:
	TaskExecPixelate(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class TaskExecAutoSum : public EtTaskExecBase
{
public:
	TaskExecAutoSum(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	void convertFuncNameToID(WebStr, BOOL, FUNCTION_ID&, ET_Auto_Calculate_Type&, QString &);
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecInsertCellPicture : public EtTaskExecBase
{
public:
	TaskExecInsertCellPicture(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class TaskExecInsertCellPictureAttachment : public EtTaskExecBase
{
public:
	TaskExecInsertCellPictureAttachment(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class TaskExecInsertCellPictureUrl : public EtTaskExecBase
{
public:
	TaskExecInsertCellPictureUrl(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class FloatPic2CellPicProblemAcceptor;
class TaskExecFloatPic2CellPic : public EtTaskExecBase
{
public:
	TaskExecFloatPic2CellPic(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx); // 在 Exec 中判断单元格编辑权限
	}
private:
	void SerialRes(HRESULT hr, const CELL& cellSelect, const FloatPic2CellPicProblemAcceptor& problems, KEtRevisionContext* pCtx, IKWorksheet* pWorksheet);
};

class TaskExecCellPic2FloatPic : public EtTaskExecBase
{
public:
	TaskExecCellPic2FloatPic(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);  // 在 Range.put_Formular (clear) 中判断单元格编辑权限
	}

	void serializeAffectedCells(const std::list<std::pair<ROW, COL>>& affectedCells, VarObj param);
};

class TaskExecInsertBase : public EtTaskExecBase
{
public:
	TaskExecInsertBase(KEtWorkbook*);
	BOOL CanInsert(RANGE rgSrc, RANGE rgDst, DIR dir, bool isCut);
	BOOL CheckSlidValid(INT32 nInsertFrom, INT32 nInsertTo, INT32 nPasteFrom, INT32 nPasteTo, INT32 nLimit);
	void UpdateSelection(RANGE *pRgSrc, RANGE *pRgDst, KEtRevisionContext* pCtx);
};

class TaskExecInsertCopied : public TaskExecInsertBase
{
public:
	TaskExecInsertCopied(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限和检查复制内容, coredataacceptor 判断粘贴内容
	}
};

class TaskExecInsertCut : public TaskExecInsertBase
{
public:
	TaskExecInsertCut(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限和检查复制内容, coredataacceptor 判断粘贴内容
	}
};

class TaskExecRangeTextToNumber : public EtTaskExecBase
{
public:
	TaskExecRangeTextToNumber(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class RowRangeHelper;
class TaskExecBookSetFilterShared : public EtTaskExecBase
{
public:
	TaskExecBookSetFilterShared(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}

	struct SameFilterNotifyEnum :public IKAutoFilterEnum
	{
		virtual BOOL Do(PCWSTR, IKAutoFilter* af) override;
	};

private:
	void copyFilterDataToDef(PCWSTR filterID);
	void setFilterRowHiddenToSheet(ISheet* pSheet, IKAutoFilter* pFilter, IFilterRowContainter* pFilterRowCol);
	void addFilterCondition(PCWSTR filterID, VarObj& param);
	void collectFilterCondition(ISheet * pSht, IKAutoFilter * pFilter, VarObj & structObj, RowRangeHelper* pRgHelper);
	void notifyFilterConditionChangeToSame();
};

class TaskExecBookSetOptions : public EtTaskExecBase
{
public:
	TaskExecBookSetOptions(KEtWorkbook*);
		HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecBookConvertEtToAs : public EtTaskExecBase
{
public:
	TaskExecBookConvertEtToAs(KEtWorkbook*);
	HRESULT	 operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
private:
	inline void callBackToBackend(HRESULT result);
	void doConvert();
	void hanldeProtectedSheet(); 
};

class TaskExecSetRefStyle : public EtTaskExecBase
{
public:
	TaskExecSetRefStyle(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx, true);
	}
};

class TaskExecSetExclusiveRangeMode : public EtTaskExecBase
{
public:
	TaskExecSetExclusiveRangeMode(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
};

class TaskExecSetSheetProtection : public EtTaskExecBase
{
public:
	TaskExecSetSheetProtection(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}

private:
	HRESULT protect(_Worksheet* pWs, VarObj param, KEtRevisionContext* ctx);
	HRESULT unProtect(_Worksheet* pWs, VarObj param, KEtRevisionContext*);
	int getAllowEditRangeUserCount(ISheetProtection* pSheetProtection);
};

class TaskExecSetProtectionOptions : public EtTaskExecBase
{
public:
	TaskExecSetProtectionOptions(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
private:
	HRESULT changePassword(IKWorksheet* pWorksheet, KEtRevisionContext* ctx, VarObj& options);
};

class TaskExecModifySheetProtection : public EtTaskExecBase
{
public:
	TaskExecModifySheetProtection(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT modify(_Worksheet* pWs, VarObj param, KEtRevisionContext* ctx);
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
};

class TaskExecSetAllowEditRange : public EtTaskExecBase
{
public:
	TaskExecSetAllowEditRange(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
private:
	HRESULT add(ISheetProtection* pProtection, const ks_wstring& creator, VarObj param);
	void getArrUserData(VarObj param, std::vector<AllowEditRangeUserData> &arrUserData);
	HRESULT remove(ISheetProtection* pProtection, VarObj& param);
	HRESULT modify(ISheetProtection* pProtection, VarObj& param);
	void getRanges(VarObj param, IKRanges**);
};

class TaskExecRemovePartRangeProtection : public EtTaskExecBase
{
public:
	TaskExecRemovePartRangeProtection(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
};

class TaskExecSheetSetInterline : public EtTaskExecBase
{
public:
	TaskExecSheetSetInterline(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecSupBooksChangeSource : public EtTaskExecBase
{
public:
	TaskExecSupBooksChangeSource(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx, true);
	}
};

class TaskExecSupBooksUpdateLinks : public EtTaskExecBase
{
public:
	TaskExecSupBooksUpdateLinks(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 允许输入公式
	}

	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}
};

class TaskExecSetTabColor : public EtTaskExecBase
{
public:
	TaskExecSetTabColor(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRemoveCustomNF : public EtTaskExecBase
{
public:
	TaskExecRemoveCustomNF(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecBookRecalculate : public EtTaskExecBase
{
public:
	TaskExecBookRecalculate(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	bool SerialCommand(KwCommand* cmd, ISerialAcceptor*) override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecInterruptEAF : public EtTaskExecBase
{
public:
	TaskExecInterruptEAF(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class TaskExecRecalculateEAF : public EtTaskExecBase
{
public:
	TaskExecRecalculateEAF(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class TaskExecSheetSetWndInfo : public EtTaskExecBase
{
public:
	TaskExecSheetSetWndInfo(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecNameBase : public EtTaskExecBase
{
public:
	TaskExecNameBase(KEtWorkbook*);
protected:
	HRESULT getApiName(IDX sheetIdx, WebStr name, etoldapi::Name **ppName);
	HRESULT editName(VarObj param, IDX sheetIdx);
	HRESULT deleteName(VarObj param, IDX sheetIdx);
};

class TaskExecBookAddName : public TaskExecNameBase
{
public:
	TaskExecBookAddName(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override;
private:
	bool isValidNewName(WebStr);
};

class TaskExecBookEditName : public TaskExecNameBase
{
public:
	TaskExecBookEditName(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override;
};

class TaskExecSheetEditName : public TaskExecNameBase
{
public:
	TaskExecSheetEditName(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx, true);
	}
};

class TaskExecBookDeleteName : public TaskExecNameBase
{
public:
	TaskExecBookDeleteName(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override;
};

class TaskExecSheetDeleteName : public TaskExecNameBase
{
public:
	TaskExecSheetDeleteName(KEtWorkbook*);
		HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx, true);
	}
};

class TaskExecChartElementBase: public EtTaskExecBase
{
public:
	TaskExecChartElementBase(KEtWorkbook*);

protected:
	chart::KCTShape* GetChartShape(VarObj param, KEtRevisionContext* pCtx, chart::KCTChart** = nullptr);
	void FillChartShapeParam(VarObj& param, chart::KCTChart*, chart::KCTShape*, KEtRevisionContext*);

	struct AxisInfo {chart::KCTAxis* axis; bool bPrimary; bool bHorizon; };
	std::vector<AxisInfo> CollectAxis(chart::KCTChart*);

	HRESULT CreateAxis(chart::KCTAxis * axis, oldapi::KsoAxis ** ppAxis);
	HRESULT CreateDataLabel(chart::KCTDataLabel * dl, oldapi::KsoDataLabel ** ppDl);
	HRESULT CreateDataLabels(chart::KCTDataLabels * dls, oldapi::KsoDataLabels ** ppDls);
	HRESULT CreateFillFormat(chart::KCTShape * kct, oldapi::KsoFillFormat ** ppApi);
	HRESULT CreateFillFormat(chart::KCTShape * kct, bool isMark, bool isIdx, size_t idx, oldapi::KsoFillFormat ** ppApi);
	HRESULT CreateLineFormat(chart::KCTShape * kct, oldapi::KsoLineFormat ** ppApi);
	HRESULT CreateLineFormat(chart::KCTShape * kct, bool isMark, bool isIdx, size_t idx, oldapi::KsoLineFormat ** ppApi);
	HRESULT CreateSeries(chart::KCTSeries * kct, oldapi::KsoSeries ** ppApi);
	HRESULT CreateDataPoint(chart::KCTSeries * kct, size_t idx, oldapi::KsoDataPoint ** ppApi);
	HRESULT CreateSeriesCollection(chart::KCTShape * kct, chart::KCTSeriesCollection* pctSeriesColl,
	 								oldapi::KsoSeriesCollection** ppSeriesColl);
	HRESULT CreateTrendLine(chart::KCTTrendline * kct, oldapi::KsoTrendline ** ppApi);
	HRESULT CreateChartGroup(chart::KCTCoreChart * kct, oldapi::KsoChartGroup ** ppApi);
	HRESULT CreateLegend(chart::KCTLegend * kct, oldapi::KsoLegend ** ppApi);
};

class TaskExecChartMove : public TaskExecChartElementBase
{
public:
	TaskExecChartMove(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartReisze : public TaskExecChartElementBase
{
public:
	TaskExecChartReisze(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetText : public TaskExecChartElementBase
{
public:
	TaskExecChartSetText(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartOpBase : public TaskExecChartElementBase
{
public:
	TaskExecChartOpBase(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;

protected:
	virtual HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx)  { return S_OK; }
	virtual HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx)  { return S_OK; }
	virtual HRESULT AfterOp(HRESULT opHr) { return opHr; }

	HRESULT ReadFillType(const binary_wo::VarObj & fill, int & type);
	HRESULT ReadSolidFillColor(const binary_wo::VarObj & fill, drawing::Color & clr);

	HRESULT CreateSeriesOrDpt(const binary_wo::VarObj & param, chart::KCTShape* shape, oldapi::KsoSeries ** ppSerApi, oldapi::KsoDataPoint ** ppDptApi);

	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetTextProperty : public TaskExecChartOpBase
{
public:
	TaskExecChartSetTextProperty(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;

	PCWSTR GetTag() override;
private:
	void setFontProp(chart::KCTTextProperty * textProp, const FONT & font, const KXFMASK & mask);
};

class TaskExecChartSetAxisOption : public TaskExecChartOpBase
{
public:
	TaskExecChartSetAxisOption(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;

	PCWSTR GetTag() override;

private:
	HRESULT SetAxisOption(binary_wo::VarObj varOpt, oldapi::KsoAxis * apiAxis);
};

class TaskExecChartSetNumFmt : public TaskExecChartOpBase
{
public:
	TaskExecChartSetNumFmt(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
};

class TaskExecChartSetFill : public TaskExecChartOpBase
{
public:
	TaskExecChartSetFill(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoFillFormat> m_spFillFmt;
};

class TaskExecChartSetOutline : public TaskExecChartOpBase
{
public:
	TaskExecChartSetOutline(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoLineFormat> m_spLineFmt;
};

class TaskExecChartSetMarkerOption : public TaskExecChartOpBase
{
public:
	TaskExecChartSetMarkerOption(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoSeries> m_spSeries;
	ks_stdptr<oldapi::KsoDataPoint> m_spDataPoint;
};

class TaskExecChartSetSeriesOption : public TaskExecChartOpBase
{
public:
	TaskExecChartSetSeriesOption(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoSeries> m_spSeries;
	ks_stdptr<oldapi::KsoDataPoint> m_spDataPoint;
};

class TaskExecTrendlineOption : public TaskExecChartOpBase
{
public:
	TaskExecTrendlineOption(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT AfterOp(HRESULT opHr) override;
	PCWSTR GetTag() override;

private:
	ks_stdptr<oldapi::KsoTrendline> m_spApi;
};

class TaskExecDeleteTrendline : public TaskExecChartOpBase
{
public:
	TaskExecDeleteTrendline(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoTrendline> m_spApi;
};

class TaskExecDataLabelOption : public TaskExecChartOpBase
{
public:
	TaskExecDataLabelOption(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoDataLabel> m_spDataLabelApi;
	ks_stdptr<oldapi::KsoDataLabels> m_spDataLabelsApi;
};

class TaskExecLegendOption : public TaskExecChartOpBase
{
public:
	TaskExecLegendOption(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoLegend> m_spApi;
};
class TaskExecChartSetSeriesDataSource : public TaskExecChartOpBase
{
public:
	TaskExecChartSetSeriesDataSource(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;
private:
	HRESULT CheckCmdPermissionInner(const binary_wo::VarObj& param, KEtRevisionContext* ctx, WebStr str);

private:
	ks_stdptr<oldapi::KsoSeries> m_spSeries;
};


class TaskExecCategoryDataSource : public TaskExecChartOpBase
{
public:
	TaskExecCategoryDataSource(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT AfterOp(HRESULT opHr) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
private:
	ks_stdptr<oldapi::KsoSeriesCollection> m_spSeriesCollection;
};


class TaskExecNewSeries : public TaskExecChartOpBase
{
public:
	TaskExecNewSeries(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoSeriesCollection> m_spSeriesCollection;
};


class TaskExecAddSeriesByRange : public TaskExecChartOpBase
{
public:
	TaskExecAddSeriesByRange(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoSeriesCollection> m_spSeriesCollection;
};


class TaskExecSetSeriesVisible : public TaskExecChartOpBase
{
public:
	TaskExecSetSeriesVisible(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoSeries> m_spSeries;
};


class TaskExecDeleteSeries : public TaskExecChartOpBase
{
public:
	TaskExecDeleteSeries(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;

private:
	ks_stdptr<oldapi::KsoSeries> m_spSeries;
};


class TaskExecMoveSeries : public TaskExecChartOpBase
{
public:
	TaskExecMoveSeries(KEtWorkbook*);
	HRESULT BeforeOp(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	HRESULT Op(const binary_wo::VarObj & param, chart::KCTShape* shape, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
	HRESULT AfterOp(HRESULT opHr) override;
};

//-----------------------------------------------------------------------
class TaskExecDocSlim : public EtTaskExecBase
{
public:
	TaskExecDocSlim(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 现在还不需要判断
	}
};

class TaskExecIsAllocAutoSlim : public EtTaskExecBase
{
public:
	TaskExecIsAllocAutoSlim(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 现在还不需要判断
	}
};

class TaskExecSetValueByCustomList : public EtTaskExecBase
{
public:
	TaskExecSetValueByCustomList(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KCoreValidation::SetValueByCustomList 判断权限
	}
};

class TaskExecRangeQuickSet : public EtTaskExecBase
{
public:
	TaskExecRangeQuickSet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRangeFillQuickSet : public EtTaskExecBase
{
public:
	TaskExecRangeFillQuickSet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class TaskExecSetSubscriptionOption : public EtTaskExecBase
{
public:
	TaskExecSetSubscriptionOption(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecSetDocumentCustomProperty : public EtTaskExecBase
{
public:
	TaskExecSetDocumentCustomProperty(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}

private:
	KsoDocProperties stringToType(WebStr str);
	QDate getStringToDate(QString &strDate);
	DATE toSysTime(const QDate &date);
};

class TaskExecSetPrintArea : public EtMultiRangeTaskExecBase
{
public:
	TaskExecSetPrintArea(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecSetPageSetting : public EtTaskExecBase
{
public:
	TaskExecSetPageSetting(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT GetIPageSetupData(IDX sheetIdx, IPageSetupData** pIPageSetupData);
	void SetBNoPlsFalse(IDX sheetIdx);
	HRESULT SetPAGESETUPfromParam(VarObj& pram, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	void writeResOthers(HRESULT hr, KEtRevisionContext* pCtx, IDX sheetIdx);
	void invokePaginate(IDX sheetIdx);

	HRESULT setOrientation(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	HRESULT setPageZoom(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	HRESULT setMargins(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	HRESULT setCenterVertically(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	HRESULT setCenterHorizontally(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	HRESULT setPrintArea(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	HRESULT setPrintTitleColums(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	HRESULT setPrintTitleRows(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
	HRESULT setCenterFooter(VarObj& param, PageSetup* pPgStp, IDX sheetIdx, KEtRevisionContext* pCtx);
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecSheetSetPadding : public EtTaskExecBase
{
public:
	TaskExecSheetSetPadding(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class TaskExecSetListObjTotalsCal : public EtTaskExecBase
{
public:
	TaskExecSetListObjTotalsCal(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecRangeSetPreferredView: public EtTaskExecBase
{
public:
	TaskExecRangeSetPreferredView(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

class TaskExecPivotTableBase : public EtTaskExecBase
{
public:
	TaskExecPivotTableBase(KEtWorkbook*);

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;

	HRESULT CheckPivotTableIncludeHiddenRange(ISheet* spSheet, pivot_core::IPivotTable* spPvtTable, KEtRevisionContext* ctx);
	HRESULT GetPivotTable(
		KEtRevisionContext* ctx,
		const VarObj& param,
		pivot_core::IPivotTable** corePivotTable,
		etoldapi::PivotTable** pivotTable,
		etoldapi::_Worksheet** worksheet = nullptr);


	HRESULT GetPivotTable(
		KEtRevisionContext* ctx,
		etoldapi::Range* rg,
		pivot_core::IPivotTable** corePivotTable,
		etoldapi::PivotTable** pivotTable,
		etoldapi::_Worksheet* pWorksheet);

	HRESULT CheckSheetPermission(IDX sheetIdx);
};

class TaskExecCreatePivotTable : public EtTaskExecBase
{
public:
	TaskExecCreatePivotTable(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

private:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT updateFileName(KEtRevisionContext* pCtx);
	bool canCreatePivot(range_helper::ranges& rg);
};

class TaskExecRemovePivotTable : public TaskExecPivotTableBase
{
public:
	TaskExecRemovePivotTable(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableMoveField : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableMoveField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableSetFieldName : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableSetFieldName(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableSetFieldFunction : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableSetFieldFunction(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableSetFieldSubtotals : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableSetFieldSubtotals(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableRefresh : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableRefresh(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	HRESULT Refresh(KEtRevisionContext *pCtx, etoldapi::_Worksheet* pWorkSheet, pivot_core::IPivotTable* corePivotTable, etoldapi::PivotTable* pivotTable);
};

class TaskExecRefreshAllPivotTable : public TaskExecPivotTableRefresh
{
public:
	TaskExecRefreshAllPivotTable(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT RefreshSheetPivottables(etoldapi::_Worksheet*, KEtRevisionContext *);
};

class TaskExecPivotTableSetSourceData : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableSetSourceData(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableClearTable : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableClearTable(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableShowFieldList : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableShowFieldList(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableSetShowValuesAs : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableSetShowValuesAs(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableShowPages : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableShowPages(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableShowDetailAtArea : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableShowDetailAtArea(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
private:
	HRESULT showDetailAtDataArea(binary_wo::VarObj param, pivot_core::IPivotTable* pCorePvtTbl, etoldapi::PivotTable* pPvtTbl, etoldapi::Range* pRange);
	HRESULT showDetailAtHeader(binary_wo::VarObj param, etoldapi::PivotTable* pPvtTbl, etoldapi::Range* pRange);
};

class TaskExecPivotTableSetOptions : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableSetOptions(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotFieldSetOptions : public TaskExecPivotTableBase
{
public:
	TaskExecPivotFieldSetOptions(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableSetFieldLayoutPrint : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableSetFieldLayoutPrint(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTableSortField : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableSortField(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecPivotTablePivotCollapseButton : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTablePivotCollapseButton(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* pCtx) override;
};

class TaskExecPivotTableGroupBase : public TaskExecPivotTableBase
{
public:
	TaskExecPivotTableGroupBase(KEtWorkbook*);
protected:
	HRESULT CreateRangeByTwoRanges(_Worksheet* pSheet, Range* pFirst, Range* pSecond, Range** ppRange);
	bool IsDateTimeFormat(PCWSTR str, double* pd);
};

class TaskExecPivotTableGroup : public TaskExecPivotTableGroupBase
{
public:
	TaskExecPivotTableGroup(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
private:
	pivot_core::ConstantGroupByType strTogroupType(WebStr type);
	void readGroupArgs(binary_wo::VarObj var, std::vector<pivot_core::ConstantsGroupArgument>& vec, WebName name = "groupArgs");
};


class TaskExecPivotTableUnGroup : public TaskExecPivotTableGroupBase
{
public:
	TaskExecPivotTableUnGroup(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};


class TaskExecAddSlicer : public TaskExecPivotTableBase
{
public:
	TaskExecAddSlicer(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
private:
	HRESULT readRect(binary_wo::VarObj var, RECT& rect);
	HRESULT readSize(binary_wo::VarObj var, QPoint& point);
	HRESULT readFields(binary_wo::VarObj var, std::vector<WebStr>& vec);
	HRESULT AddSlicer_listobject(etoldapi::_Worksheet* worksheet, ListObject* listobj, const QString &strFldName, const RECT &pt, QPoint &sz);
};

class TaskExecPivotSelectSlicerItem: public TaskExecPivotTableBase
{
public:
	TaskExecPivotSelectSlicerItem(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
private:
	HRESULT readSelectState(binary_wo::VarObj var, std::vector<bool>& vec);
};

class TaskExecPivotClearSlicerItem: public TaskExecPivotTableBase
{
public:
	TaskExecPivotClearSlicerItem(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};


class TaskExecPivotSetSlicerStartIndex: public TaskExecPivotTableBase
{
public:
	TaskExecPivotSetSlicerStartIndex(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};


class TaskExecPivotSetSlicerOptions: public TaskExecPivotTableBase
{
public:
	TaskExecPivotSetSlicerOptions(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};


class TaskExecPivotConnectPivotTable: public TaskExecPivotTableBase
{
public:
	TaskExecPivotConnectPivotTable(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
private:
	HRESULT readConnectState(binary_wo::VarObj var, std::vector<bool>& vec);
};

class TaskExecPivotAddCalculatedItem: public TaskExecPivotTableBase
{
public:
	TaskExecPivotAddCalculatedItem(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecPivotModifyCalculatedItem: public TaskExecPivotTableBase
{
public:
	TaskExecPivotModifyCalculatedItem(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecPivotDelCalculatedItem: public TaskExecPivotTableBase
{
public:
	TaskExecPivotDelCalculatedItem(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecPivotAddCalculatedField: public TaskExecPivotTableBase
{
public:
	TaskExecPivotAddCalculatedField(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecPivotModifyCalculatedField: public TaskExecPivotTableBase
{
public:
	TaskExecPivotModifyCalculatedField(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecPivotDelCalculatedField: public TaskExecPivotTableBase
{
public:
	TaskExecPivotDelCalculatedField(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecPivotExpandRange: public TaskExecPivotTableBase
{
public:
	TaskExecPivotExpandRange(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};


class TaskExecPivotSlicerRefresh: public TaskExecPivotTableBase
{
public:
	TaskExecPivotSlicerRefresh(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecPivotItemMove: public TaskExecPivotTableBase
{
public:
	TaskExecPivotItemMove(KEtWorkbook* );
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	pivot_core::IDX_AXIS GetSelectedItems(pivot_core::IPivotTable* tbl, const RANGE& rg, bool bMoveRow, INT& idxBegin, INT& idxEnd);
	bool MoveItemsInner(etoldapi::PivotTable* pPivotTable, pivot_core::IPivotTable* tbl, pivot_core::IDX_AXIS idx, INT idxBegin, INT idxEnd, INT pos, bool bMoveRow);
	INT CalcDestPos(pivot_core::IPivotTable* tbl, RANGE& rg, bool bAfter, bool bRow);
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	HRESULT m_hr = S_OK;
};

class TaskExecSetTextLink : public EtTaskExecBase
{
public:
	TaskExecSetTextLink(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

	static HRESULT SetLinkRuns(Range *spRange, const VarObj& linkRuns);
	static LINKRUNSTYPE linkRunsTypeFromStr(PCWSTR);

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckProtectionRange(cmd, ctx);
	}
};

class TaskExecClearTransactions : public EtTaskExecBase
{
public:
	TaskExecClearTransactions(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
};

class TaskExecBookSetInsertPicAsAttachment : public EtTaskExecBase
{
public:
	TaskExecBookSetInsertPicAsAttachment(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 Exec 判断权限
	}
};

class TaskExecUpdateImportrangeUrl : public EtTaskExecBase
{
public:
	TaskExecUpdateImportrangeUrl(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;	// 不需要判断权限，授权文件后更新跨book、importRange公式
	}
};

class TaskExecUpdateSupbookData : public EtTaskExecBase
{
public:
	TaskExecUpdateSupbookData(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;	// 不需要判断权限，授权文件后更新跨book、importRange公式
	}
};

class TaskExecInitImportrangeCalc : public EtTaskExecBase
{
public:
	TaskExecInitImportrangeCalc(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;	// 不需要判断权限，授权文件后更新跨book、importRange公式
	}
private:
	void InitCbRefersAutoUpdate(KEtRevisionContext*);
};

class TaskExecUpdateCrossbookState : public EtTaskExecBase
{
public:
	TaskExecUpdateCrossbookState(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;	// 不需要判断权限，授权文件后更新跨book、importRange公式
	}
};

class TaskExecAutoUpdateMergeFile : public EtTaskExecBase
{
public:
	TaskExecAutoUpdateMergeFile(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecMergeFileAllTask : public EtTaskExecBase
{
public:
	TaskExecMergeFileAllTask(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return CheckBookHasHiddenPropRange(ctx);
	}
};

class TaskExecUpdateRemoteChart: public EtTaskExecBase
{
public:
	TaskExecUpdateRemoteChart(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};


class TaskExecSetProtectedCol: public EtTaskExecBase
{
public:
	TaskExecSetProtectedCol(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}

	HRESULT ApplyOwner(ISheet *ptrSheet, const RANGE& rg, PCWSTR uid);
	INT SetTilteCnt(ISheet *ptrSheet, const RANGE& rg, INT titleCnt, KEtRevisionContext* pCtx, bool bNeedCaclTitleCnt);
};

class TaskExecReCalcSubtotal: public EtTaskExecBase
{
public:
	TaskExecReCalcSubtotal(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecChartSetPloVisibleOnly : public EtTaskExecBase
{
public:
	TaskExecChartSetPloVisibleOnly(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecChartSetDisplayBlanksAs : public EtTaskExecBase
{
public:
	TaskExecChartSetDisplayBlanksAs(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		VarObj param = cmd->cast().get("param");
		IDX iSheet = param.field_int32("sheetIdx");
		return CheckObjectProtection(iSheet, ctx);
	}
};

class TaskExecPublishInquirer: public EtTaskExecBase
{
public:
	TaskExecPublishInquirer(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecRemoveInquirer: public EtTaskExecBase
{
public:
	TaskExecRemoveInquirer(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	
};

class TaskExecPageBreak: public EtTaskExecBase
{
public:
	TaskExecPageBreak(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK;
	}
	
};

/**
 * 多选sheet操作：插入、复制、移动、删除、隐藏、标签颜色
 * 多选不连续sheet，不允许插入
 * 多选sheet不支持设置“保护工作表”
 * 多选sheet不支持重命名
*/
class TaskExecMultiSheetsOp: public EtTaskExecBase
{
    enum OP_TYPE {
       OP_BEGIN_FLAG = 0,

       OP_INSERT,   // 插入
       OP_COPY,     // 复制
       OP_MOVE,     // 移动
       OP_DELETE,   // 删除
       OP_HIDDEN,   // 隐藏
       OP_TAB_COLOR,// 标签颜色

       OP_END_FLAG, 
    };
public:
    TaskExecMultiSheetsOp(KEtWorkbook*);
    HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

// 内部命令，测试使用
class TaskExecClearCellHistory: public EtTaskExecBase
{
public:
	TaskExecClearCellHistory(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx);
};

// 内部命令，测试使用
class TaskExecSetCellHistoryPurgeOptions: public EtTaskExecBase
{
public:
	TaskExecSetCellHistoryPurgeOptions(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx)
	{
		return S_OK;
	}
};

class TaskExecRangeSetValue2 : public EtTaskExecBase
{
public:
	TaskExecRangeSetValue2(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx);
	bool IsCompileFormulaAsText(KEtRevisionContext* ctx) override
	{
		return false; // 允许输入公式
	}

};

class TaskExecRangeExportAutoFilterValues : public EtTaskExecBase
{
public:
	TaskExecRangeExportAutoFilterValues(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx);
};


// 辅助类. 有一些操作, 它们需要分别定义成et及db的事务, 很显然它们具有相似的实现
// 通过辅助类及组合的方式实现代码复用

class TaskExecHelperSerial
{
public:
	explicit TaskExecHelperSerial(KEtWorkbook* wwb) : m_wwb(wwb) {}
public:
	bool SerialRowColHeader(binary_wo::VarObj objCmd, ISerialAcceptor* acpt);
	bool SerialUserFilterHidden(binary_wo::VarObj objCmd, ISerialAcceptor* acpt);
	bool CommandWithExtraData(binary_wo::VarObj, ISerialAcceptor*);
private:
	KEtWorkbook* m_wwb;
};

class TaskExecHelperImportWorkbook
{
public:
	TaskExecHelperImportWorkbook(KEtWorkbook* wwb, IDBProtectionJudgement* pProtectionJudgement)
		: m_wwb(wwb), m_pProtectionJudgement(pProtectionJudgement) {}
public:
	HRESULT operator()(EtTaskExecBase* pTask, KwCommand* cmd, KEtRevisionContext* pCtx);
	UINT getActiveSheetId();
private:
    HRESULT canImportWorkbook(KEtRevisionContext* pCtx, etoldapi::_Workbook* pSrcWorkbook);
    void setUnsupportedDbFieldTypes(binary_wo::VarObj& param, const std::set<ET_DbSheet_FieldType>& unsupportedDbFieldTypes);
    void parseUnsupportedDbFieldTypes(const binary_wo::VarObj& param, std::set<ET_DbSheet_FieldType>& unsupportedDbFieldTypes);
	void setUnsupportedDbViews(binary_wo::VarObj& param, const std::set<ET_DBSheet_ViewType>& unsupportedDbViews);
    void parseUnsupportedDbViews(const binary_wo::VarObj& param, std::set<ET_DBSheet_ViewType>& unsupportedDbViews);
private:
	KEtWorkbook* m_wwb;
	IDBProtectionJudgement* m_pProtectionJudgement;
private:
    // 保留新增sheets的第一个sheet的id供前端聚焦
    UINT m_activeSheetId{0};
    // 是否超过了最大sheet导入数量，供前端提示用户
    bool m_exceededMaxImportSheetCount{false};
    // 最大sheet导入数量
    static const int m_maxImportSheetCount{100};
};

class TaskExecHelperBookRecalculate
{
public:
	explicit TaskExecHelperBookRecalculate(KEtWorkbook* wwb) : m_wwb(wwb) {}
public:
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* pCtx);
	bool SerialCommand(KwCommand* cmd, ISerialAcceptor* acpt);
private:
	KEtWorkbook* m_wwb;
};

class TaskExecEtEmpty: public EtTaskExecBase
{
public:
	TaskExecEtEmpty(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 内部调用
	}
};

class TaskExecEtShareSheet: public EtTaskExecBase
{
public:
	TaskExecEtShareSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecEtCancelShareSheet: public EtTaskExecBase
{
public:
	TaskExecEtCancelShareSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecSheetCalculate : public EtTaskExecBase
{
public:
	TaskExecSheetCalculate(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 不需要判断
	}
};

// 清除AI列：清除AI列的数据有效性、清除AI列公式但保留计算结果
class TaskExecCleanAIColumn : public EtTaskExecBase
{
public:
	TaskExecCleanAIColumn(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::SetFormula 和 Exec 执行时判断权限
	}
};
class TaskExecMultiRangeClearContents : public EtMultiRangeTaskExecBase
{
public:
	TaskExecMultiRangeClearContents(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::ClearFormats 判断权限
	}
};

class TaskExecMultiRangeClearAll : public EtMultiRangeTaskExecBase
{
public:
	TaskExecMultiRangeClearAll(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::Clear 判断权限
	}
};

class TaskExecMultiRangeClearSpecialChars : public EtMultiRangeTaskExecBase
{
public:
	TaskExecMultiRangeClearSpecialChars(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在 KAppCoreRange::ClearSpecialChars 判断权限
	}
	bool GetClearSpecialCharsOption(PCWSTR, CLEAR_SPECIAL_CHARS_OPTION&);
};

class TaskExecMultiRangeClearComments : public EtMultiRangeTaskExecBase
{
public:
	TaskExecMultiRangeClearComments(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;

protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
};

class TaskExecSetCustomCalendar: public EtTaskExecBase
{
public:
	TaskExecSetCustomCalendar(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
private:
	void CollectArrayDate(WebName name, CustomCalendarType type, ICustomCalendar* pCalendar, VarObj obj);
	HRESULT CollectAllCustomCalendar(IBook* pBook, VarObj obj);
};

class TaskExecEtAddWebExtension : public EtTaskExecBase
{
public:
	explicit TaskExecEtAddWebExtension(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecEtModifyWebExtension : public EtTaskExecBase
{
public:
	explicit TaskExecEtModifyWebExtension(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecEtDeleteWebExtension : public EtTaskExecBase
{
public:
	explicit TaskExecEtDeleteWebExtension(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecEtBatchModifyWebExtension : public EtTaskExecBase
{
public:
	explicit TaskExecEtBatchModifyWebExtension(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecWebChartSetStyle : public EtTaskExecBase
{
public:
	explicit TaskExecWebChartSetStyle(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecWebChartRemoveStyle : public EtTaskExecBase
{
public:
	explicit TaskExecWebChartRemoveStyle(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

class TaskExecWebChartSetOrder : public EtTaskExecBase
{
public:
	explicit TaskExecWebChartSetOrder(KEtWorkbook*);
	HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
	HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
};

// 拆分表格
class TaskExecSplitSheet : public EtTaskExecBase
{
public:
	TaskExecSplitSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在执行中判断
	}
private:
	void AddResult(HRESULT hr, const ks_wstring& err, WebID objId);

};

class TaskExecEtV8JsEvaluate : public EtTaskExecBase
{
public:
	TaskExecEtV8JsEvaluate(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext* ctx) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在执行中判断
	}

};

class TaskExecAppendSheet : public EtTaskExecBase
{
public:
	TaskExecAppendSheet(KEtWorkbook*);
	HRESULT operator()(KwCommand* cmd, KEtRevisionContext*) override;
	PCWSTR GetTag() override;
protected:
	HRESULT CheckCmdPermission(KwCommand* cmd, KEtRevisionContext* ctx) override
	{
		return S_OK; // 在执行中判断
	}
private:
	HRESULT CopyBookData(KEtRevisionContext* ctx, ISheet* spWorksheetDst, _Workbook* ptrWorkbookSrc, VarObj& srcSheetIds);
	void SyncRowColumnSize(ISheet* pSheetSrc, ISheet* pSheetDst);
private:
	int m_curEndEmptyRow = -1;
};

}//namespace wo

#endif //__WEBET_ET_TASK_CLASS_H__
