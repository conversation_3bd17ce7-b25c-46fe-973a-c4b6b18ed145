﻿#include "etstdafx.h"

#include "db2et_exporter.h"

#include "etcore/et_core_dbsheet.h"
#ifndef X_OS_WINDOWS
#include "kern/errno.h"
#endif
#include "workbook.h"
#include "applogic/et_apihost.h"
#include "db_export_helper.h"
#include "et_dbsheet_utils.h"
#include "kfc/tools/variant_array.h"
#include "util.h"
#include "utils/et_gridsheet_utils.h"
#include "book_format_converter/db_append_data_adapter.h"
#include "utils/qrlabel_utils.h"
#include "db/db_basic_itf.h"

namespace wo
{
namespace
{
	const std::unordered_set<PCWSTR, util::PcwstrIHash, util::PcwstrICmp> pictureTypes = {
			__X("png"), __X("jpeg"), __X("jpg"), __X("bmp"), __X("gif"), __X("svg"), __X("webp"),
			__X("image/png"), __X("image/jpeg"), __X("image/jpg"), __X("image/bmp"), __X("image/gif"), __X("image/svg"), __X("image/webp")
		};
}



Db2XlsxExporter::Db2XlsxExporter(KEtWorkbook* wwb, _Workbook* pEtWorkBook, KEtRevisionContext* pCtx)
    : m_pDbWorkBook(wwb->GetCoreWorkbook())
	, m_pEtWorkBook(pEtWorkBook)
	, m_pCtx(pCtx)
{
	m_pBook = m_pDbWorkBook->GetBook();

	if (m_pBook && (m_pBook->GetBMP()->bDbSheet || m_pBook->GetBMP()->bKsheet))
    {
        IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->BeginExportDbt(m_pBook);
    }
}

Db2XlsxExporter::Db2XlsxExporter(_Workbook *pDbWorkBook, _Workbook *pEtWorkBook, KEtRevisionContext* pCtx)
    : m_pDbWorkBook(pDbWorkBook)
	, m_pEtWorkBook(pEtWorkBook)
	, m_pCtx(pCtx)
{
    m_pBook = m_pDbWorkBook->GetBook();
	if (m_pBook && (m_pBook->GetBMP()->bDbSheet || m_pBook->GetBMP()->bKsheet))
    {
        IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->BeginExportDbt(m_pBook);
    }
}

Db2XlsxExporter::~Db2XlsxExporter()
{
    if (m_pBook && (m_pBook->GetBMP()->bDbSheet || m_pBook->GetBMP()->bKsheet))
    {
        IDBSheetCtx *pCtx = _appcore_GainDbSheetContext();
		pCtx->EndExportDbt(m_pBook);
    }
}

HRESULT Db2XlsxExporter::Init()
{
	// 获取 et bookop
	if (!m_pEtWorkBook)
		return E_FAIL;
	m_pEtWorkBook->GetBook()->GetOperator(&m_spEtBookOp);
	if (!m_spEtBookOp)
		return E_FAIL;
	return S_OK;
}

PCWSTR Db2XlsxExporter::getNumberFormat(IDbField* pField) const
{
	ASSERT(pField);
	ET_DbSheet_FieldType fieldType = pField->GetType();
	switch (fieldType)
	{
		case Et_DbSheetField_Checkbox:
			// et_numfmt_str.h中未定义. [=1]"☑";[=0]"☐";0;@
			return __X("[=1]\"☑\";[=0]\"☐\";0;@");
		case Et_DbSheetField_Lookup:
		{
			ks_stdptr<IDbField_Lookup> spFieldLookup = pField;
			ks_stdptr<IDbField_Link> spFieldLink;
			HRESULT hr = spFieldLookup->GetLinkField(&spFieldLink);
			// 如果是关联单条
			if (SUCCEEDED(hr) && FALSE == spFieldLink->GetSupportMultiLinks())
			{
				ks_stdptr<IDbField> spBaseField;
				hr = spFieldLookup->GetLookupBaseField(&spBaseField);
				// 且是复选框字段
				if (SUCCEEDED(hr) && spBaseField->GetType() == Et_DbSheetField_Checkbox)
					return __X("[=1]\"☑\";[=0]\"☐\";0;@");
			}
			return pField->GetNumberFormat();
		}
		default:
			return pField->GetNumberFormat();
	}
}

int Db2XlsxExporter::getRowHeight() const
{
	return m_pBook->GetWoStake()->getSetting()->getDefaultGridViewHeight();
}
int Db2XlsxExporter::getColWidth(EtDbId fieldId, IDBSheetOp* sheetOp) const
{
	int width = 0;
	HRESULT hr = sheetOp->GetFieldDefWidth(fieldId, &width);
	if (FAILED(hr) || width == 0)
	{
		ASSERT(FALSE);
		// sheet上拿不到列宽, 则使用默认列宽
		width = COL_WIDTH_DEF;
	}
	return width;
}

HRESULT Db2XlsxExporter::CopySheetName(_Worksheet* pWorksheet, PCWSTR name)
{
	ASSERT(pWorksheet && name);
	IKWorksheets* pWorksheets = m_pEtWorkBook->GetWorksheets();
	ks_bstr newName;
	HRESULT hr = GetValidSheetName(pWorksheets, pWorksheet, name, &newName);
	if (FAILED(hr))
		return hr;
	// 导出场景下，是通过接口新增了workbook，并直接初始化好了需要的表，但是这些表名可能跟要设置的名称有冲突，因此先提前把有冲突的表名进行处理;最终，所有表名都会重置
	IDX idx = INVALIDIDX;
	m_pEtWorkBook->GetBook()->GetSheetIdxByName(name, &idx);
	if (idx != INVALIDIDX)
	{
		ks_stdptr<_Worksheet> spWorksheet = pWorksheets->GetSheetItem(idx);
		hr = spWorksheet->put_Name(newName);
		if (FAILED(hr))
			return hr;
		newName.assign(name);
	}
	return pWorksheet->put_Name(newName);
}

void Db2XlsxExporter::getAttachmentFileData(UINT sheetId, EtDbId fieldId, EtDbId recordId, int i, AttachmentInfo& attachmentInfo)
{
	AttachmentPos attachmentPos = {fieldId, recordId, i};
	if (m_pAttachmentData == nullptr)
		return;
	auto iter = m_pAttachmentData->find(sheetId);
	if (iter != m_pAttachmentData->end())
	{   
		auto it = iter->second.find(attachmentPos);  
		if (it != iter->second.end())
		{  
			attachmentInfo = it->second;
		}
	}
}

HRESULT Db2XlsxExporter::removeEtField(IDX sheetIDX, int etColPos)
{
	BMP_PTR pBMP = m_pEtWorkBook->GetBook()->GetBMP();
	RANGE rg(pBMP);
	rg.SetCols(sheetIDX, sheetIDX, etColPos, etColPos);
	return m_spEtBookOp->RemoveRange(rg, dirLeft);
}

bool Db2XlsxExporter::isFieldFiltered(ET_DbSheet_FieldType baseFieldType, ET_DbSheet_FieldType fieldType)
{
	if (baseFieldType == Et_DbSheetField_CellPicture || baseFieldType == Et_DbSheetField_Automations || baseFieldType == Et_DbSheetField_Button ||
		((!m_exportAttachment || fieldType == Et_DbSheetField_Lookup) && baseFieldType == Et_DbSheetField_Attachment))
		return true;
	return false;
}

HRESULT Db2XlsxExporter::dbViewExportData(IDX sheetIDX, PCWSTR sheetName, const IDBIds* records, const IDBIds* fields, IDBSheetOp* sheetOp, IDBSheetView* pView, bool isColumnHeaderFormatExported)
{
	HRESULT hr = S_OK;

	// 获取 XLSX 的 worksheet
	IKWorksheets* pWorksheets = m_pEtWorkBook->GetWorksheets();
	ks_stdptr<_Worksheet> spEtWorksheet = pWorksheets->GetSheetItem(sheetIDX);
	if (!spEtWorksheet)
	{
		ASSERT(!"Get _Worksheet from IKWorksheets::GetSheetItem failed!");
		WOLOG_WARN << "Get _Worksheet fromIKWorkbook::GetSheetItem failed: sheetFrom:" << sheetIDX;
		return E_FAIL;
	}

	// 导出优化. 导出后进行自适应的批处理.
	ExportOptimizer exportOptimizer(m_pEtWorkBook->GetBook()->LeakOperator());

	ISheet* pEtSheet = spEtWorksheet->GetSheet();
	BMP_PTR pBMP = m_pEtWorkBook->GetBook()->GetBMP();
	std::map <EtDbId, UINT> fieldAttachmentCnt;
	//检查导出时，拆分附件列是否超出列上限,超出列上限时不导出附件列
	m_exceedMaxColumns = isExceedMaxColumns(records, fields, sheetOp, fieldAttachmentCnt);
	if (m_exceedMaxColumns)
		setIsExportAttachment(false);
	RANGE etRANGE(pBMP);
	// 写入表名
	hr = CopySheetName(spEtWorksheet, sheetName);
	if (FAILED(hr))
		return hr;

	// et插入一行写列头
	KComVariant var;
	var.AssignDouble(etShiftDown);
	etRANGE.SetRows(sheetIDX, sheetIDX, 0, 0);
	ks_stdptr<etoldapi::Range> spEtApiCellRange;
	VS(spEtWorksheet->GetRangeByData(&etRANGE, &spEtApiCellRange));
	hr = spEtApiCellRange->Insert(var, KComVariant());
	if (FAILED(hr))
		return hr;

	KXFMASK clrMask;
	clrMask.inc_eft = 1;
	clrMask.inc_clrBack = 1;
	clrMask.inc_clr = 1;
	UINT sheetId = sheetOp->GetSheetId();
	const IDBIds* pAllFields = sheetOp->GetAllFields();
	const IDBIds* pAllRecords = sheetOp->GetAllRecords();
	ISheet* pSheet = sheetOp->GetRawSheet();
	int etColPos = 0;
	INT nWidth = app_helper::GetColWidthWithChars(m_pEtWorkBook, 15);
	for (int i = 0, fieldsCnt = fields->Count(); i < fieldsCnt; ++i)
	{
		EtDbId fieldId = fields->IdAt(i);
		ks_stdptr<IDbField> spField;
		hr = sheetOp->GetFieldsManager()->GetField(fieldId, &spField);
		if (FAILED(hr))
			continue;
		if (m_isForAppendTemplate && !DbAppendDataAdapter::IsSupportField(spField))
		{
			hr = removeEtField(sheetIDX, etColPos);
			if (FAILED(hr))
				return hr;
			continue;
		}
		ET_DbSheet_FieldType fieldType = spField->GetType();

		ET_DbSheet_FieldType baseFieldType = fieldType;
		if (fieldType == Et_DbSheetField_Lookup)
		{
			ks_stdptr<IDbField> spBaseField;
			ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
			spFieldLookup->GetLookupBaseField(&spBaseField);
			if (spBaseField)
				baseFieldType = spBaseField->GetType();
		}

		if (isFieldFiltered(baseFieldType, fieldType))
		{
			hr = removeEtField(sheetIDX, etColPos);
			if (FAILED(hr))
				return hr;
			continue;
		}

		// 在第一行记录字段名.
		etRANGE.SetCell(sheetIDX, 0, etColPos);
		m_spEtBookOp->SetCellText(etRANGE, spField->GetName(), cvoForceText);
		// et列头颜色清除列颜色
		m_spEtBookOp->ClearCellFormat(etRANGE, fsoNone);
		if (pView != nullptr && pView->GetType() == et_DBSheetView_Grid && isColumnHeaderFormatExported)
		{
			ARGB bgColor = 0;
			ks_stdptr<IDBSheetView_Grid> spGridView = pView;
			hr = spGridView->GetValidFieldTitleFormat(fieldId, &bgColor);

			if (SUCCEEDED(hr))
			{
				KXF xf;
				EtColor clrBg;
				clrBg.setARGB(bgColor);
				xf.fill.setBack(clrBg);
				xf.fill.setType(eftPatternSolid);
				hr = m_spEtBookOp->SetCellFormat(etRANGE, clrMask, xf.getxf());
				if (FAILED(hr))
					return hr;
			}
		}
		// 设置列宽 (基于field属性, 因此放在主循环内)
		pEtSheet->SetColWidth(etColPos, etColPos, getColWidth(fieldId, sheetOp));
		if (!m_copyContent)
		{
			++etColPos;
			continue;
		}
		// 向整列写入字段属性
		etRANGE.SetCols(sheetIDX, sheetIDX, etColPos, etColPos);
		ks_stdptr<etoldapi::Range> spEtRange;
		VS(spEtWorksheet->GetRangeByData(&etRANGE, &spEtRange));
		spEtRange->put_NumberFormat(ks_bstr(getNumberFormat(spField.get())));
		// 整列写入隐藏属性
		if (pView != nullptr)
		{
			const IDBIds* pVisibleFields = pView->GetVisibleFields();
            EtDbIdx col = pVisibleFields->Id2Idx(fieldId);
			if (col == INV_EtDbIdx)
			{
				ks_stdptr<Range> spColRange;
				spEtRange->get_EntireColumn(&spColRange);
				if (!spColRange)
					return E_INVALIDARG;
				KComVariant var;
				var.AssignBOOL(true);
				hr = spColRange->put_Hidden(var);
				if (FAILED(hr))
					return hr;
			}
		}
		// 为"等级"和"是否"预备的默认值token.
		alg::managed_vdbl_token_assist mValZeroToken;
		mValZeroToken.create(0);

		// 超链接设置为超链接类型
		ks_stdptr<IKHyperlinks> spEtHyperlinks;
		bool exportHyperlink = false;
		if (fieldType == Et_DbSheetField_Url || baseFieldType == Et_DbSheetField_Url)
		{
			exportHyperlink = true;
			VS(pEtSheet->GetExtDataItem(edSheetHyperlinks, (IUnknown**)&spEtHyperlinks));
		}

		if (m_exportCompressedBundle && !spEtHyperlinks)
		{
			VS(pEtSheet->GetExtDataItem(edSheetHyperlinks, (IUnknown**)&spEtHyperlinks));
		}

		int attachmentColPos = etColPos;
		if (fieldType == Et_DbSheetField_Attachment && m_exportAttachment )
		{
			auto it = fieldAttachmentCnt.find(fieldId);
			if (it == fieldAttachmentCnt.end() || it->second == 0)
			{
				hr = removeEtField(sheetIDX, attachmentColPos);
				if (FAILED(hr))
					return hr;
				continue;
			}
			UINT needInsertColCnt = it->second-1;
			if (needInsertColCnt != 0)
			{
				hr = setNewAttachmentCol(spEtWorksheet, spField, needInsertColCnt, attachmentColPos);
				if (FAILED(hr))
					continue;
				pEtSheet->SetColWidth(etColPos, attachmentColPos, nWidth);
			}
		}

		for (int j = 0, recordsCnt = records->Count(); j < recordsCnt; ++j)
		{
			EtDbId recId = records->IdAt(j);
			// 创建et的range, 位置为 j + 1 行, etColPos 列
			if (j + 1 == pBMP->cntRows)
				break;
			etRANGE.SetCell(sheetIDX, j + 1, etColPos);
			// 导出数据需要保留颜色格式信息
			if (m_needCopyDataFormat)
			{
				const XF* pDbXF = nullptr;
				hr = sheetOp->GetCellFormat(recId, fieldId, &pDbXF, nullptr);
				if (FAILED(hr))
					return hr;
				hr = m_spEtBookOp->SetCellFormat(etRANGE, clrMask, *pDbXF);
				if (FAILED(hr))
					return hr;
			}

			if (exportHyperlink)
			{
				const_token_ptr pToken = nullptr;
				hr = sheetOp->GetValueToken(recId, fieldId, &pToken);
				if (FAILED(hr) || !pToken)
					continue;

				if (fieldType == Et_DbSheetField_Url)
					setHyperlink(spEtWorksheet, etRANGE, pToken, spField, spEtHyperlinks);
				else
				{
					ks_bstr bDisplayVal;
					hr = sheetOp->GetDisplayString(recId, fieldId, &bDisplayVal);
					if (FAILED(hr))
						continue;

					// 先设值单元格，否则会把Runs清掉，同时兼容旧逻辑
					m_spEtBookOp->SetCellText(etRANGE, bDisplayVal.c_str(), cvoForceText);
					if (bDisplayVal.empty())
						continue;

					setHyperlinkRuns(spEtWorksheet, etRANGE, pToken, spField, spEtHyperlinks);
				}
				continue;
			}
			//db导出为xlsx时，导出图片附件
			if (fieldType == Et_DbSheetField_Attachment)
			{
				const_token_ptr pToken = nullptr;
                hr  = sheetOp->GetValueToken(recId, fieldId, &pToken);
                if (FAILED(hr) || !pToken || !alg::const_handle_token_assist::is_type(pToken))
                    continue;
				RecordInfo recordInfo (sheetId, fieldId, recId, j, etColPos);
				UINT insertIndex = 0;
				setCellValueForAttachment(pToken, spEtWorksheet, spEtHyperlinks, recordInfo, 0, insertIndex);
				continue;
			}
			// 直接输出文本的字段
			switch (baseFieldType)
			{
				case Et_DbSheetField_MultiLineText:
				case Et_DbSheetField_SingleLineText:
				case Et_DbSheetField_SingleSelect:
				case Et_DbSheetField_MultipleSelect:
				case Et_DbSheetField_Contact:
				case Et_DbSheetField_CreatedBy:
				case Et_DbSheetField_LastModifiedBy:
				case Et_DbSheetField_Note:
				case Et_DbSheetField_Link:
				case Et_DbSheetField_OneWayLink:
                case Et_DbSheetField_Address:
				case Et_DbSheetField_Cascade:
				case Et_DbSheetField_Department:
				case Et_DbSheetField_BarCode:
				{
					ks_bstr bstrVal;
					hr = sheetOp->GetDisplayString(recId, fieldId, &bstrVal);
					m_spEtBookOp->SetCellText(etRANGE, bstrVal.c_str(), cvoForceText);
					continue;
				}
				default:
					break;
			}

			// 输出格式的字段. 以 Token 和 NumberFormat 的形式.
			const_token_ptr pToken = nullptr;
			hr = sheetOp->GetValueToken(recId, fieldId, &pToken);
			if (FAILED(hr))
				continue;

			// 处理dbsheet内的错误码
			if (alg::const_error_token_assist::is_type(pToken))
			{
				DWORD val = alg::const_error_token_assist(pToken).get_code();
				if (alg::EXV_E_CIRCULAR == val)
				{
					alg::managed_error_token_assist meta;
					meta.create(alg::EXV_E_VALUE);
					VS(m_spEtBookOp->SetCellValue(etRANGE, meta, cvoNormal));
					continue;
				}
			}

			// “是否”和“等级”字段需要默认值。
			if ((baseFieldType == Et_DbSheetField_Checkbox || baseFieldType == Et_DbSheetField_Rating) && !pToken)
				pToken = mValZeroToken;
			if (etexec::const_token_assist(pToken).major_type() == etexec::ETP_HANDLE)
			{
				if (fieldType == Et_DbSheetField_Lookup)
				{
					ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
					ks_stdptr<IDbField_Link> spFieldLink;
					hr = spFieldLookup->GetLinkField(&spFieldLink);
					// 如果是关联单条
					if (SUCCEEDED(hr) && FALSE == spFieldLink->GetSupportMultiLinks())
					{
						alg::const_handle_token_assist hta(pToken);
						if (hta.get_handleType() == alg::ET_HANDLE_TOKENARRAY && hta.get_handle() != nullptr)
						{
							ks_stdptr<IDbTokenArrayHandle> spArray = hta.get_handle()->CastUnknown();
							UINT cnt = spArray->GetCount();
							// 取第一个token
							if (cnt > 0)
								spArray->Item(0, &pToken);
							else
								pToken = nullptr;

							if (!pToken  && (baseFieldType == Et_DbSheetField_Checkbox || baseFieldType == Et_DbSheetField_Rating))
								pToken = mValZeroToken;

							if (etexec::const_token_assist(pToken).major_type() != etexec::ETP_HANDLE)
							{
								// 第一个不是handle, 直接设token
								VS(m_spEtBookOp->SetCellValue(etRANGE, pToken, cvoNormal));
								continue;
							}
						}
					}
				}

				ks_bstr bstrVal;
				hr = sheetOp->GetDisplayString(recId, fieldId, &bstrVal);
				if (FAILED(hr))
					continue;
				m_spEtBookOp->SetCellText(etRANGE, bstrVal.c_str(), cvoForceText);
				continue;
			}
			// 向 XLSX 文件写入 Token.
			VS(m_spEtBookOp->SetCellValue(etRANGE, pToken, cvoNormal));
		}
		// 列导出完毕后，更新导出列位置。
		etColPos = attachmentColPos;
		++etColPos;
	}

	// 整表写入db的默认字体字号
	const XF* pDbXF = nullptr;
	hr = m_pBook->GetNormalXF(&pDbXF);
	if (SUCCEEDED(hr))
	{
		etRANGE.SetSheets(sheetIDX, sheetIDX);
		ks_stdptr<etoldapi::Range> spEtApiCellRange;
		VS(spEtWorksheet->GetRangeByData(&etRANGE, &spEtApiCellRange));
		ks_stdptr<IFormatHost> host = spEtApiCellRange;
		if (host)
		{
			KXFMASK mask;
			mask._cats = XFMASK::_catNone;
			mask._catsFont = static_cast<XFMASK::_category_font>(XFMASK::_cat_dyHeight | XFMASK::_cat_bFamily);
			hr = host->SetXF(&mask, pDbXF);
			if (FAILED(hr))
				return hr;
		}
	}

	// 设置行高. 除第一行字段名之外, 全表使用基于内容的相同行高.
	etRANGE.SetRows(sheetIDX, sheetIDX, 0, pBMP->cntRows - 1);
	ks_stdptr<etoldapi::Range> spApiRangeAllRows;
	VS(spEtWorksheet->GetRangeByData(&etRANGE, &spApiRangeAllRows));
	// 为使用 put_RowHeight 方法, 高度要除以20
	spApiRangeAllRows->put_RowHeight(KComVariant(getRowHeight() / 20));

	// 第一行高度设置为low.
	int defDbHeight = m_pBook->GetWoStake()->getSetting()->getDefaultGridViewHeight();
	etRANGE.SetRows(sheetIDX, sheetIDX, 0, 0);
	ks_stdptr<etoldapi::Range> spApiRange1stRow;
	VS(spEtWorksheet->GetRangeByData(&etRANGE, &spApiRange1stRow));
	spApiRange1stRow->put_RowHeight(KComVariant(defDbHeight / 20));

	// 设置标题行
	ks_stdptr<ICoreListObjects> spListObjects;
	pEtSheet->GetExtDataItem(edSheetListObjects, (IUnknown **)&spListObjects);
	ks_stdptr<ICoreListObject> spListObject;
	if (spListObjects)
		spListObjects->GetItem(0, &spListObject);
	if (spListObject)
		spListObject->SetStyleOption(TSO_HEADER_ROW, TRUE);
	return S_OK;
}

bool Db2XlsxExporter::isPersonalVisible(IDBSheetView *pView, KEtRevisionContext* ctx)
{
    UINT count = pView->GetPersonalViewUserIdsCount();
    if (0 == count)
        return true;

    for (UINT i = 0; i < count; ++i)
    {
       PCWSTR userid = pView->GetPersonalViewUserId(i);
       if (userid && xstrcmp(userid, ctx->getUser()->userID()) == 0)
          return true;
    }

    return false;
}

HRESULT Db2XlsxExporter::setHyperlink(_Worksheet* pEtSheet, RANGE &etRg, const_token_ptr pToken, IDbField* pField, IKHyperlinks* pEtHyperlinks)
{
	if (pEtHyperlinks == nullptr)
		return S_FALSE;

	if (pField->GetType() != Et_DbSheetField_Url)
		return E_FAIL;

	if (!alg::const_handle_token_assist::is_type(pToken))
		return S_FALSE;

	alg::const_handle_token_assist chta(pToken);
	alg::TOKEN_HANDLE handle = chta.get_handle();
	if (!handle)
		return S_FALSE;

	// 超链接
	if (chta.get_handleType() == etexec::ET_HANDLE_DBHYPERLINK)
	{
		ks_bstr bstrCellValue;
		ks_bstr bstrAddress;
		ks_stdptr<IDbHyperlinkHandle> spHyperlinkHandle = handle->CastUnknown();
		ks_stdptr<IDbField_Hyperlink> spFieldHyperlink = pField;
		if (spFieldHyperlink->GetHyperlinkMode() == DbSheet_Hyperlink_Url_Mode)
			bstrCellValue.assign(spHyperlinkHandle->GetDisplayText());
		else
			bstrCellValue.assign(spFieldHyperlink->GetDisplayText());
		bstrAddress.assign(spHyperlinkHandle->GetAddress());
		/**
		 * 导出超链接数据：原先调用KETHyperlinks::Add超级慢【5w行纯超链接数据导出超时】
		 * 1）etoldapi::Range的put_Formula和put_Style存在undo的事务，导出场景不需要。
		 * 2）KPaneUpdateParam自动更新与WO无关，与导出场景无关。
		 * 3）其他无关代码逻辑也不需要。
		 * 只需要创建超链接，给单元格设置超链接和样式即可【19w行纯超链接数据导出11.8s】
		 */
		ks_stdptr<etoldapi::Range> spEtApiRange;
		VS(pEtSheet->GetRangeByData(&etRg, &spEtApiRange));
		ks_stdptr<IKHyperlink> spEtHyperlink;
		VS(pEtHyperlinks->New(&spEtHyperlink, &etRg));
		spEtHyperlink->SetAddress(bstrAddress);
		ks_stdptr<IRangeInfo> spEtRangeInfo = spEtApiRange;
		spEtRangeInfo->SetFormula(bstrCellValue, &etRg, RS_A1, TRUE);
		ks_stdptr<IAppCoreRange> spEtAppCoreRange;
		spEtRangeInfo->GetAppCoreRange(&spEtAppCoreRange);
		spEtAppCoreRange->SetStyle(krt::kCachedTr("et_et_styles", "Hyperlink", "STR_STYLE_HYPERLINK", -1));
	}
	return S_OK;
}

HRESULT Db2XlsxExporter::setHyperlinkRuns(_Worksheet* pEtSheet, RANGE &etRg, const_token_ptr pToken, IDbField* pField, IKHyperlinks* pEtHyperlinks)
{
	if (pEtHyperlinks == nullptr)
		return S_FALSE;

	if (pField->GetType() != Et_DbSheetField_Lookup)
		return E_FAIL;

	if (!alg::const_handle_token_assist::is_type(pToken))
		return S_FALSE;

	alg::const_handle_token_assist chta(pToken);
	alg::TOKEN_HANDLE handle = chta.get_handle();
	if (!handle)
		return S_FALSE;

	struct LinkRun
	{
		UINT pos;
		UINT length;
		LINKRUNSTYPE type;
		PCWSTR address;
		PCWSTR subAddr;
		PCWSTR screenTip;
	};
	std::vector<LinkRun> linkRuns;
	UINT pos = 0;
	std::function<void(const_token_ptr)> proc = [&](const_token_ptr pToken)
	{
		if (!alg::const_handle_token_assist::is_type(pToken))
			return;

		alg::const_handle_token_assist chta(pToken);
		alg::TOKEN_HANDLE handle = chta.get_handle();
		if (handle && chta.get_handleType() == etexec::ET_HANDLE_TOKENARRAY)
		{
			const IDbTokenArrayHandle* pArray = chta.get_handle()->CastArray();
			for (size_t i = 0; i < pArray->GetCount(); ++i)
			{
				const_token_ptr pItem = nullptr;
				pArray->Item(i, &pItem);
				if (alg::const_handle_token_assist::is_type(pItem))
				{
					alg::const_handle_token_assist ichta(pItem);
					alg::TOKEN_HANDLE iHandle = ichta.get_handle();
					if (iHandle && ichta.get_handleType() == etexec::ET_HANDLE_DBHYPERLINK)
					{
						ks_stdptr<IDbHyperlinkHandle> spHyperlinkHandle = iHandle->CastUnknown();
						LinkRun run;
						run.length = xstrlen(spHyperlinkHandle->GetDisplayText());
						run.address = spHyperlinkHandle->GetAddress();
						run.screenTip = __X("");
						run.subAddr = __X("");
						run.type = LRT_URL;
						run.pos = pos;
						pos += run.length;
						++pos;
						linkRuns.push_back(run);
					}
				}
				proc(pItem);
			}
		}
	};
	proc(pToken);
	if (linkRuns.size() == 0)
		return S_FALSE;

	ks_stdptr<IHyperlinkRuns> spHyperlinkRuns;
	pEtHyperlinks->NewHyperlinkRuns(&spHyperlinkRuns);
	for (size_t i = 0; i < linkRuns.size(); ++i)
	{
		UINT pos = linkRuns[i].pos;
		UINT length = linkRuns[i].length;
		ks_stdptr<IHyperlinkProperty> spHyperlinkProperty;
		ks_stdptr<IHyperlinkRunAddress> spLinkRunAddress;
		VS(_appcore_CreateObject(CLSID_KHyperlinkRunAddress, IID_IHyperlinkRunAddress, (void**)&spLinkRunAddress));
		const LinkRun& curRun = linkRuns[i];
		spLinkRunAddress->Init(curRun.address, curRun.subAddr, curRun.screenTip, curRun.type);
		spHyperlinkProperty = spLinkRunAddress;
		spHyperlinkRuns->AddRun(pos, length, spHyperlinkProperty, nullptr);
	}
	pEtHyperlinks->NewHyperlinkWithRuns(&etRg, spHyperlinkRuns, nullptr);
	return S_OK;
}

void Db2XlsxExporter::getExportAttachmentCnt(const_token_ptr pToken, UINT& attachmentCnt)
{
	alg::const_handle_token_assist chta(pToken);
    if (!chta)
        return;
    if (chta.get_handleType() == alg::ET_HANDLE_DBATTACHMENT)
    {
		ks_stdptr<IDbAttachmentHandle> spDbAttachmentHandle = chta.get_handle()->CastUnknown();
		if (!spDbAttachmentHandle)
			return;
		if (!m_exportCompressedBundle)
		{
			if (pictureTypes.find(spDbAttachmentHandle->GetContentType()) != pictureTypes.end())
				attachmentCnt++;
			return;
		}
		attachmentCnt++;
	}

 	else if (chta.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
    {
        const IDbTokenArrayHandle* handle = chta.get_handle()->CastArray();
        const UINT count = handle->GetCount();
        for (UINT i = 0; i < count; ++i)
        {
			const_token_ptr pItem = nullptr;
			handle->Item(i, &pItem);
            getExportAttachmentCnt(pItem, attachmentCnt);
        }
    }
}

bool Db2XlsxExporter::isExceedMaxColumns(const IDBIds* pRecords, const IDBIds* pFields, IDBSheetOp* pSheetOp, std::map <EtDbId, UINT>& fieldAttachmentCnt)
{
	BMP_PTR pBMP = m_pEtWorkBook->GetBook()->GetBMP();
	INT32 maxCols = pBMP->cntCols; //获取最大列数
	int fieldsCnt = pFields->Count();
	int totalAttachmentCnt = 0;
	for (int i = 0; i < fieldsCnt; ++i)
	{
		EtDbId fieldId = pFields->IdAt(i);
		ks_stdptr<IDbField> spField;
		HRESULT hr = pSheetOp->GetFieldsManager()->GetField(fieldId, &spField);
		if (FAILED(hr))
			continue;
		UINT maxFieldColCnt = 0;
		ET_DbSheet_FieldType fieldType = spField->GetType();
		if (fieldType != Et_DbSheetField_Attachment)
			continue;
		for (int j = 0, recordsCnt = pRecords->Count(); j < recordsCnt; ++j)
		{
			EtDbId recId = pRecords->IdAt(j);
			const_token_ptr pToken = nullptr;
			hr  = pSheetOp->GetValueToken(recId, fieldId, &pToken);
			if (FAILED(hr) || !pToken || !alg::const_handle_token_assist::is_type(pToken))
				continue;
			UINT attachmentCnt = 0;
			getExportAttachmentCnt(pToken, attachmentCnt);
			maxFieldColCnt = std::max(maxFieldColCnt, attachmentCnt);
		}
		fieldAttachmentCnt.emplace(std::make_pair(fieldId, maxFieldColCnt));
		totalAttachmentCnt += maxFieldColCnt-1;
	}
	return (fieldsCnt + totalAttachmentCnt > maxCols);  
}

HRESULT Db2XlsxExporter::setNewAttachmentCol(_Worksheet* pEtSheet, IDbField* pField, UINT needColCnt, int& attachmentColPos)
{
	//附件列有多个附件时向右插入多列
	KComVariant var;
	var.AssignDouble(etShiftToRight);
	ISheet* pSheet = pEtSheet->GetSheet();
	RANGE etRANGE(pSheet->GetBMP());
	IDX sheetIdx = INVALIDIDX;
	pSheet->GetIndex(&sheetIdx);
	etRANGE.SetCols(sheetIdx, sheetIdx, attachmentColPos+1 , attachmentColPos + needColCnt);
	ks_stdptr<etoldapi::Range> spEtApiCellRange;
	VS(pEtSheet->GetRangeByData(&etRANGE, &spEtApiCellRange));
	HRESULT hr = spEtApiCellRange->Insert(var, KComVariant());
	if (FAILED(hr))
		return hr;

	for (UINT i = 0; i <= needColCnt; i++)
	{
		//设置新插入列的表头
		ks_wstring colName = pField->GetName();
		colName.AppendFormat(__X("_%d") , i+1);
		etRANGE.SetCell(sheetIdx, 0, attachmentColPos+i);
		m_spEtBookOp->SetCellText(etRANGE, colName.c_str(), cvoForceText);
	}
	attachmentColPos = attachmentColPos + needColCnt;
	return hr;

}

HRESULT Db2XlsxExporter::setAttachmentHyperlink(_Worksheet* pEtSheet, RANGE &etRANGE, IKHyperlinks* pEtHyperlinks, const ks_wstring& fieldName, const ks_wstring& cellValue) 
{ 
	ks_wstring address = fieldName + __X("/") + cellValue;
	ks_stdptr<etoldapi::Range> spEtRange;
	VS(pEtSheet->GetRangeByData(&etRANGE, &spEtRange));
	ks_stdptr<IKHyperlink> spEtHyperlink;
	VS(pEtHyperlinks->New(&spEtHyperlink, &etRANGE));

	spEtHyperlink->SetAddress(address.c_str());
	ks_stdptr<IRangeInfo> spEtRangeInfo = spEtRange;
	spEtRangeInfo->SetFormula(cellValue.c_str(), &etRANGE, RS_A1, TRUE);
	ks_stdptr<IAppCoreRange> spEtAppCoreRange;
	spEtRangeInfo->GetAppCoreRange(&spEtAppCoreRange);
	spEtAppCoreRange->SetStyle(krt::kCachedTr("et_et_styles", "Hyperlink", "STR_STYLE_HYPERLINK", -1));
	return S_OK;
}

void Db2XlsxExporter::setCellValueForAttachment(const_token_ptr pToken, _Worksheet* pEtSheet, IKHyperlinks* pEtHyperlinks, RecordInfo recordInfo, int index, UINT& insertIndex)
{

	alg::const_handle_token_assist hta(pToken);
    if (!hta)
        return;
    if (hta.get_handleType() == alg::ET_HANDLE_DBATTACHMENT)
    {
		HRESULT hr = S_OK;
		ISheet* pSheet = pEtSheet->GetSheet();
		RANGE etRANGE(pSheet->GetBMP());
		IDX sheetIdx = INVALIDIDX;
		pSheet->GetIndex(&sheetIdx);
        ks_stdptr<IDbAttachmentHandle> spDbAttachmentHandle = hta.get_handle()->CastUnknown();
        if(!spDbAttachmentHandle)
			return;
		if (!m_exportCompressedBundle && pictureTypes.find(spDbAttachmentHandle->GetContentType()) == pictureTypes.end())
			return;
		AttachmentInfo attachmentInfo;
		getAttachmentFileData(recordInfo.sheetId, recordInfo.fieldId, recordInfo.recordId, index, attachmentInfo);
		etRANGE.SetCell(sheetIdx, recordInfo.rowPos + 1, recordInfo.colPos + insertIndex);
		if (!attachmentInfo.isSuccess) //导出失败
		{
			m_spEtBookOp->SetCellText(etRANGE, attachmentInfo.fileName.c_str(), cvoForceText);
			insertIndex++;
			return;
		}

		//导出压缩包时,非图片附件设置本地超链接
		if (m_exportCompressedBundle && !attachmentInfo.isImage)
		{
			hr = setAttachmentHyperlink(pEtSheet, etRANGE, pEtHyperlinks, attachmentInfo.fieldName, attachmentInfo.fileName);
			if (FAILED(hr))
				return;
			insertIndex++;
			return;
		}
		//插入图片
		if (attachmentInfo.attachmentWidth == 0  || attachmentInfo.attachmentHeight == 0)
			return;
		float width = static_cast<float>(attachmentInfo.attachmentWidth);
		float height = static_cast<float>(attachmentInfo.attachmentHeight);

		ks_wstring uuid;
		ks_stdptr<IKShape> spShape;
		hr = pEtSheet->InsertCellPicture(etRANGE.RowFrom(), etRANGE.ColFrom(), attachmentInfo.filePath.c_str(), uuid, nullptr, width, height, FALSE, &spShape);
		if (FAILED(hr))
			return;
		insertIndex++;
	}
	else if (hta.get_handleType() == alg::ET_HANDLE_TOKENARRAY)
	{
		const IDbTokenArrayHandle* handle = hta.get_handle()->CastArray();
        const UINT count = handle->GetCount();
        for (UINT i = 0; i < count; ++i)
        {
            const_token_ptr pItem = nullptr;
            handle->Item(i, &pItem);
        	setCellValueForAttachment(pItem, pEtSheet, pEtHyperlinks, recordInfo, i, insertIndex);
        }
	}
}

DbViewExporter::DbViewExporter(KEtWorkbook *wwb, _Workbook *pEtWorkBook, UINT sheetId, EtDbId viewID)
	: Db2XlsxExporter(wwb, pEtWorkBook), m_dbSheetID(sheetId), m_viewID(viewID)
{
}

DbViewExporter::DbViewExporter(_Workbook* wwb, _Workbook* pEtWorkBook, UINT sheetId, EtDbId viewID)
	: Db2XlsxExporter(wwb, pEtWorkBook), m_dbSheetID(sheetId), m_viewID(viewID)
{
}

HRESULT DbViewExporter::Init()
{
	HRESULT hr = Db2XlsxExporter::Init();
	if (FAILED(hr))
		return hr;

	// 获取源db sheet
	ks_stdptr<ISheet> spDbSheet;
	hr = DbSheet::GetDbSheet(m_pBook, m_dbSheetID, &spDbSheet);
	if (FAILED(hr))
		return hr;
	m_spDbSheetOp.clear(); // clear 避免内存泄漏
	VS(DbSheet::GetDBSheetOp(spDbSheet.get(), &m_spDbSheetOp));
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	VS(DbSheet::GetDBSheetViews(spDbSheet.get(), &spDbSheetViews));

	spDbSheetViews->GetItemById(m_viewID, &m_spView);
	if (!m_spView)
		return E_DBSHEET_VIEW_NOT_FOUND;
	if (m_spView->GetType() == et_DBSheetView_Form)
	{
		WOLOG_WARN << "Do not support export form view by now.";
		return E_INVALID_REQUEST;
	}

	return S_OK;
}

int DbViewExporter::getRowHeight() const
{
	// 获取指向表格视图的指针 (如果是的话)
	if (m_spView->GetType() == et_DBSheetView_Grid)
	{
		ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = m_spView;
		int height = 0;
		HRESULT hr = spDbSheetViewGrid->GetRecordsHeight(&height);
		if (SUCCEEDED(hr) && height != 0)
			return height;
	}
	return Db2XlsxExporter::getRowHeight();
}
int DbViewExporter::getColWidth(EtDbId fieldId, IDBSheetOp* sheetOp) const
{
	// 获取指向表格视图的指针 (如果是的话)
	if (m_spView->GetType() == et_DBSheetView_Grid)
	{
		ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = m_spView;
		int width = 0;
		HRESULT hr = spDbSheetViewGrid->GetFieldWidth(fieldId, &width);
		if (SUCCEEDED(hr) && width != 0)
			return width;
	}
	return Db2XlsxExporter::getColWidth(fieldId, sheetOp);
}

HRESULT DbViewExporter::Exec()
{
	return ExecToEtSheet(0);
}

HRESULT DbViewExporter::ExecToEtSheet(IDX destEtSheetIdx)
{
	PCWSTR pExpectedSheetName = m_spView->GetName();
	if (m_spView->IsDefaultView())
		m_spView->GetSheetOp()->GetName(&pExpectedSheetName);
    
    const IDBIds* pVisibleIds = nullptr;
    ks_stdptr<IDBIds> spVisibleRecIds;
    if (m_spView->GetType() == et_DBSheetView_Kanban)
    {
        ks_stdptr<IDBSheetView_Kanban> spKanbanView = m_spView;
        spKanbanView->GetOrderedVisibleRecIds(&spVisibleRecIds);
        if (spVisibleRecIds)
            pVisibleIds = spVisibleRecIds;
    }
    else
    {
        pVisibleIds = m_spView->GetVisibleRecords();
    }
   
	return dbViewExportData(destEtSheetIdx, pExpectedSheetName, pVisibleIds, m_spView->GetVisibleFields(), m_spDbSheetOp, m_spView);
}

DbData2XlsxExporter::DbData2XlsxExporter(KEtWorkbook* wwb, _Workbook* pEtWorkBook, KEtRevisionContext* pCtx)
	: Db2XlsxExporter(wwb, pEtWorkBook, pCtx)
{
}

HRESULT DbData2XlsxExporter::Exec()
{
	int sheetCnt = 0, exportIdx = 0;
	RemoveNoPermissionDbSheetData(m_pDbWorkBook);
	m_pBook->GetSheetCount(&sheetCnt);

	for (int i = 0; i < sheetCnt; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		m_pBook->GetSheet(i, &spSheet);
		ASSERT(spSheet);

		SHEETSTATE state;
		spSheet->GetVisible(&state);
		if (state != ssVisible || !spSheet->IsDbSheet())
			continue;

		ks_stdptr<IDBSheetOp> sheetOp;
		VS(DbSheet::GetDBSheetOp(spSheet.get(), &sheetOp));
		ks_stdptr<IDBSheetViews> spDbSheetViews;
		VS(DbSheet::GetDBSheetViews(spSheet.get(), &spDbSheetViews));

		ks_bstr strName;
		ks_stdptr<_Worksheet> spDbWorkSheet = m_pDbWorkBook->GetWorksheets()->GetSheetItem(i);
		spDbWorkSheet->get_Name(&strName);

		ks_stdptr<IDBSheetView> spValidView = nullptr;
		const IDBIds* records = nullptr;
		const IDBIds* fields = nullptr;

		for (int j = 0; j < spDbSheetViews->GetSize(Et_DBSheetViewUse_ForDb); ++j)
		{
			ks_stdptr<IDBSheetView> spView;
			spDbSheetViews->GetItemAt(j, Et_DBSheetViewUse_ForDb, &spView);
			auto viewType = spView->GetType();

			if (!isPersonalVisible(spView, m_pCtx))
				continue;

			if (viewType == et_DBSheetView_Form || viewType == et_DBSheetView_Query) 
				continue;
			spValidView = spView;
			if (viewType == et_DBSheetView_Kanban)
			{
				records = sheetOp->GetAllRecords();
				fields = spView->GetOrderFields();
			}
			else
			{
				records = spView->GetOrderRecords();
				fields = spView->GetOrderFields();
			}
			break;
		}

		if (!spValidView)
		{
			records = sheetOp->GetAllRecords();
			fields = sheetOp->GetAllFields();
		}

		HRESULT hr = dbViewExportData(exportIdx, strName.c_str(), records, fields, sheetOp, spValidView, false);
		if (FAILED(hr))
			return hr;

		++exportIdx;
	}
	return S_OK;
}

DbSheetViewDataToEtSheetExporter::DbSheetViewDataToEtSheetExporter(wo::KEtWorkbook* wwb, UINT dbSheetStId, EtDbId viewID)
	:DbViewExporter(wwb->GetCoreWorkbook(), wwb->GetCoreWorkbook(), dbSheetStId, viewID)
	,m_resultSheetStId(0)
{

}

DbSheetViewDataToEtSheetExporter::DbSheetViewDataToEtSheetExporter(_Workbook* wwb, UINT dbSheetStId, EtDbId viewID)
	:DbViewExporter(wwb, wwb, dbSheetStId, viewID)
	,m_resultSheetStId(0)
{

}

bool DbSheetViewDataToEtSheetExporter::_CheckDbSheetIsValid()
{
	IDX iDbSheetIdx = INVALIDIDX;
	m_pBook->STSheetToRTSheet(GetDbSheetStId(), &iDbSheetIdx);

	long sheetCount = 0;
	ks_stdptr<Worksheets> spWorksheets = m_pDbWorkBook->GetWorksheets();
	spWorksheets->get_Count(&sheetCount);
	if(iDbSheetIdx < 0 || iDbSheetIdx >= sheetCount)
	{
		WOLOG_ERROR << "[_CheckDbSheetIsValid] sheetIdx < 0 || sheetIdx >= sheetCount error";
		return false;
	}

	ks_stdptr<_Worksheet> spDbWorkSheet = spWorksheets->GetSheetItem(iDbSheetIdx);
	if(!spDbWorkSheet)
	{
		WOLOG_ERROR << "[_CheckDbSheetIsValid] spDbWorkSheet empty!";
		return false;
	}

	ISheet* pDbSheet = spDbWorkSheet->GetSheet();
	if (!pDbSheet)
	{
		WOLOG_ERROR << "[_CheckDbSheetIsValid] pDbSheet empty!";
		return false;
	}

	if(!pDbSheet->IsDbSheet())
	{
		WOLOG_ERROR << "[_CheckDbSheetIsValid] param sheetIdx is not dbSheet!";
		return false;
	}

	SHEETSTATE state;
	pDbSheet->GetVisible(&state);
	if (state != ssVisible)
	{
		WOLOG_ERROR << "[_CheckDbSheetIsValid] param sheetIdx is not visible!";
		return false;
	}
	return true;
}

HRESULT DbSheetViewDataToEtSheetExporter::CopySheetName(_Worksheet* pWorksheet, PCWSTR name)
{
	ASSERT(pWorksheet && name);
	IKWorksheets* pWorksheets = m_pEtWorkBook->GetWorksheets();
	ks_bstr newName;
	HRESULT hr = GetValidSheetName(pWorksheets, pWorksheet, name, &newName);
	if (FAILED(hr))
		return hr;
	return pWorksheet->put_Name(newName);
}

HRESULT DbSheetViewDataToEtSheetExporter::Exec()
{
	if(!_CheckDbSheetIsValid())
	{
		WOLOG_ERROR << "[DbSheetViewDataToEtSheetExporter] src dbsheet is unvalid!";
		return E_FAIL;
	}

	//创建一个新的et sheet
	KComVariant vBefore;
	KComVariant vAfter = util::getLastSheetIdx(m_pBook) + 1; // 供 oldapi::Worksheets使用，其计数从1开始
	KComVariant vCount = 1;
	KComVariant vType(xlWorksheet);
	
	ks_stdptr<Worksheets> spWorksheets = m_pDbWorkBook->GetWorksheets();
	
	ks_stdptr<IKCoreObject> spObj;
	spWorksheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid);
	ks_stdptr<_Worksheet> spNewEtWorkSheet = spObj;
	if (!spNewEtWorkSheet)
	{
		WOLOG_ERROR << "[DbSheetViewDataToEtSheetExporter] create worksheet failed!";
		return E_FAIL;
	}
	
	ISheet* pNewEtSheet = spNewEtWorkSheet->GetSheet();
	IDX iDestEtSheetIdx = INVALIDIDX;
	pNewEtSheet->GetIndex(&iDestEtSheetIdx);

	//导数据
	HRESULT hr = ExecToEtSheet(iDestEtSheetIdx);
	if (FAILED(hr))
		return hr;
	m_resultSheetStId = pNewEtSheet->GetStId();

	return S_OK;
}


KSheet2XlsxExporter::KSheet2XlsxExporter(KEtWorkbook* srcBook, _Workbook* pTarWorkBook, KEtRevisionContext* pCtx) 
	: Db2XlsxExporter(srcBook, pTarWorkBook, pCtx)
	, m_pSrcBook(srcBook->GetCoreWorkbook())
	, m_pTarWorkBook(pTarWorkBook)
{

}

KSheet2XlsxExporter::KSheet2XlsxExporter(_Workbook *pDbWorkBook, _Workbook *pEtWorkBook, KEtRevisionContext* pCtx)
    : Db2XlsxExporter(pDbWorkBook, pEtWorkBook, pCtx)
	, m_pSrcBook(pDbWorkBook)
	, m_pTarWorkBook(pEtWorkBook)
{
}

HRESULT KSheet2XlsxExporter::Exec()
{
	// 获取源表sheets
	IKWorksheets* pSrcWorksheets = m_pSrcBook->GetWorksheets();
	int count = pSrcWorksheets->GetSheetCount();
	std::vector<int> srcSheetIdx;
	for (int i = 0; i < count; ++i)
	{
		ISheet* pSrcSheet = pSrcWorksheets->GetSheetItem(i)->GetSheet();
		if (pSrcSheet->IsGridSheet() || pSrcSheet->IsOldDbSheet() || pSrcSheet->IsDbSheet())
			srcSheetIdx.emplace_back(i);
	}

	int srcSheetCnt = srcSheetIdx.size();
	KVariantArrayDim1 varCoppingSheets(srcSheetCnt);
	for (int i = 0; i < srcSheetCnt; ++i)
		varCoppingSheets.SetAt(i, KComVariant(srcSheetIdx[i] + 1));

	ks_stdptr<IKCoreObject> spCoreObjCopySheets;
	HRESULT hr = pSrcWorksheets->GetSheet(varCoppingSheets, &spCoreObjCopySheets, stUnknown);
	if (FAILED(hr))
		return hr;
	ks_stdptr<Sheets> spCopySheets = spCoreObjCopySheets;

	ks_stdptr<Worksheets> spTarWorkSheets;
	m_pTarWorkBook->get_Worksheets(&spTarWorkSheets);
	ks_stdptr<IKCoreObject> spCoreObjTarSheet;
	spTarWorkSheets->get_Item(KComVariant(1), &spCoreObjTarSheet);
	VARIANT varAfter = KComVariant(spCoreObjTarSheet);
	// 延后计算时机，减少重复计算
	// 修复815359. KCachedSupBook::BeginUpdate对跨book引用也会生效, 不仅限于importrange.
	util::CalcBatchUpdate batchUpdate(m_pTarWorkBook->GetBook()->LeakOperator(), m_pTarWorkBook->GetBook()->GetWoStake()->HasImportrangeFuncs());
	hr = spCopySheets->Copy(KComVariant(), varAfter);
	if (FAILED(hr))
		return hr;

	// 删除第一张空sheet
	int tarSheetCnt = 0;
	m_pTarWorkBook->GetBook()->GetSheetCount(&tarSheetCnt);
	if (tarSheetCnt > 1)
	{
		ks_stdptr<IKWorksheet> spDelSheet = spCoreObjTarSheet;
		spDelSheet->DeleteDirectly();
	}

	for (int i = 0; i < srcSheetCnt; ++i)
	{
		ks_stdptr<ISheet> spSheet;
		m_pSrcBook->GetBook()->GetSheet(srcSheetIdx[i], &spSheet);
		ASSERT(spSheet);
		if (!spSheet->IsDbSheet())
			continue;

		ks_stdptr<IDBSheetOp> sheetOp;
		VS(DbSheet::GetDBSheetOp(spSheet.get(), &sheetOp));
		ks_bstr strName;
		ks_stdptr<_Worksheet> spDbWorkSheet = m_pSrcBook->GetWorksheets()->GetSheetItem(srcSheetIdx[i]);
		spDbWorkSheet->get_Name(&strName);
		ks_stdptr<IDBSheetViews> spViews;
		sheetOp->GetDbSheetViews(&spViews);
		// AS中defaultView和前端看到的是一致的
		IDBSheetView* pView = spViews->GetDefaultView();
		HRESULT hr = dbViewExportData(i, strName.c_str(), pView->GetOrderRecords(), pView->GetOrderFields(), sheetOp, pView, false);
		if (FAILED(hr))
			return hr;
	}

	IKWorksheets* pTarWorksheets = m_pTarWorkBook->GetWorksheets();
	int tarSheetCount = pTarWorksheets->GetSheetCount();
	for(int i = 0; i < tarSheetCount; ++i)
	{
		util::QRLabelHelper qrLabelHelper(m_pTarWorkBook, m_pCtx, util::QRLabelHelper::UseType_Export_KSheet2Xlsx, nullptr);
		qrLabelHelper.ConvertQRLabel2CellImg(i);
	}

	BOOKWNDINFO* pBookWndInfo = NULL;
	m_pSrcBook->GetWndInfos()->GetItem(0, &pBookWndInfo);
	if (!pBookWndInfo)
		return S_OK;
	
	IDX activeSheetIdx = pBookWndInfo->nActiveSheet;
	for (int i = 0; i < pBookWndInfo->nActiveSheet; ++i)
	{
		if (activeSheetIdx == INVALIDIDX)
			break;
		ks_stdptr<ISheet> spSheet;
		spSheet = pSrcWorksheets->GetSheetItem(i)->GetSheet();
		ASSERT(spSheet);
		bool bSheetIsTarActive = spSheet->IsGridSheet() || spSheet->IsOldDbSheet() || spSheet->IsDbSheet();
		if (!bSheetIsTarActive)
			activeSheetIdx--;
	}

	if (activeSheetIdx != INVALIDIDX)
		m_pTarWorkBook->GetWndInfos()->SetActiveSheet(0, activeSheetIdx);

	return S_OK;
}

} // namespace wo
