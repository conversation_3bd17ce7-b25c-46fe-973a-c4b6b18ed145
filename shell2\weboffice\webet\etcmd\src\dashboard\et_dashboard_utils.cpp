﻿#include "et_dashboard_utils.h"
#include <etcore/little_alg.h>
#include "pivot_core/pivot_core_x.h"
#include <newchart/src/model/charttype.h>
#include <etchart/kdatasourcerangesdivider.h>
#include <etchart/kdividerchooser.h>
#include "appcore/et_appcore_enum.h"

namespace wo
{
namespace DashBoard
{

int GetDashBoardModuleLimit()
{
	constexpr int maxDashboardModule = 60;
	return maxDashboardModule;
}

HRESULT initDashBoardSheet(IKEtWindow* pEtWindow, _Worksheet* pWorksheet, SHEETTYPE st)
{
	if (!pEtWindow || !pWorksheet)
		return E_FAIL;

	ISheet* pSheet = pWorksheet->GetSheet();
	HRESULT hr = pSheet->UpdateSheetType(st);
	if (FAILED(hr))
		return hr;

	INT32 idxWnd = pEtWindow->GetIndex();
	ISheetWndInfos* wndsInfo = pWorksheet->GetWndInfos();
	ks_stdptr<ISheetWndInfo> wndInfo;
	VS(wndsInfo->GetItem(idxWnd, &wndInfo));
	hr = wndInfo->SetShowGridLines(false);
	if (FAILED(hr))
		return hr;
	hr = wndInfo->SetShowRowColHeader(false);
	if (FAILED(hr))
		return hr;

	// 页面设置
	ks_stdptr<PageSetup> spKPageSetup;
	pWorksheet->get_PageSetup(&spKPageSetup);
	ASSERT(spKPageSetup);

	KComVariant kcomVar(etLandscape);
	hr = spKPageSetup->put_Orientation(kcomVar);
	if (FAILED(hr))
		return hr;
	KComVariant scale(1);
	hr = spKPageSetup->put_FitToPagesWide(scale);
	if (FAILED(hr))
		return hr;
	hr = spKPageSetup->put_FitToPagesTall(scale);
	if (FAILED(hr))
		return hr;
	// preferView
	constexpr ROW rowTo = 50;
	constexpr COL colTo = 19;
	hr = pSheet->SetPreferredView(rowTo, colTo);
	if (FAILED(hr))
		return hr;

	return S_OK;
}

HRESULT SetWebextentionProperty(const VarObj &param, IKWebExtension *pWebExtension, IDBDashBoardDataOp* pDashboardData) 
{
	if (!pWebExtension)
		return E_FAIL;
	HRESULT hr = S_OK;
	if (param.has("propertyRemoveList")) 
	{
		binary_wo::VarObj array = param.get("propertyRemoveList");
		for (int i = 0, length = array.arrayLength(); i < length; ++i) 
		{
			binary_wo::VarObj item = array.at(i);
			if (!item.has("key"))
				continue;
			if (xstrcmp(item.field_str("key"), __X("plugin-custom-kv")) == 0)
				continue;
			hr = pWebExtension->DeleteProperty(item.field_str("key"));
			if (FAILED(hr))
				return hr;
			if (pDashboardData)
                pDashboardData->WebExtensionPropertyChange(pWebExtension->GetWebExtensionKey());
		}
	}
	if (param.has("propertyModifyList")) 
	{
		binary_wo::VarObj array = param.get("propertyModifyList");
		for (int i = 0, length = array.arrayLength(); i < length; ++i) 
		{
			binary_wo::VarObj item = array.at(i);
			if (!item.has("key") || !item.has("value"))
				continue;
			if (xstrcmp(item.field_str("key"),  __X("plugin-custom-kv")) == 0)
				continue;
			hr = pWebExtension->SetProperty(item.field_str("key"), item.field_str("value"));
			if (FAILED(hr))
				return hr;
			if (pDashboardData)
                pDashboardData->WebExtensionPropertyChange(pWebExtension->GetWebExtensionKey());
		}
	}
	return S_OK;
}

HRESULT GetPivotTableFromRange(IBook* pBook, const RANGE& range, pivot_core::IPivotTable** ppTbl)
{
	ks_stdptr<ISheet> spSheet;
	ASSERT(range.SheetFrom() == range.SheetTo());
	pBook->GetSheet(range.SheetFrom(), &spSheet);
	if (!spSheet)
		return E_FAIL;
	return pivot_helper::GetCorePivotTable(spSheet, Range2Rect(range), ppTbl) ? S_OK : S_FALSE;
}

void CalcBoundaryRowColAndSeriesDirection(IBookOp* pBookOp, IKRanges* pRanges, UINT& boundaryRow, UINT& boundaryCol, BOOL& seriesByColumn)
{
    UINT rgCount = 0;
    pRanges->GetCount(&rgCount);
    ASSERT(rgCount == 1);
    const RANGE *range = nullptr;
    pRanges->GetItem(0, nullptr, &range);
    et_sptr<chart::KDataSourceRangesDivider> rangeDivider(chart::KDividerChooser::createDivider(pBookOp, pRanges, chart::ChartTypeColumnClustered));
    CELL inBoundaryCell = {-1, -1};
    chart::RangeDivideResult divideResult;
    if (rangeDivider->divide(sdirAuto, inBoundaryCell, -1, -1, &divideResult))
    {
        seriesByColumn = divideResult.direction == sdirColumn;
        CELL boundaryCell = rangeDivider->getBoundaryCell();
        boundaryRow = boundaryCell.row - range->RowFrom();
        boundaryCol = boundaryCell.col - range->ColFrom();
    }
}

HRESULT SetWorkbookDataSource(IBook* pBook, IKWebExtension* pWebExtension, IRangeInfo* pRangeInfo)
{
	if(!pRangeInfo)
		return E_FAIL;

	ks_stdptr<IWorksheetChart> spChart = pWebExtension;
	if(spChart)
	{
		UINT boundaryRow = 0;
		UINT boundaryCol = 0;
		BOOL seriesByColumn = TRUE;
		ks_stdptr<IBookOp> spBookOp;
		pBook->GetOperator(&spBookOp);
		ks_stdptr<IKRanges> spRanges;
		pRangeInfo->GetIRanges(&spRanges);
		CalcBoundaryRowColAndSeriesDirection(spBookOp, spRanges, boundaryRow, boundaryCol, seriesByColumn);

		spChart->ResetSeries();
		spChart->SetSeriesByColumn(seriesByColumn);
		spChart->SetBoundaryRowCol(boundaryRow, boundaryCol);
	}
	return S_OK;
}

HRESULT SetPivotTableDataSource(IKWebExtension* pWebExtension)
{
	ks_stdptr<IWorksheetChart> spChart = pWebExtension;
	if(spChart)
	{
		spChart->ResetSeries();
		spChart->SetSeriesByColumn(TRUE);
		spChart->SetShowHiddenData(FALSE);
	}
	return S_OK;
}

bool IsDashboardChart(IKWebExtension* pWebExtension)
{
	if(pWebExtension)
		return pWebExtension->GetWebShapeType() == WET_DbDataSource;
	return false;
}

HRESULT SetEtDashboardChartSetting(const VarObj& param, IKWebExtension* pWebExtension)
{
	HRESULT hr = SetWebextentionProperty(param, pWebExtension);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IWorksheetChart> spChart = pWebExtension;
	SetWebextentionExtData(param, spChart);
	return hr;
}

HRESULT SetWebextentionExtData(const VarObj& param, IWorksheetChart* pChart)
{
	if(!pChart)
		return E_FAIL;

	HRESULT hr = E_FAIL;
	IEncodeDecoder* pDecoder = _appcore_GainEncodeDecoder();
	if (param.has("ascending"))
	{
		hr = pChart->SetSortAscending(param.field_bool("ascending"));
		if (FAILED(hr))
			return hr;
	}
	if (param.has("sortType"))
	{
		ET_DBSheet_ChartSortType sortType = DbSheet_Chart_ST_NoSort;
		hr = pDecoder->DecodeDbChartSortType(param.field_str("sortType"), &sortType);
		if (FAILED(hr))
			return hr;

		hr = pChart->SetSortType(sortType);
		if (FAILED(hr))
			return hr;
    }
	if (param.has("showHiddenData"))
	{
		hr = pChart->SetShowHiddenData(param.field_bool("showHiddenData"));
		if (FAILED(hr))
			return hr;
	}
	if (param.has("seriesByColumn"))
	{
		hr = pChart->SetSeriesByColumn(param.field_bool("seriesByColumn"));
		if (FAILED(hr))
			return hr;
	}
	if (param.has("seriesOrder"))
	{
		std::vector<UINT> seriesOrder;
		binary_wo::VarObj array = param.get("seriesOrder");
		for (int i = 0, length = array.arrayLength(); i < length; ++i)
			seriesOrder.push_back(array.item_uint32(i));
		hr = pChart->SetSeriesOrder(seriesOrder.data(), seriesOrder.size());
		if (FAILED(hr))
			return hr;
	}
	if (param.has("seriesToRemove"))
	{
		hr = pChart->DeleteSeries(param.field_uint32("seriesToRemove"));
		if (FAILED(hr))
			return hr;
    }
	if (param.has("seriesToHide"))
	{
		hr = pChart->HideSeries(param.field_uint32("seriesToHide"));
		if (FAILED(hr))
			return hr;
	}
	if (param.has("seriesToShow"))
	{
		hr = pChart->ShowSeries(param.field_uint32("seriesToShow"));
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

} // namespace DashBoard

} // namespace wo
