﻿#ifndef __SHELL2_WEBET_KSIGNAL_BINNODE_H__
#define __SHELL2_WEBET_KSIGNAL_BINNODE_H__

#include "kbinreadertravel.h"
#include "ksignalbinwebstr.h"
namespace wo {

enum class BinNodeNameType
{
    kRoot,
    kCommon,
    kVersionsArray,
    kVersionItem,
    kVersionNewObjArray,
    
    kNewObjArray,
    kNewCustomObjArray,
    kNewObjItem,
    
    kVersionOpsArray,
    kVersionOpsObjItem,
    
    kOrgObjsArray,
    kOrgObjItem,
    
    _kCount
};

class BinNode
{
public:
    enum SizeType
    {
        kSelfSize,
        kSize,
    };
    
    BinNode();
    explicit BinNode(BinNode* parent);
    virtual ~BinNode() {}
    BinNode(const BinNode &) = delete;
    BinNode & operator=(const BinNode&) = delete;
    
    void setName(const binary_wo::BuffName & name) { m_name = name; }
    const binary_wo::BuffName & name() const { return m_name; }
    void setSize(int byteSize);
    int size() const { return m_size; }
    int selfSize() const { return m_selfSize; }
    int sizeByType(SizeType tp) const { if (tp == kSelfSize) { return m_selfSize; } return m_size; }
    BinNode * parent() { return m_parent; }
    BinNode * findFistNewObjNode();
    std::string genPath();
    void setNameType(BinNodeNameType type) { m_nameType = type; }
    BinNodeNameType nameType() { return m_nameType; }
    bool isTopObj() const { return m_isTop; }
    void setTopObj() { m_isTop = true; }
    void incItemCnt(int v) { m_itemCnt += v; }
    int itemCnt() const { return m_itemCnt; }
    
    bool isNewObjItem() { return m_nameType == BinNodeNameType::kNewObjItem; }
    bool isOpsObjItem() { return m_nameType == BinNodeNameType::kVersionOpsObjItem; }
    bool isOrgObjItem() { return m_nameType == BinNodeNameType::kOrgObjItem; }
    
    bool isNewObjArray();
    void dispose();
    
    virtual void setObjId(WebID id) { ASSERT(false); }
    virtual WebID objId() { return 0; }
    virtual void setClsName(const WCHAR * wszArray, int len) { ASSERT(false); }
    virtual const WCHAR* clsNameArray() { return nullptr; }
    virtual int clsNameLen() { return 0; }
    
protected:
    binary_wo::BuffName m_name;
    BinNodeNameType m_nameType = BinNodeNameType::kCommon;
    BinNode *m_parent = nullptr;
    std::vector<BinNode*> m_children;
    int m_selfSize = 0; // 不含children的size.
    int m_size = 0;
    int m_itemCnt = 0;
    bool m_isTop = false;
};


class ObjBinNode: public BinNode
{
public:
    explicit ObjBinNode(BinNode *parent): BinNode(parent), m_id(0)
    {}
    void setObjId(WebID id) override { m_id = id; }
    WebID objId() override { return m_id; }
    
protected:
    WebID m_id;
};

class NewObjBinNode: public ObjBinNode
{
public:
    explicit NewObjBinNode(BinNode *parent): ObjBinNode(parent)
    {
        setNameType(BinNodeNameType::kNewObjItem);
    }
    void setClsName(const WCHAR * wszArray, int len) override
    {
        m_clsName.m_wszArray = wszArray;
        m_clsName.m_len = len;
    }
    const WCHAR* clsNameArray() override { return m_clsName.m_wszArray; }
    int clsNameLen() override { return m_clsName.m_len; }
    
protected:
    BinWebStr m_clsName;
};

}

#endif //