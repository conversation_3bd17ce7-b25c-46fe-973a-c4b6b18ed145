#include "formula_idx_generator.h"

namespace wo
{

size_t TokenVectorPersistHasher::operator()(ITokenVectorPersist* fmla) const
{
	if(fmla == NULL)
		return 0;

	size_t res = fmla->GetFlags();
	for (int i = 0, size = fmla->GetSize(); i < size; ++i)
	{
		const_token_ptr pTok = NULL;
		fmla->GetItem(i, &pTok);
		UINT hash = 0;
		GetExecTokenHash(pTok, &hash);
		res = (res << 1) + res + hash;
	}
	return res;
}


bool TokenVectorPersistEuqer::operator()(ITokenVectorPersist* fmlaA, ITokenVectorPersist* fmlaB) const
{
	if(fmlaA == fmlaB)
		return true;

	if(fmlaA == NULL || fmlaB == NULL)
		return false;

	if(fmlaA->GetFlags() != fmlaB->GetFlags() || fmlaA->GetSize() != fmlaB->GetSize())
		return false;

	for (int i = 0, size = fmlaA->GetSize(); i < size; ++i)
	{
		const_token_ptr pTokA = NULL;
		fmlaA->GetItem(i, &pTokA);

		const_token_ptr pTokB = NULL;
		fmlaB->GetItem(i, &pTokB);

		if (IsExecTokenEqual(pTokA, pTokB, true) != S_OK)
			return false;
	}
	return true;
}

size_t FormulaIdxGenerator::gainIdx(ITokenVectorPersist* fmla)
{
	auto res = m_mapFmla.insert(std::make_pair(fmla, m_vecFmla.size()));
	if (res.second)
	{
		m_vecFmla.push_back(fmla);
	}
	return res.first->second;
}

size_t FormulaIdxGenerator::size()
{
	return m_vecFmla.size();
}

ITokenVectorPersist* FormulaIdxGenerator::at(size_t idx)
{
	return m_vecFmla[idx];
}

} //namespace wo