﻿#ifndef __WEBET_DBSHEET_APPEND_DATA_ADAPTER_H__
#define __WEBET_DBSHEET_APPEND_DATA_ADAPTER_H__

#include "wo/et_shared_str.h"

struct IDBProtectionJudgement;

namespace wo
{
	class DbAppendDataAdapter
	{
	public:
		struct DbAppendDataParam
		{
			using AttachmentIdMap = std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher>;
			IDX srcStIdx = INVALIDIDX;
			IDX tarStIdx = INVALIDIDX;
			const AttachmentIdMap* pAttachmentIdMap = nullptr;
		};

		DbAppendDataAdapter(IKWorkbook*, IKWorkbook*, IDBProtectionJudgement*);

		HRESULT Init(const DbAppendDataParam&);
		HRESULT Exec();

		bool GetRowLimitFlag() const { return m_bReachRowLimit; }
		const std::vector<EtDbId>& GetNewRecordIds() const { return m_newRecordIds; }
		static bool IsSupportField(IDbField*, bool isDbSheet = false);
	private:
		HRESULT getSourceCellText(ROW, COL, BSTR*) const;
		bool isEtSourceBlankRow(ROW, const std::vector<COL>&) const;
		std::unordered_map<GlobalSharedString, EtDbId, GlobalSharedStringHasher> getTarFieldMap() const;
		HRESULT execForGridSheet();
		HRESULT execForDbSheet();

		bool m_bReachRowLimit = false;
		std::vector<EtDbId> m_newRecordIds;
		IKWorkbook* m_pSrcWorkbook = nullptr;
		IKWorkbook* m_pTarWorkbook = nullptr;
		IKWorksheet* m_pSrcWorksheet = nullptr;
		IKWorksheet* m_pTarWorksheet = nullptr;
		ISheet* m_pSrcSheet = nullptr;
		ISheet* m_pTarSheet = nullptr;
		IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
		ks_stdptr<IETStringTools> m_spStringTools;
		ks_stdptr<IDBSheetOp> m_spTarDBSheetOp;
		DbAppendDataParam m_param;
	};
} // namespace wo

#endif
