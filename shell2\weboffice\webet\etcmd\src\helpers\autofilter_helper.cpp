#include "autofilter_helper.h"
#include "etcore/little_alg.h"

namespace AutoFilterHelper
{
    IKAutoFilters *GetAutoFilters(IKWorkbook *workbook, const RANGE &rg)
    {
        ks_stdptr<_Worksheet> spSheet = workbook->GetWorksheets()->GetSheetItem(rg.SheetFrom());
        ks_stdptr<ISheet> pCoreSheet = spSheet->GetSheet();

        ks_stdptr<ICoreListObjects> spCoreLstObjs;
        ks_stdptr<ICoreListObject> spCoreLstObj;
        pCoreSheet->GetExtDataItem(edSheetListObjects, (IUnknown**)&spCoreLstObjs);
        if (spCoreLstObjs != NULL)
        {
            spCoreLstObjs->FindFirstItem(rg, &spCoreLstObj);
            if (spCoreLstObj != NULL)
                return spCoreLstObj->GetAutoFilters();
        }
        
        return pCoreSheet->GetAutoFilters();
    }

    // NOTE: 如果给定区域与List Object相交，只返回第一个找到的List Object对应的AutoFilter
    IKAutoFilter *GetUserFilter(IKWorkbook *workbook, PCWSTR filterID, const RANGE &rg,bool bGetDefFilter)
    {
        IKAutoFilters* ptrFilters = GetAutoFilters(workbook, rg);
        if (ptrFilters != NULL)
            return ptrFilters->GetFilter(filterID, bGetDefFilter);
        return NULL;
    }

    // NOTE: 如果给定区域与List Object相交，只返回第一个找到的List Object对应的AutoFilter
    IKAutoFilter *GainUserFilter(IKWorkbook *workbook, PCWSTR filterID, const RANGE &rg)
    {
        IKAutoFilters* ptrFilters = GetAutoFilters(workbook, rg);
        if (ptrFilters != NULL)
            return ptrFilters->GainOwnFilter(filterID);
        return NULL;
    }

    bool GetFilterByRange(_Worksheet* pSheet, PCWSTR filterID, const RANGE& rg, AutoFilter** ppAutoFilter, ListObject** pListObject)
    {
        long nListCount = 0;
        ks_stdptr<ListObjects> spLists;
        pSheet->get_ListObjects(&spLists);
        spLists->get_Count(&nListCount);
        for (long i = 1; i <= nListCount; ++i)
        {
            ks_stdptr<ListObject> spList;
            spLists->get_Item(KComVariant(i), &spList);
            RANGE rgList(pSheet->GetSheet()->GetBMP());
            spList->get_Range(&rgList);
            if (rgList.Intersect(rg).IsValid())
            {
                spList->get_AutoFilter(filterID, ppAutoFilter);
                if (pListObject) *pListObject = spList.detach();
                return true;
            }
        }

        pSheet->get_AutoFilter(filterID, ppAutoFilter);
        return false;
    }

    HRESULT ApiAutoFilterToCore(AutoFilter *pApi, IKAutoFilter **ppCore)
    {
        ks_stdptr<IAutoFilterInfo> spInfo = pApi;
        if(spInfo == NULL)
            return E_FAIL;

        return spInfo->GetCoreAutoFilter(ppCore);
    }

    bool IsFrozenSeria(IKWorksheet* pWorksheet, ROW r1, ROW r2)
    {
        ISheetWndInfos *pWndInfos = pWorksheet->GetWndInfos();
        INT nCount = 0;
        pWndInfos->GetCount(&nCount);
        if (nCount > 0)
        {
            ISheetWndInfo *pWndInfo = nullptr;
            pWndInfos->GetItem(0, &pWndInfo);
            if (pWndInfo && pWndInfo->GetFrozen())
            {
                CELL cellLeftTop;
                pWndInfo->GetLeftTopCell(cellLeftTop);
                ROW top = pWndInfo->GetPanePositionTop();
                ROW frozenRow = cellLeftTop.row + top;

                // 跨越了冻结线
                if (r1 < frozenRow && frozenRow <= r2)
                    return true;
            }
        }

        return false;
    }

    CELL GetFirstResultCell(IKWorksheet* pWorksheet, IKAutoFilter *pAutoFilter, bool *bCrossFrozen)
    {
        ISheet *pSheet = pWorksheet->GetSheet();
        RANGE filterRg(pSheet->GetBMP());
        pAutoFilter->GetFilterRange(&filterRg);
        CELL cell = {filterRg.RowFrom(), filterRg.ColFrom()};

        ks_stdptr<IRowColOp> spRowColOp;
        pSheet->GetCurUserOperator(&spRowColOp);
        ROW firstVisibleResult = -1;
        for (ROW r = filterRg.RowFrom() + 1; r <= filterRg.RowTo(); ++r)
            if (!spRowColOp->GetRowHidden(r))
            {
                firstVisibleResult = r;
                break;
            }
        if (bCrossFrozen) *bCrossFrozen = false;
        if (IsFrozenSeria(pWorksheet, filterRg.RowFrom(), firstVisibleResult))
        {
            if (bCrossFrozen) *bCrossFrozen = true;
            cell.row = firstVisibleResult;
        }

        return cell;
    }

    RECT GetFilterResultRect(IKWorksheet* pWorksheet, IKAutoFilter *pAutoFilter)
    {
        ISheet *pSheet = pWorksheet->GetSheet();
        RANGE buttonRg(pSheet->GetBMP());
        pAutoFilter->GetFilterRange(&buttonRg);
        if (buttonRg.RowFrom() + 1 <= buttonRg.RowTo())
        {
            buttonRg.SetRowFrom(buttonRg.RowFrom() + 1);
            return Range2Rect(buttonRg);
        }
        
        return Rect_CreateScaleNone();
    }

    RECT GetFilterButtonRect(IKWorksheet* pWorksheet, IKAutoFilter *pAutoFilter)
    {
        ISheet *pSheet = pWorksheet->GetSheet();
        RANGE buttonRg(pSheet->GetBMP());
        pAutoFilter->GetFilterRange(&buttonRg);
        buttonRg.SetRowFromTo(buttonRg.RowFrom());

        return Range2Rect(buttonRg);
    }
}