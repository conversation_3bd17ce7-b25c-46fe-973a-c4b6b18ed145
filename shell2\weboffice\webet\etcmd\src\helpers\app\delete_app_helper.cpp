#include "delete_app_helper.h"
#include "etcore/little_alg.h"
#include "util.h"
#include "addTemplateSheets.h"
#include "dbsheet/et2db_exporter.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/webmime_helper.h"

extern Callback* gs_callback;

namespace DeleteAppHelper
{

static BOOL DelDbSheet(wo::KEtWorkbook* pWorkbook, UINT dbSheetStId, EtDbId viewId)
{
	IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
	if(!pBook)
		return FALSE;

	IDX dbSheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(dbSheetStId, &dbSheetIdx);
	
	long sheetCount = 0;
	ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
	spWorksheets->get_Count(&sheetCount);
	if(dbSheetIdx < 0 || dbSheetIdx >= sheetCount)
		return FALSE;
	
	IKWorksheet* pDbWorkSheet = spWorksheets->GetSheetItem(dbSheetIdx);
	if(!pDbWorkSheet)
		return FALSE;
	
	ISheet* pDbSheet = pDbWorkSheet->GetSheet();
	if(!pDbSheet || !pDbSheet->IsDbSheet())
		return FALSE;
	
	if (viewId == INV_EtDbId)
	{
		HRESULT hr = pDbWorkSheet->DeleteDirectly();
		if (FAILED(hr))
			return FALSE;
	}
	else
	{
		ks_stdptr<IDBSheetViews> spViews;
		VS(pDbSheet->GetExtDataItem(edSheetDbViews, (IUnknown**)&spViews));
		ks_stdptr<IDBSheetView> spView;
		spViews->GetItemById(viewId, &spView);
		PCWSTR sharedId = __X("");
		IDBSharedLinkView* pLink = spView->GetSharedLink();
		if (pLink)
			sharedId = pLink->Id();
		HRESULT hr = spViews->DelItem(viewId);
		if (FAILED(hr))
			return FALSE;
	}
	
	return TRUE;
}

void OnAfterDeleteAppSheet(PCWSTR sharedId)
{
	binary_wo::BinWriter binWriter;
	binWriter.addStringField(sharedId, "sharedId");
	wo::IEtRevisionContext *ctx = _etcore_GetEtRevisionContext();
	if (ctx)
		binWriter.addStringField(ctx->getUser()->connID(), "connId");

	binary_wo::BinWriter::StreamHolder shbt = binWriter.buildStream();
	WebSlice resWebSlice = {shbt.get(), binWriter.writeLength()};
	gs_callback->onAfterDeleteAppSheet(&resWebSlice);
}

HRESULT DelOneAppRelatedDbSheet(wo::KEtWorkbook* pWorkbook, UINT relatedDbSheetStId, EtDbId viewId)
{
	IBook *pBook = pWorkbook->GetCoreWorkbook()->GetBook();
	if(!pBook)
		return E_FAIL;

	ks_stdptr<IBookOp> ptrBookOp;
	pBook->GetOperator(&ptrBookOp);
	app_helper::KBatchUpdateCal buc(ptrBookOp);

	//删除对应的db_sheet
	bool bRes = DelDbSheet(pWorkbook, relatedDbSheetStId, viewId);
	if(!bRes)
		return E_FAIL;

	//删除关联
	long sheetCount = 0;
	ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
	spWorksheets->get_Count(&sheetCount);
	for (IDX idx = 0; idx < sheetCount; idx++)
	{
		ks_stdptr<ISheet> spSheet;
        pBook->GetSheet(idx, &spSheet);
        if (!spSheet)
			continue;
		if(spSheet->IsDbSheet())
			continue;
		ks_stdptr<IUnknown> spUnk;
		spSheet->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
		ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
		if(!spSheetAppEtDbRelations || !spSheetAppEtDbRelations->ContainRelatedDbSheetStId(relatedDbSheetStId))
			continue;
		//找到了 直接删除。
		spSheetAppEtDbRelations->PopAppEtDbRelationItem(relatedDbSheetStId);
		break;
	}

	return S_OK;
}

}//end namespace DeleteAppHelper


