﻿#include "etstdafx.h"
#include "workbook.h"
#include "db_dashboard_query_class.h"
#include "db_dashboard_module_mgr_wrapper.h"
#include "src/et_revision_context_impl.h"
#include "etcore/et_core_dbsheet_enum.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "webbase/wo_sa_helper.h"
#include "appcore/et_appcore_shared_link_sheet.h"

namespace wo
{

// ================== QueryDbDashboardModuleContent ==================
QueryDbDashboardModuleContent::QueryDbDashboardModuleContent(KEtWorkbook* wb)
        : ETDbSheetQueryClassBase(wb, __X("db.dashboard.queryModuleContent"))
{
}

HRESULT QueryDbDashboardModuleContent::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt)
{
    UINT sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    if (!param.has("webExtensionKey"))
        return E_INVALIDARG;
    PCWSTR webExtensionKey = param.field_str("webExtensionKey");
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    std::unique_ptr<KDbDashboardModuleWrapper> spModuleWrapper = dashboardModuleMgrWrapper.GetModule(webExtensionKey);
    if (!spModuleWrapper)
        return E_FAIL;

    binary_wo::VarObj lang = param.get_s("lang");
    hr = spModuleWrapper->SerializeContent(param, lang, pCtx, pAcpt);
    if (FAILED(hr))
        return hr;

    return S_OK;
}

HRESULT QueryDbDashboardModuleContent::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
    UINT sheetStId = param.field_uint32("sheetStId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

// ================== QueryDbDashboardModulesContent ==================
QueryDbDashboardModulesContent::QueryDbDashboardModulesContent(KEtWorkbook* wb)
        : ETDbSheetQueryClassBase(wb, __X("db.dashboard.queryModulesContent"))
{
}

HRESULT QueryDbDashboardModulesContent::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt)
{
    UINT sheetStId = param.field_uint32("sheetStId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    binary_wo::VarObj lang = param.get_s("lang");
    binary_wo::VarObj modulesObj = param.get_s("modules");
    KDbDashboardModuleMgrWrapper dashboardModuleMgrWrapper(m_wwb, spWorksheet);
    sa::Leave modules(sa::enterArray(pAcpt, "modules"));
    for (int i = 0, moduleCnt = modulesObj.arrayLength_s(); i < moduleCnt ; ++i)
    {
        binary_wo::VarObj obj = modulesObj.at_s(i);
        PCWSTR webExtensionKey = obj.field_str("webExtensionKey");
        std::unique_ptr<KDbDashboardModuleWrapper> spModuleWrapper = dashboardModuleMgrWrapper.GetModule(webExtensionKey);
        bool isValid = false;
        sa::Leave module(sa::enterStruct(pAcpt, nullptr));
        if (spModuleWrapper)
        {
            sa::Leave result(sa::enterStruct(pAcpt, "result"));
            hr = spModuleWrapper->SerializeContent(param, lang, pCtx, pAcpt);
            if (SUCCEEDED(hr))
                isValid = true;
        }
        pAcpt->addString("webExtensionKey", webExtensionKey);
        pAcpt->addBool("valid", isValid);
    }
    return S_OK;
}

HRESULT QueryDbDashboardModulesContent::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
    UINT sheetStId = param.field_uint32("sheetStId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

// ================== QueryDbDashboardFilterValues ==================
QueryDbDashboardFilterValues::QueryDbDashboardFilterValues(KEtWorkbook* wb)
        : ETDbSheetQueryClassBase(wb, __X("db.dashboard.queryFilterValues"))
{
}

HRESULT QueryDbDashboardFilterValues::Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt)
{
    UINT sheetStId = param.field_uint32("sheetId");
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = m_commonHelper.GetWorksheet(sheetStId, &spWorksheet);
    if (FAILED(hr))
        return hr;

    ISheet* pSheet = spWorksheet->GetSheet();
    if (!pSheet->IsDbDashBoardSheet())
        return E_INVALIDARG;

    BOOL bDateClassify = alg::bool2BOOL(param.has("isDateClassify") && param.field_bool("isDateClassify"));
    KDBFilterValuesSearchType searchType = DBFVST_None;
    PCWSTR searchStr = nullptr;
    binary_wo::VarObj search = param.get_s("search");
    if (search.type() != binary_wo::typeInvalid)
    {
        hr = _appcore_GainEncodeDecoder()->DecodeKDBFilterValuesSearchType(search.field_str("type"), &searchType);
        if (FAILED(hr))
            return hr;
        searchStr = search.field_str("value");
    }

    EtDbId filterId = GetEtDbId(param, "filterId");
    ks_stdptr<IDBDashBoardDataOp> spDbDashboard;
    VS(DbSheet::GetDBDashBoardOp(pSheet, &spDbDashboard));
    ks_stdptr<IDbDashboardFilter> spFilter;
    hr = spDbDashboard->GetFilterMgr()->GetFilter(filterId, &spFilter);
    if (FAILED(hr))
        return hr;

    std::unique_ptr<DbSheet::DisableDbProtectScope> spDisableDbProtectScope;
    // 在分享仪表盘页面，仪表盘筛选器筛选值列表可返回筛选字段对应列的全部数据
    if (IsShareDashboardConn(pCtx->getUser(), sheetStId))
        spDisableDbProtectScope.reset(new DbSheet::DisableDbProtectScope(m_spProtectionJudgement));
    return spFilter->GetFieldValues(searchType, searchStr, bDateClassify, pAcpt);
}

HRESULT QueryDbDashboardFilterValues::CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx)
{
    UINT sheetStId = param.field_uint32("sheetId");
    return m_spProtectionJudgement->CheckSheetCanVisit(sheetStId);
}

bool QueryDbDashboardFilterValues::IsShareDashboardConn(IKUserConn* pUser, UINT sheetId)
{
    if (!pUser)
        return false;

    IBook *pBook = m_wwb->GetCoreWorkbook()->GetBook();
    ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
    pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    if (!spSharedLinkMgr)
        return false;

    PCWSTR shareId = spSharedLinkMgr->GetConnSharedLink(pUser);
    if (!shareId)
        return false;

    ISharedLink* pSharedLink = spSharedLinkMgr->GetItem(shareId);
    if (!pSharedLink)
        return false;

    if (pSharedLink->Type() != SharedLinkType_Sheet)
        return false;

    ks_stdptr<ISharedLinkSheet> spSharedLinkSheet = pSharedLink;
    ISheet* pSheet = spSharedLinkSheet->GetSheet();
    return pSheet && pSheet->IsDbDashBoardSheet() && pSheet->GetStId() == sheetId;
}

}