﻿#ifndef __WEBET_WEBCHART_CONFIG_H__
#define __WEBET_WEBCHART_CONFIG_H__

#include "json/json.h"

class KWebChartConfig
{
public:
    explicit KWebChartConfig(PCWSTR configStr);
    KWebChartConfig(const KWebChartConfig&) = delete;
    KWebChartConfig& operator=(const KWebChartConfig&) = delete;
public:
    bool SerializeBaseInfo(ISerialAcceptor* pAcpt, bool bUsedNewKey);
    void SerializeConfig(ISerialAcceptor* pAcpt, PCWSTR configLayout);
    void SerializeConfigXAxis(ISerialAcceptor* pAcpt);
    void SerializeConfigYAxis(ISerialAcceptor* pAcpt);
    void SerializeConfigLegend(ISerialAcceptor* pAcpt);
    void SerializeConfigAllLabel(ISerialAcceptor* pAcpt);
    void SerializeConfigValueFormat(ISerialAcceptor* pAcpt);
    ET_DBSheet_ChartType GetChartType();
private:
    void SerializeConfigFunnelLayer(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt);
    bool SerializeStringValue(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt);
    bool SerializeStringArray(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt);
    bool SerializeBoolValue(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt);
    bool SerializeDoubleValue(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt);
    bool SerializeUintValue(const Json::Value& object, PCSTR keyName, ISerialAcceptor* pAcpt);
    void SerializeFontStyleValue(const Json::Value& root, ISerialAcceptor* pAcpt);
    void SerializeLayout(const Json::Value& object, ISerialAcceptor* pAcpt);
private:
    std::string m_jsonString;
};

#endif // __WEBET_WEBCHART_CONFIG_H__
