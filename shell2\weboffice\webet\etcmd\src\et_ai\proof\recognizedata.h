﻿// -------------------------------------------------------------------------- //
//	文件名		：	recognizedata.h
//	创建者		：	laiw
//	创建时间	：	2021-02-03 14:50:50
//	功能描述	：	表格识别结果对象类
//
// -------------------------------------------------------------------------- //

#ifndef __ETAI_SMARTDATASERVICE_RECOGNIZE_RECOGNIZEDATA_H__
#define __ETAI_SMARTDATASERVICE_RECOGNIZE_RECOGNIZEDATA_H__

namespace etai
{
template <class element> class Container
{
  public:
    Container(const QJsonArray &data)
    {
        for (int i = 0; i < data.size(); i++)
        {
            m_elements.push_back(new element(data.at(i)));
        }
    };
    virtual ~Container()
    {
        clear();
    };
    Container(const Container &o)
    {
        for (size_t i = 0; i < o.m_elements.size(); i++)
            m_elements.push_back(new element(*o.m_elements.at(i)));
    }
    Container &operator=(const Container &o)
    {
        if (this == &o)
            return *this;

        clear();

        for (size_t i = 0; i < o.m_elements.size(); i++)
            m_elements.push_back(new element(*o.m_elements.at(i)));

        return *this;
    }

  public:
    const element *at(size_t index) const
    {
        ASSERT(index >= 0 && index < size());
        return m_elements.at(index);
    };
    const element *operator[](size_t index) const
    {
        return at(index);
    };
    size_t size() const
    {
        return m_elements.size();
    }

  private:
    void clear()
    {
        for (size_t i = 0; i < m_elements.size(); i++)
            delete m_elements.at(i);
        m_elements.clear();
    }

  private:
    std::vector<element *> m_elements;
};

bool readString(const QJsonObject &obj, const QString &key, QString *value);
bool readInteger(const QJsonObject &obj, const QString &key, int *value, int defaultVal = -1);
bool readStringList(const QJsonObject &obj, const QString &key, QStringList *list);

template <class T> void parseListObject(const QJsonObject &obj, const QString &key, T **t)
{
    QJsonObject::const_iterator it = obj.constFind(key);
    if (it != obj.constEnd() && it.value().isArray())
        *t = new T(it.value().toArray());
}

enum ZoneType
{
    RowTitle = 0, //行标题（表头）
    BigTitle,     //大标题
    Content,      //内容
    SubTitle,     //副标题
    Other,        //其他
    Info,         //表格信息
    Empty,        //空白
};

enum StyleZoneType
{
    StyleInvalid = -1, //非法
    StyleBigTitle,     //大标题
    StyleInfo,         //表格信息
    StyleRowTitle,     //行标题（表头）
    StyleFirstCol,     //首列
    StyleOddRowAlter,  //行交替区域-奇数行
    StyleEvenRowAlter, //行交替区域-偶数行
    StyleOddColAlter,  //列交替区域-奇数列
    StyleEvenColAlter, //列交替区域-偶数列
    StyleNoneAlter,    //无交替区域
};

enum FillAlterBase
{
    NoneAlternation = 0,
    RowAlternation, //行交替
    ColAlternation, //列交替
};

enum ColorType
{
    LightColor = 0, //浅色系
    MediumColor,    //中色系
    DeepColor,      //深色系
};

////////////////////////////////////////////////////
HRESULT createRange(IN const QJsonArray &rangeArray, OUT ES_CUBE &cube);

//表格整理区域识别参数解析结构
struct Zone
{
  public:
    Zone() = default;
    Zone(const QJsonArray &eachZone);

  public:
    ZoneType m_type;
    ES_CUBE m_range;
};

//一个描述表格的数据结构，包括它的标题、副标题、行标题、内容的结构
class TableRangeInfo
{
  public:
    TableRangeInfo() = default;

    void setWorksheet(IKWorksheet *pWorkSheet);
    IKWorksheet *getWorkSheet() const;
    IKWorkbook *getWorkBook() const;
    bool isEmptyTableInfo() const;
    void insertZone(const Zone &zone);
    ZoneType getCellZoneType(int row, int col) const;
    void setRgDescribe(const QString &rgDescribe);
    QString getRgDescribe() const;
    ES_CUBE getFillAlterArea() const;
    ES_CUBE getFillRowAlterArea() const;
    int getFirstColIdx() const;

  private:
    bool isCellInZone(int row, int col, const ES_CUBE &cube) const;

  public:
    ES_CUBE allRangeInfo;
    std::vector<ES_CUBE> vecTitleRangeInfo;    //大标题
    std::vector<ES_CUBE> vecHeadRangeInfo;     //表头(行标题)
    std::vector<ES_CUBE> vecContentRangeInfo;  //内容
    std::vector<ES_CUBE> vecSubTitleRangeInfo; //副标题
    std::vector<ES_CUBE> vecOtherRangeInfo;    //其他
    std::vector<ES_CUBE> vecInfoRangeInfo;     //表格信息类型
  private:
    IKWorksheet *m_pWorkSheet;
    QString m_rgDescribe;
};

struct borderStyleInfo
{
  public:
    borderStyleInfo(const QString &borderColor, const QString &borderStyle)
      : m_borderColor(borderColor), m_borderLineStyle(borderStyle)
    {
    }
    QString getBorderColor() const
    {
        return m_borderColor;
    }
    QString getBorderStyle() const
    {
        return m_borderLineStyle;
    }

  private:
    QString m_borderColor;
    QString m_borderLineStyle;
};

class TableZoneStyle
{
  public:
    TableZoneStyle(const QString &textColor, const QString &fillColor, std::unique_ptr<borderStyleInfo> pEdgeTopBorder,
                   std::unique_ptr<borderStyleInfo> pEdgeBottomBorder, std::unique_ptr<borderStyleInfo> pEdgeLeftBorder,
                   std::unique_ptr<borderStyleInfo> pEdgeRightBorder, std::unique_ptr<borderStyleInfo> pInsideHBorder,
                   std::unique_ptr<borderStyleInfo> pInsideVBorder);
    ~TableZoneStyle() = default;

    borderStyleInfo *getOneBorderStyle(oldapi::ETBorderIndex borderIdx);

    QString getTextColor()
    {
        return m_textColor;
    }
    QString getFillColor()
    {
        return m_fillColor;
    }

  private:
    std::vector<QString> m_vecColor;
    //文字
    QString m_textColor;
    //填充
    QString m_fillColor;
    //外边框
    std::unique_ptr<borderStyleInfo> m_spEdgeTopBorder;
    std::unique_ptr<borderStyleInfo> m_spEdgeBottomBorder;
    std::unique_ptr<borderStyleInfo> m_spEdgeLeftBorder;
    std::unique_ptr<borderStyleInfo> m_spEdgeRightBorder;

    //内边框
    std::unique_ptr<borderStyleInfo> m_spInsideVBorder;
    std::unique_ptr<borderStyleInfo> m_spInsideHBorder;
};

class TableRangeStyle
{
  public:
    TableRangeStyle(const QVariantMap &dataMap);
    ~TableRangeStyle() = default;

    void parseBorderStyleInfo(const QVariantMap &borderStyleMap, std::unique_ptr<borderStyleInfo>&);

    TableZoneStyle *getTableRangeStyle(StyleZoneType type);
    void setFirstCol(bool bFirstCol)
    {
        m_bFirstCol = bFirstCol;
    }
    bool isFirstCol()
    {
        return m_bFirstCol;
    }
    bool isHeader()
    {
        return m_bHeader;
    }
    FillAlterBase getFillAlterBase()
    {
        return m_fillAlterBase;
    }
    QString getStyleName() const
    {
        return m_styleName;
    }

  private:
    StyleZoneType getStyleZoneType(const QString &strStyleZoneType);
    FillAlterBase getAlterType(const QString &strAlterType);
    ColorType getColorType(const QString &strColorType);

  private:
    bool m_bFirstCol;
    bool m_bHeader;
    bool m_bCharge;
    FillAlterBase m_fillAlterBase;
    std::map<StyleZoneType, std::unique_ptr<TableZoneStyle>> m_mapTableRangeStyle;
    QString m_styleName;
};

class TableRangeInfoList
{
  public:
    TableRangeInfoList() = default;
    void insertRangeInfo(const TableRangeInfo &tableInfo);
    size_t elementCnt();
    TableRangeInfo &item(int idx);
    void setZoneTypeInfo(const Zone &zone);

  private:
    bool isCubeEqual(const ES_CUBE &cube1, const ES_CUBE &cube2);
    bool isZoneInRange(const Zone &zone, const ES_CUBE &rangeCube);

  private:
    std::vector<TableRangeInfo> m_rangeInfoList;
};

struct TableStructCollectInfo
{
  public:
    TableStructCollectInfo() : m_TitleCount(0), m_HeaderCount(0), m_ContentCount(0), m_TableInfoCount(0), m_Range()
    {
    }
    void increaseZoneTypeCnt(const Zone *zone)
    {
        switch (zone->m_type)
        {
        case RowTitle:
            ++m_HeaderCount;
            break;
        case BigTitle:
            ++m_TitleCount;
            break;
        case Content:
            ++m_ContentCount;
            break;
        case Info:
            ++m_TableInfoCount;
            break;
        }
        m_Range = zone->m_range;
    }
    int getTitleCnt() const
    {
        return m_TitleCount;
    }
    int getHeaderCnt() const
    {
        return m_HeaderCount;
    }
    int getContentCnt() const
    {
        return m_ContentCount;
    }
    int getTableInfoCnt() const
    {
        return m_TableInfoCount;
    }

    ES_CUBE *getTableRange() const
    {
        return (ES_CUBE *)(&m_Range);
    }

  private:
    int m_TitleCount;
    int m_HeaderCount;
    int m_ContentCount;
    int m_TableInfoCount;
    ES_CUBE m_Range;
};

class RangeList
{
  public:
    RangeList(const QJsonObject &jsonObject, IKWorksheet *pWorkSheet, bool bMerge = false);
    ~RangeList() = default;
    TableRangeInfoList &getTableInfoList();
    QString getSceneDescription();
    void getTableStructCollectInfo(OUT int &tableCnt,
                                   OUT std::vector<TableStructCollectInfo> &tableStructCollectInfoList);

  private:
    void init(const QJsonObject &jsonObject, IKWorksheet *pWorkSheet, bool bMerge = false);

  private:
    TableRangeInfoList m_tableInfoList;
    QString m_sceneDescription;
    int m_tableCnt;
    std::vector<TableStructCollectInfo> m_vecTableStructCollectInfo;
};

} // namespace etai
#endif // __KAIET_SMARTDATASERVICE_RECOGNIZE_RECOGNIZEDATA_H__