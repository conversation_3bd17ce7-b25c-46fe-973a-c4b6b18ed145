﻿#include "http_error.h"

#include "hresult_to_string.h"

namespace wo
{

struct HttpError
{
    HRESULT type;
    PCWSTR message;
};

#define HTTP_ERROR_MESSAGE(hr, message) case hr: return {hr, __X(message)};
static
HttpError GetError(HRESULT hr)
{
	switch (hr)
	{
		HTTP_ERROR_MESSAGE(E_INVALID_REQUEST, "Required parameter missing or incorrect")
		HTTP_ERROR_MESSAGE(E_DBSHEET_RECORD_NOT_FOUND, "Record not found")
		HTTP_ERROR_MESSAGE(E_DBSHEET_FIELD_NOT_FOUND, "Field not found")
		HTTP_ERROR_MESSAGE(E_DBSHEET_ILLEGAL_PRIMARY_FIELD, "Invalid field type for primary field")
		HTTP_ERROR_MESSAGE(E_DBSHEET_FIELD_INVALID_NAME, "Field name invalid")
		HTTP_ERROR_MESSAGE(E_DBSHEET_FILTER_INVALID, "Invalid filter")
		HTTP_ERROR_MESSAGE(E_DBSHEET_VIEW_NOT_FOUND, "View not found")
		HTTP_ERROR_MESSAGE(E_DBSHEET_SHEET_NOT_FOUND, "Sheet not found")
		HTTP_ERROR_MESSAGE(E_DBSHEET_INVALID_INPUT, "Update record failed")
		HTTP_ERROR_MESSAGE(E_DBSHEET_FORM_VER_CONFLICT, "Form version conflict")
		HTTP_ERROR_MESSAGE(E_DBSHEET_FORM_REQUIRED_FIELD, "Missing some form required field")
		HTTP_ERROR_MESSAGE(E_DBSHEET_VIEWS_EMPTY, "Views are empty")
		HTTP_ERROR_MESSAGE(E_DBSHEET_VIEWS_NOT_EMPTY, "Views are not empty")

		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_FAIL, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_MANAGE, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_SHEET_ADD, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_SHEET_REMOVE, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_SHEET_EDIT, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_SHEET_MANAGE, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_VIEW_MANAGE, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_FIELD_MANAGE, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_FIELD_VISIT, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_FIELD_EDIT, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_RECORD_ADD, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_RECORD_REMOVE, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_RECORD_VISIT, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_RECORD_EDIT, "Not have permission");
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_RECORD_MOVE, "Not have permission");
		
		HTTP_ERROR_MESSAGE(E_DBSHEET_AD_FIELD_PROTECT_CANNOT_PRIMARY, "Can not set the field as primary field")
		HTTP_ERROR_MESSAGE(E_DBSHEET_PERMISSION_CHART_NOT_ALLOW,"Chart Permission not allow");
		HTTP_ERROR_MESSAGE(E_DBSHEET_PERMISSION_NOT_ALLOW_SHARE,"Share Permission not allow");
		HTTP_ERROR_MESSAGE(E_DBSHEET_PERMISSION_ID_NOT_EXIST, "PermissionId not exist");
		HTTP_ERROR_MESSAGE(E_DBSHEET_SELECT_ITEM_INVALID, "Invalid select items")
		HTTP_ERROR_MESSAGE(E_DBSHEET_SELECT_ITEM_CONFLICT, "Duplicate select items")
		HTTP_ERROR_MESSAGE(E_DBSHEET_SELECT_ITEM_EMPTY, "No valid select items found")
		HTTP_ERROR_MESSAGE(E_DBSHEET_CONTACT_CELL_INVALID_CONTENT, "Meets invalid contact field cell")
		HTTP_ERROR_MESSAGE(E_DBSHEET_MODIFY_ORDER_FIELDS_NOT_MATCH_ALL_FIELDS, "Modifying fields order failed, all fields in sheet should be provided")
		HTTP_ERROR_MESSAGE(E_DBSHEET_UNSUPPORTED_MUTIPLE, "This cell is not supported multiple value");
		HTTP_ERROR_MESSAGE(E_DBSHEET_MERGE_DB_REACH_MAX_SHEET_ROWS, "MergeDB reach max sheet rows")
		HTTP_ERROR_MESSAGE(E_KSHEET_NOT_DBSHEET, "Unsupported operation on dbsheet")
		HTTP_ERROR_MESSAGE(E_KSHEET_SHEET_NOT_FOUND, "Sheet not found")
		HTTP_ERROR_MESSAGE(E_NOT_HAVE_PERMISSION, "Not have permission")
		HTTP_ERROR_MESSAGE(E_NOTIMPL, "Unsupported operation")
		HTTP_ERROR_MESSAGE(E_OPERATION_NOT_SUPPORTED_ON_PROTECTED_SHEET, "Operation not supported on protected sheet")
		HTTP_ERROR_MESSAGE(E_OPERATION_NOT_SUPPORTED_ON_RANGE_WITH_HYPERLINK_RUNS, "Operation not support on range with hyperlink runs")
		HTTP_ERROR_MESSAGE(E_RANGE_INVALID, "Range invalid")
		HTTP_ERROR_MESSAGE(E_KSHEET_NOT_GRIDSHEET, "Operating sheet is not gridsheet")

		HTTP_ERROR_MESSAGE(E_INVALID_CHAR, "Name string is invalid")
		HTTP_ERROR_MESSAGE(E_NAME_CONFLICT, "Name conflict")		
		HTTP_ERROR_MESSAGE(E_COMMAND_NOT_FOUND, "Command not found")
		HTTP_ERROR_MESSAGE(E_UNKNOWN_ENUM, "Unknown enum")
        HTTP_ERROR_MESSAGE(E_QRCOL_NOT_FOUND, "QRCol not found")
		HTTP_ERROR_MESSAGE(E_OPERATION_NOT_SUPPORTED_ON_HIDDEN_RANGE, "Hidden range is not supported in this operation")
		HTTP_ERROR_MESSAGE(E_KSHEET_COPY_FORMAT_ERROR, "Format is invalid and not supported empty")
		HTTP_ERROR_MESSAGE(E_KSHEET_RANGE_ERROR, "Range is invalid")
		HTTP_ERROR_MESSAGE(E_KSHEET_COPY_FAILED, "Core copy failed")
        HTTP_ERROR_MESSAGE(E_KSHEET_ADD_ROWS_HAS_DUP_VAL, "Added rows has duplicates")
		HTTP_ERROR_MESSAGE(E_KSHEET_ADD_ROWS_ALL_DUP_VAL, "All added rows are duplicates")
		HTTP_ERROR_MESSAGE(E_KSHEET_TITLE_RECORD_NULL, "No title for empty sheet")
		HTTP_ERROR_MESSAGE(E_KSHEET_TITLE_RECORD_OVERLIMIT, "Title range overlimit")
		HTTP_ERROR_MESSAGE(E_KSHEET_TITLE_RECORD_EMPTY_CELL, "Title contains empty cell")
		HTTP_ERROR_MESSAGE(E_KSHEET_TITLE_UNKNOWN, "Title unknown")
		// 从GridSheet同步到db
		HTTP_ERROR_MESSAGE(E_DBSHEET_SYNC_GRIDSHEET_DATA_INVALID, "SyncData invalid")
		HTTP_ERROR_MESSAGE(E_DBSHEET_SYNC_GRIDSHEET_ID_CONTAINER_INVALID, "IdContainer invalid");
		HTTP_ERROR_MESSAGE(E_DBSHEET_SYNC_GRIDSHEET_HEADER_ROW_INVALID, "HeaderRow invalid");
		HTTP_ERROR_MESSAGE(E_DBSHEET_SYNC_GRIDSHEET_ID_CONTAINER_EMPTY, "IdContainer empty");
		HTTP_ERROR_MESSAGE(E_DATAANALYZE_NO_TASKID, "Not find task id")
		HTTP_ERROR_MESSAGE(E_DATAANALYZE_INVAILD_CONNID, "Invalid connId")
		HTTP_ERROR_MESSAGE(E_DATAANALYZE_PERMISSION_FORBIDDEN, "data analyze permission forbidden")
		HTTP_ERROR_MESSAGE(E_DATAANALYZE_FAILED, "data analyze failed")

		default: return {hr, __X("Internal error")};
	}
}
#undef HTTP_ERROR_MESSAGE

void AddHttpError(HRESULT hr, ISerialAcceptor *acpt, PCWSTR extraInfo,std::unique_ptr<IDbErrInfoCollector> pErrInfoCollector)
{
	if (SUCCEEDED(hr))
		return;

	acpt->addKey("error");
	acpt->beginStruct();
	HttpError error = GetError(hr);
	acpt->addInt32("code", error.type); // 错误码的枚举值. 打算逐步废弃, 因为枚举的值不保证不变
	acpt->addString("type", GetErrWideString(error.type)); // 错误码枚举值的变量名字符串. 保证名称不变
	if (extraInfo)
	{
		ks_wstring msg(error.message);
		msg.append(__X(". "));
		msg.append(extraInfo);
		acpt->addString("message", msg.c_str());
	}
	else
		acpt->addString("message", error.message);

	if(pErrInfoCollector)
	{
		//由于之前的postExecute可能会把writer给清空，因此在这里统一序列化一些错误的额外信息回去。
		acpt->addKey("errorExtra");
		acpt->beginStruct();
		{
			acpt->addKey("invalidCellsInfo");
			pErrInfoCollector->SerialContent(acpt);
		}
		
		acpt->endStruct();
	}
	acpt->endStruct();
}

} // wo
