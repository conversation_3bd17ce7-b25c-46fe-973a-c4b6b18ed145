﻿#ifndef __WEBET_STATSHEET_HELPER__
#define __WEBET_STATSHEET_HELPER__

#include "etstdafx.h"
#include "workbook.h"
#include "dbsheet/et_dbsheet_utils.h"

namespace wo
{
class KEtWorkbook;

//初始化统计表sheet
HRESULT initStatDbSheet(const VarObj&, KEtWorkbook*, _Worksheet*, IDBProtectionJudgement*);
HRESULT SetStatSheetSetting(const VarObj& param, KEtWorkbook*pWorkbook, ISheet* pStatSheet);
HRESULT ActivateStatSheetByResetSettingField(const VarObj& param, KEtWorkbook*pWorkbook, ISheet* pStatSheet);
HRESULT RecoverStatSheet2NormalDbSheet(ISheet* pStatSheet);
bool IsStatSheet(IBook* pBook, UINT sheetStId);
bool IsStatSheetSettingField(IBook* pBook, UINT sheetStId, EtDbId fldId);
void UpdateStatSheetFormula(IBook *pBook);// 遍历所有统计表，并最后判断是否需要公式重新设置
} // end namespace wo

#endif // __WEBET_STATSHEET_HELPER__
