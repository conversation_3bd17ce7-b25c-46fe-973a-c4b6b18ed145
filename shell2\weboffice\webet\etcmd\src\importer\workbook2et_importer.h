﻿#ifndef __WEBET_WORKBOOK2ET_IMPORTER_H__
#define __WEBET_WORKBOOK2ET_IMPORTER_H__


#include "dbsheet/db2et_exporter.h"
#include "workbook_importer.h"

namespace wo
{
class KEtRevisionContext;

class Workbook2EtImporter : public WorkbookImporter
{
public:
    Workbook2EtImporter(KEtRevisionContext* pCtx, etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook, PCWSTR userId, binary_wo::VarObj& param);
    void SerialAppSharedInfo(binary_wo::VarObj* pObj) override {}
private:
    HRESULT copySheet(etoldapi::_Worksheet* pSrcWorkSheet, VARIANT before, VARIANT after,
                      IKCoreObject** ppNewSheetObj) override;
    HRESULT onBeforeImport() override;
    HRESULT onAfterImportOneSheet(etoldapi::_Worksheet* pSrcWorkSheet,
                                  etoldapi::_Worksheet* pNewWorkSheet) override;
    HRESULT onAfterImport(const std::vector<etoldapi::_Worksheet*>& srcWorkSheets,
                          const std::vector<etoldapi::_Worksheet*>& newWorkSheets) override;
private:
    KSheet2XlsxExporter m_exporter;
    KEtRevisionContext* m_pCtx;
    std::unordered_map<UINT, UINT> m_sheetIdMap;
};


} // namespace wo

#endif
