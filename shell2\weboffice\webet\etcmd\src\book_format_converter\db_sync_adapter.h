﻿#ifndef __WEBET_ADD_SYNC_DBSHEETS_H__
#define __WEBET_ADD_SYNC_DBSHEETS_H__

#include "etcore/et_core_dbsheet_enum.h"
#include "applogic/etapi_old.h"
#include "wo/et_shared_str.h"
#include "helpers/varobject_helper.h"
#include "dbsheet/et_dbsheet_utils.h"

interface IDBProtectionJudgement;
namespace wo
{
	class KEtRevisionContext;
	struct SyncDbParam
	{
		PCWSTR fldSourceName = nullptr;
		PCWSTR urlTemplate = nullptr;
		binary_wo::VarObj* m_pParam = nullptr;
		KEtRevisionContext* m_pCtx = nullptr;
		PCWSTR m_pcwFilePath = nullptr;
	};
	struct SyncSheetInfo
	{
		UINT srcSheetId = 0;
		UINT tarSheetId = 0;
		EtDbId fldSourceId = INV_EtDbId;
		std::unordered_map<EtDbId, EtDbId> fldMap;
		ET_DbSheet_Sync_Type syncType;
	};
	class AddSyncDbSheetsAdapter
	{
	public:
		struct SyncDbSheetsParam : SyncDbParam
		{
			int maxSyncSheetLimit = INT_MAX;
			PCWSTR viewName = nullptr;
			ET_DbSheet_Sync_Type syncType = DbSheet_St_DB;
			binary_wo::VarObj* sheetsInfo;
		};

		AddSyncDbSheetsAdapter(etoldapi::_Workbook*, IDBProtectionJudgement*);

		HRESULT Init(const SyncDbSheetsParam&);
		HRESULT Exec();
		const std::vector<SyncSheetInfo>& getSyncSheetInfo() const { return m_syncSheetInfoVec; }
		bool GetReachSyncSheetLimit() const { return m_bReachSyncSheetLimit; }
		bool GetLostFlag() const { return m_lostFlag; }
		UINT getActiveStId() const { return m_activeStId; }
	private:
		HRESULT exec();
		HRESULT checkSyncSheetNumLimit();
		HRESULT addWorksheets();
		void rollback();

		SyncDbSheetsParam m_param;
		bool m_bReachSyncSheetLimit = false;
		int m_addSyncSheetCnt = 0;
		bool m_lostFlag = false;
		UINT m_activeStId = 0;
		std::vector<IKWorksheet*> m_newWorksheetsVec;
		std::vector<SyncSheetInfo> m_syncSheetInfoVec;
		etoldapi::_Workbook* m_pWorkbook = nullptr;
		IKWorksheets* m_pWorksheets = nullptr;
		IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
	};

	class DBAddMergeSyncHelper;
	class FieldMappingItem;
	class AddMergeSyncDbSheetsAdapter
	{
	public:
		struct SyncDbSheetsParam : SyncDbParam
		{
			int maxSyncSheetLimit = INT_MAX;
			PCWSTR viewName = nullptr;
			PCWSTR sheetName = nullptr;
			PCWSTR fldSourceFileName = nullptr;
		};

		AddMergeSyncDbSheetsAdapter(etoldapi::_Workbook*, IDBProtectionJudgement*);
		~AddMergeSyncDbSheetsAdapter();
		HRESULT Init(const SyncDbSheetsParam&);

		HRESULT AddFieldMappingItem(PCWSTR fileId, binary_wo::VarObj* srcSheetsInfo);
		HRESULT ExecMerge();
		UINT GetActiveStId() const { return m_activeStId; }
		EtDbId GetFieldSourceId() const;
		EtDbId GetFieldSourceNameId() const;
		const std::map<ks_wstring, std::vector<FieldMappingItem>>& getFileIdMap() const { return m_fileIdMap; }
	protected:
		HRESULT PrepareMerge();
		HRESULT createTarWorksheet();
		HRESULT AdjustMerge();
		void rollback();
	private:
		SyncDbSheetsParam m_param;
		etoldapi::_Workbook* m_pTarWorkbook = nullptr;
		IKWorksheets* m_pTarWorksheets = nullptr;
		IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
		IKWorksheet* m_pNewWorksheet = nullptr;
		UINT m_activeStId = 0;
		int m_iBookNum = 0;
		int m_iRecordCount;
		std::map<ks_wstring, binary_wo::VarObj*> m_srcFileIdMap;
		std::unique_ptr<DBAddMergeSyncHelper> m_upDBSyncMergeHelper;
		std::map<ks_wstring, std::vector<FieldMappingItem>> m_fileIdMap;
		bool m_bNeedRollback = true;
	};

	class DbSheetValueSerialiser;
	class DbSyncSheetHelper
	{
	public:
		struct SyncDbSheetParam : SyncDbParam
		{
			const std::unordered_map<EtDbId, EtDbId>* pFieldMap = nullptr; // srcFieldId: tarFieldId
			UINT srcSheetId;
			binary_wo::VarObj sheetData;
		};
		struct SyncSrcSheetData
		{
			EtDbId primaryId = INV_EtDbId;
			EtDbId syncFieldSourceId = INV_EtDbId;
			EtDbId syncFieldSourceNameId = INV_EtDbId;
		};
		DbSyncSheetHelper(etoldapi::_Workbook*, UINT, IDBProtectionJudgement*, bool bNewLookupConvert = false);
		~DbSyncSheetHelper();
		HRESULT Init(const SyncDbSheetParam&, DbSheetValueSerialiser* pValueSetter);
		HRESULT Exec();
		const SyncSheetInfo& getSyncSheetInfo() const { return m_syncSheetInfo; }
		PCWSTR GetErrorMsg() const { return m_errMsg.c_str(); }
	private:
		HRESULT SyncDbSheetProperty(const VarObj& sheetObj);
		HRESULT SyncDbSheetFields(const VarObj& fieldsObj);
		HRESULT SyncDbSheetFieldProperty(IDbField* pTarField, VarObj& fieldObj);
		HRESULT SyncDbSheetRecords(const VarObj& recordsObj);
		HRESULT SyncDbSheetRecord(EtDbId tarRecId, const VarObj& recordObj);
		HRESULT SyncDbSheetCellValue(EtDbId srcFldId, EtDbId tarRecId, IDbField* pTarField, WebName srcFldIdStr, const VarObj& valObj);
		HRESULT SetInfoField();
		HRESULT SetViewFieldWidth();
		HRESULT SetDbSheetFieldCommonProperty(IDbField* pField, const VarObj& fieldObj);
		HRESULT GetSrcSheetRecordIdList(const VarObj& sheetObj, std::unordered_set<EtDbId>& srcRecIdSet);
		HRESULT GetSyncFieldMapping(std::unordered_map<EtDbId, EtDbId>& map);
		HRESULT ClearSheetRecords();
		bool IsFirstBatch(); //支持分批更新，判断是否是第一次分批更新
		ET_DbSheet_FieldType GetLookupBaseFieldType(EtDbId fldId);
		HRESULT SetLookupFallBackTextValue(EtDbId recId, EtDbId fldId, ET_DbSheet_FieldType fldType, binary_wo::VarObj objValue);
		alg::managed_token_assist GenerateLookupFallBackTextValueToken(const binary_wo::VarObj& objValue);
		void WriteSyncFieldError(HRESULT hr, PCWSTR key, ET_DbSheet_FieldType srcFldType, ET_DbSheet_FieldType tarFldType, VarObj obj);
		HRESULT CleanSrcSheetFieldProperty(VarObj& fieldObj);
	private:
		SyncSheetInfo m_syncSheetInfo;
		SyncDbSheetParam m_param;
		SyncSrcSheetData m_srcSheetData;
		etoldapi::_Workbook* m_pTarWorkbook = nullptr;
		ISheet* m_pTarSheet = nullptr;
		ks_stdptr<IDBSheetOp> m_spTarDbSheetOp;
		IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
		DbSheetValueSerialiser* m_pValueSetter = nullptr;
		bool m_bNewLookupConvert = false;
		std::unordered_map<EtDbId, ET_DbSheet_FieldType> m_srcfldIdTypeMap;
		std::unordered_map<EtDbId, EtDbId> m_recMap;
		std::vector<EtDbId> m_batchInsertSrcRecIds;
		ks_wstring m_errMsg;
	};

	class FullUpdateSyncDbSheetsAdapter
	{
	public:
		struct FullUpdateSyncDbSheetParam : SyncDbParam
		{
			const std::unordered_map<EtDbId, EtDbId>* pFieldMap = nullptr; // srcFieldId: tarFieldId
			ks_wstring* pTimeStatInfo = nullptr;	//暂时放这里收集耗时信息，后面可以去掉
			PCWSTR m_pcwDefaultName = __X("Unknown");
			bool m_bEnableCopyAttachment = true;
		};

		FullUpdateSyncDbSheetsAdapter(etoldapi::_Workbook*, etoldapi::_Workbook*, UINT, UINT, IDBProtectionJudgement*,
			bool bNewLookupConvert = false);

		HRESULT Init(const FullUpdateSyncDbSheetParam&);
		HRESULT Exec();
		const SyncSheetInfo& getSyncSheetInfo() const { return m_syncSheetInfo; }
		bool GetLostFlag() const { return m_lostFlag; }
	private:
		HRESULT execDbSync(etoldapi::_Worksheet*, etoldapi::_Worksheet*);
		HRESULT execGridsheetSync(etoldapi::_Worksheet*, etoldapi::_Worksheet*);
	private:
		FullUpdateSyncDbSheetParam m_param;
		bool m_lostFlag = false;
		SyncSheetInfo m_syncSheetInfo;
		etoldapi::_Workbook* m_pSrcWorkbook = nullptr;
		etoldapi::_Workbook* m_pTarWorkbook = nullptr;
		IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
		bool m_bNewLookupConvert = false;
	};

	class DBSyncMergeHelper;
	class FullUpdateMergeSyncDbSheetsAdapter
	{
	public:
		struct FullUpdateMergeSyncDbSheetParam : SyncDbParam
		{
			PCWSTR fldSourceFileName = nullptr;
		};
		FullUpdateMergeSyncDbSheetsAdapter(etoldapi::_Workbook*, UINT, IDBProtectionJudgement*, bool);
		HRESULT Init(const FullUpdateMergeSyncDbSheetParam&);
		HRESULT AddFieldMappingItem(PCWSTR fileId, PCWSTR fileName, const VarObj& srcStIdVec);
		HRESULT ExecMerge();
		bool GetReachSyncSheetLimit() const { return m_bReachSyncSheetLimit; }
		bool GetLostFlag() const { return m_lostFlag; }
		UINT GetActiveStId() const { return m_activeStId; }
		void SetSrcWorkbook(etoldapi::_Workbook* pSrcWorkbook, int idx) 
		{ 
			m_pSrcWorkbook = pSrcWorkbook;
			m_pSrcWorksheets = m_pSrcWorkbook->GetWorksheets();
			m_iCurIdx = idx;
		}
		EtDbId GetFieldSourceId() const;
		EtDbId GetFieldSourceNameId() const;
		const std::map<ks_wstring, std::vector<FieldMappingItem>>& getFileIdMap() const { return m_fileIdMap; }
		void SetBookNum(int iBookNum) { m_iBookNum = iBookNum; }
	private:
		HRESULT PrepareMerge(const std::vector<IKWorksheet*>& srcWorksheets);
		HRESULT AdjustMerge();
		HRESULT getTarWorksheet();
		HRESULT fillSrcWorksheets(std::vector<IKWorksheet*>& srcWorksheets);
		bool checkIsStatisicSheetInThisBook(ISheet* pSrcSheet);
	private:
		FullUpdateMergeSyncDbSheetParam m_param;
		UINT m_tarSheetId;
		bool m_bNewLookupConvert = true;
		bool m_bReachSyncSheetLimit = false;
		bool m_lostFlag = false;
		UINT m_activeStId = 0;
		IKWorksheet* m_pNewWorksheet = nullptr;
		etoldapi::_Workbook* m_pSrcWorkbook = nullptr;
		etoldapi::_Workbook* m_pTarWorkbook = nullptr;
		IKWorksheets* m_pSrcWorksheets = nullptr;
		IKWorksheets* m_pTarWorksheets = nullptr;
		IDBProtectionJudgement* m_pProtectionJudgement = nullptr;
		PCWSTR m_curFileId = nullptr;
		PCWSTR m_curFileName = nullptr;
		std::map<ks_wstring, std::vector<FieldMappingItem>> m_fileIdMap;
		std::unique_ptr<DBSyncMergeHelper> m_upDBSyncMergeHelper;
		int m_iBookNum = 0;
		int m_iCurIdx = 0;
	};
	class UnsyncDbSheetHelper
	{
		using AttachmentIdMap = std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher>;
	public:
		explicit UnsyncDbSheetHelper(IKWorksheet*);

		HRESULT Init(const AttachmentIdMap*);
		HRESULT Exec();
		bool GetLostNoteFlag() const { return m_lostNoteFlag; }
	private:
		IKWorksheet* m_pWorksheet = nullptr;
		const AttachmentIdMap* m_pAttachmentIdMap = nullptr;
		bool m_lostNoteFlag = false;
	};
} // namespace wo

#endif
