﻿#ifndef __IDENTIFYTABLE_H__
#define __IDENTIFYTABLE_H__

enum JsonDataType
{
	JsonDataArrangement,
	JsonDataProofreading,
	JsonDataAnalysis,
	JsonDataPivotTableSource,
	JsonDataQABookSource,
	JsonDataAIFill
};

HRESULT GetJsonDataType(PCWSTR, JsonDataType*);

struct IdentifyRange
{
	ROW iTop = -1;
	ROW iBottom = -1;
	COL iLeft = -1;
	COL iRight = -1;
	IdentifyRange() = default;
	IdentifyRange(ROW nT, ROW nB, COL nL, COL nR) : iTop(nT), iBottom(nB), iLeft(nL), iRight(nR) {}
	explicit IdentifyRange(const RANGE&);
	bool IsInRange(ROW iRow, COL iCol) const
	{
		return iRow >= iTop && iRow <= iBottom && iCol >= iLeft && iCol <= iRight;
	}
	bool IsOneCell() const
	{
		return iTop == iBottom && iLeft == iRight;
	}
	void ResetRange(const RANGE&);
};

class HiddenRowColTool
{
	IdentifyRange m_usedRange;
	std::set<ROW> m_setHiddenRow;
	std::set<COL> m_setHiddenCol;

	void Init(ISheet*, const RANGE&);

	COL GetExportJsonColIdx(COL) const;
	// 当当前行为隐藏行则返回上一个非隐藏行对应的idx
	ROW GetExportJsonRowIdx1(ROW) const;
	COL GetExportJsonColIdx1(COL) const;
public:
	HiddenRowColTool(ISheet*, const RANGE&);

	bool IsHiddenRow(ROW) const;
	bool IsHiddenCol(COL) const;
	// 当当前行为隐藏行则返回下一个非隐藏行对应的idx
	ROW GetExportJsonRowIdx(ROW) const;
	// 处理区域的情况
	IdentifyRange GetExportJsonRange(const IdentifyRange*) const;
};

constexpr float defaultFontSize = 11.0;

struct IdentifyCells : public IdentifyRange
{
	ks_bstr strContent;
	ks_bstr strValue2;
	PCWSTR strNumberFormat = nullptr;
	float fFontSize = defaultFontSize;
	ETHAlign nAlignment = etHAlignGeneral;

	explicit IdentifyCells(const RANGE& rg) : IdentifyRange(rg) {}
	IdentifyCells(IdentifyCells&& rs) noexcept
		: IdentifyRange(rs)
		, strNumberFormat(rs.strNumberFormat)
		, fFontSize(rs.fFontSize)
		, nAlignment(rs.nAlignment)
	{
		strContent.attach(rs.strContent.detach());
		strValue2.attach(rs.strValue2.detach());
		rs.strNumberFormat = nullptr;
		rs.fFontSize = defaultFontSize;
		rs.nAlignment = etHAlignGeneral;
	}
	IdentifyCells(const IdentifyCells&) = delete;
	IdentifyCells& operator=(const IdentifyCells&) = delete;
	IdentifyCells& operator=(IdentifyCells&&) = delete;
	~IdentifyCells() = default;
	void ExportJson(QJsonObject&, HiddenRowColTool*) const;
};

struct IdentifyMergeCellsList : public std::vector<IdentifyCells>
{
	const IdentifyCells* FindCellInMergeCells(ROW, COL) const;
};

using IdentifyTableContinueProc = bool(__stdcall*)();

namespace wo
{
	interface IEtProtectionCtx;
} // namespace wo

class KIdentifyTable
{
	IKWorksheet* m_pWorksheet = nullptr;
	ISheet* m_pSheet = nullptr;
	BMP_PTR m_pBmp = nullptr;
	wo::IEtProtectionCtx* m_pCtx = nullptr;
	ks_stdptr<IBookOp> m_spBookOp;
	ks_stdptr<IETStringTools> m_spTools;
	JsonDataType m_nJsonDataType = JsonDataArrangement;
	IdentifyRange m_usedRange;
	std::vector<IdentifyRange> m_selectRangeList;
	std::unique_ptr<HiddenRowColTool> m_spTool;
	IdentifyTableContinueProc m_identifyTableContinueProc = nullptr;

	bool IsNotEmptyCell(IDX, ROW, COL) const;
	void GetTableCols(std::vector<std::pair<COL, COL>>&) const;
	void GetTableRanges(std::vector<IdentifyRange>&) const;
	void BuildCells(IDX, ROW, COL, IdentifyCells&) const;
	void ExportJsonTable(const IdentifyRange&, QJsonObject&) const;
	bool TransformTable(const IdentifyRange&, QJsonObject&, int&) const;
	bool GetTables(const std::vector<IdentifyRange>&, QJsonObject&) const;
	void GetSelectionRangeList(QJsonObject&) const;
public:
	KIdentifyTable(IKWorksheet*, wo::IEtProtectionCtx*, JsonDataType);

	HRESULT Init();

	void SetRangeLimit(UINT rowLimit);
	bool IsContinue() const;
	void SetSelectionRange(const std::vector<RANGE>&);
	void Identify(QJsonObject&);
	void SetIdentifyTableContinueProc(IdentifyTableContinueProc);
    void SetCustomRange(const RANGE& customRg);
};

#endif
