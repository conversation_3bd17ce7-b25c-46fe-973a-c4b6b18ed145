#ifndef __ET_APPCORE_WEBHOOK_DATA_CREATE_HELPER_H__
#define __ET_APPCORE_WEBHOOK_DATA_CREATE_HELPER_H__

#include "webbase/binvariant/binwriter.h"
#include "webbase/binvariant/binvarobj.h"
#include "ettools/ettools_encode_decoder.h"
#include "dbsheet/et_dbsheet_common_helper.h"
#include "webhook/webhook_basic_itf.h"
#include "webhook/webhook_reflection.h"

interface IBook;
interface IDBSheetOp;
interface IWebhookData;
interface IDbExtraFilter;
interface IDbFilterCriteria;
interface IDbFcValueBase;

class WebhookDataCreateHelper
{
public:
    WebhookDataCreateHelper(IBook* pBook) : m_pBook(pBook), m_hookId(__X("")), m_pRes(nullptr) 
    {
        m_pBook->GetExtDataItem(edBookWebhookManager, (IUnknown**)&m_spWebhookMgr);
    };
    HRESULT CreateWebhookData(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData** ppData, bool update = false, bool add = true);
private:
    HRESULT writeHookResult(INT code, PCWSTR message);
    void setDbWebhookDataParam(Et_DbSheet_WebhookType type, binary_wo::VarObj &hook, IWebhookData* pData);
    void setEtWebhookDataParam(Et_WebhookType type, binary_wo::VarObj &hook, IWebhookData* pData);
    bool hasEtUpdateHookPermission(bool update, bool add);
    bool isBookHasHidden();
private:
    IBook* m_pBook;
    ks_wstring m_hookId;
    binary_wo::BinWriter* m_pRes;
    ks_stdptr<IWebhookManager> m_spWebhookMgr;
};

class WebhookDataCreatorProvider : public IWebhookDataCreatorProvider
{
public:
    WebhookDataCreatorProvider(IBook* pBook);

    // IWebhookDataCreatorProvider
    STDPROC_(void) InstallFilter(IWebhookData* pData, webhook_data::Filter filter, UINT sheetId) override;
    STDPROC_(void) InstallFilterResult(IWebhookData* pData, webhook_data::Filter filter, UINT sheetId) override;
    STDPROC_(BMP_PTR) GetBMP() override { return m_pBook->GetBMP(); }

private:
    HRESULT getFilter(webhook_data::Filter filter, IDbExtraFilter** ppFilter);
    HRESULT createDbFilterCriteria(const webhook_data::Criteria criteria, IDbFilterCriteria** ppVal);
    HRESULT createDbFilterVal(const webhook_data::filter_value value, IDbFcValueBase** ppVal);

private:
    IBook* m_pBook = nullptr;
    ks_stdptr<IDBSheetOp> m_spDbSheetOp;
};
#endif