﻿#ifndef __WEBET_DB_DASHBOARD_HTTP_TASK_CLASS_H__
#define __WEBET_DB_DASHBOARD_HTTP_TASK_CLASS_H__

#include "db_http_task_class.h"

namespace wo
{

class DbDashboardHttpCreateFiltersTaskClass : public DbHttpTaskClassBase
{
public:
    explicit DbDashboardHttpCreateFiltersTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class DbDashboardHttpUpdateFiltersTaskClass : public DbHttpTaskClassBase
{
public:
    explicit DbDashboardHttpUpdateFiltersTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

class DbDashboardHttpDeleteFiltersTaskClass : public DbHttpTaskClassBase
{
public:
    explicit DbDashboardHttpDeleteFiltersTaskClass(KEtWorkbook*);
    HRESULT operator()(KwCommand*, KEtRevisionContext*) override;
    PCWSTR GetTag() override;
protected:
    HRESULT CheckCmdPermission(KwCommand*, KEtRevisionContext*) override;
};

};
#endif // __WEBET_DB_DASHBOARD_HTTP_TASK_CLASS_H__