﻿#ifndef __WEBET_HTTP_MACRO_H__
#define __WEBET_HTTP_MACRO_H__

#define SET_ERROR_MESSAGE(name, value, pErrorMsg) \
{ \
    (pErrorMsg)->assign(krt::utf16( \
        QStringLiteral("The %1 is unknown. The unknown %1 value is %2.").arg(name).arg(value))); \
}

#define DECODE_STR_OBJ(obj, name, decodeType, res, pErrorMsg) \
{ \
    PCWSTR str = obj.field_str(name); \
    HRESULT hr = _appcore_GainEncodeDecoder()->Decode##decodeType(str, &res); \
    if (FAILED(hr)) \
    { \
        SET_ERROR_MESSAGE(__X(name), str, pErrorMsg); \
        return hr; \
    } \
}

#endif // __WEBET_HTTP_MACRO_H__