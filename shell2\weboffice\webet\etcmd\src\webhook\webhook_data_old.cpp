﻿#include "etstdafx.h"
#include "etcore/et_core_sheet.h"
#include "webhook_data_old.h"

//////////////////////WebhookDataBase/////////////////////
void WebhookDataBase::CreateServerId(BSTR *pStr)
{
    QDateTime qtTime = QDateTime::currentDateTime();
    qint64 time = qtTime.toMSecsSinceEpoch();
    QString qStr = QString::number(time);
    ks_wstring str(krt::utf16(qStr));
    *pStr = str.allocBSTR();
}

BOOL WebhookDataBase::ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res)
{
    return m_spParamsValidator->ValidateParams(hook, res, this);
}

STDIMP_(BOOL) WebhookDataBase::IsDbSheet() const
{
    Et_DbSheet_WebhookType type = DbSheet_WebhookType_invalid;
    GetType(type);
    if (type != DbSheet_WebhookType_invalid)
        return TRUE;
    return FALSE;
}

STDIMP_(UINT32) WebhookDataBase::GetHookCacheSize() const
{
    Et_DbSheet_WebhookType hookType = DbSheet_WebhookType_invalid;
    GetType(hookType);
    if (hookType != DbSheet_WebhookType_updateCells 
        && hookType != DbSheet_WebhookType_updateSheet
        && hookType != DbSheet_WebhookType_updateFieldCells
        && hookType != DbSheet_WebhookType_updateRecordCells
        && hookType != DbSheet_WebhookType_updateRecords
        && hookType != DbSheet_WebhookType_createAndFillInRecord)
        return 0;
    return m_webHookCache.totalSize();
}
//////////////////////WebhookDataRemoveSheet/////////////////////
STDIMP_(void) WebhookDataRemoveSheet::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new RemoveSheetParamsValidator(pBook));
}

//////////////////////WebhookDataRenameSheet/////////////////////
STDIMP_(void) WebhookDataRenameSheet::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new RenameSheetParamsValidator(pBook));
}

//////////////////////WebhookDataUpdateSheetDesc/////////////////////
STDIMP_(void) WebhookDataUpdateSheetDesc::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateSheetDescParamsValidator(pBook));
}

//////////////////////WebhookDataUpdateSheetIcon/////////////////////
STDIMP_(void) WebhookDataUpdateSheetIcon::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateSheetIconParamsValidator(pBook));
}

//////////////////////WebhookDataCreateView/////////////////////
STDIMP_(void) WebhookDataCreateView::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new CreateViewParamsValidator(pBook));
}

//////////////////////WebhookDataRenameView/////////////////////
STDIMP_(void) WebhookDataRenameView::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new RenameViewParamsValidator(pBook));
}

//////////////////////WebhookDataRemoveView/////////////////////
STDIMP_(void) WebhookDataRemoveView::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new RemoveViewParamsValidator(pBook));
}

//////////////////////WebhookDataCreateField/////////////////////
STDIMP_(void) WebhookDataCreateField::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new CreateFieldParamsValidator(pBook));
}

//////////////////////WebhookDataUpdateField/////////////////////
STDIMP_(void) WebhookDataUpdateField::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateFieldParamsValidator(pBook));
}

//////////////////////WebhookDataUpdatePrimaryField/////////////////////
STDIMP_(void) WebhookDataUpdatePrimaryField::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdatePrimaryFieldParamsValidator(pBook));
}

//////////////////////WebhookDataRemoveField/////////////////////
STDIMP_(void) WebhookDataRemoveField::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new RemoveFieldParamsValidator(pBook));
}

//////////////////////WebhookDataCreateRecord/////////////////////
STDIMP_(void) WebhookDataCreateRecord::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new CreateRecordParamsValidator(pBook));
}

//////////////////////WebhookDataRemoveRecord/////////////////////
STDIMP_(void) WebhookDataRemoveRecord::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new RemoveRecordParamsValidator(pBook));
}

//////////////////////WebhookDataUpdateRecordsParent//////////////
STDIMP_(void) WebhookDataUpdateRecordsParent::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateRecordsParentParamsValidator(pBook));
}

//////////////////////WebhookDataUpdateCells/////////////////////
STDIMP_(void) WebhookDataUpdateCells::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateCellsParamsValidator(pBook));
}

STDIMP_(BOOL) WebhookDataUpdateCells::FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId)
{
    if (sheetId != m_dbRange.sheetId)
        return FALSE;
    if (m_dbRange.fieldIds.find(fieldId) == m_dbRange.fieldIds.cend())
        return FALSE;
    if (m_dbRange.recordIds.find(recordId) == m_dbRange.recordIds.cend())
        return FALSE;
    return TRUE;
}

//////////////////////WebhookDataUpdateSheet/////////////////////
STDIMP_(void) WebhookDataUpdateSheet::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateSheetParamsValidator(pBook));
}

STDIMP_(BOOL) WebhookDataUpdateSheet::FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId)
{
    if (sheetId != m_dbRange.sheetId)
        return FALSE;
    return TRUE;
}

//////////////////////WebhookDataUpdateFieldCells/////////////////////
STDIMP_(void) WebhookDataUpdateFieldCells::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateFieldCellsParamsValidator(pBook));
}

STDIMP_(BOOL) WebhookDataUpdateFieldCells::FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId)
{
    if (sheetId != m_dbRange.sheetId)
        return FALSE;
    if (m_dbRange.fieldIds.find(fieldId) == m_dbRange.fieldIds.cend())
        return FALSE;
    return TRUE;
}

//////////////////////WebhookDataUpdateRecordCells/////////////////////
STDIMP_(void) WebhookDataUpdateRecordCells::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateRecordCellsParamsValidator(pBook));
}

STDIMP_(BOOL) WebhookDataUpdateRecordCells::FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId)
{
    if (sheetId != m_dbRange.sheetId)
        return FALSE;
    if (m_dbRange.recordIds.find(recordId) == m_dbRange.recordIds.cend())
        return FALSE;
    return TRUE;
}

//////////////////////WebhookDataUpdateRecords/////////////////////
STDIMP_(void) WebhookDataUpdateRecords::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateRecordsParamsValidator(pBook));
}

STDIMP_(BOOL) WebhookDataUpdateRecords::FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId)
{
    if (sheetId != m_dbRange.sheetId)
        return FALSE;
    return TRUE;
}

STDIMP_(void) WebhookDataUpdateRecords::GetFilter(IDbExtraFilter** ppFilter) const
{
    if (m_spFilter)
    {
        *ppFilter = m_spFilter;
        (*ppFilter)->AddRef();
    }
}

STDIMP_(void) WebhookDataUpdateRecords::SetFilter(IDbExtraFilter* pFilter)
{
    m_spFilter = pFilter;
}

STDIMP_(void) WebhookDataUpdateRecords::GetFilterResult(IDbExtraFilter** ppFilter) const
{
    if (m_spFilterResult)
    {
        *ppFilter = m_spFilterResult;
        (*ppFilter)->AddRef();
    }
}

STDIMP_(void) WebhookDataUpdateRecords::SetFilterResult(IDbExtraFilter* pFilter)
{
    m_spFilterResult = pFilter;
}

//////////////////////WebhookDataUpdateSheetsAllChange/////////////////////
STDIMP_(void) WebhookDataUpdateSheetsAllChange::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new UpdateSheetsAllChangeParamsValidator(pBook));
}

STDIMP_(BOOL) WebhookDataUpdateSheetsAllChange::FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId)
{
    if (m_bAllSheets)
        return TRUE;
    if (m_sheetIds.empty())
        return FALSE;
    if (m_sheetIds.find(sheetId) != m_sheetIds.cend())
        return TRUE;
    return FALSE;
}

STDIMP WebhookDataUpdateSheetsAllChange::EnumSheetIds(IWebhookSheetIdsEnum* pEnum)
{
    for (auto id : m_sheetIds)
    {
        // 当返回false时提前退出
        if (pEnum->Do(id) == FALSE)
            return S_OK;
    }
    return S_OK;
}

//////////////////////WebhookDataCreateAndFillInRecord/////////////////////
STDIMP_(void) WebhookDataCreateAndFillInRecord::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new CreateAndFillInRecordParamsValidator(pBook));
}

STDIMP_(void) WebhookDataCreateAndFillInRecord::AddNeedCheckRecord(EtDbId id)
{
    m_needCheckRecords.insert(id);
}

STDIMP_(void) WebhookDataCreateAndFillInRecord::EnumNeedCheckRecord(IWebhookDbIdsEnum* pEnum)
{
    for (const EtDbId recId : m_needCheckRecords)
    {
        if (pEnum->Do(recId) == FALSE)
            return;
    }
}

STDIMP_(void) WebhookDataCreateAndFillInRecord::DeleteNeedCheckRecord(EtDbId id)
{
    m_needCheckRecords.erase(id);
}

STDIMP_(bool) WebhookDataCreateAndFillInRecord::HasCreateAndFillWithField() const
{
    std::set<EtDbId>& fieldIdsSet = m_dbRange.fieldIds;
    return !fieldIdsSet.empty();
}

//////////////////////EtWebhookDataUpdateSheet/////////////////////
STDIMP_(void) EtWebhookDataUpdateSheet::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtUpdateSheetParamsValidator(pBook));
}

//////////////////////EtWebhookDataUpdateRanges/////////////////////
STDIMP_(void) EtWebhookDataUpdateRanges::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtUpdateRangesParamsValidator(pBook));
    m_pBook = pBook;
}

//////////////////////EtWebhookDataUpdateCharts/////////////////////
STDIMP_(void) EtWebhookDataUpdateCharts::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtUpdateChartsParamsValidator(pBook));
}
//////////////////////EtWebhookDataRemoveSheet/////////////////////
STDIMP_(void) EtWebhookDataRemoveSheet::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtRemoveSheetParamsValidator(pBook));
}

//////////////////////EtWebhookDataInsertRows/////////////////////
STDIMP_(void) EtWebhookDataInsertRows::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtInsertRowsParamsValidator(pBook));
}

//////////////////////EtWebhookDataRemoveRows/////////////////////
STDIMP_(void) EtWebhookDataRemoveRows::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtRemoveRowsParamsValidator(pBook));
}

STDIMP_(void) EtWebhookDataRemoveRows::SetExtraReturnRowCols(const INT* pIdxs, UINT size)
{
    if (size == 0)
        return;
    for (int i = 0; i < size; ++i)
        m_idxSet.insert(pIdxs[i]);
}

STDIMP EtWebhookDataRemoveRows::EnumExtraReturnRowCols(IWebhookReturnIdxEnum* pEnum)
{
    for (const auto& idx : m_idxSet)
    {
        pEnum->Do(idx);
    }
    return S_OK;
}

//////////////////////EtWebhookDataInsertCols/////////////////////
STDIMP_(void) EtWebhookDataInsertCols::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtInsertColsParamsValidator(pBook));
}

//////////////////////EtWebhookDataRemoveCols/////////////////////
STDIMP_(void) EtWebhookDataRemoveCols::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtRemoveColsParamsValidator(pBook));
}

STDIMP_(void) EtWebhookDataRemoveCols::SetExtraReturnRowCols(const INT* pIdxs, UINT size)
{
    if (size == 0)
        return;
    for (int i = 0; i < size; ++i)
        m_idxSet.insert(pIdxs[i]);
}

STDIMP EtWebhookDataRemoveCols::EnumExtraReturnRowCols(IWebhookReturnIdxEnum* pEnum)
{
    for (const auto& idx : m_idxSet)
    {
        pEnum->Do(idx);
    }
    return S_OK;
}

//////////////////////EtWebhookDataUpdateRange/////////////////////
STDIMP_(void) EtWebhookDataUpdateRange::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtUpdateRangeParamsValidator(pBook));
}

STDIMP_(void) EtWebhookDataUpdateRange::SetExtraReturnRowCols(const INT* pIdxs, UINT size)
{
    if (size == 0)
        return;
    for (int i = 0; i < size; ++i)
        m_idxSet.insert(pIdxs[i]);
}

STDIMP EtWebhookDataUpdateRange::EnumExtraReturnRowCols(IWebhookReturnIdxEnum* pEnum)
{
    for (const auto& idx : m_idxSet)
    {
        pEnum->Do(idx);
    }
    return S_OK;
}

//////////////////////EtWebhookDataUpdateSheetsAllChange/////////////////////
STDIMP_(void) EtWebhookDataUpdateSheetsAllChange::Init(IBook* pBook)
{
    m_spParamsValidator.reset(new EtUpdateSheetsAllChangeParamsValidator(pBook));
}

STDIMP EtWebhookDataUpdateSheetsAllChange::EnumSheetIds(IWebhookSheetIdsEnum* pEnum)
{
    for (auto id : m_sheetIds)
    {
        // 当返回false时提前退出
        if (pEnum->Do(id) == FALSE)
            return S_OK;
    }
    return S_OK;
}