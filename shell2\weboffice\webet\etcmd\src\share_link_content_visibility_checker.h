﻿#ifndef __WEBOFFICE_WEBET_SHARE_LINK_CONTENT_VISIBILITY_CHECKER_H__
#define __WEBOFFICE_WEBET_SHARE_LINK_CONTENT_VISIBILITY_CHECKER_H__

interface IDBChartStatisticMgr;
interface IDbDashboardFilterMgr;

namespace wo
{
class ShareLinkContentVisibilityChecker
{
public:
    static std::unique_ptr<ShareLinkContentVisibilityChecker> CreateInstance(IBook* pBook, PCWSTR shareId);
    static std::unique_ptr<ShareLinkContentVisibilityChecker> CreateInstance(IBook* pBook, UINT sheetId);
    virtual ~ShareLinkContentVisibilityChecker() = default;
    virtual bool IsSheetVisible(UINT sheetId) const = 0;
    virtual bool IsSheetPropertyVisible(UINT sheetId) const = 0;
    virtual bool IsSheetViewVisible(UINT sheetId, EtDbId viewId) const = 0;
    virtual bool IsFieldVisible(UINT sheetId, EtDbId fieldId) const = 0;
    virtual bool IsRecordVisible(UINT sheetId, EtDbId recordId) const = 0;
};

class ShareViewContentVisibilityChecker : public ShareLinkContentVisibilityChecker
{
public:
    ShareViewContentVisibilityChecker(IBook* pBook, IDBSheetView* pView, UINT appSheetId);
    ~ShareViewContentVisibilityChecker() override = default;
    bool IsSheetVisible(UINT sheetId) const override;
    bool IsSheetPropertyVisible(UINT sheetId) const override;
    bool IsSheetViewVisible(UINT sheetId, EtDbId viewId) const override;
    bool IsFieldVisible(UINT sheetId, EtDbId fieldId) const override;
    bool IsRecordVisible(UINT sheetId, EtDbId recordId) const override;
private:
    void InitVisibleViews(const IDBSheetView* pView);
    void InsertVisibleView(UINT sheetId, EtDbId viewId);
    std::vector<const IDBIds*> GetVisibleFields(UINT sheetId, const std::unordered_set<EtDbId>& viewIds) const;
    std::vector<const IDBIds*> GetVisibleRecords(UINT sheetId, const std::unordered_set<EtDbId>& viewIds) const;
    bool IsApp() const { return m_appSheetId; }
private:
    IBook* m_pBook;
    UINT m_sheetId;
    UINT m_appSheetId;
    std::unordered_set<EtDbId> m_visibleContentFields; // 内容可见的字段，例如分组字段，看板视图和画册视图的封面字段，这些字段即使隐藏也能看到内容
    std::unordered_map<UINT, std::unordered_set<EtDbId>> m_visibleViews;
    mutable std::unordered_map<UINT, std::vector<const IDBIds*>> m_visibleFields;
    mutable std::unordered_map<UINT, std::vector<const IDBIds*>> m_visibleRecords;
};

class ShareDashBoardContentVisibilityChecker : public ShareLinkContentVisibilityChecker
{
public:
    ShareDashBoardContentVisibilityChecker(IDBDashBoardDataOp* pDbDashboard, ISheet* pSheet);
    ~ShareDashBoardContentVisibilityChecker() override = default;
    bool IsSheetVisible(UINT sheetId) const override;
    bool IsSheetPropertyVisible(UINT sheetId) const override;
    bool IsSheetViewVisible(UINT sheetId, EtDbId viewId) const override;
    bool IsFieldVisible(UINT sheetId, EtDbId fieldId) const override;
    bool IsRecordVisible(UINT sheetId, EtDbId recordId) const override;
private:
    void InitVisibleFields(IDBDashBoardDataOp* pDbDashboard);
    void InitVisibleViews(ISheet* pSheet);
    void CollectVisibleFields(IDBChartStatisticMgr* pChartStatisticMgr);
    void CollectVisibleFields(IDbDashboardFilterMgr* pFilterMgr);
    void InsertVisibleField(UINT sheetId, EtDbId fieldId);
private:
    std::unordered_map<UINT, std::unordered_set<EtDbId>> m_visibleFields;
    std::unordered_map<UINT, std::unordered_set<EtDbId>> m_visibleViews;
    UINT m_sheetId;
};

class ShareGridSheetContentVisibilityChecker : public ShareLinkContentVisibilityChecker
{
public:
    ShareGridSheetContentVisibilityChecker(UINT sheetId);
    ~ShareGridSheetContentVisibilityChecker() override = default;
    bool IsSheetVisible(UINT sheetId) const override;
    bool IsSheetPropertyVisible(UINT sheetId) const override;
    bool IsSheetViewVisible(UINT sheetId, EtDbId viewId) const override;
    bool IsFieldVisible(UINT sheetId, EtDbId fieldId) const override;
    bool IsRecordVisible(UINT sheetId, EtDbId recordId) const override;
private:
    UINT m_sheetId;
};

}   // wo

#endif // __WEBOFFICE_WEBET_SHARE_LINK_CONTENT_VISIBILITY_CHECKER_H__