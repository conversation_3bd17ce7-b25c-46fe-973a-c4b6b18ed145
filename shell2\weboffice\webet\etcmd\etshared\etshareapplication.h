﻿#ifndef ETCONSOLE_H_ET
#define ETCONSOLE_H_ET

#include "etmodules.h"
#include "kxshare/kxcorenotify.h"
#include <QApplication>
#include <public_header/webcommon/src/wokxdialog.h>

namespace wo
{

namespace et
{
#if QT_VERSION < QT_VERSION_CHECK(5, 0, 0)
#define QGuiApplication QApplication
#endif

class KConApplication	: public QApplication
						, public IKShellService
						, public KxAppCoreNotify
						, public KxWindowsCoreNotify
						, public KxDocumentsCoreNotify
						, public KxDocumentCoreNotify
						, public KFakeUnknown<IKPdfExportNotify>
{
	Q_OBJECT

public:
    KConApplication(QString appName);
	KConApplication(QString appName, int &argc, char **argv);
	~KConApplication();

	IKApplication* coreApplication();
	KxKsoModule* libKso();
	void setCoreApplication(IKApplication* coreApp);

	int exec();

// IKShellService
public:
	STDMETHODIMP NewDialog(
		KSO_DialogType,
		IUnknown* objParam,
		IUnknown* envParam,
		IGeneralEvent*,
		void* userData,
		IShellDialog**);
	STDMETHODIMP_(int) MessageBox(
		const BSTR Text,
		const BSTR Caption,
		DWORD Flags);
	STDMETHODIMP_(int) InputBox(
		KSO_InputBoxID,
		IROStringsMap* inParams,
		IROStringsMap** outParams,
		IUnknown* Callback = NULL,
		IUnknown* pDocument = NULL);

	STDMETHODIMP_(IDataClipboard*)	GetDataClipboard();
	STDMETHODIMP_(IApcCompManagerSite*)GetApcCompManagerSite();
	STDMETHODIMP_(HWND)	GetActiveMainWindowHandle();
	STDMETHODIMP_(ISmartLabelsService*) GetSmartLabelService();
	STDMETHODIMP_(IIMEProperty*)  GetImeProperty();
	STDMETHODIMP GetThemeColor(IN INT idx, OUT UINT* Argb);
	STDMETHODIMP Get_CursorIndex(OUT long*);
	STDMETHODIMP Set_CursorIndex(IN long);
	STDMETHODIMP GetThemeImagePath(int, BSTR*);
	STDMETHODIMP_(ShellVersion) GetShellVersion();
	STDMETHODIMP_(int) ShowApiDefaultDialog(BSTR bstrInfo, BOOL* bNotShowAgain);
	STDMETHODIMP_(bool) QConnect(QObject *, const char *, const char *);
	STDMETHODIMP_(bool) IsCloudFile(const QString&);
	STDMETHODIMP_(bool) IsCloudCacheBackupFile(const QString& filePath);
	STDMETHODIMP_(AppShellMode) GetAppShellMode();
	STDMETHODIMP BlockUpdate(BOOL bLock) {return S_OK;}
	STDMETHODIMP GetAppGUIFontName(const char *, QString&);
	STDMETHODIMP CheckFileEditingLock(BSTR bstrFilePath, BOOL &bSaveAs);
	STDMETHODIMP_(int) ShowEdittingLockMessageBox(IN const BSTR text);
	STDMETHODIMP OpenPromeBrowser(QString& url, bool bFromDocArea = false);
	STDMETHODIMP SendDcInfoCollect(const QString& eventName,const QHash<QString,QString>& customArgs);
	STDMETHODIMP SendOpenFileSrcInfoCollect(const QString& eventName, const QString& appName, const QString& from);
	STDMETHODIMP GetAppUIThemePropFontsMap(const QString& className, QMap<QString, QFont>&);
	STDMETHODIMP NotifyPromeMainWindowStateChanged(IKMainWindow* coremw);
	STDMETHODIMP SetLeaveModalActivateProme(bool bActive);
	STDMETHODIMP_(bool) IsNeedLeaveModalActivateProme();
	STDMETHODIMP SetBlockModalActivateProme(bool bBlock);
	STDMETHODIMP_(bool) IsPromeShelllessMode();
	STDMETHODIMP EnsureLoadOfficespace();
	STDMETHODIMP_(IAppCenterDelegate*) GetAppCenterDelegate();

	STDPROC_(int) Get_AppModalLevel();
	STDPROC_(bool) IsDarkModeTheme();
	STDPROC_(bool) startAccessingSecurityScopedResourceWithFilePath(const QString & filepath, bool showAuthorizeDialog = false, bool bRemoveFileBookmark = false);
	STDPROC_(bool) hasAccessingSecurityScopedResourceWithFilePath(const QString & filepath);
	STDPROC_(bool) saveOpenFileToBookmarkWithFilePath(const QString & filepath);
	STDPROC_(bool) isExistWhiteListFonts(QStringList &missFontList);

	STDPROC_(BOOL) IsVbaReady();
	STDPROC_(BOOL) IsJdeReady();
	STDPROC_(BOOL) IsJdeMode();
	STDPROC_(bool) ShowDevelopLanguage();
	STDPROC CoreWaitChartUpdateFinish();
	STDPROC_(void) CoreLogMsg(const QString& moduleName, const QString &msg);
	STDPROC_(void) NotifyPromeCloseMsg(
		const QString& moduleName,
		const QString& msg,
		PromeCloseMsgActionType action,
		const QMap<QString, QString> &extParams);
	STDPROC_(int) ShowInsertPicFailedMessageBox(IN const BSTR Text,
		IN const BSTR Caption,
		IN DWORD Flags,
		IN bool bHasLableLink,
		IN bool bTransReselct,
		OUT bool* pClickedLabel);
	STDPROC_(int) ShowQuitCoopEditMessageBox(
		IN DWORD Flags,
		IN bool bQuiting,
		IN bool bQuitFailed,
		IN bool bCoopNetOK);

	// 判断是否支持触屏特性，该特性目前只在Windows10下生效
	STDPROC_(bool) IsTouchEnabled() const;

	STDPROC_(void) NotifyUrlHyperlinkClick(LPCWSTR pcwHyperAddress);

	// from old IKShellServiceEx
	STDPROC_(int) ExecMessageBox(
		const BSTR Text, 
		const BSTR Caption, 
		DWORD Flags,
		const std::vector<ks_wstring>& ButtonTexts);

	STDPROC_(int) MessageBox4(
		const BSTR Text, const BSTR Caption,
		const BSTR AcceptBtn, const BSTR RejectBtn,
		const BSTR InputPromote, void* dataOut);

	STDPROC_(int) MessageBox3(
		const BSTR Text,
		const BSTR Caption);

	STDPROC_(bool) IsOfficialIndepentComponent();

	STDPROC_(bool) CheckAndOpenbyOtherComponent(const BSTR filePath); // 公文组件和文字组件之间,跳转打开文件

	STDPROC OpenPromeBrowser(const QString& url, bool bFromDocArea = false);
	STDPROC SendDcInfoCollect(const QString& eventName, const QHash<QString, QString>& customArgs, SendMsgProbability pb = SendMsg_All);
	STDPROC SetMultiRecordInfoCollect(MultiRecordInfoType type, bool bBegin);
	STDPROC_(bool) GetHoneycombConfig(qint64 moduleId); // 获取字段类型为bool的蜂巢值
	STDPROC_(bool) GetHoneycombSwitchConfig(qint64 moduleId, bool defaultValue) { return defaultValue; }
	STDPROC_(QVariant) GetHoneycombConfigValue(qint64 moduleId); // 获取全部字段类型的蜂巢值
	STDPROC_(int) ShowCustomMsgBox(CustomMessageBoxType cmbt, PCWSTR pcwsCaption, void* extra);
	STDPROC_(bool) RestrictEmbedFont(IKDocument* pDoc, EmbedFontMode embedMode, bool bNotEmbSysFonts, bool bProtect);
	STDPROC EnterIoTask(UINT breakMode, ThreadLiteLib::IThreadpoolSink* sink);
	STDPROC LeaveIoTask(bool isBroken);
	STDPROC_(bool) IsLastIOBroken();
	STDPROC_(int) ShowMessageBox(MessageBoxParameterBase* pParameter);
	STDPROC_(QColor) GetColorFromTheme(const QString& className, const QString& propName, const QColor& def = QColor());
	STDPROC_(QFont) GetFontFromTheme(const QString& className, const QString& propName, const QFont& def = QFont());
	STDPROC_(int) GetHintFromTheme(const QString& className, const QString& propName, int def = 0, bool* pOk = nullptr);
	STDPROC_(QIcon) GetIcon(const QString& name);
	STDPROC_(bool) LoadApiModule();

private: // coreNotify
	// application
	bool appQueryQuitNotify(IKApplication* coreApp, ksoNotify* ne);
	bool appCreateDocumentNotify(IKDocument* coreDoc, ksoNotify* ne);
	bool appCreateMainWindowNotify(IKMainWindow* coreMainWin, ksoNotify* ne);
	bool appDestroyNotify(IKApplication* coreApp, ksoNotify* ne);
	bool appDataChangedNotify(IKApplication* coreApp, ksoNotify* ne);
	bool appBeforeOpenDocumentNotify(IKDocument* coreDoc, ksoOpenNotify* ne);
	bool appAfterOpenDocumentNotify(IKDocument* coreDoc, ksoOpenNotify* ne);
	bool appCoreNotify(IKApplication* coreApp, ksoNotify* neotify);

	// windows
	bool addWindowNotify(IKWindow* coreWin, ksoNotify* ne);
	bool removeWindowNotify(IKWindow* coreWin, ksoNotify* ne);
	bool windowsActiveNotify(IKWindow* coreWin, ksoNotify* ne);
	bool windowsDeactiveNotify(IKWindow* coreWin, ksoNotify* ne);

	// documents
	bool addDocumentNotify(IKDocument* coreDoc, ksoNotify* ne);
	bool removeDocumentNotify(IKDocument* coreDoc, ksoNotify* ne);
	bool docsActiveNotify(IKDocument* coreDoc, ksoNotify* ne);
	bool docsDeactiveNotify(IKDocument* coreDoc, ksoNotify* ne);

	// document
	bool docBeforeOpenNotify(IKDocument* doc, ksoOpenNotify* ne);
	bool docAfterOpenNotify(IKDocument* doc, ksoOpenNotify* ne);
	bool docBeforeSaveNotify(IKDocument* doc, ksoSaveNotify* ne);
	bool docAfterSaveNotify(IKDocument* doc, ksoSaveNotify* ne);
	bool docQueryCloseNotify(IKDocument* doc, ksoQueryCloseNotify* ne);
	bool docDataChangedNotify(IKDocument* doc, ksoNotify* ne);
	bool docDestroyNotify(IKDocument* doc, ksoNotify* ne);

// IKPdfExportNotify
public:
	STDMETHODIMP OnNofity(KPdfExportNotifyCode code, VARIANT iParam, VARIANT* oParam);
	QString productName();
	QString productVersion();

private:
	enum { LauchQuitApplication = QEvent::User + 1 };
	bool event(QEvent* e);
	bool quitEvent();
	void lauchQuitApplication();

private:
	krt::KAppTranslators* m_translators;
	IKApplication*	m_coreApplication;
	KxDialog m_dialog;
	KxKsoModule	m_ksoLib;
	long m_cursorIndex;
	QString m_productName;
};
}
#define kxApp (static_cast<et::KConApplication *>(et::KConApplication::instance()))
}
#endif // ETCONSOLE_H_ET
