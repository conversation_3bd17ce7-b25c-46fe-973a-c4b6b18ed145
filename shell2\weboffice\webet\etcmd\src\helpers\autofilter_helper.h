#ifndef __LIST_AUTOFILTER_HELPER_H__
#define __LIST_AUTOFILTER_HELPER_H__

#include "etstdafx.h"

namespace AutoFilterHelper
{
    IKAutoFilters *GetAutoFilters(IKWorkbook *workbook, const RANGE &rg);
    IKAutoFilter *GetUserFilter(IKWorkbook *workbook, PCWSTR filterID, const RANGE &rg,bool bGetDefFilter = true);
    IKAutoFilter *GainUserFilter(IKWorkbook *workbook, PCWSTR filterID, const RANGE &rg);
    bool GetFilterByRange(_Worksheet* pSheet, PCWSTR filterID, const RANGE& rg, AutoFilter** ppAutoFilter, ListObject** pListObject);

    HRESULT ApiAutoFilterToCore(AutoFilter *pApi, IKAutoFilter **ppCore);
    CELL GetFirstResultCell(IKWorksheet* pWorksheet, IKAutoFilter *pAutoFilter, bool *bCrossFrozen = NULL);
    RECT GetFilterResultRect(IKWorksheet* pWorksheet, IKAutoFilter *pAutoFilter);
    RECT GetFilterButtonRect(IKWorksheet* pWorksheet, IKAutoFilter *pAutoFilter);
}

#endif // __LIST_AUTOFILTER_HELPER_H__