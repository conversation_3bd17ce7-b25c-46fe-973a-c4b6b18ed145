﻿#include "etstdafx.h"
#include "KjEtRangeEx.h"
#include "utils/attachment_utils.h"
#include "webetlink.h"
#include <kso/api/smartparam.h>
#include <kycorenotify.h>
#include "et_revision_context_impl.h"
#include "helpers/jsapi_helper.h"
#include "applogic/cell_image_helper.h"
#include "helpers/jsapi_record.h"
#include "workbook.h"
#include "api/jsapi/jscefservice/jscefservice_intf.h"

extern Callback* gs_callback;

namespace wo
{

FnJSAPIGet KjWOEtRangeEx::s_fnBaseGet = nullptr;
FnJSAPISet KjWOEtRangeEx::s_fnBaseSet = nullptr;
FnJSAPIExecute KjWOEtRangeEx::s_fnBaseExecute = nullptr;
FnJSGetProperties KjWOEtRangeEx::s_fnBaseGetProps = nullptr;
int KjWOEtRangeEx::s_nInsertCellPictureRaw = -1;
int KjWOEtRangeEx::s_nCreate = INVALID_CLASSIDENTIFIER;

namespace
{
    bool GetParams(bool bAttachment, JSApiArguments& arguments, KJSVariant& id, KJSVariant& url, int &width, int &height)
    {
        if (!bAttachment && arguments[1].IsStringType())
        {
            id = arguments[1];
            return true;
        }
        else if (bAttachment && arguments[1].IsInterfaceType())
        {
            ks_stdptr<IJSONValue> spJson = V_UNKNOWN(&arguments[1]);
            if (!spJson)
            {
                WOLOG_ERROR << "[JsApi] RangeEx: arg1 not json.";
                return false;
            }
            spJson->GetValue("picId", &id);
            spJson->GetValue("url", &url);
            if (!id.IsStringType() || !url.IsStringType())
            {
                WOLOG_ERROR << "[JsApi] RangeEx: get picId and url failed.";
                return false;
            }

            KJSVariant w;
            KJSVariant h;
            spJson->GetValue("width", &w);
            spJson->GetValue("height", &h);
            if (!w.IsIntergertype() || !h.IsIntergertype())
            {
                WOLOG_ERROR << "[JsApi] RangeEx: get width and height failed.";
                return false;
            }
            KJsApiHelper::Pixel2Twip(w.GetInt(), h.GetInt(), width, height);
            return true;
        }
        WOLOG_ERROR << "[JsApi] RangeEx: arg1 invalid.";
        return false;
    }
}

HRESULT KjWOEtRangeEx::GetInMainThread(bool bCefEngine,
	const JSContextEnv& jsEnv,
	int nAttributeIdx,
	JSInputData inputData,
	KJSVariant& retval,
	JSApiObject *pJSApiObj,
	KJSVariant& exception)
{
	return s_fnBaseGet(bCefEngine, jsEnv, nAttributeIdx, inputData,
		retval, pJSApiObj, exception);
}

HRESULT KjWOEtRangeEx::SetInMainThread(const JSContextEnv& jsEnv,
	int nAttributeIdx,
	JSInputData inputData,
	KJSVariant& value,
	KJSVariant& exception)
{
	return s_fnBaseSet(jsEnv, nAttributeIdx, inputData, value, exception);
}

HRESULT KjWOEtRangeEx::ExecuteInMainThread(bool bCefEngine,
	const JSContextEnv& jsEnv,
	int nAttributeIdx,
	JSInputData inputData,
	JSApiArguments& arguments,
	KJSVariant& retval,
	JSApiObject *pJSApiObj,
	KJSVariant& exception)
{
	ks_stdptr<IKCoreNotify> spCoreNotify = reinterpret_cast<IUnknown*>(inputData);
	KyCoreNotifyBase* pBase = static_cast<KyCoreNotifyBase*>(spCoreNotify.get());
	if (!pBase)
		return E_FAIL;
	ks_stdptr<oldapi::EtRangeEx> spEtRangeEx = pBase->coreObj();
	if (!spEtRangeEx)
		return E_FAIL;
	if (s_nInsertCellPictureRaw == nAttributeIdx)
	{
		if (2 > arguments.size())
        {
            WOLOG_ERROR << "[JsApi] RangeEx args.size < 2.";
			return E_FAIL;
        }
		bool bAttachment = (VARIANT_FALSE != V_BOOL(&arguments[0]));
        KJSVariant varId, varUrl;
        int width = -1, height = -1;
        if (!GetParams(bAttachment, arguments, varId, varUrl, width, height))
            return E_FAIL;
		PCWSTR id = varId.GetString();
		PCWSTR url = varUrl.GetString();
        if (!id || *id == __Xc('\0'))
        {
            WOLOG_ERROR << "[JsApi] RangeEx pic id is empty.";
            return E_FAIL;
        }
		ks_wstring name;
		KJsApiHelper::GetPicUuid(id, name);

		QString strPathData;
        ks_wstring strfakeUrl;
		if (bAttachment)
		{
			strfakeUrl = util::generateFakeAttachmentUrl(id, url);
		}
		else
		{
			QByteArray strIdUtf8 = krt::fromUtf16(id).toUtf8();
			WebInt count = 0;
			WebMimeData* pMineDatas = nullptr;
			gs_callback->getMimeDatas(strIdUtf8.data(), &count, &pMineDatas);
			if (pMineDatas == nullptr || count < 1 || pMineDatas[0].path == nullptr)
			{
				WOLOG_ERROR << "[JsApi] RangeEx get pic path from mime failed: " << strIdUtf8.data();
				return E_FAIL;
			}
			strPathData = QString::fromUtf8(pMineDatas[0].path);
		}

		ks_stdptr<etoldapi::Range> spRange;
		HRESULT hr = spEtRangeEx->GetRange(&spRange);
		if (FAILED(hr))
			return hr;
		ks_stdptr<_Worksheet> spWorksheet;
		spRange->get_Worksheet(&spWorksheet);
		if (!spWorksheet)
			return E_FAIL;

		CELL cell = { 0 };
		if (!app_helper::IsSingleCell(spRange, &cell, FALSE))
			return E_INVALIDARG;

		ks_stdptr<IKShape> spShape;
		hr = spWorksheet->InsertCellPicture(cell.row, cell.col, krt::utf16(strPathData),
			name, strfakeUrl.c_str(), width, height, FALSE, &spShape);
		if (SUCCEEDED(hr))
		{
			KEtWorkbook* pWb = KJsApiHelper::CurrentWorkbook();
			KEtRevisionContext* pCtx = KJsApiHelper::CurrentContext();
			if (pWb && (!pCtx || !pCtx->isExecDirect()))
			{
				JsApiExecRecord record;
				record.type = JSRecordType::PicUuid;
				record.scriptName = id;
				record.taskId = name.c_str();
				pWb->getApiRecords().push_back(record);
			}
		}
		return hr;
	}
	return s_fnBaseExecute(bCefEngine, jsEnv, nAttributeIdx, inputData,
		arguments, retval, pJSApiObj, exception);
}

void KjWOEtRangeEx::GetProperties(JSInputData inputData,
	const JSContextEnv& jsEnv,
	FnJSAPIGet &fnGet,
	FnJSAPISet &fnSet,
	FnJSAPIExecute &fnExecute,
	IJSPropertyList *pProps)
{
	s_fnBaseGetProps(inputData, jsEnv, fnGet, fnSet, fnExecute, pProps);
	if (!s_fnBaseExecute)
	{
		s_fnBaseGet = fnGet;
		s_fnBaseSet = fnSet;
		s_fnBaseExecute = fnExecute;
		pProps->GetIdx(__X("InsertCellPictureRaw"), s_nInsertCellPictureRaw);
	}
	fnGet = KjWOEtRangeEx::GetInMainThread;
	fnSet = KjWOEtRangeEx::SetInMainThread;
	fnExecute = KjWOEtRangeEx::ExecuteInMainThread;
}

void KjWOEtRangeEx::GetFunction(bool bCefEngine, JSApiObject* pJSApiObj)
{
	pJSApiObj->nCreate = s_nCreate;
	pJSApiObj->fnGetProperties = KjWOEtRangeEx::GetProperties;
}

} // namespace wo