﻿#ifndef __ET_APPCORE_WEBHOOK_DATA_H__
#define __ET_APPCORE_WEBHOOK_DATA_H__

#include "appcore/et_appcore_dbsheet.h"
#include "appcore/et_appcore_webhook.h"
#include "webhook_param_validator.h"
#include <vector>
#include "webhook/webhook_common.h"
#include "webhook/webhook_basic_itf.h"

//////////////////////WebhookDataBase/////////////////////
class WebhookDataBase : public IWebhookData
{
public:
    DECLARE_INSTANCE_COUNTER(WebhookDataBase)
    DECLARE_CLASS(WebhookDataBase)
    BEGIN_QUERYINTERFACE(IWebhookData)
    END_QUERYINTERFACE()
    STDPROC_(PCWSTR) GetId() const override { return m_id.c_str(); }
    STDPROC_(void) SetId(PCWSTR id) override { m_id = id; }
    STDPROC_(PCWSTR) GetUserId() const override { return m_userId.c_str(); }
    STDPROC_(void) SetUserId(PCWSTR id) override { m_userId = id; }
    STDPROC_(void) GetRange(DbWebhookRange* pRg) const override { *pRg = m_dbRange; }
    STDPROC_(void) SetRange(DbWebhookRange& rg) override { m_dbRange = rg; }
    STDPROC_(DbWebhookRange *) GetDbRange() const override { return &m_dbRange; }
    STDPROC_(DbWebhookQueryRange *) GetQueryRange() const override {return &m_dbQueryRange;}
    STDPROC_(void) SetQueryRange(DbWebhookQueryRange &rg) override {m_dbQueryRange = rg;}
    STDPROC_(void) GetHookCache(WebHookCache*& pCache) override {pCache = &m_webHookCache;}
    STDPROC_(void) SetHookCache(WebHookCache& cache) override {m_webHookCache = cache;}
    STDPROC_(void) ClearHookCache() override {m_webHookCache.Clear();}
    STDPROC_(UINT32) GetHookCacheSize() const override;
    STDPROC_(void) GetRange(EtWebhookRange* pRg) const override { *pRg = m_etRange; }
    STDPROC_(void) SetRange(EtWebhookRange& rg) override { m_etRange = rg; }
    STDPROC_(EtWebhookRange *) GetEtRange() const override { return &m_etRange; }
    STDPROC_(void) GetRanges(EtWebhookRanges* pRgs) const override { *pRgs = m_etRanges; }
    STDPROC_(void) SetRanges(EtWebhookRanges& rgs) override { m_etRanges = rgs; }
    STDPROC_(EtWebhookRanges *) GetEtRanges() const override { return &m_etRanges; }
    STDPROC_(void) GetCharts(EtWebhookCharts* pCharts) const override { *pCharts = m_etCharts;};
    STDPROC_(void) SetCharts(EtWebhookCharts& charts) override { m_etCharts = charts;};
    STDPROC_(EtWebhookCharts *) GetEtCharts() const override { return &m_etCharts; }

    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_invalid; }
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_invalid; }

    // filter
    STDPROC_(void) GetFilter(IDbExtraFilter** ppFilter) const override {};
    STDPROC_(void) SetFilter(IDbExtraFilter* pFilter) override {};
    STDPROC_(void) GetFilterResult(IDbExtraFilter** ppFilter) const override {};
    STDPROC_(void) SetFilterResult(IDbExtraFilter* pFilter) override {};

    // support sheets
    STDPROC_(void) SetAllSheets(BOOL b) override {};
    STDPROC_(BOOL) GetAllSheets() const override { return false; };
    STDPROC_(void) InsertSheetId(UINT sheetId) override {}
    STDPROC_(BOOL) IsMultiSheet() const override { return false; }
    STDPROC EnumSheetIds(IWebhookSheetIdsEnum* pEnum) override { return E_FAIL; }

    // collect formula change
    STDPROC_(void) SetCollectFormulaResChange(BOOL b) override {}
    STDPROC_(BOOL) GetCollectFormulaResChange() const override { return false; }

    // et extra return cols
    STDPROC_(void) SetExtraReturnRowCols(const INT* pIdxs, UINT size) override {}
    STDPROC EnumExtraReturnRowCols(IWebhookReturnIdxEnum* pEnum) override { return E_FAIL; }

    STDPROC_(BOOL) IsDbSheet() const override;
    STDPROC_(BOOL) FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId) { return false; }
    STDPROC_(void) AddNeedCheckRecord(EtDbId id) override {};
    STDPROC_(void) EnumNeedCheckRecord(IWebhookDbIdsEnum* pEnum) override {};
    STDPROC_(void) DeleteNeedCheckRecord(EtDbId id) override {};
    STDPROC_(void) SetSkipAfterMatchCreateAndFill(BOOL b) override {};
    STDPROC_(BOOL) GetSkipAfterMatchCreateAndFill() const override { return FALSE; };
    STDPROC UnHook() override { return S_OK; }
    STDPROC_(UINT) GetSheetIdsSize() const { return 0; }
    STDPROC_(UINT) GetExtraReturnRowColsSize() const override {return 0;}
    STDPROC_(bool) HasFilter() const override { return false; }
    STDPROC_(bool) HasCreateAndFillWithField() const override { return false; }

public:
    BOOL ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res);

protected:
    void CreateServerId(BSTR *pStr);
protected:
    ks_wstring m_id = __X("");
    ks_wstring m_userId = __X("");
    std::unique_ptr<ParamsValidator> m_spParamsValidator;
    mutable DbWebhookRange m_dbRange;
    mutable DbWebhookQueryRange m_dbQueryRange;
    mutable EtWebhookRange m_etRange;
    mutable EtWebhookRanges m_etRanges;
    mutable EtWebhookCharts m_etCharts;
    BOOL m_bCollectFormulaResChange = false;
    WebHookCache m_webHookCache;
};

//////////////////////WebhookDataCreateSheet/////////////////////
class WebhookDataCreateSheet : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook) {};
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_createSheet; }
};

//////////////////////WebhookDataRemoveSheet/////////////////////
class WebhookDataRemoveSheet : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_removeSheet; }
};

//////////////////////WebhookDataRenameSheet/////////////////////
class WebhookDataRenameSheet : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_renameSheet; }
};

//////////////////////WebhookDataUpdateSheetDesc/////////////////////
class WebhookDataUpdateSheetDesc : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateSheetDesc; }
};

//////////////////////WebhookDataUpdateSheetIcon/////////////////////
class WebhookDataUpdateSheetIcon : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateSheetIcon; }
};

//////////////////////WebhookDataCreateView/////////////////////
class WebhookDataCreateView : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_createView; }
};

//////////////////////WebhookDataRenameView/////////////////////
class WebhookDataRenameView : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_renameView; }
};

//////////////////////WebhookDataRemoveView/////////////////////
class WebhookDataRemoveView : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_removeView; }
};

//////////////////////WebhookDataCreateField/////////////////////
class WebhookDataCreateField : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_createField; }
};

//////////////////////WebhookDataUpdateField/////////////////////
class WebhookDataUpdateField : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateField; }
};

//////////////////////WebhookDataUpdatePrimaryField/////////////////////
class WebhookDataUpdatePrimaryField : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updatePrimaryField; }
};

//////////////////////WebhookDataRemoveField/////////////////////
class WebhookDataRemoveField : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_removeField; }
};

//////////////////////WebhookDataCreateRecord/////////////////////
class WebhookDataCreateRecord : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_createRecord; }
};

//////////////////////WebhookDataRemoveRecord/////////////////////
class WebhookDataRemoveRecord : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_removeRecord; }
};

//////////////////////WebhookDataUpdateRecordsParent/////////////////////
class WebhookDataUpdateRecordsParent : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateRecordsParent;}
};


//////////////////////WebhookDataUpdateCells/////////////////////
class WebhookDataUpdateCells : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateCells; }
    STDPROC_(BOOL) FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId) override;
    STDPROC_(void) SetCollectFormulaResChange(BOOL b) override { m_bCollectFormulaResChange = b; }
    STDPROC_(BOOL) GetCollectFormulaResChange() const override { return m_bCollectFormulaResChange; }
    STDPROC_(void) SetSkipAfterMatchCreateAndFill(BOOL b) override { m_bSkipAfterMatchCreateAndFill = b; };
    STDPROC_(BOOL) GetSkipAfterMatchCreateAndFill() const override { return m_bSkipAfterMatchCreateAndFill; };
private:
    BOOL m_bSkipAfterMatchCreateAndFill = FALSE;
};

//////////////////////WebhookDataUpdateSheet/////////////////////
class WebhookDataUpdateSheet : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateSheet; }
    STDPROC_(BOOL) FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId) override;
    STDPROC_(void) SetCollectFormulaResChange(BOOL b) override { m_bCollectFormulaResChange = b; }
    STDPROC_(BOOL) GetCollectFormulaResChange() const override { return m_bCollectFormulaResChange; }
    STDPROC_(void) SetSkipAfterMatchCreateAndFill(BOOL b) override { m_bSkipAfterMatchCreateAndFill = b; };
    STDPROC_(BOOL) GetSkipAfterMatchCreateAndFill() const override { return m_bSkipAfterMatchCreateAndFill; };
private:
    BOOL m_bSkipAfterMatchCreateAndFill = FALSE;
};

//////////////////////WebhookDataUpdateFieldCells/////////////////////
class WebhookDataUpdateFieldCells : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateFieldCells; }
    STDPROC_(BOOL) FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId) override;
    STDPROC_(void) SetCollectFormulaResChange(BOOL b) override { m_bCollectFormulaResChange = b; }
    STDPROC_(BOOL) GetCollectFormulaResChange() const override { return m_bCollectFormulaResChange; }
    STDPROC_(void) SetSkipAfterMatchCreateAndFill(BOOL b) override { m_bSkipAfterMatchCreateAndFill = b; };
    STDPROC_(BOOL) GetSkipAfterMatchCreateAndFill() const override { return m_bSkipAfterMatchCreateAndFill; };
private:
    BOOL m_bSkipAfterMatchCreateAndFill = FALSE;
};

//////////////////////WebhookDataUpdateRecordCells/////////////////////
class WebhookDataUpdateRecordCells : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateRecordCells; }
    STDPROC_(BOOL) FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId) override;
    STDPROC_(void) SetCollectFormulaResChange(BOOL b) override { m_bCollectFormulaResChange = b; }
    STDPROC_(BOOL) GetCollectFormulaResChange() const override { return m_bCollectFormulaResChange; }
    STDPROC_(void) SetSkipAfterMatchCreateAndFill(BOOL b) override { m_bSkipAfterMatchCreateAndFill = b; };
    STDPROC_(BOOL) GetSkipAfterMatchCreateAndFill() const override { return m_bSkipAfterMatchCreateAndFill; };
private:
    BOOL m_bSkipAfterMatchCreateAndFill = FALSE;
};

//////////////////////WebhookDataUpdateRecords/////////////////////
class WebhookDataUpdateRecords : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateRecords; }
    STDPROC_(BOOL) FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId) override;
    STDPROC_(void) GetFilter(IDbExtraFilter** ppFilter) const override;
    STDPROC_(void) SetFilter(IDbExtraFilter* pFilter) override;
    STDPROC_(void) GetFilterResult(IDbExtraFilter** ppFilter) const override;
    STDPROC_(void) SetFilterResult(IDbExtraFilter* pFilter) override;
    STDPROC_(void) SetCollectFormulaResChange(BOOL b) override { m_bCollectFormulaResChange = b; }
    STDPROC_(BOOL) GetCollectFormulaResChange() const override { return m_bCollectFormulaResChange; }
    STDPROC_(void) SetSkipAfterMatchCreateAndFill(BOOL b) override { m_bSkipAfterMatchCreateAndFill = b; };
    STDPROC_(BOOL) GetSkipAfterMatchCreateAndFill() const override { return m_bSkipAfterMatchCreateAndFill; };
    STDPROC_(bool) HasFilter() const override { return m_spFilter && m_spFilterResult; }

    private:
    ks_stdptr<IDbExtraFilter> m_spFilter;
    ks_stdptr<IDbExtraFilter> m_spFilterResult;
    BOOL m_bSkipAfterMatchCreateAndFill = FALSE;
};

//////////////////////WebhookDataUpdateSheetsAllChange/////////////////////
class WebhookDataUpdateSheetsAllChange : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_updateSheetsAllChange; }
    STDPROC_(BOOL) FindCell(UINT sheetId, EtDbId recordId, EtDbId fieldId) override;
    STDPROC_(void) SetAllSheets(BOOL b) override { m_bAllSheets = b; };
    STDPROC_(BOOL) GetAllSheets() const override { return m_bAllSheets; };
    STDPROC_(void) InsertSheetId(UINT sheetId) override { m_sheetIds.insert(sheetId); }
    STDPROC EnumSheetIds(IWebhookSheetIdsEnum* pEnum) override;
    STDPROC_(BOOL) IsMultiSheet() const override { return true; }
    STDPROC_(void) SetCollectFormulaResChange(BOOL b) override { m_bCollectFormulaResChange = b; }
    STDPROC_(BOOL) GetCollectFormulaResChange() const override { return m_bCollectFormulaResChange; }
    STDPROC_(UINT) GetSheetIdsSize() const override {return static_cast<UINT>(m_sheetIds.size());}

private:
    BOOL m_bAllSheets = false;
    std::set<UINT> m_sheetIds;
};

//////////////////////WebhookDataCreateAndFillInRecord/////////////////////
class WebhookDataCreateAndFillInRecord : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_DbSheet_WebhookType &type) const override { type = DbSheet_WebhookType_createAndFillInRecord; }
    STDPROC_(void) AddNeedCheckRecord(EtDbId id) override;
    STDPROC_(void) EnumNeedCheckRecord(IWebhookDbIdsEnum* pEnum) override;
    STDPROC_(void) DeleteNeedCheckRecord(EtDbId id) override;
    STDPROC_(bool) HasCreateAndFillWithField() const override;

private:
    std::unordered_set<EtDbId> m_needCheckRecords;
};

//////////////////////EtWebhookDataUpdateSheet/////////////////////
class EtWebhookDataUpdateSheet : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_etUpdateSheet; }
};

//////////////////////EtWebhookDataRemoveSheet/////////////////////
class EtWebhookDataRemoveSheet : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_etRemoveSheet; }
};

//////////////////////EtWebhookDataInsertRows/////////////////////
class EtWebhookDataInsertRows : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_etInsertRows; }
};

//////////////////////EtWebhookDataRemoveRows/////////////////////
class EtWebhookDataRemoveRows : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_etRemoveRows; }
    STDPROC_(void) SetExtraReturnRowCols(const INT* pIdxs, UINT size) override;
    STDPROC EnumExtraReturnRowCols(IWebhookReturnIdxEnum* pEnum) override;
    STDPROC_(UINT) GetExtraReturnRowColsSize() const override {return static_cast<UINT>(m_idxSet.size());}

private:
    std::set<INT> m_idxSet;
};

//////////////////////EtWebhookDataInsertCols/////////////////////
class EtWebhookDataInsertCols : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_etInsertCols; }
};

//////////////////////EtWebhookDataUpdateRanges/////////////////////
class EtWebhookDataUpdateRanges : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_updateRanges; }
    IBook* m_pBook;
};

//////////////////////EtWebhookDataUpdateCharts/////////////////////
class EtWebhookDataUpdateCharts : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_updateCharts; }
};

//////////////////////EtWebhookDataRemoveCols/////////////////////
class EtWebhookDataRemoveCols : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_etRemoveCols; }
    STDPROC_(void) SetExtraReturnRowCols(const INT* pIdxs, UINT size) override;
    STDPROC EnumExtraReturnRowCols(IWebhookReturnIdxEnum* pEnum) override;
    STDPROC_(UINT) GetExtraReturnRowColsSize() const override {return static_cast<UINT>(m_idxSet.size());}

private:
    std::set<INT> m_idxSet;
};

//////////////////////EtWebhookDataUpdateRange/////////////////////
class EtWebhookDataUpdateRange : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_etUpdateRange; }
    STDPROC_(void) SetExtraReturnRowCols(const INT* pIdxs, UINT size);
    STDPROC EnumExtraReturnRowCols(IWebhookReturnIdxEnum* pEnum);
    STDPROC_(UINT) GetExtraReturnRowColsSize() const override {return static_cast<UINT>(m_idxSet.size());}

private:
    std::set<INT> m_idxSet;
};

//////////////////////EtWebhookDataUpdateSheetsAllChange/////////////////////
class EtWebhookDataUpdateSheetsAllChange : public WebhookDataBase
{
public:
    STDPROC_(void) Init(IBook* pBook);
    STDPROC_(void) GetType(Et_WebhookType &type) const override { type = Et_WebhookType_etUpdateSheetsAllChange; }
    STDPROC_(void) SetAllSheets(BOOL b) override { m_bAllSheets = b; };
    STDPROC_(BOOL) GetAllSheets() const override { return m_bAllSheets; };
    STDPROC_(void) InsertSheetId(UINT sheetId) override { m_sheetIds.insert(sheetId); }
    STDPROC EnumSheetIds(IWebhookSheetIdsEnum* pEnum) override;
    STDPROC_(BOOL) IsMultiSheet() const override { return true; }
    STDPROC_(UINT) GetSheetIdsSize() const override {return static_cast<UINT>(m_sheetIds.size());}

private:
    BOOL m_bAllSheets = false;
    std::set<UINT> m_sheetIds;
};
#endif // __ET_APPCORE_DBSHEET_WEBHOOK_MANAGER_H__
