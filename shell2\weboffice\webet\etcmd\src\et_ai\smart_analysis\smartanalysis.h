﻿#ifndef __SMARTANALYSIS_H__
#define __SMARTANALYSIS_H__

namespace etai
{
// 功能接口
interface ISmartUnderstand;

enum KParseJsonErrorEnum
{
    JsonDataReceive = -1, // 数据还没发回来
    JsonErrorNone = 0,
    JsonErrorNotObj,        // 返回数据有误
    JsonErrorNotPass,       // 超过可分析的非空单元格数
    JsonErrorDecrypt,       // 解密失败
    JsonErrorTransformJson, // 解析结果有错
    JsonErrorEmpty,         // 结果为空
    JsonErrorParseFailed,   // 插入清洗数据表失败or解析数据失败。
    JsonErrorNoNetWork,     // 发送dataming发现数据请求失败
    JsonErrorBookNameFail,  // 文件名包含[]错误
};

interface ISmartAnalysisListener
{
  public:
    virtual void Destory() = 0;
    virtual void UpdateCommand() = 0;
    virtual ULONG AddRef(void) = 0;
    virtual ULONG Release(void) = 0;
    virtual void stop() = 0;
    virtual bool isStop() = 0;
};

interface ISmartAnalysis
{
  public:
    virtual ~ISmartAnalysis()
    {
    }
    // 获取发给服务器分析的Json数据
    virtual BOOL GetIdentifyData(QJsonObject & jsonObj) = 0;

    // 接收服务端返回的Json数据
    virtual HRESULT ParseJsonObj(QJsonArray & jsonObj) = 0;
    // 是否有分析数据透视表
    virtual BOOL HaveAnalysisPivotTable() = 0;
    // 切换当前选择的分析
    virtual HRESULT SelectAnalysis(IKWorksheet * pWorksheet) = 0;
    // 添加空白的分析
    virtual HRESULT NewAnalysis() = 0;
    // 刷新数据透视表
    virtual HRESULT Refresh() = 0;
    // 是否插入清洗数据
    virtual BOOL IsInsertCleanTable(int *pEnum) = 0;
    // 是否选择数据透视表
    virtual BOOL IsSelectionPivotTable() = 0;

    // 获取字段列表
    virtual HRESULT GetFieldList(QJsonArray & jsonObj) = 0;
    // 获取某个字段完整信息
    virtual HRESULT GetField(QString strField, QJsonObject & jsonObj) = 0;
    // 获取使用到的字段列表
    virtual QStringList GetUsedFieldList() = 0;
    // 获取使用到的筛选器字段列表
    virtual QStringList GetUsedPageFieldList() = 0;
    // 获取当前分析的行字段、列字段、值字段名字列表
    virtual void GetPivotTableFields(QStringList & qslRowFields, QStringList & qslColFields,
                                     QStringList & qslDataFields) = 0;
    // 获取所有字段列表名字
    virtual QStringList GetAllFieldList() = 0;
    // 获取按添加顺序的使用到的字段列表，目前仅图谱使用
    virtual QStringList GetInsertFieldList() = 0;

    // 添加字段
    virtual HRESULT InsertField(QString strField, bool isPage, bool bSelect = true, bool bCleanUR = false) = 0;
    // 删除字段
    virtual HRESULT DeleteField(QString strField, bool bSelect = true, bool bCleanUR = false) = 0;
    // 将数据透视表已使用字段清空后，加入需要的字段列表
    virtual HRESULT ReplaceFields(QStringList & qslFields, bool bSelect = true, bool bCleanUR = false) = 0;
    // 获取某字段项的筛选信息（联动）
    virtual HRESULT GetFilterInfo(QString strField, QString strItem, QJsonArray & jsonObj) = 0;
    // 字符串类型字段按计数个数排序
    virtual HRESULT SortStringFieldCount(QString strField, bool bDes, QJsonObject &jsonObj) = 0;
    // 重置字段列表选择的状态，包括字符串字段改为降序，日期字段改为按日显示且为降序
    virtual HRESULT resetState() = 0;
    // 重新读取数据透视表使用到的行、列、值字段，用于更新使用到的字段，pReload为TRUE表示有变化，需要刷新
    virtual HRESULT ResetPivotTable(BOOL * pReload) = 0;

    // 获取字段图谱数据
    virtual HRESULT GetFieldMapJsonObj(QJsonObject & jsonObj) = 0;
    // 获取字段图谱某个字段详细信息
    virtual HRESULT GetFieldMapInfoJsonObj(QString strField, QJsonObject & jsonObj) = 0;
    // 进入图谱新建图谱专用的分析工作表
    virtual HRESULT InsertFieldMapPivotTable() = 0;
    // 退出图谱删除图谱专用的分析工作表
    virtual HRESULT DeleteFieldMapPivotTable() = 0;
    // 获取图谱工作表
    virtual IKWorksheet *GetFieldMapWorksheet(bool bDel = false) = 0;

    // 获取当前分析的数据，用于给服务端进行解读
    virtual HRESULT GetActiveAnalysisData(QJsonObject & jsonObj) = 0;
    // 当前分析是否为空白分析
    virtual BOOL IsEmptyAnalysis() = 0;
    // 当前分析是否有维度，即行字段或者列字段
    virtual BOOL HasDimension() = 0;
    // 数据是否已准备好
    virtual BOOL IsReady() = 0;
    // 获取字段个数
    virtual INT GetFieldCount() = 0;
    // 获取源数据行数
    virtual INT GetFieldRowCount() = 0;
    // 获取源数据非空单元格数
    virtual INT GetNotEmptyCellCount() const = 0;
    // 获取源数据sheet服务端返回的表格个数
    virtual INT GetCleanTableCount() const = 0;
    // 获取当前分析的Worksheet
    virtual IKWorksheet *GetActiveAnalysisWorksheet() = 0;
    // 获取源数据的Worksheet
    virtual IKWorksheet *GetSourceWorksheet() = 0;
    // 获取创建智能分析的Worksheet
    virtual IKWorksheet *GetNewAnalysisWorksheet() = 0;
    // 当前分析是否标脏
    virtual BOOL IsActiveAnalysisDirty() = 0;
    // 设置当前分析标脏
    virtual HRESULT SetActiveAnalysisDirty() = 0;
    // 当删除工作表时，检查更新我的分析列表和当前分析
    virtual HRESULT UpdateOnDeleteSheet(IKWorksheet * pDelSheet) = 0;

    // 解读插入图表时调用记录状态
    virtual void SetInsertChart(BOOL b) = 0;
    virtual BOOL GetInsertChart() const = 0;

    // 获取是否是调用NewAnalysis创建的新工作表
    virtual BOOL GetIsNewSheet() const = 0;
    virtual void SetIsNewSheet(BOOL b) = 0;

    // 获取解读接口
    virtual ISmartUnderstand *GetUnderstand() = 0;
    // 清除当前分析的解读数据
    virtual void CleanUnderstand() = 0;
    // 是否已生成解读数据
    virtual BOOL HasUnderstand() = 0;
    // 获取某个字段的类型信息
    virtual HRESULT GetFieldTypeInfo(QString strField, QString & strType, QString & strFieldMapType) = 0;

    // 是否推荐排序
    virtual BOOL GetIsRecommend(bool isSource) = 0;
    virtual void SetIsRecommend(BOOL isRecommend, bool isSource) = 0;
    // 是否仅显示正在分析的字段
    virtual BOOL GetIsOnlyUsed() = 0;
    virtual void SetIsOnlyUsed(BOOL isOnlyUsed) = 0;
    // 设置查找的字段名称
    virtual QString GetFindField() = 0;
    virtual void SetFindField(QString strName) = 0;
    // 获取数据解析错误信息
    virtual KParseJsonErrorEnum GetParseJsonError() = 0;
    virtual void SetParseJsonError(KParseJsonErrorEnum err) = 0;
    // 获取使用的数值字段的数值计算类型
    virtual int GetUsedNumberFieldTypeList(std::vector<std::pair<QString, QString>> & vecNumFieldType) = 0;
    // 添加监听smartanalysis是否被删除的监听器
    virtual void addListener(ISmartAnalysisListener *) = 0;
    virtual void removeListener(ISmartAnalysisListener *) = 0;
    // 获取某字段项数
    virtual INT GetFieldItemCount(QString strName) const = 0;
    // 获取图谱是否展开
    virtual BOOL GetFieldMapIsDisplay() = 0;
    virtual void SetFieldMapIsDisplay(BOOL b) = 0;
    // 是否首次打开数据源
    virtual void SetIsFirstSource(BOOL b) = 0;
    virtual BOOL GetIsFirstSource() const = 0;
    // 是否首次打开图谱
    virtual void SetIsFirstDispalyFieldMap(BOOL b) = 0;
    virtual BOOL GetIsFirstDispalyFieldMap() const = 0;
    // 插入数据是否中断
    virtual void SetIsStop(BOOL b) = 0;
    virtual BOOL GetIsStop() const = 0;
    // 获取某个字段在所有字段的推荐排序
    virtual INT GetFieldPredValueIndex(QString strName) const = 0;
    // 获取和设置字段列表是否打开
    virtual BOOL GetFieldListIsOpen() = 0;
    virtual void SetFieldListIsOpen(BOOL) = 0;
    // 检查当前数据透视表对象是否有效，如无效则更新数据透视表对象
    virtual BOOL ResetApiPivotTable() = 0;
    // 是否当前分析不发送解读
    virtual BOOL GetIsNotRequestUnderstand() = 0;
    // 获取推荐解读接口
    virtual ISmartUnderstand *GetRecommendUnderstand() = 0;
    // 将推荐解读片段插入分析方案
    virtual HRESULT InsertRecommendAnalysis(QString strName) = 0;
    // 是否已发送识别请求
    virtual BOOL GetDoRecognize() = 0;
    // 设置已发送识别请求
    virtual void SetDoRecognize(BOOL) = 0;
    // 获取数据源场景分类结果
    virtual QString GetLabel() const = 0;
};

interface ISmartAnalysisManager
{
  public:
    virtual ~ISmartAnalysisManager()
    {
    }
    // 获取当前数据源
    virtual ISmartAnalysis *GetActiveSmartAnalysis() = 0;
    virtual void SetActiveSmartAnalysis(ISmartAnalysis * pAnalysis) = 0;
    // 设置当前数据源
    virtual HRESULT SelectAnalysis(IKWorksheet * pSheet) = 0;
    // 新增数据源
    virtual HRESULT AddAnalysis(IKWorksheet * pSheet) = 0;
    // 删除数据源
    virtual HRESULT DeleteAnalysis(ISmartAnalysis * pAnalysis) = 0;
    virtual HRESULT DeleteAnalysis(IKWorksheet * pSheet) = 0;
    virtual HRESULT DeleteAnalysis(IKWorkbook * pBook) = 0;
    // 查找数据源
    virtual ISmartAnalysis *GetSmartAnalysis(IKWorksheet * pSheet) = 0;
    // 判断当前工作表是否是分析页
    virtual BOOL IsAnalysisPivotTable(IKWorksheet * pSheet) = 0;
    // 判断当前工作表是否是数据源页
    virtual BOOL IsAnalysisPivotTableSource(IKWorksheet * pSheet) = 0;

    virtual uint32 GetAnalysisCount(IKWorkbook * pBook) = 0;
    // 判断该分析是否有效
    virtual BOOL IsValidAnalysis(ISmartAnalysis * pAnalysis) = 0;
    // 清除所有数据
    virtual HRESULT ClearAll() = 0;
};

interface ISmartUnderstand
{
  public:
    virtual ~ISmartUnderstand()
    {
    }
    // 接受服务器返回的解读结果Json数据
    virtual HRESULT ParseUnderstandJsonObj(IKWorksheet * pSheet, QJsonArray & jsonObj, QJsonArray & jsonReportList) = 0;
    // 获取解读结果Json数据
    virtual HRESULT ExportJsonObj(QJsonArray & jsonReportList) = 0;
    // 获取某图表的表格数据
    virtual HRESULT GetChartTableJsonObj(QString strChart, QJsonArray & jsonObj) = 0;
    // 将某解读片段插入文档
    virtual HRESULT InsertReport(QString strChart, QString strType, QString strInsertType, bool isNewSheet,
                                 QString &strSheetName, bool bSelect) = 0;
    // 当前是否有解读
    virtual BOOL IsEmpty() = 0;
    // 获取指定片段的解读报告
    virtual HRESULT GetReport(const QString &strType, const QString &strChart, QJsonObject &object) = 0;
    // 是否插入过解读片段
    virtual BOOL IsFirstInsertReport() const = 0;
    // 获取解读个数
    virtual INT GetCount() const = 0;
    // 获取解读名称
    virtual QString GetReportName(int idx) const = 0;
    // 获取解读信息
    virtual int GetReportInfo(const QString &strChartName, QString &strChartType, QString &strAnalysisType) const = 0;
    // 设置某解读片段点赞状态
    virtual HRESULT SetReportLike(const QString &strChart, bool b) = 0;
    // 判断是否已经返回解读结果
    virtual BOOL IsReady() const = 0;
    // 设置空白分析页的解读
    virtual HRESULT SetIsReady() = 0;
    // 获取某个解读结果Json数据
    virtual HRESULT ExportReportJsonObj(const QString &strChartName, QJsonObject &jsonReport) const = 0;
    // 切换某个解读的图表类型
    virtual HRESULT ChangeChartType(const QString &strChartName, const QString &strChartType) = 0;
    // 获取当前分析的行字段、列字段、值字段名字列表
    virtual void GetRowColDataFields(const QString &strChartName, QStringList &qslRowFields, QStringList &qslColFields,
                                     QStringList &qslDataFields) const = 0;
    // 获取某解读片段的使用字段名称
    virtual QStringList GetNameList(QString strChart) const = 0;
};

HRESULT Create_SmartAnalysisManager(IKWorksheet *pWorksheet, ISmartAnalysisManager **ppSmartAnalysisManager);
} // namespace etai
#endif /* __SMARTANALYSIS_H__ */
