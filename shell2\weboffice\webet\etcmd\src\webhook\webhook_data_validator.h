﻿#ifndef __WEBHOOK_DATA_VALIDATOR_H__
#define __WEBHOOK_DATA_VALIDATOR_H__

namespace webhook_data_validator {
    // 要传出的参数才不加 const
    struct settings {};
    struct settings_fieldIds : settings {
        const bool empty;
        constexpr settings_fieldIds(bool empty) : empty {empty} {}
    };
    struct settings_recordIds : settings {
        const bool empty;
        constexpr settings_recordIds(bool empty) : empty {empty} {}
    };
    struct settings_queryRanges : settings {
        const bool empty;
        constexpr settings_queryRanges(bool empty) : empty {empty} {}
    };
    struct settings_etUpdateRanges : settings {
        std::size_t errorNumber {};
    };
    struct settings_etUpdateCharts : settings {
        std::size_t errorNumber {};
    };
    struct settings_updateCells : settings_fieldIds, settings_queryRanges, settings_recordIds {
        constexpr settings_updateCells(bool fieldEmpty, bool queryRangesEmpty, bool recordEmpty) :
                settings_fieldIds {fieldEmpty}, settings_queryRanges {queryRangesEmpty},
                settings_recordIds {recordEmpty} {}
    };
    struct settings_updateRecords : settings_fieldIds, settings_queryRanges {
        constexpr settings_updateRecords(bool fieldEmpty, bool queryRangesEmpty) :
                settings_fieldIds {fieldEmpty}, settings_queryRanges {queryRangesEmpty} {}
    };
    struct settings_createAndFillInRecords : settings_fieldIds, settings_queryRanges {
        constexpr settings_createAndFillInRecords(bool fieldEmpty, bool queryRangesEmpty) :
                settings_fieldIds {fieldEmpty}, settings_queryRanges {queryRangesEmpty} {}
    };
    struct settings_updateFieldCells : settings_fieldIds, settings_queryRanges {
        constexpr settings_updateFieldCells(bool fieldEmpty, bool queryRangesEmpty) :
                settings_fieldIds {fieldEmpty}, settings_queryRanges {queryRangesEmpty} {}
    };
    struct settings_removeRecord : settings_recordIds {
        constexpr settings_removeRecord(bool empty) : settings_recordIds {empty} {}
    };
    struct settings_updateRecordsParent : settings_recordIds {
        constexpr settings_updateRecordsParent(bool empty) : settings_recordIds {empty} {}
    };
    struct settings_updateRecordCells : settings_recordIds {
        constexpr settings_updateRecordCells(bool empty) : settings_recordIds {empty} {}
    };
}

using WebhookDataValidator = std::pair<bool, PCWSTR> (*)(const IWebhookData *, IBook *,
        webhook_data_validator::settings &);

/* 
 * 带有 constexpr 标识的函数都只在 VarObj 层进行校验, 内部没有校验的必要
 * 之后有新增的校验逻辑, 去掉 constexpr 并实现即可
 */

namespace webhook_data_validator
{

constexpr std::pair<bool, PCWSTR> userId(const IWebhookData *pData, IBook *pBook, settings &) {return {true, {}};}
std::pair<bool, PCWSTR> sheetId(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> sheetIds(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> viewId(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> fieldId(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> fieldIds(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> actionFieldIds(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> recordIds(const IWebhookData *pData, IBook *pBook, settings &);
constexpr std::pair<bool, PCWSTR> filter(const IWebhookData *pData, IBook *pBook, settings &) {return {true, {}};}
constexpr std::pair<bool, PCWSTR> formulaChanged(const IWebhookData *pData, IBook *pBook, settings &) {return {true, {}};}
constexpr std::pair<bool, PCWSTR> skipAfterMatchCreateAndFill(const IWebhookData *pData, IBook *pBook, settings &) {return {true, {}};}
std::pair<bool, PCWSTR> updateSheetDescription(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateSheetIcon(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateSheet(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> renameSheet(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> removeSheet(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> createView(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> renameView(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> removeView(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> createField(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateField(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updatePrimaryField(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> removeField(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> createRecord(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateRecords(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> removeRecord(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateCells(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateFieldCells(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateRecordCells(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateRecordsParent(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> updateSheetsAllChanged(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> createAndFillInRecord(const IWebhookData *pData, IBook *pBook, settings &);

std::pair<bool, PCWSTR> etSheetId(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etSheetIds(const IWebhookData *pData, IBook *pBook, settings &);
constexpr std::pair<bool, PCWSTR> etRange(const IWebhookData *pData, IBook *pBook, settings &) {return {true, {}};}
std::pair<bool, PCWSTR> etRowColumnIndexArray(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etUpdateSheet(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etUpdateRange(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etUpdateRanges(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etUpdateCharts(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etRemoveSheet(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etInsertRows(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etRemoveRows(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etInsertColumns(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etRemoveColumns(const IWebhookData *pData, IBook *pBook, settings &);
std::pair<bool, PCWSTR> etUpdateSheetsAllChanged(const IWebhookData *pData, IBook *pBook, settings &);

}

#endif      // __WEBHOOK_DATA_VALIDATOR_H__