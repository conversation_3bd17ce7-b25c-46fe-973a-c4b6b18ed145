﻿#include "etstdafx.h"
#include "etentrys.h"
#include <kso/kso_base.h>

namespace wo
{

KEntrysBase::KEntrysBase()
	: m_coreEntry(NULL)
{
	//
	// 创建CoreEntry
	//
	m_coreEntry = KSGetCoreGlobalEntry();
	Q_ASSERT(m_coreEntry);
}

KEntrysBase::~KEntrysBase()
{
	TerminateCoreEntry();
}

IKCoreGlobalEntry* KEntrysBase::coreEntry()
{
	return m_coreEntry;
}

void KEntrysBase::TerminateCoreEntry()
{
	if (m_coreEntry)
		m_coreEntry->Terminate();
	m_coreEntry = nullptr;
}

//////////////////////////////////////////////////////////////////////////
KEntrys* KEntrys::g_entry = nullptr;
KApiEntry* KEntrys::g_apiEntryModule = nullptr;
KApiEntryV8* KEntrys::g_apiEntryModuleV8 = nullptr;
KEntrys::_func_KSGetApiGlobalEntry KEntrys::m_funcApiEntry = nullptr;
KEntrys::_func_KSGetApiGlobalEntry KEntrys::m_funcApiEntryV8 = nullptr;

KEntrys::KEntrys()
	: KEntrysBase()
	, m_apiEntry(NULL)
	, m_apiEntryV8(NULL)
	, m_bLoadApi(false)
{
	ASSERT(g_entry == nullptr);
	g_entry = this;

	//
	// 内核初始化
	//
	if (coreEntry())
		coreEntry()->Initialize(FALSE, FALSE);

	VS(_kso_Initialize());
}
KEntrys::~KEntrys()
{
#ifdef X_OS_WINDOWS
	if (m_apiEntry)
		m_apiEntry->Terminate();
	if (m_apiEntryV8)
		m_apiEntryV8->Terminate();

	_kso_Terminate();

	TerminateCoreEntry();

	if (isLoaded())
		unload();
	if (g_apiEntryModule && g_apiEntryModule->isLoaded())
		g_apiEntryModule->unload();
	if (g_apiEntryModuleV8 && g_apiEntryModuleV8->isLoaded())
		g_apiEntryModuleV8->unload();
#endif
}

KEntrys* KEntrys::getInstance()
{
	return g_entry;
}

IKApiGlobalEntry* KEntrys::apiEntry()
{
	return m_apiEntry;
}

IKApiGlobalEntry* KEntrys::apiEntryV8()
{
	return m_apiEntryV8;
}

void KEntrys::_LoadMsoApiEntry()
{
	g_apiEntryModule = new KApiEntry();
	if (g_apiEntryModule->load())
		m_funcApiEntry = (_func_KSGetApiGlobalEntry)g_apiEntryModule->resolve("KSGetApiGlobalEntry");
	Q_ASSERT(g_apiEntryModule->isLoaded());
	if (m_funcApiEntry)
		m_apiEntry = m_funcApiEntry();
	Q_ASSERT(m_apiEntry);
}

void KEntrys::_LoadKsoApiEntry()
{
	g_apiEntryModuleV8 = new KApiEntryV8();
	if (g_apiEntryModuleV8->load())
		m_funcApiEntryV8 = (_func_KSGetApiGlobalEntry)g_apiEntryModuleV8->resolve("KSGetApiGlobalEntry");
	Q_ASSERT(g_apiEntryModuleV8->isLoaded());
	if (m_funcApiEntryV8)
		m_apiEntryV8 = m_funcApiEntryV8();
	Q_ASSERT(m_apiEntryV8);
}

bool KEntrys::loadApi()
{
	if (m_bLoadApi)
		return false;

	m_bLoadApi = true;
	//
	// 创建ShellEntry
	//

	int apiType = _kso_QueryFeatureState(kaf_kso_ApiCompatible);
	if (kaf_api_mso == apiType || kaf_api_both == apiType)
		_LoadMsoApiEntry();
#ifdef X_OS_WINDOWS
	if (kaf_api_kso == apiType || kaf_api_both == apiType)
		_LoadKsoApiEntry();
#endif
	//
	// ShellEntry跟CoreEntry要先建立关系，才能收到内核对象的通知。
	//
	if (m_apiEntry)
		m_apiEntry->Initialize(coreEntry());

#ifdef X_OS_WINDOWS
	if (m_apiEntryV8)
		m_apiEntryV8->Initialize(coreEntry());
#endif
	return m_apiEntry != NULL;
}
}