#ifndef __KCOMCTL_KLINKEDDEQUE_H__
#define __KCOMCTL_KLINKEDDEQUE_H__

namespace wo
{
template<class _Tv>
struct KLinkedDequeNode
{
	KLinkedDequeNode(const _Tv & v)
		: value(v), next(NULL)
	{
	}

	_Tv value;
	KLinkedDequeNode * next;
};

template<class _Tv>
class KLinkedDequeIterator : public std::iterator<std::forward_iterator_tag, _Tv>
{
public:
	typedef KLinkedDequeNode<_Tv> node_type;

	KLinkedDequeIterator(node_type * node)
		: node(node)
	{}

	KLinkedDequeIterator & operator ++()
	{
		node = node->next;
		return *this;
	}
	_Tv & operator * ()
	{
		return node->value;
	}
	_Tv * operator -> ()
	{
		return &node->value;
	}
	bool operator != (const KLinkedDequeIterator & rhs)
	{
		return node != rhs.node;
	}
private:
	node_type * node;
};

template<class _Tv>
class KLinkedDeque
{
public:
	typedef _Tv value_type;
	typedef KLinkedDequeNode<_Tv> node_type;
	typedef KLinkedDeque<_Tv> this_type;
	typedef KLinkedDequeIterator<_Tv> iterator;

public:
	KLinkedDeque()
		: m_first(NULL), m_last(NULL), m_count(0)
	{
	}
	~KLinkedDeque()
	{
		while (m_first)
		{
			node_type * n = m_first->next;
			delete m_first;
			m_first = n;
		}
	}
	void append(const _Tv & v)
	{
		node_type * n = new node_type(v);
		_append(n);
	}
	_Tv & front()
	{
		return m_first->value;
	}
	void pop()
	{
		Q_ASSERT(m_first);
		delete _pop();
	}
	void shiftTo(this_type & rhs)
	{
		Q_ASSERT(m_first);
		rhs._append(_pop());
	}
	void shiftAllTo(this_type & rhs)
	{
		Q_ASSERT(this != &rhs);
		if (m_first)
		{
			if (rhs.m_last)
			{
				rhs.m_last->next = m_first;
			}
			else
			{
				rhs.m_first = m_first;
			}
			rhs.m_last = m_last;
			m_first = m_last = NULL;
			rhs.m_count += m_count;
			m_count = 0;
		}
	}
	template<class _Ft>
	void shiftToIf(this_type & rhs, _Ft f)
	{
		node_type * prev = NULL;
		node_type * cur = m_first;
		while (cur)
		{
			if (f(cur->value))
			{
				if (m_last == cur)
				{
					m_last = prev;
				}
				if (prev)
				{
					prev->next = cur->next;
				}
				else
				{
					m_first = cur->next;
				}
				rhs._append(cur);
				cur = prev ? prev->next : m_first;
				--m_count;
			}
			else
			{
				prev = cur;
				cur = cur->next;
			}
		}	
	}
	void removeAll(const _Tv & v)
	{
		this_type tmp;
		shiftToIf(tmp, std::bind2nd(std::equal_to<_Tv>(), v));
	}
	template<class _Ft>
	void removeIf(_Ft f)
	{
		this_type tmp;
		shiftToIf(tmp, f);
	}
	bool isEmpty()
	{
		return m_first == NULL;
	}
	iterator begin()
	{
		return iterator(m_first);
	}
	iterator end()
	{
		return iterator(NULL);
	}
	int count()
	{
		return m_count;
	}

private:
	node_type * _pop()
	{
		node_type * n = m_first;
		m_first = m_first->next;
		if (m_last == n)
		{
			m_last = NULL;
		}
		--m_count;
		return n;
	}
	void _append(node_type * n)
	{
		if (m_last)
		{
			m_last->next = n;
			m_last = n;
		}
		else
		{
			m_first = m_last = n;
		}
		n->next = NULL;
		++m_count;
	}

private:
	node_type * m_first;
	node_type * m_last;
	int m_count;
};
}

#endif//__KCOMCTL_KLINKEDDEQUE_H__