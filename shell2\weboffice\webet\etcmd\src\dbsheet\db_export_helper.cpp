﻿#include "etstdafx.h"
#include "db_export_helper.h"

#include "etcmd/src/workbooks.h"
#include "et_revision_context_impl.h"
#include "webbase/context_init_helper.h"
#include "et_dbsheet_utils.h"
#include "util.h"
#include "db/db_basic_itf.h"

namespace wo
{

AddWorkbookScope::AddWorkbookScope(
	Workbooks* wbs, KEtWorkbook* pKEtWb, KEtRevisionContext& ctx
	, UINT defaultSheetCount, PCWSTR font /* = nullptr */, int fontSize /* = 0 */, BOOL bDbSt /* = FALSE */)
	: m_pKEtWb(pKEtWb), m_ctx(ctx)
{
	class AppSheetsInNewWorkbookScope
	{
	public:
		AppSheetsInNewWorkbookScope(KEtWorkbook* pKEtWb, UINT sheetsInNewWorkbook, PCWSTR font, int fontSize)
			: m_pKEtWb(pKEtWb), m_sheetsInNewWorkbook(m_pKEtWb->GetCoreApp()->GetAppSettings()->GetSheetsInNewWorkbook())
		{
			_Application* ptrApp = m_pKEtWb->GetCoreApp();
			ptrApp->GetAppSettings()->SetSheetsInNewWorkbook(sheetsInNewWorkbook);
			// 设置db默认字体
			ptrApp->get_StandardFont(&m_etFont);
			ptrApp->get_StandardFontSize(&m_etFontSize);
			if (font != nullptr && fontSize != 0)
			{
				ks_bstr dbFont(font);
				long dbFontSize = fontSize;
				ptrApp->put_StandardFont(dbFont);
				ptrApp->put_StandardFontSize(dbFontSize);
			}
		}
		~AppSheetsInNewWorkbookScope()
		{
			_Application* ptrApp = m_pKEtWb->GetCoreApp();
			ptrApp->GetAppSettings()->SetSheetsInNewWorkbook(m_sheetsInNewWorkbook);
			// 恢复ET的默认字体
			ptrApp->put_StandardFont(m_etFont);
			ptrApp->put_StandardFontSize(m_etFontSize);
		}
	private:
		KEtWorkbook* m_pKEtWb;
		UINT m_sheetsInNewWorkbook;
		ks_bstr m_etFont;
		long m_etFontSize;
	};
	AppSheetsInNewWorkbookScope appSheetsInNewBook(m_pKEtWb, defaultSheetCount, font, fontSize);
	wbs->Add(KComVariant(), &m_spNewWb, NULL, 0, bDbSt);
	
	m_fromDb = pKEtWb->GetCoreWorkbook()->GetBook()->GetBMP()->bDbSheet ? true : false;

	if (nullptr == m_spNewWb)
	{
		WO_LOG_X(m_pKEtWb->getLogger(), WO_LOG_ERROR, "Fail to add workbook!");
		return;
	}
}

AddWorkbookScope::~AddWorkbookScope()
{
	if (m_spNewWb)
	{
		VARIANT SaveChanges;
		V_VT(&SaveChanges) = VT_BOOL;
		V_BOOL(&SaveChanges) = VARIANT_FALSE;

		VARIANT Filename = {0};
		VARIANT RouteWorkbook = {0};

		m_spNewWb->Close(SaveChanges, Filename, RouteWorkbook);
	}

	// m_pKEtWb->GetCoreApp()->GetEtTranscationTool()->ClearAllUndoRedoSteps(m_pKEtWb->GetCoreWorkbook());
	KwTasks tasks;
	KwCommand* pCmd = new KwCommand;
	binary_wo::VarObj objCmd = pCmd->cast();

	if (m_fromDb)
		objCmd.add_field_str("name", __X("dbSheet.clearTransactions"));
	else
		objCmd.add_field_str("name", __X("book.clearTransactions"));

	binary_wo::VarObj param = objCmd.add_field_struct("param");
	tasks.push_back(new KwTask(pCmd, InitVersion, USER_INNER_INV_ID, NULL, InitVersion));
	
	m_pKEtWb->execTasksDirect(tasks, &m_ctx);

	binary_wo::BinWriter ww;
	m_pKEtWb->SerialVerStatusInfo(ww);
	m_pKEtWb->BroadcastChanged(ww, NULL);

    m_pKEtWb->doClearJobAfterExecCmd();
}
_Workbook* AddWorkbookScope::GetWorkbook()
{
	if (nullptr == m_spNewWb)
		return nullptr;
	else
		return m_spNewWb.get();
}

DbAttachmentDataExporter::DbAttachmentDataExporter(KEtWorkbook* pWorkbook)
        : m_pBook(pWorkbook->GetCoreWorkbook()->GetBook())
{
}

void DbAttachmentDataExporter::ExtractItemToServer(const_token_ptr pItem, UINT &itemIndex, binary_wo::BinWriter& writer)
{
    alg::const_handle_token_assist hta(pItem);
    ASSERT(hta);
    if (hta.get_handleType() == alg::ET_HANDLE_DBATTACHMENT)
    {
        ks_stdptr<IDbAttachmentHandle> spDbAttachmentHandle = hta.get_handle()->CastUnknown();
        ASSERT(spDbAttachmentHandle);
        PCWSTR name = spDbAttachmentHandle->GetName();
        if (!name || !xstrcmp(name, __X("")) 
        || (m_isFilterVideo && m_unsupportedAttachmentFileSuffixTypes.find(spDbAttachmentHandle->GetContentType()) != m_unsupportedAttachmentFileSuffixTypes.end()))
            return;

        PCWSTR linkUrl = spDbAttachmentHandle->GetLinkUrl();
        PCWSTR fileId = spDbAttachmentHandle->GetFileId();
        PCWSTR fileType = spDbAttachmentHandle->GetContentType();
        DbSheet_AttachmentSource source = spDbAttachmentHandle->GetSource();
        ASSERT(source == AttachmentSource_upload_ks3 || source == AttachmentSource_cloud);
        writer.beginStruct();
        writer.addStringField(krt::utf16(QString::number(itemIndex++)), "objectKey");
        if (source == AttachmentSource_upload_ks3)
            writer.addStringField(__X("ks3"), "type");
        else if (source == AttachmentSource_cloud)
            writer.addStringField(__X("cloud"), "type");
        writer.addStringField(fileId, "fileId");
        writer.addStringField(name, "fileName");
        writer.addStringField(fileType, "fileType");
        if (linkUrl)
            writer.addStringField(linkUrl, "url");
        writer.endStruct();
    }
    else
    {
        ASSERT(hta.get_handleType() == alg::ET_HANDLE_TOKENARRAY);
        const auto handle {hta.get_handle()->CastArray()};
        const auto count {handle->GetCount()};
        for (auto i {0}; i < count; ++i)
        {
            const_token_ptr token {};
            handle->Item(i, &token);
            this->ExtractItemToServer(token, itemIndex, writer);
        }
    }
}

void DbAttachmentDataExporter::SetIsFilterVideo(bool isFilterVideo)
{
    m_isFilterVideo = isFilterVideo;
}

void DbAttachmentDataExporter::SetUnsupportedAttachmentFileSuffixTypes(const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes)
{
    m_unsupportedAttachmentFileSuffixTypes = std::move(unsupportedAttachmentFileSuffixTypes);
}

void DbAttachmentDataExporter::ExtractFieldToServer(EtDbId fieldId, const IDBIds* pRecords, IDBSheetOp* pDbSheetOp, binary_wo::BinWriter& writer)
{
    IDbFieldsManager* pFieldsManager = pDbSheetOp->GetFieldsManager();
    ks_stdptr<IDbField> spField;
    pFieldsManager->GetField(fieldId, &spField);
    if (spField->GetType() != Et_DbSheetField_Attachment)
        return;

    EtDbId primaryField = pFieldsManager->GetPrimaryField();
    EtDbIdx recordCount = pRecords->Count();
    for (EtDbIdx recordIndex = 0; recordIndex < recordCount; ++recordIndex)
    {
        EtDbId recordId = pRecords->IdAt(recordIndex);
        const_token_ptr pToken = nullptr;
        HRESULT hr = pDbSheetOp->GetValueToken(recordId, fieldId, &pToken);
        if (FAILED(hr) || !pToken || !alg::const_handle_token_assist::is_type(pToken))
            continue;

        alg::const_handle_token_assist chta(pToken);
        if (alg::ET_HANDLE_TOKENARRAY != chta.get_handleType())
            continue;

        alg::TOKEN_HANDLE handle = chta.get_handle();
        if (!handle)
            continue;

        ks_stdptr<IDbTokenArrayHandle> spTokenArray = handle->CastUnknown();
        UINT cnt = spTokenArray->GetCount();
        if (!cnt)
            continue;

        ks_bstr recordTitle;
        hr = pDbSheetOp->GetDisplayString(recordId, primaryField, &recordTitle);
        if (FAILED(hr))
            continue;

        writer.beginStruct();
        EtDbIdStr fieldIdBuf;
        _appcore_GainDbSheetContext()->EncodeEtDbId(fieldId, &fieldIdBuf);
        writer.addStringField(fieldIdBuf, "fieldId");
        EtDbIdStr recordIdBuf;
        _appcore_GainDbSheetContext()->EncodeEtDbId(recordId, &recordIdBuf);
        writer.addStringField(recordIdBuf, "recordId");
        writer.addStringField(spField->GetName(), "fieldName");
        writer.addStringField(recordTitle.c_str() ? recordTitle.c_str() : __X(""), "keyWords");
        writer.beginArray("objects");
        UINT itemIndex {};
        for (UINT i = 0; i < cnt; ++i)
        {
            const_token_ptr pItem = nullptr;
            spTokenArray->Item(i, &pItem);
            ExtractItemToServer(pItem, itemIndex, writer);
        }
        writer.endArray();
        writer.endStruct();
    }
}

HRESULT DbAttachmentDataExporter::ExtractDataToServer(UINT sheetId, EtDbId viewId, binary_wo::BinWriter& writer)
{
    ks_stdptr<ISheet> spSheet;
    HRESULT hr = DbSheet::GetDbSheet(m_pBook, sheetId, &spSheet);
    if (FAILED(hr))
        return hr;

    ks_stdptr<IDBSheetOp> spDbSheetOp;
    ks_stdptr<IDBSheetViews> spDbSheetViews;
    ks_stdptr<IDBSheetView> spView;
    VS(DbSheet::GetDBSheetOp(spSheet.get(), &spDbSheetOp));
    VS(DbSheet::GetDBSheetViews(spSheet.get(), &spDbSheetViews));
    spDbSheetViews->GetItemById(viewId, &spView);
    if (!spView)
        return E_DBSHEET_VIEW_NOT_FOUND;

    if (spView->GetType() == et_DBSheetView_Form)
        return E_INVALID_REQUEST;

    writer.beginStruct();
    writer.addUint32Field(sheetId, "sheetId");
    writer.beginArray("cellAttachments");
    const IDBIds* pFields = spView->GetVisibleFields();
    const IDBIds* pRecords = spView->GetVisibleRecords();
    if (pRecords->Count() == 0 && viewId == EtDbId_DefaultView)
        pRecords = spView->GetAllRecords();
    EtDbIdx fieldCount = pFields->Count();
    for (EtDbIdx fieldIndex = 0; fieldIndex < fieldCount; ++fieldIndex)
    {
        EtDbId fieldId = pFields->IdAt(fieldIndex);
        ExtractFieldToServer(fieldId, pRecords, spDbSheetOp, writer);
    }
    writer.endArray();
    writer.endStruct();
    return S_OK;
}

HRESULT DbAttachmentDataExporter::ExtractAllSheetDataToServer(KEtWorkbook* pWorkbook, binary_wo::BinWriter& writer)
{
    HRESULT hr = S_OK;
    int sheetCnt = 1;
    RemoveNoPermissionDbSheetData(pWorkbook->GetCoreWorkbook());
    VS(m_pBook->GetSheetCount(&sheetCnt));
    for (int i = 0; i < sheetCnt; i++)
    {
        ks_stdptr<ISheet> spSheet;
        m_pBook->GetSheet(i, &spSheet);
        ASSERT(spSheet);
        SHEETSTATE state;
        spSheet->GetVisible(&state);
        if (state != ssVisible || !spSheet->IsDbSheet())
            continue;
        ks_stdptr<IDBSheetOp> sheetOp;
        VS(DbSheet::GetDBSheetOp(spSheet.get(), &sheetOp));
        UINT sheetId = sheetOp->GetSheetId();
        EtDbId viewId = EtDbId_DefaultView;
        hr = ExtractDataToServer(sheetId, viewId, writer);
    }
    return hr;
}

HRESULT ProcessCrossBookDbData(IKWorkbook* pWorkbook)
{
	if (!pWorkbook)
		return E_FAIL;
	IBook* pBook = pWorkbook->GetBook();
	IKWorksheets* pWorksheets = pWorkbook->GetWorksheets();
	if (!pBook || !pWorksheets)
		return E_FAIL;
	if (!pBook->GetBMP()->bDbSheet)
		return E_FAIL;
	ks_stdptr<IUnknown> spUnknown;
	pBook->GetExtDataItem(edDBUserGroups, &spUnknown);
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	if (spDBUserGroups)
		spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if (!spProtectionJudgement)
		return E_FAIL;
	class CrossFieldFallbackEnum : public IDbFieldEnum
	{
	public:
		explicit CrossFieldFallbackEnum(IBook* pBook) : m_pBook(pBook) {}
		HRESULT Do(IDbField* pField) override
		{
			HRESULT hr = S_OK;
			DbSheet::DbFieldUpdateScope updateScope(pField, m_pBook, hr);
			// 跨 book 关联字段及相关的引用字段统一回落为文本字段；待引用字段回落方案完善后可以再以完善
			VS(pField->SetType(Et_DbSheetField_MultiLineText, nullptr));
			return S_OK;
		}
	private:
		IBook* m_pBook = nullptr;
	};
	std::vector<UINT> delSheetIds;
	app_helper::KBatchUpdateCal buc(pBook->LeakOperator());
	DbSheet::DisableDbProtectScope dbProtectScope(spProtectionJudgement);
	DbSheet::DisableDbTrackHistoryScope historyScope;
	DbSheet::DisableDbUpdateLastModifiedInfoScope lastModifiedInfoScope;
	for (int stIdx = 0, sheetCount = pWorksheets->GetSheetCount(); stIdx < sheetCount; ++stIdx)
	{
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(stIdx);
		if (!pWorksheet)
			continue;
		ISheet* pSheet = pWorksheet->GetSheet();
		if (!pSheet->IsDbSheet())
			continue;
		ks_stdptr<IDBSheetOp> spDBSheetOp;
		DbSheet::GetDBSheetOp(pSheet, &spDBSheetOp);
		if (!spDBSheetOp)
			continue;
		if (spDBSheetOp->GetSheetSyncType() == DbSheet_St_Cross_DB)
		{
			delSheetIds.emplace_back(pSheet->GetStId());
			continue;
		}
		IDbFieldsManager* pFieldsMgr = spDBSheetOp->GetFieldsManager();
		const IDBIds* pFldIds = spDBSheetOp->GetAllFields();
		EtDbIdx fldCnt = pFldIds->Count();
		for (int fldIdx = 0; fldIdx < fldCnt; ++fldIdx)
		{
			EtDbId fldId = pFldIds->IdAt(fldIdx);
			ks_stdptr<IDbField> spField;
			pFieldsMgr->GetField(fldId, &spField);
			if (spField->GetType() == Et_DbSheetField_OneWayLink)
			{
				ks_stdptr<IDbField_Link> spFieldLink = spField;
				if (!spFieldLink->IsSyncLink())
					continue;
				// 先将相关引用字段回落后再处理关联字段
				CrossFieldFallbackEnum enumer(pBook);
				spFieldLink->EnumLookupFields(&enumer);
				enumer.Do(spField);
			}
		}
	}
	for (UINT sheetId : delSheetIds)
	{
		IDX stIdx = INVALIDIDX;
		pBook->STSheetToRTSheet(sheetId, &stIdx);
		IKWorksheet* pWorksheet = pWorksheets->GetSheetItem(stIdx);
		if (!pWorksheet)
			continue;
		pWorksheet->DeleteDirectly();
	}

	return S_OK;
}

HRESULT RemoveNoPermissionDbSheetData(_Workbook* pWorkbook)
{
    if (!pWorkbook)
        return E_FAIL;

    IBook* pBook = pWorkbook->GetBook();
    if (!pBook)
        return E_FAIL;

    if (pBook->GetBMP()->bDbSheet)
    {
        ks_stdptr<IUnknown> spUnknown;
        pBook->GetExtDataItem(edDBUserGroups, &spUnknown);
        ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
        ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
        if (spDBUserGroups)
        {
            spDBUserGroups->GetJudgement(&spProtectionJudgement);
        }
        if (!spProtectionJudgement)
            return E_FAIL;
        ks_stdptr<etoldapi::Worksheets> spWorksheets = pWorkbook->GetWorksheets();
        if (!spWorksheets)
            return E_FAIL;

        bool filterData = false;
        std::vector<etoldapi::_Worksheet*>  deleteSheetVec;
        int sheetCount = spWorksheets->GetSheetCount();
        for (int i = 0; i < sheetCount; i++)
        {
            ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorksheets->GetSheetItem(i);
            if (!spWorksheet)
                return E_FAIL;

            ISheet* pSheet = spWorksheet->GetSheet();
            UINT sheetId = pSheet->GetStId();

            //防止隐藏表格一起判断为无权限，删除所有sheet会崩溃
			if (!pSheet->IsDbSheet() && !pSheet->IsDbDashBoardSheet() && !pSheet->IsFpSheet())
				continue;

            HRESULT hr = spProtectionJudgement->CheckSheetCanVisit(sheetId);
            if (FAILED(hr))
            {
                deleteSheetVec.push_back(spWorksheet);
                filterData = true;
                continue;
            }

            //数据表才有行列权限
            if (!pSheet->IsDbSheet())
                continue;

            ks_stdptr<IDBSheetOp> spSheetOp;
            DbSheet::GetDBSheetOp(pSheet, &spSheetOp);
            if (!spSheetOp)
                return E_FAIL;

            {
                ks_stdptr<IDBSheetRange> spRg;
                spProtectionJudgement->GetNoPermissionRecords(sheetId, &spRg);
                if (spRg)
                {
					DbSheet::DisableDbSheetProtectScope disableScop(spProtectionJudgement, sheetId);
                    spSheetOp->RemoveRecords(spRg);
                    filterData = true;
                }
            }

            {
                ks_stdptr<IDBSheetRange> spRg;
                spProtectionJudgement->GetNoPermissionFields(sheetId, &spRg);
                if (spRg)
                {
					DbSheet::DisableDbSheetProtectScope disableScop(spProtectionJudgement, sheetId);
                    spSheetOp->RemoveFields(spRg);
                    filterData = true;
                }
            }
        }

        for (auto it = deleteSheetVec.rbegin(); it != deleteSheetVec.rend(); ++it)
        {
            (*it)->DeleteDirectly();
        }

        WOLOG_INFO << "[RemoveNoPermissionDbSheetData]  filter no permission data :" <<  (filterData ? "true" : "false");

    }

    return S_OK;
}

HRESULT RemoveNoExportSheet(_Workbook* pWorkbook, std::set<UINT>& sheetStIds)
{
    if (!pWorkbook)
        return E_FAIL;
    
    IBook* pBook = pWorkbook->GetBook();
    if (!pBook)
        return E_FAIL;

    app_helper::KBatchUpdateCal buc(pBook->LeakOperator());

    if (pBook->GetBMP()->bDbSheet || pBook->GetBMP()->bKsheet)
    {
        ks_stdptr<etoldapi::Worksheets> spWorksheets = pWorkbook->GetWorksheets();
        if (!spWorksheets)
            return E_FAIL;
        
        bool hasCellImgListSht = util::hasCellImgListSheet(pBook);
        int sheetCount = spWorksheets->GetSheetCount();
        // 如果需要导出的sheet数目为所有sheet,就不用后续处理了。需要先判断下是否存在单元格图片sheet
        if ((hasCellImgListSht && sheetCount == sheetStIds.size() + 1)
            || (!hasCellImgListSht && sheetCount == sheetStIds.size()))
            return S_OK;

        std::vector<etoldapi::_Worksheet*>  deleteSheetVec;
        for (int i = 0; i < sheetCount; i++)
        {
            ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorksheets->GetSheetItem(i);
            if (!spWorksheet)
                return E_FAIL;

            if (hasCellImgListSht && util::isCellImgListSheet(pBook, i))
                continue;

            ISheet* pSheet = spWorksheet->GetSheet();
            UINT stId = pSheet->GetStId();
            if (sheetStIds.find(stId) == sheetStIds.end() || !pSheet->IsDbSheet())
            {
                deleteSheetVec.push_back(spWorksheet);
            }
            else
            {
                std::set<EtDbIdx> fallBackLinkFldIds, fallBackLookupFldIds;
                GetFallBackFields(pSheet, sheetStIds, fallBackLinkFldIds, fallBackLookupFldIds);
                DoFallBackFields(pSheet, fallBackLinkFldIds, fallBackLookupFldIds);
            }
        }

        for (auto it = deleteSheetVec.begin(); it != deleteSheetVec.end(); ++it)
        {
            (*it)->DeleteDirectly();
        }
    }
    return S_OK;
}

void GetFallBackFields(ISheet* pSheet, const std::set<UINT>& sheetStIds,
    std::set<EtDbIdx>& fallBackLinkFldIds, std::set<EtDbIdx>& fallBackLookupFldIds)
{
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp);
    if (!spDbSheetOp)
        return;
    IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();
    const IDBIds* pFields = spDbSheetOp->GetAllFields();

    // 先确定需要回落的关联字段, 还有引用数据表的查找引用字段
    for (EtDbIdx i = 0, fldCnt = pFields->Count(); i < fldCnt; ++i)
    {
        EtDbId fldId = pFields->IdAt(i);
        ks_stdptr<IDbField> spField;
        pFieldsMgr->GetField(fldId, &spField);
        if (spField->GetType() == Et_DbSheetField_Link || spField->GetType() == Et_DbSheetField_OneWayLink)
        {
            ks_stdptr<IDbField_Link> spFieldLink = spField;
            UINT linkShId = spFieldLink->GetLinkSheet();
            if (sheetStIds.find(linkShId) == sheetStIds.end())
                fallBackLinkFldIds.insert(spField->GetID());
        }
        else if (spField->GetType() == Et_DbSheetField_Lookup)
        {
            ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
            UINT linkSheetId = spFieldLookup->GetLookupSheetId();
            if (spFieldLookup->IsNormalType() || linkSheetId <= 0)
                continue;
            if (sheetStIds.find(linkSheetId) == sheetStIds.end())
                fallBackLookupFldIds.insert(spField->GetID());
        }
    }

    // 再确定需要回落的引用字段
    for (EtDbIdx i = 0, fldCnt = pFields->Count(); i < fldCnt; ++i)
    {
        EtDbId fldId = pFields->IdAt(i);
        ks_stdptr<IDbField> spField;
        pFieldsMgr->GetField(fldId, &spField);
        if(spField->GetType() == Et_DbSheetField_Lookup)
        {
            ks_stdptr<IDbField_Lookup> spFieldLookup = spField;
            if(fallBackLinkFldIds.find(spFieldLookup->GetLinkFieldId()) != fallBackLinkFldIds.end())
                fallBackLookupFldIds.insert(spField->GetID());
        }
    }
}

HRESULT DoFallBackFields(ISheet* pSheet, const std::set<EtDbIdx>& fallBackLinkFldIds, const std::set<EtDbIdx>& fallBackLookupFldIds)
{
    ks_stdptr<IDBSheetOp> spDbSheetOp;
    DbSheet::GetDBSheetOp(pSheet, &spDbSheetOp);
    if (!spDbSheetOp)
        return E_FAIL;
    IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();

    //先回落引用字段，先回落关联字段会导致引用字段直接被删除了
    for (auto fldId : fallBackLookupFldIds)
    {
        DoFallBackFieldsInner(pFieldsMgr, fldId);
    }

    for (auto fldId : fallBackLinkFldIds)
    {
        DoFallBackFieldsInner(pFieldsMgr, fldId);
    }

    return S_OK;
}

HRESULT DoFallBackFieldsInner(IDbFieldsManager* pFieldsMgr, EtDbIdx fldId)
{
    HRESULT hr = S_OK;
    ks_stdptr<IDbField> spField;
    pFieldsMgr->GetField(fldId, &spField);
    DbSheet::DbFieldUpdateScope updateScope(spField, spField->GetDbSheetData()->GetBook(), hr);
    VS(spField->SetType(Et_DbSheetField_MultiLineText, nullptr));
    return S_OK;
}
} // namespace wo
