#ifndef __CONDITIONALFORMAT_WRAPPER_H__
#define __CONDITIONALFORMAT_WRAPPER_H__

struct UiCfVo
{
	bool bGte;
	XlConditionValueTypes cvTy;
	QString val;
};

struct UiDataBarNegAxisSetting
{
	XlDataBarNegativeColorType negFillType;
	EtColor clrNegativeFill;
	XlDataBarNegativeColorType negBorderType;
	EtColor clrNegativeBorder;
	XlDataBarAxisPosition ap;
	EtColor clrAxis;

	UiDataBarNegAxisSetting()
	{
		negFillType   = xlDataBarColor;
		clrNegativeFill.setARGB(0xffff0000); // red
		negBorderType = xlDataBarSameAsPositive;
		ap            = xlDataBarAxisAutomatic;
		clrAxis.setARGB(0xff000000); // black
	}
};

enum UiDBDirection { UiContext, UiL2R, UiR2L };
struct UiCfDataBar
{
	bool bShowValue;
	bool bBorder;
	bool bGradient;
	UiDBDirection dir;
	EtColor clrFill;
	EtColor clrBorder;
	UiCfVo arrCv[2];
	UiDataBarNegAxisSetting axisSetting;
};

struct UiCfIconSet
{
	bool bCustom;		// 是否自定义
	bool bReverse;		// 反转图标次序
	bool bShowOnly;		// 仅显示图标
	XlIconSet is;		// 图标样式
	XlIcon arrIcon[5];	// 显示图标的规则：图标
	UiCfVo arrCv[4];	// 显示图标的规则：值 and 类型
};

struct UiCfColorScale
{
	bool b3ColorScale;
	UiCfVo arrCv[3];
	EtColor arrClr[3];
};

struct CondFormatItem
{
	ETFormatConditionType type;
	bool bStopIfTrue;
	bool bPercent;
	bool bBottom;
	bool bDuplicate;
	int opt;
	int nPriority;
	int nStdDev;
	int idx;
	QString strRank;
	QString formula1;
	QString formula2;
	QString strCfRuleNotes;
	UiCfColorScale cs;
	std::shared_ptr<UiCfDataBar> pDB;
	std::shared_ptr<UiCfIconSet> pIS;
	std::shared_ptr<KXF> pKXF;
	ks_stdptr<Range> m_spRange;
	WebID objId;
	AbsObject* pObject;

	CondFormatItem()
	{
		// invalid
		opt         = -1;
		nPriority   = -1;
		nStdDev     = -1;
		idx         = -1;
		objId       = 0;
		pObject		= NULL;
		// default
		bStopIfTrue = false;
	}
	~CondFormatItem()
	{
	}

	void ensureDB()
	{
		pDB.reset(new UiCfDataBar);
	}

	void ensureIS()
	{
		pIS.reset(new UiCfIconSet);
	}

	void ensureKXF()
	{
		pKXF.reset(new KXF);
	}
};

class CF_ApiWrapper
{
public:
	virtual HRESULT ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject**, bool lastone = false) = 0;
	virtual HRESULT ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds) = 0;
	virtual void ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond) = 0;
	virtual void SwapPriority(FormatConditions *pConds, long lUp, long lDown) = 0;
	virtual HRESULT Delete(IKCoreObject *pCore) = 0;
	virtual VARIANT_BOOL GetStopIfTrue(IKCoreObject *pCore) = 0;
	virtual void SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL) = 0;
	virtual void GetAppliesTo(IKCoreObject *pCore, Range **ppRg) = 0;
	virtual HRESULT SetAppliesTo(IKCoreObject *pCore, Range *pRg) = 0;
	virtual long GetPriority(IKCoreObject* pCore) = 0;
	virtual void SetPriority(IKCoreObject* pCore, long nPriority) = 0;
	virtual void SetFirstPriority(IKCoreObject* pCore) = 0;
	virtual void SetLastPriority(IKCoreObject* pCore) = 0;
};

class FormatConditionWrapper : public CF_ApiWrapper
{
	HRESULT ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject**, bool lastone = false) override;
	HRESULT ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds) override;
	void ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond) override;
	void SwapPriority(FormatConditions *pConds, long lUp, long lDown) override;
	HRESULT Delete(IKCoreObject *pCore) override;
	VARIANT_BOOL GetStopIfTrue(IKCoreObject *pCore) override;
	void SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL) override;
	void GetAppliesTo(IKCoreObject *pCore, Range **ppRg) override;
	HRESULT SetAppliesTo(IKCoreObject *pCore, Range *pRg) override;
	long GetPriority(IKCoreObject* pCore) override;
	void SetPriority(IKCoreObject* pCore, long nPriority) override;
	void SetFirstPriority(IKCoreObject* pCore) override;
	void SetLastPriority(IKCoreObject* pCore) override;
};

class Top10Wrapper : public CF_ApiWrapper
{
	HRESULT ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject**, bool lastone = false) override;
	HRESULT ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds) override;
	void ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond) override;
	void SwapPriority(FormatConditions *pConds, long lUp, long lDown) override;
	HRESULT Delete(IKCoreObject *pCore) override;
	VARIANT_BOOL GetStopIfTrue(IKCoreObject *pCore) override;
	void SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL) override;
	void GetAppliesTo(IKCoreObject *pCore, Range **ppRg) override;
	HRESULT SetAppliesTo(IKCoreObject *pCore, Range *pRg) override;
	HRESULT ImportInner(CondFormatItem *pItem, IKCoreObject *pCore);
	long GetPriority(IKCoreObject* pCore) override;
	void SetPriority(IKCoreObject* pCore, long nPriority) override;
	void SetFirstPriority(IKCoreObject* pCore) override;
	void SetLastPriority(IKCoreObject* pCore) override;
};

class AboveAverageWrapper : public CF_ApiWrapper
{
	HRESULT ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject**, bool lastone = false) override;
	HRESULT ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds) override;
	void ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond) override;
	void SwapPriority(FormatConditions *pConds, long lUp, long lDown) override;
	HRESULT Delete(IKCoreObject *pCore) override;
	VARIANT_BOOL GetStopIfTrue(IKCoreObject *pCore) override;
	void SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL) override;
	void GetAppliesTo(IKCoreObject *pCore, Range **ppRg) override;
	HRESULT SetAppliesTo(IKCoreObject *pCore, Range *pRg) override;
	HRESULT ImportInner(CondFormatItem *pItem, IKCoreObject *pCore);
	long GetPriority(IKCoreObject* pCore) override;
	void SetPriority(IKCoreObject* pCore, long nPriority) override;
	void SetFirstPriority(IKCoreObject* pCore) override;
	void SetLastPriority(IKCoreObject* pCore) override;
};

class UniqueValuesWrapper : public CF_ApiWrapper
{
	HRESULT ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject**, bool lastone = false) override;
	HRESULT ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds) override;
	void ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond) override;
	void SwapPriority(FormatConditions *pConds, long lUp, long lDown) override;
	HRESULT Delete(IKCoreObject *pCore) override;
	VARIANT_BOOL GetStopIfTrue(IKCoreObject *pCore) override;
	void SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL) override;
	void GetAppliesTo(IKCoreObject *pCore, Range **ppRg) override;
	HRESULT SetAppliesTo(IKCoreObject *pCore, Range *pRg) override;
	HRESULT ImportInner(CondFormatItem *pItem, IKCoreObject *pCore);
	long GetPriority(IKCoreObject* pCore) override;
	void SetPriority(IKCoreObject* pCore, long nPriority) override;
	void SetFirstPriority(IKCoreObject* pCore) override;
	void SetLastPriority(IKCoreObject* pCore) override;
};

class ColorScaleWrapper : public CF_ApiWrapper
{
	HRESULT ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject**, bool lastone = false) override;
	HRESULT ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds) override;
	void ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond) override;
	void SwapPriority(FormatConditions *pConds, long lUp, long lDown) override;
	HRESULT Delete(IKCoreObject *pCore) override;
	VARIANT_BOOL GetStopIfTrue(IKCoreObject *pCore) override;
	void SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL) override;
	void GetAppliesTo(IKCoreObject *pCore, Range **ppRg) override;
	HRESULT SetAppliesTo(IKCoreObject *pCore, Range *pRg) override;
	HRESULT ImportInner(CondFormatItem *pItem, IKCoreObject *pCore);
	long GetPriority(IKCoreObject* pCore) override;
	void SetPriority(IKCoreObject* pCore, long nPriority) override;
	void SetFirstPriority(IKCoreObject* pCore) override;
	void SetLastPriority(IKCoreObject* pCore) override;
};

class DatabarWrapper : public CF_ApiWrapper
{
	HRESULT ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject**, bool lastone = false) override;
	HRESULT ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds) override;
	void ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond) override;
	void SwapPriority(FormatConditions *pConds, long lUp, long lDown) override;
	HRESULT Delete(IKCoreObject *pCore) override;
	VARIANT_BOOL GetStopIfTrue(IKCoreObject *pCore) override;
	void SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL) override;
	void GetAppliesTo(IKCoreObject *pCore, Range **ppRg) override;
	HRESULT SetAppliesTo(IKCoreObject *pCore, Range *pRg) override;
	HRESULT ImportInner(CondFormatItem *pItem, IKCoreObject *pCore);
	long GetPriority(IKCoreObject* pCore) override;
	void SetPriority(IKCoreObject* pCore, long nPriority) override;
	void SetFirstPriority(IKCoreObject* pCore) override;
	void SetLastPriority(IKCoreObject* pCore) override;
};

class IconSetWrapper : public CF_ApiWrapper
{
	HRESULT ImportFromItem(CondFormatItem *pItem, FormatConditions *pConds, AbsObject**, bool lastone = false) override;
	HRESULT ModifyFromItem(CondFormatItem *pItem, long lOldIdx, FormatConditions *pConds) override;
	void ItemFromCore(CondFormatItem *pItem, IKCoreObject *pCond) override;
	void SwapPriority(FormatConditions *pConds, long lUp, long lDown) override;
	HRESULT Delete(IKCoreObject *pCore) override;
	VARIANT_BOOL GetStopIfTrue(IKCoreObject *pCore) override;
	void SetStopIfTrue(IKCoreObject *pCore, VARIANT_BOOL) override;
	void GetAppliesTo(IKCoreObject *pCore, Range **ppRg) override;
	HRESULT SetAppliesTo(IKCoreObject *pCore, Range *pRg) override;
	HRESULT ImportInner(CondFormatItem *pItem, IKCoreObject *pCore);
	long GetPriority(IKCoreObject* pCore) override;
	void SetPriority(IKCoreObject* pCore, long nPriority) override;
	void SetFirstPriority(IKCoreObject* pCore) override;
	void SetLastPriority(IKCoreObject* pCore) override;
};

class CF_ApiWrapperFactory
{
public:
	static CF_ApiWrapper* getWrapper(ETFormatConditionType type);
};

#endif