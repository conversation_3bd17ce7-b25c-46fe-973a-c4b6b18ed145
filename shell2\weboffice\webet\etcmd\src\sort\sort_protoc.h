﻿#ifndef __WEBOFFICE_WEBET_SORT_PROTOC_H__
#define __WEBOFFICE_WEBET_SORT_PROTOC_H__

namespace wo
{
namespace range_sort
{
//因为原排序就使用二进制了，所以这里也使用二进制
//以下常量定义为服务端和前端的二进制协议，不能修改其值。

enum KSortOrder
{
    etAscending = 1,
    etDescending = 2,
};

enum KSortOrientation
{
    etSortColumns = 1,
    etSortRows = 2,
};

enum KSortMethod
{
    etPinYin = 1,
    etStroke = 2,
};

enum KSortOn
{
    xlSortOnValues = 0,
    xlSortOnCellColor = 1,
    xlSortOnFontColor = 2,
    xlSortOnIcon = 3
};

bool ReadSortOption(const binary_wo::VarObj &param, bool *case_sensitive, ETSortOrientation *orientation, ETSortMethod *sort_method, ETYesNoGuess *header_guess);
bool ReadSortField(const binary_wo::VarObj &param, int32_t *sort_on, int32_t *sort_order, KComVariant *custom_order);
bool ReadSortOnValue(IBook *book, const binary_wo::VarObj &param, int32_t sort_on, IKSortField *sort_field);

void WriteSortOption(ISerialAcceptor *acpt, bool case_sensitive, ETSortOrientation orientation, ETSortMethod sort_method, bool hasHeader, bool hasFilterHeader);
void WriteSortField(ISerialAcceptor *acpt, int32_t sort_on, int32_t sort_order, const QStringList &custom_order);
bool WriteSortOnValue(IBook *book, ISerialAcceptor *acpt, int32_t sort_on, ks_stdptr<ICellColor> &spCellColor,
                      ks_stdptr<IFontColor> &spFontColor, ks_stdptr<ICellIcon> &spCellIcon);

void WriteSortKeyRange(ISerialAcceptor *acpt, ETSortOrientation sortOrientation, const RANGE& range);
void WriteColor(ISerialAcceptor *acpt, const EtColor &ec, IBook *bk, KeyName tag = NULL);
void WriteFill(ISerialAcceptor *acpt, const EtFill &fill, IBook *bk, KeyName tag = NULL);

} // namespace range_sort
} // namespace wo

#endif