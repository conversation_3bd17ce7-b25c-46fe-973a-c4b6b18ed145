﻿#ifndef __WO_RANGE_DESCRIPTION_H__
#define __WO_RANGE_DESCRIPTION_H__

#include <QMap>
#include <public_header/chart/src/model/kctcells.h>
#include "message_producer.h"
#include <etchart/etchart_types.h>

namespace chart {
    enum ContextResult ENUM_DECL(unsigned);
};

namespace wo {
using namespace etoldapi;

enum SourceCalcType
{
    CalcNotifyNone = 0x0,
    CalcNotifyName = 0x1,
    CalcNotifyCategory = 0x1 << 1,
    CalcNotifyValue = 0x1 << 2,
    CalcNotifyBubble = 0x1 << 3,
    CalcNotifyAll = CalcNotifyName |CalcNotifyCategory |CalcNotifyValue |CalcNotifyBubble
};

struct HandleEle {
    HANDLE hd;
    INT idxFmla;
    bool bName;
    HandleEle() {
        hd = nullptr;
        idxFmla = -1;
        bName = false;
    }
};

class ChartMessgeProducer;
class RemoteChartLinkManager;

class RemoteCalcNotifyBase: public IImmobileRegionMonitor
{
public:
    RemoteCalcNotifyBase(SourceCalcType nCalcType, bool bRegion = false);
    virtual ~RemoteCalcNotifyBase();

public:
    void SetSubscription(SeriesRangeSubscription*);
    void InitContext(IBook* pBook, const int sheetIdx, const int idx);
    chart::ContextResult RegNotify(const QString& context, bool bUpdate = false, ITokenVectorInstant** ppIns = nullptr);

    virtual bool onCalcUpdate(const_token_ptr pToken = nullptr);
    void connectProducer(ChartMessgeProducer* producer);

    int GetIdx();
    bool IsHidden();
    IKRanges* GetRange();
    const chart::KCTSglCells* GetValues();
    const QString GetNFmla();
    const QString GetBFmla();

    SeriesDirection GetDirection();
    void SetDirection(const SeriesDirection dir);
    void EnableRegion(bool bRegion);
public:
    STDPROC_(BOOL) IsNeedDumpSup(HANDLE) { return TRUE; }
    STDPROC OnValueChange(HANDLE);
    STDPROC OnRegionOperation(HANDLE, INT bookID, const RANGE& afterOp, RegionOperationCode code);
    STDPROC OnNameIdChanged(HANDLE, INT bookID, INT nameID);
    STDPROC OnCalculate(HANDLE);

protected:
    void modifyByStref(const_token_ptr, chart::KCTSglCells**);
    void modifyByVector(const_token_ptr, chart::KCTSglCells**);
    void modifyByDefault(const_token_ptr, chart::KCTSglCells**);

protected:
    const_token_ptr prepareUpdate();
    IAreaService* GetAreaService();
    void UnRegNotifyAll();
    void ReRegHandle();

    HRESULT RegRegionMonitor(HANDLE, INT bookId, const RANGE&, RegionOperationCode);
    HRESULT RegWatchedName(HANDLE, INT bookId, INT nameId);

    HRESULT OnFmlaChanged();

    virtual chart::ContextResult checkValueType(const QString &context, const_token_ptr token, QString &outContext, bool bNameFormula = false);
    virtual chart::ContextResult checkRangeType(IKRanges *ranges);

    chart::ContextResult modifyContext(IFormula* pFmla, const QString& inputContext = QString(""));
    bool unSupportFmla(ITokenVectorInstant* pVec);

    void handleMatrix(const_token_ptr, chart::KCTSglCells**);
    void handleCommon(const_token_ptr, chart::KCTSglCells**);

    void handleMatrix(const_token_ptr, chart::KCTMultiSglCells**);
    void handleCommon(const_token_ptr, chart::KCTMultiSglCells**);

protected:
    int m_sheetIdx;
    IBook* m_pBook;
    IAreaService *m_pAreaService;

    std::vector<HandleEle> m_hVec;
    exec_token_vector m_vecToken;
    alg::managed_token_assist m_token;

    QString m_nFmla; //normal formula
    QString m_bFmla; //book formula

    SeriesDirection m_dir;
    SourceCalcType m_calcUnitType;
    ks_stdptr<IKRanges> m_spRanges;

    et_sptr<chart::KCTSglCells> m_spCache;
    ChartMessgeProducer* m_producer;
    SeriesRangeSubscription* m_pSubsriber;

    int m_idx;
    bool m_bHidden;
    bool m_bRegion;
    const SourceCalcType m_calcType;
};

class RemoteCalcNameNotify: public RemoteCalcNotifyBase
{
public:
    RemoteCalcNameNotify(SourceCalcType, bool bRegion = false);
public:
    std::vector<QString>& GetNames();
    virtual bool onCalcUpdate(const_token_ptr pToken = nullptr) override;
protected:
    virtual chart::ContextResult checkValueType(const QString &context, const_token_ptr token, QString &outContext, bool bNameFormula = false) override;
    virtual chart::ContextResult checkRangeType(IKRanges *) override;

    bool valueFromToken(etexec::const_stref_token_assist &stRefToken);

private:
    std::vector<QString> m_names;
};

class RemoteCalcCateNotify: public RemoteCalcNotifyBase
{
public:
    RemoteCalcCateNotify(SourceCalcType, bool bRegion = false);
public:
    virtual bool onCalcUpdate(const_token_ptr pToken = nullptr) override;
    const chart::KCTMultiSglCells* GetCategory();

protected:
    virtual chart::ContextResult checkValueType(const QString &context, const_token_ptr token, QString &outContext, bool bNameFormula = false) override;
    virtual chart::ContextResult checkRangeType(IKRanges *) override;
protected:
    void modifyByStref(const_token_ptr, chart::KCTMultiSglCells**);
    void modifyByVector(const_token_ptr, chart::KCTMultiSglCells**);
    void modifyByDefault(const_token_ptr, chart::KCTMultiSglCells**);

private:
    et_sptr<chart::KCTMultiSglCells> m_spCategories;
};

struct NotifierGroup {
    RemoteCalcNameNotify* nameNotifier;
    RemoteCalcCateNotify* cateNotifier;
    RemoteCalcNotifyBase* valueNotifier;
    RemoteCalcNotifyBase* bubbleNotifier;

    NotifierGroup(bool bRegion = false)
    : nameNotifier(nullptr)
    , cateNotifier(nullptr)
    , valueNotifier(nullptr)
    , bubbleNotifier(nullptr)
    {
        nameNotifier  = new RemoteCalcNameNotify(CalcNotifyName, bRegion);
        cateNotifier  = new RemoteCalcCateNotify(CalcNotifyCategory, bRegion);
        valueNotifier = new RemoteCalcNotifyBase(CalcNotifyValue, bRegion);
        bubbleNotifier = new RemoteCalcNotifyBase(CalcNotifyBubble, bRegion);
    }

    void InitContext(IBook* pBook, const int sheetIdx, const int idx) {
        nameNotifier->InitContext(pBook, sheetIdx, idx);
        cateNotifier->InitContext(pBook, sheetIdx, idx);
        valueNotifier->InitContext(pBook, sheetIdx, idx);
        bubbleNotifier->InitContext(pBook, sheetIdx, idx);
    }

    ~NotifierGroup() {
        delete nameNotifier; nameNotifier = nullptr;
        delete cateNotifier; cateNotifier = nullptr;
        delete valueNotifier; valueNotifier = nullptr;
        delete bubbleNotifier; bubbleNotifier = nullptr;
    }
};

class RemoteChartLinkManager;
//订阅series的范围数据变动监听
class SeriesRangeSubscription {
public:
    SeriesRangeSubscription(RemoteChartLinkManager* );
    ~SeriesRangeSubscription();

public:
    void InitUser(const QString& connId, const QString& userId, const QString& cuid);

    NotifierGroup* GetNotifyGroup(const int idx, bool bInsert = true);
    int GetGroupSize();
    void SetGroupSize(const int size);

    NotifierGroup* GetRegionGroup();
    void UpdateRegion(SeriesDirection dir,
        const QString& nameFmla,
        const QString& valueFmla,
        const QString& cateFmla);

    ChartMessgeProducer* GetProducer();
    void CheckProducer();

    BOOL b1904();
    void Setb1904(BOOL b1904);

    void SetChartType(int chartType);
    chart::ChartType GetChartType();

    void NotifyRangeDivide();
    bool DoExportData();
protected:
    void EnsureProducer();
    void ConnectProducer(NotifierGroup* group);

protected:
    RemoteChartLinkManager* m_pManager;
private:
    BOOL m_b1904;
    chart::ChartType m_chartType;

    QString m_connId, m_userId;
    ChartMessgeProducer* m_producer;

    QMap<int, NotifierGroup*> m_notifyGroup;
    int m_nGroups;

    NotifierGroup* m_pRegion;

    bool m_bModified;
    BinaryMessage m_message;
};

}

#endif
