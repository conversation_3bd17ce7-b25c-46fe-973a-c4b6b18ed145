﻿#include "etstdafx.h"

#include "et_dbsheet_common_helper.h"
#include "et_dbsheet_block_init_helper.h"
#include "webbase/binvariant/binvarobj.h"
#include "appcore/et_appcore_dbsheet.h"
#include "appcore/et_appcore_enum.h"
#include "src/workbook.h"
#include "wo/workbook_obj.h"
#include "et_revision_context_impl.h"
#include "etcore/little_alg.h"

namespace wo {

constexpr int kMaxGridRecord = 40;
constexpr int kMaxGridField = 30;
constexpr int kMaxKanBanGroup = 10;
constexpr int kMaxKanBanGroupRecord = 10;
constexpr int kMaxField = 10;
constexpr int kMaxGalleryRecord = 30;


DBSheetInitHelper::DBSheetInitHelper(KEtWorkbook * workbook)
: m_workbook(workbook)
, m_commonHelper(workbook)
{

}

void DBSheetInitHelper::afterInitBlocks(KEtRevisionContext& ctx, bool isEmptyAddZero)
{
    IDX sheetIdx = INVALIDIDX;
    m_spDbView->GetSheetOp()->GetIndex(&sheetIdx);
    IWorkbookObj* wbObj = m_workbook->GetCoreWorkbook()->GetWoObject();
    IWorksheetObj * wsObj = wbObj->getSheetItem(sheetIdx);

    std::vector<RECT> vtblocks;
    makeBlocks(vtblocks, isEmptyAddZero);
    ctx.setInitBlock(wsObj->objId(), vtblocks);
    ks_castptr<IKETUserConn> uc = ctx.getUser();
    uc->getBlockPointCache()->addCompleteInstallSheet(wsObj->objId());

    m_spDbView.clear();
    clear();
}

void DBSheetInitHelper::setDbView(IDBSheetView* pView)
{
    m_spDbView = pView;
}

bool DBSheetInitHelper::setDbInitBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &blocks)
{
    UINT sheetStId = blocks.field_uint32("sheetStId");
    EtDbId viewId = m_commonHelper.GetEtDbId(blocks, "viewId");

    ks_stdptr<IDBSheetView> spDbSheetView;
    ks_stdptr<IDBSheetViews> spDbSheetViews;
    m_commonHelper.GetDBSheetView(sheetStId, viewId, &spDbSheetView, &spDbSheetViews);
    if (!spDbSheetView) return false;

    m_spDbView = spDbSheetView;
    bool isEmptyAddZero = true;
    switch (m_spDbView->GetType()) {
        case et_DBSheetView_Grid:
        case et_DBSheetView_Query:
        case et_DBSheetView_Gantt:
            initDbGridBlocks(ctx, blocks);
            break;
        case et_DBSheetView_Kanban:
            initDbKanBanBlocks(ctx, blocks);
            break;
        case et_DBSheetView_Gallery:
            initDbGalleryBlocks(ctx, blocks);
            break;
        case et_DBSheetView_Form: // do nothing
            isEmptyAddZero = false;
            break;
        case et_DBSheetView_Calendar:
            initDbCalendarBlocks(ctx, blocks);
            break;
    }

    afterInitBlocks(ctx, isEmptyAddZero);
    return true;
}

void DBSheetInitHelper::clear()
{
    m_rows.clear();
    m_cols.clear();
}

void DBSheetInitHelper::initDbGridBlocks(KEtRevisionContext& ctx, EtDbId ltRecId, EtDbId ltFldId, EtDbIdx fltFldId, int frozenCols)
{
    // row
    EtDbIdx from = m_spDbView->GetVisibleRecords()->Id2Idx(ltRecId);
    appendRows(from, from + kMaxGridRecord);

    // col
    from = m_spDbView->GetVisibleFields()->Id2Idx(ltFldId);
    if (from == INV_EtDbIdx) return;
    if (frozenCols > 0) {
        appendCols(from, from + frozenCols); // [from, from + frozenCols)

        EtDbIdx frozenFldIdx = m_spDbView->GetVisibleFields()->Id2Idx(fltFldId);
        if (frozenFldIdx == INV_EtDbIdx || frozenFldIdx < (from + frozenCols)) {
            frozenFldIdx = from + frozenCols;
        }
        from = frozenFldIdx;
        appendCols(from, from + (kMaxGridField - frozenCols)); // [from + frozenCols, max)
    } else {
        appendCols(from, from + kMaxGridField);
    }
}

void DBSheetInitHelper::initDbGridBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &param)
{
    if (!param.has("leftTop")) return;

    VarObj leftTop = param.get("leftTop");
    if (!leftTop.has("recordId") || !leftTop.has("fieldId"))
        return;
    EtDbId recId = m_commonHelper.GetEtDbId(leftTop, "recordId");
    EtDbId fldId = m_commonHelper.GetEtDbId(leftTop, "fieldId");
    ks_stdptr<IDBSheetView_Grid> spDbGridView = m_spDbView;

    EtDbId frozenFldId = 0;
    int frozenCols = 0;
    spDbGridView->GetFrozenCols(&frozenCols);
    if (param.has("frozenLeftTop") && frozenCols > 0) {
        frozenFldId = m_commonHelper.GetEtDbId(param.get("frozenLeftTop"), "fieldId");
    }
    initDbGridBlocks(ctx, recId, fldId, frozenFldId, frozenCols);
}

void DBSheetInitHelper::initDbKanBanBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &param)
{
    if (!param.has("groupRecordIds")) return;

    // row
    VarObj recIds = param.get("groupRecordIds");
    int cnt = std::min(recIds.arrayLength_s(), kMaxKanBanGroup); // 容错
    EtDbIdx preFrom = INV_EtDbIdx, preTo;
    for (int i = 0; i < cnt; ++i) {
        EtDbId recId = m_commonHelper.GetEtDbId(recIds.at(i));
        EtDbIdx from = m_spDbView->GetVisibleRecords()->Id2Idx(recId);
        EtDbIdx to = from + kMaxKanBanGroupRecord;

        if (preFrom != INV_EtDbIdx && (preFrom < from && from < preTo)) {
            // 与前一个有交集.
            appendRows(preTo, to);
        } else {
            appendRows(from, to);
        }

        preFrom = from;
        preTo = to;
    }

    // col
    appendCols(0, kMaxField);
}

void DBSheetInitHelper::initDbGalleryBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &param)
{
    if (!param.has("gallery")) return;

    // row
    VarObj gallery = param.get("gallery");
    if (!gallery.has("recordId")) return;

    EtDbId recId = m_commonHelper.GetEtDbId(gallery, "recordId");
    EtDbIdx from = m_spDbView->GetVisibleRecords()->Id2Idx(recId);
     int len = kMaxGalleryRecord;
    if (gallery.has("length")) len = std::min(gallery.field_int32("length"), len);
    appendRows(from, from + len);

    // col
    appendCols(0, kMaxField);
}

void DBSheetInitHelper::initDbCalendarBlocks(KEtRevisionContext& ctx, const binary_wo::VarObj &param)
{
    if (!param.has("calendar")) return;

    VarObj calendar = param.get("calendar");
    if (!calendar.has("row") || !calendar.has("col")) return;

    EtDbIdx row = calendar.field_int32("row");
    EtDbIdx col = calendar.field_int32("col");
    if (row == INV_EtDbIdx)
        row = 0;
    if (col == INV_EtDbIdx)
        col = 0;

    // 行
    appendRows(0, row);
    // 列：存在隐藏字段依然需要展示
    for (EtDbIdx i = 0; i < col; ++i) {
        EtDbId id = m_spDbView->GetAllFields()->IdAt(i);
        m_cols.insert(m_spDbView->GetAllFields()->Id2Idx(id) / BLK_COLS_COUNT);
    }
}

void DBSheetInitHelper::appendRows(EtDbIdx from, EtDbIdx to)
{
    if (from == INV_EtDbIdx) return;
    to = std::min(m_spDbView->GetVisibleRecords()->Count(), to);

    for (EtDbIdx i = from; i < to; ++i) {
        EtDbId id = m_spDbView->GetVisibleRecords()->IdAt(i);
        EtDbIdx row = m_spDbView->GetAllRecords()->Id2Idx(id);
        m_rows.insert(row / BLK_ROWS_COUNT); // row block index.
    }
}

void DBSheetInitHelper::appendCols(EtDbIdx from, EtDbIdx to)
{
    if (from == INV_EtDbIdx) return;
    to = std::min(m_spDbView->GetVisibleFields()->Count(), to);

    for (EtDbIdx i = from; i < to; ++i) {
        EtDbId id = m_spDbView->GetVisibleFields()->IdAt(i);
        EtDbIdx col = m_spDbView->GetAllFields()->Id2Idx(id);
        m_cols.insert(col / BLK_COLS_COUNT);
    }
}

void DBSheetInitHelper::makeBlocks(std::vector<RECT> &blocks, bool isEmptyAddZero)
{
    if (m_rows.empty() && isEmptyAddZero) m_rows.insert(0);
    if (m_cols.empty() && isEmptyAddZero) m_cols.insert(0);

    for (auto r = m_rows.begin(); r != m_rows.end(); ++r) {
        for (auto c = m_cols.begin(); c != m_cols.end(); ++c) {
            RECT rc = Rect_CreateScaleNone();
            rc.top = (*r) * BLK_ROWS_COUNT;
            rc.left = (*c) * BLK_COLS_COUNT;
            rc.bottom = rc.top + BLK_ROWS_COUNT - 1;
            rc.right = rc.left + BLK_COLS_COUNT - 1;
            blocks.push_back(rc);
        }
    }
}

}

