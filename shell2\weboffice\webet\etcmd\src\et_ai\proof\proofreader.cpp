﻿#include "proofreader.h"
#include "etstdafx.h"
#include "proofreaderdata.h"

namespace etai
{
namespace
{
void SetRangeVisible(IKEtView *pEtView, const RANGE &rg)
{
    if (NULL == pEtView || !rg.IsValid())
        return;

    IRenderMeasure *rdMeasure = pEtView->GetRenderView()->GetRenderMeasure();
    if (pEtView->GetFreeze())
    {
        CELL ltCell = {0, 0}, freezeLtCell = {0, 0};
        rdMeasure->CalcFreezeVisibleCellByRg(rg, ltCell, freezeLtCell);
        pEtView->SetLTCell(ltCell);
        pEtView->SetFreezeLTCell(freezeLtCell);
    }
    else
    {
        CELL ltCell = {0, 0};
        rdMeasure->CalcNormalVisibleCellByRg(rg, ltCell);
        ltCell.row = MIN(ltCell.row, rg.RowFrom());
        ltCell.col = MIN(ltCell.col, rg.ColFrom());
        pEtView->SetLTCell(ltCell);
    }
}

HRESULT cellMajorIsStr(etoldapi::Range *range, ROW row, COL col, bool *isStr)
{
    ks_stdptr<_Worksheet> spWorksheet;
    HRESULT hr = range->get_Worksheet(&spWorksheet);
    if (FAILED(hr))
        return E_FAIL;

    ks_stdptr<IKWorksheet> spWorksheetInfo;
    hr = spWorksheet->QueryInterface(IID_IKWorksheet, (void **)&spWorksheetInfo);
    if (FAILED(hr))
        return E_FAIL;

    ks_stdptr<ISheet> spISheet = spWorksheetInfo->GetSheet();
    if (spISheet == NULL)
        return E_FAIL;

    ks_stdptr<IBook> spIBook;
    hr = spISheet->GetBook(&spIBook);
    if (FAILED(hr))
        return E_FAIL;

    ks_stdptr<IBookOp> spIBookOp;
    hr = spIBook->GetOperator(&spIBookOp);
    if (FAILED(hr))
        return E_FAIL;

    IDX sheetID = INVALIDIDX;
    hr = spISheet->GetIndex(&sheetID);
    if (FAILED(hr))
        return E_FAIL;

    const etexec::ExecToken *pToken = NULL;
    hr = spIBookOp->GetCellValue(sheetID, row, col, &pToken);
    if (FAILED(hr))
        return E_FAIL;

    *isStr = etexec::ETP_VSTR == etexec::const_token_assist(pToken).major_type();

    return S_OK;
}
} // namespace

Corrector::Corrector(IKWorkbook *workbook, IKWorksheet *worksheet, const Correcte &correcte, const QString &fmt,
                     const QVariant &val, int correctType)
    : m_workbook(workbook), m_worksheet(worksheet), m_correcte(correcte), m_fmt(fmt), m_val(val),
      m_correctType(correctType)
{
}

Corrector::~Corrector()
{
}

bool Corrector::dataHasBeenEdited() const
{
    if (m_correctType == CorrectType_Uniform_Format)
        return dataHasBeenEdited(m_correcte);

    return false;
}

bool Corrector::dataHasBeenEdited(const CellErrorList &cellList)
{
    for (size_t i = 0; i < cellList.size(); i++)
    {
        if (dataHasBeenEdited(*cellList.at(i)))
            return true;
    }

    return false;
}

bool Corrector::dataHasBeenEdited(const CorrecteData &recognizeData)
{
    for (size_t i = 0; i < recognizeData.correcteList().size(); i++)
    {
        const Correcte *correcte = recognizeData.correcteList().at(i);
        if (correcte->columnError() != NULL && correcte->status() == Status_Untreated)
        {
            if (dataHasBeenEdited(*correcte))
                return true;
        }
    }

    return false;
}

bool Corrector::dataHasBeenEdited(const Correcte &correcte)
{
    if (correcte.columnError()->unifiedCellErrorList() != NULL)
    {
        if (dataHasBeenEdited(*correcte.columnError()->unifiedCellErrorList()))
            return true;
    }

    if (correcte.columnError()->normCellErrorList() != NULL)
    {
        if (dataHasBeenEdited(*correcte.columnError()->normCellErrorList()))
            return true;
    }

    return false;
}

void Corrector::saveOldValue(CellErrorBase *cell)
{
    if (!cell->hasOldNumberFormatLocal())
    {
        ks_bstr oldNumberFormatLocal;
        HRESULT hr = cell->range()->get_NumberFormatLocal(&oldNumberFormatLocal);
        if (SUCCEEDED(hr))
            cell->setOldNumberFormatLocal(oldNumberFormatLocal.c_str());
        bool isStr = false;
        hr = cellMajorIsStr(cell->range(), cell->rowIndex(), cell->cube().colFrom, &isStr);
        if (SUCCEEDED(hr) && isStr)
        {
            ks_bstr oldText;
            cell->range()->get_Text(&oldText);
            cell->setOldText(oldText.c_str());
        }
    }
}

bool Corrector::run()
{
    bool succeeded = false;
    if (m_correctType == CorrectType_Uniform_Format)
        succeeded = runFmt();
    else if (m_correctType == CorrectType_Content)
        succeeded = runCont();

    return succeeded;
}

bool Corrector::checkRange(Range *range)
{
    return range != NULL && !range->IsDestroyed() && range->IsObjectValid();
}

bool Corrector::correctOneKeyFmt(const CorrecteData &recognizeData, const QVariantMap &args)
{
    for (size_t i = 0; i < recognizeData.correcteList().size(); i++)
    {
        const Correcte *correcte = recognizeData.correcteList().at(i);
        if (correcte->columnError() != NULL && correcte->status() == Status_Untreated)
        {
            QString fmt = args.value(QString::number(i), "").toString();
            if (fmt.isEmpty() && correcte->columnError()->unifiedNormalFormatLocalList() != NULL &&
                correcte->columnError()->unifiedNormalFormatLocalList()->size() > 0)
                fmt = correcte->columnError()->unifiedNormalFormatLocalList()->at(0)->formatLocal();

            Corrector corrector(recognizeData.workbook(), recognizeData.worksheet(), *correcte, fmt, QString(),
                                CorrectType_Uniform_Format);

            if (!corrector.run())
                return false;
        }
    }

    return true;
}

bool Corrector::dataHasBeenEdited(const CellErrorBase &cell)
{
    if (!checkRange(cell.range()))
        return true;

    long count = 0;
    cell.range()->get_Count(&count);
    if (count == 0)
        return true;

    ks_bstr bstrText;
    HRESULT hr = cell.range()->get_Text(&bstrText);
    if (SUCCEEDED(hr))
    {
        if (!bstrText.empty())
        {
            const QString value = krt::fromUtf16(bstrText.c_str());
            if (value.compare(cell.content(), Qt::CaseSensitive) != 0)
                return true;
        }
        else
        {
            return !cell.content().isEmpty();
        }
    }

    return false;
}

bool Corrector::setCellFmt(const CellError &cell, const ks_bstr &fmt)
{
    return setCellFmt(cell, cell.value2(), fmt);
}

bool Corrector::setCellFmt(const CellError &cell, const QString &wval, const ks_bstr &fmt)
{
    if (!checkRange(cell.range()))
        return false;

    long count = 0;
    cell.range()->get_Count(&count);
    if (count == 0)
        return false;

    saveOldValue(&const_cast<CellError &>(cell));

    if (!fmt.empty())
        cell.range()->put_NumberFormatLocal(fmt);
    else if (!cell.numberFormatLocal().isEmpty())
        cell.range()->put_NumberFormatLocal(ks_bstr(krt::utf16(cell.numberFormatLocal())));
    else
        cell.range()->put_NumberFormatLocal(ks_bstr(__X("G/通用格式")));

    VARIANT_BOOL hasFormula;
    cell.range()->get_HasFormula(&hasFormula);
    if (!hasFormula)
    {
        if (!cell.hasOldValue2())
        {
            KComVariant oldValue2;
            HRESULT hr = cell.range()->get_Value2(&oldValue2);
            if (SUCCEEDED(hr))
                const_cast<CellError &>(cell).setOldValue2(oldValue2);
        }

        KComVariant val2;
        ks_bstr bstr(krt::utf16(wval));
        val2.AssignBSTR(bstr);
        cell.range()->put_Value2(val2);

        return true;
    }

    return false;
}

bool Corrector::runFmt()
{
    ASSERT(m_worksheet != NULL);
    ASSERT(m_workbook != NULL);

    if (m_correcte.columnError() == NULL)
        return false;

    if (!checkRange(m_correcte.columnError()->range()))
        return false;

    long count = 0;
    m_correcte.columnError()->range()->get_Count(&count);
    if (count == 0)
        return false;

    const_cast<ColumnError *>(m_correcte.columnError())->setCorrecteUnifiedCellCount(0);
    const_cast<ColumnError *>(m_correcte.columnError())->setCorrecteNormCellCount(0);

    ks_bstr fmt(krt::utf16(m_fmt));

    if (m_correcte.columnError()->unifiedCellErrorList() != NULL)
    {
        for (size_t i = 0; i < m_correcte.columnError()->unifiedCellErrorList()->size(); i++)
        {
            const CellError &cellError = *m_correcte.columnError()->unifiedCellErrorList()->at(i);
            //如果数据被用户编辑过，则跳过此条更新
            if (dataHasBeenEdited(cellError))
                continue;

            if (setCellFmt(cellError, fmt))
            {
                calcCellCount(const_cast<ColumnError *>(m_correcte.columnError()), cellError);
            }
        }
    }

    if (m_correcte.columnError()->normCellErrorList() != NULL)
    {
        for (size_t i = 0; i < m_correcte.columnError()->normCellErrorList()->size(); i++)
        {
            const CellError &cellError = *m_correcte.columnError()->normCellErrorList()->at(i);
            //如果数据被用户编辑过，则跳过此条更新
            if (dataHasBeenEdited(cellError))
                continue;

            ks_bstr fmt(krt::utf16(cellError.numberFormatLocal()));
            if (setCellFmt(cellError, fmt))
            {
                calcCellCount(const_cast<ColumnError *>(m_correcte.columnError()), cellError);
            }
        }
    }

    const_cast<Correcte &>(m_correcte).deal();

    return true;
}

bool Corrector::runCont()
{
    ASSERT(m_worksheet != NULL);
    ASSERT(m_workbook != NULL);

    if (m_correcte.contentError() == NULL)
        return false;

    if (!checkRange(m_correcte.contentError()->range()))
        return false;

    long count = 0;
    m_correcte.contentError()->range()->get_Count(&count);
    if (count == 0)
        return false;

    VARIANT_BOOL hasFormula;
    m_correcte.contentError()->range()->get_HasFormula(&hasFormula);
    if (hasFormula)
        return false;

    if (checkRange(m_correcte.contentError()->range()))
    {
        long count = 0;
        m_correcte.contentError()->range()->get_Count(&count);
        if (count == 0)
            return false;

        saveOldValue(const_cast<ContentError *>(m_correcte.contentError()));

        if (!m_fmt.isEmpty())
            m_correcte.contentError()->range()->put_NumberFormatLocal(ks_bstr(krt::utf16(m_fmt)));

        KComVariant val2;
        if (m_val.isValid())
        {
            if (m_val.type() == QVariant::Int)
            {
                val2.AssignDouble(m_val.toInt());
            }
            else if (m_val.type() == QVariant::Bool)
            {
                val2.AssignBOOL(m_val.toBool());
            }
            else if (m_val.type() == QVariant::Double)
            {
                val2.AssignDouble(m_val.toDouble());
            }
            else if (m_val.type() == QVariant::Date)
            {
                val2.AssignDate(m_val.toDouble());
            }
            else if (m_val.type() == QVariant::String)
            {
                QString qstr = m_val.toString();
                ks_bstr bstr(krt::utf16(qstr));
                val2.AssignBSTR(bstr);
            }
            else
            {
                QString qstr = m_val.toString();
                ks_bstr bstr(krt::utf16(qstr));
                val2.AssignBSTR(bstr);
            }
        }

        if (!m_correcte.contentError()->hasOldValue2())
        {
            KComVariant oldValue2;
            HRESULT hr = m_correcte.contentError()->range()->get_Value2(&oldValue2);
            const_cast<ContentError *>(m_correcte.contentError())->setOldValue2(oldValue2);
        }

        m_correcte.contentError()->range()->put_Value(etRangeValueDefault, val2);
        const_cast<ContentError *>(m_correcte.contentError())->setNewValue(m_val);
    }

    const_cast<Correcte &>(m_correcte).deal();

    return true;
}

void Corrector::calcCellCount(ColumnError *columnError, const CellError &cellError)
{
    if (cellError.errorType() == ErrorType_UMultipleDate || cellError.errorType() == ErrorType_UMultipleTime)
        columnError->setCorrecteUnifiedCellCount(columnError->correcteUnifiedCellCount() + 1);
    else
        columnError->setCorrecteNormCellCount(columnError->correcteNormCellCount() + 1);
}

bool revokeCell(
	const CellErrorBase* cell)
{
	if (!Corrector::checkRange(cell->range()))
		return false;

	long count = 0;
	cell->range()->get_Count(&count);
	if (count == 0)
		return false;

	HRESULT hr = E_FAIL;
	if (!cell->hasOldText())
	{
		if (cell->hasOldNumberFormatLocal())
		{
			hr = cell->range()->put_NumberFormatLocal(
				ks_bstr(cell->oldNumberFormatLocal().c_str()));
			if (FAILED(hr))
				return false;
		}

		if (cell->hasOldValue2())
			hr = cell->range()->put_Value2(cell->oldValue2());
	}
	else
	{
		hr = cell->range()->put_NumberFormatLocal(ks_bstr(__X("@")));
		if (FAILED(hr))
			return false;

		KComVariant val2;
		ks_bstr val(cell->oldText().c_str());
		val2.AssignBSTR(val);

		hr = cell->range()->put_Value2(val2);
		if (FAILED(hr))
			return false;

		if (cell->hasOldNumberFormatLocal())
		{
			hr = cell->range()->put_NumberFormatLocal(
				ks_bstr(cell->oldNumberFormatLocal().c_str()));
			if (FAILED(hr))
				return false;
		}
	}

	return SUCCEEDED(hr);
}

bool Correct(const Correcte &correct)
{
    if (correct.columnError() != NULL)
    {
        if (!Corrector::checkRange(correct.columnError()->range()))
            return false;

        long count = 0;
        correct.columnError()->range()->get_Count(&count);
        if (count == 0)
            return false;

        if (correct.columnError()->unifiedCellErrorList() != NULL)
        {
            for (size_t i = 0; i < correct.columnError()->unifiedCellErrorList()->size(); i++)
            {
                if (!revokeCell(correct.columnError()->unifiedCellErrorList()->at(i)))
                    return false;
            }
        }

        if (correct.columnError()->normCellErrorList() != NULL)
        {
            for (size_t i = 0; i < correct.columnError()->normCellErrorList()->size(); i++)
            {
                if (!revokeCell(correct.columnError()->normCellErrorList()->at(i)))
                    return false;
            }
        }

        const_cast<Correcte &>(correct).untreated();
        return true;
    }
    else if (correct.contentError() != NULL)
    {
        if (!Corrector::checkRange(correct.contentError()->range()))
            return false;

        long count = 0;
        correct.contentError()->range()->get_Count(&count);
        if (count == 0)
            return false;

        if (!revokeCell(correct.contentError()))
            return false;

        const_cast<Correcte &>(correct).untreated();
        return true;
    }

    return false;
}

} // namespace etai