#include "create_app_view_helper.h"
#include "addTemplateSheets.h"
#include "dbsheet/et2db_exporter.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "helpers/webmime_helper.h"
#include "et_hard_define_strings.h"
#include "update_app_version_helper.h"
#include "et_revision_context_impl.h"
#include "utils/et_gridsheet_utils.h"

namespace CreateAppViewHelper
{

static PCWSTR ErrorParam = __X("ParamError");
static PCWSTR ErrorViewInvalidName = __X("ViewInvalidNameError");
static PCWSTR ErrorCreateView = __X("CreateViewError");
static PCWSTR ErrorImportFromTemp = __X("ImportFromTempError");
static PCWSTR ErrorHasSheetHiddenProp = __X("HasSheetHiddenPropError");
static PCWSTR ErrorHasBookHiddenProp = __X("HasBookHiddenPropError");
static PCWSTR ErrorShareView = __X("ShareViewError");

HRESULT createAirApp(wo::KEtWorkbook* pWorkbook, IDBSheetView* pView, IN const ks_wstring& setAppName, IN IDX specAfterSheetIdx, OUT ks_wstring& appName, OUT UINT& airAppSheetStId, OUT EtDbId& airAppId)
{
	ks_stdptr<etoldapi::Worksheets> spSheets;
	_Workbook* pWb = pWorkbook->GetCoreWorkbook();
	pWb->get_Worksheets(&spSheets);
	if (!spSheets)
		return E_FAIL;
	KComVariant vAfter;
	INT sheetCnt = 0;
	IBook* pBook = pWb->GetBook();
	pBook->GetSheetCount(&sheetCnt);
	ks_stdptr<ISheet> spLastSheet;
	pBook->GetSheet(sheetCnt - 1 , &spLastSheet);
	const WCHAR* pName = NULL;
	spLastSheet->GetName(&pName);
	if (specAfterSheetIdx != INVALIDIDX)
	{
		vAfter = specAfterSheetIdx + 1;
	}
	else if (0 == xstrcmp(pName, STR_CELL_IMAGE_SHEET_NAME))
		vAfter = sheetCnt - 1;
	else
		vAfter = sheetCnt;
	ks_stdptr<IKCoreObject> spObj;
	HRESULT hr = spSheets->Add(KComVariant(), vAfter, KComVariant(1), KComVariant(xlWorksheet), &spObj, stApp);
	if (FAILED(hr))
		return hr;

	ks_stdptr<_Worksheet> spWorksheet = spObj;
	ISheet* pSheet = spWorksheet->GetSheet();
	ks_bstr sheetName;
	hr = wo::GetValidSheetName(spSheets, spWorksheet, setAppName.c_str(), &sheetName);
	if (FAILED(hr))
		return hr;
	hr = spWorksheet->put_Name(sheetName);
	if (FAILED(hr))
		return hr;
	appName = sheetName;
	ks_stdptr<IAppSheetData> spAppSheetData;
	pSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spAppSheetData);
	ET_DBSheet_ViewType viewType = pView->GetType();
	AirAppCreateParam param;
	param.pView = pView;
	param.appType = _appcore_GainAirAppTypeConverter()->CombineAirAppType(KSheet_AirAppCat_DbSheetView, viewType);
	ks_stdptr<IAirApp> spAirApp;
	hr = spAppSheetData->CreateApp(param, &spAirApp);
	if (FAILED(hr))
		return hr;
	airAppSheetStId = pSheet->GetStId();
	airAppId = spAirApp->GetId();
	return S_OK;
}

HRESULT resetAirApp(wo::KEtWorkbook* pWorkbook, IDBSheetView* pDbSheetView, UINT airAppSheetStId, EtDbId airAppId, PCWSTR sharedId, OUT ks_wstring& airAppName)
{
	ks_stdptr<etoldapi::Worksheets> spSheets;
	IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
	IDX sheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(airAppSheetStId, &sheetIdx);
	if (sheetIdx == INVALIDIDX)
		return E_FAIL;
	ks_stdptr<ISheet> spSheet;
	pBook->GetSheet(sheetIdx, &spSheet);
	if (!spSheet)
		return E_FAIL;

	ks_stdptr<IAppSheetData> spAppSheetData;
	spSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spAppSheetData);
	IAirApp* pApp = spAppSheetData->GetAppById(airAppId);
	if (!pApp)
		return E_FAIL;
	if (pApp->GetCategory() == KSheet_AirAppCat_DbSheetView)
	{
		ks_stdptr<IAirApp_DBView> spApp_DBView = pApp;
		IDBSheetView* pView = spApp_DBView->GetDBSheetView();
		if (pView)
		{
			ks_stdptr<IDBSheetViews> spDbSheetViews;
			pView->GetSheetOp()->GetDbSheetViews(&spDbSheetViews);
			spDbSheetViews->DelItem(pView->GetId());
		}
		spApp_DBView->SetDBSheetView(pDbSheetView);
		wo::IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
		UpdateAppVersionHelper::ExecShareView(pCtx, pDbSheetView->GetSheetOp()->GetSheetId(), pDbSheetView->GetId(), sharedId, airAppSheetStId, airAppId, DBDFT_Default);
	}
	return S_OK;
}

static HRESULT shareView(binary_wo::VarObj &param, IDBSheetView *pDbSheetView, UINT airAppSheetStId, EtDbId airAppId)
{
	HRESULT hr = S_OK;
	if(param.has("sharedInfo"))
	{
		binary_wo::VarObj sharedInfo = param.get_s("sharedInfo");
		PCWSTR sharedId = sharedInfo.field_str("sharedId");
		KDbDisplayFormType displayFormTp = DBDFT_Default;
		if(sharedInfo.has("displayFormTp"))
		{
			_appcore_GainEncodeDecoder()->DecodeDbDisplayForm(sharedInfo.field_str("displayFormTp"), &displayFormTp);
		}
		
		wo::IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
		hr = UpdateAppVersionHelper::ExecShareView(pCtx, pDbSheetView->GetSheetOp()->GetSheetId(), pDbSheetView->GetId(), sharedId, airAppSheetStId, airAppId, displayFormTp);
	}
	return hr;
}

static HRESULT createDbViewForApp(wo::KEtWorkbook* pWorkbook, ISheet* pSheet, IDBProtectionJudgement* pProtectionJudgement, binary_wo::VarObj &param, 
	wo::KEtRevisionContext* pCtx, OUT EtDbId& appViewId, OUT ks_wstring& appName, OUT UINT& airAppSheetStId, OUT EtDbId& airAppId, OUT ks_wstring& errName)
{
	if(!param.has("viewType") || !param.has("viewName"))
	{
		WOLOG_ERROR << "[createDbViewForApp] param viewType or viewName not exist!";
		errName = ErrorParam;
		return E_FAIL;
	}

	ks_stdptr<IDBSheetViews> spDbSheetViews;
	pSheet->GetExtDataItem(edSheetDbViews, (IUnknown**)&spDbSheetViews);
	if(!spDbSheetViews)
	{
		WOLOG_ERROR << "[createDbViewForApp] spDbSheetViews not exist!";
		return E_FAIL;
	}
	
	WebStr viewName = param.field_str("viewName");
	HRESULT hr = spDbSheetViews->IsValidViewName(viewName);
	if (hr == E_DBSHEET_VIEW_INVALID_NAME)
	{
		WOLOG_ERROR << "[createDbViewForApp] view invalid name!";
		errName = ErrorViewInvalidName;
		return hr;
	}

	ks_wstring viewNameStr(viewName);
	if (hr == E_DBSHEET_VIEW_NAME_CONFLICT)
	{
		int postfix = 2;
		do
		{
			QString newName = QString::fromUtf16(viewName) + QString::number(postfix, 10);
			postfix++;
			viewNameStr = krt::utf16(newName);
		} while (E_DBSHEET_VIEW_NAME_CONFLICT == spDbSheetViews->IsValidViewName(viewNameStr.c_str()));
		viewName = viewNameStr.c_str();
	}

	IEncodeDecoder* pEncodeDecoder =  _appcore_GainEncodeDecoder();
	ET_DBSheet_ViewType type = wo::DbSheet::ConvertViewType(pEncodeDecoder, param.field_str("viewType"));

	bool isAddField = false;
	ks_stdptr<IDBSheetRange> spRange;
	if (type == et_DBSheetView_Kanban && param.has("viewData"))
		wo::DbSheet::AddNecessarySelectField(spDbSheetViews.get(), &spRange, isAddField, param.get_s("viewData"), true);
	
	wo::DbSheet::DisableDbSheetProtectScope disPtScope(pProtectionJudgement, pSheet->GetStId());

	bool bAirApp = false;
	if (param.has("isAirApp"))
		bAirApp = param.field_bool("isAirApp");
	ks_stdptr<IDBSheetView> spDbSheetView;
	hr = spDbSheetViews->CreateView(type, viewName, bAirApp, Et_DBSheetViewUse_ForApp, &spDbSheetView);
	//Todo：原本的TaskExecDbCreateView::operator()方法后面还有一些其他的操作,由于参数太多了，不确定是否是必要的，后续需要再加上。
	if (FAILED(hr))
	{
		WOLOG_ERROR << "[createDbViewForApp] CreateView failed!";
		errName = ErrorCreateView;
		return hr;
	}
	if (param.has("viewData"))
	{
		binary_wo::VarObj viewData = param.get_s("viewData");
		hr = wo::DbSheet::SetViewProp(spDbSheetViews, spDbSheetView, viewData, pCtx);
		if (FAILED(hr))
			return hr;
	}

	ks_wstring airAppName;
	if (bAirApp)
	{
		if (param.has("airAppData"))
		{
			binary_wo::VarObj airAppData = param.get_s("airAppData");
			airAppSheetStId = airAppData.field_uint32("airAppSheetStId");
			airAppId = wo::DbSheet::GetEtDbId(airAppData, "airAppId");
			PCWSTR sharedId = airAppData.field_str("sharedId");
			resetAirApp(pWorkbook, spDbSheetView, airAppSheetStId, airAppId, sharedId, airAppName);

		}
		else
		{
			ks_wstring setAppName;
			if(param.has("airAppName"))
			{
				setAppName = param.field_str("airAppName");
			}
			else 
			{
				setAppName = spDbSheetView->GetName();
			}
			
			createAirApp(pWorkbook, spDbSheetView, setAppName, INVALIDIDX, airAppName, airAppSheetStId, airAppId);
		}
	}
	if (spDbSheetView->GetType() == et_DBSheetView_Query)
	{
		ks_stdptr<IDBSheetView_Query> spView_Query = spDbSheetView;
		IDbFieldsManager* pFieldMgr = spDbSheetView->GetFieldsManager();
		const IDBIds* pFields = spDbSheetView->GetVisibleFields();
		for (int i = 0; i < pFields->Count(); ++i)
		{
			EtDbId id = pFields->IdAt(i);
			ks_stdptr<IDbField> spField;
			pFieldMgr->GetField(id, &spField);
			ET_DbSheet_FieldType type = spField->GetType();
			if (type == Et_DbSheetField_Phone)
			{
				KDbFilterCriteriaOpType tp = DBFCOT_Equals;
				PCWSTR customPrompt = __X("");
				BOOL bEnableScanCodeToInputs = FALSE;
				BOOL bConditionCanBlank = FALSE;
				BOOL bNeedSecondCheck = TRUE;
				spView_Query->SetQueryFields(&id, &tp,
					&customPrompt, &bEnableScanCodeToInputs,
					&bConditionCanBlank, &bNeedSecondCheck,1);
				break;
			}
		}
	}

	hr = shareView(param, spDbSheetView, airAppSheetStId, airAppId);
	if(FAILED(hr))
	{
		WOLOG_ERROR << "[createDbViewForApp] ExecShareView failed!";
		errName = ErrorShareView;
		return hr;
	}
	
	appViewId = spDbSheetView->GetId();
	if (airAppName.empty())
		appName = spDbSheetView->GetName();
	else
		appName = airAppName;
	return S_OK;
}

HRESULT BuildEtDbRelation(ISheet* pEtSheet, UINT relatedDbSheetStId, ROW titleBeginIdx, ROW titleEndIdx, COL colBegin, COL colEnd)
{
	//把src_et_sheet和dest_db_sheet给建立关联
	ks_stdptr<IUnknown> spUnk;
	pEtSheet->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
	ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
	if(!spSheetAppEtDbRelations)
	{
		WOLOG_ERROR << "[BuildEtDbRelation] spSheetAppEtDbRelations not exist!";
		return E_FAIL;
	}

	ks_stdptr<IAppEtDbRelationItem> spAppEtDbRelationItem;
	_appcore_CreateObject(CLSID_KAppEtDbRelationItem, IID_IAppEtDbRelationItem, (void **)&spAppEtDbRelationItem);
	if(!spAppEtDbRelationItem)
	{
		WOLOG_ERROR << "[BuildEtDbRelation] spAppEtDbRelationItem not exist!";
		return E_FAIL;
	}
	spAppEtDbRelationItem->Init(pEtSheet, relatedDbSheetStId, titleBeginIdx, titleEndIdx, colBegin, colEnd);
	spAppEtDbRelationItem->InitDirty(false, false);
	spSheetAppEtDbRelations->PushAppEtDbRelationItem(relatedDbSheetStId, spAppEtDbRelationItem);

	return S_OK;
}

static HRESULT createAppViewFromTemplate(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, binary_wo::VarObj &param,
	OUT UINT& appSheetStId, OUT EtDbId& appViewId, OUT ks_wstring& appName,
	OUT UINT& airAppSheetStId, OUT EtDbId& airAppId, OUT ks_wstring& errName)
{
	// 当前文件有不可见区域禁止导入
	if (ctx->getProtectionCtx()->isBookHasHiddenProperty())
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] book has hidden prop, not allow do this!";
		errName = ErrorHasBookHiddenProp;
		return E_FAIL;;
	}

	if (!param.has("mimeDataID"))
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] param template mimeDataID not exist!";
		errName = ErrorParam;
		return E_FAIL;
	}
	if (!param.has("extraData"))
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] param extraData not exist!";
		errName = ErrorParam;
		return E_FAIL;
	}
	VarObj extraData = param.get("extraData");

	//走模板导入
	wo::WebMimeHelper wmh;
	wmh.objParam = param, wmh.strField = "mimeDataID";
	QString path = wmh.resolvePath();
	PCWSTR fileName = krt::utf16(path);

	bool copyContent = true;//默认值
	if(param.has("isCopyTemplateContent"))
		copyContent = param.field_bool("isCopyTemplateContent");
	bool delAllSheet = false;//默认值

	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
	ks_stdptr<IUnknown> spUnknown;
	VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	if(!spUnknown)
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] edDBUserGroups ExtDataItem not exist!";
		return E_FAIL;
	}
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if(!spProtectionJudgement)
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] spProtectionJudgement empty!";
		return E_FAIL;
	}

	UINT activeSheetId = 0;
	wo::AddTemplateSheetsHelper addTemplateHelper(pWorkbook, ctx, fileName, copyContent, delAllSheet, spProtectionJudgement.get(), param);
	HRESULT hr = addTemplateHelper.Exec(activeSheetId);
	if (FAILED(hr))
	{
		errName = ErrorImportFromTemp;
		return hr;
	}
	
	appSheetStId = addTemplateHelper.GetLastDbSheetStId();
	if(appSheetStId == 0)
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] temp error because not dbSheet!";
		return E_FAIL;
	}

	//模板已经自带view 这时候直接把对应sheet的最后一个viewId返回即可
	IDX appSheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(appSheetStId, &appSheetIdx);

	ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
	long sheetCount = 0;
	spWorksheets->get_Count(&sheetCount);
	if(appSheetIdx < 0 || appSheetIdx >= sheetCount)
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] appSheetIdx < 0 || appSheetIdx >= sheetCount error";
		return E_FAIL;
	}

	ISheet* pNewSheet = spWorksheets->GetSheetItem(appSheetIdx)->GetSheet();
	if (!pNewSheet)
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] pNewSheet empty!";
		return E_FAIL;
	}

	ASSERT(pNewSheet->IsDbSheet());

	ks_stdptr<IDBSheetViews> spDbNewSheetViews;
	pNewSheet->GetExtDataItem(edSheetDbViews, (IUnknown**)&spDbNewSheetViews);
	if(!spDbNewSheetViews)
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] spDbNewSheetViews empty!";
		return E_FAIL;
	}
		

	int viewCnt = spDbNewSheetViews->GetSize(Et_DBSheetViewUse_ForApp);
	if(viewCnt <= 0)
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] viewCnt <=0 !";
		return E_FAIL;
	}

	ks_stdptr<IDBSheetView> spDbSheetView;
	spDbNewSheetViews->GetItemAt(viewCnt - 1, Et_DBSheetViewUse_ForApp, &spDbSheetView);
	if(!spDbSheetView)
	{
		WOLOG_ERROR << "[createAppViewFromTemplate] spDbSheetView empty!";
		return E_FAIL;
	}
	bool bAirApp = false;
	if (extraData.has("isAirApp"))
	{
		bAirApp = extraData.field_bool("isAirApp");
	}
	bool bHasCopyAirAppSheet = addTemplateHelper.HasCopyAirAppSheet();
	ks_wstring airAppName;
	if (bAirApp)
	{
		//为了兼容新旧模板，模板文件可能有应用sheet，也可能没有应用sheet，
		//若无应用sheet，则说明为旧模板，才需给其创建一个appsheet；若有应用sheet，则无需再手动创建一个appsheet
		if (bHasCopyAirAppSheet)
		{
			AirAppInfo info = addTemplateHelper.GetActiveAppInfo();
			airAppSheetStId = info.sheetStId;
			airAppId = info.appId;
		}
		else
		{
			ks_wstring setAppName;
			if(param.has("airAppName"))
				setAppName = param.field_str("airAppName");
			else 
				setAppName = spDbSheetView->GetName();

			createAirApp(pWorkbook, spDbSheetView, setAppName, INVALIDIDX, airAppName, airAppSheetStId, airAppId);
		}
	}

	hr = shareView(extraData, spDbSheetView, airAppSheetStId, airAppId);
	if(FAILED(hr))
	{
		WOLOG_ERROR << "[createDbViewForApp] ExecShareView failed!";
		errName = ErrorShareView;
		return hr;
	}

	appViewId = spDbSheetView->GetId();
	if (airAppName.empty())
		appName = spDbSheetView->GetName();
	else
		appName = airAppName;
	return S_OK;
}

static HRESULT createAppViewFromLocal(wo::KEtWorkbook* pEtWorkbook, wo::KEtRevisionContext* ctx, binary_wo::VarObj& param,
	OUT UINT& appSheetStId, OUT UINT& srcSheetStId, OUT EtDbId& appViewId, OUT ks_wstring& appName,
	OUT UINT& airAppSheetStId, OUT EtDbId& airAppId, OUT ks_wstring& errName)
{
	IDX appSheetIdx = INVALIDIDX;//应用所在的sheetIdx,作为出参
	WOLOG_INFO << "[createAppViewFromLocal] from local sheet";
	if(!param.has("sheetStId"))
	{
		WOLOG_ERROR << "[createAppViewFromLocal] param sheetStId not exist!";
		errName = ErrorParam;
		return E_FAIL;
	}

	if (!param.has("extraData"))
	{
		WOLOG_ERROR << "[createAppViewFromLocal] param extraData not exist!";
		errName = ErrorParam;
		return E_FAIL;
	}
	VarObj extraData = param.get("extraData");

	UINT sheetStId = param.field_uint32("sheetStId");
	_Workbook* pWorkbook = pEtWorkbook->GetCoreWorkbook();
	IBook* pBook = pWorkbook->GetBook();

	ks_stdptr<IUnknown> spUnknown;
	VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
	if(!spUnknown)
	{
		WOLOG_ERROR << "[createAppViewFromLocal] edDBUserGroups ExtDataItem not exist!";
		return E_FAIL;
	}
	ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
	ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
	if (spDBUserGroups)
		spDBUserGroups->GetJudgement(&spProtectionJudgement);
	if(!spProtectionJudgement)
	{
		WOLOG_ERROR << "[createAppViewFromLocal] spProtectionJudgement empty!";
		return E_FAIL;
	}

	IDX sheetIdx = INVALIDIDX;
	pBook->STSheetToRTSheet(sheetStId, &sheetIdx);

	long sheetCount = 0;
	ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetWorksheets();
	spWorksheets->get_Count(&sheetCount);
	if(sheetIdx < 0 || sheetIdx >= sheetCount)
	{
		WOLOG_ERROR << "[createAppViewFromLocal] sheetIdx < 0 || sheetIdx >= sheetCount error";
		errName = ErrorParam;
		return E_FAIL;
	}

	ISheet* pSheet = spWorksheets->GetSheetItem(sheetIdx)->GetSheet();
	if (!pSheet)
	{
		WOLOG_ERROR << "[createAppViewFromLocal] param sheetIdx inValid!";
		errName = ErrorParam;
		return E_FAIL;
	}
	HRESULT hr = S_OK;
	//判断当前sheet是否为dbSheet还是et
	if(pSheet->IsDbSheet())
	{
		appSheetIdx = sheetIdx;
		srcSheetStId = 0;//若是从本地et创建应用，返回et_sheet stid，除此之外从本地et创建和从模板创建，均则返回0 表示无效的值
	}
	else
	{
		if(param.has("isNeedCheckSheetHiddenProp"))
		{
			//表示是否需要对当前et sheet检查含有禁止查看的区域权限，若为false，说明当前是canEditShare，无需进行此检查。
			bool bNeedCheckSheetHiddenProp = param.field_bool("isNeedCheckSheetHiddenProp");
			if(bNeedCheckSheetHiddenProp)
			{
				//判断et数据源是否含有禁止查看。
				bool hasHiddenProp = ctx->getProtectionCtx()->isSheetHasHiddenProperty(pSheet);
				if(hasHiddenProp)
				{
					WOLOG_ERROR << "[createAppViewFromLocal] src etSheet has hidden property, not allow createApp!";
					errName = ErrorHasSheetHiddenProp;
					return E_FAIL;
				}
			}
		}
		ks_stdptr<_Worksheet> spAppWorksheet;
		ROW titleBeginIdx = INVALID_ROW;
		ROW titleEndIdx = INVALID_ROW;
		COL colBegin = INVALID_COL;
		COL colEnd = INVALID_COL;
		bool addDbSheetWithInfo = false;
		if (param.has("addDbSheetWithInfo"))
			addDbSheetWithInfo = param.field_bool("addDbSheetWithInfo");
		if (addDbSheetWithInfo)
		{
			if (!extraData.has("dbInfo"))
			{
				errName = ErrorParam;
				return E_FAIL;
			}
			VarObj dbInfo = extraData.get_s("dbInfo");
			if (!dbInfo.has("rowFrom") || !dbInfo.has("colFrom") || !dbInfo.has("fieldNameVec") || !dbInfo.has("fieldTypeVec"))
			{
				errName = ErrorParam;
				return E_FAIL;
			}
			VarObj fieldNameVec = dbInfo.get("fieldNameVec");
			VarObj fieldTypeVec = dbInfo.get("fieldTypeVec");
			int fldCnt= fieldNameVec.arrayLength_s();
			if (fldCnt == 0 || fldCnt != fieldTypeVec.arrayLength_s())
			{
				WOLOG_ERROR << "[createAppViewFromLocal] param fieldNameVec or fieldTypeVec is wrong!";
				errName = ErrorParam;
				return E_INVALIDARG;
			}
			std::vector<PCWSTR> fldNameVec;
			fldNameVec.reserve(fldCnt);
			std::vector<ET_DbSheet_FieldType> fldTypeVec;
			fldTypeVec.reserve(fldCnt);
			IEncodeDecoder* pEncodeDecoder =  _appcore_GainEncodeDecoder();
			for (int i = 0; i < fldCnt; ++i)
			{
				fldNameVec.emplace_back(fieldNameVec.item_str(i));
				ET_DbSheet_FieldType type = ET_DbSheet_FieldType_Invalid;
				hr = pEncodeDecoder->DecodeFieldType(fieldTypeVec.item_str(i), &type);
				if (FAILED(hr))
					return hr;
				fldTypeVec.emplace_back(type);
			}
			wo::CreateDbSheetFromEtWithSpecifyInfo::Param importerParam;
			importerParam.etSheetIdx = sheetIdx;
			importerParam.rowFrom = dbInfo.field_int32("rowFrom");
			importerParam.colFrom = dbInfo.field_int32("colFrom");
			importerParam.pFieldNameVec = &fldNameVec;
			importerParam.pFieldTypeVec = &fldTypeVec;
			wo::CreateDbSheetFromEtWithSpecifyInfo importer(pWorkbook, pWorkbook, ctx, spProtectionJudgement);
			hr = importer.Init(importerParam);
			if (FAILED(hr))
				return hr;
			hr = importer.Exec();
			if (FAILED(hr))
			{
				WOLOG_ERROR << "[createAppViewFromLocal] WithInfo Exec() Fail!!";
				errName = importer.GetErrName();
				return hr;
			}
			titleBeginIdx = 0;
			titleEndIdx = importerParam.rowFrom - 1;
			colBegin = importerParam.colFrom;
			colEnd = importerParam.colFrom + fldCnt - 1;
			spAppWorksheet = importer.GetNewWorksheet();
		}
		else
		{
			//将et sheet导出成dbt
			if (!extraData.has("xlsxImport") || !extraData.has("defaultName"))
			{
				WOLOG_ERROR << "[createAppViewFromLocal] param xlsxImport or defaultName not exist!";
				errName = ErrorParam;
				return E_FAIL;
			}
			VarObj xlsxImport = extraData.get("xlsxImport");
			VarObj defaultName = extraData.get("defaultName");

			//对et sheet转db sheet
			wo::Et2DbImporterSameProcess importer(pWorkbook, pWorkbook,
				sheetIdx, INVALIDIDX,
				ctx, false, defaultName, xlsxImport,
				spProtectionJudgement, false, true);
			importer.setRequestIsFromApp();
			hr = importer.Exec();
			if (FAILED(hr))
			{
				WOLOG_ERROR << "[createAppViewFromLocal] Exec() Fail!!";
				errName = importer.GetErrName();
				return hr;
			}
			// 产品规定:et转db的导入数据范围的第一行才算是表头区域，下面的部分属于内容区域
			titleBeginIdx = 0;
			titleEndIdx = importer.GetImporterRangeRowFrom();
			colBegin = 0;
			colEnd = pSheet->GetBMP()->cntCols - 1;
			spAppWorksheet = importer.GetDbWorksheet();
		}
		ISheet* pRelatedDbSheet = spAppWorksheet->GetSheet();
		pRelatedDbSheet->GetIndex(&appSheetIdx);
		if(extraData.has("appSheetName"))
		{
			//指定创建出来的db_sheet名字
			WebStr newDbSheetName = extraData.field_str("appSheetName");//由于历史原因，这个appSheetName含义已经变化了，是指dbsheet的name

			ks_bstr bstrSheetName;
			hr = wo::GetValidSheetName(spWorksheets, spAppWorksheet, newDbSheetName, &bstrSheetName);
			if (SUCCEEDED(hr))
			{
				//检查成功才去做这个事情吧，不然直接返回失败也不好。
				hr = spAppWorksheet->put_Name(bstrSheetName); // 重名时虽然会自动增加序号, 但并不符合dbsheet的预期。去重后，用 VS 预期不重名
				if(FAILED(hr))
				{
					WOLOG_ERROR << "[createAppViewFromLocal] put appSheetName error";
					return E_FAIL;
				}
			}
		}

		srcSheetStId = sheetStId;//有效值
		bool isRelateEtDb = false;
		if(param.has("isRelateEtDb"))
			isRelateEtDb = param.field_bool("isRelateEtDb");

		if(isRelateEtDb)
		{
			if (!IsRowValid(titleEndIdx, pSheet->GetBMP()))
			{
				WOLOG_ERROR << "[createAppViewFromLocal] titleRowIdx unValid!";
				return E_FAIL;
			}
			hr = BuildEtDbRelation(pSheet, pRelatedDbSheet->GetStId(), titleBeginIdx, titleEndIdx, colBegin, colEnd);
			if (FAILED(hr))
			{
				WOLOG_ERROR << "[createAppViewFromLocal] BuildEtDbRelation Fail!!";
				return E_FAIL;
			}
			//把关联的db_sheet给深度隐藏
			pRelatedDbSheet->SetVisible(ssVeryhidden);
		}
	}
	WOLOG_INFO << "[createAppViewFromLocal] prepare to create view of appSheetIdx:" << appSheetIdx;

	//对appSheetIdx 的dbt创建对应应用类型的view。
	ISheet* pAppSheet = spWorksheets->GetSheetItem(appSheetIdx)->GetSheet();
	if (!pAppSheet)
	{
		WOLOG_ERROR << "[createAppViewFromLocal] pAppSheet empty!";
		return E_FAIL;
	}

	hr = createDbViewForApp(pEtWorkbook, pAppSheet, spProtectionJudgement.get(), extraData, ctx, appViewId, appName, airAppSheetStId, airAppId, errName);
	if(FAILED(hr))
	{
		WOLOG_ERROR << "[createAppViewFromLocal] createDbViewForApp error";
		return hr;
	}
	//返回结果
	appSheetStId = pAppSheet->GetStId();
	return S_OK;
}

HRESULT CreateAppView(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, binary_wo::VarObj &param, 
	OUT UINT& appSheetStId, OUT UINT& srcSheetStId, OUT EtDbId& appViewId, OUT ks_wstring& appName,
	OUT UINT& airAppSheetStId, OUT EtDbId& airAppId, OUT ks_wstring& errName)
{
	WOLOG_INFO << "[CreateAppView] begin!";
	if (!param.has("isFromTemplate"))
	{
		WOLOG_ERROR << "[CreateAppView] param isFromTemplate not exist!";
		errName = ErrorParam;
		return E_FAIL;
	}

	HRESULT hr = E_FAIL;
	bool bFromTemplate = param.field_bool("isFromTemplate");
	if(!bFromTemplate)
		hr = createAppViewFromLocal(pWorkbook, ctx, param, appSheetStId, srcSheetStId, appViewId, appName, airAppSheetStId, airAppId, errName);
	else
		hr = createAppViewFromTemplate(pWorkbook, ctx, param, appSheetStId, appViewId, appName, airAppSheetStId, airAppId, errName);

	return hr;
}

}//end namespace CreateAppViewHelper
