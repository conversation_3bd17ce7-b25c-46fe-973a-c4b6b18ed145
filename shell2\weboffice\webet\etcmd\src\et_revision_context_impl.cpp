﻿#include "etstdafx.h"
#include "et_revision_context_impl.h"
#include "appcore/et_appcore_basic_itf.h"
#include "etcore/little_alg.h"
#include "wo/core_stake.h"
#include "wo/workbook_obj.h"
#include "workbook.h"
#include "block_point.h"
#include "ctime"
#include <public_header/etcore/mvc/et_worksheet_layer.h>
#include <public_header/opl/mvc/et_shape_tree.h>
#include "webetlink.h"
#include "etcore/et_core_basic.h"
#include <public_header/drawing/api/dghost_i.h>
#include <public_header/chart/src/model/kctshape.h>
#include "et_hard_define_strings.h"
#include "webbase/logger.h" 
#include "pivot_core/pivot_core_tools.h"
#include "pivot_core/pivot_core_x.h"
#include "helpers/pivot_tables_helper.h"
#include "helpers/shape_helper.h"
#include "webbase/logger.h" 
#include "protection_context_impl.h"
#include "dbsheet/db_protection_context_impl.h"
#include "appcore/et_appcore_dbsheet.h"
#include "hresult_to_string.h"
#include "utils/attachment_utils.h"
#include "util.h"
#include "ksheet/ksheet_protection_context_impl.h"
#include "etcore/wo_et_cell_history.h"
#include "etconfig.h"
#include "webbase/binvariant/binreader.h"
#include "fileInfoCollect.h"
#include "database/database_def.h"
#include "database/database.h"
#include "database/database_field_context.h"

extern Callback* gs_callback;
extern WebProcType gs_procType;

namespace wo
{
using namespace binary_wo;
// 编译不通过的也认为是公式，非法公式
bool isFormulaStr(IBook* pBook, PCWSTR str)
{
	while(*str == __Xc(' ') || *str == __Xc('　'))
	{
		str++;
	}

	if (str[0] == __Xc('=') || str[0] == __Xc('＝'))
		return true;

	if (str[0] == __Xc('+') ||  str[0] == __Xc('-') || str[0] == __Xc('＋') ||  str[0] == __Xc('－'))
	{
		ks_stdptr<IBookOp> spBookOp;
		pBook->GetOperator(&spBookOp);
		ks_stdptr<IFormula> spFmla;
		spBookOp->CreateFormula(&spFmla);
		CS_COMPILE_PARAM	ccp(cpfSysDefault, 0, 0, 0);

		COMPILE_RESULT cr;
		spFmla->SetFormula(str, ccp, &cr);
		if(cr.nErrCode != COMPILE_SUCCESS)
			return true;

		BOOL bFmla = FALSE;
		spFmla->GetContent(&bFmla, NULL, NULL);
		return bFmla;
	}
	return false;
}

//---------------------------------------------------------------------------
KEtRevisionContext::KEtRevisionContext(KEtWorkbook* pWoWb)
	: m_wb(pWoWb->GetCoreWorkbook()), m_app(pWoWb->GetCoreApp())
	, m_bk(pWoWb->GetCoreWorkbook()->GetBook())
	, m_user(pWoWb->GetCoreApp()->getUserConns()->getCurrentUserConn())
	, m_bks(pWoWb->GetCoreWorkbook()->GetBook()->GetWoStake())
	, m_wbo(pWoWb->GetCoreWorkbook()->GetWoObject())
	, m_activeGridCells(0)
	, m_activeSheetIndex(0)	
	, m_idxWnd(0)
	, m_pWoWb(pWoWb)
	, m_nSourceCount(-1)
	, m_nResultCount(-1)
	, m_bIgnoreHistory(false)
	, m_bFmlaChange(false)
	, m_state(CtxState::Unknown)
	, m_flashFillRect(Rect_CreateScaleNone())
	, m_bSyncSupBookCache(false)
	, m_filterCtx(m_bk, this)
	, m_mostRecentMbId(WOMB_NONE)
	, m_isPivotTableLayoutOnCellContent(false)
	, m_bAllMutableResult(false)
	, m_upWriter(new binary_wo::BinWriter(false))
	, m_hr(S_OK)
	, m_cmdName(__X(""))
	, m_perfStatContext(pWoWb)
	, m_bSearchCellImg(false)
	, m_cfHandleStrategy(cfHandleStrategyDef)
	, m_beginClock(std::chrono::steady_clock::now())
	, m_elapsed(0)
	, m_getElapsedCnt(0)
	, m_redirectSharedId(NULL)
	, m_bNeedCleanChildProc(S_FALSE)
	, m_serializeChangeContent(false)
	, m_isReSerialize(false)
	, m_IsNoCheckPerms(false)
{
	if (m_bk->GetBMP()->bKsheet)
		m_spProtectionCtx.reset(new KsheetProtectionContext(pWoWb, this));
	else if (m_bk->GetBMP()->bDbSheet)
		m_spProtectionCtx.reset(new KDbProtectionContext(pWoWb));
	else
		m_spProtectionCtx.reset(new KProtectionContext(pWoWb->GetCoreWorkbook(), pWoWb->GetCoreWorkbook()->GetWoObject(), this));

	_etcore_CreateObject(
		CLSID_KETStringTools, IID_IETStringTools, (void**)&m_tools);

	ASSERT(_etcore_GetEtRevisionContext() == NULL);
	_kso_SetRevisionContext(this);
	clearEtContext();
	
	//随便塞入一个sheet来初始化，等前端完善了，m_tools是要去掉的
	ks_stdptr<ISheet> sht;
	VS(m_bk->GetSheet(0, &sht));
	m_tools->SetEnv(sht.get());

	for (INT32 i = 0; i < m_wbo->getSheetCount(); ++i)
	{
		AbsObject* tmp = m_wbo->getSheetItem(i);
		m_idWeb2Core[tmp->objId()] = tmp;

		ks_stdptr<ISheet> spSheet;
		m_bk->GetSheet(i, &spSheet);
		if (spSheet)
		{
			const AbsObject* pBlockGridData = spSheet->GetWoStake()->getObjCells();
			m_gridData2SheetId[pBlockGridData] = tmp->objId();
			const AbsObject* pRows = spSheet->GetWoStake()->getObjRows();
			m_rows2SheetId[pRows] = tmp->objId();
			const AbsObject* pCols = spSheet->GetWoStake()->getObjCols();
			m_cols2SheetId[pCols] = tmp->objId();
			const AbsObject* pDvs = spSheet->GetWoStake()->getObjDataValidation();
			m_dvs2SheetId[pDvs] = tmp->objId();
			
			setupProtectionCache(spSheet);
			collectCellHistoryInfo(spSheet);
		}
	}

	m_spProtectionCtx->setHasProtectionCache(true);
	setupEtContext();

	ks_stdptr<IKEtWindow> etWindow = m_app->GetActiveWindow();
	m_idxWnd = etWindow->GetIndex();

	INT32 idxActive = 0;
	BOOKWNDINFO* wndInfo = nullptr;
	IBookWndInfos* bkWnd = m_wb->GetWndInfos();
	VS(bkWnd->GetItem(m_idxWnd, &wndInfo));
	if (wndInfo != nullptr) m_activeSheetIndex = wndInfo->nActiveSheet;

	INT32 idx = m_activeSheetIndex;
	if (0 <= idx && idx < m_wbo->getSheetCount()) {
		ks_stdptr<ISheet> sht;
		VS(m_bk->GetSheet(idx, &sht));
		m_activeGridCells = sht->GetWoStake()->getObjCells()->objId();
	}

	m_curCommitVersionTime = QDateTime::currentSecsSinceEpoch();
	ASSERT(m_pWoWb->getVersionMgr());
	if (m_pWoWb->getVersionMgr())
		m_initVersionCount = m_pWoWb->getVersionMgr()->getTaskVersionCount();
	else
		m_initVersionCount = 0;
}

KEtRevisionContext::~KEtRevisionContext()
{
	m_spProtectionCtx->clearProtectionCache();
	clearEtContext();
    _kso_SetRevisionContext(NULL);
}

UINT KEtRevisionContext::elapsed(UINT mark)
{
	++m_getElapsedCnt;
	if((m_getElapsedCnt & mark) == 0)
	{
		m_elapsed = (std::chrono::steady_clock::now() - m_beginClock) / std::chrono::milliseconds(1);
	}
	return m_elapsed;
}

void KEtRevisionContext::getSerializingRects(WebID objGridCells, const RECT** ppRects, int* pCount)
{
	if (m_activeGridCells == objGridCells)
	{
		validateInitRect();

		*ppRects = m_vecInitRects.size() > 0 ? &m_vecInitRects[0] : NULL;
		*pCount = m_vecInitRects.size();
	}
	else
	{
		*ppRects = NULL;
		*pCount = 0;
	}
}

IBook* KEtRevisionContext::getBook()
{
	return m_bk;
}

IETStringTools* KEtRevisionContext::getStringTools()
{
	return m_tools.get();
}

void KEtRevisionContext::addFmla(const CellNode* cn)
{
	m_nodesFmla.insert(cn);
}

template <typename OpTy_>
inline void expandByPos(
	INT32 x, INT32 blk, INT32 pos, OpTy_& le, OpTy_& be)
{
	--x;
	INT32 z = 0, off = blk / 4;
	if (std::max(z, pos-off) < le) le -= blk;
	if (be < std::min(x, pos+off)) be += blk;
	ASSERT(IsSeria(0, le, be, x) && le < be);
}

bool KEtRevisionContext::IsUserHasSerialObjs()
{
	IWoETSettings* pWoEtSettings = _kso_GetWoEtSettings();
	if (!pWoEtSettings->EnableReleaseConn())
		return true;
	ks_castptr<IKETUserConn> etUser = m_user;
	PCWSTR connId = etUser->connID();
	if (!wo::util::IsEnableRelease(connId))
		return true;
	return !etUser->getSerialObjsSet()->empty();
}

void KEtRevisionContext::validateInitRect()
{
	if (m_vecInitRects.size() > 0)
		return;

	ks_stdptr<IUnknown> spUnk;
	ks_stdptr<ISheet> sht; 
	VS(m_bk->GetSheet(m_activeSheetIndex, &sht));
	if (FAILED(sht->GetExtDataItem(edSheetWndInfos, &spUnk)))
		return;

	ks_castptr<ISheetWndInfos> wndInfos = spUnk;
	const SHEETWNDINFO* wndInfo = nullptr;
	VS(wndInfos->GetItem(m_idxWnd, &wndInfo));
	m_vecInitRects.push_back(getBlockRect(wndInfo->cellLeftTop.row, wndInfo->cellLeftTop.col));

	if (!wndInfo->fFrozen)
		return;

	CELL frozenCellLeftTop = wndInfo->PaneInfo.LTCell;
	if (frozenCellLeftTop.row == 0)
		frozenCellLeftTop.row = wndInfo->cellLeftTop.row;
	if (frozenCellLeftTop.col == 0)
		frozenCellLeftTop.col = wndInfo->cellLeftTop.col;

	m_vecInitRects.push_back(getBlockRect(frozenCellLeftTop.row, frozenCellLeftTop.col));
	auto it = std::unique(m_vecInitRects.begin(), m_vecInitRects.end(), Rect_Equal);
	m_vecInitRects.resize(std::distance(m_vecInitRects.begin(),it) );
}

void KEtRevisionContext::setAllMutableResult(bool b)
{
	m_bAllMutableResult = b;
}

bool KEtRevisionContext::getAllMutableResult() const
{
	return m_bAllMutableResult;
}

RECT KEtRevisionContext::getBlockRect(ROW posRow, COL posCol) 
{
	auto dd = [](INT32 x, INT32 d)->INT32{return x / d * d;};

	RECT rc = Rect_CreateScaleNone();
	rc.top = dd(posRow, BLK_ROWS_COUNT);
	rc.left = dd(posCol, BLK_COLS_COUNT);
	rc.bottom = rc.top + BLK_ROWS_COUNT - 1;
	rc.right = rc.left + BLK_COLS_COUNT - 1;

	INT32 rf = rc.top, rt = rc.bottom;
	expandByPos(m_bk->GetBMP()->cntRows, BLK_ROWS_COUNT, posRow, rf, rt);
	rc.top = rf;
	rc.bottom = rt;

	INT32 cf = rc.left, ct = rc.right;
	expandByPos(m_bk->GetBMP()->cntCols, BLK_COLS_COUNT, posCol, cf, ct);
	rc.left = cf;
	rc.right = ct;
	return rc;
}

void KEtRevisionContext::setInitRect(WebID sheet, const CELL& leftTop, const CELL& frozenCellLeftTop)
{ 
	INT32 idx = getSheetIndex(sheet);
	if (idx < 0 || idx >= m_wbo->getSheetCount())
		return;

	m_activeSheetIndex = idx;
	ks_stdptr<ISheet> sht;
	VS(m_bk->GetSheet(idx, &sht));
	m_activeGridCells = sht->GetWoStake()->getObjCells()->objId();
	m_vecInitRects.clear();

	if (leftTop.row >= 0 && leftTop.col >= 0)
		m_vecInitRects.push_back(getBlockRect(leftTop.row, leftTop.col));

	if (frozenCellLeftTop.row < 0 || frozenCellLeftTop.col < 0)
		return;

	ks_stdptr<IUnknown> spUnk;
	RANGE rgActive(sht->GetBMP());
	if (FAILED(sht->GetExtDataItem(edSheetWndInfos, &spUnk)))
		return;

	ks_castptr<ISheetWndInfos> wndInfos = spUnk;
	const SHEETWNDINFO* wndInfo = nullptr;
	VS(wndInfos->GetItem(m_idxWnd, &wndInfo));
	if (!wndInfo->fFrozen)
		return;

	m_vecInitRects.push_back(getBlockRect(frozenCellLeftTop.row, frozenCellLeftTop.col));
	auto it = std::unique(m_vecInitRects.begin(), m_vecInitRects.end(), Rect_Equal);
	m_vecInitRects.resize(std::distance(m_vecInitRects.begin(),it));
}

void KEtRevisionContext::setInitBlock(WebID sheet, const binary_wo::VarObj& blocks)
{
	INT32 idx = getSheetIndex(sheet);
	if (idx < 0 || idx >= m_wbo->getSheetCount())
		return;

	m_activeSheetIndex = idx;
	ks_stdptr<ISheet> sht;
	VS(m_bk->GetSheet(idx, &sht));
	m_activeGridCells = sht->GetWoStake()->getObjCells()->objId();
	m_vecInitRects.clear();

	for (int32 i = 0, len = blocks.arrayLength_s(); i < len; ++i)	
	{
		binary_wo::VarObj vBlock = blocks.at(i);
		m_vecInitRects.push_back(getBlockRect(vBlock.field_int32("row"), vBlock.field_int32("col")));
	}
	
	auto it = std::unique(m_vecInitRects.begin(), m_vecInitRects.end(), Rect_Equal);
	m_vecInitRects.resize(std::distance(m_vecInitRects.begin(),it));
}

void KEtRevisionContext::setInitBlock(WebID sheet, std::vector<RECT> & blocks)
{
	INT32 idx = getSheetIndex(sheet);
	if (idx < 0 || idx >= m_wbo->getSheetCount())
		return;

	m_activeSheetIndex = idx;
	ks_stdptr<ISheet> sht;
	VS(m_bk->GetSheet(idx, &sht));
	m_activeGridCells = sht->GetWoStake()->getObjCells()->objId();
	m_vecInitRects.clear();

	blocks.swap(m_vecInitRects);
}

IKUserConns* KEtRevisionContext::getUsers()
{
	return m_pWoWb->GetCoreApp()->getUserConns();
}

IKUserConn*	KEtRevisionContext::getUser()
{
	return m_user;
}

// 注意：调用这个函数会改变权限
// predirectSharedId 相当于一把钥匙必需是用户给的，如果内核生成的要注意不要越权
HRESULT KEtRevisionContext::SetRedirectSharedId(PCWSTR redirectSharedId)
{
	m_redirectSharedId = redirectSharedId;
	return S_OK;
}

PCWSTR KEtRevisionContext::GetRedirectSharedId()
{
	return m_redirectSharedId;
}

IKUserConn* KEtRevisionContext::setUser(IKUserConn* p)
{
	IKUserConn* oldUser = m_user;
	m_user = p;
	return oldUser;
}

IKUserConn* KEtRevisionContext::getUserFromInnerID(UINT32 innerID)
{
	IKUserConns* ctn = m_wb->GetApplication()->getUserConns();
	return ctn->getItemFromInnerID(innerID);
}

IKDocument* KEtRevisionContext::getDocument()
{
	return m_wb;
}

void KEtRevisionContext::submitModifyShape(drawing::AbstractShape* shape)
{
	m_pWoWb->submitModifyShape(shape);
}

namespace
{
void getSharedViewInfo(KEtRevisionContext* pCtx, bool& isSharedView, IDX& sharedViewSheetIdx)
{
	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	pCtx->getBook()->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
	if (spSharedLinkMgr)
	{
		PCWSTR sharedId = spSharedLinkMgr->GetConnSharedLink(pCtx->getUser());
		if (sharedId)
		{
			ISharedLink* pLink = spSharedLinkMgr->GetItem(sharedId);
			if (pLink && SUCCEEDED(pLink->CheckPermission()))
			{
				ks_stdptr<IDBSharedLinkView> spSharedLinkView = pLink;
				if (spSharedLinkView)
				{
					isSharedView = true;
					IDBSheetView* pView = spSharedLinkView->GetView();
					pView->GetSheetOp()->GetIndex(&sharedViewSheetIdx);
				}
			}
		}
	}
}
} // namespace (anonymous)

void KEtRevisionContext::collectInitBlockPoints(
	bool bInit, std::vector<BlockPoint>& v)
{
	if (bInit && m_vecInitRects.size())
	{
		ks_stdptr<ISheet> sht; 
		VS(m_bk->GetSheet(m_activeSheetIndex, &sht));
		bool isHidden = getProtectionCtx()->isSheetHidden(sht->GetStId());
		if (isHidden)
			return;

		AbsObject* obj = m_wbo->getSheetItem(m_activeSheetIndex);
		for (size_t i = 0; i < m_vecInitRects.size(); ++i)
		{
			BlockPointEnum bpe(obj->objId(), m_vecInitRects[i]);
			for (; bpe.IsValid(); bpe.Next())
			{
				BlockPoint current = bpe.Current();
				v.push_back(current);
			}
		}
	}
}

void KEtRevisionContext::collectFmla(std::vector<const CellNode*>& cns)
{
	cns.insert(cns.end(), m_nodesFmla.begin(), m_nodesFmla.end());
}

void KEtRevisionContext::collectCachedCalc(std::vector<const CellNode*>& cns)
{
	cns.insert(cns.end(), m_nodesCalcCache.begin(), m_nodesCalcCache.end());
}

IWorksheetObj* KEtRevisionContext::getSheetMain(IDX sheetId)
{
	if (0 <= sheetId && sheetId < m_wbo->getSheetCount())
		return m_wbo->getSheetItem(sheetId);
	else
		return nullptr;
}

IDX KEtRevisionContext::getSheetIndex(WebID id)
{
	auto it = m_idWeb2Core.find(id);
	if (it == m_idWeb2Core.end())
		return INVALIDIDX;
	else
		return m_wbo->getSheetIndex(it->second);
}

void KEtRevisionContext::onSheetDelete(WebID const objId) 
{
	m_idWeb2Core.erase(objId);
}

void KEtRevisionContext::resetCalcNodes(const CellNode** arr, size_t cnt)
{
	m_nodesCalcCache.assign(arr, arr+cnt);
}

IWoLogger* KEtRevisionContext::getLogger()
{
	return m_pWoWb->getLogger();
}

IDX KEtRevisionContext::getSheetIndexByGridData(const AbsObject* obj)
{
	auto it = m_gridData2SheetId.find(obj);
	if (it == m_gridData2SheetId.end())
		return INVALIDIDX;
	else
		return getSheetIndex(it->second);
}

IDX KEtRevisionContext::getSheetIndexByDvPlayer(const AbsObject* obj)
{
	auto it = m_dvs2SheetId.find(obj);
	if (it == m_dvs2SheetId.end())
		return INVALIDIDX;
	else
		return getSheetIndex(it->second);
}

bool KEtRevisionContext::getShapeIdxPath(drawing::GroupShape* shapeGp, WebID objId, ShapeIdxPath& idxPath, ShapeCountPath& idxInfo)
{
	INT32 shapeCnt = shapeGp->childCount();
	for (int i = 0; i < shapeCnt; ++i)
	{
		drawing::AbstractShape* pShape = shapeGp->childAt(i);
		if (pShape->objId() == objId)
		{
			idxPath.push_back(i);
			idxInfo.push_back(shapeCnt);
			return true;
		}

		if (pShape->isGroupShape()) 
		{
			bool isFound = getShapeIdxPath(static_cast<drawing::GroupShape*>(pShape), objId, idxPath, idxInfo);
			if(isFound)
			{
				idxPath.push_back(i);
				idxInfo.push_back(shapeCnt);
				return true;
			}
		}
	}
	return false;
}

void KEtRevisionContext::getShapeIdxPathById(IDX iSheet, WebID objId, ShapeIdxPath& idxPath, ShapeCountPath& idxInfo)
{
	IKWorksheet* pSheet = m_wb->GetWorksheets()->GetSheetItem(iSheet);
	if(pSheet == nullptr)
		return;

	ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
	oplGetSheetOplData(pSheet->GetSheet(), &spCanvas);
	if (!spCanvas)
		return;
	ks_castptr<EtShapeTree> shapeTree = spCanvas;
	getShapeIdxPath(shapeTree, objId, idxPath, idxInfo);
	if (idxPath.size() > 1)
	{
		std::reverse(idxPath.begin(), idxPath.end());
		std::reverse(idxInfo.begin(), idxInfo.end());
	}
}

drawing::AbstractShape* KEtRevisionContext::getShapeByIdxPath(IDX iSheet, const ShapeIdxPath& idxPath)
{
	if (idxPath.size() < 1)
		return nullptr;

	IKWorksheet* pSheet = m_wb->GetWorksheets()->GetSheetItem(iSheet);
	if(pSheet == nullptr)
		return nullptr;

	ks_stdptr<IKDrawingCanvas> spCanvas = NULL;
	oplGetSheetOplData(pSheet->GetSheet(), &spCanvas);
	if (!spCanvas)
		return nullptr;
	ks_castptr<EtShapeTree> shape = spCanvas;
	for (size_t i = 0; i < idxPath.size(); ++i) {
		if(!shape->isGroupShape())
			return nullptr;

		drawing::GroupShape* shapeGp = static_cast<drawing::GroupShape*>(shape.get());
		int idx = idxPath[i];
		if (idx >= shapeGp->getChildCount() || idx < 0)
			return nullptr;

		shape = shapeGp->childAt(idx);
	}
	return shape;
}

bool KEtRevisionContext::getChartShapeIdxPath(drawing::AbstractShape* shapeGp, WebID objId,
                                              ShapeIdxPath& idxPath)
{
	if (!shapeGp) {
		return false;
	}

    INT32 shapeCnt = shapeGp->getChildCount();
    for (int i = 0; i < shapeCnt; ++i) {
        AbstractModel* pShape = shapeGp->getChild(i);
        if (pShape->objId() == objId) {
            idxPath.push_back(i);
            return true;
        }

        bool isFound = getChartShapeIdxPath(dynamic_cast<drawing::AbstractShape*>(pShape), objId, idxPath);
        if (isFound) {
            idxPath.push_back(i);
            return true;
        }
    }

    return false;
}

void KEtRevisionContext::getChartShapeIdxPathById(drawing::AbstractShape* root, WebID objId, ShapeIdxPath& idxPath)
{
	getChartShapeIdxPath(root, objId, idxPath);
    if(idxPath.size() > 1) {
		std::reverse(idxPath.begin(), idxPath.end());
	}
}

drawing::AbstractShape* KEtRevisionContext::getChartRootByIdxPath(IDX iSheet, const ShapeIdxPath& idxPath)
{
	auto *pShape = getShapeByIdxPath(iSheet, idxPath);
	if (!pShape) {
		return nullptr;
	}

	drawing::ShapeTreeControl* pCtl = getShapeTreeControl(pShape);
	ks_stdptr<IKCoreObject> spParent;
	pCtl->getSupLayerControl()->getCoreObject(pCtl->getLayer()->getSupLayer()->getModel(), &spParent);
	if (spParent != nullptr) {
		KsoShape* kShape = nullptr;
		getShapeTreeApiFactory(pShape)->CreateShape(spParent, pShape, &kShape);
		ks_stdptr<oldapi::KsoChart> spChart;
		kShape->get_Chart(&spChart);
		if (!spChart)
			return nullptr;

		return dynamic_cast<drawing::AbstractShape*>(spChart->coreChartShape());
	}

	return nullptr;
}

drawing::AbstractShape* KEtRevisionContext::getChartShapeByIdxPath(drawing::AbstractShape* root, const ShapeIdxPath& idxPath)
{
	if (idxPath.size() < 1)
		return nullptr;

	AbstractModel *shape = root;
	for (size_t i = 0; i < idxPath.size(); ++i) {
		int idx = idxPath[i];
		if (idx >= shape->getChildCount() || idx < 0)
			return nullptr;

		shape = shape->getChild(idx);
	}
	return dynamic_cast<drawing::AbstractShape*>(shape);
}

void KEtRevisionContext::setFilterExecRes(int nSourceCount, int nResultCount)
{
	m_nSourceCount = nSourceCount;
	m_nResultCount = nResultCount;
}

void KEtRevisionContext::getFilterExecRes(int& nSourceCount, int& nResultCount)
{
	nSourceCount = m_nSourceCount;
	nResultCount = m_nResultCount;
}

void KEtRevisionContext::setFlashFillRect(const RECT& rc)
{
	m_flashFillRect = rc;
}

void KEtRevisionContext::getFlashFillRect(const RECT** ppRects)
{
	*ppRects = &m_flashFillRect;
}

void KEtRevisionContext::beginIgnoreHistory()
{
	m_bIgnoreHistory = true;
}

bool KEtRevisionContext::isIgnoreHistory()
{
	return m_bIgnoreHistory;
}

void KEtRevisionContext::endIgnoreHistory()
{
	m_bIgnoreHistory = false;
}

bool KEtRevisionContext::isSyncSupBookCache()
{
	return m_bSyncSupBookCache;
}

void KEtRevisionContext::addSupBookCache(PCWSTR fileId)
{
	m_supBookCacheUpdateInfos.insert(std::make_pair(fileId, SupBookCacheUpdateInfo()));
}

void KEtRevisionContext::addSupBookCacheName(PCWSTR fileId, IDX idxName)
{
	auto it = m_supBookCacheUpdateInfos.find(fileId);
	if (it == m_supBookCacheUpdateInfos.end())
	{
		ASSERT(FALSE);
		return;
	}

	it->second.names.push_back(idxName);
}

void KEtRevisionContext::addSupBookCacheCell(PCWSTR fileId, IDX iSheet, ROW r, COL c)
{
	auto it = m_supBookCacheUpdateInfos.find(fileId);
	if (it == m_supBookCacheUpdateInfos.end())
	{
		ASSERT(FALSE);
		return;
	}

	it->second.sheetsData[iSheet][r].push_back(c);
}

void KEtRevisionContext::addSupBookCacheEmptyRow(PCWSTR fileId, IDX iSheet, ROW r)
{
	auto it = m_supBookCacheUpdateInfos.find(fileId);
	if (it == m_supBookCacheUpdateInfos.end())
	{
		ASSERT(FALSE);
		return;
	}

	it->second.sheetsData[iSheet].insert(std::make_pair(r, SupBookCacheUpdateInfo::ROWDATA()));
}

void KEtRevisionContext::markDirtySupBook(PCWSTR fileId)
{
	m_supBooksStateToSerial.insert(fileId);
}

bool KEtRevisionContext::isDirtySupBook(PCWSTR fileId)
{
	return m_supBooksStateToSerial.find(fileId) != m_supBooksStateToSerial.end();
}

void KEtRevisionContext::clearDirtySupBook()
{
	m_supBooksStateToSerial.clear();
}

void KEtRevisionContext::InitCopyShapeCtx(const ks_wstring& prefixPath, const ks_wstring& postfixPath)
{
	m_copyShapeCtx.ResetCtx();
	m_copyShapeCtx.bIsCopying = true;
	m_copyShapeCtx.cloudPathPrefix = prefixPath;
	m_copyShapeCtx.cloudPathPostfix = postfixPath;
}

bool KEtRevisionContext::GenWoCloudImgPath(INT shapeId, ks_wstring& imgPath)
{
	if (!m_copyShapeCtx.bIsCopying)	false;

	auto itor = m_copyShapeCtx.shapeSha1Maper.find(shapeId);
	if (itor == m_copyShapeCtx.shapeSha1Maper.end())
		return false;

	imgPath = m_copyShapeCtx.cloudPathPrefix + itor->second + m_copyShapeCtx.cloudPathPostfix;
	WOLOG_INFO << "[GenWoCloudImgPath] CloudImgPath: " << imgPath;
	return true;
}


void KEtRevisionContext::AddCopyShapeInfo(drawing::AbstractShape* shape, BOOL* pSameAdjacentSha1)
{
	if (!m_copyShapeCtx.bIsCopying)	return;

	IKBlipAtom* spBlipAtom = shape->picID();
	if (nullptr == spBlipAtom)
		return;

	INT id = shape->id();
	ks_bstr strUrl;
	spBlipAtom->GetLinkPath(&strUrl);
	if (!strUrl.empty() && IS_WO_ET_ATTACHMENT_LINK_PATH(strUrl.c_str()))
	{
		m_copyShapeCtx.shapeSha1Maper.insert(std::make_pair(id, util::getAttachmentId(strUrl.c_str())));
	}
	else
	{
		int dpi = getUser()->getDpi();
		QByteArray qSha1, picData;
		if (shape->getPicture(qSha1, picData, dpi / 96.0) != WO_OK)
			return;

		ks_wstring sha1 = krt::utf16(QString(qSha1));
		m_copyShapeCtx.shapeSha1Maper.insert(std::make_pair(id, sha1));

		if (m_copyShapeCtx.lastSha1Value == sha1)
		{
			if (pSameAdjacentSha1)
				*pSameAdjacentSha1 = TRUE;
		}
		else
		{
			m_copyShapeCtx.lastSha1Value = sha1;
		}

		WOLOG_INFO << "[AddCopyShapeInfo] shapeId: " << id  << ", sha1: " << QString(qSha1);
	}
}

void KEtRevisionContext::ResetCopyShapeCtx(ISerialAcceptor* acpt)
{
	if (not m_copyShapeCtx.mapObjectType.empty())
	{
		// 上报埋点
		collectInfo("behaviour_webet_copy_cell_img", m_copyShapeCtx.mapObjectType.size());
		// 提供给前端
		if (acpt)
		{
			acpt->addKey("htmlClipboardObjId");
			acpt->beginArray();
			for (const auto& elem : m_copyShapeCtx.mapObjectType)
			{
				acpt->beginStruct();
					acpt->addString("id", elem.first.c_str());
					acpt->addUint32("type", elem.second);
				acpt->endStruct();
			}
			acpt->endArray();
		}
	}

	m_copyShapeCtx.ResetCtx();
}

BOOL KEtRevisionContext::genUrlBySha1(drawing::AbstractShape* pAbsShape, OUT BSTR* pUrl)
{
	QByteArray sha1;
	bool uploaded = util::UploadImg(
		getUser()->getDpi(), pAbsShape, sha1);
	if (not uploaded)
	{
		WOLOG_INFO << "[genUrlBySha1] shape get Picture failed!";
		return FALSE;
	}

	ks_wstring htmlPath;
	QString sha1Str(sha1);
	genImgHtmlUrl(krt::utf16(sha1Str), WebDownloadBySha1, htmlPath);
	*pUrl = htmlPath.AllocBSTR();
	return TRUE;
}

// 对本地图片和云空间图片, objectKey是sha1; 对附件图片, objectKey是attachmentId
void KEtRevisionContext::genImgHtmlUrl(PCWSTR objectKey, WebDownloadBasicTag tag, ks_wstring& output)
{
	output = m_copyShapeCtx.cloudPathPrefix + objectKey + m_copyShapeCtx.cloudPathPostfix;
	m_copyShapeCtx.mapObjectType[objectKey] = tag;
}


BOOL KEtRevisionContext::GenWoImgHtmlUrl(IN IKShape* pShape, OUT BSTR* pUrl)
{
	if (nullptr == pShape || nullptr == pUrl)
		return FALSE;
	// 获取服务端提供的图片的临时链接
	LONG nId = 0;
	pShape->GetShapeID(&nId);

	ks_castptr<drawing::AbstractShape> pAbsShape = pShape;
	if (nullptr == pAbsShape)
		return FALSE;
	IKBlipAtom* spBlipAtom = pAbsShape->picID();
	if (nullptr == spBlipAtom)
		return FALSE;

	ks_bstr strUrl;
	spBlipAtom->GetLinkPath(&strUrl);
	if (strUrl.empty())
	{
		// 本地图片(非在线图片)
		ks_wstring cloudPath;
		if (GenWoCloudImgPath(nId, cloudPath))
		{
			*pUrl = cloudPath.AllocBSTR();
			return TRUE;
		}
		else
		{
			return genUrlBySha1(pAbsShape, pUrl);
		}
	}
	else
	{
		// 在线图片(1附件图片;2.云空间图片)
		ks_wstring htmlPath;
		bool isAttachment = IS_WO_ET_ATTACHMENT_LINK_PATH(strUrl.c_str()); // 判断是否为附件
		if (isAttachment)
		{
			
			genImgHtmlUrl(util::getAttachmentId(strUrl.c_str()), WebDownloadByAttachmentId, htmlPath);
			*pUrl = htmlPath.AllocBSTR();
			return TRUE;
		}
		else
		{
			// 分析发现云空间图片的 GetLinkPath() 返回的是下载链接, 而它同样有sha1, 应当用sha1获取url
			return genUrlBySha1(pAbsShape, pUrl);
		}
	}
	// 不在函数末尾return. 确保每个分支中的逻辑都正确处理了.
}


void KEtRevisionContext::SetEnableCellMultiLink(bool b)
{
	m_pasteTextCtx.bEnableCellMultiHyperlinks = b;
}

bool KEtRevisionContext::GetEnableCellMultiLink()
{
	return m_pasteTextCtx.bEnableCellMultiHyperlinks;
}

void KEtRevisionContext::SetUTF_8_BOM(bool b)
{
	m_pasteTextCtx.bEnableBom = b;
}
bool KEtRevisionContext::GetUTF_8_BOM() 
{
	return m_pasteTextCtx.bEnableBom;
}


BOOL KEtRevisionContext::GetNeedCleanChildProc()
{
	return m_bNeedCleanChildProc;
}

void KEtRevisionContext::OnClearAllUndoRedoSteps()
{
	KEtVersionManager* versionMgr= static_cast<KEtVersionManager*>(m_pWoWb->getVersionMgr());
	versionMgr->markClearVersionData(true);
}

void KEtRevisionContext::SetClearCurVersionCmd()
{
	KEtVersionManager* versionMgr= static_cast<KEtVersionManager*>(m_pWoWb->getVersionMgr());
	versionMgr->markClearCurVersionCmdData(true);	
}

void KEtRevisionContext::SetAutoFixLinkRunsFlag(bool b)
{
	m_pasteTextCtx.bAutoFixLinkRuns = b;
}

bool KEtRevisionContext::GetAutoFixLinkRunsFlag()
{
	return m_pasteTextCtx.bAutoFixLinkRuns;
}

void KEtRevisionContext::resetSyncSupBookCacheCtx(bool bEnableSync)
{
	m_bSyncSupBookCache = bEnableSync;
	m_supBookCacheUpdateInfos.clear();
}

void KEtRevisionContext::SetEnablePasteDynamicArray(bool b)
{
	m_dynamicArrayCtx.m_bEnablePasteDynamicArray = b;
}

bool KEtRevisionContext::GetEnablePasteDynamicArray(bool bPostExecute)
{
	if (bPostExecute)
		return m_dynamicArrayCtx.m_bGotEnablePasteDynamicArray && m_dynamicArrayCtx.m_bEnablePasteDynamicArray;

	m_dynamicArrayCtx.m_bGotEnablePasteDynamicArray = true;
	return m_dynamicArrayCtx.m_bEnablePasteDynamicArray;
}

void KEtRevisionContext::SetEnablePasteDynamicArrayValue(bool b)
{
	m_dynamicArrayCtx.m_bEnablePasteDynamicArrayValue = b;
}

bool KEtRevisionContext::GetEnablePasteDynamicArrayValue(bool bPostExecute)
{
	if (bPostExecute)
		return m_dynamicArrayCtx.m_bGotEnablePasteDynamicArrayValue && m_dynamicArrayCtx.m_bEnablePasteDynamicArrayValue;

	m_dynamicArrayCtx.m_bGotEnablePasteDynamicArrayValue = true;
	return m_dynamicArrayCtx.m_bEnablePasteDynamicArrayValue;
}

const SupBookCacheUpdateInfos& KEtRevisionContext::getSupBookCacheUpdateInfos() const
{
	return m_supBookCacheUpdateInfos;
}

PCWSTR KEtRevisionContext::getNetDiskName()
{
	return util::getNetDiskName();
}

ks_wstring KEtRevisionContext::GetDownloadFileUserid()
{
	if (m_user)
	{
		PCWSTR userId = m_user->userID();
		if (userId)
			return ks_wstring(userId);
	}

	return ks_wstring(); // 服务端已经支持空用户名
}

bool KEtRevisionContext::asyncGetNetDiskFileByFileid(PCWSTR fileId)
{
	IWoETSettings* pWoEtSettings = _kso_GetWoEtSettings();
	BOOL fallback = pWoEtSettings && pWoEtSettings->IsCrossBookEmptyUserFallback();
	if (fallback)
	{
		return m_pWoWb->asyncGetNetDiskFile(m_user->userID(), fileId, DownloadFileTask_CrossBook);
	}
	else
	{
		ks_wstring userId = GetDownloadFileUserid();
		return m_pWoWb->asyncGetNetDiskFile(userId.c_str(), fileId, DownloadFileTask_CrossBook);
	}
}


bool KEtRevisionContext::asyncGetNetDiskFileByUrl(PCWSTR url, DownloadFileTask downloadFileTask)
{
	IWoETSettings* pWoEtSettings = _kso_GetWoEtSettings();
	BOOL fallback = pWoEtSettings && pWoEtSettings->IsCrossBookEmptyUserFallback();
	if (fallback)
	{
		return m_pWoWb->asyncGetNetDiskFile(m_user->userID(), url, downloadFileTask);
	}
	else
	{
		ks_wstring userId = GetDownloadFileUserid();
		return m_pWoWb->asyncGetNetDiskFile(userId.c_str(), url, downloadFileTask);		
	}

}

std::size_t KEtRevisionContext::ProtectionCtxExchanger::numObjects = 0;

KEtRevisionContext::ProtectionCtxExchanger* 
	KEtRevisionContext::ProtectionCtxExchanger::makeExchanger(KEtRevisionContext* etRevisionCtx, et_sptr<KProtectionContext>& spProtectionCtx)
{
	if (numObjects >= 1) return nullptr;
	ProtectionCtxExchanger* pExchanger = new ProtectionCtxExchanger(etRevisionCtx, spProtectionCtx.detach());
	++numObjects;
	return pExchanger;
}

KEtRevisionContext::ProtectionCtxExchanger::~ProtectionCtxExchanger()
{
	m_etRevisionCtx->m_spProtectionCtx.reset(m_spOldProtectionCtx.detach());
	--numObjects;
}

KEtRevisionContext::ProtectionCtxExchanger::ProtectionCtxExchanger(KEtRevisionContext* etRevisionCtx, KProtectionContext* newProtectionCtx)
	: m_etRevisionCtx(etRevisionCtx)
{
	m_spOldProtectionCtx.reset(m_etRevisionCtx->m_spProtectionCtx.detach());
	m_etRevisionCtx->m_spProtectionCtx.reset(newProtectionCtx);
}

std::unique_ptr<KEtRevisionContext::ProtectionCtxExchanger> KEtRevisionContext::generateExchanger(et_sptr<KProtectionContext>& spProtectionCtx)
{
	return std::unique_ptr<ProtectionCtxExchanger>(ProtectionCtxExchanger::makeExchanger(this, spProtectionCtx));
}

SUPBOOK_STAT KEtRevisionContext::getNetDiskFile(PCWSTR netDiskName, PCWSTR fileId, 
	ks_wstring& path, ks_wstring& version, ks_wstring& fileName)
{
	QString strUserId;
	if(m_user)
		strUserId = QString::fromUtf16(m_user->userID());
	return util::getNetDiskFile(netDiskName, fileId, path, version, fileName, strUserId);
}

ISupBooksCtx* KEtRevisionContext::getSupBookCtx()
{
	return &m_supBooksCtx;
}

KSupBooksCtx::~KSupBooksCtx()
{
	for (auto it = m_booksInfo.begin(); it != m_booksInfo.end(); ++it)
		alg::msrUnreferStringResource(it->first);
	
	for (auto it = m_book2SheetInfo.begin(); it != m_book2SheetInfo.end(); ++it)
		alg::msrUnreferStringResource(it->first);
}

void KSupBooksCtx::reset(const binary_wo::VarObj& booksCtx)
{
	for (auto it = m_booksInfo.begin(); it != m_booksInfo.end(); ++it)
		alg::msrUnreferStringResource(it->first);
	m_booksInfo.clear();

	for (auto it = m_book2SheetInfo.begin(); it != m_book2SheetInfo.end(); ++it)
		alg::msrUnreferStringResource(it->first);

	m_book2SheetInfo.clear();
	
    if (booksCtx.type() == binary_wo::typeInvalid)
		return; // 空时相当于清除。

	const binary_wo::VarObj booksMap = booksCtx.get("booksMap");
	for (int i = 0, count = booksMap.arrayLength_s(); i < count; ++i)
	{
		binary_wo::VarObj item = booksMap.at_s(i);
		insert(item.field_str("name"), item.field_str("fullName"), item.field_str("fileId"));
	}

	const binary_wo::VarObj sheetsMatcher = booksCtx.get("sheetsMatcher");
	for (int i = 0, nMatchers = sheetsMatcher.arrayLength_s(); i < nMatchers; ++i)
	{
		binary_wo::VarObj item = sheetsMatcher.at_s(i);
		SheetsInfo* pSheets = getSheetsInfo(item.field_str("fullName"), true);

		binary_wo::VarObj sheets = item.get("sheets");
		for(int j = 0, nSheets = sheets.arrayLength_s(); j < nSheets; ++j)
		{
			binary_wo::VarObj item = sheets.at_s(j);
			pSheets->m_old2new.insert(std::make_pair(ks_wstring(item.field_str("oldSheet")), ks_wstring(item.field_str("newSheet"))));
		}
	}
}

void KSupBooksCtx::insertSupBook(PCWSTR fullName, PCWSTR fileId)
{
	insert(fullName, fullName, fileId);
}

bool KSupBooksCtx::insert(PCWSTR name, PCWSTR fullName, PCWSTR fileId)
{
	alg::MSR_HANDLE hd = alg::msrIdentifyStringResource(name);
	auto it = m_booksInfo.find(hd);
	if (it != m_booksInfo.end()) 
	{
		it->second.fullName = fullName;
		it->second.fileId = fileId;
		alg::msrUnreferStringResource(hd);
		return false;
	}
	Value val;
	val.fullName = fullName;
	val.fileId = fileId;

	m_booksInfo.insert(std::make_pair(hd, val));
	return true;
}

PCWSTR KSupBooksCtx::findFullName(PCWSTR name)
{
	alg::MSR_HANDLE hd = alg::msrIdentifyStringResource(name);
	auto it = m_booksInfo.find(hd);
	alg::msrUnreferStringResource(hd);

	if (it == m_booksInfo.end())
		return NULL;

	return it->second.fullName.c_str();
}

PCWSTR KSupBooksCtx::findFileId(PCWSTR name)
{
	alg::MSR_HANDLE hd = alg::msrIdentifyStringResource(name);
	auto it = m_booksInfo.find(hd);
	alg::msrUnreferStringResource(hd);

	if (it == m_booksInfo.end())
		return NULL;

	return it->second.fileId.c_str();
}

void KSupBooksCtx::getUnmatchSheetInfo(PCWSTR fullBookName, binary_wo::VarObj& infoObj)
{
	SheetsInfo* pSheets = getSheetsInfo(fullBookName, true);
	binary_wo::VarObj arrOld = infoObj.add_field_array("unmatchSheets", binary_wo::typeString);
	for (size_t i = 0; i < pSheets->m_unmatchOldName.size(); ++i)
		arrOld.add_item_str(pSheets->m_unmatchOldName[i].c_str());
    
	binary_wo::VarObj arrNew = infoObj.add_field_array("newSheets", binary_wo::typeString);
	for (size_t i = 0; i < pSheets->m_newName.size(); ++i)
		arrNew.add_item_str(pSheets->m_newName[i].c_str());
}

KSupBooksCtx::SheetsInfo* KSupBooksCtx::getSheetsInfo(PCWSTR fullBookName, bool bGain)
{
	alg::MSR_HANDLE hd = alg::msrIdentifyStringResource(fullBookName);
	auto it = m_book2SheetInfo.find(hd);
	if (bGain) 
	{
		auto res = m_book2SheetInfo.insert(std::make_pair(hd, SheetsInfo()));
		return &(res.first->second);
	}
	else 
	{
		alg::msrUnreferStringResource(hd);
		if (it == m_book2SheetInfo.end())
			return NULL;

		return &(it->second);
	}
}

static bool IsCellImgListSheet(PCWSTR name)
{
	return 0 == xstrcmp(name, STR_CELL_IMAGE_SHEET_NAME);
}

void KSupBooksCtx::collectUnmatchSheets(PCWSTR fullBookName, ISheetsNameMatcher* pMatcher)
{
	auto sheetsInfo = getSheetsInfo(fullBookName, true);
	if (sheetsInfo->m_newName.empty()) 
	{
		for (INT i = 0, size = pMatcher->NewSheetsCount(); i < size; ++i)
		{
			ks_bstr bstr;
			pMatcher->GetNewSheetName(i, &bstr);
			ks_wstring name(bstr);
			if (IsCellImgListSheet(name.c_str()))
				continue;
			sheetsInfo->m_newName.push_back(bstr.c_str());
		}
	}

	if (sheetsInfo->m_unmatchOldName.empty())
	{
		for (INT i = 0, size = pMatcher->OldSheetsCount(); i < size; ++i)
		{
			ks_bstr bstr;
			pMatcher->GetOldSheetName(i, &bstr);
			sheetsInfo->m_unmatchOldName.push_back(bstr.c_str());
		}
	}
}

void KSupBooksCtx::userAssignSheetsName(PCWSTR fullBookName, ISheetsNameMatcher* pMatcher)
{
	auto sheetsInfo = getSheetsInfo(fullBookName, false);
	if (sheetsInfo == NULL) 
	{
		collectUnmatchSheets(fullBookName, pMatcher);
		return;
	}

	std::map<ks_wstring, INT, sheetNameCmper> newNameToIdx;
	for (INT i = 0, size = pMatcher->NewSheetsCount(); i < size; ++i)
	{
		ks_bstr bstr;
		pMatcher->GetNewSheetName(i, &bstr);
		newNameToIdx.insert(std::make_pair(ks_wstring(bstr.c_str()),i));
	}

	for (INT i = 0, size = pMatcher->OldSheetsCount(); i < size; ++i)
	{
		ks_bstr bstr;
		pMatcher->GetOldSheetName(i, &bstr);
		auto it = sheetsInfo->m_old2new.find(bstr.c_str());

		if (it == sheetsInfo->m_old2new.end())
			continue; // 没有指定
		
		auto itIdx = newNameToIdx.find(it->second);
		if (itIdx == newNameToIdx.end()) 
		{
			sheetsInfo->m_unmatchOldName.push_back(bstr.c_str()); // 指定的sheet又不在了。
			continue;
		}
		pMatcher->SetMatchPair(i, itIdx->second);
	}

	collectUnmatchSheets(fullBookName, pMatcher);
}

void KEtRevisionContext::ViewRect2CoreRect(const drawing::AbstractShape* shape, QRectF& rcfView, QRectF& rcfCore)
{
	if (!shape) return;

	m_pWoWb->ViewRect2CoreRect(shape->getActiveShapeVisual(), rcfView, rcfCore);
}

PCWSTR KEtRevisionContext::getFileId()
{
	return m_pWoWb->getFileId();
}

void KEtRevisionContext::onDbAutomationTriggered(
	WebTriggerAction action,
	long triggerTime,
	const WebSlice *msg,
	UINT sheetId,
	EtDbId recordId,
	EtDbId automationId,
	EtDbId actionId,
	HRESULT hr)
{
	if (gs_callback && gs_callback->trigger && action != WoTriggerActionInvalid)
	{
		QString qRecordId = QString::number(recordId);
		QString qAutomationId = QString::number(automationId);
		QString qActionId = QString::number(actionId);
		QString qSheetId = QString::number(sheetId);
		QString qGroupId = QStringLiteral("%1|%2|%3").arg(qSheetId).arg(qAutomationId).arg(qActionId);
		QString qTimerId = QStringLiteral("%1|%2").arg(qGroupId).arg(qRecordId);
		gs_callback->trigger(action, qGroupId.toUtf8(), qTimerId.toUtf8(), triggerTime, msg);
	}

	// ActionNow
	if (triggerTime != -1)
		return;

	if (m_automationTriggerInfo.triggeredCount == 0 || FAILED(m_automationTriggerInfo.hr))
	{
		m_automationTriggerInfo.sheetId = sheetId;
		m_automationTriggerInfo.recordId = recordId;
		m_automationTriggerInfo.automationId = automationId;
		m_automationTriggerInfo.hr = hr;
	}
	m_automationTriggerInfo.triggeredCount++;
}

void KEtRevisionContext::serialiseDbAutomationTriggeredInfo(ISerialAcceptor *acpt)
{
	acpt->beginStruct();
	if (m_automationTriggerInfo.triggeredCount > 0)
	{
		if (FAILED(m_automationTriggerInfo.hr))
			acpt->addString("errName", GetErrWideString(m_automationTriggerInfo.hr));
		acpt->addInt32("triggeredCount", m_automationTriggerInfo.triggeredCount);
		acpt->addUint32("sheetId", m_automationTriggerInfo.sheetId);
		EtDbIdStr buf;
		VS(_appcore_GainDbSheetContext()->EncodeEtDbId(m_automationTriggerInfo.recordId, &buf));
		acpt->addString("recordId", buf);
		VS(_appcore_GainDbSheetContext()->EncodeEtDbId(m_automationTriggerInfo.automationId, &buf));
		acpt->addString("automationId", buf);
	}
	acpt->endStruct();
}

ks_stdptr<ISheetProtection> KEtRevisionContext::getSheetProtection(ISheet *spSheet)
{
	return wo::util::getSheetProtection(spSheet);
}

IEtProtectionCtx* KEtRevisionContext::getProtectionCtx()
{
	return m_spProtectionCtx;
}

void KEtRevisionContext::addCloudImgAttachmentId(drawing::AbstractShape* pShape)
{
	if (!pShape->isPicture())
		return;

	ks_bstr path;
	IKBlipAtom* pBlipAtom = pShape->picID();
	if (pBlipAtom && pBlipAtom->GetLinkPath(&path) == S_OK && !path.empty())
	{
		PCWSTR atttachmentId = util::getAttachmentId(path);
		if (atttachmentId && *atttachmentId)
			m_attachmentIdsToFetch.emplace(atttachmentId);
	}
}

void KEtRevisionContext::addCloudImgAttachmentIdDirectly(LPCWSTR attachmentId)
{
    if (attachmentId)
        m_attachmentIdsToFetch.emplace(attachmentId);
}

void KEtRevisionContext::addCloudImgAttachmentId(LPCWSTR path)
{
	PCWSTR atttachmentId = util::getAttachmentId(path);
	if (atttachmentId && *atttachmentId)
		m_attachmentIdsToFetch.emplace(atttachmentId);
}

void KEtRevisionContext::addAttachmentAnnexIdBySheet(ISheet* pSheet)
{
	std::vector<ks_wstring> vecIds;
	std::vector<ks_wstring> vecIdsVideo;
	getAttachmentAnnexIdBySheet(pSheet, vecIds, vecIdsVideo);
	for (int i = 0; i < vecIds.size(); i++)
	{
		m_attachmentIdsToFetch.emplace(vecIds[i]);
	}
	for (int i = 0; i < vecIdsVideo.size(); i++)
	{
		m_attachmentIdsToFetchVideo.emplace(vecIdsVideo[i]);
	}
}

void KEtRevisionContext::getAttachmentAnnexIdBySheet(ISheet* spSheet, std::vector<ks_wstring>& vecIds,
	std::vector<ks_wstring>& vecIdsVideo) 
{
	if (!spSheet)
	{
		return;
	}
	ks_stdptr<IKHyperlinks> pHLinks;
	ks_stdptr<IKHyperlink> spHLink;

	ks_stdptr<IUnknown> spUnk;
	HRESULT hr;
	if (FAILED(hr = spSheet->GetExtDataItem(edSheetHyperlinks, &spUnk)) || spUnk == NULL)
		return;
	spUnk->QueryInterface(IID_IKHyperlinks, (void**)&pHLinks);
	if (pHLinks == NULL)
		return;

	pHLinks->ResetToLast();
	BOOL isVideo = FALSE;
	for (; S_OK == pHLinks->Prev(&spHLink); spHLink.clear())
	{
		ASSERT(spHLink != NULL);
		HYPERLINKTYPE hlType = HLinkNone;
		spHLink->GetHyperlinkType(&hlType);
		if (FALSE == IsRunsLink(hlType))
			continue;

		ks_stdptr<IHyperlinkRuns> spRuns;
		spHLink->GetRuns(&spRuns);

		UINT count = 0;
		if (!spRuns || (count = spRuns->GetRunsCount()) <= 0)
		{
			continue;
		}

		for (UINT i = 0; i < count; ++i)
		{
			IHyperlinkRun *run = spRuns->GetRun(i);

			ks_stdptr<IHyperlinkRunAddress> address(run->GetProperty());
			if (!address)
			{
				continue;
			}

			ks_wstring addRessKs = address->GetAddress();
			if (!addRessKs.find(__X("kw:annex")) == 0)
			{
				continue;
			}
			isVideo = FALSE;
			ks_wstring atttachmentId = util::getAttachmentIdByAnnex(addRessKs.c_str(), isVideo);
			if (atttachmentId.empty())
			{
				continue;
			}

			if (isVideo)
			{
				vecIdsVideo.push_back(atttachmentId);
				continue;
			}

			vecIds.push_back(atttachmentId);
		}		
	}
}

void KEtRevisionContext::addCloudImgAttachmentIdByAnnex(LPCWSTR path)
{
	BOOL isVideo = FALSE;
 	ks_wstring atttachmentId = util::getAttachmentIdByAnnex(path, isVideo);

	if (atttachmentId.empty())
	{
		return;
	}

	if (isVideo)
	{
		m_attachmentIdsToFetchVideo.emplace(atttachmentId);
		return;
	}

	m_attachmentIdsToFetch.emplace(atttachmentId);
}

void KEtRevisionContext::getAllAttachmentIds(std::vector<ks_wstring>& vecIds, std::vector<ks_wstring>& vecIdsVideo)
{
	for (const ks_wstring& id : m_attachmentIdsToFetch)
		vecIds.push_back(id);

	for (const ks_wstring& id : m_attachmentIdsToFetchVideo)
		vecIdsVideo.push_back(id);

}

void KEtRevisionContext::clearAttachmentIds()
{
	m_attachmentIdsToFetch.clear();
	m_attachmentIdsToFetchVideo.clear();
}

void KEtRevisionContext::setupProtectionCache(ISheet *spSheet)
{
	m_spProtectionCtx->setupProtectionCache(spSheet);
}

void KEtRevisionContext::collectCellHistoryInfo(ISheet* spSheet)
{
	//note: 这里还没没有调用 InitContext, commitVersion 还没设置上
	if (!m_bk->GetBMP()->bKsheet || spSheet->IsDbSheet() || spSheet->IsAppSheet() || spSheet->IsWorkbenchSheet())
		return;

	ISheetStake *stake = spSheet->GetWoStake();
	if (!stake) 
		return;

	IHistoryInfo *spHistory = stake->getCellHistory();
	if (!spHistory)
		return;

	m_cellHistoryCtx.addCommitCount(spHistory->getCommitCount());
}

void KEtRevisionContext::setupEtContext()
{
	m_spProtectionCtx->setupEtContext();
}

void KEtRevisionContext::clearEtContext()
{
	m_spProtectionCtx->clearEtContext();
}

IWoFilterContext* KEtRevisionContext::getFilterContext()
{
	return &m_filterCtx;
}

void KEtRevisionContext::SetCmdName(PCWSTR name)
{
	m_cmdName = name;
	if (xstrcmp(__X("dbSheet.removeRecords"), name) == 0)
		m_IsNoCheckPerms = true;
}

void KEtRevisionContext::collectInfo(const char *collectName, int count /* = 1 */)
{
	binary_wo::BinWriter binWriter;
	collectInfoPart(binWriter, collectName, count);
	if (getUser())
	{
		PCWSTR userId = getUser()->userID();
		// 埋点中增加 "_account_id" 信息. 服务端接受的字段名是 _account_id, 但以前的代码都是 account_id
		// 目前提交两个字段, 确保信息有被收集
		binWriter.addKey("account_id");
		binWriter.addString(userId);
		binWriter.addKey("_account_id");
		binWriter.addString(userId);
	}
	
	BinWriter::StreamHolder shbt = binWriter.buildStream();
	WebSlice slice = {shbt.get(), binWriter.writeLength()};
	gs_callback->collectInfo("fileinclude", &slice);

	WOLOG_INFO << "[collectInfo] " << collectName;
}

void KEtRevisionContext::collectInfoPart(binary_wo::BinWriter& binWriter, const char *collectName, int count)
{
	binWriter.addKey("name");
	binWriter.addString(krt::utf16(QString::fromUtf8(collectName)));
	binWriter.addKey("count");
	binWriter.addInt32(count);

	if (getFileId())
	{
		binWriter.addKey("fileid");
		binWriter.addString(getFileId());
	}

	if (m_cmdName.size() != 0)
	{
		binWriter.addKey("cmd_name");
		binWriter.addString(m_cmdName.c_str());
	}

	FileInfoCollector::AddComponentInfo(binWriter, m_bk);
}

void KEtRevisionContext::collectTasksDirectFailedInfo(HRESULT hr, const char *collectName, int count /* = 1 */)
{
	binary_wo::BinWriter binWriter;
	collectInfoPart(binWriter, collectName, count);
	if (getUser())
	{
		PCWSTR userId = getUser()->userID();
		binWriter.addKey("account_id");
		binWriter.addString(userId);
	}

	binWriter.addKey("exec_state");
	binWriter.addString(isExecDirect() ? __X("execDirect") : __X("exec"));

	binWriter.addKey("hr");
	binWriter.addInt32(hr);
	binWriter.addKey("hr_detail");
	binWriter.addString(GetErrWideString(hr));

	BinWriter::StreamHolder shbt = binWriter.buildStream();
	WebSlice slice = {shbt.get(), binWriter.writeLength()};
	gs_callback->collectInfo("et_tasks_directfailed", &slice);

	WOLOG_INFO << "[collectTasksDirectFailedInfo] " << collectName;
}

void KEtRevisionContext::collectInfo(const WCHAR *collectName, int count, int count2)
{
	util::CollectInfo(m_pWoWb, collectName, count, count2);
}

WebInt KEtRevisionContext::fetchPermissionId(const char *userId, char *permissionIdBuf, int permissionIdBufLen)
{
	return gs_callback->fetchPermissionId(userId, permissionIdBuf, permissionIdBufLen);
}

bool KEtRevisionContext::IsOwner(LPCWSTR userId)
{
	QString strUserId = krt::fromUtf16(userId);
	WebInt retInt = gs_callback->getUserProtectManagePermission(strUserId.toUtf8());
	return retInt > 0;
}

void KEtRevisionContext::setMessageBoxAnswer(WoMbIdentifier id, int answer)
{
	m_mbAnswer.insert(std::make_pair(id, answer));
}

int KEtRevisionContext::getMessageBoxAnswer(WoMbIdentifier id)
{
	if (m_mbAnswer.count(id) > 0)
		return m_mbAnswer.at(id);
	m_mostRecentMbId = id;
	return 0;
}

WoMbIdentifier KEtRevisionContext::mostRecentMessageBoxId()
{
	return m_mostRecentMbId;
}

bool KEtRevisionContext::isPivotTableLayoutOnCellContent()
{
	return m_isPivotTableLayoutOnCellContent;
}

void KEtRevisionContext::setPivotTableLayoutOnCellContent(bool enable)
{
	m_isPivotTableLayoutOnCellContent = enable;
}

void KEtRevisionContext::postExecute(HRESULT hr)
{
	m_hr = hr;
	if (FAILED(hr))
		m_upWriter.reset(new binary_wo::BinWriter());
}

IEtPerfStatContext* KEtRevisionContext::getPerfStatCtx()
{
	return &m_perfStatContext;
}

ICellHistoryCtx* KEtRevisionContext::getCellHistoryCtx()
{
	return &m_cellHistoryCtx;
}

uint64_t KEtRevisionContext::getCurCommitVersionTime()
{
	return m_curCommitVersionTime;
}

void KEtRevisionContext::setCurCommitVersionTime(uint64_t tm)
{
	m_curCommitVersionTime = tm;
}

bool KEtRevisionContext::isUserEditMode()
{
	const ServiceType st = m_pWoWb->getServiceType();
	return st != ServiceType_QueryCellHistories && 
           st != ServiceType_ReplayHistories &&
           getCurCommitVersion() > WO_INVALID_COMMIT_ID;
}

bool KEtRevisionContext::isInRevisionMode()
{
	const ServiceType st = m_pWoWb->getServiceType();
	if (ServiceType_QueryCellHistories == st || ServiceType_ReplayHistories == st)
		return getCurCommitVersion() > WO_INVALID_COMMIT_ID;

	return isExecDirect() && getCurCommitVersion() > WO_INVALID_COMMIT_ID;
}

void KEtRevisionContext::SubmitEventTrackingInfo()
{
	IWoCallBack *pWoCb = _kso_GetWoCallBack();
	if (nullptr == pWoCb)
		return;
	for (EventTrackingInfo info : eventTrackingInfos)
	{
		pWoCb->CollectCallTimeRand(info.name, info.ms);
	}
}

void KEtRevisionContext::markVisitingView(UINT sheetId, EtDbId viewId)
{
	IKUserConn *pUserConn = getUser();
	if (!pUserConn)
		return;
	static_cast<IKETUserConn*>(pUserConn)->markVisitingView(sheetId, viewId);
}

void KEtRevisionContext::getDbVisitingViews(UINT sheetId, std::unordered_set<EtDbId> *pSet)
{
	IKUserConns *pUserConns = getUsers();
	if (!pUserConns || !pSet)
		return;

	class KEnumUserConn : public IKEnumUserConn
	{
	public:
		KEnumUserConn(UINT sheetId, std::unordered_set<EtDbId> *pSet) : m_sheetId(sheetId), m_pSet(pSet) {}
		virtual int Do(IKUserConn *pUserConn) override
		{
			ks_castptr<IKETUserConn> spUserConn = pUserConn;
			const EtDbId visitingViewId = spUserConn->getVisitingView(m_sheetId);
			if (visitingViewId != INV_EtDbId)
				m_pSet->insert(visitingViewId);
			return S_OK;
		}
	private:
		UINT m_sheetId;
		std::unordered_set<EtDbId> *m_pSet;
	};
	KEnumUserConn euc(sheetId, pSet);
	pUserConns->enumUserConn(&euc);
}

EtDbId KEtRevisionContext::getUserVisitingView(UINT sheetId, IKUserConn *pUserConn) const
{
	ks_castptr<IKETUserConn> spUserConn = pUserConn;
	return spUserConn->getVisitingView(sheetId);
}

WebProcType KEtRevisionContext::getProcType()
{
	return gs_procType;
}

void KEtRevisionContext::notifyRenderMsg(
			IRenderView* rdView,
			int viewIdx,
			int msg,
			KSO_WParam wParam,
			KSO_LParam lParam)
{
	if (viewIdx != 0) {
		return; // 多pane情况，默认在pane 0处理
	}
    if (m_bRunJs)
        return;
	util::SlowCollectCallTimeStat callTime(
		"notifyRenderMsg", 
		kCalcDirtyRegionThreshold,
		m_pWoWb,
		__X("notify_render_msg")
	);
	IKWorksheets * pWorksheets = m_wb->GetWorksheets();
	for (int i = 0, sz = pWorksheets->GetSheetCount(); i < sz; ++i) {
		IKWorksheet *pWorksheet = pWorksheets->GetSheetItem(i);
		BOOL isVisible = FALSE;
		pWorksheet->GetSheet()->GetVisible(&isVisible);
		if (!isVisible) continue;

		IRenderViews *rdViews = pWorksheet->GetActiveWorksheetView()->GetRenderViews();
		for (int i = 0; i < rdViews->GetRenderViewCount(); ++i) {
			IRenderView * renderView = rdViews->GetRenderViewItem(i);
			if (renderView != nullptr) {
				renderView->Notify((ET_RENDER_MSG)msg, wParam, lParam);
			}
		}
	}
}

void KEtRevisionContext::addBeautifyOperator(PCWSTR cmdName, PCWSTR params)
{
	m_vecBeautifyOperators.push_back({cmdName, params});
}

std::vector<std::pair<ks_wstring, ks_wstring>>* KEtRevisionContext::getBeautifyOperators()
{
	return &m_vecBeautifyOperators;
}
// 记录一下操作 禁止查看序列化时导致数据泄漏
void KEtRevisionContext::markChangeOp(ChangeOpType type)
{
	if (type == contentChangeOP)
	{
		// 改变了内容 设置标志位
		m_serializeChangeContent = true;
	}
	else if (type == coordinateChangeOp && m_serializeChangeContent)
	{
		m_isReSerialize = true;
	}

	if (type == permissionChangeOp)
		m_isReSerialize = true;
}

Database::FieldType KEtRevisionContext::getCellDbFieldType(RANGE& rg)
{
	wo::util::VALIDATION_Wrap dv;
	m_bk->LeakOperator()->GetDataValidation(rg, rg.SheetFrom(), rg.RowFrom(), rg.ColFrom(), &dv, nullptr, &rg, nullptr);

	Database::FieldContext fieldContext(m_pWoWb, this);
	Database::FieldsManager* pDbMgr = Database::FieldsManager::Instance();
	Database::IDbField* pField = pDbMgr->IdentifyAll(&fieldContext, rg, dv);

	return pField ? pField->GetType() : Database::dftInvalid;
}

IBookCollectInfo * KEtRevisionContext::collectInfo()
{
	return &m_pWoWb->collectInfo();
}

bool KEtRevisionContext::isCurUserCanProtect()
{
	if (isExecDirect())
		return true;

	if (gs_callback->getUserProtectPermission)
	{
		// todo 该回调不符合逻辑，先返回true，后续对接
		/*QString connID = QString::fromUtf16(getUser()->connID());
		bool userHasPermision = gs_callback->getUserProtectPermission(connID.toUtf8());
		WOLOG_INFO << "[protection] getUserProtectPermission: " << WOLOG_MK_KV(connID) << ", " << userHasPermision;
		return userHasPermision;*/
		return true;
	}

	return false;
}

bool KEtRevisionContext::isCurUserCanReadAllSheets()
{
	if (nullptr == m_spProtectionCtx)
		return false;
	return m_spProtectionCtx->isCanReadWholeBook();
}

bool KEtRevisionContext::isCurUserCanEditAllSheets()
{
	if (nullptr == m_spProtectionCtx)
		return false;
	return m_spProtectionCtx->isCanEditWholeBook();
}


void KEtRevisionContext::setUserRequestInfo(const char* requestInfo)
{
	m_userRequestInfo = krt::utf16(krt::fromUtf8(requestInfo));
}

const ks_wstring& KEtRevisionContext::getUserRequestInfo()
{
	return m_userRequestInfo;
}

void KEtRevisionContext::RecordErrorCell(const CELL& cell) 
{
	m_errCell = cell;
}

CELL KEtRevisionContext::GetRecordErrorCell()
{
	return m_errCell;
}


void KEtRevisionContext::addGridExportInfo(int blockCnt, int emptyCellCnt, int solidCellCnt)
{
	KCoreMetric & mc = m_pWoWb->coreMetric();
	mc.addU32Metrics(KU32MetricItem::kSerialGridBlockCnt, blockCnt);
	mc.addU32Metrics(KU32MetricItem::kSerialGridEmptyCellCnt, emptyCellCnt);
	mc.addU32Metrics(KU32MetricItem::kSerialGridSolidCellCnt, solidCellCnt);
}
// ------------------------------------- KEtPerfStatContext --------------------------------

KEtPerfStatContext::KEtPerfStatContext(KEtWorkbook* wb) : m_wb(wb)
{
}

KEtPerfStatContext::~KEtPerfStatContext()
{
}

void KEtPerfStatContext::incWriteCellValueCount()
{
	++m_wb->mutablePerfStat().writeCellValueCount;
}

void KEtPerfStatContext::incWriteCellFormatCount()
{
	++m_wb->mutablePerfStat().writeCellFormatCount;
}

// ------------------------------------- KCellHistoryCtx --------------------------------

KCellHistoryCtx::KCellHistoryCtx()
{
}

KCellHistoryCtx::~KCellHistoryCtx()
{
}

void KCellHistoryCtx::onCommitCellHsitory()
{
	++m_commitCount;
}

bool KCellHistoryCtx::needCleanHistory()
{
	return m_commitCount > _kso_GetWoEtSettings()->GetCellHistoryMaxPurgeTag();
}

int KCellHistoryCtx::getReserveCount()
{
	return _kso_GetWoEtSettings()->GetCellHistoryReserveCount();
}

}//namespace wo
