#include "etstdafx.h"
#include "webbase/logger.h"
#include "workbook.h"
#include "ksheet_protection_context_impl.h"
#include "protection_context_impl.h"
#include "dbsheet/db_protection_context_impl.h"
#include "appcore/et_appcore_shared_link_sheet.h"
#include "appcore/et_appcore_shared_link.h"
#include "util.h"

namespace wo
{
KsheetProtectionContext::KsheetProtectionContext(KEtWorkbook *workbook, KEtRevisionContext *ctx)
{
    m_pBook = workbook->GetCoreWorkbook()->GetBook();;
    m_spDbProtectionCtx.reset(new KDbProtectionContext(workbook));
    m_spEtProtectionCtx.reset(new KProtectionContext(workbook->GetCoreWorkbook(), workbook->GetCoreWorkbook()->GetWoObject(), ctx));
}

KsheetProtectionContext::~KsheetProtectionContext()
{
}

IEtProtectionCtx* KsheetProtectionContext::getContext(ISheet* pSheet)
{
    if (pSheet == nullptr)
        return nullptr;
    if (pSheet->IsDbSheet() || pSheet->IsDbDashBoardSheet())
        return m_spDbProtectionCtx;
    else
        return m_spEtProtectionCtx;
}

IEtProtectionCtx* KsheetProtectionContext::getContextByIdx(IDX sheetIdx)
{
    // 区域权限中入参（range, sheetIdx) 可能是无效的（可能被操作变化等调整了）
    // 参数无效时返回值和 KProtectionContext 中保持一致
    if (!util::IsValidSheetIdx(m_pBook, sheetIdx))
        return nullptr;

    ks_stdptr<ISheet> spSheet;
    m_pBook->GetSheet(sheetIdx, &spSheet);
    return getContext(spSheet);
}

IEtProtectionCtx* KsheetProtectionContext::getContextById(UINT sheetId)
{
    IDX sheetIdx = INVALIDIDX;
    m_pBook->STSheetToRTSheet(sheetId, &sheetIdx);
    return getContextByIdx(sheetIdx);
}

void KsheetProtectionContext::setupProtectionCache(ISheet *spSheet)
{
    IEtProtectionCtx* pCtx = getContext(spSheet);
    pCtx->setupProtectionCache(spSheet);
}

void KsheetProtectionContext::setHasProtectionCache(bool bl)
{
    m_spEtProtectionCtx->setHasProtectionCache(bl);
}

void KsheetProtectionContext::setupEtContext()
{
    m_spEtProtectionCtx->setupEtContext();
}

void KsheetProtectionContext::clearEtContext()
{
    m_spEtProtectionCtx->clearEtContext();
}

bool KsheetProtectionContext::isAllHidden()
{
    return m_spEtProtectionCtx->isAllHidden();
}

bool KsheetProtectionContext::isCellHidden(ISheet *pSheet, INT32 row, INT32 col)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    return pCtx->isCellHidden(pSheet, row, col);
}

bool KsheetProtectionContext::isRangeHasHidden(const RANGE &rg, bool ignoreShared /* false */)
{
    // todo: 先将range的第一个sheet作为判断依据，后面ksheet支持db与普通sheet跨表选区再处理
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->isRangeHasHidden(rg, ignoreShared);

    return false;
}

bool KsheetProtectionContext::isRangeHasHidden(IKRanges *rgs, bool ignoreShared /* false */)
{
    if (!rgs || (!isBookHasHidden() && !isConnSharedLink()))
    {
        return false;
    }

    UINT count = 0;
    rgs->GetCount(&count);
    for (UINT i = 0; i < count; ++i)
    {
        const RANGE *rg = nullptr;
        rgs->GetItem(i, NULL, &rg);
        if (isRangeHasHidden(*rg, ignoreShared))
        {
            return true;
        }
    }

    return false;
}

bool KsheetProtectionContext::isRangeHasHidden(ISheet *pSheet, const RANGE &rg, bool ignoreShared /* false */)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    return pCtx->isRangeHasHidden(pSheet, rg, ignoreShared);
}

void KsheetProtectionContext::splitVisibleRange(ISheet *pSheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    pCtx->splitVisibleRange(pSheet, rg, vctVisibleRg);
}

void KsheetProtectionContext::splitHiddenRange(ISheet *pSheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    return pCtx->splitHiddenRange(pSheet, rg, hiddenRanges);
}

bool KsheetProtectionContext::isSheetHidden(UINT stId)
{
    IEtProtectionCtx* pCtx = getContextById(stId);
    if (!pCtx)
        return true;
    // 判断是否是sharedlink的状态
	ks_stdptr<ISharedLinkMgr> spSharedLinkMgr;
	m_pBook->GetExtDataItem(edBookSharedLinkManager, (IUnknown**)&spSharedLinkMgr);
    wo::IEtRevisionContext *pRvsCtx = _etcore_GetEtRevisionContext();

    if (!spSharedLinkMgr || !pRvsCtx)
        return pCtx->isSheetHidden(stId);

    PCWSTR sharedId = spSharedLinkMgr->GetConnSharedLink(pRvsCtx->getUser());
    if (!sharedId)
        return pCtx->isSheetHidden(stId);

    ISharedLink* pLink = spSharedLinkMgr->GetItem(sharedId);
    if (pLink)
    {
        switch(pLink->Type())
        {
            case SharedLinkType_DbView:
                break;
            case SharedLinkType_Sheet:
            {
                ISharedLinkSheet* pSharedLinkSheet = static_cast<ISharedLinkSheet*>(pLink);
                if (pSharedLinkSheet && pSharedLinkSheet->GetSheet()->IsDbDashBoardSheet())
                {
                    const IDBSheetProtection* pDbSheetProtection = pSharedLinkSheet->GetDbSheetProtection(stId);
                    return !pDbSheetProtection->GetVisible();
                }
                return !pCtx->isSheetShared(stId);
            }
            default:
                ASSERT(FALSE);
        }
    }
    
    return pCtx->isSheetHidden(stId);
}

bool KsheetProtectionContext::isSheetShared(UINT stId)
{
    IEtProtectionCtx* pCtx = getContextById(stId);
    if (!pCtx)
        return false;
    return pCtx->isSheetShared(stId);
}

bool KsheetProtectionContext::isSheetHasHidden(ISheet *pSheet)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    return pCtx->isSheetHasHidden(pSheet);
}

bool KsheetProtectionContext::isBookHasHidden(IBook *pBook)
{
    return m_spEtProtectionCtx->isBookHasHidden(pBook);
}

bool KsheetProtectionContext::isBookHasHidden()
{
    return m_spEtProtectionCtx->isBookHasHidden();
}

bool KsheetProtectionContext::isBookHasHidden(IKWorkbook *workbook)
{
    return m_spEtProtectionCtx->isBookHasHidden(workbook);
}

bool KsheetProtectionContext::isSheetHasHiddenProperty(ISheet *pSheet)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    return pCtx->isSheetHasHiddenProperty(pSheet);
}

bool KsheetProtectionContext::isBookHasHiddenProperty(IBook *pBook)
{
    return m_spEtProtectionCtx->isBookHasHiddenProperty(pBook);
}

bool KsheetProtectionContext::isRangeHasHiddenProperty(const RANGE &rg, bool checkUnprotected /* = false */)
{
    return m_spEtProtectionCtx->isRangeHasHiddenProperty(rg, checkUnprotected);
}

bool KsheetProtectionContext::isBookHasHiddenProperty()
{
    return m_spEtProtectionCtx->isBookHasHiddenProperty();
}

bool KsheetProtectionContext::isSheetHasReadonly(ISheet *pSheet)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    return pCtx->isSheetHasReadonly(pSheet);
}

bool KsheetProtectionContext::isBookHasReadonly(IBook *pBook)
{
    return m_spEtProtectionCtx->isBookHasReadonly(pBook);
}

bool KsheetProtectionContext::isBookHasReadonly()
{
    return m_spEtProtectionCtx->isBookHasReadonly();
}

bool KsheetProtectionContext::isSheetHasReadonlyProperty(ISheet *pSheet)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    return pCtx->isSheetHasReadonlyProperty(pSheet);
}

bool KsheetProtectionContext::isBookHasReadonlyProperty(IBook *pBook)
{
    return m_spEtProtectionCtx->isBookHasReadonlyProperty(pBook);
}

bool KsheetProtectionContext::isSheetHasEditable(ISheet *pSheet)
{
    IEtProtectionCtx* pCtx = getContext(pSheet);
    return pCtx->isSheetHasEditable(pSheet);
}

bool KsheetProtectionContext::isBookHasReadonlyProperty()
{
    return m_spEtProtectionCtx->isBookHasReadonlyProperty();
}

bool KsheetProtectionContext::isBookHasSheetProtected()
{
    return m_spEtProtectionCtx->isBookHasSheetProtected();
}

bool KsheetProtectionContext::isThisBookHasEditable()
{
    return m_spEtProtectionCtx->isThisBookHasEditable();
}

void KsheetProtectionContext::clearProtectionCache()
{
    m_spEtProtectionCtx->clearProtectionCache();
}

bool KsheetProtectionContext::isCellImageFormula(const_token_vector vecToken)
{
    return m_spEtProtectionCtx->isCellImageFormula(vecToken);
}

bool KsheetProtectionContext::isCellImageFormula(PCWSTR formula)
{
    return m_spEtProtectionCtx->isCellImageFormula(formula);
}

HRESULT KsheetProtectionContext::checkAllowEdit(const RANGE &rg)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkAllowEdit(rg);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkAllowEditWidthLockedCell(const RANGE &rg)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkAllowEditWidthLockedCell(rg);

     return S_OK;
}

bool KsheetProtectionContext::isAllowEdit(const RANGE &rg, ProtectionAccessPerms *out)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->isAllowEdit(rg, out);

    if (out)
        *out = PTAAP_Edit;
    return true;
}

HRESULT KsheetProtectionContext::isOperationAllowedProtected(const RANGE &rg, et_appcore::ActionType acType)
{
    return m_spEtProtectionCtx->isOperationAllowedProtected(rg, acType);
}

bool KsheetProtectionContext::isAllowEdit(IKRanges *rgs, ProtectionAccessPerms *right)
{
    return m_spEtProtectionCtx->isAllowEdit(rgs, right);
}

bool KsheetProtectionContext::isAllowEdit(const std::vector<RANGE> &ranges, ProtectionAccessPerms *right)
{
    return m_spEtProtectionCtx->isAllowEdit(ranges, right);
}

bool KsheetProtectionContext::isRangeAllHidden(const RANGE &rg)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->isRangeAllHidden(rg);

    return false;
}

bool KsheetProtectionContext::isProtectionAreaUnlock(LPCWSTR id)
{
    return m_spEtProtectionCtx->isProtectionAreaUnlock(id);
}

bool KsheetProtectionContext::isAllowedFormula(PCWSTR formula)
{
    return m_spEtProtectionCtx->isAllowedFormula(formula);
}

HRESULT KsheetProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, const_token_ptr token)
{
    IEtProtectionCtx* pCtx = getContextByIdx(iSheet);
    if (pCtx)
        return pCtx->checkFormula(iSheet, row, col, token);

     return S_OK;
}

HRESULT KsheetProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, const_token_vector vecToken)
{
    IEtProtectionCtx* pCtx = getContextByIdx(iSheet);
    if (pCtx)
        return pCtx->checkFormula(iSheet, row, col, vecToken);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkFormula(const RANGE &rg, const_token_vector vecToken)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkFormula(rg, vecToken);

     return S_OK;
}

HRESULT KsheetProtectionContext::checkFormula(IDX iSheet, ROW row, COL col, PCWSTR fmla)
{
    IEtProtectionCtx* pCtx = getContextByIdx(iSheet);
    if (pCtx)
        return pCtx->checkFormula(iSheet, row, col, fmla);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkFormula(const RANGE &rg, PCWSTR fmla)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkFormula(rg, fmla);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkDVFormula(const RANGE &rg, const VALIDATION *dv)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkDVFormula(rg, dv);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkDVFormula(const RANGE &rg, DVValueType dvType, PCWSTR pFormula1, PCWSTR pFormula2)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkDVFormula(rg, dvType, pFormula1, pFormula2);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkDVFormula(const RANGE &rg,
                                           DVValueType dvType,
                                           ITokenVectorInstant *pFormula1,
                                           ITokenVectorInstant *pFormula2)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkDVFormula(rg, dvType, pFormula1, pFormula2);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkDVFormulaInRange(const RANGE &rg)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkDVFormulaInRange(rg);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkCFFormula(const RANGE &appliedRg, ICondFmt *condFmt)
{
    IEtProtectionCtx* pCtx = getContextByIdx(appliedRg.SheetFrom());
    if (pCtx)
        return pCtx->checkCFFormula(appliedRg, condFmt);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkCFFormulaInRange(const RANGE &rg)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkCFFormulaInRange(rg);

    return S_OK;
}

HRESULT KsheetProtectionContext::checkProtectionCopyContent(const RANGE &rg)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkProtectionCopyContent(rg);

    return S_OK;
}

uint32_t KsheetProtectionContext::getProtectionContentKind(const RANGE &rg, bool isAll)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->getProtectionContentKind(rg, isAll);

    return pckNone;
}

HRESULT KsheetProtectionContext::checkProtectionContent(const RANGE &rg, uint32_t *pckKind)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->checkProtectionContent(rg, pckKind);

    return S_OK;
}

void KsheetProtectionContext::setValidRefKind(uint32_t k)
{
    m_spEtProtectionCtx->setValidRefKind(k);
}

bool KsheetProtectionContext::isCompileFormula() const
{
    return m_spEtProtectionCtx->isCompileFormula();
}

void KsheetProtectionContext::setCompileFormula(bool set)
{
    m_spEtProtectionCtx->setCompileFormula(set);
}

uint32_t KsheetProtectionContext::getPasteContentKind() const
{
    return m_spEtProtectionCtx->getPasteContentKind();
}

void KsheetProtectionContext::setPasteContentKind(uint32_t kind)
{
    m_spEtProtectionCtx->setPasteContentKind(kind);
}

bool KsheetProtectionContext::allowDeleteLockedRowCol() const
{
    return m_spEtProtectionCtx->allowDeleteLockedRowCol();
}

void KsheetProtectionContext::setAllowDeleteLockedRowCol(bool isAllowed)
{
    m_spEtProtectionCtx->setAllowDeleteLockedRowCol(isAllowed);
}

int KsheetProtectionContext::currentCommand() const
{
    return m_spEtProtectionCtx->currentCommand();
}

void KsheetProtectionContext::setCurrentCommand(int cmd)
{
    m_spEtProtectionCtx->setCurrentCommand(cmd);
}

bool KsheetProtectionContext::getAutoPassword(PCWSTR srcUUID, ks_wstring *outPwd)
{
    return m_spEtProtectionCtx->getAutoPassword(srcUUID, outPwd);
}

bool KsheetProtectionContext::needCheckInsertDeleteHiddenRange(const RANGE& rg)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->needCheckInsertDeleteHiddenRange(rg);

    return true;
}

bool KsheetProtectionContext::canRefColProtect(const RANGE& rg)
{
    IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->canRefColProtect(rg);

    return true;
}

bool KsheetProtectionContext::hasHiddenColProtect(ISheet* spSheet, const RANGE& rg)
{
    IEtProtectionCtx* pCtx = getContext(spSheet);
    if (pCtx)
        return pCtx->hasHiddenColProtect(spSheet, rg);

    return false;
}

bool KsheetProtectionContext::isBookHasColProtect()
{
    return m_spEtProtectionCtx->isBookHasColProtect();
}

void KsheetProtectionContext::splitVisibleRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &vctVisibleRg)
{
    IEtProtectionCtx* pCtx = getContext(isheet);
    if (pCtx)
        pCtx->splitVisibleRangeForClear(isheet, rg, vctVisibleRg);
    else
        vctVisibleRg.push_back(rg);
}

void KsheetProtectionContext::splitHiddenRangeForClear(ISheet *isheet, const RANGE &rg, std::vector<RANGE> &hiddenRanges)
{
    IEtProtectionCtx* pCtx = getContext(isheet);
    if (pCtx)
        pCtx->splitHiddenRangeForClear(isheet, rg, hiddenRanges);
}

void KsheetProtectionContext::onUserColPermsChanged(LPCWSTR userId)
{
    m_spEtProtectionCtx->onUserColPermsChanged(userId);
}

void KsheetProtectionContext::updateUserColPermsStatus(LPCWSTR excludeId)
{
    m_spEtProtectionCtx->updateUserColPermsStatus(excludeId);
}

void KsheetProtectionContext::markAllUsersColPermsChanged(LPCWSTR excludeId)
{
    m_spEtProtectionCtx->markAllUsersColPermsChanged(excludeId);
}

bool KsheetProtectionContext::isAllUsersColPermsChanged()
{
    return m_spEtProtectionCtx->isAllUsersColPermsChanged();
}

bool KsheetProtectionContext::isConnSharedLink()
{
    return m_spEtProtectionCtx->isConnSharedLink();
}

IEtProtectionCtx::ProtectionType KsheetProtectionContext::getPasteProtectionType() const
{
    return m_spEtProtectionCtx->getPasteProtectionType();
}

void KsheetProtectionContext::setPasteProtectionType(IEtProtectionCtx::ProtectionType type)
{
    m_spEtProtectionCtx->setPasteProtectionType(type);
}

bool KsheetProtectionContext::isUserRangeInvisible(const RANGE &rg)
{
	IEtProtectionCtx* pCtx = getContextByIdx(rg.SheetFrom());
    if (pCtx)
        return pCtx->isUserRangeInvisible(rg);

    return false;
}

bool KsheetProtectionContext::isCanReadWholeBook()
{
	return m_spEtProtectionCtx->isCanReadWholeBook();
}

bool KsheetProtectionContext::isCanEditWholeBook()
{
	return m_spEtProtectionCtx->isCanEditWholeBook();
}

} // wo