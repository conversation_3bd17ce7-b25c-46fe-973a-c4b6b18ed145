﻿#ifndef __WEBET_UTILS_RENAME_UTILS_H__
#define __WEBET_UTILS_RENAME_UTILS_H__

// TODO 可以把这个方法放到applogic里，复用worksheets的validateSheetName，然后worksheets再暴露个接口供外面调用。
// 进程内将一个表格文件导入(copy)另一个表格文件时，其表公式的跨Sheet引用有BUG，导入后其单元格和引用区域无法正确建立依赖关系
// 目前有个办法，就是导入前处理好文件重名，导入后再次强制触发一次重命名，让其建立正确的依赖关系。
class DBSheetRenameHelper
{
    public:
        // 将一个DB文件导入另一个DB文件前，处理一下sheet名冲突，保证导入后的sheet都按现有的重名规则处理
        static void RenameConflictDBSheetName(etoldapi::Worksheets* pTgtWorksheets, etoldapi::Worksheets* pSrcWorksheets, const std::unordered_set<UINT>& importSheetIds = {});
        // 强制重命名一定区域内的sheet
        // 导入前处理重名只能保证新公式的书写正确，并不能建立正确的引用关系，这里再次通过重命名来触发引用依赖的建立
        static void ForceRenameDBSheets(etoldapi::Worksheets* pWorksheets, int startIdx, int endIdx);
    private:
        // 通过DB重命名规则改造得到的新方法，与老方法不一样的是sheet名集合是作为参数传入的，该集合是动态变化的
        static HRESULT ValidateSheetName(std::vector<ks_wstring>& vSheetNames, SHEETTYPE st, ks_wstring& pcwszName, IDX* pSheetIdx = nullptr);
        static HRESULT GetNamePreFix(SHEETTYPE st, const WCHAR* pwszSheetName, BSTR* pbstrSheetNamePreFix);
        static HRESULT GetSheetNameIdx(std::vector<ks_wstring>& vSheetNames, ks_wstring& pcwszName, IDX& index);
        static void CollectSheetNames(etoldapi::Worksheets* pWorksheets, std::vector<ks_wstring>& names, const std::unordered_set<UINT>& importSheetIds = {});
    
};
#endif