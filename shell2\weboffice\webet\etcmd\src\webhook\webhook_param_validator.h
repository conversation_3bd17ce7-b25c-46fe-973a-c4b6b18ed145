﻿#ifndef __WEBET_WEBHOOK_PARAM_VALIDATOR_H__
#define __WEBET_WEBHOOK_PARAM_VALIDATOR_H__

#include "appcore/et_appcore_webhook.h"
#include "dbsheet/et_dbsheet_common_helper.h"

class ParamsValidator
{
public:
    ParamsValidator(IBook* pBook): m_pBook(pBook) {}
    virtual ~ParamsValidator() {}
    virtual bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) { return false; }
    void WriteHookInValidResult(PC<PERSON><PERSON> hookId, PC<PERSON>TR message, binary_wo::BinWriter &res);
    void WriteHookInValidSheetIds(PCWS<PERSON> hookId, PC<PERSON><PERSON> message, const std::set<UINT>& sheetIds, binary_wo::BinWriter &res);
    void WriteHookInValidResult(PCWSTR hookId, PCWSTR uuid, int code, PC<PERSON><PERSON> message, binary_wo::BinWriter &res);
protected:
    bool validateParam(PCSTR paramName, PCWSTR hookId, binary_wo::VarObj &obj, binary_wo::BinWriter &res);

protected:
    IBook* m_pBook;
};

class WebhookParamsValidator: public ParamsValidator
{
public:
    WebhookParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class UserIdValidator: public ParamsValidator
{
public:
    UserIdValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class SheetIdValidator: public ParamsValidator
{
public:
    SheetIdValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtSheetIdValidator: public ParamsValidator
{
public:
    EtSheetIdValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class SheetIdsValidator: public ParamsValidator
{
public:
    SheetIdsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
    void SetCheckDbsheet(bool bCheckDbsheet) { m_bCheckDbsheet = bCheckDbsheet; }
private:
    bool m_bCheckDbsheet = true;
};

class ViewIdValidator: public ParamsValidator
{
public:
    ViewIdValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class FieldIdValidator: public ParamsValidator
{
public:
    FieldIdValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class FieldIdsValidator: public ParamsValidator
{
public:
    FieldIdsValidator(IBook* pBook, bool canEmpty): ParamsValidator(pBook), m_canEmpty(canEmpty) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
private:
    bool m_canEmpty = false;
};

class ActionFieldIdsValidator: public ParamsValidator
{
public:
    ActionFieldIdsValidator(IBook* pBook, bool canEmpty): ParamsValidator(pBook), m_canEmpty(canEmpty) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
private:
    bool m_canEmpty = false;
};

class RecordIdsValidator: public ParamsValidator
{
public:
    RecordIdsValidator(IBook* pBook, bool canEmpty = false): ParamsValidator(pBook), m_canEmpty(canEmpty) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
private:
    bool m_canEmpty = false;
};

class FilterValidator: public ParamsValidator
{
public:
    FilterValidator(IBook* pBook, bool canEmpty): ParamsValidator(pBook), m_canEmpty(canEmpty) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
protected:
    HRESULT getFilter(PCWSTR hookId, binary_wo::VarObj& filter, wo::DBSheetCommonHelper& commonHelper, binary_wo::BinWriter &res, IDbExtraFilter** ppFilter);

protected:
    ks_stdptr<IDBSheetOp> m_spDbSheetOp;
private:
    bool m_canEmpty = false;
};

class FormulaChangeValidator: public ParamsValidator
{
public:
    FormulaChangeValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtRangeValidator: public ParamsValidator
{
public:
    EtRangeValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
private:
    bool ValidateRange(binary_wo::VarObj &range, PCWSTR hookId, binary_wo::BinWriter &res, IWebhookData* pData);
};

class EtRowColIndexArrayValidator: public ParamsValidator
{
public:
    EtRowColIndexArrayValidator(IBook* pBook, PCSTR pArrayName): ParamsValidator(pBook), m_pArrayName(pArrayName) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
private:
    PCSTR m_pArrayName;
};

class SkipAfterMatchCreateAndFillValidator: public ParamsValidator
{
public:
    SkipAfterMatchCreateAndFillValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};
// =================================================================================

class UpdateSheetDescParamsValidator: public ParamsValidator
{
public:
    UpdateSheetDescParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class UpdateSheetIconParamsValidator: public ParamsValidator
{
public:
    UpdateSheetIconParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class RenameSheetParamsValidator: public ParamsValidator
{
public:
    RenameSheetParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class RemoveSheetParamsValidator: public ParamsValidator
{
public:
    RemoveSheetParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class CreateViewParamsValidator: public ParamsValidator
{
public:
    CreateViewParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class RenameViewParamsValidator: public ParamsValidator
{
public:
    RenameViewParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class RemoveViewParamsValidator: public ParamsValidator
{
public:
    RemoveViewParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class CreateFieldParamsValidator: public ParamsValidator
{
public:
    CreateFieldParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class UpdateFieldParamsValidator: public ParamsValidator
{
public:
    UpdateFieldParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class UpdatePrimaryFieldParamsValidator: public ParamsValidator
{
public:
    UpdatePrimaryFieldParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};


class RemoveFieldParamsValidator: public ParamsValidator
{
public:
    RemoveFieldParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class CreateRecordParamsValidator: public ParamsValidator
{
public:
    CreateRecordParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class RemoveRecordParamsValidator: public ParamsValidator
{
public:
    RemoveRecordParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class UpdateRecordsParentParamsValidator: public ParamsValidator
{
public:
    UpdateRecordsParentParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};


// 整表修改
class UpdateSheetParamsValidator: public ParamsValidator
{
public:
    UpdateSheetParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

// 某些单元格修改
class UpdateCellsParamsValidator: public ParamsValidator
{
public:
    UpdateCellsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

// 整行修改
class UpdateFieldCellsParamsValidator: public ParamsValidator
{
public:
    UpdateFieldCellsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

// 整列修改
class UpdateRecordCellsParamsValidator: public ParamsValidator
{
public:
    UpdateRecordCellsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

// 满足筛选条件的整行修改
class UpdateRecordsParamsValidator: public ParamsValidator
{
public:
    UpdateRecordsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

// 多个表的全部修改
class UpdateSheetsAllChangeParamsValidator: public ParamsValidator
{
public:
    UpdateSheetsAllChangeParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class CreateAndFillInRecordParamsValidator: public ParamsValidator
{
public:
    CreateAndFillInRecordParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};
// ==================================et====================================
class EtUpdateSheetParamsValidator: public ParamsValidator
{
public:
    EtUpdateSheetParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};


class EtUpdateRangesParamsValidator: public ParamsValidator
{
public:
    EtUpdateRangesParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtUpdateChartsParamsValidator: public ParamsValidator
{
public:
    EtUpdateChartsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
    bool isChartExist(ISheet* isheet, const WCHAR* uuid);
};

class EtRemoveSheetParamsValidator: public ParamsValidator
{
public:
    EtRemoveSheetParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtInsertRowsParamsValidator: public ParamsValidator
{
public:
    EtInsertRowsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtRemoveRowsParamsValidator: public ParamsValidator
{
public:
    EtRemoveRowsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtInsertColsParamsValidator: public ParamsValidator
{
public:
    EtInsertColsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtRemoveColsParamsValidator: public ParamsValidator
{
public:
    EtRemoveColsParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtUpdateRangeParamsValidator: public ParamsValidator
{
public:
    EtUpdateRangeParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

class EtUpdateSheetsAllChangeParamsValidator: public ParamsValidator
{
public:
    EtUpdateSheetsAllChangeParamsValidator(IBook* pBook): ParamsValidator(pBook) {}
    bool ValidateParams(binary_wo::VarObj &hook, binary_wo::BinWriter &res, IWebhookData* pData) override;
};

#endif // __WEBET_WEBHOOK_PARAM_VALIDATOR_H__
