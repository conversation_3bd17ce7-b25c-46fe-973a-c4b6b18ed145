﻿#ifndef __MERGE_RANGE_HELPER_H__
#define __MERGE_RANGE_HELPER_H__

namespace wo
{

class RectContainer
{
public:
    RectContainer() = default;
    void add(const RANGE& rg);
    std::vector<RANGE> get();
protected:
    bool tryMerge(const RANGE& rg);
private:
    std::vector<RANGE> m_rgs;
};

class MergeRangeHelper
{
public:
    explicit MergeRangeHelper(std::vector<RANGE>& ranges) : m_rgs(ranges) {};
    void executeMerge();
    void serialize(const char* key, ISerialAcceptor* acpt);
private:
    std::vector<RANGE> m_rgs;
    RectContainer m_rc;
};

}

#endif //__MERGE_RANGE_HELPER_H__