#include "etstdafx.h"
#include "supbooks_helper.h"
#include "workbook.h"
#include "et_revision_context_impl.h"

namespace wo
{
void getSupEditLinks(KEtWorkbook* wwb, ISupEditLinks** ppEditLinks)
{
	IBook *pBook = wwb->GetCoreWorkbook()->GetBook();
	VS(_appcore_CreateObject(CLSID_KSupEditLinks, IID_ISupEditLinks, (void **)ppEditLinks));
	(*ppEditLinks)->Initialize(pBook);
}

PCWSTR SLIS2Str(SUP_LINKS_INFO_STATUS slis)
{
	switch (slis)
	{
	case SLIS_OK:
		return __X("OK");
	case SLIS_Unknown:
		return __X("Unknown");
	case SLIS_NotApplicable:
		return __X("NotApplicable");
	case SLIS_SourceIsOpen:
		return __X("SourceIsOpen");
	case SLIS_E_SourceNotFound:
		return __X("SourceNotFound");
	case SLIS_E_FileIdMissing:
		return __X("FileIdMissing");
	case SLIS_E_AccessDenied:
		return __X("AccessDenied");
	case SLIS_E_WorksheetNotFound:
		return __X("WorksheetNotFound");
	case SLIS_E_UndefinedOrNonRectangularName:
		return __X("UndefinedOrNonRectangularName");
	case SLIS_E_StatusIndeterminate:
		return __X("StatusIndeterminate");
	case SLIS_W_ValuesNotUpdated:
		return __X("ValuesNotUpdated");
	case SLIS_W_SourceNotRecalculated:
		return __X("SourceNotRecalculated");
	case SLIS_W_OpenSourceToUpdateValues:
		return __X("OpenSourceToUpdateValues");
	case SLIS_E_NotAllowReferSecurityDocument:
	case SLIS_E_SecureDoc:
		return __X("NotAllowReferSecurityDocument");
	case SLIS_E_FileProtectedWidthHiddenRange:
		return __X("FileProtectedWidthHiddenRange");
	case SLIS_E_RelationNotExists:
		return __X("RelationNotExists");
	case SLIS_E_FileTooLarge:
		return __X("FileTooLarge");
	case SLIS_E_CircularReference:
		return __X("CircularReference");
	case SLIS_E_InvalidRelation:
		return __X("InvalidRelation");
	case SLIS_E_InvalidURL:
		return __X("InvalidURL");
	case SLIS_E_Internal:
		return __X("ServerInternal");
	case SLIS_E_InvalidOfficeType:
		return __X("InvalidOfficeType");
	case SLIS_E_RelateConflict:
		return __X("RelateConflict");
	case SLIS_E_NeePassword:
		return __X("NeedPassword");
	case SLIS_E_ContentPermission:
		return __X("ContentPermission");	
	default:
		ASSERT(FALSE);
		return __X("");
	}
}

void getSupbookStatus(ISupEditLinks* pEditLinks, BSTR bookFullName, KEtRevisionContext* pCtx, binary_wo::VarObj& status)
{
	SUP_LINKS_INFO_STATUS slis = SLIS_Unknown;
	IDX iBook = -1;
	if (S_OK != pEditLinks->GetStatus(bookFullName, &slis, &iBook))
	{
		status.add_field_str("remove", bookFullName);
		return;
	}

	status.add_field_int16("bookId", iBook);
	status.add_field_str("status", SLIS2Str(slis));

	if (slis != SLIS_E_WorksheetNotFound)
		return;

	binary_wo::VarObj sheetsInof = status.add_field_struct("unmatchSheet");
	pCtx->getSupBookCtx()->getUnmatchSheetInfo(bookFullName, sheetsInof);
}



} // namespace wo