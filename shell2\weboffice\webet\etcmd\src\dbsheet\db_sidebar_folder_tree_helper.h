﻿#ifndef __WEBET_DATABASE_SHEET_DB_SIDEBAR_FOLDER_TREE_HELPER_H__
#define __WEBET_DATABASE_SHEET_DB_SIDEBAR_FOLDER_TREE_HELPER_H__

#include "appcore/et_appcore_dbsheet.h"

namespace wo
{
class KEtWorkbook;
class DbSidebarFolderTreeHelper
{
public:
    DbSidebarFolderTreeHelper(KEtWorkbook* pWorkbook);

protected:
    IBook* m_pBook;
};

class DbSidebarFolderTreeAddHelper : public DbSidebarFolderTreeHelper
{
public:
    DbSidebarFolderTreeAddHelper(KEtWorkbook*);

    HRESULT MoveSheet(binary_wo::VarObj param, UINT stId);
    HRESULT MoveSheetPart(EtDbId targetId, UINT stId, DbSidebarFolderTreeRelPos relPos, EtDbId relId);
};

class DbSidebarFolderTreeCopyHelper : public DbSidebarFolderTreeHelper
{
public:
    DbSidebarFolderTreeCopyHelper(KEtWorkbook*);

    HRESULT MoveSheet(UINT stId, UINT relStId);
};
} // namespace wo

#endif // __WEBET_DATABASE_SHEET_DB_SIDEBAR_FOLDER_TREE_HELPER_H__