﻿#ifndef __WEBET_ET_BINVAR_SPEC_H__
#define __WEBET_ET_BINVAR_SPEC_H__

#include "webbase/binvariant/binvarobj.h"
#include "etcore/et_core_sheet.h"


namespace wo
{

inline bool DecodeInt_s(binary_wo::VarObj var, WebName numFieldName, int& val)
{
	binary_wo::VarObj x = var.get_s(numFieldName);
	if (x.type() != binary_wo::typeInvalid)
	{
		val = x.value_int32();
		return true;
	}
	else
	{
		return false;
	}
}

inline IDX GetSheetIdx(IBook* pBook, binary_wo::VarObj var)
{
	IDX sheetIdx = INVALIDIDX;
	if (var.has("sheetStId"))
	{
		UINT32 stId = var.field_uint32("sheetStId");
		pBook->STSheetToRTSheet(stId, &sheetIdx);
	}
	else if (var.has("sheetIdx"))
	{
		sheetIdx = var.field_int32("sheetIdx");
	}
	return sheetIdx;
}

inline RANGE ReadRangeInl(BMP_PTR bmp, binary_wo::VarObj var)
{
	RANGE rg(bmp);
	rg.SetSheetFromTo(var.field_int32("sheetIdx"));
	rg.SetRowFromTo(
		var.field_int32("rowFrom"), var.field_int32("rowTo"));
	rg.SetColFromTo(
		var.field_int32("colFrom"), var.field_int32("colTo"));
	return rg;
}

inline RANGE ReadRangeInl(BMP_PTR bmp, IDX sheetIdx, binary_wo::VarObj var)
{
	RANGE rg(bmp);
	rg.SetSheetFromTo(sheetIdx);
	rg.SetRowFromTo(
		var.field_int32("rowFrom"), var.field_int32("rowTo"));
	rg.SetColFromTo(
		var.field_int32("colFrom"), var.field_int32("colTo"));
	return rg;
}

inline void ReadRangesInl(BMP_PTR bmp, IDX sheetIdx, binary_wo::VarObj var, std::vector<RANGE> &vec, WebName name = "rgs")
{
	binary_wo::VarObj rgs = var.get_s(name);
	ASSERT(rgs.type() == binary_wo::typeArray);
	for (int i = 0; i < rgs.arrayLength_s(); i++)
	{
		binary_wo::VarObj item = rgs.at_s(i);
		RANGE rg = ReadRangeInl(bmp, sheetIdx, item);
		if (rg.IsValid())
			vec.push_back(rg);
	}
}

inline void ReadRangesInl(BMP_PTR bmp, binary_wo::VarObj var, std::vector<RANGE> &vec, WebName name = "rgs")
{
	binary_wo::VarObj rgs = var.get_s(name);
	
	ASSERT(rgs.type() == binary_wo::typeArray);
	for (int i = 0; i < rgs.arrayLength_s(); i++)
	{
		binary_wo::VarObj item = rgs.at_s(i);
		RANGE rg = ReadRangeInl(bmp, item);
		if (rg.IsValid())
			vec.push_back(rg);
	}
}

}//namespace wo

#endif //__WEBET_ET_BINVAR_SPEC_H__
