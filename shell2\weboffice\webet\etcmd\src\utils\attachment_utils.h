﻿#ifndef __WEBET_UTILS_ATTACHMENT_H__
#define __WEBET_UTILS_ATTACHMENT_H__

namespace wo
{
	namespace util
	{
		// 获取本地附件的id
		PCWSTR getAttachmentId(PCWSTR path);
		// 获取本地或云文档附件的 id
		PCWSTR getFileId(PCWSTR linkPath, PCWSTR* source = nullptr);
		// 获取本地附件的url
		ks_wstring generateFakeAttachmentUrl(PCWSTR id, PCWSTR prefix = nullptr);
		// 解析kw:annex 协议中的id
		ks_wstring getAttachmentIdByAnnex(PCWSTR path, BOOL& isVideo);

	} // namespace util
} // namespace wo

#endif
