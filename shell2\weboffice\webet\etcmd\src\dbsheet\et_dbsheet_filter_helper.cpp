#include "etstdafx.h"
#include "et_dbsheet_utils.h"
#include "et_dbsheet_filter_helper.h"
#include "appcore/et_appcore_dbsheet.h"
#include "ettools/ettools_encode_decoder.h"
#include "helpers/varobject_helper.h"
#include "dbsheet/et_dbsheet_provider.h"

using binary_wo::VarObj;

namespace
{

HRESULT CreateNullVal(IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueNull> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueNull, IID_IDbFcValueNull, (void**)&spVal));
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateAnyVal(IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueAny> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueAny, IID_IDbFcValueAny, (void**)&spVal));
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterTextVal(VarObj vVal, bool bQueryView, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueText> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueText, IID_IDbFcValueText, (void**)&spVal));
	PCWSTR str = vVal.field_str("value");
	ks_wstring newStr;
	if (bQueryView)
	{
		wo::DbSheet::AddWildChar(str, newStr);
		if (newStr.size() != 0)
			str = newStr.c_str();
	}
	spVal->SetValue(str);
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterNumVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueNum> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueNum, IID_IDbFcValueNum, (void**)&spVal));
	spVal->SetValue(vVal.field_double("value"));
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterDateVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueDate> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueDate, IID_IDbFcValueDate, (void**)&spVal));

	KDbFcValueDateInfo date;
	if (vVal.has("year"))
	{
		date.year = vVal.field_int32("year");
	}
    if (vVal.has("mon"))
	{
		date.mon = vVal.field_int32("mon");
	}
    if (vVal.has("mday"))
	{
		date.mday = vVal.field_int32("mday");
	}
    if (vVal.has("hour"))
	{
		date.hour = vVal.field_int32("hour");
	}
    if (vVal.has("min"))
	{
		date.min = vVal.field_int32("min");
	}
    if (vVal.has("sec"))
	{
		date.sec = vVal.field_int32("sec");
	}
	spVal->SetValue(date);
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterTimeVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueTime> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueTime, IID_IDbFcValueTime, (void**)&spVal));

	KDbFcValueTimeInfo time;
	if (vVal.has("hour"))
	{
		time.hour = vVal.field_int32("hour");
	}
	if (vVal.has("min"))
	{
		time.min = vVal.field_int32("min");
	}
	if (vVal.has("sec"))
	{
		time.sec = vVal.field_int32("sec");
	}
	spVal->SetValue(time);
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterSelectItemVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueSelectItem> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueSelectItem, IID_IDbFcValueSelectItem, (void**)&spVal));

	EtDbId selectItemId = INV_EtDbId;
	VS(_appcore_GainDbSheetContext()->DecodeEtDbId(vVal.field_str("value"), &selectItemId));
	spVal->SetSelectItemId(nullptr, selectItemId);
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterContactVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueContact> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueContact, IID_IDbFcValueContact, (void**)&spVal));

	spVal->SetValue(vVal.field_str("value"));
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterLinkVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueLink> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueLink, IID_IDbFcValueLink, (void**)&spVal));

	if (vVal.type() == binary_wo::typeString)
	{
		spVal->SetValue(INV_EtDbId);
		spVal->SetContent(__X(""));
	}
	else
	{
		VarObj vRecId = vVal.get("value");
		if (vRecId.type() == binary_wo::typeString)
		{
			EtDbId recId = INV_EtDbId;
			VS(_appcore_GainDbSheetContext()->DecodeEtDbId(vRecId.value_str(), &recId));
			spVal->SetValue(recId);
		}
		else
		{
			spVal->SetValue(vVal.field_int32("value"));
		}
		spVal->SetContent(vVal.field_str("content"));
	}
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterCheckboxVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueCheckbox> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueCheckbox, IID_IDbFcValueCheckbox, (void**)&spVal));
	spVal->SetValue(vVal.field_bool("value"));
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterDynamicSimpleVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueDynamicSimple> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueDynamicSimple, IID_IDbFcValueDynamicSimple, (void**)&spVal));

	KDbFcDynamicType tp = DBFCDT_Today;
	HRESULT hr = _appcore_GainEncodeDecoder()->DecodeKDbFcDynamicType(vVal.field_str("dynamicType"), &tp);
	if(FAILED(hr))
		return hr;

	spVal->SetDynamicType(tp);
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterDynamicThresholdVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueDynamicThreshold> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueDynamicThreshold, IID_IDbFcValueDynamicThreshold, (void**)&spVal));
	KDbFcDynamicType tp = DBFCDT_Today;
	HRESULT hr = _appcore_GainEncodeDecoder()->DecodeKDbFcDynamicType(vVal.field_str("dynamicType"), &tp);
	if(FAILED(hr))
		return hr;

	spVal->SetDynamicType(tp);
	spVal->SetThreshold(vVal.field_double("threshold"));
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateFilterDepartmentVal(VarObj vVal, IDbFcValueBase** ppVal)
{
	ks_stdptr<IDbFcValueDepartment> spVal;
	VS(_appcore_CreateObject(CLSID_KDbFcValueDepartment, IID_IDbFcValueDepartment, (void**)&spVal));
	spVal->SetId(vVal.field_str("deptId"));
	spVal->SetValue(vVal.field_str("value"));
	*ppVal = spVal.detach();
	return S_OK;
}

HRESULT CreateDbFilterVal(VarObj vVal, IDbFcValueBase** ppVal, bool bSvrApi, bool bQueryView)
{
	// server api 使用简化的方式构造筛选条件. 日后筛选条件支持解析type后, 此处应同步修改
	if (binary_wo::typeString == vVal.type())
	{
		ks_stdptr<IDbFcValueText> spVal;
		VS(_appcore_CreateObject(CLSID_KDbFcValueText, IID_IDbFcValueText, (void**)&spVal));
		spVal->SetValue(vVal.value_str());
		*ppVal = spVal.detach();
		return S_OK;
	}

	KDbFcValueType valueType = DBFCVT_Text;
	VAR_OBJ_EXPECT_STRING(vVal, "type");
	HRESULT hr = _appcore_GainEncodeDecoder()->DecodeKDbFcValueType(vVal.field_str("type"), &valueType);
	if (FAILED(hr))
		return hr;

	switch (valueType)
	{
	case DBFCVT_Null:
		hr = CreateNullVal(ppVal);
		break;
	case DBFCVT_Any:
		hr =  CreateAnyVal(ppVal);
		break;
    case DBFCVT_Text:
		hr =  CreateFilterTextVal(vVal, bQueryView, ppVal);
		break;
    case DBFCVT_Num:
		hr =  CreateFilterNumVal(vVal, ppVal);
		break;
    case DBFCVT_Date:
		hr =  CreateFilterDateVal(vVal, ppVal);
		break;
	case DBFCVT_Time:
		hr =  CreateFilterTimeVal(vVal, ppVal);
		break;
	case DBFCVT_SelectItem:
		hr =  CreateFilterSelectItemVal(vVal, ppVal);
		break;
    case DBFCVT_Contact:
		hr =  CreateFilterContactVal(vVal, ppVal);
		break;
	case DBFCVT_Link:
		hr =  CreateFilterLinkVal(vVal, ppVal);
		break;
    case DBFCVT_Checkbox:
		hr =  CreateFilterCheckboxVal(vVal, ppVal);
		break;
    case DBFCVT_DynamicSimple:
		hr =  CreateFilterDynamicSimpleVal(vVal, ppVal);
		break;
    case DBFCVT_DynamicThreshold:
		hr =  CreateFilterDynamicThresholdVal(vVal, ppVal);
		break;
	case DBFCVT_Department:
		hr = CreateFilterDepartmentVal(vVal, ppVal);
		break;
	default:
		return E_INVALIDARG;
	}

	if (vVal.has("id") && (*ppVal))
	{
		VAR_OBJ_EXPECT_STRING(vVal, "id");
		EtDbId recId = INV_EtDbId;
		_appcore_GainDbSheetContext()->DecodeEtDbId(vVal.field_str("id"), &recId);
		(*ppVal)->SetValueID(recId);
	}
	return hr;
}

} // namespace

namespace wo 
{

HRESULT CreateDbFilterCriteria(VarObj vCriteria, IBook* pBook, IDbFilterCriteria** ppVal, bool bSvrApi, bool bQueryView)
{
	ks_stdptr<IDbFilterCriteria> spCriteria;
	VS(_appcore_CreateObject(CLSID_KDbFilterCriteria, IID_IDbFilterCriteria, (void**)&spCriteria));
	std::unique_ptr<KDbDataProvider> upProvider = std::make_unique<KDbDataProvider>(pBook);
	spCriteria->Init(upProvider.release());
	KDbFilterCriteriaOpType op = DBFCOT_Null;
	VAR_OBJ_EXPECT_STRING(vCriteria, "op");
	HRESULT hr = _appcore_GainEncodeDecoder()->DecodeKDbFilterCriteriaOpType(vCriteria.field_str("op"), &op);
	if(FAILED(hr))
		return hr;

	spCriteria->SetCriteriaOp(op);
	VAR_OBJ_EXPECT_ARRAY(vCriteria, "values");
	VarObj vValues = vCriteria.get("values");
	for (int32 i = 0, cnt = vValues.arrayLength(); i < cnt; ++i)
	{
		ks_stdptr<IDbFcValueBase> spVal;
		hr = CreateDbFilterVal(vValues.at(i), &spVal, bSvrApi, bQueryView);
		if(FAILED(hr))
			return hr;
		
		hr = spCriteria->AddValue(spVal);
		if(FAILED(hr))
			return hr;
	}

	*ppVal = spCriteria.detach();
	return S_OK;
}

HRESULT SetComposeFilter(IDBSheetView* pView, const binary_wo::VarObj& param)
{
	auto mode = [&](const binary_wo::VarObj& param, KDbFilterOpType &op) -> HRESULT
	{
		if (param.has("mode"))
		{
			if (FAILED(_appcore_GainEncodeDecoder()->DecodeKDbFilterOpType(param.field_str("mode"), &op)))
				return E_DBSHEET_FILTER_INVALID;
		}
		return S_OK;
	};

	auto builder = [&](const binary_wo::VarObj& param, IDbFilter* pFilter) -> HRESULT
	{
		KDbFilterOpType op = DBFOT_And;
		if (FAILED(mode(param, op)))
			return E_DBSHEET_FILTER_INVALID;

		if (op != DBFOT_And && op != DBFOT_Or)
			return E_DBSHEET_FILTER_INVALID;

		if (param.has("criteria"))
		{
			if (param.has("filters"))
				return E_DBSHEET_FILTER_INVALID;

			VarObj criteria = param.get_s("criteria");
			if (binary_wo::typeArray != criteria.type())
				return E_DBSHEET_FILTER_INVALID;

			if (FAILED(pFilter->SetOperator(op)))
				return E_DBSHEET_FILTER_INVALID;

			IDbFieldsManager* pFieldsMgr = pView->GetSheetOp()->GetFieldsManager();
			for (int32 i = 0, cnt = criteria.arrayLength(); i < cnt; ++i)
			{
				VarObj criterion = criteria.at(i);
				if (binary_wo::typeStruct != criterion.type())
					return E_DBSHEET_FILTER_INVALID;

				if (false == criterion.has("fieldId"))
					return E_DBSHEET_FILTER_INVALID;

				ks_stdptr<IDbFieldFilter> spFieldFilter;
				if (FAILED(pFilter->AddFilter(DbSheet::GetEtDbId(criterion, "fieldId"), &spFieldFilter)))
					return E_DBSHEET_FILTER_INVALID;

				ks_stdptr<IDbFilterCriteria> spCriteria;
				if (FAILED(CreateDbFilterCriteria(criterion, pView->GetSheetOp()->GetBook(), &spCriteria, false, true)))
					return E_DBSHEET_FILTER_INVALID;

				if (FAILED(spFieldFilter->SetCriteria(spCriteria)))
					return E_DBSHEET_FILTER_INVALID;
			}
			return S_OK;
		}
		return S_FALSE;
	};

	ks_stdptr<IDbFilter> compose = pView->GetMutableFilter();
	compose->ClearFilter();
	HRESULT hr = builder(param, compose);
	if (hr != S_FALSE)
		return hr;

	if (param.has("filters"))
	{
		if (param.has("criteria"))
			return E_DBSHEET_FILTER_INVALID;

		KDbFilterOpType op = DBFOT_And;
		if (FAILED(mode(param, op)))
			return E_DBSHEET_FILTER_INVALID;

		if (op != DBFOT_And)
			return E_DBSHEET_FILTER_INVALID;

		VarObj filters = param.get_s("filters");
		if (binary_wo::typeArray != filters.type())
			return E_DBSHEET_FILTER_INVALID;

		for (UINT i = 0, cnt = filters.arrayLength(); i < cnt; ++i)
		{
			ks_stdptr<IDbFilter> filter;
			compose->CreateFilter(&filter, TRUE);
			hr = builder(filters.at_s(i), filter);
			if (FAILED(hr))
				return hr;

			compose = filter;
		}
	}
	return hr;
}

}//namespace wo 