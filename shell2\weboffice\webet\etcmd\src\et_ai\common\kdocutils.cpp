﻿#include "kdocutils.h"
#include <cryptopp/filters.h>
#include <cryptopp/sha.h>
#include <cryptopp/hmac.h>
#include <zlib.h>
#include "kxshare/kxshare.h"
#include <kxshare/kaescodec.h>
#include <kxshare/krsacodec.h>

namespace KDocUtils
{
	bool doCompress(const QByteArray& src, QByteArray& dest)
	{
		const int BUFSIZE = 128 * 1024;
		char tmpBuf[BUFSIZE];
		int ret;

		dest.clear();

		z_stream strm;
		strm.zalloc = Z_NULL;
		strm.zfree = Z_NULL;
		strm.opaque = Z_NULL;
		strm.next_in = reinterpret_cast<uchar*>(const_cast<char*>(src.data()));
		strm.avail_in = src.length();
		strm.next_out = reinterpret_cast<uchar*>(tmpBuf);
		strm.avail_out = BUFSIZE;

		// windowBits = 15 + 16 to enable gzip
		// From the zlib manual: windowBits can also be greater than 15 for optional gzip encoding. Add 16 to windowBits
		// to write a simple gzip header and trailer around the compressed data instead of a zlib wrapper.
		ret = deflateInit2(&strm, Z_BEST_COMPRESSION, Z_DEFLATED, 15 + 16, 8, Z_DEFAULT_STRATEGY);

		if (ret != Z_OK)
			return false;

		while (strm.avail_in != 0) {
			ret = deflate(&strm, Z_NO_FLUSH);
			if (ret != Z_OK)
				return false;

			if (strm.avail_out == 0) {
				dest.append(tmpBuf, BUFSIZE);
				strm.next_out = reinterpret_cast<uchar*>(tmpBuf);
				strm.avail_out = BUFSIZE;
			}
		}

		int deflateRes = Z_OK;
		while (deflateRes == Z_OK) {
			if (strm.avail_out == 0) {
				dest.append(tmpBuf, BUFSIZE);
				strm.next_out = reinterpret_cast<uchar*>(tmpBuf);
				strm.avail_out = BUFSIZE;
			}

			deflateRes = deflate(&strm, Z_FINISH);
		}

		if (deflateRes != Z_STREAM_END)
			return false;

		dest.append(tmpBuf, BUFSIZE - strm.avail_out);
		deflateEnd(&strm);

		return true;
	}

	bool doUnCompress(const QByteArray& src, QByteArray& dest)
	{
		dest.clear();

		if (src.size() <= 4) {
			qWarning("uncompress: Input data is truncated");
			return false;
		}

		z_stream strm;
		const int CHUNK_SIZE = 1024;
		char out[CHUNK_SIZE];

		strm.zalloc = Z_NULL;
		strm.zfree = Z_NULL;
		strm.opaque = Z_NULL;
		strm.avail_in = static_cast<uint>(src.size());
		strm.next_in = reinterpret_cast<uchar*>(const_cast<char*>(src.data()));

		const int windowBits = 15;
		const int ENABLE_ZLIB_GZIP = 32;

		int ret = inflateInit2(&strm, windowBits | ENABLE_ZLIB_GZIP); // gzip decoding
		if (ret != Z_OK)
			return false;

		// run inflate()
		do {
			strm.avail_out = CHUNK_SIZE;
			strm.next_out = reinterpret_cast<uchar*>(out);

			ret = inflate(&strm, Z_NO_FLUSH);
			Q_ASSERT(ret != Z_STREAM_ERROR); // state not clobbered

			switch (ret) {
			case Z_NEED_DICT:
			case Z_DATA_ERROR:
			case Z_MEM_ERROR:
				inflateEnd(&strm);
				return false;
			}

			dest.append(out, CHUNK_SIZE - strm.avail_out);
		} while (!strm.avail_out);

		// clean up and return
		inflateEnd(&strm);
		return true;
	}

	QString encrypt(const QByteArray& text)
	{
		char dde[32] = { char(0x8b ^ 0x12), char(0xe3 ^ 0x13), char(0xe4 ^ 0x14), char(0x91 ^ 0x15), char(0x69 ^ 0x12),
	char(0xb7 ^ 0x13), char(0x7e ^ 0x14), char(0x0f ^ 0x15), char(0x05 ^ 0x12), char(0x2a ^ 0x13),
	char(0x83 ^ 0x14), char(0xa5 ^ 0x15), char(0x73 ^ 0x12), char(0x1e ^ 0x13), char(0x38 ^ 0x14),
	char(0x5a ^ 0x15), char(0xc5 ^ 0x12), char(0xbd ^ 0x13), char(0xeb ^ 0x14), char(0xff ^ 0x15),
	char(0x1d ^ 0x12), char(0x49 ^ 0x13), char(0x01 ^ 0x14), char(0xe4 ^ 0x15), char(0x32 ^ 0x12),
	char(0x63 ^ 0x13), char(0xd2 ^ 0x14), char(0x1b ^ 0x15), char(0xf8 ^ 0x12), char(0xdc ^ 0x13),
	char(0x7c ^ 0x14), char(0x93 ^ 0x15) };

		for (int i = 0; i < 32; ++i)
		{
			int m = i % 4;
			if (m == 0)
				dde[i] = dde[i] ^ 0x12;
			else if (m == 1)
				dde[i] = dde[i] ^ 0x13;
			else if (m == 2)
				dde[i] = dde[i] ^ 0x14;
			else
				dde[i] = dde[i] ^ 0x15;
		}
		QByteArray iv;
		if (!KAesCodec::generateAesKey(iv, 16))
			return QString();

		QString rv;
		if (!KAesCodec::aesEncryptCBCMode(
			QByteArray(dde, 32),
			iv,
			text,
			rv))
		{
			return QString();
		}

		iv.append(QByteArray::fromBase64(rv.toUtf8()));
		QString t(iv.toBase64());
		return t;
	}

	QByteArray decrypt(const QString& text)
	{
		QByteArray ba = QByteArray::fromBase64(text.toUtf8());
		if (ba.length() <= 16)
			return QByteArray();
		QByteArray iv = ba.mid(0, 16);
		QByteArray content = ba.mid(16).toBase64();

		char dde[32] = { char(0x8b ^ 0x12), char(0xe3 ^ 0x13), char(0xe4 ^ 0x14), char(0x91 ^ 0x15), char(0x69 ^ 0x12),
			char(0xb7 ^ 0x13), char(0x7e ^ 0x14), char(0x0f ^ 0x15), char(0x05 ^ 0x12), char(0x2a ^ 0x13),
			char(0x83 ^ 0x14), char(0xa5 ^ 0x15), char(0x73 ^ 0x12), char(0x1e ^ 0x13), char(0x38 ^ 0x14),
			char(0x5a ^ 0x15), char(0xc5 ^ 0x12), char(0xbd ^ 0x13), char(0xeb ^ 0x14), char(0xff ^ 0x15),
			char(0x1d ^ 0x12), char(0x49 ^ 0x13), char(0x01 ^ 0x14), char(0xe4 ^ 0x15), char(0x32 ^ 0x12),
			char(0x63 ^ 0x13), char(0xd2 ^ 0x14), char(0x1b ^ 0x15), char(0xf8 ^ 0x12), char(0xdc ^ 0x13),
			char(0x7c ^ 0x14), char(0x93 ^ 0x15) };

		for (int i = 0; i < 32; ++i)
		{
			int m = i % 4;
			if (m == 0)
				dde[i] = dde[i] ^ 0x12;
			else if (m == 1)
				dde[i] = dde[i] ^ 0x13;
			else if (m == 2)
				dde[i] = dde[i] ^ 0x14;
			else
				dde[i] = dde[i] ^ 0x15;
		}

		QByteArray rv;
		if (!KAesCodec::aesDecryptCBCMode(
			QByteArray(dde, 32),
			iv,
			content,
			rv))
		{
			return QByteArray();
		}
		return rv;
	}
}