﻿#include "sheets_importer.h"
#include "helpers/varobject_helper.h"

SheetsImporter::SheetsImporter() : m_useImportSheets(false)
{
}

void SheetsImporter::InitImportSheets(const VarObj& param)
{
    if (param.has("importSheets"))
    {
        m_useImportSheets = true;
        VarObj importSheets = param.get_s("importSheets");
        for (int32 i = 0, count = importSheets.arrayLength_s(); i < count; ++i)
            m_importSheets.emplace(importSheets.item_uint32(i));
    }
}

bool SheetsImporter::CheckSheetImport(UINT sheetStId)
{
    return m_useImportSheets ? m_importSheets.find(sheetStId) != m_importSheets.end() : true;
}

bool SheetsImporter::IsEnableImportSheets() const
{
    return m_useImportSheets;
}

const std::unordered_set<UINT>& SheetsImporter::GetImportSheets() const
{
    return m_importSheets;
}