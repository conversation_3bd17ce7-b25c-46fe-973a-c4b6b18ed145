﻿#ifndef __WEBET_EXEC_DETAIL_H__
#define __WEBET_EXEC_DETAIL_H__

#include "webdeclare.h"
#include "ss_rect.h"
#include "block_point.h"
#include "webbase/binvariant/binvarobj.h"
#include "webbase/binvariant/binwriter.h"
#include "wo/wo_msgType_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "revision_ext.h"
#include "dbsheet/db2et_exporter.h"

namespace wo
{

class KEtWorkbook;
struct BlockPoint;
class KEtRevisionContext;
class KwVersionManager;
class KEtWorkbooks;
class KwTasks;
class KwCommand;
struct CellPos;

struct DetailExtArgs
{
	DetailExtArgs()
		: fileId(nullptr)
		, task(nullptr)
		, corePermissionID(nullptr)
		, commitVersion(WO_INVALID_COMMIT_ID)
		, notifyType(WoNotifyInvalid)
		, autoUpdateThreshold(0)
		, isServerLocalExec(false)
		, bNeedSubscriptionOp(true)
		, bWriteCooperationRecord(true)
		, bNeedCleanChildProc(true)
		, connFrom(WoConnFromUnknown)
		, connScene(WoConnSceneUnknown)
		, connLife(WoConnLifeUnknown)
		, userRequestInfo(nullptr)
	{};

	const char *fileId;
	const char *task;
	const char *corePermissionID;
	CommitID commitVersion;
	WebNotifyType notifyType;
	int autoUpdateThreshold;
	WoConnFrom connFrom;
	WoConnScene connScene;
	WoConnLife connLife;
	bool isServerLocalExec;
	bool bNeedSubscriptionOp;
	bool bWriteCooperationRecord;
	bool bNeedCleanChildProc;
	const char *userRequestInfo;
};

enum ExportXlsxOptions : int 
{
	XlsxWithoutAttachmentCol = 1,			//仅导出xlsx文件，删除附件列
	XlsxEmbedImages = 2,					//导出xlsx文件，图片嵌入xlsx文件
	ZipWithImages = 0x100,					//导出压缩包时，包含图片
	ZipWithAttachment = 0x200,				//导出压缩包时，包含附件
};

class ExecDetail
{
public:
	ExecDetail(KEtWorkbooks*, const char* connID, const char* userid, const char* sessionid, DetailExtArgs args, const WebSlice* slice, const WebSlice* serExtraParam, bool ignoreSeq);
	~ExecDetail();

	WebInt ExecInit();
	WebInt ExecCmd();
	WebInt ExecQuery();
	WebInt ExecDelayCopy(const WebMimeData *pMimeData);
	WebInt ExecCopyImg();
	WebInt ExecCmdDirect();
	WebInt ExecExtraCommand();
	WebInt ExecSummary();
	WebInt ExecSummaryTile();// 平铺类型
	WebInt ExecAttachments();
	WebInt ExecSheetsAttachments();
	WebInt ExecSubscriptionInfo();
	WebInt ExecMemoStat();
	WebInt ExecBlock();
	WebInt ExecUpdateShapes();
	WebInt ExecComment();
	WebInt ExecExportPdf();
	WebInt ExecExportOfd();
	WebInt ExecExportImg();
	WebInt ExecExportXlsx();
	WebInt ExecExportDbAttachmnet();
	WebInt ExecExportDbToKSheet();
	WebInt ExecExportKSheetToKSheet();
	WebInt ExecExportSheetToCSV();
	WebInt ExecExportToLocalFile(FILEFORMAT fileFormat);
	WebInt ExecExportTxt();
	WebInt ExecExportXlsxToKSheet();
	WebInt ExecExportDb();
	WebInt ExecExportSvg();
	WebInt RunExportDb(_Workbook*, bool toEmptySheet, VarObj& kanbanCfg, VarObj& defaultName
		, IDX sheetIdx, PCWSTR filePath, bool isWholeBook);
	WebInt CheckDownLoadImg(ISheet *spSheet, bool bForceAttachmentReDownload = true);
	WebInt InitConnState();
	WebInt ExecGetImages();
	WebInt ExecSubscribeCrossBooks();
	WebInt SetCrossBookAutoUpdateThreshold();
	WebInt OnNotify();
	WebInt RefreshInitVersion();
	WebInt SetTriggerConf();
	WebInt OnBindAttachmentIdsDone();
	WebInt CrossRefRecalculate(RecalculateType type);
	
	WebInt MergeFileTaskRecalculate(RecalculateType type);
	WebInt OnMergeTaskSrcDataDirty(const char* fileId);
	WebInt OnAsyncGetMergeFileDone(NetFileRes res, const char* url, const char* fileId);
	WebInt OnAsyncGetCrossbookState(NetFileRes res, const char* url, const char* fileId);

	WebInt AuthQueryDbCoHistories(const char *connID, const char *userID);
	WebInt GetLastPosition(int *sheetIdx, CELL *cell);
	WebInt UpdateSupBooks();

	WebInt ExportInquirerData(UINT32 stId, ks_wstring cond);

	WebInt UpdateDbDirty();
	WebInt GenTask4DynamicArrayFormulaResumeCalc();
	WebInt CalcImportrangeOnOpen();
	WebInt ExecExportThumbnail();
	WebInt ExecCheckUserAllVisiblePermission();
    WebInt ConvertCurUserFilterToAnyUserFilter();
	WebInt TransferAutoPassword();
	WebInt ExecExportAutofilterList();
	WebInt ExecSetUserPermissionId(VarObj* pInfos);
	WebInt SplitToNewFile(const char* path);
	WebInt ExecExportAirApp();
	WebInt GetMergedSubRanges();
protected:
	WebInt ExecInitImpl(bool bCallByInit);
	WebInt ResetCbSubscriptions();
	WebInt NotifyCrossBooksUpdate();
	WebInt CollectCbsToBeReSubscribed(std::vector<INT> &);
	WebInt OnCbSubscribe();
	WebInt OnCbUnSubscribe();
	WebInt OnCbUpdate();
	WebInt CbReferTask();
	WebInt ParseThenSignal();
	WebInt PrepareCurUserConn();
	WebID InitWebDefaultSheet(KEtRevisionContext&, IKETUserConn * pUserConn);
	bool InitWebDbSheetView(KEtRevisionContext&, binary_wo::VarObj&, IKWorksheet*);
	void ConvertDbGridVisibleBlocks(IDBSheetView*, binary_wo::VarObj&, EtDbId&, EtDbId&, EtDbId&);
	WebInt verifyAndPrepareUserConn();
    void NotifyCustomListUpdate();

private:
	WebInt ExecExportDb2Db();
	void ParseExportSheetStIds(std::set<UINT>&);
	WebInt ParseMergefileTaskMsg(binary_wo::VarObj&, binary_wo::VarObj&);
	bool NeedGenerateDbEmptyTask();
public:
	::IKETUserConn* GetCurUser();
	std::unique_ptr<KEtRevisionContext> CreateContext();
	void FillError(WebInt);

protected:
	typedef std::unordered_map<BlockPoint, \
		std::vector<RECT>, BlockPoint::Hasher, BlockPoint::KeyEq> BP_Cluser;
	typedef std::vector<BlockPoint> BP_Vec;

	WebInt ExecInitWindows(bool bCallByInit);
	WebInt ExecInitWeb();
	void AddExtState(binary_wo::BinWriter&, KEtRevisionContext &);
	void BP_Init(KEtRevisionContext&, binary_wo::BinWriter&, ISerialAcceptor* acpt);
	void BP_Proc(KEtRevisionContext&, binary_wo::BinWriter&, ISerialAcceptor*);
	bool BP_Build(KEtRevisionContext&,
		const BP_Vec& bpm, const BP_Vec& bpn, std::vector<SsRect>&, BP_Vec&);
	bool BP_Adjust(KEtRevisionContext&, std::vector<SsRect>&);
	void BP_Update(binary_wo::BinWriter&, BP_Vec& vOld, BP_Vec& vNew);
	void BP_Fill(KEtRevisionContext&, binary_wo::BinWriter&, ISerialAcceptor*, const SsRect*, size_t);
	void BP_Formula(KEtRevisionContext&, std::vector<SsRect>&, ISerialAcceptor*);
	void BP_CalcCells(KEtRevisionContext&, binary_wo::BinWriter&, const BP_Vec&);
	void BP_UpdateSparklineRefArea(KEtRevisionContext& ctx, binary_wo::BinWriter& ww, const BP_Vec& bpVec, std::vector<SsRect>& fresh);
	void BP_EafCellsFetch(KEtRevisionContext&, std::vector<const CellNode*>&, bool&, WebInt&);
	void BP_CalcCellsFetch(KEtRevisionContext&, std::vector<const CellNode*>&, bool& bAll, WebInt& fmlaResVer);
	void BP_CalcCellsFetchDiffusing(KEtRevisionContext&, std::vector<const CellNode*>&, bool& bAll, WebInt& fmlaResVer);
	void BP_CalcCellsFetchNoDiffusing(KEtRevisionContext& ctx, std::vector<const CellNode*>& cns, bool& bAll, WebInt& fmlaResVer);
	void BP_CalcCellsFetchInstantly(KEtRevisionContext& ctx, std::vector<const CellNode*>& cns);
	void Proc_AutoSlim(KEtRevisionContext&, binary_wo::BinWriter&);
	static BP_Vec BP_Collect(const BP_Cluser&, const BP_Cluser&);
	static void BP_Uniform(
		const SsRect* arr, size_t cnt, BP_Cluser& rm, bool bWhole);
	bool HasHyperlinkWithRun();
	bool HasUnsupportSheetType() const;
	
	void MonitorRange_Proc(KEtRevisionContext& ctx, binary_wo::BinWriter&);
	void MonitorRange_BuildTasks(KwTasks& tasks, KEtRevisionContext&);
	void MonitorRange_ApplyTasks(KwTasks& tasks, KEtRevisionContext&, binary_wo::BinWriter&);

	void Proc_ExtAppendInfo(KEtRevisionContext& ctx, binary_wo::BinWriter& bw);
	void Proc_ExtWindowInfo(KEtRevisionContext& ctx, binary_wo::BinWriter& bw);

	void SerialPageSetupData(KEtRevisionContext& ctx, binary_wo::BinWriter& bw);
	void SerialPageSetRanges(IPageSetupData* spPageSetupData, ISheet* spISheet, WebID objSheet, binary_wo::BinWriter& bw);
	void SerialBeautifyOperators(KEtRevisionContext& ctx, binary_wo::BinWriter& bw);
	void SerialExclusiveRangeData(KEtRevisionContext& ctx, binary_wo::BinWriter& bw);

protected:
	void FillOut(binary_wo::BinWriter&, MsgType type);
	void FillOut(IWebSlice* pWebSlice, MsgType type);
	void FillOutPic();
	void FillOther();
	void FillLog(KEtRevisionContext* ctx, KwTasks& tasks);
	void FillLog(KEtRevisionContext* ctx);
    void FillRevisionSelection(KEtRevisionContext&, binary_wo::BinWriter&);
	WebStr getCmdName(const KwCommand* cmd);
	void ClearApiCommandBeforeCommit(KwTasks& tasks);
protected:
	void SerializeFmla(KEtRevisionContext&, ISerialAcceptor*);
	void BPInit_SerialHyperlinks(KEtRevisionContext&, ISerialAcceptor*, std::vector<BlockPoint>&);
	void BPInit_SerialSparklines(KEtRevisionContext& ctx, ISerialAcceptor* acpt, std::vector<BlockPoint>& vbp);
	void BPInit_SerialCellCondition(KEtRevisionContext&, ISerialAcceptor*, std::vector<BlockPoint>&);
	void BPInit_SerialCellProtection(KEtRevisionContext&, ISerialAcceptor*, std::vector<BlockPoint>&);

	void Respond(KEtRevisionContext& ctx, WebInt);
	void InitContext(KEtRevisionContext& ctx, bool forceConn = false, bool isExecCmd = false);
	void MakeUndoRedoRes(HRESULT ret, PCWSTR cmdName);
	void FillCallbackIds(binary_wo::BinWriter &);
	void SerializeSupBookIds(KEtRevisionContext&, binary_wo::BinWriter&);
	void SerializeBookSetting(binary_wo::BinWriter&);
	void SerializeCalcOption(binary_wo::BinWriter&, IAppSettings*);
	void SerializeConnectingObjects(binary_wo::BinWriter&, KEtRevisionContext*);
	void SerializeCustomLists(binary_wo::BinWriter&, KEtRevisionContext*);
	void SerializeIngoreParam(binary_wo::BinWriter&);
	void SerializeSupBooksInfo(binary_wo::BinWriter&, KEtRevisionContext*);
	void SerializeFileInfoCore(binary_wo::BinWriter&, KEtRevisionContext&);
	void SerializeConfigCore(binary_wo::BinWriter& writer, KEtRevisionContext& ctx);
	
private:
	void Summary_ShapeInfo(ISerialAcceptor*, IKWorksheet*, AbsObject*);
	void Summary_tableStyle(ISerialAcceptor*, ISheet*);
	void Summary_sheetBackgroundImg(ISerialAcceptor*, ISheet*);
	void Summary_cellImage(binary_wo::BinWriter&, KEtRevisionContext*);
	void MemoStat_bookSheet(binary_wo::BinWriter&, IKWorksheets*);
	void MemoStat_oleDoc(binary_wo::BinWriter&);
	void MemoStat_PivotCache(binary_wo::BinWriter&);
	void MemoStat_transactEnv(binary_wo::BinWriter&);
	UINT64 MemoStat_chart();
	UINT64 MemoStat_chartShape(const drawing::GroupShape*);
	void onFillOut(WebSize sliceSize, MsgType type, const char * msgType, IWebSlice *pSlice);

private:
	void SummaryTile_TabClr(ISerialAcceptor*, ISheet*, IBook*);
	void SummaryTile_SheetType(ISerialAcceptor*, ISheet*);
	void ExecSummaryTile_comment(ISerialAcceptor* acpt, IKWorksheets* pWss, int index);
	void SummaryTile_SheetIsShare(ISerialAcceptor* acpt, IKWorksheets* pWss, int index);

private:
	typedef std::unordered_set<BlockPoint, BlockPoint::Hasher, BlockPoint::KeyEq> BPS;
	bool _SerialCell(KEtRevisionContext& ctx, BPS& bps, IBook* bk, CellPos& cell, ISerialAcceptor* acpt, bool &isVisibleCell);
	
	void SerialFmlaRes(KEtRevisionContext& ctx, const BP_Vec& bpVec, IBook* bk, ISerialAcceptor* acpt);
	WebInt _ExecDelayShapeCopy(const WebMimeData *pMimeData, const binary_wo::VarObj &param);
	WebInt _ExecRangeImgCopy(const binary_wo::VarObj &param, binary_wo::BinWriter& binWriter);
	WebInt _ExecShapeImgCopy(const binary_wo::VarObj &param, binary_wo::BinWriter& binWriter);
	WebInt _ExecDelayDbsheetCopy(const WebMimeData *pMimeData, const binary_wo::VarObj &param);
	void _GetIKShape(IDX sheetIdx, binary_wo::VarObj objIdxPath, KEtRevisionContext* pCtx, IKShape** ppShape);
	void _GetApiShape(IKShape* pShape, KsoShape** ppShape);
	chart::KCTShape* _GetChartShape(IKShape* pShape);
	void _SetInitRect(KEtRevisionContext& ctx,WebID activeSheet, binary_wo::VarObj& vExArgs);
	void _SetInitDefRect(KEtRevisionContext& ctx, WebID activeSheet, IKWorksheet *);
	void _BinWriteRange(binary_wo::BinWriter& bw, const RANGE& rg, WebID objSheet);
	void _SerialHyperlink(IDX sheetId, const RECT& rc, ISerialAcceptor* acpt);
	void _SerialSparklines(IDX sheetId, const RECT& rc, ISerialAcceptor* acpt);
	void _SerialDirtySparklineRefAreas(ISerialAcceptor* acpt, KSparklineRefAreaVersion::DirtyRefAreas& dirtyRefAreas, WebInt newClientVer);
	void _SerialDirtySparklineGroupSet(SPARKLINE_GROUP_SET& sparklineGroupSet, ISerialAcceptor* acpt);
	void _SerialDirtySparklineSet(SPARKLINE_SET& sparklineSet, ISerialAcceptor* acpt);
	WebInt _CollectDirtySparklineRefAreas(KSparklineRefAreaVersion::DirtyRefAreas&, const BPS&, const std::unordered_set<WebID>&, WebInt, std::vector<SsRect>& fresh);
	void _CollectDirtySparklineRefAreasByVersion(KSparklineRefAreaVersion::DirtyRefAreas& dst, wo::KSparklineRefAreaVersion* version, const BPS& bps, const std::unordered_set<WebID>&, std::vector<SsRect>& fresh);
	bool _IsInFreshBP(std::vector<SsRect>& fresh, SsRect& target);
	void _SerialCellCondBlock(IDX sheetId, const RECT& rc, ISerialAcceptor* acpt);
	void _SerialCellProtection(IDX sheetId, const RECT& rc, ISerialAcceptor* acpt);
	void _WriteDaaysChangeArray(binary_wo::BinWriter& bw);

	void InitUrlSupBooks(KEtRevisionContext* ctx);
	void SerialCbReferAutoUpdateState(KEtRevisionContext& ctx, binary_wo::BinWriter& bw);
	bool IsSupBookStateOK();
	void ExtendSvgRange(ISheet *pSheet, int rowCount, int colCount, CELL &cell);
	void InitMergeFileAutoUpdate(KEtRevisionContext* ctx);
	bool IsUseSeq();
	bool DoReExec();
	bool hasReadonlyFilter();
	bool isSharedLink();
	bool FetchUserProtectPermsChanged(KEtRevisionContext*);
	bool isSharedLinkNotExist();
	ISheet* GetSharedSheet(IKETUserConn * pUserConn);
	void UpdateUserColPermsStatus(KEtRevisionContext*);
	PCWSTR _GetExcludeUserColPermsChange(KEtRevisionContext*);
    HRESULT ExtractDbAttachmentDataToServer(UINT sheetId, EtDbId viewId, bool isDbData2Xlsx, bool isCompressedBundle, bool isFilterVideo, const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes, DbSheetsAttachmentInfoMap& attachmentData);
	HRESULT ExtractDbViewAttachmentDataToServer(UINT sheetId, EtDbId viewId, bool isFilterVideo, const std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes);
	void WriteHookInValidResult(PCWSTR hookId, int code, PCWSTR message, binary_wo::BinWriter &res);
	void WriteHookInValidResult(PCWSTR hookId, PCWSTR uuid, int code, PCWSTR message, binary_wo::BinWriter &res);
	void ResetTypeBreaks();

	void GetDashboardViewSrcSheet(ISheet*, std::map<UINT, ISheet*>&);
	void updateEnableCommonLog();
protected:
	KEtWorkbooks* 	m_wbs;
	KEtWorkbook* 	m_workbook;
	IKUserConn*		m_curUserConn;
	binary_wo::VarObjRoot		m_rootObj;
	binary_wo::VarObj			m_root;
	binary_wo::VarObjRoot		m_serverExtraParamObj;
	binary_wo::VarObj			m_serverExtraParam;
	WebInt			m_seq;
	ks_stdptr<IWebSlice>  m_spSignalSlice;
	const char*		m_signalSliceType;
	const char* 	m_connID;
	const char* 	m_userID;
	const WebSlice* m_slice;
	bool			m_bQueryOrCmd;
	bool			m_bEverInit;
	bool			m_bHttpCalling;
	bool			m_bPrepareUser;

	// image cache
	typedef struct
	{
		QByteArray sha1;
		int width;
		int height;
	} ImgCacheVal;
	
	typedef std::unordered_map<uint32, ImgCacheVal> ImgsCacheMap; // imageId -> cacheMap
	ImgsCacheMap m_ImgsCacheMap;
	DetailExtArgs m_extArgs;
	ks_wstring m_traceId;
};

} // namespace wo

#endif //__WEBET_EXEC_DETAIL_H__
