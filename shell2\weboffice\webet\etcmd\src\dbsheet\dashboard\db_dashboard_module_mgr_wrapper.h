﻿#ifndef __WEBET_DB_DASHBOARD_MODULE_MGR_WRAPPER_H__
#define __WEBET_DB_DASHBOARD_MODULE_MGR_WRAPPER_H__
#include "db_dashboard_module_wrapper.h"

interface IKWebExtension;
interface IDbFilterCriteria;
namespace wo
{

class KDbChartWrapper;
class KDbDashboardPluginWrapper;
class KDbDashboardViewWrapper;
class KDbDashboardModuleMgrWrapper
{
public:
    KDbDashboardModuleMgrWrapper(KEtWorkbook* pEtWorkbook, etoldapi::_Worksheet* pWorksheet);
    KDbDashboardModuleMgrWrapper(const KDbDashboardModuleMgrWrapper&) = delete;
    KDbDashboardModuleMgrWrapper& operator=(const KDbDashboardModuleMgrWrapper&) = delete;
public:
    HRESULT EnumChart(const std::function<HRESULT(KDbChartWrapper*)>& func);
    HRESULT EnumModule(const std::function<HRESULT(KDbDashboardModuleWrapper*)>& func);
    std::unique_ptr<KDbChartWrapper> GetChart(PCWSTR chartId);
    std::unique_ptr<KDbDashboardPluginWrapper> GetPlugin(PCWSTR webExtensionKey);
    std::unique_ptr<KDbDashboardModuleWrapper> GetModule(PCWSTR webExtensionKey);
    std::unique_ptr<KDbDashboardViewWrapper> GetView(PCWSTR webExtensionKey);
    HRESULT SerializeServerRenderData(const binary_wo::VarObj& param, bool isMobile, KEtRevisionContext* pCtx,
                                      ISerialAcceptor* pAcpt);
    HRESULT CheckCanAddPlugin();
    HRESULT CheckCanAddView();
    HRESULT DeleteAllDbPlugin();

    void SerializeFilters(const char* keyName, ISerialAcceptor* pAcpt) const;
    void SerializeFilter(IDbDashboardFilter* pFilter, ISerialAcceptor* pAcpt) const;
    static bool IsValidFilterName(PCWSTR name);
private:
    IKWebExtension* GetWebExtension(PCWSTR webExtensionKey);
    std::unique_ptr<KDbChartWrapper> GetChart(IKWebExtension* pWebExtension);
    std::unique_ptr<KDbDashboardModuleWrapper> GetModule(IKWebExtension* pWebExtension);
    std::vector<std::unique_ptr<KDbDashboardModuleWrapper>> GetModules();

    KEtWorkbook* m_pEtWorkbook;
    ks_stdptr<etoldapi::_Worksheet> m_spWorksheet;
    ks_stdptr<IDBDashBoardDataOp> m_spDbDashboard;
};

};
#endif // __WEBET_DB_DASHBOARD_MODULE_MGR_WRAPPER_H__