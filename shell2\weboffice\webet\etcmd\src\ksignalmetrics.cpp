﻿#include "etstdafx.h"
#include "ksignalmetrics.h"
#include "woetsetting.h"
#include "kbinreadertravel.h"
#include <unordered_map>
#include "ksignalbinnode.h"
#include "collect.h"
#include "workbook.h"

namespace wo {
    
enum class NameStrType
{
    kVersions,
    kNewObj,
    kNewCustomObj,
    kOrgObjs,
    kId,
    kClsName,
    kOps,
    kTop,
    
    _kCount
};

struct NameStrItem
{
    NameStrType m_type;
    WebName m_name;
    int m_nameLen;
};

#define NAME_ITEM(em, strName) NameStrItem{em, strName, szLen(strName)}
static constexpr NameStrItem g_NameStrItems[] = {
    NAME_ITEM(NameStrType::kVersions, "versions"),
    NAME_ITEM(NameStrType::kNewObj, "newObjs"),
    NAME_ITEM(NameStrType::kNewCustomObj, "newCustomObjs"),
    NAME_ITEM(NameStrType::kOrgObjs, "orgObjs"),
    NAME_ITEM(NameStrType::kId, "id"),
    NAME_ITEM(NameStrType::kClsName, "clsname"),
    NAME_ITEM(NameStrType::kOps, "ops"),
    NAME_ITEM(NameStrType::kTop, "top"),
};

template<typename T, std::size_t N>
constexpr bool checkGlbItems(const T(&arr)[N], int exptSize)
{
    if (N != exptSize) return false;
    
    int i = 0;
    for (const auto & item : arr)
    {
        if (i != (int)item.m_type)
            return false;
        ++i;
    }
    return true;
}
static_assert(checkGlbItems(g_NameStrItems, (int)NameStrType::_kCount), "g_NameItems顺序不对");
////////////////////////////////////////////////////////////////////////
struct BinNodeGlbInfo
{
    BinNodeNameType m_type;
    const char * m_shortPath;
    int m_shortPathLen;
    bool m_isFilterByNameType;
};

#define BIN_INFO_ITEM(em, shortPath, isHeapFilter) BinNodeGlbInfo{em, shortPath, szLen(shortPath), isHeapFilter}
static constexpr BinNodeGlbInfo g_BinNotInfos[] = {
    BIN_INFO_ITEM(BinNodeNameType::kRoot, "", true),
    BIN_INFO_ITEM(BinNodeNameType::kCommon, "", false),
    BIN_INFO_ITEM(BinNodeNameType::kVersionsArray, "^vers", true),
    BIN_INFO_ITEM(BinNodeNameType::kVersionItem, "", true),
    BIN_INFO_ITEM(BinNodeNameType::kVersionNewObjArray, "^verNewObjs", true),
    BIN_INFO_ITEM(BinNodeNameType::kNewObjArray, "^newObjs", true),
    BIN_INFO_ITEM(BinNodeNameType::kNewCustomObjArray, "^cusNewObjs", true),
    BIN_INFO_ITEM(BinNodeNameType::kNewObjItem, "", false),
    BIN_INFO_ITEM(BinNodeNameType::kVersionOpsArray, "^verOpObjs", true),
    BIN_INFO_ITEM(BinNodeNameType::kVersionOpsObjItem, "", false),
    BIN_INFO_ITEM(BinNodeNameType::kOrgObjsArray, "^orgObjs", true),
    BIN_INFO_ITEM(BinNodeNameType::kOrgObjItem, "", false),
};
static_assert(checkGlbItems(g_BinNotInfos, (int)BinNodeNameType::_kCount), "g_BinNotInfos顺序不对");
////////////////////////////////////////////////////////////////////////

bool cmpSelfSizeGT(const BinNode * left, const BinNode * right)
{
    if (left == nullptr || right == nullptr) return true;
    return left->selfSize() > right->selfSize();
}

bool cmpSizeGT(const BinNode * left, const BinNode * right)
{
    if (left == nullptr || right == nullptr) return true;
    return left->size() > right->size();
}

template <typename Data>
class KMaxSizeHeap
{
    using FuncCmp = std::function<bool(const Data &, const Data &)>;
public:
    KMaxSizeHeap(int maxCnt, FuncCmp cmp)
        : m_maxCnt(maxCnt)
        , m_cmp(cmp)
    {
    }
    
    void add(Data d)
    {
        if (m_heap.size() == m_maxCnt)
        {
            if (m_cmp(d, m_heap[0]))
            {
                std::pop_heap(m_heap.begin(), m_heap.end(), m_cmp);
                m_heap.pop_back();
            }
            else
                return;
        }
        
        m_heap.push_back(d);
        std::push_heap(m_heap.begin(), m_heap.end(), m_cmp);
    }
    std::vector<Data> getSortData() const
    {
        std::vector<Data> selfHeap = m_heap;
        for (int i = 0, sz = selfHeap.size(); i < sz; ++i)
        {
            std::pop_heap(selfHeap.begin(), selfHeap.end() - i, m_cmp);
        }
        return selfHeap;
    }
    
    void clear() { m_heap.clear(); }
    std::size_t size() const { return m_heap.size(); }
    
private:
    std::vector<Data> m_heap;
    FuncCmp m_cmp;
    int m_maxCnt;
};

////////////////////////////////////////////////////////////////////////
enum class NameTravelState
{
    kNone,
    kIn,
    kOut,
};

struct KObjParentInfo
{
    BinNode * m_parent = nullptr;
    binary_wo::BuffName m_name;
    KObjParentInfo(BinNode *parent, const binary_wo::BuffName & name): m_parent(parent), m_name(name)
    {}
};

using KObjParentInfoVec = std::vector<KObjParentInfo>;
using KMaxBinSizeHeap = KMaxSizeHeap<BinNode*>;
using BinStrData = std::pair<BinWebStr, KCntSize>;

bool cmpBinStrCntData(const BinStrData & ls, const BinStrData & rs)
{
    return ls.second.m_cnt > rs.second.m_cnt;
};

class ISignalBreak
{
public:
    virtual ~ISignalBreak() {}
    virtual bool needBreak() = 0;
};

class KSignalTravel: public binary_wo::IBinReaderTravel
{
public:
    explicit KSignalTravel(ISignalBreak *pBreak);
    ~KSignalTravel();
    
    KSignalTravel(const KSignalTravel &) = delete;
    KSignalTravel& operator=(const KSignalTravel &) = delete;
    
    void onBeginStruct(const binary_wo::BuffName & name) override;
    void onEndStruct(int size) override;
    void onBeginNameStruct(const binary_wo::BuffName & name, const binary_wo::BuffName & nameStruct) override;
    void onEndNameStruct(int size) override;
    void onBeginArray(const binary_wo::BuffName & name, int32 arrLen) override;
    void onEndArray(int size) override;
    void onValueInt8(const binary_wo::BuffName & name, int8 v) override;
    void onValueUint8(const binary_wo::BuffName & name, uint8 v) override;
    void onValueInt16(const binary_wo::BuffName & name, int16 v) override;
    void onValueUint16(const binary_wo::BuffName & name, uint16 v) override;
    void onValueInt32(const binary_wo::BuffName & name, int32 v) override;
    void onValueUint32(const binary_wo::BuffName & name, uint32 v) override;
    void onValueFloat32(const binary_wo::BuffName & name, float32 v) override;
    void onValueFloat64(const binary_wo::BuffName & name, float64 v) override;
    void onValueAbsObj32(const binary_wo::BuffName & name, uint32 v) override;
    void onValueAbsObj64(const binary_wo::BuffName & name, int64 v) override;
    void onValueBool(const binary_wo::BuffName & name, bool v) override;
    void onValueString(const binary_wo::BuffName & name, const WCHAR * wszArray, uint32 len) override;
    void onValueNameString(const binary_wo::BuffName & name, const binary_wo::BuffName & v) override;
    void onValueUint8Array(const binary_wo::BuffName & name, uint8 *pArray, uint32 len) override;
    void onArrayItemString(const WCHAR * wszArray, uint32 len) override;
    void onArrayItemNameString(const binary_wo::BuffName & v) override;
    void onArrayItemValue(uint8 *pArray, uint32 len, binary_wo::Types tp) override;
    bool needBreak() override;
    
    void enter(const binary_wo::BuffName & name, bool isStruct);
    void leave(int size);
    
    int topSelfCnt() { return m_maxSelfHeap.size(); }
    TopCollectInfos makeTopSelfInfo() { return makeHeapInfo(m_maxSelfHeap, BinNode::kSelfSize); }
    TopCollectInfos makeTopSizeInfo() { return makeHeapInfo(m_sizeHeap, BinNode::kSize); }
    TopCollectWStrInfos makeTopNewObjCntInfo();
    int getNoRefNewObjCnt() const { return m_noRefNewObjCnt; }
    int getNoRefNewObjBytes() const { return m_noRefNewObjBytes; }
    int getNewObjCnt() const { return m_nNewObjCnt; }
    int getNewObjClsNameCnt() const { return m_nNewObjClsNameCnt; }
    int getNewObjTopObjCnt() const { return m_nNewObjTopCnt; }
    
private:
    TopCollectInfos makeHeapInfo(const KMaxBinSizeHeap & sizeHeap, BinNode::SizeType szType);
    BinNode * createChildBinNode(const binary_wo::BuffName & name);
    void onSetSelfSize(BinNode * pNode);
    void onAddAbsObj(const binary_wo::BuffName & name, int64 v);
    void handleNewObjId(const binary_wo::BuffName & name, int64 v);
    void handleNewObjBool(const binary_wo::BuffName & name, bool v);
    void handleNewObjStr(const binary_wo::BuffName & name, const WCHAR * wszArray, uint32 len);
    
    bool isEqual(const binary_wo::BuffName & name, NameStrType type);
    bool updateCurNodeByEnter(BinNodeNameType nameType, NameStrType type);
    bool isNewObj(BinNode *pNode);
    void findValidRefNode(BinNode *p, BinNode **pRefClsNameNdoe, int &refClsNamelvl, binary_wo::BuffName & refName, int &refNameLvl);
    std::string genNodePath(BinNode *pNode);
    std::string genNodeSelfPath(BinNode *pNode, bool *pIsShortPath = nullptr);
    
private:
    BinNode* m_pRoot ;
    KMaxBinSizeHeap m_maxSelfHeap;
    KMaxBinSizeHeap m_sizeHeap;
    std::unordered_map<WebID, KObjParentInfoVec> m_objParentMap;
    std::unordered_map<WebID, KObjParentInfoVec> m_newObjMap;
    std::unordered_map<BinWebStr, KCntSize, BinWebStrHash, BinWebStrEqual> m_clsNameMap;
    NameTravelState m_state[(int)BinNodeNameType::_kCount];
    BinNode* m_curNode;
    ISignalBreak* m_pBreak;
    int m_noRefNewObjCnt;
    int m_noRefNewObjBytes;
    int m_nNewObjCnt;
    int m_nNewObjClsNameCnt;
    int m_nNewObjTopCnt;
};

constexpr std::size_t MAX_HEAP_CNT = 20;

KSignalTravel::KSignalTravel(ISignalBreak *pBreak)
: m_pRoot(new BinNode())
, m_curNode(nullptr)
, m_noRefNewObjCnt(0)
, m_noRefNewObjBytes(0)
, m_maxSelfHeap(MAX_HEAP_CNT, cmpSelfSizeGT)
, m_sizeHeap(MAX_HEAP_CNT, cmpSizeGT)
, m_pBreak(pBreak)
, m_nNewObjCnt(0)
, m_nNewObjClsNameCnt(0)
, m_nNewObjTopCnt(0)
{
    m_pRoot->setNameType(BinNodeNameType::kRoot);
    for (int i = 0; i < (int)BinNodeNameType::_kCount; ++i)
        m_state[i] = NameTravelState::kNone;
}

KSignalTravel::~KSignalTravel()
{
    m_pRoot->dispose();
    m_pRoot = nullptr;
    m_curNode = nullptr;
    m_maxSelfHeap.clear();
    m_sizeHeap.clear();
}

std::string KSignalTravel::genNodeSelfPath(BinNode *pNode, bool *pIsShortPath)
{
    const BinNodeGlbInfo & info = g_BinNotInfos[(int)pNode->nameType()];
    if (pIsShortPath)
    {
        *pIsShortPath = info.m_shortPathLen != 0;
    }
    if (info.m_shortPathLen)
    {
        return std::string(info.m_shortPath, info.m_shortPathLen);
    }
    return pNode->name().makeNameStr();
}

void KSignalTravel::findValidRefNode(BinNode *p, BinNode **pRefClsNameNdoe, int &refClsNamelvl, binary_wo::BuffName & refName, int &refNameLvl)
{
    BinNode *pCur = p->findFistNewObjNode();
    refClsNamelvl = 1;
    refNameLvl = 1;
    bool hasFindRefName = false;
    bool hasFindRefClsName = false;
    for (int i = 0; i < 5; ++i)
    {
        if (pCur == nullptr || (hasFindRefClsName && hasFindRefName))
            break;
        auto itr = this->m_newObjMap.find(pCur->objId());
        if (itr == this->m_newObjMap.end())
            break;
        
        const KObjParentInfo & firstRef = itr->second[0];
        if (!hasFindRefName && firstRef.m_name.nameLen() > 0)
        {
            hasFindRefName = true;
            refName = firstRef.m_name;
        }
        
        pCur = firstRef.m_parent;
        if (pCur == nullptr)
            break;
        pCur = pCur->findFistNewObjNode();
        if (pCur == nullptr)
            break;

        if (!hasFindRefClsName && pCur->clsNameLen() > 0)
        {
            hasFindRefClsName = true;
            *pRefClsNameNdoe = pCur;
        }
        
        if (!hasFindRefClsName)
            ++refClsNamelvl;
        if (!hasFindRefName)
            ++refNameLvl;
    }
}

// 遇到newObj时： key为第一个有效parent的name
//  /^verNewObjs/^ancestorNewObjClsName:key:clsName/
//  /^newObjs/^ancestorNewObjClsName:key:clsName/
//  /^cusNewObjs/^ancestorNewObjClsName:key:clsName/
// 遇到opObj/orgObjs时:
//  /^verOpObjs/^ancestorNewObjClsName:key:clsName/
//  /^orgObjs/^ancestorNewObjClsName:key:clsName/
std::string KSignalTravel::genNodePath(BinNode *pNode)
{
    auto addClsNameArray = [](std::string & objName, BinNode *p) {
        if (p && p->clsNameLen() && p->clsNameArray() != nullptr)
        {
            for (int i = 0, sz = p->clsNameLen(); i < sz; ++i)
            {
                objName.push_back((char)p->clsNameArray()[i]);
            }
        }
    };
    
    auto objPathName = [this, addClsNameArray](BinNode *p) -> std::string {
        std::string objName("^");
        if (p->objId())
        {
            BinNode *pRefClsNameNdoe = nullptr;
            int refClsNameLvl = 1, refNameLvl = 1;
            binary_wo::BuffName refName;
            
            // 找到第一个有效clsName的ref NewObj, 及ref lvl
            // 找到第一个有效的name，及ref lvl
            findValidRefNode(p, &pRefClsNameNdoe, refClsNameLvl, refName, refNameLvl);
            if (pRefClsNameNdoe)
            {
                addClsNameArray(objName, pRefClsNameNdoe);
                ASSERT(refClsNameLvl >= 1 && refClsNameLvl <= 9);
                if (refClsNameLvl > 1 && refClsNameLvl <= 9)
                {
                    objName.push_back('-');
                    objName.push_back('0' + refClsNameLvl);
                }
            }
            objName.push_back(':');
            if (refName.nameLen() > 0)
            {
                objName += refName.makeNameStr();
                ASSERT(refNameLvl >= 1 && refNameLvl <= 9);
                if (refNameLvl > 1 && refNameLvl <= 9)
                {
                    objName.push_back('-');
                    objName.push_back('0' + refNameLvl);
                }
            }
        }
        else
            objName.push_back(':');
        objName.push_back(':');
        addClsNameArray(objName, p);
        return objName;
    };
    
    std::string str;
    BinNode *pCur = pNode;
    while (pCur != nullptr)
    {
        if (pCur->isNewObjItem() || pCur->isOpsObjItem() || pCur->isOrgObjItem())
        {
            std::string s = objPathName(pCur);
            str = "/" + genNodeSelfPath(pCur->parent()) + "/" + s + "/" + str;
            break;
        }
        else
        {
            bool isShortPath = false;
            std::string path = genNodeSelfPath(pCur, &isShortPath);
            if (isShortPath)
            {
                str = "/" + path + "/" + str;
                break;
            }
            else
                str = path + "/" + str;
        }
        pCur = pCur->parent();
    }
    return str;
}

TopCollectInfos KSignalTravel::makeHeapInfo(const KMaxBinSizeHeap & sizeHeap, BinNode::SizeType szType)
{
    TopCollectInfos infos;
    infos.reserve(sizeHeap.size());
    std::vector<BinNode*> vt = sizeHeap.getSortData();
    for (auto itr : vt)
    {
        BinNode *pNode = itr;
        infos.emplace_back(genNodePath(pNode), KCntSize{pNode->itemCnt(), pNode->sizeByType(szType)});
    }
    return infos;
}

TopCollectWStrInfos KSignalTravel::makeTopNewObjCntInfo()
{
    KMaxSizeHeap<BinStrData> heap(MAX_HEAP_CNT, cmpBinStrCntData);
    // add
    for (const auto & item : m_clsNameMap)
    {
        heap.add(std::make_pair(item.first, item.second));
    }
    
    std::vector<BinStrData> data = heap.getSortData();
    TopCollectWStrInfos res;
    for (const auto & item : data)
    {
        if (item.first.m_len)
            res.emplace_back(ks_wstring(item.first.m_wszArray, item.first.m_len), item.second);
        else
            res.emplace_back(ks_wstring(), item.second);
    }
    return res;
}

BinNode * KSignalTravel::createChildBinNode(const binary_wo::BuffName & name)
{
    auto setNameType = [this](BinNode *pNode, BinNodeNameType type) {
        this->m_state[(int)type] = NameTravelState::kIn;
        pNode->setNameType(type);
    };
    
    switch (m_curNode->nameType())
    {
        case BinNodeNameType::kNewObjArray:
        case BinNodeNameType::kVersionNewObjArray:
        case BinNodeNameType::kNewCustomObjArray:
        {
            m_state[(int)BinNodeNameType::kNewObjItem] = NameTravelState::kIn;
            ++m_nNewObjCnt;
            return new NewObjBinNode(m_curNode);
        }
        case BinNodeNameType::kOrgObjsArray:
        {
            BinNode *pNode = new ObjBinNode(m_curNode);
            setNameType(pNode, BinNodeNameType::kOrgObjItem);
            return pNode;
        }
        case BinNodeNameType::kVersionOpsArray:
        {
            BinNode *pNode = new ObjBinNode(m_curNode);
            setNameType(pNode, BinNodeNameType::kVersionOpsObjItem);
            return pNode;
        }
        case BinNodeNameType::kVersionsArray:
        {
            BinNode *pNode = new BinNode(m_curNode);
            setNameType(pNode, BinNodeNameType::kVersionItem);
            return pNode;
        }
        default:
            return new BinNode(m_curNode);
    }
}

void KSignalTravel::onSetSelfSize(BinNode * pNode)
{
    // filter
    const BinNodeGlbInfo & info = g_BinNotInfos[(int)pNode->nameType()];
    if (info.m_isFilterByNameType) return;
    
    m_maxSelfHeap.add(pNode);
    m_sizeHeap.add(pNode);
}

void KSignalTravel::enter(const binary_wo::BuffName & name, bool isStruct)
{
    if (m_curNode == nullptr)
        m_curNode = m_pRoot;
    else
        m_curNode = createChildBinNode(name);
    
    m_curNode->setName(name);
    
    // state
    if (!isStruct)
    {
        BinNode *parent = m_curNode->parent();
        if (parent == m_pRoot)
        {
            updateCurNodeByEnter(BinNodeNameType::kVersionsArray, NameStrType::kVersions);
            updateCurNodeByEnter(BinNodeNameType::kNewObjArray, NameStrType::kNewObj);
            updateCurNodeByEnter(BinNodeNameType::kNewCustomObjArray, NameStrType::kNewCustomObj);
            updateCurNodeByEnter(BinNodeNameType::kOrgObjsArray, NameStrType::kOrgObjs);
        }
        else if (parent)
        {
            switch (parent->nameType())
            {
                case BinNodeNameType::kVersionItem:
                    updateCurNodeByEnter(BinNodeNameType::kVersionNewObjArray, NameStrType::kNewObj);
                    updateCurNodeByEnter(BinNodeNameType::kVersionOpsArray, NameStrType::kOps);
                    break;
            }
        }
    }
    else
    {
    }
}

bool KSignalTravel::isEqual(const binary_wo::BuffName & name, NameStrType type)
{
    const NameStrItem &item = g_NameStrItems[(int)type];
    if (name.nameLen() != item.m_nameLen) return false;
    return strncmp(name.nameArray(), item.m_name, item.m_nameLen) == 0;
}

bool KSignalTravel::updateCurNodeByEnter(BinNodeNameType nameType, NameStrType type)
{
    NameTravelState &st = m_state[(int)nameType];
    if (st != NameTravelState::kNone)
        return false;
    
    if (isEqual(m_curNode->name(), type))
    {
        st = NameTravelState::kIn;
        m_curNode->setNameType(nameType);
        return true;
    }
    return false;
}

void KSignalTravel::leave(int size)
{
    m_curNode->setSize(size);
    onSetSelfSize(m_curNode);
    
    if (m_curNode->objId())
    {// handle newObj
        auto itr = m_objParentMap.find(m_curNode->objId());
        if (itr != m_objParentMap.end())
        {
            if (!itr->second.empty())
                m_newObjMap.emplace(m_curNode->objId(), std::move(itr->second));
        }
        else
        {
            if (!m_curNode->isTopObj())
            {
                ++m_noRefNewObjCnt;
                m_noRefNewObjBytes += m_curNode->size();
            }
        }
        
        if (m_curNode->clsNameArray())
        {
            BinWebStr clsName {m_curNode->clsNameArray(), m_curNode->clsNameLen()};
            auto itr = m_clsNameMap.find(clsName);
            if (itr == m_clsNameMap.end())
            {
                m_clsNameMap[clsName] = {1, m_curNode->size()};
            }
            else
            {
                ++itr->second.m_cnt;
                itr->second.m_size += m_curNode->size();
            }
        }
    }
    
    if (m_curNode->nameType() != BinNodeNameType::kCommon)
        m_state[(int)m_curNode->nameType()] = NameTravelState::kOut;
    if (m_curNode->isNewObjArray()) // 
        m_objParentMap.clear();
    m_curNode = m_curNode->parent();
    if (m_curNode)
        m_curNode->incItemCnt(1);
}

void KSignalTravel::onBeginStruct(const binary_wo::BuffName & name)
{
    enter(name, true);
}

void KSignalTravel::onEndStruct(int size)
{
    leave(size);
}

void KSignalTravel::onBeginNameStruct(const binary_wo::BuffName & name, const binary_wo::BuffName & nameStruct)
{
    enter(name, true);
}

void KSignalTravel::onEndNameStruct(int size)
{
    leave(size);
}

void KSignalTravel::onBeginArray(const binary_wo::BuffName & name, int32 arrLen)
{
    enter(name, false);
}

void KSignalTravel::onEndArray(int size)
{
    leave(size);
}

void KSignalTravel::onValueInt8(const binary_wo::BuffName & name, int8 v)
{
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueUint8(const binary_wo::BuffName & name, uint8 v)
{
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueInt16(const binary_wo::BuffName & name, int16 v)
{
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueUint16(const binary_wo::BuffName & name, uint16 v)
{
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueInt32(const binary_wo::BuffName & name, int32 v)
{
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueUint32(const binary_wo::BuffName & name, uint32 v)
{
    handleNewObjId(name, v);
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueFloat32(const binary_wo::BuffName & name, float32 v)
{
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueFloat64(const binary_wo::BuffName & name, float64 v)
{
    handleNewObjId(name, v);
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueAbsObj32(const binary_wo::BuffName & name, uint32 v)
{
    onAddAbsObj(name, v);
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueAbsObj64(const binary_wo::BuffName & name, int64 v)
{
    onAddAbsObj(name, v);
    m_curNode->incItemCnt(1);
}

bool KSignalTravel::isNewObj(BinNode *pNode)
{
    BinNode *parent = m_curNode->parent();
    if (!parent) return false;
    return parent->isNewObjArray();
}

void KSignalTravel::handleNewObjId(const binary_wo::BuffName & name, int64 v)
{
    if (!m_curNode->isNewObjItem()) return;
    if (m_curNode->objId()) return;
    
    if (isEqual(name, NameStrType::kId))
        m_curNode->setObjId(v);
}

void KSignalTravel::handleNewObjBool(const binary_wo::BuffName & name, bool v)
{
    if (!m_curNode->isNewObjItem()) return;
    
    if (isEqual(name, NameStrType::kTop))
    {
        ++m_nNewObjTopCnt;
        m_curNode->setTopObj();
    }
}

void KSignalTravel::handleNewObjStr(const binary_wo::BuffName & name, const WCHAR * wszArray, uint32 len)
{
    if (!m_curNode->isNewObjItem()) return;
    if (m_curNode->clsNameArray()) return;
    
    if (isEqual(name, NameStrType::kClsName))
    {
        ++m_nNewObjClsNameCnt;
        m_curNode->setClsName(wszArray, len);
    }
}

void KSignalTravel::onAddAbsObj(const binary_wo::BuffName & name, int64 v)
{
//eg: versions.[].ops, versions.[].newObjs,  orgObjs, newObjs, newCustomObj
    auto itr = m_objParentMap.find(v);
    if (itr == m_objParentMap.end())
    {
        KObjParentInfoVec vc;
        vc.emplace_back(m_curNode, name);
        m_objParentMap.emplace(v, std::move(vc));
    }
    else
    {
        itr->second.emplace_back(m_curNode, name);
    }
}

void KSignalTravel::onValueBool(const binary_wo::BuffName & name, bool v)
{
    handleNewObjBool(name, v);
    m_curNode->incItemCnt(1);
}

void KSignalTravel::onValueString(const binary_wo::BuffName & name, const WCHAR * wszArray, uint32 len)
{
    handleNewObjStr(name, wszArray, len);
    m_curNode->incItemCnt(1);
}
void KSignalTravel::onValueNameString(const binary_wo::BuffName & name, const binary_wo::BuffName & v)
{
    m_curNode->incItemCnt(1);
}
void KSignalTravel::onValueUint8Array(const binary_wo::BuffName & name, uint8 *pArray, uint32 len)
{
    m_curNode->incItemCnt(1);
}
void KSignalTravel::onArrayItemString(const WCHAR * wszArray, uint32 len)
{
    m_curNode->incItemCnt(1);
}
void KSignalTravel::onArrayItemNameString(const binary_wo::BuffName & v)
{
    m_curNode->incItemCnt(1);
}
void KSignalTravel::onArrayItemValue(uint8 *pArray, uint32 len, binary_wo::Types tp)
{
    int objLen = len / binary_wo::s_typeLen[tp];
    if (tp == binary_wo::typeAbsObject64)
    {
        BuffName name("", tp, 0);
        double *pObjArray = (double*)pArray;
        for (int i = 0; i < objLen; ++i)
        {
            onAddAbsObj(name, pObjArray[i]);
        }
    }
    else if (tp == binary_wo::typeAbsObject32)
    {
        BuffName name("", tp, 0);
        uint32 *pObjArray = (uint32*)pArray;
        for (int i = 0; i < objLen; ++i)
        {
            onAddAbsObj(name, pObjArray[i]);
        }
    }
    m_curNode->incItemCnt(objLen);
}

bool KSignalTravel::needBreak()
{
    if (m_pBreak) return m_pBreak->needBreak();
    return false;
}
////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////
class KSignalWorker
{
public:
    explicit KSignalWorker(IWebSlice *pSlice, KSignalResult & res);
    binary_wo::ResultCode analyze(ISignalBreak *pBreak, std::shared_ptr<IWoSignalResultNotify>& spNotify);
    
private:
    void handleRes(KSignalTravel & travel, std::shared_ptr<IWoSignalResultNotify>& spNotify);
    
private:
    ks_stdptr<IWebSlice> m_spSlice;
    KSignalResult & m_res;
};

KSignalWorker::KSignalWorker(IWebSlice *pSlice, KSignalResult & res)
: m_spSlice(pSlice)
, m_res(res)
{
    
}

binary_wo::ResultCode KSignalWorker::analyze(ISignalBreak *pBreak, std::shared_ptr<IWoSignalResultNotify>& spNotify)
{
    std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
    const WebSlice * pSc = m_spSlice->Get();
    KSignalTravel travel(pBreak);
    binary_wo::KBinReaderEnum reader(pSc->data, pSc->size, &travel);
    binary_wo::ResultCode res = reader.read();
    if (res == binary_wo::ResultCode::kSucc)
        handleRes(travel, spNotify);    
    
    unsigned int ms = (std::chrono::steady_clock::now() - begin) / std::chrono::milliseconds(1);
    if (ms > 100)
    {
        WOLOG_INFO << "[signalMetrics] analyze time: " << ms;
    }
    return res;
}

void KSignalWorker::handleRes(KSignalTravel & travel, std::shared_ptr<IWoSignalResultNotify>& spNotify)
{
    // selfSize <path, size>
    m_res.m_selfSizeInfos = travel.makeTopSelfInfo();
    m_res.m_sizeInfos = travel.makeTopSizeInfo();
    m_res.m_newObjClsNameInfos = travel.makeTopNewObjCntInfo();
    m_res.m_noRefNewObjCnt = travel.getNoRefNewObjCnt();
    m_res.m_noRefNewObjBytes = travel.getNoRefNewObjBytes();
    m_res.m_nNewObjCnt = travel.getNewObjCnt();
    m_res.m_nNewObjClsNameCnt = travel.getNewObjClsNameCnt();
    m_res.m_nNewObjTopObjCnt = travel.getNewObjTopObjCnt();
    
    if (spNotify)
        spNotify->onSuccResult(m_res);
}
////////////////////////////////////////////////////////////////////////

enum class KSignalState
{
    kIdle,
    kRunning,
    kInterrupt, // 中断正在执行的任务，并停止
    kFinishStop, // 等待队列中所有任务完成并停止，还没入队的就中断。与kInterrupt互斥
    kStoped
};
// state change
// idle --(runTask)--> running --(finishQueueTask)--> idle
// running --(kInterrupt)--> interrupt --(完成中断)--> stoped
// running --(kFinishStop)--> finishStop --(完成等待)--> stoped
// idle --(kInterrupt/kFinishStop)--> stoped
// interrupt --(kInterrupt/kFinishStop)--> interrupt --(完成中断)--> stoped
// finishStop --(kInterrupt/kFinishStop)--> finishStop --(完成等待)--> stoped

struct KSignalMcTask
{
    ks_stdptr<IWebSlice> m_spWebSlice;
    std::shared_ptr<IWoSignalResultNotify> m_spNotify;
    ks_wstring m_traceId;
    wo::MsgType m_msgType;
};

class KSignalMetricsData : public ISignalBreak
{
public:
    KSignalMetricsData();
    
    std::deque<KSignalMcTask> m_queue;
    std::vector<KSignalResult> m_resVec;
    std::mutex m_mutex;
    std::mutex m_resMutex;
    std::condition_variable m_cv;
    KSignalState m_state;
    int m_maxSize;
    int m_minSize;
    int m_maxCnt;
    int m_collectCnt;
    bool m_enable;
    
    IWoSignalMcNotify *notify() { return m_spNotify.get(); }
    void setNotify(std::shared_ptr<IWoSignalMcNotify> spNotify);
    bool needBreak() override { return m_atomBreak; }
    void setBreak(bool v) { m_atomBreak = v; }
private:
    std::shared_ptr<IWoSignalMcNotify> m_spNotify;
    std::shared_ptr<IWoSignalMcNotify> m_spDefNotify;
    std::atomic<bool> m_atomBreak;
};

KSignalMetricsData::KSignalMetricsData()
: m_enable(false)
, m_minSize(DEFAULT_MIN_METRIC_SIGNAL_SIZE)
, m_maxSize(DEFAULT_MAX_METRIC_SIGNAL_SIZE)
, m_state(KSignalState::kIdle)
, m_maxCnt(1)
, m_collectCnt(0)
, m_atomBreak(false)
{
    if (_kso_GetWoEtSettings() && _kso_GetWoEtSettings()->IsEnableMetricSignalData())
    {
        m_enable = true;
        _kso_GetWoEtSettings()->GetMetricSignalRange(m_minSize, m_maxSize);
        m_maxCnt = _kso_GetWoEtSettings()->GetMetricSignalMaxCnt();
    }
    setNotify(nullptr);
}

class KDefWoSignalMcNotify : public IWoSignalMcNotify
{
public:
    void onNotify(WoSignalMcNotifyCode code, long param) override {}
};

void KSignalMetricsData::setNotify(std::shared_ptr<IWoSignalMcNotify> spNotify)
{
    if (spNotify)
    {
        m_spNotify = spNotify;
    }
    else
    {
        if (!m_spDefNotify)
            m_spDefNotify = std::make_shared<KDefWoSignalMcNotify>();
        m_spNotify = m_spDefNotify;
    }
}
////////////////////////////////////////////////////////////////////////

KSignalMetrics::KSignalMetrics()
: m_spData(std::make_unique<KSignalMetricsData>())
{
}

KSignalMetrics::~KSignalMetrics()
{
}

void KSignalMetrics::setEnable(bool v)
{
    m_spData->m_enable = v;
}

bool KSignalMetrics::isEnable()
{
    return m_spData->m_enable;
}

void KSignalMetrics::setMinCollectSize(int sz)
{
    m_spData->m_minSize = sz;
}
    
bool KSignalMetrics::isNeedMetrics(WebSize sliceSize, wo::MsgType msgType, WebProcType procType)
{
    if (!m_spData->m_enable) return false;
    if (procType != WebProcTypeMaster) return false;
    
    bool isValidMsg = false;
    switch (msgType)
    {
#ifdef _DEBUG
        case wo::msgType_Exec:
        case wo::msgType_Query:
#endif
        case wo::msgType_Init:
            isValidMsg = true;
        break;
    }
    if (!isValidMsg) return false;
    if (sliceSize < m_spData->m_minSize || sliceSize > m_spData->m_maxSize)
        return false;
    
    return m_spData->m_collectCnt < m_spData->m_maxCnt;
}

bool KSignalMetrics::collectMetricData(IWebSlice *pSlice, wo::MsgType msgType, WebProcType procType, PCWSTR traceId, std::shared_ptr<IWoSignalResultNotify> spNotify)
{
    if (!pSlice || !isNeedMetrics(pSlice->Get()->size, msgType, procType))
    {
        if (spNotify) spNotify->onResultCode(binary_wo::ResultCode::kFail);
        return false;
    }
    
    // start task
    {
        ++m_spData->m_collectCnt;
        std::lock_guard<std::mutex> guard(m_spData->m_mutex);
        
        if (m_spData->m_state != KSignalState::kRunning && 
            m_spData->m_state != KSignalState::kIdle)
        {
            if (spNotify) spNotify->onResultCode(binary_wo::ResultCode::kInterrupt);
            return false;
        }

        m_spData->m_queue.push_back(KSignalMcTask{pSlice, spNotify, traceId, msgType});
        
        // start thread
        if (m_spData->m_state == KSignalState::kIdle)
        {
            m_spData->m_state = KSignalState::kRunning;
            std::thread th(KSignalMetrics::threadProc, m_spData);
            th.detach();
        }
    }
    return true;
}

void KSignalMetrics::threadProc(const std::shared_ptr<KSignalMetricsData> &spData)
{
    while (true) 
    {
        KSignalMcTask task;
        spData->notify()->onNotify(WoSignalMcNotifyCode::kBeforeRunTask, 0);
        {// get task
            std::lock_guard<std::mutex> guard(spData->m_mutex);
            if (spData->m_state != KSignalState::kRunning)
            {
                ASSERT(spData->m_state == KSignalState::kInterrupt || spData->m_state == KSignalState::kFinishStop);
            }
            
            if (spData->m_state == KSignalState::kInterrupt)
            {
                for (const auto & t : spData->m_queue)
                {
                    if (t.m_spNotify)
                        t.m_spNotify->onResultCode(binary_wo::ResultCode::kInterrupt);
                }
                spData->m_queue.clear();
            
                spData->m_state = KSignalState::kStoped;
                spData->m_cv.notify_all();
                break;
            }
             
            if (!spData->m_queue.empty())
            {
                task = spData->m_queue.front();
                spData->m_queue.pop_front();
                spData->notify()->onNotify(WoSignalMcNotifyCode::kAfterGetTask, 0);
            }
            else
            {
                if (spData->m_state == KSignalState::kFinishStop)
                {
                    spData->m_state = KSignalState::kStoped;
                    spData->m_cv.notify_all();
                }
                else
                {
                    spData->m_state = KSignalState::kIdle;
                }
                break;
            }
        }
        
        // exec task
        if (task.m_spWebSlice)
        {
            spData->notify()->onNotify(WoSignalMcNotifyCode::kBeforeTaskExec, 0);
            KSignalResult res(task.m_traceId, task.m_msgType, task.m_spWebSlice->Get()->size);
            KSignalWorker worker(task.m_spWebSlice, res);
            binary_wo::ResultCode retCode = worker.analyze(spData.get(), task.m_spNotify);
            spData->notify()->onNotify(WoSignalMcNotifyCode::kAfterTaskExec, (int)retCode);
            
            if (task.m_spNotify)
                task.m_spNotify->onResultCode(retCode);
            
            {// save result
                std::lock_guard<std::mutex> guard(spData->m_resMutex);
                spData->m_resVec.push_back(std::move(res));
            }
        }
    }
}

void KSignalMetrics::interrupt()
{
    if (!m_spData->m_enable) return;
    
    std::lock_guard<std::mutex> guard(m_spData->m_mutex);
    switch (m_spData->m_state)
    {
        case KSignalState::kIdle:
            m_spData->m_state = KSignalState::kStoped;
            break;
        case KSignalState::kRunning:
            m_spData->m_state = KSignalState::kInterrupt;
            m_spData->setBreak(true);
            break;
    }
}

void KSignalMetrics::interruptAndWait()
{
    if (!m_spData->m_enable) return;
    
    std::unique_lock<std::mutex> lock(m_spData->m_mutex);
    switch (m_spData->m_state)
    {
        case KSignalState::kIdle:
            m_spData->m_state = KSignalState::kStoped;
            break;
        case KSignalState::kRunning:
            m_spData->m_state = KSignalState::kInterrupt;
            m_spData->setBreak(true);
            m_spData->m_cv.wait(lock);
            break;
        case KSignalState::kInterrupt:
        case KSignalState::kFinishStop:
            m_spData->m_cv.wait(lock);
            break;
    }
}

void KSignalMetrics::finishAndWait()
{
    if (!m_spData->m_enable) return;
    
    std::unique_lock<std::mutex> lock(m_spData->m_mutex);
    switch (m_spData->m_state)
    {
        case KSignalState::kIdle:
            m_spData->m_state = KSignalState::kStoped;
            break;
        case KSignalState::kRunning:
            m_spData->m_state = KSignalState::kFinishStop;
            m_spData->m_cv.wait(lock);
            break;
        case KSignalState::kInterrupt:
        case KSignalState::kFinishStop:
            m_spData->m_cv.wait(lock);
            break;
    }
}

void KSignalMetrics::sendCollectInfo(KEtWorkbook *pWoWb)
{
    std::vector<KSignalResult> res;
    {
        std::lock_guard<std::mutex> guard(m_spData->m_resMutex);
        m_spData->m_resVec.swap(res);
    }
    
    if (res.empty()) return;
    
    for (const auto & item : res)
    {
        sendRes(item, pWoWb);
    }
}

void KSignalMetrics::setNotify(std::shared_ptr<IWoSignalMcNotify> spNotify)
{
    m_spData->setNotify(spNotify);
}

enum class ColSizeNameType
{
    kInvalid,
    kSelfPath,
    kSelfSize,
    kSelfItemCnt,
    kPath,
    kSize,
    kItemCnt,
    kNewObjClsName,
    kNewObjClsNameCnt,
    kNewObjClsNameSize,
    
    _kCount
};

struct ColSizeName
{
    const char * m_colName[(int)ColSizeNameType::_kCount];
};

#define SIZE_NAME(n) ColSizeName{"", "self_path_"#n, "self_sizekb_"#n,  "self_itemcnt_"#n, \
    "path_"#n, "sizekb_"#n, "itemcnt_"#n, \
    "clsname_"#n, "clsname_cnt_"#n, "clsname_sizekb_"#n}
constexpr ColSizeName g_sizeName[] = 
{
    SIZE_NAME(1), SIZE_NAME(2), SIZE_NAME(3), SIZE_NAME(4), SIZE_NAME(5),
    SIZE_NAME(6), SIZE_NAME(7), SIZE_NAME(8), SIZE_NAME(9), SIZE_NAME(10),
};

QString makeQString(const std::string & s)
{
    return krt::fromUtf8(s.c_str(), s.length());
}

QString makeQString(const ks_wstring & s)
{
    return krt::fromUtf16(s.c_str(), s.length());
}

template <typename TFir, typename TSec>
void collectInfos(
    wo::WoCollectInfo &collect, 
    const std::vector<std::pair<TFir, TSec>> & infos,
    ColSizeNameType strKeyType,
    ColSizeNameType sizeKeyType,
    ColSizeNameType cntKeyType,
    const char * szDefStrKey,
    const char * szDefCntPrefix)
{
    static_assert(std::is_same_v<TSec, int> || std::is_same_v<TSec, KCntSize>, "need add handle.");
    
    int sz = std::min(sizeof(g_sizeName) / sizeof(g_sizeName[0]), infos.size());
    for (int i = 0; i < sz; ++i)
    {
        QString strVal = makeQString(infos[i].first);
        const char * szStrKey = g_sizeName[i].m_colName[(int)strKeyType];
        collect.addString(szStrKey, strVal);
        
        const char * szSizeKey = g_sizeName[i].m_colName[(int)sizeKeyType];
        const char * szCntKey = g_sizeName[i].m_colName[(int)cntKeyType];
        if constexpr (std::is_same_v<TSec, int>)
        {
            bool isSize = sizeKeyType != ColSizeNameType::kInvalid;
            const char * szKey = isSize ? szSizeKey : szCntKey;
            int val = isSize ? (infos[i].second >> 10) : infos[i].second;
            collect.addInt32(szKey, val);
            
            WOLOG_INFO << "[signalMetrics] " << szKey << ": " << val 
            << " " << szStrKey << ": " << strVal;
        }
        else if constexpr (std::is_same_v<TSec, KCntSize>)
        {
            ASSERT(sizeKeyType != ColSizeNameType::kInvalid && cntKeyType != ColSizeNameType::kInvalid);
            collect.addInt32(szSizeKey, (infos[i].second.m_size >> 10));
            collect.addInt32(szCntKey, infos[i].second.m_cnt);
            WOLOG_INFO << "[signalMetrics] " << szCntKey << ": " << infos[i].second.m_cnt
                << " " << szSizeKey << ": " << (infos[i].second.m_size >> 10)
                << " " << szStrKey << ": " << strVal;
        }
    }
    
    for (int i = sz, infosSz = infos.size(); i < infosSz; ++i)
    {
        QString strVal = makeQString(infos[i].first);
        int idx = i + 1;
        
        const char * szSizeKey = "sizekb";
        const char * szCntKey = "cnt";
        if constexpr (std::is_same_v<TSec, int>)
        {
            bool isSize = sizeKeyType != ColSizeNameType::kInvalid;
            const char * szKey = isSize ? szSizeKey : szCntKey;
            int val = isSize ? (infos[i].second >> 10) : infos[i].second;
            WOLOG_INFO << "[signalMetrics] " << szKey << "_" << idx << ": " << val 
                << " " << szDefStrKey << "_" << idx << ": " << strVal;
        }
        else if constexpr (std::is_same_v<TSec, KCntSize>)
        {
            WOLOG_INFO << "[signalMetrics] " << szDefCntPrefix << szCntKey << "_" << idx << ": " << infos[i].second.m_cnt
                << " " << szSizeKey  << "_" << idx << ": " << (infos[i].second.m_size >> 10)
                << " " << szDefStrKey << ": " << strVal;
        }
    }
}

void KSignalMetrics::sendRes(const KSignalResult & res, KEtWorkbook *pWoWb)
{
    wo::WoCollectInfo collect;
    collect.addString("fileid", pWoWb->getFileId());
    collect.addComponentInfo(pWoWb->GetBMP());
    collect.addAppVersion();
    collect.addString("traceid", res.m_traceId);
    collect.addInt32("msg_type", res.m_msgType);
    collect.addInt32("value_kb", res.m_signalSizeKb);
    collect.addInt32("noref_newobj_cnt", res.m_noRefNewObjCnt);
    collect.addInt32("noref_newobj_kb", (res.m_noRefNewObjBytes >> 10));
    collect.addInt32("newobj_cnt", res.m_nNewObjCnt);
    collect.addInt32("newobj_clsname_cnt", res.m_nNewObjClsNameCnt);
    WOLOG_INFO << "[signalMetrics] traceid: " << res.m_traceId << ", msgType: " << res.m_msgType
        << ", sizeKb: " << res.m_signalSizeKb 
        << ", noRefNewObjKb: " << (res.m_noRefNewObjBytes >> 10) << ", noRefNewObjCnt: " << res.m_noRefNewObjCnt
        << ", newObjCnt: " << res.m_nNewObjCnt << ", newObjTopObjCnt: " << res.m_nNewObjTopObjCnt
        << ", newObjClsNameCnt: " << res.m_nNewObjClsNameCnt;

    collectInfos(collect, 
        res.m_selfSizeInfos, 
        ColSizeNameType::kSelfPath,
        ColSizeNameType::kSelfSize,
        ColSizeNameType::kSelfItemCnt,
        "self_path",
        "self_item");
        
    collectInfos(collect, 
        res.m_sizeInfos, 
        ColSizeNameType::kPath,
        ColSizeNameType::kSize,
        ColSizeNameType::kItemCnt,
        "path",
        "item");

    collectInfos(collect, 
        res.m_newObjClsNameInfos, 
        ColSizeNameType::kNewObjClsName,
        ColSizeNameType::kNewObjClsNameSize,
        ColSizeNameType::kNewObjClsNameCnt,
        "clsname",
        "clsname_");

    collect.collect("svr_coresignal");
}

}