#ifndef __WEBET_ATTACHMENT_HELPER_H__
#define __WEBET_ATTACHMENT_HELPER_H__

namespace wo
{
class KEtRevisionContext;

struct AttachmentItem
{
	std::vector<ks_wstring> vecIds;
	std::vector<ks_wstring> vecIdsVideo;

	bool empty() const
	{
		return vecIds.empty() && vecIdsVideo.empty();
	}

};

class CloudImgAttachmentCollector
{
public:
	CloudImgAttachmentCollector(KEtRevisionContext* pCtx)
		: m_pCtx(pCtx)
	{
	}

	void begin(const ks_wstring& srcFileId);
	void end();
	void filloutAttachmentIds(bool bSucceed);
	void setFileTagByPicAsAttachments(bool isAttachments);

private:
	KEtRevisionContext* m_pCtx;
	ks_wstring m_curSrcFileId;
	std::map<ks_wstring, AttachmentItem> m_IdContainer;

};

} // end namespace wo

#endif // __WEBET_ATTACHMENT_HELPER_H__