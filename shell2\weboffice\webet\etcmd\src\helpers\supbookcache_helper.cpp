﻿#include "etstdafx.h"
#include "supbookcache_helper.h"
#include "webbase/serialize_impl.h"
#include "webbase/wo_sa_helper.h"
#include "wo/sa_helper.h"

namespace wo
{
	SyncClientSupBookCacheHelper::SyncClientSupBookCacheHelper(KEtRevisionContext* pCtx, KwCommand* cmd, const HRESULT& hr)
		: m_pCtx(pCtx)
		, m_hr(hr)
		, m_pCmd(cmd)
	{
		m_pCtx->resetSyncSupBookCacheCtx(true);
	}

	SyncClientSupBookCacheHelper::~SyncClientSupBookCacheHelper()
	{
		const wo::SupBookCacheUpdateInfos& infos = m_pCtx->getSupBookCacheUpdateInfos();
		if (SUCCEEDED(m_hr) && !infos.empty())
		{
			binary_wo::VarObj param = m_pCmd->cast().get("param");
			binary_wo::BinWriter::StreamHolder holder(nullptr);
			int32 length = 0;
			buildCacheStream(infos, holder, length);
			if (length > 0)
			{
				if (param.has("idSyncClientSupBook"))
					m_pCtx->getBook()->GetWoStake()->RemoveSyncClientSupBookData(param.field_uint32("idSyncClientSupBook"));

				UINT32 id = m_pCtx->getBook()->GetWoStake()->AddSyncClientSupBookData(holder.get(), (UINT32)length);
				param.add_field_uint32("idSyncClientSupBook", id);
			}

			m_pCmd->setHasExtra();
			m_pCtx->setIsRealTransform(true);
		}

		m_pCtx->resetSyncSupBookCacheCtx(false);
	}

	void SyncClientSupBookCacheHelper::buildCacheStream(const wo::SupBookCacheUpdateInfos& infos,
		binary_wo::BinWriter::StreamHolder& holder, int32& length)
	{
		binary_wo::BinWriter bw;
		KSerialWrapBinWriter acpt(bw, m_pCtx);
		serialCache(infos, &acpt);

		holder = bw.buildStream();
		length = bw.writeLength();
	}

	void SyncClientSupBookCacheHelper::serialCache(const wo::SupBookCacheUpdateInfos& infos, ISerialAcceptor* acpt)
	{
		ks_stdptr<ISupBooks> spSupBooks;
		m_pCtx->getBook()->GetSupBooks(&spSupBooks);
		INT nCnt = 0;
		spSupBooks->GetCount(&nCnt);

		sa::Leave arrLeave = sa::enterArray(acpt, "supBooks");
		for (INT i = 1; i < nCnt; ++i)
		{
			ks_stdptr<ISupBook> spSupBook;
			spSupBooks->GetSupBook(i, &spSupBook);
			auto it = infos.find(spSupBook->getNetFileId());
			if (it == infos.end())
				continue;

			sa::Leave structLeave = sa::enterStruct(acpt, nullptr);
			const wo::SupBookCacheUpdateInfo& info = it->second;

			bool bFileProtected = spSupBook->IsFileProtected();
			const WCHAR* pName = NULL;
			spSupBook->GetName(&pName);
			acpt->addString("name", pName);
			acpt->addString("fileId", spSupBook->getNetFileId());
			acpt->addString("netDiskName", spSupBook->getNetDisk());
			if (spSupBook->GetSourceCompatibleMode())
				acpt->addBool("isCompatible", true);
			if (spSupBook->IsSecurityDoc())
				acpt->addBool("isSecurityDoc", true);

			if (bFileProtected)
			{
				acpt->addBool("isFileProtected", true);
			}
			else
			{
				serialSheetNames(acpt, spSupBook);
				serialDefNames(acpt, spSupBook, info.names);
				serialSheetsData(acpt, spSupBook, info);
			}
		}
	}

	void SyncClientSupBookCacheHelper::serialSheetNames(ISerialAcceptor* acpt, ISupBook* pSupBook)
	{
		sa::Leave arrLeave = sa::enterArray(acpt, "sheetNames");

		INT nCnt = 0;
		pSupBook->GetValidSheetCount(&nCnt);
		for (INT i = 0; i < nCnt; ++i)
		{
			const WCHAR* pName = NULL;
			pSupBook->GetSheetName(i, &pName);
			acpt->addString(NULL, pName);
		}
	}

	void SyncClientSupBookCacheHelper::serialDefNames(ISerialAcceptor* acpt, ISupBook* pSupBook,
		const std::vector<IDX>& defNameIds)
	{
		if (defNameIds.empty())
			return;

		sa::Leave arrLeave = sa::enterArray(acpt, "defNames");
		for (auto it = defNameIds.begin(); it != defNameIds.end(); ++it)
		{
			IDX iSheet = INVALIDIDX;
			PCWSTR pcwMoniker = NULL;
			alg::managed_token_assist rgt;
			if (FAILED(pSupBook->GetNameRefInfo(*it, &iSheet, &pcwMoniker, NULL, NULL)))
			{
				ASSERT(FALSE);
				continue;
			}
			pSupBook->GetNameRefContent(*it, &rgt);

			{
				sa::Leave structLeave = sa::enterStruct(acpt, nullptr);
				acpt->addInt32("sheetId", iSheet);
				acpt->addString("moniker", pcwMoniker);

				if (rgt.is_valid())
				{
					if (alg::ETP_STREF == rgt.major_type())
						serialCellRegion(acpt, rgt);	//理论上目前只会是单元格或区域的情况
					else
						wo::sa::addToken(acpt, "val", rgt);
				}
			}
		}
	}

	void SyncClientSupBookCacheHelper::serialCellRegion(ISerialAcceptor* acpt, const_token_ptr pTok)
	{
		ASSERT(alg::const_stref_token_assist::is_type(pTok));
		alg::const_stref_token_assist rcs(pTok);
		if (rcs.is_cell())
		{
			sa::Leave structLeave = sa::enterStruct(acpt, "cell");
			acpt->addInt32("flags", rcs.get_flags());
			acpt->addInt32("bookId", rcs.get_book_id());
			acpt->addInt32("sheetId", rcs.get_sheet_id());
			acpt->addInt32("row", rcs.get_cell_row());
			acpt->addInt32("col", rcs.get_cell_col());
		}
		else if (rcs.is_region())
		{
			sa::Leave structLeave = sa::enterStruct(acpt, "region");
			acpt->addInt32("flags", rcs.get_flags());
			acpt->addInt32("bookId", rcs.get_book_id());
			acpt->addInt32("beginSheet", rcs.get_begin_sheet());
			acpt->addInt32("endSheet", rcs.get_end_sheet());
			acpt->addInt32("beginRow", rcs.get_begin_row());
			acpt->addInt32("endRow", rcs.get_end_row());
			acpt->addInt32("beginCol", rcs.get_begin_col());
			acpt->addInt32("endCol", rcs.get_end_col());
		}
		else
		{
			ASSERT(FALSE);
		}
	}

	void SyncClientSupBookCacheHelper::serialSheetsData(ISerialAcceptor* acpt, ISupBook* pSupBook,
		const wo::SupBookCacheUpdateInfo& info)
	{
		sa::Leave arrLeave = sa::enterArray(acpt, "sheetsData");

		INT nCnt = 0;
		pSupBook->GetValidSheetCount(&nCnt);
		for (INT iSht = 0; iSht < nCnt; ++iSht)
		{
			sa::Leave arrLeave2 = sa::enterArray(acpt, nullptr);
			auto itShtData = info.sheetsData.find(iSht);
			if (itShtData == info.sheetsData.end())
				continue;

			for (auto itRowData = itShtData->second.begin(); itRowData != itShtData->second.end(); itRowData++)
			{
				sa::Leave structLeave = sa::enterStruct(acpt, nullptr);
				serialRowData(acpt, pSupBook, iSht, itRowData->first, itRowData->second);
			}
		}
	}

	void SyncClientSupBookCacheHelper::serialRowData(ISerialAcceptor* acpt, ISupBook* pSupBook, IDX iSht, ROW r,
		const wo::SupBookCacheUpdateInfo::ROWDATA& rowData)
	{
		acpt->addInt32("row", r);
		sa::Leave arrLeave = sa::enterArray(acpt, "cols");;
		for (auto it = rowData.begin(); it != rowData.end(); ++it)
		{
			sa::Leave structLeave = sa::enterStruct(acpt, nullptr);
			acpt->addInt32("col", *it);
			const_token_ptr pTok = NULL;
			pSupBook->GetCellValue(iSht, r, *it, &pTok);
			if (pTok)
				wo::sa::addToken(acpt, "val", pTok);
		}
	}

	bool SyncClientSupBookCacheHelper::addCacheStream(binary_wo::VarObj& param, IBookStake* pBookStake)
	{
		if (!param.has("idSyncClientSupBook"))
			return true;

		uint32 id = param.field_uint32("idSyncClientSupBook");
		const byte* data = nullptr;
		UINT32 size = 0;
		if (FAILED(pBookStake->GetSyncClientSupBookData(id, &data, &size)))
			return false;

		ASSERT(data && size > 0);
		binary_wo::VarObj arr = param.add_field_array("syncSupBookCacheStream", binary_wo::typeUint8);
		arr.add_item_uint8(data, size);

		return true;
	}

	bool SyncClientSupBookCacheHelper::addCache(binary_wo::VarObj& param, IBookStake* pBookStake)
	{
		return wo::SyncClientSupBookCacheHelper::addCacheStream(param, pBookStake);
	}
}
