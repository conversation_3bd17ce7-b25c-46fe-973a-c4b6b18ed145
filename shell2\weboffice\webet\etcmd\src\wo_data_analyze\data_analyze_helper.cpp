﻿#include "etstdafx.h"
#include "wo_sa_helper.h"
#include "data_analyze.h"
#include "data_analyze_helper.h"
#include "dataanalyze/et_dataanalyze_helper.h"
#include "kso/l10n/et/etshell.h"
#include "webbase/serialize_impl.h"
namespace wo
{
    void KNotEmptyColsSet::SetNotEmptyCol(int col) 
    {
        if ((size_t)col >= m_notEmptyCols.size())
        {
            m_notEmptyCols.resize(col + 1, false);
        }
        m_notEmptyCols[col] = true;
		m_lastNotEmptyCol = col > m_lastNotEmptyCol ? col : m_lastNotEmptyCol;
		m_firstNotEmptyCol = col < m_firstNotEmptyCol ? col : m_firstNotEmptyCol;
    }
    bool KNotEmptyColsSet::IsSkipCol(int col)
    {
        if (col < 0)
            return true;
        col += m_fromColIdx;
        if ((size_t)col >= m_notEmptyCols.size())
			return true;
		return !m_notEmptyCols[col] && col < m_firstNotEmptyCol && col > m_lastNotEmptyCol;
    }
    bool KNotEmptyColsSet::HasNotEmptyCol()
    {
        for (auto i : m_notEmptyCols)
        {
            if (i)
                return true;
        }
        return false;
    }
    void KNotEmptyColsSet::SetColFrom(int fromColIdx)
    {
        m_fromColIdx = fromColIdx;
    }
    
    BOOL EmptyColAcceptor::Do(ROW row, COL col, const_token_ptr pToken)
    {
        if (pToken)
        {
            m_notEmptyColsSet.SetNotEmptyCol(col);
        }
        return 0;
    }
    KNotEmptyColsSet& EmptyColAcceptor::getNotEmptyCols()
    {
        return m_notEmptyColsSet;
    }

    QString GetColNamePrefix(const QString& bookName, const QString& sheetName)
    {
        const QString separator("_");
        QString prefix = QString("%1%2%3%4").arg(bookName, separator, sheetName, separator);
        return prefix;
    }

    QString GetAutoCompletColNameForEmptyCol(int sourceIndex, int emptyColIdx, const QString& tableText)
    {
        QString colName = QString("%1%2_%3").arg(tableText).arg(QString::number(sourceIndex)).arg(QString::number(emptyColIdx));
        return colName;
    }

    etda::MergeType ParseMergeDirection(PCWSTR direction)
    {
        if (xstrcmp(direction, __X("join")) == 0)
            return etda::MergeType::Join;
        else if (xstrcmp(direction, __X("union")) == 0)
            return etda::MergeType::Union;
        return etda::MergeType::Join;
    }

    etda::JoinMode ParseJoinModeStr(PCWSTR joinMode)
    {
        if (xstrcmp(joinMode, __X("left")) == 0)
            return etda::JoinMode::LeftJoin;
        else if (xstrcmp(joinMode, __X("right")) == 0)
            return etda::JoinMode::RightJoin;
        else if (xstrcmp(joinMode, __X("outer")) == 0)
            return etda::JoinMode::OuterJoin;
        else if (xstrcmp(joinMode, __X("inner")) == 0)
            return etda::JoinMode::InnerJoin;
        return etda::JoinMode::InnerJoin;
    }

    PCWSTR ParseJoinModeEnum(etda::JoinMode joinMode)
    {
        switch (joinMode)
        {
        case etda::JoinMode::LeftJoin:
            return __X("left join");
        case etda::JoinMode::RightJoin:
            return __X("right join");
        case etda::JoinMode::OuterJoin:
            return __X("outer join");
        case etda::JoinMode::InnerJoin:
            return __X("inner join");
        default:
            return __X("inner join");
        }
    }   
  
    struct StrEqual
    {
        bool operator()(const ks_wstring& a, const ks_wstring& b) const
        {
            return xstrcmp(a.c_str(), b.c_str()) == 0;
        }
    };

    struct StrHasher
    {
        size_t operator()(const ks_wstring& userId) const
        {
            return alg::HashWString(userId.c_str());
        }
    };

    void processMatrixColName(std::vector<ks_wstring>& headerNameWithAutoCompleteEmptyVec,  const int sourceIndex, std::vector<std::vector<ks_wstring>>& matrix, KNotEmptyColsSet& notEmptyColsSet, PCWSTR tableText)  
    {
        if (matrix.empty())
            return;

        std::unordered_map<ks_wstring, int, StrHasher, StrEqual> colNameMap;
        int skipCount = 0;
        for(auto j = 0; j < matrix[0].size(); j++)
        {
            if (notEmptyColsSet.IsSkipCol(j))
            {
                skipCount++;
                continue;
            }
            int duplicateNameCount = 0;
            ks_wstring newColName;
            int colIdx = j - skipCount;
            if(matrix[0][j].empty())
            {  
                QString emptyColsName = wo::GetAutoCompletColNameForEmptyCol(sourceIndex, colIdx, krt::fromUtf16(tableText));
                newColName = krt::utf16(emptyColsName);
            }  
            else
            {
                QString colName = krt::fromUtf16(matrix[0][j].c_str());
                colName = colName.toLower();
                newColName = krt::utf16(colName);
            }
            
            duplicateNameCount = ++colNameMap[newColName];
            if (duplicateNameCount > 1)
            {
                ks_wstring tmp = newColName;
                tmp.AppendFormat(__X("_%d"), duplicateNameCount);
                while(colNameMap.find(tmp) != colNameMap.end())
                {
                    duplicateNameCount++;
                    tmp = newColName;
                    tmp.AppendFormat(__X("_%d"), duplicateNameCount);
                }
                newColName = tmp;
                ++colNameMap[newColName];
            }
            headerNameWithAutoCompleteEmptyVec.emplace_back(newColName);
        }
    }
    
    void exportRangeData(KNotEmptyColsSet& notEmptyColsSet,const std::vector<std::vector<ks_wstring>>& matrix, const std::vector<ks_wstring>& headerNameWithAutoCompleteEmptyVec, QSet<int>& hideColsList,KSerialWrapBinWriter& acpt)  
    {
        {
            wo::sa::Leave recArr(wo::sa::enterArray(&acpt, "rangeData"));
            if (notEmptyColsSet.HasNotEmptyCol())
            {
                for(auto i = 0; i < matrix.size(); i++)
                {
                    wo::sa::Leave subArr(wo::sa::enterArray(&acpt, nullptr));
                    for(auto j = 0; j < matrix[i].size(); j++)
                    {
                        if (notEmptyColsSet.IsSkipCol(j))
                            continue;
                        if (hideColsList.find(j) == hideColsList.end())
                            acpt.addString(nullptr, matrix[i][j].c_str());
                    }
                }
            }
        }
        {
            wo::sa::Leave recArr(wo::sa::enterArray(&acpt, "headerNameWithAutoCompleteEmpty"));
            for(auto colIdx = 0; colIdx < headerNameWithAutoCompleteEmptyVec.size(); colIdx++)
            {
                if (hideColsList.find(colIdx) == hideColsList.end())
                {
                    acpt.addString(nullptr, headerNameWithAutoCompleteEmptyVec[colIdx].c_str());
                }
            }
        }
    }
}