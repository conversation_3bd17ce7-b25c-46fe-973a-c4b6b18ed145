﻿#ifndef __DB_JSON_DATA_HELPER__H__
#define __DB_JSON_DATA_HELPER__H__

namespace wo
{

    enum DBJsonDataType
    {
        DBJsonDataType_Str = 0,
        DBJsonDataType_Time,
        DBJsonDataType_Num,
        DBJsonDataType_Sel,
        DBJsonDataType_Url,
        DBJsonDataType_Usr,
        DBJsonDataType_Addr,
        DBJsonDataType_Atta,
        DBJsonDataType_Note,
        DBJsonDataType_Link,
        DBJsonDataType_Dept,
    };

    struct DBJsonDataUserItem
    {
        ks_wstring id;
        ks_wstring name;
        ks_wstring avatar;
        ks_wstring companyId;
    };

    struct DBJsonDataAttachmentItem
    {
        ks_wstring id;
        ks_wstring type;
        int size;
        ks_wstring imageSize;
        ks_wstring link;
        int source;
        ks_wstring name;
    };

    struct DBJsonDataNoteItem
    {
        ks_wstring attachmentId;
        ks_wstring shortSummary;
        double modifyDate = 0.0;
    };

    class DBJsonDataBaseObject
    {
    public:
        virtual ~DBJsonDataBaseObject() {}
        virtual DBJsonDataType GetType() { return DBJsonDataType_Str; }
        virtual void GetStringValue(ks_wstring &value) {}
        virtual void SerialContent(QJsonObject& jsonObj) {}
    public:
        void SetIsBlank(BOOL isBlk) { m_bBlank = isBlk; }
        BOOL IsBlank() { return m_bBlank; }
    protected:
        BOOL m_bBlank;
    };

    class DBJsonDataString : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataString() {}
        DBJsonDataString(QJsonObject jsonObj);

        void SetStringValue(PCWSTR value) { m_value = value; }
    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Str; }
        void GetStringValue(ks_wstring &value) override { value = m_value; }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        ks_wstring m_value;
    };

    class DBJsonDataTime : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataTime() {}
        DBJsonDataTime(QJsonObject jsonObj);

        void SetStringValue(PCWSTR value) { m_value = value; }
        void SetDoubleValue(double d) { m_d = d; }
        double GetDoubleValue() { return m_d; }

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Time; }
        void GetStringValue(ks_wstring &value) override { value = m_value; }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        double m_d;
        ks_wstring m_value;
    };

    class DBJsonDataNumber : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataNumber() {}
        DBJsonDataNumber(QJsonObject jsonObj);

        void SetStringValue(PCWSTR value) { m_value = value; }
        void SetRealNumber(double d) { m_d = d; }
        double GetRealNumber() { return m_d; }

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Num; }
        void GetStringValue(ks_wstring &value) override { value = m_value; }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        double m_d;
        ks_wstring m_value;
    };

    class DBJsonDataSelect : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataSelect() {}
        DBJsonDataSelect(QJsonObject jsonObj);

        void AddSelectItem(PCWSTR str) { m_selects.push_back(ks_wstring(str)); }
        int GetSelectItemsCount() { return m_selects.size(); }
        PCWSTR GetSelectItem(int index) { if (0 <= index && index < m_selects.size()) { return m_selects.at(index).c_str(); } return NULL; }

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Sel; }
        void GetStringValue(ks_wstring &value) override
        {
            value.assign(__X(""));
            for (int32 i = 0; i < m_selects.size(); ++i)
            {
                value.append(m_selects.at(i));
                if (i < m_selects.size() - 1)
                    value.append(__X(","));
            }
        }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        std::vector<ks_wstring> m_selects;
    };

    class DBJsonDataUrl : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataUrl() {}
        DBJsonDataUrl(QJsonObject jsonObj);

        void SetAddress(PCWSTR address) { m_address = address; }
        PCWSTR GetAddress() const { return m_address.c_str(); }
        void SetDispalayText(PCWSTR display) { m_displayText = display; }
        PCWSTR GetDisplayText() const { return m_displayText.c_str(); }

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Url; }
        void GetStringValue(ks_wstring &value) override { value = m_displayText; }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        ks_wstring m_address;
        ks_wstring m_displayText;
    };

    class DBJsonDataUser : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataUser() {}
        DBJsonDataUser(QJsonObject jsonObj);
        
        void AddUserItem(const DBJsonDataUserItem &ui) { m_items.push_back(ui); }
        int GetUsersCount() { return m_items.size(); }
        DBJsonDataUserItem* GetUser(int index) { if (0 <= index && index < m_items.size()) { return &(m_items.at(index));} return NULL;}

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Usr; }
        void GetStringValue(ks_wstring &value) override
        {
            value.assign(__X(""));
            for (int32 i = 0; i < m_items.size(); ++i)
            {
                DBJsonDataUserItem &item = m_items.at(i);
                value.append(item.name.c_str());
                if (i < m_items.size() - 1)
                    value.append(__X(","));
            }
        }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        std::vector<DBJsonDataUserItem> m_items;
    };

    class DBJsonDataAddress : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataAddress() {}
        DBJsonDataAddress(QJsonObject jsonObj);

        void SetDetail(PCWSTR detail) { m_detail = detail; }
        PCWSTR GetDetail() const { return m_detail.c_str(); }
        void AddDistrict(PCWSTR district) { m_districts.push_back(ks_wstring(district)); }
        int GetDistrictsCount() { return m_districts.size(); }
        PCWSTR GetDistrict(int index) { if (0 <= index && index < m_districts.size()) { return m_districts.at(index).c_str(); } return NULL; }

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Addr; }
        void GetStringValue(ks_wstring &value) override
        {
            value.assign(__X(""));
            for (int32 i = 0; i < m_districts.size(); ++i)
            {
                value.append(m_districts.at(i));
            }
            value.append(m_detail);
        }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        ks_wstring m_detail;
        std::vector<ks_wstring> m_districts;
    };

    class DBJsonDataDepartment : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataDepartment() {}
        DBJsonDataDepartment(QJsonObject jsonObj);

        void AddDepartmentItem(const DBJsonDataAddress &item) { m_items.push_back(item); }
        int GetDepartmentCount() { return m_items.size(); }
        DBJsonDataAddress* GetDepartmentItem(int index) { if (0 <= index && index < m_items.size()) { return &(m_items.at(index));} return NULL; }

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Dept; }
        void GetStringValue(ks_wstring &value) override
        {
            value.assign(__X(""));
            for (int32 i = 0; i < m_items.size(); ++i)
            {
                DBJsonDataAddress &item = m_items.at(i);
                for (int32 j = 0; j < item.GetDistrictsCount(); ++j)
                {
                    value.append(item.GetDistrict(j));
                    if (j < item.GetDistrictsCount() - 1)
                        value.append(__X("/"));
                }
                if (i < m_items.size() - 1)
                    value.append(__X(","));
            }
        }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
         std::vector<DBJsonDataAddress>  m_items;
    };

    class DBJsonDataAttachment : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataAttachment() {}
        DBJsonDataAttachment(QJsonObject jsonObj);

        void AddAttachmentItem(const DBJsonDataAttachmentItem &ai) { m_items.push_back(ai); }
        int GetAttachmentsCount() { return m_items.size(); }
        DBJsonDataAttachmentItem* GetAttachmentItem(int index) { if (0 <= index && index < m_items.size()) { return &(m_items.at(index));} return NULL; }

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Atta; }
        void GetStringValue(ks_wstring &value) override
        {
            value.assign(__X(""));
            for (int32 i = 0; i < m_items.size(); ++i)
            {
                DBJsonDataAttachmentItem &item = m_items.at(i);
                value.append(item.name);
                if (i < m_items.size() - 1)
                    value.append(__X(","));
            }
        }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        std::vector<DBJsonDataAttachmentItem> m_items;
    };

    class DBJsonDataNote : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataNote() {}
        DBJsonDataNote(QJsonObject jsonObj);

        void AddNoteItem(const DBJsonDataNoteItem &ai) { m_items.push_back(ai); }
        int GetNotesCount() { return m_items.size(); }
        DBJsonDataNoteItem* GetNoteItem(UINT index)
        {
            ASSERT(0 <= index && index < m_items.size());
            return &(m_items.at(index));
        }

    public:
        DBJsonDataType GetType() override { return DBJsonDataType_Note; }
        void GetStringValue(ks_wstring &value) override 
        {
            int count = m_items.size();
            value.assign(__X(""));
            for (int32 i = 0; i < count; ++i)
            {
                DBJsonDataNoteItem &item = m_items.at(i);
                value.append(item.shortSummary);
                if (i < count - 1)
                    value.append(__X(","));
            }
        }
        void SerialContent(QJsonObject& jsonObj) override;
    private:
        std::vector<DBJsonDataNoteItem> m_items;
    };

    class DBJsonDataLink : public DBJsonDataBaseObject
    {
    public:
        DBJsonDataLink(UINT sheetStId, EtDbId viewId)
            : m_sheetStId(sheetStId)
            , m_viewId(viewId) {}
        DBJsonDataLink(QJsonObject jsonObj);
        DBJsonDataType GetType() { return DBJsonDataType_Link; }
        void GetStringValue(ks_wstring &value);
        void SetStringValue(PCWSTR value) { m_recordPrimaryFieldString = value; }
        UINT GetLinkSheet() { return m_sheetStId; }
        EtDbId GetLinkView() { return m_viewId; }
        void SerialContent(QJsonObject& jsonObj);
        void AssignContainerSize(UINT size) { m_linkRecords.reserve(size); }
        void AddRecordItem(EtDbId recordId)
        {
            m_linkRecords.emplace_back(recordId);
        }
        const std::vector<EtDbId>& GetRecIdContainer() { return m_linkRecords; }
    private:
        UINT m_sheetStId;
        EtDbId m_viewId;
        std::vector<EtDbId> m_linkRecords;
        ks_wstring m_recordPrimaryFieldString;
    };
	
	class DBJsonRecord
    {
    public:
        void addCellData(DBJsonDataBaseObject* cellData) {m_cellData.emplace_back(cellData);}
        DBJsonDataBaseObject* getCellData(UINT index) const { return index < m_cellData.size() ?  m_cellData[index].get() : nullptr;}
        UINT getCellCount() const { return m_cellData.size(); }
        void setParentRecordIdx(EtDbIdx idx) { m_parent = idx;}
        EtDbIdx getParentRecordIdx() const { return m_parent; }
    private:
        std::vector<std::shared_ptr<DBJsonDataBaseObject> > m_cellData;
        EtDbIdx m_parent = INV_EtDbIdx; //父记录
    };

    class DBJsonDataHelper
    {
    public:
        typedef DBJsonDataBaseObject CellOBJ;
        typedef std::vector<std::shared_ptr<DBJsonRecord> > Records;

    public:

        HRESULT parse(QJsonDocument jsonDoc);
        void toJson(ks_wstring& jsonString);

        void SetSrcFileId(PCWSTR fileId);
        PCWSTR GetSrcFileId();
        UINT GetRowCount();
        UINT GetColCount();
        void GetString(UINT r, UINT c, ks_wstring &res);
        CellOBJ *GetCellData(UINT r, UINT c);
        void AddRecord(DBJsonRecord* record);
        const DBJsonRecord* GetRecord(UINT r);
        void setHandleSubRecord(bool b) { m_handleSubRecord = b; }
        bool getHandleSubRecord() const { return m_handleSubRecord;}
        DBJsonDataType TranslateFieldTypeToCopyDataType(ET_DbSheet_FieldType fldType);
    private:
        DBJsonDataType getCellType(QString ws);

    private:
        UINT m_rowCnt;
        UINT m_colCnt;
        Records m_records;
        ks_wstring m_srcFileId;
        bool m_handleSubRecord = false;
    };

}

#endif // __DB_JSON_DATA_HELPER__H__