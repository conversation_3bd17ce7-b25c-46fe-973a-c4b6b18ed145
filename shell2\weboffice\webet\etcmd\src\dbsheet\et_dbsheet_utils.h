﻿#ifndef __WEBET_DATABASE_SHEET_UTILS_H__
#define __WEBET_DATABASE_SHEET_UTILS_H__

#include "appcore/et_appcore_dbsheet.h"
#include "appcore/et_appcore_enum.h"
#include "etcore/et_core_dbsheet_enum.h"
#include "webbase/binvariant/binvarobj.h"
#include "wo/et_shared_str.h"
#include "database/database_def.h"
#include "dbsheet/et_dbsheet_common_helper.h"

constexpr ARGB DEFAULT_COLOR = 0xFF3DC5BA;

namespace wo
{
using binary_wo::VarObj;
class KEtRevisionContext;
namespace DbSheet
{
bool needGenerateSelectItemsFromContents(IDbField *pOldField, IDbField *pNewField);
HRESULT ConfigureDbContactField(IDbField*, const VarObj&, bool, const char* multipleContacts, const char* noticeContact);
HRESULT ConfigureDbContactExtendField(IDbField*, const VarObj&, IDBSheetRange** ppIstRg = nullptr);
HRESULT ClearDbContactExtendField(IDbFieldsManager* pFieldsMgr, EtDbId extendFldId);
HRESULT SetDbContactExtendField(IDbFieldsManager* pFieldsMgr, EtDbId extendFldId, EtDbId contactFldId);
HRESULT GetDbContactExtendFields(IDbField*, std::vector<EtDbId>&);
HRESULT ConfigureLookupCondProps(IDbField* pField, IDBSheetOp*, const VarObj& args);
HRESULT ConfigureDbLinkField(IDbField*, const VarObj&, bool, const char* multipleLinks);
HRESULT ConfigureDbField(IDbField*, const VarObj&, ET_DbSheet_FieldType, bool, IDBSheetView* = nullptr, bool = false, BOOL = FALSE);
HRESULT correctFieldType(IDbField* pField, const VarObj& args, ET_DbSheet_FieldType& type);
HRESULT GetFieldValues(IDBSheetOp* pDbSheetOp, EtDbId fldId, ET_DbSheet_FieldType oldType, ET_DbSheet_FieldType newType, 
    GlobalSharedStringUnorderedSet&);
HRESULT ConfigureDbFormulaResultField(IDbField*, const VarObj&, bool);

// array support field 对外提供的接口
namespace array_field_fall_back
{
void convertToText(IDbField* pField);
bool unwrap(const_token_ptr pToken, IDBSheetOp* pSheetOp, EtDbId recordID, EtDbId fieldID, IDbField* pField);
bool checkErrorToken(IDbField* pField);
void fallback(IDbField* pField, OUT bool* pIsFallBack2Txt = nullptr);
}

namespace statistic_sheet_helper {
bool needDiscardZeroDoubleToken(IDbField *, const_token_ptr) noexcept;
}

struct SyncSheetInitParam
{
	ET_DbSheet_Sync_Type syncType = DbSheet_St_DB;
	SyncInfo info;
};
HRESULT ConfigureNewSheet(_Worksheet*, IDBProtectionJudgement*, UINT nRow, UINT nCol, Database::SheetInitConfig, const SyncSheetInitParam* = nullptr);
bool ExceededMaxDueDateAutomationLimits(IDBSheetOp *, UINT);
bool HasDueDateAutomation(IDBSheetOp *, IDBSheetRange *);
bool IsRelatedToDueDate(IDBSheetOp *, EtDbId);
bool ExceededFieldCountLimits(IDBSheetView*, ET_DbSheet_FieldType, UINT);
HRESULT ValidateBaseLookupField(IBook*, UINT sheetId, EtDbId fldId);

HRESULT AddField(IDBSheetView* pView, ET_DbSheet_FieldType type, const VarObj& args, IDBSheetRange **ppRange);
HRESULT AddFields(IDBSheetView* pView, const VarObj& args, IDBSheetRange **ppRange);
HRESULT OnAddField(IDBSheetView* pView, EtDbId fldId, ET_DbSheet_FieldType type, IDBSheetRange *pRange);
bool CheckFieldTypeValidForStatDbSheet(IBook* pBook, UINT sheetStId, ET_DbSheet_FieldType type);
bool hasSelectField(IDBSheetView* pView);
void AddNecessarySelectField(IDBSheetViews*, IDBSheetRange**, bool&, const VarObj&, bool bUsedForApp = false);
HRESULT initDbSheet(const VarObj&, _Worksheet*, IDBProtectionJudgement*);
HRESULT configureDbSheet(IDBSheetView*, const VarObj&, INT, bool  = false);
HRESULT initDbSheetFields(IDBSheetView*, const VarObj&, bool = false);
HRESULT initDbSheetValues(IDBSheetView*, const VarObj&);
HRESULT initDbSheetViewRowHeight(IDBSheetView*, INT);

HRESULT initDbSyncSheet(_Worksheet*, IDBProtectionJudgement*, const Database::SheetInitConfig&, const SyncSheetInitParam*, IDBSheetView** = nullptr);
HRESULT setDbSyncSheet(const VarObj&, _Worksheet*, IDBProtectionJudgement*, IDBSheetView*,
    std::unordered_map<EtDbId, EtDbId>*, bool bNewLookupConvert = false);
int GetDBDashBoardSheetLimit();
int GetDBFpsheetLimit();
HRESULT copyDbUsersManager(IBook* pSrcBook, IBook* pTarBook);

HRESULT GetDbSheet(IBook* pBook, UINT sheetStId, ISheet** ppSheet);
HRESULT GetDbDashBoardSheet(IBook*, UINT, ISheet**);
HRESULT GetDBSheetViews(IBook* pBook, UINT sheetStId, IDBSheetViews** ppIDbSheetViews);
HRESULT GetDBSheetViews(ISheet* pSheet, IDBSheetViews** ppIDbSheetViews);
HRESULT GetDBSheetView(IBook* pBook, UINT sheetStId, EtDbId viewId
    , IDBSheetView** ppIDbSheetView, IDBSheetViews** ppIDbSheetViews);
HRESULT GetDBChartStatisticMgr(ISheet*, IDBChartStatisticMgr**);
HRESULT GetDBSheetOp(IBook* pBook, UINT sheetStId, IDBSheetOp** ppDbSheetOp);
HRESULT GetDBSheetOp(ISheet* pSheet, IDBSheetOp** ppDbSheetOp);
HRESULT GetDBDashBoardOp(ISheet*, IDBDashBoardDataOp**);
HRESULT GetDBFpSheetOp(ISheet*, IFPSheetData**);

HRESULT GenerateValidDbsheetViewName(IDBSheetViews* pViews, WebStr& baseName, ks_wstring& nameStr);

ET_DBSheet_ViewType ConvertViewType(IEncodeDecoder* pEncodeDecoder, WebStr typeStr);
HRESULT AddWildChar(PCWSTR srcStr, ks_wstring& resStr);

// 从 common helper 中移过来. 实现本身不依赖 common helper 类. 预计逐渐用这里的方法替代 common helper 里的
// 为控制影响面, 暂时不大量地替换
EtDbId GetEtDbId(const binary_wo::VarObj& obj, WebName name);
EtDbId GetEtDbId(binary_wo::VarObj obj);

//开启分享视图
HRESULT DoShareView(binary_wo::VarObj& param, IEtRevisionContext* pCtx, IEncodeDecoder* pEncodeDecoder);
void ParseUnsupportedAttachmentFileSuffixTypes(const binary_wo::VarObj& param, std::set<ks_wstring>& unsupportedAttachmentFileSuffixTypes);
bool ContainTimeFormat(PCWSTR numberFormat);

//从服务端获取分享链接。非命令回放时调用，调用时要注意命令回放时的处理。
void FetchSharedIdForNotDirect(ET_DBSheet_ViewType viewType, ks_wstring* sharedId);
void SetSharedIdParam(std::vector<etoldapi::_Worksheet*>& newWorkSheets, binary_wo::VarObj &param);

HRESULT SetViewProp(IDBSheetViews *pViews, IDBSheetView* pView, binary_wo::VarObj& param, KEtRevisionContext* pCtx);
void SetMaxFieldsCnt(VarObj param, IDBSheetView* pView);
HRESULT DealViewFormParam(VarObj param, IDBSheetView* pDbSheetView,IDBSheetViews* pDbSheetViews);
HRESULT filtrateByExtraFilter(IDbExtraFilter* pFilter, const binary_wo::VarObj& filter,
	DBSheetCommonHelper& commonHelper, IDBSheetOp* pData, bool bPreferId, OUT const IDbExtraFilter** ppTopFilter);
HRESULT RemoveFilterByFieldId(IDbFilter* pFilter, EtDbId fieldId, IDbFieldFilter* pExceptFieldFilter = nullptr);
HRESULT GenerateExtraSort(IDBSheetOp* pData, DBSheetCommonHelper& commonHelper, const binary_wo::VarObj& sort, OUT IDbExtraSort** ppSort);
HRESULT GetValidViewName(IDBSheetViews* pDbSheetViews, PCWSTR name, OUT ks_wstring& viewName);

void WalkThroughHandleTokenArray(const_token_ptr pToken, const std::function<void(alg::TOKEN_HANDLE, DWORD)>& tokenHandler);

HRESULT SetFilterConditions(IDbFilter* pFilter, const binary_wo::VarObj& conditions, IDBSheetOp* pData, const DBSheetCommonHelper& commonHelper);
HRESULT GenerateDBProtection(IBook* pBook, IDBProtectionJudgement* pDBProtectObj, const binary_wo::VarObj& permissionParam, OUT IDBProtection** pProtection);
HRESULT ReadDBProtectionConfig(IBook* pBook, const WebSlice& permissionSlice);
void GetAttachmentIdMap(const VarObj& param, std::unordered_map<GlobalSharedString, GlobalSharedString, GlobalSharedStringHasher>& attachmentIdMap);

HRESULT ResetAppDBViewSharedId(IAirApp_DBView* pAirAppView, PCWSTR newSharedId);
HRESULT SubmitCollectedFmlCells2DbSheet(IBook*);
size_t CountCollectedFmlCells2DbSheet(IBook*);
HRESULT SerializeFilterList(const binary_wo::VarObj& param, KEtRevisionContext* ctx, DBSheetCommonHelper* pCommonHelper, ISerialAcceptor* acpt, WebInt& version);

HRESULT ConvertStringToDateTime(IBook* pBook, PCWSTR str, double& dt);

BOOL GetDbLinkCustomConfigRecords(IDBSheetOp* pDbSheetOp, const binary_wo::VarObj& param, std::vector<EtDbId>& records);
HRESULT GenDbLinkCustomConfigResult(IDBSheetOp* pDbSheetOp, const binary_wo::VarObj& param, managed_token_ptr* ppRes, BOOL& bLinkCustomConfig);

class DisableDbRecordProtectScope
{
public:
    DisableDbRecordProtectScope(IDBProtectionJudgement* pProtectionJudgement,
                                std::unordered_set<EtDbId>&& recordIds) = delete;

    DisableDbRecordProtectScope(IDBProtectionJudgement* pProtectionJudgement,
                                const std::unordered_set<EtDbId>* pRecordIds)
            : m_spProtectionJudgement(pProtectionJudgement),
              m_pRecordIds(pRecordIds)
    {
        for (EtDbId recordId: *m_pRecordIds)
            m_spProtectionJudgement->BeginDisableRecord(recordId);
    }

    // 需要保证pIds指向的对象的生命周期比DisableDbRecordProtectScope的生命周期长
    DisableDbRecordProtectScope(IDBProtectionJudgement* pProtectionJudgement,
                                const EtDbId* pIds, const UINT cnt)
            : m_spProtectionJudgement(pProtectionJudgement),
            m_pRecIds(pIds), m_cnt(cnt), m_pRecordIds(nullptr)
    {
        for (int i = 0; i < m_cnt; ++i)
            m_spProtectionJudgement->BeginDisableRecord(m_pRecIds[i]);
    }

    DisableDbRecordProtectScope(IDBProtectionJudgement* pProtectionJudgement,
                                const IDBSheetRange* pRange)
            : m_spProtectionJudgement(pProtectionJudgement)
    {
        pRange->GetRecordIds(&m_pRecIds, &m_cnt);
        for (int i = 0; i < m_cnt; ++i)
            m_spProtectionJudgement->BeginDisableRecord(m_pRecIds[i]);
    }

    ~DisableDbRecordProtectScope()
    {
        if (m_pRecIds)
        {
            for (int i = 0; i < m_cnt; ++i)
                m_spProtectionJudgement->EndDisableRecord(m_pRecIds[i]);
        }
        else
        {
            for (EtDbId recordId: *m_pRecordIds)
                m_spProtectionJudgement->EndDisableRecord(recordId);
        }
    }

private:
    ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
    const std::unordered_set<EtDbId>* m_pRecordIds;
    const EtDbId* m_pRecIds = nullptr;
    UINT m_cnt = 0;
};

class DisableDbSheetProtectScope
{
public:
    DisableDbSheetProtectScope(IDBProtectionJudgement* pProtectionJudgement, UINT sheetStId)
        : m_spProtectionJudgement(pProtectionJudgement)
        , m_sheetStId(sheetStId)
    {
        m_spProtectionJudgement->BeginDisableSheet(m_sheetStId);
    }

    ~DisableDbSheetProtectScope()
    {
        m_spProtectionJudgement->EndDisableSheet(m_sheetStId);
    }
private:
    ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
    UINT m_sheetStId;
};

class DisableDbProtectScope
{
    public:
    DisableDbProtectScope(IDBProtectionJudgement* pProtectionJudgement)
        : m_spProtectionJudgement(pProtectionJudgement)
    {
        if (m_spProtectionJudgement)
            m_spProtectionJudgement->BeginDisable();
    }

    ~DisableDbProtectScope()
    {
        if (m_spProtectionJudgement)
            m_spProtectionJudgement->EndDisable();
    }
private:
    ks_stdptr<IDBProtectionJudgement> m_spProtectionJudgement;
};

class DisableDbTrackHistoryScope
{
public:
    DisableDbTrackHistoryScope()
    {
        m_pCtx = _appcore_GainDbSheetContext();
        m_oldValue = m_pCtx->GetEnableLineHistoryTrack();
        m_pCtx->SetEnableLineHistoryTrack(FALSE);
    }

    ~DisableDbTrackHistoryScope()
    {
        m_pCtx->SetEnableLineHistoryTrack(m_oldValue);
    }

private:
    IDBSheetCtx* m_pCtx;
    BOOL m_oldValue;
};

class DisableDbUpdateLastModifiedInfoScope
{
public:
    DisableDbUpdateLastModifiedInfoScope()
    {
        m_pCtx = _appcore_GainDbSheetContext();
        m_oldValue = m_pCtx->GetEnableLastModifiedInfoTrack();
        m_pCtx->SetEnableLastModifiedInfoTrack(FALSE);
    }

    ~DisableDbUpdateLastModifiedInfoScope()
    {
        m_pCtx->SetEnableLastModifiedInfoTrack(m_oldValue);
    }

private:
    IDBSheetCtx* m_pCtx;
    BOOL m_oldValue;
};

class DbFieldUpdateScope
{
public:
    DbFieldUpdateScope(IDbField *pField, IBook *pBook, HRESULT &hr);
    ~DbFieldUpdateScope();

    IDbField *GetOldField();
private:
    ks_stdptr<IDBFieldUpdateScope> m_spDbFieldUpdateScope;
};

// SubmitChange 批处理. 需注意 SubmitChange 批处理的逻辑是基于区域的, 而编辑操作可能引发坐标的变更,
// 因此这个批处理对象的生命周期尽量贴近要处理的编辑操作, 不要放得太靠外
struct DbtBatchSubmitOptimizer
{
    // 这个对象, 以及它调用的对 submitchange 的批处理操作, 都是只对db设计的. 构造函数里的 IDBSheetOp* 必须有效
    // 如果对工作表也有类似需求, 需要自行开发. 工作表的编辑操作涉及单元格的移动等, submitchange批处理需要单独设计
    explicit DbtBatchSubmitOptimizer(IDBSheetOp* pOp, bool bOn = true) : m_spDbSheetOp(pOp), m_buc(pOp->GetBook()->LeakOperator()), m_bOn(bOn)
    {
        ASSERT(m_spDbSheetOp);
        if (m_bOn)
            m_spDbSheetOp->BeginBatchSetVal();
    }
    ~DbtBatchSubmitOptimizer()
    {
        ASSERT(m_spDbSheetOp);
        if (m_bOn)
            m_spDbSheetOp->EndBatchSetVal(FALSE);
    }

    ks_stdptr<IDBSheetOp> m_spDbSheetOp;
    // 针对"提交改动"的批量优化, 要发生在计算批量优化的内部. 如果先计算, 后提交改动, 则会导致脏单元格本次无法计算
    app_helper::KBatchUpdateCal m_buc;
    bool m_bOn = true;      // 外面可能需要一些功能开关，控制它是否生效
};

struct UserInfo
{
    ks_wstring id;
    ks_wstring nickname;
    ks_wstring avatar;
    // 这里用指针是为了判断企业id是否存在，企业id不存在时更新联系人信息不会覆盖已有的企业id
    std::unique_ptr<ks_wstring> companyId;
    KDbCstrUserInfo ToCstr() const
    {
        return {id.c_str(), nickname.c_str(), avatar.c_str(),
                               companyId ? companyId->c_str(): nullptr};
    }
};
HRESULT GetCstrUserInfoVec(
    IN const std::vector<wo::DbSheet::UserInfo>&, 
    OUT std::vector<KDbCstrUserInfo>&);
HRESULT GetCstrFailedIds(IN const std::vector<ks_wstring>&, OUT std::vector<PCWSTR>&);

struct DbLinkHlp
{
	DbLinkHlp(IBook* pBook, DbReversedLinkParam& param) : m_pBook(pBook), m_param(param)
	{
		VS(_appcore_GainDbSheetContext()->BeginProcDbLink(&m_param));
	}
	~DbLinkHlp() { VS(_appcore_GainDbSheetContext()->EndProcDbLink(m_pBook)); }

	IBook* m_pBook;
	DbReversedLinkParam& m_param;
};

class AllDbViewsEnum : public IDbViewEnum
{
    using CallBack = std::function<HRESULT(IDBSheetView*)>;
public:
    AllDbViewsEnum(const CallBack &cb)
        : m_cb(cb)
    {}

    STDPROC Do(IDBSheetView *pView) override
    {
        return m_cb(pView);
    }
private:
    const CallBack m_cb;
};

} // namespace DbSheet

struct OpenWorkbookScope
{
    OpenWorkbookScope(Workbooks* ptrWorkbooks, IETPersist *ptrPersist, PCWSTR fileName);
    ~OpenWorkbookScope();

    _Workbook *GetWorkbook();
    HRESULT GetHr() const { return m_hr; }
private:
    ks_stdptr<_Workbook> m_spWorkbook;
    HRESULT m_hr;
};

class KDbIdIdxMap_Find : public IDBIds
{
public:
    DECLARE_INSTANCE_COUNTER(KDbIdIdxMap_Find)
    DECLARE_COMCLASS(KDbIdIdxMap_Find, IDBIds)

public:
    // IDBIds
    STDPROC_(EtDbId) IdAt(EtDbIdx idx) const override;
    STDPROC_(EtDbIdx) Id2Idx(EtDbId id) const override;
    STDPROC_(EtDbIdx) Count() const override;
    STDPROC_(BOOL) IsEntire() const override;

public:
    HRESULT AppendId(EtDbId id);
private:
    std::vector<EtDbId> m_vecIds;
    std::unordered_set<EtDbId> m_setIds;
};

class IDbCellValInvalidInfoBase
{
public:
    virtual ~IDbCellValInvalidInfoBase(){}
    virtual void SerialContent(binary_wo::VarObj& resObj) PURE;
    virtual void SerialContent(ISerialAcceptor *acpt) PURE;
};

class KDbNormalCellValInvalidInfo:  public IDbCellValInvalidInfoBase
{
    friend class KDbSetCellValInvalidInfoCollector;
public:
    KDbNormalCellValInvalidInfo(EtDbId recordId, EtDbId fieldId, ks_wstring expectedVal, HRESULT errName);
    virtual ~KDbNormalCellValInvalidInfo();
    virtual void SerialContent(binary_wo::VarObj& resObj) override;
    virtual void SerialContent(ISerialAcceptor *acpt) override;
private:
    EtDbId m_recordId;
    EtDbId m_fieldId;
    ks_wstring m_expectedVal;
    HRESULT m_errName;
};

class KDbRecordCellValInvalidInfo:  public IDbCellValInvalidInfoBase
{
    friend class KDbSetCellValInvalidInfoCollector;
public:
    KDbRecordCellValInvalidInfo(EtDbId recordId, ks_wstring fieldKey, bool bPreferId, ks_wstring expectedVal, HRESULT errName);
    virtual ~KDbRecordCellValInvalidInfo();
    virtual void SerialContent(binary_wo::VarObj& resObj) override;
    virtual void SerialContent(ISerialAcceptor *acpt) override;
private:
    bool m_bPreferId;
    EtDbId m_recordId;
    ks_wstring m_fieldKey;
    ks_wstring m_expectedVal;
    HRESULT m_errName;
};

class IDbErrInfoCollector
{
public:
    virtual ~IDbErrInfoCollector(){}
    virtual bool IsEmpty() PURE;
    virtual int GetSize() PURE;
    virtual void SerialContent(binary_wo::VarObj& resObj) PURE;
    virtual void SerialContent(ISerialAcceptor *acpt) PURE;
};

class KDbSetCellValInvalidInfoCollector: public IDbErrInfoCollector
{
public:
    KDbSetCellValInvalidInfoCollector(UINT sheetStId);
    virtual ~KDbSetCellValInvalidInfoCollector();
    void InsertNormalCellItem(EtDbId recordId, EtDbId fieldId, ks_wstring expectedVal, HRESULT errName);
    void InsertRecordCellItem(EtDbId recordId, ks_wstring fieldKey, bool bPreferId, ks_wstring expectedVal, HRESULT errName);

    virtual bool IsEmpty() override;
    virtual int GetSize() override;
    virtual void SerialContent(binary_wo::VarObj& resObj) override;
    virtual void SerialContent(ISerialAcceptor *acpt) override;

private:
    UINT m_sheetStId;
    std::vector<IDbCellValInvalidInfoBase*> m_cellInfos;
};


class KDbFieldValCacheGuard
{
public:
    KDbFieldValCacheGuard(EtDbId fldId, IDbFieldsManager *pFieldsMgr);
    ~KDbFieldValCacheGuard();

private:
    IDbFieldsManager *m_pFieldsMgr;
    EtDbId m_fldId;
};
} // namespace wo

#endif // __WEBET_DATABASE_SHEET_UTILS_H__
