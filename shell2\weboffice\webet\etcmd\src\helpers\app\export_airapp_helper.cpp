﻿#include "export_airapp_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "../sheet_operator_helper.h"
#include "et_revision_context_impl.h"
#include "helpers/attachment_helper.h"

namespace ExportAirAppHelper
{
HRESULT ExecExportAirApp(wo::KEtWorkbook* pWb, ISheet* pAppSheet, wo::KEtRevisionContext* pCtx, const ks_wstring& newSharedId, OUT std::set<ks_wstring>& attachmentIdSet)
{
	HRESULT hr = E_FAIL;
	if(!pAppSheet->IsAppSheet())
	{
		return E_FAIL;
	}

	UINT baseDbSheetStId = 0;
	ks_stdptr<IDBSheetView> spAppView;
	ks_stdptr<IAppSheetData> spAppSheetData;
	pAppSheet->GetExtDataItem(edSheetAppData, (IUnknown**)&spAppSheetData);
	IAirApp* pApp = spAppSheetData->GetApp(0);
	PCWSTR sharedId = pApp->GetSharedId();
	if(!sharedId)
	{
		WOLOG_ERROR << "[ExecExportAirApp] not existed sharedId!";
		return E_FAIL;
	}
	if (pApp->GetCategory() == KSheet_AirAppCat_DbSheetView)
	{
		ks_stdptr<IAirApp_DBView> spAppDbView = pApp;
		spAppView = spAppDbView->GetDBSheetView();
		if (spAppView)
		{
			EtDbId viewId = spAppView->GetId();
			baseDbSheetStId = spAppView->GetSheetOp()->GetSheetId();
			if(!newSharedId.empty())
			{
				//reset一下shared Id
				hr = wo::DbSheet::ResetAppDBViewSharedId(spAppDbView, newSharedId.c_str()); 
				if(FAILED(hr))
				{
					WOLOG_ERROR << "[ExecExportAirApp] reset sharedId fialed!";
					return hr;
				}
			}
			
		}
	}

	if(baseDbSheetStId == 0)
	{
		WOLOG_ERROR << "[ExecExportAirApp] cannot find baseDbSheetStId!";
		return E_FAIL;
	}

	//由于db底表可能处于深度隐藏，因此得手动改一下visible
	ks_stdptr<ISheet> spBaseDbSheet;
	wo::DbSheet::GetDbSheet(pWb->GetCoreWorkbook()->GetBook(), baseDbSheetStId, &spBaseDbSheet);
	if(!spBaseDbSheet)
	{
		WOLOG_ERROR << "[ExecExportAirApp] find baseDbSheet failed!";
		return E_FAIL;
	}
	spBaseDbSheet->SetVisible(ssVisible);

	//删除数据底表和当前应用表外的其余sheet
	_Workbook* pWorkbook = pWb->GetCoreWorkbook();
	ks_stdptr<etoldapi::Worksheets> spWorksheets = pWorkbook->GetWorksheets();
	if (!spWorksheets)
		return E_FAIL;

	std::vector<uint> vecSheetId;
	int sheetCount = spWorksheets->GetSheetCount();
	for (int i = 0; i < sheetCount; i++)
	{
		ks_stdptr<etoldapi::_Worksheet> spWorksheet = spWorksheets->GetSheetItem(i);
		if (!spWorksheet)
			return E_FAIL;

		ISheet* pSheet = spWorksheet->GetSheet();
		if(pSheet->GetStId() == pAppSheet->GetStId() || pSheet->GetStId() == baseDbSheetStId)
			continue;
		vecSheetId.push_back(pSheet->GetStId());
		//由于查询应用除了绑定一个db的底表，还会绑定一个et的表作为更新的数据源。
		//由于后续会把et表给移除掉，所以得先解决et表和应用表之间的绑定关系，避免后面delete etsheet时把关联的db底表一并删除的逻辑。
		if(pSheet->IsGridSheet())
		{
			ks_stdptr<IUnknown> spUnk;
			pSheet->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
			ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
			if(spSheetAppEtDbRelations) 
				spSheetAppEtDbRelations->ClearAll();
		}
	}

	bool bIgnoreProtection = true;
	hr = wo::SheetOperatorHelper::DeleteSheets(pWb, pCtx, vecSheetId, bIgnoreProtection);
	if(FAILED(hr))
	{
		WOLOG_ERROR << "[ExecExportAirApp] DeleteSheets failed!";
		return E_FAIL;
	}

	//删除db数据表上的除default view和app view外的所有其余view
	ks_stdptr<IDBSheetViews> spDbSheetViews;
	hr = spAppView->GetSheetOp()->GetDbSheetViews(&spDbSheetViews);
	if(FAILED(hr) || !spDbSheetViews)
		return E_FAIL;

	std::vector<EtDbId> needDelViewIdVec;
	ks_stdptr<IDBSheetViewsEnum> spEnum;
	if (SUCCEEDED(spDbSheetViews->GetViewsEnumerator(&spEnum)) && spEnum && S_OK == spEnum->Reset())
	{
		do
		{

			ks_stdptr<IDBSheetView> spDbSheetView;
			spEnum->GetCurView(&spDbSheetView);
			if (!spDbSheetView)
				continue;

			if (spDbSheetView->GetUseType() == Et_DBSheetViewUse_ForApp)
			{
				VS(spDbSheetView->RemoveInvalidLookupFilterCond());
				continue;
			}

			if(spDbSheetView->GetId() == spAppView->GetId())
				continue;
			needDelViewIdVec.push_back(spDbSheetView->GetId());

		}while (SUCCEEDED(spEnum->Next()));
	}

	for(EtDbId viewId: needDelViewIdVec)
	{
		HRESULT hr = spDbSheetViews->DelItem(viewId);
		if (FAILED(hr))
		{
			WOLOG_ERROR << "[ExecExportAirApp] DelItem failed!";
			return E_FAIL;
		}
	}
	
	//产品规定：移除view上的隐藏字段
	const IDBIds* pAllFileds = spAppView->GetAllFields();
	const IDBIds* pVisibleFields = spAppView->GetVisibleFields();

	std::vector<UINT> needDelFieldIdVec;
	for (int i = 0; i < pAllFileds->Count(); ++i)
	{
		EtDbId id = pAllFileds->IdAt(i);
		if(pVisibleFields->Id2Idx(id) == INV_EtDbIdx)
		{
			needDelFieldIdVec.push_back(id);
		}
	}

	const IDBIds *pAllRecords = spAppView->GetAllRecords();
	for(EtDbId fieldId: needDelFieldIdVec)
	{
		ks_stdptr<IDBSheetRange> spRg;
		spAppView->GetSheetOp()->CreateDBSheetRange(&spRg);
		spRg->AddFieldId(fieldId);
		spRg->SetRecordIds(pAllRecords); 
		hr = spAppView->GetSheetOp()->RemoveFields(spRg);
		if(FAILED(hr))
			return hr;
	}

	//清除底表的所有数据
	bool bClearAllData = true;
	if (bClearAllData)
	{
		ks_stdptr<IDBSheetRange> spRg;
		spAppView->CreateDBSheetRange(&spRg);
		spRg->SetFieldIds(spAppView->GetAllFields());
		spRg->SetRecordIds(spAppView->GetAllRecords());
		hr = spAppView->Clear(spRg, TRUE); 
		if(FAILED(hr))
			return hr;
	}

	// 收集db数据表上的附件
	for (size_t i = 0; i < spDbSheetViews->GetSize(Et_DBSheetViewUse_ForDb); ++i)
	{
		ks_stdptr<IDBSheetView> spView;
		spDbSheetViews->GetItemAt(i, Et_DBSheetViewUse_ForDb, &spView);
		if (spView == nullptr)
			continue;
		PCWSTR attachmentId = spView->GetBackgroundImageAttachment();
		if(xstrcmp(attachmentId, __X("")) != 0)
		{
			attachmentIdSet.insert(attachmentId);
		}
	}
	return S_OK;
}

}