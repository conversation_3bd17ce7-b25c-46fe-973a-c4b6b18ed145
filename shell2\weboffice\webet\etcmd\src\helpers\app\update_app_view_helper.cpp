﻿#include "update_app_view_helper.h"
#include "dbsheet/et2db_exporter.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "et/appcore/dbsheet/dbsheet_utils.h"

namespace UpdateAppViewHelper
{
static HRESULT RemoveListObject(ISheet* pDbSheet)
{
    ks_stdptr<ICoreListObjects> spLists;
    pDbSheet->GetExtDataItem(edSheetListObjects, (IUnknown **)&spLists);
    size_t listObjectCnt = spLists->GetCount();
    ASSERT(listObjectCnt > 0);
    ks_stdptr<ICoreListObject> spList;
    spLists->GetItem(0, &spList);
    HRESULT hr = spList->Delete();
    if(FAILED(hr))
        return hr;

    return S_OK;
}

static HRESULT ClearDbSheetContent(ISheet* pDbSheet, BO<PERSON> clearField)
{
    HRESULT hr = S_OK;

    ks_stdptr<IDBSheetOp> spDbSheetOp;
    VS(wo::DbSheet::GetDBSheetOp(pDbSheet, &spDbSheetOp));
    spDbSheetOp->Reset(clearField);

    ks_stdptr<IDBSheetViews> spViews;
    VS(wo::DbSheet::GetDBSheetViews(pDbSheet, &spViews));

    DbEnumUtils::AllDbViewsEnum viewsEnum([clearField](IDBSheetView* pView) -> HRESULT {
        //清除各个视图的信息(查询视图会去清除查询条件(字段)。)
        pView->Reset(clearField);
        return S_OK;
    });

    spViews->EnumViews(&viewsEnum);
    // 清除list object
    hr = RemoveListObject(pDbSheet);
    if (FAILED(hr))
    {
        WOLOG_ERROR << "[ClearDbSheetContent] Fail in RemoveListObject!";
        return hr;
    }
    return S_OK;
}

HRESULT UpdateAppSheetData(wo::KEtWorkbook* pWorkbook, wo::KEtRevisionContext* ctx, binary_wo::VarObj &param, ISheet* pDbSheet, OUT bool& needResetApp)
{
    VarObj extraData = param.get("extraData");
    UINT sheetStId = param.field_uint32("etSheetStId");
    IBook* pBook = pWorkbook->GetCoreWorkbook()->GetBook();
    IDX etSheetIdx = INVALIDIDX;
    pBook->STSheetToRTSheet(sheetStId, &etSheetIdx);
    long sheetCount = 0;
    ks_stdptr<Worksheets> spWorksheets = pWorkbook->GetCoreWorkbook()->GetWorksheets();
    spWorksheets->get_Count(&sheetCount);
    if(etSheetIdx < 0 || etSheetIdx >= sheetCount)
    {
        WOLOG_ERROR << "[UpdateAppSheetData] etSheetIdx < 0 || etSheetIdx >= sheetCount error";
        return E_FAIL;
    }

    ISheet* pEtSheet = spWorksheets->GetSheetItem(etSheetIdx)->GetSheet();
    if (!pEtSheet)
    {
        WOLOG_ERROR << "[UpdateAppSheetData] param etSheetIdx inValid!";
        return E_FAIL;
    }

    // db不需要导入
    if (pEtSheet->IsDbSheet())
    {
        WOLOG_ERROR << "[UpdateAppSheetData] param sheet is dbSheet!";
        return E_FAIL;
    }

    ks_stdptr<IDBProtectionJudgement> spProtectionJudgement;
    ks_stdptr<IUnknown> spUnknown;
    VS(pBook->GetExtDataItem(edDBUserGroups, &spUnknown));
    if (!spUnknown)
    {
        WOLOG_ERROR << "[UpdateAppSheetData] edDBUserGroups ExtDataItem not exist!";
        return E_FAIL;
    }
    ks_stdptr<IDBUserGroups> spDBUserGroups = spUnknown;
    spDBUserGroups->GetJudgement(&spProtectionJudgement);
    if (!spProtectionJudgement)
    {
        WOLOG_ERROR << "[UpdateAppSheetData] spProtectionJudgement empty!";
        return E_FAIL;
    }

    if (!extraData.has("xlsxImport") || !extraData.has("defaultName"))
    {
        WOLOG_ERROR << "[UpdateAppSheetData] param xlsxImport or defaultName not exist!";
        return E_FAIL;
    }
    VarObj xlsxImport = extraData.get("xlsxImport");
    VarObj defaultName = extraData.get("defaultName");
    IDX dbSheetIdx = INVALIDIDX;
    pBook->STSheetToRTSheet(pDbSheet->GetStId(), &dbSheetIdx);
    HRESULT hr = S_OK;

    ks_stdptr<IUnknown> spUnk;
    pEtSheet->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
    ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
    BOOL titleDirty = FALSE, dataDirty = FALSE;
    spSheetAppEtDbRelations->GetEtSheetDirty(titleDirty, dataDirty);
    if (titleDirty)
    {
        //整个表更新的流程:
        /*
            ClearDbSheetContent()
                清除各个视图的信息(查询视图会去清除查询条件(字段)。)
                清除list object
            importer 导入整表
                SetPrimaryFieldForIO()重新设置主键
                导入数据
            找到查询视图，对查询视图InitQueryFilter()，重新查询视图的筛选
        */

        hr = ClearDbSheetContent(pDbSheet, TRUE);
        if (FAILED(hr))
            return hr;

        wo::Et2DbImporterSameProcess importer(pWorkbook->GetCoreWorkbook(), pWorkbook->GetCoreWorkbook(), etSheetIdx,
                                              dbSheetIdx, ctx, true, defaultName, xlsxImport,
                                              spProtectionJudgement.get(), false, true);
        importer.setRequestIsFromApp();
        hr = importer.Exec();
        if (FAILED(hr))
        {
            WOLOG_ERROR << "[UpdateAppSheetData] Exec() Fail!!";
            return hr;
        }
        needResetApp = true;

        ks_stdptr<IDBSheetViews> spViews;
        VS(wo::DbSheet::GetDBSheetViews(pDbSheet, &spViews));
        ks_stdptr<IDBSheetView> spView;
        for (int i = 0; i < spViews->GetSize(Et_DBSheetViewUse_ForApp); ++i)
        {
            ks_stdptr<IDBSheetView> spView_Temp;
            spViews->GetItemAt(i, Et_DBSheetViewUse_ForApp, &spView_Temp);
            if (spView_Temp->GetType() == et_DBSheetView_Query)
            {
                spView = spView_Temp;
                break;
            }
        }
        if (spView == nullptr)
            return E_FAIL;
        ks_stdptr<IDBSheetView_Query> spView_Query = spView;
        //重置查询条件
        spView_Query->InitQueryFilter();
        
    }
    else
    {
        std::vector<EtDbId> phoneFldIdVec;
        ks_stdptr<IDBSheetViews> spViews;
        VS(wo::DbSheet::GetDBSheetViews(pDbSheet, &spViews));
        ks_stdptr<IDBSheetView> spView;
        for (int i = 0; i < spViews->GetSize(Et_DBSheetViewUse_ForApp); ++i)
        {
            ks_stdptr<IDBSheetView> spView_Temp;
            spViews->GetItemAt(i, Et_DBSheetViewUse_ForApp, &spView_Temp);
            if (spView_Temp->GetType() == et_DBSheetView_Query)
            {
                spView = spView_Temp;
                break;
            }
        }
        if (spView == nullptr)
            return E_FAIL;
        IDbFieldsManager* pFieldsMgr = spView->GetFieldsManager();
        const IDBIds* pFieldIds = spView->GetVisibleFields();
        for (int i = 0; i < pFieldIds->Count(); ++i)
        {
            ks_stdptr<IDbField> spField;
            EtDbId id = pFieldIds->IdAt(i);
            pFieldsMgr->GetField(id, &spField);
            if (spField->GetType() == Et_DbSheetField_Phone)
                phoneFldIdVec.push_back(id);
        }

        hr = ClearDbSheetContent(pDbSheet, FALSE);
        if (FAILED(hr))
            return hr;

        wo::Et2DbImporterSameProcess importer(pWorkbook->GetCoreWorkbook(), pWorkbook->GetCoreWorkbook(), etSheetIdx,
                                              dbSheetIdx, ctx, true, defaultName, xlsxImport,
                                              spProtectionJudgement.get(), false, false);
        importer.setRequestIsFromApp();
        hr = importer.Exec();
        if (FAILED(hr))
        {
            WOLOG_ERROR << "[UpdateAppSheetData] Exec() Fail!!";
            return hr;
        }

        ks_stdptr<IDBSheetView_Query> spView_Query = spView;
        for (int i = 0; i < phoneFldIdVec.size(); ++i)
        {
            ks_stdptr<IDbField> spField;
            pFieldsMgr->GetField(phoneFldIdVec[i], &spField);
            if (spField->GetType() != Et_DbSheetField_Phone)
            {
                if (spView_Query->IsPhoneField(phoneFldIdVec[i]))
                {
                    //重置查询条件
                    spView_Query->InitQueryFilter();
                    needResetApp = true;
                    break;
                }
            }
        }
    }
    if(SUCCEEDED(hr))
    {
        //更新完成后更新内核的标脏状态;
        UINT dbSheetStId = pDbSheet->GetStId();
        ks_stdptr<IUnknown> spUnk;
		pEtSheet->GetExtDataItem(edSheetAppEtDbRelations, &spUnk);
		ks_stdptr<ISheetAppEtDbRelations> spSheetAppEtDbRelations = spUnk;
		if(spSheetAppEtDbRelations)
		{
            IAppEtDbRelationItem* pItem =  spSheetAppEtDbRelations->GetAppEtDbRelationItem(dbSheetStId);
            if(pItem)
                pItem->ResetDirty();
        }

        
    }

    return hr;
}

}