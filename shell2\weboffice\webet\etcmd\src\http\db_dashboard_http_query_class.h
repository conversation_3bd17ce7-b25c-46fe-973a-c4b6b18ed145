﻿#ifndef __WEBET_DB_DASHBOARD_HTTP_QUERY_CLASS_H__
#define __WEBET_DB_DASHBOARD_HTTP_QUERY_CLASS_H__

#include "db_http_query_class.h"

namespace wo
{

class DbDashboardHttpListChartsQueryClass : public DbHttpQueryClassBase
{
public:
    explicit DbDashboardHttpListChartsQueryClass(KEtWorkbook*);
    HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx) override;
};

class DbDashboardHttpGetChartsDataQueryClass : public DbHttpQueryClassBase
{
public:
    explicit DbDashboardHttpGetChartsDataQueryClass(KEtWorkbook*);
    HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx) override;
};

class DbChartHttpListRecordsQueryClass : public DbHttpQueryClassBase
{
public:
    explicit DbChartHttpListRecordsQueryClass(KEtWorkbook*);
    HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx) override;
};

class DbDashboardHttpListFiltersQueryClass : public DbHttpQueryClassBase
{
public:
    explicit DbDashboardHttpListFiltersQueryClass(KEtWorkbook*);
    HRESULT Exec(const binary_wo::VarObj& param, KEtRevisionContext* pCtx, ISerialAcceptor* pAcpt) override;
protected:
    HRESULT CheckCmdPermission(const binary_wo::VarObj& param, KEtRevisionContext* pCtx) override;
};

};
#endif // __WEBET_DB_DASHBOARD_HTTP_QUERY_CLASS_H__