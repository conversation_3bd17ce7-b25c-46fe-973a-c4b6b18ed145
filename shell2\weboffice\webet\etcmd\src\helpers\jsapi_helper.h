﻿#ifndef __JSAPI_HELPER_H__
#define __JSAPI_HELPER_H__

struct JsApiIpcRpcCall;
class KJSVariantList;
struct JsApiIpcArgument;
struct JsApiIpcRpcCallArg;
typedef PVOID ApiObjectInterface;

namespace wo
{
class KEtWorkbook;
struct JsApiExecRecord;

class IJsApiBatchResult
{
public:
    ~IJsApiBatchResult() {}
    virtual void setNeedCommitApi(bool bNeed) = 0;
    virtual void setDependCommitId(int commitId) = 0;
    virtual int getCurApiTaskCnt() = 0;
    virtual void addCurApiTaskCnt(int v) = 0;
    virtual void addApiRecord(const JsApiExecRecord & record) = 0;
    virtual size_t getApiRecordsSize() = 0;
    virtual bool MarkJSContextChange(bool value) = 0;
};

class KJsApiHelper
{
public:
    KJsApiHelper(KEtRevisionContext* pCtx, KEtWorkbook* pWb, bool isQuery, IJsApiBatchResult * pBatchRes = nullptr);
    WebInt Execute(const binary_wo::VarObj& param);
    void ExecuteApi(const JsApiIpcRpcCall* pRpcCall, KJSVariantList* pValueList, KJSVariantList *pRetList);
    void GainObjMapContext(std::unordered_map<ApiObjectInterface, ks_stdptr<IDispatch>>& objMap, const void* pData, int nLen);
    static void InitEnv();
    static KEtRevisionContext* CurrentContext();
    static KEtWorkbook* CurrentWorkbook();
	static void Pixel2Twip(int nSrcWidth, int nSrcHeight, int &nDstWidth, int &nDesHeight);
	static void AddPicUuid(PCWSTR picId, PCWSTR uuid);
	static void GetPicUuid(PCWSTR picId, ks_wstring& uuid);

private:
    void RecordApi(const binary_wo::VarObj& param,
        const QString& taskId,
        QByteArray& blob,
        const JsApiIpcRpcCall &rpcCall,
        KJSVariantList &retList);
    HRESULT GetGlobalProperties(KJSVariantList *pRetList);
    bool CanExecute(const JsApiIpcRpcCall& rpcCall);
	WebInt ExecuteApi(const binary_wo::VarObj& param, const QString& taskId, const char* pBuffer, int nSize,
		const JsApiIpcRpcCall& rpcCall, JsApiIpcArgument* pArgs, const std::vector<const JsApiIpcRpcCallArg*>& args,
		const std::vector<ApiObjectInterface> &apis, KJSVariantList& retList);
	void UpdateTaskContext(KJSVariantList &retList);
	void AddObject(IDispatch* pDisp, KJSVariantList &retList);
    void GetCurrentContext(std::vector<ks_stdptr<IDispatch>>& apis);
    void SetException(PCWSTR exception, PCSTR log, int callType, KJSVariantList& retList);
	void InitAllSheetsPermission();

private:
    KEtRevisionContext* m_pCtx = nullptr;
    KEtWorkbook* m_pWb = nullptr;
    IJsApiBatchResult * m_pRes = nullptr;
    bool m_isQuery = false;
	bool m_bThrowExceptionOnError = false;
	bool m_bHasAllSheetsPermission = false;
};

struct JSTaskScope 
{
	JSTaskScope(QString& taskId);
	~JSTaskScope();
private:
	QString m_last;
};

struct JSRevisionContextScope
{
    JSRevisionContextScope(wo::KEtRevisionContext* pCtx);
    ~JSRevisionContextScope();
private:
    wo::KEtRevisionContext* m_pOld = nullptr;
};

} // namespace wo

#endif // __JSAPI_HELPER_H__