﻿#ifndef __WEBET_USER_ORGANIZES_H__
#define __WEBET_USER_ORGANIZES_H__

#include "etstdafx.h"

namespace wo
{

class KUserOrganizesCache
{
public:
	KUserOrganizesCache(IBook*, UserOrganizesInfo*);

    bool FetchAll();
    HRESULT FetchCurrentUserOrganizeInfo();
    HRESULT ResetCurrentUserOrganizeInfo();
    WebInt SerialContent(ISerialAcceptor* acpt);
private:
    WebInt userIsInOrganize(const PCWSTR userId, const PCWSTR connId, std::map<ks_wstring, bool>& mapOrganizeIds);
    bool checkOrganizeId(const ks_wstring& userId, std::set<ks_wstring>& organizeIds);
protected:
	IBook* m_bk;
    std::set<ks_wstring> m_organizeIds;
    UserOrganizesInfo* m_userOrganizesInfo;
    std::set<ks_wstring> m_idsCache;
};

}

#endif // __WEBET_USER_ORGANIZES_H__