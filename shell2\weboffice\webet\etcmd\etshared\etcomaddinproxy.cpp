﻿#include "etstdafx.h"
#include "etcomaddinproxy.h"
#include "etshareapplication.h"
#ifdef X_OS_WINDOWS
#include "kso/addins/comaddin_i.h"
#endif
namespace wo
{
void COMAddInProxy::Startup(COMAddInType type)
{
#ifdef X_OS_WINDOWS
	HRESULT hr = E_FAIL;
	IKCOMAddInProxy* pCOMAddInProxy = NULL;
	ks_stdptr<IKCoreObject> spAppDispatch = kxApp->coreApplication();
	ks_stdptr<IKApplication> spApplication = spAppDispatch;
	switch(type)
	{
	case et_comaddin:
		hr = _kso_CreateCOMAddInInterface(spApplication, __X("ET"), &pCOMAddInProxy);
		break;
	case wpp_comaddin:
		hr = _kso_CreateCOMAddInInterface(spApplication, __X("WPP"), &pCOMAddInProxy);
		break;
	case wps_comaddin:
		hr = _kso_CreateCOMAddInInterface(spApplication, __X("WPS"), &pCOMAddInProxy);
		break;
	}

	if (SUCCEEDED(hr))
	{
		kxApp->coreApplication()->SetCOMAddInProxy(pCOMAddInProxy);
		pCOMAddInProxy->OnConnection(ext_cm_Startup);
		pCOMAddInProxy->OnStartupComplete();
	}
#endif
}

void COMAddInProxy::Shutdown()
{
#ifdef X_OS_WINDOWS
	IKCOMAddInProxy* pCOMAddInProxy = kxApp->coreApplication()->GetCOMAddInProxy();
	if (pCOMAddInProxy)
	{
		pCOMAddInProxy->OnBeginShutdown();
		pCOMAddInProxy->OnDisconnection(ext_dm_HostShutdown);
		KS_RELEASE(pCOMAddInProxy);
		kxApp->coreApplication()->SetCOMAddInProxy(NULL);
	}
#endif
}
}