﻿#include  "coreconquer_helper.h"
#include "workbook.h"
#include "autofilter_helper.h"

namespace wo{

static void submitSheetRangeChange(wo::KEtWorkbook*wb,const QString& strUserID)
{
	int nSheetCount = wb->GetCoreWorkbook()->GetWorksheets()->GetSheetCount();
	ks_stdptr<IBookOp> spBookOp;
	auto* pBook = wb->GetCoreWorkbook()->GetBook();
	pBook->GetOperator(&spBookOp);
	RANGE rg(wb->GetBMP());

	spBookOp->BeginBatchUpdate();
	auto* bkStake = pBook->GetWoStake();
	for(auto index = 0 ; index < nSheetCount ; ++ index)
	{
		rg.SetSheets(index, index);
		IKAutoFilter* pAutoFilter = AutoFilterHelper::GetUserFilter(wb->GetCoreWorkbook(), krt::utf16(strUserID), rg);
		if (pAutoFilter == NULL )
			continue;

		RANGE filterRg(rg);
		pAutoFilter->GetFilterRange(&filterRg);
		if(filterRg.IsValid())
			bkStake->SubmitSheetChange(filterRg);
	}
	spBookOp->EndBatchUpdate();		
}

void handleCoreConquer(wo::KEtWorkbook*wb,const QString& strUserID)
{
	if(wb->GetBMP()->bDbSheet)
		return;
		
	auto* pBook = wb->GetCoreWorkbook()->GetBook();
	auto* bkStake = pBook->GetWoStake();
	wo::IBookSetting* pSetting = bkStake->getSetting();
	if(pSetting->getIsFilterShared() != FALSE)
		return;
	
	QString coreConquerId = QString::fromUtf16(pSetting->GetCoreConquerUser());
	if(coreConquerId == strUserID)
		return;

	BOOK_INFO_COLLECT *info = pBook->GetBookInfoCollect();
	if(info != nullptr)
	{
		auto bHasSubtotal = alg::IsBitUsed(info->data, BOOK_INFO_COLLECT::_maskSubTotalFunc);
		if(bHasSubtotal)
		{
			pSetting->SetCoreConquerUser(krt::utf16(strUserID));
			bkStake->SetCoreConqureOnOpen(true);
			submitSheetRangeChange(wb,strUserID);
		}
	}	
}
}
