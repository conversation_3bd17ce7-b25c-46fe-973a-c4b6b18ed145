#include "copy_helper.h"
#include "workbook.h"
#include "kso/io/clipboard/ksoclipboard.h"
#include "kso/io/clipboard/ksomimetype.h"
#include <krt/krtstring.h>

namespace Copy_Helper
{
constexpr int ONE_MILLION_SIZE = 1024*1024;

HRESULT Copy(IETPersist* pPersist, IDX sheetid, IUnknown* pRanges, const COPYINFO* pCopyInfo,
	const char* format, QString& text, bool &hasBreak, IBook *pBook)
{
	STGMEDIUM medium = {0};
	HRESULT hr = pPersist->WoCopy(pBook, sheetid, pRanges, pCopyInfo, format, &medium);
	if (FAILED(hr))
		return hr;

	ks_stdptr<IStream> spStrm;
	spStrm.attach(medium.pstm);
	KsoDataStream* dataStream = ks_castptr<KsoDataStream>(medium.pstm);
	Q_ASSERT(medium.tymed == TYMED_ISTREAM && dataStream != NULL);
	QByteArray arr = dataStream->getByteArray();
	if (0 == strncmp(kso_cb_html_format, format, ks_strnlen_s(kso_cb_html_format, 15)))
	{
		text = QString::fromUtf8(arr);
	}
	else
	{
		if (arr.size() < ONE_MILLION_SIZE)
			text = krt::fromUtf16(reinterpret_cast<PCWSTR>(arr.data()));
		else
		{
			int i = 0;
			const WCHAR *data = reinterpret_cast<PCWSTR>(arr.data());
			while(data[i] != 0 && i < ONE_MILLION_SIZE)
				++i;
			text = krt::fromUtf16(reinterpret_cast<PCWSTR>(arr.data()), i);
			hasBreak = true;
		}

	}

	return S_OK;
}

}// end namespace wo
