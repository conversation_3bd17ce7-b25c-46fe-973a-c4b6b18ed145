﻿#include "etstdafx.h"
#include "db_sync_adapter.h"

#include "dbsheet/et_dbsheet_copysheet_helper.h"
#include "dbsheet/et_dbsheet_utils.h"
#include "util.h"
#include "utils/et_gridsheet_utils.h"
#include "database/database_utils.h"
#include "dbsheet/db_gridsheet_sync_helper.h"
#include "http/db_value_serialiser.h"
#include "dbsheet/et_dbsheet_syncsheet_utils.h"
#include "binvariant/binwriter.h"
#include "webbase/serialize_impl.h"
#include "hresult_to_string.h"
#include "db/db_basic_itf.h"

namespace wo
{

ks_wstring VarObjToBase64(VarObj obj)
{
	binary_wo::BinWriter writer;
	KSerialWrapBinWriter wrap(writer, NULL);
	obj.serialContent(&wrap, true);
	binary_wo::BinWriter::StreamHolder stream = writer.buildStream();
	QByteArray ba((const char*)stream.get(), writer.writeLength());
	return krt::utf16(QString::fromUtf8(ba.toBase64()));
}

AddSyncDbSheetsAdapter::AddSyncDbSheetsAdapter(etoldapi::_Workbook* pWorkbook, IDBProtectionJudgement* pProtectionJudgement)
	: m_pWorkbook(pWorkbook)
	, m_pProtectionJudgement(pProtectionJudgement)
{
}

HRESULT AddSyncDbSheetsAdapter::Init(const SyncDbSheetsParam& param)
{
	if (!m_pWorkbook || !m_pProtectionJudgement)
		return E_INVALIDARG;
	m_pWorksheets = m_pWorkbook->GetWorksheets();
	if (!m_pWorksheets)
		return E_FAIL;
	m_param = param;
	return S_OK;
}

HRESULT AddSyncDbSheetsAdapter::Exec()
{
	// 同步数据场景, 不需要产生协作记录 (不存在合理的"操作者")
	DbSheet::DisableDbTrackHistoryScope dbHistoryDisabler;

	HRESULT hr = checkSyncSheetNumLimit();
	if (FAILED(hr))
		return hr;
	hr = exec();
	if (FAILED(hr))
	{
		rollback();
		return hr;
	}
	return S_OK;
}

HRESULT AddSyncDbSheetsAdapter::checkSyncSheetNumLimit()
{
	m_addSyncSheetCnt = m_param.sheetsInfo->arrayLength_s();
	if(m_addSyncSheetCnt == 0)
		return E_INVALIDARG;

	int existSyncStCnt = 0;
	for (int i = 0, sheetsCnt = m_pWorksheets->GetSheetCount(); i < sheetsCnt; ++i)
	{
		ISheet* pSheet = m_pWorksheets->GetSheetItem(i)->GetSheet();
		ks_stdptr<IDBSheetOp> spDBSheetOp;
		DbSheet::GetDBSheetOp(pSheet, &spDBSheetOp);
		if (!spDBSheetOp)
			continue;
		if (spDBSheetOp->IsSyncSheet())
			++existSyncStCnt;
	}
	if (existSyncStCnt >= m_param.maxSyncSheetLimit)
		return E_DBSHEET_SYNCSHEET_EXCEEDED_LIMITS;
	if (existSyncStCnt + m_addSyncSheetCnt > m_param.maxSyncSheetLimit)
	{
		m_addSyncSheetCnt = m_param.maxSyncSheetLimit - existSyncStCnt;
		m_bReachSyncSheetLimit = true;
	}
	return S_OK;
}

HRESULT AddSyncDbSheetsAdapter::exec()
{
	HRESULT hr = addWorksheets();
	if (FAILED(hr))
		return hr;
	return S_OK;
}

HRESULT AddSyncDbSheetsAdapter::addWorksheets()
{
	m_newWorksheetsVec.clear();
	size_t srcSheetCnt = m_addSyncSheetCnt;
	KComVariant vBefore;
	int idx = util::getLastSheetIdx(m_pWorkbook->GetBook()) + 1;
	KComVariant vAfter(idx);
	KComVariant vCount(srcSheetCnt);
	KComVariant vType(xlWorksheet);
	ks_stdptr<etoldapi::Worksheets> spWorksheets = m_pWorksheets;
	ks_stdptr<IKCoreObject> spObj;
	HRESULT hr = spWorksheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid_DB);
	if (FAILED(hr))
		return hr;
	m_newWorksheetsVec.reserve(srcSheetCnt);
	for (int i = 0; i < srcSheetCnt; ++i)
		m_newWorksheetsVec.emplace_back(spWorksheets->GetSheetItem(i + idx));
	UINT endIdx = idx + srcSheetCnt;
	IBook* pBook = m_pWorkbook->GetBook();
	for (int i = 0; i < srcSheetCnt; ++i)
	{
		binary_wo::VarObj sheetInfoObj = m_param.sheetsInfo->at_s(i);
		PCWSTR name = sheetInfoObj.field_str("name");
		ks_stdptr<etoldapi::_Worksheet> spWorksheet = m_newWorksheetsVec[i];
		ISheet* pSheet = spWorksheet->GetSheet();
		ks_stdptr<IDBSheetOp> spDBSheetOp;
		DbSheet::GetDBSheetOp(pSheet, &spDBSheetOp);
		ET_DbSheet_Sync_Type syncType = ET_DbSheet_Sync_Type::DbSheet_St_DB;
		if (sheetInfoObj.has("syncType"))
			VS(_appcore_GainEncodeDecoder()->DecodeDbSheetSyncType(sheetInfoObj.field_str("syncType"), &syncType));
		Database::SheetInitConfig config = Database::Utils::ParseDbSheetInitConfig(sheetInfoObj);
		DbSheet::SyncSheetInitParam syncInitParam;
		syncInitParam.syncType = syncType;
		ks_stdptr<IDBSheetView> spDbSheetView;
		hr = DbSheet::initDbSyncSheet(spWorksheet, m_pProtectionJudgement, config, &syncInitParam, &spDbSheetView);
		if (FAILED(hr))
			return hr;
		std::unordered_map<EtDbId, EtDbId> fieldMap;
		hr = DbSheet::setDbSyncSheet(sheetInfoObj, spWorksheet, m_pProtectionJudgement, spDbSheetView, &fieldMap);
		if (FAILED(hr))
			return hr;

		ks_bstr newName;
		hr = GetValidSheetName(spWorksheets, spWorksheet, name, &newName);
		if (FAILED(hr))
			return hr;
		hr = spWorksheet->put_Name(newName);
		if (FAILED(hr))
			return hr;

		SyncSheetInfo sheetInfo;
		sheetInfo.srcSheetId = sheetInfoObj.field_uint32("srcSheetId");
		sheetInfo.tarSheetId = pSheet->GetStId();
		sheetInfo.syncType = syncType;
		m_syncSheetInfoVec.emplace_back(std::move(sheetInfo));
	}
	m_activeStId = m_newWorksheetsVec[0]->GetSheet()->GetStId();
	return S_OK;
}

void AddSyncDbSheetsAdapter::rollback()
{
	for (IKWorksheet* pWorksheet : m_newWorksheetsVec)
		pWorksheet->DeleteDirectly();
	m_newWorksheetsVec.clear();
}

AddMergeSyncDbSheetsAdapter::AddMergeSyncDbSheetsAdapter(etoldapi::_Workbook* pTarWorkbook, IDBProtectionJudgement* pProtectionJudgement)
	: m_pTarWorkbook(pTarWorkbook)
	, m_pProtectionJudgement(pProtectionJudgement)
{
	m_iRecordCount = 0;
}

HRESULT AddMergeSyncDbSheetsAdapter::AddFieldMappingItem(PCWSTR fileId, binary_wo::VarObj* pSrcSheetsInfo)
{
	m_srcFileIdMap[fileId] = pSrcSheetsInfo;
	int srcInfoCnt = pSrcSheetsInfo->arrayLength();
	for (int i = 0; i < srcInfoCnt; i++)
	{
		VarObj srcSheetItem = pSrcSheetsInfo->at(i);
		if (srcSheetItem.has("recordsCount"))
			m_iRecordCount += srcSheetItem.field_uint32("recordsCount");
	}
	return S_OK;
}

HRESULT AddMergeSyncDbSheetsAdapter::Init(const SyncDbSheetsParam& param)
{
	if (!m_pTarWorkbook || !m_pProtectionJudgement)
		return E_INVALIDARG;

	m_pTarWorksheets = m_pTarWorkbook->GetWorksheets();
	if (!m_pTarWorksheets)
		return E_FAIL;
	m_param = param;
	return S_OK;
}

HRESULT AddMergeSyncDbSheetsAdapter::PrepareMerge()
{
	IBook* pTarBook = m_pTarWorkbook->GetBook();
	if (!pTarBook)
		return E_FAIL;
	if (MAX_ROW_COUNT <= m_iRecordCount)
		return E_DBSHEET_MERGE_DB_REACH_MAX_SHEET_ROWS;
	HRESULT hr = createTarWorksheet();
	if (FAILED(hr))
		return hr;
	DBAddMergeSyncHelper::DBAddMergeSyncParam syncParam;
	syncParam.pcwFldSourceName = m_param.fldSourceName;
	syncParam.pcwSheetName = m_param.sheetName;
	syncParam.m_pSrcFileIdMap = &m_srcFileIdMap;
	syncParam.pcwFldSourceFileName = m_param.fldSourceFileName;
	etoldapi::_Worksheet* pTarWorksheet = static_cast<etoldapi::_Worksheet*>(m_pNewWorksheet);
	m_upDBSyncMergeHelper = std::make_unique<DBAddMergeSyncHelper>(pTarWorksheet);
	hr = m_upDBSyncMergeHelper->Init(syncParam);
	if (FAILED(hr))
		return hr;
	hr = m_upDBSyncMergeHelper->PrepareMerge();
	if (FAILED(hr))
		return hr;
	return S_OK;
}

HRESULT AddMergeSyncDbSheetsAdapter::AdjustMerge()
{
	return m_upDBSyncMergeHelper->AdjustMerge();
}

HRESULT AddMergeSyncDbSheetsAdapter::ExecMerge()
{
	HRESULT hr = S_OK;
	IBook* pTarBook = m_pTarWorkbook->GetBook();
	if (!pTarBook)
		return E_FAIL;
	ks_stdptr<IBookOp> pTarBookOp;
	VS(pTarBook->GetOperator(&pTarBookOp));
	app_helper::KBatchUpdateCal buc(pTarBookOp);
	DbSheet::DisableDbProtectScope disPtScope(m_pProtectionJudgement);
	hr = PrepareMerge();
	if (FAILED(hr))
		return hr;
	if (nullptr == m_upDBSyncMergeHelper)
		return E_FAIL;
	hr = m_upDBSyncMergeHelper->ExecMerge();
	if (FAILED(hr))
		return hr;
	hr = AdjustMerge();
	if (FAILED(hr))
		return hr;
	m_upDBSyncMergeHelper->MoveFieldMappingItems(m_fileIdMap);
	m_bNeedRollback = false;
	return S_OK;
}

HRESULT AddMergeSyncDbSheetsAdapter::createTarWorksheet()
{
	KComVariant vBefore;
	int idx = util::getLastSheetIdx(m_pTarWorkbook->GetBook()) + 1;
	KComVariant vAfter(idx);
	KComVariant vCount(1);
	KComVariant vType(xlWorksheet);
	etoldapi::Worksheets* pTarWorksheets = static_cast<etoldapi::Worksheets*>(m_pTarWorksheets);
	ks_stdptr<IKCoreObject> spObj;
	HRESULT hr = pTarWorksheets->Add(vBefore, vAfter, vCount, vType, &spObj, stGrid_DB);
	if (FAILED(hr))
		return hr;
	m_pNewWorksheet = pTarWorksheets->GetSheetItem(idx);
	etoldapi::_Worksheet* pTarWorksheet = static_cast<etoldapi::_Worksheet*>(m_pNewWorksheet);
	DbSheet::SyncSheetInitParam syncInitParam;
	syncInitParam.syncType = DbSheet_St_Merge_DB;
	Database::SheetInitConfig config;
	config.strViewName = m_param.viewName;
	hr = DbSheet::initDbSyncSheet(pTarWorksheet, m_pProtectionJudgement, config, &syncInitParam);
	if (FAILED(hr))
		return hr;
	ks_bstr newName;
	hr = GetValidSheetName(m_pTarWorksheets, m_pNewWorksheet, m_param.sheetName, &newName);
	if (FAILED(hr))
		return hr;
	hr = pTarWorksheet->put_Name(newName);
	if (FAILED(hr))
		return hr;
	m_activeStId = m_pNewWorksheet->GetSheet()->GetStId();
	return S_OK;
}

EtDbId AddMergeSyncDbSheetsAdapter::GetFieldSourceId() const
{
	if (m_upDBSyncMergeHelper)
		return m_upDBSyncMergeHelper->GetFldSourceId();
	return INV_EtDbId;
}

EtDbId AddMergeSyncDbSheetsAdapter::GetFieldSourceNameId() const
{
	if (m_upDBSyncMergeHelper)
		return m_upDBSyncMergeHelper->GetFldSourceNameId();
	return INV_EtDbId;
}

void AddMergeSyncDbSheetsAdapter::rollback()
{
	if (NULL != m_pNewWorksheet)
		m_pNewWorksheet->DeleteDirectly();
}

AddMergeSyncDbSheetsAdapter::~AddMergeSyncDbSheetsAdapter()
{
	if (m_bNeedRollback)
		rollback();
}

DbSyncSheetHelper::DbSyncSheetHelper(etoldapi::_Workbook* pTarWorkbook, UINT tarStId,
	IDBProtectionJudgement* pProtectionJudgement, bool bNewLookupConvert)
	: m_pTarWorkbook(pTarWorkbook)
	, m_pProtectionJudgement(pProtectionJudgement)
	, m_bNewLookupConvert(bNewLookupConvert)
{
	m_syncSheetInfo.tarSheetId = tarStId;
}

DbSyncSheetHelper::~DbSyncSheetHelper()
{
	IEtRevisionContext* pCtx = _etcore_GetEtRevisionContext();
	const ks_wstring& errMsg = GetErrorMsg();
	if (!errMsg.empty() && pCtx)
		pCtx->collectInfo(krt::fromUtf16(errMsg.c_str()).toUtf8());
}

HRESULT DbSyncSheetHelper::Init(const SyncDbSheetParam& param, DbSheetValueSerialiser* pValueSetter)
{
	if (!m_pTarWorkbook || !m_pProtectionJudgement || !pValueSetter)
		return E_INVALIDARG;
	m_pValueSetter = pValueSetter;
	IBook* pTarBook = m_pTarWorkbook->GetBook();
	if (!pTarBook)
		return E_INVALIDARG;
	IDX tarStIdx = INVALIDIDX;
	pTarBook->STSheetToRTSheet(m_syncSheetInfo.tarSheetId, &tarStIdx);
	if (tarStIdx == INVALIDIDX)
		return E_INVALIDARG;
	ks_stdptr<_Worksheet> spWorksheet = m_pTarWorkbook->GetWorksheets()->GetSheetItem(tarStIdx);
	if (!spWorksheet)
		return E_INVALIDARG;
	m_pTarSheet = spWorksheet->GetSheet();
	if (!m_pTarSheet || !m_pTarSheet->IsDbSheet())
		return E_INVALIDARG;
	
	VS(DbSheet::GetDBSheetOp(m_pTarSheet, &m_spTarDbSheetOp));
	m_param = param;
	m_errMsg.clear();
	return S_OK;
}

HRESULT DbSyncSheetHelper::Exec()
{
	HRESULT hr = S_OK;
	// 同步数据场景, 不需要产生协作记录 (不存在合理的"操作者")
	DbSheet::DisableDbTrackHistoryScope dbHistoryDisabler;
	IBook* pTarBook = m_pTarWorkbook->GetBook();
	ks_stdptr<IBookOp> spTarBookOp;
	VS(pTarBook->GetOperator(&spTarBookOp));
	app_helper::KBatchUpdateCal buc(spTarBookOp);
	DbSheet::DbtBatchSubmitOptimizer batchSubmitter(m_spTarDbSheetOp.get());
	DbSheet::DisableDbProtectScope disPtScope(m_pProtectionJudgement);
	m_spTarDbSheetOp->SetSyncSheetFullupdateMode(DbSyncSheet_Fullupdate_Mode_ServerAPI);

	if (m_param.sheetData.has("baseSchema"))
	{
		const VarObj& baseSchema = m_param.sheetData.get("baseSchema");
		hr = SyncDbSheetProperty(baseSchema);
		if (FAILED(hr))
			return hr;
		hr = SyncDbSheetFields(baseSchema.get("fields"));
		if (FAILED(hr))
			return hr;
		if (baseSchema.has("recordsCount") && baseSchema.field_uint32("recordsCount") == 0) //源表没有记录，直接清空同步表记录
			ClearSheetRecords();
	}
	if (m_param.sheetData.has("records"))
	{
		const VarObj& records = m_param.sheetData.get("records");
		hr = SyncDbSheetRecords(records);
		if (FAILED(hr))
			return hr;
	}
	hr = SetInfoField();
	if (FAILED(hr))
		return hr;
	return S_OK;
}

HRESULT DbSyncSheetHelper::SyncDbSheetProperty(const VarObj& sheetObj)
{
	VS(m_spTarDbSheetOp->SetSheetDescription(sheetObj.field_str("description")));
	VS(m_spTarDbSheetOp->SetSheetIcon(sheetObj.field_str("icon")));
	//sheetname 设置
	m_srcSheetData.primaryId = DbSheet::GetEtDbId(sheetObj, "primaryFieldId");
	m_srcSheetData.syncFieldSourceId = DbSheet::GetEtDbId(sheetObj, "syncFieldSourceId");
	m_srcSheetData.syncFieldSourceNameId = DbSheet::GetEtDbId(sheetObj, "syncFieldSourceNameId");
	return S_OK;
}

HRESULT DbSyncSheetHelper::SyncDbSheetFields(const VarObj& fieldsObj)
{
	HRESULT hr = S_OK;
	if (binary_wo::typeArray != fieldsObj.type())
		return E_INVALIDARG;
	
	std::unordered_map<EtDbId, EtDbId> oldFldMap;
	GetSyncFieldMapping(oldFldMap);
	
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	const IDBIds* pTarIds = m_spTarDbSheetOp->GetAllFields();
	EtDbId fldSourceId = pTarFieldsMgr->GetSyncFieldSourceId();
	EtDbId oldPrimaryFldId = pTarFieldsMgr->GetPrimaryField();
	int fldCnt = fieldsObj.arrayLength();
	WOLOG_INFO << "SyncDbSheetFields cnt:" << fldCnt;
	std::unordered_set<EtDbId> holdFldId;
	holdFldId.reserve(fldCnt + 1);
	std::vector<EtDbId> insertFldId;
	std::unordered_map<EtDbId, int> fieldIdIdxMap; 
	for (int i = 0; i < fldCnt; ++i)
	{
		const VarObj& fieldObj = fieldsObj.at(i);
		EtDbId srcFldId = DbSheet::GetEtDbId(fieldObj, "id");
		if (srcFldId == INV_EtDbId)
			return E_INVALIDARG;
		if (srcFldId == m_srcSheetData.syncFieldSourceId || srcFldId == m_srcSheetData.syncFieldSourceNameId)
			continue;
		
		ET_DbSheet_FieldType srcFldType = Et_DbSheetField_MultiLineText;
		_appcore_GainEncodeDecoder()->DecodeFieldType(fieldObj.field_str("type"), &srcFldType);
		if (!DBSyncHelper::isValidType(srcFldType))
			continue;

		fieldIdIdxMap.emplace(srcFldId, i);
		m_srcfldIdTypeMap.emplace(srcFldId, srcFldType);
		auto it = oldFldMap.find(srcFldId);
		if (it == oldFldMap.end() || pTarIds->Id2Idx(it->second) == INV_EtDbIdx)
			insertFldId.emplace_back(srcFldId);
		else
		{
			m_syncSheetInfo.fldMap.emplace(srcFldId, it->second);
			holdFldId.emplace(it->second);
		}
	}

	size_t insertFldCnt = insertFldId.size();
	size_t insertRangeCnt = insertFldCnt;
	if (fldSourceId == INV_EtDbId)
		++insertRangeCnt;
	ks_stdptr<IDBSheetRange> spDbRange;
	if (insertRangeCnt > 0)
	{
		hr = m_spTarDbSheetOp->InsertFields(insertRangeCnt, &spDbRange);
		if (FAILED(hr))
			return hr;
	}
	ks_stdptr<IDBSheetViews> spDBSheetViews;
	hr = m_spTarDbSheetOp->GetDbSheetViews(&spDBSheetViews);
	if (FAILED(hr))
		return hr;
	for (int i = 0; i < insertFldCnt; ++i)
	{
		EtDbId newFldId = spDbRange->GetFieldId(i);
		ks_stdptr<IDbField> spField;
		hr = pTarFieldsMgr->GetField(newFldId, &spField);
		if (FAILED(hr))
			return hr;
		spField->SetSyncField(true);
		EtDbId srcFldId = insertFldId[i];
		EtDbIdStr buf;
		VS(_appcore_GainDbSheetContext()->EncodeEtDbId(srcFldId, &buf));
		spField->SetSyncSourceFieldId(buf);
		m_syncSheetInfo.fldMap.emplace(srcFldId, newFldId);
		holdFldId.emplace(newFldId);
		class AllDbViewsEnum : public IDbViewEnum
		{
			using CallBack = std::function<HRESULT(IDBSheetView*)>;
		public:
			explicit AllDbViewsEnum(CallBack cb) : m_cb(std::move(cb)) {}
			HRESULT Do(IDBSheetView* pView) override
			{
				return m_cb(pView);
			}
		private:
			CallBack m_cb;
		};
		AllDbViewsEnum viewsEnum([newFldId](IDBSheetView* pView) -> HRESULT {
			ET_DBSheet_ViewType type = pView->GetType();
			if (type == et_DBSheetView_Grid)
				pView->SetFieldsHidden(newFldId, FALSE);
			return S_OK;
		});
		spDBSheetViews->EnumViews(&viewsEnum);
	}
	if (fldSourceId == INV_EtDbId)
		fldSourceId = spDbRange->GetFieldId(insertFldCnt);
	m_syncSheetInfo.fldSourceId = fldSourceId;
	holdFldId.emplace(fldSourceId);
	hr = pTarFieldsMgr->SetSyncFieldSourceIdForIO(fldSourceId);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField> spField;
	hr = pTarFieldsMgr->GetField(fldSourceId, &spField);
	if (FAILED(hr))
		return hr;
	spField->SetSyncField(true);
	if (oldPrimaryFldId != m_syncSheetInfo.fldMap[m_srcSheetData.primaryId])
	{
		hr = pTarFieldsMgr->SetPrimaryField(m_syncSheetInfo.fldMap[m_srcSheetData.primaryId]);
		if (FAILED(hr))
			return hr;
	}
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	spRemoveRange->SetRecordIds(m_spTarDbSheetOp->GetAllRecords());
	for (EtDbIdx i = 0, tarFldCnt = pTarIds->Count(); i < tarFldCnt; ++i)
	{
		EtDbId id = pTarIds->IdAt(i);
		if (holdFldId.find(id) == holdFldId.end())
		{
			ks_stdptr<IDbField> pTarField;
			hr = pTarFieldsMgr->GetField(id, &pTarField);
			if (FAILED(hr))
				return hr;
			if (pTarField->IsSyncField())
				spRemoveRange->AddFieldId(id);
		}		
	}
	if (spRemoveRange->GetFieldCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveFields(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}

	if (m_syncSheetInfo.fldMap.size() != fieldIdIdxMap.size())
		return E_FAIL;

	std::vector<std::pair<IDbField*, VarObj>> formulaFlds;
	for (auto it : m_syncSheetInfo.fldMap)
	{
		EtDbId srcFldId = it.first;
		EtDbId tarFldId = it.second;

		ks_stdptr<IDbField> pTarField;
		hr = pTarFieldsMgr->GetField(tarFldId, &pTarField);
		if (FAILED(hr))
			return hr;
		if (fieldIdIdxMap.find(srcFldId) == fieldIdIdxMap.end())
			return E_FAIL;
		
		VarObj fieldObj = fieldsObj.at(fieldIdIdxMap.at(srcFldId));
		fieldObj.add_field_bool("arraySupport", false);
		ET_DbSheet_FieldType srcFldType = Et_DbSheetField_MultiLineText;
		_appcore_GainEncodeDecoder()->DecodeFieldType(fieldObj.field_str("type"), &srcFldType);
		if (srcFldType == Et_DbSheetField_Formula)
		{
			//公式字段先设置字段名，公式之间如果有依赖关系，设置公式表达式时确保依赖字段名已存在
			pTarField->SetName(fieldObj.field_str("name"), TRUE, TRUE);
			formulaFlds.push_back({pTarField, fieldObj});
			continue;
		}			

		hr = SyncDbSheetFieldProperty(pTarField, fieldObj);
		if (FAILED(hr))
		{
			WriteSyncFieldError(hr, __X("SyncDbSheetFieldProperty"), srcFldType, pTarField->GetType(), fieldObj);
			return hr;
		}
	}

	// 最后配置公式字段
	for (auto& formulaFld : formulaFlds)
	{
		IDbField* pFormulaFld = formulaFld.first;
		VarObj& fieldObj = formulaFld.second;
		hr = SyncDbSheetFieldProperty(pFormulaFld, fieldObj);
		if (FAILED(hr))
		{
			WriteSyncFieldError(hr, __X("SyncDbSheetFieldProperty"), Et_DbSheetField_Formula, Et_DbSheetField_MultiLineText, fieldObj);
			// 通过失败上报数据，配置公式字段可能会失败，原因未知，如果公式字段配置失败先转换成公式结果字段
			pFormulaFld->SetTypeForIO(Et_DbSheetField_FormulaResult);
			HRESULT hr = DbSheet::ConfigureDbFormulaResultField(pFormulaFld, fieldObj, true);
			if (FAILED(hr))
				return hr;
			SetDbSheetFieldCommonProperty(pFormulaFld, fieldObj);
		}
	}
	
	SetViewFieldWidth();
	return S_OK;
}

HRESULT DbSyncSheetHelper::SyncDbSheetFieldProperty(IDbField* pTarField, VarObj& fieldObj)
{
	HRESULT hr = S_OK;
	CleanSrcSheetFieldProperty(fieldObj);
	fieldObj.add_field_bool("syncField", true);
	ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
	_appcore_GainEncodeDecoder()->DecodeFieldType(fieldObj.field_str("type"), &type);
	switch (type)
	{
		case Et_DbSheetField_Lookup:
		{
			if (m_bNewLookupConvert)
			{
				auto configFormulaResultField = [this](const VarObj& fieldObj, IDbField* pField)
				{
					pField->SetTypeForIO(Et_DbSheetField_FormulaResult);
					HRESULT hr = DbSheet::ConfigureDbFormulaResultField(pField, fieldObj, true);
					if (FAILED(hr))
						return hr;
					SetDbSheetFieldCommonProperty(pField, fieldObj);
					return S_OK;
				};

				fieldObj.add_field_bool("arraySupport", true);
				ET_DbSheet_Lookup_Function func = DbSheet_Lookup_Function_Origin;
				if (fieldObj.has("aggregation"))
					_appcore_GainEncodeDecoder()->DecodeDbLookupFunction(fieldObj.field_str("aggregation"), &func);
				switch(func)
				{
					case DbSheet_Lookup_Function_Origin:
					case DbSheet_Lookup_Function_Unique:
					{
						if (fieldObj.has("baseFieldInfo"))
						{
							VarObj baseFieldObj = fieldObj.get("baseFieldInfo");
							baseFieldObj.add_field_bool("arraySupport", true);
							baseFieldObj.add_field_bool("uniqueValue", false);
							ET_DbSheet_FieldType baseType = Et_DbSheetField_MultiLineText;
							_appcore_GainEncodeDecoder()->DecodeFieldType(baseFieldObj.field_str("type"), &baseType);
							if (baseType == Et_DbSheetField_Formula || baseType == Et_DbSheetField_FormulaResult)
								return configFormulaResultField(fieldObj, pTarField);
							hr = SyncDbSheetFieldProperty(pTarField, baseFieldObj);
							if (FAILED(hr))
								return hr;
						}
						else
						{
							fieldObj.add_field_bool("arraySupport", false);
							pTarField->SetType(Et_DbSheetField_Lookup, nullptr); //无效的lookup类型还是当作lookup和线上保持一致
						}
						SetDbSheetFieldCommonProperty(pTarField, fieldObj);
						return S_OK;
					}
					case DbSheet_Lookup_Function_Sum:
					case DbSheet_Lookup_Function_Average:
					case DbSheet_Lookup_Function_Max:
					case DbSheet_Lookup_Function_Min:
					case DbSheet_Lookup_Function_Counta:
					case DbSheet_Lookup_Function_CountaUnique:
					case DbSheet_Lookup_Function_ToString:
					{
						return configFormulaResultField(fieldObj, pTarField);
					}
					default:
						ASSERT("引用聚合函数未处理!" == nullptr);
						break;
				}
			}
			[[fallthrough]];
		}
		case Et_DbSheetField_Link:
		case Et_DbSheetField_OneWayLink:
		case Et_DbSheetField_ParentRecord:
		{
			pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText);
			SetDbSheetFieldCommonProperty(pTarField, fieldObj);
			return S_OK;
		}
		case Et_DbSheetField_Formula:
		{
			if (fieldObj.has("isCrossSheetFormula") && fieldObj.field_bool("isCrossSheetFormula"))
			{
				pTarField->SetTypeForIO(Et_DbSheetField_MultiLineText);
				SetDbSheetFieldCommonProperty(pTarField, fieldObj);
				return S_OK;
			}
			else
			{
				ks_wstring errMsg;
				return UpdateField(fieldObj, true, m_spTarDbSheetOp, pTarField, &errMsg);
			}
		}
		default:
		{
			if (type == Et_DbSheetField_Note)	//字段SetType会检测富文本个数，限制只能有一个富文本字段，如果有引用富文本情况，会同步失败，单独调用SetTypeForIO绕开检查和线上一致
				pTarField->SetTypeForIO(Et_DbSheetField_Note);
			
			ks_wstring errMsg;
			hr = UpdateField(fieldObj, true, m_spTarDbSheetOp, pTarField, &errMsg);
			if (FAILED(hr))
				return hr;

			if (type == Et_DbSheetField_AutoNumber && fieldObj.has("nextNumber"))
			{
				UINT nextNumber = fieldObj.field_uint32("nextNumber");
				ks_stdptr<IDbtBookCtx> spDbBookCtx;
				VS(m_spTarDbSheetOp->GetBook()->GetExtDataItem(edBookDbBookCtx, (IUnknown**)&spDbBookCtx));
				spDbBookCtx->SetNextAutoNumber(pTarField, nextNumber);
			}
			
		}
	}
	return S_OK;
}

HRESULT DbSyncSheetHelper::SetDbSheetFieldCommonProperty(IDbField* pField, const VarObj& fieldObj)
{
	if (!pField)
		return E_FAIL;

	if (fieldObj.has("name")) pField->SetName(fieldObj.field_str("name"), TRUE, TRUE);
	if (fieldObj.has("description")) pField->SetDescription(fieldObj.field_str("description"));
	if (fieldObj.has("arraySupport")) pField->SetArray(fieldObj.field_bool("arraySupport"));
	return S_OK;
}

HRESULT DbSyncSheetHelper::SyncDbSheetRecords(const VarObj& recordsObj)
{
	HRESULT hr = S_OK;
	const IDBIds* pTarIds = m_spTarDbSheetOp->GetAllRecords();
	EtDbIdx tarRecCnt = pTarIds->Count();
	EtDbId fldSourceId = m_spTarDbSheetOp->GetFieldsManager()->GetSyncFieldSourceId();
	std::unordered_map<EtDbId, EtDbId> oldRecMap;
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	ks_stdptr<IDBSheetRange> spUpdateRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spUpdateRange);

	std::unordered_set<EtDbId> srcRecIdSet;
	if (IsFirstBatch())
		GetSrcSheetRecordIdList(m_param.sheetData.get("baseSchema"), srcRecIdSet);
	for (EtDbIdx i = 0; i < tarRecCnt; ++i) 
	{
		EtDbId recId = pTarIds->IdAt(i);
		ks_bstr source;
		m_spTarDbSheetOp->GetValueString(recId, fldSourceId, &source);
		if (source.empty())
		{
			spRemoveRange->AddRecordId(recId);
			continue;
		}
		EtDbId srcRecId = DBSyncUtil::decodeRecordId(source.c_str());
		if (srcRecIdSet.size() > 0 && srcRecIdSet.find(srcRecId) == srcRecIdSet.end())
			spRemoveRange->AddRecordId(recId);

		if (srcRecId != INV_EtDbId)
			oldRecMap.emplace(srcRecId, recId);
	}

	auto itEnd = oldRecMap.end();
	int srcRecCnt = recordsObj.arrayLength();
	std::vector<EtDbId> insertRecId;
	insertRecId.reserve(srcRecCnt);
	WOLOG_INFO << "SyncDbSheetRecords cnt:" << srcRecCnt;
	for (EtDbIdx i = 0; i < srcRecCnt; ++i)
	{
		const VarObj& recordObj = recordsObj.at(i);
		EtDbId srcRecId = DbSheet::GetEtDbId(recordObj, "id");
		if (srcRecId == INV_EtDbId)
			return E_INVALIDARG;

		auto it = oldRecMap.find(srcRecId);
		if (it == itEnd)
			insertRecId.emplace_back(srcRecId);
		else
		{
			m_recMap.emplace(srcRecId, it->second);
			spUpdateRange->AddRecordId(it->second);
		}
	}

	spRemoveRange->SetFieldIds(m_spTarDbSheetOp->GetAllFields());
	if (spRemoveRange->GetRecordCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveRecords(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	size_t insertRecCnt = insertRecId.size();
	if (insertRecCnt > 0)
	{
		ks_stdptr<IDBSheetRange> spDbRange;
		hr = m_spTarDbSheetOp->InsertRecords(insertRecCnt, &spDbRange);
		if (FAILED(hr))
			return hr;
		for (int i = 0; i < insertRecCnt; ++i)
		{
			EtDbId tarInsertRecId = spDbRange->GetRecordId(i);
			EtDbId srcRecId = insertRecId[i];
			m_batchInsertSrcRecIds.push_back(srcRecId);
			m_recMap.emplace(srcRecId, tarInsertRecId);
			spUpdateRange->AddRecordId(tarInsertRecId);
		}
	}
	
    const IDBIds* pIds = m_spTarDbSheetOp->GetAllFields();
    EtDbIdx cnt = pIds->Count();
    for (EtDbIdx i = 0; i < cnt; ++i)
    {
        EtDbId id = pIds->IdAt(i);
        ks_stdptr<IDbField> spField;
        hr = m_spTarDbSheetOp->GetFieldsManager()->GetField(id, &spField);
        if (FAILED(hr))
            return hr;
		bool isSourceField = (spField->GetID() == m_spTarDbSheetOp->GetFieldsManager()->GetSyncFieldSourceId());
		if (spField->IsSyncField() && !isSourceField)
			spUpdateRange->AddFieldId(id);
    }
	
	hr = m_spTarDbSheetOp->Clear(spUpdateRange);
	if (FAILED(hr))
        return hr;

	for (EtDbIdx i = 0; i < srcRecCnt; ++i)
	{
		const VarObj& recordObj = recordsObj.at(i);
		EtDbId srcRecId = DbSheet::GetEtDbId(recordObj, "id");
		if (srcRecId == INV_EtDbId || m_recMap.find(srcRecId) == m_recMap.end())
			continue;
		
		SyncDbSheetRecord(m_recMap.at(srcRecId), recordObj);
	}
	return S_OK;
}

HRESULT DbSyncSheetHelper::SyncDbSheetRecord(EtDbId tarRecId, const VarObj& recordObj)
{
	HRESULT hr = S_OK;
	if (!recordObj.has("fields"))
		return S_OK;
		
	const VarObj& fieldsValObj = recordObj.get("fields");
	std::vector<WebName> srcFldIds = fieldsValObj.keys();
	for(WebName srcFldIdStr : srcFldIds)
	{
		EtDbId srcFldId = INV_EtDbId;
		_appcore_GainDbSheetContext()->DecodeEtDbId(krt::utf16(krt::fromUtf8(srcFldIdStr)), &srcFldId);
		if(srcFldId == INV_EtDbId || m_syncSheetInfo.fldMap.find(srcFldId) == m_syncSheetInfo.fldMap.end() || m_srcfldIdTypeMap.find(srcFldId) == m_srcfldIdTypeMap.end())
			continue;

		ks_stdptr<IDbField> spTarField;
		hr = m_spTarDbSheetOp->GetFieldsManager()->GetField(m_syncSheetInfo.fldMap.at(srcFldId), &spTarField);
		if(FAILED(hr))
			continue;

		hr = SyncDbSheetCellValue(srcFldId, tarRecId, spTarField, srcFldIdStr, fieldsValObj);
		if(FAILED(hr))
		{
			const binary_wo::VarObj& valueObj = fieldsValObj.get_s(srcFldIdStr);
			WriteSyncFieldError(hr, __X("SyncDbSheetRecord"), m_srcfldIdTypeMap.at(srcFldId), spTarField->GetType(), valueObj);
		}
	}
	return S_OK;
}

HRESULT DbSyncSheetHelper::SyncDbSheetCellValue(EtDbId srcFldId, EtDbId tarRecId, IDbField* pTarField, WebName srcFldIdStr, const VarObj& fieldsValObj)
{
	HRESULT hr = S_OK;
	auto setFallbackTextValue = [](const binary_wo::VarObj& valueObj, EtDbId recId, EtDbId fldId, IDBSheetOp* pDBSheetOp)
	{
		alg::managed_vstr_token_assist assist;
		assist.create(valueObj.field_str("text"));
		return pDBSheetOp->SetTokenValue(recId, fldId, assist);
	};
	ET_DbSheet_FieldType srcFldType = m_srcfldIdTypeMap.at(srcFldId);
	if (srcFldType == Et_DbSheetField_Lookup)
		srcFldType = pTarField->GetType();
	switch (srcFldType)
	{
		case Et_DbSheetField_Link:
		case Et_DbSheetField_OneWayLink:
		{
			binary_wo::VarObj valueObj = fieldsValObj.get_s(srcFldIdStr);
			hr = setFallbackTextValue(valueObj, tarRecId, pTarField->GetID(), m_spTarDbSheetOp);
			if(FAILED(hr))
				return hr;
			break;
		}
		case Et_DbSheetField_AutoNumber:
		case Et_DbSheetField_CreatedBy:
		case Et_DbSheetField_CreatedTime:
		case Et_DbSheetField_LastModifiedBy:
		case Et_DbSheetField_LastModifiedTime:
		case Et_DbSheetField_Formula:
		case Et_DbSheetField_ParentRecord:
		{
			if (pTarField->GetType() == Et_DbSheetField_MultiLineText)
			{
				ASSERT(srcFldType == Et_DbSheetField_Formula || srcFldType == Et_DbSheetField_ParentRecord);
				//跨表公式、父子记录回落成文本
				binary_wo::VarObj valueObj = fieldsValObj.get_s(srcFldIdStr);
				hr = setFallbackTextValue(valueObj, tarRecId, pTarField->GetID(), m_spTarDbSheetOp);
				if(FAILED(hr))
				{
					return hr;
				}
			}
			else
			{
				hr = m_pValueSetter->SetDBSyncAutoValue(tarRecId, pTarField, srcFldIdStr, fieldsValObj);
				if(FAILED(hr))
				{
					return hr;
				}
			}
			break;
		}
		default:
		{
			ET_DbSheet_FieldType realSrcFldType = m_srcfldIdTypeMap.at(srcFldId);
			ET_DbSheet_FieldType lookupBaseFieldType = ET_DbSheet_FieldType_Invalid;
			if (realSrcFldType == Et_DbSheetField_Lookup)
				lookupBaseFieldType = GetLookupBaseFieldType(srcFldId);
			// 引用关联/单向关联/父子记录时，会回落成文本，需要将序列化的token转换成文本类型token
			if (realSrcFldType == Et_DbSheetField_Lookup && pTarField->GetType() == Et_DbSheetField_MultiLineText &&
				(lookupBaseFieldType == Et_DbSheetField_Link 
				|| lookupBaseFieldType == Et_DbSheetField_OneWayLink
				|| lookupBaseFieldType == Et_DbSheetField_ParentRecord))
			{
				if (fieldsValObj.has(srcFldIdStr))
				{
					hr = SetLookupFallBackTextValue(tarRecId, pTarField->GetID(), lookupBaseFieldType, fieldsValObj.get(srcFldIdStr));
					if(FAILED(hr))
					{
						return hr;
					}
				}
			}
			else if (realSrcFldType == Et_DbSheetField_Lookup && lookupBaseFieldType == ET_DbSheet_FieldType_Invalid)
			{
				//无效的引用字在同步表中还是引用字段没有回落，值为空，不需要同步无效引用字段值
				return S_OK;
			}
			else
			{
				hr = m_pValueSetter->Set(tarRecId, pTarField, srcFldIdStr, fieldsValObj);
				if(FAILED(hr))
				{
					return hr;
				}
			}
			break;
		}
	}
	return S_OK;
}

HRESULT DbSyncSheetHelper::SetInfoField()
{
	// 初次建立引用关系的时候，引用表需要冗余一个’记录来源‘的超链接字段
	IDBSheetCtx* pCtx = _appcore_GainDbSheetContext();
	HRESULT hr = S_OK;
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	EtDbId fldSourceId = pTarFieldsMgr->GetSyncFieldSourceId();
	ks_stdptr<IDbField> spInfoField;
	hr = pTarFieldsMgr->GetField(fldSourceId, &spInfoField);
	if (FAILED(hr))
		return hr;
	VS(spInfoField->SetTypeForIO(Et_DbSheetField_Url));
	hr = spInfoField->SetName(m_param.fldSourceName, TRUE, TRUE);
	if (FAILED(hr))
		return hr;
	ks_stdptr<IDbField_Hyperlink> spInfoUrl = spInfoField;
	spInfoUrl->SetDisplayText(m_param.fldSourceName);

	QString templateStr = krt::fromUtf16(m_param.urlTemplate);
	templateStr.replace(QRegularExpression(":sheetStId"), QString::number(m_param.srcSheetId));
	const QRegularExpression reRecId(":recordId");
	EtDbIdStr buf;
	for (auto srcRecId : m_batchInsertSrcRecIds)
	{
		if (m_recMap.find(srcRecId) == m_recMap.end())
			continue;
		EtDbId tarRecId = m_recMap.at(srcRecId);
		VS(pCtx->EncodeEtDbId(srcRecId, &buf));
		QString info = templateStr;
		info.replace(reRecId, krt::fromUtf16(buf));
		DBSyncUtil::encodeToBase64(info);
		PCWSTR valueStr = krt::utf16(info);
		alg::managed_token_assist mta;
		hr = pCtx->Text2DbHyperlinkToken(valueStr, valueStr, &mta);
		if (FAILED(hr))
			return hr;
		hr = m_spTarDbSheetOp->SetTokenValue(tarRecId, fldSourceId, mta);
		if (FAILED(hr))
			return hr;
	}
	return S_OK;
}

HRESULT DbSyncSheetHelper::SetViewFieldWidth()
{
	// 只处理第一次同步且视图上未设置过列宽的字段列宽
	ks_stdptr<IDBSheetViews> spDBSheetViews;
	HRESULT hr = m_spTarDbSheetOp->GetDbSheetViews(&spDBSheetViews);
	if (FAILED(hr))
		return hr;

	if (spDBSheetViews->GetSize(Et_DBSheetViewUse_ForDb) < 0)
		return E_FAIL;

	ks_stdptr<IDBSheetView> spView;
	spDBSheetViews->GetItemAt(0, Et_DBSheetViewUse_ForDb, &spView);
	if (spView == nullptr)
		return E_FAIL;
	
	if (spView->GetType() == et_DBSheetView_Grid)
	{
		ks_stdptr<IDBSheetView_Grid> spDbSheetViewGrid = spView;
		const IDBIds *pIds = m_spTarDbSheetOp->GetAllFields();
		for (UINT i = 0; i < pIds->Count(); ++i)
    	{
			EtDbId id = pIds->IdAt(i);
			ks_stdptr<IDbField> spField;
			hr = m_spTarDbSheetOp->GetFieldsManager()->GetField(id, &spField);
			if (FAILED(hr))
				continue;

			int width = 0;
			spDbSheetViewGrid->GetFieldWidth(id, &width);
			int defualtWidth = 0;
			m_spTarDbSheetOp->GetFieldDefWidth(id, &defualtWidth);
			if (width == defualtWidth)
			{
				ks_bstr bstr(spField->GetName());
				UINT32 charCnt = bstr.size();
				if(charCnt > 60) //和前端一样，限制最大60个字符
					charCnt = 60;
				int w = app_helper::GetColWidthWithChars(m_pTarWorkbook, charCnt + 12); //名字长度 + 图标长度
				spDbSheetViewGrid->SetFieldWidth(id, w, FALSE);
			}
		}
	}
	
	return S_OK;
}

HRESULT DbSyncSheetHelper::GetSrcSheetRecordIdList(const VarObj& sheetObj, std::unordered_set<EtDbId>& srcRecIdSet)
{
	if (!sheetObj.has("recordIds"))	//第一次传入源表记录id,后面分片更新可以不用传
		return S_OK;
	const VarObj& recordIds = sheetObj.get("recordIds");
	for (UINT i = 0; i < recordIds.arrayLength(); ++i)
	{
		EtDbId srcRecId = INV_EtDbId;
		_appcore_GainDbSheetContext()->DecodeEtDbId(recordIds.at(i).value_str(), &srcRecId);
		if (srcRecId == INV_EtDbId)
			continue;
		srcRecIdSet.insert(srcRecId);
	}

	return S_OK;
}

HRESULT DbSyncSheetHelper::GetSyncFieldMapping(std::unordered_map<EtDbId, EtDbId>& map)
{
	//规则：优先使用内核保存的映射关系，只有内核映射关系不存在时(旧版本第一次切到新版本)才使用AF传入的
	HRESULT hr = S_OK;
	IDbFieldsManager* pTarFieldsMgr = m_spTarDbSheetOp->GetFieldsManager();
	const IDBIds* pTarIds = m_spTarDbSheetOp->GetAllFields();
	for (int i = 0; i < pTarIds->Count(); i++)
	{
		ks_stdptr<IDbField> spField;
		EtDbId fldId = pTarIds->IdAt(i);
		hr = pTarFieldsMgr->GetField(fldId, &spField);
		if (FAILED(hr))
			return hr;
		if (!spField->IsSyncField())
			continue;
		PCWSTR syncSourceFieldId = spField->GetSyncSourceFieldId();
		if (syncSourceFieldId)
		{
			EtDbId srcFldId = INV_EtDbId;
			_appcore_GainDbSheetContext()->DecodeEtDbId(syncSourceFieldId, &srcFldId);
			if (srcFldId != INV_EtDbId)
				map.emplace(srcFldId, fldId);
		}
	}
	if (map.empty() && m_param.pFieldMap)
	{
		for (auto itr : *m_param.pFieldMap)
		{
			EtDbId srcFldId = itr.first;
			EtDbId tarFldId = itr.second;
			ks_stdptr<IDbField> spField;
			hr = pTarFieldsMgr->GetField(tarFldId, &spField);
			if (FAILED(hr))
				continue;
			EtDbIdStr buf;
			VS(_appcore_GainDbSheetContext()->EncodeEtDbId(srcFldId, &buf));
			spField->SetSyncSourceFieldId(buf);
			map.emplace(srcFldId, tarFldId);
		}
	}

	return S_OK;
}

HRESULT DbSyncSheetHelper::ClearSheetRecords()
{
	HRESULT hr = S_OK;
	ks_stdptr<IDBSheetRange> spRemoveRange;
	m_spTarDbSheetOp->CreateDBSheetRange(&spRemoveRange);
	const IDBIds* pRecIds = m_spTarDbSheetOp->GetAllRecords();
	spRemoveRange->SetRecordIds(pRecIds);
	spRemoveRange->SetFieldIds(m_spTarDbSheetOp->GetAllFields());
	if (spRemoveRange->GetRecordCnt() > 0)
	{
		hr = m_spTarDbSheetOp->RemoveRecords(spRemoveRange);
		if (FAILED(hr))
			return hr;
	}
	return hr;
}

bool DbSyncSheetHelper::IsFirstBatch()
{
	if (m_param.sheetData.has("baseSchema"))
	{
		const VarObj& baseSchema = m_param.sheetData.get("baseSchema");
		if (baseSchema.has("recordIds"))
		{
			const VarObj& recordIds = baseSchema.get("recordIds");
			if (recordIds.arrayLength() > 0)
				return true;
		}
	}
	return false;
}

ET_DbSheet_FieldType DbSyncSheetHelper::GetLookupBaseFieldType(EtDbId fldId)
{
	ET_DbSheet_FieldType ft = ET_DbSheet_FieldType_Invalid;
	if (!m_param.sheetData.has("baseSchema"))
		return ft;
	const VarObj& baseSchema = m_param.sheetData.get("baseSchema");
	if (!baseSchema.get("fields"))
		return ft;
	const VarObj& fieldsObj = baseSchema.get("fields");
	int fldCnt = fieldsObj.arrayLength();
	for (int i = 0; i < fldCnt; ++i)
	{
		const VarObj& fieldObj = fieldsObj.at(i);
		EtDbId srcFldId = DbSheet::GetEtDbId(fieldObj, "id");
		if (fldId == srcFldId)
		{
			if (fieldObj.has("baseFieldInfo"))
			{
				VarObj baseFieldObj = fieldObj.get("baseFieldInfo");
				_appcore_GainEncodeDecoder()->DecodeFieldType(baseFieldObj.field_str("type"), &ft);
				return ft;
			}
		}
	}
	return ft;
}

alg::managed_token_assist DbSyncSheetHelper::GenerateLookupFallBackTextValueToken(const binary_wo::VarObj& objValue)
{
	if (objValue.type() == binary_wo::typeArray)
	{
		ks_stdptr<IDbTokenArrayHandle> spTokenArray;
		_db_CreateObject(CLSID_KDbTokenArrayHandle, IID_IDbTokenArrayHandle, reinterpret_cast<void**>(&spTokenArray));
		int cnt = objValue.arrayLength();
		if (cnt > 0)
		{
			const binary_wo::VarObj& item = objValue.at(0);
			if (item.type() == binary_wo::typeArray)
			{
				for (int i = 0; i < cnt; i++)
				{
					spTokenArray->Add(GenerateLookupFallBackTextValueToken(objValue.at(i)).detach());
				}
			}
			else if (item.type() == binary_wo::typeStruct)
			{
				for (int i = 0; i < cnt; i++)
				{
					const binary_wo::VarObj& item = objValue.at(i);
					PCWSTR text = __X("");
					if (item.has("text"))
						text = item.field_str("text");	
					alg::managed_vstr_token_assist assist;
					assist.create(text);
					spTokenArray->Add(assist.detach());
				}
			}
		}
		alg::managed_handle_token_assist assist;
		assist.create(alg::ET_HANDLE_TOKENARRAY, spTokenArray);
		return alg::managed_token_assist {assist.detach()};
	}
	return alg::managed_token_assist{};
}

HRESULT DbSyncSheetHelper::SetLookupFallBackTextValue(EtDbId recId, EtDbId fldId, ET_DbSheet_FieldType fldType, binary_wo::VarObj objValue)
{
	//引用关联/父子记录，token值序列化时是这种形式[{"id":"B","text":"aa"}]
	//同步表引用关联和父子记录需要把token转换成["aa"]，否则文本类型设置值失败
	if (objValue.has("data"))
        objValue = objValue.get_s("data");
    else if (objValue.has("value"))
        objValue = objValue.get_s("value");

	switch (fldType)
	{
	case Et_DbSheetField_Link:
	case Et_DbSheetField_OneWayLink:
	case Et_DbSheetField_ParentRecord:
	{
		return m_spTarDbSheetOp->SetTokenValue(recId, fldId, GenerateLookupFallBackTextValueToken(objValue));
	}
	default:
		break;
	}
	return S_OK;
}

void DbSyncSheetHelper::WriteSyncFieldError(HRESULT hr, PCWSTR key, ET_DbSheet_FieldType srcFldType, ET_DbSheet_FieldType tarFldType, VarObj obj)
{
	PCWSTR srcTypeStr = __X("");
	_appcore_GainEncodeDecoder()->EncodeFieldType(srcFldType, &srcTypeStr);
	PCWSTR tarTypeStr = __X("");
	_appcore_GainEncodeDecoder()->EncodeFieldType(tarFldType, &tarTypeStr);
	m_errMsg.AppendFormat(__X("behavior.DbSyncSheetHelper.%s.error %s: type:%s->%s,obj:%s"), key, GetErrWideString(hr), srcTypeStr, tarTypeStr, VarObjToBase64(obj).c_str());
}

HRESULT DbSyncSheetHelper::CleanSrcSheetFieldProperty(VarObj& fieldObj)
{
	if (fieldObj.has("uniqueValue"))
		fieldObj.remove_field("uniqueValue"); //现在一张表只有一个唯一字段，源表和同步表辅助列都有唯一列会同步失败
	
	ET_DbSheet_FieldType type = Et_DbSheetField_MultiLineText;
	_appcore_GainEncodeDecoder()->DecodeFieldType(fieldObj.field_str("type"), &type);
	switch (type)
	{
	case Et_DbSheetField_SingleSelect:
	case Et_DbSheetField_MultipleSelect:
	{
		//清洗重复的选项
		if (fieldObj.has("items"))
        {
            std::set<ks_wstring> itemVec;
			std::vector<int> duplicateItemIdx;
            VAR_OBJ_EXPECT_ARRAY(fieldObj, "items")
            binary_wo::VarObj items = fieldObj.get_s("items");
            for (int i = 0, cnt = items.arrayLength_s(); i < cnt; i++)
            {
                binary_wo::VarObj item = items.at_s(i);
				if (item.has("value"))
				{
					VAR_OBJ_EXPECT_STRING(item, "value")
					PCWSTR itemValue = item.field_str("value");
					if (itemVec.find(itemValue) == itemVec.end())
					{
						itemVec.insert(itemValue);
					}
					else
					{
						duplicateItemIdx.push_back(i);
					}
				}
			}

			for (auto itr = duplicateItemIdx.rbegin(); itr != duplicateItemIdx.rend(); itr++)
				items.remove_item(*itr);
		}
		break;
	}
	default:
		break;
	}
	return S_OK;
}

FullUpdateSyncDbSheetsAdapter::FullUpdateSyncDbSheetsAdapter(etoldapi::_Workbook* pSrcWorkbook, etoldapi::_Workbook* pTarWorkbook, UINT srcStId, UINT tarStId,
	IDBProtectionJudgement* pProtectionJudgement, bool bNewLookupConvert)
	: m_pSrcWorkbook(pSrcWorkbook)
	, m_pTarWorkbook(pTarWorkbook)
	, m_pProtectionJudgement(pProtectionJudgement)
	, m_bNewLookupConvert(bNewLookupConvert)
{
	m_syncSheetInfo.srcSheetId = srcStId;
	m_syncSheetInfo.tarSheetId = tarStId;
}

HRESULT FullUpdateSyncDbSheetsAdapter::Init(const FullUpdateSyncDbSheetParam& param)
{
	if (!m_pSrcWorkbook || !m_pTarWorkbook || !m_pProtectionJudgement)
		return E_INVALIDARG;
	m_param = param;
	return S_OK;
}

HRESULT FullUpdateSyncDbSheetsAdapter::Exec()
{
	std::chrono::steady_clock::time_point execBegin = std::chrono::steady_clock::now();
	// 同步数据场景, 不需要产生协作记录 (不存在合理的"操作者")
	DbSheet::DisableDbTrackHistoryScope dbHistoryDisabler;

	IBook* pSrcBook = m_pSrcWorkbook->GetBook();
	IBook* pTarBook = m_pTarWorkbook->GetBook();
	if (!pSrcBook || !pTarBook)
		return E_FAIL;
	IDX srcStIdx = INVALIDIDX;
	IDX tarStIdx = INVALIDIDX;
	pSrcBook->STSheetToRTSheet(m_syncSheetInfo.srcSheetId, &srcStIdx);
	pTarBook->STSheetToRTSheet(m_syncSheetInfo.tarSheetId, &tarStIdx);
	if (srcStIdx == INVALIDIDX || tarStIdx == INVALIDIDX)
		return E_INVALIDARG;
	ks_stdptr<_Worksheet> spSrcWorksheet = m_pSrcWorkbook->GetWorksheets()->GetSheetItem(srcStIdx);
	ks_stdptr<_Worksheet> spTarWorksheet = m_pTarWorkbook->GetWorksheets()->GetSheetItem(tarStIdx);
	ISheet* pTarShet = spTarWorksheet->GetSheet();
	if (!pTarShet->IsDbSheet())
		return E_INVALIDARG;
	ks_stdptr<IDBSheetOp> spDBSheetOp;
	HRESULT hr = DbSheet::GetDBSheetOp(pTarShet, &spDBSheetOp);
	if (FAILED(hr))
		return hr;
	if (!spDBSheetOp->IsSyncSheet())
		return E_INVALIDARG;
	if (spDBSheetOp->GetSyncSheetFullupdateMode() == DbSyncSheet_Fullupdate_Mode_ServerAPI)
		DbSyncSheet::ClearSyncFieldSourceFieldId(spDBSheetOp);
	spDBSheetOp->SetSyncSheetFullupdateMode(DbSyncSheet_Fullupdate_Mode_File);
	ks_stdptr<IBookOp> pTarBookOp;
	VS(pTarBook->GetOperator(&pTarBookOp));
	app_helper::KBatchUpdateCal buc(pTarBookOp);
	DbSheet::DisableDbProtectScope disPtScope(m_pProtectionJudgement);
	hr = DbSheet::copyDbUsersManager(pSrcBook, pTarBook);
	if (FAILED(hr))
		return hr;
	if (spSrcWorksheet->GetSheet()->IsDbSheet())
		hr = execDbSync(spSrcWorksheet, spTarWorksheet);
	else
		hr = execGridsheetSync(spSrcWorksheet, spTarWorksheet);
	auto execElapsed = (std::chrono::steady_clock::now() - execBegin) / std::chrono::milliseconds(1);
	if(m_param.pTimeStatInfo)
		m_param.pTimeStatInfo->AppendFormat(__X("exec_%d_"), execElapsed);
	return hr;
}

HRESULT FullUpdateSyncDbSheetsAdapter::execDbSync(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet)
{
	DBSyncParam syncParam;
	syncParam.fldSourceName = m_param.fldSourceName;
	syncParam.urlTemplate = m_param.urlTemplate;
	syncParam.pOldFieldMap = m_param.pFieldMap;
	syncParam.pTimeStatInfo = m_param.pTimeStatInfo;

	DBSyncHelper syncHelper(pSrcWorkSheet, pTarWorkSheet, m_bNewLookupConvert);
	HRESULT hr = syncHelper.Init(syncParam);
	if (FAILED(hr))
		return hr;

	std::chrono::steady_clock::time_point execCopyBegin = std::chrono::steady_clock::now();
	hr = syncHelper.ExecCopy();
	if (FAILED(hr))
		return hr;
	auto execCopyElapsed = (std::chrono::steady_clock::now() - execCopyBegin) / std::chrono::milliseconds(1);
	if(m_param.pTimeStatInfo)
		m_param.pTimeStatInfo->AppendFormat(__X("execCopy_%d_"), execCopyElapsed);

	if (!m_lostFlag && syncHelper.GetLostNoteFlag())
		m_lostFlag = true;
	m_syncSheetInfo.fldSourceId = syncHelper.getFldSourceId();
	m_syncSheetInfo.fldMap = syncHelper.getFldMap();
	return S_OK;
}

HRESULT FullUpdateSyncDbSheetsAdapter::execGridsheetSync(etoldapi::_Worksheet* pSrcWorkSheet, etoldapi::_Worksheet* pTarWorkSheet)
{
	GridsheetSyncHelper::GridsheetSyncParam syncParam;
	syncParam.fldSourceName = m_param.fldSourceName;
	syncParam.urlTemplate = m_param.urlTemplate;
	syncParam.pOldFieldMap = m_param.pFieldMap;
	syncParam.m_pParam = m_param.m_pParam;
	syncParam.m_pCtx = m_param.m_pCtx;
	syncParam.m_pcwFilePath = m_param.m_pcwFilePath;
	syncParam.m_pcwDefaultName = m_param.m_pcwDefaultName;
	syncParam.m_bEnableCopyAttachment = m_param.m_bEnableCopyAttachment;
	GridsheetSyncHelper syncHelper(pSrcWorkSheet, pTarWorkSheet);
	HRESULT hr = syncHelper.Init(syncParam);
	if (FAILED(hr))
		return hr;
	hr = syncHelper.ExecCopy();
	if (FAILED(hr))
		return hr;
	if (!m_lostFlag && syncHelper.GetLostNoteFlag())
		m_lostFlag = true;
	m_syncSheetInfo.fldSourceId = syncHelper.getFldSourceId();
	m_syncSheetInfo.fldMap = syncHelper.getFldMap();
	return S_OK;
}

UnsyncDbSheetHelper::UnsyncDbSheetHelper(IKWorksheet* pWorksheet)
	: m_pWorksheet(pWorksheet)
{
}

HRESULT UnsyncDbSheetHelper::Init(const AttachmentIdMap* pAttachmentIdMap)
{
	if (!pAttachmentIdMap)
		return E_FAIL;
	m_pAttachmentIdMap = pAttachmentIdMap;
	return S_OK;
}

static void fallBackArraySupportField_unsync(IDbField* pField, IDBSheetOp* pSheetOp, EtDbId fieldID)
{
	pField->SetArray(FALSE);
	DbSheet::array_field_fall_back::fallback(pField);
}

HRESULT UnsyncDbSheetHelper::Exec()
{
	// 同步数据场景, 不需要产生协作记录 (不存在合理的"操作者")
	DbSheet::DisableDbTrackHistoryScope dbHistoryDisabler;

	ks_stdptr<IDBSheetOp> spDbSheetOp;
	HRESULT hr = DbSheet::GetDBSheetOp(m_pWorksheet->GetSheet(), &spDbSheetOp);
	if (FAILED(hr))
		return hr;
	if (!spDbSheetOp->IsSyncSheet())
		return E_FAIL;
	IDbFieldsManager* pFieldsMgr = spDbSheetOp->GetFieldsManager();
	const IDBIds* pFields = spDbSheetOp->GetAllFields();
	// 清理掉自动生成的数据源字段和数据源名称字段
	EtDbId fldSourceId = pFieldsMgr->GetSyncFieldSourceId();
	EtDbId fldSourceNameId = pFieldsMgr->GetSyncFieldSourceNameId();
	if (INV_EtDbId != fldSourceId || INV_EtDbId != fldSourceNameId)
	{
		ks_stdptr<IDBSheetRange> spRg;
		spDbSheetOp->CreateDBSheetRange(&spRg);
		spRg->SetRecordIds(spDbSheetOp->GetAllRecords());
		if (INV_EtDbId != fldSourceId && fldSourceId != pFieldsMgr->GetPrimaryField())
			spRg->AddFieldId(fldSourceId);
		if (INV_EtDbId != fldSourceNameId)
			spRg->AddFieldId(fldSourceNameId);
		if (spRg->GetFieldCnt() > 0)
		{
			hr = spDbSheetOp->RemoveFields(spRg);
			if (FAILED(hr))
				return hr;
		}
	}
	VS(spDbSheetOp->SetSheetSyncType(DbSheet_St_None));
	for (EtDbIdx i = 0, fldCnt = pFields->Count(); i < fldCnt; ++i)
	{
		EtDbId fldId = pFields->IdAt(i);
		ks_stdptr<IDbField> spField;
		pFieldsMgr->GetField(fldId, &spField);
		if (spField->IsSyncLookupField() || Et_DbSheetField_FormulaResult == spField->GetType())
			fallBackArraySupportField_unsync(spField.get(), spDbSheetOp.get(), fldId);
		if (spField->IsSyncField())
			spField->SetSyncFieldForIO(FALSE);
		switch (spField->GetType())
		{
			case Et_DbSheetField_Attachment:
				VS(UpdateAttachmentByMapping(fldId, spDbSheetOp, m_pAttachmentIdMap, m_lostNoteFlag));
				break;
			case Et_DbSheetField_Note:
				VS(UpdateNoteByMapping(fldId, spDbSheetOp, m_pAttachmentIdMap, m_lostNoteFlag));
				break;
			case Et_DbSheetField_MultipleSelect:
			case Et_DbSheetField_SingleSelect:
			{
				ks_stdptr<IDbField_Select> spFieldSelect = spField;
				spFieldSelect->SetAutoAddItem(FALSE);
				break;
			}
			case Et_DbSheetField_Formula:	//跨表公式回落成文本,本表公式需要重新计算
			{
				spField->UpdateAutoValue();
				break;
			}
			default:
				break;
		}
	}

	DbSyncSheet::ClearSyncFieldFlag(spDbSheetOp); // 清除同步字段Flag的时机须放在最后
	DbSyncSheet::ClearSyncFieldSourceFieldId(spDbSheetOp);

	return S_OK;
}

FullUpdateMergeSyncDbSheetsAdapter::FullUpdateMergeSyncDbSheetsAdapter(etoldapi::_Workbook* pTarWorkbook, UINT tarStId, IDBProtectionJudgement* pProtectionJudgement, bool bNewLookupConvert)
	: m_pTarWorkbook(pTarWorkbook)
	, m_pProtectionJudgement(pProtectionJudgement)
	, m_bNewLookupConvert(bNewLookupConvert)
{
	m_tarSheetId = tarStId;
}

HRESULT FullUpdateMergeSyncDbSheetsAdapter::Init(const FullUpdateMergeSyncDbSheetParam& param)
{
	if (!m_pTarWorkbook || !m_pProtectionJudgement)
		return E_INVALIDARG;
	m_param = param;
	return S_OK;
}

HRESULT FullUpdateMergeSyncDbSheetsAdapter::PrepareMerge(const std::vector<IKWorksheet*>& srcWorksheets)
{
	IBook* pSrcBook = m_pSrcWorkbook->GetBook();
	IBook* pTarBook = m_pTarWorkbook->GetBook();
	if (!pSrcBook || !pTarBook)
		return E_FAIL;
	HRESULT hr = getTarWorksheet();
	if (FAILED(hr))
		return hr;
	ks_stdptr<IBookOp> spTarBookOp;
	VS(pTarBook->GetOperator(&spTarBookOp));
	app_helper::KBatchUpdateCal buc(spTarBookOp);
	DbSheet::DisableDbProtectScope disPtScope(m_pProtectionJudgement);
	DBSyncMergeHelper::DBSyncMergeParam syncParam;
	syncParam.bNeedCopySheetInfo = true;
	syncParam.pcwFldSourceName = m_param.fldSourceName;
	syncParam.pcwUrlTemplate = m_param.urlTemplate;
	syncParam.pcwFldSourceFileName = m_param.fldSourceFileName;
	etoldapi::_Worksheet* pTarWorksheet = static_cast<etoldapi::_Worksheet*>(m_pNewWorksheet);
	if (srcWorksheets.empty())
		return E_FAIL;
	etoldapi::_Worksheet* pSrcWorksheet = static_cast<etoldapi::_Worksheet*>(srcWorksheets[0]);
	m_upDBSyncMergeHelper = std::make_unique<DBSyncMergeHelper>(pSrcWorksheet, pTarWorksheet, m_bNewLookupConvert);
	hr = m_upDBSyncMergeHelper->Init(syncParam);
	if (FAILED(hr))
		return hr;
	hr = m_upDBSyncMergeHelper->PrepareMerge();
	if (FAILED(hr))
		return hr;
	return S_OK;
}

HRESULT FullUpdateMergeSyncDbSheetsAdapter::AdjustMerge()
{
	return m_upDBSyncMergeHelper->AdjustMerge();
}

HRESULT FullUpdateMergeSyncDbSheetsAdapter::AddFieldMappingItem(PCWSTR fileId, PCWSTR fileName, const VarObj& srcStIdVec)
{
	if (binary_wo::typeArray != srcStIdVec.type() || nullptr == m_pSrcWorkbook)
		return E_INVALIDARG;
	m_curFileId = fileId;
	m_curFileName = fileName;
	std::vector<FieldMappingItem>& sheetStIdVec = m_fileIdMap[m_curFileId];
	int srcSheetCnt = srcStIdVec.arrayLength();
	for (int i = 0; i < srcSheetCnt; ++i)
	{
		VarObj srcSheetItem = srcStIdVec.at(i);
		sheetStIdVec.emplace_back(srcSheetItem.field_uint32("sheet_id"));
		auto& fieldMappingItem = sheetStIdVec.back();
		if (!srcSheetItem.has("field_id_map"))
			continue;
		VarObj srcSheetIdMappingList = srcSheetItem.get("field_id_map");
		IDBSheetCtx* dbCtx = _appcore_GainDbSheetContext();
		for (WebName key : srcSheetIdMappingList.keys())
		{
			VAR_OBJ_EXPECT_STRING(srcSheetIdMappingList, key)
			EtDbId srcFldId = INV_EtDbId;
			HRESULT hr = dbCtx->DecodeEtDbId(krt::utf16(krt::fromUtf8(key)), &srcFldId);
			if (FAILED(hr))
				return E_INVALID_REQUEST;
			PCWSTR strTarId = srcSheetIdMappingList.field_str(key);
			EtDbId tarFldId = INV_EtDbId;
			hr = dbCtx->DecodeEtDbId(strTarId, &tarFldId);
			if (tarFldId == INV_EtDbId)
				return E_INVALID_REQUEST;
			fieldMappingItem.m_fieldIdMap.emplace(srcFldId, tarFldId);
		}
	}
	return S_OK;
}

HRESULT FullUpdateMergeSyncDbSheetsAdapter::getTarWorksheet()
{
	IBook* pTarBook = m_pTarWorkbook->GetBook();
	if (!pTarBook)
		return E_FAIL;
	IDX tarStIdx = INVALIDIDX;
	pTarBook->STSheetToRTSheet(m_tarSheetId, &tarStIdx);
	if (tarStIdx == INVALIDIDX)
		return E_INVALIDARG;
	m_pNewWorksheet = m_pTarWorkbook->GetWorksheets()->GetSheetItem(tarStIdx);
	if (!m_pNewWorksheet || !m_pNewWorksheet->GetSheet()->IsDbSheet())
		return E_INVALIDARG;
	return S_OK;
}

HRESULT FullUpdateMergeSyncDbSheetsAdapter::ExecMerge()
{
	if (m_iBookNum <= 0 || m_iCurIdx >= m_iBookNum)
		return E_FAIL;
	HRESULT hr = S_OK;
	IBook* pSrcBook = m_pSrcWorkbook->GetBook();
	IBook* pTarBook = m_pTarWorkbook->GetBook();
	if (!pSrcBook || !pTarBook)
		return E_FAIL;
	ks_stdptr<IBookOp> pTarBookOp;
	VS(pTarBook->GetOperator(&pTarBookOp));
	app_helper::KBatchUpdateCal buc(pTarBookOp);
	DbSheet::DisableDbProtectScope disPtScope(m_pProtectionJudgement);
	std::vector<IKWorksheet*> srcWorksheets;
	hr = fillSrcWorksheets(srcWorksheets);
	if (FAILED(hr))
		return hr;
	if (m_iCurIdx == 0)
	{
		hr = PrepareMerge(srcWorksheets);
		if (FAILED(hr))
			return hr;
	}
	if (nullptr == m_upDBSyncMergeHelper)
		return E_FAIL;
	hr = DbSheet::copyDbUsersManager(pSrcBook, pTarBook);
	if (FAILED(hr))
		return hr;
	m_upDBSyncMergeHelper->SetSrcWorkbook(pSrcBook);
	m_upDBSyncMergeHelper->SetSrcWorkSheetsVec(srcWorksheets);
	m_upDBSyncMergeHelper->SetCurFileInfo(m_curFileId, m_curFileName);
	m_upDBSyncMergeHelper->SetFieldMappingItems(m_fileIdMap[m_curFileId]);
	hr = m_upDBSyncMergeHelper->ExecMerge();
	if (FAILED(hr))
		return hr;
	if (m_iCurIdx + 1 == m_iBookNum)
	{
		hr = AdjustMerge();
		if (FAILED(hr))
			return hr;
	}
	m_upDBSyncMergeHelper->MoveFieldMappingItems(m_fileIdMap[m_curFileId]);
	return S_OK;
}

HRESULT FullUpdateMergeSyncDbSheetsAdapter::fillSrcWorksheets(std::vector<IKWorksheet*>& srcWorksheets)
{
	if (nullptr == m_curFileId)
		return E_FAIL;
	auto iter = m_fileIdMap.find(m_curFileId);
	if (iter == m_fileIdMap.end())
		return E_FAIL;
	IBook* pBook = m_pSrcWorkbook->GetBook();
	HRESULT hr = S_OK;
	for (const FieldMappingItem& item : iter->second)
	{
		IDX sheetIdx = INVALIDIDX;
		hr = pBook->STSheetToRTSheet(item.m_uSheetStId, &sheetIdx);
		if (FAILED(hr))
			return E_DBSHEET_SHEET_NOT_FOUND;
		IKWorksheet* pSrcWorksheet = m_pSrcWorksheets->GetSheetItem(sheetIdx);
		if (!pSrcWorksheet)
			return E_INVALIDARG;
		ISheet* pSrcSheet = pSrcWorksheet->GetSheet();
		// 目前仅支持 dbsheet 作为引用表来源
		if (!pSrcSheet->IsDbSheet())
			return E_INVALIDARG;

		if (checkIsStatisicSheetInThisBook(pSrcSheet))
			return E_INVALIDARG;

		srcWorksheets.emplace_back(pSrcWorksheet);
	}
	return S_OK;
}

bool FullUpdateMergeSyncDbSheetsAdapter::checkIsStatisicSheetInThisBook(ISheet* pSrcSheet)
{
	IWoETSettings *pWoSettings = _kso_GetWoEtSettings();
	if (xstrcmp(m_curFileId, pWoSettings->GetFileId()) == 0)
	{
		ks_stdptr<IDBSheetOp> spSrcSheetOp;
		DbSheet::GetDBSheetOp(pSrcSheet, &spSrcSheetOp);
		if (spSrcSheetOp && spSrcSheetOp->GetSheetSubType() == DbSheet_Sub_Type_StatSheet)
		{
			WOLOG_ERROR << "[FullUpdateMergeSyncDbSheetsAdapter::fillSrcWorksheets] statSheet can not be source in same book";
			return true;
		}
	}
	return false;
}

EtDbId FullUpdateMergeSyncDbSheetsAdapter::GetFieldSourceId() const
{
	if (m_upDBSyncMergeHelper)
		return m_upDBSyncMergeHelper->GetFldSourceId();
	return INV_EtDbId;
}

EtDbId FullUpdateMergeSyncDbSheetsAdapter::GetFieldSourceNameId() const
{
	if (m_upDBSyncMergeHelper)
		return m_upDBSyncMergeHelper->GetFldSourceNameId();
	return INV_EtDbId;
}
} // namespace wo
