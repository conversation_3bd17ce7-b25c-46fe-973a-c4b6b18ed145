﻿#include "etcmd/etstdafx.h"
#include "wo/et_revision_context.h"
#include "condition_format_cache.h"
#include "logger.h"

extern WebProcType gs_procType;

ConditionFormatCache::ConditionFormatCache()
    : m_pBook(nullptr)
{}

static ConditionFormatCacheMode GetCfCacheMode()
{
    static ConditionFormatCacheMode mode = CFCM_None;
    if (mode == CFCM_None)
    {
        mode = CFCM_Disabled;
        char* str = ::getenv("CF_CACHE_MODE");
        if (str)
        {
            if (strncmp(str, "on", 2) == 0)
                mode = CFCM_Enabled;
        }
        WOLOG_INFO << "[GetCfCacheMode]: " << mode;
    }
    return mode;
}

bool ConditionFormatCache::IsEnabled()
{
    switch (GetCfCacheMode())
    {
        case CFCM_Enabled:
            return true;
        default:
            return false;
    }
}

void ConditionFormatCache::Init(IBook* pBook)
{
    ASSERT(pBook);
    m_pBook = pBook;
}

void ConditionFormatCache::PrepareCfData(IKWorkbook* pWorkbook, const RANGE &rg)
{
    if (!rg.IsValid())
    {
        ASSERT(!"invalid range");
        return;
    }
    WOLOG_DEBUG << "[ConditionFormatCache] PrepareCfData";
    RANGE rgUpdate(rg);
    for (IDX sheetIdx = rg.SheetFrom(); sheetIdx <= rg.SheetTo(); sheetIdx++)
    {
        rgUpdate.SetSheetFromTo(sheetIdx);
        IKWorksheet* pWorksheet = pWorkbook->GetWorksheets()->GetSheetItem(sheetIdx);
        ks_stdptr<IKXFGridCache> spXFGrid;
        getXFGridCache(pWorksheet, &spXFGrid);
        spXFGrid->prepareCFData(&rgUpdate);
    }
}
void ConditionFormatCache::getXFGridCache(IKWorksheet* pWorksheet, IKXFGridCache** ppXFGridCache)
{
    _render_GetRenderGlobal()->getXFGridCache(pWorksheet, ppXFGridCache);
}

void ConditionFormatCache::OptmiseCache()
{
    _render_GetRenderGlobal()->checkEvictXFCache();
}

void ConditionFormatCache::Reset(IBook* pBook)
{
    _render_GetRenderGlobal()->ClearXFBook(pBook);
}